#include "nettypes.h"
#include "netjob.h"
#include "esclattr.h"
#include "escldef.h"
#include "escljob.h"
#include <sys/epoll.h>
#include <sys/timerfd.h>
#include "scan_status.h"

#define ESCLJOB_MLOCK_UN(o)     { if ( (o) != NULL ) pi_mutex_unlock((o)->list_lock); }
#define ESCLJOB_MLOCK_EX(o)     { if ( (o) != NULL ) pi_mutex_lock((o)->list_lock);   }

#define ESCL_JOB_TAG            ( 0x19820326 )

#define ESCL_SEND_FILE_TIMEOUT  60

struct page_info
{
    struct list_head    page_list;
    char                file_path[FILE_PATH_MAX];
    int32_t             last_page;
};

struct escl_job
{
    ESCL_ATTR_S*        pattr;
    PI_MUTEX_T          list_lock;
    struct list_head    page_head;
    time_t              start_timer;
    int32_t             page_completed;
    int32_t             page_transfer;
    int32_t             jobid;
    int32_t             job_done_invalid;
    volatile int32_t    job_scanning_done :1;
    volatile int32_t    job_canceled      :1; // cancel by scan
    int32_t             job_running;

    struct epoll_event  ev;
    struct itimerspec   tspec;
    int                 timerfd;           //for file transfering timeout
};

typedef struct escljob_context
{
    int epollfd;
    PI_THREAD_T tid;
}
ESCLJOB_CTX_S;

static ESCLJOB_CTX_S s_escljob_ctx = {-1, INVALIDTHREAD};

static pthread_mutex_t  s_scan_mutex = PTHREAD_MUTEX_INITIALIZER;

int32_t escljob_page_list_empty(ESCL_JOB_S* pejob)
{
    int32_t rv;

    ESCLJOB_MLOCK_EX(pejob);
    rv = pi_list_empty(&pejob->page_head);
    ESCLJOB_MLOCK_UN(pejob);

    return rv;
}

static int32_t escljob_send_status_to_airscan(ESCL_JOB_S* pjob, AIRSCAN_STATUS_E status)
{
    ROUTER_MSG_S message;
    int ret;

    message.msgType     = MSG_SCAN_STATUS_UPDATE;
    message.msg1        = 0;
    message.msg2        = status;
    message.msg3        = NULL;
    message.msgSender   = MID_PORT_NET;

    ret = task_msg_send_by_router(MID_SCAN_OUT_AIRSCAN, &message);
    if(ret)
    {

        NET_WARN("send status<%d> to MID_SCAN_OUT_AIRSCAN failed", status);
    }
    else
    {
        NET_DEBUG("send status<%d> to MID_SCAN_OUT_AIRSCAN successfully", status);
    }

    return ret;
}

static void escljob_clear_page_list(ESCL_JOB_S* pejob)
{
    struct page_info*   page;
    struct list_head*   pos;
    struct list_head*   n;

    ESCLJOB_MLOCK_EX(pejob);
    pi_list_for_each_safe(pos, n, &pejob->page_head)
    {
        page = pi_list_entry(pos, struct page_info, page_list);
        pi_list_del_entry(&page->page_list);
        pi_free(page);
    }
    ESCLJOB_MLOCK_UN(pejob);
}

static void escljob_add_page(ESCL_JOB_S* pejob, const char* file, int32_t last_page)
{
    struct  page_info*  page;

    RETURN_IF(pejob == NULL || file == NULL, NET_WARN);

    page = (struct page_info *)pi_zalloc(sizeof(struct page_info));
    RETURN_IF(page == NULL, NET_WARN);

    ESCLJOB_MLOCK_EX(pejob);
    pejob->page_completed++;
    if ( esclattr_get_file_type(pejob->pattr) == FILE_PDF )
    {
        pejob->page_completed = 1;
    }
    page->last_page = last_page;
    NET_DEBUG("job %d add page %d file(%s) last_page(%d)", pejob->jobid, pejob->page_completed, file, last_page);
    snprintf(page->file_path, sizeof(page->file_path), "%s", file);
    pi_list_add_tail(&page->page_list, &pejob->page_head);
    ESCLJOB_MLOCK_UN(pejob);
}

static void escljob_release(ESCL_JOB_S* pejob)
{
    escljob_clear_page_list(pejob);
    if ( pejob->list_lock != INVALIDMTX )
    {
        pi_mutex_destroy(pejob->list_lock);
    }
    if ( pejob->pattr != NULL )
    {
        pi_free(pejob->pattr);
    }
    memset(pejob, 0, sizeof(ESCL_JOB_S));
    pi_free(pejob);
}

static void escljob_release_handler(void* userdata, uint32_t type)
{
    ESCL_JOB_S* pejob = (ESCL_JOB_S *)userdata;

    RETURN_IF(pejob == NULL || type != ESCL_JOB_TAG, NET_WARN);

    escljob_release(pejob);
}

static void escljob_cancel(ESCL_JOB_S* pejob)
{
    NETJOB_STATE_E  jobstate = netjob_get_state(pejob->jobid);
    ROUTER_MSG_S    message = { .msgSender = MID_PORT_NET };
    uint32_t        count = 0;

    NET_DEBUG("job %d state: %d(%s)", pejob->jobid, jobstate, netjob_state_string(jobstate));
    if ( jobstate <= NETJOB_STATE_RUNNING )
    {
        message.msgType = MSG_CTRL_JOB_CANCEL;
        message.msg1    = pejob->jobid;
        message.msg2    = 0;
        message.msg3    = NULL;
        task_msg_send_by_router(MID_SYS_JOB_MGR, &message);
        while ( netjob_get_state(pejob->jobid) <= NETJOB_STATE_RUNNING && count++ < 30 ) /* 等待作业取消完成 */
        {
            NET_DEBUG("waiting netjob_state canceled. now is %d", netjob_get_state(pejob->jobid));
            pi_msleep(1000);
        }
    }

}

static void escljob_start_notify( uint32_t job_id, int32_t result , void *context )
{
    ESCL_JOB_S* pejob = context;
    if ( result != 0 )
    {
        pejob->job_running = 0;
        NET_WARN("escljob %u failed to start, result = %d, starting to cancel", job_id, result);
    }
}

static void sec2itimespec(int seconds, struct itimerspec* its)
{
    its->it_value.tv_sec = seconds;
    its->it_value.tv_nsec = 0;
    its->it_interval.tv_sec = 0;
    its->it_interval.tv_nsec = 0;
}

static int32_t _escljob_timer_restart(ESCL_JOB_S* pejob, int seconds)
{
    RETURN_VAL_IF(s_escljob_ctx.epollfd < 0 || pejob->timerfd < 0, NET_DEBUG, -1);

    sec2itimespec(seconds, &pejob->tspec);
    if ( timerfd_settime(pejob->timerfd, 0, &pejob->tspec, NULL) == 0 )
    {
        return 0;
    }
    else
    {
        NET_WARN("%s failed, seconds: %d", __func__, seconds);
        return -1;
    }
}

int32_t escljob_timer_restart(ESCL_JOB_S* pejob)
{
    NET_DEBUG("ecsljob:%d restart timer", pejob->jobid);
    return _escljob_timer_restart(pejob, ESCL_SEND_FILE_TIMEOUT);
}

int32_t escljob_timer_stop(ESCL_JOB_S* pejob)
{
    NET_DEBUG("ecsljob:%d stop timer", pejob->jobid);
    return _escljob_timer_restart(pejob, 0);
}

void escljob_timer_delete(ESCL_JOB_S* pejob)
{
    if (pejob->timerfd >= 0)
    {
        epoll_ctl(s_escljob_ctx.epollfd, EPOLL_CTL_DEL, pejob->timerfd, &pejob->ev);
        close(pejob->timerfd);

        NET_DEBUG("ecsljob:%d delete timer", pejob->jobid);
        pejob->timerfd = -1;
    }
}

static int32_t escljob_timer_start(ESCL_JOB_S* pejob, int seconds)
{
    int32_t ret = -1;
    RETURN_VAL_IF(s_escljob_ctx.epollfd < 0, NET_ERROR, -1);

    do
    {
        pejob->timerfd = timerfd_create(CLOCK_MONOTONIC, TFD_NONBLOCK | TFD_CLOEXEC);
        NET_DEBUG("ecsljob:%d begin to start timer", pejob->jobid);
        if (pejob->timerfd >= 0)
        {
            sec2itimespec(seconds, &pejob->tspec);
            BREAK_IF ( timerfd_settime(pejob->timerfd, 0, &pejob->tspec, NULL), NET_WARN );
            pejob->ev.events = EPOLLIN;
            pejob->ev.data.ptr = pejob;
            BREAK_IF ( epoll_ctl(s_escljob_ctx.epollfd, EPOLL_CTL_ADD, pejob->timerfd, &pejob->ev), NET_WARN );
            ret = 0;
        }
        else
        {
            NET_WARN("escljob:%d start timer failed", pejob->jobid);
        }
    }
    while (0);

    return ret;
}


static int32_t escljob_handler(void* cookie)
{
    ESCL_JOB_S*     pejob = (ESCL_JOB_S *)cookie;
    ROUTER_MSG_S    message;
    JOB_REQUEST_S*  pjobreq = NULL;
    void*           reqdata = NULL;
    int32_t         rv = 0;

    RETURN_VAL_IF(pejob == NULL, NET_WARN, -1);

    pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
    RETURN_VAL_IF(pjobreq == NULL, NET_WARN, -1);

    reqdata = esclattr_convert_request_data(pejob->pattr, pejob->jobid, MID_AIRSCAN);
    if ( reqdata == NULL )
    {
        NET_WARN("job %d convert scan request data failed", pejob->jobid);
        netjob_free(pejob->jobid, 0);
        pi_free(pjobreq);
        return -1;
    }

    pthread_mutex_lock(&s_scan_mutex);
    while ( task_msg_tryto_get_msg_count(MID_AIRSCAN) > 0 && task_msg_tryto_get_by_router(MID_AIRSCAN, &message) == 0 )
    {
        NET_DEBUG("clear invalid message type(%s)", get_msg_type_string(message.msgType));
    }

    NET_DEBUG("send MSG_CTRL_JOB_REQUEST to MID_SYS_JOB_MGR");
    pjobreq->io_class         = IO_CLASS_SCAN;
    pjobreq->io_via           = IO_VIA_NET;
    pjobreq->job_config_param = reqdata;
    pjobreq->notify           = escljob_start_notify;
    pjobreq->context          = pejob;
    pjobreq->pqio             = NULL;

    message.msgType   = MSG_CTRL_JOB_REQUEST;
    message.msg1      = pejob->jobid;
    message.msg2      = SCAN_OBJ;
    message.msg3      = pjobreq;
    message.msgSender = MID_PORT_NET;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &message);

    pi_time(&pejob->start_timer);
    pejob->job_running = 1;
    while ( pejob->job_running )
    {
        memset(&message, 0, sizeof(message));
        rv = task_msg_wait_timeout_by_router(MID_AIRSCAN, &message, 2, 0);
        if ( rv < 0 )
        {
            NET_WARN("task_msg_wait_timeout_by_router failed(%d)", rv);
            break;
        }
        else if ( rv > 0 )
        {
            continue; /* timeout */
        }

        NET_DEBUG("netjob %d recv %s from %s", pejob->jobid, get_msg_type_string(message.msgType), get_module_id_string(message.msgSender));
        if ( pejob->jobid != message.msg1 )
        {
            NET_WARN("message jobid(%u) current jobid(%d)", message.msg1, pejob->jobid);
            //continue; TODO: 扫描模块目前未传递jobid ！！！
        }

        switch ( message.msgType )
        {
        case MSG_DATA_JOB_START:
            break;
        case MSG_DATA_PAGE_START:
            break;
        case MSG_DATA_PAGE_END:
            escljob_add_page(pejob, (const char *)message.msg3, 0);
            pi_free(message.msg3);
            break;
        case MSG_DATA_JOB_END:
            if ( esclattr_get_file_type(pejob->pattr) == FILE_PDF )
            {
                escljob_add_page(pejob, (const char *)message.msg3, 1);
                pi_free(message.msg3);
            }
            if ( pejob->job_canceled )  // cancel by scan
            {
                netjob_free(pejob->jobid, 10);
            }
            else if ( escljob_timer_start(pejob, ESCL_SEND_FILE_TIMEOUT) )
            {
                //timer start failed, send AIRSCAN_TRAN_SUCCESS directly
                pejob->timerfd = -1;
                escljob_send_status_to_airscan(pejob, AIRSCAN_TRAN_SUCCESS);
                netjob_free(pejob->jobid, 10);
            }
            pejob->job_scanning_done = 1;
            pejob->job_running = 0;

            break;
        case MSG_CTRL_JOB_CANCEL:
            pejob->job_canceled = 1;
            break;
        default:
            break;
        }
    }

    pthread_mutex_unlock(&s_scan_mutex);

    return 0;
}

int32_t escljob_get_jobs_info(char* buf, size_t buf_size)
{
    ESCL_JOB_S*     pejob;
    int32_t         jobid = netjob_get_next_jobid(0);
    int32_t         jobactive = 0;
    int32_t         jobcount = 0;
    int32_t         age;
    time_t          now_time;
    char            jobinfo[4096];
    char*           pstr = jobinfo;
    const char*     pwg_reason;
    const char*     pwg_state;

    for ( jobcount = 0; (jobcount < 5 && jobid > 0); jobid = netjob_get_next_jobid(jobid) )
    {
        pejob = escljob_search(jobid);
        if ( pejob == NULL )
        {
            continue;
        }
        jobcount++;

        switch ( netjob_get_state(jobid) )
        {
        case NETJOB_STATE_PROCESSING:
        case NETJOB_STATE_RUNNING:
            pwg_reason = "JobScanning";
            pwg_state  = "Processing";
            jobactive++;
            break;
        case NETJOB_STATE_CANCELED:
            pwg_reason = "JobCanceledByUser";
            pwg_state  = "Canceled";
            break;
        case NETJOB_STATE_ABORTED:
            pwg_reason = "JobAborted";
            pwg_state  = "Aborted";
            break;
        case NETJOB_STATE_DONE:
            if ( escljob_page_list_empty(pejob) )
            {
                if ( pejob->job_done_invalid )
                {
                    pwg_reason = "JobAborted";
                    pwg_state  = "Completed";
                }
                else
                {
                    pwg_reason = "JobCompletedSuccessfully";
                    pwg_state  = "Completed";
                }
            }
            else
            {
                if ( pejob->job_done_invalid )
                {
                    pwg_reason = "JobAborted";
                    pwg_state  = "Completed";
                }
                else /* job is still transferring data, so call it scanning */
                {
                    pwg_reason = "JobScanning";
                    pwg_state  = "Processing";
                    jobactive++;
                }
            }
            break;
        default:
            pwg_reason = "JobInitiated";
            pwg_state  = "Pending";
            break;
        }

        age = (int32_t)(pejob->start_timer > 0 ? pi_time(NULL) - pejob->start_timer : 0);
        NET_DEBUG("jobcount(%d) jobid(%d) age(%d) page_completed(%d) page_transfer(%d) pwg_state(%s) pwg_reason(%s)",
                jobcount, jobid, age, pejob->page_completed, pejob->page_transfer, pwg_state, pwg_reason);

        pstr += snprintf(pstr, sizeof(jobinfo) + jobinfo - pstr, ESCL_JOBINFO_TEMPLATE,
                jobid, jobid, age, pejob->page_completed, pejob->page_transfer, pwg_state, pwg_reason);
        if ( (size_t)pstr > (size_t)jobinfo + sizeof(jobinfo) )
        {
            NET_WARN("jobinfo buffer no enough!!!");
            break;
        }
    }

    if ( jobcount > 0 )
    {
        snprintf(buf, buf_size, ESCL_JOBS_TEMPLATE, jobinfo);
    }
    else
    {
        buf[0] = '\0';
    }
    return jobactive;
}

int32_t escljob_get_page_num(ESCL_JOB_S* pejob, int32_t* code)
{
    NETJOB_STATE_E  jobstate;
    int32_t         retries = 0;
    int32_t         rcode = 404;
    int32_t         pages = 0;

    RETURN_VAL_IF(pejob == NULL || code == NULL, NET_WARN, 0);

    while ( retries++ < 120 )
    {
        jobstate = netjob_get_state(pejob->jobid);
        if ( jobstate == NETJOB_STATE_CANCELED )
        {
            rcode = 410;
            break;
        }
        else if ( jobstate == NETJOB_STATE_ABORTED )
        {
            rcode = 500;
            break;
        }
        else
        {
            if ( pejob->page_completed > pejob->page_transfer )
            {
                pages = (++pejob->page_transfer);
                rcode = 200;
                break;
            }
            else if ( pejob->job_scanning_done )
            {
                break;
            }
            else if ( esclattr_get_input_source(pejob->pattr) == SCAN_MODE_ADF )
            {
                rcode = 503;
                break;
            }
            else
            {
                pi_msleep(500);
            }
        }
    }

    NET_DEBUG("job %d rcode(%d) state(%s)", pejob->jobid, rcode, netjob_state_string(jobstate));
    *code = rcode;
    return pages;
}

int32_t escljob_takes_page(ESCL_JOB_S* pejob, char* file, size_t size, int32_t* last_page)
{
    struct page_info* page;
    int32_t           rv;

    ESCLJOB_MLOCK_EX(pejob);
    page = pi_list_first_entry_or_null(&pejob->page_head, struct page_info, page_list);
    if ( page != NULL )
    {
        NET_DEBUG("job %d takes page %d file(%s)", pejob->jobid, pejob->page_transfer, page->file_path);
        snprintf(file, size, "%s", page->file_path);
        if ( last_page  != NULL )
        {
            *last_page = page->last_page;
        }
        pi_list_del_entry(&page->page_list);
        pi_free(page);
        rv = 0;
    }
    else
    {
        NET_DEBUG("job %d page list empty", pejob->jobid);
        rv = -1;
    }
    ESCLJOB_MLOCK_UN(pejob);

    return rv;
}

int32_t escljob_get_file_type(ESCL_JOB_S* pejob)
{
    return (int32_t)esclattr_get_file_type(pejob->pattr);
}

int32_t escljob_delete(ESCL_JOB_S* pejob, AIRSCAN_STATUS_E sts)
{
    char file[FILE_PATH_MAX];

    RETURN_VAL_IF(pejob == NULL, NET_WARN, -1);

    escljob_timer_delete(pejob);
    if ( pejob->job_done_invalid != 1 )
    {
        escljob_cancel(pejob);
    }
    while ( escljob_takes_page(pejob, file, sizeof(file), NULL) == 0 )
    {
        unlink(file);
    }

    if (!pejob->job_canceled)
    {
        escljob_send_status_to_airscan(pejob, sts);
    }
    netjob_free(pejob->jobid, 10);

    return 0;
}

void escljob_finish(ESCL_JOB_S* pejob)
{
    if (pejob->timerfd >= 0)
    {
        NET_DEBUG("escljob_finish, will send status to scan");
        escljob_timer_delete(pejob);
        escljob_send_status_to_airscan(pejob, AIRSCAN_TRAN_SUCCESS);
        netjob_free(pejob->jobid, 10);
    }
}

int32_t escljob_start(const char* xml)
{
    ESCL_JOB_S* pejob = NULL;
    int32_t     jobid = 0;

    pejob = (ESCL_JOB_S *)pi_zalloc(sizeof(ESCL_JOB_S));
    RETURN_VAL_IF(pejob == NULL, NET_WARN, 0);
    pi_init_list_head(&pejob->page_head);

    do
    {
        BREAK_IF((pejob->pattr = esclattr_parse_scan_settings(xml)) == NULL, NET_WARN); /* 解析当前作业参数 */
        BREAK_IF(esclattr_get_input_source(pejob->pattr) == SCAN_MODE_ADF && scan_status_get_adf_paper_status() == ADF_NO_PAPER, NET_WARN) /* ADF作业确认当前ADF有纸 */
        BREAK_IF((pejob->list_lock = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        pejob->jobid = netjob_alloc(NETJOB_TYPE_SCAN, NULL);
        BREAK_IF(pejob->jobid <= 0, NET_WARN);

        netjob_set_userdata(pejob->jobid, ESCL_JOB_TAG, (void *)pejob, escljob_release_handler);
        pejob->timerfd = -1;

        BREAK_IF(netjob_start_task(pejob->jobid, escljob_handler, (void *)pejob) < 0, NET_WARN);

        jobid = pejob->jobid;
    }
    while ( 0 );

    if ( jobid == 0 )
    {
        NET_WARN("start eSCL job failed");
        if ( pejob->jobid > 0 )
        {
            netjob_free(pejob->jobid, 0);
        }
        else
        {
            escljob_release(pejob);
        }
    }
    return jobid;
}

ESCL_JOB_S* escljob_search(int32_t jobid)
{
    ESCL_JOB_S* pejob;
    uint32_t    type;

    RETURN_VAL_IF(jobid <= 0 || netjob_get_userdata(jobid, &type, (void **)&pejob) != 0, NET_WARN, NULL);
    RETURN_VAL_IF(type != ESCL_JOB_TAG || pejob == NULL, NET_WARN, NULL);
    RETURN_VAL_IF(pejob->jobid != jobid, NET_WARN, NULL);

    return pejob;
}

static void* escljob_thread(void *arg)
{
    struct epoll_event events[3];
    int nfd;
    ESCL_JOB_S* pejob;

    while (1)
    {
        nfd = epoll_wait(s_escljob_ctx.epollfd, events, sizeof(events), -1);
        if (nfd > 0)
        {
            for (int i=0; i < nfd; ++i)
            {
                pejob = events[i].data.ptr;
                if (pejob)
                {
                    pejob->job_done_invalid = 1;
                    NET_WARN("escl jobid:%d is timeout to be retrieved", pejob->jobid);
                    // notify to scan modules
                    escljob_delete(pejob, AIRSCAN_TRAN_ERROR);
                }
            }
        }
    }
    return NULL;
}

// return 0 for success, -1 for fail
int escljob_thread_create()
{
    s_escljob_ctx.epollfd = epoll_create(1);
    s_escljob_ctx.tid = pi_thread_create(escljob_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "escljob_thread");
    RETURN_VAL_IF(s_escljob_ctx.epollfd < 0 || s_escljob_ctx.tid == INVALIDTHREAD, NET_WARN, -1);

    return 0;
}

void escljob_thread_destroy()
{
    if (s_escljob_ctx.epollfd >= 0)
    {
        close(s_escljob_ctx.epollfd);
    }
    if (s_escljob_ctx.tid != INVALIDTHREAD)
    {
        pi_thread_destroy(s_escljob_ctx.tid);
    }

    s_escljob_ctx.epollfd = -1;
    s_escljob_ctx.tid = INVALIDTHREAD;
}
