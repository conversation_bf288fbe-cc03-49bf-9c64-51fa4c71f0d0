/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netmisc.h
 * @addtogroup net
 * @{
 * @addtogroup netmisc
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network custom interface header file
 */
#ifndef __NETMISC_H__
#define __NETMISC_H__

#include "netctx.h"

#define	REGULAR_EXPRESSIONS_POLICY_01		"[$%&|;`'?]|([]+[\"]{2})"
#define	REGULAR_EXPRESSIONS_POLICY_02		"[`']|([]+[\"]{2})"
#define	REGULAR_EXPRESSIONS_POLICY_03		"[=$%&|;`'?]|([]+[\"]{2})"

typedef enum
{
    CODING_FORMAT_ASCII = 0,
    CODING_FORMAT_UTF8,
    CODING_FORMAT_GBK
}
CODING_FORMAT_E;

/**
 * @brief       Get the encoding format of the string.
 * @param[in]   str           : The string pointer.
 * @return      The encoding format in CODING_FORMAT_E.
 * <AUTHOR>
 * @date        2023-9-16
 */
CODING_FORMAT_E get_coding_format           (const char* str);

/**
 * @brief       Convert the coding format of the string.
 * @param[in]   from_charset  : from coding format.
 * @param[in]   to_charset    : to coding format.
 * @param[in]   src           : source buffer pointer.
 * @param[in]   src_len       : source buffer length.
 * @param[out]  dst           : destination buffer pointer.
 * @param[in]   dst_size      : destination buffer size.
 * @return      Convert result.
 * @retval      ==0           : success\n
 *              < 0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         convert_coding_format       (const char* from_charset, const char* to_charset, char* src, size_t src_len, char* dst, size_t dst_size);

/**
 * @brief       Convert the source string to UTF-8.
 * @param[in]   src           : source buffer pointer.
 * @param[out]  dst           : destination buffer pointer.
 * @param[in]   dst_size      : destination buffer size.
 * @return      Convert result.
 * @retval      ==0           : success\n
 *              < 0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         convert_str_to_utf8         (char* src, char* dst, size_t dst_size);

/**
 * @brief       Convert HEX buffer to string.
 * @param[in]   src           : source buffer pointer.
 * @param[in]   src_len       : source buffer length.
 * @param[out]  dst           : destination buffer.
 * @param[in]   dst_size      : destination buffer size.
 * @return      Convert result.
 * @retval      ==0           : success\n
 *              < 0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
size_t          convert_hexbytes_to_string  (const uint8_t* src, size_t src_len, char* dst, size_t dst_size);

/**
 * @brief       Convert HEX string to bytes buffer.
 * @param[in]   src           : source string pointer.
 * @param[out]  dst           : destination buffer.
 * @param[in]   dst_size      : destination buffer size.
 * @return      Convert result.
 * @retval      ==0           : success\n
 *              < 0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
size_t          convert_string_to_hexbytes  (const char* src, uint8_t* dst, size_t dst_size);

/**
 * @brief       Check string format by regular.
 * @param[in]   str           : source string pointer.
 * @return      Check result.
 * @retval      ==0           : success\n
 *              < 0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         check_string_format         (const char* str, const char* pattern);

/**
 * @brief       Get the pid from this pid file.
 * @param[in]   pidfile       : The file path to saved PID number.
 * @return      PID number.
 * @retval      > 0           : valid PID number\n
 *              ==0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         get_pid_from_file           (const char* pidfile);

/**
 * @brief       Check whether the program is running by this pid file.
 * @param[in]   pidfile       : The file path to saved PID number.
 * @return      PID number.
 * @retval      !=0           : yes\n
 *              ==0           : no
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         check_program_exist         (const char* pidfile);

/**
 * @brief       Waiting the program start finish.
 * @param[in]   pidfile       : The file path to saved PID number.
 * @param[in]   max_retries   : The retry times.
 * @param[in]   interval      : The interval time.
 * @return      Check result.
 * @retval      ==0           : start success\n
 *              < 0           : start fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         waiting_program_start_ent   (const char* pidfile, uint32_t max_retries, uint32_t interval, const char* caller);
#define waiting_program_start(a, b, c)      waiting_program_start_ent(a, b, c, __func__)

/**
 * @brief       send signal(SIGKILL) to stop program, and waiting the program stop finish.
 * @param[in]   pidfile       : The file path to saved PID number.
 * @param[in]   max_retries   : The retry times.
 * @param[in]   interval      : The interval time.
 * @return      Check result.
 * @retval      ==0           : stop success\n
 *              < 0           : stop fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         waiting_program_stop_ent    (const char* pidfile, uint32_t max_retries, uint32_t interval, const char* caller);
#define waiting_program_stop(a, b, c)       waiting_program_stop_ent(a, b, c, __func__)

/**
 * @brief       encrypt data by RC4.
 * @param[in]   in_buf        : input buffer pointer.
 * @param[out]  out_buf       : output buffer pointer.
 * @param[in]   len           : buffer length.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            encrypt_data_rc4            (uint8_t* in_buf, uint8_t* out_buf, size_t len);

/**
 * @brief       Compare the contents of the two files.
 * @param[in]   src_file1     : source file1 path.
 * @param[in]   src_file2     : source file2 path.
 * @param[in]   size          : compare data size.
 * @return      Compare result.
 * @retval      ==0           : same contents\n
 *              !=0           : different
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         compare_file                (const char* src_file1, const char* src_file2, size_t size);

/**
 * @brief       Copy the contents of the file to other file.
 * @param[out]  dst_file      : The destination file path.
 * @param[in]   src_file      : The source file path.
 * @param[in]   mode          : fopen(dst_file, mode).
 * @return      Copy result.
 * @retval      ==0           : copy success\n
 *              !=0           : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         copy_file                   (const char* dst_file, const char* src_file, const char* mode);

void            save_file                   (const char* file, const char* buf, size_t len);

size_t          read_file                   (const char* file, char* buf, size_t size);

#define MAX_URI_PROTO_LEN   64
#define MAX_URI_HOST_LEN    1024
#define MAX_URI_PATH_LEN    1024
#define MAX_URI_PARM_LEN    1024

typedef struct uri_elements
{
    char     proto[MAX_URI_PROTO_LEN];
    char     host[MAX_URI_HOST_LEN];
    char     path[MAX_URI_PATH_LEN];
    char     parm[MAX_URI_PARM_LEN];
    uint16_t port;
}
URI_ELEMS_S;

int32_t         parse_uri_host              (const char* pstr_start, const char* pstr_end, uint16_t* port_val, char* host_val, size_t val_size);

int32_t         parse_uri                   (const char* uri, URI_ELEMS_S* uri_elems);

/**
 * @brief       change card priority.
 * @param[out]  try_times         : try time flag.
 * @param[in]   net_ctx        : Net ctx.
 * <AUTHOR> Jun
 * @date        2024-4-28
 */
int32_t change_card_priority(NET_CTX_S* net_ctx, int32_t try_times);

/**
 * @brief       array to string.
 * @param[in]   arr             : The array.
 * @param[in]   arr_count       : The lenth of array.
 * @param[out]  str             : str buffe
 * @param[in]   str_size        : str size.
 * @return      result.
 * @retval      =0              : fail\n
 *              >0              : lenth of string
 * <AUTHOR> Huanbin
 * @date        2024-7-24
 */
int32_t         array_to_string(int32_t* arr, int32_t arr_count, char* str, size_t str_size);

#endif /* __NETMISC_H__ */
/**
 *@}
 */
