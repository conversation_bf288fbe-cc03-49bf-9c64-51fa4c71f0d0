/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file storage_mgr.h
 * @addtogroup storage manager
 * @{
 * @addtogroup storage manager
 * <AUTHOR>
 * @date 2023-09-14
 * @brief storage manager modules interface declaration
 */

#ifndef __STORAGE_MGR_H__
#define __STORAGE_MGR_H__
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/**
 * @brief storage manager moudle init and start
 * @retval =0: success
 *         <0: fail
 * <AUTHOR>
 * @data   2023-09-14
 */
int pi_storage_mgr_prolog(void);

/**
 * @brief storage manager moudle stop and deinit
 * @return result
 * @retval =0: success
 *         <0: fail
 * <AUTHOR>
 * @data   2023-09-14
 */
void pi_storage_mgr_epilog(void);

#ifdef __cplusplus
}
#endif/* __cplusplus */
#endif/* __STORAGE_MGR_H__ */
/**
 * @}
 */
