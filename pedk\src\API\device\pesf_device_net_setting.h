#ifndef _PESF_DEVICE_NET_SETTING_
#define _PESF_DEVICE_NET_SETTING_

#include <quickjs.h>

/*
    声明 QuickJS C 函数由于初始化回调
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

JSValue js_get_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_set_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_set_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_wired_net_ipv4_mask(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_set_wired_net_ipv4_mask(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_wired_net_ipv4_gateway(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_set_wired_net_ipv4_gateway(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

int js_device_net_setting_init(JSContext *ctx, JSValueConst global);

#endif /* _PESF_DEVICE_SETTING_ */
