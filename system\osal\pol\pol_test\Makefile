.SUFFIXES: .c .o

ifeq ($(CROSS),1)
TOOLCHAIN_DIR=/tools/linaro/gcc-linaro-4.9.4-2017.01-i686_arm-linux-gnueabihf
SYSROOT=$(TOOLCHAIN_DIR)/arm-linux-gnueabihf/libc
CC=$(TOOLCHAIN_DIR)/bin/arm-linux-gnueabihf-gcc
AR=$(TOOLCHAIN_DIR)/bin/arm-linux-gnueabihf-ar
STRIP=$(TOOLCHAIN_DIR)/bin/arm-linux-gnueabihf-strip
CFLAGS=-std=gnu99 --sysroot=$(SYSROOT)
libctest=ctest_arm
else
CC=gcc
#CFLAGS=-g
endif

SRC+= ./pol_io_test.c
SRC+= ./pol_test.c
SRC+= ./pol_mem_test.c
SRC+= ./pol_string_test.c
SRC+= ./pol_time_test.c
#SRC+= test/convert_test.c

INC=-I ../../../include
INC+=-I ../../../include/pol		
INC+=-I ../../../include/linux

CC_FLAG=-Wall
LIB=-lpthread

TARGET=pol
OBJ = $(patsubst %.c,%.o,$(SRC))


all: $(TARGET)
$(TARGET):$(OBJ)
	$(CC)  -o $@ $(OBJ)  $(LIB)
.c.o:
	$(CC) $(CC_FLAG) $(INC) -o $@ -c $<	

.PHONY:clean
clean:
	@echo "clean project $(TARGET)" 
	@rm -f $(TARGET) $(OBJ) 
