/*
 * @Author: your name
 * @Date: 2024-01-16 13:41:59
 * @LastEditTime: 2024-01-19 17:04:26
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \runtime\src\runtime\modules\module_manager copy.h
 */
#include "runtime/modules/module_manager.h"
#include <stdio.h>
#include "basic/config.h"
#include "basic/hash/uthash.h"


#define MODULE_MANAGER "module manager"

typedef struct ModuleContextList{
    char module_name[APP_NAME_LEN_MAX];
    ModuleContext *md_ctx;
    UT_hash_handle hh;
}ModuleContextList;

//全PeSF模块管理唯一列表
ModuleContextList *hash_module_context_list = NULL;

/**
 * @brief 将模块的上下文注册到hash_module_context_list哈希表中
 * 
 * @param md_ctx 
 */
void module_context_add(ModuleContext *md_ctx)
{
    ModuleContextList *md_ctx_l = (ModuleContextList*)malloc(sizeof(ModuleContextList));
    
    //参数名长度检查并处理
    if(strlen(md_ctx->module_name) >= APP_NAME_LEN_MAX) {
        md_ctx->module_name[APP_NAME_LEN_MAX - 1] = '\0';
    }

    //复制索引
    strcpy(md_ctx_l->module_name, md_ctx->module_name);
    md_ctx_l->md_ctx = md_ctx;

    //插入哈希表
    HASH_ADD_STR(hash_module_context_list, module_name, md_ctx_l);

    LOG_I(MODULE_MANAGER,"Module: [%s] registed.\n",md_ctx->module_name);
}

/**
 * @brief 根据模块名在hash_module_context_list哈希表中查找模块上下文，并返回
 * 
 * @param module_name 
 * @return ModuleContext* 
 */
ModuleContext* module_context_find(char *module_name)
{
    ModuleContext *md_ctx = NULL;
    ModuleContextList *md_ctx_l = NULL;

    HASH_FIND_STR(hash_module_context_list, module_name, md_ctx_l);
    if(NULL != md_ctx_l){
        md_ctx = md_ctx_l->md_ctx;
    }else{
        LOG_E(MODULE_MANAGER,"Can't find [%s] module.\n", module_name);
    }

    return md_ctx;
}

/**
 * @brief 根据名字从hash_module_context_list哈希表中删除上下文模块
 * 
 * @param module_name 
 */
void module_context_del(char *module_name)
{
    ModuleContextList *md_ctx_l = NULL;

    HASH_FIND_STR(hash_module_context_list, module_name, md_ctx_l);
    if(NULL != md_ctx_l){
        HASH_DEL(hash_module_context_list,md_ctx_l);
        free(md_ctx_l);
    }else{
         LOG_E(MODULE_MANAGER,"Can't delete [%s] module.\n", module_name);
    }
}

/**
 * @brief 初始全部子模块
 * 
 */
void modules_init_all()
{
    ModuleContextList *md_ctx_l, *tmp;
    ModuleContext *md_ctx;

//    printf("modules_init_all\n");

    HASH_ITER(hh,hash_module_context_list,md_ctx_l,tmp){
        md_ctx = md_ctx_l->md_ctx;
        if(NULL != md_ctx->module_init){
            LOG_I(MODULE_MANAGER,"Module [%s] init start;   ",md_ctx->module_name);
            md_ctx->module_init();
            LOG_I(MODULE_MANAGER,"Module [%s] init done.\n",md_ctx->module_name);
        }else{
            LOG_E(MODULE_MANAGER,"Can't init [%s] module.\n", md_ctx->module_name);
        }
    }
}

/**
 * @brief 释放全部子模块
 * 
 */
void modules_release_all()
{
    ModuleContextList *md_ctx_l, *tmp;
    ModuleContext *md_ctx;

    HASH_ITER(hh,hash_module_context_list,md_ctx_l,tmp){
        md_ctx = md_ctx_l->md_ctx;
        if(NULL != md_ctx->module_release){
            md_ctx->module_release();
        }else{
            LOG_E(MODULE_MANAGER,"Can't release [%s] module.\n", md_ctx->module_name);
        }
    }
}

/**
 * @brief 实例化全部子模块
 * 
 */
void modules_instance_all(PeSFRunTime *prt)
{
    ModuleContextList *md_ctx_l, *tmp;
    ModuleContext *md_ctx;

    HASH_ITER(hh,hash_module_context_list,md_ctx_l,tmp){
        md_ctx = md_ctx_l->md_ctx;
        //如果该模块存在实例化函数，则实例化，并把实例化数据存入
        if(NULL != md_ctx->module_instance){
            md_ctx->module_instance(prt);
        }
    }
}

/**
 * @brief 去实例化全部子模块
 * 
 */
void modules_generalization_all(PeSFRunTime *prt)
{
    ModuleContextList *md_ctx_l, *tmp;
    ModuleContext *md_ctx;

    HASH_ITER(hh, hash_module_context_list, md_ctx_l, tmp){
        md_ctx = md_ctx_l->md_ctx;
        //如果该模块存在去实例化函数，则执行，并把实例数据从运行时上删除
        if(NULL != md_ctx->module_generalization){
            md_ctx->module_generalization(prt);
        }
    }
}

/**
 * @brief 向运行时添加自定义实例数据
 * 
 * @param prt :运行时
 * @param instance_data_name：key值字符串 
 * @param data ：已申请空间的数据指针。
 * @return int32_t 
 */
int32_t add_instance_data(PeSFRunTime *prt, char *instance_data_name, void *data)
{
    InstanceDataList **ins_l = &prt->instance_list;
    InstanceDataList *new_ins_l = (InstanceDataList*)malloc(sizeof(InstanceDataList));

    strcpy(new_ins_l->instance_name, instance_data_name);
    new_ins_l->instance_data = data;

    HASH_ADD_STR(*ins_l, instance_name , new_ins_l);

    return 0;
}

/**
 * @brief 从运行时上根据实例数据名，获取实例数据
 * 
 * @param prt 
 * @param instance_data_name 
 * @return void* 
 */
void* get_instance_data(PeSFRunTime *prt, char *instance_data_name)
{
    InstanceDataList *ins_l = prt->instance_list;
    InstanceDataList *get_ins_l = NULL;

    HASH_FIND_STR(ins_l, instance_data_name, get_ins_l);
    if(NULL == get_ins_l){
        LOG_E(MODULE_MANAGER,"Can't get instance data [%s]!\n", instance_data_name);
    }

    return get_ins_l->instance_data;
}

/**
 * @brief 从运行时删除实例数据
 * 
 * @param prt 
 * @param instance_data_name 
 * @return int32_t 
 */
int32_t del_instance_data(PeSFRunTime *prt, char *instance_data_name)
{
    int32_t ret = 0;
    InstanceDataList **ins_l = &prt->instance_list;
    InstanceDataList *get_ins_l;

    HASH_FIND_STR(*ins_l, instance_data_name , get_ins_l);
    if(NULL == get_ins_l){
        ret = -1;
        LOG_E(MODULE_MANAGER,"Can't get instance data [%s]!\n", instance_data_name);
    }
    else{
        HASH_DEL(*ins_l,get_ins_l);
    }

    return ret;
}