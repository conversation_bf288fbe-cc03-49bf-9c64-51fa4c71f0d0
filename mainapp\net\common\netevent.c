/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netevent.c
 * @addtogroup net
 * @{
 * @addtogroup netevent
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network event manager client handler
 */
#include "nettypes.h"
#include "netevent.h"
#include "netjob.h"
#include "netsts.h"
#include "job_manager.h"
#include "wifi.h"

static PI_SEMAPHORE_T   s_platform_wired_sem = INVALIDSEM;
static PI_SEMAPHORE_T   s_platform_wifi_sem  = INVALIDSEM;

/**
 * @brief       Convert data to unsigned integer.
 * @param[in]   data    : data pointer.
 * @param[in]   len     : data length.
 * @return      The value of data is converted into unsigned integer.
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static uint32_t msg_data_to_number(const void* data, uint32_t len)
{
    uint32_t val = 0;

    RETURN_VAL_IF(data == NULL || len == 0, NET_WARN, 0);

    switch ( len )
    {
    case 4:  val = (uint32_t)*(uint32_t *)data; break;
    case 2:  val = (uint32_t)*(uint16_t *)data; break;
    case 1:  val = (uint32_t)*(uint8_t  *)data; break;
    default: NET_WARN("invalid len(%u)", len);  break;
    }

    return val;
}

/**
 * @brief       The callback function of network register eventID is notified.
 * @param[in]   msg     : The parameter message of event notification.
 * @param[in]   ctx     : The context of callback.
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static void netevent_client_callback(const EVT_MSG_S* msg, void* ctx)
{
    NET_CTX_S* net_ctx = (NET_CTX_S *)ctx;

    NET_TRACE("from module(%u) event_type(0x%08x) data_length(%u)", msg->module_id, msg->event_type, msg->data_length);
    switch ( msg->event_type )
    {
    case EVT_TYPE_NET_HOST_NAME_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length == 0, NET_WARN);
            netctx_update_hostname(net_ctx, (char *)msg->data);
            break;
        }
    case EVT_TYPE_NET_ETH_SPEED_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length == 0, NET_WARN);
            netctx_update_wired_speed(net_ctx, (IFACE_SPEED_E)msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_NET_ETH_SWITCH_REQUEST:
        {
            netctx_update_iface_switch(net_ctx, IFACE_ID_ETH, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_NET_ETH_IPV4_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(NET_IPV4_CONF_S), NET_WARN);
            netctx_update_ipv4_config(net_ctx, IFACE_ID_ETH, (NET_IPV4_CONF_S *)msg->data);
            break;
        }
    case EVT_TYPE_NET_ETH_DNSV4_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(NET_DNSV4_CONF_S), NET_WARN);
            netctx_update_dnsv4_config(net_ctx, IFACE_ID_ETH, (NET_DNSV4_CONF_S *)msg->data);
            break;
        }
    case EVT_TYPE_NET_ETH_IPV6_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(NET_IPV6_CONF_S), NET_WARN);
            netctx_update_ipv6_config(net_ctx, IFACE_ID_ETH, (NET_IPV6_CONF_S *)msg->data);
            break;
        }

#if CONFIG_NET_WIFI
    case EVT_TYPE_NET_WIFI_SWITCH_REQUEST:
        {
            netctx_update_iface_switch(net_ctx, IFACE_ID_STA, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_NET_WIFI_SCAN_SSID_REQUEST:
        {
            netctx_scan_wifi_ssid(net_ctx, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_NET_WIFI_CONNECT_REQUEST:
        {
            RETURN_IF(msg->data != NULL && msg->data_length != sizeof(WIFI_CONN_CONF_S), NET_WARN);
            netctx_connect_wifi_station(net_ctx, (WIFI_CONN_CONF_S *)(msg->data), 0);
            break;
        }
    case EVT_TYPE_NET_WIFI_WPS_REQUEST:
        {
            netctx_request_wps_command(net_ctx, (char *)msg->data);
            break;
        }
    case EVT_TYPE_NET_WIFI_IPV4_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data != NULL && msg->data_length != sizeof(NET_IPV4_CONF_S), NET_WARN);
            netctx_update_ipv4_config(net_ctx, IFACE_ID_STA, (NET_IPV4_CONF_S *)msg->data);
            break;
        }
    case EVT_TYPE_NET_WIFI_DNSV4_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data != NULL && msg->data_length != sizeof(NET_DNSV4_CONF_S), NET_WARN);
            netctx_update_dnsv4_config(net_ctx, IFACE_ID_STA, (NET_DNSV4_CONF_S *)msg->data);
            break;
        }
    case EVT_TYPE_NET_WIFI_IPV6_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data != NULL && msg->data_length != sizeof(NET_IPV6_CONF_S), NET_WARN);
            netctx_update_ipv6_config(net_ctx, IFACE_ID_STA, (NET_IPV6_CONF_S *)msg->data);
            break;
        }
    case EVT_TYPE_NET_WFD_SWITCH_REQUEST:
        {
            netctx_update_iface_switch(net_ctx, IFACE_ID_WFD, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_NET_WFD_RESPONSE_REQUEST:
        {
            wifi_ctrl_wfd_response(msg_data_to_number(msg->data, msg->data_length));
            break;
        }
#endif /* CONFIG_NET_WIFI */

    case EVT_TYPE_NET_ADDRESS_BOOK_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(ADDRBOOK_OP_S), NET_WARN);
            ADDRBOOK_OP_S* op = (ADDRBOOK_OP_S *)msg->data;

            switch ( op->type )
            {
            case AB_TYPE_MAIL_GROUP: netctx_update_mail_grouplist(net_ctx, op); break;
            case AB_TYPE_MAIL:       netctx_update_mail_addrbook (net_ctx, op); break;
            case AB_TYPE_FTP:        netctx_update_ftp_addrbook  (net_ctx, op); break;
            case AB_TYPE_SMB:        netctx_update_smb_addrbook  (net_ctx, op); break;
            default: NET_WARN("invalid addrbook type(%d)", op->type);           break;
            }
            break;
        }
#if CONFIG_NET_SMTP
    case EVT_TYPE_NET_SMTP_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(NET_SMTP_CONF_S), NET_WARN);
            NET_SMTP_CONF_S* smtp_conf = (NET_SMTP_CONF_S *)msg->data;

            netctx_update_smtp_sender_addr(net_ctx, smtp_conf->sender_addr);
            netctx_update_smtp_server_addr(net_ctx, smtp_conf->server_addr);
            netctx_update_smtp_server_port(net_ctx, (uint32_t)smtp_conf->server_port);
            netctx_update_smtp_server_auth(net_ctx, (uint32_t)smtp_conf->server_auth);
            netctx_update_smtp_sec_mode(net_ctx, (uint32_t)smtp_conf->sec_mode);
            netctx_update_smtp_username(net_ctx, smtp_conf->username);
            netctx_update_smtp_password(net_ctx, smtp_conf->password);
            break;
        }
#endif /* CONFIG_NET_SMTP */

#if CONFIG_NET_WHITELIST
        case EVT_TYPE_NET_WHITELIST_SWITCH_REQUEST:
            {
                netctx_update_whitelist_switch(net_ctx, msg_data_to_number(msg->data, msg->data_length));
                break;
            }
#endif /* CONFIG_NET_WHITELIST */

    case EVT_TYPE_NET_EXPORT_LOG_REQUEST:                   ///< 日志下载
        {
            netdata_set_export_log_switch(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }

    /* ----------------------outside the network module------------------------------------ */
    case EVT_TYPE_PLATFORM_PRODUCT_1284_STRING_MODIFY:      ///< IEEE-1284字符串
        {
            NET_DEBUG("str_1284(%s)", (char *)msg->data);
            netdata_set_ieee1284(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_MFG_NAME_MODIFY:                 ///< 厂商名称，例如：Pantum
        {
            NET_DEBUG("mfg_name(%s)", (char *)msg->data);
            netdata_set_mfg_name(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_SERIES_NAME_MODIFY:              ///< 产品系列名称，例如：CP2100
        {
            NET_DEBUG("ser_name(%s)", (char *)msg->data);
            netdata_set_ser_name(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_PDT_NAME_MODIFY:                 ///< 产品名称，例如：CP2100DW
        {
            NET_DEBUG("pdt_name(%s)", (char *)msg->data);
            netdata_set_pdt_name(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_MODIFY:       ///< 产品序列号
        {
            NET_DEBUG("pdt_sn(%s)", (char *)msg->data);
            netdata_set_pdt_sn(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_FIRMWARE_VERSION_MODIFY:         ///< 固件版本号
        {
            NET_DEBUG("fw_ver(%s)", (char *)msg->data);
            netdata_set_fw_ver(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_LOCATION_MODIFY:                 ///< 位置信息
        {
            NET_DEBUG("location(%s)", (char *)msg->data);
            netdata_set_location(net_ctx->data_mgr, (const char *)msg->data);
            netctx_push_netport_subject(net_ctx, PORT_UPDATE_BONJOUR | PORT_UPDATE_SNMP);
            break;
        }
    case EVT_TYPE_PLATFORM_CONTACT_INFO_MODIFY:             ///< 联系人
        {
            NET_DEBUG("contacts(%s)", (char *)msg->data);
            netdata_set_contacts(net_ctx->data_mgr, (const char *)msg->data);
            netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
            break;
        }
    case EVT_TYPE_PLATFORM_PROPERTY_NUMBER_MODIFY:          ///< 财产编号
        {
            NET_DEBUG("prop_num(%s)", (char *)msg->data);
            netdata_set_prop_num(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY:            ///< 面板语言
        {
            netdata_set_language(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_COUNTRY_CODE_MODIFY:             ///< 国家代码
        {
            netdata_set_country(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_PID_MODIFY:                      ///< 产品ID
        {
            netdata_set_pdt_id(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_TIMEZONE_MODIFY:                 ///< 系统时区
        {
            netdata_set_timezone(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_SCAN_ENABLE_MODIFY:              ///< 是否支持扫描
        {
            netdata_set_support_scan(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_DUPLEX_ENABLE_MODIFY:            ///< 是否支持双面打印
        {
            netdata_set_support_duplex(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_COLOR_MODIFY:                    ///< 是否支持彩色打印
        {
            netdata_set_support_color(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_WIRED_ENABLE_MODIFY:             ///< 硬件是否支持有线网络
        {
            netdata_set_support_wired(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            pi_sem_post(s_platform_wired_sem);
            break;
        }
    case EVT_TYPE_PLATFORM_WIRELESS_ENABLE_MODIFY:          ///< 硬件是否支持无线网络（webpage wifi界面是否显示）
        {
            netdata_set_support_wifi(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            pi_sem_post(s_platform_wifi_sem);
            break;
        }
    case EVT_TYPE_PLATFORM_IO_TIMEOUT_MODIFY:               ///< 打印超时时间
        {
            netdata_set_io_timeout(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_PRINT_LANGUAGE_MODIFY:           ///< 打印语言
        {
            netdata_set_print_language(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_SPEED_MODIFY:                    ///< 打印速度
        {
            netdata_set_print_speed(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PRNSDK_ENABLE_MODIFY:
        {
            netdata_set_prnsdk_enabled(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_SLEEP_TIME_MODIFY:                  ///< 休眠时间
        {
            netdata_set_sleep_time(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_SLEEP_MODE_MODIFY:                  ///< 休眠模式
        {
            netdata_set_sleep_mode(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_COLOR_COPY_ENABLE_MODIFY:           ///< 彩色复印管控开关
        {
            netdata_set_copy_color_ctrl(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_MODIFY:         ///< 彩色复印密码
        {
            RETURN_IF(msg->data == NULL || msg->data_length == 0, NET_WARN);
            netdata_set_copy_color_pswd(net_ctx->data_mgr, (const char *)msg->data);
            break;
        }
    case EVT_TYPE_PANEL_SAVE_TONER_MODE_MODIFY:            ///< 省墨设置
        {
            netdata_set_save_toner(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_DATE_FORMAT_MODIFY:                 ///< 日期格式
        {
            netdata_set_date_format(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_TIME_FORMAT_MODIFY:                 ///< 时间格式
        {
            netdata_set_time_format(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_SYS_VOLUE_MODIFY:                   ///< 音量设置
        {
            netdata_set_sys_volume(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_LCD_BACKLIGHT_MODIFY:              ///< 屏幕背光设置
        {
            netdata_set_lcd_backlight(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_SAFETY_MANAGEMENT_PSW_MODIFY:                ///< 面板安全管理密码
        {
            netdata_set_secure_password(net_ctx->data_mgr, msg->data);
            break;
        }
    case EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_MODIFY:                 ///< 出错作业处理模式
        {
            netdata_set_err_deal_mode(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_MODIFY:                 ///< 出错作业延时删除时间
        {
            netdata_set_err_deal_time(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY:             ///< 面板固件版本号
        {
            netdata_set_panl_fw_ver(net_ctx->data_mgr, msg->data);
            break;
        }
    case EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY:                 ///< 面板子板卡序列号
        {
            netdata_set_bord_id(net_ctx->data_mgr, msg->data);
            break;
        }
    case EVT_TYPE_USBDEVICE_CONNECTION_CHANGED:             ///< USB Device是否有连接
        {
            netdata_set_usb_dev_status(net_ctx->data_mgr, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PRINT_CONSUMPTION_PARAM:                  ///< 打印机耗材参数（型号、序列号、最大打印页数等）
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_CONSUMPTION_PARAM_S), NET_WARN);
            PRINT_CONFIG_CONSUMPTION_PARAM_S* param = (PRINT_CONFIG_CONSUMPTION_PARAM_S *)msg->data;

            NET_DEBUG("EVT_TYPE_PRINT_CONSUMPTION_PARAM:\n maxpages toner(%u-%u-%u-%u) drum(%u-%u-%u-%u)\n"\
                                                         " serial   toner(%s-%s-%s-%s) drum(%s-%s-%s-%s)\n"\
                                                         " model    toner(%s-%s-%s-%s) durm(%s-%s-%s-%s)",
                    param->tb_c_max_print,  param->tb_m_max_print,  param->tb_y_max_print,  param->tb_k_max_print,
                    param->dr_c_max_print,  param->dr_m_max_print,  param->dr_y_max_print,  param->dr_k_max_print,
                    param->tb_c_serial,     param->tb_m_serial,     param->tb_y_serial,     param->tb_k_serial,
                    param->dr_c_serial,     param->dr_m_serial,     param->dr_y_serial,     param->dr_k_serial,
                    param->tb_c_model,      param->tb_m_model,      param->tb_y_model,      param->tb_k_model,
                    param->dr_c_model,      param->dr_m_model,      param->dr_y_model,      param->dr_k_model);

            netdata_set_tb_maxpages(net_ctx->data_mgr, MARKER_ID_C, param->tb_c_max_print);    ///< 青色 碳粉盒 最大打印页数
            netdata_set_tb_maxpages(net_ctx->data_mgr, MARKER_ID_M, param->tb_m_max_print);    ///< 品红 碳粉盒 最大打印页数
            netdata_set_tb_maxpages(net_ctx->data_mgr, MARKER_ID_Y, param->tb_y_max_print);    ///< 黄色 碳粉盒 最大打印页数
            netdata_set_tb_maxpages(net_ctx->data_mgr, MARKER_ID_K, param->tb_k_max_print);    ///< 黑色 碳粉盒 最大打印页数
            netdata_set_tb_serial  (net_ctx->data_mgr, MARKER_ID_C, param->tb_c_serial);       ///< 青色 碳粉盒 序列号
            netdata_set_tb_serial  (net_ctx->data_mgr, MARKER_ID_M, param->tb_m_serial);       ///< 品红 碳粉盒 序列号
            netdata_set_tb_serial  (net_ctx->data_mgr, MARKER_ID_Y, param->tb_y_serial);       ///< 黄色 碳粉盒 序列号
            netdata_set_tb_serial  (net_ctx->data_mgr, MARKER_ID_K, param->tb_k_serial);       ///< 黑色 碳粉盒 序列号
            netdata_set_tb_model   (net_ctx->data_mgr, MARKER_ID_C, param->tb_c_model);        ///< 青色 碳粉盒 型号
            netdata_set_tb_model   (net_ctx->data_mgr, MARKER_ID_M, param->tb_m_model);        ///< 品红 碳粉盒 型号
            netdata_set_tb_model   (net_ctx->data_mgr, MARKER_ID_Y, param->tb_y_model);        ///< 黄色 碳粉盒 型号
            netdata_set_tb_model   (net_ctx->data_mgr, MARKER_ID_K, param->tb_k_model);        ///< 黑色 碳粉盒 型号
            netdata_set_dr_maxpages(net_ctx->data_mgr, MARKER_ID_C, param->dr_c_max_print);    ///< 青色 鼓组件 最大打印页数
            netdata_set_dr_maxpages(net_ctx->data_mgr, MARKER_ID_M, param->dr_m_max_print);    ///< 品红 鼓组件 最大打印页数
            netdata_set_dr_maxpages(net_ctx->data_mgr, MARKER_ID_Y, param->dr_y_max_print);    ///< 黄色 鼓组件 最大打印页数
            netdata_set_dr_maxpages(net_ctx->data_mgr, MARKER_ID_K, param->dr_k_max_print);    ///< 黑色 鼓组件 最大打印页数
            netdata_set_dr_serial  (net_ctx->data_mgr, MARKER_ID_C, param->dr_c_serial);       ///< 青色 鼓组件 序列号
            netdata_set_dr_serial  (net_ctx->data_mgr, MARKER_ID_M, param->dr_m_serial);       ///< 品红 鼓组件 序列号
            netdata_set_dr_serial  (net_ctx->data_mgr, MARKER_ID_Y, param->dr_y_serial);       ///< 黄色 鼓组件 序列号
            netdata_set_dr_serial  (net_ctx->data_mgr, MARKER_ID_K, param->dr_k_serial);       ///< 黑色 鼓组件 序列号
            netdata_set_dr_model   (net_ctx->data_mgr, MARKER_ID_C, param->dr_c_model);        ///< 青色 鼓组件 型号
            netdata_set_dr_model   (net_ctx->data_mgr, MARKER_ID_M, param->dr_m_model);        ///< 品红 鼓组件 型号
            netdata_set_dr_model   (net_ctx->data_mgr, MARKER_ID_Y, param->dr_y_model);        ///< 黄色 鼓组件 型号
            netdata_set_dr_model   (net_ctx->data_mgr, MARKER_ID_K, param->dr_k_model);        ///< 黑色 鼓组件 型号
            break;
        }
    case EVT_TYPE_PRINT_CONSUMPTION_STATUS:                 ///< 打印机耗材状态
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_CONSUMPTION_STATUS_S), NET_WARN);
            PRINT_CONFIG_CONSUMPTION_STATUS_S* status = (PRINT_CONFIG_CONSUMPTION_STATUS_S *)msg->data;

            NET_DEBUG("EVT_TYPE_PRINT_CONSUMPTION_STATUS:\n status toner(%d-%d-%d-%d) drum(%d-%d-%d-%d) waste toner(%d)",
                    status->tb_c_status, status->tb_m_status, status->tb_y_status, status->tb_k_status,
                    status->dr_c_status, status->dr_m_status, status->dr_y_status, status->dr_k_status,
                    status->waste_toner_status);

            netdata_set_tb_status (net_ctx->data_mgr, MARKER_ID_C, status->tb_c_status);        ///< 青色 碳粉盒 状态
            netdata_set_tb_status (net_ctx->data_mgr, MARKER_ID_M, status->tb_m_status);        ///< 品红 碳粉盒 状态
            netdata_set_tb_status (net_ctx->data_mgr, MARKER_ID_Y, status->tb_y_status);        ///< 黄色 碳粉盒 状态
            netdata_set_tb_status (net_ctx->data_mgr, MARKER_ID_K, status->tb_k_status);        ///< 黑色 碳粉盒 状态
            netdata_set_dr_status (net_ctx->data_mgr, MARKER_ID_C, status->dr_c_status);        ///< 青色 鼓组件 状态
            netdata_set_dr_status (net_ctx->data_mgr, MARKER_ID_M, status->dr_m_status);        ///< 品红 鼓组件 状态
            netdata_set_dr_status (net_ctx->data_mgr, MARKER_ID_Y, status->dr_y_status);        ///< 黄色 鼓组件 状态
            netdata_set_dr_status (net_ctx->data_mgr, MARKER_ID_K, status->dr_k_status);        ///< 黑色 鼓组件 状态
            netdata_set_wtb_status(net_ctx->data_mgr, status->waste_toner_status);              ///<      废粉瓶 状态
            netctx_push_sysstat_subject(net_ctx, 0);
            break;
        }
    case EVT_TYPE_PRINT_CONSUMPTION_INFO:                   ///< 打印机耗材信息
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_CONSUMPTION_INFO_S), NET_WARN);
            PRINT_CONFIG_CONSUMPTION_INFO_S* info = (PRINT_CONFIG_CONSUMPTION_INFO_S *)msg->data;

            NET_DEBUG("EVT_TYPE_PRINT_CONSUMPTION_INFO:\n remain toner(%u-%u-%u-%u) drum(%u-%u-%u-%u)",
                    info->tb_c_remain, info->tb_m_remain, info->tb_y_remain, info->tb_k_remain,
                    info->dr_c_remain, info->dr_m_remain, info->dr_y_remain, info->dr_k_remain);

            netdata_set_tb_remain(net_ctx->data_mgr, MARKER_ID_C, info->tb_c_remain);          ///< 青色 碳粉盒 余量
            netdata_set_tb_remain(net_ctx->data_mgr, MARKER_ID_M, info->tb_m_remain);          ///< 品红 碳粉盒 余量
            netdata_set_tb_remain(net_ctx->data_mgr, MARKER_ID_Y, info->tb_y_remain);          ///< 黄色 碳粉盒 余量
            netdata_set_tb_remain(net_ctx->data_mgr, MARKER_ID_K, info->tb_k_remain);          ///< 黑色 碳粉盒 余量
            netdata_set_dr_remain(net_ctx->data_mgr, MARKER_ID_C, info->dr_c_remain);          ///< 青色 鼓组件 余量
            netdata_set_dr_remain(net_ctx->data_mgr, MARKER_ID_M, info->dr_m_remain);          ///< 品红 鼓组件 余量
            netdata_set_dr_remain(net_ctx->data_mgr, MARKER_ID_Y, info->dr_y_remain);          ///< 黄色 鼓组件 余量
            netdata_set_dr_remain(net_ctx->data_mgr, MARKER_ID_K, info->dr_k_remain);          ///< 黑色 鼓组件 余量
            netdata_set_dv_remain(net_ctx->data_mgr, MARKER_ID_C, info->dv_c_remain);          ///< 青色 显影器 余量
            netdata_set_dv_remain(net_ctx->data_mgr, MARKER_ID_M, info->dv_m_remain);          ///< 品红 显影器 余量
            netdata_set_dv_remain(net_ctx->data_mgr, MARKER_ID_Y, info->dv_y_remain);          ///< 黄色 显影器 余量
            netdata_set_dv_remain(net_ctx->data_mgr, MARKER_ID_K, info->dv_k_remain);          ///< 黑色 显影器 余量
            netdata_set_xfer_roller_remain(net_ctx->data_mgr, info->transfer_roller_remain);   ///< 转印辊 余量
            netdata_set_xfer_belt_remain(net_ctx->data_mgr, info->transfer_belt_remain);       ///< 转印带 余量
            netdata_set_fuser_remain(net_ctx->data_mgr, info->fuser_remain);                   ///< 定影器 余量
            break;
        }
    case EVT_TYPE_PRINT_INSTALL_INFO:                       ///< 打印机部件安装信息
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_INSTALL_INFO_S), NET_WARN);
            PRINT_CONFIG_INSTALL_INFO_S* install = (PRINT_CONFIG_INSTALL_INFO_S *)msg->data;

            NET_DEBUG("EVT_TYPE_PRINT_INSTALL_INFO:\n tray install(%d|%d|%d|%d|%d|%d|%d)", (int32_t)install->tray_1, (int32_t)install->tray_2,
                                                                                           (int32_t)install->tray_3, (int32_t)install->tray_4,
                                                                                           (int32_t)install->tray_lct_in,
                                                                                           (int32_t)install->tray_lct_out,
                                                                                           (int32_t)install->tray_multi);

            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_STANDARD,          install->tray_1);
            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_2,                 install->tray_2);
            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_3,                 install->tray_3);
            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_4,                 install->tray_4);
            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_External_LCT_IN,   install->tray_lct_in);
            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_External_LCT_OUT,  install->tray_lct_out);
            netdata_set_tray_install(net_ctx->data_mgr, TRAY_INPUT_MULTIFUNCTION,     install->tray_multi);
            break;
        }
    case EVT_TYPE_PRINTERRES_PAPER_SIZE_CONFIG_MODIFY:      ///< 打印机纸盒信息(对应FOOLSCAP3,STATEMENT)
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_TRAY_INFO_S), NET_WARN);
            PRINT_CONFIG_TRAY_INFO_S* tray = (PRINT_CONFIG_TRAY_INFO_S *)msg->data;

            NET_DEBUG("EVT_TYPE_PRINT_TRAY_INFO:\n tray info(%d|%d|%d|%d|%d|%d|%d)", tray->tray_1_paper_remain, tray->tray_2_paper_remain,
                                                                                     tray->tray_3_paper_remain, tray->tray_4_paper_remain,
                                                                                     tray->tray_lct_in_paper_remain,
                                                                                     tray->tray_lct_out_paper_remain,
                                                                                     tray->tray_multi_paper_remain);

            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_STANDARD,         tray->tray_1_paper_remain);
            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_2,                tray->tray_2_paper_remain);
            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_3,                tray->tray_3_paper_remain);
            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_4,                tray->tray_4_paper_remain);
            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_External_LCT_IN,  tray->tray_lct_in_paper_remain);
            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_External_LCT_OUT, tray->tray_lct_out_paper_remain);
            netdata_set_tray_status(net_ctx->data_mgr, TRAY_INPUT_MULTIFUNCTION,    tray->tray_multi_paper_remain);

            NET_DEBUG("EVT_TYPE_PRINT_TRAY_INFO:\n paper size(%d|%d|%d|%d|%d|%d|%d)", (int32_t)tray->tray_1_paper_size, (int32_t)tray->tray_2_paper_size,
                                                                                      (int32_t)tray->tray_3_paper_size, (int32_t)tray->tray_4_paper_size,
                                                                                      (int32_t)tray->tray_lct_in_paper_size,
                                                                                      (int32_t)tray->tray_lct_out_paper_size,
                                                                                      (int32_t)tray->tray_multi_paper_size);

            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_STANDARD,          tray->tray_1_paper_size);
            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_2,                 tray->tray_2_paper_size);
            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_3,                 tray->tray_3_paper_size);
            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_4,                 tray->tray_4_paper_size);
            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_External_LCT_IN,   tray->tray_lct_in_paper_size);
            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_External_LCT_OUT,  tray->tray_lct_out_paper_size);
            netdata_set_paper_size(net_ctx->data_mgr, TRAY_INPUT_MULTIFUNCTION,     tray->tray_multi_paper_size);

            NET_DEBUG("EVT_TYPE_PRINT_TRAY_INFO:\n paper type(%d|%d|%d|%d|%d|%d|%d)", (int32_t)tray->tray_1_paper_type, (int32_t)tray->tray_2_paper_type,
                                                                                      (int32_t)tray->tray_3_paper_type, (int32_t)tray->tray_4_paper_type,
                                                                                      (int32_t)tray->tray_lct_in_paper_type,
                                                                                      (int32_t)tray->tray_lct_out_paper_type,
                                                                                      (int32_t)tray->tray_multi_paper_type);

            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_STANDARD,          tray->tray_1_paper_type);
            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_2,                 tray->tray_2_paper_type);
            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_3,                 tray->tray_3_paper_type);
            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_4,                 tray->tray_4_paper_type);
            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_External_LCT_IN,   tray->tray_lct_in_paper_type);
            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_External_LCT_OUT,  tray->tray_lct_out_paper_type);
            netdata_set_paper_type(net_ctx->data_mgr, TRAY_INPUT_MULTIFUNCTION,     tray->tray_multi_paper_type);
            break;
        }
    case EVT_TYPE_PRINT_STATISTIC_INFO:                     ///< 打印机打印计数
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_STATISTIC_INFO_S), NET_WARN);
            PRINT_CONFIG_STATISTIC_INFO_S* pc = (PRINT_CONFIG_STATISTIC_INFO_S *)msg->data;
            PAPER_SIZE_STATISTIC_INFO_S* psc;

            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_TOTAL,  pc->page_total_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_DUPLEX, pc->sheet_print_mode_counter.duplex_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR,  pc->page_color_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO,   pc->page_mono_counter);

            psc = &(pc->page_paper_size_color_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_A3,          psc->a3_counter + psc->a3_wide1_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_A4,          psc->a4_counter + psc->a4l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_A5,          psc->a5_counter + psc->a5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_A6,          psc->a6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_BIG16K,      psc->costom_big_16k_counter + psc->costom_big_16kl_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_EXECUTIVE,   psc->executive_counter + psc->executive_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_FOOLSCAP3,   psc->foolscap3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_INVOICE,     psc->invoice_counter + psc->invoice_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_JIS_B4,      psc->jis_b4_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_JIS_B5,      psc->jis_b5_counter + psc->jis_b5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_JIS_B6,      psc->jis_b6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_LEDGER,      psc->ledger_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_LETTER,      psc->letter_counter + psc->letter_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_LEGAL,       psc->legal_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_SRA3,        psc->sra3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_8K,          psc->_8k_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_COLOR_USERDEF,     psc->user_define1_counter + psc->user_define2_counter + psc->user_define3_counter);

            psc = &(pc->page_paper_size_mono_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_A3,           psc->a3_counter + psc->a3_wide1_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_A4,           psc->a4_counter + psc->a4l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_A5,           psc->a5_counter + psc->a5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_A6,           psc->a6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_BIG16K,       psc->costom_big_16k_counter + psc->costom_big_16kl_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_EXECUTIVE,    psc->executive_counter + psc->executive_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_FOOLSCAP3,    psc->foolscap3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_INVOICE,      psc->invoice_counter + psc->invoice_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_JIS_B4,       psc->jis_b4_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_JIS_B5,       psc->jis_b5_counter + psc->jis_b5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_JIS_B6,       psc->jis_b6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_LEDGER,       psc->ledger_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_LETTER,       psc->letter_counter + psc->letter_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_LEGAL,        psc->legal_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_SRA3,         psc->sra3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_8K,           psc->_8k_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_PRINT_MONO_USERDEF,      psc->user_define1_counter + psc->user_define2_counter + psc->user_define3_counter);
            break;
        }
    case EVT_TYPE_PRINT_STATISTIC_COPY_INFO:                ///< 打印机复印计数
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_STATISTIC_INFO_S), NET_WARN);
            PRINT_CONFIG_STATISTIC_INFO_S* pc = (PRINT_CONFIG_STATISTIC_INFO_S *)msg->data;
            PAPER_SIZE_STATISTIC_INFO_S* psc;

            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_TOTAL,  pc->page_total_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_DUPLEX, pc->sheet_print_mode_counter.duplex_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR,  pc->page_color_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO,   pc->page_mono_counter);

            psc = &(pc->page_paper_size_color_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_A3,           psc->a3_counter + psc->a3_wide1_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_A4,           psc->a4_counter + psc->a4l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_A5,           psc->a5_counter + psc->a5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_A6,           psc->a6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_BIG16K,       psc->costom_big_16k_counter + psc->costom_big_16kl_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_EXECUTIVE,    psc->executive_counter + psc->executive_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_FOOLSCAP3,    psc->foolscap3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_INVOICE,      psc->invoice_counter + psc->invoice_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_JIS_B4,       psc->jis_b4_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_JIS_B5,       psc->jis_b5_counter + psc->jis_b5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_JIS_B6,       psc->jis_b6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_LEDGER,       psc->ledger_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_LETTER,       psc->letter_counter + psc->letter_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_LEGAL,        psc->legal_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_SRA3,         psc->sra3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_8K,           psc->_8k_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_COLOR_USERDEF,      psc->user_define1_counter + psc->user_define2_counter + psc->user_define3_counter);

            psc = &(pc->page_paper_size_mono_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_A3,            psc->a3_counter + psc->a3_wide1_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_A4,            psc->a4_counter + psc->a4l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_A5,            psc->a5_counter + psc->a5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_A6,            psc->a6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_BIG16K,        psc->costom_big_16k_counter + psc->costom_big_16kl_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_EXECUTIVE,     psc->executive_counter + psc->executive_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_FOOLSCAP3,     psc->foolscap3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_INVOICE,       psc->invoice_counter + psc->invoice_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_JIS_B4,        psc->jis_b4_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_JIS_B5,        psc->jis_b5_counter + psc->jis_b5l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_JIS_B6,        psc->jis_b6_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_LEDGER,        psc->ledger_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_LETTER,        psc->letter_counter + psc->letter_l_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_LEGAL,         psc->legal_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_SRA3,          psc->sra3_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_8K,            psc->_8k_counter);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_COPY_MONO_USERDEF,       psc->user_define1_counter + psc->user_define2_counter + psc->user_define3_counter);

            break;
        }
    case EVT_TYPE_SCAN_COPY_TOTAL_ADFSCAN_PAGE_NUM_MODIFY:///< 扫描计数-ADF扫描到复印
        {
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_ADF_TO_COPY, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_NUM_MODIFY:     ///< 扫描计数-ADF扫描
        {
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_ADF_TOTAL, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_SCAN_COPY_TOTAL_FBSCAN_PAGE_NUM_MODIFY: ///< 扫描计数-FB扫描到复印
        {
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_FB_TO_COPY, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_NUM_MODIFY:      ///< 扫描计数-FB扫描
        {
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_FB_TOTAL, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_SCAN_DOUBLE_PAGE_COUNT_MODIFY:            ///< 扫描计数-双面
        {
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_DUPLEX, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_SCAN_PAPER_SIZE_COLOR_NUM_MODIFY:         ///< 扫描计数-彩色
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(SCAN_PAPER_SIZE_NUM_S), NET_WARN);
            SCAN_PAPER_SIZE_NUM_S* pc = (SCAN_PAPER_SIZE_NUM_S *)msg->data;

            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR,          PAGES_COUNTER_SUM(pc));
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_A3,       pc->a3_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_A4,       pc->a4_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_A5,       pc->a5_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_JIS_B5,   pc->jisb5_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_A6,       pc->a6_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_JIS_B6,   pc->jisb6_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_LETTER,   pc->letter_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_LEGAL,    pc->legal_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_8K,       pc->paper_8k_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_COLOR_BIG16K,   pc->big16k_num);
            break;
        }
    case EVT_TYPE_SCAN_PAPER_SIZE_MONO_NUM_MODIFY:          ///< 扫描计数-黑白
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(SCAN_PAPER_SIZE_NUM_S), NET_WARN);
            SCAN_PAPER_SIZE_NUM_S* pc = (SCAN_PAPER_SIZE_NUM_S *)msg->data;

            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO,           PAGES_COUNTER_SUM(pc));
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_A3,        pc->a3_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_A4,        pc->a4_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_A5,        pc->a5_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_JIS_B5,    pc->jisb5_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_A6,        pc->a6_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_JIS_B6,    pc->jisb6_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_LETTER,    pc->letter_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_LEGAL,     pc->legal_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_8K,        pc->paper_8k_num);
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_MONO_BIG16K,    pc->big16k_num);
            break;
        }
    case EVT_TYPE_SCAN_TOTAL_PAGE_COUNT_MODIFY:             ///< 扫描计数
        {
            netdata_set_page_counter(net_ctx->data_mgr, PC_IDX_SCAN_TOTAL, msg_data_to_number(msg->data, msg->data_length));
            break;
        }
    case EVT_TYPE_PRINTERINFO_UPDATE_DYNAMICINFO:           ///< 打印机动态特性
        {
            NET_DEBUG("recv length(%u) DYNAMIC_STATUS_S(%u)", msg->data_length, sizeof(DYNAMIC_STATUS_S));
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(DYNAMIC_STATUS_S), NET_WARN);
            netdata_set_dynamic_feature(net_ctx->data_mgr, msg->data, msg->data_length);
            break;
        }
    case EVT_TYPE_PRINTERINFO_UPDATE_STATICINFO:            ///< 打印机静态特性
        {
            NET_DEBUG("recv length(%u) STATIC_STATUS_S(%u)", msg->data_length, sizeof(STATIC_STATUS_S));
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(STATIC_STATUS_S), NET_WARN);
            netdata_set_static_feature(net_ctx->data_mgr, msg->data, msg->data_length);
            break;
        }
    case EVT_TYPE_SECURITY_AUDITJOB_INFO:                   ///< 审计作业信息
        {
            NET_DEBUG("recv length(%u) AUDIT_JOBS_INFO_S(%u)", msg->data_length, sizeof(AUDIT_JOBS_INFO_S));
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(AUDIT_JOBS_INFO_S), NET_WARN);
            netdata_set_audit_jobs_info(net_ctx->data_mgr, msg->data, msg->data_length);
            break;
        }
    case EVT_TYPE_SECURITY_SOLE_SERIAL_NUMBER:              ///< 唯一序列号
        {
            netdata_set_sole_serial_number(net_ctx->data_mgr, (char *)msg->data);
            break;
        }
    case EVT_TYPE_SYSTEMSTATUS_UPDATE:                      ///< 系统状态
        {
            RETURN_IF(msg->data == NULL || msg->data_length < sizeof(NETSTS_UNIT_S), NET_WARN);
            netsts_save_packet((NETSTS_UNIT_S *)(msg->data), msg->data_length / sizeof(NETSTS_UNIT_S));
            netctx_push_sysstat_subject(net_ctx, 0);
            break;
        }
    case EVT_TYPE_SYSTEMJOB_UPDATE:
        {
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(JOBINFO_S), NET_WARN);
            JOBINFO_S* jobinfo = (JOBINFO_S *)msg->data;
            if ( jobinfo->io_via == IO_VIA_IPP || jobinfo->io_via == IO_VIA_NET )
            {
                NET_DEBUG("job %d update status(%d) detail(%d) current_pages(%d) total_pages(%d)", (int32_t)jobinfo->job_id, (int32_t)jobinfo->status, (int32_t)jobinfo->status_detail,
                                                                                                   (int32_t)jobinfo->current_pages, (int32_t)jobinfo->total_pages);
                netjob_set_page_done(jobinfo->job_id, jobinfo->current_pages);
                if ( jobinfo->status != JOB_FINISHED )
                {
                    switch ( jobinfo->status )
                    {
                        case JOB_RUNNING :
                            netjob_set_state(jobinfo->job_id, NETJOB_STATE_RUNNING);
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    switch ( jobinfo->status_detail )
                    {
                        case JOB_CANCELED :
                            netjob_set_state(jobinfo->job_id, NETJOB_STATE_CANCELED);
                            break;
                        case JOB_ABORTED :
                            netjob_set_state(jobinfo->job_id, NETJOB_STATE_ABORTED);
                            break;
                        case JOB_END :
                            netjob_set_state(jobinfo->job_id, NETJOB_STATE_DONE);
                            break;
                        default:
                            break;
                    }
                }
            }
            if ( jobinfo->status == JOB_FINISHED )
            {
                netctx_push_sysjob_subject(net_ctx, jobinfo);
            }
            break;
        }
    case EVT_TYPE_NET_IPP_FINISHINGS_CONFIG_REQUEST:
        {
            RETURN_IF(msg->data == NULL, NET_WARN);
            int* ipp_finishings_array = msg->data;
            int  i;
            NET_DEBUG("finishings length (%u)", msg->data_length);
            for ( i = 0; i < msg->data_length/sizeof(int); ++i)
            {
                NET_DEBUG("ipp_finishings_array[%d]=(%d)", i, ipp_finishings_array[i]);
                if ( ipp_finishings_array[i] == 0 )
                {
                    break;
                }
            }
            netdata_set_ipp_finisher_info(net_ctx->data_mgr, msg->data, msg->data_length);
            netctx_push_netport_subject(net_ctx, PORT_UPDATE_BONJOUR | PORT_UPDATE_IPP);
            break;
        }
    default:
        {
            NET_DEBUG("ignore event type: 0x%08x", msg->event_type);
            break;
        }
    }
}

void netevent_client_waiting_platform_ready(void)
{
    NET_DEBUG("waiting wired interface status");
    pi_sem_wait(s_platform_wired_sem);

    NET_DEBUG("waiting wifi interface status");
    pi_sem_wait(s_platform_wifi_sem);
}

int32_t netevent_client_construct(NET_CTX_S* net_ctx)
{
    EVT_TYPE_E      event_array[] =
    {
        EVT_TYPE_NET_HOST_NAME_REQUEST,
        EVT_TYPE_NET_ETH_SPEED_REQUEST,
        EVT_TYPE_NET_ETH_SWITCH_REQUEST,
        EVT_TYPE_NET_ETH_IPV4_CONFIG_REQUEST,
        EVT_TYPE_NET_ETH_DNSV4_CONFIG_REQUEST,
        EVT_TYPE_NET_ETH_IPV6_CONFIG_REQUEST,
        EVT_TYPE_NET_WIFI_SWITCH_REQUEST,
        EVT_TYPE_NET_WIFI_SCAN_SSID_REQUEST,
        EVT_TYPE_NET_WIFI_CONNECT_REQUEST,
        EVT_TYPE_NET_WIFI_WPS_REQUEST,
        EVT_TYPE_NET_WIFI_IPV4_CONFIG_REQUEST,
        EVT_TYPE_NET_WIFI_DNSV4_CONFIG_REQUEST,
        EVT_TYPE_NET_WIFI_IPV6_CONFIG_REQUEST,
        EVT_TYPE_NET_WFD_SWITCH_REQUEST,
        EVT_TYPE_NET_WFD_RESPONSE_REQUEST,
        EVT_TYPE_NET_ADDRESS_BOOK_REQUEST,
        EVT_TYPE_NET_SMTP_CONFIG_REQUEST,
        EVT_TYPE_NET_EXPORT_LOG_REQUEST,
        EVT_TYPE_NET_IPP_FINISHINGS_CONFIG_REQUEST,
        EVT_TYPE_NET_WHITELIST_SWITCH_REQUEST,
        /* Platform */
        EVT_TYPE_PLATFORM_PRODUCT_1284_STRING_MODIFY,   ///< IEEE-1284字符串
        EVT_TYPE_PLATFORM_MFG_NAME_MODIFY,              ///< 厂商名称，例如：Pantum
        EVT_TYPE_PLATFORM_SERIES_NAME_MODIFY,           ///< 产品系列名称，例如：CP2100
        EVT_TYPE_PLATFORM_PDT_NAME_MODIFY,              ///< 产品名称，例如：CP2100DW
        EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_MODIFY,    ///< 产品序列号
        EVT_TYPE_PLATFORM_FIRMWARE_VERSION_MODIFY,      ///< 固件版本号
        EVT_TYPE_PLATFORM_LOCATION_MODIFY,              ///< 位置信息
        EVT_TYPE_PLATFORM_CONTACT_INFO_MODIFY,          ///< 联系人
        EVT_TYPE_PLATFORM_PROPERTY_NUMBER_MODIFY,       ///< 财产编号
        EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY,         ///< 面板语言
        EVT_TYPE_PLATFORM_COUNTRY_CODE_MODIFY,          ///< 国家代码
        EVT_TYPE_PLATFORM_PID_MODIFY,                   ///< 产品ID
        EVT_TYPE_PLATFORM_TIMEZONE_MODIFY,              ///< 系统时区
        EVT_TYPE_PLATFORM_SCAN_ENABLE_MODIFY,           ///< 是否支持扫描
        EVT_TYPE_PLATFORM_DUPLEX_ENABLE_MODIFY,         ///< 是否支持双面打印
        EVT_TYPE_PLATFORM_COLOR_MODIFY,                 ///< 是否支持彩色打印
        EVT_TYPE_PLATFORM_WIRED_ENABLE_MODIFY,          ///< 硬件是否支持有线网络
        EVT_TYPE_PLATFORM_WIRELESS_ENABLE_MODIFY,       ///< 硬件是否支持无线网络（webpage wifi界面是否显示）
        EVT_TYPE_PLATFORM_IO_TIMEOUT_MODIFY,            ///< 打印超时时间
        EVT_TYPE_PLATFORM_PRINT_LANGUAGE_MODIFY,        ///< 打印语言
        EVT_TYPE_PLATFORM_SPEED_MODIFY,                 ///< 打印速度
        /* PRNSDK */
        EVT_TYPE_PRNSDK_ENABLE_MODIFY,                  ///< 文印SDK功能使能
        /* Panel */
        EVT_TYPE_PANEL_SLEEP_TIME_MODIFY,               ///< 休眠时间
        EVT_TYPE_PANEL_SLEEP_MODE_MODIFY,               ///< 休眠模式
        EVT_TYPE_PANEL_COLOR_COPY_ENABLE_MODIFY,        ///< 彩色复印管控开关
        EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_MODIFY,      ///< 彩色复印密码
        EVT_TYPE_PANEL_SAVE_TONER_MODE_MODIFY,          ///< 省墨设置
        EVT_TYPE_PANEL_DATE_FORMAT_MODIFY,              ///< 日期格式
        EVT_TYPE_PANEL_TIME_FORMAT_MODIFY,              ///< 时间格式
        EVT_TYPE_PANEL_SYS_VOLUE_MODIFY,                ///< 系统音量
        EVT_TYPE_PANEL_LCD_BACKLIGHT_MODIFY,            ///< 屏幕背光亮度
        EVT_TYPE_PANEL_SAFETY_MANAGEMENT_PSW_MODIFY,    ///< 面板安全管理密码
        EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_MODIFY,   ///< 出错作业处理模式
        EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_MODIFY,    ///< 出错作业延时删除时间
        EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY,      ///< 面板固件版本号
        EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY,       ///< 面板子板卡序列号
        /* USB Device */
        EVT_TYPE_USBDEVICE_CONNECTION_CHANGED,          ///< USB Device是否有连接
        /* Print */
        EVT_TYPE_PRINT_CONSUMPTION_PARAM,               ///< 打印机耗材参数（型号、序列号、最大打印页数等）
        EVT_TYPE_PRINT_CONSUMPTION_STATUS,              ///< 打印机耗材状态
        EVT_TYPE_PRINT_CONSUMPTION_INFO,                ///< 打印机耗材信息
        EVT_TYPE_PRINT_INSTALL_INFO,                    ///< 打印机部件安装信息
        EVT_TYPE_PRINTERRES_PAPER_SIZE_CONFIG_MODIFY,   ///< 打印机纸盒信息(对应FOOLSCAP3,STATEMENT)
        EVT_TYPE_PRINT_STATISTIC_INFO,                  ///< 打印机打印计数
        EVT_TYPE_PRINT_STATISTIC_COPY_INFO,             ///< 打印机复印计数
        /* Scan */
        EVT_TYPE_SCAN_COPY_TOTAL_ADFSCAN_PAGE_NUM_MODIFY, ///< 扫描计数-ADF扫描到复印
        EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_NUM_MODIFY,      ///< 扫描计数-ADF扫描
        EVT_TYPE_SCAN_COPY_TOTAL_FBSCAN_PAGE_NUM_MODIFY,  ///< 扫描计数-FB扫描到复印
        EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_NUM_MODIFY,       ///< 扫描计数-FB扫描
        EVT_TYPE_SCAN_DOUBLE_PAGE_COUNT_MODIFY,             ///< 扫描计数-双面
        EVT_TYPE_SCAN_PAPER_SIZE_COLOR_NUM_MODIFY,          ///< 扫描计数-彩色
        EVT_TYPE_SCAN_PAPER_SIZE_MONO_NUM_MODIFY,           ///< 扫描计数-黑白
        EVT_TYPE_SCAN_PAPER_SIZE_COUNT_MODIFY,              ///< 扫描计数
        /* PrinterInfo */
        EVT_TYPE_PRINTERINFO_UPDATE_DYNAMICINFO,        ///< 打印机动态特性
        EVT_TYPE_PRINTERINFO_UPDATE_STATICINFO,         ///< 打印机静态特性
        /* security */
        EVT_TYPE_SECURITY_AUDITJOB_INFO,                ///< 审计作业信息
        EVT_TYPE_SECURITY_SOLE_SERIAL_NUMBER,           ///< 唯一序列号
        /* SystemStatus */
        EVT_TYPE_SYSTEMSTATUS_UPDATE,                   ///< 系统状态
        /* SystemJob */
        EVT_TYPE_SYSTEMJOB_UPDATE,                      ///< 系统作业
        /* Export Log Switch  */
    };
    size_t          event_count = sizeof(event_array) / sizeof(event_array[0]);
    int32_t         ret = -1;

    RETURN_VAL_IF(net_ctx == NULL || net_ctx->event_client != NULL, NET_WARN, -1); /* 避免重复创建 */

    do
    {
        s_platform_wired_sem = pi_sem_create(0);
        BREAK_IF(s_platform_wired_sem == INVALIDSEM, NET_WARN);

        s_platform_wifi_sem = pi_sem_create(0);
        BREAK_IF(s_platform_wifi_sem == INVALIDSEM, NET_WARN);

        net_ctx->event_client = pi_event_mgr_create_client(EVT_MODULE_NET, netevent_client_callback, net_ctx, &ret);
        if ( net_ctx->event_client == NULL || ret != 0 )
        {
            NET_WARN("create event client failed(%d)", ret);
            break;
        }

        ret = pi_event_mgr_register(net_ctx->event_client, event_array, event_count);
        if ( ret < 0 )
        {
            NET_DEBUG("register event failed(%d)", ret);
            break;
        }
    }
    while ( 0 );

    if ( ret != 0 )
    {
        netevent_client_destruct(net_ctx);
    }
    return ret;
}

void netevent_client_destruct(NET_CTX_S* net_ctx)
{
    if ( net_ctx && net_ctx->event_client )
    {
        pi_event_mgr_destroy_client(net_ctx->event_client);
        net_ctx->event_client = NULL;
    }
    if ( s_platform_wifi_sem != INVALIDSEM )
    {
        pi_sem_destroy(s_platform_wifi_sem);
        s_platform_wifi_sem = INVALIDSEM;
    }
    if ( s_platform_wired_sem != INVALIDSEM )
    {
        pi_sem_destroy(s_platform_wired_sem);
        s_platform_wired_sem = INVALIDSEM;
    }
}
/**
 *@}
 */
