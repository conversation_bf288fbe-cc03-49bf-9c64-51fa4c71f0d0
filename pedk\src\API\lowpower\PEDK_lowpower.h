#ifndef _PEDK_LOWPOWER_
#define _PEDK_LOWPOWER_

#include <quickjs.h>

/*
    声明 QuickJS C 函数由于初始化回调
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv);


JSValue js_lowpower_get_CurrentState(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_lowpower_set_CurrentState(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);


#endif /* _PEDK_LOWPOWR_ */
