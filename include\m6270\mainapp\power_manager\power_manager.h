/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file power_manager.h
 * @addtogroup power_manager
 * @{
 * @addtogroup power_manager
 * <AUTHOR>
 * @date 2023-06-03
 * @brief power_manager module
 */
 
 #ifndef POWERMANAGER_H
#define POWERMANAGER_H
 
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief PowerManager moudle start and initialize, Register PM event and create thread which communicates with EC/SC .
 * @return result
 * @retval =0: success
 *         <0: fail
 * <AUTHOR>
 * @data   2023-7-17
 */
int pi_powermanager_prolog( void );
void wakeup_powermanager(void);

#ifdef __cplusplus
}
#endif
#endif
/**
 * @}
 */