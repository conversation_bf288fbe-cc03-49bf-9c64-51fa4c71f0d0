export function getSystemCapabilitiesList()
{
	console.log("\n#### getSystemCapabilitiesList test ####\n");
	const retstr = js_getSystemCapabilitiesList();
	if(retstr === "EXIT_FAILURE" || retstr === "ENOTPERMISSION")
	{
		return retstr;
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	//将字符转换为对象
	const retobj = JSON.parse(retstr);
	//遍历对象，将键值对以数组形式，再转化为Map对象返回
	const map = new Map(Object.entries(retobj));
	console.log(map);

	return map;
}

export function getSupportedScanPaperSize()
{

  console.log("getSupportedScanPaperSize start");


  var papersize =["MEDIA_SIZE_A3",
                  "MEDIA_SIZE_A4",
                  "MEDIA_SIZE_A4_LEF",
                  "MEDIA_SIZE_A5",
                  "MEDIA_SIZE_A5_LEF",
                  "MEDIA_SIZE_JIS_B4",
                  "MEDIA_SIZE_JIS_B5",
                  "MEDIA_SIZE_JIS_B5_LEF",
                  "MEDIA_SIZE_JIS_B6",
                  "MEDIA_SIZE_ISO_B5",
                  "MEDIA_SIZE_8K",
                  "MEDIA_SIZE_BIG_16K",
                  "MEDIA_SIZE_BIG_16K_LEF",
                  "MEDIA_SIZE_16K",
                  "MEDIA_SIZE_LETTER",
                  "MEDIA_SIZE_LETTER_LEF",
                  "MEDIA_SIZE_LEGAL",
                  "MEDIA_SIZE_FOLIO",
                  "MEDIA_SIZE_EXECUTIVE",
                  "MEDIA_SIZE_INVOICE",
                  "MEDIA_SIZE_INVOICE_LEF",
                  "MEDIA_SIZE_A6",
                  "MEDIA_SIZE_B6",
                  "MEDIA_SIZE_USERDEFINE",
                  "MEDIA_SIZE_LEDGER",
                  "MEDIA_SIZE_A3_WIDE",
                  "MEDIA_SIZE_SRA3",
                  "MEDIA_SIZE_CARD"];

	return papersize;
}

export function getSupportedPrintPaperTray()
{
	var tray_list = ["OPTION_TRAY1","OPTION_TRAY2","OPTION_TRAY3","OPTION_TRAY14","EXTERNAL_HIGH_CAPACITY_TRAY","INSTALL_HIGH_CAPACITY_TRAY","MULTI_FUNCTION_TRAY"];

	return tray_list;
}

export function getSupportedPrintPaperSize(tray)
{
	if(tray == "MULTI_FUNCTION_TRAY")
    {
		var paper_size_list = [	"MEDIA_SIZE_A6",
								"MEDIA_SIZE_JIS_B6",
								"MEDIA_SIZE_STATEMENT",
								"MEDIA_SIZE_A5",
							    "MEDIA_SIZE_JIS_B5",
								"MEDIA_SIZE_EXECUTIVE",
							    "MEDIA_SIZE_BIG_16K",
								"MEDIA_SIZE_A5_LEF",
								"MEDIA_SIZE_A4",
								"MEDIA_SIZE_STATEMENT_LEF",
							    "MEDIA_SIZE_LETTER",
								"MEDIA_SIZE_FOLIO",
								"MEDIA_SIZE_LEGAL",
								"MEDIA_SIZE_JIS_B5_LEF",
								"MEDIA_SIZE_JIS_B4",
								"MEDIA_SIZE_EXECUTIVE_LEF",
								"MEDIA_SIZE_BIG_16K_LEF",
								"MEDIA_SIZE_8K",
							    "MEDIA_SIZE_LETTER_LEF",
								"MEDIA_SIZE_LEDGER",
								"MEDIA_SIZE_A4_LEF",
								"MEDIA_SIZE_A3",
								"MEDIA_SIZE_A3_WIDE",
								"MEDIA_SIZE_SRA3" ];
		return paper_size_list;
	}
	else if((tray == "OPTION_TRAY1")||(tray == "OPTION_TRAY3")||(tray == "OPTION_TRAY4"))
    {
		var paper_size_list = [	"MEDIA_SIZE_A5",
								"MEDIA_SIZE_JIS_B5",
								"MEDIA_SIZE_A4",
								"MEDIA_SIZE_LETTER",
								"MEDIA_SIZE_FOLIO",
								"MEDIA_SIZE_LEGAL",
								"MEDIA_SIZE_JIS_B5_LEF",
								"MEDIA_SIZE_JIS_B4",
								"MEDIA_SIZE_BIG_16K_LEF",
								"MEDIA_SIZE_8K",
								"MEDIA_SIZE_LETTER_LEF",
								"MEDIA_SIZE_LEDGER",
								"MEDIA_SIZE_A4_LEF",
								"MEDIA_SIZE_A3" ];
		return paper_size_list;
	}
	else if(tray == "OPTION_TRAY2")
    {
       var paper_size_list = [	"MEDIA_SIZE_A5",
								"MEDIA_SIZE_JIS_B5",
								"MEDIA_SIZE_A4",
								"MEDIA_SIZE_LETTER",
								"MEDIA_SIZE_FOLIO",
								"MEDIA_SIZE_LEGAL",
								"MEDIA_SIZE_JIS_B5_LEF",
								"MEDIA_SIZE_JIS_B4",
								"MEDIA_SIZE_BIG_16K_LEF",
								"MEDIA_SIZE_8K",
								"MEDIA_SIZE_LETTER_LEF",
								"MEDIA_SIZE_LEDGER",
								"MEDIA_SIZE_A4_LEF",
								"MEDIA_SIZE_A3",
								"MEDIA_SIZE_A3_WIDE",
								"MEDIA_SIZE_SRA3" ];
	   return paper_size_list;
	}
	else if((tray == "EXTERNAL_HIGH_CAPACITY_TRAY")||(tray == "INSTALL_HIGH_CAPACITY_TRAY"))
    {
		 var paper_size_list = ["MEDIA_SIZE_A4_LEF"];
		 return paper_size_list;
	}
	else
    {
		var paper_size_list = "EOPNOTSUPP";
		return paper_size_list;
	}
}

export function getSupportedPrintMediaType(tray)
{
	if(tray == "MULTI_FUNCTION_TRAY")
    {
		var media_type_list = [
								"MEDIA_TYPE_PLAIN",
							    "MEDIA_TYPE_PLAIN_PLUS",
							    "MEDIA_TYPE_THICK_PAPER1",
							    "MEDIA_TYPE_THICK_PAPER1_PLUS",
							    "MEDIA_TYPE_THICK_PAPER2",
							    "MEDIA_TYPE_THICK_PAPER3",
							    "MEDIA_TYPE_TRANSPARENCY",
							    "MEDIA_TYPE_POSTCARD",
							    "MEDIA_TYPE_ENVELOPE",
							    "MEDIA_TYPE_LABEL" ];
		return media_type_list;
	}
	else if((tray == "OPTION_TRAY1")||(tray == "OPTION_TRAY2")||(tray == "OPTION_TRAY3")||
		    (tray == "OPTION_TRAY3")||(tray == "EXTERNAL_HIGH_CAPACITY_TRAY")||
		    (tray == "INSTALL_HIGH_CAPACITY_TRAY"))
    {

		var media_type_list = [
								"MEDIA_TYPE_PLAIN",
								"MEDIA_TYPE_PLAIN_PLUS",
								"MEDIA_TYPE_THICK_PAPER1",
								"MEDIA_TYPE_THICK_PAPER1_PLUS",
								"MEDIA_TYPE_THICK_PAPER2",
								"MEDIA_TYPE_THICK_PAPER3" ];
		return media_type_list;
	}
	else
    {
		var media_type_list = "EOPNOTSUPP";
		return media_type_list;
	}
}


globalThis.pedk.device.capabilities = {}
globalThis.pedk.device.capabilities.getSystemCapabilitiesList = getSystemCapabilitiesList

globalThis.pedk.device.capabilities.getSupportedPrintPaperTray = getSupportedPrintPaperTray
globalThis.pedk.device.capabilities.getSupportedPrintPaperSize = getSupportedPrintPaperSize
globalThis.pedk.device.capabilities.getSupportedPrintMediaType = getSupportedPrintMediaType
globalThis.pedk.device.capabilities.getSupportedScanPaperSize = getSupportedScanPaperSize

