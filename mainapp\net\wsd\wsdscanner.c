#include <sys/eventfd.h>
#include <poll.h>
#include "nettypes.h"
#include "netmodules.h"
#include "http_task.h"
#include "netport.h"
#include "netmisc.h"
#include "netsock.h"
#include "wsdef.h"
#include "wsd.h"
#include "qxml.h"
#include "soap.h"
#include "public_data_proc.h"
#include "job_manager.h"
#include "netjob.h"
#include "wsdjob.h"
#include "wsdsubs.h"
#include "wsdservice.h"
#include "pol/pol_list.h"
#include <pthread.h>

#define DOCUMENT_SIZE_AUTO_DETECT_SUPPORTED "false"

//default maximum size
#define WSD_SCAN_AREA_W     11700
#define WSD_SCAN_AREA_H     17000

#define WSD_ALIGN_W_MASK    0x000F
#define WSD_ALIGN_H_MASK    0x0007
#define WSD_SCAN_TIMEOUT    60
#define WSD_IMAGE_READ_SIZE  0x2000

#define WSD_MESSAGE_JOB_FAILED 1

#define WSD_SCAN_HEADER     \
"HTTP/1.1 200 OK\r\nDate: %s\r\nCache-Control: no-cache\r\n"                \
"Server: bsa/1.0\r\nConnection: close\r\nTransfer-Encoding: chunked\r\n"    \
"Content-Type: multipart/related;start-info=\"application/soap+xml\";"      \
"start=\"%s\";type=\"application/xop+xml\";boundary=\"%s\"\r\n\r\n"

#define WSD_SCAN_DATA       \
"--%s\r\nContent-Type: %s\r\nContent-Transfer-Encoding: %s\r\n"       \
"Content-ID: <%s>\r\n\r\n"

#define WSD_SCAN_XMLNS      "wscn"
#define WSD_SCAN_XMLNS_URL  "http://schemas.microsoft.com/windows/2006/08/wdp/scan"

#define  PushStatus(x)

#define MAX_SCANNER_JOB_NUM 16
typedef struct wsd_scan_trans {
    const char *var;
    int        mode;
    const char *othervar;
}
WSD_SCAN_TRANS_S;

typedef struct wsd_scan_parm
{
    uint32_t scan_mode;
    uint32_t scan_color;
    uint32_t xres;
    uint32_t yres;
    uint32_t area_width;
    uint32_t area_height;
    uint32_t margin_top;
    uint32_t margin_left;
    uint32_t scan_file;

    uint32_t imgtrans;
    uint32_t media_width;
    uint32_t media_height;
    int32_t contrast;
    int32_t brightness;
    uint32_t scal_width;
    uint32_t scal_height;
    uint32_t rotation:1;
    uint32_t docsize_autodetect:1;

    const char *file;
    const char *mode;
    const char *color;
    const char *img_fmt;
}
WSD_SCAN_PARM_S;

struct page_info
{
    struct list_head    page_list;
    char                file_path[FILE_PATH_MAX];
    int32_t             last_page;
};

typedef struct wsd_scan_ticket
{
    WSD_TICKET_S        ticket;
    int                 evtfd;
    char                jobInfo[64];        ///< misc job info string

    pthread_spinlock_t  images_lock;          /// following images_* protected by this lock
    int                 images_tranfered;          ///< image/page transferred for this ticket
    int                 images_scanned;            ///< image/page had been scanned for this ticket
    int                 images_scanned_finish;            ///< image/page had been scanned for this ticket

    WSD_SCAN_PARM_S*    param;

    PI_MUTEX_T          list_lock;
    struct list_head    page_head;
    time_t              start_timer;
}
WSD_SCAN_TICKET_S;

typedef struct wsd_scanner_context
{
    WSD_CTX_S*       wsd_ctx;
    PI_THREAD_T      net_tid;
    PI_THREAD_T      msg_tid;
    int              efd;
    int              bytesProcessed;         ///< bytes xferred this job
    int              documentsProcessed;     ///< document count this job
    PI_MUTEX_T       mutex;
}
WSD_SCANNER_CTX_S;

typedef struct wsdconn_task_private
{
    char        iobuf[TCP_CHUNK_SIZE];
    size_t      received;
    WSD_SERVICE_DATA_S service_data;
}
PRIV_INFO_S;

static WSD_SCANNER_CTX_S* s_wsd_scn_ctx = NULL;

typedef struct wsd_scan_record
{
    uint32_t record;
    time_t   time;
}
WSD_SCAN_RECORD_S;

static WSD_SCAN_RECORD_S s_wscn_record = {0xff, 0};

static WSD_SCAN_TRANS_S scan_inputsrc[] = {
    {"Platen",      SCAN_MODE_FB,    NULL},
    {"ADF",         SCAN_MODE_ADF,   NULL},
    {"ADFDuplex",   SCAN_MODE_DADF,  NULL}, //TODO: SCAN_MODE_ADF ?
    {"Film",        SCAN_MODE_ADF,   NULL}   //TODO: SCAN_MODE_ADF ?
};

static WSD_SCAN_TRANS_S scan_color[] = {
    {"RGB24",               COLOR_RGB,    NULL},
    {"BlackAndWhite1",      COLOR_MONO,   NULL},
    {"Grayscale8",          COLOR_GRAY,   NULL}
};

static int scan_get_job_elements_handler(WSD_TICKET_S* pticket, QXML_S* qxml, OUT SOAP_VAR_S** out_pdata, OUT int* out_valid);
static int WSDscanJobInfoVar(WSD_TICKET_S* pticket, int endState, int summary, OUT SOAP_VAR_S **out_ppvar);
static int WSDscanTicketInfoVar(WSD_TICKET_S* pticket, OUT SOAP_VAR_S **out_ppvar);
static void scan_ticket_clear_page_list(WSD_SCAN_TICKET_S* scan_ticket);
static void ScanTicketDestroy(WSD_TICKET_S* pticket);
static ACTION_HANDLER_DEFINITION(wsd_scan_create_job_handler);
static ACTION_HANDLER_DEFINITION(wsd_scan_get_scanner_elements_handler);
static ACTION_HANDLER_DEFINITION(wsd_scan_retrieve_image_handler);
static ACTION_HANDLER_DEFINITION(wsd_scan_validate_scan_ticket_handler);

static ACTION_HNDL_S s_action_table[] =
{
    { "CreateScanJob",       wsd_scan_create_job_handler            },
    { "GetScannerElements",  wsd_scan_get_scanner_elements_handler  },
    { "RetrieveImage",       wsd_scan_retrieve_image_handler        },
    { "CancelJob",           wsd_srvcomm_cancel_job_handler         },
    { "GetJobElements",      wsd_srvcomm_get_job_elements_handler   },
    { "GetActiveJobs",       wsd_srvcomm_get_active_job_handler     },
    { "GetJobHistory",       wsd_srvcomm_get_job_history_handler    },
    { "ValidateScanTicket",  wsd_scan_validate_scan_ticket_handler  },
    { "Probe",               wsd_srvcomm_probe_handler              },
    { NULL,                  NULL                                   },
};

static WSD_SVC_S s_scan_service = {
    .xmlns = WSD_SCAN_XMLNS,
    .xmlns_url = WSD_SCAN_XMLNS_URL,
    .event_rate = WSD_DEFAULT_EVENT_RATE,
    .action_hndl_tbl = s_action_table,
    .get_job_elements_handler = scan_get_job_elements_handler,
    .ticket_destroy = ScanTicketDestroy,
};

static int wsd_message_wait(const WSD_SCAN_TICKET_S* scan_ticket, int timeout_secs)
{
    int            ready;
    uint64_t       evt_data;
    struct pollfd  pfd;

    pfd.fd = scan_ticket->evtfd;
    pfd.events = POLLIN;
    for (size_t i = 0; i < 3; i++)   // retry 2 times
    {
        ready = poll(&pfd, 1, timeout_secs * 1000);
        if (ready == 0)
        {
            NET_DEBUG("wsd_message_wait timeout (%d)", timeout_secs);
            break;
        }
        if (ready > 0 && pfd.events & POLLIN)
        {
            read(scan_ticket->evtfd, &evt_data, sizeof(evt_data));
            return 0;
        }
        // ready < 0, interrupt or other, retry
    }
    return -1;
}

static inline void wsd_message_notify(const WSD_SCAN_TICKET_S* ticket, int msg)
{
    uint64_t evt_data = msg;
    write(ticket->evtfd, &evt_data, sizeof(evt_data));
}

static void job_start_notify( uint32_t job_id, int32_t result , void *context )
{
    uint64_t evt_data;
    WSD_TICKET_S* pticket = context;
    WSD_SCAN_TICKET_S* scan_ticket = (WSD_SCAN_TICKET_S*)pticket;

    RETURN_IF(pticket==NULL, NET_WARN);
    if (result == 0)
    {
        NET_DEBUG("wsd scanner job %u start successfully", job_id);
        evt_data = MSG_DATA_JOB_START;
        pticket->state = NETJOB_STATE_PROCESSING;
    }
    else
    {
        evt_data = MSG_CTRL_JOB_CANCEL;
        pticket->state = NETJOB_STATE_ABORTED;
        NET_WARN("wsd scanner job %u failed to start, result = %d, starting to cancel", job_id, result);
        wsd_cancel_job(&s_scan_service, pticket);
    }
    write(scan_ticket->evtfd, &evt_data, sizeof(evt_data));
}

static int scan_get_job_elements_handler(WSD_TICKET_S* pticket, QXML_S* pxml, OUT SOAP_VAR_S** out_pdata, OUT int* out_valid)
{
    int rc;
    char  req[128] = {0};
    if (! strcmp(QXMLelementName(pxml, req), "JobStatus"))
    {
        rc = WSDscanJobInfoVar(pticket, 0, 0, out_pdata);
        *out_valid = 1;
    }
    else if (! strcmp(QXMLelementName(pxml, req), "ScanTicket"))
    {
        rc = WSDscanTicketInfoVar(pticket, out_pdata);
        *out_valid = 1;
    }
    else
    {
        rc = 0;
        *out_valid = 0;
    }
    return rc;
}

static uint16_t inline wsd_scanner_get_port()
{
    return netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_scn_ctx)) ? WSD_SCN_PORT : 0;
}

static SCAN_JOB_REQUEST_DATA_S* scan_parm_to_request_data(WSD_SCAN_PARM_S* scan_parm)
{
    SCAN_JOB_REQUEST_DATA_S*    scan_req;

    scan_req = (SCAN_JOB_REQUEST_DATA_S *)pi_zalloc(sizeof(SCAN_JOB_REQUEST_DATA_S));
    RETURN_VAL_IF(scan_req == NULL, NET_WARN, NULL);

    scan_req->scan_config_param.scan_dst  = SCAN_JOB_TYPE_WSDSCAN;
    scan_req->scan_config_param.scan_send_router[0] = SCAN_TO_WSD;
    scan_req->scan_config_param.scan_mode = scan_parm->scan_mode;
    scan_req->scan_config_param.combination = 0;
    scan_req->scan_config_param.xres = scan_parm->xres;
    scan_req->scan_config_param.yres = scan_parm->yres;
    if (scan_parm->docsize_autodetect ||
         (scan_parm->scan_mode == SCAN_MODE_FB && scan_parm->area_width == WSD_SCAN_AREA_W && scan_parm->area_height == WSD_SCAN_AREA_H) )
    {
        scan_req->area_width  = 0;
        scan_req->area_height = 0;
        scan_req->margin_top  = 0;
        scan_req->margin_left = 0;
    }
    else
    {    //all is resolution
        scan_req->area_width  = scan_parm->area_width  * scan_parm->xres / 1000;
        scan_req->area_height = scan_parm->area_height * scan_parm->yres / 1000;
        scan_req->margin_top  = scan_parm->margin_top  * scan_parm->yres / 1000;
        scan_req->margin_left = scan_parm->margin_left * scan_parm->xres / 1000;
    }
    scan_req->scan_config_param.image_quality.brightness = 11*scan_parm->brightness/2000 + 6;
    scan_req->scan_config_param.image_quality.contrast = 11*scan_parm->contrast/2000 + 6;
    scan_req->scan_config_param.scan_area  = PAPER_SIZE_USER_DEFINE;
    scan_req->scan_config_param.scan_color = scan_parm->scan_color;
    scan_req->scan_config_param.file_type  = scan_parm->scan_file;
    scan_req->scan_engine_param.use_edge_clean = 1;
    scan_req->scan_engine_param.edge_clean.left = 2;
    scan_req->scan_engine_param.edge_clean.right = 2;
    scan_req->scan_engine_param.edge_clean.bottom = 2;
    scan_req->scan_engine_param.edge_clean.top = 2;
    scan_req->wsdscan_moudle_id = MID_WSDSCAN;
    scan_req->job_via           = IO_VIA_NET;
    scan_req->compress_enable   = ( scan_parm->scan_file == FILE_PDF
                                  || scan_parm->scan_file == FILE_JPEG
                                  || scan_parm->scan_file == FILE_TIFF ) ? 1 : 0;

    NET_DEBUG("margin_top(%u), margin_left(%u), area_width(%u) area_height(%u)",
              scan_req->margin_top, scan_req->margin_left,
              scan_req->area_width, scan_req->area_height);
    return scan_req;
}

static void ticket_add_page(WSD_SCAN_TICKET_S* scan_ticket, const char* file)
{
    struct  page_info*  page;

    RETURN_IF(scan_ticket == NULL || file == NULL, NET_WARN);

    page = (struct page_info *)pi_zalloc(sizeof(struct page_info));
    RETURN_IF(page == NULL, NET_WARN);

    MUTEX_LOCK(scan_ticket->list_lock, {
            NET_DEBUG("job %u add page (%s))", ((WSD_TICKET_S*)scan_ticket)->job_id, file);
            snprintf(page->file_path, sizeof(page->file_path), "%s", file);
            pi_list_add_tail(&page->page_list, &scan_ticket->page_head);
            SPIN_LOCK(&scan_ticket->images_lock, {
                    ++scan_ticket->images_scanned;
            })
    })
}

static int WSDscanTicketInfoVar(WSD_TICKET_S* pticket, OUT SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S* pv, *pvv;
    int rc;

    *out_ppvar = NULL;
    rc = 1;
    do   // TRY
    {
        pv = soap_create_var("wscn:JobDescription", dtchar, 0, pticket->jobIdString, 0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        pvv = pv;
        pvv->m_child = soap_create_var("wscn:JobName", dtchar, 0, pticket->jobName, 0);
        if (! pvv->m_child)
        {
            break;
        }
        pvv = pvv->m_child;
        pvv->m_next = soap_create_var("wscn:JobOriginatingUserName", dtchar, 0, pticket->jobOriginator, 0);
        if (! pvv->m_next)
        {
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:JobInformation", dtchar, 0, ((WSD_SCAN_TICKET_S*)pticket)->jobInfo, 0);
        if (! pvv->m_next)
        {
            break;
        }
        pvv = pvv->m_next;
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int WSDscanJobInfoVar(WSD_TICKET_S* pticket, int endState, int summary, OUT SOAP_VAR_S **out_ppvar)
{
    WSD_SCAN_TICKET_S* scan_ticket = (WSD_SCAN_TICKET_S*)pticket;
    RETURN_VAL_IF(pticket == NULL, NET_WARN, 1);

    SOAP_VAR_S* pv, *pvv;
    char *jobStateString = NULL;
    char numbuf[32];
    int rc = 1;

    *out_ppvar = NULL;
    pv = NULL;
    rc = 1;

    // Call with service locked!
    //
    do   // TRY
    {
        // look up print ticket by jobid
        //
        pv = soap_create_var("wscn:JobId", dtchar, 0, pticket->jobIdString, 0);
        if (! pv)
        {
            break;
        }
        pvv = pv;
        if (endState || summary)
        {
            pvv->m_next = soap_create_var("wscn:JobName", dtchar, 0,  pticket->jobName, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wscn:JobOriginatingUserName", dtchar, 0, pticket->jobOriginator, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
        }

        if (endState)
        {
            jobStateString = "Completed"; //FIXME mjxian
            pvv->m_next = soap_create_var("wscn:JobCompletedState", dtchar, 0, jobStateString, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wscn:JobCompletedStateReasons", dtchar, 0, NULL, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
        }
        else
        {
            pvv->m_next = soap_create_var("wscn:JobState", dtchar, 0, jobStateString, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wscn:JobStateReasons", dtchar, 0, NULL, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
        }
        pvv->m_child = soap_create_var("wscn:JobStateReason", dtchar, 0, "None", 0);
        if (! pvv->m_child)
        {
            break;
        }
        snprintf(numbuf, sizeof(numbuf), "%d", scan_ticket->images_scanned);
        pvv->m_next = soap_create_var("wscn:ScansCompleted", dtint, 0, numbuf, 0);
        if (! pvv->m_next)
        {
            break;
        }
        pvv = pvv->m_next;
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int32_t wsd_scan_update_status(const char** status_string, const char** reason_string, WSD_SERVICE_DATA_S* srv_data, WSD_SCAN_TICKET_S* scan_ticket)
{
    STATUS_ID_E         scan_status;
    const char*         rs;
    const char*         ss;
//    int32_t             state = NETJOB_STATE_RUNNING;
    int32_t             ret = 0;

    scan_status = scan_status_get_status();

    if (!scan_ticket)
    {
        scan_ticket = (WSD_SCAN_TICKET_S *)srv_data->pticket;
    }

    if (STATUS_I_SCAN_IDLE == scan_status || STATUS_I_SCAN_SLEEP == scan_status)
    {
        rs = "None";
        ss = "Idle";
    }
    else if (STATUS_I_SCAN_RUNNING == scan_status || STATUS_I_SCAN_PROCESSING == scan_status)
    {
        rs = "None";
        ss = "Processing";
    }
    else if ( scan_ticket && scan_ticket->param &&
        (SCAN_MODE_ADF == scan_ticket->param->scan_mode || SCAN_MODE_DADF == scan_ticket->param->scan_mode) )
    {
        switch (scan_status)
        {
            case STATUS_E_SCAN_ADF_COVER_OPEN:
                NET_WARN("WSD Scanner SCAN_STATUS_ADF_COVER_OPEN !!!\n");
 //               state = WSD_JOB_WILLEND;
                rs = "CoverOpen";
                ss = "Stopped";
                ret = -1;
                PushStatus(STATUS_E_SCAN_ADF_COVER_OPEN);
                break;
            case STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ:
            case STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN:
            case STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT:
            case STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ:
            case STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN:
            case STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT:
            case STATUS_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT:
            case STATUS_E_SCAN_PAPER_JAM_REMAIN_ADJ:
            case STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN:
            case STATUS_E_SCAN_PAPER_JAM_REMAIN_EXIT:
                NET_WARN("WSD Scanner SCAN_STATUS_ADF_PAPER_JAM !!!\n");
                //state = NETJOB_STATE_CANCELED;
                rs = "MediaJam";
                ss = "Stopped";
                break;
            case STATUS_E_SCAN_ADF_PAPER_OUT:
                NET_WARN("WSD Scanner SCAN_STATUS_ADF_PAPER_OUT !!!\n");
   //             state = WSD_JOB_WILLEND;
                rs = "InputTrayEmpty";
                ss = "Stopped";
                PushStatus(STATUS_E_SCAN_ADF_PAPER_OUT);
                break;
            default:
                if (ADF_NO_PAPER == scan_status_get_adf_paper_status() && scan_ticket != NULL)
                {
                    NET_WARN("WSD Scanner ADF_NO_PAPER !!!\n");
   //                 state = WSD_JOB_WILLEND;
                    rs = "InputTrayEmpty";
                    ss = "Stopped";
                    PushStatus(STATUS_E_SCAN_ADF_PAPER_OUT);
                    ret = -1;
                }
                else
                {
                    NET_WARN("WSD Scanner status is %x, please fix it", scan_status);
                    rs = "None";
                    ss = "Stopped";
                }
                /*
                   if(Scan_Status_Get_Fb_Cover_status() == true)
                   {
                   NET_WARN("WSD Scanner STATUS_E_SCAN_FB_COVER_OPEN !!!\n");
                   state = WSD_JOB_WILLEND;
                   rs = "AttentionRequired";
                   ss = "Stopped";
                   PushStatus(STATUS_E_SCAN_FB_COVER_OPEN);
                   ret = -1;
                   }
                   */
                break;
        }
    }
    else
    {
//        state = NETJOB_STATE_ABORTED;
        NET_WARN("WSD Scanner status is %x, please fix it", scan_status);
        rs = "Paused";
        ss = "Stopped";
    }

/*
    if (scan_ticket)
    {
        ((WSD_TICKET_S*)scan_ticket)->state = state;
    }
    */

    if (status_string)
    {
        *status_string = ss;
    }
    if (reason_string)
    {
        *reason_string = rs;
    }
    NET_DEBUG("ss: %s, rs: %s\n", ss, rs);
    NET_WARN("WSD Scanner status: %s, rs: %s\n", ss, rs);

    return ret;
}

static int WSDcheckScanRecord(WSD_SCAN_PARM_S* param)
{
    int ret = 0;
    time_t now;

    time(&now);
    if ( param && FILE_BMP == param->scan_file
       && (SCAN_MODE_ADF == param->scan_mode || SCAN_MODE_DADF == param->scan_mode)
       && s_wscn_record.record == 0 && s_wscn_record.time > now  )
    {
        ret = 1;
    }
    s_wscn_record.time = 0;
    s_wscn_record.record = 0xff;

    return ret;
}

// <0 for failed, 0 for success
int32_t wsd_scan_job_start(WSD_TICKET_S* pticket)
{
    int             msg;
    int32_t         ret;
    JOB_REQUEST_S*  pjobreq;
    ROUTER_MSG_S    message;
    SCAN_JOB_REQUEST_DATA_S* reqdata;
    WSD_SCAN_TICKET_S* scan_ticket = (WSD_SCAN_TICKET_S*)pticket;

    RETURN_VAL_IF(pticket == NULL, NET_WARN, -1);

    if (scan_ticket->evtfd < 0)
    {
        RETURN_VAL_IF((scan_ticket->evtfd = eventfd(0, EFD_NONBLOCK)) < 0, NET_WARN, -1);
    }
    if (scan_ticket->list_lock == INVALIDMTX)
    {
        RETURN_VAL_IF((scan_ticket->list_lock = pi_mutex_create()) == INVALIDMTX, NET_WARN, -1);
    }

    pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
    RETURN_VAL_IF(pjobreq == NULL, NET_WARN, -1);

    reqdata = scan_parm_to_request_data( scan_ticket->param );
    if ( reqdata == NULL )
    {
        NET_WARN("job %d convert scan request data failed", pticket->job_id);
        pi_free(pjobreq);
        return -1;
    }

    NET_DEBUG("send MSG_CTRL_JOB_REQUEST to MID_SYS_JOB_MGR");
    pjobreq->io_class         = IO_CLASS_SCAN;
    pjobreq->io_via           = IO_VIA_NET;
    pjobreq->job_config_param = reqdata;
    pjobreq->notify = job_start_notify;
    pjobreq->context = pticket;
    pjobreq->pqio             = NULL;
    message.msgType   = MSG_CTRL_JOB_REQUEST;
    message.msg1      = pticket->job_id;
    message.msg2      = SCAN_OBJ;
    message.msg3      = pjobreq;
    message.msgSender = MID_PORT_NET;
    ret = task_msg_send_by_router(MID_SYS_JOB_MGR, &message);
    if (ret == 0)
    {
        ret = wsd_message_wait(scan_ticket, 60);  //60 seconds
        RETURN_VAL_IF(ret < 0, NET_WARN, ret);
    }
    return 0;
}

static WSD_SCAN_TICKET_S* ScanTicketCreate()
{
    WSD_TICKET_S* pticket;
    WSD_SCAN_TICKET_S* scan_ticket;

    scan_ticket = (WSD_SCAN_TICKET_S *)pi_zalloc(sizeof(WSD_SCAN_TICKET_S));
    if (scan_ticket)
    {
        pticket = (WSD_TICKET_S*)scan_ticket;
        pticket->state = NETJOB_STATE_NEW;
        pticket->svc_type = &s_scan_service;
        pticket->job_id  = (int32_t)job_manager_apply_jobid(IO_VIA_NET , 0);
        if(pticket->job_id <= 0)
        {
            NET_WARN("job_id <= 0");
            pi_free(scan_ticket);
            return NULL;
        }

        NET_DEBUG("create ticket:%p, ticket->id:%d", pticket, pticket->job_id);
        pthread_spin_init(&scan_ticket->images_lock, PTHREAD_PROCESS_PRIVATE);
        pi_init_list_head(&scan_ticket->page_head);
        scan_ticket->evtfd = -1;
        scan_ticket->list_lock = INVALIDMTX;
        //wsd_ticket_ref(pticket);
    }
    return scan_ticket;
}

static void WSDstartScanRecord(WSD_SCAN_PARM_S* param)
{
    time_t now;

    time(&now);
    if (  param && FILE_BMP == param->scan_file
       && (SCAN_MODE_ADF == param->scan_mode || SCAN_MODE_RADF == param->scan_mode)  )
    {
        s_wscn_record.time = now + 10; //10s
        s_wscn_record.record = 0;
        NET_WARN("Start scan record time %08lx!!!", s_wscn_record.time);
    }
}

static void ScanTicketDestroy(WSD_TICKET_S* pticket)
{
    WSD_SCAN_TICKET_S* scan_ticket = (WSD_SCAN_TICKET_S*)pticket;
    if (scan_ticket)
    {
        if (scan_ticket->param)
        {
            pi_free(scan_ticket->param);
        }
        if ( scan_ticket->list_lock != INVALIDMTX )
        {
            pi_mutex_destroy(scan_ticket->list_lock);
        }
        if (scan_ticket->evtfd >= 0)
        {
            close(scan_ticket->evtfd);
        }
        pthread_spin_destroy(&scan_ticket->images_lock);
        scan_ticket_clear_page_list(scan_ticket);
        pi_free(scan_ticket);
    }
}

static WSD_SCAN_TRANS_S scan_format[] = {
    {"jfif",                        FILE_JPEG,  "image/jpeg"},
    {"dib",                         FILE_BMP,   "image/bmp"},
    {"tiff-single-g4",              FILE_TIFF,  "image/tiff"},
    {"tiff-single-uncompressed",    FILE_TIFF,  "image/tiff"},
    {"pdf-a",                       FILE_PDF,   "application/pdf"},
    {"exif",                        FILE_JPEG,  "image/jpeg"},//TODO: SCAN_FILE_JPEG ?
    {"png",                         FILE_JPEG,  "image/jpeg"},//TODO: SCAN_FILE_JPEG ?
    {"jpeg2k",                      FILE_JPEG,  "image/jpeg"},
    {"xps",                         FILE_XPS,   "image/xps" },
};

static void scanner_set_default_param(WSD_SCAN_PARM_S* param)
{
    RETURN_IF(param == NULL, NET_WARN);

    param->file = scan_format[0].var;
    param->scan_file = scan_format[0].mode;
    param->img_fmt = scan_format[0].othervar;
    param->scan_mode = SCAN_MODE_FB;
    param->mode = scan_inputsrc[0].var;
    param->margin_left = 0;
    param->margin_top = 0;
    param->area_width = WSD_SCAN_AREA_W;
    param->area_height = WSD_SCAN_AREA_H;
    param->scan_color = COLOR_RGB;
    param->color = scan_color[0].var;
    param->xres = DPI_300;
    param->yres = DPI_300;
    param->imgtrans = 0;
    param->media_width = WSD_SCAN_AREA_W;
    param->media_height = WSD_SCAN_AREA_H;
    param->contrast = 0;
    param->brightness = 0;
    param->scal_width = 100;
    param->scal_height = 100;
    param->rotation = 0;
    param->docsize_autodetect = 0;
    return;
}
static void scanner_get_doc_parms(const char *xmlarea, QXML_S* pxml, WSD_SCAN_PARM_S* param)
{
    char    var[256];
    char    val[256];
    QXML_S  xml, xml_cursor;
    int     rc = 0;
    char    int_str[4][16] = {0};
    SOAP_CHILD_ELEM_S child_elems[4];

    if (NULL == param)
    {
        NET_WARN("The param is NULL!!!\n");
        return ;
    }

    rc = soap_find_var_in_xml(pxml, xmlarea);
    if (rc)
    {
        NET_WARN("No find %s !!!", xmlarea);
        return ;
    }

    QXMLparserSyncTo(pxml, &xml);   // pxml point to xmlarea
    rc = soap_get_var_value_from_xml(&xml, "Format", val, sizeof(val));
    if (0 == rc)
    {
        NET_DEBUG("scan format to be matched: %s", val);
        FOREACH_ARRAY(scan_format)
        {
            if (strcmp(val, scan_format[_i].var) == 0)
            {
                param->scan_file = scan_format[_i].mode;
                param->img_fmt = scan_format[_i].othervar;
                param->file = scan_format[_i].var;
                NET_DEBUG("match scan file：%s", val);
                break;
            }
        }
    }

    QXMLparserSyncTo(pxml, &xml);
    rc = soap_get_var_value_from_xml(&xml, "InputSource", val, sizeof(val));
    if (0 == rc)
    {
        NET_DEBUG("scan mode to be matched: %s", val);
        FOREACH_ARRAY(scan_inputsrc)
        {
            if (strcmp(val, scan_inputsrc[_i].var) == 0)
            {
                param->scan_mode = scan_inputsrc[_i].mode;
                param->mode = scan_inputsrc[_i].var;
                NET_DEBUG("match scan mode：%s", val);
                break;
            }
        }
    }

    QXMLparserSyncTo(pxml, &xml);
    rc = soap_get_var_value_from_xml(&xml, "ImagesToTransfer",  val, sizeof(val));
    param->imgtrans = (0 == rc) ? atoi(val) : 0;

    QXMLparserSyncTo(pxml, &xml);
    rc = soap_get_var_value_from_xml(&xml, "Rotation", val, sizeof(val));
    param->rotation = (0 == rc) ? atoi(val) : 0;

    //MediaSides.MediaFront
    QXMLparserSyncTo(pxml, &xml);
    rc = soap_find_var_in_xml(&xml, "MediaSides.MediaFront");
    if (rc)
    {
        NET_WARN("No find %s.%s !!!", xmlarea, "MediaSides.MediaFront");
        return ;
    }

    QXMLparserSyncTo(&xml, &xml_cursor);

    SOAP_CHILD_ELEM_RESET( child_elems[0], "ScanRegionXOffset", &param->margin_left, sizeof(param->margin_left), dtint );
    SOAP_CHILD_ELEM_RESET( child_elems[1], "ScanRegionYOffset", &param->margin_top, sizeof(param->margin_top), dtint );
    SOAP_CHILD_ELEM_RESET( child_elems[2], "ScanRegionWidth", &param->area_width, sizeof(param->area_width), dtint );
    SOAP_CHILD_ELEM_RESET( child_elems[3], "ScanRegionHeight", &param->area_height, sizeof(param->area_height), dtint );
    soap_find_child_elements(&xml, "ScanRegion", child_elems, 4);

    QXMLparserSyncTo(&xml_cursor, &xml);
    rc = soap_get_var_value_from_xml(&xml, "ColorProcessing", val, sizeof(val));
    if (0 == rc)
    {
        NET_DEBUG("scan color to be matched: %s", val);
        FOREACH_ARRAY(scan_color)
        {
            if (strcmp(val, scan_color[_i].var) == 0)
            {
                param->scan_color = scan_color[_i].mode;
                param->color = scan_color[_i].var;
                NET_DEBUG("match scan color：%s", val);
                break;
            }
        }
    }

    QXMLparserSyncTo(&xml_cursor, &xml);
    SOAP_CHILD_ELEM_RESET( child_elems[0], "Width", &param->xres, sizeof(param->xres), dtint );
    SOAP_CHILD_ELEM_RESET( child_elems[1], "Height", &param->yres, sizeof(param->yres), dtint );
    soap_find_child_elements(&xml, "Resolution", child_elems, 2);

    QXMLparserSyncTo(pxml, &xml);
    rc = soap_get_var_value_from_xml(&xml, "InputSize.DocumentSizeAutoDetect", val, sizeof(val));
    if (0 == rc)
    {
        param->docsize_autodetect = 1;
    }
    else
    {
        //InputSize.InputMediaSize
        QXMLparserSyncTo(pxml, &xml);
        SOAP_CHILD_ELEM_RESET( child_elems[0], "Width", &param->media_width, sizeof(param->media_width), dtint );
        SOAP_CHILD_ELEM_RESET( child_elems[1], "Height", &param->media_height, sizeof(param->media_height), dtint );
        soap_find_child_elements(&xml, "InputSize.InputMediaSize", child_elems, 2);
    }

    //Exposure.ExposureSettings
    QXMLparserSyncTo(pxml, &xml);
    SOAP_CHILD_ELEM_RESET( child_elems[0], "Contrast", &param->contrast, sizeof(param->contrast), dtint );
    SOAP_CHILD_ELEM_RESET( child_elems[1], "Brightness", &param->brightness, sizeof(param->brightness), dtint );
    soap_find_child_elements(&xml, "Exposure.ExposureSettings", child_elems, 2);

    //Scaling
    QXMLparserSyncTo(pxml, &xml);
    SOAP_CHILD_ELEM_RESET( child_elems[0], "ScalingWidth", &param->scal_width, sizeof(param->scal_width), dtint );
    SOAP_CHILD_ELEM_RESET( child_elems[1], "ScalingHeight", &param->scal_height, sizeof(param->scal_height), dtint );
    soap_find_child_elements(&xml, "Scaling", child_elems, 2);
}

static int32_t WSDscanImageInfoVar(WSD_SCAN_PARM_S* param, SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S *pv, *pvv;
    int rc;
    uint32_t ppl, nol, bpl;
    char buf[64];

    *out_ppvar = NULL;
    rc = 1;
    ppl = nol = bpl = 0;
    buf[0] = '\0';
    do   // TRY
    {
        pv = soap_create_var(
                "wscn:ImageInformation",
                dtchar,
                0,
                NULL,
                0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        pv->m_child = soap_create_var("wscn:MediaFrontImageInfo", dtchar, 0, "", 0);
        if (! pv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pv->m_child;
        ppl = ((param->area_width*DPI_300)/1000 + WSD_ALIGN_W_MASK) & (~WSD_ALIGN_W_MASK);
        snprintf(buf, 64, "%u", ppl);
        pvv->m_child = soap_create_var("wscn:PixelsPerLine", dtint, 0, buf, 0);//128 -> buf
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_child;
        nol = ((param->area_height*DPI_300)/1000 + WSD_ALIGN_H_MASK) & (~WSD_ALIGN_H_MASK);
        snprintf(buf, 64, "%u", nol);
        pvv->m_next = soap_create_var("wscn:NumberOfLines", dtint, 0, buf, 0);//128 -> buf
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        bpl = (COLOR_MONO == param->scan_color) ? ppl/8 :
              ((COLOR_GRAY == param->scan_color) ? ppl : ppl*3);
        snprintf(buf, 64, "%u", bpl);
        //pvv->m_next = soap_create_var("wscn:BytesPerLine", dtint, 0, buf, 0);//384 -> buf
        pvv->m_next = soap_create_var("wscn:BytesPerLine", dtint, 0, "0", 0);//384 -> buf
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int WSDscanDocumentInfoVar(WSD_SCAN_PARM_S* pparam, SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S       *pv, *pvv, *px, *pxx;
    int              rc = -1;
    WSD_SCAN_PARM_S  param;
    char             tostr[64];

    *out_ppvar = NULL;
    if (NULL == pparam)
    {
        scanner_set_default_param(&param);
    }
    else
    {
        memcpy(&param, pparam, sizeof(WSD_SCAN_PARM_S));
    }

    do   // TRY
    {
        pv = soap_create_var("wscn:Format", dtchar, 0, param.file, 0);
        BREAK_IF(! pv, NET_WARN);
        pv->m_next = soap_create_var("wscn:CompressionQualityFactor", dtint, 0, "0",
                         1, "UsedDefault", "true");
        BREAK_IF(! pv->m_next, NET_WARN)
        pvv = pv->m_next;
        pvv->m_next = soap_create_var("wscn:ImagesToTransfer", dtint, 0,
                                    (SCAN_MODE_FB == param.scan_mode) ? "1" : "0", 0);
        BREAK_IF (! pvv->m_next, NET_WARN)

        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:InputSource", dtchar, 0, param.mode, 0);
        BREAK_IF (! pvv->m_next, NET_WARN)

        #if 0//add by dengxingsheng
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:FilmScanMode", dtchar, 0, "NotApplicable", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        #endif//end add by dengxingsheng
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:ContentType", dtchar, 0, "Photo",
                         1, "UsedDefault", "true");
        BREAK_IF (! pvv->m_next, NET_WARN)
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:InputSize", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_next, NET_WARN)
        pvv = pvv->m_next;
        /*
        if (param.docsize_autodetect)
        {
            pvv->m_child = soap_create_var("wscn:DocumentSizeAutoDetect", dtchar, 0, "true", 0);
            BREAK_IF (! pvv->m_child, NET_WARN);
        }
        else {
            */
        pvv->m_child = soap_create_var("wscn:InputMediaSize", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_child, NET_WARN);
        snprintf(tostr, 64, "%u", param.media_width);
        px = soap_create_var("wscn:Width", dtint, 0, tostr, 0);
        BREAK_IF (! px, NET_WARN);
        snprintf(tostr, 64, "%u", param.media_height);
        px->m_next = soap_create_var("wscn:Height", dtint, 0, tostr, 0);
        BREAK_IF (! px->m_next, NET_WARN);
        pvv->m_child->m_child = px;
        //}
        pvv->m_next = soap_create_var("wscn:Exposure", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_next, NET_WARN)
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:ExposureSettings", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_child, NET_WARN)

        snprintf(tostr, 64, "%d", param.contrast);
        px = soap_create_var("wscn:Contrast", dtint, 0, tostr, 0);
        BREAK_IF (! px, NET_WARN)

        pvv->m_child->m_child = px;
        snprintf(tostr, 64, "%d", param.brightness);
        px->m_next = soap_create_var("wscn:Brightness", dtint, 0, tostr, 1);
        BREAK_IF (! px->m_next, NET_WARN)

        px = px->m_next;
        px->m_next = soap_create_var("wscn:Sharpness", dtint, 0, "0", 0);
        BREAK_IF (! px->m_next, NET_WARN)

        pvv->m_next = soap_create_var("wscn:Scaling", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_next, NET_WARN)
        pvv = pvv->m_next;
        snprintf(tostr, 64, "%u", param.scal_width);
        px = soap_create_var("wscn:ScalingWidth", dtint, 0, tostr, 0);
        BREAK_IF (! px, NET_WARN)
        pvv->m_child = px;
        snprintf(tostr, 64, "%u", param.scal_height);
        px->m_next = soap_create_var("wscn:ScalingHeight", dtint, 0, tostr, 0);
        BREAK_IF (! px->m_next, NET_WARN)
        snprintf(tostr, 64, "%u", param.rotation);
        pvv->m_next = soap_create_var("wscn:Rotation", dtint, 0, tostr, 0);
        BREAK_IF (! pvv->m_next, NET_WARN)
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:MediaSides", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_next, NET_WARN)

        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:MediaFront", dtchar, 0, "", 0);
        BREAK_IF (! pvv->m_child, NET_WARN)
        px = soap_create_var("wscn:ScanRegion", dtchar, 0, "", 0);
        BREAK_IF (! px, NET_WARN)
        pvv->m_child->m_child = px;
        snprintf(tostr, 64, "%u", param.margin_left);
        pxx = soap_create_var("wscn:ScanRegionXOffset", dtint, 0, tostr, 0);
        BREAK_IF (! pxx, NET_WARN)
        px->m_child = pxx;
        snprintf(tostr, 64, "%u", param.margin_top);
        pxx->m_next = soap_create_var("wscn:ScanRegionYOffset", dtint, 0, tostr, 0);
        BREAK_IF (! pxx->m_next, NET_WARN)
        pxx = pxx->m_next;
        snprintf(tostr, 64, "%u", param.area_width);
        pxx->m_next = soap_create_var("wscn:ScanRegionWidth", dtint, 0, tostr, 1, "Override", "true");
        BREAK_IF (! pxx->m_next, NET_WARN)
        pxx = pxx->m_next;
        snprintf(tostr, 64, "%u", param.area_height);
        pxx->m_next = soap_create_var("wscn:ScanRegionHeight", dtint, 0, tostr, 1, "Override", "true");
        BREAK_IF (! pxx->m_next, NET_WARN)
        px->m_next = soap_create_var("wscn:ColorProcessing", dtchar, 0, param.color, 2, "Override", "true", "UsedDefault", "true");
        BREAK_IF (! px->m_next, NET_WARN)
        px = px->m_next;
        px->m_next = soap_create_var("wscn:Resolution", dtchar, 0, "", 0);
        BREAK_IF (! px->m_next, NET_WARN)

        px = px->m_next;
        snprintf(tostr, 64, "%u", param.xres);
        pxx = soap_create_var("wscn:Width", dtint, 0, tostr, 0);
        BREAK_IF (! pxx, NET_WARN)
        px->m_child = pxx;
        snprintf(tostr, 64, "%u", param.yres);
        pxx->m_next = soap_create_var("wscn:Height", dtint, 0, tostr, 0);
        BREAK_IF (! pxx->m_next, NET_WARN)

        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

int WSDscannerDefaultTicketInfoVar(SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S* pv, *pvv, *pvt;
    int rc;

    *out_ppvar = NULL;
    rc = 1;
    do   // TRY
    {
        pv = soap_create_var(
                "wscn:DefaultScanTicket",
                dtchar,
                0,
                NULL,
                0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        pv->m_child = soap_create_var("wscn:JobDescription", dtchar, 0, "", 0);
        if (! pv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pv->m_child;
        pvv->m_child = soap_create_var("wscn:JobName", dtchar, 0, "Scan Job"/*"default"*/, 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_child;
        pvv->m_next = soap_create_var("wscn:JobOriginatingUserName", dtchar, 0, "Unknown"/*"default"*/, 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:JobInformation", dtchar, 0, "No job information"/*"none"*/, 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        //add by dengxingsheng
        pvv = pv->m_child;
        pvv->m_next = soap_create_var("wscn:DocumentParameters", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        rc = WSDscanDocumentInfoVar(NULL, &pvt);
        if (rc || !pvt)
        {
            rc = -1;
            break;
        }
        pvv->m_child = pvt;
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int32_t wsd_scan_get_default_ticket_element(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *pVar, *pVV;
    int rc = 1;

    *out_ppvar = NULL;

    do  // try
    {
        pVar = soap_create_var(
                "wscn:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wscn:DefaultScanTicket",
                "Valid", "true"
                );
        if (! pVar)
        {
            rc = -1;
            break;
        }
        rc = WSDscannerDefaultTicketInfoVar(&pVV);
        if (rc)
        {
            break;
        }
        if ( ! pVV)
        {
            rc = -1;
            break;
        }
        pVar->m_child = pVV;
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static void scan_ticket_clear_page_list(WSD_SCAN_TICKET_S* scan_ticket)
{
    struct page_info*   page;
    struct list_head*   pos;
    struct list_head*   n;

    MUTEX_LOCK(scan_ticket->list_lock, {
            pi_list_for_each_safe(pos, n, &scan_ticket->page_head)
            {
                page = pi_list_entry(pos, struct page_info, page_list);
                pi_list_del_entry(&page->page_list);
                pi_free(page);
            }
    })
}

static ACTION_HANDLER_DEFINITION(wsd_scan_retrieve_image_handler)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    char  mimeBoundary[128];      ///< mime/multipart boundary if multipart xfer
    int   mimeBoundaryLength;     ///< string length of boundary, 0 if no boundary in effect

    WSD_TICKET_S* pticket = NULL;
    WSD_SCAN_TICKET_S* scan_ticket = NULL;
    SOAP_VAR_S* pResponse = NULL;
    int32_t  job_id;
    int32_t  rc, count, len;
    char* pchar = NULL;
    char buf[WSD_MAX_ENVELOPE_SIZE];
    char bufbase64[128];
    char boundary_len[128];
    time_t now;
    const char *fault_code   = WSD_SOAP_RECEIVER;
    const char *fault_sucode = "OperationFailed";
    const char *fault_reason = "Busy";
    int waiting;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    NET_DEBUG("Handling RetrieveImageRequest");

    buf[0] = '\0';
    bufbase64[0] = '\0';
    do // TRY
    {
        // find job ID
        rc = soap_get_var_value_from_xml( pxml, "JobId", buf, 64);
        if (rc || wsd_strtol(buf, &job_id))
        {
            NET_WARN("invalid job ID; %s", buf);
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "InvalidArgs";
            fault_reason = "Jod ID format is not correct";
            break;
        }

        pticket = wsd_ticket_get_from_job_id(&s_scan_service, job_id);
        srv_data->pticket = pticket;
        scan_ticket = (WSD_SCAN_TICKET_S*)pticket;

        if (pticket == NULL)
        {
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "ClientErrorJobIdNotFound";
            fault_reason = "InvalidArgs";
            break;
        }

#define  CONTINUE_IF(_block, ...) \
        if (_block) {   \
            NET_WARN(#_block " : " __VA_ARGS__);    \
            pticket->state = NETJOB_STATE_ABORTED;   \
            continue;    \
        }


        //        wsd_ticket_ref(pticket);
        while (1)
        {
            switch( pticket->state )
            {
                case NETJOB_STATE_DONE:
                    NET_INFO("[WSD] pticket->state = %d\n", pticket->state);
                    fault_code = WSD_SOAP_SENDER;
                    fault_sucode = "ClientErrorNoImagesAvailable";
                    fault_reason = "The server has no images available to acquire";
                    rc = WSD_ERR_NoImagesAvailable;
                    *reply_code = (char*)http_status_string(500);
                    break;

                case NETJOB_STATE_NEW:
                    rc = wsd_scan_job_start(pticket);
                    if (rc)
                    {
                        NET_INFO("[WSD] pticket->state = %d\n", pticket->state);
                        fault_code = WSD_SOAP_RECEIVER;
                        fault_sucode = "ServerErrorTemporaryError";
                        fault_reason = "The service had an unexpected error";
                        break;
                    }
                    continue;

                case NETJOB_STATE_CANCELED:
                    fault_code = WSD_SOAP_SENDER;
                    fault_sucode = "ClientErrorJobCancelledd";
                    fault_reason = "The current Scan job has been cancelled";
                    //cancel_job(pticket);
                    rc = -1;
                    break;
                case NETJOB_STATE_ABORTED:
                    fault_code = WSD_SOAP_RECEIVER;
                    fault_sucode = "ServerErrorTemporaryError";
                    fault_reason = "The service had an unexpected error";
                    rc = -1;
                    break;

                case NETJOB_STATE_PROCESSING:
                    SPIN_LOCK(&scan_ticket->images_lock, {
                            waiting = (scan_ticket->images_tranfered == scan_ticket->images_scanned);
                    })
                    if (waiting)
                    {
                        pi_msleep(1000);
                        continue;
                    }

                    wsd_generate_uuid(bufbase64, 64);
                    len = snprintf(buf, sizeof(buf), "urn:uuid:%s", bufbase64);
                    base64_encode(bufbase64, len, buf, sizeof(buf));
                    snprintf(mimeBoundary, sizeof(mimeBoundary), "==%s==", buf);
                    NET_DEBUG("[WSD] boundary[%s]\n", mimeBoundary);
                    //
                    //
                    // format response
                    pResponse = soap_create_var("wscn:ScanData", dtchar, 0, "", 0);
                    CONTINUE_IF(!pResponse);
                    snprintf(buf, 64, "cid:id%d", job_id);
                    pResponse->m_child = soap_create_var("xop:Include",
                            dtchar, 0, "", 1, "href", buf);
                    CONTINUE_IF(! pResponse->m_child);

                    time(&now);
                    strcpy(bufbase64, ctime_r(&now, buf));
                    for (pchar = bufbase64; *pchar && *pchar != '\n'; pchar++);
                    *pchar = '\0';
                    count = snprintf(priv->iobuf, sizeof(priv->iobuf), WSD_SCAN_HEADER,
                            bufbase64, "<soap:Envelope>", mimeBoundary);
                    len = http_task_send(ptask, priv->iobuf, count);
                    if (len != count)
                    {
                        NET_WARN("Send scan WSD_SCAN_HEADER failed %d!!!\n", rc);
                        pticket->state = NETJOB_STATE_ABORTED;
                        continue;
                    }

                    len = snprintf(buf, sizeof(buf), WSD_SCAN_DATA, mimeBoundary,
                            "application/xop+xml; charset=utf-8; type=application/soap+xml",
                            "binary", "soap:Envelope");
                    count = snprintf(priv->iobuf, sizeof(priv->iobuf), "%x\r\n%s\r\n", len, buf);
                    len = http_task_send(ptask, priv->iobuf, count);
                    CONTINUE_IF(len != count, "Send scan <soap:Envelope> header failed !!!");

                    rc = soap_format_response(
                            pResponse,
                            WSD_NAME_SPACES,
                            "RetrieveImageResponse",
                            srv_data->soap_header,
                            WSD_SCAN_XMLNS,
                            WSD_SCAN_XMLNS_URL,
                            buf,
                            sizeof(buf)
                            );
                    len = strlen(buf);
                    count = snprintf(priv->iobuf, sizeof(priv->iobuf), "%x\r\n%s\r\n\r\n", len + 2, buf);
                    len = http_task_send(ptask, priv->iobuf, count);
                    CONTINUE_IF(len != count, "Send scan <soap:Envelope> failed !!!");

                    snprintf(bufbase64, 64, "id%d", job_id);
                    len = snprintf(buf, sizeof(buf), WSD_SCAN_DATA, mimeBoundary,
                            scan_ticket->param ? scan_ticket->param->img_fmt : "application/binary",
                            "binary", bufbase64);
                    /*
                       len = snprintf(buf, sizeof(buf), WSD_SCAN_DATA, mimeBoundary,
                       "application/binary", "binary", bufbase64);
                       */

                    count = snprintf(priv->iobuf, sizeof(priv->iobuf), "%x\r\n%s\r\n", len, buf);
                    len = http_task_send(ptask, priv->iobuf, count);
                    CONTINUE_IF(len != count, "Send scan data header failed !!!");

                    {   // wait for page to scanned and transfer back to client
                        FILE*  stream;
                        struct page_info* page;

                        size_t  nread = 0;
                        int32_t nsend = 0;

                        MUTEX_LOCK(scan_ticket->list_lock, {
                                page = pi_list_first_entry_or_null(&scan_ticket->page_head, struct page_info, page_list);
                                })

                        CONTINUE_IF(page == NULL, "bug in the program!!!");

                        stream = pi_fopen(page->file_path, "r");
                        CONTINUE_IF ( stream == NULL , "open scan file(%s) failed: %d<%s>", page->file_path, errno, strerror(errno));

                        NET_DEBUG(" %d images had been scanned, start to send page %d back", scan_ticket->images_scanned, scan_ticket->images_tranfered + 1);
                        do
                        {
                            nread = fread(srv_data->soap_reply, 1, WSD_IMAGE_READ_SIZE, stream);
                            if ( nread > 0 )
                            {
                                count = snprintf(priv->iobuf, sizeof(priv->iobuf), "%x", nread);
                                priv->iobuf[count] = '\r';
                                priv->iobuf[count+1] = '\n';
                                memcpy(priv->iobuf+count+2, srv_data->soap_reply, nread);
                                priv->iobuf[count+2+nread] = '\r';
                                priv->iobuf[count+2+nread+1] = '\n';
                                len = count + nread + 4;   // \r\n data \r\n
                                nsend = http_task_send(ptask, priv->iobuf, len);
                                BREAK_IF ( nsend != len, NET_WARN );
                            }
                        }
                        while ( nread == WSD_IMAGE_READ_SIZE );
                        pi_fclose(stream);

                        CONTINUE_IF(nsend != len, "sending image boundary data failed, need to send (%u), but only had sent (%d)", nread, nsend);

                        unlink(page->file_path);
                        MUTEX_LOCK(scan_ticket->list_lock, {
                                pi_list_del_entry(&page->page_list);
                                pi_free(page);
                        });

                        // send last boundary and end of chunk
                        len = snprintf(buf, sizeof(buf), "\r\n--%s--", mimeBoundary);
                        count = snprintf(srv_data->soap_reply, sizeof(srv_data->soap_reply),
                                "%x\r\n%s\r\n", len, buf);
                        nsend = http_task_send(ptask, srv_data->soap_reply, count);

                        CONTINUE_IF(nsend != count, "sending last boundary failed, need to send(%u), but only had sent (%d)", nread, nsend);

                        NET_DEBUG("[WSD] send page %d end chunk, count = %d\n", scan_ticket->images_tranfered+1, count);
                        nsend = http_task_send(ptask, END_OF_CHUNK, LEN(END_OF_CHUNK));

                        CONTINUE_IF(nsend != LEN(END_OF_CHUNK), "sending end of chunk failed, need to send(%u), but only had send (%d)", nread, nsend);
                        srv_data->http_finished = 1;
                        SPIN_LOCK(&scan_ticket->images_lock, {
                            if (++scan_ticket->images_tranfered == scan_ticket->images_scanned && scan_ticket->images_scanned_finish)
                            {
                                pticket->state = NETJOB_STATE_DONE;
                            }
                        })

                    }
                    break;
            }
            break;
        }
    }
    while(0); // CATCH
    //wsd_ticket_unref(svc_type, pticket);

    if (rc)
    {
        rc = WSDcreateFaultResponse(srv_data, fault_code, WSD_SCAN_XMLNS, fault_sucode, fault_reason);
        if (rc)
        {
            NET_WARN("Can't even format a response\n");
        }
    }
	if(pResponse)
    {
		soap_delete_var(pResponse);
    }

    return rc;
}

static ACTION_HANDLER_DEFINITION(wsd_scan_validate_scan_ticket_handler)
{
    SOAP_VAR_S *pResponse, *pVar;
    int   rc;
    uint32_t ppl, nol, bpl;
    char buf[64];
    WSD_SCAN_PARM_S param;

    NET_DEBUG("Handling ValidateScanTicketRequest\n");

    // parse forward to requested elements
    //
    rc = soap_find_var_in_xml(pxml, "ValidateScanTicketRequest.ScanTicket");
    if (rc)
    {
        NET_WARN("No ticket to validate\n");
        return rc;
    }
    rc = 0;

    pResponse = NULL;
    pVar = NULL;
    ppl = nol = bpl = 0;
    buf[0] = '\0';
    memset(&param, 0, sizeof(param));
    do  // TRY
    {
        scanner_get_doc_parms("DocumentParameters", pxml, &param);
        pResponse = soap_create_var("wscn:ValidationInfo", dtchar, 0, "", 0);
        if (! pResponse)
        {
            rc = -1;
            break;
        }
        pResponse->m_child = soap_create_var("wscn:ValidTicket", dtbool, 0, "true", 0);
        if (! pResponse->m_child)
        {
            rc = -1;
            break;
        }
        pVar = pResponse->m_child;
        pVar->m_next = soap_create_var("wscn:ImageInformation", dtchar, 0, "", 0);
        if (! pVar->m_next)
        {
            rc = -1;
            break;
        }
        pVar = pVar->m_next;
        pVar->m_child = soap_create_var("wscn:MediaFrontImageInfo", dtchar, 0, "", 0);
        if (! pVar->m_child)
        {
            rc = -1;
            break;
        }
        pVar = pVar->m_child;
        ppl = ((param.area_width*DPI_300)/1000 + WSD_ALIGN_W_MASK) & (~WSD_ALIGN_W_MASK);
        snprintf(buf, 64, "%d", ppl);
        pVar->m_child = soap_create_var("wscn:PixelsPerLine", dtint, 0, buf, 0);//128 -> buf
        if (! pVar->m_child)
        {
            rc = -1;
            break;
        }
        pVar = pVar->m_child;
        nol = ((param.area_height*DPI_300)/1000 + WSD_ALIGN_H_MASK) & (~WSD_ALIGN_W_MASK);
        snprintf(buf, 64, "%d", nol);
        pVar->m_next = soap_create_var("wscn:NumberOfLines", dtint, 0, buf, 0);//128 -> buf
        if (! pVar->m_next)
        {
            rc = -1;
            break;
        }
        pVar = pVar->m_next;
        bpl = (COLOR_MONO == param.scan_color) ? ppl/8 :
              ((COLOR_GRAY == param.scan_color) ? ppl : ppl*3);
        snprintf(buf, 64, "%d", bpl);
        //pVar->m_next = soap_create_var("wscn:BytesPerLine", dtint, 0, buf, 0);//384 -> buf
        pVar->m_next = soap_create_var("wscn:BytesPerLine", dtint, 0, "0", 0);//384 -> buf
        if (! pVar->m_next)
        {
            rc = -1;
            break;
        }

        /*
        pVar = pResponse->m_child->m_next;
        if (WSDscannerValidScanTicket(pVar, pxmlSoap, &param))
        {
            rc = -1;
            break;
        }
        */

        rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "ValidateScanTicketResponse",
                        srv_data->soap_header,
                        WSD_SCAN_XMLNS,
                        WSD_SCAN_XMLNS_URL,
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
        if (rc)
        {
            NET_WARN("Can't format SOAP\n");
        }
    }
    while (0); // CATCH

    if(pResponse)
    {
        soap_delete_var(pResponse);
    }
    return rc;
}

static int WSDscannerConfigurationInfoVar(SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *pv, *pvv, *pvt;
    int32_t rc = 1;
    char buf[64] = {0};
   // SCAN_TYPE_E  scan_type = wsd_extra_get_scan_type();
    SCAN_TYPE_E  scan_type = SCAN_TYPE_FB;
    NET_INFO("THE SCAN TYPE SUPPORT scan_type==%d !!!", scan_type);
    do   // TRY
    {
        pv = soap_create_var("wscn:ScannerConfiguration", dtchar, 0, NULL, 0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        // chain rest of scanner configuration elements as siblings
        //
        pv->m_child = soap_create_var("wscn:DeviceSettings", dtchar, 0, "", 0);
        if (! pv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pv->m_child;
        pvv->m_child = soap_create_var("wscn:FormatsSupported", dtchar, 0, "", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_child;
        pvv->m_child = soap_create_var("wscn:FormatValue", dtchar, 0, "jfif", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvv->m_child;
        pvt->m_next = soap_create_var("wscn:FormatValue", dtchar, 0, "tiff-single-uncompressed", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_next;
        pvt->m_next = soap_create_var("wscn:FormatValue", dtchar, 0, "xps", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
//end add by dengxingsheng
        pvv->m_next = soap_create_var("wscn:CompressionQualityFactorSupported", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:MinValue", dtint, 0, "0", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next = soap_create_var("wscn:MaxValue", dtint, 0, "100", 0);
        if (! pvv->m_child->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_next = soap_create_var("wscn:ContentTypesSupported", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:ContentTypeValue", dtchar, 0, "Auto", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
//add by dengxingsheng
        pvt = pvv->m_child;
        pvt->m_next = soap_create_var("wscn:ContentTypeValue", dtchar, 0, "Text", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_next;
        pvt->m_next = soap_create_var("wscn:ContentTypeValue", dtchar, 0, "Photo", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_next;
        pvt->m_next = soap_create_var("wscn:ContentTypeValue", dtchar, 0, "Mixed", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_next = soap_create_var("wscn:DocumentSizeAutoDetectSupported", dtbool, 0, DOCUMENT_SIZE_AUTO_DETECT_SUPPORTED, 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:AutoExposureSupported", dtbool, 0, "false", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:BrightnessSupported", dtbool, 0, "true", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:ContrastSupported", dtbool, 0, "true", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:ScalingRangeSupported", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:ScalingWidth", dtchar, 0, "", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_child = soap_create_var("wscn:MinValue", dtint, 0, "25", 0);
        if (! pvv->m_child->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_child->m_next = soap_create_var("wscn:MaxValue", dtint, 0, "400", 0);
        if (! pvv->m_child->m_child->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next = soap_create_var("wscn:ScalingHeight", dtchar, 0, "", 0);
        if (! pvv->m_child->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next->m_child = soap_create_var("wscn:MinValue", dtint, 0, "25", 0);
        if (! pvv->m_child->m_next->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next->m_child->m_next = soap_create_var("wscn:MaxValue", dtint, 0, "400", 0);
        if (! pvv->m_child->m_next->m_child->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_next = soap_create_var("wscn:RotationsSupported", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:RotationValue", dtint, 0, "0", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next = soap_create_var("wscn:RotationValue", dtint, 0, "90", 0);
        if (! pvv->m_child->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next->m_next = soap_create_var("wscn:RotationValue", dtint, 0, "180", 0);
        if (! pvv->m_child->m_next->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next->m_next->m_next = soap_create_var("wscn:RotationValue", dtint, 0, "270", 0);
        if (! pvv->m_child->m_next->m_next->m_next)
        {
            rc = -1;
            break;
        }
//add by dengxingsheng
        //Platen
        pvv = pv->m_child;
        pvv->m_next = soap_create_var("wscn:Platen", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:PlatenOpticalResolution", dtchar, 0, "", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_child;
        pvv->m_child = soap_create_var("wscn:Width", dtint, 0, "300", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_child->m_next = soap_create_var("wscn:Height", dtint, 0, "300", 0);
        if (! pvv->m_child->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_next = soap_create_var("wscn:PlatenResolutions", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:Widths", dtchar, 0, "", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvv->m_child;
        pvt->m_child = soap_create_var("wscn:Width", dtint, 0, "200", 0);
        if (! pvt->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_child;
        pvt->m_next = soap_create_var("wscn:Width", dtint, 0, "300", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_next;
        pvt->m_next = soap_create_var("wscn:Width", dtint, 0, "600", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvv->m_child;
        pvt->m_next = soap_create_var("wscn:Heights", dtchar, 0, "", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_next;
        pvt->m_child = soap_create_var("wscn:Height", dtint, 0, "200", 0);
        if (! pvt->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_child;
        pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "300", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_next;
        pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "600", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvv->m_next = soap_create_var("wscn:PlatenColor", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wscn:ColorEntry", dtchar, 0, "Grayscale8", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvv->m_child;
        pvt->m_next = soap_create_var("wscn:ColorEntry", dtchar, 0, "RGB24", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        /*
        pvt = pvt->m_next;
        pvt->m_next = soap_create_var("wscn:ColorEntry", dtchar, 0, "BlackAndWhite1", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        */
        pvv->m_next = soap_create_var("wscn:PlatenMinimumSize", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvv->m_next;
        pvt->m_child = soap_create_var("wscn:Width", dtint, 0, "1", 0);
        if (! pvt->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_child;
        pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "1", 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wscn:PlatenMaximumSize", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvt = pvv->m_next;
        snprintf(buf, 64, "%d", WSD_SCAN_AREA_W);
        pvt->m_child = soap_create_var("wscn:Width", dtint, 0, buf, 0);
        if (! pvt->m_child)
        {
            rc = -1;
            break;
        }
        pvt = pvt->m_child;
        snprintf(buf, 64, "%d", WSD_SCAN_AREA_H);
        pvt->m_next = soap_create_var("wscn:Height", dtint, 0, buf, 0);
        if (! pvt->m_next)
        {
            rc = -1;
            break;
        }


        // for now support fb, adf, dadf
        {
            //ADF
            pvv = pv->m_child->m_next;
            pvv->m_next = soap_create_var("wscn:ADF", dtchar, 0, "", 0);
            if (! pvv->m_next)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_next;
            pvv->m_child = soap_create_var("wscn:ADFSupportsDuplex", dtbool, 0, "true", 0);
            if (! pvv->m_child)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_child;
            pvv->m_next = soap_create_var("wscn:ADFFront", dtchar, 0, "", 0);
            if (! pvv->m_next)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_next;
            pvv->m_child = soap_create_var("wscn:ADFOpticalResolution", dtchar, 0, "", 0);
            if (! pvv->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvv->m_child;
            pvt->m_child = soap_create_var("wscn:Width", dtint, 0, "300", 0);
            if (! pvt->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_child;
            pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "300", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_child;
            pvv->m_next = soap_create_var("wscn:ADFResolutions", dtchar, 0, "", 0);
            if (! pvv->m_next)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_next;
            pvv->m_child = soap_create_var("wscn:Widths", dtchar, 0, "", 0);
            if (! pvv->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvv->m_child;
            pvt->m_child = soap_create_var("wscn:Width", dtint, 0, "200", 0);
            if (! pvt->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_child;
            pvt->m_next = soap_create_var("wscn:Width", dtint, 0, "300", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_next;
            pvt->m_next = soap_create_var("wscn:Width", dtint, 0, "600", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvt = pvv->m_child;
            pvt->m_next = soap_create_var("wscn:Heights", dtchar, 0, "", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_next;
            pvt->m_child = soap_create_var("wscn:Height", dtint, 0, "200", 0);
            if (! pvt->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_child;
            pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "300", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_next;
            pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "600", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvv->m_next = soap_create_var("wscn:ADFColor", dtchar, 0, "", 0);
            if (! pvv->m_next)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_next;
            pvv->m_child = soap_create_var("wscn:ColorEntry", dtchar, 0, "Grayscale8", 0);
            if (! pvv->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvv->m_child;
	        pvt->m_next = soap_create_var("wscn:ColorEntry", dtchar, 0, "RGB24", 0);
	        if (! pvt->m_next)
	        {
	            rc = -1;
	            break;
	        }
            /*
	        pvt = pvt->m_next;
            pvt->m_next = soap_create_var("wscn:ColorEntry", dtchar, 0, "BlackAndWhite1", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            */
            pvv->m_next = soap_create_var("wscn:ADFMinimumSize", dtchar, 0, "", 0);
            if (! pvv->m_next)
            {
                rc = -1;
                break;
            }
            pvt = pvv->m_next;
            pvt->m_child = soap_create_var("wscn:Width", dtint, 0, "400", 0);
            if (! pvt->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_child;
            pvt->m_next = soap_create_var("wscn:Height", dtint, 0, "400", 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wscn:ADFMaximumSize", dtchar, 0, "", 0);
            if (! pvv->m_next)
            {
                rc = -1;
                break;
            }
            pvt = pvv->m_next;
            snprintf(buf, 64, "%d", WSD_SCAN_AREA_W);
            pvt->m_child = soap_create_var("wscn:Width", dtint, 0, buf, 0);
            if (! pvt->m_child)
            {
                rc = -1;
                break;
            }
            pvt = pvt->m_child;
            snprintf(buf, 64, "%d", WSD_SCAN_AREA_H);
            pvt->m_next = soap_create_var("wscn:Height", dtint, 0, buf, 0);
            if (! pvt->m_next)
            {
                rc = -1;
                break;
            }
        }
        rc = 0;
    }
    while(0);

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}


int WSDscannerStateInfoVar(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S *pv, *pvv;
    const char *stateString;
    const char *reasonString;
    int   rc;

    *out_ppvar = NULL;
    rc = 1;
    do   // TRY
    {
        wsd_scan_update_status(&stateString, &reasonString, srv_data, NULL);
        pv = soap_create_var("wscn:ScannerState", dtchar, 0, stateString, 0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        pv->m_next = soap_create_var("wscn:ScannerStateReasons", dtchar, 0, "", 0);
        if (! pv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pv->m_next;
        pvv->m_child = soap_create_var("wscn:ScannerStateReason", dtchar, 0, reasonString, 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv->m_next = soap_create_var("wscn:ActiveConditions", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_next;
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int32_t wsd_scan_get_status_element(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *pVar, *pVV, *pvs;
    char timestr[64];
    time_t now;
    struct tm *ct;
    int rc = 1;

    *out_ppvar = NULL;

    time(&now);
    ct = gmtime(&now);
    snprintf(timestr, sizeof(timestr), "%4d-%02d-%02dT%02d:%02d:%02dZ",
        ct->tm_year + 1900, ct->tm_mon, ct->tm_mday, ct->tm_hour, ct->tm_min, ct->tm_sec);

    do  // try
    {
        pVar = soap_create_var(
                "wscn:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wscn:ScannerStatus",
                "Valid", "true"
                );
        if (! pVar)
        {
            rc = -1;
            break;
        }
        pVar->m_child = soap_create_var(
                "wscn:ScannerStatus",
                dtchar,
                0,
                NULL,
                0);
        if (! pVar->m_child)
        {
            rc = -1;
            break;
        }
        pVV = pVar->m_child;
        pVV->m_child = soap_create_var("wscn:ScannerCurrentTime", dtchar, 0, timestr, 0);
        if (! pVV->m_child)
        {
            rc = -1;
            break;
        }
        pVV = pVV->m_child;

        // get Scanner status summary
        //
        rc = WSDscannerStateInfoVar(srv_data, &pvs);
        if (rc)
        {
            break;
        }
        if (! pvs)
        {
            rc = -1;
            break;
        }
        rc = 1;

        // and link it to the end of current sibling chain
        //
        pVV->m_next = pvs;
        while (pvs->m_next)
        {
            pvs = pvs->m_next;
        }
        pVV = pvs;

        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static int32_t wsd_scan_get_configuration_element(SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S* pVar, *pVV;
    int rc = 1;
    *out_ppvar = NULL;

    do  // try
    {
        pVar = soap_create_var(
                "wscn:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wscn:ScannerConfiguration",
                "Valid", "true"
                );
        if (! pVar)
        {
            rc = -1;
            break;
        }

        rc = WSDscannerConfigurationInfoVar(&pVV);
        if (rc)
        {
            break;
        }
        if (! pVV)
        {
            rc = -1;
            break;
        }
        pVar->m_child = pVV;
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static int32_t wsd_scan_get_description_element(SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *pVar, *pVV;
    int     rc = 1;
    char    namestr[256];
    char    hostname[HOSTNAME_LEN];
    char    pdt_name[PDT_NAME_LEN];

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsd_scn_ctx), hostname, sizeof(hostname));
    netdata_get_pdt_name(DATA_MGR_OF_WSD(s_wsd_scn_ctx), pdt_name, sizeof(pdt_name));

    *out_ppvar = NULL;
    do  // try
    {
        pVar = soap_create_var(
                "wscn:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wscn:ScannerDescription",
                "Valid", "true"
                );
        if (! pVar)
        {
            rc = -1;
            break;
        }
        pVar->m_child = soap_create_var(
                "wscn:ScannerDescription",
                dtchar,
                0,
                NULL,
                0);
        if (! pVar->m_child)
        {
            rc = -1;
            break;
        }
        pVV = pVar->m_child;

        // chain rest of Scanner description as siblings under description
        //
        snprintf(namestr, 256, "%s (%s Series)", hostname, pdt_name);
        pVV->m_child = soap_create_var("wscn:ScannerName", dtchar, 0, namestr/*"InfernoScan"*/, 0);
        if (! pVV->m_child)
        {
            rc = -1;
            break;
        }
        pVV = pVV->m_child;
        pVV->m_next = soap_create_var("wscn:ScannerInfo", dtchar, 0, "Development scanner wsd"/*"Testing"*/, 0);
        if (! pVV->m_next)
        {
            rc = -1;
            break;
        }
        pVV = pVV->m_next;
        pVV->m_next = soap_create_var("wscn:ScannerLocation", dtchar, 0, "test's Cubicale"/*"Brians Office"*/, 0);
        if (! pVV->m_next)
        {
            rc = -1;
            break;
        }
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static ACTION_HANDLER_DEFINITION(wsd_scan_get_scanner_elements_handler)
{
    QXML_S xml;
    SOAP_VAR_S* pResponse, *pVar;
    char *pe, *pv;
    char  req[128];
    int   rc, parseLevel;
    int   requests;
    NET_DEBUG("Handling GetScannerElementsRequest !");

    // parse forward to requested elements
    //
    rc = soap_find_var_in_xml(pxml, "GetScannerElementsRequest.RequestedElements");
    if (rc)
    {
        NET_WARN("No requested elements\n");
        return rc;
    }
    QXMLparserSyncTo(pxml, &xml);
    rc = 0;

    pResponse = NULL;
    pVar = NULL;

    do  // TRY
    {
        // parse the requested elements, and add replies for
        // each element in the request
        //
        pe = QXMLchildElement(pxml);
        parseLevel = pxml->m_level;
        requests = 0;

        while (pe && parseLevel <= pxml->m_level && rc == 0)
        {
            if (! QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Name"))
            {
                pv = QXMLnextValue(pxml);
                if (! pv)
                {
                    NET_WARN("No value for Name\n");
                    rc = -3;
                    break;
                }
                requests++;
                QXMLvalueCopy(pxml, req, pv, sizeof(req));

                pVar = NULL;

                NET_DEBUG("GetScannerElement: %s\n", QXMLelementName(pxml, req));

                if (! strcmp(QXMLelementName(pxml, req), "ScannerDescription"))
                {
                    rc = wsd_scan_get_description_element(&pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "ScannerConfiguration"))
                {
                    rc = wsd_scan_get_configuration_element(&pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "ScannerStatus"))
                {
                    rc = wsd_scan_get_status_element(srv_data, &pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "DefaultScanTicket"))
                {
                    rc = wsd_scan_get_default_ticket_element(srv_data, &pVar);
                }
                else
                {
                    NET_DEBUG("Unhandled ScannerElement %s\n", QXMLelementName(pxml, req));

                    pVar = soap_create_var(
                            "wscn:ElementData",
                            dtchar,
                            0,
                            NULL,
                            2,
                            "Name", req,
                            "Valid", "false"
                            );
                }
                if (pVar)
                {
                    if (pResponse && pResponse->m_child)
                    {
                        SOAP_VAR_S* pvx;

                        // enlist the new element on the reply list
                        // as sibling of last element
                        //
                        for (pvx = pResponse->m_child; pvx->m_next;)
                        {
                            pvx = pvx->m_next;
                        }
                        pvx->m_next = pVar;
                    }
                    else
                    {
                        if (pResponse)
                        {
                            // can't happen, but...
                            soap_delete_var(pResponse);
                        }
                        // create the element list and set element as first child
                        //
                        pResponse = soap_create_var(
                                "wscn:ScannerElements",
                                dtchar,
                                0,
                                "",
                                0
                                );
                        if (! pResponse)
                        {
                            soap_delete_var(pVar);
                            rc = -1;
                            break;
                        }
                        pResponse->m_child = pVar;
                    }
                }
            }
            pe = QXMLnextElement(pxml);
        }
        if (rc)
        {
            break;
        }
        if (requests <= 0)
        {
            NET_WARN("No elements in requested elements\n");
            rc = -3;
            break;
        }
        // format response
        //
        rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "GetScannerElementsResponse",
                        srv_data->soap_header,
                        "wscn",
                        "http://schemas.microsoft.com/windows/2006/08/wdp/scan",
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
        if (rc)
        {
            NET_WARN("Can't format SOAP\n");
        }
    }
    while (0); // CATCH

    if(pResponse)
    {
        soap_delete_var(pResponse);
    }
    return rc;
}

static ACTION_HANDLER_DEFINITION(wsd_scan_create_job_handler)
{
    WSD_TICKET_S* pticket = NULL;
    WSD_SCAN_TICKET_S* scan_ticket;
    SOAP_VAR_S *pResponse, *pVV, *px;
    int rc;
    char jobtoken[128] = {0};
    QXML_S xml;
    const char *fault_code   = WSD_SOAP_RECEIVER;
    const char *fault_sucode = "OperationFailed";
    const char *fault_reason = "Busy";
    SOAP_CHILD_ELEM_S child_elems[3];

    pResponse = NULL;
    NET_DEBUG("Handling CreateScanJobRequest\n");

    do
    {
        rc = soap_find_var_in_xml(pxml, "ScanTicket");
        if (rc)
        {
            NET_WARN("No find %s !!!", "ScanTicket");
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "InvalidArgs";
            fault_reason = "Not find ScanTicket";
            break;
        }

/*
        if (scan_status_get_status() != STATUS_I_SCAN_IDLE STATUS_I_SCAN_SLEEP)
        {
            fault_code = WSD_SOAP_RECEIVER;
            fault_sucode = "ServerErrorNotAcceptingJobs";
            fault_reason = "The service is temporarily blocked and can't accept new scan job requests.";
            rc = -1;
            break;
        }
        */

        QXMLparserSyncTo(pxml, &xml);
        scan_ticket = ScanTicketCreate();
        pticket = (WSD_TICKET_S*) scan_ticket;

        if ( pticket == NULL || wsd_job_register(&s_scan_service, pticket) )
        {
            NET_WARN("Can't create scan ticket\n");
            fault_code   = WSD_SOAP_RECEIVER;
            fault_sucode = "OperationFailed";
            fault_reason = "Busy";
            rc = -1;

            if (pticket) // wsd_job_register must be failed
            {
                ScanTicketDestroy(pticket);
                pticket = NULL;
            }
            break;
        }

        SOAP_CHILD_ELEM_RESET( child_elems[0], "JobName", pticket->jobName, sizeof(pticket->jobName), dtstring );
        SOAP_CHILD_ELEM_RESET( child_elems[1], "JobOriginatingUserName", pticket->jobOriginator, sizeof(pticket->jobOriginator), dtstring );
        SOAP_CHILD_ELEM_RESET( child_elems[2], "JobInformation", scan_ticket->jobInfo, sizeof(scan_ticket->jobInfo), dtstring );
        rc = soap_find_child_elements(&xml, "JobDescription", child_elems, 3);
        if (rc)
        {
            NET_DEBUG("No job JobDescription");
            rc = 0;
        }
        scan_ticket->param = (WSD_SCAN_PARM_S*)pi_zalloc(sizeof(WSD_SCAN_PARM_S));
        if (scan_ticket->param == NULL)
        {
            NET_WARN("Can't create scan param\n");
            rc = -1;
            break;
        }

        scanner_set_default_param(scan_ticket->param);
        scanner_get_doc_parms("DocumentParameters", pxml, scan_ticket->param);
        if ( (SCAN_MODE_ADF == scan_ticket->param->scan_mode || SCAN_MODE_DADF == scan_ticket->param->scan_mode)
             && STATUS_I_SCAN_SLEEP != scan_status_get_status()
             && ADF_NO_PAPER == scan_status_get_adf_paper_status() )
        {
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "ClientErrorNoImagesAvailable";
            fault_reason = "The server has no images available to acquire";
            rc = WSD_ERR_NoImagesAvailable;
            *reply_code = (char*)http_status_string(500);
            break;
        }

        snprintf(pticket->jobIdString, sizeof(pticket->jobIdString), "%d", pticket->job_id);
        pResponse = soap_create_var("wscn:JobId", dtint, 0, pticket->jobIdString, 0);
        if (! pResponse)
        {
            rc = -1;
            break;
        }
        pVV = pResponse;
        snprintf(jobtoken, sizeof(jobtoken), "JobToken: %d", pticket->job_id);
        pVV->m_next = soap_create_var("wscn:JobToken", dtchar, 0, jobtoken, 0);
        if (! pVV->m_next)
        {
            rc = -1;
            break;
        }
        pVV = pVV->m_next;
        rc = WSDscanImageInfoVar(scan_ticket->param, &px);
        if (rc || !px)
        {
            rc = rc ? rc : -1;
            break;
        }
        pVV->m_next = px;
        while (pVV->m_next)
        {
            pVV = pVV->m_next;
        }
        pVV->m_next = soap_create_var("wscn:DocumentFinalParameters", dtchar, 0, "", 0);
        if (! pVV->m_next)
        {
            rc = -1;
            break;
        }
        pVV = pVV->m_next;
        rc = WSDscanDocumentInfoVar(scan_ticket->param, &px);
        if (rc || !px)
        {
            rc = rc ? rc : -1;
            break;
        }
        pVV->m_child = px;

        // format response
        //
        rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "CreateScanJobResponse",
                        srv_data->soap_header,
                        WSD_SCAN_XMLNS,
                        WSD_SCAN_XMLNS_URL,
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
    }
    while(0); // CATCH

	if (pResponse)
    {
		soap_delete_var(pResponse);
    }

    if (rc)
    {
        if (pticket)
        {
            pticket->state = NETJOB_STATE_ABORTED;
        }

        rc = 0;
        if ( WSDcreateFaultResponse(srv_data, fault_code, WSD_SCAN_XMLNS, fault_sucode, fault_reason) )
        {
            NET_WARN("And can't even format a response\n");
            rc = -1;
        }
    }
    else
    {
        NET_DEBUG("CreateScanJobRequest Success !!!");
        srv_data->pticket = pticket;
    }

    return rc;
}

static int32_t wsd_scanner_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    char desired_url[8];
    WSD_TICKET_S* pticket;

    RETURN_VAL_IF(netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_scn_ctx)) == 0, NET_WARN, -1);
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    //reserve pticket;
    pticket = priv->service_data.pticket;
    memset(priv, 0, sizeof(PRIV_INFO_S));
    priv->service_data.pticket = pticket;

    snprintf(desired_url, sizeof(desired_url), "/%u", ptask->l_port);

    if (strcmp(url, desired_url))
    {
        NET_WARN("desired url: %s, but receive url: %s, discard", desired_url, url);
        return -1;
    }
    priv->received = 0;

    return 0;
}

static int32_t wsd_scanner_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        NET_WARN("request body overlength(%u + %u) from client(%u->%u)", priv->received, ndata, ptask->r_port, ptask->l_port);
        return -1;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;

    return 0;
}

static int32_t wsd_scanner_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    WSD_SERVICE_DATA_S* srv_data = &priv->service_data;
    char hostname[HOSTNAME_LEN];

    RETURN_VAL_IF(netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_scn_ctx)) == 0, NET_WARN, -1);
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsd_scn_ctx), hostname, sizeof(hostname));
    snprintf(srv_data->service_url, sizeof(srv_data->service_url),
            "http://%s:%u/%u", hostname, ptask->l_port, ptask->l_port);

    return wsd_process_service(&s_scan_service, ptask, priv->iobuf, srv_data);
}


static int32_t wsd_scanner_construct(HTTP_TASK_S* ptask)
{
    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[0] == NULL )
    {
        ptask->priv_subclass[0] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[0] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = wsd_scanner_process_headers;
    ptask->reqbody_received_callback = wsd_scanner_process_reqbody;
    ptask->request_complete_callback = wsd_scanner_process_request;

    return 0;
}

static void wsd_scanner_destruct(HTTP_TASK_S* ptask)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    if ( priv )
    {
        pi_free(priv);
        ptask->priv_subclass[0] = NULL;
    }
    return;
}


static void wsd_scanner_connection(NET_CONN_S* pnc)
{
    HTTP_TASK_S*    ptask;
    QIO_S*          pqtcp;

    pqtcp = qio_tcp_create(pnc->sockfd, NETQIO_SOCK_NOCLOSE);
    if ( pqtcp != NULL )
    {
        ptask = http_task_create(pqtcp, pnc->remote_port, pnc->local_port);
        if ( wsd_scanner_construct(ptask) == 0 )
        {
            http_task_process(ptask);
        }
        wsd_scanner_destruct(ptask);
        http_task_destroy(ptask);
        QIO_CLOSE(pqtcp);
    }
    netsock_close_connection(pnc);
}

static void* wsd_scanner_thread(void *arg)
{
    wsd_tcp_loop(s_wsd_scn_ctx->efd, THREADS_POOL_OF(s_wsd_scn_ctx->wsd_ctx), wsd_scanner_get_port, (THREAD_TASK_FUNC)wsd_scanner_connection);
    return NULL;
}

static void* wsd_scanner_message_thread(void *arg)
{
    WSD_TICKET_S* pticket;
    WSD_SCAN_TICKET_S* scan_ticket;
    ROUTER_MSG_S  message;
    int32_t       job_id;
    uint64_t      evt_data = 1;
    int32_t       rv;

    while( 1 )
    {
        memset(&message, 0, sizeof(message));
        rv = task_msg_wait_forever_by_router(MID_WSDSCAN, &message);
        if ( rv < 0 )
        {
            continue; /* timeout */
        }

        NET_DEBUG("wsd scanner message thread recv %s from %s, jobid = %u", get_msg_type_string(message.msgType),
                get_module_id_string(message.msgSender), message.msg1);

        job_id = message.msg1;
        pticket = wsd_ticket_get_from_job_id(&s_scan_service, job_id);
        scan_ticket = (WSD_SCAN_TICKET_S*)pticket;
        if (pticket == NULL)
        {
            continue;
        }

        switch ( message.msgType )
        {
            case MSG_DATA_JOB_START:
                pticket->state = NETJOB_STATE_PROCESSING;
                break;
            case MSG_DATA_PAGE_START:
                break;
            case MSG_DATA_PAGE_END:
                ticket_add_page(scan_ticket, (const char *)message.msg3);
                pi_free(message.msg3);
                break;
            case MSG_DATA_JOB_END:
                if (scan_ticket->param->scan_file == FILE_PDF || scan_ticket->param->scan_file == FILE_XPS)
                {
                    ticket_add_page(scan_ticket, (const char *)message.msg3);
                    pi_free(message.msg3);
                }
                if ( pticket->state != NETJOB_STATE_CANCELED)
                {
                        SPIN_LOCK(&scan_ticket->images_lock, {
                            scan_ticket->images_scanned_finish = 1;
                            if (scan_ticket->images_tranfered == scan_ticket->images_scanned)
                            {
                                pticket->state = NETJOB_STATE_DONE;
                            }
                        })
                }

                break;
            case MSG_CTRL_JOB_CANCEL:
                pticket->state = NETJOB_STATE_CANCELED;
                break;
            default:
                break;
        }
        wsd_message_notify(scan_ticket, message.msgType);
    }
    return NULL;
}

static void wsd_scanner_epilog(void)
{
    if ( s_wsd_scn_ctx != NULL )
    {
        if (s_wsd_scn_ctx->net_tid != INVALIDTHREAD)
        {
            pi_thread_destroy(s_wsd_scn_ctx->net_tid);
        }
        if (s_wsd_scn_ctx->msg_tid != INVALIDTHREAD)
        {
            pi_thread_destroy(s_wsd_scn_ctx->msg_tid);
        }
        if ( s_wsd_scn_ctx->mutex != INVALIDMTX )
        {
            pi_mutex_destroy(s_wsd_scn_ctx->mutex);
        }
        if (s_wsd_scn_ctx->efd)
        {
            pi_close(s_wsd_scn_ctx->efd);
        }
        if (s_scan_service.subs_list)
        {
            wsd_subs_list_destroy(s_scan_service.subs_list);
        }
        if (s_scan_service.job_pool)
        {
            wsd_job_pool_destroy(s_scan_service.job_pool);
        }

        pi_free(s_wsd_scn_ctx);
        s_wsd_scn_ctx = NULL;
    }
}

static int32_t wsd_scanner_prolog(WSD_CTX_S* wsd_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_wsd_scn_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(wsd_ctx == NULL, NET_WARN, -1);

    s_wsd_scn_ctx = (WSD_SCANNER_CTX_S*)pi_zalloc(sizeof(WSD_SCANNER_CTX_S));
    RETURN_VAL_IF(s_wsd_scn_ctx == NULL, NET_WARN, -1);
    s_wsd_scn_ctx->wsd_ctx = wsd_ctx;

    do
    {
        s_scan_service.subs_list = wsd_subs_list_create();
        BREAK_IF(s_scan_service.subs_list == NULL, NET_WARN);

        s_scan_service.job_pool = wsd_job_pool_create(8);
        BREAK_IF(s_scan_service.job_pool == NULL, NET_WARN);

        s_wsd_scn_ctx->efd = eventfd(0, EFD_NONBLOCK);
        BREAK_IF(s_wsd_scn_ctx->efd == -1, NET_WARN);
        wsd_ctx->efd_arr[wsd_ctx->efd_cnt++] = s_wsd_scn_ctx->efd;

        s_wsd_scn_ctx->mutex = pi_mutex_create();
        BREAK_IF(s_wsd_scn_ctx->mutex == INVALIDMTX, NET_WARN);

        s_wsd_scn_ctx->net_tid = pi_thread_create(wsd_scanner_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "wsd_scanner_net_thread");
        BREAK_IF(s_wsd_scn_ctx->net_tid == INVALIDTHREAD, NET_WARN);

        s_wsd_scn_ctx->msg_tid = pi_thread_create(wsd_scanner_message_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "wsd_scanner_message_thread");
        BREAK_IF(s_wsd_scn_ctx->msg_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WSD initialize result(%d)", ret);
    if ( ret != 0 )
    {
        wsd_scanner_epilog();
    }
    return ret;
}

FUNC_EXPORT(init, wsd_scanner_prolog);
FUNC_EXPORT(deinit, wsd_scanner_epilog);
