/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       job_subsytem_interface.h
 * @addtogroup copy system
 * @{
 * @addtogroup job subsystem
 * <AUTHOR>
 * @date       2023-06-1
 * @version    v1.0
 * @details    interface for the job subsystem
 */
#ifndef __JOB_SUBSYSTEM_INTERFACE_H__
#define __JOB_SUBSYSTEM_INTERFACE_H__

#ifdef __cplusplus
extern "C" {
#endif

#ifdef CONFIG_COPY
extern int get_current_job_id();
#else
int get_current_job_id(){return 0;};
#endif

/**
* @brief get the version of this interface
* @return the version
* @autor liushaoxi
* @date 2023-06-1
*/
char *job_get_intf_version(void);

/**
* @brief initialize the job subsystem
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-06-1
*/
int job_subsystem_init(void);

#ifdef __cplusplus
}
#endif


#endif
/**
 *@}
 */
