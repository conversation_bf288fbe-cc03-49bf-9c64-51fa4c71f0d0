/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi.h
 * @addtogroup net
 * @{
 * @addtogroup wifi
 * <AUTHOR>
 * @date 2023-4-20
 * @brief wifi control API
 */
#ifndef __WIFI_H__
#define __WIFI_H__

typedef enum
{
    PMF_DISABLE  = 0,
    PMF_CAPABLE  = 1,
    PMF_REQUIRED = 2,

    PMF_AUTO     = 0xff
}
WIFI_PMF_MODE_E;

int32_t wifi_prolog                     (NET_CTX_S* net_ctx);

void    wifi_epilog                     (void);

/**
 * control Wi-Fi station interface
 */
int32_t wifi_ctrl_sta_wpa_reconfigure   (void);

int32_t wifi_ctrl_sta_switch            (uint32_t on);

int32_t wifi_ctrl_sta_scan_ssid         (uint32_t on);

int32_t wifi_ctrl_sta_connect           (int32_t sync); /* sync: 0 - 异步调用； 1 - 等待process_sta_connect执行完成后返回； 2 - 等待本次连接结果后返回 */

int32_t wifi_ctrl_sta_disconnect        (void);

int32_t wifi_ctrl_sta_wps_pbc           (void);

int32_t wifi_ctrl_sta_wps_pin           (void);

int32_t wifi_ctrl_sta_wps_cancel        (void);

/**
 * control Wi-Fi Direct interface
 */
int32_t wifi_ctrl_wfd_p2p_group         (uint32_t operate); /* 0 - remove; 1 - add */

int32_t wifi_ctrl_wfd_switch            (uint32_t mode);

int32_t wifi_ctrl_wfd_running           (void);

int32_t wifi_ctrl_wfd_response          (uint32_t resp);

#endif /* __WIFI_H__ */
/**
 *@}
 */
