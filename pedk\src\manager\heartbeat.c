/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file heartbeat.c
 * @addtogroup manager
 * @{
 * @addtogroup manager
 * @autor 
 * @date 2024-06-11
 * @brief heartbeat init
 */
#include "manager/heartbeat.h"
#include "manager/manager.h"
#include "runtime/runtime.h"
#include "basic/sys/sys.h"
#include "trans/transmission.h"
#include <string.h>
#include "runtime/utils/msgq_utils.h"
#include "runtime/utils/tool_utils.h"
#include "runtime/utils/uthash_utils.h"
#include "runtime/utils/quickjs_utils.h"
#include "basic/config.h"
#include <uv.h>
#include "runtime/modules/module_manager.h"
#include "runtime/modules/bridge/bridge.h"

/**
 * @brief 心跳的回调函数
 *       每隔15s调一下：
 *          （1）如果心跳正常，则向所有已运行的app，广播心跳
 *          （2）如果存在心跳异常，则停止定时器，将心跳运行时加入到哈希表中，再启动心跳脚本
 * @param handle 
 */
static void heartbeat_cb(uv_timer_t* handle)
{
    PeSFRunTime* prt;

    PESF_ITER(prt){
        if(prt->heartbeat_flag == BEAT){
            prt->heartbeat_flag = NOT_BEAT;

            //制作心跳消息
            INNER_MSG inner_msg;
            inner_msg.type = E_INNER_HEARTBEAT;
            inner_msg.data_length = 0;
            inner_msg.data = NULL;
            LOG_D("heartbeat","heart beat:[%s]\n",prt->app_name);
            //发送心跳消息
            send_data_to_queue(prt, &inner_msg);
        }else{
            LOG_I("heartbeat","heart not beat:[%s]",prt->app_name);
            //停止心跳
            uv_timer_stop(handle);

            //获取heartbeat的运行时
            PeSFRunTime* heartbeat_prt = (PeSFRunTime*)handle->data;
            //将运行时插入哈希表，否则收不到消息
            uint8_t rtid = hash_add(heartbeat_prt);
            heartbeat_prt->dynamic_property.rtid = rtid;
            LOG_D("manager","exception.js hash_add ok");
            
            //替换app名，方便提示页显示
            heartbeat_prt->app_name = prt->app_name;

            // 2.执行脚本
            runtime_run(heartbeat_prt);
            //send_to_bridge((PeSFRunTime*)handle->data, strlen(prt->app_name), prt->app_name);
            LOG_D("heartbeat","heart not beat after run exception\n");

            break;
            //uv_timer_start(handle, heartbeat_cb, 15000, 15000);
        }
    } 
}

/**
 * @brief 心跳运行时的初始化
 *        和普通app初始化存在区别，去掉大部分无用功能，仅保留uv_loop
 * @param prt 
 */
static void runtime_init_inner(PeSFRunTime* prt)
{
    // 创建JS运行时与JS上下文
    prt->qjs_rt = JS_NewRuntime();
    prt->qjs_ctx = JS_NewContext(prt->qjs_rt);

    // 2.将运行时传递给qjs_ctx的user，使JS内部也可以使用运行时外部内容。
    JS_SetContextOpaque(prt->qjs_ctx, prt);

    // 3.初始化libuv
    prt->uv_loop = uv_loop_new();

    // 5.1.实例化列表初始为NULL
    prt->instance_list = NULL;
    // 5.2.调用全部模块的实例化函数。
    modules_instance_all(prt);
}

/**
 * @brief 心跳线程的启动函数
 *        *跟普通APP的启动函数存在区别，此线程不启动脚本，只启动定时器，再失去心跳时再启动脚本*
 * @param rt 
 * @return void* 
 */
void* heartbeat_start(void* rt)
{
    int32_t ret = 0;
    int32_t loop_flag = 1;

    // 参数获取属于自己线程的运行时
    PeSFRunTime* prt = (PeSFRunTime*)rt;

	LOG_I("inner","inner [%s] start\n", prt->app_path.app_js);
    // 1.执行脚本准备
    runtime_init(prt);

    uv_timer_t* timer = (uv_timer_t*)malloc(sizeof(uv_timer_t));
    timer->data = prt;
    uv_timer_init(prt->uv_loop, timer);

    // 启动定时器
    uv_timer_start(timer, heartbeat_cb, 60000, 60000);

    // 4.进入js循环
    do {
        uv_run(prt->uv_loop, UV_RUN_DEFAULT);
    } while (1);

}

/**
 * @brief 心跳功能的运行时创建。
 *        *和普通app的运行时创建有区别，路径和初始化函数不同*
 * 
 * @return PeSFRunTime* 
 */
PeSFRunTime* runtime_create_heartbeat(char* inner_path_app)
{
    PeSFRunTime* prt = (PeSFRunTime*)malloc(sizeof(PeSFRunTime));

    // start指令，数据为应用程序名，字符串后'\0'
    prt->app_name = "heartbeat";
   
    // 准备基础数据
    prt->app_path.work_space = PESF_JS_INNER_PATH;
    prt->app_path.app_js = EXCEPTION_APP;
    prt->app_path.app_json = NULL;
    prt->app_path.proj_config_json = NULL;

    // 函数绑定
    prt->start_func = heartbeat_start; // 绑定默认回调函数，pesf开发者可以自定义替换这些回调函数。
    prt->add_instance_data = add_instance_data; // 添加实例数据函数
    prt->get_instance_data = get_instance_data; // 获取实例数据函数
    prt->del_instance_data = del_instance_data; // 删除实例数据函数

    return prt;
}

void runtime_destroy_heartbeat(PeSFRunTime *prt)
{
    free(prt);
}

/**
 * @brief 心跳功能初始化函数
 * 
 * @return int32_t 
 */
int32_t heartbeat_init()
{
    int32_t ret;
    uint32_t rtid;
    PeSFRunTime* prt = runtime_create_heartbeat(EXCEPTION_APP);
    
    ret = pesf_new_thread(prt);
    if (0 != ret) {
        // 如果启动失败，则回收运行时
        runtime_destroy_heartbeat(prt);
        LOG_E("heartbeat","start heartbeat fail");
    }



    return ret;
}
/**
 * @}
 */
 
