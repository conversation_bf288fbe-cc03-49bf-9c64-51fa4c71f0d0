#include <stdio.h>
#include <unistd.h>
#include "cmd.h"
#include "pol_mem_trace.h"
#include "pol_threads.h"
#include "pol_mem.h"
#include "pol_io.h"

#include <assert.h>
#include <errno.h>
#include "case.h"
#define CHILD_SLEEP_INTERVAL		10
#define PARENT_SLEEP_INTERVAL		2
#define THREAD_SLEEP_LONG			2
#define THREAD_SLEEP_INTERVAL		1
#define MMAP_LEN					8*1024 
#define THREAD_NUM 	   				3


typedef enum {
    CHILD_ID_0,
    CHILD_ID_1,
    CHILD_ID_2,
    CHILD_ID_3,
    CHILD_ID_4,
    CHILD_ID_MAX,
};

typedef void* (*ENTRY_F)(void *);

int parent_loop(void );
int thread_0_loop( void );
int thread_1_loop( void );
int thread_2_loop( void );
int thread_3_loop( void );
int thread_4_loop( void );

static uint8_t freelist_addr_check(void *addr, char* func_str);
static uint8_t alloctree_addr_check(void *addr,uint32_t len,char * func_str);

int array_t[16] = {0};
int main(void)
{
    pid_t pid;
    int8_t i;


    assert(CONFIG_POL_MEM_TRACE==1);
    assert(CONFIG_POL_MEM_TRACE_BACKTRACE_LV>0);
    parent_loop();
}

static void* share_address[8];
static void* share_map_address[8];

static uint32_t search_free_share_address( uint32_t off)
{
	uint32_t j = 0;

	while(share_address[off])
	{
		j++;
		off = (off+j)%8;
		if((j%8)==0)
			sleep(1);
		 
	}
	printf("%s :off:%d\n", __func__, (int32_t)off);
	return off;
}

static uint32_t search_free_share_map_address( uint32_t off)
{
	uint32_t j = 0;

	while(share_map_address[off])
	{
		j++;
		off = (off+j)%8;
		if((j%8)==0)
			sleep(1);
		 
	}
	printf("%s :off:%d\n", __func__,(int32_t)off);
	return off;
}


static uint32_t search_alloc_share_address( )
{
	uint32_t j = 0;

	while(share_address[j]==NULL)
	{
		j = (j+1)%8;
		if(j==0)
			sleep(1);
		 
	}
	printf("%s :off:%d\n", __func__,(int32_t)j);
	return j;
}



static uint32_t search_mmap_share_address( )
{
	uint32_t j = 0;

	while(share_map_address[j]==NULL)
	{
		j = (j+1)%8;
		if(j==0)
			sleep(1);
		 
	}
	printf("%s :off:%d\n", __func__,(int32_t)j);
	return j;
}


/* malloc  cycle  */
int thread_0_loop( void )
{
    int fd;
    static uint32_t count = 0;
	int i= 0, off = 0;

    while(1)
    {
        log("[%s:%d] loop count %d\n",__func__,getpid(),(int32_t)count++);
		off = search_free_share_address(off);
        share_address[off] = pi_malloc(20);
		printf("%s :off:%d\n", __func__,off);		
   		pi_sleep(THREAD_SLEEP_LONG);
    }	
    close(fd);

}

/* free  cycle  */
int thread_1_loop( void )
{
    int fd;
    static uint32_t count = 0;
	int i= 0, off = 0;
	void *addr_tmp;
	void *addr_map;

    while(1)
    {
        log("[%s:%d] loop count %d\n",__func__,getpid(),(int32_t)count++);
		off = search_alloc_share_address();
		addr_tmp = share_address[off];
		if(alloctree_addr_check(addr_tmp,20,__func__)){
			printf(COLOR_RED "case :%s-%d  FAIL!!!\n"COLOR_END, __func__,__LINE__);			
		}		
        pi_free(share_address[off]);

		off = search_mmap_share_address();
		addr_map = share_map_address[off];
		if(alloctree_addr_check(addr_map,MMAP_LEN,__func__)){
			printf(COLOR_RED "case :%s-%d  FAIL!!!\n"COLOR_END, __func__,__LINE__);			
		}		
        pi_munmap(share_map_address[off],MMAP_LEN);
		
	    if(freelist_addr_check(addr_tmp,__func__)){			
			printf(COLOR_RED "case :%s-%d  FAIL!!!\n"COLOR_END, __func__,__LINE__);
	    }	
//		printf("%s :off:%d\n", __func__,off);		
   		pi_sleep(THREAD_SLEEP_INTERVAL);
    }	
    close(fd);

}

/* free  cycle  */
int thread_2_loop( void )
{
    int fd;
    static uint32_t count = 0;
	int i= 0, off = 0;
    while(1)
    {
    
        log("[%s:%d] loop count %d\n",__func__,getpid(),(int32_t)count++);
		off = search_free_share_map_address(off);
        share_map_address[off] = pi_mmap(NULL, MMAP_LEN,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
		printf("%s :off:%d\n", __func__,off);		
   		pi_sleep(THREAD_SLEEP_LONG );
    }	
    close(fd);


}


extern void memtrace_heap_dump_node_to_file( char *path );


/*#define MALLOC_CMD		"malloc"
#define REALLOC_CMD		"realloc"
#define ZALLOC_CMD		"zalloc"
#define FREE_CMD		"free"*/
#define DUMP_CMD		"dump"
#define DUMP_FREE_CMD	"dumpfree"


int cmd_hdl(char *cmd,char *param)
{
    if(!strcasecmp(cmd,DUMP_CMD))
    {

        memtrace_heap_dump_node_to_file(param);

    }

    if(!strcasecmp(cmd,DUMP_FREE_CMD))
    {

        memtrace_heap_dump_freelist_to_file(param);

    }

}

#define PARENT_INPUT_PATH	"p_in"

int32_t cmd_recieve_and_handle( int fd  )
{

    char cmd[16];
    char param[16];
    char buf[32];

    pi_memset(buf, 0, sizeof(buf));	
    pi_memset(cmd, 0, sizeof(cmd));
    pi_memset(param, 0, sizeof(param));
    pi_memcmp(param,cmd,8);

    ssize_t ret;
    if(ret = read(fd,buf,sizeof(buf))){
        log("[%s]read string:%s\n",__func__,buf);	
        if(sscanf(buf,"%15s %15s", cmd,param))
        {
            log("[%s]read  cmd:%s,param:%s\n",__func__,cmd,param);	
            return cmd_hdl(cmd,param);
        }
    }
    if(ret == -1){
        log("[%s] pid::%d read error:%s\n",__func__,getpid(),strerror(errno));	
        return -1;
    }
    else {
        return 0;
    }
}






pthread_t pt_id[THREAD_NUM];
void* pt_arg[THREAD_NUM];
ENTRY_F pt_entry[THREAD_NUM]={
    thread_0_loop,
    thread_1_loop,
    thread_2_loop,
    thread_3_loop,
    thread_4_loop,
};


int parent_loop(void )
{	

    void* ptr;

    static uint32_t count = 0;
    int32_t t_id=0,case0_num,i=0;
    case0_func c0_func;
    printf("%s\n",__func__);
    mem_trace_init();

    memset(pt_arg,0, sizeof(pt_arg));

    /*    if(mkfifo(PARENT_INPUT_PATH,S_IRWXU|S_IRWXO|S_IRWXG)<0)
          {
          log("%s err:mkfifo \n",__func__);	
          }		
          fd= open(PARENT_INPUT_PATH, O_RDONLY);	
          mem_trace_init();

          while(1)
          {
          cmd_recieve_and_handle(fd);
          log("[%s:%d] loop count %d\n",__func__,getpid(),count++);

          ptr = pi_malloc(20);
          pi_sleep(PARENT_SLEEP_INTERVAL);
          pi_free(ptr);
          }	
          */
    while(i<THREAD_NUM)
    {
        if(pthread_create(&pt_id[i],NULL,pt_entry[i],pt_arg[i])){

            perror("pthread_create error");
            printf("id:%d\n",i);

        }
        i++;
    }


    /////////////////////////////////////////////////////////////

    case0_num =get_case_sets_num();


    while(t_id<case0_num)
    {
        c0_func = case0_sets[t_id++];
        if(c0_func()!=PASS)
            sleep(10);
    }
    while(1) sleep(5);
    printf("%s:end\n",__func__);
}



