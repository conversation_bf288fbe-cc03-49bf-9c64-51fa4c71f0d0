#include <pol_crypto.h>

int32_t aes_256_ctr_encrypt(const unsigned char *key, const unsigned char *plaintext, int32_t plaintext_len, unsigned char **ciphertext, unsigned char *iv)
{
    EVP_CIPHER_CTX *ctx;
    int32_t len;
    int32_t ciphertext_len = 0;

    // 创建并初始化上下文
    if( !(ctx = EVP_CIPHER_CTX_new()) ) 
    {
        printf("[%s:%d] EVP_CIPHER_CTX_new error occurred \n", __FILE__, __LINE__);
    }
    // 初始化加密操作，指定使用AES-256-CTR算法
    if(1 != EVP_EncryptInit_ex(ctx, EVP_aes_256_ctr(), NULL, key, iv))
    {
        printf("[%s:%d] EVP_EncryptInit_ex error occurred \n", __FILE__, __LINE__);
    }
    // 分配内存用于密文
    *ciphertext = (unsigned char *)malloc(plaintext_len + AES_BLOCK_SIZE);
    if ( NULL == *ciphertext ) 
    {
        printf("[%s:%d] malloc error occurred \n", __FILE__, __LINE__);
    }

    // 执行加密操作，更新密文长度
    if( 1 != EVP_EncryptUpdate(ctx, *ciphertext, &len, plaintext, plaintext_len) )
    {
        printf("[%s:%d] EVP_EncryptUpdate error occurred \n", __FILE__, __LINE__);
    }
    ciphertext_len = len;
    // 完成加密操作，追加可能存在的最后一块数据
    if( 1 != EVP_EncryptFinal_ex(ctx, *ciphertext + len, &len) ) 
    {
        printf("[%s:%d] EVP_EncryptFinal_ex error occurred \n", __FILE__, __LINE__);
    }
    ciphertext_len += len;
    // 清理
    EVP_CIPHER_CTX_free(ctx);

    return ciphertext_len;
}

int32_t aes_256_ctr_decrypt(const unsigned char *key, const unsigned char *ciphertext, int ciphertext_len, unsigned char **plaintext, unsigned char *iv)
{
    EVP_CIPHER_CTX *ctx;
    int len;
    int plaintext_len;

    // 创建并初始化上下文
    if( !(ctx = EVP_CIPHER_CTX_new()) )
    {
        printf("[%s:%d] EVP_CIPHER_CTX_new error occurred \n", __FILE__, __LINE__);
    }
    // 初始化解密操作，指定使用AES-256-CTR算法
    if( 1 != EVP_DecryptInit_ex(ctx, EVP_aes_256_ctr(), NULL, key, iv) )
    {
        printf("[%s:%d] EVP_DecryptInit_ex error occurred \n", __FILE__, __LINE__);
    }
    // 分配内存用于明文
    *plaintext = (unsigned char *)malloc(ciphertext_len + 1);
    if ( NULL == *plaintext )
    {
        printf("[%s:%d] malloc error occurred \n", __FILE__, __LINE__);
    }
    // 执行解密操作，更新明文长度
    if( 1 != EVP_DecryptUpdate(ctx, *plaintext, &len, ciphertext, ciphertext_len) )
    {
        printf("[%s:%d] EVP_DecryptUpdate error occurred \n", __FILE__, __LINE__);
    }
    plaintext_len = len;

    // 完成解密操作，追加可能存在的最后一块数据
    if( 1 != EVP_DecryptFinal_ex(ctx, *plaintext + len, &len) )
    {
        printf("[%s:%d] EVP_DecryptFinal_ex error occurred \n", __FILE__, __LINE__);
    }
    plaintext_len += len;

    // 添加字符串结束符
    (*plaintext)[plaintext_len] = '\0';

    // 清理
    EVP_CIPHER_CTX_free(ctx);

    return plaintext_len;
}

unsigned char* read_file(const char *filename, long *length)
{
    FILE* fd = NULL;
    fd = fopen(filename, "rb");
    if ( NULL == fd )
    {
        printf("Unable to open file\n");
        return NULL;
    }
    fseek(fd, 0, SEEK_END);
    ssize_t file_size = ftell(fd);
    if ( file_size == -1 ) 
    {
        printf("Unable to determine file size\n");
        fclose(fd);
        return NULL;
    }

    *length = file_size;
    fseek(fd, 0, SEEK_SET);

    unsigned char *content = (unsigned char *)malloc(*length);
    if ( NULL == content ) 
    {
        printf("Unable to allocate memory\n");
        fclose(fd);
        return NULL;
    }
    if ( fread(content, sizeof(char), *length, fd) != *length ) 
    {
        printf("Unable to read file\n");
        free(content);
        content = NULL;
    }
    fclose(fd);
    return content;
}

unsigned char* find_sub_data(unsigned char* src_text, int src_len, unsigned char* sub_text, int sub_len)
{
    int offset = 0;

    while ( offset < (src_len - sub_len) )
    {
        int i = 0;
        const unsigned char* src_tmp = src_text;
        const unsigned char* sub_tmp = sub_text;

        for ( i = 0; i < sub_len; i++ )
        {
            if ( src_tmp[i] != sub_tmp[i] )
            {
                offset++;
                src_text++;
                break;
            }
        }

        if ( i == sub_len )
        {
            return src_text;
        }
    }

    return NULL;
}

int32_t read_and_decrypt_logs(const char *in_filename, const unsigned char *key, const unsigned char *iv, const char* out_filename)
{
    long encrypted_len;
    unsigned char *encrypted_content = read_file(in_filename, &encrypted_len);
    if ( NULL == encrypted_content )
    {
        return -1;
    }
    FILE *fd = NULL;
    fd = fopen(out_filename,"wb");
    if ( NULL == fd )
    {
        printf("fopen out_filename error \n");
        return -1;
    }
    //int i = 0;
    printf("encrypted_len == %ld\n", encrypted_len);
    unsigned char *start_ptr = NULL;
    unsigned char *end_ptr = NULL;
    unsigned char *ciphertext;
    unsigned char *decryptedtext;
    long size = encrypted_len;

    start_ptr = find_sub_data(encrypted_content, size, (unsigned char *)CRYPTO_START_MARKER, strlen(CRYPTO_START_MARKER));
    //printf("%p \t %p\n",encrypted_content,start_ptr);

    //size += strlen(CRYPTO_START_MARKER);
    //printf("%p\n", start_ptr);    
    while (start_ptr != NULL) 
    {

        size = encrypted_len - (start_ptr - encrypted_content);

        end_ptr = find_sub_data(start_ptr + strlen(CRYPTO_START_MARKER), size, (unsigned char *)CRYPTO_END_MARKER, strlen(CRYPTO_END_MARKER));

        if( end_ptr == NULL )
        {
            printf("error\n");
            int i;
            for( i = 0 ; i < (strlen(CRYPTO_START_MARKER) + 1); i++ )
            {
                printf("%d,", start_ptr[i]);
            }
            printf("\n");
            return -1;
            break;
        }

        start_ptr += strlen(CRYPTO_START_MARKER);
        int ciphertext_len = end_ptr - start_ptr;
        //printf("密文长度 == %d\n",ciphertext_len);
        ciphertext = (unsigned char *)malloc(ciphertext_len);

        memcpy(ciphertext, start_ptr, ciphertext_len);

        int decryptedtext_len = aes_256_ctr_decrypt(key, ciphertext, ciphertext_len, &decryptedtext, (unsigned char *)iv);
        //printf("明文 ciphertext_len == %d\n", ciphertext_len);
        //printf("Decrypted log data: %s\n", decryptedtext);

        size += ciphertext_len;

        //i++;
        if( 0 > (fwrite(decryptedtext, sizeof(char), decryptedtext_len, fd)))
        {
            printf("fwrite error \n");
            return -1;
        }

        free(ciphertext);
        free(decryptedtext);

        size = encrypted_len - (end_ptr - encrypted_content);
        start_ptr = find_sub_data(end_ptr + strlen(CRYPTO_END_MARKER), size, (unsigned char *)CRYPTO_START_MARKER, strlen(CRYPTO_START_MARKER));
        if ( ( start_ptr != NULL ) && (start_ptr - end_ptr) != strlen(CRYPTO_END_MARKER) )
        {
            printf("start_ptr == %p , end_ptr ==%p  \t%d  size: %ld \n", start_ptr, end_ptr, (start_ptr - end_ptr), size);
            int j;
            for(j = 0 ; j < (strlen(CRYPTO_END_MARKER) + 1); j++ )
            {
                printf("%d,", start_ptr[j]);
            }
            printf("\n");
        }
    }
    //printf("i == %d\n",i);
    free(encrypted_content);
    return 0;
}

