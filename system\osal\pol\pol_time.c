/**************************************************************
Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name :  	POL (PANTUM OS LAYER)
file   name :	pol_time.c
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-26
description	:   pol time relative system interface header file
****************************************************************/

#include "pol_inner.h"
#include "pol/pol_time.h"
#if defined(CONFIG_POL_TIME_DEBUG)
#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

char* pi_ctime_r(const time_t *timep, char *buf)
{
    char* ret = NULL;
    _pi_input_check(timep&&buf);
    ret = ctime_r(timep,buf);
    _pi_retptr_check(ret,ret);
    return ret;
}

char * pi_asctime_r(const struct tm *tm, char *buf)
{
    char* ret = NULL;
    _pi_input_check(tm&&buf);
    ret = asctime_r(tm,buf);
    _pi_retptr_check(ret,ret);
    return ret;
}

struct tm* pi_gmtime_r(const time_t *timep, struct tm *result)
{
	struct tm* ret = NULL;
    _pi_input_check(timep&&result);
    ret = gmtime_r(timep,result);
    _pi_retptr_check(ret,ret);
    return ret;

}

struct tm* pi_localtime_r(const time_t *timep, struct tm *result)
{
	struct tm* ret = NULL;
    _pi_input_check(timep&&result);
    ret = localtime_r(timep,result);
    _pi_retptr_check(ret,ret);
    return ret;
}

size_t pi_strftime(char *s, size_t max, const char *format, const struct tm *tm)
{
	size_t ret = 0 ;
    _pi_input_check(s&&tm&&max);
    ret = strftime(s,max,format,tm);
    return ret;
}

time_t pi_time(time_t *tloc)
{
	time_t ret;
    ret = time(tloc);
    _pi_ret_check_err(ret!=-1);
    return ret;
}

time_t pi_mktime(struct tm *tm)
{
	time_t ret;

    _pi_input_check(tm);
	ret = mktime(tm);
	_pi_ret_check_err(ret!=-1);
	return ret;
}

clock_t pi_clock(void)
{
	clock_t ret;

	ret = clock();
	_pi_ret_check_err(ret!=-1);
	return ret;
}

int32_t pi_clock_gettime(clockid_t clk_id, struct timespec *tp)
{
	int ret = 0;

	_pi_input_check(tp);
	ret = clock_gettime(clk_id,tp);
	_pi_retint_check_err(ret!=-1,ret);
	return ret;
}

int32_t pi_clock_settime(clockid_t clk_id, const struct timespec *tp)
{
	int ret = 0;

	_pi_input_check(tp);
	ret = clock_settime(clk_id,tp);
	_pi_retint_check_err(ret!=-1,ret);
	return ret;
}

int32_t pi_msleep(uint32_t msec)
{
    struct timespec ts;
	int32_t ret =0 ;

	_pi_input_check(msec<1000);
    ts.tv_sec  = msec / 1000;
    ts.tv_nsec = msec % 1000 * 1000000;
    do
    {
        ret = nanosleep(&ts, &ts);
    }
    while (ret == -1 && errno == EINTR);
    _pi_retint_check_err(ret!=-1,ret);

	return ret;
}

int32_t pi_usleep(uint32_t usec)
{
    struct timespec ts;
	int32_t ret =0 ;

	_pi_input_check(usec<1000);
    ts.tv_sec  = usec / 1000000;
    ts.tv_nsec = usec % 1000000 * 1000;
    do
    {
        ret = nanosleep(&ts, &ts);
    }
    while (ret == -1 && errno == EINTR);
	_pi_retint_check_err(ret!=-1, ret);

	return ret;
}

int32_t pi_nanosleep(const struct timespec *req, struct timespec *rem)
{
	_pi_input_check(req&&rem);
	int32_t ret = nanosleep(req, rem);
	_pi_retint_check_err(ret!=-1, ret);
	return ret;
}

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

char* _pi_ctime_r(const char *callfile ,const uint32_t callline, const time_t *timep, char *buf)
{
    char* ret = NULL;
    _pi_input_check(timep&&buf);
    ret = ctime_r(timep,buf);
    _pi_retptr_check(ret,ret);
    return ret;
}

char * _pi_asctime_r(const char *callfile ,const uint32_t callline, const struct tm *tm, char *buf)
{
    char* ret = NULL;
    _pi_input_check(tm&&buf);
    ret = asctime_r(tm,buf);
    _pi_retptr_check(ret,ret);
    return ret;
}

struct tm* _pi_gmtime_r(const char *callfile ,const uint32_t callline, const time_t *timep, struct tm *result)
{
	struct tm* ret = NULL;
    _pi_input_check(timep&&result);
    ret = gmtime_r(timep,result);
    _pi_retptr_check(ret,ret);
    return ret;
}

struct tm* _pi_localtime_r(const char *callfile ,const uint32_t callline, const time_t *timep, struct tm *result)
{
	struct tm* ret = NULL;
    _pi_input_check(timep&&result);
    ret = localtime_r(timep,result);
    _pi_retptr_check(ret,ret);
    return ret;
}

size_t _pi_strftime(const char *callfile ,const uint32_t callline, char *s, size_t max, const char *format, const struct tm *tm)
{
	size_t ret = 0 ;
    _pi_input_check(s&&tm&&max);
    ret = strftime(s,max,format,tm);
    return ret;
}

time_t _pi_time(const char *callfile ,const uint32_t callline, time_t *tloc)
{
	time_t ret;
    ret = time(tloc);
    _pi_ret_check_err(ret!=-1);
    return ret;
}

time_t _pi_mktime(const char *callfile ,const uint32_t callline, struct tm *tm)
{
	time_t ret;

    _pi_input_check(tm);
	ret = mktime(tm);
	_pi_ret_check_err(ret!=-1);
	return ret;
}

clock_t _pi_clock(const char *callfile ,const uint32_t callline)
{
	clock_t ret;

	ret = clock();
	_pi_ret_check_err(ret!=-1);
	return ret;
}

int32_t _pi_clock_gettime(const char *callfile ,const uint32_t callline, clockid_t clk_id, struct timespec *tp)
{
	int ret = 0;

	_pi_input_check(tp);
	ret = clock_gettime(clk_id,tp);
	_pi_retint_check_err(ret!=-1,ret);
	return ret;
}

int32_t _pi_clock_settime(const char *callfile ,const uint32_t callline, clockid_t clk_id, const struct timespec *tp)
{
	int ret = 0;

	_pi_input_check(tp);
	ret = clock_settime(clk_id,tp);
	_pi_retint_check_err(ret!=-1,ret);
	return ret;
}

int32_t _pi_msleep(const char *callfile ,const uint32_t callline, uint16_t msec)
{
    struct timespec ts;
	int32_t ret =0 ;

	_pi_input_check(msec<1000);
    ts.tv_sec  = msec / 1000;
    ts.tv_nsec = msec % 1000 * 1000000;
    do
    {
        ret = nanosleep(&ts, &ts);
    }
    while (ret == -1 && errno == EINTR);
    _pi_retint_check_err(ret!=-1,ret);

	return ret;
}

int32_t _pi_usleep(const char *callfile ,const uint32_t callline, uint32_t usec)
{
    struct timespec ts;
	int32_t ret =0 ;

	_pi_input_check(usec<1000);
    ts.tv_sec  = usec / 1000000;
    ts.tv_nsec = usec % 1000000 * 1000;
    do
    {
        ret = nanosleep(&ts, &ts);
    }
    while (ret == -1 && errno == EINTR);
	_pi_retint_check_err(ret!=-1, ret);
	return ret;
}

int32_t _pi_nanosleep(const char *callfile ,const uint32_t callline, const struct timespec *req, struct timespec *rem)
{
	_pi_input_check(req&&rem);
	int32_t ret = nanosleep(req, rem);
	_pi_retint_check_err(ret!=-1, ret);
	return ret;
}
#endif
#else

int32_t pi_msleep(uint32_t msec)
{
    struct timespec ts;
	int32_t ret =0 ;

    ts.tv_sec  = msec / 1000;
    ts.tv_nsec = msec % 1000 * 1000000;
    do
    {
        ret = nanosleep(&ts, &ts);
    }
    while (ret == -1 && errno == EINTR);

	return ret;
}

int32_t pi_usleep(uint32_t usec)
{
    struct timespec ts;
	int32_t ret =0 ;

    ts.tv_sec  = usec / 1000000;
    ts.tv_nsec = usec % 1000000 * 1000;
    do
    {
        ret = nanosleep(&ts, &ts);
    }
    while (ret == -1 && errno == EINTR);

	return ret;
}

#endif

