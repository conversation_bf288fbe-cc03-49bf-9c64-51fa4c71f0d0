/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  image_process_common.h
* @addtogroup ipm
*
* @{
* @addtogroup  image_prossing_system
* <AUTHOR> (<EMAIL>)
* @date  2021-12-25
* @version  v1.0
* @brief  image processing system API
**/


#ifndef IMAGE_PROCESS_COMMON_H
#define IMAGE_PROCESS_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include <unistd.h>
#include "pol/pol_log.h"
// module header file

//	end

/**
 * @brief extern g_ipm_log_level
 */
extern int g_ipm_log_level;


#define DEFAULT_LEVEL	1       ///< defualt log level
#define HIGH_LEVEL	    -3      ///< high log level
#define MIDDLE_LEVEL	-2      ///< middle log level
#define LOW_LEVEL	    -1      ///< low log level
#define LOG_OFF	        0       ///< log off level
#define LOG_ON	        1       ///< log on level

#define FOREVER	       -4       ///< forever log on level

#define     Log(a,fmt,...)      pi_log_i(fmt,##__VA_ARGS__) ///< information log print
#define     Error(fmt,...)      pi_log_w(fmt,##__VA_ARGS__) ///< error log print

#if 0
#define Log(a,fmt,...) printf("[%s]: "fmt,__FUNCTION__,##__VA_ARGS__)
#define Error(fmt,...)  Log(DEFAULT_LEVEL,fmt,##__VA_ARGS__)
#endif
/*
#define Log(a,...)  do{ \
                        if ( (g_ipm_log_level != LOG_OFF) && (a >= g_ipm_log_level) ) \
                        {\
                            LOG(a,##__VA_ARGS__); \
                        }\
                    }while(0)*/
/*#define Log(a,...)  do{ \
                        if ( (a == FOREVER) || ((g_ipm_log_level != LOG_OFF) && (a >= g_ipm_log_level)) ) \
                        {\
                            LOG(a,##__VA_ARGS__); \
                        }\
                    }while(0)
#define Error ERROR
*/

/**
 * @brief step information out
 */
#define STEP_IMAGE_INFO_OUT(PSTEP) do{ \
                                    Log(FOREVER,"step(%s) is dealing with page -> imgin  : %s w %d h %d s %d d %d f %x\n",PSTEP->step_name,FMT_PAGE_IS_BACK(PSTEP->imgin.page_type)?"BACK":"FRONT",PSTEP->imgin.w,PSTEP->imgin.h,PSTEP->imgin.s,PSTEP->imgin.d,PSTEP->imgin.f);\
                                    Log(FOREVER,"step(%s) is dealing with page -> imgout : %s w %d h %d s %d d %d f %x\n",PSTEP->step_name,FMT_PAGE_IS_BACK(PSTEP->imgout.page_type)?"BACK":"FRONT",PSTEP->imgout.w,PSTEP->imgout.h,PSTEP->imgout.s,PSTEP->imgout.d,PSTEP->imgout.f);\
                                 }while(0)

/**
 * @brief current function out
 */
#define CURRENT_FUNCTION(BUF,STRING) do{ \
                                    snprintf(BUF, sizeof(BUF), "%s:%d %s",__FUNCTION__,__LINE__,STRING);\
                                 }while(0)


#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* IMAGE_PROCESS_COMMON_H */

/**
* @}
**/
