/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_list.h
 * @addtogroup list
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal list interface set
 */
#ifndef _LINUX_LIST_H
#define _LINUX_LIST_H

#include <pol/pol_define.h>

PT_BEGIN_DECLS

struct list_head {
	struct list_head *next, *prev;
};

struct hlist_head {
	struct hlist_node *first;
};

struct hlist_node {
	struct hlist_node *next, **pprev;
};

#define LIST_POISON1  ((void *) 0x00100100)
#define LIST_POISON2  ((void *) 0x00200200)

#ifndef offsetof
#define offsetof(TYPE, MEMBER) ((size_t) &((TYPE *)0)->MEMBER)
#endif

#ifndef container_of


/**
 * @brief cast a member of a structure out to the containing structure
 * @param[in] ptr the pointer to the member.
 * @param[in] the type of the container struct this is embedded in.
 * @param[in] the name of the member within the struct.
 */
#define container_of(ptr, type, member) ({			\
	const typeof(((type *)0)->member) * __mptr = (ptr);	\
	(type *)((char *)__mptr - offsetof(type, member)); })
#endif


/**
 * @brief Simple doubly linked list implementation.
 * Some of the internal functions ("__xxx") are useful when
 * manipulating whole lists rather than single entries, as
 * sometimes we already know the next/prev entries and we can
 * generate better code by using them directly rather than
 * using the generic single-entry routines.
 */
#define PI_LIST_HEAD_INIT(name) { &(name), &(name) }

#define PI_LIST_HEAD(name) \
	struct list_head name = PI_LIST_HEAD_INIT(name)


/**
 * @brief initializes the specified 'pi_list_head' structure,setting it to an empty list
 * @param[in] list pointer to the 'pi_list_head' structure to be initialized
 */
static inline void pi_init_list_head(struct list_head *list)
{
	list->next = list;
	list->prev = list;
}

/**
 * @brief Insert a new entry between two known consecutive entries.
 * This is only for internal list manipulation where we know
 * the prev/next entries already!
 * @param[in] entry node to be inserted
 * @param[in] prev Previous node
 * @param[in] next Next node
 */
#ifndef CONFIG_DEBUG_LIST
static inline void __list_add(struct list_head *entry,
			      struct list_head *prev,
			      struct list_head *next)
{
	next->prev = entry;
	entry->next = next;
	entry->prev = prev;
	prev->next = entry;
}
#else
extern void __list_add(struct list_head *entry,
			      struct list_head *prev,
			      struct list_head *next);
#endif

/**
 * @brief add a new entry
 * Insert a new entry after the specified head.
 * This is good for implementing stacks.
 * @param[in] entry new entry to be added
 * @param[in] head list head to add it after
 */
static inline void pi_list_add_head(struct list_head *entry, struct list_head *head)
{
	__list_add(entry, head, head->next);
}

/**
 * @brief add a new entry from tail
 * Insert a new entry before the specified head.
 * This is useful for implementing queues.
 * @param[in] entry new entry to be added
 * @param[in] head list head to add it before
 */
static inline void pi_list_add_tail(struct list_head *entry, struct list_head *head)
{
	__list_add(entry, head->prev, head);
}

/**
 * @brief add a new entry from tail
 * Insert a new entry before the specified head.
 * This is useful for implementing queues.
 * @param[in] entry new entry to be added
 * @param[in] head list head to add it before
 */
static inline void pi_list_add_direct(struct list_head *entry, struct list_head *before)
{
	__list_add(entry, before, before->next);
}


/**
 * @brief Delete a list entry by making the prev/next entries
 * point to each other.
 * This is only for internal list manipulation where we know
 * the prev/next entries already!
 * @param[in] prev prev entry to be deleted
 * @param[in] next next entry to be deleted
 */
static inline void __list_del(struct list_head * prev, struct list_head * next)
{
	next->prev = prev;
	prev->next = next;
}

/**
 * @brief Delete a linked list
 * @param[in] entry entry to be deleted
 */
#ifndef CONFIG_DEBUG_LIST
static inline void __list_del_entry(struct list_head *entry)
{
	__list_del(entry->prev, entry->next);
}
#else
extern void __list_del_entry(struct list_head *entry);
#endif

/**
 * @brief deletes entry from list.
 * Note: pi_list_empty() on entry does not return true after this, the entry is
 * in an undefined state.
 * @param[in] entry the element to delete from the list.
 */
static inline void pi_list_del_entry(struct list_head *entry)
{
	__list_del(entry->prev, entry->next);
	entry->next = (struct list_head *)LIST_POISON1;
	entry->prev = (struct list_head *)LIST_POISON2;
}

/**
 * @brief delete the next node after the head node.
 * @param[in] head list head node
 */
static inline struct list_head *pi_list_del_head(struct list_head *head)
{
	struct list_head *tmp = head->next;
	if (tmp == head) {
		return (struct list_head *)0;
	}
	pi_list_del_entry(tmp);
	return tmp;
}

/**
 * @brief delete the next node before the head node.
 * @param[in] head list head node
 */
static inline struct list_head *pi_list_del_tail(struct list_head *head)
{
	struct list_head *tmp = head->prev;
	if (tmp == head) {
		return (struct list_head *)0;
	}
	pi_list_del_entry(tmp);
	return tmp;
}

/**
 * @brief delete the specified node
 * @param[in] entry entry node to be inserted
 */
static inline void pi_list_del_direct(struct list_head *entry)
{
	__list_del(entry->prev, entry->next);
}


/**
 * @brief replace old entry by new one
 * If @old was empty, it will be overwritten.
 * @param[in] old the element to be replaced
 * @param[in] entry the element to be replaced
 */
static inline void pi_list_replace(struct list_head *old,
				struct list_head *entry)
{
	entry->next = old->next;
	entry->next->prev = entry;
	entry->prev = old->prev;
	entry->prev->next = entry;
}

/**
 * @brief replace the specified 'old' list element with the entry element and initializes the 'entry' element to an empty list.
 * @param[in] old the element to be replaced
 * @param[in] entry the element to be replaced
 */
static inline void pi_list_replace_init(struct list_head *old,
					struct list_head *entry)
{
	pi_list_replace(old, entry);
	pi_init_list_head(old);
}

/**
 * @brief deletes entry from list and reinitialize it.
 * @param[in] entry the element to delete from the list.
 */
static inline void pi_list_del_init(struct list_head *entry)
{
	__list_del_entry(entry);
	pi_init_list_head(entry);
}

/**
 * @brief delete from one list and add as another's head
 * @param[in] list the entry to move
 * @param[in] head the head that will precede our entry
 */
static inline void pi_list_move(struct list_head *list, struct list_head *head)
{
	__list_del_entry(list);
	pi_list_add_head(list, head);
}

/**
 * @brief delete from one list and add as another's tail
 * @param[in] list the entry to move
 * @param[in] head the head that will follow our entry
 */
static inline void pi_list_move_tail(struct list_head *list,
				  struct list_head *head)
{
	__list_del_entry(list);
	pi_list_add_tail(list, head);
}

/**
 * @brief tests whether @list is the last entry in list @head
 * @param[in] list the entry to test
 * @param[in] head the head of the list
 */
static inline int pi_list_is_last(const struct list_head *list,
				const struct list_head *head)
{
	return list->next == head;
}

/**
 * @brief tests whether a list is empty
 * @param[in] head the list to test.
 */
static inline int pi_list_empty(const struct list_head *head)
{
	return head->next == head;
}

/**
 * @brief tests whether a list is empty and not being modified
 * Description:
 * tests whether a list is empty _and_ checks that no other CPU might be
 * in the process of modifying either member (next or prev)
 * NOTE: using pi_list_empty_careful() without synchronization
 * can only be safe if the only activity that can happen
 * to the list entry is pi_list_del_init(). Eg. it cannot be used
 * if another CPU could re-pi_list_add() it.
 * @param[in] head the list to test.
 */
static inline int pi_list_empty_careful(const struct list_head *head)
{
	struct list_head *next = head->next;
	return (next == head) && (next == head->prev);
}


/**
 * @brief rotate the list to the left
 * @param[in] head the list to test.
 */
static inline void pi_list_rotate_left(struct list_head *head)
{
	struct list_head *first;

	if (!pi_list_empty(head)) {
		first = head->next;
		pi_list_move_tail(first, head);
	}
}


/**
 * @brief tests whether a list has just one entry.
 * @param[in] head the list to test.
 */
static inline int pi_list_is_singular(const struct list_head *head)
{
	return !pi_list_empty(head) && (head->next == head->prev);
}


/**
 * @brief cuts a portion of the list specified by 'list' starting from the 'entry' node and splices it into the list before the position pointer to by 'head'.
 * @param[in] list pointer to the list from which a portion will be cut and spliced
 * @param[in] head pointer to the position in the existing list where the cut portion will be spliced
 * @param[in] entry pointer the node in 'list' that serves as the starting point for cutting the portion
 */
static inline void __list_cut_position(struct list_head *list,
		struct list_head *head, struct list_head *entry)
{
	struct list_head *new_first = entry->next;
	list->next = head->next;
	list->next->prev = list;
	list->prev = entry;
	entry->next = list;
	head->next = new_first;
	new_first->prev = head;
}


/**
 * @brief cut a list into two
 * This helper moves the initial part of @head, up to and
 * including @entry, from @head to @list. You should
 * pass on @entry an element you know is on @head. @list
 * should be an empty list or a list you do not care about
 * losing its data.
 * @param[in] list a new list to add all removed entries
 * @param[in] head a list with entries
 * @param[in] entry an entry within head, could be the head itself and if so we won't cut the list
 */
static inline void pi_list_cut_position(struct list_head *list,
		struct list_head *head, struct list_head *entry)
{
	if (pi_list_empty(head))
		return;
	if (pi_list_is_singular(head) &&
		(head->next != entry && head != entry))
		return;
	if (entry == head)
		pi_init_list_head(list);
	else
		__list_cut_position(list, head, entry);
}

/**
 * @brief join two lists, this is designed for stacks
 * @param[in] list the new list to add.
 * @param[in] head the place to add it in the first list.
 */
static inline void __list_splice(const struct list_head *list,
				 struct list_head *prev,
				 struct list_head *next)
{
	struct list_head *first = list->next;
	struct list_head *last = list->prev;

	first->prev = prev;
	prev->next = first;

	last->next = next;
	next->prev = last;
}

/**
 * @brief join two lists, this is designed for stacks
 * @param[in] list the new list to add.
 * @param[in] head the place to add it in the first list.
 */
static inline void pi_list_splice(const struct list_head *list,
				struct list_head *head)
{
	if (!pi_list_empty(list))
		__list_splice(list, head, head->next);
}


/**
 * @brief join two lists, each list being a queue
 * @param[in] list the new list to add.
 * @param[in] head the place to add it in the tail list.
 */
static inline void pi_list_splice_tail(struct list_head *list,
				struct list_head *head)
{
	if (!pi_list_empty(list))
		__list_splice(list, head->prev, head);
}

/**
 * @brief join two lists and reinitialise the emptied list.
 * The list at @list is reinitialised
 * @param[in] list the new list to add.
 * @param[in] the place to add it in the first list.
 */
static inline void pi_list_splice_init(struct list_head *list,
				    struct list_head *head)
{
	if (!pi_list_empty(list)) {
		__list_splice(list, head, head->next);
		pi_init_list_head(list);
	}
}

/**
 * @brief join two lists and reinitialise the emptied list
 * Each of the lists is a queue.
 * The list at @list is reinitialised
 * @param[in] list the new list to add.
 * @param[in] the place to add it in the tail list.
 */
static inline void pi_list_splice_tail_init(struct list_head *list,
					 struct list_head *head)
{
	if (!pi_list_empty(list)) {
		__list_splice(list, head->prev, head);
		pi_init_list_head(list);
	}
}

/**
 * @brief get the struct for this entry
 * @param[in] ptr the &struct list_head pointer.
 * @param[in] type the type of the struct this is embedded in.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_entry(ptr, type, member) \
	container_of(ptr, type, member)


/**
 * @brief get the first element from a list
 * Note, that list is expected to be not empty.
 * @param[in] ptr the list head to take the element from.
 * @param[in] type the type of the struct this is embedded in.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_first_entry(ptr, type, member) \
	pi_list_entry((ptr)->next, type, member)


/**
 * @brief get the last element from a list
 * Note, that list is expected to be not empty.
 * @param[in] ptr the list head to take the element from.
 * @param[in] type the type of the struct this is embedded in.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_last_entry(ptr, type, member) \
	pi_list_entry((ptr)->prev, type, member)


/**
 * @brief get the first element from a list
 * Note that if the list is empty, it returns NULL.
 * @param[in] ptr the list head to take the element from.
 * @param[in] type the type of the struct this is embedded in.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_first_entry_or_null(ptr, type, member) \
	(!pi_list_empty(ptr) ? pi_list_first_entry(ptr, type, member) : NULL)

/**
 * @brief get the next element in list
 * @param[in] pos the type * to cursor
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_next_entry(pos, member) \
	pi_list_entry((pos)->member.next, typeof(*(pos)), member)

/**
 * @brief get the prev element in list
 * @param[in] pos the type * to cursor
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_prev_entry(pos, member) \
	pi_list_entry((pos)->member.prev, typeof(*(pos)), member)

/**
 * pi_list_for_each	-	iterate over a list
 * @pos:	the &struct list_head to use as a loop cursor.
 * @head:	the head for your list.
 */

/**
 * @brief iterate over a list
 * @param[in] pos the &struct list_head to use as a loop cursor.
 * @param[in] head the head for your list.
 */
#define pi_list_for_each(pos, head) \
	for (pos = (head)->next; pos != (head); pos = pos->next)

/**
 * @brief iterate over a list backwards
 * @param[in] pos the &struct list_head to use as a loop cursor.
 * @param[in] head the head for your list.
 */
#define pi_list_for_each_prev(pos, head) \
	for (pos = (head)->prev; pos != (head); pos = pos->prev)


/**
 * @brief iterate over a list safe against removal of list entry
 * @param[in] pos the &struct list_head to use as a loop cursor.
 * @param[in] n another &struct list_head to use as temporary storage
 * @param[in] head the head for your list.
 */
#define pi_list_for_each_safe(pos, n, head) \
	for (pos = (head)->next, n = pos->next; pos != (head); \
		pos = n, n = pos->next)


/**
 * @brief iterate over a list backwards safe against removal of list entry
 * @param[in] pos the &struct list_head to use as a loop cursor.
 * @param[in] n another &struct list_head to use as temporary storage
 * @param[in] head the head for your list.
 */
#define pi_list_for_each_prev_safe(pos, n, head) \
	for (pos = (head)->prev, n = pos->prev; \
	     pos != (head); \
	     pos = n, n = pos->prev)


/**
 * @brief iterate over list of given type
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry(pos, head, member)				\
	for (pos = pi_list_first_entry(head, typeof(*pos), member);	\
	     &pos->member != (head);					\
	     pos = pi_list_next_entry(pos, member))

/**
 * @brief iterate backwards over list of given type.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_reverse(pos, head, member)			\
	for (pos = pi_list_last_entry(head, typeof(*pos), member);		\
	     &pos->member != (head); 					\
	     pos = pi_list_prev_entry(pos, member))

/**
 * @brief prepare a pos entry for use in pi_list_for_each_entry_continue()
 * Prepares a pos entry for use as a start point in pi_list_for_each_entry_continue().
 * @param[in] pos the type * to use as a start point
 * @param[in] head the head of the list
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_prepare_entry(pos, head, member) \
	((pos) ? : pi_list_entry(head, typeof(*pos), member))


/**
 * @brief continue iteration over list of given type
 * Continue to iterate over list of given type, continuing after
 * the current position.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_continue(pos, head, member) 		\
	for (pos = pi_list_next_entry(pos, member);			\
	     &pos->member != (head);					\
	     pos = pi_list_next_entry(pos, member))


/**
 * @brief iterate backwards from the given point
 * Start to iterate over list of given type backwards, continuing after
 * the current position.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_continue_reverse(pos, head, member)		\
	for (pos = pi_list_prev_entry(pos, member);			\
	     &pos->member != (head);					\
	     pos = pi_list_prev_entry(pos, member))

/**
 * @brief iterate over list of given type from the current point
 * Iterate over list of given type, continuing from current position.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_from(pos, head, member) 			\
	for (; &pos->member != (head);					\
	     pos = pi_list_next_entry(pos, member))

/**
 * @brief iterate over list of given type safe against removal of list entry
 * Iterate over list of given type, continuing from current position.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] n another type * to use as temporary storage
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_safe(pos, n, head, member)			\
	for (pos = pi_list_first_entry(head, typeof(*pos), member),	\
		n = pi_list_next_entry(pos, member);			\
	     &pos->member != (head); 					\
	     pos = n, n = pi_list_next_entry(n, member))

/**
 * @brief continue list iteration safe against removal
 * Iterate over list of given type, continuing after current point,
 * safe against removal of list entry.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] n another type * to use as temporary storage
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_safe_continue(pos, n, head, member) 		\
	for (pos = pi_list_next_entry(pos, member), 				\
		n = pi_list_next_entry(pos, member);				\
	     &pos->member != (head);						\
	     pos = n, n = pi_list_next_entry(n, member))

/**
 * @brief iterate over list from current point safe against removal
 * Iterate over list of given type from current point, safe against
 * removal of list entry.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] n another type * to use as temporary storage
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_safe_from(pos, n, head, member) 			\
	for (n = pi_list_next_entry(pos, member);					\
	     &pos->member != (head);						\
	     pos = n, n = pi_list_next_entry(n, member))

/**
 * @brief iterate backwards over list safe against removal
 * Iterate backwards over list of given type, safe against removal
 * of list entry.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] n another type * to use as temporary storage
 * @param[in] head the head for your list.
 * @param[in] member the name of the list_head within the struct.
 */
#define pi_list_for_each_entry_safe_reverse(pos, n, head, member)		\
	for (pos = pi_list_last_entry(head, typeof(*pos), member),		\
		n = pi_list_prev_entry(pos, member);			\
	     &pos->member != (head); 					\
	     pos = n, n = pi_list_prev_entry(n, member))

/**
 * @brief reset a stale pi_list_for_each_entry_safe loop
 * pi_list_safe_reset_next is not safe to use in general if the list may be
 * modified concurrently (eg. the lock is dropped in the loop body). An
 * exception to this is if the cursor element (pos) is pinned in the list,
 * and pi_list_safe_reset_next is called after re-taking the lock and before
 * completing the current iteration of the loop body.
 * @param[in] pos the loop cursor used in the pi_list_for_each_entry_safe loop
 * @param[in] n temporary storage used in pi_list_for_each_entry_safe
 * @param[in] member the name of the list_head within the struct.
 */
#define list_safe_reset_next(pos, n, member)				\
	n = pi_list_next_entry(pos, member)


/**
 * @brief linked lists with a single pointer list head.
 * Mostly useful for hash tables where the two pointer list head is
 * too wasteful.
 * You lose the ability to access the tail in O(1).
 */
#define HLIST_HEAD_INIT { .first = NULL }
#define HLIST_HEAD(name) struct hlist_head name = {  .first = NULL }
#define INIT_HLIST_HEAD(ptr) ((ptr)->first = NULL)
static inline void INIT_HLIST_NODE(struct hlist_node *h)
{
	h->next = NULL;
	h->pprev = NULL;
}

/**
 * @brief checks if the specified node is unhashed,i.e,not currently part of any hash linked list
 * @param[in] n the pointer to the node to be checked
 */
static inline int hlist_unhashed(const struct hlist_node *h)
{
	return !h->pprev;
}

/**
 * @brief checks if the specified hash linked list is empty
 * @param[in] n the pointer to the head of the hash linked list
 */
static inline int hlist_empty(const struct hlist_head *h)
{
	return !h->first;
}

/**
 * @brief Deletes the specified node from the hash linked list
 * @param[in] n the pointer to the node to be deleted from the hash linked list
 */
static inline void __hlist_del(struct hlist_node *n)
{
	struct hlist_node *next = n->next;
	struct hlist_node **pprev = n->pprev;
	*pprev = next;
	if (next)
		next->pprev = pprev;
}

/**
 * @brief Deletes the specified node from the hash linked list and initializes it.
 * @param[in] n The node to be deleted.
 */
static inline void hlist_del(struct hlist_node *n)
{
	__hlist_del(n);
	n->next = (struct hlist_node *)LIST_POISON1;
	n->pprev = (struct hlist_node **)LIST_POISON2;
}

/**
 * @brief Deletes the specified node from the hash linked list and initializes it.
 * @param[in] n The pointer to the node to be deleted from the hash linked list.
 */
static inline void hlist_del_init(struct hlist_node *n)
{
	if (!hlist_unhashed(n)) {
		__hlist_del(n);
		INIT_HLIST_NODE(n);
	}
}

/**
 * @brief Adds the new node to the beginning of the linked list represented by head
 * Iterate over list of given type, continuing from current position.
 * @param[in] n The new node to be added
 * @param[in] h The head node of the linked list
 */
static inline void hlist_add_head(struct hlist_node *n, struct hlist_head *h)
{
	struct hlist_node *first = h->first;
	n->next = first;
	if (first)
		first->pprev = &n->next;
	h->first = n;
	n->pprev = &h->first;
}

/**
 * @brief Inserts the specified node before the given node in the hash linked list.
 * next node must be != NULL.
 * @param[in] n The pointer to the node to be inserted.
 * @param[in] next The pointer to the node that will be the previous node before the inserted node.
 */
static inline void hlist_add_before(struct hlist_node *n,
					struct hlist_node *next)
{
	n->pprev = next->pprev;
	n->next = next;
	next->pprev = &n->next;
	*(n->pprev) = n;
}

/**
 * @brief Inserts the specified node behind the given node in the hash linked list.
 * @param[in] n The pointer to the node to be inserted
 * @param[in] prev The pointer to the node that will be the next node after the inserted node.
 */
static inline void hlist_add_behind(struct hlist_node *n,
				    struct hlist_node *prev)
{
	n->next = prev->next;
	prev->next = n;
	n->pprev = &prev->next;

	if (n->next)
		n->next->pprev  = &n->next;
}

/**
 * @brief Inserts a "fake" node into the linked list represented by head before the first real node.
 * after that we'll appear to be on some hlist and hlist_del will work
 * @param[in] n The pointer to the fake node to be inserted.
 */
static inline void hlist_add_fake(struct hlist_node *n)
{
	n->pprev = &n->next;
}

 /**
 * @brief Moves the entire linked list from old to new, effectively merging the lists.
 * Move a list from one list head to another. Fixup the pprev
 * reference of the first entry if it exists.
 * @param[in] old The pointer to the head of the source linked list that will be moved.
 * @param[in] entry The pointer to the head of the destination linked list where the source list will be moved to.
 */
static inline void hlist_move_list(struct hlist_head *old,
				   struct hlist_head *entry)
{
	entry->first = old->first;
	if (entry->first)
		entry->first->pprev = &entry->first;
	old->first = NULL;
}

#define hlist_entry(ptr, type, member) container_of(ptr,type,member)

#define hlist_for_each(pos, head) \
	for (pos = (head)->first; pos ; pos = pos->next)

#define hlist_for_each_safe(pos, n, head) \
	for (pos = (head)->first; pos && ({ n = pos->next; 1; }); \
	     pos = n)

#define hlist_entry_safe(ptr, type, member) \
	({ typeof(ptr) ____ptr = (ptr); \
	   ____ptr ? hlist_entry(____ptr, type, member) : NULL; \
	})

/**
 * @brief iterate over list of given type
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] head the head for your list.
 * @param[in] member the name of the hlist_node within the struct.
 */
#define hlist_for_each_entry(pos, head, member)				\
	for (pos = hlist_entry_safe((head)->first, typeof(*(pos)), member);\
	     pos;							\
	     pos = hlist_entry_safe((pos)->member.next, typeof(*(pos)), member))

/**
 * @brief iterate over a hlist continuing after current point
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] member the name of the hlist_node within the struct.
 */
#define hlist_for_each_entry_continue(pos, member)			\
	for (pos = hlist_entry_safe((pos)->member.next, typeof(*(pos)), member);\
	     pos;							\
	     pos = hlist_entry_safe((pos)->member.next, typeof(*(pos)), member))


/**
 * @brief iterate over a hlist continuing from current point
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] member the name of the hlist_node within the struct.
 */
#define hlist_for_each_entry_from(pos, member)				\
	for (; pos;							\
	     pos = hlist_entry_safe((pos)->member.next, typeof(*(pos)), member))


/**
 * @brief iterate over list of given type safe against removal of list entry
 * Iterate over list of given type, continuing from current position.
 * @param[in] pos the type * to use as a loop cursor.
 * @param[in] n another type * to use as temporary storage
 * @param[in] head the head for your list.
 * @param[in] member the name of the hlist_node within the struct.
 */
#define hlist_for_each_entry_safe(pos, n, head, member) 		\
	for (pos = hlist_entry_safe((head)->first, typeof(*pos), member);\
	     pos && ({ n = pos->member.next; 1; });			\
	     pos = hlist_entry_safe(n, typeof(*pos), member))

/**
 * @brief Deletes the specified item from the PI (Priority Inheritance) list.
 * @param[in] head the head for your list.
 * @param[in] entry The pointer to the item to be deleted from the PI list.
 */
static inline unsigned int pi_list_del_item(struct list_head *head, struct list_head *entry)
{
	struct list_head *pos, *n;
	pi_list_for_each_safe(pos, n, head)
	{
		if (pos == entry)
		{
			pi_list_del_entry(entry);
			return (1);
		}
	}
	return (0);
}

PT_END_DECLS

#endif
/**
 *@}
 */
