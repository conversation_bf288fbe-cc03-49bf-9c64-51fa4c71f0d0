/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_mgr_typedef.h
 * @addtogroup event_manager
 * @{
 * <AUTHOR> Xin
 * @date 2023-07-13
 * @brief event manager typedef
 */
#ifndef EVENT_MGR_TYPEDEF_H
#define EVENT_MGR_TYPEDEF_H

#include <stdlib.h>

#include "pol/pol_types.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

#define ERR_EVT_MGR_BASE                            (-1000)
#define ERR_EVT_MGR_INVALID_PARAM                   (ERR_EVT_MGR_BASE - 1)
#define ERR_EVT_MGR_MODULE_EXIST                    (ERR_EVT_MGR_BASE - 2)
#define ERR_EVT_MGR_MEMORY                          (ERR_EVT_MGR_BASE - 3)
#define ERR_EVT_MGR_SOCKETPAIR                      (ERR_EVT_MGR_BASE - 4)
#define ERR_EVT_MGR_BINDER_INIT                     (ERR_EVT_MGR_BASE - 5)
#define ERR_EVT_MGR_BINDER_CALL                     (ERR_EVT_MGR_BASE - 6)
#define ERR_EVT_MGR_INVALID_MSG                     (ERR_EVT_MGR_BASE - 7)
#define ERR_EVT_MGR_UNSUPPORD_REGISTER              (ERR_EVT_MGR_BASE - 8)

#define EVT_LEVEL_DEFAULT                           (0x03)

#define EVT_TAG_NORMAL                              (0x00 | EVT_LEVEL_DEFAULT)
#define EVT_TAG_OWN                                 (0x10 | EVT_LEVEL_DEFAULT)

#define EVT_MODULE(e)                               ((uint8_t)((e) >> 24))

#define EVT_IS_OWN(e)                               ((uint8_t)((e) >> 20) & 0x0F)

#define EVT_LEVEL(e)                                ((uint8_t)((e) >> 16) & 0x0F)

#define EVT_INDEX(e)                                ((uint16_t)(e))

/* build:  [moduleID: 8bit(24~31)] | [tag: [is_own: 4bit(20~23)] | [priority: 4bit(16~19)]] | [index: 16bit(0~15)] */
#define EVT_MAKE(evt_mod, evt_tag, evt_idx)         (uint32_t)(((uint8_t)evt_mod << 24) | ((uint8_t)evt_tag  << 16) | ((uint16_t)evt_idx << 0 ))

#define EVENT_MODULE_TABLE(out)                                       \
    out( EVT_MODULE_DEBUG        , 0x00 , EVT_TYPE_DEBUG_MAX        ) \
    out( EVT_MODULE_PANEL        , 0x01 , EVT_TYPE_PANEL_MAX        ) \
    out( EVT_MODULE_PRINT        , 0x02 , EVT_TYPE_PRINT_MAX        ) \
    out( EVT_MODULE_SCAN         , 0x03 , EVT_TYPE_SCAN_MAX         ) \
    out( EVT_MODULE_COPY         , 0x04 , EVT_TYPE_COPY_MAX         ) \
    out( EVT_MODULE_NET          , 0x05 , EVT_TYPE_NET_MAX          ) \
    out( EVT_MODULE_SYSTEMSTATUS , 0x06 , EVT_TYPE_SYSTEMSTATUS_MAX ) \
    out( EVT_MODULE_SYSTEMJOB    , 0x07 , EVT_TYPE_SYSTEMJOB_MAX    ) \
    out( EVT_MODULE_PLATFORM     , 0x08 , EVT_TYPE_PLATFORM_MAX     ) \
    out( EVT_MODULE_STORAGE_MGR  , 0x09 , EVT_TYPE_STORAGEMGR_MAX   ) \
    out( EVT_MODULE_POWER_MGR    , 0x0a , EVT_TYPE_POWERMGR_MAX     ) \
    out( EVT_MODULE_POWEROFF_MGR , 0x0b , EVT_TYPE_POWEROFFMGR_MAX  ) \
    out( EVT_MODULE_USBDEVICE    , 0x0c , EVT_TYPE_USBDEVICE_MAX    ) \
    out( EVT_MODULE_USBHOST      , 0x0d , EVT_TYPE_USBHOST_MAX      ) \
    out( EVT_MODULE_UPGRADE      , 0x0e , EVT_TYPE_UPGRADE_MAX      ) \
    out( EVT_MODULE_ACL_PARSER   , 0x0f , EVT_TYPE_ACL_PARSER_MAX   ) \
    out( EVT_MODULE_SECURITY     , 0x10 , EVT_TYPE_SECURITY_MAX     ) \
    out( EVT_MODULE_PRINTERINFO  , 0x11 , EVT_TYPE_PRINTERINFO_MAX  ) \
    out( EVT_MODULE_EVENTLOG     , 0x12 , EVT_TYPE_EVENTLOG_MAX     ) \
    out( EVT_MODULE_PRNSDK       , 0x13 , EVT_TYPE_PRNSDK_MAX       ) \
    out( EVT_MODULE_PEDK_MGR     , 0x14 , EVT_TYPE_PEDKMGR_MAX      ) \
    out( EVT_MODULE_PRINTER_RES  , 0x15 , EVT_TYPE_PRINTERRES_MAX   ) \

#define enum_out(mid, num, max)   mid = num,
typedef enum { EVENT_MODULE_TABLE(enum_out) EVT_MODULE_ENUM_MAX } EVT_MODULE_E;
#undef  enum_out

typedef enum
{
    EVT_TYPE_DEBUG_NUMBER                                               = EVT_MAKE(EVT_MODULE_DEBUG,            EVT_TAG_NORMAL,  0x0000), /* 0x00030000 */
    EVT_TYPE_DEBUG_BUFFER                                               = EVT_MAKE(EVT_MODULE_DEBUG,            EVT_TAG_NORMAL,  0x0001), /* 0x00030001 */
    EVT_TYPE_DEBUG_MAX,

    /**
     * @brief EventID of panel module
     */
    EVT_TYPE_PANEL_TEST                                                 = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0000), /* 0x01030000 */
    EVT_TYPE_PANEL_KEY_PRESS                                            = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0001), /* 0x01130001 */
    EVT_TYPE_PANEL_SLEEP_TIME_REQUEST                                   = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0002), /* 0x01130002 */
    EVT_TYPE_PANEL_SLEEP_TIME_MODIFY                                    = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0003), /* 0x01030003 */
    EVT_TYPE_PANEL_PRINT_DEFAULT_CONFIG_MODIFY                          = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0004), /* 0x01030004 */
    EVT_TYPE_PANEL_ADVANCED_IMAGE_CONFIG_MODIFY                         = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0005), /* 0x01030005 */
    EVT_TYPE_PANEL_SLEEP_MODE_REQUEST                                   = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0006), /* 0x01130006 */
    EVT_TYPE_PANEL_SLEEP_MODE_MODIFY                                    = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0007), /* 0x01030007 */
    EVT_TYPE_PANEL_COLOR_COPY_ENABLE_REQUEST                            = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0008), /* 0x01130008 */
    EVT_TYPE_PANEL_COLOR_COPY_ENABLE_MODIFY                             = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0009), /* 0x01030009 */
    EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_REQUEST                          = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x000a), /* 0x0113000a */
    EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_MODIFY                           = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x000b), /* 0x0103000b */
    EVT_TYPE_PANEL_LCD_BACKLIGHT_REQUEST                                = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x000c), /* 0x0113000c */
    EVT_TYPE_PANEL_LCD_BACKLIGHT_MODIFY                                 = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x000d), /* 0x0103000d */
    EVT_TYPE_PANEL_SYS_VOLUE_REQUEST                                    = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x000e), /* 0x0113000e */
    EVT_TYPE_PANEL_SYS_VOLUE_MODIFY                                     = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x000f), /* 0x0103000f */
    EVT_TYPE_PANEL_DATE_FORMAT_REQUEST                                  = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0010), /* 0x01130010 */
    EVT_TYPE_PANEL_DATE_FORMAT_MODIFY                                   = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0011), /* 0x01030011 */
    EVT_TYPE_PANEL_TIME_FORMAT_REQUEST                                  = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0012), /* 0x01130012 */
    EVT_TYPE_PANEL_TIME_FORMAT_MODIFY                                   = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0013), /* 0x01030013 */
    EVT_TYPE_PANEL_SAVE_TONER_MODE_MODIFY                               = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0014), /* 0x01030014 */
    EVT_TYPE_PANEL_ENTER_MAINTENANCE_MODIFY                             = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0015), /* 0x01030015 */
    EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_MODIFY                        = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0016), /* 0x01030016 */
    EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_MODIFY                         = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0017), /* 0x01030017 */
    EVT_TYPE_PANEL_SAFETY_MANAGEMENT_PSW_MODIFY                         = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_NORMAL,  0x0018), /* 0x01030018 */
    EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_REQUEST                       = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x0019), /* 0x01130019 */
    EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_REQUEST                        = EVT_MAKE(EVT_MODULE_PANEL,            EVT_TAG_OWN,     0x001a), /* 0x0113001a */
    EVT_TYPE_PANEL_MAX,

    /**
     * @brief EventID of print module
     */
    EVT_TYPE_PRINT_TEST                                                 = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0000), /* 0x02030000 */
    EVT_TYPE_PRINT_ENGINE_INFO                                          = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0001), /* 0x02030001 */
    EVT_TYPE_PRINT_INSTALL_INFO                                         = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0002), /* 0x02030002 */
    EVT_TYPE_PRINT_CONSUMPTION_PARAM                                    = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0003), /* 0x02030003 */
    EVT_TYPE_PRINT_CONSUMPTION_INFO                                     = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0004), /* 0x02030004 */
    EVT_TYPE_PRINT_CONSUMPTION_STATUS                                   = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0005), /* 0x02030005 */
    EVT_TYPE_PRINT_TRAY_INFO                                            = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0006), /* 0x02030006 */
    EVT_TYPE_PRINT_PROCESS_JOB_INFO                                     = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0007), /* 0x02030007 */
    EVT_TYPE_PRINT_PROCESS_PAGE_INFO                                    = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0008), /* 0x02030008 */
    EVT_TYPE_PRINT_STATISTIC_INFO                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0009), /* 0x02030009 */
    EVT_TYPE_PRINT_SET_TRAY1_SIZE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x000A), /* 0x0213000A */
    EVT_TYPE_PRINT_SET_TRAY2_SIZE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x000B), /* 0x0213000B */
    EVT_TYPE_PRINT_SET_TRAY3_SIZE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x000C), /* 0x0213000C */
    EVT_TYPE_PRINT_SET_TRAY4_SIZE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x000D), /* 0x0213000D */
    EVT_TYPE_PRINT_SET_MULTI_TRAY_SIZE                                  = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x000E), /* 0x0213000E */
    EVT_TYPE_PRINT_SET_LCT_IN_TRAY_SIZE                                 = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x000F), /* 0x0213000F */
    EVT_TYPE_PRINT_SET_LCT_OUT_TRAY_SIZE                                = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0010), /* 0x02130010 */
    EVT_TYPE_PRINT_SET_TRAY1_TYPE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0011), /* 0x02130011 */
    EVT_TYPE_PRINT_SET_TRAY2_TYPE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0012), /* 0x02130012 */
    EVT_TYPE_PRINT_SET_TRAY3_TYPE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0013), /* 0x02130013 */
    EVT_TYPE_PRINT_SET_TRAY4_TYPE                                       = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0014), /* 0x02130014 */
    EVT_TYPE_PRINT_SET_MULTI_TRAY_TYPE                                  = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0015), /* 0x02130015 */
    EVT_TYPE_PRINT_SET_LCT_IN_TRAY_TYPE                                 = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0016), /* 0x02130016 */
    EVT_TYPE_PRINT_SET_LCT_OUT_TRAY_TYPE                                = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x0017), /* 0x02130017 */
    EVT_TYPE_PRINT_STATISTIC_COPY_INFO                                  = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0018), /* 0x02030018 */
    EVT_TYPE_PRINT_FNS_CONFIG_PRINTER_RES_MOTIFY                        = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0019), /* 0x02030019 */
    EVT_TYPE_PRINT_JOB_CONTROL_REQUEST                                  = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x001a), /* 0x0213001a */
    EVT_TYPE_PRINT_JOB_CONTROL_MODIFY                                   = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x001b), /* 0x0203001b */
    EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST                         = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_OWN,     0x001c), /* 0x0213001c */
    EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_MODIFY                          = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x001d), /* 0x0203001d */
    EVT_TYPE_PRINT_GET_ENGINE_READY_MODIFY                              = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x001e), /* 0x0203001e */
    EVT_TYPE_PRINT_PAPER_JAM_COUNT_MODIFY                               = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x001f), /* 0x0203001f */
    EVT_TYPE_PRINT_CANCEL_PAGE_COUNT_MODIFY                             = EVT_MAKE(EVT_MODULE_PRINT,            EVT_TAG_NORMAL,  0x0020), /* 0x02030020 */
    EVT_TYPE_PRINT_MAX,

    /**
     * @brief EventID of scan module\n
     *        xxxx_REQUEST      means:others module set value to scan module
     *        xxxx_MOTIFY       means:scan module notify others module
     */
    EVT_TYPE_SCAN_TEST                                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0000), /* 0x03030000 */

    EVT_TYPE_SCAN_TEST__COUNT_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0001), /* 0x03030001 */
    EVT_TYPE_SCAN_TESTL_JOB_COUNT_REQUEST                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0002), /* 0x03030002 */
    EVT_TYPE_SCAN_JOB_ID_MODIFY                                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0003), /* 0x03030003 */
    //2023 scan event list
    EVT_TYPE_SCAN_RESOLUTION_MODIFY                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0004), /* 0x03030004 */
    EVT_TYPE_SCAN_RESOLUTION_REQUEST                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0005), /* 0x03030005 */
    EVT_TYPE_SCAN_SHADING_CORRECTION_MODIFY                             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0006), /* 0x03030006 */
    EVT_TYPE_SCAN_SHADING_CORRECTION_REQUEST                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0007), /* 0x03030007 */
    EVT_TYPE_SCAN_COLOR_MODIFY                                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0008), /* 0x03030008 */
    EVT_TYPE_SCAN_COLOR_REQUEST                                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0009), /* 0x03030009 */
    EVT_TYPE_SCAN_FILE_FORMAT_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x000A), /* 0x0303000A */
    EVT_TYPE_SCAN_FILE_FORMAT_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x000B), /* 0x0303000B */
    EVT_TYPE_SCAN_AREA_SIZE_MODIFY                                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x000C), /* 0x0303000C */
    EVT_TYPE_SCAN_AREA_SIZE_REQUEST                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x000D), /* 0x0303000D */
    EVT_TYPE_SCAN_GAMMA_MODIFY                                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x000E), /* 0x0303000E */
    EVT_TYPE_SCAN_GAMMA_REQUEST                                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x000F), /* 0x0303000F */
    EVT_TYPE_SCAN_BRIGHTNESS_MODIFY                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0010), /* 0x03030010 */
    EVT_TYPE_SCAN_BRIGHTNESS_REQUEST                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0011), /* 0x03030011 */
    EVT_TYPE_SCAN_CONTRAST_MODIFY                                       = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0012), /* 0x03030012 */
    EVT_TYPE_SCAN_CONTRAST_REQUEST                                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0013), /* 0x03030013 */
    EVT_TYPE_SCAN_X_NUMERATOR_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0014), /* 0x03030014 */
    EVT_TYPE_SCAN_X_NUMERATOR_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0015), /* 0x03030015 */
    EVT_TYPE_SCAN_X_DENOMINATOR_MODIFY                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0016), /* 0x03030016 */
    EVT_TYPE_SCAN_X_DENOMINATOR_REQUEST                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0017), /* 0x03030017 */
    EVT_TYPE_SCAN_Y_NUMERATOR_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0018), /* 0x03030018 */
    EVT_TYPE_SCAN_Y_NUMERATOR_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0019), /* 0x03030019 */
    EVT_TYPE_SCAN_Y_DENOMINATOR_MODIFY                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x001A), /* 0x0303001A */
    EVT_TYPE_SCAN_Y_DENOMINATOR_REQUEST                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x001B), /* 0x0303001B */
    EVT_TYPE_SCAN_SHARP_MODIFY                                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x001C), /* 0x0303001C */
    EVT_TYPE_SCAN_SHARP_REQUEST                                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x001D), /* 0x0303001D */
    EVT_TYPE_SCAN_DEPRECATED_MODIFY                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x001E), /* 0x0303001E */
    EVT_TYPE_SCAN_DEPRECATED_REQUEST                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x001F), /* 0x0303001F */
    EVT_TYPE_SCAN_BITS_PER_PIXEL_MODIFY                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0020), /* 0x03030020 */
    EVT_TYPE_SCAN_BITS_PER_PIXEL_REQUEST                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0021), /* 0x03030021 */
    EVT_TYPE_SCAN_FLAGS_MODIFY                                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0022), /* 0x03030022 */
    EVT_TYPE_SCAN_FLAGS_REQUEST                                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0023), /* 0x03030023 */
    EVT_TYPE_SCAN_DATA_TYPE_MODIFY                                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0024), /* 0x03030024 */
    EVT_TYPE_SCAN_DATA_TYPE_REQUEST                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0025), /* 0x03030025 */
    EVT_TYPE_SCAN_WINDOW_TOP_MODIFY                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0026), /* 0x03030026 */
    EVT_TYPE_SCAN_WINDOW_TOP_REQUEST                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0027), /* 0x03030027 */
    EVT_TYPE_SCAN_WINDOW_LEFT_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0028), /* 0x03030028 */
    EVT_TYPE_SCAN_WINDOW_LEFT_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0029), /* 0x03030029 */
    EVT_TYPE_SCAN_WINDOW_BOTTOM_MODIFY                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x002A), /* 0x0303002A */
    EVT_TYPE_SCAN_WINDOW_BOTTOM_REQUEST                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x002B), /* 0x0303002B */
    EVT_TYPE_SCAN_WINDOW_RIGHT_MODIFY                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x002C), /* 0x0303002C */
    EVT_TYPE_SCAN_WINDOW_RIGHT_REQUEST                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x002D), /* 0x0303002D */
    EVT_TYPE_SCAN_SCANNABLE_AREA_TOP_MODIFY                             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x002E), /* 0x0303002E */
    EVT_TYPE_SCAN_SCANNABLE_AREA_TOP_REQUEST                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x002F), /* 0x0303002F */
    EVT_TYPE_SCAN_SCANNABLE_AREA_LEFT_MODIFY                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0030), /* 0x03030030 */
    EVT_TYPE_SCAN_SCANNABLE_AREA_LEFT_REQUEST                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0031), /* 0x03030031 */
    EVT_TYPE_SCAN_SCANNABLE_AREA_BOTTOM_MODIFY                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0032), /* 0x03030032 */
    EVT_TYPE_SCAN_SCANNABLE_AREA_BOTTOM_REQUEST                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0033), /* 0x03030033 */
    EVT_TYPE_SCAN_SCANNABLE_AREA_RIGHT_MODIFY                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0034), /* 0x03030034 */
    EVT_TYPE_SCAN_SCANNABLE_AREA_RIGHT_REQUEST                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0035), /* 0x03030035 */
    EVT_TYPE_SCAN_TYPE_MODIFY                                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0036), /* 0x03030036 */
    EVT_TYPE_SCAN_TYPE_REQUEST                                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0037), /* 0x03030037 */
    EVT_TYPE_SCAN_ADF_SUPPORT_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0038), /* 0x03030038 */
    EVT_TYPE_SCAN_ADF_SUPPORT_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0039), /* 0x03030039 */
    EVT_TYPE_SCAN_FLATBED_SUPPORT_MODIFY                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x003A), /* 0x0303003A */
    EVT_TYPE_SCAN_FLATBED_SUPPORT_REQUEST                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x003B), /* 0x0303003B */
    EVT_TYPE_SCAN_DUPLEX_SUPPORT_MODIFY                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x003C), /* 0x0303003C */
    EVT_TYPE_SCAN_DUPLEX_SUPPORT_REQUEST                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x003D), /* 0x0303003D */
    EVT_TYPE_SCAN_NUM_SCANS_MODIFY                                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x003E), /* 0x0303003E */
    EVT_TYPE_SCAN_NUM_SCANS_REQUEST                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x003F), /* 0x0303003F */
    EVT_TYPE_SCAN_TARGET_HOST_NAME_0_MODIFY                             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0040), /* 0x03030040 */
    EVT_TYPE_SCAN_TARGET_HOST_NAME_0_REQUEST                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0041), /* 0x03030041 */
    EVT_TYPE_SCAN_TARGET_HOST_SCAN_PENDING_0_MODIFY                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0042), /* 0x03030042 */
    EVT_TYPE_SCAN_TARGET_HOST_SCAN_PENDING_0_REQUEST                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0043), /* 0x03030043 */
    EVT_TYPE_SCAN_TOTAL_PAGE_COUNT_MODIFY                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0044), /* 0x03030044 */
    EVT_TYPE_SCAN_TOTAL_PAGE_COUNT_REQUEST                              = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0045), /* 0x03030045 */
    EVT_TYPE_SCAN_COPY_TOTAL_FBSCAN_PAGE_COUNT_MODIFY                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0046), /* 0x03030046 */
    EVT_TYPE_SCAN_COPY_TOTAL_FBSCAN_PAGE_COUNT_REQUEST                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0047), /* 0x03030047 */
    EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_COUNT_MODIFY                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0048), /* 0x03030048 */
    EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_COUNT_REQUEST                       = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0049), /* 0x03030049 */
    EVT_TYPE_SCAN_COPY_TOTAL_ADFSCAN_PAGE_COUNT_MODIFY                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x004A), /* 0x0303004A */
    EVT_TYPE_SCAN_COPY_TOTAL_ADFSCAN_PAGE_COUNT_REQUEST                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x004B), /* 0x0303004B */
    EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_COUNT_MODIFY                       = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x004C), /* 0x0303004C */
    EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_COUNT_REQUEST                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x004D), /* 0x0303004D */
    EVT_TYPE_SCAN_TOTAL_JOB_COUNT_MODIFY                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x004E), /* 0x0303004E */
    EVT_TYPE_SCAN_TOTAL_JOB_COUNT_REQUEST                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x004F), /* 0x0303004F */
    EVT_TYPE_SCAN_FTP_ADDR_OP_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0050), /* 0x03030050 */
    EVT_TYPE_SCAN_FTP_ADDR_OP_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0051), /* 0x03030051 */
    EVT_TYPE_SCAN_MAIL_ADDR_OP_MODIFY                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0052), /* 0x03030052 */
    EVT_TYPE_SCAN_MAIL_ADDR_OP_REQUEST                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0053), /* 0x03030053 */
    EVT_TYPE_SCAN_GROUP_ADDR_OP_MODIFY                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0054), /* 0x03030054 */
    EVT_TYPE_SCAN_GROUP_ADDR_OP_REQUEST                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0055), /* 0x03030055 */
    EVT_TYPE_SCAN_FTP_ADDR_ARRAY_MODIFY                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0056), /* 0x03030056 */
    EVT_TYPE_SCAN_FTP_ADDR_ARRAY_REQUEST                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0057), /* 0x03030057 */
    EVT_TYPE_SCAN_MAIL_ADDR_ARRAY_MODIFY                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0058), /* 0x03030058 */
    EVT_TYPE_SCAN_MAIL_ADDR_ARRAY_REQUEST                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0059), /* 0x03030059 */
    EVT_TYPE_SCAN_MAIL_GROUP_ARRAY_MODIFY                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x005A), /* 0x0303005A */
    EVT_TYPE_SCAN_MAIL_GROUP_ARRAY_REQUEST                              = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x005B), /* 0x0303005B */
    EVT_TYPE_SCAN_GAMMA_CORRECTION_HAS_FB_MODIFY                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x005C), /* 0x0303005C */
    EVT_TYPE_SCAN_GAMMA_CORRECTION_HAS_FB_REQUEST                       = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x005D), /* 0x0303005D */
    EVT_TYPE_SCAN_GAMMA_CORRECTION_HAS_ADF_FRONT_MODIFY                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x005E), /* 0x0303005E */
    EVT_TYPE_SCAN_GAMMA_CORRECTION_HAS_ADF_FRONT_REQUEST                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x005F), /* 0x0303005F */
    EVT_TYPE_SCAN_GAMMA_CORRECTION_HAS_ADF_BACK_MODIFY                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0060), /* 0x03030060 */
    EVT_TYPE_SCAN_GAMMA_CORRECTION_HAS_ADF_BACK_REQUEST                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0061), /* 0x03030061 */
    EVT_TYPE_SCAN_UNIFORM_CORRECTION_600_MODIFY                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0062), /* 0x03030062 */
    EVT_TYPE_SCAN_UNIFORM_CORRECTION_600_REQUEST                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0063), /* 0x03030063 */
    EVT_TYPE_SCAN_UNIFORM_CORRECTION_300_MODIFY                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0064), /* 0x03030064 */
    EVT_TYPE_SCAN_UNIFORM_CORRECTION_300_REQUEST                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0065), /* 0x03030065 */
    EVT_TYPE_SCAN_EMPTY_MARGIN_TOP_ADF_MODIFY                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0066), /* 0x03030066 */
    EVT_TYPE_SCAN_EMPTY_MARGIN_TOP_ADF_REQUEST                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0067), /* 0x03030067 */
    EVT_TYPE_SCAN_EMPTY_MARGIN_TOP_FB_MODIFY                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0068), /* 0x03030068 */
    EVT_TYPE_SCAN_EMPTY_MARGIN_TOP_FB_REQUEST                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0069), /* 0x03030069 */
    EVT_TYPE_SCAN_EMPTY_MARGIN_LEFT_ADF_MODIFY                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x006A), /* 0x0303006A */
    EVT_TYPE_SCAN_EMPTY_MARGIN_LEFT_ADF_REQUEST                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x006B), /* 0x0303006B */
    EVT_TYPE_SCAN_EMPTY_MARGIN_LEFT_FB_MODIFY                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x006C), /* 0x0303006C */
    EVT_TYPE_SCAN_EMPTY_MARGIN_LEFT_FB_REQUEST                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x006D), /* 0x0303006D */
    EVT_TYPE_SCAN_STATUS_NOTFFY_MODIFY                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x006E), /* 0x0303006E */
    EVT_TYPE_SCAN_STATUS_NOTFFY_REQUEST                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x006F), /* 0x0303006F */

    //1/2/3 step sleep event
    EVT_TYPE_SCAN_SLEEP_MODIFY                                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0070), /* 0x03030070 */
    EVT_TYPE_SCAN_IDLE_MODIFY                                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0071), /* 0x03030071 */
    EVT_TYPE_SCAN_SLEEP_STATE_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0072), /* 0x03030072 */
    EVT_TYPE_SCAN_IDLE_STATE_MODIFY                                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0073), /* 0x03030073 */
    //xzy acl add
    EVT_TYPE_SCAN_ADF_PAPER_STATUS_NOTFFY_MODIFY                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0074), /* 0x03030074 */
    EVT_TYPE_SCAN_ADF_PAPER_STATU_NOTFFY_REQUEST                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0075), /* 0x03030075 */
    EVT_TYPE_SCAN_ADF_COVER_STATUS_NOTFFY_MODIFY                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0076), /* 0x03030076 */
    EVT_TYPE_SCAN_ADF_COVER_STATUS_NOTFFY_REQUEST                       = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0077), /* 0x03030077 */
    EVT_TYPE_SCAN_PAPER_SENSOR_NOTFFY_MODIFY                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0078), /* 0x03030078 */
    EVT_TYPE_SCAN_PAPER_SENSOR_NOTFFY_REQUEST                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0079), /* 0x03030079 */
    EVT_TYPE_SCAN_HOME_SENSOR_NOTFFY_MODIFY                             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x007A), /* 0x0303007A */
    EVT_TYPE_SCAN_HOME_SENSOR_NOTFFY_REQUEST                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x007B), /* 0x0303007B */
    EVT_TYPE_SCAN_FB_FUNCTION_SWITCH_NOTFFY_MODIFY                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x007C), /* 0x0303007C */
    EVT_TYPE_SCAN_FB_FUNCTION_SWITCH_NOTFFY_REQUEST                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x007D), /* 0x0303007D */
    EVT_TYPE_SCAN_ADF_FUNCTION_SWITCH_NOTFFY_MODIFY                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x007E), /* 0x0303007E */
    EVT_TYPE_SCAN_ADF_FUNCTION_SWITCH_NOTFFY_REQUEST                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x007F), /* 0x0303007F */
    EVT_TYPE_SCAN_FB_SCANNER_SENSOR_NOTFFY_MODIFY                       = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0080), /* 0x03030080 */
    EVT_TYPE_SCAN_FB_SCANNER_SENSOR_NOTFFY_REQUEST                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0081), /* 0x03030081 */
    EVT_TYPE_SCAN_ADF_TRANSPORT_TEST_NOTFFY_MODIFY                      = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0082), /* 0x03030082 */
    EVT_TYPE_SCAN_ADF_TRANSPORT_TEST_NOTFFY_REQUEST                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0083), /* 0x03030083 */
    EVT_TYPE_SCAN_ADF_VERTICAL_AMPLIFICATION_NOTFFY_MODIFY              = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0084), /* 0x03030084 */
    EVT_TYPE_SCAN_ADF_VERTICAL_AMPLIFICATION_NOTFFY_REQUEST             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0085), /* 0x03030085 */
    EVT_TYPE_SCAN_FB_VERTICAL_AMPLIFICATION_NOTFFY_MODIFY               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0086), /* 0x03030086 */
    EVT_TYPE_SCAN_FB_VERTICAL_AMPLIFICATION_NOTFFY_REQUEST              = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0087), /* 0x03030087 */

    /*scan engine version event*/
    EVT_TYPE_SCAN_ENGINE_VERSION_MODIFY                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0088), /* 0x03030088 */

    /*scan info page event*/
    EVT_TYPE_SCAN_COPY_TOTAL_FBSCAN_PAGE_NUM_MODIFY                     = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0089), /* 0x03030089 */
    EVT_TYPE_SCAN_COPY_TOTAL_FBSCAN_PAGE_NUM_REQUEST                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x008A), /* 0x0303008A */
    EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_NUM_MODIFY                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x008B), /* 0x0303008B */
    EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_NUM_REQUEST                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x008C), /* 0x0303008C */
    EVT_TYPE_SCAN_COPY_TOTAL_ADFSCAN_PAGE_NUM_MODIFY                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x008D), /* 0x0303008D */
    EVT_TYPE_SCAN_COPY_TOTAL_ADFSCAN_PAGE_NUM_REQUEST                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x008E), /* 0x0303008E */
    EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_NUM_MODIFY                         = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x008F), /* 0x0303008F */
    EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_NUM_REQUEST                        = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0090), /* 0x03030090 */
    EVT_TYPE_SCAN_DOUBLE_PAGE_COUNT_MODIFY                              = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0091), /* 0x03030091 */
    EVT_TYPE_SCAN_DOUBLE_PAGE_COUNT_REQUEST                             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0092), /* 0x03030092 */
    EVT_TYPE_SCAN_SOURCE_PAGE_NUM_MODIFY                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0093), /* 0x03030093 */
    EVT_TYPE_SCAN_SOURCE_PAGE_NUM_REQUEST                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0094), /* 0x03030094 */
    EVT_TYPE_SCAN_PAPER_SIZE_MONO_NUM_MODIFY                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0095), /* 0x03030095 */
    EVT_TYPE_SCAN_PAPER_SIZE_MONO_NUM_REQUEST                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0096), /* 0x03030096 */
    EVT_TYPE_SCAN_PAPER_SIZE_COLOR_NUM_MODIFY                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0097), /* 0x03030097 */
    EVT_TYPE_SCAN_PAPER_SIZE_COLOR_NUM_REQUEST                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0098), /* 0x03030098 */
    EVT_TYPE_SCAN_PAPER_SIZE_COUNT_MODIFY                               = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x0099), /* 0x03030099 */
    EVT_TYPE_SCAN_PAPER_SIZE_COUNT_REQUEST                              = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x009A), /* 0x0303009A */

    /*FB/ADF cover status event*/
    EVT_TYPE_SCAN_FB_ADF_COVER_STATUS_MODIFY                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x009B), /* 0x0303009B */
    EVT_TYPE_SCAN_FB_ADF_COVER_STATUS_REQUEST                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x009C), /* 0x0303009C */

    /*scanner maintain mode param define event*/
    EVT_TYPE_SCAN_FB_ADF_SENSOR_MODIFY                                  = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x009D), /* 0x0303009D */
    EVT_TYPE_SCAN_FB_ADF_SENSOR_REQUEST                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x009E), /* 0x0303009E */
    EVT_TYPE_SCAN_FB_ADF_SCANING_MODIFY                                 = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x009F), /* 0x0303009F */
    EVT_TYPE_SCAN_FB_ADF_SCANING_REQUEST                                = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A0), /* 0x030300A0 */
    EVT_TYPE_SCAN_ADF_ENGINE_PARAM_SET_MODIFY                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A1), /* 0x030300A1 */
    EVT_TYPE_SCAN_ADF_ENGINE_PARAM_SET_REQUEST                          = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A2), /* 0x030300A2 */
    EVT_TYPE_SCAN_FB_ENGINE_PARAM_SET_MODIFY                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A3), /* 0x030300A3 */
    EVT_TYPE_SCAN_FB_ENGINE_PARAM_SET_REQUEST                           = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A4), /* 0x030300A4 */
    EVT_TYPE_SCAN_ENGINE_ERROR_CLEAR_MODIFY                             = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A5), /* 0x030300A5 */
    EVT_TYPE_SCAN_ENGINE_ERROR_CLEAR_REQUEST                            = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A6), /* 0x030300A6 */

    EVT_TYPE_SCAN_JOB_CONTROL_REQUEST                                   = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_OWN,     0x00A7), /* 0x031300A7 */
    EVT_TYPE_SCAN_JOB_CONTROL_MODIFY                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A8), /* 0x030300A8 */
    EVT_TYPE_SCAN_ADF_PARAM_SET_SHOW                                    = EVT_MAKE(EVT_MODULE_SCAN,             EVT_TAG_NORMAL,  0x00A9), /* 0x030300A9 */
    EVT_TYPE_SCAN_MAX,

    /**
     * @brief EventID of copy module
     *        xxxx_REQUEST      means:others module set value to copy module
     *        xxxx_NOTIFY       means:copy module notify others module
     */
    EVT_TYPE_COPY_TEST                                                  = EVT_MAKE(EVT_MODULE_COPY,             EVT_TAG_NORMAL,  0x0000), /* 0x04030000 */
    EVT_TYPE_COPY_JOB_CONTROL_REQUEST                                   = EVT_MAKE(EVT_MODULE_COPY,             EVT_TAG_OWN,     0x0001), /* 0x04130001 */
    EVT_TYPE_COPY_JOB_CONTROL_MODIFY                                    = EVT_MAKE(EVT_MODULE_COPY,             EVT_TAG_NORMAL,  0x0002), /* 0x04030002 */
    EVT_TYPE_COPY_MAX,

    /**
     * @brief EventID of NET module
     *        xxxx_REQUEST      means:others module set value to NET module
     *        xxxx_CHANGED      means:NET module notify others module
     */
    EVT_TYPE_NET_TEST                                                   = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0000), /* 0x05030000 */
    EVT_TYPE_NET_HOST_NAME_REQUEST                                      = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0001), /* 0x05130001 */
    EVT_TYPE_NET_ETH_SWITCH_REQUEST                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0002), /* 0x05130002 */
    EVT_TYPE_NET_ETH_IPV4_CONFIG_REQUEST                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0003), /* 0x05130003 */
    EVT_TYPE_NET_ETH_DNSV4_CONFIG_REQUEST                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0004), /* 0x05130004 */
    EVT_TYPE_NET_ETH_IPV6_CONFIG_REQUEST                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0005), /* 0x05130005 */
    EVT_TYPE_NET_WIFI_SWITCH_REQUEST                                    = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0006), /* 0x05130006 */
    EVT_TYPE_NET_WIFI_SCAN_SSID_REQUEST                                 = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0007), /* 0x05130007 */
    EVT_TYPE_NET_WIFI_CONNECT_REQUEST                                   = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0008), /* 0x05130008 */
    EVT_TYPE_NET_WIFI_WPS_REQUEST                                       = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0009), /* 0x05130009 */
    EVT_TYPE_NET_WIFI_IPV4_CONFIG_REQUEST                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x000a), /* 0x0513000a */
    EVT_TYPE_NET_WIFI_DNSV4_CONFIG_REQUEST                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x000b), /* 0x0513000b */
    EVT_TYPE_NET_WIFI_IPV6_CONFIG_REQUEST                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x000c), /* 0x0513000c */
    EVT_TYPE_NET_WFD_SWITCH_REQUEST                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x000d), /* 0x0513000d */
    EVT_TYPE_NET_WFD_RESPONSE_REQUEST                                   = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x000e), /* 0x0513000e */
    EVT_TYPE_NET_ADDRESS_BOOK_REQUEST                                   = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x000f), /* 0x0513000f */
    EVT_TYPE_NET_SMTP_CONFIG_REQUEST                                    = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0010), /* 0x05130010 */
    EVT_TYPE_NET_HOST_NAME_CHANGED                                      = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0011), /* 0x05030011 */
    EVT_TYPE_NET_DOMAIN_NAME_CHANGED                                    = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0012), /* 0x05030012 */
    EVT_TYPE_NET_ETH_SWITCH_CHANGED                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0013), /* 0x05030013 Abort Ethernet */
    EVT_TYPE_NET_ETH_MAC_CHANGED                                        = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0014), /* 0x05030014 */
    EVT_TYPE_NET_ETH_CONNECTION_CHANGED                                 = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0015), /* 0x05030015 */
    EVT_TYPE_NET_ETH_IPV4_DHCP_SWITCH_CHANGED                           = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0016), /* 0x05030016 */
    EVT_TYPE_NET_ETH_IPV4_ADDRESS_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0017), /* 0x05030017 */
    EVT_TYPE_NET_ETH_IPV4_MASK_CHANGED                                  = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0018), /* 0x05030018 */
    EVT_TYPE_NET_ETH_IPV4_GATEWAY_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0019), /* 0x05030019 */
    EVT_TYPE_NET_ETH_IPV4_AUTODNS_SWITCH_CHANGED                        = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x001a), /* 0x0503001a */
    EVT_TYPE_NET_ETH_IPV4_PRIMARY_DNS_CHANGED                           = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x001b), /* 0x0503001b */
    EVT_TYPE_NET_ETH_IPV4_SECONDARY_DNS_CHANGED                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x001c), /* 0x0503001c */
    EVT_TYPE_NET_ETH_IPV6_SWITCH_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x001d), /* 0x0503001d */
    EVT_TYPE_NET_ETH_IPV6_DHCP_SWITCH_CHANGED                           = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x001e), /* 0x0503001e */
    EVT_TYPE_NET_ETH_IPV6_LINK_ADDRESS_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x001f), /* 0x0503001f */
    EVT_TYPE_NET_ETH_IPV6_STLS_ADDRESS_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0020), /* 0x05030020 */
    EVT_TYPE_NET_ETH_IPV6_DHCP_ADDRESS_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0021), /* 0x05030021 */
    EVT_TYPE_NET_ETH_IPV6_PRIMARY_DNS_CHANGED                           = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0022), /* 0x05030022 */
    EVT_TYPE_NET_ETH_IPV6_SECONDARY_DNS_CHANGED                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0023), /* 0x05030023 */
    EVT_TYPE_NET_WIFI_SWITCH_CHANGED                                    = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0024), /* 0x05030024 Abort WiFi-Station */
    EVT_TYPE_NET_WIFI_MAC_CHANGED                                       = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0025), /* 0x05030025 */
    EVT_TYPE_NET_WIFI_CONNECTION_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0026), /* 0x05030026 */
    EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0027), /* 0x05030027 */
    EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED                                   = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0028), /* 0x05030028 */
    EVT_TYPE_NET_WIFI_IPV4_DHCP_SWITCH_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0029), /* 0x05030029 */
    EVT_TYPE_NET_WIFI_IPV4_ADDRESS_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x002a), /* 0x0503002a */
    EVT_TYPE_NET_WIFI_IPV4_MASK_CHANGED                                 = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x002b), /* 0x0503002b */
    EVT_TYPE_NET_WIFI_IPV4_GATEWAY_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x002c), /* 0x0503002c */
    EVT_TYPE_NET_WIFI_IPV4_AUTODNS_SWITCH_CHANGED                       = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x002d), /* 0x0503002d */
    EVT_TYPE_NET_WIFI_IPV4_PRIMARY_DNS_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x002e), /* 0x0503002e */
    EVT_TYPE_NET_WIFI_IPV4_SECONDARY_DNS_CHANGED                        = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x002f), /* 0x0503002f */
    EVT_TYPE_NET_WIFI_IPV6_SWITCH_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0030), /* 0x05030030 */
    EVT_TYPE_NET_WIFI_IPV6_DHCP_SWITCH_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0031), /* 0x05030031 */
    EVT_TYPE_NET_WIFI_IPV6_LINK_ADDRESS_CHANGED                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0032), /* 0x05030032 */
    EVT_TYPE_NET_WIFI_IPV6_STLS_ADDRESS_CHANGED                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0033), /* 0x05030033 */
    EVT_TYPE_NET_WIFI_IPV6_DHCP_ADDRESS_CHANGED                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0034), /* 0x05030034 */
    EVT_TYPE_NET_WIFI_IPV6_PRIMARY_DNS_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0035), /* 0x05030035 */
    EVT_TYPE_NET_WIFI_IPV6_SECONDARY_DNS_CHANGED                        = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0036), /* 0x05030036 */
    EVT_TYPE_NET_WFD_SWITCH_CHANGED                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0037), /* 0x05030037 Abort WiFi-Direct */
    EVT_TYPE_NET_WFD_MAC_CHANGED                                        = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0038), /* 0x05030038 */
    EVT_TYPE_NET_WFD_SSID_PREFIX_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0039), /* 0x05030039 */
    EVT_TYPE_NET_WFD_SSID_SUFFIX_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x003a), /* 0x0503003a */
    EVT_TYPE_NET_WFD_PASSWORD_CHANGED                                   = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x003b), /* 0x0503003b */
    EVT_TYPE_NET_WFD_IPV4_ADDRESS_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x003c), /* 0x0503003c */
    EVT_TYPE_NET_WFD_IPV4_MASK_CHANGED                                  = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x003d), /* 0x0503003d */
    EVT_TYPE_NET_WFD_IPV4_GATEWAY_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x003e), /* 0x0503003e */
    EVT_TYPE_NET_WFD_REQUEST_CHANGED                                    = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x003f), /* 0x0503003f */
    EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0040), /* 0x05030040 */
    EVT_TYPE_NET_RAWPRINT_SWITCH_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0041), /* 0x05030041 Abort protocal switch */
    EVT_TYPE_NET_RAWPRINT_PORT_CHANGED                                  = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0042), /* 0x05030042 */
    EVT_TYPE_NET_LPD_SWITCH_CHANGED                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0043), /* 0x05030043 */
    EVT_TYPE_NET_WSD_SWITCH_CHANGED                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0044), /* 0x05030044 */
    EVT_TYPE_NET_SLP_SWITCH_CHANGED                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0045), /* 0x05030045 */
    EVT_TYPE_NET_IPP_IDENTIFY_ACTION_CHANGED                            = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0046), /* 0x05030046 */
    EVT_TYPE_NET_BONJOUR_SWITCH_CHANGED                                 = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0047), /* 0x05030047 */
    EVT_TYPE_NET_BONJOUR_SERVER_CHANGED                                 = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0048), /* 0x05030048 */
    EVT_TYPE_NET_SNMP_SWITCH_CHANGED                                    = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0049), /* 0x05030049 */
    EVT_TYPE_NET_SNMP_V1V2C_SWITCH_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x004a), /* 0x0503004a */
    EVT_TYPE_NET_SNMP_V3_SWITCH_CHANGED                                 = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x004b), /* 0x0503004b */
    EVT_TYPE_NET_SNMP_V1_COMMUNITY_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x004c), /* 0x0503004c */
    EVT_TYPE_NET_SNMP_V2C_COMMUNITY_CHANGED                             = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x004d), /* 0x0503004d */
    EVT_TYPE_NET_SNMP_V3_COMMUNITY_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x004e), /* 0x0503004e */
    EVT_TYPE_NET_SNMP_V3_USERNAME_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x004f), /* 0x0503004f */
    EVT_TYPE_NET_SNMP_V3_AUTHPASS_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0050), /* 0x05030050 */
    EVT_TYPE_NET_SNMP_V3_PRIVPASS_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0051), /* 0x05030051 */
    EVT_TYPE_NET_SMTP_SENDER_ADDRESS_CHANGED                            = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0052), /* 0x05030052 */
    EVT_TYPE_NET_SMTP_SERVER_ADDRESS_CHANGED                            = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0053), /* 0x05030053 */
    EVT_TYPE_NET_SMTP_SERVER_PORT_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0054), /* 0x05030054 */
    EVT_TYPE_NET_SMTP_SERVER_AUTH_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0055), /* 0x05030055 */
    EVT_TYPE_NET_SMTP_SEC_MODE_CHANGED                                  = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0056), /* 0x05030056 */
    EVT_TYPE_NET_SMTP_USERNAME_CHANGED                                  = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0057), /* 0x05030057 */
    EVT_TYPE_NET_SMTP_PASSWORD_CHANGED                                  = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0058), /* 0x05030058 */
    EVT_TYPE_NET_ALARM_CLIENT_ADDRESS1_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0059), /* 0x05030059 */
    EVT_TYPE_NET_ALARM_CLIENT_ADDRESS2_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x005a), /* 0x0503005a */
    EVT_TYPE_NET_ALARM_CLIENT_ADDRESS3_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x005b), /* 0x0503005b */
    EVT_TYPE_NET_ALARM_CLIENT_ADDRESS4_CHANGED                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x005c), /* 0x0503005c */
    EVT_TYPE_NET_ALARM_PAPER_EMPTY_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x005d), /* 0x0503005d */
    EVT_TYPE_NET_ALARM_PAPER_JAM_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x005e), /* 0x0503005e */
    EVT_TYPE_NET_ALARM_TONER_EMPTY_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x005f), /* 0x0503005f */
    EVT_TYPE_NET_ALARM_TONER_LOW_CHANGED                                = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0060), /* 0x05030060 */
    EVT_TYPE_NET_ALARM_WASTE_TONER_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0061), /* 0x05030061 */
    EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0062), /* 0x05030062 */
    EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED                             = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0063), /* 0x05030063 */
    EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0064), /* 0x05030064 */
    EVT_TYPE_NET_SMB_ADDRESS_BOOK_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0065), /* 0x05030065 */
    EVT_TYPE_NET_CUSTOM_WATER_MARK_CHANGED                              = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0066), /* 0x05030066 */
    EVT_TYPE_NET_INITIALIZE_DONE                                        = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0067), /* 0x05030067 */
    EVT_TYPE_NET_ETH_SPEED_REQUEST                                      = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x0068), /* 0x05130068 */
    EVT_TYPE_NET_ETH_SPEED_CHANGED                                      = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0069), /* 0x05030069 */
    EVT_TYPE_NET_EXPORT_LOG_REQUEST                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x006a), /* 0x0513006a */
    EVT_TYPE_NET_EXPORT_LOG_CHANGED                                     = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x006b), /* 0x0503006b */
    EVT_TYPE_NET_IPP_FINISHINGS_CONFIG_REQUEST                          = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x006c), /* 0x0513006c */
    EVT_TYPE_NET_WHITELIST_SWITCH_REQUEST                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x006d), /* 0x0513006d */
    EVT_TYPE_NET_WHITELIST_SWITCH_CHANGED                               = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x006e), /* 0x0503006e */
    EVT_TYPE_NET_WHITELIST_ADDRESS_BOOK_REQUEST                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_OWN,     0x006f), /* 0x0513006f */
    EVT_TYPE_NET_WHITELIST_ADDRESS_BOOK_CHANGED                         = EVT_MAKE(EVT_MODULE_NET,              EVT_TAG_NORMAL,  0x0070), /* 0x05030070 */
    EVT_TYPE_NET_MAX,

    /**
     * @brief EventID of system status module
     */
    EVT_TYPE_SYSTEMSTATUS_TEST                                          = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_NORMAL,  0x0000), /* 0x06030000 */
    EVT_TYPE_SYSTEMSTATUS_UPDATE                                        = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_NORMAL,  0x0001), /* 0x06030001 */
    EVT_TYPE_SYSTEMSTATUS_FROM_SYSTEMJOB                                = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0002), /* 0x06130002 */
    EVT_TYPE_SYSTEMSTATUS_FROM_PRINT                                    = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0003), /* 0x06130003 */
    EVT_TYPE_SYSTEMSTATUS_FROM_SCAN                                     = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0004), /* 0x06130004 */
    EVT_TYPE_SYSTEMSTATUS_FROM_COPY                                     = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0005), /* 0x06130005 */
    EVT_TYPE_SYSTEMSTATUS_FROM_FAX                                      = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0006), /* 0x06130006 */
    EVT_TYPE_SYSTEMSTATUS_FROM_NET                                      = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0007), /* 0x06130007 */
    //EVT_TYPE_SYSTEMSTATUS_FROM_WIFI                                     = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0008), /* 0x06130008 */
    EVT_TYPE_SYSTEMSTATUS_FROM_FWUPDATE                                 = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x0009), /* 0x06130009 */
    EVT_TYPE_SYSTEMSTATUS_FROM_POWERMGR                                 = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x000a), /* 0x0613000a */
    //EVT_TYPE_SYSTEMSTATUS_FROM_USBDEVICE                                = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x000b), /* 0x0613000b */
   // EVT_TYPE_SYSTEMSTATUS_FROM_USBHOST                                  = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x000c), /* 0x0613000c */
    EVT_TYPE_SYSTEMSTATUS_FROM_STORAGE                                  = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x000d), /* 0x0613000d */
    EVT_TYPE_SYSTEMSTATUS_FROM_USB                                      = EVT_MAKE(EVT_MODULE_SYSTEMSTATUS,     EVT_TAG_OWN,     0x000e), /* 0x0613000e */
    EVT_TYPE_SYSTEMSTATUS_MAX,

    /**
     * @brief EventID of system job module
     */
    EVT_TYPE_SYSTEMJOB_TEST                                             = EVT_MAKE(EVT_MODULE_SYSTEMJOB,        EVT_TAG_NORMAL,  0x0000), /* 0x07030000 */
    EVT_TYPE_SYSTEMJOB_UPDATE                                           = EVT_MAKE(EVT_MODULE_SYSTEMJOB,        EVT_TAG_NORMAL,  0x0001), /* 0x07030001 */
    EVT_TYPE_SYSTEMJOB_PAGES_INFO                                       = EVT_MAKE(EVT_MODULE_SYSTEMJOB,        EVT_TAG_OWN,     0x0002), /* 0x07130002 */
    EVT_TYPE_SYSTEMJOB_ATTRIBUTE_INFO                                   = EVT_MAKE(EVT_MODULE_SYSTEMJOB,        EVT_TAG_OWN,     0x0003), /* 0x07130003 */
    EVT_TYPE_SYSTEMJOB_PARAM_INFO                                       = EVT_MAKE(EVT_MODULE_SYSTEMJOB,        EVT_TAG_OWN,     0x0004), /* 0x07130004 */
    EVT_TYPE_SYSTEMJOB_MAX,

    /**
     * @brief EventID of platform module
     */
    EVT_TYPE_PLATFORM_TEST                                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0000), /* 0x08030000 */
    EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_REQUEST                     = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0001), /* 0x08130001 */
    EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_REQUEST                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0002), /* 0x08130002 */
    EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_REQUEST                   = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0003), /* 0x08130003 */
    EVT_TYPE_PLATFORM_POWER_ON_COUNT_REQUEST                            = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0004), /* 0x08130004 */
    EVT_TYPE_PLATFORM_LCD_BACKLIGHT_SET_REQUEST                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0005), /* 0x08130005 */
    EVT_TYPE_PLATFORM_VOLUME_SET_REQUEST                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0006), /* 0x08130006 */
    EVT_TYPE_PLATFORM_SLEEP_TIME_REQUEST                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0007), /* 0x08130007 */
    EVT_TYPE_PLATFORM_COUNTRY_CODE_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0008), /* 0x08130008 */
    EVT_TYPE_PLATFORM_LANGUAGE_CODE_REQUEST                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0009), /* 0x08130009 */
    EVT_TYPE_PLATFORM_REGION_CODE_REQUEST                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x000a), /* 0x0813000a */
    EVT_TYPE_PLATFORM_QUIET_REQUEST                                     = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x000b), /* 0x0813000b */
    EVT_TYPE_PLATFORM_RESTORE_FLAG_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x000c), /* 0x0813000c */
    EVT_TYPE_PLATFORM_TIMEZONE_REQUEST                                  = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x000d), /* 0x0813000d */
    EVT_TYPE_PLATFORM_DATE_FORMAT_REQUEST                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x000e), /* 0x0813000e */
    EVT_TYPE_PLATFORM_TIME_FORMAT_REQUEST                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x000f), /* 0x0813000f */
    EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_REQUEST                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0010), /* 0x08130010 */
    EVT_TYPE_PLATFORM_PORTLIMIT_USB_REQUEST                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0011), /* 0x08130011 */
    EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_REQUEST                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0012), /* 0x08130012 */
    EVT_TYPE_PLATFORM_LOCATION_REQUEST                                  = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0013), /* 0x08130013 */
    EVT_TYPE_PLATFORM_PRODUCT_DATE_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0014), /* 0x08130014 */
    EVT_TYPE_PLATFORM_BATCH_NUMBER_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0015), /* 0x08130015 */
    EVT_TYPE_PLATFORM_CONTACT_INFO_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0016), /* 0x08130016 */
    EVT_TYPE_PLATFORM_PROPERTY_NUMBER_REQUEST                           = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0017), /* 0x08130017 */
    EVT_TYPE_PLATFORM_STORAGE_CLEAR_REQUEST                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0018), /* 0x08130018 */
    EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_REQUEST                     = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0019), /* 0x08130019 */
    EVT_TYPE_PLATFORM_IO_TIMEOUT_REQUEST                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x001a), /* 0x0813001a */
    EVT_TYPE_PLATFORM_CALIBRATION_QRCODE_REQUEST                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x001b), /* 0x0813001b */
    EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_REQUEST                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x001c), /* 0x0813001c */
    EVT_TYPE_PLATFORM_ACRFACTORY_REQUEST                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x001d), /* 0x0813001d */
    EVT_TYPE_PLATFORM_CALIBRATION_REQUEST                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x001e), /* 0x0813001e */
    EVT_TYPE_PLATFORM_TR2MODESET_REQUEST                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x001f), /* 0x0813001f */
    EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST                   = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0020), /* 0x08130020 */
    EVT_TYPE_PLATFORM_MACHINE_TYPE_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0021), /* 0x08130021 */
    EVT_TYPE_PLATFORM_FIRST_SLEEP_TIME_REQUEST                          = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0022), /* 0x08130022 */
    EVT_TYPE_PLATFORM_SECOND_SLEEP_TIME_REQUEST                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0023), /* 0x08130023 */
    EVT_TYPE_PLATFORM_UDISK_ENABLE_REQUEST                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_OWN,     0x0024), /* 0x08130024 */
    EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_MODIFY                      = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0025), /* 0x08030025 */
    EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0026), /* 0x08030026 */
    EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_MODIFY                    = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0027), /* 0x08030027 */
    EVT_TYPE_PLATFORM_POWER_ON_COUNT_MODIFY                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0028), /* 0x08030028 */
    EVT_TYPE_PLATFORM_LCD_BACKLIGHT_SET_MODIFY                          = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0029), /* 0x08030029 */
    EVT_TYPE_PLATFORM_VOLUME_SET_MODIFY                                 = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x002a), /* 0x0803002a */
    EVT_TYPE_PLATFORM_SLEEP_TIME_MODIFY                                 = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x002b), /* 0x0803002b */
    EVT_TYPE_PLATFORM_COUNTRY_CODE_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x002c), /* 0x0803002c */
    EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x002d), /* 0x0803002d */
    EVT_TYPE_PLATFORM_REGION_CODE_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x002e), /* 0x0803002e */
    EVT_TYPE_PLATFORM_QUIET_MODIFY                                      = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x002f), /* 0x0803002f */
    EVT_TYPE_PLATFORM_RESTORE_FLAG_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0030), /* 0x08030030 */
    EVT_TYPE_PLATFORM_TIMEZONE_MODIFY                                   = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0031), /* 0x08030031 */
    EVT_TYPE_PLATFORM_DATE_FORMAT_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0032), /* 0x08030032 */
    EVT_TYPE_PLATFORM_TIME_FORMAT_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0033), /* 0x08030033 */
    EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0034), /* 0x08030034 */
    EVT_TYPE_PLATFORM_PORTLIMIT_USB_MODIFY                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0035), /* 0x08030035 */
    EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0036), /* 0x08030036 */
    EVT_TYPE_PLATFORM_LOCATION_MODIFY                                   = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0037), /* 0x08030037 */
    EVT_TYPE_PLATFORM_PRODUCT_DATE_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0038), /* 0x08030038 */
    EVT_TYPE_PLATFORM_BATCH_NUMBER_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0039), /* 0x08030039 */
    EVT_TYPE_PLATFORM_CONTACT_INFO_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x003a), /* 0x0803003a */
    EVT_TYPE_PLATFORM_PROPERTY_NUMBER_MODIFY                            = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x003b), /* 0x0803003b */
    EVT_TYPE_PLATFORM_MACHINE_TYPE_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x003c), /* 0x0803003c */
    EVT_TYPE_PLATFORM_MFG_NAME_MODIFY                                   = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x003d), /* 0x0803003d */
    EVT_TYPE_PLATFORM_SERIES_NAME_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x003e), /* 0x0803003e */
    EVT_TYPE_PLATFORM_PDT_NAME_MODIFY                                   = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x003f), /* 0x0803003f */
    EVT_TYPE_PLATFORM_PID_MODIFY                                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0040), /* 0x08030040 */
    EVT_TYPE_PLATFORM_SPEED_MODIFY                                      = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0041), /* 0x08030041 */
    EVT_TYPE_PLATFORM_COLOR_MODIFY                                      = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0042), /* 0x08030042 */
    EVT_TYPE_PLATFORM_DUPLEX_ENABLE_MODIFY                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0043), /* 0x08030043 */
    EVT_TYPE_PLATFORM_WIRED_ENABLE_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0044), /* 0x08030044 */
    EVT_TYPE_PLATFORM_WIRELESS_ENABLE_MODIFY                            = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0045), /* 0x08030045 */
    EVT_TYPE_PLATFORM_PRINT_LANGUAGE_MODIFY                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0046), /* 0x08030046 */
    EVT_TYPE_PLATFORM_SCREEN_TYPE_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0047), /* 0x08030047 */
    EVT_TYPE_PLATFORM_EXPAND_TRAY_NUMBER_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0048), /* 0x08030048 */
    EVT_TYPE_PLATFORM_USBHTTP_ENABLE_MODIFY                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0049), /* 0x08030049 */
    EVT_TYPE_PLATFORM_PRINT_ENABLE_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x004a), /* 0x0803004a */
    EVT_TYPE_PLATFORM_SCAN_ENABLE_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x004b), /* 0x0803004b */
    EVT_TYPE_PLATFORM_COPY_ENABLE_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x004c), /* 0x0803004c */
    EVT_TYPE_PLATFORM_FAX_ENABLE_MODIFY                                 = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x004d), /* 0x0803004d */
    EVT_TYPE_PLATFORM_SCAN_TYPE_MODIFY                                  = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x004e), /* 0x0803004e */
    EVT_TYPE_PLATFORM_PRINT_RK_MODE_MODIFY                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x004f), /* 0x0803004f */
    EVT_TYPE_PLATFORM_KERNEL_VERSION_MODIFY                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0050), /* 0x08030050 */
    EVT_TYPE_PLATFORM_ROOTFS_VERSION_MODIFY                             = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0051), /* 0x08030051 */
    EVT_TYPE_PLATFORM_FIRMWARE_VERSION_MODIFY                           = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0052), /* 0x08030052 */
    EVT_TYPE_PLATFORM_PRODUCT_1284_STRING_MODIFY                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0053), /* 0x08030053 */
    EVT_TYPE_PLATFORM_PRINTER_CID_STRING_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0054), /* 0x08030054 */
    EVT_TYPE_PLATFORM_STORAGE_CLEAR_MODIFY                              = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0055), /* 0x08030055 */
    EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_MODIFY                      = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0056), /* 0x08030056 */
    EVT_TYPE_PLATFORM_IO_TIMEOUT_MODIFY                                 = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0057), /* 0x08030057 */
    EVT_TYPE_PLATFORM_UNIQUE_NAME_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0058), /* 0x08030058 */
    EVT_TYPE_PLATFORM_CALIBRATION_QRCODE_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0059), /* 0x08030059 */
    EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_MODIFY                         = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x005a), /* 0x0803005a */
    EVT_TYPE_PLATFORM_ACRFACTORY_MODIFY                                 = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x005b), /* 0x0803005b */
    EVT_TYPE_PLATFORM_CALIBRATION_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x005c), /* 0x0803005c */
    EVT_TYPE_PLATFORM_TR2MODESET_MODIFY                                 = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x005d), /* 0x0803005d */
    EVT_TYPE_PLATFORM_VID_MODIFY                                        = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x005e), /* 0x0803005e */
    EVT_TYPE_PLATFORM_FAX_1284_STRING_MODIFY                            = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x005f), /* 0x0803005f */
    EVT_TYPE_PLATFORM_FIRST_SLEEP_TIME_MODIFY                           = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0060), /* 0x08030060 */
    EVT_TYPE_PLATFORM_SECOND_SLEEP_TIME_MODIFY                          = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0061), /* 0x08030061 */
    EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY                           = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0062), /* 0x08030062 */
    EVT_TYPE_PLATFORM_PRINT_NAME_STRING_MODIFY                          = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0063), /* 0x08030063 */
    EVT_TYPE_PLATFORM_UDISK_ENABLE_MODIFY                               = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0064), /* 0x08030064 */
    EVT_TYPE_PLATFORM_SYSTEM_TIME_SYNC                                  = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0065), /* 0x08030065 */
    EVT_TYPE_PLATFORM_FW_UPGRADE_WAY                                    = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0066), /* 0x08030066 */
    EVT_TYPE_PLATFORM_SYSTEM_TIME_MODIFY                                = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0067), /* 0x08030067 */
    EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY                            = EVT_MAKE(EVT_MODULE_PLATFORM,         EVT_TAG_NORMAL,  0x0068), /* 0x08030068 */
    EVT_TYPE_PLATFORM_MAX,

    /**
     * @brief EventID of storage manager module
     */
    EVT_TYPE_STORAGEMGR_TEST                                            = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0000), /* 0x09030000 */
    EVT_TYPE_STORAGEMGR_PUSH_EMMC_MOUNT_POINT                           = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0001), /* 0x09030001 */
    EVT_TYPE_STORAGEMGR_PUSH_SATASSDDISK_MOUNT_POINT                    = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0002), /* 0x09030002 */
    EVT_TYPE_STORAGEMGR_PUSH_USBDISK_MOUNT_POINT                        = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0003), /* 0x09030003 */
    EVT_TYPE_STORAGEMGR_CLEAR_ERR_REQUEST                               = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0004), /* 0x09130004 */
    EVT_TYPE_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED                        = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0005), /* 0x09030005 */
    EVT_TYPE_STORAGEMGR_SATASSD1_ENABLE                                 = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0006), /* 0x09130006 */
    EVT_TYPE_STORAGEMGR_SATASSD1_ENCRYPT_ENABLE                         = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0007), /* 0x09130007 */
    EVT_TYPE_STORAGEMGR_PUSH_USBDISK2_MOUNT_POINT                       = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0008), /* 0x09030008 */
    EVT_TYPE_STORAGEMGR_SATASSD1_CLEAR_ALL_DATA                         = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0009), /* 0x09130009 */
    EVT_TYPE_STORAGEMGR_SATASSD1_CLEAR_ALL_DATA_END                     = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x000a), /* 0x0903000a */
    EVT_TYPE_STORAGEMGR_USBDISK_DISABLE                                 = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x000b), /* 0x0913000b */
    EVT_TYPE_STORAGEMGR_GET_SATASSD1_ENABLE                             = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x000c), /* 0x0903000c */
    EVT_TYPE_STORAGEMGR_GET_SATASSD1_ENCRYPT_ENABLE                     = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x000d), /* 0x0903000d */
    EVT_TYPE_STORAGEMGR_GET_USBDISK_DISABLE                             = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x000e), /* 0x0903000e */
    EVT_TYPE_STORAGEMGR_UNINSTALL_REQUEST                               = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x000f), /* 0x0913000f */
    EVT_TYPE_STORAGEMGR_REPAIR_REQUEST                                  = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0010), /* 0x09130010 */
    EVT_TYPE_STORAGEMGR_FORMAT_REQUEST                                  = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0011), /* 0x09130011 */
    EVT_TYPE_STORAGEMGR_POWER_CTL                                       = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0012), /* 0x09130012 */
    EVT_TYPE_STORAGEMGR_SATASSD1_RWCHECK_REQUEST                        = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0013), /* 0x09130013 */
    EVT_TYPE_STORAGEMGR_SATASSD1_RWCHECK_MODIFY                         = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0014), /* 0x09030014 */
    EVT_TYPE_STORAGEMGR_PUSH_EMMC_MOUNT_POINT2                          = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0015), /* 0x09030015 */
    EVT_TYPE_STORAGEMGR_PUSH_EMMC_CACHE_MOUNT_POINT                     = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0016), /* 0x09030016 */
    EVT_TYPE_STORAGEMGR_PUSH_SATASSDDISK_CACHE_MOUNT_POINT              = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0017), /* 0x09030017 */
    EVT_TYPE_STORAGEMGR_PUSH_USBDISK_CACHE_MOUNT_POINT                  = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0018), /* 0x09030018 */
    EVT_TYPE_STORAGEMGR_PUSH_EMMC_CACHE_MOUNT_POINT2                    = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0019), /* 0x09030019 */
    EVT_TYPE_STORAGEMGR_SATASSD1_COMPAT_ENABLE                          = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x001a), /* 0x0913001a */
    EVT_TYPE_STORAGEMGR_GET_SATASSD1_COMPAT_ENABLE                      = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x001b), /* 0x0903001b */
    EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_REQUEST                    = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x001c), /* 0x0913001c */
    EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_MODIFY                     = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x001d), /* 0x0903001d */
    EVT_TYPE_STORAGEMGR_SATASSD1_COMPATCHECK_REQUEST                    = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x001e), /* 0x0913001e */
    EVT_TYPE_STORAGEMGR_SATASSD1_COMPATCHECK_MODIFY                     = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x001f), /* 0x0903001f */
    EVT_TYPE_STORAGEMGR_SATASSD1_FULL_CHANGE_REQUEST                    = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0020), /* 0x09130020 */
    EVT_TYPE_STORAGEMGR_INNER_FULL_CHANGE_REQUEST                       = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0021), /* 0x09130021 */
    EVT_TYPE_STORAGEMGR_SATASSD1_FORMAT_REQUEST                         = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0022), /* 0x09130022 */
    EVT_TYPE_STORAGEMGR_SATASSD1_FORMAT_MODIFY                          = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0023), /* 0x09030023 */
    EVT_TYPE_STORAGEMGR_SATASSD1_FIXSTATE_MODFIY                        = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0024), /* 0x09030024 */
    EVT_TYPE_STORAGEMGR_SATASSD1_ACTIVATE_REQUEST                       = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_OWN,     0x0025), /* 0x09130025 */
    EVT_TYPE_STORAGEMGR_SATASSD1_ACTIVATE_MODIFY                        = EVT_MAKE(EVT_MODULE_STORAGE_MGR,      EVT_TAG_NORMAL,  0x0026), /* 0x09030026 */
    EVT_TYPE_STORAGEMGR_MAX,

    /**
     * @brief EventID of power manager module
     */
    EVT_TYPE_POWERMGR_TEST                                              = EVT_MAKE(EVT_MODULE_POWER_MGR,        EVT_TAG_NORMAL,  0x0000), /* 0x0a030000 */
    EVT_TYPE_POWERMGR_CONFIG                                            = EVT_MAKE(EVT_MODULE_POWER_MGR,        EVT_TAG_OWN,     0x0001), /* 0x0a130001 */
    EVT_TYPE_POWERMGR_STATUS                                            = EVT_MAKE(EVT_MODULE_POWER_MGR,        EVT_TAG_NORMAL,  0x0002), /* 0x0a030002 */
    EVT_TYPE_POWERMGR_MAX,

    /**
     * @brief EventID of poweroff manager module
     */
    EVT_TYPE_POWEROFFMGR_TEST                                           = EVT_MAKE(EVT_MODULE_POWEROFF_MGR,     EVT_TAG_NORMAL,  0x0000), /* 0x0b030000 */
    EVT_TYPE_POWEROFFMGR_CONFIG                                         = EVT_MAKE(EVT_MODULE_POWEROFF_MGR,     EVT_TAG_OWN,     0x0001), /* 0x0b130001 */
    EVT_TYPE_POWEROFFMGR_MAX,

    /**
     * @brief EventID of USB Device module
     */
    EVT_TYPE_USBDEVICE_TEST                                             = EVT_MAKE(EVT_MODULE_USBDEVICE,        EVT_TAG_NORMAL,  0x0000), /* 0x0c030000 */
    EVT_TYPE_USBDEVICE_CONNECTION_CHANGED                               = EVT_MAKE(EVT_MODULE_USBDEVICE,        EVT_TAG_NORMAL,  0x0001), /* 0x0c030001 */
    EVT_TYPE_USBDEVICE_DRIVER_LOAD_STATE                                = EVT_MAKE(EVT_MODULE_USBDEVICE,        EVT_TAG_NORMAL,  0x0002), /* 0x0c030002 */
    EVT_TYPE_USBDEVICE_MAX,

    /**
     * @brief EventID of USB Host module
     */
    EVT_TYPE_USBHOST_TEST                                               = EVT_MAKE(EVT_MODULE_USBHOST,          EVT_TAG_NORMAL,  0x0000), /* 0x0d030000 */
    EVT_TYPE_USBHOST_STATUS_CHANGED                                     = EVT_MAKE(EVT_MODULE_USBHOST,          EVT_TAG_NORMAL,  0x0001), /* 0x0d030001 */
    EVT_TYPE_USBHOST_UDISK_STATUS_CHANGED                               = EVT_MAKE(EVT_MODULE_USBHOST,          EVT_TAG_NORMAL,  0x0002), /* 0x0d030002 */
    EVT_TYPE_USBHOST_OVER_CURRENT_ERROR                                 = EVT_MAKE(EVT_MODULE_USBHOST,          EVT_TAG_NORMAL,  0x0003), /* 0x0d030003 */
    EVT_TYPE_USBHOST_GET_ICCARD_INFO                                    = EVT_MAKE(EVT_MODULE_USBHOST,          EVT_TAG_NORMAL,  0x0004), /* 0x0d030004 */
    EVT_TYPE_USBHOST_MAX,

    /**
     * @brief EventID of upgrade module
     */
    EVT_TYPE_UPGRADE_STATUS                                             = EVT_MAKE(EVT_MODULE_UPGRADE ,         EVT_TAG_NORMAL,  0x0000), /* 0x0e030000 */
    EVT_TYPE_UPGRADE_CONFIRM_REQUEST                                    = EVT_MAKE(EVT_MODULE_UPGRADE ,         EVT_TAG_NORMAL,  0x0001), /* 0x0e030001 */
    EVT_TYPE_UPGRADE_CONFIRM_MODIFY                                     = EVT_MAKE(EVT_MODULE_UPGRADE ,         EVT_TAG_NORMAL,  0x0002), /* 0x0e030002 */
    EVT_TYPE_UPGRADE_OFFLINE_UPGRADE_MODE_REQUEST                       = EVT_MAKE(EVT_MODULE_UPGRADE ,         EVT_TAG_OWN,     0x0003), /* 0x0e130003 */
    EVT_TYPE_UPGRADE_MAX,

    /**
     * @brief EventID of ACL parser module
     */
    EVT_TYPE_ACL_PARSER_TEST                                            = EVT_MAKE(EVT_MODULE_ACL_PARSER,       EVT_TAG_NORMAL,  0x0000), /* 0x0f030000 */
    EVT_TYPE_ACL_PARSER_MAX,

    /**
     * @brief EventID of security module
     */
    EVT_TYPE_SECURITY_TEST                                              = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_NORMAL,  0x0000), /* 0x10030000 */
    EVT_TYPE_SECURITY_FWUPGRADE_RECORD                                  = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_OWN,     0x0001), /* 0x10130001 */
    EVT_TYPE_SECURITY_OPERATE_RECORD                                    = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_OWN,     0x0002), /* 0x10130002 */
    EVT_TYPE_SECURITY_AUDITJOB_RECORD                                   = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_OWN,     0x0003), /* 0x10130003 */
    EVT_TYPE_SECURITY_AUDITJOB_INFO                                     = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_NORMAL,  0x0004), /* 0x10030004 */
    EVT_TYPE_SECURITY_SOLE_SERIAL_NUMBER                                = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_NORMAL,  0x0005), /* 0x10030005 */
    EVT_TYPE_SECURITY_CANCEL_PAGES                                      = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_NORMAL,  0x0006), /* 0x10030006 */
    EVT_TYPE_SECURITY_HTTC_VERSION                                      = EVT_MAKE(EVT_MODULE_SECURITY,         EVT_TAG_NORMAL,  0x0007), /* 0x10030007 */
    EVT_TYPE_SECURITY_MAX,

    /**
     * @brief EventID of printerinfo module
     */
    EVT_TYPE_PRINTERINFO_TEST                                           = EVT_MAKE(EVT_MODULE_PRINTERINFO,      EVT_TAG_NORMAL,  0x0000), /* 0x11030000 */
    EVT_TYPE_PRINTERINFO_UPDATE_DYNAMICINFO                             = EVT_MAKE(EVT_MODULE_PRINTERINFO,      EVT_TAG_NORMAL,  0x0001), /* 0x11030001 */
    EVT_TYPE_PRINTERINFO_UPDATE_STATICINFO                              = EVT_MAKE(EVT_MODULE_PRINTERINFO,      EVT_TAG_NORMAL,  0x0002), /* 0x11030002 */
    EVT_TYPE_PRINTERINFO_MAX,

    /**
     * @brief EventID of EventLog module
     */
    EVT_TYPE_EVENTLOG_TEST                                              = EVT_MAKE(EVT_MODULE_EVENTLOG,         EVT_TAG_NORMAL,  0x0000), /* 0x12030000 */
    EVT_TYPE_EVENTLOG_SYSTEM_STATUS                                     = EVT_MAKE(EVT_MODULE_EVENTLOG,         EVT_TAG_OWN,     0x0001), /* 0x12130001 */
    EVT_TYPE_EVENTLOG_FIRMWARE                                          = EVT_MAKE(EVT_MODULE_EVENTLOG,         EVT_TAG_OWN,     0x0002), /* 0x12130002 */
    EVT_TYPE_EVENTLOG_JOB                                               = EVT_MAKE(EVT_MODULE_EVENTLOG,         EVT_TAG_OWN,     0x0003), /* 0x12130003 */
    EVT_TYPE_EVENTLOG_CONSUMABLE                                        = EVT_MAKE(EVT_MODULE_EVENTLOG,         EVT_TAG_OWN,     0x0004), /* 0x12130004 */
    EVT_TYPE_EVENTLOG_MAX,

    /**
     * @brief EventID of PrintSDK module
     */
    EVT_TYPE_PRNSDK_TEST                                                = EVT_MAKE(EVT_MODULE_PRNSDK,           EVT_TAG_NORMAL,  0x0000), /* 0x13030000 */
    EVT_TYPE_PRNSDK_ENABLE_REQUEST                                      = EVT_MAKE(EVT_MODULE_PRNSDK,           EVT_TAG_OWN,     0x0001), /* 0x13130001 */
    EVT_TYPE_PRNSDK_ENABLE_MODIFY                                       = EVT_MAKE(EVT_MODULE_PRNSDK,           EVT_TAG_NORMAL,  0x0002), /* 0x13030002 */
    EVT_TYPE_PRNSDK_MAX,

    /**
     * @brief EventID of PEDKMGR module
     */
    EVT_TYPE_PEDKMGR_TEST                                               = EVT_MAKE(EVT_MODULE_PEDK_MGR,         EVT_TAG_NORMAL,   0x0000), /* 0x14030000 */
    EVT_TYPE_PEDKMGR_APP_INSTALL_MODIFY                                 = EVT_MAKE(EVT_MODULE_PEDK_MGR,         EVT_TAG_NORMAL,   0x0001), /* 0x14030001 */
    EVT_TYPE_PEDKMGR_APP_UNINSTALL_MODIFY                               = EVT_MAKE(EVT_MODULE_PEDK_MGR,         EVT_TAG_NORMAL,   0x0002), /* 0x14030002 */
    EVT_TYPE_PEDKMGR_MAX,

    /**
     * @brief EventID of PRINTER_RES module
     */
    EVT_TYPE_PRINTERRES_TEST                                            = EVT_MAKE(EVT_MODULE_PRINTER_RES,      EVT_TAG_NORMAL,   0x0000), /* 0x15030000 */
    EVT_TYPE_PRINTERRES_PAPER_SIZE_CONFIG_REQUEST                       = EVT_MAKE(EVT_MODULE_PRINTER_RES,      EVT_TAG_OWN,      0x0001), /* 0x15130001 */
    EVT_TYPE_PRINTERRES_PAPER_SIZE_CONFIG_MODIFY                        = EVT_MAKE(EVT_MODULE_PRINTER_RES,      EVT_TAG_NORMAL,   0x0002), /* 0x15030002 */
    EVT_TYPE_PRINTERRES_MAX,

    EVT_TYPE_ENUM_MAX
}
EVT_TYPE_E;

struct evt_mgr_cli;
typedef struct evt_mgr_cli EVT_MGR_CLI_S;

typedef struct evt_msg
{
    uint32_t module_id;
    uint32_t event_type;
    uint32_t data_length;
    void*    data;
} EVT_MSG_S;

PT_END_DECLS

#endif /* EVENT_MGR_TYPEDEF_H */
/**
 *@}
 */
