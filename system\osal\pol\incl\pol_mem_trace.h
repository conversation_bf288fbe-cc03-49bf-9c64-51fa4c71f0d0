/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_mem_trace.h
 * @addtogroup pol
 * @{
 * @addtogroup mem_debug
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal mem trace interface set
 */
#ifndef __POL_MEM_TRACE_H__
#define __POL_MEM_TRACE_H__

#include <sys/types.h>
#include <unistd.h>
#include <errno.h>
#include <locale.h>
#include <pthread.h>
#include "pol/pol_types.h"
#include "pol/pol_string.h"
#include "pol/pol_convert.h"
#include "pol/pol_io.h"
#include "rbtree/rbtree.h"
#include <pol/pol_define.h>

PT_BEGIN_DECLS

#if defined(CONFIG_POL_MEM_TRACE)



#define MODULE_MEM_TRACE 		"MEM_TRACE"



#ifdef CONFIG_POL_TRACE_ASSERT
#define _pol_trace_assert       assert(0)
#else
#define _pol_trace_assert
#endif


typedef enum{
	TRACE_ERR_NODE_OVER_COUNT = -100,
	TRACE_ERR_INIT_CB_OVER_COUNT,
	TRACE_ERR_NODE_INSERT_REPEAT,

	/**
     * @brief  MEM OPERATE ERR FROM USER
     */
	TRACE_ERR_USER_DOUBLE_FREE = -80,
	TRACE_ERR_WILD_POINTER,
	TRACE_ERR_OVER_REGION,
	
	TRACE_ERR_UNMAP_NOT_MATCH,

	TRACE_FAIL = -1,
	/**
     * @brief ERR STATE IS ABOVE THE LINE
     */
	TRACE_OK = 0,
	/**
     * @brief NORMAL STATE IS BELOW THE LINE
     */

	

}MEM_TRACE_STATE_E;



typedef struct
{
	union{
		struct rb_node 			node;   ///< rb node
		struct list_head		fnode;  ///< free node
	};
	void * 		start;  ///< start address of memery,is the keyword for rbtree
	void * 		end;    ///< end address of memery
    pid_t 		LWP;    ///< phtread Id
#if (CONFIG_POL_MEM_TRACE_BACKTRACE_LV)
	void*			entry[CONFIG_POL_MEM_TRACE_BACKTRACE_LV]; ///< backtrace
	int32_t			bt_size;
#endif
}HEAP_NODE_S,*HEAP_NODE_P;


typedef struct{
	uint32_t 				a_count; ///<alloc node count
	uint32_t 				f_count; ///<free count
	struct list_head		f_list; ///< freelilst
	struct rb_root			a_root; ///<alloc rbtree root
	uint32_t 				max_a_count; ///< max alloc count
}HEAP_STAT_S,*HEAP_STAT_P;


#define MEM_TRACE_LV  (CONFIG_POL_MEM_TRACE_BACKTRACE_LV%5)
#define MAX_HEAPNODE_NUM   1000

MEM_TRACE_STATE_E _mem_alloc_trace(void* start, void* end );
void  _mem_realloc_trace (void* addr, void* end);
MEM_TRACE_STATE_E _mem_free_trace(void* addr);
MEM_TRACE_STATE_E _mem_access_trace(const void* start, const void* end);
void _mem_munmap_trace (void* start, void* end);
int32_t  _mem_trace_init(void);



#define mem_alloc_trace(s, e)		{if(s)	{_mem_alloc_trace(s,e);}}
#define mem_free_trace(s)			_mem_free_trace(s)
#define mem_access_trace(s, e)		_mem_access_trace(s,e)
#define mem_realloc_trace(s, e)		{if(s) {_mem_realloc_trace(s,e);}}
#define mem_trace_init() 			_mem_trace_init()
#define mem_mmap_trace(s, e)			{if((s)!=(void*)(-1))	{_mem_alloc_trace(s,e);}}
#define mem_munmap_trace(s, e)		_mem_munmap_trace(s,e)

#else
#define mem_alloc_trace(s, e)		
#define mem_free_trace(s)			
#define mem_access_trace(s, e)		
#define mem_realloc_trace(s, e)
#define mem_mmap_trace(s, e)		
#define mem_munmap_trace(s, e)		
#define mem_trace_init()
#endif


PT_END_DECLS

#endif
/**
 *@}
 */
