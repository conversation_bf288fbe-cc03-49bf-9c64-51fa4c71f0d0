/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       pipelines_mgr.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-11-01
 * @version    v1.0
 * @details    pipelines management
 */

#ifndef PIPELINES_MGR_H
#define PIPELINES_MGR_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "pipeline.h"


#define MAX_PIPELINES   4   ///< pipelines config


/**
 * @brief pipeline process core
 * @param[in] hmem memory object
 * @param[in] pipe_rule pipe line rule str
 * @param[in] callback pipe line callback
 * @param[in] context pipe running context
 * @return PIPE_LINE_P \n
 *          … …
 * @retval current pipeline object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
PIPE_LINE_P pipelines_build_pipeline(MEMOBJ_P hmem,char *pipe_rule,PIPELINE_CALLBACK callback,void *context);

/**
 * @brief pipeline process core
 * @param[in] pipeline current pipeline object
 * @param[in] pipe_rule pipe line rule str
 * @return uint32_t \n
 *          … …
 * @retval FAIL:-1 SUCCESS:0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
uint32_t pipelines_update_pipeline(PIPE_LINE_P pipeline, char *pipe_rule);

/**
 * @brief pipeline process core
 * @param[in] pipe_rule pipe line rule str
 * @return uint32_t \n
 *          … …
 * @retval FAIL:-1 SUCCESS:0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
uint32_t pipelines_activate_pipeline(PIPE_LINE_P pipeline);

/**
 * @brief pipeline process core
 * @param[in] pipe_rule pipe line rule str
 * @return uint32_t \n
 *          … …
 * @retval FAIL:-1 SUCCESS:0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
uint32_t pipelines_destroy_pipeline(PIPE_LINE_P pipeline);

/**
 * @brief init pipe core module
 * @param[in] void
 * @return uint32_t \n
 *          … …
 * @retval FAIL:-1 SUCCESS:0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipelines_mgr_prolog();

/**
 * @brief destroy pipe core module
 * @param[in] void
 * @return uint32_t \n
 *          … …
 * @retval FAIL:-1 SUCCESS:0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipelines_mgr_eprolog();

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* PIPELINES_MGR_H */

/**
 *@}
 */


