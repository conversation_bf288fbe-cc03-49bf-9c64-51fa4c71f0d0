#ifndef _PEDK_NET_SMB_
#define _PEDK_NET_SMB_

#include <quickjs.h>

#define SMB_ADDR_LEN 256
#define SMB_SERVER_PATH_LEN 256
#define SMB_LOGIN_NAME_LEN  128
#define SMB_LOGIN_PWD_LEN   41


typedef struct
{
    char     smb_addr[SMB_ADDR_LEN];               //服务器IP
    char     smb_server_path[SMB_SERVER_PATH_LEN];        //smb服务器路径
    uint16_t smb_port;                                         //端口号
    uint16_t smb_anonymity;                                    //是否匿名
    char     smb_login_name[SMB_LOGIN_NAME_LEN];               //登录名
    char     smb_login_pwd[SMB_LOGIN_PWD_LEN];             //密码

}
PEDK_SMB_PARAMETER_SET;

JSValue js_smb_connect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_smb_disconnect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);



#endif /* _PEDK_NET_SMB_ */
