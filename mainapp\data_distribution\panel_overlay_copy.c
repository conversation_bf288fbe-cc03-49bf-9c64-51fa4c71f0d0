/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_overlay_copy.c
 * @addtogroup panel_dc
 * @{
 * @brief panel dc save and process overlay copy param
 * <AUTHOR> 
 * @date 2024-07-9
 */

#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "utilities/msgrouter.h"
#include "event_manager/event_mgr.h"
#include "pol/pol_log.h"
#include "panel_event.h"
#include "public/msgrouter_main.h"
#include "pol/pol_string.h"
#include "panel_overlay_copy.h"
#include "panel_dc_cmd_tpyedef.h"
#include "nvram.h"
#include "moduleid.h"
#include "panel_dc_cmd_process.h"


#define                 OVERLAY_PANEL_ID_INIT       1                   ///< 叠图id从1开始

static PANLE_OVERLAY_COPY_DATA_S overlay_copy_data_s[OVERLAY_NUM] = {0}; 
static char overlay_mount_point[64] = {0};

PANLE_OVERLAY_COPY_DATA_S* get_overlay_data()
{
    return overlay_copy_data_s;
}

void panel_get_overlay_mount_point( void* data, uint32_t data_len )
{
    if( NULL !=  data && data_len > 0)
    {
        pi_memset( overlay_mount_point, 0, sizeof(overlay_mount_point) );
        pi_memcpy( overlay_mount_point, data, data_len );
        pi_log_d( "panel set overlay mount point:%s\n", overlay_mount_point );
    }
}

void panel_new_overlay_copy_info( OVERLAY_IMG_INFO_S* overlay_img_info )
{
    int i = 0;
    int ret = 0;
    if( overlay_img_info == NULL )
    {
        pi_log_e("panel get new overlay data error\n");
        return;
    }

    for( i = 0; i < sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S); i++ )
    {
        if( overlay_copy_data_s[i].id == 0 && overlay_copy_data_s[i].name[0] == '\0' )
        {
            if( i == 0 )
            {
                overlay_copy_data_s[i].id = OVERLAY_PANEL_ID_INIT;
            }
            else
            {
                //id 为累加
                overlay_copy_data_s[i].id = overlay_copy_data_s[i-1].id + 1;
            }
            //面板设置的叠图名
            pi_strncpy( overlay_copy_data_s[i].name, overlay_img_info->register_id, strlen(overlay_img_info->register_id) );
            
            //填充注册叠图id到copy
            pi_snprintf( overlay_img_info->register_id, sizeof(overlay_img_info->register_id), "%u", overlay_copy_data_s[i].id );
            pi_log_d(" create overlay id:%s\n",overlay_img_info->register_id);

            //保存数据
            ret = pi_nvram_set( UI_ID_PANEL_OVERLAY_COPY_DATA, VTYPE_STRUCT, &overlay_copy_data_s, sizeof(overlay_copy_data_s), 1, NULL );
            if( ret < 0 )
            {
                pi_log_e( "panel set nv overlay data ERROR\n");
                return ;
            }
            //通知面板更新数据
            panel_send_data_u8( SETTING, SETTING_CMD_OVERLAY_COPY_DATA, NOTIFICATION_RESPONSE, &overlay_copy_data_s, sizeof(overlay_copy_data_s) );
            break;
        }
    }
    if(i == sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S))
    {
        pi_log_e( "the num of overlay is full\n");
        return;
    }

}

void panel_modify_overlay_copy_info( void* data, uint32_t data_len )
{
    int i = 0;
    int ret = 0;
    PANEL_OPERATE_OVERLAY_S* panel_operate = (PANEL_OPERATE_OVERLAY_S*) data;
    if( panel_operate == NULL )
    {
        pi_log_e("panel get new overlay data error\n");
        return;
    }

    for( i = 0; i < sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S); i++ )
    {
        if( overlay_copy_data_s[i].id == panel_operate->overlay_info.id )
        {
            pi_memset( overlay_copy_data_s[i].name, 0, sizeof(overlay_copy_data_s[i].name) );
            pi_strncpy( overlay_copy_data_s[i].name, panel_operate->overlay_info.name, strlen(panel_operate->overlay_info.name) );
            //无需通知复印

            //保存数据
            ret = pi_nvram_set( UI_ID_PANEL_OVERLAY_COPY_DATA, VTYPE_STRUCT, &overlay_copy_data_s, sizeof(overlay_copy_data_s), 1, NULL );
            if( ret < 0 )
            {
                pi_log_e( "panel set nv overlay data ERROR\n");
                return ;
            }
            //通知面板更新数据
            panel_send_data_u8( SETTING, SETTING_CMD_OVERLAY_COPY_DATA, NOTIFICATION_RESPONSE, &overlay_copy_data_s, sizeof(overlay_copy_data_s) );
            break;
        }
    }
    if(i == sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S))
    {
        pi_log_e( "cannot found overlay data\n");
        return;
    }

}

void panel_delete_overlay_copy_info( void* data, uint32_t data_len )
{
    int i = 0;
    int ret = 0;
    char rm_file_cmd[256] = {0};
    PANEL_OPERATE_OVERLAY_S* panel_operate = (PANEL_OPERATE_OVERLAY_S*) data;
    if( panel_operate == NULL )
    {
        pi_log_e("panel get new overlay data error\n");
        return;
    }

    for( i = 0; i < sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S); i++ )
    {
        if( overlay_copy_data_s[i].id == panel_operate->overlay_info.id )
        {
            //删除文件
            pi_log_d("panel rm overlay file result:%s\n", pi_runcmd( NULL, 0, 0, "rm -rf %s/%s/%d_oly*", overlay_mount_point, CONFIG_OVERLAY_IMAGE, overlay_copy_data_s[i].id ) ); 

            //删除后的数组整体前移
            for( int k = i; k < sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S); k++ )
            {
                pi_memset( &overlay_copy_data_s[k], 0, sizeof(PANLE_OVERLAY_COPY_DATA_S) );
                //最后一个数据只需清空
                if( k !=  sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S) -1 )
                {
                    overlay_copy_data_s[k].id = overlay_copy_data_s[k+1].id;
                    pi_strncpy( overlay_copy_data_s[k].name, overlay_copy_data_s[k+1].name, strlen(overlay_copy_data_s[k+1].name) );
                }
                if( overlay_copy_data_s[k].id == 0 )
                {
                    break;
                }
            }

            //保存数据
            ret = pi_nvram_set( UI_ID_PANEL_OVERLAY_COPY_DATA, VTYPE_STRUCT, &overlay_copy_data_s, sizeof(overlay_copy_data_s), 1, NULL );
            if( ret < 0 )
            {
                pi_log_e( "panel set nv overlay data ERROR\n");
                return ;
            }
            //通知面板更新数据
            panel_send_data_u8( SETTING, SETTING_CMD_OVERLAY_COPY_DATA, NOTIFICATION_RESPONSE, &overlay_copy_data_s, sizeof(overlay_copy_data_s) );
            break;
        }
    }
    if(i == sizeof(overlay_copy_data_s)/sizeof(PANLE_OVERLAY_COPY_DATA_S))
    {
        pi_log_e( "cannot found overlay data\n");
        return;
    }

}

void panel_delete_all_overlay_copy_info( void )
{
    //删除文件
    pi_log_d("panel rm overlay file result:%s\n", pi_runcmd( NULL, 0, 0, "rm -rf %s/%s/*_oly*", overlay_mount_point, CONFIG_OVERLAY_IMAGE ) ); 

    pi_memset( &overlay_copy_data_s, 0, sizeof(overlay_copy_data_s) );
    //保存数据
    int ret = pi_nvram_set( UI_ID_PANEL_OVERLAY_COPY_DATA, VTYPE_STRUCT, &overlay_copy_data_s, sizeof(overlay_copy_data_s), 0, NULL );

    if( ret < 0 )
    {
        pi_log_e( "panel set nv overlay data ERROR\n");
        return ;
    }
    //通知面板更新数据
    panel_send_data_u8( SETTING, SETTING_CMD_OVERLAY_COPY_DATA, NOTIFICATION_RESPONSE, &overlay_copy_data_s, sizeof(overlay_copy_data_s) );

}


void panel_modify_delete_overlay_data( void* data, uint32_t data_len )
{
    PANEL_OPERATE_OVERLAY_S* panel_operate = (PANEL_OPERATE_OVERLAY_S*) data;
    if( panel_operate == NULL )
    {
        pi_log_e("panel get new overlay data error\n");
        return;
    }
    switch ( panel_operate->operate )
    {
    case PANEL_OVERLAY_DELETE:
        panel_delete_overlay_copy_info( data, data_len );
        break;
    case PANEL_OVERLAY_MODIFY:
        panel_modify_overlay_copy_info( data, data_len );
        break;
    case PANEL_OVERLAY_DELETE_ALL:
        panel_delete_all_overlay_copy_info();
        break;
    
    default:
        pi_log_e( "panel oprate overlay cmd error!" );
        break;
    }

}

void panel_overlay_transfer_id( OVERLAY_IMG_INFO_S* overlay_img_info )
{
    uint32_t overlay_panel_front_id = 0;
    uint32_t overlay_panel_back_id = 0;
    overlay_panel_front_id = *( (uint32_t*)overlay_img_info->front_use_id );
    overlay_panel_back_id = *( (uint32_t*)overlay_img_info->back_use_id );
    pi_snprintf( overlay_img_info->front_use_id, sizeof( overlay_img_info->front_use_id ), "%u", overlay_panel_front_id);
    pi_snprintf( overlay_img_info->back_use_id, sizeof( overlay_img_info->back_use_id ), "%u", overlay_panel_back_id);
    pi_log_d( "overlay ui front id:%d, back id:%d, to copy front id:%s, back id:%s\n",overlay_panel_front_id,overlay_panel_back_id,overlay_img_info->front_use_id,overlay_img_info->back_use_id );
}

void panel_get_overlay_copy_data()
{
    //保存数据
    int ret = pi_nvram_get( UI_ID_PANEL_OVERLAY_COPY_DATA, VTYPE_STRUCT, &overlay_copy_data_s, sizeof(overlay_copy_data_s) );
    if( ret < 0 )
    {
        pi_log_e( "panel set nv overlay data ERROR\n");
        return ;
    }
    // for( int i = 0; i < sizeof(overlay_copy_data_s)/sizeof(overlay_copy_data_s[0]); i++ )
    // {
    //     pi_log_d("overlay data [%d] id:%d, name:%s\n", i, overlay_copy_data_s[i].id, overlay_copy_data_s[i].name);
    // }
    //通知面板更新数据
    panel_send_data_u8( SETTING, SETTING_CMD_OVERLAY_COPY_DATA, NOTIFICATION_RESPONSE, &overlay_copy_data_s, sizeof(overlay_copy_data_s) );
}

void panel_overlay_job_process( OVERLAY_IMG_INFO_S* overlay_img_info )
{
    if( NULL == overlay_img_info )
    {
        return;
    }
    switch ( overlay_img_info->mode )
    {
        case OVERLAY_IMG_MODE_ONLY_REGISTER:
            panel_new_overlay_copy_info( overlay_img_info );
            break;
        case OVERLAY_IMG_MODE_ONLY_COMBINE:
            //panel_overlay_transfer_id( overlay_img_info );
            pi_log_d("panel overlay front id:%s,back id:%s\n",overlay_img_info->front_use_id,overlay_img_info->back_use_id);
            break;
    
    default:
        break;
    }
}
/**
 * @brief cmd callback for test the setting value
 */
int32_t overlay_cmd_test_callback(int32_t argc, char *argv[])
{   
    COPY_JOB_REQUEST_DATA_S copy_job_data = {0};
    PANEL_OPERATE_OVERLAY_S panel_modify_overlay = {0};

    int32_t data_0 = atoi(argv[0]);
    int32_t data_1 = atoi(argv[1]);
    char* data_2 = argv[2];

    switch (data_0)
    {
        //new 
        case 0:
            printf("panel test new overlay\n");
            pi_snprintf( copy_job_data.oly_img_info.register_id, sizeof(copy_job_data.oly_img_info.register_id), "AAAA");
            copy_job_data.oly_img_info.mode = OVERLAY_IMG_MODE_ONLY_REGISTER;
            copy_job_data.copies = 1;
            copy_job_start_process( &copy_job_data, sizeof(COPY_JOB_REQUEST_DATA_S) );
            break;
        case 1:
            panel_modify_overlay.operate = PANEL_OVERLAY_MODIFY;
            panel_modify_overlay.overlay_info.id = data_1;
            strcpy( panel_modify_overlay.overlay_info.name, data_2 );
            panel_modify_overlay_copy_info( &panel_modify_overlay, sizeof(PANEL_OPERATE_OVERLAY_S) );
            break;

        case 2:
            panel_modify_overlay.operate = PANEL_OVERLAY_DELETE;
            panel_modify_overlay.overlay_info.id = data_1;
            panel_delete_overlay_copy_info( &panel_modify_overlay, sizeof(PANEL_OPERATE_OVERLAY_S) );
            break;

        case 3:
            panel_get_overlay_copy_data();
            break;

        case 4:
            printf("panel test new overlay\n");
            pi_snprintf( copy_job_data.oly_img_info.front_use_id, sizeof(copy_job_data.oly_img_info.front_use_id), argv[1]);
            pi_snprintf( copy_job_data.oly_img_info.back_use_id, sizeof(copy_job_data.oly_img_info.back_use_id), argv[2]);
            copy_job_data.oly_img_info.mode = OVERLAY_IMG_MODE_ONLY_COMBINE;
            copy_job_data.copies = 1;
            copy_job_start_process( &copy_job_data, sizeof(COPY_JOB_REQUEST_DATA_S) );

            break;
        
        default:
            break;
    }

    return 0;
}











 
 /**                                                                                                                                                                  
  * @}
  */


