/**
 *  @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 *  @file proxy_work.h
 *  @{
 *  @addtogroup proxy
 *  @brief proxy module 
 *  <AUTHOR> 
 *  @version 1.0
 *  @date 2023-04-28
 **/
 
#ifndef _PROXY_WORK_H
#define _PROXY_WORK_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief proxy module initialization 
 *
 * @param proxy_recv_callback[in] the proxy module data comes from the callback function, \n
 *                                 which is triggered when data arrives in the peer SOC. \n
 *                                 The first parameter of the callback parameter represents \n
 *                                 the first address of the data buffer, and the second parameter\n
 *                                 is the length of the data
 * @param proxy_connected_callbck[in] this callback will be triggeted when the connection state of the\n
 *                                     proxy module changes.Currently only supported when the host side \n
 *                                     checks that the device side is connected,the callback will be \n
 *                                     triggered,and the parameter is 1.
 *                             
 * @note the callbakc function is not recommended to be empty, end when it is empty,\n 
 *             it will not be able to receive data from the peer.
 *                
 */
void proxy_work_prolog(void (*proxy_recv_callback)(void* , unsigned int) , void (*proxy_connected_callbck)(int) , int enable_timer);

/**
 * @brief proxy module destruction
 */
void proxy_work_epilog(void);

/**
 * @brief when data needs to be transferred to the peer SOC,\n
 *         it is transmitted through this proxy interface.
 *
 * @param payload[in] the first address of the data buffer to be transferred
 * @param payload_length[in] the length of the data to be transferred 
 * @param proxy_send_callback[in] this callback function is used to receive \n 
 *                                 the result of data transfer, and currently\n
 *                                 only supports failure return. \n
 *                                 The first parameter represents the result \n
 *                                 value(failure) and the second parameter \n
 *                                 represents the command word
 *
 *                                
 *
 * @note 1. proxy_send is an asynchronous interface;\n
 *       2. the callback function can be NULL 
 *
 * @return 0 on success , -1 on error 
 */
int proxy_send(void *payload , 
               unsigned int payload_length , 
               void (*proxy_send_callback)(int , unsigned int));

#ifdef __cplusplus
}
#endif

#endif //_PROXY_WORK_H

/**
 * @}
 */
