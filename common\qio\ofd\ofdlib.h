/*
* Copyright (C)...
*
* 文件名称： ofdlib.h
* 摘 要： ofd库的c实现
*
* 当前版本： 1.0
* 作 者 ： zbb
* 完成日期： 2018年9月21日
*/
#ifndef OFDLIB_H 
#define OFDLIB_H

#define PTOFD_SUCCESS 0
#define PTOFD_ERROR -1
#define PTOFD_ERROR_IO -2

/**
 * JPEG Info for jpegtoofd
 * @param resolution DPI for JPEG file. Set 0 default 300.
 */
typedef struct _JPEGInfo
{
    const char *path;
    int resolution;
    /// read from jpeg file
    //int width;
    //int height;
} JPEGInfo;

/**
 * Merge JPEG files to a single OFD file.
 */
int jpegtoofd(JPEGInfo images[], int image_count, const char *path);
int ofdAddJpegToOfd(JPEGInfo images[],int page);
int ofdInit(const char *rootDir);
int removeDir(const char *dirname);
int ofdAddJpegToOfdJobEnd( int count, const char *ofdPath) ;
#endif
