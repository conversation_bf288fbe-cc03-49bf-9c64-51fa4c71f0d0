//*****************************************************************************
//  Copyright (C) 2012 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/soap/soap.c#16 $
//  $Change: 199014 $ $Date: 2012/06/15 $
//
/// @file
/// SOAP (Simple Object Access Protocol) system
///
/// @ingroup Network
///
/// <AUTHOR> <<EMAIL>>
//
//*****************************************************************************
#include "nettypes.h"
#include "netmisc.h"
#include "soap.h"

#define SOAP_STATIC static
#define SOAPLOCK(t)
#define SOAPUNLOCK(t)

SOAP_STATIC char SOAPVELOPE[] =

    "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n"
    "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"\r\n"
    "%s>\r\n"                           /* add extra namespaces */
    "%s"                                /* add header, maybe */
    "<soap:Body>\r\n"
    "    <%s:%s xmlns:%s=\"%s\">\r\n"   /* add namespace:Action, namespace:namespaceurl */
    "%s"                                /* add parms list                       */
    "    </%s:%s>\r\n"                  /* add namespace:Action                 */
    "</soap:Body>\r\n"
    "</soap:Envelope>\r\n";

// ----------------------------------------------------------------------
//
SOAP_STATIC char SOAPVELOPE_NONEVALUE[] =

    "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n"
    "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"\r\n"
    "%s>\r\n"                           /* add extra namespaces */
    "%s"                                /* add header, maybe */
    "<soap:Body>\r\n"
    "    <%s:%s/>\r\n"                  /* add namespace:Action                 */
    "</soap:Body>\r\n"
    "</soap:Envelope>\r\n";

// ----------------------------------------------------------------------
//
SOAP_STATIC char SOAPFAULTVELOPE[] =

    "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n"
    "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"\r\n"
    "%s>\r\n"                           /* add extra namespaces */
    "%s"                                /* add header, maybe */
    "  <soap:Body>\r\n"
    "    <soap:Fault>\r\n"
    "      <soap:Code>\r\n"
    "        <soap:Value>%s</soap:Value>\r\n"   /* add fault code */
    "        <soap:Subcode>\r\n"
    "          <soap:Value>%s:%s</soap:Value>\r\n" /* add subcode */
    "        </soap:Subcode>\r\n"
    "      </soap:Code>\r\n"
    "      <soap:Reason>\r\n"
    "        <soap:Text xml:lang=\"en\">%s</soap:Text>\r\n" /* add English reason/explanation */
    "      </soap:Reason>\r\n"
    "    </soap:Fault>\r\n"
    "  </soap:Body>\r\n"
    "</soap:Envelope>\r\n";

static SOAP_ATTR_S* soap_create_var_attr(const char* name, const char* val)
{
    SOAP_ATTR_S* pa;

    pa = (SOAP_ATTR_S *)pi_zalloc(sizeof(SOAP_ATTR_S));
    RETURN_VAL_IF(pa == NULL, NET_WARN, NULL);

    pa->m_name = strdup(name);
    if ( pa->m_name == NULL )
    {
        NET_WARN("alloc name failed: %d<%s>", errno, strerror(errno));
        pi_free(pa);
        return NULL;
    }

    pa->m_val = strdup(val);
    if ( pa->m_val == NULL )
    {
        NET_WARN("alloc val failed: %d<%s>", errno, strerror(errno));
        pi_free(pa->m_name);
        pi_free(pa);
        return NULL;
    }

    return pa;
}

static void soap_delete_var_attr(SOAP_ATTR_S* pa)
{
    if ( pa )
    {
        if ( pa->m_next )
        {
            soap_delete_var_attr(pa->m_next);
        }
        if ( pa->m_name )
        {
            pi_free(pa->m_name);
        }
        if ( pa->m_val )
        {
            pi_free(pa->m_val);
        }
        pi_free(pa);
    }
}

static int32_t soap_format_var(SOAP_VAR_S* pvar, char* buf, int room, int level)
{
    SOAP_ATTR_S* pa;
    char    attrstr[128];
    char    tabstr[64];
    int32_t attrlen;
    int32_t tab_len = 0;
    int32_t totlen = 0;
    int32_t len;

    RETURN_VAL_IF(pvar == NULL || room < 0, NET_WARN, -1);

    for ( int32_t i = 0; i < level && tab_len + 3 < sizeof(tabstr); ++i )
    {
        tab_len += snprintf(tabstr + tab_len, sizeof(tabstr) - tab_len, "  ");
    }

    while ( pvar )
    {
        attrlen = 0;
        if ( pvar->m_attr )
        {
            for ( pa = pvar->m_attr; pa; pa = pa->m_next )
            {
                attrlen += snprintf(attrstr + attrlen, sizeof(attrstr) - attrlen, " %s=\"%s\"", pa->m_name, pa->m_val);
            }
        }
        else
        {
            attrstr[0] = '\0';
        }

        len = snprintf(buf + totlen, room, "%s<%s%s>", tabstr, pvar->m_name, attrstr);
        totlen += len;
        room   -= len;

        if ( pvar->m_child )
        {
            len = snprintf(buf + totlen, room, "\r\n");
            totlen += len;
            room -= len;

            len = soap_format_var(pvar->m_child, buf + totlen, room, level + 1);
            if (len < 0)
            {
                return len;
            }
            totlen += len;
            room -= len;
        }

        if ( pvar->m_val )
        {
            len = QXMLvalueEscape(buf + totlen, pvar->m_val, room);
            totlen += len;
            room   -= len;
        }

        len = snprintf(buf + totlen, room, "%s</%s>\r\n", (pvar->m_child ? tabstr : ""), pvar->m_name);
        totlen += len;
        room   -= len;

        pvar = pvar->m_next;
    }

    return totlen;
}

int32_t soap_format_fault(const char* extra_namespaces, const char* header, const char* code, const char* subcode_xmlns, const char* subcode, const char* reason, char* buf, size_t nbuf)
{
    snprintf(buf, nbuf, SOAPFAULTVELOPE, (extra_namespaces ? extra_namespaces : ""), (header ? header : ""), code, subcode_xmlns, subcode, reason);
    return 0;
}

int32_t soap_format_response(SOAP_VAR_S* pvar, const char* extra_namespaces, const char* action, const char* header, const char* xmlns, const char* xmlnsurl, char* buf, size_t nbuf)
{
    char*   response;
    int32_t totlen = 0;
    int32_t room;

    RETURN_VAL_IF(buf == NULL || nbuf == 0, NET_DEBUG, 0);

    // Allocate buffer for total response
    room = SOAP_MAX_RESPONSE;
    response = (char *)pi_zalloc(room * sizeof(char));
    RETURN_VAL_IF(response == NULL, NET_WARN, -1);

    if ( pvar )
    {
        totlen += soap_format_var(pvar, response, room, 3);
    }
    room = totlen + sizeof(SOAPVELOPE) + 512;

    if ( extra_namespaces )
    {
        room += strlen(extra_namespaces);
    }
    else
    {
        extra_namespaces = "";
    }

    if ( STRING_NO_EMPTY(response) )
    {
        snprintf(buf, nbuf, SOAPVELOPE, extra_namespaces, header, xmlns, action, xmlns, xmlnsurl, response, xmlns, action);
    }
    else
    {
        snprintf(buf, nbuf, SOAPVELOPE_NONEVALUE, extra_namespaces, header, xmlns, action);
    }
    pi_free(response);

    return 0;
}

SOAP_VAR_S* soap_var_append_child(SOAP_VAR_S* pvar, SOAP_VAR_S* child)
{
    RETURN_VAL_IF(pvar == NULL || child == NULL, NET_WARN, NULL);
    if (pvar->m_child == NULL)
    {
        pvar->m_child = pvar->m_child_last = child;
    }
    else
    {
        pvar->m_child_last = pvar->m_child_last->m_next = child;
    }
    return child;
}

SOAP_VAR_S* soap_create_var(const char* varname, soapDataType type, uint32_t flags, const char* ival, int32_t attr_count, ...)
{
    SOAP_ATTR_S* pattr;
    SOAP_ATTR_S* ptail;
    SOAP_VAR_S*  pvar;
    va_list      varg;
    char*        name;
    char*        val;

    RETURN_VAL_IF(varname == NULL, NET_WARN, NULL);

    pvar = (SOAP_VAR_S *)pi_zalloc(sizeof(SOAP_VAR_S));
    RETURN_VAL_IF(pvar == NULL, NET_WARN, NULL);

    pvar->m_name = strdup(varname);
    if ( pvar->m_name == NULL )
    {
        NET_WARN("alloc varname failed: %d<%s>", errno, strerror(errno));
        pi_free(pvar);
        return NULL;
    }

    if ( ival )
    {
        pvar->m_val = strdup(ival);
        if ( pvar->m_val == NULL )
        {
            NET_WARN("alloc ival failed: %d<%s>", errno, strerror(errno));
            pi_free(pvar->m_name);
            pi_free(pvar);
            return NULL;
        }
    }
    pvar->m_flags = flags;
    pvar->m_type  = type;

    va_start(varg, attr_count);
    for ( int32_t i = 0; i < attr_count; ++i )
    {
        name = va_arg(varg, char*);
        val  = va_arg(varg, char*);
        if ( name && val )
        {
            pattr = soap_create_var_attr(name, val);
            if ( pattr == NULL )
            {
                NET_WARN("create var attr failed");
                break;
            }

            if ( pvar->m_attr != NULL )
            {
                for ( ptail = pvar->m_attr; ptail->m_next != NULL; ptail = ptail->m_next );
                ptail->m_next = pattr;
            }
            else
            {
                pvar->m_attr = pattr;
            }
        }
    }
    va_end(varg);

    return pvar;
}

void soap_delete_var(SOAP_VAR_S* pvar)
{
    if ( pvar->m_type != dtfunction )
    {
        if ( pvar->m_attr )
        {
            soap_delete_var_attr(pvar->m_attr);
        }
        if ( pvar->m_child )
        {
            soap_delete_var(pvar->m_child);
        }
        if ( pvar->m_next )
        {
            soap_delete_var(pvar->m_next);
        }
        if ( pvar->m_name )
        {
            pi_free(pvar->m_name);
        }
        if ( pvar->m_val )
        {
            free(pvar->m_val);
        }
        pi_free(pvar);
    }
}

int32_t soap_walk_header(QXML_S * pxml)
{
    RETURN_VAL_IF(pxml == NULL, NET_WARN, -1);

    char *pe;
    if ((pe = QXMLnextElement(pxml)) == NULL)
    {
        NET_WARN("No elements in XML");
        return -1;
    }
    if (QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Envelope") != 0)
    {
        NET_WARN("XML payload not SOAP Envelope");
        return -1;
    }
    if ((pe = QXMLchildElement(pxml)) == NULL)
    {
        NET_WARN("No Header in XML");
        return -1;
    }
    if (QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Header") != 0)
    {
        NET_WARN("No SOAP Header in XML");
        return -1;
    }
    return 0;
}

int soap_next_element_case_equal(QXML_S * pxml, const char* elem_name)
{
    char *pe = pxml->m_pe;
    if (!pe)
    {
        pe = QXMLnextElement(pxml);
    }
    if (pe == NULL)
    {
        NET_WARN("No elements in XML");
        return -1;
    }

    return QXMLelementCaseCompare( pxml, QXMLelementName(pxml, pe), (char*)elem_name );
}

char* soap_iterate_child_elements(QXML_S * pxml, SOAP_CHILD_ELEM_S* child_elems, size_t child_elems_len)
{
    char*   pe = NULL;
    char*   pv = NULL;
    int32_t parse_level;
    char    element[64];

    RETURN_VAL_IF( pxml == NULL || child_elems == NULL || child_elems_len == 0, NET_WARN, NULL);

    for (
            pe = QXMLchildElement(pxml), parse_level = pxml->m_level;
            pe != NULL && pxml->m_level >= parse_level;
            pe = QXMLnextElement(pxml)
        )
    {
        int   match = 0;
        char  str[256];

        for ( int i = 0; i < child_elems_len; ++i)
        {
            if (child_elems[i].flg_found)
            {
                continue;
            }

            if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), child_elems[i].selector) == 0 )
            {
                child_elems[i].flg_found = 1;
                match = 1;
                pv = QXMLnextValue(pxml);
                if ( pv )
                {
                    child_elems[i].flg_val_exist = 1;
                    if (child_elems[i].dtype == dtstring)
                    {
                        QXMLvalueCopy(pxml, (char*)child_elems[i].elem, pv, child_elems[i].elem_len);
                    }
                    else
                    {
                        QXMLvalueCopy(pxml, str, pv, sizeof(str));
                    }

                    if ( child_elems[i].dtype == dtint)
                    {
                        *(int*)child_elems[i].elem = atoi(str);
                    }

                    NET_TRACE("SOAP %s has value: %s", child_elems[i].selector,
                            child_elems[i].dtype == dtstring ? (char*)child_elems[i].elem : str);
                }
                else
                {
                    NET_TRACE("SOAP %s has no value", child_elems[i].selector);
                }
                break;
            }
        }
        if (!match)
        {
           // QXMLelementCopy(pxml, element, pe, sizeof(element));
           // NET_DEBUG("Ignore Element(%s)", element);
        }
    }
    return pe;
}

int soap_find_var_in_xml(PQXML pxml, const char *pVarName)
{
    char varbuf[SOAP_MAX_ARG_NAME] = {0};
    const char *pv;
    char *pe;
    int recurse;
    int parseLevel;
    int i;

    if (! pxml || ! pVarName)
    {
        return -1;
    }
    // advance xml parsing to first element in varname, and if found, if there
    // is another level in varName, find that recursively
    //
    pv = pVarName;
    if (! pv || ! *pv)
    {
        return -2;
    }
    recurse = 0;
    do
    {
        for (i = 0; i < (SOAP_MAX_ARG_NAME - 1); i++)
        {
            if (*pv == '.')
            {
                recurse = 1;
                varbuf[i] = '\0';
                pv++;
                break;
            }
            else if(! *pv)
            {
                recurse = 0;
                varbuf[i] = '\0';
                break;
            }
            else
            {
                varbuf[i] = *pv++;
            }
        }
        parseLevel = pxml->m_level;

        // if recursing, drop to child level and look for element
        // else look for element at any level
        //
        if (recurse)
        {
            pe = QXMLchildElement(pxml);
        }
        else
        {
            pe = QXMLnextElement(pxml);
        }
        do
        {
            if (! QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), varbuf))
            {
                //NET_DEBUG("found subelement %s, recurse=%d", varbuf, recurse);

                // found the element name, if not recursing, that means we are
                // done, so return success
                //
                if (! recurse)
                {
                    return 0;
                }
                // recursing, so go get next element to find from here
                //
                break;
            }
            pe = QXMLnextElement(pxml);
        }
        while (pe && (! recurse || (parseLevel < pxml->m_level)));
    }
    while (recurse && *pv);

    // didn't find it
    //
    return -3;
}

int soap_get_var_value_from_xml(PQXML pxml, const char *pVarName, char *varValue, int varValueSize)
{
    char *pe;

    if (! pxml || ! pVarName || ! varValue || ! varValueSize)
    {
        return -1;
    }
    if (soap_find_var_in_xml(pxml, pVarName))
    {
        // not found
        //
        return -2;
    }
    // found, pxml is left at element of varName
    //
    pe = QXMLnextValue(pxml);
    if (! pe)
    {
        NET_DEBUG("No value for element %s\n", pVarName);
        return -3;
    }
    QXMLvalueCopy(pxml, varValue, pe, varValueSize);
    return 0;
}

int soap_find_child_elements(QXML_S * pxml, const char *elem_name, SOAP_CHILD_ELEM_S* child_elems, size_t child_elems_len)
{
    int ret;
    ret = soap_find_var_in_xml(pxml, elem_name);
    if (ret == 0)
    {
        pxml->m_pe = soap_iterate_child_elements(pxml, child_elems, child_elems_len);
    }
    else
    {
        NET_WARN("can not find element: %s", elem_name);
    }
    return ret;
}
