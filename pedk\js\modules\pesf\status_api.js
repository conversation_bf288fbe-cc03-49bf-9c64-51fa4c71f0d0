/**
 * @namespace pedk.device.status
 */

 var Module_Main = globalThis.pedk.Module_Main
 var Module_Sub = globalThis.pedk.Module_Sub
 var PEDK_STATUS_ID = globalThis.pedk.common.PEDK_STATUS_ID
 var STATUS_TYPE  = globalThis.pedk.common.STATUS_TYPE
 var ERROR_NO = globalThis.pedk.common.ERROR_NO
 var onpush_listeners = []
 var onremove_listeners = []


 /**
 * @class 状态数据类
 */
class StatusData {
    /**
     * 构造函数
     * @param {PEDK_STATUS_ID} id     状态ID
     * @param {Number} param1   状态参数一
     * @param {Number} param2   状态参数二
     * @param {Number} param3   状态参数三
     * @param {Object} data     状态参数数据
     * @param {Number} size     状态参数数据长度
     *
     */
    constructor(id, param1, param2, param3, data, size) {
    if(!(id in PEDK_STATUS_ID) ) {
        throw TypeError('EINVALIDPARAM')
    }

    this.id = id;
    this.param1 = param1;
    this.param2 = param2;
    this.param3 = param3;
    this.data = data;
    this.size = size;
    }
}

const statusTypeMap = [
    ['STATUS_ID_TYPE_INFO', 0],
    ['STATUS_ID_TYPE_WARN', 1],
    ['STATUS_ID_TYPE_ERROR', 2],
    ['STATUS_ID_TYPE_FATAL', 3]
];
const statusModuleMap = [
    ['MODULE_PRINT', 1],
    ['MODULE_SCAN', 2],
    ['MODULE_COPY', 3],
    ['MODULE_FAX', 4],
    ['MODULE_PANEL', 5],
    ['MODULE_NET', 6],
    ['MODULE_USB', 7],
    ['MODULE_PLATFORM', 8],
    ['MODULE_COMMON', 9],
    ['MODULE_FWUPDATE', 10],
    ['MODULE_POWERMGR', 11]
];
const statusPriorityMap = [
    ['eSTATUS_PRI_MAX', 99],
    ['eSTATUS_PRI_FATAL', 86],
    ['eSTATUS_PRI_INFO', 80],
    ['eSTATUS_PRI_CONFIRM', 75],
    ['eSTATUS_PRI_ERROR', 65],
    ['eSTATUS_PRI_ERROR_LOW', 63],
    ['eSTATUS_PRI_JOB', 60],
    ['eSTATUS_PRI_WARNING', 55],
    ['eSTATUS_PRI_NORMAL', 50],
    ['eSTATUS_PRI_MIN', 1]
];
function getStatusTypeMapping(src, flag) {
    var i = 0;
    for (i = 0; i < statusTypeMap.length; i++) {

        if(src === statusTypeMap[i][flag]) {
            if(flag === 0) {
                return statusTypeMap[i][1];
            } else {
                return statusTypeMap[i][0];
            }
        }
    }
}
function getStatusModuleMapping(src, flag) {
    var i = 0;
    for (i = 0; i < statusModuleMap.length; i++) {

        if(src === statusModuleMap[i][flag]) {
            if(flag === 0) {
                return statusModuleMap[i][1];
            } else {
                return statusModuleMap[i][0];
            }
        }
    }
}
function getStatusPriorityMapping(src, flag) {
    var i = 0;
    for (i = 0; i < statusPriorityMap.length; i++) {

        if(src === statusPriorityMap[i][flag]) {
            if(flag === 0) {
                return statusPriorityMap[i][1];
            } else {
                return statusPriorityMap[i][0];
            }
        }
    }
}

/**
 * 取得当前升起的指定类型的状态ID列表长度
 * @since V1.0
 * @param {Number} type 状态类型
 * @returns {Number} 状态ID列表长度
 *
 * @example
 * console.log(getStatusIdListLength(STATUS_ID_TYPE_INFO));    // 2
 */
function getStatusIdListLength(type) {
/*
    if(typeof type === 'number' ) {
        type = getStatusTypeMapping(type, 1);
    }
    console.log("getStatusIdListLength "+type+"; flag "+(type in STATUS_TYPE));
*/
    if(!(type in STATUS_TYPE)) {
        return ERROR_NO.EINVALIDPARAM
    }

    return getStatusIdListLengthFromMfp(type);
}

/**
 * 取得当前升起的指定类型的状态ID列表
 * @since V1.0
 * @param {Number} type 状态类型
 * @returns {Array} 状态ID列表
 *
 * @example
 * console.log(getStatusIdList(STATUS_ID_TYPE_INFO));    // PEDK_SID_I_PRINT_READY
 */
function getStatusIdList(type) {
/*
    if(typeof type === 'number' ) {
        type = getStatusTypeMapping(type, 1);
    }
    console.log("getStatusIdList "+type+"; flag "+(type in STATUS_TYPE));
*/
    if(!(type in STATUS_TYPE)) {
        return ERROR_NO.EINVALIDPARAM
    }

    var id_info = getStatusIdListFromMfp(type);
    if(id_info) {
        id_info = id_info.slice(0, -1);
        var id_list = id_info.split(" ");
        return id_list;
    }
    return "";
}

/**
 * 取得当前升起的指定类型的最高优先级状态ID
 * @since V1.0
 * @param {Number} type 状态类型
 * @returns {PEDK_STATUS_ID} 升起的最高优先级状态ID
 *
 * @example
 * console.log(getTopStatusId(STATUS_ID_TYPE_INFO));    // PEDK_SID_I_PRINT_READY
 */
function getTopStatusId(type) {
/*
    if(typeof type === 'number' ) {
        type = getStatusTypeMapping(type, 1);
    }
    console.log("getTopStatusId "+type+"; flag "+(type in STATUS_TYPE));
*/
    if(!(type in STATUS_TYPE)) {
       return ERROR_NO.EINVALIDPARAM
    }

    return getTopStatusIdFromMfp(type);
}

/**
 * 取得状态的类型
 * @since V1.0
 * @param {PEDK_STATUS_ID} id 状态ID
 * @returns {Number} 状态类型
 *
 * @example
 * console.log(getTopStatusId(PEDK_SID_I_PRINT_READY));    // STATUS_ID_TYPE_INFO
 */
function getStatusType(id) {
    //console.log("getStatusType "+id+"; flag "+(id in PEDK_STATUS_ID));
    if(!(id in PEDK_STATUS_ID)) {
        return ERROR_NO.EINVALIDPARAM
    }

    var type = getStatusTypeFromMfp(id);
    if(type) {
        //return getStatusTypeMapping(type, 0);
        return type
    } else {

        return ERROR_NO.EOPNOTSUPP;
    }
}

/**
 * 取得状态的所属模块
 * @since V1.0
 * @param {PEDK_STATUS_ID} id 状态ID
 * @returns {Number} 所属模块
 *
 * @example
 * console.log(getStatusModule(PEDK_SID_I_PRINT_READY));     // MODULE_PRINT
 */
function getStatusModule(id) {
    //console.log("getStatusModule "+id+"; flag "+(id in PEDK_STATUS_ID));
    if(!(id in PEDK_STATUS_ID)) {
       return ERROR_NO.EINVALIDPARAM
    }

    var module = getStatusModuleFromMfp(id);
    if(module) {
        //return getStatusModuleMapping(module, 0);
        return module
    } else {
        return ERROR_NO.EOPNOTSUPP;
    }
}

/**
 * 取得状态的表示优先级
 * @since V1.0
 * @param {PEDK_STATUS_ID} id 状态ID
 * @returns {Number} 取得状态的表示优先级
 *
 * @example
 * console.log(getStatusModule(PEDK_SID_I_PRINT_READY));     // eSTATUS_PRI_NORMAL
 */
function getStatusPriority(id) {
    //console.log("getStatusPriority "+id+"; flag "+(id in PEDK_STATUS_ID));
    if(!(id in PEDK_STATUS_ID)) {
        return ERROR_NO.EINVALIDPARAM
    }
    var priority = getStatusPriorityFromMfp(id);
    if(priority) {
        //return getStatusPriorityMapping(priority, 0);
        return priority;
    } else {
        return ERROR_NO.EOPNOTSUPP;
    }
}
function strToJson(str) {
    var json = eval("(" + str + ")");

    return json;
}
function updateStatus(obj,respond,Status) {
    var newStatus = strToJson(Status);
    var newStatusData = new StatusData(newStatus.id, newStatus.param1, newStatus.param2, newStatus.param3, newStatus.data, newStatus.size);
    for(const callback of onpush_listeners) {
        callback(newStatusData);
    }
}

function updateStatus_remove(obj,respond,Status) {
    var newStatus = strToJson(Status);
    var newStatusData = new StatusData(newStatus.id, newStatus.param1, newStatus.param2, newStatus.param3, newStatus.data, newStatus.size);
    for(const callback of onremove_listeners) {
        callback(newStatusData);
    }
}
function isListener(listener, callback) {
    if(callback === listener) {
        return false;
    }
    else {
        return true;
    }
}
function addOnPushStatusListener (callback) {
    if (typeof callback !== 'function') {
        return ERROR_NO.EINVALIDPARAM
    }

    let instance = new globalThis.pedk.ObserverManager();
    instance.addListeners(this, updateStatus, Module_Main.MSG_MODULE_STATUS, Module_Sub.MSG_STATUS_SUB_NOTIFY_PUSH);
    onpush_listeners.push(callback);

    return true;
}

function removeOnPushStatusListener (callback) {
    if (typeof callback !== 'function') {
        return ERROR_NO.EINVALIDPARAM
    }
    let len = onpush_listeners.length;
    onpush_listeners = onpush_listeners.filter(listener => isListener(listener, callback));
    if(len === onpush_listeners.length || len === 0) {
        return false;
    }
    else {
        return true;
    }
}

function addOnRemoveStatusListener (callback) {
    if (typeof callback !== 'function') {
       return ERROR_NO.EINVALIDPARAM
    }
    let instance = new globalThis.pedk.ObserverManager();
    instance.addListeners(this, updateStatus_remove, Module_Main.MSG_MODULE_STATUS, Module_Sub.MSG_STATUS_SUB_NOTIFY_REMOVE);
    onremove_listeners.push(callback)

    return true;
}
function removeOnRemoveStatusListener (callback) {
    if (typeof callback !== 'function') {
        return ERROR_NO.EINVALIDPARAM
    }
    let len = onremove_listeners.length;

    onremove_listeners = onremove_listeners.filter(listener => isListener(listener, callback));

    if(len === onremove_listeners.length || len === 0) {
        return false;
    }
    else {
        return true;
    }
}
globalThis.pedk.device.status.StatusData = StatusData
globalThis.pedk.device.status.getStatusIdList = getStatusIdList
globalThis.pedk.device.status.getStatusIdListLength = getStatusIdListLength
globalThis.pedk.device.status.getTopStatusId = getTopStatusId
globalThis.pedk.device.status.getStatusType = getStatusType
globalThis.pedk.device.status.getStatusModule = getStatusModule
globalThis.pedk.device.status.getStatusPriority = getStatusPriority
globalThis.pedk.device.status.addOnPushStatusListener = addOnPushStatusListener
globalThis.pedk.device.status.removeOnPushStatusListener = removeOnPushStatusListener
globalThis.pedk.device.status.addOnRemoveStatusListener = addOnRemoveStatusListener
globalThis.pedk.device.status.removeOnRemoveStatusListener = removeOnRemoveStatusListener

