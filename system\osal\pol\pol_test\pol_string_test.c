
/**************************************************************
  Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
  module name :  	POL (PANTUM OS LAYER)  selftest 
  file   name :	io_test.c 
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-27
description	:   selftest  for io system interface 
 ****************************************************************/
#define _GNU_SOURCE
#include <assert.h>
//#include <pol/pol.h>
#include "pol/pol_string.h"

#include "pol_test.h"
#define STRING_ERR_HINT "[STRING-TEST]"      

static int32_t should_return_negative_number_if_strlen_err()
{
	size_t size = pi_strlen(NULL);
	if (size > 0) {
		perror(STRING_ERR_HINT);
	}

	return PASS;
}

static int32_t should_return_positive_number_if_strlen_ok()
{
	char buf[36] = "test string";
	size_t size = pi_strlen(buf);
	if (size <= 0) {
		perror(STRING_ERR_HINT);
	}
	assert(size > 0);

	return PASS;
}

static int32_t should_return_negative_number_if_strncat_err()
{
	void *point = pi_strncat(NULL, NULL, 0);
	
	return PASS;
}

static int32_t should_return_positive_number_if_strncat_ok()
{
	char dest[36] = "test string";
	char src[36] = "test string";

	void *point = pi_strncat(dest, src, sizeof(src));
	if (point == NULL) {
		perror(STRING_ERR_HINT);
	}
	
	assert(point != NULL);
	return PASS;
}

static int32_t should_return_negative_number_if_strncpy_err()
{
	void *point = pi_strncpy(NULL, NULL, 0);
	return PASS;
}

static int32_t should_return_positive_number_if_strncpy_ok()
{
	char dest[36] = {0};
	char src[36] = "test string";

	void *point = pi_strncpy(dest, src, sizeof(src));
	if (point == NULL) {
		perror(STRING_ERR_HINT);
	}
	
	assert(point != NULL);
	return PASS;
}

static int32_t should_return_negative_number_if_strcmp_err()
{
	int32_t ret = pi_strcmp(NULL, NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_strcmp_ok()
{
	char dest[36] = "test string";
	char src[36] = "test string";

	int32_t ret = pi_strcmp(dest, src);

	assert(ret == 0);

	return PASS;
}

static int32_t should_return_negative_number_if_strncmp_err()
{
	int32_t ret = pi_strncmp(NULL, NULL, 0);
	return PASS;
}

static int32_t should_return_positive_number_if_strncmp_ok()
{
	char dest[36] = "test string";
	char src[36] = "test string";

	int32_t ret = pi_strncmp(dest, src, pi_strlen("test string"));

	assert(ret == 0);
	
	return PASS;
}

static int32_t should_return_negative_number_if_strncasecmp_err()
{
	int32_t ret = pi_strncasecmp(NULL, NULL, 0);
	return PASS;
}

static int32_t should_return_positive_number_if_strncasecmp_ok()
{
	char dest[36] = "test string";
	char src[36] = "Test String";

	int32_t ret = pi_strncasecmp(dest, src, pi_strlen("test string"));

	assert(ret == 0);
	
	return PASS;
}

static int32_t should_return_negative_number_if_strstr_err()
{
	char *point = pi_strstr(NULL, NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_strstr_ok()
{

	char haystack[36] = "test string";
	char needle[36] = "string";

	char *point = pi_strstr(haystack, needle);
	if (point == NULL) {
		perror(STRING_ERR_HINT);
	}

	assert(point != NULL);
	return PASS;
}


static int32_t should_return_negative_number_if_strcasestr_err()
{
	char *point = pi_strcasestr(NULL, NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_strcasestr_ok()
{

	char haystack[36] = "test string";
	char needle[36] = "string";

	char *point = pi_strcasestr(haystack, needle);
	if (point == NULL) {
		perror(STRING_ERR_HINT);
	}

	assert(point != NULL);
	return PASS;
}

static int32_t should_return_negative_number_if_snprintf_err()
{
	char buf[36];

    int32_t ret = pi_snprintf(buf, 0, NULL);
	if (ret < 0) {
		perror(STRING_ERR_HINT);
	}
	assert(ret  < 0);

	return PASS;
}

static int32_t should_return_positive_number_if_snprintf_ok()
{
	char buf[36] = {0};

    int32_t ret = pi_snprintf(buf, 36, "%s", "test string");

	if (ret < 0) {
		perror(STRING_ERR_HINT);
	}
	assert(ret > 0);

	return PASS;
}

static int32_t should_return_negative_number_if_strcasecmp_err()
{
	pi_strcasecmp(NULL, NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_strcasecmp_ok()
{
	char s1[36] = "test";
	char s2[36] = "test string";

	char *s2 = malloc(sizeof(char) * 36);
	assert(s2 != NULL);
	strcpy(s2,"test string");
	int32_t ret = strcasecmp(s1, s2);
	free(s2);
	assert(ret < 0);
	return PASS;
}

static int32_t should_return_negative_number_if_strdup_err()
{
	char *ret = pi_strdup(NULL);
	if (ret == NULL) {
		perror(STRING_ERR_HINT);
	}
	assert(ret == NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_strdup_ok()
{
	char *str = "test string";
	char *ret = pi_strdup(str);
	if (ret == NULL) {
		perror(STRING_ERR_HINT);
	}
	assert(ret != NULL);
	free(ret);

	return PASS;
}

static int32_t should_return_negative_number_if_asprintf_err()
{
	int32_t ret = pi_asprintf(NULL, "%s", "test");
	if (ret < 0) {
		perror(STRING_ERR_HINT);
	}

	assert(ret < 0);

	return PASS;
}

static int32_t should_return_positive_number_if_asprintf_ok()
{
	char *p;
	int32_t ret = pi_asprintf(&p, "%s", "test");
	if (ret < 0) {
		perror(STRING_ERR_HINT);
	}

	assert(ret > 0);
	free(p);
	return PASS;
}

TEST_ITEM_S string_test_pool[] = {
	{"pi_strlen_err", should_return_negative_number_if_strlen_err},
	{"pi_strlen", should_return_positive_number_if_strlen_ok},
	{"pi_strncat_err", should_return_negative_number_if_strncat_err},
	{"pi_strncat", should_return_positive_number_if_strncat_ok},
	{"pi_strncpy_err", should_return_negative_number_if_strncpy_err},
	{"pi_strncpy", should_return_positive_number_if_strncpy_ok},
	{"pi_strcmp_err", should_return_negative_number_if_strcmp_err},
	{"pi_strcmp", should_return_positive_number_if_strcmp_ok},
	{"pi_strncmp_err", should_return_negative_number_if_strncmp_err},
	{"pi_strncmp", should_return_positive_number_if_strncmp_ok},
	{"pi_strncasecmp_err", should_return_negative_number_if_strncasecmp_err},
	{"pi_strncasecmp", should_return_positive_number_if_strncasecmp_ok},
	{"pi_strstr_err", should_return_negative_number_if_strstr_err},
	{"pi_strstr", should_return_positive_number_if_strstr_ok},
	{"pi_strcasestr_err", should_return_negative_number_if_strcasestr_err},
	{"pi_strcasestr", should_return_positive_number_if_strcasestr_ok},
	{"pi_snprintf_err", should_return_negative_number_if_snprintf_err},
	{"pi_snprintf", should_return_positive_number_if_snprintf_ok},
	{"pi_strcasecmp_err", should_return_negative_number_if_strcasecmp_err},
	{"pi_strcasecmp", should_return_positive_number_if_strcasecmp_ok},
	{"pi_strdup_err", should_return_negative_number_if_strdup_err},
	{"pi_strdup", should_return_positive_number_if_strdup_ok},
	{"pi_asprintf_err", should_return_negative_number_if_asprintf_err},
	{"pi_asprintf", should_return_positive_number_if_asprintf_ok},
};

TEST_POOL_S string_pool = {
    "string",
    string_test_pool,
    sizeof(string_test_pool)/sizeof(TEST_ITEM_S),
};

