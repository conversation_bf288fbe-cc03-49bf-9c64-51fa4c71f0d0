/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_file.c
 * @addtogroup panel_dc
 * @{
 * @brief DC accesses the UI file interface for pedk
 * <AUTHOR>
 * @date 2025-06-23
 */
#include "mfp_to_panel_file.h"
#include "panel_file.h"

int32_t mfp_to_panel_file_access( const char* pathname, int32_t mode, int32_t* err_num )
{
    return panel_file_access( pathname, mode, err_num );
}

off_t mfp_to_panel_file_seek( int32_t fd, off_t offset, int32_t whence, int32_t* err_num )
{
    return panel_file_seek( fd, offset, whence, err_num );
}

int32_t mfp_to_panel_file_open( const char* pathname, int32_t flags, mode_t mode, int32_t* err_num )
{
    return panel_file_open( pathname, flags, mode, err_num );
}

ssize_t mfp_to_panel_file_read( int32_t fd, void *buf, size_t count, int32_t* err_num )
{
    return panel_file_read( fd, buf, count, err_num );
}

ssize_t mfp_to_panel_file_write( int32_t fd, const void* buf, size_t count, int32_t* err_num )
{
    return panel_file_write( fd, buf, count, err_num );
}


int32_t mfp_to_panel_file_close( int32_t fd, int32_t* err_num )
{
    return panel_file_close( fd, err_num );
}

 /**
  * @}
  */



