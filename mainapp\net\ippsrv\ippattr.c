#include "nettypes.h"
#include "netmodules.h"
#include "netsts.h"
#include "ippprot.h"
#include "ippx.h"

typedef struct tag_ipp_service
{
    const char* name;           ///< name of service
    uint32_t    type;           ///< type of service
    IPP_ATTR_S* printer_attributes;
}
IPP_SERVICE, *PIPP_SERVICE;

typedef struct ipp_attribute_context
{
    NET_CTX_S*  net_ctx;
    PI_MUTEX_T  mutex;
}
IPP_ATTR_CTX_S;

static IPP_ATTR_CTX_S*  s_ippattr_ctx = NULL;

static IPP_SERVICE      s_ipp_printer;

#define ATTRLOCK()          { if (s_ippattr_ctx) pi_mutex_lock(s_ippattr_ctx->mutex);   }
#define ATTRUNLOCK()        { if (s_ippattr_ctx) pi_mutex_unlock(s_ippattr_ctx->mutex); }

#define IPP_TAG_COLLECTION  IPP_TAG_BEGIN_COLLECTION

#define PAPERSIZE_4x6_WIDTH     1016   // 4*6纸型的宽，单位10mm
#define PAPERSIZE_4x6_HEIGHT    1524   // 4*6纸型的高，单位10mm

/* hack to pretend we have legal paper for faxout, even if printer
   doesn't support it. keep legal at the end of all paper table type
   things so it can be removed from attribute lists if needed
*/
#define have_PaperSize_PS8P5X14IN 3
#define PaperSize_PS8P5X14IN 1234589

typedef struct
{
    int  module_status;
    int  status_type;
}IppPrintStatusMap;

// ranges are encoded as strings X,Y internally, and the max X is 32 bits, so
// the max range string is 22 decimal digits inclusing comma and null term
#define IPP_RANGE_SIZE 24

/**
 * @brief   检查输入的参数type是否是Int类型
 *
 * @param   type            类型
 *
 * @return  返回检测结果
 * @return  <ul><li>1:是Int类型</li><li>0:不是Int类型</li></ul>
 */
static int IsIntType(int type)
{
    switch (type)
    {
    case IPP_TAG_INTEGER:
    case IPP_TAG_BOOLEAN:
    case IPP_TAG_ENUM:
    case IPP_TAG_NOVALUE:
        return 1;
    case IPP_TAG_RANGE:
    case IPP_TAG_RESOLUTION:
    case IPP_TAG_STRING:
    case IPP_TAG_DATE:
    case IPP_TAG_TEXT:
    case IPP_TAG_NAME:
    case IPP_TAG_KEYWORD:
    case IPP_TAG_URI:
    case IPP_TAG_URISCHEME:
    case IPP_TAG_CHARSET:
    case IPP_TAG_LANGUAGE:
    case IPP_TAG_MIMETYPE:
    case IPP_TAG_BEGIN_COLLECTION:
    case IPP_TAG_END_COLLECTION:
    case IPP_TAG_MEMBERNAME:
        return 0;
    default:
        NET_WARN("Not a valid type: %d", type);
        return 1;
    }
}

/**
 * @brief   检测属性值是否是Int类型
 *
 * @param   pa          IPP属性结构体
 *
 * @return  返回检测结果
 * @return  <ul><li>1:是Int类型</li><li>0:不是Int类型或传入的参数为NULL</li></ul>
 */
static int AttributeIsIntType(PIPP_ATTR pa)
{
    return pa ? IsIntType(pa->type) : 0;
}

/**
 * @brief   检查输入参数type是否是String类型
 *
 * @param   type            类型
 *
 * @return  返回检测结果
 * @return  <ul><li>1:是String类型</li><li>0:不是String类型</li></ul>
 */
static int IsStringType(int type)
{
    switch (type)
    {
    case IPP_TAG_INTEGER:
    case IPP_TAG_BOOLEAN:
    case IPP_TAG_ENUM:
    case IPP_TAG_NOVALUE:
        return 0;
    case IPP_TAG_MIMETYPE:
    case IPP_TAG_STRING:
    case IPP_TAG_TEXT:
    case IPP_TAG_NAME:
    case IPP_TAG_KEYWORD:
    case IPP_TAG_URI:
    case IPP_TAG_URISCHEME:
    case IPP_TAG_CHARSET:
    case IPP_TAG_LANGUAGE:
    case IPP_TAG_RESOLUTION:
    case IPP_TAG_RANGE:
    case IPP_TAG_DATE:
    case IPP_TAG_MEMBERNAME:
        return 1;
    case IPP_TAG_BEGIN_COLLECTION:
    case IPP_TAG_END_COLLECTION:
        return 0;
    default:
        NET_WARN("Not a valid type: %d", type);
        return 1;
    }
}

/**
 * @brief   检测属性值是否是String类型
 *
 * @param   pa          IPP属性结构体
 *
 * @return  返回检测结果
 * @return  <ul><li>1:是String类型</li><li>0:不是String类型或传入的参数为NULL</li></ul>
 */
static int AttributeIsStringType(PIPP_ATTR pa)
{
    return pa ? IsStringType(pa->type) : 0;
}

/**
 * @brief   检测属性值是否是Collection(集合)类型
 *
 * @param   pa          IPP属性结构体
 *
 * @return  返回检测结果
 * @return  <ul><li>1:是Collection类型</li><li>0:不是Collection类型</li></ul>
 */
static int AttributeIsCollectionType(PIPP_ATTR pa)
{
    return (pa->type == IPP_TAG_COLLECTION) ? 1 : 0;
}

/**
 * @brief   获取当前字符串中有多少个值(以"\4"为分隔符进行分割)
 *
 * @param   str         字符串变量指针
 *
 * @return  返回字符串中值的个数
 */
static int AttributeStringValueCount(const char* str)
{
    int count;

    for (count = *str ? 1 : 0; str && *str; str++)
    {
        //字符为"\4"并且后面还有值，则count加1
        if (*str == 4 && *(str + 1))
        {
            count++;
        }
    }
    return count;
}

/**
 * @brief   将指定的value
 *
 * @param   str         src属性对应的value字符串
 * @param   index       指定value字符串中的第index个value(从0开始)
 * @param   substr      存放取出字符串的指针
 * @param   nsubstr     单个value字符串的最大长度
 *
 * @return  返回处理结果
 * @return  <ul><li>0:成功</li><li>1:str为NULL</li><li>2:index超出str中属性值的个数</li></ul>
 */
static int GetIndexedString(const char* str, int index, char* substr, int nsubstr)
{
    const char* pv = NULL;
    int32_t     maxdex;
    int32_t     i = 0;

    RETURN_VAL_IF(substr == NULL || nsubstr <= 0, NET_WARN, -1);
    *substr = '\0';

    RETURN_VAL_IF(STRING_IS_EMPTY(str), NET_NONE, 1);

    //获取当前字符串地址中有多少个value,value之间以"/4"作为分隔符
    maxdex = AttributeStringValueCount(str);
    if ( index >= maxdex )
    {
        NET_DEBUG("No string at str(%s) index<%d> maxdex(%d)", str, index, maxdex);
        return 2;
    }
    pv = str;

    //循环定位指定index的value，将pv指针偏移到指定value字符串的首地址
    while (*pv && (i < index))
    {
        if (*pv++ == 4)
        {
            i++;
        }
    }
    //循环一个字节一个字节的将指定的value拷贝到substr中
    for (i = 0; i < (nsubstr - 1) && (*pv != '\0') && (*pv != '\4'); i++)
    {
        substr[i] = *pv++;
    }
    substr[i] = '\0';
    return 0;
}

/**
 * @brief   获取集合的size
 *
 * @param   pa              ipp attributes结构体指针
 * @param   colname         属性的名称
 *
 * @return  返回size大小
 */
int32_t IPPATTRcollectionSize(PIPP_ATTR pa, const char *colname)
{
    IPP_ATTR_S* pm = NULL;
    int32_t     member, members;
    int32_t     total = 0;

    RETURN_VAL_IF(pa == NULL, NET_WARN, 0);

    //判断当前属性是否是集合型
    RETURN_VAL_IF(AttributeIsCollectionType(pa) == 0, NET_INFO, 0);
    RETURN_VAL_IF(IS_ATTR_ARRAY(pa), NET_INFO, 0);

    NET_TRACE("Size for collection(%s)", colname);
    //若pa->var不为空，pa->varsize为0，那么说明当前集合是对另一个集合的引用
    //pa->var是引用的集合指针地址，所以改为获取引用集合的varsize大小
    while (pa->varsize == 0 && pa->var != NULL)
    {
        NET_TRACE("indirect to cur name(%s)", pa->name ? pa->name : "<noname>");
        pa = (IPP_ATTR_S *)pa->var;
        NET_TRACE("indirect to var name(%s)", pa->name ? pa->name : "<noname>");
    }

    RETURN_VAL_IF(pa == NULL || pa->varsize == 0, NET_WARN, 0);

    members = pa->varsize;
    total = sizeof(IPP_ATTR_S);

    for (pm = ++pa, member = 0; member < members; member++, pm++)
    {
        if (AttributeIsCollectionType(pm))
        {
            // if the member is a collection, recursively get its length
            //
            total += IPPATTRcollectionSize(pm, pm->name);
        }
        else
        {
            total += sizeof(IPP_ATTR_S) + pm->varsize;
        }
    }

    NET_TRACE("============= is %d", total);
    return total;
}

//**************************************************************************
int SkipOverCollection(PIPP_ATTR *ppa)
{
    PIPP_ATTR pa;
    int member, members;
    int rc;

    pa = *ppa;
    while (pa && AttributeIsCollectionType(pa))
    {
        if (pa->varsize == 0 || pa->var)
        {
            *ppa = ++pa;
            return 0;
        }
        members = pa->varsize;
        for (member = 0, pa++; member < members; member++, pa++)
        {
            if (AttributeIsCollectionType(pa))
            {
                rc = SkipOverCollection(&pa);
                if (rc)
                {
                    return rc;
                }
            }
        }
    }
    *ppa = pa;
    return 0;
}

//**************************************************************************
static int IPPATTRcopyCollectionMembers(char *pdst, const char *pdstname, PIPP_ATTR *ppsrc, int maxlen)
{
    PIPP_ATTR pm, psrc;
    char *pd;
    int member, members, used;
    int isIndirect;

    if (! pdst || ! ppsrc || ! *ppsrc)
    {
        return -1;
    }
    psrc = *ppsrc;

    if (! AttributeIsCollectionType(psrc))
    {
        NET_WARN("Not a collection");
        return -2;
    }
    // remember name of top-collection
    //
    if (! pdstname)
    {
        pdstname = psrc->name;
    }
    if (! pdstname)
    {
        pdstname = "<noname>";
    }
    isIndirect = 0;
    while (psrc->varsize == 0 && psrc->var != NULL)
    {
        // if there is a 0 varsize, and a var ptr, then this collection is a
        // reference to another, so get its size instead
        //
        isIndirect = 1;
        psrc = (IPP_ATTR_S *)psrc->var;
        //NET_TRACE("indirect to name<%s>", psrc->name ? psrc->name : "<noname>");
    }
    if (! psrc)
    {
        NET_WARN("No collection");
        return -1;
    }
    members = psrc->varsize;
    if  (! members)
    {
        // no point to include 0 size collections
        //
        NET_WARN("Warning - 0 size collection omitted");
        // next item is next regardless of if this is indirect or not
        *ppsrc = psrc + 1;
        return 0;
    }
    // remember starting position in dst to return bytes used
    //
    pd = pdst;

    // flatten collection structure into one sequential
    // list of attributes including all sub-collections inline
    //
    if (maxlen < ((members + 1) * sizeof(IPP_ATTR_S)))
    {
        NET_WARN("No room for collection entry for %s", psrc->name);
        return -1;
    }
    // copy collection header to dest
    //
    memcpy(pdst, psrc, sizeof(IPP_ATTR_S));
    //NET_WARN("AddC %s at %p", pdstname, pdst);

    // replace name in dest cause it could have been indirected
    // to a nameless collection
    //
    pm = (IPP_ATTR_S *)pdst;
    pm->name = (char *)pdstname;
    SET_ATTR(pm);

    if (pm->maxlen)
    {
        if (pm->maxlen > maxlen)
        {
            // this happens if a collection with a wider element
            // is copied to this one with a smaller expected stride
            // and will result in errors if the actual copied value is larger, so
            // this is just a warning as it could actually fit
            //
            NET_WARN("dest collection %s has smaller stride %d than source %s of %d",
                    pdstname, maxlen, psrc->name, pm->maxlen);
        }
        if (maxlen)
        {
            // in all cases, limit this collection's element size
            // to what the caller said, as the caller alloced for it
            //
            pm->maxlen = maxlen;
        }
    }
    else
    {
        pm->maxlen = maxlen;
    }
    // if there is no stride for array types then assume this is
    // an initializer that hasn't set maxlen and complain
    //
    if (IS_ATTR_ARRAY(pm) && ! pm->maxlen)
    {
        NET_WARN("no stride for %s", pm->name);
    }
    if (pm->var || ! pm->varsize)
    {
        NET_WARN("Copied an indirection");
    }
    maxlen -= sizeof(IPP_ATTR_S);
    pdst += sizeof(IPP_ATTR_S);

    // now copy all members
    //
    for (pm = ++psrc, member = 0; member < members; member++)
    {
        SET_ATTR(pm);
        if (AttributeIsCollectionType(pm))
        {
            // if the member is a collection, recursively copy its members in
            // this handles both indirect and flat collectons, so pm is
            // passed in, then back and points to the first attr after the
            // collection in the passed in list
            //
            used = IPPATTRcopyCollectionMembers(pdst, pm->name, &pm, maxlen);
            if (used < 0)
            {
                return used;
            }
            maxlen -= used;
            pdst += used;
        }
        else
        {
            if (maxlen < sizeof(IPP_ATTR_S))
            {
                NET_WARN("No room for collection entry for %s", psrc->name);
                return -1;
            }
            /*
            if (AttributeIsIntType(pm))
            {
                NET_WARN("AddM %s [%d] at %p", pm->name, *(int*)pm->var, pdst);
            }
            else
            {
                NET_WARN("AddM %s [%s] at %p", pm->name, (char*)pm->var, pdst);
            }
            */
            memcpy(pdst, pm, sizeof(IPP_ATTR_S));
            maxlen -= sizeof(IPP_ATTR_S);
            pdst += sizeof(IPP_ATTR_S);
            pm++;
        }
    }
    if (isIndirect)
    {
        // if an indirect collection passed in the next item in caller's list
        // is the next sequential item
        //
        *ppsrc = *ppsrc + 1;
    }
    else
    {
        // wasn't indirect, so next item is where we left off here
        //
        *ppsrc = pm;
    }
    // return amount of destination area we used
    //
    return (pdst - pd);
}

//**************************************************************************
int IPPATTRcopyCollection(char *pdst, const char *pdstname, PIPP_ATTR psrc, int maxlen)
{
    PIPP_ATTR pm;
    int member, members, used;

    if (! pdst || ! psrc)
    {
        return -1;
    }
    if (! AttributeIsCollectionType(psrc))
    {
        NET_WARN("Not a collection");
        return -2;
    }
    // pass one: flatten collection structure into one sequential
    // list of attributes including all sub-collections inline
    // this is recursive and doesn't change the var pointers
    //
    used = IPPATTRcopyCollectionMembers(pdst, pdstname, &psrc, maxlen);
    if (used <= 0)
    {
        return used;
    }
    // advance to data area in destination
    //
    psrc = (IPP_ATTR_S *)pdst;
    pdst += used;
    maxlen -= used;

    // pass two: copy any member variable's values and point to
    // the copied values in the copied member attribute
    //
    // at this point, pdst points at the end of the copied list of member
    // attributes and maxlen is the remaining free space size, and pd
    // points to a flat list.

    // calculate the number of ipp-attrs in the flat list, so we
    // avoid having to recurse into sub-collections
    //
    members = used / sizeof(IPP_ATTR_S);

    for (pm = psrc, member = 0; member < members; member++, pm++)
    {
        if (AttributeIsCollectionType(pm))
        {
            // just make sure it's not an indirection, the header
            // has no associated data
            //
            if (pm->var || ! pm->varsize)
            {
                NET_WARN("Copy of collection is indirect or 0 size");
                return -1;
            }
        }
        else
        {
            // a member var, copy its original bytes to the copy location
            //
            if (maxlen < pm->varsize)
            {
            		//by wdq: this error seems trivial, so just ignore it
                //NET_WARN("No room for member data for %s", pm->name);
                return -1;
            }
            if( IsStringType(pm->type) )
            {
                strcpy(pdst, pm->var);
            }
            else
            {
                memcpy(pdst, pm->var, pm->varsize);
            }

            // update this attribute's var storage to copy area
            //
            pm->var = (void*)pdst;
            maxlen -= pm->varsize;
            pdst += pm->varsize;
            /*
            if (AttributeIsIntType(pm))
            {
                NET_DEBUG("CopyM %s [%d] at %p", pm->name, *(int*)pm->var, pdst);
            }
            else
            {
                NET_DEBUG("CopyM %s [%s] at %p", pm->name, (char*)pm->var, pdst);
            }
            */
        }
    }
    return 0;
}

//**************************************************************************
static int32_t IPPATTRgetAttribute(PIPP_ATTR ptable, const char* tablename, const char* name, int32_t index, int32_t* type, void** value, int32_t* count)
{
    PIPP_ATTR pa;
    char *pvar;
    int offset;

    ATTRLOCK();
    for (pa = ptable; pa->name; pa++)
    {
        if (! strcmp(pa->name, name))
        {
            *type = pa->type;

            if (IS_ATTR_ARRAY(pa))
            {
                *count = pa->curdim;
                if (index >= pa->curdim)
                {
                    NET_DEBUG("Only %d entries for %s in %s", pa->curdim, name, tablename);
                    ATTRUNLOCK();
                    return IPP_BAD_REQUEST;
                }
                offset = pa->maxlen * index;
                if (AttributeIsCollectionType(pa))
                {
                    if ((offset + pa->maxlen) > (pa->maxlen * IPP_MAX_COLLECTION_SET))
                    {
                        NET_WARN("Index %d of %s exceeds max index %d", index, name, IPP_MAX_COLLECTION_SET);
                        ATTRUNLOCK();
                        return IPP_BAD_REQUEST;
                    }
                }
                else
                {
                    if ((offset + pa->maxlen) > pa->varsize)
                    {
                        NET_WARN("Index %d of %s exceeds storage of %d", index, name, pa->varsize);
                        ATTRUNLOCK();
                        return IPP_INTERNAL_ERROR;
                    }
                }
                pvar = (char *)pa->var + offset;
            }
            else
            {
                *count = 1;
                pvar = (char *)pa->var;
            }
            *(char **)value = pvar;
            ATTRUNLOCK();
            return 0;
        }
    }
    NET_WARN("Requested %s Attribute %s not in table", tablename, name);
    ATTRUNLOCK();

    return IPP_ATTRIBUTES;
}

static int32_t ippattr_set_attribute(PIPP_ATTR ptable, const char* tablename, const char* name, int32_t index, void* value)
{
    PIPP_ATTR pa;
    int maxlen;

    // must be called inside ATTRLOCK()
    for (pa = ptable; pa->name; pa++)
    {
        if (! strcmp(pa->name, name))
        {
            char *pvar;

            if (IS_ATTR_ARRAY(pa))
            {
                int offset;

                if (pa->maxlen <= 0)
                {
                    NET_WARN("No stride for array %s", pa->name);
                    return IPP_INTERNAL_ERROR;
                }
                offset = pa->maxlen * index;
                if (AttributeIsCollectionType(pa))
                {
                    if ((offset + pa->maxlen) > (pa->maxlen * IPP_MAX_COLLECTION_SET))
                    {
                        //NET_WARN("Index %d of %s exceeds max index %d", index, name, IPP_MAX_COLLECTION_SET);
                        return IPP_INTERNAL_ERROR;
                    }
                }
                else
                {
                    if ((offset + pa->maxlen) > pa->varsize)
                    {
                        //NET_WARN("Index %d of %s exceeds storage of %d", index, name, pa->varsize);
                        return IPP_INTERNAL_ERROR;
                    }
                }
                if (index > (pa->curdim + 1))
                {
                    NET_DEBUG("Out-of-sequence setting of %s at index %d", name, index);
                }
                pa->curdim = index + 1;
                maxlen = pa->maxlen;
                pvar = (char *)pa->var + offset;
            }
            else
            {
                if (index)
                {
                    NET_DEBUG("%s is not an array type, index %d ignored", name, index);
                }
                if (pa->maxlen > 0)
                {
                    maxlen = pa->maxlen;
                    if (! AttributeIsCollectionType(pa) && (maxlen > pa->varsize))
                    {
                        NET_WARN("maxlen %d is > varsize %d", maxlen, pa->varsize);
                        return IPP_INTERNAL_ERROR;
                    }
                }
                else
                {
                    if (AttributeIsCollectionType(pa))
                    {
                        // a collection's varsize is its member count, but it could
                        // have sub-members and storage for sub-member variables, so
                        // recursivly get its actual size needs and pad a bit. This
                        // should have already been set when the attribute set was made.
                        //
                        maxlen = IPPATTRcollectionSize(pa, name);
                    }
                    else
                    {
                        maxlen = pa->varsize;
                    }
                }
                pvar = (char *)pa->var;
            }
            if (AttributeIsIntType(pa))
            {
                if (maxlen < sizeof(int))
                {
                    NET_WARN("Can't set %s: room < %d", name, sizeof(int));
                    return IPP_INTERNAL_ERROR;
                }
                *(int *)pvar = *(int *)value;
            }
            else if (AttributeIsStringType(pa))
            {
                char *src = (char *)value;

                if (! src)
                {
                    // let NULL ptr = empty string
                    src = "";
                }
                if (maxlen < strlen(src))
                {
                    NET_WARN("Can't set %s: room %d < %d", name, maxlen, strlen(src));
                    return IPP_INTERNAL_ERROR;
                }
                strncpy(pvar, (char*)value, maxlen - 1);
                pvar[maxlen - 1] = '\0';
            }
            else if (AttributeIsCollectionType(pa))
            {
                int rc;

                rc = IPPATTRcopyCollection(pvar, pa->name, (IPP_ATTR_S *)value, maxlen);
                if (rc)
                {
                    //NET_WARN("Can't set collection %s", name);
                    return IPP_INTERNAL_ERROR;
                }
            }
            else
            {
                NET_WARN("Bad type for %s", name);
                return -3;
            }
            // indicate attribute has been set
            //
            SET_ATTR(pa);
            return IPP_OK;
        }
    }
    NET_DEBUG("attribute %s not in %s", name, tablename);

    return IPP_ATTRIBUTES;
}

static int32_t ippattr_set_attribute_locked(PIPP_ATTR ptable, const char* tablename, const char* name, void* value, int32_t index)
{
    int rc;

    ATTRLOCK();
    rc = ippattr_set_attribute(ptable, tablename, name, index, value);
    ATTRUNLOCK();

    return rc;
}

/* Printer Description / Operation Attributes */
static char s_ipp_language_tags[32]         = "zh-cn";
static char s_printer_uri_supported[IPP_MAX_URI * 4];
static char s_uri_authentication_supported[32 * 4]  = "none";
static char s_uri_security_supported[32 * 4]        = "none";

static char s_ipp_versions_supported[32]    = "2.0\4""1.1\4""1.0";
static char s_ipp_printername[128]          = "Pantum";
static int  s_ipp_printerstate              = IPP_PRINTER_IDLE;
static char s_ipp_printerstate_reasons[512] = "none";
static char s_ipp_printerstate_message[256] = "none";
static int  s_ipp_accepting                 = 1;
static int  s_ipp_zero                      = 0;
static int  s_ipp_one                       = 1;
static int  s_ipp_uptime                    = 1;
static char s_ipp_current_time[32]          = "4100000000000000000000"; /* octet encoded */
static int  s_ipp_multiop_timeout           = IPP_MULTIOP_TIMEOUT;
static char s_multiop_timeout_action[32]    = "abort-job";
static int  s_operations_supported[]        = {
    IPP_PRINT_JOB,
    IPP_VALIDATE_JOB,
    IPP_CREATE_JOB,
    IPP_SEND_DOCUMENT,
    IPP_CANCEL_JOB,
    IPP_GET_JOB_ATTRIBUTES,
    IPP_GET_JOBS,
    IPP_GET_PRINTER_ATTRIBUTES,
    IPP_IDENTIFY_PRINTER
};

static char s_ipp_features_supported[128]   = "airprint-2.1\4airprint-1.8\4airprint-1.7\4airprint-1.6\4airprint-1.5\4airprint-1.5";

static char s_1284deviceID[1284] =
"MFG:Pantum;CMD:"
#if CONFIG_PDF
"DW-PS,DW-PCL,"
#endif
"URF;MDL:-Printer;CLS:PRINTER;DES:Pantum;";

static char s_ipp_charset_configured[64]    = "utf-8";
static char s_ipp_charset_supported[64]     = "utf-8";
static char s_ipp_lang_configured[16]       = "en";
static char s_ipp_genlang_supported[64]     = "en";
static char s_ipp_docformat_supported[256];
static char s_ipp_docformat_default[128];
static char s_pdl_override_supported[64]    = "attempted";

#ifdef IN_GZIP
static char s_compression_supported[64]     = "gzip";
#else
static char s_compression_supported[64]     = "none";
#endif
static int s_pages_per_minute_color         = 20;

static int s_pages_per_minute               = 34;

static char s_mopria_certified[4]           = "2.2";

static char s_printer_location[128]         = "none";
static char s_printer_info[128]             = "Pantum IPP printer";
static char s_printer_more_info[IPP_MAX_URI]= "http://www.pantum.com";
static char s_printer_make_and_model[128]   = "Pantum IPP printer";
static char s_printer_uuid[IPP_MAX_URI]     = "urn:uuid:6861d9b0-a100-11e0-8264-acc51bef133f"; // filled in by mdns code

static int  s_pdf_fit_to_page = 0;

/** Job Template Attributes
 */
static int s_job_priority_default           = 1;
static int s_job_priority_supported         = 1;

static char s_job_holduntil_default[16]     = "no-hold";
static char s_job_holduntil_supported[128]  = "no-hold";

static char s_job_sheets_default[16]        = "none";
static char s_job_sheets_supported[64]      = "none\4""standard";

static int s_copies_default                 = 1;
static char s_copies_supported[16]          = "1,99";
static int  s_job_page_set                  = 0;

static int s_finishings_default             = 3;
static int s_finishings_supported[128];

static char s_media_default[IPP_MAX_NAME]   =  "na_letter_8.5x11in";
static char s_media_supported[64*IPP_MAX_MEDIA] =
"iso_a4_210x297mm"
"\4na_letter_8.5x11in"
;
static char s_media_ready[32*IPP_MAX_MEDIA] = {"na_letter_8.5x11in"};

static char s_sides_default[32]             = "one-sided";

static char s_sides_supported[128]          =
"one-sided"
"\4two-sided-short-edge"
"\4two-sided-long-edge"
;

static int s_orientation_req_default        = 7; // none
static int s_orientation_req_supported[]    = { 3, 4, 5, 6, 7 };

static char s_multidoc_handling_default[64] = "separate-documents-collated-copies";
static char s_multidoc_handling_supported[256]=
"separate-documents-collated-copies"
"\4separate-documents-uncollated-copies"
;


static char s_output_bin_default[8]         = "top";
static char s_output_bin_supported[32]      = "top";

static int s_print_quality_default          = 4; // normal
static int s_print_quality_supported[]      = {  4 };

// resolutions are 9 byte strings Uint32-xres+Uint32-yres+3
static char s_printer_resolution_default[12]= "600x600x3";

static char s_printer_resolution_supported[48]= "600x600x3";

// stuff a client can put in the "jobs" group of a request, so each one
// of these has to appear in the operation attributes, and is used to
// initialize the job attribute table when it is created for printjob
static char s_job_creation_attr_supported[512] =
"copies"
"\4finishings"
"\4finishings-col"
"\4ipp-attribute-fidelity"
"\4job-name"
"\4media"
"\4media-col"
"\4orientation-requested"
"\4output-bin"
"\4print-color-mode"
"\4print-quality"
"\4printer-resolution"
#if 1
"\4multiple-document-handling"
#endif
#if CONFIG_PDF
"\4page-ranges"
#endif
"\4print-scaling"
"\4sides"
;

static char s_pwg_raster_res_supported[64]  = { "300x300x3" "\4" "600x600x3" };
static char s_pwg_raster_type_supported[128]=
"black_8"
"\4sgray_8"
#ifdef IN_COLOR
"\4rgb_8"
"\4adobe-rgb_8"
"\4srgb_8"
#endif
;

#if CONFIG_PDF
static int s_page_ranges_supported          = 1;
// units are kbytes, so 64mb/1024 = 65536

//按照airprint规格，pdf需要的内存必须大于32MB，应该大于64MB，但目前固件最大支持35MB
//调整该值，否则会导致渲染后大于35MB的文档无法打印，如bug4657 ，修改后airprint驱动会协商成urf进行下发 2016.9.9  zw

static char s_pdf_k_octets_supported[]      = "0,65535";
//static char s_pdf_k_octets_supported[]      = "0,35000";

static char s_overrides_supported[256]      =
"pages"
"\4document-numbers"
"\4document-copies"
"\4media"
"\4media-col";
#endif

#if CONFIG_JPEG
// units are kbytes, so 16mb/1024 = 15625
static char s_jpeg_k_octets_supported[8]    = "0,15625";
static char s_jpeg_x_dim_supported[8]       = "0,65535";
static char s_jpeg_y_dim_supported[8]       = "0,65535";
static char s_jpeg_features_supported[128]  = "none";
#endif
static int s_preferred_landscape_orientation= 5;
static char s_print_scaling_supported[128]  = "auto\4auto-fit\4fill\4fit\4none";
static char s_print_scaling_default[64]     = "auto"; /* auto is required default for AAP */

//彩机属性
#if CONFIG_COLOR
static int s_color_supported                = 1;
static int s_marker_highlevels[4]           = { 100,100,100,100 };
static int s_marker_lowlevels[4]            = { 10,10,10,10  };
static int s_marker_levels[4]               = { 50,50,50,50  };
// used by color mode and output mode (output mode is usually commented out)
static char s_output_mode_default[32]       = "color";
static char s_output_mode_supported[128]    = "auto""\4color""\4auto-monochrome""\4monochrome";
// toner stuff, very port specific
static char s_marker_colors[32]             = "#00FFFF""\4#FF00FF""\4#FFFF00""\4#000000";
static char s_marker_names[32]              = "Cyan""\4Magenta""\4Yellow""\4Black";
static char s_marker_types[32]              = "toner""\4toner""\4toner""\4toner";
#else
//黑白机属性
static int s_color_supported                = 0;
static int s_marker_highlevels[]            = { 100 };
static int s_marker_lowlevels[]             = { 10 };
static int s_marker_levels[]                = { 50 };
static char s_output_mode_default[32]       = "monochrome";
static char s_output_mode_supported[128]    = "auto""\4auto-monochrome""\4monochrome";
static char s_marker_colors[32]             = "#000000";
static char s_marker_names[32]              = "Black";
static char s_marker_types[32]              = "toner";
#endif

static char s_pdf_versions_supported[128];

static char s_which_jobs_supported[128]     =
"completed"
"\4not-completed"
;

static char s_printer_icons[IPP_MAX_URI * 4];

static char s_media_source_supported[32*IPP_MAX_MEDIA];
// trays are octet-strings see  PWG5100.13
static char s_printer_input_tray[(IPP_MAX_NAME * IPP_MAX_MEDIA) *2];
static char s_printer_output_tray[(IPP_MAX_NAME * IPP_MAX_MEDIA)*2]; ///< no port for output tray count?
static char s_printer_finisher[(IPP_MAX_NAME * IPP_MAX_MEDIA)*2];
static char s_printer_finisher_description[128] = "";

static char s_media_type[32]                = "auto";
static char s_media_type_supported[256]     = "auto""\4stationery""\4stationery-heavyweight""\4labels";

static int  s_margins[32]                   = { 423 };

static char s_printer_dns_sd[256]           = ""; // filled in by mdns code
static char s_urf_supported[10240]          = ""; // filled in by mdns code

static char s_printer_fw_name[128]          = "IPP\4""JPEG\4""URF\4""FirmwareVersion";
static char s_printer_fw_patches[128]       = " \4"" \4"" \4"" ";
static char s_printer_fw_str_version[128];
static char s_printer_fw_version[128]; // octet string encoded

static char s_identify_actions_supported[128];
static char s_identify_actions_default[128] = "flash";

static char s_printer_kind[256]             = "document";
static char s_printer_supply_uri[IPP_MAX_URI]= "http://www.pantum.com";
static char s_printer_geo_location[IPP_MAX_URI]= "geo://unknown";

static char s_print_content_supported[]     =
{
    "auto"
};

static int  s_ipp_duplex_supported          = 1; //增加双面选项反馈,默认为双面支持
static int  s_ipp_duplex_unsupported        = 0; //小纸型不支持双面打印，设置duplex-supported为0


static char s_media_col_supported[256]      =
"media-size"
"\4media-source"
"\4media-type"
"\4media-top-margin"
"\4media-left-margin"
"\4media-right-margin"
"\4media-bottom-margin"
"\4duplex-supported"  //增加双面选项反馈，AirFax时设置为0 2016.7.14 zw
;

static int s_ipp_base_margin                = 423;
// size of one media size flattened in memory. note that a regular
// media-size has 2 int members, but it *could* be a custom
// size that has 2 int-range members, so we have to plan for that
// so make sure any media-size collections use range-size as
// the var-size, not sizeof(int) if they are used as templates
// or prototypes like the letter size is used all over
//
#define MEMBERNAME_SIZE 24

#define IPP_MEDIA_SIZE_SIZE      ((2 * IPP_RANGE_SIZE) + (2 * MEMBERNAME_SIZE) + (5 * sizeof(IPP_ATTR_S)))

// the size of one media collection (max) if flattened in memory
// the collecton contains 4 ints, a keyword, and a subcollection
// of media-size
#define IPP_MEDIA_COL_SIZE      (IPP_MEDIA_SIZE_SIZE + (4 * IPP_RANGE_SIZE) + (1 * IPP_MAX_NAME) + (8 * MEMBERNAME_SIZE) + (17 * sizeof(IPP_ATTR_S)))

// these are collections, so are a bit trickier

// the letter media collection. note that this collection is also used as the
// prototype/initializer for higher level collections which is why the sizes of
// the keyword elements are the max size, not size they actually are
static char x_dimension_str[MEMBERNAME_SIZE] = "x-dimension";
static char y_dimension_str[MEMBERNAME_SIZE] = "y-dimension";
static char media_type_str[MEMBERNAME_SIZE] = "media-type";
static char media_size_str[MEMBERNAME_SIZE] = "media-size";
static char media_source_str[MEMBERNAME_SIZE] = "media-source";
static char media_top_margin_str[MEMBERNAME_SIZE] = "media-top-margin";
static char media_left_margin_str[MEMBERNAME_SIZE] = "media-left-margin";
static char media_right_margin_str[MEMBERNAME_SIZE] = "media-right-margin";
static char media_bottom_margin_str[MEMBERNAME_SIZE] = "media-bottom-margin";
static char duplex_supported_str[MEMBERNAME_SIZE] = "duplex-supported";

/*******************************    Finishings    *****************************/
static char s_finishings_col_supported[MEMBERNAME_SIZE] = "finishing-template";
static char s_punching_config = 0;
static char s_stitching_angle_supported = 0;
static char s_stitching_method_supported[256] = "auto""\4auto""\4auto";
static char s_finishings_template_supported[1024] ="";

#define finishing_col_len MEMBERNAME_SIZE

#define DEFINE_FINISH_STRUCT(finsiher_anme) \
    static IPP_ATTR_S s_finishings_col_##finsiher_anme[] =\
    {\
        {NULL,  IPP_TAG_COLLECTION, ATTR_REQUIRED, NULL, 2 },\
        {NULL,  IPP_TAG_MEMBERNAME, ATTR_REQUIRED, &s_finishings_col_supported, sizeof(s_finishings_col_supported), MEMBERNAME_SIZE },\
        {NULL,  IPP_TAG_KEYWORD,    ATTR_REQUIRED, &s_finish_str_##finsiher_anme, sizeof(s_finish_str_##finsiher_anme), IPP_MAX_NAME },\
    };

#define DEFINE_FINISH_COL(finsiher_anme, finsiher_val) \
    static char s_finish_str_##finsiher_anme[finishing_col_len] = #finsiher_val;\
    DEFINE_FINISH_STRUCT(finsiher_anme)

DEFINE_FINISH_COL(none, none)
DEFINE_FINISH_COL(staple, staple)
DEFINE_FINISH_COL(punch, punch)
DEFINE_FINISH_COL(saddle_stitch, saddle-stitch)
DEFINE_FINISH_COL(fold, fold)
DEFINE_FINISH_COL(staple_top_left, staple-top-left)
DEFINE_FINISH_COL(staple_bottom_left, staple-bottom-left)
DEFINE_FINISH_COL(staple_top_right, staple-top-right)
DEFINE_FINISH_COL(staple_bottom_right, staple-bottom-right)
DEFINE_FINISH_COL(staple_dual_left, staple-dual-left)
DEFINE_FINISH_COL(staple_dual_top, staple-dual-top)
DEFINE_FINISH_COL(staple_dual_right, staple-dual-right)
DEFINE_FINISH_COL(staple_dual_bottom, staple-dual-bottom)
DEFINE_FINISH_COL(punch_dual_left, punch-dual-left)
DEFINE_FINISH_COL(punch_dual_top, punch-dual-top)
DEFINE_FINISH_COL(punch_dual_right, punch-dual-right)
DEFINE_FINISH_COL(punch_dual_bottom, punch-dual-bottom)
DEFINE_FINISH_COL(punch_quad_left, punch-quad-left)
DEFINE_FINISH_COL(punch_quad_top, punch-quad-left)
DEFINE_FINISH_COL(punch_quad_right, laminate)
DEFINE_FINISH_COL(punch_quad_bottom, trim-after-pages)
DEFINE_FINISH_COL(fold_letter, fold-letter)
DEFINE_FINISH_COL(fold_half, fold-half)
DEFINE_FINISH_COL(fold_z, fold-z)
DEFINE_FINISH_COL(job_offset, jog-offset)

static IPP_ATTR_S* s_finishings_col_database[48] =
{
    s_finishings_col_none,
    NULL
};

#define FINISH_DATAS_MAP(XX)                                                                 \
    XX(3,   NONE,                 none,                s_finishings_col_none)                \
    XX(4,   STAPLE,               staple,              s_finishings_col_staple)              \
    XX(5,   PUNCH,                punch,               s_finishings_col_punch)               \
    XX(8,   SADDLE_STITCH,        saddle-stitch,       s_finishings_col_saddle_stitch)       \
    XX(10,  FOLD,                 fold,                s_finishings_col_fold)                \
    XX(20,  STAPLE_TOP_LEFT,      staple-top-left,     s_finishings_col_staple_top_left)     \
    XX(21,  STAPLE_BOTTOM_LEFT,   staple-bottom-left,  s_finishings_col_staple_bottom_left)  \
    XX(22,  STAPLE_TOP_RIGHT,     staple-top-right,    s_finishings_col_staple_top_right)    \
    XX(23,  STAPLE_BOTTOM_RIGHT,  staple-bottom-right, s_finishings_col_staple_bottom_right) \
    XX(28,  STAPLE_DUAL_LEFT,     staple-dual-left,    s_finishings_col_staple_dual_left)    \
    XX(29,  STAPLE_DUAL_TOP,      staple-dual-top,     s_finishings_col_staple_dual_top)     \
    XX(30,  STAPLE_DUAL_RIGHT,    staple-dual-right,   s_finishings_col_staple_dual_right)   \
    XX(31,  STAPLE_DUAL_BOTTOM,   staple-dual-bottom,  s_finishings_col_staple_dual_bottom)  \
    XX(74,  PUNCH_DUAL_LEFT,      punch-dual-left,     s_finishings_col_punch_dual_left)     \
    XX(75,  PUNCH_DUAL_TOP,       punch-dual-top,      s_finishings_col_punch_dual_top)      \
    XX(76,  PUNCH_DUAL_RIGHT,     punch-dual-right,    s_finishings_col_punch_dual_right)    \
    XX(77,  PUNCH_DUAL_BOTTOM,    punch-dual-bottom,   s_finishings_col_punch_dual_bottom)   \
    XX(82,  PUNCH_QUAD_LEFT,      punch-quad-left,     s_finishings_col_punch_quad_left)     \
    XX(83,  PUNCH_QUAD_TOP,       punch-quad-top,      s_finishings_col_punch_quad_top)      \
    XX(84,  PUNCH_QUAD_RIGHT,     punch-quad-right,    s_finishings_col_punch_quad_right)    \
    XX(85,  PUNCH_QUAD_BOTTOM,    punch-quad-bottom,   s_finishings_col_punch_quad_bottom)   \
    XX(93,  FOLD_HALF,            fold-half,           s_finishings_col_fold_half)           \
    XX(96,  FOLD_LETTER,          fold-letter,         s_finishings_col_fold_letter)         \
    XX(100, FOLD_Z,               fold-z,              s_finishings_col_fold_z)              \

enum finish_datas
{
#define XX(num, name, string, val) FINISH_DATAS_##name = num,
FINISH_DATAS_MAP(XX)
#undef XX
};

const char* get_finishings_col_template_str(enum finish_datas s)
{
    switch ( s )
    {
    #define XX(num, name, string, val) case FINISH_DATAS_##name: return #string;
        FINISH_DATAS_MAP(XX)
    #undef XX
        default: return NULL;
    }
}

const void* get_finishings_col_data(enum finish_datas s)
{
    switch ( s )
    {
    #define XX(num, name, string, val) case FINISH_DATAS_##name: return val;
        FINISH_DATAS_MAP(XX)
    #undef XX
        default: return NULL;
    }
}

int32_t ippattr_get_finishings_col_num(IPP_ATTR_S* finishings_col_attr)
{
    IPP_ATTR_S* pa = NULL;
    int32_t     value = 0;
    int32_t     i = 0;

    RETURN_VAL_IF(finishings_col_attr == NULL, NET_WARN, -1);

    while ( finishings_col_attr->varsize == 0 && finishings_col_attr->var )
    {
        finishings_col_attr = (IPP_ATTR_S *)finishings_col_attr->var;
    }

    RETURN_VAL_IF(finishings_col_attr == NULL || finishings_col_attr->varsize == 0, NET_WARN, -1);

    i = finishings_col_attr->varsize;
    NET_DEBUG("pcol->varsize(%d)", i);
    pa = finishings_col_attr;
    while ( i-- > 0 )
    {
        pa++;
        if ( (pa->var != NULL) && (strcmp((char *)(pa->var), "finishing-template") == 0) )
        {
            pa++;
            NET_DEBUG("set media-type(%s)", (char *)(pa->var));
            break;
        }
    }
    NET_DEBUG("pa (%s)", (char *)(pa->var));

    #define XX(num, name, string, val)        \
        if ( strcmp(pa->var, #string) == 0 )  \
        {                                     \
            return num;                       \
        }
        FINISH_DATAS_MAP(XX)
    #undef XX
    return FINISH_DATAS_NONE;
}

int32_t ippattr_set_finishings_col(int32_t finishing_num, int32_t index)
{
    RETURN_VAL_IF(get_finishings_col_template_str(finishing_num) == NULL, NET_WARN, -1);
    RETURN_VAL_IF(get_finishings_col_data(finishing_num) == NULL, NET_WARN, -1);

    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "finishing-template-supported", (void*)get_finishings_col_template_str(finishing_num), index);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "finishings-col-database", (void*)get_finishings_col_data(finishing_num), index);

    //NET_DEBUG("finishing_num(%d)", finishing_num);
    //NET_DEBUG("finishings_col_template_str (%s)", get_finishings_col_template_str(finishing_num));

    return 0;
}

void IPPATTRfinishStringToProperty(int32_t finishings_num, int32_t job_orientation, PARSER_DATA_S* parser_data)
{
    if(parser_data == NULL)
    {
        return;
    }
    NET_INFO("finishings_num (%d) (%d)", finishings_num, job_orientation);

    if ( job_orientation == 0 )
    {
        switch ( finishings_num )
        {
            case FINISH_DATAS_STAPLE_TOP_LEFT :     finishings_num = 22; break;
            case FINISH_DATAS_STAPLE_BOTTOM_LEFT :  finishings_num = 23; break;
            case FINISH_DATAS_STAPLE_TOP_RIGHT :    finishings_num = 20; break;
            case FINISH_DATAS_STAPLE_BOTTOM_RIGHT : finishings_num = 21; break;
            case FINISH_DATAS_STAPLE_DUAL_LEFT :    finishings_num = 30; break;
            case FINISH_DATAS_STAPLE_DUAL_TOP :     finishings_num = 30; break;
            case FINISH_DATAS_STAPLE_DUAL_RIGHT :   finishings_num = 28; break;
            case FINISH_DATAS_STAPLE_DUAL_BOTTOM :  finishings_num = 29; break;
            case FINISH_DATAS_PUNCH_DUAL_LEFT :     finishings_num = 76; break;
            case FINISH_DATAS_PUNCH_DUAL_TOP :      finishings_num = 77; break;
            case FINISH_DATAS_PUNCH_DUAL_RIGHT :    finishings_num = 74; break;
            case FINISH_DATAS_PUNCH_DUAL_BOTTOM :   finishings_num = 75; break;
            case FINISH_DATAS_PUNCH_QUAD_LEFT :     finishings_num = 84; break;
            case FINISH_DATAS_PUNCH_QUAD_TOP :      finishings_num = 85; break;
            case FINISH_DATAS_PUNCH_QUAD_RIGHT :    finishings_num = 82; break;
            case FINISH_DATAS_PUNCH_QUAD_BOTTOM :   finishings_num = 83; break;
            default :
            NET_INFO("the finishings val is not suppoort(%d)", finishings_num);
            break;
        }
    }
    NET_INFO("finishings_num (%d) (%d)", finishings_num, job_orientation);

    switch ( finishings_num )
    {
        case FINISH_DATAS_STAPLE :              parser_data->staple_mode = STAPLE_MODE_TOP_LEFT_AUTO; break;
        case FINISH_DATAS_PUNCH :               parser_data->punch_mode = PUNCH_LEFT_TWO_HOLES; break;
        case FINISH_DATAS_FOLD :                parser_data->fold_mode = FOLD_MODE_2; break;
        case FINISH_DATAS_SADDLE_STITCH  :      parser_data->fold_mode = FOLD_MIDDLE_2_STAPLE; break;
        case FINISH_DATAS_STAPLE_TOP_LEFT :     parser_data->staple_mode = STAPLE_MODE_TOP_LEFT_AUTO; break;
        case FINISH_DATAS_STAPLE_BOTTOM_LEFT :  parser_data->staple_mode = STAPLE_MODE_BOTTOM_LEFT_AUTO; break;
        case FINISH_DATAS_STAPLE_TOP_RIGHT :    parser_data->staple_mode = STAPLE_MODE_TOP_RIGHT_AUTO; break;
        case FINISH_DATAS_STAPLE_BOTTOM_RIGHT : parser_data->staple_mode = STAPLE_MODE_BOTTOM_RIGHT_AUTO; break;
        case FINISH_DATAS_STAPLE_DUAL_LEFT :    parser_data->staple_mode = STAPLE_MODE_DOUBLE_LEFT; break;
        case FINISH_DATAS_STAPLE_DUAL_TOP :     parser_data->staple_mode = STAPLE_MODE_DOUBLE_TOP; break;
        case FINISH_DATAS_STAPLE_DUAL_RIGHT :   parser_data->staple_mode = STAPLE_MODE_DOUBLE_RIGHT; break;
        case FINISH_DATAS_STAPLE_DUAL_BOTTOM :  parser_data->staple_mode = STAPLE_MODE_DOUBLE_BOTTOM; break;
        case FINISH_DATAS_PUNCH_DUAL_LEFT :     parser_data->punch_mode = PUNCH_LEFT_TWO_HOLES; break;
        case FINISH_DATAS_PUNCH_DUAL_TOP :      parser_data->punch_mode = PUNCH_TOP_TWO_HOLES; break;
        case FINISH_DATAS_PUNCH_DUAL_RIGHT :    parser_data->punch_mode = PUNCH_RIGHT_TWO_HOLES; break;
        case FINISH_DATAS_PUNCH_DUAL_BOTTOM :   parser_data->punch_mode = PUNCH_DOWN_TWO_HOLES; break;
        case FINISH_DATAS_PUNCH_QUAD_LEFT :     parser_data->punch_mode = PUNCH_LEFT_FOUR_HOLES; break;
        case FINISH_DATAS_PUNCH_QUAD_TOP :      parser_data->punch_mode = PUNCH_TOP_FOUR_HOLES; break;
        case FINISH_DATAS_PUNCH_QUAD_RIGHT :    parser_data->punch_mode = PUNCH_RIGHT_FOUR_HOLES; break;
        case FINISH_DATAS_PUNCH_QUAD_BOTTOM :   parser_data->punch_mode = PUNCH_DOWN_FOUR_HOLES; break;
        case FINISH_DATAS_FOLD_HALF :           parser_data->fold_mode = FOLD_MODE_2; break;
        case FINISH_DATAS_FOLD_LETTER :         parser_data->fold_mode = FOLD_MODE_3; break;
        case FINISH_DATAS_FOLD_Z:               parser_data->fold_mode = FOLD_MODE_Z; break;
        default :
        NET_INFO("the finishings val is not suppoort(%d)", finishings_num);
        break;
    }
}

/**********************************    Letter    ********************************/
//  width:21590     height:27940
static int  s_mz_letter_x       = 21590;
static int  s_mz_letter_y       = 27940;
static char s_letter_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_letter[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_letter_x,             sizeof(s_mz_letter_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_letter_y,             sizeof(s_mz_letter_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_letter[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_letter,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_letter_tray,             sizeof(s_letter_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    A3    ********************************/
//  width:29700     height:42000
static int  s_mz_a3_x       = 29700;
static int  s_mz_a3_y       = 42000;
static char s_a3_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_a3[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a3_x,                 sizeof(s_mz_a3_x),              IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a3_y,                 sizeof(s_mz_a3_y),              IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_a3[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_a3,           0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_a3_tray,                 sizeof(s_a3_tray),                  IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    A4    ********************************/
//  width:21000     height:29700
static int  s_mz_a4_x       = 21000;
static int  s_mz_a4_y       = 29700;
static char s_a4_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_a4[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a4_x,                 sizeof(s_mz_a4_x),              IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a4_y,                 sizeof(s_mz_a4_y),              IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_a4[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_a4,           0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_a4_tray,                 sizeof(s_a4_tray),                  IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    A5    ********************************/
//  width:14800     height:21000
static int  s_mz_a5_x       = 14800;
static int  s_mz_a5_y       = 21000;
static char s_a5_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_a5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a5_x,                 sizeof(s_mz_a5_x),              IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a5_y,                 sizeof(s_mz_a5_y),              IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_a5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_a5,           0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_a5_tray,                 sizeof(s_a5_tray),                  IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    JIS B4    ********************************/
//  width:25700     height:36400
static int  s_mz_jis_b4_x       = 25700;
static int  s_mz_jis_b4_y       = 36400;
static char s_jis_b4_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_jis_b4[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_jis_b4_x,             sizeof(s_mz_jis_b4_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_jis_b4_y,             sizeof(s_mz_jis_b4_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_jis_b4[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_jis_b4,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_jis_b4_tray,             sizeof(s_jis_b4_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    JIS B5    ********************************/
//  width:18200     height:25700
static int  s_mz_jis_b5_x       = 18200;
static int  s_mz_jis_b5_y       = 25700;
static char s_jis_b5_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_jis_b5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_jis_b5_x,             sizeof(s_mz_jis_b5_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_jis_b5_y,             sizeof(s_mz_jis_b5_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_jis_b5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_jis_b5,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_jis_b5_tray,             sizeof(s_jis_b5_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    ISO B5    ********************************/
//  width:17600     height:25000
static int  s_mz_iso_b5_x       = 17600;
static int  s_mz_iso_b5_y       = 25000;
static char s_iso_b5_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_iso_b5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_iso_b5_x,             sizeof(s_mz_iso_b5_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_iso_b5_y,             sizeof(s_mz_iso_b5_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_iso_b5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_iso_b5,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_iso_b5_tray,             sizeof(s_iso_b5_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Ledger 11x17    ********************************/
//  width:27940     height:43180
static int  s_mz_ledger_x       = 27940;
static int  s_mz_ledger_y       = 43180;
static char s_ledger_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_ledger[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_ledger_x,             sizeof(s_mz_ledger_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_ledger_y,             sizeof(s_mz_ledger_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_ledger[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_ledger,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_ledger_tray,             sizeof(s_ledger_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

#ifdef have_PaperSize_PS8P5X14IN
/**********************************    Legal    ********************************/
//  width:21590     height:35560
static int  s_mz_legal_x       = 21590;
static int  s_mz_legal_y       = 35560;
static char s_legal_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_legal[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_legal_x,              sizeof(s_mz_legal_x),           IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_legal_y,              sizeof(s_mz_legal_y),           IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_legal[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_legal,        0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_legal_tray,              sizeof(s_legal_tray),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};
#endif

/**********************************    Folio    ********************************/
//  width:21600     height:33000
static int  s_mz_folio_x       = 21600;
static int  s_mz_folio_y       = 33000;
static char s_folio_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_folio[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_folio_x,              sizeof(s_mz_folio_x),           IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_folio_y,              sizeof(s_mz_folio_y),           IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_folio[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_folio,        0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_folio_tray,              sizeof(s_folio_tray),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Oficio    ********************************/
//  width:21590     height:34036
static int  s_mz_oficio_x       = 21590;
static int  s_mz_oficio_y       = 34036;
static char s_oficio_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_oficio[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_oficio_x,             sizeof(s_mz_oficio_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_oficio_y,             sizeof(s_mz_oficio_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_oficio[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_oficio,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_oficio_tray,             sizeof(s_oficio_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Executive    ********************************/
//  width:18415     height:26670
static int  s_mz_executive_x       = 18415;
static int  s_mz_executive_y       = 26670;
static char s_executive_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_executive[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_executive_x,          sizeof(s_mz_executive_x),       IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_executive_y,          sizeof(s_mz_executive_y),       IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_executive[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_executive,    0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_executive_tray,          sizeof(s_executive_tray),           IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Statement    ********************************/
//  width:13970     height:21590
static int  s_mz_statement_x       = 13970;
static int  s_mz_statement_y       = 21590;
static char s_statement_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_statement[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_statement_x,          sizeof(s_mz_statement_x),       IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_statement_y,          sizeof(s_mz_statement_y),       IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_statement[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_statement,    0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_statement_tray,          sizeof(s_statement_tray),           IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    正8K    ********************************/
//  width:27305     height:39370
static int  s_mz_custom_8k_x       = 27305;
static int  s_mz_custom_8k_y       = 39370;
static char s_custom_8k_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_custom_8k[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_custom_8k_x,          sizeof(s_mz_custom_8k_x),       IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_custom_8k_y,          sizeof(s_mz_custom_8k_y),       IPP_RANGE_SIZE                  }
};

__attribute__((unused)) static IPP_ATTR_S s_media_col_custom_8k[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_custom_8k,    0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_custom_8k_tray,          sizeof(s_custom_8k_tray),           IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    正16K    ********************************/
//  width:19685     height:27305
static int  s_mz_custom_16k_x       = 19685;
static int  s_mz_custom_16k_y       = 27305;
static char s_custom_16k_tray[16]   = "auto";

static IPP_ATTR_S s_media_size_custom_16k[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_custom_16k_x,         sizeof(s_mz_custom_16k_x),      IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_custom_16k_y,         sizeof(s_mz_custom_16k_y),      IPP_RANGE_SIZE                  }
};

__attribute__((unused)) static IPP_ATTR_S s_media_col_custom_16k[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_custom_16k,   0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_custom_16k_tray,         sizeof(s_custom_16k_tray),          IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    A6    ********************************/
//  width:10500     height:14800
static int  s_mz_a6_x       = 10500;
static int  s_mz_a6_y       = 14800;
static char s_a6_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_a6[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a6_x,                 sizeof(s_mz_a6_x),              IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_a6_y,                 sizeof(s_mz_a6_y),              IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_a6[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_a6,           0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_a6_tray,                 sizeof(s_a6_tray),                  IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    No.10 Env    ********************************/
//  width:10477     height:24130
static int  s_mz_env_10_x       = 10477;
static int  s_mz_env_10_y       = 24130;
static char s_env_10_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_env_10[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_10_x,             sizeof(s_mz_env_10_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_10_y,             sizeof(s_mz_env_10_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_env_10[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_env_10,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_env_10_tray,             sizeof(s_env_10_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_unsupported,  sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Monarch Env    ********************************/
//  width:9842     height:19050
static int  s_mz_env_monarch_x       = 9842;
static int  s_mz_env_monarch_y       = 19050;
static char s_env_monarch_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_env_monarch[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_monarch_x,        sizeof(s_mz_env_monarch_x),     IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_monarch_y,        sizeof(s_mz_env_monarch_y),     IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_env_monarch[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_env_monarch,  0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_env_monarch_tray,        sizeof(s_env_monarch_tray),         IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_unsupported,  sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    C6 Env    ********************************/
//  width:11400     height:16200
static int  s_mz_env_c6_x       = 11400;
static int  s_mz_env_c6_y       = 16200;
static char s_env_c6_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_env_c6[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_c6_x,             sizeof(s_mz_env_c6_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_c6_y,             sizeof(s_mz_env_c6_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_env_c6[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_env_c6,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_env_c6_tray,             sizeof(s_env_c6_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_unsupported,  sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    C5 Env    ********************************/
//  width:16200     height:22900
static int  s_mz_env_c5_x       = 16200;
static int  s_mz_env_c5_y       = 22900;
static char s_env_c5_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_env_c5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_c5_x,             sizeof(s_mz_env_c5_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_c5_y,             sizeof(s_mz_env_c5_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_env_c5[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_env_c5,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_env_c5_tray,             sizeof(s_env_c5_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_unsupported,  sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    DL Env    ********************************/
//  width:11000     height:22000
static int  s_mz_env_dl_x       = 11000;
static int  s_mz_env_dl_y       = 22000;
static char s_env_dl_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_env_dl[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_dl_x,             sizeof(s_mz_env_dl_x),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_env_dl_y,             sizeof(s_mz_env_dl_y),          IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_env_dl[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_env_dl,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_env_dl_tray,             sizeof(s_env_dl_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_unsupported,  sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Postcard    ********************************/
//  width:14800     height:20000
static int  s_mz_postcard_x       = 14800;
static int  s_mz_postcard_y       = 20000;
static char s_postcard_tray[16]   = "by-pass-tray";

static IPP_ATTR_S s_media_size_postcard[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_postcard_x,           sizeof(s_mz_postcard_x),        IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_mz_postcard_y,           sizeof(s_mz_postcard_y),        IPP_RANGE_SIZE                  }
};

static IPP_ATTR_S s_media_col_postcard[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_postcard,     0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_postcard_tray,           sizeof(s_postcard_tray),            IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_unsupported,  sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************    Coustom  ********************************/
//  width:9500-29700     height:14000-43200
static char s_mz_custom_xrange[]    = "9500,29700";
static char s_mz_custom_yrange[]    = "14000,43200";
static char s_custom_tray[16]       = "by-pass-tray";

static IPP_ATTR_S s_media_size_custom[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       4                                                               },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &x_dimension_str,           sizeof(x_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_RANGE,          ATTR_REQUIRED,      &s_mz_custom_xrange,        sizeof(s_mz_custom_xrange),     IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &y_dimension_str,           sizeof(y_dimension_str),        MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_RANGE,          ATTR_REQUIRED,      &s_mz_custom_yrange,        sizeof(s_mz_custom_yrange),     IPP_RANGE_SIZE                  }
};


static IPP_ATTR_S s_media_col_custom[] =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_media_type,              sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_custom,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_custom_tray,             sizeof(s_custom_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported),     4                               }
};

/**********************************     media col default(letter)   ********************************/
static IPP_ATTR_S s_media_col_default[]         =
{
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      NULL,                       16                                                                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_type_str,            sizeof(media_type_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      "Auto",                     sizeof(s_media_type),               IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_size_str,            sizeof(media_size_str),             MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_COLLECTION,     ATTR_REQUIRED,      &s_media_size_letter,       0                                                                   },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_source_str,          sizeof(media_source_str),           MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_KEYWORD,        ATTR_REQUIRED,      &s_letter_tray,             sizeof(s_letter_tray),              IPP_MAX_NAME                    },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_top_margin_str,      sizeof(media_top_margin_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_left_margin_str,     sizeof(media_left_margin_str),      MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_right_margin_str,    sizeof(media_right_margin_str),     MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &media_bottom_margin_str,   sizeof(media_bottom_margin_str),    MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_INTEGER,        ATTR_REQUIRED,      &s_ipp_base_margin,         sizeof(s_ipp_base_margin),          IPP_RANGE_SIZE                  },
    {NULL,  IPP_TAG_MEMBERNAME,     ATTR_REQUIRED,      &duplex_supported_str,      sizeof(duplex_supported_str),       MEMBERNAME_SIZE                 },
    {NULL,  IPP_TAG_BOOLEAN,        ATTR_REQUIRED,      &s_ipp_duplex_supported,    sizeof(s_ipp_duplex_supported)                                      }
};

static IPP_ATTR_S* s_media_sizes[IPP_MAX_MEDIA] =
{
    s_media_size_letter,
    s_media_size_a4,
    s_media_size_a3,
    s_media_size_a5,
    s_media_size_a6,
    s_media_size_jis_b4,
    s_media_size_jis_b5,
//    s_media_size_iso_b5,
    s_media_size_ledger,
    s_media_size_legal,
    s_media_size_folio,
//    s_media_size_oficio,
    s_media_size_executive,
    s_media_size_statement,
//    s_media_size_env_10,
//    s_media_size_env_monarch,
//    s_media_size_env_c5,
//    s_media_size_env_c6,
//    s_media_size_env_dl,
//    s_media_size_postcard,
    s_media_size_custom,
    NULL
};

static IPP_ATTR_S* s_media_cols[IPP_MAX_MEDIA]  =
{
    s_media_col_letter,
    s_media_col_a4,
    s_media_col_a3,
    s_media_col_a5,
    s_media_col_a6,
    s_media_col_jis_b4,
    s_media_col_jis_b5,
//    s_media_col_iso_b5,
    s_media_col_ledger,
    s_media_col_legal,
    s_media_col_folio,
//    s_media_col_oficio,
    s_media_col_executive,
    s_media_col_statement,
//    s_media_col_env_10,
//    s_media_col_env_monarch,
//    s_media_col_env_c5,
//    s_media_col_env_c6,
//    s_media_col_env_dl,
//    s_media_col_postcard,
    s_media_col_custom,
    NULL
};

static IPP_ATTR_S* s_media_cols_ready[IPP_MAX_MEDIA] =
{
    s_media_col_letter,
    NULL
};

static struct tag_media_xref
{
    const char* name;           ///< IPP namespace name for media
    int32_t     propval;        ///< property manager value for media
    PIPP_ATTR   pcol;           ///< media collection this corresponds too
    int32_t     w, h;           ///< dimensions in TRAYLINK_UNITS (mm*10)
}
s_media_xref[] =
{
    { "na_letter_8.5x11in",         PAPER_SIZE_LETTER,              s_media_col_letter,       2159,     2794},
    { "iso_a4_210x297mm",           PAPER_SIZE_A4,                  s_media_col_a4,           2100,     2970},
    { "iso_a3_297x420mm",           PAPER_SIZE_A3,                  s_media_col_a3,           2970,     4200},
    { "iso_a5_148x210mm",           PAPER_SIZE_A5,                  s_media_col_a5,           1480,     2100},
	{ "iso_a6_105x148mm",           PAPER_SIZE_A6,                  s_media_col_a6,           1050,     1480},
    { "jis_b4_257x364mm",           PAPER_SIZE_JIS_B4,              s_media_col_jis_b4,       2570,     3640},
    { "jis_b5_182x257mm",           PAPER_SIZE_JIS_B5,              s_media_col_jis_b5,       1820,     2570},
//    { "iso_b5_176x250mm",           PAPER_SIZE_ISO_B5,              s_media_col_iso_b5,       1760,     2500},
    { "na_ledger_11x17in",          PAPER_SIZE_LEDGER,              s_media_col_ledger,       2794,     4318},
    { "na_legal_8.5x14in",          PAPER_SIZE_LEGAL14,             s_media_col_legal,        2159,     3556},
    { "jis_exec_216x330mm",         PAPER_SIZE_FOLIO,               s_media_col_folio,        2160,     3300},
//    { "na_oficio_8.5x13.4in",       PAPER_SIZE_OFICIO,              s_media_col_oficio,       2159,     3403},
    { "na_executive_7.25x10.5in",   PAPER_SIZE_EXECUTIVE,           s_media_col_executive,    1841,     2667},
    { "na_invoice_5.5x8.5in",     	PAPER_SIZE_STATEMENT,           s_media_col_statement,    1397,     2159},
//    { "na_number-10_4.125x9.5in",   PAPER_SIZE_ENV_10,              s_media_col_env_10,       1047,     2413},
//    { "na_monarch_3.875x7.5in",     PAPER_SIZE_ENV_MONARCH,         s_media_col_env_monarch,  984,      1905},
//    { "iso_c5_162x229mm",           PAPER_SIZE_ENV_C5,              s_media_col_env_c5,       1620,     2290},
//    { "iso_c6_114x162mm",           PAPER_SIZE_ENV_C6,              s_media_col_env_c6,       1140,     1620},
//    { "iso_dl_110x220mm",           PAPER_SIZE_ENV_DL,              s_media_col_env_dl,       1100,     2200},
//    { "jpn_oufuku_148x200mm",       PAPER_SIZE_POSTCARD,            s_media_col_postcard,     1480,     2000},
    { "custom_min_95x140mm",       -1,                             NULL,                     950,      1400},
    { "custom_max_297x432mm",      -2,                             NULL,                     2970,     4320},
    { NULL,                         0,                             NULL,                     0,        0},
};

static int32_t map_media_index(int32_t paper_size)
{
    int32_t index = -1;

    switch ( paper_size ) /* 将paper_size映射到s_media_xref数组的索引 */
    {
        case PAPER_SIZE_LETTER_L:
        case PAPER_SIZE_LETTER:         index =  0; break;
        case PAPER_SIZE_A4L:
        case PAPER_SIZE_A4:             index =  1; break;
        case PAPER_SIZE_A3:             index =  2; break;
        case PAPER_SIZE_A5L:
        case PAPER_SIZE_A5:             index =  3; break;
        //TODO:case PAPER_SIZE_A6L:
        case PAPER_SIZE_A6:             index =  4; break;
        case PAPER_SIZE_JIS_B4:         index =  5; break;
        case PAPER_SIZE_JIS_B5L:
        case PAPER_SIZE_JIS_B5:         index =  6; break;
//        case PAPER_SIZE_ISO_B5L:
//        case PAPER_SIZE_ISO_B5:         index =  7; break;
        case PAPER_SIZE_LEDGER:         index =  7; break;
        case PAPER_SIZE_LEGAL14:        index =  8; break;
        case PAPER_SIZE_FOLIO:          index =  9; break;
//        case PAPER_SIZE_OFICIO:         index = 11; break;
        case PAPER_SIZE_EXECUTIVE_L:
        case PAPER_SIZE_EXECUTIVE:      index = 10; break;
        case PAPER_SIZE_STATEMENT_L:
        case PAPER_SIZE_STATEMENT:      index = 11; break;
//        case PAPER_SIZE_ENV_10:         index = 14; break;
//        case PAPER_SIZE_ENV_MONARCH:    index = 15; break;
//        case PAPER_SIZE_ENV_C5:         index = 16; break;
//        case PAPER_SIZE_ENV_C6:         index = 17; break;
//        case PAPER_SIZE_ENV_DL:         index = 18; break;
//      TODO:case PAPER_SIZE_POSTCARD_L:
//        case PAPER_SIZE_POSTCARD:       index = 19; break;
        default:                                    break;
    }

    return index;
}

/**
 * @brief   根据tray的paper-size,获取对应的s_media_xref数组索引
 *
 * @param   tray_index          纸盒索引
 *
 * @return  返回获取到的s_media_xref数组索引
 */
static int32_t get_media_index_by_tray(TRAY_INPUT_E tray_index)
{
    int32_t media_index;
    int32_t paper_size;

    /* 获取纸张尺寸，返回打印模块枚举值 */
    paper_size = (int32_t)netdata_get_paper_size(DATA_MGR_OF(s_ippattr_ctx), tray_index);
    /* 由打印模块枚举值 映射到 s_media_xref数组索引 */
    media_index = map_media_index(paper_size);

    NET_DEBUG("tray_index(%d) paper_size(%d) media_index(%d)", tray_index, paper_size, media_index);
    return media_index;
}

static const char* map_media_type(PAPER_TYPE_E paper_type)
{
	const char* media_type = "auto";

    switch ( paper_type )
    {
        case PAPER_TYPE_AUTO:       media_type = "auto";                    break;
        case PAPER_TYPE_ORDINARY:   media_type = "stationery";              break;
        case PAPER_TYPE_THICK1:     media_type = "stationery-heavyweight";  break;
//        case PAPER_TYPE_ENVELOP:    media_type = "envelope";                break;
        case PAPER_TYPE_FILM:       media_type = "transparency";            break;
        case PAPER_TYPE_CARD:       media_type = "cardstock";               break;
        case PAPER_TYPE_THIN:       media_type = "stationery-lightweight";  break;
        case PAPER_TYPE_LABEL:      media_type = "labels";                  break;
        default:                                                            break;
    }

    return media_type;
}

/**
 * @brief   根据tray的paper-type,获取对应的IPP定义的纸张类型字符串
 *
 * @param   tray_index          纸盒索引
 *
 * @return  返回获取到的IPP定义的纸张类型字符串
 */
static const char* get_media_type_by_tray(TRAY_INPUT_E tray_index)
{
    PAPER_TYPE_E    paper_type = PAPER_TYPE_ORDINARY;
    const char*     media_type = NULL;

    /* 获取纸张类型，返回打印模块枚举值 */
    paper_type = (PAPER_TYPE_E)netdata_get_paper_type(DATA_MGR_OF(s_ippattr_ctx), tray_index);
    /* 由打印模块枚举值 映射为 IPP定义的纸张类型字符串 */
    media_type = map_media_type(paper_type);

    NET_DEBUG("tray_index(%d) paper_type(%d) media type(%s)", tray_index, paper_type, media_type);
    return media_type;
}



#if IPP_SUPPORTS_OVERRIDES

// how many page ranges per override collection
#define IPP_MAX_OVERRIDE_PAGES  4

// how man overrides in array of collections
// note that each override can specifiy multiple ranges of pages
// so not that many are needed even for very complex scenarioes
//
#define IPP_MAX_OVERRIDES       4

// array stride of one override collection
//
#define IPP_OVERRIDE_SIZE       (3 * IPP_RANGE_SIZE + 4 * MEMBERNAME_SIZE + 2 * IPP_MAX_NAME + 10 * sizeof(IPP_ATTR_S) + IPP_MEDIA_COL_SIZE)

// these are the initializers for the overrides
//
static IPP_ATTR_S s_override[] =
{
    {NULL,          IPP_TAG_COLLECTION,     ATTR_COMBO_0011,    NULL,               9,                                          IPP_OVERRIDE_SIZE,  0   },
    {NULL,          IPP_TAG_MEMBERNAME,     ATTR_COMBO_1011,    "pages",            MEMBERNAME_SIZE                                                     },
    {NULL,          IPP_TAG_RANGE,          ATTR_COMBO_1011,    &s_ipp_zero,        IPP_MAX_OVERRIDE_PAGES * IPP_RANGE_SIZE,    IPP_RANGE_SIZE,     0   },
    {NULL,          IPP_TAG_MEMBERNAME,     ATTR_COMBO_1011,    "document-numbers", MEMBERNAME_SIZE                                                     },
    {NULL,          IPP_TAG_RANGE,          ATTR_COMBO_1011,    &s_ipp_zero,        IPP_MAX_OVERRIDE_PAGES * IPP_RANGE_SIZE,    IPP_RANGE_SIZE,     0   },
    {NULL,          IPP_TAG_MEMBERNAME,     ATTR_COMBO_1011,    "document-copies",  MEMBERNAME_SIZE                                                     },
    {NULL,          IPP_TAG_RANGE,          ATTR_COMBO_1011,    &s_ipp_zero,        IPP_MAX_OVERRIDE_PAGES * IPP_RANGE_SIZE,    IPP_RANGE_SIZE,     0   },
    {NULL,          IPP_TAG_MEMBERNAME,     ATTR_JOBTEMPLATE,   "media",            MEMBERNAME_SIZE                                                     },
    {NULL,          IPP_TAG_KEYWORD,        ATTR_JOBTEMPLATE,   "",                 IPP_MAX_NAME + 2                                                    },
    {"media-col",   IPP_TAG_COLLECTION,     ATTR_JOBTEMPLATE,   s_media_col_letter, 0,                                          IPP_MEDIA_COL_SIZE, 0   }
};

// initializer for array of override collection
//
static PIPP_ATTR s_overrides[IPP_MAX_OVERRIDES] =
{
    s_override,
    NULL
};

#endif // SUPPORTS_OVERRIDES

// printer attributes are almost all static almost all of the time.
// even when there are multiple service contexts, nearly all of these
// attributes remain constant once setup.  As you see, there are
// a lot of "almosts" in there.  For this reason this table is used
// only as an initializer for a service-specific attribute table
//
// any updates to printer attributes from our own code are done
// only to the service-specific tables which allows this table and
// its initializers to all be constants.
//
// each IPP context only has an array of flags which are set/cleared
// by the operation but all refer to the service specific table which
// which is selected based on the IPP uri handling the request.
//
static IPP_ATTR_S s_ipp_printer_attributes[] =
{
    /**** Operation Attributes ******/
    {"charset-configured",                      IPP_TAG_CHARSET,    ATTR_REQUIRED,      s_ipp_charset_configured,           sizeof(s_ipp_charset_configured)                    },
    {"charset-supported",                       IPP_TAG_CHARSET,    ATTR_COMBO_1001,    s_ipp_charset_supported,            sizeof(s_ipp_charset_supported),        8,      0   },
    {"color-supported",                         IPP_TAG_BOOLEAN,    ATTR_REQUIRED,      &s_color_supported,                 sizeof(s_color_supported)                           },
    {"document-format-default",                 IPP_TAG_MIMETYPE,   ATTR_REQUIRED,      s_ipp_docformat_default,            sizeof(s_ipp_docformat_default)                     },
    {"document-format-supported",               IPP_TAG_MIMETYPE,   ATTR_COMBO_1001,    s_ipp_docformat_supported,          sizeof(s_ipp_docformat_supported),      32,     0   },
    {"document-format-preferred",               IPP_TAG_MIMETYPE,   ATTR_REQUIRED,      s_ipp_docformat_default,            sizeof(s_ipp_docformat_default)                     },
    {"natural-language-configured",             IPP_TAG_LANGUAGE,   ATTR_REQUIRED,      s_ipp_lang_configured,              sizeof(s_ipp_lang_configured)                       },
    {"generated-natural-language-supported",    IPP_TAG_LANGUAGE,   ATTR_COMBO_1001,    s_ipp_genlang_supported,            sizeof(s_ipp_genlang_supported),        16,     0   },
    {"ipp-versions-supported",                  IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_ipp_versions_supported,           sizeof(s_ipp_versions_supported),       4,      0   },
    {"job-creation-attributes-supported",       IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_job_creation_attr_supported,      sizeof(s_job_creation_attr_supported),  32,     0   },
    {"mopria-certified",                        IPP_TAG_TEXT,       ATTR_REQUIRED,      s_mopria_certified,                 sizeof(s_mopria_certified)                          },
    {"printer-name",                            IPP_TAG_NAME,       ATTR_REQUIRED,      s_ipp_printername,                  sizeof(s_ipp_printername)                           },
    {"printer-location",                        IPP_TAG_TEXT,       ATTR_REQUIRED_20,   s_printer_location,                 sizeof(s_printer_location)                          },
    {"printer-info",                            IPP_TAG_TEXT,       ATTR_REQUIRED_20,   s_printer_info,                     sizeof(s_printer_info)                              },
    {"printer-more-info",                       IPP_TAG_URI,        ATTR_REQUIRED_20,   s_printer_more_info,                sizeof(s_printer_more_info)                         },
    {"printer-current-time",                    IPP_TAG_DATE,       ATTR_REQUIRED_20,   s_ipp_current_time,                 sizeof(s_ipp_current_time)                          },
    {"printer-make-and-model",                  IPP_TAG_TEXT,       ATTR_REQUIRED_20,   s_printer_make_and_model,           sizeof(s_printer_make_and_model)                    },
    {"printer-device-id",                       IPP_TAG_TEXT,       ATTR_REQUIRED,      s_1284deviceID,                     sizeof(s_1284deviceID)                              },
    {"printer-state",                           IPP_TAG_ENUM,       ATTR_REQUIRED,      &s_ipp_printerstate,                sizeof(s_ipp_printerstate)                          },
    {"printer-state-reasons",                   IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_ipp_printerstate_reasons,         sizeof(s_ipp_printerstate_reasons),     64,     0   },
    {"printer-state-message",                   IPP_TAG_TEXT,       ATTR_REQUIRED_20,   s_ipp_printerstate_message,         sizeof(s_ipp_printerstate_message)                  },
    {"operations-supported",                    IPP_TAG_ENUM,       ATTR_COMBO_1001,    s_operations_supported,             sizeof(s_operations_supported)                      },
    {"ipp-features-supported",                  IPP_TAG_KEYWORD,    ATTR_COMBO_1001_20, s_ipp_features_supported,           sizeof(s_ipp_features_supported),       16,     0   },
    {"printer-is-accepting-jobs",               IPP_TAG_BOOLEAN,    ATTR_REQUIRED,      &s_ipp_accepting,                   sizeof(s_ipp_accepting)                             },
    {"queued-job-count",                        IPP_TAG_INTEGER,    ATTR_REQUIRED,      &s_ipp_zero,                        sizeof(s_ipp_zero)                                  },
    {"printer-uri-supported",                   IPP_TAG_URI,        ATTR_COMBO_1001,    s_printer_uri_supported,            sizeof(s_printer_uri_supported), IPP_MAX_URI,   0   },
    {"uri-security-supported",                  IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_uri_security_supported,           sizeof(s_uri_security_supported),       32,     0   },
    {"uri-authentication-supported",            IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_uri_authentication_supported,     sizeof(s_uri_authentication_supported), 32,     0   },
    {"pages-per-minute",                        IPP_TAG_INTEGER,    ATTR_REQUIRED,      &s_pages_per_minute,                sizeof(s_pages_per_minute)                          },
#if CONFIG_COLOR
    {"pages-per-minute-color",                  IPP_TAG_INTEGER,    ATTR_REQUIRED,      &s_pages_per_minute_color,          sizeof(s_pages_per_minute_color)                    },
#endif
    {"pdl-override-supported",                  IPP_TAG_KEYWORD,    ATTR_REQUIRED,      s_pdl_override_supported,           sizeof(s_pdl_override_supported)                    },
    {"printer-uuid",                            IPP_TAG_URI,        ATTR_REQUIRED,      s_printer_uuid,                     sizeof(s_printer_uuid)                              },
    {"printer-up-time",                         IPP_TAG_INTEGER,    ATTR_REQUIRED,      &s_ipp_uptime,                      sizeof(s_ipp_uptime)                                },
    {"multiple-operation-time-out",             IPP_TAG_INTEGER,    ATTR_REQUIRED_20,   &s_ipp_multiop_timeout,             sizeof(s_ipp_multiop_timeout)                       },
    {"multiple-operation-time-out-action",      IPP_TAG_KEYWORD,    ATTR_REQUIRED_20,   &s_multiop_timeout_action,          sizeof(s_multiop_timeout_action)                    },
    {"multiple-document-jobs-supported",        IPP_TAG_BOOLEAN,    ATTR_REQUIRED_20,   &s_ipp_one,                         sizeof(s_ipp_one)                                   },
    {"compression-supported",                   IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_compression_supported,            sizeof(s_compression_supported),        8,      0   },
    {"media-ready",                             IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    &s_media_ready,                     sizeof(s_media_ready),                  32,     0   },
    /***** Airprint required Attributes *****/
    {"landscape-orientation-requested-preferred", IPP_TAG_ENUM,     ATTR_REQUIRED,      &s_preferred_landscape_orientation, sizeof(s_preferred_landscape_orientation)           },
    {"marker-colors",                           IPP_TAG_NAME,       ATTR_COMBO_1001,    s_marker_colors,                    sizeof(s_marker_colors),                8,      0   },
    {"marker-names",                            IPP_TAG_NAME,       ATTR_COMBO_1001,    s_marker_names,                     sizeof(s_marker_names),                 8,      0   },
    {"marker-types",                            IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_marker_types,                     sizeof(s_marker_types),                 8,      0   },
    {"marker-low-levels",                       IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_marker_lowlevels,                 sizeof(s_marker_lowlevels)                          },
    {"marker-high-levels",                      IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_marker_highlevels,                sizeof(s_marker_highlevels)                         },
    {"marker-levels",                           IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_marker_levels,                    sizeof(s_marker_levels)                             },
    {"printer-icons",                           IPP_TAG_URI,        ATTR_COMBO_1001,    s_printer_icons,                    sizeof(s_printer_icons),      IPP_MAX_URI,      0   },
    {"media-bottom-margin-supported",           IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_margins,                          sizeof(s_margins)                                   },
    {"media-top-margin-supported",              IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_margins,                          sizeof(s_margins)                                   },
    {"media-left-margin-supported",             IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_margins,                          sizeof(s_margins)                                   },
    {"media-right-margin-supported",            IPP_TAG_INTEGER,    ATTR_COMBO_1001,    s_margins,                          sizeof(s_margins)                                   },
    {"media-source-supported",                  IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_media_source_supported,           sizeof(s_media_source_supported),       32,     0   },
    {"media-type-supported",                    IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_media_type_supported,             sizeof(s_media_type_supported),         32,     0   },
    {"printer-dns-sd-name",                     IPP_TAG_NAME,       ATTR_REQUIRED,      s_printer_dns_sd,                   sizeof(s_printer_dns_sd)                            },
    {"printer-input-tray",                      IPP_TAG_STRING,     ATTR_COMBO_1001,    s_printer_input_tray,               sizeof(s_printer_input_tray),  IPP_MAX_URI,     0   },
    {"printer-output-tray",                     IPP_TAG_STRING,     ATTR_COMBO_1001,    s_printer_output_tray,              sizeof(s_printer_output_tray), IPP_MAX_URI,     0   },
    {"printer-finisher",                        IPP_TAG_STRING,     ATTR_COMBO_1001,    s_printer_finisher,                 sizeof(s_printer_finisher),    IPP_MAX_URI,     0   },
    {"printer-finisher-description",            IPP_TAG_TEXT,       ATTR_COMBO_1001,    s_printer_finisher_description,     sizeof(s_printer_finisher_description), 16,     0   },
    {"printer-firmware-name",                   IPP_TAG_NAME,       ATTR_COMBO_1001,    s_printer_fw_name,                  sizeof(s_printer_fw_name),              32,     0   },
    {"printer-firmware-patches",                IPP_TAG_TEXT,       ATTR_COMBO_1001,    s_printer_fw_patches,               sizeof(s_printer_fw_patches),           4,      0   },
    {"printer-firmware-string-version",         IPP_TAG_TEXT,       ATTR_COMBO_1001,    s_printer_fw_str_version,           sizeof(s_printer_fw_str_version),       16,     0   },
    {"printer-firmware-version",                IPP_TAG_STRING,     ATTR_COMBO_1001,    s_printer_fw_version,               sizeof(s_printer_fw_version),           16,     0   },
#if CONFIG_JPEG
    {"jpeg-k-octets-supported",                 IPP_TAG_RANGE,      ATTR_REQUIRED,      &s_jpeg_k_octets_supported,         sizeof(s_jpeg_k_octets_supported)                   },
    {"jpeg-x-dimension-supported",              IPP_TAG_RANGE,      ATTR_REQUIRED,      &s_jpeg_x_dim_supported,            sizeof(s_jpeg_x_dim_supported)                      },
    {"jpeg-y-dimension-supported",              IPP_TAG_RANGE,      ATTR_REQUIRED,      &s_jpeg_y_dim_supported,            sizeof(s_jpeg_y_dim_supported)                      },
    {"jpeg-features-supported",                 IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_jpeg_features_supported,          sizeof(s_jpeg_features_supported),      16,     0   },
#endif
#if CONFIG_PDF
    {"pdf-k-octets-supported",                  IPP_TAG_RANGE,      ATTR_REQUIRED,      &s_pdf_k_octets_supported,          sizeof(s_pdf_k_octets_supported)                    },
    {"page-ranges-supported",                   IPP_TAG_BOOLEAN,    ATTR_REQUIRED,      &s_page_ranges_supported,           sizeof(s_page_ranges_supported)                     },
    {"overrides-supported",                     IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    &s_overrides_supported,             sizeof(s_overrides_supported),          32,     0   },
#endif
    {"urf-supported",                           IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_urf_supported,                    sizeof(s_urf_supported),                96,     0   },
    {"pdf-versions-supported",                  IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_pdf_versions_supported,           sizeof(s_pdf_versions_supported),       32,     0   },
    {"which-jobs-supported",                    IPP_TAG_KEYWORD,    ATTR_COMBO_1001_20, s_which_jobs_supported,             sizeof(s_which_jobs_supported),         32,     0   },
    {"job-ids-supported",                       IPP_TAG_BOOLEAN,    ATTR_REQUIRED_20,   &s_ipp_one,                         sizeof(s_ipp_one)                                   },
    {"printer-supply-info-uri",                 IPP_TAG_URI,        ATTR_REQUIRED,      s_printer_supply_uri,               sizeof(s_printer_supply_uri)                        },
    {"printer-geo-location",                    IPP_TAG_URI,        ATTR_REQUIRED,      s_printer_geo_location,             sizeof(s_printer_geo_location)                      },
    {"printer-kind",                            IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    &s_printer_kind,                    sizeof(s_printer_kind),                 32,     0   },
    {"identify-actions-supported",              IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_identify_actions_supported,       sizeof(s_identify_actions_supported),   16,     0   },
    {"identify-actions-default",                IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_identify_actions_default,         sizeof(s_identify_actions_default),     16,     0   },
    {"print-scaling-default",                   IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_print_scaling_default,            sizeof(s_print_scaling_default),        16,     0   },
    {"print-scaling-supported",                 IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_print_scaling_supported,          sizeof(s_print_scaling_supported),      16,     0   },
    {"print-content-optimize-default",          IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_print_content_supported,          sizeof(s_print_content_supported)                   },
    {"print-content-optimize-supported",        IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_print_content_supported,          sizeof(s_print_content_supported)                   },
    {"media-col-supported",                     IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_media_col_supported,              sizeof(s_media_col_supported),          32,     0   },
    {"media-col-default",                       IPP_TAG_COLLECTION, ATTR_COMBO_0011,    &s_media_col_letter,                0                                                   },
    {"media-size-supported",                    IPP_TAG_COLLECTION, ATTR_COMBO_1011,    s_media_sizes,                      0                                                   },
    {"media-col-ready",                         IPP_TAG_COLLECTION, ATTR_COMBO_1011,    s_media_cols_ready,                 0                                                   },
    {"media-col-database",                      IPP_TAG_COLLECTION, ATTR_COMBO_1111,    s_media_cols,                       0                                                   },
    {"pwg-raster-document-resolution-supported",IPP_TAG_RESOLUTION, ATTR_COMBO_1001,    s_pwg_raster_res_supported,         sizeof(s_pwg_raster_res_supported),     16,     0   },
    {"pwg-raster-document-type-supported",      IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_pwg_raster_type_supported,        sizeof(s_pwg_raster_type_supported),    16,     0   },
    /***** Job Template Attributes ******/
    {"job-priority-default",                    IPP_TAG_INTEGER,    ATTR_COMBO_0011,    &s_job_priority_default,            sizeof(s_job_priority_default)                      },
    {"job-priority-supported",                  IPP_TAG_INTEGER,    ATTR_COMBO_0011,    &s_job_priority_supported,          sizeof(s_job_priority_supported)                    },
    {"job-hold-until-default",                  IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_job_holduntil_default,            sizeof(s_job_holduntil_default)                     },
    {"job-hold-until-supported",                IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_job_holduntil_supported,          sizeof(s_job_holduntil_supported),      16,     0   },
    {"job-sheets-default",                      IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_job_sheets_default,               sizeof(s_job_sheets_default)                        },
    {"job-sheets-supported",                    IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_job_sheets_supported,             sizeof(s_job_sheets_supported),         16,     0   },
    {"job-presets-supported",                   IPP_TAG_BOOLEAN,    ATTR_REQUIRED_20,   &s_job_page_set,                    sizeof(s_job_page_set)                              },
    {"copies-default",                          IPP_TAG_INTEGER,    ATTR_COMBO_0011,    &s_copies_default,                  sizeof(s_copies_default)                            },
    {"copies-supported",                        IPP_TAG_RANGE,      ATTR_COMBO_0011,    &s_copies_supported,                sizeof(s_copies_supported)                          },
    {"finishings-default",                      IPP_TAG_ENUM,       ATTR_COMBO_0011,    &s_finishings_default,              sizeof(s_finishings_default)                        },
    {"finishings-supported",                    IPP_TAG_ENUM,       ATTR_COMBO_1011,    &s_finishings_supported,            sizeof(s_finishings_supported)                      },
    {"finishings-col-default",                  IPP_TAG_COLLECTION, ATTR_COMBO_0011,    s_finishings_col_none,              0                                                   },
    {"finishings-col-supported",                IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_finishings_col_supported,         sizeof(s_finishings_col_supported),     32,     0   },
    {"finishings-col-database",                 IPP_TAG_COLLECTION, ATTR_COMBO_1011,    s_finishings_col_database,          0                                                   },
    {"finishings-col-ready",                    IPP_TAG_COLLECTION, ATTR_COMBO_1011,    s_finishings_col_database,          0                                                   },
    {"finishing-template-supported",            IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    s_finishings_template_supported,    sizeof(s_finishings_template_supported),32,     0   },
    {"sides-default",                           IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_sides_default,                    sizeof(s_sides_default)                             },
    {"sides-supported",                         IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    &s_sides_supported,                 sizeof(s_sides_supported),              32,     0   },
    {"orientation-requested-default",           IPP_TAG_ENUM,       ATTR_COMBO_0011,    &s_orientation_req_default,         sizeof(s_orientation_req_default)                   },
    {"orientation-requested-supported",         IPP_TAG_ENUM,       ATTR_COMBO_1011,    &s_orientation_req_supported,       sizeof(s_orientation_req_supported)                 },
    {"media-default",                           IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_media_default,                    sizeof(s_media_default)                             },
    {"media-supported",                         IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_media_supported,                  sizeof(s_media_supported),              64,     0   },
    {"printer-resolution-default",              IPP_TAG_RESOLUTION, ATTR_COMBO_0011,    s_printer_resolution_default,       sizeof(s_printer_resolution_default)                },
    {"printer-resolution-supported",            IPP_TAG_RESOLUTION, ATTR_COMBO_1011,    s_printer_resolution_supported,     sizeof(s_printer_resolution_supported), 12,     0   },
    {"print-quality-default",                   IPP_TAG_ENUM,       ATTR_COMBO_0011,    &s_print_quality_default,           sizeof(s_print_quality_default)                     },
    {"print-quality-supported",                 IPP_TAG_ENUM,       ATTR_COMBO_1011,    &s_print_quality_supported,         sizeof(s_print_quality_supported)                   },
    {"output-bin-default",                      IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_output_bin_default,               sizeof(s_output_bin_default)                        },
    {"output-bin-supported",                    IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_output_bin_supported,             sizeof(s_output_bin_supported),         8,      0   },
    {"multiple-document-handling-default",      IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_multidoc_handling_default,        sizeof(s_multidoc_handling_default)                 },
    {"multiple-document-handling-supported",    IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_multidoc_handling_supported,      sizeof(s_multidoc_handling_supported),  64,     0   },
    {"print-color-mode-default",                IPP_TAG_KEYWORD,    ATTR_COMBO_0011,    s_output_mode_default,              sizeof(s_output_mode_default)                       },
    {"print-color-mode-supported",              IPP_TAG_KEYWORD,    ATTR_COMBO_1011,    s_output_mode_supported,            sizeof(s_output_mode_supported),        32,     0   },
    {"language-tags",                           IPP_TAG_TEXT,       ATTR_REQUIRED,      s_ipp_language_tags,                sizeof(s_ipp_language_tags)                         },
    {NULL,                                      IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                               0                                                   }
};

//**************************************************************************
static PIPP_ATTR IPPATTRgetPrinterAttributeSet(uint32_t service)
{
    PIPP_ATTR pset;



    switch (service)
    {
    case IPPSERVICE_DEFAULTS:
        pset = s_ipp_printer_attributes;
        break;
    case IPPSERVICE_PRINT:
        pset = s_ipp_printer.printer_attributes;
        break;
    case IPPSERVICE_ALL:
        NET_WARN("Can only get from a single service attribute set");
        return NULL;
    default:
        NET_WARN("Not a valid service id <%08X>", service);
        return NULL;
    }
    return pset;
}

static int IPPATTRreportAttribute(PIPP_ATTR *pa, int ignoreisset, int level, IPPATTRCB callback, void *cookie);

//**************************************************************************
static int32_t ReportCollection(PIPP_ATTR *ppa, int ignoreisset, int index, int maxdex, int level, IPPATTRCB callback, void* cookie)
{
    PIPP_ATTR   pa, pm, pmnext;
    int32_t     member, members;
    int32_t     rc;

    pa = *ppa;

    if(strncmp(pa->name, "<noname>", 8))
    {
        rc = callback(cookie, (index == 0) ? pa->name : NULL, pa->type, NULL);
        RETURN_VAL_IF(rc != 0, NET_NONE, rc);
    }
    else
    {

        rc = callback(cookie, NULL, pa->type, NULL);
        RETURN_VAL_IF(rc != 0, NET_NONE, rc);
    }

    while (pa->varsize == 0 && pa->var != NULL)
    {
        pa = (IPP_ATTR_S *)pa->var;
    }

    RETURN_VAL_IF(pa == NULL || pa->varsize <= 0, NET_WARN, 1);

    // for arrays, there should be one flat collection at each maxlen offset from var
    //
    if (index > 0)
    {
        if (pa->maxlen <= 0)
        {
            NET_WARN("Stride never set for %s", pa->name ? pa->name : "<noname>");
        }
        pa = (IPP_ATTR_S *)((char *)pa + index * pa->maxlen);
        if (! AttributeIsCollectionType(pa) || ! pa->varsize)
        {
            NET_WARN("index %d of collection is not a collection", index);
            return -1;
        }
    }

    NET_TRACE("report Collection name(%s) index[%d.%d] at %p", pa->name ? pa->name : "<noname>", index, pa->maxlen, pa);

    members = pa->varsize;
    member = 0;

    // iterate over each member, reporting each one recursively.
    //
    for (member = 0, pm = ++pa; member < members; member++)
    {
        pmnext = pm;
        rc = IPPATTRreportAttribute(&pmnext, ignoreisset, level + 1, callback, cookie);
        if (pmnext == pm || rc != 0)
        {
            NET_WARN("reporting failed or stalled");
            return -1;
        }
        pm = pmnext;
    }
    // return next attribute from starting point so iteration works
    //
    pa = *ppa;
    if (pa->varsize == 0)
    {
        // this was an indirection, so next in list would be
        // one after this
        //
        *ppa = pa + 1;
    }
    else
    {
        // this was an inline collection, so next in list is
        // the one after this collection
        //
        *ppa = pm;
    }
    rc = callback(cookie, NULL, IPP_TAG_END_COLLECTION, NULL);
    return rc;
}

//**************************************************************************
static int ReportString(PIPP_ATTR pa, int index, int maxdex, int level, IPPATTRCB callback, void *cookie)
{
    int rc;

    rc = callback(cookie, (index == 0) ? pa->name : NULL, pa->type, (void*)((char *)pa->var + index * pa->maxlen));
    return rc;
}

//**************************************************************************
static int ReportInteger(PIPP_ATTR pa, int index, int maxdex, int level, IPPATTRCB callback, void *cookie)
{
    int rc;

    rc = callback(cookie, (index == 0) ? pa->name : NULL, pa->type, (void*)((char *)pa->var + index * sizeof(int)));
    return rc;
}

//**************************************************************************
static int ReportUnsetAttribute(PIPP_ATTR pa, int index, int maxdex, int level, IPPATTRCB callback, void *cookie)
{
    int rc;

    rc = callback(cookie, (index == 0) ? pa->name : NULL, IPP_TAG_NOVALUE, NULL);
    return rc;
}

//**************************************************************************
static int IPPATTRreportAttribute(PIPP_ATTR *ppa, int ignoreisset, int level, IPPATTRCB callback, void *cookie)
{
    PIPP_ATTR pa;
    int rc;
    int index;
    int count;
    int maxdex;
    uint8_t *pvar;

    pa = *ppa;

    if (IS_ATTR_ARRAY(pa))
    {
        count = pa->curdim;
        maxdex = count;

        // if the count < 1, the array was never set, which can happen
        // if the array is part of collection
        //
        if (count <= 0)
        {
            if (level <= 0 && IS_ATTR_SET(pa))
            {
                NET_WARN("Unset array in report");
                *ppa = ++pa;
                return -1;
            }
            NET_DEBUG("Unset array %s", pa->name);
            *ppa = ++pa;
            return 0;
        }
    }
    else
    {
        count = 1;
        maxdex = -1;
    }
    // for each index in the array, report the value
    //
    for (index = 0, rc = 0; index < count && rc == 0; index++)
    {
        pa = *ppa;

        if (IS_ATTR_ARRAY(pa))
        {
            pvar = (uint8_t *)pa->var + index * pa->maxlen;
        }
        else
        {
            pvar = (uint8_t*)pa->var;
        }
        if (AttributeIsCollectionType(pa))
        {
            if (IS_ATTR_SET(pa))
            {
                // only report set vars, or set members, unless ignoring the set field
                //
                rc = ReportCollection(&pa, ignoreisset, index, maxdex, level, callback, cookie);
            }
            else
            {
                if (ignoreisset)
                {
                    rc = ReportUnsetAttribute(pa, index, maxdex, level, callback, cookie);
                }
                rc = SkipOverCollection(&pa);
            }
        }
        else if (AttributeIsStringType(pa))
        {
            NET_TRACE("report (%s) str(%s) at %p", pa->name, (char*)pvar, pvar);
            if (IS_ATTR_SET(pa))
            {
                // only report set vars, or set members, unless ignoring the set field
                //
                rc = ReportString(pa, index, maxdex, level, callback, cookie);
            }
            else if (ignoreisset)
            {
                rc = ReportUnsetAttribute(pa, index, maxdex, level, callback, cookie);
            }
            pa++;
        }
        else
        {
            NET_TRACE("report (%s) int(%d) at %p", pa->name, *(int*)pvar, pvar);
            if (IS_ATTR_SET(pa))
            {
                // only report set vars, or set members, unless ignoring the set field
                //
                rc = ReportInteger(pa, index, maxdex, level, callback, cookie);
            }
            else if (ignoreisset)
            {
                rc = ReportUnsetAttribute(pa, index, maxdex, level, callback, cookie);
            }
            pa++;
        }
        if (index < (count - 1))
        {
            // don't advance to next attr until last index
            pa = *ppa;
        }
    }
    *ppa = pa;
    return rc;
}

int IPPATTRgetRequestedPrinterAttributes(PIPPCTX ppp, IPPATTRCB callback, void *cookie)
{
    PIPP_ATTR   pa, panext, opa;
    int32_t     rc = 0;
    int32_t     count;
    int32_t     i, j;

    pa = IPPATTRgetPrinterAttributeSet(ppp->serviceType);
    RETURN_VAL_IF(pa == NULL, NET_INFO, -1);

    ATTRLOCK();
    for ( i = 0; pa->name && i < IPP_MAX_PRINTER_ATTRIBUTES; ++i )
    {
        if ( (strcmp(pa->name, "media-ready") == 0) || (strcmp(pa->name, "media-col-ready") == 0) )
        {
            for ( count = -1, j = TRAY_INPUT_STANDARD; j <= TRAY_INPUT_MULTIFUNCTION; ++j )
            {
                if ( netdata_get_tray_install(DATA_MGR_OF(s_ippattr_ctx), j) == 0 )
                {
                    continue;
                }

                count = get_media_index_by_tray(j);
                BREAK_IF(count >= 0, NET_NONE);
            }

            if ( count < 0 )
            {
                ppp->printer_attributes[i] = 0;
            }
        }

        if ( ppp->printer_attributes[i] && pa->var )
        {
            panext = pa;
            opa = pa;
            rc = IPPATTRreportAttribute(&panext, 1, 0, callback, cookie);
            if ( pa == panext || rc != 0 )
            {
                NET_WARN("Request stalled or failed");
                break;
            }
            pa = panext;
            if ( pa != (opa + 1) )
            {
                NET_WARN("attr list not sequential");
            }
        }
        else
        {
            pa++;
        }
    }
    ATTRUNLOCK();
    return rc;
}

/**
 * @brief   将指定的属性(name)标记为被请求的属性,ppp->printer_attributes[i] |= 1
 *
 * @param   ppp         IPP Context结构体指针
 * @param   name        属性名称
 *
 * @return  处理结果
 * @return  <ul><li>0:成功</li><li>-1:失败</li></ul>
 */
int IPPATTRrequestPrinterAttribute(PIPPCTX ppp, const char *name)
{
    IPP_ATTR_S* pa = NULL;
    int32_t     rv = 1;
    int32_t     i;

    pa = IPPATTRgetPrinterAttributeSet(ppp->serviceType);
    RETURN_VAL_IF(pa == NULL, NET_WARN, -1);

    ATTRLOCK();
    for ( i = 0; pa->name && i < IPP_MAX_PRINTER_ATTRIBUTES; ++pa, ++i )
    {
        if ( strcmp(pa->name, name) == 0 )
        {
            ppp->printer_attributes[i] |= 1;
            rv = 0;
            break;
        }
    }
    ATTRUNLOCK();

    if ( rv != 0 )
    {
        NET_DEBUG("Requested Attribute %s not in table", name);
    }
    return rv;
}

/**
 * @brief   若onoff=1,并且仅是request请求独有的属性则标记属性是本次request消息中被请求的属性,
 * @brief   并根据索引将flag设定到ppp->printer_attributes[i]=1,否者ppp->printer_attributes[i]=0
 *
 * @param   ppp         IPP Context结构体指针
 * @param   onoff       若request请求是ALL(即所有属性)则onoff = 1,否则onoff=0
 *
 * @return  返回处理结果
 * @return  <ul><li>0:成功</li><li>-1:失败</li></ul>
 */
int IPPATTRrequestAllPrinterAttributes(PIPPCTX ppp, int onoff)
{
    IPP_ATTR_S* pa;
    int32_t     i;

    pa = IPPATTRgetPrinterAttributeSet(ppp->serviceType);
    RETURN_VAL_IF(pa == NULL, NET_WARN, -1);

    ATTRLOCK();
    for (
            i = 0;
            pa->name && i < IPP_MAX_PRINTER_ATTRIBUTES;
            pa++, i++
    )
    {
        if (onoff)
        {
            if (! IS_ATTR_EXPLICIT(pa))
            {
                ppp->printer_attributes[i] = 1;
            }
        }
        else
        {
            ppp->printer_attributes[i] = 0;
        }
    }
    if (pa && pa->name)
    {
        // got to max printer attr and still have attrs in set, this is
        // a configuration error, need to bump up max
        //
        NET_WARN("IPP_MAX_PRINTER_ATTRIBUTES too small");
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRgetPrinterAttribute(uint32_t service, const char *name, int *type, void **value, int index, int *count)
{
    PIPP_ATTR pset;

    pset = IPPATTRgetPrinterAttributeSet(service);
    if (! pset)
    {
        return -1;
    }
    return IPPATTRgetAttribute(pset, "Printer", name, index, type, value, count);
}

int32_t ippattr_set_printer_attribute_internal(uint32_t services, const char* name, void* value, int index)
{
    int32_t rv = 0;

    if (services & IPPSERVICE_DEFAULTS)
    {
        NET_WARN("default printer attribute set is read only");
    }
    else if (services & IPPSERVICE_PRINT)
    {
        rv = ippattr_set_attribute_locked(s_ipp_printer.printer_attributes, "Printer", name, value, index);
    }

    return rv;
}

/**
* @brief 创建PrinterAttribute
*
* @param pAttribs [out] 返回创建好的IPP字段
* @return 执行结果
* @return <ul><li>0:执行成功</li><li>-1:执行失败</li></ul>
*/
static int IPPATTRcreatePrinterAttributeSet(IPP_ATTR_S** pAttribs)
{
    IPP_ATTR_S* pset = NULL;
    IPP_ATTR_S* psrc = NULL;
    IPP_ATTR_S* pa = NULL;
    char*       data;
    int32_t     varsize;
    int32_t     allocz;
    int32_t     index;

    *pAttribs = NULL;
    pset = (IPP_ATTR_S *)pi_zalloc(sizeof(s_ipp_printer_attributes));
    RETURN_VAL_IF(pset == NULL, NET_WARN, -1);

    // start the new set as a direct copy of base set
    //将属性集合s_ipp_printer_attributes作为base拷贝到pset中,
    memcpy(pset, s_ipp_printer_attributes, sizeof(s_ipp_printer_attributes));

    // go through attribute set and add up all the sizes of vars or space needed
    //遍历属性集合pset,
    for (pa = pset, allocz = 0; pa->name; pa++)
    {
        // for most attrs, varsize is max storage needed
        //
        varsize = pa->varsize;

        // round it to 4 byte boundary
        //
        //四字节对齐
        varsize += 3;
        varsize &= ~3;

        if (AttributeIsCollectionType(pa))
        {
            if(pa->var != NULL && pa->varsize == 0)
            {
                // an indirectly initialized collection attribute
                //
                if (IS_ATTR_ARRAY(pa))
                {
                    PIPP_ATTR pcol;

                    // array initializers just point to the actual inititalizer
                    // so get first one as total size needed, then multiply
                    // by max collection set size to get total possibly needed
                    //

                    // the intializer is an array of pointers to actual collections
                    // instead of the the actual collections in a row
                    //
                    pcol = ((PIPP_ATTR *)pa->var)[0];
                    if (! pcol)
                    {
                        NET_WARN("Not a single initalizer for %s", pa->name);
                        varsize = 0;
                    }
                    else
                    {
                        varsize = IPPATTRcollectionSize(pcol, pa->name);
                        if (pa->maxlen <= 0)
                        {
                            // set the size of a single element, of not set
                            //
                            pa->maxlen = varsize;
                        }
                        // allow for setting up to max set size
                        //
                        varsize *= IPP_MAX_COLLECTION_SET;
                    }
                }
                else
                {
                    // a simple indirection to a collection initializer
                    //
                    varsize = IPPATTRcollectionSize(pa, pa->name);
                    if (pa->maxlen <= 0)
                    {
                        // set the size of a single element, of not set
                        //
                        pa->maxlen = varsize;
                    }
                }
            }
            else
            {
                // an actual collection, which should pe pointing to the
                // same header to be sane. this can't happen in the the
                // current design since collection are always indirect
                // in the initializers (and have to be) so error
                //
                NET_WARN("Non indirect collection");
                return -1;
            }
        }
        else if (varsize == 0)
        {
            if (IS_ATTR_REQUIRED(pa))
            {
                NET_WARN("0 size for requred %s", pa->name);
            }
        }
        allocz += varsize + 2;
    }
    // allocate one big chunk to hold all the var data
    //
    //申请一块空间，将所有的属性值存放到该data空间，使用指针去引用，data空间会一致保留，直到断电重启
    data = pi_zalloc(allocz);
    if ( data == NULL )
    {
        NET_WARN("Can't alloc data for set");
        pi_free(pset);
        return -1;
    }

    // go through set again and assign pointers to allocated data for variable
    // storage and copy the source value(s) over to this storage
    for (pa = pset, psrc = s_ipp_printer_attributes, allocz = 0; pa->name; pa++, psrc++)
    {
        pa->var = (void *)(data + allocz);

        varsize = pa->varsize;
        varsize += 3;
        varsize &= ~3;

        // all attrs in the set are "SET" since we're setting them
        //
        SET_ATTR(pa);

        if (AttributeIsCollectionType(pa))
        {
            // collections are always indirected, and arrays of
            // them are specified as an array of pointers to collections, not
            // an array of attributes (which would waste space) so copy the
            // collection to the proper spot
            //
            if (IS_ATTR_ARRAY(pa))
            {
                PIPP_ATTR pcol;
                char *pvar;
                int i;

                // the intializer is an array of pointers to actual collections
                // instead of the the actual collections in a sequence
                //
                pvar = (char*)pa->var;
                pa->curdim = 0;

                // form the varsize the same way it was formed in the first pass
                //
                if (pa->maxlen <= 0)
                {
                    NET_WARN("collection stride not set");
                    pa->maxlen = 1024; // just to get past this error and fail later
                }
                varsize = pa->maxlen * IPP_MAX_COLLECTION_SET;

                // copy each collection
                //
                for (i = 0; i < IPP_MAX_MEDIA; i++)
                {
                    pcol = ((PIPP_ATTR *)psrc->var)[i];
                    if (pcol)
                    {
                        SET_ATTR(pcol);
                        IPPATTRcopyCollection(pvar, pcol->name, pcol, pa->maxlen);
                        pvar += pa->maxlen;
                    }
                    else
                    {
                        break;
                    }
                }
                pa->curdim = i;
            }
            else
            {
                if (pa->maxlen <= 0)
                {
                    NET_WARN("collection stride not set");
                    pa->maxlen = 1024; // just to get past this error and fail later
                }
                varsize = pa->maxlen;
                IPPATTRcopyCollection((char *)pa->var, pa->name, psrc, pa->maxlen);
            }
        }
        else if (AttributeIsStringType(pa))
        {
            // string initializers are encoded, so decode them into values
            // (leaving octet encodings in place)
            //
            if (IS_ATTR_ARRAY(pa))
            {
                int count;

                if (pa->maxlen <= 0)
                {
                    // assume standard max-string if not explicit
                    //
                    pa->maxlen = IPP_MAX_TEXT;
                }
                if (pa->maxlen > pa->varsize)
                {
                    // this limits dimensionality to 1, which is fine for things that will
                    // always and only have one value.  if an attribute needs more values
                    // than are statically initialized, set varsize and maxlen in the initializer
                    //
                    pa->maxlen = pa->varsize;
                }

                //获取字符串中值的个数;
                //例如"ipp-versions-supported"属性值为["2.0\4""1.1\4""1.0"],字符串中包含3个value，则count = 3
                count = AttributeStringValueCount((char*)psrc->var);
                //将字符串中value的个数与定义可存储的最大value个数(属性中value累加的总最大长度/单个value的最大长度)进行比较;
                if (count > (pa->varsize / pa->maxlen))
                {
                    NET_WARN("Warning - not enough room for strings for %s, count<%d> versize<%d> maxlen<%d>", pa->name, count, pa->varsize, pa->maxlen);
                }
                // extract each string in the encoding and copy to the
                // array-stored result
                //
                for (index = 0; index < pa->varsize / pa->maxlen && index < count; index++)
                {
                    GetIndexedString((char *)psrc->var, index, (char *)pa->var + (index * pa->maxlen), pa->maxlen);
                }
                // update dimensionality
                NET_TRACE("index<%d>", index);
                pa->curdim = index;
            }
            else
            {
                if (pa->maxlen <= 0)
                {
                    // assume standard max-string if not explicit
                    //
                    pa->maxlen = pa->varsize;
                }
                if (pa->maxlen <= 0)
                {
                    NET_WARN("Can't get length for %s", pa->name);
                }
                GetIndexedString((char *)psrc->var, 0, (char *)pa->var, pa->maxlen);
            }
        }
        else if (AttributeIsIntType(pa))
        {
            if (! pa->curdim)
            {
                // if dimensionality not explicitly set, assume all values valid
                //
                pa->curdim = psrc->varsize / sizeof(int);
            }
            pa->maxlen = sizeof(int);

            // ints get a 1:1 copy of source to dest
            //
            memcpy(pa->var, psrc->var, pa->varsize);
            ((char*)pa->var)[pa->varsize] = 0;
            ((char*)pa->var)[pa->varsize + 1] = 0;
        }
        else
        {
            NET_WARN("Bad type for %s", pa->name);
        }
        allocz += varsize + 2;
    }
    *pAttribs = pset;
    return 0;
}

/**
* @brief 销毁PrinterAttribute
*
* @param pAttribs           要销毁的IPP字段
* @return 执行结果
* @return <ul><li>0:执行成功</li><li>-1:执行失败</li></ul>
*/
static int IPPATTRdestroyPrinterAttributeSet(PIPP_ATTR pAttribs)
{
    PIPP_ATTR pa;

    // the first record in the attribute set
    // point to the large data allocation, so free that
    //
    ATTRLOCK();
    pa = (IPP_ATTR_S *)pAttribs;
    if (! pa)
    {
        ATTRUNLOCK();
        return -1;
    }
    if (pa->var)
    {
        pi_free(pa->var);
    }
    pi_free(pa);
    ATTRUNLOCK();
    return 0;
}

static IPP_ATTR_S s_ipp_operation_attributes[] =
{
    {"attributes-charset",          IPP_TAG_CHARSET,    ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"attributes-natural-language", IPP_TAG_LANGUAGE,   ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"printer-uri",                 IPP_TAG_URI,        ATTR_REQUIRED,      NULL,                   IPP_MAX_URI + 2                                                           },
    {"document-uri",                IPP_TAG_URI,        ATTR_OPTIONAL,      NULL,                   IPP_MAX_URI + 2                                                           },
    {"requested-attributes",        IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    NULL,                   IPP_MAX_NAME * IPP_MAX_OPERATION_ATTRIBUTES + 2,    IPP_MAX_NAME, 0       },
    {"requesting-user-name",        IPP_TAG_NAME,       ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME                                                              },
    {"job-name",                    IPP_TAG_NAME,       ATTR_OPTIONAL,      NULL,                   IPP_MAX_NAME                                                              },
    {"copies",                      IPP_TAG_INTEGER,    ATTR_JOBTEMPLATE,   NULL,                   4                                                                         },
    {"job-pages-per-set",           IPP_TAG_INTEGER,    ATTR_JOBTEMPLATE,   NULL,                   4                                                                         },
    {"media",                       IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"media-col",                   IPP_TAG_COLLECTION, ATTR_JOBTEMPLATE,   s_media_col_default,    0,                                                  IPP_MEDIA_COL_SIZE, 0 },
#if IPP_SUPPORTS_OVERRIDES
    {"overrides",                   IPP_TAG_COLLECTION, ATTR_COMBO_1010,    s_overrides,            0,                                                  IPP_OVERRIDE_SIZE, 0  },
#endif
    {"ipp-attribute-fidelity",      IPP_TAG_BOOLEAN,    ATTR_OPTIONAL ,     NULL,                   4                                                                         },
    {"orientation-requested",       IPP_TAG_ENUM,       ATTR_JOBTEMPLATE,   NULL,                   4                                                                         },
    {"output-bin",                  IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"multiple-document-handling",  IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"print-color-mode",            IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"print-quality",               IPP_TAG_ENUM,       ATTR_JOBTEMPLATE,   NULL,                   4                                                                         },
    {"printer-resolution",          IPP_TAG_RESOLUTION, ATTR_COMBO_0011,    NULL,                   IPP_MAX_ENCRES + 2                                                        },
    {"print-scaling",               IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"print-content-optimize",      IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"sides",                       IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"finishings",                  IPP_TAG_ENUM,       ATTR_COMBO_1010,    NULL,                   IPP_MAX_NAME * 4 + 2,                               4,                  0 },
    {"finishings-col",              IPP_TAG_COLLECTION, ATTR_JOBTEMPLATE,   s_finishings_col_none,  0,                                                  256,                0 },
    {"page-ranges",                 IPP_TAG_RANGE,      ATTR_COMBO_1010,    NULL,                   IPP_RANGE_SIZE * IPP_MAX_PAGERANGES,                IPP_RANGE_SIZE, 0     },
    {"document-name",               IPP_TAG_NAME,       ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"document-format",             IPP_TAG_MIMETYPE,   ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"document-natural-language",   IPP_TAG_LANGUAGE,   ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"compression",                 IPP_TAG_KEYWORD,    ATTR_OPTIONAL,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"job-uri",                     IPP_TAG_URI,        ATTR_OPTIONAL,      NULL,                   IPP_MAX_URI + 2                                                           },
    {"job-id",                      IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"limit",                       IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"which-jobs",                  IPP_TAG_KEYWORD,    ATTR_OPTIONAL,      NULL,                   IPP_MAX_NAME                                                              },
    {"job-ids",                     IPP_TAG_INTEGER,    ATTR_ARRAY,         NULL,                   IPP_MAX_JOBIDS                                                            },
    {"my-jobs",                     IPP_TAG_BOOLEAN,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"job-k-octets",                IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"job-impressions",             IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"job-media-sheets",            IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"last-document",               IPP_TAG_BOOLEAN,    ATTR_OPTIONAL,      NULL,                   4                                                                         },
    {"identify-actions",            IPP_TAG_KEYWORD,    ATTR_COMBO_1001,    NULL,                   IPP_IDENTIFY_ACTIONS_COUNT * IPP_MAX_NAME + 2,      IPP_MAX_NAME,    0    },
    {"message",                     IPP_TAG_TEXT,       ATTR_REQUIRED,      NULL,                   IPP_MAX_NAME + 2                                                          },
    {"output-mode",                 IPP_TAG_KEYWORD,    ATTR_JOBTEMPLATE,   NULL,                   IPP_MAX_NAME + 2                                                          },
    {"pdf-fit-to-page",             IPP_TAG_BOOLEAN,    ATTR_JOBTEMPLATE,   &s_pdf_fit_to_page,     sizeof(s_pdf_fit_to_page)                                                 },
    {NULL,                          IPP_TAG_INTEGER,    ATTR_OPTIONAL,      NULL,                   0                                                                         }
};

//**************************************************************************
int IPPATTRgetSetOperationAttributes(PIPPCTX ppp, IPPATTRCB callback, void *cookie)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = ppp->operation_attributes; pa->name; pa++)
    {
        if (IS_ATTR_SET(pa) && pa->var)
        {
            if (callback(cookie, pa->name, pa->type, pa->var))
            {
                break;
            }
        }
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRsetJobAttributesFromSetOperationAttributes(PIPPCTX ppp, void *pSet)
{
    PIPP_ATTR pa;
    int rc;

    rc = 0;
    ATTRLOCK();
    for (pa = ppp->operation_attributes; pa->name; pa++)
    {
        if (IS_ATTR_SET(pa) && pa->var)
        {
            if (! strcmp(pa->name,"ipp-attribute-fidelity"))
            {
                rc = ippattr_set_attribute((IPP_ATTR_S *)pSet, "Job", pa->name, 0, pa->var);
            }

            if (IS_ATTR_JOBTEMPLATE(pa))
            {
                if (IS_ATTR_ARRAY(pa))
                {
                    int i;
                    uint8_t *pvar;

                    for (i = 0; i < pa->curdim && rc == 0; i++)
                    {
                        pvar = (uint8_t *)pa->var + i * pa->maxlen;
                        rc = ippattr_set_attribute((IPP_ATTR_S *)pSet, "Job", pa->name, i, pvar);
                    }
                }
                else
                {
                    rc = ippattr_set_attribute((IPP_ATTR_S *)pSet, "Job", pa->name, 0, pa->var);
                }
            }
        }
    }
    ATTRUNLOCK();
    return rc;
}

int IPPATTRgetOperationAttribute(PIPPCTX ppp, const char *name, int *type, void **value, int index, int *count)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    //NET_DEBUG("attribute name: '%s'", name);
    for (pa = ppp->operation_attributes; pa->name; pa++)
    {
        if (strcmp(pa->name, name) == 0)
        {
            //NET_DEBUG("match operation attribute(%s)", name);
            if (!(IS_ATTR_SET(pa)))
            {
                //NET_INFO("attribute(%s) unsupport to set", name);
                *type = pa->type;
                if (AttributeIsIntType(pa))
                {
                    *(int *)value = 0;
                }
                else if (pa->type == IPP_TAG_COLLECTION)
                {
                    *(PIPP_ATTR *)value = NULL;
                }
                else
                {
                    *(char **)value = "no-value";
                }
                ATTRUNLOCK();
                return 1;
            }
            break;
        }
    }
    ATTRUNLOCK();

    return IPPATTRgetAttribute(ppp->operation_attributes, "Operation", name, index, type, value, count);
}

//**************************************************************************
int IPPATTRsetOperationAttribute(PIPPCTX ppp, const char *name, void *value, int index)
{
    return ippattr_set_attribute_locked(ppp->operation_attributes, "Operation", name, value, index);
}

//**************************************************************************
int IPPATTRcheckOperationAttribute(PIPPCTX ppp, const char* name, int32_t pedantic, int32_t group, int32_t type)
{
    PIPP_ATTR pa;
    int rc;

    rc = IPP_ATTRIBUTES;
    ATTRLOCK();
    for (pa = ppp->operation_attributes; pa->name; pa++)
    {
        if (! strcmp(pa->name, name))
        {
            rc = IPP_OK;

            // check type to incoming type
            //
            if (type != pa->type)
            {
                // if both are int types, or both string types, the operation
                // can work, so don't error at low pedantic levels
                //
                if (
                        (IsIntType(type) && IsIntType(pa->type))
                    ||  (IsStringType(type) && IsStringType(pa->type))
                )
                {
                    // compatible types at least
                    //
                    NET_DEBUG("(compatible) Type mismatch: %s is %d, not %d", pa->name, pa->type, type);
                    /* add "requested-attributes" to bug 74142*/
                    if (pedantic && strcmp("requested-attributes", name))
                    {
                        rc = IPP_NOT_POSSIBLE;
                    }
                }
                else
                {
                    NET_WARN("Type mismatch: %s is %d, not %d",
                            pa->name, pa->type, type);
                    rc = IPP_NOT_POSSIBLE;
                }
            }
            if (rc == IPP_OK)
            {
                // types are OK, check that job template attributes are
                // in the job attribute group
                //
                if (group == IPP_TAG_JOB)
                {
                    if (! (pa->flags & ATTR_JOBTEMPLATE))
                    {
                        if (pedantic)
                        {
                            NET_WARN("Wrong group. %s is not a Job attribute", pa->name);
                            rc = IPP_NOT_POSSIBLE;
                        }
                        else
                        {
                            NET_WARN("Wrong group. %s is not a Job attribute", pa->name);
                        }
                    }
                }
                else if (group == IPP_TAG_OPERATION)
                {
                    if (pa->flags & ATTR_JOBTEMPLATE)
                    {
                        if (pedantic)
                        {
                            NET_WARN("Wrong group. %s is a Job attribute", pa->name);
                            rc = IPP_NOT_POSSIBLE;
                        }
                        else
                        {
                            NET_WARN("Wrong group. %s is a Job attribute", pa->name);
                        }
                    }
                }
                else
                {
                    NET_WARN("Bad group: %d", group);
                    if (pedantic)
                    {
                        rc = IPP_INTERNAL_ERROR;
                    }
                }
            }
            ATTRUNLOCK();
            return rc;
        }
    }
    ATTRUNLOCK();
    NET_WARN("%s is not an operation attribute", name);

    return rc;
}

//**************************************************************************
int IPPATTRrequestOperationAttribute(PIPPCTX ppp, const char *name)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = ppp->operation_attributes; pa->name; pa++)
    {
        if (! strcmp(pa->name, name))
        {
            REQUEST_ATTR(pa);
            ATTRUNLOCK();
            return 0;
        }
    }
    ATTRUNLOCK();
    NET_WARN("Requested Attribute %s not in table", name);
    return 1;
}

//**************************************************************************
int IPPATTRrequestAllOperationAttributes(PIPPCTX ppp, int onoff)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = ppp->operation_attributes; pa->name; pa++)
    {
        if (onoff)
        {
            REQUEST_ATTR(pa);
        }
        else
        {
            UNREQUEST_ATTR(pa);
        }
    }
    ATTRUNLOCK();
    return 0;
}

/**
 * @brief   初始化IPP Context中operation_attributes属性
 *
 * @param   ppp         IPP_CTX_S结构体指针
 *
 * @return  返回处理结果
 * @return  <ul><li>0:成功</li><li>-1:失败</li></ul>
 */
int IPPATTRclearOperationAttributes(PIPPCTX ppp)
{
    PIPP_ATTR pa, pd;
    uint8_t*    vptr;
    int32_t     varsize;
    int32_t     i, room;

    vptr = ppp->varblock;
    room = sizeof(ppp->varblock);

    //将s_ipp_operation_attributes中的属性及设定到ppp->operation_attributes中,作为初始值
    ATTRLOCK();
    for (
            i = 0, pa = s_ipp_operation_attributes;
            pa->name && i < IPP_MAX_OPERATION_ATTRIBUTES && room > 4;
            pa++, i++
    )
    {
        pd = &ppp->operation_attributes[i];
        pd->name  = pa->name;
        pd->type  = pa->type;
        pd->flags = pa->flags;
        pd->maxlen = pa->maxlen;
        pd->curdim = 0;
        // alloc bytes for vars from context's varblock
        pd->varsize = pa->varsize;
        pd->var = (void *)vptr;
        UNREQUEST_ATTR(pd);
        UNSET_ATTR(pd);
        varsize = pd->varsize;
        if (varsize <= 0)
        {
            // assume 0 varsize is for a pointer to collection type
            //
            if (pd->type != IPP_TAG_COLLECTION)
            {
                NET_WARN("0 for varsize in non collection %s", pa->name);
            }
            if (pa->maxlen)
            {
                // if there is a max element size/stride use that for
                // varsize area, and if an array, allow for max indexing
                //
                varsize = pa->maxlen;
                if (IS_ATTR_ARRAY(pa))
                {
                    varsize *= IPP_MAX_COLLECTION_SET;
                }
            }
        }
        if (AttributeIsIntType(pd))
        {
            *(int *)pd->var = 0;
        }
        else if (pd->type == IPP_TAG_COLLECTION)
        {
            // Collection building in operation works by filling in an existing
            // default collection with incoming values.  To make the default
            // collection in the var area here, we use a collection initializer
            // like for printer attributes and deep-copy it into the var area.
            // Since collection setting in operations is rare, I defer the actual
            // copy to begin-collection time to avoid the overhead here.  To
            // do the copy then, I put in the initializer for the attribute into
            // the var space, so the begin-collection code can get the pointer
            // the the actual collection
            //
            memcpy(pd->var, pa, sizeof(IPP_ATTR_S));
        }
        else
        {
            *(char *)pd->var = '\0';
        }
        varsize += 3;
        varsize &= ~3;
        vptr += varsize;
        room -= varsize;
    }
    if (i >= IPP_MAX_OPERATION_ATTRIBUTES)
    {
        NET_WARN("Need to add more to IPP_MAX_OPERATION_ATTRIBUTES");
        ATTRUNLOCK();
        return 1;
    }
    if (room < 4)
    {
        NET_WARN("Need to increase size of varblock");
        ATTRUNLOCK();
        return 1;
    }
    while (i < IPP_MAX_OPERATION_ATTRIBUTES)
    {
        ppp->operation_attributes[i].name = NULL;
        ppp->operation_attributes[i].type = 0;
        i++;
    }
    ATTRUNLOCK();
    return 0;
}

// note that this doesn't get used directly, it is used to
// create one set of attributes for each job
static IPP_ATTR_S s_ipp_job_attributes[] =
{
    {"job-uri",                    IPP_TAG_URI,         ATTR_REQUIRED,      NULL,       IPP_MAX_URI                                               },
    {"job-id",                     IPP_TAG_INTEGER,     ATTR_REQUIRED,      NULL,       4                                                         },
    {"job-uuid",                   IPP_TAG_URI,         ATTR_REQUIRED_20,   NULL,       IPP_MAX_URI                                               },
    {"job-printer-uri",            IPP_TAG_URI,         ATTR_REQUIRED,      NULL,       IPP_MAX_URI                                               },
    {"job-more-info",              IPP_TAG_URI,         ATTR_OPTIONAL,      NULL,       IPP_MAX_URI                                               },
    {"job-name",                   IPP_TAG_NAME,        ATTR_REQUIRED,      NULL,       IPP_MAX_NAME                                              },
    {"copies",                     IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"job-pages-per-set",          IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"media",                      IPP_TAG_KEYWORD,     ATTR_OPTIONAL,      NULL,       IPP_MAX_NAME + 2                                          },
    {"media-col",                  IPP_TAG_COLLECTION,  ATTR_OPTIONAL,      NULL,       0,                                  IPP_MEDIA_COL_SIZE, 0 },
#if IPP_SUPPORTS_OVERRIDES
    {"overrides",                  IPP_TAG_COLLECTION,  ATTR_COMBO_1010,    s_overrides,0,                                   IPP_OVERRIDE_SIZE, 0 },
#endif
    {"ipp-attribute-fidelity",     IPP_TAG_BOOLEAN,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"orientation-requested",      IPP_TAG_ENUM,        ATTR_OPTIONAL,      NULL,       4                                                         },
    {"output-bin",                 IPP_TAG_KEYWORD,     ATTR_OPTIONAL,      NULL,       IPP_MAX_NAME + 2                                          },
    {"multiple-document-handling", IPP_TAG_KEYWORD,     ATTR_JOBTEMPLATE,   NULL,       IPP_MAX_NAME + 2                                          },
    {"print-color-mode",           IPP_TAG_KEYWORD,     ATTR_OPTIONAL,      NULL,       IPP_MAX_NAME + 2                                          },
    {"print-quality",              IPP_TAG_ENUM,        ATTR_OPTIONAL,      NULL,       4                                                         },
    {"printer-resolution",         IPP_TAG_RESOLUTION,  ATTR_REQUIRED,      NULL,       IPP_MAX_ENCRES + 2                                        },
    {"print-scaling",              IPP_TAG_KEYWORD,     ATTR_JOBTEMPLATE,   NULL,       IPP_MAX_NAME + 2                                          },
    {"sides",                      IPP_TAG_KEYWORD,     ATTR_JOBTEMPLATE,   NULL,       IPP_MAX_NAME + 2                                          },
    {"finishings",                 IPP_TAG_ENUM,        ATTR_COMBO_1010,    NULL,       IPP_MAX_NAME * 4 + 2 ,                               4, 0 },
    {"finishings-col",             IPP_TAG_COLLECTION,  ATTR_JOBTEMPLATE,   NULL,       0,                                                 256, 0 },
    {"page-ranges",                IPP_TAG_RANGE,       ATTR_COMBO_1010,    NULL,       IPP_RANGE_SIZE * IPP_MAX_PAGERANGES,    IPP_RANGE_SIZE, 0 },
    {"job-originating-user-name",  IPP_TAG_NAME,        ATTR_REQUIRED,      NULL,       IPP_MAX_NAME                                              },
    {"job-state",                  IPP_TAG_ENUM,        ATTR_REQUIRED,      NULL,       4                                                         },
    {"job-state-reasons",          IPP_TAG_KEYWORD,     ATTR_COMBO_1001,    NULL,       IPP_MAX_NAME * 4 + 2,                     IPP_MAX_NAME, 0 },
    {"job-state-message",          IPP_TAG_TEXT,        ATTR_OPTIONAL,      NULL,       IPP_MAX_NAME                                              },
    {"number-of-documents",        IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"output-device-assigned",     IPP_TAG_NAME,        ATTR_OPTIONAL,      NULL,       IPP_MAX_NAME                                              },
    {"time-at-creation",           IPP_TAG_INTEGER,     ATTR_REQUIRED,      NULL,       4                                                         },
    {"time-at-processing",         IPP_TAG_INTEGER,     ATTR_REQUIRED,      NULL,       4                                                         },
    {"time-at-completed",          IPP_TAG_INTEGER,     ATTR_REQUIRED,      NULL,       4                                                         },
    {"job-printer-up-time",        IPP_TAG_INTEGER,     ATTR_REQUIRED,      NULL,       4                                                         },
    {"number-of-intervening-jobs", IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"job-message-from-operator",  IPP_TAG_TEXT,        ATTR_OPTIONAL,      NULL,       IPP_MAX_NAME                                              },
    {"job-k-octets",               IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"job-impressions",            IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"job-impressions-completed",  IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"job-media-sheets-completed", IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       4                                                         },
    {"qio-handle",                 IPP_TAG_TEXT,        ATTR_EXPLICIT,      NULL,       32                                                        },
    {"document-number",            IPP_TAG_INTEGER,     ATTR_EXPLICIT,      NULL,       4                                                         },
    {"pdl-set",                    IPP_TAG_INTEGER,     ATTR_EXPLICIT,      NULL,       4                                                         },
    {NULL,                         IPP_TAG_INTEGER,     ATTR_OPTIONAL,      NULL,       0                                                         }
};

//**************************************************************************
int IPPATTRgetRequestedJobAttributes(void *pSet, IPPATTRCB callback, void *cookie)
{
    PIPP_ATTR pa, panext;
    int rc;

    rc = 0;
    ATTRLOCK();
    for (pa = (IPP_ATTR_S *)pSet; pa->name;)
    {
        // for each attr in set, report it up if its requested and it's either set, or
        // it's a required attribute
        //
        if (IS_ATTR_REQUESTED(pa) && (IS_ATTR_REQUIRED(pa) || IS_ATTR_SET(pa)) && pa->var)
        {
            panext = pa;
            rc = IPPATTRreportAttribute(&panext, IS_ATTR_REQUIRED(pa), 0, callback, cookie);
            if (pa == panext || rc != 0)
            {
                NET_WARN("Request stalled or failed");
                break;
            }
            pa = panext;
        }
        else
        {
            pa++;
        }
    }
    ATTRUNLOCK();
    return rc;
}

//**************************************************************************
int IPPATTRgetJobAttributeIsSet(void *pSet, const char *name, int *isSet)
{
    PIPP_ATTR pa;

    *isSet = 0;
    ATTRLOCK();
    for (pa = (IPP_ATTR_S *)pSet; pa->name; pa++)
    {
        if (! strcmp(pa->name, name))
        {
            if (IS_ATTR_SET(pa) && pa->var)
            {
                *isSet = 1;
            }
            ATTRUNLOCK();
            return 0;
        }
    }
    ATTRUNLOCK();
    return 1;
}

//**************************************************************************
int IPPATTRgetJobAttribute(void *pSet, const char *name, int *type, void **value, int index, int *count)
{
    return IPPATTRgetAttribute((IPP_ATTR_S *)pSet, "Job", name, index, type, value, count);
}

//**************************************************************************
int IPPATTRsetJobAttribute(void *pSet, const char *name, void *value, int index)
{
    return ippattr_set_attribute_locked((IPP_ATTR_S *)pSet, "Job", name, value, index);
}

//**************************************************************************
int IPPATTRrequestJobAttribute(void *pSet, const char *name)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = (IPP_ATTR_S *)pSet; pa->name; pa++)
    {
        if (! strcmp(pa->name, name))
        {
            REQUEST_ATTR(pa);
            ATTRUNLOCK();
            return 0;
        }
    }
    ATTRUNLOCK();
    NET_WARN("Requested Attribute %s not in table", name);
    return 1;
}

//**************************************************************************
int IPPATTRrequestAllJobAttributes(void *pSet, int onoff)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = (IPP_ATTR_S *)pSet; pa->name; pa++)
    {
        if (onoff)
        {
            if (! IS_ATTR_EXPLICIT(pa))
            {
                REQUEST_ATTR(pa);
            }
        }
        else
        {
            UNREQUEST_ATTR(pa);
        }
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRsetAllJobAttributes(void *pSet, int onoff)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = (IPP_ATTR_S *)pSet; pa->name; pa++)
    {
        if (onoff)
        {
            SET_ATTR(pa);
        }
        else
        {
            UNSET_ATTR(pa);
        }
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRclearJobAttributes(void *pSet)
{
    PIPP_ATTR pa;

    ATTRLOCK();
    for (pa = (IPP_ATTR_S *)pSet; pa->name; pa++)
    {
        UNREQUEST_ATTR(pa);
        UNSET_ATTR(pa);
        if (AttributeIsIntType(pa))
        {
            *(int *)pa->var = 0;
        }
        else if (pa->type == IPP_TAG_COLLECTION)
        {
            *(PIPP_ATTR*)pa->var = NULL;
        }
        else
        {
            *(char *)pa->var = '\0';
        }
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRcreateJobAttributeSet(void **pAttribs)
{
    PIPP_ATTR pa, pset;
    char *data;
    int varsize;
    int allocz, i;

    *pAttribs = NULL;
    pset = (IPP_ATTR_S *)pi_zalloc(sizeof(s_ipp_job_attributes));
    if (! pset)
    {
        NET_WARN("Can't alloc new job attribute set");
        return -1;
    }
    memset(pset, 0x00, sizeof(s_ipp_job_attributes));

    // start the new set as a direct copy of base set
    memcpy(pset, s_ipp_job_attributes, sizeof(s_ipp_job_attributes));

    // go through attribute set and add up all the sizes of vars or space needed
    //
    for (pa = pset, allocz = 0; pa->name; pa++)
    {
        varsize = pa->varsize;
        varsize += 3;
        varsize &= ~3;
        if (varsize <= 0)
        {
            // assume 0 varsize is for a pointer to collection type
            //
            if (pa->type != IPP_TAG_COLLECTION)
            {
                NET_WARN("0 for varsize in non collection %s", pa->name);
            }
            if (pa->maxlen)
            {
                // if there is a max element size/stride use that for
                // varsize area, and if an array, allow for max indexing
                //
                varsize = pa->maxlen;
                if (IS_ATTR_ARRAY(pa))
                {
                    varsize *= IPP_MAX_COLLECTION_SET;
                }
            }
        }
        allocz += varsize + 2;
    }
    // allocate one big chunk to hold all the var data
    data = pi_zalloc(allocz);
    if (! data)
    {
        pi_free(pset);
        NET_WARN("Can't alloc data for set");
        return -1;
    }
    memset(data, 0x00, allocz);

    // go through set and assign pointers to data and init to 0
    // note that ALL attributes in this set will now have a 0
    // or NULL value and they must be set individually, unlike
    // when a printer attribute is created where we duplicate
    // the initiliazer set values
    //
    for (pa = pset, allocz = 0; pa->name; pa++)
    {
        pa->var = (void *)(data + allocz);
        varsize = pa->varsize;
        varsize += 3;
        varsize &= ~3;
        if (varsize <= 0)
        {
            // assume 0 varsize is for a pointer to collection type
            //
            if (pa->type != IPP_TAG_COLLECTION)
            {
                NET_WARN("0 for varsize in non collection %s", pa->name);
            }
            if (pa->maxlen)
            {
                // if there is a max element size/stride use that for
                // varsize area, and if an array, allow for max indexing
                //
                varsize = pa->maxlen;
                if (IS_ATTR_ARRAY(pa))
                {
                    varsize *= IPP_MAX_COLLECTION_SET;
                }
            }
        }
        else
        {
            for (i = 0; i < pa->varsize && i < 4; i++)
            {
                ((char *)pa->var)[i] = '\0';
            }
        }
        allocz += varsize + 2;
    }
    *pAttribs = pset;
    return 0;
}

//**************************************************************************
int IPPATTRdestroyJobAttributeSet(void *pAttribs)
{
    PIPP_ATTR pa;

    // the first record in the attribute set
    // point to the large data allocation, so free that
    //
    ATTRLOCK();
    pa = (IPP_ATTR_S *)pAttribs;
    if (! pa)
    {
        ATTRUNLOCK();
        return -1;
    }
    if (pa->var)
    {
        pi_free(pa->var);
    }
    pi_free(pa);
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRaddUnsupportedAttribute(PIPPCTX ppp, const char *name)
{
    PIPP_ATTR pa;
    int i;

    ATTRLOCK();
    for (
            i = 0, pa = ppp->unsupported_attributes;
            i < IPP_MAX_UNSUPPORTED_ATTRIBUTES;
            i++, pa++
    )
    {
        if (! pa->name)
        {
            pa->name = (char *)pi_zalloc(strlen(name) + 1);
            if (pa->name)
            {
                memset((void *)pa->name, 0x00, strlen(name) + 1);
                strcpy((char *)pa->name, name);
            }

            pa->type = IPP_TAG_UNSUPPORTED_VALUE;
            pa->var  = NULL;
            pa->varsize = 0;
            REQUEST_ATTR(pa);
            SET_ATTR(pa);
            ATTRUNLOCK();
            return 0;
        }
    }
    ATTRUNLOCK();
    NET_WARN("No room for more unsupported attributes");
    return 1;
}

//**************************************************************************
int IPPATTRgetUnsupportedAttributes(PIPPCTX ppp, IPPATTRCB callback, void *cookie)
{
    PIPP_ATTR pa;
    int i;

    ATTRLOCK();
    for (
            i = 0, pa = ppp->unsupported_attributes;
            i < IPP_MAX_UNSUPPORTED_ATTRIBUTES;
            i++, pa++
    )
    {
        if (IS_ATTR_REQUESTED(pa) && pa->name)
        {
            if (callback(cookie, pa->name, pa->type, pa->var))
            {
                break;
            }
        }
        else
        {
            break;
        }
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRclearUnsupportedAttributes(PIPPCTX ppp, int stomp)
{
    PIPP_ATTR pa;
    int i;

    ATTRLOCK();
    for (
            i = 0, pa = ppp->unsupported_attributes;
            i < IPP_MAX_UNSUPPORTED_ATTRIBUTES;
            i++, pa++
    )
    {
        UNREQUIRE_ATTR(pa);
        UNREQUEST_ATTR(pa);
        UNSET_ATTR(pa);
        if ( stomp == 0 )
        {
            if (pa->name)
            {
                pi_free(pa->name);
                pa->name = NULL;
            }
        }
        else
        {
            pa->name = NULL;
        }
        pa->type = 0;
        pa->var  = NULL;
        pa->varsize = 0;
    }
    ATTRUNLOCK();
    return 0;
}

//**************************************************************************
int IPPATTRbeginCollection(PIPPCTX ppp, int *plevel, const char *name, int index)
{
    PIPP_ATTR pa, psrc;
    int level = *plevel;
    int n;

    NET_DEBUG("Begin collection %s at %d dex %d", name, level, index);

    // There is no reason to support arbitrary collection building, since the
    // only operation attributes that can be set are the ones in our table.
    // The collections in our operation attributes table where initialized to
    // the default when the table was setup in IPPATTRclearOperationAttributes.
    //
    // How collection building works is the default/initializer collection is
    // copied to a prototype collection, and then incoming members are reconciled
    // against that member-by member and just the value of the incoming member
    // copied to the correlating member in the copy's var area.  When the
    // collection is ended, the copy is used to set the actually collection in
    // the operation attribute's table.
    //
    // For arrays, the *first* element compiled in is used to create the copy
    // so arrays of initializers really only need to have one entry, but that
    // entry has to point to an initializer that has var-sizes big enough to
    // hold any possible version of the collection and its members.
    //
    if (level >= IPP_MAX_COLLECTION_DEPTH - 1 || level < 0)
    {
        return 0;
    }
    switch (level)
    {
    case 0:
        // reset col state
        //
        for (n = 0; n < IPP_MAX_COLLECTION_DEPTH; n++)
        {
            ppp->colltree[n].type = 0;
        }
        // At the first level, find the collection by name in our
        // operations table.  if it's not there, we won't support it
        //
        for (pa = ppp->operation_attributes; pa->name; pa++)
        {
            if (strcmp(name, pa->name) == 0)
            {
                break;
            }
        }
        if (pa->name == NULL)
        {
            NET_DEBUG("Collection %s not supported", name);
            return IPP_ATTRIBUTES;
        }
        if (index == 0)
        {
            // initialize the collection in the instance's operations table from the initializer.
            // this was deferred from table creation time since there's no reason to do it unless the
            // collection was in the request.  The initializer attribute was copied into the var
            // space for this entry, so copy it back out and overwrite it for all possible indices
            //
            psrc = (IPP_ATTR_S *)pa->var;
            if (! psrc || psrc->type != IPP_TAG_COLLECTION)
            {
                NET_WARN("Collection has no initializer");
                return IPP_INTERNAL_ERROR;
            }
            if (psrc->maxlen > sizeof(ppp->collbuff))
            {
                NET_WARN("collection buffer too small for %s", name);
                return IPP_INTERNAL_ERROR;
            }
            if (IS_ATTR_ARRAY(pa))
            {
                IPP_ATTR_S ia;
                PIPP_ATTR pcol;
                PIPP_ATTR *pinit;
                char *pvar;

                // the initializer for arrays of collections is an array of pointers to
                // actual collections instead of the actual collections in a sequence
                //
                // just use the first one to build a prototype collection, since IPP
                // will only set one index at a time, we only need the one to play with
                //
                // first, copy the initializer attribute out of the table, as it will
                // be overwritten with the actual collection[0]
                //
                memcpy(&ia, psrc, sizeof(IPP_ATTR_S));
                if (ia.maxlen <= 0)
                {
                    NET_WARN("collection stride not set");
                    return IPP_INTERNAL_ERROR;
                }
                // get the first initilizer element out of the array
                //
                pinit = (PIPP_ATTR *)ia.var;
                pcol = pinit[0];

                // copy over one collection initializer to all indices
                //
                if (pcol && AttributeIsCollectionType(pcol))
                {
                    int idex;

                    pvar = (char*)pa->var;
                    for (idex = 0; idex < IPP_MAX_COLLECTION_SET; idex++)
                    {
                        IPPATTRcopyCollection(pvar, pcol->name, pcol, pa->maxlen);
                        pvar += pa->maxlen;
                    }
                }
                else
                {
                    NET_WARN("No initializer for %s", name);
                    return IPP_INTERNAL_ERROR;
                }
            }
            else
            {
                // If the initializer member is an indirection, which is should be, follow it here
                //
                while (psrc->varsize == 0 && psrc->var != NULL)
                {
                    psrc = (IPP_ATTR_S *)psrc->var;
                }
                if (! psrc || ! psrc->varsize)
                {
                    NET_WARN("No collection or size for %s", name);
                    return IPP_INTERNAL_ERROR;
                }
                if (pa->maxlen <= 0)
                {
                    NET_WARN("collection stride not set");
                    return IPP_INTERNAL_ERROR;
                }
                IPPATTRcopyCollection((char *)pa->var, pa->name, psrc, pa->maxlen);
            }
        }
        // after index-0 initialized the attributes table (for all indices) use it
        // as the source to create a copy of the collection to parse input with
        //
        psrc = (IPP_ATTR_S *)((uint8_t *)pa->var + index * pa->maxlen);

        // check to make sure it was built right
        //
        if (! AttributeIsCollectionType(psrc))
        {
            NET_WARN("Not a collection at index %d", index);
            return IPP_INTERNAL_ERROR;
        }
        if (psrc->varsize == 0)
        {
            NET_WARN("Indirected collection at index %d", index);
        }
        // since we're setting only one index at a time, this is not an array
        // even if the underlying collection is
        //
        pa = &ppp->colltree[level];
        memcpy(pa, psrc, sizeof(IPP_ATTR_S));

        if (IS_ATTR_ARRAY(pa))
        {
            NET_WARN("Array of arrays %s[%d]", pa->name, index);
            return IPP_INTERNAL_ERROR;
        }
        // make sure the copied coll. indirection points to our copy
        //
        pa->varsize = 0;
        pa->var = ppp->collbuff;

        // now deep-copy the initializer to a flattened collection
        // in the collection data buffer
        //
        IPPATTRcopyCollection((char*)ppp->collbuff, pa->name, psrc, pa->maxlen);
        *plevel = level + 1;
        break;

    default:

        // get a pointer to the current collection member list
        //
        pa = &ppp->colltree[level - 1];
        if (! pa)
        {
            return IPP_INTERNAL_ERROR;
        }
        if (pa->type != IPP_TAG_COLLECTION)
        {
            // strange, can't happen
            return IPP_INTERNAL_ERROR;
        }
        // Follow indirection to flattened collection
        //
        while (pa->varsize == 0 && pa->var != NULL)
        {
            pa = (IPP_ATTR_S *)pa->var;
        }
        if (! pa || ! pa->varsize)
        {
            NET_WARN("No collection or size for %s", name);
            return IPP_INTERNAL_ERROR;
        }
        // find this collection member in the member list skipping collections
        // that don't match the name we want
        //
        ppp->colltree[level].type = 0;

        for (n = pa->varsize, pa = pa+2; n > 0; n = n-2, pa = pa+2)
        {
            if (AttributeIsCollectionType(pa))
            {
                int rc;
                PIPP_ATTR pm = pa - 1;

                if (! strcmp(pm->var, name))
                {
                    // setup this level's collection to indirect
                    // to this member of the flattened buffered collection
                    //
                    psrc = pa;
                    pa = &ppp->colltree[level];
                    memcpy(pa, psrc, sizeof(IPP_ATTR_S));
                    pa->flags &= ~ATTR_ARRAY;
                    // this sets up the indirection
                    pa->var = (void*)psrc;
                    pa->varsize = 0;
                    break;
                }
                // not the collection we're looking for, so
                // skip over it (recursively)
                //
                rc = SkipOverCollection(&pa);
                if (rc)
                {
                    break;
                }
            }
        }
        if (! ppp->colltree[level].type)
        {
            NET_DEBUG("No such collection %s in collection %s", name, ppp->colltree[level - 1].name);
            return IPP_ATTRIBUTES;
        }
        *plevel = level + 1;
        break;
    }
    return IPP_OK;
}

//**************************************************************************
int IPPATTRaddCollectionMember(PIPPCTX ppp, int level, const char *name, int type, void *value, int index)
{
    IPP_ATTR_S* pm;
    IPP_ATTR_S* pa;
    int32_t     n;

    NET_DEBUG("Add %s, index %d to collection %d", name, index, level);

    if (level > IPP_MAX_COLLECTION_DEPTH - 1 || level < 1)
    {
        return IPP_INTERNAL_ERROR;
    }
    pa = &ppp->colltree[level - 1];
    if (! pa->type)
    {
        return IPP_INTERNAL_ERROR;
    }
    if (pa->type != IPP_TAG_COLLECTION)
    {
        // strange, can't happen
        return IPP_INTERNAL_ERROR;
    }
    // first level should be an indirection, so get to actual collection
    // flattend in the var area to play-along with
    //
    while (pa->varsize == 0 && pa->var != NULL)
    {
        pa = (IPP_ATTR_S *)pa->var;
    }
    if (! pa || ! pa->varsize)
    {
        NET_WARN("No collection");
        return IPP_INTERNAL_ERROR;
    }

    n = pa->varsize, pa = pa+2;
    while (n > 0)
    {

        pm = pa -1;

        if (AttributeIsCollectionType(pa))
        {
            // skip over sub-collections to both keep n and varsize synced
            // but also to make sure sub-collection members with same name
            // don't get matched.  this is done recursively.
            //
            if (SkipOverCollection(&pa))
            {
                return IPP_INTERNAL_ERROR;
            }
            // skipping should have left pa at first non-collection
            //
            if (! pa || AttributeIsCollectionType(pa))
            {
                return IPP_INTERNAL_ERROR;
            }
            n = n-2;
            pa--;
        }
        if (! strcmp(pm->var, name))
        {
            uint8_t *pvar;

            pvar = (uint8_t*)pa->var + index * pa->maxlen;

            if (IS_ATTR_ARRAY(pa))
            {
                if (! pa->maxlen)
                {
                    NET_WARN("No stride for %s", (char *)(pm->var));
                }
                pa->curdim++;
            }
            else if (index > 0)
            {
                NET_WARN("Not an array: %s index: %d", (char *)(pm->var), index);
            }
            // make sure type is semi-valid, to insure not mixing
            // strings and ints, etc.
            //
            if ((IsIntType(type) && ! IsIntType(pa->type)) || (IsStringType(type) && ! IsStringType(pa->type)))
            {
                NET_WARN("Type mismatch: %s is not type %d", name, type);
                return IPP_NOT_POSSIBLE;
            }
            if (AttributeIsIntType(pa))
            {
                NET_DEBUG("Set int collmem %s to %d", name, *(int*)value);
                *(int*)pvar = *(int*)value;
            }
            else if (AttributeIsStringType(pa))
            {
                int room;
                int len;
                char *psval, *psdst;

                NET_DEBUG("Set str collmem %s to %s", name, (char*)value);

                // limit size to var size
                //
                room = pa->varsize - 1;
                psval = (char*)value;
                psdst = (char *)pvar;
                if (psval && psdst)
                {
                    len = strlen(psval);
                    if (room < len)
                    {
                        NET_DEBUG("Clip value len %d to %d", len, room);
                        len = room;
                    }
                    strncpy(psdst, psval, len);
                    psdst[len] = '\0';
                }
            }
            else
            {
                NET_DEBUG("Unsupported type for setting collection member");
                return IPP_ATTRIBUTES;
            }
            SET_ATTR(pa);
            return IPP_OK;
        }
        n = n-2, pa = pa+2;
    }
    return IPP_ATTRIBUTES;
}

//**************************************************************************
int IPPATTRendCollection(PIPPCTX ppp, int *plevel, const char *name, int index)
{
    PIPP_ATTR pa;
    int level = *plevel;
    int rc;

    rc = IPP_OK;

    level--;  // pop
    NET_DEBUG("End collection %s at %d", name, level);

    if (level >= IPP_MAX_COLLECTION_DEPTH - 1 || level < 0)
    {
        return IPP_INTERNAL_ERROR;
    }
    if (level == 0)
    {
        if (ppp->colltree[level].type != 0)
        {
            NET_DEBUG("End top collection %s", name);
            pa = &ppp->colltree[level];
            rc = IPPATTRsetOperationAttribute(ppp, name, (void*)pa, index);
        }
    }
    *plevel = level;
    return rc;
}

IPP_ATTR_S* IPPATTRmediaStringToProperty(const char* medianame, PARSER_DATA_S* parser_data)
{
    RETURN_VAL_IF(medianame == NULL, NET_NONE, s_media_xref[0].pcol);

    for ( size_t i = 0; i < ARRAY_SIZE(s_media_xref); ++i )
    {
        BREAK_IF(s_media_xref[i].name == NULL, NET_NONE);

        if ( strcmp(medianame, s_media_xref[i].name) == 0 )
        {
            NET_INFO("match s_media_xref[%d]: %s|%d|%d|%d", i, s_media_xref[i].name, s_media_xref[i].propval, s_media_xref[i].w, s_media_xref[i].h);
            parser_data->paper_size = s_media_xref[i].propval;
            //pio->media_size  = s_media_xref[i].propval;
            //pio->x_dimension = s_media_xref[i].w;
            //pio->y_dimension = s_media_xref[i].h;
            return s_media_xref[i].pcol;
        }
    }

    NET_INFO("No media %s in xref table", medianame);
    parser_data->paper_size = s_media_xref[0].propval;
    //pio->media_size  = s_media_xref[0].propval;
    //pio->x_dimension = s_media_xref[0].w;
    //pio->y_dimension = s_media_xref[0].h;
    return s_media_xref[0].pcol;    // letter
}

static const char* s_media_source_name[] =
{
    "tray-1",               ///< TRAY_INPUT_STANDARD
    "tray-2",               ///< TRAY_INPUT_2
    "tray-3",               ///< TRAY_INPUT_3
    "tray-4",               ///< TRAY_INPUT_4
    "large-capacity",       ///< TRAY_INPUT_External_LCT_IN
    "alternate",            ///< TRAY_INPUT_External_LCT_OUT
    "by-pass-tray",         ///< TRAY_INPUT_MULTIFUNCTION
    NULL
};

static struct tag_media_type
{
    const char* type_name;
    int32_t     media_type;
}

/* 定义文档[PWG5100.3][PWG5101.1] */
s_media_type_table[] =
{
    {"auto",                    PAPER_TYPE_AUTO     },
    {"stationery",              PAPER_TYPE_ORDINARY },
    {"stationery-heavyweight",  PAPER_TYPE_THICK1   },
//    {"envelope",                PAPER_TYPE_ENVELOP  },
    {"cardstock",               PAPER_TYPE_CARD     },
    {"labels",                  PAPER_TYPE_LABEL    },
    {"transparency",            PAPER_TYPE_FILM     },
    {"stationery-lightweight",  PAPER_TYPE_THIN     }
};

int32_t IPPATTRmediaPropertyToParam(IPP_ATTR_S* pcol, PARSER_DATA_S* parser_data, int32_t mediacolIsSet)
{
    IPP_ATTR_S* pa = NULL;
    int32_t     value = 0;
    int32_t     i = 0;
    int32_t     x = 0;
    int32_t     y = 0;

    RETURN_VAL_IF(pcol == NULL, NET_WARN, -1);

    while ( pcol->varsize == 0 && pcol->var )
    {
        pcol = (IPP_ATTR_S *)pcol->var;
    }

    RETURN_VAL_IF(pcol == NULL || pcol->varsize == 0, NET_WARN, -1);

    i = pcol->varsize;
    pa = pcol;
    while ( i-- > 0 )
    {
        pa++;
        if ( (pa->var != NULL) && (strcmp((char *)(pa->var), "media-type") == 0) )
        {
            pa++;
            NET_DEBUG("set media-type(%s)", (char *)(pa->var));
            break;
        }
    }

    parser_data->paper_type = PAPER_TYPE_AUTO;
    for ( i = 0; i < ARRAY_SIZE(s_media_type_table); ++i )
    {
        if ( strcasecmp(pa->var, s_media_type_table[i].type_name) == 0 )
        {
            parser_data->paper_type = s_media_type_table[i].media_type;
        }
    }
    NET_DEBUG("media_type(%s->%d)", (char *)(pa->var), (int32_t)parser_data->paper_type);

    i = pcol->varsize;
    pa = pcol;
    while ( i-- > 0 )
    {
        pa++;
        if ( (pa->var != NULL) && (strcmp(pa->var, "media-source") == 0) )
        {
            //解BUG#16931，数据的封装格式有所变化，数据的解析也要做相对应的调整，change by liangzhiqiong 2018-10-8
            pa++;
            NET_DEBUG("set media-source(%s)", (char *)(pa->var));
            break;
        }
    }

    parser_data->media_source = TRAY_INPUT_AUTO;
    for ( i = 0; i < ARRAY_SIZE(s_media_source_name) && s_media_source_name[i] != NULL; ++i )
    {
        if ( strcmp(pa->var, s_media_source_name[i]) == 0 )
        {
            parser_data->media_source = i;
        }
    }
    NET_DEBUG("media_source(%s->%d)", (char *)(pa->var), (int32_t)parser_data->media_source);

#if 0
    int32_t     find = 0;
    //find media-bottom-margin
    i = pcol->varsize;
    pa = pcol;

    pio->media_bottom_margin    = s_ipp_base_margin / 25.4 * 600 / 100;
    pio->media_left_margin      = s_ipp_base_margin / 25.4 * 600 / 100;
    pio->media_right_margin     = s_ipp_base_margin / 25.4 * 600 / 100;
    pio->media_top_margin       = s_ipp_base_margin / 25.4 * 600 / 100;

    while (i-- > 0 && find < 4 && mediacolIsSet)
    {
        pa++;
        if ((NULL != pa->var) && (! strcmp(pa->var, "media-bottom-margin")))
        {
            pa++;
            find++;
            pio->media_bottom_margin = *(int *)pa->var / 25.4 * 600 / 100;
            NET_INFO("set media-bottom-margin(%d):=[%d pixel]",*(int *)pa->var, pio->media_bottom_margin);

            continue;
        }
        else if ((NULL != pa->var) && (! strcmp(pa->var, "media-left-margin")))
        {
            pa++;
            find++;
            pio->media_left_margin = *(int *)pa->var / 25.4 * 600 / 100;
            NET_INFO("set media-left-margin(%d):=[%d pixel]",*(int *)pa->var, pio->media_left_margin);
            continue;
        }
        else if ((NULL != pa->var) && (! strcmp(pa->var, "media-right-margin")))
        {
            pa++;
            find++;
            pio->media_right_margin = *(int *)pa->var / 25.4 * 600 / 100;
            NET_INFO("set media-right-margin(%d):=[%d pixel]",*(int *)pa->var, pio->media_right_margin);
            continue;
        }
        else if ((NULL != pa->var) && (! strcmp(pa->var, "media-top-margin")))
        {
            pa++;
            find++;
            pio->media_top_margin = *(int *)pa->var / 25.4 * 600 / 100;
            NET_INFO("set media-top-margin(%d):=[%d pixel]",*(int *)pa->var, pio->media_top_margin);
            continue;
        }
    }
#endif

    i = pcol->varsize;
    pa = pcol;
    while ( i-- > 0 )
    {
        pa++;
        if ( (pa->var != NULL) && (strcmp(pa->var, "media-size") == 0) )
        {
            break;
        }
    }

    if ( mediacolIsSet && (strcmp(pa->var, "media-size") == 0) )
    {
        while ( pa->varsize == 0 && pa->var )
        {
            pa = (IPP_ATTR_S *)pa->var;
        }
        if ( pa == NULL || pa->varsize == 0 )
        {
            return -1;
        }

        //解BUG#16935，数据的封装格式有所变化，数据的解析也要做相对应的调整，change by liangzhiqiong 2018-10-8
        if ( pa->type != IPP_TAG_MEMBERNAME )
        {
            return -1;
        }

        i = pa->varsize;
        while (i-- > 0)
        {
            pa++;
            //解BUG#16935，数据的封装格式有所变化，数据的解析也要做相对应的调整，change by liangzhiqiong 2018-10-8
            if ( pa->type == IPP_TAG_MEMBERNAME )
            {
                if ( strcmp(pa->var, "x-dimension") == 0 )
                {
                    pa++;
                    value = *(int*)pa->var;
                    NET_INFO("x-dimension=%d.",value);
                    x = (*(int*)pa->var + 5) / 10;
                    //pio->x_dimension = x;
                }
                else if ( strcmp(pa->var, "y-dimension") == 0 )
                {
                    pa++;
                    value = *(int*)pa->var;
                    NET_INFO("y-dimension=%d.",value);
                    y = (*(int*)pa->var + 5) / 10;
                    //pio->y_dimension = y;
                }
            }
        }
    }

    if ( mediacolIsSet == 0 )
    {
        NET_DEBUG("No match media-col from client, using PAPER_SIZE_AUTO");
        parser_data->paper_size = PAPER_SIZE_AUTO;
        //parser_data->paper_size =PAPER_SIZE_A4;
        return -1;
    }

    for ( i = 0; i < ARRAY_SIZE(s_media_xref); ++i )
    {
        BREAK_IF(s_media_xref[i].name == NULL, NET_NONE);

        if ( ((x+5) > s_media_xref[i].w && (x-5) < s_media_xref[i].w) && ((y+5) > s_media_xref[i].h && (y-5) < s_media_xref[i].h) )
        {
            NET_INFO("media_size: [w<%d> h<%d> -> %d]", x, y, s_media_xref[i].propval);
            parser_data->paper_size = s_media_xref[i].propval;
            return 0;
        }
    }

    NET_WARN("No match media %d x %d in xref table", x, y);
    if( ((x + 5) > PAPERSIZE_4x6_WIDTH && (x - 5) < PAPERSIZE_4x6_WIDTH) && ((y + 5) > PAPERSIZE_4x6_HEIGHT && (y - 5) < PAPERSIZE_4x6_HEIGHT))
    {
        NET_WARN("media %d x %d is 4x6inch, set to letter", x, y);
    }
    else
    {
        parser_data->paper_size = PAPER_SIZE_AUTO;
    }
    return -1;
}

/****************************************************************
 Description  : set media source to collection
 Input    Parm: void
 Return   Parm: none
 call by:
 Create Author: wuiygeng ,2019/05/22
***************************************************************************/
static int IPPSetMediaSource(int tray_index, PIPP_ATTR ptable, char *media_source)
{
    PIPP_ATTR pa;
    pa = ptable;
    pa++;

    for ( ; pa->var; pa++ )
    {
        if (! strcmp(pa->var, (void *)"media-source"))
        {
            pa++;
            strcpy(pa->var, media_source);
            break;
        }
        else
        {
            ;
        }

    }
    return 0;
}
/**
* @brief 设置支持纸型 字段media-supported
*
* @param void
* @return 执行结果
* @return <ul><li>0:执行成功</li><li>-1:执行失败</li></ul>
*/
static int IPPATTRbuildSupportedMedia(void)
{
    int i, j, k;

    // setup media size supported from cross reference
    //
    for (i = j = 0; i < sizeof(s_media_xref) / sizeof(struct tag_media_xref);)
    {
        if (! s_media_xref[i].name)
        {
            break;
        }

        //media-supported只设置print 2016.5.10 zw
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "media-supported", (void*)s_media_xref[i].name, j++);

        // skip all subsequent media with same code, as these are aliases
        //
        for (k = i; i < sizeof(s_media_xref) / sizeof(struct tag_media_xref); i++)
        {
            if (! s_media_xref[i].name)
            {
                break;
            }
            if (s_media_xref[i].propval != s_media_xref[k].propval)
            {
                break;
            }
        }
    }
    return 0;
}

static int32_t IPPATTRbuildOutputTrays(void)
{
    char traystr[512];

    snprintf(traystr, sizeof(traystr), "Ttype=removableBin;maxcapacity=500;remaining=-3;status=5;name=top;stackingorder=firstToLast;pagedelivery=faceDown;");
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-output-tray", traystr, 0);

    return 0;
}
/**
 * @brief   更新media-info属性相关信息
 *
 * @param   void
 *
 * @return  处理结果
 * @return  <ul><li>0:成功</li><li>-1:失败</li></ul>
 */
static int32_t ippattr_update_media_info(void)
{
    TRAY_INPUT_E    tray_index;
    char            tray_info[256];
    char            tray_name[64];
    const char*     media_type;
    int32_t         media_index;
    uint32_t        marker_levels = 0;
    int32_t         status;
    int32_t         index = 0;

    //初始化设定"media-source-supported"属性
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "media-source-supported", "auto", 0);

    //初始化设定"printer-input-tray"属性
    snprintf(tray_info, sizeof(tray_info), "Ttype=%s;mediafeed=%d;mediaxfeed=%d;status=%d;level=%d;maxcapacity=%d;name=auto;", "other", 0, 0, 0, 0, 250);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-input-tray", tray_info, 0);

    //循环获取当前装载的tray的相关信息
    //并设定"media-ready"、"media-col-ready"、"media-source-supported"、"printer-input-tray"
    for ( tray_index = TRAY_INPUT_STANDARD ; tray_index <= TRAY_INPUT_MULTIFUNCTION ; ++tray_index )
    {
        if ( netdata_get_tray_install(DATA_MGR_OF(s_ippattr_ctx), tray_index) == 0 )
        {
            NET_TRACE("uninstalled tray[%d]", tray_index);
            continue;
        }

        media_index = get_media_index_by_tray(tray_index);
        if ( media_index < 0 )
        {
            NET_DEBUG("tray(%d) media_index(%d)", tray_index, media_index);
            continue;
        }

        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "media-ready", (void*)s_media_xref[media_index].name, index);

        media_type = get_media_type_by_tray(tray_index);
        if ( media_type != NULL )
        {
            s_media_xref[media_index].pcol[2].var = (void *)media_type;
        }

        status = ( (netdata_get_tray_status(DATA_MGR_OF(s_ippattr_ctx), tray_index) == 1) ? 0 : 11 );
        snprintf(tray_name, sizeof(tray_name), "%s", s_media_source_name[tray_index]);
        if ( tray_index == TRAY_INPUT_MULTIFUNCTION )
        {
            snprintf(tray_info, sizeof(tray_info), "Ttype=sheetFeedAutoNonRemovableTray;mediafeed=%d;mediaxfeed=%d;maxcapacity=%d;level=%d;status=%d;name=%s;",
                    0, 0, 1, 0, status, tray_name);
        }
        else if ( tray_index == TRAY_INPUT_STANDARD )
        {
            snprintf(tray_info, sizeof(tray_info), "Ttype=sheetFeedAutoRemovableTray;mediafeed=%d;mediaxfeed=%d;maxcapacity=%d;level=%d;status=%d;name=%s;",
                    0, 0, 250, 0, status, tray_name);
        }
        else
        {
            snprintf(tray_info, sizeof(tray_info), "Ttype=other;mediafeed=%d;mediaxfeed=%d;maxcapacity=%d;level=%d;status=%d;name=%s;",
                    0, 0, 250, 0, status, tray_name);
        }
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "media-source-supported", tray_name, index + 1);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-input-tray", tray_info, index + 1);
        IPPSetMediaSource(tray_index, s_media_xref[media_index].pcol, tray_name);

        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "media-col-ready", (void*)s_media_xref[media_index].pcol, index);
        IPPSetMediaSource(tray_index, s_media_xref[media_index].pcol, "auto"); /*fix bug#25155 wyg 19-07-31 fix bug#105991  24-05-24 */
        index++;
    }

    //设定"sides-supported"属性
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "sides-supported", (void*)"one-sided", 0);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "sides-supported", (void*)"two-sided-short-edge", 1);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "sides-supported", (void*)"two-sided-long-edge", 2);

#if CONFIG_COLOR
    //获取青色碳粉剩余量并更新到IPP "marker-levels"
    marker_levels = netdata_get_tb_remain(DATA_MGR_OF(s_ippattr_ctx), MARKER_ID_C);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "marker-levels", (void*)&marker_levels, 0);

    //获取红色碳粉剩余量并更新到IPP "marker-levels"
    marker_levels = netdata_get_tb_remain(DATA_MGR_OF(s_ippattr_ctx), MARKER_ID_M);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "marker-levels", (void*)&marker_levels, 1);

    //获取黄色碳粉剩余量并更新到IPP "marker-levels"
    marker_levels = netdata_get_tb_remain(DATA_MGR_OF(s_ippattr_ctx), MARKER_ID_Y);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "marker-levels", (void*)&marker_levels, 2);

    //获取黑色色碳粉剩余量并更新到IPP "marker-levels"
    marker_levels = netdata_get_tb_remain(DATA_MGR_OF(s_ippattr_ctx), MARKER_ID_K);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "marker-levels", (void*)&marker_levels, 3);
#else
    //获取黑色色碳粉剩余量并更新到IPP "marker-levels"
    marker_levels = netdata_get_tb_remain(DATA_MGR_OF(s_ippattr_ctx), MARKER_ID_K);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "marker-levels", (void*)&marker_levels, 0);
#endif
    return 0;
}

static int32_t ippattr_update_printer_state(void)
{
    NETSTS_PACKET_S netsts_packet = { .count = 0 };
    uint32_t        system_status = 0;
    uint32_t        netsts_status = 0;
    int32_t         printer_error = 0;
    int32_t         report_num = 0;

    if ( job_manager_has_running_job() )
    {
        s_ipp_printerstate = IPP_PRINTER_PROCESSING;
    }
    else
    {
        s_ipp_printerstate = IPP_PRINTER_IDLE;
    }

    netsts_take_packet(&netsts_packet);
    /* 粉盒粉量低 */
    if ( netsts_check_toner_low(&netsts_packet) )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "toner-low-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "warning", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
    }

    /* 粉盒空 */
    netsts_status = netsts_check_toner_empty(&netsts_packet);
    if ( netsts_status )
    {
        if ( netsts_status < STATUS_E_PRINT_Y_TB_UNINSTALL || netsts_status > STATUS_E_PRINT_K_TB_UNINSTALL )
        {
            snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "toner-empty-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "warning" : "error", 0);
        }
        else
        {
            snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "toner-empty-%s%c", "error", 0);
        }
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    /* 开盖 */
    if ( netsts_check_cover_open(&netsts_packet) )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "door-open%c", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    /* 进纸盒缺失 */
    if ( netsts_check_trayin_missing(&netsts_packet) )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "input-tray-missing%c", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    /* 出纸口满 */
    if ( netsts_check_trayout_full(&netsts_packet) )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "output-area-full-error%c", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    /* 卡纸 */
    if ( netsts_check_paper_jam(&netsts_packet) )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "media-jam-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "error", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    if ( netsts_check_trayin_near_empty(&netsts_packet) ) /* 进纸盒少纸 */
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "media-low-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "warning", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    /* 缺纸 */
    system_status = netsts_check_paper_empty(&netsts_packet);
    NET_INFO("system_status: '%u'", system_status);
    if ( system_status == STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "media-needed-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "error", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }
    else if ( (system_status & STATUS_ID_TYPE_MASK) == STATUS_ID_TYPE_ERROR )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "media-empty-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "error", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        if (s_ipp_printerstate != IPP_PRINTER_IDLE) {
            snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "media-needed-error%c", 0);
            ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
            NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        }
        printer_error = 1;
    }
    else if ( (system_status & STATUS_ID_TYPE_MASK) == STATUS_ID_TYPE_WARN )
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "media-empty-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "error", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    system_status = netsts_check_waste_toner(&netsts_packet);
    if ( system_status == STATUS_W_PRINT_WASTE_TONER_NEAR_FULL ) /* 废粉瓶将满 */
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "marker-waste-almost-full-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "warning", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
    }
    else if ( system_status == STATUS_E_PRINT_W_TB_UNINSTALL ) /* 废粉瓶缺失 */
    {
        pi_snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "other-error%c", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }
    else if ( (system_status & STATUS_ID_TYPE_MASK) == STATUS_ID_TYPE_ERROR ) /* 废粉瓶满 */
    {
        snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "marker-waste-full-error%c", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
        NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
        printer_error = 1;
    }

    if ( report_num == 0 )
    {
        netsts_status = netsts_get_top_type(&netsts_packet);
        if ( (netsts_status >= STATUS_I_NET_WIFI_STA_CONNECTING && netsts_status <= STATUS_I_NET_WIFI_WFD_RADAR_FREQ)
              || (netsts_status >= STATUS_E_NET_WIFI_CONNECT_TIMEOUT && netsts_status <= STATUS_E_NET_WIFI_INIT_ERROR)
              || netsts_status == STATUS_F_NET_WIFI_FATAL_ERROR)
        {
            netsts_status = STATUS_ID_TYPE_INFO;
        }

        switch ( netsts_status & 0xF0000000 )
        {
            case STATUS_ID_TYPE_FATAL:
                printer_error = 1;
                pi_snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "other-error%c", 0);
                ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
                NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
                break;
            case STATUS_ID_TYPE_ERROR:
            case STATUS_ID_TYPE_WARN:
                printer_error = 1;
                pi_snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons), "other-%s%c", s_ipp_printerstate == IPP_PRINTER_IDLE ? "report" : "error", 0);
                ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, report_num++);
                NET_INFO("printer-state-reasons.%d: '%s'", report_num, s_ipp_printerstate_reasons);
                break;
            default:
                pi_snprintf(s_ipp_printerstate_reasons, sizeof(s_ipp_printerstate_reasons),"none%c", 0);
                ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-state-reasons", s_ipp_printerstate_reasons, 0);
                break;
        }
    }

    if ( printer_error == 1 && s_ipp_printerstate == IPP_PRINTER_PROCESSING )
    {
        s_ipp_printerstate = IPP_PRINTER_STOPPED;
    }
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-state", &s_ipp_printerstate, 0);
    NET_INFO("s_ipp_printerstate(%d) report_num(%d)", s_ipp_printerstate, report_num);

    return 0;
}

static int32_t ippattr_update_printer_time(void)
{
    struct tm   now_time;
    time_t      now;
    char        str[32];
    char        t[16];
    int32_t     rc = 0;

    pi_time(&now);
    gmtime_r(&now, &now_time);

    /* Format time into buffer like IPP likes (SMIv1 format)
       0      1-2   year*                     0..65536  (network byte order!)
       2       3    month                     1..12
       3       4    day                       1..31
       4       5    hour                      0..23
       5       6    minutes                   0..59
       6       7    seconds                   0..60
       (use 60 for leap-second)
       7       8    deci-seconds              0..9
       8       9    direction from UTC        '+' / '-'
       9      10    hours from UTC*           0..13
       10     11    minutes from UTC          0..59
       */
    memset(t, 0, sizeof(t));
    t[0] = now_time.tm_year % 10;
    t[1] = now_time.tm_year / 10 % 10;
    t[2] = now_time.tm_mon + 1;
    t[3] = now_time.tm_mday;
    t[4] = now_time.tm_hour;
    t[5] = now_time.tm_min;
    t[6] = now_time.tm_sec;
    t[7] = 0;
    t[8] = '+';

    /* Encode buffer as hex-bcd format octet-strings */
    snprintf(str, sizeof(str), "%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X", t[0], t[1], t[2], t[3], t[4], t[5], t[6], t[7], t[8], t[9], t[10]);

    rc |= ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-current-time", str, 0);
    rc |= ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-up-time",     &now, 0);

    return rc;
}

int32_t IPPATTRupdatePrinterAttributes(void)
{
    char    geo_location[IPP_MAX_URI] = {0};
    char    location[256] = {0};
    int32_t rc = 0;

    rc |= ippattr_update_media_info();
    rc |= ippattr_update_printer_state();
    rc |= ippattr_update_printer_time();

    //若"printer-location"属性有变更则更新"printer-location"和"printer-geo-location"
    netdata_get_location(DATA_MGR_OF(s_ippattr_ctx), location, sizeof(location));
    if ( strcmp(location, s_printer_location) != 0 )
    {
        snprintf(s_printer_location, sizeof(s_printer_location), "%s", location);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-location", s_printer_location, 0);
    }

    snprintf(geo_location, sizeof(geo_location), "geo://%s", location);
    if ( strcmp(geo_location, s_printer_geo_location) != 0 )
    {
        snprintf(s_printer_geo_location, sizeof(s_printer_geo_location), "%s", geo_location);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-geo-location", s_printer_geo_location, 0);
    }

    IPPATTRbuildOutputTrays();

    return rc;
}

/**
* @brief    设置固件版本号
*
* @param    void
* @return   void
*/
static void ippattr_set_firmware_version(void)
{
    char fw_ver[16] = {0};

    netdata_get_fw_ver(DATA_MGR_OF(s_ippattr_ctx), fw_ver, sizeof(fw_ver));

    snprintf(s_printer_fw_str_version, sizeof(s_printer_fw_str_version), "2.0\4""1.02\4""1.5\4""%s", fw_ver);
    NET_DEBUG("s_printer_fw_str_version(%s)", s_printer_fw_str_version);

    snprintf(s_printer_fw_version, sizeof(s_printer_fw_version), "00020000\4""00010002\4""00010500\4""0%c0%c0%c0%c", fw_ver[0], fw_ver[2], fw_ver[4], fw_ver[6]);
    NET_DEBUG("s_printer_fw_version(%s)", s_printer_fw_version);
}

/**
* @brief    设置支持查找动作
*
* @param    void
* @return   void
*/
static void ippattr_set_identify_actions(void)
{
    char* pstr = s_identify_actions_supported;

    pstr += snprintf(pstr, sizeof(s_identify_actions_supported) + s_identify_actions_supported - pstr, "flash");
    pstr += snprintf(pstr, sizeof(s_identify_actions_supported) + s_identify_actions_supported - pstr, "\4display");
    pstr += snprintf(pstr, sizeof(s_identify_actions_supported) + s_identify_actions_supported - pstr, "\4sound");
    NET_DEBUG("s_identify_actions_supported(%s)", s_identify_actions_supported);

    snprintf(s_identify_actions_default, sizeof(s_identify_actions_default), "flash");
    NET_DEBUG("s_identify_actions_default(%s)", s_identify_actions_default);
}

/**
* @brief    设置支持的打印语言
*
* @param    void
* @return   void
*/
static void ippattr_set_multidoc_handing(void)
{
    char*       pstr = s_multidoc_handling_supported;
    const char* tempstr;

    tempstr = "separate-documents-collated-copies";
    pstr += snprintf(pstr, sizeof(s_multidoc_handling_supported) + s_multidoc_handling_supported - pstr, "%s", tempstr);
    tempstr = "\4separate-documents-uncollated-copies";
    pstr += snprintf(pstr, sizeof(s_multidoc_handling_supported) + s_multidoc_handling_supported - pstr, "%s", tempstr);
    NET_DEBUG("s_multidoc_handling_supported(%s)", s_multidoc_handling_supported);
    NET_DEBUG("s_multidoc_handling_default(%s)", s_multidoc_handling_default);
}

/**
* @brief    设置支持的类型
*
* @param    void
* @return   void
*/
static void ippattr_set_document_format(void)
{
    char*       pstr = s_ipp_docformat_supported;
    const char* tempstr;

    tempstr = "application/octet-stream";
    pstr += snprintf(pstr, sizeof(s_ipp_docformat_supported) + s_ipp_docformat_supported - pstr, "%s", tempstr);

#if CONFIG_JPEG
    pstr += snprintf(pstr, sizeof(s_ipp_docformat_supported) + s_ipp_docformat_supported - pstr, "\4image/jpeg");
#endif

#if CONFIG_PWG
    tempstr = "image/pwg-raster";
    pstr += snprintf(pstr, sizeof(s_ipp_docformat_supported) + s_ipp_docformat_supported - pstr, "\4%s", tempstr);
#endif

#if CONFIG_URF
    tempstr = "image/urf";
    pstr += snprintf(pstr, sizeof(s_ipp_docformat_supported) + s_ipp_docformat_supported - pstr, "\4%s", tempstr);
#endif

#if CONFIG_PDF
    tempstr = "application/pdf";
    pstr += snprintf(pstr, sizeof(s_ipp_docformat_supported) + s_ipp_docformat_supported - pstr, "\4%s", tempstr);
#endif
    NET_DEBUG("s_ipp_docformat_supported(%s)", s_ipp_docformat_supported);

    snprintf(s_ipp_docformat_default, sizeof(s_ipp_docformat_default), "%s", tempstr);
    NET_DEBUG("s_ipp_docformat_default(%s)", s_ipp_docformat_default);
}

/**
* @brief    设置支持pdf版本
*
* @param    void
* @return   void
*/
static void ippattr_set_pdf_version(void)
{
    if ( netdata_get_print_language(DATA_MGR_OF(s_ippattr_ctx)) == PRINT_LANGUAGE_IPS )
    {
        snprintf(s_pdf_versions_supported, sizeof(s_pdf_versions_supported), "iso-32000-1_2008");
    }
    else
    {
        snprintf(s_pdf_versions_supported, sizeof(s_pdf_versions_supported), "none");
    }
    NET_DEBUG("s_pdf_versions_supported(%s)", s_pdf_versions_supported);
}

/**
* @brief ippattr初始化函数
*
* @param void
* @return 执行结果
* @return <ul><li>0:执行成功</li><li>-1:执行失败</li></ul>
*/
int32_t ippattr_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_ippattr_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_ippattr_ctx = (IPP_ATTR_CTX_S *)pi_zalloc(sizeof(IPP_ATTR_CTX_S));
    RETURN_VAL_IF(s_ippattr_ctx == NULL, NET_WARN, -1);

    do
    {
        s_ippattr_ctx->net_ctx = net_ctx;

        s_ippattr_ctx->mutex = pi_mutex_create();
        BREAK_IF(s_ippattr_ctx->mutex == INVALIDMTX, NET_WARN);

        s_ipp_uptime = pi_time(NULL);

        //netdata_get_ipp_finisher_info(DATA_MGR_OF(s_ippattr_ctx), s_finishings_supported, sizeof(s_finishings_supported));//设定装订属性
        for ( int i = 0; i < sizeof(s_finishings_supported)/sizeof(int); ++i )
        {
            s_finishings_supported[i] = 3;
        }
        ippattr_set_firmware_version();             //设置打印机固件版本号
        ippattr_set_identify_actions();             //设定"identify-actions-supported"属性
        ippattr_set_multidoc_handing();             //设定"multiple-document-handling-supported"属性
        ippattr_set_document_format();              //设定"document-format-supported"属性
        ippattr_set_pdf_version();                  //设定"pdf-versions-supported"属性

        memset(&s_ipp_printer, 0, sizeof(s_ipp_printer));
        s_ipp_printer.type = IPPSERVICE_PRINT;
        s_ipp_printer.name = "Printer";
        BREAK_IF(IPPATTRcreatePrinterAttributeSet(&s_ipp_printer.printer_attributes) != 0, NET_WARN);

        IPPATTRbuildSupportedMedia();
        ret = 0;
    }
    while ( 0 );

    if ( ret != 0 )
    {
        ippattr_epilog();
    }
    return 0;
}

void ippattr_epilog(void)
{
    if ( s_ippattr_ctx != NULL )
    {
        if ( s_ippattr_ctx->mutex != INVALIDMTX )
        {
            IPPATTRdestroyPrinterAttributeSet(s_ipp_printer.printer_attributes);
            pi_mutex_destroy(s_ippattr_ctx->mutex);
        }
        pi_free(s_ippattr_ctx);
        s_ippattr_ctx = NULL;
    }
}

