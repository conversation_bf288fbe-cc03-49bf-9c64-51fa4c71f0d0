/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_define.h
 * @addtogroup misc_macro_definition
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal misc interface set
 */
#ifndef __POL_DEFINE_H__
#define __POL_DEFINE_H__

#ifdef __cplusplus
#define PT_BEGIN_DECLS      extern "C" {
#define PT_END_DECLS        }
#else
#define PT_BEGIN_DECLS
#define PT_END_DECLS
#endif

#define MAX(a,b)            (((a) > (b))? (a):(b))
#define MIN(a,b)            (((a) < (b))? (a):(b))

#define HAS_NO_FILE(x)      (access(x, F_OK) != 0)
#define HAS_FILE(x)         (access(x, F_OK) == 0)

#define STRING_IS_EMPTY(s)  ((s) == NULL || (s)[0] == '\0')
#define STRING_NO_EMPTY(s)  ((s) != NULL && (s)[0] != '\0')

#ifndef ARRAY_SIZE
#define ARRAY_SIZE(a)       (sizeof(a) / sizeof((a)[0]))
#endif

#define FILE_PATH_MAX 256

#ifndef TRUE
#define TRUE  1
#endif

#ifndef FALSE
#define FALSE 0
#endif

#ifndef NULL
#define NULL (void*)0
#endif

#ifndef RET_SUCCESS
#define RET_SUCCESS  0
#endif

#ifndef RET_FAIL
#define RET_FAIL -1
#endif


#ifndef likely
#define likely(x) __builtin_expect(!!(x),1)
#endif

#ifndef unlikely
#define unlikely(x) __builtin_expect(!!(x),0)
#endif



#ifdef CONFIG_XXX_LOG
#else
#define pi_err(fmt, ...)        printf("[POL-ERR][%s]"fmt, __func__, ##__VA_ARGS__)
#define pi_none(...)            NULL
#endif

#define RETURN_VAL_SHOW_CALLER_IF(_cond, _log, _caller_, _val)  if (_cond) { _log("[CALL:%s] condition ("#_cond"), return %d.\n", _caller_, (int32_t)_val); return _val; }
#define RETURN_VAL_IF(_cond, _log, _val)                        if (_cond) { _log("[LINE:%d] condition ("#_cond"), return %d.\n", __LINE__, (int32_t)_val); return _val; }
#define RETURN_SHOW_CALLER_IF(_cond, _log, _caller_)            if (_cond) { _log("[CALL:%s] condition ("#_cond"), return.\n", _caller_); return; }
#define RETURN_IF(_cond, _log)                                  if (_cond) { _log("[LINE:%d] condition ("#_cond"), return.\n", __LINE__); return; }
#define BREAK_IF(_cond, _log)                                   if (_cond) { _log("[LINE:%d] condition ("#_cond"), break. \n", __LINE__); break;  }

#ifdef  CONFIG_ENABLE_BACKTRACE_STATIC_FUNC
#define Q_STATIC
#else
#define Q_STATIC    static
#endif

#endif
/**
 *@}
 */
