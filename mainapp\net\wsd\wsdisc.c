/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wsdisc.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WEB Service Discovery
 */
#include "nettypes.h"
#include "netport.h"
#include "netmisc.h"
#include "netsock.h"
#include "wsdef.h"
#include "qxml.h"
#include "soap.h"
#include "wsd.h"
#include <sys/eventfd.h>

#define WSDISC_HELLO                                                                                                    \
"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n"                                                                        \
"<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"\r\n"                                             \
"    xmlns:wsa=\"http://schemas.xmlsoap.org/ws/2004/08/addressing\"\r\n"                                                \
"    xmlns:wsd=\"http://schemas.xmlsoap.org/ws/2005/04/discovery\"\r\n"                                                 \
"    xmlns:wsdp=\"http://schemas.xmlsoap.org/ws/2006/02/devprof\"\r\n"                                                  \
"    xmlns:wprt=\"http://schemas.microsoft.com/windows/2006/08/wdp/print\"\r\n"                                         \
"    xmlns:wscn=\"http://schemas.microsoft.com/windows/2006/08/wdp/scan\"\r\n"                                          \
">\r\n"                                                                                                                 \
"<soap:Header>\r\n"                                                                                                     \
    "<wsa:MessageID>urn:uuid:%s</wsa:MessageID>\r\n" /* message ID */                                                   \
    "<wsa:Action>http://schemas.xmlsoap.org/ws/2005/04/discovery/Hello</wsa:Action>\r\n"                                \
    "<wsa:To>urn:schemas-xmlsoap-org:ws:2005:04:discovery</wsa:To>\r\n"                                                 \
    "<wsd:AppSequence InstanceId=\"%d\" MessageNumber=\"%d\"></wsd:AppSequence>\r\n"                                    \
"</soap:Header>\r\n"                                                                                                    \
"<soap:Body>\r\n"                                                                                                       \
"<wsd:Hello>\r\n"                                                                                                       \
"<wsa:EndpointReference>\r\n"                                                                                           \
    "<wsa:Address>urn:uuid:%s</wsa:Address>\r\n" /* UUID address */                                                     \
"</wsa:EndpointReference>\r\n"                                                                                          \
"<wsd:Types>" WSDISCO_DEVICE_TYPE "</wsd:Types>\r\n"                                                                    \
"<wsd:XAddrs>%s</wsd:XAddrs>\r\n" /* server url */                                                                      \
"<wsd:MetadataVersion>1</wsd:MetadataVersion>\r\n"                                                                      \
"</wsd:Hello>\r\n"                                                                                                      \
"</soap:Body>\r\n"                                                                                                      \
"</soap:Envelope>"

#define WSDISC_IPV4_MCAST_ADDR      "***************"
#define WSDISC_IPV6_MCAST_ADDR      "ff02::c"

#define WSDISC_BUFFER_SIZE          0x4000   /* 16 * 1024 */

typedef void (*UDP_HANDLER)(PI_SOCKET_T sockfd, char* buf, size_t bufsize, struct sockaddr_storage* from);

typedef struct  wsdiscovery_context
{
    WSD_CTX_S*  wsd_ctx;
    PI_THREAD_T srv_tid;
    PI_SOCKET_T sockfd[IPVER_NUM][IFACE_ID_NUM];
    int32_t     instance;
    int32_t     msg_num;
    uint8_t     inited;
    int         efd;
}
WSDISC_CTX_S;

static WSDISC_CTX_S* s_wsdisc_ctx = NULL;

static void wsdisc_response(PI_SOCKET_T sockfd, const char* probe_id, const char* match_type, char* buf, size_t bufsize, struct sockaddr_storage* from)
{
    char    hostname[HOSTNAME_LEN];
    char    message_id[64];
    char    srv_url[128];
    ssize_t ret;
    int     len;

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsdisc_ctx), hostname, sizeof(hostname));
    snprintf(srv_url, sizeof(srv_url), "http://%s:%u/%s", hostname, WSD_CONN_PORT, s_wsdisc_ctx->wsd_ctx->default_uuid);

    wsd_generate_uuid(message_id, sizeof(message_id));
    s_wsdisc_ctx->msg_num++;

    len = snprintf( buf, bufsize, MATCH_SOAP_FORMAT, match_type, message_id, probe_id, s_wsdisc_ctx->instance, s_wsdisc_ctx->msg_num,
            match_type, match_type, s_wsdisc_ctx->wsd_ctx->default_uuid, srv_url, match_type, match_type);

    ret = sendto(sockfd, buf, len, MSG_NOSIGNAL, (struct sockaddr *)from, (socklen_t)sizeof(*from));
    NET_DEBUG("wsdisc_response send back %d/%d bytes", len, ret);

    return;
}

static int wsdisc_hello(int dummy_sockfd, IP_VERSION_E ipver, const char* mcast_addr)
{
    char    buf[WSDISC_BUFFER_SIZE];
    char    hostname[HOSTNAME_LEN];
    char    message_id[64];
    char    srv_url[128];
    ssize_t ret;
    int     len;
    int     sockfd = -1;
    union {
        struct sockaddr_in addr4;
        struct sockaddr_in6 addr6;
    } addr;

    memset(&addr, 0, sizeof(addr));
    wsd_generate_uuid(message_id, sizeof(message_id));
    s_wsdisc_ctx->msg_num++;

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsdisc_ctx), hostname, sizeof(hostname));
    snprintf(srv_url, sizeof(srv_url), "http://%s:%u/%s", hostname, WSD_CONN_PORT, s_wsdisc_ctx->wsd_ctx->default_uuid);

    wsd_generate_uuid(message_id, sizeof(message_id));
    s_wsdisc_ctx->msg_num++;

    len = snprintf( buf, sizeof(buf), WSDISC_HELLO, message_id, s_wsdisc_ctx->instance,
                    s_wsdisc_ctx->msg_num, s_wsdisc_ctx->wsd_ctx->default_uuid, srv_url);

    if (ipver == IPV4)
    {
        inet_pton(AF_INET, mcast_addr, &addr.addr4.sin_addr.s_addr);
        addr.addr4.sin_family = AF_INET;
        addr.addr4.sin_port = htons(WSD_DISCO_PORT);
        sockfd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    }
    else
    {
        addr.addr6.sin6_family = AF_INET6;
        inet_pton(AF_INET6, mcast_addr, addr.addr6.sin6_addr.s6_addr);
        addr.addr6.sin6_port = htons(WSD_DISCO_PORT);
        sockfd = socket(AF_INET6, SOCK_DGRAM, IPPROTO_UDP);
    }
    RETURN_VAL_IF(sockfd < 0, NET_WARN, -1);
    for (int i = 0; i < 6; i++)    // retry for total 6 seconds
    {
        ret = sendto(sockfd, buf, len, MSG_NOSIGNAL, (struct sockaddr *)&addr,
                (socklen_t)(ipver == IPV4 ? sizeof(struct sockaddr) : sizeof(addr.addr6)) );
        if ( ret < 0 )
        {
            NET_DEBUG("wsdisc hello fail to sendto %s, error: %s, retrying... ", mcast_addr, strerror(errno));
            pi_msleep(1000);
        }
        else
        {
            NET_DEBUG("wsdisc hello sendto %s successfully", mcast_addr);
            break;
        }
    }
    close(sockfd);

    return 0;
}

static void wsdisc_process_request(PI_SOCKET_T sockfd, char* buf, size_t bufsize, struct sockaddr_storage* from)
{
    QXML_S* pxml = NULL;
    QXML_S  xml_cursor;
    char    element[128] = {0};
    char    action[128] = {0};
    char    msg_id[64] = {0};
    char    scopes[256] = {0};
    char    types[256] = {0};
    char    address[64] = {0};
    int32_t parse_level;
    SOAP_CHILD_ELEM_S child_elems[2];

    RETURN_IF(s_wsdisc_ctx == NULL || s_wsdisc_ctx->inited == 0, NET_WARN);
    RETURN_IF((pxml = wsd_qxml_parser(buf)) == NULL, NET_WARN);

    do
    {
        SOAP_CHILD_ELEM_RESET( child_elems[0], "Action", action, sizeof(action), dtstring );
        SOAP_CHILD_ELEM_RESET( child_elems[1], "MessageID", msg_id, sizeof(msg_id), dtstring );
        BREAK_IF(soap_find_child_elements(pxml, "Envelope.Header", child_elems, 2) != 0, NET_WARN);

        BREAK_IF(soap_next_element_case_equal(pxml, "Body"), NET_WARN);

        for (char* pe = QXMLchildElement(pxml), parse_level = pxml->m_level ; (pe != NULL && pxml->m_level >= parse_level); )
        {
            if (QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Probe") == 0)
            {
                SOAP_CHILD_ELEM_RESET( child_elems[0], "Types", types, sizeof(types), dtstring );
                SOAP_CHILD_ELEM_RESET( child_elems[1], "Scopes", scopes, sizeof(scopes), dtstring );
                pe = soap_iterate_child_elements(pxml, child_elems, 2);
                if ( child_elems[0].flg_val_exist && (strstr(types, "Print") || strstr(types, "Device")) )
                {
                    wsdisc_response(sockfd, msg_id, "Probe", buf, bufsize, from);
                    break;
                }
            }
            else if (QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Resolve") == 0)
            {
                SOAP_CHILD_ELEM_RESET( child_elems[0], "Address", address, sizeof(address), dtstring );
                pe = soap_iterate_child_elements(pxml, child_elems, 1);
                if ( child_elems[0].flg_val_exist && strstr(address, s_wsdisc_ctx->wsd_ctx->default_uuid) )
                {
                    wsdisc_response(sockfd, msg_id, "Resolve", buf, bufsize, from);
                    break;
                }
            }
            else // may be hello or bye
            {
                // QXMLelementCopy(pxml, element, pe, sizeof(element));
                // NET_DEBUG("Disco Ignore Element '%s'\n", element);
                pe = QXMLnextElement(pxml);
            }
        }
    }
    while ( 0 );

    QXMLparserDelete(pxml);
    return;
}

static uint16_t wsdisc_get_port()
{
    return netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsdisc_ctx)) ? WSD_DISCO_PORT: 0;
}

static int32_t wsdisc_reload_socket(fd_set* readfds, uint16_t port, uint8_t changed)
{
    const char*  mcast_addr[IPVER_NUM] = { WSDISC_IPV4_MCAST_ADDR , WSDISC_IPV6_MCAST_ADDR };
    char         local_addr[IPV6_ADDR_LEN];
    int32_t      maxfd = 0;
    uint8_t      link_base = LINK_CHANGE_IPV4_BASE;
    int          link_changed;

    FD_ZERO(readfds);
    for ( IP_VERSION_E ipver = IPV4; ipver < IPVER_NUM; ++ipver )
    {
        for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
        {
            if ( STRING_IS_EMPTY(IFACE_NAME(ifid)) )
            {
                continue;
            }
#ifdef CONFIG_NET_WIFI
            if ( ipver == IPV6 && ifid == IFACE_ID_WFD )
            {
                continue;
            }
#endif
            /* 当前链路状态有变更，重载socket */
            if ( (link_base << ifid) & changed )
            {
                NET_DEBUG("'%s.%s' has changed, socket reload", IFACE_NAME(ifid), IPVER_NAME(ipver));
                if ( s_wsdisc_ctx->sockfd[ipver][ifid] != INVALID_SOCKET )
                {
                    netsock_leave_multicast_group(s_wsdisc_ctx->sockfd[ipver][ifid], ifid, ipver, mcast_addr[ipver]);
                    pi_closesock(s_wsdisc_ctx->sockfd[ipver][ifid]);
                    s_wsdisc_ctx->sockfd[ipver][ifid] = INVALID_SOCKET;
                }
                if ( port == 0 )
                {
                    continue;
                }

                if ( ipver == IPV6 )
                {
                    netdata_get_ipv6_link(DATA_MGR_OF_WSD(s_wsdisc_ctx), ifid, local_addr, sizeof(local_addr));
                }
                else
                {
                    netdata_get_ipv4_addr(DATA_MGR_OF_WSD(s_wsdisc_ctx), ifid, local_addr, sizeof(local_addr));
                }

                if ( STRING_IS_EMPTY(local_addr) || netdata_get_iface_running(DATA_MGR_OF_WSD(s_wsdisc_ctx), ifid) == 0 )
                {
                    NET_DEBUG("'%s.%s' is skipped", IFACE_NAME(ifid), IPVER_NAME(ipver));
                    continue;
                }

                NET_DEBUG("load '%s.%s' to WSDiscovery multicast group", IFACE_NAME(ifid), IPVER_NAME(ipver));
                s_wsdisc_ctx->sockfd[ipver][ifid] = netsock_create_multicast(WSD_DISCO_PORT, ifid, ipver);
                if ( s_wsdisc_ctx->sockfd[ipver][ifid] == INVALID_SOCKET )
                {
                    NET_WARN("create multicast(%s.%s) socket failed: %d<%s>", IFACE_NAME(ifid), IPVER_NAME(ipver), errno, strerror(errno));
                    continue;
                }

                if ( netsock_join_multicast_group(s_wsdisc_ctx->sockfd[ipver][ifid], ifid, ipver, local_addr, mcast_addr[ipver]) != 0 )
                {
                    pi_closesock(s_wsdisc_ctx->sockfd[ipver][ifid]);
                    s_wsdisc_ctx->sockfd[ipver][ifid] = INVALID_SOCKET;
                    continue;
                }
                wsdisc_hello(s_wsdisc_ctx->sockfd[ipver][ifid], ipver, mcast_addr[ipver]);
                s_wsdisc_ctx->instance = (int32_t)pi_time(NULL);
            }

            if ( s_wsdisc_ctx->sockfd[ipver][ifid] != INVALID_SOCKET )
            {
                FD_SET(s_wsdisc_ctx->sockfd[ipver][ifid], readfds);
                if ( maxfd < s_wsdisc_ctx->sockfd[ipver][ifid] )
                {
                    maxfd = s_wsdisc_ctx->sockfd[ipver][ifid];
                }
            }
        }
        link_base = (link_base << IFACE_ID_NUM);
    }

    return maxfd;
}

void wsd_udp_loop(int notify_efd, WSD_GET_PORT_FUNC get_port_func, UDP_HANDLER handler)
{
    IP_VERSION_E            ipver;
    IFACE_ID_E              ifid;
    fd_set                  rfds;
    socklen_t               slen;
    struct sockaddr_storage from;
    struct timeval          tv;
    char                    buffer[WSDISC_BUFFER_SIZE];
    int32_t                 maxfd;
    ssize_t                 rlen;
    int32_t                 ret;
    uint8_t                 changed = LINK_CHANGE_ALL;
    uint64_t                efd_val;
    uint16_t                port;

    port = get_port_func();
    while ( 1 )
    {
        if (changed)
        {
            port = get_port_func();
        }
        maxfd = wsdisc_reload_socket(&rfds, port, changed);
        changed = 0;

        FD_SET(s_wsdisc_ctx->efd, &rfds);
        maxfd = MAX(s_wsdisc_ctx->efd, maxfd);

        ret = select(maxfd + 1, &rfds, NULL, NULL, NULL);
        if ( ret < 0 )
        {
            NET_WARN("select failed: %d<%s>", errno, strerror(errno));
            if (errno == EBADF)
            {
                changed = LINK_CHANGE_ALL;
            }
            continue;
        }
        else if ( ret == 0 )
        {
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
            {
                if ( s_wsdisc_ctx->sockfd[ipver][ifid] != INVALID_SOCKET && FD_ISSET(s_wsdisc_ctx->sockfd[ipver][ifid], &rfds) )
                {
                    memset(buffer, 0, sizeof(buffer));
                    slen = (socklen_t)sizeof(from);
                    rlen = recvfrom(s_wsdisc_ctx->sockfd[ipver][ifid], buffer, sizeof(buffer), 0, (struct sockaddr *)&from, &slen);
                    if ( rlen > 0 )
                    {
                        handler(s_wsdisc_ctx->sockfd[ipver][ifid], buffer, sizeof(buffer), &from);
                    }
                    else
                    {
                        NET_WARN("recvfrom %s.%s failed(%d): %d<%s>", IFACE_NAME(ifid), IPVER_NAME(ipver), rlen, errno, strerror(errno));
                        if (errno == EBADF || errno == ENOTSOCK)
                        {
                           changed |= (ipver == IPV4 ? LINK_CHANGE_IPV4_ALL : LINK_CHANGE_IPV6_ALL);
                        }
                    }
                }
            }
        }

        if (FD_ISSET(s_wsdisc_ctx->efd, &rfds))
        {
            if ( read(s_wsdisc_ctx->efd, &efd_val, sizeof(efd_val)) == sizeof(uint64_t) )
            {
                changed = efd_val & 0xFF;
            }
        }

    }
}

static void* wsdisc_thread_handler(void* arg)
{
    NET_DEBUG("Starting WS-Discovery thread handler");

    wsd_udp_loop(s_wsdisc_ctx->efd, wsdisc_get_port, wsdisc_process_request);

    return NULL;
}

static void wsdisc_update_netlink_cb(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    uint64_t changed;
    RETURN_IF(s_wsdisc_ctx == NULL || s_wsdisc_ctx->inited == 0 || o == NULL || s == NULL, NET_WARN);

    changed = (s->subject_status & 0xFF);
    write(s_wsdisc_ctx->efd, &changed, sizeof(changed));
}

static void wsdisc_epilog(void)
{
    if ( s_wsdisc_ctx != NULL )
    {
        s_wsdisc_ctx->inited = 0;
        if (s_wsdisc_ctx->efd)
        {
            pi_close(s_wsdisc_ctx->efd);
        }
        if ( s_wsdisc_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_wsdisc_ctx->srv_tid);
        }
        pi_free(s_wsdisc_ctx);
        s_wsdisc_ctx = NULL;
    }
}


static int32_t wsdisc_prolog(WSD_CTX_S* wsd_ctx)
{
    int32_t    ret = -1;
    uint8_t    mac[6];

    RETURN_VAL_IF(s_wsdisc_ctx != NULL, NET_DEBUG, 0);

    s_wsdisc_ctx = (WSDISC_CTX_S *)pi_zalloc(sizeof(WSDISC_CTX_S));
    RETURN_VAL_IF(s_wsdisc_ctx == NULL, NET_WARN, -1);

    do
    {
        memset(s_wsdisc_ctx->sockfd, INVALID_SOCKET, sizeof(s_wsdisc_ctx->sockfd)); /* initial value is INVALID_SOCKET(-1) */
        s_wsdisc_ctx->wsd_ctx = wsd_ctx;
        s_wsdisc_ctx->msg_num = 0;

        s_wsdisc_ctx->srv_tid = pi_thread_create(wsdisc_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "wsdisc_thread_handler");
        BREAK_IF(s_wsdisc_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        s_wsdisc_ctx->efd = eventfd(0, EFD_NONBLOCK);
        BREAK_IF(s_wsdisc_ctx->efd == -1, NET_WARN);
        wsd_ctx->efd_arr[wsd_ctx->efd_cnt++] = s_wsdisc_ctx->efd;

        s_wsdisc_ctx->inited = 1;

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WSDiscovery initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netlink_observer(wsd_ctx->net_ctx, wsdisc_update_netlink_cb, NULL);
    }
    else
    {
        wsdisc_epilog();
    }
    return ret;
}

FUNC_EXPORT(init, wsdisc_prolog);
FUNC_EXPORT(deinit, wsdisc_epilog);
/**
 *@}
 */
