#ifndef _PESF_DEVICE_SETTING_SYSTEM_
#define _PESF_DEVICE_SETTING_SYSTEM_

#include <quickjs.h>

/*
    声明 QuickJS C 函数由于初始化回调
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv);
JSValue js_setting_setPrompSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_getPrompSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_setBellSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_getBellSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_setFaxSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_getFaxSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_setVolSwitchMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_setting_getVolSwitchMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);


#endif /* _PESF_DEVICE_SETTING_ */
