var ERROR_NO = globalThis.pedk.common.ERROR_NO

export function get_ProductUuid()
    {
        return get_product_uuid();
    }

export function get_ProductName()
{
	const retstr = get_product_name();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_ProductSerialNumber()
{
	const retstr = get_product_serial_number();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_ProductPosition()
{
	const retstr = get_product_position();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

class ProductInfo{
	getProductUuid(){
		return get_ProductUuid();
	}
	getProductName(){
		return get_ProductName();
	}

	getProductSerialNumber(){
		return get_ProductSerialNumber();
	}

	getProductPosition(){
		return get_ProductPosition();
	}

	getCTonerStatus(){
		return get_CTonerStatus();
	}

	getMTonerStatus(){
		return get_MTonerStatus();
	}

	getYTonerStatus(){
		return get_YTonerStatus();
	}

	getKTonerStatus(){
		return get_KTonerStatus();
	}

	getCDrumModuleStatus(){
		return get_CDrumModuleStatus();
	}

	getMDrumModuleStatus(){
		return get_MDrumModuleStatus();
	}

	getYDrumModuleStatus(){
		return get_YDrumModuleStatus();
	}

	getKDrumModuleStatus(){
		return get_KDrumModuleStatus();
	}

}

globalThis.pedk.device.setting.ProductInfo                        = ProductInfo
globalThis.pedk.device.setting.ProductInfo.getProductUuid         = get_ProductUuid
globalThis.pedk.device.setting.ProductInfo.getProductName         = get_ProductName
globalThis.pedk.device.setting.ProductInfo.getProductSerialNumber = get_ProductSerialNumber
globalThis.pedk.device.setting.ProductInfo.getProductPosition     = get_ProductPosition
globalThis.pedk.device.setting.ProductInfo.getCTonerStatus		  = get_CTonerStatus;
globalThis.pedk.device.setting.ProductInfo.getMTonerStatus		  = get_MTonerStatus;
globalThis.pedk.device.setting.ProductInfo.getYTonerStatus		  = get_YTonerStatus;
globalThis.pedk.device.setting.ProductInfo.getKTonerStatus		  = get_KTonerStatus;
globalThis.pedk.device.setting.ProductInfo.getCDrumModuleStatus   = get_CDrumModuleStatus;
globalThis.pedk.device.setting.ProductInfo.getMDrumModuleStatus   = get_MDrumModuleStatus;
globalThis.pedk.device.setting.ProductInfo.getYDrumModuleStatus   = get_YDrumModuleStatus;
globalThis.pedk.device.setting.ProductInfo.getKDrumModuleStatus   = get_KDrumModuleStatus;

