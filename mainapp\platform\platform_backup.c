#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "pol/pol_io.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"
#include "pol/pol_string.h"
#include "platform_api.h"
#include "platform_backup.h"
#include "platform_crc.h"
#include "moduleid.h"
#include "nvram.h"
#include "scan_event.h"


#define PLATFPORM_BACKUP_FILE_PATH  "/nvram/platform_backup.bin"
#define SERIAL_NUMBER_LEN           10
#define MAC_ADDR_LEN                18
#define MAGIC_FLAG                  (0xa3)

#pragma pack(1)
struct required_data
{
    unsigned char   flag;                   ///< 防篡改标识
    unsigned short  crc16;                  ///< CRC校验
    unsigned char   serial_number[16];      ///< 机器序列号
    unsigned char   mac_address[6];         ///< MAC地址
    /* SCAN 校准参数 start */
    SCAN_ADF_ENGINE_PARAM_SET_S scan_adf_param;
    SCAN_FB_ENGINE_PARAM_SET_S  scan_fb_param;
    /* SCAN 校准参数 end */
};
#pragma pack()

static struct required_data s_reqd_file;

/*
 * @brief file_restore_create:判断备份文件是否存在，可读
 * [input]:platform backup file path
 * [return]：打开文件返回文件句柄
 */
    static FILE* file_restore_create( const char* filepath )
{
    FILE* file = NULL;
    if( 0 == access(filepath, F_OK) )
    {
        file = pi_fopen(filepath, "rb");
        if ( NULL == file )
        {
            pi_log_e("Failed to open file(%s)", filepath);
        }
        memset(&s_reqd_file , 0 , sizeof(s_reqd_file));
    }

    return file;
}

/*
 * @brief file_restore_destroy:关闭文件
 * [input]:文件句柄
 */
 static void file_restore_destroy ( FILE* file )
{
    if ( NULL != file )
    {
        fclose(file);
    }
}

/*
 * @brief reqdata_legality:数据合法性验证
 * [return]:验证结果
 */
static int32_t reqdata_legality ( struct required_data *rd )
{
    //序列号检查
    if ((pi_strcmp((const char*)rd->serial_number , DEFAULT_SER_NUM_STRING) == 0 &&
         pi_strlen((const char*)rd->serial_number) == SERIAL_NUMBER_LEN) ||
        (pi_strlen((const char*)rd->serial_number) < SERIAL_NUMBER_LEN) )
    {
        pi_log_e("serial number illegal.\n");
        return -1;
    }

    //MAC地址检查
    if (pi_memcmp(rd->mac_address , "\xac\xc5\x1b" , 3))
    {
        pi_log_e("MAC address illegal.\n");
        return -1;
    }

    return 0;
}

/*
 * @brief reqdata_validity:数据有效性验证
 * [return]:验证结果
 */
static int32_t reqdata_validity(struct required_data *rd)
{
    if (rd->flag != MAGIC_FLAG)
    {
        pi_log_e("[ERROR] MAGIC:0x%x error\n" , rd->flag);
        return -1;
    }

    unsigned short crc = rd->crc16;
    rd->crc16 = 0;
    rd->crc16 = platform_crc16_ccitt(0 , (unsigned char*)rd , sizeof(struct required_data));
    if (crc != rd->crc16)
    {
        pi_log_e("[ERROR] crc mismatched![%x:%x]\n" , crc , rd->crc16);
        return -1;
    }

    return 0;
}


static int32_t get_required_data_from_file(FILE* file , struct required_data *rd)
{
   //解析platform_backup 文件获取备份数据
    size_t read = fread(rd, sizeof(struct required_data), 1, file);
   if ( 1 != read )
   {
        pi_log_e("[ERROR] Failed to read platform backup file\n" );
        return -1;
   }
    return 0;
}

const int32_t platform_restore_prolog( void )
{
    int32_t ret = 0;
    FILE* file = NULL;

    file = file_restore_create(PLATFPORM_BACKUP_FILE_PATH);
    if ( NULL == file )
    {
        return -1;
    }
    ret = get_required_data_from_file(file, &s_reqd_file);
    if ( -1 == ret )
    {
        file_restore_destroy (file);
    }
    else
    {
        if ( (0 == reqdata_validity(&s_reqd_file)) && (0 == reqdata_legality(&s_reqd_file))  )
        {
            //sn
            pi_log_d("serial_number(%s)\n", s_reqd_file.serial_number);
            ret = pi_platform_set_serial_number((char *)s_reqd_file.serial_number, pi_strlen((const char *)s_reqd_file.serial_number));
            //mac地址
            pi_log_d(" MAC address = {%x:%x:%x:%x:%x:%x}\n", (unsigned int)s_reqd_file.mac_address[0] ,
                                                             (unsigned int)s_reqd_file.mac_address[1] ,
                                                             (unsigned int)s_reqd_file.mac_address[2] ,
                                                             (unsigned int)s_reqd_file.mac_address[3] ,
                                                             (unsigned int)s_reqd_file.mac_address[4] ,
                                                             (unsigned int)s_reqd_file.mac_address[5] );
            // SCAN校准参数
            pi_log_i(" scan_adf_param = {%d|%d|%d|%d|%d|%d}\n", s_reqd_file.scan_adf_param.sub_scanner_front_margin ,
                                                                s_reqd_file.scan_adf_param.sub_scanner_back_margin ,
                                                                s_reqd_file.scan_adf_param.main_scanner_front_margin ,
                                                                s_reqd_file.scan_adf_param.main_scanner_back_margin ,
                                                                s_reqd_file.scan_adf_param.front_scale ,
                                                                s_reqd_file.scan_adf_param.back_scale );
            pi_log_i(" scan_fb_param = {%d|%d|%d|%d|%d}\n",     s_reqd_file.scan_fb_param.top_margin ,
                                                                s_reqd_file.scan_fb_param.left_margin ,
                                                                s_reqd_file.scan_fb_param.vertical_scale ,
                                                                s_reqd_file.scan_fb_param.horizontal_scale ,
                                                                s_reqd_file.scan_fb_param.df_position );
            pi_nvram_set(SCAN_ID_ADF_ENGINE_PARAM_SET, VTYPE_STRUCT, &s_reqd_file.scan_adf_param, sizeof(SCAN_ADF_ENGINE_PARAM_SET_S), 1, NULL);
            pi_nvram_set(SCAN_ID_FB_ENGINE_PARAM_SET,  VTYPE_STRUCT, &s_reqd_file.scan_fb_param,  sizeof(SCAN_FB_ENGINE_PARAM_SET_S),  1, NULL);

            pi_log_i("Restore success and Delete the backup file.\n");
        }
        else
        {
            ret = -1;
            pi_log_e("Failed to restore data.\n");
        }
        file_restore_destroy (file);
        //删除文件
        unlink(PLATFPORM_BACKUP_FILE_PATH);

    }

    return ret;
}


