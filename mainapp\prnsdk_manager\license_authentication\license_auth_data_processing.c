/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file license_auth_data_processing.c
 * @brief Implementation of license data processing functions.
 * @details This file contains functions for handling and manipulating license data, including file operations and JSON handling.
 * <AUTHOR>
 * @date 2024-12-11
 */

#include "license_auth_data_processing.h"

/**
 * @brief Removes trailing newline, tab, and carriage return characters from a string.
 * @param[in,out] buffer The string to process.
 * @return The processed string.
 */
static char *license_auth_remove_char_from_buffer(char *buffer)
{
    if (buffer == NULL) {
        return NULL;
    }

    size_t length = strlen(buffer);

    // Remove trailing '\n', '\r', '\t'
    while (length > 0 && (buffer[length - 1] == '\n' || buffer[length - 1] == '\r' || buffer[length - 1] == '\t')) {
        buffer[--length] = '\0';
    }

    return buffer;
}

/**
 * @brief Adds file content to a cJSON object as a key-value pair.
 * @param[in,out] item Target cJSON object.
 * @param[in] key The key for the file content.
 * @param[in] input_file The path to the input file (must not be NULL).
 * @return 0 on success, -1 on failure.
 */
static int32_t license_auth_add_file_to_cjson_object(cJSON *item, const char *key, const char *input_file)
{
    if (input_file == NULL || key == NULL || item == NULL)
    {
        pi_log_d("Error: Invalid parameters to add file to cJSON.\n");
        return -1;
    }

    int32_t fd = open(input_file, O_RDONLY, 0777);
    if (fd < 0)
    {
        LICENSE_AUTH_ERROR_INT; // File open failed
    }

    unsigned long length = license_auth_get_file_length(fd);
    if (length == 0)
    {
        pi_log_d("Error: File is empty: %s\n", input_file);
        LICENSE_AUTH_ERROR_CLOSE(fd);
        return -1;
    }

    char *buffer = malloc(length + 1);
    if (buffer == NULL)
    {
        pi_log_d("Error: Memory allocation failed.\n");
        LICENSE_AUTH_ERROR_CLOSE(fd);
        return -1;
    }
    memset(buffer, 0, length + 1);

    if (read(fd, buffer, length) != length)
    {
        LICENSE_AUTH_ERROR_CLOSE(fd); // Read failed
        LICENSE_AUTH_SAFE_FREE(buffer);
        return -1;
    }

    cJSON_AddStringToObject(item, key, buffer);
    LICENSE_AUTH_SAFE_FREE(buffer);
    LICENSE_AUTH_ERROR_CLOSE(fd);

    return 0;
}

/**
 * @brief Extracts an integer value from a cJSON object.
 * @param[in] json_item The cJSON object.
 * @param[in] key The key to retrieve.
 * @return The integer value on success, -1 on failure.
 */
static int32_t license_auth_get_int_from_object(cJSON *json_item, const char *key)
{
    if (!json_item || !key)
    {
        LICENSE_AUTH_ERROR_INT;
    }

    cJSON *node = cJSON_GetObjectItem(json_item, key);
    if (node == NULL)
    {
        LICENSE_AUTH_ERROR_INT;
    }

    return node->valueint;
}

/**
 * @brief Retrieves a string value from a cJSON object.
 * @param[in] json_item The cJSON object.
 * @param[in] key The key to retrieve.
 * @return The retrieved string on success, NULL on failure.
 */
char *license_auth_get_string_from_object(cJSON *json_item, const char *key)
{
    if (!json_item || !key)
    {
        LICENSE_AUTH_ERROR_C_NULL;
    }

    cJSON *node = cJSON_GetObjectItem(json_item, key);
    if (node == NULL)
    {
        LICENSE_AUTH_ERROR_C_NULL;
    }

    return node->valuestring;
}

/**
 * @brief Retrieves or processes an AES key string.
 * @details Reads the string from the file if it exists; otherwise, returns the input string.
 * @param[in] input_path The input file path.
 * @return The retrieved or processed string (caller must free the memory).
 */
char *license_auth_get_string(const char *input_path)
{
    if (input_path == NULL)
    {
        LICENSE_AUTH_ERROR_NULL; // Input path is NULL
    }

    char *result = NULL;
    int32_t fd = -1;

    // Check if the file exists
    if (access(input_path, F_OK) != 0)
    {
        size_t length = strlen(input_path) + 1;
        result = malloc(length);
        if (!result)
        {
            LICENSE_AUTH_ERROR_NULL; // Memory allocation failed
        }
        strncpy(result, input_path, length);
        license_auth_remove_char_from_buffer(result);
    } else {
        // 文件存在，从文件中读取内容
        fd = open(input_path, O_RDONLY, 0777);
        if (fd < 0)
        {
            LICENSE_AUTH_ERROR_NULL; // File open failed
        }

        unsigned long length = license_auth_get_file_length(fd);
        if (length == 0)
        {
            LICENSE_AUTH_ERROR_CLOSE(fd);
            LICENSE_AUTH_ERROR_NULL; // File is empty
        }

        result = malloc(length + 1);
        if (!result)
        {
            LICENSE_AUTH_ERROR_CLOSE(fd);
            LICENSE_AUTH_ERROR_NULL; // Memory allocation failed
        }

        memset(result, 0, length + 1);
        if (read(fd, result, length) != (ssize_t)length)
        {
            LICENSE_AUTH_SAFE_FREE(result);
            LICENSE_AUTH_ERROR_CLOSE(fd);
            LICENSE_AUTH_ERROR_NULL; // Read failed
        }

        LICENSE_AUTH_SAFE_CLOSE(fd);
        license_auth_remove_char_from_buffer(result);
    }

    return result;
}

/**
 * @brief Writes license public key data to NVRAM.
 * @param[in] index The index to store the license data.
 * @param[in] license_data The license data string.
 * @param[in,out] license_collection The license data collection.
 * @return 0 on success, negative value on failure.
 */
int32_t license_auth_lice_data_proc(int32_t index, const char *license_data, LICENSE_COLLECTION_S *license_collection)
{
    if (index < 0 || index >= LICENSE_AUTH_LICENSE_COUNT)
	{
        pi_log_d("Error: Index %d out of range.\n", index);
        return -1; // Index out of range
    }

    if (license_data == NULL)
	{
        pi_log_d("Error: License data is NULL.\n");
        return -1; // Invalid input
    }

    size_t data_len = strnlen(license_data, LICENSE_AUTH_LICENSE_BUF_LENGTH);
    if(data_len == LICENSE_AUTH_LICENSE_BUF_LENGTH)
    {
        pi_log_d("Error: License data length exceeds maximum allowed %d.\n",LICENSE_AUTH_LICENSE_BUF_LENGTH - 1);
        return -1; //Length exceeded
    }
    // 更新指定索引的 License 数据
    license_collection->licenses[index].license_index = index + 1;
    strncpy(license_collection->licenses[index].license_data, license_data, data_len);
    license_collection->licenses[index].license_data[data_len] = '\0'; // Ensure null termination

    pi_log_d("[%d]license_data %s\n",license_collection->licenses[index].license_index,license_collection->licenses[index].license_data);

    return 0; // 成功
}

/**
 * @brief Extracts key-value data from a cJSON object and writes it to a file.
 * @param[in] json_item The cJSON object.
 * @param[in] key The key to extract the value for.
 * @param[in] mode 0 to retain the node, 1 to delete the node.
 * @param[out] output_file The output file path.
 * @return The file path on success, NULL on failure.
 */
char *license_auth_get_item_file_from_object(cJSON *json_item, const char *key, int32_t mode, const char *output_file)
{
    if (!json_item || !key || !output_file)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    cJSON *node = cJSON_GetObjectItem(json_item, key);
    if (node == NULL)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    pi_log_d("Writing key '%s' to file: %s\n", key, output_file);
    char *file_path = license_auth_write_link_node_to_file(node, output_file);
    if (mode == 1)
    {
        cJSON_DeleteItemFromObject(json_item, key);
    }

    return file_path;
}

/**
 * @brief Processes a license string by adding a newline every 64 characters.
 * @param[in] input_str The input license string.
 * @param[out] output_str The output buffer to store the processed string.
 * @param[in] max_len The maximum length of the output buffer.
 * @return 0 on success, negative value on failure.
 */
int32_t license_auth_process_license_string(const char *input_str, char *output_str, size_t max_len)
{
    pi_log_d("Start processing License string.\n");

    // 检查输入有效性
    if (input_str == NULL)
    {
        pi_log_d("Invalid input string, returning.\n");
        return -1;
    }

    size_t start_tag_length = strlen(LICENSE_AUTH_START_TAG);
    size_t end_tag_length = strlen(LICENSE_AUTH_END_TAG);

    // 查找开始标志
    const char *start_pos = strstr(input_str, LICENSE_AUTH_START_TAG);

    // 查找结束标志
    const char *end_pos = strstr(input_str, LICENSE_AUTH_END_TAG);

    if (!start_pos || !end_pos)
    {
        pi_log_d("Error: Begin or end tag not found.\n");
        return -1;
    }

    // 计算有效数据长度
    size_t data_length = (size_t)(end_pos - start_pos - start_tag_length);

    // 检查输出缓冲区是否足够大
    size_t required_length = data_length + 2 * (data_length / 64 + 1) + start_tag_length + 2 + end_tag_length + 1;
    if (required_length > max_len)
    {
        pi_log_d("Error: Output buffer too small. Required: %zu, Max: %zu\n", required_length, max_len);
        return -1;
    }

    // 复制开始标记到输出缓冲区并加上换行符
    strncpy(output_str, LICENSE_AUTH_START_TAG, start_tag_length);
    output_str[start_tag_length] = '\n';

    size_t output_index = start_tag_length + 1;

    // 遍历数据并在每隔 64 个字符处添加换行符
    for (size_t i = 0; i < data_length; ++i)
    {
        output_str[output_index++] = start_pos[start_tag_length + i];
        if ((i + 1) % 64 == 0)
        {
            output_str[output_index++] = '\n';
        }
    }

    // 在末尾追加换行符和结束标记
    output_str[output_index++] = '\n';
    strncpy(output_str + output_index, LICENSE_AUTH_END_TAG, end_tag_length + 1);

    pi_log_d("Finished processing License string.\n");
    return 0;
}


/**
 *@}
 */
