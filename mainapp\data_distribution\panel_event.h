/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_event.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-06-13
 * @brief process events from event manager
 */

#ifndef _PANEL_EVENT_H
#define _PANEL_EVENT_H

#include <pol/pol_types.h>
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_msg_typedef.h"
#include "panel_public.h"
#include "panel_config.h"


void panel_get_system_time();

/**
 * @brief set ipv6 config
 * @param[in] ipv6_enable ipv6 enable
 * @return ipv6 config struct
 * @author: madechang
 */
NET_IPV6_CONF_S* set_ipv6_config(uint32_t* ipv6_enable);


/**
 * @brief register event client and events
 * @author: madechang
 */
void panel_event_prolog( void );

/**
 * @brief get panel event client
 * @return panel event client
 * @author: madechang
 */
EVT_MGR_CLI_S* get_panel_event_client(void);


/**
 * @brief panel notify power manager to sleep
 * @param[in] data sleep mode
 * @author: madechang
 */
void panel_request_sleep( void );

/**
 * @brief panel notify power manager to wake
 * @param[in] data wakeup mode
 * @author: madechang
 */
void panel_request_wakeup( void );


/**
 * @brief panel request power off
 * @author: madechang
 */
void panel_request_machine_power_off( void );

/**
 * @brief panel cancel power off
 * @author: madechang
 */
void panel_request_cancel_power_off( void );

/**
 * @brief set system time to rtc
 * @author: madechang
 */
void panel_set_system_time( UI_DATE_TIME_S* recv_time );

/**
 * @brief panel dc init prolog
 */
void panel_init( void );

/**
 * @brief panel test cmd register
 */
void panel_test_cmd( void );

/**
 * @brief get system time from rtc 
 */
void panel_init_system_time(  void);


#endif /* _PANEL_EVENT_H */

/**
 *@}
 */


