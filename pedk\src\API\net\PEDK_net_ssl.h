#ifndef _PEDK_NET_SSL_H_
#define _PEDK_NET_SSL_H_

#include <quickjs.h>

#define CERT_PATH_LEN                           40      //证书存放路径长度
#define MAX_LEN                                 1024
#define MAXBUF                                  1024
#define RET_MAX                                 15

typedef struct certInfo{
    int type;//0:rsa   1:ec
    int key_length;
    int days;
    char password[24];
    char cert_path[CERT_PATH_LEN];
    char key_path[CERT_PATH_LEN];
    char pub_key_path[CERT_PATH_LEN];
}CERTINFO;

typedef struct loadinfo {
    char keypath[CERT_PATH_LEN];
    char certpath[CERT_PATH_LEN];
    char password[24];
}LOADINFO;

typedef struct connectioninfo {
    char keypath[CERT_PATH_LEN];
    char certpath[CERT_PATH_LEN];
    char hostname[16];
    char password[24];
    int  port;
}SSLCONINFO;

typedef struct connectflag {
    int num;
    int sslcon_port;
    char hostname[16];
}sslflag;

typedef struct sendinfo {
    char ssl_data[MAX_LEN];
    int sslcon_port;
    char sslcon_hostname[16];
}sslsendinfo;

/*
    声明 QuickJS C 函数由于初始化回调
*/
JSValue js_generateCertificate(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv);

JSValue js_loadSslCertificate(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv);

JSValue js_createSslConnection(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv);

JSValue js_SslSend(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv);

JSValue js_SslRecv(JSContext *ctx, int argc, JSValueConst *argv);

JSValue js_SslClose(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv);

JSValue sslonrecv(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv);

int js_ssl_init(JSContext *ctx, JSValueConst global);
int js_ssl_cert_init(JSContext *ctx, JSValueConst global);



#endif /* _PESF_NET_SSL_ */
