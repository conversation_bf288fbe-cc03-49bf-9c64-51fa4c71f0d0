/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file license_auth_common.c
 * @brief Common utility implementations for license authentication.
 * @details This file implements utility functions used in the license authentication process.
 * <AUTHOR>
 * @date 2024-12-11
 */

#include "license_auth_common.h"

/**
 * @brief Calculates the number of occurrences of a specific character in a string.
 * @param[in] buffer The input string.
 * @return The number of occurrences of the character. Returns 0 if the input is invalid.
 */
int32_t license_auth_get_line_count(const char *buffer)
{
    if (buffer == NULL)
    {
        return 0;
    }

    int32_t line_count = 0;
    const char delimiter = '#';

    // Traverse the string and count occurrences of the delimiter
    while (*buffer != '\0')
    {
        if (*buffer == delimiter)
        {
            ++line_count;
        }
        ++buffer;
    }

    return line_count;
}

/**
 * @brief Counts the number of leading spaces in a string.
 * @param[in] input The input string.
 * @return The number of leading spaces. Returns -1 if the input is invalid.
 */
int32_t license_auth_trim_leading_spaces(const char *input)
{
    if (input == NULL)
    {
        return -1;
    }

    int32_t space_count = 0;

    // Traverse the string and count leading spaces
    while (input[space_count] == ' ')
    {
        ++space_count;
    }

    return (space_count > 0) ? space_count - 1 : -1;
}

/**
 * @brief Computes the length of non-empty characters in a string.
 * @param[in] input_string The input string.
 * @param[in] buffer_length The total length of the buffer.
 * @return The length of non-empty characters. Returns 0 if the input is invalid.
 */
size_t license_auth_get_complex_length(const char *input_string, size_t buffer_length)
{
    if (input_string == NULL || buffer_length == 0)
    {
        return 0;
    }

    // Traverse the string in reverse to find the last non-null character
    while (buffer_length > 0 && input_string[buffer_length - 1] == '\0')
    {
        --buffer_length;
    }

    return buffer_length;
}
/**
 * @}
 */
