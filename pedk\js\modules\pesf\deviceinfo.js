
var ERROR_NO = globalThis.pedk.common.ERROR_NO

export function get_DC_FW_Version()
{
	const retstr = get_dcfw_version();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_EC_FW_Version()
{
	const retstr = get_Ecfw_version();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_FPGA1Version()
{
	const retstr = get_fpga1_version();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_FPGA2Version()
{
	const retstr = get_fpga2_version();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_CTonerRemainVal()
{
	const retstr = get_CToner_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_MTonerRemainVal()
{
	const retstr = get_MToner_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_YTonerRemainVal()
{
	const retstr = get_YToner_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_KTonerRemainVal()
{
	const retstr = get_KToner_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_CDrumModuleModel()
{
	const retstr = get_CDrumModule_Model();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_MDrumModuleModel()
{
	const retstr = get_MDrumModule_Model();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_YDrumModuleModel()
{
	const retstr = get_YDrumModule_Model();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_KDrumModuleModel()
{
	const retstr = get_KDrumModule_Model();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_CDrumModuleRemainVal()
{
	const retstr = get_CDrumModule_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_MDrumModuleRemainVal()
{
	const retstr = get_MDrumModule_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_YDrumModuleRemainVal()
{
	const retstr = get_YDrumModule_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_KDrumModuleRemainVal()
{
	const retstr = get_KDrumModule_RemainVal();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_DeviceAverageCoverageRate()
{
	const retstr = get_Device_AverageCoverageRate();
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}


class DeviceInfo{
	getDC_FW_Version(){
		return get_DC_FW_Version();
	}

	getEC_FW_Version(){
		return get_EC_FW_Version();
	}

	getFPGA1Version(){
		return get_FPGA1Version();
	}

	getFPGA2Version(){
		return get_FPGA2Version();
	}

	getCTonerRemainVal(){
		return get_CTonerRemainVal();
	}

	getMTonerRemainVal(){
		return get_MTonerRemainVal();
	}

	getYTonerRemainVal(){
		return get_YTonerRemainVal();
	}

	getKTonerRemainVal(){
		return get_KTonerRemainVal();
	}

	getCDrumModuleModel(){
		return get_CDrumModuleModel()
	}

	getMDrumModuleModel(){
		return get_MDrumModuleModel()
	}

	getYDrumModuleModel(){
		return get_YDrumModuleModel()
	}

	getKDrumModuleModel(){
		return get_KDrumModuleModel()
	}

	getCDrumModuleRemainVal(){
		return get_KDrumModuleRemainVal();
	}

	getMDrumModuleRemainVal(){
		return get_KDrumModuleRemainVal();
	}

	getYDrumModuleRemainVal(){
		return get_KDrumModuleRemainVal();
	}

	getKDrumModuleRemainVal(){
		return get_KDrumModuleRemainVal();
	}

	getDeviceAverageCoverageRate(){
		return get_DeviceAverageCoverageRate();
	}

}

globalThis.pedk.device.setting.DeviceInfo                        = DeviceInfo
globalThis.pedk.device.setting.DeviceInfo.getDC_FW_Version       = get_DC_FW_Version
globalThis.pedk.device.setting.DeviceInfo.getEC_FW_Version       = get_EC_FW_Version
globalThis.pedk.device.setting.DeviceInfo.getFPGA1Version        = get_FPGA1Version
globalThis.pedk.device.setting.DeviceInfo.getFPGA2Version        = get_FPGA2Version
globalThis.pedk.device.setting.DeviceInfo.getCTonerRemainVal     = get_CTonerRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getMTonerRemainVal     = get_MTonerRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getYTonerRemainVal     = get_YTonerRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getKTonerRemainVal     = get_KTonerRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getCDrumModuleModel    = get_CDrumModuleModel;
globalThis.pedk.device.setting.DeviceInfo.getMDrumModuleModel    = get_MDrumModuleModel;
globalThis.pedk.device.setting.DeviceInfo.getYDrumModuleModel    = get_YDrumModuleModel;
globalThis.pedk.device.setting.DeviceInfo.getKDrumModuleModel    = get_KDrumModuleModel;
globalThis.pedk.device.setting.DeviceInfo.getCDrumModuleRemainVal = get_CDrumModuleRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getMDrumModuleRemainVal = get_MDrumModuleRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getYDrumModuleRemainVal = get_YDrumModuleRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getKDrumModuleRemainVal = get_KDrumModuleRemainVal;
globalThis.pedk.device.setting.DeviceInfo.getDeviceAverageCoverageRate = get_DeviceAverageCoverageRate;

