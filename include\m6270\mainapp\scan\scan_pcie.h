/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  scan_pcie.h
* @addtogroup scan
*
* @{
* @addtogroup  scan
* <AUTHOR>
* @date   2022-5-24
* @version  v1.0
* @brief   transplant LP-2023 protocol for pc driver
**/

#ifndef SCAN_PCIE_H
#define SCAN_PCIE_H

#include <linux/ioctl.h>
#include <stdint.h>

#include "pol/pol_threads.h"
#include "memmgr/memmgr.h"
#include "utilities/msgrouter.h"
#include <msgrouter_main.h>
#include "public_data_proc.h"

#define SCAN_PCIE_LOG_LEVEL 5   ///< scan pcie log level

#define COPY_PARA_LEN_MAX   1024    ///< copy para len

#define MY_CHECK(condition) \
do { \
    if (!(condition)) { \
        fprintf(stderr, "the value is illegal(0 or null),please check again: %s, file: %s, line: %d\n", \
                #condition, __FILE__, __LINE__); \
        exit(1); \
    } \
} while(0)

/**
 * @brief transfer type
*/
typedef enum
{
    MSG_PAGE_START = 0,     ///< page start
    MSG_PAGE_BAND_DATA,     ///< band data
    MSG_PAGE_END,           ///< page end
    MSG_JOB_END,            ///< job end
    MSG_FPGA_VERSION_REPORT,///< fpga version report
    MSG_6220_VERSION_REPORT,///< 6220 version report
    MSG_JOB_REQUEST_PAUSE,
    MSG_JOB_REQUEST_RESTART,
    MSG_IMAGE_AREA = 9,
    MSG_SCAN_JOB_PROCESS_FINISHED,

    MSG_SCAN_PCIE_JOB_REQUEST   = 100,  ///< job request
    MSG_SCAN_PCIE_JOB_START,            ///< job start
    MSG_SCAN_PCIE_JOB_PAUSE,            ///< job pause
    MSG_SCAN_PCIE_JOB_RESTART,          ///< job restart
    MSG_SCAN_PCIE_JOB_CANCEL,           ///< job cancel
    MSG_SCAN_PCIE_JOB_END,              ///< job end
    MSG_SCAN_PCIE_JOB_CONTINUOUS_SCAN,  ///< job continuous scan
    MSG_SCAN_PCIE_JOB_LAST_PAGE,        ///< job last page
    MSG_SCAN_PCIE_FPGA_VERSION_REQUEST, ///< fpga version request
    MSG_SCAN_PCIE_6220_VERSION_REQUEST, ///< 6220 version request
    MSG_SCAN_PCIE_MIX_HEIGHT,           ///< mix height
    MSG_SCAN_PCIE_JOB_COMPLETE,         ///< job complete
    MSG_SCAN_PCIE_COPY_CALC_DATA_Y,     ///< calibration data y channel
    MSG_SCAN_PCIE_COPY_CALC_DATA_M,     ///< calibration data m channel
    MSG_SCAN_PCIE_COPY_CALC_DATA_C,     ///< calibration data c channel
    MSG_SCAN_PCIE_COPY_CALC_DATA_K,     ///< calibration data k channel
    MSG_SCAN_PCIE_MAX           = 199,  ///< pcie max

    /* file transfer from 6220 to 6270 */
    MSG_FILE_TRANSFER_START = 500,      ///< transfer start
    MSG_FILE_TRANSFER_DATA,             ///< transfer data
    MSG_FILE_TRANSFER_END,              ///< transfer end
    MSG_FILE_TRANSFER_FAIL,             ///< transfer fail

    MSG_PCIE_NULL = 600,
} TRANSFER_TYPE_E;

/**
 * @brief strip object
*/
typedef struct strip_s
{
    /* values can transfer through PCIE */
    int     color;              //< color
    int     w;                  ///< strip width
    int     h;                  ///< strip height
    int     s;                  ///< bytes in line
    int     y;                  //< y offset in image
    int     bpp;                ///< bits per pixel

    int     last_strip;         ///< is last strip ?
    int     first_strip;        ///< is first strip ?
    int     strip_index;        ///< strip index in image, start from 0

    PAGE_TYPE_E page_type;
    DATA_TYPE_E data_type;

    uint32_t valid_len;
} STRINP_S;


/**
 * @brief pcie page
*/
typedef struct
{
    int     width;              ///< image width
    int     height;             ///< image height

    int     bpp;                ///< bits per pixel
    int     cnt;                ///< page count
    int     page_number;        ///< page number

    int     max_strip_size;     ///< max data size of strip

    PAGE_TYPE_E page_type;      ///< page type
    DATA_TYPE_E data_type;      ///< data type
    IMAGE_S   image;            ///< image data struct,this is a universal info
    ORIGINAL_ORIENTATION_E  image_orientation;  ///< page orientation

    COPY_MARGIN_INFO_S copy_pad_info; ///< copy use 6220 pip pad info
} SCAN_PCIE_PAGE_S;

/**
 * @brief pcie job type
*/
typedef enum
{
    SCAN_PCIE_JOB_SCAN_JPEG,            ///< scan job output jpeg
    SCAN_PCIE_JOB_COPY_PIP,             ///< copy job with pip processing
    SCAN_PCIE_JOB_COPY_PIP_ROTATE,      ///< copy job with pip,rotate processing
    SCAN_PCIE_JOB_COPY_PIP_JBIG,        ///< copy job with pip,jbig processing
    SCAN_PCIE_JOB_COPY_PIP_ROTATE_JBIG, ///< copy job with pip,rotate,jbig processing
    SCAN_PCIE_JOB_COPY_CORRECT_PIP,     ///< copy job with correct pip,processing

    SCAN_PCIE_JOB_SCAN_NORMAL,          ///< scan normal job
    SCAN_PCIE_JOB_COPY_NORMAL,          ///< copy normal job

    SCAN_PCIE_JOB_COPY_SCAN_MEANTIME,   ///< copy scan meantime
} SCAN_PCIE_JOB_TYPE_E;

/**
 * @brief rotate mode
*/
typedef enum
{
    ROTATE_MODE_90,     ///< 90
    ROTATE_MODE_180,    ///< 180
    ROTATE_MODE_270,    ///< 270
    ROTATE_MODE_90_FLIP_X,///< 90 flip x
    ROTATE_MODE_180_FLIP_X,///< 180 flip x
    ROTATE_MODE_270_FLIP_X,///< 270 flip x
    ROTATE_MODE_90_FLIP_Y,  ///< 90 flip y
    ROTATE_MODE_180_FLIP_Y, ///< 180 flip y
    ROTATE_MODE_270_FLIP_Y, ///< 270 flip y
    ROTATE_MODE_90_FLIP_XY, ///< 90 flip xy
    ROTATE_MODE_180_FLIP_XY,///< 180 flip xy
    ROTATE_MODE_270_FLIP_XY ///< 270 flip xy
} ROTATE_MODE_E;

#define ROTATE_SEQUENCE_MAX     48  ///< rotate sequence max

/**
 * @brief scan pcie job request
*/
typedef struct
{
    int             job_type;                               ///< job type
    int             jpeg_quality;                           ///< jpeg quality
    int             pip_mode;                               ///< pip mode
    int             rotate_mode;                            ///< rotate mode
    int             copy_band_h;///copy band height
    int             max_band_size;  ///< max band size
    int             rotate_sequence[ROTATE_SEQUENCE_MAX];///< rotate sequence

    uint16_t        scan_mode;      ///< scan mode
    uint16_t        scan_page_type; ///< scan page type
    uint16_t        color_mode;     ///< color mode
    uint16_t        data_type;      ///< data type
    uint16_t        resolution;     ///< resolution
    uint16_t        x_scale_numerator;///< x scale numerator
    uint16_t        x_scale_denominator;///< y scale denominator
    uint16_t        y_scale_numerator;  ///< y scale numerator
    uint16_t        y_scale_denominator;///< y scale denominator
    uint16_t        margin_top;         ///< margin top
    uint16_t        margin_left;        ///< margin left
    uint16_t        area_height;        ///< area height
    uint16_t        area_width;         ///< area width
    uint16_t        scan_quality;       ///< scan quality
    uint16_t        bits_per_pixel;     ///< bits per pixel
    uint16_t        contrast;           ///< contrast
    uint16_t        brightness;         ///< brightness
    uint16_t        gamma;              ///< gamma
    uint16_t        sharpness;          ///< sharpness
    uint16_t        saturation;         ///< saturation
    uint16_t        hue;                ///< hue
    uint16_t        backgroundremove;   ///< backgroundremove
    uint16_t        colorbalance_c;     ///< colorbalance_c
    uint16_t        colorbalance_m;     ///< colorbalance_m
    uint16_t        colorbalance_y;     ///< colorbalance_y
    uint16_t        colorbalance_k;     ///< colorbalance_k
    uint16_t        in_band_h;          ///< in_band_h
    uint16_t        combine_band_num;   ///< combine_band_num
    uint16_t        out_band_h;         ///< out_band_h
    uint16_t        use_cs;             ///< use_cs
    uint16_t        use_mf;             ///< use_mf
    uint16_t        use_dn;             ///< use_dn
    uint16_t        use_xy;             ///< use_xy
    uint16_t        use_pie;            ///< use_pie
    uint16_t        scan_original;      ///< scan original
    uint16_t        scan_ppm;           ///< scan ppm
    uint16_t        machine_speed;      ///< machine speed
    uint16_t        machine_type;       ///< machine type
    uint8_t         pic_line_reverse;   ///< pic_line_reverse
    uint8_t         pie_line_reverse;   ///< pie_line_reverse
    uint8_t         is_mix_size;        ///< copy mix paper
    int             copy_edge_scan;     ///< copy_edge_scan

    uint8_t         copy_para[COPY_PARA_LEN_MAX];   ///< copy param

    //remove_color
    uint8_t         use_remove_color;   ///< remove_color (1:use 0:not use)
    uint8_t         remove_color_plane; //< 0x0:remove all,(bit0:R 1->remove,bit1:G 1->remove,bit2:B 1->remove)
    //colour_check
    uint8_t                 auto_colour_check; ///< (1:use 0:not use)

    //colour_inversion
//    uint8_t         use_colour_inversion;///< colour_inversion (1:use 0:not use)

    uint8_t         use_pip;///< use pip (1:use 0:not use)
    uint8_t         pip_scale_x;///< pip scale x
    uint8_t         pip_scale_y;///< pip scale y
    uint8_t         pip_out_bpp;///< pip output bpp

    //edge_clean
    uint8_t                 use_edge_clean;///< use edge clean (1:use 0:not use)
    COPY_EDGE_CLEAN_S       edge_clean; ///< front page edge clean info

    //jbig enc
    uint8_t                 use_jbig_encode;///< use jbig encode

    //ID_correction
    uint8_t                 use_id_correction; ///< (1:use 0:not use)

    //jpeg_encode
    uint8_t                 use_jpeg_encode; ///< (1:use 0:not use)

    //rotate
    uint8_t                 use_rotate; ///< (1:use 0:not use)
    uint8_t                 is_mirror;  ///< mirror
    uint8_t					use_book_discern; //(1:use 0:not use)

    uint8_t                 use_cache; ///< (1:cache 1 page 2 : cache 2 page  0:not use)
    uint16_t                sort_order;///< (bit0-bit1 main body start cnt)
    ///< (bit2-bit3 main body start number)
    ///< (bit4-bit5 auto-increment number 1:page cnt+1 , 2:page cnt+2)
    ///< (bit6-bit7 cover mode 0-not cover,01-out cover,10-in cover 11-double cover)
    ///< (bit8-bit9 back cover mode 0-not cover,01-out cover,10-in cover 11-double cover)
    uint8_t                 need_blank_page;///< (bit0:blank cover out bit1:blank cover in bit2:blank back cover out bit3:blank back cover in)


    //copy_scan_meantime
    uint8_t                 use_copy_scan_meantime; ///< (1:use 0:not use)
    uint8_t                 use_oly_img;            ///< (1:use 0:not use)
    uint8_t                 use_scan_sep;            ///< (1:use 0:not use)
    uint8_t                 mid_edge_clean;          ///< mid edge clean (>0:use 0:not use)
    uint8_t                 image_orientation;  ///< rotate or any processer need focuss
    uint8_t                 original_flip;          ///< rotate use;
    uint8_t                 id_scan_mode;
	uint8_t                 reserve[512+7];            //< reserve buff.
} __attribute__( ( aligned( 4 ) ) ) SCAN_PCIE_JOB_REQUEST_S ;

/**
 * @brief scan band submit
*/
typedef struct
{
    uint32_t    physical_address;   ///< physical address
    uint32_t    valid_size;         ///< valid size
    uint32_t    band_index;         ///< band index
    uint32_t    band_size;          ///< band size
} SCAN_BAND_SUMBIT_S;

/**
 * @brief pcie transfer
*/
typedef struct
{
    uint32_t msg_type;  ///<msg type
    uint32_t data[1024];    ///< data
    uint32_t data_len;      ///< data length

    union
    {
        struct
        {
            uint32_t    phy_addr;   ///< phy_addr
            uint32_t    size;       ///< size
        } mv6220;
        SCAN_BAND_SUMBIT_S   mv6270;    ///< scan band submit
    } band;
} PCI_TRANSFER_S;

/**
 * @brief pcie channel
*/
typedef enum
{
    PcieFrontSide,  ///< pcie front side
    PcieBackSide,   ///< pcie back side
} PCIE_CHANNEL_E;

typedef enum
{
    SCAN_MGR_PAUSE = 0,
    SCAN_ENGINE_PAUSE,

}PAUSE_MODULE_E;

/**
 * @brief pcie machine type
*/
typedef enum
{
    PcieMachineTypeMono,    ///< pcie machine mono type
    PcieMachineTypeColor,   ///< pcie machine color type
} PCIE_MACHINE_TYPE_E;

#define PCIE_DEVICE_NAME                "/dev/pantumpci-transfer"       ///< pcie device name
#define PCIE_MAX_EVENTS                 20                              ///< pcie max event
#define PCI_TRANSFER_IOC_TX             _IOW('t', 0x01, int)            ///< pcie transfer ico
#define PCI_TRANSFER_BAND_SUBMIT        _IOW('t', 0x02, int)            ///< pcie transfer band submit
#define PCI_TRANSFER_IOC_TXDM4          _IOW('t', 0x03, int)            ///< ioc txdm4
#define PCI_TRANSFER_IOC_TXDM2          _IOW('t', 0x04, int)            ///< ioc txdm2
#define PCI_TRANSFER_BAND_CLEAR         _IOW('t', 0x05, int)            ///< band clear
#define PCI_TRANSFER_PM_D3COLD          _IOW('t', 0x06, int)            ///< pm d3cold
#define PCI_TRANSFER_PM_D0              _IOW('t', 0x07, int)            ///< pm d0

/**
 * @brief debug_scan_msg_send
 * @param[in] msg_type msg type
 * @param[in] msg1
 * @param[in] msg2
 * @param[in] msg3
 * @param[in] msg_sender msg sender id
 * @param[in] msg_recv msg recv if
 * @return void \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void debug_scan_msg_send( int msg_type, int msg1, int msg2, void* msg3, MODULE_ID_E msg_sender, MODULE_ID_E msg_recv );

/**
 * @brief debug_scan_msg_send
 * @param[in] mem_handle mem handle
 * @param[in] receive_band_size band size
 * @return void \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_pcie_mem_init(
    void* mem_handle,
    int receive_band_size,
    int use_cache
);

/**
 * @brief debug_scan_msg_send
 * @param[in] void
 * @return void \n
 * @retval void
 * <AUTHOR>
 * @date 2022-12-14
*/
void scan_pcie_mem_deinit();


/**
 * @brief scan_pcie_continuous_scan
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_continuous_scan( void );

/**
 * @brief scan_pcie_band_submit
 * @param[in] cnt band count
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_band_submit( void );

/**
 * @brief scan_pcie_band_clear
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_band_clear();

int scan_pcie_pause_count_clear();

/**
 * @brief free band ilst
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_pcie_band_free();

void scan_pcie_band_list_init();

/**
 * @brief search band
 * @param[in] phy_addr phy addr
 * @return void*  \n
 * @retval point to band
 * <AUTHOR>
 * @date 2022-5-24
*/
void* band_member_search( uint32_t phy_addr );

/**
 * @brief scan_pcie_band_clear
 * @param[in] is_dadf is dadf
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_start( int is_dadf );

/**
 * @brief scan_pcie_job_pause
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_pause(int is_dadf);


/**
 * @brief scan_pcie_job_restart
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_restart(int is_dadf);


/**
 * @brief scan_pcie_job_pause
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_engine_pause(int is_dadf);


/**
 * @brief scan_pcie_job_restart
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_engine_restart(int is_dadf);

/**
 * @brief scan_pcie_job_cancel
 * @param[in] is_dadf is dadf
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_cancel( int is_dadf );

/**
 * @brief scan_pcie_adf_last_page
 * @param[in] is_dadf is dadf
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_adf_last_page( int is_dadf );

/**
 * @brief scan_pcie_mix_height
 * @param[in] mix_height max height
 * @param[in] is_dadf is dadf
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_mix_height( int mix_height, int is_dadf );

/**
 * @brief scan_pcie_job_complete
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_complete();

/**
 * @brief scan_pcie_init
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_init();

/**
 * @brief scan_pcie_job_request
 * @param[in] side front / back
 * @param[in] request job request
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_job_request( PCIE_CHANNEL_E side, SCAN_PCIE_JOB_REQUEST_S* request );

/**
 * @brief scan_pcie_stress
 * @param[in] PCIE_CHANNEL_E front / back
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_stress( PCIE_CHANNEL_E side );

/**
 * @brief scan_pcie_set_stress_flag
 * @param[in] flag stress flag
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_pcie_set_stress_flag( int flag );

/**
 * @brief scan_pcie_pm_d3cold
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_pm_d3cold();

/**
 * @brief scan_pcie_pm_d0
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_pm_d0();

/**
 * @brief scan_pcie_monitor_start
 * @param[in] type type
 * @param[in] timeout timeout value
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_pcie_monitor_start( int type, int timeout );

/**
 * @brief scan_pcie_monitor_start
 * @param[in] type type
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_pcie_monitor_stop( int type );

/**
 * @brief scan_pcie_fpga_version_request
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_fpga_version_request( void );

/**
 * @brief scan_pcie_6220_version_request
 * @return int  \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_pcie_6220_version_request( void );

/**
 * @brief scan_pcie_monitor_start
 * @param[in] trs point to pcie transfer object
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void pcie_file_transfer_process( PCI_TRANSFER_S* trs );

/**
 * @brief scan_pcie_band_alloc_request
 * @param[in] requeset to alloc band count
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2023-12-1
*/
int scan_pcie_band_alloc_request( int band_cnt );

int scan_pcie_job_copy_calc_data_y(void *data, int len);

int scan_pcie_job_copy_calc_data_m(void *data, int len);

int scan_pcie_job_copy_calc_data_c(void *data, int len);

int scan_pcie_job_copy_calc_data_k(void *data, int len);

int pcie_file_transfer_6270(int argc, char *argv[]);

int get_params_send_to_6220_done(void);


#endif

/**
 *@}
 */

