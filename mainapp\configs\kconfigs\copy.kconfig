# 边缘清除
config COPY_EDGE_CLEAN
    bool "copy edge clean"
    depends on COPY
    default y
    help
        "copy edge clean support"

# 边距调整
config COPY_MARGIN_ADJUST
    bool "copy margin adjust"
    depends on COPY
    default y
    help
        "copy margin adjust support"

# 水印复印
config COPY_WATERMARK
    bool "copy watermark"
    depends on COPY
    default y
    help
        "copy watermark support"

# 混合复印
config COPY_MIX
    bool "copy mix"
    depends on COPY
    default y
    help
        "copy mix support"

# 镜像复印
config COPY_MIRROR_MODE
    bool "copy mirror mode"
    depends on COPY
    default y
    help
        "copy mirror mode support"

# 反相复印
config COPY_COLOR_INVERSION
    bool "copy color inversion"
    depends on COPY
    default y
    help
        "copy color inversion support"

# 边对边复印
config COPY_EDGE_TO_EDGE_MODE
    bool "copy edge to edge mode"
    depends on COPY
    default y
    help
        "copy edge to edge mode support"

# 样本复印
config COPY_SWATCH_MODE
    bool "copy swatch mode"
    depends on COPY
    default y
    help
        "copy swatch mode support"

# OHP插页
config COPY_OHP_INSERT_PAGE_MODE
    bool "copy ohp insert page mode"
    depends on COPY
    default y
    help
        "copy ohp insert page mode support"

# 插页
config COPY_INSERT_PAGE
    bool "copy insert page"
    depends on COPY
    default y
    help
        "copy insert page support"

# 章节
config COPY_SECTION
    bool "copy section"
    depends on COPY
    default y
    help
        "copy section support"

# 预复印
config COPY_BEFOREHAND_MODE
    bool "copy beforehand mode"
    depends on COPY
    help
        "copy beforehand mode support"

# 插入图像
config COPY_INSERT_IMAGE
    bool "copy insert image"
    depends on COPY
    default y
    help
        "copy insert image support"

# 页码
config COPY_PAGE_NUMBER
    bool "copy page number"
    depends on COPY
    help
        "copy page number support"

# 日期/时间
config COPY_DATE_AND_TIME
    bool "copy date and time"
    depends on COPY
    help
        "copy date and time support"

# 印记
config COPY_STAMP
    bool "copy stamp"
    depends on COPY
    help
        "copy stamp support"

# 印记重复
config COPY_STAMP_REPETITION
    bool "copy stamp repetition"
    depends on COPY
    help
        "copy stamp repetition support"

# 叠图
config COPY_OVERLAY_IMAGE
    bool "copy overlay image"
    depends on COPY
    default y
    help
        "copy overlay image support"

# 已注册叠图
config COPY_REGISTER_OVERLAY_IMAGE
    bool "copy register overlay image"
    depends on COPY
    default y
    help
        "copy register overlay image support"

# 页边距
config COPY_PAGE_MARGIN
    bool "copy page margin"
    depends on COPY
    help
        "copy page margin support"

# 复印留底（SDK）
config COPY_BACKUP_MODE
    bool "copy backup mode"
    depends on COPY
    default y
    help
        "copy backup mode support"

# 页面分离
config COPY_PAGE_SEPARATION
    bool "copy page separation"
    depends on COPY
    default y
    help
        "copy page separation support"

# 排纸处理
config COPY_OUTPUT_PAPER_DEAL
    bool "copy output paper deal"
    depends on COPY
    default y
    help
        "copy output paper deal support"

# 书本原稿
config COPY_BOOK_ORIGINAL 
    bool "copy book original"
    depends on COPY
    default y
    help
        "copy book original support"

# 索引原稿
config COPY_INDEXES_ORIGINAL 
    bool "copy indexes original"
    depends on COPY
    help
        "copy indexes original support"

# 海报复印
config COPY_POSTER
    bool "copy poster"
    depends on COPY
    default y
    help
        "copy poster support"

# 克隆复印
config COPY_CLONE
    bool "copy clone"
    depends on COPY
    default y
    help
        "copy clone support"

# ID复印
config COPY_IDCARD
    bool "copy idcard"
    depends on COPY
    default y
    help
        "copy idcard support"

# 票据复印
config COPY_BILL
    bool "copy bill"
    depends on COPY
    default y
    help
        "copy bill support"

# 小册子复印
config COPY_BOOKLET
    bool "copy booklet"
    depends on COPY
    default y
    help
        "copy booklet support"

# 多和一复印
config COPY_NUP
    bool "copy nup"
    depends on COPY
    default y
    help
        "copy nup support"

# 页眉页脚
config COPY_PAGE_HEADER_PAGE_FOOTER
    bool "copy page header page footer"
    depends on COPY
    default y
    help
        "copy page header page footer support"

# 分隔页
config COPY_SEPARATOR_PAGE
    bool "copy separator page"
    depends on COPY
    default y
    help
        "copy separator page support"

# 背景消除
config COPY_BACKGROUNDMOVE_LEVEL
    bool "copy backgroundmove level"
    depends on COPY
    default y
    help
        "copy backgroundmove level support"

# 复印画像高清模式
config COPY_HIGH_DEFINITION_MODE
    bool "copy high definition mode"
    depends on COPY
    help
    "copy high definition mode support"