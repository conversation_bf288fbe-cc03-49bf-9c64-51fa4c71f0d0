#include <stdio.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <pthread.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "pol/pol_threads.h"
#include "pol/pol_list.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"
#include "pol/pol_io.h"

#include "pedk_mgr.h"
#include "netinet/tcp.h"
#include "./app_manager/app_manager.h"
#include "event_manager/event_mgr.h"
#include "./../system_manager/status_pedkapi.h"

//#include "event_manager/event_mgr_typedef.h"

//#include "iccard.h"

#define MUTEX_PROCESS(code, mtx)     { pthread_mutex_lock(&mtx); code; pthread_mutex_unlock(&mtx); }
#define SERVER_ADDR "127.0.0.1"
#define SERVER_PORT 50999
#define RECV_BUF_SIZE (1024)

#pragma pack(1)
struct socket_msg
{
    uint8_t     rtid;
    MAIN_MSG_E  main;  		//server to client: 发送者; clinet to server: 接收者
    SUB_MSG_E   sub;
	int32_t     respond; 	//返回值
    int32_t     size;   	//字节数据的长度
    uint8_t     data[0];	//字节数据的首字符地址
};
#pragma pack()

struct protocol_packet
{
    uint8_t     cmd;
    uint8_t     len_high;
    uint8_t     len_low;
    uint8_t     data[0];
};

struct packet_node
{
    struct list_head        list;
	struct protocol_packet* msg;
};

struct module_table
{
    struct list_head        list;
    MAIN_MSG_E              main;
    PEDKAPI_HANDLER_FUNC    func;
    void*                   arg;
};

static struct list_head     s_msg_list = PI_LIST_HEAD_INIT(s_msg_list);
static pthread_mutex_t      s_msg_lock = PTHREAD_MUTEX_INITIALIZER;

static struct list_head     s_reg_list = PI_LIST_HEAD_INIT(s_reg_list);
static pthread_mutex_t      s_reg_lock = PTHREAD_MUTEX_INITIALIZER;

static PI_THREAD_T          s_serv_tid = INVALIDTHREAD;
static PI_THREAD_T          s_rfid_tid = INVALIDTHREAD;

EVT_MGR_CLI_S * g_pedk_cli_ptr = NULL;

static void message_queue_clean(void)
{
    struct packet_node* node;
    struct list_head*   pos;
    struct list_head*   n;

    pi_list_for_each_safe(pos, n, &s_msg_list)
    {
        node = pi_list_entry(pos, struct packet_node, list);
        if ( node != NULL )
        {
            MUTEX_PROCESS(pi_list_del_entry(&node->list), s_msg_lock);
            if ( node->msg != NULL )
            {
                pi_free(node->msg);
            }
            pi_free(node);
        }
    }
}

static int32_t message_queue_push(struct protocol_packet* packet)
{
    struct packet_node* node;

    RETURN_VAL_IF(packet == NULL, pi_log_e, -1);

    node = (struct packet_node *)pi_zalloc(sizeof(struct packet_node));
    RETURN_VAL_IF(node == NULL, pi_log_e, -1);

    node->msg = packet;
    MUTEX_PROCESS(pi_list_add_tail(&node->list, &s_msg_list), s_msg_lock);

    return 0;
}

struct protocol_packet* message_queue_pop(void)
{
    struct protocol_packet* packet = NULL;
    struct packet_node*     node;

    MUTEX_PROCESS(
        node = pi_list_first_entry_or_null(&s_msg_list, struct packet_node, list);
        if ( node != NULL )
        {
            packet = node->msg;
            pi_list_del_entry(&node->list);
            pi_free(node);
        },
        s_msg_lock
        );

    return packet;
}

/*成功返回结构体指针，失败返回null*/
static struct protocol_packet* protocol_packing(uint8_t cmd, uint8_t* buf, uint16_t bufsize)
{
    struct protocol_packet* packet;

    packet = (struct protocol_packet *)pi_zalloc(sizeof(struct protocol_packet) + bufsize);
    RETURN_VAL_IF(packet == NULL, pi_log_e, NULL);

    packet->cmd      = cmd;
    packet->len_high = (uint8_t)((bufsize & 0xFF00) >> 8);
    packet->len_low  = (uint8_t)(bufsize & 0xFF);
    if ( bufsize > 0 && buf != NULL )
    {
        memcpy(packet->data, buf, bufsize);
    }

    return packet;
}

/*成功返回结构体指针，失败返回null*/
static struct socket_msg* message_packing(MAIN_MSG_E main, SUB_MSG_E sub, int32_t respond, uint8_t* buf, int32_t bufsize)
{
    struct socket_msg* msg;

    msg = (struct socket_msg*) malloc(sizeof(struct socket_msg)+ bufsize);
    RETURN_VAL_IF(msg == NULL, pi_log_e, NULL);

    msg->rtid    = 0x00;   //合入多app后修改
    msg->main    = main;
    msg->sub     = sub;
    msg->respond = respond;
    msg->size    = bufsize;
    if ( bufsize > 0 && buf != NULL )
    {
        memcpy(msg->data, buf, bufsize);
    }

    return msg;
}

#if 1
static int32_t check_sock_connected(int32_t sockfd)
{
    struct tcp_info info;
    socklen_t       len;
    int32_t         res;

    RETURN_VAL_IF(sockfd <= 0, pi_log_d, -1);

    len = (socklen_t)sizeof(info);
    getsockopt(sockfd, IPPROTO_TCP, TCP_INFO, &info, &len);
    if ( info.tcpi_state != TCP_ESTABLISHED )
    {
        pi_log_d("socket(%d) state(%d) disconnect\n", sockfd, info.tcpi_state);
        pi_close(sockfd);
        res = -1;
    }
    else
    {
        res = 0;
    }

    return res;
}

static void parse_socket_packet(struct socket_msg* info)
{
    PEDKAPI_HANDLER_FUNC  func = NULL;
    struct module_table*  node;
    struct list_head*     pos;
    struct list_head*     n;

    pi_list_for_each_safe(pos, n, &s_reg_list)
    {
        node = pi_list_entry(pos, struct module_table, list);
        if ( node != NULL && node->func != NULL && node->main == info->main )
        {
            func = node->func;
            break;
        }
    }

    if ( func != NULL )
    {
        func(info->sub, info->respond, info->size, info->data, node->arg);
        pi_log_d("parse socket packet success\n");
    }
    else
    {
        pi_log_e("no search module(%d)\n", (int32_t)info->main);
    }
    return;
}

static void error_handing(char* message)
{
    printf("%s\n",message);
    message_queue_clean();
    exit(1);
}

static void* server_thread(void* arg)
{
    int serv_sock; // 服务端socket  mfp进程
    int clnt_sock;
    struct sockaddr_in serv_addr;
    struct sockaddr_in clnt_addr;
    struct protocol_packet *sendMsg = NULL;
    int reamin_len = 0;

    serv_sock = socket(AF_INET, SOCK_STREAM, 0);
    if (serv_sock == -1)
    {
        error_handing("socket() error");
    }
    bzero(&serv_addr, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(SERVER_ADDR);
    serv_addr.sin_port =  htons(SERVER_PORT);
    printf("strat bind\n");
    if(bind(serv_sock, (struct sockaddr*)&serv_addr, sizeof(serv_addr)) == -1)
    {
        error_handing("bind() error");
    }
    printf("strat listen\n");
    if(listen(serv_sock, 5) == -1)
    {
        error_handing("listen() error");
    }

    socklen_t clnt_addr_len = sizeof(clnt_addr);
_accept:
    clnt_sock = accept(serv_sock, (struct sockaddr*)&clnt_addr, &clnt_addr_len);
    if (clnt_sock == -1)
    {
        error_handing("accept() error");
    }
    printf("accept success\n");
    //fcntl(clnt_sock, F_SETFL, fcntl(clnt_sock, F_GETFL, 0) | O_NOBLOCK);
    fcntl(clnt_sock, F_SETFL, O_NONBLOCK);

    while(1)//send pesf_c msg
    {
        struct protocol_packet recvStruct;
        memset(&recvStruct, 0, sizeof(struct protocol_packet));

        if ( (sendMsg = message_queue_pop()) != NULL )
        {
            if(write(clnt_sock, sendMsg, sizeof(struct protocol_packet)+(((sendMsg->len_high << 8) & 0xFF00) | (sendMsg->len_low & 0xFF))) > 0)
            {
                free(sendMsg);
                sendMsg = NULL;
            }
        }
        reamin_len = read(clnt_sock, &recvStruct, sizeof(struct protocol_packet));
        //printf("reamin_len=%d\n",reamin_len);
        if(reamin_len > 0 && reamin_len == sizeof(struct protocol_packet)) //parser
        {
            int msgLen = ((recvStruct.len_high << 8) & 0xFF00) | (recvStruct.len_low & 0xFF);
            //区分命令
            switch(recvStruct.cmd)
            {
                case CMD_PING://ping
                    //ack ping pesf rutime
                    pedk_mgr_send_cmd_to_runenv(CMD_PING, NULL, 0);
                    if(msgLen != 0){
                        pi_log_d("ping cmd error\n");
                    }
                    app_init();
                    break;
                case CMD_APP_START_RETURN://Runtime to Printer
                case CMD_APP_END_RETURN://Runtime to Printer
                case CMD_APP_PAUSE_RETURN://Runtime to Printer
                case CMD_APP_CONTINUE_RETURN://Runtime to Printer
                    //result
                    if(msgLen != 2 && msgLen != 1){
                        error_handing("CMD_APP_END_RETURN cmd error");
                    }
                    unsigned char res[2];//Byte1:rtid; Byte2:result
                    if( read(clnt_sock, res, msgLen) != msgLen)
                    {
                        error_handing("CMD_APP_END_RETURN recv error");
                    }

                    break;
                case CMD_APP_TO_PRINTER://Runtime to Printer
                    {
                        //组合成一个完整的包
                        pi_log_d("server recv message,msgLen:%d\n",msgLen);
                        struct socket_msg *recvInfo = (struct socket_msg *)malloc(msgLen);
                        if(recvInfo == NULL)
                        {
                            error_handing("malloc() error");
                        }
                        if(read(clnt_sock, recvInfo, msgLen) != msgLen)
                        {
                            error_handing("struct data recv error");
                        }
                        parse_socket_packet(recvInfo);

                        if(recvInfo)
                        {
                            free(recvInfo);
                            recvInfo = NULL;
                        }
                    }
                    break;
                case CMD_RETURN_PROPERTY_LIST://Runtime to Printer
                    break;
                case CMD_RETURN_APP_PROPERTY://Runtime to Printer
                    printf("*****server******recvStruct.cmd %u\n",recvStruct.cmd);
                    break;
                case CMD_APP_START://Printer to Runtime
                case CMD_APP_END://Printer to Runtime
                case CMD_APP_PAUSE://Printer to Runtime
                case CMD_APP_CONTINUE://Printer to Runtime
                case CMD_PRINTER_TO_APP://Printer to Runtime
                case CMD_GET_PROPERTY_LIST://Printer to Runtime
                case CMD_GET_APP_PROPERTY://Printer to Runtime
                    printf("recvStruct.cmd %u\n",recvStruct.cmd);
                    break;
                default:
                    break;
            }
        }
        if ( check_sock_connected(clnt_sock) == -1 )
        {
            if(sendMsg != NULL)
            {
                free(sendMsg);
                sendMsg = NULL;
            }
            goto _accept;
        }


        usleep(10000);
    }

    close(serv_sock);
    close(clnt_sock);

    return NULL;
}
#endif

int32_t pedk_mgr_register_handler(MAIN_MSG_E module, PEDKAPI_HANDLER_FUNC handler, void* ctx)
{
    struct module_table* node;

    node = (struct module_table *)pi_zalloc(sizeof(struct module_table));
    RETURN_VAL_IF(node == NULL, pi_log_e, -1);

    node->main = module;
    node->func = handler;
    node->arg  = ctx;
    MUTEX_PROCESS(pi_list_add_tail(&node->list, &s_reg_list), s_reg_lock);

    return 0;
}

void pedk_mgr_unregister_handler(MAIN_MSG_E module)
{
    struct module_table* node;
    struct list_head*    pos;
    struct list_head*    n;

    pi_list_for_each_safe(pos, n, &s_reg_list)
    {
        node = pi_list_entry(pos, struct module_table, list);
        if ( node != NULL && node->main == module )
        {
            MUTEX_PROCESS(pi_list_del_entry(&node->list), s_reg_lock);
            pi_free(node);
        }
    }
}

int32_t pedk_mgr_send_msg_to_runenv(MAIN_MSG_E main, SUB_MSG_E sub, int32_t respond, uint8_t* buf, int32_t bufsize)
{
    struct socket_msg*  msg;
    int32_t             ret;

    msg = message_packing(main, sub, respond, buf, bufsize);
    RETURN_VAL_IF(msg == NULL, pi_log_e, -1);

    ret = message_queue_push(protocol_packing(CMD_PRINTER_TO_APP, (uint8_t *)msg, sizeof(struct socket_msg) + bufsize));
    pi_free(msg);

    return ret;
}

int32_t pedk_mgr_send_cmd_to_runenv(uint8_t cmd, uint8_t* buf, uint16_t len)
{
    return message_queue_push(protocol_packing(cmd, buf, len));
}

static void pedk_mgr_event_callback(const EVT_MSG_S* msg, void* ctx)
{
    uint32_t module_id = msg->module_id;
    uint32_t event_type = msg->event_type;
    int ret = 0;
    pi_log_d("pedk mgr get module: %u request event type: %u \n", module_id, event_type);
    switch(event_type)
    {
        case EVT_TYPE_USBHOST_GET_ICCARD_INFO:
            pi_log_d("ICCARD msg->data:%s, lenth:%d\n",msg->data, msg->data_length);
            pedk_mgr_send_msg_to_runenv(MSG_MOUDLE_USB_ICCARD,MSG_GET_ICCAED_INFO,0,msg->data, msg->data_length);

            break;
        case EVT_TYPE_SYSTEMSTATUS_UPDATE:
            status_event_callback(msg->data, msg->data_length);
            break;
        default:
            pi_log_d("unknown event type: %u\n", event_type);
            break;
    }
}

EVT_MGR_CLI_S* pedk_mgr_get_event_client( void )
{
    return g_pedk_cli_ptr;
}

EVT_MGR_CLI_S * pedk_mgr_event_register(void)
{
    EVT_MGR_CLI_S * cli_ptr = pi_event_mgr_create_client(EVT_MODULE_PEDK_MGR, pedk_mgr_event_callback, NULL, NULL);
    if(cli_ptr == NULL)
    {
        return NULL;
    }

    uint32_t modify_event_array[] = {
        EVT_TYPE_USBHOST_GET_ICCARD_INFO,
        EVT_TYPE_SYSTEMSTATUS_UPDATE,
        };
    int32_t event_count = sizeof(modify_event_array) / sizeof(modify_event_array[0]);

    pi_event_mgr_register(cli_ptr, modify_event_array, event_count);

    return cli_ptr;
}

int32_t pedk_mgr_prolog(void)
{
    int32_t ec = 1;

    //app_anager init
    app_manager_init();

    do
    {
        s_serv_tid = pi_thread_create(server_thread, PI_LARGE_STACK, NULL, PI_MEDIUM_PRIORITY, NULL, "server_thread");
        BREAK_IF(s_serv_tid == INVALIDTHREAD, pi_log_w);

        g_pedk_cli_ptr = pedk_mgr_event_register();
        pi_log_d("pedk mgr event register ... \n");
        if( g_pedk_cli_ptr == NULL )
        {
            pi_log_e("pedk mgr event init error\n");
        }

        ec = 0;
    }
    while ( 0 );

    if ( ec != 0 )
    {
        pi_log_e("pedk manager initialize failed(%d)", ec);
        pedk_mgr_epilog();
    }
    return ec;
}

void pedk_mgr_epilog(void)
{
    if ( s_serv_tid != INVALIDTHREAD )
    {
        pi_thread_destroy(s_serv_tid);
        s_serv_tid = INVALIDTHREAD;
    }
    if ( s_rfid_tid != INVALIDTHREAD )
    {
        pi_thread_destroy(s_rfid_tid);
        s_rfid_tid = INVALIDTHREAD;
    }
    // 释放资源，杀pesf.elf? 是否影响固件升级?
    message_queue_clean();
}

