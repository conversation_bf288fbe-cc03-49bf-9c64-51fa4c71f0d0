/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file acl_attribute.h
 * @addtogroup acl
 * @{
 * <AUTHOR>
 * @date 2023-4-25
 * @brief ACL public header file
 */

#ifndef ACL_ATRRIBUTE_H
#define ACL_ATRRIBUTE_H

#include "acl.h"
#include "qio/qio_general.h"

#define MAX_STRING_KEY_LEN       128
#define MAX_STRING_VALUE_LEN     128
#define MAX_PARAM_NUMBER         10

#define     ATTR_TYPE_NULL               (uint8_t)0
#define     ATTR_TYPE_UINT32_VALUE       (uint8_t)1
#define     ATTR_TYPE_UINT32_INDEX       (uint8_t)2
#define     ATTR_TYPE_STRING_VALUE       (uint8_t)3
#define     ATTR_TYPE_STRING_INDEX       (uint8_t)4

typedef struct acl_attr_value
{
    char*       attr_key;
    uint32_t    attr_idx;
    uint32_t    attr_type;
    void*       func_set;
    void*       func_get;
    char*       value;
    char        value_old[MAX_STRING_VALUE_LEN];
}ACL_ATTR_VALUE_S;


typedef int32_t (*SET_UINT32_VALUE) (uint32_t value);

typedef int32_t (*GET_UINT32_VALUE) (uint32_t* value);

typedef int32_t (*SET_UINT32_INDEX) (uint32_t index, uint32_t value);

typedef int32_t (*GET_UINT32_INDEX) (uint32_t index, uint32_t* value);

typedef int32_t (*SET_STRING_VALUE) (char* value, uint32_t len);

typedef int32_t (*GET_STRING_VALUE) (char* value, uint32_t len);

typedef int32_t (*SET_STRING_INDEX) (uint32_t index, char* value, uint32_t len);

typedef int32_t (*GET_STRING_INDEX) (uint32_t index, char* value, uint32_t len);

#if 1
int32_t acl_attribute_uint32_value_register(char* key_str,
                                                SET_UINT32_VALUE func_set,
                                                GET_UINT32_VALUE func_get);

int32_t acl_attribute_uint32_index_register(char* key_str,
                                                SET_UINT32_INDEX func_set,
                                                GET_UINT32_INDEX func_get);

int32_t acl_attribute_string_value_register(char* key_str,
                                                SET_STRING_VALUE func_set,
                                                GET_STRING_VALUE func_get);

int32_t acl_attribute_string_index_register(char* key_str,
                                                SET_STRING_INDEX func_set,
                                                GET_STRING_INDEX func_get);

#endif

/*************************************************************************
Description  : register acl attribute value
Input    Parm:
         ker_str:    string for OID. e.g:"OID_DEMO_UINT32_VALUE"
         attr_type:  attribute type. e.g: ATTR_TYPE_UINT32_VALUE
         func_set:   callback function for setting attribute value.
                     e.g: demo_set_uint32_value
         func_get:   callback function for obtaining attribute value.
                     e.g: demo_get_uint32_value
note: The callback function must follow:

      1.ATTR_TYPE_UINT32_VALUE:
      int32_t (*SET_UINT32_VALUE) (uint32_t value);
      int32_t (*GET_UINT32_VALUE) (uint32_t* value);

      2.ATTR_TYPE_UINT32_INDEX:
      int32_t (*SET_UINT32_INDEX) (uint32_t index, uint32_t value);
      int32_t (*GET_UINT32_INDEX) (uint32_t index, uint32_t* value);

      3.ATTR_TYPE_STRING_VALUE:
      int32_t (*SET_STRING_VALUE) (char* value, uint32_t len);
      int32_t (*GET_STRING_VALUE) (char* value, uint32_t len);

      4.ATTR_TYPE_STRING_INDEX:
      int32_t (*SET_STRING_INDEX) (uint32_t index, char* value, uint32_t len);
      int32_t (*GET_STRING_INDEX) (uint32_t index, char* value, uint32_t len);

Return   Parm: int32_t: PARSER_ERROR\PARSER_SUCCESS
Create Author: yikaiyong ,24/11/2015
*************************************************************************/
int32_t acl_attribute_value_register( char* key_str, uint8_t attr_type,
                                      void *func_set, void *func_get );
int32_t acl_attribute_init( void );

int32_t acl_attribute_get_callback(char* attr_key, ACL_ATTR_VALUE_S* attr_info);

int acl_set_printer_attributes(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data);

int acl_get_printer_attributes(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data);

#endif /* ACL_ATRRIBUTE_H */
/**
 *@}
 */

