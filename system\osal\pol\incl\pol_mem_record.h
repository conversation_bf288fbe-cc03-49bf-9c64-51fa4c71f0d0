/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_mem_record.h
 * @addtogroup pol
 * @{
 * @addtogroup mem_debug
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal mem record interface set
 */
#ifndef __POL_MEM_RECORD_H__
#define __POL_MEM_RECORD_H__

#include "pol/pol_types.h"
#include <pol/pol_define.h>

PT_BEGIN_DECLS

#if defined(CONFIG_POL_MEM_RECORD)

#define MEM_OP_ALLOC		"alloc"
#define MEM_OP_REALLOC		"realloc"

#define MEM_OP_FREE			"free"
#define MEM_OP_READONLY		"read"
#define MEM_OP_WRITE		"write"
#define MEM_OP_RW		    "rw"
#define MEM_OP_MMAP		    "mmap"
#define MEM_OP_MUNMAP		"munmap"

#define MODULE_MEM_RECORD 		"MEM_RECORD"

#define pol_mem_record_info(fmt,args...) 		pol_info(MODULE_MEM_RECORD,fmt,##args)



#define _mem_record(op,s,e,lwp,file,line)        pol_mem_record_info("%8s %14p %14p %10d %16s %d\n",op,s,e,lwp,file,line)

#define mem_alloc_record(start,end,lwp,file,line)   {if(start) {_mem_record(MEM_OP_ALLOC,start,end,lwp,file,line);}}
#define mem_realloc_record(start,end,lwp,file,line) {if(start) {_mem_record(MEM_OP_REALLOC,start,end,lwp,file,line);}}
#define mem_free_record(start,lwp,file,line)   		{if(start) {_mem_record(MEM_OP_FREE,start,NULL,lwp,file,line);}}
#define mem_read_record(start,end,lwp,file,line)   	_mem_record(MEM_OP_READONLY,start,end,lwp,file,line)
#define mem_write_record(start,end,lwp,file,line)   _mem_record(MEM_OP_WRITE,start,end,lwp,file,line)
#define mem_rw_record(start,end,lwp,file,line)   	_mem_record(MEM_OP_RW,start,end,lwp,file,line)
#define mem_mmap_record(start,end,lwp,file,line)   	{if((start)!=(void*)(-1)) {_mem_record(MEM_OP_MMAP,start,end,lwp,file,line);}}
#define mem_munmap_record(start,end,lwp,file,line)   _mem_record(MEM_OP_MUNMAP,start,end,lwp,file,line)



#else

#define mem_alloc_record(start,end,lwp,file,line)
#define mem_realloc_record(start,end,lwp,file,line)
#define mem_free_record(start,lwp,file,line)
#define mem_read_record(start,end,lwp,file,line)  	
#define mem_write_record(start,end,lwp,file,line)
#define mem_rw_record(start,end,lwp,file,line)
#define mem_mmap_record(start,end,lwp,file,line)
#define mem_munmap_record(start,end,lwp,file,line)
#endif

PT_END_DECLS

#endif
/**
 *@}
 */
