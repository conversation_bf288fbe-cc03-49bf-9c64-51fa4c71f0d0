// Copyright (c) 2009-2014  Seine Technology. All Rights Reserved
// ******************************************************************************
//
//                        Seine Technology Confidential
// ******************************************************************************
// Function Description
// ------------------------------------------------------------------------------
// public data proc
// ******************************************************************************
// Revising Records
// ------------------------------------------------------------------------------
// Number: 1
// Author: j
// Date: 31/10/2014
// Reason:create file
// ******************************************************************************
#ifndef __PUBLIC_DATA_PROC_H__
#define __PUBLIC_DATA_PROC_H__

#include <stdio.h>
#include <string.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdint.h>

#include "memmgr/memmgr.h"
#include "pol/pol_threads.h"

#include "qio/qio_general.h"
#include "pol/pol_list.h"
#include "utilities/event_observer.h"

#include "scan_var.h"
#include "job_manager.h"
#include "copy_var.h"



#ifdef __cplusplus
extern "C" {
#endif

#define PRINT_DEBUG_ON                  5
#define PRINT_DEBUG_OFF                 10
#define PRINT_DEBUG_WARNING             2

//#define SCAN_DEBUG_ON                   -106
#define SCAN_DEBUG_ON                   5
#define SCAN_DEBUG_OFF                  10
#define SCAN_DEBUG_WARNING              3

//print string about job owner or time
#define OWNER_DESCRIPTION_MAX           32
#define TIME_DESCRIPTION_MAX            16

//print data check word or magic word,if this word is not matched,maybe data has been corrupted
#define PUBLIC_DATA_COOKIE              0xA5A5A5A5

//image process description
#define IMAGE_PROCESS_STEPS_DESCRIPTION 2048

/* mv6220 front,back rotate sequence */
#define MV6220_SCAN_ROTATE_SEQ_MAX      48

//set or check magic word
#define  PUBLIC_COOKIE_SET(p)           ( p->cookie = PUBLIC_DATA_COOKIE )
#define  PUBLIC_COOKIE_VALID(p)         ( p->cookie == PUBLIC_DATA_COOKIE ? 1:0 )

//#define  ADDRESS_LENGTH_MAX 64
#define  QIO_NOT_SUPPORT 0x12345678

//print date xor on
#if CONFIG_PRINT_JOBDATA_XOR
#define XOR_ONOFF       1       // Data xor 0-OFF 1-ON      // ZhuRan, 20230515, remove XOR function before baikal project's alpha2 step
#else
#define XOR_ONOFF       0       // Data xor 0-OFF 1-ON      // ZhuRan, 20230515, remove XOR function before baikal project's alpha2 step
#endif

#define XOR_BYTE        0xA5    // 数据异或操作字节

//panel use for a scan job request
typedef enum
{
    UNIT_INCH,
    UNIT_MM,
    UNIT_PIXEL,
    UNIT_MAX = 0xFFFFFFFF

}PAPER_UNIT_E;

typedef enum
{
    PM_EVENT_READY = 0,
    PM_EVENT_SLEEP  = 1,
    PM_EVENT_DEEPSLEEP  = 2,
    PM_EVENT_WAKEUP = 3,
    PM_EVENT_DEEP_WAKEUP = 4,
    PM_EVENT_MAX = 0xFFFFFFFF
} POWER_MANAGER_EVENT_E;

typedef enum
{
    IMAGE_CHANNEL_DEFAULT = 0,
    IMAGE_CHANNEL_FRONT = 0,
    IMAGE_CHANNEL_BACK = 1,
    IMAGE_CHANNEL_DUAL = 2,
    IMAGE_CHANNEL_MAX = 0xFFFFFFFF,
}IMAGE_CHANNEL_TYPE_E;

typedef enum
{
    TONER_SAVE_OFF = 0,
    TONER_SAVE_ON,
    TONER_SAVE_MAX_E
}TONER_SAVE_E;

//a small job which store in memory
//print every pages copy by copy or all pages
//for example,job has 4 pages and print 2 copies
//collate off:1,1;2,2;3,3;4,4
//collate on :1,2,3,4;1,2,3,4
//bit0,job multicopies on or off
//bit1,job manual pages come then make up to a full job to print
typedef enum
{
    JOB_COLLATE_OFF                 = 0,
    JOB_COLLATE_ON_COPIES           = 0x00000001,               ///< copies,order:1,2,3,4...;1,2,3,4...
    JOB_COLLATE_ON_MANUAL_DUPLEX    = 0x00000002,               ///< manual duplex receiving,order:1,3,5,7,9;10,8,6,4,2
    JOB_COLLATE_ON_REVERSE          = 0x00000004,               ///< reverse print,order:10,9,8,7,6,5,4,3,2,1
    JOB_COLLATE_ON_PRINT_DUPLEX     = 0x00000008,               ///< need collate to duplex print
    JOB_COLLATE_ON_COPY_DUPLEX      = 0x00000010,               ///< need collate to duplex print
    JOB_COLLATE_INVALID,
    JOB_DATA_SCOLLATE_MAX = 0xFFFFFFFF

}JOB_COLLATE_E;


//sheet process state,its order like this
//single      print:sheet initial->front start->front done->sheet done
//auto duplex print:sheet initial->back start->back done->front start->front done->sheet done
//print mgr,video mgr,engine interface must follow this state
typedef enum
{
    SHEET_STATE_INITIAL = 0,
    SHEET_STATE_BACK_START,
    SHEET_STATE_BACK_DONE,
    SHEET_STATE_FRONT_START,
    SHEET_STATE_FRONT_DONE,
    SHEET_STATE_DUPLEX_START,
    SHEET_STATE_DUPLEX_DONE,
    SHEET_STATE_DONE,
    SHEET_STATE_INVALID,                  ///< please keep it in the end of the enum --- invalid data
	SHEET_STATE_MAX =  0xFFFFFFFF
}SHEET_STATE_E;

//print job (PJL) supports various color modes including color (CMYK), black and white (MONO), red and black（RED_BLACK）(To be expanded)，and automatic color (COLOR_AUTO)
typedef enum
{
    JOB_COLOR_MODE_INVALID      = 0,            ///< invaild mode
    JOB_COLOR_MODE_MONO         = 1,            ///< mono mode
    JOB_COLOR_MODE_COLOR        = 2,            ///< color mode
    JOB_COLOR_MODE_RED_BLACK    = 3,            ///< red&black mode
    JOB_COLOR_MODE_AUTO         = 4,            ///< auto color mode
    JOB_COLOR_MODE_MAX          = 0xFFFFFFFF
}JOB_COLOR_MODE_E;

/*typedef struct tag_job_status_data*/
//{
    //EVENT_SUBJECT       *job_start_subject;
    //EVENT_SUBJECT       *job_done_subject;
    //EVENT_SUBJECT       *job_error_subject;
    //EVENT_SUBJECT       *job_cancel_subject;
    //EVENT_SUBJECT       *page_receive_subject;
    //EVENT_SUBJECT       *page_done_subject;
    //EVENT_SUBJECT       *page_count_subject;
//}JOB_STATUS_EVENT;

/*scan job type enum*/
typedef enum
{
    SCAN_JOB_TYPE_NONE        = 0,           ///< scan job type invaild param
    SCAN_JOB_TYPE_HOST_PULL   = 1,           ///< scan pull by pc
    SCAN_JOB_TYPE_HOST_PUSH   = 2,           ///< scan push by panel
    SCAN_JOB_TYPE_FTP         = 3,           ///< scan to ftp
    SCAN_JOB_TYPE_SMB         = 4,           ///< scan to ftp
    SCAN_JOB_TYPE_EMAIL       = 5,           ///< scan to email
    SCAN_JOB_TYPE_U_DISK      = 6,           ///< scan to udisk
    SCAN_JOB_TYPE_COPY        = 7,           ///< scan to copy
    SCAN_JOB_TYPE_FAX         = 8,           ///< scan to fax
    SCAN_JOB_TYPE_AIRSCAN     = 9,           ///< air scan
    SCAN_JOB_TYPE_WSDSCAN     = 10,          ///< wsd scan
#if CONFIG_SDK_PEDK
    SCAN_JOB_TYPE_HTTP        = 11,          ///< http scan
    SCAN_JOB_TYPE_APP         = 12,          ///< app scan
#endif
    SCAN_JOB_TYPE_MAX         = 0xFFFFFFFF

}SCAN_JOB_TYPE_E;

/**
 * @brief scan send router
*/
typedef enum
{
    SCAN_TO_INVALID = 0,
    SCAN_TO_PC ,        ///< scan to pc
    SCAN_TO_FTP,        ///< scan to ftp
    SCAN_TO_EMAIL,      ///< scan to email
    SCAN_TO_SMB,        ///< scan to smb
    SCAN_TO_U_DISK,     ///< scan to udisk
    SCAN_TO_WSD,
#if CONFIG_SDK_PEDK
    SCAN_TO_HTTP,
	SCAN_TO_APP,
#endif
    SCAN_TO_AIRSCAN,
    SCAN_TO_FAX,
    SCAN_TO_MAX

} SCAN_SEND_ROUTER_E;

/*scan paper source enum*/
typedef enum
{
    SCAN_MODE_AUTO = 0,             ///< auto selection flat glass or glass
    SCAN_MODE_FB   = 1,             ///< paper source by flat glass
    SCAN_MODE_ADF  = 2,             ///< paper source by single ADF
    SCAN_MODE_MADF = 3,             ///< paper source by manual ADF
    SCAN_MODE_DADF = 4,             ///< paper source by double DADF
    SCAN_MODE_RADF = 5,             ///< paper source by RADF
    //SCAN_MODE_DADF_R =6,          ///< ADF
    SCAN_MODE_SEP  = 7,             ///< one side separation
    SCAN_MODE_DSEP = 8,             ///< duplex separation
    SCAN_MODE_MAX = 0xFFFFFFFF
}SCAN_MODE_E;

/*scan image quality enum*/
typedef enum
{
    QUALITY_NONE_AUTO   = 0,         ///< scan image quality auto
    QUALITY_TXT         = 1,         ///< scan image quality by text
    QUALITY_MIXED       = 2,         ///< scan image quality by text mixed picture
    QUALITY_PICTURE     = 3,         ///< scan image quality by picture
    QUALITY_MAX         = 0xFFFFFFFF

}QUALITY_MODE_E;

/*scan color type enum*/
typedef enum
{
    COLOR_MONO = 0,     ///< color by mono
    COLOR_RGB  = 1,     ///< color by rgb
    COLOR_GRAY = 2,     ///< color by gray
    COLOR_CMYK = 3,     ///< color by cmyk
    COLOR_RK   = 4,     ///< color by red&black
    COLOR_MAX  = 0xFFFFFFFF
}COLOR_MODE_E;

/*scan resolution by dpi enum*/
/*typedef enum
{
    DATA_TYPE_ORI,
    DATA_TYPE_JPEG,
    DATA_TYPE_JBIG,
    DATA_TYPE_CMYK,
    DATA_TYPE_MAX = 0xFFFFFF
}DATA_TYPE_E;*/

/*resolution by dpi enum*/
typedef enum
{
    DPI_75     = 75,        ///< 75 dpi
    DPI_150    = 150,       ///< 150 dpi
    DPI_200    = 200,       ///< 200 dpi
    DPI_300    = 300,       ///< 300 dpi
    DPI_600    = 600,       ///< 600 dpi
    DPI_1200   = 1200,      ///< 1200 dpi
    DPI_2400CQ = 2400,
    DPI_MAX    = 0xFFFFFFFF
}RESOLUTION_E;

/*scan image data type enum*/
typedef enum
{
    COLOR_FORMAT_UNSPECIFIED        = 0,

    COLOR_FORMAT_RGB                = 1,            ///< image is RGB
    COLOR_FORMAT_CMYK               = 2,            ///< image is CMYK
    COLOR_FORMAT_YCbCr              = 4,            ///< image is YCC
    COLOR_FORMAT_Y                  = 6,            ///< image is contone Luminance (0=black)
    COLOR_FORMAT_Mono               = 8,            ///< alias for cfY

    COLOR_FORMAT_PLANAR             = 0x100,        ///< image is planar
    COLOR_FORMAT_PIXELINTERLEAVED   = 0x200,        ///< image is pixel-by-pixel interleaved
    COLOR_FORMAT_LINEINTERLEAVED    = 0x400,        ///< image has one of line of each color component
    COLOR_FORMAT_COLORORDER         = 0x800,        ///< image is B-G-R order (on) if it's not planar, default is R-G-B (off)

    COLOR_FORMAT_RGBi               = (COLOR_FORMAT_PIXELINTERLEAVED + COLOR_FORMAT_RGB),
    COLOR_FORMAT_RGBl               = (COLOR_FORMAT_LINEINTERLEAVED + COLOR_FORMAT_RGB),
    COLOR_FORMAT_RGBp               = (COLOR_FORMAT_PLANAR + COLOR_FORMAT_RGB),
    COLOR_FORMAT_YCCi               = (COLOR_FORMAT_PIXELINTERLEAVED + COLOR_FORMAT_YCbCr),
    COLOR_FORMAT_YCCp               = (COLOR_FORMAT_PLANAR + COLOR_FORMAT_YCbCr),
    COLOR_FORMAT_CMYKi              = (COLOR_FORMAT_PIXELINTERLEAVED + COLOR_FORMAT_CMYK),
    COLOR_FORMAT_CMYKp              = (COLOR_FORMAT_PLANAR + COLOR_FORMAT_CMYK),

    COLOR_FORMAT_COMPRESSED         = 0x1000,       ///< image is compressed, bands have compression headers
    COLOR_FORMAT_BIGENDIAN          = 0x2000,       ///< image is big-endian, default is litte endian
    COLOR_FORMAT_MIRRORED           = 0x4000,       ///< image is oriented right-to-left (mirrored), default is left to right
    COLOR_FORMAT_BITMIRRORED        = 0x8000,       ///< default is bit 31 is left bit, bit 0 is right bit

    COLOR_FORMAT_XRGB               = 0x8001,
    COLOR_FORMAT_RED                = 0x8002,
    COLOR_FORMAT_GREEN              = 0x8003,
    COLOR_FORMAT_BLUE               = 0x8004,
    COLOR_FORMAT_MONO_HALF_PACK_1BPP= 0x8005,
    COLOR_FORMAT_MONO_HALF_PACK_2BPP= 0x8006,
    COLOR_FORMAT_MONO_HALF_PACK_4BPP= 0x8007,
    COLOR_FORMAT_BRG_INTERLACED     = 0x8008,
    COLOR_FORMAT_RGBXPACKED         = 0x8009,
    COLOR_FORMAT_BRG_INTERLACED_MIRRORED = 0x800A,
    COLOR_FORMAT_MONO_MIRRORED      = 0x800B,
    COLOR_FORMAT_JPEG_COMPRESSED    = 0x800C,

    COLOR_FORMAT_MAX                = 0xFFFFFFFF

}IMAGE_COLORFORMAT_E;

/* file type enum*/
typedef enum
{
    FILE_TIFF           = 0,                  ///< TIFF format
    FILE_JPEG           = 1,                  ///< JPEG format
    FILE_PDF            = 2,                  ///< PDF format
    FILE_PDF_NEXT_PAGE  = 3,
    FILE_PDF_TMP        = 4,
    FILE_BMP            = 5,                  ///< BMP format
    FILE_OFD            = 6,                  ///< OFD format
    FILE_OFD_NEXT_PAGE,
    FILE_OFD_TMP,

    FILE_XPS,                                 ///< XPS format
    FILE_XPS_NEXT_PAGE,
    FILE_XPS_TMP,

    FILE_ORI_PPM        = 20,                ///< color ppm, gray pgm

    FILE_MAX            = 0xFFFFFFFF
}FILE_TYPE_E;

typedef struct
{
    uint32_t                colorfring;
    uint32_t                uniformcoefficent;
    uint32_t                sharpness;
    uint32_t                brightness;
    uint32_t                contrast;
    uint32_t                hue;
    uint32_t                backgroundremove;
    uint32_t                saturation;
    uint32_t                colorbalance_c;
    uint32_t                colorbalance_y;
    uint32_t                colorbalance_m;
    uint32_t                colorbalance_k;
    uint32_t                gamma;
}SCAN_IMAGE_QUALITY_S;

typedef struct
{
    uint32_t     left;
    uint32_t     right;
    uint32_t     bottom;
    uint32_t     top;
}COPY_EDGE_CLEAN_S;

/*scan engine control struct  param for job data*/
typedef struct
{

    int         scan_speed_ppm;                                 // for mv6220 scan speed control

    uint32_t    use_rotate;
    uint32_t    is_mirror;
    int         rotate_sequence[MV6220_SCAN_ROTATE_SEQ_MAX];    // for mv6220 front,back rotate

    uint32_t    use_pip;
    uint32_t    pip_scale_x;
    uint32_t    pip_scale_y;
    uint32_t    pip_mode;
    uint32_t    pip_out_bpp;

    uint32_t    use_id_correction;// id copy correction
    uint32_t    use_jpeg_encode;
    uint32_t    use_jbig_encode;

    uint8_t		use_book_discern; //(1:use 0:not use)

    //removel color
    uint8_t     use_removel_color;
    uint8_t     remove_color_plane;// (1:R 1->remove,2:G 1->remove,3:B 1->remove)

    //edge_clean
    uint8_t     use_edge_clean;
    COPY_EDGE_CLEAN_S edge_clean; ///< front page edge clean info
    uint8_t     use_colour_check; //(1:use 0:not use)
    uint8_t     use_oly_img;      //(1:use 0:not use)
    uint8_t     mid_edge_clean;      ///< rotate or any processer need focuss
    uint8_t     use_id_scan_mode;    ///<
}SCAN_ENGINE_PARAM_S;

/*print mode,single or duplex*/
typedef enum
{
    PRINT_MODE_SINGLE = 0,              ///< single print
    PRINT_MODE_AUTO_DUPLEX,             ///< auto double print
    PRINT_MODE_MANUAL_DUPLEX,           ///< manual double print
    PRINT_MODE_INVALID,                 ///< please keep it in the end of the enum --- invalid data
    PRINT_MODE_MAX = 0xFFFFFFFF
}PRINT_MODE_E;

//print density
typedef enum
{
    PRINT_DENSITY_ECO = 0,
    PRINT_DENSITY_THINNER = 1,
    PRINT_DENSITY_THIN,
    PRINT_DENSITY_NORMAL,
    PRINT_DENSITY_THICK,
    PRINT_DENSITY_THICKER,

    PRINT_DENSITY_MAX = 0xFFFFFFFF
}PRINT_DENSITY_E;

/* Begin: 20191118, modified by Zhu.Ran, new ruler for A3 project */
typedef enum
{
    TRAY_INPUT_STANDARD = 0x00,         ///< 00H, 1st Input Source
    TRAY_INPUT_2 = 0x01,                ///< 01H, 2nd Input Source
    TRAY_INPUT_3 = 0x02,                ///< 02H, 3rd Input Source
    TRAY_INPUT_4 = 0x03,                ///< 03H, 4th Input Source
    TRAY_INPUT_External_LCT_IN = 0x04,  ///< 04H, LCT(External LCT) Input Source
    TRAY_INPUT_External_LCT_OUT = 0x05, ///< 05H, LCT(External LCT) Output Source
    TRAY_INPUT_MULTIFUNCTION = 0x06,    ///< 06H, Multipe Manual Feed Source
    TRAY_INPUT_REFEED = 0x07,           ///< 07H, Re-feed Input Source
    TRAY_INPUT_AUTO = 0x64,             ///< 64H, Auto Input Source. We should find the true tray when tray_input value is "0x64".
    TRAY_INPUT_INVALID,                 ///< please keep it in the end of the enum --- invalid data

    TRAY_INPUT_MAX = 0xFFFFFFFF

}TRAY_INPUT_E;

/* staple number*/
typedef enum
{
    STAPLE_NUMBER_NONE = 0x00,
    STAPLE_NUMBER_ONE  = 0x01,
    STAPLE_NUMBER_TWO  = 0x02,

    STAPLE_NUMBER_MAX = 0xFFFFFFFF
}STAPLE_NUMBER_E;


/*the staple config enum*/
typedef enum
{
    STAPLE_MODE_OFF = 0x00,                                                                     ///< staple invaild param
    STAPLE_MODE_LEFT_AUTO,                                                                      ///< Binding on the left
    STAPLE_MODE_RIGHT_AUTO,                                                                     ///< Binding on the right
    STAPLE_MODE_RIGHT_ZREO,                                                                     ///< Binding zreo on the right
    STAPLE_MODE_LEFT_ZREO,                                                                      ///< Binding zreo on the left
    STAPLE_MODE_DOUBLE,                                                                         ///< Double binding
    STAPLE_MODE_MIDDLE,                                                                         ///< inter binding
    STAPLE_MODE_USE_PRINTER_CONFIG,                                                             ///< use printer's config
    STAPLE_MODE_TOP_LEFT_AUTO,                                                                  ///< Top left binding
    STAPLE_MODE_TOP_RIGHT_AUTO,                                                                 ///< Top right binding
    STAPLE_MODE_BOTTOM_LEFT_AUTO,                                                               ///< Bottom left binding
    STAPLE_MODE_BOTTOM_RIGHT_AUTO,                                                              ///< Bottom right binding
    STAPLE_MODE_TOP_LEFT_ZERO,                                                                  ///< Top left binding
    STAPLE_MODE_TOP_RIGHT_ZERO,                                                                 ///< Top right binding
    STAPLE_MODE_BOTTOM_LEFT_ZERO,                                                               ///< Bottom left binding
    STAPLE_MODE_BOTTOM_RIGHT_ZERO,                                                              ///< Bottom right binding
    STAPLE_MODE_DOUBLE_LEFT,                                                                    ///< Double binding
    STAPLE_MODE_DOUBLE_TOP,                                                                     ///< Top double binding
    STAPLE_MODE_DOUBLE_RIGHT,                                                                   ///< right Double binding
    STAPLE_MODE_DOUBLE_BOTTOM,                                                                  ///< Bottom double binding
    STAPLE_MODE_INVALID,                                                                        ///< please keep it in the end of the enum --- invalid data

    STAPLE_MODE_MAX = 0xFFFFFFFF
}STAPLE_MODE_E;


/* punch number*/
typedef enum
{
    PUNCH_NUMBER_NONE = 0x00,
    PUNCH_NUMBER_TWO   = 0x02,
    PUNCH_NUMBER_FOUR  = 0x04,

    PUNCH_NUMBER_MAX = 0xFFFFFFFF
}PUNCH_NUMBER_E;

/*the punch config enum*/
typedef enum
{
    PUNCH_MODE_OFF                              = 0x00,                                         ///< punch off
    PUNCH_MODE_TWO_HOLES,
    PUNCH_MODE_FOUR_HOLES,
    PUNCH_MODE_USE_PRINTER_CONFIG,                                                              ///< use printer's config
    PUNCH_LEFT_TWO_HOLES,                                                                       ///< Left 2 holes
    PUNCH_LEFT_FOUR_HOLES,                                                                      ///< Left 4 holes
    PUNCH_RIGHT_TWO_HOLES,                                                                      ///< Right 2 holes
    PUNCH_RIGHT_FOUR_HOLES,                                                                     ///< Right 4 holes
    PUNCH_TOP_TWO_HOLES,                                                                        ///< top 2 holes
    PUNCH_TOP_FOUR_HOLES,                                                                       ///< top 4 holes
    PUNCH_DOWN_TWO_HOLES,                                                                       ///< bottom 2 holes
    PUNCH_DOWN_FOUR_HOLES,                                                                      ///< bottom 4 holes
    PUNCH_MODE_INVALID,                                                                         ///< please keep it in the end of the enum --- invalid data
    PUNCH_MODE_MAX = 0xFFFFFFFF
}PUNCH_MODE_E;


/*the fold config enum*/
typedef enum
{
    FOLD_MODE_OFF                               = 0x00,                                         ///< fold off
    FOLD_MODE_2,                                                                                ///< Double fold (press) each assignment together
    FOLD_MODE_3,                                                                                ///< three folding (Ann chang) can be folded together with n pieces of paper
    FOLD_MODE_Z,                                                                                ///< Z folding
    FOLD_MIDDLE_2_STAPLE,                                                                       ///< Fold and bind (according to the copy) for each assignment
    FOLD_USE_PRINTER_CONFIG,                                                                    ///< follow printer's config
    FOLD_MODE_INVALID,                                                                          ///< please keep it in the end of the enum --- invalid data
	FOLD_MODE_MAX = 0xFFFFFFFF
}FOLD_MODE_E;


typedef enum
{
    TRAY_RECEIVE_STANDARD                       = 0x00,                                         ///< the Additional tray
    TRAY_RECEIVE_1,                                                                             ///< the 1st tray
    TRAY_RECEIVE_2,                                                                             ///< the 2nd tray
    TRAY_RECEIVE_3,                                                                             ///< the saddle tray
    TRAY_RECEIVE_USE_PRINTER_CONFIG,                                                            ///< follow printer's panel config
    TRAY_RECEIVE_INVALID,                                                                       ///< please keep it in the end of the enum --- invalid data
    TRAY_RECEIVE_MAX = 0xFFFFFFFF
}TRAY_RECEIVE_E;


// 0, off; 1, on; 2, use printer's config
typedef enum
{
    SHIFT_MODE_OFF                              = 0x00,                                         ///< shift mode off
    SHIFT_MODE_ON,                                                                              ///< shift mode on
    SHIFT_MODE_USE_PRINTER_CONFIG,                                                              ///< use printer's config
    SHIFT_MODE_1,                                                                               ///< shift mode 1
    SHIFT_MODE_2,                                                                               ///< shift mode 2
    SHIFT_MODE_CROSS,                                                                           ///< shift mode cross
    SHIFT_MODE_INVALID,                                                                         ///< please keep it in the end of the enum --- invalid data

    SHIFT_MODE_MAX = 0xFFFFFFFF
}SHIFT_MODE_E;

/*shadow page config enum, for cross shift mode*/
typedef enum
{
    SHADOW_PAGE_NOT                             = 0x00,                                         ///< not a shadow page
    SHADOW_PAGE_YES,                                                                            ///< is a shadow page
    SHADOW_PAGE_INVALID,                                                                        ///< please keep it in the end of the enum --- invalid data

    SHADOW_PAGE_MAX = 0xFFFFFFFF
}SHADOW_PAGE_E;

///> 订钉角度属性对应枚举值
typedef enum
{
    STAPLE_ANGLE_OFF                                                    = 0x00,
    STAPLE_ANGLE_AUTO,                                                                          ///< The nail Angle is automatic
    STAPLE_ANGLE_ZERO,                                                                          ///< The nail Angle is zero

    STAPLE_ANGLE_INVALID                                                                        ///< Keep this value at the end of the enumeration, which is invalid
}STAPLE_ANGLE_E;

/// 驱动输出的横纵向方向属性
typedef enum
{
    PORTRAIT                                                        = 0x00,             ///< 驱动输出纵向画像
    LADNSCAPE,                                                                          ///< 驱动输出横向画像
    REVERSE_PORTAIT,                                                                     ///< 驱动输出旋转180°加纵向画像
    REVERSE_LANDSCAPE,                                                                   ///< 驱动输出旋转180°加横向画像
}PORTRAIT_DIRECTION_E;



typedef struct tag_staple_config
{
    STAPLE_MODE_E             staple_mode;
    PUNCH_MODE_E              punch_mode;
    FOLD_MODE_E               fold_mode;
    uint32_t                  fold_pages;
    TRAY_RECEIVE_E            tray_receive;
    uint32_t                  paper_max_push_put;     // the maximum number of how many paper can be push out every times

}STAPLE_CONFIG_S;


typedef struct
{
    signed char                top_margin;
    signed char                left_margin;
}IMAGE_DISPLACEMENT_INFO_S,*IMAGE_DISPLACEMENT_INFO_P;


typedef struct
{
    IMAGE_DISPLACEMENT_INFO_S   front_page;                         // 正面画像
    IMAGE_DISPLACEMENT_INFO_S   back_page;                          // 背面画像
}IMAGE_DISPLACEMENT_S,*IMAGE_DISPLACEMENT_P;


typedef struct
{
    unsigned char               interleave_page_mode;               // 插页模式开关 0 off;1 on
    unsigned char               interleave_page_number;             // 插页的页数
    unsigned int                interleave_page_position[30];       // 插页的位置
}INTERLEAVE_PAGE_S,*INTERLEAVE_PAGE_P;


typedef struct
{
    unsigned char               chapters_page_mode;                 // 章节页模式开关 0 off;1 on
    unsigned char               chapters_page_number;               // 插入章节页的页数
    unsigned int                chapters_page_position[30];         // 插入章节页的位置
}CHAPTERS_PAGE_S,*CHAPTERS_PAGE_P;

typedef struct
{
    char                        file_path[256];                         // 样本打印存储路径
    unsigned char               sample_mode;                             // 样本打印模式开关 0 off;1 on
    unsigned int                print_copies;                           // 样本打印修改份数
    unsigned char               color_mode;                             // 样本打印修改颜色模式 0 keep;1 color;2 mono
    unsigned char               seal_page;                              // 样本打印封页模式 0 off;1 top;2 down;3 top or down
    IMAGE_DISPLACEMENT_S        image_displacement;                     // 样本打印图像偏移模式
    INTERLEAVE_PAGE_S           interleave;                             // 样本打印插页模式
    CHAPTERS_PAGE_S             chapters;                               // 样本打印章节页模式
    unsigned int                print_mode;                             // 单双面打印模式
}PRINT_SAMPLE_S,*PRINT_SAMPLE_P;

typedef struct tag_delay_job_panel_ctl
{
    char                            file_path[256];                 ///< file_path
    uint32_t                        user_id;                        ///< user id
    uint32_t                        user_num;                       ///< user num
    uint32_t                        job_id;                         ///< job id
    uint32_t                        job_num;                        ///< job num
}DELAY_JOB_PANEL_CTRL_S;

typedef enum
{
    COPY_JOB_TYPE_ID_CARD = 0,
    COPY_JOB_TYPE_BILL,
    COPY_JOB_TYPE_SCALE,
    COPY_JOB_TYPE_NUP,
    COPY_JOB_TYPE_CLONE,
    COPY_JOB_TYPE_POSTER,
    COPY_JOB_TYPE_SPILT,
    COPY_JOB_TYPE_BOOKLET,
    COPY_JOB_TYPE_MIX,
    COPY_JOB_TYPE_PAGE_SPLIT,
    COPY_JOB_TYPE_MAX = 0xFFFFFFFF,
}COPY_JOB_TYPE_E;

/**
 * @brief paper flip mode\n
 *.
 */
typedef enum
{
    FLIP_MODE_OFF       = 0,            ///< flip mode is off
    FLIP_LEFT_MODE      = 1,            ///< flip mode is left
    FLIP_RIGHT_MODE     = 2,            ///< flip mode is right
    FLIP_UP_MODE        = 3,            ///< flip mode is up
    FLIP_MODE_INVALID   = 4,            ///< invalid data
    FLIP_MAX            = 0XFFFFFFFF
}FLIP_MODE_E;

/**
 * @brief nup copy type\n
 *
 */
typedef enum
{
    COPY_NUP_CLOSE     = 0,
    COPY_NUP_2IN1      = 1,
    COPY_NUP_4IN1      = 2,
    COPY_NUP_INVALID   = 3,
    COPY_NUP_MAX       = 0XFFFFFFFF
}COPY_NUP_TYPE_E;

/**
 * @brief idcard copy type\n
 *
 */
typedef enum
{
    COPY_ID_OFF           = 0,
    COPY_ID_FULL_PORTRAIT,
    COPY_ID_HALF_PORTRAIT,
    COPY_ID_HALF_LANDSCAPE,
    COPY_ID_A5_LANDSCAPE,
    COPY_ID_MAX = 0XFFFFFFFF
}COPY_ID_TYPE_E;



/**
 * @brief clone copy type\n
 *
 */
typedef enum
{
    COPY_CLONE_OFF = 0,
    COPY_CLONE_2x2 = 2,
    COPY_CLONE_3x3,
    COPY_CLONE_4x4,
    COPY_CLONE_MAX = 0XFFFFFFFF,
}COPY_CLONE_TYPE_E;


/**
 * @brief nup job images combination type\n
 *
 */
typedef enum
{
    COPY_NUP_COMBINATION_OFF         = 0,            ///< combination off
    COPY_NUP_COMBINATION_HORIZONTAL  = 1,            ///< horizontal type
    COPY_NUP_COMBINATION_PORTRAIT    = 2,            ///< portrait type
    COPY_NUP_COMBINATION_INVALID     = 3,            ///< invalid data
    COPY_NUP_COMBINATION_MAX         = 0XFFFFFFFF
}COPY_NUP_COMBINATION_TYPE_E;

/**
 * @brief poster copy size \n
 *
 */
typedef enum
{
    COPY_POSTER_SIZE_NONE           = 0,            ///< poster off
    COPY_POSTER_SIZE_A3             = 1,            ///< poster A3 size
    COPY_POSTER_SIZE_A2             = 2,            ///< poster A2 size
    COPY_POSTER_SIZE_A1             = 3,            ///< poster A1 size
    COPY_POSTER_SIZE_A0             = 4,            ///< poster A0 size
    COPY_POSTER_SIZE_A0X2           = 5,            ///< poster A0x2 size
    COPY_POSTER_SIZE_B3             = 6,            ///< poster B3 size
    COPY_POSTER_SIZE_B2             = 7,            ///< poster B2 size
    COPY_POSTER_SIZE_B1             = 8,            ///< poster B1 size
    COPY_POSTER_SIZE_B0             = 9,            ///< poster B0 size
    COPY_POSTER_SIZE_48X64          = 10,           ///< poster 48X64 inch size
    COPY_POSTER_SIZE_44X68          = 11,           ///< poster 44X68 inch size
    COPY_POSTER_SIZE_36X48          = 12,           ///< poster 36X48 inch size
    COPY_POSTER_SIZE_34X44          = 13,           ///< poster 34X44 inch size
    COPY_POSTER_SIZE_24X36          = 14,           ///< poster 24X36 inch size
    COPY_POSTER_SIZE_22X34          = 15,           ///< poster 22X34 inch size
    COPY_POSTER_SIZE_18X24          = 16,           ///< poster 18X24 inch size
    COPY_POSTER_SIZE_17X22          = 17,           ///< poster 17X22 inch size
    COPY_POSTER_SIZE_11X17          = 18,           ///< poster 11X17 inch size
    COPY_POSTER_SIZE_USER_DEFINE    = 19,           ///< poster user define size
    COPY_POSTER_SIZE_MAX    = 0XFFFFFFFF
}COPY_POSTER_SIZE_E;

/**
 * @brief copy original first image orientation\n
 *
 */
typedef enum
{
    COPY_ORIGINAL_ORIENTATION_UP        = 0,        ///< up
    COPY_ORIGINAL_ORIENTATION_DOWN      = 1,        ///< down
    COPY_ORIGINAL_ORIENTATION_LEFT      = 2,        ///< left
    COPY_ORIGINAL_ORIENTATION_RIGHT     = 3,        ///< right
    COPY_ORIGINAL_ORIENTATION_INVALID   = 4,        ///< invalid data
    COPY_ORIGINAL_ORIENTATION_MAX       = 0XFFFFFFFF
}COPY_ORIGINAL_ORIENTATION_E;


/**
 * @brief original file or copies duplex type\n
 *        original file can single scan or double side scan\n
 *        copies file can single print or double side print\n
 *
 */
typedef enum
{
                                                  ///< scan mode    print mode
    COPY_SCAN_SINGLE_PRINT_SINGLE = 0,            ///< single    -> single
    COPY_SCAN_SINGLE_PRINT_DUPLEX = 1,            ///< single    -> duplex
    COPY_SCAN_DUPLEX_PRINT_SINGLE = 2,            ///< duplex    -> single
    COPY_SCAN_DUPLEX_PRINT_DUPLEX = 3,            ///< duplex    -> duplex
    COPY_DUPLEX_MAX               = 0XFFFFFFFF
}COPY_DUPLEX_TYPE_E;

/**
 * @brief original file scan duplex mode\n
 *
 */
typedef enum
{
    SCAN_DELLEX_SINGLE          = 0,                 ///< single scan
    SCAN_DELLEX_AUTO_DOUBLE     = 1,                 ///< auto double scan
    SCAN_DELLEX_MANUAL_DOUBLE   = 2,                 ///< manual double scan
    SCAN_DELLEX_INVALID         = 3,                 ///< invalid data
    SCAN_DELLEX_MAX             = 0xFFFFFFFF
}SCAN_DUPLEX_MODE_E;

/**
 * @brief cover and back cover tyep\n
 *
 */
typedef enum
{
    COVER_OFF                = 0,             ///< cover off
    SIGNAL_OUT_SIDE          = 1,             ///< signal on the outside
    SIGNAL_IN_SIDE           = 2,             ///< signal on the inside
    DOUBLE_SIDE              = 3,             ///< two sides cover
    COVER_EMPTY              = 4,             ///< cover empty
    COVER_SIDE_INVALID       = 5,             ///< invalid data
    COVER_SIDE_MAX          = 0xFFFFFFFF
}COVER_MODE_E;

/**
 * @brief mirror image copy is enabled\n
 *
 */
typedef enum
{
    MIRROR_IMAGE_COPY_OFF       = 0,            ///< mirror image copy is not enabled
    MIRROR_IMAGE_COPY_ON        = 1,            ///< mirror image copy is enabled
    MIRROR_IMAGE_COPY_INVALID   = 2,            ///< invalid data
    MIRROR_IMAGE_COPY_MAX       = 0xFFFFFFFF
}MIRROR_IMAGE_COPY_ENALE_E;

/**
 * @brief copy quality\n
 *
 [注释备注：COPY_QUALITY_TYPE_E不再使用，使用QUALITY_MODE_E]

typedef enum
{
    COPY_QUALITY_AUTO = 0,      ///< copy quality auto
    COPY_QUALITY_MIXED,         ///< copy quality is mixed
    COPY_QUALITY_PICTURE,       ///< copy quality is picture
    COPY_QUALITY_TXT,           ///< copy quality is text
    COPY_QUALITY_MAX = 0XFFFFFFFF,           ///< copy quality max
}COPY_QUALITY_TYPE_E; */

/**
 * @brief copy brightness\n
 *
 */
typedef enum
{
    COPY_BRIGHTNESS_AUTO    = 0x80,         ///< copy brightness auto
    COPY_BRIGHTNESS_LEVEL_1 = 1,            ///< copy brightness level 1
    COPY_BRIGHTNESS_LEVEL_2 = 2,            ///< copy brightness level 2
    COPY_BRIGHTNESS_LEVEL_3 = 3,            ///< copy brightness level 3
    COPY_BRIGHTNESS_LEVEL_4 = 4,            ///< copy brightness level 4
    COPY_BRIGHTNESS_LEVEL_5 = 5,            ///< copy brightness level 5
    COPY_BRIGHTNESS_LEVEL_6 = 6,            ///< copy brightness level 6
    COPY_BRIGHTNESS_LEVEL_7 = 7,            ///< copy brightness level 7
    COPY_BRIGHTNESS_LEVEL_8 = 8,            ///< copy brightness level 8
    COPY_BRIGHTNESS_LEVEL_9 = 9,            ///< copy brightness level 9
    COPY_BRIGHTNESS_LEVEL_10 = 10,          ///< copy brightness level 10
    COPY_BRIGHTNESS_LEVEL_11 = 11,          ///< copy brightness level 11
    COPY_BRIGHTNESS_LEVEL_MAX = 0XFFFFFFFF, ///< copy brightness max
}COPY_BRIGHTNESS_TYPE_E;

/**
 * @brief copy saturation\n
 *
 */
typedef enum
{
    COPY_SATURATION_AUTO   = 0,             ///< copy saturation auto
    COPY_SATURATION_LEVEL_1 = 1,            ///< copy saturation level 1
    COPY_SATURATION_LEVEL_2 = 2,            ///< copy saturation level 2
    COPY_SATURATION_LEVEL_3 = 3,            ///< copy saturation level 3
    COPY_SATURATION_LEVEL_4 = 4,            ///< copy saturation level 4
    COPY_SATURATION_LEVEL_5 = 5,            ///< copy saturation level 5
    COPY_SATURATION_LEVEL_6 = 6,            ///< copy saturation level 6
    COPY_SATURATION_LEVEL_7 = 7,            ///< copy saturation level 7
    COPY_SATURATION_LEVEL_8 = 8,            ///< copy saturation level 8
    COPY_SATURATION_LEVEL_9 = 9,            ///< copy saturation level 9
    COPY_SATURATION_LEVEL_10 = 10,          ///< copy saturation level 10
    COPY_SATURATION_LEVEL_11 = 11,          ///< copy saturation level 11
    COPY_SATURATION_LEVEL_MAX = 0XFFFFFFFF, ///< copy saturation max
}COPY_SATURATION_TYPE_E;

/**
 * @brief copy hue \n
 *
 */
typedef enum
{
    COPY_HUE_LEVEL_1 = 1,            ///< copy hue level 1
    COPY_HUE_LEVEL_2 = 2,            ///< copy hue level 2
    COPY_HUE_LEVEL_3 = 3,            ///< copy hue level 3
    COPY_HUE_LEVEL_4 = 4,            ///< copy hue level 4
    COPY_HUE_LEVEL_5 = 5,            ///< copy hue level 5
    COPY_HUE_LEVEL_6 = 6,            ///< copy hue level 6
    COPY_HUE_LEVEL_7 = 7,            ///< copy hue level 7
    COPY_HUE_LEVEL_8 = 8,            ///< copy hue level 8
    COPY_HUE_LEVEL_9 = 9,            ///< copy hue level 9
    COPY_HUE_LEVEL_10 = 10,          ///< copy hue level 10
    COPY_HUE_LEVEL_11 = 11,          ///< copy hue level 11
    COPY_HUE_LEVEL_MAX = 0XFFFFFFFF, ///< copy hue max
}COPY_HUE_TYPE_E;

/**
 * @brief copy contrast\n
 *
 */
typedef enum
{
    COPY_CONTRAST_AUTO   = 0,             ///< copy contrast auto
    COPY_CONTRAST_LEVEL_1 = 1,            ///< copy contrast level 1
    COPY_CONTRAST_LEVEL_2 = 2,            ///< copy contrast level 2
    COPY_CONTRAST_LEVEL_3 = 3,            ///< copy contrast level 3
    COPY_CONTRAST_LEVEL_4 = 4,            ///< copy contrast level 4
    COPY_CONTRAST_LEVEL_5 = 5,            ///< copy contrast level 5
    COPY_CONTRAST_LEVEL_6 = 6,            ///< copy contrast level 6
    COPY_CONTRAST_LEVEL_7 = 7,            ///< copy contrast level 7
    COPY_CONTRAST_LEVEL_8 = 8,            ///< copy contrast level 8
    COPY_CONTRAST_LEVEL_9 = 9,            ///< copy contrast level 9
    COPY_CONTRAST_LEVEL_10 = 10,          ///< copy contrast level 10
    COPY_CONTRAST_LEVEL_11 = 11,          ///< copy contrast level 11
    COPY_CONTRAST_LEVEL_MAX = 0XFFFFFFFF, ///< copy contrast max
}COPY_CONTRAST_TYPE_E;

/**
 * @brief copy shrarpness\n
 *
 */
typedef enum
{
    COPY_SHARPNESS_AUTO   = 0,             ///< copy sharpness auto
    COPY_SHARPNESS_LEVEL_1 = 1,            ///< copy sharpness level 1
    COPY_SHARPNESS_LEVEL_2 = 2,            ///< copy sharpness level 2
    COPY_SHARPNESS_LEVEL_3 = 3,            ///< copy sharpness level 3
    COPY_SHARPNESS_LEVEL_4 = 4,            ///< copy sharpness level 4
    COPY_SHARPNESS_LEVEL_5 = 5,            ///< copy sharpness level 5
    COPY_SHARPNESS_LEVEL_6 = 6,            ///< copy sharpness level 6
    COPY_SHARPNESS_LEVEL_7 = 7,            ///< copy sharpness level 7
    COPY_SHARPNESS_LEVEL_8 = 8,            ///< copy sharpness level 8
    COPY_SHARPNESS_LEVEL_9 = 9,            ///< copy sharpness level 9
    COPY_SHARPNESS_LEVEL_10 = 10,          ///< copy sharpness level 10
    COPY_SHARPNESS_LEVEL_11 = 11,          ///< copy sharpness level 11
    COPY_SHARPNESS_LEVEL_MAX = 0XFFFFFFFF, ///< copy sharpness max
}COPY_SHARPNESS_TYPE_E;

/**
 * @brief copy color balance\n
 *
 */
typedef enum
{
    COPY_COLORBALANCE_AUTO   = 0,             ///< copy colorbalance auto
    COPY_COLORBALANCE_LEVEL_1 = 1,            ///< copy colorbalance level 1
    COPY_COLORBALANCE_LEVEL_2 = 2,            ///< copy colorbalance level 2
    COPY_COLORBALANCE_LEVEL_3 = 3,            ///< copy colorbalance level 3
    COPY_COLORBALANCE_LEVEL_4 = 4,            ///< copy colorbalance level 4
    COPY_COLORBALANCE_LEVEL_5 = 5,            ///< copy colorbalance level 5
    COPY_COLORBALANCE_LEVEL_6 = 6,            ///< copy colorbalance level 6
    COPY_COLORBALANCE_LEVEL_7 = 7,            ///< copy colorbalance level 7
    COPY_COLORBALANCE_LEVEL_8 = 8,            ///< copy colorbalance level 8
    COPY_COLORBALANCE_LEVEL_9 = 9,            ///< copy colorbalance level 9
    COPY_COLORBALANCE_LEVEL_10 = 10,          ///< copy colorbalance level 10
    COPY_COLORBALANCE_LEVEL_11 = 11,          ///< copy colorbalance level 11
    COPY_COLORBALANCE_LEVEL_MAX = 0XFFFFFFFF, ///< copy colorbalance max
}COPY_COLORBALANCE_TYPE_E;

typedef enum
{
    COPY_BACKGROUNDREMOVE_AUTO   = 0,             ///< copy backgroundermove auto
    COPY_BACKGROUNDREMOVE_LEVEL_1 = 1,            ///< copy backgroundermove level 1
    COPY_BACKGROUNDREMOVE_LEVEL_2 = 2,            ///< copy backgroundermove level 2
    COPY_BACKGROUNDREMOVE_LEVEL_3 = 3,            ///< copy backgroundermove level 3
    COPY_BACKGROUNDREMOVE_LEVEL_4 = 4,            ///< copy backgroundermove level 4
    COPY_BACKGROUNDREMOVE_LEVEL_5 = 5,            ///< copy backgroundermove level 5
    COPY_BACKGROUNDREMOVE_LEVEL_6 = 6,            ///< copy backgroundermove level 6
    COPY_BACKGROUNDREMOVE_LEVEL_7 = 7,            ///< copy backgroundermove level 7
    COPY_BACKGROUNDREMOVE_LEVEL_8 = 8,            ///< copy backgroundermove level 8
    COPY_BACKGROUNDREMOVE_LEVEL_9 = 9,            ///< copy backgroundermove level 9
    COPY_BACKGROUNDREMOVE_LEVEL_10 = 10,          ///< copy backgroundermove level 10
    COPY_BACKGROUNDREMOVE_LEVEL_11 = 11,          ///< copy backgroundermove level 11
    COPY_BACKGROUNDREMOVE_LEVEL_MAX = 0XFFFFFFFF, ///< copy backgroundermove max
}COPY_BACKGROUNDREMOVE_TYPE_E;

/*paper size enum applies to all print and scan and copy*/
typedef enum
{
    PAPER_SIZE_A4                   = 0,///< 210.0 x 297.0
    PAPER_SIZE_A5,                      ///< 148.5 x 210.0
    PAPER_SIZE_A5L,                     ///< 210.0 x 148.0
    PAPER_SIZE_B5,
    PAPER_SIZE_FULL_PLATEN,
    PAPER_SIZE_LETTER,                  ///< 215.9 x 279.4
    PAPER_SIZE_CARD,
    PAPER_SIZE_FOLIO,                   ///< 216.0 x 330.0
    PAPER_SIZE_ISO_B5,                  ///< 176.0 x 250.0
    PAPER_SIZE_A6,                      ///< 105.0 x 148.0
    PAPER_SIZE_USER_DEFINE,             ///< 75~218x148~356,long paper above 356~1200
    PAPER_SIZE_LEGAL13,                 ///< 215.9 x 330.0
    PAPER_SIZE_LEGAL14,                 ///< 215.9 x 355.6
    PAPER_SIZE_JIS_B5,                  ///< 182.0 x 257.0
    PAPER_SIZE_ENV_MONARCH,             ///< 098.4 x 190.5
    PAPER_SIZE_ENV_DL,                  ///< 110.0 x 220.0
    PAPER_SIZE_ENV_C5,                  ///< 162.0 x 229.0
    PAPER_SIZE_ENV_10,                  ///< 104.8 x 241.3
    PAPER_SIZE_YOUKEI_SIZE4,            ///< 105.0 x 234.0
    PAPER_SIZE_JAPANESE_POSTCARD,       ///< 100.0 x 148.0
    PAPER_SIZE_CHOUKEI_SIZE3,           ///< 120.0 x 235.0
    PAPER_SIZE_CUSTOM_16K,              ///< 185.0 x 260.0
    PAPER_SIZE_CUSTOM_BIG_16K,          ///< 195.0 x 270.0
    PAPER_SIZE_CUSTOM_32K,              ///< 130.0 x 185.0
    PAPER_SIZE_CUSTOM_BIG_32K,          ///< 135.0 x 195.0
    PAPER_SIZE_EXECUTIVE,               ///< 184.0 x 267.0
    PAPER_SIZE_OFICIO,                  ///< 216.0 x 343.0
    PAPER_SIZE_STATEMENT,               ///< 140.0 x 216.0
    PAPER_SIZE_ENV_C6,                  ///< 114.3 x 162.0
    PAPER_SIZE_ZL,                      ///< 120.0 x 230.0
    PAPER_SIZE_B6,                      ///< 125.0 x 176.0
    PAPER_SIZE_ENV_B6,                  ///< 125.0 x 176.0
    PAPER_SIZE_POSTCARD,                ///< 148.0 x 200.0
    PAPER_SIZE_YOUGATA2,                ///< 114.0 x 162.0
    PAPER_SIZE_NAGAGATA3,               ///< 120.0 x 235.0
    PAPER_SIZE_YOUNAGA3,                ///< 120.0 x 235.0
    PAPER_SIZE_YOUGATA4,                ///< 105.0 x 235.0
    PAPER_SIZE_LONG,                    ///< 210.0 x 1200.0
    PAPER_SIZE_A3,                      ///< 297.0 x 420.0
    PAPER_SIZE_A4L,                     ///< 297.0 x 210.0
    PAPER_SIZE_JIS_B6,                  ///< 128.0 x 182.0
    PAPER_SIZE_JIS_B4,                  ///< 257.0 x 364.0
    PAPER_SIZE_4X6_INCH,                ///< 101.6 x 152.4 / 4'' x 6''
    PAPER_SIZE_INVOICE,                 ///< 139.7 x 215.9 / 5.5'' x 8.5''
    PAPER_SIZE_QUARTO,                  ///< 254.0 x 203.2 / 10'' x 8''
    PAPER_SIZE_G_LETTER,                ///< 266.0 x 203.2 /10.5'' x 8''
    PAPER_SIZE_11X14_INCH,              ///< 279.4 x 355.6 / 11'' x 14''
    PAPER_SIZE_LEDGER,                  ///< 279.4 x 431.8 /11'' x 17''
    PAPER_SIZE_8K,                      ///< 270.0 x 390.0
    PAPER_SIZE_SRA3 ,                   ///< 320.0 x 450.0
    PAPER_SIZE_FOOLSCAP1,               ///< 203.0 x 330.2 / 8''x13''
    PAPER_SIZE_FOOLSCAP2,               ///< 209.6 x 330.2 / 8.25''x13''
    PAPER_SIZE_FOOLSCAP3,               ///< 215.9 x 330.2 / 8.5''x13''
    PAPER_SIZE_FOOLSCAP4,               ///< 220.0 x 330.0 / 8.65''x13''
    PAPER_SIZE_FOOLSCAP5,               ///< 206.4 x 336.6 / 8.125''x13.25''
    PAPER_SIZE_A3_WIDE1,                ///< 304.8 x 457.2 / 12''x18''
    PAPER_SIZE_A3_WIDE2 ,               ///< 311.1 x 457.2 / 12.25''x18''
    PAPER_SIZE_CUSTOM_BIG_16KL,         ///< 270.0 x 195.0
    PAPER_SIZE_JIS_B5L ,                ///< 257.0 x 182.0
    PAPER_SIZE_INVOICE_L,               ///< 215.9 x 139.7 / 8.5'' x 5.5''
    PAPER_SIZE_EXECUTIVE_L,             ///< 266.7 x 184.2 / 10.5''x 7.25''
    PAPER_SIZE_QUARTO_L	,               ///< 254.0 x 203.2 / 10'' x 8''
    PAPER_SIZE_G_LETTER_L,              ///< 266.7 x 203.2 / 10.5''x 8''
    PAPER_SIZE_LETTER_L,                ///< 279.4 x 215.9 / 11" x 8.5''
    PAPER_SIZE_ISO_B5L,                 ///< 250.0 x 176.0

    PAPER_SIZE_USER_DEFINE1,
    PAPER_SIZE_USER_DEFINE2,
    PAPER_SIZE_USER_DEFINE3,
    PAPER_SIZE_USER_DEFINE4,
    PAPER_SIZE_USER_DEFINE5,
    PAPER_SIZE_B4 ,
    PAPER_SIZE_A6CARD,

    PAPER_SIZE_GENERAL,              ///< general size
    PAPER_SIZE_MIXED  ,              ///< paper size by mixed
    PAPER_SIZE_STATEMENT_L ,

    PAPER_SIZE_B5L,
    PAPER_SIZE_BIG_16K,
    PAPER_SIZE_BIG_16KL,

    PAPER_SIZE_AUTO,                           ///< paper size is auto selected
    PAPER_SIZE_UNKOWN,
    PAPER_SIZE_FULL_TABLE,                     ///< scan full table

    PAPER_SIZE_INVALID              = 0xFF,    ///< please keep it in the end of the enum --- invalid data
    PAPER_SIZE_MAX                  = 0xFFFFFFFF
}PAPER_SIZE_E;


/*paper type enum applies to all print and scan and copy*/
typedef enum
{

    PAPER_TYPE_ORDINARY = 0,                // plain paper, 75~80 -> 60~90 gsm
    PAPER_TYPE_ORDINARY_P,                  // plain paper+,
    PAPER_TYPE_THICK1,                      // thick paper 1, 91~120 gsm
    PAPER_TYPE_THICK1_P,                    // thick paper 1+,
    PAPER_TYPE_THICK2,                      // thick paper 2, 121~157 gsm
    PAPER_TYPE_THICK3,                      // thick paper 3, 158~209 gsm
    PAPER_TYPE_THICK4,                      // thick paper 4, 210~256 gsm
    PAPER_TYPE_FILM,                        // transparent film paper
    PAPER_TYPE_POST_CARD,                   // postcard paper
    PAPER_TYPE_LETTER_HEAD,                 // letterhead paper
    PAPER_TYPE_TAB,                         // tab paper
    PAPER_TYPE_LABEL,                       // label paper
    PAPER_TYPE_ROLL,                        // roll paper
    PAPER_TYPE_THICK,                       // thick paper 90~163g
    PAPER_TYPE_ENVELOP,                     // envelope paper
    PAPER_TYPE_CARD,                        // card paper
    PAPER_TYPE_THIN,                        // thin paper 60~70g
    PAPER_TYPE_VINYL,                       // vinyl paper
    PAPER_TYPE_ORDINARY_SILENCE,            // plain paper for silent printing
    PAPER_TYPE_THIN_SILENCE,                // thin paper for silent printing

    PAPER_TYPE_RECYCLE,                     // recycle paper
    PAPER_TYPE_COATED,                      // coated paper
    PAPER_TYPE_SINGLE_SIDE_ONLY,            // single side only paper
    PAPER_TYPE_COLOR,                       // color paper
    PAPER_TYPE_SPECIAL,                     // special paper
    PAPER_TYPE_AUTO             = 0x80,
    PAPER_TYPE_INVALID          = 0xFF,     // please keep it in the end of the enum --- invalid data
    PAPER_TYPE_MAX              = 0xFFFFFFFF
}PAPER_TYPE_E;

typedef struct tag_job_status_data
{
    EVENT_SUBJECT_S       *job_start_subject;
    EVENT_SUBJECT_S       *job_done_subject;
    EVENT_SUBJECT_S       *job_error_subject;
    EVENT_SUBJECT_S       *job_cancel_subject;
    EVENT_SUBJECT_S       *page_receive_subject;
    EVENT_SUBJECT_S       *page_done_subject;
    EVENT_SUBJECT_S       *page_count_subject;
}JOB_STATUS_EVENT;

//for small margin job
typedef enum
{
    JOB_MARGIN_NORMAL = 0,                                      //Normal margin
    JOB_MARGIN_SMALL,                                           //Small margin ( less or equal to 2.5mm ) in Copy Function
    JOB_MARGIN_INVALID,
    JOB_MARGIN_MAX = 0xFFFFFFFF
}PRINT_MARGIN_E;


//for separator_page
typedef enum
{
    SEPARATOR_PAGE_OFF = 0,                                     ///< separator_page function OFF
    SEPARATOR_PAGE_PAGES,                                       ///< will insert a separator_page between diffrent pages, such as 1/2/3/ 1/2/3/
    SEPARATOR_PAGE_COPIES,                                      ///< will insert a separator_page between diffrent copies, such as 123/ 123/
    SEPARATOR_PAGE_JOBS,                                        ///< will insert a separator_page between diffrent jobs, such as 123 123/  1234 1234/
    SEPARATOR_PAGE_INVALID,
    SEPARATOR_PAGE_MAX = 0xFFFFFFFF
}JOB_SEPARATOR_E;

/**
 * @brief insert page type\n
 */
typedef enum
{
    INSERT_PAGE_NO = 0,                                         ///< normal page, it is not a inserted page
    INSERT_PAGE_FOR_ODD_PAGE,                                   ///< inserted empty page, for odd page with duplex job
    INSERT_PAGE_FOR_SEPARATION_PAGE,                            ///< inserted empty page, for separation page with separation(include insert ohp job) job
    INSERT_PAGE_FOR_COVER_PAGE,                                 ///< inserted empty page, for cover page with cover job
    INSERT_PAGE_FOR_INSERT_PAGE_FUNCTION,                       ///< insert page，for insert page function
    INSERT_PAGE_FOR_OTHER,                                      ///< inserted empty page, for reserve
    INSERT_PAGE_TYPE_MAX = 0xFFFFFFFF
}INSERT_PAGE_E;

/**
 * @brief page header page footer position\n
 *
 */
typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_LEFT = 0, ///< page header page footer in left
    COPY_PAGE_HEADER_PAGE_FOOTER_MID,      ///< page header page footer in mid
    COPY_PAGE_HEADER_PAGE_FOOTER_RIGHT,    ///< page header page footer in right
    COPY_PAGE_HEADER_PAGE_FOOTER_POSITION_INVAILD=0xFFFFFFFF
}COPY_PAGE_HEADER_PAGE_FOOTER_POSITION_E;

/**
 * @brief page header page footer content type\n
 *
 */
typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_1 = 0,   ///< date style "`23/3/23"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_2,       ///< date style "16 March,2023"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_3,       ///< date style "March 16,2023"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_4,       ///< date style "16/3/`23"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_5,       ///< date style "3/16/`23"
    COPY_PAGE_HEADER_PAGE_FOOTER_TIME_1,       ///< time style "13：23"
    COPY_PAGE_HEADER_PAGE_FOOTER_TIME_2,       ///< time style "1:23 PM"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_1, ///< pagination style "P1，P2…"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_2, ///< pagination style "1/5,2/5…"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_3, ///< pagination style "1,2,3…"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_4, ///< pagination style "-1-,-2-…"
    COPY_PAGE_HEADER_PAGE_FOOTER_USER_DEFINE,  ///< user define style
    COPY_PAGE_HEADER_PAGE_FOOTER_TEXT_TYPE_INVAILD=0xFFFFFFFF
}COPY_PAGE_HEADER_PAGE_FOOTER_TEXT_TYPE_E;

/**
 * @brief page header page footer pagination\n
 *
 */
typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_ALL_PAGE = 0,       ///< apply to all page
    COPY_PAGE_HEADER_PAGE_FOOTER_ONLY_FIRST_PAGE,    ///< only apply to first_page
    COPY_PAGE_HEADER_PAGE_FOOTER_WITHOUT_FIRST_PAGE, ///< apply to without first_page
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_INVAILD=0xFFFFFFFF
}COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_E;

typedef struct
{
    bool enable;
    COPY_PAGE_HEADER_PAGE_FOOTER_POSITION_E   position;   ///< page header page footer position
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_E pagination; ///< pagination apply scene
    COPY_PAGE_HEADER_PAGE_FOOTER_TEXT_TYPE_E  text_type;  ///< text style
    char text[128]; ///< text data
}COPY_PAGE_HEADER_PAGE_FOOTER_S;

/*image bpp enum applies to all print and scan and copy*/
typedef enum
{
    IMAGE_BPP_1 = 0,                ///< image bpp 1
    IMAGE_BPP_2,                    ///< image bpp 2
    IMAGE_BPP_4,                    ///< image bpp 4
    IMAGE_BPP_8,                    ///< image bpp 8
    IMAGE_BPP_16,                   ///< image bpp 16
    IMAGE_BPP_24,                   ///< image bpp 24
    IMAGE_BPP_32,                   ///< image bpp 32
    IMAGE_BPP_MAX = 0xFFFFFFFF

}IMAGE_BPP_E;

/**
 * @brief copy original first image orientation\n
 *
 */
typedef enum
{
    ORIGINAL_ORIENTATION_TOP       = 0,        ///< up
    ORIGINAL_ORIENTATION_BOTTOM    = 1,        ///< down
    ORIGINAL_ORIENTATION_LEFT      = 2,        ///< left
    ORIGINAL_ORIENTATION_RIGHT     = 3,        ///< right
    ORIGINAL_ORIENTATION_INVALID   = 4,        ///< invalid data
    ORIGINAL_ORIENTATION_MAX       = 0XFFFFFFFF
}ORIGINAL_ORIENTATION_E;

/**
 * @brief copy overlay image color\n
 *
 */
typedef enum {
    OVERLAY_IMG_COLOR_DEFAULT = 0,         ///< all color
    OVERLAY_IMG_COLOR_BLACK,               ///< black
    OVERLAY_IMG_COLOR_RED,                 ///< read
    OVERLAY_IMG_COLOR_BLUE,                ///< blue
    OVERLAY_IMG_COLOR_GREEN,               ///< green
    OVERLAY_IMG_COLOR_YELLOW,              ///< yellow
    OVERLAY_IMG_COLOR_CYAN,                ///< cyan
    OVERLAY_IMG_COLOR_MAGENTA,             ///< magenta
    OVERLAY_IMG_COLOR_INVALID=0xFFFFFFFF   ///< invalid color
} OVERLAY_IMG_COLOR_E;

/**
 * @brief copy overlay image count\n
 *
 */
typedef enum {
    OVERLAY_IMG_COUNT_ALL_PAGE = -1,       ///< all page use overlay image
    OVERLAY_IMG_COUNT_ONLY_FIRST_PAGE = 1,  ///< only first page use overlay image
    OVERLAY_IMG_COUNT_INVALID = 0x7FFFFFFF
} OVERLAY_IMG_COUNT_E;

/**
 * @brief copy overlay image style\n
 *
 */
typedef enum {
    OVERLAY_IMG_STYLE_TRANSPARENCY = 0,    ///< overlay image transparency combine
    OVERLAY_IMG_STYLE_BACKGROUND,          ///< overlay image background combine
    OVERLAY_IMG_STYLE_FOREGROUND,          ///< overlay image foreground combine
    OVERLAY_IMG_STYLE_INVALID = 0xFFFFFFFF ///< overlay image invalid combine
} OVERLAY_IMG_STYLE_E;

/**
 * @brief copy overlay image mode\n
 *
 */
typedef enum {
    OVERLAY_IMG_MODE_OFF = 0,          ///< overlay image off
    OVERLAY_IMG_MODE_ONLY_REGISTER,    ///< only register overlay image
    OVERLAY_IMG_MODE_DEFAULT_COMBINE,  ///< default overlay using the first image as the overlay image
    OVERLAY_IMG_MODE_ONLY_COMBINE,     ///< overlay using the registered image as the overlay image
    OVERLAY_IMG_MODE_INVALID = 0xFFFFFFFF ///< overlay mode invalid
} OVERLAY_IMG_MODE_E;

/**
 * @brief copy overlay image info\n
 *
 */
typedef struct
{
    OVERLAY_IMG_MODE_E     mode;       ///< overlay mode
    char       register_id[128];       ///< register id of overlay image
    char      front_use_id[128];       ///< registered image id of front page use
    char       back_use_id[128];       ///< registered image id of back page use
    OVERLAY_IMG_STYLE_E   style;       ///< overlay style
    OVERLAY_IMG_COLOR_E   color;       ///< overlay color
    OVERLAY_IMG_COUNT_E   count;       ///< overlay count
    int                 density;       ///< overlay density
    int                offset_x;       ///< overlay offset x
    int                offset_y;       ///< overlay offset y
}OVERLAY_IMG_INFO_S;

/**
 * @brief copy insert image info\n
 *
 */
typedef struct
{
    char     insert_number[128];       ///< arrary of insert image number
    int             vaild_count;       ///< insert image vaild count
}INSERT_IMG_INFO_S;


typedef struct
{
    PAPER_SIZE_E  	paper_size;
    int16_t    	    width;
    int16_t    	    height;
}PAPER_SIZE_DETAIL_S;

typedef enum
{
    TRAY_NO_TWO_SIDE_PAPER      = 0,        ///< no long or short side paper
    TRAY_HAVE_ONE_SIDE_PAPER    = 1,        ///< only long or short side paper
    TRAY_HAVE_TWO_SIDE_PAPER    = 2,        ///< have long and short side paper
    TRAY_PAPER_MAX              = 0XFFFFFFFF
}OFFSET_TRAY_PAPER_E;

//extern const PAPER_SIZE_DETAIL c_paper_size_detail[PAPER_SIZE_MAX];
typedef struct
{
    uint32_t                    is_copy;                ///< copy job flag
    COPY_JOB_TYPE_E             copy_type;              ///< copy type
    QUALITY_MODE_E              quality;                ///< quality
    COPY_BRIGHTNESS_TYPE_E      brightness;             ///< brightness
    COPY_BACKGROUNDREMOVE_TYPE_E    backgroundremove;   ///< backgroundremove
    COPY_SATURATION_TYPE_E      saturation;             ///< saturation
    COPY_HUE_TYPE_E             hue;                    ///< hue
    COPY_CONTRAST_TYPE_E        contrast;               ///< contrast
    COPY_SHARPNESS_TYPE_E       sharpness;              ///< sharpness
    COPY_COLORBALANCE_TYPE_E    colorbalance_c;         ///< colorbalance c channel
    COPY_COLORBALANCE_TYPE_E    colorbalance_m;         ///< colorbalance m channel
    COPY_COLORBALANCE_TYPE_E    colorbalance_y;         ///< colorbalance y channel
    COPY_COLORBALANCE_TYPE_E    colorbalance_k;         ///< colorbalance k channel
    TONER_SAVE_E                inksave;                ///< copy ink save
    uint32_t                    tonerdensity;           ///< toner density
    uint32_t                    collate;                ///< collate on/off for adf
    uint32_t                    scale_percent;          ///< from 25%~400%
    uint32_t                    copy_speed;             ///< copy speed
    //COPY_OVERTURN_TYPE        copy_overturn;          ///< overturn
    uint32_t                    direction;              ///< print direction
    uint32_t                    rk_mode;
    uint32_t                    color_inversion;
    //reserve
    uint32_t                    copy_collate_delay;     ///< reserve;
    int32_t                     copy_pie_cut;
    COVER_MODE_E                cover;                  ///<.copies cover mode
    COVER_MODE_E                back_cover;             ///<.copies back cover mode
    PRINT_MODE_E                print_mode;
    uint32_t                    oly_color;          /// set overlay image color
    uint32_t                    oly_density;        /// set overlay image density
    uint32_t                    oly_style;          /// set overlay image style
    uint32_t                    reserve1;               ///< reserve;
    uint32_t                    reserve2;               ///< reserve;
    uint32_t                    reserve3;               ///< reserve;
    //......
}COPY_PARA_S;

/**
*@brief paper direction config for scan job
*/
typedef enum
{
    PAPER_DIR_INVALID = 0,
    PAPER_DIR1,             ///< top
    PAPER_DIR2,             ///< left
    PAPER_DIR3,             ///< right
    PAPER_DIR4              ///< bottom
}PAPER_DIR_E;

/**
*@brief paper turning config for only double scan
*/
typedef enum
{
    PAPER_TURN_INVALID = 0,
    PAPER_TURN_TOP,
    PAPER_TURN_BOTTOM,
    PAPER_TURN_LEFT,
    PAPER_TURN_RIGHT
}PAPER_TURNING_E;

/**
*@brief paper edge clear
*/
typedef struct
{
    short                  top_edge;
    short                  bottom_edge;
    short                  left_edge;
    short                  right_edge;
}EDGE_CLEAN_S;

/**
*@brief book copy type
*/
typedef enum
{
    BOOK_COPY_TYPE_OFF = 0,             ///< book copy off
    BOOK_COPY_TYPE_OPEN,                          ///< full image
    BOOK_COPY_TYPE_SPLIT,                         ///< split mode
    BOOK_COPY_TYPE_WITH_COVER,                    ///< scan cover first
    BOOK_COPY_TYPE_WITH_BOTH_COVER,               ///< scan back cover second
    BOOK_COPY_TYPE_MAX = 0xFFFFFFFF     ///< max enum
}BOOK_COPY_TYPE_E;

/**
*@brief config scan param for sepration scan
*/
typedef struct
{
    SCAN_MODE_E               scan_mode;
    uint32_t                  use_scan_flag;
    COPY_EDGE_CLEAN_S         edge_clean;
    PAPER_TURNING_E           paper_turning;
}SCAN_SEPRATION_S, *SCAN_SEPRATION_P;
typedef struct
{
    bool book_scan;
    bool use_image_check;
    uint32_t book_scan_type; //0 cover,1 cover & back cover,2 book merged,3 book separation
    uint32_t binding_method; //0 left binding ，1 right binding
    COPY_EDGE_CLEAN_S book_edge_clean;
    uint32_t central_elimination;
}BOOK_SCAN_TYPE_S;


#if CONFIG_SDK_EWS
typedef struct
{
    uint32_t user_id;
    uint32_t job_id;
    uint32_t max_page_count;
    char     file_name[256];
}SDK_APPROVAL_JOB_INFO_S;

typedef enum
{
    APPROVAL_MODE_OFF = 0,
    APPROVAL_MODE_SCAN = 1,
    APPROVAL_MODE_COPY = 2,

    APPROVAL_MODE_MAX = 3,
}APPROVAL_MODE_E;
#endif

/**
*@brief config scan param for scan job
*/
typedef struct
{
    /*basic configuration for scan*/
    SCAN_JOB_TYPE_E           scan_dst;
    SCAN_SEND_ROUTER_E        scan_send_router[SCAN_TO_MAX];
    SCAN_MODE_E               scan_mode;                    ///< scan source type such as fb/adf/dadf
    COLOR_MODE_E              scan_color;                   ///< scan color mode such as mono/rgb/gray
    PAPER_SIZE_E              scan_area;                    ///< scan paper size enum such as A3/A4/A5/A6
    FILE_TYPE_E               file_type;                    ///< scan file format such as TIFF/OFD/JPEG
    RESOLUTION_E              xres;                         ///< x direction resolution by dpi
    RESOLUTION_E              yres;                         ///< y direction resolution by dpi
    QUALITY_MODE_E            scan_image_quality;           ///< scan image quality such as text/mixed/picture
    SCAN_IMAGE_QUALITY_S      image_quality;                ///< scan image quality process

    /*functional configuration for scan*/
    int                       combination;                  ///< fb/adf merge scan function is off/on 0-off 1-on
    bool                      fixed_separate;
	int 					  time_out_value;
	uint32_t                  is_mix_size;                  ///< support different width and height only for adf scan
	uint32_t                  use_copy_scan_meantime;       ///< support to scan original paper when copy job
    uint32_t                  edge_scan_flag;               ///< support edge to egde scan
    EDGE_CLEAN_S              front_edge_clean;
    EDGE_CLEAN_S              back_edge_clean;
    ORIGINAL_ORIENTATION_E    paper_direction;              ///< make sure front page direction for single and double scan
    FLIP_MODE_E               paper_turning;                ///< make sure back page direction only for double scan
    BOOK_SCAN_TYPE_S          book_scan_type_member;

}SCAN_CONFIG_PARAM_S;

//panel use for a scan job request
//copy or fax job mgr use to request a scan job
typedef struct
{

    SCAN_CONFIG_PARAM_S       scan_config_param;

    RESOLUTION_E              original_res;                 ///< engine orginal resolution by dpi

    uint32_t                  x_scale_numerator;            ///< scan x direction scale numerator
    uint32_t                  x_scale_denominator;          ///< scan x direction scale denominator
    uint32_t                  y_scale_numerator;            ///< scan y direction scale numerator
    uint32_t                  y_scale_denominator;          ///< scan y direction scale denominator

    uint32_t                  original_area_height;         ///< user define scan area, height pixels
    uint32_t                  area_width;                   ///< user define scan area, width pixels
    uint32_t                  area_height;                  ///< user define scan area, height pixels
    uint16_t                  margin_top;                   ///< top margin pixels
    uint16_t                  margin_left;                  ///< left margin pixels
    uint16_t                  data_type;

    IMAGE_BPP_E               bpp;                          ///< bit-per-pixel(1,2,4,8,16,24,32)
    uint32_t                  scan_file_limit;              ///< scan file size limition
    uint32_t                  memory_low_cancel_enable;     ///< allows memory to cancel automatically 0-off 1-on

    void                      *port;                        ///< if push or pull scan to host, using send data to host
    IO_VIA_E                  job_via;                      ///< if push scan to host,panel chose scan to usb or net,wifi

    uint32_t                  email_count;
    char                      email_address[MAIL_ADDR_NUM_MAX][MAIL_ADDR_LEN];
#if CONFIG_SDK_PEDK

    HTTP_PARM_S               http_param;
#endif
    FTP_PARAM_S               ftp_param;
    SMB_PARM_S                smb_param;

    uint32_t                  memory_low_cancel;
    uint32_t                  memory_low_flag;              ///< send heartbeat to pc when pull or push scan mem low
    uint32_t                  compress_enable;              ///< tmp var,need delete


    uint32_t                  airscan_moudle_id;
    uint32_t                  wsdscan_moudle_id;

    #if CONFIG_SDK_EWS
    int                     max_scan_page_count;                            //copy sdk limit scan page count
    APPROVAL_MODE_E         approval_mode;
    SDK_APPROVAL_JOB_INFO_S approval_job_info;
    #endif

    SCAN_ENGINE_PARAM_S       scan_engine_param;            ///< scan engine param such as engine speed/rotate
    COPY_PARA_S               copy_para;
}SCAN_JOB_REQUEST_DATA_S;

typedef struct
{
    char url[512];
    char headers[1024];
    int  mode;
    int  format_type;
    char file_name_prefix[256];
    char protocol[256];
    int level;
    char custom_field[1024];

}COPY_RETENTION_PARAM_S;




typedef struct
{
    COPY_JOB_TYPE_E     copy_type;                      ///< the copy type for the copy job
    uint32_t            copies;                         ///< the copies for the copy job
    COLOR_MODE_E        color_mode;                     ///< the mode for color
    SCAN_MODE_E         scan_source;                    ///< the source for scan module.
    PAPER_SIZE_E        scan_size;                      ///< the paper type for scan module
    uint32_t            image_orientation;              ///< the orientation for image
    TRAY_INPUT_E        print_tray;                     ///< the tray for print module
    PAPER_SIZE_E        print_size;                     ///< the paper type for print module
    PAPER_TYPE_E        page_type;                      ///< the media type for page
    PRINT_MODE_E        print_mode;                     ///< single or duplex for print module
    uint32_t            auto_scale_mode;                ///< the scaler is auto for copy job
    QUALITY_MODE_E              quality;                ///< the quality for image
    COPY_COLORBALANCE_TYPE_E    color_balance_c;        ///< the color balance for c
    COPY_COLORBALANCE_TYPE_E    color_balance_m;        ///< the color balance for M
    COPY_COLORBALANCE_TYPE_E    color_balance_y;        ///< the color balance for Y
    COPY_COLORBALANCE_TYPE_E    color_balance_k;        ///< the color balance for K
    COPY_BRIGHTNESS_TYPE_E      image_brightness;       ///< brightness
    COPY_SATURATION_TYPE_E      image_saturation;       ///< saturation
    COPY_CONTRAST_TYPE_E        image_contrast;         ///< contrast
    COPY_SHARPNESS_TYPE_E       image_sharpness;        ///< sharpness
    COPY_HUE_TYPE_E             image_hue;              ///< hue
    COPY_BACKGROUNDREMOVE_TYPE_E    backgroundmove_level;   ///< backgroundmove level(1-5)
    TONER_SAVE_E        save_toner_mode;                ///< the mode for the save toner
    int                 horizontal_margin;              ///< the horizontal margin
    int                 vertical_margin;                ///< the vertical margin
    uint32_t            edge_to_edge_mode;              ///<
    uint32_t            collate;                        ///< the collated mode
    uint32_t            separator;                      ///< the separated mode
    uint32_t            nup_type;                       ///< the nup type
    uint32_t            nup_combination;                ///< the nup combinatin
    COPY_POSTER_SIZE_E  poster_size;                    ///< the poster size
    uint32_t            watermark_mode;                 ///< the watermark mode
    char                watermark_string[128];          ///< the watermark string
    uint32_t            booklet_duplex;                 ///< single or duplex for booklet type.
    uint32_t            mirror_mode;                    ///< the mirror mode
    uint32_t            clone_mode;                     ///< the clone mode
    uint32_t            skewing_mode;                   ///< the skewing mode
    OVERLAY_IMG_INFO_S  oly_img_info;                   ///< the overlay image info
    INSERT_IMG_INFO_S   insert_img_info;                ///< the insert image info
    COPY_PAGE_HEADER_PAGE_FOOTER_S           page_header;           ///< the type for the page header
    COPY_PAGE_HEADER_PAGE_FOOTER_S           page_footer;           ///< the type for the page footer
    uint32_t            backup_mode;                    ///<
    uint32_t            sample_mode;                    ///<
    uint32_t            scale_percent;                  ///< from 25%~400%
    uint32_t            auto_id_correction;             ///< id copy correction
    uint32_t            use_color_inversion;           ///< colour inversion

    uint32_t            use_copy_scan_meantime;         ///< support to scan original paper when copy job
    FILE_TYPE_E         scan_file_type;
    SCAN_SEND_ROUTER_E  scan_send_router[SCAN_TO_MAX];

    uint32_t            use_removel_color;
    uint32_t            remove_color_plane;             ///< (1:R 1->remove,2:G 1->remove,3:B 1->remove)

    uint32_t            use_edge_clean;
    uint32_t            filter_edge_margin_top;         ///<.the top margin for the filter edge mode
    uint32_t            filter_edge_margin_left;        ///<.the left margin for the filter edge mode
    uint32_t            filter_edge_margin_right;       ///<.the right margin for the filter edge mode
    uint32_t            filter_edge_margin_bottom;      ///<.the bottom margin for the filter edge mode
    FLIP_MODE_E         original_flip;                  ///<.original page flip mode
    FLIP_MODE_E         copies_flip;                    ///<.copies flip mode
    COVER_MODE_E        cover;                          ///<.copies cover mode
    COVER_MODE_E        cover_back;                     ///<.copies back cover mode
    TRAY_INPUT_E        cover_tray_in;                  ///< the tray for cover type.
    PAPER_SIZE_E        cover_paper_size;               ///< the paper size for cover page
    PAPER_TYPE_E        cover_paper_type;               ///< the paper_type for cover page.
    TRAY_INPUT_E        back_cover_tray_in;             ///< the tray for back cover type.
    PAPER_SIZE_E        back_cover_paper_size;          ///< the paper size for back cover page
    PAPER_TYPE_E        back_cover_paper_type;          ///< the paper_type for back cover page.

    ///<.staple
    STAPLE_NUMBER_E     staple_num;                     ///< the staple num
    STAPLE_ANGLE_E      staple_angle;                   ///< the staple angle
    PUNCH_NUMBER_E      punch_num;                      ///< the punch num
    STAPLE_MODE_E       staple_mode;                    ///< the staple mode
    PUNCH_MODE_E        punch_mode;                     ///< the punch mode
    FOLD_MODE_E         fold_mode;                      ///< the fold mode
    TRAY_RECEIVE_E      tray_receive;                   ///< the tray mode
    SHIFT_MODE_E        shift_mode;                     ///< the shift mode
    uint32_t            fold_number;                    ///< the fold number
    uint32_t            have_stapler;                   ///< whether have stapler ?

    uint32_t            scan_width;                     ///< user define scan area, width mm
    uint32_t            scan_height;                    ///< user define scan area, height mm
    uint32_t            print_width;                    ///< user define print area, width mm
    uint32_t            print_height;                   ///< user define print area, height mm
    TRAY_INPUT_E        separator_tray_in;              ///< the tray for separator type.
    OFFSET_TRAY_PAPER_E offset_tray_paper;              ///< tray has which paper for offset use
    uint32_t            image_bit_depth;                ///< the value is 1 or 2
    COPY_ID_TYPE_E      id_type;                        ///< the idcard composing type

    uint32_t            chapter_on_off;                 ///< chapter switch
    char                chapter_page[256];              ///< the chapter_page string like that : "3#25#57#88#134"
    PAPER_SIZE_E        chapter_paper_size;             ///< the paper size for chapter page
    PAPER_TYPE_E        chapter_paper_type;             ///< the paper type for chapter page.
    TRAY_INPUT_E        chapter_tray_in;                ///< the tray in for chapter page.

    uint32_t            ohp_insert_on_off;              ///< ohp_insert switch
    PAPER_SIZE_E        ohp_insert_paper_size;          ///< the paper size for ohp_insert page
    PAPER_TYPE_E        ohp_insert_paper_type;          ///< the paper type for ohp_insert page.
    TRAY_INPUT_E        ohp_insert_tray_in;             ///< the tray in for ohp_insert page.

    uint32_t            insert_page_on_off;             ///< insert_page switch
    uint32_t            insert_page_use;                ///< insert_page use : 0=use blank page 1:use for print original
    char                insert_page[256];               ///< the insert_page string like that : "3#25#57#88#134"
    PAPER_SIZE_E        insert_page_paper_size;         ///< the paper size for insert_page
    PAPER_TYPE_E        insert_page_paper_type;         ///< the paper type for insert_page.
    TRAY_INPUT_E        insert_page_tray_in;            ///< the tray in for insert_page.

    BOOK_COPY_TYPE_E    book_copy_type;                 ///< book copy type
    uint32_t            non_image_clean;                ///< only image
    uint32_t            center_clean;                   ///< center
    uint32_t            image_in_center;                ///< keep in center

    uint32_t            wonum;                          ///< pedk request every job has work number
    PAPER_SIZE_E        separator_paper_size;           ///< the paper size for separator page
    PAPER_TYPE_E        separator_paper_type;           ///< the paper type for separator page.
    uint32_t            separate_scan_mode;             ///< separate scan mode  0:OFF 1:ON

    uint32_t            email_count;
    char                email_address[MAIL_ADDR_NUM_MAX][MAIL_ADDR_LEN];
    FTP_PARAM_S         ftp_param;
    SMB_PARM_S          smb_param;
    uint32_t            scan_duplex;                    ///< single or duplex for scan module

    uint8_t             reserve[460];                   ///< reserve buff.
}COPY_JOB_REQUEST_DATA_S;

typedef struct
{
    FLIP_MODE_E     copies_flip;    ///< copies flip mode
    STAPLE_NUMBER_E staple_num;     ///< the staple num
    STAPLE_ANGLE_E  staple_angle;   ///< the staple angle
    STAPLE_MODE_E   staple_mode;    ///< the staple mode
}PANEL_PRINT_STAPLE_DATA_S;

typedef struct
{
    FLIP_MODE_E     copies_filp;    ///<.copies flip mode
    PUNCH_NUMBER_E punch_num;      ///< the punch num
    PUNCH_MODE_E   punch_mode;     ///< the punch mode
}PANEL_PRINT_PUNCH_DATA_S;

/**
 * @brief copy margin info
*/
typedef struct
{
    int32_t                    left;                       ///< left
    int32_t                    right;                      ///< right
    int32_t                    top;                        ///< top
    int32_t                    bottom;                     ///< bottom
} COPY_MARGIN_INFO_S;

/*job data for scan param*/
typedef struct
{
    SCAN_JOB_REQUEST_DATA_S     scan_request;
    SCAN_JOB_TYPE_E             scan_job_type;              ///< scan job type such as pull scan/push scan
    SCAN_MODE_E                 scan_source;                ///< scan source type such as fb/adf/dadf
    COLOR_MODE_E                scan_color;                 ///< scan color mode such as mono/rgb/gray

    QUALITY_MODE_E              scan_image_quality;         ///< scan image quality such as text/mixed/picture

    uint32_t                    combination;                ///< merge scan counter; 0:off -1:always 2:for example id_copy

    RESOLUTION_E                original_res;               ///< engine orginal resolution by dpi
    RESOLUTION_E                xres;                       ///< x direction resolution by dpi
    RESOLUTION_E                yres;                       ///< y direction resolution by dpi

    uint32_t                    x_scale_numerator;          ///< scan x direction scale numerator
    uint32_t                    x_scale_denominator;        ///< scan x direction scale denominator
    uint32_t                    y_scale_numerator;          ///< scan y direction scale numerator
    uint32_t                    y_scale_denominator;        ///< scan y direction scale denominator

    PAPER_SIZE_E                scan_paper_size;            ///< scan paper size enum such as A3/A4/A5/A6
    uint32_t                    original_area_height;       ///< user define scan area, height pixels
    uint16_t                    area_width;                 ///< user define scan area, width pixels
    uint16_t                    area_height;                ///< user define scan area, height pixels
    uint16_t                    margin_top;                 ///< top margin pixels
    uint16_t                    margin_left;                ///< left margin pixels

    IMAGE_COLORFORMAT_E         image_colorformat;          ///< scan image data type such as XRGB/PLANAR
    FILE_TYPE_E                 file_type;                  ///< scan file format such as TIFF/OFD/JPEG
    IMAGE_BPP_E                 bpp;                        ///< bit-per-pixel(1,2,4,8,16,24,32)
    uint32_t                    scan_file_limit;            ///< scan file size limition
    uint32_t                    memory_low_cancel_enable;   ///< allows memory to cancel automatically 0-off 1-on

    SCAN_IMAGE_QUALITY_S        image_quality;              ///< scan image quality process

    void                        *email_info;                ///< scan to email param point
    void                        *ftp_info;                  ///< scan to ftp param point
    void                        *smb_info;                  ///< scan to smb param point

    SCAN_ENGINE_PARAM_S         scan_engine_param;          ///< scan engine param such as engine speed/rotate

}SCAN_JOB_DATA_S;

/*job data for print module*/
typedef struct
{
    // for main func
    PRINT_MODE_E                    print_mode;                                         ///< print mode such as single print/auto double print/manual double print
    PAPER_SIZE_E                    paper_size;                                         ///< paper's size
    PAPER_TYPE_E                    paper_type;                                         ///< paper's type
    PAPER_TYPE_E                    paper_cover_type;                                   ///< print paper type for cover and back pages such as plain paper/thick paper1...
    TRAY_INPUT_E                    paper_tray_in;                                      ///< print paper tray such as tray1/tray2...
    JOB_COLLATE_E                   collate;                                            ///< collate print /copy
    JOB_COLLATE_E                   user_collate;                                       ///< 0:OFF; 1: ON
    JOB_COLOR_MODE_E                color_mode;                                         ///< determine which color mode is the job
    uint32_t                        copies;                                             ///< print copies for print/copy
    uint32_t                        user_copies;                                        ///< 1-999
    uint32_t                        user_page_max_one_copy;                             ///< 1-999
    uint32_t                        current_copies;
    // for image func
    PRINT_DENSITY_E                 density;
    PRINT_MARGIN_E                  job_margin_type;

    // for special page func
    JOB_SEPARATOR_E                 separator_page;

    // for finisher func
    STAPLE_CONFIG_S                 staple_config;                                      ///< finisher optional parts such as binding/puch/fold
    PORTRAIT_DIRECTION_E            lorientation;                                       ///< 驱动输出横纵向画像及旋转180°

    // for shift func
    SHIFT_MODE_E                    shift_mode;                                         ///< print shift mode
    uint8_t                         cross_shift_revert;                                 ///< 0-not use shadow pages, 1-use shadow pages


    uint8_t                         sample_copy;

    // for print speed
    uint8_t                         fb_scan_or_batch_copy;                              ///< 1 开启分批复印或fb复印

    // for airprint &mopria func
    uint8_t                         fix_finisher_mode;                                 ///< fix finisher para during illagel job, 0: disable, 1: enable

    // for sample print func
    PRINT_SAMPLE_S                  print_sample;

    uint32_t                        total_pages;                                        ///< 记录作业总页数
    uint32_t                        printed_pages;                                      ///< 记录作业已打印页数
}PRINT_JOB_DATA_S;

/*job data struct for all jobs*/
typedef struct tag_job_data
{
    uint32_t                        cookie;                                             ///< A magic word to check struct memory is ok

    // for main func
    uint32_t                        job_id;                                             ///< job id is unique
    JOB_VIA_E                       job_via;                                            ///< job comes from port usb,net,wifi ...
    JOB_CLASS_E                     job_class;                                          ///< this is a copy,print,fax job
    char                            job_owner[OWNER_DESCRIPTION_MAX];                   ///< job owner name string
    char                            job_time[TIME_DESCRIPTION_MAX];                     ///< job time string,format:yyyy-mm-dd hh:mm

    // for sys func
    void                            *system_job_info;                                   ///< system job info point
    PAGEINFO_S                      sys_pages_info;                                     ///< system pages info
    ATTRIBUTE_INFO_S                sys_attribute_info;                                 ///< system attribute info
    PI_MUTEX_T                      mtx;                                                ///< Lock sys_pages_info and sys_attribute_info
    MEMOBJ_P                        image_memory_handle;                                ///< current job occupys memory handle

    // for pipe func
    uint8_t                         pipeline_number;                                    ///< current job need pipeline number
    char                            pipe_steps[IMAGE_PROCESS_STEPS_DESCRIPTION];        ///< current job need some step to process image,"jbig,compress..."
    char                            pipe_steps_sub[IMAGE_PROCESS_STEPS_DESCRIPTION];    ///< current sub job need some step to process image,"jbig,compress..."

    // for scan job
    SCAN_JOB_DATA_S                 scan_job_data;                                      ///< scan job data strcut

    // for print job
    PRINT_JOB_DATA_S                print_job_data;                                     ///< print job data struct

    // ### reserve #########################################################################################################################
    // for oem func
    void                            *oem_data;                                          ///< oem data point, cannot be used at will

    // unnecessary parm? we will remove them later
    JOB_STATUS_EVENT                job_status_event;                                   //who is interested in print job status,job start/cancel/done...
} JOB_DATA_S;

// public page data struct
typedef struct tag_page_data
{
    uint32_t                        cookie;                                             ///< A magic word to check struct memory is ok

    // for main func
    uint32_t                        page_id;                                            ///< print page id number
    uint32_t                        page_number;                                        ///< print module will add up page counter
    MODULE_ID_E                     page_source;
    PRINT_MODE_E                    print_mode;                                         ///< duplex print mode:0,single;1,auto duplex;2,manual duplex
    PAPER_SIZE_E                    paper_size;                                         ///< paper's size
    PAPER_TYPE_E                    paper_type;                                         ///< paper's type
    PAGE_TYPE_E                     page_type;                                          ///< page's type
    TRAY_INPUT_E                    tray_in;                                            ///< the tray_in choice which engine pick from
    TRAY_INPUT_E                    tray_in_original;                                   ///< the tray_in choice which came from user's chooice
    uint32_t                        copies;                                             ///< page copies

    // for image func
    ORIGINAL_ORIENTATION_E          original_orientation;                               ///< original orientation
    COPY_MARGIN_INFO_S              copy_pad_info;                                      ///< 6220 pip pad info
    PRINT_DENSITY_E                 density;                                            ///< image black degree,0,1,2,3,4
    int                             top_margin;                                         ///< top margin
    int                             left_margin;                                        ///< left margin
    IMAGE_S                         image;                                              ///< image data struct,this is a universal info
    uint16_t                        paper_width;                                        ///< paper width, is will be used during paper size is PAPER_SIZE_USER_DEFINEX
    uint16_t                        paper_height;                                       ///< paper height, is will be used during paper size is PAPER_SIZE_USER_DEFINEX

    // for special page func
    INSERT_PAGE_E                   insert_page;                                        ///< is it a empty page，used at separator_page

    // for finisher func
    STAPLE_CONFIG_S                 staple;                                             ///< staple config
    FLIP_MODE_E                     copies_flip;                                        ///< flip mode
    uint32_t                        rotation;                                           ///< 0->do nothing; 1->rotate 180 degrees

    // for shift func
    SHIFT_MODE_E                    shift_mode;                                         ///< print shift mode
    SHADOW_PAGE_E                   shadow_page;                                        ///< 0-not shadow page, 1-is shadow page, use in cross shift mode

    // for job_data
    JOB_DATA_S                      *current_job_data;                                  ///< this page belong to job

    // ### reserve #########################################################################################################################
    // for oem func
    void                            *oem_data;                                          ///< you can add oem data struct here if you need it

    // for other func, reserve
    uint32_t                        fine_field;                                         ///< from platform project, fine mode: 0 - off; 1 - on
    uint32_t                        paper_mismatch;                                     ///< from platform project, paper_mismatch:0 - off;1 - on
    uint32_t                        pip_mode;                                           ///< used for mix color job by pip on airprint & mopria
    uint32_t                        rotate_angle;                                       ///< used for print rotate

    // unnecessary parm? we will remove them later
    //uint32_t                        renderg2;                                         ///< render model for 600 or 600 2 bit
    //MARGINE_DATA                    margin[MAX_IMAGE_COMPONENTS];                     ///< margin left and top set
    uint32_t                        page_cmpbytestot;                                   ///< compressed page data-bytes
}PAGE_DATA_S;


typedef enum {
    SHEET_POSITION_FIRST        = 0x0,                          ///< the first sheet of the job
    SHEET_POSITION_MIDDLE_COLLATE_ON,                           ///< the middle sheets of the job
    SHEET_POSITION_MIDDLE_COLLATE_OFF,                          ///< the middle sheets of the job with collate off
    SHEET_POSITION_COPY_LAST,                                   ///< the last sheet of one copy
    SHEET_POSITION_LAST,                                        ///< the last sheet of the job
    SHEET_POSITION_FIRST_COPY_LAST,                             ///< the first, and the last sheet of one copy
    SHEET_POSITION_FIRST_LAST,                                  ///< the last, and the last sheet of the job

    SHEET_POSITION_INVALID,                                     ///< please keep it in the end of the enum --- invalid data
    SHEET_POSITION_MAX = 0xFFFFFFFF,                            ///< please keep it in the end of the enum --- invalid data

}SHEET_POSITION_E;


typedef enum {
    SEND_FINISHER_CMD_DISABLE       = 0x0,                      ///< no need to send finisher cmd
    SEND_FINISHER_CMD_ENABLE,                                   ///< need to send finisher cmd
    SEND_FINISHER_CMD_ENABLE_REMOVE_STAPLE_MODE,                ///< need to send finisher cmd, and we should remove the staple mode first

    SEND_FINISHER_CMD_ENABLE_INVALID,                           ///< please keep it in the end of the enum --- invalid data
}SEND_FINISHER_CMD_E;




//keep sheet config
typedef struct tag_sheet_data
{
    uint32_t            sheet_number;                           ///< sheet count number in a job

    SHEET_STATE_E       receive_state;                          ///< sheet receive state,back-front-done
    PAGE_DATA_S         *front_page;                            ///< front page pointer
    PAGE_DATA_S         *back_page;                             ///< back page  pointer

    PRINT_MODE_E        print_mode;                             ///< one sheet need 8 params to do once print
    PAPER_SIZE_E        paper_size;                             ///< mode,size,type,width,height,density,in,out
    PAPER_TYPE_E        paper_type;
    JOB_COLLATE_E       collate;
    uint16_t            paper_width;
    uint16_t            paper_height;
    PRINT_DENSITY_E     page_desity;
    TRAY_INPUT_E        paper_tray_in;

    uint32_t            copies;                                 ///< sheet copies
    uint32_t            sent_count;                            ///< have sent copies
    uint32_t            printed_count;                          ///< print done copies
    uint32_t            renderg2;
    uint32_t            fine_field;
    uint32_t            paperMismatch;
    uint8_t             paper_space_ex;                         /* 纸间距扩展时间，用于AirPrint打印时根据需要延长纸间距，
                                                                   以达到控制打印速度的目的,单位为 100毫秒，即100毫秒的倍数，默认为0 */
    uint32_t            page_cmpbytestot;                       ///< 记录页的压缩的字节数  用于AirPrint打印时根据需要延长纸间距 2016.7.6  zw

    //PRINT_FORCE_STATUS  print_force_status;                     ///< record the force print status
    uint32_t            job_number;                             ///< job number
    TRAY_INPUT_E        tray_in_original;                       ///< the tray which come from user's chooice --- Add by Zhu.Ran, 20191115

    /*Begin: Zhu.Ran, 20200425, for finisher function*/
    STAPLE_CONFIG_S     staple;                                 ///< staple config from driver

    signed char         flag_sent_full_sheets;                  ///< a flag about whehther have sent full(20/50/...) sheets, 0-NO; 1-YES

    SHEET_POSITION_E    sheet_position;                         ///< 0 first sheet; 1 middle sheet; 2 last sheet
    SEND_FINISHER_CMD_E send_finisher_cmd;

    uint8_t             flag_sheet_copy_index;                ///< 0->no copy or no collate; 1->first copy; 2->second copy; 3->third copy......
    uint8_t             flag_have_finisher_info;                ///< 0, no finisher info; 1, have finisher info

    JOB_CLASS_E         job_class;                              ///< this is a copy,print,fax job
    signed int          user_collate;                           ///< 0:OFF; 1: ON
    uint32_t            user_copies;                            ///< 1-999
    uint32_t            user_page_max_one_copy;                 ///< -1-999, for copy module, -1 means not ready; 1-999, for IPS module
    /*End: Zhu.Ran, 20200425, for finisher function*/

#ifdef IN_COLLATE_SAVE
    uint8_t             save_done;
    uint8_t             proc_done;
#endif
}SHEET_DATA_S;

//a job data to manage print jobs
JOB_DATA_S *job_create( void );

//destroy job data memory
int job_destroy( JOB_DATA_S *job );

//a page data to store print pages
PAGE_DATA_S *page_create( void );

//destroy page data memory
int page_destroy( PAGE_DATA_S *page );

//copy page demmension
int page_copy_dimension( PAGE_DATA_S *dst, PAGE_DATA_S *src );

//create a sheet data by malloc
SHEET_DATA_S *SheetCreate( void );

//destroy sheet data memory
int SheetDestroy( SHEET_DATA_S * sheet );

///< get paper width and height
int get_paper_w_h(PAPER_SIZE_E paper_size, PAPER_UNIT_E unit, int x_dpi, int y_dpi, int *paper_width, int *paper_height);
///< get paper width
int get_paper_w(PAPER_SIZE_E paper_size, PAPER_UNIT_E unit, int x_dpi);
///< get paper height
int get_paper_h(PAPER_SIZE_E paper_size, PAPER_UNIT_E unit, int y_dpi);

///< get paper size
int get_paper_size(int w, int h);

///< convert IO_VIA_E to JOB_VIA_E
JOB_VIA_E iovia_to_jobvia(IO_VIA_E via);

///< get paper type string
char *get_page_type_string(int page_type);

//add coming bands to current page data
//int BandAddToPage( BAND *band, PAGE_DATA_S *page );

#ifdef __cplusplus
}
#endif

#endif

