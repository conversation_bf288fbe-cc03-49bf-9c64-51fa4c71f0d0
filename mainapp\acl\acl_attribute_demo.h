#ifndef _IN_ACL_ATTRIBUTE_DEMO_H_
#define _IN_ACL_ATTRIBUTE_DEMO_H_

#include "pol/pol_types.h"

/*uint32 value callback define*/
int32_t demo_set_uint32_value(uint32_t value);

int32_t demo_get_uint32_value(uint32_t* value);

/*uint32 index callback define*/
int32_t demo_set_uint32_index(uint32_t index, uint32_t value);

int32_t demo_get_uint32_index(uint32_t index, uint32_t* value);

/*string value callback define*/
int32_t demo_set_string_value(char* value, uint32_t len);

int32_t demo_get_string_value(char* value, uint32_t len);

/*string index callback define*/
int32_t demo_set_string_index(uint32_t index, char* value, uint32_t len);

int32_t demo_get_string_index(uint32_t index, char* value, uint32_t len);


#endif
