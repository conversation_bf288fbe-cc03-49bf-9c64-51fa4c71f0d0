#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netsts.h"
#include "http_task.h"
#include "escldef.h"
#include "escljob.h"
#include "power_manager/power_manager.h"

#define ESCL_IMAGE_DATA_SIZE    ( 0x2000 )                      /* 8 * 1024 */
#define ESCL_CHUNK_BUF_SIZE     ( ESCL_IMAGE_DATA_SIZE + 8 )    /* length(4bytes) + \r\n + image data + \r\n */

#define ESCL_MLOCK_UN()    { if (s_esclsrv_ctx && s_esclsrv_ctx->escl_mtx != INVALIDMTX) pi_mutex_unlock(s_esclsrv_ctx->escl_mtx); }
#define ESCL_MLOCK_EX()    { if (s_esclsrv_ctx && s_esclsrv_ctx->escl_mtx != INVALIDMTX) pi_mutex_lock(s_esclsrv_ctx->escl_mtx);   }

typedef int32_t (*REPLY_FUNC)(HTTP_TASK_S* ptask, int32_t jobid);

typedef struct esclsrv_task_private
{
    REPLY_FUNC  reply_image;
    int32_t     reply_jobid;
    char        iobuf[TCP_CHUNK_SIZE];
    size_t      received;
}
PRIV_INFO_S;

typedef struct esclsrv_context
{
    NET_CTX_S*      net_ctx;
    MODULE_ID_E     mid;
    PI_MUTEX_T      escl_mtx;
}
ESCLSRV_CTX_S;

static ESCLSRV_CTX_S*   s_esclsrv_ctx = NULL;

static inline int32_t parse_joburl(const char* joburl)
{
    int32_t jobid = -1;

    if ( sscanf(joburl, "/eSCL/ScanJobs/%d", &jobid) != 1 || jobid <= 0 )
    {
        NET_WARN("parsed jobid(%d) in joburl(%s)", jobid, joburl);
    }

    return jobid;
}

static int32_t _esclsrv_reply_image(HTTP_TASK_S* ptask, ESCL_JOB_S* pejob, int32_t jobid)
{
    char                chunk_buffer[ESCL_CHUNK_BUF_SIZE];
    char                file[256];
    FILE*               stream = NULL;
    int32_t             file_type = 0;
    int32_t             last_page = 0;
    int32_t             retry = 0;
    int32_t             nsend = 0;
    size_t              nread = 0;

    do
    {
        file_type = escljob_get_file_type(pejob);
        if ( escljob_takes_page(pejob, file, sizeof(file), &last_page) != 0 )
        {
            if ( file_type != FILE_PDF )
            {
                NET_WARN("job %d page list empty, document format(%d)",jobid, file_type);
                break;
            }
            else
            {
                if ( retry++ > 120 )
                {
                    NET_INFO("retry++ > 120");
                    return -1;
                }
                NET_DEBUG("waiting exit page for PDF document");
                pi_sleep(1);
                continue;
            }
        }
        retry = 0;

        stream = pi_fopen(file, "r");
        if ( stream == NULL )
        {
            NET_WARN("open scan file(%s) failed: %d<%s>", file, errno, strerror(errno));
            return -1;
        }

        do
        {
            nread = fread(chunk_buffer + 6, 1, ESCL_IMAGE_DATA_SIZE, stream);
            if ( nread > 0 )
            {
                NET_TRACE("read %u bytes from file(%s)", nread, file);
                snprintf(chunk_buffer, ESCL_CHUNK_BUF_SIZE, "%04X", nread);
                chunk_buffer[4] = '\r';
                chunk_buffer[5] = '\n';
                chunk_buffer[6 + nread] = '\r';
                chunk_buffer[7 + nread] = '\n';

                nsend = http_task_send(ptask, chunk_buffer, nread + 8);
                if ( nsend != nread + 8 )
                {
                    NET_WARN("http_task_send(%u) failed(%d)", nread, nsend);
                    escljob_delete(pejob, AIRSCAN_TRAN_ERROR);
                    break;
                }
            }
        }
        while ( nread == ESCL_IMAGE_DATA_SIZE );
        pi_fclose(stream);
        unlink(file);
    }
    while ( file_type == FILE_PDF && last_page == 0 );
    if ( nsend < 0 )
    {
        NET_WARN("nsend < 0");
        return -1;
    }

    nsend = http_task_send(ptask, "0\r\n\r\n", 5);
    if ( nsend != 5 )
    {
        NET_WARN("http_send failed end failed(%d)", nsend);
        escljob_delete(pejob, AIRSCAN_TRAN_ERROR);
        return -1;
    }
    pi_msleep(300);

    return 0;
}

static int32_t esclsrv_reply_image(HTTP_TASK_S* ptask, int32_t jobid)
{
    int ret = -1;
    ESCL_JOB_S* pejob;

    ESCL_MLOCK_EX();

    NET_DEBUG("job %d reply image", jobid);

    pejob = escljob_search(jobid);
    if ( pejob )
    {
        ret = _esclsrv_reply_image(ptask, pejob, jobid);
        if (ret == 0)
        {
            escljob_timer_restart(pejob);
        }
    }
    else
    {
        NET_WARN("pejob == NULL");
    }

    ESCL_MLOCK_UN();

    return ret;
}

static int32_t esclsrv_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    priv->reply_image = NULL;
    priv->reply_jobid = 0;
    priv->received    = 0;

    return 0;
}

static int32_t esclsrv_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        NET_WARN("request body overlength(%u + %u) from client(%u->%u)", priv->received, ndata, ptask->r_port, ptask->l_port);
        return -1;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;

    return 0;
}

static int32_t esclsrv_get_scanner_capabilities(HTTP_TASK_S* ptask, int32_t* rlen)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);
    const char* capabilities_format = ESCL_RESPONSE_DADF_CAPABILITES_TEMPLATE;
    char        bonjour_svcname[128];
    char        adminurl[128];
    char        icon_uri[128];
    char        mfg_name[16];
    char        pdt_name[32];
    char        uuid[64];

    RETURN_VAL_IF(priv == NULL, NET_WARN, 500);

    netdata_get_bonjour_server(DATA_MGR_OF(s_esclsrv_ctx), bonjour_svcname, sizeof(bonjour_svcname));
    netdata_get_mfg_name(DATA_MGR_OF(s_esclsrv_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(DATA_MGR_OF(s_esclsrv_ctx), pdt_name, sizeof(pdt_name));
    netdata_get_uuid(DATA_MGR_OF(s_esclsrv_ctx), uuid, sizeof(uuid));

    snprintf(icon_uri, sizeof(icon_uri), "http://%s.local:%d/img/%s128.png", bonjour_svcname, HTTP_PORT, pdt_name);
    snprintf(adminurl, sizeof(adminurl), "http://%s.local:%d/index.html",    bonjour_svcname, HTTP_PORT);

    *rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), capabilities_format, mfg_name, pdt_name, mfg_name, uuid, adminurl, icon_uri);
    RETURN_VAL_IF(*rlen >= sizeof(priv->iobuf), NET_WARN, 500);

    return 200;
}

static int32_t esclsrv_get_scanner_status(HTTP_TASK_S* ptask, int32_t* rlen)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);
    NETSTS_PACKET_S packet = { .count = 0 };
    STATUS_ID_E     error_status;
    const char*     pwg_state;
    const char*     adf_state;
    char            jobs_info[4096];
    int32_t         active_jobs;

    RETURN_VAL_IF(priv == NULL, NET_WARN, 500);

    active_jobs = escljob_get_jobs_info(jobs_info, sizeof(jobs_info));
    NET_DEBUG("active_jobs(%d)", active_jobs);

    RETURN_VAL_IF(netsts_take_packet(&packet) < 0, NET_WARN, 500);

    error_status = netsts_check_module_error(&packet, STATUS_ID_MODULE_SCAN);
    if ( error_status != 0 )
    {
        pwg_state = "Stopped";
    }
    else if ( active_jobs > 0 || netsts_check_scan_running(&packet) )
    {
        pwg_state = "Processing";
    }
    else
    {
        pwg_state = "Idle";
    }
    NET_DEBUG("pwg_state(%s)", pwg_state);


    switch ( error_status )
    {
    case STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT:
    case STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK:
        adf_state = "ScannerAdfMispick";
        break;
    case STATUS_E_SCAN_ADF_COVER_OPEN:
        adf_state = "ScannerAdfHatchOpen";
        break;
    case STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN:
        adf_state = "ScannerAdfJam";
        break;
    default:
        if ( scan_status_get_adf_paper_status() == ADF_HAVE_PAPER )
        {
            if ( strcmp(pwg_state, "Processing") == 0 )
            {
                adf_state = "ScannerAdfProcessing";
            }
            else
            {
                adf_state = "ScannerAdfLoaded";
            }
        }
        else
        {
            adf_state = "ScannerAdfEmpty";
        }
        break;
    }
    NET_DEBUG("adf_state(%s)", adf_state);

    *rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), ESCL_RESPONSE_STATUS_TEMPLATE, pwg_state, adf_state, jobs_info);
    RETURN_VAL_IF(*rlen >= sizeof(priv->iobuf), NET_WARN, 500);

    return 200;
}

static int32_t esclsrv_get_scan_job(HTTP_TASK_S* ptask, const char* url, const char** rtype)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);
    ESCL_JOB_S* pejob = NULL;
    char        filepath[256];
    int32_t     jobid = parse_joburl(url);
    int32_t     rcode = 404;
    int32_t     pages = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, 500);
    RETURN_VAL_IF(jobid <= 0, NET_WARN, 404);

    pejob = escljob_search(jobid);
    RETURN_VAL_IF(pejob == NULL, NET_WARN, 404);

    escljob_timer_stop(pejob);
    pages = escljob_get_page_num(pejob, &rcode);
    if (pages <= 0 )
    {
        if (rcode == 404)  // had transfer all pages
        {
            escljob_finish(pejob);
        }
        else
        {
            escljob_timer_restart(pejob);
        }
        NET_DEBUG( "pages <= 0");
        return rcode;
    }

    snprintf(filepath, sizeof(filepath), "/eSCL/ScanJobs/%d/photo-%d", jobid, pages);
    NET_DEBUG("Content-Location(%s) rcode(%d)", filepath, rcode);

    http_task_append_resp_headers(ptask, "Transfer-Encoding", "chunked");
    http_task_append_resp_headers(ptask, "Accept-Ranges", "bytes");
    http_task_append_resp_headers(ptask, "Content-Location", filepath);

    priv->reply_image = esclsrv_reply_image;
    priv->reply_jobid = jobid;

    if ( escljob_get_file_type(pejob) == FILE_PDF )
    {
        *rtype = "application/pdf";
    }
    else
    {
        *rtype = "image/jpeg";
    }
    return 200;
}

static int32_t esclsrv_delete_scan_job(HTTP_TASK_S* ptask, const char* url)
{
    int32_t jobid = parse_joburl(url);

    RETURN_VAL_IF(jobid <= 0, NET_WARN, 404);
    escljob_delete(escljob_search(jobid), AIRSCAN_CANCEL);

    return 200;
}

static int32_t esclsrv_post_scan_job(HTTP_TASK_S* ptask)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);
    NETSTS_PACKET_S packet = { .count = 0 };
    const char*     host_val = NULL;
    char            location[128];
    int32_t         jobid = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, 500);

    RETURN_VAL_IF(netsts_take_packet(&packet) < 0, NET_WARN, 500);

    RETURN_VAL_IF(netsts_check_module_error(&packet, STATUS_ID_MODULE_SCAN), NET_WARN, 500);

    RETURN_VAL_IF(netsts_check_scan_running(&packet), NET_WARN, 503);

    host_val = http_task_search_header_field(ptask, "Host");
    RETURN_VAL_IF(STRING_IS_EMPTY(host_val), NET_WARN, 500);

    NET_DEBUG("host(%s) from client(%u->%u)", host_val, ptask->r_port, ptask->l_port);
    RETURN_VAL_IF((jobid = escljob_start(priv->iobuf)) <= 0, NET_WARN, 500);

    snprintf(location, sizeof(location), "%s://%s/eSCL/ScanJobs/%d", (ptask->l_port == TLS_PORT) ? "https" : "http", host_val, jobid);
    http_task_append_resp_headers(ptask, "Location", location);
    NET_DEBUG("location(%s)", location);

    return 201;
}

static int32_t esclsrv_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV3(ptask, PRIV_INFO_S, priv);
    const char* reply_type = "text/xml";
    const char* reply_code = "200 OK";
    int32_t     reply_len = 0;
    int32_t     rcode = 400;
    int32_t     ret = 0;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(url), NET_WARN, -1);
    priv->iobuf[priv->received] = '\0';

    NET_DEBUG("method(%s) url(%s) body received(%u) from client(%u->%u)", http_method_str(method), url, priv->received, ptask->r_port, ptask->l_port);
    if ( strstr( url, "/eSCL/ScannerCapabilities") != NULL )
    {
        rcode = esclsrv_get_scanner_capabilities(ptask, &reply_len);
    }
    else if ( strstr(url, "/eSCL/ScannerStatus") != NULL )
    {
        rcode = esclsrv_get_scanner_status(ptask, &reply_len);
    }
    else if ( strstr(url, "/eSCL/ScanJob") != NULL )
    {
        switch ( method )
        {
            case HTTP_GET:      rcode = esclsrv_get_scan_job(ptask, url, &reply_type);  break;
            case HTTP_DELETE:   rcode = esclsrv_delete_scan_job(ptask, url);            break;
            case HTTP_POST:     rcode = esclsrv_post_scan_job(ptask);                   break;
            default: NET_WARN("invalid method(%s)", http_method_str(method));           break;
        }
    }
    else
    {
        NET_WARN("invalid url(%s)", url);
    }

    switch ( rcode )
    {
        case 503: reply_code = "503 Service Unavailable";   ret = -1; break;
        case 500: reply_code = "500 OUCH";                  ret = -1; break;
        case 408: reply_code = "500 Internal Server Error"; ret = -1; break;
        case 410: reply_code = "410 Gone";                  ret =  0; break;
        case 404: reply_code = "404 WHAT";                  ret = -1; break;
        case 400: reply_code = "400 Bad Request";           ret = -1; break;
        case 304: reply_code = "304 NOT MODIFIED";          ret =  0; break;
        case 201: reply_code = "201 CREATED";               ret =  0; break;
        case 200: reply_code = "200 OK";                    ret =  0; break;
        default: NET_WARN("invalid rcode(%d)", rcode);      ret = -1; break;
    }
    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( priv->reply_image != NULL )
    {
        ret = priv->reply_image(ptask, priv->reply_jobid);
    }
    else if ( reply_len > 0 )
    {
        http_task_send(ptask, priv->iobuf, reply_len);
    }

    return ret;
}

int32_t esclsrv_construct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[3] == NULL )
    {
        ptask->priv_subclass[3] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[3] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = esclsrv_process_headers;
    ptask->reqbody_received_callback = esclsrv_process_reqbody;
    ptask->request_complete_callback = esclsrv_process_request;

    return 0;
}

void esclsrv_destruct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    if ( ptask != NULL && ptask->priv_subclass[3] != NULL )
    {
        pi_free(ptask->priv_subclass[3]);
        ptask->priv_subclass[3] = NULL;
    }
}

int32_t esclsrv_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_esclsrv_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_esclsrv_ctx = (ESCLSRV_CTX_S *)pi_zalloc(sizeof(ESCLSRV_CTX_S));
    RETURN_VAL_IF(s_esclsrv_ctx == NULL, NET_WARN, -1);

    do
    {
        s_esclsrv_ctx->net_ctx = net_ctx;

        BREAK_IF((s_esclsrv_ctx->escl_mtx = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        BREAK_IF(msg_router_register(MID_AIRSCAN) < 0, NET_WARN);
        s_esclsrv_ctx->mid = MID_AIRSCAN;

        BREAK_IF(escljob_thread_create() < 0, NET_WARN);
        ret = 0;
    }
    while ( 0 );

    NET_INFO("esclsrv initialize result(%d)", ret);
    if ( ret != 0 )
    {
        esclsrv_epilog();
    }
    return ret;
}

void esclsrv_epilog(void)
{
    if ( s_esclsrv_ctx != NULL )
    {
        if ( s_esclsrv_ctx->mid > 0 )
        {
            msg_router_unregister(s_esclsrv_ctx->mid);
        }
        if ( s_esclsrv_ctx->escl_mtx != INVALIDMTX )
        {
            pi_mutex_destroy(s_esclsrv_ctx->escl_mtx);
        }
        pi_free(s_esclsrv_ctx);
        s_esclsrv_ctx = NULL;
        escljob_thread_destroy();
    }
}

