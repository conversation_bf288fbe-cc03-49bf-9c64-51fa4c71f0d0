/**
 * @Copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file usbhservice.h
 * @addtogroup usb
 * @{
 * @addtogroup usbdserver
 * @autor       WenQi
 * @date        2012/12/21
 * @brief       API to Generic USB Host server.  Allows applications to register callback
                functions to a single server thread which polls for device plug events and
                reconciles device classes with registered interest
 */
#ifndef USBHSERVICE_H
#define USBHSERVICE_H 1

#include "pol/pol_types.h"

#define USBH_MAX_PORTS  20    ///< total number of ports we look at (in embedded RTOS really)

#define USBH_MAX_NAME   64    ///< max usb host device name

/**
 * @brief   element in list of usb devices plugged in (one per interface)
 */
typedef struct tag_usbhdevice
{
    char    name[USBH_MAX_NAME];    ///< name of device provided by device
    int     vid;                    ///< vid
    int     pid;                    ///< pid
    int     usbclass;               ///< usb class
    int     usbsubclass;            ///< usb class sub from device 
    int     port;                   ///< which port device is in (rtos systems)
    int     usbport;                ///< which port device is in (linux systems)
    struct tag_usbhdevice *next;    ///< link
}
USBHDEVICE_S, *USBHDEVICE_P;
extern unsigned char figer_module;      ///<static unsigned char figer_module; //fingerprint indicates that the module is connected

#define USBH_CLASS_MASS_STORAGE 0x08    ///< device classes we care about

/**
 * @brief   instantiation of one USB Device Service Connection
 */
typedef struct tag_USBHconnection
{
    int             usbclass;       ///< what class this device is   
    void           *service;        ///< the service installed this connection is for
    int             port;           ///< which port device is in (rtos system)
    int             usbport;        ///< which port device is in (linux systems)
    struct tag_USBHconnection       ///< link
                   *next;
}
USBHCONNECTION_S, *USBHCONNECTION_P;
/**
 * @brief   Callback function type for function called when device inserted
 */
typedef int (*PUSBHSERVICECB)(USBHCONNECTION_P pnc, int port);

/**
 * @brief   Add a service to the server for a particular class of usb devices.
            When a device of that class is inserted into a usb host port
            the callback function is called with the connection object.
 * @param[in] name      :name of device provided by device
 * @param[in] usbclass  :usb class
 * @param[in] port      :which port device is in
 * @param[in] callback  :function to call when device is connected
 * @return  Construct result
 * @retval  =0 : success
 * @retval  <0 : fail
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
int         pi_usbh_server_add_service        (
                                        char* name,
                                        int usbclass,
                                        int port,
                                        PUSBHSERVICECB callback
                                        );
/**
 * @brief   Remove a registered service for a class of devices
 * @param[in] usbclass  :usb class
 * @param[in] port      :which port device is in
 * @return  Construct result
 * @retval  =0 : success
 * @retval  <0 : fail
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
int         pi_usbh_server_remove_service     (int usbclass, int port);
/**
 * @brief   Called to indicate to the server that the connection passed to the
            application in a connect callback is no longer being used
 * @param[in] pnc      :Instantiation of one USB Host Service Connection
 * @return  Construct result
 * @retval  =0 : success
 * @retval  <0 : fail
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
int         pi_usbh_server_end_connection     (USBHCONNECTION_P pnc);
/**
 * @brief   one-time initialization of the usb host server system
 * @return  Construct result
 * @retval  =0 : success
 * @retval  <0 : fail
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
int         pi_usbh_server_prolog            (void);
/**
 * @brief   close/reset the usb host server system
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
void        usbh_server_epilog            (void);
/**
 * @brief   
 * @return  Construct result
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
int pi_usbh_server_have_connection();
/**
 * @brief   probe the connected usb device 
 * @param[in] usbport  :which port device is in
 * @return  Construct result
 * @retval  =0 : success
 * @retval  <0 : fail
 * <AUTHOR> Qi
 * @date    2023-11-8
 */
int pi_usbh_probe_devices(int usbport);

/**
 * @brief   check whether wifi modules exist
 * <AUTHOR> Qi
 * @date    2024-2-29
 */
int pi_usbh_server_check_wifi       (void);


//int pi_get_mount_udisk_cmd( char* buffer, unsigned int len, int port );
//int pi_get_umount_udisk_cmd( char* buffer, unsigned int len, int port );
//unsigned long long pi_automount_get_udisk_availabel_size();
//void pi_automount_set_udisk_availabel_size(unsigned long long udisk_size);
//int32_t pi_get_udisk_status(uint32_t* value);
//int pi_automounter_prolog(void);
//void notify_usbmounter_status(int status);

#endif

/**
 * @}
 */

