/**************************************************************
Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
file name:       app_manager.h
date:            2024-2-19
**************************************************************/
#ifndef __APP_MANAGER_H__
#define __APP_MANAGER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include "cjson/cJSON.h"

#if 0
cJSON* am_get_app_list(void);
int am_delete_app(char* appId);
void am_set_app_list(cJSON* applist);

int am_list_find_appid(cJSON* applist, char* appid);
void am_find_auto_boot_app(char** name);
int pesf_get_app_list(cJSON** buffer);
int pesf_get_app_info(cJSON** buffer, char* appname);
char* am_list_find_rtid(char* appid);
char* am_get_app_workspace_by_rtid(char* appid);
#endif
int am_start_app(char* app_name);
void app_manager_init(void);
void app_init(void);

#ifdef __cplusplus
}
#endif

#endif


