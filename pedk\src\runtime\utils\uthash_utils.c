#include "basic/log/log.h"
#include "runtime/utils/uthash_utils.h"
#include "basic/hash/uthash.h"

typedef struct RUNTIME_HASH {
    uint32_t id;
    PeSFRunTime* rt;
    UT_hash_handle hh;
} RUNTIME_HASH;

static RUNTIME_HASH* hash_table = NULL;

/* 将运行时插入哈希表中，并分配一个rtid返回，rtid即哈希表的查询id。
   rtid范围是1~256，如果app达到最大数则返回0。
*/
uint32_t hash_add(PeSFRunTime* rt)
{
    RUNTIME_HASH* rth = (RUNTIME_HASH*)malloc(sizeof(RUNTIME_HASH));
    uint32_t rtid = 0;
    RUNTIME_HASH* trth = NULL;

    // 从1开始分配rtid，最多MAX_APP_NUM个。
    for (rtid = 1; rtid <= MAX_APP_NUM; rtid++) {
        HASH_FIND_INT(hash_table, &rtid, trth);
        if (trth == NULL) {
            break;
        }
    }

    // 如果达到最大值，则返回0
    if (rtid > MAX_APP_NUM) {
        LOG_E("uthash_utils", "APPs has reached MAX");
        rtid = 0;
    } else {
        // 将运行时存入哈希表
        rth->rt = rt;
        rth->id = rtid;
        HASH_ADD_INT(hash_table, id, rth);
    }

    // 返回rtid
    return rtid;
}

// 依据rtid,查找运行时并返回，如果没找到，则返回NULL
PeSFRunTime* hash_find(uint32_t rtid)
{
    RUNTIME_HASH* rth = NULL;
    PeSFRunTime* rt;

    HASH_FIND_INT(hash_table, &rtid, rth);

    if (NULL != rth) {
        rt = rth->rt;
    } else {
        rt = NULL;
    }

    return rt;
}

// 从哈希表中删除
int32_t hash_del(uint32_t rtid)
{
    RUNTIME_HASH* rth = NULL;
    int32_t ret = 0;

    HASH_FIND_INT(hash_table, &rtid, rth);

    if (rth != NULL) {
        HASH_DEL(hash_table, rth);
    } else {
        ret = -1;
    }

    free(rth);

    return ret;
}

// 删除整个哈希表
int32_t hash_clear()
{
    RUNTIME_HASH* rth;
    RUNTIME_HASH* tmp;

    HASH_ITER(hh, hash_table, rth, tmp)
    {
        HASH_DEL(hash_table, rth);
    }

    return 0;
}

// 遍历整个哈希表
#define PESF_ITER(prt) for (prt = hash_iter(); prt; prt = hash_iter())
PeSFRunTime* hash_iter()
{
    static RUNTIME_HASH* rth = NULL;

    if (rth == NULL) {
        rth = hash_table;
        if (rth == NULL) {
            return NULL;
        } else {
            return rth->rt;
        }
    } else {
        rth = rth->hh.next;
        if (rth == NULL) {
            return NULL;
        } else {
            return rth->rt;
        }
    }
}