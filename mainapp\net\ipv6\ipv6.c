/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ipv6.c
 * @addtogroup net
 * @{
 * @addtogroup ipv6
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network IPv6 address manager API
 */
#include "nettypes.h"
#include "netmisc.h"
#include "ipv6.h"

#define DHCP_IPV6_SCRIPT_FILE   "/usr/share/udhcpc/dhcpv6.script"
#define DHCP_PID_FILE_FORMAT    NET_BASE_DIR "/udhcpc6.pid.%s"

static char s_stls_suffix[IFACE_ID_NUM][2][20];

/**
 * @brief       update the suffix of stateless address for this network interface index(ifid).
 * @param[in]   ifid    : This network interface index.
 * <AUTHOR> <PERSON>n
 * @date        2023-9-19
 */
static void update_stls_suffix(IFACE_ID_E ifid)
{
    const char* ifname = IFACE_NAME(ifid);
    uint8_t     mac[6];
    uint16_t    val;

    RETURN_IF(net_ifctl_get_mac(ifname, mac, sizeof(mac)) != 0, NET_WARN);

    mac[0] = (mac[0] & 0x02) ? (mac[0] & 0xfd) : (mac[0] | 0x02);
    val = ((uint16_t)mac[0] << 8) | mac[1];
    if ( val != 0 )
    {
        snprintf(s_stls_suffix[ifid][0], sizeof(s_stls_suffix[ifid][0]), "%04x:%04x:%04x:%04x", val, ((uint16_t)mac[2]<<8)|0xff, 0xfe00|mac[3], ((uint16_t)mac[4]<<8)|mac[5]);
        snprintf(s_stls_suffix[ifid][1], sizeof(s_stls_suffix[ifid][1]), "%x:%x:%x:%x",         val, ((uint16_t)mac[2]<<8)|0xff, 0xfe00|mac[3], ((uint16_t)mac[4]<<8)|mac[5]);
    }
    else
    {
        snprintf(s_stls_suffix[ifid][0], sizeof(s_stls_suffix[ifid][0]), "%04x:%04x:%04x", ((uint16_t)mac[2]<<8)|0xff, 0xfe00|mac[3], ((uint16_t)mac[4]<<8)|mac[5]);
        snprintf(s_stls_suffix[ifid][1], sizeof(s_stls_suffix[ifid][1]), "%x:%x:%x",       ((uint16_t)mac[2]<<8)|0xff, 0xfe00|mac[3], ((uint16_t)mac[4]<<8)|mac[5]);
    }
    NET_DEBUG("'%s' state less address suffix: %s", ifname, s_stls_suffix[ifid][1]);
}

/**
 * @brief       update the DHCP address for this network interface index(ifid).
 * @param[in]   ifid    : This network interface index.
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static void clear_dhcp_address(IFACE_ID_E ifid)
{
    const char*     ifname = IFACE_NAME(ifid);
    FILE*           stream;
    struct in6_addr addrv6;
    char            ipstr[IPV6_ADDR_LEN];
    char            name[IF_NAMESIZE];
    uint32_t        index;
    uint32_t        prefixlen;
    uint32_t        scope_value;
    uint32_t        flag;

    RETURN_IF((stream = pi_fopen("/proc/net/if_inet6", "r")) == NULL, NET_WARN);

    /* eg. fe80000000000000aec51bfffeda0040 02 40 20 80     eth0 */
    while (
            13 == fscanf(stream, "%4s%4s%4s%4s%4s%4s%4s%4s %02x %02x %02x %02x %15s\n",
                &ipstr[0],  &ipstr[5],  &ipstr[10], &ipstr[15],
                &ipstr[20], &ipstr[25], &ipstr[30], &ipstr[35],
                &index, &prefixlen, &scope_value, &flag, name)
          )
    {
        if ( strcmp(name, ifname) == 0 && strncasecmp(ipstr, "fe80", 4) != 0 ) /* 网口名称匹配 && 不是本地链路地址 */
        {
            ipstr[4] = ipstr[9] = ipstr[14] = ipstr[19] = ipstr[24] = ipstr[29] = ipstr[34] = ':';
            ipstr[39] = '\0';
            if ( strstr(ipstr, s_stls_suffix[ifid][0]) == NULL ) /* 不是无状态地址 */
            {
                inet_pton(AF_INET6, ipstr, &addrv6);
                inet_ntop(AF_INET6, &addrv6, ipstr, sizeof(ipstr));
                pi_runcmd(NULL, 0, 0, "ifconfig %s del %s/%d > /dev/null 2>&1", ifname, ipstr, prefixlen);
            }
        }
    }
    pi_fclose(stream);
}

int32_t net_ipv6_switch(IFACE_ID_E ifid, IPV6_SWITCH_E on)
{
    const char* ifname = IFACE_NAME(ifid);
    const char* val;
    char        dev[64];
    ssize_t     len;
    int32_t     fd;

    RETURN_VAL_IF(STRING_IS_EMPTY(ifname), NET_WARN, -1);

    snprintf(dev, sizeof(dev), "/proc/sys/net/ipv6/conf/%s/disable_ipv6", ifname);
    RETURN_VAL_IF((fd = pi_open(dev, O_WRONLY)) < 0, NET_WARN, -1);

    val = (on == IPV6_SWITCH_OFF ? "1" : "0");
    len = write(fd, val, strlen(val));
    pi_close(fd);
    RETURN_VAL_IF(len <= 0, NET_WARN, -1);

    if ( on )
    {
        update_stls_suffix(ifid);
    }
    return 0;
}

void net_ipv6_start_dhcp(IFACE_ID_E ifid)
{
    const char* ifname = IFACE_NAME(ifid);
    char        pidfile[128];

    RETURN_IF(STRING_IS_EMPTY(ifname), NET_WARN);

    snprintf(pidfile, sizeof(pidfile), DHCP_PID_FILE_FORMAT, ifname);
    RETURN_IF(check_program_exist(pidfile), NET_WARN);

    pi_runcmd(NULL, 0, 1, "udhcpc6 -i %s -s " DHCP_IPV6_SCRIPT_FILE " -p %s > /dev/null 2>&1", ifname, pidfile);
    waiting_program_start(pidfile, 10, 200);
}

void net_ipv6_stop_dhcp(IFACE_ID_E ifid)
{
    const char* ifname = IFACE_NAME(ifid);
    char        pidfile[128];

    RETURN_IF(STRING_IS_EMPTY(ifname), NET_WARN);

    snprintf(pidfile, sizeof(pidfile), DHCP_PID_FILE_FORMAT, ifname);
    waiting_program_stop(pidfile, 5, 500);
    clear_dhcp_address(ifid);
}

int32_t net_ipv6_is_stls_addr(IFACE_ID_E ifid, const char* addr)
{
    const char* suffix;
    uint32_t    ret;

    RETURN_VAL_IF(STRING_IS_EMPTY(addr), NET_WARN, 0);

    suffix = strstr(addr, s_stls_suffix[ifid][1]);
    if ( suffix != NULL && strcmp(suffix, s_stls_suffix[ifid][1]) == 0 )
    {
        ret = 1;
    }
    else
    {
        ret = 0;
    }

    return ret;
}

int32_t net_ipv6_get_dns(IFACE_ID_E ifid, char (*dns)[IPV6_ADDR_LEN])
{
    struct in6_addr sin6_addr;
    FILE*           stream;
    char            suffix[32];
    char            addr[IPV6_ADDR_LEN];
    char            line[128];
    int32_t         num = 0;
    int32_t         ret;

    RETURN_VAL_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN, -1);

    snprintf(suffix, sizeof(suffix), "# %s IPv6", IFACE_NAME(ifid));

    stream = pi_fopen(RESOLV_CONF_FILE, "r");
    if ( stream != NULL )
    {
        while ( num < 2 && fgets(line, sizeof(line), stream) )
        {
            if ( strstr(line, suffix) != NULL )
            {
                memset(addr, 0, sizeof(addr));
                ret = sscanf(line, "nameserver %39[0-9a-fA-f:]", addr);
                if ( ret == 1 )
                {
                    inet_pton(AF_INET6, addr, &sin6_addr);
                    inet_ntop(AF_INET6, &sin6_addr, dns[num], IPV6_ADDR_LEN);
                    NET_DEBUG("DNSv6[%d]: %s", num, dns[num]);
                    num++;
                }
            }
        }
        pi_fclose(stream);
    }

    return 0;
}

int32_t net_ipv6_clear_dns(IFACE_ID_E ifid)
{
    char    suffix[32];
    char    line[128];
    char    buf[1024];
    char*   ptr = buf;
    FILE*   stream;

    RETURN_VAL_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN, -1);

    snprintf(suffix, sizeof(suffix), "# %s IPv6", IFACE_NAME(ifid));
    memset(buf, 0, sizeof(buf));

    stream = pi_fopen(RESOLV_CONF_FILE, "r");
    if ( stream != NULL )
    {
        while ( fgets(line, sizeof(line), stream) )
        {
            if ( line[0] != '\n' && strstr(line, suffix) == NULL ) /* 按后缀"# ifname IPv6"过滤，剩余部分存入buf */
            {
                ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "%s", line);
            }
        }
        pi_fclose(stream);
    }
    NET_DEBUG("dns buf:\n%s", buf);

    /* 清空当前文件内容，写入过滤后的DNS buf */
    stream = pi_fopen(RESOLV_CONF_FILE, "w+");
    if ( stream != NULL )
    {
        fwrite(buf, 1, strlen(buf), stream);
        pi_fclose(stream);
    }

    return 0;
}
/**
 *@}
 */
