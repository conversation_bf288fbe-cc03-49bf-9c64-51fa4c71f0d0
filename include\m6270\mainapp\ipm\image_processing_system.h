/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  image_prossing_system.h
* @addtogroup ipm
*
* @{
* @addtogroup  image_prossing_system
* <AUTHOR> (<EMAIL>)
* @date  2021-12-25
* @version  v1.0
* @brief  image processing system API
**/

#ifndef IMAGE_PROCESSING_SYSTEM_H
#define IMAGE_PROCESSING_SYSTEM_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "pol/pol_types.h"

#ifdef CONFIG_IPM

/**
 * @brief init pipe core module
 * @return  int32_t
 * @retval 0:success -1:faild
 * <AUTHOR> (<EMAIL>)
 * @date 2021-12-25
 */
int32_t pi_image_processing_system_prolog();

/**
 * @brief destroy pipe core module
 * @return  int32_t
 * @retval 0:success -1:faild
 * <AUTHOR> (<EMAIL>)
 * @date 2021-12-25
 */
int32_t pi_image_processing_system_epilog();

/**
 * @brief set log level
 * @param[in] level  log level
 * @return  int32_t
 * @retval 0:success -1:faild
 * <AUTHOR> (<EMAIL>)
 * @date 2021-12-25
 */
int32_t pi_image_processing_system_set_log_level(int32_t level);

#else
/*int32_t pi_image_processing_system_prolog(){};
int32_t pi_image_processing_system_epilog(){};
int32_t pi_image_processing_system_set_log_level(int32_t level){level=level};
*/
#endif

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* IMAGE_PROCESSING_SYSTEM_H */

/**
* @}
**/

