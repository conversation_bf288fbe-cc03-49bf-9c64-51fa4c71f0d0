/**
 * @copyright 2023 Shenzhen Pantum Technology Co.Ltd all rights reserved
 * @file print_ctl_pedk.h
 * @addtogroup print_ctl_module
 * @{
 * @addtogroup print_ctl_pedk_module
 * <AUTHOR>
 * @date 2024-05-28
 * @version v0.1
 * @brief print ctl pedk module
 */

#ifndef PRINT_CTL_PEDK_H
#define PRINT_CTL_PEDK_H

#if CONFIG_SDK_PEDK

/*** Head file area ***************************************************/
#include "public_data_proc.h"
#include "pedk_mgr.h"

/**********************************************************************/



/*** Macro description area *******************************************/
// ...
/**********************************************************************/



/*** Enumerate description area ***************************************/

/**
 * @brief media size
 *
 */
typedef enum
{
    ePEDK_MEDIA_SIZE_A3 = 0,                                                            ///< A3
    ePEDK_MEDIA_SIZE_A4,                                                                ///< A4
    ePEDK_MEDIA_SIZE_A4L,                                                               ///< A4_L
    ePEDK_MEDIA_SIZE_A5,                                                                ///< A5
    ePEDK_MEDIA_SIZE_A5L,                                                               ///< A5_L
    ePEDK_MEDIA_SIZE_JIS_B4,                                                            ///< JIS_B4
    ePEDK_MEDIA_SIZE_JIS_B5,                                                            ///< JIS_B5
    ePEDK_MEDIA_SIZE_JIS_B5L,                                                           ///< JIS_B5_L
    ePEDK_MEDIA_SIZE_JIS_B6,                                                            ///< JIS_B6
    ePEDK_MEDIA_SIZE_JIS_B6L,                                                           ///< JIS_B6_L
    ePEDK_MEDIA_SIZE_IOS_B4,                                                            ///< IOS_B4
    ePEDK_MEDIA_SIZE_IOS_B4L,                                                           ///< IOS_B4_L
    ePEDK_MEDIA_SIZE_IOS_B5,                                                            ///< IOS_B5
    ePEDK_MEDIA_SIZE_IOS_B5L,                                                           ///< IOS_B5_L
    ePEDK_MEDIA_SIZE_IOS_B6,                                                            ///< IOS_B6
    ePEDK_MEDIA_SIZE_IOS_B6L,                                                           ///< IOS_B6_L
    ePEDK_MEDIA_SIZE_8K,                                                                ///< 8K
    ePEDK_MEDIA_SIZE_BIG_16K,                                                           ///< BIG_16K
    ePEDK_MEDIA_SIZE_BIG_16KL,                                                          ///< BIG_16K_L
    ePEDK_MEDIA_SIZE_16K,                                                               ///< 16K
    ePEDK_MEDIA_SIZE_16KL,                                                              ///< 16K_L
    ePEDK_MEDIA_SIZE_BIG_32K,                                                           ///< BIG_32K
    ePEDK_MEDIA_SIZE_BIG_32KL,                                                          ///< BIG_32K_L
    ePEDK_MEDIA_SIZE_32K,                                                               ///< 32K
    ePEDK_MEDIA_SIZE_32KL,                                                              ///< 32K_L
    ePEDK_MEDIA_SIZE_11x17,                                                             ///< 11"×17"
    ePEDK_MEDIA_SIZE_LETTER,                                                            ///< LETTER
    ePEDK_MEDIA_SIZE_LETTER_L,                                                          ///< LETTER_L
    ePEDK_MEDIA_SIZE_LEGAL,                                                             ///< LEGAL
    ePEDK_MEDIA_SIZE_FOLIO,                                                             ///< FOLIO
    ePEDK_MEDIA_SIZE_OFICIO,                                                            ///< OFICIO
    ePEDK_MEDIA_SIZE_EXECUTIVE,                                                         ///< EXECUTIVE
    ePEDK_MEDIA_SIZE_EXECUTIVE_L,                                                       ///< EXECUTIVE_L
    ePEDK_MEDIA_SIZE_STATEMENT,                                                         ///< STATEMENT
    ePEDK_MEDIA_SIZE_STATEMENT_L,                                                       ///< STATEMENT_L
    ePEDK_MEDIA_SIZE_A6,                                                                ///< A6
    ePEDK_MEDIA_SIZE_A6L,                                                               ///< A6_L
    ePEDK_MEDIA_SIZE_NO_10_ENV,                                                         ///< NO_10_ENV
    ePEDK_MEDIA_SIZE_MONARCH_ENV,                                                       ///< MONARCH_ENV
    ePEDK_MEDIA_SIZE_MONARCH_ENV_L,                                                     ///< MONARCH_ENV_L
    ePEDK_MEDIA_SIZE_C6_ENV,                                                            ///< C6_ENV
    ePEDK_MEDIA_SIZE_C6_ENV_L,                                                          ///< C6_ENV_L
    ePEDK_MEDIA_SIZE_C5_ENV,                                                            ///< C5_ENV
    ePEDK_MEDIA_SIZE_C5_ENV_L,                                                          ///< C5_ENV_L
    ePEDK_MEDIA_SIZE_C4_ENV,                                                            ///< C4_ENV
    ePEDK_MEDIA_SIZE_DL_ENV,                                                            ///< DL_ENV
    ePEDK_MEDIA_SIZE_B6,                                                                ///< B6
    ePEDK_MEDIA_SIZE_B6_L,                                                              ///< B6_L
    ePEDK_MEDIA_SIZE_ZL,                                                                ///< ZL
    ePEDK_MEDIA_SIZE_ZL_L,                                                              ///< ZL_L
    ePEDK_MEDIA_SIZE_YOUGATE4,                                                          ///< YOUGATE4
    ePEDK_MEDIA_SIZE_YOUGATE3,                                                          ///< YOUGATE3
    ePEDK_MEDIA_SIZE_NAGAGATE3,                                                         ///< NAGAGATE3
    ePEDK_MEDIA_SIZE_YOUGATE2,                                                          ///< YOUGATE2
    ePEDK_MEDIA_SIZE_YOUGATE2_L,                                                        ///< YOUGATE2_L
    ePEDK_MEDIA_SIZE_POSTCARD,                                                          ///< POSTCARD
    ePEDK_MEDIA_SIZE_POSTCARD_L,                                                        ///< POSTCARD_L
    ePEDK_MEDIA_SIZE_J_POSTCARD,                                                        ///< Japanese Postcard
    ePEDK_MEDIA_SIZE_J_POSTCARD_L,                                                      ///< Japanese Postcard LEF
    ePEDK_MEDIA_SIZE_USER_DEFINE,                                                       ///< USER_DEFINE
    ePEDK_MEDIA_SIZE_LEDGER,                                                            ///< LEDGER
    ePEDK_MEDIA_SIZE_BIG_8K,                                                            ///< BIG_8K
    ePEDK_MEDIA_SIZE_ENV_B6,                                                            ///< ENV_B6
    ePEDK_MEDIA_SIZE_FOOLSCAPS,                                                         ///< FOOLSCAPS
    ePEDK_MEDIA_SIZE_INVOICE,                                                           ///< INVOICE
    ePEDK_MEDIA_SIZE_INVOICE_L,                                                         ///< INVOICE_L
    ePEDK_MEDIA_SIZE_A3_WIDE,                                                           ///< A3_WIDE
    ePEDK_MEDIA_SIZE_LEGAL13,                                                           ///< LEGAL13
    ePEDK_MEDIA_SIZE_LEGAL14,                                                           ///< LEGAL14
    ePEDK_MEDIA_SIZE_YOUKEI_SIZE4,                                                      ///< YOUKEI_SIZE4
    ePEDK_MEDIA_SIZE_CHOUKEI_SIZE3,                                                     ///< CHOUKEI_SIZE3
    ePEDK_MEDIA_SIZE_SRA3,                                                              ///< SRA3
    ePEDK_MEDIA_SIZE_CARD,                                                              ///< CARD
    ePEDK_MEDIA_SIZE_INVALID = 0xFF,                                                    ///< invalid
}EPEDK_MEDIA_SIZE;

/**
 * @brief media type
 *
 */
typedef enum
{
    ePEDK_MEDIA_TYPE_PLAIN = 0,                                                         ///< PLAIN
    ePEDK_MEDIA_TYPE_PLAIN_PLUS,                                                        ///< PLAIN+
    ePEDK_MEDIA_TYPE_THICK1,                                                            ///< THICK1
    ePEDK_MEDIA_TYPE_THICK1_PLUS,                                                       ///< THICK1+
    ePEDK_MEDIA_TYPE_THICK2,                                                            ///< THICK2
    ePEDK_MEDIA_TYPE_THICK3,                                                            ///< THICK3
    ePEDK_MEDIA_TYPE_TRANSPARENCY,                                                      ///< TRANSPARENCY
    ePEDK_MEDIA_TYPE_POST_CARD,                                                         ///< POST CARD
    ePEDK_MEDIA_TYPE_ENVELOPE,                                                          ///< ENVELOPE
    ePEDK_MEDIA_TYPE_LABEL,                                                             ///< LABEL
    ePEDK_MEDIA_TYPE_THIN,                                                              ///< THIN
    ePEDK_MEDIA_TYPE_THICK,                                                             ///< THICK
    ePEDK_MEDIA_TYPE_CARD_STORK,                                                        ///< CARD STORK
    ePEDK_MEDIA_TYPE_THICKER,                                                           ///< THICKER
    ePEDK_MEDIA_TYPE_RECYCLED,                                                          ///< RECYCLED
    ePEDK_MEDIA_TYPE_INVALID = 0xFF,                                                    ///< invalid
}EPEDK_MEDIA_TYPE;


/**
 * @brief total page of media size
 *
 */
typedef enum
{
    ePEDK_SETTING_PAPERSIZE_A4_LETTER,                                                  /// < 0 A4 and letter
    ePEDK_SETTING_PAPERSIZE_A5,                                                         /// < 1
    ePEDK_SETTING_PAPERSIZE_LEGAL_FOLIO,                                                /// < 2 legal and folio
    ePEDK_SETTING_PAPERSIZE_B5_EXECUTIVE,                                               /// < 3 jis_b5 and executive
    ePEDK_SETTING_PAPERSIZE_B6_B6ENV,                                                   /// < 4 jis_b6 and b6_env
    ePEDK_SETTING_PAPERSIZE_OTHERS,                                                     /// < 5
    ePEDK_SETTING_PAPERSIZE_INVALID = 0xFF,                                             /// < invalid
}EPEDK_TOTAL_PAGES_MEDIA_SIZE;

/**
 * @brief internal page
 *
 */
typedef enum
{
    ePRINT_PESF_INTERNAL_PAGE_DEMOE = 1,                                                ///< 1：Demo页
    ePRINT_PESF_INTERNAL_PAGE_MENU_STRUCT = 2,                                          ///< 2：菜单结构
    ePRINT_PESF_INTERNAL_PAGE_PRINTER_INFO = 3,                                         ///< 3：打印机信息页
    ePRINT_PESF_INTERNAL_PAGE_NET_CONFIG = 4,                                           ///< 4：网络配置页
    ePRINT_PESF_INTERNAL_PAGE_WIFI_SSID_LIST = 5,                                       ///< 5：WIFI热点页
    ePRINT_PESF_INTERNAL_PAGE_WIFI_GUIDE = 6,                                           ///< 6：WIFI向导页
    ePRINT_PESF_INTERNAL_PAGE_EMAIL_ADDRESS_LIST = 7,                                   ///< 7：邮件地址列表
    ePRINT_PESF_INTERNAL_PAGE_EMAIL_GROUP_LIST = 8,                                     ///< 8：邮件群组列表
    ePRINT_PESF_INTERNAL_PAGE_FTP_LIST = 9,                                             ///< 9：FTP列表
    ePRINT_PESF_INTERNAL_PAGE_MAX = 10,                                                 ///< 10：MAX
}EPRTNT_PESF_INTERNAL_PAGE_TYPE;

/**
 * @brief toner status
 *
 */
typedef enum
{
    ePRINT_PEDK_TONER_STATUS_NORMAL = 0,                                                ///< 0：Normal
    ePRINT_PEDK_TONER_STATUS_UNINSTALL = 1,                                             ///< 1：Toner cartridge not installed
    ePRINT_PEDK_TONER_STATUS_DISMATCH = 2,                                              ///< 2：Toner cartridge mismatch
    ePRINT_PEDK_TONER_STATUS_EMPTY = 3,                                                 ///< 3：Toner cartridge exhausted
    ePRINT_PEDK_TONER_STATUS_LOW = 4,                                                   ///< 4：Low toner level
    ePRINT_PEDK_TONER_STATUS_INVALID = 0xFF,                                            ///< OxFF：invalid
}EPRINT_PEDK_TONER_STATUS;

/**
 * @brief drum status
 *
 */
typedef enum
{
    ePRINT_PEDK_DRUM_STATUS_NORMAL = 0,                                                ///< 0：Normal
    ePRINT_PEDK_DRUM_STATUS_UNINSTALL = 1,                                             ///< 1：not installed
    ePRINT_PEDK_DRUM_STATUS_DISMATCH = 2,                                              ///< 2：mismatch
    ePRINT_PEDK_DRUM_STATUS_EMPTY = 3,                                                 ///< 3：exhausted
    ePRINT_PEDK_DRUM_STATUS_EMPTY_SOON = 4,                                            ///< 4：exhausted soon
    ePRINT_PEDK_DRUM_STATUS_INVALID = 0xFF,                                            ///< OxFF：invalid
}EPRINT_PEDK_DRUM_STATUS;


/**
 * @brief collate mode
 *
 */
typedef struct
{
    int mode;
}JSCollateMode;

/**
 * @brief copies
 *
 */
typedef struct
{
    int num;
}JSCopies;

/**
 * @brief color
 *
 */
typedef struct
{
    int mode;
}JSColor;


/**
 * @brief Duplex Print Mode
 *
 */
typedef struct
{
    int mode;
}JSDuplexPrintMode;

/**
 * @brief Image Orientation Mode
 *
 */
typedef struct
{
    int mode;
}JSImageOrientationMode;

/**
 * @brief OutPut Tray
 *
 */
typedef struct
{
    char out_tray[32];
    char paper_size[32];
}JSOutPutTray;

/**
 * @brief Paper Save Mode
 *
 */
typedef struct
{
    int mode;
}JSPaperSaveMode;

/**
 * @brief Paper Type
 *
 */
typedef struct
{
    char type[32];
}JSPaperType;

/**
 * @brief staple mode
 *
 */
typedef struct
{
    char staple[32];
    char punch[32];
    char fold[32];
    char shift[32];
}JSStapleConfigMode;


/**
 * @brief Print Parameter
 *
 */
typedef struct
{
    JSCopies                copies;
    JSPaperType             paper_type;
    JSOutPutTray            tray;
    JSCollateMode           collate_mode;
    JSDuplexPrintMode       duplex_mode;
    JSPaperSaveMode         paper_save_mode;
    JSImageOrientationMode  image_orientation_mode;
    JSStapleConfigMode      staple_config_mode;
    JSColor                 color;
}JSPrintParameter;

/**
 * @brief Print Job
 *
 */
typedef struct
{
    int                 job_id;
    int                 job_woNum;
    char                job_type[32];
    JSPrintParameter    job_param;
    union
    {
        int  page_type;             //< For internal page printing
        char file_path[512];        //< For usb memory printing
        char url[512];              //< For url printing
    };
}JSPrintJob;

/**
 * @brief paper size with tray struct, sopport array
 */
typedef struct
{
    char                        paper_size[32];                                     ///< paper size
    uint32_t                    tray_support[7];                                    ///< real tray array
}JSPaperSizeWithTray;


/**
 * @brief paper type with tray struct, sopport array
 */
typedef struct
{
    char                        paper_type[32];                                     ///< paper type
    uint32_t                    tray_support[7];                                    ///< real tray array
}JSPaperTypeWithTray;

typedef struct
{
    char                        type[8];                                            ///< job type:copy/print/scan
    uint32_t                    job_id;                                             ///< job id
    JOB_CLASS_E                 job_class;                                            ///< job_class
    char                        user_name[8];                                       ///< user name:admin
    char                        file_name[128];                                     ///< file_name
    char                        status[15];                                         ///< job status
    char                        start_time[25];                                     ///< job start time
    char                        OutputTray[30];                                     ///< output tray
    char                        PaperSize[30];                                      ///< paper size
    uint32_t                    Copies;                                             ///< copies
    char                        ColorMode[25];                                      ///< color mode  COLOR_MODE_BLACK_WHITE/COLOR_MODE_COLOR
    uint8_t                     DuplexPrintMode;                                    ///< print mode  0:singel, 1 auto duplex
}JSPedkJobInfo;
/**********************************************************************/



/*** Variable description area ****************************************/

/**********************************************************************/



/*** Function description area ****************************************/

/**
* @brief   paper size
* @param[in]   media_size
* @param[out]   paper size
* @return   paper size
* @retval   paper size
* <AUTHOR>
* @date   2024-05-29
* @note   N/A
*/
PAPER_SIZE_E print_ctl_pedk_paper_size(char *media_size);

/**
* @brief   media_size
* @param[in]   paper_size
* @param[out]   media_size
* @return   media_size
* @retval   paper size
* <AUTHOR>
* @date   2024-05-29
* @note   N/A
*/
char *print_ctl_pedk_media_size(PAPER_SIZE_E paper_size);

/**
* @brief   get total print color page
* @param[in]   void
* @param[out]   total print color page
* @return   total print color page
* @retval   total print color page
* <AUTHOR>
* @date   2024-05-29
* @note   N/A
*/
uint32_t print_ctl_pedk_total_color_page( void );

/**
* @brief   get total print mono page
* @param[in]   void
* @param[out]   total print mono page
* @return   total print mono page
* @retval   total print mono page
* <AUTHOR>
* @date   2024-05-29
* @note   N/A
*/
uint32_t print_ctl_pedk_total_mono_page( void );

/**
* @brief   get total print sheet
* @param[in]   void
* @param[out]   total print sheet
* @return   total print sheet
* @retval   total print sheet
* <AUTHOR>
* @date   2025-05-29
* @note   N/A
*/
uint32_t print_ctl_pedk_total_sheet( void );

/**
* @brief   get page by paper size
* @param[in]   media_size
* @param[out]   page by media_size
* @return   page by media_size
* @retval   page by media_size
* <AUTHOR>
* @date   2024-05-29
* @note   N/A
*/
uint32_t print_ctl_pedk_media_size_total_page( char *media_size );

/**
* @brief   get pincode info
* @param[in]   user_name
* @param[out]   void
* @return   void
* @retval   N/A
* <AUTHOR>
* @date   2024-05-31
* @note   N/A
*/
void print_ctl_pedk_get_pincode_info( char* user_name );

/**
* @brief   print pedk capabilities handler
* @param[in]   sub
* @param[in]   respond
* @param[in]   buf_size
* @param[in]   buf
* @param[out]   void
* @return   N/A
* @retval   N/A
* <AUTHOR>
* @date   2024-08-05
* @note   N/A
*/
void print_ctl_pedk_capabilities_handler( SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t* buf, void* ctx );

/**
* @brief   print pedk setting handler
* @param[in]   sub
* @param[in]   respond
* @param[in]   buf_size
* @param[in]   buf
* @param[out]   void
* @return   N/A
* @retval   N/A
* <AUTHOR>
* @date   2024-08-05
* @note   N/A
*/
void print_ctl_pedk_setting_handler( SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t* buf, void* ctx );


void print_ctl_pedk_get_job_attr( ATTRIBUTE_INFO_S* job_attr,JOB_CLASS_E job_class);
void print_ctl_pedk_get_page_attr( void* sheet_data );
void print_ctl_pedk_job_done_process( void );

/**
* @brief   pedk register
* @param[in]   void
* @param[out]   ret
* @return   ret
* @retval   ret
* <AUTHOR>
* @date   2024-06-05
* @note   N/A
*/
int32_t print_ctl_pedk_init(void);

/**
* @brief   pedk unregister
* @param[in]   void
* @param[out]   void
* @return   void
* @retval   N/A
* <AUTHOR>
* @date   2024-06-05
* @note   N/A
*/
void print_ctl_pedk_deinit(void);

/**********************************************************************/

#endif

#endif
