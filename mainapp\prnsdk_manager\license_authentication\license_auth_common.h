/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file license_auth_common.h
 * @brief Common utilities and macros for license authentication.
 * @details This header file defines macros and utility functions used across the license authentication module.
 * <AUTHOR>
 * @date 2024-12-11
 */

#ifndef _LICENSE_AUTH_COMMON_H_
#define _LICENSE_AUTH_COMMON_H_

#include <stdio.h>
#include <string.h>
#include <time.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>
#include <signal.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdarg.h>
#include <sys/mman.h>
#include <sys/wait.h>
#include <sys/resource.h>
#include <ctype.h>
#include <dirent.h>
#include <math.h>
#include <time.h>
#include <pthread.h>
#include <dirent.h>

#include "cmd.h"
#include "nvram.h"
#include "moduleid.h"
#include "acl/acl.h"
#include "acl_attribute.h"
#include "platform_api.h"

#include "license_authentication/license_auth_api.h"
#include "license_auth_file_utils.h"
#include "license_auth_data_processing.h"
#include "license_auth_access.h"
#include "license_auth_file_utils.h"
#include "license_auth_encryption.h"
#include "license_auth_timer.h"

#include "pol/pol_log.h"
#include "pol/pol_threads.h"
#include "pol/pol_define.h"
#include "pol/pol_string.h"

#include "utilities/msgrouter.h"
#include "utilities/parsercommon.h"

#ifdef __cplusplus
extern "C" {
#endif


// 错误处理宏
#define LICENSE_AUTH_SAFE_FREE(ptr)         do { if(ptr)  { free(ptr); ptr = NULL;} } while (0)
#define LICENSE_AUTH_SAFE_CLOSE(fd)         do { if((fd) >= 0) { close(fd); fd = -1; } } while (0)
#define LICENSE_AUTH_SAFE_DELETE_JSON(json) do { if(json) { cJSON_Delete(json); json = NULL; } } while (0)

#define LOG_ERROR_NULL(fmt, ...)            do { printf(fmt "\n", ##__VA_ARGS__); return NULL; } while (0)
#define LICENSE_AUTH_ERROR_CLOSE(fd)        do { printf("[%s][%d]: %s\n", __func__, __LINE__, strerror(errno)); LICENSE_AUTH_SAFE_CLOSE(fd); } while (0)
#define LICENSE_AUTH_ERROR_C_NULL           do { printf("[%s][%d]: cJSON is error!\n", __func__, __LINE__); return "ERROR"; } while (0)
#define LICENSE_AUTH_ERROR_NULL             do { printf("[%s][%d]: %s\n", __func__, __LINE__, strerror(errno)); return NULL; } while (0)
#define LICENSE_AUTH_ERROR_INT              do { printf("[%s][%d]: cJSON is error!\n", __func__, __LINE__); return -1; } while (0)


// 数学辅助宏
#define LICENSE_AUTH_MAX(a, b) ((a) > (b) ? (a) : (b)) // 获取最大值
#define LICENSE_AUTH_MIN(a, b) ((a) < (b) ? (a) : (b)) // 获取最小值


#define LICENSE_AUTH_DEFINE(a,b)              a/b
#define LICENSE_AUTH_DEFINE_T(a, b)           LICENSE_AUTH_DEFINE(a,b)
#define LICENSE_AUTH_STR(a)                   #a
#define LICENSE_AUTH_STR_EXPAND(a)            LICENSE_AUTH_STR(a)
#define LICENSE_AUTH_BASE64_SIZE(size)        (((size * 4) / 3) + 10)

#define LICENSE_AUTH_PATH_ACCESS              LICENSE_AUTH_PATH_LICENSE
#define LICENSE_AUTH_PATH_LICENSE             /tmp

// 临时文件路径宏定义
#define LICENSE_AUTH_TEMP_ACCESS(b)           LICENSE_AUTH_STR_EXPAND(LICENSE_AUTH_DEFINE_T(LICENSE_AUTH_PATH_ACCESS, b))
#define LICENSE_AUTH_TEMP_LICENSE(b)          LICENSE_AUTH_STR_EXPAND(LICENSE_AUTH_DEFINE_T(LICENSE_AUTH_PATH_LICENSE, b))

#define LICENSE_AUTH_CLEANUP_CMD              rm LICENSE_AUTH_DEFINE_T(LICENSE_AUTH_PATH_ACCESS,ztmp*)
#define LICENSE_AUTH_RM_PATH                  LICENSE_AUTH_STR_EXPAND(LICENSE_AUTH_CLEANUP_CMD)
#define LICENSE_AUTH_TMP_SDK_LICENSE          LICENSE_AUTH_TEMP_LICENSE(tmp_sdk_license)
#define LICENSE_AUTH_SDK_LICENSE              LICENSE_AUTH_STR_EXPAND(LICENSE_AUTH_DEFINE_T(/settings, LICENSE_AUTH_SDK_LICENSE))
#define LICENSE_AUTH_LIMITATION_FILE          LICENSE_AUTH_STR_EXPAND(LICENSE_AUTH_DEFINE_T(/event, sec_time))
#define LICENSE_AUTH_TIME_LIMITATION_FILE     LICENSE_AUTH_STR_EXPAND(LICENSE_AUTH_DEFINE_T(/event, time_limite))




/**
 * @brief Calculates the number of occurrences of a specific character in a string.
 * @param[in] buffer The input string.
 * @return The number of occurrences of the character. Returns 0 if input is invalid.
 */
int32_t license_auth_get_line_count(const char *buffer);

/**
 * @brief Gets the number of leading spaces in a string.
 * @param[in] input The input string.
 * @return The number of leading spaces. Returns -1 if input is invalid.
 */
int32_t license_auth_trim_leading_spaces(const char *input);

/**
 * @brief Computes the length of non-empty characters in a string.
 * @param[in] input_string The input string.
 * @param[in] buffer_length The total buffer length.
 * @return The length of non-empty characters. Returns 0 if input is invalid.
 */
size_t license_auth_get_complex_length(const char *input_string, size_t buffer_length);

#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_COMMON_H_ */
/**
 * @}
 */
