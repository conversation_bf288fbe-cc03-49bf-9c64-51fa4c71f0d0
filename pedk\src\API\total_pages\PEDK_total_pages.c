
#include "PEDK_event.h"

#include <quickjs.h>


JSValue js_getTotalColorPrintPage(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
    int data = 0;
    int data_size = sizeof( data );

    printf("[line:%d] %s\n",__LINE__,__func__);
    ret = SendMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_COLOR, value,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);
       // RecvMsgToMfp(MSG_MODULE_JOBCTL, sub, &ret, recvBuf, recvBufSize, MSG_RECV_TIMEOUT);

    ret = RecvMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_COLOR, &value,&data,&data_size,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,data = %d \n",__LINE__,__func__,data);
    return JS_NewInt32(ctx, data);

}

JSValue js_getTotalMonoPrintPage(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;    
    int data = 0;
    int data_size = sizeof( data );


    printf("[line:%d] %s\n",__LINE__,__func__);
    ret = SendMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MONO, value,0,NULL);
    if(ret != 0)
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MONO, &value,&data,&data_size,3);
    if(ret != 0)
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);
    return JS_NewInt32(ctx, data);

}

JSValue js_getTotalPagesByMediaSize(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
        int data = 0;
    int data_size = sizeof( data );

    JS_ToInt32(ctx, &value, argv[0]);
    ret = SendMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MEDIA_SIZE, value,0,NULL);
    if(ret != 0)
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }

    ret = RecvMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MEDIA_SIZE, &value,&data,&data_size,3);
    if(ret != 0)
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,data = %d\n",__LINE__,__func__,data);

    return JS_NewInt32(ctx, data);

}

JSValue js_getTotalPrintedSheets(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
        int data = 0;
    int data_size = sizeof( data );

    ret = SendMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_SHEETS, value,0,NULL);
    if(ret != 0)
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
         return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);
    ret = RecvMsgToMfp(MSG_MODULE_TOTAL_PAGES, MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_SHEETS, &value,&data,&data_size,11);
    if(ret != 0)
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,data = %d\n",__LINE__,__func__,data);

    return JS_NewInt32(ctx, data);

}



