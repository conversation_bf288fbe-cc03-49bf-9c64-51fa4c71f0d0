#include "runtime/utils/whitelist.h"
#include "basic/hash/uthash.h"

int32_t add_whitelist(WhileList** wl, char *name, int32_t value)
{
    WhileList* wlh = (WhileList*)malloc(sizeof(WhileList));

    strncpy(wlh->name,name,64);
    wlh->value = value;
    HASH_ADD_STR(*wl, name, wlh);

    return 0;
}

/**
 * @brief 获取白名单权限，找到了，则将权限的存储值返回，没找到则返回0
 * 
 * @param wl 
 * @param name 
 * @return int32_t 1：有权限
 *                 0：没权限
 */
int32_t get_whitelist_authority(PeSFRunTime* prt, char *name)
{
    LOG_D("whitelist","get_whitelist_authority");
    WhileList* value = NULL;
    WhileList* wl=prt->proj_config.whitelist;

    HASH_FIND_STR(wl,name,value);
    if(NULL != value){
        return value->value;
    }else{
        return 0;
    }
}

/*int32_t show_whitelist_authority(WhileList* wl)
{
    WhileList* iter_wl;
    for (iter_wl = wl; iter_wl; iter_wl = iter_wl->hh.next){
       printf("name : %s,var = %d\n" ,iter_wl->name,iter_wl->value);
    }
}*/