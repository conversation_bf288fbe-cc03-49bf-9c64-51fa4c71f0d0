//*****************************************************************************
//  Copyright (C) 2014 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/ipp/ippx.h#26 $
//  $Change: 244499 $ $Date: 2014/11/25 $
//
/// @file
/// IPP (Internet Printing Protocol) server private include
///
/// @ingroup Network
//
//*****************************************************************************
#ifndef IPPX_H
#define IPPX_H 1

#include "nettypes.h"
#include "netjob.h"
#include "ringbuf.h"
#include "ippprot.h"
#include "ipp.h"
#include "http_task.h"

// Define this to 1 to check that attributes are set to the exact
// proper type, and are presented in the proper group, etc.  this
// can be important for passing certification tests.  In a real
// product, it makes more sense to let poorly implemented drivers
// set enums from ints, etc. and not take the time to check things
//
#define IPP_PEDANTIC 1
#define IPP_SUPPORTS_OVERRIDES 1

// overrides (PWG 5100.6) are complext and take a lot of space and are only needed for
// some configs, and if in, add a lot to varblock for operation attributes
//
#ifndef IPP_SUPPORTS_OVERRIDES
    #define IPP_SUPPORTS_OVERRIDES IN_IPS
#endif

#if IPP_SUPPORTS_OVERRIDES
    #define IPP_COLLECTION_EXTRA	40000 //16384
#else
    #define IPP_COLLECTION_EXTRA    0
#endif

typedef struct ipp_header
{
    uint8_t   major_version;
    uint8_t   minor_version;
    uint16_t  opcode;
    uint32_t  id;
}
IPP_HEADER_S;

#define PDL_JOB_START_TIMEOUT       (10*60) /* 10 minutes */
#define PDL_PORT_FIRSTBYTE_TIMEOUT  60

// how many attributes to accomodate in a request.  this is
// positional, not sequential, so these need to be configured
// at least as large as the tables they work on
//
#define IPP_MAX_PRINTER_ATTRIBUTES      148
#define IPP_MAX_OPERATION_ATTRIBUTES    80 //wangzhigao 2016.9.8 mopria1.3
#define IPP_MAX_UNSUPPORTED_ATTRIBUTES  48

/// how large an array of collections we allow, in general
///
#define IPP_MAX_COLLECTION_SET          32

/// how many member elements max in a single collection
///
#define IPP_MAX_COLLECTION_SIZE         16

/// how large a single element can be, if guessing size
///
#define IPP_MAX_COLLECTION_ELEMENT      IPP_MAX_URI

/// how deeply nested collections can be for incoming attributes
///
#define IPP_MAX_COLLECTION_DEPTH        3

#define IPP_MAX_URI     256
#define IPP_MAX_TEXT    128

#define IPP_MAX_JOBIDS  245
#define IPP_IDENTIFY_ACTIONS_COUNT 4

// seconds we wait for job to finish before new one fails
//
#define IPP_MULTIOP_TIMEOUT PDL_JOB_START_TIMEOUT

// flags bits
#define ATTR_OPTIONAL    0x00       ///< option component of reply/request
#define ATTR_REQUIRED    0x01       ///< required attribute
#define ATTR_JOBTEMPLATE 0x02       ///< this is a job-template attribute
#define ATTR_EXPLICIT    0x04       ///< set request only if asked for explicitly (i.e. not "all")
#define ATTR_ARRAY       0x08       ///< the item's value is an array type

#define ATTR_COMBO_0011     (0          | 0             | ATTR_JOBTEMPLATE | ATTR_REQUIRED)
#define ATTR_COMBO_0101     (0          | ATTR_EXPLICIT | 0                | ATTR_REQUIRED)
#define ATTR_COMBO_0110     (0          | ATTR_EXPLICIT | ATTR_JOBTEMPLATE | 0)
#define ATTR_COMBO_0111     (0          | ATTR_EXPLICIT | ATTR_JOBTEMPLATE | ATTR_REQUIRED)
#define ATTR_COMBO_1001     (ATTR_ARRAY | 0             | 0                | ATTR_REQUIRED)
#define ATTR_COMBO_1010     (ATTR_ARRAY | 0             | ATTR_JOBTEMPLATE | 0)
#define ATTR_COMBO_1011     (ATTR_ARRAY | 0             | ATTR_JOBTEMPLATE | ATTR_REQUIRED)
#define ATTR_COMBO_1100     (ATTR_ARRAY | ATTR_EXPLICIT | 0                | 0)
#define ATTR_COMBO_1101     (ATTR_ARRAY | ATTR_EXPLICIT | 0                | ATTR_REQUIRED)
#define ATTR_COMBO_1110     (ATTR_ARRAY | ATTR_EXPLICIT | ATTR_JOBTEMPLATE | 0)
#define ATTR_COMBO_1111     (ATTR_ARRAY | ATTR_EXPLICIT | ATTR_JOBTEMPLATE | ATTR_REQUIRED)

#if (IPP_MAX_MAJOR > 1)
#define ATTR_REQUIRED_20    ATTR_REQUIRED
#else
#define ATTR_REQUIRED_20    ATTR_OPTIONAL
#endif

#define ATTR_COMBO_1001_20  (ATTR_ARRAY | 0             | 0                | ATTR_REQUIRED_20)

#define ATTR_REQUESTED   0x10       ///< this attribute was requested to be part of a response
#define ATTR_SET         0x20       ///< this attribute was set by an operation

#define REQUIRE_ATTR(pa)        (pa->flags |= ATTR_REQUIRED)
#define UNREQUIRE_ATTR(pa)      (pa->flags &= ~ATTR_REQUIRED)
#define IS_ATTR_REQUIRED(pa)    ((pa->flags & ATTR_REQUIRED) != 0)

#define REQUEST_ATTR(pa)        (pa->flags |= ATTR_REQUESTED)
#define UNREQUEST_ATTR(pa)      (pa->flags &= ~ATTR_REQUESTED)
#define IS_ATTR_REQUESTED(pa)   ((pa->flags & ATTR_REQUESTED) != 0)

#define SET_ATTR(pa)            (pa->flags |= ATTR_SET)
#define UNSET_ATTR(pa)          (pa->flags &= ~ATTR_SET)
#define IS_ATTR_SET(pa)         ((pa->flags & ATTR_SET) != 0)

#define IS_ATTR_EXPLICIT(pa)    ((pa->flags & ATTR_EXPLICIT) != 0)
#define IS_ATTR_ARRAY(pa)       ((pa->flags & ATTR_ARRAY) != 0)
#define IS_ATTR_JOBTEMPLATE(pa) ((pa->flags & ATTR_JOBTEMPLATE) != 0)

#define ERR_NONE           	        0x00000000
#define ERR_TONER_LOW               0x00000001
#define ERR_MEDIA_JAM               0x00000002
#define ERR_TONER_EMPTY             0x00000004
#define ERR_DOOR_OPEN               0x00000008
#define ERR_NOTONER                 0x00000010
#define ERR_PAPEROUT                0x00000020
#define ERR_OTHER                   0x00000040

#define IS_ERR_NONE(err)       		((err | ERR_NONE) == 0)
#define IS_ERR_TONER_LOW(err)       ((err & ERR_TONER_LOW) != 0)
#define IS_ERR_MEDIA_JAM(err)       ((err & ERR_MEDIA_JAM) != 0)
#define IS_ERR_TONER_EMPTY(err)     ((err & ERR_TONER_EMPTY) != 0)
#define IS_ERR_DOOR_OPEN(err)       ((err & ERR_DOOR_OPEN) != 0)
#define IS_ERR_NOTONER(err)       	((err & ERR_NOTONER) != 0)
#define IS_ERR_PAPEROUT(err)       	((err & ERR_PAPEROUT) != 0)
#define IS_ERR_OTHER(err)           ((err & ERR_OTHER) != 0)


/// Bit-mask of IPP Service type, used to select which service
/// to get or set an attribute from
///
/* -- in ipp.h as part of public api
#define IPPSERVICE_NONE     0x00    ///< default
#define IPPSERVICE_DEFAULTS 0x01    ///< compiled in default attribute set
#define IPPSERVICE_PRINT    0x02    ///< printer service attribute set
#define IPPSERVICE_FAX      0x04    ///< fax service set
#define IPPSERVICE_ALL      0xFE    ///< all services except the default set
*/

/* one IPP attribute */
typedef struct ipp_attributes
{
    char*       name;           ///< name of the attribute
    uint8_t     type;           ///< data type of it (IPP semantics)
    uint8_t     flags;          ///< array, required, requested, etc.
    void*       var;            ///< ptr to storage for this attribute
    int32_t     varsize;        ///< length of storage area in bytes (for arrays, this is total size of all elements)
    uint16_t    maxlen;         ///< max size, in bytes, of one element (for array types)
    uint16_t    curdim;         ///< current top dimension of set values (for array types)
}
IPP_ATTR_S, *PIPP_ATTR;

/// An IPP context, used to handle a single IPP request/response
typedef struct ipp_context
{
    IPP_HEADER_S    req;                 ///< request header
    //int32_t         reqid;                  ///< incrementing request id number for debug
    int32_t         jobid;                  ///< job ID of job inside the request
    int32_t         handlingData;           ///< in between operation and responseComplete
    int32_t         responseCode;           ///< what the response code was
    int32_t         lastDocument;           ///< set true for printjob, or last-document set in send-document

    int32_t         parse_completed;

    HTTP_TASK_S*    httptask;          ///< back ptr to owning HTTP server context

    uint32_t        serviceType;         ///< IPP service type request is for (i.e. print or faxout) (e.g. IPPSERVICE_PRINT)

    /// attributes callbacks pass NULL for the name of second array value on, so we need
    /// to remember the "current" attribute name in a sequence in order to hack the values
    char            attrName[IPP_MAX_NAME];

    /// collection tree parsing state, for each level a pointer to
    /// the first attribute in the collection
    IPP_ATTR_S      colltree[IPP_MAX_COLLECTION_DEPTH];

    /// buffer to place collections into to set operation attributes from
    uint8_t         collbuff[(IPP_MAX_COLLECTION_SIZE * (sizeof(IPP_ATTR_S) + IPP_MAX_COLLECTION_ELEMENT)) * IPP_MAX_COLLECTION_DEPTH + IPP_COLLECTION_EXTRA];

    /// collection parsing level specific attribute parsing state, so support
    /// array indexing at all collection levels, etc.
    int             colldex[IPP_MAX_COLLECTION_DEPTH];  ///< saves array index
    int             collpmb[IPP_MAX_COLLECTION_DEPTH];  ///< saves in-member state

    /// block of bytes to allocate vars from in operation attributes
    /// to avoid many small alloc/frees and thus fragmentation
    /// most attrs are strings up to 128, some are 256, assume big
    /// and some strings like operation-requested-attributes can be as
    /// big as all the name strings together
    ///
    uint8_t         varblock[IPP_MAX_OPERATION_ATTRIBUTES * 6 * IPP_MAX_TEXT + 4 * IPP_MAX_URI + IPP_COLLECTION_EXTRA];

    /// printer attributes which are requested for this operation
    ///
    uint8_t         printer_attributes[IPP_MAX_PRINTER_ATTRIBUTES];

    /// operation attribute set for this operation/request
    IPP_ATTR_S      operation_attributes[IPP_MAX_OPERATION_ATTRIBUTES];

    /// unsupported attribute set for this operation/request
    //
    IPP_ATTR_S      unsupported_attributes[IPP_MAX_UNSUPPORTED_ATTRIBUTES];
    int             num_unsupported;///< count of unsupported attributes added

    /// The Inferno job context the pdl stream is parsed in, used
    /// to get pages in the queue for the ipp job after job parses out
    /// among other things
    RING_BUF_S*     rbuf;

    /// response data
    char*           outbuf; // allocated to IPP_MAX_LENGTH, so can live past us
    int             outhead, outtail, outcnt;
}
IPP_CTX_S, *PIPPCTX;

int  ipp_get_request_user_name(char *pdata);

#include "ippattr.h"
#include "ippjob.h"

#endif

