#ifndef _SNMPIPC_H_
#define _SNMPIPC_H_

#include <sys/types.h>
#include <sys/file.h>
#include <sys/mman.h>
#include <sys/stat.h>        /* For mode constants */
#include <fcntl.h>           /* For O_* constants */
#include <errno.h>

#include "printer_param.h"

#ifdef __cplusplus
extern "C" {
#endif

#if __STDC_VERSION__ >= 199901L

#include <stdint.h>

#else

typedef signed char         int8_t;
typedef short int           int16_t;
typedef int                 int32_t;

typedef unsigned char       uint8_t;
typedef unsigned short int  uint16_t;
typedef unsigned int        uint32_t;

#endif /* __STDC_VERSION__ >= 199901L */

#define SNMP_MAP_NAME "snmp_map"

/* HOST-RESOURCES-MIB .*******.2.1.25 */
typedef struct host_resource_mib
{
    /* .1.6 */
    int32_t     _hrSystemProcesses;
    /* .1.7 */
    int32_t     _hrSystemMaxProcesses;

    /* .2.2 */
    int32_t     _hrMemorySize;
    /* .3.1.(1 ~ 7)*/
    int32_t     _hrStorageIndex;
    int32_t     _hrStorageType;
    char        _hrStorageDescr[256];
    int32_t     _hrStorageAllocationUnits;
    int32_t     _hrStorageSize;
    int32_t     _hrStorageUsed;
    int32_t     _hrStorageAllocationFailures;
    /* .3.2.1.(1 ~ 6) */
    int32_t     _hrDeviceIndex;
    char        _hrDeviceType[256];
    char        _hrDeviceDescr[64];
    char        _hrDeviceID[256];
    int32_t     _hrDeviceStatus;
    int32_t     _hrDeviceErrors;
    /* .3.3.(1 ~ 2) */
    int32_t     _hrProcessorFrwID;
    int32_t     _hrProcessorLoad;
    /* .4.1.1 */
    int32_t     _hrNetworkIfIndex;
    /* .3.5.1.(1 ~ 2) */
    int32_t     _hrPrinterStatus;
    char        _hrPrinterDetectedErrorState[2];
    /* .3.6.1.(1 ~ 4) */
    int32_t     _hrDiskStorageAccess;
    int32_t     _hrDiskStorageMedia;
    int32_t     _hrDiskStorageRemoveble;
    int32_t     _hrDiskStorageCapacity;
    /* .3.7.1.(1 ~ 6) */
    int32_t     _hrPartitionIndex;
    char        _hrPartitionLabel[128];
    char        _hrPartitionID[128];
    int32_t     _hrPartitionSize;
    int32_t     _hrPartitionFSIndex;
    /* .3.8.1.(1 ~ 9) */
    int32_t     _hrFSIndex;
    char        _hrFSMountPoint[128];
    char        _hrFSRemoteMountPoint[128];
    int32_t     _hrFSType;
    int32_t     _hrFSAccess;
    int32_t     _hrFSBootable;
    int32_t     _hrFSStorageIndex;
    char        _hrFSLastFullBackupDate[64];
    char        _hrFSLastPartialBackupDate[64];
    /* .4.1.1 */
    int32_t     _hrSWOSIndex;
    /* .4.2.1.(1 ~ 7) */
    int32_t     _hrSWRunIndex;
    char        _hrSWRunName[64];
    char        _hrSWRunID[64];
    char        _hrSWRunPath[128];
    char        _hrSWRunParameters[128];
    int32_t     _hrSWRunType;
    int32_t     _hrSWRunStatus;
    /* .5.1.1.(1 ~ 2) */
    int32_t     _hrSWRunPerfCPU;
    int32_t     _hrSWRunPerfMem;
    /* .6.3.1.(1 ~ 5) */
    int32_t     _hrSWInstalledIndex;
    char        _hrSWInstalledName[64];
    int32_t     _hrSWInstalledID;
    int32_t     _hrSWInstalledType;
    int32_t     _hrSWInstalledDate;
}
HRES_MIB_S;

/* Printer-MIB .*******.2.1.43 */
typedef struct printer_mib
{
    /* .5.1.1.(1 ~ 13,16 ~ 17) */
    int32_t     _prtGeneralConfigChanges;
    int32_t     _prtGeneralCurrentLocalization;
    int32_t     _prtGeneralReset;
    char        _prtGeneralCurrentOperator[128];
    char        _prtGeneralServicePerson[128];
    int32_t     _prtInputDefaultIndex;
    int32_t     _prtOutputDefaultIndex;
    int32_t     _prtMarkerDefaultIndex;
    int32_t     _prtMediaPathDefaultIndex;
    int32_t     _prtConsoleLocalization;
    int32_t     _prtConsoleNumberOfDisplayLines;
    int32_t     _prtConsoleNumberOfDisplayChars;
    int32_t     _prtConsoleDisable;
    int32_t     _prtAuxiliarySheetStartupPage;
    int32_t     _prtAuxiliarySheetBannerPage;
    char        _prtGeneralPrinterName[32];
    char        _prtGeneralSerialNumber[32];

    /* .5.2.1.(1 ~ 2) */
    int32_t     _prtStorageRefSeqNumber;    ///< unused
    int32_t     _prtStorageRefIndex;        ///< unused

    /* .5.3.1.(1 ~ 2) */
    int32_t     _prtDeviceRefSeqNumber;     ///< unused
    int32_t     _prtDeviceRefIndex;         ///< unused

    /* .6.1.1.(1 ~ 3) */
    int32_t     _prtCoverIndex;
    char        _prtCoverDescription1[256];
    char        _prtCoverDescription2[256]; ///< unused
    int32_t     _prtCoverStatus1;
    int32_t     _prtCoverStatus2;           ///< unused

    /* .7.1.1.(1 ~ 4) */
    int32_t     _prtLocalizationIndex;
    char        _prtLocalizationLanguage[4];
    char        _prtLocalizationCountry[4];
    int32_t     _prtLocalizationCharacterSet;

    /* .8.2.1.(1 ~ 23) */
    int32_t     _prtInputIndex;
    int32_t     _prtInputType;
    int32_t     _prtInputDimUnit;
    int32_t     _prtInputMediaDimFeedDirDeclared;
    int32_t     _prtInputMediaDimXFeedDirDeclared;
    int32_t     _prtInputMediaDimFeedDirChosen;
    int32_t     _prtInputMediaDimXFeedDirChosen;
    int32_t     _prtInputCapacityUnit;
    int32_t     _prtInputMaxCapacity;
    int32_t     _prtInputCurrentLevel;
    int32_t     _prtInputStatus;
    char        _prtInputMediaName[64];
    char        _prtInputName[64];
    char        _prtInputVendorName[64];
    char        _prtInputModel[64];
    char        _prtInputVersion[64];
    char        _prtInputSerialNumber[32];
    char        _prtInputDescription[256];
    int32_t     _prtInputSecurity;
    int32_t     _prtInputMediaWeight;
    char        _prtInputMediaType[64];
    char        _prtInputMediaColor[64];
    int32_t     _prtInputMediaFormParts;

    /* .9.2.1.(1 ~ 24) */
    int32_t     _prtOutputIndex;
    int32_t     _prtOutputType;
    int32_t     _prtOutputCapacityUnit;
    int32_t     _prtOutputMaxCapacity;
    int32_t     _prtOutputRemainingCapacity;
    int32_t     _prtOutputStatus;
    char        _prtOutputName[64];
    char        _prtOutputVendorName[64];
    char        _prtOutputModel[64];
    char        _prtOutputVersion[64];
    char        _prtOutputSerialNumber[64];
    char        _prtOutputDescription[256];
    int32_t     _prtOutputSecurity;
    int32_t     _prtOutputDimUnit;
    int32_t     _prtOutputMaxDimFeedDir;
    int32_t     _prtOutputMaxDimXFeedDir;
    int32_t     _prtOutputMinDimFeedDir;
    int32_t     _prtOutputMinDimXFeedDir;
    int32_t     _prtOutputStackingOrder;
    int32_t     _prtOutputPageDeliveryOrientation;
    int32_t     _prtOutputBursting;
    int32_t     _prtOutputDecollating;
    int32_t     _prtOutputPageCollated;
    int32_t     _prtOutputOffsetStacking;

    /* .10.2.1.(1 ~ 15) */
    int32_t     _prtMarkerIndex;
    int32_t     _prtMarkerMarkTech;
    int32_t     _prtMarkerCounterUnit;
    int32_t     _prtMarkerLifeCount;
    int32_t     _prtMarkerPowerOnCount;
    int32_t     _prtMarkerProcessColorants;
    int32_t     _prtMarkerSpotColorants;
    int32_t     _prtMarkerAddressabilityUnit;
    int32_t     _prtMarkerAddressabilityFeedDir;
    int32_t     _prtMarkerAddressabilityXFeedDir;
    int32_t     _prtMarkerNorthMargin;
    int32_t     _prtMarkerSouthMargin;
    int32_t     _prtMarkerWestMargin;
    int32_t     _prtMarkerEastMargin;
    int32_t     _prtMarkerStatus;

    /* .11.1.1.(1 ~ 9) */
    int32_t     _prtMarkerSuppliesIndex;
    int32_t     _prtMarkerSuppliesMarkerIndex;

    /* .12.1.1.(1 ~ 5) */
    int32_t     _prtMarkerColorantIndex;
    int32_t     _prtMarkerColorantMarkerIndex;
    int32_t     _prtMarkerColorantRole;
    char        _prtMarkerColorantValue[64];
    int32_t     _prtMarkerColorantTonality;

    /* .13.4.1.(1 ~ 11) */
    int32_t     _prtMediaPathIndex;
    int32_t     _prtMediaPathMaxSpeedPrintUnit;
    int32_t     _prtMediaPathMediaSizeUnit;
    int32_t     _prtMediaPathMaxSpeed;
    int32_t     _prtMediaPathMaxMediaFeedDir;
    int32_t     _prtMediaPathMaxMediaXFeedDir;
    int32_t     _prtMediaPathMinMediaFeedDir;
    int32_t     _prtMediaPathMinMediaXFeedDir;
    int32_t     _prtMediaPathType;
    char        _prtMediaPathDescription[256];
    int32_t     _prtMediaPathStatus;

    /* .14.1.1.(1 ~ 8) */
    int32_t     _prtChannelIndex;
    int32_t     _prtChannelType;
    char        _prtChannelProtocolVersion[64];
    int32_t     _prtChannelCurrentJobCntlLangIndex;
    int32_t     _prtChannelDefaultPageDescLangIndex;
    int32_t     _prtChannelState;
    int32_t     _prtChannelIfIndex;
    int32_t     _prtChannelStatus;

    /* .15.1.1.(1 ~ 12) */
    int32_t     _prtInterpreterIndex;
    int32_t     _prtInterpreterLangFamily;
    char        _prtInterpreterLangLevel[32];
    char        _prtInterpreterLangVersion[32];
    char        _prtInterpreterDescription[32];
    char        _prtInterpreterVersion[32];
    int32_t     _prtInterpreterDefaultOrientation;
    int32_t     _prtInterpreterFeedAddressability;
    int32_t     _prtInterpreterXFeedAddressability;
    int32_t     _prtInterpreterDefaultCharSetIn;
    int32_t     _prtInterpreterDefaultCharSetOut;
    int32_t     _prtInterpreterTwoWay;

    /* .16.5.1.(1 ~ 2) */
    int32_t     _prtConsoleDisplayBufferIndex;
    char        _prtConsoleDisplayBufferText[256];

    /* .17.6.1.(1 ~ 5) */
    int32_t     _prtConsoleLightIndex;
    int32_t     _prtConsoleOnTime;
    int32_t     _prtConsoleOffTime;
    int32_t     _prtConsoleColor;
    char        _prtConsoleDescription[256];

    /* .18.1.1.(1 ~ 9) */
    int32_t     _prtAlertIndex;
    int32_t     _prtAlertSeverityLevel;
    int32_t     _prtAlertTrainingLevel;
    int32_t     _prtAlertGroup;
    int32_t     _prtAlertGroupIndex;
    int32_t     _prtAlertLocation;
    int32_t     _prtAlertCode;
    char        _prtAlertDescription[128];
    int32_t     _prtAlertTime;
}
PRT_MIB_S;

/* PRINTER-PORT-MONITOR-MIB .*******.4.1.2699.1.2.1 */
typedef struct printer_port_monitor_mib
{
    /* .1.(1 ~ 3) */
    char        _ppmGeneralNaturalLanguage[64];
    int32_t     _ppmGeneralNumberOfPrinters;
    int32_t     _ppmGeneralNumberOfPorts;

    /* .2.1.1.(1 ~ 8) */
    int32_t     _ppmPrinterIndex; /* unused */
    char        _ppmPrinterName[128];
    char        _ppmPrinterIEEE1284DeviceId[1024];
    int32_t     _ppmPrinterNumberOfPorts;
    int32_t     _ppmPrinterPreferredPortIndex;
    int32_t     _ppmPrinterHrDeviceIndex;
    char        _ppmPrinterSnmpCommunityName[256];
    int32_t     _ppmPrinterSnmpQueryEnabled;

    /* .3.1.1.(1 ~ 9) */
    int32_t     _ppmPortIndex; /* unused */
    int32_t     _ppmPortEnabled;
    char        _ppmPortName[128];
    char        _ppmPortServiceNameOrURI[256];
    int32_t     _ppmPortProtocolType;
    int32_t     _ppmPortProtocolTargetPort;
    int32_t     _ppmPortProtocolAltSourceEnabled;
    int32_t     _ppmPortPrtChannelIndex;
    int32_t     _ppmPortLprByteCountEnabled;
}
PPM_MIB_S;

typedef struct snmp_map
{
    /* Public MIB */
    HRES_MIB_S          hres;
    PRT_MIB_S           prt;
    PPM_MIB_S           ppm;

    /* Pantum-MPS-MIB .*******.4.1.40093.10 */
    /* .1.1.(1 ~ 16) */
    char                _ptFirmwareVersion[16];         ///< 固件版本
    int32_t             _ptCardMemory;                  ///< 内存大小
    char                _ptEngineVersion[32];           ///< 引擎版本
    char                _ptSerialNumber[64];            ///< 产品序列号
    char                _ptManufacturerName[32];        ///< 厂商名称
    char                _ptProductDate[16];             ///< 生产日期
    char                _ptProductName[32];             ///< 产品名称
    char                _ptBootVersion[16];             ///< Boot版本
    char                _ptKernelVersion[16];           ///< 内核版本
    char                _ptMACAddress[24];              ///< MAC地址
    int32_t             _ptPrintColor;                  ///< 彩色模式
    int32_t             _ptPrintSpeed;                  ///< 打印速度
    char                _ptGeneralProduceConfig[152];   ///< 产品配置信息
    char                _ptDeviceMibMpsVer[16];         ///< 支持mps的MIB标准的版本号
    int32_t             _ptDeviceMibPrintControlEnable; ///< 是否支持文印管控的MIB节点
    char                _ptDeviceMibPrintControlVer[16];///< 文印管控支持的版本号
    /* .1.2.1 */
    int32_t             _ptDeviceStatus;                ///< 设备状态
    /* .1.3.(1 ~ 3) */
    int32_t             _ptSleepTime;                   ///< 休眠时间
    int32_t             _ptPrintSpeedMode;              ///< 速度模式
    int32_t             _ptJobTimeOut;                  ///< 超时时间
    /* .1.3.4.(1 ~ 9)*/
    int32_t             _ptInkSavingSetting;            ///< 省墨设置
    int32_t             _ptLanguageSetting;             ///< 语言设置
    int32_t             _ptRestoreFactorySetting;       ///< 恢复出厂设置
    int32_t             _ptMutePrintSetting;            ///< 静音打印设置
    int32_t             _ptScreenBrightnessSetting;     ///< 屏幕亮度设置
    int32_t             _ptShutdownConditonSetting;     ///< 关机条件设置
    int32_t             _ptShutdownDelaySetting;        ///< 关机延时设置
    int32_t             _ptClesnFixingPageSetting;      ///< 打印清洁定影页设置
    int32_t             _ptImageCheapSetting;           ///< 图像偏移设置
    /* .1.3.4.10.1.(1 ~ 2)*/
    int32_t             _ptVolumeIndex;                 ///< 声音索引
    int32_t             _ptVolumeSetting;               ///< 声音/音量设置
    /* .1.3.4.(11 ~ 15)*/
    int32_t             _ptDataFormat;                  ///< 日期格式
    int32_t             _ptTimeFormat;                  ///< 时间格式
    char                _ptDateTimeSetting[24];         ///< 时间/日期
    int32_t             _ptTimezoneSetting;             ///< 时区
    int32_t             _ptOnlineUpdateSetting;         ///< 在线升级设置
    /* .********.1.(1 ~ 3)*/
    int32_t             _ptWindowsLogInEnable;          ///< 是否启用windows登录
    int32_t             _ptServerAuthentication;        ///< 服务器身份认证
    int32_t             _ptWindowsLogIn;                ///< windows登录设置
    /* .1.3.5.(1 ~ 2)*/
    int32_t             _ptTrayIndex;                   ///< 纸盒索引
    int32_t             _ptTrayPlacingPaperTips;        ///< 纸盒放纸提示设置
    int32_t             _ptPaperSizeSetting;            ///< 纸张尺寸设置
    int32_t             _ptPaperTypeSetting;            ///< 纸张类型设置
    int32_t             _ptPaperSourceSetting;          ///< 纸张来源设置
    int32_t             _ptPrintingQuantitySetting;     ///< 打印份数设置
    int32_t             _ptPrintDuplexSetting;          ///< 双面打印设置
    int32_t             _ptImageOrientationSetting;     ///< 图像方向设置
    int32_t             _ptPrintConcentrationSetting;   ///< 打印浓度设置
    int32_t             _ptResolutionSetting;           ///< 分辨率设置
    /* .1.3.6.(1 ~ 5)*/
    int32_t             _ptWPSModeEnable;               ///< WPS启动方式设置
    int32_t             _ptWirelessFrequencySetting;    ///< 无线频路设置
    int32_t             _ptWirelessDirectConnectSetting;///< WIFI直连设置
    int32_t             _ptEmailEncryptionSetting;      ///< SMTP参数邮箱加密方式设置
    int32_t             _ptNetworkContactsEnable;       ///< 是否启用网络联系人
    /* .1.3.7.(1 ~ 6)*/
    int32_t             _ptUserLoginEnable;             ///< 是否启用用户登录
    int32_t             _ptPanelSessionTimeoutTimeSetting;  ///< 面板会话超时时间
    int32_t             _ptHardwarePortsEnable;         ///< 是否启用硬件端口
    int32_t             _ptForbidScanTOEmailSmbFtpEnable;   ///< 是否禁止在打印机显示屏幕中新增扫描到Email/SMB/FTP的地址
    int32_t             _ptWebEncryptionEnable;         ///< 是否启用启用Web加密
    int32_t             _ptMemoryResetSetting;          ///< 是否启用内存清零
    /* .1.4.1.1 */
    int32_t             _ptUsbConnectedFlag;            ///< USB连接状态
    /* .1.4.2.1 */
    int32_t             _ptNWConnectedFlag;             ///< 网络连接状态
    /* .1.5.1 */
    int32_t             _ptPrintGeneralStatus;          ///< Powder cartridge status
    /* .*******.1.(1 ~ 4) */
    char                _ptToneindexK[4];               ///< 粉盒标识K C M Y
    char                _ptToneindexC[4];
    char                _ptToneindexM[4];
    char                _ptToneindexY[4];
    /* .*******.2.(1 ~ 4) */
    char                _ptTonerUnitK[4];               ///< 粉盒余量单位
    char                _ptTonerUnitC[4];
    char                _ptTonerUnitM[4];
    char                _ptTonerUnitY[4];
    /* .*******.3.(1 ~ 4) */
    int32_t             _ptTonerRemainK;                ///< 粉盒余量
    int32_t             _ptTonerRemainC;
    int32_t             _ptTonerRemainM;
    int32_t             _ptTonerRemainY;
    /* .*******.4.(1 ~ 4) */
    int32_t             _ptTonerMaximumK;               ///< 粉盒最大打印页数
    int32_t             _ptTonerMaximumC;
    int32_t             _ptTonerMaximumM;
    int32_t             _ptTonerMaximumY;
    /* .*******.5.(1 ~ 4) */
    char                _ptTonerModelK[16];             ///< 粉盒型号
    char                _ptTonerModelC[16];
    char                _ptTonerModelM[16];
    char                _ptTonerModelY[16];
    /* .*******.6.(1 ~ 4) */
    char                _ptTonerSerialK[32];            ///< 粉盒序列号
    char                _ptTonerSerialC[32];
    char                _ptTonerSerialM[32];
    char                _ptTonerSerialY[32];
    /* .*******.7.(1 ~ 4) */
    int32_t             _ptTonerStateK;                 ///< 粉盒状态
    int32_t             _ptTonerStateC;
    int32_t             _ptTonerStateM;
    int32_t             _ptTonerStateY;
    /* .*******.8.(1 ~ 4) */
    char                _ptTonerDescriptionK[10];       ///< 粉盒描述
    char                _ptTonerDescriptionC[10];
    char                _ptTonerDescriptionM[10];
    char                _ptTonerDescriptionY[10];
    /* .*******.1.(1 ~ 4) */
    char                _ptCartridgeUnitK[4];           ///< 硒鼓余量单位
    char                _ptCartridgeUnitC[4];
    char                _ptCartridgeUnitM[4];
    char                _ptCartridgeUnitY[4];
    /* .*******.2.(1 ~ 4) */
    int32_t             _ptCartridgeRemainK;            ///< 硒鼓余量
    int32_t             _ptCartridgeRemainC;
    int32_t             _ptCartridgeRemainM;
    int32_t             _ptCartridgeRemainY;
    /* .*******.3.(1 ~ 4) */
    int32_t             _ptCartridgeMaximumK;           ///< 硒鼓最大打印页数
    int32_t             _ptCartridgeMaximumC;
    int32_t             _ptCartridgeMaximumM;
    int32_t             _ptCartridgeMaximumY;
    /* .*******.4.(1 ~ 4) */
    char                _ptCartridgeModelK[16];         ///< 硒鼓型号
    char                _ptCartridgeModelC[16];
    char                _ptCartridgeModelM[16];
    char                _ptCartridgeModelY[16];
    /* .*******.5.(1 ~ 4) */
    char                _ptCartridgeSerialK[32];        ///< 硒鼓序列号
    char                _ptCartridgeSerialC[32];
    char                _ptCartridgeSerialM[32];
    char                _ptCartridgeSerialY[32];
    /* .*******.6.(1 ~ 4) */
    int32_t             _ptCartridgeStateK;             ///< 硒鼓状态
    int32_t             _ptCartridgeStateC;
    int32_t             _ptCartridgeStateM;
    int32_t             _ptCartridgeStateY;
    /* .1.5.4.1.(1 ~ 5) */
    int32_t             _ptWasterTonerState;            ///< 废粉瓶状态
    int32_t             _ptWasterTonerRemain;           ///< 废粉瓶余量
    int32_t             _ptWasterTonerMaximum;          ///< 废粉瓶最大容量         unused
    char                _ptWasterTonerModel[16];        ///< 废粉瓶型号             unused
    char                _ptWasterTonerSerial[32];       ///< 废粉瓶序列号           unused
    /* .1.6.1 */
    int32_t             _ptCopyScanMode;                ///< 扫描模式
    /* .1.7.2 */
    int32_t             _ptCopyJobType;                 ///< 复印作业类型
    /* .1.8.1.(1 ~ 5) */
    int32_t             _ptAlertSeverityLevel;          ///< 故障等级 1-致命 2-错误 3-警告
    int32_t             _ptAlertGroup;                  ///< 故障发生模块 1-print 2-scan 3-fix
    int32_t             _ptAlertGroupIndex;             ///< 故障所属组 1-engine 2-toner 3-cartridge 4-cover
    int32_t             _ptAlertLocation;               ///< 故障发生位置 0-无 1-前 2-中 3-后
    int32_t             _ptAlertCode;                   ///< 故障代码
    char                _ptAlertdescription[32];        ///< 故障描述
    /* .1.9.1.(1 ~ 6) */
    int32_t             _ptAlertNewSeverityLevel;       ///< 故障发生严重程度
    int32_t             _ptAlertNewGroup;               ///< 故障所在模块
    int32_t             _ptAlertNewGroupIndex;          ///< 故障所在模块的组
    int32_t             _ptAlertNewLocation;            ///< 故障发生位置
    int32_t             _ptAlertNewCode;                ///< 故障代码
    char                _ptAlerNewdescription[32];      ///< 故障描述
    /* .2.1.1.(1 ~ 11) */
    int32_t             _ptJobIndex;                    ///< 作业索引               unused
    int32_t             _ptJobID;                       ///< 当前作业ID             unused
    int32_t             _ptJobCurPrintptgeNumber;       ///< 当前作业正在打印的页码 unused
    int32_t             _ptJobTray;                     ///< 当前作业的纸张来源     unused
    int32_t             _ptJobptperSize;                ///< 当前作业的介质类型     unused
    int32_t             _ptJobptperMedia;               ///< 当前作业的介质来源     unused
    int32_t             _ptJobMemory;                   ///< 当前作业的内存状态     unused
    int32_t             _ptJobVia;                      ///< 当前作业的来源         unused
    char                _ptJobOwner[8];                 ///< 当前作业PJL头中 @PJL SET JOBATTR=HOST:VALUE中VALUE的值 unused
    int32_t             _ptPrintJobID;                  ///< 存储当前打印中的JobID  unused
    int32_t             _ptJobAliveFlag;                ///< 网络打印中防止打印超时的标识 unused
    /* .2.1.2.(1 ~  11) */
    int32_t             _ptScanJobIndex;                ///< 扫描作业索引
    int32_t             _ptScanJobID;                   ///< 当前扫描作业的ID
    int32_t             _ptJobCurScanpageNumber;        ///< 当前扫描作业,正在扫描的页码
    int32_t             _ptScanJobTray;                 ///< 当前扫描作业的纸张来源
    int32_t             _ptScanJobpaperSize;            ///< 当前扫描作业的介质类型
    int32_t             _ptScanJobpaperMedia;           ///< 当前扫描作业的来源
    int32_t             _ptScanJobMemory;               ///< 当前扫描作业时固件的内存状态
    int32_t             _ptScanJobVia;                  ///< 当前扫描作业的来源
    char                _ptScanJobOwner[8];             ///< 扫描作业的PJL头中 @PJL SET JOBATTR=HOST:VALUE中VALUE 的值
    //int32_t             _ptScanJobID;                   ///< 存储当前扫描中的JobID
    int32_t             _ptScanJobAliveFlag;            ///< 网络扫描中防止扫描超时的标识
    /* .2.1.3.(1 ~  11) */
    int32_t             _ptCopyJobIndex;                ///< 复印作业索引
    int32_t             _ptCopyJobID;                   ///< 当前复印作业的ID
    int32_t             _ptJobCurCopypageNumber;        ///< 当前复印作业,正在复印的页码
    int32_t             _ptCopyJobTray;                 ///< 当前复印作业的纸张来源
    int32_t             _ptCopyJobpaperSize;            ///< 当前复印作业的介质类型
    int32_t             _ptCopyJobpaperMedia;           ///< 当前复印作业的来源
    int32_t             _ptCopyJobMemory;               ///< 当前复印作业时固件的内存状态
    int32_t             _ptCopyJobVia;                  ///< 当前复印作业的来源
    char                _ptCopyJobOwner[8];             ///< 复印作业的PJL头中 @PJL SET JOBATTR=HOST:VALUE中VALUE 的值
    //int32_t             _ptCopyJobID;                   ///< 存储当前复印中的JobID
    int32_t             _ptCopyJobAliveFlag;            ///< 网络复印中防止复印超时的标识
    /* .2.1.4.(1 ~  11) */
    int32_t             _ptFaxJobIndex;                 ///< 传真作业索引
    int32_t             _ptFaxJobID;                    ///< 当前传真作业的ID
    int32_t             _ptJobCurFaxpageNumber;         ///< 当前传真作业,正在传真的页码
    int32_t             _ptFaxJobTray;                  ///< 当前传真作业的纸张来源
    int32_t             _ptFaxJobpaperSize;             ///< 当前传真作业的介质类型
    int32_t             _ptFaxJobpaperMedia;            ///< 当前传真作业的来源
    int32_t             _ptFaxJobMemory;                ///< 当前传真作业时固件的内存状态
    int32_t             _ptFaxJobVia;                   ///< 当前传真作业的来源
    char                _ptFaxJobOwner[8];              ///< 传真作业的PJL头中 @PJL SET JOBATTR=HOST:VALUE中VALUE 的值
    //int32_t             _ptFaxJobID;                   ///< 存储当前传真中的JobID
    int32_t             _ptFaxJobAliveFlag;             ///< 网络传真中防止传真超时的标识
    /* .3.1.(1 ~ 35) */
    int32_t             _ptEngineCounter;               ///< 引擎已打印页数
    int32_t             _ptPrintCounterTotal;           ///< 总体打印计数
    int32_t             _ptPrintCounterColor;           ///< 执行打印功能，彩色打印总页数
    int32_t             _ptPrintCounterMono;            ///< 执行打印功能，黑白打印总页数
    int32_t             _ptPrintCounterDuplex;          ///< 执行打印功能，双面打印纸张的总页数
    int32_t             _ptPrintCounterA3;              ///< 执行打印功能，打印A3纸张的总面数
    int32_t             _ptPrintCounterA4;              ///< 执行打印功能，打印A4纸张的总面数
    int32_t             _ptPrintCounterA5;              ///< 执行打印功能，打印A5纸张的总面数
    int32_t             _ptPrintCounterB4;              ///< 执行打印功能，打印B4纸张的总面数
    int32_t             _ptPrintCounterJISB5;           ///< 执行打印功能，打印JIS B5纸张的总面数
    int32_t             _ptPrintCounterIOSB5;           ///< 执行打印功能，打印IOS B5纸张的总面数
    int32_t             _ptPrintCounter11x17;           ///< 执行打印功能，打印11"X17"纸张的总面数
    int32_t             _ptPrintCounterLetter;          ///< 执行打印功能，打印Letter纸张的总面数
    int32_t             _ptPrintCounterLegal;           ///< 执行打印功能，打印Legal纸张的总面数
    int32_t             _ptPrintCounterFolio;           ///< 执行打印功能，打印Folio纸张的总面数
    int32_t             _ptPrintCounterOficio;          ///< 执行打印功能，打印Oficio纸张的总面数
    int32_t             _ptPrintCounterExec;            ///< 执行打印功能，打印Executive纸张的总面数
    int32_t             _ptPrintCounterStatement;       ///< 执行打印功能，打印Statement纸张的总面数
    int32_t             _ptPrintCounter8k;              ///< 执行打印功能，打印8K纸张的总面数
    int32_t             _ptPrintCounter16k;             ///< 执行打印功能，打印16K纸张的总面数
    int32_t             _ptPrintCounterUserdef;         ///< 执行打印功能，打印自定义纸张的总面数
    int32_t             _ptPrintCounterA6;              ///< A6打印面数统计
    int32_t             _ptPrintCounterEnv10;           ///< ENV 10打印面数统计
    int32_t             _ptPrintCounterEnvMon;          ///< ENV Monarch打印面数统计
    int32_t             _ptPrintCounterEnvC6;           ///< ENV C6打印面数数统计
    int32_t             _ptPrintCounterEnvC5;           ///< ENV C5打印面数统计
    int32_t             _ptPrintCounterEnvDL;           ///< ENV DL打印面数统计
    int32_t             _ptPrintCounterPosterCard;      ///< POSTERCARD打印面数统计
    int32_t             _ptPrintCounterEquPages;        ///< 等效打印页数（按page计算）
    int32_t             _ptPrintCounterColor_A3;        ///< 执行打印功能，打印彩色A3纸张的总面数
    int32_t             _ptPrintCounterMono_A3;         ///< 执行打印功能，打印黑白A3纸张的总面数
    /* .3.2.(1 ~ 30) */
    int32_t             _ptScanCounterTotal;            ///< 总体扫描计数
    int32_t             _ptScanCounterColor;            ///< 执行扫描功能，彩色扫描总页数
    int32_t             _ptScanCounterMono;             ///< 执行扫描功能，黑白扫描总页数
    int32_t             _ptScanCounterDuplex;           ///< 执行扫描功能，双面扫描纸张的总页数
    int32_t             _ptScanCounterA3;               ///< 执行扫描功能，扫描A3纸张的总面数
    int32_t             _ptScanCounterA4;               ///< 执行扫描功能，扫描A4纸张的总面数
    int32_t             _ptScanCounterA5;               ///< 执行扫描功能，扫描A5纸张的总面数
    int32_t             _ptScanCounterB4;               ///< 执行扫描功能，扫描B4纸张的总面数
    int32_t             _ptScanCounterJISB5;            ///< 执行扫描功能，扫描JIS B5纸张的总面数
    int32_t             _ptScanCounterIOSB5;            ///< 执行扫描功能，扫描IOS B5纸张的总面数
    int32_t             _ptScanCounter11x17;            ///< 执行扫描功能，扫描11"X17"纸张的总面数
    int32_t             _ptScanCounterLetter;           ///< 执行扫描功能，扫描Letter纸张的总面数
    int32_t             _ptScanCounterLegal;            ///< 执行扫描功能，扫描Legal纸张的总面数
    int32_t             _ptScanCounterFolio;            ///< 执行扫描功能，扫描Folio纸张的总面数
    int32_t             _ptScanCounterOficio;           ///< 执行扫描功能，扫描Oficio纸张的总面数
    int32_t             _ptScanCounterExec;             ///< 执行扫描功能，扫描Executive纸张的总面数
    int32_t             _ptScanCounterStatement;        ///< 执行扫描功能，扫描Statement纸张的总面数
    int32_t             _ptScanCounter8k;               ///< 执行扫描功能，扫描8K纸张的总面数
    int32_t             _ptScanCounter16k;              ///< 执行扫描功能，扫描16K纸张的总面数
    int32_t             _ptScanCounterUserdef;          ///< 执行扫描功能，扫描自定义纸张的总面数
    int32_t             _ptScanCounterA6;               ///< A6扫描面数统计
    int32_t             _ptScanCounterEnv10;            ///< ENV 10扫描面数统计
    int32_t             _ptScanCounterEnvMon;           ///< ENV Monarch扫描面数统计
    int32_t             _ptScanCounterEnvC6;            ///< ENV C6扫描面数统计
    int32_t             _ptScanCounterEnvC5;            ///< ENV C5扫描面数统计
    int32_t             _ptScanCounterEnvDL;            ///< ENV DL扫描面数统计
    int32_t             _ptScanCounterPosterCard;       ///< POSTERCARD扫描面数统计
    int32_t             _ptScanCounterEquPages;         ///< 等效扫描页数（按page计算）
    int32_t             _ptScanCounterColor_A3;         ///< 执行扫描功能，扫描彩色A3纸张的总面数
    int32_t             _ptScanCounterMono_A3;          ///< 执行扫描功能，扫描黑白A3纸张的总面数
    /* .3.3.(1 ~ 30) */
    int32_t             _ptCopyCounterTotal;            ///< 总复印计数
    int32_t             _ptCopyCounterColor;            ///< 执行复印功能，彩色复印总页数
    int32_t             _ptCopyCounterMono;             ///< 执行复印功能，黑白复印总页数
    int32_t             _ptCopyCounterDuplex;           ///< 执行复印功能，双面复印纸张的总页数
    int32_t             _ptCopyCounterA3;               ///< 执行复印功能，复印A3纸张的总面数
    int32_t             _ptCopyCounterA4;               ///< 执行复印功能，复印A4纸张的总面数
    int32_t             _ptCopyCounterA5;               ///< 执行复印功能，复印A5纸张的总面数
    int32_t             _ptCopyCounterB4;               ///< 执行复印功能，复印B4纸张的总面数
    int32_t             _ptCopyCounterJISB5;            ///< 执行复印功能，复印JIS B5纸张的总面数
    int32_t             _ptCopyCounterIOSB5;            ///< 执行复印功能，复印IOS B5纸张的总面数
    int32_t             _ptCopyCounter11x17;            ///< 执行复印功能，复印11"X17"纸张的总面数
    int32_t             _ptCopyCounterLetter;           ///< 执行复印功能，复印Letter纸张的总面数
    int32_t             _ptCopyCounterLegal;            ///< 执行复印功能，复印Legal纸张的总面数
    int32_t             _ptCopyCounterFolio;            ///< 执行复印功能，复印Folio纸张的总面数
    int32_t             _ptCopyCounterOficio;           ///< 执行复印功能，复印Oficio纸张的总面数
    int32_t             _ptCopyCounterExec;             ///< 执行复印功能，复印Executive纸张的总面数
    int32_t             _ptCopyCounterStatement;        ///< 执行复印功能，复印Statement纸张的总面数
    int32_t             _ptCopyCounter8k;               ///< 执行复印功能，复印8K纸张的总面数
    int32_t             _ptCopyCounter16k;              ///< 执行复印功能，复印16K纸张的总面数
    int32_t             _ptCopyCounterUserdef;          ///< 执行复印功能，复印自定义纸张的总面数
    int32_t             _ptCopyCounterA6;               ///< A6复印面数统计
    int32_t             _ptCopyCounterEnv10;            ///< ENV 10复印面数统计
    int32_t             _ptCopyCounterEnvMon;           ///< ENV Monarch复印面数统计
    int32_t             _ptCopyCounterEnvC6;            ///< ENV C6复印面数统计
    int32_t             _ptCopyCounterEnvC5;            ///< ENV C5复印面数统计
    int32_t             _ptCopyCounterEnvDL;            ///< ENV DL复印面数统计
    int32_t             _ptCopyCounterPosterCard;       ///< POSTERCARD复印面数统计
    int32_t             _ptCopyCounterEquPages;         ///< 等效复印页数（按page计算）
    int32_t             _ptCopyCounterColor_A3;         ///< 执行复印功能，复印彩色A3纸张的总面数
    int32_t             _ptCopyCounterMono_A3;          ///< 执行复印功能，复印黑白A3纸张的总面数
    /* .3.4.(1 ~ 30) */
    int32_t             _ptFaxCounterTotal;             ///< 总传真计数
    int32_t             _ptFaxCounterColor;             ///< 执行传真功能，彩色传真总页数
    int32_t             _ptFaxCounterMono;              ///< 执行传真功能，黑白传真总页数
    int32_t             _ptFaxCounterDuplex;            ///< 执行传真功能，双面传真纸张的总页数
    int32_t             _ptFaxCounterA3;                ///< 执行传真功能，传真A3纸张的总面数
    int32_t             _ptFaxCounterA4;                ///< 执行传真功能，传真A4纸张的总面数
    int32_t             _ptFaxCounterA5;                ///< 执行传真功能，传真A5纸张的总面数
    int32_t             _ptFaxCounterB4;                ///< 执行传真功能，传真B4纸张的总面数
    int32_t             _ptFaxCounterJISB5;             ///< 执行传真功能，传真JIS B5纸张的总面数
    int32_t             _ptFaxCounterIOSB5;             ///< 执行传真功能，传真IOS B5纸张的总面数
    int32_t             _ptFaxCounter11x17;             ///< 执行传真功能，传真11"X17"纸张的总面数
    int32_t             _ptFaxCounterLetter;            ///< 执行传真功能，传真Letter纸张的总面数
    int32_t             _ptFaxCounterLegal;             ///< 执行传真功能，传真Legal纸张的总面数
    int32_t             _ptFaxCounterFolio;             ///< 执行传真功能，传真Folio纸张的总面数
    int32_t             _ptFaxCounterOficio;            ///< 执行传真功能，传真Oficio纸张的总面数
    int32_t             _ptFaxCounterExec;              ///< 执行传真功能，传真Executive纸张的总面数
    int32_t             _ptFaxCounterStatement;         ///< 执行传真功能，传真Statement纸张的总面数
    int32_t             _ptFaxCounter8k;                ///< 执行传真功能，传真8K纸张的总面数
    int32_t             _ptFaxCounter16k;               ///< 执行传真功能，传真16K纸张的总面数
    int32_t             _ptFaxCounterUserdef;           ///< 执行传真功能，传真自定义纸张的总面数
    int32_t             _ptFaxCounterA6;                ///< A6传真面数统计
    int32_t             _ptFaxCounterEnv10;             ///< ENV 10传真面数统计
    int32_t             _ptFaxCounterEnvMon;            ///< ENV Monarch传真面数统计
    int32_t             _ptFaxCounterEnvC6;             ///< ENV C6传真面数统计
    int32_t             _ptFaxCounterEnvC5;             ///< ENV C5传真面数统计
    int32_t             _ptFaxCounterEnvDL;             ///< ENV DL传真面数统计
    int32_t             _ptFaxCounterPosterCard;        ///< POSTERCARD传真面数统计
    int32_t             _ptFaxCounterEquPages;          ///< 等效传真页数（按page计算）
    int32_t             _ptFaxCounterColor_A3;          ///< 执行传真功能，传真彩色A3纸张的总面数
    int32_t             _ptFaxCounterMono_A3;           ///< 执行传真功能，传真黑白A3纸张的总面数
    /* .4.(1 ~ 6) */
    STATIC_STATUS_S     _ptQueryStaticFeature;          ///< 查询打印机静态特征
    DYNAMIC_STATUS_S    _ptQueryDynamicFeature;         ///< 查询打印机动态特征
    AUDIT_JOBS_INFO_S   _ptQueryAuditJobsInfo;          ///< 查询审计作业信息
    TRC_INFO_S          _ptQueryTRC600Info;             ///< 查询600dpi校准曲线
    TRC_INFO_S          _ptQueryTRC1200Info;            ///< 查询1200dpi校准曲线
    TRC_INFO_S          _ptQueryTRC2400Info;            ///< 查询2400dpi校准曲线
    /* .5.1.(1 ~ 2) */
    char                _ptPrinterHostname[32];         ///< 打印机主机名--Pantum-mac地址后六位
    int32_t             _ptIpMethod;                    ///< 提供IP的方法
    /* .5.2.(1 ~ 2) */
    int32_t             _ptWiredConnectStatus;          ///< 有线连接状态
    int32_t             _ptWiredIpMode;                 ///< 有线IP模式
    /* .5.2.3.(1 ~ 4) */
    char                _ptWiredIPAddress[16];          ///< 有线网络ip地址
    char                _ptWiredSubnetMask[16];         ///< 有线网络子网掩码
    char                _ptWiredGateway[16];            ///< 有线网络网关
    char                _ptWiredDNSServerAddress[16];   ///< 有线网络DNS服务器地址
    /* .5.2.4.(1 ~ 8) */
    char                _ptWiredIpv6LinkLocalAddress[64];           ///< 有线IPv6链路本地地址
    int32_t             _ptWiredIpv6ManuEnableFlag;                 ///< 有线IPv6手动启用标志
    char                _ptWiredIpv6ManuConfigureAddr[64];          ///< 有线IPv6手动配置地址
    char                _ptWiredIpv6ManuGatewayAddr[64];            ///< 有线IPv6手动网关地址
    char                _ptWiredIpv6AutoConfigureAddr1[64];         //< 有线IPv6自动配置地址
    char                _ptWiredIpv6AutoGatewayAddr[64];            ///< 有线IPv6自动网关地址
    char                _ptWiredIpv6ManuConfigureAddrMark[64];      ///< 有线IPv6手动配置地址掩码
    char                _ptWiredIpv6AutoConfigureAddrMark1[64];     ///< 有线IPv6自动配置地址掩码
    /* .5.2.5 */
    int32_t             _ptWiredInterfaceRate;                      ///< 有线接口速率
    /* .5.3.(1 ~ 2) */
    int32_t             _ptWirelessStatus;                          ///< 无线连接状态
    int32_t             _ptWirelessIpMode;                          ///< 无线IP模式
    /* .5.3.3.(1 ~ 4) */
    char                _ptWirelessIPAddress[16];                   ///< 无线网络ip地址
    char                _ptWirelessSubnetMask[16];                  ///< 无线网络子网掩码
    char                _ptWirelessGateway[16];                     ///< 无线网络网关
    char                _ptWirelessDNSServerAddr[16];            ///< 无线网络DNS服务器地址
    /* .5.3.4.(1 ~ 8) */
    char                _ptWirelessIpv6LinkLocalAddress[64];        ///< 无线IPv6链路本地地址
    int32_t             _ptWirelessIpv6ManuEnableFlag;              ///< 无线IPv6手动启用标志
    char                _ptWirelessIpv6ManuConfigureAddr[64];       ///< 无线IPv6手动配置地址
    char                _ptWirelessIpv6ManuGatewayAddr[64];         ///< 无线IPv6手动网关地址
    char                _ptWirelessIpv6AutoConfigureAddr1[64];      ///< 无线IPv6自动配置地址
    char                _ptWirelessIpv6AutoGatewayAddr[64];         ///< 无线IPv6自动网关地址
    char                _ptWirelessIpv6ManuConfigureAddrMark[64];   ///< 无线IPv6手动配置地址掩码
    char                _ptWirelessIpv6AutoConfigureAddrMark1[64];  ///< 无线IPv6自动配置地址掩码
    /* .5.3.(5 ~ 10) */
    char                _ptWirelessSSID[32];            ///< 无线网络名称
    int32_t             _ptWirelessType;                ///< 无线类型
    int32_t             _ptEncryptionProtocol;          ///< 加密协议
    int32_t             _ptWEPIndex;                    ///< WEP索引
    char                _ptWirelessWEPPassword[32];     ///< 无线WEP密码
    char                _ptWirelessWPAPSKPassword[32];  ///< 无线WPAPSK密码
    /* .5.4.1.(1 ~ 3) */
    int32_t             _ptDHCPPort;                    ///< DHCP端口号
    int32_t             _ptDHCPEnable;                  ///< 是否启用DHCP协议
    int32_t             _ptDHCPv6Enable;                ///< 是否启用DHCPv6（是否启用ipv6自动配置）
    /* .5.4.2.(1 ~ 2) */
    int32_t             _ptDHCPSPort;                   ///< DHCPS端口号
    int32_t             _ptDHCPSEnable;                 ///< 是否启用DHCPS协议
    /* .5.4.3.(1 ~ 9) */
    int32_t             _ptHTTPPort;                    ///< HTTP端口号
    int32_t             _ptHTTPEnable;                  ///< 是否启用HTTP服务器
    int32_t             _ptHTTPNumLinks;                ///< 可配置HTTP链路数
    int32_t             _ptHTTPBytesRemaining;          ///< 存储HTTP链接/标签 信息的字节数
    int32_t             _ptHTTPLinksIndex;              ///< 标识HTTP链接设置的唯一值
    int32_t             _ptHTTPLinksStatus;             ///< HTTP链接状态
    char                _ptHTTPLinksLabel[32];          ///< HTTP链接的标签
    char                _ptHTTPLinksURL[32];            ///< HTTP链接超链接的URL
    int32_t             _ptHTTPConfigEnable;            ///< 是否启用HTTP配置页面
    /* .5.4.4.(1 ~ 2) */
    int32_t             _ptHTTPSPort;                   ///< HTTPS端口号
    int32_t             _ptHTTPSEnable;                 ///< 是否启用HTTPS协议
    /* .5.4.5.(1 ~ 2) */
    int32_t             _ptLPDPort;                     ///< LPD端口号
    int32_t             _ptLPDEnable;                   ///< 是否启用LPD打印协议
    /* .5.4.6.(1 ~ 2) */
    int32_t             _ptWSDPort;                     ///< WSD端口号
    int32_t             _ptWSDEnable;                   ///< 是否启用WSD协议
    /* .5.4.7.(1 ~ 3) */
    int32_t             _ptSNMPPort;                     ///< SNMP端口号
    int32_t             _ptSNMPEnable;                   ///< 是否启用SNMP协议
    int32_t             _ptSNMPProtocolVer;              ///< SNMP协议支持版本
    /* .5.4.8.(1 ~ 2) */
    int32_t             _ptSMTPPort;                     ///< SMTP端口号
    int32_t             _ptSMTPEnable;                   ///< 是否启用SMTP协议
    /* .5.4.9.(1 ~ 2) */
    int32_t             _ptFTPPort;                      ///< FTP端口号
    int32_t             _ptFTPEnable;                    ///< 是否启用FTP协议
    /* .5.4.10.(1 ~ 3) */
    int32_t             _ptSMBPort;                      ///< SMB端口号
    int32_t             _ptSMBEnable;                    ///< 是否启用SMB协议
    int32_t             _ptSMBAuthentication;            ///< SMB认证方式
    /* .5.4.11.(1 ~ 2) */
    int32_t             _ptSLPPort;                      ///< SLP端口号
    int32_t             _ptSLPEnable;                    ///< 是否启用SLP协议
    /* .5.4.12.(1 ~ 2) */
    int32_t             _ptIPPPort;                      ///< IPP端口号
    int32_t             _ptIPPEnable;                    ///< 是否启用IPP协议
    /* .5.4.13.(1 ~ 2) */
    int32_t             _ptSNTPPort;                     ///< SNTP端口号
    int32_t             _ptSNTPEnable;                   ///< 是否启用SNTP协议
    /* .5.4.14.(1 ~ 2) */
    int32_t             _ptmDNSPort;                     ///< mDNS端口号
    int32_t             _ptmDNSEnable;                   ///< 是否启用mDNS协议
    /* .5.4.15.(1 ~ 3) */
    int32_t             _ptDNSPort;                      ///< DNS端口号
    int32_t             _ptDNSEnable;                    ///< 是否启用DNS协议
    int32_t             _ptWiredIPv4DNSAllocationMethod; ///< 有线ipv4DNS分配方式
    /* .5.4.16.(1 ~ 2) */
    int32_t             _ptBonjourPort;                  ///< Bonjour端口号
    int32_t             _ptBonjourEnable;                ///< 是否启用Bonjour协议
    /* .5.4.17.(1 ~ 2) */
    int32_t             _ptTelnetPort;                   ///< Telnet端口号
    int32_t             _ptTelnetEnable;                 ///< 是否启用Telnet协议
    /* .5.4.18.(1 ~ 2) */
    int32_t             _ptUDPPort;                      ///< UDP端口号
    int32_t             _ptUDPEnable;                    ///< 是否启用UDP协议
    /* .5.4.19.(1 ~ 2) */
    int32_t             _pt9100Port;                     ///< 9100端口号
    int32_t             _pt9100Enable;                   ///< 是否启用9100协议
    /* .5.4.20.(1 ~ 2) */
    int32_t             _ptSoapPort;                     ///< Soap端口号
    int32_t             _ptSoapEnable;                   ///< 是否启用Soap协议
    /* .5.4.21.(1 ~ 2) */
    int32_t             _ptNetBiosPort;                  ///< NetBios端口号
    int32_t             _ptNetBiosEnable;                ///< 是否启用NetBios协议
    /* .5.4.22.(1 ~ 2) */
    int32_t             _ptLLMNRPort;                    ///< LLMNR端口号
    int32_t             _ptLLMNREnable;                  ///< 是否启用LLMNR协议
    /* .5.4.23.(1 ~ 2) */
    int32_t             _ptOpenSSLPort;                  ///< OpenSSL端口号
    int32_t             _ptOpenSSLEnable;                ///< 是否启用OpenSSL协议
    /* .5.4.24.(1 ~ 2) */
    int32_t             _ptAirPrintPort;                 ///< AirPrint端口号
    int32_t             _ptAirPrintEnable;               ///< 是否启用AirPrint协议
    /* .5.4.25.(1 ~ 2) */
    int32_t             _ptAirScanPort;                  ///< AirScan端口号
    int32_t             _ptAirScanEnable;                ///< 是否启用AirScan协议
    /* .5.4.26.(1 ~ 2) */
    int32_t             _ptMopriaPort;                   ///< Mopria端口号
    int32_t             _ptMopriaEnable;                 ///< 是否启用Mopria协议
    /* .5.4.27.(1 ~ 2) */
    int32_t             _ptLLTDPort;                     ///< LLTD端口号
    int32_t             _ptLLTDEnable;                   ///< 是否启用LLTD协议
    /* .5.4.28.(1 ~ 2) */
    int32_t             _ptNetScanPort;                  ///< NetScan端口号
    int32_t             _ptNetScanEnable;                ///< 是否启用NetScan协议
    /* .5.4.29.(1 ~ 2) */
    int32_t             _ptARPPort;                      ///< ARP端口号
    int32_t             _ptARPEnable;                    ///< 是否启用ARP协议
    /* .5.4.30.(1 ~ 2) */
    int32_t             _ptLLAPort;                      ///< LLA端口号
    int32_t             _ptLLAEnable;                    ///< 是否启用LLA协议
    /* .5.4.31.(1 ~ 2) */
    int32_t             _ptTFTPPort;                     ///< TFTP端口号
    int32_t             _ptTFTPEnable;                   ///< 是否启用TFTP协议
    /* .5.4.32.(1 ~ 2) */
    int32_t             _ptNetservicePort;               ///< Netservice端口号
    int32_t             _ptNetserviceEnable;             ///< 是否启用Netservice协议
    /* .5.4.33.(1 ~ 2) */
    int32_t             _ptWi_FiPort;                    ///< Wi-Fi端口号
    int32_t             _ptWi_FiEnable;                  ///< 是否启用Wi-Fi协议
    /* .5.4.34.(1 ~ 2) */
    int32_t             _ptBluetoothPort;                ///< Bluetooth端口号
    int32_t             _ptBluetoothEnable;              ///< 是否启用Bluetooth协议
    /* .5.4.35.(1 ~ 2) */
    int32_t             _ptEthernetPort;                 ///< Ethernet端口号
    int32_t             _ptEthernetEnable;               ///< 是否启用Ethernet协议
    /* .5.4.36.(1 ~ 3) */
    int32_t             _pt802_1XPort;                   ///< 802.1X端口号
    int32_t             _pt802_1XEnable;                 ///< 是否启用802.1X
    int32_t             _pt802_1XAuthenticationMethod;   ///< 802.1X协议认证方式
    /* .5.4.37.(1 ~ 2) */
    int32_t             _ptRAWPort;                     ///< RAW端口号
    int32_t             _ptRAWEnable;                   ///< 是否启用RAW协议
    /* .5.4.38.(1 ~ 2) */
    int32_t             _ptLDAPPort;                    ///< LDAP端口号
    int32_t             _ptLDAPEnable;                  ///< 是否启用LDAP协议
    /* .6.1.(1 ~ 24).(1 ~ 3)  */
    int32_t             _ptPrintStatAllSizeTotalAll;    ///<打印计数器（总计数器）
    int32_t             _ptPrintStatAllSizeTotalSingle; ///<单面打印计数器（包括彩色、黑白及所有纸型）
    int32_t             _ptPrintStatAllSizeTotalDuplex; ///<双面打印计数器（包括彩色、黑白及所有纸型）
    int32_t             _ptPrintStatAllSizeBlackTotal;  ///<黑白打印计数器（包括所有纸型）
    int32_t             _ptPrintStatAllSizeBlackSingle; ///<黑白单面打印计数器（包括所有纸型）
    int32_t             _ptPrintStatAllSizeBlackDuplex; ///<黑白双面打印计数器（包括所有纸型）
    int32_t             _ptPrintStatAllSizeColorAll;    ///<彩色打印计数器（包括所有纸型）
    int32_t             _ptPrintStatAllSizeColorSingle; ///<彩色单面打印计数器（包括所有纸型）
    int32_t             _ptPrintStatAllSizeColorDuplex; ///<彩色双面打印计数器(包括所有纸型)
    int32_t             _ptPrintStatUserdefTotalAll;    ///<打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefTotalSingle; ///<单面打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefTotalDuplex; ///<双面打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefBlackAll;    ///<黑白打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefBlackSingle; ///<黑白单面打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefBlackDuplex; ///<黑白双面打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefColorAll;    ///<彩色打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefColorSingle; ///<彩色单面打印自定义纸型计数器
    int32_t             _ptPrintStatUserdefColorDuplex; ///<彩色双面打印自定义纸型计数器
    int32_t             _ptPrintStatA3TotalAll;         ///<打印A3纸型计数器
    int32_t             _ptPrintStatA3TotalSingle;      ///<单面打印A3纸型计数器
    int32_t             _ptPrintStatA3TotalDuplex;      ///<双面打印A3纸型计数器
    int32_t             _ptPrintStatA3BlackAll;         ///<黑白打印A3纸型计数器
    int32_t             _ptPrintStatA3BlackSingle;      ///<黑白单面打印A3纸型计数器
    int32_t             _ptPrintStatA3BlackDuplex;      ///<黑白双面打印A3纸型计数器
    int32_t             _ptPrintStatA3ColorAll;         ///<彩色打印A3纸型计数器
    int32_t             _ptPrintStatA3ColorSingle;      ///<彩色单面打印A3纸型计数器
    int32_t             _ptPrintStatA3ColorDuplex;      ///<彩色双面打印A3纸型计数器
    int32_t             _ptPrintStatA4TotalAll;         ///<打印A4纸型计数器
    int32_t             _ptPrintStatA4TotalSingle;      ///<单面打印A4纸型计数器
    int32_t             _ptPrintStatA4TotalDuplex;      ///<双面打印A4纸型计数器
    int32_t             _ptPrintStatA4BlackAll;         ///<黑白打印A4纸型计数器
    int32_t             _ptPrintStatA4BlackSingle;      ///<黑白单面打印A4纸型计数器
    int32_t             _ptPrintStatA4BlackDuplex;      ///<黑白双面打印A4纸型计数器
    int32_t             _ptPrintStatA4ColorAll;         ///<彩色打印A4纸型计数器
    int32_t             _ptPrintStatA4ColorSingle;      ///<彩色单面打印A4纸型计数器
    int32_t             _ptPrintStatA4ColorDuplex;      ///<彩色双面打印A4纸型计数器
    int32_t             _ptPrintStatA5TotalAll;         ///<打印A5纸型计数器
    int32_t             _ptPrintStatA5TotalSingle;      ///<单面打印A5纸型计数器
    int32_t             _ptPrintStatA5TotalDuplex;      ///<双面打印A5纸型计数器
    int32_t             _ptPrintStatA5BlackAll;         ///<黑白打印A5纸型计数器
    int32_t             _ptPrintStatA5BlackSingle;      ///<黑白单面打印A5纸型计数器
    int32_t             _ptPrintStatA5BlackDuplex;      ///<黑白双面打印A5纸型计数器
    int32_t             _ptPrintStatA5ColorAll;         ///<彩色打印A5纸型计数器
    int32_t             _ptPrintStatA5ColorSingle;      ///<彩色单面打印A5纸型计数器
    int32_t             _ptPrintStatA5ColorDuplex;      ///<彩色双面打印A5纸型计数器
    int32_t             _ptPrintStatB4TotalAll;         ///<打印B4纸型计数器
    int32_t             _ptPrintStatB4TotalSingle;      ///<单面打印B4纸型计数器
    int32_t             _ptPrintStatB4TotalDuplex;      ///<双面打印B4纸型计数器
    int32_t             _ptPrintStatB4BlackAll;         ///<黑白打印B4纸型计数器
    int32_t             _ptPrintStatB4BlackSingle;      ///<黑白单面打印B4纸型计数器
    int32_t             _ptPrintStatB4BlackDuplex;      ///<黑白双面打印B4纸型计数器
    int32_t             _ptPrintStatB4ColorAll;         ///<彩色打印B4纸型计数器
    int32_t             _ptPrintStatB4ColorSingle;      ///<彩色单面打印B4纸型计数器
    int32_t             _ptPrintStatB4ColorDuplex;      ///<彩色双面打印B4纸型计数器
    int32_t             _ptPrintStatJISB5TotalAll;      ///<打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5TotalSingle;   ///<单面打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5TotalDuplex;   ///<双面打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5BlackAll;      ///<黑白打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5BlackSingle;   ///<黑白单面打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5BlackDuplex;   ///<黑白双面打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5ColorAll;      ///<彩色打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5ColorSingle;   ///<彩色单面打印JISB5纸型计数器
    int32_t             _ptPrintStatJISB5ColorDuplex;   ///<彩色双面打印JISB5纸型计数器
    int32_t             _ptPrintStatIOSB5TotalAll;      ///<打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5TotalSingle;   ///<单面打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5TotalDuplex;   ///<双面打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5BlackAll;      ///<黑白打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5BlackSingle;   ///<黑白单面打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5BlackDuplex;   ///<黑白双面打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5ColorAll;      ///<彩色打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5ColorSingle;   ///<彩色单面打印ISOB5纸型计数器
    int32_t             _ptPrintStatIOSB5ColorDuplex;   ///<彩色双面打印ISOB5纸型计数器
    int32_t             _ptPrintStat11x17TotalAll;      ///<打印11x17纸型计数器
    int32_t             _ptPrintStat11x17TotalSingle;   ///<单面打印11x17纸型计数器
    int32_t             _ptPrintStat11x17TotalDuplex;   ///<双面打印11x17纸型计数器
    int32_t             _ptPrintStat11x17BlackAll;      ///<黑白打印11x17纸型计数器
    int32_t             _ptPrintStat11x17BlackSingle;   ///<黑白单面打印11x17纸型计数器
    int32_t             _ptPrintStat11x17BlackDuplex;   ///<黑白双面打印11x17纸型计数器
    int32_t             _ptPrintStat11x17ColorAll;      ///<彩色打印11x17纸型计数器
    int32_t             _ptPrintStat11x17ColorSingle;   ///<彩色单面打印11x17纸型计数器
    int32_t             _ptPrintStat11x17ColorDuplex;   ///<彩色双面打印11x17纸型计数器
    int32_t             _ptPrintStatLetterTotalAll;     ///<打印Letter纸型计数器
    int32_t             _ptPrintStatLetterTotalSingle;  ///<单面打印Letter纸型计数器
    int32_t             _ptPrintStatLetterTotalDuplex;  ///<双面打印Letter纸型计数器
    int32_t             _ptPrintStatLetterBlackAll;     ///<黑白打印Letter纸型计数器
    int32_t             _ptPrintStatLetterBlackSingle;  ///<黑白单面打印Letter纸型计数器
    int32_t             _ptPrintStatLetterBlackDuplex;  ///<黑白双面打印Letter纸型计数器
    int32_t             _ptPrintStatLetterColorAll;     ///<彩色打印Letter纸型计数器
    int32_t             _ptPrintStatLetterColorSingle;  ///<彩色单面打印Letter纸型计数器
    int32_t             _ptPrintStatLetterColorDuplex;  ///<彩色双面打印Letter纸型计数器
    int32_t             _ptPrintStatLegalTotalAll;      ///<打印Legal纸型计数器
    int32_t             _ptPrintStatLegalTotalSingle;   ///<单面打印Legal纸型计数器
    int32_t             _ptPrintStatLegalTotalDuplex;   ///<双面打印Legal纸型计数器
    int32_t             _ptPrintStatLegalBlackAll;      ///<黑白打印Legal纸型计数器
    int32_t             _ptPrintStatLegalBlackSingle;   ///<黑白单面打印Legal纸型计数器
    int32_t             _ptPrintStatLegalBlackDuplex;   ///<黑白双面打印Legal纸型计数器
    int32_t             _ptPrintStatLegalColorAll;      ///<彩色打印Legal纸型计数器
    int32_t             _ptPrintStatLegalColorSingle;   ///<彩色单面打印Legal纸型计数器
    int32_t             _ptPrintStatLegalColorDuplex;   ///<彩色双面打印Legal纸型计数器
    int32_t             _ptPrintStatFilioTotalAll;      ///<打印Filio纸型计数器
    int32_t             _ptPrintStatFilioTotalSingle;   ///<单面打印Filio纸型计数器
    int32_t             _ptPrintStatFilioTotalDuplex;   ///<双面打印Filio纸型计数器
    int32_t             _ptPrintStatFilioBlackAll;      ///<黑白打印Filio纸型计数器
    int32_t             _ptPrintStatFilioBlackSingle;   ///<黑白单面打印Filio纸型计数器
    int32_t             _ptPrintStatFilioBlackDuplex;   ///<黑白双面打印Filio纸型计数器
    int32_t             _ptPrintStatFilioColorAll;      ///<彩色打印Filio纸型计数器
    int32_t             _ptPrintStatFilioColorSingle;   ///<彩色单面打印Filio纸型计数器
    int32_t             _ptPrintStatFilioColorDuplex;   ///<彩色双面打印Filio纸型计数器
    int32_t             _ptPrintStatOficioTotalAll;     ///<打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioTotalSingle;  ///<单面打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioTotalDuplex;  ///<双面打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioBlackAll;     ///<黑白打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioBlackSingle;  ///<黑白单面打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioBlackDuplex;  ///<黑白双面打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioColorAll;     ///<彩色打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioColorSingle;  ///<彩色单面打印Oficio纸型计数器
    int32_t             _ptPrintStatOficioColorDuplex;  ///<彩色双面打印Oficio纸型计数器
    int32_t             _ptPrintStatExecutiveTotalAll;  ///<打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveTotalSingle;   ///<单面打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveTotalDuplex;   ///<双面打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveBlackAll;      ///<黑白打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveBlackSingle;   ///<黑白单面打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveBlackDuplex;   ///<黑白双面打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveColorAll;      ///<彩色打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveColorSingle;   ///<彩色单面打印Executive纸型计数器
    int32_t             _ptPrintStatExecutiveColorDuplex;   ///<彩色双面打印Executive纸型计数器
    int32_t             _ptPrintStatStatementTotalAll;      ///<打印Statement纸型计数器
    int32_t             _ptPrintStatStatementTotalSingle;   ///<单面打印Statement纸型计数器
    int32_t             _ptPrintStatStatementTotalDuplex;   ///<双面打印Statement纸型计数器
    int32_t             _ptPrintStatStatementBlackAll;      ///<黑白打印Statement纸型计数器
    int32_t             _ptPrintStatStatementBlackSingle;   ///<黑白单面打印Statement纸型计数器
    int32_t             _ptPrintStatStatementBlackDuplex;   ///<黑白双面打印Statement纸型计数器（不支持该节点时返回-2
    int32_t             _ptPrintStatStatementColorAll;      ///<彩色打印Statement纸型计数器
    int32_t             _ptPrintStatStatementColorSingle;   ///<彩色单面打印Statement纸型计数器
    int32_t             _ptPrintStatStatementColorDuplex;   ///<彩色双面打印Statement纸型计数器
    int32_t             _ptPrintStat8KTotalAll;         ///<打印8K纸型计数器
    int32_t             _ptPrintStat8KTotalSingle;      ///<单面打印8K纸型计数器
    int32_t             _ptPrintStat8KTotalDuplex;      ///<双面打印8K纸型计数器
    int32_t             _ptPrintStat8KBlackAll;         ///<黑白打印8K纸型计数器
    int32_t             _ptPrintStat8KBlackSingle;      ///<黑白单面打印8K纸型计数器
    int32_t             _ptPrintStat8KBlackDuplex;      ///<黑白双面打印8K纸型计数器
    int32_t             _ptPrintStat8KColorAll;         ///<彩色打印8K纸型计数器
    int32_t             _ptPrintStat8KColorSingle;      ///<彩色单面打印8K纸型计数器
    int32_t             _ptPrintStat8KColorDuplex;      ///<彩色双面打印8K纸型计数器
    int32_t             _ptPrintStat16KTotalAll;        ///<打印16K纸型计数器
    int32_t             _ptPrintStat16KTotalSingle;     ///<单面打印16K纸型计数器
    int32_t             _ptPrintStat16KTotalDuplex;     ///<双面打印16K纸型计数器
    int32_t             _ptPrintStat16KBlackAll;        ///<黑白打印16K纸型计数器
    int32_t             _ptPrintStat16KBlackSingle;     ///<黑白单面打印16K纸型计数器
    int32_t             _ptPrintStat16KBlackDuplex;     ///<黑白双面打印16K纸型计数器
    int32_t             _ptPrintStat16KColorAll;        ///<彩色打印16K纸型计数器
    int32_t             _ptPrintStat16KColorSingle;     ///<彩色单面打印16K纸型计数器
    int32_t             _ptPrintStat16KColorDuplex;     ///<彩色双面打印16K纸型计数器
    int32_t             _ptPrintStatA6TotalAll;         ///<打印A6纸型计数器
    int32_t             _ptPrintStatA6TotalSingle;      ///<单面打印A6纸型计数器
    int32_t             _ptPrintStatA6TotalDuplex;      ///<双面打印A6纸型计数器
    int32_t             _ptPrintStatA6BlackAll;         ///<黑白打印A6纸型计数器
    int32_t             _ptPrintStatA6BlackSingle;      ///<黑白单面打印A6纸型计数器
    int32_t             _ptPrintStatA6BlackDuplex;      ///<黑白双面打印A6纸型计数器
    int32_t             _ptPrintStatA6ColorAll;         ///<彩色打印A6纸型计数器
    int32_t             _ptPrintStatA6ColorSingle;      ///<彩色单面打印A6纸型计数器
    int32_t             _ptPrintStatA6ColorDuplex;      ///<彩色双面打印A6纸型计数器
    int32_t             _ptPrintStatEnv10TotalAll;      ///<打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10TotalSingle;   ///<单面打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10TotalDuplex;   ///<双面打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10BlackAll;      ///<黑白打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10BlackSingle;   ///<黑白单面打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10BlackDuplex;   ///<黑白双面打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10ColorAll;      ///<彩色打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10ColorSingle;   ///<彩色单面打印Env10纸型计数器
    int32_t             _ptPrintStatEnv10ColorDuplex;   ///<彩色双面打印Env10纸型计数器
    int32_t             _ptPrintStatEnvMonTotalAll;     ///<打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonTotalSingle;  ///<单面打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonTotalDuplex;  ///<双面打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonBlackAll;     ///<黑白打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonBlackSingle;  ///<黑白单面打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonBlackDuplex;  ///<黑白双面打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonColorAll;     ///<彩色打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonColorSingle;  ///<彩色单面打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvMonColorDuplex;  ///<彩色双面打印EnvMon纸型计数器
    int32_t             _ptPrintStatEnvC6TotalAll;      ///<打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6TotalSingle;   ///<单面打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6TotalDuplex;   ///<双面打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6BlackAll;      ///<黑白打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6BlackSingle;   ///<黑白单面打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6BlackDuplex;   ///<黑白双面打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6ColorAll;      ///<彩色打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6ColorSingle;   ///<彩色单面打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC6ColorDuplex;   ///<彩色双面打印EnvC6纸型计数器
    int32_t             _ptPrintStatEnvC5TotalAll;      ///<打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5TotalSingle;   ///<单面打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5TotalDuplex;   ///<双面打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5BlackAll;      ///<黑白打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5BlackSingle;   ///<黑白单面打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5BlackDuplex;   ///<黑白双面打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5ColorAll;      ///<彩色打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5ColorSingle;   ///<彩色单面打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvC5ColorDuplex;   ///<彩色双面打印EnvC5纸型计数器
    int32_t             _ptPrintStatEnvDLTotalAll;      ///<打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLTotalSingle;   ///<单面打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLTotalDuplex;   ///<双面打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLBlackAll;      ///<黑白打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLBlackSingle;   ///<黑白单面打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLBlackDuplex;   ///<黑白双面打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLColorAll;      ///<彩色打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLColorSingle;   ///<彩色单面打印EnvDL纸型计数器
    int32_t             _ptPrintStatEnvDLColorDuplex;   ///<彩色双面打印EnvDL纸型计数器
    int32_t             _ptPrintStatPostcardTotalAll;   ///<打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardTotalSingle;    ///<单面打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardTotalDuplex;    ///<双面打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardBlackAll;       ///<黑白打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardBlackSingle;    ///<黑白单面打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardBlackDuplex;    ///<黑白双面打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardColorAll;       ///<彩色打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardColorSingle;    ///<彩色单面打印Postcard纸型计数器
    int32_t             _ptPrintStatPostcardColorDuplex;    ///<彩色双面打印Postcard纸型计数器
    /* .6.2.(1 ~ 24).(1 ~ 3) */
    int32_t             _ptScanStatAllSizeTotalAll;         ///<扫描计数器（总计数器）
    int32_t             _ptScanStatAllSizeTotalSingle;      ///<单面扫描计数器（包括彩色、黑白及所有纸型）
    int32_t             _ptScanStatAllSizeTotalDuplex;      ///<双面扫描计数器（包括彩色、黑白及所有纸型）
    int32_t             _ptScanStatAllSizeBlackTotal;       ///<黑白扫描计数器（包括所有纸型）
    int32_t             _ptScanStatAllSizeBlackSingle;      ///<黑白单面扫描计数器（包括所有纸型）
    int32_t             _ptScanStatAllSizeBlackDuplex;      ///<黑白双面扫描计数器（包括所有纸型）
    int32_t             _ptScanStatAllSizeColorAll;         ///<彩色扫描计数器（包括所有纸型）
    int32_t             _ptScanStatAllSizeColorSingle;      ///<彩色单面扫描计数器（包括所有纸型）
    int32_t             _ptScanStatAllSizeColorDuplex;      ///<彩色双面扫描计数器(包括所有纸型)
    int32_t             _ptScanStatUserdefTotalAll;         ///<扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefTotalSingle;      ///<单面扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefTotalDuplex;      ///<双面扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefBlackAll;         ///<黑白扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefBlackSingle;      ///<黑白单面扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefBlackDuplex;      ///<黑白双面扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefColorAll;         ///<彩色扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefColorSingle;      ///<彩色单面扫描自定义纸型计数器
    int32_t             _ptScanStatUserdefColorDuplex;      ///<彩色双面扫描自定义纸型计数器
    int32_t             _ptScanStatA3TotalAll;              ///<扫描A3纸型计数器
    int32_t             _ptScanStatA3TotalSingle;           ///<单面扫描A3纸型计数器
    int32_t             _ptScanStatA3TotalDuplex;           ///<双面扫描A3纸型计数器
    int32_t             _ptScanStatA3BlackAll;              ///<黑白扫描A3纸型计数器
    int32_t             _ptScanStatA3BlackSingle;           ///<黑白单面扫描A3纸型计数器
    int32_t             _ptScanStatA3BlackDuplex;           ///<黑白双面扫描A3纸型计数器
    int32_t             _ptScanStatA3ColorAll;              ///<彩色扫描A3纸型计数器
    int32_t             _ptScanStatA3ColorSingle;           ///<彩色单面扫描A3纸型计数器
    int32_t             _ptScanStatA3ColorDuplex;           ///<彩色双面扫描A3纸型计数器
    int32_t             _ptScanStatA4TotalAll;              ///<扫描A4纸型计数器
    int32_t             _ptScanStatA4TotalSingle;           ///<单面扫描A4纸型计数器
    int32_t             _ptScanStatA4TotalDuplex;           ///<双面扫描A4纸型计数器
    int32_t             _ptScanStatA4BlackAll;              ///<黑白扫描A4纸型计数器
    int32_t             _ptScanStatA4BlackSingle;           ///<黑白单面扫描A4纸型计数器
    int32_t             _ptScanStatA4BlackDuplex;           ///<黑白双面扫描A4纸型计数器
    int32_t             _ptScanStatA4ColorAll;              ///<彩色扫描A4纸型计数器
    int32_t             _ptScanStatA4ColorSingle;           ///<彩色单面扫描A4纸型计数器
    int32_t             _ptScanStatA4ColorDuplex;           ///<彩色双面扫描A4纸型计数器
    int32_t             _ptScanStatA5TotalAll;              ///<扫描A5纸型计数器
    int32_t             _ptScanStatA5TotalSingle;           ///<单面扫描A5纸型计数器
    int32_t             _ptScanStatA5TotalDuplex;           ///<双面扫描A5纸型计数器
    int32_t             _ptScanStatA5BlackAll;              ///<黑白扫描A5纸型计数器
    int32_t             _ptScanStatA5BlackSingle;           ///<黑白单面扫描A5纸型计数器
    int32_t             _ptScanStatA5BlackDuplex;           ///<黑白双面扫描A5纸型计数器
    int32_t             _ptScanStatA5ColorAll;              ///<彩色扫描A5纸型计数器
    int32_t             _ptScanStatA5ColorSingle;           ///<彩色单面扫描A5纸型计数器
    int32_t             _ptScanStatA5ColorDuplex;           ///<彩色双面扫描A5纸型计数器
    int32_t             _ptScanStatB4TotalAll;              ///<扫描B4纸型计数器
    int32_t             _ptScanStatB4TotalSingle;           ///<单面扫描B4纸型计数器
    int32_t             _ptScanStatB4TotalDuplex;           ///<双面扫描B4纸型计数器
    int32_t             _ptScanStatB4BlackAll;              ///<黑白扫描B4纸型计数器
    int32_t             _ptScanStatB4BlackSingle;           ///<黑白单面扫描B4纸型计数器
    int32_t             _ptScanStatB4BlackDuplex;           ///<黑白双面扫描B4纸型计数器
    int32_t             _ptScanStatB4ColorAll;              ///<彩色扫描B4纸型计数器
    int32_t             _ptScanStatB4ColorSingle;           ///<彩色单面扫描B4纸型计数器
    int32_t             _ptScanStatB4ColorDuplex;           ///<彩色双面扫描B4纸型计数器
    int32_t             _ptScanStatJISB5TotalAll;           ///<扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5TotalSingle;        ///<单面扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5TotalDuplex;        ///<双面扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5BlackAll;           ///<黑白扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5BlackSingle;        ///<黑白单面扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5BlackDuplex;        ///<黑白双面扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5ColorAll;           ///<彩色扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5ColorSingle;        ///<彩色单面扫描JISB5纸型计数器
    int32_t             _ptScanStatJISB5ColorDuplex;        ///<彩色双面扫描JISB5纸型计数器
    int32_t             _ptScanStatIOSB5TotalAll;           ///<扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5TotalSingle;        ///<单面扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5TotalDuplex;        ///<双面扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5BlackAll;           ///<黑白扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5BlackSingle;        ///<黑白单面扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5BlackDuplex;        ///<黑白双面扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5ColorAll;           ///<彩色扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5ColorSingle;        ///<彩色单面扫描ISOB5纸型计数器
    int32_t             _ptScanStatIOSB5ColorDuplex;        ///<彩色双面扫描ISOB5纸型计数器
    int32_t             _ptScanStat11x17TotalAll;           ///<扫描11x17纸型计数器
    int32_t             _ptScanStat11x17TotalSingle;        ///<单面扫描11x17纸型计数器
    int32_t             _ptScanStat11x17TotalDuplex;        ///<双面扫描11x17纸型计数器
    int32_t             _ptScanStat11x17BlackAll;           ///<黑白扫描11x17纸型计数器
    int32_t             _ptScanStat11x17BlackSingle;        ///<黑白单面扫描11x17纸型计数器
    int32_t             _ptScanStat11x17BlackDuplex;        ///<黑白双面扫描11x17纸型计数器
    int32_t             _ptScanStat11x17ColorAll;           ///<彩色扫描11x17纸型计数器
    int32_t             _ptScanStat11x17ColorSingle;        ///<彩色单面扫描11x17纸型计数器
    int32_t             _ptScanStat11x17ColorDuplex;        ///<彩色双面扫描11x17纸型计数器
    int32_t             _ptScanStatLetterTotalAll;          ///<扫描Letter纸型计数器
    int32_t             _ptScanStatLetterTotalSingle;       ///<单面扫描Letter纸型计数器
    int32_t             _ptScanStatLetterTotalDuplex;       ///<双面扫描Letter纸型计数器
    int32_t             _ptScanStatLetterBlackAll;          ///<黑白扫描Letter纸型计数器
    int32_t             _ptScanStatLetterBlackSingle;       ///<黑白单面扫描Letter纸型计数器
    int32_t             _ptScanStatLetterBlackDuplex;       ///<黑白双面扫描Letter纸型计数器
    int32_t             _ptScanStatLetterColorAll;          ///<彩色扫描Letter纸型计数器
    int32_t             _ptScanStatLetterColorSingle;       ///<彩色单面扫描Letter纸型计数器
    int32_t             _ptScanStatLetterColorDuplex;       ///<彩色双面扫描Letter纸型计数器
    int32_t             _ptScanStatLegalTotalAll;           ///<扫描Legal纸型计数器
    int32_t             _ptScanStatLegalTotalSingle;        ///<单面扫描Legal纸型计数器
    int32_t             _ptScanStatLegalTotalDuplex;        ///<双面扫描Legal纸型计数器
    int32_t             _ptScanStatLegalBlackAll;           ///<黑白扫描Legal纸型计数器
    int32_t             _ptScanStatLegalBlackSingle;        ///<黑白单面扫描Legal纸型计数器
    int32_t             _ptScanStatLegalBlackDuplex;        ///<黑白双面扫描Legal纸型计数器
    int32_t             _ptScanStatLegalColorAll;           ///<彩色扫描Legal纸型计数器
    int32_t             _ptScanStatLegalColorSingle;        ///<彩色单面扫描Legal纸型计数器
    int32_t             _ptScanStatLegalColorDuplex;        ///<彩色双面扫描Legal纸型计数器
    int32_t             _ptScanStatFilioTotalAll;           ///<扫描Filio纸型计数器
    int32_t             _ptScanStatFilioTotalSingle;        ///<单面扫描Filio纸型计数器
    int32_t             _ptScanStatFilioTotalDuplex;        ///<双面扫描Filio纸型计数器
    int32_t             _ptScanStatFilioBlackAll;           ///<黑白扫描Filio纸型计数器
    int32_t             _ptScanStatFilioBlackSingle;        ///<黑白单面扫描Filio纸型计数器
    int32_t             _ptScanStatFilioBlackDuplex;        ///<黑白双面扫描Filio纸型计数器
    int32_t             _ptScanStatFilioColorAll;           ///<彩色扫描Filio纸型计数器
    int32_t             _ptScanStatFilioColorSingle;        ///<彩色单面扫描Filio纸型计数器
    int32_t             _ptScanStatFilioColorDuplex;        ///<彩色双面扫描Filio纸型计数器
    int32_t             _ptScanStatOficioTotalAll;          ///<扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioTotalSingle;       ///<单面扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioTotalDuplex;       ///<双面扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioBlackAll;          ///<黑白扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioBlackSingle;       ///<黑白单面扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioBlackDuplex;       ///<黑白双面扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioColorAll;          ///<彩色扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioColorSingle;       ///<彩色单面扫描Oficio纸型计数器
    int32_t             _ptScanStatOficioColorDuplex;       ///<彩色双面扫描Oficio纸型计数器
    int32_t             _ptScanStatExecutiveTotalAll;       ///<扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveTotalSingle;    ///<单面扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveTotalDuplex;    ///<双面扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveBlackAll;       ///<黑白扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveBlackSingle;    ///<黑白单面扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveBlackDuplex;    ///<黑白双面扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveColorAll;       ///<彩色扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveColorSingle;    ///<彩色单面扫描Executive纸型计数器
    int32_t             _ptScanStatExecutiveColorDuplex;    ///<彩色双面扫描Executive纸型计数器
    int32_t             _ptScanStatStatementTotalAll;       ///<扫描Statement纸型计数器
    int32_t             _ptScanStatStatementTotalSingle;    ///<单面扫描Statement纸型计数器
    int32_t             _ptScanStatStatementTotalDuplex;    ///<双面扫描Statement纸型计数器
    int32_t             _ptScanStatStatementBlackAll;       ///<黑白扫描Statement纸型计数器
    int32_t             _ptScanStatStatementBlackSingle;    ///<黑白单面扫描Statement纸型计数器
    int32_t             _ptScanStatStatementBlackDuplex;    ///<黑白双面扫描Statement纸型计数器
    int32_t             _ptScanStatStatementColorAll;       ///<彩色扫描Statement纸型计数器
    int32_t             _ptScanStatStatementColorSingle;    ///<彩色单面扫描Statement纸型计数器
    int32_t             _ptScanStatStatementColorDuplex;    ///<彩色双面扫描Statement纸型计数器
    int32_t             _ptScanStat8KTotalAll;              ///<扫描8K纸型计数器
    int32_t             _ptScanStat8KTotalSingle;           ///<单面扫描8K纸型计数器
    int32_t             _ptScanStat8KTotalDuplex;           ///<双面扫描8K纸型计数器
    int32_t             _ptScanStat8KBlackAll;              ///<黑白扫描8K纸型计数器
    int32_t             _ptScanStat8KBlackSingle;           ///<黑白单面扫描8K纸型计数器
    int32_t             _ptScanStat8KBlackDuplex;           ///<黑白双面扫描8K纸型计数器
    int32_t             _ptScanStat8KColorAll;              ///<彩色扫描8K纸型计数器
    int32_t             _ptScanStat8KColorSingle;           ///<彩色单面扫描8K纸型计数器
    int32_t             _ptScanStat8KColorDuplex;           ///<彩色双面扫描8K纸型计数器
    int32_t             _ptScanStat16KTotalAll;             ///<扫描16K纸型计数器
    int32_t             _ptScanStat16KTotalSingle;          ///<单面扫描16K纸型计数器
    int32_t             _ptScanStat16KTotalDuplex;          ///<双面扫描16K纸型计数器
    int32_t             _ptScanStat16KBlackAll;             ///<黑白扫描16K纸型计数器
    int32_t             _ptScanStat16KBlackSingle;          ///<黑白单面扫描16K纸型计数器
    int32_t             _ptScanStat16KBlackDuplex;          ///<黑白双面扫描16K纸型计数器
    int32_t             _ptScanStat16KColorAll;             ///<彩色扫描16K纸型计数器
    int32_t             _ptScanStat16KColorSingle;          ///<彩色单面扫描16K纸型计数器
    int32_t             _ptScanStat16KColorDuplex;          ///<彩色双面扫描16K纸型计数器
    int32_t             _ptScanStatA6TotalAll;              ///<扫描A6纸型计数器
    int32_t             _ptScanStatA6TotalSingle;           ///<单面扫描A6纸型计数器
    int32_t             _ptScanStatA6TotalDuplex;           ///<双面扫描A6纸型计数器
    int32_t             _ptScanStatA6BlackAll;              ///<黑白扫描A6纸型计数器
    int32_t             _ptScanStatA6BlackSingle;           ///<黑白单面扫描A6纸型计数器
    int32_t             _ptScanStatA6BlackDuplex;           ///<黑白双面扫描A6纸型计数器
    int32_t             _ptScanStatA6ColorAll;              ///<彩色扫描A6纸型计数器
    int32_t             _ptScanStatA6ColorSingle;           ///<彩色单面扫描A6纸型计数器
    int32_t             _ptScanStatA6ColorDuplex;           ///<彩色双面扫描A6纸型计数器
    int32_t             _ptScanStatEnv10TotalAll;           ///<扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10TotalSingle;        ///<单面扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10TotalDuplex;        ///<双面扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10BlackAll;           ///<黑白扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10BlackSingle;        ///<黑白单面扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10BlackDuplex;        ///<黑白双面扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10ColorAll;           ///<彩色扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10ColorSingle;        ///<彩色单面扫描Env10纸型计数器
    int32_t             _ptScanStatEnv10ColorDuplex;        ///<彩色双面扫描Env10纸型计数器
    int32_t             _ptScanStatEnvMonTotalAll;          ///<扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonTotalSingle;       ///<单面扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonTotalDuplex;       ///<双面扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonBlackAll;          ///<黑白扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonBlackSingle;       ///<黑白单面扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonBlackDuplex;       ///<黑白双面扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonColorAll;          ///<彩色扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonColorSingle;       ///<彩色单面扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvMonColorDuplex;       ///<彩色双面扫描EnvMon纸型计数器
    int32_t             _ptScanStatEnvC6TotalAll;           ///<扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6TotalSingle;        ///<单面扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6TotalDuplex;        ///<双面扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6BlackAll;           ///<黑白扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6BlackSingle;        ///<黑白单面扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6BlackDuplex;        ///<黑白双面扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6ColorAll;           ///<彩色扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6ColorSingle;        ///<彩色单面扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC6ColorDuplex;        ///<彩色双面扫描EnvC6纸型计数器
    int32_t             _ptScanStatEnvC5TotalAll;           ///<扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5TotalSingle;        ///<单面扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5TotalDuplex;        ///<双面扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5BlackAll;           ///<黑白扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5BlackSingle;        ///<黑白单面扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5BlackDuplex;        ///<黑白双面扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5ColorAll;           ///<彩色扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5ColorSingle;        ///<彩色单面扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvC5ColorDuplex;        ///<彩色双面扫描EnvC5纸型计数器
    int32_t             _ptScanStatEnvDLTotalAll;           ///<扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLTotalSingle;        ///<单面扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLTotalDuplex;        ///<双面扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLBlackAll;           ///<黑白扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLBlackSingle;        ///<黑白单面扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLBlackDuplex;        ///<黑白双面扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLColorAll;           ///<彩色扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLColorSingle;        ///<彩色单面扫描EnvDL纸型计数器
    int32_t             _ptScanStatEnvDLColorDuplex;        ///<彩色双面扫描EnvDL纸型计数器
    int32_t             _ptScanStatPostcardTotalAll;        ///<扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardTotalSingle;     ///<单面扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardTotalDuplex;     ///<双面扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardBlackAll;        ///<黑白扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardBlackSingle;     ///<黑白单面扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardBlackDuplex;     ///<黑白双面扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardColorAll;        ///<彩色扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardColorSingle;     ///<彩色单面扫描Postcard纸型计数器
    int32_t             _ptScanStatPostcardColorDuplex;     ///<彩色双面扫描Postcard纸型计数器
    /* .6.3.(1 ~ 24) */
    int32_t             _ptCopyStatAllSizeTotalAll;         ///<复印计数器（总计数器）
    int32_t             _ptCopyStatAllSizeTotalSingle;      ///<单面复印计数器（包括彩色、黑白及所有纸型）
    int32_t             _ptCopyStatAllSizeTotalDuplex;      ///<双面复印计数器（包括彩色、黑白及所有纸型）
    int32_t             _ptCopyStatAllSizeBlackTotal;       ///<黑白复印计数器（包括所有纸型）
    int32_t             _ptCopyStatAllSizeBlackSingle;      ///<黑白单面复印计数器（包括所有纸型）
    int32_t             _ptCopyStatAllSizeBlackDuplex;      ///<黑白双面复印计数器（包括所有纸型）
    int32_t             _ptCopyStatAllSizeColorAll;         ///<彩色复印计数器（包括所有纸型）
    int32_t             _ptCopyStatAllSizeColorSingle;      ///<彩色单面复印计数器（包括所有纸型）
    int32_t             _ptCopyStatAllSizeColorDuplex;      ///<彩色双面复印计数器(包括所有纸型)
    int32_t             _ptCopyStatUserdefTotalAll;         ///<复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefTotalSingle;      ///<单面复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefTotalDuplex;      ///<双面复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefBlackAll;         ///<黑白复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefBlackSingle;      ///<黑白单面复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefBlackDuplex;      ///<黑白双面复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefColorAll;         ///<彩色复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefColorSingle;      ///<彩色单面复印自定义纸型计数器
    int32_t             _ptCopyStatUserdefColorDuplex;      ///<彩色双面复印自定义纸型计数器
    int32_t             _ptCopyStatA3TotalAll;              ///<复印A3纸型计数器
    int32_t             _ptCopyStatA3TotalSingle;           ///<单面复印A3纸型计数器
    int32_t             _ptCopyStatA3TotalDuplex;           ///<双面复印A3纸型计数器
    int32_t             _ptCopyStatA3BlackAll;              ///<黑白复印A3纸型计数器
    int32_t             _ptCopyStatA3BlackSingle;           ///<黑白单面复印A3纸型计数器
    int32_t             _ptCopyStatA3BlackDuplex;           ///<黑白双面复印A3纸型计数器
    int32_t             _ptCopyStatA3ColorAll;              ///<彩色复印A3纸型计数器
    int32_t             _ptCopyStatA3ColorSingle;           ///<彩色单面复印A3纸型计数器
    int32_t             _ptCopyStatA3ColorDuplex;           ///<彩色双面复印A3纸型计数器
    int32_t             _ptCopyStatA4TotalAll;              ///<复印A4纸型计数器
    int32_t             _ptCopyStatA4TotalSingle;           ///<单面复印A4纸型计数器
    int32_t             _ptCopyStatA4TotalDuplex;           ///<双面复印A4纸型计数器
    int32_t             _ptCopyStatA4BlackAll;              ///<黑白复印A4纸型计数器
    int32_t             _ptCopyStatA4BlackSingle;           ///<黑白单面复印A4纸型计数器
    int32_t             _ptCopyStatA4BlackDuplex;           ///<黑白双面复印A4纸型计数器
    int32_t             _ptCopyStatA4ColorAll;              ///<彩色复印A4纸型计数器
    int32_t             _ptCopyStatA4ColorSingle;           ///<彩色单面复印A4纸型计数器
    int32_t             _ptCopyStatA4ColorDuplex;           ///<彩色双面复印A4纸型计数器
    int32_t             _ptCopyStatA5TotalAll;              ///<复印A5纸型计数器
    int32_t             _ptCopyStatA5TotalSingle;           ///<单面复印A5纸型计数器
    int32_t             _ptCopyStatA5TotalDuplex;           ///<双面复印A5纸型计数器
    int32_t             _ptCopyStatA5BlackAll;              ///<黑白复印A5纸型计数器
    int32_t             _ptCopyStatA5BlackSingle;           ///<黑白单面复印A5纸型计数器
    int32_t             _ptCopyStatA5BlackDuplex;           ///<黑白双面复印A5纸型计数器
    int32_t             _ptCopyStatA5ColorAll;              ///<彩色复印A5纸型计数器
    int32_t             _ptCopyStatA5ColorSingle;           ///<彩色单面复印A5纸型计数器
    int32_t             _ptCopyStatA5ColorDuplex;           ///<彩色双面复印A5纸型计数器
    int32_t             _ptCopyStatB4TotalAll;              ///<复印B4纸型计数器
    int32_t             _ptCopyStatB4TotalSingle;           ///<单面复印B4纸型计数器
    int32_t             _ptCopyStatB4TotalDuplex;           ///<双面复印B4纸型计数器
    int32_t             _ptCopyStatB4BlackAll;              ///<黑白复印B4纸型计数器
    int32_t             _ptCopyStatB4BlackSingle;           ///<黑白单面复印B4纸型计数器
    int32_t             _ptCopyStatB4BlackDuplex;           ///<黑白双面复印B4纸型计数器
    int32_t             _ptCopyStatB4ColorAll;              ///<彩色复印B4纸型计数器
    int32_t             _ptCopyStatB4ColorSingle;           ///<彩色单面复印B4纸型计数器
    int32_t             _ptCopyStatB4ColorDuplex;           ///<彩色双面复印B4纸型计数器
    int32_t             _ptCopyStatJISB5TotalAll;           ///<复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5TotalSingle;        ///<单面复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5TotalDuplex;        ///<双面复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5BlackAll;           ///<黑白复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5BlackSingle;        ///<黑白单面复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5BlackDuplex;        ///<黑白双面复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5ColorAll;           ///<彩色复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5ColorSingle;        ///<彩色单面复印JISB5纸型计数器
    int32_t             _ptCopyStatJISB5ColorDuplex;        ///<彩色双面复印JISB5纸型计数器
    int32_t             _ptCopyStatIOSB5TotalAll;           ///<复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5TotalSingle;        ///<单面复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5TotalDuplex;        ///<双面复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5BlackAll;           ///<黑白复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5BlackSingle;        ///<黑白单面复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5BlackDuplex;        ///<黑白双面复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5ColorAll;           ///<彩色复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5ColorSingle;        ///<彩色单面复印ISOB5纸型计数器
    int32_t             _ptCopyStatIOSB5ColorDuplex;        ///<彩色双面复印ISOB5纸型计数器
    int32_t             _ptCopyStat11x17TotalAll;           ///<复印11x17纸型计数器
    int32_t             _ptCopyStat11x17TotalSingle;        ///<单面复印11x17纸型计数器
    int32_t             _ptCopyStat11x17TotalDuplex;        ///<双面复印11x17纸型计数器
    int32_t             _ptCopyStat11x17BlackAll;           ///<黑白复印11x17纸型计数器
    int32_t             _ptCopyStat11x17BlackSingle;        ///<黑白单面复印11x17纸型计数器
    int32_t             _ptCopyStat11x17BlackDuplex;        ///<黑白双面复印11x17纸型计数器
    int32_t             _ptCopyStat11x17ColorAll;           ///<彩色复印11x17纸型计数器
    int32_t             _ptCopyStat11x17ColorSingle;        ///<彩色单面复印11x17纸型计数器
    int32_t             _ptCopyStat11x17ColorDuplex;        ///<彩色双面复印11x17纸型计数器
    int32_t             _ptCopyStatLetterTotalAll;          ///<复印Letter纸型计数器
    int32_t             _ptCopyStatLetterTotalSingle;       ///<单面复印Letter纸型计数器
    int32_t             _ptCopyStatLetterTotalDuplex;       ///<双面复印Letter纸型计数器
    int32_t             _ptCopyStatLetterBlackAll;          ///<黑白复印Letter纸型计数器
    int32_t             _ptCopyStatLetterBlackSingle;       ///<黑白单面复印Letter纸型计数器
    int32_t             _ptCopyStatLetterBlackDuplex;       ///<黑白双面复印Letter纸型计数器
    int32_t             _ptCopyStatLetterColorAll;          ///<彩色复印Letter纸型计数器
    int32_t             _ptCopyStatLetterColorSingle;       ///<彩色单面复印Letter纸型计数器
    int32_t             _ptCopyStatLetterColorDuplex;       ///<彩色双面复印Letter纸型计数器
    int32_t             _ptCopyStatLegalTotalAll;           ///<复印Legal纸型计数器
    int32_t             _ptCopyStatLegalTotalSingle;        ///<单面复印Legal纸型计数器
    int32_t             _ptCopyStatLegalTotalDuplex;        ///<双面复印Legal纸型计数器
    int32_t             _ptCopyStatLegalBlackAll;           ///<黑白复印Legal纸型计数器
    int32_t             _ptCopyStatLegalBlackSingle;        ///<黑白单面复印Legal纸型计数器
    int32_t             _ptCopyStatLegalBlackDuplex;        ///<黑白双面复印Legal纸型计数器
    int32_t             _ptCopyStatLegalColorAll;           ///<彩色复印Legal纸型计数器
    int32_t             _ptCopyStatLegalColorSingle;        ///<彩色单面复印Legal纸型计数器
    int32_t             _ptCopyStatLegalColorDuplex;        ///<彩色双面复印Legal纸型计数器
    int32_t             _ptCopyStatFilioTotalAll;           ///<复印Filio纸型计数器
    int32_t             _ptCopyStatFilioTotalSingle;        ///<单面复印Filio纸型计数器
    int32_t             _ptCopyStatFilioTotalDuplex;        ///<双面复印Filio纸型计数器
    int32_t             _ptCopyStatFilioBlackAll;           ///<黑白复印Filio纸型计数器
    int32_t             _ptCopyStatFilioBlackSingle;        ///<黑白单面复印Filio纸型计数器
    int32_t             _ptCopyStatFilioBlackDuplex;        ///<黑白双面复印Filio纸型计数器
    int32_t             _ptCopyStatFilioColorAll;           ///<彩色复印Filio纸型计数器
    int32_t             _ptCopyStatFilioColorSingle;        ///<彩色单面复印Filio纸型计数器
    int32_t             _ptCopyStatFilioColorDuplex;        ///<彩色双面复印Filio纸型计数器
    int32_t             _ptCopyStatOficioTotalAll;          ///<复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioTotalSingle;       ///<单面复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioTotalDuplex;       ///<双面复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioBlackAll;          ///<黑白复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioBlackSingle;       ///<黑白单面复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioBlackDuplex;       ///<黑白双面复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioColorAll;          ///<彩色复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioColorSingle;       ///<彩色单面复印Oficio纸型计数器
    int32_t             _ptCopyStatOficioColorDuplex;       ///<彩色双面复印Oficio纸型计数器
    int32_t             _ptCopyStatExecutiveTotalAll;       ///<复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveTotalSingle;    ///<单面复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveTotalDuplex;    ///<双面复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveBlackAll;       ///<黑白复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveBlackSingle;    ///<黑白单面复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveBlackDuplex;    ///<黑白双面复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveColorAll;       ///<彩色复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveColorSingle;    ///<彩色单面复印Executive纸型计数器
    int32_t             _ptCopyStatExecutiveColorDuplex;    ///<彩色双面复印Executive纸型计数器
    int32_t             _ptCopyStatStatementTotalAll;       ///<复印Statement纸型计数器
    int32_t             _ptCopyStatStatementTotalSingle;    ///<单面复印Statement纸型计数器
    int32_t             _ptCopyStatStatementTotalDuplex;    ///<双面复印Statement纸型计数器
    int32_t             _ptCopyStatStatementBlackAll;       ///<黑白复印Statement纸型计数器
    int32_t             _ptCopyStatStatementBlackSingle;    ///<黑白单面复印Statement纸型计数器
    int32_t             _ptCopyStatStatementBlackDuplex;    ///<黑白双面复印Statement纸型计数器
    int32_t             _ptCopyStatStatementColorAll;       ///<彩色复印Statement纸型计数器
    int32_t             _ptCopyStatStatementColorSingle;    ///<彩色单面复印Statement纸型计数器
    int32_t             _ptCopyStatStatementColorDuplex;    ///<彩色双面复印Statement纸型计数器
    int32_t             _ptCopyStat8KTotalAll;              ///<复印8K纸型计数器
    int32_t             _ptCopyStat8KTotalSingle;           ///<单面复印8K纸型计数器
    int32_t             _ptCopyStat8KTotalDuplex;           ///<双面复印8K纸型计数器
    int32_t             _ptCopyStat8KBlackAll;              ///<黑白复印8K纸型计数器
    int32_t             _ptCopyStat8KBlackSingle;           ///<黑白单面复印8K纸型计数器
    int32_t             _ptCopyStat8KBlackDuplex;           ///<黑白双面复印8K纸型计数器
    int32_t             _ptCopyStat8KColorAll;              ///<彩色复印8K纸型计数器
    int32_t             _ptCopyStat8KColorSingle;           ///<彩色单面复印8K纸型计数器
    int32_t             _ptCopyStat8KColorDuplex;           ///<彩色双面复印8K纸型计数器
    int32_t             _ptCopyStat16KTotalAll;             ///<复印16K纸型计数器
    int32_t             _ptCopyStat16KTotalSingle;          ///<单面复印16K纸型计数器
    int32_t             _ptCopyStat16KTotalDuplex;          ///<双面复印16K纸型计数器
    int32_t             _ptCopyStat16KBlackAll;             ///<黑白复印16K纸型计数器
    int32_t             _ptCopyStat16KBlackSingle;          ///<黑白单面复印16K纸型计数器
    int32_t             _ptCopyStat16KBlackDuplex;          ///<黑白双面复印16K纸型计数器
    int32_t             _ptCopyStat16KColorAll;             ///<彩色复印16K纸型计数器
    int32_t             _ptCopyStat16KColorSingle;          ///<彩色单面复印16K纸型计数器
    int32_t             _ptCopyStat16KColorDuplex;          ///<彩色双面复印16K纸型计数器
    int32_t             _ptCopyStatA6TotalAll;              ///<复印A6纸型计数器
    int32_t             _ptCopyStatA6TotalSingle;           ///<单面复印A6纸型计数器
    int32_t             _ptCopyStatA6TotalDuplex;           ///<双面复印A6纸型计数器
    int32_t             _ptCopyStatA6BlackAll;              ///<黑白复印A6纸型计数器
    int32_t             _ptCopyStatA6BlackSingle;           ///<黑白单面复印A6纸型计数器
    int32_t             _ptCopyStatA6BlackDuplex;           ///<黑白双面复印A6纸型计数器
    int32_t             _ptCopyStatA6ColorAll;              ///<彩色复印A6纸型计数器
    int32_t             _ptCopyStatA6ColorSingle;           ///<彩色单面复印A6纸型计数器
    int32_t             _ptCopyStatA6ColorDuplex;           ///<彩色双面复印A6纸型计数器
    int32_t             _ptCopyStatEnv10TotalAll;           ///<复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10TotalSingle;        ///<单面复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10TotalDuplex;        ///<双面复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10BlackAll;           ///<黑白复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10BlackSingle;        ///<黑白单面复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10BlackDuplex;        ///<黑白双面复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10ColorAll;           ///<彩色复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10ColorSingle;        ///<彩色单面复印Env10纸型计数器
    int32_t             _ptCopyStatEnv10ColorDuplex;        ///<彩色双面复印Env10纸型计数器
    int32_t             _ptCopyStatEnvMonTotalAll;          ///<复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonTotalSingle;       ///<单面复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonTotalDuplex;       ///<双面复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonBlackAll;          ///<黑白复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonBlackSingle;       ///<黑白单面复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonBlackDuplex;       ///<黑白双面复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonColorAll;          ///<彩色复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonColorSingle;       ///<彩色单面复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvMonColorDuplex;       ///<彩色双面复印EnvMon纸型计数器
    int32_t             _ptCopyStatEnvC6TotalAll;           ///<复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6TotalSingle;        ///<单面复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6TotalDuplex;        ///<双面复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6BlackAll;           ///<黑白复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6BlackSingle;        ///<黑白单面复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6BlackDuplex;        ///<黑白双面复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6ColorAll;           ///<彩色复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6ColorSingle;        ///<彩色单面复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC6ColorDuplex;        ///<彩色双面复印EnvC6纸型计数器
    int32_t             _ptCopyStatEnvC5TotalAll;           ///<复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5TotalSingle;        ///<单面复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5TotalDuplex;        ///<双面复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5BlackAll;           ///<黑白复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5BlackSingle;        ///<黑白单面复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5BlackDuplex;        ///<黑白双面复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5ColorAll;           ///<彩色复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5ColorSingle;        ///<彩色单面复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvC5ColorDuplex;        ///<彩色双面复印EnvC5纸型计数器
    int32_t             _ptCopyStatEnvDLTotalAll;           ///<复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLTotalSingle;        ///<单面复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLTotalDuplex;        ///<双面复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLBlackAll;           ///<黑白复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLBlackSingle;        ///<黑白单面复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLBlackDuplex;        ///<黑白双面复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLColorAll;           ///<彩色复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLColorSingle;        ///<彩色单面复印EnvDL纸型计数器
    int32_t             _ptCopyStatEnvDLColorDuplex;        ///<彩色双面复印EnvDL纸型计数器
    int32_t             _ptCopyStatPostcardTotalAll;        ///<复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardTotalSingle;     ///<单面复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardTotalDuplex;     ///<双面复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardBlackAll;        ///<黑白复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardBlackSingle;     ///<黑白单面复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardBlackDuplex;     ///<黑白双面复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardColorAll;        ///<彩色复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardColorSingle;     ///<彩色单面复印Postcard纸型计数器
    int32_t             _ptCopyStatPostcardColorDuplex;     ///<彩色双面复印Postcard纸型计数器

}
SNMP_MAP_S;

void* ipc_shm(int* shm_fd, const char* shm_name, size_t size);

void init_snmpipc(void);

#ifdef __cplusplus
}
#endif

#endif /* _SNMPIPC_H_ */

