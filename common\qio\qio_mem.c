/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_mem.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief QIO object wrapper for files formatted into memory
 */
#include "qiox.h"

typedef struct priv_info
{
    uint8_t*    content;        ///< pointer to "file" content in ram/rom
    uint32_t    contsize;       ///< how big an area is this?
    uint32_t    maxsize;        ///< can the area be reallocated?
    uint32_t    growsize;       ///< how much larger to make realloced buffer (goes up by power of 2)
    uint32_t    count;          ///< how many bytes are valid?
    uint32_t    offset;         ///< where are we?
    int32_t     writeable;      ///< can we write to it?
    int32_t     contalloc;      ///< did we allocate it?
}
PRIV_INFO_S;

typedef struct qio_memory_file
{
    struct list_head    file_list;
    char                name[64];
    uint8_t*            bytes;
    uint32_t            n_valid;
    uint32_t            n_alloc;
}
QIO_MEM_FILE_S;

static struct list_head s_file_head = PI_LIST_HEAD_INIT(s_file_head);
static pthread_mutex_t  s_mem_mutex = PTHREAD_MUTEX_INITIALIZER;

#define QIOMEM_UNLOCK() pthread_mutex_unlock(&s_mem_mutex)
#define QIOMEM_LOCK()   pthread_mutex_lock(&s_mem_mutex)

static int32_t match_path(const char* path1, const char* path2)
{
    const char* name1;
    const char* name2;

    name1 = strrchr(path1, SEP);
    name1 = ( (name1 != NULL) ? (name1 + 1) : path1 );

    name2 = strrchr(path2, SEP);
    name2 = ( (name2 != NULL) ? (name2 + 1) : path2 );

    QIO_DEBUG("compare(%s - %s)", name1, name2);
    return strcmp(name1, name2);
}

static uint8_t* get_bytes_from_name(const char* name, uint32_t* alloc, uint32_t* valid)
{
    QIO_MEM_FILE_S*     pf = NULL;
    struct list_head*   pos;
    struct list_head*   n;

    pi_list_for_each_safe(pos, n, &s_file_head)
    {
        pf = pi_list_entry(pos, QIO_MEM_FILE_S, file_list);
        if ( match_path(pf->name, name) == 0 )
        {
            QIO_DEBUG("get file(%s) valid(%u) alloc(%u) at %p", name, pf->n_valid, pf->n_alloc, pf->bytes);
            if ( alloc )
            {
                *alloc = pf->n_alloc;
            }
            if ( valid )
            {
                *valid = pf->n_valid;
            }
            return pf->bytes;
        }
    }

    return NULL;
}

static QIO_MEM_FILE_S* create_file_mem(const char* name, uint8_t* bytes, uint32_t n_room, uint32_t n_valid)
{
    QIO_MEM_FILE_S* pf;

    pf = (QIO_MEM_FILE_S *)pi_zalloc(sizeof(QIO_MEM_FILE_S));
    RETURN_VAL_IF(pf == NULL, QIO_WARN, NULL);

    snprintf(pf->name, sizeof(name), "%s", name);
    pf->n_valid = n_valid;
    pf->n_alloc = n_room;
    pf->bytes   = bytes;

    return pf;
}

static void release_file_mem(QIO_MEM_FILE_S* pf)
{
    pi_list_del_entry(&(pf->file_list));
    pi_free(pf);
}

static void update_file_data(PRIV_INFO_S* priv)
{
    QIO_MEM_FILE_S*     pf = NULL;
    struct list_head*   pos;
    struct list_head*   n;

    QIOMEM_LOCK();
    pi_list_for_each_safe(pos, n, &s_file_head)
    {
        pf = pi_list_entry(pos, QIO_MEM_FILE_S, file_list);
        if ( pf->bytes == priv->content )
        {
            break;
        }
    }

    if ( pf != NULL )
    {
        if ( priv->count != pf->n_valid )
        {
            QIO_DEBUG("new size for QIOMEM file %s is %d", pf->name, priv->count);
            pf->n_valid = priv->count;
        }
    }
    else
    {
        if ( priv->contalloc )
        {
            pi_free(priv->content);
        }
    }
    QIOMEM_UNLOCK();
}

static int32_t qio_mem_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    if ( what & QIO_POLL_READ )
    {
        RETURN_VAL_IF(priv->offset >= priv->count, QIO_DEBUG, 0);
    }

    if ( what & QIO_POLL_WRITE )
    {
        RETURN_VAL_IF(priv->offset >= priv->contsize, QIO_DEBUG, 0);
    }

    return 1;
}

static int32_t qio_mem_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);
    int32_t rem;

    RETURN_VAL_IF(priv == NULL || buffer == NULL, QIO_WARN, QIOEOF);

    rem = priv->count - priv->offset;
    RETURN_VAL_IF(rem <= 0, QIO_DEBUG, 0);

    if ( rem > nbuf )
    {
        rem = nbuf;
    }
    memcpy(buffer, priv->content + priv->offset, (size_t)rem);
    priv->offset += rem;

    return rem;
}

static int32_t qio_mem_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);
    int32_t rem;

    RETURN_VAL_IF(priv == NULL || buffer == NULL, QIO_WARN, QIOEOF);
    RETURN_VAL_IF(priv->writeable == 0, QIO_WARN, QIOEOF);

    rem = priv->contsize - priv->offset;
    if ( rem < nbuf && priv->contalloc && priv->maxsize > priv->contsize )
    {
        int32_t  nz;
        uint8_t* bb;

        nz = priv->contsize + nbuf;
        nz += priv->growsize;
        nz &= ~(priv->growsize - 1);
        if ( priv->growsize < 0x400000 )
        {
            priv->growsize <<= 1;
        }

        bb = (uint8_t*)pi_zalloc(nz);
        if ( bb == NULL )
        {
            QIO_WARN("No memory to grow qiomem buffer: %d<%s>", errno, strerror(errno));
        }
        else
        {
            if ( priv->count > 0 )
            {
                memcpy(bb, priv->content, priv->count);
            }
            pi_free(priv->content);
            priv->content  = bb;
            priv->contsize = nz;
            rem = (priv->contsize - priv->offset);
        }
    }

    RETURN_VAL_IF(rem <= 0, QIO_DEBUG, 0);

    if ( rem > nbuf )
    {
        rem = nbuf;
    }
    memcpy(priv->content + priv->offset, buffer, rem);
    priv->offset += rem;
    if ( priv->offset > priv->count )
    {
        priv->count = priv->offset;
    }

    return rem;
}

static int32_t qio_mem_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    DECL_PRIV(pqio, priv);
    ssize_t aoff;

    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    switch ( whence )
    {
        case SEEK_CUR:
            aoff = priv->offset + offset;
            break;
        case SEEK_END:
            if (offset > 0)
            {
            }
            aoff = priv->count + offset;
            break;
        case SEEK_SET:
        default:
            aoff = offset;
            break;
    }

    if ( aoff > priv->contsize )
    {
        QIO_DEBUG("Attempt to seek past current size");
        aoff = priv->contsize;
    }

    if ( aoff < 0 )
    {
        QIO_DEBUG("Attempt to seek before front");
        aoff = 0;
    }

    QIO_DEBUG("newoff=%d from %d+ at %d", aoff, offset, whence);
    priv->offset = (uint32_t)aoff;

    return (int32_t)aoff;
}

static int32_t qio_mem_close(QIO_S* pqio)
{
    PRIV_INFO_S* priv;

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( pqio->priv )
    {
        priv = (PRIV_INFO_S *)pqio->priv;
        update_file_data(priv);
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_mem_create(uint8_t* data, uint32_t n_room, uint32_t n_max, uint32_t n_valid, int32_t flags)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    if ( data == NULL )
    {
        priv->content = pi_zalloc(n_room);
        if ( priv->content == NULL )
        {
            QIO_WARN("alloc writeable mem data failed: %d<%s>", errno, strerror(errno));
            pi_free(priv);
            return NULL;
        }

        priv->count     = 0;
        priv->contsize  = n_room;
        priv->maxsize   = n_max;
        priv->growsize  = 8192;
        priv->contalloc = 1;
    }
    else
    {
        priv->count     = n_valid;
        priv->contsize  = n_room;
        if ( n_max > 0 && n_max != n_room )
        {
            QIO_WARN("Warning - ignoring max size, don't know how to realloc data");
        }
        priv->maxsize   = n_room;
        priv->content   = data;
        priv->growsize  = 0;
        priv->contalloc = 0;
    }
    priv->writeable = (flags | O_WRONLY);
    priv->offset = 0;
    QIO_WARN("open qio with count %d of %d tot at %p\n", priv->count, priv->contsize, priv->content);

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL)
    {
        QIO_WARN("alloc QIO_S failed: %d<%s>", errno, strerror(errno));
        if ( priv->contalloc )
        {
            pi_free(priv->content);
        }
        pi_free(priv);
        return NULL;
    }

    pqio->close = qio_mem_close;
    pqio->poll  = qio_mem_poll;
    pqio->read  = qio_mem_read;
    pqio->write = qio_mem_write;
    pqio->seek  = qio_mem_seek;
    pqio->priv  = priv;

    return pqio;
}

QIO_S* qio_mem_create_from_name(const char* name, int32_t flags)
{
    uint8_t*    bytes;
    uint32_t    valid;
    uint32_t    room;

    RETURN_VAL_IF(name == NULL, QIO_WARN, NULL);

    bytes = get_bytes_from_name(name, &room, &valid);
    if ( bytes == NULL )
    {
        QIO_DEBUG("file(%s) not found", name);
        return NULL;
    }

    return qio_mem_create(bytes, room, room, valid, flags);
}

int32_t qio_mem_set_file_contents(const char* name, uint8_t* bytes, uint32_t n_room, uint32_t n_valid)
{
    QIO_MEM_FILE_S*     pf = NULL;
    int32_t             ret = 0;
    struct list_head*   pos;
    struct list_head*   n;

    QIOMEM_LOCK();
    pi_list_for_each_safe(pos, n, &s_file_head)
    {
        pf = pi_list_entry(pos, QIO_MEM_FILE_S, file_list);
        if ( strcmp(pf->name, name) == 0 )
        {
            break;
        }
    }

    if ( pf != NULL )
    {
        pf->n_valid = n_valid;
        pf->n_alloc = n_room;
        pf->bytes   = bytes;
    }
    else
    {
        QIO_WARN("no such file(%s)", name);
        ret = -1;
    }
    QIOMEM_UNLOCK();

    return ret;
}

int32_t qio_mem_add_file(const char* name, uint8_t* bytes, uint32_t n_room, uint32_t n_valid)
{
    QIO_MEM_FILE_S* pf;

    QIOMEM_LOCK();
    pf = create_file_mem(name, bytes, n_room, n_valid);
    if ( pf != NULL )
    {
        QIO_DEBUG("added file %s of %d bytes", name, n_valid);
        pi_list_add_head(&(pf->file_list), &s_file_head);
    }
    QIOMEM_UNLOCK();

    return 0;
}

int32_t qio_mem_remove_file(const char* name)
{
    QIO_MEM_FILE_S*     pf = NULL;
    int32_t             ret = 0;
    struct list_head*   pos;
    struct list_head*   n;

    QIOMEM_LOCK();
    pi_list_for_each_safe(pos, n, &s_file_head)
    {
        pf = pi_list_entry(pos, QIO_MEM_FILE_S, file_list);
        if ( strcmp(pf->name, name) == 0 )
        {
            break;
        }
    }

    if ( pf != NULL )
    {
        release_file_mem(pf);
    }
    else
    {
        QIO_WARN("no such file(%s)", name);
        ret = -1;
    }
    QIOMEM_UNLOCK();

    return ret;
}
/**
 *@}
 */
