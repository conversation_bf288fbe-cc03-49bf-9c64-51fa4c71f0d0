/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file md5c.h
 * @addtogroup platform
 * @{
 * @addtogroup platform_aclregister
 * <AUTHOR>
 * @date 2022-02-16
 * @brief acl module  settings local acl parser will do some machine settings.\n
 *        for example: get mac address burn flash, and so on.
 */

/* POINTER defines a generic pointer type */  
typedef unsigned char * POINTER;  
      
/* UINT2 defines a two byte word */  
//typedef unsigned short int UINT2;   
      
/* UINT4 defines a four byte word */  
typedef unsigned long int Uint4;  
      
      

/**
 *@brief  MD5 context. 
 */
typedef struct {  
	Uint4 state[4];                 ///< /* state (ABCD) */  
	Uint4 count[2];        			///< /* number of bits, modulo 2^64 (lsb first) */  
	unsigned char buffer[64];       ///< /* input buffer */  
} MD5_CTX;  
      
void MD5Init (MD5_CTX *context); // (ipsinf.o):zutil.c: first defined here, 其它地方有定义，并且这些函数可以不开放，因此修改为static本地函数
void MD5Update (MD5_CTX *context, unsigned char *input, unsigned int inputLen);  
void MD5UpdaterString(MD5_CTX *context,const char *string);  
int MD5FileUpdateFile (MD5_CTX *context,char *filename);  
void MD5Final (unsigned char digest[16], MD5_CTX *context);  
void MDString (char *string,unsigned char digest[16]);  
int MD5File (char *filename,unsigned char digest[16]);  
void MD5mem(unsigned char*pdata,unsigned int length,unsigned char digest[16]);





/**
 *@}
 */