#ifndef _NETPORT_H_
#define _NETPORT_H_

#define SMTP_PORT           ((uint16_t)25)

#define HTTP_PORT           ((uint16_t)80)

#define NETBIOS_PORT        ((uint16_t)137)

#define SNMP_PORT           ((uint16_t)161)

#define SLP_PORT            ((uint16_t)427)

#define TLS_PORT            ((uint16_t)443)

#define LPD_PORT            ((uint16_t)515)

#define IPP_PORT            ((uint16_t)631)

#define BONJOUR_PORT        ((uint16_t)5353)

#define LLMNR_PORT          ((uint16_t)5355)

#define RAWPRINT_PORT       ((uint16_t)9100)

#define RAWSCAN_PORT        ((uint16_t)9200)

#define WSD_DISCO_PORT      ((uint16_t)3702)

#define WSD_CONN_PORT       ((uint16_t)5357)
                                           
#if CONFIG_PRINT
#define WSD_PRN_PORT        ((uint16_t)43001)
#else
#define WSD_PRN_PORT        ((uint16_t)0)
#endif
                                             
#if CONFIG_SCAN
#define WSD_SCN_PORT        ((uint16_t)43002)
#else
#define WSD_SCN_PORT        ((uint16_t)0)
#endif
                                           
#ifndef CONFIG_SCAN_ADF
#define CONFIG_SCAN_ADF     1
#endif

#ifndef CONFIG_SCAN
#define CONFIG_SCAN         0
#endif

#ifndef CONFIG_FAX
#define CONFIG_FAX          0
#endif

#endif /* _NETPORT_H_ */

