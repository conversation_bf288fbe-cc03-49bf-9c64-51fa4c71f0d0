/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_time.h
 * @addtogroup time
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal time interface set
 */

#ifndef __POL_TIME_H__
#define __POL_TIME_H__

#include <time.h>
#include <unistd.h>
#include <stdint.h>
#include <pol/pol_define.h>

PT_BEGIN_DECLS

#if defined(CONFIG_POL_TIME_DEBUG)

#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

/**
 * @brief Converts a time value to a string representation of the date and time.
 * @param[in] timep Pointer to a `time_t` object representing the time value.
 * @param[in] buf Pointer to a character array where the resulting string will be stored.
 * @return Returns a pointer to the resulting string.
 */
char* pi_ctime_r(const time_t *timep, char *buf);

/**
 * @brief Converts a time value to a string representation of the date and time.
 * @param[in] timep Pointer to a `struct tm` object representing the time value.
 * @param[in] buf Pointer to a character array where the resulting string will be stored.
 * @return Returns a pointer to the resulting string.
 */
char * pi_asctime_r(const struct tm *tm, char *buf);

/**
 * @brief Converts a time value to Coordinated Universal Time (UTC) time broken down into a `struct tm`.
 * @param[in] timep Pointer to a `time_t` object representing the time value.
 * @param[in] result Pointer to a `struct tm` object where the broken-down time will be stored.
 * @return Returns Returns a pointer to the `struct tm` object.
 */
struct tm* pi_gmtime_r(const time_t *timep, struct tm *result);

/**
 * @brief Converts a time value to local time broken down into a `struct tm`.
 * @param[in] timep  Pointer to a `time_t` object representing the time value..
 * @param[in] result Pointer to a `struct tm` object where the broken-down time will be stored.
 * @return Returns Returns a pointer to the `struct tm` object.
 */
struct tm* pi_localtime_r(const time_t *timep, struct tm *result);

/**
 * @brief Formats a time value according to a specified format string.
 * @param[in] s Pointer to a character array where the formatted string will be stored..
 * @param[in] max Maximum number of characters to write to the output buffer.
 * @param[in] format Pointer to a format string that specifies the desired output format.
 * @param[in] tm  Pointer to a `struct tm` object representing the time value.
 * @return Pointer to a format string that specifies the desired output format.
 */
size_t pi_strftime(char *s, size_t max, const char *format, const struct tm *tm);

/**
 * @brief Returns the current calendar time as a `time_t` object.
 * @param[in] tloc Optional pointer to a `time_t` object where the calendar time will be stored.
 * @return Returns the current calendar time.
 */
time_t pi_time(time_t *tloc);

/**
 * @brief Converts a `struct tm` object representing a local time to a `time_t` object.
 * @param[in] tm Pointer to a `struct tm` object representing the local time.
 * @return Returns a `time_t` object representing the calendar time.
 */
time_t pi_mktime(struct tm *tm);

/**
 * @brief Returns the processor time used by the program.
 * @return Returns the processor time as a `clock_t` value.
 */
clock_t pi_clock(void);

/**
 * @brief Retrieves the current value of a specified clock.
 * @param[in] clk_id Identifier of the clock to retrieve the value from.
 * @param[in] tp Pointer to a `struct timespec` object where the clock value will be stored.
 * @return Returns 0 on success, or -1 on failure.
 */
int pi_clock_gettime(clockid_t clk_id, struct timespec *tp);

/**
 * @brief Sets the value of a specified clock.
 * @param[in] clk_id  Identifier of the clock to set the value for.
 * @param[in] tp Pointer to a `struct timespec` object containing the new clock value.
 * @return Returns 0 on success, or -1 on failure.
 */
int pi_clock_settime(clockid_t clk_id, const struct timespec *tp);

/**
 * @brief Suspends the execution of the current thread for a specified number of milliseconds.
 * @param[in] msec Number of milliseconds to sleep.
 * @return Returns 0 on success, or -1 on failure.
 */
int pi_msleep(uint32_t msec);

/**
 * @brief Suspends the execution of the current thread for a specified number of microseconds.
 * @param[in] usec Number of microseconds to sleep.
 * @return Returns 0 on success, or -1 on failure.
 */
int pi_usleep(uint32_t usec);

/**
 * @brief Suspends the execution of the current thread for a specified time period.
 * @param[in] req Pointer to a `struct timespec` object specifying the desired sleep duration.
 * @param[in] rem Pointer to a `struct timespec` object where the remaining sleep time will be stored if the sleep is interrupted.
 * @return Returns 0 on success, or -1 on failure.
 */
int pi_nanosleep(const struct timespec *req, struct timespec *rem);

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

char* _pi_ctime_r(const char *callfile ,const uint32_t callline, const time_t *timep, char *buf);
#define pi_ctime_r(timep, buf)          _pi_ctime_r(__FILE__,__LINE__,timep,buf)

char * _pi_asctime_r(const char *callfile ,const uint32_t callline, const struct tm *tm, char *buf);
#define pi_asctime_r(tm, buf)          _pi_asctime_r(__FILE__,__LINE__,tm,buf)

struct tm* _pi_gmtime_r(const char *callfile ,const uint32_t callline, const time_t *timep, struct tm *result);
#define pi_gmtime_r(timep, result)          _pi_gmtime_r(__FILE__,__LINE__,timep,result)

struct tm* _pi_localtime_r(const char *callfile ,const uint32_t callline, const time_t *timep, struct tm *result);
#define pi_localtime_r(timep, result)          _pi_localtime_r(__FILE__,__LINE__,timep,result)

size_t _pi_strftime(const char *callfile ,const uint32_t callline, char *s, size_t max, const char *format, const struct tm *tm);
#define pi_strftime(s,max,format,tm)          _pi_strftime(__FILE__,__LINE__,s,max,format,tm)

time_t _pi_time(const char *callfile ,const uint32_t callline, time_t *tloc);
#define pi_time(tloc)          _pi_time(__FILE__,__LINE__,tloc)

time_t _pi_mktime(const char *callfile ,const uint32_t callline, struct tm *tm);
#define pi_mktime(tm)          _pi_mktime(__FILE__,__LINE__,tm)

clock_t _pi_clock(const char *callfile ,const uint32_t callline);
#define pi_clock()          _pi_clock(__FILE__,__LINE__)

int _pi_clock_gettime(const char *callfile ,const uint32_t callline, clockid_t clk_id, struct timespec *tp);
#define pi_clock_gettime(clk_id,tp)          _pi_clock_gettime(__FILE__,__LINE__,clk_id,tp)

int _pi_clock_settime(const char *callfile ,const uint32_t callline, clockid_t clk_id, const struct timespec *tp);
#define pi_clock_settime(clk_id,tp)          _pi_clock_settime(__FILE__,__LINE__,clk_id,tp)

int _pi_msleep(const char *callfile ,const uint32_t callline, uint16_t msec);
#define pi_msleep(msec)          _pi_msleep(__FILE__,__LINE__,msec)

int _pi_usleep(const char *callfile ,const uint32_t callline, useconds_t usec);
#define pi_usleep(usec)          _pi_usleep(__FILE__,__LINE__,usec)

int _pi_nanosleep(const char *callfile ,const uint32_t callline, const struct timespec *req, struct timespec *rem);
#define pi_nanosleep(req, rem)	 _pi_nanosleep(__FILE__, __LINE__, req, rem)

#endif /* defined(CONFIG_POL_DEBUG_MODE_BACKTRACE) || defined(CONFIG_POL_DEBUG_MODE_CALLER) */

#else /* defined(CONFIG_POL_TIME_DEBUG) */

#define pi_ctime_r 				ctime_r
#define pi_asctime_r 			asctime_r
#define pi_gmtime_r 			gmtime_r
#define pi_localtime_r 			localtime_r
#define pi_strftime				strftime
#define pi_time	 				time
#define pi_mktime	 			mktime
#define pi_clock 				clock
#define pi_clock_gettime 		clock_gettime
#define pi_clock_settime 		clock_settime
#define pi_nanosleep	 	    nanosleep


/**
 * @brief Suspends the execution of the current thread for a specified number of milliseconds.
 * @param[in] msec Number of milliseconds to sleep.
 * @return Returns 0 on success, or -1 on failure.
 */
int32_t pi_msleep(uint32_t msec);

/**
 * @brief Suspends the execution of the current thread for a specified number of microseconds.
 * @param[in] usec Number of microseconds to sleep.
 * @return Returns 0 on success, or -1 on failure.
 */
int32_t pi_usleep(uint32_t usec);
#endif /* defined(CONFIG_POL_TIME_DEBUG) */

#define pi_sleep	 			sleep

PT_END_DECLS

#endif /* __POL_TIME_H__ */

/**
 *@}
 */
