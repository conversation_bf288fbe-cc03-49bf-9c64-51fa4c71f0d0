﻿/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netmisc.c
 * @addtogroup net
 * @{
 * @addtogroup netmisc
 * <AUTHOR>
 * @date 2023-4-20
 * @brief network custom API
 */
#include <openssl/rc4.h>
#include <ifaddrs.h>
#include <iconv.h> /* 编码转换 */
#include <regex.h> /* 正则处理 */

#include "nettypes.h"
#include "netmisc.h"
#include "netsock.h"

static const uint8_t s_rc4_key[256] = {"MIIBKzCB0gIBADBwMQswCQYDVQQGEwJDTjESMBAGA1UECAwJR3Vhbmdkb25nMRMwEQYDVQQKDApQYW50dW0gSW5jMRcwFQYDVQQLDA53d3cucGFudHVtLmNvbTEfMB0GA1UEAwwWUGFudHVtIFRlY2hub2xvZ3kgQ2VydDBZMBMGByqGSM49AgEGCCqBHM9VAQjCBenux5b4cVypH19gWVUcPAV8CyqourAYEQIhALXV3gnZS5bnq1uhMcBUJO3u"};

/* 检查目标字符串编码格式 */
CODING_FORMAT_E get_coding_format(const char* str)
{
    CODING_FORMAT_E format = CODING_FORMAT_ASCII;
    const char* ptr = str;
    uint8_t chr;
    uint8_t bytes;
    uint8_t i;

    while ( *ptr )
    {
        chr = (uint8_t)(*ptr++);
        if ( (chr & 0x80) != 0 )
        {
            /* 多字节编码字符，首字节高位1的个数表示该字符所占字节数，这里bytes表示首字节以外的总字节数(假设当前是UTF-8编码) */
            if      ( (chr & 0xF0) == 0xE0 )  bytes = 2;  /* 1110 XXXX 高频情况，优先判定 */
            else if ( (chr & 0xE0) == 0xC0 )  bytes = 1;  /* 110X XXXX */
            else if ( (chr & 0xF8) == 0xF0 )  bytes = 3;  /* 1111 0XXX */
            else    return CODING_FORMAT_GBK;

            for ( i = 0; i < bytes; ++i )
            {
                chr = (uint8_t)(*ptr++);
                if ( (chr & 0xC0) != 0x80 )               /* 10XX XXXX */
                {
                    return CODING_FORMAT_GBK;
                }
            }

            if ( format == CODING_FORMAT_ASCII )
            {
                format = CODING_FORMAT_UTF8;
            }
        }
        else
        {
            /* do nothing, ASCII code, 0x00 ~ 0x0F */
        }
    }

    return format;
}

/* 转换字符串编码 */
int32_t convert_coding_format(const char* from_charset, const char* to_charset, char* src, size_t src_len, char* dst, size_t dst_size)
{
    iconv_t     cd;
    char*       inbuf  = src;
    char*       outbuf = dst;
    size_t      outlen = dst_size;
    size_t      inlen  = src_len;
    size_t      rc;
    int32_t     rs;

    cd = iconv_open(to_charset, from_charset);
    if ( cd != (iconv_t)(-1) )
    {
        NET_DEBUG("iconv start: inbuf(%p), inlen(%lu), outbuf(%p), outlen(%lu)", inbuf, (unsigned long)inlen, outbuf, (unsigned long)outlen);
        memset(outbuf, 0, outlen);
        rc = iconv(cd, &inbuf, &inlen, &outbuf, &outlen);
        NET_DEBUG("iconv end: rc(%d), inbuf(%p), inlen(%lu), outbuf(%p), outlen(%lu)", (int32_t)rc, inbuf, (unsigned long)inlen, outbuf, (unsigned long)outlen);
        iconv_close(cd);
        if ( rc != (size_t)(-1) )
        {
            rs = 0;
        }
        else
        {
            NET_WARN("iconv failed: %d<%s>", errno, strerror(errno));
            rs = -1;
        }
    }
    else
    {
        NET_WARN("iconv open failed: %d<%s>", errno, strerror(errno));
        rs = -1;
    }
    NET_DEBUG("convert %s(%s) to %s(%s) %d", from_charset, src, to_charset, dst, rs);

    return rs;
}

/* 处理SSID编码格式，将GBK编码的SSID转换为UTF-8编码 */
int32_t convert_str_to_utf8(char* src, char* dst, size_t dst_size)
{
    CODING_FORMAT_E format;
    int32_t         ret;

    format = get_coding_format(src);
    switch ( format )
    {
    case CODING_FORMAT_ASCII:
    case CODING_FORMAT_UTF8:
        snprintf(dst, dst_size, "%s", src);
        ret = 0;
        break;
    case CODING_FORMAT_GBK:
        ret = convert_coding_format("gbk", "utf-8", src, strlen(src), dst, dst_size);
        break;
    default:
        NET_INFO("unknown code format(%d)", format);
        ret = -1;
        break;
    }

    return ret;
}

size_t convert_hexbytes_to_string(const uint8_t* src, size_t src_len, char* dst, size_t dst_size)
{
    size_t num = MIN(src_len, dst_size/2);
    size_t i;

    for ( i = 0; i < num; ++i )
    {
        snprintf(dst + (i*2), dst_size - (i*2), "%02X", src[i]);
    }

    return (num*2);
}

size_t convert_string_to_hexbytes(const char* src, uint8_t* dst, size_t dst_size)
{
    size_t num = MIN(strlen(src)/2, dst_size);
    size_t tmp = 0;
    size_t i;

    for ( i = 0; i < num; ++i )
    {
        sscanf(src + (i*2), "%02x", &tmp);
        dst[i] = (uint8_t)tmp;
    }

    return num;
}

int32_t check_string_format(const char* str, const char* pattern)
{
    regmatch_t  pmatch[1];
    regex_t     reg;
    int32_t     ret;

    regcomp(&reg, pattern, REG_EXTENDED|REG_NOSUB|REG_ICASE);
    ret = regexec(&reg, str, 1, pmatch, 0);
    NET_DEBUG("check(%s) ret(%d)", str, ret);
    regfree(&reg);

    return ret;
}

int32_t get_pid_from_file(const char* pidfile)
{
    int32_t pid = 0;
    char    buf[16];
    FILE*   stream;

    stream = fopen(pidfile, "r");
    if ( stream != NULL )
    {
        fgets(buf, sizeof(buf), stream);
        pid = atoi(buf);
        if ( pid <= 0 )
        {
            NET_DEBUG("get pid(%d) from pidfile(%s)", pid, pidfile);
            pid = 0;
        }
        fclose(stream);
    }

    return pid;
}

int32_t check_program_exist(const char* pidfile)
{
    int32_t pid;

    RETURN_VAL_IF(STRING_IS_EMPTY(pidfile), NET_WARN, 0);

    pid = get_pid_from_file(pidfile);
    if ( pid > 0 && kill(pid, 0) != 0 )
    {
        NET_DEBUG("program(%s:%d) no exist", pidfile, pid);
        pid = 0;
    }

    return pid;
}

int32_t waiting_program_start_ent(const char* pidfile, uint32_t max_retries, uint32_t interval, const char* caller)
{
    uint32_t    retries = 0; /* 重试次数 */

    do
    {
        NET_DEBUG("[%s] starting program(%s), retries(%u)", caller, pidfile, retries);
        pi_msleep(interval);
    }
    while ( check_program_exist(pidfile) <= 0 && retries++ < max_retries );
    RETURN_VAL_SHOW_CALLER_IF(retries > max_retries, NET_WARN, caller, -1);

    NET_DEBUG("[%s] start program(%s) successfully", caller, pidfile);
    return 0;
}

int32_t waiting_program_stop_ent(const char* pidfile, uint32_t max_retries, uint32_t interval, const char* caller)
{
    uint32_t    retries = 0; /* 重试次数 */
    int32_t     pid;

    if ( (pid = check_program_exist(pidfile)) > 0 )
    {
        do
        {
            NET_DEBUG("[%s] stopping program(%s:%d), retries(%u)", caller, pidfile, pid, retries);
            kill(pid, SIGKILL);
            pi_msleep(interval);
        }
        while ( kill(pid, 0) == 0 && retries++ < max_retries );
        RETURN_VAL_SHOW_CALLER_IF(retries > max_retries, NET_WARN, caller, -1);

        NET_DEBUG("[%s] stop program(%s:%d) successfully", caller, pidfile, pid);
    }
    remove(pidfile);

    return 0;
}

void encrypt_data_rc4(uint8_t* in_buf, uint8_t* out_buf, size_t len)
{
    RC4_KEY key;

    RC4_set_key(&key, sizeof(s_rc4_key), s_rc4_key);
    RC4(&key, len, in_buf, out_buf);
}

int32_t compare_file(const char* src_file1, const char* src_file2, size_t size)
{
    struct stat file_info1;
    struct stat file_info2;
    FILE*       stream1 = NULL;
    FILE*       stream2 = NULL;
    char*       buf1 = NULL;
    char*       buf2 = NULL;
    int32_t     rs = 1;
    int32_t     nread1;
    int32_t     nread2;

    RETURN_VAL_IF(src_file1 == NULL || src_file2 == NULL, NET_WARN, -1);

    stat(src_file1, &file_info1);
    stat(src_file2, &file_info2);
    do
    {
        NET_DEBUG("file1(%s) size(%ld) file2(%s) size(%ld)", src_file1, file_info1.st_size, src_file2, file_info2.st_size);
        if ( file_info1.st_size != file_info2.st_size )
        {
            rs = ( file_info1.st_size > file_info2.st_size ? 1 : -1 );
            break;
        }

        if ( size == 0 || size > file_info1.st_size )
        {
            NET_DEBUG("reset size to %ld", file_info1.st_size);
            size = file_info1.st_size;
        }
        NET_DEBUG("compare size is %u", size);

        buf1 = pi_malloc(size);
        BREAK_IF(buf1 == NULL, NET_WARN);

        buf2 = pi_malloc(size);
        BREAK_IF(buf2 == NULL, NET_WARN);

        stream1 = fopen(src_file1, "r");
        BREAK_IF(stream1 == NULL, NET_WARN);

        stream2 = fopen(src_file2, "r");
        BREAK_IF(stream2 == NULL, NET_WARN);

        nread1 = fread(buf1, 1, size, stream1);
        BREAK_IF(nread1 != size, NET_WARN);

        nread2 = fread(buf2, 1, size, stream2);
        BREAK_IF(nread2 != size, NET_WARN);

        rs = memcmp(buf1, buf2, size);
    }
    while ( 0 );

    if ( stream1 != NULL )
    {
        fclose(stream1);
    }
    if ( stream2 != NULL )
    {
        fclose(stream2);
    }
    if ( buf1 != NULL )
    {
        pi_free(buf1);
    }
    if ( buf2 != NULL )
    {
        pi_free(buf2);
    }
    NET_DEBUG("compare result(%d)", rs);
    return rs;
}

#define FILE_CHUNK_SIZE     ( 0x10000 ) /* 64 * 1024 */
int32_t copy_file(const char* dst_file, const char* src_file, const char* mode)
{
    FILE*   dst_stream = NULL;
    FILE*   src_stream = NULL;
    char*   buf = NULL;
    size_t  wlen = 0;
    size_t  rlen = 0;
    int32_t rs = -1;

    NET_DEBUG("copy file (%s) to (%s) mode (%s)", src_file, dst_file, mode);
    do
    {
        dst_stream = fopen(dst_file, mode);
        BREAK_IF(dst_stream == NULL, NET_WARN);

        src_stream = fopen(src_file, "r");
        BREAK_IF(src_stream == NULL, NET_WARN);

        buf = (char *)pi_zalloc(FILE_CHUNK_SIZE);
        BREAK_IF(buf == NULL, NET_WARN);

        do
        {
            rlen = fread(buf, 1, FILE_CHUNK_SIZE, src_stream);
            BREAK_IF(rlen == 0, NET_DEBUG);

            wlen = fwrite(buf, 1, rlen, dst_stream);
            BREAK_IF(rlen != wlen, NET_WARN);
        }
        while ( FILE_CHUNK_SIZE == rlen );

        rs = ( (wlen == rlen) ? 0 : -1 );
    }
    while ( 0 );

    if ( dst_stream != NULL )
    {
        fclose(dst_stream);
    }
    if ( src_stream != NULL )
    {
        fclose(src_stream);
    }
    if ( buf != NULL )
    {
        pi_free(buf);
    }
    return rs;
}

void save_file(const char* file, const char* buf, size_t len)
{
    FILE* stream;

    RETURN_IF(file == NULL || buf == NULL || len == 0, NET_WARN);

    stream = fopen(file, "w");
    if ( stream == NULL )
    {
        NET_WARN("fopen(%s) failed: %d<%s>", file, errno, strerror(errno));
    }
    else
    {
        fwrite(buf, 1, len, stream);
        fclose(stream);
    }
}

size_t read_file(const char* file, char* buf, size_t size)
{
    FILE*   stream;
    size_t  len;

    RETURN_VAL_IF(file == NULL || buf == NULL || size == 0, NET_WARN, 0);
    RETURN_VAL_IF((stream = fopen(file, "r")) == NULL, NET_WARN, 0);

    len = fread(buf, 1, size, stream);
    fclose(stream);

    return len;
}

int32_t parse_uri_host(const char* pstr_start, const char* pstr_end, uint16_t* port_val, char* host_val, size_t val_size)
{
    const char* host_start = pstr_start;
    const char* host_end;
    size_t      host_len;
    long int    port;

    RETURN_VAL_IF(host_start == NULL || host_val == NULL || val_size == 0, NET_WARN, -1);

    if ( pstr_end == NULL )
    {
        pstr_end = pstr_start + strlen(pstr_start);
    }

    host_end = strchr(host_start, ']');
    if ( *host_start == '[' && host_end != NULL && host_end < pstr_end ) /* 当Host为IPv6地址时格式为：[xxxx:xxxx:xxxx::xxxx]:Port */
    {
        host_end = strchr(host_end + 1, ':');
    }
    else
    {
        host_end = strchr(host_start, ':');
    }

    if ( host_end != NULL && host_end < pstr_end )
    {
        port = strtol(host_end + 1, NULL, 10);
        if ( port <= 0 || port >= 0x10000 )
        {
            NET_WARN("invalid port(%ld) in host(%s), use default.", port, host_start);
            port = 80;
        }
    }
    else
    {
        host_end = pstr_end;
        port = 80;
    }

    host_len = (size_t)(host_end - host_start + 1);
    snprintf(host_val, (val_size < host_len) ? val_size : host_len, "%s", host_start);
    if ( port_val != NULL )
    {
        *port_val = port;
    }

    return 0;
}

int32_t parse_uri(const char* uri, URI_ELEMS_S* uri_elems)
{
    const char* proto_ptr = uri;
    const char* host_ptr;
    const char* path_ptr;
    const char* parm_ptr;
    size_t      len;

    RETURN_VAL_IF(STRING_IS_EMPTY(proto_ptr) || uri_elems == NULL, NET_WARN, -1);

    host_ptr = strstr(proto_ptr, "://"); /* 提取URI中的Protocol( eg. "http", "https", "ipp" ) */
    if ( host_ptr != NULL )
    {
        len = (size_t)((sizeof(uri_elems->proto) < (host_ptr - proto_ptr + 1)) ? sizeof(uri_elems->proto) : (host_ptr - proto_ptr + 1));
        snprintf(uri_elems->proto, len, "%s", proto_ptr);
        host_ptr += strlen("://");
    }
    else
    {
        snprintf(uri_elems->proto, sizeof(uri_elems->proto), "http");
        host_ptr = uri;
    }

    RETURN_VAL_IF(STRING_IS_EMPTY(host_ptr), NET_TRACE, 0);

    path_ptr = strchr(host_ptr, '/'); /* 获取下一个'/'，将剩余URI拆分成 (Host:Port) / (Path?Parm) 两部分 */
    if ( path_ptr == NULL )
    {
        path_ptr = host_ptr + strlen(host_ptr);
    }

    /* 拆分Host和Port */
    RETURN_VAL_IF(parse_uri_host(host_ptr, path_ptr, &(uri_elems->port), uri_elems->host, sizeof(uri_elems->host)) < 0, NET_WARN, -1);

    RETURN_VAL_IF(STRING_IS_EMPTY(path_ptr), NET_TRACE, 0);

    /* 拆分Path和Parm */
    parm_ptr = strchr(path_ptr, '?');
    if ( parm_ptr != NULL )
    {
        snprintf(uri_elems->parm, sizeof(uri_elems->parm), "%s", parm_ptr);
        len = (size_t)((sizeof(uri_elems->path) < (parm_ptr - path_ptr + 1)) ? sizeof(uri_elems->path) : (parm_ptr - path_ptr + 1));
        snprintf(uri_elems->path, len, "%s", path_ptr);
    }
    else
    {
        snprintf(uri_elems->path, sizeof(uri_elems->path), "%s", path_ptr);
        uri_elems->parm[0] = '\0';
    }

    return 0;
}

int32_t change_card_priority(NET_CTX_S* net_ctx, int32_t try_times)
{
    char ipv4_eth0_gtwy[IPV4_ADDR_LEN] = {0};
    char ipv4_wlan_gtwy[IPV4_ADDR_LEN] = {0};

    const char* ifname_eth0 = IFACE_NAME(IFACE_ID_ETH);
    netdata_get_ipv4_gtwy(net_ctx->data_mgr, IFACE_ID_ETH, ipv4_eth0_gtwy, sizeof(ipv4_eth0_gtwy));
    #if CONFIG_NET_WIFI
        const char* ifname_wlan = IFACE_NAME(IFACE_ID_STA);
        netdata_get_ipv4_gtwy(net_ctx->data_mgr, IFACE_ID_STA, ipv4_wlan_gtwy, sizeof(ipv4_wlan_gtwy));
        NET_DEBUG("ipv4_eth0_gtwy %s ipv4_wlan_gtwy %s", ipv4_eth0_gtwy, ipv4_wlan_gtwy);
    #else
        NET_DEBUG("ipv4_eth0_gtwy %s", ipv4_eth0_gtwy);
    #endif

    if ( try_times == 0 )
    {
        pi_runcmd(NULL, 0, 0, "ip route del via %s dev %s", ipv4_eth0_gtwy, ifname_eth0);
        pi_runcmd(NULL, 0, 0, "ip route add via %s dev %s metric 2", ipv4_eth0_gtwy, ifname_eth0);
        #if CONFIG_NET_WIFI
        pi_runcmd(NULL, 0, 0, "ip route del via %s dev %s", ipv4_wlan_gtwy, ifname_wlan);
        pi_runcmd(NULL, 0, 0, "ip route add via %s dev %s metric 1", ipv4_wlan_gtwy, ifname_wlan);
        #endif
    }
    else if ( try_times == 1 )
    {
        pi_runcmd(NULL, 0, 0, "ip route del via %s dev %s", ipv4_eth0_gtwy, ifname_eth0);
        pi_runcmd(NULL, 0, 0, "ip route add via %s dev %s metric 1", ipv4_eth0_gtwy, ifname_eth0);
        #if CONFIG_NET_WIFI
        pi_runcmd(NULL, 0, 0, "ip route del via %s dev %s", ipv4_wlan_gtwy, ifname_wlan);
        pi_runcmd(NULL, 0, 0, "ip route add via %s dev %s metric 2", ipv4_wlan_gtwy, ifname_wlan);
        #endif
    }
    try_times++;
    return try_times;
}

/*[1,2,3,5,7,9] to "1-2-3-5-7-9"*/
int32_t array_to_string(int32_t* arr, int32_t arr_count, char* str, size_t str_size)
{
    char*   result;
    char*   ptr;
    int32_t total_length = 0;
    int32_t num = 0;
    int32_t ret = 0;
    int32_t i;

    RETURN_VAL_IF(arr == NULL || arr_count == 0, NET_WARN, ret);

    /* 估算所需的字符串长度 */
    for ( i = 0; i < arr_count; ++i )
    {
        if ( i < arr_count - 1 && arr[i] != 0 )
        {
            total_length += snprintf(NULL, 0, "%d", arr[i]);
            total_length += 1;
            num++;
        }
        else
        {
            break;
        }
    }
    NET_DEBUG("total_length %d", total_length);

    if ( str_size > total_length )
    {
        /* 将数组元素拼接到字符串中 */
        ptr = str;
        for ( i = 0; i < num; ++i )
        {
           ptr += sprintf(ptr, "%d", arr[i]);
           if ( i < num - 1 )
           {
               *ptr = '-';
               ptr += 1;
           }
        }
        *ptr = '\0';
        ret = total_length;
    }

    return ret;
}

/**
 *@}
 */
