/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file manager.c
 * @addtogroup manager
 * @{
 * @addtogroup manager
 * @autor 
 * @date 2024-06-11
 * @brief manager module
 */
#include "manager/manager.h"
#include "runtime/runtime.h"
#include "basic/sys/sys.h"
#include "runtime/utils/uthash_utils.h"
#include "trans/transmission.h"
#include <string.h>
#include "runtime/utils/msgq_utils.h"
#include "runtime/utils/tool_utils.h"
#include "runtime/utils/uthash_utils.h"
#include "runtime/utils/quickjs_utils.h"
#include "basic/config.h"
#include "cJSON.h"
#include "runtime/utils/whitelist.h"

#define MANAGER "manager"

static int64_t get_curr_time()
{
    time_t current_time = time(NULL);

    return (int64_t)current_time;
}

 static int32_t get_proj_config_json(PeSFRunTime *prt)
 {
    FILE *file = NULL;
    file = fopen(prt->app_path.proj_config_json, "r");
    if (file == NULL) {
        LOG_E(MANAGER,"Error opening file\n");
        prt->proj_config.valid_time = 0x7FFFFFFF;
        prt->proj_config.whitelist = NULL;
        return 0;
    }

    // 读取文件内容
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    char *json_string = (char *)malloc(file_size + 1);
    fread(json_string, 1, file_size, file);
    json_string[file_size] = '\0';

    // 解析JSON字符串
    cJSON *root = cJSON_Parse(json_string);
    if (root == NULL) {
        LOG_E(MANAGER,"Error parsing JSON\n");
        prt->proj_config.valid_time = 0x7FFFFFFF;
        prt->proj_config.whitelist = NULL;
        //释放资源
        free(json_string);
        cJSON_Delete(root);
        fclose(file);
        file = NULL;
        return 0;
    }

    // 获取有效使用时间
    cJSON *valid_time_obj = cJSON_GetObjectItem(root, "valid_time");
    if((valid_time_obj != NULL) && (valid_time_obj->type == cJSON_Number)){
        prt->proj_config.valid_time = valid_time_obj->valueint;
    }else{
		LOG_I("proj_config_json"," app valid forever");
        prt->proj_config.valid_time = 0x7FFFFFFF;
       
    }
    prt->proj_config.whitelist = NULL;
    cJSON *whitelist_obj = cJSON_GetObjectItem(root, "whitelist");
	if(NULL != whitelist_obj){
	    cJSON *curr = whitelist_obj->child;
	    while(curr != NULL){
	        add_whitelist(&(prt->proj_config.whitelist), curr->string, curr->valueint);
	        curr = curr->next;
	    }
	}else{
        LOG_E(MANAGER,"whitelist is NULL\n");
        prt->proj_config.whitelist = NULL;
    }

    //释放资源
    free(json_string);
    cJSON_Delete(root);
    fclose(file);
    file = NULL;
    return 0;
 }

/**
 * @brief 检查app是否过期
 * 
 * @return int32_t 
 */
static int32_t check_app_expired(PeSFRunTime* prt)
{
    int64_t curr_time,expired_time;

    expired_time = prt->proj_config.valid_time;
    //2.获取当前系统时间
    curr_time = get_curr_time();

    //3.比较系统时间与过期时间，超时返回错误，未超时返回正确
    if(curr_time > expired_time){
        LOG_E("check_app_expired","curr_time = %ld\n",curr_time);
        LOG_E("check_app_expired","expired_time = %ld\n",expired_time);
        return -1;
    }

    LOG_D("manager","check_app_expired ok");
    return 0;
}

/**
 * @brief 启动流程
 * 
 * @param data  :app名称 
 * @param len   :app名称长度
 * @return int32_t
 */
static int32_t exec_start(uint8_t* data, uint16_t len)
{
    int32_t ret = 0;
    uint32_t rtid = 256;
    uint32_t start_flag = -1;

    PeSFRunTime* prt;

    //检查app是否已经启动，如果已经启动，则调用process.on_front函数，然后退出。
    PESF_ITER(prt){
        if(0 == strncmp(prt->app_name, data, len)){
            LOG_I("exec_start","This app has already been run,call process.on_front");
            JSValue global = JS_GetGlobalObject(prt->qjs_ctx);
            JSValue process = JS_GetPropertyStr(prt->qjs_ctx, global, "process");
            JSValue on_front = JS_GetPropertyStr(prt->qjs_ctx, process, "on_front");

            if (JS_IsFunction(prt->qjs_ctx, on_front)) {
                JS_Call(prt->qjs_ctx, on_front, process, 0, NULL);
            }

            JS_FreeValue(prt->qjs_ctx, on_front);
            JS_FreeValue(prt->qjs_ctx, process);
            JS_FreeValue(prt->qjs_ctx, global);

            start_flag = 0;
        }
    }

    if(0 == start_flag){
        return 0;
    }
   

    // 创建运行时
    prt = runtime_create(data, len);

    LOG_D("manager","start app[%s]!",prt->app_name);

    do{
        //解析project.config.json文件，提取信息
        /*ret = get_proj_config_json(prt);
        if(ret != 0){
            LOG_D("manager","get_proj_config_json fail");
            runtime_destroy_simple(prt);
            return -1;
        }
        LOG_D("manager","get_proj_config_json ok");
        //检查app是否过期
        ret = check_app_expired(prt);
        if(0 != ret){
            // 如果已过期，则不启动启动通知脚本，不启动原app
            LOG_E(MANAGER,"the app has expired\n");
            free(prt->app_path.app_js);
            prt->app_path.app_js = EXPIRED_APP;
        }

        LOG_D("manager","check_app_expired ok");*/  //wenqi 0401
        // 创建新线程，分配app运行时给新线程
        ret = pesf_new_thread(prt);
        if (0 != ret) {
            // 如果启动失败，则回收运行时
            runtime_destroy(prt);
            break;
        }
        LOG_D("manager","pesf_new_thread ok");
        
        // 如果启动成功，将运行时压入管理表，并获取rtid
        rtid = hash_add(prt);
        prt->dynamic_property.rtid = rtid;
        LOG_D("manager","hash_add ok");

        // 返回启动成功
        uint8_t buffer[4];
        uint16_t length;
        // 制作启动响应
        make_start_res(buffer, &length, (uint8_t)rtid);
        ret = transport_send(buffer, length);
        LOG_D("manager","res start ok");
    }while(0);

    return ret;
}

/**
 * @brief 结束流程
 * 
 * @param data ：结束流程数据
 * @param len  ：结束流程数据长度
 * @return int32_t 
 */
static int32_t exec_end(uint8_t* data, uint16_t len)
{
    // 1.获取rtid
    uint8_t rtid = data[0];
    LOG_D("manager","exec_end start ");

    // 2.获取APP运行时
    PeSFRunTime* prt;
    prt = hash_find(rtid);
    if (prt == NULL) {
        LOG_E(MANAGER, "Don't have this rtid's app:%d\n", rtid);
        return -1;
    }

    // 3.向子线程发送退出消息
    // 3.1.制作内部消息
    INNER_MSG inner_msg;
    inner_msg.type = E_INNER_END;
    inner_msg.data_length = 0;
    inner_msg.data = NULL;
    // 3.2然后发送给指定线程
    send_data_to_queue(prt, &inner_msg);
    LOG_D("manager","send_data_to_queue ok ");

    // 4.然后监视线程退出
    // 十秒线程没有退出，则强制杀死线程
    join_thread_exit(prt);

    // 退出成功后续，将运行时退出管理表
    hash_del(prt->dynamic_property.rtid);
    LOG_D("manager","hash_del ok ");

    uint8_t buffer[5];
    uint16_t length;
    // 制作结束响应
    make_end_res(buffer, &length, (uint8_t)prt->dynamic_property.rtid);
    transport_send(buffer, length);

    // 执行运行时回收流程
    runtime_destroy(prt);

    return 0;
}

static int32_t exec_pause(uint8_t* app_name, uint16_t len)
{
}

static int32_t exec_resume(uint8_t* app_name, uint16_t len)
{
}

// 为exec_p2a服务，制作消息然后发送给指定运行时的线程

static int32_t make_msg_and_send_to_que(PeSFRunTime* prt, uint16_t len, uint8_t* data)
{
    INNER_MSG inner_msg;
    inner_msg.type = E_INNER_APP_MSG;
    inner_msg.data_length = len - 1;
    //因为实际收到长度不固定，所以此处必须malloc申请数据空间，收到后处理完，再放掉。
    inner_msg.data = (uint8_t*)malloc(inner_msg.data_length);
    memcpy(inner_msg.data, &data[1], inner_msg.data_length);

    LOG_D("manager","send_to_que");
    // 然后发送给指定线程
    return send_data_to_queue(prt, &inner_msg);
}

/**
 * @brief 从打印机收来的消息，分发给各自app
 * 
 * @param data :发送到APP的数据
 * @param len  :发送到APP的数据长度
 * @return int32_t 
 */
static int32_t exec_p2a(uint8_t* data, uint16_t len)
{
    uint8_t rtid = data[0];
    PeSFRunTime* prt;

    LOG_D("manager","exec_p2a start");
    
    if (rtid == 0) {
        // 广播
        LOG_D("manager","broadcast msg");
        // 遍历全部运行时
        PESF_ITER(prt)
        {
            // 制作消息然后发送给子线程
            make_msg_and_send_to_que(prt, len, data);
        }
    } else {
        // 单播
        LOG_D("manager","unicast msg");
        //  获取APP运行时
        prt = hash_find(rtid);
        if (prt == NULL) {
            LOG_E(MANAGER, "Don't have this rtid's app:%d\n", rtid);
            return -1;
        }

        // 制作消息然后发送给子线程
        make_msg_and_send_to_que(prt, len, data);
    }

    return 0;
}

/**
 * @brief 获取静态属性
 *        静态属性保存在应用文件夹下的 property.json 文件中，只需要打开，把全部内容拿出来，传给打印机程序即可
 * @param app_name ：app名
 * @param len      ：app名长度
 * @return int32_t
 */
static int32_t exec_get_static_prop(uint8_t* app_name, uint16_t len)
{
    int32_t ret = -1;
    uint16_t length = 0;

    do {
        // 1.根据名字拼接app的属性文件的完整路径
        
        // 1.1 app_name后边可能没有'\0',也可能后边空间不足
        char temp[APP_NAME_LEN_MAX] = {0};
        strncpy(temp,app_name,len);
        temp[len] = '\0';

        // 1.2 获取完整静态属性路径
        char* property_path = make_file_path(temp,JS_APP_NAME_JSON);
        if (NULL == property_path) {
            break;
        }

        // 2.从属性文件获取全部静态属性
        uint8_t* buf = get_static_property_buffer_from_file(property_path, &length);
        if (NULL == buf) {
            break;
        }

        // 3.发送给打印机程序
        transport_send(buf, length);

        // 4.释放property路径内存
        if (NULL != property_path) {
            free(property_path);
        }
        // 5.释放文件缓存
        if (NULL != buf) {
            free(buf);
        }

        ret = 0;
    } while (0);

    return ret;
}

static void fill_in_dynamic_prop(DynamicProperty* dy_prop, PeSFRunTime* prt)
{
    JSMemoryUsage js_mu;

    // 填充动态内存使用情况
    JS_ComputeMemoryUsage(prt->qjs_rt, &js_mu);
    dy_prop->malloc_size = js_mu.malloc_size;
   
     // 填充当前已使用时间
    int64_t curr_time = get_sys_time();
    dy_prop->duration_time.time_v = curr_time - dy_prop->start_time.time_v;
    if(NULL != dy_prop->duration_time.time_str){
        // 如果已经被使用过,则释放，重新刷新一下，否则会有泄露
        free(dy_prop->duration_time.time_str);
        dy_prop->duration_time.time_str = NULL;
    }
    dy_prop->duration_time.time_str = sys_diff_time_to_str(dy_prop->duration_time.time_v);
}

static void free_dynamic_prop(DynamicProperty* dy_prop)
{
    if(NULL != dy_prop->duration_time.time_str){
        free(dy_prop->duration_time.time_str);
    }

}

static int32_t exec_get_dynamic_prop()
{
    PeSFRunTime* prt;
    DynamicProperty* p_dy_prop;
    cJSON* json_item = NULL;
    char* json_str = NULL;
    uint16_t length;
    uint8_t* buf;

    LOG_I(MANAGER,"exec_get_dynamic_prop start");
    cJSON* json_array = cJSON_CreateArray();

    // 1.遍历正在运行的运行时列表
    PESF_ITER(prt)
    {
        LOG_I(MANAGER,"[%s] get_dynamic start", prt->app_name);
        // 2.取出运行时的动态属性数据
        p_dy_prop = &(prt->dynamic_property);

        // 3.填充动态属性
        fill_in_dynamic_prop(p_dy_prop, prt);

        // 4.结构体转json
        json_item = struct_to_json_dynamic(p_dy_prop);

        // 5.添加到json数组中
        cJSON_AddItemToArray(json_array, json_item);

        // 6.释放动态属性数据
        free_dynamic_prop(p_dy_prop);
    }

    // 6.转换成长字符串，发送给打印机
    LOG_I(MANAGER,"start cJSON Print");
    json_str = cJSON_Print(json_array);

    length = strlen(json_str) + 1;

    // 7.制作发送数据
    LOG_I(MANAGER,"make dynamic buf");
    buf = (uint8_t*)malloc(length + 3);
    make_dynamic_buf(buf, json_str, length);

    LOG_I(MANAGER,"send dynamic buf");
    // 8.发完整数据给打印机
    transport_send(buf, length + 3);

    LOG_I(MANAGER,"free dynamic buf");
    // 9.释放
    free(buf);
    free(json_str);
    cJSON_Delete(json_array);
}

/**
 * @brief 管理执行函数
 *        根据不同的消息类型，调研不同的动作函数
 * @param des ：格式化消息
 * @return int32_t
 */
int32_t manager_execute(FORMAT des)
{
    int32_t ret = 0;

    switch (des.type) {
    case E_START:
        ret = exec_start(des.data, des.length);
        break;
    case E_END:
        ret = exec_end(des.data, des.length);
        break;
    case E_PAUSE:
        ret = exec_pause(des.data, des.length);
        break;
    case E_RESUME:
        ret = exec_resume(des.data, des.length);
        break;
    case E_PRINTER_TO_APP:
        ret = exec_p2a(des.data, des.length);
        break;
    case E_GET_STATIC_PROPERTY:
        ret = exec_get_static_prop(des.data, des.length);
        break;
    case E_GET_DYNAMIC_PROPERTY:
        ret = exec_get_dynamic_prop();
        break;
    }

    return ret;
}

/**
 * @}
 */

