/*
 * @Description:  xps_tools.c
 * @brief: 用于创建xps文件的工具
 * @Author: <PERSON><PERSON>.zhang
 * @Date: 2023-06-20 16:47:26
 * @LastEditors: Zoey.zhang
 * @Email: <EMAIL>
 */

#ifndef XPS_TOOLS_H
#define XPS_TOOLS_H

#include <stdio.h>

#include "../ofd/zip.h"


/**
 * @description: xpsAddImage
 * @brief: 在xps文档中新增image resource
 * @param {zipFile} zFile: zip file
 * @param {char} *dstPath: 目前文件
 * @param {char} *srcPath: 待添加的image
 * @return {*}
 * @author: zoey.zhang
 */
extern int xpsAddResource(zipFile zFile, char *dstPath, const char *srcPath);

/**
 * @description: xpsDocumentInit
 * @brief: 初始化 xps 文档
 * @param {zipFile} zFile
 * @return {void}
 * @author: zoey.zhang
 */
extern int xpsDocumentInit(zipFile zFile);

/**
 * @description: xpsFixedPagesCreate
 * @brief: 创建xps文档中的页面描述文件 %d.fpage
 * @param {zipFile} zFile : 表示xps文档的zip文件
 * @param {char*} path：页面描述文件%d.page路径
 * @param {int} Width：页面宽度，该值必须为在XPS的坐标系统中定义的值。XPS的默认坐标空间：左下角为原点，单位为1/96inch
 * @param {int} Height：页面高度
 * @param {int} pageCount：页数，第几页
 * @param {int} imgW：页面中图像宽度，在xps坐标系
 * @param {int} imgH：图像高度
 * @return {*}
 * @author: zoey.zhang
 */
extern int xpsFixedPagesCreate(zipFile zFile, const char* path, int Width, int Height, int pageCount,  int imgW, int imgH);

/**
 * @description: xpsPageRelsCreate
 * @brief: 创建与fixedpage对应的relationship文件
 * @param {zipFile} zFile: zip file
 * @param {char const*} path：page relationship file path
 * @param {int} imgID: image resource 的ID， 从0开始
 * @return {*}
 * @author: zoey.zhang
 */
extern int xpsPageRelsCreate(zipFile zFile, char const* path, int imgID);

/**
 * @description: xpsFixedDocumentCreate
 * @brief: 创建xps文档中的 FixedDoc.fdoc 文件
 * @param {zipFile} zFile：zip file
 * @param {char} *path：FixedDoc.fdoc文件路径
 * @param {int} pageNum：xps文档中的页面数量
 * @return {*}
 * @author: zoey.zhang
 */
extern int xpsFixedDocumentCreate(zipFile zFile, char *path, int pageNum);

#endif