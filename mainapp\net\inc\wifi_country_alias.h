#ifndef __WIFI_COUNTRY_ALIAS_H__
#define __WIFI_COUNTRY_ALIAS_H__

extern const char* s_wifi_country_alias[];
extern uint32_t s_wifi_country_alias_count;

static inline const char* wifi_country_alias(uint32_t country_code)
{
    RETURN_VAL_IF(country_code == 0 || country_code > s_wifi_country_alias_count, NET_WARN, "");

    return s_wifi_country_alias[country_code - 1];
}

#endif /* __WIFI_COUNTRY_ALIAS_H__ */
