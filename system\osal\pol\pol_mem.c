/**************************************************************
Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name :  	POL (PANTUM OS LAYER)
file   name :	pol_mem.c 
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-26
description	:   pol memmory relative system interface header file 
****************************************************************/

#include "pol_inner.h"
#include "pol/pol_mem.h"
#include "pol_mem_dbg.h"


#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

void* pi_malloc(size_t size)
{
    void* ret = NULL; 

    _pi_input_check(size); 
    ret = malloc(size);
    _pi_retptr_check_err(ret,ret);   
	mem_alloc_trace(ret,ret+size);
    return ret;  
}

void* pi_realloc(void *ptr, size_t size)
{
    void* ret = NULL; 

    _pi_input_check(size||ptr); 
    ret = realloc(ptr, size);
    _pi_retptr_check_err(ret,ret);     
	if(ptr&&!size){
		mem_free_trace(ptr);
	}
	else if(ret!=ptr){
		mem_free_trace(ptr);
		mem_alloc_trace(ret,ret+size);
	}else if(ret == ptr){		
		mem_realloc_trace(ptr,ptr+size);
	}
    return ret;  
}

void* pi_zalloc(size_t size)
{
    void* ret = NULL; 

    _pi_input_check(size); 
    ret = calloc(1,size);
    _pi_retptr_check_err(ret,ret);     
	mem_alloc_trace(ret,ret+size);
    return ret;  
}

void* pi_memset(void *s, int c, size_t n)
{
    void* ret = NULL; 

    _pi_input_check(s&&n); 
	mem_access_trace(s, s+n);	
    ret = memset(s,c,n);
    return ret;
}

void* pi_memcpy(void *dest, const void *src, size_t n)
{
    void* ret = NULL; 

    _pi_input_check(dest&&src&&n&&((dest<src)||(dest>=src+n))); 
	mem_access_trace(dest, dest+n);
	mem_access_trace(src, src+n);
    ret = memcpy(dest,src,n);
    return ret;

}

int pi_memcmp(const void *s1, const void *s2, size_t n)
{
    _pi_input_check(s1&&s2&&n); 
	mem_access_trace(s1, s1+n);
	mem_access_trace(s2, s2+n);
    return memcmp(s1,s2,n);
}

void *pi_mmap(void *addr, size_t length, int prot, int flags, int fd, off_t offset)
{
    void* ret;
    ret = mmap(addr,length,prot,flags,fd,offset);
    _pi_retptr_check_err(ret!=-1,ret);
    return ret;
}

int pi_munmap(void *addr, size_t length)
{
    int ret;
    ret = munmap(addr,length);
    _pi_retint_check_err(ret!=-1,ret);
    return ret;
}

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

void* _pi_malloc(const char *callfile ,const uint32_t callline, size_t size)
{
    void* ret = NULL; 

    _pi_input_check(size); 
    ret = malloc(size);
    _pi_retptr_check_err(ret,ret);   
	alloc_dbg_ops(ret, ret+size, callfile, callline);
    return ret;  
}

void* _pi_realloc(const char *callfile ,const uint32_t callline, void *ptr, size_t size)
{
    void* ret = NULL; 
	
    _pi_input_check(size||ptr); 
    ret = realloc(ptr, size);
    _pi_retptr_check_err(ret,ret);     
	if(ret){
		if(ptr&&!size){
			free_dbg_ops(ptr, callfile, callline);
		}
		else if(ret!=ptr){
			free_dbg_ops(ptr, callfile, callline);			
			alloc_dbg_ops(ret, ret+size, callfile, callline);			
		}else if(ret == ptr){		
			realloc_dbg_ops(ptr,ptr+size,callfile,callline);			
		}
	}
    return ret;  
}

void* _pi_zalloc(const char *callfile ,const uint32_t callline, size_t size)
{
    void* ret = NULL; 

    _pi_input_check(size); 
    ret = calloc(1,size);
    _pi_retptr_check_err(ret,ret);     
    alloc_dbg_ops(ret, ret+size, callfile, callline);
	
    return ret;  
}

void* _pi_memset(const char *callfile ,const uint32_t callline, void *s, int c, size_t n)
{
    void* ret = NULL; 

    _pi_input_check(s&&n); 
	mem_access_trace(s, s+n);		
    ret = memset(s,c,n);
	mem_write_record(s, s+n,pi_gettid(),callfile,callline);	
    return ret;
}

void* _pi_memcpy(const char *callfile ,const uint32_t callline, void *dest, const void *src, size_t n)
{
    void* ret = NULL; 

    _pi_input_check(dest&&src&&n&&((dest<src)||(dest>=src+n))); 
	mem_access_trace(dest, dest+n);
	mem_access_trace(src, src+n);
    ret = memcpy(dest,src,n);
	mem_write_record(dest, dest+n,pi_gettid(),callfile,callline);	
	mem_read_record(src, src+n,pi_gettid(),callfile,callline);	
    return ret;
}

int _pi_memcmp(const char *callfile ,const uint32_t callline, const void *s1, const void *s2, size_t n)
{
	int ret;
    _pi_input_check(s1&&s2&&n); 
	mem_access_trace(s1, s1+n);
	mem_access_trace(s2, s2+n);		
    ret = memcmp(s1,s2,n);
	mem_read_record(s1, s1+n,pi_gettid(),callfile,callline);	
	mem_read_record(s2, s2+n,pi_gettid(),callfile,callline);	
	return ret;	
}

void *_pi_mmap(const char *callfile ,const uint32_t callline, void *addr, size_t length, int prot, int flags, int fd, off_t offset)
{
    void* ret;
    ret = mmap(addr,length,prot,flags,fd,offset);
    _pi_retptr_check_err(ret != (void *)-1,ret);
	mmap_dbg_ops(ret, ret+length, callfile, callline);
	
    return ret;
}

int _pi_munmap(const char *callfile ,const uint32_t callline, void *addr, size_t length)
{
    int ret;
    ret = munmap(addr,length);
    _pi_retint_check_err(ret!=-1,ret);
	if(ret != -1 )
	    munmap_dbg_ops(addr, addr+length, callfile, callline);
	
    return ret;
}

void _pi_free(const char *callfile ,const uint32_t callline, void *ptr)
{
	if(ptr){
		free(ptr);
//		mem_free_trace(ptr);
//		mem_free_record(ptr, gettid(),callfile,callline);	
		free_dbg_ops(ptr, callfile, callline);

	}
}


#endif

