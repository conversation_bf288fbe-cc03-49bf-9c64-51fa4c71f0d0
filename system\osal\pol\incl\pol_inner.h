/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_inner.h
 * @addtogroup pol
 * @{
 * @addtogroup inner
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pol inner header file
 */
#ifndef	 __POL_INNER_H__
#define  __POL_INNER_H__

#include <errno.h>
#include <assert.h>
#include "pol/pol_types.h"
#include "pol/pol_io.h"
#include "pol/pol_string.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

#define pol_err(module,fmt,args...)  			printf("[POL-%s-ERR]:(%s %d) " fmt,module,__FILE__,__LINE__,##args )
#define pol_log(module,fmt,args...)  			printf("[POL-%s-LOG]:(%s %s) " fmt,module,__FILE__,__func__,##args )
#define pol_info(module,fmt,args...)  			printf("[POL-%s-INFO]:" fmt,module,##args )
#define pol_perr(module, err_enum )  			printf("[POL-%s-ERR]:(%s %d) err: %s\n",module,__FILE__,__LINE__,#err_enum )
#define pol_perrno(module)  					printf("[POL-%s-ERR]:(%s %d) syserr:%s\n",module,__FILE__,__LINE__,strerror(errno) )

#ifdef CONFIG_POL_INPUT_CHECK_ASSERT
#define _pol_input_assert       assert(0)
#else
#define _pol_input_assert
#endif

#ifdef CONFIG_POL_RET_CHECK_ASSERT
#define _pol_ret_assert       assert(0)
#else
#define _pol_ret_assert
#endif

#if defined (CONFIG_POL_INPUT_CHECK)
    #if defined (CONFIG_POL_DEBUG_MODE_CALLER)
        #define _pi_input_check(p) do { \
			if (unlikely(!(p))) { \
			    pi_err("%s-%d-%s input err\n", callfile, callline, __func__ ); \
				_pol_input_assert; \
			} \
        } while(0)
        #define _pi_input_check_rtn(p,r) do { \
			if (unlikely(!(p))) { \
	            pi_err("%s-%d-%s input err\n", callfile, callline, __func__ ); \
				_pol_input_assert; \
			} \
        } while(0)
    #elif defined (CONFIG_POL_DEBUG_MODE_BACKTRACE)
        #define _pi_input_check(p) do { \
			if (unlikely(!(p))) { \
				pi_err("%s input err\n", __func__ ); \
				_pol_input_assert; \
			} \
	    } while(0)
        #define _pi_input_check_rtn(p,r) do { \
			if (unlikely(!(p))) { \
			    pi_err("%s input err \n", __func__); \
				_pol_input_assert; \
			} \
        } while(0)
	#endif
#else
    #define _pi_input_check(p)
    #define _pi_input_check_rtn(p,r)
#endif


#if defined (CONFIG_POL_RET_CHECK)
    #if defined (CONFIG_POL_DEBUG_MODE_CALLER)
        #define _pi_ret_check(cond) if(unlikely(!(cond))) {pi_err("%s-%d-%s return err\n ",       \
                callfile,callline,__func__);_pol_ret_assert; }
        #define _pi_ret_check_err(cond) if(unlikely(!(cond))) {pi_err("%s-%d-%s return err:%s\n", \
                callfile,callline,__func__,strerror(errno));_pol_ret_assert;}
		#define _pi_retint_check(cond,retv) if(unlikely(!(cond))) {pi_err("%s-%d-%s return [%d] err\n ",\
                callfile,callline,__func__,retv);_pol_ret_assert; }
		#define _pi_retint_check_err(cond,retv) if(unlikely(!(cond))) {pi_err("%s-%d-%s return [%d] err:%s\n", \
                callfile,callline,__func__,retv,strerror(errno));_pol_ret_assert;}

		#define _pi_retptr_check(cond,retv) if(unlikely(!(cond))) {pi_err("%s-%d-%s return [%p] err\n ",\
		        callfile,callline,__func__,retv);_pol_ret_assert; }
		#define _pi_retptr_check_err(cond,retv) if(unlikely(!(cond))) {pi_err("%s-%d-%s return [%p] err:%s\n", \
		        callfile,callline,__func__,retv,strerror(errno));_pol_ret_assert;}



    #elif defined (CONFIG_POL_DEBUG_MODE_BACKTRACE)
        #define _pi_ret_check(r) if(unlikely(!(r))) {pi_err("%s return err\n ",__func__);_pol_ret_assert;}
        #define _pi_ret_check_err(r) if(unlikely(!(r)) {pi_err("%s return err:%s\n ",       \
                    __func__,strerror(errno));_pol_ret_assert;}
    #endif
#else
    #define _pi_ret_check(r)
    #define _pi_ret_check_err(r)
	#define _pi_retint_check(cond,retv) 
	#define _pi_retint_check_err(cond,retv) 
	#define _pi_retptr_check(cond,retv) 
	#define _pi_retptr_check_err(cond,retv) 
#endif


PT_END_DECLS
#endif /* __POL_INNER_H__ */

/**
 *@}
 */
