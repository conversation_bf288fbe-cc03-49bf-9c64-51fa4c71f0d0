/**************************************************************
Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name :  	POL (PANTUM OS LAYER)
file   name :	pol_io.c
author		:	z<PERSON><PERSON>jing (<EMAIL>)
date		:	2021-09-26
description	:   pol io relative system interface header file 
****************************************************************/

#include <stdarg.h>

#include "pol_inner.h"
#include "pol/pol_io.h"

#ifndef  O_TMPFILE
#define OPEN_WITH_MODE(f)    ((f)&O_CREAT)
#else
#define OPEN_WITH_MODE(f)   ( ((f)&O_CREAT)||((f)&O_TMPFILE))
#endif

#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

int pi_open(const char *pathname, int flags, ...)
{
    int ret;
    _pi_input_check(pathname);
    if(OPEN_WITH_MODE(flags)) {
        mode_t mode;
        va_list args;
        va_start(args,flags);
        mode = va_arg(args,mode_t);
        va_end(args);
        ret = open(pathname, flags,mode);    

    }else{
        ret = open(pathname, flags);    
    }
    _pi_retint_check_err(ret>=0,ret);   
    return ret;
}   

int pi_creat(const char *pathname, mode_t mode)
{

    int ret;
    _pi_input_check(pathname);
    ret = creat(pathname,mode);
    _pi_retint_check_err(ret>=0,ret);   
    return ret;
}

int pi_close(int fd)
{
    int ret;
    ret = close(fd);
    _pi_retint_check_err(ret==0,ret);   
    return ret;
}

ssize_t pi_read(int fd, void *buf, size_t count)
{
    ssize_t ret;
    _pi_input_check((fd>=0)&&buf&&count);
    ret = read(fd,buf,count);
    _pi_ret_check_err(ret>=0);   
    return ret;
}

ssize_t pi_write(int fd, const void *buf, size_t count)
{
    ssize_t ret;
    _pi_input_check((fd>=0)&&buf&&count);
    ret = write(fd,buf,count);
    _pi_ret_check_err(ret>=0);   
    return ret;
}

DIR *pi_opendir(const char *name)
{
    DIR *ret;
    _pi_input_check(name);
    ret = opendir(name);
    _pi_retptr_check_err(ret,ret);   
    return ret;
}

int  pi_closedir(DIR *dirp)
{
    int ret;
    _pi_input_check(dirp);
    ret = closedir(dirp);
    _pi_retint_check_err(ret==0,ret);   
    return ret;
}

FILE *pi_fopen(const char *path, const char *mode)
{
    FILE *ret;
    _pi_input_check(path&&mode);
    ret = fopen(path,mode);
    _pi_retptr_check_err(ret,ret);   
    return ret;
} 

int pi_fclose(FILE *stream)
{
    int ret;
    ret = fclose(stream);
    _pi_retint_check_err(ret==0,ret);   
    return ret;
}

size_t pi_fread(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
    size_t ret;
    _pi_input_check(stream&&ptr&&size&&nmemb);
    ret = fread(ptr,size,nmemb,stream);
#ifdef CONFIG_POL_RET_CHECK
    if(ferror(stream)) {
        pi_err("%s return err \n", __func__);
    };
#endif
    return ret;
}

size_t pi_fwrite(const void *ptr, size_t size, size_t nmemb, FILE *stream)
{
    size_t ret;
    _pi_input_check(stream&&ptr&&size&&nmemb);
    ret = fwrite(ptr,size,nmemb,stream);
#ifdef CONFIG_POL_RET_CHECK
    if(ferror(stream)) {
        pi_err("%s return err \n", __func__);
    };
#endif    
    return ret;
}

int pi_fileno(FILE *stream)
{
    int ret;
    _pi_input_check(stream);
    ret = fileno(stream);
    _pi_retint_check_err(ret>=0,ret);
    return ret;
}

int pi_select(int nfds, fd_set *readfds, fd_set *writefds, fd_set *exceptfds, struct timeval *timeout)
{
    int ret;  
    ret = select(nfds, readfds,writefds,exceptfds,timeout);
    _pi_retint_check_err(ret!=-1,ret);
    return ret;
}

int pi_sscanf(char *str, const char *format, ...)
{
    int ret;
    va_list args;
    _pi_input_check(str&&format);
    va_start(args,format);
    ret = vsscanf(str, format, args);
    va_end(args);
    return ret;
}

int pi_fprintf(FILE *stream, const char *format, ...)
{
    int ret;
    va_list args;
    _pi_input_check(stream&&format);

    va_start(args,format);
    ret = vfprintf(stream, format, args);
    va_end(args);
    return ret;
}

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

int _pi_open(const char *callfile ,const uint32_t callline , const char *pathname, int flags, ...)
{
    int ret;
    _pi_input_check(pathname);
    if(OPEN_WITH_MODE(flags)) {
        mode_t mode;
        va_list args;
        va_start(args,flags);
        mode = va_arg(args,mode_t);
        va_end(args);
        ret = open(pathname, flags,mode);    

    }else{
        ret = open(pathname, flags);    
    }
    _pi_retint_check_err(ret>=0,ret);   
    return ret;
}

int _pi_creat(const char *callfile ,const uint32_t callline, const char *pathname, mode_t mode)
{
    int ret;
    _pi_input_check(pathname);
    ret = creat(pathname,mode);
    _pi_retint_check_err(ret>=0,ret);   
    return ret;
}

int _pi_close(const char *callfile ,const uint32_t callline, int fd)
{
    int ret;
    ret = close(fd);
    _pi_retint_check_err(ret==0,ret);   
    return ret;
}

ssize_t _pi_read(const char *callfile ,const uint32_t callline, int fd, void *buf, size_t count)
{
    ssize_t ret;
    _pi_input_check((fd>=0)&&buf&&count);
    ret = read(fd,buf,count);
    _pi_ret_check_err(ret>=0);   
    return ret;
}

ssize_t _pi_write(const char *callfile ,const uint32_t callline, int fd, const void *buf, size_t count)
{
    ssize_t ret;
    _pi_input_check((fd>=0)&&buf&&count);
    ret = write(fd,buf,count);
    _pi_ret_check_err(ret>=0);   
    return ret;
}

DIR *_pi_opendir(const char *callfile ,const uint32_t callline, const char *name)
{
    DIR *ret;
    _pi_input_check(name);
    ret = opendir(name);
    _pi_retptr_check_err(ret,ret);   
    return ret;
}

int  _pi_closedir(const char *callfile ,const uint32_t callline, DIR *dirp)
{
    int ret;
    _pi_input_check(dirp);
    ret = closedir(dirp);
    _pi_retint_check_err(ret==0,ret);   
    return ret;
}

FILE *_pi_fopen(const char *callfile ,const uint32_t callline, const char *path, const char *mode)
{
    FILE *ret;
    _pi_input_check(path&&mode);
    ret = fopen(path,mode);
    _pi_retptr_check_err(ret,ret);   
    return ret;
} 

int _pi_fclose(const char *callfile ,const uint32_t callline, FILE *stream)
{
    int ret;
    ret = fclose(stream);
    _pi_retint_check_err(ret==0,ret);   
    return ret;
}

size_t _pi_fread(const char *callfile ,const uint32_t callline, void *ptr, size_t size, size_t nmemb, FILE *stream)
{
    size_t ret;
    _pi_input_check(stream&&ptr&&size&&nmemb);
    ret = fread(ptr,size,nmemb,stream);
#ifdef CONFIG_POL_RET_CHECK
    if(ferror(stream)) {
        pi_err("%s return err \n", __func__);
    };
#endif
    return ret;
}

size_t _pi_fwrite(const char *callfile ,const uint32_t callline, const void *ptr, size_t size, size_t nmemb,
        FILE *stream)
{
    size_t ret;
    _pi_input_check(stream&&ptr&&size&&nmemb);
    ret = fwrite(ptr,size,nmemb,stream);
#ifdef CONFIG_POL_RET_CHECK
    if(ferror(stream)) {
        pi_err("%s return err \n", __func__);
    };
#endif    
    return ret;
}

int _pi_fileno(const char *callfile ,const uint32_t callline, FILE *stream)
{
    int ret;
    _pi_input_check(stream);
    ret = fileno(stream);
    _pi_retint_check_err(ret>=0,ret);
    return ret;
}

int _pi_select(const char *callfile ,const uint32_t callline, int nfds, fd_set *readfds, fd_set *writefds, fd_set *exceptfds, struct timeval *timeout)
{
    int ret;  
    ret = select(nfds, readfds,writefds,exceptfds,timeout);
    _pi_retint_check_err(ret!=-1,ret);
    return ret;
}

int _pi_sscanf(const char *callfile, const uint32_t callline, char *str, const char *format, ...)
{
    int ret;
    va_list args;
    _pi_input_check(str&&format);

    va_start(args,format);
    ret = vsscanf(str, format, args);
    va_end(args);
    return ret;
}

int _pi_fprintf(const char *callfile, const uint32_t callline, FILE *stream, const char *format, ...)
{
    int ret;
    va_list args;
    _pi_input_check(stream&&format);

    va_start(args, format);
    ret = vfprintf(stream, format, args);
    va_end(args);
    return ret;
}

#endif

