/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file nettls.h
 * @addtogroup net
 * @{
 * @addtogroup nettls
 * <AUTHOR>
 * @date 2023-7-28
 * @brief TLS manager header file
 */
#ifndef __NETTLS_H__
#define __NETTLS_H__

#include "netctx.h"

typedef enum
{
    CERT_REQ    = 0,
    CERT_TXT    = 1,
    CERT_APPLY  = 2,
    CERT_UPDATE = 3
}
CERT_FLAG_E;

typedef struct tls_cert_info
{
    long    ver;                ///< 版本号
    char    sn[32];             ///< 颁发序号
    int32_t snlen;              ///< 序号的实际长度
    char    date_start[64];     ///< 证书的开始日期
    char    date_end[64];       ///< 证书的结束日期
    char    encryption[128];    ///< 使用的公钥加密算法
    char    sign_alg[128];      ///< 使用的签名算法
    char    ca[360];            ///< 颁发者
    char    user[360];          ///< 证书用户名
    char    pubkey[1024];       ///< 颁发者的公钥信息
    char    signature[1024];    ///< 颁发者的签名信息
}
TLS_CERT_INFO_S;

typedef struct tls_cert_self
{
    char    name[64];           ///< common name
    char    org[64];            ///< organization name
    char    unit[64];           ///< organizational unit name
    char    city[64];           ///< province name
    char    state[64];          ///< state name
    char    country[4];         ///< country name
    char    start[16];          ///< start time
    char    end[16];            ///< end time
    int32_t flag;               ///< CERT_FLAG_E
    char    res[8];             ///< res
}
TLS_CERT_SELF_S;

typedef struct tls_cert_conf
{
    int32_t type;               ///< 0:rsa   1:ec
    int32_t key_length;
    int32_t days;
    char    password[24];
    char    cert_path[40];
    char    key_path[40];
    char    pub_key_path[40];
}
TLS_CERT_CONF_S;

/**
 * @brief       Reset configuration of current certificate
 * @return      Get result
 * @retval      == 0        : success\n
 *              != 0        : fail
 * <AUTHOR> Xin
 * @date        2023-12-01
 */
int32_t         nettls_reset_cert_conf  (void);

/**
 * @brief       Get infomation of current certificate
 * @param[out]  info        : Parse current certificate and save to the TLS_CERT_INFO_S.
 * @param[out]  def         : 1 - using firmware certificate; 0 - using self-signed certificate
 * @return      Get result
 * @retval      == 0        : success\n
 *              != 0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         nettls_get_cert_info    (TLS_CERT_INFO_S* info, int32_t* is_def);

/**
 * @brief       Install a certificate
 * @param[in]   certfile    : The certificate file path
 * @param[in]   password    : The certificate private key
 * @return      Install result
 * @retval      == 0        : success\n
 *              != 0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t	        nettls_install_cert     (const char* certfile, const char* password);

/**
 * @brief       Make a self-signed certificate
 * @param[in]   cert    : Certificate configuration.
 * @param[out]  outfile : Output certificate file.
 * @return      certificate file path.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         nettls_make_cert        (TLS_CERT_SELF_S* cert, const char* outfile);

/**
 * @brief       generate a certificate
 * @param[in]   cert    : Certificate configuration.
 * @return      make result.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> huanbin
 * @date        2024-6-14
 */
int32_t         nettls_generate_cert(TLS_CERT_CONF_S* cert_conf_info);

/**
 * @brief       Delete the self-signed certificate\n
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
void            nettls_delete_cert      (void);

/**
 * @brief       TLS module initialize
 * @param[out]  net_ctx : The NET_CTX_S object pointer.
 * @return      initialize result.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         nettls_init             (NET_CTX_S* net_ctx);

/**
 * @brief       TLS module de-initialize
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
void            nettls_deinit           (void);

#endif /* __NETTLS_H__ */
/**
 *@}
 */
