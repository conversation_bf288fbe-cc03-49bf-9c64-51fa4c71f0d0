INCLUDE_FLAGS := -Iinclude \
		   	 -Iosal/pol/incl 

LIB_FLAGS := -L/usr/local/lib -lgtest -lpthread -lb64

DEFINE_FUNC := Linux
DEFINE_FLAGS := $(patsubst %, -D%, $(DEFINE_FUNC))

TARGET := gtest/gtest_system

GTEST_MAIN := gtest/gtest_main.cpp

#所有参与测试模块中的.cpp文件
DIR_MODULES := gtest/ptm_base64_gtest/*.cpp\
			gtest/pol_gtest/*.cpp

MODULE_CPP := $(wildcard $(DIR_MODULES))

#需要用到的测试接口关联的.c文件
SRC_MODULE := osal/pol/pol_threads/*.c\
	osal/pol/*.c\
	gtest/pol_gtest/*.c\
	osal/pol/os/Linux/*.c \
	lib/ptm_base64/*.c \
	lib/cbinder/*.c

SRC := $(filter-out osal/pol/pol_mem_trace.c,$(wildcard $(SRC_MODULE)))
OBJS := $(patsubst %.c,%.o,$(SRC)) \
	$(patsubst %.cpp,%.o,$(GTEST_MAIN)) \
	$(patsubst %.cpp,%.o,$(MODULE_CPP))

GTEST_CC := gcc
GTEST_CPP := g++
CFLAGS := -g -Wextra -std=gnu99
CPPFLAGS := -g  -Wextra

.PHONY:all
all:$(TARGET)
$(TARGET):$(OBJS)
	@echo " [CPP]   $@"
	$(GTEST_CPP) $(OBJS) -o $(TARGET)  $(LIB_FLAGS) $(LDFLAGS)
	@echo -e
	@echo "****************************"
	@echo "*****gtest build Finish*****"
	@echo "****************************"

	$(GTEST_CC) -fPIC -shared -o libsystem.so $(SRC) $(INCLUDE_FLAGS) $(DEFINE_FLAGS) $(CFLAGS)
%.o:%.c
	@echo " [CC]   $@"
	$(GTEST_CC) -c $(CFLAGS) $(DEFINE_FLAGS) $(INCLUDE_FLAGS)  $^ -o $@

%.o:%.cpp
	@echo " [CPP] $(CPP)  $@"
	$(GTEST_CPP) -c $(CPPFLAGS) $(DEFINE_FLAGS) $(INCLUDE_FLAGS) $^ -o $@
