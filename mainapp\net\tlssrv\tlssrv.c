/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file tlssrv.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief TLS Server for port 443
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netsock.h"
#include "netmisc.h"
#include "http_task.h"
#include "power_manager/power_manager.h"

#define PRNSDK_URL_PREFIX   "/PrintSystem"
#define PEDKSRV_URL_PREFIX  "/pedk"
#define ESCLSRV_URL_PREFIX  "/eSCL"
#define IPPSRV_URL_PREFIX   "/ipp"

typedef enum
{
    TLS_TYPE_UNKNOWN = 0,
    TLS_TYPE_WEBSRV,
    TLS_TYPE_IPPSRV,
    TLS_TYPE_ESCLSRV,
    TLS_TYPE_PEDKSRV,
    TLS_TYPE_PRNSDK
}
TLS_TYPE_E;

typedef struct tlssrv_private_info
{
    TLS_TYPE_E          srv_type;
}
PRIV_INFO_S;

typedef struct tlssrv_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         srv_tid;
}
TLSSRV_CTX_S;

static TLSSRV_CTX_S*    s_tlssrv_ctx = NULL;

static int32_t is_ippsrv_url(enum http_method method, const char* url)
{
    RETURN_VAL_IF(strncmp(url, IPPSRV_URL_PREFIX, strlen(IPPSRV_URL_PREFIX)) == 0 || (method == HTTP_POST && strcmp(url, "/") == 0), NET_NONE, 1);

    return 0;
}

static int32_t is_esclsrv_url(enum http_method method, const char* url)
{
    RETURN_VAL_IF(strncmp(url, ESCLSRV_URL_PREFIX, strlen(ESCLSRV_URL_PREFIX)) == 0, NET_NONE, 1);

    return 0;
}

static int32_t is_pedksrv_url(enum http_method method, const char* url)
{
    RETURN_VAL_IF(strncmp(url, PEDKSRV_URL_PREFIX, strlen(PEDKSRV_URL_PREFIX)) == 0, NET_NONE, 1);

    return 0;
}

static int32_t is_prnsdk_url(enum http_method method, const char* url)
{
    RETURN_VAL_IF(strncmp(url, PRNSDK_URL_PREFIX, strlen(PRNSDK_URL_PREFIX)) == 0, NET_NONE, 1);

    return 0;
}

static int32_t tlssrv_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    int32_t ret = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( is_ippsrv_url(method, url) )
    {
        priv->srv_type = ( ippsrv_construct(ptask)  ? TLS_TYPE_UNKNOWN : TLS_TYPE_IPPSRV );
    }
    else if ( is_esclsrv_url(method, url) )
    {
        priv->srv_type = ( esclsrv_construct(ptask) ? TLS_TYPE_UNKNOWN : TLS_TYPE_ESCLSRV );
    }
    else if ( is_pedksrv_url(method, url) )
    {
        priv->srv_type = ( pedksrv_construct(ptask) ? TLS_TYPE_UNKNOWN : TLS_TYPE_PEDKSRV );
    }
    else if ( is_prnsdk_url(method, url) )
    {
        priv->srv_type = ( prnsdk_construct(ptask)  ? TLS_TYPE_UNKNOWN : TLS_TYPE_PRNSDK );
    }
    else
    {
        priv->srv_type = ( websrv_construct(ptask)  ? TLS_TYPE_UNKNOWN : TLS_TYPE_WEBSRV );
    }

    if ( priv->srv_type != TLS_TYPE_UNKNOWN && ptask->headers_complete_callback != tlssrv_process_headers ) /* 确认callback被重载，避免无效嵌套 */
    {
        ret = ptask->headers_complete_callback(ptask, method, url, content_length);
    }
    else
    {
        NET_WARN("request method(%s) url(%s) not supported", http_method_str(method), url);
    }
    return ret;
}

static int32_t tlssrv_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    NET_WARN("tlssrv does not handle HTTP requests directly from client(%u->%u)!!!", ptask->r_port, ptask->l_port);
    return -1;
}

static int32_t tlssrv_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    NET_WARN("tlssrv does not handle HTTP requests directly from client(%u->%u)!!!", ptask->r_port, ptask->l_port);
    return -1;
}

static int32_t tlssrv_construct(HTTP_TASK_S* ptask)
{
    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[0] == NULL )
    {
        ptask->priv_subclass[0] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[0] == NULL, NET_WARN, -1);
    }
    else
    {
        NET_INFO("priv_subclass[0] is not NULL, reset");
    }
    ptask->headers_complete_callback = tlssrv_process_headers;
    ptask->reqbody_received_callback = tlssrv_process_reqbody;
    ptask->request_complete_callback = tlssrv_process_request;

    return 0;
}

static void tlssrv_destruct(HTTP_TASK_S* ptask)
{
    if ( ptask != NULL && ptask->priv_subclass[0] != NULL )
    {
        PRIV_INFO_S* priv = (PRIV_INFO_S *)ptask->priv_subclass[0];
        switch ( priv->srv_type )
        {
            case TLS_TYPE_IPPSRV:  ippsrv_destruct(ptask);  break;
            case TLS_TYPE_ESCLSRV: esclsrv_destruct(ptask); break;
            case TLS_TYPE_PEDKSRV: pedksrv_destruct(ptask); break;
            case TLS_TYPE_PRNSDK:  prnsdk_destruct(ptask);  break;
            case TLS_TYPE_WEBSRV:  websrv_destruct(ptask);  break;
            default: break;
        }
        pi_free(ptask->priv_subclass[0]);
        ptask->priv_subclass[0] = NULL;
    }
}

static void tlssrv_connection(void* arg)
{
    NET_CONN_S*     pnc = (NET_CONN_S *)arg;
    HTTP_TASK_S*    ptask;
    QIO_S*          pqssl;

    pqssl = qio_ssl_create(pnc->sockfd, NETQIO_SSL_SERVER, NETQIO_SOCK_NOCLOSE);
    if ( pqssl != NULL )
    {
        ptask = http_task_create(pqssl, pnc->remote_port, pnc->local_port);
        if ( tlssrv_construct(ptask) == 0 )
        {
            http_task_process(ptask);
        }
        tlssrv_destruct(ptask);
        http_task_destroy(ptask);
        QIO_CLOSE(pqssl);
    }
    netsock_close_connection(pnc);
}

static void* tlssrv_thread_handler(void* arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };    /* PORT: 443 */
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    int32_t         update = 0;
    int32_t         max_fd;

    while ( 1 )
    {
        FD_ZERO(&rfds);
        max_fd = 0;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, TLS_PORT, 1);
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_tlssrv_ctx), tlssrv_connection, pnc) < 0 )
                    {
                        NET_WARN("add tlssrv_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}

static void tlssrv_ignore_signal(int32_t sig)
{
    NET_INFO("Ignoring signal: %d\n", sig);
}

int32_t tlssrv_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_tlssrv_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_tlssrv_ctx = (TLSSRV_CTX_S *)pi_zalloc(sizeof(TLSSRV_CTX_S));
    RETURN_VAL_IF(s_tlssrv_ctx == NULL, NET_WARN, -1);

    signal(SIGPIPE, tlssrv_ignore_signal);
    do
    {
        s_tlssrv_ctx->net_ctx = net_ctx;
        s_tlssrv_ctx->srv_tid = pi_thread_create(tlssrv_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "tlssrv_thread_handler");
        BREAK_IF(s_tlssrv_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("tlssrv initialize result(%d)", ret);
    if ( ret != 0 )
    {
        tlssrv_epilog();
    }
    return ret;
}

void tlssrv_epilog(void)
{
    if ( s_tlssrv_ctx != NULL )
    {
        if ( s_tlssrv_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_tlssrv_ctx->srv_tid);
        }
        pi_free(s_tlssrv_ctx);
        s_tlssrv_ctx = NULL;
    }
}
/**
 *@}
 */
