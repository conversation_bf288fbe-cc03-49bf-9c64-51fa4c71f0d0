#mainapp中的头文件路径
INCLUDE_FLAGS := -Iinclude/print \
                 -Icopy/copy_core/configuration_mgr_subsystem \
                -Icopy/copy_core/copy_mgr_subsystem \
                -Icopy/copy_core/job_mgr_subsystem \
                -Icopy/ \
		-Iprint/print_app/print_status \
                -Iprint/print_app/print_finisher \
                -Iprint/print_app/print_process \
                -Iprint/print_app/print_framework \
                -Iprint/engine_if/engine_if_app/engine_mgr \
                -Iprint/engine_if/engine_if_framework/incl \
                -Iprint/engine_if/engine_if_framework/engine_general_if_layer \
				-Ipublic \
				-Iinclude \
				-Iinclude/scan \
				-Iinclude/copy \
				-Iinclude/net \
				-Iinclude/platform \
				-Iinclude/nvtables \
				-Iinclude/proxy_transfer \
				-Iinclude/data_distribution \
				-Iinclude/system_manager \
				-Iinclude/usb \
				-Iinclude/public \
				-Iinclude/printer_status \
				-Iinclude/acl \
				-Iplatform

#编译生成可执行的gtest测试程序 可能需要链接到的库
LIB_FLAGS := -L/usr/local/lib -lgtest -lpthread $(LDFLAGS) -lcommon -losal  -lb64

#编译宏
DEFINE_FUNC := Linux \
			CONFIG_PLATFORM_CM605ADN
DEFINE_FLAGS := $(patsubst %, -D%, $(DEFINE_FUNC))

TARGET := gtest/gtest_system

#gtest main主函数,一般不需要修改
GTEST_MAIN := gtest/gtest_main.cpp

#开发过程中使用gtest工具 主要在以下两个变量中添加文件路径，以及根据demo格式添加文件名
#1.所有参与测试模块中的.cpp文件
#DIR_MODULES := gtest/msgrouter_gtest/*.cpp \
#            gtest/acl_gtest/*.cpp
DIR_MODULES := gtest/print/print_app/print_framework/print_get_str_gtest.cpp
DIR_MODULES += gtest/copy/configuration_interface_gtest.cpp
DIR_MODULES += gtest/system_manager/system_manager.cpp
DIR_MODULES += gtest/proxy_transfer/proxy_transfer.cpp
DIR_MODULES += gtest/platform_gtest/platform_gtest.cpp

#2.需要用到的测试接口关联的.c文件
#SRC_MODULE := public/*.c \
#	acl/*.c \
#	system_manager/*.c \
#	proxy_transfer/*.c
SRC_MODULE := print/print_app/print_framework/print_get_str.c \
    public/msgrouter_main.c
SRC_MODULE += copy/copy_core/configuration_mgr_subsystem/*.c
SRC_MODULE += system_manager/*.c \
			  proxy_transfer/*.c \
			  platform/platform_*.c \
			  acl/*.c \
			  usb/usbdevice/*.c

SRC := $(wildcard $(SRC_MODULE))

MODULE_CPP := $(wildcard $(DIR_MODULES))

OBJS := $(patsubst %.c,%.o,$(SRC)) \
	$(patsubst %.cpp,%.o,$(GTEST_MAIN)) \
	$(patsubst %.cpp,%.o,$(MODULE_CPP))

GTEST_CC := gcc
GTEST_CPP := g++
CFLAGS := -g -Wextra -std=gnu99
CPPFLAGS := -g  -Wextra

.PHONY:all
all:$(TARGET)
$(TARGET):$(OBJS)
	@echo " [CPPs]   $@"
	$(GTEST_CPP) $(OBJS) -o $(TARGET)  $(LIB_FLAGS) $(LDFLAGS)
	@echo -e
	@echo "****************************"
	@echo "*****gtest build Finish*****"
	@echo "****************************"
%.o:%.c
	@echo " [CC]   $@"
	$(GTEST_CC) -c $(CFLAGS) $(DEFINE_FLAGS) $(INCLUDE_FLAGS)  $^ -o $@

%.o:%.cpp
	@echo " [CPP] $(GTEST_CPP)  $@"
	$(GTEST_CPP) -c $(CPPFLAGS) $(DEFINE_FLAGS)  $(INCLUDE_FLAGS) $^ -o $@

