/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file parser_resource.c
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2025-01-07
 * @brief parser resource service
 */

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_threads.h"
#include "pol/pol_string.h"
#include "pol/pol_log.h"
#include "cbinder/cbinder.h"

#include "parser_ips.h"
#include "ips/parser_ips_interation.h"

static unsigned token;

/**
 * @brief cbinder server callback
 * @param[in] bs
 * @param[in] txn
 * @param[in] msg
 * @param[in] reply
 * @param[in] user_data
 * @param[out] N/A
 * @return int32_t
 * @retval 0
 * <AUTHOR>
 * @date 2025-01-16
 * @note  N/A
 */
static int32_t parser_resource_handler(struct binder_state* bs, struct binder_txn* txn, struct binder_io* msg, struct binder_io* reply, void* user_data)
{
    switch ( txn->code )
    {
        case PARSER_IPS_CMD_GET_SYSTEM_STATUS_FOR_MONO:
        {
            int32_t ips_render_mode;
            unsigned long result = 0;
            ips_render_mode = (int32_t)bio_get_uint32(msg);
            result = print_ips_interation_get_system_status_for_mono(ips_render_mode);
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_SET_DO_ENCRYPT_WARN:
        {
            int32_t count;
            count = bio_get_uint32(msg);
            print_ips_interation_ioSetDOEncryptWarn(count);
            break;
        }

        case PARSER_IPS_CMD_SET_DO_UNSUPPORT_FILE_FORMAT:
        {
            int32_t count;
            count = bio_get_uint32(msg);
            print_ips_interation_ioSetDOUnsupportedFileFormat(count);
            break;
        }

        case PARSER_IPS_CMD_GET_MACHINE_SPEED:
        {
            uint32_t result = 0;
            result =  print_ips_interation_param_get_machine_speed();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_MACHINE_COLOR:
        {
            uint32_t result;
            result = print_ips_interation_param_get_machine_color();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_FINISHER_INSRALL:
        {
            uint32_t result;
            result = print_ips_interation_config_get_finisher_install();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_UDISK_PAPERSIZE_VALID_CHECK:
        {
            uint32_t result;
            int ips_paper_size = bio_get_uint32(msg);
            result = print_ips_interation_udisk_paper_size_valid_check(ips_paper_size);
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_TRAY_INSTALL:
        {
            int32_t result;
            IPS_INPUT_TRAY_E ips_input_tray = (IPS_INPUT_TRAY_E)bio_get_uint32(msg);
            result = print_ips_interation_param_get_tray_install(ips_input_tray);
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_TRAY_INEMPTY_STATUS:
        {
            uint32_t result;
            IPS_INPUT_TRAY_E ips_input_tray = (IPS_INPUT_TRAY_E)bio_get_uint32(msg);
            result = print_ips_interation_param_get_tray_in_empty_status(ips_input_tray);
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_PAPER_SIZE:
        {
            uint32_t result;
            IPS_MEDIA_SIZE_E media_size_ptr;
            IPS_PRINT_CUSTOM_TYPE_E custom_type;
            uint32_t width;
            uint32_t height;

            IPS_INPUT_TRAY_E ips_input_tray = (IPS_INPUT_TRAY_E)bio_get_uint32(msg);
            result =  print_ips_interation_param_ips_get_paper_size(ips_input_tray,&media_size_ptr,&custom_type,&width,&height);

            bio_put_uint32(reply, result);
            bio_put_uint32(reply, media_size_ptr);
            bio_put_uint32(reply, custom_type);
            bio_put_uint32(reply, width);
            bio_put_uint32(reply, height);
            break;
        }

        case PARSER_IPS_CMD_GET_PAPER_TYPE:
        {
            uint32_t ips_paper_type;
            int32_t result;
            uint32_t ips_tray_input = (IPS_INPUT_TRAY_E)bio_get_uint32(msg);
            result = print_ips_interation_param_pass_tray_get_paper_type(ips_tray_input,&ips_paper_type);
            bio_put_uint32(reply, result);
            bio_put_uint32(reply, ips_paper_type);
            break;
        }

        case PARSER_IPS_CMD_ADD_BAND_TOPAGE_OUTPUT_QUEUE_PAGE:
        {
            break;
        }

        case PARSER_IPS_CMD_FINISHED_ADDING_TOPAGE_OUTPUT_QUEUE_PAGE:
        {
            uint32_t result;
            result = print_ips_interation_finished_adding_to_page_output_queue_page();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_CREATE_PAGE_OUTPUT_QUEUE_SET:
        {
            unsigned int  copies =  bio_get_uint32(msg);
            int collate = bio_get_uint32(msg);
            PAGE_OUTPUT_QUEUE_APP_E producing_app = (PAGE_OUTPUT_QUEUE_APP_E)bio_get_uint32(msg);
            PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E incoming_format  = (PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E)bio_get_uint32(msg);
            PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E storage_format = (PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E)bio_get_uint32(msg);

            uint32_t result =  print_ips_interation_create_page_output_queue_set(copies,
                collate,producing_app,incoming_format,storage_format);
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_END_OF_JOB:
        {
            int32_t result = print_ips_interation_io_end_of_job();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_IO_IPS_IN_USE:
        {
            int32_t result =  print_ips_interation_io_is_ips_in_use();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_IO_STOP_READ_DATA:
        {
            int32_t result = print_ips_interation_io_stop_read_data();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_IO_STOP_FILE_READ_DATA:
        {
            int32_t result = print_ips_interation_io_stop_file_read_data();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_PRN_READ_ENABLE_FLAG:
        {
            int32_t result = print_ips_interation_io_get_prn_read_enable_flag();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_JOB_ID_VALUE:
        {
            int32_t result = print_ips_interation_param_get_job_id_value();
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_IO_POST_JOB_END_MSG:
        {
            int job_number = (int)bio_get_uint32(msg);
            int result = print_ips_interation_io_post_job_end_msg(job_number);
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_IO_READ_DATA:
        {
            break;
        }

        case PARSER_IPS_CMD_IO_POST_JOB_PIPELINT_NAME:
        {
            uint32_t len = 0;
            char *name = bio_get_string8(msg,&len);
            int bytes  = (int)bio_get_uint32(msg);
            uint32_t job_id = bio_get_uint32(msg);
            print_ips_interation_io_post_job_pipelint_name(name,bytes,job_id);
            break;
        }

        case PARSER_IPS_CMD_GET_TRAY_IN_AND_EDGE:
        {
            IPS_MEDIA_SIZE_E ips_media_size = (IPS_MEDIA_SIZE_E)bio_get_uint32(msg);
            IPS_MEDIA_TYPE_E ips_media_type=  (IPS_MEDIA_TYPE_E)bio_get_uint32(msg);
            IPS_INPUT_TRAY_E ips_input_tray_original = (IPS_INPUT_TRAY_E)bio_get_uint32(msg);
            IPS_PRINT_MODE_E print_mode = (IPS_PRINT_MODE_E)bio_get_uint32(msg);

            IPS_BOOKBINDING_S ips_bookbinding;
            ips_bookbinding.ips_fold_mode = (IPS_LFOLD_E)bio_get_uint32(msg);
            ips_bookbinding.ips_punching = (IPS_LPUNCHING_E)bio_get_uint32(msg);
            ips_bookbinding.ips_lstaple = (IPS_LSTAPLE_E)bio_get_uint32(msg);
            ips_bookbinding.ips_orientation = (IPS_ORIENTATION_E)bio_get_uint32(msg);

            IPS_EDGE_E edge;
            IPS_INPUT_TRAY_E ips_input_tray_in;

            int32_t result =  print_ips_interation_get_tray_in_and_edge(ips_media_size,ips_media_type,
                    ips_input_tray_original,&edge, &ips_input_tray_in,print_mode,  ips_bookbinding);

            bio_put_uint32(reply, result);
            bio_put_uint32(reply, edge);
            bio_put_uint32(reply, ips_input_tray_in);

            pi_log_i("Get result [%d],edge[%d],ips_input_tray_in[%d]\n",result,edge,ips_input_tray_in);

            break;
        }

        case PARSER_IPS_CMD_GET_SYSTEM_SUPPORT_MEDIA_SIZE:
        {
            int result = 0;
            bio_put_uint32(reply, result);
            break;
        }

        case PARSER_IPS_CMD_GET_TIMEOUT_VALUE_INT:
        {
            int timeout = 120;
            uint32_t result = print_ips_interation_inf_get_timeout_value_int(&timeout);
            bio_put_uint32(reply, result);
            bio_put_uint32(reply, timeout);
            break;
        }

        case PARSER_IPS_CMD_ADD_PAGE_TO_PAGE_OUTPUT_QUEUE_SET:
        {
            PAGE_OUTPUT_QUEUE_PAGE_DESC_S desc;
            uint32_t len;
            desc = *((PAGE_OUTPUT_QUEUE_PAGE_DESC_S *)bio_get_buffer(msg,&len));
            int page_number = bio_get_uint32(msg);
            uint32_t result = print_ips_interation_add_page_to_page_output_queue_set(&desc,page_number);
            bio_put_uint32(reply,result);
            bio_put_buffer(reply,(char *) &desc,sizeof(PAGE_OUTPUT_QUEUE_PAGE_DESC_S));
            break;
        }

        case PARSER_IPS_CMD_FINISHED_ADDING_PAGES_TO_PAGE_OUTPUT_QUEUE_SET:
        {
            (void)print_ips_interation_finished_adding_pages_to_page_output_queue_set();
            break;
        }

        case PARSER_IPS_CMD_GET_IPS_LIB_PARAM:
        {
            IPS_LIB_PARAM_S lib_param = print_ips_interation_param_get_ips_lib_param();
            bio_put_buffer(reply,(char *)&lib_param,sizeof(IPS_LIB_PARAM_S));
            break;
        }

        case PARSER_IPS_CMD_GET_JOB_SETTING_TYPE:
        {
            int32_t result = print_ips_interation_GetJobSettingType();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_SET_TIMEOUT_FLAG:
        {
            int timeout_flag = bio_get_uint32(msg);
            (void)print_ips_interation_io_set_timeout_flag(timeout_flag);
            break;
        }

        case PARSER_IPS_CMD_GET_TIMEOUT_FLAG:
        {
            int32_t result =  print_ips_interation_get_timeout_flag();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_GET_MFP_READY:
        {
            int32_t result = print_ips_interation_get_mfp_ready();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_GET_MEMOBJ_ID:
        {
            int32_t result = print_ips_interation_get_memobj_id();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_ADD_BAND_TO_PAGE_OUTPUT_QUEUE_PAGE_NEW:
        {
            int32_t band_id = (int32_t)bio_get_uint32(msg);
            uint32_t result = print_ips_interation_add_band_to_page_output_queue_page_new(band_id);
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_INF_GET_CANCEL_FLAG:
        {
            uint32_t sz;
            char *func = bio_get_string8(msg,&sz);
            uint32_t result = print_ips_interation_inf_get_cancel_flag(func);
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_IS_PJLPS_DATA_STREAM:
        {
            uint32_t result = 0;
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_GET_USB_DUMMY:
        {
            uint32_t result = print_ips_interation_io_get_USB_dummy();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_INPUT_TIMEOUT:
        {
            int type = (int)bio_get_uint32(msg);
            print_ips_interation_io_input_timeout(type);
            break;
        }

        case PARSER_IPS_CMD_IO_GET_QIO_ERROR:
        {
            int result = print_ips_interation_io_get_qio_error();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER:
        {
            int result = print_ips_interation_io_always_in_sniffer();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_REWIND_DATA:
        {
            uint32_t sz = 0;
            char *buffer = bio_get_buffer(msg,&sz);
            int bytes = (int) bio_get_uint32(msg);
            int flag = (int)bio_get_uint32(msg);
            int result = print_ips_interation_io_rewind_data(buffer,bytes,flag);
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_SET_PDF_FONT_MISSING_WARN:
        {
            print_ips_interation_io_set_pdf_font_missing_warn();
            break;
        }

        case PARSER_IPS_CMD_IO_SET_PDF_ENCRYPT_WARN:
        {
            print_ips_interation_io_set_pdf_encrypt_warn();
            break;
        }

        case PARSER_IPS_CMD_IO_SET_PDF_FONT_INVALID_WARN:
        {
            print_ips_interation_io_set_pdf_font_invalid_warn();
            break;
        }

        case PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_JOB:
        {
            int result = print_ips_interation_io_is_air_print_pdf_job();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_READ_OVER:
        {
            int result = print_ips_interation_io_is_air_print_pdf_read_over();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_GET_UDISK_PARAM:
        {
            IPS_PRINT_PARAM_S print_param = print_ips_interation_get_udisk_print_param();
            bio_put_buffer(reply,(char *) &print_param,sizeof(IPS_PRINT_PARAM_S));
            break;
        }

        case PARSER_IPS_CMD_GET_CUSTOM_SIZE_TABLE:
        {
            IPS_CUSTOM_TYPE_TABLE_S tables = print_ips_interation_get_custom_size_table();
            bio_put_buffer(reply,(char *) &tables,sizeof(IPS_CUSTOM_TYPE_TABLE_S));
            break;
        }
        case PARSER_IPS_CMD_IO_FILE_READ_DATA:
        {
            uint32_t sz = 0;
            char *buffer = NULL;
            char *pbuf = bio_get_buffer(msg,&sz);
            int bytes = (int) bio_get_uint32(msg);
            do
            {
                buffer =  (char *)pi_malloc(bytes);
                if(NULL == buffer)
                {
                    pi_msleep(1);
                }
            }while(NULL == buffer);
            pi_memcmp(buffer,pbuf,bytes);

            int result = print_ips_interation_io_file_read_data(buffer,bytes);
            bio_put_buffer(reply,buffer, bytes);
            bio_put_uint32(reply,bytes);
            bio_put_uint32(reply,result);
            pi_free(buffer);
            break;
        }

        case PARSER_IPS_CMD_IO_GET_FIRSET_PASS_PRN_PAGES:
        {
            int result = print_ips_interation_get_first_pass_prn_pages();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_GET_SAMPLE_PARAM:
        {
            IPS_SAMPLE_PARAM_S sample_param = print_ips_interation_param_get_sample_param();
            bio_put_buffer(reply,(char *) &sample_param,sizeof(IPS_SAMPLE_PARAM_S));
            break;
        }

        case PARSER_IPS_CMD_IO_GET_JOB_SUSPEND_STATUS:
        {
            int32_t result = print_ips_interation_param_get_job_suspend_status();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_IO_SET_FILE_ENCRYPT_WARN:
        {
            print_ips_interation_io_set_file_encrypt_warn();
            break;
        }

        case PARSER_IPS_CMD_IO_SET_FILE_LARGE_WARN:
        {
            print_ips_interation_io_set_file_large_warn();
            break;
        }

        case PARSER_IPS_CMD_IO_SET_FILE_MISSING_WARN:
        {
            print_ips_interation_io_set_file_font_missing_warn();
            break;
        }

        case PARSER_IPS_CMD_IO_SET_FILE_UNSUPPORT_WARN:
        {
            print_ips_interation_io_set_file_unsupport_warn();
            break;
        }

        case PARSER_IPS_CMD_SET_PARSER_AGAIN_FLAG:
        {
            int flag;
            flag = (int)bio_get_uint32(msg);
            print_ips_interation_set_parse_again_flag(flag);
            break;
        }

        case PARSER_IPS_CMD_GET_PARSER_AGAIN_FLAG:
        {
            int result = print_ips_interation_get_parse_again_flag();
            bio_put_uint32(reply,result);
            break;
        }

        case PARSER_IPS_CMD_ANSWER_SUSPEND_ACK_BY_IPS:
        {
            print_ips_interation_answer_suspend_ack_by_ips();
            break;
        }

        case PARSER_IPS_CMD_SET_IMAGEMEM_RELEASE:
        {
            print_ips_interation_set_imagemem_release();
            break;
        }

        default:
        {
            pi_log_e("invalid cmd(%x)\n", txn->code);
            break;
        }
    }

    return 0;
}

/**
 * @brief cbinder server thread
 * @param[in] arg
 * @param[out] N/A
 * @return void*
 * @retval NULL
 * <AUTHOR>
 * @date 2025-01-16
 * @note  N/A
 */
static void* parser_resource_thread(void* arg)
{
    struct binder_state* bs = NULL;
    int32_t ret = 0;

    bs = binder_start(1);

    ret = svcmgr_add_service(bs, BINDER_SERVICE_MANAGER, "pantum.parser.resource", &token);
    RETURN_VAL_IF(ret != 0, pi_log_e, NULL);

    binder_add_target(bs, &token, parser_resource_handler, NULL);

    while ( 1 )
    {
        sleep(100000);
    }

    binder_stop(bs);

    return NULL;
}

/**
 * @brief start cbinder server thread
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0
 * <AUTHOR>
 * @date 2025-01-16
 * @note  N/A
 */
int32_t parser_resource_prolog(void)
{
    pi_thread_create(parser_resource_thread, PI_NORMAL_STACK, NULL, PI_MEDIUM_PRIORITY, NULL, "parser_resource_thread");
    return 0;
}


/**
 * @brief
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-01-16
 * @note  N/A
 */
void parser_resource_epilog(void)
{
    return;
}
/**
 *@}
 */
