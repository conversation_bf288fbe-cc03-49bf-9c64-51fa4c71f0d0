/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file platform_pedk.c
 * @addtogroup platform
 * @{
 * @addtogroup platform_pedk
 * <AUTHOR>
 * @date 2024-06-04
 * @brief platform pedk module init
 *        PEDK API : 1.System Capability API
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>

#include "pedk_mgr.h"
#include "platform_pedk.h"
#include "platform_api.h"
#include "pol/pol_log.h"
#include "pol/pol_string.h"
#include "cjson/cJSON.h"
#include "location_api.h"
//#include "../../print/print_core/print_app/print_ctl/print_ctl_pedk.h"
#include "print_ctl_pedk.h"



#ifndef ENABLE
#define ENABLE 1
#endif
#ifndef DISABLE
#define DISABLE 0
#endif
#define CAPABIL_STRLEN 8

/*
 *@brief PEDK define system capability list
*/
typedef struct {
    bool        color;                              ///< Color printing
    bool        duplex_enable;                      ///< Duplex unit
    bool        wired_enable;                       ///< Wired network
    bool        wireless_enable;                    ///< Wireless network
    bool        usbhttp_enable;                     ///< USB HTTP protocol
    bool        print_enble;                        ///< Printing
    bool        scan_enble;                         ///< Scanning
    bool        copy_enble;                         ///< Copying
    bool        fax_enble;                          ///< Faxing
    bool        printrk_mode;                       ///< Red-black printing
    bool        cloud_print;                        ///< Cloud printing
    char        res;                                ///< res undefine
    uint16_t    expand_max_tray_count;              ///< Maximum supported tray count
    uint16_t    exist_tray_count;                   ///< Current tray count
    uint16_t    max_print_copies;                   ///< Maximum print copies
    char        scan_type[CAPABIL_STRLEN];          ///< Scan type
    char        print_language[CAPABIL_STRLEN];     ///< Print parsing language
} CAPABILITY_LIST_S;

/**
 *@ brief system language
 */
typedef enum
{
    PEDK_LANGUAGE_CHINESE = 1,
    PEDK_LANGUAGE_ENGLISH,
    PEDK_LANGUAGE_FRENCH,
    PEDK_LANGUAGE_GERMAN,
    PEDK_LANGUAGE_HEBREW,
    PEDK_LANGUAGE_ITALIAN,
    PEDK_LANGUAGE_JAPANESE,
    PEDK_LANGUAGE_POLISH = 8,
    PEDK_LANGUAGE_RUSSIAN = 26,
    PEDK_LANGUAGE_SPANISH = 9,
    PEDK_LANGUAGE_TRADITIONAL_CHINESE = 10,
    PEDK_LANGUAGE_TURKISH = 11,
    PEDK_LANGUAGE_ARABIC = 27,
    PEDK_LANGUAGE_PORTUGUESE= 12,
    PEDK_LANGUAGE_KOREAN = 13,
    PEDK_LANGUAGE_ROMANIAN = 14,
    PEDK_LANGUAGE_THAI = 15,
    PEDK_LANGUAGE_GREEK = 16,
    PEDK_LANGUAGE_CZECH = 17,
    PEDK_LANGUAGE_UKRAINIAN = 18,
    PEDK_LANGUAGE_KAZAKH = 19,
    PEDK_LANGUAGE_DANISH = 20,
    PEDK_LANGUAGE_NORWEGIAN = 21,
    PEDK_LANGUAGE_SWEDISH = 22,
    PEDK_LANGUAGE_AZERBAIJANI = 23,
    PEDK_LANGUAGE_HUNGARIAN = 24,
    PEDK_LANGUAGE_BULGARIAN = 25,
    PEDK_LANGUAGE_VIETNAMESE = 28,
    PEDK_LANGUAGE_NONE = 0xFF
} PEDK_LANGUAGE_CODE_E;

// 映射数组
int language_mapping[] = {
    0, PEDK_LANGUAGE_CHINESE, PEDK_LANGUAGE_ENGLISH, PEDK_LANGUAGE_FRENCH, PEDK_LANGUAGE_GERMAN, PEDK_LANGUAGE_HEBREW, PEDK_LANGUAGE_ITALIAN,
    PEDK_LANGUAGE_JAPANESE, PEDK_LANGUAGE_POLISH, PEDK_LANGUAGE_RUSSIAN, PEDK_LANGUAGE_SPANISH, PEDK_LANGUAGE_TRADITIONAL_CHINESE,
    PEDK_LANGUAGE_TURKISH, PEDK_LANGUAGE_ARABIC, PEDK_LANGUAGE_PORTUGUESE, PEDK_LANGUAGE_KOREAN, PEDK_LANGUAGE_ROMANIAN, PEDK_LANGUAGE_THAI,
    PEDK_LANGUAGE_GREEK, PEDK_LANGUAGE_CZECH, PEDK_LANGUAGE_UKRAINIAN, PEDK_LANGUAGE_KAZAKH, PEDK_LANGUAGE_DANISH, PEDK_LANGUAGE_NORWEGIAN,
    PEDK_LANGUAGE_SWEDISH, PEDK_LANGUAGE_AZERBAIJANI, PEDK_LANGUAGE_HUNGARIAN, PEDK_LANGUAGE_BULGARIAN, PEDK_LANGUAGE_VIETNAMESE
};

// 反向映射数组
int reverse_language_mapping[] = {
    0, SYS_LANGUAGE_CHINESE, SYS_LANGUAGE_ENGLISH, SYS_LANGUAGE_FRENCH, SYS_LANGUAGE_GERMAN, SYS_LANGUAGE_HEBREW, SYS_LANGUAGE_ITALIAN,
    SYS_LANGUAGE_JAPANESE, SYS_LANGUAGE_POLISH, SYS_LANGUAGE_SPANISH, SYS_LANGUAGE_TRADITIONAL_CHINESE, SYS_LANGUAGE_TURKISH,
    SYS_LANGUAGE_PORTUGUESE, SYS_LANGUAGE_KOREAN, SYS_LANGUAGE_ROMANIAN, SYS_LANGUAGE_THAI, SYS_LANGUAGE_GREEK, SYS_LANGUAGE_CZECH,
    SYS_LANGUAGE_UKRAINIAN, SYS_LANGUAGE_KAZAKH, SYS_LANGUAGE_DANISH, SYS_LANGUAGE_NORWEGIAN, SYS_LANGUAGE_SWEDISH, SYS_LANGUAGE_AZERBAIJANI,
    SYS_LANGUAGE_HUNGARIAN, SYS_LANGUAGE_BULGARIAN, SYS_LANGUAGE_RUSSIAN, SYS_LANGUAGE_ARABIC, SYS_LANGUAGE_VIETNAMESE
};

#if 0
int language_mapping[] = {
    0,
    PEDK_LANGUAGE_CHINESE,
    PEDK_LANGUAGE_ENGLISH,
    PEDK_LANGUAGE_FRENCH,
    PEDK_LANGUAGE_GERMAN,
    PEDK_LANGUAGE_HEBREW,
    PEDK_LANGUAGE_ITALIAN,
    PEDK_LANGUAGE_JAPANESE,
    PEDK_LANGUAGE_POLISH,
    PEDK_LANGUAGE_RUSSIAN,
    PEDK_LANGUAGE_SPANISH,
    PEDK_LANGUAGE_TRADITIONAL_CHINESE,
    PEDK_LANGUAGE_TURKISH,
    PEDK_LANGUAGE_ARABIC,
    PEDK_LANGUAGE_PORTUGUESE,
    PEDK_LANGUAGE_KOREAN,
    PEDK_LANGUAGE_ROMANIAN,
    PEDK_LANGUAGE_THAI,
    PEDK_LANGUAGE_GREEK,
    PEDK_LANGUAGE_CZECH,
    PEDK_LANGUAGE_UKRAINIAN,
    PEDK_LANGUAGE_KAZAKH,
    PEDK_LANGUAGE_DANISH,
    PEDK_LANGUAGE_NORWEGIAN,
    PEDK_LANGUAGE_SWEDISH,
    PEDK_LANGUAGE_AZERBAIJANI,
    PEDK_LANGUAGE_HUNGARIAN,
    PEDK_LANGUAGE_BULGARIAN,
    PEDK_LANGUAGE_VIETNAMESE
};
#endif
static CAPABILITY_LIST_S s_cur_capablist = {0};

static int get_mapping_language(e_LanguageCode lang)
{
    //检查输入有效性，确保在映射数组的范围内
    if(lang < 1 || lang > SYS_LANGUAGE_VIETNAMESE)
    {
        pi_log_e("Invalid language type:%d\n",lang);
        return -1;
    }
    return language_mapping[lang];
}
// 获取封装接口语言编码
int get_encapsulated_language(e_LanguageCode lang) {
    if (lang < 1 || lang > 28) {
        printf("Invalid language type: %d\n", lang);
        return -1;
    }
    return language_mapping[lang];
}

// 反向设置原始语言编码
int set_original_language(PEDK_LANGUAGE_CODE_E pedkLang) {
    if (pedkLang < 1 || pedkLang > 28) {
        printf("Invalid encapsulated language type: %d\n", pedkLang);
        return -1;
    }
    return reverse_language_mapping[pedkLang];
}


//init capability configs from platform, save to current capablity list
static void capability_init(void)
{
    MACHINE_CONFIG_S cur_config;
    int32_t          ret;
    uint32_t         tmp;

    s_cur_capablist.expand_max_tray_count = 7;
    s_cur_capablist.max_print_copies      = 9999;
    s_cur_capablist.cloud_print           = false;

    ret = pi_platform_get_config(&cur_config);
    if ( ret == -1)
    {
        pi_log_e("capability get config from platform failed.\n");
        return;
    }

    switch( cur_config.scan_type )
    {
    case SCAN_TYPE_FB:
        pi_strncpy(s_cur_capablist.scan_type, "FB", pi_strlen("FB"));
        break;
    case SCAN_TYPE_ADF:
        pi_strncpy(s_cur_capablist.scan_type, "ADF", pi_strlen("ADF"));
        break;
    case SCAN_TYPE_DADF:
        pi_strncpy(s_cur_capablist.scan_type, "DADF", pi_strlen("DADF"));
        break;
    case SCAN_TYPE_RADF:
        pi_strncpy(s_cur_capablist.scan_type, "RADF", pi_strlen("RADF"));
        break;
    default:
        pi_strncpy(s_cur_capablist.scan_type, "None", pi_strlen("None"));
        break;
    }

    switch( cur_config.print_language )
    {
    case PRINT_LANGUAGE_GDI:
        pi_strncpy(s_cur_capablist.print_language, "GDI", pi_strlen("GDI"));
        break;
    case PRINT_LANGUAGE_IPS:
        pi_strncpy(s_cur_capablist.print_language, "IPS", pi_strlen("IPS"));
        break;
    default:
        pi_strncpy(s_cur_capablist.print_language, "None", pi_strlen("None"));
        break;
    }

    if ( cur_config.color == COLOR_PRINT )
    {
        s_cur_capablist.color = true;
    }
    else
    {
        s_cur_capablist.color = false;
    }

    if ( cur_config.duplex_enable == ENABLE )
    {
        s_cur_capablist.duplex_enable = true;
    }
    else
    {
        s_cur_capablist.duplex_enable = false;
    }
    if ( cur_config.wired_enable == ENABLE )
    {
        s_cur_capablist.wired_enable = true;
    }
    else
    {
        s_cur_capablist.wired_enable = false;
    }
    if ( cur_config.wireless_enable == ENABLE )
    {
        s_cur_capablist.wireless_enable = true;
    }
    else
    {
        s_cur_capablist.wireless_enable = false;
    }
    if ( cur_config.usbhttp_enable == ENABLE )
    {
        s_cur_capablist.usbhttp_enable = true;
    }
    else
    {
        s_cur_capablist.usbhttp_enable = false;
    }
    if ( cur_config.print_enable == ENABLE )
    {
        s_cur_capablist.print_enble = true;
    }
    else
    {
        s_cur_capablist.print_enble = false;
    }
    if ( cur_config.scan_enable == ENABLE )
    {
        s_cur_capablist.scan_enble = true;
    }
    else
    {
        s_cur_capablist.scan_enble = false;
    }
    if ( cur_config.copy_enable == ENABLE )
    {
        s_cur_capablist.copy_enble = true;
    }
    else
    {
        s_cur_capablist.copy_enble = false;
    }
    if ( cur_config.fax_enable == ENABLE )
    {
        s_cur_capablist.fax_enble = true;
    }
    else
    {
        s_cur_capablist.fax_enble = false;
    }
    if ( cur_config.print_rk_mode == ENABLE )
    {
        s_cur_capablist.printrk_mode = true;
    }
    else
    {
        s_cur_capablist.printrk_mode = false;
    }

    ret = pi_platform_get_exit_tray_count(&tmp);
    if ( ret == -1)
    {
        pi_log_e("capability get exit tray count failed.\n");
    }
    s_cur_capablist.exist_tray_count = (uint16_t)(tmp & 0xFFFF);

    return;
}

static int32_t pedk_get_CapabilitiesList(void)
{
    int32_t ret = 0;
    int32_t len;
    cJSON*  obj = NULL;
    char*   json_str = NULL;

    pi_log_d("pedk-capability get list API\n");
    capability_init();
    obj = cJSON_CreateObject(); //creat JSON obj

    cJSON* array_str1 = cJSON_CreateArray();        //string => array
    cJSON_AddItemToArray(array_str1, cJSON_CreateString(s_cur_capablist.scan_type));
    cJSON_AddItemToObject(obj,   "Scan_Type",        array_str1);
    cJSON_AddBoolToObject(obj,   "Color",            s_cur_capablist.color);
    cJSON_AddBoolToObject(obj,   "Duplex_Enable",    s_cur_capablist.duplex_enable);
    cJSON_AddBoolToObject(obj,   "Wired_Enable",     s_cur_capablist.wired_enable);
    cJSON_AddBoolToObject(obj,   "Wireless_Enable",  s_cur_capablist.wireless_enable);
    cJSON* array_str2 = cJSON_CreateArray();        //string => array
    cJSON_AddItemToArray(array_str2, cJSON_CreateString(s_cur_capablist.print_language));
    cJSON_AddItemToObject(obj,   "Print_Language",   array_str2);
    cJSON_AddNumberToObject(obj, "Expand_Max_Tray_Count", s_cur_capablist.expand_max_tray_count);
    cJSON_AddNumberToObject(obj, "Exist_Tray_count", s_cur_capablist.exist_tray_count);
    cJSON_AddBoolToObject(obj,   "Usbhttp_Enable",   s_cur_capablist.usbhttp_enable);
    cJSON_AddBoolToObject(obj,   "Print_Enable",     s_cur_capablist.print_enble);
    cJSON_AddBoolToObject(obj,   "Scan_Enable",      s_cur_capablist.scan_enble);
    cJSON_AddBoolToObject(obj,   "Copy_Enable",      s_cur_capablist.copy_enble);
    cJSON_AddBoolToObject(obj,   "Fax_Enable",       s_cur_capablist.fax_enble);
    cJSON_AddBoolToObject(obj,   "Print_Rk_Mode",    s_cur_capablist.printrk_mode);
    cJSON_AddBoolToObject(obj,   "Cloud_Print",      s_cur_capablist.cloud_print);
    cJSON_AddNumberToObject(obj, "Max_Print_Copies", s_cur_capablist.max_print_copies);

    //json format list
    json_str = cJSON_PrintUnformatted(obj);
    if( NULL == json_str )
    {
        cJSON_Delete(obj);

        pi_log_e("get json string failed\n");
        return -1;
    }
    len = pi_strlen(json_str);

    //MSG communicate RE<->MFP
    pi_log_i("pedk_get_CapabilitiesList< %s >, len[%d]\n", json_str, len);
    ret = pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LEN, len, NULL, 0);
    if ( -1 == ret )
    {
        free(json_str);
        cJSON_Delete(obj);
        pi_log_e("[pedk_get_CapabilitiesList]send to RE failed:list len\n");
        return -1;
    }
    ret = pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LIST, len, (unsigned char*)json_str, len);
    if ( -1 == ret )
    {
        free(json_str);
        cJSON_Delete(obj);
        pi_log_e("[pedk_get_CapabilitiesList]send to RE failed:list context\n");
        return -1;
    }
    free(json_str);
    cJSON_Delete(obj);
    return ret;
}

static void platform_pedkapi_handler(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t* buf, void* ctx)
{
    int32_t ret;
    pi_log_i("sub %d, respond %d, buf_size %d, addr %p\n", sub, respond, buf_size, buf);

    switch( sub )
    {
    case MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LEN:
    case MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LIST:
        ret = pedk_get_CapabilitiesList();
        if ( !ret )
        {
            pi_log_e("pedk_get_CapabilitiesList error\n");
        }
        break;
    case MSG_PLATFORM_SUB_DEVICEINFO_GET_DCFWVERSION:
        {
            char version[NVLEN_VERSION] = {0};

            (void)pi_platform_get_firmware_version(version, NVLEN_VERSION);
            pi_log_d("[pedk_get_DC_version]:version[%s]\n", version);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_DCFWVERSION, 0, (unsigned char*)version, sizeof(version));
        }
        break;
    case MSG_PLATFORM_SUB_DEVICEINFO_GET_ECFWVERSION:
        {
            char version[NVLEN_LVERSION] = {0};

            (void)pi_platform_get_engine_firmware_version(version, NVLEN_LVERSION);
            pi_log_d("[pedk_get_EC_version]:version[%s]\n", version);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_ECFWVERSION, 0, (unsigned char*)version, sizeof(version));

        }
        break;
    case MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA1VERSION:
        {
            char ver_fpga1[NVLEN_LVERSION] = {0};
            char ver_fpga2[NVLEN_LVERSION] = {0};

            (void)pi_platform_get_fpga_ver(ver_fpga1, ver_fpga2);
            pi_log_d("[pedk_get_FPGA1 version]:FPGA1 version[%s]\n", ver_fpga1);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA1VERSION, 0, (unsigned char*)ver_fpga1, sizeof(ver_fpga1));
        }
        break;
    case MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA2VERSION:
        {
            char ver_fpga1[NVLEN_LVERSION] = {0};
            char ver_fpga2[NVLEN_LVERSION] = {0};

            (void)pi_platform_get_fpga_ver(ver_fpga1, ver_fpga2);
            pi_log_d("[pedk_get_FPGA2 version]:FPGA2 version[%s]\n", ver_fpga2);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA2VERSION, 0, (unsigned char*)ver_fpga2, sizeof(ver_fpga2));
        }
        break;
    case MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTNAME:
        {
            char pdtname[PDT_NAME_LEN] = {0};

            (void)pi_platform_get_print_name_string(pdtname, PDT_NAME_LEN);
            pi_log_d("[pedk_get_print_name_string]:pdtname[%s]\n", pdtname);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTNAME, 0, (unsigned char*)pdtname, sizeof(pdtname));
        }
        break;
    case MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTSN:
        {
            char    sn_str[NVLEN_PROD_SER_NUM] = {0};

            (void)pi_platform_get_serial_number(sn_str, NVLEN_PROD_SER_NUM);
            pi_log_d("[pedk_get_serial_number]:serial_number[%s]\n", sn_str);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTSN, 0, (unsigned char*)sn_str, sizeof(sn_str));
        }
        break;
    case MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTPOSITION:
        {
            char position[NVLEN_PRINTER_LOCATION] = {0};

            (void)pi_platform_get_location(position, NVLEN_PRINTER_LOCATION);
            pi_log_d("[pedk_get_serial_number]:product position[%s]\n", position);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTPOSITION, 0, (unsigned char*)position, sizeof(position));
        }
        break;
    case MSG_PLATFORM_SUB_PRODUCTINFO_GET_LANGUAGE:
        {
            uint16_t language_code;
            pi_platform_get_language_code(&language_code);
            pi_log_d("[pedk_get_language_code]:language_code[%hu]\n", language_code);
            uint16_t mapping_language = (uint16_t)get_encapsulated_language(language_code);
            pi_log_d("[pedk_get_language_code]:mapping_language[%hu]\n", mapping_language);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_LANGUAGE, 0, (uint8_t *)&mapping_language, sizeof(mapping_language));
        }
        break;
	case MSG_PLATFORM_SUB_PRODUCTINFO_SET_LANGUAGE:
        {
            uint16_t set_int = *buf;
            pi_log_d("[pedk_set_language_code] set language %hu\n",set_int);
            ret = pi_platform_set_language_code((uint16_t)set_original_language(set_int));
            pi_log_d("[pedk_set_language_code] set language ret %d\n",ret);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_PLATFORM_SUB_PRODUCTINFO_SET_LANGUAGE, ret, NULL, 0);
            break;
	}

    case MSG_CAPABILITIESLIST_SUB_SUPPORT_PAPER_SIZE_LIST:
    case MSG_CAPABILITIESLIST_SUB_SUPPORT_MEDIA_TYPE_LIST:
        {
            print_ctl_pedk_capabilities_handler( sub, respond, buf_size, buf, ctx );
        }
        break;

    default:
        pi_log_e("Invaild MSG: %d\n", sub);
        break;
    }

    return;
}

int32_t platform_pedkapi_init(void)
{
    int32_t ret = 0;

    ret = pedk_mgr_register_handler(MSG_MODULE_PLATFORM, platform_pedkapi_handler, NULL);
    return ret;
}

void platform_pedkapi_deinit(void)
{
    pedk_mgr_unregister_handler(MSG_MODULE_PLATFORM);
}

/**
 * @}
 */

