/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_usbd.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief QIO object wrapper for file usb device
 */
#include "qiox.h"

typedef struct priv_info
{
    QIO_CLOSE_CALLBACK  callback;
    void*               context;
    int32_t             dev_fd;
}
PRIV_INFO_S;

static int32_t qio_usbd_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, priv);
    struct timeval  tv = { .tv_sec = (__time_t)tos, .tv_usec = (__suseconds_t)tous };
    fd_set          fds;
    fd_set*         rfds = ( what & QIO_POLL_READ  ) ? &fds : NULL;
    fd_set*         wfds = ( what & QIO_POLL_WRITE ) ? &fds : NULL;
    fd_set*         efds = ( what & QIO_POLL_EVENT ) ? &fds : NULL;
    int32_t         ret;

    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    FD_ZERO(&fds);
    FD_SET(priv->dev_fd, &fds);
    ret = select(priv->dev_fd + 1, rfds, wfds, efds, (tos < 0 || (tos == 0 && tous < 0)) ? NULL : &tv);
    if ( ret == 0 )
    {
        if ( ioctl(priv->dev_fd, QUSBD_CONNECT_STATUS, 0) == 0 )
        {
            ret = -1;
        }
        else if ( ioctl(priv->dev_fd, QUSBD_PC_DUMMY_STATUS, 0) == 0 ) /* 软固件协议与IPS的约定，当PC处理慢时返回-2 */
        {
            ret = -2;
        }
    }

    return ret;
}

static int32_t qio_usbd_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    return read(priv->dev_fd, buffer, nbuf);
}

static int32_t qio_usbd_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    return write(priv->dev_fd, buffer, nbuf);
}

static int32_t qio_usbd_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

static int32_t qio_usbd_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( priv )
    {
        QIO_DEBUG("close USBDEVICE(%d) QIO<%p>", priv->dev_fd, pqio);
        if ( priv->callback )
        {
            priv->callback(priv->context);
        }
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_usbd_create(int32_t dev_fd, QIO_CLOSE_CALLBACK callback, void* context)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;

    RETURN_VAL_IF(dev_fd < 0, QIO_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    priv->callback  = callback;
    priv->context   = context;
    priv->dev_fd    = dev_fd;

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        QIO_WARN("alloc USBDEVICE(%d) QIO failed: %d<%s>", dev_fd, errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }

    QIO_DEBUG("create USBDEVICE(%d) QIO<%p>", dev_fd, pqio);
    pqio->close     = qio_usbd_close;
    pqio->poll      = qio_usbd_poll;
    pqio->read      = qio_usbd_read;
    pqio->write     = qio_usbd_write;
    pqio->seek      = qio_usbd_seek;
    pqio->priv      = priv;

    return pqio;
}

void* qio_usbd_get_context(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    return priv->context;
}
/**
 *@}
 */
