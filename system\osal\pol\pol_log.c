/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_log.c
 * @addtogroup log
 * @{
 * <AUTHOR>
 * @date 2023-05-9
 * @brief pantum osal log interface set
 */
#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_list.h"
#include "pol/pol_mem.h"

#include "pol_threads_private.h"

#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <stdio.h>
#include <sys/file.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/time.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <time.h>
#include "pol/pol_log.h"
#include <sys/file.h>
#include "pol_crypto.h"
#include "pol/pol_threads.h"

#define LOG_FILE_MAX_SIZE   (0x400000)                              ///< The single log file is the maximum 4M Bytes
#define LOG_FILE_TAG        "\n**** LOG DATA OUTPUT TO FILE ****\n" ///< The mark that start save log to file
#define LOG_FILE_PATH       "/tslog/pol_log"                        ///< The directory path of the log file
#define LOG_FILE_0          LOG_FILE_PATH "/pol_log_0.log"          ///< The current log filename
#define LOG_FILE_1          LOG_FILE_PATH "/pol_log_1.log"          ///< The last log filename
#define LOG_FILE_CRASH      LOG_FILE_PATH "/pol_log_crash.log"      ///< The crash log filename
#define NETLOG_DEV          "/dev/netlog_dev"

#define LOG_MODE_TABLE(out) {           \
    out(LOG_OUT_TO_CONSOLE, "console")  \
    out(LOG_OUT_TO_FILE,    "file"   )  \
}

#define LOG_LVL_TABLE(out)  {           \
    out(LOG_LVL_OFF,        "OFF"    )  \
    out(LOG_LVL_FATAL,      "FATAL"  )  \
    out(LOG_LVL_ERROR,      "ERROR"  )  \
    out(LOG_LVL_WARN,       "WARNING")  \
    out(LOG_LVL_INFO,       "INFO"   )  \
    out(LOG_LVL_DEBUG,      "DEBUG"  )  \
    out(LOG_LVL_TRACE,      "TRACE"  )  \
    out(LOG_LVL_MAX,        "ALL"    )  \
}

#define name_out(e, n)      n,
#define enum_out(e, n)      e,

//crypto or no crypto
#define CRYPTO_FILE

#define NV_LVL_DF_VALUE     0xFF    ///< nv default log level value

typedef enum LOG_MODE_TABLE(enum_out)   LOG_OUTPUT_MODE_E;
typedef enum LOG_LVL_TABLE(enum_out)    LOG_OUTPUT_LVL_E;

typedef struct log_record
{
    struct list_head    record_list;
    struct timeval      record_time;
    const char*         lvl_tag;
    char                content[1024];
    char                caller[48];
}
LOG_RECORD_S;

static struct list_head s_output_async_list = PI_LIST_HEAD_INIT(s_output_async_list);
static void*            s_output_async_tid  = NULL;
static void*            s_output_async_mtx  = NULL;
static void*            s_output_async_sem  = NULL;
static const char*      s_output_mode_tag[] = LOG_MODE_TABLE(name_out);
static const char*      s_output_lvl_tag[]  = LOG_LVL_TABLE(name_out);
//static int32_t          s_output_mode_set   = LOG_OUT_TO_CONSOLE;
//static int32_t          s_output_lvl_set    = LOG_LVL_OFF;
static uint32_t          s_output_mode_set   = LOG_OUT_TO_CONSOLE;
static uint32_t          s_output_lvl_set    = LOG_LVL_OFF;
static ssize_t          s_netlog_refresh    = 0;
static int32_t          s_netlog_handle     = -1;
static int32_t          s_logfile_handle    = -1;
static int32_t          s_crashlog_handle   = -1;
static ssize_t          s_logfile_size      = 0;
static uint8_t          s_crypt_key[32]     = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A";
static uint8_t          s_crypt_iv[16]      = "9db8891021833ba6";
static void (*s_flush_crashlog_callback)(void) = NULL;

void pi_log_lvl_mode_init(uint32_t output_lvl_set, uint32_t ouput_mode_set)
{
    if ( NV_LVL_DF_VALUE != output_lvl_set )
    {
        pi_log_i("set nv log level is %u \n", output_lvl_set);
        s_output_lvl_set = output_lvl_set;
    }

    s_output_mode_set = ouput_mode_set;
}
static void log_record_send_to_console(LOG_RECORD_S* record, const char* buf)
{
    /* 关闭日志写文件的句柄 */
    if ( s_logfile_handle >= 0 )
    {
        printf("[%s:%d][CALL:%s] current output mode is console, close logfile handle(%d)\n", __FILE__, __LINE__, record->caller, s_logfile_handle);
        close(s_logfile_handle);
        s_logfile_handle = -1;
    }

    /* 每分钟重载一次netlog句柄 */
    if ( (s_netlog_handle >= 0) && (s_netlog_refresh + 60 < record->record_time.tv_sec) )
    {
        printf("[%s:%d][CALL:%s] reload netlog handle(%d)\n", __FILE__, __LINE__, record->caller, s_netlog_handle);
        close(s_netlog_handle);
        s_netlog_handle = -1;
    }

    /* 检查是否加载netlog驱动，日志数据优先输出到netlog，否则从串口输出 */
    if ( HAS_FILE(NETLOG_DEV) && s_netlog_handle < 0 )
    {
        s_netlog_refresh = record->record_time.tv_sec;
        s_netlog_handle  = open(NETLOG_DEV, O_WRONLY);
    }

    if ( s_netlog_handle < 0 )
    {
        printf("%s", buf);
    }
    else
    {
        if ( write(s_netlog_handle, buf, strlen(buf)) < 0 )
        {
            printf("[%s:%d][CALL:%s] write to netlog failed: %d<%s>", __FILE__, __LINE__, record->caller, errno, strerror(errno));
            close(s_netlog_handle);
            s_netlog_handle = -1;
        }
    }
}
static void log_record_save_to_file(LOG_RECORD_S* record, const char* buf)
{
    /* 关闭日志写netlog的句柄 */
    if ( s_netlog_handle >= 0 )
    {
        printf("[%s:%d][CALL:%s] current output mode is file, close netlog handle(%d)\n", __FILE__, __LINE__, record->caller, s_netlog_handle);
        close(s_netlog_handle);
        s_netlog_handle = -1;
    }

    if ( s_logfile_handle < 0 )
    {
        s_logfile_handle = open(LOG_FILE_0, O_WRONLY | O_CREAT,0644);
        if ( s_logfile_handle < 0 )
        {
            printf("[%s:%d][CALL:%s] open("LOG_FILE_0") failed: %d<%s>\n", __FILE__, __LINE__, record->caller, errno, strerror(errno));
            return;
        }

        s_logfile_size = (int32_t)lseek(s_logfile_handle, 0, SEEK_END);
        s_logfile_size += write(s_logfile_handle, LOG_FILE_TAG, sizeof(LOG_FILE_TAG) - 1);
    }

    s_logfile_size += write(s_logfile_handle, buf, strlen(buf));
    if ( s_logfile_size > LOG_FILE_MAX_SIZE )
    {
        printf("[%s:%d][CALL:%s] s_logfile_size %dK Bytes, backup "LOG_FILE_0" to "LOG_FILE_1"\n", __FILE__, __LINE__, record->caller, s_logfile_size/1024);
        close(s_logfile_handle);
        s_logfile_handle = -1;

        rename(LOG_FILE_0, LOG_FILE_1);
        remove(LOG_FILE_0);

        s_logfile_handle = open(LOG_FILE_0, O_WRONLY | O_CREAT,0644);
        if ( s_logfile_handle < 0 )
        {
            printf("[%s:%d][CALL:%s] open("LOG_FILE_0") failed: %d<%s>\n", __FILE__, __LINE__, record->caller, errno, strerror(errno));
            return;
        }
        s_logfile_size = 0;
    }
}
static void log_record_crypto_save_to_file(LOG_RECORD_S* record, const char* buf)
{
    unsigned char* ciphertext = NULL;
    int ciphertext_len = 0;
    static long size = 0;
    //static int num = 0;
    /* 关闭日志写netlog的句柄 */
    if ( s_netlog_handle >= 0 )
    {
        printf("[%s:%d][CALL:%s] current output mode is file, close netlog handle(%d)\n", __FILE__, __LINE__, record->caller, s_netlog_handle);
        close(s_netlog_handle);
        s_netlog_handle = -1;
    }

    if ( s_logfile_handle < 0 )
    {
        s_logfile_handle = open(LOG_FILE_0, O_WRONLY | O_CREAT | O_APPEND, 0644);
        if ( s_logfile_handle < 0 )
        {
            printf("[%s:%d][CALL:%s] open("LOG_FILE_0") failed: %d<%s>\n", __FILE__, __LINE__, record->caller, errno, strerror(errno));
            return;
        }
        s_logfile_size = (int32_t)lseek(s_logfile_handle, 0, SEEK_END);

        //加密操作
        ciphertext_len = aes_256_ctr_encrypt(s_crypt_key, (const unsigned char*)LOG_FILE_TAG , strlen(LOG_FILE_TAG), &ciphertext, s_crypt_iv);
        s_logfile_size += write(s_logfile_handle, (const unsigned char*)CRYPTO_START_MARKER,strlen(CRYPTO_START_MARKER));
        s_logfile_size += write(s_logfile_handle, ciphertext, ciphertext_len);
        //s_logfile_size += write(s_logfile_handle, LOG_FILE_TAG, strlen(LOG_FILE_TAG));
        s_logfile_size += write(s_logfile_handle, (const unsigned char*)CRYPTO_END_MARKER, strlen(CRYPTO_END_MARKER));
        //num++;
        free(ciphertext);
        ciphertext = NULL;
        ciphertext_len = 0;

    }
    //加密操作
    ciphertext_len = aes_256_ctr_encrypt(s_crypt_key, (const unsigned char*)buf , strlen(buf), &ciphertext, s_crypt_iv);
    size = ciphertext_len + strlen(CRYPTO_START_MARKER) +strlen(CRYPTO_END_MARKER);
    if ( ( s_logfile_size + size) > LOG_FILE_MAX_SIZE )
    {
        printf("[%s:%d][CALL:%s] s_logfile_size %dK Bytes, backup "LOG_FILE_0" to "LOG_FILE_1"\n", __FILE__, __LINE__, record->caller, s_logfile_size/1024);
        close(s_logfile_handle);
        s_logfile_handle = -1;

        rename(LOG_FILE_0, LOG_FILE_1);
        remove(LOG_FILE_0);

        s_logfile_handle = open(LOG_FILE_0, O_WRONLY | O_CREAT | O_APPEND);
        if ( s_logfile_handle < 0 )
        {
            printf("[%s:%d][CALL:%s] open("LOG_FILE_0") failed: %d<%s>\n", __FILE__, __LINE__, record->caller, errno, strerror(errno));
            return;
        }
        s_logfile_size = 0;
    }
    //printf("ciphertext_size == %d \n",ciphertext_len);
    while( 0 != flock(s_logfile_handle, LOCK_EX)); 
    {
        usleep(100);
    }
    s_logfile_size += write(s_logfile_handle, (const unsigned char*)CRYPTO_START_MARKER,strlen(CRYPTO_START_MARKER));
    s_logfile_size += write(s_logfile_handle, ciphertext, ciphertext_len);
    //s_logfile_size += write(s_logfile_handle, buf,strlen(buf));
    s_logfile_size += write(s_logfile_handle, (const unsigned char*)CRYPTO_END_MARKER, strlen(CRYPTO_END_MARKER));
    //num++;
    //printf("num == %d\n",num);
    if( flock(s_logfile_handle, LOCK_UN) < 0 )
    {
        printf("flock LOCK_UN is error \n");
    }
    free(ciphertext);
    ciphertext = NULL;
    ciphertext_len = 0;
}

static int local_log_append_file(int src_fd, int dest_fd)
{
    unsigned char* buffer = NULL;
    size_t bytes_read;
    ssize_t log_file_size;

    log_file_size = (int32_t)lseek(src_fd, 0, SEEK_END);
    lseek(src_fd, 0, SEEK_SET);

    buffer = (unsigned char*)malloc(log_file_size);
    if(buffer == NULL)
    {
        printf("malloc buffer error\n");
        return -1;
    }

    while((bytes_read = read(src_fd,buffer,log_file_size)) > 0)
    {
        write(dest_fd,buffer,bytes_read);
    }

    free(buffer);
    buffer = NULL;

    return 0;

}
int32_t pi_decrypto_log_from_file(const char *in_filename, const char* out_filename)
{
    int32_t retval;
    if( (NULL == in_filename) || (NULL == out_filename) )
    {
        printf("input or output file is NULL\n");
        return -1;
    }
    retval = read_and_decrypt_logs(in_filename, s_crypt_key, s_crypt_iv, out_filename);
    if( 0 != retval )
    {
        printf("decrypto is fail \n");
        return retval;
    }
    return retval;
}


int32_t pi_crashlog_record_save_to_file(CRASH_LOG_DATA_S crash_log_data)
{
    int32_t log0_file = -1;
    int32_t log1_file = -1;

    if ( NULL != s_flush_crashlog_callback )
    {
        s_flush_crashlog_callback();
    }

    //打开用来保存crashlog的文件
    s_crashlog_handle = open(LOG_FILE_CRASH, O_WRONLY | O_CREAT, 0644);
    if( s_crashlog_handle < 0 )
    {
        printf("open pol_log_crash_log.log error \n");
        return -1;
    }

    pi_log_e("\n%s\n",crash_log_data.sig_handle);
    pi_log_e("no:%d\n",crash_log_data.sig_num);
    pi_log_e("name:%s\n",crash_log_data.sig_name);
    pi_log_e("errno:%d\n",crash_log_data.si_error);
    pi_log_e("code:%d\n",crash_log_data.si_code);
    pi_log_e("sender pid:%d\n",crash_log_data.si_pid_t);
    pi_log_e("sender uid:%d\n",crash_log_data.si_uid_t);
    pi_log_e("si_addr:%p\n",crash_log_data.si_addr_t);
    pi_log_e("si_fd:%d\n",crash_log_data.si_fd_t);
    if (NULL != crash_log_data.pInfo)
    {
        for (int i = 0; i < crash_log_data.size - 2; i++)
        {
            pi_log_e("%s\n",crash_log_data.pInfo[i]);
        }
    }
    //打开用来保存操作log的文件log2，并将内容写入到到crash_log
    log1_file = open(LOG_FILE_1, O_RDONLY);
    if( log1_file > 0 )
    {
        if( local_log_append_file(log1_file, s_crashlog_handle) )
        {
            printf("log1_file merge failed\n");
        }
        close(log1_file);
    }

    //打开用来保存操作log的文件log1，并将内容写入到到crash_log
    log0_file = open(LOG_FILE_0, O_RDONLY);
    if(log0_file > 0)
    {
        if(local_log_append_file(log0_file, s_crashlog_handle))
        {
            printf("log0_file merge failed\n");
        }
        close(log0_file);
    }
    close(s_crashlog_handle);

    printf("file merge successful\n");

    return 0;

}


static void log_record_output(LOG_RECORD_S* record)
{
    char    time_str[16];
    char    buf[1200];

    strftime(time_str, sizeof(time_str), "%H:%M:%S", localtime(&(record->record_time.tv_sec)));
    snprintf(buf, sizeof(buf), "[%s.%03ld] %-48s - [%s]%s", time_str, record->record_time.tv_usec / 1000, record->caller, record->lvl_tag, record->content);

    if ( s_output_mode_set == LOG_OUT_TO_CONSOLE )
    {
        log_record_send_to_console(record, buf);
    }
    else
    {
#ifdef CRYPTO_FILE
        log_record_crypto_save_to_file(record, buf);
#else
        log_record_save_to_file(record, buf);
#endif 
    }
}

static void* log_record_async_thread(void* arg)
{
    LOG_RECORD_S*   record;
    while ( 1 )
    {
        if ( pol_sem_wait(s_output_async_sem) != 0 )
        {
            printf("[%s:%d] pol_sem_wait failed: %d<%s>!!!\n", __FILE__, __LINE__, errno, strerror(errno));
            usleep(500 * 1000);
            continue;
        }

        pol_mutex_lock(s_output_async_mtx);
        record = pi_list_first_entry_or_null(&s_output_async_list, LOG_RECORD_S, record_list);
        if ( record == NULL )
        {
            printf("[%s:%d] semaphore active, but list is empty!!!\n", __FILE__, __LINE__);
        }
        else
        {
            log_record_output(record);
            pi_list_del_entry(&(record->record_list));
            pi_free(record);
        }
        pol_mutex_unlock(s_output_async_mtx);
    }

    return NULL;
}

static void log_record_handing(int32_t level, const char* func, const char* format, va_list arg)
{
    LOG_RECORD_S*   record = NULL;
    char*           ptr;
    size_t          len;

    RETURN_IF(level <= LOG_LVL_OFF || level > s_output_lvl_set, pi_none);

    record = (LOG_RECORD_S *)pi_zalloc(sizeof(LOG_RECORD_S));
    RETURN_SHOW_CALLER_IF(record == NULL, pi_err, func);

    ptr = strchr(func, ':');
    if ( ptr != NULL )
    {
        while ( ptr > func && *ptr != ' ' )
        {
            ptr--;
        }
        func = ptr + 1;
    }

    ptr = strchr(func, '(');
    len = ((ptr == NULL) ? strlen(func) : (ptr - func));
    if ( len >= sizeof(record->caller) )
    {
        snprintf(record->caller, sizeof(record->caller), "%s.", func);
    }
    else
    {
        snprintf(record->caller, len + 1, "%s", func);
    }
    vsnprintf(record->content, sizeof(record->content), format, arg);
    gettimeofday(&(record->record_time), NULL);
    record->lvl_tag = s_output_lvl_tag[level];

    pol_mutex_lock(s_output_async_mtx);
    pi_list_add_tail(&(record->record_list), &s_output_async_list);
    pol_mutex_unlock(s_output_async_mtx);
    pol_sem_post(s_output_async_sem);
}

void log_fatal(const char* func, const char* format, ...)
{
    va_list arg;

    va_start(arg, format);
    log_record_handing(LOG_LVL_FATAL, func, format, arg);
    va_end(arg);

    abort();
}

void log_error(const char* func, const char* format, ...)
{
    va_list arg;

    va_start(arg, format);
    log_record_handing(LOG_LVL_ERROR, func, format, arg);
    va_end(arg);
}

void log_warn(const char* func, const char* format, ...)
{
    va_list arg;

    va_start(arg, format);
    log_record_handing(LOG_LVL_WARN, func, format, arg);
    va_end(arg);
}

void log_info(const char* func, const char* format, ...)
{
    va_list arg;

    va_start(arg, format);
    log_record_handing(LOG_LVL_INFO, func, format, arg);
    va_end(arg);
}

void log_debug(const char* func, const char* format, ...)
{
    va_list arg;

    va_start(arg, format);
    log_record_handing(LOG_LVL_DEBUG, func, format, arg);
    va_end(arg);
}

void log_trace(const char* func, const char* format, ...)
{
    va_list arg;

    va_start(arg, format);
    log_record_handing(LOG_LVL_TRACE, func, format, arg);
    va_end(arg);
}

#define UART_SET_NAME "/sys/class/debug_uart_control/uart_control/debug_mode_set"
#define UART_ENABLE     1
#define UART_DISABLE    0

int uart_set(uint8_t _value)
{
    char value[32] = {0};
    int ret;
    snprintf(value, sizeof(value), "%d", _value);
    int fd = open(UART_SET_NAME, O_RDWR);
    if( fd < 0)
    {
        printf(" open uart_set_name file error \n");
        return -1;
    }

    ret =write(fd, &value, sizeof(value)); 
    if(ret < 0)
    {
        printf(" uart_set_name file write error \n");
        close(fd);
        return -1;
    }
    close(fd);
    return 0;
}
void pi_log_set_level(int32_t level)
{
    switch(level)
    {
        case 0:
            pi_runcmd(NULL, 0, 0, "rmmod netlog ");        
            log_set_level(LOG_LVL_OFF);
            break;
        case 1:
            log_set_level(LOG_LVL_DEBUG);
            pi_runcmd(NULL, 0, 0, "modprobe netlog  decrypt=1");
            break;
        case 2:
            log_set_level(LOG_LVL_DEBUG);
            log_set_mode(LOG_OUT_TO_FILE);
        break;
        default:
            printf("param no function \n");
            break;
    }
}

void log_set_level(int32_t level)
{
    if ( level < LOG_LVL_OFF )
    {
        s_output_lvl_set = LOG_LVL_OFF;
    }
    else if ( level > LOG_LVL_MAX )
    {
        s_output_lvl_set = LOG_LVL_MAX;
    }
    else
    {
        s_output_lvl_set = level;
    }
    printf("set output level<%u><%s>\n", s_output_lvl_set, s_output_lvl_tag[s_output_lvl_set]);
}
void log_show_level(void)
{
    printf("output level<%u><%s>\n", s_output_lvl_set, s_output_lvl_tag[s_output_lvl_set]);
}

void log_set_mode(int32_t mode)
{
    if ( mode == LOG_OUT_TO_CONSOLE )
    {
        s_output_mode_set = LOG_OUT_TO_CONSOLE;
    }
    else
    {
        if ( HAS_NO_FILE(LOG_FILE_PATH) )
        {
            printf("create file path(%s)\n", LOG_FILE_PATH);
            mkdir(LOG_FILE_PATH, 0666);
        }
        s_output_mode_set = LOG_OUT_TO_FILE;
    }
    printf("set output mode<%u><%s>\n", s_output_mode_set, s_output_mode_tag[s_output_mode_set]);
}

void log_set_flush_crashlog_callback(void (*callback)(void))
{
    s_flush_crashlog_callback = callback;
}

void log_show_mode(void)
{
    printf("output mode<%u><%s>\n", s_output_mode_set, s_output_mode_tag[s_output_mode_set]);
}

void __attribute__((constructor)) pol_log_constructor(void)
{
    s_output_async_sem = pol_sem_create(0);
    if ( s_output_async_sem == NULL )
    {
        printf("[%s:%d] create semaphore failed: %d<%s>!!!\n", __FILE__, __LINE__, errno, strerror(errno));
        return;
    }

    s_output_async_mtx = pol_mutex_create();
    if ( s_output_async_mtx == NULL )
    {
        printf("[%s:%d] create mutex failed: %d<%s>!!!\n", __FILE__, __LINE__, errno, strerror(errno));
        return;
    }

    s_output_async_tid = pol_thread_create(log_record_async_thread, 0x80000, NULL, 50, NULL, "log_record_async_thread");
    if ( s_output_async_tid == NULL )
    {
        printf("[%s:%d] create thread failed: %d<%s>!!!\n", __FILE__, __LINE__, errno, strerror(errno));
        return;
    }
    if ( HAS_NO_FILE(LOG_FILE_PATH) )
    {
        printf("create file path(%s)\n", LOG_FILE_PATH);
        mkdir(LOG_FILE_PATH, 0666);
    }
    printf("pol log construct successfully\n");
}

void __attribute__((destructor)) pol_log_destructor(void)
{
    if ( s_output_async_tid != NULL )
    {
        pol_thread_destroy(s_output_async_tid);
        s_output_async_tid = NULL;
    }

    if ( s_output_async_mtx != NULL )
    {
        pol_mutex_destroy(s_output_async_mtx);
        s_output_async_mtx = NULL;
    }

    if ( s_output_async_sem != NULL )
    {
        pol_sem_destroy(s_output_async_sem);
        s_output_async_sem = NULL;
    }
}
/**
 *@}
 */
