/**
 * @Copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file usbdservice.h
 * @addtogroup usb
 * @{
 * @addtogroup usbdservice
 * <AUTHOR>
 * @date 2014-11-24
 * @brief 		API to Generic USB Device End-Point server.  Allows applications to register
                callback functions to a single server thread which polls the registered
                endpoint for data and calls the callback when a connection is made and
                data is available
 */
#ifndef USBDSERVICE_H
#define USBDSERVICE_H 1

#include "qio/qioctl.h"
#include "qio/qio.h"
#include "printer_param.h"

/* usbd endpoint numbers*/
#define USB_PRT_INTERFACE           1
#define USB_SCAN_INTERFACE          2
#define USB_FAX_INTERFACE           3
#define USB_EWS_INTERFACE           4
#define USBD_PRINTIPP1_ENDPOINT  5
#define USBD_PRINTIPP2_ENDPOINT  6
#define USBD_PRINTIPP3_ENDPOINT  7
#define USBD_PRINTIPP4_ENDPOINT  8
//#define USBD_SERIAL_ENDPOINT       9// 8


#ifndef USBDSERVICE_STACK_SIZE
    #define USBDSERVICE_STACK_SIZE PI_LARGE_STACK ///< STACK SIZE
#endif/* USBDSERVICE_STACK_SIZE */
#ifndef USBDSERVICE_TASK_PRIORITY
    #define USBDSERVICE_TASK_PRIORITY PI_MEDIUM_PRIORITY ///< TASK PRIORITY
#endif/* USBDSERVICE_TASK_PRIORITY */

struct tag_usbdconnection;
typedef int (*PUSBDSERVICECB)(struct tag_usbdconnection* pnc);
/**
 * @brief 	Instantiation of one USB Device Service Connection
 */
typedef struct tag_usbdconnection
{
    PUSBDSERVICECB  pcb;            ///< connection handler function, this connection
 //   TsThread        taskID;         ///< thread id
 //   TsMailbox       mailBox;        ///< mailbox to wait on to start handling connection
 //   uint32_t          stack[USBDSERVICE_STACK_SIZE/4]; ///< task stack

    int             driver;			///< pointer for usb fd
    void*           service;		///< pointer for Return function
    struct tag_usbdconnection*
                    next;
}
USBDCONNECTION_S, *USBDCONNECTION_P;

/**
 * @brief 	Called to indicate to the server that the connection passed to the
			application in a connect callback is no longer being used
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
void* 		pi_usbd_server_end_connection	   (USBDCONNECTION_P pnc);
/**
 * @brief 	Called to indicate to the server that the connection passed to the
			application in a connect callback is no longer being used
 * @param[in] pnc 	:Instantiation of one USB Device Service Connection
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int         _pi_usbd_server_end_connection     (USBDCONNECTION_P pnc);
/**
 * @brief 	one-time initialization of the usb device server system
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int         pi_usbd_server_prolog            (void);
/**
 * @brief  	close/reset the usb device server system
 * <AUTHOR> Qi
 * @date  	2023-5-15
 */
void        pi_usbd_server_epilog            (void);
/**
 * @brief  	status manage call this function to update printer status to PC throw USB EP0
 * <AUTHOR> Qi
 * @date  	2023-5-15
 */
void        pi_usbd_server_update_printer_status(STATIC_STATUS_S *pstatic_status, DYNAMIC_STATUS_S *DYNAMIC_STATUS_P);
/**
 * @brief  	status manage call this function to update printer calibration to PC throw USB EP0
 * <AUTHOR> Qi
 * @date  	2023-5-15
 */
void        pi_usbd_server_update_printer_calibration_data(TRC_INFO_S *ptrc_info);
/**
 * @brief 	push scan cmd
 * @param[in] cmd_buffer 	:cmd buffer size
 * @param[in] cmd_len 	:cmd buffer len
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int         pi_usbd_server_push_scan_cmd( char *cmd_buffer, int cmd_len );
/**
 * @brief 	clear push scan cmd send to host
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int         pi_usbd_server_clear_pusb_scan_cmd( void );
/**
 * @brief  	clean flag
 * <AUTHOR> Qi
 * @date  	2023-5-15
 */
void        pi_usbd_set_usb_clean_flag(QIO_P pqio, int clean_flag);
/**
 * @brief 	clear push scan cmd send to host
 * @param[in] endpoint 	:endpoint service was on
 * @param[in] buffer 	:buffer size
 * @param[in] buffer 	:buffer len
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int         pi_usbd_server_push_raw_data(int endpoint, char *buffer, int len);
/**
 * @brief 	state of plug on
 * @param[in] endpoint 	:endpoint service was on
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int 		pi_usbd_server_state_plug_on( int endpoint );
/**
 * @brief  	update user list
 * <AUTHOR> Qi
 * @date  	2023-5-15
 */
void        pi_usbd_server_update_user_list(char *UserList,int len);
#if IN_ISOLATOR
/**
 * @brief 	get partition status 
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int 		pi_usbd_server_get_partition_status(void);
#endif/* IN_ISOLATOR */

/**
 * @brief  	used to transfer audit information between software and firmware
 * <AUTHOR> Qi
 * @date  	2023-5-15
 */
void pi_usbd_server_update_audit_info(AUDIT_JOBS_INFO_S *audit_info);

/**
 * @brief 	get ippusb driver
 * @param[in] pusbdc    :Instantiation of one USB Device Service Connection
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2024-3-19
 */
int pi_usbd_server_get_hander(USBDCONNECTION_P pusbdc);

/**
 * @brief 	init ippusb 1~4
 * @param[in] callback	:function to call when device is connected
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2024-3-19
 */
int pi_usbd_server_init_ippusb(PUSBDSERVICECB callback);

/**
 * @brief  	release ippusb 1~4
 * <AUTHOR> Qi
 * @date  	2024-3-19
 */
void pi_usbd_server_release_ippusb();

/**
 * @brief 	device plug
 * @return  Construct result
 * @retval 	=0 : success\n
 *         	<0 : fail
 * <AUTHOR> Qi
 * @date   	2023-5-15
 */
int 		pi_usbd_device_plug( void );

#endif /* USBDSERVICE_H */


/**
 * @}
 */
