
/**************************************************************
  Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
  module name :  	POL (PANTUM OS LAYER)  selftest 
  file   name :	io_test.c 
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-27
description	:   selftest  for io system interface 
 ****************************************************************/
#define _GNU_SOURCE
#include <assert.h>
#include "pol/pol_time.h"
#include "pol_test.h"

#define TIME_ERR_HINT "[TIME-TEST]"

static int32_t should_return_negative_number_if_ctime_r_err()
{
	char buf[36];

	char *point = pi_ctime_r(NULL, buf);
	if (point == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(point == NULL);

	return PASS;
}

static int32_t should_return_positive_number_if_ctime_r_ok()
{
	time_t curtime;

	char buf[36];

	char *point = pi_ctime_r(&curtime, buf);
	if (point == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(point != NULL);
	printf("should_return_positive_number_if_ctime_r_ok\n");
	return PASS;
}

static int32_t should_return_negative_number_if_asctime_r_err()
{
	struct tm time;
	char buf[128];

	char *point = pi_asctime_r(NULL, buf);
	if (point == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(point == NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_asctime_r_ok()
{
	struct tm time;
	char buf[128];

	char *point = pi_asctime_r(&time, buf);
	if (point == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(point != NULL);
	
	return PASS;
}

static int32_t should_return_negative_number_if_gmtime_r_err()
{
	struct tm result;

	char *point = pi_gmtime_r(NULL, &result);
	if (point == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(point == NULL);

	return PASS;
}

static int32_t should_return_positive_number_if_gmtime_r_ok()
{
	struct tm result;
	time_t time;

	char *point = pi_gmtime_r(&time, &result);
	if (point == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(point != NULL);
	
	return PASS;
}

static int32_t should_return_negative_number_if_localtime_r_err()
{
	struct tm result;
	time_t time;

	struct tm *ret = pi_localtime_r(NULL, NULL);
	if (ret == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(ret == NULL);

	return PASS;
}

static int32_t should_return_positive_number_if_localtime_r_ok()
{
	struct tm result;
	time_t time;

	struct tm *ret = pi_localtime_r(&time, &result);
	if (ret == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(ret != NULL);

	return PASS;

}

static int32_t should_return_negative_number_if_strftime_err()
{
	char buf[128];
	struct tm time;

	size_t ret = pi_strftime(NULL, 0, "%Y-%m-%d %H:%M:%S", &time);
	if (ret == 0) {
		perror(TIME_ERR_HINT);
	}

	assert(ret == 0);
	return PASS;
}

static int32_t should_return_positive_number_if_strftime_ok()
{
	char buf[128];
	struct tm *info;
	time_t num;
	struct tm result;

	time_t seconds = pi_time(&num);
	if (seconds == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(seconds != -1);

	info = pi_localtime_r(&num, &result);
	if (info == NULL) {
		perror(TIME_ERR_HINT);
	}
	assert(info != NULL);

	size_t ret = pi_strftime(buf, sizeof(buf)/sizeof(buf[0]), "%Y-%m-%d %H:%M:%S", info);
	if (ret == 0) {
		perror(TIME_ERR_HINT);
	}
	assert(ret != 0);
	return PASS;

}

static int32_t should_return_negative_number_if_time_err()
{
#if 0
	time_t time;

	time_t seconds = pi_time((time_t *)0xFFFFFFFF);
	if (seconds == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(seconds == -1);
#endif
	return PASS;
}

static int32_t should_return_positive_number_if_time_ok()
{
	time_t num;

	time_t seconds = pi_time(&num);
	if (seconds == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(seconds != -1);

	return PASS;
}



static int32_t should_return_negative_number_if_mktime_err()
{

	time_t ret = pi_mktime(NULL);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret == -1);

	return PASS;
}

static int32_t should_return_positive_number_if_mktime_ok()
{
	struct tm tm;

	time_t ret = pi_mktime(&tm);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret != -1);

	return PASS;
}

static int32_t should_return_negative_number_if_clock_err()
{
	clock_t ret = pi_clock();
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret == -1);

	return PASS;
}

static int32_t should_return_positive_number_if_clock_ok()
{
	clock_t ret = pi_clock();
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret != -1);

	return PASS;
}

static int32_t should_return_negative_number_if_clock_gettime_err()
{
	struct timespec tp;
	clockid_t clk_id = -1;

	int32_t ret = pi_clock_gettime(clk_id, &tp);
	//printf("%d\n", CLOCK_REALTIME);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}

	assert(ret == -1);
	return PASS;
}

static int32_t should_return_positive_number_if_clock_gettime_ok()
{
	struct timespec tp;
	clockid_t clk_id = 0;

	int32_t ret = pi_clock_gettime(clk_id, &tp);
	//printf("%d\n", CLOCK_REALTIME);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}

	assert(ret != -1);

	return PASS;
}

static int32_t should_return_negative_number_if_clock_settime_err()
{
	struct timespec tp;
	clockid_t clk_id = -1;

	int32_t ret = pi_clock_settime(clk_id, &tp);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}

	assert(ret == -1);

	return PASS;
}

static int32_t should_return_positive_number_if_clock_settime_ok()
{
	struct timespec tp;
	clockid_t clk_id = 0;

	tp.tv_sec = 0;
	tp.tv_nsec = 0;

	int32_t ret = pi_clock_settime(clk_id, &tp);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}

	assert(ret != -1);

	return PASS;

}

static int32_t should_return_negative_number_if_msleep_err()
{
	useconds_t usec = 2000000;

    int32_t ret = pi_msleep(usec);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret == -1);

	return PASS;
}

static int32_t should_return_positive_number_if_msleep_ok()
{
	useconds_t usec = 10;

    int32_t ret = pi_msleep(usec);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret != -1);

	return PASS;
}

static int32_t should_return_negative_number_if_usleep_err()
{

	useconds_t usec = 2000000;

    int32_t ret = pi_usleep(usec);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret == -1);

	return PASS;
}

static int32_t should_return_positive_number_if_usleep_ok()
{
	useconds_t usec = 10;

    int32_t ret = pi_usleep(usec);
	if (ret == -1) {
		perror(TIME_ERR_HINT);
	}
	assert(ret != -1);

	return PASS;
}

static int32_t should_return_negative_number_if_nanosleep_err()
{
	struct timespec req, rem;

    req.tv_sec = -1;
    req.tv_nsec = 0xffffffff;

	int32_t ret = pi_nanosleep(&req, &rem);
	if (ret != 0) {
		perror(TIME_ERR_HINT);
	}

	assert(ret != 0);

	return PASS;
}

static int32_t should_return_positive_number_if_nanosleep_ok()
{
	struct timespec req, rem;

    req.tv_sec = 1;
    req.tv_nsec = 100;

	int32_t ret = pi_nanosleep(&req, &rem);
	if (ret != 0) {
		perror(TIME_ERR_HINT);
	}

	assert(ret == 0);

	return PASS;
}



TEST_ITEM_S time_test_pool[] = {
	{"pi_ctime_r_err", should_return_negative_number_if_ctime_r_err},
	{"pi_ctime_r", should_return_positive_number_if_ctime_r_ok},
	{"pi_asctime_r_err", should_return_negative_number_if_asctime_r_err},
	{"pi_asctime_r", should_return_positive_number_if_asctime_r_ok},
	{"pi_gmtime_r_err", should_return_negative_number_if_gmtime_r_err},
	{"pi_gmtime_r", should_return_positive_number_if_gmtime_r_ok},
	{"pi_localtime_r_err", should_return_negative_number_if_localtime_r_err},
	{"pi_localtime_r", should_return_positive_number_if_localtime_r_ok},
	{"pi_strftime_err", should_return_negative_number_if_strftime_err},
	{"pi_strftime", should_return_positive_number_if_strftime_ok},
	{"pi_time_err", should_return_negative_number_if_time_err},
	{"pi_time", should_return_positive_number_if_time_ok},
	{"pi_mktime_err", should_return_negative_number_if_mktime_err},
	{"pi_mktime", should_return_positive_number_if_mktime_ok},
	{"pi_clock_err", should_return_negative_number_if_clock_err},
	{"pi_clock", should_return_positive_number_if_clock_ok},
	{"pi_clock_gettime_err", should_return_negative_number_if_clock_gettime_err},
	{"pi_clock_gettime", should_return_positive_number_if_clock_gettime_ok},
	{"pi_clock_settime_err", should_return_negative_number_if_clock_settime_err},
	{"pi_clock_settime", should_return_positive_number_if_clock_settime_ok},
	{"pi_msleep_err", should_return_negative_number_if_msleep_err},
	{"pi_msleep", should_return_positive_number_if_msleep_ok},
	{"pi_usleep_err", should_return_negative_number_if_usleep_err},
	{"pi_usleep", should_return_positive_number_if_usleep_ok},
	{"pi_nanosleep_err", should_return_negative_number_if_nanosleep_err},
	{"pi_nanosleep", should_return_positive_number_if_nanosleep_ok},
};

TEST_POOL_S time_pool = {
    "time",
    time_test_pool,
    sizeof(time_test_pool)/sizeof(TEST_ITEM_S),
};

