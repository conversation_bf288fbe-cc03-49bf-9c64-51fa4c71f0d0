/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file jobinfo_storage.h
 * @addtogroup system_manager
 * @{
 * @brief Job information storage. 
 * <AUTHOR> 
 * @version 1.0
 * @date 2024-05-28
 */

#ifndef _JOBINFO_STORAGE_H
#define _JOBINFO_STORAGE_H

#include "job_common.h"

enum QUEUE_TYPE
{
    T_DELAY = 0,
    T_SAMPLE ,
    T_PINCODE ,
    T_MAX
};

int jobinfo_storage_init(unsigned int *jobid , EVT_MGR_CLI_S *client);

int jobinfo_storage_destroy(void);

int jobinfo_storage_write(unsigned int jobid , void *data , unsigned int size);

int jobinfo_storage_read(unsigned int jobid , void *data , unsigned int size);

unsigned int jobinfo_storage_get_history_records(void *context , int(*callback)(const JOBINFO_S* job , void *context, uint32_t size));

void jobinfo_storage_clear_history_records(void);

unsigned int jobinfo_storage_get_queue_records(enum QUEUE_TYPE type , void *context , void(*callback)(const JOBINFO_S* job , void *context));

int jobinfo_storage_get_queue_record(unsigned int jobid);

int jobinfo_storage_remove_queue_record(unsigned int jobid , JOBINFO_S *jobinfo);

void jobinfo_storage_remove_queue_records(enum QUEUE_TYPE type);

int jobinfo_storage_remove_job_and_file(unsigned int jobid);

#endif //_JOBINFO_STORAGE_H

/**
 * @}
 */
