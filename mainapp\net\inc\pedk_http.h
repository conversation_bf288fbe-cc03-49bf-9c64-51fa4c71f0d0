/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pedk_http.h
 * @pedk net
 * @{
 * @http netmodules
 * <AUTHOR>
 * @date 2024-12-24
 * @brief pedk http function for pedk
 */
#ifndef _PEDK_HTTP_H_
#define _PEDK_HTTP_H_

#include <curl/curl.h>

// HTTPS 配置参数结构体
typedef struct {
    int verify_certificate;      // 0: 不验证 (默认)，1: 验证证书
    int verify_host_mode;        // 0: 不验证 (默认)，1: 验证主机名
    char client_cert_path[256];  // 客户端证书路径
    char client_key_path[256];   // 客户端私钥路径
    char key_password[128];      // 私钥密码
} HTTPS_CONFIG_PARAM_S;

// 证书安装结构体
typedef struct {
    char url[256];
    char headers[2048];
    char file_name_prefix[256];
	char custom_field[1024];

} SCAN_HTTP_PARAM_S;

// 设置和获取 HTTPS 配置参数的接口
int pedk_http_set_https_config(const HTTPS_CONFIG_PARAM_S *config);
int pedk_http_get_https_config(HTTPS_CONFIG_PARAM_S *config);

int pedk_http_set_url_header_params(SCAN_HTTP_PARAM_S *scan_http_param);
int pedk_http_get_url_header_params(SCAN_HTTP_PARAM_S *scan_http_param);

#endif /* _PEDK_HTTP_H_ */
/**
 *@}
 */
