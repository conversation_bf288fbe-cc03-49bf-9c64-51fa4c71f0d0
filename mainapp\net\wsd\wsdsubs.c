#include "soap.h"
#include "wsdef.h"
#include "wsd.h"
#include "soap.h"
#include "wsdsubs.h"
#include "wsdservice.h"
#include "nettypes.h"
#include "pol/pol_mem.h"
#include "pol/pol_threads.h"
#include <string.h>
#include <stdio.h>
#include <curl/curl.h>
#include <curl/easy.h>
#include <errno.h>

#define WSD_MAX_SUBSCRIPTIONS       64

#define SOAP_NOTIFICATION_HEADER                       \
    "<soap:Header>\r\n"                                  \
    "    <wsa:To>%s</wsa:To>\r\n"                        \
    "    <wsa:Action>%s</wsa:Action>\r\n"                \
    "    <wse:Identifier>%s</wse:Identifier>\r\n"        \
    "</soap:Header>\r\n"

typedef struct wsd_event_table
{
    const char* name;
    WSD_EVT_E   event;
}
WSD_EVT_TAB_S;

typedef struct wsd_subscription
{
    char               identifier[WSD_MAX_FIELD_SIZE]; ///< id handed to client for this subcription
    char               destination[WSD_MAX_HTTP_URL];      ///< address of event notification (i.e. subscriber)
    char               filter[WSD_MAX_HTTP_URL];           ///< orginal subscription filter in request
    WSD_EVT_E          event;                          ///< the thing we are subscribing to
    WSD_EVT_E          xevent;                         ///< source/cause of event trigger (psuedo-event)
    time_t             subscibed;                      ///< time when subscription was created
    time_t             expires;                        ///< time when subscription expires
    int                push;                           ///< set non-0 to indicate event should be sent/triggered
}
WSD_SUBS_S;

typedef struct wsd_subscription_list
{
    PI_MUTEX_T  mutex;  ///< mutex lock
    WSD_SUBS_S  subscriptionPool[WSD_MAX_SUBSCRIPTIONS]; ///< pool of event subscriptions
    WSD_SUBS_S* pool_refs[WSD_MAX_SUBSCRIPTIONS];
    int         free_ref_index;
}
WSD_SUBS_LIST_S;

static const WSD_EVT_TAB_S s_evtab[] =
{
    {   "PrinterElementsChangeEvent",           evtPrinterChanged       },
    {   "PrinterStatusSummaryEvent",            evtPrinterStatus        },
    {   "PrinterStatusConditionEvent",          evtPrinterError         },
    {   "PrinterStatusConditionClearedEvent",   evtPrinterErrorCleared  },
#if CONFIG_SCAN
    {   "ScanAvailableEvent",                   evtScanAvailable        },
    {   "ScannerElementsChangeEvent",           evtScannerChanged       },
    {   "ScannerStatusSummaryEvent",            evtScannerStatus        },
    {   "ScannerStatusConditionEvent",          evtScannerError         },
    {   "ScannerStatusConditionClearedEvent",   evtScannerErrorCleared  },
#endif /* CONFIG_SCAN */
    {   "JobStatusEvent",                       evtJobStatus            },
    {   "JobEndStateEvent",                     evtJobEndState          },
    {   "None",                                 evtNone                 }
};

typedef int32_t (*SUBS_HANELDR)(QXML_S* pxml, const char* id, WSD_SERVICE_DATA_S* srv_data, WSD_SUBS_LIST_S* subs_list);

static int32_t wsd_curl_send(const char* url, const char* xmlnsurl, const char* action, const char* body)
{
    struct curl_slist*  headers = NULL;
    CURL*               curl;
    CURLcode            rc;
    const char*         ifname;
    char                soap_action_header[WSD_MAX_HTTP_URL];

    RETURN_VAL_IF(!url || !action || !body, NET_WARN, -1);

    curl = curl_easy_init();
    headers = curl_slist_append(headers, "Content-Type: " MIME_TYPE_SOAP_XML);
    headers = curl_slist_append(headers, "Accept: application/soap+xml,text/xml,text/html");
    headers = curl_slist_append(headers, "Connection: close");

    snprintf(soap_action_header, sizeof(soap_action_header), "SOAPAction: \"%s#%s\"", xmlnsurl, action);
    headers = curl_slist_append(headers, soap_action_header);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

    curl_easy_setopt(curl, CURLOPT_POST, 1L);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, body);

    curl_easy_setopt(curl, CURLOPT_URL, url);

    if (strstr(url, "[fe80::"))
    {
        for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
        {
#ifdef CONFIG_NET_WIFI
            if ( ifid == IFACE_ID_WFD )
            {
                continue;
            }
#endif
            ifname = IFACE_NAME(ifid);
            if ( !STRING_IS_EMPTY(ifname) )
            {
                curl_easy_setopt(curl, CURLOPT_ADDRESS_SCOPE, if_nametoindex(ifname));
                break;
            }
        }
    }
    curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 5);
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);

    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1);
    curl_easy_setopt(curl, CURLOPT_MAXREDIRS, 1);
    curl_easy_setopt(curl, CURLOPT_CONNECTTIMEOUT, 5);

    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

    rc = curl_easy_perform(curl);
    if ( rc != CURLE_OK )
    {
        NET_DEBUG("send to %s failed, curl err(%s)", url, curl_easy_strerror(rc));
    }

    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    return 0;
}

static const char* translate_event_from_event(WSD_EVT_E event)
{
    int i;

    FOREACH_ARRAY(s_evtab)
    {
        if (event == s_evtab[_i].event)
        {
            return s_evtab[_i].name;
        }
    }
    return "??";
}

static WSD_EVT_E translate_event_from_name(const char *eventName)
{
    int i;

    FOREACH_ARRAY(s_evtab)
    {
        if (strstr(eventName, s_evtab[_i].name))
        {
            return s_evtab[_i].event;
        }
    }
    return evtNone;
}

int32_t wsd_subs_send_event(WSD_SUBS_LIST_S* subs_list, WSD_EVT_E evt, SOAP_VAR_S* pvar, const char* xmlns, const char* xmlnsurl)
{
    WSD_SUBS_S* pevs;
    char        header[LEN(SOAP_NOTIFICATION_HEADER) + 3 * WSD_MAX_FIELD_SIZE + UUID_LEN];
    char        identifier[WSD_MAX_FIELD_SIZE];     ///< id handed to client for this subcription
    char        destination[WSD_MAX_HTTP_URL];      ///< address of event notification (i.e. subscriber)
    char        filter[WSD_MAX_HTTP_URL];           ///< orginal subscription filter in request
    char        *body;
    const char  *action;
    int32_t     rc = -1;
    time_t      now;
    size_t      i = 0;

    body = (char *)pi_zalloc(SOAP_MAX_RESPONSE);
    if (body == NULL)
    {
        NET_WARN("alloc SOAP body failed: %s", strerror(errno));
        return -1;
    }

    time(&now);
    while (i < subs_list->free_ref_index)
    {
        pevs = subs_list->pool_refs[i];
        if ( pevs->expires < now )
        {
            WSD_SUBS_S* tmp = subs_list->pool_refs[i];
            subs_list->pool_refs[i] = subs_list->pool_refs[--subs_list->free_ref_index];
            subs_list->pool_refs[subs_list->free_ref_index] = tmp;
            continue;
        }

        if ( subs_list->pool_refs[i]->event == evt )
        {
            snprintf( header, sizeof(header), SOAP_NOTIFICATION_HEADER,
                      pevs->destination, pevs->filter, pevs->identifier);
            action = translate_event_from_event(evt);
            rc = soap_format_response( pvar, WSD_NAME_SPACES, action, header,
                                       xmlns, xmlnsurl, body, SOAP_MAX_RESPONSE );
            if (rc)
            {
                pi_free(body);
                return -1;
            }
            NET_INFO("Try to send %s event to %s", translate_event_from_event(evt), pevs->destination);
            wsd_curl_send(pevs->destination, xmlnsurl, action, body);
        }
        ++i;
    }
    return 0;
}


#define SUBS_HANDLER_DEFINITION(_name) static int32_t _name(QXML_S* pxml, const char* id, WSD_SERVICE_DATA_S* srv_data, WSD_SUBS_LIST_S* subs_list)

typedef struct subs_handler_table
{
    const char*    action_name;
    SUBS_HANELDR   handler;
}
SUBS_HANDLER_TBL_S;

WSD_SUBS_LIST_S* wsd_subs_list_create()
{
    WSD_SUBS_LIST_S* subs_list = pi_zalloc(sizeof(*subs_list));
    RETURN_VAL_IF(subs_list == NULL, NET_WARN, NULL);

    subs_list->mutex = pi_mutex_create();
    if ( subs_list->mutex == INVALIDMTX )
    {
        pi_free(subs_list);
        return NULL;
    }

    FOREACH_ARRAY(subs_list->subscriptionPool)
    {
        subs_list->pool_refs[_i] = &subs_list->subscriptionPool[_i];
    }
    return subs_list;
}

void wsd_subs_list_destroy(WSD_SUBS_LIST_S* subs_list)
{
    RETURN_IF(subs_list == NULL, NET_DEBUG);
    if ( subs_list->mutex != INVALIDMTX )
    {
        pi_mutex_destroy(subs_list->mutex);
    }
    pi_free(subs_list);
}

static void subscription_init( WSD_SUBS_S* pevs, const char *id, const char *address, const char *filter)
{
    snprintf(pevs->identifier, sizeof(pevs->identifier), "%s", id);
    snprintf(pevs->destination, sizeof(pevs->destination), "%s", address);
    snprintf(pevs->filter, sizeof(pevs->filter), "%s", filter);
    pevs->push = 0;
}

static WSD_SUBS_S* subslist_find(WSD_SUBS_LIST_S* subs_list, const char *id)
{
    RETURN_VAL_IF(subs_list == NULL || id == NULL, NET_DEBUG, NULL);
    struct list_head *pos;
    WSD_SUBS_S* pevs = NULL;

    pi_mutex_lock(subs_list->mutex);
    for (size_t i = 0; i < subs_list->free_ref_index; i++)
    {
        if ( !strcmp(subs_list->pool_refs[i]->identifier, id) )
        {
            pevs = subs_list->pool_refs[i];
            break;
        }
    }
    pi_mutex_unlock(subs_list->mutex);
    return pevs;
}

static void subslist_delete(WSD_SUBS_LIST_S* subs_list, const char* id)
{
    WSD_SUBS_S* pevs;
    struct list_head *pos;
    RETURN_IF(subs_list == NULL || id == NULL, NET_DEBUG);

    pi_mutex_lock(subs_list->mutex);
    for (size_t i = 0; i < subs_list->free_ref_index; i++)
    {
        if ( !strcmp(subs_list->pool_refs[i]->identifier, id) )
        {
            subs_list->pool_refs[i]->push = 0;
            WSD_SUBS_S* tmp = subs_list->pool_refs[i];
            subs_list->pool_refs[i] = subs_list->pool_refs[--subs_list->free_ref_index];
            subs_list->pool_refs[subs_list->free_ref_index] = tmp;
            break;
        }
    }
    pi_mutex_unlock(subs_list->mutex);
}

static WSD_SUBS_S* subslist_get(WSD_SUBS_LIST_S* subs_list)
{
    RETURN_VAL_IF(subs_list == NULL, NET_DEBUG, NULL);
    WSD_SUBS_S* pevs = NULL;

    pi_mutex_lock(subs_list->mutex);
    if (subs_list->free_ref_index >= sizeof(subs_list->pool_refs))
    {
        NET_DEBUG("No available subscriptions");
    }
    else
    {
        pevs = subs_list->pool_refs[subs_list->free_ref_index++];
    }
    memset(pevs, 0, sizeof(WSD_SUBS_S));
    pi_mutex_unlock(subs_list->mutex);

    return pevs;
}

SUBS_HANDLER_DEFINITION(wsd_evt_subscribe_handler) {
    QXML_S            xml;
    SOAP_VAR_S*       pResponse = NULL;
    SOAP_VAR_S*       pVV;
    WSD_SUBS_S*       pevs;
    char              address[WSD_MAX_FIELD_SIZE] = {0};
    char              identifier[WSD_MAX_FIELD_SIZE] = {0};
    char              filter[WSD_MAX_FIELD_SIZE] = {0};
    char              *pv, *pe;
    int32_t           rc;
    int32_t           isevtScanAvailable = 0;

    RETURN_VAL_IF(pxml == NULL, NET_WARN, -1);
    do  // TRY
    {
        rc = soap_find_var_in_xml(pxml, "Subscribe");
        if (rc)
        {
            NET_WARN("No find %s !!!", "Subscribe");
            return -1 ;
        }

        QXMLparserSyncTo(pxml, &xml);
        // snapshot soap state
        // find address, this is what the client is subscribing to
        rc = soap_get_var_value_from_xml(&xml, "Delivery.NotifyTo.Address", address, sizeof(address));
        if (rc)
        {
            NET_WARN("No notify address in subscription request");
            break;
        }
        NET_DEBUG("address: %s", address);

        // find filter
        QXMLparserSyncTo(pxml, &xml);
        rc = soap_get_var_value_from_xml(&xml, "Filter", filter, sizeof(filter));
        if (rc)
        {
            NET_WARN("No filter in subscription request");
            break;
        }
        NET_DEBUG("filter: %s", filter);

        QXMLparserSyncTo(pxml, &xml);
        rc = soap_get_var_value_from_xml(&xml, "ReferenceParameters.Identifier", identifier, sizeof(identifier));
        if (rc)
        {
            wsd_generate_uuid(identifier, sizeof(identifier));
            NET_WARN("No identifier in subscription request, generate: %s", identifier);
            rc = 0;
        }
        NET_DEBUG("identifier: %s", identifier);

        // for each string in the filter, allocate a subscription event record for this event
        pv = filter;
        while (pv && *pv && ! rc)
        {
            for (pe = pv; *pe;)
            {
                if (*pe == ' ' || *pe == '\t')
                {
                    *pe++ = '\0';
                    break;
                }
                pe++;
            }
            if (pe > pv)
            {
                WSD_EVT_E evcode;

                // got one event to subscribe to
                evcode = translate_event_from_name(pv);
                isevtScanAvailable = (evtScanAvailable == evcode) ? 1 : 0;
                if (evcode == evtNone)
                {
                    NET_DEBUG("Event %s has no translation, not handling", pv);
                }
                else
                {
                    NET_DEBUG("%s is subscribing to %s", address, pv);
                    if ( (pevs = subslist_get(subs_list)) == NULL )
                    {
                        NET_ERROR("Can't malloc subscription");
                        rc = -1;
                        break;
                    }
                    subscription_init(pevs, identifier, address, pv);
                    time(&pevs->subscibed);
                    pevs->expires = pevs->subscibed + 60*60; // 1 hour
                    pevs->event = evcode;
                }
                pv = pe;
            }
            else
            {
                break;
            }
        }
        if (rc)
        {
            break;
        }

        NET_DEBUG("begin create SOAP response");
        // build reply <subman><addr><ref><id><exp>
        pResponse = soap_create_var("wse:SubscriptionManager", dtchar, 0, NULL, 0);
        if (pResponse == NULL)
        {
            rc = -1;
            break;
        }

        pVV = soap_create_var("wsa:Address", dtchar, 0, srv_data->service_url, 0);
        if (pVV == NULL)
        {
            rc = -1;
            break;
        }

        pResponse->m_child = pVV;
        pVV->m_next = soap_create_var("wsa:ReferenceParameters", dtchar, 0, NULL, 0);
        if (pVV->m_next == NULL)
        {
            rc = -1;
            break;
        }

        pVV = pVV->m_next;
        pVV->m_child = soap_create_var("wse:Identifier", dtchar, 0, identifier, 0);
        if (pVV->m_child == NULL)
        {
            rc = -1;
            break;
        }

        pResponse->m_next = soap_create_var("wse:Expires", dtchar, 0, "PT1H0M0S", 0 /* PT1H -> PT1H0M0S */);
        if (pResponse->m_next == NULL)
        {
            rc = -1;
            break;
        }

        if (1 == isevtScanAvailable)
        {
            pVV = pResponse->m_next;
            pVV->m_next = soap_create_var("wscn:DestinationResponses", dtchar, 0, "", 0);
            if (! pVV->m_next)
            {
                rc = -1;
                break;
            }
            pVV = pVV->m_next;
            pVV->m_child = soap_create_var("wscn:DestinationResponse", dtchar, 0, "", 0);
            if (! pVV->m_child)
            {
                rc = -1;
                break;
            }
            pVV = pVV->m_child;
            pVV->m_child = soap_create_var("wscn:ClientContext", dtchar, 0, "Scan", 0);
            if (! pVV->m_child)
            {
                rc = -1;
                break;
            }
            pVV->m_child->m_next = soap_create_var("wscn:DestinationToken", dtchar, 0, "Token: 0000000001", 0);
            if (! pVV->m_child->m_next)
            {
                rc = -1;
                break;
            }
            pVV->m_next = soap_create_var("wscn:DestinationResponse", dtchar, 0, "", 0);
            if (! pVV->m_next)
            {
                rc = -1;
                break;
            }
            pVV = pVV->m_next;
            pVV->m_child = soap_create_var("wscn:ClientContext", dtchar, 0, "ScanToPrint", 0);
            if (! pVV->m_child)
            {
                rc = -1;
                break;
            }
            pVV->m_child->m_next = soap_create_var("wscn:DestinationToken", dtchar, 0, "Token: 0000000002", 0);
            if (! pVV->m_child->m_next)
            {
                rc = -1;
                break;
            }
            pVV->m_next = soap_create_var("wscn:DestinationResponse", dtchar, 0, "", 0);
            if (! pVV->m_next)
            {
                rc = -1;
                break;
            }
            pVV = pVV->m_next;
            pVV->m_child = soap_create_var("wscn:ClientContext", dtchar, 0, "ScanToEmail", 0);
            if (! pVV->m_child)
            {
                rc = -1;
                break;
            }
            pVV->m_child->m_next = soap_create_var("wscn:DestinationToken", dtchar, 0, "Token: 0000000003", 0);
            if (! pVV->m_child->m_next)
            {
                rc = -1;
                break;
            }
            pVV->m_next = soap_create_var("wscn:DestinationResponse", dtchar, 0, "", 0);
            if (! pVV->m_next)
            {
                rc = -1;
                break;
            }
            pVV = pVV->m_next;
            pVV->m_child = soap_create_var("wscn:ClientContext", dtchar, 0, "ScanToFax", 0);
            if (! pVV->m_child)
            {
                rc = -1;
                break;
            }
            pVV->m_child->m_next = soap_create_var("wscn:DestinationToken", dtchar, 0, "Token: 0000000004", 0);
            if (! pVV->m_child->m_next)
            {
                rc = -1;
                break;
            }
            pVV->m_next = soap_create_var("wscn:DestinationResponse", dtchar, 0, "", 0);
            if (! pVV->m_next)
            {
                rc = -1;
                break;
            }
            pVV = pVV->m_next;
            pVV->m_child = soap_create_var("wscn:ClientContext", dtchar, 0, "ScanToOCR", 0);
            if (! pVV->m_child)
            {
                rc = -1;
                break;
            }
            pVV->m_child->m_next = soap_create_var("wscn:DestinationToken", dtchar, 0, "Token: 0000000005", 0);
            if (! pVV->m_child->m_next)
            {
                rc = -1;
                break;
            }
        }
        isevtScanAvailable = 0;

        rc = soap_format_response(pResponse, WSD_NAME_SPACES, "SubscribeResponse", srv_data->soap_header, "wse",
                                "http://schemas.xmlsoap.org/ws/2004/08/eventing", srv_data->soap_reply, sizeof(srv_data->soap_reply));
    }
    while (0); // CATCH

    if (pResponse)
    {
        soap_delete_var(pResponse);
    }
    return rc;
}

SUBS_HANDLER_DEFINITION(wsd_evt_unsubscribe_handler)
{
    WSD_SUBS_S* pevs;
    int rc;

    subslist_delete(subs_list, id);
    rc = soap_format_response(
                        NULL,
                        WSD_NAME_SPACES,
                        "UnsubscribeResponse",
                        srv_data->soap_header,
                        "wse",
                        "http://schemas.xmlsoap.org/ws/2004/08/eventing",
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                );
    return rc;
}

SUBS_HANDLER_DEFINITION(wsd_evt_renew_handler)
{
    WSD_SUBS_S* pevs;
    SOAP_VAR_S*     pResponse;
    int             rc;

    pevs = subslist_find(subs_list, id);
    if (pevs)
    {
        time (&pevs->expires);
        pevs->expires += 60 * 60;
    }
    else
    {
        NET_ERROR("Subscription id:%s not in active use", id);
    }

    pResponse = soap_create_var(
                        "wse:Expires",
                        dtchar, 0,
                        "PT1H0M0S", 0 //PT1H -> PT1H0M0S
                );
    if (! pResponse)
    {
        return -1;
    }
    // format response
    //
    rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "RenewResponse",
                        srv_data->soap_header,
                        "wse",
                        "http://schemas.xmlsoap.org/ws/2004/08/eventing",
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                );

	if(pResponse)
    {
		soap_delete_var(pResponse);
    }

    return rc;
}

SUBS_HANDLER_DEFINITION(wsd_evt_get_status_handler)
{
    SOAP_VAR_S*       pResponse = NULL;
    int rc;

    pResponse = soap_create_var(
                        "wse:Expires",
                        dtchar, 0,
                        "PT1H0M0S", 0 //PT1H -> PT1H0M0S
                );
    if (!pResponse)
    {
        return -1;
    }
    // format response
    //
    rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "GetStatusResponse",
                        srv_data->soap_header,
                        "wse",
                        "http://schemas.xmlsoap.org/ws/2004/08/eventing",
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                );

	if(pResponse)
    {
		soap_delete_var(pResponse);
    }

    return rc;
}

static SUBS_HANDLER_TBL_S hdl_tbl[] =
{
    { "Subscribe", wsd_evt_subscribe_handler},
    { "Unsubscribe", wsd_evt_unsubscribe_handler},
    { "Renew", wsd_evt_renew_handler},
    { "GetStatus", wsd_evt_get_status_handler},
};

int wsd_is_subscription_action(const char* action)
{
    for (int i = 0; i < ARRAY_SIZE(hdl_tbl); ++i)
    {
        if (strcasecmp(hdl_tbl[i].action_name, action) == 0)
        {
            return 1;
        }
    }
    return 0;
}

int wsd_do_subscription(QXML_S* pxml, const char* action, const char* id, WSD_SERVICE_DATA_S* srv_data, WSD_SUBS_LIST_S* subs_list)
{
    for (int i = 0; i < ARRAY_SIZE(hdl_tbl); ++i)
    {
        if (strcasecmp(hdl_tbl[i].action_name, action) == 0)
        {
            return hdl_tbl[i].handler(pxml, id, srv_data, subs_list);
        }
    }
    //not find subscription action
    return -1;
}

int wsd_subs_list_is_empty(WSD_SUBS_LIST_S* subs_list)
{
    return subs_list->free_ref_index <= 0 ? 1 : 0;
}
