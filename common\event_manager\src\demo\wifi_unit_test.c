#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <math.h>

#include "event_msg_typedef.h"
#include "event_mgr.h"

static void debug_handler(const EVT_MSG_S* msg, void* ctx)
{
    switch (msg->event_type)
    {
        case EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED:
        {
            printf("\n\n\n\n\nwps_pin: %s\n\n\n\n\n\n", (char *)msg->data);
            break;
        }
        case EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED:
        {
            WIFI_SCAN_RESULT_S  scan_result;

            if ( msg != NULL && msg->data_length == sizeof(scan_result) )
            {
                printf("\n\n\n[%s:%d] receive scan result:\n\n", __func__, __LINE__);
                memcpy(&scan_result, msg->data, msg->data_length);
                for ( uint32_t i = 0; i < scan_result.ap_count; ++i )
                {
                    printf("ap_info[%02u]: ssid(%s) bssid(%s) channel(%u) sig_lvl(%u)\n", i, scan_result.ap_info[i].ssid, scan_result.ap_info[i].bssid,
                            scan_result.ap_info[i].channel, scan_result.ap_info[i].sig_lvl);
                }
            }
            else
            {
                printf("invalid event message\n");
            }
        }
        default: break;
    }

    return;
}

#define usage() \
{ \
    printf("usage:\n"); \
    printf("    wifi_unit_test sta [0/1]        0: turn off; 1: turn on\n"); \
    printf("    wifi_unit_test scan [0/1]       0: stop;     1: start\n"); \
    printf("    wifi_unit_test connect [sec_mode] [ssid] [psk]\n"); \
    printf("    wifi_unit_test disconnect\n"); \
    printf("    wifi_unit_test wps [pbc/pin/cancel]\n"); \
    printf("    wifi_unit_test wfd [0/1/2]\n"); \
    ret = -1; \
    goto end; \
}

int main(int argc, char** argv)
{
    EVT_TYPE_E event_array[] = {
        EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED,
        EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED
    };
    WIFI_CONN_CONF_S conn_info;
    EVT_MGR_CLI_S*   cli_ptr = NULL;
    uint32_t         val = 0;
    int32_t          ret = 0;

    cli_ptr = pi_event_mgr_create_client(EVT_MODULE_DEBUG, debug_handler, NULL, &ret);
    if (cli_ptr == NULL)
    {
        printf("fail to call pi_event_mgr_create_client, ret: %d\n", ret);
        return -1;
    }

    ret = pi_event_mgr_register(cli_ptr, event_array, sizeof(event_array) / sizeof(event_array[0]));
    printf("register ret: %d\n", ret);

    if (argc < 2)
    {
        usage();
    }

    if (strcasecmp(argv[1], "sta") == 0)
    {
        if (argc < 3)
        {
            usage();
        }
        val = (uint32_t)atoi(argv[2]);
        printf("%s[%d] station turn %s\n", __func__, __LINE__, val ? "on" : "off");
        pi_event_mgr_notify(cli_ptr, EVT_TYPE_NET_WIFI_SWITCH_REQUEST, &val, sizeof(val));
    }
    else if (strcasecmp(argv[1], "scan") == 0)
    {
        /* scan ssid */
        if (argc < 3)
        {
            usage();
        }
        val = (uint32_t)atoi(argv[2]);
        printf("%s[%d] %s scan ssid\n", __func__, __LINE__, val ? "start" : "stop");
        pi_event_mgr_notify(cli_ptr, EVT_TYPE_NET_WIFI_SCAN_SSID_REQUEST, &val, sizeof(val));
    }
    else if (strcasecmp(argv[1], "connect") == 0)
    {
        if (argc < 4)
        {
            usage();
        }

        snprintf(conn_info.ssid, sizeof(conn_info.ssid), "%s", argv[3]);
        conn_info.sec_mode = (uint16_t)atoi(argv[2]);
        if (conn_info.sec_mode != 0)
        {
            if (argc >= 5)
            {
                snprintf(conn_info.psk, sizeof(conn_info.psk), "%s", argv[4]);
            }
            else
            {
                usage();
            }
        }
        printf("%s[%d] connect to: ssid(%s) psk(%s) sec(%u)\n", __func__, __LINE__, conn_info.ssid, conn_info.psk, conn_info.sec_mode);
        pi_event_mgr_notify(cli_ptr, EVT_TYPE_NET_WIFI_CONNECT_REQUEST, &conn_info, sizeof(conn_info));
    }
    else if (strcasecmp(argv[1], "disconnect") == 0)
    {
        pi_event_mgr_notify(cli_ptr, EVT_TYPE_NET_WIFI_CONNECT_REQUEST, NULL, 0);
    }
    else if (strcasecmp(argv[1], "wps") == 0)
    {
        if (argc < 3)
        {
            usage();
        }
        pi_event_mgr_notify(cli_ptr, EVT_TYPE_NET_WIFI_WPS_REQUEST, argv[2], strlen(argv[2]));
    }
    else if (strcasecmp(argv[1], "wfd") == 0)
    {
        if (argc < 3)
        {
            usage();
        }
        val = (uint32_t)atoi(argv[2]);
        printf("%s[%d] wfd turn %s mode %u\n", __func__, __LINE__, val ? "on" : "off", val);
        pi_event_mgr_notify(cli_ptr, EVT_TYPE_NET_WFD_SWITCH_REQUEST, &val, sizeof(val));
    }
    else
    {
        usage();
    }

    getc(stdin); /* 任意键退出 */

end:
    pi_event_mgr_destroy_client(cli_ptr);
    return ret;
}

