/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ptm_base64.c
 * @addtogroup ptm_base64
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @brief pantum osal base64 encode and decode interface source file
 */
#include <b64/cencode.h>
#include <b64/cdecode.h>
#include <stdio.h>
#include <stdlib.h>
#include <pol/pol_types.h>
#include <pol/pol_io.h>
#include <pol/pol_threads.h>
#include <pol/pol_mem.h>
#include <pol/pol_log.h>


int32_t base64_encode(const char *input, size_t input_strlen, char *output, size_t output_buflen)
{
    if(input == NULL || input_strlen == 0 )
    {
        pi_log_e("input is %p input_strlen:%d\n", input, input_strlen);
        return -1;
    }

    if( output == NULL || output_buflen <= 4*((input_strlen+2)/3))
    {
        //pi_log_e("output is%p, output_buflen is %d \n",output, output_buflen);
        pi_log_e("output is%p, output_buflen is %d, at least need size:%d\n",output, output_buflen, 4*(input_strlen+2/3)+1);
        return -1;
    }

    /* keep track of our encoded position */
    char* c = output;
    /* store the number of bytes encoded by a single call */
    int32_t cnt = 0;
    /* we need an encoder state */
    base64_encodestate s;

    /*---------- START ENCODING ----------*/
    /* initialise the encoder state */
    base64_init_encodestate(&s);
    /* gather data from the input and send it to the output */
    cnt = base64_encode_block(input, input_strlen, c, &s);
    c += cnt;
    /* since we have encoded the entire input string, we know that
       there is no more input data; finalise the encoding */
    cnt = base64_encode_blockend(c, &s);
    c += cnt;
    /*---------- STOP ENCODING  ----------*/


    return c-1-output;
}



int32_t base64_decode(const char *input, size_t input_strlen, char *output, size_t output_buflen)
{
    if( input==NULL || input_strlen%4 )
    {
        pi_log_e("input is %p input_strlen:%d\n", input, input_strlen);
        return -1;
    }

    if( output == NULL || output_buflen <= (input_strlen/4)*3 )
    {
        pi_log_e("output is%p, output_buflen is %d, at least need size:%d\n",output, output_buflen, (input_strlen/4)*3+1);
        return -1;
    }


   /* keep track of our decoded position */
    char* c = output;
    /* store the number of bytes decoded by a single call */
    int cnt = 0;
    /* we need a decoder state */
    base64_decodestate s;

    /*---------- START DECODING ----------*/
    /* initialise the decoder state */
    base64_init_decodestate(&s);
    /* decode the input data */
    cnt = base64_decode_block(input, strlen(input), c, &s);
    c += cnt;
    /* note: there is no base64_decode_blockend! */
    /*---------- STOP DECODING  ----------*/

    /* we want to print the decoded data, so null-terminate it: */
    *c = 0;

    return c-output;
}

#if 0
char* base64_encode(const char *input,size_t input_length,size_t *output_length)
{
    if(input == NULL || input_length ==0 )
    {
        pi_log_e("input is %p input_length:%d\n", input, input_length);
        return NULL;
    }

    if(output_length == NULL)
    {
        pi_log_e("output_length is %p \n", output_length);
        return NULL;
    }

    *output_length = 4*((input_length+2)/3);
    /* set up a destination buffer large enough to hold the encoded data */
    char* output = (char*)pi_malloc((*output_length)+1);
    if(output == NULL)
    {
        pi_log_e("malloc failed\n");
        return NULL;
    }

    /* keep track of our encoded position */
    char* c = output;
    /* store the number of bytes encoded by a single call */
    int cnt = 0;
    /* we need an encoder state */
    base64_encodestate s;

    /*---------- START ENCODING ----------*/
    /* initialise the encoder state */
    base64_init_encodestate(&s);
    /* gather data from the input and send it to the output */
    cnt = base64_encode_block(input, input_length, c, &s);
    c += cnt;
    /* since we have encoded the entire input string, we know that
       there is no more input data; finalise the encoding */
    cnt = base64_encode_blockend(c, &s);
    c += cnt;
    /*---------- STOP ENCODING  ----------*/

    /* we want to print the encoded data, so null-terminate it: */
    *(c-1) = 0;

    return output;
}


char* base64_decode(const char *input, size_t input_length, size_t *output_length)
{
    if(input==NULL || input_length%4)
    {
        pi_log_e("input is:%p,input_length is %d\n",input,input_length);
        return NULL;
    }

    if(output_length == NULL)
    {
        pi_log_e("output_length is %p \n", output_length);
        return NULL;
    }

    *output_length = (input_length/4)*3;
    if(input[input_length-1] == '=')
        (*output_length)--;

    if(input[input_length-2] == '=')
        (*output_length)--;

    /* set up a destination buffer large enough to hold the encoded data */
    char* output = (char*)pi_malloc((*output_length)+1);
    if(output == NULL)
    {
        pi_log_e("malloc failed\n");
        return NULL;
    }
    /* keep track of our decoded position */
    char* c = output;
    /* store the number of bytes decoded by a single call */
    int cnt = 0;
    /* we need a decoder state */
    base64_decodestate s;

    /*---------- START DECODING ----------*/
    /* initialise the decoder state */
    base64_init_decodestate(&s);
    /* decode the input data */
    cnt = base64_decode_block(input, strlen(input), c, &s);
    c += cnt;
    /* note: there is no base64_decode_blockend! */
    /*---------- STOP DECODING  ----------*/

    /* we want to print the decoded data, so null-terminate it: */
    *c = 0;

    return output;
}

#endif


int32_t base64_encode_file(FILE* inputFile, FILE* outputFile)
{
    /* set up a destination buffer large enough to hold the encoded data */
    int size = 256;
    char* input = (char*)pi_malloc(size);
    if(input == NULL)
    {
        pi_log_e("input malloc failed\n");
        return -1;
    }
    char* encoded = (char*)pi_malloc(2*size); /* ~4/3 x input */
    if(encoded == NULL)
    {
        pi_log_e("encoded malloc failed\n");
        pi_free(input);
        return -1;
    }
    /* we need an encoder and decoder state */
    base64_encodestate es;
    /* store the number of bytes encoded by a single call */
    int cnt = 0;

    /*---------- START ENCODING ----------*/
    /* initialise the encoder state */
    base64_init_encodestate(&es);
    /* gather data from the input and send it to the output */
    while (1)
    {
        cnt = pi_fread(input, sizeof(char), size, inputFile);
        if (cnt == 0) break;
        cnt = base64_encode_block(input, cnt, encoded, &es);
        /* output the encoded bytes to the output file */
        pi_fwrite(encoded, sizeof(char), cnt, outputFile);
    }
    /* since we have reached the end of the input file, we know that
       there is no more input data; finalise the encoding */
    cnt = base64_encode_blockend(encoded, &es);
    /* write the last bytes to the output file */
    pi_fwrite(encoded, sizeof(char), cnt, outputFile);
    /*---------- STOP ENCODING  ----------*/

    pi_free(encoded);
    pi_free(input);
    return 0;
}

int32_t base64_decode_file(FILE* inputFile, FILE* outputFile)
{
    /* set up a destination buffer large enough to hold the decoded data */
    int size = 256;
    char* encoded = (char*)pi_malloc(size);
    if(encoded == NULL)
    {
        pi_log_e("malloc failed\n");
        return -1;
    }
    char* decoded = (char*)pi_malloc(size); /* ~3/4 x encoded */
    if(decoded == NULL)
    {
        pi_log_e("malloc failed\n");
        pi_free(encoded);
        return -1;
    }
    /* we need an encoder and decoder state */
    base64_decodestate ds;
    /* store the number of bytes encoded by a single call */
    int cnt = 0;

    /*---------- START DECODING ----------*/
    /* initialise the encoder state */
    base64_init_decodestate(&ds);
    /* gather data from the input and send it to the output */
    while (1)
    {
        cnt = pi_fread(encoded, sizeof(char), size, inputFile);
        if (cnt == 0) break;
        cnt = base64_decode_block(encoded, cnt, decoded, &ds);
        /* output the encoded bytes to the output file */
        pi_fwrite(decoded, sizeof(char), cnt, outputFile);
    }
    /*---------- START DECODING  ----------*/

    pi_free(encoded);
    pi_free(decoded);
    return 0;
}

/**
 *@}
 */
