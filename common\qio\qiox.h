/**************************************************************
 * Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * module name:     qio
 * file name:       qiox.h
 * author:          <PERSON> (<EMAIL>)
 * date:            2023-04-04
 * description:     QIO private header file
 **************************************************************/

#ifndef __QIOX_H__
#define __QIOX_H__

#if defined(Linux) || defined(OSX)

#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <pthread.h>
#include <termios.h>
#define SEP     '/'

#elif defined(Windows)

#include <stdio.h>
#include <io.h>
#include <fcntl.h>
#define SEP     '\\'

#else

#include <stdio.h>
#define SEP     '/'

#endif /* defined(OS) */

#include <stdlib.h>
#include <errno.h>

#ifndef O_BINARY
#define O_BINARY 0
#endif

#include "pol/pol_types.h"
#include "pol/pol_convert.h"
#include "pol/pol_define.h"
#include "pol/pol_io.h"
#include "pol/pol_list.h"
#include "pol/pol_mem.h"
#include "pol/pol_socket.h"
#include "pol/pol_string.h"
#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "pol/pol_time.h"
#include "memmgr/memmgr.h"
#include "qio/qio_general.h"
#include "qio/qioctl.h"

#define MAX_TTY_CHUNK_SIZE      ( 0x10000 ) /* 64 * 1024 */
#define MAX_PDF_CHUNK_SIZE      ( 0x10000 ) /* 64 * 1024 */

#define DECL_PRIV(q, s)         PRIV_INFO_S* s = ( (q) ? (PRIV_INFO_S *)((q)->priv) : NULL )

#define QIO_DEBUG(fmt, ...)     pi_log_d( fmt "\n", ##__VA_ARGS__)
#define QIO_INFO(fmt, ...)      pi_log_i( fmt "\n", ##__VA_ARGS__)
#define QIO_WARN(fmt, ...)      pi_log_w( fmt "\n", ##__VA_ARGS__)
#define QIO_NULL(fmt, ...)      NULL

QIO_S*  qio_posixfile_create    (const char* stream, int32_t flags);
QIO_S*  qio_storage_file_create (const char* stream, int32_t flags);

#endif /* __QIOX_H__ */
