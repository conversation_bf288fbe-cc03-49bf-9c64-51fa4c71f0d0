#include "PEDK_event.h"

#include <quickjs.h>
#include "PEDK_defined.h"
#include "PEDK_lowpower.h"
#include "app_manager.h"

typedef enum{
    LOWPOWER_MACHINE_WAKEUP,    //整机唤醒
    LOWPOWER_MACHINE_SLEEP,     //整机休眠
    LOWPOWER_CPU_SLEEP,         //CPU休眠
    LOWPOWER_CPU_WAKEUP,        //CPU唤醒
    LOWPOWER_DEEPSLEEP, //深度休眠(二级休眠)
    LOWPOWER_MUM
}LOWPOWER_STATUS;
#define countof(x) (sizeof(x) / sizeof((x)[0]))

#define JS_INIT_MODULE js_init_module
static JSClassID js_lowpower_listener_class_id;

typedef struct
{
    char state[32];
}JSListener;


// LowPowerStateChangeListener
JSValue js_lowpower_listener_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    JSListener *listener;
    JSValue obj = JS_UNDEFINED;
    JSValue proto;

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    listener = js_malloc(ctx, sizeof(*listener));
    if(!listener)
    {
        printf("[LINE:%d]js_malloc failed\n",__LINE__);
        return JS_EXCEPTION;
    }

    strcpy(listener->state, "JBSts_Init");


    /* using this_val to get the prototype is necessary when the class is extended. */
    proto = JS_GetPropertyStr(ctx, this_val, "prototype");
    if (JS_IsException(proto))
        return JS_UNDEFINED;
    obj = JS_NewObjectProtoClass(ctx, proto, js_lowpower_listener_class_id);
    JS_FreeValue(ctx, proto);
    if (JS_IsException(obj))
        return JS_UNDEFINED;
    JS_SetOpaque(obj, listener);

    return obj;
}

static int js_lowpower_get()
{
    int ret = 0;
    int respond = 0;
    int size = 10;
    unsigned char buff[1];
    
    ret = SendMsgToMfp(MSG_MODULE_LOWPOWER, MSG_LOWPOWER_SUB_GET, 1, 0, NULL);
    if(ret < 0){
        printf("[LINE:%d]lowpowersave SendMsgToMfp failed\n",__LINE__);
        return -1;
    }
    ret = RecvMsgToMfp(MSG_MODULE_LOWPOWER, MSG_LOWPOWER_SUB_GET, &respond, buff, &size, 10);
    if(ret < 0){
        printf("[LINE:%d]js_lowpower_get failed\n",__LINE__);
        return -1;
    }
    return buff[0];
}

static void js_lowpower_set(int ret)
{
    unsigned char mode[1];
    mode[0] = ret;
    SendMsgToMfp(MSG_MODULE_LOWPOWER, MSG_LOWPOWER_SUB_SET, ret, 1, mode);
    return;
}

JSValue js_lowpower_get_CurrentState(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    
    /*ret = am_get_whitelist_enable("Powersave", ctx);
    if(ret == UNSUPPORT)
    {
        printf("Powersave set unsupport,permission denied\n");
        return JS_NewString(ctx, "ENOTPERMISSION"); //API权限未开启
    } if(ret == SUPPORT){*/
        ret = js_lowpower_get();
        printf("[LINE:%d]receive arg = %d\n",__LINE__,ret);
        return JS_NewInt32(ctx, ret);
    /*}else{
        printf("Unable to get Powersave, operation failed\n");
        return JS_NewString(ctx, "EXIT_FAILURE");
    }*/
}

JSValue js_lowpower_set_CurrentState(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    if (!JS_IsNumber(argv[0]))
    {
        return JS_NewString(ctx, "EINVALIDPARAM");
    }
    /*ret = am_get_whitelist_enable("Powersave", ctx);
    if(ret == UNSUPPORT)
    {
        printf("Powersave set unsupport,permission denied\n");
        return JS_NewString(ctx, "ENOTPERMISSION"); //API权限未开启
    } if(ret == SUPPORT){
        if (JS_IsString(argv[0]))
        {
            return JS_EXCEPTION;
        }*/
    JS_ToInt32(ctx, &ret, argv[0]);
	if(ret != LOWPOWER_MACHINE_SLEEP && ret != LOWPOWER_MACHINE_WAKEUP)
    {
        return JS_NewString(ctx, "EINVALIDPARAM");
    }
        printf("[LINE:%d]js_lowpower_set_CurrentState send arg = %d\n",__LINE__,ret);
        js_lowpower_set(ret);
        return JS_NewString(ctx, "EXIT_SUCCESS");
    /*}else{
        printf("Unable to get Powersave, operation failed\n");
        return JS_NewString(ctx, "EXIT_FAILURE");
    }*/
}

static void js_lowpower_listener_finalizer(JSRuntime *rt, JSValue val)
{
    JSListener *listener = JS_GetOpaque(val, js_lowpower_listener_class_id);

    js_free_rt(rt, listener);
}

static JSClassDef js_listener_class =
{
    "LowPowerStateChangeListener",
     .finalizer = js_lowpower_listener_finalizer,
};

static JSValue js_lowpower_notify(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    return JS_UNDEFINED;
}

static const JSCFunctionListEntry PEDK_lowpower_listener_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("notify", 1, js_lowpower_notify),
};

int js_lowpower_init(JSContext *ctx, JSValueConst global)
{
    JSValue lowpower_proto, lowpower_constructor;
    int i = 0;

    JS_NewClassID(&js_lowpower_listener_class_id);
    JS_NewClass(JS_GetRuntime(ctx), js_lowpower_listener_class_id, &js_listener_class);

    lowpower_proto = JS_NewObject(ctx);
    JS_SetPropertyFunctionList(ctx, lowpower_proto, PEDK_lowpower_listener_funcs, countof(PEDK_lowpower_listener_funcs));
    JS_SetClassProto(ctx, js_lowpower_listener_class_id, lowpower_proto);

    lowpower_constructor = JS_NewCFunction2(ctx, js_lowpower_listener_ctor, "PEDKLowpowerListener",0, JS_CFUNC_constructor, 0);
    JS_SetConstructor(ctx, lowpower_constructor, lowpower_proto);

    JS_DefinePropertyValueStr(ctx, global, "PEDKLowpowerListener", lowpower_constructor, JS_PROP_C_W_E);

}

