export function getCurLanguage()
{
    return get_current_language();
}
export function setLanguage(language)
{
    return set_current_language(language);
}

class SystemSetting {
    constructor() {

    }
    setPrompSoundMode(sound_mode)
    {
        console.log('Input function setPrompSoundMode');
        return js_setPrompSoundMode(sound_mode);
    }
    getPrompSoundMode()
    {
        console.log('Input function switch getPrompSoundMode');
        return js_getPrompSoundMode();
    }
    setBellSoundMode(sound_mode)
    {
        return js_setBellSoundMode(sound_mode);
    }
    getBellSoundMode()
    {
        return js_getBellSoundMode();
    }
    setFaxSoundMode(sound_mode)
    {
        return js_setFaxSoundMode(sound_mode);
    }
    getFaxSoundMode()
    {
        return js_getFaxSoundMode();
    }
    setVolSwitchMode(sound_mode)
    {
        return js_setVolSwitchMode(sound_mode);
    }
    getVolSwitchMode()
    {
        return js_getVolSwitchMode();
    }

	getCurLanguage()
	{
		const retstr = get_current_language();
		if(retstr === "EXIT_FAILURE")
		{
			return ERROR_NO.EXIT_FAILURE
		}
		else
		{
			console.log("EXIT_SUCCESS\n");
		}
		return retstr;
	}
	setLanguage(language){
		return set_current_language(language);
	}

}


globalThis.pedk.device.setting.SystemSetting = SystemSetting
globalThis.pedk.device.setting.SystemSetting.getCurLanguage = getCurLanguage
globalThis.pedk.device.setting.SystemSetting.setLanguage = setLanguage
