/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file cini.c
 * @addtogroup system_manager
 * @{
 * @brief used to parse system resource configuration items 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#include "cini.h"
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include <sys/stat.h>
#include <stdio.h>
#include <sys/types.h>
#include <fcntl.h>
#include <errno.h>

static void strip_space(char *buf , int buf_len , enum SECTION_POS pos)
{
    int start_pos = -1 , end_pos = -1 ,
        space_pos = -1;
    if (!buf || buf_len <= 0)
        return ;
    
    if (pos == FRONT)
    {
        for(int i = 0; i < buf_len; ++i)
        {
            if (buf[i] != ' ' && buf[i] != '\t')
            {
                start_pos = i;
                break;
            }
        }
        if (start_pos == 0)
        {
            return ;
        }
        if (start_pos == -1)
        { 
            buf[0] = 0x0;
            return;
        }
        memmove(buf , buf + start_pos , buf_len - start_pos);
        buf[buf_len - start_pos] = 0x0;
    }
    else if (pos == CENTRE)
    {
        for (int i = 0; i < buf_len; ++i)
        {
            if (buf[i] != ' ' && buf[i] != '\t')
            {
                start_pos = i;
                break;
            }
        }
        for (int i = buf_len - 1; i >= 0; --i)
        {
            if (buf[i] != ' ' && buf[i] != '\t')
            {
                end_pos = i;
                break;
            }
        }
        if (start_pos >= end_pos)
            return ;
        struct {
            int i;
        }loop;
        for (loop.i = start_pos; loop.i <= end_pos; ++loop.i)
        {
            if (space_pos == -1 && (buf[loop.i] == ' ' || buf[loop.i] == '\t'))
            {
                space_pos = loop.i;
            }
            else if (space_pos != -1 && buf[loop.i] != ' ' && buf[loop.i] != '\t')
            {
                buf[space_pos] = buf[space_pos] ^ buf[loop.i];
                buf[loop.i] = buf[space_pos] ^ buf[loop.i];
                buf[space_pos] = buf[space_pos] ^ buf[loop.i];
                loop.i = space_pos;
                space_pos = -1;
            }
        }
        buf[space_pos] = 0x0;
    }
    else if (pos == BACK)
    {
        for (int i = buf_len - 1; i >= 0; --i)
        {
            if (buf[i] != ' ' && buf[i] != '\t')
            {
                end_pos = i;
                break;
            }
        }
        if (end_pos == -1 || end_pos == buf_len - 1)
        {
            return;
        }
        buf[end_pos + 1] = 0x0;
    }
    else
    {
        fprintf(stderr , "section pos[%d] is error\n" , pos);
    }
}

static int handle_line(char *line , size_t line_len , struct cini *root)
{
    char *start_pos = NULL , *end_pos = NULL;
    char *key = NULL;
    char *value = NULL;
    struct section *last_section = NULL;
    if (!line || !root)
        return -1;

    if ((start_pos = strchr(line , ';')) || 
          (start_pos = strchr(line , '\n')))
    {
        *start_pos = 0x0;
        line_len = strlen(line);
    }

    if (line_len == 0)
        return 0;

    if ((start_pos = strchr(line , '[')))
    {
        if (!(end_pos = strchr(line , ']')))
        {
            fprintf(stderr , "error:miss \']\';line:%s\n" , line);
            return -1;
        }
        
        ++start_pos; 
        *end_pos = 0x0;
        strip_space(start_pos , strlen(start_pos) , FRONT);
        strip_space(start_pos , strlen(start_pos) , BACK);
        return cini_add_section(root , start_pos) ? 0:-1;
    }

    if (!(last_section = cini_last_section(root)))
    {
        fprintf(stderr , "error:not section\n");
        return -1;
    }

    strip_space(line , strlen(line) , FRONT);
    if (*line == '=')
    {
        fprintf(stderr , "error:miss key;line:%s\n" , line);
        return -1;
    }
    start_pos = line;
    if (!(end_pos = strchr(start_pos , '=')))
    {
		strip_space(start_pos , strlen(start_pos) , BACK);
		key = start_pos;
    }
	else
	{
		*end_pos = 0x0; 
		strip_space(start_pos , strlen(start_pos) , BACK);
		key = start_pos;

		start_pos = end_pos + 1;
		strip_space(start_pos , strlen(start_pos) , FRONT);
		strip_space(start_pos , strlen(start_pos) , BACK);
		value = start_pos;
	}
    return cini_add_parameter(last_section , key , value);
}

void cini_init(struct cini *root)
{
    if (!root)
        return;
    root->section_num = 0;
    root->head = NULL;
}

static void cini_del_parameters(struct section *sect)
{
    if (!sect)
        return;

    struct parameter *ptr = sect->subpara , *tmp = NULL;
    if (!ptr)
        return;
    while(ptr->next)
    {
        tmp = ptr->next;
        ptr->next = tmp->next;
		if (ptr->key)
		{
			free(ptr->key);
		}
		if (ptr->value)
		{
			free(ptr->value);
		}
        free(ptr);
        ptr = tmp;
    }
	if (ptr->key)
	{
		free(ptr->key);
	}
	if (ptr->value)
	{
		free(ptr->value);
	}
    free(ptr);
}

void cini_destroy(struct cini *root)
{
    if (!root)
        return;
    struct section *tmp = NULL , *ptr = root->head;
    while(ptr)
    {
        cini_del_parameters(ptr);
        tmp = ptr->next;
        free(ptr);
        ptr = tmp;
        --root->section_num;
    }
    root->head = NULL;
    root->section_num = 0;
}

int parser_cini(const char *file ,  struct cini *root)
{
    if (!file || !root)
    {
        fprintf(stderr , "error:input args is error\n");
        return -1;
    }

    if (access(file , F_OK) == -1)
    {
        fprintf(stderr , "error:%s , %s\n" , file , strerror(errno));
        return -1;
    }

    FILE *fp = fopen(file , "r"); 
    if (!fp)
    {
        fprintf(stderr , "error:open %s file\n" , file);
        return -1;
    }

    char *line = NULL;
    size_t line_len = 0;
    ssize_t read = 0 , ret = 0;
    
    while ( (read = getline(&line, &line_len, fp)) != -1) 
    {
        ret = handle_line(line , strlen(line) , root);
    }

    free(line);
    fclose(fp);

    return ret;
}

int write_cini(const char *file ,  struct cini *root)
{
    int fd = -1 , len = 512;
    struct section *p_sect = NULL;
    struct parameter *p_para = NULL;
    char buf[len];
    if (!root)
        return -1;

    fd = open(file , O_CREAT|O_WRONLY|O_TRUNC , 0664);
    if (fd == -1)
    {
        fprintf(stderr , "open %s error:%s\n" , file , strerror(errno)); 
        return -1;
    }
    p_sect = root->head;
    while(p_sect)
    {
        memset(buf , 0 , len);
        snprintf(buf , len , "[%s]\n" , p_sect->name );
        write(fd , buf , strlen(buf));
        
        p_para = p_sect->subpara;
        while(p_para)
        {
            memset(buf , 0 , len);
			if (p_para->value)
				snprintf(buf , len , "%s=%s\n" , p_para->key , p_para->value);
			else
				snprintf(buf , len , "%s\n" , p_para->key);
            write(fd , buf , strlen(buf));
            p_para = p_para->next;
        }
        p_sect = p_sect->next;
    }

    close(fd);
    return 0;
}

struct section* cini_find_section(const struct cini *root , const char *section)
{
    struct section *ptr = NULL;
    if (!root || !section)
        return ptr;

    if (!(ptr = root->head))
        return ptr;

    while(ptr)
    {
        if (strlen(ptr->name) == strlen(section) && 
            memcmp(ptr->name , section , strlen(ptr->name)) == 0)
        {
            break;
        }
        ptr = ptr->next;
    }
    return ptr;
}

struct section* cini_first_section(const struct cini *root)
{
    return !root ? NULL : root->head;
}

struct section* cini_last_section(struct cini *root)
{
    struct section *ptr = NULL;
    if (!root) 
        return ptr;
    if (!(ptr = root->head))
        return ptr;
    while(ptr->next)
    {
        ptr = ptr->next;
    }
    return ptr;
}

int cini_calculate_section(struct cini *root)
{
	return !root?-1:root->section_num;
}

struct section* cini_add_section(struct cini *root , const char *section)
{
    if (!root || !section)
        return NULL;
    
    struct section *ptr = NULL , 
        *new_sect = (struct section*)malloc(sizeof(*new_sect));
    if (!new_sect)
    {
        fprintf(stderr , "add section:malloc error\n");
        return NULL;
    }
    memset(new_sect , 0 , sizeof(*new_sect));
    snprintf(new_sect->name , KEY_LEN , "%s" , section); 

    if (!(ptr = root->head))
    {
        root->head = new_sect;
    }
    else
    {
        while(ptr->next)
        {
            ptr = ptr->next;
        }
        ptr->next = new_sect;
    }

    root->section_num++;
    return new_sect;
}

int cini_del_section(struct cini *root , const char *section)
{
    struct section *ptr = NULL , *tmp = NULL; 
    if (!root || !section)
        return -1;

    if (!(ptr = root->head))
        return -1;

    if (strlen(section) == strlen(ptr->name) &&
        memcmp(section , ptr->name , strlen(section)) == 0)
    {
        root->head = ptr->next;
        free(ptr);
        --root->section_num;
        return 0;
    }
    while(ptr->next)
    {
        if (strlen(ptr->next->name) == strlen(section) && 
            memcmp(ptr->next->name , section , strlen(ptr->next->name)) == 0)
        {
            tmp = ptr->next;
            ptr->next = tmp->next;
            cini_del_parameters(tmp);
            free(tmp);

            --root->section_num;
            return 0;
        }
        ptr = ptr->next;
    }
    return -1;
}

char* cini_find_parameter(const struct section *sect , const char *key)
{
    struct parameter *ptr = NULL;
    if (!sect || !key)
        return NULL;
    if (!(ptr = sect->subpara))
        return NULL;
    while(ptr)
    {
        if (strlen(key) == strlen(ptr->key) &&
            memcmp(key , ptr->key , strlen(key)) == 0)
            return ptr->value;
        ptr = ptr->next;
    }
    return NULL;
}

int cini_add_parameter(struct section *sect , const char *key , const char *value)
{
    struct parameter *ptr = NULL , *new_param = NULL;
    if (!sect || !key )
        return -1;
	if (cini_find_parameter(sect , key))
	{
		return 0;
	}

    new_param = (struct parameter*)malloc(sizeof(*new_param));
    if (!new_param)
    {
        fprintf(stderr , "add parameter:malloc error\n");
        return -1;
    }
    memset(new_param , 0 , sizeof(*new_param));

	if (key)
	{
		new_param->key = strdup(key);
	}
	if (value)
	{
		new_param->value = strdup(value);
	}
	else
	{
		new_param->value = strdup(""); 
	}
//    snprintf(new_param->key , KEY_LEN , "%s" , key);
 //   if (value)
  //  {
   //     snprintf(new_param->value , VALUE_LEN , "%s" , value);
    //}
    
    if (!(ptr = sect->subpara))
    {
        sect->subpara = new_param;
    }
    else
    {
        while(ptr->next)
        {
            ptr = ptr->next;
        }
        ptr->next = new_param;
    }
	++sect->param_num;
    return 0;    
}

int cini_del_parameter(struct section *sect , const char *key)
{
    struct parameter *ptr = NULL , *tmp = NULL;
    if (!sect || !key)
        return -1;

    if (!(ptr = sect->subpara))
    {
        return -1;
    }

    if (strlen(ptr->key) == strlen(key) &&
        strcmp(ptr->key , key) == 0)
    {
        sect->subpara = ptr->next;
		if (ptr->key)
		{
			free(ptr->key);
		}
		if (ptr->value)
		{
			free(ptr->value);
		}
        free(ptr);
		--sect->param_num;
        return 0;
    }
    while(ptr->next)
    {
        if (strlen(ptr->next->key) == strlen(key) &&
            strcmp(ptr->next->key , key) == 0)
        {
            tmp = ptr->next;
            ptr->next = tmp->next;
			if (tmp->key)
			{
				free(tmp->key);
			}
			if (tmp->value)
			{
				free(tmp->value);
			}
            free(tmp);
			--sect->param_num;
            return 0;
        }
        ptr = ptr->next;
    }

    return -1;
}

/**
 * @}
 */
