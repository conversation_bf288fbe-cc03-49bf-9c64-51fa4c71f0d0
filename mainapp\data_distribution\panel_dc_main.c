/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file data_distribution.c
 * @addtogroup panel_dc
 * @{
 * @brief panel_dc program entry ,register panel sys_msg client,create
 *      a thread to recv msg from sys_msg_mgr
 * <AUTHOR>
 * @version 1.0
 * @date 2023-06-3
 */


#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "public/msgrouter_main.h"
#include "pol/pol_threads.h"
#include "panel_dc_main.h"
#include "qio/qio_general.h"
#include "public_data_proc.h"
#include "pol/pol_log.h"
#include "panel_event.h"
#include "panel_config.h"
#include "public/msgrouter_main.h"
#include "panel_maintenance_mode.h"
#include "panel_pedk_mfp.h"
#if 1
#include "panel_file.h"
#endif

#define PANEL_STACK_SIZE                   ( 64 * 1024 )       ///< panel thread stack size
#define PANEL_TASK_PRIORITY                ( 50 )               ///< panel thread priority

typedef struct
{
    int32_t job_id;
    int32_t code;
}JOB_RETURN_S;



/**
* @brief create a thread to recv msg from sys_msg_mgr
* @author: madechang
*/
void* panel_sys_msg_thread(void* arg)
{
    int32_t     ret = 0;
    ROUTER_MSG_S   sys_msg;
    JOB_RETURN_S job_ret;
    while (1)
    {
         ret = task_msg_wait_forever_by_router(MID_PANEL, &sys_msg);
         if (ret < 0)
         {
             break;
         }

         pi_log_i("panel_dc recv msgType = %d,msg1 = %d,msg2 = %d,msgSender = %d\n", \
                            sys_msg.msgType,sys_msg.msg1,sys_msg.msg2,sys_msg.msgSender);
         switch (sys_msg.msgType)
         {
            case MSG_CTRL_JOBID_GENERATE:            /// sys_job moudle return job id to panel
                job_ret.job_id  = sys_msg.msg1;
                job_ret.code    = sys_msg.msg2;
                panel_send_data_u8(STATUS, STATUS_CMD_JOB_START_RETURN, NOTIFICATION_RESPONSE, &job_ret, sizeof(JOB_RETURN_S));
                break;

            case MSG_PRINT_ENGINE_ENTER_MAIN_MAINTENANCE:
                //panel_maintenance_mode_unlock();
                if( sys_msg.msg2 == MAINTENANCE_ACK_SUCCESS ) ///ack success
                {
                    panel_maintenance_mode_unlock();
                    panel_get_engine_data_init();
                    //panel_send_data_u8(SETTING, SETTING_CMD_MAINTENANCE_ENTER, NOTIFICATION_RESPONSE, );
                }
                else if( sys_msg.msg2 == MAINTENANCE_ACK_FAIL)
                {
                    ///enter mainten mode fail
                    panel_send_data_u8(SETTING, SETTING_CMD_MAINTENANCE_ENTER, FAIL_RESPONSE, &sys_msg.msg2,sizeof(sys_msg.msg2) );
                }
                break;

            case MSG_PRINT_ENGINE_EXIT_MAIN_MAINTENANCE:
                break;

            case MSG_PRINT_ENGINE_MAINTENANCE_GET:
                //panel_maintenance_mode_unlock();
                engine_reply_get_func_msg( sys_msg.msg1, sys_msg.msg2);
                break;

            case MSG_PRINT_ENGINE_MAINTENANCE_SET:
                //panel_maintenance_mode_unlock();
                engine_reply_set_func_msg(sys_msg.msg1, sys_msg.msg2 );
                break;

            case MSG_PRINT_ENGINE_MAINTENANCE_CHECK:
                 engine_reply_check_func_msg(sys_msg.msg1, sys_msg.msg2 );
                break;

            case MSG_FW_UPGRATE_PARSER_FAIL:
                panel_send_cmd( STATUS, STATUS_CMD_UPGRATE_FW_PARSER_FAIL, NOTIFICATION_RESPONSE );
                break;

            case MSG_POWER_MANAGER_ENGINES_SLEEP:
                panel_send_cmd( STATUS, STATUS_CMD_ENGINES_SLEEP, NOTIFICATION_RESPONSE );
                break;

            case MSG_PRINT_ENGINE_ADVANCE_READY:
                panel_send_cmd( STATUS, STATUS_CMD_PRINT_ENGINE_ADVANCE_READY, NOTIFICATION_RESPONSE );
                break;

            case MSG_CTRL_INTERRUPT_MODE_ACK:
                pi_log_i("panel recv suspend ack\n");
                panel_send_cmd( OPERATE, OPERATE_CMD_SUSPEND_PRINT, NOTIFICATION_RESPONSE );
                break;

            case MSG_CTRL_INTERRUPT_RELIEVE_ACK:
                pi_log_i("panel recv resume ack\n");
                panel_send_cmd( OPERATE, OPERATE_CMD_RESUME_PRINT, NOTIFICATION_RESPONSE );
                break;

            case MSG_UPDATE_SOLE_FAIL:
                panel_send_cmd( STATUS, STATUS_CMD_SERIAL_NUM_WRITE_FAILED, NOTIFICATION_RESPONSE );
                break;

            case MSG_PEDK_APP_INSTALL:
                pedk_install_proc( sys_msg.msg1, sys_msg.msg2, sys_msg.msg3 );
                break;

            default:
                break;
         }
    }
    return NULL;
}

///< this for update data to panel when wakeup from deep sleep
void panel_data_init( int data )
{
    panel_data_sync();
}

/**
 * @brief notify panel that dc is ready,it can enter the home window
 * @author: madechang
 */
void panel_notify_ready(void    )
{
    uint32_t data = 0;      /// useless
    pi_log_d( "panel init!!!!\n" );
    panel_send_data_u8( OPERATE, OPERATE_CMD_DC_INIT_DONE_NOTIFY, NOTIFICATION_RESPONSE, &data, sizeof(data) );
}

/**
 * @brief start panel register panel client and create sys msg recv thread
 * @return true or false
 * @author: madechang
 */
 int32_t panel_prolog( void )
{
    PI_THREAD_T g_panel_taskID = INVALIDTHREAD;

    msg_router_register( MID_PANEL);
    g_panel_taskID = pi_thread_create( panel_sys_msg_thread, PANEL_STACK_SIZE, NULL, PANEL_TASK_PRIORITY, NULL, "panel_sys_msg_thread");
    if(INVALIDTHREAD == g_panel_taskID )
    {
        pi_log_e( "Create panel recv System Msg Thread Fail \r\n" );
        return FALSE;
    }
    panel_init();
    //for test
    panel_maintenance_mode_prolog();

    #if CONFIG_SDK_PEDK
    if( panel_pedk_init() < 0 )
    {
        printf("init panel pedk error!\n");
    }

    #endif

#if 1
    panel_file_debug_cmd_register();
#endif

    return TRUE;
}


/**
 * @}
 */

