/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file memmgr.h
 * @addtogroup memmgr
 * @{
 * <AUTHOR>
 * @date 2023-4-18
 * @version v1.0
 * @brief image memory manage module
 */
#ifndef __MEMMGR_H__
#define __MEMMGR_H__

#include <pol/pol_define.h>
#include <pol/pol_threads.h>
#include <pol/pol_list.h>
#include <sys/types.h>
#include <sys/shm.h>

PT_BEGIN_DECLS


typedef unsigned char     color_t,*color_p; ///< color type
typedef unsigned long     addr_t;  ///< addr type

struct init_info{
    size_t size;
    size_t fragment_pool_size;
    uint32_t block_count;
    uint32_t block_size;
    char handle_name[32];
};

struct req_info{
    int32_t mode;
    size_t reqsize;
    size_t actsize;
    addr_t addr;
    int32_t cached;
    char func[24];
    int line;
};

typedef enum{
    SINGLE_LIST,
    MULTI_LIST,
    FRAGMENTS_LIST
}FREE_LIST_MODE_E;


#define PADN(val, N)    (((val) + (N)-1) & (~((N)-1)))

#define PTMEM_INIT_HANDLE               0x9C21

#define PTMEM_MALLOC                    0x9C22

#define PTMEM_FREE                      0x9C23

#define PTMEM_FLUSH                     0x9C24

#define PTMEM_FLUSHINVALIDATE           0x9C25

#define PTMEM_INVALIDATE                0x9C26

#define PTMEM_HANDLE_FREE_SIZE          0x9C27

#define PTMEM_HANDLE_BLOCK_MAXSIZE      0x9C28

#define PTMEM_COPYTOUSER                0x9C29

#define PTMEM_COPYTOPHYS                0x9C2A

#define PTMEM_FLUSHPHYS                 0x9C2B

#define PTMEM_FORCE_MERGE_LIST          0x9C2C

#define PTMEM_CLEANUP                   0x9C2D

#define PTMEM_MEMCOPY                   0x9C2E

#define PTMEM_MEMSET                    0x9C2F

#define PTMEM_VIR_2_PHY                 0x9C3A

#define PTMEM_UNMAP                     0x9C3B

#define PTMEM_MALLOC_SPAD               0x9C3C

#define PTMEM_POOL_FREE_SIZE            0x9C3D

#define PTMEM_DEINIT_HANDLE             0x9C3E

#define PTMEM_RECYCLE_MULTI_LIST        0x9C3F

#define PTMEM_RELEASE                   0x9C40/*release memory*/

#define PTMEM_RELEASE2                  0x9C41/*release reserved memory*/
typedef enum
{
    ccRED        = 0,   ccR     = 0,    ccMIN   = 0,
    ccGREEN      = 1,   ccG     = 1,
    ccBLUE       = 2,   ccB     = 2,
    ccLUMINANCE  = 3,   ccY     = 3,
    ccBLUECHROMA = 4,   ccCb    = 4,
    ccREDCHROMA  = 5,   ccCr    = 5,
    ccCYAN       = 6,   ccC     = 6,
    ccMAGENTA    = 7,   ccM     = 7,
    ccYELLOW     = 8,   ccYL    = 8,
    ccBLACK      = 9,   ccBLK   = 9,
    ccSEGMENT    = 10,  ccSG    = 10,
    ccTEXT       = 11,  ccTX    = 11,
    ccSUBSAMPLED = 12,  ccSS    = 12,
    ccSEGMENTBE  = 13,  ccSGBE  = 13,  ///< big-endian segmentation data
    ccMAX        = 14,  NUM_COLORCOMPONENTS = 14
}COLOR_COMPONENT_E;

/**
 * @brief Image Compression type
 */
typedef enum
{
    comprNONE,      ///< not compressed
    comprJBIG,      ///< jbig compressed
    comprRLE,       ///< run length encoded
    comprZLIB,      ///< zlib compressed
    comprG3,        ///< g3 compressed
    comprSOLID,     ///< data is uniformly colored (solid)
    comprWPT,       ///< white black point tracking
    comprSKEWDET,   ///< skew angle detection
}COMPRESSION_TYPE_E;

/**
* @brief  JBIG compression Info structure used to represent parameters needed to \n
*  JBIG decompress a band
*/
typedef struct tag_jbig_cmpr
{
    uint8_t  Dl;
    uint8_t  D;
    uint8_t  P;
    uint8_t  reserved;
    uint32_t Xd;
    uint32_t Yd;
    uint32_t L0;
    uint8_t  Mx;
    uint8_t  My;
    uint8_t  Order;
    uint8_t  Options;

    /**
    * @brief  grey encoding table, if bitmangled \n
    * Lookup for translation of reconstructed pixel values. Only used for 2 and 4 bit dept
    */
    uint8_t iGreyCodeTable[16];
}JBIG_COMPR_S;

/**
* @brief G3 compression Info structure used to represent parameters needed to \n
* handle G3 compress a band
*/
typedef struct tag_g3_cmpr
{
    uint8_t  sts;             ///< status; more data for page, EOI, EOJ
    uint8_t  typ;             ///< mh, mr, mmr
    uint32_t seq;             ///< sequence index
    uint32_t minbits;         ///< enc: minimum bits in line
    uint32_t maxbits;         ///< dec: maximum bits in line
    uint8_t  kfactor;        ///< K factor
}G3_COMPR_S;

/**
 * @brief White Point Tracking Info structure to store the tracking data to\n
 * represent parameters passed from either of SFE45XX step to SBE step\n
 * or use WPTHIST method to SBE\n
 * Notice! The image data is NOT compressed \n
 */
typedef struct tag_wpt_cmpr
{
    uint32_t  method;           ///< WPC method, 0: undefined, 1: SFE45XX WPT. 2:WPTHIST

    uint32_t  maxWhite0;        ///< SFE45XX: max [white] RGB/Mono => whitest
    uint32_t  maxWhite1;        ///< SFE45XX: max [white] RGB => whitest
    uint32_t  maxWhite2;        ///< SFE45XX: max [white] RGB => whitest
    uint32_t  minBlack0;        ///< SFE45XX: min [black] RGB/Mono => blackest
    uint32_t  minBlack1;        ///< SFE45XX: min [black] RGB => blackest
    uint32_t  minBlack2;        ///< SFE45XX: min [black] RGB cd=> blackest
    uint32_t  wideMode;         ///< SFE45XX: 0: narrow mode or 1: wide mode

    uint32_t  histWhite0;       ///< WPTHIST: white from histogram, RGB/Mono
    uint32_t  histWhite1;       ///< WPTHIST: white from histogram, RGB
    uint32_t  histWhite2;       ///< WPTHIST: white from histogram, RGB
    uint32_t  removalWhite0;     ///< WPTHIST: background removal white, RGB/Mono
    uint32_t  removalWhite1;     ///< WPTHIST: background removal white, RGB
    uint32_t  removalWhite2;    ///< WPTHIST: background removal white, RGB
    uint32_t  stdDev0;           ///< WPTHIST: std deviation from peak to max histogram bucket, RGB/Mono
    uint32_t  stdDev1;           ///< WPTHIST: std deviation from peak to max histogram bucket, RGB
    uint32_t  stdDev2;           ///< WPTHIST: std deviation from peak to max histogram bucket, RGB

    uint32_t  updated;          ///< the data is just updated at this time or not changed as previous status.
}WPT_COMPR_S;

/**
 * @brief  Skew Detection Info structure to store the detection data to\n
 *  represent parameters passed from Deskew step to Rotatep_8 step
 */

typedef struct tag_skew_cmpr
{
    uint32_t  method;         ///< reserved
    uint32_t  bandnum;        ///< for debug
    uint32_t  updated;        ///< the data is just updated at this time or not changed as previous status.
    double angleSkew;
}SKEW_DATA_S, *SKEW_DATA_P;

/**
 * @brief Compression Info, attached to a band
 */
typedef struct tag_compr_info
{
    color_t       *psrc;         ///< compressed data pixel ptr
    COLOR_COMPONENT_E   cc;         ///< what plane this is for
    COMPRESSION_TYPE_E  type;       ///< what type of compression this is
    int32_t             planebit;   ///< 0 = bit0, 1 = bit1, 2 = bit2, etc. if this is one plane slice
    int32_t             encbytes;   ///< size in bytes of compressed data
    int32_t             bytestot;   ///< size in bytes of compressed data and any padding
    int32_t             decbytes;   ///< size in bytes of uncompressed data
    void                *parent;    ///< allocation object data is to be freed in, acture, refrence MEMOBJ_P
     /**
     * @brief a compression info can be a chain of overlapped areas or even overlapped bit components of a plane
     */
    struct tag_compr_info *next;
    int                   compr_id;
    int                   use;
    /**
     * @brief various types of things for specfic types of data
     */
    union utag_compr_hdrs
    {
         JBIG_COMPR_S   jbig_hdr;    ///< jbig context, if jbig compressed
         G3_COMPR_S     g3_hdr;      ///< g3 context, if jbig compressed
         color_t        solid_color; ///< solid color value, if solid compressed
         WPT_COMPR_S    wpt_data;    ///< white point tracking data (uncompressed)
         SKEW_DATA_S    skew_data;    ///< skew data
    }compr_hdr;
}COMPRESSION_INFO_S, *COMPRESSION_INFO_P;


typedef enum
{
    PAGE_TYPE_FRONT             =    0x00000001,
    PAGE_TYPE_BACK              =    0x00000002,

    PAGE_TYPE_SCAN_FB           =    0x00000010,
    PAGE_TYPE_SCAN_ADF          =    0x00000020,

    PAGE_TYPE_FIRST             =    0x00000100,
    PAGE_TYPE_LAST              =    0x00000200,

    PAGE_TYPE_FRONT_COVER_INSIDE    =    0x00010000,
    PAGE_TYPE_FRONT_COVER_OUTSIDE   =    0x00020000,
    PAGE_TYPE_BACK_COVER_INSIDE     =    0x00040000,
    PAGE_TYPE_BACK_COVER_OUTSIDE    =    0x00080000,

    PAGE_TYPE_BLANK_PAGE     =    0x01000000,
    PAFE_TYPE_INSTER_PAGE    =    0x02000000,
    PAGE_TYPE_SPEARATE_PAGE  =    0x04000000,

    PAGE_TYPE_INVALID,
    PAGE_TYPE_MAX            =    0xFFFFFFFF
}PAGE_TYPE_E;

#define FMT_PAGE_SIDE_MASK        0xF
#define FMT_PAGE_SOURCE_MASK      0xF0
#define FMT_PAGE_ORDER_MASK       0xFF00
#define FMT_PAGE_COVER_MASK       0xFF0000
#define FMT_PAGE_ATTR_MASK        0xFF000000

#define FMT_PAGE_IS_SIDE(f)            ( (f) & FMT_PAGE_SIDE_MASK)
#define FMT_PAGE_IS_FRONT(f)           ( (f) & PAGE_TYPE_FRONT )
#define FMT_PAGE_IS_BACK(f)            ( (f) & PAGE_TYPE_BACK )

#define FMT_PAGE_IS_ORDER(f)           ( (f) & FMT_PAGE_ORDER_MASK)
#define FMT_PAGE_IS_FIRST(f)           ( (f) & PAGE_TYPE_FIRST)
#define FMT_PAGE_IS_LAST(f)            ( (f) & PAGE_TYPE_LAST)

#define FMT_PAGE_IS_COVER(f)                ( (f) & FMT_PAGE_COVER_MASK)
#define FMT_PAGE_IS_FRONT_COVER_INSIDE(f)   ( (f) & PAGE_TYPE_FRONT_COVER_INSIDE)
#define FMT_PAGE_IS_FRONT_COVER_OUTSIDE(f)  ( (f) & PAGE_TYPE_FRONT_COVER_OUTSIDE)
#define FMT_PAGE_IS_BACK_COVER_INSIDE(f)    ( (f) & PAGE_TYPE_BACK_COVER_INSIDE    )
#define FMT_PAGE_IS_BACK_COVER_OUTSIDE(f)   ( (f) & PAGE_TYPE_BACK_COVER_OUTSIDE)

#define FMT_PAGE_IS_ATTR(f)            ( (f) & FMT_PAGE_ATTR_MASK     )
#define FMT_PAGE_IS_BLANK(f)           ( (f) & PAGE_TYPE_BLANK_PAGE )
#define FMT_PAGE_IS_INSTER(f)          ( (f) & PAFE_TYPE_INSTER_PAGE)
#define FMT_PAGE_IS_SPEARATE(f)        ( (f) & PAGE_TYPE_SPEARATE_PAGE)

#define FMT_PAGE_IS_SOURCE(f)       ( (f) & FMT_PAGE_SOURCE_MASK )
#define FMT_PAGE_IS_SCAN_FB(f)      ( (f) & PAGE_TYPE_SCAN_FB )
#define FMT_PAGE_IS_SCAN_ADF(f)     ( (f) & PAGE_TYPE_SCAN_ADF)


#define BAND_BLANK      0x0000      ///< BAND is uninitialized
#define BAND_DSP        0x0001      ///< BAND is in DSP internal memory

#define BAND_RAM        0x0100      ///< BAND is in regular memory
#define BAND_SCRATCH    0x0200      ///< BAND is in scratch-pad / IPM memory

#define BAND_LOC_MASK   0x02FF      ///< mask to get location bits

#define BAND_RDY        0x1000      ///< BAND ready for processing
#define BAND_BUSY       0x2000      ///< BAND is being processed
#define BAND_DONE       0x4000      ///< BAND processing is done
#if NOT_USE
#define BAND_ERR        0x8000      ///< BAND processing encountered an error
#endif
#define BAND_STAT_MASK  0xF000      ///< mask to get BAND status

#define BANDstatus(b)           ((b)->flags & BAND_STAT_MASK)
#define BANDsetStatus(b, t)     ((b)->flags = (((b)->flags & ~BAND_STAT_MASK) | t))

#if NOT_USE
#define BANDlocation(b)         ((b)->flags & BAND_LOC_MASK)
#define BANDsetLocation(b, l)   ((b)->flags = (((b)->flags & ~BAND_LOC_MASK) | l))
#endif

typedef enum
{
    MIDDLE_BAND         = 0x00000000,
    LAST_BAND           = 0x00000001,
    FIRST_BAND          = 0x00000002,
    BAND_TYPE_MAX
}BAND_TYPE_E;

/*scan resolution by dpi enum*/
typedef enum
{
    DATA_TYPE_ORI,
    DATA_TYPE_JPEG,
    DATA_TYPE_JBIG,
    DATA_TYPE_CMYK,
    DATA_TYPE_MAX = 0xFFFFFF
}DATA_TYPE_E;

/**
 * @brief band (image) data represents one color component of an image, the pdata
 member points to the top left of the band pixels, represented by one or
 more contiguous rows of pixels.  Each pixel is one or more bytes each,
 and adding stride (s) bytes to the start of one line will point to the next
 line in a band, assuming its not the last line of course.  The lines
 in a band are always physically contiguous, even in user-memory, and
 each line in a band is always 4-byte aligned, and has a stride that is
 at least a 4-byte multiple.  Each band is in a doubly-linked vertical list,
 and that list will always hold bands of the same color component and
 dimensions.\n

 a band may be one line only, or it may represent an entire image.
 the dimensions of bands are usually the same as the image that holds
 them, but image dimension info is copied in the band structure so
 that it is handy for coprocessing without having to pass the image info\n

 band data is a reference counted object.  If multiple concurrent
 processing steps need access to the bands data (for example, a
 step may need part of a current band and part of a previous band
 while the same step might be concurrently working on the previous
 band as its current band) then the last referer is responsible for
 deleting the band object.  The refs member is used for that.  All
 bands are allocated with 0 references.  Most band processors will
 not modify the reference count.  Steps that create bands should
 insure the reference count is 0 leaving the step.  If a band is
 destroyed with a reference count of 0, the data is destroyed as
 well.  Note that calling BANDdestory(pband) will always free the
 band structure, but will only delete the band's pdata object if
 the reference count is 0.   The collator for example, can pass
 on band's with ref count > 0 for copies of pages.  It will allocate
 a band structure, set the band's data member to the data for that
 position, and the ref count to > 0.  The printer will destroy the
 band structure, but not the data.   For the last copy, the original
 band with ref count 0 is passed on the printer which will also then
 free the data.\n

 band data is allocated from an image memory allocator object, since
 large chunks of real (physical) memory need to mananged carefully.
 the band structure (described below) is regular memory and comes
 from a single global pool managed by the image subsystem.  The
 compression info structure in a band is also allocated from regular
 memory. The band's reference count also applies to any physical
 allocations in compression info chains, so destroying a band with
 refcount > 0 will not destroy physical data pointed to by any
 compression info structures.\n

 a single image processing pipeline may manage a number of allocator
 objects for a number of reasons, like avoiding fragmentation, limiting
 the size of allocations passed on to indeterminate-lifetime entities
 like a page-queue, etc.   To avoid having to manage which allocator
 is managing which band, the band itself points to its own allocator
 (which is also reference counted).  When the band's data is freed
 the reference count on the allocation object is decremented and when
 the last band from that object is freed, the allocator object
 itself is self torn-down.
 */

typedef struct tag_band
{
    struct list_head         band_list; ///< add band_member to s_scan_mgr.bands_list
    color_t                 *pdata;     ///< actual data, 32 bit aligned
    int32_t                 w;          ///< width of band, in pixels, usually == image width
    int32_t                 h;          ///< height, in lines, of band
    int32_t                 s;          ///< stride in bytes of each line usually == image stride
    int32_t                 y;          ///< y offset in image band top line represents, 0 based
    COLOR_COMPONENT_E       cc;         ///< what color the pixel data is
    int32_t                 allocz;     ///< allocation size of buffer pdata
    uint16_t                flags;      ///< whats up with this band, where its at, etc.
    int16_t                 refs;       ///< reference count (see above comments)
    void                    *parent;    ///< allocation object data is to be freed in, acture, refrence MEMOBJ_P
    COMPRESSION_INFO_P      pcompr;     ///< compression info for this band if image is compressed and needed
    struct tag_band         *prev;      ///< prev band vertically in color component chain
    struct tag_band         *next;      ///< next band vertically in color component chain

    /*user define params*/
    int32_t                 dot;              ///<
    int32_t                 band_id;          ///<
    int32_t                 use;              ///<
    BAND_TYPE_E             band_type;        ///<band type
    PAGE_TYPE_E             page_type;        ///<page_type
    DATA_TYPE_E             data_type;        ///<data_type
}BAND_S, *BAND_P;

typedef struct tag_bandocator
{
    int mobj_id;
    int bands;
    int comprs;
    int band_cnt;
    int compr_cnt;
    int used;
    int inited;
    struct init_info ii;
    key_t band_shm_key;
    size_t band_shm_size;
    key_t compr_shm_key;
    size_t compr_shm_size;
    pthread_mutexattr_t mutexattr;
    pthread_mutex_t mutex;
} MEMOBJ_S, *MEMOBJ_P;

typedef enum
{
    cfUNSPECIFIED   = 0,

    cfRGB           = 1,            ///< image is RGB
    cfCMYK          = 2,            ///< image is CMYK
    cfYCbCr         = 4,            ///< image is YCC
    cfY             = 8,            ///< image is contone Luminance (0=black)
    cfMono          = 8,            ///< alias for cfY

    cfPLANAR            = 0x100,    ///< image is planar
    cfPIXELINTERLEAVED  = 0x200,    ///< image is pixel-by-pixel interleaved
    cfLINEINTERLEAVED   = 0x400,    ///< image has one of line of each color component
    cfCOLORORDER        = 0x800,    ///< image is B-G-R order (on) if it's not planar, default is R-G-B (off)

    cfCOMPRESSED        = 0x1000,   ///< image is compressed, bands have compression headers
    cfBIGENDIAN         = 0x2000,   ///< image is big-endian, default is litte endian
    cfMIRRORED          = 0x4000,   ///< image is oriented right-to-left (mirrored), default is left to right
    cfBITMIRRORED       = 0x8000,   ///< default is bit 31 is left bit, bit 0 is right bit
    cfTAGGED            = 0x10000,  ///< image has a tag plane associated with it


    cfRGBi          = (cfPIXELINTERLEAVED + cfRGB),
    cfRGBl          = (cfLINEINTERLEAVED + cfRGB),
    cfRGBp          = (cfPLANAR + cfRGB),
    cfYCCi          = (cfPIXELINTERLEAVED + cfYCbCr),
    cfYCCp          = (cfPLANAR + cfYCbCr),
    cfCMYKi         = (cfPIXELINTERLEAVED + cfCMYK),
    cfCMYKp         = (cfPLANAR + cfCMYK),
    cfMonoP         = (cfPLANAR + cfY)
}COLOR_FORMAT_E;

typedef struct tag_bandlist
{
    COLOR_COMPONENT_E       cc;            ///< color component band represents
    BAND_P                pbands;        ///< list of bands vertically
    struct tag_bandlist *next;          ///< next color component head (horizontal)
}BANDHEAD_S, *BANDHEAD_P;


#define MAX_IMAGE_COMPONENTS 8

typedef enum
{
    IMAGE_ORIENTATION_PORTRAIT            = 0,
    IMAGE_ORIENTATION_LANDSPACE,
    IMAGE_ORIENTATION_INVALID             = 0xFF,                       ///< please keep it in the end of the enum --- invalid data
}IMAGE_ORIENTATION_E;

typedef struct tag_image
{
    int32_t                         w, h, d, s;                         ///< width, height, depth, byte-stride
    COLOR_FORMAT_E                  f;                                  ///< color format
    int32_t                         bandh;                              ///< height of each band in lists, or preferred at least
    int16_t                         xres;                               ///< x direction resolution by dpi, please refer to RESOLUTION_E for mode details
    int16_t                         yres;                               ///< y direction resolution by dpi, please refer to RESOLUTION_E for mode details
    int32_t                         collate;                            ///< Image need collate with flags,such as rotate
    PI_MUTEX_T                      lock;                               ///< mutex for access to band lists and other internals
    struct tag_image                *next_alloc;                        ///< for micro-allocation of this object
    BANDHEAD_P                      pbandheads;                         ///< start of color-component ordered list of band lists
    BANDHEAD_P                      pfreeheads;                         ///< nano-allocator list
    BANDHEAD_S                      bands[MAX_IMAGE_COMPONENTS];        ///< nano-alloc pool
    COMPRESSION_TYPE_E              type;                               ///<page compression method
    //IMAGE_ORIENTATION_E             image_orientation;
    PAGE_TYPE_E                     page_type;
    int32_t                         page_num;                           ///<1~ffff
    unsigned int                    left_skip_pixel;                    ///< rotate pad pixel fro video use
    unsigned int                    right_skip_pixel;                   ///< rotate pad pixel fro video use
    unsigned int                    top_skip_line;                      ///< rotate pad pixel fro video use
    unsigned int                    down_skip_line;                     ///< rotate pad pixel fro video use
}IMAGE_S, *IMAGE_P;


#define FMT_COLORMASK       0x00FF
#define FMT_TYPEMASK        0x07FF
#define FMT_FLAGSMASK       0xFF00
#define FMT_COLOR(f)        ((f) & FMT_COLORMASK)
#define FMT_COLORPLANES(f)  (((f) & cfPLANAR)?((((f) & FMT_COLORMASK) == cfY) ? 1 : ((((f) & FMT_COLORMASK) == cfCMYK) ? 4 : 3)):1)

/**
 * @brief  get free mem size of the mem object
 * @param[in] pmobj:      which point to mem cache pool object you want to query
 * @param[in] list_mode:specfic the mode
 * @return  free size
 * @retval >0:  free mem size \n
 *         <0:  fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_get_free_size(MEMOBJ_P pmobj, int list_mode);

/**
 * @brief  get free mem size of ptmem
 * @return  free size
 * @retval >0:  free mem size \n
 *         <0:  fail
 * <AUTHOR>
 * @data   2023-4-18
*/
uint32_t pi_ptmem_get_free_size(void);

/**
 * @brief  query if there is enough mem in the mem cache pool
 * @param[in] pmobj    :which point to mem cache pool object you want to query
 * @param[in] size   :mem size to query
 * @param[in] timeout: if >0 the func will retun until has enough mem or over time, the minimum time intervel is 100ms\n
 *  if <=0  the func will return soon
 * @return  query result
 * @retval 0: if free mem >= query mem \n
 *         -1: if free mem < query mem
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_wait_enough_size(MEMOBJ_P pmobj, size_t size, time_t timeout);

/**
 * @brief  enforcement of merging the free list of  MEMOBJ_P object
 * @param[in] pmobj    : specfied the pmobj to merge  which created by pi_imagemem_create
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_imagemem_merge_list(MEMOBJ_P pmobj);

/**
 * @brief  get the max free memblock size  of the MEMOBJ_P
 * @param[in] pmobj    :specfied MEMOBJ_P object  which created by pi_imagemem_create
 * @param[in] size   :mem size to query
 * @param[in] timeout: if >0 the func will retun until has enough mem or over time, the minimum time intervel is 100ms\n
 *  if <=0  the func will return soon
 * @return  the max free memblock size of MEMOBJ_P
 * <AUTHOR>
 * @data   2023-4-18
*/
size_t pi_imagemem_get_maxblocksize(MEMOBJ_P pmobj, int list_mode);

/**
 * @brief  create imagemem object  and  cachepool initialize according to init params,  pair with pi_imagemem_destory
 * @param[in] info    :for init the second cache pool
 * @param[in] bands   :the max band count with the band pool and compr_pool\n
 * @param[in] timeout: if >0 the func will retun until have enough mem or over time, the minimum time intervel is 100ms\n
 *  if <=0  the func will return soon whether alloc success or not
 * @return  imagemem object handle
 * @retval !NULL: create success \n
 *         NULL: create fail \n
 * <AUTHOR>
 * @data   2023-4-18
*/
MEMOBJ_P pi_imagemem_create(struct init_info info, int32_t bands, time_t timeout);

/**
 * @brief  destroy the imagemem object ,and release the conresponding second-level cachepool,pair with pi_imagemem_create
 * @param[in] pmobj    :the imagemem object handler which is created by pi_imagemem_create
 * @return  destroy result
 * @retval 0: success \n
 *         -1: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_destory(MEMOBJ_P pmobj);

/**
 * @brief  memcpy the physical memory addr
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] to    :specfied phyaddr address of the target memory
 * @param[in] from  :specfied phyaddr address of the source memory
 * @param[in] size  :specfied the size of memory area to memcpy
 * @return  memcpy result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_memcpy(MEMOBJ_P pmobj, addr_t to, addr_t from, size_t size);

/**
 * @brief  memset the physical memory addr
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] src   :specfied phyaddr address of the memory to memset
 * @param[in] val   :specfied memset value
 * @param[in] size  :specfied the size of memory area to memset
 * @return  memset result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_memset(MEMOBJ_P pmobj, addr_t src, int32_t val, size_t size);

/**
 * @brief  copy form kernel space with physical addr to user space  virtual addr
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] phy   :specfied kernel phyaddr address of the memory copy from
 * @param[in] vir   :specfied user virtual address of memory copy to
 * @param[in] size  :specfied the size of memory area to copy
 * @return  copy result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_copytouser(MEMOBJ_P pmobj, addr_t phy, addr_t vir, size_t size);

/**
 * @brief  copy form user space with  virtual addr to kernel space with physical addr
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] phy   :specfied kernel phyaddr address of the memory copy to
 * @param[in] vir   :specfied user virtual address of memory copy from
 * @param[in] size  :specfied the size of memory area to copy
 * @return  copy result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_copyfromuser(MEMOBJ_P pmobj, addr_t vir, addr_t phy, size_t size);


/**
 * @brief  flush  cache data to ddr
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] phy   :specfied  phyaddr address of the ddr
 * @param[in] size  :specfied the size of memory area to flush
 * @return flush result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_flush_cache(MEMOBJ_P pmobj, addr_t phy, size_t size);

/**
 * @brief  flush  cache data  invalidate
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] phy   :specfied  phyaddr address of the ddr
 * @param[in] size  :specfied the size of memory area to flush
 * @return flush invalidate result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_imagemem_flush_invalidate(MEMOBJ_P pmobj, addr_t phy, size_t size);

/**
 * @brief  flush  cache data  invalidate
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] phy   :specfied  phyaddr address of the ddr
 * @param[in] size  :specfied the size of memory area to flush
 * @return flush invalidate result, if fail with errno
 * @retval 0:  on success \n
 *         -1: on error and errno is set appropriately
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t  pi_imagemem_flush(MEMOBJ_P pmobj, addr_t phy, size_t size);
int32_t  pi_imagemem_invalidate(MEMOBJ_P pmobj, addr_t phy, size_t size);

/**
 * @brief  convert virtual address to physical address
 * @param[in] fd    :the open file description of /dev/ptmem
 * @param[in] vir   :specfied user virtual address of memory copy from
 * @return physical  addr
 * <AUTHOR>
 * @data   2023-4-18
*/
addr_t pi_imagemem_vir_2_phys(MEMOBJ_P pmobj, addr_t vir);

/**
 * @brief  module init
 * <AUTHOR>
 * @data   2025-1-11
*/
void pi_imagemem_init(void);

/**
 * @brief  get imagemem object id
 * @param[in] pmobj: the pointer to the imagemem object
 * @return imagemem object id
 * <AUTHOR>
 * @data   2025-1-11
*/
int pi_imagemem_get_memobj_id(MEMOBJ_P pmobj);

/**
 * @brief  get the pointer of imagemem object by id, pair with pi_imagemem_put_memobj
 * @param[in] memobj_id : imagemem object id
 * @return  the pointer of imagemem object
 * @retval !NULL: get success \n
 *         NULL: get fail \n
 * <AUTHOR>
 * @data   2025-1-11
*/
MEMOBJ_P pi_imagemem_get_memobj(int memobj_id);

/**
 * @brief  put back the pointer to the imagemem object, pair with pi_imagemem_get_memobj
 * @param[in] pmobj: the pointer to the imagemem object
 * <AUTHOR>
 * @data   2025-1-11
*/
void pi_imagemem_put_memobj(MEMOBJ_P pmobj);

/**
 * @brief  zalloc the memory from second-level cache pool which is managed by imagemem object ,pair with _imagemem_free
 * @param[in] pmobj: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] size: the size of memory to alloc
 * @param[in] mode: free list mode , SINGLE_LIST or MULTI_LIST mode
 * @param[in] cached: map with cache or not, if don't map to user address, the param is invalid
 * @param[in] func: call function name
 * @param[in] line: call line
 * @return the physical address of memory malloc
 * @retval !NULL: the physical address of  memory if malloc success \n
 *          NULL: malloc fail
 * <AUTHOR>
 * @data   2023-4-18
*/
void *_imagemem_zalloc(MEMOBJ_P pmobj, size_t size, FREE_LIST_MODE_E mode, int32_t cached, const char *func, int32_t line);

/**
 * @brief  free the imagemem object ,pair with _imagemem_malloc
 * @param[in] pmobj    :the imagemem object handler which is created by pi_imagemem_create
 * @param[in] phyaddr: the physical addr to free
 * <AUTHOR>
 * @data   2023-4-18
*/
void _imagemem_free(MEMOBJ_P pmobj, void *phyaddr);

/**
 * @brief  create a new mapping in the virtual address space of calling processing for the physical address\n
 * pair with pi_imagemem_unmap
 * @param[in] a:the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the physical addr of mapping
 * @param[in] c: memory length specfied to mapping
 * @return  mapping result
 * @retval !NULL: the virtual address of mapping if mmaping success \n
 *          NULL: mapping fail
 * <AUTHOR>
 * @data   2023-4-18
*/
void *_imagemem_mmap(MEMOBJ_P pmobj, void *phyaddr, size_t size);
#define pi_imagemem_mmap(a, b, c) _imagemem_mmap(a, b, c)

/**
 * @brief  delete the mappings for the specfied address range  ,pair with pi_imagemem_mmap
 * @param[in] a: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the virtual addr specfied to unmap
 * @param[in] c: the memory length specfied to unmmap
 * @return unmmap result
 * @retval 0:  unmap success \n
 *      <0: unmap fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t _imagemem_unmap(MEMOBJ_P pmobj, void *viraddr, size_t size);
#define pi_imagemem_unmap(a, b, c) _imagemem_unmap(a, b, c)
/**
 * @brief  malloc the memory from second-level cache pool which is managed by imagemem object ,pair with pi_imagemem_free
 * @param[in] a: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the size of memory to alloc
 * @param[in] c: free list mode , SINGLE_LIST or MULTI_LIST mode
 * @param[in] d: map with cache or not, if don't map to user address, the param is invalid
 * @return the physical address of memory malloc
 * @retval !NULL: the physical address of  memory if malloc success \n
 *          NULL: malloc fail
 * <AUTHOR>
 * @data   2023-4-18
*/
void *_imagemem_malloc(MEMOBJ_P pmobj, size_t size, FREE_LIST_MODE_E mode, int32_t cached, const char *func, int32_t line);
#define pi_imagemem_malloc(a, b, c, d) _imagemem_malloc(a, b, c, d, __func__, __LINE__)

/**
 * @brief  free the imagemem object ,pair with pi_imagemem_malloc
 * @param[in] a    :the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the physical addr to free
 * <AUTHOR>
 * @data   2023-4-18
*/
#define pi_imagemem_free(a, b) _imagemem_free(a, b)

/**
 * @brief  malloc the memory from second-level cache pool which is managed by imagemem object ,pair with pi_pix_free
 * @param[in] a:    :the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the size of memory to alloc
 * @param[in] c: free list mode , SINGLE_LIST or MULTI_LIST mode
 * @param[in] d: map with cache or not, if don't map to user address, the param is invalid
 * @return the physical address of memory malloc
 * @retval !NULL: the physical address of  memory if malloc success \n
 *          NULL: malloc fail
 * <AUTHOR>
 * @data   2023-4-18
*/
color_t *_pix_alloc(MEMOBJ_P pmobj, int32_t size, FREE_LIST_MODE_E mode, int32_t cached, const char *func, int32_t line);
#define pi_pix_alloc(a, b, c, d) _pix_alloc(a, b, c, d, __func__, __LINE__)

/**
 * @brief  zalloc the memory from second-level cache pool which is managed by imagemem object ,pair with pi_pix_free
 * @param[in] a: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the size of memory to alloc
 * @param[in] c: free list mode , SINGLE_LIST or MULTI_LIST mode
 * @param[in] d: map with cache or not, if don't map to user address, the param is invalid
 * @return the physical address of memory malloc
 * @retval !NULL: the physical address of  memory if malloc success \n
 *          NULL: malloc fail
 * <AUTHOR>
 * @data   2023-4-18
*/
color_t *_pix_zalloc(MEMOBJ_P pmobj, int32_t size, FREE_LIST_MODE_E mode, int32_t cached, const char *func, int32_t line);
#define pi_pix_alloc_memset(a, b, c, d) _pix_zalloc(a, b, c, d, __func__, __LINE__)

/**
 * @brief  free the imagemem object ,pair with pi_pix_alloc
 * @param[in] a:the imagemem object handler which is created by pi_imagemem_create
 * @param[in] b: the physical addr to free
 * <AUTHOR>
 * @data   2023-4-18
*/
void _pix_free(MEMOBJ_P pmobj,color_t *pd);
#define pi_pix_free(a, b) _pix_free(a, b)

/**
 * @brief  alloc a band structure and initializethe memory from imagemem object ,pair with pi_band_destroy
 * @param[in] pmobj: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] pData: band data pointer
 * @param[in] w: width of band, in pixels, usually == image width
 * @param[in] h: height of band, in lines, of band
 * @param[in] s: stride in bytes of each line usually == image stride
 * @param[in] y: y-offset of the band from sheet top,0 based
 * @param[in] z: allocation size of buffer pdata
 * @param[in] dot: reserved,unused
 * @param[in] cc: what color the pixel data is
 * @return the band pointer
 * @retval !NULL: the address of band if malloc success \n
 *          NULL: malloc fail
 * <AUTHOR>
 * @data   2023-4-18
*/
BAND_P pi_band_create(MEMOBJ_P pmobj, color_t *pData, int32_t w, int32_t h, int32_t s, int32_t y, int32_t z, int32_t dot, COLOR_COMPONENT_E cc);

/**
 * @brief  free the band  ,pair with pi_band_create
 * @param[in] pb: pointer of band to free
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_band_destroy(BAND_P pb);

/**
 * @brief  output the band info
 * @param[in] pb: pointer of band to output
 * @param[in] lable:lable string
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_band_report(BAND_P pb, const char *label);

/**
 * @brief  get the pointer of band by id,pair with pi_band_put
 * @param[in] pmobj: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] band_id: band id
 * @return the pointer of band
 * @retval !NULL: get success \n
 *          NULL: get fail
 * <AUTHOR>
 * @data   2025-1-11
*/
BAND_P pi_band_get(MEMOBJ_P pmobj, int band_id);

/**
 * @brief  put back the pointer to the band,pair with pi_band_get
 * @param[in] pband: the pointer to the band
 * <AUTHOR>
 * @data   2025-1-11
*/
void pi_band_put(BAND_P pband);

/**
 * @brief  get band id
 * @param[in] pband: the pointer to the band
 * @return the band id
 * <AUTHOR>
 * @data   2025-1-11
*/
int pi_band_get_band_id(BAND_P pband);

/**
 * @brief  alloc a Compression structure from imagemem object ,pair with pi_compr_destroy
 * @param[in] pmobj: the imagemem object handler which is created by pi_imagemem_create
 * @return the Compression pointer
 * @retval !NULL: the address of band if malloc success \n
 *          NULL: malloc fail
 * <AUTHOR>
 * @data   2023-4-18
*/
COMPRESSION_INFO_P pi_compr_create(MEMOBJ_P pmobj);

/**
 * @brief  free the compr ,pair with pi_compr_create_create
 * @param[in] pmobj: the imagemem object handler which is created by pi_imagemem_create
 * @param[in] pc: pointer of Compression to free
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_compr_destroy(MEMOBJ_P pmobj, COMPRESSION_INFO_P pc);

/**
 * @brief  clean up an image, removes bandlists, etc., but leave open
 * @param[in] pimg: the pointer of image to open
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_cleanup(IMAGE_P pimg);

/**
 * @brief  close an image for use, destroys lock and cleans up
 * @param[in] pimg: the pointer of image to close
 * @return close result
 * @retval 0: success \n
 *         !0:fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_close(IMAGE_P pimg);

/**
 * @brief  open an image for use, creates a lock, and calls cleanup
 * @param[in] pimg: the pointer of image to open
 * @return open result
 * @retval 0: success \n
 *         !0:fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_open(IMAGE_P pimg);

/**
 * @brief  append band(s) of a single plane to an image
 * @param[in] pimgdst: the pointer of image to append
 * @param[in] pb: the pointer of band or bandlist
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_append_bands(IMAGE_P pimgdst, BAND_P pb);

/**
 * @brief  insert a single band into image at appropriate Y location, or append if only band of color
 * @param[in] pimgdst: the pointer of image to append
 * @param[in] pb: the pointer of band
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_insert_band(IMAGE_P pimgdst, BAND_P pb);

/**
 * @brief  move a single band from one image to another
 * @param[in] pimgdst: the pointer of target image
 * @param[in] pimgsrc: the pointer of source image
 * @param[in] pb: the pointer of band
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_move_band(IMAGE_P pimgdst, IMAGE_P pimgsrc, BAND_P pb);

/**
 * @brief  remove a band from one image
 * @param[in] pimgsrc: the pointer of source image
 * @param[in] pb: the pointer of band
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_remove_band(IMAGE_P pimgsrc, BAND_P pb);

/**
 * @brief  move all bands of color cc from one image to another
 * @param[in] pimgdst: the pointer of target image
 * @param[in] pimgsrc: the pointer of source image
 * @param[in] cc: the color cc to specfic
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_move_bands(IMAGE_P pimgdst, IMAGE_P pimgsrc, COLOR_COMPONENT_E cc);

/**
 * @brief  move all bands from one image to another,assumes images are already locked with their mutex
 * @param[in] pimgdst: the pointer of target image
 * @param[in] pimgsrc: the pointer of source image
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_move_bands_without_lock(IMAGE_P pimgdst, IMAGE_P pimgsrc);

/**
 * @brief  move all bands from one image to another
 * @param[in] pimgdst: the pointer of target image
 * @param[in] pimgsrc: the pointer of source image
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_move_allbands(IMAGE_P pimgdst, IMAGE_P pimgsrc);

/**
 * @brief find an existing band of y coordinate Y and color cc
 * @param[in] pimg: the pointer of image
 * @param[in] y: the y offset of band to specfic
 * @param[in] cc: the cc color to specfic
 * @return the band pointer
 * @retval NULL: fail \n
 *         !NULL:success
 * <AUTHOR>
 * @data   2023-4-18
*/
BAND_P pi_image_find_band(IMAGE_P pimg, int32_t y, COLOR_COMPONENT_E cc);

/**
 * @brief find an existing band which include the specficed line  and specfied color cc
 * @param[in] pimg: the pointer of image
 * @param[in] line: the y coordinate in the plane to specfic
 * @param[in] cc: the cc color to specfic
 * @return band pointer
 * @retval NULL: fail \n
 *         !NULL:success
 * <AUTHOR>
 * @data   2023-4-18
*/
BAND_P pi_image_find_band_by_line(IMAGE_P pimg, int32_t line, COLOR_COMPONENT_E cc);

/**
 * @brief pick a existing bands of specficed image
 * @param[in] pimg: the pointer of image
 * @return band pointer
 * @retval NULL: fail \n
 *         !NULL:success
 * <AUTHOR>
 * @data   2023-4-18
*/
BAND_P pi_image_pick_bands(IMAGE_P pimg);

/**
 * @brief count bands of color component cc in image
 * @param[in] pimg: the pointer of image
 * @param[in] cc: the cc color to specfic
 * @return counts
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_count_bands(IMAGE_P pimg, COLOR_COMPONENT_E cc);

/**
 * @brief count bands of specficed y offset in image
 * @param[in] pimg: the pointer of image
 * @param[in] y: the y-offset of band  to specfic
 * @return counts of bands
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_count_bandy(IMAGE_P pimg, int32_t y);

/**
 * @brief count all bands height of color component cc in image
 * @param[in] pimg: the pointer of image
 * @param[in] cc: the cc color to specfic
 * @return counts of height
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_count_height(IMAGE_P pimg, COLOR_COMPONENT_E cc);
/**
 * @brief count total bands in one image
 * @param[in] pimg: the pointer of image
 * @return counts of bands
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_count_all_bands(IMAGE_P pimg);

/**
 * @brief count rows of bands in image
 * @param[in] pimg: the pointer of image
 * @return counts of rows
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_count_rows(IMAGE_P pimg);

/**
 * @brief whether the image has pages number page
 * @param[in] pimg: the pointer of image
 * @param[in] pages: image pages number
 * @return result
 * @retval 1: yes \n
 *         0: no \n
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_is_imgpages(IMAGE_P pimg, int32_t pages);

/**
 * @brief whether the image has bands
 * @param[in] pimg: the pointer of image
 * @return result
 * @retval 1: yes \n
 *         0: no \n
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_has_bands(IMAGE_P pimg);

/**
 * @brief returns non-zero if there is a complete Y coherent row of bands in an image,based on its format, with each band of status withStatus if withStatus is non-zero, and also checks Y coherency.
 * @param[in] pimg: the pointer of image
 * @param[in] with_status: specfied band status
 * @return result
 * @retval 1: yes \n
 *         0: no \n
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_image_has_row(IMAGE_P pimg, int32_t with_status);

/**
 * @brief delete plane from image
 * @param[in] pimg: the pointer of image
 * @param[in] cc: the cc color to specfic
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_destroy_plane(IMAGE_P pimg, COLOR_COMPONENT_E cc);

/**
 * @brief  create an image for object
 * @return image pointer
 * @retval NULL:Fail \n
 *         !NULL: image pointer
 * <AUTHOR>
 * @data   2023-4-18
*/
IMAGE_P pi_image_create(void);

/**
 * @brief delete an  image
 * @param[in] pimg: the pointer of image
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_destroy(IMAGE_P pimg);

/**
 * @brief print out band and memory use of an image
 * @param[in] pimg: the pointer of image
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_dump_stats(IMAGE_P pimg);

/**
 * @brief print out contents (bands) of an image
 * @param[in] pimg: the pointer of image
 * @param[in] rows: the rows to output
 * @param[in] verbose:1-output band info; 0-not output band info
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_dump_contents(IMAGE_P pimg, int32_t rows, int32_t verbose);

/**
 * @brief dump/report image object info/data
 * @param[in] pimg: the pointer of image
 * @param[in] lable:lable string
 * @param[in] dumpbandstoo:1-output band info; 0-not output band info
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_report(IMAGE_P pimg, const char* label, int32_t dumpbandstoo);

/**
 * @brief print out band dimensions info
 * @param[in] pb: the pointer of band
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_image_dump_band(BAND_P pb);

/**
 * @brief print out band dimensions info
 * @param[in] pb: the pointer of pmobj
 * <AUTHOR>
 * @data   2025-1-13
*/
void pi_image_dump_band2(MEMOBJ_P pmobj);

/**
 * @brief return Name of color component
 * <AUTHOR>
 * @data   2023-4-18
*/
const char* pi_image_color_component_string(COLOR_COMPONENT_E cc);

/**
 * @brief print out ptmem info
 * <AUTHOR>
 * @data   2023-4-18
*/
void pi_ptmem_dump_info(void);

/**
 * @brief  output the memobj info
 * <AUTHOR>
 * @data   2024-4-17
*/
void pi_imagemem_dump_memobj(void);

/**
 * @brief  release memory
 * <AUTHOR>
 * @data   2024-12-04
*/
void pi_imagemem_release(void);

/**
 * @brief  release reserved memory
 * <AUTHOR>
 * @data   2025-05-13
*/
void pi_imagemem_release2(void);

/**
 * @brief  recycle multi list
 * @param[in] pmobj: the imagemem object handler which is created by pi_imagemem_create
 * <AUTHOR>
 * @data   2024-12-04
*/
void pi_imagemem_recycle_multi_list(MEMOBJ_P pmobj);


#define SYNC()     asm volatile("DSB")

PT_END_DECLS
#endif
/**
 *@}
 */
