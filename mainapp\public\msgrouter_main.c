/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file msgrouter_main.c
 * @addtogroup msgrouter_main
 * @{
 * <AUTHOR>
 * @date 2023-5-25
 * @brief msgrouter app interface
 **/
#include <msgrouter_main.h>

#define NAME_OUT1(n, v)     #n,
#define NAME_OUT2(n)        #n,

#undef  NAME_OUT1
#undef  NAME_OUT2

#define  RETURN_STRING_OF_ENUM( eid ) \
    case eid: \
        return #eid;



__attribute__((unused))  const char* get_module_id_string(MODULE_ID_E module_id)
{
    switch(module_id)
    {
    RETURN_STRING_OF_ENUM(MID_NULL)
    RETURN_STRING_OF_ENUM(MID_TONER_CORE)
    RETURN_STRING_OF_ENUM(MID_SSCDC2K)
    RETURN_STRING_OF_ENUM(MID_SSCINFOINCHIP)
    RETURN_STRING_OF_ENUM(MID_SSCFORWARDTHREAD)
    RETURN_STRING_OF_ENUM(MID_SSCCHECKTHREAD)

    /*port modules */
    RETURN_STRING_OF_ENUM(MID_PORT_USB)
    RETURN_STRING_OF_ENUM(MID_PORT_NET)
    RETURN_STRING_OF_ENUM(MID_PORT_WIFI)
    RETURN_STRING_OF_ENUM(MID_PORT_FILE)
    RETURN_STRING_OF_ENUM(MID_PORT_RAM)
    RETURN_STRING_OF_ENUM(MID_PORT_EMAIL)
    RETURN_STRING_OF_ENUM(MID_PORT_FAX_EMAIL)
    RETURN_STRING_OF_ENUM(MID_PORT_FTP)
    RETURN_STRING_OF_ENUM(MID_PORT_IPP)

    RETURN_STRING_OF_ENUM(MID_CONNECT_MGR)

    RETURN_STRING_OF_ENUM(MID_SYS_JOB_MGR)
    RETURN_STRING_OF_ENUM(MID_SYS_STATUS_MGR)

    /*print modules*/
    RETURN_STRING_OF_ENUM(MID_PRINT_JOB_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_JOB_DATA_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_JOB_CTRL_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_CONSUMABLE)
    RETURN_STRING_OF_ENUM(MID_PRINT_VIDEO_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_VIDEO_DRIVER)
    RETURN_STRING_OF_ENUM(MID_PRINT_VIDEO_READY)
    RETURN_STRING_OF_ENUM(MID_PRINT_VIDEO_REAP)
    RETURN_STRING_OF_ENUM(MID_PRINT_STATISTICS)
    RETURN_STRING_OF_ENUM(MID_PRINT_STATUS_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_TRAY_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_ENGINE_CONFIG)
    RETURN_STRING_OF_ENUM(MID_PRINT_ENGINE_CAL)

    RETURN_STRING_OF_ENUM(MID_PRINT_FRAMEWORK)
    RETURN_STRING_OF_ENUM(MID_PRINT_CTL)
    RETURN_STRING_OF_ENUM(MID_PRINT_DATA)
    RETURN_STRING_OF_ENUM(MID_PRINT_IF)
    RETURN_STRING_OF_ENUM(MID_PRINT_PARSER_IF)
    RETURN_STRING_OF_ENUM(MID_PRINT_PRE)
    RETURN_STRING_OF_ENUM(MID_PRINT_RELAY_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_RELAY_RECEIVE)
    RETURN_STRING_OF_ENUM(MID_PRINT_RELAY_WRITE)
    RETURN_STRING_OF_ENUM(MID_PRINT_RELAY_READ)
    RETURN_STRING_OF_ENUM(MID_PRINT_PROCESS)
    RETURN_STRING_OF_ENUM(MID_PRINT_DEBUG)
    RETURN_STRING_OF_ENUM(MID_PRINT_FINISHER)
    RETURN_STRING_OF_ENUM(MID_PRINT_STATUS)
    RETURN_STRING_OF_ENUM(MID_PRINT_INFO)
    RETURN_STRING_OF_ENUM(MID_PRINT_APP_STATUS)
    RETURN_STRING_OF_ENUM(MID_PRINT_STORAGE)
    RETURN_STRING_OF_ENUM(MID_PRINT_DATA_IMAGE_SAVE)
    RETURN_STRING_OF_ENUM(MID_PRINT_DATA_IMAGE_SEND)
    RETURN_STRING_OF_ENUM(MID_PRINT_DATA_IMAGE_CANCEL)

    /*engine modules*/
    RETURN_STRING_OF_ENUM(MID_PRINT_ENGINE_MGR)
    RETURN_STRING_OF_ENUM(MID_PRINT_ENGINE_PROCESS_MGR)

    /*parser modules */
    RETURN_STRING_OF_ENUM(MID_PARSER_PJL)
    RETURN_STRING_OF_ENUM(MID_PARSER_CGDI)
    RETURN_STRING_OF_ENUM(MID_PARSER_MGDI)
    RETURN_STRING_OF_ENUM(MID_PARSER_IPS)
    RETURN_STRING_OF_ENUM(MID_PARSER_ACL)
    RETURN_STRING_OF_ENUM(MID_PARSER_PWG)
    RETURN_STRING_OF_ENUM(MID_PARSER_URF)
    RETURN_STRING_OF_ENUM(MID_PARSER_FPL)
    RETURN_STRING_OF_ENUM(MID_PARSER_IMAGE)
    RETURN_STRING_OF_ENUM(MID_PARSER_JPEG)
    RETURN_STRING_OF_ENUM(MID_PARSER_CGDI_ACR)
    RETURN_STRING_OF_ENUM(MID_PARSER_PINCODE)
    RETURN_STRING_OF_ENUM(MID_PARSER_IF_PWG)
    RETURN_STRING_OF_ENUM(MID_PARSER_IF_URF)
    RETURN_STRING_OF_ENUM(MID_PARSER_SAMPLE)
    RETURN_STRING_OF_ENUM(MID_PARSER_STATUS)

    /*scan modules */
    RETURN_STRING_OF_ENUM(MID_SCAN_JOB_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_TEST_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_TEST_ENGINE)
    RETURN_STRING_OF_ENUM(MID_SCAN_PARSER)
    RETURN_STRING_OF_ENUM(MID_SCAN_PARSER_NEW)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_HOST)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_UDISK)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_FTP)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_SMB)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_EMAIL)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_AIRSCAN)
    RETURN_STRING_OF_ENUM(MID_SCAN_OUT_WSDSCAN)
    RETURN_STRING_OF_ENUM(MID_SCAN_ADF_TRAY)
    RETURN_STRING_OF_ENUM(MID_SCAN_STATISTICS)
    RETURN_STRING_OF_ENUM(MID_SCAN_STATUS_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_ENGINE_TEST)
    RETURN_STRING_OF_ENUM(MID_SCAN_ENGINE_ACK)
    RETURN_STRING_OF_ENUM(MID_SCAN_ENGINE_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_INTERFACE_TEST)
    RETURN_STRING_OF_ENUM(MID_SCAN_INTERFACE_ACK)
    RETURN_STRING_OF_ENUM(MID_SCAN_INTERFACE_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_DEBUG_MGR)
    RETURN_STRING_OF_ENUM(MID_SCAN_PCIE_RCV_DEBUG)
    RETURN_STRING_OF_ENUM(MID_SCAN_PCIE_NTF_DEBUG)
    /*copy modules */
    RETURN_STRING_OF_ENUM(MID_COPY_JOB_MGR)
    RETURN_STRING_OF_ENUM(MID_COPY_MGR)
    RETURN_STRING_OF_ENUM(MID_COPY_STATISTICS)
    RETURN_STRING_OF_ENUM(MID_COPY_STATUS_MGR)
    RETURN_STRING_OF_ENUM(MID_COPY_DEBUG)

    RETURN_STRING_OF_ENUM(MID_IMAGE_SAVE)
    RETURN_STRING_OF_ENUM(MID_IMAGE_SEND)
    RETURN_STRING_OF_ENUM(MID_IMAGE_CANCEL)

    /*fax modules*/
    RETURN_STRING_OF_ENUM(MID_FAX_JOB_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_STATISTICS)
    RETURN_STRING_OF_ENUM(MID_FAX_PRN_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_PRN)
    RETURN_STRING_OF_ENUM(MID_FAX_SCN_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_SCN_DATA_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_STATUS_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_PARSER_MGR)
    RETURN_STRING_OF_ENUM(MID_FAX_REPORT)
    RETURN_STRING_OF_ENUM(MID_AIRFAX_MGR)

    /*image process modules */
    RETURN_STRING_OF_ENUM(MID_IMAGE_PROCESS)
    RETURN_STRING_OF_ENUM(MID_PIP_PROCESS)
    RETURN_STRING_OF_ENUM(MID_CORRECT_PROCESS)
    RETURN_STRING_OF_ENUM(MID_PIP_FRONT_PAGE_MGR)
    RETURN_STRING_OF_ENUM(MID_PIP_BACK_PAGE_MGR)
    RETURN_STRING_OF_ENUM(MID_ROTATE_PROCESS)
    RETURN_STRING_OF_ENUM(MID_JPEG_PROCESS)
    RETURN_STRING_OF_ENUM(MID_JPEG_PROCESS_INSIDE)

    RETURN_STRING_OF_ENUM(MID_EMMC_CACHE)

    /*other modules */
    RETURN_STRING_OF_ENUM(MID_LOW_POWER)
    RETURN_STRING_OF_ENUM(MID_PLATFORM)
    RETURN_STRING_OF_ENUM(MID_INTERNAL_PAGE)
    RETURN_STRING_OF_ENUM(MID_INTERNAL_PAGE_STORE)
    RETURN_STRING_OF_ENUM(MID_NETWORK)
    RETURN_STRING_OF_ENUM(MID_NET_GEN_REPORT)

    /*ui module */
    RETURN_STRING_OF_ENUM(MID_PANEL)

    RETURN_STRING_OF_ENUM(MID_FINGER)
    RETURN_STRING_OF_ENUM(MID_FINGER_STATUS_MGR)

    RETURN_STRING_OF_ENUM(MID_CONSUMABLE)
    RETURN_STRING_OF_ENUM(MID_CARTRIDGE)
    RETURN_STRING_OF_ENUM(MID_TONER)
    RETURN_STRING_OF_ENUM(MID_AIRSCAN)
    RETURN_STRING_OF_ENUM(MID_WSDSCAN)
    RETURN_STRING_OF_ENUM(MID_MANAGESERVER)
    RETURN_STRING_OF_ENUM(MID_CARDSERVER)

    RETURN_STRING_OF_ENUM(MID_GCP)

    /*pincode module */
    RETURN_STRING_OF_ENUM(MID_PINCODE_PARSER)
    RETURN_STRING_OF_ENUM(MID_PINCODE_PRN)

    /*udiskprint module*/
    RETURN_STRING_OF_ENUM(MID_UDISK_PRN)

    /* JBIG */
    RETURN_STRING_OF_ENUM(MID_JBIG_WORK_THREAD)
    RETURN_STRING_OF_ENUM(MID_JBIG_INPUT_MGR)
    RETURN_STRING_OF_ENUM(MID_JBIG_OUTPUT_MGR)

    RETURN_STRING_OF_ENUM(MID_UPGRADE)
    RETURN_STRING_OF_ENUM(MID_ACR_MGR)

    RETURN_STRING_OF_ENUM(MID_POWER_MANAGER)
    RETURN_STRING_OF_ENUM(MID_PARSER_DELAY)

   /* PRNSDK MID */
    RETURN_STRING_OF_ENUM(MID_PRNSDK_NET)
    RETURN_STRING_OF_ENUM(MID_PRNSDK_STATE_MGR)
    RETURN_STRING_OF_ENUM(MID_PRNSDK_AUTH_LICENSE)

   /* PEDK MID */
    RETURN_STRING_OF_ENUM(MID_PEDK)

    /* MUST add RETURN_STRING_OF_ENUM(MID above this line  */
    RETURN_STRING_OF_ENUM(MID_INVALID )

    default:
    return "MID_UNKNOWN";
    }
}

__attribute__((unused))  const char* get_msg_type_string(MSG_TYPE_E msg_type)
{
    switch(msg_type)
    {
    RETURN_STRING_OF_ENUM(MSG_NULL )
    RETURN_STRING_OF_ENUM(MSG_CHECK_CMYK )
    RETURN_STRING_OF_ENUM(MSG_SYNC_CMYK )
    RETURN_STRING_OF_ENUM(MSG_SYNC_TO_C )
    RETURN_STRING_OF_ENUM(MSG_SYNC_TO_M )
    RETURN_STRING_OF_ENUM(MSG_SYNC_TO_Y )
    RETURN_STRING_OF_ENUM(MSG_SYNC_TO_K )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_CHECK )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_SET )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_GET )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_S_SET )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_S_GET )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_DIRECT_TO_CHIP )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_CRYPTO2K )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_CRYPTO2K2 )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_CRYPTO2K3 )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZB_R )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZB_W )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZB_E )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_VCC_ON )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_VCC_OFF )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_IIC_SEND )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_IIC_RECE )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_GET_AGAIN )
    RETURN_STRING_OF_ENUM(MSG_SSCINFOINCHIP_CHECK )
    RETURN_STRING_OF_ENUM(MSG_SSCINFOINCHIP_SET )
    RETURN_STRING_OF_ENUM(MSG_SSCINFOINCHIP_C2 )
    RETURN_STRING_OF_ENUM(MSG_SSCINFOINCHIP_ZbRead )
    RETURN_STRING_OF_ENUM(MSG_SSCINFOINCHIP_ZbWrite )
    RETURN_STRING_OF_ENUM(MSG_SSCINFOINCHIP_ZbErase )
    RETURN_STRING_OF_ENUM(MSG_SSC_GI )
    RETURN_STRING_OF_ENUM(MSG_SSC_SS )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZB_R )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZB_W )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZB_E )
    RETURN_STRING_OF_ENUM(MSG_SSC_C2 )
    RETURN_STRING_OF_ENUM(MSG_SSC_C22 )
    RETURN_STRING_OF_ENUM(MSG_SSC_C23 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN0 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN1 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN2 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN3 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN4 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN5 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN6 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SN7 )
    RETURN_STRING_OF_ENUM(MSG_SSC_GI_A )
    RETURN_STRING_OF_ENUM(MSG_SSC_MER )
    RETURN_STRING_OF_ENUM(MSG_SSC_USE64K )
    RETURN_STRING_OF_ENUM(MSG_SSC_C20220817 )
    RETURN_STRING_OF_ENUM(MSG_SSC_SS1 )
    RETURN_STRING_OF_ENUM(MSG_SSC_PKI_READ )
    RETURN_STRING_OF_ENUM(MSG_SSC_PKI_VERIFY )
    RETURN_STRING_OF_ENUM(MSG_SSC_PKI_CANREAD )
    RETURN_STRING_OF_ENUM(MSG_SSC_PKI_CANVERIFY )
    RETURN_STRING_OF_ENUM(MSG_SSC_UP_ERASE )
    RETURN_STRING_OF_ENUM(MSG_SSC_UP_WRITE )
    RETURN_STRING_OF_ENUM(MSG_SSC_UP_READ )
    RETURN_STRING_OF_ENUM(MSG_SSC_UP_VERIFY )
    RETURN_STRING_OF_ENUM(MSG_SSC_CHECK )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_Z_CRYPTO2K )
    RETURN_STRING_OF_ENUM(MSG_SSC_Z_C2 )
    RETURN_STRING_OF_ENUM(MSG_SSC_Z_C22 )
    RETURN_STRING_OF_ENUM(MSG_SSC_Z_C23 )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZZB_R )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZZB_W )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZZB_E )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZZB_R )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZZB_W )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZZB_E )
    RETURN_STRING_OF_ENUM(MSG_SSC_ECC )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZSS )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZSET )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZGET )
    RETURN_STRING_OF_ENUM(MSG_SSCDC2K_ZSN )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZGI )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZS0 )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZS1 )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZS2 )
    RETURN_STRING_OF_ENUM(MSG_SSC_ZGS )

    RETURN_STRING_OF_ENUM(MSG_DATA_JOB_START)
    RETURN_STRING_OF_ENUM(MSG_DATA_JOB_PAUSE)
    RETURN_STRING_OF_ENUM(MSG_DATA_JOB_RESTART)
    RETURN_STRING_OF_ENUM(MSG_DATA_JOB_END)
    RETURN_STRING_OF_ENUM(MSG_DATA_PAGE_START)
    RETURN_STRING_OF_ENUM(MSG_DATA_PAGE_END)
    RETURN_STRING_OF_ENUM(MSG_DATA_PLANE_START)
    RETURN_STRING_OF_ENUM(MSG_DATA_PLANE_DATA)
    RETURN_STRING_OF_ENUM(MSG_DATA_PLANE_END)
    RETURN_STRING_OF_ENUM(MSG_DATA_JOB_ABORT)
    RETURN_STRING_OF_ENUM(MSG_DATA_MEMORY_LOW)
    RETURN_STRING_OF_ENUM(MSG_DATA_PREPRINT)
    RETURN_STRING_OF_ENUM(MSG_DATA_IMAGE_SAVE)
    RETURN_STRING_OF_ENUM(MSG_DATA_IMAGE_SEND)
    RETURN_STRING_OF_ENUM(MSG_DATA_IMAGE_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_DATA_IMAGE_REMOVE)
    RETURN_STRING_OF_ENUM(MSG_DATA_PINCODE_CONTROL)
    RETURN_STRING_OF_ENUM(MSG_DATA_DELAY_CONTROL)
    RETURN_STRING_OF_ENUM(MSG_DATA_FINISHER_REMOVE)

    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_CONTINUE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_SUSPEND)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_SUSPEND_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RESUME)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RESUME_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_INSERT)
    RETURN_STRING_OF_ENUM(MSG_CTRL_INTERRUPT_MODE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_INTERRUPT_MODE_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_INTERRUPT_RELIEVE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_INTERRUPT_RELIEVE_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_ROLLBACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RECOVERY)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_REMOVE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_FORCE_PRINT)
    RETURN_STRING_OF_ENUM(MSG_CTRL_ENGINE_FLUSH)
    RETURN_STRING_OF_ENUM(MSG_CTRL_ENGINE_ROLLBACK)

    RETURN_STRING_OF_ENUM(MSG_CTRL_QIO_START)
    RETURN_STRING_OF_ENUM(MSG_CTRL_QIO_END)
    RETURN_STRING_OF_ENUM(MSG_CTRL_QIO_UPGRADE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_QIO_INVALID)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_REQUEST)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RUN)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_START)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_DONE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_CANCEL_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_AUTO_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_PAUSE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_PAUSE_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RESTART)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RESTART_ACK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_WAIT)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_GOON)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_ABORT)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_JOBOWNER_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOBID_GENERATE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_PAGE_NEXT)
    RETURN_STRING_OF_ENUM(MSG_CTRL_GET_CORRECT_PASSWORD)
    RETURN_STRING_OF_ENUM(MSG_CTRL_SECURE_JOB_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_CTRL_AUDIT_JOB_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_CTRL_SYSTEM_UNACTIVED)

    RETURN_STRING_OF_ENUM(MSG_CTRL_RESOURCE_LINK)
    RETURN_STRING_OF_ENUM(MSG_CTRL_RESOURCE_START)
    RETURN_STRING_OF_ENUM(MSG_CTRL_RESOURCE_IO_FREE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_RESOURCE_FREE)
    RETURN_STRING_OF_ENUM(MSG_CTRL_CLEAR_ERRORS)
    RETURN_STRING_OF_ENUM(MSG_CTRL_USER_INPUT)
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_RERUN)
    RETURN_STRING_OF_ENUM(MSG_CTRL_CHANGE_JOB_ATTR)

    RETURN_STRING_OF_ENUM(MSG_PRINT_DATA_FORMAT)
    RETURN_STRING_OF_ENUM(MSG_PRINT_DATA_STORAGE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_RELAY_INIT)
    RETURN_STRING_OF_ENUM(MSG_PRINT_RELAY_PAGE_REQ)
    RETURN_STRING_OF_ENUM(MSG_PRINT_RELAY_JOB_REQ)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_SAVE_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_RESTART)
    RETURN_STRING_OF_ENUM(MSG_PRINT_PAGE_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_PAGE_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_JOB_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_READY)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_DOT_SET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_PAGE_TYPE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_CLEAR_TYPE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_CHANGE_TYPE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_CALIBRATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_CALIBRATE_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_STATUS_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_VIDEO_PRINT_INTERIOR_PAGE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_THRESHOLD)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_PRINTAPP_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_IMAGE_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_INNER_REQUEST)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_OUTSIDE_REQUEST)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_INSERT_REQUEST)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_SUBMOD_REGIST)
    RETURN_STRING_OF_ENUM(MSG_PRINT_STATUS_CONFIG_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_TONER_STATUS)
    RETURN_STRING_OF_ENUM(MSG_PRINT_QUIET_PRINT)
    RETURN_STRING_OF_ENUM(MSG_PRINT_TRAY_STATUS_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_TRAY_WARNING_IDLE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_IGNORE_TONER_ERROR)
    RETURN_STRING_OF_ENUM(MSG_PRINT_PREPRINT_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_PRINTCHECK_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CALIBRATION)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CALIBRATION_PREPARE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CALIBRATION_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CALIBRATION_PARAM_REC_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_OPC_CALIBRATION_PREPARE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CAL_TIMEOUT)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CONFIG_SET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CONFIG_GET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_SERIAL_SET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_SERIAL_SYNC)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_DIAGNOSTICMODE_CHANGED)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_JOBDELAYCANCEL)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_RESET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_TONER_UPADTE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_RUNONEMPTY_CHECK)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_RUNONEMPTY_COPYCHECK)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_PROCESS)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_PROCESS_HANDSHAKE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_STATUS_CHANGED)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_PRINT_RESTART)
    RETURN_STRING_OF_ENUM(MSG_PRINT_REPAIR_MODE_NOTIFY)
    RETURN_STRING_OF_ENUM(MSG_PRINT_INTERNAL_IMAGE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_QUICKTEST_IMAGE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_PARSER_IMAGE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SAVE_JOB_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SAVE_PAGE_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SAVE_PLANE_DATA)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SAVE_PAGE_END)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SAVE_JOB_END)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SAVE_SEND_DATA)
    RETURN_STRING_OF_ENUM(MSG_PRINT_JOB_CANCEL_REQ)
    RETURN_STRING_OF_ENUM(MSG_PRINT_JOB_CANCEL_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_ERROR)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_RESEND)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_FLUSH)
    RETURN_STRING_OF_ENUM(MSG_PRINT_FORCE_RESEND)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_IDLE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_INIT_FINISH)

    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_SLEEP)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_WAKEUP)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_PAUSE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_FLUSH)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_ROLLBACK)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_STOP_SHEET_SEND)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_ENTER_MAIN_MAINTENANCE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_EXIT_MAIN_MAINTENANCE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_MAINTENANCE_GET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_MAINTENANCE_SET)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_MAINTENANCE_CHECK)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_MAINTENANCE_TEST)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_PRINT_TEST_START)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_MAINTENANCE_REPLY)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_CONSUMPTION_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_DATA_UPLOAD)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_PRINT_PERMISSION)
    RETURN_STRING_OF_ENUM(MSG_PRINT_IMAGE_OPERATE_FAIL)
    RETURN_STRING_OF_ENUM(MSG_PRINT_SHEET_SEND_DONE)
    RETURN_STRING_OF_ENUM(MSG_PRINT_RELAY_FINISHER_JOB_REQ)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_ADVANCE_READY)
    RETURN_STRING_OF_ENUM(MSG_PRINT_TONER_COVERAGE_CLEAR)
    RETURN_STRING_OF_ENUM(MSG_PRINT_ENGINE_OPC_CLEAR)

    /**
     * @brief ips module message
     */
    RETURN_STRING_OF_ENUM(MSG_IMAGE_IPS_CALIBRATION_DATA)
    RETURN_STRING_OF_ENUM(MSG_IAMGE_IPS_TONER_EMPTY_TURN_MONO)

    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_REQUEST)
    RETURN_STRING_OF_ENUM(MSG_SCAN_STATUS_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_SENSOR_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_FEATURE_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_RESTART)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_ERROR)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_ABORT)
    RETURN_STRING_OF_ENUM(MSG_SCAN_MEMORY_LOW)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_DONE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_PARSER)
    RETURN_STRING_OF_ENUM(MSG_SCAN_JOB_PAUSE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_NEXT_PAGE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_SCANNER_READY)
    RETURN_STRING_OF_ENUM(MSG_SCAN_WAIT_HOST_RESPONSE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_WAIT_HOST_QIOEND)
    RETURN_STRING_OF_ENUM(MSG_SCAN_WAIT_PANEL_CONFIRM)
    RETURN_STRING_OF_ENUM(MSG_SCAN_SCANNER_ADJUST)
    RETURN_STRING_OF_ENUM(MSG_SCAN_CALADJUST_SET)
    RETURN_STRING_OF_ENUM(MSG_SCAN_CALADJUST_RESPOND)
    RETURN_STRING_OF_ENUM(MSG_SCAN_RESOURCE_LINK)
    RETURN_STRING_OF_ENUM(MSG_SCAN_RESOURCE_FREE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_DEBUG_INTERFACE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_LAST_PAGE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_CONTINUOUS_SCAN)
    RETURN_STRING_OF_ENUM(MSG_PCIE_MONITOR_ERROR)
    RETURN_STRING_OF_ENUM(MSG_PCIE_MSG_RECEIVE)
    RETURN_STRING_OF_ENUM(MSG_SCAN_ENGINE_JOB_END)
    RETURN_STRING_OF_ENUM(MSG_SCAN_ENGINE_UPDATE_STATUS)
    RETURN_STRING_OF_ENUM(MSG_SCAN_BACKUP)

       /* copy module message */
    RETURN_STRING_OF_ENUM(MSG_COPY_STATUS_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_COPY_WAIT_PANEL_CONFIRM)

       /* fax module message */
    RETURN_STRING_OF_ENUM(MSG_FAX_JOB_REQUEST)
    RETURN_STRING_OF_ENUM(MSG_FAX_PRINT_CHECK)
    RETURN_STRING_OF_ENUM(MSG_FAX_PRINT_AVALIABLE)
    RETURN_STRING_OF_ENUM(MSG_FAX_PRINT_UNAVALIABLE)
    RETURN_STRING_OF_ENUM(MSG_FAX_HOOK_EVENT)
    RETURN_STRING_OF_ENUM(MSG_FAX_DIAL_EVENT)
    RETURN_STRING_OF_ENUM(MSG_FAX_REDIAL_CANCEL_EVENT)
    RETURN_STRING_OF_ENUM(MSG_FAX_CNG_DETECT)
    RETURN_STRING_OF_ENUM(MSG_FAX_STATUS_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_FAX_STATUS_TOMAIL_UPDATE)
    RETURN_STRING_OF_ENUM(MSG_FAX_REPORT_PRINT)
    RETURN_STRING_OF_ENUM(MSG_FAX_AT_COMMAND)
    RETURN_STRING_OF_ENUM(MSG_FAX_REJECT_CALL)
    RETURN_STRING_OF_ENUM(MSG_FAX_SCAN_CANCEL)
    RETURN_STRING_OF_ENUM(MSG_FAX_UPDATE_DONE)

       /* ips module message*/
    RETURN_STRING_OF_ENUM(MSG_IPS_GQIO_ERROR)
    RETURN_STRING_OF_ENUM(MSG_IPS_GQIO_TIMEOUT)
    RETURN_STRING_OF_ENUM(MSG_IPS_EOJ)
    RETURN_STRING_OF_ENUM(MSG_IPS_CANCEL_DONE)

       /*image process message*/
    RETURN_STRING_OF_ENUM(MSG_IMAGE_PROCESS_START)
    RETURN_STRING_OF_ENUM(MSG_IMAGE_PROCESS_DONE)

       /* low power module message */
    RETURN_STRING_OF_ENUM(MSG_LOWPOWER_ON)
    RETURN_STRING_OF_ENUM(MSG_LOWPOWER_OFF)
    RETURN_STRING_OF_ENUM(MSG_LOWPOWER_CPU_FREQUENT_LOW)
    RETURN_STRING_OF_ENUM(MSG_LOWPOWER_CPU_FREQUENT_NORMAL)
    RETURN_STRING_OF_ENUM(MSG_POWER_MANAGER_MACHINE_SLEEP)
    RETURN_STRING_OF_ENUM(MSG_POWER_MANAGER_MACHINE_WAKEUP)
    RETURN_STRING_OF_ENUM(MSG_POWER_MANAGER_CPU_SLEEP)
    RETURN_STRING_OF_ENUM(MSG_POWER_MANAGER_CPU_WAKEUP)
    RETURN_STRING_OF_ENUM(MSG_POWER_MANAGER_ENGINES_SLEEP)

       /* internal page module message*/
    RETURN_STRING_OF_ENUM(MSG_INTERNAL_PAGE_INFO)
    RETURN_STRING_OF_ENUM(MSG_INTERNAL_PAGE_FONT)
    RETURN_STRING_OF_ENUM(MSG_INTERNAL_PAGE_END)

       /* EmWin module message*/
    RETURN_STRING_OF_ENUM(MSG_EW_KEY_INPUT)
    RETURN_STRING_OF_ENUM(MSG_EW_TOUCH_INPUT)
    RETURN_STRING_OF_ENUM(MSG_EW_OS_STATE_CHANGE)
    RETURN_STRING_OF_ENUM(MSG_EW_OS_REDRAW)
    RETURN_STRING_OF_ENUM(MSG_AIRPRINT_IDENTIFY)
    RETURN_STRING_OF_ENUM(MSG_EW_AUTO_TRAY_PAPER_PLACE)
    RETURN_STRING_OF_ENUM(MSG_EW_AUTO_TRAY_PAPER_REMOVE)
    RETURN_STRING_OF_ENUM(MSG_EW_MANUAL_TRAY_PAPER_PLACE)
    RETURN_STRING_OF_ENUM(MSG_EW_MANUAL_TRAY_PAPER_REMOVE)
    RETURN_STRING_OF_ENUM(MSG_EW_USB_INSERT)                               /*U盘已介入 */
    RETURN_STRING_OF_ENUM(MSG_EW_USB_EXTRACT)                               /*U盘已移除 */
    RETURN_STRING_OF_ENUM(MSG_EW_USB_UNMOUNT)                               /*U盘格式错误 */
    RETURN_STRING_OF_ENUM(MSG_EW_USB_OVERLOAD)                            /*U盘过载 */
    RETURN_STRING_OF_ENUM(MSG_EW_ADF_HAVE_PAPER)                             /*ADF已装入文档*/
    RETURN_STRING_OF_ENUM(MSG_EW_ADF_NO_PAPER)                             /*ADF文档已移除*/
    RETURN_STRING_OF_ENUM(MSG_EW_WIFI_DIRECT_TIMEOUT)                       /*WIFI直连手动确认超时*/

    RETURN_STRING_OF_ENUM(MSG_EW_FCT_STATUS)
    RETURN_STRING_OF_ENUM(MSG_EW_UI_INIT_DONE)                           /*通知传真模块UI初始化完成 */
    RETURN_STRING_OF_ENUM(MSG_EW_FPJOB_CONTINUE)                         /*指纹打印，用于通知用户打印机中仍有该用户作业*/

    RETURN_STRING_OF_ENUM(MSG_EW_SCAN_AUDIT_JOB_ENABLE)
    RETURN_STRING_OF_ENUM(MSG_EW_COPY_AUDIT_JOB_ENABLE)
    RETURN_STRING_OF_ENUM(MSG_EW_ILLEGAL_DATA)
    RETURN_STRING_OF_ENUM(MSG_EW_TURN_OFF_NET)
    RETURN_STRING_OF_ENUM(MSG_EW_SECRET_WARNING)
    RETURN_STRING_OF_ENUM(MSG_UPDATE_SOLE_FAIL)                         /*唯一序列号写入失败*/
    RETURN_STRING_OF_ENUM(MSG_EW_ENTER_CARD_NUMBER)
    RETURN_STRING_OF_ENUM(MSG_EW_CARD_ENTER_SUCCESS)
    RETURN_STRING_OF_ENUM(MSG_CARD_NUMBER_IDENT)

       /*fw upgrade module message*/
    RETURN_STRING_OF_ENUM(MSG_FW_UPGRATE_CONFIRM)
    RETURN_STRING_OF_ENUM(MSG_FW_UPGRATE_PARSER_FAIL)
    RETURN_STRING_OF_ENUM(MSG_FW_UPGRATE_CONFIRM_SEC)


    RETURN_STRING_OF_ENUM(MSG_SUBBOARD_CHECK)
    RETURN_STRING_OF_ENUM(MSG_SIGNATURE_VERIFY)
    RETURN_STRING_OF_ENUM(MSG_MUTUAL_AUTHENTICATION)
    RETURN_STRING_OF_ENUM(MSG_JOB_INPUT_PASSWORD)

       /* system status mgr message*/
    RETURN_STRING_OF_ENUM(MSG_SM_UPDATE_STATUS)
    RETURN_STRING_OF_ENUM(MSG_SM_UPDATE_PARAM)
    RETURN_STRING_OF_ENUM(MSG_SM_CONFIRM_ALERT)
    RETURN_STRING_OF_ENUM(MSG_SM_STATUS_RESEND)

       /*pincode status message*/
    RETURN_STRING_OF_ENUM(MSG_PINCODE_DATA_START)
    RETURN_STRING_OF_ENUM(MSG_PINCODE_DATA_COMPLETE)
    RETURN_STRING_OF_ENUM(MSG_PINCODE_DATA_ERROR)

    RETURN_STRING_OF_ENUM(MSG_FINGER_UNDISCOVERED_DEVICE)                 /*未接入指纹模块设备*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_INSERT)                              /*指纹模块已接入*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_REMOVE)                              /*指纹模块已移除 */
    RETURN_STRING_OF_ENUM(MSG_FINGER_ENROLL)                              /*录入指纹 */
    RETURN_STRING_OF_ENUM(MSG_FINGER_ENROLL_FAIL)                         /*录入指纹失败 */
    RETURN_STRING_OF_ENUM(MSG_FINGER_ENROLL_SUCCESS)                      /*录入指纹成功*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_IDENT_FAIL)                          /*认证指纹失败 */
    RETURN_STRING_OF_ENUM(MSG_FINGER_IDENT_SUCCESS)                       /*认证指纹成功*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_IDENT)                               /*认证指纹*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_DELETE)                              /*删除指纹 */
    RETURN_STRING_OF_ENUM(MSG_FINGER_DELETE_FAIL)                         /*删除指纹失败*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_DELETE_SUCCESS)                      /*删除指纹成功*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_UPDATE_HOST_MARK)                    /*更新主机标识*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_JOB_CANCEL)                          /*作业取消*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_ENROLL_CANCEL)                       /*指纹录入前取消*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_USER_DATA_RECOVER)                   /*指纹用户数据恢复*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_MOD_MISMATCH)                        /*指纹模块不匹配*/
    RETURN_STRING_OF_ENUM(MSG_FINGER_MOD_CHANGE)                          /*指纹模块型号已变更*/

       /* other message */
    RETURN_STRING_OF_ENUM(MSG_SYS_FLASH_ERROR)

       /* 反馈给UI，工装白条校准成功消息 ; leiyanhu 20171107 */
    RETURN_STRING_OF_ENUM(MSG_EW_UP_PAPER_WHITE_CORECTION_OK)
       /* 反馈给UI，玻璃板下的白条校准成功消息; leiyanhu  20171107*/
    RETURN_STRING_OF_ENUM(MSG_EW_UP_GLASS_WHITE_CORECTION_OK)

    RETURN_STRING_OF_ENUM(MSG_PRINT_SEND_ACK)
    RETURN_STRING_OF_ENUM(MSG_PRINT_RECV_ACK)

    RETURN_STRING_OF_ENUM(MSG_SERVER_COPY_PAGE_INFO)
    RETURN_STRING_OF_ENUM(MSG_ENTER_CARD_NUMBER)

       /* JBIG */
    RETURN_STRING_OF_ENUM(MSG_JBIG_OUTPUT_DISTRIBUTE)                      /* jbig output thread distrbute */
    RETURN_STRING_OF_ENUM(MSG_JBIG_VIDEO_DRIVER_DECODE)                    /* data from video driver to jbig for decode */
    RETURN_STRING_OF_ENUM(MSG_JBIG_VIDEO_DRIVER_IOCTL)                     /* message from video driver to jbig for ioctl */

    RETURN_STRING_OF_ENUM(MSG_ACR_MGR_START) 								/* 开始色彩校正*/
    RETURN_STRING_OF_ENUM(MSG_ACR_MGR_STARTUP) 							/* 正式启动 */
    RETURN_STRING_OF_ENUM(MSG_ACR_MGR_DONE) 						        /*  ACR结束*/
    RETURN_STRING_OF_ENUM(MSG_DATA_SEND_CANCEL) 								/*data cancel*/
    RETURN_STRING_OF_ENUM(MSG_CTRL_JOB_ATTRIBUTE_INCORRECT)                 /*非法数据*/

    RETURN_STRING_OF_ENUM(MSG_PORT9120_RESPONSE)                           /* 9120端口响应*/
    RETURN_STRING_OF_ENUM(MSG_PKI_RESPONSE)
    RETURN_STRING_OF_ENUM(MSG_LAST_MSG )

      /* PRNSDK */
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_NET_UI_RS)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_NET_MANAGE_RS)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_JOB_INFO)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_PANEL_INFO)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_ENABLE_CLOSE)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_LOGOUT_PANEL)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_LOGIN_PANEL)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_ACCESS_PANEL)
    RETURN_STRING_OF_ENUM(MSG_PRNSDK_AUTHORITY_PANEL)

      /* PESDK */
    RETURN_STRING_OF_ENUM(MSG_PEDK_APP_INSTALL)

    default:
        return "MSG_UNKNOWN";
    }
}


/**
 *@}
 */
