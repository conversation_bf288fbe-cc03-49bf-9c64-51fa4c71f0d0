/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file location_api.h
 * @addtogroup platform
 * @{
 * @addtogroup location_api
 * <AUTHOR>
 * @date 2023-07-17
 * @brief Country Code defines.
 */

#ifndef LOCATION_API_H
#define LOCATION_API_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 *@ brief These are order dependant as they are used as indices into the\n
 * CountryDefaults array.
 */
typedef enum
{
    ARGENTINA = 1,
    AUSTRALIA,
    AUSTRIA,
    BELARUS,
    BELGIUM,
    BOLIVIA,
    BRAZIL,
    BRUNEI,
    BULGARIA,
    CAMBODIA,

    CANADA = 11,
    CHAD,
    CHILE,
    CHINA,
    COLUMBIA,
    COSTA_RICA,
    CROATIA,
    CUBA,
    CYPRUS,
    CZECH_REPUBLIC,

    DENMARK = 21,
    DOMINICAN_REPUBLIC,
    ECUADOR,
    EGYPT,
    ENGLISH,
    ESTONIA,
    FIJI,
    FINLAND,
    FRANCE,
    GERMANY,

    GREECE = 31,
    <PERSON><PERSON>ATEMAL<PERSON>,
    HONDURAS,
    HONG_KONG,
    HUNGARY,
    ICELAND,
    INDIA,
    INDONESIA,
    IRAN,
    IRAQ,

    IRELAND = 41,
    ISRAEL,
    ITALY,
    JAPAN,
    JORDAN,
    KAZAKHSTAN,
    KOREA,
    KUWAIT,
    LATVIA,
    LEBANON,

    LIECHTENSTEIN = 51,
    LITHUANIA,
    LUXEMBURG,
    MACEDONIA,
    MALAYSIA,
    MALTA,
    MEXICO,
    MONOCO,
    MONGOLIA,
    MOROCCO,

    NETHERLANDS = 61,
    NEW_ZEALAND,
    NORTH_AFRICA,
    NORWAY,
    OMAN,
    PAKISTAN,
    PANAMA,
    PARAGUAY,
    PERU,
    PHILIPPINES,

    POLAND = 71,
    PORTUGAL,
    ROMANIA,
    RUSSIA,
    SAMOA,
    SAUDI_ARABIA,
    SINGAPORE,
    SLOVAK_REP,
    SLOVENIA,
    SOUTH_AFRICA,

    SOUTH_KOREA = 81,
    SPAIN,
    SRI_LANKA,
    SUDAN,
    SWEDEN,
    SWITZERLAND,
    SYRIA,
    TAIWAN,
    THAILAND,
    TUNISIA,

    TURKEY = 91,
    UAE,                                      ///< United Arab Emirates
    UK,
    UKRAINE,
    USA,
    URUGUAY,
    VENEZUELA,
    VIETNAM,
    YEMEN,
    GEORGIA,

    AZERBAIJAN = 101,
    LATIN_AMERICA,
    CONGO,
    JAMAICA,
    SERBIA,
    MALDIVES,

    END_COUNTRY = 107,

    NO_COUNTRY = 0xFF
} e_CountryCode;

/**
 *@ brief These are order dependant as they are used as indices into the\n
 * regioncode Defaults array.
 */
typedef enum
{
    REGION_EUROPE   = 0x0A,
    REGION_AMERICAN = 0x0B,
    REGION_ASIA     = 0x0C,
    REGION_CHINA    = 0x0D,
} e_RegionCode;

/**
 *@ brief system language
 */
typedef enum
{
    SYS_LANGUAGE_CHINESE = 1,
    SYS_LANGUAGE_ENGLISH,
    SYS_LANGUAGE_FRENCH,
    SYS_LANGUAGE_GERMAN,
    SYS_LANGUAGE_HEBREW,
    SYS_LANGUAGE_ITALIAN,
    SYS_LANGUAGE_JAPANESE,
    SYS_LANGUAGE_POLISH,
    SYS_LANGUAGE_RUSSIAN,
    SYS_LANGUAGE_SPANISH,
    SYS_LANGUAGE_TRADITIONAL_CHINESE,
    SYS_LANGUAGE_TURKISH,
    SYS_LANGUAGE_ARABIC,
    SYS_LANGUAGE_PORTUGUESE,
    SYS_LANGUAGE_KOREAN,
    SYS_LANGUAGE_ROMANIAN,
    SYS_LANGUAGE_THAI,
    SYS_LANGUAGE_GREEK,
    SYS_LANGUAGE_CZECH,
    SYS_LANGUAGE_UKRAINIAN,
    SYS_LANGUAGE_KAZAKH,
    SYS_LANGUAGE_DANISH,
    SYS_LANGUAGE_NORWEGIAN,
    SYS_LANGUAGE_SWEDISH,
    SYS_LANGUAGE_AZERBAIJANI,
    SYS_LANGUAGE_HUNGARIAN,
    SYS_LANGUAGE_BULGARIAN,
    SYS_LANGUAGE_VIETNAMESE,
    SYS_LANGUAGE_NONE = 0xFF

} e_LanguageCode;

#ifdef __cplusplus
}
#endif

#endif
/**
 * @}
 */

