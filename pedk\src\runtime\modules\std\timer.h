/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file timer.h
 * @addtogroup std
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief timer init 
 */
#ifndef __MODULES__STD__TIMER_H__
#define __MODULES__STD__TIMER_H__

#include <quickjs.h>
#include <uv.h>

/**
 * @brief   js setTimeout
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_setTimeout(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv);
/**
 * @brief   js clearTimeout
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_clearTimeout(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv);
/**
 * @brief   js setInterval
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_setInterval(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv);
/**
 * @brief   js clearInterval
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_clearInterval(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv);

/**
 * @brief   timer init
 * <AUTHOR> @date    2024-06-11
 */
void timer_init();

/**
 * @brief   timer close
 * @param[in] *ctx :a heap space runtime handle
 * <AUTHOR> @date    2024-06-11
 */
void timer_close(JSContext* ctx);

#endif // __MODULES__STD__TIMER_H__
/**
 * @}
 */
