#include "pedk_jobctl.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define USER_NAME_SIZE    (108)
#define JOB_NAME_SIZE     (152)
#define JOB_LIST_LEN      (1024)
#define JOB_STR_SIZE      (32)
#define MSG_RECV_TIMEOUT  (3)
#define COUNTOF(arr)      (sizeof(arr) / sizeof((arr)[0]))

#define MAX_JOB_RECORDS 200 // 最大记录数
#define SCAN_HTTP_SESSION_FILE "/pesf_data/scan_http_session.txt"
#define COPY_HTTP_SESSION_FILE "/pesf_data/copy_http_session.txt"

static int send_msg2mfp_recv(SUB_MSG sub, int respond, unsigned char *recvBuf, int *recvBufSize)
{
    int ret = 0;

    ret = SendMsgToMfp(MSG_MODULE_JOBCTL, sub, respond, 0, "");

    if (ret < 0)
    {
        printf("error : send failed - sub:%d\n", sub);
        return ret;
    }

    RecvMsgToMfp(MSG_MODULE_JOBCTL, sub, &ret, recvBuf, recvBufSize, MSG_RECV_TIMEOUT);

    return ret;
}

static JSValue js_get_job_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int     size     = JOB_LIST_LEN;
    char   *job_list = NULL;
    JSValue js_value = JS_UNDEFINED;

    job_list = malloc(JOB_LIST_LEN);
    if (!job_list)
    {
        printf("get job list is NULL\n");
        return JS_UNDEFINED;
    }
    memset(job_list, 0x00, JOB_LIST_LEN);

    send_msg2mfp_recv(MSG_JOBCTL_SUB, 0, (unsigned char *)job_list, &size);

    js_value = JS_NewString(ctx, job_list);

    free(job_list);

    return js_value;
}

static JSValue js_cancel_job(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int id  = 0;
    int ret = 0;

    if (!JS_IsNumber(argv[0]))
    {
        return JS_EXCEPTION;
    }

    if (JS_ToInt32(ctx, &id, argv[0]))
    {
        return JS_EXCEPTION;
    }

    ret = send_msg2mfp_recv(MSG_JOBCTL_SUB_CANCEL, id, NULL, 0);

    return JS_NewBool(ctx, ret);
}

static JSValue js_cancel_all_job(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;

    ret = send_msg2mfp_recv(MSG_JOBCTL_SUB_CANCEL, 0, NULL, 0);

    return JS_NewBool(ctx, ret);
}

static JSValue js_apply_job_id(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;

    ret = send_msg2mfp_recv(MSG_JOBCTL_SUB_APPLYID, 0, NULL, 0);

    return JS_NewUint32(ctx, (uint32_t)ret);
}

static JSValue js_get_job_history_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;
    int size = 30 * JOB_LIST_LEN;
    unsigned char *job_list = NULL;
    JSValue js_value;

    job_list = (unsigned char*)malloc(size);
    if (!job_list)
    {
        printf("malloc failed\n");
        return JS_UNDEFINED;
    }
    memset(job_list , 0 , size);
    ret = send_msg2mfp_recv(MSG_JOBCTL_SUB_HISTORY_LIST, 0 , job_list, &size);
    js_value = JS_NewString(ctx, job_list);

    free(job_list);
    return js_value;
}

JSValue js_get_net_job_history_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if (argc < 1)
    {
        return JS_ThrowTypeError(ctx, "Expected 1 argument (job_type)");
    }

    const char *job_type = JS_ToCString(ctx, argv[0]);
    if (!job_type)
    {
        return JS_ThrowTypeError(ctx, "Invalid job_type parameter");
    }

    const char *file_path = NULL;
    if (strcmp(job_type, "NET_JOB_SCAN_HTTP") == 0)
    {
        file_path = SCAN_HTTP_SESSION_FILE;
    }
    else if (strcmp(job_type, "NET_JOB_COPY_RETENTION") == 0)
    {
        file_path = COPY_HTTP_SESSION_FILE;
    }
    else
    {
        JS_FreeCString(ctx, job_type);
        return JS_ThrowTypeError(ctx, "Invalid job_type value (expected 'scan' or 'copy')");
    }

    cJSON *json_array = cJSON_CreateArray();
    if (!json_array)
    {
        JS_FreeCString(ctx, job_type);
        return JS_ThrowOutOfMemory(ctx);
    }

    FILE *file = fopen(file_path, "r");
    if (!file)
    {
        printf("Failed to open file: %s\n", file_path);
        cJSON_Delete(json_array);
        JS_FreeCString(ctx, job_type);
        return JS_ThrowTypeError(ctx, "null file data");
    }

    char buffer[512];
    while (fgets(buffer, sizeof(buffer), file))
    {
        cJSON *json = cJSON_Parse(buffer);
        if (!json)
        {
            printf("Failed to parse JSON line: %s\n", buffer);
            continue;
        }
        cJSON_AddItemToArray(json_array, json);
    }

    fclose(file);
    JS_FreeCString(ctx, job_type);

    char *json_string = cJSON_PrintUnformatted(json_array);
    cJSON_Delete(json_array);

    if (!json_string)
    {
        return JS_NewArray(ctx);
    }

    printf("Final JSON string: %s\n", json_string); // 打印生成的 JSON 字符串
    JSValue result = JS_NewString(ctx, json_string);
    free(json_string);

    return result;
}

static JSValue js_get_job_last_history(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;
    int size = JOB_LIST_LEN;
    unsigned char *job_list = NULL;
    JSValue js_value;

    job_list = (unsigned char*)malloc(size);
    if (!job_list)
    {
        printf("malloc failed\n");
        return JS_UNDEFINED;
    }
    memset(job_list , 0 , size);
    ret = send_msg2mfp_recv(MSG_JOBCTL_SUB_HISTORY_LAST, 0 , job_list, &size);
    js_value = JS_NewString(ctx, job_list);

    free(job_list);
    return js_value;
}

static JSValue js_clear_all_job_history(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;

    ret = send_msg2mfp_recv(MSG_JOBCTL_SUB_HISTORY_CLEAR, 0, NULL, 0);

    return JS_NewBool(ctx, ret);
}

int js_jobctl_init(JSContext *ctx, JSValueConst global)
{
    int     i = 0;
    JSValue cfunc_obj;

    const struct
    {
        const char  *name;
        int          param_num;
        JSCFunction *func;
    } js_cfuncs[] = {
        { "getJobListFromDC",           0, js_get_job_list              },
        { "cancelJob",                  1, js_cancel_job                },
        { "cancelAllJob",               0, js_cancel_all_job            },
        { "applyJobId",                 0, js_apply_job_id              },
        { "getJobHistoryListFromDC",    0, js_get_job_history_list      },
        { "getNetJobHistoryListFromDC", 1, js_get_net_job_history_list  },
        { "getJobLastHistoryFromDC",    0, js_get_job_last_history      },
        { "clearJobHistoryList",        0, js_clear_all_job_history     },
    };

    for (i = 0; i < COUNTOF(js_cfuncs); ++i)
    {
        cfunc_obj = JS_NewCFunction(ctx, js_cfuncs[i].func, js_cfuncs[i].name, js_cfuncs[i].param_num);

        JS_SetPropertyStr(ctx, global, js_cfuncs[i].name, cfunc_obj);
    }

    return 0;
}

