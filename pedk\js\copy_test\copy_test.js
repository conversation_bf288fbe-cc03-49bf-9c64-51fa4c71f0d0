class CopyJobStateListener extends pedk.jobs.copy.JobStateListener {
    constructor() {
        super();
        this.job = undefined;
    }

    notify(state) {
        console.log('copy_test CopyJobStateListener notify ' + state);
    }
}
;

console.log( '\n\n\n\n\n\n\n\n\n\n' );
console.log( 'copy_test  start  \n' );


//Copy job parameter set: Define a copy job parameter set instance to send all copy parameters to the copy job management module in one go
var paramSet = new globalThis.pedk.jobs.copy.CopyParameterSet();



	//Normal copy job: Set the current copy job as a normal copy job
	var copy_job = new globalThis.pedk.jobs.copy.CopyJob('COPY_NORMAL');
	//var copy_job = new globalThis.pedk.jobs.copy.CopyJob('COPY_NUP');
	//var copy_job = new globalThis.pedk.jobs.copy.CopyJob('COPY_BOOKLET');////booklet copy need set //var collate_mode = new globalThis.pedk.jobs.copy.CollateMode(1);

	// Set color mode: Define the color mode of the copy, black and white, color, etc.
	//var color_mode = new globalThis.pedk.jobs.copy.ColorMode('COLOR_MODE_COLOR');
	var color_mode = new globalThis.pedk.jobs.copy.ColorMode('COLOR_MODE_BLACK_WHITE');
	paramSet.addParameter('COPY_COLOR_MODE', color_mode);

	//Set the number of copies: Set the number of copies attribute, i.e., how many copies are needed
	var copy_copies = new globalThis.pedk.jobs.copy.Copies(1);
	//var copy_copies = new globalThis.pedk.jobs.copy.Copies(2);
	paramSet.addParameter('COPY_PARAM_COPIES', copy_copies);

	//Set collate mode: When the number of copies is greater than 1, set whether the copies should be printed collated or uncollated as needed
	var collate_mode = new globalThis.pedk.jobs.copy.CollateMode(0);
	//var collate_mode = new globalThis.pedk.jobs.copy.CollateMode(1);
	//paramSet.addParameter('COPY_PARAM_COLLATEMODE', collate_mode);

	//Set scan source: The copy source can be a platen or ADF, set the scan source of the original according to the actual original placement
	//var copy_source = new globalThis.pedk.jobs.copy.CopyScanSource(2);//ADF
	var copy_source = new globalThis.pedk.jobs.copy.CopyScanSource(3);//FB
	//var copy_source = new globalThis.pedk.jobs.copy.CopyScanSource(1);//DADF
	paramSet.addParameter('COPY_SCAN_SOURCE', copy_source);

	//Set scan size: Define the size of the original, set according to the actual size of the original, otherwise the copy effect cannot be guaranteed
	var scan_size = new globalThis.pedk.jobs.copy.InputPaperSize('MEDIA_SIZE_A4');
	//var scan_size = new globalThis.pedk.jobs.copy.InputPaperSize('MEDIA_SIZE_A3');
	paramSet.addParameter('COPY_PARAM_INPUTPAPERSIZE', scan_size);

	// Set output tray: Define the output paper tray for the copy job, the copy will be printed from the specified tray
	var output_tray = new globalThis.pedk.jobs.copy.OutputTray('STANDAR_TRAY', 'MEDIA_SIZE_A4');
	//var output_tray = new globalThis.pedk.jobs.copy.OutputTray('OPTION_TRAY2', 'MEDIA_SIZE_A3');
	//var output_tray = new globalThis.pedk.jobs.copy.OutputTray('AUTO_SELECTION_TRAY', 'MEDIA_SIZE_A4');
	paramSet.addParameter('COPY_PARAM_OUTPUTTRAY', output_tray);

	// Paper tray media type: Define the type of media in the paper tray, such as plain paper, thick paper, etc.
	var page_type = new globalThis.pedk.jobs.copy.PageType('MEDIA_TYPE_PLAIN');
	//var page_type = new globalThis.pedk.jobs.copy.PageType('MEDIA_TYPE_THICK_PAPER1');
	paramSet.addParameter('COPY_PAGE_TYPE', page_type);

	//Set print duplex mode:   1: PRINT  SINGLE   2:PRINT DUPLEX
	var duplex_mode = new globalThis.pedk.jobs.copy.CopyMode(1);
	//var duplex_mode = new globalThis.pedk.jobs.copy.CopyMode(2);
	paramSet.addParameter('COPY_PARAM_COPYMODE', duplex_mode);

	//Zoom: Define the zoom ratio for scanning images in copy mode. 0-24 auto scale   25-400:user define scale  >400:no scale
	var zoom_value = new globalThis.pedk.jobs.copy.Zoom(0);
	//var zoom_value = new globalThis.pedk.jobs.copy.Zoom(70);
	paramSet.addParameter('COPY_PARAM_ZOOM', zoom_value);

	// Copy quality: Set different modes for different copying originals' image content: text, text + image, etc.
	//var quality_type = new globalThis.pedk.jobs.copy.QualityType(3);
	var quality_type = new globalThis.pedk.jobs.copy.QualityType(1);
	paramSet.addParameter('COPY_PARAM_QUALITYTYPE', quality_type);


	// Save toner mode: Define whether to enable the toner saving mode, which will print the copy with the lowest density when enabled
	var save_toner_mode = new globalThis.pedk.jobs.copy.SaveTonerMode(0);
	//var save_toner_mode = new globalThis.pedk.jobs.copy.SaveTonerMode(1);
	paramSet.addParameter('COPY_TONER_MODE', save_toner_mode);



	// Separator page: Define separator page mode, the separator page is to facilitate users to distinguish between copies
	var separator_mode = new globalThis.pedk.jobs.copy.Separator(0);
	//var separator_mode = new globalThis.pedk.jobs.copy.Separator(1);
	paramSet.addParameter('COPY_SEPARATOR', separator_mode);

	// Separator page paper tray: Define which paper tray the separator page uses, which can be defined as the same or different paper tray as the copy as needed
	var separator_tray_in = new globalThis.pedk.jobs.copy.SeparatorTrayIn('STANDAR_TRAY');
	paramSet.addParameter('COPY_SEPARATOR_TRAY_IN', separator_tray_in);

	// N-up mode: Define N-up mode, 2-in-1/4-in-1
	var nup_mode = new globalThis.pedk.jobs.copy.NupMode(0);
	//var nup_mode = new globalThis.pedk.jobs.copy.NupMode(1);
	paramSet.addParameter('COPY_PARAM_NUPMODE', nup_mode);


	//Set edge-to-edge mode: When the original has no margins, set edge-to-edge copy mode. The copy margin will not exceed 3mm
	var edge_to_edge = new globalThis.pedk.jobs.copy.EdgeToEdgeMode(0);
	paramSet.addParameter('COPY_EDGE_TO_EDGE_MODE', edge_to_edge);

	//Original flip: Define the flip method of the original for double-sided originals
	var origianl_flip = new globalThis.pedk.jobs.copy.OriginalFlip(1);
	paramSet.addParameter('COPY_ORIGINAL_FLIP', origianl_flip);
	//Copies flip: Define the flip method of the copies for double-sided printing
	var copies_flip = new globalThis.pedk.jobs.copy.CopiesFlip(1);
	paramSet.addParameter('COPY_COPIES_FLIP', copies_flip);
	//Original direction: Define the direction of the original placed on the platen or ADF (top, bottom, left, right) for user convenience
	var image_orientation = new globalThis.pedk.jobs.copy.ImageOrientation(0);
	paramSet.addParameter('COPY_IMAGE_ORIENTAITON', image_orientation);


	// Watermark copy: Define the watermark content, when enabled, the watermark content will be displayed on the copy
	var watermark_copy = new globalThis.pedk.jobs.copy.Watermark(0);
	//var watermark_copy = new globalThis.pedk.jobs.copy.Watermark(1);
	watermark_copy.setWatermark('Watermark_test');
	paramSet.addParameter('COPY_PARAM_WATERMARK', watermark_copy);




	// Set edge clean enable: Define whether to enable the edge clean function. After enabling, the edge clean amount needs to be set, and the edge of the original image will be removed during copying.
	var edge_clean_mode = new globalThis.pedk.jobs.copy.EdgeClean(0);
	//var edge_clean_mode = new globalThis.pedk.jobs.copy.EdgeClean(1);
	paramSet.addParameter('COPY_USE_EDGE_CLEAN', edge_clean_mode);
	// Edge clean distance: Define the edge clean amount for the four edges of the original document
	var edge_margin_clean = new globalThis.pedk.jobs.copy.EdgeMargin(0, 3);
	edge_margin_clean.setEdgeMargin(1, 3);
	edge_margin_clean.setEdgeMargin(2, 18);
	edge_margin_clean.setEdgeMargin(3, 18);
	paramSet.addParameter('COPY_FILTER_EDGE_MARGIN', edge_margin_clean);



	// Copy horizontal margin: Define the adjustment of the horizontal margin of the original document. After setting, the original document will be translated in the horizontal direction.
	var horizontal_margin = new globalThis.pedk.jobs.copy.HorizontalMargin(0);
	//var horizontal_margin = new globalThis.pedk.jobs.copy.HorizontalMargin(20);
	paramSet.addParameter('COPY_HORIZONTAL_MARGIN', horizontal_margin);
	// Copy vertical margin: Define the adjustment of the vertical margin of the original document. After setting, the original document will be translated in the vertical direction.
	var vertical_margein = new globalThis.pedk.jobs.copy.VerticalMargin(0);
	//var vertical_margein = new globalThis.pedk.jobs.copy.VerticalMargin(20);
	paramSet.addParameter('COPY_VERTICAL_MARGIN', vertical_margein);



	var CopyRetentionParams = new globalThis.pedk.device.setting.CopyRetentionParam();
	CopyRetentionParams.url = "http://192.168.2.101:8080/cgi-bin/upload.py";
	CopyRetentionParams.format_type = 2;
	CopyRetentionParams.level = 3;
	CopyRetentionParams.protocol = "protocolxxx";

	var headers = new globalThis.pedk.net.http.Headers();
	headers.set("x-customer-key","1234567890");
	CopyRetentionParams.headers = headers;

	globalThis.pedk.device.setting.setCopyRetentionParams(CopyRetentionParams);

	//globalThis.pedk.device.setting.setCopyRetentionSwitch(true);
	globalThis.pedk.device.setting.setCopyRetentionSwitch(false);

		
		




/*  <========================  Unsupport   ========================>

// Skew correction: Define whether to enable automatic skew correction for printing
var skewing_mode = new globalThis.pedk.jobs.copy.SkewingMode(1);
paramSet.addParameter('COPY_SKEWING_MODE', skewing_mode);
// Stapler: Define whether a stapler is installed, needs to be set according to the actual situation
var have_stapler = new globalThis.pedk.jobs.copy.HaveStapler(0);
paramSet.addParameter('COPY_HAVE_STAPLER', have_stapler);
// Output tray: Define which output tray to use after the copy job is printed
var tray_receive = new globalThis.pedk.jobs.copy.TrayReceive(0);
paramSet.addParameter('COPY_TRAY_RECEIVE', tray_receive);
// Punching: Define whether to enable the punching function and how many holes are needed
var punch_mode = new globalThis.pedk.jobs.copy.PunchMode(0);
paramSet.addParameter('COPY_PUNCH_MODE', punch_mode);
// Staple mode: Define whether to enable the stapling function and the stapling mode, such as double-staple on the left, double-staple on the bottom, etc.
var staple_mode = new globalThis.pedk.jobs.copy.StapleMode(0);
paramSet.addParameter('COPY_STAPLE_MODE', staple_mode);
// Staple offset mode: Define whether to enable staple offset function, making it easier for users to retrieve different copy jobs
var shift_mode = new globalThis.pedk.jobs.copy.ShiftMode(0);
paramSet.addParameter('COPY_SHIFT_MODE', shift_mode);
// Fold mode: Define whether to enable folding function and the fold mode, such as fold, fold and center staple, etc.
var fold_mode = new globalThis.pedk.jobs.copy.FoldMode(0);
paramSet.addParameter('COPY_FOLD_MODE', fold_mode);
// Number of sheets for folding: Define the number of sheets to be folded each time
var fold_number = new globalThis.pedk.jobs.copy.FoldNumber(10);
paramSet.addParameter('COPY_FOLD_NUMBER', fold_number);

// N-up enable: Define whether to enable the N-up function, after enabling the copy job will be N-up copy, not ordinary copy
//var nup_mode_enable = new globalThis.pedk.jobs.copy.NupCombination(0);
//paramSet.addParameter('COPY_NUP_COMBINATION', nup_mode_enable);

// ID copy arrangement: When the copy job is an ID copy, define the arrangement of the ID front and back images
var arrange_mode = new globalThis.pedk.jobs.copy.ArrangementMode(1);
paramSet.addParameter('COPY_PARAM_ARRANGEMENTMODE', arrange_mode);
// Set auto correction: A unique attribute of ID copy, enable auto correction, users can place the ID at any angle on the tablet, and the final effect is watermark printing of the ID
var auto_corr_mode = new globalThis.pedk.jobs.copy.AutoCorrectionMode(0);
paramSet.addParameter('COPY_PARAM_AUTOCORRECTIONMODE', auto_corr_mode);



// Set booklet duplex mode: When the copy job is in booklet copy mode, define whether the original document for copying is single-sided or double-sided scanning
var booklet_duplex_mode = new globalThis.pedk.jobs.copy.BookletDuplex(0);
paramSet.addParameter('COPY_BOOKLET_DUPLEX', booklet_duplex_mode);

// Copy backup mode: Define whether to enable copy backup mode, when enabled, the image of the copy will be backed up
var backup_mode = new globalThis.pedk.jobs.copy.BackupMode(0);
paramSet.addParameter('COPY_BACKUP_MODE', backup_mode);

//Copy mirror mode: Define whether to enable mirror mode. If enabled, all original copies will undergo mirror processing
var mirror_mode = new globalThis.pedk.jobs.copy.MirrorMode(0);
paramSet.addParameter('COPY_MIRROR_MODE', mirror_mode);

// Poster copy mode: When the copy job is in poster copy mode, define the mode of the poster copy job
var poster_mode = new globalThis.pedk.jobs.copy.PosterMode(0);
paramSet.addParameter('COPY_PARAM_POSTER', poster_mode);

// Set clone mode: When the copy job is in clone copy mode, define the mode of the clone copy job, 2x2/3x3
var clone_mode = new globalThis.pedk.jobs.copy.CloneMode(0);
paramSet.addParameter('COPY_PARAM_CLONEMODE', clone_mode);

// Set cover mode: Define cover type as outer, inner, or double-sided
var cover_mode = new globalThis.pedk.jobs.copy.Cover(0);
paramSet.addParameter('COPY_COVER', cover_mode);
// Set back cover mode: Define back cover type as outer, inner, or double-sided
var cover_back_mode = new globalThis.pedk.jobs.copy.CoverBack(0);
paramSet.addParameter('COPY_COVER_BACK', cover_back_mode);
// Cover/back cover paper medium: Define the printing paper medium for the cover/back cover as plain paper, thick paper, etc.
var cover_paper_type = new globalThis.pedk.jobs.copy.CoverPaperType(1);
paramSet.addParameter('COPY_COVER_PAPER_TYPE', cover_paper_type);
// Cover/back cover paper tray: Define the paper tray for the cover/back cover printing paper
var cover_tray_in = new globalThis.pedk.jobs.copy.CoverTrayIn(0);
paramSet.addParameter('COPY_COVER_TRAY_IN', cover_tray_in);

// Color inversion mode: Define whether to enable the color inversion function. When enabled, the image color of the original copy will be inverted, for example: black -> white
var color_inversion = new globalThis.pedk.jobs.copy.ColorInversion(0);
paramSet.addParameter('COPY_USE_COLOR_INVERSION', color_inversion);


// Set color balance: Define the color balance parameter values, including color balance of 4 channels
var color_balance = new globalThis.pedk.jobs.copy.ColorBalance(0, 5);
color_balance.setColorBalance(1, 5);
color_balance.setColorBalance(2, 5);
color_balance.setColorBalance(3, 5);
paramSet.addParameter('COPY_COLOR_BALANCE', color_balance);
// Image color: Define the levels of brightness, saturation, contrast, sharpness, hue, and background removal parameters
var image_color = new globalThis.pedk.jobs.copy.ImageColor(0,5);
image_color.setImageColor(1, 5);
image_color.setImageColor(2, 5);
image_color.setImageColor(3, 5);
image_color.setImageColor(4, 5);
image_color.setImageColor(5, 5);
paramSet.addParameter('COPY_PARAM_IMAGE_COLOR', image_color);
// Color removal mode: Define whether to remove a certain color from the original copy image
var remove_color = new globalThis.pedk.jobs.copy.RemoveColor(0);
paramSet.addParameter('COPY_USE_REMOVE_COLOR', remove_color);

//Copy footer enable: Defines whether to enable footer function, the copied document will add footer after enabled
var footer_enable = new globalThis.pedk.jobs.copy.PaperFooterEnable(0);
paramSet.addParameter('COPY_PAGE_FOOTER_ENABLE', footer_enable);
//Copy footer pagination mode: Defines on which copied documents the page number should be displayed
var footer_pagination = new globalThis.pedk.jobs.copy.PaperFooterPagination(0);
paramSet.addParameter('COPY_PAGE_FOOTER_PAGINATION', footer_pagination);
//Copy footer position: Defines the footer display position: left, center, right
var footer_position = new globalThis.pedk.jobs.copy.PaperFooterPosition(0);
paramSet.addParameter('COPY_PAGE_FOOTER_POSITION', footer_position);
//Copy footer custom content: Defines the user-customized footer text content, set by the user themselves
var footer_text = new globalThis.pedk.jobs.copy.PaperFooterText('footer');
paramSet.addParameter('COPY_PAGE_FOOTER_TEXT', footer_text);
//Copy footer content type: User selects preset footer content as needed
var footer_text_type = new globalThis.pedk.jobs.copy.PaperFooterTextType(0);
paramSet.addParameter('COPY_PAGE_FOOTER_TEXT_TYPE', footer_text_type);
//Copy header enable: Defines whether to enable header function, each copied document will add a header after enabled
var header_enable = new globalThis.pedk.jobs.copy.PaperHeaderEnable(0);
paramSet.addParameter('COPY_PAGE_HEADER_ENABLE', header_enable);
//Copy header pagination mode: Defines on which copied documents the page number should be displayed
var header_pagination = new globalThis.pedk.jobs.copy.PaperHeaderPagination(0);
paramSet.addParameter('COPY_PAGE_HEADER_PAGINATION', header_pagination);
//Copy header position: Defines the header display position: left, center, right
var header_position = new globalThis.pedk.jobs.copy.PaperHeaderPosition(0);
paramSet.addParameter('COPY_PAGE_HEADER_POSITION', header_position);
//Copy header custom content: Defines the user-customized header text content, set by the user themselves
var header_text = new globalThis.pedk.jobs.copy.PaperHeaderText('header');
paramSet.addParameter('COPY_PAGE_HEADER_TEX', header_text);
//Copy header content type: User selects preset header content as needed
var header_text_type = new globalThis.pedk.jobs.copy.PaperHeaderTextType(0);
paramSet.addParameter('COPY_PAGE_HEADER_TEXT_TYPE', header_text_type);

// Sample copy: Define whether to enable sample copying, when enabled, a sample copy will be printed first for the user to confirm the effect before continuing
var sample_copy = new globalThis.pedk.jobs.copy.SampleCopyMode(0);
paramSet.addParameter('COPY_SAMPLE_MODE', sample_copy);

*/

// Initiate copy job
copy_job.start(1, paramSet, null);


console.log( 'copy_test ADF_PAGE_COUNT= ' + getADFScanCopyedPage() );
console.log( 'copy_test FB_PAGE_COUNT= '  + getFBScanCopyedPage() );

const listener = new CopyJobStateListener();
console.log('copy_test addListener');
const ret = copy_job.addListener(listener);
console.log('copy_test addListener ret is ', ret);

copy_job.start( 1, paramSet , null );
//copy_job.cancel();