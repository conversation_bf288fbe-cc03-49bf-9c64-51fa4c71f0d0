/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_common.c
 * @addtogroup panel_dc
 * @{
 * @brief the common function and set config for panel_dc
 * <AUTHOR>
 * @date 2023-08-12
 */


#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "qio/qio_general.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"
#include "pol/pol_string.h"
#include "panel_public.h"
#include "panel_event.h"
#include "panel_config.h"
#include "panel_maintenance_mode.h"
#include "panel_event.h"
#include "public/msgrouter_main.h"
#include "event_manager/event_mgr.h"



//for test
#include "cmd.h"

static PI_SEMAPHORE_T   s_panel_maintenance_sem = INVALIDSEM;
static PI_THREAD_T s_panel_maintenance_thread = INVALIDTHREAD;

static ENG_MAINTENANCE_SENSOR_VALUE_S sensor_value = {0};
static ENG_MAINTENANCE_TABLE_VALUE_S table_value = {0};
static ENG_MAINTENANCE_VOLTAGE_VALUE_S voltage_value = {0};
static ENG_MAINTENANCE_ENVIRONMENT_VALUE_S envitonment_value = {0};
static ENG_MAINTENANCE_CALI_VALUE_S cali_value = {0};
static ENG_MAINTENANCE_SKEW_VALUE_S skew_value = {0};

static ENG_MAINTENANCE_FUSER_TEMP_VALUE_S fuser_temp_value = {0};
static ENG_MAINTENANCE_FUSER_SPEED_VALUE_S fuser_speed_value = {0};
static ENG_MAINTENANCE_PRINT_AREA_VALUE_S print_area_value = {0};
static ENG_MAINTENANCE_PAPER_LOOP_AMOUNT_S paper_loop_amount = {0};
static ENG_MAINTENANCE_COLOR_POS_VALUE_S color_pos_value = {0};
static ENG_MAINTENANCE_FAN_DELAY_S fan_delay = {0};
static ENG_MAINTENANCE_DENSITY_MAX_S density_max = {0};
static ENG_MAINTENANCE_DENSITY_MONO_S density_mano = {0};
static ENG_MAINTENANCE_DENSITY_THICK_S density_thick = {0};
static ENG_MAINTENANCE_TROUBLE_ISOLATION_S trouble_isolation = {0};
static ENG_MAINTENANCE_WARMUP_MODE_S warmup_made = {0};
static ENG_MAINTENANCE_LED_STATE_S led_state = {0};
static ENG_MAINTENANCE_FINISHER_540_ADJUST_S finisher_540_adjust = {0};
static ENG_MAINTENANCE_BACKGROUND_VOLTAGE_S background_voltage = {0};
static ENG_MAINTENANCE_NEUTRALIZING_VOLTAGE_S neutralizing_voltage = {0};
static ENG_MAINTENANCE_TRANSFER_OUTPUT_S transfer_output = {0};
static ENG_MAINTENANCE_DV_AC_CHOICE_S dv_ac_choice = {0};
static ENG_MAINTENANCE_DV_AC_FREQ_CHOICE_S dv_ac_freq_choice = {0};
static ENG_MAINTENANCE_TONER_SUPPLY_S toner_supply = { 0 };
static ENG_MAINTENANCE_MANUAL_TRAY_ADJUST_S manual_tray_adjust = {0};
static ENG_MAINTENANCE_CHARGE_AC_OUTPUT_S charge_ac_output = {0};
static ENG_MAINTENANCE_NEW_UNIT_CONFIRM_S new_unit_confirm = {0};

static ENG_MAINTENANCE_LD_ADJUST_S ld_adjust = { 0 };
static ENG_MAINTENANCE_PAPER_FEED_DIRECTION_ADJUST_S paper_feed_direction_adjust = {0};
static ENG_MAINTENANCE_PAPER_SEPARATION_ADJUST_S paper_separation_adjust = {0};
static ENG_MAINTENANCE_FEED_TEST_S feed_test = {0};

/**
 * @brief Maintenance operation enum.
 *
 */
typedef enum
{
    MAINTENANCE_TYPE_GET = 0x00,                 ///< type get function
    MAINTENANCE_TYPE_SET,                    ///< type set function
    MAINTENANCE_TYPE_CHECK,                       ///< type check function

    MAINTENANCE_TYPE_INVALID = 0xFFFFFFFF,                        ///< please keep it in the end of the enum --- invalid data
}MAINTENANCE_FUNC_TYPE_E;

typedef struct
{

    MAINTENANCE_FUNCTION_TYPE_E function_id;            ///< maintenance mode function id
    uint32_t msg_type;                                  ///< msg type
    void* data;                                         ///< data for function
    uint32_t data_len;                                  ///< data lenght
    uint16_t panel_dc_cmd_id;                           ///< when recv panel cmd ,find the function to execute
    MAINTENANCE_FUNCTION_TYPE_E reply_action_function;  ///< when execute function_id fail, the next action

}MAINTENANCE_FUNC_TAB_S;

MAINTENANCE_FUNC_TAB_S maintenance_func_table[] =
{
    { FUNC_GET_SENSOR_VALUE ,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &sensor_value ,           sizeof(ENG_MAINTENANCE_SENSOR_VALUE_S)    ,        SETTING_CMD_MAINTENANCE_GET_SENSOR_VALUE,            FUNC_INVALID ,          },
    { FUNC_GET_TABLE_VALUE  ,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &table_value    ,         sizeof(ENG_MAINTENANCE_TABLE_VALUE_S)    ,         SETTING_CMD_MAINTENANCE_GET_TABLE_VALUE,             FUNC_INVALID  ,         },
    { FUNC_GET_VOLTAGE_VALUE,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &voltage_value     ,      sizeof(ENG_MAINTENANCE_VOLTAGE_VALUE_S)     ,      SETTING_CMD_MAINTENANCE_GET_VOLTAGE_VALUE,           FUNC_INVALID,           },
    { FUNC_GET_ENVIRONMENT  ,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &envitonment_value,       sizeof(ENG_MAINTENANCE_ENVIRONMENT_VALUE_S)     ,  SETTING_CMD_MAINTENANCE_GET_ENVIRONMENT_VALUE,       FUNC_INVALID  ,  },
    { FUNC_GET_CALI_VALUE   ,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &cali_value   ,           sizeof(ENG_MAINTENANCE_CALI_VALUE_S)     ,         SETTING_CMD_MAINTENANCE_GET_CALIBRATION_VALUE,       FUNC_INVALID   ,  },
    { FUNC_GET_SKEW_VALUE   ,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &skew_value    ,          sizeof(ENG_MAINTENANCE_SKEW_VALUE_S)     ,         SETTING_CMD_MAINTENANCE_GET_SKEW_VALUE,              FUNC_INVALID   ,            },

    { FUNC_GET_FUSER_TEMP   ,            MSG_PRINT_ENGINE_MAINTENANCE_GET,    &fuser_temp_value,        sizeof(ENG_MAINTENANCE_FUSER_TEMP_VALUE_S)    ,    SETTING_CMD_MAINTENANCE_GET_FUSER_TMP,               FUNC_INVALID ,        },
    { FUNC_GET_FUSER_SPEED ,             MSG_PRINT_ENGINE_MAINTENANCE_GET,    &fuser_speed_value,       sizeof(ENG_MAINTENANCE_FUSER_SPEED_VALUE_S)    ,   SETTING_CMD_MAINTENANCE_GET_FUSER_SPEED,             FUNC_INVALID  ,         },
    { FUNC_GET_PRINT_AREA  ,             MSG_PRINT_ENGINE_MAINTENANCE_GET,    &print_area_value,        sizeof(ENG_MAINTENANCE_PRINT_AREA_VALUE_S)    ,    SETTING_CMD_MAINTENANCE_GET_PRINT_AREA,              FUNC_INVALID,          },
    { FUNC_GET_PAPER_LOOP_AMOUNT,        MSG_PRINT_ENGINE_MAINTENANCE_GET,    &paper_loop_amount,       sizeof(ENG_MAINTENANCE_PAPER_LOOP_AMOUNT_S)     ,  SETTING_CMD_MAINTENANCE_GET_PAPER_LOOP,              FUNC_INVALID  ,        },
    { FUNC_GET_COLOR_POSITION   ,        MSG_PRINT_ENGINE_MAINTENANCE_GET,    &color_pos_value,         sizeof(ENG_MAINTENANCE_COLOR_POS_VALUE_S)    ,     SETTING_CMD_MAINTENANCE_GET_COLOR_POS,               FUNC_INVALID   ,      },
    { FUNC_GET_FAN_DELAY ,               MSG_PRINT_ENGINE_MAINTENANCE_GET,    &fan_delay,               sizeof(ENG_MAINTENANCE_FAN_DELAY_S)    ,           SETTING_CMD_MAINTENANCE_GET_FAN_DELAY,               FUNC_INVALID   ,      },
    { FUNC_GET_PPM  ,                    MSG_PRINT_ENGINE_MAINTENANCE_GET,    NULL,                     0    ,       									    0,                                                 FUNC_INVALID ,     },
    { FUNC_GET_MAX_DENSITY,              MSG_PRINT_ENGINE_MAINTENANCE_GET,    &density_max,             sizeof(ENG_MAINTENANCE_DENSITY_MAX_S)     ,        SETTING_CMD_MAINTENANCE_GET_MAX_DENSITY,             FUNC_INVALID  ,      },
    { FUNC_GET_MONO_DENSITY   ,          MSG_PRINT_ENGINE_MAINTENANCE_GET,    &density_mano,            sizeof(ENG_MAINTENANCE_DENSITY_MONO_S)    ,        SETTING_CMD_MAINTENANCE_GET_MONO_DENSITY,            FUNC_INVALID,         },
    { FUNC_GET_PAPER_MEDIUM_DENSITY ,    MSG_PRINT_ENGINE_MAINTENANCE_GET,    &density_thick,           sizeof(ENG_MAINTENANCE_DENSITY_THICK_S)    ,       SETTING_CMD_MAINTENANCE_GET_THICK_PAPER_DENSITY,     FUNC_INVALID  , },
    { FUNC_GET_TROUBLE_ISOLATION  ,      MSG_PRINT_ENGINE_MAINTENANCE_GET,    &trouble_isolation,       sizeof(ENG_MAINTENANCE_TROUBLE_ISOLATION_S)    ,   SETTING_CMD_MAINTENANCE_GET_TROUBLE_ISOLATION,       FUNC_INVALID   ,           },
    { FUNC_GET_WARMUP_MODE,              MSG_PRINT_ENGINE_MAINTENANCE_GET,    &warmup_made,             sizeof(ENG_MAINTENANCE_WARMUP_MODE_S)     ,        SETTING_CMD_MAINTENANCE_GET_WARMUP_MODE,             FUNC_INVALID   ,     },
    { FUNC_GET_LED_STATE   ,             MSG_PRINT_ENGINE_MAINTENANCE_GET,    &led_state,               sizeof(ENG_MAINTENANCE_LED_STATE_S)    ,           SETTING_CMD_MAINTENANCE_GET_LED_STATE,               FUNC_INVALID ,     },
    { FUNC_GET_FS540_FINISHER_ADJUST,    MSG_PRINT_ENGINE_MAINTENANCE_GET,    &finisher_540_adjust,     sizeof(ENG_MAINTENANCE_FINISHER_540_ADJUST_S)    , SETTING_CMD_MAINTENANCE_GET_FINISHER_ADJUSTMENT,     FUNC_INVALID  , },
    { FUNC_GET_BACKGROUND_VOLT  ,        MSG_PRINT_ENGINE_MAINTENANCE_GET,    &background_voltage,      sizeof(ENG_MAINTENANCE_BACKGROUND_VOLTAGE_S)    ,  SETTING_CMD_MAINTENANCE_GET_BACKGROUND_VOLTAGE,      FUNC_INVALID,  },
    { FUNC_GET_NEUTRALIZING_VOLT,        MSG_PRINT_ENGINE_MAINTENANCE_GET,    &neutralizing_voltage,    sizeof(ENG_MAINTENANCE_NEUTRALIZING_VOLTAGE_S) ,   SETTING_CMD_MAINTENANCE_GET_NEUTRALIZING_VOLTAGE,    FUNC_INVALID  ,  },
    { FUNC_GET_TRANSFER_OUTPUT   ,       MSG_PRINT_ENGINE_MAINTENANCE_GET,    &transfer_output,         sizeof(ENG_MAINTENANCE_TRANSFER_OUTPUT_S)    ,     SETTING_CMD_MAINTENANCE_GET_TRANSFER_OUTPUT,         FUNC_INVALID   ,         },
    { FUNC_GET_CHARGE_AC_OUTPUT   ,      MSG_PRINT_ENGINE_MAINTENANCE_GET,    &charge_ac_output,        sizeof(ENG_MAINTENANCE_CHARGE_AC_OUTPUT_S)    ,    SETTING_CMD_MAINTENANCE_GET_CHARGE_AC_OUTPUT,         FUNC_INVALID   ,         },
    { FUNC_GET_DV_AC_VOLT_CHOICE ,       MSG_PRINT_ENGINE_MAINTENANCE_GET,    &dv_ac_choice,            sizeof(ENG_MAINTENANCE_DV_AC_CHOICE_S)    ,        SETTING_CMD_MAINTENANCE_GET_DV_AC_CHOICE,            FUNC_INVALID   ,      },
    { FUNC_GET_DV_AC_FREQ_CHOICE  ,      MSG_PRINT_ENGINE_MAINTENANCE_GET,    &dv_ac_freq_choice,       sizeof(ENG_MAINTENANCE_DV_AC_FREQ_CHOICE_S)    ,   SETTING_CMD_MAINTENANCE_GET_DV_AC_FREQ_CHOICE,       FUNC_INVALID  ,},

    { FUNC_SET_FUSER_TEMP   ,            MSG_PRINT_ENGINE_MAINTENANCE_SET,    &fuser_temp_value,        sizeof(ENG_MAINTENANCE_FUSER_TEMP_VALUE_S)    ,    SETTING_CMD_MAINTENANCE_SET_FUSER_TMP,               FUNC_GET_FUSER_TEMP   ,           },
    { FUNC_SET_FUSER_SPEED ,             MSG_PRINT_ENGINE_MAINTENANCE_SET,    &fuser_speed_value,       sizeof(ENG_MAINTENANCE_FUSER_SPEED_VALUE_S)    ,   SETTING_CMD_MAINTENANCE_SET_FUSER_SPEED,             FUNC_GET_FUSER_SPEED , },
    { FUNC_SET_PRINT_AREA  ,             MSG_PRINT_ENGINE_MAINTENANCE_SET,    &print_area_value,        sizeof(ENG_MAINTENANCE_PRINT_AREA_VALUE_S)    ,    SETTING_CMD_MAINTENANCE_SET_PRINT_AREA,              FUNC_GET_PRINT_AREA  ,},
    { FUNC_SET_PAPER_LOOP_AMOUNT,        MSG_PRINT_ENGINE_MAINTENANCE_SET,    &paper_loop_amount,       sizeof(ENG_MAINTENANCE_PAPER_LOOP_AMOUNT_S)     ,  SETTING_CMD_MAINTENANCE_SET_PAPER_LOOP,              FUNC_GET_PAPER_LOOP_AMOUNT,         },
    { FUNC_SET_COLOR_POSITION   ,        MSG_PRINT_ENGINE_MAINTENANCE_SET,    &color_pos_value,         sizeof(ENG_MAINTENANCE_COLOR_POS_VALUE_S)    ,     SETTING_CMD_MAINTENANCE_SET_COLOR_POS,               FUNC_GET_COLOR_POSITION   ,       },
    { FUNC_SET_FAN_DELAY ,               MSG_PRINT_ENGINE_MAINTENANCE_SET,    &fan_delay,               sizeof(ENG_MAINTENANCE_FAN_DELAY_S)    ,           SETTING_CMD_MAINTENANCE_SET_FAN_DELAY,               FUNC_GET_FAN_DELAY , },
    { FUNC_SET_PPM  ,                    MSG_PRINT_ENGINE_MAINTENANCE_SET,    NULL,                     0    ,       									    0,                                                 FUNC_GET_PPM  ,      },
    { FUNC_SET_MAX_DENSITY,              MSG_PRINT_ENGINE_MAINTENANCE_SET,    &density_max,             sizeof(ENG_MAINTENANCE_DENSITY_MAX_S)     ,        SETTING_CMD_MAINTENANCE_SET_MAX_DENSITY,             FUNC_GET_MAX_DENSITY,  },
    { FUNC_SET_MONO_DENSITY   ,          MSG_PRINT_ENGINE_MAINTENANCE_SET,    &density_mano,            sizeof(ENG_MAINTENANCE_DENSITY_MONO_S)    ,        SETTING_CMD_MAINTENANCE_SET_MONO_DENSITY,            FUNC_GET_MONO_DENSITY   ,            },
    { FUNC_SET_PAPER_MEDIUM_DENSITY ,    MSG_PRINT_ENGINE_MAINTENANCE_SET,    &density_thick,           sizeof(ENG_MAINTENANCE_DENSITY_THICK_S)    ,       SETTING_CMD_MAINTENANCE_SET_THICK_PAPER_DENSITY,     FUNC_GET_PAPER_MEDIUM_DENSITY ,},
    { FUNC_SET_TROUBLE_ISOLATION  ,      MSG_PRINT_ENGINE_MAINTENANCE_SET,    &trouble_isolation,       sizeof(ENG_MAINTENANCE_TROUBLE_ISOLATION_S)    ,   SETTING_CMD_MAINTENANCE_SET_TROUBLE_ISOLATION,       FUNC_GET_TROUBLE_ISOLATION  ,},
    { FUNC_SET_WARMUP_MODE,              MSG_PRINT_ENGINE_MAINTENANCE_SET,    &warmup_made,             sizeof(ENG_MAINTENANCE_WARMUP_MODE_S)     ,        SETTING_CMD_MAINTENANCE_SET_WARMUP_MODE,             FUNC_GET_WARMUP_MODE,  },
    { FUNC_SET_LED_STATE   ,             MSG_PRINT_ENGINE_MAINTENANCE_SET,    &led_state,               sizeof(ENG_MAINTENANCE_LED_STATE_S)    ,           SETTING_CMD_MAINTENANCE_SET_LED_STATE,               FUNC_GET_LED_STATE   ,            },
    { FUNC_SET_FS540_FINISHER_ADJUST,    MSG_PRINT_ENGINE_MAINTENANCE_SET,    &finisher_540_adjust,     sizeof(ENG_MAINTENANCE_FINISHER_540_ADJUST_S)    , SETTING_CMD_MAINTENANCE_SET_FINISHER_ADJUSTMENT,     FUNC_GET_FS540_FINISHER_ADJUST,},
    { FUNC_SET_BACKGROUND_VOLT  ,        MSG_PRINT_ENGINE_MAINTENANCE_SET,    &background_voltage,      sizeof(ENG_MAINTENANCE_BACKGROUND_VOLTAGE_S)    ,  SETTING_CMD_MAINTENANCE_SET_BACKGROUND_VOLTAGE,      FUNC_GET_BACKGROUND_VOLT  ,   },
    { FUNC_SET_NEUTRALIZING_VOLT,        MSG_PRINT_ENGINE_MAINTENANCE_SET,    &neutralizing_voltage,    sizeof(ENG_MAINTENANCE_NEUTRALIZING_VOLTAGE_S) ,   SETTING_CMD_MAINTENANCE_SET_NEUTRALIZING_VOLTAGE,    FUNC_GET_NEUTRALIZING_VOLT,     },
    { FUNC_SET_TRANSFER_OUTPUT   ,       MSG_PRINT_ENGINE_MAINTENANCE_SET,    &transfer_output,         sizeof(ENG_MAINTENANCE_TRANSFER_OUTPUT_S)    ,     SETTING_CMD_MAINTENANCE_SET_TRANSFER_OUTPUT,         FUNC_GET_TRANSFER_OUTPUT   ,            },
    { FUNC_SET_CHARGE_AC_OUTPUT   ,      MSG_PRINT_ENGINE_MAINTENANCE_SET,    &charge_ac_output,        sizeof(ENG_MAINTENANCE_CHARGE_AC_OUTPUT_S)    ,    SETTING_CMD_MAINTENANCE_SET_CHARGE_AC_OUTPUT,        FUNC_GET_CHARGE_AC_OUTPUT   ,           },
    { FUNC_SET_DV_AC_VOLT_CHOICE ,       MSG_PRINT_ENGINE_MAINTENANCE_SET,    &dv_ac_choice,            sizeof(ENG_MAINTENANCE_DV_AC_CHOICE_S)    ,        SETTING_CMD_MAINTENANCE_SET_DV_AC_CHOICE,            FUNC_GET_DV_AC_VOLT_CHOICE ,         },
    { FUNC_SET_DV_AC_FREQ_CHOICE  ,      MSG_PRINT_ENGINE_MAINTENANCE_SET,    &dv_ac_freq_choice,       sizeof(ENG_MAINTENANCE_DV_AC_FREQ_CHOICE_S)    ,   SETTING_CMD_MAINTENANCE_SET_DV_AC_FREQ_CHOICE,       FUNC_GET_DV_AC_FREQ_CHOICE  ,},

    { FUNC_SKEW_ADJUST,                  MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_SKEW_ADJUST,            FUNC_INVALID,     },
    { FUNC_MANUAL_TRAY_ADJUST   ,        MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  &manual_tray_adjust,      sizeof(ENG_MAINTENANCE_MANUAL_TRAY_ADJUST_S)    ,  SETTING_CMD_MAINTENANCE_EXEC_MANUAL_TRAY_ADJUST,     FUNC_INVALID   ,            },
    { FUNC_ENG_RESET ,                   MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_ENG_RESET,              FUNC_INVALID ,         },
    { FUNC_EXECUTE_CALI  ,               MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_IMAGE_STABILIZE,        FUNC_INVALID  ,},
    { FUNC_EXECUTE_CALI_RAW  ,           MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_IMAGE_STABILIZE,        FUNC_INVALID  ,},

    { FUNC_RU_FEED_MRT_CHECK          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_FNS_ENT_FEED_MRT_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,     FUNC_INVALID   ,            },
    { FUNC_MAIN_EXIT_FEED_MTR_CHECK   ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,              FUNC_INVALID ,         },
    { FUNC_TAMPER_FRONT_MTR_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_STACKER_MTR_CHECK          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_TAMPER_REAR_MTR_CHECK      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,  FUNC_INVALID  ,},
    { FUNC_END_FENCE_MTR_CHECK        ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_GRIP_EJECT_MTR_CHECK       ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_BUFFER_LIFT_MTR_CHECK      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_SCU_MTR_CHECK              ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,     FUNC_INVALID   ,            },
    { FUNC_STAPLER_MOVE_MTR_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,              FUNC_INVALID ,         },
    { FUNC_PRE_EJECT_MTR_CHECK        ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_MAIN_PADDLE_MTR_CHECK      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_EXIT_DIVERT_CHECK          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,  FUNC_INVALID  ,},
    { FUNC_END_FENCE_REAR_MTR_CHECK   ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_PADDLE_ARM_MTR_CHECK       ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_PUSHER_MTR_CHECK           ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_SUB_EXIT_FEED_MTR_CHECK    ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,     FUNC_INVALID   ,            },
    { FUNC_BERO_MTR_CHECK             ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,              FUNC_INVALID ,         },
    { FUNC_ZU_FEED_MTR_CHECK          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_JS_EXIT_DIVERT_CHECK       ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_SD_ENT_FEED_MTR_CHECK      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,  FUNC_INVALID  ,},
    { FUNC_SD_BELT_TRAY_FEED_MTR_CHECK,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_ZU_CHOPPER_CHECK           ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_ZU_GUIDE_MTR_CHECK         ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_ZU_PRESS_MTR_CHECK         ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,     FUNC_INVALID   ,            },
    { FUNC_M302_SWING_DRIVE_M_HP_SEARCH_CHECK,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,              FUNC_INVALID ,         },
    { FUNC_M302_SWING_DRIVE_M_END_FACE_DETECTION_SENSOR_CONTROL_CHECK,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_M301_PUNCH_DRIVE_M_HP_SEARCH_CHECK      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_M301_PUNCH_DRIVE_M_PUNCH_2_HOLES_CHECK  ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,  FUNC_INVALID  ,},
    { FUNC_M301_PUNCH_DRIVE_M_PUNCH_3_4_HOLES_CHECK,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_M203_PAPER_FEED_LILNE_SPEED_DRIVE_CHECK ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_MC201_UPPER_PAPER_FEED_CLUTCH_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_MC202_LOWER_PAPER_FEED_CLUTCH_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,     FUNC_INVALID   ,            },
    { FUNC_M201_UPPER_ELEVATING_M_DESCENT_CHECK    ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,              FUNC_INVALID ,         },
    { FUNC_M201_UPPER_ELEVATING_M_ASCENDING_CHECK  ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_M202_LOWER_ELEVATING_M_DESCENT_CHECK    ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_M202_LOWER_LIFT_M_ASCEND_CHECK          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,  FUNC_INVALID  ,},
    { FUNC_SD201_UPPER_HANDLING_SOLENOID_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_SD202_LOWER_HANDLING_SOLENOID_CHECK     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_MC203_RESIST_TRANSFER_CLUTCH_CHECK      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_SD_PAPER_TRANSPORT_MOTOR_CHECK        ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_SD_FOLDING_ROLLER_MOTOR_CHECK         ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_SD_LEADING_EDGE_GRIPPER_SOLENOID_CHECK,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,            FUNC_INVALID,     },
    { FUNC_SD_LEADING_EDGE_STOPPER_MOTOR_CHECK   ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,     FUNC_INVALID   ,            },
    { FUNC_SD_ALIGNMENT_PLATE_MOTOR_CHECK        ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,              FUNC_INVALID ,         },
    { FUNC_SD_FOLDING_KNIFE_MOTOR_CHECK          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_SD_FOLDING_CHANGE_MOTOR_CHECK         ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,        FUNC_INVALID  ,},
    { FUNC_SD_PAPER_DISCHARGE_CONTROL_MOTOR_CHECK,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,  FUNC_INVALID  ,},
    { FUNC_SD_PADDLE_MOTOR_CHECK                 ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,   FUNC_INVALID  ,},
    { FUNC_SD_TRI_FOLDING_KNIFE_MOTOR            ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    FUNC_INVALID  ,},

    { FUNC_HOPPER_TONER_FILLING  ,                    MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_HOPPER_TONER_FILLING,   FUNC_INVALID  ,},
    { FUNC_TCR_TONER_SUPPLY  ,                    MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  &toner_supply,                      sizeof( ENG_MAINTENANCE_TONER_SUPPLY_S )    ,                                            SETTING_CMD_MAINTENANCE_EXEC_TONER_SUPPLY_CHOICE,    FUNC_INVALID  ,},

    { FUNC_SENSOR_CHECK  ,                    MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_SENSOR_CHECK,    FUNC_INVALID  ,},
    { FUNC_NEW_UNIT_DETECTION_CONFIRM  ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  &new_unit_confirm,                sizeof(new_unit_confirm)    ,                       SETTING_CMD_MAINTENANCE_EXEC_NEW_UNIT_CONFIRM,    FUNC_INVALID  ,},

    // add for kanas
    { FUNC_GET_LD_ADJUST  ,                    MSG_PRINT_ENGINE_MAINTENANCE_GET,  &ld_adjust,                      sizeof(ld_adjust)    ,                                            SETTING_CMD_MAINTENANCE_GET_LD_ADJUST,    FUNC_INVALID  ,},
    { FUNC_GET_PAPER_FEED_DIRECTION_ADJUST  ,      MSG_PRINT_ENGINE_MAINTENANCE_GET,  &paper_feed_direction_adjust,                     sizeof(paper_feed_direction_adjust)    ,                       SETTING_CMD_MAINTENANCE_GET_PAPER_FEED_DIRECTION_ADJUST,    FUNC_INVALID  ,},
    { FUNC_GET_PAPER_SEPARATION_ADJUST  ,      MSG_PRINT_ENGINE_MAINTENANCE_GET,  &paper_separation_adjust,                     sizeof(paper_separation_adjust)    ,                       SETTING_CMD_MAINTENANCE_GET_PAPER_SEPARATION_ADJUST,    FUNC_INVALID  ,},

    { FUNC_SET_LD_ADJUST  ,                    MSG_PRINT_ENGINE_MAINTENANCE_SET,  &ld_adjust,                      sizeof(ld_adjust)    ,                                            SETTING_CMD_MAINTENANCE_SET_LD_ADJUST,    FUNC_INVALID  ,},
    { FUNC_SET_PAPER_FEED_DIRECTION_ADJUST  ,      MSG_PRINT_ENGINE_MAINTENANCE_SET,  &paper_feed_direction_adjust,                     sizeof(paper_feed_direction_adjust)    ,                       SETTING_CMD_MAINTENANCE_SET_PAPER_FEED_DIRECTION_ADJUST,    FUNC_INVALID  ,},
    { FUNC_SET_PAPER_SEPARATION_ADJUST  ,      MSG_PRINT_ENGINE_MAINTENANCE_SET,  &paper_separation_adjust,                     sizeof(paper_separation_adjust)    ,                       SETTING_CMD_MAINTENANCE_SET_PAPER_SEPARATION_ADJUST,    FUNC_INVALID  ,},

    { FUNC_PAPER_TRANSFER_MOTOR        ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,   FUNC_INVALID  ,},
    { FUNC_PAPER_ENTRANCE_MOTOR         ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,    FUNC_INVALID  ,},
    { FUNC_PAPER_EXIT_MOTOR,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,            FUNC_INVALID,     },
    { FUNC_PAPER_ALIGNMENT_PLATE_MOTOR_FRONT   ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,     FUNC_INVALID   ,            },
    { FUNC_MAIN_TRAY_UP_DOWN_MOTOR        ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,              FUNC_INVALID ,         },
    { FUNC_PAPER_ALIGNMENT_PLATE_MOTOR_REAR          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,        FUNC_INVALID  ,},
    { FUNC_SIDE_STOPPER_GUIDE_MOTOR         ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,        FUNC_INVALID  ,},
    { FUNC_PAPER_EXIT_BELT_MOTOR,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,  FUNC_INVALID  ,},
    { FUNC_EXIT_ROLLER_RETRACTION_MOTOR                 ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,   FUNC_INVALID  ,},
    { FUNC_TAIL_EDGE_HOLDING_PLATE_MOTOR            ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,    FUNC_INVALID  ,},
    { FUNC_SIDE_STAPLER_MOVEMENT_MOTOR          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0 ,                                               SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,            FUNC_INVALID,     },
    { FUNC_PRE_EXIT_DRIVE_MOTOR     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,     FUNC_INVALID   ,            },
    { FUNC_PUNCH_DRIVE_MOTOR   ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,              FUNC_INVALID ,         },
    { FUNC_PADDLE_MOTOR     ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,        FUNC_INVALID  ,},
    { FUNC_OUTPUT_TRAY_CHANGE_MOTOR          ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  NULL,                      0    ,                                            SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,        FUNC_INVALID  ,},

    { FUNC_SIMPLEX_FEED      ,      MSG_PRINT_ENGINE_MAINTENANCE_CHECK,  &feed_test,                      sizeof(feed_test)    ,                                            SETTING_CMD_MAINTENANCE_EXEC_SIMPLEX_FEED,  FUNC_INVALID  ,},

};

MAINTENANCE_FUNC_TAB_S* function_data_search( MAINTENANCE_FUNCTION_TYPE_E func )
{
    for( int i = 0; i < sizeof(maintenance_func_table)/sizeof(maintenance_func_table[0]); i++ )
    {
        if( maintenance_func_table[i].function_id == func )
        {
            return &maintenance_func_table[i];
        }
    }
    return NULL;
}

MAINTENANCE_FUNC_TAB_S* function_data_search_by_cmd( uint32_t panel_dc_cmd )
{
    for( int i = 0; i < sizeof(maintenance_func_table)/sizeof(maintenance_func_table[0]); i++ )
    {
        if( maintenance_func_table[i].panel_dc_cmd_id == panel_dc_cmd )
        {
            return &maintenance_func_table[i];
        }
    }
    return NULL;
}

void panel_maintenance_mode_lock()
{
    if (INVALIDSEM != s_panel_maintenance_sem )
    {
        pi_sem_wait(s_panel_maintenance_sem);
        pi_log_d("miantenance lock:%d\n",*((int32_t*) s_panel_maintenance_sem));
    }
}
void panel_maintenance_mode_unlock()
{
    if (INVALIDSEM != s_panel_maintenance_sem )
    {
        pi_sem_post(s_panel_maintenance_sem);
        pi_log_d("miantenance unlock:%d\n",*((int32_t*) s_panel_maintenance_sem));
    }
}

/**
 * @brief panel reuqest to enter maintenance mode
 * @author: madechang
 */
void panel_request_enter_maintenance_mode( void )
{

    ROUTER_MSG_S send_msg;    
    EVT_MGR_CLI_S* panel_evt_client = NULL;
    uint32_t enter = 1;

    s_panel_maintenance_sem = pi_sem_create(0);

    send_msg.msgType     = MSG_PRINT_ENGINE_ENTER_MAIN_MAINTENANCE;
    send_msg.msg1        = 0;
    send_msg.msg2        = MAINTENANCE_REQUEST;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_PRINT_ENGINE_MGR, &send_msg );

        //退出维修模式需要通知系统作业
    panel_evt_client = get_panel_event_client();
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_ENTER_MAINTENANCE_MODIFY, &enter, sizeof(enter));

    

}



/**
 * @brief panel reuqest to exit maintenance mode
 * @author: madechang
 */
void panel_request_exit_maintenance_mode( void )
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_PRINT_ENGINE_EXIT_MAIN_MAINTENANCE;
    send_msg.msg1        = 0;
    send_msg.msg2        = MAINTENANCE_REQUEST;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_PRINT_ENGINE_MGR, &send_msg );

    pi_sem_destroy( s_panel_maintenance_sem );
    pi_thread_destroy( s_panel_maintenance_thread );

}

///get value fuction
void panel_get_function_msg( MAINTENANCE_FUNCTION_TYPE_E func_type, MAINTENANCE_ACTION_TYPE_E reply, void* data )
{
    ROUTER_MSG_S send_msg;

    panel_maintenance_mode_lock();
    send_msg.msgType     = MSG_PRINT_ENGINE_MAINTENANCE_GET;
    send_msg.msg1        = func_type;
    send_msg.msg2        = reply;
    send_msg.msg3        = data;
    send_msg.msgSender   = MID_PANEL;
    if( NULL == data )
    {
        pi_log_d("data is null\n");
    }
    task_msg_send_by_router( MID_PRINT_ENGINE_MGR, &send_msg );

}

void engine_reply_get_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply )
{
    MAINTENANCE_FUNC_TAB_S* mainten_func = NULL;

    panel_maintenance_mode_unlock();
    mainten_func = function_data_search( func );
    if( NULL == mainten_func )
    {        
        pi_log_e( "cannot found maintenance func:%d, reply::%d\n", func, reply );
        return;
    }


    if( MAINTENANCE_ACK_SUCCESS == reply )
    {
        //send data to panel
        pi_log_d("panel get maintenance info success\n");
        panel_send_data_u8( SETTING, mainten_func->panel_dc_cmd_id, SUCCESSFUL_RESPONSE, mainten_func->data, mainten_func->data_len );
    }
    else if( MAINTENANCE_ACK_FAIL == reply )
    {

        pi_log_d("panel get maintenance info fail\n");
        panel_send_data_u8( SETTING, mainten_func->panel_dc_cmd_id, FAIL_RESPONSE, mainten_func->data, mainten_func->data_len );
    }
}

///set value fuction
void panel_set_function_msg( MAINTENANCE_FUNCTION_TYPE_E func_type, MAINTENANCE_ACTION_TYPE_E reply, void* data )
{
    ROUTER_MSG_S send_msg;


    panel_maintenance_mode_lock();

    send_msg.msgType     = MSG_PRINT_ENGINE_MAINTENANCE_SET;
    send_msg.msg1        = func_type;
    send_msg.msg2        = reply;
    send_msg.msg3        = data;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_PRINT_ENGINE_MGR, &send_msg );

}

void engine_reply_set_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply )
{
    MAINTENANCE_FUNC_TAB_S* mainten_func = NULL;

    panel_maintenance_mode_unlock();
    mainten_func = function_data_search( func );
    if( NULL == mainten_func )
    {        
        pi_log_e( "cannot found maintenance func:%d, reply::%d\n", func, reply );
        return;
    }

    if( MAINTENANCE_ACK_SUCCESS == reply )
    {
        pi_log_d("set function success\n");
        pi_log_d("data1:%d\n",*((int8_t*)mainten_func->data) );
        panel_send_data_u8( SETTING, mainten_func->panel_dc_cmd_id, SUCCESSFUL_RESPONSE, mainten_func->data, mainten_func->data_len );
    }
    else if( MAINTENANCE_ACK_FAIL == reply )
    {
        panel_send_data_u8( SETTING, mainten_func->panel_dc_cmd_id, FAIL_RESPONSE, mainten_func->data, mainten_func->data_len );
        panel_get_function_msg( mainten_func->reply_action_function, MAINTENANCE_REQUEST, mainten_func->data );
        pi_log_d("set function fail\n");
    }

}

///check fuction
void panel_check_function_msg( MAINTENANCE_FUNCTION_TYPE_E func_type, MAINTENANCE_ACTION_TYPE_E reply, void* data )
{
    ROUTER_MSG_S send_msg;

    if( data == NULL )
    {
        pi_log_e("check function data error!\n");
    }

    panel_maintenance_mode_lock();

    send_msg.msgType     = MSG_PRINT_ENGINE_MAINTENANCE_CHECK;
    send_msg.msg1        = func_type;
    send_msg.msg2        = reply;
    send_msg.msg3        = data;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_PRINT_ENGINE_MGR, &send_msg );

}


void engine_reply_check_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply )
{
    MAINTENANCE_FUNC_TAB_S* mainten_func = NULL;
    uint32_t result = reply;

    panel_maintenance_mode_unlock();
    mainten_func = function_data_search( func );

    if( NULL == mainten_func )
    {        
        pi_log_e( "cannot found maintenance func:%d, reply::%d\n", func, reply );
    }
    else
    {
        panel_send_data_u8(SETTING, mainten_func->panel_dc_cmd_id, NOTIFICATION_RESPONSE, &result, sizeof(result));
    }


/*
    if( MAINTENANCE_ACK_SUCCESS == reply )
    {
        pi_log_d("check function success\n");
        panel_send_cmd( SETTING, mainten_func->panel_dc_cmd_id, SUCCESSFUL_RESPONSE );
    }
    else if( MAINTENANCE_ACK_FAIL == reply )
    {
        panel_send_cmd( SETTING, mainten_func->panel_dc_cmd_id, FAIL_RESPONSE );
        pi_log_d("check function fail\n");
    }
    */

}

void panel_request_execute_func(  uint16_t panel_dc_cmd, void* data, uint32_t data_len)
{
    MAINTENANCE_FUNC_TAB_S* mainten_func = NULL;
    MAINTENANCE_FUNCTION_TYPE_E finisher_sensor_check_func_type = 0;

    mainten_func = function_data_search_by_cmd( panel_dc_cmd );
    if( mainten_func == NULL )
    {
        pi_log_e("maintenance function not found!\n");
        return;
    }
    switch ( mainten_func->msg_type )
    {
        case MSG_PRINT_ENGINE_MAINTENANCE_GET:
            panel_get_function_msg( mainten_func->function_id, MAINTENANCE_REQUEST, mainten_func->data );
            break;
        case MSG_PRINT_ENGINE_MAINTENANCE_SET:
            if( data_len != mainten_func->data_len )
            {
                pi_log_e("panel maintenance data error!\n");
                return;
            }
            pi_memcpy( mainten_func->data, data, mainten_func->data_len );

            panel_set_function_msg( mainten_func->function_id, MAINTENANCE_REQUEST, mainten_func->data );
            break;
        case MSG_PRINT_ENGINE_MAINTENANCE_CHECK:
            // 这些执行类function带数据
            if( mainten_func->function_id == FUNC_MANUAL_TRAY_ADJUST || mainten_func->function_id == FUNC_TCR_TONER_SUPPLY
                || mainten_func->function_id == FUNC_NEW_UNIT_DETECTION_CONFIRM )
            {
                if( data_len != mainten_func->data_len )
                {
                    pi_log_e("panel maintenance data error!\n");
                    return;
                }
                pi_memcpy( mainten_func->data, data, mainten_func->data_len );

                panel_check_function_msg( mainten_func->function_id, MAINTENANCE_REQUEST, mainten_func->data );
            }
            else if( SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK == panel_dc_cmd || SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST == panel_dc_cmd )
            {
                //马达测试及装订器传感器的check function都使用同一条panel_dc_cmd，在data中使用枚举区分
                if( data == NULL || data_len == 0 )
                {
                    return;
                }
                finisher_sensor_check_func_type = *( (uint32_t*) data);
                pi_log_d("finisher_sensor_check_func_type:%d\n",finisher_sensor_check_func_type);
                pi_memcpy( mainten_func->data, data, mainten_func->data_len );
                panel_check_function_msg( finisher_sensor_check_func_type, MAINTENANCE_REQUEST, mainten_func->data );
            }
            else if( SETTING_CMD_MAINTENANCE_EXEC_SIMPLEX_FEED == panel_dc_cmd )
            {
                if( data == NULL || data_len == 0 )
                {
                    //stop 不带数据
                    panel_maintenance_mode_unlock();
                    panel_check_function_msg( mainten_func->function_id, MAINTENANCE_STOP, mainten_func->data );
                }
                else
                {
                    pi_memcpy( mainten_func->data, data, mainten_func->data_len );
                    panel_check_function_msg( mainten_func->function_id, MAINTENANCE_REQUEST, mainten_func->data );
                }
            }
            else
            {
                //不带数据的function
                panel_check_function_msg( mainten_func->function_id, MAINTENANCE_REQUEST, mainten_func->data );
            }
            break;

        default:
            break;
    }
}

void panel_get_engine_data_init()
{
    uint32_t ret = MAINTENANCE_ACK_SUCCESS;
    //panel_get_fuser_temp_value();
    //panel_maintenance_mode_unlock();
    panel_send_data_u8(SETTING, SETTING_CMD_MAINTENANCE_ENTER, SUCCESSFUL_RESPONSE, &ret,sizeof(int));
    //s_panel_maintenance_thread = pi_thread_create( panel_mainten_init_callbak, 16*1024, NULL, 50, NULL, "panel_mainten_tread");
}


///for test
int32_t test_mode_callback( int32_t argc, char *argv[] )
{
    int cmd;
    cmd = atoi(argv[0]);
    switch( cmd )
    {
        case 0:
            panel_request_enter_maintenance_mode();
            break;
        case 1:
            //panel_set_fuser_temp_value();
            break;
        case 2:
            //panel_get_fuser_temp_value();
            break;
        case 3:
            panel_get_engine_data_init();
            break;
        case 4:
            panel_request_exit_maintenance_mode();
            break;
    }

    return 0;
}

void panel_maintenance_mode_prolog()
{
    cmd_register( "test" , "mode", test_mode_callback,     NULL );
}


/**
 * @}
 */


