# 1. PeSF 平台兼容性策略

## 1.1. 索引

- [1. PeSF 平台兼容性策略](#1-pesf-平台兼容性策略)
  - [1.1. 索引](#11-索引)
  - [1.2. 术语](#12-术语)
    - [1.2.1. 兼容性（compatibility）术语](#121-兼容性compatibility术语)
      - [1.2.1.1. 向前兼容（Forward Compatibility）](#1211-向前兼容forward-compatibility)
      - [1.2.1.2. 向后兼容（Backward Compatibility）](#1212-向后兼容backward-compatibility)
      - [1.2.1.3. 版本控制（Versioning）](#1213-版本控制versioning)
      - [1.2.1.4. 弃用（Deprecation）](#1214-弃用deprecation)
      - [1.2.1.5. 移除（Removal）](#1215-移除removal)
      - [1.2.1.6. 渐进式升级（Progressive Upgrades）](#1216-渐进式升级progressive-upgrades)
      - [1.2.1.7. 大版本升级（Major Version Upgrade）和小版本升级（Minor Version Upgrade）](#1217-大版本升级major-version-upgrade和小版本升级minor-version-upgrade)
    - [1.2.2. PeSF 术语](#122-pesf-术语)
      - [1.2.2.1. PeSF 平台（Platform）](#1221-pesf-平台platform)
      - [1.2.2.2. PeSF 运行时（Runtime）](#1222-pesf-运行时runtime)
      - [1.2.2.3. PeSF SDK](#1223-pesf-sdk)
      - [1.2.2.4. PeSF 开发工具链（Develop Toolchain）](#1224-pesf-开发工具链develop-toolchain)
      - [1.2.2.5. PeSF 应用程序（Application）](#1225-pesf-应用程序application)
  - [1.3. PeSF API 的兼容性策略](#13-pesf-api-的兼容性策略)
    - [1.3.1. PeSF API 的版本号](#131-pesf-api-的版本号)
    - [1.3.2. PeSF API 小版本升级](#132-pesf-api-小版本升级)
      - [1.3.2.1. 保持向前兼容](#1321-保持向前兼容)
      - [1.3.2.2. 允许出现弃用（Deprecation）](#1322-允许出现弃用deprecation)
      - [1.3.2.3. 不出现移除（Removal）](#1323-不出现移除removal)
    - [1.3.3. PeSF API 大版本升级](#133-pesf-api-大版本升级)
      - [1.3.3.1. 不保证向前兼容](#1331-不保证向前兼容)
      - [1.3.3.2. 允许出现移除（Removal）标记](#1332-允许出现移除removal标记)
      - [1.3.3.3. 需要给出迁移指导](#1333-需要给出迁移指导)
  - [1.4. PeSF SDK 的兼容性策略](#14-pesf-sdk-的兼容性策略)
    - [1.4.1. 渐进式升级（Progressive Upgrades）](#141-渐进式升级progressive-upgrades)
    - [1.4.2. PeSF SDK 的 Level](#142-pesf-sdk-的-level)
    - [1.4.3. PeSF SDK 的内部版本号（Internal Version）](#143-pesf-sdk-的内部版本号internal-version)
    - [1.4.4. 过时的（Outdated）PeSF SDK Level](#144-过时的outdatedpesf-sdk-level)
    - [1.4.5. 不被支持的（Unsupported） PeSF API 大版本](#145-不被支持的unsupported-pesf-api-大版本)
    - [1.4.6. PeSF API 大版本的支持矩阵](#146-pesf-api-大版本的支持矩阵)

## 1.2. 术语

### 1.2.1. 兼容性（compatibility）术语

是指硬件之间、软件之间或是软硬件组合系统之间的相互协调工作的程度。

#### 1.2.1.1. 向前兼容（Forward Compatibility）

指老的版本的软／硬件可以使用新版本的软／硬件产生的数据。“Forward”一词在这里有“未来”的意思。
**使用新版本 SDK 开发的应用程序可以在过去版本的运行时上运行**

#### 1.2.1.2. 向后兼容（Backward Compatibility）

指新的版本的软件/硬件被设计为能够与过去版本兼容。这意味着新版本的数据、软件或硬件在过去的版本中应该能够正常工作或被支持。
**使用旧版本 SDK 开发的应用程序可以在新版本的运行时上运行**

#### 1.2.1.3. 版本控制（Versioning）

在 API 的演进过程中，通过使用版本号来标识不同的 API 版本。版本控制可确保应用程序使用正确的 API 版本，并为开发人员提供机会管理兼容性问题。

#### 1.2.1.4. 弃用（Deprecation）

指示某个 API 或功能已被官方标记为不推荐使用，并计划在未来的版本中移除。弃用通常会带有警告提示，并提供替代方案或迁移指南。

#### 1.2.1.5. 移除（Removal）

指从 API 中彻底删除某个功能或接口。一般情况下，移除操作会在经过一段时间的弃用后进行，以给应用程序开发者足够的时间进行迁移和调整。

#### 1.2.1.6. 渐进式升级（Progressive Upgrades）

允许应用程序在逐步迁移的过程中使用新版本的 API。这可以减轻应用程序开发者的负担，并为他们提供调整的时间和空间。

#### 1.2.1.7. 大版本升级（Major Version Upgrade）和小版本升级（Minor Version Upgrade）

在软件开发中，"大版本升级"（Major Version Upgrade）和"小版本升级"（Minor Version Upgrade）是两个常见的术语，用于描述软件版本的变化和更新程度。

- **大版本升级**：大版本升级通常指软件的主要版本号的增加，例如从版本 1.0 升级到 2.0。大版本升级意味着软件进行了较大程度的修改、功能增强、重新设计或重构，可能会引入不向后兼容的变化。这意味着旧版本的应用程序可能需要相应地修改或调整才能适应新版本。大版本升级通常引入了重要的新功能或架构变化。
- **小版本升级**：小版本升级通常指软件版本号中次要部分或修订号的增加，例如从版本 1.2 升级到 1.3。小版本升级通常是为了修复错误、增加较小的功能或引入性能优化等。小版本升级通常是向后兼容的，旧版本的应用程序可以无缝地进行升级而无需进行重大修改。

这两种类型的升级在软件版本控制中起着重要的作用。大版本升级通常是一个较为重大的改变，而小版本升级则是在既有软件的基础上进行的一系列较小的改进。根据项目的需求和实际情况，软件开发团队会选择何时进行大版本升级或小版本升级，并在版本号中进行相应的增加和更新。

### 1.2.2. PeSF 术语

#### 1.2.2.1. PeSF 平台（Platform）

#### 1.2.2.2. PeSF 运行时（Runtime）

#### 1.2.2.3. PeSF SDK

#### 1.2.2.4. PeSF 开发工具链（Develop Toolchain）

#### 1.2.2.5. PeSF 应用程序（Application）

## 1.3. PeSF API 的兼容性策略

### 1.3.1. PeSF API 的版本号

PeSF 运行时的版本号分为`大版本`和`小版本`：

a.b， 小数点前表示大版本是 a 版本， 小数点后表示是 a 版本的小版本 b。

> **例如：**
>
> **1.2 版本，表示当前大版本是 1.x ，小版本的 0.2 版。**

PeSF 应用程序开发者可以通过 PeSF API 取得当前 PeSF 运行时的版本号并根据版本号选择使用的 API 版本。

### 1.3.2. PeSF API 小版本升级

#### 1.3.2.1. 保持向前兼容

使用旧版本的 API 的 PeSF 应用可以在相同的大版本的 PeSF 运行时运行。

> **例如：**
>
> **使用了 1.1 版本 PeSF API 的 PeSF 应用程序在不更新应用程序的情况下，是可以在提供 PeSF API 1.5 的运行时上运行的。**

#### 1.3.2.2. 允许出现弃用（Deprecation）

PeSF API 在小版本升级中允许 API 出现`弃用（Deprecation）`标记。

**弃用意味着某个 PeSF API 或功能已被`不推荐使用`，并计划在未来的版本中移除。**
**弃用需要在 PeSF 应用程序开发手册中警告提示，并提供替代方案或迁移指南。**
**为了保证兼容性, 某个 PeSF API 即使弃用也允许被使用，并保证功能正确。**

#### 1.3.2.3. 不出现移除（Removal）

PeSF API 在小版本升级中不允许 API 出现`移除（Removal）`标记。
**不允许在小版本的升级中出现 PeSF API 的移除。**

### 1.3.3. PeSF API 大版本升级

#### 1.3.3.1. 不保证向前兼容

使用旧版本的 PeSF API 的 PeSF 应用程序不保证可以在提供更高大版本的 PeSF 运行时运行。
需要开发者更新应用程序才能在更高的大版本的 PeSF 运行时。

> **例如 ：**
>
> 使用了 1.x 版本 PeSF API 的 PeSF 应用程序在不更新应用程序的情况下，是不保证能在提供 PeSF API 2.x 的运行时上运行的。

#### 1.3.3.2. 允许出现移除（Removal）标记

PeSF API 在大版本的升级中允许 API 出现`移除（Removal）`标记。
**移除意味着某个 PeSF API 或功能已被`不允许使用`，并移除。**

#### 1.3.3.3. 需要给出迁移指导

需要在 PeSF 应用程序开发手册中给出大版本升级的迁移指南。

## 1.4. PeSF SDK 的兼容性策略

在未来的 PeSF 平台开发的设想中，PeSF 平台会提供 `PeSF SDK` 用于帮助 PeSF 应用开发者处理 PeSF 应用程序的兼容性。

### 1.4.1. 渐进式升级（Progressive Upgrades）

PeSF SDK 采用`渐进式升级（Progressive Upgrades）`的策略，**向前兼容 PeSF API 小版本**，并在**一定范围内向后兼容 PeSF API 大版本**。
逐步使用新版本的 PeSF API 并保持对旧 PeSF API 的兼容性。
减轻的 PeSF 应用程序开发者负担, 实现使用新 SDK 的 PeSF 应用程序可以兼容过去数个大版本的 PeSF API。

### 1.4.2. PeSF SDK 的 Level

PeSF SDK 使用 Level 来区分 PeSF SDK 的版本，并根据 PeSF SDK 的 Level 说明对 PeSF API 的兼容性
每当 PeSF API 大版本升级时，PeSF SDK 将升级一个 Level。

> **例如：**
> 
> 当 PeSF API 升级到2.x 时 PeSF SDK Level 升级到 2

### 1.4.3. PeSF SDK 的内部版本号（Internal Version）

PeSF SDK 使用内部版号本来区分 SDK 的版本。内部版本号的格式是 `XX.YY.ZZ`。

- `XX`： 表示 SDK Level。
- `YY`： 更新一般表示增加了新的功能或者优化了性能。
- `ZZ`： 更新一般表示对代码的进行了错误的修正。

> **例如：**
>
> SDK Version 2.02.03

### 1.4.4. 过时的（Outdated）PeSF SDK Level

过去的 PeSF SDK Level 被标记为`过时的（Outdated）`。
PeSF 应用程序开发手册中推荐使用最新版的 PeSF SDK 开发 PeSF 应用程序。

### 1.4.5. 不被支持的（Unsupported） PeSF API 大版本

当最新版本的 PeSF SDK Level 放弃对一个 PeSF API 大版本支持时, 这个 PeSF API 大版本被标记为`不被支持的（Unsupported）`。
推荐开发者使用最新版本的 PeSF SDK 开发自己的 PeSF 应用程序。

### 1.4.6. PeSF API 大版本的支持矩阵

PeSF SDK 对 PeSF API 大版本的支持关系比较复杂，需要使用支持矩阵表示。
以下是一个例子，以实际开发情况为准。

> **例如:**
>
> |                  | **SDK Level 1** | **SDK Level 2** | **SDK Level 3** | **SDK Level 4** | **SDK Level 5** |
> | ---------------- | --------------- | --------------- | --------------- | --------------- | --------------- |
> | **PeSF API 5.x** | 不支持 ×        | 不支持 ×        | 不支持 ×        | 不支持 ×        | 支持 √          |
> | **PeSF API 4.x** | 不支持 ×        | 不支持 ×        | 不支持 ×        | 支持 √          | 支持 √          |
> | **PeSF API 3.x** | 不支持 ×        | 不支持 ×        | 支持 √          | 支持 √          | 支持 √          |
> | **PeSF API 2.x** | 不支持 ×        | 支持 √          | 支持 √          | 支持 √          | 不支持 ×        |
> | **PeSF API 1.x** | 支持 √          | 支持 √          | 支持 √          | 不支持 ×        | 不支持 ×        |
