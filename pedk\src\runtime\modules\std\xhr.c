
#include "runtime/modules/std/curl_utils.h"
#include "runtime/runtime.h"

#include <quickjs.h>
#include <quickjs-libc.h> // TODO 纭蹇呴』锛?
#include <stdbool.h>
#include <ctype.h>
#include <string.h>
#include <strings.h>
#include "API/app_manager/app_manager.h"
#include <unistd.h>

#define xhrFreeValueRT(rt, val) do  {                                                                                   \
    NET_LOG_DEBUG(#val" tag %d\n", JS_VALUE_GET_TAG((val)));                        \
    if ( JS_IsObject( (val) ) || JS_IsString( (val) ) ) {                           \
        NET_LOG_DEBUG(#val" count is %d\n", pp ? pp->ref_count : -1111);            \
        JS_FreeValueRT(rt, val);                                                    \
    }                                                                               \
} while(0)

enum {
    XHR_EVENT_ABORT = 0,
    XHR_EVENT_ERROR,
    XHR_EVENT_LOAD,
    XHR_EVENT_LOAD_END,
    XHR_EVENT_LOAD_START,
    XHR_EVENT_PROGRESS,
    XHR_EVENT_READY_STATE_CHANGED,
    XHR_EVENT_TIMEOUT,
    XHR_EVENT_MAX,
};

enum {
    XHR_RSTATE_UNSENT = 0,
    XHR_RSTATE_OPENED,
    XHR_RSTATE_HEADERS_RECEIVED,
    XHR_RSTATE_LOADING,
    XHR_RSTATE_DONE,
};

enum {
    XHR_RTYPE_DEFAULT = 0,
    XHR_RTYPE_TEXT,
    XHR_RTYPE_ARRAY_BUFFER,
    XHR_RTYPE_JSON,
};

typedef struct {
    JSContext *ctx;
    JSValue events[XHR_EVENT_MAX];
    STPeSFCurlPrivate curl_private;

    struct curl_slist *slist;
    bool sent;
    bool async;
    unsigned long timeout;
    short response_type;
    unsigned short ready_state;
    struct {
        char *raw;
        JSValue status;
        JSValue status_text;
    } status;
    struct {
        JSValue url;
        JSValue headers;
        JSValue response;
        JSValue response_text;
        DynBuf hbuf;
        DynBuf bbuf;
    } result;

    /* wget使用，js传入的路径为“虚拟的绝对路径”前面拼接上/pesp_app/print_app_name/为实际路径 */
    JSValue user_path;
    JSValue overwrite;
} STPeSFXhr;

static JSClassID net_xhr_class_id;

int net_wget_create_dir(const char *file_path, char *app_path);

static void uvTimerCloseCallbackFreeX(uv_handle_t* handle) {
    STPeSFXhr* x;
    STPeSFCurlPrivate* curl_private;

    RETURN_IF_FAIL(handle);
    curl_private = handle->data;
    RETURN_IF_FAIL(curl_private);
    x = curl_private->arg;
    RETURN_IF_FAIL(x);
    NET_LOG_DEBUG("free x %p\n", x);
    free(x);
    x = NULL;
}

static void net_xhr_finalizer(JSRuntime *rt, JSValue val) {
    int rc;
    STPeSFXhr* x = JS_GetOpaque(val, net_xhr_class_id);
    if (x) {
        NET_LOG_DEBUG("~p = %p, x %p, curl_h %p\n", JS_VALUE_GET_PTR(val), x, x->curl_private.curl_h);

        if (x->curl_private.curl_h) {
            if (x->async)
                curl_multi_remove_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
            curl_easy_cleanup(x->curl_private.curl_h);
            x->curl_private.curl_h = NULL;
        }

        if (x->curl_private.curlm_h) {
            curl_multi_cleanup(x->curl_private.curlm_h);
            x->curl_private.curlm_h = NULL;
        }

        rc = uv_timer_stop(&(x->curl_private.uv_timer));
        NET_LOG_DEBUG("stop x %p timer %p rc%d\n", x, &(x->curl_private.uv_timer), rc);
/*
        rc = uv_poll_stop(&(x->curl_private.poll));
        NET_LOG_DEBUG("stop poll %d\n", rc);
        uv_close((uv_handle_t* )(&(x->curl_private.poll)), NULL);
*/
        if (x->slist)
            curl_slist_free_all(x->slist);
        if (x->status.raw)
            js_free_rt(rt, x->status.raw);
        for (int i = 0; i < XHR_EVENT_MAX; i++) {
            JSRefCountHeader *p = (JSRefCountHeader *)JS_VALUE_GET_PTR(x->events[i]);
            NET_LOG_DEBUG("is undef %d, tag %d, count %d\n", JS_IsUndefined(x->events[i]), JS_VALUE_GET_TAG(x->events[i]), p ? p->ref_count : -1);
            /* net_xhr_constructor中赋初值JS_UNDEFINED(ref_count is 0)为普通数值不需要释放, net_xhr_event_set中调用JS_DupValue会++ref_count */
            if ( p && (p->ref_count > 0) && JS_IsLiveObject(rt, x->events[i]) ) {
                JS_FreeValueRT(rt, x->events[i]);
            }
        }
        xhrFreeValueRT(rt, x->status.status);
        xhrFreeValueRT(rt, x->status.status_text);
        xhrFreeValueRT(rt, x->result.url);
        xhrFreeValueRT(rt, x->result.headers);
        xhrFreeValueRT(rt, x->result.response);
        xhrFreeValueRT(rt, x->result.response_text);
        dbuf_free(&x->result.hbuf);
        dbuf_free(&x->result.bbuf);
        xhrFreeValueRT(rt, x->user_path);
        x->overwrite = JS_TRUE;
        uv_close((uv_handle_t* )(&(x->curl_private.uv_timer)), uvTimerCloseCallbackFreeX);
        NET_LOG_DEBUG("end %p\n", x);
    }
}

static void net_xhr_mark(JSRuntime *rt, JSValueConst val, JS_MarkFunc *mark_func) {
    STPeSFXhr* x = JS_GetOpaque(val, net_xhr_class_id);
    if (x) {
        for (int i = 0; i < XHR_EVENT_MAX; i++)
            JS_MarkValue(rt, x->events[i], mark_func);
        JS_MarkValue(rt, x->status.status, mark_func);
        JS_MarkValue(rt, x->status.status_text, mark_func);
        JS_MarkValue(rt, x->result.url, mark_func);
        JS_MarkValue(rt, x->result.headers, mark_func);
        JS_MarkValue(rt, x->result.response, mark_func);
        JS_MarkValue(rt, x->result.response_text, mark_func);
    }
}

static STPeSFXhr* net_xhr_get(JSContext *ctx, JSValueConst obj) {

    return JS_GetOpaque2(ctx, obj, net_xhr_class_id);
}

static void maybe_emit_event(STPeSFXhr* x, int event, JSValue arg) {
    JSContext *ctx = x->ctx;
    JSValue event_func = x->events[event];
    if (!JS_IsFunction(ctx, event_func)) {
        JS_FreeValue(ctx, arg);
        return;
    }

    JSValue func = JS_DupValue(ctx, event_func);
    JSValue ret = JS_Call(ctx, func, JS_UNDEFINED, 1, (JSValueConst *) &arg);
    if (JS_IsException(ret))
        js_std_dump_error(ctx);

    JS_FreeValue(ctx, ret);
    JS_FreeValue(ctx, func);
    JS_FreeValue(ctx, arg);
}

static void curl__done_cb(CURLcode result, void *arg) {
    STPeSFXhr* x = arg;
    RETURN_IF_FAIL(x);

    CURL* easy_handle = x->curl_private.curl_h;
    RETURN_IF_FAIL(easy_handle);

    char *done_url = NULL;
    curl_easy_getinfo(easy_handle, CURLINFO_EFFECTIVE_URL, &done_url);
    if (done_url)
        x->result.url = JS_NewString(x->ctx, done_url);

    if (x->slist) {
        curl_slist_free_all(x->slist);
        x->slist = NULL;
    }

    x->ready_state = XHR_RSTATE_DONE;
    maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);

    if (result == CURLE_OPERATION_TIMEDOUT)
        maybe_emit_event(x, XHR_EVENT_TIMEOUT, JS_UNDEFINED);

    maybe_emit_event(x, XHR_EVENT_LOAD_END, JS_UNDEFINED);

    if (result != CURLE_OPERATION_TIMEDOUT) {
        if (result != CURLE_OK)
            maybe_emit_event(x, XHR_EVENT_ERROR, JS_UNDEFINED);
        else
            maybe_emit_event(x, XHR_EVENT_LOAD, JS_UNDEFINED);
    }
}

static void curlm__done_cb(CURLMsg *message, void *arg) {
    STPeSFXhr* x = arg;
    RETURN_IF_FAIL(x);

    //CURL *easy_handle = message->easy_handle;
    //RETURN_IF_FAIL(easy_handle);
    curl__done_cb(message->data.result, x);

    //TODO 23-12-11 确认是否释放了？net_xhr_finalizer 中是否已为NULL未释放
    // The calling function will disengage the easy handle when this
    // function returns.
    NET_LOG_DEBUG("x=%p, p = %p\n", x, x->curl_private.curl_h);
    x->curl_private.curl_h = NULL;
}

static size_t curl__data_cb(char *ptr, size_t size, size_t nmemb, void *userdata) {
    STPeSFXhr* x = userdata;
    RETURN_VAL_IF_FAIL(x, 0);

    if (x->ready_state == XHR_RSTATE_HEADERS_RECEIVED) {
        x->ready_state = XHR_RSTATE_LOADING;
        maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);
    }

    size_t realsize = size * nmemb;

    if (dbuf_put(&x->result.bbuf, (const uint8_t *) ptr, realsize))
        return -1;

    return realsize;
}

static size_t curl__header_cb(char *ptr, size_t size, size_t nmemb, void *userdata) {
    static const char status_line[] = "HTTP/";
    static const char emptly_line[] = "\r\n";

    STPeSFXhr* x = userdata;
    RETURN_VAL_IF_FAIL(x, 0);

    DynBuf *hbuf = &x->result.hbuf;
    size_t realsize = size * nmemb;
    if (strncmp(status_line, ptr, sizeof(status_line) - 1) == 0) {
        if (hbuf->size == 0) {
            // Fire loadstart on the first HTTP status line.
            maybe_emit_event(x, XHR_EVENT_LOAD_START, JS_UNDEFINED);
        } else {
            dbuf_free(hbuf);
            dbuf_init(hbuf);
        }
        if (x->status.raw) {
            js_free(x->ctx, x->status.raw);
            x->status.raw = NULL;
        }
        // Store status line without the protocol.
        const char *p = memchr(ptr, ' ', realsize);
        if (p) {
            *(ptr + realsize - 2) = '\0';
            x->status.raw = js_strdup(x->ctx, p + 1);
        }
    } else if (strncmp(emptly_line, ptr, sizeof(emptly_line) - 1) == 0) {
        // If the code is not a redirect, this is the final response.
        long code = -1;
        curl_easy_getinfo(x->curl_private.curl_h, CURLINFO_RESPONSE_CODE, &code);
        if (code > -1 && code / 100 != 3) {
            RETURN_VAL_IF_FAIL(x->status.raw, -1);
            x->status.status_text = JS_NewString(x->ctx, x->status.raw);
            x->status.status = JS_NewInt32(x->ctx, code);
            x->ready_state = XHR_RSTATE_HEADERS_RECEIVED;
            maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);
            dbuf_putc(hbuf, '\0');
        }
    } else {
        const char *p = memchr(ptr, ':', realsize);
        if (p) {
            // Lowercae header names.
            for (char *tmp = ptr; tmp != p; tmp++)
                *tmp = tolower(*tmp);
            if (dbuf_put(hbuf, (const uint8_t *) ptr, realsize))
                return -1;
        }
    }

    return realsize;
}

static int curl__progress_cb(void *clientp,
                             curl_off_t dltotal,
                             curl_off_t dlnow,
                             curl_off_t ultotal,
                             curl_off_t ulnow) {

    printf("curl__progress_cb\n");
#if 0
    STPeSFXhr* x = clientp;
    RETURN_VAL_IF_FAIL(x, 0);

    if (x->ready_state == XHR_RSTATE_LOADING) {
#if LIBCURL_VERSION_NUM >= 0x073700 /* added in 7.55.0 */
        curl_off_t cl = -1;
        curl_easy_getinfo(x->curl_private.curl_h, CURLINFO_CONTENT_LENGTH_DOWNLOAD_T, &cl);
#else
        double cl = -1;
        curl_easy_getinfo(x->curl_private.curl_h, CURLINFO_CONTENT_LENGTH_DOWNLOAD, &cl);
#endif
        JSContext *ctx = x->ctx;
        JSValue event = JS_NewObjectProto(ctx, JS_NULL);
        JS_DefinePropertyValueStr(ctx, event, "lengthComputable", JS_NewBool(ctx, cl > 0), JS_PROP_C_W_E);
        JS_DefinePropertyValueStr(ctx, event, "loaded", JS_NewInt64(ctx, dlnow), JS_PROP_C_W_E);
        JS_DefinePropertyValueStr(ctx, event, "total", JS_NewInt64(ctx, dltotal), JS_PROP_C_W_E);
        maybe_emit_event(x, XHR_EVENT_PROGRESS, event);
    }
#endif
    return 0;
}

static JSValue net_xhr_constructor(JSContext *ctx, JSValueConst new_target, int argc, JSValueConst *argv) {
    int rc = -1;
    //runtime_t* qrt = JS_GetContextOpaque(ctx);

    PeSFRunTime *qrt = GET_PESF_RUNTIME(ctx);

    RETURN_VAL_IF_FAIL(qrt, JS_EXCEPTION);

    JSValue obj = JS_NewObjectClass(ctx, net_xhr_class_id);
    if (JS_IsException(obj))
        return obj;

    STPeSFXhr* x = calloc(1, sizeof(STPeSFXhr));
    NET_LOG_DEBUG("new xhr %x, x %p\n", JS_VALUE_GET_PTR(obj), x);
    if (!x) {
        JS_FreeValue(ctx, obj);
        return JS_EXCEPTION;
    }

    x->ctx = ctx;
    x->result.url = JS_NULL;
    x->result.headers = JS_NULL;
    x->result.response = JS_NULL;
    x->result.response_text = JS_NULL;
    dbuf_init(&x->result.hbuf);
    dbuf_init(&x->result.bbuf);
    x->ready_state = XHR_RSTATE_UNSENT;
    x->status.raw = NULL;
    x->status.status = JS_UNDEFINED;
    x->status.status_text = JS_UNDEFINED;
    x->slist = NULL;
    x->sent = false;
    x->async = true;
    x->user_path = JS_UNDEFINED;
    x->overwrite = JS_UNDEFINED;
    for (int i = 0; i < XHR_EVENT_MAX; i++) {
        x->events[i] = JS_UNDEFINED;
    }

    memset(&(x->curl_private), 0, sizeof(STPeSFCurlPrivate));
    x->curl_private.uv_loop = qrt->uv_loop;
    x->curl_private.arg = x;
    x->curl_private.done_cb = curlm__done_cb;

    rc = uv_timer_init(qrt->uv_loop, &(x->curl_private.uv_timer));
    if (PESF_UNLIKELY(0 != rc)) {
        NET_LOG_ERROR("init curl timer\n");
        return JS_EXCEPTION;
    }
    STPeSFCurlPrivate* p = &(x->curl_private);
    x->curl_private.uv_timer.data = p;

    x->curl_private.curlm_h = pesfGetCurlm(p);
    x->curl_private.curl_h = pesfCurlEasyInit(NULL);
    if (PESF_UNLIKELY( (NULL == p->curlm_h) || (NULL == p->curl_h) )) {
        JS_FreeValue(ctx, obj);
        free(x);
        x = NULL;
        NET_LOG_ERROR("init curl\n");
        return JS_EXCEPTION;
    }

    curl_easy_setopt(p->curl_h, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(p->curl_h, CURLOPT_SSL_VERIFYHOST, 1L);
    curl_easy_setopt(p->curl_h, CURLOPT_PRIVATE, &x->curl_private);
    curl_easy_setopt(p->curl_h, CURLOPT_NOPROGRESS, 0L);
    curl_easy_setopt(p->curl_h, CURLOPT_NOSIGNAL, 1L);
    curl_easy_setopt(p->curl_h, CURLOPT_XFERINFOFUNCTION, curl__progress_cb);
    curl_easy_setopt(p->curl_h, CURLOPT_XFERINFODATA, x);
    curl_easy_setopt(p->curl_h, CURLOPT_WRITEFUNCTION, curl__data_cb);
    curl_easy_setopt(p->curl_h, CURLOPT_WRITEDATA, x);
    curl_easy_setopt(p->curl_h, CURLOPT_HEADERFUNCTION, curl__header_cb);
    curl_easy_setopt(p->curl_h, CURLOPT_HEADERDATA, x);

    NET_LOG_DEBUG("set opaque obj%p, x %p, timer handle%p\n", JS_VALUE_GET_PTR(obj), x, &(x->curl_private.uv_timer));
    JS_SetOpaque(obj, x);
    return obj;
}

static JSValue net_xhr_event_get(JSContext *ctx, JSValueConst this_val, int magic) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    return JS_DupValue(ctx, x->events[magic]);
}

static JSValue net_xhr_event_set(JSContext *ctx, JSValueConst this_val, JSValueConst value, int magic) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    if (JS_IsFunction(ctx, value) || JS_IsUndefined(value) || JS_IsNull(value)) {
        JS_FreeValue(ctx, x->events[magic]);
        x->events[magic] = JS_DupValue(ctx, value);
    }
    return JS_UNDEFINED;
}

static JSValue net_xhr_readystate_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    return JS_NewInt32(ctx, x->ready_state);
}

static JSValue net_xhr_response_get(JSContext *ctx, JSValueConst this_val) {
    printf("net_xhr_response_get\n");

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    DynBuf *bbuf = &x->result.bbuf;
    if (bbuf->size == 0)
        return JS_NULL;

    /* 用于wget保存文件 */
    if (JS_IsString(x->user_path)) {
        char* real_save_path = (char*)malloc(PATH_MAX);
        RETURN_JS_VALUE_IF_FAIL(real_save_path, JS_EXCEPTION);
        const char *sub_path = JS_ToCString(ctx, x->user_path);
        snprintf(real_save_path, PATH_MAX, "%s%s", GetAppWorkspace(ctx), sub_path);
        NET_LOG_DEBUG("real_save_path %s\n",real_save_path);

        if (x->overwrite == JS_FALSE && access(real_save_path, F_OK) == 0) {
            char *dot = strrchr(sub_path, '.');
            char *slash = strrchr(sub_path, '/');
            char *extensionName = NULL;
            if(dot && slash && dot > slash) {
                extensionName = dot;
            }

            time_t timestamp = time(NULL);

            const char *user_path = (char*)malloc(PATH_MAX);
            RETURN_JS_VALUE_IF_FAIL(user_path, JS_EXCEPTION);
            snprintf(user_path, PATH_MAX, "%s", sub_path);
            int len = strlen(user_path);
            if(extensionName) {
                int extensionLen = strlen(extensionName);
                snprintf(user_path + (len - extensionLen), PATH_MAX - (len - extensionLen), "_%d%s", timestamp, extensionName);
            } else {
                snprintf(user_path + len, PATH_MAX - len, "_%d", timestamp);
            }

            snprintf(real_save_path, PATH_MAX, "%s%s", GetAppWorkspace(ctx), user_path);

            JS_FreeValue(ctx, x->user_path);
            x->user_path = JS_NewString(ctx, user_path);
            free(user_path);
        }

        ssize_t wb = 0;
        int fd = open(real_save_path, O_CREAT|O_WRONLY|O_TRUNC, S_IRUSR|S_IWUSR);
        NET_LOG_DEBUG("open %s, fd %d\n", real_save_path, fd);
        free(real_save_path);
        if (fd > 0) {
            wb = write(fd, bbuf->buf, bbuf->size);
            close(fd);
        }
        JS_FreeCString(ctx, sub_path);

        return (wb > 0) ? JS_DupValue(ctx, x->user_path) : JS_UNDEFINED;
    }

    if (JS_IsNull(x->result.response)) {
        switch (x->response_type) {
            case XHR_RTYPE_DEFAULT:
            case XHR_RTYPE_TEXT:
                x->result.response = JS_NewStringLen(ctx, (char *) bbuf->buf, bbuf->size);
                break;
            case XHR_RTYPE_ARRAY_BUFFER:
                x->result.response = JS_NewArrayBufferCopy(ctx, bbuf->buf, bbuf->size);
                break;
            case XHR_RTYPE_JSON:
                // It's necessary to null-terminate the string passed to JS_ParseJSON.
                dbuf_putc(bbuf, '\0');
                x->result.response = JS_ParseJSON(ctx, (char *) bbuf->buf, bbuf->size-1, "<xhr>");
                if (JS_EXCEPTION == x->result.response) {
                    x->result.response = JS_NewStringLen(ctx, (char *) bbuf->buf, bbuf->size - 1);
                }
                break;
            default:
                abort();
        }
    }
    return JS_DupValue(ctx, x->result.response);
}

static JSValue net_xhr_responsetext_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    DynBuf *bbuf = &x->result.bbuf;
    if (bbuf->size == 0)
        return JS_NULL;
    if (JS_IsNull(x->result.response_text))
        x->result.response_text = JS_NewStringLen(ctx, (char *) bbuf->buf, bbuf->size);
    return JS_DupValue(ctx, x->result.response_text);
}

static JSValue net_xhr_responsetype_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    switch (x->response_type) {
        case XHR_RTYPE_DEFAULT:
            return JS_NewString(ctx, "");
        case XHR_RTYPE_TEXT:
            return JS_NewString(ctx, "text");
        case XHR_RTYPE_ARRAY_BUFFER:
            return JS_NewString(ctx, "arraybuffer");
        case XHR_RTYPE_JSON:
            return JS_NewString(ctx, "json");
        default:
            abort();
    }
}

static JSValue net_xhr_responsetype_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    static const char array_buffer[] = "arraybuffer";
    static const char json[] = "json";
    static const char text[] = "text";

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;

    if (x->ready_state >= XHR_RSTATE_LOADING)
        JS_Throw(ctx, JS_NewString(ctx, "InvalidStateError"));

    const char *v = JS_ToCString(ctx, value);
    if (v) {
        if (strncmp(array_buffer, v, sizeof(array_buffer) - 1) == 0)
            x->response_type = XHR_RTYPE_ARRAY_BUFFER;
        else if (strncmp(json, v, sizeof(json) - 1) == 0)
            x->response_type = XHR_RTYPE_JSON;
        else if (strncmp(text, v, sizeof(text) - 1) == 0)
            x->response_type = XHR_RTYPE_TEXT;
        else if (strlen(v) == 0)
            x->response_type = XHR_RTYPE_DEFAULT;
        JS_FreeCString(ctx, v);
    }

    return JS_UNDEFINED;
}

static JSValue net_xhr_responseurl_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    return JS_DupValue(ctx, x->result.url);
}

static JSValue net_xhr_status_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    return JS_DupValue(ctx, x->status.status);
}

static JSValue net_xhr_statustext_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    return JS_DupValue(ctx, x->status.status_text);
}

static JSValue net_xhr_timeout_get(JSContext *ctx, JSValueConst this_val) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    return JS_NewInt32(ctx, x->timeout);
}

static JSValue net_xhr_timeout_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;

    int32_t timeout;
    if (JS_ToInt32(ctx, &timeout, value))
        return JS_EXCEPTION;

    x->timeout = timeout;

    if (!x->sent)
        curl_easy_setopt(x->curl_private.curl_h, CURLOPT_TIMEOUT_MS, timeout);

    return JS_UNDEFINED;
}

static JSValue net_wget_save_path_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    RETURN_JS_VALUE_IF_FAIL(x, JS_EXCEPTION);

    return JS_DupValue(ctx, x->user_path);
}

static JSValue net_wget_save_path_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    JSValue js_rv = JS_UNDEFINED;
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    RETURN_JS_VALUE_IF_FAIL(x, JS_EXCEPTION);

    if (!JS_IsString(value))
    {
        NET_LOG_ERROR("userSavePath format error\n");
        return JS_UNDEFINED;
    }
    const char *sub_path = JS_ToCString(ctx, value);
    if ( (NULL == sub_path) || (!isprint(sub_path[0])) ) {
        NET_LOG_ERROR("userSavePath format error, %s\n", sub_path);
        js_rv = JS_ThrowTypeError(ctx, "userSavePath format error");
    }
    int rc = net_wget_create_dir(sub_path, GetAppWorkspace(ctx));
    NET_LOG_DEBUG("create dir %s, %d\n", sub_path, rc);
    if (0 == rc) {
        x->user_path = JS_DupValue(ctx, value);
    } else {
        js_rv = JS_ThrowTypeError(ctx, "file path error: %s", sub_path);
    }
    JS_FreeCString(ctx, sub_path);

    return js_rv;
}

static JSValue net_wget_overwrite_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    RETURN_JS_VALUE_IF_FAIL(x, JS_EXCEPTION);

    return x->overwrite;
}

static JSValue net_wget_overwrite_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {

    if (!JS_IsUndefined(value) && !JS_IsBool(value)) {
        NET_LOG_ERROR("overwrite format error\n");
        return JS_UNDEFINED;
    }

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    RETURN_JS_VALUE_IF_FAIL(x, JS_EXCEPTION);

    if (JS_IsUndefined(value) || value == JS_TRUE) {
        x->overwrite = JS_TRUE;
    } else {
        x->overwrite = JS_FALSE;
    }

    return JS_UNDEFINED;
}

static JSValue net_xhr_upload_get(JSContext *ctx, JSValueConst this_val) {
    // TODO.
    return JS_UNDEFINED;
}

static JSValue net_xhr_withcredentials_get(JSContext *ctx, JSValueConst this_val) {
    // TODO.
    return JS_UNDEFINED;
}

static JSValue net_xhr_withcredentials_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    // TODO.
    return JS_UNDEFINED;
}

static JSValue net_xhr_abort(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    if (x->curl_private.curl_h) {
        curl_multi_remove_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
        curl_easy_cleanup(x->curl_private.curl_h);
        x->curl_private.curl_h = NULL;
        x->curl_private.curlm_h = NULL;
        x->ready_state = XHR_RSTATE_UNSENT;
        JS_FreeValue(ctx, x->status.status);
        x->status.status = JS_NewInt32(x->ctx, 0);
        JS_FreeValue(ctx, x->status.status_text);
        x->status.status_text = JS_NewString(ctx, "");

        maybe_emit_event(x, XHR_EVENT_ABORT, JS_UNDEFINED);
    }
    return JS_UNDEFINED;
}

static JSValue net_xhr_getallresponseheaders(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    DynBuf *hbuf = &x->result.hbuf;
    if (hbuf->size == 0)
        return JS_NULL;
    if (JS_IsNull(x->result.headers))
        x->result.headers = JS_NewStringLen(ctx, (char *) hbuf->buf, hbuf->size - 1);  // Skip trailing null byte.
    return JS_DupValue(ctx, x->result.headers);
}

static JSValue net_xhr_getresponseheader(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    DynBuf *hbuf = &x->result.hbuf;
    if (hbuf->size == 0)
        return JS_NULL;
    const char *header_name = JS_ToCString(ctx, argv[0]);
    if (!header_name)
        return JS_EXCEPTION;

    // Lowercae header name
    for (char *tmp = (char *) header_name; *tmp; tmp++)
        *tmp = tolower(*tmp);

    DynBuf r;
    dbuf_init(&r);
    char *ptr = (char *) hbuf->buf;
    for (;;) {
        // Find the header name
        char *tmp = strstr(ptr, header_name);
        if (!tmp)
            break;
        // Find the end of the header, the \r
        char *p = strchr(tmp, '\r');
        if (!p)
            break;
        // Check if the header has a value
        char *p1 = memchr(tmp, ':', p - tmp);
        if (p1) {
            p1++;  // skip the ":"
            for (; *p1 == ' '; ++p1)
                ;
            // p1 now points to the start of the header value
            // check if it was a header without a value like x-foo:\r\n
            size_t size = p - p1;
            if (size > 0) {
                dbuf_put(&r, (const uint8_t *) p1, size);
                dbuf_putstr(&r, ", ");
            }
        }
        ptr = p;
    }

    JS_FreeCString(ctx, header_name);

    JSValue ret;
    if (r.size == 0)
        ret = JS_NULL;
    else
        ret = JS_NewStringLen(ctx, (const char *) r.buf, r.size - 2);  // skip the last ", "
    dbuf_free(&r);
    return ret;
}

static JSValue net_xhr_open(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {
    static const char head_method[] = "HEAD";
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;

    // TODO: support username and password.

    if (x->ready_state == XHR_RSTATE_DONE) {
        if (x->slist)
            curl_slist_free_all(x->slist);
        if (x->status.raw)
            js_free(ctx, x->status.raw);
        for (int i = 0; i < XHR_EVENT_MAX; i++)
            JS_FreeValue(ctx, x->events[i]);
        JS_FreeValue(ctx, x->status.status);
        JS_FreeValue(ctx, x->status.status_text);
        JS_FreeValue(ctx, x->result.url);
        JS_FreeValue(ctx, x->result.headers);
        JS_FreeValue(ctx, x->result.response);
        JS_FreeValue(ctx, x->result.response_text);
        dbuf_free(&x->result.hbuf);
        dbuf_free(&x->result.bbuf);

        dbuf_init(&x->result.hbuf);
        dbuf_init(&x->result.bbuf);
        x->result.url = JS_NULL;
        x->result.headers = JS_NULL;
        x->result.response = JS_NULL;
        x->result.response_text = JS_NULL;
        x->ready_state = XHR_RSTATE_UNSENT;
        x->status.raw = NULL;
        x->status.status = JS_UNDEFINED;
        x->status.status_text = JS_UNDEFINED;
        x->slist = NULL;
        x->sent = false;
        x->async = true;

        for (int i = 0; i < XHR_EVENT_MAX; i++) {
            x->events[i] = JS_UNDEFINED;
        }
    }
    if (x->ready_state < XHR_RSTATE_OPENED) {
        JSValue method = argv[0];
        JSValue url = argv[1];
        JSValue async = argv[2];
        const char *method_str = JS_ToCString(ctx, method);
        const char *url_str = JS_ToCString(ctx, url);
        if (argc == 3)
            x->async = JS_ToBool(ctx, async);
        if (strncasecmp(head_method, method_str, sizeof(head_method) - 1) == 0)
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_NOBODY, 1L);
        else
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_CUSTOMREQUEST, method_str);
        curl_easy_setopt(x->curl_private.curl_h, CURLOPT_URL, url_str);

        JS_FreeCString(ctx, method_str);
        JS_FreeCString(ctx, url_str);

        x->ready_state = XHR_RSTATE_OPENED;
        maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);
    }

    return JS_UNDEFINED;
}

static JSValue net_xhr_overridemimetype(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {

    return JS_ThrowTypeError(ctx, "unsupported");
}

static JSValue net_xhr_send(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    if (!x->sent) {
        JSValue arg = argv[0];
        if (JS_IsString(arg)) {
            size_t size;
            const char *body = JS_ToCStringLen(ctx, &size, arg);
            if (body) {
                curl_easy_setopt(x->curl_private.curl_h, CURLOPT_POSTFIELDSIZE, (long) size);
                curl_easy_setopt(x->curl_private.curl_h, CURLOPT_COPYPOSTFIELDS, body);
                JS_FreeCString(ctx, body);
            }
        }
        if (x->slist)
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_HTTPHEADER, x->slist);
        if (x->async)
            curl_multi_add_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
        else {
            CURLcode result = curl_easy_perform(x->curl_private.curl_h);
            curl__done_cb(result, x);
        }
        x->sent = true;
    }
    return JS_UNDEFINED;
}

static JSValue net_xhr_setrequestheader(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {

    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    if (!x)
        return JS_EXCEPTION;
    if (!JS_IsString(argv[0]))
        return JS_UNDEFINED;
    const char *h_name, *h_value = NULL;
    h_name = JS_ToCString(ctx, argv[0]);
    if (!JS_IsUndefined(argv[1]))
        h_value = JS_ToCString(ctx, argv[1]);
    char buf[CURL_MAX_HTTP_HEADER];
    if (h_value)
        snprintf(buf, sizeof(buf), "%s: %s", h_name, h_value);
    else
        snprintf(buf, sizeof(buf), "%s;", h_name);
    JS_FreeCString(ctx, h_name);
    if (h_value)
        JS_FreeCString(ctx, h_value);
    struct curl_slist *list = curl_slist_append(x->slist, buf);
    if (list)
        x->slist = list;
    return JS_UNDEFINED;
}

static const JSCFunctionListEntry net_xhr_class_funcs[] = {
    JS_PROP_INT32_DEF("UNSENT", XHR_RSTATE_UNSENT, JS_PROP_ENUMERABLE),
    JS_PROP_INT32_DEF("OPENED", XHR_RSTATE_OPENED, JS_PROP_ENUMERABLE),
    JS_PROP_INT32_DEF("HEADERS_RECEIVED", XHR_RSTATE_HEADERS_RECEIVED, JS_PROP_ENUMERABLE),
    JS_PROP_INT32_DEF("LOADING", XHR_RSTATE_LOADING, JS_PROP_ENUMERABLE),
    JS_PROP_INT32_DEF("DONE", XHR_RSTATE_DONE, JS_PROP_ENUMERABLE),
};

static const JSCFunctionListEntry net_xhr_proto_funcs[] = {
    JS_CGETSET_MAGIC_DEF("onabort", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_ABORT),
    JS_CGETSET_MAGIC_DEF("onerror", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_ERROR),
    JS_CGETSET_MAGIC_DEF("onload", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_LOAD),
    JS_CGETSET_MAGIC_DEF("onloadend", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_LOAD_END),
    JS_CGETSET_MAGIC_DEF("onloadstart", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_LOAD_START),
    JS_CGETSET_MAGIC_DEF("onprogress", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_PROGRESS),
    JS_CGETSET_MAGIC_DEF("onreadystatechange", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_READY_STATE_CHANGED),
    JS_CGETSET_MAGIC_DEF("ontimeout", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_TIMEOUT),
    JS_CGETSET_DEF("readyState", net_xhr_readystate_get, NULL),
    JS_CGETSET_DEF("response", net_xhr_response_get, NULL),
    JS_CGETSET_DEF("responseText", net_xhr_responsetext_get, NULL),
    JS_CGETSET_DEF("responseType", net_xhr_responsetype_get, net_xhr_responsetype_set),
    JS_CGETSET_DEF("responseURL", net_xhr_responseurl_get, NULL),
    JS_CGETSET_DEF("status", net_xhr_status_get, NULL),
    JS_CGETSET_DEF("statusText", net_xhr_statustext_get, NULL),
    JS_CGETSET_DEF("timeout", net_xhr_timeout_get, net_xhr_timeout_set),
    JS_CGETSET_DEF("upload", net_xhr_upload_get, NULL),
    JS_CGETSET_DEF("withCredentials", net_xhr_withcredentials_get, net_xhr_withcredentials_set),
    JS_CGETSET_DEF("userSavePath", net_wget_save_path_get, net_wget_save_path_set),
    JS_CGETSET_DEF("overwrite", net_wget_overwrite_get, net_wget_overwrite_set),
    JS_CFUNC_DEF("abort", 0, net_xhr_abort),
    JS_CFUNC_DEF("getAllResponseHeaders", 0, net_xhr_getallresponseheaders),
    JS_CFUNC_DEF("getResponseHeader", 1, net_xhr_getresponseheader),
    JS_CFUNC_DEF("open", 5, net_xhr_open),
    JS_CFUNC_DEF("overrideMimeType", 1, net_xhr_overridemimetype),
    JS_CFUNC_DEF("send", 1, net_xhr_send),
    JS_CFUNC_DEF("setRequestHeader", 2, net_xhr_setrequestheader),
};

void add_xhr(JSContext* ctx) {
    JSValue proto, obj;
    JSClassDef net_xhr_class = {
        "XMLHttpRequest",
        .finalizer = net_xhr_finalizer,
        .gc_mark = net_xhr_mark,
    };

    /* XHR class */
    JS_NewClassID(&net_xhr_class_id);
    JS_NewClass(JS_GetRuntime(ctx), net_xhr_class_id, &net_xhr_class);
    proto = JS_NewObject(ctx);
    JS_SetPropertyFunctionList(ctx, proto, net_xhr_proto_funcs, countof(net_xhr_proto_funcs));
    JS_SetClassProto(ctx, net_xhr_class_id, proto);

    /* XHR object */
    obj = JS_NewCFunction2(ctx, net_xhr_constructor, "XMLHttpRequest", 1, JS_CFUNC_constructor, 0);
    JS_SetPropertyFunctionList(ctx, obj, net_xhr_class_funcs, countof(net_xhr_class_funcs));
//    JS_SetModuleExport(ctx, m, "XMLHttpRequest", obj);
    JSValue global = JS_GetGlobalObject(ctx);
    JS_DefinePropertyValueStr(ctx, global, "XMLHttpRequest", obj, JS_PROP_C_W_E);
    JS_FreeValue(ctx, global);
}
