/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file threads_pool.h
 * @addtogroup net
 * @{
 * @addtogroup threads_pool
 * <AUTHOR>
 * @date 2023-9-18
 * @brief threads pool header file
 */
#ifndef __THREADS_POOL_H__
#define __THREADS_POOL_H__

struct thread_pool;
typedef struct threads_pool THREADS_POOL_S;

typedef void (*THREAD_TASK_FUNC)(void* arg);

/**
 * @brief       Add the task handler to this threads pool.
 * @param[in]   thiz        : The threads pool object pointer.
 * @param[in]   handler     : The task handling function.
 * @param[in]   arg         : The task handling args.
 * @return      Add result.
 * @retval      == 0        : success\n
 *              <  0        : fail
 * <AUTHOR>
 * @date        2023-9-18
 */
int32_t         threads_pool_add_task   (THREADS_POOL_S* thiz, THREAD_TASK_FUNC handler, void* arg);

/**
 * @brief       Destroy this threads pool.
 * @param[in]   thiz        : The threads pool object pointer.
 * <AUTHOR>
 * @date        2023-9-18
 */
void            threads_pool_destroy    (THREADS_POOL_S* thiz);

/**
 * @brief       Create threads pool.
 * @param[in]   max_threads : The max number of threads in the threads pool.
 * @return      create result.
 * @retval      != NULL     : The THREADS_POOL_S object pointer(success)\n
 *              == NULL     : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
THREADS_POOL_S* threads_pool_create     (uint32_t max_threads);

#endif /* __THREADS_POOL_H__ */
/**
 *@}
 */
