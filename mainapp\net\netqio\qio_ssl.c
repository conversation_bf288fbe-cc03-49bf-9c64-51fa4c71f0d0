/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_ssl.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief QIO object wrapper openssl SSL wrapped TCP/IP sockets
 */
#include <openssl/ossl_typ.h>
#include <openssl/ssl.h>
#include <openssl/err.h>
#include "nettypes.h"
#include "netsock.h"

#define SSL_CIPHERS_LIST    "DHE-RSA-AES256-SHA:DHE-DSS-AES256-SHA:AES256-SHA:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA:AES128-SHA"

#define SSL_UNLOCK()        pthread_mutex_unlock(&s_ssl_mtx_lock)
#define SSL_LOCK()          pthread_mutex_lock(&s_ssl_mtx_lock)

typedef struct priv_info
{
    PI_SOCKET_T     sockfd;
    SSL*            pssl;
    const char*     role;
    int32_t         flag;
}
PRIV_INFO_S;

static pthread_mutex_t  s_ssl_mtx_lock  = PTHREAD_MUTEX_INITIALIZER;
static SSL_CTX*         s_ssl_srv_ctx   = NULL;
static SSL_CTX*         s_ssl_cli_ctx   = NULL;
static char             s_ssl_passwd[128];

/**
 * @brief       Get the password of this pem certificate.
 * @param[out]  buf     : Get the pem password and set this buffer.
 * @param[in]   size    : The buffer size.
 * @param[in]   rwflag  : The flag of read/write.
 * @param[in]   userdata: The callback context.
 * @return      Password length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-21
 */
static int32_t get_pem_password(char* buf, int32_t size, int32_t rwflag, void* userdata)
{
    int32_t len;

    len = (int32_t)strlen(s_ssl_passwd);
    RETURN_VAL_IF(size < len, NET_DEBUG, 0);

    memcpy(buf, s_ssl_passwd, (size_t)len);

    return len;
}

/**
 * @brief       The callback function of ssl error.
 * @param[in]   str     : The error information string.
 * @param[in]   len     : The string length.
 * @param[in]   u       : The callback context.
 * @return      0
 * <AUTHOR> Xin
 * @date        2023-9-21
 */
static int32_t error_callback(const char* str, size_t len, void* u)
{
    NET_WARN("%s", str);
    return 0;
}

/**
 * @brief       create The ssl context.
 * @param[in]   method  : SSLv23_server_method() or SSLv23_client_method().
 * @param[in]   certfile: The certificate file path.
 * @param[in]   keyfile : The key file.
 * @return      Create result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-21
 */
static SSL_CTX* ssl_ctx_create(const SSL_METHOD* method, const char* certfile, const char* keyfile)
{
    SSL_CTX* sslctx = NULL;
    int32_t  ret = -1;

    RETURN_VAL_IF((sslctx = SSL_CTX_new(method)) == NULL, NET_WARN, NULL);
    do
    {
        SSL_CTX_set_default_passwd_cb(sslctx, get_pem_password);
        SSL_CTX_set_options(sslctx, SSL_OP_ALL | SSL_OP_NO_COMPRESSION | SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_RENEGOTIATION);
        SSL_CTX_set_cipher_list(sslctx, SSL_CIPHERS_LIST);

        BREAK_IF(SSL_CTX_use_certificate_chain_file(sslctx, certfile) == 0,           NET_WARN);
        BREAK_IF(SSL_CTX_use_PrivateKey_file(sslctx, keyfile, SSL_FILETYPE_PEM) == 0, NET_WARN);
        BREAK_IF(SSL_CTX_load_verify_locations(sslctx, certfile, 0) == 0,             NET_WARN);

#if (OPENSSL_VERSION_NUMBER < 0x00905100L)
        SSL_CTX_set_verify_depth(ctx, 1);
#endif
        ret = 0;
    }
    while ( 0 );

    if ( ret < 0 )
    {
        ERR_print_errors_cb(error_callback, sslctx);
        SSL_CTX_free(sslctx);
        sslctx = NULL;
    }
    return sslctx;
}

/**
 * @brief       create SSL object as the SSL server.
 * @param[in]   sockfd  : socket handle of this TCP connection.
 * @return      Create result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-21
 */
static SSL* ssl_create_server(PI_SOCKET_T sockfd)
{
    X509*   peer = NULL;
    SSL*    pssl = NULL;
    int32_t retries = 0;
    int32_t result = 0;
    int32_t sslerr = 0;
    int32_t errerr = 0;
    char    errbuf[80];
    char    peer_cn[256];

    RETURN_VAL_IF(s_ssl_srv_ctx == NULL, NET_WARN, NULL);

    SSL_LOCK();
    pssl = SSL_new(s_ssl_srv_ctx);
    if ( pssl != NULL )
    {
        SSL_set_fd(pssl, sockfd);
        do
        {
            ERR_clear_error();
            result = SSL_accept(pssl);
            if ( result == 1 )
            {
                break;
            }

            sslerr = SSL_get_error(pssl, result);
            if ( sslerr == SSL_ERROR_WANT_READ || sslerr == SSL_ERROR_WANT_WRITE )
            {
                if ( retries == 0 )
                {
                    NET_DEBUG("waiting for socket(%d) to contirm the certificate of server...", sockfd);
                }
                pi_msleep(200);
                continue;
            }
            else
            {
                errerr = ERR_get_error();
                NET_DEBUG("SSL_accept(%d) result(%d) sslerr(%d<0x%08x %s>)", sockfd, result, sslerr, errerr, ERR_error_string(errerr, errbuf));
                break;
            }
        }
        while ( retries++ < 10 );

        if ( result == 1 )
        {
            ERR_clear_error();
            if ( SSL_get_verify_result(pssl) != X509_V_OK )
            {
                NET_WARN("SSL Certificate failed to verify: %s", ERR_error_string(ERR_get_error(), errbuf));
                peer = SSL_get_peer_certificate(pssl);
                X509_NAME_get_text_by_NID(X509_get_subject_name(peer), NID_commonName, peer_cn, (int32_t)sizeof(peer_cn));
                result = 0;
            }
        }

        if ( result != 1 )
        {
            SSL_free(pssl);
            pssl = NULL;
        }
    }
    else
    {
        NET_WARN("SSL_new failed: %d<%s>", errno, strerror(errno));
    }
    SSL_UNLOCK();

    return pssl;
}

/**
 * @brief       create SSL object as the SSL client.
 * @param[in]   sockfd  : socket handle of this TCP connection.
 * @return      Create result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-21
 */
static SSL* ssl_create_client(PI_SOCKET_T sockfd)
{
    SSL*    pssl = NULL;
    int32_t retries = 0;
    int32_t result = 0;
    int32_t sslerr = 0;
    int32_t errerr = 0;
    char    errbuf[80];

    RETURN_VAL_IF(s_ssl_cli_ctx == NULL, NET_WARN, NULL);

    SSL_LOCK();
    pssl = SSL_new(s_ssl_cli_ctx);
    if ( pssl != NULL )
    {
        SSL_set_fd(pssl, sockfd);
        do
        {
            ERR_clear_error();
            result = SSL_connect(pssl);
            if ( result == 1 )
            {
                break;
            }

            sslerr = SSL_get_error(pssl, result);
            if ( sslerr == SSL_ERROR_WANT_READ || sslerr == SSL_ERROR_WANT_WRITE )
            {
                if ( retries == 0 )
                {
                    NET_DEBUG("waiting for socket(%d) to contirm the certificate of client...", sockfd);
                }
                pi_msleep(200);
                continue;
            }
            else
            {
                errerr = ERR_get_error();
                NET_DEBUG("SSL_connect(%d) result(%d) sslerr(%d<0x%08x %s>)", sockfd, result, sslerr, errerr, ERR_error_string(errerr, errbuf));
                break;
            }
        }
        while ( retries++ < 10 );

        if ( result != 1 )
        {
            SSL_free(pssl);
            pssl = NULL;
        }
    }
    else
    {
        NET_WARN("SSL_new failed: %d<%s>", errno, strerror(errno));
    }
    SSL_UNLOCK();

    return pssl;
}

/**
 * @brief       The callback function of QIO_CLOSE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for SSL connection.
 * @return      Close result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_ssl_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    PI_SOCKET_T sockfd;

    RETURN_VAL_IF(pqio == NULL, NET_WARN, QIOEOF);

    if ( priv )
    {
        NET_DEBUG("close SSL(%s %d) QIO<%p>", priv->role, priv->sockfd, pqio);
        SSL_LOCK();
        if ( priv->pssl )
        {
            if ( priv->flag == NETQIO_SOCK_CLOSE )
            {
                sockfd = SSL_get_fd(priv->pssl);
                if ( sockfd != priv->sockfd )
                {
                    NET_WARN("sockfd(%d - %d) mismatch", sockfd, priv->sockfd);
                }
                pi_closesock(sockfd);
            }
            SSL_free(priv->pssl);
            priv->pssl = NULL;
        }
        SSL_UNLOCK();
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

/**
 * @brief       The callback function of QIO_READABLE(pqio) or QIO_WRITEABLE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for SSL connection.
 * @return      Poll result
 * @retval      > 0     : this QIO_S object can be read or written\n
 *              ==0     : poll timeout\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_ssl_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    struct timeval  tv = { .tv_sec = (__time_t)tos, .tv_usec = (__suseconds_t)tous };
    fd_set          fds;
    fd_set*         rfds = ( what & QIO_POLL_READ  ) ? &fds : NULL;
    fd_set*         wfds = ( what & QIO_POLL_WRITE ) ? &fds : NULL;
    fd_set*         efds = ( what & QIO_POLL_EVENT ) ? &fds : NULL;
    PI_SOCKET_T     sockfd;

    RETURN_VAL_IF(priv == NULL || priv->pssl == NULL, NET_WARN, QIOEOF);

    sockfd = SSL_get_fd(priv->pssl);
    RETURN_VAL_IF(sockfd < 0, NET_WARN, QIOEOF);

    FD_ZERO(&fds);
    FD_SET(sockfd, &fds);

    return select(sockfd + 1, rfds, wfds, efds, (tos < 0 || (tos == 0 && tous < 0)) ? NULL : &tv);
}

/**
 * @brief       The callback function of QIO_READ(pqio), recv data from sockfd.
 * @param[in]   pqio    : The QIO_S object pointer for SSL connection.
 * @return      Read result
 * @retval      >=0     : recv the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_ssl_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    int32_t chunk_size = 0;
    int32_t retries = 0;
    int32_t rlen = 0;
    int32_t rtotal = 0;
    int32_t syserr = 0;
    int32_t sslerr = 0;
    int32_t errerr = 0;
    char    errbuf[80];

    RETURN_VAL_IF(priv == NULL || priv->pssl == NULL, NET_WARN, QIOEOF);

    while ( nbuf > (size_t)rtotal && retries < 100 )
    {
        chunk_size = (int32_t)(((nbuf - rtotal) < TCP_CHUNK_SIZE) ? (nbuf - rtotal) : TCP_CHUNK_SIZE);

        ERR_clear_error();
        rlen = SSL_read(priv->pssl, (char *)buffer + rtotal, chunk_size);
        syserr = errno;

        if ( rlen > 0 )
        {
            rtotal += rlen;
        }
        else if ( rtotal > 0 )
        {
            break;
        }
        else
        {
            sslerr = SSL_get_error(priv->pssl, rlen);
            if ( sslerr == SSL_ERROR_WANT_READ )
            {
                if ( (++retries) % 10 == 0 )
                {
                    NET_INFO("QIO_S<%p> rlen(%d) rtotal(%d) retries(%d)", pqio, rlen, rtotal, retries);
                }
                pi_msleep(200);
                continue;
            }
            else
            {
                if ( (sslerr == SSL_ERROR_ZERO_RETURN || sslerr == SSL_ERROR_SYSCALL) && rlen == 0 && syserr == 0 )
                {
                    /* rlen == 0 && rtotal == 0 && errno == 0 对端关闭 */
                }
                else
                {
                    errerr = ERR_get_error();
                    NET_DEBUG("QIO_S<%p> rlen(%d) sslerr(%d<0x%08x %s>) errno(%d)", pqio, rlen, sslerr, errerr, ERR_error_string(errerr, errbuf), syserr);
                }
                rtotal = rlen;
                break;
            }
        }
    }

    return rtotal;
}

/**
 * @brief       The callback function of QIO_WRITE(pqio), send data to TCP stream.
 * @param[in]   pqio    : The QIO_S object pointer for SSL connection.
 * @return      Write result
 * @retval      >=0     : send the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_ssl_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    int32_t chunk_size = 0;
    int32_t retries = 0;
    int32_t wlen = 0;
    int32_t wtotal = 0;
    int32_t syserr = 0;
    int32_t sslerr = 0;
    int32_t errerr = 0;
    char    errbuf[80];

    RETURN_VAL_IF(priv == NULL || priv->pssl == NULL, NET_WARN, QIOEOF);

    while ( nbuf > (size_t)wtotal && retries < 100 )
    {
        chunk_size = (int32_t)(((nbuf - wtotal) < TCP_CHUNK_SIZE) ? (nbuf - wtotal) : TCP_CHUNK_SIZE);

        ERR_clear_error();
        wlen = SSL_write(priv->pssl, (char *)buffer + wtotal, chunk_size);
        syserr = errno;

        if ( wlen > 0 )
        {
            wtotal += wlen;
        }
        else
        {
            sslerr = SSL_get_error(priv->pssl, wlen);
            if ( sslerr == SSL_ERROR_WANT_WRITE )
            {
                if ( (++retries) % 10 == 0 )
                {
                    NET_INFO("QIO_S<%p> wlen(%d) wtotal(%d) retries(%d)", pqio, wlen, wtotal, retries);
                }
                pi_msleep(200);
            }
            else
            {
                errerr = ERR_get_error();
                NET_DEBUG("QIO<%p> wlen(%d) wtotal(%d) sslerr(%d<0x%08x %s>) errno(%d)", pqio, wlen, wtotal, sslerr, errerr, ERR_error_string(errerr, errbuf), syserr);
                break;
            }
        }
    }

    if ( wtotal == 0 && nbuf > 0 )
    {
        wtotal = -1;
    }
    return wtotal;
}

/**
 * @brief       The callback function of QIO_SEEK(pqio), unsupported.
 * @param[in]   pqio    : The QIO_S object pointer for SSL connection.
 * @return      Seek result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_ssl_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

QIO_S* qio_ssl_create_custom(int32_t n, PI_SOCKET_T sockfd, NETQIO_SSL_ROLE_E role, ...)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;
    va_list         arg;

    RETURN_VAL_IF(sockfd < 0, NET_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, NET_WARN, NULL);

    if ( n == 3 )
    {
        va_start(arg, role);
        priv->flag = va_arg(arg, int32_t);
        va_end(arg);
    }
    else if ( n == 2 )
    {
        priv->flag = NETQIO_SOCK_CLOSE;
    }
    else
    {
        NET_WARN("invalid parameter count(%d)", n);
        pi_free(priv);
        return NULL;
    }

    if ( role == NETQIO_SSL_SERVER )
    {
        priv->pssl = ssl_create_server(sockfd);
        priv->role = "server";
    }
    else
    {
        priv->pssl = ssl_create_client(sockfd);
        priv->role = "client";
    }
    priv->sockfd = sockfd;

    if ( priv->pssl == NULL )
    {
        NET_DEBUG("create SSL(%s %d) end", priv->role, priv->sockfd);
        pi_free(priv);
        return NULL;
    }

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        NET_WARN("alloc SSL(%s %d) QIO failed: %d<%s>", priv->role, priv->sockfd, errno, strerror(errno));
        SSL_free(priv->pssl);
        pi_free(priv);
        return NULL;
    }

    NET_DEBUG("create SSL(%s %d) QIO<%p>", priv->role, priv->sockfd, pqio);
    pqio->close = qio_ssl_close;
    pqio->poll  = qio_ssl_poll;
    pqio->read  = qio_ssl_read;
    pqio->write = qio_ssl_write;
    pqio->seek  = qio_ssl_seek;
    pqio->priv  = (void *)priv;

    return pqio;
}

int32_t qio_ssl_init(const char* certfile, const char* password)
{
    int32_t ret = -1;

    RETURN_VAL_IF(certfile == NULL || password == NULL, NET_WARN, -1);

    NET_DEBUG("certfile(%s) password(%s)", certfile, password);
    SSL_LOCK();
    do
    {
        SSL_library_init();
        OpenSSL_add_all_algorithms();
        SSL_load_error_strings();
        ERR_load_ERR_strings();
        ERR_load_BIO_strings();

        snprintf(s_ssl_passwd, sizeof(s_ssl_passwd), "%s", password);

        if ( s_ssl_srv_ctx != NULL )
        {
            NET_DEBUG("reload s_ssl_srv_ctx(%p)", s_ssl_srv_ctx);
            SSL_CTX_free(s_ssl_srv_ctx);
        }
        s_ssl_srv_ctx = ssl_ctx_create(SSLv23_server_method(), certfile, certfile);
        NET_DEBUG("s_ssl_srv_ctx(%p)", s_ssl_srv_ctx);
        BREAK_IF(s_ssl_srv_ctx == NULL, NET_WARN);

        if ( s_ssl_cli_ctx != NULL )
        {
            NET_DEBUG("reload s_ssl_cli_ctx(%p)", s_ssl_cli_ctx);
            SSL_CTX_free(s_ssl_cli_ctx);
        }
        s_ssl_cli_ctx = ssl_ctx_create(SSLv23_client_method(), certfile, certfile);
        NET_DEBUG("s_ssl_cli_ctx(%p)", s_ssl_cli_ctx);
        BREAK_IF(s_ssl_cli_ctx == NULL, NET_WARN);

        ret = 0;
    }
    while ( 0 );
    SSL_UNLOCK();

    return ret;
}

void qio_ssl_deinit(void)
{
    if ( s_ssl_srv_ctx != NULL )
    {
        SSL_CTX_free(s_ssl_srv_ctx);
        s_ssl_srv_ctx = NULL;
    }
    if ( s_ssl_cli_ctx != NULL )
    {
        SSL_CTX_free(s_ssl_cli_ctx);
        s_ssl_cli_ctx = NULL;
    }
}
/**
 *@}
 */
