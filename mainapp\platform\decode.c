#include <stdio.h>
#include <string.h>


char key[128] = "xxxaaabbbcccxxx";


int main(int argc, char **argv)
{
	if (argc != 2) {
		printf("usage: decode file en_machine_config.ini\n");
		return -1;
	}
	printf("go to decode file: %s\n", argv[1]);

	FILE *r_fp = NULL;
	FILE *w_fp = NULL;
	char *src_file = argv[1];
	r_fp = fopen(src_file, "rw");
	if (r_fp == NULL) {
		printf("open file err\n");
		return -1;
	}
	
	char dest_file[128] ={0};
	snprintf(dest_file, 128, "%s", "decode.ini");
	printf("output file: %s\n", dest_file);

	w_fp = fopen(dest_file, "w+");
	if (w_fp == NULL) {
		printf("open output file err\n");
        fclose(r_fp);
		return -1;
	}
	char enkey;
	int index = 0;
	char rbuff;
	int keylen = strlen(key);
	while(!feof(r_fp)) {
		fread(&rbuff, 1, 1, r_fp);
		if (!feof(r_fp)) {
			enkey = key[index % keylen];
			rbuff = rbuff ^ enkey;
			fwrite(&rbuff, 1, 1, w_fp);
			index++;
		}
	}

	fclose(r_fp);
	fclose(w_fp);
	return 0;
}
