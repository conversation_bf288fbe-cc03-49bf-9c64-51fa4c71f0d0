//*****************************************************************************
//  Copyright (C) 2012 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/incl/ipp.h#7 $
//  $Change: 244499 $ $Date: 2014/11/25 $
//  
/// @file
/// IPP (Internet Print Protocol) Public Inteface
/// 
/// @ingroup Network
///
/// <AUTHOR> <<EMAIL>>
//  
//*****************************************************************************
#ifndef IPP_H
#define IPP_H 1

#include "nettypes.h"
#include "ringbuf.h"

#define IPP_MAX_TCP_SERVERS 5

/// Bit-mask of IPP Service type, used to select which service
/// to get or set an attribute from
///
#define IPPSERVICE_NONE     0x00    ///< default
#define IPPSERVICE_DEFAULTS 0x01    ///< compiled in default attribute set
#define IPPSERVICE_PRINT    0x02    ///< printer service attribute set
#define IPPSERVICE_FAX      0x04    ///< fax service set
#define IPPSERVICE_ALL      0xFE    ///< all services except the default set

//**************************************************************************
// QIO implementation for IPP data source.  This is the QIO given to
// a PDL parser for reading stream data
//
// The only way for this QIO to close is for the PDL thread(s) to close it
//
#define IPP_MAX_LENGTH  (65536 * 4)
#define IPP_MAX_NAME    256

#define IPP_INUSE_FIDELITY              0x0001
#define IPP_INUSE_COPIES                0x0002
#define IPP_INUSE_COLLATE               0x0004
#define IPP_INUSE_MEDIA                 0x0008
#define IPP_INUSE_SCALE                 0x0010
#define IPP_INUSE_ROTATE                0x0020
#define IPP_INUSE_RESOLUTION            0x0040
#define IPP_INUSE_PDL_SET               0x0080
#define IPP_INUSE_SIDES                 0x0100
#define IPP_INUSE_PAGE_RANGES           0x0200
#define IPP_INUSE_QUALITY               0x0400
#define IPP_INUSE_NUP                   0x0800
#define URF_RESET_TIMEOUT_FLAG 1 //fixbug6029 wangzhigao 2016.9.22
#define JPEG_RESET_TIMEOUT_FLAG 1

int     IPPATTRgetPrinterAttribute  (
                                    uint32_t service,     /// [in] which IPP service (print/fax)
                                    const char *name,   /// [in] name of attribute to get value of
                                    int *type,          /// [out] the value type
                                    void **value,       /// [out] pointer to value(s)
                                    int index,          /// [in] index of value to get (for array types)
                                    int *count          /// [out] count of number of values
                                    );

int     IPPATTRsetPrinterAttribute  (
                                    uint32_t service,     /// [in] which IPP service (print/fax)
                                    const char *name,   /// [in] which attribute to set value for
                                    void *value,        /// [in] pointer to value
                                    int index           /// [in] which [index] value to set
                                    );
#endif

