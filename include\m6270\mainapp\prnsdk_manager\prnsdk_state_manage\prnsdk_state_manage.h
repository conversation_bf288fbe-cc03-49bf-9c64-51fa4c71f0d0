/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file prnsdk_state_manager.c
 * @addtogroup mainapp
 * @{
 * @brief PRINT SDK state manager module
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-09
 */

#ifndef _PRNSDK_STATE_MANAGE_H
#define _PRNSDK_STATE_MANAGE_H

#ifdef __cplusplus
extern "C"{
#endif

#include <pthread.h>

/*
*@brief PRNSDK access state  接入状态
*/
typedef enum {
    STATE_TYPE_UNACCESS,        ///< 未接入
    STATE_TYPE_ACCESSING,       ///< 接入中
    STATE_TYPE_ACCESSED,        ///< 已接入

    STATE_TYPE_ACCESS_MAX,
}STATE_ACCESS_TYPE_E;

/*
*@brief PRNSDK login state  登录状态
*/
typedef enum {
    STATE_TYPE_UNLOGIN,         ///< 未登录
    STATE_TYPE_LOGGING_IN,      ///< 登录中
    STATE_TYPE_LOGGED_IN,       ///< 已登录

    STATE_TYPE_LOG_MAX,
}STATE_LOGIN_TYPE_E;

/*
*@brief PRNSDK authorization state  授权状态
*/
typedef enum {
    STATE_TYPE_UNAUTHORIZED,    ///< 未授权
    STATE_TYPE_AUTHORIZED,      ///< 已授权

    STATE_TYPE_AUTHORIZATION_MAX,
}STATE_AUTHORIZATION_TYPE_E;

/*
*@brief PRNSDK control state  管控状态
*/
typedef enum {
    MANAGE_CONTROL_MODE_LOCAL,
    MANAGE_CONTROL_MODE_SDK,

    MANAGE_CONTROL_MODE_MAX,
}MANAGE_CONTROL_MODE_TYPE_E;

/*
*@brief PRNSDK login method  用户登录方式
*/
typedef enum {
    STATE_LOGINMETHOD_ACCOUNT,
    STATE_LOGINMETHOD_QRCODE,
    STATE_LOGINMETHOD_CARD,

    STATE_LOGINMETHOD_MAX,
}STATE_LOGINMETHOD_E;


/*
*@brief PRNSDK state mange data
*/
typedef struct {
    pthread_spinlock_t          spin_access;
    pthread_spinlock_t          spin_login;
    pthread_spinlock_t          spin_authorization;
    pthread_spinlock_t          spin_control_mode;
    pthread_spinlock_t          spin_user_id;

    STATE_ACCESS_TYPE_E         state_access;
    STATE_LOGIN_TYPE_E          state_login;
    STATE_AUTHORIZATION_TYPE_E  state_authorization;
    MANAGE_CONTROL_MODE_TYPE_E  manage_control_mode;
}STATE_MANAGE_S;

/**
 * @brief  Get PRINT SDK version
 * @return SDK version string
 * @autor  liangshiqin
 * @date   2024-12-09
 */
char * get_sdk_version( void );

/**
 * @brief  Get PRINT SDK Access State
 * @return state_access STATE_ACCESS_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
STATE_ACCESS_TYPE_E state_manage_access_get ( void );

/**
 * @brief     Set PRINT SDK Access State
 * @param[in] state_access STATE_ACCESS_TYPE_E
 * @autor     liangshiqin
 * @date      2024-12-09
 */
 void state_manage_access_set (STATE_ACCESS_TYPE_E state_access);

/**
 * @brief  Get PRINT SDK Login State
 * @return STATE_LOGIN_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
STATE_LOGIN_TYPE_E state_manage_login_get ( void );

/**
 * @brief  Set PRINT SDK Login State
 * @param[in] state_login STATE_LOGIN_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 void state_manage_login_set (STATE_LOGIN_TYPE_E state_login);
/**
 * @brief  Get PRINT SDK Authorization State
 * @return STATE_AUTHORIZATION_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
STATE_AUTHORIZATION_TYPE_E state_manage_authorization_get ( void );

/**
 * @brief  Set PRINT SDK Authorization State
 * @param[in] state_authorization STATE_AUTHORIZATION_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 void state_manage_authorization_set (STATE_AUTHORIZATION_TYPE_E state_authorization);

/**
 * @brief  Get PRINT SDK Manage Control mode
 * @return MANAGE_CONTROL_MODE_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 MANAGE_CONTROL_MODE_TYPE_E manage_control_model_get ( void );

/**
 * @brief  Set PRINT SDK Manage Control mode
 * @param[in] manage_control_mode MANAGE_CONTROL_MODE_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 void manage_control_model_set (MANAGE_CONTROL_MODE_TYPE_E manage_control_mode);

/**
 * @brief  Get PRINT SDK Manage Control Mutex init
 * @return 0 - succeess  -1 - FAIL
 * @autor  liangshiqin
 * @date   2024-12-09
 */
int32_t manage_control_mutex_init ( void );

/**
 * @brief  Get User-provided buffer of PRINT SDK Manage
 * @param[in]  lenth Target length of the hexadecimal string.
 * @param[out] buffer User-provided buffer to store the generated hexadecimal string.
 * @return 0 - succeess  -1 - FAIL
 * @autor  liangshiqin
 * @date   2024-12-09
 */
int32_t state_randomnum_get(char *buffer, size_t buffer_size, int32_t length);

/**
 * @brief  Request add printer
 * @param[in] buf
 * @return 0-STATE is ACCESSED -1-STATE is UNACCESS
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 int32_t state_printer_add_request (char *buf);

/**
 * @brief  Request add printer
 * @param[in] login_method
 * @param[in] data input data
 * @return 0 - succeess  -1 - FAIL
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 int32_t state_login_password(STATE_LOGINMETHOD_E login_method, char *data);

/**
 * @brief  Login State handle
 * @param[in] buf
 * @return STATE_LOGIN_TYPE_E
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 STATE_LOGIN_TYPE_E state_login_result_pro(char *buf);

/**
 * @brief  Logout  State handle
 * @return 0 - succeess  -1 - FAIL
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 int32_t state_logout_password(void);

/**
 * @brief  Get PRINT SDK State Manage user ID
 * @return m_user_id
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 uint32_t state_get_userid (void);

/**
 * @brief  Reset PRINT SDK State Manage
 * @return 0 - succeess  -1 - FAIL
 * @autor  liangshiqin
 * @date   2024-12-09
 */
 int32_t state_manage_reset (void);

#ifdef __cplusplus
}
#endif

#endif  ///< _PRNSDK_STATE_MANAGE_H
/**
 * @}
 */

