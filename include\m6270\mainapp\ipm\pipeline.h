/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       pipeline.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-10-29
 * @version    v1.0
 * @details    implement a pipeline
 */


#ifndef PIPELINE_H
#define PIPELINE_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "pol/pol_threads.h"

#include "pipe_core.h"

/**************************************************************
    pipeline config
****************************************************************/
#define MAX_PIPE_NAME    64         ///< define max pipe name str len

struct tag_pipeline;

/**
 * @brief action type of pipeline
 */
typedef enum
{
    PIPELINE_BUILDING = 0,       ///< pipeline building
    PIPELINE_UPDATE,            ///< pipeline update
    PIPELINE_RUNNING,               ///< pipeline running
    PIPELINE_REMOVING,          ///< pipeline removing
}PIPELINE_ACTION_E;

/**
 * @brief action result of pipeline
 */
typedef enum
{
    PIPELINE_SUCCESS = 0,        ///< pipeline action success
    PIPELINE_FAIL,               ///< pipeline action failed
    PIPELINE_EOI,               ///< pipeline action eoi
    PIPELINE_EOJ,               ///< pipeline action eoj
}PIPELINE_RESULT_E;

/**
 * @brief status of pipeline
 */
typedef enum
{
    PIPELINE_INVALID = 0,       ///< pipeline status is invaild
    PIPELINE_IDLE,              ///< pipeline status is idle
    PIPELINE_RUN,               ///< pipeline status is run
    PIPELINE_FINISH,            ///< pipeline status is finish
    PIPELINE_ERROR,             ///< pipeline status is error
}PIPELINE_STATUS_E;

/**
 * @brief wake current pipe callback
 * @param[in] pipeline current pipeline object
 * @param[in] state pipeline status
 * @param[in] action pipeline action
 * @param[in] result pipeline run result
 * @return int32_t \n
 * @retval callback run result
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef int32_t (*PIPELINE_CALLBACK)  (struct tag_pipeline *pipeline,PIPELINE_STATUS_E state,
                                        PIPELINE_ACTION_E action,PIPELINE_RESULT_E result);

/**
 * @brief pipeline Object
 */
typedef struct tag_pipeline
{
    int32_t          id;               ///< pipeline id
    PIPE_P           pipe;             ///< point to pipe
    PIPELINE_STATUS_E   state;       ///< pipe state

    PI_MAILBOX_T    mailbox;        ///< event wait msg q
    PI_THREAD_T     thread_id;      ///< work thread
    PIPELINE_CALLBACK    callback;   ///< callback
    PI_MUTEX_T    lock;             ///< lock

    void               *context;       ///< point to object of the up layout
    MEMOBJ_P           hImem;          ///< memory object

    PI_SEMAPHORE_T     pause;          ///< Semaphore handle

    struct tag_pipeline *prev;      ///< list prev
    struct tag_pipeline *next;      ///< list next
}PIPE_LINE_S, *PIPE_LINE_P;

/**
 * @brief pipeline process core
 * @param[in] pipeline current pipeline object
 * @param[in] wake pipeline wake
 * @return int32_t \n
 * @retval 0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipeline_send_runing_msg_to_pipeline(PIPE_LINE_P pipeline, int wake);

/**
 * @brief init pipe core module
 * @param[in] void
 * @return PIPE_LINE_P \n
 * @retval current pipeline object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
PIPE_LINE_P pipeline_create();

/**
 * @brief destroy pipe core module
 * @param[in] pipeline current pipeline object
 * @return int32_t \n
 * @retval 0
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipeline_destroy(PIPE_LINE_P pipeline);

/**
 * @brief init step core module
 * @param[in] void
 * @return int32_t \n
 * @retval 0:success -1:faild
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipeline_prolog();

/**
 * @brief destroy step core module
 * @param[in] void
 * @return int32_t \n
 * @retval 0:success -1:faild
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipeline_epilog();


#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* PIPE_CORE_H */

/**
 *@}
 */


