/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  image_prossing_manager.h
* @addtogroup ipm
*
* @{
* @addtogroup  image_prossing_mgr_submodule
* <AUTHOR> (<EMAIL>)
* @date  2021-12-25
* @version  v1.0
* @brief  image processing manager module
**/

#ifndef IMAGE_PROCESSING_MANAGER_H
#define IMAGE_PROCESSING_MANAGER_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

/**
 * @brief image processing manager prolog
 * @return  return the vaule\n
 * @retval 0:success 1:faild
 * <AUTHOR> (<EMAIL>)
 * @date 2021-12-25
 */
int32_t image_processing_manager_prolog();

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* IMAGE_PROCESSING_MANAGER_H */

/**
* @}
**/

