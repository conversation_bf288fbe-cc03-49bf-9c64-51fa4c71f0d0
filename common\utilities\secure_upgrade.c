/**
 * @copyright 2025 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file secure_upgrade.c
 * @addtogroup utilities
 * @{
 * <AUTHOR>
 * @date 2025-07-2
 * @version v1.0
 * @brief Disables trusted measurement for the secure boot secure_reboot
 */
#include "utilities/secure_upgrade.h"
#include "pol/pol_log.h"
#include "pol/pol_threads.h"
#include <stdio.h>      
#include <string.h>     
#include <unistd.h>     
#include <fcntl.h>      
#include <errno.h>      
#include <sys/reboot.h> 



#define ENDIANSWAP32(x) ((((x) & 0xff)<<24) | (((x) & 0xff00) << 8) | (((x) & 0xff0000)>>8) | (((x)>>24) & 0xff))


static int write_platform(int httc_switch , const char *version)
{
    pi_log_i("-- httc set disable \n");
	int mfd = open("/dev/mtd0" , O_RDWR);
	if(mfd==-1)
	{
		pi_log_e("open error: %s\n",strerror(errno));
		return -1;
	}
	
	typedef struct {
		unsigned int type;
		unsigned int boot_addr;
		unsigned int boot_size;
		char         boot_ver[32];
		unsigned int httc_enable;
	} tpcm_plat_info_t;
	tpcm_plat_info_t platform_info={0};
	if(version != NULL)
	{
		memcpy(platform_info.boot_ver, version , strlen(version));
	}
	else
	{
		pi_log_e("version porint NULL\n");
		return -1;
	}
	platform_info.boot_addr = ENDIANSWAP32(0x0);
	platform_info.boot_size = ENDIANSWAP32(0x800000);
	platform_info.type = ENDIANSWAP32(10);
	platform_info.httc_enable = ENDIANSWAP32(httc_switch);
	if (lseek(mfd , 0x700000 , SEEK_SET) == -1)
	{
		pi_log_e("write platform seek error : %s\n" , strerror(errno));
        close(mfd);
		return -1;
	}
	if (write(mfd , &platform_info , sizeof(tpcm_plat_info_t)) == -1)
	{
		pi_log_e("write platform error : %s\n" , strerror(errno));
        close(mfd);
		return -1;
	}
    close(mfd);
	return 0;
}


int secure_reboot()
{	
	pi_log_d("start secure_reboot\n");
    pi_runcmd(NULL, 0, 0,"modprobe spi-nor.ko");
    pi_runcmd(NULL, 0, 0,"modprobe m25p80");
    if(write_platform(0x0 , "0.0.0")==-1)
	{	
		pi_log_e("write_platform error\n");
		return -1;
	}
    sync();
    return reboot(RB_AUTOBOOT);
}
