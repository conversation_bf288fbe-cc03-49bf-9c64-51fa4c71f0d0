#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <errno.h>
#include <fcntl.h>
#include <signal.h>
#include <stdarg.h>
#include <string.h>
#include <signal.h>
#include <ucontext.h>
#include <execinfo.h>
#include "pol/pol_threads.h"
#include <utilities/msgrouter.h>
#include "msgrouter_main.h"
#include <cmd.h>
#include "usbdservice.h"
/*#include "connect_mgr/connect_mgr.h"*/
#include "acl/acl.h"
#include "platform_api.h"
#include "job_manager.h"
#include "status_manager.h"
#include "log/logdebug.h"
#include "utilities/systime.h"
#include "printer_info.h"
#include "image.h"
#include "proxy_work.h"
#include "data_distribution.h"
#include "printer_resource.h"
#if CONFIG_PRINT
#include "print.h"
#endif

#if CONFIG_IPM
#include "image_processing_system.h"
#endif

#if CONFIG_SCAN
#include "scan.h"
#endif

#if CONFIG_COPY
#include "copy_system_interface.h"
#endif

#include "nvram.h"
#include "nvtables.h"

#if CONFIG_HTTC
#include "httc_audit.h"
#endif


#include "power_manager/power_manager.h"


#include "storage_manager/storage_mgr.h"


#if CONFIG_AUDIT_RECORD
#include "security_register.h"
#endif

#if CONFIG_USBHOST
#include "usbhservice.h"
#endif

#if CONFIG_UPGRADE
#include "upgrade/upgrade.h"
#endif

#if CONFIG_EVENT_LOG
#include "event_log.h"
#endif

#if CONFIG_SDK_EWS
#include "license_authentication/license_auth_api.h"
#endif

#include "panel_dc_main.h"
#include "network.h"

#include "hal.h"
#include "pol/pol_log.h"


#if CONFIG_SDK_PEDK
#include "pedk_mgr.h"
#include "../peripheral/beep.h"
#include "../pedk_setting/pedk_mfp_setting.h"
#endif
#include "hal_load.h"


extern const char *builddate;
extern const char *buildtime;

typedef struct SigInfo_
{
	int     signo;
	char    *signame;
}SigInfo;

//signal info type string define
static SigInfo s_sigs[] =
{
    {SIGHUP,    "SIGHUP"    },
    {SIGINT,    "SIGINT"    },
    {SIGQUIT,   "SIGQUIT"   },
    {SIGBUS,    "SIGBUS"    },
    {SIGFPE,    "SIGFPE"    },
    {SIGSEGV,   "SIGSEGV"   },
    {SIGALRM,   "SIGALRM"   },
    {SIGXCPU,   "SIGXCPU"   },
    {SIGXFSZ,   "SIGXFSZ"   },
    {SIGVTALRM, "SIGVTALRM" },
    {SIGPROF,   "SIGPROF"   },
    {SIGPWR,    "SIGPWR"    },
    {SIGSYS,    "SIGSYS"    },
    {SIGIO,     "SIGIO"     },
    {SIGABRT,   "SIGABRT"   },
    {SIGPIPE,   "SIGPIPE"   }
};

//signal handler to be called when abort
void SigHandler(int signum, siginfo_t *info, void *ucontext)
{
	void * bt[25];
	char **pInfo = NULL;
	int size;
	int i;
	int sigcount = sizeof(s_sigs)/sizeof(s_sigs[0]);
    char print_pid_name[128];
    CRASH_LOG_DATA_S crash_log_data = {0};

	for (i = 0; i < sigcount; i++)
	{
		if (s_sigs[i].signo == signum)
		{
			break;
		}
	}

    fprintf(stdout, "Signal Handle:\n");
    fprintf(stdout, "no: %d\n", signum);
    fprintf(stdout, "name: %s\n", (i >= sigcount) ? "unkown" : s_sigs[i].signame);
    fprintf(stdout, "errno: %d\n", info->si_errno);
    fprintf(stdout, "code: %d\n", info->si_code);
    fprintf(stdout, "sender pid: %d sender uid:%d\n", info->si_pid,info->si_uid);
    fprintf(stdout, "si_addr: %p\n", info->si_addr);
    fprintf(stdout, "si_fd: %d\n", info->si_fd);

	size = backtrace(bt, 25);
    fprintf(stdout, "Backtrace size :%d\n",size);
	pInfo = backtrace_symbols(bt, size);

    fprintf(stdout, "Backtrace:\n");
	if (NULL != pInfo)
	{
		for (i = 2; i < size; i++)
		{
            fprintf(stdout, "%d %s\n", i-2, pInfo[i]);
		}
    }
    if(info->si_pid!=0)
    {
        sprintf(print_pid_name,"ps -ef|grep '%d' |grep -v grep",info->si_pid);
        fprintf(stdout,"signal sender pid name is :");
        system(print_pid_name);
    }

    signal(SIGABRT, SIG_DFL);

    //以下代码为崩溃信息写入日志操作
    int size_valid = size - 2;

    //数据打包
    snprintf(crash_log_data.sig_handle,sizeof(crash_log_data.sig_handle),"Signal Handle:");
    crash_log_data.sig_num = signum;
    snprintf(crash_log_data.sig_name,sizeof(crash_log_data.sig_name),((i >= sigcount) ? "unkown" : s_sigs[i].signame));
    crash_log_data.si_error = info->si_errno;
    crash_log_data.si_code = info->si_code;
    crash_log_data.si_pid_t = info->si_pid;
    crash_log_data.si_uid_t = info->si_uid;
    crash_log_data.si_addr_t = info->si_addr;
    crash_log_data.si_fd_t = info->si_fd;
    crash_log_data.size = size;
    crash_log_data.pInfo = (char **)malloc(size_valid * sizeof(char *));
    if(crash_log_data.pInfo == NULL)
    {
        perror("malloc error\n");
    }

    //pInfo内容传入
    if (NULL != pInfo && NULL != crash_log_data.pInfo)
    {
        for (i = 2; i < size; i++)
        {
            crash_log_data.pInfo[i - 2] = strdup(pInfo[i]);
            //printf(".pInfo[%d] = %s\n",i-2,crash_log_data.pInfo[i - 2]);    //TODO
        }
    }

    //将上述信息传入，写入文件
    if(pi_crashlog_record_save_to_file(crash_log_data))
    {
        printf("crash_log_record_save_to_file failed\n");
    }

    //free动作
    if(crash_log_data.pInfo != NULL)
    {
        for (i = 0; i < size_valid; i++)
        {
            free(crash_log_data.pInfo[i]);
            printf("free [%d]\n",i);    //TODO
        }
        free(crash_log_data.pInfo);
    }
    else
        printf("PINFO is NULL!\n");     //TODO

    #if IN_MEMORY_DEBUG
    __real_free( pInfo );
    #else
	free(pInfo);
	#endif

    abort();
}

//initial signal catch type
void CatchSignal(void)
{
	int i = 0;
	struct sigaction action;

	action.sa_flags = SA_SIGINFO;
	action.sa_sigaction = SigHandler;

	for (i = 0; i < sizeof(s_sigs)/sizeof(s_sigs[0]); i++)
	{
		if (0 != sigaction(s_sigs[i].signo, &action, NULL))
		{
            fprintf(stderr, "sigaction error %d\n", s_sigs[i].signo);
		}
	}
}

int main(void)
{
    printf("hello world\n");
    printf("build time: %s %s\n", buildtime, builddate);
    //trace app run abnormally,such as null pointer,bad memory usage,invalid access...
    //once occurs,this will log some stack info to help engineers to analyse problems
    CatchSignal();
    pi_threads_prolog();

#if CONFIG_HTTC
    printf("CONFIG_HTTC IS OPEN\n");
    Httc_Socket_prolog();
#endif
    pi_imagemem_init();

    printf("---------------------msg_router-------init------------\n\n");
    msg_router_prolog(MID_NUMBERS);
    printf("---------------------cmd-------init------------\n\n");
    cmd_prolog("cmd");

    printf("---------------------nvram-------init------------\n\n");
    pi_nvram_init(get_nvtables_point() , get_nvtables_number());
    printf("---------------------log-------init------------\n\n");
    debug_prolog();
    printf("---------------------job_manager-------init------------\n\n");
    job_manager_prolog();

    printf("---------------------acl-------init------------\n\n");
    acl_parser_prolog();    
    printf("---------------------hal-------int------------\n\n");
    hal_prolog();
    printf("---------------------platform-------init------------\n\n");
    platform_prolog();
    printf("---------------------platform-------end------------\n\n");

#if CONFIG_EVENT_LOG
    printf("---------------------event log-------init------------\n\n");
    event_log_prolog();
    printf("---------------------event log-------end------------\n\n");
#endif

#if CONFIG_AUDIT_RECORD
    printf("---------------------audit record -------init------------\n\n");
    security_prolog();
    printf("---------------------audit record------- end------------\n\n");
#endif

#if CONFIG_PRINTERINFO
    printf("---------------------printer info-------init------------\n\n");
    printerst_prolog();
#endif
    proxy_work_prolog( data_distribution ,panel_data_init , 1);



#if CONFIG_SDK_PEDK
    printf("---------------------pedk_mgr_prolog-------int------------\n\n");
    pedk_mgr_prolog();
    beep_pedkapi_init();
    setting_pedk_init();
#endif

    systime_init();

    printf("---------------------panel dc-------int------------\n\n");
    panel_prolog();
    panel_notify_ready();


    status_manager_prolog();

#if CONFIG_USBDEVICE
    pi_usbd_server_prolog();    //init usbdevice device
#endif /* CONFIG_USBDEVICE */

#if CONFIG_USBHOST
    pi_usbh_server_prolog();    //init usbhost device
#endif /* CONFIG_USBHOST */

    printer_resource_func();


    printf("hello,CONFIG_STORAGE_MANAGER\n");
    pi_storage_mgr_prolog();


#if CONFIG_IPM
    printf("hello,CONFIG_IPM\n");
    pi_image_processing_system_prolog();
#endif

#if CONFIG_PRINT
    printf("hello,CONFIG_PRINT\n");
    print_func();
    image_prolog();
#endif

#if CONFIG_SCAN
    printf("hello,CONFIG_SCAN\n");
    scan_prolog();
#endif

#if CONFIG_COPY
    printf("hello,CONFIG_COPY\n");
    pi_copy_system_prolog();
#endif


#if CONFIG_SYSTEM
    printf("hello,CONFIG_SYSTEM\n");
    system_func();
#endif

    printf("---------------------network-------init------------\n\n");
    network_prolog();

    pi_powermanager_prolog();

#if CONFIG_SDK_EWS
    printf("---------------------license auth-------init------------\n\n");
    license_auth_prolog();
    printf("---------------------license auth-------end------------\n\n");
#endif

#if CONFIG_UPGRADE
    printf("---------------------upgrade_prolog-------init------------\n\n");
    upgrade_prolog();
#endif

    while(1)
    {
        sleep(10);
    }
    return 0;
}
