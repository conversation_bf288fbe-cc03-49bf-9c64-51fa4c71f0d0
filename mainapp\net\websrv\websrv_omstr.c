#include "nettypes.h"
#include "netmisc.h"
#include "netctx.h"
#include "netsts.h"
#include "ipv4.h"
#include "websrv_omstr.h"
#include "websrv_format.h"

#define WEBOM_PORT_BONJOUR      "5353"
#define WEBOM_PORT_WSD          "43001"

#define CJSON_WEBPAGE_SUPPORT_CONFIG_PATH    "webpage_support_config.json"


#if CONFIG_NET_UPGRADE_FIRMWARE
#define WEBOM_SUPPORT_UPGRADE   "1"
#else
#define WEBOM_SUPPORT_UPGRADE   "0"
#endif

#define WEBOM_SUPPORT_UPGRADE_OFFLINE   "1"
#define WEBOM_SUPPORT_UPGRADE_ONLINE    "0"

#define WEBOM_SUPPORT_PCL       "0"
#define WEBOM_SUPPORT_PS        "0"

#if CONFIG_AIRPRINT
#define WEBOM_SUPPORT_AIRPRINT  "1"
#else
#define WEBOM_SUPPORT_AIRPRINT  "0"
#endif

#if CONFIG_NET_SNTP
#define WEBOM_SUPPORT_SNTP      "1"
#else
#define WEBOM_SUPPORT_SNTP      "0"
#endif

#if CONFIG_NET_WSD
#define WEBOM_SUPPORT_WSD       "1"
#else
#define WEBOM_SUPPORT_WSD       "0"
#endif

#if CONFIG_NET_LPD
#define WEBOM_SUPPORT_LPD       "1"
#else
#define WEBOM_SUPPORT_LPD       "0"
#endif

#define WEBPAGE_SUPPORT_MAJOR_INIT(member, data)                \
{                                                               \
    support = 0;                                                \
    cjson_data = cJSON_GetObjectItem(root, #member);            \
    if ( cjson_data != NULL )                                   \
    {                                                           \
        support = 1;                                            \
    }                                                           \
    netdata_set_support_##member(net_ctx->data_mgr, support);   \
    NET_DEBUG(#member"(%u)", support);                          \
}

#define WEBPAGE_SUPPORT_SUB_INIT(member, member1, data)                         \
{                                                                               \
    support = 0;                                                                \
    cjson_data = cJSON_GetObjectItem(root, #member);                            \
    if ( cjson_data != NULL )                                                   \
    {                                                                           \
        cjson_data = cJSON_GetObjectItem(cjson_data, #member1);                 \
        if ( cjson_data != NULL )                                               \
        {                                                                       \
            support = cJSON_IsNumber(cjson_data) ? cjson_data->valueint : 1;    \
        }                                                                       \
    }                                                                           \
    netdata_set_support_##member1(net_ctx->data_mgr, support);                  \
    NET_DEBUG(#member1"(%u)", support);                                         \
}

#define WEBPAGE_SUPPORT_SUB1_INIT(member, member1, member2, data)           \
{                                                                           \
    support = 0;                                                            \
    cjson_data = cJSON_GetObjectItem(root, #member);                        \
    if ( cjson_data != NULL )                                               \
    {                                                                       \
        cjson_data = cJSON_GetObjectItem(cjson_data, #member1);             \
        if ( cjson_data != NULL )                                           \
        {                                                                   \
            cjson_data = cJSON_GetObjectItem(cjson_data, #member2);         \
            if ( cjson_data != NULL && cJSON_IsNumber(cjson_data) )         \
            {                                                               \
                support = cjson_data->valueint;                             \
            }                                                               \
        }                                                                   \
    }                                                                       \
    netdata_set_support_##member2(net_ctx->data_mgr, support);               \
    NET_DEBUG(#member2"(%u)", support);                                      \
}




typedef int32_t     (*WEBOM_GET_STRINT_ARRAY_FUNC)  (DATA_MGR_S *, uint32_t, char *, size_t);
typedef int32_t     (*WEBOM_GET_STRINT_VALUE_FUNC)  (DATA_MGR_S *, char *, size_t);
typedef uint32_t    (*WEBOM_GET_UINT32_ARRAY_FUNC)  (DATA_MGR_S *, uint32_t);
typedef uint32_t    (*WEBOM_GET_UINT32_VALUE_FUNC)  (DATA_MGR_S *);
typedef int32_t     (*WEBOM_GET_INTERNAL_FUNC)      (WEBOM_CTX_S *, int32_t, char *, size_t);
typedef int32_t     (*WEBOM_SET_INTERNAL_FUNC)      (WEBOM_CTX_S *, int32_t, const char *);
typedef int32_t     (*WEBOM_FORMAT_CHECK_FUNC)      (const char *);

typedef enum omval_type
{
    OMTYPE_CONSTANT = 0,
    OMTYPE_INTERNAL,
    OMTYPE_UINT32,
    OMTYPE_STRING,
}
OMVAL_TYPE_E;

struct websrv_omstr_table
{
    const char*         omstr;
    OMVAL_TYPE_E        val_type;
    void*               set_func;
    void*               get_func;
    void*               verify_func;
    size_t              val_size;
    IFACE_ID_E          ifid;
};

struct websrv_omstr_group
{
    const char*         module_name;
    WEBOM_TABLE_S*      omstr_table;
    size_t              omstr_count;
};

/**
 * @brief       Get function for "omPrinterStatus".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t webom_get_printer_status(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    NETSTS_PACKET_S packet = { .count = 0 };
    int32_t         len = 0;
    size_t          i;

    netsts_take_packet(&packet);
    RETURN_VAL_IF(packet.count == 0, NET_WARN, 0);

    len += snprintf(omval, val_size, "0x%X", packet.array[0].status_id);
    for ( i = 1; i < packet.count; ++i )
    {
        if ( val_size <= (size_t)len )
        {
            NET_WARN("printer_status overlength(%d)", len);
            break;
        }
        len += snprintf(omval + len, val_size - len, ",0x%X", packet.array[i].status_id);
    }
    NET_DEBUG("printer_status(%s)", omval);

    return len;
}

#if CONFIG_NET_WIFI
/**
 * @brief       Get function for "wifiScanResult".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t webom_get_sta_scan_result(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    WIFI_SCAN_RESULT_S  scan_result = { .ap_count = 0 };
    cJSON*              json_root = NULL;
    char*               json_str = NULL;
    char                temp_str[8];
    int32_t             rlen = 0;

    RETURN_VAL_IF(index < 0 || index >= ARRAY_SIZE(scan_result.ap_info), NET_WARN, 0);

    netdata_get_ap_list(DATA_MGR_OF(webomctx), &scan_result, sizeof(scan_result));
    RETURN_VAL_IF(scan_result.ap_count <= index, NET_INFO, 0);

    RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, 0);

    cJSON_AddStringToObject(json_root, "ssid"           , scan_result.ap_info[index].ssid);
    cJSON_AddStringToObject(json_root, "bssid"          , scan_result.ap_info[index].bssid);
    cJSON_AddExtendToObject(json_root, "mode"           , "%u", scan_result.ap_info[index].mode);
    cJSON_AddExtendToObject(json_root, "channel"        , "%u", scan_result.ap_info[index].channel);
    cJSON_AddExtendToObject(json_root, "security"       , "%u", scan_result.ap_info[index].sec_mode);
    cJSON_AddExtendToObject(json_root, "signalStrength" , "%u", scan_result.ap_info[index].sig_lvl);
    json_str = cJSON_PrintUnformatted(json_root);
    if ( json_str != NULL )
    {
        rlen = snprintf(omval, val_size, "%s", json_str);
        if ( rlen >= (int32_t)val_size )
        {
            NET_WARN("json_str(%s) is too long !!!", json_str);
            rlen = 0;
        }
        pi_free(json_str);
    }
    else
    {
        NET_WARN("cJSON_PrintUnformatted failed!");
    }
    cJSON_Delete(json_root);

    return rlen;
}

/**
 * @brief       Get function for "wifiWpsTime".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> kaiyong
 * @date        2023-9-16
 */
static int32_t webom_get_wps_time(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    uint32_t running_time;

    running_time = (uint32_t)(pi_time(NULL) - netdata_get_wps_start_time(DATA_MGR_OF(webomctx)));
    NET_DEBUG("running_time(%u)", running_time);
    if ( running_time >= 120 )
    {
        running_time = 120;
    }

    return snprintf(omval, val_size, "%u", running_time);
}
#endif

/**
 * @brief       Get function for "omSystemDate".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_get_system_date(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    struct tm   ntm;
    size_t      len;

    systime_get(&ntm);
    len = strftime(omval, val_size, "%Y-%m-%d", &ntm);
    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return (int32_t)len;
}

/**
 * @brief       Get function for "omSystemTime".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_get_system_time(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    struct tm   ntm;
    size_t      len;

    systime_get(&ntm);
    len = strftime(omval, val_size, "%H:%M:%S", &ntm);
    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return (int32_t)len;
}

/**
 * @brief       Get function for "omMailgroupList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2024-01-24
 */
static int32_t webom_get_mail_grouplist(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    MAIL_GROUPLIST_S    grouplist;
    MAIL_GROUPINFO_S*   info;
    int32_t             len = 0;
    int32_t             ret;
    uint32_t            i;
    cJSON*              json_root = NULL;
    char*               json_str = NULL;
    char                temp_str[8];

    ret = netdata_get_mail_grouplist(DATA_MGR_OF(webomctx), &grouplist, sizeof(grouplist));
    NET_DEBUG("ret(%d) grouplist.num(%u)", ret, grouplist.num);


    for ( i = 0; i < grouplist.num; ++i )
    {
        info = &(grouplist.info[i]);
        RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, 0);
        cJSON_AddStringToObject(json_root, "name"           , info->name);
        cJSON_AddExtendToObject(json_root, "no"             , "%u", i+1);
        cJSON_AddExtendToObject(json_root, "idx"            , "%d", info->record_id);
        cJSON_AddExtendToObject(json_root, "record_id"      , "%d", info->record_id);
        json_str = cJSON_PrintUnformatted(json_root);
        if ( json_str != NULL )
        {
            len += snprintf(omval + len, val_size - len, "%s%s", ((i == 0) ? "" : ","), json_str);

            pi_free(json_str);
            json_str = NULL;
            cJSON_Delete(json_root);

            if ( val_size <= (size_t)len )
            {
                NET_WARN("grouplist overlength(%d)", len);
                break;
            }
        }
        else
        {
            cJSON_Delete(json_root);
            NET_WARN("cJSON_PrintUnformatted failed!");
            break;
        }
    }

    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return len;
}

/**
 * @brief       Get function for "omMailinfoList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2024-01-24
 */
static int32_t webom_get_mail_addrbook(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    MAIL_ADDRBOOK_S     addrbook;
    MAIL_ADDRINFO_S*    info;
    char                group_str[128];
    int32_t             group_len = 0;
    int32_t             len = 0;
    int32_t             ret;
    uint32_t            i, j;
    cJSON*              json_root = NULL;
    char*               json_str = NULL;
    char                temp_str[8];

    ret = netdata_get_mail_addrbook(DATA_MGR_OF(webomctx), &addrbook, sizeof(addrbook));
    NET_DEBUG("ret(%d) addrbook.num(%u)", ret, addrbook.num);

    for ( i = 0; i < addrbook.num; ++i )
    {
        group_len = 0;
        memset(group_str, 0x00, sizeof(group_str));
        info = &(addrbook.info[i]);
        for ( j = 0; j < ARRAY_SIZE(info->group_id); ++j )
        {
            if ( info->group_id[j] > 0 )
            {
                group_len += snprintf(group_str + group_len, sizeof(group_str) - group_len, "%d%s", info->group_id[j], ((group_len >= 0) ? "#" : ""));
                if ( sizeof(group_str) <= (size_t)group_len )
                {
                    NET_WARN("group string overlength(%d)", group_len);
                    break;
                }
            }
        }
        RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, 0);
        cJSON_AddStringToObject(json_root, "user"           , info->name);
        cJSON_AddStringToObject(json_root, "email"          , info->addr);
        cJSON_AddStringToObject(json_root, "group"          , group_str);
        cJSON_AddExtendToObject(json_root, "idx"            , "%d", info->record_id);
        cJSON_AddExtendToObject(json_root, "record_id"      , "%d", info->record_id);
        json_str = cJSON_PrintUnformatted(json_root);

        if ( json_str != NULL )
        {
            len += snprintf(omval + len, val_size - len, "%s%s", ((i == 0) ? "" : ","), json_str);

            pi_free(json_str);
            json_str = NULL;
            cJSON_Delete(json_root);

            if ( val_size <= (size_t)len )
            {
                NET_WARN("mail_addrbook overlength(%d)", len);
                break;
            }
        }
        else
        {
            cJSON_Delete(json_root);
            NET_WARN("cJSON_PrintUnformatted failed!");
            break;
        }
    }
    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return len;
}

/**
 * @brief       Get function for "omMailinfoList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_get_ftp_addrbook(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    FTP_ADDRBOOK_S      addrbook;
    FTP_ADDRINFO_S*     info;
    int32_t             len = 0;
    int32_t             ret;
    uint32_t            i;
    cJSON*              json_root = NULL;
    char*               json_str = NULL;
    char                temp_str[8];

    ret = netdata_get_ftp_addrbook(DATA_MGR_OF(webomctx), &addrbook, sizeof(addrbook));
    NET_DEBUG("ret(%d) addrbook.num(%u)", ret, addrbook.num);

    for ( i = 0; i < addrbook.num; ++i )
    {
        info = &(addrbook.info[i]);
        RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, 0);
        cJSON_AddStringToObject(json_root, "addr"           , info->server_addr);
        cJSON_AddStringToObject(json_root, "path"           , info->server_path);
        cJSON_AddExtendToObject(json_root, "port"           , "%u", info->server_port);
        cJSON_AddExtendToObject(json_root, "anony"          , "%d", info->anonymity);
        cJSON_AddStringToObject(json_root, "user"           , info->login_name);
        cJSON_AddStringToObject(json_root, "pswd"           , info->login_pswd);
        cJSON_AddStringToObject(json_root, "name"           , info->server_name);
        cJSON_AddExtendToObject(json_root, "idx"            , "%d", info->record_id);
        cJSON_AddExtendToObject(json_root, "record_id"      , "%d", info->record_id);
        json_str = cJSON_PrintUnformatted(json_root);

        if ( json_str != NULL )
        {
            len += snprintf(omval + len, val_size - len, "%s%s", ((i == 0) ? "" : ","), json_str);

            pi_free(json_str);
            json_str = NULL;
            cJSON_Delete(json_root);

            if ( val_size <= (size_t)len )
            {
                NET_WARN("ftp_addrbook overlength(%d)", len);
                break;
            }
        }
        else
        {
            cJSON_Delete(json_root);
            NET_WARN("cJSON_PrintUnformatted failed!");
            break;
        }
    }
    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return len;
}

/**
 * @brief       Get function for "omSmbinfoList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_get_smb_addrbook(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    SMB_ADDRBOOK_S  addrbook;
    SMB_ADDRINFO_S* info;
    int32_t         len = 0;
    int32_t         ret;
    uint32_t        i;
    cJSON*          json_root = NULL;
    char*           json_str = NULL;
    char                temp_str[8];

    ret = netdata_get_smb_addrbook(DATA_MGR_OF(webomctx), &addrbook, sizeof(addrbook));
    NET_DEBUG("ret(%d) addrbook.num(%u)", ret, addrbook.num);

    for ( i = 0; i < addrbook.num; ++i )
    {
        info = &(addrbook.info[i]);
        RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, 0);
        cJSON_AddStringToObject(json_root, "addr"           , info->server_addr);
        cJSON_AddStringToObject(json_root, "path"           , info->server_path);
        cJSON_AddExtendToObject(json_root, "port"           , "%u", info->server_port);
        cJSON_AddExtendToObject(json_root, "anony"          , "%d", info->anonymity);
        cJSON_AddStringToObject(json_root, "user"           , info->login_name);
        cJSON_AddStringToObject(json_root, "pswd"           , info->login_pswd);
        cJSON_AddStringToObject(json_root, "name"           , info->server_name);
        cJSON_AddExtendToObject(json_root, "idx"            , "%d", info->record_id);
        cJSON_AddExtendToObject(json_root, "record_id"      , "%d", info->record_id);
        json_str = cJSON_PrintUnformatted(json_root);

        if ( json_str != NULL )
        {
            len += snprintf(omval + len, val_size - len, "%s%s", ((i == 0) ? "" : ","), json_str);

            pi_free(json_str);
            json_str = NULL;
            cJSON_Delete(json_root);

            if ( val_size <= (size_t)len )
            {
                NET_WARN("smb_addrbook overlength(%d)", len);
                break;
            }
        }
        else
        {
            cJSON_Delete(json_root);
            NET_WARN("cJSON_PrintUnformatted failed!");
            break;
        }
    }
    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return len;
}

/**
 * @brief       update function for "omAirprintUserList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @return      NULL
 * <AUTHOR> kaiyong
 * @date        2024-12-17
 */
static void webom_update_airprint_userlist(WEBOM_CTX_S* webomctx)
{
    int32_t               i, empty_index = 0;
    AIRPRINT_USERLIST_S   userlist, swap_userlist;

    netdata_get_airprint_userlist(DATA_MGR_OF(webomctx), &userlist, sizeof(AIRPRINT_USERLIST_S));
    memset(&swap_userlist, 0x00, sizeof(AIRPRINT_USERLIST_S));

    for ( i = 0; i < MAX_AIRPRINT_USERINFO; i++ )
    {
        if ( strlen(userlist.userinfo[i]) > 0 )
        {
            strcpy(swap_userlist.userinfo[empty_index], userlist.userinfo[i]);
            empty_index++;
        }
    }

    netdata_set_airprint_userlist(DATA_MGR_OF(webomctx), &swap_userlist, sizeof(AIRPRINT_USERLIST_S));
}

/**
 * @brief       Get function for "omAirprintUserList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[out]  omval   : The value of this om string.
 * @param[in]   val_size: The value size.
 * @return      The value length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> huanbin
 * @date        2024-10-11
 */
static int32_t webom_get_airprint_userlist(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    AIRPRINT_USERLIST_S   userlist;
    int32_t               len = 0;

    NET_DEBUG("get index(%d)", index);
    RETURN_VAL_IF(index > MAX_AIRPRINT_USERINFO || index < 0, NET_WARN, 0);

    if(index == 0) //update airprint userlist
        webom_update_airprint_userlist(webomctx);

    netdata_get_airprint_userlist(DATA_MGR_OF(webomctx), &userlist, sizeof(userlist));
    NET_DEBUG("userlist.userinfo[%d](%s)", index, userlist.userinfo[index]);
    len = snprintf(omval, val_size, "%s", userlist.userinfo[index]);

    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return len;
}

/**
 * @brief       Set function for "omHostName".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t webom_set_hostname(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_hostname(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omUserDHCP".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_usedhcp(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_ETH], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_ANY);
    webomctx->ipv4_conf[IFACE_ID_ETH]->dhcp_enabled = (uint8_t)(!!atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omIPv4Address".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_addr(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_ETH], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_ETH);
    snprintf(webomctx->ipv4_conf[IFACE_ID_ETH]->address, sizeof(webomctx->ipv4_conf[IFACE_ID_ETH]->address), "%s", omval);
    if ( webomctx->ipv4_conf[IFACE_ID_ETH]->dhcp_enabled == 0 && net_ipv4_check_conflict(IFACE_ID_ETH, webomctx->ipv4_conf[IFACE_ID_ETH]->address) )
    {
        NET_INFO("IPv4 address(%s) conflict on %s !!!", webomctx->ipv4_conf[IFACE_ID_ETH]->address, IFACE_ETH);
        return WEBSRV_SET_IPV4_INUSED;
    }
    webomctx->ipv4_conf[IFACE_ID_ETH]->changed |= IPV4_CONF_ADDRESS;
    return 0;
}

/**
 * @brief       Set function for "omIPv4SubnetMask".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_mask(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_ETH], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_ETH);
    snprintf(webomctx->ipv4_conf[IFACE_ID_ETH]->mask, sizeof(webomctx->ipv4_conf[IFACE_ID_ETH]->mask), "%s", omval);
    webomctx->ipv4_conf[IFACE_ID_ETH]->changed |= IPV4_CONF_MASK;
    return 0;
}

/**
 * @brief       Set function for "omIPv4GatewayAddress".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_gtwy(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_ETH], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_ETH);
    snprintf(webomctx->ipv4_conf[IFACE_ID_ETH]->gateway, sizeof(webomctx->ipv4_conf[IFACE_ID_ETH]->gateway), "%s", omval);
    webomctx->ipv4_conf[IFACE_ID_ETH]->changed |= IPV4_CONF_GATEWAY;
    return 0;
}

/**
 * @brief       Set function for "omDomainName".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_domain(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_domain(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omIPv4DNSDHCP".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_autodns(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->dnsv4_conf[IFACE_ID_ETH], NET_DNSV4_CONF_S, autodns, netdata_get_ipv4_autodns, IFACE_ID_ANY);
    webomctx->dnsv4_conf[IFACE_ID_ETH]->autodns = (uint8_t)(!!atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omIPv4MainDNS".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_dns0(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->dnsv4_conf[IFACE_ID_ETH], NET_DNSV4_CONF_S, autodns, netdata_get_ipv4_autodns, IFACE_ID_ETH);
    snprintf(webomctx->dnsv4_conf[IFACE_ID_ETH]->dns0, sizeof(webomctx->dnsv4_conf[IFACE_ID_ETH]->dns0), "%s", omval);
    webomctx->dnsv4_conf[IFACE_ID_ETH]->changed |= IPV4DNS_CONF_DNS0;
    return 0;
}

/**
 * @brief       Set function for "omIPv4OtherDNS".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv4_dns1(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->dnsv4_conf[IFACE_ID_ETH], NET_DNSV4_CONF_S, autodns, netdata_get_ipv4_autodns, IFACE_ID_ETH);
    snprintf(webomctx->dnsv4_conf[IFACE_ID_ETH]->dns1, sizeof(webomctx->dnsv4_conf[IFACE_ID_ETH]->dns1), "%s", omval);
    webomctx->dnsv4_conf[IFACE_ID_ETH]->changed |= IPV4DNS_CONF_DNS1;
    return 0;
}

/**
 * @brief       Set function for "omEnableIPv6".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv6_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv6_conf[IFACE_ID_ETH], NET_IPV6_CONF_S, dhcp_enabled, netdata_get_ipv6_usedhcp, IFACE_ID_ETH);
    webomctx->ipv6_conf[IFACE_ID_ETH]->enabled = (uint8_t)(!!atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omUseDHCPv6".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_eth_ipv6_usedhcp(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv6_conf[IFACE_ID_ETH], NET_IPV6_CONF_S, enabled, netdata_get_ipv6_switch, IFACE_ID_ETH);
    webomctx->ipv6_conf[IFACE_ID_ETH]->dhcp_enabled = (uint8_t)(!!atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnable9100PRT".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_rawprint_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_rawprint_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableLPRPRT".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_lpd_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_lpd_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableLPRPRT".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_rawprint_port(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_rawprint_port(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableSnmp".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableSnmpv1v2".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v1v2c_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v1v2c_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableSnmpv3".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v3_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v3_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSnmpComv1".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v1_community(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v1_community(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSnmpComv2c".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v2_community(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v2_community(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSnmpComv3".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v3_community(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v3_community(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSnmpV3user".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v3_user_name(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v3_user_name(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSnmpV3auth".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v3_auth_pswd(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v3_auth_pswd(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSnmpV3priv".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_snmp_v3_priv_pswd(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_snmp_v3_priv_pswd(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSnmpV3priv".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_slp_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_slp_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSmbNtlmv1Enable".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smb_ntlmv1_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_smb_ntlmv1_switch(DATA_MGR_OF(webomctx), (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSmbNtlmv1Enable".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smb_ntlm_auto(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_smb_ntlm_auto(DATA_MGR_OF(webomctx), (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableWSD".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_wsd_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_wsd_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPSenderAddress".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_sender_addr(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_sender_addr(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPAddress".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_server_addr(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_server_addr(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPPort".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_server_port(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_server_port(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPServerAuthentication".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_server_auth(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_server_auth(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPSecurity".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_sec_mode(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_sec_mode(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPUserName".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_username(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_username(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPUserPassword".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_password(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_smtp_password(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPTest".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_smtp_test_start(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_push_netport_subject(webomctx->net_ctx, PORT_UPDATE_SMTP);
    return 0;
}

/**
 * @brief       Set function for "omEnableBonjour".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_bonjour_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_bonjour_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omBonjourName".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_bonjour_server(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_bonjour_server(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "omPrinterLongitude".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_printer_longitude(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_printer_longitude(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omPrinterLatitude".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_printer_latitude(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_printer_latitude(DATA_MGR_OF(webomctx), omval);
    return 0;
}

#if CONFIG_NET_WIFI
/**
 * @brief       Set function for "wifiScanStatus".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-02-28
 */
static int32_t webom_start_scan_ssid(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_ap_num(DATA_MGR_OF(webomctx), 0xff);
    netctx_scan_wifi_ssid(webomctx->net_ctx, 1);
    return 0;
}

/**
 * @brief       Set function for "wifiStaEnabled".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-02-28
 */
static int32_t webom_set_sta_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    uint32_t on = (uint32_t)(!!atoi(omval));

    netctx_update_iface_switch(webomctx->net_ctx, IFACE_ID_STA, on);
    if ( on )
    {
        CONSTRUCT_STACONN_CONFIG(webomctx->wifi_conf);
    }
    return 0;
}

/**
 * @brief       Set function for "wifiStaSecMode".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-02-28
 */
static int32_t webom_set_sta_sec_mode(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_STACONN_CONFIG(webomctx->wifi_conf);
    webomctx->wifi_conf->sec_mode = (uint16_t)atoi(omval);
    return 0;
}

/**
 * @brief       Set function for "wifiStaSSID".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-02-28
 */
static int32_t webom_set_sta_ssid(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_STACONN_CONFIG(webomctx->wifi_conf);
    snprintf(webomctx->wifi_conf->ssid, sizeof(webomctx->wifi_conf->ssid), "%s", omval);
    return 0;
}

/**
 * @brief       Set function for "wifiStaWPAPassword".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-02-28
 */
static int32_t webom_set_sta_psk(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    CONSTRUCT_STACONN_CONFIG(webomctx->wifi_conf);
    snprintf(webomctx->wifi_conf->psk, sizeof(webomctx->wifi_conf->psk), "%s", omval);
    return 0;
}

/**
 * @brief       Set function for "wifiStaStatus".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-02-28
 */
static int32_t webom_set_sta_status(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    uint32_t    detail = netdata_get_sta_detail(DATA_MGR_OF(webomctx));
    int32_t     result = 0;

    switch ( detail )
    {
        case WIFI_DISCONNECTED_DETAIL_WPS_CANCEL:
            result = WEBSRV_WPS_CONNECTION_FAIL;
            break;
        case WIFI_CONNECTED_DETAIL_WPS_SUCCESS:
            result = WEBSRV_WPS_CONNECTION_OK;
            break;
        case WIFI_CONNECTING_DETAIL_WPS_PBC:
        case WIFI_CONNECTING_DETAIL_WPS_PIN:
            result = WEBSRV_WPS_CONNECTING;
            break;
        default:
            NET_DEBUG("detail(%u)", detail);
            break;
    }
    return result;
}

/**
 * @brief       Set function for "wifiStaIpEnable".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_sta_ipv4_usedhcp(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    RETURN_VAL_IF(STRING_IS_EMPTY(IFACE_NAME(IFACE_ID_STA)), NET_WARN, -1);

    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_STA], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_ANY);
    webomctx->ipv4_conf[IFACE_ID_STA]->dhcp_enabled = (uint8_t)(!!atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omIPv4Address".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_sta_ipv4_addr(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    RETURN_VAL_IF(STRING_IS_EMPTY(IFACE_NAME(IFACE_ID_STA)), NET_WARN, -1);

    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_STA], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_STA);
    if ( webomctx->ipv4_conf[IFACE_ID_STA]->dhcp_enabled == 0 && net_ipv4_check_conflict(IFACE_ID_STA, webomctx->ipv4_conf[IFACE_ID_STA]->address) )
    {
        NET_INFO("IPv4 address(%s) conflict on %s !!!", webomctx->ipv4_conf[IFACE_ID_STA]->address, IFACE_STA);
        return WEBSRV_SET_IPV4_INUSED;
    }

    snprintf(webomctx->ipv4_conf[IFACE_ID_STA]->address, sizeof(webomctx->ipv4_conf[IFACE_ID_STA]->address), "%s", omval);
    webomctx->ipv4_conf[IFACE_ID_STA]->changed |= IPV4_CONF_ADDRESS;
    return 0;
}

/**
 * @brief       Set function for "wifiIPv4SubnetMask".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_sta_ipv4_mask(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    RETURN_VAL_IF(STRING_IS_EMPTY(IFACE_NAME(IFACE_ID_STA)), NET_WARN, -1);

    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_STA], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_STA);
    snprintf(webomctx->ipv4_conf[IFACE_ID_STA]->mask, sizeof(webomctx->ipv4_conf[IFACE_ID_STA]->mask), "%s", omval);
    webomctx->ipv4_conf[IFACE_ID_STA]->changed |= IPV4_CONF_MASK;
    return 0;
}

/**
 * @brief       Set function for "wifiIPv4GatewayAddress".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_sta_ipv4_gtwy(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    RETURN_VAL_IF(STRING_IS_EMPTY(IFACE_NAME(IFACE_ID_STA)), NET_WARN, -1);

    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv4_conf[IFACE_ID_STA], NET_IPV4_CONF_S, dhcp_enabled, netdata_get_ipv4_usedhcp, IFACE_ID_STA);
    snprintf(webomctx->ipv4_conf[IFACE_ID_STA]->gateway, sizeof(webomctx->ipv4_conf[IFACE_ID_STA]->gateway), "%s", omval);
    webomctx->ipv4_conf[IFACE_ID_STA]->changed |= IPV4_CONF_GATEWAY;
    return 0;
}

/**
 * @brief       Set function for "wifiUseDHCPv6".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_sta_ipv6_usedhcp(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    RETURN_VAL_IF(STRING_IS_EMPTY(IFACE_NAME(IFACE_ID_STA)), NET_WARN, -1);

    CONSTRUCT_ADDRESS_CONFIG(webomctx->ipv6_conf[IFACE_ID_STA], NET_IPV6_CONF_S, enabled, netdata_get_ipv6_switch, IFACE_ID_STA);
    webomctx->ipv6_conf[IFACE_ID_STA]->dhcp_enabled = (uint8_t)(!!atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "wifiWpsSecMode".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_start_wps_request(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_request_wps_command(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "wifiWpsSecMode".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_wfd_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_iface_switch(webomctx->net_ctx, IFACE_ID_WFD, (uint32_t)(atoi(omval)));
    return 0;
}

/**
 * @brief       Set function for "wifiWfdUapSSID".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_wfd_ssid(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_wfd_ssid(webomctx->net_ctx, omval);
    return 0;
}

/**
 * @brief       Set function for "wifiWfdPassword".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-15
 */
static int32_t webom_set_wfd_psk(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_wfd_psk(webomctx->net_ctx, omval);
    return 0;
}
#endif

/**
 * @brief       Set function for "omConsumerPosition".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_location(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;

    if ( netdata_set_location(DATA_MGR_OF(webomctx), omval) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_PLATFORM_LOCATION_REQUEST, omval);
    }
    return 0;
}

/**
 * @brief       Set function for "omContactInfo".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_contacts(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;

    if ( netdata_set_contacts(DATA_MGR_OF(webomctx), omval) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_PLATFORM_CONTACT_INFO_REQUEST, omval);
    }
    return 0;
}

/**
 * @brief       Set function for "omPropertyNumber".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_property_number(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;

    if ( netdata_set_prop_num(DATA_MGR_OF(webomctx), omval) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_PLATFORM_PROPERTY_NUMBER_REQUEST, omval);
    }
    return 0;
}

/**
 * @brief       Set function for "omPropertyNumber".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sleep_time(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;
    uint32_t    val = (uint32_t)atoi(omval);

    if ( netdata_set_sleep_time(DATA_MGR_OF(webomctx), val) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_PANEL_SLEEP_TIME_REQUEST, val);
    }
    return 0;
}

/**
 * @brief       Set function for "omSleepMode".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2023-12-26
 */
static int32_t webom_set_sleep_mode(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;
    uint32_t    val = (uint32_t)atoi(omval);

    if ( netdata_set_sleep_mode(DATA_MGR_OF(webomctx), val) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_PANEL_SLEEP_MODE_REQUEST, val);
    }
    return 0;
}

/**
 * @brief       Set function for "omJobTimeOut".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_io_timeout(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;
    uint32_t    val = (uint32_t)atoi(omval);

    if ( netdata_set_io_timeout(DATA_MGR_OF(webomctx), val) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_PLATFORM_IO_TIMEOUT_REQUEST, val);
    }
    return 0;
}

/**
 * @brief       Set function for "omSystemDate".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_system_date(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    int32_t y, m, d, ret;

    ret = sscanf(omval, "%d-%d-%d", &y, &m, &d);
    if ( ret != 3 || y < 1970 || m < 1 || m > 12 || d < 1 || d > 31 )
    {
        NET_WARN("invalid date(%s)", omval);
        return -1;
    }

    CONSTRUCT_SYSTIME_CONFIG(webomctx->time_conf);
    webomctx->modfiy_data = 1;
    webomctx->time_conf->tm_year = (y);
    webomctx->time_conf->tm_mon  = (m);
    webomctx->time_conf->tm_mday = (d);
    return 0;
}


/**
 * @brief       Set function for "omSystemTime".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_system_time(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    int32_t h, m, s, ret;

    ret = sscanf(omval, "%d:%d:%d", &h, &m, &s);
    if ( ret != 3 || h < 0 || h > 23 || m < 0 || m > 59 || s < 0 || s > 60 )
    {
        NET_WARN("invalid time(%s)", omval);
        return -1;
    }

    CONSTRUCT_SYSTIME_CONFIG(webomctx->time_conf);
    if ( webomctx->modfiy_data == 0 )
    {
        webomctx->time_conf->tm_year += 1900;
        webomctx->time_conf->tm_mon += 1;
    }

    webomctx->time_conf->tm_hour = h;
    webomctx->time_conf->tm_min  = m;
    webomctx->time_conf->tm_sec  = s;
    return 0;
}

/**
 * @brief       Set function for "omSystemTimeZone".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_timezone(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;
    uint8_t     val = (uint8_t)atoi(omval);

    if ( netdata_set_timezone(DATA_MGR_OF(webomctx), (uint32_t)val) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_PLATFORM_TIMEZONE_REQUEST, val);
    }
    return 0;
}

static int32_t webom_check_mail_format(const char* mail, const char* name)
{
    int32_t ret = -1;
    if ( NULL != name )
    {
        ret = check_mail_usergroup_format(name);
        RETURN_VAL_IF(-1 == ret, NET_DEBUG, ret);
    }

    if ( NULL != mail )
    {
        ret = check_email_format(mail);
        RETURN_VAL_IF(-1 == ret, NET_DEBUG, ret);
    }

    return ret;
}

/**
 * @brief       Set function for "omMailgroupList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-24
 */
static int32_t webom_set_mail_grouplist(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    ADDRBOOK_OP_S   operate;
    char            mail_str[1024];
    char*           pstr = mail_str;
    int32_t         mail_id = 0;
    int32_t         ret = 0;
    int32_t         n = 0;
    int32_t         i = 0;
    int32_t         ret_code = 0;

    NET_DEBUG("index(%d) omval(%s)", index, omval);
    memset(&operate, 0, sizeof(operate));
    operate.type = AB_TYPE_MAIL_GROUP;

    if ( strcmp(omval, "255") == 0 )
    {
        operate.operation = AB_OP_DELETE;
        operate.delete_id[0] = index;
        operate.delete_id[1] = 0;
    }
    else
    {
        memset(mail_str, 0, sizeof(mail_str));
        if ( strstr(omval, "<omval#1><1>") == NULL )
        {
            ret = sscanf(omval, "<omval#0>%d<0><omval#1>%1023[^<]<1><omval#2>%15[^<]<2>", &n, mail_str, operate.u.group_info.name);
            RETURN_VAL_IF(ret != 3, NET_WARN, -1);
            RETURN_VAL_IF(-1 == webom_check_mail_format(NULL, operate.u.group_info.name), NET_NONE, -1);
            do
            {
                mail_id = (int32_t)strtol(pstr, &pstr, 10);
                if ( mail_id <= 0 )
                {
                    NET_WARN("mail_str(%s) format exception", mail_str);
                    break;
                }
                operate.u.group_info.mail_id[i++] = mail_id;

                if ( STRING_NO_EMPTY(pstr) )
                {
                    pstr++;
                }
            }
            while ( STRING_NO_EMPTY(pstr) && i < ARRAY_SIZE(operate.u.group_info.mail_id) );
        }
        else
        {
            ret = sscanf(omval, "<omval#0>%d<0><omval#1><1><omval#2>%15[^<]<2>", &n, operate.u.group_info.name);
            RETURN_VAL_IF(ret != 2, NET_WARN, -1);
            RETURN_VAL_IF(-1 == webom_check_mail_format(NULL, operate.u.group_info.name), NET_NONE, -1);
        }
        NET_DEBUG("name(%s)", operate.u.group_info.name);

        if ( index < 0 ) ///< Edit
        {
            operate.u.group_info.record_id = abs(index);
            operate.operation = AB_OP_UPDATE;
        }
        else ///< Add
        {
            operate.operation = AB_OP_ADD;
        }
    }
    ret_code = netctx_update_mail_grouplist(webomctx->net_ctx, &operate);
    if ( ret_code == -2)
    {
        return WEBSRV_ADDRESS_REPEAT;
    }
    else
    {
        return ret_code;
    }
}

/**
 * @brief       Set function for "omMailinfoList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-24
 */
static int32_t webom_set_mail_addrbook(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    ADDRBOOK_OP_S   operate;
    char            group_str[256];
    char*           pstr = group_str;
    int32_t         group_id = 0;
    int32_t         ret = 0;
    int32_t         n = 0;
    int32_t         i = 0;
    int32_t         ret_code;

    NET_DEBUG("index(%d) omval(%s)", index, omval);
    memset(&operate, 0, sizeof(operate));
    operate.type = AB_TYPE_MAIL;

    if ( strcmp(omval, "255") == 0 )
    {
        operate.operation = AB_OP_DELETE;
        operate.delete_id[0] = index;
        operate.delete_id[1] = 0;
    }
    else
    {
        memset(group_str, 0, sizeof(group_str));
        if ( strstr(omval, "<omval#1><1>") == NULL )
        {
            ret = sscanf(omval, "<omval#0>%d<0><omval#1>%127[^<]<1><omval#2>%63[^<]<2><omval#3>%255[^<]<3>", &n, group_str, operate.u.mail_info.name, operate.u.mail_info.addr);
            RETURN_VAL_IF(ret != 4, NET_WARN, -1);
            RETURN_VAL_IF(-1 == webom_check_mail_format(operate.u.mail_info.addr, operate.u.group_info.name), NET_NONE, -1);

            do
            {
                group_id = (int32_t)strtol(pstr, &pstr, 10);
                if ( group_id <= 0 )
                {
                    NET_WARN("group_str(%s) format exception", group_str);
                    break;
                }
                operate.u.mail_info.group_id[i++] = group_id;

                if ( STRING_NO_EMPTY(pstr) )
                {
                    pstr++;
                }
            }
            while ( STRING_NO_EMPTY(pstr) && i < ARRAY_SIZE(operate.u.mail_info.group_id) );
        }
        else
        {
            ret = sscanf(omval, "<omval#0>%d<0><omval#1><1><omval#2>%63[^<]<2><omval#3>%255[^<]<3>", &n, operate.u.mail_info.name, operate.u.mail_info.addr);
            RETURN_VAL_IF(ret != 3, NET_WARN, -1);
            RETURN_VAL_IF(-1 == webom_check_mail_format(operate.u.mail_info.addr, operate.u.group_info.name), NET_NONE, -1);
        }
        NET_DEBUG("name(%s)", operate.u.mail_info.name);
        NET_DEBUG("addr(%s)", operate.u.mail_info.addr);

        if ( index < 0 ) ///< Edit
        {
            operate.u.mail_info.record_id = abs(index);
            operate.operation = AB_OP_UPDATE;
        }
        else ///< Add
        {
            operate.operation = AB_OP_ADD;
        }
    }
    ret_code = netctx_update_mail_addrbook(webomctx->net_ctx, &operate);
    if ( ret_code == -2)
    {
        return WEBSRV_ADDRESS_REPEAT;
    }
    else
    {
        return ret_code;
    }
}

static int32_t webom_check_ftp_info_format(FTP_ADDRINFO_S* ftp_info)
{
    do
    {
        BREAK_IF(0 != check_ftp_server_name_format(ftp_info->server_name), NET_DEBUG);
        BREAK_IF(0 != check_ftp_server_addr_format(ftp_info->server_addr), NET_DEBUG);
        BREAK_IF(0 != check_ftp_path_format(ftp_info->server_path), NET_DEBUG);
        BREAK_IF((1 > ftp_info->server_port) || (65535 < ftp_info->server_port), NET_DEBUG);
        BREAK_IF(0 != check_ftp_username_format(ftp_info->login_name), NET_DEBUG);
        BREAK_IF(0 != check_ftp_password_format(ftp_info->login_pswd), NET_DEBUG);

        return 0;
    }while(0);

    return -1;
}

/**
 * @brief       Set function for "omFtpinfoList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-24
 */
static int32_t webom_set_ftp_addrbook(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    ADDRBOOK_OP_S   operate;
    int32_t         ret = 0;
    int32_t         n = 0;
    int32_t         ret_code;

    NET_DEBUG("index(%d) omval(%s)", index, omval);
    memset(&operate, 0, sizeof(operate));
    operate.type = AB_TYPE_FTP;

    if ( strcmp(omval, "255") == 0 )
    {
        operate.operation = AB_OP_DELETE;
        operate.delete_id[0] = index;
        operate.delete_id[1] = 0;
    }
    else
    {
        ret = sscanf(omval, "<omval#0>%d<0><omval#1>%128[^<]<1><omval#2>%31[^<]<2><omval#3>%63[^<]<3><omval#4>%hu<4><omval#5>%hu<5><omval#6>%63[^<]<6><omval#7>%31[^<]<7>",
                &n, operate.u.ftp_info.server_name, operate.u.ftp_info.server_addr, operate.u.ftp_info.server_path, &(operate.u.ftp_info.server_port),
                &(operate.u.ftp_info.anonymity), operate.u.ftp_info.login_name, operate.u.ftp_info.login_pswd);

        if( ret == 8 ||
			(ret == 6 && operate.u.ftp_info.anonymity == 1) ||
			(ret == 7 && operate.u.ftp_info.anonymity == 0 && STRING_NO_EMPTY(operate.u.ftp_info.login_name)) )
        {
            NET_DEBUG("server_name(%s)" , operate.u.ftp_info.server_name);
            NET_DEBUG("server_addr(%s)" , operate.u.ftp_info.server_addr);
            NET_DEBUG("server_path(%s)" , operate.u.ftp_info.server_path);
            NET_DEBUG("server_port(%u)" , operate.u.ftp_info.server_port);
            NET_DEBUG("anonymity(%u)"   , operate.u.ftp_info.anonymity);
            NET_DEBUG("login_name(%s)"  , operate.u.ftp_info.login_name);
            NET_DEBUG("login_pswd(%s)"  , operate.u.ftp_info.login_pswd);

            if ( -1 == webom_check_ftp_info_format(&operate.u.ftp_info) )
            {
                return -1;
            }

            if ( index < 0 ) ///< Edit
            {
                operate.u.ftp_info.record_id = abs(index);
                operate.operation = AB_OP_UPDATE;
            }
            else ///< Add
            {
                operate.operation = AB_OP_ADD;
            }
        }
        else
        {
            NET_WARN("invalid parameter (%s)", omval);
            return -1;
        }

    }
    ret_code = netctx_update_ftp_addrbook(webomctx->net_ctx, &operate);
    if ( ret_code == -2)
    {
        return WEBSRV_ADDRESS_REPEAT;
    }
    else
    {
        return ret_code;
    }
}

static int32_t webom_check_smb_info_format(SMB_ADDRINFO_S* smb_info)
{
    do
    {
        BREAK_IF(0 != check_smb_server_name_format(smb_info->server_name), NET_DEBUG);
        BREAK_IF(0 != check_smb_server_addr_format(smb_info->server_addr), NET_DEBUG);
        BREAK_IF(0 != check_smb_path_format(smb_info->server_path), NET_DEBUG);
        BREAK_IF((1 > smb_info->server_port) || (65535 < smb_info->server_port), NET_DEBUG);
        BREAK_IF(0 != check_smb_username_format(smb_info->login_name), NET_DEBUG);
        BREAK_IF(0 != check_smb_password_format(smb_info->login_pswd), NET_DEBUG);

        return 0;
    }while(0);

    return -1;
}

/**
 * @brief       Set function for "omSmbinfoList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2024-01-24
 */
static int32_t webom_set_smb_addrbook(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    ADDRBOOK_OP_S   operate;
    int32_t         ret = 0;
    int32_t         n = 0;
    int32_t         ret_code;
    char*           pswd_head_ptr = NULL;
    const char*     pswd_tail_ptr = NULL;
    char            pswd_part[32 + 1] = {0};

    NET_DEBUG("index(%d) omval(%s)", index, omval);
    memset(&operate, 0, sizeof(operate));
    operate.type = AB_TYPE_SMB;

    if ( strcmp(omval, "255") == 0 )
    {
        operate.operation = AB_OP_DELETE;
        operate.delete_id[0] = index;
        operate.delete_id[1] = 0;
    }
    else
    {
        ret = sscanf(omval, "<omval#0>%d<0><omval#1>%128[^<]<1><omval#2>%32[^<]<2><omval#3>%128[^<]<3><omval#4>%hu<4><omval#5>%hu<5><omval#6>%128[^<]<6>",
                &n, operate.u.smb_info.server_name, operate.u.smb_info.server_addr, operate.u.smb_info.server_path, &(operate.u.smb_info.server_port),
                &(operate.u.smb_info.anonymity), operate.u.smb_info.login_name);
        if ( ret == 7 || (ret == 6 && operate.u.smb_info.anonymity == 1))
        {
            if ( ret == 7 )
            {
                pswd_head_ptr = strstr(omval, "<omval#7>");
                pswd_tail_ptr = omval + strlen(omval) - 3;
                if ( pswd_head_ptr == NULL || memcmp(pswd_tail_ptr, "<7>", 3) != 0 )
                {
                    NET_WARN("pswd invalid parameter (%s)", omval);
                    return -1;
                }
                else
                {
                    pswd_head_ptr = pswd_head_ptr + strlen("<omval#7>");
                    memcpy(pswd_part, pswd_head_ptr, ((pswd_tail_ptr - pswd_head_ptr) >= 32 ? 32 : (pswd_tail_ptr - pswd_head_ptr)));
                    memset(&operate.u.smb_info.login_pswd, 0, sizeof(operate.u.smb_info.login_pswd));
                    snprintf(operate.u.smb_info.login_pswd, sizeof(operate.u.smb_info.login_pswd), "%s", pswd_part);
                }
            }
        }
        else
        {
            NET_WARN("invalid parameter (%s)", omval);
            return -1;
        }
        NET_DEBUG("server_name(%s)" , operate.u.smb_info.server_name);
        NET_DEBUG("server_addr(%s)" , operate.u.smb_info.server_addr);
        NET_DEBUG("server_path(%s)" , operate.u.smb_info.server_path);
        NET_DEBUG("server_port(%u)" , operate.u.smb_info.server_port);
        NET_DEBUG("anonymity(%u)"   , operate.u.smb_info.anonymity);
        NET_DEBUG("login_name(%s)"  , operate.u.smb_info.login_name);
        NET_DEBUG("login_pswd(%s)"  , operate.u.smb_info.login_pswd);
        NET_DEBUG("type(%d)"  , operate.type);
        if ( -1 == webom_check_smb_info_format(&operate.u.smb_info) )
        {
            return -1;
        }
        if ( index < 0 ) ///< Edit
        {
            operate.u.smb_info.record_id = abs(index);
            operate.operation = AB_OP_UPDATE;
        }
        else ///< Add
        {
            operate.operation = AB_OP_ADD;
        }
    }
    ret_code = netctx_update_smb_addrbook(webomctx->net_ctx, &operate);
    if ( ret_code == -2)
    {
        return WEBSRV_ADDRESS_REPEAT;
    }
    else
    {
        return ret_code;
    }
}

static int32_t webom_check_airprint_userlist_format(const char* omval)
{
    char   temp_str[64] = {0};
    cJSON* json_root = NULL;
    cJSON* json_temp = NULL;

    RETURN_IF(NULL == omval, NET_NONE);

    do
    {
        json_root = cJSON_Parse(omval);
        BREAK_IF(NULL == json_root, NET_DEBUG);

        CJSON_GET_VALUESTRING(json_root, "user", temp_str);
        BREAK_IF(NULL == json_temp, NET_DEBUG);
        BREAK_IF(-1 == check_airprint_user_format(temp_str), NET_DEBUG);

        memset(temp_str, 0, sizeof(temp_str));
        CJSON_GET_VALUESTRING(json_root, "pswd", temp_str);
        BREAK_IF(NULL == json_temp, NET_DEBUG);
        BREAK_IF(-1 == check_airprint_pswd_format(temp_str), NET_DEBUG);

        cJSON_Delete(json_root);
        return 0;

    }while(0);
    cJSON_Delete(json_root);

    return -1;
}

/**
 * @brief       Set function for "omAirprintUserList".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2024-10-11
 */
static int32_t webom_set_airprint_userlist(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    AIRPRINT_USERLIST_S   userlist;
    int32_t               i = 0;

    NET_DEBUG("index(%d) omval(%s)", index, omval);
    RETURN_VAL_IF(index > MAX_AIRPRINT_USERINFO || index < 0, NET_WARN, 0);

    netdata_get_airprint_userlist(DATA_MGR_OF(webomctx), &userlist, sizeof(userlist));
    NET_DEBUG("userlist.userinfo[%d](%s)", index, userlist.userinfo[index]);

    if ( strcmp(omval, "255") == 0 )
    {
        memset(userlist.userinfo[index], 0x00, sizeof(userlist.userinfo[index]));
    }
    else
    {
        if ( -1 == webom_check_airprint_userlist_format(omval) )
        {
            NET_DEBUG("airprint userlist(%s) format error", userlist.userinfo[index]);
            return -1;
        }
        snprintf(userlist.userinfo[index], sizeof(userlist.userinfo[index]), "%s", omval);
    }
    netdata_set_airprint_userlist(DATA_MGR_OF(webomctx), &userlist, sizeof(AIRPRINT_USERLIST_S));

    return 0;
}

/**
 * @brief       Set function for "omSMTPClientAddress1".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_client_addr1(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_client_addr(webomctx->net_ctx, 1, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPClientAddress2".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_client_addr2(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_client_addr(webomctx->net_ctx, 2, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPClientAddress3".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_client_addr3(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_client_addr(webomctx->net_ctx, 3, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPClientAddress4".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_client_addr4(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_client_addr(webomctx->net_ctx, 4, omval);
    return 0;
}

/**
 * @brief       Set function for "omSMTPEmailPaperEmpty".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_paper_empty(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_paper_empty(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPEmailPaperJam".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_paper_jam(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_paper_jam(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPEmailCartridgeEnd".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_toner_empty(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_toner_empty(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}


/**
 * @brief       Set function for "omSMTPEmailTonerLowWarning".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_toner_low(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_toner_low(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSMTPEmailWasteToner".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_alarm_waste_toner(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_alarm_waste_toner(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

static int32_t webom_check_water_mark_format(const char* str)
{
    const char* p = NULL;
    char title[128] = {0};
    RETURN_VAL_IF(NULL == str, NET_NONE, 0);
    RETURN_VAL_IF(128 < strlen(str), NET_DEBUG, -1);
#if !CONFIG_NET_SAFETY_MACHINE
    for ( int i = 0; i < strlen(str); i++)
    {
        if ( ':' == str[i] )
        {
            p = &str[i];
            break;
        }
    }
    RETURN_VAL_IF(NULL == p, NET_DEBUG, -1);

    strncpy(title, str, p - str);
    RETURN_VAL_IF(1 >= strlen(title), NET_NONE, 0);
#else
    strncpy(title, str, strlen(str));
#endif
    return check_water_mark_format(title);
}

/**
 * @brief       Set function for "omCustomWaterMark1".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_water_mark1(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    if ( -1 == webom_check_water_mark_format(omval) )
    {
        return -1;
    }
    netctx_update_water_mark(webomctx->net_ctx, 1, omval);
    return 0;
}

/**
 * @brief       Set function for "omCustomWaterMark2".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_water_mark2(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    if ( -1 == webom_check_water_mark_format(omval) )
    {
        return -1;
    }
    netctx_update_water_mark(webomctx->net_ctx, 2, omval);
    return 0;
}

/**
 * @brief       Set function for "omCustomWaterMark3".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_water_mark3(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    if ( -1 == webom_check_water_mark_format(omval) )
    {
        return -1;
    }
    netctx_update_water_mark(webomctx->net_ctx, 3, omval);
    return 0;
}

/**
 * @brief       Set function for "omCustomWaterMark4".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_water_mark4(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    if ( -1 == webom_check_water_mark_format(omval) )
    {
        return -1;
    }
    netctx_update_water_mark(webomctx->net_ctx, 4, omval);
    return 0;
}

/**
 * @brief       Set function for "omCustomWaterMark5".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_water_mark5(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    if ( -1 == webom_check_water_mark_format(omval) )
    {
        return -1;
    }
    netctx_update_water_mark(webomctx->net_ctx, 5, omval);
    return 0;
}

/**
 * @brief       Set function for "omCustomWaterMark6".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_water_mark6(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    if ( -1 == webom_check_water_mark_format(omval) )
    {
        return -1;
    }
    netctx_update_water_mark(webomctx->net_ctx, 6, omval);
    return 0;
}

/**
 * @brief       Set function for "omCopyAuthEnabled".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_copy_audit_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_copy_audit_switch(DATA_MGR_OF(webomctx), (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omAuthServerAddress".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_copy_audit_server(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_copy_audit_server(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omCopyColorControl".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> kaiyong
 * @date        2024-05-27
 */
static int32_t webom_set_copy_color_ctrl(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;

    uint32_t val= (uint32_t)atoi(omval);
    NET_DEBUG("copy_color_control(%u)", val);

    if( netdata_set_copy_color_ctrl(DATA_MGR_OF(webomctx), val) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_PANEL_COLOR_COPY_ENABLE_REQUEST, val);
    }
    return 0;
}

/**
 * @brief       Set function for "omCopyColorPassword".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> kaiyong
 * @date        2024-05-27
 */
static int32_t webom_set_copy_color_pswd(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;

    NET_DEBUG("copy_color_pswd(%s)", omval);
    if( netdata_set_copy_color_pswd(DATA_MGR_OF(webomctx), omval) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_REQUEST, omval);
    }
    return 0;
}

/**
 * @brief       Set function for "omSecEnabled".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sec_alert_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_sec_alert_switch(DATA_MGR_OF(webomctx), (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omSecUnit".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sec_alert_unit(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_sec_alert_unit(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omSecDepartment".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sec_alert_department(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_sec_alert_department(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omSecOwner".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sec_alert_owner(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_sec_alert_owner(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omSecCountryIPAddr".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sec_alert_server1(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_sec_alert_server1(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omSecNationIPAddr".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_sec_alert_server2(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_sec_alert_server2(DATA_MGR_OF(webomctx), omval);
    return 0;
}

/**
 * @brief       Set function for "omEnableUsbHID".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_usb_hid_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netdata_set_usb_hid_switch(DATA_MGR_OF(webomctx), (uint32_t)atoi(omval));
    return 0;
}

/**
 * @brief       Set function for "omEnableIPP".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> xin
 * @date        2024-12-12
 */
static int32_t webom_set_ipp_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_ipp_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}


#if CONFIG_NET_WHITELIST
/**
 * @brief       Set function for "omEnableWHITELIST".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
static int32_t webom_set_whitelist_switch(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    netctx_update_whitelist_switch(webomctx->net_ctx, (uint32_t)atoi(omval));
    return 0;
}

static int32_t webom_get_whitelist_addrbook(WEBOM_CTX_S* webomctx, int32_t index, char* omval, size_t val_size)
{
    WHITELIST_ADDRBOOK_S    addrbook;
    WHITELIST_ADDRINFO_S*   info;
    int32_t             len = 0;
    int32_t             ret;
    uint32_t            i;

    ret = netdata_get_whitelist_addrbook(DATA_MGR_OF(webomctx), &addrbook, sizeof(addrbook));
    NET_DEBUG("ret(%d) addrbook.num(%u)", ret, addrbook.num);

    for ( i = 0; i < addrbook.num; ++i )
    {
        info = &(addrbook.info[i]);
        len += snprintf(omval + len, val_size - len,
#if CONFIG_NET_SAFETY_MACHINE
                "%s{\"IPV4\":\"%s\", \"MAC\":\"%s\", \"record_id\":\"%d\"}%s",
#else
                "%s{\"ipv4\":\"%s\", \"mac\":\"%s\", \"record_id\":\"%d\"}%s",
#endif
                ((i == 0) ? "[" : ","), info->ip, info->mac, info->record_id, ((i == addrbook.num - 1) ? "]" : ""));
        if ( val_size <= (size_t)len )
        {
            NET_WARN("whitelist_addrbook overlength(%d)", len);
            break;
        }
    }
    NET_DEBUG("len(%d) omval(%s)", len, omval);

    return len;
}

static int32_t webom_set_whitelist_addrbook(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    ADDRBOOK_OP_S   operate;
    int32_t     ret = 0;
#if !CONFIG_NET_SAFETY_MACHINE
    int32_t     n = 0;
#endif
    cJSON *json, *item;
    WHITELIST_ADDRBOOK_S    addrbook;

    NET_DEBUG("index(%d) omval(%s)", index, omval);
    memset(&operate, 0, sizeof(operate));
    operate.type = AB_TYPE_WL;

    if ( strcmp(omval, "255") == 0 )
    {
        operate.operation = AB_OP_DELETE;
        operate.whitelist_delete_id[0] = index;
        operate.whitelist_delete_id[1] = 0;
    }
    else
    {
#if CONFIG_NET_SAFETY_MACHINE
        json = cJSON_Parse(omval);
        if ( json == NULL )
        {
            NET_ERROR("Json format error");
            return -1;
        }

        item = cJSON_GetObjectItem(json, "IPV4");
        NET_DEBUG("ip(%s)" , cJSON_GetStringValue(item));
        snprintf(operate.u.whitelist_info.ip, sizeof(operate.u.whitelist_info.ip), "%s", cJSON_GetStringValue(item));

        item = cJSON_GetObjectItem(json, "MAC");
        NET_DEBUG("mac(%s)" , cJSON_GetStringValue(item));
        snprintf(operate.u.whitelist_info.mac, sizeof(operate.u.whitelist_info.mac), "%s", cJSON_GetStringValue(item));

        item = cJSON_GetObjectItem(json, "record_id");
        if ( item != NULL )
        {
            operate.operation = AB_OP_UPDATE;
            NET_DEBUG("record_id(%s)" , cJSON_GetStringValue(item));
            operate.u.whitelist_info.record_id = strtol(cJSON_GetStringValue(item), NULL, 10);
        }
        else
        {
            operate.operation = AB_OP_ADD;
        }
        cJSON_Delete(json);
#else
        if ( strstr(omval, "<omval#1><1>") == NULL )
        {
            ret = sscanf(omval, "<omval#0>%d<0><omval#1>%15[^<]<1><omval#2>%17[^<]<2>",
                    &n, operate.u.whitelist_info.ip, operate.u.whitelist_info.mac);
            RETURN_VAL_IF(ret != 3, NET_WARN, -1);
        }
        else
        {
            ret = sscanf(omval, "<omval#0>%d<0><omval#1><1><omval#2>%17[^<]<2>",
                    &n, operate.u.whitelist_info.mac);
            RETURN_VAL_IF(ret != 2, NET_WARN, -1);
        }

        NET_DEBUG("ip(%s)" , operate.u.whitelist_info.ip);
        NET_DEBUG("mac(%s)" , operate.u.whitelist_info.mac);

        if ( index < 0 ) ///< Edit
        {
            operate.u.whitelist_info.record_id = abs(index);
            operate.operation = AB_OP_UPDATE;
        }
        else ///< Add
        {
            operate.operation = AB_OP_ADD;
        }
#endif
    }

    netctx_update_whitelist_addrbook(webomctx->net_ctx, &operate);

    return 0;
}
#endif

/**
 * @brief       Set function for "omErrDeal".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2025-06-05
 */
static int32_t webom_set_err_deal(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;
    uint32_t    val = (uint32_t)atoi(omval);  // 0->恢复继续     1->立即删除 2->延时删除
    if ( netdata_set_err_deal_mode(DATA_MGR_OF(webomctx), val) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_REQUEST, val);
    }
    return 0;
}

/**
 * @brief       Set function for "omErrDelayTime".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> Xin
 * @date        2025-06-05
 */
static int32_t webom_set_err_deal_time(WEBOM_CTX_S* webomctx, int32_t index, const char* omval)
{
    NET_CTX_S*  net_ctx = webomctx->net_ctx;
    uint32_t    val = (uint32_t)atoi(omval);

    if ( netdata_set_err_deal_time(DATA_MGR_OF(webomctx), val) == 0 )
    {
        uint32_t  mode = netdata_get_err_deal_mode(DATA_MGR_OF(webomctx));
        if ( mode == 2 )
        {
            NETEVT_NOTIFY_I(EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_REQUEST, val);
        }
    }
    return 0;
}

static WEBOM_TABLE_S    s_omstr_extern_table[] =
{
    {"omFirmVersion"              , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_fw_ver            , (void *)NULL    , 32   , IFACE_ID_ANY},///< 固件版本号
    {"omFirmName"                 , OMTYPE_CONSTANT , (void *)NULL                          , (void *)""                            , (void *)NULL    , 4    , IFACE_ID_ANY},///< 固件名称
    {"omProductID"                , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_pdt_id            , (void *)NULL    , 16   , IFACE_ID_ANY},///< Product id
#if CONFIG_NET_SAFETY_MACHINE
    {"omBoardID"                  , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_bord_id           , (void *)NULL    , 32   , IFACE_ID_ANY},///< 面板板卡序列号
    {"omFirmwareVersion"          , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_panl_fw_ver       , (void *)NULL    , 32   , IFACE_ID_ANY},///< 面板固件版本号
#endif
    {"wifiEthrEnumerated"         , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_wired     , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持有线网络
    {"wifiEnumerated"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_wifi      , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持无线网络
    {"scanEnumerated"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_scan      , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持扫描
    {"wifiStaStatus"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_sta_status        , (void *)NULL    , 16   , IFACE_ID_ANY},///< WiFi连接状态
    {"omSupportAirPrint"          , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_AIRPRINT        , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持AirPrint
    {"omSupportUpgrade"           , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_UPGRADE         , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持固件升级
    {"omSupportUpgradeOffline"    , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_UPGRADE_OFFLINE , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持固件离线升级
    {"omSupportUpgradeOnline"     , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_UPGRADE_ONLINE  , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持固件在线升级
    {"omSupportSNTP"              , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_SNTP            , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持SNTP
    {"omSupportWSD"               , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_WSD             , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持WSD
    {"omSupportLPD"               , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_LPD             , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持LPD
    {"omSupportPCL"               , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_PCL             , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持PCL选项
    {"omSupportPS"                , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_SUPPORT_PS              , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持PS选项
    {"omSupportExportLog"         , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_export_log_switch , (void *)NULL    , 4    , IFACE_ID_ANY},///< 是否支持日志下载
    {"omWebSupportMultilingual"   , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_language           , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持多语言界面
    {"omWebSupportWireless"       , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_wifi               , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持无线界面
    {"omWebSupportWsd"            , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_wsd                , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持wsd界面
    {"omWebSupportSlp"            , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_slp                , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持slp界面
    {"omWebSupportRaw"            , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_raw                , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持raw界面
    {"omWebSupportSmb"            , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_smb                , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持smb界面
    {"omWebSupportSntp"           , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_sntp               , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持sntp界面
    {"omWebSupportSleepMode"      , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_sleep_mode         , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持休眠界面
    {"omWebSupportEmailNotify"    , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_email_notification , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持邮件通知界面
    {"omWebSupportRemoteControl"  , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_remote_control     , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持远程控制界面
    {"omWebSupportEquip_Report"   , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_equipment_report   , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持设备报告界面
    {"omWebSupportSafety_Log"     , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_safety_log         , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持安全日志界面
    {"omWebSupportUpgrade"        , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_firmware_upgrade   , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持固件升级界面
    {"omWebSupportUpgradeOffline" , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_offline_upgrade    , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持离线升级界面
    {"omWebSupportUpgradeOnline"  , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_online_upgrade     , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持在线升级界面
    {"omWebSupportWaterMark"      , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_watermark          , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持复印水印界面

    {"omWebSupportWhitelist"      , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_whitelist       , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持白名单界面
    {"omWebSupportSnmp"           , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_snmp            , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持snmp界面
    {"omWebSupportSmtp"           , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_smtp            , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持smtp界面
    {"omWebSupportBonjour"        , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_bonjour         , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持bonjour界面
    {"omWebSupportAirPrint"       , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_airprint        , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持airprint界面
    {"omWebSupportMobilePrint"    , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_mobile_print    , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持mobile_print界面
    {"omWebSupportIppsrv"         , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_ippsrv          , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持ippsrv界面
    {"omWebSupportFtpAddressBook" , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_ftp_addressbook , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持ftp_addressbook界面
    {"omWebSupportSmbAddressBook" , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_smb_addressbook , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持smb_addressbook界面
    {"omWebSupportAddressBook"    , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_support_addressbook     , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持addressbook界面
    {"omWebSupportJobcontrol"   , OMTYPE_UINT32     , (void *)NULL                          , (void *)netdata_get_support_job_control     , (void *)NULL   , 4    , IFACE_ID_ANY},///< webpage是否支持job_control界面
};

static WEBOM_TABLE_S    s_omstr_info_table[] =
{
    {"omErrorFlag"                , OMTYPE_CONSTANT , (void *)NULL                          , (void *)""                             , (void *)NULL   , 16   , IFACE_ID_ANY},///< 打印机状态
    {"omDrumStatus"               , OMTYPE_CONSTANT , (void *)NULL                          , (void *)""                             , (void *)NULL   , 16   , IFACE_ID_ANY},///< 鼓组件状态
    {"omConsumerPosition"         , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_location           , (void *)NULL   , 256  , IFACE_ID_ANY},///< 位置
    {"omContactInfo"              , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_contacts           , (void *)NULL   , 128  , IFACE_ID_ANY},///< 联系人
    {"omProductName"              , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_pdt_name           , (void *)NULL   , 32   , IFACE_ID_ANY},///< 产品名称
    {"omSerialNumber"             , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_pdt_sn             , (void *)NULL   , 32   , IFACE_ID_ANY},///< 序列号
    {"omPrinterStatus"            , OMTYPE_INTERNAL , (void *)NULL                          , (void *)webom_get_printer_status       , (void *)NULL   , 1024 , IFACE_ID_ANY},///< 打印机状态
    {"omStatusModule"             , OMTYPE_CONSTANT , (void *)NULL                          , (void *)""                             , (void *)NULL   , 4    , IFACE_ID_ANY},///< 打印机状态
    {"omTonerAbandonStatus"       , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_wtb_status         , (void *)NULL   , 4    , IFACE_ID_ANY},///< 废粉瓶状态
    {"omTonerStatusC"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_status          , (void *)NULL   , 4    , MARKER_ID_C },///< 青色碳粉盒状态
    {"omTonerStatusM"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_status          , (void *)NULL   , 4    , MARKER_ID_M },///< 红色碳粉盒状态
    {"omTonerStatusY"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_status          , (void *)NULL   , 4    , MARKER_ID_Y },///< 黄色碳粉盒状态
    {"omTonerStatusK"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_status          , (void *)NULL   , 4    , MARKER_ID_K },///< 黑色碳粉盒状态
    {"omTonerRemainBlue"          , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_remain          , (void *)NULL   , 4    , MARKER_ID_C },///< 青色碳粉盒余量
    {"omTonerRemainRed"           , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_remain          , (void *)NULL   , 4    , MARKER_ID_M },///< 红色碳粉盒余量
    {"omTonerRemainYellow"        , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_remain          , (void *)NULL   , 4    , MARKER_ID_Y },///< 黄色碳粉盒余量
    {"omTonerRemain"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_tb_remain          , (void *)NULL   , 4    , MARKER_ID_K },///< 黑色碳粉盒余量
    {"omDrumStatusC"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_status          , (void *)NULL   , 4    , MARKER_ID_C },///< 青色鼓组件状态
    {"omDrumStatusM"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_status          , (void *)NULL   , 4    , MARKER_ID_M },///< 红色鼓组件状态
    {"omDrumStatusY"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_status          , (void *)NULL   , 4    , MARKER_ID_Y },///< 黄色鼓组件状态
    {"omDrumStatusK"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_status          , (void *)NULL   , 4    , MARKER_ID_K },///< 黑色鼓组件状态
#if CONFIG_NET_SAFETY_MACHINE
    {"omTonerRemainBlueRoll"      , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_C },///< 青色鼓组件余量
    {"omTonerRemainRedRoll"       , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_M },///< 红色鼓组件余量
    {"omTonerRemainYellowRoll"    , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_Y },///< 黄色鼓组件余量
    {"omTonerRemainRoll"          , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_K },///< 黑色鼓组件余量
#else
    {"omDrumRemainBlue"           , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_C },///< 青色鼓组件余量
    {"omDrumRemainRed"            , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_M },///< 红色鼓组件余量
    {"omDrumRemainYellow"         , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_Y },///< 黄色鼓组件余量
    {"omDrumRemainBlack"          , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dr_remain          , (void *)NULL   , 4    , MARKER_ID_K },///< 黑色鼓组件余量
#endif
    {"omTonerSerialNumberC"       , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_tb_serial          , (void *)NULL   , 32   , MARKER_ID_C },///< 青色碳粉盒序列号
    {"omTonerSerialNumberM"       , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_tb_serial          , (void *)NULL   , 32   , MARKER_ID_M },///< 红色碳粉盒序列号
    {"omTonerSerialNumberY"       , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_tb_serial          , (void *)NULL   , 32   , MARKER_ID_Y },///< 黄色碳粉盒序列号
    {"omTonerSerialNumberK"       , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_tb_serial          , (void *)NULL   , 32   , MARKER_ID_K },///< 黑色碳粉盒序列号
    {"omDeveloperRemainBlue"      , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dv_remain          , (void *)NULL   , 4    , MARKER_ID_C },///< 青色显影器余量
    {"omDeveloperRemainRed"       , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dv_remain          , (void *)NULL   , 4    , MARKER_ID_M },///< 红色显影器余量
    {"omDeveloperRemainYellow"    , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dv_remain          , (void *)NULL   , 4    , MARKER_ID_Y },///< 黄色显影器余量
    {"omDeveloperRemainBlack"     , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_dv_remain          , (void *)NULL   , 4    , MARKER_ID_K },///< 黑色显影器余量
    {"omTransferBeltRemain"       , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_xfer_belt_remain   , (void *)NULL   , 4    , IFACE_ID_ANY},///< 图像转印带单元余量
    {"omTransferRollerRemain"     , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_xfer_roller_remain , (void *)NULL   , 4    , IFACE_ID_ANY},///< 转印辊组件余量
    {"omFuserUnitRemain"          , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_fuser_remain       , (void *)NULL   , 4    , IFACE_ID_ANY},///< 定影器组件单元余量

};

static WEBOM_TABLE_S    s_omstr_manager_table[] =
{
#if CONFIG_NET_SAFETY_MACHINE
    {"omSecAdminUser"             , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEB_SEC_ADMIN_NAME      , (void *)NULL           , 32   , IFACE_ID_ANY},///< 安全用户登录名
    {"omAuditAdminUser"           , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEB_AUD_ADMIN_NAME      , (void *)NULL           , 32   , IFACE_ID_ANY},///< 审计用户登录名
    {"omAdminUser"                , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEB_SYS_ADMIN_NAME      , (void *)NULL           , 32   , IFACE_ID_ANY},///< 系统用户登录名
    {"omAdminID"                  , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_web_admin_id, (void *)NULL           , 4    , IFACE_ID_ANY},///< 系统用户登录名
#else
    {"omAdminUser"                , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_web_user   , (void *)NULL           , 32   , IFACE_ID_ANY},///< 用户登录名
#endif
};

static WEBOM_TABLE_S    s_omstr_ipv4_table[] =
{
    {"omHostName"                 , OMTYPE_STRING   , (void *)webom_set_hostname            , (void *)netdata_get_hostname     , (void *)check_hostname_format, 32   , IFACE_ID_ANY},///< 主机名
    {"omMACAddress"               , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_mac_addr     , (void *)NULL         , 32   , IFACE_ID_ETH},///< ETH MAC地址
    {"omUserDHCP"                 , OMTYPE_UINT32   , (void *)webom_set_eth_ipv4_usedhcp    , (void *)netdata_get_ipv4_usedhcp , (void *)check_switch_format, 4    , IFACE_ID_ETH},///< ETH DHCPv4开关
    {"omIPv4Address"              , OMTYPE_STRING   , (void *)webom_set_eth_ipv4_addr       , (void *)netdata_get_ipv4_addr    , (void *)check_ipv4_format, 16   , IFACE_ID_ETH},///< ETH IPv4地址
    {"omIPv4SubnetMask"           , OMTYPE_STRING   , (void *)webom_set_eth_ipv4_mask       , (void *)netdata_get_ipv4_mask    , (void *)check_ipv4_format, 16   , IFACE_ID_ETH},///< ETH IPv4子网掩码
    {"omIPv4GatewayAddress"       , OMTYPE_STRING   , (void *)webom_set_eth_ipv4_gtwy       , (void *)netdata_get_ipv4_gtwy    , (void *)check_ipv4_format, 16   , IFACE_ID_ETH},///< ETH IPv4网关地址
    {"omDomainName"               , OMTYPE_STRING   , (void *)webom_set_domain              , (void *)netdata_get_domain       , (void *)check_domain_format, 64   , IFACE_ID_ANY},///< 域名
    {"omIPv4DNSDHCP"              , OMTYPE_UINT32   , (void *)webom_set_eth_ipv4_autodns    , (void *)netdata_get_ipv4_autodns , (void *)check_switch_format, 4    , IFACE_ID_ETH},///< ETH AutoDNS开关
    {"omIPv4MainDNS"              , OMTYPE_STRING   , (void *)webom_set_eth_ipv4_dns0       , (void *)netdata_get_ipv4_dns0    , (void *)check_ipv4_format, 64   , IFACE_ID_ETH},///< ETH IPv4主DNS
    {"omIPv4OtherDNS"             , OMTYPE_STRING   , (void *)webom_set_eth_ipv4_dns1       , (void *)netdata_get_ipv4_dns1    , (void *)check_ipv4_format, 64   , IFACE_ID_ETH},///< ETH IPv4辅DNS
    {"omIPv6LocalAddress"         , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_link    , (void *)NULL         , 64   , IFACE_ID_ETH},///< ETH IPv6本地链路
    {"omIPv6GatewayAddress"       , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_stls    , (void *)NULL         , 64   , IFACE_ID_ETH},///< ETH IPv6无状态
    {"omIPv6Address"              , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_site    , (void *)NULL         , 64   , IFACE_ID_ETH},///< ETH IPv6有状态
    {"omIPv6MainDNS"              , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_dns0    , (void *)NULL         , 64   , IFACE_ID_ETH},///< ETH IPv6主DNS
    {"omIPv6OtherDNS"             , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_dns1    , (void *)NULL         , 64   , IFACE_ID_ETH},///< ETH IPv6辅DNS
};

static WEBOM_TABLE_S    s_omstr_ipv6_table[] =
{
    {"omEnableIPv6"               , OMTYPE_UINT32   , (void *)webom_set_eth_ipv6_switch     , (void *)netdata_get_ipv6_switch  , (void *)check_switch_format, 4    , IFACE_ID_ETH},///< IPv6开关
    {"omUseDHCPv6"                , OMTYPE_UINT32   , (void *)webom_set_eth_ipv6_usedhcp    , (void *)netdata_get_ipv6_usedhcp , (void *)check_switch_format, 4    , IFACE_ID_ETH},///< DHCPv6开关
};

static WEBOM_TABLE_S    s_omstr_rawlpd_table[] =
{
    {"omEnable9100PRT"            , OMTYPE_UINT32   , (void *)webom_set_rawprint_switch     , (void *)netdata_get_rawprint_switch , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< RAWPRINT开关
    {"omEnableLPRPRT"             , OMTYPE_UINT32   , (void *)webom_set_lpd_switch          , (void *)netdata_get_lpd_switch      , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< LPR/LPD开关
    {"omRawPort"                  , OMTYPE_UINT32   , (void *)webom_set_rawprint_port       , (void *)netdata_get_rawprint_port   , (void *)check_9100_format, 64   , IFACE_ID_ANY},///< RAWPRINT端口号
};

static WEBOM_TABLE_S    s_omstr_snmp_table[] =
{
    {"omEnableSnmp"               , OMTYPE_UINT32   , (void *)webom_set_snmp_switch         , (void *)netdata_get_snmp_switch       , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< SNMP开关
    {"omEnableSnmpv1v2"           , OMTYPE_UINT32   , (void *)webom_set_snmp_v1v2c_switch   , (void *)netdata_get_snmp_v1v2c_switch , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< SNMPv1v2c开关
    {"omEnableSnmpv3"             , OMTYPE_UINT32   , (void *)webom_set_snmp_v3_switch      , (void *)netdata_get_snmp_v3_switch    , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< SNMPv3开关
    {"omSnmpComv1"                , OMTYPE_STRING   , (void *)webom_set_snmp_v1_community   , (void *)netdata_get_snmp_v1_community , (void *)check_snmp_community_format, 64   , IFACE_ID_ANY},///< SNMPv1社区名
    {"omSnmpComv2c"               , OMTYPE_STRING   , (void *)webom_set_snmp_v2_community   , (void *)netdata_get_snmp_v2_community , (void *)check_snmp_community_format, 64   , IFACE_ID_ANY},///< SNMPv2c社区名
    {"omSnmpComv3"                , OMTYPE_STRING   , (void *)webom_set_snmp_v3_community   , (void *)netdata_get_snmp_v3_community , (void *)check_snmp_community_format, 64   , IFACE_ID_ANY},///< SNMPv3社区名
    {"omSnmpV3user"               , OMTYPE_STRING   , (void *)webom_set_snmp_v3_user_name   , (void *)netdata_get_snmp_v3_user_name , (void *)check_snmp_user_format, 64   , IFACE_ID_ANY},///< SNMPv3用户名
    {"omSnmpV3auth"               , OMTYPE_STRING   , (void *)webom_set_snmp_v3_auth_pswd   , (void *)netdata_get_snmp_v3_auth_pswd , (void *)check_snmp_pwsd_format, 36   , IFACE_ID_ANY},///< SNMPv3密钥
    {"omSnmpV3priv"               , OMTYPE_STRING   , (void *)webom_set_snmp_v3_priv_pswd   , (void *)netdata_get_snmp_v3_priv_pswd , (void *)check_snmp_pwsd_format, 36   , IFACE_ID_ANY},///< SNMPv3私钥
};

static WEBOM_TABLE_S    s_omstr_slp_table[] =
{
    {"omEnableSLP"                , OMTYPE_UINT32   , (void *)webom_set_slp_switch          , (void *)netdata_get_slp_switch        , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< SLP开关
};

static WEBOM_TABLE_S    s_omstr_smb_table[] =
{
    {"omSmbNtlmv1Enable"          , OMTYPE_UINT32   , (void *)webom_set_smb_ntlmv1_switch   , (void *)netdata_get_smb_ntlmv1_switch , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< NTLMv1认证开关
    {"omSmbAutoNtlmvEnable"       , OMTYPE_UINT32   , (void *)webom_set_smb_ntlm_auto       , (void *)netdata_get_smb_ntlm_auto     , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< NTLM自动认证
};

static WEBOM_TABLE_S    s_omstr_wsd_table[] =
{
    {"omEnableWSD"                , OMTYPE_UINT32   , (void *)webom_set_wsd_switch          , (void *)netdata_get_wsd_switch        , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< WSD开关
    {"omWSDPort"                  , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_PORT_WSD                , (void *)NULL    , 8    , IFACE_ID_ANY},///< WSD端口
};

static WEBOM_TABLE_S    s_omstr_smtp_table[] =
{
    {"omSMTPSenderAddress"        , OMTYPE_STRING   , (void *)webom_set_smtp_sender_addr    , (void *)netdata_get_smtp_sender_addr  , (void *)check_email_format, 64   , IFACE_ID_ANY},///< smtp发件地址
    {"omSMTPAddress"              , OMTYPE_STRING   , (void *)webom_set_smtp_server_addr    , (void *)netdata_get_smtp_server_addr  , (void *)check_email_server_format, 64   , IFACE_ID_ANY},///< smtp服务器
    {"omSMTPPort"                 , OMTYPE_UINT32   , (void *)webom_set_smtp_server_port    , (void *)netdata_get_smtp_server_port  , (void *)check_port_format, 8    , IFACE_ID_ANY},///< smtp端口
    {"omSMTPServerAuthentication" , OMTYPE_UINT32   , (void *)webom_set_smtp_server_auth    , (void *)netdata_get_smtp_server_auth  , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< smtp服务器校验
    {"omSMTPSecurity"             , OMTYPE_UINT32   , (void *)webom_set_smtp_sec_mode       , (void *)netdata_get_smtp_sec_mode     , (void *)check_email_sec_mode_format, 4    , IFACE_ID_ANY},///< smtp安全模式
    {"omSMTPUserName"             , OMTYPE_STRING   , (void *)webom_set_smtp_username       , (void *)netdata_get_smtp_username     , (void *)check_email_server_format, 64   , IFACE_ID_ANY},///< smtp用户名
    {"omSMTPUserPassword"         , OMTYPE_STRING   , (void *)webom_set_smtp_password       , (void *)netdata_get_smtp_password     , (void *)check_email_pwsd_format, 21   , IFACE_ID_ANY},///< smtp用户密码
    {"omNetworkSmtpReset"         , OMTYPE_STRING   , (void *)NULL                          , (void *)NULL                          , (void *)NULL    , 32   , IFACE_ID_ANY},///< smtp重置
    {"omSMTPTest"                 , OMTYPE_STRING   , (void *)webom_set_smtp_test_start     , (void *)netdata_get_smtp_test_result  , (void *)NULL, 128  , IFACE_ID_ANY},///< smtp自测结果
};

static WEBOM_TABLE_S    s_omstr_mdns_table[] =
{
    {"omEnableBonjour"            , OMTYPE_UINT32   , (void *)webom_set_bonjour_switch      , (void *)netdata_get_bonjour_switch    , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< Bonjour开关
    {"omHostName"                 , OMTYPE_STRING   , (void *)webom_set_hostname            , (void *)netdata_get_hostname          , (void *)check_hostname_format, 32   , IFACE_ID_ANY},///< 主机名
    {"omBonjourPort"              , OMTYPE_CONSTANT , (void *)NULL                          , (void *)WEBOM_PORT_BONJOUR            , (void *)NULL    , 8    , IFACE_ID_ANY},///< Bonjour端口
    {"omBonjourName"              , OMTYPE_STRING   , (void *)webom_set_bonjour_server      , (void *)netdata_get_bonjour_server    , (void *)check_bonjour_sercer_format    , 128  , IFACE_ID_ANY},///< Bonjour服务名
    {"omConsumerPosition"         , OMTYPE_STRING   , (void *)webom_set_location            , (void *)netdata_get_location          , (void *)check_location_format, 256  , IFACE_ID_ANY},///< 位置
    {"omPrinterLongitude"         , OMTYPE_STRING   , (void *)webom_set_printer_longitude   , (void *)netdata_get_printer_longitude , (void *)check_lot_format, 32   , IFACE_ID_ANY},///< 打印机经度
    {"omPrinterLatitude"          , OMTYPE_STRING   , (void *)webom_set_printer_latitude    , (void *)netdata_get_printer_latitude  , (void *)check_lat_format, 32   , IFACE_ID_ANY},///< 打印机纬度
    {"omAirprintUserList"         , OMTYPE_INTERNAL , (void *)webom_set_airprint_userlist   , (void *)webom_get_airprint_userlist   , (void *)NULL    , 256  , IFACE_ID_ANY},///< 打印机用户列表
};

static WEBOM_TABLE_S    s_omstr_ipp_table[] =
{
    {"omServiceEnableIPP"         , OMTYPE_UINT32   , (void *)webom_set_ipp_switch          , (void *)netdata_get_ipp_switch        , (void *)check_switch_format    , 4 , IFACE_ID_ANY},///< IPP开关
};


static WEBOM_TABLE_S    s_omstr_ssltls_table[] =
{
    {"omHostName"                 , OMTYPE_STRING   , (void *)webom_set_hostname            , (void *)netdata_get_hostname          , (void *)check_hostname_format, 32   , IFACE_ID_ANY},///< 主机名
};

#if CONFIG_NET_WIFI
static WEBOM_TABLE_S    s_omstr_stascan_table[] =
{
    {"wifiScanResult"             , OMTYPE_INTERNAL , (void *)NULL                          , (void *)webom_get_sta_scan_result     , (void *)NULL    , 512  , IFACE_ID_ANY},///< STA 热点搜索结果
    {"wifiScanStatus"             , OMTYPE_UINT32   , (void *)webom_start_scan_ssid         , (void *)netdata_get_ap_num            , (void *)NULL    , 16   , IFACE_ID_ANY},///< STA 热点搜索结果
};

static WEBOM_TABLE_S    s_omstr_sta_table[] =
{
    {"wifiStaEnabled"             , OMTYPE_UINT32   , (void *)webom_set_sta_switch          , (void *)netdata_get_iface_switch      , (void *)check_switch_format, 16   , IFACE_ID_STA},///< STA 使能开关
    {"wifiStaSecMode"             , OMTYPE_UINT32   , (void *)webom_set_sta_sec_mode        , (void *)netdata_get_sta_sec_mode      , (void *)check_wifi_sec_mode_format, 16   , IFACE_ID_ANY},///< STA 安全模式
    {"wifiStaSSID"                , OMTYPE_STRING   , (void *)webom_set_sta_ssid            , (void *)netdata_get_sta_ssid          , (void *)check_wifi_ssid_format, 128  , IFACE_ID_ANY},///< STA SSID
    {"wifiStaWPAPassword"         , OMTYPE_STRING   , (void *)webom_set_sta_psk             , (void *)netdata_get_sta_psk           , (void *)check_wifi_pwsd_format, 128  , IFACE_ID_ANY},///< STA PSK
    {"wifiWepCurKeyValue"         , OMTYPE_CONSTANT , (void *)NULL                          , (void *)""                            , (void *)NULL    , 128  , IFACE_ID_ANY},///< STA PSK
    {"wifiScanStatus"             , OMTYPE_UINT32   , (void *)webom_start_scan_ssid         , (void *)netdata_get_ap_num            , (void *)NULL    , 16   , IFACE_ID_ANY},///< STA 搜索状态
    {"wifiStaStatus"              , OMTYPE_UINT32   , (void *)webom_set_sta_status          , (void *)netdata_get_sta_status        , (void *)check_switch_format, 16   , IFACE_ID_ANY},///< STA 连接状态
    {"wifiStaStatusReason"        , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_sta_detail        , (void *)NULL    , 16   , IFACE_ID_ANY},///< STA 状态细节
};

static WEBOM_TABLE_S    s_omstr_wifiip_table[] =
{
    {"wifiStaEnabled"             , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_iface_switch      , (void *)NULL    , 16   , IFACE_ID_STA},///< STA 使能开关
    {"wifiStaMacAddr"             , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_mac_addr          , (void *)NULL    , 32   , IFACE_ID_STA},///< STA MAC地址
    {"wifiStaIpEnable"            , OMTYPE_UINT32   , (void *)webom_set_sta_ipv4_usedhcp    , (void *)netdata_get_ipv4_usedhcp      , (void *)check_switch_format, 16   , IFACE_ID_STA},///< STA DHCPv4开关
    {"wifiStaIpAddr"              , OMTYPE_STRING   , (void *)webom_set_sta_ipv4_addr       , (void *)netdata_get_ipv4_addr         , (void *)check_ipv4_format, 16   , IFACE_ID_STA},///< STA IPv4地址
    {"wifiIPv4SubnetMask"         , OMTYPE_STRING   , (void *)webom_set_sta_ipv4_mask       , (void *)netdata_get_ipv4_mask         , (void *)check_ipv4_format, 16   , IFACE_ID_STA},///< STA IPv4子网掩码
    {"wifiIPv4GatewayAddress"     , OMTYPE_STRING   , (void *)webom_set_sta_ipv4_gtwy       , (void *)netdata_get_ipv4_gtwy         , (void *)check_ipv4_format, 16   , IFACE_ID_STA},///< STA IPv4网关地址
    {"wifiUseDHCPv6"              , OMTYPE_UINT32   , (void *)webom_set_sta_ipv6_usedhcp    , (void *)netdata_get_ipv6_usedhcp      , (void *)check_switch_format, 16   , IFACE_ID_STA},///< STA DHCPv6开关
    {"wifiIPv6LocalAddress"       , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_link         , (void *)NULL    , 64   , IFACE_ID_STA},///< STA IPv6本地链路
    {"wifiIPv6GatewayAddress"     , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_stls         , (void *)NULL    , 64   , IFACE_ID_STA},///< STA IPv6无状态
    {"wifiIPv6Address"            , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_ipv6_site         , (void *)NULL    , 64   , IFACE_ID_STA},///< STA IPv6有状态
};

static WEBOM_TABLE_S    s_omstr_wps_table[] =
{
    {"wifiWpsSecMode"             , OMTYPE_CONSTANT , (void *)webom_start_wps_request       , (void *)""                            , (void *)check_wps_request_format, 16   , IFACE_ID_ANY},///< WPS 启动模式
    {"wifiWpsModePin"             , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_wps_pin           , (void *)NULL    , 16   , IFACE_ID_ANY},///< WPS PIN码
    {"wifiWpsStatus"              , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_wps_status        , (void *)NULL    , 16   , IFACE_ID_ANY},///< WPS STATUS
    {"wifiWpsTime"                , OMTYPE_INTERNAL , (void *)NULL                          , (void *)webom_get_wps_time            , (void *)NULL    , 16   , IFACE_ID_ANY},///< WPS TIME
};

static WEBOM_TABLE_S    s_omstr_wfd_table[] =
{
    {"wifiWfdSupported"           , OMTYPE_UINT32   , (void *)webom_set_wfd_switch          , (void *)netdata_get_iface_switch      , (void *)check_switch_format, 16   , IFACE_ID_WFD},///< WFD 使能开关
    {"wifiWfdMacAddr"             , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_mac_addr          , (void *)NULL    , 32   , IFACE_ID_WFD},///< WFD MAC地址
    {"wifiWfdSsidPrefix"          , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_wfd_ssid_prefix   , (void *)NULL    , 32   , IFACE_ID_ANY},///< WFD SSID前缀
    {"wifiWfdUapSSID"             , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_wfd_ssid_suffix   , (void *)NULL    , 32   , IFACE_ID_ANY},///< WFD SSID
    {"wifiWfdPassword"            , OMTYPE_STRING   , (void *)webom_set_wfd_psk             , (void *)netdata_get_wfd_psk           , (void *)check_wfd_psk, 64   , IFACE_ID_ANY},///< WFD 密码
};
#endif

static WEBOM_TABLE_S    s_omstr_system_table[] =
{
    {"omConsumerPosition"         , OMTYPE_STRING   , (void *)webom_set_location            , (void *)netdata_get_location          , (void *)check_location_format, 256  , IFACE_ID_ANY},///< 位置
    {"omContactInfo"              , OMTYPE_STRING   , (void *)webom_set_contacts            , (void *)netdata_get_contacts          , (void *)check_contacts_format, 128  , IFACE_ID_ANY},///< 联系人
    {"omPropertyNumber"           , OMTYPE_STRING   , (void *)webom_set_property_number     , (void *)netdata_get_prop_num          , (void *)check_property_number_format, 64   , IFACE_ID_ANY},///< 财产编号
    {"omSleepTime"                , OMTYPE_UINT32   , (void *)webom_set_sleep_time          , (void *)netdata_get_sleep_time        , (void *)check_sleep_time_format, 16   , IFACE_ID_ANY},///< 休眠时间
    {"omSleepMode"                , OMTYPE_UINT32   , (void *)webom_set_sleep_mode          , (void *)netdata_get_sleep_mode        , (void *)check_switch_format, 16   , IFACE_ID_ANY},///< 休眠模式
    {"omJobTimeOut"               , OMTYPE_UINT32   , (void *)webom_set_io_timeout          , (void *)netdata_get_io_timeout        , (void *)check_io_timeout_format, 16   , IFACE_ID_ANY},///< 打印超时时间
    {"omSystemDate"               , OMTYPE_INTERNAL , (void *)webom_set_system_date         , (void *)webom_get_system_date         , (void *)check_system_date_format, 16   , IFACE_ID_ANY},///< 系统日期
    {"omSystemTime"               , OMTYPE_INTERNAL , (void *)webom_set_system_time         , (void *)webom_get_system_time         , (void *)check_system_time_format, 16   , IFACE_ID_ANY},///< 系统时间
    {"omSystemTimeZone"           , OMTYPE_UINT32   , (void *)NULL                          , (void *)netdata_get_timezone          , (void *)NULL    , 16   , IFACE_ID_ANY},///< 时区
};

static WEBOM_TABLE_S    s_omstr_mailgroup_table[] =
{
    {"omMailgroupList"            , OMTYPE_INTERNAL , (void *)webom_set_mail_grouplist      , (void *)webom_get_mail_grouplist      , (void *)NULL    , 20480, IFACE_ID_ANY},///< 群组邮件
};

static WEBOM_TABLE_S    s_omstr_mailinfo_table[] =
{
    {"omMailinfoList"             , OMTYPE_INTERNAL , (void *)webom_set_mail_addrbook       , (void *)webom_get_mail_addrbook       , (void *)NULL    , 20480, IFACE_ID_ANY},///< 邮件地址簿
};

static WEBOM_TABLE_S    s_omstr_ftpinfo_table[] =
{
    {"omFtpinfoList"              , OMTYPE_INTERNAL , (void *)webom_set_ftp_addrbook        , (void *)webom_get_ftp_addrbook        , (void *)NULL    , 20480, IFACE_ID_ANY},///< FTP地址簿
};

static WEBOM_TABLE_S    s_omstr_smbinfo_table[] =
{
    {"omSmbinfoList"              , OMTYPE_INTERNAL , (void *)webom_set_smb_addrbook        , (void *)webom_get_smb_addrbook        , (void *)NULL    , 20480, IFACE_ID_ANY},///< SMB地址簿
};

#if CONFIG_NET_WHITELIST
static WEBOM_TABLE_S    s_omstr_whitelistinfo_table[] =
{
#if CONFIG_NET_SAFETY_MACHINE
    {"omWebpageWhiteList"              , OMTYPE_INTERNAL , (void *)NULL,                                 (void *)webom_get_whitelist_addrbook , (void *)NULL, 81920, IFACE_ID_ANY},///<WHITELIST地址簿
    {"omWhiteListContent"              , OMTYPE_INTERNAL , (void *)webom_set_whitelist_addrbook        , (void *)webom_get_whitelist_addrbook ,(void *)NULL, 81920, IFACE_ID_ANY},///<WHITELIST地址簿
#else
    {"omWhitelistinfoList"              , OMTYPE_INTERNAL , (void *)webom_set_whitelist_addrbook        , (void *)webom_get_whitelist_addrbook , (void *)NULL, 81920, IFACE_ID_ANY},///<WHITELIST地址簿
#endif
    {"omWhiteListEnable"                , OMTYPE_UINT32  , (void *)webom_set_whitelist_switch          , (void *)netdata_get_whitelist_switch , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< WHITELIST开关
};
#endif /*CONFIG_NET_WHITELIST end*/

static WEBOM_TABLE_S    s_omstr_email_table[] =
{
    {"omSMTPClientAddress1"       , OMTYPE_STRING   , (void *)webom_set_alarm_client_addr1  , (void *)netdata_get_alarm_client_addr1 , (void *)check_email_format, 64   , IFACE_ID_ANY},///< 收件箱地址1
    {"omSMTPClientAddress2"       , OMTYPE_STRING   , (void *)webom_set_alarm_client_addr2  , (void *)netdata_get_alarm_client_addr2 , (void *)check_email_format, 64   , IFACE_ID_ANY},///< 收件箱地址2
    {"omSMTPClientAddress3"       , OMTYPE_STRING   , (void *)webom_set_alarm_client_addr3  , (void *)netdata_get_alarm_client_addr3 , (void *)check_email_format, 64   , IFACE_ID_ANY},///< 收件箱地址3
    {"omSMTPClientAddress4"       , OMTYPE_STRING   , (void *)webom_set_alarm_client_addr4  , (void *)netdata_get_alarm_client_addr4 , (void *)check_email_format, 64   , IFACE_ID_ANY},///< 收件箱地址4
    {"omSMTPEmailPaperEmpty"      , OMTYPE_UINT32   , (void *)webom_set_alarm_paper_empty   , (void *)netdata_get_alarm_paper_empty  , (void *)check_switch_format, 8    , IFACE_ID_ANY},///< 缺纸警告
    {"omSMTPEmailPaperJam"        , OMTYPE_UINT32   , (void *)webom_set_alarm_paper_jam     , (void *)netdata_get_alarm_paper_jam    , (void *)check_switch_format, 8    , IFACE_ID_ANY},///< 卡纸警告
    {"omSMTPEmailCartridgeEnd"    , OMTYPE_UINT32   , (void *)webom_set_alarm_toner_empty   , (void *)netdata_get_alarm_toner_empty  , (void *)check_switch_format, 8    , IFACE_ID_ANY},///< 粉盒寿命尽警告
    {"omSMTPEmailTonerLowWarning" , OMTYPE_UINT32   , (void *)webom_set_alarm_toner_low     , (void *)netdata_get_alarm_toner_low    , (void *)check_switch_format, 8    , IFACE_ID_ANY},///< 粉盒粉量低警告
    {"omSMTPEmailWasteToner"      , OMTYPE_UINT32   , (void *)webom_set_alarm_waste_toner   , (void *)netdata_get_alarm_waste_toner  , (void *)check_switch_format, 8    , IFACE_ID_ANY},///< 废粉瓶警告
};

static WEBOM_TABLE_S    s_omstr_watermark_table[] =
{
#if CONFIG_NET_SAFETY_MACHINE
    {"omWaterMark1"         , OMTYPE_STRING   , (void *)webom_set_water_mark1         , (void *)netdata_get_water_mark1 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印1
    {"omWaterMark2"         , OMTYPE_STRING   , (void *)webom_set_water_mark2         , (void *)netdata_get_water_mark2 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印2
    {"omWaterMark3"         , OMTYPE_STRING   , (void *)webom_set_water_mark3         , (void *)netdata_get_water_mark3 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印3
    {"omWaterMark4"         , OMTYPE_STRING   , (void *)webom_set_water_mark4         , (void *)netdata_get_water_mark4 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印4
    {"omWaterMark5"         , OMTYPE_STRING   , (void *)webom_set_water_mark5         , (void *)netdata_get_water_mark5 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印5
    {"omWaterMark6"         , OMTYPE_STRING   , (void *)webom_set_water_mark6         , (void *)netdata_get_water_mark6 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印6
#else
    {"omCustomWaterMark1"         , OMTYPE_STRING   , (void *)webom_set_water_mark1         , (void *)netdata_get_water_mark1 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印1
    {"omCustomWaterMark2"         , OMTYPE_STRING   , (void *)webom_set_water_mark2         , (void *)netdata_get_water_mark2 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印2
    {"omCustomWaterMark3"         , OMTYPE_STRING   , (void *)webom_set_water_mark3         , (void *)netdata_get_water_mark3 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印3
    {"omCustomWaterMark4"         , OMTYPE_STRING   , (void *)webom_set_water_mark4         , (void *)netdata_get_water_mark4 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印4
    {"omCustomWaterMark5"         , OMTYPE_STRING   , (void *)webom_set_water_mark5         , (void *)netdata_get_water_mark5 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印5
    {"omCustomWaterMark6"         , OMTYPE_STRING   , (void *)webom_set_water_mark6         , (void *)netdata_get_water_mark6 , (void *)NULL          , 128  , IFACE_ID_ANY},///< 水印6
#endif
};

static WEBOM_TABLE_S    s_omstr_copyauth_table[] =
{
    {"omCopyAuthEnabled"          , OMTYPE_UINT32   , (void *)webom_set_copy_audit_switch   , (void *)netdata_get_copy_audit_switch , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< 复印审计开关
    {"omAuthServerAddress"        , OMTYPE_STRING   , (void *)webom_set_copy_audit_server   , (void *)netdata_get_copy_audit_server , (void *)NULL, 64   , IFACE_ID_ANY},///< 服务器地址
};

static WEBOM_TABLE_S    s_omstr_copyconfig_table[] =
{
    {"omCopyColorControl"         , OMTYPE_UINT32    , (void *)webom_set_copy_color_ctrl    , (void *)netdata_get_copy_color_ctrl    , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< 彩色复印管控开关
    {"omCopyColorPassword"        , OMTYPE_STRING    , (void *)webom_set_copy_color_pswd    , (void *)netdata_get_copy_color_pswd    , (void *)check_color_pswd_format, 64   , IFACE_ID_ANY},///< 彩色复印密码
};

static WEBOM_TABLE_S    s_omstr_secalert_table[] =
{
#if CONFIG_NET_SAFETY_MACHINE
  {"omSecEnabled"               , OMTYPE_UINT32   , (void *)webom_set_sec_alert_switch    , (void *)netdata_get_sec_alert_switch     , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< 安全报警开关
  {"omSecUnit"                  , OMTYPE_STRING   , (void *)webom_set_sec_alert_unit      , (void *)netdata_get_sec_alert_unit       , (void *)NULL , 128  , IFACE_ID_ANY},///< 单位
  {"omSecDepartment"            , OMTYPE_STRING   , (void *)webom_set_sec_alert_department, (void *)netdata_get_sec_alert_department , (void *)NULL , 128  , IFACE_ID_ANY},///< 部门
  {"omSecOwner"                 , OMTYPE_STRING   , (void *)webom_set_sec_alert_owner     , (void *)netdata_get_sec_alert_owner      , (void *)NULL , 128  , IFACE_ID_ANY},///< 人员
  {"omSecCountryIPAddr"         , OMTYPE_STRING   , (void *)webom_set_sec_alert_server1   , (void *)netdata_get_sec_alert_server1    , (void *)NULL , 128  , IFACE_ID_ANY},///< 服务器地址1
  {"omSecNationIPAddr"          , OMTYPE_STRING   , (void *)webom_set_sec_alert_server2   , (void *)netdata_get_sec_alert_server2    , (void *)NULL , 128  , IFACE_ID_ANY},///< 服务器地址2
#endif
};

#if CONFIG_NET_SAFETY_MACHINE
static WEBOM_TABLE_S    s_omstr_secerr_table[] =
{
    {"omErrDeal"                 , OMTYPE_UINT32   , (void *)webom_set_err_deal           , (void *)netdata_get_err_deal_mode , (void *)NULL , 32   , IFACE_ID_ANY},///< 出错作业处理模式
    {"omErrDelayTime"            , OMTYPE_UINT32   , (void *)webom_set_err_deal_time      , (void *)netdata_get_err_deal_time , (void *)NULL , 32   , IFACE_ID_ANY},///< 出错作业处理延时
};
#endif


static WEBOM_TABLE_S    s_omstr_device_table[] =
{
    {"omDeviceID"                 , OMTYPE_STRING   , (void *)NULL                          , (void *)netdata_get_sole_serial_number   , (void *)NULL , 32   , IFACE_ID_ANY},///< 设备标识码
};

static WEBOM_TABLE_S    s_omstr_usb_table[] =
{
    {"omEnableUsbHID"             , OMTYPE_UINT32   , (void *)webom_set_usb_hid_switch      , (void *)netdata_get_usb_hid_switch       , (void *)check_switch_format, 4    , IFACE_ID_ANY},///< USB HID计开关
};

static WEBOM_GROUP_S    s_omstr_group[] =
{
    {"EXTERN"           , s_omstr_extern_table          , ARRAY_SIZE(s_omstr_extern_table)          },
    {"INFO"             , s_omstr_info_table            , ARRAY_SIZE(s_omstr_info_table)            },
    {"LOGIN"            , s_omstr_manager_table         , ARRAY_SIZE(s_omstr_manager_table)         },
    {"MANAGER"          , s_omstr_manager_table         , ARRAY_SIZE(s_omstr_manager_table)         },
    {"IPV4"             , s_omstr_ipv4_table            , ARRAY_SIZE(s_omstr_ipv4_table)            },
    {"IPV6"             , s_omstr_ipv6_table            , ARRAY_SIZE(s_omstr_ipv6_table)            },
    {"RAWLPD"           , s_omstr_rawlpd_table          , ARRAY_SIZE(s_omstr_rawlpd_table)          },
    {"SNMP"             , s_omstr_snmp_table            , ARRAY_SIZE(s_omstr_snmp_table)            },
    {"SLP"              , s_omstr_slp_table             , ARRAY_SIZE(s_omstr_slp_table)             },
    {"SMB"              , s_omstr_smb_table             , ARRAY_SIZE(s_omstr_smb_table)             },
    {"WSD"              , s_omstr_wsd_table             , ARRAY_SIZE(s_omstr_wsd_table)             },
    {"SMTP"             , s_omstr_smtp_table            , ARRAY_SIZE(s_omstr_smtp_table)            },
    {"AirPrint"         , s_omstr_mdns_table            , ARRAY_SIZE(s_omstr_mdns_table)            },
    {"IPP"              , s_omstr_ipp_table             , ARRAY_SIZE(s_omstr_mdns_table)            },
    {"MDNS"             , s_omstr_mdns_table            , ARRAY_SIZE(s_omstr_mdns_table)            },
    {"AIRPUSER"         , s_omstr_mdns_table            , ARRAY_SIZE(s_omstr_mdns_table)            },
    {"SSLTLS"           , s_omstr_ssltls_table          , ARRAY_SIZE(s_omstr_ssltls_table)          },
#if CONFIG_NET_WIFI
    {"STASCAN"          , s_omstr_stascan_table         , ARRAY_SIZE(s_omstr_stascan_table)         },
    {"STA"              , s_omstr_sta_table             , ARRAY_SIZE(s_omstr_sta_table)             },
    {"WIFIIP"           , s_omstr_wifiip_table          , ARRAY_SIZE(s_omstr_wifiip_table)          },
    {"WPS"              , s_omstr_wps_table             , ARRAY_SIZE(s_omstr_wps_table)             },
    {"WFD"              , s_omstr_wfd_table             , ARRAY_SIZE(s_omstr_wfd_table)             },
#endif
    {"SYSTEM"           , s_omstr_system_table          , ARRAY_SIZE(s_omstr_system_table)          },
    {"MAILGROUP"        , s_omstr_mailgroup_table       , ARRAY_SIZE(s_omstr_mailgroup_table)       },
    {"MAILINFO"         , s_omstr_mailinfo_table        , ARRAY_SIZE(s_omstr_mailinfo_table)        },
    {"FTPINFO"          , s_omstr_ftpinfo_table         , ARRAY_SIZE(s_omstr_ftpinfo_table)         },
    {"SMBINFO"          , s_omstr_smbinfo_table         , ARRAY_SIZE(s_omstr_smbinfo_table)         },
#if CONFIG_NET_WHITELIST
#if CONFIG_NET_SAFETY_MACHINE
    {"WHITELIST"    , s_omstr_whitelistinfo_table   , ARRAY_SIZE(s_omstr_whitelistinfo_table)   },
#else
    {"WHITELISTINFO"    , s_omstr_whitelistinfo_table   , ARRAY_SIZE(s_omstr_whitelistinfo_table)   },
#endif
#endif /*CONFIG_NET_WHITELIST end*/
    {"EMAIL"            , s_omstr_email_table           , ARRAY_SIZE(s_omstr_email_table)           },
#if CONFIG_NET_SAFETY_MACHINE
    {"WATERMARK"        , s_omstr_watermark_table       , ARRAY_SIZE(s_omstr_watermark_table)       },
    {"DEVICEINFO"       , s_omstr_device_table          , ARRAY_SIZE(s_omstr_device_table)          },
    {"SECERR"           , s_omstr_secerr_table          , ARRAY_SIZE(s_omstr_secerr_table)          },
#else
    {"CUSTOMWATERMARK"  , s_omstr_watermark_table       , ARRAY_SIZE(s_omstr_watermark_table)       },
    {"DEVICE"           , s_omstr_device_table          , ARRAY_SIZE(s_omstr_device_table)          },
#endif
    {"COPYAUTH"         , s_omstr_copyauth_table        , ARRAY_SIZE(s_omstr_copyauth_table)        },
    {"COPYCONFIG"       , s_omstr_copyconfig_table      , ARRAY_SIZE(s_omstr_copyconfig_table)      },
    {"SECALERT"         , s_omstr_secalert_table        , ARRAY_SIZE(s_omstr_secalert_table)        },
    {"USB"              , s_omstr_usb_table             , ARRAY_SIZE(s_omstr_usb_table)             },
};

/**
 * @brief       Set function for "websrv_omstr_set_val".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   omstr   : The oms tring.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
int32_t websrv_omstr_set_val(WEBOM_CTX_S* webomctx, const char* omstr, int32_t index, const char* val)
{
    int32_t         ret = -1;
    WEBOM_GROUP_S*  group = NULL;
    WEBOM_TABLE_S*  table = NULL;
    char            omval[1024];
    size_t          i, j;

    RETURN_VAL_IF(omstr == NULL || val == NULL, NET_WARN, -1);
    RETURN_VAL_IF(webomctx == NULL, NET_WARN, -1);

    memset(omval, 0, sizeof(omval));
    if ( base64_decode(val, strlen(val), omval, sizeof(omval)) < 0 )
    {
        NET_WARN("base64_decode(%s) for omstr(%s) failed", val, omstr);
        return ret;
    }

    for ( j = 1; j < ARRAY_SIZE(s_omstr_group) && table == NULL; ++j )
    {
        if ( group != &(s_omstr_group[j]) )
        {
            group = &(s_omstr_group[j]);
        }
        else
        {
            continue;
        }

        for ( i = 0; i < group->omstr_count; ++i )
        {
            if ( strcmp(group->omstr_table[i].omstr, omstr) == 0 && group->omstr_table[i].set_func != NULL )
            {
                table = &(group->omstr_table[i]);
                break;
            }
        }
    }

    if ( table != NULL && table->verify_func != NULL && strlen(omval) > 0 )
    {
        ret = ((WEBOM_FORMAT_CHECK_FUNC)table->verify_func)(omval);
        if ( -1 == ret )
        {
            NET_DEBUG("set omstr(%s) index(%d) omval(%s) format error", omstr, index, omval);
            return ret;
        }
    }

    if ( table == NULL || table->set_func == NULL )
    {
        NET_DEBUG("invalid omstr(%s)", omstr);
        return ret;
    }

    NET_TRACE("set omstr(%s) index(%d) omval(%s)", omstr, index, omval);
    ret = ((WEBOM_SET_INTERNAL_FUNC)table->set_func)(webomctx, index, omval);
    return ret;
}

/**
 * @brief       Set function for "websrv_omstr_set_val".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   omstr   : The oms tring.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
int32_t websrv_omstr_get_val(WEBOM_CTX_S* webomctx, WEBOM_GROUP_S* group, const char* omstr, int32_t index, char* buf, size_t buf_size)
{
    WEBOM_TABLE_S*  table = NULL;
    char*           omval = NULL;
    int32_t         omlen = 0;
    int32_t         nbuf = 0;
    size_t          i;

    RETURN_VAL_IF(group == NULL || group->omstr_table == NULL || group->omstr_count == 0, NET_WARN, 0);
    RETURN_VAL_IF(webomctx == NULL || omstr == NULL || buf == NULL, NET_WARN, 0);

    for ( i = 0; i < group->omstr_count; ++i )
    {
        if ( strcmp(group->omstr_table[i].omstr, omstr) == 0 )
        {
            table = &(group->omstr_table[i]);
            break;
        }
    }

    if ( table == NULL )
    {
        NET_DEBUG("no search valid omstr(%s) in group(%s)", omstr, group->module_name);
        return 0;
    }

    if ( table->get_func == NULL || table->val_size == 0 )
    {
        NET_DEBUG("unsupport to get omstr(%s)", omstr);
        return 0;
    }

    omval = (char *)pi_zalloc(table->val_size);
    RETURN_VAL_IF(omval == NULL, NET_WARN, 0);

    switch ( table->val_type )
    {
    case OMTYPE_CONSTANT:
        omlen = snprintf(omval, table->val_size, "%s", (const char *)(table->get_func));
        break;
    case OMTYPE_INTERNAL:
        omlen = ((WEBOM_GET_INTERNAL_FUNC)table->get_func)(webomctx, index, omval, table->val_size);
        break;
    case OMTYPE_UINT32:
        if ( table->ifid >= 0 )
        {
            omlen = snprintf(omval, table->val_size, "%u", ((WEBOM_GET_UINT32_ARRAY_FUNC)table->get_func)(DATA_MGR_OF(webomctx), (uint32_t)table->ifid));
        }
        else
        {
            omlen = snprintf(omval, table->val_size, "%u", ((WEBOM_GET_UINT32_VALUE_FUNC)table->get_func)(DATA_MGR_OF(webomctx)));
        }
        if ( omlen >= table->val_size )
        {
            NET_WARN("omval(%s) is too long !", omval);
            omval = NULL;
        }
        break;
    case OMTYPE_STRING:
        if ( table->ifid >= 0 )
        {
            omlen = ((WEBOM_GET_STRINT_ARRAY_FUNC)table->get_func)(DATA_MGR_OF(webomctx), (uint32_t)table->ifid, omval, table->val_size);
        }
        else
        {
            omlen = ((WEBOM_GET_STRINT_VALUE_FUNC)table->get_func)(DATA_MGR_OF(webomctx), omval, table->val_size);
        }
        break;
    default:
        NET_WARN("invalid val_type(%d)", table->val_type);
        break;
    }

    if ( omlen > 0 )
    {
        nbuf = base64_encode(omval, omlen, buf, buf_size);
    }
    pi_free(omval);

    return nbuf;
}

/**
 * @brief       Set function for "websrv_omstr_set_val".
 * @param[in]   webomctx: The WEBOM_CTX_S object pointer.
 * @param[in]   omstr   : The oms tring.
 * @param[in]   index   : The index of this om string.
 * @param[in]   omval   : The value of this om string.
 * <AUTHOR> huanbin
 * @date        2023-10-17
 */
WEBOM_GROUP_S* websrv_omstr_get_group(const char* module)
{
    WEBOM_GROUP_S*  group = NULL;
    size_t          i;

    for ( i = 0; i < ARRAY_SIZE(s_omstr_group); ++i )
    {
        if ( strcmp(s_omstr_group[i].module_name, module) == 0 )
        {
            group = &(s_omstr_group[i]);
            break;
        }
    }

    if ( group == NULL )
    {
        NET_DEBUG("no search valid group(%s)", module);
    }
    return group;
}

int32_t websrv_omstr_page_support_init(NET_CTX_S* net_ctx)
{
    int32_t             ret = -1;
    size_t              read_byte;
    char*               read_buf;
    long                file_size;
    FILE*               file;
    uint32_t            support;
    cJSON*              root = NULL;
    cJSON*              cjson_data = NULL;
    char                pdt_name[64];
    char                config_file[128];

    do
    {
        netdata_get_pdt_name(net_ctx->data_mgr, pdt_name, sizeof(pdt_name));
        snprintf(config_file, sizeof(config_file), "/etc/%s_%s", pdt_name, CJSON_WEBPAGE_SUPPORT_CONFIG_PATH);
        NET_DEBUG("config_file %s", config_file);
        file = fopen(config_file, "r");
        BREAK_IF(NULL == file, NET_WARN);
        fseek(file, 0, SEEK_END);
        file_size = ftell(file);
        fseek(file, 0, SEEK_SET);

        read_buf = (char*)malloc(file_size + 1);
        BREAK_IF(read_buf == NULL, NET_WARN);
        memset(read_buf, 0, file_size + 1);

        read_byte = fread(read_buf, 1, file_size, file);
        BREAK_IF(read_byte != file_size, NET_WARN);
        root = cJSON_Parse(read_buf);
        BREAK_IF(root == NULL, NET_WARN);
        ret = 0;
    }
    while( 0 );


    NET_DEBUG("read_buf %s", read_buf);
    WEBPAGE_SUPPORT_MAJOR_INIT(language, read_buf)
    WEBPAGE_SUPPORT_MAJOR_INIT(safety_log, read_buf)
#if CONFIG_NET_RAWPRINT
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, raw, read_buf)
#endif
#if CONFIG_NET_SLP
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, slp, read_buf)
#endif
#if CONFIG_NET_WHITELIST
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, whitelist, read_buf)
#endif
#if CONFIG_NET_SNMP
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, snmp, read_buf)
#endif
#if CONFIG_NET_WSD
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, wsd, read_buf)
#endif
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, smb, read_buf)
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, sntp, read_buf)
    WEBPAGE_SUPPORT_SUB1_INIT(machine_setting, system, sleep_mode, read_buf)
#if CONFIG_NET_SMTP
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, smtp, read_buf)
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, addressbook, read_buf)
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, email_notification, read_buf)
#endif
#if CONFIG_NET_BONJOUR
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, bonjour, read_buf)
    WEBPAGE_SUPPORT_SUB1_INIT(protocol_setting, bonjour, mobile_print, read_buf)
    #if CONFIG_AIRPRINT
        WEBPAGE_SUPPORT_SUB1_INIT(protocol_setting, bonjour, airprint, read_buf)
    #endif
#endif
#if CONFIG_NET_IPPSRV
    WEBPAGE_SUPPORT_SUB_INIT(protocol_setting, ippsrv, read_buf)
#endif
#if CONFIG_NET_FTP
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, ftp_addressbook, read_buf)
#endif
#if CONFIG_SCAN_SMB
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, smb_addressbook, read_buf)
#endif
#if CONFIG_COPY_WATERMARK
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, watermark, read_buf)
#endif
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, remote_control, read_buf)
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, equipment_report, read_buf)
#if CONFIG_NET_UPGRADE_FIRMWARE
    WEBPAGE_SUPPORT_SUB_INIT(machine_setting, firmware_upgrade, read_buf)
    WEBPAGE_SUPPORT_SUB1_INIT(machine_setting, firmware_upgrade, offline_upgrade, read_buf)
    WEBPAGE_SUPPORT_SUB1_INIT(machine_setting, firmware_upgrade, online_upgrade, read_buf)
#endif
#if CONFIG_COPY

#if CONFIG_COLOR
    WEBPAGE_SUPPORT_MAJOR_INIT(job_control, read_buf)
#endif

#endif
    if ( file != NULL )
    {
        fclose(file);
    }
    if ( read_buf != NULL )
    {
        free(read_buf);
    }
    NET_DEBUG("webpage support init ok !!!\n");

    return ret;
}


