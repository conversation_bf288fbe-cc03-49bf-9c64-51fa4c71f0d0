/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file transmission.h
 * @addtogroup trans
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief transport init 
 */
#include "basic/config.h"
#include <uv.h>

/**
 * @brief   transport_init
 * <AUTHOR> @date    2024-06-11
 */
void transport_init();

/**
 * @brief   transport send
 * @param[in] *buffer :buffer cache
 * @param[in] length :buffer length
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t transport_send(const uint8_t* buffer, uint16_t length);

/**
 * @brief   transport receive
 * @param[in] *buffer :buffer cache
 * @param[in] length :buffer length
 * @param[in] ms :outtime timers
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t transport_receive(uint8_t* buffer, uint16_t* length, uint32_t ms);

/**
 * @brief   trans release
 * <AUTHOR> @date    2024-06-11
 */
void trans_release();
/**
 * @}
 */
 
