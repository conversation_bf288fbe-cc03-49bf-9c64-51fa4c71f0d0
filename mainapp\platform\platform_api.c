/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file platform_api.c
 * @addtogroup platform
 * @{
 * @addtogroup platform_api
 * <AUTHOR> @date 2014-12-03
 * @brief provide some common printer proterty information and interface functions\n
 *        for getting properties
 */

#include <stdio.h>
#include <errno.h>
#include <stdbool.h>
#include <string.h>
#include <time.h>
#include "platform_api.h"
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_mgr.h"
#include "moduleid.h"
#include "nvram.h"
#include "pol/pol_mem.h"
#include "pol/pol_time.h"
#include "pol/pol_string.h"
#include "pol/pol_convert.h"
#include "pol/pol_io.h"
#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "pol/pol_define.h"
#include "utilities/systime.h"
#include "platform_aclregister.h"
#include "platform_backup.h"
#include "print/print.h"
#include "pedkapi/platform_pedk.h"
#include "job_manager.h"
#include "hal.h"
#include "data_distribution/panel_serial_num.h"
#include "utilities/secure_upgrade.h"

#define PLATFORM_ERR -1
#define PLATFORM_OK 0

#define NEED_TO_SAVE 1
#define NOT_TO_SAVE 0

#define EN_MACHINE_PATH "/root/en_machine_config.ini"

#ifdef PARTITION_PLAN
#define MACHINE_PATH "/data/machine_config.ini"
#else
#define MACHINE_PATH "/tmp/machine_config.ini"
#endif
char decode_key[128] = "xxxaaabbbcccxxx";

#define LINE_SIZE 1024
#define MAC_ADDRESS_LEN 6

static CONFIG_TABLE_VARS_S s_plat_config_vars;
static EVT_MGR_CLI_S *g_cli_ptr = NULL;
static MACHINE_CONFIG_S s_machine_config_ary[24] = {{0}};
static MACHINE_CONFIG_S s_current_machine_config;

#define CHECK_GET_NVRAM_RESULT(ret, id) do { \
    if (ret < 0) { \
        pi_log_d( "platform get nv fail, ret: %d id: %d\n", ret, id); \
    } \
} while(0)

typedef enum {
    INI_UINT = 0,
    INI_INT,
    INI_STRING,
} INI_VALUE_TYPE_E;

typedef struct tag_country_language
{
    e_CountryCode   countryCode;
    e_RegionCode    regionCode;
    e_LanguageCode  languageCode;
}COUNTRYCODE_LANGUAGE_S;

typedef struct {
    EVT_TYPE_E event_id;
    PLATFORM_ID_E nv_id;
} EVENT_ID_TO_NV_ID_S;

typedef struct {
    uint32_t machine_type;
    char*    series_name;
    char*    pdt_name;
    uint16_t pid;
} MACHINE_CONFIG_TAG_S;


static const COUNTRYCODE_LANGUAGE_S s_country_to_language[] =
{
    {ITALY,         REGION_EUROPE,      SYS_LANGUAGE_ITALIAN            },
    {ENGLISH,       REGION_EUROPE,      SYS_LANGUAGE_ENGLISH            },
    {UK,            REGION_EUROPE,      SYS_LANGUAGE_ENGLISH            },
    {GERMANY,       REGION_EUROPE,      SYS_LANGUAGE_GERMAN             },
    {ISRAEL,        REGION_EUROPE,      SYS_LANGUAGE_ENGLISH            },
    {AUSTRALIA,     REGION_EUROPE,      SYS_LANGUAGE_ENGLISH            },
    {TURKEY,        REGION_EUROPE,      SYS_LANGUAGE_TURKISH            },
    {POLAND,        REGION_EUROPE,      SYS_LANGUAGE_POLISH             },
    {USA,           REGION_AMERICAN,    SYS_LANGUAGE_ENGLISH            },
    {CANADA,        REGION_AMERICAN,    SYS_LANGUAGE_ENGLISH            },
    {VENEZUELA,     REGION_AMERICAN,    SYS_LANGUAGE_SPANISH            },
    {BOLIVIA,       REGION_AMERICAN,    SYS_LANGUAGE_SPANISH            },
    {PERU,          REGION_AMERICAN,    SYS_LANGUAGE_SPANISH            },
    {COLUMBIA,      REGION_AMERICAN,    SYS_LANGUAGE_SPANISH            },
    {RUSSIA,        REGION_AMERICAN,    SYS_LANGUAGE_RUSSIAN            },
    {INDIA,         REGION_ASIA,        SYS_LANGUAGE_ENGLISH            },
    {SOUTH_AFRICA,  REGION_ASIA,        SYS_LANGUAGE_ENGLISH            },
    {TAIWAN,        REGION_ASIA,        SYS_LANGUAGE_TRADITIONAL_CHINESE},
    {THAILAND,      REGION_ASIA,        SYS_LANGUAGE_ENGLISH            },
    {PHILIPPINES,   REGION_ASIA,        SYS_LANGUAGE_ENGLISH            },
    {CHINA,         REGION_CHINA,       SYS_LANGUAGE_CHINESE            },
    {FRANCE,        REGION_EUROPE,      SYS_LANGUAGE_FRENCH             },
    {TUNISIA,       REGION_ASIA,        SYS_LANGUAGE_FRENCH             },
    {ISRAEL,        REGION_ASIA,        SYS_LANGUAGE_HEBREW             },
    {JAPAN,         REGION_ASIA,        SYS_LANGUAGE_JAPANESE           },
    {SPAIN,         REGION_EUROPE,      SYS_LANGUAGE_SPANISH            },
    {SAUDI_ARABIA,  REGION_EUROPE,      SYS_LANGUAGE_ARABIC             },
    {UAE,           REGION_ASIA,        SYS_LANGUAGE_ARABIC             },
    {EGYPT,         REGION_ASIA,        SYS_LANGUAGE_ARABIC             },
    {MOROCCO,       REGION_ASIA,        SYS_LANGUAGE_ARABIC             },
    {PORTUGAL,      REGION_EUROPE,      SYS_LANGUAGE_PORTUGUESE         },
    {BRAZIL,        REGION_AMERICAN,    SYS_LANGUAGE_PORTUGUESE         },
    {KOREA,         REGION_ASIA,        SYS_LANGUAGE_KOREAN             },
    {ROMANIA,       REGION_EUROPE,      SYS_LANGUAGE_ROMANIAN           },
    {THAILAND,      REGION_ASIA,        SYS_LANGUAGE_THAI               },
    {GREECE,        REGION_EUROPE,      SYS_LANGUAGE_GREEK              },
    {CZECH_REPUBLIC,REGION_EUROPE,      SYS_LANGUAGE_CZECH              },
    {SLOVAK_REP,    REGION_EUROPE,      SYS_LANGUAGE_CZECH              },
    {UKRAINE,       REGION_EUROPE,      SYS_LANGUAGE_UKRAINIAN          },
    {KAZAKHSTAN,    REGION_ASIA,        SYS_LANGUAGE_KAZAKH             },
    {DENMARK,       REGION_EUROPE,      SYS_LANGUAGE_DANISH             },
    {NORWAY,        REGION_EUROPE,      SYS_LANGUAGE_NORWEGIAN          },
    {AZERBAIJAN,    REGION_ASIA,        SYS_LANGUAGE_AZERBAIJANI        },
    {HUNGARY,       REGION_EUROPE,      SYS_LANGUAGE_HUNGARIAN          },
    {VIETNAM,       REGION_ASIA,        SYS_LANGUAGE_VIETNAMESE         }
};

static const EVENT_ID_TO_NV_ID_S s_eventid_to_nvid_table[] = {
    {EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_REQUEST, PLATFORM_ID_AUTO_SHUTDOWN_ENABLED},
    {EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_REQUEST, PLATFORM_ID_AUTO_SHUTDOWN_TIME},
    {EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_REQUEST, PLATFORM_ID_AUTO_SHUTDOWN_CONDITION},
    {EVT_TYPE_PLATFORM_POWER_ON_COUNT_REQUEST, PLATFORM_ID_POWER_ON_COUNT},
    {EVT_TYPE_PLATFORM_VOLUME_SET_REQUEST, PLATFORM_ID_VOLUME_SET},
    {EVT_TYPE_PLATFORM_COUNTRY_CODE_REQUEST, PLATFORM_ID_COUNTRY_CODE},
    {EVT_TYPE_PLATFORM_LANGUAGE_CODE_REQUEST, PLATFORM_ID_LANGUAGE_CODE},
    {EVT_TYPE_PLATFORM_REGION_CODE_REQUEST, PLATFORM_ID_REGION_CODE},
    {EVT_TYPE_PLATFORM_QUIET_REQUEST, PLATFORM_ID_QUIET},
    {EVT_TYPE_PLATFORM_RESTORE_FLAG_REQUEST, PLATFORM_ID_RESTORE_FLAG},
    {EVT_TYPE_PLATFORM_TIMEZONE_REQUEST, PLATFORM_ID_TIMEZONE},
    {EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_REQUEST, PLATFORM_ID_AIRPRINT_EN_ON_USB},
    {EVT_TYPE_PLATFORM_PORTLIMIT_USB_REQUEST, PLATFORM_ID_PORTLIMIT_USB},
    {EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_REQUEST, PLATFORM_ID_PRODUCT_SERIAL_NUM},
    {EVT_TYPE_PLATFORM_LOCATION_REQUEST, PLATFORM_ID_LOCATION},
    {EVT_TYPE_PLATFORM_PRODUCT_DATE_REQUEST, PLATFORM_ID_PRODUCT_DATE},
    {EVT_TYPE_PLATFORM_BATCH_NUMBER_REQUEST, PLATFORM_ID_BATCH_NUMBER},
    {EVT_TYPE_PLATFORM_CONTACT_INFO_REQUEST, PLATFORM_ID_CONTACT_INFO},
    {EVT_TYPE_PLATFORM_PROPERTY_NUMBER_REQUEST, PLATFORM_ID_PROPERTY_NUMBER},
    {EVT_TYPE_PLATFORM_STORAGE_CLEAR_REQUEST, PLATFORM_ID_STORAGE_CLEAR},
    {EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_REQUEST, PLATFORM_ID_DELAYCANCEL_TIMECOUNT},
    {EVT_TYPE_PLATFORM_IO_TIMEOUT_REQUEST, PLATFORM_ID_IO_TIMEOUT},
    {EVT_TYPE_PLATFORM_CALIBRATION_QRCODE_REQUEST, PLATFORM_ID_CALIBRATION_QRCODE},
    {EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_REQUEST, PLATFORM_ID_SYSTEM_ENERGY_CODE},
    {EVT_TYPE_PLATFORM_ACRFACTORY_REQUEST, PLATFORM_ID_ACRFACTORY},
    {EVT_TYPE_PLATFORM_CALIBRATION_REQUEST, PLATFORM_ID_CALIBRATION},
    {EVT_TYPE_PLATFORM_TR2MODESET_REQUEST, PLATFORM_ID_TR2MODESET},
    {EVT_TYPE_PLATFORM_MACHINE_TYPE_REQUEST, PLATFORM_ID_PLATFORM_PRODUCT_MODEL},
};

static const MACHINE_CONFIG_TAG_S s_manchine_config_tag[] ={
    /*7265 V1R2*/
    {MACHINE_TYPE_CM805ADN, "CM805ADN", "CM805ADN", PID_TYPE_CM805ADN},
    {MACHINE_TYPE_BM805ADN, "BM805ADN", "BM805ADN", PID_TYPE_BM805ADN},
    {MACHINE_TYPE_CM605ADN, "CM605ADN", "CM605ADN", PID_TYPE_CM605ADN},
    {MACHINE_TYPE_BM605ADN, "BM605ADN", "BM605ADN", PID_TYPE_BM605ADN},
    /*7265 V1R2*/
    /*Kanas V2R12*/
    {MACHINE_TYPE_CM9708DN, "CM9708DN", "CM9708DN", PID_TYPE_CM9708DN},
    {MACHINE_TYPE_CM9108DN, "CM9108DN", "CM9108DN", PID_TYPE_CM9108DN},
    {MACHINE_TYPE_M9708DN,  "M9708DN",  "M9708DN",  PID_TYPE_M9708DN},
    {MACHINE_TYPE_M9108DN,  "M9108DN",  "M9108DN",  PID_TYPE_M9108DN},
    {MACHINE_TYPE_CM9108DW, "CM9108DW", "CM9108DW", PID_TYPE_CM9108DW},
    {MACHINE_TYPE_M9108DW,  "M9108DW",  "M9108DW",  PID_TYPE_M9108DW},
    /*Kanas V2R12*/
    /*7265 V1R3 XC*/
    {MACHINE_TYPE_CM9705DN_PLUS, "CM9705DN Plus", "CM9705DN Plus", PID_TYPE_CM9705DN_PLUS},
    {MACHINE_TYPE_CM9105DN_PLUS, "CM9105DN Plus", "CM9105DN Plus", PID_TYPE_CM9105DN_PLUS},
    {MACHINE_TYPE_M9705DN_PLUS,  "M9705DN Plus",  "M9705DN Plus",  PID_TYPE_M9705DN_PLUS},
    {MACHINE_TYPE_M9105DN_PLUS,  "M9105DN Plus",  "M9105DN Plus",  PID_TYPE_M9105DN_PLUS},
    /*7265 V1R3 XC*/
    /*7265 ELS*/
    {MACHINE_TYPE_CM600ADN, "CM600ADN", "CM600ADN", PID_TYPE_CM600ADN},
    {MACHINE_TYPE_CM800ADN, "CM800ADN", "CM800ADN", PID_TYPE_CM800ADN},
    {MACHINE_TYPE_BM600ADN, "BM600ADN", "BM600ADN", PID_TYPE_BM600ADN},
    {MACHINE_TYPE_BM800ADN, "BM800ADN", "BM800ADN", PID_TYPE_BM800ADN},
    /*7265 ELS*/
};

static int32_t platform_countrycode_nvram(void);
int32_t platform_set_fw_upgrade_way(uint32_t data);
int32_t platform_get_panel_test_mode_support(uint32_t *value);

static uint32_t event_id_switch_nv_id(uint32_t event_id)
{
    if ( EVT_INDEX(event_id) >= EVT_INDEX(EVT_TYPE_PLATFORM_MAX) ) {
        pi_log_e("event_id(%08x) out of range!\n", event_id);
        return PLATFORM_ERR;
    }
    int32_t nv_id = -1;
    int32_t i;
    for (i = 0; i < (sizeof(s_eventid_to_nvid_table) / sizeof(s_eventid_to_nvid_table[0])); i++) {
        if (s_eventid_to_nvid_table[i].event_id == event_id) {
            nv_id = s_eventid_to_nvid_table[i].nv_id;
        }
    }
    return nv_id;
}

int32_t adjust_request_data_whether_notify(uint32_t data_len, void *new, void *old, uint32_t event_id)
{
    if ( new == NULL || old == NULL || data_len < 0 )
    {
        pi_log_e("data to request is err\n");
        return PLATFORM_ERR;
    }
    if  ( pi_memcmp(new, old, data_len) != 0 )
    {
        pi_memcpy(old, new, data_len);
        pi_log_d( "platform notify event: 0x%x data_len(%d)\n", event_id, data_len);
        pi_event_mgr_notify(g_cli_ptr, event_id, new, data_len);
    }
    else if ( data_len == 0 )
    {
        pi_log_d( "platform notify event: 0x%x data_len = 0\n", event_id);
        pi_event_mgr_notify(g_cli_ptr, event_id, new, data_len);
    }
    else
    {
        pi_log_d( "data not change\n");
        return PLATFORM_OK;
    }
    return PLATFORM_OK;
}

static MACHINE_CONFIG_S *platform_search_machine_config(uint32_t machine_type)
{
    int32_t i;
    for (i = 0; i < sizeof(s_machine_config_ary) / sizeof(s_machine_config_ary[0]); i++) {
        if (machine_type == s_machine_config_ary[i].machine_tag) {
            return &s_machine_config_ary[i];
        }
    }
    return NULL;
}

static int32_t platform_countrycode_nvram(void)
{
    int32_t ret;
    e_CountryCode countryCode = (e_CountryCode)s_plat_config_vars.firmware_country_code;
    e_RegionCode regionCode = REGION_AMERICAN;
    e_LanguageCode languageCode = (e_LanguageCode)s_plat_config_vars.system_language_code;

    // get the regionCode and languageCode according to countryCode.
    uint32_t numberOfCountry = sizeof(s_country_to_language)/sizeof(COUNTRYCODE_LANGUAGE_S);
    uint32_t i = 0;
    for (i = 0; i < numberOfCountry; i++)
    {
        if(s_country_to_language[i].countryCode == countryCode)
        {
            regionCode = s_country_to_language[i].regionCode;
            languageCode = s_country_to_language[i].languageCode;
            break;
        }
    }
    pi_log_d("platform nvram record:countryCode[%d] regionCode[%d] languageCode[%d]\n", countryCode, regionCode, languageCode);
    ret = pi_nvram_set(PLATFORM_ID_COUNTRY_CODE, VTYPE_UINT, (void*)(&countryCode), sizeof(countryCode), 0, NULL);
    if (N_SUCCESS != ret) {
        pi_log_e("ERROR :PLATFORM_ID_COUNTRY_CODE\n");
    }

    ret = pi_nvram_set(PLATFORM_ID_REGION_CODE, VTYPE_UINT, (void*)(&regionCode), sizeof(regionCode), 0, NULL);
    if (N_SUCCESS != ret) {
        pi_log_e("ERROR :PLATFORM_ID_REGION_CODE\n");
    }

    ret = pi_nvram_set(PLATFORM_ID_LANGUAGE_CODE, VTYPE_UINT, (void*)(&languageCode), sizeof(languageCode), 0, NULL);
    if (N_SUCCESS != ret) {
        pi_log_e("ERROR :PLATFORM_ID_LANGUAGE_CODE\n");
    }
    return PLATFORM_OK;
}

int32_t pi_platform_set_countrycode(uint16_t data)
{
    pi_log_d("pi_platform_set_countrycode(%d)", data);
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint16_t), (void *)&data, &s_plat_config_vars.firmware_country_code,
        EVT_TYPE_PLATFORM_COUNTRY_CODE_MODIFY);
    ret = platform_countrycode_nvram();
    return ret;
}

int32_t pi_platform_get_countrycode(uint16_t *data)
{
    *data = (uint16_t)(s_plat_config_vars.firmware_country_code);
    return PLATFORM_OK;
}

int32_t pi_platvars_restore_factory_default(uint32_t index)
{
    int32_t  e_res          = PLATFORM_OK;
    uint32_t restore_flag   = 0;
    uint32_t AirPrintEN     = 1;

    pi_log_d( "Begin to restore the factory default value, index = %d\n", index);

    if ( index == 0 )
    {
        pi_log_d( "restore factory default index:%d\n", index);
        return PLATFORM_OK;
    }
    else if ( index == 1 )
    {
        //call nv func to restore nv defualt value
        e_res = pi_nvram_recover_factory();
        if ( N_SUCCESS != e_res )
        {
            pi_log_e("NVRAM restore factory failed (ret:%d)!\n", e_res);
            return e_res;
        }

        //the defualt language  of panel  is determined by countrycode
        platform_countrycode_nvram();

        e_res = pi_nvram_set(PLATFORM_ID_AIRPRINT_EN_ON_USB, VTYPE_UINT, (void*)(&AirPrintEN), sizeof(AirPrintEN), 0, NULL);
        if ( N_SUCCESS != e_res )
        {
            pi_log_e("NVRAMOID_PLATFORM_AIRPRINT_EN_ON_USB set failed (ret:%d)!\n", e_res);
            return e_res;
        }

        // Remove specify joblist(Job Manager)
        job_manager_remove_specify_joblist(JOB_TYPE_PRINT_DELAY);
        job_manager_remove_specify_joblist(JOB_TYPE_PRINT_SAMPLE);
        job_manager_remove_specify_joblist(JOB_TYPE_PRINT_PINCODE);


        // set flag true, then save to nvram
        restore_flag = true;
        e_res = pi_nvram_set(PLATFORM_ID_RESTORE_FLAG, VTYPE_UINT, (void*)(&restore_flag), sizeof(restore_flag), 0, NULL);
        if ( N_SUCCESS != e_res )
        {
            pi_log_e("OID_PLATFORM_RESTORE_FLAG set failed (ret:%d)!\n", e_res);
            return e_res;
        }

        pi_msleep(1000);
        sync();
        // reboot(baikal unsupport soft-reboot)
        //pi_runcmd(NULL, 0, 0,"reboot -f");
        pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_RESTORE_FLAG_MODIFY, (void *)&restore_flag, sizeof(uint8_t));
        pi_log_d( "restore factory default OK without soft-reboot :%d\n", e_res);
    }
    else
    {
        pi_log_d( "restore factory default get other index:%d\n", index);
    }
    return PLATFORM_OK;
}

int32_t platvars_set_system_time(char *system_time)
{
    char cmd[NVLEN_SYSTEM_TIME] = "date -s ";
    if(pi_strlen(system_time) > SET_SYSTEM_TIME_LEN)
    {
        pi_log_e("the length of the system_time is too long!\n");
        return PLATFORM_ERR;
    }
    strcat(cmd, "\"");
    strcat(cmd, system_time);
    strcat(cmd, "\"");
    (void)pi_runcmd(NULL, 0, 0, cmd);
    (void)pi_runcmd(NULL, 0, 0, "hwclock -w");
    return PLATFORM_OK;
}

char cmd[NVLEN_SYSTEM_TIME] = {0};
int32_t platvars_get_system_time(char **system_time)
{

    time_t timep;
    struct tm *p_time;

    (void)time(&timep);
    p_time = localtime(&timep);

    pi_strncpy(cmd, asctime(p_time),  NVLEN_SYSTEM_TIME);
    *system_time = cmd;
    return PLATFORM_OK;
}


int32_t platvars_get_verdor_id(uint32_t *vendor_id)
{
    *vendor_id = (uint32_t)(DEFAULT_VENDOR_ID);
    return PLATFORM_OK;
}


int32_t pi_platform_set_serial_number(char *data, int32_t len)
{
    int32_t ret = 0;
    ret = adjust_request_data_whether_notify(len, (void *)data, (void *)s_plat_config_vars.product_serial_number,
        EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_MODIFY);

    if (ret < 0) {
        pi_log_e("platform set serial number err!\n");
        return PLATFORM_ERR;
    }
    pi_nvram_set(PLATFORM_ID_PRODUCT_SERIAL_NUM, VTYPE_STRING, (void *)data, len, 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_serial_number(char *data, int32_t len)
{
    pi_memcpy(data, s_plat_config_vars.product_serial_number, MIN(len, pi_strlen(s_plat_config_vars.product_serial_number)));

    return PLATFORM_OK;
}

int32_t pi_platform_set_machine_type(uint32_t machine_type)
{
    pi_log_d( "request platform machine type change: %d.\n", machine_type);
    MACHINE_CONFIG_S *machine_config = platform_search_machine_config(machine_type);

    if ( machine_config == NULL)
    {
        pi_log_e("platform can't find machine type: %d\n", machine_type);
        return PLATFORM_ERR;
    }

    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), (void *)&machine_type, &s_current_machine_config.machine_tag,
        EVT_TYPE_PLATFORM_MACHINE_TYPE_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set machine type err!\n");
        return PLATFORM_ERR;
    }
    pi_nvram_set(PLATFORM_ID_PLATFORM_PRODUCT_MODEL, VTYPE_UINT, (void *)&machine_type, sizeof(uint32_t), 0, NULL);

    //同步国家代码、区域代码和语言代码
    pi_memcpy(&s_current_machine_config, machine_config, sizeof(MACHINE_CONFIG_S));
    s_plat_config_vars.firmware_country_code = (uint16_t)s_current_machine_config.machine_country_code;
    pi_log_d("pi_platform_set_machine_type(%d|%d)", s_plat_config_vars.firmware_country_code, s_current_machine_config.machine_country_code);
    pi_platform_set_countrycode((uint16_t)s_current_machine_config.machine_country_code);

    return PLATFORM_OK;
}

int32_t pi_platform_get_machine_type(uint32_t *machine_type)
{
    if (machine_type == NULL) {
        pi_log_e("platform get error machine type.\n");
        return PLATFORM_ERR;
    }
    *machine_type = s_current_machine_config.machine_tag;
    pi_log_d( "get platform machine type: %d.\n", *machine_type);

    return PLATFORM_OK;
}

int32_t pi_platform_set_quiet(uint32_t data)
{
    int32_t ret = 0;
    ret = adjust_request_data_whether_notify(sizeof(uint32_t), (void *)&data, (void *)&s_plat_config_vars.quiet,
        EVT_TYPE_PLATFORM_QUIET_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set quiet err!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_QUIET, VTYPE_UINT, (void *)&data, sizeof(uint32_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_quiet(uint32_t *data)
{
    *data = s_plat_config_vars.quiet;
    return PLATFORM_OK;
}

int32_t pi_platvars_set_calibration_progress(uint32_t flag)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), &flag, &s_plat_config_vars.calibration,
        EVT_TYPE_PLATFORM_CALIBRATION_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set calibration err!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_CALIBRATION, VTYPE_UINT, (void *)&flag, sizeof(uint32_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_set_auto_shutdown_enable(uint32_t data)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), &data, &s_plat_config_vars.auto_shutdown_enable,
        EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set auto shutdown enable err!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_AUTO_SHUTDOWN_ENABLED, VTYPE_UINT, (void *)&data, sizeof(uint32_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_auto_shutdown_enable(uint32_t *data)
{
    *data = s_plat_config_vars.auto_shutdown_enable;
    return PLATFORM_OK;
}

int32_t pi_platform_set_auto_shutdown_time(uint32_t data)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), &data, &s_plat_config_vars.auto_shutdown_time,
        EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set auto shutdown enable err!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_AUTO_SHUTDOWN_TIME, VTYPE_UINT, (void *)&data, sizeof(uint32_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_auto_shutdown_time(uint32_t *data)
{
    *data = s_plat_config_vars.auto_shutdown_time;
    return PLATFORM_OK;
}

int32_t pi_platform_set_auto_shutdown_condition(uint32_t data)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), &data, &s_plat_config_vars.auto_shutdown_condition,
        EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set auto shutdown condition err!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_AUTO_SHUTDOWN_CONDITION, VTYPE_UINT, (void *)&data, sizeof(uint32_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_auto_shutdown_condition(uint32_t *data)
{
    *data = s_plat_config_vars.auto_shutdown_condition;
    return PLATFORM_OK;
}

int32_t pi_platform_get_firmware_version(char *data, int32_t len) {
    pi_memcpy(data, s_plat_config_vars.firmware_version, MIN(len, pi_strlen(s_plat_config_vars.firmware_version)));
    return PLATFORM_OK;
}

static int32_t platform_init_print_engfw_version(char *data, int32_t len)
{
    PRINT_CONFIG_ENGINE_INFO_S *pdata = (PRINT_CONFIG_ENGINE_INFO_S *)data;
    if (data == NULL || len <= 0 || pdata->eng_firmware_version[0] == 0)
    {
        pi_log_e("platform get print enginefw version err!\n");
        return PLATFORM_ERR;
    }
    pi_memset(&s_plat_config_vars.engine_version[0], 0, NVLEN_LVERSION);
    pi_memcpy(s_plat_config_vars.engine_version, pdata->eng_firmware_version, sizeof(pdata->eng_firmware_version));
    pi_log_d("VERINFO:print_engfw_version:%s\n", s_plat_config_vars.engine_version);
    pi_nvram_set(PLATFORM_ID_ENG_FW_VERSION, VTYPE_STRING, (void *)s_plat_config_vars.engine_version, pi_strlen(s_plat_config_vars.engine_version), 1, NULL);
    return PLATFORM_OK;
}

//print engine firmware
int32_t pi_platform_get_engine_firmware_version(char *data, int32_t len)
{
    pi_memcpy(data, s_plat_config_vars.engine_version, MIN(len, pi_strlen(s_plat_config_vars.engine_version)));
    return PLATFORM_OK;
}

int32_t pi_platform_get_panel_fw_version(char *data, int32_t len)
{
    pi_memcpy(data, s_plat_config_vars.panel_fw_version, MIN(len, pi_strlen(s_plat_config_vars.panel_fw_version)));
    return PLATFORM_OK;
}

static int32_t platform_init_panel_fw_version(char *data, int32_t len)
{
    if (data == NULL || len <= 0 || len > NVLEN_VERSION)
    {
        pi_log_e("platform set panel fw version err!\n");
        return PLATFORM_ERR;
    }
    pi_memset(&s_plat_config_vars.panel_fw_version[0], 0, NVLEN_VERSION);
    pi_memcpy(s_plat_config_vars.panel_fw_version, data, len);
    pi_log_d("VERINFO:panel_fw_version:%s\n", s_plat_config_vars.panel_fw_version);
    pi_nvram_set(PLATFORM_ID_PANEL_FW_VERSION, VTYPE_STRING, (void *)data, len, 1, NULL);
    return PLATFORM_OK;
}

static int32_t platform_init_scan_versions(char *data, int32_t len)
{
    char buff[NVLEN_LVERSION]={0};
    if (data == NULL || len <= 0)
    {
        pi_log_e("platform get scan version err!\n");
        return PLATFORM_ERR;
    }
    pi_memcpy(buff, data, NVLEN_LVERSION);
    pi_nvram_set(PLATFORM_ID_SCAN_ENG_FW_VERSION, VTYPE_STRING, (void *)buff, pi_strlen(buff), 1, NULL);
    pi_log_d("scan engine fw version:%s\n", buff);

    pi_memset(&buff[0], 0, NVLEN_LVERSION);
    pi_memcpy(buff, (data + NVLEN_LVERSION), NVLEN_LVERSION);
    pi_nvram_set(PLATFORM_ID_FPGA_FRONT_VERSION, VTYPE_STRING, (void *)buff, pi_strlen(buff), 1, NULL);
    pi_log_d("fpga front version:%s\n", buff);

    pi_memset(&buff[0], 0, NVLEN_LVERSION);
    pi_memcpy(buff, (data + NVLEN_LVERSION*2), NVLEN_LVERSION);
    pi_nvram_set(PLATFORM_ID_FPGA_BACK_VERSION, VTYPE_STRING, (void *)buff, pi_strlen(buff), 1, NULL);
    pi_log_d("fpga back version:%s\n", buff);

    pi_memset(&buff[0], 0, NVLEN_LVERSION);
    pi_memcpy(buff, (data + NVLEN_LVERSION*3), NVLEN_LVERSION);
    pi_nvram_set(PLATFORM_ID_M6220_FRONT_VERSION, VTYPE_STRING, (void *)buff, pi_strlen(buff), 1, NULL);
    pi_log_d("6220 front version:%s\n", buff);

    pi_memset(&buff[0], 0, NVLEN_LVERSION);
    pi_memcpy(buff, (data + NVLEN_LVERSION*4), NVLEN_LVERSION);
    pi_nvram_set(PLATFORM_ID_M6220_BACK_VERSION, VTYPE_STRING, (void *)buff, pi_strlen(buff), 1, NULL);
    pi_log_d("6220 back version:%s\n", buff);
    return PLATFORM_OK;
}

static int32_t platform_set_exit_tray_count(char *data, int32_t len)
{
    if ( data == NULL || len <= 0 || len != sizeof(PRINT_CONFIG_INSTALL_INFO_S) )
    {
        pi_log_e("invalid data from PRINT_CONFIG_INSTALL_INFO_S\n");
        return PLATFORM_ERR;
    }
    PRINT_CONFIG_INSTALL_INFO_S *pdata = (PRINT_CONFIG_INSTALL_INFO_S *)data;
    s_plat_config_vars.exit_tray_count = 0;

    if ( INFO_INSTALL == pdata->tray_1 )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    if ( INFO_INSTALL == pdata->tray_2 )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    if ( INFO_INSTALL == pdata->tray_3 )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    if ( INFO_INSTALL == pdata->tray_4 )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    if ( INFO_INSTALL == pdata->tray_lct_in )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    if ( INFO_INSTALL == pdata->tray_lct_out )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    if ( INFO_INSTALL == pdata->tray_multi )
    {
        s_plat_config_vars.exit_tray_count += 1;
    }
    return PLATFORM_OK;
}

int32_t pi_platform_get_exit_tray_count(uint32_t *data)
{
    if ( data == NULL )
    {
        pi_log_e("platform get exit tray count error.\n");
        return PLATFORM_ERR;
    }

    *data = (s_plat_config_vars.exit_tray_count);
    pi_log_d( "platform get exit tray count: %d.\n", *data);
    return PLATFORM_OK;
}

int32_t pi_platform_get_print_name_string(char *data, int32_t len) {
    pi_memcpy(data, s_current_machine_config.pdt_name, MIN(len, pi_strlen(s_current_machine_config.pdt_name)));

    return PLATFORM_OK;
}

int32_t pi_platform_set_system_time(uint32_t index, char *data, int32_t len)
{
    int32_t ret = 0;
    char sys_time[20]={0};

    if(len > 21 || data == NULL)
    {
        pi_log_e("set system time data err(%d)!\n", len);
        return PLATFORM_ERR;
    }
    pi_log_d("platform set system time(len:%d)\n", len);
    pi_strncpy(sys_time, data, len);
    //同步到tm
    struct tm time;
    int year =0, month = 0, day = 0, hour = 0, min = 0, sec = 0;

    if((ret=sscanf(sys_time, "%4d-%2d-%2d %2d:%2d:%2d", &year, &month, &day, &hour, &min, &sec)) != 6)
    {
        pi_log_e("platform set system time format err(%d)!\n", ret);
        return PLATFORM_ERR;
    }
    pi_log_d("platform set system time[%d-%d-%d %d:%d:%d] ret(%d)\n", year, month, day, hour, min, sec, ret);
    time.tm_year = year;
    time.tm_mon  = month;
    time.tm_mday = day;
    time.tm_hour = hour;
    time.tm_min  = min;
    time.tm_sec  = sec;
    pi_log_d("platform set time[%d-%d-%d %d-%d-%d] \n", time.tm_year, time.tm_mon, time.tm_mday, time.tm_hour, time.tm_min, time.tm_sec,ret);
    systime_set(&time);
    //事件推送,保存nv
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SYSTEM_TIME_SYNC, NULL, 0);
    pi_nvram_set(PLATFORM_ID_SYSTEM_TIME, VTYPE_STRING, (void *)data, len, 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_system_time(uint32_t index, char *data, int32_t len)
{
    struct tm   n_time;
    int32_t     ret;
    char        rtc_time[80] = {0};

    switch (index)
    {
    case 0://获取系统时间
        systime_get(&n_time);
        ret = pi_snprintf(data, (size_t)len, "%d-%d-%d %d:%d:%d", n_time.tm_year, n_time.tm_mon, n_time.tm_mday, n_time.tm_hour, n_time.tm_min, n_time.tm_sec);
        if ( ret >= len )
        {
            pi_log_w("The data buffer length(%d) is not enough\n", len);
        }
        pi_log_d("get system time(%s)\n", data);
        break;
    case 1://获取系统/RTC时间(RTC)
        systime_get(&n_time);
        strftime(rtc_time, sizeof(rtc_time), "%c", &n_time);
        pi_memcpy(data, rtc_time, MIN(len, pi_strlen(rtc_time)));
        pi_log_d("get system time(%s)\n", data);
        break;
    default:
        pi_log_d("unknown index(%d)\n", index);
        break;
    }
    return PLATFORM_OK;
}

int32_t pi_platform_set_product_date(char *data, int32_t len) {
    int32_t ret = 0;
    ret = adjust_request_data_whether_notify(len, (void *)data, (void *)s_plat_config_vars.product_date,
        EVT_TYPE_PLATFORM_PRODUCT_DATE_MODIFY);

    if (ret < 0) {
        pi_log_e("platform set product date err!\n");
        return PLATFORM_ERR;
    }
    pi_nvram_set(PLATFORM_ID_PRODUCT_DATE, VTYPE_STRING, (void *)data, len, 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_product_date(char *data, int32_t len) {
    pi_memcpy(data, s_plat_config_vars.product_date, MIN(len, pi_strlen(s_plat_config_vars.product_date)));

    return PLATFORM_OK;
}

int32_t pi_platform_get_kernel_version(char *data, int32_t len) {
    pi_memcpy(data, s_plat_config_vars.kernel_version, MIN(len, pi_strlen(s_plat_config_vars.kernel_version)));

    return PLATFORM_OK;
}

int32_t pi_platform_set_language_code(uint16_t data)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint16_t), &data, &s_plat_config_vars.system_language_code,
        EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set language code error!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_LANGUAGE_CODE, VTYPE_UINT, (void *)&data, sizeof(uint16_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_get_language_code(uint16_t *data)
{
    if (data == NULL) {
        pi_log_e("platform get language code error.\n");
        return PLATFORM_ERR;
    }

    *data = (s_plat_config_vars.system_language_code);
    pi_log_d( "get language code: %d.\n", *data);
    return PLATFORM_OK;
}



int32_t platform_get_panel_test_mode_support(uint32_t *value)
{
    if (value == NULL) {
        pi_log_e("platform get panel test mode support.\n");
        return PLATFORM_ERR;
    }
    *value = PANEL_TEST_MODE_DISPLAY | PANEL_TEST_MODE_KEY | PANEL_TEST_MODE_TOUCH_CLICK | PANEL_TEST_MODE_TOUCH_SWIPE;
    pi_log_d( "get platform panel test mode support: %#x.\n", *value);
    return PLATFORM_OK;
}

int32_t pi_platform_set_udisk_enable(uint32_t data)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), &data, &s_plat_config_vars.udisk_enable,
        EVT_TYPE_PLATFORM_UDISK_ENABLE_MODIFY);
    if (ret < 0) {
        pi_log_e("platform set udisk enable err!\n");
        return PLATFORM_ERR;
    }

    pi_nvram_set(PLATFORM_ID_UDISK_ENABLE, VTYPE_UINT, (void *)&data, sizeof(uint32_t), 0, NULL);

    return PLATFORM_OK;
}

int32_t pi_platform_set_uart_enable(uint32_t data)
{
 /** baikal unsupport control uart

    int32_t ret = engine_mgr_uart_input_switch((uint8_t)data);
    if (ret != 0) {
        pi_log_e("platform set uart enable[%d] err[%d]!\n", data, ret);
    }

    if (data == 1) {
        pi_hal_serial_input_enable();
    } else {
        pi_hal_serial_input_disabled();
    }
*/
    return PLATFORM_OK;
}

int32_t pi_platform_set_system_reboot(uint32_t data)
{
#if CONFIG_XC4_UPGRADE
    pi_log_d("platform set system sec_reboot !\n");
    secure_reboot();
#else
    if ( data > 0 )
    {
        pi_log_d("platform set system reboot ok!\n");
        pi_msleep(10);
        pi_runcmd(NULL, 0, 0,"reboot -f");
    }
    else
    {
        pi_log_e("platform set system reboot error: invalid value(%d)!\n", data);
        return PLATFORM_ERR;
    }
#endif

    return PLATFORM_OK;
}

int32_t  platform_set_fw_upgrade_way(uint32_t data)
{
    int32_t ret = adjust_request_data_whether_notify(sizeof(uint32_t), &data, &s_plat_config_vars.fw_upgrade_way,
        EVT_TYPE_PLATFORM_FW_UPGRADE_WAY);
    if (ret < 0) {
        pi_log_e("platform set fw_upgrade_way error!\n");
        return PLATFORM_ERR;
    }
    pi_log_d( "platform_set_fw_upgrade_way: get data %d\n", data);
    return PLATFORM_OK;
}

int32_t pi_platform_get_fw_upgrade_way(uint32_t *data)
{
    *data = (s_plat_config_vars.fw_upgrade_way);
    pi_log_d( "get fw_upgrade_way: %d.\n", s_plat_config_vars.fw_upgrade_way);
    return PLATFORM_OK;
}

int32_t pi_platform_get_location(char *data, int32_t len)
{
    pi_memcpy(data, s_plat_config_vars.printer_location, MIN(len, pi_strlen(s_plat_config_vars.printer_location)));
    return PLATFORM_OK;
}

int32_t pi_platform_get_fpga_ver(char *fpga_f, char *fpga_b)
{
    int32_t ret;

    ret = pi_nvram_get(PLATFORM_ID_FPGA_FRONT_VERSION, VTYPE_STRING, fpga_f, NVLEN_LVERSION);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_FPGA_FRONT_VERSION);

    ret = pi_nvram_get(PLATFORM_ID_FPGA_BACK_VERSION, VTYPE_STRING, fpga_b, NVLEN_LVERSION);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_FPGA_BACK_VERSION);

    return PLATFORM_OK;
}

int32_t pi_platform_set_panel_board_ssn(char *data, int32_t len)
{
    int32_t ret = 0;
    char set_data[PANEL_BOARD_SERIAL_NUMBER_LEN] = {0};


     if ( data == NULL || len != PANEL_BOARD_SERIAL_NUMBER_LEN )
    {
        pi_log_e("panel data to request is err\n");
        return PLATFORM_ERR;
    }

    ret = panel_set_ui_board_serial_num(data, len);
    if ( 0 == ret )
    {
        ret = panel_get_ui_board_serial_num(set_data, PANEL_BOARD_SERIAL_NUMBER_LEN);
        if ( 0 == ret )
        {
            pi_log_d( "platform notify event:EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY data{%s} data_len(%d)\n", set_data, len);
            pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY, set_data, len);
        }
    }

    return PLATFORM_OK;
}

int32_t pi_platform_get_panel_board_ssn(char *data, int32_t len)
{
    int32_t ret = PLATFORM_OK;

    ret = panel_get_ui_board_serial_num(data, len);
    pi_log_e("platform get panel board ssn data{%s}!\n", data);
    if ( 0 != ret )
    {
       ret = PLATFORM_ERR;
    }

    return ret;
}

int32_t pi_platform_get_config(MACHINE_CONFIG_S *config)
{
    if( config != NULL )
    {
        *config = s_current_machine_config;
        return PLATFORM_OK;
    }
    else
    {
        return PLATFORM_ERR;
    }
}

/************************************************************
func: platform_register_request_event
param: void
return: void
description: register all requested modification events to event manager
author: mazhenhuan
************************************************************/
static void platform_request_event_callback(const EVT_MSG_S* msg, void* ctx)
{
    uint32_t module_id = msg->module_id;
    uint32_t event_type = msg->event_type;
    int32_t need_to_save = NEED_TO_SAVE;
    VARIABLE_TYPE_E variable_type = VTYPE_UINT;
    uint32_t nv_id = 0;
    int32_t ret = PLATFORM_ERR;
    uint32_t u32data = 0;
    pi_log_d( "platform get module: %u request event type: %u\n", module_id, event_type);

    switch (event_type)
    {
        case EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_REQUEST:
            ret = pi_platform_set_auto_shutdown_enable(*(uint32_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_REQUEST:
            ret = pi_platform_set_auto_shutdown_time(*(uint32_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_REQUEST:
            ret = pi_platform_set_auto_shutdown_condition(*(uint32_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_POWER_ON_COUNT_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.power_on_count,
                EVT_TYPE_PLATFORM_POWER_ON_COUNT_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_VOLUME_SET_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.system_volume,
                EVT_TYPE_PLATFORM_VOLUME_SET_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_COUNTRY_CODE_REQUEST:
            pi_platform_set_countrycode(*(uint16_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_LANGUAGE_CODE_REQUEST:
            ret = adjust_request_data_whether_notify(sizeof(uint16_t), msg->data, &s_plat_config_vars.system_language_code,
                EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_REGION_CODE_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.firmware_region_code,
                EVT_TYPE_PLATFORM_REGION_CODE_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_QUIET_REQUEST:
            ret = pi_platform_set_quiet(*(uint32_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_RESTORE_FLAG_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.restore_flag,
                EVT_TYPE_PLATFORM_RESTORE_FLAG_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_TIMEZONE_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.timezone,
                EVT_TYPE_PLATFORM_TIMEZONE_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.AirPrintEn,
                EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_MODIFY);
            break;
#if IN_PORT_CTRL
        case EVT_TYPE_PLATFORM_PORTLIMIT_USB_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.port_limit_usb,
                EVT_TYPE_PLATFORM_PORTLIMIT_USB_MODIFY);
            break;
#endif
        case EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_REQUEST:
            ret = pi_platform_set_serial_number(msg->data, msg->data_length);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_LOCATION_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, s_plat_config_vars.printer_location,
                EVT_TYPE_PLATFORM_LOCATION_MODIFY);
            variable_type = VTYPE_STRING;
            break;
        case EVT_TYPE_PLATFORM_PRODUCT_DATE_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, s_plat_config_vars.product_date,
                EVT_TYPE_PLATFORM_PRODUCT_DATE_MODIFY);
            variable_type = VTYPE_STRING;
            break;
        case EVT_TYPE_PLATFORM_BATCH_NUMBER_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, s_plat_config_vars.batch_number,
                EVT_TYPE_PLATFORM_BATCH_NUMBER_MODIFY);
            variable_type = VTYPE_STRING;
            break;
        case EVT_TYPE_PLATFORM_CONTACT_INFO_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, s_plat_config_vars.contact_info,
                EVT_TYPE_PLATFORM_CONTACT_INFO_MODIFY);
            variable_type = VTYPE_STRING;
            break;
        case EVT_TYPE_PLATFORM_PROPERTY_NUMBER_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, s_plat_config_vars.property_number,
                EVT_TYPE_PLATFORM_PROPERTY_NUMBER_MODIFY);
            variable_type = VTYPE_STRING;
            break;
#if IN_PORT_CTRL
        case EVT_TYPE_PLATFORM_STORAGE_CLEAR_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.storage_clear,
                EVT_TYPE_PLATFORM_STORAGE_CLEAR_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.delay_cancel_time_count,
                EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_MODIFY);
            break;
#endif
        case EVT_TYPE_PLATFORM_IO_TIMEOUT_REQUEST:
            if ((*(uint32_t*)msg->data > MAX_IO_TIMEOUT) || (*(uint32_t*)msg->data < MIN_IO_TIMEOUT)) {
                pi_log_e("The timeout value is out of range!\n");
                return;
            }
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.io_timeout,
                EVT_TYPE_PLATFORM_IO_TIMEOUT_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_CALIBRATION_QRCODE_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, s_plat_config_vars.calibration_qrcode,
                EVT_TYPE_PLATFORM_CALIBRATION_QRCODE_MODIFY);
            variable_type = VTYPE_STRING;
            break;
        case EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.system_energy_code,
                EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_ACRFACTORY_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.AcrFactory,
                EVT_TYPE_PLATFORM_ACRFACTORY_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_CALIBRATION_REQUEST:
            ret = pi_platvars_set_calibration_progress(*(uint32_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_TR2MODESET_REQUEST:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.TR2ModeSet,
                EVT_TYPE_PLATFORM_TR2MODESET_MODIFY);
            break;
        case EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST:
            ret = pi_platvars_restore_factory_default(*(uint32_t*)msg->data);
            if (ret != PLATFORM_OK) {
                pi_log_e("platform restore factory default fail\n");
                return;
            }
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_MACHINE_TYPE_REQUEST:
            pi_platform_set_machine_type(*(uint32_t *)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_UDISK_ENABLE_REQUEST:
            ret = pi_platform_set_udisk_enable(*(uint32_t*)msg->data);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_FW_UPGRADE_WAY:
            ret = adjust_request_data_whether_notify(msg->data_length, msg->data, &s_plat_config_vars.fw_upgrade_way,
            EVT_TYPE_PLATFORM_FW_UPGRADE_WAY);
            need_to_save = NOT_TO_SAVE;
            break;
        case EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY:
            pi_log_d("VERINFO:platform get panelVER\n");
            ret = platform_init_panel_fw_version(msg->data, msg->data_length);
            break;
        case EVT_TYPE_PRINT_ENGINE_INFO:
            pi_log_d("VERINFO:platform get engfwVER\n");
            ret = platform_init_print_engfw_version(msg->data, msg->data_length);
            break;
        case EVT_TYPE_SCAN_ENGINE_VERSION_MODIFY:
            pi_log_d("VERINFO:platform get scanengfwVER\n");
            ret = platform_init_scan_versions(msg->data, msg->data_length);
            break;
        case EVT_TYPE_PRINT_INSTALL_INFO:
            platform_set_exit_tray_count(msg->data, msg->data_length);
            break;
        default:
            pi_log_e("unknown event type: %u\n", event_type);
            break;
    }
    /*save data to nvram*/
    if ((ret == PLATFORM_OK) && (need_to_save == NEED_TO_SAVE)) {
        nv_id = event_id_switch_nv_id(event_type);
        if (nv_id < 0) {
            pi_log_e("err nv id. event_type: %u\n", event_type);
            return;
        }
        pi_log_d("platform set nv_id:%d, data:%d\n", nv_id,*((uint32_t *)msg->data));
        pi_nvram_set(nv_id, variable_type, (void *)msg->data, msg->data_length, 1, NULL);


    }
}

/*///20230423 liangshiqin

static char *get_ini_key_string(char *key, FILE *fp)
{


    if (fp == NULL) {
        return NULL;
    }

    fseek(fp, 0, SEEK_END);
    int32_t size = ftell(fp);
    if (size <= 0) {
        pi_log_e("file size null return\n");
        return NULL;
    }
    fseek(fp, 0,SEEK_SET);
    static char buff[LINE_SIZE] = {0};
    char *p = NULL;
    int32_t i;
    while(fgets(buff, LINE_SIZE, fp) != NULL) {
        if ((pi_strncmp("//", buff, 2) == 0) || (pi_strncmp(";", buff, 1) == 0)) {
            continue;
        }

        p = strchr(buff, '=');
        if (p != NULL) {
            i = 0;
            if (0 == pi_strncmp(key, buff, p - 1 - buff)) {
                while (buff[pi_strlen(buff) - i] != ';') {
                    i++;
                }
                buff[pi_strlen(buff) - i] = '\0';
                return p + 1;
            }
        } else {
            continue;
        }
    }
    return NULL;
}
*/

static void get_all_config(FILE *fp)
{
    fseek(fp, 0, SEEK_END);
    int size = ftell(fp);
    if (size <= 0) {
        printf("file size null return\n");
        return;
    }
    printf("file size:%d\n", size);
    fseek(fp, 0,SEEK_SET);
    static char buff[LINE_SIZE] = {0};

    char *p;
    int i, index = -1;
    while(fgets(buff, LINE_SIZE, fp)!= NULL) {
        if ((strncmp("[", buff, 1) == 0)) {
            index++;
            continue;
        } else {
            if ((strncmp("//", buff, 2) == 0) || (strncmp(";", buff, 1) == 0)) {
                continue;
            }

            p = strchr(buff, '=');
            if (p != NULL) {
                i = 0;
                if (0 == strncmp("machine_tag", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].machine_tag = pi_strtoul(p + 2, NULL, 16);
                    pi_log_d("ini get[%d]machine_tag:%x\n", index, s_machine_config_ary[index].machine_tag);

                }
                if (0 == strncmp("mfg_name", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    pi_strncpy(s_machine_config_ary[index].mfg_name, p + 2, MFG_NAME_LEN);
                    pi_log_d("ini get mfg_name:%s\n", s_machine_config_ary[index].mfg_name);

                }
                if (0 == strncmp("series_name", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    pi_strncpy(s_machine_config_ary[index].series_name, p + 2, SERIES_NAME_LEN);
                    pi_log_d("ini get series_name:%s\n", s_machine_config_ary[index].series_name);
                }

                if (0 == strncmp("pdt_name", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    pi_strncpy(s_machine_config_ary[index].pdt_name, p + 2, PDT_NAME_LEN);
                    pi_log_d("ini get pdt_name:%s\n", s_machine_config_ary[index].pdt_name);
                }
                if (0 == strncmp("pid", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].pid =(uint16_t)pi_strtoul(p + 2, NULL, 16);
                    pi_log_d("ini get pid:%x\n", s_machine_config_ary[index].pid);
                }
                if (0 == strncmp("speed", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].speed = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get speed:%d\n", s_machine_config_ary[index].speed);
                }
#if 0
                if (0 == strncmp("color", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].color = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get color:%d\n", s_machine_config_ary[index].color);
                }
#endif
#if CONFIG_COLOR
                s_machine_config_ary[index].color = 1;
#else
                s_machine_config_ary[index].color = 0;
#endif
                pi_log_d("ini get color:%d\n", s_machine_config_ary[index].color);
                if (0 == strncmp("duplex_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].duplex_enable = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get duplex_enable:%d\n", s_machine_config_ary[index].duplex_enable);
                }
#if 0
                if (0 == strncmp("wired_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].wired_enable = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get wired_enable:%d\n", s_machine_config_ary[index].wired_enable);
                }
                if (0 == strncmp("wireless_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].wireless_enable = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    if(s_machine_config_ary[index].wireless_enable == OPTION)
                    {
                        void *handle = NULL;
                        if ( pi_hal_wifi_request(&handle, 0 ) == 0 )
                        {
                            s_machine_config_ary[index].wireless_enable = 1;
                            pi_hal_wifi_free(&handle);
                        }
                        else
                        {
                            s_machine_config_ary[index].wireless_enable = 0;

                        }
                    }
                    pi_log_d("ini get wireless_enable:%d\n", s_machine_config_ary[index].wireless_enable);
                }
#endif
#if CONFIG_NET_WIRED
                s_machine_config_ary[index].wired_enable = 1;
#else
                s_machine_config_ary[index].wired_enable = 0;
#endif
                pi_log_d("ini get wired_enable:%d\n", s_machine_config_ary[index].wired_enable);
#if CONFIG_NET_WIFI
                s_machine_config_ary[index].wireless_enable = 1;
#else
                s_machine_config_ary[index].wireless_enable = 0;
#endif
                pi_log_d("ini get wireless_enable:%d\n", s_machine_config_ary[index].wireless_enable);
#if 0
                if (0 == strncmp("print_language", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].print_language = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    if (PRINT_LANGUAGE_GDI == s_machine_config_ary[index].print_language) {
                        pi_log_d("ini get print_language: GDI\n");
                    } else if (PRINT_LANGUAGE_IPS == s_machine_config_ary[index].print_language) {
                        pi_log_d("ini get print_language: IPS\n");
                    } else {
                        pi_log_d("ini get print_language: NULL\n");
                    }

                }
#endif

#if CONFIG_IPS
                s_machine_config_ary[index].print_language = PRINT_LANGUAGE_IPS;
                pi_log_d("ini get print_language: IPS\n");
#elif CONFIG_GDI
                s_machine_config_ary[index].print_language = PRINT_LANGUAGE_GDI;
                pi_log_d("ini get print_language: GDI\n");
#else
                s_machine_config_ary[index].print_language = PRINT_LANGUAGE_NULL;
                pi_log_d("ini get print_language: NULL\n");
#endif /* CONFIG_IPS */

                if (0 == strncmp("screen_type", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].screen_type = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get screen_type:%d\n", s_machine_config_ary[index].screen_type);
                }
                if (0 == strncmp("expand_tray_number", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].expand_tray_number  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get expand_tray_number:%d\n", s_machine_config_ary[index].expand_tray_number);
                }

                if (0 == strncmp("usbhttp_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].usbhttp_enable  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get usbhttp_enable:%d\n", s_machine_config_ary[index].usbhttp_enable);
                }
#if 0
                if (0 == strncmp("print_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].print_enable  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get print_enable:%d\n", s_machine_config_ary[index].print_enable);
                }

                if (0 == strncmp("scan_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].scan_enable   = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get scan_enable:%d\n", s_machine_config_ary[index].scan_enable);
                }

                if (0 == strncmp("copy_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].copy_enable  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get copy_enable:%d\n", s_machine_config_ary[index].copy_enable);
                }

                if (0 == strncmp("fax_enable", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].fax_enable  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get fax_enable:%d\n", s_machine_config_ary[index].fax_enable);
                }
#endif

#if CONFIG_PRINT
                s_machine_config_ary[index].print_enable = 1;
#else
                s_machine_config_ary[index].print_enable = 0;
#endif /* CONFIG_PRINT */
                pi_log_d("ini get print_enable:%d\n", s_machine_config_ary[index].print_enable);
#if CONFIG_SCAN
                s_machine_config_ary[index].scan_enable = 1;
#else
                s_machine_config_ary[index].scan_enable = 0;
#endif /* CONFIG_SCAN */
                pi_log_d("ini get scan_enable:%d\n", s_machine_config_ary[index].scan_enable);
#if CONFIG_COPY
                s_machine_config_ary[index].copy_enable = 1;
#else
                s_machine_config_ary[index].copy_enable = 0;
#endif /* CONFIG_COPY */
                pi_log_d("ini get copy_enable:%d\n", s_machine_config_ary[index].copy_enable);
#if CONFIG_FAX
                s_machine_config_ary[index].fax_enable = 1;
#else
                s_machine_config_ary[index].fax_enable = 0;
#endif /* CONFIG_FAX */
                pi_log_d("ini get copy_enable:%d\n", s_machine_config_ary[index].copy_enable);
                if (0 == strncmp("scan_type", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].scan_type  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get scan_type:%d\n", s_machine_config_ary[index].scan_type);
                }
                if (0 == strncmp("print_rk_mode", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].print_rk_mode  = (uint8_t)pi_strtoul(p + 2, NULL, 0);
                    pi_log_d("ini get print_rk_mode:%d\n", s_machine_config_ary[index].print_rk_mode);
                }
                if (0 == strncmp("country_code", buff, p - 1 - buff)) {
                    while (buff[strlen(buff) -i] != ';') {
                        i++;
                    }
                    buff[strlen(buff) - i] = '\0';
                    s_machine_config_ary[index].machine_country_code  = (uint8_t)pi_strtoul(p + 2, NULL, 0);

                    pi_log_d("ini get (index:%d)country_code:%d\n", index, s_machine_config_ary[index].print_rk_mode);
                }
            } else {
                continue;
            }
        }

    }
}

int32_t platform_read_machine_info_from_file()
{
    FILE *fp = NULL;

    fp = pi_fopen(MACHINE_PATH, "r");
    if (fp == NULL) {
        return PLATFORM_ERR;
    }
    get_all_config(fp);

    pi_fclose(fp);
    /*int32_t ret = remove(MACHINE_PATH);
    if (ret < 0) {
        pi_log_e("remove %s fail\n", MACHINE_PATH);
        return PLATFORM_ERR;
    }*/
    pi_log_d( "read all machine config end\n");
    return PLATFORM_OK;
}


int32_t platform_read_machine_info_from_param()
{
    return PLATFORM_OK;
}

static void platform_decode_ini_file()
{
    FILE *r_fp = NULL;
    FILE *w_fp = NULL;

    r_fp = pi_fopen(EN_MACHINE_PATH, "r");
    if (r_fp == NULL) {
        pi_log_e("open file err %s\n", EN_MACHINE_PATH);
        return;
    }

    w_fp = pi_fopen(MACHINE_PATH, "w+");
    if (w_fp == NULL) {
        pi_log_e("open output file err %s\n", MACHINE_PATH);
        pi_fclose(r_fp);
        return;
    }
    char enkey;
    int index = 0;
    char rbuff;
    int keylen = pi_strlen(decode_key);
    while(!feof(r_fp)) {
        pi_fread(&rbuff, 1, 1, r_fp);
        if (!feof(r_fp)) {
            enkey = decode_key[index % keylen];
            rbuff = rbuff ^ enkey;
            pi_fwrite(&rbuff, 1, 1, w_fp);
            index++;
        }
    }
    pi_fclose(r_fp);
    pi_fclose(w_fp);
}

void platform_init_machine_config()
{
    int32_t ret = 0;

    if (access(EN_MACHINE_PATH, F_OK) == 0) {
        pi_log_d( "read all machine config from ini\n");
        platform_decode_ini_file();
        ret = platform_read_machine_info_from_file();
    } else {
        pi_log_d( "read all machine config from param\n");
        ret = platform_read_machine_info_from_param();
    }
    if (ret < 0) {
        pi_log_e("platform read machine info fail\n");
    }
}

int32_t platform_init_current_machine_config(uint32_t machine_type)
{

    MACHINE_CONFIG_S *machine_config = platform_search_machine_config(machine_type);
    if (machine_config == NULL) {
        pi_log_e("fail to find %x machine configs.\n", machine_type);
        return PLATFORM_ERR;
    }

    pi_log_d( "find %x machine configs.\n", machine_type);
    pi_memcpy(&s_current_machine_config, machine_config, sizeof(MACHINE_CONFIG_S));
    //同步国家代码、区域代码和语言代码
    s_plat_config_vars.firmware_country_code = (uint16_t)s_current_machine_config.machine_country_code;
    pi_log_d("platform_init_current_machine_config(%d|%d)", s_plat_config_vars.firmware_country_code, s_current_machine_config.machine_country_code);
    pi_platform_set_countrycode((uint16_t)s_current_machine_config.machine_country_code);

    return PLATFORM_OK;
}

int32_t platform_init_config_from_nv(void)
{
    uint32_t nvram_vlaue = 0;
    int32_t ret = 0;
    ret = pi_nvram_get(PLATFORM_ID_PLATFORM_PRODUCT_MODEL, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    if ( ret < 0 || nvram_vlaue == 0 )
    {
        nvram_vlaue = s_machine_config_ary[0].machine_tag;
        pi_nvram_set(PLATFORM_ID_PLATFORM_PRODUCT_MODEL, VTYPE_UINT, (void *)(&nvram_vlaue), sizeof(uint32_t), 0, NULL);
    }

    pi_log_i( "platform get product module-machine type:0x%x.\n", nvram_vlaue);

    /*check machine type in range*/
    ret = platform_init_current_machine_config(nvram_vlaue);
    if (ret < 0) {
        pi_log_e("platform init current machine configs fail.\n");
        return PLATFORM_ERR;
    }

    ret = pi_nvram_get(PLATFORM_ID_AUTO_SHUTDOWN_ENABLED, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_AUTO_SHUTDOWN_ENABLED);
    s_plat_config_vars.auto_shutdown_enable = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_AUTO_SHUTDOWN_TIME, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_AUTO_SHUTDOWN_TIME);
    s_plat_config_vars.auto_shutdown_time = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_AUTO_SHUTDOWN_CONDITION, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_AUTO_SHUTDOWN_CONDITION);
    s_plat_config_vars.auto_shutdown_condition = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_POWER_ON_COUNT, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_POWER_ON_COUNT);
    s_plat_config_vars.power_on_count = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_VOLUME_SET, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_VOLUME_SET);
    s_plat_config_vars.system_volume = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_COUNTRY_CODE, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_COUNTRY_CODE);
    s_plat_config_vars.firmware_country_code = (uint16_t)nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_LANGUAGE_CODE, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_LANGUAGE_CODE);
    s_plat_config_vars.system_language_code = (uint16_t)nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_REGION_CODE, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_REGION_CODE);
    s_plat_config_vars.firmware_region_code = (uint16_t)nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_QUIET, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_QUIET);
    s_plat_config_vars.quiet = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_RESTORE_FLAG, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_RESTORE_FLAG);
    s_plat_config_vars.restore_flag = (uint8_t)nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_TIMEZONE, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_TIMEZONE);
    s_plat_config_vars.timezone = (uint8_t)nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_AIRPRINT_EN_ON_USB, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_AIRPRINT_EN_ON_USB);
    s_plat_config_vars.AirPrintEn = nvram_vlaue;

#if IN_PORT_CTRL
    ret = pi_nvram_get(PLATFORM_ID_PORTLIMIT_USB, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_PORTLIMIT_USB);
    s_plat_config_vars.port_limit_usb = nvram_vlaue;
#endif

    ret = pi_nvram_get(PLATFORM_ID_PRODUCT_SERIAL_NUM, VTYPE_STRING, s_plat_config_vars.product_serial_number,
                       NVLEN_PROD_SER_NUM);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_PRODUCT_SERIAL_NUM);
    pi_log_d( "platform get product_serial_number from nv: %s.\n", s_plat_config_vars.product_serial_number);

    ret = pi_nvram_get(PLATFORM_ID_LOCATION, VTYPE_STRING, s_plat_config_vars.printer_location,
                       NVLEN_PRINTER_LOCATION);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_LOCATION);

    ret = pi_nvram_get(PLATFORM_ID_PRODUCT_DATE, VTYPE_STRING, s_plat_config_vars.product_date,
                       NVLEN_PRODUCT_INFO);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_PRODUCT_DATE);

    ret = pi_nvram_get(PLATFORM_ID_BATCH_NUMBER, VTYPE_STRING, s_plat_config_vars.batch_number,
                       NVLEN_PRODUCT_INFO);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_BATCH_NUMBER);

    ret = pi_nvram_get(PLATFORM_ID_CONTACT_INFO, VTYPE_STRING, s_plat_config_vars.contact_info,
                       NVLEN_CONTACT_INFO);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_CONTACT_INFO);

    ret = pi_nvram_get(PLATFORM_ID_PROPERTY_NUMBER, VTYPE_STRING, s_plat_config_vars.property_number,
                       NVLEN_CONTACT_INFO);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_PROPERTY_NUMBER);
#if IN_PORT_CTRL
    ret = pi_nvram_get(PLATFORM_ID_STORAGE_CLEAR, VTYPE_UINT,  &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_STORAGE_CLEAR);
    s_plat_config_vars.storage_clear = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_DELAYCANCEL_TIMECOUNT, VTYPE_UINT,  &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_DELAYCANCEL_TIMECOUNT);
    s_plat_config_vars.delay_cancel_time_count = nvram_vlaue;
#endif
    ret = pi_nvram_get(PLATFORM_ID_IO_TIMEOUT, VTYPE_UINT,  &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_IO_TIMEOUT);
    s_plat_config_vars.io_timeout = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_CALIBRATION_QRCODE, VTYPE_STRING,  s_plat_config_vars.calibration_qrcode, NVLEN_CONTACT_INFO);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_CALIBRATION_QRCODE);

    ret = pi_nvram_get(PLATFORM_ID_SYSTEM_ENERGY_CODE, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_SYSTEM_ENERGY_CODE);
    s_plat_config_vars.system_energy_code = (uint16_t)nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_ACRFACTORY, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_ACRFACTORY);
    s_plat_config_vars.AcrFactory = nvram_vlaue; /* ENG_ACR_WAY_ALL modify by kuangmh -- nvram_vlaue */

    ret = pi_nvram_get(PLATFORM_ID_CALIBRATION, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_CALIBRATION);
    s_plat_config_vars.calibration = nvram_vlaue; /* ENG_ACR_WAY_ALL modify by kuangmh -- nvram_vlaue */

    ret = pi_nvram_get(PLATFORM_ID_TR2MODESET, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_TR2MODESET);
    s_plat_config_vars.TR2ModeSet = nvram_vlaue;

    ret = pi_nvram_get(PLATFORM_ID_SYSTEM_TIME, VTYPE_STRING, s_plat_config_vars.system_time, SET_SYSTEM_TIME_LEN);
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_SYSTEM_TIME);

    ret = pi_nvram_get(PLATFORM_ID_UDISK_ENABLE, VTYPE_UINT, &nvram_vlaue, sizeof(nvram_vlaue));
    CHECK_GET_NVRAM_RESULT(ret, PLATFORM_ID_UDISK_ENABLE);
    s_plat_config_vars.udisk_enable = nvram_vlaue;

    return PLATFORM_OK;
}

void platform_notify_defualt_data()
{
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_MODIFY,
                        (void *)&s_plat_config_vars.auto_shutdown_enable, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_MODIFY,
                        (void *)&s_plat_config_vars.auto_shutdown_time, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_MODIFY,
                        (void *)&s_plat_config_vars.auto_shutdown_condition, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_POWER_ON_COUNT_MODIFY,
                        (void *)&s_plat_config_vars.power_on_count, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_VOLUME_SET_MODIFY,
                        (void *)&s_plat_config_vars.system_volume, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_COUNTRY_CODE_MODIFY,
                        (void *)&s_plat_config_vars.firmware_country_code, sizeof(uint16_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY,
                        (void *)&s_plat_config_vars.system_language_code, sizeof(uint16_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_REGION_CODE_MODIFY,
                        (void *)&s_plat_config_vars.firmware_region_code, sizeof(uint16_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_QUIET_MODIFY,
                        (void *)&s_plat_config_vars.quiet, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_TIMEZONE_MODIFY,
                        (void *)&s_plat_config_vars.timezone, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_MODIFY,
                        (void *)&s_plat_config_vars.AirPrintEn, sizeof(uint32_t));
#if IN_PORT_CTRL
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PORTLIMIT_USB_MODIFY,
                        (void *)&s_plat_config_vars.port_limit_usb, sizeof(uint32_t));
#endif
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_MODIFY,
                        (void *)s_plat_config_vars.product_serial_number, NVLEN_PROD_SER_NUM);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_LOCATION_MODIFY,
                        (void *)s_plat_config_vars.printer_location, NVLEN_PRINTER_LOCATION);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRODUCT_DATE_MODIFY,
                        (void *)s_plat_config_vars.product_date, NVLEN_PRODUCT_INFO);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_BATCH_NUMBER_MODIFY,
                        (void *)s_plat_config_vars.batch_number, NVLEN_PRODUCT_INFO);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_CONTACT_INFO_MODIFY,
                        (void *)s_plat_config_vars.contact_info, NVLEN_CONTACT_INFO);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PROPERTY_NUMBER_MODIFY,
                        (void *)s_plat_config_vars.property_number, NVLEN_CONTACT_INFO);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_MACHINE_TYPE_MODIFY,
                        (void *)&s_current_machine_config.machine_tag, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_MFG_NAME_MODIFY,
                        (void *)s_current_machine_config.mfg_name,  MFG_NAME_LEN);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SERIES_NAME_MODIFY,
                        (void *)s_current_machine_config.series_name,  SERIES_NAME_LEN);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PDT_NAME_MODIFY,
                        (void *)s_current_machine_config.pdt_name,  PDT_NAME_LEN);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PID_MODIFY,
                        (void *)&s_current_machine_config.pid, sizeof(uint16_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SPEED_MODIFY,
                        (void *)&s_current_machine_config.speed, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_COLOR_MODIFY,
                        (void *)&s_current_machine_config.color, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_DUPLEX_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.duplex_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_WIRED_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.wired_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_WIRELESS_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.wireless_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRINT_LANGUAGE_MODIFY,
                        (void *)&s_current_machine_config.print_language, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SCREEN_TYPE_MODIFY,
                        (void *)&s_current_machine_config.screen_type, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_EXPAND_TRAY_NUMBER_MODIFY,
                        (void *)&s_current_machine_config.expand_tray_number, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_USBHTTP_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.usbhttp_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRINT_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.print_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SCAN_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.scan_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_COPY_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.copy_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_FAX_ENABLE_MODIFY,
                        (void *)&s_current_machine_config.fax_enable, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SCAN_TYPE_MODIFY,
                        (void *)&s_current_machine_config.scan_type, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRINT_RK_MODE_MODIFY,
                        (void *)&s_current_machine_config.print_rk_mode, sizeof(uint8_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_KERNEL_VERSION_MODIFY,
                        (void *)s_plat_config_vars.kernel_version, NVLEN_VERSION);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_ROOTFS_VERSION_MODIFY,
                        (void *)s_plat_config_vars.rootfs_version, NVLEN_VERSION);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_FIRMWARE_VERSION_MODIFY,
                        (void *)s_plat_config_vars.firmware_version, NVLEN_VERSION);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRODUCT_1284_STRING_MODIFY,
                        (void *)s_plat_config_vars.product_1284_string, NVLEN_1284_STRING);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PRINTER_CID_STRING_MODIFY,
                        (void *)s_plat_config_vars.printer_cid_string, NVLEN_CID_STR);
#if IN_PORT_CTRL
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_STORAGE_CLEAR_MODIFY,
                        (void *)&s_plat_config_vars.storage_clear, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_MODIFY,
                        (void *)&s_plat_config_vars.delay_cancel_time_count, sizeof(uint32_t));
#endif
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_IO_TIMEOUT_MODIFY,
                        (void *)&s_plat_config_vars.io_timeout, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_UNIQUE_NAME_MODIFY,
                        (void *)s_plat_config_vars.unique_name, NVLEN_UNIQUE_NAME);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_MODIFY,
                        (void *)&s_plat_config_vars.system_energy_code, sizeof(uint16_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_ACRFACTORY_MODIFY,
                        (void *)&s_plat_config_vars.AcrFactory, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_CALIBRATION_MODIFY,
                        (void *)&s_plat_config_vars.calibration, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_VID_MODIFY,
                        (void *)&s_plat_config_vars.vid, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_FAX_1284_STRING_MODIFY,
                        (void *)s_plat_config_vars.product_fax_1284_string, NVLEN_1284_STRING);
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_TR2MODESET_MODIFY,
                        (void *)&s_plat_config_vars.TR2ModeSet, sizeof(uint32_t));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_UDISK_ENABLE_MODIFY,
                        (void *)&s_plat_config_vars.udisk_enable, sizeof(uint32_t));

}

EVT_MGR_CLI_S * platform_event_register()
{
    EVT_MGR_CLI_S * cli_ptr = pi_event_mgr_create_client(EVT_MODULE_PLATFORM, platform_request_event_callback, NULL, NULL);
    if (cli_ptr == NULL) {
        return NULL;
    }

    uint32_t modify_event_array[] = {
        EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_ENABLED_REQUEST,
        EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_TIME_REQUEST,
        EVT_TYPE_PLATFORM_AUTO_SHUTDOWN_CONDITION_REQUEST,
        EVT_TYPE_PLATFORM_POWER_ON_COUNT_REQUEST,
        EVT_TYPE_PLATFORM_VOLUME_SET_REQUEST,
        EVT_TYPE_PLATFORM_COUNTRY_CODE_REQUEST,
        EVT_TYPE_PLATFORM_LANGUAGE_CODE_REQUEST,
        EVT_TYPE_PLATFORM_REGION_CODE_REQUEST,
        EVT_TYPE_PLATFORM_QUIET_REQUEST,
        EVT_TYPE_PLATFORM_RESTORE_FLAG_REQUEST,
        EVT_TYPE_PLATFORM_TIMEZONE_REQUEST,
        EVT_TYPE_PLATFORM_DATE_FORMAT_REQUEST,
        EVT_TYPE_PLATFORM_TIME_FORMAT_REQUEST,
        EVT_TYPE_PLATFORM_AIRPRINT_EN_ON_USB_REQUEST,
        EVT_TYPE_PLATFORM_PORTLIMIT_USB_REQUEST,
        EVT_TYPE_PLATFORM_PRODUCT_SERIAL_NUM_REQUEST,
        EVT_TYPE_PLATFORM_LOCATION_REQUEST,
        EVT_TYPE_PLATFORM_PRODUCT_DATE_REQUEST,
        EVT_TYPE_PLATFORM_BATCH_NUMBER_REQUEST,
        EVT_TYPE_PLATFORM_CONTACT_INFO_REQUEST,
        EVT_TYPE_PLATFORM_PROPERTY_NUMBER_REQUEST,
        EVT_TYPE_PLATFORM_STORAGE_CLEAR_REQUEST,
        EVT_TYPE_PLATFORM_DELAYCANCEL_TIMECOUNT_REQUEST,
        EVT_TYPE_PLATFORM_IO_TIMEOUT_REQUEST,
        EVT_TYPE_PLATFORM_CALIBRATION_QRCODE_REQUEST,
        EVT_TYPE_PLATFORM_SYSTEM_ENERGY_CODE_REQUEST,
        EVT_TYPE_PLATFORM_ACRFACTORY_REQUEST,
        EVT_TYPE_PLATFORM_CALIBRATION_REQUEST,
        EVT_TYPE_PLATFORM_TR2MODESET_REQUEST,
        EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST,
        EVT_TYPE_PLATFORM_MACHINE_TYPE_REQUEST,
        EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY,
        EVT_TYPE_PRINT_ENGINE_INFO,
        EVT_TYPE_SCAN_ENGINE_VERSION_MODIFY,
        EVT_TYPE_PRINT_INSTALL_INFO,
    };

    int32_t event_cuont = sizeof(modify_event_array) / sizeof(modify_event_array[0]);

    pi_event_mgr_register(cli_ptr, modify_event_array, event_cuont);

    return cli_ptr;
}

static void platform_init_version(void)
{
    /* boot_version read from BIOS */
    //strcpy(s_current_machine_config.boot_version, buildversion);

    /* kernel_version read from /proc/version */
    //char* pstr = NULL;
    char buf[100];
    pi_memset(buf, 0, 100);
    int32_t len, i;
    size_t read_size;
    int32_t fd = pi_open("/proc/version", O_RDONLY);
    if (fd >= 0)
    {
        read_size = (size_t)pi_read(fd, buf, 100);
        len = read_size - 7;
        if (len >= 0)
        {
            for(i = 0; i < len; i++)
            {
                if (buf[i] == 'P' && buf[i + 1] == 'a' && buf[i + 2] == 'n' && buf[i + 3] == 't' &&
                    buf[i + 4] == 'u' && buf[i + 5] == 'm' && buf[i + 6] == '-')
                {
                    pi_strncpy(s_plat_config_vars.kernel_version, buf + i + 7, 7);
                    break;
                }
            }
        }
        pi_close(fd);
    }

    /*  rootfs_version read from /etc/firm_version */
    pi_memset(buf, 0, 100);
    fd = pi_open("/etc/firm_version", O_RDONLY);
    if (fd >= 0)
    {
        read_size = (size_t)pi_read(fd, buf, 100);
        buf[pi_strlen(buf) - 1] = '\0';
        pi_log_d( "/etc/firm_version:(%s)\n", buf);
        pi_close(fd);
    }
    /* rootfs_version is also firmware_version */
    pi_strncpy(s_plat_config_vars.firmware_version, buf, NVLEN_VERSION);
    pi_strncpy(s_plat_config_vars.rootfs_version, buf, NVLEN_VERSION);
    pi_nvram_set(PLATFORM_ID_FIRMWARE_VERSION, VTYPE_STRING, (void *)s_plat_config_vars.firmware_version, pi_strlen(s_plat_config_vars.firmware_version), 1, NULL);
    pi_close(fd);
}

static void platform_init_1284_string()
{
    char *cmd = NULL;
    char *cls = "PRINTER";
    uint8_t print_language = s_current_machine_config.print_language;
    char* pdt_name = "";

    uint32_t numberOfMachinetag = sizeof(s_manchine_config_tag)/sizeof(MACHINE_CONFIG_TAG_S);
    uint32_t i = 0;
    for (i = 0; i < numberOfMachinetag; i++)
    {
        if( (s_manchine_config_tag[i].machine_type == s_current_machine_config.machine_tag) && (s_manchine_config_tag[i].pid == s_current_machine_config.pid) )
        {
            pdt_name = s_manchine_config_tag[i].pdt_name;
            break;
        }
    }
    pi_memset(s_plat_config_vars.printer_cid_string, 0, NVLEN_CID_STR);
    pi_snprintf(s_plat_config_vars.printer_cid_string, NVLEN_CID_STR, "%s %s", s_current_machine_config.mfg_name,
        pdt_name);

    if  (PRINT_LANGUAGE_GDI == print_language) {
        cmd = "ACL,ZJS,URF,PJL";
    } else if (PRINT_LANGUAGE_IPS == print_language) {
        cmd = "ACL,PJL,PCL,PCLXL,PS3,PDF";
    } else {
        pi_log_d( "the print language is not correct\r\n");
        cmd = "ACL,PJL,PL,GDI";
    }
    pi_memset(s_plat_config_vars.product_1284_string, 0, NVLEN_1284_STRING);

    pi_snprintf(s_plat_config_vars.product_1284_string,  NVLEN_1284_STRING,
        "MFG:%s;CMD:%s;MDL:%s series;CID:%s series;CLS:%s;DES:%s series;",
        s_current_machine_config.mfg_name,
        cmd,
        s_current_machine_config.pdt_name,
        s_plat_config_vars.printer_cid_string,
        cls,
        s_plat_config_vars.printer_cid_string);

    pi_log_d( "1284 string = %s\r\n", s_plat_config_vars.product_1284_string);

}


void platvars_get_unique_printer_name()
{
#if IN_NETWORK
    uint8_t physAddr[MAC_ADDRESS_LEN];
#endif
    //unique printer name = printer_name [mac(lase three byte)]
#if IN_NETWORK
    //if (!NetGetHostMACaddr(0, physAddr, MAC_ADDRESS_LEN)) {
        pi_memset(s_plat_config_vars.unique_name, 0, NVLEN_UNIQUE_NAME);
        pi_snprintf(s_plat_config_vars.unique_name, NVLEN_UNIQUE_NAME, "%s[%02x%02x%02x]", s_current_machine_config.pdt_name,
            physAddr[3], physAddr[4], physAddr[5]);
    //}
#endif
}

static void platform_init_vid()
{
    s_plat_config_vars.vid = (uint32_t)(DEFAULT_VENDOR_ID);
}

static void  platform_init_fax_1284_string()
{
    pi_memset(s_plat_config_vars.product_fax_1284_string, 0, NVLEN_1284_STRING);
}

static void platvars_get_panel_board_ssn()
{
    int32_t ret = 0;
    char    data[PANEL_BOARD_SERIAL_NUMBER_LEN] = {0};

    ret = panel_get_ui_board_serial_num(data, PANEL_BOARD_SERIAL_NUMBER_LEN);
    if ( 0 != ret )
    {
        pi_log_e( "platform init notify event:EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY get error\n");
    }
    pi_log_d( "platform init notify event:EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY data{%s} data_len(%d)\n", data, sizeof(data));
    pi_event_mgr_notify(g_cli_ptr, EVT_TYPE_PLATFORM_PANEL_BOARD_SSN_MODIFY, data, PANEL_BOARD_SERIAL_NUMBER_LEN);
}

void platform_init_other_config()
{
    platform_init_version();

    platform_init_1284_string();

    platform_init_vid();

    platform_init_fax_1284_string();

    platvars_get_unique_printer_name();

    platvars_get_panel_board_ssn();

}

int32_t platform_init_machine_config_defualt()
{
    s_current_machine_config.machine_tag = s_machine_config_ary[0].machine_tag;
    s_plat_config_vars.product_1284_string[0] = 0;
    s_plat_config_vars.unique_name[0] = 0;
    s_plat_config_vars.restore_flag = false;
    s_plat_config_vars.timezone = DEFAULT_TIMEZONE;
    s_plat_config_vars.quiet = 0;///wangbo 2016-March-5
    s_plat_config_vars.io_timeout = DEFAULT_IO_TIMEOUT;
    s_plat_config_vars.firmware_country_code = s_machine_config_ary[0].machine_country_code;
    s_plat_config_vars.firmware_region_code = REGION_CHINA;
    s_plat_config_vars.system_language_code = SYS_LANGUAGE_CHINESE;


    pi_strncpy(s_plat_config_vars.printer_location, DEFAULT_LOCATION, NVLEN_PRINTER_LOCATION);
    pi_strncpy(s_plat_config_vars.product_serial_number, DEFAULT_SER_NUM_STRING, NVLEN_PROD_SER_NUM);//AA2A000000
    pi_strncpy(s_plat_config_vars.product_date, DEFAULT_PRODUCT_DATE, NVLEN_PRODUCT_INFO);
    pi_strncpy(s_plat_config_vars.batch_number, DEFAULT_BATCH_NUMBER, NVLEN_PRODUCT_INFO);
    pi_strncpy(s_plat_config_vars.contact_info, DEFAULT_CONTACT_INFO, NVLEN_CONTACT_INFO);
    pi_strncpy(s_plat_config_vars.property_number, DEFAULT_PROPERTY_NUMBER, NVLEN_CONTACT_INFO);

    return 0;

}

void platform_defualt_config_nvram()
{
    pi_nvram_set(PLATFORM_ID_PLATFORM_PRODUCT_MODEL, VTYPE_UINT, (void *)&s_current_machine_config.machine_tag, sizeof(uint32_t), 0, NULL);

}

/*
void platform_change_config_on_product()
{
    // cp2100d doesnt support AirPrint.
    if (s_current_machine_config.pid == 0xA444) {
        s_plat_config_vars.AirPrintEn = 0;
    }
}
*/

void platform_prolog(void)
{
    int32_t ret;
    /* register all events */
    g_cli_ptr = platform_event_register();
    if (g_cli_ptr == NULL) {
        pi_log_e("platform event init err\n");
        return;
    }
    pi_memset(&s_plat_config_vars, 0, sizeof(s_plat_config_vars));
    /* init machine info */
    platform_init_machine_config();

    /* init all config */
    ret = platform_init_config_from_nv();
    if (ret != PLATFORM_OK)
    {
        platform_init_machine_config_defualt();
        platform_defualt_config_nvram();
    }

    platform_init_other_config();

    platform_register_acl_cmds();
    platform_register_oid_attr();
    //platform_change_config_on_product();

    /* notify default data */
    platform_notify_defualt_data();
    if( 0 != platform_restore_prolog())
    {
        pi_log_w("platform restore failed\n");
    }

#if CONFIG_SDK_PEDK
    platform_pedkapi_init();  /* PEDK API处理函数注册 */
#endif

    //event client destroy need to do

}
/**
 * @}
 */

