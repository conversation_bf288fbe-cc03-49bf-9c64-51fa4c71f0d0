# QuickJS C API 绑定指导

- [QuickJS C API 绑定指导](#quickjs-c-api-绑定指导)
  - [数据类型的映射关系](#数据类型的映射关系)
  - [绑定步骤](#绑定步骤)
    - [1. 编写 C 函数](#1-编写-c-函数)
    - [2.包含头文件](#2包含头文件)
    - [3. 注册绑定函数](#3-注册绑定函数)
    - [4. 实现函数逻辑](#4-实现函数逻辑)
    - [5. 编译和链接](#5-编译和链接)
  - [例子](#例子)

## 数据类型的映射关系

QuickJS 是一个 C 语言实现的 JavaScript 虚拟机，虚拟机的实现中一定有对应 JavaScript 和 C 语言的数据类型。
在 JavaScript 和 C 语言的交互中，JavaScript 数据类型 和 C 语言的数据类型的映射关系是必须要关注的。

| 类型      | JavaScript | C 语言 |
| --------- | ---------- | ------ |
| 整型      | Number     | int    |
| 浮点型    | Number     | double |
| 字符串    | String     | char[] |
| 布尔型    | Boolean    | int    |
| 数组      | Array      | 数组   |
| 字典/映射 | Object/Map | 结构体 |

## 绑定步骤

### 1. 编写 C 函数

首先，编写您要绑定到 QuickJS 的 C 函数。确保函数按照 C 语言的语法规则编写，并考虑适当的参数和返回值类型。您可以根据需要定义其他的 C 函数或结构体。

### 2.包含头文件

在 C 文件中，包含 QuickJS 的头文件以便使用 QuickJS 的 API。
请确保编译配置中包含 QuickJS 的头文件目录。

使用下面的代码行包含 QuickJS 的头文件：

```c
#include "quickjs.h"
```

### 3. 注册绑定函数

当绑定 C 函数时，请使用 QuickJS 提供的宏`JS_NewCFunction`，该宏接受以下参数：

- `ctx`：`JSContext`上下文对象，它是 QuickJS 引擎的关键结构，用于管理 JavaScript 运行环境。
- `func`：C 函数指针，指向实际的绑定函数。
- `name`：绑定函数的名称。在 JavaScript 中调用函数时会使用该名称。
- `length`：指定函数参数的长度。传入-1 表示不限制参数个数。

下面是注册绑定函数的具体示例代码：

```c
// 要绑定的 C 函数
static JSValue js_your_function(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
   // C函数的实现代码
   // ...
}

// 在初始化QuickJS环境时注册绑定函数
JS_SetPropertyStr(ctx, global_obj, "your_function", JS_NewCFunction(ctx, js_your_function, "your_function", -1));
```

在这个示例中，我们通过将 C 函数`js_your_function`注册到 QuickJS 的全局对象中，并使用名称`"your_function"`将其命名为绑定函数。这样，JavaScript 代码可以通过`your_function()`来调用该函数。

解释每个参数的含义如下：

- `JSContext *ctx`：表示 QuickJS 的上下文对象，用于执行 JavaScript 代码和处理 JavaScript 值。
- `JSValueConst this_val`：表示在调用函数时传递的`this`值。在 C 函数中，无需使用此值，因此我们将其声明为`JSValueConst`类型，并命名为`this_val`。
- `int argc`：表示传递给函数的参数数量。
- `JSValueConst *argv`：表示一个指向传递给函数的参数数组的指针。该数组中的每个元素都是一个`JSValueConst`类型的值，表示函数的参数。

在`js_your_function`函数中，我们使用 QuickJS 提供的 API 函数来处理和操作这些参数。`ctx`参数用于访问 QuickJS 的 API 函数，`this_val`参数没有在此函数中使用。

`argc`参数表示传递给函数的参数数量。我们可以根据需要使用循环和索引来遍历参数数组`argv`，以获取每个参数的值。

请注意，`JSValueConst`类型表示一个不可变的 JavaScript 值，用于在 C 函数中处理传入的 JavaScript 参数。如果需要在 C 函数中修改值，则需要使用`JSValue`类型。

请根据您的实际需求和代码结构，将`js_your_function`替换为您自己的 C 函数名称，并根据需要调整函数参数的处理。

记住，在注册绑定函数之前，确保正确包含 QuickJS 的头文件，并在编译和链接时链接 QuickJS 的库文件。

### 4. 实现函数逻辑

在实现函数逻辑时，可以使用 QuickJS 的 API 与 JavaScript 界面进行交互，包括获取参数、调用其他 JavaScript 函数等。下面是一个详细的步骤：

首先，根据参数的数量(`argc`)和参数数组(`argv`)来解析传入的参数。可以使用 QuickJS 的 API 函数来获取和检查参数的类型和值。以下是一些常用的 API 函数示例：

- `JS_ToInt32(ctx, argv[i])`：将参数转换为 32 位整数。
- `JS_ToFloat64(ctx, argv[i])`：将参数转换为 64 位浮点数。
- `JS_ToBool(ctx, argv[i])`：将参数转换为布尔值。
- `JS_ToCString(ctx, argv[i])`：将参数转换为 C 字符串。

如下所示：

```c
JSValue arg1 = argv[0];
if (JS_IsNumber(arg1)) {
    int32_t num = JS_ToInt32(ctx, arg1);
    // 处理整数参数
    // ...
}
else if (JS_IsString(arg1)) {
    const char *str = JS_ToCString(ctx, arg1);
    // 处理字符串参数
    // ...
    JS_FreeCString(ctx, str); // 释放由JS_ToCString分配的内存
}
else {
    // 处理其他类型的参数
    // ...
}
```

在处理参数后，可以执行您的自定义逻辑。根据您的需求，可以进行各种操作，例如执行计算、访问外部资源、打印输出等。

如下所示：

```c
printf("Your function is called with %d arguments.\n", argc);

for (int i = 0; i < argc; i++) {
    JSValue arg = argv[i];
    if (JS_IsNumber(arg)) {
        double num = JS_ToFloat64(ctx, arg);
        printf("Argument %d is a number: %f\n", i, num);
    }
    else if (JS_IsString(arg)) {
        const char *str = JS_ToCString(ctx, arg);
        printf("Argument %d is a string: %s\n", i, str);
        JS_FreeCString(ctx, str);
    }
}

// 继续执行其他操作
```

在函数逻辑的末尾，可以根据需要返回一个值给 JavaScript 调用方。可以使用 QuickJS 的 API 函数来创建适当的返回值。以下是一些常用的 API 函数示例：

- `JS_NewInt32(ctx, value)`：创建一个 32 位整数的值。
- `JS_NewFloat64(ctx, value)`：创建一个 64 位浮点数的值。
- `JS_NewString(ctx, value)`：创建一个字符串的值。
- `JS_NewBool(ctx, value)`：创建一个布尔值的值。

如下所示：

```c
// 创建并返回一个整数值给JavaScript
JSValue result = JS_NewInt32(ctx, 42);
return result;
```

确保在函数逻辑代码中处理错误情况并进行适当的错误处理。

### 5. 编译和链接

最后，在编译和链接时，确保包含 QuickJS 的库文件，并正确设置链接选项，以便在运行时正确解析和调用绑定函数。

## 例子

下面是一个完整的例子，演示了如何注册一个绑定函数并实现其逻辑：

```c
#include <stdio.h>
#include <stdlib.h>
#include "quickjs.h"

static JSValue js_hello(JSContext *ctx, JSValueConst this_val,
                        int argc, JSValueConst *argv)
{
    // 处理传入的参数
    if (argc > 0) {
        JSValue arg = argv[0];
        if (JS_IsString(arg)) {
            const char *name = JS_ToCString(ctx, arg);

            // 输出欢迎消息
            printf("Hello, %s!\n", name);

            JS_FreeCString(ctx, name);
        }
    }

    // 返回一个成功的值
    return JS_NewString(ctx, "Hello, World!");
}

int main()
{
    // 创建QuickJS上下文对象
    JSRuntime *rt = JS_NewRuntime();
    JSContext *ctx = JS_NewContext(rt);

    // 创建全局对象
    JSValue global_obj = JS_GetGlobalObject(ctx);

    // 注册绑定函数
    JS_SetPropertyStr(ctx, global_obj, "hello", JS_NewCFunction(ctx, js_hello, "hello", 1));

    // 执行JavaScript代码，这只是一个例子
    const char *code = "console.log(hello('Motoko'));";
    JS_Eval(ctx, code, strlen(code), "<input>", JS_EVAL_TYPE_GLOBAL);

    // 释放资源
    JS_FreeValue(ctx, global_obj);
    JS_FreeContext(ctx);
    JS_FreeRuntime(rt);

    return 0;
}
```

在这个例子中，我们首先创建了 QuickJS 的运行时和上下文对象。然后，我们创建了全局对象，并在其中注册了一个名为"hello"的绑定函数，其 C 函数指针指向`js_hello`函数。

在`js_hello`函数中，我们检查传入的参数是否是字符串，并将其转换为 C 字符串。然后，我们根据传入的参数输出相应的欢迎消息，并返回一个成功的字符串。

最后，我们执行了一个简单的 JavaScript 代码，调用了我们注册的绑定函数"hello"并将结果打印到控制台。
