/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file tls.c
 * @addtogroup net
 * @{
 * @addtogroup tls
 * <AUTHOR> <PERSON>n
 * @date 2023-7-28
 * @brief TLS manager API
 */
#include <openssl/ossl_typ.h>
#include <openssl/bio.h>
#include <openssl/ssl.h>
#include <openssl/err.h>
#include <openssl/md5.h>

#include "nettypes.h"
#include "netmisc.h"
#include "netctx.h"
#include "nettls.h"

#define TLS_PRIV_PSWD_DEF   "Pantum000000"

#define TLS_PRIV_CERT_DEF   \
"-----BEGIN RSA PRIVATE KEY-----\n"\
"Proc-Type: 4,ENCRYPTED\n"\
"DEK-Info: AES-128-CBC,6F102D88FDB21ADD757CE247B4F7DF8A\n"\
"\n"\
"xII0RX5JCMicqqJD5b8gc1aMeNqyA/idASC0n/rcMGmLJYchG5lHTgwRwt7hxiDY\n"\
"eMaZUHoX15ZC8tnq7sRJ0n480OsflFMiQrailzuRbSIdCkm5YMo0686ZMszVstW8\n"\
"jTBJx3lSm+3tyidHVg7HBmCjSxpQIVxd30UNDwjDk63hHG5SmGGu1b8tVDbRjNFx\n"\
"/QQm1W25T+Abd2dV4fMzPOFF5W7sYjtB0pkzugdEFx13rE4UkFpeauGMy325Umgw\n"\
"8tkxpFu5k/l8wzjYcDv1CYTTB8m3PDbDgA9Lt6yh1pPHRNzmOGEHTubWchLWbjTG\n"\
"JzvEc6/3BhltED+DgAJvczfuanOeqE1qc//9gt7HMXOfqne1ut9TQ1G0kBf5XFkI\n"\
"TBzvcPrshtuVMvooren9jgqhAtNwSaSC+BoyQxb2r0F566pZk91BGBKYHmE4vU/j\n"\
"7oJDtRI3EHv/yLlMzyPzl1akzF1F3aGuTZpPeIEitUaR/J7TvYJB8Us12ABX8P2a\n"\
"70P4vRzUDfqRajMRLadPoMnICwlq3+wX/HgTUgJsQ2lOq6W/mcEeVZQSOtEahTRA\n"\
"41A9c2X1SOz5J9oSwjcN/RQHkzD3zNTKsdqnjgSRYMynwgW6Qz9LcnYEOChl+F4Z\n"\
"X9Im7fmeJtvrLZY0HayIxtBj5f4Wl5Fr/VAsz44kLjipIdMcSUdvrzQ52CtiY69J\n"\
"jLb50IVMpP65vLRtdCS/RlKPykXZMeR6QclGuvuHTu+hQcoNVy0X9n0RK271FL39\n"\
"0fD4DkoQ70piaZCxtVUnciyRbOuKARuobrI0w1n1wL7cuYn9U+6vEid4iiakxcml\n"\
"ziPl6X01CFswVUEpv1aieX6kmkEUGQIhUUH9m2rQcpAv4WTgVqvqB6HSdIpONZHw\n"\
"slWPU0L72StebtKiehiKaxI8P80HsS/v0cD2fnvIYmqQZi2/fGxIXTYk+57iROKl\n"\
"e83esykuj0SLVm816rUwbeAfcgWRqlQz2YOMlYFef7ZXm5jJrkeGdOt7YIHhz7vk\n"\
"OpmiK/sm/wSj7eUXa8auHW/tq1f1n4Q97A6TXY/RwtzuOMYP9fxC3/y3+LmAovtx\n"\
"rfS0tjMbs7DTLQpG5lI+goezAWVaoTVpNWbD5KWJlkt14EuuVYYHUGpbd6DgvpzA\n"\
"W0PHvJkhWWvhlLz5pnRrGqOjEWAyqd+NWFOKIOBjhwq+lNzrZJbEFoxGFRaB1qlj\n"\
"h2Pt/+HkhzSGw3HIbdVgmJ6d1gUtgutJXxDbjLDMjqhAiVc9VZGo/weZeAbbbHL/\n"\
"WoAO0EN1po+ovsNCtBtdVMPkpfF0YcUbD/jGqggeh3vWPLkXxD8G+ASDaoFu1+88\n"\
"p+h6qRrSFLWATziLhoxfH9usZu6MqdEBND3MV+MieKg/NX319kS59OGVNbuK3siD\n"\
"/Ul5ghNqXw0H+okJlJV3kzu3KcL6nlFFO8BSCeCFS8sXNupsXMMyGH/xP4psOlh6\n"\
"63nuvYfZA9X5TcQZ9QFEAXs89Pv0pKHNM3Ytkzrn+DgLM7wKTlszrTqq9ms720iq\n"\
"OnOqCVP8u9InmjBX+0AFfESi6LE4ae09c7F8gE7qX78k8zOvjN7pLmDmYxJg6zDo\n"\
"-----END RSA PRIVATE KEY-----\n"\
"Certificate:\n"\
"    Data:\n"\
"        Version: 3 (0x2)\n"\
"        Serial Number: 29285 (0x7265)\n"\
"    Signature Algorithm: sha256WithRSAEncryption\n"\
"        Issuer: C=CN, ST=Guangdong, O=Pantum Inc, OU=www.pantum.com, CN=Pantum Technology Cert\n"\
"        Validity\n"\
"            Not Before: Jun 12 13:28:47 2023 GMT\n"\
"            Not After : Jun  9 13:28:47 2033 GMT\n"\
"        Subject: C=CN, ST=Guangdong, O=Pantum Inc, OU=www.pantum.com, CN=Pantum Technology Cert\n"\
"        Subject Public Key Info:\n"\
"            Public Key Algorithm: rsaEncryption\n"\
"                Public-Key: (2048 bit)\n"\
"                Modulus:\n"\
"                    00:b5:e2:e9:c0:ad:b1:7a:9b:1c:a9:ed:5b:dc:34:\n"\
"                    bd:db:18:1d:47:2b:64:05:08:ee:58:7d:be:2c:65:\n"\
"                    08:94:52:cf:2b:c5:f8:31:5c:5d:3f:0c:b3:1d:60:\n"\
"                    fd:e0:95:88:56:af:dc:20:7f:4a:31:ae:03:da:90:\n"\
"                    f8:b7:bb:73:2b:16:13:af:ff:97:13:15:00:83:fd:\n"\
"                    59:d8:af:29:2b:c4:10:86:b2:ad:60:89:31:ef:c0:\n"\
"                    dc:0c:95:fb:06:15:d5:38:3f:c6:87:33:37:cc:82:\n"\
"                    9d:2c:61:3c:3f:93:ba:ba:d5:81:26:fc:90:42:46:\n"\
"                    3e:f8:22:90:cd:14:2d:7f:b4:51:b1:5e:2f:b4:27:\n"\
"                    62:8b:bb:79:fb:30:e5:57:ee:a6:e7:dd:b0:89:ec:\n"\
"                    93:1f:91:6e:ca:79:cd:3e:3d:74:dd:3b:92:59:57:\n"\
"                    f1:f4:39:cb:9e:eb:53:5c:ec:13:3f:aa:fb:f8:15:\n"\
"                    7a:6b:45:fa:16:cd:ee:4b:5c:63:be:62:62:69:3e:\n"\
"                    06:1f:01:96:83:76:d1:07:87:26:2a:03:62:71:09:\n"\
"                    11:ed:bf:74:63:5d:10:79:bf:1f:97:6c:f6:a5:9a:\n"\
"                    97:9e:19:f4:da:49:70:29:af:19:eb:12:18:5a:5b:\n"\
"                    ed:c0:0a:95:68:dd:97:73:3c:08:b3:e0:28:58:47:\n"\
"                    55:eb\n"\
"                Exponent: 65537 (0x10001)\n"\
"        X509v3 extensions:\n"\
"            X509v3 Basic Constraints: \n"\
"                CA:FALSE\n"\
"            Netscape Comment: \n"\
"                OpenSSL Generated Certificate\n"\
"            X509v3 Subject Key Identifier: \n"\
"                12:05:9B:4B:56:2E:BB:71:F0:45:C4:BC:90:0F:3D:D2:64:AA:97:FC\n"\
"            X509v3 Authority Key Identifier: \n"\
"                keyid:B2:46:8B:EE:87:23:16:3A:10:83:97:D6:21:54:04:3B:D2:23:AD:EC\n"\
"\n"\
"    Signature Algorithm: sha256WithRSAEncryption\n"\
"         3e:51:7c:de:a7:42:5b:09:32:a5:d9:14:1a:8b:ee:c6:50:9b:\n"\
"         ed:1c:b3:9b:57:ec:68:fe:48:f3:69:2f:c0:de:53:15:86:88:\n"\
"         f4:c6:ee:da:a3:cf:52:b1:78:bc:ec:9f:84:08:23:4c:59:0f:\n"\
"         3a:51:34:6e:86:b9:5d:a8:3b:99:9a:e4:ff:66:51:d1:06:15:\n"\
"         ec:61:5d:a5:fe:62:14:92:b2:ef:92:50:3c:0a:6f:ea:34:13:\n"\
"         7b:71:cd:e2:df:b1:a9:77:6a:c1:68:ef:2c:a0:5e:df:d8:66:\n"\
"         da:22:ce:09:52:ba:24:64:0f:e9:86:25:33:80:21:2a:fe:fd:\n"\
"         fa:bd:4e:9f:c9:40:4b:d2:c3:2a:1d:9c:45:91:4e:2a:a4:b1:\n"\
"         d9:c8:f0:27:cb:44:2d:6e:9b:c9:9e:36:a6:8c:4b:d1:72:d2:\n"\
"         ce:cb:f4:31:a1:a9:23:40:e2:25:44:5f:ac:01:f0:7b:be:6d:\n"\
"         a0:25:54:03:94:6e:ae:8c:eb:b6:96:91:bb:a2:4a:b9:a1:25:\n"\
"         18:d1:2c:19:f9:e2:0b:74:6b:51:8d:12:f3:76:87:35:24:be:\n"\
"         90:ab:3f:b7:8d:22:54:a3:ef:23:ab:4d:eb:6e:2d:89:8d:96:\n"\
"         e5:25:11:d4:71:db:29:e3:3b:e1:8a:64:de:b3:0e:9b:e6:ec:\n"\
"         0e:c5:38:e1\n"\
"-----BEGIN CERTIFICATE-----\n"\
"MIID1zCCAr+gAwIBAgICcmUwDQYJKoZIhvcNAQELBQAwcDELMAkGA1UEBhMCQ04x\n"\
"EjAQBgNVBAgMCUd1YW5nZG9uZzETMBEGA1UECgwKUGFudHVtIEluYzEXMBUGA1UE\n"\
"CwwOd3d3LnBhbnR1bS5jb20xHzAdBgNVBAMMFlBhbnR1bSBUZWNobm9sb2d5IENl\n"\
"cnQwHhcNMjMwNjEyMTMyODQ3WhcNMzMwNjA5MTMyODQ3WjBwMQswCQYDVQQGEwJD\n"\
"TjESMBAGA1UECAwJR3Vhbmdkb25nMRMwEQYDVQQKDApQYW50dW0gSW5jMRcwFQYD\n"\
"VQQLDA53d3cucGFudHVtLmNvbTEfMB0GA1UEAwwWUGFudHVtIFRlY2hub2xvZ3kg\n"\
"Q2VydDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALXi6cCtsXqbHKnt\n"\
"W9w0vdsYHUcrZAUI7lh9vixlCJRSzyvF+DFcXT8Msx1g/eCViFav3CB/SjGuA9qQ\n"\
"+Le7cysWE6//lxMVAIP9WdivKSvEEIayrWCJMe/A3AyV+wYV1Tg/xoczN8yCnSxh\n"\
"PD+TurrVgSb8kEJGPvgikM0ULX+0UbFeL7QnYou7efsw5VfupufdsInskx+Rbsp5\n"\
"zT49dN07kllX8fQ5y57rU1zsEz+q+/gVemtF+hbN7ktcY75iYmk+Bh8BloN20QeH\n"\
"JioDYnEJEe2/dGNdEHm/H5ds9qWal54Z9NpJcCmvGesSGFpb7cAKlWjdl3M8CLPg\n"\
"KFhHVesCAwEAAaN7MHkwCQYDVR0TBAIwADAsBglghkgBhvhCAQ0EHxYdT3BlblNT\n"\
"TCBHZW5lcmF0ZWQgQ2VydGlmaWNhdGUwHQYDVR0OBBYEFBIFm0tWLrtx8EXEvJAP\n"\
"PdJkqpf8MB8GA1UdIwQYMBaAFLJGi+6HIxY6EIOX1iFUBDvSI63sMA0GCSqGSIb3\n"\
"DQEBCwUAA4IBAQA+UXzep0JbCTKl2RQai+7GUJvtHLObV+xo/kjzaS/A3lMVhoj0\n"\
"xu7ao89SsXi87J+ECCNMWQ86UTRuhrldqDuZmuT/ZlHRBhXsYV2l/mIUkrLvklA8\n"\
"Cm/qNBN7cc3i37Gpd2rBaO8soF7f2GbaIs4JUrokZA/phiUzgCEq/v36vU6fyUBL\n"\
"0sMqHZxFkU4qpLHZyPAny0QtbpvJnjamjEvRctLOy/QxoakjQOIlRF+sAfB7vm2g\n"\
"JVQDlG6ujOu2lpG7okq5oSUY0SwZ+eILdGtRjRLzdoc1JL6Qqz+3jSJUo+8jq03r\n"\
"bi2JjZblJRHUcdsp4zvhimTesw6b5uwOxTjh\n"\
"-----END CERTIFICATE-----\n"

#define VALID_DATE_FORMAT   "([0-9]{4}+"\
    "((0[1-9]|1[0-2])(0[1-9]|1[0-9]|2[0-8])|(0[13-9]|1[0-2])(29|30)|(0[13578]|1[02])31)|([0-9]{2}(0[48]|[2468][048]|[13579][26])|(0[48]|[2468][048]|[13579][26])00)0229)"\
    "+(0[0-9]|1[0-9]|2[0-3])+([0-5][0-9])+([0-5][0-9])" ///< date format : yyyymmddhhmmss

#ifndef SSL_CERT_SUBJ
#define SSL_CERT_SUBJ       "/C=CN/ST=Guangdong/O=Pantum Inc/OU=www.pantum.com/CN=Pantum Technology Cert"
#endif

#define CERT_DIR            "/tmp/cert"
#define TLS_PRIV_KEY_FILE   CERT_DIR "/server_key.pem"  ///< 证书私钥文件
#define TLS_CERT_REQ_FILE   CERT_DIR "/server_req.pem"  ///< 证书申请文件
#define TLS_CERT_SIG_FILE   CERT_DIR "/server_sig.pem"  ///< 使用CA签名的证书文件 */
#define TLS_CERT_FILE       CERT_DIR "/tlscert.pem"     ///< 证书私钥 + 签名

#define CA_DIR              "/tmp/demoCA"
#define CA_PKEY_FILE        CA_DIR "/ca.key"
#define CA_CERT_FILE        CA_DIR "/ca.crt"
#define CA_PKEY_PSWD        "3012upgrade"

#define TLS_MLOCK_EX()      { if (s_tls_ctx != NULL) pi_mutex_lock(s_tls_ctx->mutex);   }
#define TLS_MLOCK_UN()      { if (s_tls_ctx != NULL) pi_mutex_unlock(s_tls_ctx->mutex); }

#define runcmd_and_check(outfile, fmt, ...)         \
    do {                                            \
        remove(outfile);                            \
        pi_runcmd(NULL, 0, 0, fmt, ##__VA_ARGS__);  \
        pi_usleep(500);                             \
        BREAK_IF(HAS_NO_FILE(outfile), NET_WARN);   \
    } while (0)

typedef struct tls_contect
{
    NET_CTX_S*      net_ctx;
    PI_MUTEX_T      mutex;
    char            priv_cert[8192];
    char            ca_cert[2048];
    char            ca_pkey[2048];
    int32_t         is_def;
}
TLS_CTX_S;

static TLS_CTX_S*   s_tls_ctx = NULL;

static void tls_update_priv_cert(const char* cert, const char* pswd, uint32_t is_def)
{
    snprintf(s_tls_ctx->priv_cert, sizeof(s_tls_ctx->priv_cert), "%s", cert);
    netdata_set_tls_priv_cert(DATA_MGR_OF(s_tls_ctx), cert);
    netdata_set_tls_priv_pswd(DATA_MGR_OF(s_tls_ctx), pswd);
    s_tls_ctx->is_def = is_def;
}

/* 解析X509中的数据到指定的证书结构体中 */
static int32_t tls_parse_x509(X509* x, TLS_CERT_INFO_S* info)
{
    EVP_PKEY*       pkey = NULL;
    X509_CINF*      ci;
    ASN1_INTEGER*   bs;
    X509_NAME*      xname;
    BIO*            b;
    int32_t         ret = -1;
    int32_t         i;
    long            sn;

    memset(info, 0, sizeof(TLS_CERT_INFO_S));
    do
    {
        b = BIO_new(BIO_s_mem());
        if ( b == NULL )
        {
            NET_WARN("create BIO memory object failed");
            break;
        }

        ci = x->cert_info;

        /* 证书版本 */
        info->ver = X509_get_version(x) + 1;

        /* 证书序号 */
        bs = X509_get_serialNumber(x);
        if ( bs->length <= 4 )
        {
            sn = ASN1_INTEGER_get(bs);
            info->sn[0] = (char)(sn & 0xff);
            info->sn[1] = (char)((sn >> 8) & 0xff);
            info->sn[2] = (char)((sn >> 16)& 0xff);
            info->sn[3] = (char)((sn >> 24)& 0xff);
            info->snlen = 4;
        }
        else
        {
            info->snlen = bs->length;
            if ( info->snlen > sizeof(info->sn) )
            {
                info->snlen = sizeof(info->sn);
            }
            for ( i = 0; i < info->snlen; ++i )
            {
                info->sn[i] = bs->data[i];
            }
        }

        /* 签名算法 */
        if ( i2a_ASN1_OBJECT(b, ci->signature->algorithm) <= 0 )
        {
            NET_WARN("Cannot read cert signature algorithm");
            break;
        }

        if ( BIO_read(b, info->sign_alg, sizeof(info->sign_alg)) <= 0 )
        {
            NET_WARN("Cannot read cert signature algorithm");
            break;
        }

        /* 证书用户的身份信息 */
        if ( (xname = X509_get_issuer_name(x)) == NULL )
        {
            NET_WARN("Cannot read cert CA information");
            break;
        }

        if ( X509_NAME_print(b, xname, 16) < 0 )
        {
            NET_WARN("Cannot read cert CA information");
            break;
        }

        if ( BIO_read(b, info->ca, sizeof(info->ca)) <= 0 )
        {
            NET_WARN("Cannot read cert CA information");
            break;
        }

        /* 证书有效期 */
        if ( ASN1_TIME_print(b, X509_get_notBefore(x)) == 0 )
        {
            NET_WARN("Cannot read cert start time");
            break;
        }

        if ( BIO_read(b, info->date_start, sizeof(info->date_start)) <= 0 )
        {
            NET_WARN("Cannot read cert start time");
            break;
        }

        if ( ASN1_TIME_print(b, X509_get_notAfter(x)) == 0)
        {
            NET_WARN("Cannot read cert end time");
            break;
        }

        if ( BIO_read(b, info->date_end, sizeof(info->date_end)) <= 0 )
        {
            NET_WARN("Cannot read cert end time");
            break;
        }

        /* 用户的身份信息 */
        if ( (xname = X509_get_subject_name(x)) == NULL )
        {
            NET_WARN("Cannot read cert user information");
            break;
        }

        if ( X509_NAME_print(b, xname, 16) < 0 )
        {
            NET_WARN("Cannot read cert user information");
            break;
        }

        if ( BIO_read(b, info->user, sizeof(info->user)) <= 0 )
        {
            NET_WARN("Cannot read cert user information");
            break;
        }

        /* 颁发者使用的公钥加密算法 */
        if ( i2a_ASN1_OBJECT(b, ci->key->algor->algorithm) <= 0 )
        {
            NET_WARN("Cannot read cert subject key algorithm");
            break;
        }

        if ( BIO_read(b, info->encryption, sizeof(info->encryption)) <= 0 )
        {
            NET_WARN("Cannot read cert subject key algorithm");
            break;
        }

        /* 颁发者的公钥 */
        pkey = X509_get_pubkey(x);
        if ( pkey )
        {
            EVP_PKEY_print_public(b, pkey, 16, NULL);
            BIO_read(b, info->pubkey, sizeof(info->pubkey));
            EVP_PKEY_free(pkey);
        }
        else
        {
            NET_WARN("No public key to read");
        }

        /* 颁发者使用的签名 */
        if ( X509_signature_print(b, x->sig_alg, x->signature) > 0 )
        {
            BIO_read(b, info->signature, sizeof(info->signature));
        }
        else
        {
            NET_WARN("No signature to read");
        }
        ret = 0;
    }
    while ( 0 );

    if ( b != NULL )
    {
        BIO_free(b);
    }
    return ret;
}

/* 重置证书索引和序列号配置，使可以重复生成并安装相同证书 */
int32_t nettls_reset_cert_conf(void)
{
    FILE* stream;

    stream = pi_fopen(CA_DIR "/index.txt", "w+");
    RETURN_VAL_IF(stream == NULL, NET_WARN, -1);
    pi_fclose(stream);

    stream = pi_fopen(CA_DIR "/serial", "w+");
    RETURN_VAL_IF(stream == NULL, NET_WARN, -1);
    fprintf(stream, "01");
    pi_fclose(stream);

    remove(CA_DIR "index.txt.attr");
    remove(CA_DIR "index.txt.old");
    remove(CA_DIR "serial.old");

    return 0;
}

/* 读取当前系统中使用的证书的信息, 如果读取失败，使用固件中默认的证书 */
int32_t nettls_get_cert_info(TLS_CERT_INFO_S* info, int32_t* is_def)
{
    X509*   x509 = NULL;
    BIO*    pbio = NULL;
    int32_t ret  = -1;

    RETURN_VAL_IF(s_tls_ctx == NULL, NET_WARN, -1);
    RETURN_VAL_IF(info == NULL, NET_WARN, -1);

    TLS_MLOCK_EX();
    while ( 1 )
    {
        do
        {
            /* 创建内存BIO对象，并写入指定的文本 */
            pbio = BIO_new(BIO_s_mem());
            BREAK_IF(pbio == NULL, NET_WARN);

            BIO_write(pbio, s_tls_ctx->priv_cert, strlen(s_tls_ctx->priv_cert));

            /* 读取BIO对象为X.509 证书 */
            x509 = PEM_read_bio_X509(pbio, NULL, NULL, NULL);
            BREAK_IF(x509 == NULL, NET_WARN);

            /* 读取X.509证书到指定的结构体中 */
            ret = tls_parse_x509(x509, info);
            BREAK_IF(ret != 0, NET_WARN);
        }
        while ( 0 );

        if ( x509 != NULL )
        {
            X509_free(x509);
        }
        if ( pbio != NULL )
        {
            BIO_free(pbio);
        }

        if ( ret == 0 )
        {
            if ( is_def )
            {
                *is_def = s_tls_ctx->is_def;
            }
            break;
        }
        else
        {
            if ( s_tls_ctx->is_def == 0 )
            {
                /* 删除解析失败的自签名证书 */
                NET_INFO("parse self-signed certificate failed, use firmware certificate");
                tls_update_priv_cert(TLS_PRIV_CERT_DEF, TLS_PRIV_CERT_DEF, 1);
            }
            else
            {
                NET_WARN("parse firmware certificate failed!");
                break;
            }
        }
    }
    TLS_MLOCK_UN();

    return ret;
}

/* 安装证书 */
int32_t	nettls_install_cert(const char* certfile, const char* password)
{
    TLS_CERT_INFO_S     info;
    X509*               x509 = NULL;
    BIO*                pbio = NULL;
    char*               cert = NULL;
    int32_t             ret = -1;
    size_t              len = 0;
    struct stat         st;

    NET_DEBUG("install certificate(%s) password(%s)", certfile, password);
    RETURN_VAL_IF(s_tls_ctx == NULL,        NET_WARN, -1);
    RETURN_VAL_IF(certfile == NULL,         NET_WARN, -1);
    RETURN_VAL_IF(password == NULL,         NET_WARN, -1);
    RETURN_VAL_IF(stat(certfile, &st) != 0, NET_WARN, -1);
    RETURN_VAL_IF(S_ISREG(st.st_mode) == 0, NET_WARN, -1);
    RETURN_VAL_IF(st.st_size <= 0,          NET_WARN, -1);

    TLS_MLOCK_EX();
    do
    {
        cert = (char *)pi_zalloc(st.st_size + 1);
        BREAK_IF(cert == NULL, NET_WARN);

        len = read_file(certfile, cert, st.st_size);
        BREAK_IF(len != st.st_size, NET_WARN);
        printf("certfile:\n%s\n", cert);

        /* 创建内存BIO对象，并写入指定的文本 */
        pbio = BIO_new(BIO_s_mem());
        BREAK_IF(pbio == NULL, NET_WARN);

        BIO_write(pbio, cert, len);

        /* 将该BIO对象按X.509证书PEM格式进行读取 */
        x509 = PEM_read_bio_X509(pbio, NULL, NULL, NULL);
        BREAK_IF(x509 == NULL, NET_WARN);

        ret = tls_parse_x509(x509, &info);
        BREAK_IF(ret != 0, NET_WARN);
    }
    while ( 0 );

    if ( ret == 0 )
    {
        tls_update_priv_cert(cert, password, 0);
    }
    if ( x509 != NULL )
    {
        X509_free(x509);
    }
    if ( pbio != NULL )
    {
        BIO_free(pbio);
    }
    if ( cert != NULL )
    {
        pi_free(cert);
    }
    TLS_MLOCK_UN();

    return ret;
}

/* 生成一个TLS X.509证书 */
int32_t nettls_make_cert(TLS_CERT_SELF_S* cert, const char* outfile)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_tls_ctx == NULL, NET_WARN, -1);
    RETURN_VAL_IF(outfile == NULL, NET_WARN, -1);

    TLS_MLOCK_EX();
    do
    {
        BREAK_IF(nettls_reset_cert_conf() < 0, NET_WARN);
        BREAK_IF(cert == NULL, NET_WARN);

        /* CA证书写入临时文件 */
        save_file(CA_CERT_FILE, s_tls_ctx->ca_cert, strlen(s_tls_ctx->ca_cert));
        BREAK_IF(HAS_NO_FILE(CA_CERT_FILE), NET_WARN);

        /* CA私钥写入临时文件 */
        save_file(CA_PKEY_FILE, s_tls_ctx->ca_pkey, strlen(s_tls_ctx->ca_pkey));
        BREAK_IF(HAS_NO_FILE(CA_PKEY_FILE), NET_WARN);

        /* 生成服务器私钥文件 */
        runcmd_and_check(TLS_PRIV_KEY_FILE, "openssl genrsa -aes128 -passout pass:%s -out %s 2048 > /dev/null 2>&1", TLS_PRIV_PSWD_DEF, TLS_PRIV_KEY_FILE);

        /* 使用私钥创建服务器证书申请文件 */
        runcmd_and_check(TLS_CERT_REQ_FILE, "openssl req -new -passin pass:%s -key %s -out %s -subj /C='%s'/ST='%s %s'/O='%s'/OU='%s'/CN='%s' > /dev/null 2>&1",
                TLS_PRIV_PSWD_DEF, TLS_PRIV_KEY_FILE, TLS_CERT_REQ_FILE, cert->country, cert->city, cert->state, cert->org, cert->unit, cert->name);

        if ( cert->flag == CERT_REQ )
        {
            ret = copy_file(outfile, TLS_CERT_REQ_FILE, "w+");
            break;
        }

        /* 校验自签名证书数据中的日期格式，有效格式为：YYYYMMDDHHMMSS */
        BREAK_IF(check_string_format(cert->start, VALID_DATE_FORMAT) != 0, NET_WARN);
        BREAK_IF(check_string_format(cert->end,   VALID_DATE_FORMAT) != 0, NET_WARN);

        /* 使用CA私钥对服务器申请文件进行签名并颁发TLS证书 */
        runcmd_and_check(TLS_CERT_SIG_FILE, "openssl ca -passin pass:%s -cert %s -keyfile %s -in %s -out %s -outdir %s -startdate %sZ -enddate %sZ -batch > /dev/null 2>&1",
                CA_PKEY_PSWD, CA_CERT_FILE, CA_PKEY_FILE, TLS_CERT_REQ_FILE, TLS_CERT_SIG_FILE, CERT_DIR, cert->start, cert->end);

        if ( cert->flag == CERT_TXT )
        {
            runcmd_and_check(outfile, "openssl x509 -in %s -out %s", TLS_CERT_SIG_FILE, outfile);
            ret = 0;
            break;
        }

        BREAK_IF(copy_file(TLS_CERT_FILE, TLS_PRIV_KEY_FILE, "w+")  != 0, NET_WARN);
        BREAK_IF(copy_file(TLS_CERT_FILE, TLS_CERT_SIG_FILE, "a+")  != 0, NET_WARN);
        ret = 0;
    }
    while ( 0 );
    TLS_MLOCK_UN();

    if ( ret == 0 && HAS_FILE(TLS_CERT_FILE) )
    {
        ret = nettls_install_cert(TLS_CERT_FILE, TLS_PRIV_PSWD_DEF);
    }
    remove(TLS_PRIV_KEY_FILE);
    remove(TLS_CERT_REQ_FILE);
    remove(TLS_CERT_SIG_FILE);
    remove(TLS_CERT_FILE);
    remove(CA_CERT_FILE);
    remove(CA_PKEY_FILE);
    return ret;
}

int32_t nettls_generate_cert(TLS_CERT_CONF_S* cert_conf_info)

{
//    char*       encrypt_type_str[] = {"genrsa", "gendsa", "genec"};
    char        hostname[HOSTNAME_LEN];
    char        pubkey_path[40] = {0};
    char        prikey_path[40] = {0};
    char        cer_path[40] = {0};
    int32_t     ret = -1;

    TLS_CERT_SELF_S cert =
    {
        .org     = "Inc",
        .unit    = "www.pantum.com",
        .city    = "Shenzhen",
        .state   = "Pantum",
        .country = "CN",
    };

    RETURN_VAL_IF(s_tls_ctx == NULL, NET_WARN, -1);
    RETURN_VAL_IF(cert_conf_info == NULL, NET_WARN, -1);

    memset(hostname, 0, sizeof(hostname));
    netdata_get_hostname(s_tls_ctx->net_ctx->data_mgr, hostname, sizeof(hostname));
    snprintf(pubkey_path, sizeof(pubkey_path), "/tmp/%s", cert_conf_info->pub_key_path);
    snprintf(prikey_path, sizeof(prikey_path), "/tmp/%s", cert_conf_info->key_path);
    snprintf(cer_path,    sizeof(cer_path),    "/tmp/%s", cert_conf_info->cert_path);

    if ( HAS_FILE(pubkey_path) || HAS_FILE(prikey_path) || HAS_FILE(cer_path))
    {
        remove(pubkey_path);
        remove(prikey_path);
        remove(cer_path);
    }

    TLS_MLOCK_EX();
    do
    {
        BREAK_IF(nettls_reset_cert_conf() < 0, NET_WARN);

        /* CA证书写入临时文件 */
        save_file(CA_CERT_FILE, s_tls_ctx->ca_cert, strlen(s_tls_ctx->ca_cert));
        BREAK_IF(HAS_NO_FILE(CA_CERT_FILE), NET_WARN);

        /* CA私钥写入临时文件 */
        save_file(CA_PKEY_FILE, s_tls_ctx->ca_pkey, strlen(s_tls_ctx->ca_pkey));
        BREAK_IF(HAS_NO_FILE(CA_PKEY_FILE), NET_WARN);

        /* 生成服务器私钥文件 */
        runcmd_and_check(prikey_path, "openssl genrsa -passout pass:%s -out %s %d > /dev/null 2>&1", cert_conf_info->password, prikey_path, cert_conf_info->key_length);

        /* 根据私钥生成公钥文件 */
        runcmd_and_check(pubkey_path, "openssl rsa -in %s -pubout -out %s > /dev/null 2>&1", prikey_path, pubkey_path);

        /* 使用私钥创建服务器证书申请文件 */
        runcmd_and_check(TLS_CERT_REQ_FILE, "openssl req -new -passin pass:%s -key %s -out %s -subj /C='%s'/ST='%s %s'/O='%s'/OU='%s'/CN='%s' > /dev/null 2>&1",
                cert_conf_info->password, prikey_path, TLS_CERT_REQ_FILE, cert.country, cert.city, cert.state, cert.org, cert.unit, hostname);

        /* 使用CA私钥对服务器申请文件进行签名并颁发TLS证书 */
        runcmd_and_check(cer_path, "openssl ca -passin pass:%s -cert %s -keyfile %s -in %s -out %s -outdir %s -days %d -batch > /dev/null 2>&1",
                CA_PKEY_PSWD, CA_CERT_FILE, CA_PKEY_FILE, TLS_CERT_REQ_FILE, cer_path, "/tmp/", cert_conf_info->days);

        ret = 0;
    }
    while ( 0 );
    TLS_MLOCK_UN();

    remove(TLS_CERT_REQ_FILE);
    remove(CA_CERT_FILE);
    remove(CA_PKEY_FILE);

    return ret;
}

/* 删除当前的证书 */
void nettls_delete_cert(void)
{
    RETURN_IF(s_tls_ctx == NULL, NET_WARN);

    TLS_MLOCK_EX();
    tls_update_priv_cert(TLS_PRIV_CERT_DEF, TLS_PRIV_PSWD_DEF, 1);
    TLS_MLOCK_UN();
}

int32_t nettls_init(NET_CTX_S* net_ctx)
{
    char    password[128];
    int32_t ret = -1;

    RETURN_VAL_IF(s_tls_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_tls_ctx = (TLS_CTX_S *)pi_zalloc(sizeof(TLS_CTX_S));
    RETURN_VAL_IF(s_tls_ctx == NULL, NET_WARN, -1);

    do
    {
        BREAK_IF((s_tls_ctx->mutex = pi_mutex_create()) == INVALIDMTX, NET_WARN);
        s_tls_ctx->net_ctx = net_ctx;

        BREAK_IF(HAS_NO_FILE(CERT_DIR) && (mkdir(CERT_DIR, 0755) < 0), NET_WARN);  ///< 自签名证书临时目录
        BREAK_IF(HAS_NO_FILE(CA_DIR)   && (mkdir(CA_DIR,   0755) < 0), NET_WARN);  ///< CA证书临时目录

        BREAK_IF(nettls_reset_cert_conf() < 0, NET_WARN);                          ///< 重置CA配置文件

        /* 检查CA证书 */
        netdata_get_tls_ca_cert(net_ctx->data_mgr, s_tls_ctx->ca_cert, sizeof(s_tls_ctx->ca_cert));
        netdata_get_tls_ca_pkey(net_ctx->data_mgr, s_tls_ctx->ca_pkey, sizeof(s_tls_ctx->ca_pkey));
        if ( STRING_IS_EMPTY(s_tls_ctx->ca_cert) || STRING_IS_EMPTY(s_tls_ctx->ca_pkey) )
        {
            /* 生成CA证书 */
            NET_DEBUG("generate CA certificate base on subject(%s)", SSL_CERT_SUBJ);
            pi_runcmd(NULL, 0, 0, "openssl req -new -x509 -passout pass:%s -keyout %s -out %s -subj \"%s\" > /dev/null 2>&1", CA_PKEY_PSWD, CA_PKEY_FILE, CA_CERT_FILE, SSL_CERT_SUBJ);
            BREAK_IF(HAS_NO_FILE(CA_PKEY_FILE) || HAS_NO_FILE(CA_CERT_FILE), NET_WARN);

            BREAK_IF(read_file(CA_CERT_FILE, s_tls_ctx->ca_cert, sizeof(s_tls_ctx->ca_cert)) == 0, NET_WARN);
            BREAK_IF(read_file(CA_PKEY_FILE, s_tls_ctx->ca_pkey, sizeof(s_tls_ctx->ca_pkey)) == 0, NET_WARN);

            netdata_set_tls_ca_cert(net_ctx->data_mgr, s_tls_ctx->ca_cert);
            netdata_set_tls_ca_pkey(net_ctx->data_mgr, s_tls_ctx->ca_pkey);

            remove(CA_CERT_FILE);
            remove(CA_PKEY_FILE);
        }

        netdata_get_tls_priv_cert(net_ctx->data_mgr, s_tls_ctx->priv_cert, sizeof(s_tls_ctx->priv_cert));
        if ( STRING_IS_EMPTY(s_tls_ctx->priv_cert) )
        {
            NET_DEBUG("first start, using the firmware default certificate");
            tls_update_priv_cert(TLS_PRIV_CERT_DEF, TLS_PRIV_PSWD_DEF, 1);
        }
        else
        {
            if ( strcmp(s_tls_ctx->priv_cert, TLS_PRIV_CERT_DEF) == 0 )
            {
                NET_DEBUG("using firmware default certificate");
                s_tls_ctx->is_def = 1;
            }
            else
            {
                NET_DEBUG("using self-signed certificate");
                s_tls_ctx->is_def = 0;
            }
        }

        netdata_get_tls_priv_pswd(net_ctx->data_mgr, password, sizeof(password));
        if ( STRING_IS_EMPTY(password) )
        {
            NET_WARN("using the firmware private password(%s)", TLS_PRIV_PSWD_DEF);
            snprintf(password, sizeof(password), TLS_PRIV_PSWD_DEF);
            netdata_set_tls_priv_pswd(net_ctx->data_mgr, password);
        }

        /* 将证书内容写入临时文件，并在qio_ssl_init完成后删除文件 */
        save_file(TLS_CERT_FILE, s_tls_ctx->priv_cert, strlen(s_tls_ctx->priv_cert));
        ret = qio_ssl_init(TLS_CERT_FILE, password);
        remove(TLS_CERT_FILE);
    }
    while ( 0 );

    if ( ret < 0 )
    {
        NET_WARN("nettls_init failed(%d) !!!", ret);
        if (s_tls_ctx->mutex != INVALIDMTX)
        {
            pi_mutex_destroy(s_tls_ctx->mutex);
        }
        pi_free(s_tls_ctx);
        s_tls_ctx = NULL;
    }
    return ret;
}

void nettls_deinit(void)
{
    if ( s_tls_ctx != NULL )
    {
        pi_free(s_tls_ctx);
    }
    qio_ssl_deinit();
}
/**
 *@}
 */
