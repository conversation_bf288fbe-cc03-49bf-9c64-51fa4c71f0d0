/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file on.h
 * @addtogroup process
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief on init
 */
#ifndef _ON_H_
#define _ON_H_

#include <quickjs.h>

/**
 * @brief   app process on create
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_on_create(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on start
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_on_start(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on resume
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_on_resume(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on pause
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_on_pause(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on stop
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_on_stop(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on destroy
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_on_destroy(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on back
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_process_on_back(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

/**
 * @brief   app process on front
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_process_on_front(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

#endif /* _ON_H_ */

/**
 * @}
 */
 

