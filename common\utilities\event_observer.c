/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_observer.c
 * @addtogroup utilities
 * @{
 * @addtogroup msgrouter
 * <AUTHOR>
 * @date 2023-06-21
 * @version v1.0
 * @brief event observer API
 */

#include "pol/pol_define.h"
#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "utilities/event_observer.h"

#define OBS_LOG(fmt, ...)    pi_log_e( fmt "\n", ##__VA_ARGS__)

EVENT_OBSERVER_S* observer_construct(uint32_t id, void* data, NOTIFY_CALLBACK cb)
{
    EVENT_OBSERVER_S*   observer;

    observer = (EVENT_OBSERVER_S *)pi_zalloc(sizeof(EVENT_OBSERVER_S));
    RETURN_VAL_IF(observer == NULL, OBS_LOG, NULL);

    pi_init_list_head(&(observer->observer_node));
    observer->observer_id   = id;
    observer->observer_data = data;
    observer->callback      = cb;

    return observer;
}

EVENT_OBSERVER_S* observer_copy(EVENT_OBSERVER_S* observer)
{
    RETURN_VAL_IF(observer == NULL, OBS_LOG, NULL);

    return observer_construct(observer->observer_id, observer->observer_data, observer->callback);
}

void observer_attach_to_subject(EVENT_OBSERVER_S* observer, EVENT_SUBJECT_S* subject)
{
    struct list_head*   pos;
    struct list_head*   n;
    EVENT_OBSERVER_S*   o;

    RETURN_IF(subject == NULL || observer == NULL, OBS_LOG);

    pi_list_for_each_safe(pos, n, &(subject->observer_head))
    {
        o = pi_list_entry(pos, EVENT_OBSERVER_S, observer_node);
        RETURN_IF(o == observer, OBS_LOG);
    }

    pi_mutex_lock(subject->mutex);
    pi_list_add_tail(&(observer->observer_node), &(subject->observer_head));
    pi_mutex_unlock(subject->mutex);
}

void observer_detach_from_subject(EVENT_OBSERVER_S* observer, EVENT_SUBJECT_S* subject)
{
    struct list_head*   pos;
    struct list_head*   n;

    RETURN_IF(subject == NULL || observer == NULL, OBS_LOG);

    pi_mutex_lock(subject->mutex);
    pi_list_for_each_safe(pos, n, &(subject->observer_head))
    {
        if ( pos == &(observer->observer_node) )
        {
            pi_list_del_entry(pos);
        }
    }
    pi_mutex_unlock(subject->mutex);
}

void observer_destruct(EVENT_OBSERVER_S* observer)
{
    pi_free(observer);
}

EVENT_SUBJECT_S* subject_construct(uint32_t id, void* data)
{
    EVENT_SUBJECT_S*    subject;

    subject = (EVENT_SUBJECT_S *)pi_zalloc(sizeof(EVENT_SUBJECT_S));
    RETURN_VAL_IF(subject == NULL, OBS_LOG, NULL);

    subject->mutex = pi_mutex_create();
    if ( subject->mutex == INVALIDMTX )
    {
        OBS_LOG("create mutex failed\n");
        pi_free(subject);
        return NULL;
    }

    pi_init_list_head(&(subject->observer_head));
    subject->subject_id     = id;
    subject->subject_data   = data;
    subject->subject_status = 0;

    return subject;
}

void subject_notify_observers(EVENT_SUBJECT_S* subject)
{
    EVENT_OBSERVER_S*   observer;
    struct list_head*   pos;
    struct list_head*   n;

    RETURN_IF(subject == NULL, OBS_LOG);

    pi_mutex_lock(subject->mutex);
    pi_list_for_each_safe(pos, n, &(subject->observer_head))
    {
        observer = pi_list_entry(pos, EVENT_OBSERVER_S, observer_node);
        if ( observer->callback )
        {
            observer->callback(observer, subject);
        }
    }
    pi_mutex_unlock(subject->mutex);
}

void subject_destruct(EVENT_SUBJECT_S* subject)
{
    EVENT_OBSERVER_S*   observer;
    struct list_head*   pos;
    struct list_head*   n;

    RETURN_IF(subject == NULL, OBS_LOG);

    pi_mutex_lock(subject->mutex);
    pi_list_for_each_safe(pos, n, &(subject->observer_head))
    {
        observer = pi_list_entry(pos, EVENT_OBSERVER_S, observer_node);
        pi_list_del_entry(pos);
        pi_free(observer);
    }
    pi_mutex_unlock(subject->mutex);

    pi_mutex_destroy(subject->mutex);
    pi_free(subject);
}
/**
 *@}
 */
