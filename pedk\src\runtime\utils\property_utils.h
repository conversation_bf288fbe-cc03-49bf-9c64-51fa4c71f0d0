/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file propert_utils.h
 * @addtogroup utils
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief propert utils 
 */
#include "basic/config.h"
#include "cJSON.h"

typedef enum {
    APP_RUN_BACKEND,
    APP_RUN_FRONTEND,
} APP_STATE;

typedef struct PTIME {
    int64_t time_v;
    char*   time_str;
} PTIME;

// APP属性
#define PROPERTY_LIST "rtid,appname,state,start_time,end_time,during_time"

typedef struct DynamicProperty {
    char* app_name;       ///< 应用程序名
    uint8_t rtid;         ///< rtid
    APP_STATE run_state;  ///< APP运行状态
    int64_t malloc_size;    ///< 动态内存的大小
    PTIME start_time;     ///< APP运行开始时间
    PTIME duration_time;  ///< APP已持续时间
} DynamicProperty;

//从文件中获取静态属性
/**
 * @brief   get static property buffer from file
 * @param[in] *property_path :property path
 * @param[in] *length :string len
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
uint8_t* get_static_property_buffer_from_file(char* property_path,uint16_t *length );

//动态属性结构体转json格式
/**
 * @brief   struct to json dynamic
 * @param[in] *dy_prop :Dynamic Property
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
cJSON* struct_to_json_dynamic(DynamicProperty *dy_prop);

// 差分时间转字符串
/**
 * @brief   sys diff time to str
 * @param[in] diff_time :diff time
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
char* sys_diff_time_to_str(int64_t diff_time);
/**
 * @}
 */

