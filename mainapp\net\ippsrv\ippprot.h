//*****************************************************************************
//  Copyright (C) 2014 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/ipp/ippprot.h#11 $
//  $Change: 243377 $ $Date: 2014/11/05 $
//  
/// @file
/// IPP (Internet Printing Protocol) protocol defines
///
/// @ingroup Network
//  
//*****************************************************************************
#ifndef IPPPROT_H
#define IPPPROT_H 1

#ifdef __cplusplus
extern "C" {
#endif

/// Version supported.  It is assumed that all version equal and under
/// are supported by this code
///
#define IPP_MAX_MAJOR  0x02
#define IPP_MAX_MINOR  0x01

#define IPP_MAX_ENCRES  12
#define IPP_MAX_VALUES  8

#define IPP_MAX_PAGERANGES 16

// this is port specific, and is number if possible media sizes supported
// we need arrays of collections to hold at least this many media items
// so make this the max collection array size (as defined in ippx.h)
//
#define IPP_MAX_MEDIA   IPP_MAX_COLLECTION_SET

typedef enum
{
    IPP_TAG_ZERO = 0x00,            /// Zero tag - used for separators
    IPP_TAG_OPERATION,              /// Operation group
    IPP_TAG_JOB,                    /// Job group
    IPP_TAG_END,                    /// End-of-attributes
    IPP_TAG_PRINTER,                /// Printer group
    IPP_TAG_UNSUPPORTED_GROUP,      /// Unsupported attributes group
    IPP_TAG_SUBSCRIPTION,           /// Subscription group
    IPP_TAG_EVENT_NOTIFICATION,     /// Event group
    IPP_TAG_UNSUPPORTED_VALUE = 0x10,   /// Unsupported value
    IPP_TAG_DEFAULT,                /// Default value
    IPP_TAG_UNKNOWN,                /// Unknown value
    IPP_TAG_NOVALUE,                /// No-value value
    IPP_TAG_NOTSETTABLE = 0x15,     /// Not-settable value
    IPP_TAG_DELETEATTR,             /// Delete-attribute value
    IPP_TAG_ADMINDEFINE,            /// Admin-defined value
    IPP_TAG_INTEGER = 0x21,         /// Integer value
    IPP_TAG_BOOLEAN,                /// Boolean value
    IPP_TAG_ENUM,                   /// Enumeration value
    IPP_TAG_STRING = 0x30,          /// Octet string value
    IPP_TAG_DATE,                   /// Date/time value
    IPP_TAG_RESOLUTION,             /// Resolution value
    IPP_TAG_RANGE,                  /// Range value
    IPP_TAG_BEGIN_COLLECTION,       /// Beginning of collection value
    IPP_TAG_TEXTLANG,               /// Text-with-language value
    IPP_TAG_NAMELANG,               /// Name-with-language value
    IPP_TAG_END_COLLECTION,         /// End of collection value
    IPP_TAG_TEXT = 0x41,            /// Text value
    IPP_TAG_NAME,                   /// Name value
    IPP_TAG_KEYWORD = 0x44,         /// Keyword value
    IPP_TAG_URI,                    /// URI value
    IPP_TAG_URISCHEME,              /// URI scheme value
    IPP_TAG_CHARSET,                /// Character set value
    IPP_TAG_LANGUAGE,               /// Language value
    IPP_TAG_MIMETYPE,               /// MIME media type value
    IPP_TAG_MEMBERNAME              /// Collection member name value
}
IPPTAG;

typedef enum
{
    IPP_RES_PER_INCH = 3,           /// Pixels per inch
    IPP_RES_PER_CM                  /// Pixels per centimeter
}
IPPRES;

typedef enum
{
    IPP_FINISHINGS_NONE = 3,            /// No finishing
    IPP_FINISHINGS_STAPLE,              /// Staple (any location)
    IPP_FINISHINGS_PUNCH,               /// Punch (any location/count)
    IPP_FINISHINGS_COVER,               /// Add cover
    IPP_FINISHINGS_BIND,                /// Bind
    IPP_FINISHINGS_SADDLE_STITCH,       /// Staple interior
    IPP_FINISHINGS_EDGE_STITCH,         /// Stitch along any side
    IPP_FINISHINGS_FOLD,                /// Fold (any type)
    IPP_FINISHINGS_TRIM,                /// Trim (any type)
    IPP_FINISHINGS_BALE,                /// Bale (any type)
    IPP_FINISHINGS_BOOKLET_MAKER,       /// Fold to make booklet
    IPP_FINISHINGS_JOB_OFFSET,          /// Offset for binding (any type)
    IPP_FINISHINGS_STAPLE_TOP_LEFT = 20,/// Staple top left corner
    IPP_FINISHINGS_STAPLE_BOTTOM_LEFT,  /// Staple bottom left corner
    IPP_FINISHINGS_STAPLE_TOP_RIGHT,    /// Staple top right corner
    IPP_FINISHINGS_STAPLE_BOTTOM_RIGHT, /// Staple bottom right corner
    IPP_FINISHINGS_EDGE_STITCH_LEFT,    /// Stitch along left side
    IPP_FINISHINGS_EDGE_STITCH_TOP,     /// Stitch along top edge
    IPP_FINISHINGS_EDGE_STITCH_RIGHT,   /// Stitch along right side
    IPP_FINISHINGS_EDGE_STITCH_BOTTOM,  /// Stitch along bottom edge
    IPP_FINISHINGS_STAPLE_DUAL_LEFT,    /// Two staples on left
    IPP_FINISHINGS_STAPLE_DUAL_TOP,     /// Two staples on top
    IPP_FINISHINGS_STAPLE_DUAL_RIGHT,   /// Two staples on right
    IPP_FINISHINGS_STAPLE_DUAL_BOTTOM,  /// Two staples on bottom
    IPP_FINISHINGS_BIND_LEFT = 50,      /// Bind on left
    IPP_FINISHINGS_BIND_TOP,            /// Bind on top
    IPP_FINISHINGS_BIND_RIGHT,          /// Bind on right
    IPP_FINISHINGS_BIND_BOTTOM          /// Bind on bottom
}
IPPFINISHING;

typedef enum
{
    IPP_PORTRAIT = 3,                   /// No rotation
    IPP_LANDSCAPE,                      /// 90 degrees counter-clockwise
    IPP_REVERSE_LANDSCAPE,              /// 90 degrees clockwise
    IPP_REVERSE_PORTRAIT                /// 180 degrees
}
IPPORIENT;

typedef enum
{
    IPP_QUALITY_DRAFT = 3,              /// Draft quality
    IPP_QUALITY_NORMAL,                 /// Normal quality
    IPP_QUALITY_HIGH                    /// High quality
}
IPPQUALITY;

typedef enum
{
    IPP_JOB_PENDING = 3,                /// Job is waiting to be printed
    IPP_JOB_HELD,                       /// Job is held for printing
    IPP_JOB_PROCESSING,                 /// Job is currently printing
    IPP_JOB_STOPPED,                    /// Job has been stopped
    IPP_JOB_CANCELED,                   /// Job has been canceled
    IPP_JOB_ABORTED,                    /// Job has aborted due to error
    IPP_JOB_COMPLETED                   /// Job has completed successfully
}
IPPJOBSTATE;

typedef enum
{
    IPP_PRINTER_IDLE = 3,               /// Printer is idle
    IPP_PRINTER_PROCESSING,             /// Printer is working
    IPP_PRINTER_STOPPED                 /// Printer is stopped
}
IPPPRINTERSTATE;

typedef enum
{
    IPP_ERROR = -1,                     /// An error occurred
    IPP_IDLE,                           /// Nothing is happening/request completed
    IPP_HEADER,                         /// The request header needs to be sent/received
    IPP_ATTRIBUTE,                      /// One or more attributes need to be sent/received
    IPP_DATA                            /// IPP request data needs to be sent/received
}
IPPSTATE;

typedef enum
{
    IPP_PRINT_JOB = 0x0002,             /// Print a single file
    IPP_PRINT_URI,                      /// Print a single URL
    IPP_VALIDATE_JOB,                   /// Validate job options
    IPP_CREATE_JOB,                     /// Create an empty print job
    IPP_SEND_DOCUMENT,                  /// Add a file to a job
    IPP_SEND_URI,                       /// Add a URL to a job
    IPP_CANCEL_JOB,                     /// Cancel a job
    IPP_GET_JOB_ATTRIBUTES,             /// Get job attributes
    IPP_GET_JOBS,                       /// Get a list of jobs
    IPP_GET_PRINTER_ATTRIBUTES,         /// Get printer attributes
    IPP_HOLD_JOB,                       /// Hold a job for printing
    IPP_RELEASE_JOB,                    /// Release a job for printing
    IPP_RESTART_JOB,                    /// Reprint a job
    IPP_PAUSE_PRINTER = 0x0010,         /// Stop a printer
    IPP_RESUME_PRINTER,                 /// Start a printer
    IPP_PURGE_JOBS,                     /// Cancel all jobs
    IPP_SET_PRINTER_ATTRIBUTES,         /// Set printer attributes
    IPP_SET_JOB_ATTRIBUTES,             /// Set job attributes
    IPP_GET_PRINTER_SUPPORTED_VALUES,   /// Get supported attribute values
    IPP_CREATE_PRINTER_SUBSCRIPTION,    /// Create a printer subscription
    IPP_CREATE_JOB_SUBSCRIPTION,        /// Create a job subscription
    IPP_GET_SUBSCRIPTION_ATTRIBUTES,    /// Get subscription attributes
    IPP_GET_SUBSCRIPTIONS,              /// Get list of subscriptions
    IPP_RENEW_SUBSCRIPTION,             /// Renew a printer subscription
    IPP_CANCEL_SUBSCRIPTION,            /// Cancel a subscription
    IPP_GET_NOTIFICATIONS,              /// Get notification events
    IPP_SEND_NOTIFICATIONS,             /// Send notification events
    IPP_GET_PRINT_SUPPORT_FILES = 0x0021,/// Get printer support files
    IPP_ENABLE_PRINTER,                 /// Start a printer
    IPP_DISABLE_PRINTER,                /// Stop a printer
    IPP_PAUSE_PRINTER_AFTER_CURRENT_JOB,/// Stop printer after the current job
    IPP_HOLD_NEW_JOBS,                  /// Hold new jobs
    IPP_RELEASE_HELD_NEW_JOBS,          /// Release new jobs
    IPP_DEACTIVATE_PRINTER,             /// Stop a printer
    IPP_ACTIVATE_PRINTER,               /// Start a printer
    IPP_RESTART_PRINTER,                /// Restart a printer
    IPP_SHUTDOWN_PRINTER,               /// Turn a printer off
    IPP_STARTUP_PRINTER,                /// Turn a printer on
    IPP_REPROCESS_JOB,                  /// Reprint a job
    IPP_CANCEL_CURRENT_JOB,             /// Cancel the current job
    IPP_SUSPEND_CURRENT_JOB,            /// Suspend the current job
    IPP_RESUME_JOB,                     /// Resume the current job
    IPP_PROMOTE_JOB,                    /// Promote a job to print sooner
    IPP_SCHEDULE_JOB_AFTER,             /// Schedule a job to print after another
    IPP_IDENTIFY_PRINTER = 0x003C,      /// Identify a printer by having the printer buzz or flash
    IPP_PRIVATE = 0x4000,               /// Reserved
}
IPPOPCODE;

typedef enum
{
    IPP_OK = 0x0000,                    /// successful-ok
    IPP_OK_SUBST,                       /// successful-ok-ignored-or-substituted-attributes
    IPP_OK_CONFLICT,                    /// successful-ok-conflicting-attributes
    IPP_OK_IGNORED_SUBSCRIPTIONS,       /// successful-ok-ignored-subscriptions
    IPP_OK_IGNORED_NOTIFICATIONS,       /// successful-ok-ignored-notifications
    IPP_OK_TOO_MANY_EVENTS,             /// successful-ok-too-many-events
    IPP_OK_BUT_CANCEL_SUBSCRIPTION,     /// successful-ok-but-cancel-subscription
    IPP_OK_EVENTS_COMPLETE,             /// successful-ok-events-complete
    IPP_REDIRECTION_OTHER_SITE = 0x200, /// redirection-other-site
    IPP_BAD_REQUEST = 0x0400,           /// client-error-bad-request
    IPP_FORBIDDEN,                      /// client-error-forbidden
    IPP_NOT_AUTHENTICATED,              /// client-error-not-authenticated
    IPP_NOT_AUTHORIZED,                 /// client-error-not-authorized
    IPP_NOT_POSSIBLE,                   /// client-error-not-possible
    IPP_TIMEOUT,                        /// client-error-timeout
    IPP_NOT_FOUND,                      /// client-error-not-found
    IPP_GONE,                           /// client-error-gone
    IPP_REQUEST_ENTITY,                 /// client-error-request-entity-too-large
    IPP_REQUEST_VALUE,                  /// client-error-request-value-too-long
    IPP_DOCUMENT_FORMAT,                /// client-error-document-format-not-supported
    IPP_ATTRIBUTES,                     /// client-error-attributes-or-values-not-supported
    IPP_URI_SCHEME,                     /// client-error-uri-scheme-not-supported
    IPP_CHARSET,                        /// client-error-charset-not-supported
    IPP_CONFLICT,                       /// client-error-conflicting-attributes
    IPP_COMPRESSION_NOT_SUPPORTED,      /// client-error-compression-not-supported
    IPP_COMPRESSION_ERROR,              /// client-error-compression-error
    IPP_DOCUMENT_FORMAT_ERROR,          /// client-error-document-format-error
    IPP_DOCUMENT_ACCESS_ERROR,          /// client-error-document-access-error
    IPP_ATTRIBUTES_NOT_SETTABLE,        /// client-error-attributes-not-settable
    IPP_IGNORED_ALL_SUBSCRIPTIONS,      /// client-error-ignored-all-subscriptions
    IPP_TOO_MANY_SUBSCRIPTIONS,         /// client-error-too-many-subscriptions
    IPP_IGNORED_ALL_NOTIFICATIONS,      /// client-error-ignored-all-notifications
    IPP_PRINT_SUPPORT_FILE_NOT_FOUND,   /// client-error-print-support-file-not-found
    IPP_DOCUMENT_UNPRINTABLE_ERROR = 0x041B, //client-error-document-unprintable-error
    IPP_INTERNAL_ERROR = 0x0500,        /// server-error-internal-error
    IPP_OPERATION_NOT_SUPPORTED,        /// server-error-operation-not-supported
    IPP_SERVICE_UNAVAILABLE,            /// server-error-service-unavailable
    IPP_VERSION_NOT_SUPPORTED,          /// server-error-version-not-supported
    IPP_DEVICE_ERROR,                   /// server-error-device-error
    IPP_TEMPORARY_ERROR,                /// server-error-temporary-error
    IPP_NOT_ACCEPTING,                  /// server-error-not-accepting-jobs
    IPP_PRINTER_BUSY,                   /// server-error-busy
    IPP_ERROR_JOB_CANCELED,             /// server-error-job-canceled
    IPP_MULTIPLE_JOBS_NOT_SUPPORTED,    /// server-error-multiple-document-jobs-not-supported
    IPP_PRINTER_IS_DEACTIVATED          /// server-error-printer-is-deactivated
}
IPPSTATUS;

#ifdef __cplusplus
}
#endif
#endif

