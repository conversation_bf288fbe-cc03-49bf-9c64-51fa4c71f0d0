#ifndef __WSD_SUBS_H__
#define __WSD_SUBS_H__

#include "wsd.h"
#include "qxml.h"

/** how many subscriptions allowed to in one service
 *
 */
#define WSD_MAX_SUBSCRIPTIONS       64

/**
 * Event subscription structure
 */

/** event types we recognize
 */
// it would be cleaner if printer/scanner/etc. events
// were local to their domains, but WSD stinks all over anyway
// so I am not going to be picky here
//
typedef enum
{
    evtNone,
    evtPrinterChanged,
    evtPrinterStatus,
    evtPrinterError,
    evtPrinterErrorCleared,
    evtJobStatus,
    evtJobEndState,

    evtScanAvailable,
    evtScannerChanged,
    evtScannerStatus,
    evtScannerError,
    evtScannerErrorCleared
}
WSD_EVT_E;

typedef struct wsd_subscription_list WSD_SUBS_LIST_S;

WSD_SUBS_LIST_S* wsd_subs_list_create();
void wsd_subs_list_destroy(WSD_SUBS_LIST_S*);

int wsd_subs_list_is_empty(WSD_SUBS_LIST_S* subs_list);
int wsd_is_subscription_action(const char* action);
int wsd_do_subscription(QXML_S* pxml, const char* action, const char* id, WSD_SERVICE_DATA_S* srv_data, WSD_SUBS_LIST_S* subs_list);
int32_t wsd_subs_send_event(WSD_SUBS_LIST_S* subs_list, WSD_EVT_E evt, SOAP_VAR_S* pvar, const char* xmlns, const char* xmlnsurl);
#endif

