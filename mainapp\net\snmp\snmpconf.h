#ifndef _SNMPCONF_H_
#define _SNMPCONF_H_

#define SNMP_CONF_HEAD \
	"agentAddress udp:161,udp6:161\n"						    \
    "view systemview included .*******.2.1\n"                   \
    "view systemview included .*******.********.*******.1\n"	\
    "view systemview included .*******.4.1.2699.1.2.1\n"		\
    "view systemview included .*******.4.1.40093 \n"			\
    "view systemview included .*******.*******.6.1\n"			\
    "view systemview included .*******.********\n"			    \
    "view systemview included .*******.********\n"			    \
    "view systemview included .*******.********\n"			    \
    "view systemview included .*******.********\n"			    \
    "\n" \
    "sysDescr        %s\n" \
    "sysObjectID     *******.4.1.40093.10\n" \
    "sysLocation     %s\n" \
    "sysContact      %s\n" \
    "sysName         %s\n" \
    "sysServices     72\n" \
    "\n"

// SNMP support v1, v2.
#define SNMPD_V1_V2_CONF \
	"rocommunity %s default -V systemview\n" \
    "rocommunity6 %s default -V systemview\n" \
    "rocommunity %s default -V systemview\n" \
    "rocommunity6 %s default -V systemview\n" \
    "\n" \
    "com2sec snmpv2c_user  default     %s\n" \
    "com2sec snmpv1_user   default     %s\n" \
    "group   v2c_group  v2c    snmpv2c_user\n" \
    "group   v1_group   v1     snmpv1_user\n" \
    "access  v2c_group   \"\"   any   noauth   exact  systemview    none         none\n" \
    "access  v1_group    \"\"   any   noauth   exact  systemview    none         none\n" \
    "trapsink  localhost  public\n"

// SNMP only support v3.
#define SNMPD_ONLY_V3_CONF \
    "createUser %s MD5 \"%s\" DES \"%s\"\n" \
    "group   v3_group   usm    %s\n" \
    "access  v3_group    \"\"   any   priv     exact  systemview    systemview   none\n" \
    "trapsink  localhost  public\n"

// SNMP support v1, v2, v3.
#define SNMPD_ALL_CONF \
    "rocommunity %s default -V systemview\n" \
    "rocommunity6 %s default -V systemview\n" \
    "rocommunity %s default -V systemview\n" \
    "rocommunity6 %s default -V systemview\n" \
    "\n" \
    "createUser %s MD5 \"%s\" DES \"%s\"\n" \
    "com2sec snmpv2c_user  default     %s\n" \
    "com2sec snmpv1_user   default     %s\n" \
    "com2sec snmpv3_user   default     %s\n" \
    "group   v2c_group  v2c    snmpv2c_user\n" \
    "group   v1_group   v1     snmpv1_user\n" \
    "group   v3_group   usm    %s\n" \
    "access  v2c_group   \"\"   any   noauth   exact  systemview    none         none\n" \
    "access  v1_group    \"\"   any   noauth   exact  systemview    none         none\n" \
    "access  v3_group    \"\"   any   priv     exact  systemview    systemview   none\n" \
    "trapsink  localhost  public\n"

#endif /* _SNMPCONF_H_ */

