/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       curl_utils2.h
 * @addtogroup 
 * @{
 * <AUTHOR>
 * @date    2023/12/12
 * @version v1.0
 * @details 
 * 
 */

#ifndef PESF_NET_CURL_UTILS_H
#define PESF_NET_CURL_UTILS_H

#include "net_private.h"

#include <quickjs.h>
#include <cutils.h> // for DynBuf TODO 改为自有的去掉对quickjs库的依赖
#include <curl/curl.h>
#include <uv.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void (*pesfCurlDoneCallback)(CURLMsg *message, void *arg);
typedef struct {
    uint32_t magic;
    void *arg;
    pesfCurlDoneCallback done_cb;

    CURL*  curl_h;
    CURLM* curlm_h;
    uv_timer_t uv_timer;
    struct uv_loop_s* uv_loop;

} STPeSFCurlPrivate;

CURL* pesfCurlEasyInit(CURL* curl_h);
CURLM* pesfGetCurlm(STPeSFCurlPrivate* x);

#ifdef __cplusplus
}  /* End of the 'extern "C"' block */
#endif

#endif //PESF_NET_CURL_UTILS_H

/**
*@}
*/