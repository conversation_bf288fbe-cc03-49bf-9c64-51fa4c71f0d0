/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_msg_typedef.h
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2023-07-13
 * @brief event message typedef
 */
#ifndef EVENT_MSG_TYPEDEF_H
#define EVENT_MSG_TYPEDEF_H

#include "pol/pol_types.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

/*
 *@brief changed mask in NET_IPV4_CONF_S
 */
typedef enum
{
    IPV4_CONF_ADDRESS   = 1 << 0,
    IPV4_CONF_MASK      = 1 << 1,
    IPV4_CONF_GATEWAY   = 1 << 2,
}
IPV4_CONF_E;

/*
 *@brief data format used by EVT_TYPE_NET_ETH_IPV4_CONFIG_REQUEST or EVT_TYPE_NET_WIFI_IPV4_CONFIG_REQUEST
 */
typedef struct
{
    uint8_t     dhcp_enabled;       ///< 0 - disabled; 1 - enabled
    uint8_t     changed;            ///< IPV4_CONF_E 0x01(address) | 0x02(mask) | 0x04(gateway)
    char        address[16];        ///< "xxx.xxx.xxx.xxx"
    char        mask[16];           ///< "xxx.xxx.xxx.xxx"
    char        gateway[16];        ///< "xxx.xxx.xxx.xxx"
}
NET_IPV4_CONF_S;

/*
 *@brief changed mask in NET_DNSV4_CONF_S
 */
typedef enum
{
    IPV4DNS_CONF_DNS0   = 1 << 0,
    IPV4DNS_CONF_DNS1   = 1 << 1,
}
IPV4DNS_CONF_E;

/*
 *@brief data format used by EVT_TYPE_NET_ETH_DNSV4_CONFIG_REQUEST or EVT_TYPE_NET_WIFI_DNSV4_CONFIG_REQUEST
 */
typedef struct
{
    uint8_t     autodns;            ///< 0 - disabled; 1 - enabled
    uint8_t     changed;            ///< IPV4DNS_CONF_E 0x01(dns0) | 0x02(dns1)
    char        dns0[16];           ///< "xxx.xxx.xxx.xxx"
    char        dns1[16];           ///< "xxx.xxx.xxx.xxx"
}
NET_DNSV4_CONF_S;

/*
 *@brief data format used by EVT_TYPE_NET_ETH_IPV6_CONFIG_REQUEST or EVT_TYPE_NET_WIFI_IPV6_CONFIG_REQUEST
 */
typedef struct
{
    uint8_t     enabled;            ///< 0 - disabled; 1 - enabled
    uint8_t     dhcp_enabled;       ///< 0 - disabled; 1 - enabled
}
NET_IPV6_CONF_S;

/*
 *@brief WiFi-Station security mode.
 */
typedef enum
{
    SEC_OPEN = 0,
    SEC_WPA,
    SEC_WPAWPA2,
    SEC_WPA2,
    SEC_WPA2WPA3,
    SEC_WPA3,

    SEC_AUTO = 0xff,
}
WIFI_SEC_MODE_E;

/*
 *@brief WiFi-Station mode
 */
typedef enum
{
    WIFI_ERR = 0,                   ///< 无法识别
    WIFI_ADH,                       ///< AD Hoc
    WIFI_INF,                       ///< 基础连接方式
    WIFI_P2P,                       ///< P2P方式
}
WIFI_CONN_MODE_E;

/*
 *@brief data format used by EVT_TYPE_NET_WIFI_CONNECT_REQUEST
 */
typedef struct
{
    char        ssid[128];          ///< support UTF-8 or GBK
    uint8_t     sec_mode;           ///< WIFI_SEC_MODE_E
    char        psk[65];            ///< valid length 8 ~ 64
}
WIFI_CONN_CONF_S;

/*
 *@brief data format used by EVT_TYPE_NET_WIFI_CONNECTION_CHANGED
 */
typedef struct
{
    uint32_t    status;             ///< NETLINK_STATUS_E in network.h
    uint32_t    detail;             ///< WIFI_CONNECTION_DETAIL_E in network.h
    char        ssid[128];          ///< support UTF-8
    char        bssid[18];          ///< "xx:xx:xx:xx:xx:xx"
    uint8_t     sec_mode;           ///< WIFI_SEC_MODE_E
    uint8_t     channel;            ///< The channel used for current connection.
    uint8_t     mode;               ///< WIFI_CONN_MODE_E
}
WIFI_CONN_INFO_S;

/*
 *@brief data format used by EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED
 */
typedef struct
{
    char        ssid[128];
    char        bssid[18];
    uint8_t     sec_mode;
    uint8_t     channel;
    uint8_t     sig_lvl;
    uint8_t     mode;
}
WIFI_AP_INFO_S;

typedef struct
{
    WIFI_AP_INFO_S  ap_info[50];
    uint32_t        ap_count;
}
WIFI_SCAN_RESULT_S;

/*
 *@brief data format used by EVT_TYPE_NET_IPP_IDENTIFY_ACTION_CHANGED
 */
typedef enum
{
    IDENTIFY_ACTION_NONE    = 0,
    IDENTIFY_ACTION_DISPLAY = 1 << 0,
    IDENTIFY_ACTION_FLASH   = 1 << 1,
    IDENTIFY_ACTION_SOUND   = 1 << 2,
}
IPP_IDENTIFY_ACTION_E;

/*
 *@brief data format used by EVT_TYPE_NET_SMTP_CONFIG_REQUEST
 */
typedef struct
{
    char        sender_addr[64];    ///< sender email address
    char        server_addr[64];    ///< server address or domain
    uint16_t    server_port;        ///< valid value 0 ~ 65535
    uint8_t     server_auth;        ///< 0 - close; 1 - open
    uint8_t     sec_mode;           ///< 0 - none; 1 - ssl; 2 - starttls
    char        username[64];
    char        password[21];
}
NET_SMTP_CONF_S;

/*
 *@brief data format of a mail group info, used by EVT_TYPE_NET_ADDRESS_BOOK_REQUEST or EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED
 */
typedef struct
{
    int32_t     mail_id[60];        ///< 当前群组中包含的Email的唯一标识（对应 MAIL_ADDRINFO_S 中的 record_id）
    char        name[64];           ///< 群组名称
    int32_t     record_id;          ///< 群组唯一标识
}
MAIL_GROUPINFO_S;

/*
 *@brief data format of a mail address info, used by EVT_TYPE_NET_ADDRESS_BOOK_REQUEST or EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    int32_t     group_id[10];       ///< 当前Email所属群组的唯一标识（对应 MAIL_GROUPINFO_S 中的 record_id）
    char        name[64];           ///< Email名称
    char        addr[256];          ///< Email地址
    int32_t     record_id;          ///< Email唯一标识
}
MAIL_ADDRINFO_S;

/*
 *@brief data format of a ftp address info, used by EVT_TYPE_NET_ADDRESS_BOOK_REQUEST or EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    char        server_name[128+1];    ///< FTP服务器名称
    char        server_addr[32];    ///< FTP服务器地址
    char        server_path[64];    ///< FTP服务器子目录
    uint16_t    server_port;        ///< FTP服务器端口
    uint16_t    anonymity;          ///< 是否匿名登陆
    char        login_name[64];     ///< 登陆用户名
    char        login_pswd[32];     ///< 登陆密码
    int32_t     record_id;          ///< FTP唯一标识
}
FTP_ADDRINFO_S;

/*
 *@brief data format of a samba address info, used by EVT_TYPE_NET_ADDRESS_BOOK_REQUEST or EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    char        server_name[128+1];  ///< Samba服务器名称
    char        server_addr[32+1];  ///< Samba服务器地址
    char        server_path[128+1]; ///< Samba服务器子目录
    uint16_t    server_port;        ///< Samba服务器端口
    uint16_t    anonymity;          ///< 是否匿名登陆
    char        login_name[128+1];  ///< 登陆用户名
    char        login_pswd[32+1];   ///< 登陆密码
    int32_t     record_id;          ///< Samba唯一标识
}
SMB_ADDRINFO_S;

/*
 *@brief data format of a whitelist address info, used by EVT_TYPE_NET_ADDRESS_BOOK_REQUEST or EVT_TYPE_NET_WHITELIST_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    char        ip[16];     ///< 白名单ipv4
    char        mac[18];    ///< 白名单mac
    int32_t     record_id;  ///< 白名单唯一标识
}
WHITELIST_ADDRINFO_S;
/*
 *@brief the operation of EVT_TYPE_NET_ADDRESS_BOOK_REQUEST
 */
typedef enum
{
    AB_OP_INVALID = 0,              ///< 地址簿操作 - 无效操作
    AB_OP_ADD,                      ///< 地址簿操作 - 添加
    AB_OP_DELETE,                   ///< 地址簿操作 - 删除
    AB_OP_UPDATE,                   ///< 地址簿操作 - 修改
    AB_OP_MAX
}
ADDRBOOK_OPERATION_E;

/*
 *@brief the type of EVT_TYPE_NET_ADDRESS_BOOK_REQUEST
 */
typedef enum
{
    AB_TYPE_MAIL_GROUP = 0,         ///< 地址簿类型 - 邮件群组
    AB_TYPE_MAIL,                   ///< 地址簿类型 - 邮件
    AB_TYPE_FTP,                    ///< 地址簿类型 - FTP
    AB_TYPE_SMB,                    ///< 地址簿类型 - Samba
    AB_TYPE_WL,                     ///< 地址簿类型 - 白名单

    AB_TYPE_MAX
}
ADDRBOOK_TYPE_E;

/*
 *@brief data format used by EVT_TYPE_NET_ADDRESS_BOOK_REQUEST
 */
typedef struct
{
    ADDRBOOK_OPERATION_E    operation;      ///< 地址簿操作
    ADDRBOOK_TYPE_E         type;           ///< 地址簿类型
    int32_t                 delete_id[60];  ///< 批量删除的 record_id 数组，当 operation == AB_OP_DELETE 时有效
    int32_t                 whitelist_delete_id[999];  ///< 批量删除的 record_id 数组，当 operation == AB_OP_DELETE 时有效
    union
    {
        MAIL_GROUPINFO_S        group_info;     ///< 邮件群组信息，当 type == AB_TYPE_MAIL_GROUP 时有效
        MAIL_ADDRINFO_S         mail_info;      ///< 邮件群组信息，当 type == AB_TYPE_MAIL 时有效
        FTP_ADDRINFO_S          ftp_info;       ///< 邮件群组信息，当 type == AB_TYPE_FTP 时有效
        SMB_ADDRINFO_S          smb_info;       ///< 邮件群组信息，当 type == AB_TYPE_SMB 时有效
        WHITELIST_ADDRINFO_S    whitelist_info; ///< 邮件群组信息，当 type == AB_TYPE_WHITELIST 时有效
    } u;
}
ADDRBOOK_OP_S;

/*
 *@brief data format used by EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED
 */
typedef struct
{
    MAIL_GROUPINFO_S    info[10];   ///< 所有邮件群组信息
    uint32_t            num;        ///< 当前邮件群组个数
    int32_t             idx;
}
MAIL_GROUPLIST_S;

/*
 *@brief data format used by EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    MAIL_ADDRINFO_S     info[60];   ///< 所有Email信息
    uint32_t            num;        ///< 当前Email个数
    int32_t             idx;
}
MAIL_ADDRBOOK_S;

/*
 *@brief data format used by EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    FTP_ADDRINFO_S      info[60];   ///< 所有FTP信息
    uint32_t            num;        ///< 当前FTP个数
    int32_t             idx;
}
FTP_ADDRBOOK_S;

/*
 *@brief data format used by EVT_TYPE_NET_SMB_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    SMB_ADDRINFO_S      info[60];   ///< 所有Samba信息
    uint32_t            num;        ///< 当前Samba个数
    int32_t             idx;
}
SMB_ADDRBOOK_S;

/*
 *@brief data format used by EVT_TYPE_NET_WHITELIST_ADDRESS_BOOK_CHANGED
 */
typedef struct
{
    WHITELIST_ADDRINFO_S      info[999];   ///< 所有Whitelist信息
    uint32_t                  num;         ///< 当前Whitelist个数
    int32_t                   idx;
}
WHITELIST_ADDRBOOK_S;
/*
 *@brief data format used by EVT_TYPE_NET_CUSTOM_WATER_MARK_CHANGED
 */
typedef struct
{
    uint32_t    index;
    char        mark[128];
}
CUSTOM_WATER_MARK_S;

PT_END_DECLS

#endif /* EVENT_MSG_TYPEDEF_H */
/**
 *@}
 */
