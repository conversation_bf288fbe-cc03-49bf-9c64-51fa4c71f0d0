#ifndef _WEBSRV_FORMAT_H_
#define _WEBSRV_FORMAT_H_

#ifdef __cplusplus
extern "C"
{
#endif

int32_t check_hostname_format(const char *str);
int32_t check_ipv4_format(const char* str);
int32_t check_domain_format(const char* str);
int32_t check_9100_format(const char* str);
int32_t check_snmp_community_format(const char* str);
int32_t check_snmp_user_format(const char* str);
int32_t check_snmp_pwsd_format(const char* str);
int32_t check_email_format(const char* str);
int32_t check_email_server_format(const char* str);
int32_t check_port_format(const char* str);
int32_t check_email_sec_mode_format(const char* str);
int32_t check_email_pwsd_format(const char* str);
int32_t check_location_format(const char* str);
int32_t check_lat_format(const char* str);
int32_t check_lot_format(const char* str);
int32_t check_wifi_sec_mode_format(const char* str);
int32_t check_wifi_ssid_format(const char* str);
int32_t check_wifi_pwsd_format(const char* str);
int32_t check_wfd_switch_format(const char* str);
int32_t check_wps_request_format(const char* str);
int32_t check_wfd_psk(const char* str);
int32_t check_bonjour_sercer_format(const char* str);
int32_t check_property_number_format(const char* str);
int32_t check_contacts_format(const char* str);
int32_t check_sleep_time_format(const char* str);
int32_t check_io_timeout_format(const char* str);
int32_t check_system_date_format(const char* date);
int32_t check_system_time_format(const char* time);
int32_t check_color_pswd_format(const char* str);
int32_t check_switch_format(const char* str);
int32_t check_airprint_user_format(const char* str);
int32_t check_airprint_pswd_format(const char* str);
int32_t check_mail_usergroup_format(const char* str);
int32_t check_ftp_server_name_format(const char* str);
int32_t check_ftp_server_addr_format(const char* str);
int32_t check_ftp_path_format(const char* str);
int32_t check_ftp_port_format(const char* str);
int32_t check_ftp_username_format(const char* str);
int32_t check_ftp_password_format(const char* str);
int32_t check_smb_server_name_format(const char* str);
int32_t check_smb_server_addr_format(const char* str);
int32_t check_smb_path_format(const char* str);
int32_t check_smb_port_format(const char* str);
int32_t check_smb_username_format(const char* str);
int32_t check_smb_password_format(const char* str);
int32_t check_water_mark_format(const char* str);

#ifdef __cplusplus
}
#endif


#endif
