CC = $(CROSS_COMPILE)gcc

PARSER_TARGET_DIR =$(realpath $(BASE_DIR)/../../../target/include/m6270)

PARSER_INCLUDE += 	-I$(PARSER_TARGET_DIR)/mainapp	\
					-I$(PARSER_TARGET_DIR)/mainapp/public \
					-I$(PARSER_TARGET_DIR)/mainapp/scan \
					-I$(PARSER_TARGET_DIR)/mainapp/system_manager \
					-I$(PARSER_TARGET_DIR)/mainapp/copy \
					-I$(PARSER_TARGET_DIR)/mainapp/parser \


CFLAGS += -g -rdynamic -fPIC -O2 -Wall -Werror -std=gnu99 -Wno-unused 
CFLAGS += -DLinux $(PARSER_INCLUDE)
LDFLAGS += -L./ 

LIB_OBJS += parser_ips.o

all: libparser_resource.so

libparser_resource.so: $(LIB_OBJS)
	$(CC) $(LDFLAGS) -shared -o $@ $^

clean:
	-rm $(LIB_OBJS) libparser_resource.so
