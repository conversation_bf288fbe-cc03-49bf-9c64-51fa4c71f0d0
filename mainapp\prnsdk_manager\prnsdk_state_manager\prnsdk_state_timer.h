/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file prnsdk_state_timer.c
 * @addtogroup mainapp
 * @{
 * @brief PRINT SDK state timer module
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-09
 */

#ifndef __PRNSDK_STATE_TIMER_H
#define __PRNSDK_STATE_TIMER_H

#include <sys/time.h>
#include <stdbool.h>
#include "pol/pol_threads.h"

typedef struct state_timer
{
    bool    bCircu;                 ///< 单次触发或重复触发
    bool    bStart;                 ///< 定时器启动标志位
    bool    bExit;                  ///< 定时器结束标志位

    int32_t  tfd;                   ///< 句柄

    void    *arg;                   ///< 定时器回调传参
    void    (*timeout_callback)(void *data);
    struct  timeval time_arg;       ///< 定时时长

    PI_MUTEX_T  mtx;                ///< Timer Mutex
    PI_THREAD_T timer_pid;          ///< Timer Thread
}STATE_TIMER_S;

int32_t state_timer_init(STATE_TIMER_S *stimer ,
                            void (*timeout_callback) (void *data) ,
                            void *arg ,
                            struct timeval timeout_arg ,
                            int32_t bCircu
                        );

int32_t state_timer_start(STATE_TIMER_S *stimer);

int32_t state_timer_active(STATE_TIMER_S *stimer);

int32_t state_timer_stop(STATE_TIMER_S *stimer);

int32_t state_timer_restart(STATE_TIMER_S *stimer);

int32_t state_timer_update(STATE_TIMER_S *stimer , struct timeval timeout_arg);

int32_t state_timer_destroy(STATE_TIMER_S *stimer);

#endif ///< PRNSDK_STATE_TIMER_H

/**
 * @}
 */

