#ifndef __WSD_H__
#define __WSD_H__

#include <stdint.h>
#include "threads_pool.h"
#include "netctx.h"
#include "nettypes.h"
#include "qxml.h"
#include <signal.h>

#define EFD_ARRAY_CAPACITY 16

#define LEN(_chars)          (sizeof(_chars) - 1)

#define WSD_SECTION_PREFIX    wsd_

#define _STR(x)              #x
#define STR(x)              _STR(x)
#define __WSD_SECTION_NAME(x, y)    x ## y
#define _WSD_SECTION_NAME(x, y)    __WSD_SECTION_NAME(x, y)
#define WSD_SECTION_NAME(_name)    _WSD_SECTION_NAME(WSD_SECTION_PREFIX, _name)
#define __attr_section(_name)   __attribute__((used, section(STR(WSD_SECTION_NAME(_name)))))

#define FOREACH_ARRAY(arr)    for (size_t _i = 0; _i < ARRAY_SIZE(arr); ++_i)

typedef uint16_t (*WSD_GET_PORT_FUNC)(void);
#define UUID_LEN  (36 + 1)

typedef struct  wsd_context
{
    NET_CTX_S*  net_ctx;
    uint8_t     mac[6];
    char        default_uuid[UUID_LEN];
    int         efd_arr[EFD_ARRAY_CAPACITY];
    int         efd_cnt;
}
WSD_CTX_S;

#define WSD_MAX_ENVELOPE_SIZE   32767       ///< octets          This profile
#define WSD_MAX_FIELD_SIZE      2048         ///< Unicode characters This profile Microsoft Lies!!
#define WSD_MAX_HTTP_URL        1024
#define OUT

struct wsd_service;
typedef struct wsd_ticket
{
    int                 job_id;             ///< job id we gave client and system job id
    volatile unsigned   ref_cnt;
    volatile int        state;              ///< job state last announced
    time_t              created_time;
    char                jobIdString[64];
    char                jobName[128];       ///< name of job
    char                jobOriginator[128]; ///< guy who's running the job
    struct wsd_service* svc_type;
}
WSD_TICKET_S;

/*
typedef enum {
    WSD_JOB_STATE_

} WSD_JOB_STATE_E;
*/

typedef struct wsd_service_data
{
    char      soap_header[WSD_MAX_ENVELOPE_SIZE];  ///< pre-formatted header for replies
    char      soap_reply[WSD_MAX_ENVELOPE_SIZE];  ///< soap reply buffer
    char      service_url[128];
    int       http_finished;                       /// had finished http reply
    int       action_success;
    WSD_TICKET_S* pticket;
}
WSD_SERVICE_DATA_S;


typedef void (*INIT_FUNC)(WSD_CTX_S* wsd_ctx);
typedef void (*DEINIT_FUNC)(void);
typedef void (*SECTION_FUNC)(void);

void wsd_tcp_loop(int notify_efd, THREADS_POOL_S* trd_pool, WSD_GET_PORT_FUNC get_port_func, THREAD_TASK_FUNC trd_handler);
const char* wsd_generate_uuid(char* buf, size_t nbuf);
int wsd_block_sigpipe(sigset_t *out_oldset);
QXML_S* wsd_qxml_parser(char* src);

#define DATA_MGR_OF_WSD(pctx)                       ( (pctx) ? pctx->wsd_ctx->net_ctx->data_mgr     : NULL )

#define FUNC_EXPORT(_section_name, _func)    static SECTION_FUNC s_##_func __attr_section(_section_name) = (SECTION_FUNC)_func

#define MUTEX_LOCK(_lock, _block)  \
     pi_mutex_lock(_lock);    \
     _block               \
     pi_mutex_unlock(_lock);

#define SPIN_LOCK(_lock, _block)  \
     pthread_spin_lock(_lock);    \
     _block               \
     pthread_spin_unlock(_lock);

#endif /* __WSDISC_H__*/
