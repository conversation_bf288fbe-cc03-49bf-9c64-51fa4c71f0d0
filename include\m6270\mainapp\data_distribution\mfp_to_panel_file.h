/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_file.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2025-06-23
 * @brief DC accesses the UI file interface for pedk
 */

#ifndef PANEL_PEDK_FILE_H
#define PANEL_PEDK_FILE_H

#include <stdint.h>
#include <unistd.h>
#include <sys/stat.h>

/**
 * @brief access panel board file
 */
int32_t mfp_to_panel_file_access( const char* pathname, int32_t mode, int32_t* err_num );

/**
 * @brief open panel board file
 */
off_t mfp_to_panel_file_seek( int32_t fd, off_t offset, int32_t whence, int32_t* err_num );

/**
 * @brief open panel board file
 */
int32_t mfp_to_panel_file_open( const char* pathname, int32_t flags, mode_t mode, int32_t* err_num );

/**
 * @brief read panel board file
 */
ssize_t mfp_to_panel_file_read( int32_t fd, void *buf, size_t count, int32_t* err_num );

/**
 * @brief write panel board file
 */
ssize_t mfp_to_panel_file_write( int32_t fd, const void* buf, size_t count, int32_t* err_num );

/**
 * @brief close panel board file
 */
int32_t mfp_to_panel_file_close( int32_t fd, int32_t* err_num );

#endif /* PANEL_PEDK_FILE_H */

/**
 *@}
 */

