#ifndef __CASE_H__
#define __CASE_H__

#ifdef LOG_ON 
#define log(fmt,args...)         printf(fmt,##args)
#else
#define log(fmt,...)
#endif

#define COLOR_RED               "\033[31m"
#define COLOR_GREEN               "\033[32m"
#define COLOR_END               "\033[0m"

typedef  enum{
    PASS,
    FAIL,
}TEST_RESULT_E;
#define case_pass_fclose(fp)        {fclose(fp);\
                                    printf(COLOR_GREEN"case :%s  PASS!!!\n"COLOR_END, __func__);\
                                    return PASS;}

#define case_fail_fclose(fp)        {fclose(fp);\
                                    printf(COLOR_RED "case :%s  FAIL!!!\n"COLOR_END, __func__);\
                                    return FAIL;}


#define case_pass()                 { printf(COLOR_GREEN"case :%s  PASS!!!\n"COLOR_END, __func__);\
                                    return PASS;}

#define case_fail()                 {printf(COLOR_RED "case :%s  FAIL!!!\n"COLOR_END, __func__);\
                                    return FAIL;}

typedef TEST_RESULT_E (*case0_func)(void);





extern case0_func case0_sets[];
uint8_t get_case_sets_num();







#endif 
