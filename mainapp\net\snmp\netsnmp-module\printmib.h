/*
 * Note: this file originally auto-generated by mib2c using
 *        : mib2c.old-api.conf 17851 2009-11-30 16:46:06Z dts12 $
 */
#ifndef PRINTMIB_H
#define PRINTMIB_H

/* function declarations */
void init_printmib(void);
FindVarMethod var_printmib;
FindVarMethod var_prtGeneralTable;
FindVarMethod var_prtStorageRefTable;
FindVarMethod var_prtDeviceRefTable;
FindVarMethod var_prtCoverTable;
FindVarMethod var_prtLocalizationTable;
FindVarMethod var_prtInputTable;
FindVarMethod var_prtOutputTable;
FindVarMethod var_prtMarkerTable;
FindVarMethod var_prtMarkerSuppliesTable;
FindVarMethod var_prtMarkerColorantTable;
FindVarMethod var_prtMediaPathTable;
FindVarMethod var_prtChannelTable;
FindVarMethod var_prtInterpreterTable;
FindVarMethod var_prtConsoleDisplayBufferTable;
FindVarMethod var_prtConsoleLightTable;
FindVarMethod var_prtAlertTable;
    WriteMethod write_prtGeneralCurrentLocalization;
    WriteMethod write_prtGeneralReset;
    WriteMethod write_prtGeneralCurrentOperator;
    WriteMethod write_prtGeneralServicePerson;
    WriteMethod write_prtInputDefaultIndex;
    WriteMethod write_prtOutputDefaultIndex;
    WriteMethod write_prtMarkerDefaultIndex;
    WriteMethod write_prtMediaPathDefaultIndex;
    WriteMethod write_prtConsoleLocalization;
    WriteMethod write_prtConsoleDisable;
    WriteMethod write_prtAuxiliarySheetStartupPage;
    WriteMethod write_prtAuxiliarySheetBannerPage;
    WriteMethod write_prtGeneralPrinterName;
    WriteMethod write_prtGeneralSerialNumber;
    WriteMethod write_prtInputMediaDimFeedDirDeclared;
    WriteMethod write_prtInputMediaDimXFeedDirDeclared;
    WriteMethod write_prtInputMaxCapacity;
    WriteMethod write_prtInputCurrentLevel;
    WriteMethod write_prtInputMediaName;
    WriteMethod write_prtInputName;
    WriteMethod write_prtInputSecurity;
    WriteMethod write_prtInputMediaWeight;
    WriteMethod write_prtInputMediaType;
    WriteMethod write_prtInputMediaColor;
    WriteMethod write_prtInputMediaFormParts;
    WriteMethod write_prtInputMediaLoadTimeout;
    WriteMethod write_prtInputNextIndex;
    WriteMethod write_prtOutputMaxCapacity;
    WriteMethod write_prtOutputRemainingCapacity;
    WriteMethod write_prtOutputName;
    WriteMethod write_prtOutputSecurity;
    WriteMethod write_prtOutputMaxDimFeedDir;
    WriteMethod write_prtOutputMaxDimXFeedDir;
    WriteMethod write_prtOutputMinDimFeedDir;
    WriteMethod write_prtOutputMinDimXFeedDir;
    WriteMethod write_prtOutputStackingOrder;
    WriteMethod write_prtOutputPageDeliveryOrientation;
    WriteMethod write_prtOutputBursting;
    WriteMethod write_prtOutputDecollating;
    WriteMethod write_prtOutputPageCollated;
    WriteMethod write_prtOutputOffsetStacking;
    WriteMethod write_prtMarkerSuppliesMaxCapacity;
    WriteMethod write_prtMarkerSuppliesLevel;
    WriteMethod write_prtChannelCurrentJobCntlLangIndex;
    WriteMethod write_prtChannelDefaultPageDescLangIndex;
    WriteMethod write_prtChannelState;
    WriteMethod write_prtChannelIfIndex;
    WriteMethod write_prtInterpreterDefaultOrientation;
    WriteMethod write_prtInterpreterDefaultCharSetIn;
    WriteMethod write_prtInterpreterDefaultCharSetOut;
    WriteMethod write_prtConsoleDisplayBufferText;
    WriteMethod write_prtConsoleOnTime;
    WriteMethod write_prtConsoleOffTime;

#endif /* PRINTMIB_H */
