// **************************************
// 日志系统说明
// 1 EVENT_LOG 简写 EL或el
// 2 Manger    简写 mgr
// **************************************
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <dirent.h>
#include <unistd.h>
#include <time.h>
#include <sys/types.h>
#include "event_log.h"
#include "cmd.h"
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_mgr.h"

#include "nvram.h"
#include "moduleid.h"

#include <pthread.h>
#include "pol/pol_log.h"
#include "platform_api.h"

#include "print.h"

#define EL_DEBUG_BUF_LEN 256

enum el_debug_colour {
    EL_NOR     = 0,
    EL_RED     = 1,
    EL_GREEN   = 2,
    EL_YELLOW  = 3,
    EL_BLUE    = 4,
    EL_PINK    = 5,
    EL_SKY     = 6,
    EL_FLAG    = 0x10,
    EL_MAX_COLOUR
};

#define EL_DATA_FILE_NAME_MAX 32   // 数据文件的文件名最大字节数 包含结尾符
#define EL_FILE_CHECK_MAX 32       // 文件校验保留

#define EL_INIT_FLAG_FILE  "/event/EL_MF" // 如果该文件存在 说明已经初始化过日志系统

#define EL_MANGER_FILE_MAX 5       // 日志系统管理文件循环使用最大的文件个数 注 考虑到存储器有擦写寿命问题，采用循环使用的方式延迟存储器寿命
#define EL_MANGER_FILE_1   "/event/EL_M1" // 日志系统管理文件名 下同
#define EL_MANGER_FILE_2   "/event/EL_M2"
#define EL_MANGER_FILE_3   "/event/EL_M3"
#define EL_MANGER_FILE_4   "/event/EL_M4"
#define EL_MANGER_FILE_5   "/event/EL_M5"

#define EL_DATA_FILE_NANE_NUM_MIN 1
#define EL_DATA_FILE_NANE_NUM_MAX 99999
#define EL_DATA_FILE_NANE_BASE    "/event/EL_D"
#define EL_DATA_FILE_NANE_FORMAT  "%s%05u"
#define MAX_RECOVERY_DEPTH 3  // 最大允许的恢复尝试次数

// Generic buffer lengths for summary generation
#define SUMMARY_BUF_LEN_L 128
#define SUMMARY_BUF_LEN_M 64
#define SUMMARY_BUF_LEN_S 32

#define DATA_BUFFER_SIZE 1024

#define CHECK_GET_NV_RESULT(ret) do { \
    if (ret < 0) { \
        pi_log_d( "event log get nv fail, ret: %d id: %d\n", ret); \
    } \
} while(0)

#define WRITE_LINE_CHECKED(...) \
    do { \
        if (el_write_summary_line(__VA_ARGS__) != EL_OK) { \
            el_debug(EL_RED, "[EL_SUM] ERROR: Failed to write summary line at %s:%d", __FILE__, __LINE__); \
            return EL_ERROR; \
        } \
    } while(0)

struct el_manger_info {
    char file_name[EL_DATA_FILE_NAME_MAX]; // 当前正在使用的数据文件的文件名
	unsigned int offset;                   // 数据文件中记录最后一条日志的偏移地址
	unsigned int record_id;                // 日志系统的记录编号 追加一条日志 数值加1 不重复 按产品寿命内 该数据值不会产生溢出
	unsigned int record_sum;               // 当前正在使用的数据文件已经保存的日志总数
	unsigned int file_sum;                 // 记录数据文件是否达到最大文件个数
	unsigned int file_id;                  // 数据文件的编号 用于生成数据文件名
	unsigned int check[EL_FILE_CHECK_MAX]; // 保留 用于校验完整性
};

struct el_data_file_manger_info {
    char file_name[EL_DATA_FILE_NAME_MAX]; // 当前数据文件的前一个数据文件名 如果没有全部填0x00
    unsigned int last_index_num;           // 记录当前数据文件最后一条日志的编号 范围在 0 - EL_DTAT_FILE_INDEX_MAX
    unsigned int last_index_offset;        // 记录当前数据文件最后一条日志的偏移地址
    unsigned int check[EL_FILE_CHECK_MAX]; // 保留 用于校验完整性
};

struct el_data_file_read_info {
    char file_name[EL_DATA_FILE_NAME_MAX]; // 当前读取数据文件的文件名
    unsigned int cur_offset;               // 当前读取的日志节点在文件中的偏移
    unsigned int pre_offset;               // 当前读取的日志节点的前节点在文件中的偏移

    unsigned int init_flag;                // 本数据结构初始化标记 0->未进行初始化 1->已初始化
};


static const char*  s_mgr_file_table[EL_MANGER_FILE_MAX] = {EL_MANGER_FILE_1, EL_MANGER_FILE_2, EL_MANGER_FILE_3, EL_MANGER_FILE_4, EL_MANGER_FILE_5};
static const unsigned char s_end_data_flag = 0xFF;

static unsigned int s_mgr_file_table_index = 0xFFFFFFFF;
static struct el_data_file_read_info s_data_file_read_info;
static unsigned int s_last_valid_offset = 0;  // 保存最后一次有效的读取位置
static int s_recovery_mode = 0;  // 恢复模式标志
static unsigned int s_recovery_depth = 0;  // 记录恢复尝试的递归深度

static EVT_MGR_CLI_S *g_pst_cli_ptr = NULL;

EVENT_LOG_PAGE_COUNT_S g_event_log_page_count = {0};
pthread_mutex_t g_page_count_mutex = PTHREAD_MUTEX_INITIALIZER;

struct event_log_consumable_status {
    uint8_t waste_toner_remain;
    uint8_t toner_k_rem;
    uint8_t toner_c_rem;
    uint8_t toner_m_rem;
    uint8_t toner_y_rem;
};

struct summary_header_data {
    char time_str[SUMMARY_BUF_LEN_M];
    char machine_series[SUMMARY_BUF_LEN_M];
    char serial_number[SUMMARY_BUF_LEN_S];
    char rip_fw[SUMMARY_BUF_LEN_M];
    char engine_fw[SUMMARY_BUF_LEN_M];
    char maint_kit_rem[SUMMARY_BUF_LEN_S];
    char waste_toner_rem[SUMMARY_BUF_LEN_S];
    char toner_k_rem[SUMMARY_BUF_LEN_S];
    char toner_c_rem[SUMMARY_BUF_LEN_S];
    char toner_m_rem[SUMMARY_BUF_LEN_S];
    char toner_y_rem[SUMMARY_BUF_LEN_S];
    unsigned int page_count;
    unsigned int adf_count;
    unsigned int fb_count;
    unsigned int scan_count;
};

struct summary_line_data {
    char time_stamp[SUMMARY_BUF_LEN_S];
    char page_count[SUMMARY_BUF_LEN_S];
    char adf_count[SUMMARY_BUF_LEN_S];
    char event_name[SUMMARY_BUF_LEN_M];
    char details[SUMMARY_BUF_LEN_L];
};

static struct event_log_consumable_status g_consumable_status = {0};
static pthread_mutex_t g_consumable_status_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t g_export_summary_mutex = PTHREAD_MUTEX_INITIALIZER;


static int event_log_category_wirte_firmware(uint32_t module_id, void* data);
static int event_log_category_wirte_job(uint32_t module_id,void* data, uint32_t data_length);
static int event_log_category_wirte_consumable(uint32_t module_id, void* data, uint32_t data_length);
static int event_log_category_wirte_system_status(uint32_t module_id, void* data, uint32_t data_length);

static void el_debug(enum el_debug_colour colour, const char* format, ...)
{
    va_list arg;
    char string_buf[EL_DEBUG_BUF_LEN];

    va_start(arg, format);
    vsnprintf(string_buf, EL_DEBUG_BUF_LEN, format, arg);
    va_end(arg);

    switch (colour)
    {
        case EL_RED:
        printf("\033[31m%s\033[0m \r\n", string_buf);
        break;

        case EL_GREEN:
        printf("\033[32m%s\033[0m \r\n", string_buf);
        break;

        case EL_YELLOW:
        printf("\033[33m%s\033[0m \r\n", string_buf);
        break;

        case EL_BLUE:
        printf("\033[34m%s\033[0m \r\n", string_buf);
        break;

        case EL_PINK:
        printf("\033[35m%s\033[0m \r\n", string_buf);
        break;

        case EL_SKY:
        printf("\033[36m%s\033[0m \r\n", string_buf);
        break;

        case EL_FLAG:
        printf("%s", string_buf);
        break;

        default:
        printf("%s \r\n", string_buf);
        break;
    }
}

static FILE* el_file_open(const char* path, const char* mode)
{
    FILE* file_handle = NULL;

    file_handle = fopen(path, mode);
    if (file_handle == NULL) {
        el_debug(EL_RED, "el_file_open %s open fail", path);
        return NULL;
    }

    return file_handle;
}

static int el_file_close(FILE* file_handle)
{
    int ret = 0;

    if (file_handle == NULL) {
        el_debug(EL_RED, "el_file_close file_handle == NULL" );
        return 0;
    }

    ret = fclose( file_handle );
    if (ret != 0) {
        el_debug(EL_RED, "el_file_close ret != 0" );
        return 0;
    }

    return ret;
}

static int el_file_read_data(FILE* file_handle, unsigned char* file_buffer, int file_size)
{
    int ret_read   = 0;
    int count_read = 0;
    int need_read  = 0;

    need_read = file_size;
    fseek(file_handle, 0, SEEK_SET);
    while( 1 )
    {
        ret_read = fread(&file_buffer[ count_read ], 1, need_read, file_handle);
        count_read += ret_read;
        if( ret_read == 0 )
        {
            break;
        }
        else
        {
            need_read = file_size - count_read;
        }
    }

    return count_read;
}

static int el_file_read_data_ex(FILE* file_handle, unsigned int offset, unsigned char* file_buffer, int file_size)
{
    int ret_read   = 0;
    int count_read = 0;
    int need_read  = 0;

    need_read = file_size;
    fseek(file_handle, offset, SEEK_SET);
    while (1) {
        ret_read = fread(&file_buffer[count_read], 1, need_read, file_handle);
        count_read += ret_read;
        if (ret_read == 0) {
            break;
        } else {
            need_read = file_size - count_read;
        }
    }

    return count_read;
}


static int el_file_write_data(FILE* file_handle, unsigned char* file_buffer, int file_size)
{
    int ret_write   = 0;
    int count_write = 0;
    int need_write  = 0;

    need_write = file_size;
    fseek(file_handle, 0, SEEK_END);
    while (1) {
        ret_write = fwrite(&file_buffer[count_write], 1, need_write, file_handle);
        count_write += ret_write;
        if (count_write == file_size) {
            break;
        } else {
            need_write = file_size - count_write;
        }
    }

    return count_write;
}

static int el_file_write_data_ex(FILE* file_handle, unsigned int offset, unsigned char* file_buffer, int file_size)
{
    int ret_write   = 0;
    int count_write = 0;
    int need_write  = 0;

    need_write = file_size;
    fseek(file_handle, offset, SEEK_SET);
    while (1) {
        ret_write = fwrite(&file_buffer[count_write], 1, need_write, file_handle);
        count_write += ret_write;
        if (count_write == file_size) {
            break;
        } else {
            need_write = file_size - count_write;
        }
    }

    return count_write;
}


static unsigned int el_file_get_size(FILE* file_handle)
{
    unsigned int file_size = 0;

    if (file_handle != NULL ) {
        fseek(file_handle, SEEK_SET, SEEK_END);
        file_size = ftell(file_handle);
        fseek(file_handle, 0, SEEK_SET);
        return file_size;
    } else {
        el_debug(EL_RED, "el_file_get_size file_handle == NULL");
        return 0;
    }
}

static int el_file_remove(char* file_path)
{
    int file_ret = 0;

    file_ret = remove(file_path);
    if (file_ret == 0) {
        return 0;
    }

    el_debug(EL_RED, "el_file_remove fail");
    return file_ret;
}

static void* el_memset(void* ptr, int ch, size_t size)
{
    return memset(ptr, ch, size);
}

#ifdef __EL_OUTPUT_DEBUG__
static void event_log_manger_info_debug(struct el_manger_info manger_info)
{
	el_debug(EL_GREEN, "-------------------------------");
	el_debug(EL_GREEN, "file name      %s", s_mgr_file_table[s_mgr_file_table_index]);
	el_debug(EL_GREEN, "mgr.file_name  %s", manger_info.file_name);
	el_debug(EL_GREEN, "mgr.offset     %d 0x%08x", manger_info.offset, manger_info.offset);
	el_debug(EL_GREEN, "mgr.record_id  %d", manger_info.record_id);
	el_debug(EL_GREEN, "mgr.record_sum %d", manger_info.record_sum);
	el_debug(EL_GREEN, "mgr.file_sum   %d", manger_info.file_sum);
	el_debug(EL_GREEN, "mgr.file_id    %d", manger_info.file_id);
	el_debug(EL_GREEN, "mgr.check      0x%08x 0x%08x", manger_info.check[0], manger_info.check[1]);
	el_debug(EL_GREEN, "-------------------------------\r\n");
}
#endif

static int event_log_creat_data_file(char* file_name, char* pre_file_name)
{
    int file_ret = 0;
    FILE* file_handle = NULL;
    struct el_data_file_manger_info data_file_manger_info;

    if (file_name != NULL) {
        file_handle = el_file_open(file_name, "wb+");
		if (file_handle == NULL) {
			el_debug(EL_RED, "Create %s error!", file_name);
			return EL_ERROR;
		}

        el_memset(&data_file_manger_info, 0x00, sizeof(struct el_data_file_manger_info));
        data_file_manger_info.check[0] = 0x12345678;
        data_file_manger_info.check[1] = 0xABCDEF00;

		if (pre_file_name != NULL) {
			if (strlen(pre_file_name) < EL_DATA_FILE_NAME_MAX) {
				strcpy(data_file_manger_info.file_name, pre_file_name);
			}  else {
				el_debug(EL_RED, "pre_file_name size %d >= %d", strlen(pre_file_name), EL_DATA_FILE_NAME_MAX);
				el_file_close(file_handle);
				file_handle = NULL;
				return EL_ERROR;
			}
		}

        data_file_manger_info.last_index_num = 0;
        data_file_manger_info.last_index_offset += sizeof(struct el_data_file_manger_info);
        file_ret = el_file_write_data(file_handle, (unsigned char*)&data_file_manger_info, sizeof(struct el_data_file_manger_info));
        if (file_ret != sizeof(struct el_data_file_manger_info)) {
            el_debug(EL_RED, "write %s error %d", file_name, file_ret);
            el_file_close(file_handle);
            file_handle = NULL;
            return EL_ERROR;
        }

        fflush(file_handle);
        fsync(fileno(file_handle));

        el_file_close(file_handle);
        file_handle = NULL;
        return EL_OK;
    } else {
		el_debug(EL_RED, "file_name == NULL");
		return EL_ERROR;
    }
}

static int event_log_mgr_file_table_index_init(unsigned int file_index, unsigned int* recode_max)
{
	int file_ret = 0;
	FILE* file_handle = NULL;
	struct el_manger_info manger_info;

	file_handle = el_file_open(s_mgr_file_table[file_index], "r+");
	if (file_handle == NULL) {
		el_debug(EL_RED, "mgr_file open %s error", s_mgr_file_table[file_index]);
		return EL_ERROR;
	}

	file_ret = el_file_read_data(file_handle, (unsigned char*)&manger_info, sizeof(struct el_manger_info));
	if (file_ret != sizeof(struct el_manger_info)) {
		el_debug(EL_RED, "mgr_file read %s error %d", s_mgr_file_table[file_index], file_ret);
		el_file_close(file_handle);
		file_handle = NULL;
		return EL_ERROR;
	}

    el_file_close(file_handle);
    file_handle = NULL;

	if (s_mgr_file_table_index == 0xFFFFFFFF) {
        *recode_max = manger_info.record_id;
		s_mgr_file_table_index = file_index;
	} else {
        if (manger_info.record_id > *recode_max) {
            *recode_max = manger_info.record_id;
            s_mgr_file_table_index = file_index;
        }
	}

	return EL_OK;
}

static void event_log_mgr_file_table_index_next(void)
{
	if (s_mgr_file_table_index < (EL_MANGER_FILE_MAX - 1)) {
		s_mgr_file_table_index++;
	} else {
		s_mgr_file_table_index = 0;
	}
}

static int event_log_load_manger_info(struct el_manger_info* manger_info)
{
	int access_ret = 0;
	int file_ret = 0;
	FILE* file_handle = NULL;

    if (s_mgr_file_table_index < EL_MANGER_FILE_MAX) {
        access_ret = access(s_mgr_file_table[s_mgr_file_table_index], F_OK);
        if (access_ret == 0) {
            file_handle = el_file_open(s_mgr_file_table[s_mgr_file_table_index], "r+");
            if (file_handle == NULL) {
                el_debug(EL_RED, "open %s error!", s_mgr_file_table[s_mgr_file_table_index]);
                return EL_ERROR;
            }

            file_ret = el_file_read_data(file_handle, (unsigned char*)manger_info, sizeof(struct el_manger_info));
            if (file_ret != sizeof(struct el_manger_info)) {
                el_debug(EL_RED, "read %s error %d", s_mgr_file_table[s_mgr_file_table_index], file_ret);
                el_file_close(file_handle);
                file_handle = NULL;
                return EL_ERROR;
            }

            fflush(file_handle);
            fsync(fileno(file_handle));

            el_file_close(file_handle);
            file_handle = NULL;
            return EL_OK;
        } else {
            el_debug(EL_RED, "%s not find", s_mgr_file_table[s_mgr_file_table_index]);
            return EL_ERROR;
        }
    } else {
        el_debug(EL_RED, "s_mgr_file_table_index %d error", s_mgr_file_table_index);
        return EL_ERROR;
    }
}

static int event_log_load_data_file_manger_info(char* file_name, struct el_data_file_manger_info* data_file_manger_info)
{
    int file_ret = 0;
    FILE* file_handle = NULL;

    file_handle = el_file_open(file_name, "r+");
    if (file_handle == NULL) {
        el_debug(EL_RED, "open %s error", file_name);
        return EL_ERROR;
    }

    file_ret = el_file_read_data_ex(file_handle, 0, (unsigned char*)data_file_manger_info, sizeof(struct el_data_file_manger_info));
    if (file_ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "read %s error %d", file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    el_file_close(file_handle);
    file_handle = NULL;
    return EL_OK;
}

static int event_log_updata_manger_info(struct el_manger_info* manger_info)
{
	int access_ret = 0;
	int file_ret = 0;
	FILE* file_handle = NULL;

    if (s_mgr_file_table_index < EL_MANGER_FILE_MAX) {
        access_ret = access(s_mgr_file_table[s_mgr_file_table_index], F_OK);
        if (access_ret == 0) {
            file_handle = el_file_open(s_mgr_file_table[s_mgr_file_table_index], "wb+");
            if (file_handle == NULL) {
                el_debug(EL_RED, "open %s error!", s_mgr_file_table[s_mgr_file_table_index]);
                return EL_ERROR;
            }

            file_ret = el_file_write_data(file_handle, (unsigned char*)manger_info, sizeof(struct el_manger_info));
            if (file_ret != sizeof(struct el_manger_info)) {
                el_debug(EL_RED, "read %s error %d", s_mgr_file_table[s_mgr_file_table_index], file_ret);
                el_file_close(file_handle);
                file_handle = NULL;
                return EL_ERROR;
            }

            fflush(file_handle);
            fsync(fileno(file_handle));

            el_file_close(file_handle);
            file_handle = NULL;
            return EL_OK;
        } else {
            el_debug(EL_RED, "%s not find", s_mgr_file_table[s_mgr_file_table_index]);
            return EL_ERROR;
        }
    } else {
        el_debug(EL_RED, "s_mgr_file_table_index %d error", s_mgr_file_table_index);
        return EL_ERROR;
    }
}

static int event_log_write_data_clean_pre_file_name_info(unsigned int file_id)
{
    int file_ret = 0;
    unsigned int clean_file_id = 0;
    FILE* file_handle = NULL;
    char file_name[EL_DATA_FILE_NAME_MAX];
    struct el_data_file_manger_info data_file_manger_info;

    // 计算要清理的文件ID
    if (file_id > EL_DTAT_FILE_MAX) {
        clean_file_id = file_id - EL_DTAT_FILE_MAX;
    } else {
        clean_file_id = (EL_DATA_FILE_NANE_NUM_MAX + file_id) - EL_DTAT_FILE_MAX;
    }

    // 构造文件名
    el_memset(file_name, 0x00, EL_DATA_FILE_NAME_MAX);
    sprintf(file_name, EL_DATA_FILE_NANE_FORMAT, EL_DATA_FILE_NANE_BASE, clean_file_id);

    // 打开文件
    file_handle = el_file_open(file_name, "r+");
    if (file_handle == NULL) {
        el_debug(EL_RED, "open %s error!", file_name);
        return EL_ERROR;
    }

    // 读取管理信息
    file_ret = el_file_read_data_ex(file_handle, 0, (unsigned char*)&data_file_manger_info, sizeof(struct el_data_file_manger_info));
    if (file_ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "read %s error %d", file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 清除前一个文件名信息
    el_memset(data_file_manger_info.file_name, 0x00, EL_DATA_FILE_NAME_MAX);
    file_ret = 0;
    file_ret = el_file_write_data_ex(file_handle, 0, (unsigned char*)&data_file_manger_info, sizeof(struct el_data_file_manger_info));
    if (file_ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "write %s error %d", file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    el_file_close(file_handle);
    file_handle = NULL;
    return EL_OK;
}

static int event_log_write_data_del_pre_file(unsigned int file_id, unsigned int record_id)
{
    int file_ret = 0;
    unsigned int del_file_id = 0;
    unsigned int loop_record_id = 0;
    char file_name[EL_DATA_FILE_NAME_MAX];

    // 计算要删除的文件ID
    if (file_id > EL_DTAT_FILE_MAX) {
        del_file_id = file_id - EL_DTAT_FILE_MAX - 1;
        if (del_file_id == 0) {
            loop_record_id = EL_DTAT_FILE_INDEX_MAX * (EL_DTAT_FILE_MAX + 1);
            if (record_id > loop_record_id) {
                del_file_id = EL_DATA_FILE_NANE_NUM_MAX;
            } else {
                return EL_OK;
            }
        }
    } else {
        del_file_id = (EL_DATA_FILE_NANE_NUM_MAX + file_id) - EL_DTAT_FILE_MAX - 1;
    }

    sprintf(file_name, EL_DATA_FILE_NANE_FORMAT, EL_DATA_FILE_NANE_BASE, del_file_id);
    file_ret = el_file_remove(file_name);
    if (file_ret != 0) {
        el_debug(EL_RED, "remove %s error %d", file_name, file_ret);
        return EL_ERROR;
    }

    return EL_OK;
}

static int event_log_write_data_file(struct el_manger_info* manger_info, void* data, int data_len)
{
    int file_ret = 0;
    int file_len = 0;
    FILE* file_handle = NULL;
    unsigned int pre_index_addr = 0;
    char file_name[EL_DATA_FILE_NAME_MAX];
    struct el_data_file_manger_info data_file_manger_info;

    if (manger_info->record_sum == 0) {
        pre_index_addr = 0xFFFFFFFF;
    } else {
        pre_index_addr = manger_info->offset;
    }

    file_handle = el_file_open(manger_info->file_name, "r+");
    if (file_handle == NULL) {
        el_debug(EL_RED, "open %s error!", manger_info->file_name);
        return EL_ERROR;
    }

    file_len = el_file_get_size(file_handle);

    // 写入前索引地址
    file_ret = el_file_write_data(file_handle, (unsigned char*)&pre_index_addr, sizeof(pre_index_addr));
    if (file_ret != sizeof(pre_index_addr)) {
        el_debug(EL_RED, "pre_index_addr write %s error %d", manger_info->file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 写入数据
    file_ret = 0;
    file_ret = el_file_write_data(file_handle, (unsigned char*)data, data_len);
    if (file_ret != data_len) {
        el_debug(EL_RED, "data write %s error %d", manger_info->file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 写入结束标志
    file_ret = 0;
    file_ret = el_file_write_data(file_handle, (unsigned char*)&s_end_data_flag, sizeof(s_end_data_flag));
    if (file_ret != sizeof(s_end_data_flag)) {
        el_debug(EL_RED, "s_end_data_flag write %s error %d", manger_info->file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 确保数据写入磁盘
    fflush(file_handle);
    fsync(fileno(file_handle));

    // 读取管理信息
    file_ret = 0;
    file_ret = el_file_read_data_ex(file_handle, 0, (unsigned char*)&data_file_manger_info, sizeof(struct el_data_file_manger_info));
    if (file_ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "data_file_manger_info read %s error %d", manger_info->file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 更新管理信息
    data_file_manger_info.last_index_num += 1;
    if (data_file_manger_info.last_index_num > 1) {
        data_file_manger_info.last_index_offset = file_len;
    }

    // 确保校验值正确
    data_file_manger_info.check[0] = 0x12345678;
    data_file_manger_info.check[1] = 0xABCDEF00;

    // 写入更新后的管理信息
    file_ret = 0;
    file_ret = el_file_write_data_ex(file_handle, 0, (unsigned char*)&data_file_manger_info, sizeof(struct el_data_file_manger_info));
    if (file_ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "data_file_manger_info write %s error %d", manger_info->file_name, file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 确保管理信息写入磁盘
    fflush(file_handle);
    fsync(fileno(file_handle));

    el_file_close(file_handle);
    file_handle = NULL;

    if (pre_index_addr != 0xFFFFFFFF) {
        //manger_info->offset += sizeof(pre_index_addr) + data_len + sizeof(s_end_data_flag);
         manger_info->offset = file_len;
    }

    manger_info->record_id  += 1;
    manger_info->record_sum += 1;

	if (manger_info->record_sum >= EL_DTAT_FILE_INDEX_MAX) {
		manger_info->record_sum = 0;
		if (manger_info->file_sum > EL_DTAT_FILE_MAX) {
		    event_log_write_data_clean_pre_file_name_info(manger_info->file_id);
		    event_log_write_data_del_pre_file(manger_info->file_id, manger_info->record_id);
		} else {
		    manger_info->file_sum += 1;
		}

		manger_info->file_id += 1;
		if (manger_info->file_id > EL_DATA_FILE_NANE_NUM_MAX) {
		    manger_info->file_id = 1;
		}

        manger_info->offset = sizeof(struct el_data_file_manger_info);
		sprintf(file_name, EL_DATA_FILE_NANE_FORMAT, EL_DATA_FILE_NANE_BASE, manger_info->file_id);
		event_log_creat_data_file(file_name, manger_info->file_name);
		el_memset(manger_info->file_name, 0x00, sizeof(manger_info->file_name));
		strcpy(manger_info->file_name, file_name);
    }

    return EL_OK;
}

static int event_log_read_data_file(void* data, int* data_len)
{
    int read_len = 0;
    int file_ret = 0;
    int file_len = 0;
    unsigned int pre_index_addr = 0;
    FILE* file_handle = NULL;
    struct el_data_file_manger_info data_file_manger_info;

    if(data == NULL || data_len == NULL) {
        el_debug(EL_RED, "Invalid parameters");
        return EL_ERROR;
    }

    // 打开文件前先进行文件存在性检查
    if (access(s_data_file_read_info.file_name, F_OK) != 0) {
        el_debug(EL_RED, "File %s does not exist", s_data_file_read_info.file_name);
        return EL_ERROR;
    }

    // 只读方式打开文件，避免意外修改
    file_handle = el_file_open(s_data_file_read_info.file_name, "r");
    if (file_handle == NULL) {
        el_debug(EL_RED, "open %s error!", s_data_file_read_info.file_name);
        return EL_ERROR;
    }

    // 获取文件大小
    file_len = el_file_get_size(file_handle);
    if (file_len <= sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "file %s too small: %d", s_data_file_read_info.file_name, file_len);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 读取文件头管理信息
    file_ret = el_file_read_data_ex(file_handle, 0, (unsigned char*)&data_file_manger_info,
                                   sizeof(struct el_data_file_manger_info));
    if (file_ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "read manager info error");
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 文件完整性校验
    if (data_file_manger_info.check[0] != 0x12345678 || data_file_manger_info.check[1] != 0xABCDEF00) {
        el_debug(EL_RED, "File integrity check failed");
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 检查当前偏移量是否有效
    if (s_data_file_read_info.cur_offset >= (unsigned int)file_len ||
        s_data_file_read_info.cur_offset < sizeof(struct el_data_file_manger_info)) {

        el_debug(EL_RED, "Invalid offset detected: cur=%u, file_len=%d",
                s_data_file_read_info.cur_offset, file_len);

        // 检查递归深度
        if (s_recovery_depth >= MAX_RECOVERY_DEPTH) {
            el_debug(EL_RED, "Max recovery depth reached (%d), giving up recovery", s_recovery_depth);
            s_recovery_depth = 0;  // 重置递归深度
            s_recovery_mode = 0;   // 退出恢复模式
            el_file_close(file_handle);
            file_handle = NULL;
            return EL_ERROR;
        }

        // 如果在恢复模式中，尝试使用文件中的最后一条记录
        if (s_recovery_mode == 0) {
            s_recovery_mode = 1;  // 进入恢复模式
            s_recovery_depth++;   // 增加递归深度

            // 尝试使用上次有效位置
            if (s_last_valid_offset > 0 && s_last_valid_offset < (unsigned int)file_len) {
                el_debug(EL_YELLOW, "Trying to recover using last valid offset: %u", s_last_valid_offset);
                s_data_file_read_info.cur_offset = s_last_valid_offset;
            } else {
                // 使用文件中记录的最后一条记录位置
                s_data_file_read_info.cur_offset = data_file_manger_info.last_index_offset;
                if (s_data_file_read_info.cur_offset >= (unsigned int)file_len) {
                    el_debug(EL_RED, "last_index_offset also invalid: %u, resetting to file header",
                            s_data_file_read_info.cur_offset);
                    s_data_file_read_info.cur_offset = sizeof(struct el_data_file_manger_info);
                }
            }
            s_data_file_read_info.pre_offset = 0;

            el_debug(EL_YELLOW, "Recovery mode: reset to offset %u (attempt %d/%d)",
                    s_data_file_read_info.cur_offset, s_recovery_depth, MAX_RECOVERY_DEPTH);
            el_file_close(file_handle);
            file_handle = NULL;
            return event_log_read_data_file(data, data_len);  // 递归尝试读取
        } else {
            // 已经在恢复模式中，但仍然失败，尝试切换到前一个文件
            s_recovery_mode = 0;  // 重置恢复模式

            if (data_file_manger_info.file_name[0] != 0x00) {
                el_debug(EL_YELLOW, "Trying to switch to previous file: %s", data_file_manger_info.file_name);
                el_memset(s_data_file_read_info.file_name, 0x00, EL_DATA_FILE_NAME_MAX);
                strcpy(s_data_file_read_info.file_name, data_file_manger_info.file_name);
                s_data_file_read_info.pre_offset = 0;
                s_data_file_read_info.cur_offset = data_file_manger_info.last_index_offset;
                s_last_valid_offset = 0;

                el_file_close(file_handle);
                file_handle = NULL;
                return EL_OK;  // 返回OK让调用者继续尝试读取
            }

            el_debug(EL_RED, "Recovery failed, no previous file available");
            el_file_close(file_handle);
            file_handle = NULL;
            return EL_ERROR;
        }
    }

    // 如果是第一个文件，且当前偏移量已经到达文件开头，说明读取完成
    if (strncmp(s_data_file_read_info.file_name, "/event/EL_D00001", strlen("/event/EL_D00001")) == 0) {
        if (s_data_file_read_info.cur_offset < sizeof(struct el_data_file_manger_info)) {
            el_debug(EL_GREEN, "Reach the beginning of first file, reading complete");
            el_file_close(file_handle);
            file_handle = NULL;
            s_last_valid_offset = 0; // 重置最后有效位置
            s_recovery_mode = 0;     // 重置恢复模式
            return EL_END;
        }
    }

    // 计算读取长度
    if (s_data_file_read_info.pre_offset != 0) {
        read_len = (int)(s_data_file_read_info.pre_offset - s_data_file_read_info.cur_offset);
    } else {
        read_len = file_len - (int)s_data_file_read_info.cur_offset;
    }

    // 验证读取长度
    if (read_len <= (sizeof(pre_index_addr) + sizeof(s_end_data_flag)) || read_len > file_len) {
        el_debug(EL_RED, "invalid read length %d", read_len);

        // 检查递归深度
        if (s_recovery_depth >= MAX_RECOVERY_DEPTH) {
            el_debug(EL_RED, "Max recovery depth reached (%d), giving up recovery", s_recovery_depth);
            s_recovery_depth = 0;  // 重置递归深度
            s_recovery_mode = 0;   // 退出恢复模式
            el_file_close(file_handle);
            file_handle = NULL;
            return EL_ERROR;
        }

        // 进入恢复模式
        if (s_recovery_mode == 0) {
            s_recovery_mode = 1;
            s_recovery_depth++;   // 增加递归深度
            s_data_file_read_info.cur_offset = data_file_manger_info.last_index_offset;
            s_data_file_read_info.pre_offset = 0;

            el_debug(EL_YELLOW, "Recovery mode: reset to last_index_offset %u (attempt %d/%d)",
                    s_data_file_read_info.cur_offset, s_recovery_depth, MAX_RECOVERY_DEPTH);
            el_file_close(file_handle);
            file_handle = NULL;
            return event_log_read_data_file(data, data_len);
        }

        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 读取前一个索引地址
    file_ret = el_file_read_data_ex(file_handle, s_data_file_read_info.cur_offset,
                                   (unsigned char*)&pre_index_addr, sizeof(pre_index_addr));
    if (file_ret != sizeof(pre_index_addr)) {
        el_debug(EL_RED, "read pre_index_addr error %d", file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 验证前一个索引地址的有效性
    if (pre_index_addr != 0xFFFFFFFF &&
        (pre_index_addr >= (unsigned int)file_len || pre_index_addr < sizeof(struct el_data_file_manger_info))) {
        el_debug(EL_RED, "Invalid pre_index_addr: %u", pre_index_addr);

        // 检查递归深度
        if (s_recovery_depth >= MAX_RECOVERY_DEPTH) {
            el_debug(EL_RED, "Max recovery depth reached (%d), giving up recovery", s_recovery_depth);
            s_recovery_depth = 0;  // 重置递归深度
            s_recovery_mode = 0;   // 退出恢复模式
            el_file_close(file_handle);
            file_handle = NULL;
            return EL_ERROR;
        }

        // 尝试恢复到上一个有效位置
        if (s_recovery_mode == 0 && s_last_valid_offset > 0 &&
            s_last_valid_offset != s_data_file_read_info.cur_offset) {
            s_recovery_mode = 1;
            s_recovery_depth++;   // 增加递归深度
            el_debug(EL_YELLOW, "Trying to recover using last valid offset: %u (attempt %d/%d)",
                    s_last_valid_offset, s_recovery_depth, MAX_RECOVERY_DEPTH);
            s_data_file_read_info.cur_offset = s_last_valid_offset;
            el_file_close(file_handle);
            file_handle = NULL;
            return event_log_read_data_file(data, data_len); // 递归尝试读取
        }

        // 如果无法恢复，尝试切换到前一个文件
        s_recovery_mode = 0;  // 重置恢复模式
        if (data_file_manger_info.file_name[0] != 0x00) {
            el_debug(EL_YELLOW, "Trying to switch to previous file: %s", data_file_manger_info.file_name);
            el_memset(s_data_file_read_info.file_name, 0x00, EL_DATA_FILE_NAME_MAX);
            strcpy(s_data_file_read_info.file_name, data_file_manger_info.file_name);
            s_data_file_read_info.pre_offset = 0;
            s_data_file_read_info.cur_offset = data_file_manger_info.last_index_offset;
            s_last_valid_offset = 0;
            el_file_close(file_handle);
            file_handle = NULL;
            return EL_OK; // 返回OK让调用者继续尝试读取
        }

        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 读取实际数据
    int data_size = read_len - sizeof(pre_index_addr) - sizeof(s_end_data_flag);
    if (data_size <= 0) {
        el_debug(EL_RED, "Invalid data size: %d", data_size);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    file_ret = el_file_read_data_ex(file_handle,
                                   s_data_file_read_info.cur_offset + sizeof(pre_index_addr),
                                   (unsigned char*)data,
                                   data_size);
    if (file_ret != data_size) {
        el_debug(EL_RED, "read data error %d", file_ret);
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 恢复模式成功读取数据，退出恢复模式
    if (s_recovery_mode) {
        el_debug(EL_GREEN, "Recovery successful, exiting recovery mode");
        s_recovery_mode = 0;
        s_recovery_depth = 0;  // 重置递归深度
    }

    // 保存当前有效的读取位置
    s_last_valid_offset = s_data_file_read_info.cur_offset;

    // 更新读取位置信息
    s_data_file_read_info.pre_offset = s_data_file_read_info.cur_offset;
    s_data_file_read_info.cur_offset = pre_index_addr;
    *data_len = data_size;

    // 处理文件切换
    if (s_data_file_read_info.cur_offset == 0xFFFFFFFF) {
        if (data_file_manger_info.file_name[0] == 0x00) {
            el_debug(EL_GREEN, "No previous file, reading complete");
            el_file_close(file_handle);
            file_handle = NULL;
            s_last_valid_offset = 0; // 重置最后有效位置
            return EL_END;
        }

        el_debug(EL_GREEN, "Switching to previous file: %s", data_file_manger_info.file_name);
        el_memset(s_data_file_read_info.file_name, 0x00, EL_DATA_FILE_NAME_MAX);
        strcpy(s_data_file_read_info.file_name, data_file_manger_info.file_name);
        s_data_file_read_info.pre_offset = 0;
        s_data_file_read_info.cur_offset = data_file_manger_info.last_index_offset;
        s_last_valid_offset = data_file_manger_info.last_index_offset; // 更新最后有效位置
    }

    el_file_close(file_handle);
    file_handle = NULL;
    return EL_OK;
}

static int event_log_write(unsigned char* event_log_data, int event_log_len)
{
	int ret = 0;
    struct el_manger_info manger_info;

    if (event_log_data == NULL) {
		el_debug(EL_RED, "event_log_data == NULL error");
		return EL_ERROR;
    }

    if (event_log_len == 0) {
		el_debug(EL_RED, "event_log_len == 0 error");
		return EL_ERROR;
    }

    ret = event_log_load_manger_info(&manger_info);
    if (ret == EL_ERROR) {
		el_debug(EL_RED, "load manger info error");
		return EL_ERROR;
    }

    ret = event_log_write_data_file(&manger_info, event_log_data, event_log_len);
    if (ret == EL_ERROR) {
		el_debug(EL_RED, "write data file error");
		return EL_ERROR;
    }

    event_log_mgr_file_table_index_next();
    ret = event_log_updata_manger_info(&manger_info);
    if (ret == EL_ERROR) {
		el_debug(EL_RED, "updata manger info error");
		return EL_ERROR;
    }

    #ifdef __EL_OUTPUT_DEBUG__
    event_log_manger_info_debug(manger_info);
    #endif

    return EL_OK;
}

int event_log_read_reset_first_index(void)
{
    int ret = 0;
    struct el_manger_info manger_info;
    struct el_data_file_manger_info data_file_manger_info;
    FILE* file_handle = NULL;
    // 重置恢复模式和有效位置
    s_recovery_mode = 0;
    s_last_valid_offset = 0;
    s_recovery_depth = 0;  // 重置递归深度

    // 清空读取信息结构
    memset(&s_data_file_read_info, 0, sizeof(struct el_data_file_read_info));

    ret = event_log_load_manger_info(&manger_info);
    if (ret == EL_ERROR) {
        el_debug(EL_RED, "load manger info error");
        return EL_ERROR;
    }

    if (manger_info.record_id == 0) {
        return EL_NULL;
    }

    // 检查文件是否存在
    if (access(manger_info.file_name, F_OK) != 0) {
        el_debug(EL_RED, "File %s does not exist", manger_info.file_name);
        return EL_ERROR;
    }

    // 先尝试打开当前文件
    file_handle = el_file_open(manger_info.file_name, "r");
    if (file_handle == NULL) {
        el_debug(EL_RED, "open %s error", manger_info.file_name);
        return EL_ERROR;
    }

    // 读取文件头管理信息
    ret = el_file_read_data_ex(file_handle, 0, (unsigned char*)&data_file_manger_info,
                              sizeof(struct el_data_file_manger_info));

    if (ret != sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "read manager info error");
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 文件完整性校验
    if (data_file_manger_info.check[0] != 0x12345678 || data_file_manger_info.check[1] != 0xABCDEF00) {
        el_debug(EL_RED, "File integrity check failed");
        el_file_close(file_handle);
        file_handle = NULL;
        return EL_ERROR;
    }

    // 验证last_index_offset的有效性
    unsigned int file_size = el_file_get_size(file_handle);
    if (data_file_manger_info.last_index_offset >= file_size ||
        data_file_manger_info.last_index_offset < sizeof(struct el_data_file_manger_info)) {
        el_debug(EL_RED, "Invalid last_index_offset: %u, file size: %u",
                data_file_manger_info.last_index_offset, file_size);
        // 重置为文件头之后的位置
        data_file_manger_info.last_index_offset = sizeof(struct el_data_file_manger_info);
    }

    el_file_close(file_handle);
    file_handle = NULL;

    // 设置读取起始位置
    el_memset(s_data_file_read_info.file_name, 0x00, EL_DATA_FILE_NAME_MAX);
    strcpy(s_data_file_read_info.file_name, manger_info.file_name);

    // 重要：始终从文件的最后一条记录开始读取
    s_data_file_read_info.pre_offset = 0;
    s_data_file_read_info.cur_offset = data_file_manger_info.last_index_offset;
    s_last_valid_offset = data_file_manger_info.last_index_offset;

    el_debug(EL_GREEN, "Reset read position: file=%s, offset=%u",
             s_data_file_read_info.file_name, s_data_file_read_info.cur_offset);

    s_data_file_read_info.init_flag = 1;
    return EL_OK;
}

static int event_log_read(void* data, int* data_len)
{
    int read_ret = 0;

    if (s_data_file_read_info.init_flag == 0) {
        el_debug(EL_RED, "s_data_file_read_info not init");
        return EL_ERROR;
    }

    if (data == NULL) {
        el_debug(EL_RED, "data == NULL");
        return EL_ERROR;
    }

    if (data_len == NULL) {
        el_debug(EL_RED, "data_len == NULL");
        return EL_ERROR;
    }

    read_ret = event_log_read_data_file(data, data_len);
    return read_ret;
}

static void event_log_time_stamp(char* buf)
{
    time_t nSeconds;
    struct tm* pTM;

    time(&nSeconds);
    pTM = localtime(&nSeconds);

    /* 系统日期和时间,格式: yyyymmddHHMMSS */
    sprintf(buf, "%04d-%02d-%02d %02d:%02d:%02d",
            pTM->tm_year + 1900, pTM->tm_mon + 1, pTM->tm_mday,
            pTM->tm_hour, pTM->tm_min, pTM->tm_sec);

    //el_debug(EL_GREEN, "time stamp = [%s]", buf);
}

#if 0
static void event_log_time_up(char* buf)
{
	int file_ret = 0;
	FILE* file_handle = NULL;
	unsigned long time = 0;
	unsigned char buf_index = 0;
	char read_buf[20] = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

    //system("cat /proc/uptime");
	file_handle = el_file_open("/proc/uptime", "r");
	if (file_handle == NULL) {
		el_debug(EL_RED, "time_up open /proc/uptime error");
		return;
	}

    file_ret = el_file_read_data(file_handle, (unsigned char*)read_buf, sizeof(read_buf));
    el_file_close(file_handle);
    file_handle = NULL;
    if (file_ret <= 0) {
        el_debug(EL_RED, "time_up read /proc/uptime error file_ret = %d", file_ret);
        return;
    }

    for (buf_index = 0; buf_index < 20; buf_index++) {
        if (read_buf[buf_index] == '.') {
            read_buf[buf_index] = '\0';
            break;
        }
    }

    time = strtoul(read_buf, NULL, 10); /* 第二参数返回字符串部分 10表示10进制 */
    sprintf(buf, "%02d:%02d:%02d", (unsigned int)(time/3600), (unsigned int)((time%3600)/60), (unsigned int)((time%3600)%60));
    //el_debug(EL_GREEN, "time up    = [%s]", buf);
}
#endif

static void event_log_set_page_count(EVENT_LOG_PAGE_COUNT_VALUE count_value,unsigned int count)
{
    switch(count_value)
    {
        case EL_PAGE_ADF_COUNT:
            pthread_mutex_lock(&g_page_count_mutex);
            el_debug(EL_GREEN, "-L%d    set adf_count %u -> %u\n",__LINE__,g_event_log_page_count.adf_count,count);
            g_event_log_page_count.adf_count = count;
            pthread_mutex_unlock(&g_page_count_mutex);

            break;

        case EL_PAGE_FB_COUNT:
            pthread_mutex_lock(&g_page_count_mutex);
            el_debug(EL_GREEN, "-L%d    set fb_count %u -> %u\n",__LINE__,g_event_log_page_count.fb_count,count);
            g_event_log_page_count.fb_count = count;
            pthread_mutex_unlock(&g_page_count_mutex);

            break;

        case EL_PAGE_TOTAL_PRINT_COUNT:
        case EL_PAGE_TOTAL_COPY_COUNT:
            pthread_mutex_lock(&g_page_count_mutex);
            if(count_value == EL_PAGE_TOTAL_PRINT_COUNT)
            {
                el_debug(EL_GREEN, "-L%d    set total_print_count %u -> %u\n",__LINE__,g_event_log_page_count.print_count,count);
                g_event_log_page_count.print_count = count;
            }
            else
            {
                el_debug(EL_GREEN, "-L%d    set copy_count %u -> %u\n",__LINE__,g_event_log_page_count.copy_count,count);
                g_event_log_page_count.copy_count = count;
            }
            g_event_log_page_count.total_count = g_event_log_page_count.print_count + g_event_log_page_count.copy_count;
            el_debug(EL_GREEN, "-L%d    print %u + copy %u = total %u\n",__LINE__,
                                                                                  g_event_log_page_count.print_count,
                                                                                  g_event_log_page_count.copy_count,
                                                                                  g_event_log_page_count.total_count);

            pthread_mutex_unlock(&g_page_count_mutex);

            break;

        default:
            el_debug(EL_GREEN, "no match key\n");
            break;

    }
}

static EVENT_LOG_PAGE_COUNT_S event_log_get_page_count(unsigned int* adf_count, unsigned int* fb_count, unsigned int* total_count)
{
    pthread_mutex_lock(&g_page_count_mutex);

    if(adf_count != NULL)
    {
        *adf_count = g_event_log_page_count.adf_count;
    }

    if(fb_count != NULL)
    {
        *fb_count = g_event_log_page_count.fb_count;
    }

    if(adf_count != NULL)
    {
        *total_count = g_event_log_page_count.total_count;
    }
    el_debug(EL_GREEN, "-L%d    adf_count = %u,fb_count = %u,total_print_count = %u\n",__LINE__,g_event_log_page_count.adf_count,g_event_log_page_count.fb_count,g_event_log_page_count.total_count);

    pthread_mutex_unlock(&g_page_count_mutex);

    return g_event_log_page_count;
}


EVENT_LOG_TYPE_JOB_E event_log_get_log_page_count(void* data, uint32_t data_length)
{
    struct event_log_category_job event_log;

    if(data == NULL || data_length < sizeof(int) * 3)
    {
        el_debug(EL_RED, "-L%d    data = null or datalenth < sizeof(int) *3\n",__LINE__);
        return -1;
    }

    data = (char*) data + (sizeof(int) * 2);
    memcpy(&event_log.event_log_common_param.total_print_count, data, sizeof(int));

    return event_log.event_log_common_param.total_print_count;
}


static void event_log_event_callback(const EVT_MSG_S* msg, void* ctx)
{
    uint32_t module_id = msg->module_id;
    uint32_t event_type = msg->event_type;
    uint32_t data_length = msg->data_length;
    EVENT_LOG_TYPE_JOB_E page_count = -1;

    printf("-L%d    module_id %u, event_type = 0x%x\n",__LINE__,module_id,event_type);
    switch(event_type)
    {
       /* case EVT_TYPE_PRINTERSTATUS_TEST:
            pi_log_d("EVT_TYPE_PRINTERSTATUS_TEST ... \n");
            break;*/

        case EVT_TYPE_EVENTLOG_SYSTEM_STATUS:
            event_log_category_wirte_system_status(module_id, msg->data, data_length);

            break;
        case EVT_TYPE_EVENTLOG_FIRMWARE:
            event_log_category_wirte_firmware(module_id, msg->data);

            break;

        case EVT_TYPE_EVENTLOG_JOB:
            event_log_category_wirte_job(module_id, msg->data, data_length);

            break;
        case EVT_TYPE_EVENTLOG_CONSUMABLE:
            event_log_category_wirte_consumable(module_id, msg->data, data_length);

            break;

        case EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_NUM_MODIFY:
            if(msg->data != NULL && data_length >= sizeof(int))
            {
                page_count = *(uint32_t*)msg->data;
                event_log_set_page_count(EL_PAGE_FB_COUNT,page_count);
            }
            break;

        case EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_NUM_MODIFY:
            if(msg->data != NULL || data_length >= sizeof(int))
            {
                page_count = *(uint32_t*)msg->data;
                event_log_set_page_count(EL_PAGE_ADF_COUNT,page_count);
            }
            break;

        case EVT_TYPE_PRINT_STATISTIC_INFO:
            page_count = event_log_get_log_page_count(msg->data,data_length);
            if(page_count == -1)
            {
                pi_log_d("get data error,break\n");
                break;
            }
            el_debug(EL_GREEN, "-L%d  print.page_count = %u\n",__LINE__,page_count);
            event_log_set_page_count(EL_PAGE_TOTAL_PRINT_COUNT,page_count);
            break;
        case EL_PAGE_TOTAL_COPY_COUNT:
            page_count = event_log_get_log_page_count(msg->data,data_length);
            if(page_count == -1)
            {
                pi_log_d("get data error,break\n");
                break;
            }

            el_debug(EL_GREEN, "-L%d  copy.page_count = %u\n",__LINE__,page_count);
            event_log_set_page_count(EL_PAGE_TOTAL_COPY_COUNT,page_count);
            break;
        case EVT_TYPE_PRINT_CONSUMPTION_INFO:
            RETURN_IF(msg->data == NULL || msg->data_length != sizeof(PRINT_CONFIG_CONSUMPTION_INFO_S), pi_log_w);
            PRINT_CONFIG_CONSUMPTION_INFO_S* info = (PRINT_CONFIG_CONSUMPTION_INFO_S *)msg->data;

            pi_log_w("EVT_TYPE_PRINT_CONSUMPTION_INFO:\n remain toner(%u-%u-%u-%u) \n remain waste_toner %u",
                    info->tb_c_remain, info->tb_m_remain, info->tb_y_remain, info->tb_k_remain,info->waste_toner_remain);

            pthread_mutex_lock(&g_consumable_status_mutex);
            g_consumable_status.waste_toner_remain = info->waste_toner_remain;
            g_consumable_status.toner_k_rem = info->tb_k_remain;
            g_consumable_status.toner_c_rem = info->tb_c_remain;
            g_consumable_status.toner_m_rem = info->tb_m_remain,
            g_consumable_status.toner_y_rem = info->tb_y_remain;
            pthread_mutex_unlock(&g_consumable_status_mutex);

            break;

        default:
            pi_log_d("unknown event type: %u\n", event_type);
            break;
    }

}

EVT_MGR_CLI_S * event_log_event_register(void)
{
    EVT_MGR_CLI_S * cli_ptr = pi_event_mgr_create_client(EVT_MODULE_EVENTLOG, event_log_event_callback, NULL, NULL);
    if(cli_ptr == NULL)
    {
        return NULL;
    }

    uint32_t modify_event_array[] = {
        // EVT_TYPE_EVENTLOG_TEST,
        EVT_TYPE_EVENTLOG_SYSTEM_STATUS,
        EVT_TYPE_EVENTLOG_FIRMWARE,
        EVT_TYPE_EVENTLOG_JOB,
        EVT_TYPE_EVENTLOG_CONSUMABLE,

        EVT_TYPE_SCAN_TOTAL_FBSCAN_PAGE_NUM_MODIFY,
        EVT_TYPE_SCAN_TOTAL_ADFSCAN_PAGE_NUM_MODIFY,
        EVT_TYPE_PRINT_STATISTIC_INFO,

        EVT_TYPE_PRINT_CONSUMPTION_PARAM, //废粉容量
        EVT_TYPE_PRINT_CONSUMPTION_INFO, //c k m y余量
    };

    int32_t event_count = sizeof(modify_event_array) / sizeof(modify_event_array[0]);

    int ret = pi_event_mgr_register( cli_ptr, modify_event_array, event_count );
    if( ret < 0 )
    {
        pi_log_e( "fail to call pi_event_mgr_register, ret: %d\n", ret );
        pi_event_mgr_destroy_client( cli_ptr );
    }

    return cli_ptr;
}

int event_log_prolog(void)
{
    int ret = 0;
    int file_ret = 0;
    int access_ret = 0;

    //char temp_time_asc[100];
    unsigned int i = 0;
    unsigned int recode_max = 0;

    FILE* file_handle = NULL;
    struct el_manger_info manger_info;

    g_pst_cli_ptr = event_log_event_register();
    pi_log_d("event_log event register ... \n");
    if( g_pst_cli_ptr == NULL )
    {
        pi_log_e("event_log event init error\n");
    }

    access_ret = access(EL_INIT_FLAG_FILE, F_OK);
    if (access_ret != 0) {
        file_handle = el_file_open(EL_INIT_FLAG_FILE, "wb+");
        if (file_handle == NULL) {
            el_debug(EL_RED, "Create %s error", EL_INIT_FLAG_FILE);
            return EL_ERROR;
        }

        fflush(file_handle);
        fsync(fileno(file_handle));

        el_file_close(file_handle);
        file_handle = NULL;

        for (i = 0; i < EL_MANGER_FILE_MAX; i++) {
            access_ret = access(s_mgr_file_table[i], F_OK);
            if (access_ret != 0) {
                file_handle = el_file_open(s_mgr_file_table[i], "wb+");
                if (file_handle == NULL) {
                    el_debug(EL_RED, "Create %s error", s_mgr_file_table[i]);
                    return EL_ERROR;
                }

                el_memset(&manger_info, 0x00, sizeof(struct el_manger_info));
                sprintf(manger_info.file_name, "%s%05d", EL_DATA_FILE_NANE_BASE, EL_DATA_FILE_NANE_NUM_MIN);
                manger_info.offset = sizeof(struct el_data_file_manger_info);
                manger_info.record_id  = 0;
                manger_info.record_sum = 0;
                manger_info.file_sum   = 1;
                manger_info.file_id    = 1;
                manger_info.check[0]   = 0x12345678;
                manger_info.check[1]   = 0xABCDEF00;

                file_ret = el_file_write_data(file_handle, (unsigned char*)(&manger_info), sizeof(struct el_manger_info));
                if (file_ret != sizeof(struct el_manger_info)) {
                    el_debug(EL_RED, "write %s error %d", manger_info.file_name, file_ret);
                    el_file_close(file_handle);
                    file_handle = NULL;
                    return EL_ERROR;
                }

                fflush(file_handle);
                fsync(fileno(file_handle));

                el_file_close(file_handle);
                file_handle = NULL;
            } else {
                el_debug(EL_RED, "write %s error %d", manger_info.file_name, file_ret);
                return EL_ERROR;
            }
        }

        ret = event_log_creat_data_file(manger_info.file_name, NULL);
        if (ret == EL_ERROR) {
            return ret;
        }

        if (s_mgr_file_table_index == 0xFFFFFFFF) {
            s_mgr_file_table_index = 0;
        }
    } else {
        for (i = 0; i < EL_MANGER_FILE_MAX; i++) {
            access_ret = access(s_mgr_file_table[i], F_OK);
            if (access_ret == 0) {
                ret = event_log_mgr_file_table_index_init(i, &recode_max);
                if (ret == EL_ERROR) {
                    return ret;
                }

            } else {
                el_debug(EL_RED, "%s not find", s_mgr_file_table[i]);
                return EL_ERROR;
            }
        }
    }

    s_data_file_read_info.init_flag = 0;
    //event_log_time_stamp(temp_time_asc);
    //event_log_time_up(temp_time_asc);

    return EL_OK;
}

static int event_log_category_wirte_firmware(uint32_t module_id, void* data)
{
    int ret = 0;
    struct event_log_category_firmware event_log = {0};

    event_log.category = EL_CATEGORY_FIRMWARE;

    event_log_get_page_count(&event_log.event_log_common_param.adf_count,&event_log.event_log_common_param.fb_count,&event_log.event_log_common_param.total_print_count);
    event_log.event_log_common_param.module_id = module_id;

    event_log_time_stamp(event_log.event_log_common_param.time_stamp);

    ret = pi_nvram_get(PLATFORM_ID_FIRMWARE_VERSION, VTYPE_STRING, event_log.version_6270 , FIRMWARE_BUF_SIZE);
    pi_log_d("event_log.version_6270: %u\n", event_log.version_6270);
    CHECK_GET_NV_RESULT(ret);

    ret = pi_nvram_get(PLATFORM_ID_ENG_FW_VERSION, VTYPE_STRING, event_log.version_printer_engine , FIRMWARE_BUF_SIZE);
    CHECK_GET_NV_RESULT(ret);

    ret = pi_nvram_get(PLATFORM_ID_PANEL_FW_VERSION, VTYPE_STRING, event_log.version_panel , FIRMWARE_BUF_SIZE);
    CHECK_GET_NV_RESULT(ret);

    ret = pi_nvram_get(PLATFORM_ID_FPGA_FRONT_VERSION, VTYPE_STRING, event_log.version_fpga1 , FIRMWARE_BUF_SIZE);
    CHECK_GET_NV_RESULT(ret);

    ret = pi_nvram_get(PLATFORM_ID_FPGA_BACK_VERSION, VTYPE_STRING, event_log.version_fpga2 , FIRMWARE_BUF_SIZE);
    CHECK_GET_NV_RESULT(ret);

    ret = pi_nvram_get(PLATFORM_ID_M6220_FRONT_VERSION, VTYPE_STRING, event_log.version_6220_a , FIRMWARE_BUF_SIZE);
    CHECK_GET_NV_RESULT(ret);

    ret = pi_nvram_get(PLATFORM_ID_M6220_BACK_VERSION, VTYPE_STRING, event_log.version_6220_b , FIRMWARE_BUF_SIZE);
    CHECK_GET_NV_RESULT(ret);

    ret = event_log_write((unsigned char*)&event_log, sizeof(struct event_log_category_firmware));
    return ret;
}

static int event_log_category_wirte_job(uint32_t module_id,void* data, uint32_t data_length)
{
    int ret = 0;
    struct event_log_category_job event_log;

    event_log.category = EL_CATEGORY_JOB;
    if(module_id == EVT_MODULE_PRINT)
    {
        if(data == NULL || data_length < sizeof(int) * 3)
        {
            el_debug(EL_RED, "-L%d    data = null or datalenth < sizeof(int) *3\n",__LINE__);
            return -1;
        }

        memcpy(&event_log.type, data, sizeof(int));
        data = (char*) data + sizeof(int);

        memcpy(&event_log.job_module, data, sizeof(int));
        data = (char*) data + sizeof(int);

        memcpy(&event_log.current_page_count, data, sizeof(int));
        el_debug(EL_GREEN, "-L%d  type = %d,job_module = %d,current_page_count = %d\n",__LINE__,event_log.type,event_log.job_module,event_log.current_page_count);

    }
    else if(module_id == EVT_MODULE_SCAN)
    {
        if(data == NULL || data_length < sizeof(int))
        {
            el_debug(EL_RED, "-L%d    data = null or datalenth < sizeof(int) *3\n",__LINE__);
            return -1;
        }

        event_log.job_module = EL_NAME_JOB_SCAN;
        event_log.current_page_count = *(uint32_t*)data;
    }
    event_log_get_page_count(&event_log.event_log_common_param.adf_count,&event_log.event_log_common_param.fb_count,&event_log.event_log_common_param.total_print_count);

    event_log.event_log_common_param.module_id = module_id;

    event_log_time_stamp(event_log.event_log_common_param.time_stamp);
    ret = event_log_write((unsigned char*)&event_log, sizeof(struct event_log_category_job));
    return ret;
}

static int event_log_category_wirte_consumable(uint32_t module_id, void* data, uint32_t data_length)
{
    int ret = 0;
    struct event_log_category_consumable event_log;

    if(data == NULL || data_length < sizeof(int))
    {
        el_debug(EL_RED, "-L%d    data = null or datalenth < sizeof(int)\n",__LINE__);
        return -1;
    }

    event_log.consumable_type = *(uint32_t*)data;
    el_debug(EL_GREEN, "-L%d    event_log.consumable_type = %d\n",__LINE__,event_log.consumable_type);

    event_log.category = EL_CATEGORY_CONSUMABLE;
    event_log_get_page_count(&event_log.event_log_common_param.adf_count,&event_log.event_log_common_param.fb_count,&event_log.event_log_common_param.total_print_count);
    event_log.event_log_common_param.module_id = module_id;

    event_log_time_stamp(event_log.event_log_common_param.time_stamp);
    ret = event_log_write((unsigned char*)&event_log, sizeof(struct event_log_category_consumable));
    return ret;
}

static int event_log_category_wirte_system_status(uint32_t module_id, void* data, uint32_t data_length)
{
    int ret = 0;
    struct event_log_category_system_status event_log;
    int32_t status_id = 0;

    if(data == NULL || data_length < sizeof(int))
    {
        el_debug(EL_RED, "-L%d    data = null or datalenth < sizeof(int) *3\n",__LINE__);
        return -1;
    }

    uint32_t err_num = data_length / 4;
    pi_log_d("err_num: %u\n", err_num);
    for(int i = 0; i < err_num; i++)
    {
        memcpy(&status_id, data, sizeof(int));

        if(i < err_num - 1)
        {
            data = (char*) data + (sizeof(int) * err_num);
        }
        el_debug(EL_GREEN, "-L%d    status_id = 0x%x\n",__LINE__,status_id);

        event_log.category = EL_CATEGORY_STATUS;
        event_log_get_page_count(&event_log.event_log_common_param.adf_count,&event_log.event_log_common_param.fb_count,&event_log.event_log_common_param.total_print_count);
        event_log.event_log_common_param.module_id = module_id;

        event_log.status_id = status_id;

        event_log_time_stamp(event_log.event_log_common_param.time_stamp);
        ret = event_log_write((unsigned char*)&event_log, sizeof(struct event_log_category_system_status));

    }
    return ret;
}


int event_log_category_read(enum event_log_category* category, enum event_log_name* name, void* data, int* data_len)
{
    int read_ret = 0;
    unsigned int* ptr = NULL;

    read_ret = event_log_read(data, data_len);
    if (read_ret == EL_OK || read_ret == EL_END) {
        ptr = (unsigned int*)data;
        *category = (enum event_log_category)*ptr;
        ptr++;
        *name = (enum event_log_name)*ptr;
    } else {
        *category = EL_CATEGORY_MAX; /* 无效值 */
        *name = EL_NAME_MAX;         /* 无效值    */
        *data_len = 0;
    }

    return read_ret;
}

static void event_log_write_head(FILE* file_handle, const char* title1, const char* title2, const char* title3, const char* title4)
{
    char log_buf[256] = {0};

    if (file_handle == NULL) {
        el_debug(EL_RED, "file_handle == NULL");
        return;
    }

    sprintf(log_buf, "%-19s %-10s %-10s %-20s %-30s\r\n", title1, title2, title3, title4, "");
    fwrite(log_buf, strlen(log_buf), 1, file_handle);
}

static void event_log_write_summary(FILE* file_handle, const char* time_stamp, const char* page_count, const char* adf_count, const char* event_name, const char* details)
{
    char log_buf[256] = {0};

    if (file_handle == NULL) {
        el_debug(EL_RED, "file_handle == NULL");
        return;
    }

    sprintf(log_buf, "%-19s %-10s %-10s %-20s %-30s\r\n",
            time_stamp, page_count, adf_count, event_name, details ? details : "");
    fwrite(log_buf, strlen(log_buf), 1, file_handle);
}

static void event_log_write_Summary(FILE* fp_dst, const char* time_stamp, const char* page_count, const char* adf_count, const char* event_name, const char* details)
{
    char log_buff[256] = {0};
    snprintf(log_buff, sizeof(log_buff), "%-25s %-12s %-12s %-35s %s\n",
             time_stamp, page_count, adf_count, event_name, details ? details : "");
    fwrite(log_buff, strlen(log_buff), 1, fp_dst);
}

static int el_write_summary_line(FILE* fp, const char* format, ...)
{
    char log_buff[512];
    va_list args;

    va_start(args, format);
    int len = vsnprintf(log_buff, sizeof(log_buff), format, args);
    va_end(args);

    if (len < 0) {
        return EL_ERROR;
    }

    if (fwrite(log_buff, len, 1, fp) != 1) {
        return EL_ERROR;
    }

    return EL_OK;
}

/**
 * @brief Writes the complete, aligned header section to the summary file.
 * @param fp_dst File pointer.
 * @return EL_OK on success, EL_ERROR on failure.
 */
static int event_log_write_summary_headers(FILE* fp_dst)
{
    time_t timep;
    struct tm* ptr;
    struct summary_header_data header;
    memset(&header, 0, sizeof(header));

    // Initialize string fields to "N/A"
    snprintf(header.rip_fw, sizeof(header.rip_fw), "N/A");
    snprintf(header.engine_fw, sizeof(header.engine_fw), "N/A");
    snprintf(header.maint_kit_rem, sizeof(header.maint_kit_rem), "N/A");

    // 获取序列号和打印机名称
    pi_platform_get_print_name_string(header.machine_series, sizeof(header.machine_series));
    pi_platform_get_serial_number(header.serial_number, sizeof(header.serial_number));

    // Get consumable status safely
    pthread_mutex_lock(&g_consumable_status_mutex);
    snprintf(header.waste_toner_rem, sizeof(header.waste_toner_rem), "%d%%", g_consumable_status.waste_toner_remain);
    snprintf(header.toner_k_rem, sizeof(header.toner_k_rem), "%d%%", g_consumable_status.toner_k_rem);
    snprintf(header.toner_c_rem, sizeof(header.toner_c_rem), "%d%%", g_consumable_status.toner_c_rem);
    snprintf(header.toner_m_rem, sizeof(header.toner_m_rem), "%d%%", g_consumable_status.toner_m_rem);
    snprintf(header.toner_y_rem, sizeof(header.toner_y_rem), "%d%%", g_consumable_status.toner_y_rem);
    pthread_mutex_unlock(&g_consumable_status_mutex);

    // Get page counts
    event_log_get_page_count(&header.adf_count, &header.fb_count, &header.page_count);
    header.scan_count = header.adf_count + header.fb_count;

    // Get current time
    time(&timep);
    ptr = localtime(&timep);
    if (ptr == NULL) {
        snprintf(header.time_str, sizeof(header.time_str), "N/A");
    } else {
        strftime(header.time_str, sizeof(header.time_str), "%b %d %Y %H:%M:%S", ptr);
    }

    // Write all header information to the file
    WRITE_LINE_CHECKED(fp_dst, "Event Log Summary\n");
    WRITE_LINE_CHECKED(fp_dst, "Pantum %s Series\t\t%s\t\t%-25s\t%s\n", header.machine_series, header.serial_number, "Maint Kit % Remaining", header.maint_kit_rem);
    WRITE_LINE_CHECKED(fp_dst, "%-12s\t%-15s\t%-25s\t%s\n", "TLI/SPR", "N/A", "Waste Toner % Remaining", header.waste_toner_rem);
    WRITE_LINE_CHECKED(fp_dst, "%-12s\t%-15s\t%-25s\n", "RIP FW", header.rip_fw, "Supplies % Remaining");
    WRITE_LINE_CHECKED(fp_dst, "%-12s\t%-15s\t%-15s\t%s\n", "Engine FW", header.engine_fw, "Black", header.toner_k_rem);
    WRITE_LINE_CHECKED(fp_dst, "%-12s\t%-15u\t%-15s\t%s\n", "Page Count", header.page_count, "Cyan", header.toner_c_rem);
    WRITE_LINE_CHECKED(fp_dst, "%-12s\t%-15u\t%-15s\t%s\n", "Scan Count", header.scan_count, "Magenta", header.toner_m_rem);
    WRITE_LINE_CHECKED(fp_dst, "%-12s\t%-15s\t%-15s\t%s\n", "Date / Time", header.time_str, "Yellow", header.toner_y_rem);

    // Placeholder for long option strings
    char installed_options[] = "550-Sheet Drawer,Duplex";
    char solutions_apps[] = "Framework,Display Customization,Shortcut Center,Scan Center-E-mail,Card Copy,Scan Center,Scan Center-printer,Scan Center - Network Folders,Scan Center-Fax";
    WRITE_LINE_CHECKED(fp_dst, "%-18s\t%-25s\t%-18s\t%s\n", "Installed Options", installed_options, "Solutions / Apps", solutions_apps);

    const char* separator = "-----------------------------------------------------------------------------------------------------------------------------------------\n";
    WRITE_LINE_CHECKED(fp_dst, separator);
    WRITE_LINE_CHECKED(fp_dst, "%-25s %-12s %-12s %-35s %s\n", "Date / Time", "Page Count", "ADF Count", "Event Name", "Details");
    WRITE_LINE_CHECKED(fp_dst, separator);

    return EL_OK;
}

static void event_log_format_event_line_data(struct summary_line_data* line_data,
                                 const EVENT_LOG_COMMON_PARAM_S* common_param,
                                 const char* event_name) {
    if (!line_data || !common_param || !event_name) return;

    snprintf(line_data->time_stamp, sizeof(line_data->time_stamp), "%s", common_param->time_stamp);
    snprintf(line_data->page_count, sizeof(line_data->page_count), "%u", common_param->total_print_count);
    snprintf(line_data->adf_count, sizeof(line_data->adf_count), "%u", common_param->adf_count);
    snprintf(line_data->event_name, sizeof(line_data->event_name), "%s", event_name);
    snprintf(line_data->details, sizeof(line_data->details), "PorCount:0");
}

int event_log_export_log_summary(const char* summary_file_path)
{
    if(summary_file_path == NULL) {
        el_debug(EL_RED, "[EL] Export path is NULL");
        return EL_ERROR;
    }

    enum event_log_category category;
    enum event_log_name name;

    FILE* fp_dst = NULL;

    void* data = NULL;

    int ret = EL_ERROR;
    int data_len = 0;
    int loop_ret;

    //这里加互斥锁的目的：确保同一时间只有一个线程可以执行此函数，实现线程安全
    //锁内的整体看作为一个原子操作，是一个不可分割的操作
    //任务包括：1. 打开文件 2. 写入头部信息 3. 分配内存 4. 读取事件日志 5. 关闭文件
    pthread_mutex_lock(&g_export_summary_mutex);

    do {
        fp_dst = el_file_open(summary_file_path, "w");
        if (fp_dst == NULL) {
            el_debug(EL_RED, "[EL] Failed to open summary file: %s", summary_file_path);
            break;
        }

        if (event_log_write_summary_headers(fp_dst) != EL_OK) {
            el_debug(EL_RED, "[EL] Failed to write summary headers");
            break;
        }

        data = malloc(DATA_BUFFER_SIZE);
        if (data == NULL) {
            el_debug(EL_RED, "[EL] Malloc failed for data buffer");
            break;
        }

        if (event_log_read_reset_first_index() != EL_OK) {
            // The function returns EL_NULL for empty log, which is fine.
            el_debug(EL_RED, "[EL] event file null");
            pthread_mutex_unlock(&g_export_summary_mutex);
            return EL_NULL;
        }

        while (1) {
            memset(data, 0x00, DATA_BUFFER_SIZE);
            data_len = DATA_BUFFER_SIZE;
            loop_ret = event_log_category_read(&category, &name, data, &data_len);

            if (loop_ret == EL_ERROR || loop_ret == EL_NULL) {
                break; // No more entries or error
            }

            struct summary_line_data line_data;
            memset(&line_data, 0, sizeof(line_data));

            switch (category) {
                case EL_CATEGORY_STATUS: {
                    struct event_log_category_system_status* status_log = (struct event_log_category_system_status*)data;
                    event_log_format_event_line_data(&line_data, &status_log->event_log_common_param,
                                         event_log_status_id_to_string(status_log->status_id));
                    break;
                }
                case EL_CATEGORY_JOB: {
                    struct event_log_category_job* job_log = (struct event_log_category_job*)data;
                    event_log_format_event_line_data(&line_data, &job_log->event_log_common_param,
                                         event_log_job_type_to_string(job_log->job_module));
                    break;
                }
                case EL_CATEGORY_CONSUMABLE: {
                    struct event_log_category_consumable* consumable_log = (struct event_log_category_consumable*)data;
                    event_log_format_event_line_data(&line_data, &consumable_log->event_log_common_param,
                                         event_log_consumable_type_to_string(consumable_log->consumable_type));
                    break;
                }
                case EL_CATEGORY_FIRMWARE: {
                    struct event_log_category_firmware* firmware_log = (struct event_log_category_firmware*)data;
                    event_log_format_event_line_data(&line_data, &firmware_log->event_log_common_param, "Firmware Upgrade");
                    break;
                }
                default:
                    el_debug(EL_YELLOW, "[EL] Unknown event category: %d", category);
                    continue; // Skip unknown categories
            }

            if(line_data.time_stamp[0] == '\0') {
                if(loop_ret == EL_END) {
                    break;
                }
                continue;
            }

            if (el_write_summary_line(fp_dst, "%-25s %-12s %-12s %-35s %s\n",
                                      line_data.time_stamp,
                                      line_data.page_count,
                                      line_data.adf_count,
                                      line_data.event_name,
                                      line_data.details) != EL_OK)
            {
                el_debug(EL_RED, "[EL] Failed to write event line to summary");
                // Decide if we should abort or continue
                break;
            }

            if (loop_ret == EL_END) {
                break; // End of all logs
            }
        }

        ret = EL_OK; // Success

    } while(0);

    if (data != NULL) {
        free(data);
    }
    if (fp_dst != NULL) {
        fclose(fp_dst);
    }

    // 在函数返回前，解锁互斥锁
    pthread_mutex_unlock(&g_export_summary_mutex);

    return ret;
}



