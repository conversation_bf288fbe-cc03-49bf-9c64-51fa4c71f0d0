/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       pip_mgr.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR>
 * @date       2018-11-21
 * @version    v1.0
 * @details    message queue data description
 */

#ifndef _PIP_MGR_H
#define _PIP_MGR_H

/*
#define MQ_NAME_0 "/copyapp_mq_0"
#define MQ_NAME_1 "/copyapp_mq_1"
#define MQ_NAME_2 "/copyapp_mq_2"



struct scan_message_s
{
   char output_filename[256];
   char mq_name[256];
};

typedef enum
{
    SCAN_STRIP_DATA = 0,
    SCAN_PAGE_START = 1,
    SCAN_PAGE_END   = 2,
    SCAN_THRD_STOP  = 3,
    SCAN_DATA_TYPE_END_OF_ENUM

} jpeg_data_type_t;

struct meta_data_s
{
   uint32_t pixel_width;
   uint32_t bits_per_pixel;
   uint32_t num_rows;
   bool last_buffer;
};

struct scan_data_s
{
   jpeg_data_type_t data_type;
   struct meta_data_s meta_data;
   struct dma_alloc_s dma_alloc;
};
*/

#include "pip_page_adaptor.h"
#include "pip_strip_adaptor.h"
#include "pip_init_api.h"
#include "dma_buffer.h"
#include "dma_alloc_api.h"
#include "uio_lib_api.h"

#define PIP_OUT_XRES        600 ///< output resolution
#define PIP_OUT_BPP         1   ///< output bit per pixel
#define PIP_OUT_BANDH       128 ///< output band height
#define PIP_COMBINE_LINE    128 ///< conbine line

#define PIP_DEBUG_ON        5   ///< debug on level
#define PIP_DEBUG_OFF       10  ///< debug off level

/**
 * @brief page out
 */
typedef struct page_out_s
{
    int         width;  ///< page width
}PAGE_PIP_OUT;

/**
 * @brief process page info
 */
 struct page_blob_s
{
    uint32_t    width;          ///< page width
    uint32_t    height;         ///< page height
    uint8_t     bits_per_pixel; ///< bits_per_pixel
    uint32_t    pip_mode;       ///< pip mode
    uint32_t    clip_left;      ///< clip left
    uint32_t    clip_right;     ///< clip right
    uint32_t    pad_left;       ///< pad left
    uint32_t    pad_right;      ///< pad right
    uint32_t    scale_x;        ///< scale percent x
    uint32_t    scale_y;        ///< scale percent y
    uint32_t    rgb2mono;       ///< rgb to mono
};

/**
 * @brief strip info
 */
struct strip_blob_s
{
    uint32_t    width;                  ///< strip width
    uint32_t    height;                 ///< strip height
    uint32_t    out_strip_height;       ///< ouput strip height
    uint8_t     bits_per_pixel;         ///< strip bpp
    uint32_t    pip_mode;               ///< pip mode
    struct BigBuffer_s  *big_buffer;    ///< strip bigbuffer
    bool        is_last_strip;          ///< last strip flag
};

/**
 * @brief output strip info
 */
typedef struct strip_out_s
{
    void*       data;           ///< strip data
    uint32_t    datalen;        ///< data length
    int         color;          ///< color
    int         planecount;     ///< plane count number
    int         width;          ///< strip width
    int         height;         ///< strip height
    int         bpp;            ///< strip bpp
    int         last_buffer;    ///< last buffer strip flag
    uint32_t    page_type;      ///< page type

}BAND_PIP_OUT;

/**
 * @brief pip adptor info
 */
struct pip_adaptors_s
{
   struct pip_page_adaptor  *page_adaptor;  ///< page adaptor
   struct pip_strip_adaptor *strip_adaptor; ///< strip adaptor
};

/**
 * @brief pip process strip done callback
 * @param[in] contex  strip adaptor context
 * @param[in] strip_out output strip
 * @return 0 on success,negative number on error
 * <AUTHOR>
 * @date 2021-10-29
 */
typedef int32_t (*PIP_DONE_CALLBACK)( void *contex, struct strip_out_s strip_out );

/**
 * @brief consturct pip_adaptors include page and strip adapator
 * @param[in] page_blob  process page info
 * @param[in] mqd massege queue
 * @param[in] mem_handle  memory handle
 * @param[in] page_type page type front or back
 * @param[in] brightnesss  level 1~5
 * @param[in] done_callback strip process done callback
 * @param[in] context  strip adaptor context
 * @return pip_adaptors_s pointer
 * <AUTHOR>
 * @date 2021-10-29
 */
struct pip_adaptors_s *open_scan_pip( struct page_blob_s *page_blob,
                                          mqd_t mqd,
                                          void* mem_handle,
                                          uint32_t page_type,
                                          uint32_t brightnesss,
                                          PIP_DONE_CALLBACK done_callback,
                                          void *contex );

/**
 * @brief detory pip_adaptors
 * @param[in] pip_adaptors  pip_adaptors pointer
 * @return void
 * <AUTHOR>
 * @date 2021-10-29
 */
void close_scan_pip( struct pip_adaptors_s *pip_adaptors );

/**
 * @brief use this to porcess strip
 * @param[in] pip_adaptors  pip_adaptors pointer
 * @param[in] big_buffer  strip bigbuffer
 * @param[in] num_bytes  input strip bytes
 * @param[in] num_rows  input row
 * @param[in] out_rows  ouput row
 * @param[in] last_buffer  last buffer flag
 * @param[in] pip_mode  pip mode
 * @return void
 * <AUTHOR>
 * @date 2021-10-29
 */
int pip_process_scan_strip( struct pip_adaptors_s *pip_adaptors,
                                    struct BigBuffer_s *big_buffer,
                                    uint32_t num_bytes,
                                    uint32_t num_rows,
                                    uint32_t out_rows,
                                    bool last_buffer,
                                    uint32_t pip_mode );

#endif

/**
 *@}
 */