/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file cini.h
 * @addtogroup system_manager
 * @{
 * @brief used to parse system resource configuration items 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef __CINI_H
#define __CINI_H

#define KEY_LEN 128
#define VALUE_LEN 128


struct parameter
{
	char *key;
	char *value;
    struct parameter *next;
};

struct section 
{
    char name[KEY_LEN];
	int param_num;
    struct parameter *subpara;
    struct section *next;
};

struct cini
{
    int section_num;
    struct section *head;
};

enum SECTION_POS
{
    FRONT, 
    CENTRE,
    BACK  
};

/**
 * @brief cini structure initialization
 *
 * @param root structure pointer
 */
void cini_init(struct cini *root);

/**
 * @brief parsing configuration files
 *
 * @param file configuration file path 
 * @param root structure pointer
 *
 * @return 0 on success , -1 on error 
 */
int parser_cini(const char *file ,  struct cini *root);

/**
 * @brief write the contents of the cini structure to the specified file 
 *
 * @param file configuration file path 
 * @param root structure pointer
 *
 * @return 0 on success , -1 on error 
 */
int write_cini(const char *file ,  struct cini *root);

/**
 * @brief destroy the ini structure pointer
 *
 * @param root structure pointer
 */
void cini_destroy(struct cini *root);

/**
 * @brief find the section structure corresponding to the node name
 *
 * @param root structure pointer
 * @param section the node name
 *
 * @return section structure pointer on success , NULL on error
 */
struct section* cini_find_section(const struct cini *root , const char *section);

/**
 * @brief get the section structure corresponding to the first node in the cini structure
 *
 * @param root structure pointer
 *
 * @return section structure pointer on success , NULL on error
 */
struct section* cini_first_section(const struct cini *root); 

/**
 * @brief get the section structure corresponding to the last node in the cini structure
 *
 * @param root structure pointer
 *
 * @return section structure pointer on success , NULL on error
 */
struct section* cini_last_section(struct cini *root); 

/**
 * @brief calculate how many nodes exist in the cini structure 
 *
 * @param root structure pointer
 *
 * @return number of nodes
 */
int cini_calculate_section(struct cini *root);

/**
 * @brief add a node to the cini structure
 *
 * @param root structure pointer
 * @param section the node name
 *
 * @return section structure pointer on success , NULL on error
 */
struct section* cini_add_section(struct cini *root , const char *section);

/**
 * @brief delete the content corresponding to the specified node int he cini structure
 *
 * @param root structure pointer
 * @param section the node name
 *
 * @return 0 on success , -1 on error 
 */
int cini_del_section(struct cini *root , const char *section);

/**
 * @brief find the value corresponding to the specified key in the node content 
 *
 * @param sect section structure pointer
 * @param key key name
 *
 * @return a pointer to the beginning of the valid buffer on success , NULL on error 
 */
char* cini_find_parameter(const struct section *sect , const char *key);

/**
 * @brief add a key-value pair to the node content
 *
 * @param sect section structure pointer
 * @param key key name
 * @param value the content corresponding to the key 
 *
 * @return 0 on success , -1 on error 
 */
int cini_add_parameter(struct section *sect , const char *key , const char *value);

/**
 * @brief delete the key-value pair specified in the node content
 *
 * @param sect section structure pointer
 * @param key key name
 *
 * @return 0 on success , -1 on error 
 */
int cini_del_parameter(struct section *sect , const char *key);
#endif //__CINI_H

/**
 * @}
 */
