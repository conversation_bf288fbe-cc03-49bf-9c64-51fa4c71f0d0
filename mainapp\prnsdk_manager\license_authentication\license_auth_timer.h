/**
 * @file license_auth_timer.h
 * @brief Head<PERSON> file for license authentication timer management.
 * @details This file contains macros, structures, and function declarations related to timer operations in the license authentication module.
 * @addtogroup license_authentication
 * @{
 * @modifier yangzikun
 * @date 2024-12-11
 */

#ifndef _LICENSE_AUTH_TIMER_H_
#define _LICENSE_AUTH_TIMER_H_

#include "license_auth_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @def LICENSE_AUTH_PERIOD_TIME
 * @brief The default period time for timers (in seconds).
 */
#define LICENSE_AUTH_PERIOD_TIME 1800

/**
 * @def LICENSE_AUTH_REBOOT_ON_OFF
 * @brief Enables or disables the reboot functionality. Set to 0 to disable, 1 to enable.
 */
#define LICENSE_AUTH_REBOOT_ON_OFF 0

/**
 * @def LICENSE_AUTH_REBOOT_TIME
 * @brief The default reboot time (in seconds). Set to 24 hours.
 */
#define LICENSE_AUTH_REBOOT_TIME (24 * 60 * 60)

/**
 * @struct LICENSE_AUTH_PT_CB_S
 * @brief Structure for defining timer-related callback parameters.
 * @details This structure contains two time values and a function pointer for the timer callback routine.
 *
 * @var LICENSE_AUTH_PT_CB_S::time0
 * The initial time for the timer (in seconds).
 *
 * @var LICENSE_AUTH_PT_CB_S::time1
 * The periodic interval for the timer (in seconds).
 *
 * @var LICENSE_AUTH_PT_CB_S::start_routine
 * Pointer to the callback function to execute when the timer expires.
 */
typedef struct
{
    long long time0; /**< Initial time in seconds. */
    long long time1; /**< Interval time in seconds. */
    void *(*start_routine)(void *); /**< Callback function to execute. */
} LICENSE_AUTH_PT_CB_S;

/**
 * @brief Checks the expiration time from a file.
 * @details Reads the expiration time from the specified file and converts it to a `long long` value.
 * @param[in] infile Path to the input file containing the expiration time.
 * @return The expiration time as a `long long` value. Returns 0 for no expiration, or -1 on error.
 */
long long license_auth_check_time(const char *infile);

/**
 * @brief Starts the cut-time pthread.
 * @details Creates and starts a new thread for managing time-related tasks.
 * @param[in] str Pointer to the string data passed to the thread.
 * @return Pointer to the result of the thread routine.
 */
void *license_auth_start_pthread_cuttime(void *str);

/**
 * @brief Creates a pthread for the timer.
 * @details Sets up a timer using the parameters provided in the `LICENSE_AUTH_PT_CB_S` structure.
 * @param[in] value Pointer to a `LICENSE_AUTH_PT_CB_S` structure containing timer configuration.
 * @return 0 on success, or -1 on failure.
 */
int32_t license_auth_create_timer_thread(LICENSE_AUTH_PT_CB_S *value);

#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_TIMER_H_ */

/**
 * @}
 */
