/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file sql.h
 * @addtogroup system_manager
 * @{
 * @brief Database Operation Interface
 * <AUTHOR> 
 * @version 1.0
 * @date 2024-05-28
 */

#ifndef _SQL_H
#define _SQL_H

void* sql_prolog(const char *name);

int sql_epilog(void *handle);

int sql_integrity_check(void *handle);

int sql_try_repair(void **handle , const char *file);

int sql_create_table(const void *handle , const char *name);

int sql_find_table(const void *handle , const char *name);

int sql_get_tables(const void *handle , 
        void *context , 
        void (*callabck)(void* context , const unsigned char* table));

int sql_set_table_item(const void *handle , 
        const char *table,
        const unsigned int id , 
        const void *data , 
        const unsigned int size);

int sql_get_table_item(const void *handle , 
        const char *table ,
        const unsigned int id , 
        void *data , 
        unsigned int size);

int sql_del_table_item(const void *handle , const char *table , const unsigned int id);

int sql_del_table(const void *handle , const char *table);

unsigned int sql_get_table_items(const void *handle , 
        const char *table , 
        const void *callback_context ,
        int(*callback)(const void *context , const void *data , unsigned int size));

#endif //_SQL_H

/**
 * @}
 */
