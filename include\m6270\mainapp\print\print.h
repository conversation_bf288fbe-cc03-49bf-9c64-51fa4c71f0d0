#ifndef __PRINT_H
#define __PRINT_H

#include "public_data_proc.h"
//EVENT:EVT_TYPE_PRINT_MACHINE_INFO-->EVT_TYPE_PRINT_ENGINE_INFO
typedef struct
{
    char           eng_serial_num[32];       ///< record engine serial number
    char           eng_firmware_version[32]; ///< record engine firware version number
    char           eng_speed_type[32];       ///< engine speed type string, include "65PPM","55PPM","45PPM"
    char           eng_color_type[32];       ///< engine color type string, include "MONO","COLOR","RED_BLACK"
}
PRINT_CONFIG_ENGINE_INFO_S,*PRINT_CONFIG_ENGINE_INFO_P;

typedef enum
{
    INFO_UNINSTALL      = 0,
    INFO_INSTALL,
}PRINT_INSTALL_INFO_E;

typedef enum
{
    INFO_FNS_UNINSTALL  =0,
    INFO_FNS533,
    INFO_FNS540,
    INFO_FNS534,
    INFO_FNS536,
    INFO_FNS540SD,
}PRINT_FNS_TYPE_E;

//EVENT:EVT_TYPE_PRINT_CONFIG_INFO-->EVT_TYPE_PRINT_INSTALL_INFO
typedef struct
{//enum
    PRINT_FNS_TYPE_E           fns               ;          //装订器安装信息
    PRINT_INSTALL_INFO_E       zu_unit           ;          //Z折单元
    PRINT_INSTALL_INFO_E       ru_unit           ;          //中继搬送单元
    PRINT_INSTALL_INFO_E       pk_unit           ;          //打孔单元
    PRINT_INSTALL_INFO_E       sd_unit           ;          //鞍式折叠单元（双折）
    PRINT_INSTALL_INFO_E       option_tray1      ;          //选择托盘1
    PRINT_INSTALL_INFO_E       option_tray2      ;          //选择托盘2


    PRINT_INSTALL_INFO_E       tray_1            ;          //纸盒1
    PRINT_INSTALL_INFO_E       tray_2            ;          //纸盒2
    PRINT_INSTALL_INFO_E       tray_3            ;          //纸盒3
    PRINT_INSTALL_INFO_E       tray_4            ;          //纸盒4
    PRINT_INSTALL_INFO_E       tray_lct_in       ;          //内置大容量纸盒
    PRINT_INSTALL_INFO_E       tray_lct_out      ;          //外置大容量纸盒
    PRINT_INSTALL_INFO_E       tray_multi        ;          //手送纸盒

    PRINT_INSTALL_INFO_E       toner_c           ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       toner_m           ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       toner_y           ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       toner_k           ;          ///< 0:uninstall 1:install

    PRINT_INSTALL_INFO_E       drum_c            ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       drum_m            ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       drum_y            ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       drum_k            ;          ///< 0:uninstall 1:install

    PRINT_INSTALL_INFO_E       dv_c              ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       dv_m              ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       dv_y              ;          ///< 0:uninstall 1:install
    PRINT_INSTALL_INFO_E       dv_k              ;          ///< 0:uninstall 1:install

    PRINT_INSTALL_INFO_E       waste_toner       ;          ///< 0:uninstall 1:install
}
PRINT_CONFIG_INSTALL_INFO_S,*PRINT_CONFIG_INSTALL_INFO_P; //???

//EVENT:EVT_TYPE_PRINT_CONSUMPTION_PARAM
typedef struct
{
    char           dr_c_id[32]                   ;                    ///< record the drum c components's id
    char           dr_m_id[32]                   ;                    ///< record the drum m components's id
    char           dr_y_id[32]                   ;                    ///< record the drum y components's id
    char           dr_k_id[32]                   ;                    ///< record the drum k components's id

    char           tb_c_id[32]                   ;                    ///< record the toner c components's id
    char           tb_m_id[32]                   ;                    ///< record the toner m components's id
    char           tb_y_id[32]                   ;                    ///< record the toner y components's id
    char           tb_k_id[32]                   ;                    ///< record the toner k components's id

    char           dr_c_serial[32]               ;                    ///< record the drum c components's serial
    char           dr_m_serial[32]               ;                    ///< record the drum m components's serial
    char           dr_y_serial[32]               ;                    ///< record the drum y components's serial
    char           dr_k_serial[32]               ;                    ///< record the drum k components's serial

    char           tb_c_serial[32]               ;                    ///< record the toner c components's serial
    char           tb_m_serial[32]               ;                    ///< record the toner m components's serial
    char           tb_y_serial[32]               ;                    ///< record the toner y components's serial
    char           tb_k_serial[32]               ;                    ///< record the toner k components's serial

    char           dr_c_model[32]                 ;                    ///< record the drum c model
    char           dr_m_model[32]                 ;                    ///< record the drum m model
    char           dr_y_model[32]                 ;                    ///< record the drum y model
    char           dr_k_model[32]                 ;                    ///< record the drum k model

    char           tb_c_model[32]                 ;                    ///< record the toner c model
    char           tb_m_model[32]                 ;                    ///< record the toner m model
    char           tb_y_model[32]                 ;                    ///< record the toner y model
    char           tb_k_model[32]                 ;                    ///< record the toner k model

    uint32_t   tb_c_max_print                 ;                    ///< toner c max print page number
    uint32_t   tb_m_max_print                 ;                    ///< toner m max print page number
    uint32_t   tb_y_max_print                 ;                    ///< toner y max print page number
    uint32_t   tb_k_max_print                 ;                    ///< toner k max print page number

    uint32_t   dr_c_max_print                 ;                    ///< drum c max print page number
    uint32_t   dr_m_max_print                 ;                    ///< drum m max print page number
    uint32_t   dr_y_max_print                 ;                    ///< drum y max print page number
    uint32_t   dr_k_max_print                 ;                    ///< drum k max print page number

    uint32_t   dv_c_max_print                 ;                    ///< dv c max print page number
    uint32_t   dv_m_max_print                 ;                    ///< dv m max print page number
    uint32_t   dv_y_max_print                 ;                    ///< dv y max print page number
    uint32_t   dv_k_max_print                 ;                    ///< dv k max print page number

    uint32_t    toner_filter_capacity       ;                      ///< toner filter capacity value
    uint32_t    transfer_roller_capacity    ;                   ///< transfer roller capacity value
    uint32_t    transfer_belt_capacity      ;                     ///< transfer belt capacity value
    uint32_t    fuser_capacity              ;                             ///< fuser capacity value
    uint32_t    waste_toner_capacity        ;                       ///< waste toner capacity value
}
PRINT_CONFIG_CONSUMPTION_PARAM_S,*PRINT_CONFIG_CONSUMPTION_PARAM_P;

typedef enum
{
    TONER_C     =1,
    TONER_M,
    TONER_Y,
    TONER_K,
    DRUM_C,
    DRUM_M,
    DRUM_Y,
    DRUM_K,
}
PRINT_CONSUMPTION_E;

//EVENT:EVT_TYPE_PRINT_CONSUMPTION_INFO
typedef struct
{
    uint8_t tb_c_remain             ;    ///< toner c remain percent
    uint8_t tb_m_remain             ;    ///< toner m remain percent
    uint8_t tb_y_remain             ;    ///< toner y remain percent
    uint8_t tb_k_remain             ;    ///< toner k remain percent

    uint8_t dv_c_remain             ;    ///< dv c remain percent
    uint8_t dv_m_remain             ;    ///< dv m remain percent
    uint8_t dv_y_remain             ;    ///< dv y remain percent
    uint8_t dv_k_remain             ;    ///< dv k remain percent

    uint8_t dr_c_remain             ;    ///< drum c remain percent
    uint8_t dr_m_remain             ;    ///< drum m remain percent
    uint8_t dr_y_remain             ;    ///< drum y remain percent
    uint8_t dr_k_remain             ;    ///< drum k remain percent

    uint8_t toner_fileter_remian    ;    ///< toner fileterremain percent
    uint8_t transfer_roller_remain  ;    ///< transfer roller remain percent
    uint8_t transfer_belt_remain    ;    ///< transfer belt remain percent
    uint8_t fuser_remain            ;    ///< fuser remain percent
    uint8_t waste_toner_remain      ;    ///< waste toner remain percent
    uint8_t punchwastebox_remain_info;   ///< punch waste box
    uint8_t finisher_punch_waste_box;    ///< finisher install optional punch  waste box 0:uninstall 1:install
    uint8_t finisher_staple_waste_box;   ///< finisher install optional staple waste box 0:uninstall/full 1:install
}
PRINT_CONFIG_CONSUMPTION_INFO_S,*PRINT_CONFIG_CONSUMPTION_INFO_P;

typedef enum
{
    DRUM_STATUS_NORMAL = 0,
    DRUM_STATUS_UNINSTALL,
    DRUM_STATUS_DEAD,
    DRUM_STATUS_NEARDEAD,
    DRUM_STATUS_MISMATCH,
}
PRINT_DRUM_STATUS_E;

typedef enum SETTING_ID_TONER_STATUS_E ///< 碳粉盒状态
{
    V_ID_TONER_NORMAL = 0,
    V_ID_TONER_MISSING,
    V_ID_TONER_LOW,
    V_ID_TONER_EMPTY,
    V_ID_TONER_EMPTY_STOP,
    V_ID_TONER_DISMATCH,
    V_ID_TONER_INSUFFICIENT,
} SETTING_ID_TONER_STATUS_E;

typedef enum SETTING_ID_WASTE_TONER_STATUS_E ///< 废粉盒状态
{
    V_ID_WASTE_TONER_NORMAL = 0,
    V_ID_WASTE_TONER_FULL,
    V_ID_WASTE_TONER_NEAR_FULL,
    V_ID_WASTE_TONER_MISSING,
} SETTING_ID_WASTE_TONER_STATUS_E;

typedef enum SETTING_ID_DRUM_STATUS_E ///< 硒鼓状态
{
    V_ID_DRUM_NORMAL = 0,
    V_ID_DRUM_MISSING,
    V_ID_DRUM_LOW,
    V_ID_DRUM_EMPTY,
    V_ID_DRUM_DISMATCH,
    V_ID_DRUM_INSUFFICIENT,
} SETTING_ID_DRUM_STATUS_E;


//EVENT:EVT_TYPE_PRINT_CONSUMPTION_STATUS 待追加
typedef struct
{
    SETTING_ID_TONER_STATUS_E tb_c_status          ;
    SETTING_ID_TONER_STATUS_E tb_m_status          ;
    SETTING_ID_TONER_STATUS_E tb_y_status          ;
    SETTING_ID_TONER_STATUS_E tb_k_status          ;

    SETTING_ID_DRUM_STATUS_E dr_c_status          ;
    SETTING_ID_DRUM_STATUS_E dr_m_status          ;
    SETTING_ID_DRUM_STATUS_E dr_y_status          ;
    SETTING_ID_DRUM_STATUS_E dr_k_status          ;

    SETTING_ID_WASTE_TONER_STATUS_E waste_toner_status   ;
}
PRINT_CONFIG_CONSUMPTION_STATUS_S,*PRINT_CONFIG_CONSUMPTION_STATUS_P;

//EVENT:EVENT：EVT_TYPE_PRINT_TRAY_INFO
typedef struct
{
    int8_t tray_1_paper_remain                ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open
    int8_t tray_2_paper_remain                ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open
    int8_t tray_3_paper_remain                ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open
    int8_t tray_4_paper_remain                ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open
    int8_t tray_lct_in_paper_remain           ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open
    int8_t tray_lct_out_paper_remain          ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open
    int8_t tray_multi_paper_remain            ;    ///< 0-empty, 1-exist, -1-indeterminate, -2-open

    PAPER_SIZE_E tray_1_paper_size          ;    ///< public define size
    PAPER_SIZE_E tray_2_paper_size          ;    ///< public define size
    PAPER_SIZE_E tray_3_paper_size          ;    ///< public define size
    PAPER_SIZE_E tray_4_paper_size          ;    ///< public define size
    PAPER_SIZE_E tray_lct_in_paper_size     ;    ///< public define size
    PAPER_SIZE_E tray_lct_out_paper_size    ;    ///< public define size
    PAPER_SIZE_E tray_multi_paper_size      ;    ///< public define size

    PAPER_TYPE_E tray_1_paper_type          ;    ///< public define paper type
    PAPER_TYPE_E tray_2_paper_type          ;    ///< public define paper type
    PAPER_TYPE_E tray_3_paper_type          ;    ///< public define paper type
    PAPER_TYPE_E tray_4_paper_type          ;    ///< public define paper type
    PAPER_TYPE_E tray_lct_in_paper_type     ;    ///< public define paper type
    PAPER_TYPE_E tray_lct_out_paper_type    ;    ///< public define paper type
    PAPER_TYPE_E tray_multi_paper_type      ;    ///< public define paper type
}
PRINT_CONFIG_TRAY_INFO_S,*PRINT_CONFIG_TRAY_INFO_P;

typedef struct
{
    uint32_t    length;
    uint32_t    width;
}
TRAY_CUSTOM_SIZE_S, *TRAY_CUSTOM_SIZE_P;

typedef struct
{
    TRAY_CUSTOM_SIZE_S  custom_1;
    TRAY_CUSTOM_SIZE_S  custom_2;
    TRAY_CUSTOM_SIZE_S  custom_3;
}
PRINT_CONFIG_CUSTOM_SIZE_S, *PRINT_CONFIG_CUSTOM_SIZE_P;

//EVENT:EVT_TYPE_PRINT_PROCESS_JOB_INFO
typedef struct
{
    char            job_owner[32]          ;      ///< job owner job owner name string
    JOB_VIA_E       job_via                  ;      ///< job comes from port usb,net,wifi ...
    JOB_CLASS_E     job_class                ;      ///< copy,print,fax job
    PAPER_SIZE_E    job_paper_size           ;      ///< public define paper size enum
    PRINT_MODE_E    job_print_mode           ;      ///< public define print mode
    //unsigned char   job_print_color          ;      ///< public define print color:RB/COLOR/MONO
    unsigned int    job_id                   ;      ///< job id
    unsigned int    job_copies               ;      ///< job copies number
    //unsigned int    job_percopy_pages        ;      ///< job pages in each copies
    //unsigned int    job_finish_copies        ;      ///< job finish copies not support
    //unsigned int    job_finish_percopy_pages ;      ///< not support
}
PRINT_PROCESS_JOB_INFO_S,*PRINT_PROCESS_JOB_INFO_P;

//EVENT:EVT_TYPE_PRINT_PROCESS_PAGE_INFO
typedef struct
{
    uint32_t    page_printed_num            ;      ///< page printed num
    uint32_t    page_receive_num            ;      ///< current job receive number
}
PRINT_PROCESS_PAGE_INFO_S,*PRINT_PROCESS_PAGE_INFO_P;


typedef struct
{
    JOB_VIA_E       job_via                  ;      ///< job comes from port usb,net,wifi ...
    JOB_CLASS_E     job_class                ;      ///< copy,print,fax job
    uint32_t    job_page_number          ;      ///< job page number
}
PRINT_EVENTLOG_JOB_INFO_S,*PRINT_EVENTLOG_JOB_INFO_P;


typedef struct
{
    uint32_t            a6_counter              ;      ///< pages_size a6 counter
    uint32_t            jis_b6_counter          ;      ///< pages_size jis_b6 counter
    uint32_t            invoice_counter         ;      ///< pages_size invoice counter
    uint32_t            a5_counter              ;      ///< pages_size a5 counter
    uint32_t            jis_b5_counter          ;      ///< pages_size jis_b5 counter
    uint32_t            executive_counter       ;      ///< pages_size executive counter
    uint32_t            costom_big_16k_counter  ;      ///< pages_size costom_big_16k counter
    uint32_t            a5l_counter             ;      ///< pages_size a5l counter
    uint32_t            a4_counter              ;      ///< pages_size a4 counter
    uint32_t            invoice_l_counter       ;      ///< pages_size invoice_l counter
    uint32_t            letter_counter          ;      ///< pages_size letter counter
    uint32_t            foolscap3_counter       ;      ///< pages_size foolscap3 counter
    uint32_t            legal_counter           ;      ///< pages_size legal counter
    uint32_t            jis_b5l_counter         ;      ///< pages_size jis_b5l counter
    uint32_t            jis_b4_counter          ;      ///< pages_size jis_b4 counter
    uint32_t            executive_l_counter     ;      ///< pages_size executive_l counter
    uint32_t            costom_big_16kl_counter ;      ///< pages_size costom_big_16kl counter
    uint32_t            _8k_counter             ;      ///< pages_size 8k counter
    uint32_t            letter_l_counter        ;      ///< pages_size letter_l counter
    uint32_t            ledger_counter          ;      ///< pages_size ledger counter
    uint32_t            a4l_counter             ;      ///< pages_size a4l counter
    uint32_t            a3_counter              ;      ///< pages_size a3 counter
    uint32_t            a3_wide1_counter        ;      ///< pages_size a3_wide1 counter
    uint32_t            sra3_counter            ;      ///< pages_size sra3 counter
    uint32_t            user_define1_counter    ;      ///< pages_size user_define1 counter
    uint32_t            user_define2_counter    ;      ///< pages_size user_define2 counter
    uint32_t            user_define3_counter    ;      ///< pages_size user_define3 counter
}PAPER_SIZE_STATISTIC_INFO_S,*PAPER_SIZE_STATISTIC_INFO_P;

typedef enum
{
    ATTR_PAPER_TYPE = 0,
    ATTR_PAPER_SIZE,
    ATTR_INPUT_TRAY,
    ATTR_DUPLEX,
    ATTR_COLOR,
    ATTR_COLLATE,
    ATTR_COPIES,
    ATTR_BARRIER,
    ATTR_BLANK,
    ATTR_IMAGE_H,
    ATTR_PUNCH,
    ATTR_FLOD,
    ATTR_RECEIVE,
    ATTR_DEVIATE,
    ATTR_BIND,
    ATTR_FOLD_COMPONENT_HAVE_PAPER,
}PRINT_PARAM_ATTRIBUTE_E;

typedef enum
{
    COMPONENT_TRAY = 0,
    COMPONENT_TONER,
    COMPONENT_DRUM,
    COMPONENT_DV,
    COMPONENT_RECEIVE,
    COMPONENT_DOOR,
    COMPONENT_PUNCH,
    COMPONENT_BIND,
    COMPONENT_FOLD,
    COMPONENT_SHIFT,
    COMPONENT_Z_FOLD,
    COMPONENT_FINISHER,
}PRINT_PARAM_COMPONENT_E;

typedef enum
{
    INTERNAL_MODULE_INIT = 0,
    INTERNAL_MODULE_REGISTER,
    INTERNAL_MSG_ROUTER,
    INTERNAL_MEM_MALLOC,
    INTERNAL_DATA_NULL,
    INTERNAL_DATA_TIME,
    INTERNAL_BAND_NOT_ENOUGH,
}
PRINT_PARAM_INTERNAL_ERROR_E;

typedef enum
{
    FILE_OPEN = 0,
    FILE_CLOSE,
    FILE_WRITE,
    FILE_READ,
    FILE_FORMAT,
    FILE_SAVING,
}
PRINT_FILE_PARAMETER_E;

typedef enum
{
    SDK_AUTHORITY_LOCAL = 0,
    SDK_AUTHORITY_GLOBAL,
    SDK_AUTHORITY_USB,
    SDK_AUTHORITY_COLOR,
    SDK_AUTHORITY_UP_LIMITED,
}PRINT_PARAM_SDK_ATTRIBUTE_E;


typedef enum
{
    PROCESS_TRAY_1 = 0,
    PROCESS_TRAY_2,
}
PRINT_PARAM_PROCESS_TRAY_E;

typedef enum
{
    STITCH_STAPLE_EMPTY = 0,
    STAPLE_EMPTY_FRONT,
    STAPLE_EMPTY_REAR,
}
PRINT_PARAM_STAPLE_E;

typedef enum
{
    DOC_ENCRYPTED = 0,
    DOC_RESET_INPUT_STREAM,
    DOC_NOT_ZIP_FILE,
    DOC_DECRYPT_ERROR,
    DOC_PROTECTED,
    DOC_MISS_MAGIC_NUM,
    DOC_UNSUPPORT_WORD6,
    DOC_UNSUPPORT_UNKNOWN,
    DOC_UNSUPPORT_FILE_FORMAT_1,
    DOC_UNSUPPORT_FILE_FORMAT_2,
    DOC_UNSUPPORT_FILE_FORMAT_3,
    PDF_ENCRYPT,
    PDF_FONT_MISSING,
    PDF_FONT_INVALID,
    PDF_FILE_LARGE,
}
PRINT_PARAM_DOC_PARSE_E;

typedef enum
{
    FILE_ENCRYPT = 0,           ///< File is encrypted
    FILE_FONT_MISSING,          ///< File font missing
    FILE_LARGE,                 ///< File is large
    FILE_UNSUPPORT,             ///< File is unsupport
}
PRINT_PARAM_FILE_PARSE_E;

typedef enum
{
    SECURITY_CHECK_ERROR = 0,           ///< security feature verification error
    OTHER_PRE_PARSER_ERROR,             ///< other pre parser error
}
PRINT_PARAM_PRE_PARSE_E;

typedef struct
{
    uint32_t            ordinary_counter        ;      ///< paper_type ordinary counter
    uint32_t            ordinary_p_counter      ;      ///< paper_type ordinary_p counter
    uint32_t            thick1_counter          ;      ///< paper_type thick1 counter
    uint32_t            thick1_p_counter        ;      ///< paper_type thick1_p counter
    uint32_t            thick2_counter          ;      ///< paper_type thick2 counter
    uint32_t            thick3_counter          ;      ///< paper_type thick3 counter
    uint32_t            film_counter            ;      ///< paper_type film counter
    uint32_t            pose_card_counter       ;      ///< paper_type pose_card counter
    uint32_t            envelop_counter         ;      ///< paper_type envelop counter
    uint32_t            label_counter           ;      ///< paper_type label counter
}PAPER_TYPE_STATISTIC_INFO_S;


typedef struct
{
    uint32_t            duplex_counter         ;      ///< sheets print_mode duplex counter
    uint32_t            single_counter         ;      ///< sheets print_mode single counter
}SHEET_MODE_STATISTIC_INFO_S;

typedef struct
{
    uint32_t            c_total_counter         ;      ///< Each toner independently records the total number of pages printed by the machine
    uint32_t            m_total_counter         ;      ///< Each toner independently records the total number of pages printed by the machine
    uint32_t            y_total_counter         ;      ///< Each toner independently records the total number of pages printed by the machine
    uint32_t            k_total_counter         ;      ///< Each toner independently records the total number of pages printed by the machine
}TONER_PAGE_COUNT_STATISTIC_INFO_S;


typedef struct tag_printed_page_sheet_counter
{
    uint32_t                                job_class                           ;      ///< printed pages belong job type, 1 means print, 2 means copy

    //page statistics
    uint32_t                                page_mono_counter                   ;
    uint32_t                                page_color_counter                  ;
    uint32_t                                page_total_counter                  ;      ///< printed pages total counter
    
    PAPER_SIZE_STATISTIC_INFO_S             page_paper_size_mono_counter        ;
    PAPER_SIZE_STATISTIC_INFO_S             page_paper_size_color_counter       ;
    //PAPER_TYPE_STATISTIC_INFO_S           page_paper_type_counter             ;

    //sheet statistics
    uint32_t                                sheet_total_counter                 ;      ///< printed pages total counter
    PAPER_SIZE_STATISTIC_INFO_S             sheet_paper_size_counter            ;
    SHEET_MODE_STATISTIC_INFO_S             sheet_print_mode_counter            ;

    //toner coverage page counter statistics
    TONER_PAGE_COUNT_STATISTIC_INFO_S       toner_page_total_counter            ;
}PRINTED_PAGE_SHEET_COUNTER_S;


typedef struct tag_printed_jobs_counter
{
    uint32_t            jobs_total_counter                 ;      ///< printed jobs total counter

    //job via statistics                                   ;
    uint32_t            jobs_via_usb_counter               ;      ///< jobs via usb counter
    uint32_t            jobs_via_ethernet_counter          ;      ///< jobs via ethernet counter
    uint32_t            jobs_via_wifi_counter              ;      ///< jobs via wifi counter
    uint32_t            jobs_via_file_counter              ;      ///< jobs via file counter
    uint32_t            jobs_via_internal_counter          ;      ///< jobs via internal counter
    uint32_t            jobs_via_copy_counter              ;      ///< jobs via copy counter
    uint32_t            jobs_via_fax_counter               ;      ///< jobs via fax counter
    uint32_t            jobs_via_debug_counter             ;      ///< jobs via debug counter
    uint32_t            jobs_via_image_counter             ;      ///< jobs via image counter
    uint32_t            jobs_via_unknown_counter           ;      ///< jobs via unknown counter
}PRINTED_JOBS_COUNTER_S;

//EVENT：EVT_TYPE_PRINT_STATISTIC_INFO
typedef struct
{
    //JOB_CLASS_E                             job_class                           ;      ///< printed pages belong job type, 1 means print, 2 means copy
    //page statistics
    unsigned int                            page_mono_counter                   ;
    unsigned int                            page_color_counter                  ;
    unsigned int                            page_total_counter                  ;      ///< printed pages total counter
    PAPER_SIZE_STATISTIC_INFO_S             page_paper_size_mono_counter        ;
    PAPER_SIZE_STATISTIC_INFO_S             page_paper_size_color_counter       ;

    //sheet statistics
    unsigned int                            sheet_total_counter                 ;      ///< printed pages total counter
    PAPER_SIZE_STATISTIC_INFO_S             sheet_paper_size_counter            ;
    SHEET_MODE_STATISTIC_INFO_S             sheet_print_mode_counter            ;
}
PRINT_CONFIG_STATISTIC_INFO_S,*PRINT_CONFIG_STATISTIC_INFO_P;


typedef struct
{
    uint16_t word[64];                                             ///< word
}PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S;

/**
 * @brief engine calibration gradation each data
 *
 */
typedef struct
{
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  C_screen_value[4];             ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  M_screen_value[4];             ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  Y_screen_value[4];             ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  K_screen_value[4];             ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  C_error_diffusion_value[4];    ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  M_error_diffusion_value[4];    ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  Y_error_diffusion_value[4];    ///< word
    PRINT_CALI_GRADATION_STANDARDIZED_VALUE_S  K_error_diffusion_value[4];    ///< word
}PRINT_CALI_GRADATION_VALUE_S, *PRINT_CALI_GRADATION_VALUE_P;

typedef struct
{
    PRINT_CALI_GRADATION_VALUE_S cali_value_array[15];
}PRINT_CALI_ORG_VALUE_S, *PRINT_CALI_ORG_VALUE_P;

typedef struct tag_image_screen_curve

{
    uint8_t C_screen_curve[256];
    uint8_t M_screen_curve[256];
    uint8_t Y_screen_curve[256];
    uint8_t K_screen_curve[256];
}IMAGE_SCREEN_CURVE_S;

typedef struct tag_image_ed_curve
{
    uint8_t  C_error_diffusion_curve[256];
    uint8_t  M_error_diffusion_curve[256];
    uint8_t  Y_error_diffusion_curve[256];
    uint8_t  K_error_diffusion_curve[256];
}IMAGE_ED_CURVE_S;

void print_func();

#endif
