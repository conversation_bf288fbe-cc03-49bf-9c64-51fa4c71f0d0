/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_mutex.c
 * @addtogroup threads
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief Pantum semaphore API functions
 */
#include "pol/pol_threads.h"
#include "pol/pol_log.h"

#include "pol_threads_private.h"
#include "pol_inner.h"

#define get_sem(m)  (POL_SEM_S *)(((POL_SEM_S *)m >= &s_semaphore_pool[0] && (POL_SEM_S *)m <= &s_semaphore_pool[PI_NUM_SEMS - 1]) ? m : NULL)

typedef struct pol_sem
{
    void*   os_sem;		///< underlying os semaphore primitive
}
POL_SEM_S;

static POL_SEM_S    s_semaphore_pool[PI_NUM_SEMS];
static void*        s_semaphore_mutex = NULL;

PI_SEMAPHORE_T pi_sem_create_ent(uint32_t count, const char* caller)
{
    POL_SEM_S*  psem = INVALIDSEM;

    RETURN_VAL_SHOW_CALLER_IF(s_semaphore_mutex == NULL, pi_log_e, caller, INVALIDMTX);

    pol_mutex_lock(s_semaphore_mutex);
    for ( int32_t i = 0; i < PI_NUM_SEMS; ++i )
    {
        if ( s_semaphore_pool[i].os_sem == NULL )
        {
            RETURN_VAL_SHOW_CALLER_IF((s_semaphore_pool[i].os_sem = pol_sem_create(count)) == NULL, pi_log_e, caller, INVALIDMTX);

            psem = &s_semaphore_pool[i];
            break;
        }
    }
    pol_mutex_unlock(s_semaphore_mutex);

    if ( psem == NULL )
    {
        pi_log_e("[CALL:%s]semaphore pool full!!!\n", caller);
    }
    return (PI_SEMAPHORE_T)psem;
}

int32_t pi_sem_destroy_ent(PI_SEMAPHORE_T sem, const char* caller)
{
    POL_SEM_S*  psem = get_sem(sem);

    RETURN_VAL_SHOW_CALLER_IF(s_semaphore_mutex == NULL, pi_log_e, caller, -1);

    pol_mutex_lock(s_semaphore_mutex);
    if ( psem == NULL || psem->os_sem == NULL )
    {
        pi_log_e("[CALL:%s] no such semaphore(%p)!!!\n", caller, sem);
    }
    else
    {
        pol_sem_destroy(psem->os_sem);
        psem->os_sem = NULL;
    }
    pol_mutex_unlock(s_semaphore_mutex);

    return 0;
}

int32_t pi_sem_wait_ent(PI_SEMAPHORE_T sem, const char* caller)
{
    POL_SEM_S*  psem = get_sem(sem);
    int32_t     ret;

    RETURN_VAL_SHOW_CALLER_IF(psem == NULL || psem->os_sem == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_semaphore_mutex == NULL, pi_log_e, caller, -1);

    ret = pol_sem_wait(psem->os_sem);
    if ( ret < 0 )
    {
        pi_log_e("[CALL:%s] pol_sem_wait failed(%d): %d<%s>\n", caller, ret, errno, strerror(errno));
    }
    return ret;
}

int32_t pi_sem_timedwait_ent(PI_SEMAPHORE_T sem, int32_t secs, int32_t usecs, const char* caller)
{
    POL_SEM_S*  psem = get_sem(sem);
    int32_t     ret;

    RETURN_VAL_SHOW_CALLER_IF(psem == NULL || psem->os_sem == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_semaphore_mutex == NULL, pi_log_e, caller, -1);

    if ( secs > 0 || (secs == 0 && usecs >= 0) )
    {
        ret = pol_sem_timedwait(psem->os_sem, secs, usecs);
    }
    else
    {
        ret = pol_sem_wait(psem->os_sem);
    }

    if ( ret < 0 )
    {
        pi_log_e("[CALL:%s] pol_sem_timedwait(%d, %d) failed(%d): %d<%s>\n", caller, secs, usecs, ret, errno, strerror(errno));
    }
    return ret;
}

int32_t pi_sem_post_ent(PI_SEMAPHORE_T sem, const char* caller)
{
    POL_SEM_S*  psem = get_sem(sem);
    int32_t     ret;

    RETURN_VAL_SHOW_CALLER_IF(psem == NULL || psem->os_sem == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_semaphore_mutex == NULL, pi_log_e, caller, -1);

    ret = pol_sem_post(psem->os_sem);
    if ( ret < 0 )
    {
        pi_log_e("[CALL:%s] pol_sem_post failed(%d): %d<%s>\n", caller, ret, errno, strerror(errno));
    }
    return ret;
}

int32_t pi_sem_prolog(void)
{
    RETURN_VAL_IF(s_semaphore_mutex != NULL, pi_log_e, 0); /* 以互斥锁作是否初始化的标识，避免同一进程中多次调用重复初始化 */
    RETURN_VAL_IF((s_semaphore_mutex = pol_mutex_create()) == NULL, pi_log_e, -1);

    pol_mutex_lock(s_semaphore_mutex);
    memset(&s_semaphore_pool, 0, sizeof(s_semaphore_pool));
    pol_mutex_unlock(s_semaphore_mutex);

    return 0;
}

void pi_sem_epilog(void)
{
    RETURN_IF(s_semaphore_mutex == NULL, pi_log_e);

    pol_mutex_lock(s_semaphore_mutex);
    for ( int32_t i = 0; i < PI_NUM_SEMS; ++i )
    {
        if ( s_semaphore_pool[i].os_sem != NULL )
        {
            pol_sem_destroy(s_semaphore_pool[i].os_sem);
        }
        memset(&s_semaphore_pool, 0, sizeof(s_semaphore_pool));
    }
    pol_mutex_unlock(s_semaphore_mutex);

    pol_mutex_destroy(s_semaphore_mutex);
    s_semaphore_mutex = NULL;
}
/**
 *@}
 */
