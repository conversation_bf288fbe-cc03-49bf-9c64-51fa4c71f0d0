/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file config.h
 * @addtogroup basic
 * @{
 * @addtogroup basic
 * @autor 
 * @date 2024-06-11
 * @brief common config 
 */

#ifndef _CONFIG_H_
#define _CONFIG_H_

/* 全局引用头文件 */
#include <stdint.h>
#include <stdio.h>
#include "basic/log/log.h"

/* 字符串定义 */
#define PESF_WORKSPACE_PATH "/pesf_data"                          ///<PeSF工作空间路径
#define JS_APP_NAME "app.js"                                      ///<应用程序的js脚本名
#define JS_APP_NAME_JSON "app.json"                               ///<应用程序静态属性文件
#define JS_APP_PROJ_CONFIG_NAME "project.config.json"             ///<应用程序的js脚本名
#define PESF_JS_INNER_PATH "/pesf/js/inner/"                      ///<PeSF工作空间路径
#define EXPIRED_APP PESF_JS_INNER_PATH "expired.js"               ///<证书过期时启动的脚本
#define EXCEPTION_APP PESF_JS_INNER_PATH "exception.js"           ///<APP异常时，强制加载的脚本

/* 数字定义 */
#define JS_FILE_BUF_SIZE 1 * 1024 * 1024 ///<容纳JS脚本的最大缓存，1M
#define WORK_PATH_LENGTH 256             ///<存储工作路径最大缓存
#define BUFFER_MAX_LENGTH 1024           ///<内部各处消息最大缓存
#define MAX_APP_NUM 256                  ///<最大支持的app数；实际支持255个app，rtid：1~255。rtid 0用来广播
#define APP_NAME_LEN_MAX 64              ///<应用程序名最大缓存
#define END_WAIT_TIME 10                 ///<退出app时，最大等待时间s
#define MAX_QUEUE_NUM 10                 ///<最大内部消息队列深度

/* 控制宏定义 */
//#define TRANS_MONITOR                    ///<传输模式为终端时定义

#endif /* _CONFIG_H_ */

/**
 * @}
 */

