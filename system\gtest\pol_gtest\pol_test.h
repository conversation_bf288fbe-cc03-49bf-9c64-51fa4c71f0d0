#ifndef __POL_TEST_H__
#define __POL_TEST_H__ 

#include "pol/pol_types.h"

#define PASS         1
#define FAIL        -1
#define NOT_SUPPORT -2

#define MAX_FUNCNAME_LENGTH         64
#define MAX_INTERFACE_TYPE_LENGHT   16


typedef int32_t  (*test_func0)( void );


typedef struct{
    char        interface_name[MAX_FUNCNAME_LENGTH];
    test_func0  func;
}TEST_ITEM_S,*TEST_ITEM_P;


typedef struct{
    char            pool_type[MAX_INTERFACE_TYPE_LENGHT];
    TEST_ITEM_P     pool;
    uint32_t        pool_cnt;   
}TEST_POOL_S,*TEST_POOL_P;


#define  POOL_SIZE(p)  (sizeof(p)/sizeof(TEST_ITEM_S))

#endif
