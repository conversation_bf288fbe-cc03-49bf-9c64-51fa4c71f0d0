/**************************************************************
Copyright (c) 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:    prnsdk
file name:      prnsdk_status.h
author:         <PERSON>(<EMAIL>)
date:           2024-05-24
description:    system status map to prnsdk status
**************************************************************/
#ifndef _PRNSDK_H_
#define _PRNSDK_H_

#include <stdint.h>

// -1 for remote print but fail to authorize or timeout, 0 for valid remote priht, 1 for not remote print
int prnsdk_check_remote_print(const char* buf, int32_t buf_len);

#endif

