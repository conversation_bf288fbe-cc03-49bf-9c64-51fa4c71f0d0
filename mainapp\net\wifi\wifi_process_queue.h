/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi_process_queue.h
 * @addtogroup net
 * @{
 * @addtogroup wifi_process_queue
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WiFi process queue API
 */
#ifndef __WIFI_PROCESS_QUEUE__
#define __WIFI_PROCESS_QUEUE__

#define PUSH_TASK_TO_STA_PROCESS_QUEUE(callback, arg)       wifi_process_queue_push(callback, arg)
#define PUSH_TASK_TO_WFD_PROCESS_QUEUE(callback, arg)       wifi_process_queue_push(callback, arg)

int32_t wifi_process_queue_init     (void);

void    wifi_process_queue_deinit   (void);

int32_t wifi_process_queue_push     (int32_t (*callback)(NET_CTX_S *, void *), void* arg);

int32_t wifi_process_queue_run      (NET_CTX_S* net_ctx);

#endif /* __WIFI_PROCESS_QUEUE__ */
/**
 *@}
 */
