
#if defined(Linux)

#include <unistd.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <stdio.h>
#include <sys/syscall.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>

#ifndef THREAD_STACK_MIN
#define THREAD_STACK_MIN            (16 * 1024)
#endif

#ifndef THREAD_STACK_1M
#define THREAD_STACK_1M            (1 * 1024 * 1024)
#endif
#define TAG_RED_START               "\033[35m"
#define TAG_RED_END                 "\033[0m"

#define print_console(fmt, ...)     printf(TAG_RED_START "[%s:%d]" fmt TAG_RED_END "\n", __FILE__, __LINE__, ##__VA_ARGS__)

typedef struct thread_context
{
    void*       (*thread_entry)(void *);
    void*       thread_arg;
    const char* description;
    int32_t     detach_self;
    int32_t     stack_size;
    pthread_t   thread_id;
}
THREAD_CTX_S;

static void* thread_start(void* arg)
{
    THREAD_CTX_S*   os_thread = (THREAD_CTX_S *)arg;
    void*           res;
    long            tid;

    tid = syscall(__NR_gettid);
    print_console("tid(%ld) statck size(0x%08x) name(%s)", tid, os_thread->stack_size, os_thread->description);

    res = os_thread->thread_entry(os_thread->thread_arg);
    if ( os_thread->detach_self )
    {
        print_console("tid(%ld) name(%s) destroy self", tid, os_thread->description);
        free(os_thread);
    }

    return res;
}

void* pol_thread_create(void* (*entry)(void *), int32_t size, void* stack, int32_t priority, void* arg, const char* name)
{
    THREAD_CTX_S*   os_thread;
    pthread_attr_t  attr;
    int32_t         err;

    os_thread = (THREAD_CTX_S *)calloc(sizeof(THREAD_CTX_S), 1);
    if ( os_thread == NULL )
    {
        print_console("alloc THREAD_CTX_S failed: %d<%s>", errno, strerror(errno));
        return NULL;
    }

    pthread_attr_init(&attr);
#if 1
    pthread_attr_setstacksize(&attr, (size >= THREAD_STACK_1M ? size : THREAD_STACK_1M));
#endif
    pthread_setcancelstate(PTHREAD_CANCEL_ENABLE, NULL);
    pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS, NULL);

    os_thread->thread_entry = entry;
    os_thread->thread_arg   = arg;
    os_thread->description  = name;
    os_thread->stack_size   = size;

    err = pthread_create(&(os_thread->thread_id), &attr, thread_start, os_thread);
    if ( err )
    {
        print_console("pthread_create failed: %d<%s>", err, strerror(err));
        free(os_thread);
        return NULL;
    }

    return (void *)os_thread;
}

int32_t pol_thread_destroy(void* os_thread)
{
    pthread_t   thread = ((THREAD_CTX_S *)os_thread)->thread_id;
    void*       res = NULL;
    int32_t     err = 0;

    err = pthread_cancel(thread);
    if ( err )
    {
        print_console("thread(%s) cancel failed: %d<%s>", ((THREAD_CTX_S *)os_thread)->description, err, strerror(err));
    }
    err = pthread_join(thread, &res);
    if ( err )
    {
        print_console("thread(%s) join failed: %d<%s>", ((THREAD_CTX_S *)os_thread)->description, err, strerror(err));
    }
    free(os_thread);

    return 0;
}

int32_t pol_thread_detach(void* os_thread)
{
    print_console("thread(%s) detach self", ((THREAD_CTX_S *)os_thread)->description);
    ((THREAD_CTX_S *)os_thread)->detach_self = 1;
    pthread_detach(pthread_self());

    return 0;
}

int32_t pol_thread_is_self(void* os_thread)
{
    return ( pthread_self() == ((THREAD_CTX_S *)os_thread)->thread_id );
}

void* pol_mutex_create(void)
{
    pthread_mutex_t* os_mtx;

    os_mtx = (pthread_mutex_t *)calloc(sizeof(pthread_mutex_t), 1);
    if ( os_mtx == NULL )
    {
        print_console("alloc pthread_mutex_t failed: %d<%s>", errno, strerror(errno));
        return NULL;
    }

    if ( pthread_mutex_init(os_mtx, NULL) != 0 )
    {
        print_console("pthread_mutex_init failed: %d<%s>", errno, strerror(errno));
        free(os_mtx);
        return NULL;
    }

    return os_mtx;
}

void pol_mutex_destroy(void* os_mtx)
{
    if ( os_mtx != NULL )
    {
        pthread_mutex_destroy((pthread_mutex_t *)os_mtx);
        free(os_mtx);
    }
}

int32_t pol_mutex_lock(void* os_mtx)
{
    return pthread_mutex_lock((pthread_mutex_t *)os_mtx);
}

int32_t pol_mutex_timedlock(void* os_mtx, int32_t secs, int32_t usecs)
{
    struct timespec ts;
    int32_t         rv;

    clock_gettime(CLOCK_REALTIME, &ts);
    ts.tv_nsec += usecs * 1000;
    ts.tv_sec  += secs;
    if ( ts.tv_nsec > 1000000000 )
    {
        ts.tv_sec += ts.tv_nsec / 1000000000;
        ts.tv_nsec = ts.tv_nsec % 1000000000;
    }

    do
    {
        rv = pthread_mutex_timedlock((pthread_mutex_t *)os_mtx, &ts);
        if ( rv != 0 )
        {
            if ( errno == EINTR )
            {
                continue;   ///< This mutex is interrupted by a signal before the timeout, so continue to wait.
            }
            else if ( errno == ETIMEDOUT )
            {
                rv = 1;     ///< mutex wait timeout
            }
            else
            {
                print_console("pthread_mutex_timedlock(%d) err(%d, %s)\n", rv, errno, strerror(errno));
            }
        }
    }
    while ( 0 );

    return rv;
}

int32_t pol_mutex_unlock(void* os_mtx)
{
    return pthread_mutex_unlock((pthread_mutex_t *)os_mtx);
}

void* pol_sem_create(int32_t count)
{
    sem_t*  os_sem = NULL;

    os_sem = (sem_t *)calloc(sizeof(sem_t), 1);
    if ( os_sem == NULL )
    {
        print_console("alloc sem_t failed: %d<%s>", errno, strerror(errno));
        return NULL;
    }

    if ( sem_init(os_sem, PTHREAD_PROCESS_PRIVATE, count) != 0 )
    {
        print_console("sem_init failed: %d<%s>", errno, strerror(errno));
        free(os_sem);
        return NULL;
    }

    return os_sem;
}

void pol_sem_destroy(void* os_sem)
{
    if ( os_sem != NULL )
    {
        sem_destroy((sem_t *)os_sem);
        free(os_sem);
    }
}

int32_t pol_sem_wait(void* os_sem)
{
    return sem_wait((sem_t *)os_sem);
}

int32_t pol_sem_timedwait(void* os_sem, int32_t secs, int32_t usecs)
{
    struct timespec ts;
    int32_t         rv;

    clock_gettime(CLOCK_REALTIME, &ts);
    ts.tv_nsec += usecs * 1000;
    ts.tv_sec  += secs;
    if ( ts.tv_nsec > 1000000000 )
    {
        ts.tv_sec += ts.tv_nsec / 1000000000;
        ts.tv_nsec = ts.tv_nsec % 1000000000;
    }

    do
    {
        rv = sem_timedwait((sem_t *)os_sem, &ts);
        if ( rv != 0 )
        {
            if ( errno == EINTR )
            {
                continue;   ///< This semaphore is interrupted by a signal before the timeout, so continue to wait.
            }
            else if ( errno == ETIMEDOUT )
            {
                rv = 1;     ///< This semaphore wait timeout.
            }
            else
            {
                print_console("sem_timedwait(%d) err(%d, %s)\n", rv, errno, strerror(errno));
            }
        }
    }
    while ( 0 );

    return rv;
}

int32_t pol_sem_post(void* os_sem)
{
    return sem_post((sem_t *)os_sem);
}
#endif /* defined(Linux) */
