/**
 * @copyright  2024 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       app_manager.c
 * @addtogroup
 * @{
 * @date    2024/2/19
 * @version v1.0
 * @details
 *
 */

#include <stdint.h>
#include <time.h>

#include "cjson/cJSON.h"
#include "utilities/msgrouter.h"
#include "pedk_mgr.h"
#include "app_manager.h"
#include "cmd.h"
#include "pedk_download.h"

#ifndef MIN
# define MIN(a,b) ((a) < (b) ? (a) : (b))
#endif
cJSON* applist = NULL;

char* am_json_to_array(cJSON* applist)
{
	//申请动态空间

	//循环遍历json数组

		//将json的成员添加到动态数组中

	return NULL;
}
#if 0
int pedk_get_app_info(cJSON** buffer, char* appname)
{

	char apppath[PEDK_DOWNLOAD_MAX_NAME_LEN] = {0};
    snprintf(apppath, sizeof(apppath), "%s/%s/%s", PEDK_PATH, appname, PEDK_APP_JSON);

	long fileSize = get_file_len(apppath);
	if(fileSize == EXIT_FAILURE_PEDK){
		return EXIT_FAILURE_PEDK;
	}

	char* app_buffer = malloc(fileSize + 1);
	int ret = get_file_content(apppath, &app_buffer, fileSize);
	if(ret == EXIT_FAILURE_PEDK){
		printf( "get %s fail\n", PEDK_APP_LIST_FILE);
	}

	cJSON* app_info = cJSON_CreateObject();
	cJSON_AddStringToObject(app_info, "name", cJSON_GetObjectItem(cJSON_Parse(app_buffer), "name")->valuestring);
	cJSON_AddStringToObject(app_info, "appId", cJSON_GetObjectItem(cJSON_Parse(app_buffer), "appId")->valuestring);
    cJSON_AddStringToObject(app_info, "version", "0.1.0");
    cJSON_AddStringToObject(app_info, "apiLevel", "1.0");
    cJSON_AddStringToObject(app_info, "uiLevel", "1.0");

	char appfunclist[PEDK_DOWNLOAD_MAX_NAME_LEN] = {0};
    snprintf(appfunclist, sizeof(appfunclist), "%s/%s/%s", PEDK_PATH, appname, PEDK_FUNCLIST_JSON);

	fileSize = get_file_len(appfunclist);
	if(fileSize == EXIT_FAILURE_PEDK){
		return EXIT_FAILURE_PEDK;
	}

	char* app_funclist = malloc(fileSize + 1);
	ret = get_file_content(appfunclist, &app_funclist, fileSize);
	if(ret == EXIT_FAILURE_PEDK){
		printf( "get %s fail\n", PEDK_APP_LIST_FILE);
	}
	//cJSON* app_funclist_json = cJSON_Parse(app_funclist);

	*buffer = cJSON_GetObjectItem(cJSON_Parse(app_buffer), "app");

	*buffer = app_info;
	printf( "buffer %s\n", cJSON_Print(*buffer));

	free(app_buffer);
	free(app_funclist);

	return EXIT_SUCCESS_PEDK;
}

int pedk_get_app_list(cJSON** buffer)
{
	long fileSize = get_file_len(PEDK_APP_LIST_FILE);
	if(fileSize == EXIT_FAILURE_PEDK){
		return EXIT_FAILURE_PEDK;
	}

	char* app_buffer = malloc(fileSize + 1);
	int ret = get_file_content(PEDK_APP_LIST_FILE, &app_buffer, fileSize);
	if(ret == EXIT_FAILURE_PEDK){
		printf( "get %s fail\n", PEDK_APP_LIST_FILE);
	}

	*buffer = cJSON_GetObjectItem(cJSON_Parse(app_buffer), "app");

	printf( "buffer %s\n", cJSON_Print(*buffer));

	free(app_buffer);
	return EXIT_SUCCESS_PEDK;
}


int am_list_find_appid(cJSON* applist, char* appid)
{
	//获取数组数量
	int num = cJSON_GetArraySize(applist);

	//循环遍历查找
	int i = 0;
	for(i = 0; i < num; i++){
		cJSON* item = cJSON_GetArrayItem(applist, i);
		cJSON* appId = cJSON_GetObjectItem(item, "appId");

		printf( "appId->valuestring=%s\n",appId->valuestring);
		printf( "appid=%s\n",appid);
		if(0 == strcmp(appId->valuestring, appid)){
			printf( "find appid return \n");
			return i;
		}
	}

	return -1;
}

cJSON* am_get_app_list(void)
{
	cJSON* json_data = cJSON_CreateObject();
	int ret = pedk_get_app_list(&json_data);
	/*if(ret == EXIT_FAILURE_PEDK){
		cJSON_Delete(json_data);
		return NULL;
	}*/
	return json_data;
}

void am_set_app_list(cJSON* applist)
{

	cJSON* json_data = cJSON_CreateObject();
	cJSON_AddItemToObject(json_data, "app", applist);
	char* json_data_str = cJSON_Print(json_data);
	set_file_content(PEDK_APP_LIST_FILE, json_data_str, strlen(json_data_str));

	cJSON_Delete(json_data);
}

int am_delete_app(char* appId)
{
    char js_folder_name[PEDK_DOWNLOAD_MAX_NAME_LEN];
	cJSON* app_list = am_get_app_list();
	int num = am_list_find_appid(app_list, appId);
	if(num == -1){
		return EXIT_FAILURE_PEDK;
	}else{
		cJSON* item = cJSON_GetArrayItem(app_list, num);
		cJSON* appname = cJSON_GetObjectItem(item, "name");
		//拼路径
		snprintf(js_folder_name, sizeof(js_folder_name), "%s/%s",PEDK_PATH, appname->valuestring);
		printf( "js_folder_name=%s\n",js_folder_name);
		//查找文件
		if(access(js_folder_name, F_OK) == 0){
		//删除路径
		pi_runcmd(NULL, 0, 0, "rm -rf %s ", js_folder_name);
		//更新app_namelist
		cJSON_DeleteItemFromArray(app_list, num);
		am_set_app_list(app_list);
		}
	}

	//cJSON_Delete(app_list);
	return EXIT_SUCCESS_PEDK;
}

//TODO:找auto_boot_app
void am_find_auto_boot_app(char** name)
{
	//char* appname_str = NULL;
	cJSON* applist = cJSON_CreateObject();
	applist = am_get_app_list();
	/*if(applist == NULL){
		printf( "Can't every find app\n");
		cJSON_Delete(applist);
		*name = NULL;
		return;
	}*/

	//获取数组数量
	int num = cJSON_GetArraySize(applist);

	//循环遍历查找
	int i = 0;
	for(i = 0; i < num; i++){
		cJSON* item = cJSON_GetArrayItem(applist, i);
		cJSON* auto_boot = cJSON_GetObjectItem(item, "auto_boot");
		printf( "auto_boot = %d\n", auto_boot->valueint);
		if(auto_boot->valueint == 1){
			strcpy(*name, cJSON_GetObjectItem(item, "name")->valuestring);
			printf( "find app auto_boot %s\n", *name);
			break;
		}else{
			*name = NULL;
		}
	}

	cJSON_Delete(applist);
	//return appname_str;
}

//TODO：获取app工作路径
char* am_get_app_workspace_by_rtid(char* appid)
{
	char* appname = am_list_find_rtid(appid);
	if(appname == NULL){
		printf( "find rtid error return\n");
		return NULL;
	}
	char* apppath = malloc(strlen(PEDK_PATH) + strlen("/") + strlen(appname) +1);
    snprintf(apppath, sizeof(apppath), "%s/%s", PEDK_PATH, appname);
	return apppath;
}
#endif

//TODO:启动app接口
int am_start_app(char* app_name)
{
	printf( "start app %s\n", app_name);
	int len = strlen(app_name);
	printf( "strlen %d\n", len);
	pedk_mgr_send_cmd_to_runenv(CMD_APP_START, (unsigned char*)app_name, len);
	return 0;
}

int am_start_app_recv(char* app_name)
{
	printf( "start app %s\n", app_name);
	int len = strlen(app_name);
	printf( "strlen %d\n", len);
	pedk_mgr_send_cmd_to_runenv(CMD_APP_START, (unsigned char*)app_name, len);
	return 0;
}


//TODO：创建链表记录rtid和appname的对应关系
int am_add_app_meg(int rtid, char* appname)
{
	cJSON* app_meg = cJSON_CreateObject();
	cJSON_AddStringToObject(app_meg, "name", appname);
	cJSON_AddNumberToObject(app_meg, "rtid", rtid);

	cJSON_AddItemToArray(applist, app_meg);
	return 0;
}

char* am_list_find_rtid(char* appid)
{
	//获取数组数量
	int num = cJSON_GetArraySize(applist);

	//循环遍历查找
	int i = 0;
	for(i = 0; i < num; i++){
		cJSON* item = cJSON_GetArrayItem(applist, i);
		cJSON* appId = cJSON_GetObjectItem(item, "appId");

		printf( "appId->valuestring=%s\n",appId->valuestring);
		printf( "appid=%s\n",appid);
		if(0 == strcmp(appId->valuestring, appid)){
			printf( "find appid return \n");
			return cJSON_GetObjectItem(item, "name")->valuestring;
		}
	}

	return NULL;
}


void app_init(void)
{
    cJSON *root = pedk_download_applist_read_json();
    if(root == NULL)
    {
        printf("pedk_download_applist_get_all_entries error\n");
        return;
    }

    char *app_list_buff = cJSON_PrintUnformatted(root);

    char appid[100] = "";

    pedk_download_find_auto_boot(app_list_buff, appid);

    if (strlen(appid) == 0) {
        printf("No appId found with auto_root=true. Considering all items with auto_root=false.\n");
    } else {
        printf("AppId with auto_root=true: %s\n", appid);
        am_start_app(appid);
    }

    free(app_list_buff);
}


//测试用接口，暂时屏蔽
/*void app_init(void)
{
	char* name = "test";//= malloc(PEDK_DOWNLOAD_MAX_NAME_LEN);
	//am_find_auto_boot_app(&name);
	printf("name == %s\n",name);
	if(name != NULL){
		am_start_app(name);
	}
	//free(name);
}*/

void app_manager_init(void)
{
	applist = cJSON_CreateArray();
}

