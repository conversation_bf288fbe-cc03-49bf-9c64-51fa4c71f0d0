/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file threads_pool.h
 * @addtogroup net
 * @{
 * @addtogroup threads_pool
 * <AUTHOR>
 * @date 2023-9-18
 * @brief threads pool manager
 */
#include "nettypes.h"

#include "threads_pool.h"

struct thread_context
{
    THREAD_TASK_FUNC    task_func;
    PI_SEMAPHORE_T      task_sem;
    PI_THREAD_T         task_tid;
    void*               task_arg;
    uint32_t            active;
};

struct threads_pool
{
    struct thread_context*  threads_array;
    size_t                  threads_num;
    PI_MUTEX_T              mutex;
};

static void* thread_task_handler(void* arg)
{
    struct thread_context* pctx = (struct thread_context *)arg;

    while (1)
    {
        pi_sem_wait(pctx->task_sem);

        if ( pctx->active == 0 )
        {
            continue;
        }

        if ( pctx->task_func )
        {
            pctx->task_func(pctx->task_arg);
        }
        pctx->task_func = NULL;
        pctx->task_arg  = NULL;
        pctx->active    = 0;
    }

    return NULL;
}

int32_t threads_pool_add_task(THREADS_POOL_S* thiz, THREAD_TASK_FUNC handler, void* arg)
{
    struct thread_context*  pctx = NULL;
    int32_t                 rs = -1;

    RETURN_VAL_IF(thiz == NULL, NET_WARN, -1);

    pi_mutex_lock(thiz->mutex);
    for ( int32_t retries = 0; retries < 5; ++retries )
    {
        for ( size_t i = 0; i < thiz->threads_num; ++i )
        {
            if ( thiz->threads_array[i].active == 0 )
            {
                pctx = &(thiz->threads_array[i]);
                pctx->task_func = handler;
                pctx->task_arg  = arg;
                pctx->active    = 1;
                break;
            }
        }

        if ( pctx != NULL )
        {
            rs = 0;
            break;
        }
        NET_WARN("Threads pool busy, retry again after 2S...");
        pi_sleep(2);
    }
    pi_mutex_unlock(thiz->mutex);

    if ( pctx != NULL )
    {
        pi_sem_post(pctx->task_sem);
    }
    return rs;
}

void threads_pool_destroy(THREADS_POOL_S* thiz)
{
    struct thread_context*  pctx;

    if ( thiz != NULL )
    {
        for ( size_t i = 0; i < thiz->threads_num; ++i )
        {
            pctx = &(thiz->threads_array[i]);
            pi_thread_destroy(pctx->task_tid);
            pi_sem_destroy(pctx->task_sem);
        }
        if ( thiz->mutex != INVALIDMTX )
        {
            pi_mutex_destroy(thiz->mutex);
        }
        if ( thiz->threads_array != NULL )
        {
            pi_free(thiz->threads_array);
        }
        pi_free(thiz);
    }
}

THREADS_POOL_S* threads_pool_create(size_t max_threads)
{
    struct thread_context*  pctx;
    struct threads_pool*    thiz;

    RETURN_VAL_IF(max_threads == 0, NET_WARN, NULL);

    thiz = (THREADS_POOL_S *)pi_zalloc(sizeof(THREADS_POOL_S));
    RETURN_VAL_IF(thiz == NULL, NET_WARN, NULL);

    do
    {
        BREAK_IF((thiz->mutex = pi_mutex_create()) == NULL, NET_WARN);

        thiz->threads_array = (struct thread_context *)pi_zalloc(max_threads * sizeof(struct thread_context));
        BREAK_IF(thiz->threads_array == NULL, NET_WARN);

        for ( thiz->threads_num = 0; thiz->threads_num < max_threads; thiz->threads_num++ )
        {
            pctx = &(thiz->threads_array[thiz->threads_num]);

            BREAK_IF((pctx->task_sem = pi_sem_create(0)) == NULL, NET_WARN);

            pctx->task_tid = pi_thread_create(thread_task_handler, PI_NORMAL_STACK, NULL, PI_LEVEL_PRIORITY, pctx, "thread_task_handler");
            BREAK_IF(pctx->task_tid == NULL, NET_WARN);
        }

        if ( thiz->threads_num < max_threads )
        {
            NET_WARN("create threads_pool[%d] failed: %d<%s>", thiz->threads_num, errno, strerror(errno));
            if ( pctx->task_sem != INVALIDSEM )
            {
                pi_sem_destroy(pctx->task_sem);
                pctx->task_sem = INVALIDSEM;
            }
        }
    }
    while ( 0 );

    NET_DEBUG("there are %u threads in the threads pool", thiz->threads_num);
    return thiz;
}
/**
 *@}
 */
