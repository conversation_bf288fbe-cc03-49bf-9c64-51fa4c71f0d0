class CopyJob
{
	#listeners
    #job_state
    /**
     * 复印种类
     *
     * | 种类 | 含义 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | COPY_NORMAL | 普通复印 | 支持 | 支持 |
     * | COPY_ID_CARD | ID复印 | 支持 | 支持 |
     * | COPY_BILL | 票据复印 | 支持 | 支持 |
     * | COPY_CLONE | 克隆复印 | 支持 | 支持 |
     * | COPY_POSTER | 海报复印 | 支持 | 支持 |
     * | COPY_NUP | 多页合一 | 支持 | 支持 |
     * | COPY_BOOKLET | 小册子复印 | 支持 | 不支持 |
     * | COPY_WATERMARK | 水印复印 | 不支持 | 支持 |
     * | COPY_SCALE | 缩放复印 | 不支持 | 支持 |
     * | COPY_MIX | 混合纸型复印 | 支持 | 不支持 |
     *
     * @example
     * //创建启动作业参数
     * param = new CopyParameterSet();
     * //设置启动作业需要的参数
     * input_size = new InputPaperSize(A4);
     * param.addParameter(COPY_PARAM_INPUTPAPERSIZE, input_size);
     * param.addParameter(...);
     * auto_corr_mode = new AutoCorrectionMode(true);
     * param.addParameter(COPY_PARAM_AUTOCORRECTIONMODE, auto_corr_mode);
     * //创建复印作业并启动
     * copy_job = new CopyJob(COPY_NORMAL);
     * state = copy_job.start(1, param ,null);
     * console.log("复印作业状态:" + state);
     */

    /**
    * 创建一个作业类实例
    * @since V1.0
    * @param {String} type 复印作业类型
    */
    constructor( type )
    {
        this.copy_type = type;
		console.log( 'NNNNNN copy copy_type = ' + type +'\n');
		this.#listeners = []
        this.#job_state = undefined
    }
	/**
     * 注册作业状态监听器
     * @since V1.0
     * @param {JobStateListener} listener 作业状态监听器
     * @returns {Boolean}
     * <pre>
     * true：注册成功
     * false：注册失败
     * </pre>
     */
    addListener(listener)
    {
		if(typeof listener != "object")
		{
			throw TypeError('EINVALIDPARAM')
		}
		return this.#listeners.length + 1 === this.#listeners.push(listener)? true : false
    }

    /**
     * 取消注册作业状态监听器
     * @since V1.0
     * @param {JobStateListener} listener 作业状态监听器
     * @returns {Boolean}
     * <pre>
     * true：取消成功
     * false：取消失败
     * </pre>
     */
    removeListener(listener)
    {
        if(typeof listener != "object")
        {
            throw TypeError('EINVALIDPARAM')
        }

        //return this.#listeners.length - 1 === this.#listeners.pop(listener)? true : false
		this.#listeners.pop(listener);
		return true;
    }
	/**
     * 作业状态更新
     * @since V1.0
     */
    notify(jobid, newJobState)
    {
		if(typeof jobid !== 'number' || typeof newJobState !== 'string')
		{
			throw TypeError('EINVALIDPARAM')
		}

		this.jobid = jobid;

		console.log( 'copy jobid = ' + this.jobid +'\n');


		if(this.#job_state !== newJobState)
		{
			this.#job_state = newJobState
			for(const listener of this.#listeners)
		{
			listener.notify(newJobState)
		}
		}

    }
    /**
     * @description获取作业ID
     * @since V1.0
     * @returns {Number|String} 作业id
     */
    getJobId()
    {

        return this.jobid;
    }

    /**
     * 获取工单号
     * @since V1.0
     * @returns {Number|String} 工单号
     */
    getWoNum()
    {

        return this.woNum;
    }

    /**
     * 取得复印作业种类
     * @since V1.0
     * @returns {String} 复印种类
     */
    getJobType()
    {
        return this.copy_type;
    }

    /**
     * 取得作业状态
     * @since V1.0
     * @returns {String} 作业状态
     * <ul>
     * <li>JBSts_Init</li>
     * <li>JBSts_PrePare</li>
     * <li>JBSts_Ready</li>
     * <li>JBSts_Running</li>
     * <li>JBSts_Suspend</li>
     * <li>JBSts_Pause</li>
     * <li>JBSts_Cancelling</li>
     * <li>JBSts_Aborting</li>
     * <li>JBSts_Finish</li>
     * <li>JBSts_History</li>
     * </ul>
     */
    getJobState()
    {
        return this.#job_state;
    }

    /**
     * 启动作业
     * @since V1.0
     * @param {Number} woNum 工单号
     * @param {CopyParameterSet} param 作业参数
     * @param {Quota} quota 作业配额
     * @returns {Number|String} 作业启动结果
     * <ul>
     * <li>0:成功</li>
     * <li>1:输入参数错误</li>
     * <li>2:内部不足</li>
     * <li>3:权限拒绝</li>
     * <li>4:其它</li>
     * </ul>
     */
    start( woNum, param, quota )
    {
		var ret = 0;
		if(typeof woNum !== 'number' || typeof param !== 'object' || typeof quota !== 'object')
		{
			//throw TypeError('EINVALIDPARAM')
			console.log( 'EINVALIDPARAM \n');
			return 1;
		}

		this.woNum = woNum;
		console.log( 'copy woNum = ' + this.woNum + ' copy_type = ' + this.copy_type +'\n');

		let instance = new globalThis.pedk.jobctl.JobctlManager()

		if(instance.addListeners(this, woNum) === true)
		{
			ret = copy_jsc_copyStart(param.id,this.copy_type,woNum);
			if(ret == 3)
			{
				 return 3;
			}
			else
			{
				return 0;
			}

			return
		}
		else
		{
		   return 1;
		}
    }

    /**
     * 取消作业
     * @since V1.0
     */
    cancel()
    {
		var ret = 0;
        ret = copy_jsc_copyCancel();

		if(ret == 3)
		{
			 return 3;
		}
		else
		{
			return 0;
		}
    }

    continue()
    {
		var ret = 0;
        ret = copy_jsc_copy_next_page();

		return 0;
    }

    finish()
    {
		var ret = 0;
        ret = copy_jsc_copy_job_done();
		return 0;
    }

}

/**
 * @const {String} 参数:原稿尺寸
 */
var COPY_PARAM_INPUTPAPERSIZE = 'COPY_PARAM_INPUTPAPERSIZE';
/**
 * @const {String} 参数:出纸纸盒
 */
var COPY_PARAM_OUTPUTTRAY = 'COPY_PARAM_OUTPUTTRAY';
/**
 * @const {String} 参数:缩放率
 */
var COPY_PARAM_ZOOM = 'COPY_PARAM_ZOOM';
/**
 * @const {String} 参数:画像色彩
 */
var COPY_PARAM_IMAGE_COLOR = 'COPY_PARAM_IMAGE_COLOR';
/**
 * @const {String} 参数:双面复印模式
 */
var COPY_PARAM_COPYMODE = 'COPY_PARAM_COPYMODE';
/**
 * @const {String} 参数:逐份模式
 */
var COPY_PARAM_COLLATEMODE = 'COPY_PARAM_COLLATEMODE';
/**
 * @const {String} 参数:复印画质
 */
var COPY_PARAM_QUALITYTYPE = 'COPY_PARAM_QUALITYTYPE';
/**
 * @const {String} 参数:水印内容
 */
var COPY_PARAM_WATERMARK = 'COPY_PARAM_WATERMARK';
/**
 * @const {String} 参数:份数
 */
var COPY_PARAM_COPIES = 'COPY_PARAM_COPIES';
/**
 * @const {String} 参数:自动纠偏
 */
var COPY_PARAM_AUTOCORRECTIONMODE = 'COPY_PARAM_AUTOCORRECTIONMODE';
/**
 * @const {String} 参数:排列方式
 */
var COPY_PARAM_ARRANGEMENTMODE = 'COPY_PARAM_ARRANGEMENTMODE';
/**
 * @const {String} 参数:多页合一
 */
var COPY_PARAM_NUPMODE = 'COPY_PARAM_NUPMODE';
/**
 * @const {String} 参数:克隆
 */
var COPY_PARAM_CLONEMODE = 'COPY_PARAM_CLONEMODE';
/**
 * @const {String} 参数:海报
 */
var COPY_PARAM_POSTER = 'COPY_PARAM_POSTER';
/**
 * @const {String} 参数:色彩模式
 */
var COPY_COLOR_MODE = 'COPY_COLOR_MODE';
/**
 * @const {String} 参数:复印扫描纸张来源
 */
var COPY_SCAN_SOURCE = 'COPY_SCAN_SOURCE';
/**
 * @const {String} 参数:复印画像方向
 */
var COPY_IMAGE_ORIENTATION = 'COPY_IMAGE_ORIENTATION';
/**
 * @const {String} 参数:复印色彩平衡
 */
var COPY_COLOR_BALANCE = 'COPY_COLOR_BALANCE';
/**
 * @const {String} 参数:复印水平边距
 */
var COPY_HORIZONTAL_MARGIN = 'COPY_HORIZONTAL_MARGIN';
/**
 * @const {String} 参数:复印垂直边距
 */
var COPY_VERTICAL_MARGIN = 'COPY_VERTICAL_MARGIN';
/**
 * @const {String} 参数:复印边对边模式
 */
var COPY_EDGE_TO_EDGE_MODE = 'COPY_EDGE_TO_EDGE_MODE';
/**
 * @const {String} 参数:复印分隔页
 */
var COPY_SEPARATOR = 'COPY_SEPARATOR';
/**
 * @const {String} 参数:复印多合一复印合并模式
 */
var COPY_NUP_COMBINATION = 'COPY_NUP_COMBINATION';
/**
 * @const {String} 参数:复印双面小册子模式
 */
var COPY_BOOKLET_DUPLEX = 'COPY_BOOKLET_DUPLEX';
/**
 * @const {String} 参数:复印镜像模式
 */
var COPY_MIRROR_MODE = 'COPY_MIRROR_MODE';
/**
 * @const {String} 参数:复印页眉使能
 */
var COPY_PAGE_HEADER_ENABLE = 'COPY_PAGE_HEADER_ENABLE';
/**
 * @const {String} 参数:复印页眉使能
 */
var COPY_PAGE_HEADER_POSITION = 'COPY_PAGE_HEADER_POSITION';
/**
 * @const {String} 参数:复印页眉页码模式
 */
var COPY_PAGE_HEADER_PAGINATION = 'COPY_PAGE_HEADER_PAGINATION';
/**
 * @const {String} 参数:复印页眉内容类型
 */
var COPY_PAGE_HEADER_TEXT_TYPE = 'COPY_PAGE_HEADER_TEXT_TYPE';
/**
 * @const {String} 参数:复印页眉自定义内容
 */
var COPY_PAGE_HEADER_TEXT = 'COPY_PAGE_HEADER_TEXT';
/**
 * @const {String} 参数:复印页脚使能
 */
var COPY_PAGE_FOOTER_ENABLE = 'COPY_PAGE_FOOTER_ENABLE';
/**
 * @const {String} 参数:复印页脚使能
 */
var COPY_PAGE_FOOTER_POSITION = 'COPY_PAGE_FOOTER_POSITION';
/**
 * @const {String} 参数:复印页脚页码模式
 */
var COPY_PAGE_FOOTER_PAGINATION = 'COPY_PAGE_FOOTER_PAGINATION';
/**
 * @const {String} 参数:复印页脚内容类型
 */
var COPY_PAGE_FOOTER_TEXT_TYPE = 'COPY_PAGE_FOOTER_TEXT_TYPE';
/**
 * @const {String} 参数:复印页脚自定义内容
 */
var COPY_PAGE_FOOTER_TEXT = 'COPY_PAGE_FOOTER_TEXT';
/**
 * @const {String} 参数:复印备份模式
 */
var COPY_BACKUP_MODE = 'COPY_BACKUP_MODE';
/**
 * @const {String} 参数:样本复印模式
 */
var COPY_SAMPLE_MODE = 'COPY_SAMPLE_MODE';
/**
 * @const {String} 参数:颜色反显模式
 */
var COPY_USE_COLOR_INVERSION = 'COPY_USE_COLOR_INVERSION';
/**
 * @const {String} 参数:扫描时复印模式(同一个作业，复印的时候同时扫描到)
 */
var COPY_USE_COPY_SCAN_MEANTIME = 'COPY_USE_COPY_SCAN_MEANTIME';
/**
 * @const {String} 参数:颜色滤除模式
 */
var COPY_USE_REMOVE_COLOR = 'COPY_USE_REMOVE_COLOR';
/**
 * @const {String} 参数:边缘消除模式
 */
var COPY_USE_EDGE_CLEAN = 'COPY_USE_EDGE_CLEAN';
/**
 * @const {String} 参数:边缘消除距离
 */
var COPY_FILTER_EDGE_MARGIN = 'COPY_FILTER_EDGE_MARGIN';
/**
 * @const {String} 参数:原稿翻页方式
 */
var COPY_ORIGINAL_FLIP = 'COPY_ORIGINAL_FLIP';
/**
 * @const {String} 参数:复印翻页方式
 */
var COPY_COPIES_FLIP = 'COPY_COPIES_FLIP';
/**
 * @const {String} 参数:封面
 */
var COPY_COVER = 'COPY_COVER';
/**
 * @const {String} 参数:封底
 */
var COPY_COVER_BACK = 'COPY_COVER_BACK';
/**
 * @const {String} 参数:封面/底进纸盒
 */
var COPY_COVER_TRAY_IN = 'COPY_COVER_TRAY_IN';
/**
 * @const {String} 参数:装订模式
 */
var COPY_STAPLE_MODE = 'COPY_STAPLE_MODE';
/**
 * @const {String} 参数:打孔模式
 */
var COPY_PUNCH_MODE = 'COPY_PUNCH_MODE';
/**
 * @const {String} 参数:折叠模式
 */
var COPY_FOLD_MODE = 'COPY_FOLD_MODE';
/**
 * @const {String} 参数:接纸架
 */
var COPY_TRAY_RECEIVE = 'COPY_TRAY_RECEIVE';
/**
 * @const {String} 参数:装订模式
 */
var COPY_SHIFT_MODE = 'COPY_SHIFT_MODE';
/**
 * @const {String} 参数:折叠纸张数量
 */
var COPY_FLOD_NUMBER = 'COPY_FLOD_NUMBER';
/**
 * @const {String} 参数:装订器
 */
var COPY_HAVE_STAPLER = 'COPY_HAVE_STAPLER';
/**
 * @const {String} 参数:封面/封底纸张
 */
var COPY_COVER_PAPER_TYPE = 'COPY_COVER_PAPER_TYPE';
/**
 * @const {String} 参数:分隔页进纸盒
 */
var COPY_SEPARATOR_TRAY_IN = 'COPY_SEPARATOR_TRAY_IN';
/**
 * @const {String} 参数:用户自定义复印扫描纸张参数
 */
var COPY_USER_DEFINE_SCAN_PAPER_PARAM = 'COPY_USER_DEFINE_SCAN_PAPER_PARAM';
/**
 * @const {String} 参数:用户自定义复印打印纸张参数
 */
var COPY_USER_DEFINE_PRINT_PAPER_PARAM = 'COPY_USER_DEFINE_PRINT_PAPER_PARAM';
/**
 * @const {String} 参数:打印纸张介质
 */
var COPY_PAGE_TYPE = 'COPY_PAGE_TYPE';
/**
 * @const {String} 参数:纠偏模式
 */
var COPY_SKEWING_MODE = 'COPY_SKEWING_MODE';
/**
 * @const {String} 参数:省墨模式
 */
var COPY_TONER_MODE = 'COPY_TONER_MODE';

var COPY_RETENTION  = 'COPY_RETENTION';


var copy_param_set_id = 8;

/**
* 复印参数集合
* @class
*/
class CopyParameterSet
{
    /**
     * 构造函数
     * @since V1.0
     */
    constructor()
    {
    	this.id = copy_param_set_id++;
		console.log("CopyParameterSet id ="+this.id);

		copy_jsc_copy_parameter_set_init(this.id);
    }

    /**
     * 设置参数
     * @since V1.0
     * @param {String} prop_key  属性键
     * @param {Object} prop_val  属性值
     * @returns {Boolean|String} <pre>true:成功 false:失败</pre>
     */
    addParameter( prop_key, prop_val )
    {
		var ret = 0;
		console.log( 'copy addParameter prop_key= ' + prop_key +' prop_val= ' + JSON.stringify(prop_val)+'\n');

		switch(prop_key)
		{
			case COPY_PARAM_INPUTPAPERSIZE:
			{
				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_OUTPUTTRAY:
			{

				ret += copy_jsc_addParameter(prop_key+'_TRAY',this.id,prop_val.tray);
				ret += copy_jsc_addParameter(prop_key+'_SIZE',this.id,prop_val.size);
				break;
			}
			case COPY_PARAM_ZOOM:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_IMAGE_COLOR:
			{

				ret += copy_jsc_addParameter(prop_key+'_BRIGHTNESS',this.id,prop_val.brightness);
				ret += copy_jsc_addParameter(prop_key+'_CONTRAST',this.id,prop_val.contrast);
				ret += copy_jsc_addParameter(prop_key+'_HUE',this.id,prop_val.hue);
				ret += copy_jsc_addParameter(prop_key+'_SATURATION',this.id,prop_val.saturation);
				ret += copy_jsc_addParameter(prop_key+'_SHARPNESS',this.id,prop_val.sharpness);
				ret += copy_jsc_addParameter(prop_key+'_BACKGROUNDMOVE_LEVEL',this.id,prop_val.backgroundmove_level);
				break;
			}
			case COPY_PARAM_COPYMODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_COLLATEMODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_QUALITYTYPE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_WATERMARK:
			{

				ret += copy_jsc_addParameter(prop_key+'_MODE',this.id,prop_val.val);
				ret += copy_jsc_addParameter(prop_key+'_STR',this.id,prop_val.water_str);
				break;
			}
			case COPY_PARAM_COPIES:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_AUTOCORRECTIONMODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_ARRANGEMENTMODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_NUPMODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_CLONEMODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PARAM_POSTER:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COLOR_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_SCAN_SOURCE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_IMAGE_ORIENTATION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COLOR_BALANCE:
			{

				ret += copy_jsc_addParameter(prop_key+'_C',this.id,prop_val.C);
				ret += copy_jsc_addParameter(prop_key+'_M',this.id,prop_val.M);
				ret += copy_jsc_addParameter(prop_key+'_Y',this.id,prop_val.Y);
				ret += copy_jsc_addParameter(prop_key+'_K',this.id,prop_val.K);
				break;
			}
			case COPY_HORIZONTAL_MARGIN:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_VERTICAL_MARGIN:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_EDGE_TO_EDGE_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_SEPARATOR:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_NUP_COMBINATION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_BOOKLET_DUPLEX:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_MIRROR_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_HEADER_ENABLE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_HEADER_POSITION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_HEADER_PAGINATION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_HEADER_TEXT_TYPE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_HEADER_TEXT:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_FOOTER_ENABLE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_FOOTER_POSITION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_FOOTER_PAGINATION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_FOOTER_TEXT_TYPE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PAGE_FOOTER_TEXT:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_BACKUP_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_SAMPLE_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_USE_COLOR_INVERSION:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_USE_COPY_SCAN_MEANTIME:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_USE_REMOVE_COLOR:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_USE_EDGE_CLEAN:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_FILTER_EDGE_MARGIN:
			{

				ret += copy_jsc_addParameter(prop_key+'_UP',this.id,prop_val.up);
				ret += copy_jsc_addParameter(prop_key+'_DOWN',this.id,prop_val.down);
				ret += copy_jsc_addParameter(prop_key+'_LEFT',this.id,prop_val.left);
				ret += copy_jsc_addParameter(prop_key+'_RIGHT',this.id,prop_val.right);
				break;
			}
			case COPY_ORIGINAL_FLIP:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COPIES_FLIP:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COVER:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COVER_BACK:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COVER_TRAY_IN:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_STAPLE_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_PUNCH_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_FOLD_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_TRAY_RECEIVE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_SHIFT_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_FLOD_NUMBER:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_HAVE_STAPLER:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_COVER_PAPER_TYPE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_SEPARATOR_TRAY_IN:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_USER_DEFINE_SCAN_PAPER_PARAM:
			{

				ret += copy_jsc_addParameter(prop_key+'W',this.id,prop_val.w);
				ret += copy_jsc_addParameter(prop_key+'H',this.id,prop_val.h);
				break;
			}
			case COPY_USER_DEFINE_PRINT_PAPER_PARAM:
			{

				ret += copy_jsc_addParameter(prop_key+'W',this.id,prop_val.w);
				ret += copy_jsc_addParameter(prop_key+'H',this.id,prop_val.h);
				break;
			}
			case COPY_PAGE_TYPE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_SKEWING_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_TONER_MODE:
			{

				ret += copy_jsc_addParameter(prop_key,this.id,prop_val.val);
				break;
			}
			case COPY_RETENTION:
			{
				console.log("set_retention_param mode = "+prop_val.mode);
				console.log("set_retention_param format_type = "+prop_val.copy_retention_param.format_type);
				console.log("set_retention_param url = "+prop_val.copy_retention_param.url);
				console.log("set_retention_param headers = "+prop_val.copy_retention_param.headers);

				ret += copy_jsc_set_retention_param(prop_val.mode,prop_val.copy_retention_param.format_type,prop_val.copy_retention_param.url,prop_val.copy_retention_param.headers);
				break;
			}
			default :
			console.log("COPY param error ");
			ret = 1;
			break;

		}

		if(ret == 0)
		{
			return true;
		}
		else
		{
			return false;
		}

    }

    /**
     * 设置参数
     * @since V1.0
     * @param {String} prop_key  属性键
     * @returns {Boolean|String} <pre>true:成功 false:失败</pre>
     */
    removeParameter( prop_key )
	{
		var ret = 0;
		console.log( 'copy removeParameter prop_key= ' + prop_key +'\n');

		switch(prop_key)
		{
			case COPY_PARAM_INPUTPAPERSIZE:
			{
				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_OUTPUTTRAY:
			{

				ret += copy_jsc_removeParameter(prop_key+'_TRAY',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_SIZE',this.id);
				break;
			}
			case COPY_PARAM_ZOOM:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_IMAGE_COLOR:
			{

				ret += copy_jsc_removeParameter(prop_key+'_BRIGHTNESS',this.id,);
				ret += copy_jsc_removeParameter(prop_key+'_CONTRAST',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_HUE',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_SATURATION',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_SHARPNESS',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_BACKGROUNDMOVE_LEVEL',this.id);
				break;
			}
			case COPY_PARAM_COPYMODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_COLLATEMODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_QUALITYTYPE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_WATERMARK:
			{

				ret += copy_jsc_removeParameter(prop_key+'_MODE',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_STR',this.id);
				break;
			}
			case COPY_PARAM_COPIES:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_AUTOCORRECTIONMODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_ARRANGEMENTMODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_NUPMODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_CLONEMODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PARAM_POSTER:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COLOR_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_SCAN_SOURCE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_IMAGE_ORIENTATION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COLOR_BALANCE:
			{

				ret += copy_jsc_removeParameter(prop_key+'_C',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_M',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_Y',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_K',this.id);
				break;
			}
			case COPY_HORIZONTAL_MARGIN:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_VERTICAL_MARGIN:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_EDGE_TO_EDGE_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_SEPARATOR:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_NUP_COMBINATION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_BOOKLET_DUPLEX:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_MIRROR_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_HEADER_ENABLE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_HEADER_POSITION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_HEADER_PAGINATION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_HEADER_TEXT_TYPE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_HEADER_TEXT:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_FOOTER_ENABLE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_FOOTER_POSITION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_FOOTER_PAGINATION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_FOOTER_TEXT_TYPE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PAGE_FOOTER_TEXT:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_BACKUP_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_SAMPLE_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_USE_COLOR_INVERSION:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_USE_COPY_SCAN_MEANTIME:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_USE_REMOVE_COLOR:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_USE_EDGE_CLEAN:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_FILTER_EDGE_MARGIN:
			{

				ret += copy_jsc_removeParameter(prop_key+'_UP',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_DOWN',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_LEFT',this.id);
				ret += copy_jsc_removeParameter(prop_key+'_RIGHT',this.id);
				break;
			}
			case COPY_ORIGINAL_FLIP:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COPIES_FLIP:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COVER:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COVER_BACK:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COVER_TRAY_IN:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_STAPLE_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_PUNCH_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_FOLD_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_TRAY_RECEIVE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_SHIFT_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_FLOD_NUMBER:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_HAVE_STAPLER:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_COVER_PAPER_TYPE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_SEPARATOR_TRAY_IN:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_USER_DEFINE_SCAN_PAPER_PARAM:
			{

				ret += copy_jsc_removeParameter(prop_key+'W',this.id);
				ret += copy_jsc_removeParameter(prop_key+'H',this.id);
				break;
			}
			case COPY_USER_DEFINE_PRINT_PAPER_PARAM:
			{

				ret += copy_jsc_removeParameter(prop_key+'W',this.id);
				ret += copy_jsc_removeParameter(prop_key+'H',this.id);
				break;
			}
			case COPY_PAGE_TYPE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_SKEWING_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_TONER_MODE:
			{

				ret += copy_jsc_removeParameter(prop_key,this.id);
				break;
			}
			case COPY_RETENTION:
			{
				console.log("set_retention_param clear ");
				ret += copy_jsc_set_retention_param(0,1,"url-url","headers");
				break;
			}
			default :
			ret = 1;
			break;

		}

		if(ret == 0)
		{
			return true;
		}
		else
		{
			return false;
		}

	}


}

function is_outof_range( value, min, max )
{
    if( value < min || value > max )
    {
        return true;
    }

    return false;
}



/**
 * 原稿尺寸类(纸型对应的值请参照mediasize.js)
 * @class
 */
class InputPaperSize
{
    /**
     * 构造函数
     * @since V1.0
     * @param {Number} size 原稿尺寸
     *
     * | 纸型 | Kanas | 4020 |
     * |:--:|:--:|:--:|
     * | A3 | 支持 | 不支持 |
     * | A4 | 支持 | 支持 |
     * | A4 LEF | 支持 | 不支持 |
     * | A5 | 支持 | 支持 |
     * | A5 LEF | 支持 | 不支持 |
     * | A6 | 支持 | 支持 |
     * | 8K | 支持 | 不支持 |
     * | Big 16K | 支持 | 不支持 |
     * | Big 16K LEF | 支持 | 不支持 |
     * | JIS B4 | 支持 | 不支持 |
     * | JIS B5 | 支持 | 支持 |
     * | JIS B6 | 支持 | 不支持 |
     * | Statement | 支持 | 不支持 |
     * | Letter | 支持 | 支持 |
     * | Legal | 支持 | 支持 |
     * | Postcard | 不支持 | 不支持 |
     * | Folio | 不支持 | 支持 |
     * | Ledger | 支持 | 不支持 |
     * | SCAN_FULL_PLATEN | 支持 | 支持 |
     * | Executive | 不支持 | 不支持 |
     * | ISO B5 | 不支持 | 不支持 |
     * | User Define | 支持 | 不支持 |
     * | AUTO_PAPER_CHECK | 支持 | 不支持 |
     *
     * @see class/pedk/common/mediasize.js~MediaSize.html
     * @see class/pedk/common/mediasize.js~SpecialScanSize.html
     */
    constructor( size )
    {
        if(typeof size !== 'string')
        {
            console.log("The parmeter type error")
        }

		this.val = size ;
		console.log( 'constructor InputPaperSize = ' + this.val +'\n');
    }

    /**
     * 设置原稿尺寸
     * @since V1.0
     * @param {Number} size 原稿尺寸
     * @returns {String} 执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    setPaperSize( size )
    {
        if(typeof size !== 'string')
        {
            console.log("The parmeter type error")
			return 'EINVALIDPARAM'
        }
		this.val = size ;
        return 'EXIT_SUCCESS'
    }

    /**
     * 取得原稿尺寸
     * @since V1.0
     * @returns {Number|String} 原稿尺寸|执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    getPaperSize()
    {
        return this.val;
    }

    /**
     * 设置X方向纸张尺寸
     * @since V1.0
     * @param {Number} x_size X方向纸张尺寸
     * @returns {String} 执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    setXSize( x_size )
    {
        return 0;
    }

    /**
     * 设置X方向纸张尺寸
     * @since V1.0
     * @returns {Number|String} x_size X方向纸张尺寸|执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    getXSize()
    {
        return 0;
    }

    /**
     * 设置Y方向纸张尺寸
     * @since V1.0
     * @param {Number} y_size y方向纸张尺寸
     * @returns {String} 执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    setYSize( y_size )
    {
        return 0;
    }

    /**
     * 设置Y方向纸张尺寸
     * @since V1.0
     * @returns {Number|String} y_size Y方向纸张尺寸|执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    getYSize()
    {
        return 0;
    }
}

/**
 * 出纸纸盒类
 * @class
 */
class OutputTray
{
    /**
     * 构造函数
     * @since V1.0
     * @param {Number} tray   出纸纸盒
     * @param {Number} size  纸张尺寸(纸型对应的值请参照mediasize.js)
     *
     * | 纸盒类型 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | 自动选择纸盒 | 0 | 支持 | 支持 |
     * | 多功能纸盒 | 1 | 支持 | 支持 |
     * | 标准纸盒 | 2 | 支持 | 支持 |
     * | 选配纸盒1 | 3 | 支持 | 支持 |
     * | 选配纸盒2 | 4 | 支持 | 支持 |
     * | 选配纸盒3 | 5 | 支持 | 不支持 |
     * | 内置大容量纸盒 | 6 | 支持 | 不支持 |
     * | 外置大容量纸盒 | 7 | 支持 | 不支持 |
     * <br>
     * | 纸张类型 | Kanas | 4020 |
     * |:--:|:--:|:--:|
     * | A3 | 支持 | 不支持 |
     * | A4 | 支持 | 支持 |
     * | A4 LEF | 支持 | 不支持 |
     * | A5 | 支持 | 支持 |
     * | A5 LEF | 支持 | 支持 |
     * | A6 | 支持 | 支持 |
     * | Big 8K | 支持 | 不支持 |
     * | Big 16K | 支持 | 支持 |
     * | Big 16K LEF | 支持 | 不支持 |
     * | 16K | 不支持 | 支持 |
     * | 32K | 不支持 | 支持 |
     * | Big 32K | 不支持 | 支持 |
     * | JIS B4 | 支持 | 不支持 |
     * | JIS B5 | 支持 | 支持 |
     * | JIS B6 | 支持 | 不支持 |
     * | Env B6 | 不支持 | 支持 |
     * | B6 | 不支持 | 支持 |
     * | ISO B5 | 不支持 | 支持 |
     * | FoolScaps | 支持 | 不支持 |
     * | Ledger | 支持 | 不支持 |
     * | Letter | 支持 | 支持 |
     * | Letter LEF | 支持 | 不支持 |
     * |Invoice | 支持 | 不支持 |
     * |Invoice LEF | 支持 | 不支持 |
     * | Executive | 支持 | 支持 |
     * | Executive LEF | 支持 | 不支持 |
     * | A3 Wide | 支持 | 不支持 |
     * | User Define | 支持 | 支持 |
     * | Legal13 | 不支持 | 支持 |
     * | Legal14 | 不支持 | 支持 |
     * | No.10 Env | 不支持 | 支持 |
     * | Monarch Env | 不支持 | 支持 |
     * | DL Env（5号） | 不支持 | 支持 |
     * | C5 Env（7号） | 不支持 | 支持 |
     * | Youkei Size4 | 不支持 | 支持 |
     * | Japanese Postcard | 不支持 | 支持 |
     * | Choukei Size3 | 不支持 | 支持 |
     * | Folio | 不支持 | 支持 |
     * | Oficio | 不支持 | 支持 |
     * | Statement | 不支持 | 支持 |
     * | C6 Env | 不支持 | 支持 |
     * | ZL（6号） | 不支持 | 支持 |
     * | Postcard | 不支持 | 支持 |
     * | Yougata2 | 不支持 | 支持 |
     * | Nagagata3 | 不支持 | 支持 |
     * | Younaga3 | 不支持 | 支持 |
     * | Yougata4 | 不支持 | 支持 |
     *
     * @see class/pedk/common/mediasize.js~MediaSize.html
     * @see class/pedk/common/mediasize.js~SpecialScanSize.html
     */
    constructor( tray, size )
    {
        if(typeof tray !== 'string')
        {
            console.log("The parmeter type error")

        }
        if(typeof size !== 'string')
        {
            console.log("The parmeter type error")

        }


		this.tray = tray;
		this.size     = size;

    }

    /**
     * 设置出纸纸盒
     * @since V1.0
     * @param {Number} tray  出纸纸盒
     * @returns {String} 执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    setTray( tray )
    {
        if(typeof tray !== 'string')
        {
            console.log("The parmeter type error")
			return 'EINVALIDPARAM'
        }
		this.tray = tray;
        return 'EXIT_SUCCESS'
    }

    /**
     * 获取出纸纸盒
     * @since V1.0
     * @returns {Number|String} 出纸纸盒|执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    getTray()
    {
        return this.tray;
    }

    /**
     * 设置纸张尺寸
     * @since V1.0
     * @param {Number} size  纸张尺寸
     * @returns {String} 执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    setPaperSize( size )
    {
        if(typeof size !== 'string')
        {
            console.log("The parmeter type error")
			return 'EINVALIDPARAM'
        }

		this.size     = size;
        return 'EXIT_SUCCESS'
    }

    /**
     * 获取纸张尺寸
     * @since V1.0
     * @returns {Number|String} 纸张尺寸|执行结果
     * @see class/pedk/common/errorno.js~ErrorNo.html
     */
    getPaperSize()
    {
        return this.size;
    }
}

/**
 * 份数
 * @class
 */
class Copies
{
    constructor( val )
    {
        if( is_outof_range( val, 1, 9999 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }
        this.val = val;
    }


    setCopies( val )
    {
        if( is_outof_range( val, 1, 9999 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = val;
        return 'EXIT_SUCCESS'
    }

    getCopies()
    {
        return	this.val;
    }

}

///////////////////////////////////////////////////////
class ArrangementMode
{
    constructor( value )
    {
        if( is_outof_range( value, 1, 4 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setArrangementMode( value )
    {

        if( is_outof_range( value, 1, 4 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getArrangementMode()
    {
        return	this.val;
    }
}



///////////////////////////////////////////////////////
class AutoCorrectionMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setAutoCorrectionMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getAutoCorrectionMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class BackupMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 4 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setBackupMode( value )
    {

        if( is_outof_range( value, 0, 4 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getBackupMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class BookletDuplex
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setBookletDuplex( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getBookletDuplex()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CloneMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCloneMode( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCloneMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CollateMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCollateMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCollateMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class ColorBalance
{
    constructor( color, value )
    {
        if( is_outof_range( color, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        if( is_outof_range( value, 1, 5 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

		this.C = 1;
		this.M = 1;
		this.Y = 1;
		this.K = 1;
		if(color == 0)
		{
			this.C = value;
		}
		else if(color == 1)
		{
			this.M = value;
		}
		else if(color == 2)
		{
			this.Y = value;
		}
		else if(color == 3)
		{
			this.K = value;
		}
    }

    setColorBalance( color, value )
    {
        if( is_outof_range( color, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }

        if( is_outof_range( value, 1, 5 ) )
        {
            return 'EINVALIDPARAM'
        }
		if(color == 0)
		{
			this.C = value;
		}
		else if(color == 1)
		{
			this.M = value;
		}
		else if(color == 2)
		{
			this.Y = value;
		}
		else if(color == 3)
		{
			this.K = value;
		}
        return 'EXIT_SUCCESS'
    }

    getColorBalance( color )
    {
		if(color == 0)
		{
			return this.C;
		}
		else if(color == 1)
		{
			return this.M;
		}
		else if(color == 2)
		{
			return this.Y;
		}
		else if(color == 3)
		{
			return this.K;
		}
    }
}


///////////////////////////////////////////////////////
class ColorInversion
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setColorInversion( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getColorInversion()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class ColorMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setColorMode( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getColorMode()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class CopiesFlip
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCopiesFlip( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCopiesFlip()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class OriginalFlip
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setOriginalFlip( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getOriginalFlip()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CopyMode
{
    constructor( value )
    {
        if( is_outof_range( value, 1, 4 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCopyMode( value )
    {

        if( is_outof_range( value, 1, 4 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCopyMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CopyScanMeantime
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCopyScanMeantime( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCopyScanMeantime()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CopyScanSource
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 4 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCopyScanSource( value )
    {

        if( is_outof_range( value, 0, 4 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCopyScanSource()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class Cover
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCover( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCover()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CoverBack
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setCoverBack( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCoverBack()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class CoverPaperType
{
    constructor( value )
    {
		if(typeof value !== 'string')
		{
			console.log("The parmeter type error")
		}


        this.val = value;
    }

    setCoverPaperType( value )
    {

		if(typeof value !== 'string')
		{
			console.log("The parmeter type error")
			return 'EINVALIDPARAM'
		}

        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCoverPaperType()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class CoverTrayIn
{
    constructor( value )
    {
        if(typeof value !== 'string')
        {
            console.log("The parmeter type error")
			return 'EINVALIDPARAM'
        }

        this.val = value;
    }

    setCoverTrayIn( value )
    {

		if(typeof value !== 'string')
		{
			console.log("The parmeter type error")
			return 'EINVALIDPARAM'
		}

        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getCoverTrayIn()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class EdgeClean
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setEdgeClean( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getEdgeClean()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class EdgeMargin
{
    constructor( dir, value )
    {
        if( is_outof_range( dir, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        if( is_outof_range( value, 0, 10000 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

		this.up 	= 1;
		this.down 	= 1;
		this.left 	= 1;
		this.right 	= 1;
		if(dir == 0)
		{
			this.up = value;
		}
		else if(dir == 1)
		{
			this.down = value;
		}
		else if(dir == 2)
		{
			this.left = value;
		}
		else if(dir == 3)
		{
			this.right = value;
		}
    }

    setEdgeMargin( dir, value )
    {
        if( is_outof_range( dir, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }

        if( is_outof_range( value, 0, 10000 ) )
        {
            return 'EINVALIDPARAM'
        }
		if(dir == 0)
		{
			this.up = value;
		}
		else if(dir == 1)
		{
			this.down = value;
		}
		else if(dir == 2)
		{
			this.left = value;
		}
		else if(dir == 3)
		{
			this.right = value;
		}
        return 'EXIT_SUCCESS'
    }

    getEdgeMargin( dir)
    {
		if(dir == 0)
		{
			return this.up;
		}
		else if(dir == 1)
		{
			return this.down;
		}
		else if(dir == 2)
		{
			return this.left;
		}
		else if(dir == 3)
		{
			return this.right;
		}
    }
}


///////////////////////////////////////////////////////
class EdgeToEdgeMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setEdgeToEdgeMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getEdgeToEdgeMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class FoldMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setFoldMode( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getFoldMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class FoldNumber
{
    constructor( value )
    {
        if( is_outof_range( value, 1, 20 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setFoldNumber( value )
    {

        if( is_outof_range( value, 1, 20 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getFoldNumber()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class HaveStapler
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setHaveStapler( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getHaveStapler()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class HorizontalMargin
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 10000 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setHorizontalMargin( value )
    {

        if( is_outof_range( value, 0, 10000 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getHorizontalMargin()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class ImageColor
{
    constructor( mode, value )
    {
        if( is_outof_range( mode, 0, 5 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        if( is_outof_range( value, 1, 5 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

		this.brightness 	= 1;
		this.saturation 	= 1;
		this.contrast 		= 1;
		this.sharpness		= 1;
		this.hue 			= 1;
		this.backgroundmove_level = 1;
		if(mode == 0)
		{
			this.brightness = value;
		}
		else if(mode == 1)
		{
			this.saturation = value;
		}
		else if(mode == 2)
		{
			this.contrast = value;
		}
		else if(mode == 3)
		{
			this.sharpness = value;
		}
		else if(mode == 4)
		{
			this.hue = value;
		}
		else if(mode == 5)
		{
			this.backgroundmove_level = value;
		}
    }

    setImageColor( mode, value )
    {

        if( is_outof_range( mode, 0, 5 ) )
        {
            return 'EINVALIDPARAM'
        }
        if( is_outof_range( value, 1, 5 ) )
        {
            return 'EINVALIDPARAM'
        }
		if(mode == 0)
		{
			this.brightness = value;
		}
		else if(mode == 1)
		{
			this.saturation = value;
		}
		else if(mode == 2)
		{
			this.contrast = value;
		}
		else if(mode == 3)
		{
			this.sharpness = value;
		}
		else if(mode == 4)
		{
			this.hue = value;
		}
		else if(mode == 5)
		{
			this.backgroundmove_level = value;
		}
        return 'EXIT_SUCCESS'
    }

    getImageColor(mode)
    {
		if(mode == 0)
		{
			return this.brightness;
		}
		else if(mode == 1)
		{
			return this.saturation;
		}
		else if(mode == 2)
		{
			return this.contrast;
		}
		else if(mode == 3)
		{
			return this.sharpness;
		}
		else if(mode == 4)
		{
			return this.hue;
		}
		else if(mode == 5)
		{
			return this.backgroundmove_level;
		}
        return this.val;
    }
}



///////////////////////////////////////////////////////
class ImageOrientation
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setImageOrientation( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getImageOrientation()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class MirrorMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setMirrorMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getMirrorMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class NupCombination
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setNupCombination( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getNupCombination()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class NupMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setNupMode( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getNupMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PageType
{
    constructor( value )
    {
        if(typeof value !== 'string')
        {
            console.log("The parmeter type error")
        }


        this.val = value;
    }

    setPageType( value )
    {

		if(typeof value !== 'string')
		{
			console.log("The parmeter type error")
			return 'EINVALIDPARAM'
		}

        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPageType()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperFooterEnable
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperFooterEnable( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperFooterEnable()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperFooterPagination
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperFooterPagination( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperFooterPagination()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperFooterPosition
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperFooterPosition( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperFooterPosition()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class PaperFooterText
{
    constructor( value )
    {
        this.val = value;
    }

    setPaperFooterText( value )
    {
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperFooterText()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperFooterTextType
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 10 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperFooterTextType( value )
    {

        if( is_outof_range( value, 0, 10 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperFooterTextType()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperHeaderEnable
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperHeaderEnable( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperHeaderEnable()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperHeaderPagination
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperHeaderPagination( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperHeaderPagination()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperHeaderPosition
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperHeaderPosition( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperHeaderPosition()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperHeaderText
{
    constructor( value )
    {

        this.val = value;
    }

    setPaperHeaderText( value )
    {
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperHeaderText()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class PaperHeaderTextType
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 10 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPaperHeaderTextType( value )
    {

        if( is_outof_range( value, 0, 10 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPaperHeaderTextType()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class PosterMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 22 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPosterMode( value )
    {

        if( is_outof_range( value, 0, 22 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPosterMode()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class PunchMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 2 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setPunchMode( value )
    {

        if( is_outof_range( value, 0, 2 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getPunchMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class QualityType
{
    constructor( value )
    {
        if( is_outof_range( value, 1, 4 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setQualityTypeMode( value )
    {

        if( is_outof_range( value, 1, 4 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getQualityTypeMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class RemoveColor
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 7 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setRemoveColor( value )
    {

        if( is_outof_range( value, 0, 7 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getRemoveColor()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class SampleCopyMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setSampleCopyMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getSampleCopyMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class SaveTonerMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setSaveTonerMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getSaveTonerMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class Separator
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setSeparator( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getSeparator()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class SeparatorTrayIn
{
    constructor( value )
    {
        if(typeof value !== 'string')
        {
            console.log("The parmeter type error")
        }


        this.val = value;
    }

    setSeparatorTrayIn( value )
    {

		if(typeof value !== 'string')
		{
			console.log("The parmeter type error")
			return 'EINVALIDPARAM'
		}

        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getSeparatorTrayIn()
    {
        return this.val;
    }
}




///////////////////////////////////////////////////////
class ShiftMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setShiftMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getShiftMode()
    {
        return this.val;
    }
}



///////////////////////////////////////////////////////
class SkewingMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setSkewingMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getSkewingMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class StapleMode
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 12 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setStapleMode( value )
    {

        if( is_outof_range( value, 0, 12 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getStapleMode()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class TrayReceive
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 3 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setTrayReceive( value )
    {

        if( is_outof_range( value, 0, 3 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getTrayReceive()
    {
        return this.val;
    }
}


///////////////////////////////////////////////////////
class UserDefinePrintPaperParam
{
    constructor(w_or_h, value )
    {
        if( is_outof_range( w_or_h, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }
        if( is_outof_range( value, 0, 10000 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

		if(w_or_h == 0)
		{
			this.w = value;
		}
		else
		{
			this.h = value;
		}
	}

    setPrintPaperParam(w_or_h, value )
    {

        if( is_outof_range( w_or_h, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        if( is_outof_range( value, 0, 10000 ) )
        {
            return 'EINVALIDPARAM'
        }

		if(w_or_h == 0)
		{
			this.w = value;
		}
		else
		{
			this.h = value;
		}
        return 'EXIT_SUCCESS'
    }

    getPrintPaperParam(w_or_h)
    {
		if(w_or_h == 0)
		{
			return this.w;
		}
		else
		{
			return this.h;
		}
	}
}


///////////////////////////////////////////////////////
class UserDefineScanPaperParam
{
    constructor(w_or_h, value )
    {
        if( is_outof_range( w_or_h, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }
        if( is_outof_range( value, 0, 10000 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

		if(w_or_h == 0)
		{
			this.w = value;
		}
		else
		{
			this.h = value;
		}
    }

    setScanPaperParam(w_or_h, value )
    {

        if( is_outof_range( w_or_h, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        if( is_outof_range( value, 0, 10000 ) )
        {
            return 'EINVALIDPARAM'
        }
		if(w_or_h == 0)
		{
			this.w = value;
		}
		else
		{
			this.h = value;
		}
        return 'EXIT_SUCCESS'
    }

    getScanPaperParam(w_or_h)
    {
		if(w_or_h == 0)
		{
			return this.w;
		}
		else
		{
			return this.h;
		}
	}
}



///////////////////////////////////////////////////////
class VerticalMargin
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 10000 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setVerticalMargin( value )
    {

        if( is_outof_range( value, 0, 10000 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getVerticalMargin()
    {
        return this.val;
    }
}

///////////////////////////////////////////////////////
class Watermark
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 1 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setWatermarkMode( value )
    {

        if( is_outof_range( value, 0, 1 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getWatermarkMode()
    {
        return this.val;
    }


    setWatermark( str )
    {
        this.water_str = str;
        return 'EXIT_SUCCESS'
    }

    getWatermark()
    {
        return this.water_str;
    }



}


///////////////////////////////////////////////////////
class Zoom
{
    constructor( value )
    {
        if( is_outof_range( value, 0, 400 ) )
        {
            throw RangeError( "Parmeter out of range !" )
        }

        this.val = value;
    }

    setZoom( value )
    {

        if( is_outof_range( value, 0, 400 ) )
        {
            return 'EINVALIDPARAM'
        }
        this.val = value;
        return 'EXIT_SUCCESS'
    }

    getZoom()
    {
        return this.val;
    }
}



/**
 * 作业状态变化监听类
 * @class
 */
class JobStateListener
{
    /**
     * 构造函数
     * @since V1.0
     */
    constructor() { }

    /**
     * 作业状态通知
     * @since V1.0
     * @param {String} state 作业状态
     * <ul>
     * <li>JBSts_Init</li>
     * <li>JBSts_PrePare</li>
     * <li>JBSts_Ready</li>
     * <li>JBSts_Running</li>
     * <li>JBSts_Suspend</li>
     * <li>JBSts_Pause</li>
     * <li>JBSts_Cancelling</li>
     * <li>JBSts_Aborting</li>
     * <li>JBSts_Finish</li>
     * <li>JBSts_History</li>
     * </ul>
     */
    notify(state)
    {
        console.log("JobStateListener")
        if(typeof state !== 'string')
        {
            throw TypeError('Must be only one string parmeter')
        }
    }

}
globalThis.pedk.jobs.copy.JobStateListener                      =  JobStateListener;
globalThis.pedk.jobs.copy.ArrangementMode                       =  ArrangementMode;
globalThis.pedk.jobs.copy.AutoCorrectionMode                    =  AutoCorrectionMode;
globalThis.pedk.jobs.copy.BackupMode                            =  BackupMode;
globalThis.pedk.jobs.copy.BookletDuplex                         =  BookletDuplex;
globalThis.pedk.jobs.copy.CloneMode                             =  CloneMode;
globalThis.pedk.jobs.copy.CollateMode                           =  CollateMode;
globalThis.pedk.jobs.copy.ColorBalance                          =  ColorBalance;
globalThis.pedk.jobs.copy.ColorInversion                        =  ColorInversion;
globalThis.pedk.jobs.copy.ColorMode                             =  ColorMode;
globalThis.pedk.jobs.copy.Copies                                =  Copies;
globalThis.pedk.jobs.copy.CopiesFlip                            =  CopiesFlip;
globalThis.pedk.jobs.copy.OriginalFlip                          =  OriginalFlip;
globalThis.pedk.jobs.copy.CopyJob                               =  CopyJob;
globalThis.pedk.jobs.copy.CopyMode                              =  CopyMode;
globalThis.pedk.jobs.copy.CopyParameterSet                      =  CopyParameterSet;
globalThis.pedk.jobs.copy.CopyScanMeantime                      =  CopyScanMeantime;
globalThis.pedk.jobs.copy.CopyScanSource                        =  CopyScanSource;
globalThis.pedk.jobs.copy.Cover                                 =  Cover;
globalThis.pedk.jobs.copy.CoverBack                             =  CoverBack;
globalThis.pedk.jobs.copy.CoverPaperType                        =  CoverPaperType;
globalThis.pedk.jobs.copy.CoverTrayIn                           =  CoverTrayIn;
globalThis.pedk.jobs.copy.EdgeClean                             =  EdgeClean;
globalThis.pedk.jobs.copy.EdgeMargin                            =  EdgeMargin;
globalThis.pedk.jobs.copy.EdgeToEdgeMode                        =  EdgeToEdgeMode;
globalThis.pedk.jobs.copy.FoldMode                              =  FoldMode;
globalThis.pedk.jobs.copy.FoldNumber                            =  FoldNumber;
globalThis.pedk.jobs.copy.HaveStapler                           =  HaveStapler;
globalThis.pedk.jobs.copy.HorizontalMargin                      =  HorizontalMargin;
globalThis.pedk.jobs.copy.ImageColor                            =  ImageColor;
globalThis.pedk.jobs.copy.ImageOrientation                      =  ImageOrientation;
globalThis.pedk.jobs.copy.InputPaperSize                        =  InputPaperSize;
globalThis.pedk.jobs.copy.MirrorMode                            =  MirrorMode;
globalThis.pedk.jobs.copy.NupCombination                        =  NupCombination;
globalThis.pedk.jobs.copy.NupMode                               =  NupMode;
globalThis.pedk.jobs.copy.OutputTray                            =  OutputTray;
globalThis.pedk.jobs.copy.PageType                              =  PageType;
globalThis.pedk.jobs.copy.PaperFooterEnable                     =  PaperFooterEnable;
globalThis.pedk.jobs.copy.PaperFooterPagination                 =  PaperFooterPagination;
globalThis.pedk.jobs.copy.PaperFooterPosition                   =  PaperFooterPosition;
globalThis.pedk.jobs.copy.PaperFooterText                       =  PaperFooterText;
globalThis.pedk.jobs.copy.PaperFooterTextType                   =  PaperFooterTextType;
globalThis.pedk.jobs.copy.PaperHeaderEnable                     =  PaperHeaderEnable;
globalThis.pedk.jobs.copy.PaperHeaderPagination                 =  PaperHeaderPagination;
globalThis.pedk.jobs.copy.PaperHeaderPosition                   =  PaperHeaderPosition;
globalThis.pedk.jobs.copy.PaperHeaderText                       =  PaperHeaderText;
globalThis.pedk.jobs.copy.PaperHeaderTextType                   =  PaperHeaderTextType;
globalThis.pedk.jobs.copy.PosterMode                            =  PosterMode;
globalThis.pedk.jobs.copy.PunchMode                             =  PunchMode;
globalThis.pedk.jobs.copy.QualityType                           =  QualityType;
globalThis.pedk.jobs.copy.RemoveColor                           =  RemoveColor;
globalThis.pedk.jobs.copy.SampleCopyMode                        =  SampleCopyMode;
globalThis.pedk.jobs.copy.SaveTonerMode                         =  SaveTonerMode;
globalThis.pedk.jobs.copy.Separator                             =  Separator;
globalThis.pedk.jobs.copy.SeparatorTrayIn                       =  SeparatorTrayIn;
globalThis.pedk.jobs.copy.ShiftMode                             =  ShiftMode;
globalThis.pedk.jobs.copy.SkewingMode                           =  SkewingMode;
globalThis.pedk.jobs.copy.StapleMode                            =  StapleMode;
globalThis.pedk.jobs.copy.TrayReceive                           =  TrayReceive;
globalThis.pedk.jobs.copy.UserDefinePrintPaperParam             =  UserDefinePrintPaperParam;
globalThis.pedk.jobs.copy.UserDefineScanPaperParam              =  UserDefineScanPaperParam;
globalThis.pedk.jobs.copy.VerticalMargin                        =  VerticalMargin;
globalThis.pedk.jobs.copy.Watermark                             =  Watermark;
globalThis.pedk.jobs.copy.Zoom                                  =  Zoom;


/**
 * @const {String} 参数:原稿尺寸
 */
globalThis.pedk.jobs.copy.COPY_PARAM_INPUTPAPERSIZE = 'COPY_PARAM_INPUTPAPERSIZE';
/**
 * @const {String} 参数:出纸纸盒
 */
globalThis.pedk.jobs.copy.COPY_PARAM_OUTPUTTRAY = 'COPY_PARAM_OUTPUTTRAY';
/**
 * @const {String} 参数:缩放率
 */
globalThis.pedk.jobs.copy.COPY_PARAM_ZOOM = 'COPY_PARAM_ZOOM';
/**
 * @const {String} 参数:画像色彩
 */
globalThis.pedk.jobs.copy.COPY_PARAM_IMAGE_COLOR = 'COPY_PARAM_IMAGE_COLOR';
/**
 * @const {String} 参数:双面复印模式
 */
globalThis.pedk.jobs.copy.COPY_PARAM_COPYMODE = 'COPY_PARAM_COPYMODE';
/**
 * @const {String} 参数:逐份模式
 */
globalThis.pedk.jobs.copy.COPY_PARAM_COLLATEMODE = 'COPY_PARAM_COLLATEMODE';
/**
 * @const {String} 参数:复印画质
 */
globalThis.pedk.jobs.copy.COPY_PARAM_QUALITYTYPE = 'COPY_PARAM_QUALITYTYPE';
/**
 * @const {String} 参数:水印内容
 */
globalThis.pedk.jobs.copy.COPY_PARAM_WATERMARK = 'COPY_PARAM_WATERMARK';
/**
 * @const {String} 参数:份数
 */
globalThis.pedk.jobs.copy.COPY_PARAM_COPIES = 'COPY_PARAM_COPIES';
/**
 * @const {String} 参数:自动纠偏
 */
globalThis.pedk.jobs.copy.COPY_PARAM_AUTOCORRECTIONMODE = 'COPY_PARAM_AUTOCORRECTIONMODE';
/**
 * @const {String} 参数:排列方式
 */
globalThis.pedk.jobs.copy.COPY_PARAM_ARRANGEMENTMODE = 'COPY_PARAM_ARRANGEMENTMODE';
/**
 * @const {String} 参数:多页合一
 */
globalThis.pedk.jobs.copy.COPY_PARAM_NUPMODE = 'COPY_PARAM_NUPMODE';
/**
 * @const {String} 参数:克隆
 */
globalThis.pedk.jobs.copy.COPY_PARAM_CLONEMODE = 'COPY_PARAM_CLONEMODE';
/**
 * @const {String} 参数:海报
 */
globalThis.pedk.jobs.copy.COPY_PARAM_POSTER = 'COPY_PARAM_POSTER';
/**
 * @const {String} 参数:色彩模式
 */
globalThis.pedk.jobs.copy.COPY_COLOR_MODE = 'COPY_COLOR_MODE';
/**
 * @const {String} 参数:复印扫描纸张来源
 */
globalThis.pedk.jobs.copy.COPY_SCAN_SOURCE = 'COPY_SCAN_SOURCE';
/**
 * @const {String} 参数:复印画像方向
 */
globalThis.pedk.jobs.copy.COPY_IMAGE_ORIENTATION = 'COPY_IMAGE_ORIENTATION';
/**
 * @const {String} 参数:复印色彩平衡
 */
globalThis.pedk.jobs.copy.COPY_COLOR_BALANCE = 'COPY_COLOR_BALANCE';
/**
 * @const {String} 参数:复印水平边距
 */
globalThis.pedk.jobs.copy.COPY_HORIZONTAL_MARGIN = 'COPY_HORIZONTAL_MARGIN';
/**
 * @const {String} 参数:复印垂直边距
 */
globalThis.pedk.jobs.copy.COPY_VERTICAL_MARGIN = 'COPY_VERTICAL_MARGIN';
/**
 * @const {String} 参数:复印边对边模式
 */
globalThis.pedk.jobs.copy.COPY_EDGE_TO_EDGE_MODE = 'COPY_EDGE_TO_EDGE_MODE';
/**
 * @const {String} 参数:复印分隔页
 */
globalThis.pedk.jobs.copy.COPY_SEPARATOR = 'COPY_SEPARATOR';
/**
 * @const {String} 参数:复印多合一复印合并模式
 */
globalThis.pedk.jobs.copy.COPY_NUP_COMBINATION = 'COPY_NUP_COMBINATION';
/**
 * @const {String} 参数:复印双面小册子模式
 */
globalThis.pedk.jobs.copy.COPY_BOOKLET_DUPLEX = 'COPY_BOOKLET_DUPLEX';
/**
 * @const {String} 参数:复印镜像模式
 */
globalThis.pedk.jobs.copy.COPY_MIRROR_MODE = 'COPY_MIRROR_MODE';
/**
 * @const {String} 参数:复印页眉使能
 */
globalThis.pedk.jobs.copy.COPY_PAGE_HEADER_ENABLE = 'COPY_PAGE_HEADER_ENABLE';
/**
 * @const {String} 参数:复印页眉使能
 */
globalThis.pedk.jobs.copy.COPY_PAGE_HEADER_POSITION = 'COPY_PAGE_HEADER_POSITION';
/**
 * @const {String} 参数:复印页眉页码模式
 */
globalThis.pedk.jobs.copy.COPY_PAGE_HEADER_PAGINATION = 'COPY_PAGE_HEADER_PAGINATION';
/**
 * @const {String} 参数:复印页眉内容类型
 */
globalThis.pedk.jobs.copy.COPY_PAGE_HEADER_TEXT_TYPE = 'COPY_PAGE_HEADER_TEXT_TYPE';
/**
 * @const {String} 参数:复印页眉自定义内容
 */
globalThis.pedk.jobs.copy.COPY_PAGE_HEADER_TEXT = 'COPY_PAGE_HEADER_TEXT';
/**
 * @const {String} 参数:复印页脚使能
 */
globalThis.pedk.jobs.copy.COPY_PAGE_FOOTER_ENABLE = 'COPY_PAGE_FOOTER_ENABLE';
/**
 * @const {String} 参数:复印页脚使能
 */
globalThis.pedk.jobs.copy.COPY_PAGE_FOOTER_POSITION = 'COPY_PAGE_FOOTER_POSITION';
/**
 * @const {String} 参数:复印页脚页码模式
 */
globalThis.pedk.jobs.copy.COPY_PAGE_FOOTER_PAGINATION = 'COPY_PAGE_FOOTER_PAGINATION';
/**
 * @const {String} 参数:复印页脚内容类型
 */
globalThis.pedk.jobs.copy.COPY_PAGE_FOOTER_TEXT_TYPE = 'COPY_PAGE_FOOTER_TEXT_TYPE';
/**
 * @const {String} 参数:复印页脚自定义内容
 */
globalThis.pedk.jobs.copy.COPY_PAGE_FOOTER_TEXT = 'COPY_PAGE_FOOTER_TEXT';
/**
 * @const {String} 参数:复印备份模式
 */
globalThis.pedk.jobs.copy.COPY_BACKUP_MODE = 'COPY_BACKUP_MODE';
/**
 * @const {String} 参数:样本复印模式
 */
globalThis.pedk.jobs.copy.COPY_SAMPLE_MODE = 'COPY_SAMPLE_MODE';
/**
 * @const {String} 参数:颜色反显模式
 */
globalThis.pedk.jobs.copy.COPY_USE_COLOR_INVERSION = 'COPY_USE_COLOR_INVERSION';
/**
 * @const {String} 参数:扫描时复印模式(同一个作业，复印的时候同时扫描到)
 */
globalThis.pedk.jobs.copy.COPY_USE_COPY_SCAN_MEANTIME = 'COPY_USE_COPY_SCAN_MEANTIME';
/**
 * @const {String} 参数:颜色滤除模式
 */
globalThis.pedk.jobs.copy.COPY_USE_REMOVE_COLOR = 'COPY_USE_REMOVE_COLOR';
/**
 * @const {String} 参数:边缘消除模式
 */
globalThis.pedk.jobs.copy.COPY_USE_EDGE_CLEAN = 'COPY_USE_EDGE_CLEAN';
/**
 * @const {String} 参数:边缘消除距离
 */
globalThis.pedk.jobs.copy.COPY_FILTER_EDGE_MARGIN = 'COPY_FILTER_EDGE_MARGIN';
/**
 * @const {String} 参数:原稿翻页方式
 */
globalThis.pedk.jobs.copy.COPY_ORIGINAL_FLIP = 'COPY_ORIGINAL_FLIP';
/**
 * @const {String} 参数:复印翻页方式
 */
globalThis.pedk.jobs.copy.COPY_COPIES_FLIP = 'COPY_COPIES_FLIP';
/**
 * @const {String} 参数:封面
 */
globalThis.pedk.jobs.copy.COPY_COVER = 'COPY_COVER';
/**
 * @const {String} 参数:封底
 */
globalThis.pedk.jobs.copy.COPY_COVER_BACK = 'COPY_COVER_BACK';
/**
 * @const {String} 参数:封面/底进纸盒
 */
globalThis.pedk.jobs.copy.COPY_COVER_TRAY_IN = 'COPY_COVER_TRAY_IN';
/**
 * @const {String} 参数:装订模式
 */
globalThis.pedk.jobs.copy.COPY_STAPLE_MODE = 'COPY_STAPLE_MODE';
/**
 * @const {String} 参数:打孔模式
 */
globalThis.pedk.jobs.copy.COPY_PUNCH_MODE = 'COPY_PUNCH_MODE';
/**
 * @const {String} 参数:折叠模式
 */
globalThis.pedk.jobs.copy.COPY_FOLD_MODE = 'COPY_FOLD_MODE';
/**
 * @const {String} 参数:接纸架
 */
globalThis.pedk.jobs.copy.COPY_TRAY_RECEIVE = 'COPY_TRAY_RECEIVE';
/**
 * @const {String} 参数:装订模式
 */
globalThis.pedk.jobs.copy.COPY_SHIFT_MODE = 'COPY_SHIFT_MODE';
/**
 * @const {String} 参数:折叠纸张数量
 */
globalThis.pedk.jobs.copy.COPY_FLOD_NUMBER = 'COPY_FLOD_NUMBER';
/**
 * @const {String} 参数:装订器
 */
globalThis.pedk.jobs.copy.COPY_HAVE_STAPLER = 'COPY_HAVE_STAPLER';
/**
 * @const {String} 参数:封面/封底纸张
 */
globalThis.pedk.jobs.copy.COPY_COVER_PAPER_TYPE = 'COPY_COVER_PAPER_TYPE';
/**
 * @const {String} 参数:分隔页进纸盒
 */
globalThis.pedk.jobs.copy.COPY_SEPARATOR_TRAY_IN = 'COPY_SEPARATOR_TRAY_IN';
/**
 * @const {String} 参数:用户自定义复印扫描纸张参数
 */
globalThis.pedk.jobs.copy.COPY_USER_DEFINE_SCAN_PAPER_PARAM = 'COPY_USER_DEFINE_SCAN_PAPER_PARAM';
/**
 * @const {String} 参数:用户自定义复印打印纸张参数
 */
globalThis.pedk.jobs.copy.COPY_USER_DEFINE_PRINT_PAPER_PARAM = 'COPY_USER_DEFINE_PRINT_PAPER_PARAM';
/**
 * @const {String} 参数:打印纸张介质
 */
globalThis.pedk.jobs.copy.COPY_PAGE_TYPE = 'COPY_PAGE_TYPE';
/**
 * @const {String} 参数:纠偏模式
 */
globalThis.pedk.jobs.copy.COPY_SKEWING_MODE = 'COPY_SKEWING_MODE';
/**
 * @const {String} 参数:省墨模式
 */
globalThis.pedk.jobs.copy.COPY_TONER_MODE = 'COPY_TONER_MODE';

globalThis.pedk.jobs.copy.COPY_RETENTION  = 'COPY_RETENTION';

globalThis.pedk.jobs.copy.COPY_NORMAL  = 'COPY_NORMAL';
globalThis.pedk.jobs.copy.COPY_ID_CARD  = 'COPY_ID_CARD';
globalThis.pedk.jobs.copy.COPY_BILL  = 'COPY_BILL';
globalThis.pedk.jobs.copy.COPY_CLONE  = 'COPY_CLONE';
globalThis.pedk.jobs.copy.COPY_POSTER  = 'COPY_POSTER';
globalThis.pedk.jobs.copy.COPY_NUP  = 'COPY_NUP';
globalThis.pedk.jobs.copy.COPY_BOOKLET  = 'COPY_BOOKLET';
globalThis.pedk.jobs.copy.COPY_WATERMARK  = 'COPY_WATERMARK';
globalThis.pedk.jobs.copy.COPY_SCALE  = 'COPY_SCALE';
globalThis.pedk.jobs.copy.COPY_MIX  = 'COPY_MIX';

const COLOR_MODE = {
  COLOR_MODE_BLACK_WHITE: "COLOR_MODE_BLACK_WHITE",
  COLOR_MODE_COLOR: "COLOR_MODE_COLOR",
  COLOR_MODE_RED_BLACK: "COLOR_MODE_RED_BLACK",
};

function test_func()
{

	console.log( 'js copy test main start  \n' );

	//threadInit();

	let copy_job                = new pedk.jobs.copy.CopyJob( 'COPY_NORMAL' );
	//console.log( 'getJobType =' + copy_job.getJobType() );

	let copy_param_1            = new pedk.jobs.copy.CopyParameterSet();
	let copy_param              = new pedk.jobs.copy.CopyParameterSet();
	let copy_input_size         = new pedk.jobs.copy.InputPaperSize( 2 );
	let copy_output             = new pedk.jobs.copy.OutputTray( 2, 2 );
	let copy_copies             = new pedk.jobs.copy.Copies( 2 );
	//let copy_ArrangementMode    = new pedk.jobs.copy.ArrangementMode( 1 );
	//let copy_ArrangementMode2 = new pedk.jobs.copy.ArrangementMode( 6);
	//let copy_AutoCorrectionMode = new pedk.jobs.copy.AutoCorrectionMode( true );
	//let copy_Cover              = new pedk.jobs.copy.Cover( 1 );
	//let copy_Collate            = new pedk.jobs.copy.CollateMode( 1 );


	//let copy_OriginalFlip       = new pedk.jobs.copy.OriginalFlip ( 1 );


	//console.log( 'copy_ArrangementMode.setArrangementMode( 2 ) ret = ' + copy_ArrangementMode.setArrangementMode( 2 ) );

	//console.log( 'copy_ArrangementMode.setArrangementMode( 5 ) ret = ' + copy_ArrangementMode.setArrangementMode( 5 ) );

	copy_copies.setCopies( 3 );
	//copy_Cover.setCover( 3 );



	copy_param.addParameter( 'COPY_PARAM_INPUTPAPERSIZE', copy_input_size );
	copy_param.addParameter( 'COPY_PARAM_OUTPUTTRAY', copy_output );
	copy_param.addParameter( 'COPY_PARAM_COPIES', copy_copies );
	//copy_param.addParameter( 'COPY_PARAM_AUTOCORRECTIONMODE', copy_ArrangementMode );

	//copy_param.addParameter( 'COPY_COVER', copy_Cover );

	//copy_param.addParameter( 'COPY_PARAM_COLLATEMODE', copy_Collate );
	//copy_param.addParameter( 'COPY_ORIGINAL_FLIP', copy_OriginalFlip );

	//console.log( 'ADF_PAGE_COUNT= ' + getADFScanCopyedPage() );
	//console.log( 'FB_PAGE_COUNT= '  + getFBScanCopyedPage() );


	copy_job.start( 1, copy_param , null );

}

//test_func();

