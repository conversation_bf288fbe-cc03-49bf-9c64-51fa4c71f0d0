/**************************************************************
Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name :  	POL (PANTUM OS LAYER)
file   name :	pol_string.c 
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-26
description	:   pol string  relative system interface header file 
****************************************************************/

#include <stdarg.h>

#include "pol/pol_types.h"
#include "pol/pol_string.h"
#include "pol_inner.h"

#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

size_t pi_strlen(const char *s)
{
    _pi_input_check(s);
    return strlen(s);    
}

char* pi_strncat(char *dest, const char *src, size_t n)
{
    _pi_input_check(dest&&src&&n&&((dest<src)||(dest>src+n))); 
    return  strncat(dest,src,n);

}

char* pi_strncpy(char *dest, const char *src, size_t n)
{

    _pi_input_check(dest&&src&&n&&((dest<src)||(dest>=(src+n)))); 
    return strncpy(dest,src,n);
}

int pi_strcmp(const char *s1, const char *s2)
{

    _pi_input_check(s1&&s2); 
    return strcmp(s1,s2);

}

int pi_strncmp(const char *s1, const char *s2, size_t n)
{
    _pi_input_check(s1&&s2&&n); 
    return strncmp(s1,s2,n);
}

int pi_strncasecmp(const char *s1, const char *s2, size_t n)
{
    _pi_input_check(s1&&s2&&n);
    return strncasecmp(s1, s2, n);
}

char* pi_strstr(const char *haystack, const char *needle)
{

    _pi_input_check(haystack&&needle);
    return strstr(haystack, needle);
}

char* pi_strcasestr(const char *haystack, const char *needle)
{
    _pi_input_check(haystack&&needle);
    return strcasestr(haystack, needle);
}

int pi_snprintf(char *str, size_t size, const char *format, ...)
{
    int ret;
    va_list args;	
    _pi_input_check(str&&size&&format);
    va_start(args,format);
    ret = vsnprintf(str,size,format,args);
    va_end(args);
    return ret;
}

int pi_strcasecmp(const char *s1, const char *s2)
{
    _pi_input_check(s1&&s2);

    return strcasecmp(s1, s2);
}

int pi_strdup(const char *s)
{
    _pi_input_check(s);
    return strdup(s);
}

int pi_asprintf(char **strp, const char *format, ...)
{
    int ret;
    va_list args;
    _pi_input_check(strp&&format);
    va_start(args,format);
    ret = vasprintf(strp, format, args);
    va_end(args);
    return ret;
}

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

size_t _pi_strlen(const char *callfile ,const uint32_t callline, const char *s)
{
    _pi_input_check(s);
    return strlen(s);    
}


char* _pi_strncat(const char *callfile ,const uint32_t callline, char *dest, const char *src, size_t n)
{
    _pi_input_check(dest&&src&&n&&((dest<src)||(dest>src+n))); 
    return  strncat(dest,src,n);

}

char* _pi_strncpy(const char *callfile ,const uint32_t callline, char *dest, const char *src, size_t n)
{

    _pi_input_check(dest&&src&&n&&((dest<src)||(dest>=(src+n)))); 
    return strncpy(dest,src,n);
}

int _pi_strcmp(const char *callfile ,const uint32_t callline, const char *s1, const char *s2)
{

    _pi_input_check(s1&&s2); 
    return strcmp(s1,s2);

}

int _pi_strncmp(const char *callfile ,const uint32_t callline, const char *s1, const char *s2, size_t n)
{
    _pi_input_check(s1&&s2&&n); 
    return strncmp(s1,s2,n);
}

int _pi_strncasecmp(const char *callfile ,const uint32_t callline, const char *s1, const char *s2, size_t n)
{
    _pi_input_check(s1&&s2&&n);
    return strncasecmp(s1, s2, n);
}

char* _pi_strstr(const char *callfile ,const uint32_t callline, const char *haystack, const char *needle)
{

    _pi_input_check(haystack&&needle);
    return strstr(haystack, needle);
}

char* _pi_strcasestr(const char *callfile ,const uint32_t callline, const char *haystack, const char *needle)
{
    _pi_input_check(haystack&&needle);
    return strcasestr(haystack, needle);
}

int _pi_snprintf(const char *callfile ,const uint32_t callline, char *str, size_t size, const char *format, ...)
{
    int ret;
    va_list args;	
    _pi_input_check(str&&size&&format);
    va_start(args,format);
    ret = vsnprintf(str,size,format,args);
    va_end(args);
    return ret;
}

int _pi_strcasecmp(const char *callfile, const uint32_t callline, const char *s1, const char *s2)
{
    _pi_input_check(s1&&s2);
    return strcasecmp(s1, s2);
}

char* _pi_strdup(const char *callfile, const uint32_t callline, const char *s)
{
    _pi_input_check(s);
    return strdup(s);
}

int _pi_asprintf(const char *callfile, const uint32_t callline, char **strp, const char *format, ...)
{
    int ret;
    va_list args;
    _pi_input_check(strp&&format);
    va_start(args,format);
    ret = vasprintf(strp, format, args);
    va_end(args);
    return ret;
}

#endif

