/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netctx.c
 * @addtogroup net
 * @{
 * @addtogroup netctx
 * <AUTHOR>
 * @date 2023-5-10
 * @brief Network context manager
 */
#include "nettypes.h"
#include "netevent.h"
#include "netmisc.h"
#include "wifi.h"
#include "ipv6.h"
#include "ipv4.h"

#if CONFIG_NET_WHITELIST
#include "whitelist.h"
#endif

typedef struct network_context_private
{
    EVENT_SUBJECT_S*    sysstat_subj;   ///< 系统状态变更
    EVENT_SUBJECT_S*    sysjob_subj;    ///< 系统作业状态变更
    EVENT_SUBJECT_S*    netport_subj;   ///< 网络端口变更
    EVENT_SUBJECT_S*    netlink_subj;   ///< 网络链路变更
}
PRIV_INFO_S;

static pthread_mutex_t  s_sysstat_subj_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t  s_sysjob_subj_mutex  = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t  s_netport_subj_mutex = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t  s_netlink_subj_mutex = PTHREAD_MUTEX_INITIALIZER;
static NET_CTX_S*       s_network_context    = NULL;

void netctx_add_sysstat_observer(NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);
    EVENT_OBSERVER_S* observer;

    RETURN_IF(priv == NULL || priv->sysstat_subj == NULL, NET_WARN);

    observer = observer_construct(MID_PORT_NET, data, callback);
    RETURN_IF(observer == NULL, NET_WARN);

    observer_attach_to_subject(observer, priv->sysstat_subj);
}

void netctx_add_sysjob_observer(NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);
    EVENT_OBSERVER_S* observer;

    RETURN_IF(priv == NULL || priv->sysjob_subj == NULL, NET_WARN);

    observer = observer_construct(MID_PORT_NET, data, callback);
    RETURN_IF(observer == NULL, NET_WARN);

    observer_attach_to_subject(observer, priv->sysjob_subj);
}

void netctx_add_netport_observer(NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);
    EVENT_OBSERVER_S* observer;

    RETURN_IF(priv == NULL || priv->netport_subj == NULL, NET_WARN);

    observer = observer_construct(MID_PORT_NET, data, callback);
    RETURN_IF(observer == NULL, NET_WARN);

    observer_attach_to_subject(observer, priv->netport_subj);
}

void netctx_add_netlink_observer(NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);
    EVENT_OBSERVER_S* observer;

    RETURN_IF(priv == NULL || priv->netlink_subj == NULL, NET_WARN);

    observer = observer_construct(MID_PORT_NET, data, callback);
    RETURN_IF(observer == NULL, NET_WARN);

    observer_attach_to_subject(observer, priv->netlink_subj);
}

void netctx_push_sysstat_subject(NET_CTX_S* net_ctx, int32_t status)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);

    RETURN_IF(priv == NULL || priv->sysstat_subj == NULL, NET_WARN);

    pthread_mutex_lock(&s_sysstat_subj_mutex);
    priv->sysstat_subj->subject_status = status;
    subject_notify_observers(priv->sysstat_subj);
    pthread_mutex_unlock(&s_sysstat_subj_mutex);
}

void netctx_push_sysjob_subject(NET_CTX_S* net_ctx, void* data)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);

    RETURN_IF(priv == NULL || priv->sysjob_subj == NULL, NET_WARN);

    pthread_mutex_lock(&s_sysjob_subj_mutex);
    priv->sysjob_subj->subject_data = data;
    subject_notify_observers(priv->sysjob_subj);
    pthread_mutex_unlock(&s_sysjob_subj_mutex);
}

void netctx_push_netport_subject(NET_CTX_S* net_ctx, int32_t port)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);

    RETURN_IF(priv == NULL || priv->netport_subj == NULL, NET_WARN);

    pthread_mutex_lock(&s_netport_subj_mutex);
    priv->netport_subj->subject_status = port;
    subject_notify_observers(priv->netport_subj);
    pthread_mutex_unlock(&s_netport_subj_mutex);
}

void netctx_push_netlink_subject(NET_CTX_S* net_ctx, int32_t link)
{
    DECL_PRIV(net_ctx, PRIV_INFO_S, priv);

    RETURN_IF(priv == NULL || priv->netlink_subj == NULL, NET_WARN);

    pthread_mutex_lock(&s_netlink_subj_mutex);
    priv->netlink_subj->subject_status = link;
    subject_notify_observers(priv->netlink_subj);
    pthread_mutex_unlock(&s_netlink_subj_mutex);
}

int32_t netctx_update_wired_speed(NET_CTX_S* net_ctx, IFACE_SPEED_E mode)
{
    RETURN_VAL_IF(mode < IFACE_SPEED_AUTO || mode > IFACE_SPEED_1000, NET_WARN, -1);
    RETURN_VAL_IF(net_ifctl_set_speed(IFACE_ETH, mode) != 0, NET_WARN, -1);

    if ( netdata_set_wired_speed(net_ctx->data_mgr, mode) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ETH_SPEED_CHANGED, mode);
    }
    return 0;
}

#define HOSTNAME_FORMAT     "^[a-zA-Z0-9][-a-zA-Z0-9]{0,13}[a-zA-Z0-9]{0,1}$"
int32_t netctx_update_hostname(NET_CTX_S* net_ctx, const char* hostname)
{
    int32_t ret = -1;

    RETURN_VAL_IF(net_ctx == NULL || STRING_IS_EMPTY(hostname), NET_WARN, -1);

    do
    {
        BREAK_IF(check_string_format(hostname, HOSTNAME_FORMAT) != 0, NET_WARN); /* 主机名由字母数字和'-'组成，且首尾不能为'-' */
        BREAK_IF(strspn(hostname, "0123456789") == strlen(hostname), NET_WARN);  /* 主机名不允许为纯数字 */
        BREAK_IF(sethostname(hostname, strlen(hostname)) != 0, NET_WARN);        /* 系统接口调用出错 */

        if ( netdata_set_hostname(net_ctx->data_mgr, hostname) == 0 )
        {
            NETEVT_NOTIFY_S(EVT_TYPE_NET_HOST_NAME_CHANGED, hostname);
            netctx_push_netport_subject(net_ctx, PORT_UPDATE_SLP | PORT_UPDATE_BONJOUR);
        }
        ret = 0;
    }
    while ( 0 );

    if ( ret < 0 )
    {
        NET_WARN("ignore invalid hostname(%s)", hostname);
    }
    return ret;
}

#define DOMAIN_FORMAT       "^[a-zA-Z0-9.]{0,1}[-a-zA-Z0-9.]{0,61}[a-zA-Z0-9.]{0,1}$"
int32_t netctx_update_domain(NET_CTX_S* net_ctx, const char* domain)
{
    int32_t ret = -1;

    RETURN_VAL_IF(net_ctx == NULL || domain == NULL, NET_WARN, -1);

    do
    {
        BREAK_IF(check_string_format(domain, DOMAIN_FORMAT) != 0, NET_WARN);
        BREAK_IF(strstr(domain, "..") != NULL, NET_WARN);

        if ( netdata_set_domain(net_ctx->data_mgr, domain) == 0 )
        {
            NETEVT_NOTIFY_S(EVT_TYPE_NET_DOMAIN_NAME_CHANGED, domain);
        }
        ret = 0;
    }
    while ( 0 );

    if ( ret < 0 )
    {
        NET_WARN("ignore invalid domain(%s)", domain);
    }
    return ret;
}

int32_t netctx_update_mac_addr(NET_CTX_S* net_ctx, IFACE_ID_E ifid, const char* str)
{
    char        mfg_name[32];
    char        hostname[16];
    char        mac_str[18];
    uint8_t     mac[6];
    uint32_t    tmp[6];
    int32_t     ret = -1;
    size_t      i;

    RETURN_VAL_IF(net_ctx == NULL || ifid != IFACE_ID_ETH || STRING_IS_EMPTY(str), NET_WARN, -1);

    /* parse the MAC address string, eg. XX:XX:XX:XX:XX:XX */
    if ( 6 == sscanf(str, "%02X:%02X:%02X:%02X:%02X:%02X", &tmp[0], &tmp[1], &tmp[2], &tmp[3], &tmp[4], &tmp[5]) )
    {
        for ( i = 0; i < sizeof(mac); ++i )
        {
            mac[i] = (uint8_t)(tmp[i] & 0xFF);
        }
        if ( net_ifctl_set_mac(IFACE_ETH, mac, sizeof(mac)) == 0 )
        {
            /* 重新基于mac[6]组装mac_str "XX:XX:XX:XX:XX:XX"，避免参数str有冗余数据 */
            snprintf(mac_str, sizeof(mac_str), "%02X:%02X:%02X:%02X:%02X:%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
            if ( netdata_set_mac_addr(net_ctx->data_mgr, IFACE_ID_ETH, mac_str) == 0 )
            {
                NETEVT_NOTIFY_S(EVT_TYPE_NET_ETH_MAC_CHANGED, mac_str);
                netdata_get_mfg_name(net_ctx->data_mgr, mfg_name, sizeof(mfg_name));
                snprintf(hostname, sizeof(hostname), "%s-%02X%02X%02X", mfg_name, mac[3], mac[4], mac[5]);
                NET_DEBUG("reset default hostname(%s) base on MAC(%s)", hostname, mac_str);
                netctx_update_hostname(net_ctx, hostname);
            }
            ret = 0;
        }
    }
    else
    {
        NET_WARN("invalid MAC string(%s)", str);
    }

    return ret;
}

void netctx_update_iface_switch(NET_CTX_S* net_ctx, IFACE_ID_E ifid, uint32_t on)
{
    switch ( ifid )
    {
    case IFACE_ID_ETH:
        if ( netdata_set_iface_switch(net_ctx->data_mgr, ifid, on) == 0 )
        {
            NETEVT_NOTIFY_I(EVT_TYPE_NET_ETH_SWITCH_CHANGED, on);
            net_ifctl_switch_safe(IFACE_ETH, on);
        }
        break;
#if CONFIG_NET_WIFI
    case IFACE_ID_STA:
        wifi_ctrl_sta_switch(on);
        break;
    case IFACE_ID_WFD:
        wifi_ctrl_wfd_switch(on);
        break;
#endif /* CONFIG_NET_WIFI */
    default:
        NET_WARN("invalid ifid(%d)", ifid);
        break;
    }
}

void netctx_update_ipv4_config(NET_CTX_S* net_ctx, IFACE_ID_E ifid, NET_IPV4_CONF_S* ipv4_conf)
{
    EVT_TYPE_E  ipv4_dhcp_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV4_DHCP_SWITCH_CHANGED);
    EVT_TYPE_E  ipv4_addr_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_ADDRESS_CHANGED);
    EVT_TYPE_E  ipv4_mask_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_MASK_CHANGED);
    EVT_TYPE_E  ipv4_gtwy_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_GATEWAY_CHANGED);
    uint32_t    usedhcp;
    uint32_t    running;

    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);
    RETURN_IF(net_ctx == NULL || ipv4_conf == NULL, NET_WARN);

    NET_DEBUG("config(%u | %x | %s | %s | %s)", ipv4_conf->dhcp_enabled, ipv4_conf->changed, ipv4_conf->address, ipv4_conf->mask, ipv4_conf->gateway);
    usedhcp = (uint32_t)(!!(ipv4_conf->dhcp_enabled));
    running = net_ifctl_is_running(IFACE_NAME(ifid));
    if ( netdata_set_ipv4_usedhcp(net_ctx->data_mgr, ifid, usedhcp) == 0 )
    {
        NETEVT_NOTIFY_I(ipv4_dhcp_event[ifid], usedhcp);
        if ( usedhcp && running )
        {
            net_ipv4_start_dhcp(ifid);
        }
    }

    if ( usedhcp == 0 )
    {
        net_ipv4_stop_dhcp(ifid);

        /* IP未变更，获取当前IP, 否则更新到data_mgr并上报 */
        if ( (ipv4_conf->changed & IPV4_CONF_ADDRESS) == 0 )
        {
            netdata_get_ipv4_addr(net_ctx->data_mgr, ifid, ipv4_conf->address, sizeof(ipv4_conf->address));
        }
        else if ( netdata_set_ipv4_addr(net_ctx->data_mgr, ifid, ipv4_conf->address) == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_addr_event[ifid], ipv4_conf->address);
        }

        /* 掩码未变更，获取当前掩码地址, 否则更新到data_mgr并上报 */
        if ( (ipv4_conf->changed & IPV4_CONF_MASK) == 0 )
        {
            netdata_get_ipv4_mask(net_ctx->data_mgr, ifid, ipv4_conf->mask, sizeof(ipv4_conf->mask));
        }
        else if ( netdata_set_ipv4_mask(net_ctx->data_mgr, ifid, ipv4_conf->mask) == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_mask_event[ifid], ipv4_conf->mask);
        }

        /* 网关未变更，获取当前网关地址, 否则更新到data_mgr并上报 */
        if ( (ipv4_conf->changed & IPV4_CONF_GATEWAY) == 0 )
        {
            netdata_get_ipv4_gtwy(net_ctx->data_mgr, ifid, ipv4_conf->gateway, sizeof(ipv4_conf->gateway));
        }
        else if ( netdata_set_ipv4_gtwy(net_ctx->data_mgr, ifid, ipv4_conf->gateway) == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_gtwy_event[ifid], ipv4_conf->gateway);
        }

        if ( running )
        {
            net_ipv4_set_addr(ifid, ipv4_conf->address, ipv4_conf->mask, ipv4_conf->gateway);
        }
    }
}

void netctx_update_dnsv4_config(NET_CTX_S* net_ctx, IFACE_ID_E ifid, NET_DNSV4_CONF_S* dnsv4_conf)
{
    EVT_TYPE_E  ipv4_autodns_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV4_AUTODNS_SWITCH_CHANGED);
    EVT_TYPE_E  ipv4_dns0_event[IFACE_ID_NUM]    = NET_EVT_ARRAY2(IPV4_PRIMARY_DNS_CHANGED);
    EVT_TYPE_E  ipv4_dns1_event[IFACE_ID_NUM]    = NET_EVT_ARRAY2(IPV4_SECONDARY_DNS_CHANGED);
    char        dns[2][IPV4_ADDR_LEN];
    uint32_t    autodns;

    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);
    RETURN_IF(net_ctx == NULL || dnsv4_conf == NULL, NET_WARN);

    NET_DEBUG("config(%u | %x | %s | %s)", dnsv4_conf->autodns, dnsv4_conf->changed, dnsv4_conf->dns0, dnsv4_conf->dns1);
    autodns = (uint32_t)(!!(dnsv4_conf->autodns));
    if ( netdata_set_ipv4_autodns(net_ctx->data_mgr, ifid, autodns) == 0 )
    {
        NETEVT_NOTIFY_I(ipv4_autodns_event[ifid], autodns);
    }

    memset(dns, 0, sizeof(dns));
    if ( autodns )
    {
        if ( netdata_get_iface_running(net_ctx->data_mgr, ifid) )
        {
            if ( netdata_get_ipv4_usedhcp(net_ctx->data_mgr, ifid) )
            {
                netdata_get_ipv4_dns0_bak(net_ctx->data_mgr, ifid, dns[0], sizeof(dns[0]));
                netdata_get_ipv4_dns1_bak(net_ctx->data_mgr, ifid, dns[1], sizeof(dns[1]));
                net_ipv4_set_dns(ifid, dns);
            }
            else
            {
                net_ipv4_update_autodns(ifid); /* DHCP未开启，单独运行udhcpc以更新自动DNS到resolv.conf */
                net_ipv4_get_dns(ifid, dns);
            }
        }
    }
    else
    {
        if ( (dnsv4_conf->changed & IPV4DNS_CONF_DNS0) == 0 )
        {
            netdata_get_ipv4_dns0(net_ctx->data_mgr, ifid, dns[0], sizeof(dns[0]));
        }
        else
        {
            snprintf(dns[0], sizeof(dns[0]), "%s", dnsv4_conf->dns0);
        }

        if ( (dnsv4_conf->changed & IPV4DNS_CONF_DNS1) == 0 )
        {
            netdata_get_ipv4_dns1(net_ctx->data_mgr, ifid, dns[1], sizeof(dns[1]));
        }
        else
        {
            snprintf(dns[1], sizeof(dns[1]), "%s", dnsv4_conf->dns1);
        }

        if ( netdata_get_iface_running(net_ctx->data_mgr, ifid) )
        {
            net_ipv4_set_dns(ifid, dns);
        }
    }

    if ( netdata_set_ipv4_dns0(net_ctx->data_mgr, ifid, dns[0]) == 0 )
    {
        NETEVT_NOTIFY_S(ipv4_dns0_event[ifid], dns[0]);
    }

    if ( netdata_set_ipv4_dns1(net_ctx->data_mgr, ifid, dns[1]) == 0 )
    {
        NETEVT_NOTIFY_S(ipv4_dns1_event[ifid], dns[1]);
    }
}

void netctx_update_ipv6_config(NET_CTX_S* net_ctx, IFACE_ID_E ifid, NET_IPV6_CONF_S* ipv6_conf)
{
    EVT_TYPE_E  dhcpv6_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_DHCP_SWITCH_CHANGED);
    EVT_TYPE_E  switch_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_SWITCH_CHANGED);
    uint32_t    valid_config = 0;
    uint32_t    dhcp_enabled;
    uint32_t    ipv6_enabled;

    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);
    RETURN_IF(net_ctx == NULL || ipv6_conf == NULL, NET_WARN);

    NET_DEBUG("config(%u | %u)", ipv6_conf->enabled, ipv6_conf->dhcp_enabled);
    dhcp_enabled = (uint32_t)(!!(ipv6_conf->dhcp_enabled));
    ipv6_enabled = (uint32_t)(!!(ipv6_conf->enabled));
    if ( netdata_set_ipv6_switch(net_ctx->data_mgr, ifid, ipv6_enabled) == 0 )
    {
        net_ipv6_switch(ifid, ipv6_enabled);
        NETEVT_NOTIFY_I(switch_event[ifid], ipv6_enabled);
        valid_config = 1;
    }
    if ( netdata_set_ipv6_usedhcp(net_ctx->data_mgr, ifid, dhcp_enabled) == 0 )
    {
        NETEVT_NOTIFY_I(dhcpv6_event[ifid], dhcp_enabled);
        valid_config = 1;
    }

    if ( valid_config && ipv6_enabled )
    {
        if ( dhcp_enabled )
        {
            net_ipv6_start_dhcp(ifid);
        }
        else
        {
            net_ipv6_stop_dhcp(ifid);
        }
    }
}

void netctx_scan_wifi_ssid(NET_CTX_S* net_ctx, uint32_t on)
{
#if CONFIG_NET_WIFI
    wifi_ctrl_sta_scan_ssid(on);
#else
    NET_WARN("unsupported!!!");
#endif
}

void netctx_connect_wifi_station(NET_CTX_S* net_ctx, WIFI_CONN_CONF_S* conn, int32_t sync)
{
#if CONFIG_NET_WIFI
    char utf8_ssid[128];

    if ( conn )
    {
        NET_DEBUG("connect to: ssid(%s) sec_mode(%u) psk(%s)", conn->ssid, conn->sec_mode, conn->psk);
        if ( STRING_NO_EMPTY(conn->ssid) || STRING_NO_EMPTY(conn->psk) )
        {
            RETURN_IF(convert_str_to_utf8(conn->ssid, utf8_ssid, sizeof(utf8_ssid)) != 0, NET_WARN);

            netdata_set_sta_ssid(net_ctx->data_mgr, utf8_ssid);
            netdata_set_sta_sec_mode(net_ctx->data_mgr, conn->sec_mode);
            netdata_set_sta_psk(net_ctx->data_mgr, conn->psk);
        }
        wifi_ctrl_sta_connect(sync);
    }
    else
    {
        wifi_ctrl_sta_disconnect();
    }
#else
    NET_WARN("unsupported!!!");
#endif
}

void netctx_request_wps_command(NET_CTX_S* net_ctx, const char* wps_cmd)
{
#if CONFIG_NET_WIFI
    RETURN_IF(net_ctx == NULL || wps_cmd == NULL, NET_WARN);
    NET_DEBUG("wps_cmd(%s)", wps_cmd);

    if ( strcasecmp(wps_cmd, "cancel") == 0 || strcmp(wps_cmd, "255") == 0 )
    {
        wifi_ctrl_sta_wps_cancel();
    }
    else if ( strcasecmp(wps_cmd, "pbc") == 0 || strcmp(wps_cmd, "0") == 0 )
    {
        wifi_ctrl_sta_wps_pbc();
    }
    else if ( strcasecmp(wps_cmd, "pin") == 0 || strcmp(wps_cmd, "1") == 0 )
    {
        wifi_ctrl_sta_wps_pin();
    }
#else
    NET_WARN("unsupported!!!");
#endif
}

void netctx_update_wfd_ssid(NET_CTX_S* net_ctx, const char* suffix)
{
#if CONFIG_NET_WIFI
    RETURN_IF(STRING_IS_EMPTY(suffix), NET_WARN);
    NET_DEBUG("update wfd ssid(%s)", suffix);

    if ( netdata_set_wfd_ssid_suffix(net_ctx->data_mgr, suffix) == 0 )
    {
        wifi_ctrl_wfd_switch(netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_WFD)); /* IFACE_WFD 重载 */
        NETEVT_NOTIFY_S(EVT_TYPE_NET_WFD_SSID_SUFFIX_CHANGED, suffix);
    }
#else
    NET_WARN("unsupported!!!");
#endif
}

void netctx_update_wfd_psk(NET_CTX_S* net_ctx, const char* psk)
{
#if CONFIG_NET_WIFI
    RETURN_IF(STRING_IS_EMPTY(psk), NET_WARN);
    NET_DEBUG("update wfd psk(%s)", psk);

    if ( netdata_set_wfd_psk(net_ctx->data_mgr, psk) == 0 )
    {
        wifi_ctrl_wfd_switch(netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_WFD));/* IFACE_WFD 重载 */
        NETEVT_NOTIFY_S(EVT_TYPE_NET_WFD_PASSWORD_CHANGED, psk);
    }
#else
    NET_WARN("unsupported!!!");
#endif
}

void netctx_update_rawprint_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_rawprint_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_RAWPRINT_SWITCH_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_RAWPRINT);
    }
}

void netctx_update_rawprint_port(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_rawprint_port(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_RAWPRINT_PORT_CHANGED, value);
#if CONFIG_NET_IPPSRV
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_RAWPRINT);
#else
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_RAWPRINT | PORT_UPDATE_BONJOUR);
#endif
    }
}

void netctx_update_lpd_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_lpd_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_LPD_SWITCH_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_LPD);
    }
}

void netctx_update_wsd_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_wsd_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WSD_SWITCH_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_WSD);
    }
}

void netctx_update_slp_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_slp_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SLP_SWITCH_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SLP);
    }
}

void netctx_update_bonjour_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_bonjour_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_BONJOUR_SWITCH_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_BONJOUR);
    }
}

void netctx_update_bonjour_server(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_bonjour_server(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_BONJOUR_SERVER_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_BONJOUR);
    }
}

void netctx_update_ipp_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_ipp_switch(net_ctx->data_mgr, value) == 0 )
    {
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_IPP | PORT_UPDATE_BONJOUR);
    }
}

void netctx_update_snmp_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_snmp_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SNMP_SWITCH_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SLP | PORT_UPDATE_BONJOUR | PORT_UPDATE_SNMP);
    }
}

void netctx_update_snmp_v1v2c_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_snmp_v1v2c_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SNMP_V1V2C_SWITCH_CHANGED, value);
        flags = netdata_get_snmp_version_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)SNMP_SUPPORT_V1V2C);
        }
        else
        {
            flags |= ((uint32_t)SNMP_SUPPORT_V1V2C);
        }

        if ( netdata_set_snmp_version_flags(net_ctx->data_mgr, flags) == 0 )
        {
            netctx_push_netport_subject(net_ctx, PORT_UPDATE_SLP | PORT_UPDATE_BONJOUR | PORT_UPDATE_SNMP);
        }
        else
        {
            NET_WARN("snmp version flags(0x%x) no changed!", flags);
        }
    }
}

void netctx_update_snmp_v3_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_snmp_v3_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SNMP_V3_SWITCH_CHANGED, value);
        flags = netdata_get_snmp_version_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)SNMP_SUPPORT_V3);
        }
        else
        {
            flags |= ((uint32_t)SNMP_SUPPORT_V3);
        }

        if ( netdata_set_snmp_version_flags(net_ctx->data_mgr, flags) == 0 )
        {
            netctx_push_netport_subject(net_ctx, PORT_UPDATE_SLP | PORT_UPDATE_BONJOUR | PORT_UPDATE_SNMP);
        }
        else
        {
            NET_WARN("snmp version flags(0x%x) no changed!", flags);
        }
    }
}

void netctx_update_snmp_v1_community(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_snmp_v1_community(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V1_COMMUNITY_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
    }
}

void netctx_update_snmp_v2_community(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_snmp_v2_community(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V2C_COMMUNITY_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
    }
}

void netctx_update_snmp_v3_community(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_snmp_v3_community(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_COMMUNITY_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
    }
}

void netctx_update_snmp_v3_user_name(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_snmp_v3_user_name(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_USERNAME_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
    }
}

void netctx_update_snmp_v3_auth_pswd(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_snmp_v3_auth_pswd(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_AUTHPASS_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
    }
}

void netctx_update_snmp_v3_priv_pswd(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_snmp_v3_priv_pswd(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_PRIVPASS_CHANGED, value);
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
    }
}

void netctx_update_smtp_sender_addr(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_smtp_sender_addr(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_SENDER_ADDRESS_CHANGED, value);
    }
}

void netctx_update_smtp_server_addr(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_smtp_server_addr(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_SERVER_ADDRESS_CHANGED, value);
    }
}

void netctx_update_smtp_server_port(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_smtp_server_port(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SMTP_SERVER_PORT_CHANGED, value);
    }
}

void netctx_update_smtp_server_auth(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_smtp_server_auth(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SMTP_SERVER_AUTH_CHANGED, value);
    }
}

void netctx_update_smtp_sec_mode(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_smtp_sec_mode(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_SMTP_SEC_MODE_CHANGED, value);
    }
}

void netctx_update_smtp_username(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_smtp_username(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_USERNAME_CHANGED, value);
    }
}

void netctx_update_smtp_password(NET_CTX_S* net_ctx, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    if ( netdata_set_smtp_password(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_PASSWORD_CHANGED, value);
    }
}

void netctx_update_alarm_client_addr(NET_CTX_S* net_ctx, int32_t index, const char* value)
{
    RETURN_IF(net_ctx == NULL || value == NULL, NET_WARN);

    switch ( index )
    {
    case 1:
        if ( netdata_set_alarm_client_addr1(net_ctx->data_mgr, value) == 0 )
        {
            NETEVT_NOTIFY_S(EVT_TYPE_NET_ALARM_CLIENT_ADDRESS1_CHANGED, value);
        }
        break;
    case 2:
        if ( netdata_set_alarm_client_addr2(net_ctx->data_mgr, value) == 0 )
        {
            NETEVT_NOTIFY_S(EVT_TYPE_NET_ALARM_CLIENT_ADDRESS2_CHANGED, value);
        }
        break;
    case 3:
        if ( netdata_set_alarm_client_addr3(net_ctx->data_mgr, value) == 0 )
        {
            NETEVT_NOTIFY_S(EVT_TYPE_NET_ALARM_CLIENT_ADDRESS3_CHANGED, value);
        }
        break;
    case 4:
        if ( netdata_set_alarm_client_addr4(net_ctx->data_mgr, value) == 0 )
        {
            NETEVT_NOTIFY_S(EVT_TYPE_NET_ALARM_CLIENT_ADDRESS4_CHANGED, value);
        }
        break;
    default:
        break;
    }
}

void netctx_update_alarm_paper_empty(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_alarm_paper_empty(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_PAPER_EMPTY_CHANGED, value);
        flags = netdata_get_alarm_status_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)EMAIL_ALARM_PAPER_EMPTY);
        }
        else
        {
            flags |= ((uint32_t)EMAIL_ALARM_PAPER_EMPTY);
        }

        if ( netdata_set_alarm_status_flags(net_ctx->data_mgr, flags) != 0 )
        {
            NET_WARN("alarm status flags(0x%x) no changed!", flags);
        }
    }
}

void netctx_update_alarm_paper_jam(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_alarm_paper_jam(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_PAPER_JAM_CHANGED, value);
        flags = netdata_get_alarm_status_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)EMAIL_ALARM_PAPER_JAM);
        }
        else
        {
            flags |= ((uint32_t)EMAIL_ALARM_PAPER_JAM);
        }

        if ( netdata_set_alarm_status_flags(net_ctx->data_mgr, flags) != 0 )
        {
            NET_WARN("alarm status flags(0x%x) no changed!", flags);
        }
    }
}

void netctx_update_alarm_toner_empty(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_alarm_toner_empty(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_TONER_EMPTY_CHANGED, value);
        flags = netdata_get_alarm_status_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)EMAIL_ALARM_TONER_EMPTY);
        }
        else
        {
            flags |= ((uint32_t)EMAIL_ALARM_TONER_EMPTY);
        }

        if ( netdata_set_alarm_status_flags(net_ctx->data_mgr, flags) != 0 )
        {
            NET_WARN("alarm status flags(0x%x) no changed!", flags);
        }
    }
}

void netctx_update_alarm_toner_low(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_alarm_toner_low(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_TONER_LOW_CHANGED, value);
        flags = netdata_get_alarm_status_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)EMAIL_ALARM_TONER_LOW);
        }
        else
        {
            flags |= ((uint32_t)EMAIL_ALARM_TONER_LOW);
        }

        if ( netdata_set_alarm_status_flags(net_ctx->data_mgr, flags) != 0 )
        {
            NET_WARN("alarm status flags(0x%x) no changed!", flags);
        }
    }
}

void netctx_update_alarm_waste_toner(NET_CTX_S* net_ctx, uint32_t value)
{
    uint32_t flags;

    RETURN_IF(net_ctx == NULL, NET_WARN);

    NET_DEBUG("[L%d] val(%u)", __LINE__, value);
    value = (!!value);
    if ( netdata_set_alarm_waste_toner(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_WASTE_TONER_CHANGED, value);
        flags = netdata_get_alarm_status_flags(net_ctx->data_mgr);
        if ( value == 0 )
        {
            flags &= (~(uint32_t)EMAIL_ALARM_WASTE_TONER);
        }
        else
        {
            flags |= ((uint32_t)EMAIL_ALARM_WASTE_TONER);
        }

        if ( netdata_set_alarm_status_flags(net_ctx->data_mgr, flags) != 0 )
        {
            NET_WARN("alarm status flags(0x%x) no changed!", flags);
        }
    }
}

static void sync_grouplist_mail(NET_CTX_S* net_ctx)
{
    MAIL_GROUPLIST_S    grouplist;
    MAIL_ADDRBOOK_S     addrbook;
    uint32_t            i, j;
    uint32_t            addr_group_index;
    uint32_t            group_mail_index;

    netdata_get_mail_grouplist(net_ctx->data_mgr, &grouplist, sizeof(grouplist));
    netdata_get_mail_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));

    for( i = 0; i < addrbook.num; ++i )
    {
        addr_group_index = 0;
        memset( addrbook.info[i].group_id, 0x00, sizeof(addrbook.info[i].group_id) );             //先清空mail 的group群组信息。
        for( j = 0; j < grouplist.num; ++j )                                                      //遍历group群组
        {
            group_mail_index = 0;
            while( group_mail_index < ARRAY_SIZE(grouplist.info[j].mail_id) && grouplist.info[j].mail_id[group_mail_index] != 0 )
            {
                if(grouplist.info[j].mail_id[group_mail_index] == addrbook.info[i].record_id)
                {
                    NET_DEBUG("update node addrbook[%u] grouplist[%u] addr_node[%u] grouplist_node[%u]", i, j, addr_group_index, group_mail_index);
                    addrbook.info[i].group_id[addr_group_index++] = grouplist.info[j].record_id;
                    group_mail_index = 0;
                    break;
                }
                group_mail_index++;
            }
        }
    }

    netdata_set_mail_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED, addrbook);
}

int32_t netctx_update_mail_grouplist(NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op)
{
    MAIL_GROUPLIST_S    grouplist;
    uint32_t            i, j;

    RETURN_VAL_IF(net_ctx == NULL || op == NULL || op->type != AB_TYPE_MAIL_GROUP, NET_WARN, -1);

    netdata_get_mail_grouplist(net_ctx->data_mgr, &grouplist, sizeof(grouplist));
    //netdata_get_mail_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));

    switch ( op->operation )
    {
    case AB_OP_ADD:
        RETURN_VAL_IF(grouplist.num >= ARRAY_SIZE(grouplist.info), NET_WARN, -1);
        for ( i = 0; i < grouplist.num; ++i )
        {
            RETURN_VAL_IF(strcmp(grouplist.info[i].name, op->u.group_info.name) == 0, NET_WARN, -2);
        }
        op->u.group_info.record_id = ++grouplist.idx;
        grouplist.info[grouplist.num] = op->u.group_info;
        grouplist.num++;
        break;
    case AB_OP_DELETE:
        BREAK_IF(grouplist.num == 0, NET_WARN);
        for ( j = 0; j < ARRAY_SIZE(op->delete_id) && op->delete_id[j] > 0; ++j )
        {
            for ( i = 0; i < grouplist.num; ++i )
            {
                if ( grouplist.info[i].record_id == op->delete_id[j] )
                {
                    break;
                }
            }
            if ( i < grouplist.num - 1 )
            {
                memcpy(&(grouplist.info[i]), &(grouplist.info[i + 1]), sizeof(MAIL_GROUPINFO_S) * (grouplist.num - i - 1));
                NET_DEBUG("remove grouplist.info[%u] record_id(%d)", i, op->delete_id[j]);
                memset(&(grouplist.info[grouplist.num - 1]), 0, sizeof(MAIL_GROUPINFO_S));
                grouplist.num--;
            }
            else if ( i == grouplist.num - 1 )
            {
                grouplist.num--;
            }
            else
            {
                NET_DEBUG("no search item record_id(%d)", op->delete_id[j]);
            }
        }
        break;
    case AB_OP_UPDATE:
        RETURN_VAL_IF(op->u.group_info.record_id <= 0, NET_WARN, -1);
        for ( i = 0; i < grouplist.num; ++i )
        {
            RETURN_VAL_IF(strcmp(grouplist.info[i].name, op->u.group_info.name) == 0 && grouplist.info[i].record_id != op->u.group_info.record_id, NET_WARN, -2);
        }
        for ( i = 0; i < grouplist.num; ++i )
        {
            if ( grouplist.info[i].record_id == op->u.group_info.record_id )
            {
                grouplist.info[i] = op->u.group_info;
                break;
            }
        }
        break;
    default:
        NET_WARN("invalid op(%d)", op->operation);
        break;
    }
    netdata_set_mail_grouplist(net_ctx->data_mgr, &grouplist, sizeof(grouplist));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED, grouplist);

    sync_grouplist_mail(net_ctx);
    return 0;
}

static void sync_mail_addrbook_group(NET_CTX_S* net_ctx, MAIL_ADDRBOOK_S* addrbook)
{
    MAIL_GROUPLIST_S    grouplist;
    uint32_t            addrbook_num, group_num;
    uint32_t            addr_group_index;
    uint32_t            group_mail_index;

    memset(&grouplist, 0, sizeof(MAIL_GROUPLIST_S));
    netdata_get_mail_grouplist(net_ctx->data_mgr, &grouplist, sizeof(MAIL_GROUPLIST_S));

    for ( group_num = 0; group_num < grouplist.num; group_num++ )
    {
        group_mail_index = 0;
        memset(grouplist.info[group_num].mail_id, 0, sizeof(grouplist.info[group_num].mail_id));
        for ( addrbook_num = 0; addrbook_num < addrbook->num; addrbook_num++ )
        {
            addr_group_index = 0;
            while ( addr_group_index < ARRAY_SIZE(addrbook->info[addrbook_num].group_id) && grouplist.info[group_num].record_id != 0 )
            {
                if ( grouplist.info[group_num].record_id == addrbook->info[addrbook_num].group_id[addr_group_index] )
                {
                    NET_DEBUG("update grouplist[%s] record_id[%u] mail[%s]", grouplist.info[group_num].name, grouplist.info[group_num].record_id, addrbook->info[addrbook_num].name);
                    grouplist.info[group_num].mail_id[group_mail_index++] = addrbook->info[addrbook_num].record_id;
                    break;
                }
                addr_group_index++;
            }
        }
    }

    netdata_set_mail_grouplist(net_ctx->data_mgr, &grouplist, sizeof(grouplist));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED, grouplist);
}


int32_t netctx_update_mail_addrbook(NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op)
{
    MAIL_ADDRBOOK_S     addrbook;
    uint32_t            i, j;

    RETURN_VAL_IF(net_ctx == NULL || op == NULL, NET_WARN, -1);
    RETURN_VAL_IF(op->type != AB_TYPE_MAIL, NET_WARN, -1);

    netdata_get_mail_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    switch ( op->operation )
    {
    case AB_OP_ADD:
        RETURN_VAL_IF(addrbook.num >= ARRAY_SIZE(addrbook.info), NET_WARN, -1);
        for ( i = 0; i < addrbook.num; ++i )
        {
            RETURN_VAL_IF(strcmp(addrbook.info[i].name, op->u.mail_info.name) == 0, NET_WARN, -2);
        }
        addrbook.info[addrbook.num] = op->u.mail_info;
        addrbook.info[addrbook.num].record_id = ++addrbook.idx;
        addrbook.num++;
        break;
    case AB_OP_DELETE:
        BREAK_IF(addrbook.num == 0, NET_WARN);
        for ( j = 0; j < ARRAY_SIZE(op->delete_id) && op->delete_id[j] > 0; ++j )
        {
            for ( i = 0; i < addrbook.num; ++i )
            {
                if ( addrbook.info[i].record_id == op->delete_id[j] )
                {
                    break;
                }
            }
            if ( i < addrbook.num - 1 )
            {
                memcpy(&(addrbook.info[i]), &(addrbook.info[i + 1]), sizeof(MAIL_ADDRINFO_S) * (addrbook.num - i - 1));
                NET_DEBUG("remove addrbook.info[%u] record_id(%d)", i, op->delete_id[j]);
                memset(&(addrbook.info[addrbook.num - 1]), 0, sizeof(MAIL_ADDRINFO_S));
                addrbook.num--;
            }
            else if ( i == addrbook.num - 1 )
            {
                addrbook.num--;
            }
            else
            {
                NET_DEBUG("no search item record_id(%d)", op->delete_id[j]);
            }
        }
        break;
    case AB_OP_UPDATE:
        RETURN_VAL_IF(op->u.mail_info.record_id <= 0, NET_WARN, -1);
        for ( i = 0; i < addrbook.num; ++i )
        {
            RETURN_VAL_IF(strcmp(addrbook.info[i].name, op->u.mail_info.name) == 0 && addrbook.info[i].record_id != op->u.mail_info.record_id, NET_WARN, -2);
        }
        for ( i = 0; i < addrbook.num; ++i )
        {
            if ( addrbook.info[i].record_id == op->u.mail_info.record_id )
            {
                addrbook.info[i] = op->u.mail_info;
                break;
            }
        }
        break;
    default:
        NET_WARN("invalid op(%d)", op->operation);
        break;
    }
    netdata_set_mail_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED, addrbook);

    sync_mail_addrbook_group(net_ctx, &addrbook);
    return 0;
}

int32_t netctx_update_ftp_addrbook(NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op)
{
    FTP_ADDRBOOK_S      addrbook;
    uint32_t            i, j;

    RETURN_VAL_IF(net_ctx == NULL || op == NULL, NET_WARN, -1);
    RETURN_VAL_IF(op->type != AB_TYPE_FTP, NET_WARN, -1);

    netdata_get_ftp_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    switch ( op->operation )
    {
    case AB_OP_ADD:
        RETURN_VAL_IF(addrbook.num >= ARRAY_SIZE(addrbook.info), NET_WARN, -1);
        for ( i = 0; i < addrbook.num; ++i )
        {
            RETURN_VAL_IF(strcmp(addrbook.info[i].server_name, op->u.ftp_info.server_name) == 0, NET_WARN, -2);
        }
        addrbook.info[addrbook.num] = op->u.ftp_info;
        addrbook.info[addrbook.num].record_id = ++addrbook.idx;
        addrbook.num++;
        break;
    case AB_OP_DELETE:
        BREAK_IF(addrbook.num == 0, NET_WARN);
        for ( j = 0; j < ARRAY_SIZE(op->delete_id) && op->delete_id[j] > 0; ++j )
        {
            for ( i = 0; i < addrbook.num; ++i )
            {
                if ( addrbook.info[i].record_id == op->delete_id[j] )
                {
                    break;
                }
            }
            if ( i < addrbook.num - 1 )
            {
                memcpy(&(addrbook.info[i]), &(addrbook.info[i + 1]), sizeof(FTP_ADDRINFO_S) * (addrbook.num - i - 1));
                NET_DEBUG("remove addrbook.info[%u] record_id(%d)", i, op->delete_id[j]);
                memset(&(addrbook.info[addrbook.num - 1]), 0, sizeof(FTP_ADDRINFO_S));
                addrbook.num--;
            }
            else if ( i == addrbook.num - 1 )
            {
                addrbook.num--;
            }
            else
            {
                NET_DEBUG("no search item record_id(%d)", op->delete_id[j]);
            }
        }
        break;
    case AB_OP_UPDATE:
        RETURN_VAL_IF(op->u.ftp_info.record_id <= 0, NET_WARN, -1);
        for ( i = 0; i < addrbook.num; ++i )
        {
            RETURN_VAL_IF(strcmp(addrbook.info[i].server_name, op->u.ftp_info.server_name) == 0 && addrbook.info[i].record_id != op->u.ftp_info.record_id, NET_WARN, -2);
        }
        for ( i = 0; i < addrbook.num; ++i )
        {
            if ( addrbook.info[i].record_id == op->u.ftp_info.record_id )
            {
                addrbook.info[i] = op->u.ftp_info;
                break;
            }
        }
        break;
    default:
        NET_WARN("invalid op(%d)", op->operation);
        break;
    }
    netdata_set_ftp_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED, addrbook);
    return 0;
}

int32_t netctx_update_smb_addrbook(NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op)
{
    SMB_ADDRBOOK_S      addrbook;
    uint32_t            i, j;

    RETURN_VAL_IF(net_ctx == NULL || op == NULL, NET_WARN, -1);
    RETURN_VAL_IF(op->type != AB_TYPE_SMB, NET_WARN, -1);

    netdata_get_smb_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    switch ( op->operation )
    {
    case AB_OP_ADD:
        RETURN_VAL_IF(addrbook.num >= ARRAY_SIZE(addrbook.info), NET_WARN, -1);
        for ( i = 0; i < addrbook.num; ++i )
        {
            RETURN_VAL_IF(strcmp(addrbook.info[i].server_name, op->u.smb_info.server_name) == 0, NET_WARN, -2);
        }
        addrbook.info[addrbook.num] = op->u.smb_info;
        addrbook.info[addrbook.num].record_id = ++addrbook.idx;
        addrbook.num++;
        break;
    case AB_OP_DELETE:
        BREAK_IF(addrbook.num == 0, NET_WARN);
        for ( j = 0; j < ARRAY_SIZE(op->delete_id) && op->delete_id[j] > 0; ++j )
        {
            for ( i = 0; i < addrbook.num; ++i )
            {
                if ( addrbook.info[i].record_id == op->delete_id[j] )
                {
                    break;
                }
            }
            if ( i < addrbook.num - 1 )
            {
                memcpy(&(addrbook.info[i]), &(addrbook.info[i + 1]), sizeof(SMB_ADDRINFO_S) * (addrbook.num - i - 1));
                NET_DEBUG("remove addrbook.info[%u] record_id(%d)", i, op->delete_id[j]);
                memset(&(addrbook.info[addrbook.num - 1]), 0, sizeof(SMB_ADDRINFO_S));
                addrbook.num--;
            }
            else if ( i == addrbook.num - 1 )
            {
                addrbook.num--;
            }
            else
            {
                NET_DEBUG("no search item record_id(%d)", op->delete_id[j]);
            }
        }
        break;
    case AB_OP_UPDATE:
        RETURN_VAL_IF(op->u.smb_info.record_id <= 0, NET_WARN, -1);
        for ( i = 0; i < addrbook.num; ++i )
        {
            RETURN_VAL_IF(strcmp(addrbook.info[i].server_name, op->u.smb_info.server_name) == 0 && addrbook.info[i].record_id != op->u.smb_info.record_id, NET_WARN, -2);
        }
        for ( i = 0; i < addrbook.num; ++i )
        {
            if ( addrbook.info[i].record_id == op->u.smb_info.record_id )
            {
                addrbook.info[i] = op->u.smb_info;
                break;
            }
        }
        break;
    default:
        NET_WARN("invalid op(%d)", op->operation);
        break;
    }
    netdata_set_smb_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_SMB_ADDRESS_BOOK_CHANGED, addrbook);
    return 0;
}

#if CONFIG_NET_WHITELIST

void netctx_update_whitelist_switch(NET_CTX_S* net_ctx, uint32_t value)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( netdata_set_whitelist_switch(net_ctx->data_mgr, value) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WHITELIST_SWITCH_CHANGED, value);
        whitelist_update_enable_to_kernel(value);
    }
}

void netctx_update_whitelist_addrbook(NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op)
{
    WHITELIST_ADDRBOOK_S      addrbook;
    uint32_t            i, j;

    RETURN_IF(net_ctx == NULL || op == NULL, NET_WARN);
    RETURN_IF(op->type != AB_TYPE_WL, NET_WARN);

    netdata_get_whitelist_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    switch ( op->operation )
    {
    case AB_OP_ADD:
        RETURN_IF(addrbook.num >= ARRAY_SIZE(addrbook.info), NET_WARN);
        addrbook.info[addrbook.num] = op->u.whitelist_info;
        addrbook.info[addrbook.num].record_id = ++addrbook.idx;
        addrbook.num++;
        break;
    case AB_OP_DELETE:
        BREAK_IF(addrbook.num == 0, NET_WARN);
        for ( j = 0; j < ARRAY_SIZE(op->whitelist_delete_id) && op->whitelist_delete_id[j] > 0; ++j )
        {
            for ( i = 0; i < addrbook.num; ++i )
            {
                if ( addrbook.info[i].record_id == op->whitelist_delete_id[j] )
                {
                    break;
                }
            }
            if ( i < addrbook.num - 1 )
            {
                memcpy(&(addrbook.info[i]), &(addrbook.info[i + 1]), sizeof(WHITELIST_ADDRINFO_S) * (addrbook.num - i - 1));
                NET_DEBUG("remove addrbook.info[%u] record_id(%d)", i, op->whitelist_delete_id[j]);
                memset(&(addrbook.info[addrbook.num - 1]), 0, sizeof(WHITELIST_ADDRINFO_S));
                addrbook.num--;
            }
            else if ( i == addrbook.num - 1 )
            {
                memset(&(addrbook.info[i]), 0, sizeof(WHITELIST_ADDRINFO_S));
                addrbook.num--;
            }
            else
            {
                NET_DEBUG("no search item record_id(%d)", op->whitelist_delete_id[j]);
            }
        }
        break;
    case AB_OP_UPDATE:
        RETURN_IF(op->u.whitelist_info.record_id <= 0, NET_WARN);
        for ( i = 0; i < addrbook.num; ++i )
        {
            if ( addrbook.info[i].record_id == op->u.whitelist_info.record_id )
            {
                addrbook.info[i] = op->u.whitelist_info;
                break;
            }
        }
        break;
    default:
        NET_WARN("invalid op(%d)", op->operation);
        break;
    }
    netdata_set_whitelist_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    netdata_get_whitelist_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook));
    whitelist_update_info_to_kernel(&addrbook);
    NETEVT_NOTIFY_T(EVT_TYPE_NET_WHITELIST_ADDRESS_BOOK_CHANGED, addrbook);
}
#endif /*CONFIG_NET_WHITELIST end*/

void netctx_update_water_mark(NET_CTX_S* net_ctx, uint32_t index, const char* value)
{
    CUSTOM_WATER_MARK_S water_mark;
    int32_t             ret = -1;

    RETURN_IF(net_ctx == NULL || index == 0 || value == NULL, NET_WARN);

    snprintf(water_mark.mark, sizeof(water_mark.mark), "%s", value);
    water_mark.index = index;

    switch ( index )
    {
    case 1:
        ret = netdata_set_water_mark1(net_ctx->data_mgr, value);
        break;
    case 2:
        ret = netdata_set_water_mark2(net_ctx->data_mgr, value);
        break;
    case 3:
        ret = netdata_set_water_mark3(net_ctx->data_mgr, value);
        break;
    case 4:
        ret = netdata_set_water_mark4(net_ctx->data_mgr, value);
        break;
    case 5:
        ret = netdata_set_water_mark5(net_ctx->data_mgr, value);
        break;
    case 6:
        ret = netdata_set_water_mark6(net_ctx->data_mgr, value);
        break;
    default:
        NET_WARN("invalid index(%u)", index);
        break;
    }

    if ( ret == 0 )
    {
        NETEVT_NOTIFY_T(EVT_TYPE_NET_CUSTOM_WATER_MARK_CHANGED, water_mark);
    }
}

void netctx_notify_event_first(NET_CTX_S* net_ctx)
{
    EVT_TYPE_E          ipv4_dhcp_event[IFACE_ID_NUM]    = NET_EVT_ARRAY2(IPV4_DHCP_SWITCH_CHANGED);
    EVT_TYPE_E          ipv4_addr_event[IFACE_ID_NUM]    = NET_EVT_ARRAY3(IPV4_ADDRESS_CHANGED);
    EVT_TYPE_E          ipv4_mask_event[IFACE_ID_NUM]    = NET_EVT_ARRAY3(IPV4_MASK_CHANGED);
    EVT_TYPE_E          ipv4_gtwy_event[IFACE_ID_NUM]    = NET_EVT_ARRAY3(IPV4_GATEWAY_CHANGED);
    EVT_TYPE_E          ipv4_autodns_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV4_AUTODNS_SWITCH_CHANGED);
    EVT_TYPE_E          ipv4_dns0_event[IFACE_ID_NUM]    = NET_EVT_ARRAY2(IPV4_PRIMARY_DNS_CHANGED);
    EVT_TYPE_E          ipv4_dns1_event[IFACE_ID_NUM]    = NET_EVT_ARRAY2(IPV4_SECONDARY_DNS_CHANGED);
    EVT_TYPE_E          ipv6_switch_event[IFACE_ID_NUM]  = NET_EVT_ARRAY2(IPV6_SWITCH_CHANGED);
    EVT_TYPE_E          ipv6_dhcp_event[IFACE_ID_NUM]    = NET_EVT_ARRAY2(IPV6_DHCP_SWITCH_CHANGED);
    EVT_TYPE_E          iface_mac_event[IFACE_ID_NUM]    = NET_EVT_ARRAY3(MAC_CHANGED);
    CUSTOM_WATER_MARK_S water_mark;
    MAIL_GROUPLIST_S    m_grouplist;
    MAIL_ADDRBOOK_S     m_addrbook;
    FTP_ADDRBOOK_S      f_addrbook;
    SMB_ADDRBOOK_S      s_addrbook;
    IFACE_ID_E          ifid;
    char                mfg_name[32];
    char                pdt_name[32];
    char                str[256];
    uint8_t             mac[6];
    uint32_t            flags;
    uint32_t            val;

    netdata_get_mfg_name(net_ctx->data_mgr, mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(net_ctx->data_mgr, pdt_name, sizeof(pdt_name));

    netdata_get_hostname(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_NO_EMPTY(str) ) /* 初始化主机名 */
    {
        sethostname(str, strlen(str));
        NETEVT_NOTIFY_S(EVT_TYPE_NET_HOST_NAME_CHANGED, str);
        NET_DEBUG("notify hostname(%s)", str);
    }

    for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        if ( net_ifctl_get_mac(IFACE_NAME(ifid), mac, sizeof(mac)) == 0 ) /* 上报网卡MAC地址 */
        {
            snprintf(str, sizeof(str), "%02X:%02X:%02X:%02X:%02X:%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
            NET_DEBUG("%s mac(%s)", IFACE_NAME(ifid), str);
            netdata_set_mac_addr(net_ctx->data_mgr, ifid, str);
            NETEVT_NOTIFY_S(iface_mac_event[ifid], str);
        }

        val = netdata_get_ipv4_usedhcp(net_ctx->data_mgr, ifid);
        if ( ipv4_dhcp_event[ifid] > 0 ) /* 上报IPv4 DHCP初始信息 */
        {
            NETEVT_NOTIFY_I(ipv4_dhcp_event[ifid], val);
        }

        if ( val == 0 ) /* 上报IPv4静态模式初始信息 */
        {
            netdata_get_ipv4_addr(net_ctx->data_mgr, ifid, str, sizeof(str));
            NETEVT_NOTIFY_S(ipv4_addr_event[ifid], str);
            netdata_get_ipv4_mask(net_ctx->data_mgr, ifid, str, sizeof(str));
            NETEVT_NOTIFY_S(ipv4_mask_event[ifid], str);
            netdata_get_ipv4_gtwy(net_ctx->data_mgr, ifid, str, sizeof(str));
            NETEVT_NOTIFY_S(ipv4_gtwy_event[ifid], str);
        }
        else /* DHCP模式下，清空备份的地址记录，待netlink收到内核上报信息后再更新 */
        {
            netdata_set_ipv4_addr(net_ctx->data_mgr, ifid, "");
            netdata_set_ipv4_mask(net_ctx->data_mgr, ifid, "");
            netdata_set_ipv4_gtwy(net_ctx->data_mgr, ifid, "");
        }

        val = netdata_get_ipv4_autodns(net_ctx->data_mgr, ifid);
        if ( ipv4_autodns_event[ifid] > 0 ) /* 上报autodns初始信息 */
        {
            NETEVT_NOTIFY_I(ipv4_autodns_event[ifid], val);
        }

        if ( val == 0 ) /* 上报手动DNS初始信息 */
        {
            netdata_get_ipv4_dns0(net_ctx->data_mgr, ifid, str, sizeof(str));
            NETEVT_NOTIFY_S(ipv4_dns0_event[ifid], str);
            netdata_get_ipv4_dns1(net_ctx->data_mgr, ifid, str, sizeof(str));
            NETEVT_NOTIFY_S(ipv4_dns1_event[ifid], str);
        }
        else /* DHCP模式下，清空备份的地址记录，待netlink收到内核上报信息后再更新 */
        {
            netdata_set_ipv4_dns0(net_ctx->data_mgr, ifid, "");
            netdata_set_ipv4_dns1(net_ctx->data_mgr, ifid, "");
        }

        val = netdata_get_ipv6_switch(net_ctx->data_mgr, ifid);
        NETEVT_NOTIFY_I(ipv6_switch_event[ifid], val);

        val = netdata_get_ipv6_usedhcp(net_ctx->data_mgr, ifid);
        NETEVT_NOTIFY_I(ipv6_dhcp_event[ifid], val);
    }

    val = netdata_get_wired_speed(net_ctx->data_mgr);
    if ( val != IFACE_SPEED_AUTO )
    {
        pi_msleep(600);
        for ( int32_t i = 1; i < 20 && net_ifctl_set_speed(IFACE_ETH, val) != 0; ++i )
        {
            NET_WARN("set %s speed mode(%u) failed, retry %d times after 100ms", IFACE_ETH, val, i);
            pi_msleep(100);
        }
    }
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ETH_SPEED_CHANGED, val);
    NET_DEBUG("notify wired speed(%u)", val);

#if CONFIG_NET_RAWPRINT /* 上报RAWPrint初始信息 */
    val = netdata_get_rawprint_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_RAWPRINT_SWITCH_CHANGED, val);
    NET_DEBUG("notify rawprint switch(%u)", val);

    val = netdata_get_rawprint_port(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_RAWPRINT_PORT_CHANGED, val);
    NET_DEBUG("notify rawprint port(%u)", val);
#endif  /* CONFIG_NET_RAWPRINT */

#if CONFIG_NET_LPD /* 上报LPD初始信息 */
    val = netdata_get_lpd_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_LPD_SWITCH_CHANGED, val);
    NET_DEBUG("notify lpd switch(%u)", val);
#endif /* CONFIG_NET_LPD */

#if CONFIG_NET_WSD /* 上报WSD初始信息 */
    val = netdata_get_wsd_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_WSD_SWITCH_CHANGED, val);
    NET_DEBUG("notify wsd switch(%u)", val);
#endif /* CONFIG_NET_WSD */

#if CONFIG_NET_SLP
    val = netdata_get_slp_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SLP_SWITCH_CHANGED, val);
    NET_DEBUG("notify slp switch(%u)", val);
#endif

#if CONFIG_NET_BONJOUR /* 上报Bonjour初始信息 */
    val = netdata_get_bonjour_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_BONJOUR_SWITCH_CHANGED, val);
    NET_DEBUG("notify bonjour switch(%u)", val);

    netdata_get_bonjour_server(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM && net_ifctl_get_mac(IFACE_NAME(ifid), mac, sizeof(mac)) != 0; ++ifid );

        snprintf(str, sizeof(str), "%s %s Series %02X%02X%02X", mfg_name, pdt_name, mac[3], mac[4], mac[5]);
        NET_DEBUG("generate bonjour server(%s) base on %s", str, IFACE_NAME(ifid));
        netdata_set_bonjour_server(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_BONJOUR_SERVER_CHANGED, str);
    NET_DEBUG("notify bonjour server(%s)", str);
#endif /* CONFIG_NET_BONJOUR */

#if CONFIG_NET_SNMP /* 上报SNMP初始信息 */
    val = netdata_get_snmp_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SNMP_SWITCH_CHANGED, val);
    NET_DEBUG("notify snmp switch(%u)", val);

    flags = netdata_get_snmp_version_flags(net_ctx->data_mgr);
    NET_DEBUG("snmp version flags(0x%x)", flags);

    val = (!!(flags & SNMP_SUPPORT_V1V2C));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SNMP_V1V2C_SWITCH_CHANGED, val);
    netdata_set_snmp_v1v2c_switch(net_ctx->data_mgr, val);
    NET_DEBUG("notify snmp V1V2C switch(%u)", val);

    val = (!!(flags & SNMP_SUPPORT_V3));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SNMP_V3_SWITCH_CHANGED, val);
    netdata_set_snmp_v3_switch(net_ctx->data_mgr, val);
    NET_DEBUG("notify snmp V3 switch(%u)", val);

    netdata_get_snmp_v1_community(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        snprintf(str, sizeof(str), "public");
        netdata_set_snmp_v1_community(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V1_COMMUNITY_CHANGED, str);
    NET_DEBUG("notify snmp v1 community(%s)", str);

    netdata_get_snmp_v2_community(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        snprintf(str, sizeof(str), "v2cpublic");
        netdata_set_snmp_v2_community(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V2C_COMMUNITY_CHANGED, str);
    NET_DEBUG("notify snmp v2c community(%s)", str);

    netdata_get_snmp_v3_community(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        snprintf(str, sizeof(str), "%s-v3", mfg_name);
        netdata_set_snmp_v3_community(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_COMMUNITY_CHANGED, str);
    NET_DEBUG("notify snmp v3 community(%s)", str);

    netdata_get_snmp_v3_user_name(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        snprintf(str, sizeof(str), "Snmpv3_user");
        netdata_set_snmp_v3_user_name(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_USERNAME_CHANGED, str);
    NET_DEBUG("notify snmp v3 username(%s)", str);

    netdata_get_snmp_v3_auth_pswd(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        snprintf(str, sizeof(str), "%s_auth_pass_000", mfg_name);
        netdata_set_snmp_v3_auth_pswd(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_AUTHPASS_CHANGED, str);
    NET_DEBUG("notify snmp v3 auth psk(%s)", str);

    netdata_get_snmp_v3_priv_pswd(net_ctx->data_mgr, str, sizeof(str));
    if ( STRING_IS_EMPTY(str) )
    {
        snprintf(str, sizeof(str), "%s_priv_pass_000", mfg_name);
        netdata_set_snmp_v3_priv_pswd(net_ctx->data_mgr, str);
    }
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SNMP_V3_PRIVPASS_CHANGED, str);
    NET_DEBUG("notify snmp v3 priv psk(%s)", str);
#endif /* CONFIG_NET_SNMP */

#if CONFIG_NET_SMTP /* 上报SMTP初始信息 */
    netdata_get_smtp_sender_addr(net_ctx->data_mgr, str, sizeof(str));
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_SENDER_ADDRESS_CHANGED, str);
    NET_DEBUG("notify smtp sender addr(%s)", str);

    netdata_get_smtp_server_addr(net_ctx->data_mgr, str, sizeof(str));
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_SERVER_ADDRESS_CHANGED, str);
    NET_DEBUG("notify smtp server addr(%s)", str);

    val = netdata_get_smtp_server_port(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SMTP_SERVER_PORT_CHANGED, val);
    NET_DEBUG("notify smtp server port(%u)", val);

    val = netdata_get_smtp_server_auth(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SMTP_SERVER_AUTH_CHANGED, val);
    NET_DEBUG("notify smtp server_auth(%u)", val);

    val = netdata_get_smtp_sec_mode(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_SMTP_SEC_MODE_CHANGED, val);
    NET_DEBUG("notify smtp sec_mode(%u)", val);

    netdata_get_smtp_username(net_ctx->data_mgr, str, sizeof(str));
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_USERNAME_CHANGED, str);
    NET_DEBUG("notify smtp username(%s)", str);

    netdata_get_smtp_password(net_ctx->data_mgr, str, sizeof(str));
    NETEVT_NOTIFY_S(EVT_TYPE_NET_SMTP_PASSWORD_CHANGED, str);
    NET_DEBUG("notify smtp password(%s)", str);

    /* 上报邮件告警的初始配置 */
    flags = netdata_get_alarm_status_flags(net_ctx->data_mgr);
    NET_DEBUG("alarm status flags(0x%x)", flags);

    val = (!!(flags & EMAIL_ALARM_PAPER_EMPTY));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_PAPER_EMPTY_CHANGED, val);
    netdata_set_alarm_paper_empty(net_ctx->data_mgr, val);
    NET_DEBUG("notify alarm paper empty(%u)", val);

    val = (!!(flags & EMAIL_ALARM_PAPER_JAM));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_PAPER_JAM_CHANGED, val);
    netdata_set_alarm_paper_jam(net_ctx->data_mgr, val);
    NET_DEBUG("notify alarm paper jam(%u)", val);

    val = (!!(flags & EMAIL_ALARM_TONER_EMPTY));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_TONER_EMPTY_CHANGED, val);
    netdata_set_alarm_toner_empty(net_ctx->data_mgr, val);
    NET_DEBUG("notify alarm toner empty(%u)", val);

    val = (!!(flags & EMAIL_ALARM_TONER_LOW));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_TONER_LOW_CHANGED, val);
    netdata_set_alarm_toner_low(net_ctx->data_mgr, val);
    NET_DEBUG("notify alarm toner low(%u)", val);

    val = (!!(flags & EMAIL_ALARM_WASTE_TONER));
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ALARM_WASTE_TONER_CHANGED, val);
    netdata_set_alarm_waste_toner(net_ctx->data_mgr, val);
    NET_DEBUG("notify alarm waste toner(%u)", val);
#endif /* CONFIG_NET_SMTP */

#if CONFIG_NET_WHITELIST /* 上报WHITELIST初始信息 */
    val = netdata_get_whitelist_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_WHITELIST_SWITCH_CHANGED, val);
#endif

    /* 上报自定义水印 */
    for ( water_mark.index = 1; water_mark.index <= 6; water_mark.index++ )
    {
        water_mark.mark[0] = '\0';
        switch ( water_mark.index )
        {
        case 1 : netdata_get_water_mark1(net_ctx->data_mgr, water_mark.mark, sizeof(water_mark.mark)); break;
        case 2 : netdata_get_water_mark2(net_ctx->data_mgr, water_mark.mark, sizeof(water_mark.mark)); break;
        case 3 : netdata_get_water_mark3(net_ctx->data_mgr, water_mark.mark, sizeof(water_mark.mark)); break;
        case 4 : netdata_get_water_mark4(net_ctx->data_mgr, water_mark.mark, sizeof(water_mark.mark)); break;
        case 5 : netdata_get_water_mark5(net_ctx->data_mgr, water_mark.mark, sizeof(water_mark.mark)); break;
        case 6 : netdata_get_water_mark6(net_ctx->data_mgr, water_mark.mark, sizeof(water_mark.mark)); break;
        default: break;
        }
        if ( STRING_NO_EMPTY(water_mark.mark) )
        {
            NETEVT_NOTIFY_T(EVT_TYPE_NET_CUSTOM_WATER_MARK_CHANGED, water_mark);
            NET_DEBUG("water_mark[%u](%s)", water_mark.index, water_mark.mark);
        }
    }

    netdata_get_mail_grouplist(net_ctx->data_mgr, &m_grouplist, sizeof(m_grouplist));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED, m_grouplist);

    netdata_get_mail_addrbook(net_ctx->data_mgr, &m_addrbook, sizeof(m_addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED, m_addrbook);

    netdata_get_ftp_addrbook(net_ctx->data_mgr, &f_addrbook, sizeof(f_addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED, f_addrbook);

    netdata_get_smb_addrbook(net_ctx->data_mgr, &s_addrbook, sizeof(s_addrbook));
    NETEVT_NOTIFY_T(EVT_TYPE_NET_SMB_ADDRESS_BOOK_CHANGED, s_addrbook);

    /* 上报webpage导出日志功能开关初始配置 */
    val =netdata_get_export_log_switch(net_ctx->data_mgr);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_EXPORT_LOG_CHANGED, val);
    NET_DEBUG("notify net export log changed(%u)", val);

    NETEVT_NOTIFY_S(EVT_TYPE_NET_INITIALIZE_DONE, NULL);
}

NET_CTX_S* network_context_construct(void)
{
    PRIV_INFO_S*    priv = NULL;
    int32_t         rs = -1;

    RETURN_VAL_IF(s_network_context != NULL, NET_TRACE, s_network_context);

    NET_DEBUG("network context start construct");
    s_network_context = (NET_CTX_S *)pi_zalloc(sizeof(NET_CTX_S));
    RETURN_VAL_IF(s_network_context == NULL, NET_WARN, NULL);

    do
    {
        priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        BREAK_IF(priv == NULL, NET_WARN);
        s_network_context->priv = priv;

        priv->sysstat_subj = subject_construct(SUBJECT_NET_STS_ID, s_network_context);
        BREAK_IF(priv->sysstat_subj == NULL, NET_WARN);

        priv->netport_subj = subject_construct(SUBJECT_NET_STS_ID, s_network_context);
        BREAK_IF(priv->netport_subj == NULL, NET_WARN);

        priv->netlink_subj = subject_construct(SUBJECT_NET_STS_ID, s_network_context);
        BREAK_IF(priv->netlink_subj == NULL, NET_WARN);

        priv->sysjob_subj  = subject_construct(SUBJECT_NET_STS_ID, s_network_context);
        BREAK_IF(priv->sysjob_subj == NULL, NET_WARN);

        s_network_context->threads_pool = threads_pool_create(32);  ///< 创建32个预备线程的线程池
        BREAK_IF(s_network_context->threads_pool == NULL, NET_WARN);

        s_network_context->data_mgr = netdata_manager_create();     ///< 创建网络数据管理子模块
        BREAK_IF(s_network_context->data_mgr == NULL, NET_WARN);

        rs = netevent_client_construct(s_network_context);
    }
    while ( 0 );

    if ( rs != 0 )
    {
        NET_WARN("construct context failed(%d)", rs);
        network_context_destruct();
    }
    return s_network_context;
}

void network_context_destruct(void)
{
    DECL_PRIV(s_network_context, PRIV_INFO_S, priv);

    RETURN_IF(s_network_context == NULL, NET_WARN);

    if ( priv )
    {
        if ( priv->sysstat_subj )
        {
            subject_destruct(priv->sysstat_subj);
        }
        if ( priv->netport_subj )
        {
            subject_destruct(priv->netport_subj);
        }
        if ( priv->netlink_subj )
        {
            subject_destruct(priv->netlink_subj);
        }
        if ( priv->sysjob_subj)
        {
            subject_destruct(priv->sysjob_subj);
        }
    }
    netevent_client_destruct(s_network_context);
    if ( s_network_context->threads_pool )
    {
        threads_pool_destroy(s_network_context->threads_pool);
    }
    if ( s_network_context->data_mgr )
    {
        netdata_manager_destroy(s_network_context->data_mgr);
    }
    memset(s_network_context, 0, sizeof(NET_CTX_S));
    pi_free(s_network_context);
    s_network_context = NULL;
}

/**
 *@}
 */
