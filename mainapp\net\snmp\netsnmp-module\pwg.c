/*
 * Note: this file originally auto-generated by mib2c using
 *        : mib2c.old-api.conf 17851 2009-11-30 16:46:06Z dts12 $
 */

#include <net-snmp/net-snmp-config.h>
#include <net-snmp/net-snmp-includes.h>
#include <net-snmp/agent/net-snmp-agent-includes.h>
#include "pwg.h"
#include "snmpipc.h"

static int snmp_lock = 0;
static SNMP_MAP_S* snmp_map = NULL;
static PPM_MIB_S* pwg_map = NULL;

/* 
 * pwg_variables_oid:
 *   this is the top level oid that we want to register under.  This
 *   is essentially a prefix, with the suffix appearing in the
 *   variable below.
 */

oid pwg_variables_oid[] = { 1,3,6,1,4,1,2699 };

/* 
 * variable8 pwg_variables:
 *   this variable defines function callbacks and type return information 
 *   for the pwg mib section 
 */
#define PPMGENERALNATURALLANGUAGE		1
#define PPMGENERALNUMBEROFPRINTERS		2
#define PPMGENERALNUMBEROFPORTS		    3

#define PPMPRINTERINDEX		            1
#define PPMPRINTERNAME		            2
#define PPMPRINTERIEEE1284DEVICEID		3
#define PPMPRINTERNUMBEROFPORTS		    4
#define PPMPRINTERPREFERREDPORTINDEX    5
#define PPMPRINTERHRDEVICEINDEX		    6
#define PPMPRINTERSNMPCOMMUNITYNAME		7
#define PPMPRINTERSNMPQUERYENABLED		8

#define PPMPRINTER_MAX_IND              1

#define PPMPORTINDEX		            1
#define PPMPORTENABLED		            2
#define PPMPORTNAME		                3
#define PPMPORTSERVICENAMEORURI		    4
#define PPMPORTPROTOCOLTYPE		        5
#define PPMPORTPROTOCOLTARGETPORT		6
#define PPMPORTPROTOCOLALTSOURCEENABLED	7
#define PPMPORTPRTCHANNELINDEX		    8
#define PPMPORTLPRBYTECOUNTENABLED		9

#define PPMPORT_MAX_IND                 1

struct variable8 pwg_variables[] = 
{
    /*  magic number,                   variable type , ro/rw , callback fn,          L, oidsuffix */
    {PPMGENERALNATURALLANGUAGE,         ASN_OCTET_STR,  RONLY ,  var_pwg,             5,  { 1,2,1,1,1 }},
    {PPMGENERALNUMBEROFPRINTERS,        ASN_GAUGE,      RONLY ,  var_pwg,             5,  { 1,2,1,1,2 }},
    {PPMGENERALNUMBEROFPORTS,           ASN_GAUGE,      RONLY ,  var_pwg,             5,  { 1,2,1,1,3 }},

    {PPMPRINTERINDEX,                   ASN_INTEGER,    RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 1 }},
    {PPMPRINTERNAME,                    ASN_OCTET_STR,  RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 2 }},
    {PPMPRINTERIEEE1284DEVICEID,        ASN_OCTET_STR,  RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 3 }},
    {PPMPRINTERNUMBEROFPORTS,           ASN_GAUGE,      RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 4 }},
    {PPMPRINTERPREFERREDPORTINDEX,      ASN_INTEGER,    RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 5 }},
    {PPMPRINTERHRDEVICEINDEX,           ASN_INTEGER,    RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 6 }},
    {PPMPRINTERSNMPCOMMUNITYNAME,       ASN_OCTET_STR,  RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 7 }},
    {PPMPRINTERSNMPQUERYENABLED,        ASN_INTEGER,    RONLY,   var_ppmPrinterTable, 7,  { 1,2,1,2,1 , 1, 8 }},

    {PPMPORTINDEX,                      ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 1,1 }},
    {PPMPORTENABLED,                    ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 2,1 }},
    {PPMPORTNAME,                       ASN_OCTET_STR,  RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 3,1 }},
    {PPMPORTSERVICENAMEORURI,           ASN_OCTET_STR,  RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 4,1 }},
    {PPMPORTPROTOCOLTYPE,               ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 5,1 }},
    {PPMPORTPROTOCOLTARGETPORT,         ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 6,1 }},
    {PPMPORTPROTOCOLALTSOURCEENABLED,   ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 7,1 }},
    {PPMPORTPRTCHANNELINDEX,            ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 8,1 }},
    {PPMPORTLPRBYTECOUNTENABLED,        ASN_INTEGER,    RONLY,   var_ppmPortTable,    8,  { 1,2,1,3,1 , 1, 9,1 }},
};
/*    (L = length of the oidsuffix) */

/** Initializes the pwg module */
    void
init_pwg(void)
{

    DEBUGMSGTL(("pwg", "Initializing\n"));

    snmp_map = (SNMP_MAP_S *)ipc_shm(&snmp_lock, SNMP_MAP_NAME, sizeof(SNMP_MAP_S));

    if (snmp_map) {
        pwg_map = &snmp_map->ppm;
        /* register ourselves with the agent to handle our mib tree */
        REGISTER_MIB("pwg",         pwg_variables, variable8, pwg_variables_oid);
    } else {
        fprintf(stderr, "Failed to create shared memory so that PRINTER-PORT-MONITOR-MIB module will not response\n");
    }

    /* place any other initialization junk you need here */
}

/*
 * var_pwg():
 *   This function is called every time the agent gets a request for
 *   a scalar variable that might be found within your mib section
 *   registered above.  It is up to you to do the right thing and
 *   return the correct value.
 *     You should also correct the value of "var_len" if necessary.
 *
 *   Please see the documentation for more information about writing
 *   module extensions, and check out the examples in the examples
 *   and mibII directories.
 */
    unsigned char *
     var_pwg(struct variable *vp, 
             oid     *name, 
             size_t  *length, 
             int     exact, 
             size_t  *var_len, 
             WriteMethod **write_method)
{
    /* variables we may use later */
    static long long_ret;
    static u_long ulong_ret;
    static unsigned char string[SPRINT_MAX_LEN];
    static oid objid[MAX_OID_LEN];
    static struct counter64 c64;

    if (header_generic(vp,name,length,exact,var_len,write_method)
            == MATCH_FAILED )
    {
        return NULL;
    }

    /* 
     * this is where we do the value assignments for the mib results.
     */
    switch(vp->magic) {
        case PPMGENERALNATURALLANGUAGE:
            flock(snmp_lock, LOCK_EX);
            snprintf(string, sizeof(string), pwg_map->_ppmGeneralNaturalLanguage);
            flock(snmp_lock, LOCK_UN);
            *var_len = strlen(string);
            return (u_char*) string;
        case PPMGENERALNUMBEROFPRINTERS:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmGeneralNumberOfPrinters;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;
        case PPMGENERALNUMBEROFPORTS:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmGeneralNumberOfPorts;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;
        default:
            ERROR_MSG("");
    }
    return NULL;
}


/*
 * var_ppmPrinterTable():
 *   Handle this table separately from the scalar value case.
 *   The workings of this are basically the same as for var_pwg above.
 */
unsigned char *var_ppmPrinterTable(struct variable *vp,
        oid     *name,
        size_t  *length,
        int     exact,
        size_t  *var_len,
        WriteMethod **write_method)
{
    /* variables we may use later */
    static long long_ret;
    static u_long ulong_ret;
    static unsigned char string[SPRINT_MAX_LEN];
    static oid objid[MAX_OID_LEN];
    static struct counter64 c64;

    /* 
     * This assumes that the table is a 'simple' table.
     *	See the implementation documentation for the meaning of this.
     *	You will need to provide the correct value for the TABLE_SIZE parameter
     *
     * If this table does not meet the requirements for a simple table,
     *	you will need to provide the replacement code yourself.
     *	Mib2c is not smart enough to write this for you.
     *    Again, see the implementation documentation for what is required.
     */
    if (header_simple_table(vp,name,length,exact,var_len,write_method, PPMPRINTER_MAX_IND)
            == MATCH_FAILED )
    {
        return NULL;
    }

    /* 
     * this is where we do the value assignments for the mib results.
     */
    switch(vp->magic) {
        case PPMPRINTERINDEX:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPrinterIndex;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;
            return NULL;

        case PPMPRINTERNAME:
            flock(snmp_lock, LOCK_EX);
            snprintf(string, sizeof(string), pwg_map->_ppmPrinterName);
            flock(snmp_lock, LOCK_UN);
            *var_len = strlen(string);
            return (u_char*) string;

        case PPMPRINTERIEEE1284DEVICEID:
            flock(snmp_lock, LOCK_EX);
            snprintf(string, sizeof(string), pwg_map->_ppmPrinterIEEE1284DeviceId);
            flock(snmp_lock, LOCK_UN);
            *var_len = strlen(string);
            return (u_char*) string;

        case PPMPRINTERNUMBEROFPORTS:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPrinterNumberOfPorts;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPRINTERPREFERREDPORTINDEX:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPrinterPreferredPortIndex;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPRINTERHRDEVICEINDEX:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPrinterHrDeviceIndex;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPRINTERSNMPCOMMUNITYNAME:
            flock(snmp_lock, LOCK_EX);
            snprintf(string, sizeof(string), pwg_map->_ppmPrinterSnmpCommunityName);
            flock(snmp_lock, LOCK_UN);
            *var_len = strlen(string);
            return (u_char*) string;

        case PPMPRINTERSNMPQUERYENABLED:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPrinterSnmpQueryEnabled;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        default:
            ERROR_MSG("");
    }
    return NULL;
}
/*
 * var_ppmPortTable():
 *   Handle this table separately from the scalar value case.
 *   The workings of this are basically the same as for var_pwg above.
 */
    unsigned char *
     var_ppmPortTable(struct variable *vp,
             oid     *name,
             size_t  *length,
             int     exact,
             size_t  *var_len,
             WriteMethod **write_method)
{
    /* variables we may use later */
    static long long_ret;
    static u_long ulong_ret;
    static unsigned char string[SPRINT_MAX_LEN];
    static oid objid[MAX_OID_LEN];
    static struct counter64 c64;
    int i;

    /* 
     * This assumes that the table is a 'simple' table.
     *	See the implementation documentation for the meaning of this.
     *	You will need to provide the correct value for the TABLE_SIZE parameter
     *
     * If this table does not meet the requirements for a simple table,
     *	you will need to provide the replacement code yourself.
     *	Mib2c is not smart enough to write this for you.
     *    Again, see the implementation documentation for what is required.
     */
    if (header_simple_table(vp,name,length,exact,var_len,write_method, PPMPORT_MAX_IND)
            == MATCH_FAILED )
        return NULL;

    /* 
     * this is where we do the value assignments for the mib results.
     */
    switch(vp->magic) {
        case PPMPORTINDEX:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortIndex;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;
            return NULL;

        case PPMPORTENABLED:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortEnabled;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPORTNAME:
            flock(snmp_lock, LOCK_EX);
            snprintf(string, sizeof(string), pwg_map->_ppmPortName);
            flock(snmp_lock, LOCK_UN);
            *var_len = strlen(string);
            return (u_char*) string;

        case PPMPORTSERVICENAMEORURI:
            flock(snmp_lock, LOCK_EX);
            snprintf(string, sizeof(string), pwg_map->_ppmPortServiceNameOrURI);
            flock(snmp_lock, LOCK_UN);
            *var_len = strlen(string);
            return (u_char*) string;

        case PPMPORTPROTOCOLTYPE:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortProtocolType;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPORTPROTOCOLTARGETPORT:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortProtocolTargetPort;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPORTPROTOCOLALTSOURCEENABLED:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortProtocolAltSourceEnabled;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPORTPRTCHANNELINDEX:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortPrtChannelIndex;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        case PPMPORTLPRBYTECOUNTENABLED:
            flock(snmp_lock, LOCK_EX);
            long_ret = pwg_map->_ppmPortLprByteCountEnabled;
            flock(snmp_lock, LOCK_UN);
            return (u_char*) &long_ret;

        default:
            ERROR_MSG("");
    }
    return NULL;
}


