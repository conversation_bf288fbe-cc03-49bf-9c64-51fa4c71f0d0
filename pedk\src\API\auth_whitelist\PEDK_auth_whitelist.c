#include "PEDK_auth_whitelist.h"
#include "PEDK_event.h"
#include "runtime/runtime.h"
#include <quickjs.h>
#include "cJSON.h"
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>

#define countof(x) (sizeof(x) / sizeof((x)[0]))
#define Log(format, ...) printf("[whiltelist] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);
#define Error(format, ...) printf("[whiltelist_error] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

#define FILE_PERMITMACLIST "/pesf_data/PermitMACList.json"
#define FILE_PROHIBITMACLIST "/pesf_data/ProhibitMACList.json"
#define JS_EOPNOTSUPP "EOPNOTSUPP"
#define JS_EXIT_SUCCESS "EXIT_SUCCESS"
#define JS_EXIT_FAILURE "EXIT_FAILURE"
#define JS_ETIMEDOUT "ETIMEDOUT"
#define JS_EALLOWED "EALLOWED"
#define JS_ENOTALLOWED "ENOTALLOWED"

#define ERROR -1
#define NORMAL 0

static int file_len = 0;

static int get_file_len(char* target)
{
	FILE *filep;
	int len = 0;
	filep = fopen(target, "rb");
	if(filep == NULL)
	{
		Error("can't open %s\n", target);
		return ERROR;
	}

	fseek(filep, 0, SEEK_END);
	len = ftell(filep);
	//printf("file len : %d", len);
	fclose(filep);

	return len;
}


/*setMACaddrFilter*/
/*向白名单/黑名单新增MACaddress时，查看黑名单/白名单是否存在该MACaddress
若存在，即移除，确保两个名单中不存在同一MACaddress*/
static int setMACaddrFilter(char *filter_path, char *set_addr)
{
    FILE *fp = fopen(filter_path, "r");
    if(fp == NULL)
    {
        Error("can't open %s\n", filter_path);
        return ERROR;
    }
    //读取文件内容到buffer

    int length = 0;
    //getdelim(&buffer, &length, '\0', fp);
    fseek(fp, 0, SEEK_END);
    length = ftell(fp);
    fseek(fp, 0, SEEK_SET);
    char *buffer = (char *)malloc(length);
    fread(buffer,sizeof(char), length,fp);
    fclose(fp);

    //parser JSON
    cJSON *root = cJSON_Parse(buffer);
    free(buffer);
    if(root == NULL)
    {
        Error("can't parser json file %s\n", filter_path);
        return ERROR;
    }

    //parser array "MACaddress"
    cJSON *mac_array = cJSON_GetObjectItem(root, "MACaddress");
    if((mac_array == NULL)||(mac_array->type != cJSON_Array))
    {
        cJSON_Delete(root);
        Error(" parser MACaddress json type error \n");
        return ERROR;
    }

    //遍历mac_array, 查找set_addr是否存在
    int found_flag = 0;
    for(int i = 0; i < cJSON_GetArraySize(mac_array); i++)
    {
        cJSON *mac_str = cJSON_GetArrayItem(mac_array, i);
        if((mac_str->type == cJSON_String) && (strcmp(mac_str->string, set_addr) == 0))
        {
            //set_addr存在,移除
            cJSON_DeleteItemFromArray(mac_array, i);
            found_flag = 1;
            break;
        }
    }
    if(found_flag) //set_addr存在,移除后需要重新序列化JSON并写入文件
    {
        char *output_str = cJSON_Print(root);
        printf("file %s update:[%s]\n", filter_path, output_str);
        fp = fopen(filter_path, "w");
        if(fp)
        {
            fprintf(fp, "%s", output_str);
            fclose(fp);
        }
        else
        {
            Error("can't write new json to  %s\n", filter_path);
            free(output_str);
            return ERROR;
        }
        free(output_str);
        cJSON_Delete(root);
        return NORMAL;
    }

    //set_addr不存在
    cJSON_Delete(root);
    return NORMAL;
}

/*取得可以控制功能点的列表*/
/*PermitIPList.json/ProhibitMACList.json
{
    "MACaddress":[
        "11:22:33:44:55:66",
        "22:33:44:55:66:77"
    ]
}
*/
static char* getPermitMACList(char **target)
{
    Log("getPermitMACList \n");
	char* ret = JS_EXIT_SUCCESS;
	int fd = 0;
	char *tmp = NULL;
	cJSON* cjson_whitelist = NULL;
	cJSON* cjson_app = NULL;
	fd = open(FILE_PERMITMACLIST, O_RDONLY);
	if(fd < 0)
	{
		Error("can't open %s", FILE_PERMITMACLIST);
		return JS_EXIT_FAILURE;
	}

	tmp = malloc(file_len);
	if(-1 == read(fd, tmp, file_len))
	{
		Error("can't read %s error : %s\n", FILE_PERMITMACLIST, strerror(errno));
        close(fd);
        free(tmp);
        return JS_EXIT_FAILURE;
	}
	close(fd);
    Log("getPermitIPList parse--- len(%d)\n%s\n\n", file_len, tmp);
	cjson_app = cJSON_Parse(tmp);
	if(cjson_app == NULL || cjson_app->type == cJSON_NULL)
	{
		Error("json error\n");
		ret = JS_EXIT_FAILURE;
	}
	else
	{
		cjson_whitelist = cJSON_GetObjectItem(cjson_app, "MACaddress");
		*target = cJSON_Print(cjson_whitelist);
        Log("getPermitMACList---%s--- \n",cJSON_Print(cjson_whitelist));
        ret = JS_EXIT_SUCCESS;
	}
	cJSON_Delete(cjson_app);

	free(tmp);

    return ret;
}

/*设置白名单的列表*/
static char* setPermitMACList(char *target) //target:macaddress
{
    Log("setPermitMACList \n");
	int fd = 0;
	char *tmp = NULL;
	char* ret = JS_EXIT_SUCCESS;
	cJSON* cjson_funlist = NULL;
	cJSON* cjson_array = NULL;
	int file_len = 0;
    int addr_len = 0;

	file_len = get_file_len(FILE_PERMITMACLIST);    //获取文件大小
    Log("setPermitIPList file len (%d) \n",file_len);
	if(file_len == ERROR)
	{
		Error("can't get %s len\n", FILE_PERMITMACLIST);
		return JS_EXIT_FAILURE;
	}
	fd = open(FILE_PERMITMACLIST, O_RDWR);
	if(fd < 0)
	{
		Error("can't open %s\n", FILE_PERMITMACLIST);
		return JS_EXIT_FAILURE;
	}
    addr_len = strlen(target);
	tmp = malloc(file_len + addr_len + 5);            //分配文件大小＋新增macaddress大小,"xx:xx:xx:xx:xx:xx"
	if(-1 == read(fd, tmp, file_len))
	{
		Error("can't read %s error : %s\n", FILE_PERMITMACLIST, strerror(errno));
        close(fd);
        free(tmp);
        tmp = NULL;
		return JS_EXIT_FAILURE;
	}
	cjson_funlist = cJSON_Parse(tmp);
	if(cjson_funlist == NULL || cjson_funlist->type == cJSON_NULL)
	{
		Error("json error\n");
        close(fd);
        free(tmp);
        tmp = NULL;
		return JS_EXIT_FAILURE;
	}
	else{
		cjson_array = cJSON_GetObjectItem(cjson_funlist, "MACaddress");
		cJSON_AddItemToArray(cjson_array, cJSON_CreateString((const char*)target));

		memset(tmp, 0, file_len + addr_len + 5);
		strcpy(tmp, cJSON_Print(cjson_funlist));
		Log("setPermitMACList json:\n%s\n", cJSON_Print(cjson_funlist));

		if(-1 == lseek(fd, 0, SEEK_SET))
        {
			Error("lseek file %s error : %s\n", FILE_PERMITMACLIST, strerror(errno));
            close(fd);
	        cJSON_Delete(cjson_funlist);

	        free(tmp);
	        tmp = NULL;

            return JS_EXIT_FAILURE;
		}
		if(-1 == write(fd, tmp, file_len + addr_len + 5))
		{
			Error("write file %s error : %s\n", FILE_PERMITMACLIST, strerror(errno));
			ret = JS_EXIT_FAILURE;
		}
	}
	close(fd);
	cJSON_Delete(cjson_funlist);

	free(tmp);
	tmp = NULL;
    /*if(setMACaddrFilter(FILE_PROHIBITMACLIST, target) == ERROR)
    {
        Error("can't remove MACaddress %s from ProhibitMACList.json\n", target);
    }*/
    return ret;
}

/*取得黑名单的列表*/
static char* getProhibitMACList(char **target)
{
    Log("getProhibitIPList \n");
	char* ret = JS_EXIT_SUCCESS;
	int fd = 0;
	char *tmp = NULL;
	cJSON* cjson_whitelist = NULL;
	cJSON* cjson_app = NULL;

	fd = open(FILE_PROHIBITMACLIST, O_RDONLY);
	if(fd < 0)
	{
		Error("can't open %s", FILE_PROHIBITMACLIST);
		return JS_EXIT_FAILURE;
	}

	tmp = malloc(file_len);
	if(-1 == read(fd, tmp, file_len))
	{
		Error("can't read %s error : %s\n", FILE_PROHIBITMACLIST, strerror(errno));
        close(fd);
        free(tmp);
        return JS_EXIT_FAILURE;
	}
	close(fd);

	cjson_app = cJSON_Parse(tmp);
	if(cjson_app == NULL || cjson_app->type == cJSON_NULL)
	{
		Error("json error\n");
		ret = JS_EXIT_FAILURE;
	}
	else
	{
		cjson_whitelist = cJSON_GetObjectItem(cjson_app, "MACaddress");
		*target = cJSON_Print(cjson_whitelist);
        Log("getProhibitIPList ----%s---- \n",cJSON_Print(cjson_whitelist));
        ret = JS_EXIT_SUCCESS;
	}
	cJSON_Delete(cjson_app);

	free(tmp);

    return ret;
}

/*设置黑名单的列表*/
static char* setProhibitMACList(char *target)
{
    Log("setProhibitIPList \n");
	int fd = 0;
	char *tmp = NULL;
	char* ret = JS_EXIT_SUCCESS;
	cJSON* cjson_funlist = NULL;
	cJSON* cjson_array = NULL;
	int file_len = 0;
    int addr_len = 0;

	file_len = get_file_len(FILE_PROHIBITMACLIST);
	if(file_len == ERROR){
		Error("can't get %s len\n", FILE_PROHIBITMACLIST);
		return JS_EXIT_FAILURE;
	}
	fd = open(FILE_PROHIBITMACLIST, O_RDWR);
	if(fd < 0)
	{
		Error("can't open %s", FILE_PROHIBITMACLIST);
		return JS_EXIT_FAILURE;
	}
    addr_len = strlen(target);
	tmp = malloc(file_len + addr_len + 5);
	if(-1 == read(fd, tmp, file_len))
	{
		Error("can't read %s error : %s\n", FILE_PROHIBITMACLIST, strerror(errno));
        close(fd);
        free(tmp);
	    tmp = NULL; //指针置空防止成为野指针
		return JS_EXIT_FAILURE;
	}
	cjson_funlist = cJSON_Parse(tmp);
	if(cjson_funlist == NULL || cjson_funlist->type == cJSON_NULL)
	{
		Error("json error\n");
        close(fd);
        free(tmp);
	    tmp = NULL;
		return JS_EXIT_FAILURE;
	}
	else{
		cjson_array = cJSON_GetObjectItem(cjson_funlist, "MACaddress");
		cJSON_AddItemToArray(cjson_array, cJSON_CreateString((const char*)target));

		memset(tmp, 0, file_len + addr_len + 5);
		strcpy(tmp, cJSON_Print(cjson_funlist));
		Log("setProhibitMACList json:%s\n", cJSON_Print(cjson_funlist));

		if(-1 == lseek(fd, 0, SEEK_SET))
        {
			Error("lseek file %s error : %s\n", FILE_PROHIBITMACLIST, strerror(errno));
            close(fd);
	        cJSON_Delete(cjson_funlist);
	        free(tmp);
	        tmp = NULL;
            return JS_EXIT_FAILURE;
		}
		if(-1 == write(fd, tmp, file_len + addr_len + 5))
		{
			Error("write file %s error : %s\n", FILE_PROHIBITMACLIST, strerror(errno));
			ret = JS_EXIT_FAILURE;
		}
	}
	close(fd);
	cJSON_Delete(cjson_funlist);
	free(tmp);
	tmp = NULL;
    /*if(setMACaddrFilter(FILE_PERMITMACLIST, target) == ERROR)
    {
        Error("can't remove MACaddress %s from ProhibitMACList.json\n", target);
    }*/
    return ret;
}

/*取得可以控制功能点的列表*/
static char* getWhiteList(char **target, char* apppath)
{
    char* ret = JS_EXIT_SUCCESS;
    int fd = 0;
    char *tmp = NULL;

    tmp = malloc(file_len);
    fd = open(apppath, O_RDONLY);
    if(fd < 0)
    {
        Error("can't open %s", apppath);
        free(tmp);
        tmp = NULL;
        return JS_EXIT_FAILURE;
    }

    if(-1 == read(fd, tmp, file_len))
    {
        Error("can't read %s error : %s\n", apppath, strerror(errno));
        close(fd);
        free(tmp);
        tmp = NULL;
        return JS_EXIT_FAILURE;
    }
    close(fd);

    if(tmp == NULL)
    {
        Error("can't read %s context", apppath);
        return JS_EXIT_FAILURE;
    }
    memset(*target, 0, file_len + 1);
    strncpy(*target, tmp, file_len);

    free(tmp);
    tmp = NULL;
    return ret;
}

//功能白名单会在app安装时生成
//入参：app_name:    安装的appname用于确定功能白名单文件路径/pesf/app_name/Whitelist.json
//入参：white_str： 安装时选中的功能权限"xxx,zzz,xzx"
//返回值：0:-成功创建功能白名单 -1：-创建功能白名单失败
int new_WhiteList_file(char *app_name, char *white_str)
{
    int ret = -1;
    FILE *fd = NULL;
    int str_len = 0;
    char whitelist_file[128] = {0}; //功能白名单路径
    char *out_str = NULL;
    char content[]="{\"Panel\":0,\"Status\":0,\"Setting\":0,\"Copy\":0,\"Scan\":0,\
\"Print\":0,\"Fax\":0,\"JobCtl\":0,\"Powersave\":0,\"Addressbook\":0,\"Usbh\":0,\
\"Usbd\":0,\"Device\":0,\"DeviceLog\":0,\"Email\":0,\"FTP\":0,\"SMB\":0,\"Http\":0,\
\"APPSetting\":0,\"SysCapabilities\":0,\"Crypto\":0,\"SSL\":0,\"Shortcuts\":0,\
\"Login\":0,\"Socket\":0,\"ICCard\":0,\"Audit\":0}";

    //parser file string to content
    cJSON *file_root = cJSON_Parse(content);
    if(file_root == NULL)
    {
       Error("parser white_list file string error\n");
        return ret;
     }

    //parser input string
    char *token = strtok(white_str, ","); //function name
    while(token != NULL)
    {
        Log("update function (%s) enable\n", token);
        //在JSON对象中查询解析的function name
        cJSON_ReplaceItemInObject(file_root, token, cJSON_CreateNumber(1));
        token = strtok(NULL, ",");
    }

    out_str = cJSON_Print(file_root);
    Log("update funclist :%s\n", out_str);

    //写入文件
    if((str_len = strlen(app_name)) == 0)
    {
        Error("can't get app info \n");
        cJSON_Delete(file_root);
        free(out_str);
        return ret;
    }
    snprintf(whitelist_file, 128, "/pesf/%s/funclist.json", app_name);
    Log("create file(%s)\n", whitelist_file);

    fd = fopen(whitelist_file, "w");
    if(fd == NULL)
    {
        Error("create file(%s) error1\n", whitelist_file);
        cJSON_Delete(file_root);
        free(out_str);
        return ret;
    }
    //写入content
    Log("write file content (%s)\n", out_str);
    fprintf(fd, "%s", out_str);
    fclose(fd);

    str_len = get_file_len(whitelist_file);
    Log("get file len(%d)\n", str_len);

    if(str_len == 0)
    {
        Error("create file(%s) error2\n", whitelist_file);
        cJSON_Delete(file_root);
        free(out_str);
        return ret;
    }

    cJSON_Delete(file_root);
    free(out_str);
    ret = 0;

    return ret;
}


/*设置在可控功能中用户可使用功能*/
static char* setPermitFunction(const char * filepath, char *target, uint32_t value)
{
	if(target == NULL)
	{
		return JS_EXIT_FAILURE;
	}

	int fd = 0;
	char *tmp = NULL;
	int file_len = 0;
	char* ret = JS_EXIT_SUCCESS;
	int func_ret = 0;
	cJSON* cjson_funlist = NULL;
	cJSON* newItem = NULL;

	file_len = get_file_len((char *)filepath);
	if(file_len == ERROR)
	{
		return JS_EXIT_FAILURE;
	}

	fd = open(filepath, O_RDWR);
	if(fd < 0)
	{
		printf("can't open %s", filepath);
		return JS_EXIT_FAILURE;
	}

	tmp = malloc(file_len);
	memset(tmp, 0, sizeof(file_len));
	func_ret = read(fd, tmp, file_len);
	if(func_ret == -1){
		Error("error:%s\n", strerror(errno));
        free(tmp);
        tmp = NULL;
        close(fd);
		return JS_EXIT_FAILURE;
	}
	cjson_funlist = cJSON_Parse(tmp);
	if(cjson_funlist == NULL)
	{
		Error("json value error\n");
		free(tmp);
        tmp = NULL;
        close(fd);
		return JS_EXIT_FAILURE;
	}
	else
	{
		newItem = cJSON_CreateNumber(value);
		cJSON_ReplaceItemInObject(cjson_funlist, target, newItem);
		strcpy(tmp, cJSON_Print(cjson_funlist));
		Log("setPermitFunction json:%s\n", tmp);
		lseek(fd, 0, SEEK_SET);
		if(-1 == write(fd, tmp, file_len))
		{
			Error("write file error : %s\n", strerror(errno));
			ret = JS_EXIT_FAILURE;
		}
	}
	close(fd);
	cJSON_Delete(cjson_funlist);

	free(tmp);
	tmp = NULL;
    return ret;
}

/*
    定义 QuickJS C 函数
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv)
//{
//    xxx();
//    return JS_NewString(ctx, "OK");
//}

JSValue js_getPermitMACList(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	char *jscstr = NULL;
	char* ret = NULL;
	JSValue ret_JS;
	file_len = get_file_len(FILE_PERMITMACLIST);
	jscstr = malloc(file_len);
    Log("js_getPermitMACList file_len:%d\n", file_len);
    ret = getPermitMACList(&jscstr);
	if(NORMAL == strcmp(ret, JS_EXIT_SUCCESS))
	{
		Log("js_getPermitMACList json:%s\n", jscstr);
    	ret_JS = JS_NewString(ctx, jscstr);
	}
	else{
		Error("ret = %s\n", ret);
		ret_JS = JS_NewString(ctx, ret);
	}

	free(jscstr);
	jscstr = NULL;
	return ret_JS;
}

JSValue js_setPermitMACList(JSContext *ctx, JSValueConst this_val,
							  int argc, JSValueConst *argv)
{
	char* ret;
	if (!JS_IsString(argv[0])) //判断参数是否是字符串
	{
	    Error("error:INVAILD value\n");
	    return JS_NewString(ctx, JS_EXIT_FAILURE);
    }
    const char *jscstr = JS_ToCString(ctx, argv[0]);
    ret = setPermitMACList((char *)jscstr);
    JS_FreeCString(ctx, jscstr);
	return JS_NewString(ctx, ret);
}

JSValue js_getProhibitMACList(JSContext *ctx, JSValueConst this_val,
					 int argc, JSValueConst *argv)
{
	char *jscstr = NULL;
	char* ret = NULL;
	JSValue ret_JS;
	file_len = get_file_len(FILE_PROHIBITMACLIST);
	jscstr = malloc(file_len);

	ret = getProhibitMACList(&jscstr);
	if(NORMAL == strcmp(ret, JS_EXIT_SUCCESS))
	{
		Log("js_getProhibitMACList json:%s\n", jscstr);
		ret_JS = JS_NewString(ctx, jscstr);
	}
	else{
		Error("ret = %s\n", ret);
		ret_JS = JS_NewString(ctx, ret);
	}

	free(jscstr);
	jscstr = NULL;
	return ret_JS;
}

JSValue js_setProhibitMACList(JSContext *ctx, JSValueConst this_val,
					int argc, JSValueConst *argv)
{
	char* ret;
	if (!JS_IsString(argv[0]))
	{
	    Error("error:INVAILD value\n");
		return JS_NewString(ctx, JS_EXIT_FAILURE);
	}
	const char *jscstr = JS_ToCString(ctx, argv[0]);
	ret = setProhibitMACList((char *)jscstr);
    JS_FreeCString(ctx, jscstr);
	return JS_NewString(ctx, ret);
}

JSValue js_getWhiteList(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    char* jscstr = NULL;
    char* ret = NULL;
    JSValue ret_JS;

    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    const char *appname = prt->dynamic_property.app_name;
    char apppath[128] = {0};
    snprintf(apppath, sizeof(apppath),"/pesf_data/%s/funclist.json", appname);

    file_len = get_file_len(apppath);
    if(file_len == ERROR)
    {
        Error("error:get file len error\n");
        return JS_NewString(ctx, JS_EXIT_FAILURE);
    }
    jscstr = malloc(file_len);

    ret = getWhiteList(&jscstr, apppath);
    if(NORMAL == strcmp(ret, JS_EXIT_SUCCESS))
    {
        Log("return funclist.json:%s\n", jscstr);
        ret_JS = JS_NewString(ctx, jscstr);
    }
    else
    {
        Log("ret = %s\n", jscstr);
        ret_JS = JS_NewString(ctx, ret);
	}

	free(jscstr);
	jscstr = NULL;
	return ret_JS;
}


JSValue js_setPermitFunction(JSContext *ctx, JSValueConst this_val,
							  int argc, JSValueConst *argv)
{
	uint32_t value;
	char* ret = NULL;
	if (!JS_IsString(argv[0]))
    {
        Error("error:INVAILD value\n");
	    return JS_NewString(ctx, JS_EXIT_FAILURE);
    }
	if(JS_ToUint32(ctx, &value, argv[1]))
    {
        Error("error:INVAILD value\n");
		return JS_NewString(ctx, JS_EXIT_FAILURE);
	}
    const char *jscstr = JS_ToCString(ctx, argv[0]);
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    const char *appname = prt->dynamic_property.app_name;

    char apppath[128] = {0};
    snprintf(apppath, sizeof(apppath),"/pesf_data/%s/funclist.json", appname);
    ret = setPermitFunction(apppath, (char *)jscstr, value);
	Log("ret:%s\n", ret);
	JS_FreeCString(ctx, jscstr);

	return JS_NewString(ctx, ret);
}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList PEDK_whitelist_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    //whitelist
    {"js_getPermitMACList",0,js_getPermitMACList},
    {"js_setPermitMACList",1,js_setPermitMACList},
    {"js_getProhibitMACList",0,js_getProhibitMACList},
    {"js_setProhibitMACList",1,js_setProhibitMACList},
    {"js_getWhiteList",0,js_getWhiteList},
    {"js_setPermitFunction",2,js_setPermitFunction},
};

const JSCFunctionList* get_whitelist_JSCFunctionList(int *length) {
	*length = countof(PEDK_whitelist_funcs);
	return PEDK_whitelist_funcs;
}

int js_whitelist_init(JSContext *ctx, JSValueConst global)
{
    printf("*********start whitelist module*******\n");
    /* creat the classes */
    int count = 0;
    const JSCFunctionList* PEDK_funcs = get_whitelist_JSCFunctionList(&count);
    printf("count:%d\n",count);
    for(int i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, PEDK_funcs[i].name,
                            JS_NewCFunction(ctx, PEDK_funcs[i].func, PEDK_funcs[i].name, PEDK_funcs[i].length));

    }
    printf("*********start whitelist init end*******\n");
    return 0;
}
