/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file status_test.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-07-18
 * @brief provide cmd for test system status
 */

#ifndef _PANEL_STATUS_H
#define _PANEL_STATUS_H

#include <pol/pol_types.h>

/**
 * @brief callback of the status test cmd
 * @param[in] argc input argument number
 * @param[in] argv input argument
 * @author: madechang
 */
int32_t status_test(int32_t argc, char *argv[]);

#endif /* _PANEL_EVENT_H */

/**
 *@}
 */


