#ifndef _ESCL_DEFINE_H_
#define _ESCL_DEFINE_H_

#define ESCL_RESPONSE_HEADER    "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"

#define ESCL_RESPONSE_FB_CAPABILITES_TEMPLATE \
ESCL_RESPONSE_HEADER \
"<scan:ScannerCapabilities " \
"xmlns:scan=\"http://schemas.hp.com/imaging/escl/2011/05/03\" " \
"xmlns:pwg=\"http://www.pwg.org/schemas/2010/12/sm\">\n" \
"    <pwg:Version>2.63</pwg:Version>\n" \
"    <pwg:MakeAndModel>%s %s</pwg:MakeAndModel>\n" \
"    <pwg:SerialNumber>5300</pwg:SerialNumber>\n" \
"    <scan:Certifications>\n" \
"        <scan:Certification>\n" \
"            <scan:Name>mopria-certified-scan</scan:Name>\n" \
"            <scan:Version>1.5</scan:Version>\n" \
"        </scan:Certification>\n" \
"    </scan:Certifications>\n" \
"    <scan:Manufacturer>%s</scan:Manufacturer>\n" \
"    <scan:UUID>%s</scan:UUID>\n" \
"    <scan:AdminURI>%s</scan:AdminURI>\n" \
"    <scan:IconURI>%s</scan:IconURI>\n" \
"    <scan:Platen>\n" \
"        <scan:PlatenInputCaps>\n" \
"            <scan:MinWidth>30</scan:MinWidth>\n" /* 值过小会导致Morpia认证测试时，执行STCL 3.16 Scan Area Support（Minimum Area）项，打印机必现死机 */ \
"            <scan:MaxWidth>3510</scan:MaxWidth>\n" \
"            <scan:MinHeight>30</scan:MinHeight>\n" \
"            <scan:MaxHeight>5100</scan:MaxHeight>\n" \
"            <scan:MaxScanRegions>1</scan:MaxScanRegions>\n" \
"            <scan:SettingProfiles>\n" \
"                <scan:SettingProfile>\n" \
"                    <scan:ColorModes>\n" \
"                        <scan:ColorMode>RGB24</scan:ColorMode>\n" \
"                        <scan:ColorMode>Grayscale8</scan:ColorMode>\n" \
"                        <scan:ColorMode>BlackAndWhite1</scan:ColorMode>\n" \
"                    </scan:ColorModes>\n" \
"                    <scan:ContentTypes>\n" \
"                    <pwg:ContentType>Photo</pwg:ContentType>\n" \
"                    <pwg:ContentType>Text</pwg:ContentType>\n" \
"                    <pwg:ContentType>TextAndPhoto</pwg:ContentType>\n" \
"                    </scan:ContentTypes>\n" \
"                    <scan:DocumentFormats>\n" \
"                        <pwg:DocumentFormat>image/jpeg</pwg:DocumentFormat>\n" \
"                        <pwg:DocumentFormat>application/pdf</pwg:DocumentFormat>\n" \
"                    </scan:DocumentFormats>\n" \
"                    <scan:SupportedResolutions>\n" \
"                        <scan:DiscreteResolutions>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>150</scan:XResolution>\n" \
"                                <scan:YResolution>150</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>200</scan:XResolution>\n" \
"                                <scan:YResolution>200</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>300</scan:XResolution>\n" \
"                                <scan:YResolution>300</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>600</scan:XResolution>\n" \
"                                <scan:YResolution>600</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                        </scan:DiscreteResolutions>\n" \
"                    </scan:SupportedResolutions>\n" \
"                    <scan:ColorSpaces>\n" \
"                        <scan:ColorSpace scan:default=\"true\">sRGB</scan:ColorSpace>\n" \
"                    </scan:ColorSpaces>\n" \
"                    <scan:CcdChannels>\n" \
"                        <scan:CcdChannel>Red</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Blue</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Green</scan:CcdChannel>\n" \
"                        <scan:CcdChannel scan:default=\"true\">NTSC</scan:CcdChannel>\n" \
"                    </scan:CcdChannels>\n" \
"                    <scan:BinaryRenderings>\n" \
"                        <scan:BinaryRendering>Halftone</scan:BinaryRendering>\n" \
"                        <scan:BinaryRendering scan:default=\"true\">Threshold</scan:BinaryRendering>\n" \
"                    </scan:BinaryRenderings>\n" \
"                </scan:SettingProfile>\n" \
"            </scan:SettingProfiles>\n" \
"                <scan:SupportedIntents>\n" \
"                    <scan:Intent>Document</scan:Intent>\n" \
"                    <scan:Intent>Photo</scan:Intent>\n" \
"                    <scan:Intent>Preview</scan:Intent>\n" \
"                    <scan:Intent>TextAndGraphic</scan:Intent>\n" \
"                </scan:SupportedIntents>\n" \
"            <scan:MaxOpticalXResolution>600</scan:MaxOpticalXResolution>\n" \
"            <scan:MaxOpticalYResolution>600</scan:MaxOpticalYResolution>\n" \
"        </scan:PlatenInputCaps>\n" \
"    </scan:Platen>\n" \
"</scan:ScannerCapabilities>\n"

#define ESCL_RESPONSE_ADF_CAPABILITES_TEMPLATE \
ESCL_RESPONSE_HEADER \
"<scan:ScannerCapabilities " \
"xmlns:scan=\"http://schemas.hp.com/imaging/escl/2011/05/03\" " \
"xmlns:pwg=\"http://www.pwg.org/schemas/2010/12/sm\">\n" \
"    <pwg:Version>2.63</pwg:Version>\n" \
"    <pwg:MakeAndModel>%s %s</pwg:MakeAndModel>\n" \
"    <pwg:SerialNumber>5300</pwg:SerialNumber>\n" \
"    <scan:Certifications>\n" \
"        <scan:Certification>\n" \
"            <scan:Name>mopria-certified-scan</scan:Name>\n" \
"            <scan:Version>1.5</scan:Version>\n" \
"        </scan:Certification>\n" \
"    </scan:Certifications>\n" \
"    <scan:Manufacturer>%s</scan:Manufacturer>\n" \
"    <scan:UUID>%s</scan:UUID>\n" \
"    <scan:AdminURI>%s</scan:AdminURI>\n" \
"    <scan:IconURI>%s</scan:IconURI>\n" \
"    <scan:Platen>\n" \
"        <scan:PlatenInputCaps>\n" \
"            <scan:MinWidth>30</scan:MinWidth>\n" /* 值过小会导致Morpia认证测试时，执行STCL 3.16 Scan Area Support（Minimum Area）项，打印机必现死机 */ \
"            <scan:MaxWidth>3510</scan:MaxWidth>\n" \
"            <scan:MinHeight>30</scan:MinHeight>\n" \
"            <scan:MaxHeight>5100</scan:MaxHeight>\n" \
"            <scan:MaxScanRegions>1</scan:MaxScanRegions>\n" \
"            <scan:SettingProfiles>\n" \
"                <scan:SettingProfile>\n" \
"                    <scan:ColorModes>\n" \
"                        <scan:ColorMode>RGB24</scan:ColorMode>\n" \
"                        <scan:ColorMode>Grayscale8</scan:ColorMode>\n" \
"                        <scan:ColorMode>BlackAndWhite1</scan:ColorMode>\n" \
"                    </scan:ColorModes>\n" \
"                    <scan:ContentTypes>\n" \
"                    <pwg:ContentType>Photo</pwg:ContentType>\n" \
"                    <pwg:ContentType>Text</pwg:ContentType>\n" \
"                    <pwg:ContentType>TextAndPhoto</pwg:ContentType>\n" \
"                    </scan:ContentTypes>\n" \
"                    <scan:DocumentFormats>\n" \
"                        <pwg:DocumentFormat>image/jpeg</pwg:DocumentFormat>\n" \
"                        <pwg:DocumentFormat>application/pdf</pwg:DocumentFormat>\n" \
"                    </scan:DocumentFormats>\n" \
"                    <scan:SupportedResolutions>\n" \
"                        <scan:DiscreteResolutions>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>150</scan:XResolution>\n" \
"                                <scan:YResolution>150</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>200</scan:XResolution>\n" \
"                                <scan:YResolution>200</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>300</scan:XResolution>\n" \
"                                <scan:YResolution>300</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>600</scan:XResolution>\n" \
"                                <scan:YResolution>600</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                        </scan:DiscreteResolutions>\n" \
"                    </scan:SupportedResolutions>\n" \
"                    <scan:ColorSpaces>\n" \
"                        <scan:ColorSpace scan:default=\"true\">sRGB</scan:ColorSpace>\n" \
"                    </scan:ColorSpaces>\n" \
"                    <scan:CcdChannels>\n" \
"                        <scan:CcdChannel>Red</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Blue</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Green</scan:CcdChannel>\n" \
"                        <scan:CcdChannel scan:default=\"true\">NTSC</scan:CcdChannel>\n" \
"                    </scan:CcdChannels>\n" \
"                    <scan:BinaryRenderings>\n" \
"                        <scan:BinaryRendering>Halftone</scan:BinaryRendering>\n" \
"                        <scan:BinaryRendering scan:default=\"true\">Threshold</scan:BinaryRendering>\n" \
"                    </scan:BinaryRenderings>\n" \
"                </scan:SettingProfile>\n" \
"            </scan:SettingProfiles>\n" \
"                <scan:SupportedIntents>\n" \
"                    <scan:Intent>Document</scan:Intent>\n" \
"                    <scan:Intent>Photo</scan:Intent>\n" \
"                    <scan:Intent>Preview</scan:Intent>\n" \
"                    <scan:Intent>TextAndGraphic</scan:Intent>\n" \
"                </scan:SupportedIntents>\n" \
"            <scan:MaxOpticalXResolution>600</scan:MaxOpticalXResolution>\n" \
"            <scan:MaxOpticalYResolution>600</scan:MaxOpticalYResolution>\n" \
"        </scan:PlatenInputCaps>\n" \
"    </scan:Platen>\n" \
"    <scan:Adf>\n" \
"        <scan:AdfSimplexInputCaps>\n" \
"            <scan:MinWidth>1122</scan:MinWidth>\n" /* 值过小会导致Morpia认证测试时，执行STCL 3.16 Scan Area Support（Minimum Area）项，打印机必现死机 */ \
"            <scan:MaxWidth>3510</scan:MaxWidth>\n" \
"            <scan:MinHeight>1650</scan:MinHeight>\n" \
"            <scan:MaxHeight>5100</scan:MaxHeight>\n" \
"            <scan:SettingProfiles>\n" \
"                <scan:SettingProfile>\n" \
"                    <scan:ColorModes>\n" \
"                        <scan:ColorMode>RGB24</scan:ColorMode>\n" \
"                        <scan:ColorMode>Grayscale8</scan:ColorMode>\n" \
"                        <scan:ColorMode>BlackAndWhite1</scan:ColorMode>\n" \
"                    </scan:ColorModes>\n" \
"                    <scan:ContentTypes>\n" \
"                    <pwg:ContentType>Photo</pwg:ContentType>\n" \
"                    <pwg:ContentType>Text</pwg:ContentType>\n" \
"                    <pwg:ContentType>TextAndPhoto</pwg:ContentType>\n" \
"                    </scan:ContentTypes>\n" \
"                    <scan:SupportedResolutions>\n" \
"                        <scan:DiscreteResolutions>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>150</scan:XResolution>\n" \
"                                <scan:YResolution>150</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>200</scan:XResolution>\n" \
"                                <scan:YResolution>200</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>300</scan:XResolution>\n" \
"                                <scan:YResolution>300</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>600</scan:XResolution>\n" \
"                                <scan:YResolution>600</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                        </scan:DiscreteResolutions>\n" \
"                    </scan:SupportedResolutions>\n" \
"                    <scan:DocumentFormats>\n" \
"                        <pwg:DocumentFormat>image/jpeg</pwg:DocumentFormat>\n" \
"                        <pwg:DocumentFormat>application/pdf</pwg:DocumentFormat>\n" \
"                    </scan:DocumentFormats>\n" \
"                    <scan:CcdChannels>\n" \
"                        <scan:CcdChannel scan:default=\"true\">Red</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Blue</scan:CcdChannel>\n" \
"                    </scan:CcdChannels>\n" \
"                    <scan:BinaryRenderings>\n" \
"                        <scan:BinaryRendering>Threshold</scan:BinaryRendering>\n" \
"                        <scan:BinaryRendering scan:default=\"true\">Halftone</scan:BinaryRendering>\n" \
"                    </scan:BinaryRenderings>\n" \
"                </scan:SettingProfile>\n" \
"            </scan:SettingProfiles>\n" \
"                    <scan:SupportedIntents>\n" \
"                    <scan:Intent>Document</scan:Intent>\n" \
"                    <scan:Intent>Photo</scan:Intent>\n" \
"                    <scan:Intent>Preview</scan:Intent>\n" \
"                    <scan:Intent>TextAndGraphic</scan:Intent>\n" \
"                    </scan:SupportedIntents>\n" \
"            <scan:MaxOpticalXResolution>600</scan:MaxOpticalXResolution>\n" \
"            <scan:MaxOpticalYResolution>600</scan:MaxOpticalYResolution>\n" \
"        </scan:AdfSimplexInputCaps>\n" \
"        <scan:FeederCapacity>35</scan:FeederCapacity>\n" \
"        <scan:AdfOptions>\n" \
"            <scan:AdfOption>DetectPaperLoaded</scan:AdfOption>\n" \
"            <scan:AdfOption>SelectSinglePage</scan:AdfOption>\n" \
"        </scan:AdfOptions>\n" \
"    </scan:Adf>\n" \
"</scan:ScannerCapabilities>\n"

#define ESCL_RESPONSE_DADF_CAPABILITES_TEMPLATE \
ESCL_RESPONSE_HEADER \
"<scan:ScannerCapabilities " \
"xmlns:scan=\"http://schemas.hp.com/imaging/escl/2011/05/03\" " \
"xmlns:pwg=\"http://www.pwg.org/schemas/2010/12/sm\">\n" \
"    <pwg:Version>2.63</pwg:Version>\n" \
"    <pwg:MakeAndModel>%s %s</pwg:MakeAndModel>\n" \
"    <pwg:SerialNumber>5300</pwg:SerialNumber>\n" \
"    <scan:Certifications>\n" \
"        <scan:Certification>\n" \
"            <scan:Name>mopria-certified-scan</scan:Name>\n" \
"            <scan:Version>1.5</scan:Version>\n" \
"        </scan:Certification>\n" \
"    </scan:Certifications>\n" \
"    <scan:Manufacturer>%s</scan:Manufacturer>\n" \
"    <scan:UUID>%s</scan:UUID>\n" \
"    <scan:AdminURI>%s</scan:AdminURI>\n" \
"    <scan:IconURI>%s</scan:IconURI>\n" \
"    <scan:Platen>\n" \
"        <scan:PlatenInputCaps>\n" \
"            <scan:MinWidth>30</scan:MinWidth>\n" /* 值过小会导致Morpia认证测试时，执行STCL 3.16 Scan Area Support（Minimum Area）项，打印机必现死机 */ \
"            <scan:MaxWidth>3510</scan:MaxWidth>\n" \
"            <scan:MinHeight>30</scan:MinHeight>\n" \
"            <scan:MaxHeight>5100</scan:MaxHeight>\n" \
"            <scan:MaxScanRegions>1</scan:MaxScanRegions>\n" \
"            <scan:SettingProfiles>\n" \
"                <scan:SettingProfile>\n" \
"                    <scan:ColorModes>\n" \
"                        <scan:ColorMode>RGB24</scan:ColorMode>\n" \
"                        <scan:ColorMode>Grayscale8</scan:ColorMode>\n" \
"                        <scan:ColorMode>BlackAndWhite1</scan:ColorMode>\n" \
"                    </scan:ColorModes>\n" \
"                    <scan:ContentTypes>\n" \
"                    <pwg:ContentType>Photo</pwg:ContentType>\n" \
"                    <pwg:ContentType>Text</pwg:ContentType>\n" \
"                    <pwg:ContentType>TextAndPhoto</pwg:ContentType>\n" \
"                    </scan:ContentTypes>\n" \
"                    <scan:DocumentFormats>\n" \
"                        <pwg:DocumentFormat>image/jpeg</pwg:DocumentFormat>\n" \
"                        <pwg:DocumentFormat>application/pdf</pwg:DocumentFormat>\n" \
"                    </scan:DocumentFormats>\n" \
"                    <scan:SupportedResolutions>\n" \
"                        <scan:DiscreteResolutions>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>150</scan:XResolution>\n" \
"                                <scan:YResolution>150</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>200</scan:XResolution>\n" \
"                                <scan:YResolution>200</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>300</scan:XResolution>\n" \
"                                <scan:YResolution>300</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>600</scan:XResolution>\n" \
"                                <scan:YResolution>600</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                        </scan:DiscreteResolutions>\n" \
"                    </scan:SupportedResolutions>\n" \
"                    <scan:ColorSpaces>\n" \
"                        <scan:ColorSpace scan:default=\"true\">sRGB</scan:ColorSpace>\n" \
"                    </scan:ColorSpaces>\n" \
"                    <scan:CcdChannels>\n" \
"                        <scan:CcdChannel>Red</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Blue</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Green</scan:CcdChannel>\n" \
"                        <scan:CcdChannel scan:default=\"true\">NTSC</scan:CcdChannel>\n" \
"                    </scan:CcdChannels>\n" \
"                    <scan:BinaryRenderings>\n" \
"                        <scan:BinaryRendering>Halftone</scan:BinaryRendering>\n" \
"                        <scan:BinaryRendering scan:default=\"true\">Threshold</scan:BinaryRendering>\n" \
"                    </scan:BinaryRenderings>\n" \
"                </scan:SettingProfile>\n" \
"            </scan:SettingProfiles>\n" \
"                <scan:SupportedIntents>\n" \
"                    <scan:Intent>Document</scan:Intent>\n" \
"                    <scan:Intent>Photo</scan:Intent>\n" \
"                    <scan:Intent>Preview</scan:Intent>\n" \
"                    <scan:Intent>TextAndGraphic</scan:Intent>\n" \
"                </scan:SupportedIntents>\n" \
"            <scan:MaxOpticalXResolution>600</scan:MaxOpticalXResolution>\n" \
"            <scan:MaxOpticalYResolution>600</scan:MaxOpticalYResolution>\n" \
"        </scan:PlatenInputCaps>\n" \
"    </scan:Platen>\n" \
"    <scan:Adf>\n" \
"        <scan:AdfSimplexInputCaps>\n" \
"            <scan:MinWidth>1122</scan:MinWidth>\n" /* 值过小会导致Morpia认证测试时，执行STCL 3.16 Scan Area Support（Minimum Area）项，打印机必现死机 */ \
"            <scan:MaxWidth>3510</scan:MaxWidth>\n" \
"            <scan:MinHeight>1650</scan:MinHeight>\n" \
"            <scan:MaxHeight>5100</scan:MaxHeight>\n" \
"            <scan:SettingProfiles>\n" \
"                <scan:SettingProfile>\n" \
"                    <scan:ColorModes>\n" \
"                        <scan:ColorMode>RGB24</scan:ColorMode>\n" \
"                        <scan:ColorMode>Grayscale8</scan:ColorMode>\n" \
"                        <scan:ColorMode>BlackAndWhite1</scan:ColorMode>\n" \
"                    </scan:ColorModes>\n" \
"                    <scan:ContentTypes>\n" \
"                    <pwg:ContentType>Photo</pwg:ContentType>\n" \
"                    <pwg:ContentType>Text</pwg:ContentType>\n" \
"                    <pwg:ContentType>TextAndPhoto</pwg:ContentType>\n" \
"                    </scan:ContentTypes>\n" \
"                    <scan:SupportedResolutions>\n" \
"                        <scan:DiscreteResolutions>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>150</scan:XResolution>\n" \
"                                <scan:YResolution>150</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>200</scan:XResolution>\n" \
"                                <scan:YResolution>200</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>300</scan:XResolution>\n" \
"                                <scan:YResolution>300</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                            <scan:DiscreteResolution>\n" \
"                                <scan:XResolution>600</scan:XResolution>\n" \
"                                <scan:YResolution>600</scan:YResolution>\n" \
"                            </scan:DiscreteResolution>\n" \
"                        </scan:DiscreteResolutions>\n" \
"                    </scan:SupportedResolutions>\n" \
"                    <scan:DocumentFormats>\n" \
"                        <pwg:DocumentFormat>image/jpeg</pwg:DocumentFormat>\n" \
"                        <pwg:DocumentFormat>application/pdf</pwg:DocumentFormat>\n" \
"                    </scan:DocumentFormats>\n" \
"                    <scan:CcdChannels>\n" \
"                        <scan:CcdChannel scan:default=\"true\">Red</scan:CcdChannel>\n" \
"                        <scan:CcdChannel>Blue</scan:CcdChannel>\n" \
"                    </scan:CcdChannels>\n" \
"                    <scan:BinaryRenderings>\n" \
"                        <scan:BinaryRendering>Threshold</scan:BinaryRendering>\n" \
"                        <scan:BinaryRendering scan:default=\"true\">Halftone</scan:BinaryRendering>\n" \
"                    </scan:BinaryRenderings>\n" \
"                </scan:SettingProfile>\n" \
"            </scan:SettingProfiles>\n" \
"                    <scan:SupportedIntents>\n" \
"                    <scan:Intent>Document</scan:Intent>\n" \
"                    <scan:Intent>Photo</scan:Intent>\n" \
"                    <scan:Intent>Preview</scan:Intent>\n" \
"                    <scan:Intent>TextAndGraphic</scan:Intent>\n" \
"                    </scan:SupportedIntents>\n" \
"            <scan:MaxOpticalXResolution>600</scan:MaxOpticalXResolution>\n" \
"            <scan:MaxOpticalYResolution>600</scan:MaxOpticalYResolution>\n" \
"        </scan:AdfSimplexInputCaps>\n" \
"    <scan:AdfDuplexInputCaps>\n" \
"        <scan:MinWidth>1122</scan:MinWidth> \n" /* 值过小会导致Morpia认证测试时，执行STCL 3.16 Scan Area Support（Minimum Area）项，打印机必现死机 */ \
"        <scan:MaxWidth>3510</scan:MaxWidth> \n" \
"        <scan:MinHeight>1650</scan:MinHeight> \n" \
"        <scan:MaxHeight>5100</scan:MaxHeight> \n" \
"        <scan:MaxScanRegions>1</scan:MaxScanRegions> \n" \
"        <scan:SettingProfiles>\n" \
"            <scan:SettingProfile>\n" \
"                <scan:ColorModes>\n" \
"                    <scan:ColorMode>RGB24</scan:ColorMode> \n" \
"                    <scan:ColorMode>Grayscale8</scan:ColorMode> \n" \
"                    <scan:ColorMode>BlackAndWhite1</scan:ColorMode>\n" \
"                </scan:ColorModes>\n" \
"                <scan:ContentTypes>\n" \
"                    <pwg:ContentType>Photo</pwg:ContentType> \n" \
"                    <pwg:ContentType>Text</pwg:ContentType> \n" \
"                    <pwg:ContentType>TextAndPhoto</pwg:ContentType> \n" \
"                </scan:ContentTypes>\n" \
"                <scan:DocumentFormats>\n" \
"                    <pwg:DocumentFormat>image/jpeg</pwg:DocumentFormat> \n" \
"                    <pwg:DocumentFormat>application/pdf</pwg:DocumentFormat> \n" \
"                </scan:DocumentFormats>\n" \
"                <scan:CcdChannels>\n" \
"                    <scan:CcdChannel scan:default=\"true\">Red</scan:CcdChannel>\n" \
"                    <scan:CcdChannel>Blue</scan:CcdChannel>\n" \
"                </scan:CcdChannels>\n" \
"                <scan:BinaryRenderings>\n" \
"                    <scan:BinaryRendering>Threshold</scan:BinaryRendering>\n" \
"                    <scan:BinaryRendering scan:default=\"true\">Halftone</scan:BinaryRendering>\n" \
"                </scan:BinaryRenderings>\n" \
"                <scan:SupportedResolutions>\n" \
"                    <scan:DiscreteResolutions>\n" \
"                        <scan:DiscreteResolution>\n" \
"                            <scan:XResolution>150</scan:XResolution> \n" \
"                            <scan:YResolution>150</scan:YResolution> \n" \
"                        </scan:DiscreteResolution>\n" \
"                        <scan:DiscreteResolution>\n" \
"                            <scan:XResolution>200</scan:XResolution>\n" \
"                            <scan:YResolution>200</scan:YResolution>\n" \
"                        </scan:DiscreteResolution>\n" \
"                        <scan:DiscreteResolution>\n" \
"                            <scan:XResolution>300</scan:XResolution> \n" \
"                            <scan:YResolution>300</scan:YResolution> \n" \
"                        </scan:DiscreteResolution>\n" \
"                        <scan:DiscreteResolution>\n" \
"                            <scan:XResolution>600</scan:XResolution> \n" \
"                            <scan:YResolution>600</scan:YResolution> \n" \
"                        </scan:DiscreteResolution>\n" \
"                    </scan:DiscreteResolutions>\n" \
"                </scan:SupportedResolutions>\n" \
"                <scan:ColorSpaces>\n" \
"                    <scan:ColorSpace>sRGB</scan:ColorSpace> \n" \
"                </scan:ColorSpaces>\n" \
"            </scan:SettingProfile>\n" \
"        </scan:SettingProfiles>\n" \
"        <scan:SupportedIntents>\n" \
"            <scan:Intent>Document</scan:Intent> \n" \
"            <scan:Intent>Photo</scan:Intent> \n" \
"            <scan:Intent>Preview</scan:Intent> \n" \
"            <scan:Intent>TextAndGraphic</scan:Intent> \n" \
"        </scan:SupportedIntents>\n" \
"        <scan:MaxOpticalXResolution>600</scan:MaxOpticalXResolution> \n" \
"        <scan:MaxOpticalYResolution>600</scan:MaxOpticalYResolution> \n" \
"    </scan:AdfDuplexInputCaps>\n" \
"        <scan:FeederCapacity>35</scan:FeederCapacity>\n" \
"        <scan:AdfOptions>\n" \
"            <scan:AdfOption>DetectPaperLoaded</scan:AdfOption>\n" \
"            <scan:AdfOption>Duplex</scan:AdfOption>\n" \
"            <scan:AdfOption>SelectSinglePage</scan:AdfOption>\n" \
"        </scan:AdfOptions>\n" \
"    </scan:Adf>\n" \
"</scan:ScannerCapabilities>\n"

#define ESCL_RESPONSE_STATUS_TEMPLATE \
ESCL_RESPONSE_HEADER \
"<scan:ScannerStatus  " \
"xmlns:scan=\"http://schemas.hp.com/imaging/escl/2011/05/03\" " \
"xmlns:pwg=\"http://www.pwg.org/schemas/2010/12/sm\" " \
"xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" " \
"xsi:schemaLocation=\"http://schemas.hp.com/imaging/escl/2011/05/03 ../../schemas/eSCL-1_92.xsd\">\n" \
"    <pwg:Version>2.0</pwg:Version>\n" \
"    <pwg:State>%s</pwg:State>\n" /* ScannerState: Idle, Processing, Testing, Stopped, Down */ \
"    <scan:AdfState>%s</scan:AdfState>\n"  /* AdfState: ScannerAdfProcessing, ScannerAdfEmpty, ScannerAdfLoaded */ \
"%s" /* scan:Jobs */ \
"</scan:ScannerStatus>\n"

#define ESCL_JOBS_TEMPLATE \
"<scan:Jobs>\n" \
"%s" /* scan:JobInfo */ \
"</scan:Jobs>\n"

#define ESCL_JOBINFO_TEMPLATE \
"<scan:JobInfo>\n" \
"    <pwg:JobUri>/eSCL/ScanJobs/%d</pwg:JobUri>\n" \
"    <pwg:JobUuid>%d</pwg:JobUuid>\n" \
"    <scan:Age>%d</scan:Age>\n" \
"    <pwg:ImagesCompleted>%d</pwg:ImagesCompleted>\n" \
"    <pwg:ImagesToTransfer>%d</pwg:ImagesToTransfer>\n" \
"    <pwg:JobState>%s</pwg:JobState>\n" \
"    <pwg:JobStateReasons>\n" \
"        <pwg:JobStateReason>%s</pwg:JobStateReason>\n" \
"    </pwg:JobStateReasons>\n" \
"</scan:JobInfo>\n" \

#endif /* _ESCL_DEFINE_H_ */

