﻿/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file port9120.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief PORT 9120 service for receive ACL command.
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"

typedef struct port9120_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         thread;
    uint8_t             inited;
}
PORT9120_CTX_S;

static PORT9120_CTX_S*  s_port9120_ctx = NULL;

static void port9120_connection(void* arg)
{
    NET_CONN_S*     pnc = (NET_CONN_S *)arg;
    GQIO_S*         pgqio = NULL;
    QIO_S*          pqtcp = NULL;
    ROUTER_MSG_S    sendmsg;

    do
    {
        BREAK_IF(netsock_peek_connection(pnc, 60) <= 0, NET_DEBUG);

        pqtcp = qio_tcp_create(pnc->sockfd);
        BREAK_IF(pqtcp == NULL, NET_WARN);
        pnc->sockfd = INVALID_SOCKET; /* sockfd流转到pqtcp，随QIO_CLOSE释放 */

        pgqio = gqio_create(pqtcp, IO_CLASS_NONE, IO_VIA_NET);
        BREAK_IF(pgqio == NULL, NET_WARN);
        pqtcp = NULL; /* pqtcp流转到pgqio，随GQIO_CLOSE释放 */

        sendmsg.msgType   = MSG_PORT9120_RESPONSE;
        sendmsg.msg1      = IO_CLASS_NONE;
        sendmsg.msg2      = IO_VIA_NET;
        sendmsg.msg3      = (void *)pgqio;
        sendmsg.msgSender = MID_PORT_NET;
        task_msg_send_by_router(MID_PARSER_ACL, &sendmsg);
    }
    while ( 0 );

    if ( pqtcp != NULL )
    {
        QIO_CLOSE(pqtcp);
    }
    netsock_close_connection(pnc);
}

static void* port9120_thread_handler(void* arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    uint32_t        update = 0;
    int32_t         max_fd;

    while (1)
    {
        FD_ZERO(&rfds);
        max_fd = 0;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, 9120, 1);
        if ( max_fd <= 0 )
        {
            NET_WARN("all service socket invalid, reload after 2S");
            pi_msleep(2000);
            update = 1;
            continue;
        }
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    NET_TRACE("new connection %d from %s : %u to %u", pnc->sockfd, pnc->remote_addr, pnc->remote_port, pnc->local_port);
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_port9120_ctx), port9120_connection, pnc) < 0 )
                    {
                        NET_WARN("add port9120_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}

int32_t port9120_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret;

    RETURN_VAL_IF(s_port9120_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_port9120_ctx = (PORT9120_CTX_S *)pi_zalloc(sizeof(PORT9120_CTX_S));
    RETURN_VAL_IF(s_port9120_ctx == NULL, NET_WARN, -1);

    do
    {
        s_port9120_ctx->net_ctx = net_ctx;

        s_port9120_ctx->thread = pi_thread_create(port9120_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "port9120_thread_handler");
        BREAK_IF(s_port9120_ctx->thread == INVALIDTHREAD, NET_WARN);

        s_port9120_ctx->inited = 1;
    }
    while ( 0 );

    NET_INFO("Port9120 inited(%u)", s_port9120_ctx->inited);
    if ( s_port9120_ctx->inited == 0 )
    {
        port9120_epilog();
        ret = -1;
    }
    else
    {
        ret = 0;
    }
    return ret;
}

void port9120_epilog(void)
{
    if ( s_port9120_ctx != NULL )
    {
        s_port9120_ctx->inited = 0;
        if ( s_port9120_ctx->thread != INVALIDTHREAD )
        {
            pi_thread_destroy(s_port9120_ctx->thread);
        }
        pi_free(s_port9120_ctx);
        s_port9120_ctx = NULL;
    }
}
/**
 *@}
 */
