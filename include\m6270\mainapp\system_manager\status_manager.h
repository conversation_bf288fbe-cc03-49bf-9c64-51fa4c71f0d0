/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file status_manager.h
 * @addtogroup system_manager
 * @{
 * @brief status management module 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef _STATUS_MANAGER_H
#define _STATUS_MANAGER_H

#include "statusid.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

/**
 * @brief status management module initialization
 *
 * @return 0 on success , -1 on error 
 */
int32_t status_manager_prolog(void);

/**
 * @brief status management module destruction
 *
 */
void status_manager_epilog(void);

/**
 * \section call example
\code

    int moduleid = SYSTEM_STATUS;
    uint8_t *addr = NULL;
    uint32_t len = 0;
    int32_t ret = 0;

    ret = status_manager_get(moduleid , &addr , &len);
    
    if (!ret && len > 0)
    {
        //the status data has been obtained
        ...
        handle status
        ...
        

        free(addr); //always remember to release the memory
    }


\endcode
 *
 *
 * @brief get the status data of the specified module
 *
 * @param module[in] status id module number
 * @param addr[out] buffer for receiving status data , \n
 *          When not in use, you must call free to release the memory.
 *
 * @param len[out] store the length of state data
 *
 * @return 0 on success , -1 on error 
 */
int32_t status_manager_get(STATUS_ID_MODULE_E module , uint8_t **addr , uint32_t *len);

/**
 * @brief check if there is an exception in a module.
 *
 * @param module[in] status id module number
 *
 * @return 1 exception , 0 no exception
 */
int32_t status_manager_check_abnormality(STATUS_ID_MODULE_E module);


/**
 * @brief Check if the printer is ready for the first time.
 *
 * @param module[in] status id module number . Only check the print, copy, and scan modules.
 *
 * @return 1 ready, 0 not
 */
int32_t status_manager_first_ready(STATUS_ID_MODULE_E module);

PT_END_DECLS

#endif //_STATUS_MANAGER_H

/**
 * @}
 */
