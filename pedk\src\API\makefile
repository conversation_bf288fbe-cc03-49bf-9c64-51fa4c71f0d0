# 项目名称以及生成应用程序名称
project_name  := libpedk.so

############################################################
# 代码文件路径
code_path :=event/				\
			test/				\
			common/				\
  			ui/					\
			device/				\
			copy/				\
			scan/				\
			print/				\
			fax/				\
			jobs/				\
			net/                \
			usb/				\
			shortcuts/			\
			auth_whitelist/		\
			crypto/				\
			defined/			\
            lowpower/			\
			app_manager/		\
			appsetting/         \
			storage/         \
            total_pages/
############################################################
# 头文件路径
include_path  := ./ 					\
				 event/					\
                 defined/			    \
				 test/					\
				 common/				\
				 ui/					\
				 device/				\
				 copy/					\
				 scan/					\
				 print/					\
				 fax/					\
				 jobs/					\
				 net/              	 	\
				 usb/					\
				 shortcuts/				\
				 auth_whitelist/		\
				 crypto/				\
				 app_manager/			\
                 lowpower/              \
				 storage/               \
				 $(STAGING_DIR)/usr/include/quickjs/	\
				 $(STAGING_DIR)/usr/lib/libuv/include				\
				 $(STAGING_DIR)/usr/include/cjson				\
				 ../../src/runtime/modules/bridge/		\
				 ../../src/runtime/		\
				 ../../src/		\
				 appsetting/            \
                 total_pages/
include_flags := $(patsubst %, -I%, $(include_path))

############################################################
# 库文件路径
lib_path      :=  .	\
				  ../	\
#				  ../tools/httc/ram-2g/Base \
#                 net/net_lib \

lib_flags     := $(patsubst %, -L%, $(lib_path))

############################################################
# 库文件
lib_file      := -lm 	\
				 -ldl	\
				 # -lquickjs \
				 # -luv_a		\
				 # -lPEDK		\
				 -lpthread
#                 -lmdnsresponder \


############################################################
# objs
obj-y :=
# obj-y +=pesf_event.o	\
		# test_demo.o	\
		# test_add.o	\
		# test_helloworld.o \
		# pesf_common.o		\
		# pesf_ui.o			\
		# pesf_device_status.o \
		# pesf_device_setting.o \
		# pesf_jobs_copy.o		\
		# pesf_jobs_scan.o		\
		# pesf_jobs_print.o		\
		# pesf_jobs_fax.o		\
		# pesf_jobctl.o			\
		# pesf_device_powersave.o		\
		# pesf_usbh_udisk.o		\
		# pesf_usbd.o			\
		# pesf_device.o			\
		# pesf_device_log.o			\
		# pesf_net_email.o		\
		# pesf_net_ftp.o		\
		# pesf_net_smb.o		\
		# pesf_net_http.o		\
		# pesf_device_settings.o	\
		# pesf_device_capabilities.o	\
		# pesf_crypto.o			\
		# pesf_net_ssl.o		\
		# pesf_shortcuts.o		\
		# pesf_auth_whitelist.o	\
export obj-y

############################################################
# 包含子Makefile
sub_makefile_path = $(patsubst %, %makefile, $(code_path))
-include $(sub_makefile_path)


############################################################
#
objs_file    := $(patsubst %, %, $(obj-y))
deps_file    := $(patsubst %.o, %.d, $(obj-y))


############################################################
# 交叉编译变量
CC		  := $(CROSS_COMPILE)gcc
#AR      := $(CROSS_COMPILE)ar
#AS      := $(CROSS_COMPILE)as
#LD      := $(CROSS_COMPILE)ld
#NM      := $(CROSS_COMPILE)nm
#GCC 	   := $(CROSS_COMPILE)gcc
#CPP 	   := $(CROSS_COMPILE)cpp
#CXX 	   ?= $(CROSS_COMPILE)c++
#RANLIB  ?= $(CROSS_COMPILE)ranlib
#STRIP   ?= $(CROSS_COMPILE)strip
#READELF ?= $(CROSS_COMPILE)readelf
#OBJCOPY ?= $(CROSS_COMPILE)objcopy
#OBJDUMP ?= $(CROSS_COMPILE)objdump

############################################################
# 编译时间
build_time="\"`date '+%Y-%m-%d %H:%M:%S'`"\"
SVN_REV		:= -D'SVN_REV="$(shell svnversion -n)"'

############################################################
# 编译参数 -D__DATE__=$(build_date) -D__TIME__=$(build_time)
cc_flags = -fPIC -shared -rdynamic -funwind-tables -ffunction-sections -g -std=gnu99 -Wall -Dbuild_time=$(build_time) $(SVN_REV)

$(app_defines)
############################################################
# 编译执行
all: $(project_name)
-include $(deps_file)
$(project_name): $(objs_file) $(deps_file)
	@$(CC)  $(objs_file) -o $(project_name) $(lib_flags) $(lib_file) -shared 
%.o:%.c
	@echo " [CC]   $@"
	@$(CC) $(cc_flags) $(define_flags) $(include_flags) -c $< -o $@

%.d:%.c
	@$(CC) -MM $(include_flags) $< -o $@


############################################################
# 清除
.PHONY:clean
clean:
	@rm -rf $(objs_file) $(deps_file) $(project_name) 

############################################################
# 执行脚本
.PHONY:run_app
run_app:
	@./$(project_name)
