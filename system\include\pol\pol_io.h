/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_io.h
 * @addtogroup io
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal io interface set
 */
#ifndef __POL_IO_H__
#define __POL_IO_H__

#include <dirent.h>
#include "pol_define.h"
#include <stdio.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/select.h>
#include <sys/time.h>
#include <unistd.h>

PT_BEGIN_DECLS

#if defined(CONFIG_POL_IO_DEBUG)

#if defined(CONFIG_POL_RET_CHECK)
#define pi_ioctl(fd, req, args...)  ({ \
        int ret;\
        ret=ioctl((fd),(req), ##args);  \
        if(ret== -1) {\
        pi_err("%s-%d return err:%s\n ",__func__,__LINE__,strerror(errno));\
        } \
        ret; \
        })
#else /* defined(CONFIG_POL_RET_CHECK) */
#define pi_ioctl            ioctl
#endif /* defined(CONFIG_POL_RET_CHECK) */

#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

/**
 * @brief Opens a file specified by the pathname using
 * the Pi library's file opening mechanism.
 * @param[in] pathname The path to the file to be opened.
 * @param[in] flags The flags that determine the file access mode (e.g., read, write, create, etc.).
 */
int pi_open(const char *pathname, int flags, ...);

/**
 * @brief Creates a new file with the specified pathname
 * using the Pi library's file creation mechanism.
 * @param[in] pathname The path to the file to be created.
 * @param[in] mode The file mode permissions to be set for the newly created file.
 */
int pi_creat(const char *pathname, mode_t mode);

/**
 * @brief Closes the file descriptor specified by `fd` using the Pi library's file closing mechanism.
 * @param[in] fd The file descriptor of the file to be closed.
 */
int pi_close(int fd);

/**
 * @brief read data from a file descriptor
 * @param[in] fd The file descriptor of the file to read data to.
 * @param[in] buf Pointer to the buffer containing the data to be read.
 * @param[in] count The number of bytes to read.
 */
ssize_t pi_read(int fd, void *buf, size_t count);

/**
 * @brief Writes data from the buffer `buf` to the file associated with the file descriptor `fd` using the Pi library's file writing mechanism.
 * @param[in] fd The file descriptor of the file to write data to.
 * @param[in] buf Pointer to the buffer containing the data to be written.
 * @param[in] count The number of bytes to write.
 */
ssize_t pi_write(int fd, const void *buf, size_t count);

/**
 * @brief Opens a directory specified by the dirname using the Pi library's directory opening mechanism.
 * @param[in] name The path to the directory to be opened.
 */
DIR *pi_opendir(const char *name);

/**
 * @brief  Closes the directory stream specified by `dirp` using the Pi library's directory closing mechanism.
 * @param[in] dirp Pointer to the directory stream.
 */
int  pi_closedir(DIR *dirp);

/**
 * @brief Opens a file specified by the filename using the Pi library's file opening mechanism.
 * @param[in] path The path to the file to be opened.
 * @param[in] mode The mode in which the file should be opened (e.g., "r" for read, "w" for write).
 */
FILE *pi_fopen(const char *path, const char *mode);

/**
 * @brief Closes the file stream specified by `stream` using the Pi library's file closing mechanism.
 * @param[in] stream Pointer to the file stream.
 */
int pi_fclose(FILE *stream);

/**
 * @brief Reads data from the file stream specified by `stream` into the buffer pointed by `ptr` using the Pi library's file reading mechanism.
 * @param[in] ptr Pointer to the buffer where the data will be read into.
 * @param[in] size The size of each element to be read.
 * @param[in] nmemb The number of elements to be read.
 * @param[in] stream Pointer to the file stream.
 */
size_t pi_fread(void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * @brief  Writes data from the buffer pointed by `ptr` to the file stream specified by `stream` using the Pi library's file writing mechanism.
 * @param[in] ptr Pointer to the buffer containing the data to be written.
 * @param[in] size The size of each element to be written.
 * @param[in] nmemb The number of elements to be written.
 * @param[in] stream Pointer to the file stream.
 */
size_t pi_fwrite(const void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * @brief Returns the file descriptor associated with the file stream `stream` using the Pi library's file descriptor retrieval mechanism.
 * @param[in] stream Pointer to the file stream.
 */
int pi_fileno(FILE *stream);

/**
 * @brief  Waits until one or more of the specified file descriptors
 * become ready for reading, writing, or have an exceptional condition
 * using the Pi library's I/O multiplexing mechanism.
 * @param[in] nfds The highest-numbered file descriptor in the sets, plus 1.
 * @param[in] readfds Pointer to the set of file descriptors to monitor for read readiness.
 * @param[in] writefds Pointer to the set of file descriptors to monitor for write readiness.
 * @param[in] exceptfds Pointer to the set of file descriptors to monitor for exceptional conditions.
 * @param[in] timeout Pointer to a `struct timeval` specifying the maximum time to wait.
 */
 int pi_select(int nfds, fd_set *readfds, fd_set *writefds, fd_set *exceptfds, struct timeval *timeout);

/**
 * @brief Reads formatted data from the input string `str` based
 * on the format string `format` using the Pi library's string scanning mechanism.
 * @param[in] str The input string from which to read the formatted data.
 * @param[in] format The format string that specifies the expected format of the input data.
 */
 int pi_sscanf(char *str, const char *format, ...);

/**
 * @brief Writes formatted output to the file
 * stream `stream` based on the format string `format` using
 * the Pi library's formatted printing mechanism.
 * @param[in] stream Pointer to the file stream to which the output will be written.
 * @param[in] format The format string that specifies the desired format of the output.
 */
 int pi_fprintf(FILE *stream, const char *format, ...);

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

extern int _pi_open(const char *callfile ,const uint32_t callline , const char *pathname, int flags, ...);
#define pi_open(pn, f,mode...)              _pi_open(__FILE__,__LINE__,pn,f,##mode)

int _pi_creat(const char *callfile ,const uint32_t callline , const char *pathname, mode_t mode);
#define pi_creat(pn, mode)                  _pi_creat(__FILE__,__LINE__,pn,mode)

int _pi_close(const char *callfile ,const uint32_t callline , int fd);
#define pi_close(fd)                        _pi_close(__FILE__,__LINE__,fd)

ssize_t _pi_read(const char *callfile ,const uint32_t callline , int fd, void *buf, size_t count);
#define pi_read(fd,buf,count)               _pi_read(__FILE__,__LINE__,fd,buf,count)

ssize_t _pi_write(const char *callfile ,const uint32_t callline , int fd, const void *buf, size_t count);
#define pi_write(fd,buf,count)              _pi_write(__FILE__,__LINE__,fd,buf,count)

DIR*  _pi_opendir(const char *callfile ,const uint32_t callline , const char *name);
#define pi_opendir(name)                    _pi_opendir(__FILE__,__LINE__,name)

int  _pi_closedir(const char *callfile ,const uint32_t callline , DIR *dirp);
#define pi_closedir(dirp)                   _pi_closedir(__FILE__,__LINE__,dirp)

FILE *_pi_fopen(const char *callfile ,const uint32_t callline, const char *path, const char *mode);
#define pi_fopen(path,mode)                 _pi_fopen(__FILE__,__LINE__,path,mode)

int _pi_fclose(const char *callfile ,const uint32_t callline, FILE *stream);
#define pi_fclose(stream)                   _pi_fclose(__FILE__,__LINE__,stream)

size_t _pi_fread(const char *callfile ,const uint32_t callline, void *ptr, size_t size, size_t nmemb,
        FILE *stream);
#define pi_fread(ptr,size,nmemb,stream)     _pi_fread(__FILE__,__LINE__,ptr,size,nmemb,stream)

size_t _pi_fwrite(const char *callfile ,const uint32_t callline, const void *ptr, size_t size, size_t nmemb,
        FILE *stream);
#define pi_fwrite(ptr,size,nmemb,stream)    _pi_fwrite(__FILE__,__LINE__,ptr,size,nmemb,stream)

int _pi_fileno(const char *callfile ,const uint32_t callline, FILE *stream);
#define pi_fileno(stream)                   _pi_fileno(__FILE__,__LINE__,stream)

int _pi_select(const char *callfile ,const uint32_t callline, int nfds, fd_set *readfds, fd_set *writefds,
        fd_set *exceptfds, struct timeval *timeout);
#define pi_select(nfds,rfds,wfds,efds,t)    _pi_select(__FILE__,__LINE__,nfds,rfds,wfds,efds,t)

int _pi_sscanf(const char *callfile, const uint32_t callline, char *str, const char *format, ...);
#define pi_sscanf(str, format, args...)    _pi_sscanf(__FILE__, __LINE__, str, format, ##args)

int _pi_fprintf(const char *callfile, const uint32_t callline, FILE *stream, const char *format, ...);
#define pi_fprintf(stream, format, args...)    _pi_fprintf(__FILE__, __LINE__, stream, format, ##args)

#endif /* defined(CONFIG_POL_DEBUG_MODE_BACKTRACE) || defined(CONFIG_POL_DEBUG_MODE_CALLER) */

#else /* defined(CONFIG_POL_IO_DEBUG) */

#define pi_open				open
#define pi_creat			creat
#define pi_close			close
#define pi_read				read
#define pi_write			write
#define pi_opendir			opendir
#define pi_closedir			closedir
#define pi_fopen			fopen
#define pi_fclose			fclose
#define pi_fread			fread
#define pi_fileno           fileno
#define pi_fwrite			fwrite
#define pi_fprintf			fprintf
#define pi_ioctl			ioctl
#define pi_select			select
#define pi_sscanf			sscanf
#define pi_fprintf			fprintf

#endif /* defined(CONFIG_POL_IO_DEBUG) */

#define pi_feof             feof
#define pi_ferror           ferror

PT_END_DECLS

#endif /* __POL_IO_H__ */
/**
 *@}
 */
