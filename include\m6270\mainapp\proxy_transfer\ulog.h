/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ulog.h
 * @addtogroup proxy
 * @{
 * @brief proxy log debugging interface 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef __ULOG_H
#define __ULOG_H


struct ulog
{
	char *name; // module name 
	char *file; // local file name 
	int fd;
	unsigned int limitsize; // local file size limit
	unsigned char level:4; // log level options
	short o_time:1; //timestamp configuration options , 0 is not displayed.
	short o_file:1; //file name configuration options , 0 is not displayed.
	short o_func:1; //function name configuration options , 0 is not displayed.
	short o_line:1; //line number configuration options , 0 is not displayed.
	short o_module:1; //module name configuration options , 0 is not displayed.
	short o_level:1; //log level name configuration options , 0 is not displayed.
	short o_pid:1; // process pid configuration options , 0 is not displayed.
	short o_thread:1; // thread number configuration options , 0 is not displayed.
	short o_color:1; // Not currently enabled.
	short o_limitsize:1; //local file size limit configuration options , 0 is not disable.
	struct ulog *next;
}__attribute__((packed));

enum log_level
{
	ALL,
	TRACE,
	DEBUG,
	INFO,
	WARN,
	ERROR,
	FATAL
};

/**
 * @brief Register a log module.
 *
 * @param lg The address pointed to by the lg.
 *
 * @return On success, 0 is returned;On error,-1 is returned.
 */
int ulog_register(struct ulog *lg);

/**
 * @brief Unregister a log module.
 *
 * @param name log module name.
 *
 * @return On success, valid address is returned;On error,NULL is returned.
 */
struct ulog* ulog_unregister(const char *name);

/**
 * @brief Output a log message. 
 *
 * @param name log module name
 * @param file source file
 * @param func function name in source file 
 * @param line line number in source file
 * @param level log level option
 * @param fmt the format string
 * @param ...
 */
void ulog_print(const char *name , 
		const char *file , 
		const char *func , 
		int line , 
		int level , 
		const char *fmt , ...);

#define ULOG_ALL(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , ALL ,fmt,##args);

#define ULOG_TRACE(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , TRACE ,fmt,##args);

#define ULOG_DEBUG(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , DEBUG ,fmt,##args);

#define ULOG_INFO(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , INFO,fmt,##args);

#define ULOG_WARN(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , WARN,fmt,##args);

#define ULOG_ERROR(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , ERROR,fmt,##args);

#define ULOG_FATAL(name,fmt,args...) \
	ulog_print(name,__FILE__ , __func__ , __LINE__ , FATAL,fmt,##args);


#endif //__ULOG_H

/**                                                                                                                                                                  
 * @}
 */
