;
; 配置规则说明：
;  1. 该文件必须以UTF-8编码方式打开并保存
;  2. 英文的;符号开始处，表示后面的内容都是注释
;  3. 中括号中的内容表示作业配置项或资源配置项信息
;  4. 每个配置项下的每条属性都是以 key = value 的方式呈现
;  5. depend_resource_list 这条属性的value格式说明：
;       a. 作业允许出现一条或多条资源列表 ，每条资源列表通过英文的,符号隔开
;       b. 每个资源列表的开头都以英文的{符号开始，以英文的}符号结束
;       c. 一个资源列表中允许存在一个或多个资源，每个资源间通过英文的,符号隔开
;       d. 当出现多个资源列表时，列表开头必须有相同的资源，至少一个，但不能全部相同
;  6. 所有作业配置是以job_开头
;  7. 所有资源配置是以resource_开头
;  8. msgrouter_source 属性表示该作业是几部分组成，如推扫由面板发起和驱动下发组成
;                      当该作业只有一个来源时，则可以忽略
;  9. multiple_job_depend_ssd 该属性可选
;
;

[job_scan]
job_type = JOB_TYPE_SCAN_PUSH   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_SCAN  ; JOB_CLASS_E枚举值
image_memory_required = 400 ;单位MBytes
image_memory_lowest = 200;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_SCAN_JOB_MGR , MID_SCAN_OUT } ; resource_link的资源列表
start_module_id = MID_SCAN_JOB_MGR; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_SCAN_OUT; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 1 ; 0表示非独占 , 1表示独占
wait_type = 1; 0表示排队，1表示不排队，直接丢弃

[job_scan_push_pc]
job_type = JOB_TYPE_SCAN_HOST_PUSH   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_SCAN  ; JOB_CLASS_E枚举值
image_memory_required = 400 ;单位MBytes
image_memory_lowest = 200;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_SCAN_JOB_MGR , MID_SCAN_OUT } ; resource_link的资源列表
msgrouter_source = {MID_PANEL , MID_INVALID}; 表示该作业由两个来源组成，第一个来源是面板，第二个来源MID_INVALID，
;                                            这里MID_INVALID泛指除了MID_PANEL以外的任意一个MID
start_module_id = MID_SCAN_JOB_MGR; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_SCAN_OUT; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 1 ; 0表示非独占 , 1表示独占
wait_type = 1; 0表示排队，1表示不排队，直接丢弃

[job_scan_pull_pc]
job_type = JOB_TYPE_SCAN_PULL   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_SCAN  ; JOB_CLASS_E枚举值
image_memory_required = 400 ;单位MBytes
image_memory_lowest = 200;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_SCAN_JOB_MGR , MID_SCAN_OUT } ; resource_link的资源列表
start_module_id = MID_SCAN_JOB_MGR; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_SCAN_OUT; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 1 ; 0表示非独占 , 1表示独占
wait_type = 1; 0表示排队，1表示不排队，直接丢弃

[job_normal_copy]
job_type = JOB_TYPE_COPY   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_COPY  ; JOB_CLASS_E枚举值
image_memory_required = 872 ;单位MBytes
image_memory_lowest = 872 ;单位MBytes
image_memory_fragment = 0 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_SCAN_JOB_MGR , MID_COPY_JOB_MGR, MID_IMAGE_PROCESS, MID_PRINT_IF} ; resource_link的资源列表
start_module_id = MID_COPY_JOB_MGR; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = 0; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 1 ; 0表示非独占 , 1表示独占
wait_type = 1; 0表示排队，1表示不排队，直接丢弃

[job_copy_push_scan]
job_type = JOB_TYPE_COPY_PUSH_SCAN   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_COPY  ; JOB_CLASS_E枚举值
image_memory_required = 872 ;单位MBytes
image_memory_lowest = 872;单位MBytes
image_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_SCAN_JOB_MGR , MID_COPY_JOB_MGR, MID_IMAGE_PROCESS, MID_PRINT_IF} , {MID_SCAN_JOB_MGR , MID_SCAN_OUT } ; resource_link的资源列表
msgrouter_source = {MID_PANEL , MID_INVALID}; 表示该作业由两个来源组成，第一个来源是面板，第二个来源MID_INVALID，
;                                            这里MID_INVALID泛指除了MID_PANEL以外的任意一个MID
start_module_id = MID_COPY_JOB_MGR; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_SCAN_OUT; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 1 ; 0表示非独占 , 1表示独占
wait_type = 1; 0表示排队，1表示不排队，直接丢弃

[job_copy_scan]
job_type = JOB_TYPE_COPY_SCAN   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_COPY  ; JOB_CLASS_E枚举值
image_memory_required = 872 ;单位MBytes
image_memory_lowest = 872;单位MBytes
image_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_SCAN_JOB_MGR , MID_COPY_JOB_MGR, MID_IMAGE_PROCESS, MID_PRINT_IF} , {MID_SCAN_JOB_MGR , MID_SCAN_OUT } ; resource_link的资源列表
start_module_id = MID_COPY_JOB_MGR; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_SCAN_OUT; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 1 ; 0表示非独占 , 1表示独占
wait_type = 1; 0表示排队，1表示不排队，直接丢弃


[job_acl_config]
job_type = JOB_TYPE_PRINT_ACL   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 0 ;单位MBytes
image_memory_lowest = 0 ;单位MBytes
image_memory_fragment = 0 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PARSER_ACL} ; resource_link的资源列表
start_module_id = MID_PARSER_ACL; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PARSER_ACL; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃

[job_print]
job_type = JOB_TYPE_PRINT_IPS   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PRINT_PARSER_IF , MID_IMAGE_PROCESS , MID_PRINT_IF} ; resource_link的资源列表
start_module_id = MID_PRINT_PARSER_IF; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PRINT_PARSER_IF; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃
multiple_job_depend_ssd = 1; 1表示多作业发起依赖SSD

[job_sample_print]
job_type = JOB_TYPE_PRINT_SAMPLE ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PRINT_PARSER_IF , MID_IMAGE_PROCESS , MID_PRINT_IF} ; resource_link的资源列表
start_module_id = MID_PRINT_PARSER_IF; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PRINT_PARSER_IF; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃
multiple_job_depend_ssd = 1; 1表示多作业发起依赖SSD

[job_print_pdf]
job_type = JOB_TYPE_PRINT_CREATE_PDF  ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PRINT_PARSER_IF} ; resource_link的资源列表
start_module_id = MID_PRINT_PARSER_IF; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PRINT_PARSER_IF; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃


[job_urf_print]
job_type = JOB_TYPE_PRINT_URF   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PRINT_PARSER_IF , MID_IMAGE_PROCESS , MID_PRINT_IF} ; resource_link的资源列表
start_module_id = MID_PRINT_PARSER_IF; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PRINT_PARSER_IF; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃

[job_pwg_print]
job_type = JOB_TYPE_PRINT_PWG   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PRINT_PARSER_IF , MID_IMAGE_PROCESS , MID_PRINT_IF} ; resource_link的资源列表
start_module_id = MID_PRINT_PARSER_IF; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PRINT_PARSER_IF; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃

[job_pincode_print]
job_type = JOB_TYPE_PRINT_PINCODE   ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PRINT_PARSER_IF} ; resource_link的资源列表
start_module_id = MID_PRINT_PARSER_IF; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PRINT_PARSER_IF; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃

[job_delay_print]
job_type = JOB_TYPE_PRINT_DELAY ; JOB_TYPE_E枚举值
job_class = JOB_CLASS_PRINT  ; JOB_CLASS_E枚举值
image_memory_required = 800 ;单位MBytes
image_memory_lowest = 800 ;单位MBytes
image_memory_fragment = 10 ; 单位MBytes
video_memory_required = 0 ;单位MBytes
video_memory_lowest = 0 ;单位MBytes
video_memory_fragment = 0 ; 单位MBytes
;列表中的数字表示资源的编号 , 与resource_xxx节点下的resource_id对应
;具体配置请看配置规则
depend_resource_list = {MID_PARSER_DELAY} ; resource_link的资源列表
start_module_id = MID_PARSER_DELAY; 接收MSG_CTRL_RESOURCE_START的模块编号
resource_io_id = MID_PARSER_DELAY; 当前作业有存在某模块资源读取qio时，须指出该资源id，没有时给0
monop_type = 0 ; 0表示非独占 , 1表示独占
wait_type = 0; 0表示排队，1表示不排队，直接丢弃


;目前只有一块DDR , 此处无用
;[memory_resource]
;name = ptmem
;id = 1
;memory_size = 1000 ;单位MBytes

;[resource_ips]
;resource_id = 48 ; 该编号将系统作业给出
;resource_number = 1 ;表示该模块可使用的资源数
;resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_print_image]
resource_id = MID_PRINT_PARSER_IF ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_print]
resource_id = MID_PRINT_IF ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 1 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_delay]
resource_id = MID_PARSER_DELAY ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_scan_job]
resource_id = MID_SCAN_JOB_MGR ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 1 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_scan_out]
resource_id = MID_SCAN_OUT ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_copy_job]
resource_id = MID_COPY_JOB_MGR ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_ipm]
resource_id = MID_IMAGE_PROCESS ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

[resource_acl]
resource_id = MID_PARSER_ACL ; 该编号将系统作业给出
resource_number = 1 ;表示该模块可使用的资源数
resource_reuse = 0 ;表示该资源是否支持复用，当该选项打开后，资源数将不起效果

