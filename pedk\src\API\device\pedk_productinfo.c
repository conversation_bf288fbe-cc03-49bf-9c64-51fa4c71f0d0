#include <string.h>
#include <stdio.h>
#include "pedk_productinfo.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define PDT_NAME_LEN            32
#define NVLEN_PROD_SER_NUM      16
#define NVLEN_PRINTER_LOCATION  (63*3 + 1)
#define countof(x)              (sizeof(x) / sizeof((x)[0]))

JSValue js_get_product_uuid(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[1024];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_PRODUCT_UUID, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_PRODUCT_UUID, &respond, receive_data, &receive_cnt, 3);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_get_product_name(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[PDT_NAME_LEN];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTNAME, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTNAME, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :product name[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_get_product_serial_number(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[NVLEN_PROD_SER_NUM];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTSN, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTSN, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :serial number[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_get_product_position(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[NVLEN_PRINTER_LOCATION];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTPOSITION, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTPOSITION, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :product position[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_setting_getCTonerStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_TONER_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_TONER_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getMTonerStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_TONER_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_TONER_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getYTonerStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_TONER_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_TONER_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getKTonerStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_TONER_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_TONER_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getCDrumStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_DRUM_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_DRUM_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getMDrumStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_DRUM_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_DRUM_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getYDrumStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_DRUM_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_DRUM_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}

JSValue js_setting_getKDrumStatus(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_DRUM_STATUS, 0,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_DRUM_STATUS, &value,NULL,NULL,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,value = %d\n",__LINE__,__func__,value);

    if( 0xFF == value )
    {
        JS_NewString(ctx, "EXIT_FAILURE");
    }

    return JS_NewInt32(ctx, value);
}


typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pedk_productinfo_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    {"get_product_uuid",            0,  js_get_product_uuid},
    {"get_product_name",            0,  js_get_product_name},
    {"get_product_serial_number",   0,  js_get_product_serial_number},
    {"get_product_position",        0,  js_get_product_position},
    {"get_CTonerStatus",            0,  js_setting_getCTonerStatus},
    {"get_MTonerStatus",            0,  js_setting_getMTonerStatus},
    {"get_YTonerStatus",            0,  js_setting_getYTonerStatus},
    {"get_KTonerStatus",            0,  js_setting_getKTonerStatus},
    {"get_CDrumModuleStatus",       0,  js_setting_getCDrumStatus},
    {"get_MDrumModuleStatus",       0,  js_setting_getMDrumStatus},
    {"get_YDrumModuleStatus",       0,  js_setting_getYDrumStatus},
    {"get_KDrumModuleStatus",       0,  js_setting_getKDrumStatus},
};

const JSCFunctionList* device_productinfo_JSCFunctionList(int *length)
{
    *length = countof(pedk_productinfo_funcs);
    return pedk_productinfo_funcs;
}

int js_device_productinfo_init(JSContext *ctx, JSValueConst global)
{
   printf("*********start device setting product info module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pedk_funcs = device_productinfo_JSCFunctionList(&count);
   printf("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pedk_funcs[i].name,
                           JS_NewCFunction(ctx, pedk_funcs[i].func, pedk_funcs[i].name, pedk_funcs[i].length));

   }
   printf("*********start device setting product info init end**********\n");
   return 0;
}

