/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file http_task.h
 * @addtogroup net
 * @{
 * @addtogroup http
 * <AUTHOR> <PERSON>n
 * @date 2023-4-20
 * @brief HTTP task parser API
 */
#ifndef __HTTP_TASK_H__
#define __HTTP_TASK_H__

#include "http_parser.h"

#ifndef ULLONG_MAX
#define ULLONG_MAX              ((uint64_t) -1) ///< 2^64-1 defined copy from http_parser.c
#endif

#define MIME_TYPE_HTML          "text/html; charset=\"utf-8\""
#define MIME_TYPE_CSS           "text/css; charset=\"utf-8\""

#define MIME_TYPE_JSON          "application/json; charset=\"utf-8\""
#define MIME_TYPE_JS_GZ         "gzip-application/x-javascript"
#define MIME_TYPE_JS            "application/x-javascript"
#define MIME_TYPE_SWF           "application/x-swf-flash"
#define MIME_TYPE_OCTET         "application/octet-stream"
#define MIME_TYPE_DOWNLOAD      "application/force-download"
#define MIME_TYPE_BINARY        "application/binary"
#define MIME_TYPE_SOAP_XML      "application/soap+xml; charset=\"utf-8\""

#define MIME_TYPE_SVG           "image/svg+xml"
#define MIME_TYPE_JPEG          "image/jpeg"
#define MIME_TYPE_TIFF          "image/tiff"
#define MIME_TYPE_PNG           "image/png"
#define MIME_TYPE_GIF           "image/gif"

#define FIELD_TITLE_LEN         128
#define FIELD_VALUE_LEN         512
#define FIELD_COUNT_MAX         32

#define JOIN_STR(x, y)          x " " y

__attribute__((unused))
static inline const char* http_status_string(int32_t code)
{
    switch ( code )
    {
#define XX(num, name, string)   case num: return JOIN_STR(#num, #string);
        HTTP_STATUS_MAP(XX)
#undef  XX
        default: NET_WARN("unknown status code(%d)", code); break;
    }

    return "<unknown>";
}

typedef enum
{
#define XX(num, name, string)   HTTP_STATUS_##num = num,
    HTTP_STATUS_MAP(XX)
#undef  XX
}
HTTP_STATUS_E;

struct http_task;

typedef int32_t (*HTTP_HEADERS_FUNC)(struct http_task* ptask, enum http_method method, const char* url, uint64_t content_length);
typedef int32_t (*HTTP_REQBODY_FUNC)(struct http_task* ptask, const char* data, size_t ndata);
typedef int32_t (*HTTP_REQUEST_FUNC)(struct http_task* ptask, enum http_method method, const char* url, const char* parms);
typedef int32_t (*HTTP_CHUNKED_FUNC)(struct http_task* ptask, uint32_t chunk_count, uint16_t chunk_size);

typedef struct http_task
{
    /**
     * @brief The public method of this HTTP_TASK_S object.
     */
    HTTP_HEADERS_FUNC       headers_complete_callback;  ///< The callback function when parsing HTTP headers complete.
    HTTP_REQBODY_FUNC       reqbody_received_callback;  ///< The callback function when receiving new request body.
    HTTP_REQUEST_FUNC       request_complete_callback;  ///< The callback function when receive HTTP request complete.
    HTTP_CHUNKED_FUNC       chunked_complete_callback;  ///< The callback function when receive a chunk complete.

    /**
     * @brief The public member of this HTTP_TASK_S object.
     */
    uint16_t                r_port;                     ///< The port of remote client in TCP connection.
    uint16_t                l_port;                     ///< The port of local service in TCP connection.

    /**
     * @brief The private member pointer of subclass.
     * priv_subclass[0]: TLS  Service
     * priv_subclass[1]: WEB  Service
     * priv_subclass[2]: IPP  Service
     * priv_subclass[3]: eSCL Service
     * priv_subclass[4]: WSD  Service
     * priv_subclass[5]: PEDK Service
     * priv_subclass[6]: PrintSDK
     * priv_subclass[7]: reserved
     * priv_subclass[8]: reserved
     * priv_subclass[9]: reserved
     */
    void*                   priv_subclass[10];
    void*                   priv;
}
HTTP_TASK_S;

/**
 * @brief       Get the title and value of the header field in current request.
 * @param[in]   ptask           : The HTTP_TASK_S object pointer.
 * @param[in]   index           : The index of headers field.
 * @param[out]  title           : The field title array.
 * @param[out]  value           : The field value array.
 * @return      The headers field count.
 * @retval      >= 0            : count
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
uint32_t        http_task_get_headers_field     (HTTP_TASK_S* ptask, const char (**title)[FIELD_TITLE_LEN], const char (**value)[FIELD_VALUE_LEN]);

/**
 * @brief       Search HTTP header field value by the title.
 * @param[in]   ptask           : The HTTP_TASK_S object pointer.
 * @param[in]   title           : The header field title.
 * @return      Search result
 * @retval      != NULL         : the header field value pointer\n
 *              == NULL         : no search this header field
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
const char*     http_task_search_header_field   (HTTP_TASK_S* ptask, const char* title);

/**
 * @brief       Append the HTTP response headers to private context, send to client when calling the http_task_reply_resp_headers.
 * @param[in]   ptask           : The HTTP_TASK_S object pointer.
 * @param[in]   field_title     : The HTTP header field title.
 * @param[in]   field_value     : The HTTP header field value.
 * @return      Append result
 * @retval      ==0             : success\n
 *              < 0             : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         http_task_append_resp_headers   (HTTP_TASK_S* ptask, const char* title, const char* value);

/**
 * @brief       Reply the HTTP response headers to client.
 * @param[in]   ptask           : The HTTP_TASK_S object pointer.
 * @param[in]   rcode           : The reply code.
 * @param[in]   content_type    : The reply content type.
 * @param[in]   content_length  : The reply content length.
 * @return      Reply result
 * @retval      >=0             : The length of send successfully\n
 *              <0              : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         http_task_reply_resp_headers    (HTTP_TASK_S* ptask, const char* rcode, const char* content_type, int32_t content_length);

/**
 * @brief       Send the buf to client.
 * @param[in]   ptask           : The HTTP_TASK_S object pointer.
 * @param[in]   buf             : The buffer pointer.
 * @param[in]   len             : The buffer length.
 * @return      Send result
 * @retval      >=0             : The length of send successfully\n
 *              <0              : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         http_task_send                  (HTTP_TASK_S* ptask, const char* buf, size_t len);

/**
 * @brief       HTTP task handling function, receive data from TCP port and parsing.
 * @param[in]   ptask           : The RAW_TASK_S object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            http_task_process               (HTTP_TASK_S* ptask);

/**
 * @brief       Destroy the HTTP_TASK_S object.
 * @param[in]   ptask           : The HTTP_TASK_S object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            http_task_destroy               (HTTP_TASK_S* ptask);

/**
 * @brief       Create the HTTP_TASK_S object.
 * @param[in]   pqio            : The QIO_S object pointer of current connection.
 * @param[in]   remote_port     : The port of remote client in TCP connection.
 * @param[in]   local_port      : The port of local  server in TCP connection.
 * @return      Create result
 * @retval      !=NULL          : The HTTP_TASK_S object pointer\n
 *              ==NULL          : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
HTTP_TASK_S*    http_task_create                (QIO_S* pqio, uint16_t remote_port, uint16_t local_port);

#endif /* __HTTP_TASK_H__ */
/**
 *@}
 */
