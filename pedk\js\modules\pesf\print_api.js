/**
 * @namespace pedk.jobs.print
 */

var ERROR_NO = globalThis.pedk.common.ERROR_NO
var Module_Main = globalThis.pedk.Module_Main
var Module_Sub = globalThis.pedk.Module_Sub
var TRAY_TYPE = Object.values(globalThis.pedk.common.TRAY_TYPE);
var MEDIA_SIZE = Object.values(globalThis.pedk.common.MEDIA_SIZE);
var MEDIA_TYPE = Object.values(globalThis.pedk.common.MEDIA_TYPE);
//var COLOR_MODE = Object.values(globalThis.pedk.jobs.print.ColorMode);

function Range_check ( value, min, max )
{
    if( value < min || value > max )
    {
        return true;
    }
    return false;
}

/**
 * U盘打印类
 * @class
 */
class PrintFromUSBMemoryJob extends PEDKPrintJob
{
    #listeners
    #job_type
    #job_id
    #job_woNum
    #job_state

    constructor(file_path)
    {
        if(typeof file_path !== 'string')
        {
            console.log("Must be only one string parmeter")
            return ERROR_NO.EINVALIDPARAM
        }
        super();
        super.PrintFromUSBMemoryJob(file_path)
        this.#listeners = []
        this.#job_type = "PRINT_USBMEMORY"
        this.#job_id = -1
        this.#job_woNum = -1
        this.#job_state = undefined
    }

    getJobId()
    {
        return this.#job_id
    }

    setJobId(job_id)
    {
        this.#job_id = job_id
        super.setJobId(job_id)
        return true
    }

    getWoNum()
    {
        return this.#job_woNum
    }

    getJobType()
    {
        return this.#job_type
    }

    getJobState()
    {
        return this.#job_state
    }

    addListener(listener)
    {
		if(typeof listener != "object")
		{
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
		}
		return this.#listeners.length + 1 === this.#listeners.push(listener)? true : false
    }

    removeListener(listener)
    {
        if(typeof listener != "object")
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        return this.#listeners.length - 1 === this.#listeners.pop(listener)? true : false
    }

    start(woNum, param, quato)
    {
       if(typeof woNum != 'number' || typeof param != 'object' || typeof quato != 'object')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        let instance = new globalThis.pedk.jobctl.JobctlManager()

        if(instance.addListeners(this, woNum) === true)
        {
            this.#job_woNum = woNum
            super.start(woNum, param, quato)

            return ERROR_NO.EXIT_SUCCESS
        }
        else
        {
           return ERROR_NO.EINVALIDPARAM
        }
    }

    notify(jobid, newJobState)
    {
 	   if(typeof jobid !== 'number' || typeof newJobState !== 'string')
 	   {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
 	   }

 	   if(this.getJobId() === -1)
 	   {
            this.setJobId(jobid)
 	   }
 	   if(this.getJobId() === jobid)
 	   {
 		   if(this.#job_state !== newJobState)
 		   {
 			   this.#job_state = newJobState
 			   for(const listener of this.#listeners)
 			   {
 				   listener.notify(newJobState)
 			   }
 		   }
 	   }
    }

    cancel()
    {
        return super.cancel()
    }
}


/**
 * 路径打印类
 * @class
 */
class PrintFromPath extends PEDKPrintJob
{
    #listeners
    #job_type
    #job_id
    #job_woNum
    #job_state
	#job_start_flag = 0

    constructor(file_path)
    {
        if(typeof file_path !== 'string')
        {
            console.log("Must be only one string parmeter")
            throw TypeError('EINVALIDPARAM')
        }
		console.log("path job constructor")
        super();
        super.PrintFromPath(file_path)
        this.#listeners = []
        this.#job_type = undefined
        this.#job_id = undefined
        this.#job_woNum = undefined
        this.#job_state = undefined
        this.#job_start_flag = 0
    }

    getJobId()
    {
        console.log("path job get job start flag"+this.#job_start_flag)
    	if(1 == this.#job_start_flag)
    	{
        	//this.#job_id = super.getJobId()
		    return this.#job_id
    	}
		else
	    {
			console.log("path job not start")
			return undefined
		}
    }

    setJobId(job_id)
    {
        //this.#job_id = job_id
        //super.setJobId(job_id)
        return true
    }

    getWoNum()
    {
        return this.#job_woNum
    }

    getJobType()
    {
        return this.#job_type
    }

    getJobState()
    {
    	console.log("path job get job state"+this.#job_state)
		console.log("path job get job start flag"+this.#job_start_flag)
    	if(1 == this.#job_start_flag)
    	{
			this.#job_state = super.getJobState()
		    return this.#job_state
    	}
		else
	    {
			console.log("path job not start")
			return undefined
		}
    }

    addListener(listener)
    {
		if(typeof listener != "object")
		{
            console.log("The parmeter type error")
            return false
		}
		return this.#listeners.length + 1 === this.#listeners.push(listener)? true : false
    }

    removeListener(listener)
    {
        if(typeof listener != "object")
        {
            console.log("The parmeter type error")
            return false
        }

        //return this.#listeners.length - 1 === this.#listeners.pop(listener)? true : false
		this.#listeners.pop(listener);
		return true;
    }

    start(woNum, param, quato)
    {
       if(typeof woNum != 'number' || typeof param != 'object' || typeof quato != 'object')
        {
            console.log("The parmeter type error")
            return 1
        }

        let instance = new globalThis.pedk.jobctl.JobctlManager()

        if(instance.addListeners(this, woNum) === true)
        {
            this.#job_woNum = woNum
			this.#job_type = "PRINT_PATH"
			this.#job_state = "JBSts_Init"
			this.#job_start_flag = 1;
            this.#job_id = super.start(woNum, param, quato)

            return 0
        }
        else
        {
           return 1
        }
    }

    notify(jobid, newJobState)
    {
 	   if(typeof jobid !== 'number' || typeof newJobState !== 'string')
 	   {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
 	   }

	   console.log("job id "+jobid)
	   console.log("newJobState "+newJobState)

	   this.jobid = jobid;

	   if(this.#job_state !== newJobState)
	   {
		   this.#job_state = newJobState
		   for(const listener of this.#listeners)
		   {
			   listener.notify(newJobState)
		   }
	   }
    }

    cancel()
    {
        return super.cancel()
    }
}


/**
 * 信息页打印类
 * @class
 */
class InternalPagePrint extends PEDKPrintJob
{
    #listeners
    #job_type
    #job_id
    #job_woNum
    #job_state
	#job_start_flag = 0

    constructor(type)
    {
        if(typeof type !== 'number')
        {
            console.log("Must be only one number parmeter")
		    throw TypeError('EINVALIDPARAM')
        }
        super();
        super.InternalPagePrint(type);
        this.#listeners = []
        this.#job_type = undefined
        this.#job_id = undefined
        this.#job_woNum = undefined
        this.#job_state = undefined
    }


    getJobId()
    {
    	if(1 == this.#job_start_flag)
    	{
        	//this.#job_id = super.getJobId()
			return this.#job_id
    	}
		else
	    {
			console.log("loacl job not start")
			return undefined
		}
    }

    setJobId(job_id)
    {
        //this.#job_id = job_id
        //super.setJobId(job_id)
        return true
    }

    getWoNum()
    {
        return this.#job_woNum
    }

    getJobType()
    {
        return this.#job_type
    }

    getJobState()
    {
    	if(1 == this.#job_start_flag)
    	{
			return super.getJobState()
    	}
		else
	    {
			console.log("local job not start")
			return undefined
		}
    }

    addListener(listener)
    {
		if(typeof listener != "object")
		{
			console.log("The parmeter type error")
            return false
		}
		return this.#listeners.length + 1 === this.#listeners.push(listener)? true : false
    }

    removeListener(listener)
    {
        if(typeof listener != "object")
        {
            console.log("The parmeter type error")
            return false
        }
        //return this.#listeners.length - 1 === this.#listeners.pop(listener)? true : false

		this.#listeners.pop(listener);
		return true;
    }

    start(woNum, param, quato)
    {
       if(typeof woNum != 'number' || typeof param != 'object' || typeof quato != 'object')
        {
            console.log("The parmeter type error")
            return 1
        }

        let instance = new globalThis.pedk.jobctl.JobctlManager()

        if(instance.addListeners(this, woNum) === true)
        {
            this.#job_woNum = woNum
			this.#job_type = "PRINT_LOCAL"
			this.#job_state = "JBSts_Init"
            this.#job_id = super.start(woNum, param, quato)
            //super.start(woNum, param, quato)

            return 0
        }
        else
        {
           return 1
        }
    }

    notify(jobid, newJobState)
    {
        if(typeof jobid !== 'number' || typeof newJobState !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

		this.jobid = jobid;

		if(this.#job_state !== newJobState)
		{
			this.#job_state = newJobState
			for(const listener of this.#listeners)
			{
				listener.notify(newJobState)
			}
		}
    }

    cancel()
    {
        return super.cancel()
    }
}

// 安全打印用户列表
const EncryptJobInfoList = new Map()

function strToJson(str) {

    var json = eval("(" + str + ")");

    return json;
}

function EncryptJobInfoListUpdate(newJobInfo)
{
    var i = 0, j = 0;
    var joblist = []

    if(newJobInfo.length < 0)
    {
        return
    }

    console.log('newJobInfo', newJobInfo[0].user_name, 'push EncryptJobInfoList', newJobInfo.length)

    for(i; i < newJobInfo.length; i++)
    {
        console.log('newJobInfo i:', i)
        console.log('user_name:',newJobInfo[i].user_name,'job_name:',newJobInfo[i].job_name)
        joblist.push(new globalThis.pedk.jobctl.JobInfo(newJobInfo[i]))
    }

    EncryptJobInfoList.set(newJobInfo[0].user_name,joblist)

    console.log('newJobInfo push EncryptJobInfoList OK')

    var jobinfo = EncryptJobInfoList.get(newJobInfo[0].user_name)

    for(i = 0; i < jobinfo.length; i++)
    {
        console.log('user_name:',jobinfo[i].user_name,'job_name:',jobinfo[i].job_name)
    }

    console.log('all user:',[...EncryptJobInfoList.keys()])

}

function getEncryptJobList_test(name, password)
{
    var i = 0;
    var joblist = [];

    if(typeof name !== 'string' || typeof password !== 'string')
    {
        throw TypeError("The parmeter type error")
    }
    if (EncryptJobInfoList.has(name))
    {
        var job_list = EncryptJobInfoList.get(name)

        for(i = 0; i < job_list.length; i++)
        {
            console.log('getEncryptJobList','user_name:',job_list[i].user_name,'job_name:',job_list[i].job_name)

            joblist.push(job_list[i])
        }
    }
    else
    {
        console.log('getEncryptJobList not found',name,password)
    }

    return joblist
}


/**
 * 安全打印作业监听类
 * @class
 */

class EncryptJobListListener
{
    constructor()
    {
        let instance = new globalThis.pedk.ObserverManager();
        instance.addListeners(this, this.EncryptJobPrintListUpdate, Module_Main.MSG_MODULE_PRINT, Module_Sub.MSG_PRINT_SUB_JOB_STATE);
        console.log('new EncryptJobListListener')
    }

    EncryptJobPrintListUpdate(obj,respond,data)
    {
        var i = 0;
        var jsonarr = strToJson(data);
        EncryptJobInfoListUpdate(jsonarr);
    }
}

const encrypt_list_listener = new EncryptJobListListener()

/**
 * 安全打印类
 * @class
 */
class EncryptJobPrint extends PEDKPrintJob
{
    #listeners
    #job_type
    #job_id
    #job_woNum
    #job_state

    constructor()
    {
        super();
        super.EncryptJobPrint();
        this.#listeners = []
        this.#job_type = "PRINT_ENCRYPT"
        this.#job_id = -1
        this.#job_woNum = -1
        this.#job_state = undefined
    }

    getJobId()
    {
        return this.#job_id
    }

    setJobId(job_id)
    {
    this.#job_id = job_id
    super.setJobId(job_id)
    return true
    }

    getWoNum()
    {
        return this.#job_woNum
    }

    getJobType()
    {
        return this.#job_type
    }

    getJobState()
    {
        return this.#job_state
    }

    addListener(listener)
    {
		if(typeof listener != "object")
		{
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
		}
		return this.#listeners.length + 1 === this.#listeners.push(listener)? true : false
    }

    removeListener(listener)
    {
        if(typeof listener != "object")
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }
        return this.#listeners.length - 1 === this.#listeners.pop(listener)? true : false
    }

    start(woNum, param, quato)
    {
       if(typeof woNum != 'number' || typeof param != 'object' || typeof quato != 'object')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        let instance = new globalThis.pedk.jobctl.JobctlManager()

        if(instance.addListeners(this, woNum) === true)
        {
            this.#job_woNum = woNum
            super.start(woNum, param, quato)

            return ERROR_NO.EXIT_SUCCESS
        }
        else
        {
           return ERROR_NO.EINVALIDPARAM
        }
    }

    notify(jobid, newJobState)
    {
        if(typeof jobid !== 'number' || typeof newJobState !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        if(this.getJobId() === -1)
        {
            this.setJobId(jobid)
        }

        if(this.getJobId() === jobid)
        {
            if(this.#job_state !== newJobState)
            {
                this.#job_state = newJobState
                for(const listener of this.#listeners)
                {
                    listener.notify(newJobState)
                }
            }
        }
    }

    cancel()
    {
        return super.cancel()
    }

    getUserNameList()
    {
        return [...EncryptJobInfoList.keys()]
    }

    getEncryptJobList(name, password)
    {
        var i = 0;
        var joblist = [];

        if(typeof name !== 'string' || typeof password !== 'string')
        {
            return ERROR_NO.EINVALIDPARAM
        }
        if (EncryptJobInfoList.has(name))
        {
            var job_list = EncryptJobInfoList.get(name)

            for(i = 0; i < job_list.length; i++)
            {
                console.log('getEncryptJobList','user_name:',job_list[i].user_name,'job_name:',job_list[i].job_name)

                joblist.push(job_list[i])
            }
        }
        else
        {
            console.log('getEncryptJobList not found',name,password)
        }

        return joblist
    }
}

/**
 * URL打印类
 * @class
 */
class URLJobPrint extends PEDKPrintJob
{
    #listeners
    #job_type
    #job_id
    #job_woNum
    #job_state

    constructor(url)
    {
        if(typeof url !== 'string')
        {
            console.log("Must be only one string parmeter")
        }
        super();
        super.URLJobPrint(url);
        this.#listeners = []
        this.#job_type = "PRINT_URL"
        this.#job_id = -1
        this.#job_woNum = -1
        this.#job_state = undefined
    }

    getJobId()
    {
        return this.#job_id
    }

    getWoNum()
    {
        return super.getWoNum();
    }

    setJobId(job_id)
    {
        this.#job_id = job_id
        super.setJobId(job_id)
        return true
    }

    getWoNum()
    {
        return this.#job_woNum
    }

    getJobType()
    {
        return this.#job_type
    }

    getJobState()
    {
        return this.#job_state
    }

    addListener(listener)
    {
		if(typeof listener != "object")
		{
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
		}
		return this.#listeners.length + 1 === this.#listeners.push(listener)? true : false
    }

    /**
     * 取消注册作业状态监听器
     * @since V1.0
     * @param {JobStateListener} listener 作业状态监听器
     * @returns {Boolean}
     * <pre>
     * true：取消成功
     * false：取消失败
     * </pre>
     */
    removeListener(listener)
    {
        if(typeof listener != "object")
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }
        return this.#listeners.length - 1 === this.#listeners.pop(listener)? true : false
    }

    start(woNum, param, quato)
    {
       if(typeof woNum != 'number' || typeof param != 'object' || typeof quato != 'object')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        let instance = new globalThis.pedk.jobctl.JobctlManager()

        if(instance.addListeners(this, woNum) === true)
        {
            this.#job_woNum = woNum
            super.start(woNum, param, quato)

            return ERROR_NO.EXIT_SUCCESS
        }
        else
        {
           return ERROR_NO.EINVALIDPARAM
        }
    }

    notify(jobid, newJobState)
    {
        if(typeof jobid !== 'number' || typeof newJobState !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        if(this.getJobId() === -1)
        {
            this.setJobId(jobid)
        }

        if(this.getJobId() === jobid)
        {
            if(this.#job_state !== newJobState)
            {
                this.#job_state = newJobState
                for(const listener of this.#listeners)
                {
                    listener.notify(newJobState)
                }
            }
        }
    }

    cancel()
    {
        return super.cancel()
    }
}

/**
 * 作业状态变化监听类
 * @class
 */
class JobStateListener
{
    constructor() { }

    notify(state)
    {
        console.log("JobStateListener")
        if(typeof state !== 'string')
        {
            return ERROR_NO.EINVALIDPARAM
        }
    }

}

/**
 * 出纸纸盒类
 * @class
 */
class OutputTray extends PEDKOutPutTray
{
    constructor(tray,size)
    {
        if(typeof tray !== 'string' || typeof size !== 'string' )
        {
            console.log("The parmeter type error")
        }

        if(!TRAY_TYPE.includes(tray))
        {
            console.log( "Parmeter out of range ! ->",tray)
        }

        if(!MEDIA_SIZE.includes(size))
        {
            console.log( "Parmeter out of range ! ->",size)
        }

        super(tray,size)
    }

    setTray(tray)
    {
        if(typeof tray !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        if(!TRAY_TYPE.includes(tray))
        {
            console.log( "Parmeter out of range ! ->",tray)
            return ERROR_NO.EINVALIDPARAM
        }

        super.setTray(tray)
        return ERROR_NO.EXIT_SUCCESS
    }

    getTray()
    {
        return super.getTray()
    }

    setPaperSize(size)
    {
        if(typeof size !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        if(!MEDIA_SIZE.includes(size))
        {
            console.log("Parmeter out of range ! ->",size)
            return ERROR_NO.EINVALIDPARAM
        }

        super.setPaperSize(size)
        return ERROR_NO.EXIT_SUCCESS
    }

    getPaperSize()
    {
        return super.getPaperSize()
    }
}


/**
 * 装订类型类
 * @class
 */
class StapleConfig extends PEDKStapleConfig
{
    constructor(staple,punch,fold,shift)
    {
        if(typeof staple !== 'string' || typeof punch !== 'string' ||
		   typeof fold !== 'string' || typeof shift !== 'string')
        {
            console.log("The parmeter type error")
        }

        super(staple,punch,fold,shift)
    }

    setStaple(staple)
    {
        if(typeof staple !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        super.setStaple(staple)
        return ERROR_NO.EXIT_SUCCESS
    }

    getStaple()
    {
        return super.getStaple()
    }

	setPunch(punch)
    {
        if(typeof punch !== 'string')
        {
            console.log("The parmeter type error")
            return ERROR_NO.EINVALIDPARAM
        }

        super.setPunch(punch)
        return ERROR_NO.EXIT_SUCCESS
    }

    getPunch()
    {
        return super.getPunch()
    }


	setFold(fold)
	{
		if(typeof fold !== 'string')
		{
			console.log("The parmeter type error")
			return ERROR_NO.EINVALIDPARAM
		}

		super.setFold(fold)
		return ERROR_NO.EXIT_SUCCESS
	}

	getFold()
	{
		return super.getFold()
	}

	setShift(shift)
	{
		if(typeof fold !== 'string')
		{
			console.log("The parmeter type error")
			return ERROR_NO.EINVALIDPARAM
		}

		super.setShift(shift)
		return ERROR_NO.EXIT_SUCCESS
	}

	getShift()
	{
		return super.getShift()
	}


}



/**
 * 纸张类型类
 * @class
 */
class PaperType extends PEDKPaperType
{
    constructor(type)
    {
        if(typeof type !== 'string')
        {
            console.log("The parmeter type error")
        }
        if(!MEDIA_TYPE.includes(type))
        {
            console.log("Parmeter out of range ! ->",type)
        }

        super(type)
    }

    setPaperType(type)
    {
        if(!MEDIA_TYPE.includes(type))
        {
            console.log("Parmeter out of range ! ->",type)
            return ERROR_NO.EINVALIDPARAM
        }
        super.setPaperType(type)

        return ERROR_NO.EXIT_SUCCESS
    }

    getPaperType()
    {
        return super.getPaperType()
    }
}

const PAPER_SAVE_MODE = [0, 1, 2, 3, 4, 5];

/**
 * 省纸打印类
 * @class
 */
class PaperSaveMode  extends PEDKPaperSaveMode
{
    constructor(mode)
    {
        if(typeof mode !== 'number'  ||  !PAPER_SAVE_MODE.includes(mode))
        {
            console.log("Must be only one number parmeter")
        }
        super(mode)
    }

    setPaperSaveMode(mode)
    {
        if( !PAPER_SAVE_MODE.includes(mode) )
        {
            console.log("Parmeter out of range ! ->",mode)
            return ERROR_NO.EINVALIDPARAM
        }

        super.setPaperSaveMode(mode)
        return ERROR_NO.EXIT_SUCCESS
    }

    getPaperSaveMode()
    {
        return super.getPaperSaveMode()
    }
}

const IMAGE_ORIENTATION_MODE = [1, 2];

/**
 * 图像方向类
 * @class
 */
class ImageOrientationMode extends PEDKImageOrientationMode
{
    constructor(mode)
    {
        if(typeof mode !== 'number')
        {
            console.log("Must be only one number parmeter")
        }
        if( !IMAGE_ORIENTATION_MODE.includes(mode) )
        {
            console.log("Parmeter out of range ! ->",mode)
        }

        super(mode)
    }

    setImageOrientationMode(mode)
    {
        if(typeof mode !== 'number')
        {
            console.log("Must be only one number parmeter")
            return ERROR_NO.EINVALIDPARAM
        }
		if( !IMAGE_ORIENTATION_MODE.includes(mode) )
		{
            console.log("Parmeter out of range ! ->",mode)
            return ERROR_NO.EINVALIDPARAM
		}
        super.setImageOrientationMode(mode)
        return ERROR_NO.EXIT_SUCCESS
    }

    getImageOrientationMode()
    {
        return super.getImageOrientationMode()
    }
}


const DUPLEX_PRINT_MODE_RANGE = [0, 1, 2];
/**
 * 双面打印模式
 * @class
 */
class DuplexPrintMode extends PEDKDuplexPrintMode
{
    constructor(mode)
    {
        if(typeof mode !== 'number')
        {
            console.log("Must be only one number parmeter")
        }
		if( !DUPLEX_PRINT_MODE_RANGE.includes(mode))
		{
			console.log("Parmeter out of range ! ->",mode)
		}

        super(mode)
    }

    setDuplexPrintMode(mode)
    {
        if(typeof mode !== 'number')
        {
            console.log("Must be only one number parmeter")
            return ERROR_NO.EINVALIDPARAM
        }
		if( !DUPLEX_PRINT_MODE_RANGE.includes(mode))
		{
			console.log("Parmeter out of range ! ->",mode)
            return ERROR_NO.EINVALIDPARAM
		}
        super.setDuplexPrintMode(mode)
        return ERROR_NO.EXIT_SUCCESS
    }

    getDuplexPrintMode()
    {
        return super.getDuplexPrintMode()
    }
}

/**
 * 逐份模式类
 * @class
 */
class CollateMode extends PEDKCollateMode
{
    constructor(mode)
    {
        if(typeof mode !== 'boolean')
        {
            console.log("Must be only one boolean parmeter")
        }

        super(mode)
    }

    setCollateMode(mode)
    {
        if(typeof mode !== 'boolean')
        {
            console.log("Must be only one boolean parmeter")
            return ERROR_NO.EINVALIDPARAM
        }

        super.setCollateMode(mode)

        return ERROR_NO.EXIT_SUCCESS
    }

    getCollateMode()
    {
        return super.getCollateMode()
    }
}

/**
 * 份数
 * @class
 */
class Copies extends PEDKCopies
{
    constructor(num)
    {
        if(typeof num !== 'number')
        {
            console.log("Must be only one number parmeter")
        }
		if(Range_check(num,1,999))
		{
			console.log("Parmeter out of range ! ->",num)
		}
        super(num)
    }

    setCopies(num)
    {
        if(typeof num !== 'number')
        {
            console.log("Must be only one number parmeter")
            return ERROR_NO.EINVALIDPARAM
        }
		if(Range_check(num,1,999))
		{
			console.log("Parmeter out of range ! ->",num)
            return ERROR_NO.EINVALIDPARAM
		}
        super.setCopies(num)
        return ERROR_NO.EXIT_SUCCESS
    }

    getCopies()
    {
        return super.getCopies()
    }
}

//const COLOR_MODE = ["COLOR_MODE_COLOR","COLOR_MODE_BLACK_WHITE"];

/**
 * 色彩模式
 * @class
 */
class ColorMode extends PEDKColorMode
{
    constructor(mode)
    {
        if(typeof mode !== 'string')
        {
            console.log("Must be only string parmeter")
		    return ERROR_NO.EINVALIDPARAM
        }
        super(mode)
    }

    setColorMode(mode)
    {
        if(typeof mode !== 'string')
        {
            console.log("Must be only string parmeter")
            return ERROR_NO.EINVALIDPARAM
        }
        super.setColorMode(mode)
        return ERROR_NO.EXIT_SUCCESS
    }

    getColorMode()
    {
        return super.getColorMode()
    }
}


/**
  * @const {String} 参数:纸张大小
  */
globalThis.pedk.jobs.print.PRINT_PARAM_OUTPUTSIZE = 'PRINT_PARAM_OUTPUTSIZE'
/**
  * @const {String} 参数:纸盒类型
  */
globalThis.pedk.jobs.print.PRINT_PARAM_OUTPUTTRAY = 'PRINT_PARAM_OUTPUTTRAY'
/**
  * @const {String} 参数:纸张类型
  */
globalThis.pedk.jobs.print.PRINT_PARAM_PAPERTYPE = 'PRINT_PARAM_PAPERTYPE'
/**
  * @const {String} 参数:打印方向模式
  */
globalThis.pedk.jobs.print.PRINT_PARAM_IMAGEORIENTATIONMODE = 'PRINT_PARAM_IMAGEORIENTATIONMODE'
/**
  * @const {String} 参数:省纸打印模式
  */
globalThis.pedk.jobs.print.PRINT_PARAM_PAPERSAVEMODE = 'PRINT_PARAM_PAPERSAVEMODE'
/**
  * @const {String} 参数:打印双面模式
  */
globalThis.pedk.jobs.print.PRINT_PARAM_DUPLEXPRINTMODE = 'PRINT_PARAM_DUPLEXPRINTMODE'
/**
  * @const {String} 参数:逐份打印模式
  */
globalThis.pedk.jobs.print.PRINT_PARAM_COLLATEMODE = 'PRINT_PARAM_COLLATEMODE'
/**
  * @const {String} 参数:打印份数
  */
globalThis.pedk.jobs.print.PRINT_PARAM_COPIES = 'PRINT_PARAM_COPIES'

/**
  * @const {String} 参数:装订
  */
globalThis.pedk.jobs.print.PRINT_PARAM_STAPLECONFIG = 'PRINT_PARAM_STAPLECONFIG'

/**
  * @const {String} 参数:色彩模式
  */
globalThis.pedk.jobs.print.PRINT_PARAM_COLORMODE = 'PRINT_PARAM_COLORMODE'


function addParameterChecek( prop_key,prop_val )
{
    if(typeof prop_key !== 'string' || typeof prop_val !== 'object' )
    {
        console.log("The parmeter type error")
        return false
    }

    if( prop_key === pedk.jobs.print.PRINT_PARAM_COLLATEMODE )
    {
        //let collate_mode = prop_val
        console.log(prop_key);
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_COPIES )
    {
        //let copies = prop_val
        console.log(prop_key);
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_DUPLEXPRINTMODE )
    {
        //let duplex_mode = prop_val
        console.log(prop_key);
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_OUTPUTTRAY )
    {
        console.log(prop_key);
        console.log('tray:',prop_val.getTray(),'papersize:',prop_val.getPaperSize())
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_PAPERSAVEMODE )
    {
        //let paper_save_mode = prop_val
        console.log(prop_key);
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_IMAGEORIENTATIONMODE )
    {
        //let image_orientation_mode = prop_val
        console.log(prop_key);
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_PAPERTYPE )
    {
        //let  paper_type = prop_val
        console.log(prop_key);

    }
	else if( prop_key === pedk.jobs.print.PRINT_PARAM_STAPLECONFIG )
    {
        console.log(prop_key);
        //console.log('staple:',prop_val.getStaple(),'punch:',prop_val.getPunch(),
			        //'fold:',prop_val.getFold(),'shift:',prop_val.getShift())
    }
    else if( prop_key === pedk.jobs.print.PRINT_PARAM_COLORMODE )
    {
        //let  paper_type = prop_val
        console.log(prop_key);

    }
    else
    {
        console.log('not support key',prop_key);

        return false
    }

    return true
}
/**
 * 打印参数集合类
 * @class
 */
class PrintParameterSet extends PEDKPrintParameterSet
{
    constructor()
    {
        super()
    }

    addParameter(prop_key,prop_val)
    {
        if(addParameterChecek(prop_key,prop_val))
        {
            return super.addParameter(prop_key,prop_val)
        }
        else
        {
            return false
        }
    }

    removeParameter(prop_key)
    {
        if(typeof prop_key !== 'string')
        {
            console.log("Must be only one string parmeter")
            return false
        }
        return super.removeParameter(prop_key)
    }
}


globalThis.pedk.jobs.print.PrintFromUSBMemoryJob = PrintFromUSBMemoryJob
globalThis.pedk.jobs.print.PrintFromPath = PrintFromPath
globalThis.pedk.jobs.print.InternalPagePrint = InternalPagePrint
globalThis.pedk.jobs.print.EncryptJobPrint = EncryptJobPrint
globalThis.pedk.jobs.print.URLJobPrint = URLJobPrint
globalThis.pedk.jobs.print.JobStateListener = JobStateListener
globalThis.pedk.jobs.print.PrintParameterSet = PrintParameterSet
globalThis.pedk.jobs.print.OutputTray = OutputTray
globalThis.pedk.jobs.print.PaperType = PaperType
globalThis.pedk.jobs.print.PaperSaveMode = PaperSaveMode
globalThis.pedk.jobs.print.ImageOrientationMode = ImageOrientationMode
globalThis.pedk.jobs.print.DuplexPrintMode = DuplexPrintMode
globalThis.pedk.jobs.print.CollateMode = CollateMode
globalThis.pedk.jobs.print.Copies = Copies
globalThis.pedk.jobs.print.StapleConfig = StapleConfig
globalThis.pedk.jobs.print.ColorMode = ColorMode



