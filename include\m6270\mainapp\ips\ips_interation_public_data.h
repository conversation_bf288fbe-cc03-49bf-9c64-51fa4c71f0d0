#ifndef  _IPS_INTERATION_PUBLIC_DATA_
#define  _IPS_INTERATION_PUBLIC_DATA_

#include "public_data_proc.h"
#include "ips/print_ips_public_data.h"

#define IPS_MAXJOBOWNERSTRING               255
#define IPS_MAXTONERMODULUSSTRING           127

typedef enum {
    POQF_PRINT_OUTPUT_DEVICE_UNCOMPRESSED,
    POQF_PRINT_OUTPUT_DEVICE_COMPRESSED,
    POQF_BITONAL,
    POQF_YCC_PIXEL_INTERLEAVED,
    POQF_YCC_LINE_INTERLEAVED,
    POQF_YCC_PLANE_INTERLEAVED,
    POQF_RGB_PIXEL_INTERLEAVED,
    POQF_RGB_LINE_INTERLEAVED,
    POQF_RGB_PLANE_INTERLEAVED,
    POQF_CMYK_PIXEL_INTERLEAVED,
    POQF_CMYK_LINE_INTERLEAVED,
    POQF_CMYK_PLANE_INTERLEAVED,
    POQF_CMYK_PLANE_INTERLEAVED_BIT_REVERSED,
    POQF_RGBT_PLANE_INTERLEAVED,                                    ///< move this up when old ips builds are purged?
    POQF_JPEG,
    POQF_UNKNOWN,
} PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E;

typedef enum  {
    IPS_MEDIA_SIZE_NALETTER,                                        ///< NORTHAMERICALETTER MEDIA SIZE
    IPS_MEDIA_SIZE_NALEGAL,                                         ///< NORTHAMERICALEGAL MEDIA SIZE
    IPS_MEDIA_SIZE_A3WIDE,                                          ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NASUPERB,                                        ///< NORTHAMERICA SUPERB/B+ MEDIA SIZE
    IPS_MEDIA_SIZE_NAEXECUTIVE,                                     ///< NORTHAMERICAEXECUTIVE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA4,                                           ///< ISOA4 MEDIA SIZE
    IPS_MEDIA_SIZE_FOLIO,                                           ///< FOLIO MEDIA SIZE
    IPS_MEDIA_SIZE_OFICIO,                                          ///< OFICIO MEDIA SIZE
    IPS_MEDIA_SIZE_ROC8K,                                           ///< ROC8K MEDIA SIZE
    IPS_MEDIA_SIZE_ROC16K,                                          ///< ROC16K MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA4WIDE,                                       ///< WIDER PRINTABLE AREA ISOA4 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA4L,                                          ///< ISOA4L MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA3,                                           ///< ISOA3 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA5,                                           ///< ISOA5 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA6,                                           ///< ISOA6 MEDIA SIZE
    IPS_MEDIA_SIZE_NAARCHITECTUREASHEET,                            ///< NORTHAMERICAARCHITECTUREASHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NANUMBER10ENVELOPE,                              ///< NORTHAMERICANUMBER10ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NAMONARCHENVELOPE,                               ///<NORTHAMERICAMONARCHENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC5ENVELOPE,                                   ///<ISOC5ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC6ENVELOPE,                                   ///< ISOC6ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PCLB5,                                           ///< ISOB5ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB5ENVELOPE,                                   ///< ISOB5ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISODLENVELOPE,                                   ///< ISODLENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JISB4,                                           ///< JISB4 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB5,                                           ///< JISB5 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB6,                                           ///< JISB6 MEDIA SIZE
    IPS_MEDIA_SIZE_JPHAGAKIPOSTCARD,                                ///< JAPANHAGAKIPOSTCARD MEDIA SIZE
    IPS_MEDIA_SIZE_JPDOUBLEHAGAKIPOSTCARD,                          ///< JAPANDOUBLEHAGAKIPOSTCARD MEDIA SIZE
    IPS_MEDIA_SIZE_JPCHOU3ENVELOPE,                                 ///< JAPANCHOU3ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPCHOU4ENVELOPE,                                 ///< JAPANCHOU4ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NASTATEMENT,                                     ///< NORTHAMERICASTATEMENT MEDIA SIZE
    IPS_MEDIA_SIZE_NA5X7,                                           ///< NORTHAMERICA5X7 MEDIA SIZE
    IPS_MEDIA_SIZE_NA4X6,                                           ///< NORTHAMERICA4X6 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA0,                                           ///< ISOA0 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA1,                                           ///< ISOA1 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA2,                                           ///< ISOA2 MEDIA SIZE
    IPS_MEDIA_SIZE_NACSHEET,                                        ///< NORTHAMERICACSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NAARCHITECTURECSHEET,                            ///< NORTHAMERICAARCHITECTURECSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NADSHEET,                                        ///< NORTHAMERICADSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NAARCHITECTUREDSHEET,                            ///< NORTHAMERICAARCHITECTUREDSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NAESHEET,                                        ///< NORTHAMERICAESHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NAARCHITECTUREESHEET,                            ///< NORTHAMERICAARCHITECTUREESHEET MEDIA SIZE
    IPS_MEDIA_SIZE_NAARCHITECTUREFSHEET,                            ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_CREDITCARD,                                      ///< CREDITCARD MEDIA SIZE
    IPS_MEDIA_SIZE_NAARCHITECTUREBSHEET,                            ///< NORTHAMERICAARCHITECTUREBSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_SRA3,                                            ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB5,                                           ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB5L,                                          ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_BIG16K,                                          ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_BIG16KL,                                         ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_POSTCARD,                                        ///< POSTCARD MEDIA SIZE
    IPS_MEDIA_SIZE_POSTCARDL,                                       ///< POSTCARDL MEDIA SIZE
    IPS_MEDIA_SIZE_LEDGER,                                          ///< NORTHAMERICAARCHITECTUREFSHEET MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA10,                                          ///< ISOA10 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA3EXTRA,                                      ///< ISOA3EXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA4EXTRA,                                      ///< ISOA4EXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA5EXTRA,                                      ///< ISOA5EXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA7,                                           ///<ISOA7 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA8,                                           ///< ISOA8 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA9,                                           ///< ISOA9 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB0,                                           ///<ISOB0 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB1,                                           ///<ISOB1 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB10,                                          ///< ISOB10 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB2,                                           ///< ISOB2 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB3,                                           ///< ISOB3 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB4,                                           ///< ISOB4 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB4ENVELOPE,                                   ///< ISOB4ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB5EXTRA,                                      ///< ISOB5EXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB7,                                           ///< ISOB7 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB8,                                           ///< ISOB8 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOB9,                                           ///< ISOB9 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC0,                                           ///< ISOC0 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC1,                                           ///< ISOC1 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC10,                                          ///< ISOC10 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC2,                                           ///< ISOC2 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC3,                                           ///< ISOC3 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC3ENVELOPE,                                   ///< ISOC3ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC4,                                           ///< ISOC4 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC4ENVELOPE,                                   ///< ISOC4ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC5,                                           ///< ISOC5 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC6,                                           ///< ISOC6 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC6C5ENVELOPE,                                 ///< ISOC6C5ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC7,                                           ///< ISOC7 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC8,                                           ///< ISOC8 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOC9,                                           ///< ISOC9 MEDIA SIZE
    IPS_MEDIA_SIZE_ISOSRA3,                                         ///< ISOSRA3 MEDIA SIZE
    IPS_MEDIA_SIZE_JP2LPHOTO,                                       ///< JAPAN2LPHOTO MEDIA SIZE
    IPS_MEDIA_SIZE_JPKAKU2ENVELOPE,                                 ///< JAPANKAKU2ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPKAKU3ENVELOPE,                                 ///< JAPANKAKU3ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPLPHOTO,                                        ///< JAPANLPHOTO MEDIA SIZE
    IPS_MEDIA_SIZE_JPQUADRUPLEHAGAKIPOSTCARD,                       ///< JAPANQUADRUPLEHAGAKIPOSTCARD MEDIA SIZE
    IPS_MEDIA_SIZE_JPYOU1ENVELOPE,                                  ///< JAPANYOU1ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPYOU2ENVELOPE,                                  ///< JAPANYOU2ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPYOU3ENVELOPE,                                  ///< JAPANYOU3ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPYOU4ENVELOPE,                                  ///< JAPANYOU4ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JPYOU6ENVELOPE,                                  ///< JAPANYOU6ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_JISB0,                                           ///< JISB0 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB1,                                           ///< JISB1 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB10,                                          ///< JISB10 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB2,                                           ///< JISB2 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB3,                                           ///< JISB3 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB7,                                           ///< JISB7 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB8,                                           ///< JISB8 MEDIA SIZE
    IPS_MEDIA_SIZE_JISB9,                                           ///< JISB9 MEDIA SIZE
    IPS_MEDIA_SIZE_NA10X11,                                         ///< NORTHAMERICA10X11 MEDIA SIZE
    IPS_MEDIA_SIZE_NA10X12,                                         ///< NORTHAMERICA10X12 MEDIA SIZE
    IPS_MEDIA_SIZE_NA10X14,                                         ///< NORTHAMERICA10X14 MEDIA SIZE
    IPS_MEDIA_SIZE_NA14X17,                                         ///< NORTHAMERICA14X17 MEDIA SIZE
    IPS_MEDIA_SIZE_NA3X5,                                           ///< NORTHAMERICA3X5 MEDIA SIZE
    IPS_MEDIA_SIZE_NA4X8,                                           ///< NORTHAMERICA4X8 MEDIA SIZE
    IPS_MEDIA_SIZE_NA8X10,                                          ///< NORTHAMERICA8X10 MEDIA SIZE
    IPS_MEDIA_SIZE_NA9X11,                                          ///< NORTHAMERICA9X11 MEDIA SIZE
    IPS_MEDIA_SIZE_NAGERMANLEGALFANFOLD,                            ///< NORTHAMERICAGERMANLEGALFANFOLD MEDIA SIZE
    IPS_MEDIA_SIZE_NAGERMANSTANDARDFANFOLD,                         ///< NORTHAMERICAGERMANSTANDARDFANFOLD MEDIA SIZE
    IPS_MEDIA_SIZE_NALEGALEXTRA,                                    ///< NORTHAMERICALEGALEXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_NALETTEREXTRA,                                   ///< NORTHAMERICALETTEREXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_NALETTERPLUS,                                    ///< NORTHAMERICALETTERPLUS MEDIA SIZE
    IPS_MEDIA_SIZE_NANOTE,                                          ///< NORTHAMERICANOTE MEDIA SIZE
    IPS_MEDIA_SIZE_NANUMBER11ENVELOPE,                              ///< NORTHAMERICANUMBER11ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NANUMBER12ENVELOPE,                              ///< NORTHAMERICANUMBER12ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NANUMBER14ENVELOPE,                              ///< NORTHAMERICANUMBER14ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NANUMBER9ENVELOPE,                               ///< NORTHAMERICANUMBER9ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NAPERSONALENVELOPE,                              ///< NORTHAMERICAPERSONALENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_NAQUARTO,                                        ///< NORTHAMERICAQUARTO MEDIA SIZE
    IPS_MEDIA_SIZE_WINSUPERA,                                       ///< WINDOWS SUPERA MEDIA SIZE
    IPS_MEDIA_SIZE_WINSUPERB,                                       ///< WINDOWS SUPERB MEDIA SIZE
    IPS_MEDIA_SIZE_NATABLOID,                                       ///< NORTHAMERICATABLOID MEDIA SIZE
    IPS_MEDIA_SIZE_NATABLOIDEXTRA,                                  ///< NORTHAMERICATABLOIDEXTRA MEDIA SIZE
    IPS_MEDIA_SIZE_PHOTOL,                                          ///< PHOTO L 3.5X5IN MEDIA SIZE
    IPS_MEDIA_SIZE_DSCPHOTO,                                        ///< ASIA STANDARD 4:3 PHOTO MEDIA SIZE
    IPS_MEDIA_SIZE_OTHERMETRICA3PLUS,                               ///< OTHERMETRICA3PLUS MEDIA SIZE
    IPS_MEDIA_SIZE_OTHERMETRICA4PLUS,                               ///< OTHERMETRICA4PLUS MEDIA SIZE
    IPS_MEDIA_SIZE_OTHERMETRICFOLIO,                                ///< OTHERMETRICFOLIO MEDIA SIZE
    IPS_MEDIA_SIZE_OTHERMETRICINVITEENVELOPE,                       ///< OTHERMETRICINVITEENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_OTHERMETRICITALIANENVELOPE,                      ///< OTHERMETRICITALIANENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC10ENVELOPE,                                   ///< PRC10ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC16K,                                          ///< PRC16K MEDIA SIZE
    IPS_MEDIA_SIZE_PRC1ENVELOPE,                                    ///< PRC1ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC2ENVELOPE,                                    ///< PRC2ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC32K,                                          ///< PRC32K MEDIA SIZE
    IPS_MEDIA_SIZE_PRC32KBIG,                                       ///< PRC32KBIG MEDIA SIZE
    IPS_MEDIA_SIZE_PRC3ENVELOPE,                                    ///< PRC3ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC4ENVELOPE,                                    ///< PRC4ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC5ENVELOPE,                                    ///< PRC5ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC6ENVELOPE,                                    ///< PRC6ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC7ENVELOPE,                                    ///< PRC7ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC8ENVELOPE,                                    ///< PRC8ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_PRC9ENVELOPE,                                    ///< PRC9ENVELOPE MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL04INCH,                                      ///< ROLL04INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL06INCH,                                      ///< ROLL06INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL08INCH,                                      ///< ROLL08INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL12INCH,                                      ///< ROLL12INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL15INCH,                                      ///< ROLL15INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL18INCH,                                      ///< ROLL18INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL22INCH,                                      ///< ROLL22INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL24INCH,                                      ///< ROLL24INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL30INCH,                                      ///< ROLL30INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL36INCH,                                      ///< ROLL36INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ROLL54INCH,                                      ///< ROLL54INCH MEDIA SIZE
    IPS_MEDIA_SIZE_ISOA5L,                                          ///< ISOA5L MEDIA SIZE
    IPS_MEDIA_SIZE_JISB5L,                                          ///< JISB5L MEDIA SIZE
    IPS_MEDIA_SIZE_PCLB5L,                                          ///< ISOB5ENVELOPEL MEDIA SIZE
    IPS_MEDIA_SIZE_NALETTERL,                                       ///< NORTHAMERICALETTERL MEDIA SIZE
    IPS_MEDIA_SIZE_NAEXECUTIVEL,                                    ///< NORTHAMERICAEXECUTIVEL MEDIA SIZE
    IPS_MEDIA_SIZE_NASTATEMENTL,                                    ///< NORTHAMERICASTATEMENTL MEDIA SIZE
    IPS_MEDIA_SIZE_ROC16KL,                                         ///< ROC16KL MEDIA SIZE
    IPS_MEDIA_SIZE_CUSTOM,                                          ///< CUSTOM MEDIA SIZE, DIMENSIONS DEFINED BY SETTINGS.PAGE.CUSTOMWIDTH/HEIGHT
    IPS_MEDIA_SIZE_PSCUSTOM,                                        ///< PS CUSTOM MEDIA SIZE USED BY XPS, DIMENSIONS DEFINED BY SETTINGS.PAGE.CUSTOMWIDTH/HEIGHT
}IPS_MEDIA_SIZE_E;

typedef enum  {
    IPS_INPUT_TRAY_AUTOSELECT,                                      ///< UNSPECIFIED
    IPS_INPUT_TRAY_STANDARD,                                        ///< STANDARD
    IPS_INPUT_TRAY_MULTIPLE,                                        ///< MULTIPLE
    IPS_INPUT_TRAY_OPTIONAL1,                                       ///< OPTIONAL1
    IPS_INPUT_TRAY_OPTIONAL2,                                       ///< OPTIONAL2
    IPS_INPUT_TRAY_OPTIONAL3,                                       ///< OPTIONAL3
    IPS_INPUT_TRAY_OPTIONAL4,                                       ///< OPTIONAL4
    IPS_INPUT_TRAY_OPTIONAL5,                                       ///< OPTIONAL5
    IPS_INPUT_TRAY_TRAY3,                                           ///< TRAY3
    IPS_INPUT_TRAY_TRAY4,                                           ///< TRAY4
    IPS_INPUT_TRAY_TRAY5,                                           ///< TRAY5
    IPS_INPUT_TRAY_EXTERNLCTIN,                                     ///< EXTERNALLCTIN
    IPS_INPUT_TRAY_EXTERNLCTOUT,                                    ///< EXTERNALLCTOUT
    IPS_INPUT_TRAY_TRAYPHOTO,                                       ///< PHOTO TRAY
    IPS_INPUT_TRAY_MANUALENV,                                       ///< MANUAL ENVELOPE
    IPS_INPUT_TRAY_OPTIONAL,                                        ///< OPTIONAL
    IPS_INPUT_TRAY_EJECT,                                           ///< EJECT
}IPS_INPUT_TRAY_E;

typedef enum  {
    IPS_MEDIA_TYPE_PLAIN1,                                          ///< PLAIN1
    IPS_MEDIA_TYPE_PLAIN2,                                          ///< PLAIN2
    IPS_MEDIA_TYPE_RECYCLED,                                        ///< RECYCLED
    IPS_MEDIA_TYPE_FILM,                                            ///< FILM
    IPS_MEDIA_TYPE_POSTCARD,                                        ///< POSTCARD
    IPS_MEDIA_TYPE_ENVELOPE,                                        ///< ENVELOPE
    IPS_MEDIA_TYPE_TAB,                                             ///< TAB
    IPS_MEDIA_TYPE_LABELS,                                          ///< LABELS
    IPS_MEDIA_TYPE_BOND,                                            ///< BOND
    IPS_MEDIA_TYPE_BANNER,                                          ///< BANNER
    IPS_MEDIA_TYPE_COLOR,                                           ///< COLOR
    IPS_MEDIA_TYPE_CARDSTOCK,                                       ///< CARDSTOCK
    IPS_MEDIA_TYPE_TRANSPARENCY,                                    ///< TRANSPARENCY
    IPS_MEDIA_TYPE_THICK1,                                          ///< USER DEFINED TYPE 1
	IPS_MEDIA_TYPE_THICK1_P,
    IPS_MEDIA_TYPE_THICK2,                                          ///< USER DEFINED TYPE 2
    IPS_MEDIA_TYPE_THICK3,                                          ///< USER DEFINED TYPE 3
    IPS_MEDIA_TYPE_THICK4,                                          ///< USER DEFINED TYPE 4
    IPS_MEDIA_TYPE_USERTYPE5,                                       ///< USER DEFINED TYPE 5
    IPS_MEDIA_TYPE_UNSPECIFIED,                                     ///< UNSPECIFIED/AUTOSELECT
    IPS_MEDIA_TYPE_BACKPRINTFILM,                                   ///< BACKPRINTFILM
    IPS_MEDIA_TYPE_PHOTOGRAPHICFILM,                                ///< PHOTOGRAPHICFILM
    IPS_MEDIA_TYPE_CONTINUOUS,                                      ///< CONTINUOUS
    IPS_MEDIA_TYPE_ENVELOPEPLAIN,                                   ///< ENVELOPEPLAIN
    IPS_MEDIA_TYPE_ENVELOPEWINDOW,                                  ///< ENVELOPEWINDOW
    IPS_MEDIA_TYPE_STATIONERY,                                      ///< STATIONERY
    IPS_MEDIA_TYPE_PHOTOGRAPHIC,                                    ///< PHOTOGRAPHIC
    IPS_MEDIA_TYPE_PHOTOGRAPHICGLOSSY,                              ///< PHOTOGRAPHICGLOSSY
    IPS_MEDIA_TYPE_PHOTOGRAPHICHIGHGLOSS,                           ///< PHOTOGRAPHICHIGHGLOSS
    IPS_MEDIA_TYPE_PHOTOGRAPHICMATTE,                               ///< PHOTOGRAPHICMATTE
    IPS_MEDIA_TYPE_PHOTOGRAPHICSATIN,                               ///< PHOTOGRAPHICSATIN
    IPS_MEDIA_TYPE_PHOTOGRAPHICSEMIGLOSS,                           ///< PHOTOGRAPHICSEMIGLOSS
    IPS_MEDIA_TYPE_ARCHIVAL,                                        ///< ARCHIVAL
    IPS_MEDIA_TYPE_FABRIC,                                          ///< FABRIC
    IPS_MEDIA_TYPE_HIGHRESOLUTION,                                  ///< HIGHRESOLUTION
    IPS_MEDIA_TYPE_LABEL,                                           ///< LABEL
    IPS_MEDIA_TYPE_MULTILAYERFORM,                                  ///< MULTILAYERFORM
    IPS_MEDIA_TYPE_MULTIPARTFORM,                                   ///< MULTIPARTFORM
    IPS_MEDIA_TYPE_NONE,                                            ///< NONE
    IPS_MEDIA_TYPE_SCREEN,                                          ///< SCREEN
    IPS_MEDIA_TYPE_SCREENPAGED,                                     ///< SCREENPAGED
    IPS_MEDIA_TYPE_TABSTOCKFULL,                                    ///< TABSTOCKFULL
    IPS_MEDIA_TYPE_TABSTOCKPRECUT,                                  ///< TABSTOCKPRECUT
    IPS_MEDIA_TYPE_TSHIRTTRANSFER,                                  ///< TSHIRTTRANSFER
}IPS_MEDIA_TYPE_E;

typedef enum  {
    IPS_LFOLD_OFF,                                                  ///< THE DEFAULT OPTION
    IPS_LFOLD_CENTER,                                               ///< CENTER OPTION
    IPS_LFOLD_DOU,                                                  ///< DOUBLE OPTION
    IPS_LFOLD_TRI,                                                  ///< TRIPLE OPTION
    IPS_LFOLD_Z,                                                    ///< Z OPTION
    IPS_LFOLD_USE_PRINTER_COF,                                      ///< USER DEFINE OPTION
}IPS_LFOLD_E;

typedef enum  {
    IPS_LSTAPLE_OFF,                                                ///< THE DEFAULT OPTION
    IPS_LSTAPLE_BOTTOMLEFT,                                         ///< BOTTOM LEFT OPTION
    IPS_LSTAPLE_BOTTOMLEFTAUTO,                                     ///< BOTTOM LEFT AUTO OPTION
    IPS_LSTAPLE_BOTTOMRIGHT,                                        ///< BOTTOM RIGHT OPTION
    IPS_LSTAPLE_BOTTOMRIGHTAUTO,                                    ///< BOTTOM RIGHT AUTO OPTION
    IPS_LSTAPLE_DUALBOTTOM,                                         ///< DUAL BOTTOM OPTION
    IPS_LSTAPLE_DUALLEFT,                                           ///< DUAL LEFT OPTION
    IPS_LSTAPLE_DUALRIGHT,                                          ///< DUAL RIGHT OPTION
    IPS_LSTAPLE_DUALTOP,                                            ///< DUAL TOP OPTION
    IPS_LSTAPLE_TOPLEFT,                                            ///< TOP LEFT OPTION
    IPS_LSTAPLE_TOPLEFTAUTO,                                        ///< TOP LEFT AUTO OPTION
    IPS_LSTAPLE_TOPRIGHT,                                           ///< TOP RIGHT OPTION
    IPS_LSTAPLE_TOPRIGHTAUTO,                                       ///< TOP RIGHT AUTO OPTION
    IPS_LSTAPLE_USE_PRINTER_COF,                                    ///< USER DEFINE OPTION
}IPS_LSTAPLE_E;

typedef enum {
    IPS_LPAPER_BOX_STANDARD,                                        ///< the default option
    IPS_LPAPER_BOX_PAPERBOX1,                                       ///< PaperBox1 option
    IPS_LPAPER_BOX_PAPERBOX2,                                       ///< PaperBox2 option
    IPS_LPAPER_BOX_PAPERBOX3,                                       ///< PaperBox3 option
    IPS_LPAPER_BOX_USE_PRINTER_COF,                                 ///< user define option
}IPS_LPAPER_BOX_E;

typedef enum  {
    IPS_LPUNCHING_OFF,                                              ///< THE DEFAULT OPTION
    IPS_LPUNCHING_FOURHOLESBOTTOM,                                  ///< 4 HOLES BOTTOM OPTION
    IPS_LPUNCHING_FOURHOLESLEFT,                                    ///< 4 HOLES LEFT OPTION
    IPS_LPUNCHING_FOURHOLESRIGHT,                                   ///< 4 HOLES RIGHT OPTION
    IPS_LPUNCHING_FOURHOLESTOP,                                     ///< 4 HOLES TOP OPTION
    IPS_LPUNCHING_FOURHOLESAUTO,                                     ///< 4 HOLES AUTO OPTION
    IPS_LPUNCHING_TWOHOLESBOTTOM,                                   ///< 2 HOLES BOTTOM OPTION
    IPS_LPUNCHING_TWOHOLESLEFT,                                     ///< 2 HOLES LEFT OPTION
    IPS_LPUNCHING_TWOHOLESRIGHT,                                    ///< 2 HOLES RIGHT OPTION
    IPS_LPUNCHING_TWOHOLESTOP,                                      ///< 2 HOLES TOP OPTION
    IPS_LPUNCHING_TWOHOLESAUTO,                                     ///< 2 HOLES AUTO OPTION
    IPS_LPUNCHING_USE_PRINTER_COF,                                  ///< USER DEFINE OPTION
}IPS_LPUNCHING_E;

typedef enum  {
    IPS_LOFFSET_OFF,                                                ///< the default option
    IPS_LOFFSET_ON,                                                 ///< on option
    IPS_LOFFSET_USEPRINTERCOF,                                      ///< user define option
}IPS_LOFFSET_E ;

 //for separator_page
typedef enum
{
    IPS_SEPARATOR_PAGE_OFF,                                            ///< Close Delimiter
    IPS_SEPARATOR_PAGE_PAGES,                                          ///< OHP insert pages
    IPS_SEPARATOR_PAGE_COPIES,                                         ///< Between copies
    IPS_SEPARATOR_PAGE_JOBS,                                           ///< Between JOBs
    IPS_SEPARATOR_PAGE_USEPRINTTRCOF,                                  ///< Using printer s
}IPS_SEPARATOR_E;

/*
   brief: the image direct
 */
typedef enum {
    eIPS_ORIENTATION_PORTRAIT,               /**< Portrait */
    eIPS_ORIENTATION_LANDSCAPSE,             /**< Landscape */
    eIPS_ORIENTATION_REVERSE_PORTRATI,       /**< Reverse Portrait */
    eIPS_ORIENTATION_REVERSER_LANDSCAPSE,    /**< Reverse Landscape */
} IPS_ORIENTATION_E ;

typedef enum {
    IPS_PRINT_SCALE_MODE_AUTO = 0,
    IPS_PRINT_SCALE_MODE_FIT = 1 ,
    IPS_PRINT_SCALE_MODE_AUTOFIT = 2,
    IPS_PRINT_SCALE_MODE_FILL = 3,
    IPS_PRINT_SCALE_MODE_CENTER = 4,
} IPS_PRINT_SCALE_MODE_E;

typedef enum {
    IPS_IPP_FIDELITY_UNSPECIFIED =0,
    IPS_IPP_FIDELITY_FORCE =1,
    IPS_IPP_FIDELITY_AUTO,
}IPS_PRINT_IPP_FIDELITY_E;

typedef enum {
    IPS_PRINT_CUSTOM_TYPE_1 = 0,
    IPS_PRINT_CUSTOM_TYPE_2,
    IPS_PRINT_CUSTOM_TYPE_3,
    IPS_PRINT_CUSTOM_TYPE_4,
    IPS_PRINT_CUSTOM_TYPE_5,
    IPS_PRINT_CUSTOM_TYPE_INVALID
}IPS_PRINT_CUSTOM_TYPE_E;

typedef enum
{
    IPS_EDGE_SHORT = 0x0,
    IPS_EDGE_LONG,
    IPS_EDGE_INVALID = 0xFFFF
}IPS_EDGE_E;

typedef enum
{
    IPS_PRINT_MODE_SINGLE = 0,
    IPS_PRINT_MODE_AUTO_DUPLEX = 1,
    IPS_PRINT_MODE_INVALID,
}IPS_PRINT_MODE_E;

typedef enum {
    POQA_COPY,
    POQA_PFH,
    POQA_PHOTOPRINT,
    POQA_FAX,
    POQA_POSTSCRIPT,
    POQA_PCL,
    POQA_IPS,
    POQA_SWT,
} PAGE_OUTPUT_QUEUE_APP_E;

typedef struct{
    IPS_LFOLD_E ips_fold_mode;
    IPS_LPUNCHING_E ips_punching;
    IPS_LSTAPLE_E ips_lstaple;
    IPS_ORIENTATION_E  ips_orientation;
} IPS_BOOKBINDING_S;


typedef enum {
    POQS_OK,
    POQS_CANCELED,
    POQS_ACCEPTING_DATA,
    POQS_MEMORY_FULL,
    POQS_DATA_COMPLETE,
    POQS_DELIVERING_DATA,
    POQS_JAM,
    POQS_PAPER_OUT,
    POQS_PAGE_LIFETIME_OVER,
    POQS_UNKNOWN_ERROR,
    POQS_PAGE_UNKNOWN,
} PAGE_OUTPUT_QUEUE_STATUS_E;

typedef struct
{
    int32_t  copies;                            ///< 份数：-1    面板不设定 大于0 期待打印的份数
    int32_t  color;                             ///< 色彩：-1 面板不设定 0 彩色 1 黑白
    int32_t  cover;                             ///< 封面封底  -1 面板不设定 0 添加封面 1 添加封底 2 同时添加封面封底
    int32_t   interleave_page_mode;              ///< 插页模式开关       0 关 1:开
    uint32_t interleave_page_number;            ///< 插页的页数        最大30页
    uint32_t interleave_page_position[30];      ///< 插页的位置        按顺序从小到大
    int32_t   chapters_page_mode;                ///< 章节页模式开关         0 关 1:开
    uint32_t chapters_page_number;              ///< 插入章节页的页数          上限制30页
    uint32_t chapters_page_position[30];        ///< 插入章节页的位置
                                                ///< 章节只有在双面打印时有效
                                                ///< 节页必须为双面打印的正面，不满足条件时可以在章节页前面补充空白页
    int32_t  reverse[4];                        ///< 保留，暂不使用
}IPS_SAMPLE_PARAM_S;

typedef struct
{
    IPS_PRINT_PRINT_MODE print_print_mode;                              ///<双面模式
    IPS_PRINT_COLLATE_MODE print_collate_mode;                          ///<逐份模式
    int print_copies;                                                   ///<份数 0~9999
    IPS_INPUT_TRAY_E     print_input_tray;                              ///<纸盒选择
    IPS_MEDIA_SIZE_E     print_paper_size;                               ///<纸张尺寸
    int print_paper_size_custom_width;                                  ///<自定义纸宽
    int print_paper_size_custom_height;                                 ///<自定义纸高
    IPS_MEDIA_TYPE_E  print_paper_type;                                 ///纸张介质类型
    int print_resolution;                                               ///<分辨率模式 3 600DPI
    IPS_PRINT_COLOR_MODE print_color_mode;                              ///<颜色模式
    IPS_PRINT_ALL_IN_ONE_PRINT  print_all_in_one_print;                 ///<多合一打印
    char page_range[65];                                                ///<可选页面打印,全为0表示关闭
    IPS_PRINT_DOCUMENT_TYPE print_document_type;                        ///<文档类型
    IPS_PRINT_IMAGE_ROTATE_DIR print_image_rotate_dir;                  ///<图像旋转方向
    IPS_LFOLD_E print_fold_mode;                                        ///<折叠模式
    IPS_LSTAPLE_E print_staple_mode;                                    ///<钉钉模式
    IPS_PRINT_PUNCH_MODE print_punch_mode;                              ///<打孔模式
    IPS_LOFFSET_E print_shift_mode;                                     ///<偏移模式
    IPS_LPAPER_BOX_E  print_tray_receive;                               ///<接纸架
    int print_image_color_balance_C;                                    ///<色彩平衡-青色 1-11 默认6
    int print_image_color_balance_M;                                    ///<色彩平衡-品红色 1-11 默认6
    int print_image_color_balance_Y;                                    ///<色彩平衡-黄色 1-11 默认6
    int print_image_color_balance_K;                                    ///<色彩平衡-黑色 1-11 默认6
    int print_image_color_density;                                      ///<碳粉浓度 1-11 默认6
    int print_image_color_saturation;                                   ///<RGB饱和度 1-11 默认6
    int print_image_color_contrast;                                     ///<RGB对比度 1-11 默认6
    int print_image_color_brightness;                                   ///<明亮度 1-11 默认6
    IPS_SEPARATOR_E print_separate_page;                                ///<分隔页
    IPS_INPUT_TRAY_E print_input_tray_original;                         ///<纸盒选择-用户
    IPS_INPUT_TRAY_E print_separate_page_tray_in;                       ///<分隔页进纸盒来源
    uint32_t print_fold_number;                                         ///< 65535：每份折一次 1-10：N页折叠一次
    IPS_PRINT_NUP_ORDER print_all_in_one_order;                         ///< 多合一顺序
    IPS_PRINT_SCALE_MODE_E print_scale_mode;                            ///<缩放模式
    IPS_PRINT_IPP_FIDELITY_E print_ipp_fidelity;                        /// <保真度
    uint32_t                 print_paper_size_custom_type;              /// < custom_type
    uint32_t                 print_paper_size_auto_flag;                /// < 0: no auto paper size  1: auto paper size
    uint32_t reverse[5];                                                ///< 保留字
}IPS_PRINT_PARAM_S;

typedef struct {
    unsigned long    job_setting_rotate;
    unsigned long    job_setting_print_mode;                            ///< 打印模式 0-单面打印(默认) 1-双面长边打印
    unsigned long    job_setting_nup_mode;
    unsigned long    job_setting_media_type;
    unsigned long    job_setting_XDi_mension;
    unsigned long    job_setting_YDi_mension;
    unsigned long    job_setting_input_tray;
    unsigned long    job_setting_color_mode;
    unsigned long    job_setting_enable;
    unsigned long    job_setting_media_size;
    unsigned long    system_status_for_mono;                            ///< 色彩 0-打印机下发      -1-黑白
    unsigned long    system_support_media_size;
    unsigned long    UI_color_mode;
    unsigned long    UI_type;

    unsigned long    UI_document_type;
    unsigned long    UI_binding_mode;
    unsigned long    UI_punch_mode;
    unsigned long    UI_fold_mode;
    unsigned long    UI_paper_tray_mode;
    unsigned long    UI_offset_mode;
    unsigned long    UI_color_balance_C;                                ///< 青色1-11 default 6
    unsigned long    UI_color_balance_M;                                ///< 品红色 1-11 default 6
    unsigned long    UI_color_balance_Y;                                ///< 黄色 1-11 default 6
    unsigned long    UI_color_balance_K;                                ///< 黑色 1-11 default 6
    unsigned long    UI_density;                                        ///< 碳粉浓度1-11 default 6
    unsigned long    UI_saturation;                                     ///< 饱和度 1-11    default 6
    unsigned long    UI_contrast;                                       ///< 对比度 1-11 default 6
    unsigned long    UI_toner_save;                                     ///< 省墨模式     0 – OFF,1 –ON
    unsigned long    UI_brightness;                                     ///< 明亮度 1-11 default 6
    unsigned long    UI_original_type;                                  ///< 原稿类型     1-3; 1-Document; 2-Photo;3-Mixed(默认)
}IPS_LIB_PARAM_S;


/*** Variable description area ****************************************/
typedef struct page_output_queue_page
{
    int     inuse;                                                  ///<
    int     page_start;                                             ///<
    int     bandH;                                                  ///<
    int     bandY[ccMAX];                                           ///<
    int     page_number;                                            ///< what IPS lied

    int     width;                                                  ///< 宽度
    int     height;                                                 ///< 高度
    int     depth;                                                  ///< 位深
    int     byte_stride;                                            ///< 步幅字节数
    int     color_format;                                           ///< 颜色格式
    int     size_valid;                                             ///< 宽度、高度越界标志

    int     paper_size;                                             ///< 纸张尺寸
    int     paper_source;                                           ///< 纸张来源
//    BAND_P   pband;                                                 ///< 图像数据band指针
    int     band_num;                                               ///< band的数量
    int     delay_flag;                                             ///< 延时标志

    uint32_t lpunching;                                             ///< punching, value from pjl only
    uint32_t lpaperbox;                                             ///< paperbox, value from pjl only
    uint32_t loffset;                                               ///< offset, value from pjl only
    uint32_t lfold;                                                 ///< lfold, value from pjl only
    uint32_t lstaple;                                               ///< lstaple, value from pjl only
    uint32_t qty;                                                   ///< qty, value from pjl only */
    uint32_t lorientation;                                          ///< orientation, value from pjl only
    uint32_t tonerSave;                                             ///< 0:OFF;   1: ON
    uint32_t user_collate;                                          ///< 0:OFF;   1: ON
    uint32_t user_copies;                                           ///< 1-999
    uint32_t user_page_max_one_copy;                                ///< 1-999
}POQP, *PPOQP;

typedef struct page_output_queue_set *PAGE_OUTPUT_QUEUE_SET_P;
typedef void (*page_output_queue_free)(void *buf);
typedef struct page_output_queue_page *PAGE_OUTPUT_QUEUE_PAGE_P;

typedef void (*page_output_queue_callback)(PAGE_OUTPUT_QUEUE_SET_P set,
                                        PAGE_OUTPUT_QUEUE_PAGE_P page,
                                        PAGE_OUTPUT_QUEUE_STATUS_E status);

struct tag_prtips_context;
typedef struct page_output_queue_set
{
    int                         complete;                           //
    uint32_t                      copies;                           //
    int                         collate;                            //
    PAGE_OUTPUT_QUEUE_APP_E          producing_app;                 //
    PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E incoming_format;              //
    PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E storage_format;               //
    page_output_queue_free         freeFunc;                        //
    page_output_queue_callback     callback;                        //
}POQS, *PPOQS;

typedef struct page_output_queue_page_desc {
    PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E format;
    uint32_t bits_per_component;
    uint32_t height;
    uint32_t width;
    uint32_t dpi_x;
    uint32_t dpi_y;
    uint32_t mediatype;                                             ///< papertype, now supported
    uint32_t papersize;                                             ///< This comes from IPS POQ driver.
    uint32_t tray;
    uint32_t isenvelope;                                            ///< TRUE = env, FALSE = paper.
    uint32_t duplex_field;                                          ///< 0 => simplex, 1 => front, 2 => back
    uint32_t density;                                               ///< SDRAGON, value from PJL
    uint32_t copycount;                                             ///< SDRAGON - pass it to inferno for band playback
    uint32_t intray1;                                               ///< value from pjl only
    uint32_t intray2;                                               ///< value from pjl only
    uint32_t intray3;                                               ///< value from pjl only
    uint32_t intray4;                                               ///< value from pjl only
    uint32_t manualfeed;                                            ///< value from pjl + parser
    uint32_t mpffeed;                                               ///< value from pjl
    uint32_t spin_cycle;                                            ///< effectively postscript tumble
    uint32_t renderg2;                                              ///< render model 600*600*1/1200*1200*1 黑白 221 彩色218 600*600*4 黑白 219 彩色 217
    uint32_t fine_field;                                            ///< 0: off 1: on
    uint32_t jobid;                                                 ///< jobid, value from pjl only
    uint32_t lpunching;                                             ///< punching, value from pjl only
    uint32_t lpaperbox;                                             ///< paperbox, value from pjl only
    uint32_t loffset;                                               ///< offset, value from pjl only
    uint32_t paper_source;
    uint32_t lfold;                                                 ///< lfold, value from pjl only
    uint32_t lfoldPages;
    uint32_t lstaple;                                               ///< lstaple, value from pjl only
    uint32_t qty;                                                   ///< qty, value from pjl only
    uint32_t lorientation;                                          ///<orientation, value from pjl only
    uint32_t tonerSave;                                             ///< 0:OFF;   1: ON
    uint32_t allinone;                                              ///< Nin1, value from pjl only
    uint32_t user_collate;                                          ///< 0:OFF;   1: ON
    uint32_t user_copies;                                           ///< 1-999
    uint32_t user_page_max_one_copy;                                ///< 1-999
    uint32_t covermediatype;                                        ///< cover papertype, now supported
    uint32_t mixsize;
    uint32_t mixtype;
    uint32_t pageNumber;
    uint32_t custompaperwidth;
    uint32_t custompaperheight;
    uint32_t custompaperregion;
    INSERT_PAGE_E insert_page;                                      ///< is it a empty page，used at separator_page
    uint32_t separatepage;
    uint32_t blankpage;                                             ///<
    uint32_t original_orientation;                                  ///< original orientation
    char   jobowner[IPS_MAXJOBOWNERSTRING + 1];
    char   jobname[IPS_MAXJOBOWNERSTRING + 1];
    char   username[IPS_MAXJOBOWNERSTRING + 1];
    char   jobpassword[IPS_MAXJOBOWNERSTRING + 1];
    char   tonermodulus[IPS_MAXTONERMODULUSSTRING + 1];
    uint32_t shadow_page;
    uint32_t cover;                                                 ///< 0: No cover setting , 1: Front cover  2: Back cover  3: Front cover and Back cover
    uint32_t sample_original_copies;                                ///< sample print original copois
    uint32_t custom_size_type;                                      ///< custom type
    uint32_t rotateToPrint;                                         ///< rotate
    uint32_t total_pages;                                           ///< job total page
    uint32_t color_mode;                                            ///< JOB_COLOR_MODE_E
    int32_t reserved;                                               ///< reserved
}PAGE_OUTPUT_QUEUE_PAGE_DESC_S;

/* the print-from-ips context */
typedef struct tag_prtips_context
{
    int       inuse;                                                ///< 使用标示。1-表示IPS模块正在解析；0-表示IPS模块空闲

    // 与作业相关的参数
    uint32_t       job_id;                                               ///< 作业ID
    uint32_t       resource_id;                                          ///< 资源ID
    char      job_name[128];                                        ///< 作业名称
    int       job_type;                                             ///< 作业类型
    int       IPS_type;
    GQIO_S*   pgqio;                                                ///< IPS模块数据来源 GQIO 句柄
    MODULE_ID_E prev_module;
    MODULE_ID_E next_module;                                        ///< IPS模块数据输出的目标模块 ID

    MEMOBJ_P   hImage_mem;                                          ///< image memory allocator

    POQP      page;                                                 ///< 页面的一些参数，与 poq_page_desc 一起组成页面信息
    int       page_num;                                             ///< 页数
    PAGE_OUTPUT_QUEUE_PAGE_DESC_S
              poq_page_desc;                                        ///< IPS模块解析出来的页面信息描述

    POQS      poq_set;                                              ///< page output queue set
    int       poq_busy;
    int       poq_busy_cancel;
                                                                    ///< direct office作业取消延迟标志: 1-需要在作业输出时，置位取消标志；0-不需要。
                                                                    ///< 原因: 如果在还没有图像输出时取消，IPS会卡死，所以需要延迟取消。
    int       do_cancel_set;

    int       job_start;                                            ///< 1-表示还没有发送 job start 消息给 next_module; 0-表示已发送
    int       job_cancel;                                           ///< 1-表示作业取消; 0-表示正常解析
    int       set_rotate;                                           ///< 是否已经设置翻转标志
    int       rotate;                                               ///< 翻转标志

    void     *pResourceData;                                        ///< 存放系统作业结构体指针，与 system job manager 通信用
    int      cancel_msg_got_if;                                     ///< if receive cancel msg 0: No ,1 Yes
    int      color_type;                                            ///< 0:color 1: mono
    void     *pjob_data_addr;                                       ///< 存放job地址,用于唤醒IPM
}IPSCTX, *PIPSCTX;
/**********************************************************************/

#endif
