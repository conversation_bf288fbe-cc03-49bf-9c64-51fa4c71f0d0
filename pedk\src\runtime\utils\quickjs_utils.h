/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file quickjs_utils.h
 * @addtogroup utils
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief quickjs utils
 */
#ifndef __RUNTIME__UTILS__QUICKJS_UTILS_H__
#define __RUNTIME__UTILS__QUICKJS_UTILS_H__

#include <quickjs.h>

#ifndef countof
#define countof(x) (sizeof(x) / sizeof((x)[0]))
#endif

/**
 * @brief   quickjs load js file
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] *file_path :file path
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int quickjs_load_js_file(JSContext *ctx, const char *file_path);

/**
 * @brief   quickjs print exception
 * @param[in] *ctx :a heap space runtime handle
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
void quickjs_print_exception(JSContext *ctx);

/**
 * @brief   quickjs utils std dump error
 * @param[in] *ctx :a heap space runtime handle
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
void quickjs_utils_std_dump_error(JSContext *ctx);

#endif // __RUNTIME__UTILS__QUICKJS_UTILS_H__
/**
 * @}
 */
 
