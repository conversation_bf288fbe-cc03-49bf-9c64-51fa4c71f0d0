/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ipv4.c
 * @addtogroup net
 * @{
 * @addtogroup ipv4
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network IPv4 address manager API
 */
#include "nettypes.h"
#include "netmisc.h"
#include "netsock.h"
#include "ipv4.h"

#define DHCP_IPV4_SCRIPT_FILE       "/usr/share/udhcpc/dhcpv4.script"
#define DHCP_DNS_SCRIPT_FILE        "/usr/share/udhcpc/dnsv4.script"
#define DHCP_PID_FILE_FORMAT        NET_BASE_DIR "/dhcp.pid.%s"

#define ARP_PROBE_WAIT              1   ///< ARP初次探测延时
#define ARP_PROBE_INTERVAL          2   ///< ARP连续探测的间隔时间
#define ARP_PROBE_NUM               3   ///< 发送探测包的数量
#define ARP_RATE_LIMIT_INTERVAL     60  ///< IP冲突达到最大数时，探测的间隔时间
#define ARP_MAX_CONFLICTS           10  ///< IP冲突最大数(超过之后必须用ARP_RATE_LIMIT_INTERVAL)
#define ARP_ANNOUNCE_NUM            2   ///< ARP公告数量
#define ARP_ANNOUNCE_INTERVAL       2   ///< ARP公告间隔
#define ARP_DEFEND_INTERVAL         10  ///< IP冲突等待确认的时间
#define ARP_MSG_SIZE                42  ///< sizeof(struct arp_msg) - sizeof(arp.pad)

typedef enum
{
    LLA_STEP_ADDR_USE_DHCP = 0,
    LLA_STEP_ADDR_PROBING,
    LLA_STEP_ADDR_ANNOUNCING,
    LLA_STEP_ADDR_DEFENDING,
    LLA_STEP_ADDR_RESET,
}
LLA_STEP_E;

struct arp_msg 
{
    /**
     * @brief Ethernet header
     */
    uint8_t         dst_mac[6];         ///< 00 destination ether addr
    uint8_t         src_mac[6];         ///< 06 source ether addr
    uint16_t        proto;              ///< 0c packet type ID field

    /**
     * @brief ARP packet
     */
    uint16_t        htype;              ///< 0e hardware type (must be ARPHRD_ETHER)
    uint16_t        ptype;              ///< 10 protocol type (must be ETH_P_IP)
    uint8_t         hlen;               ///< 12 hardware address length (must be 6)
    uint8_t         plen;               ///< 13 protocol address length (must be 4)
    uint16_t        operation;          ///< 14 ARP opcode
    uint8_t         sender_mac[6];      ///< 16 sender's hardware address
    uint32_t        sender_addr;        ///< 1c sender's IP address
    uint8_t         target_mac[6];      ///< 20 target's hardware address
    uint32_t        target_addr;        ///< 26 target's IP address
    uint8_t         pad[18];            ///< 2a pad for min. ethernet payload
}
#ifdef _MSC_VER
#pragma pack(1)
#else
__attribute__((__packed__))
#endif
; /* ARP数据包单字节对齐 */

#if CONFIG_NET_WIFI
static PI_THREAD_T  s_lla_thread[IFACE_ID_STA + 1] = { INVALIDTHREAD, INVALIDTHREAD };
#else
static PI_THREAD_T  s_lla_thread[IFACE_ID_ETH + 1] = { INVALIDTHREAD };
#endif

/**
 * @brief       Generate default link-local-address for this network interface(ifname),\n
 *              the default address base on MAC address.
 * @param[out]  buf         : generate default link-local-address and set to this buffer.
 * @param[in]   size        : buffer size.
 * @param[in]   mac         : mac address of this interface(must has 6 bytes).
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static void generate_default_lladdr(char* buf, size_t size, uint8_t* mac)
{
    uint8_t tmp[2] = {0x01, 0x01};
    uint8_t i, j;

    for ( (i = 0, j = 0) ; (i < 6 && j < 2) ; ++i )
    {
        if ( mac[5 - i] != 0x0 && mac[5 - i] != 0xff )
        {
            tmp[j++] = mac[5 - i];
        }
    }
    snprintf(buf, size, "169.254.%u.%u", tmp[1], tmp[0]);
}

/**
 * @brief       Measure link-local-address for this network interface(ifname).
 * @param[I&O]  buf         : measure link-local-address and set to this buffer.
 * @param[in]   size        : buffer size.
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static void measure_lladdr(char* buf, size_t size)
{
    in_addr_t addr;

    addr = inet_network(buf) + 1;               ///< 按主机字节序将IPv4地址字符串转无符号整形(in_addr_t)
    if ( (addr & 0x000000ff) == 0x000000ff )    ///< 169.254.x.255
    {
        addr += 2;                              ///< 169.254.(x+1).1
    }
    if ( (addr & 0x0000ff00) == 0x0000ff00 )    ///< *************
    {
        addr &= 0xffff01ff;                     ///< ***********
    }
    snprintf(buf, size, "%d.%d.%d.%d", (addr >> 24) & 0xFF, (addr >> 16) & 0xFF, (addr >> 8) & 0xFF, addr & 0xFF);
}

/**
 * @brief       Get IPv4 address string of this network interface(ifname).
 * @param[in]   ifname      : interface name.
 * @param[out]  buf         : Get IPv4 address string and set to this buffer.
 * @param[in]   size        : buffer size.
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static void get_ipv4_addr(const char* ifname, char* buf, size_t size)
{
    PI_SOCKET_T     sockfd;
    struct ifreq    ifr;

    sockfd = netsock_create_custom(AF_INET, SOCK_STREAM, IPPROTO_IP);
    RETURN_IF(sockfd == INVALID_SOCKET, NET_WARN);

    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
    if ( ioctl(sockfd, SIOCGIFADDR, &ifr) == 0 )
    {
        inet_ntop(AF_INET, &(((struct sockaddr_in *)&(ifr.ifr_addr))->sin_addr), buf, size);
    }
    pi_closesock(sockfd);
}

/**
 * @brief       Create the ARP socket handle for this network interface(ifname).
 * @param[in]   ifname      : interface name.
 * @return      The socket handle
 * @retval      >=0         : success\n
 *              < =         : fail
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static PI_SOCKET_T create_arp_socket(const char* ifname)
{
    PI_SOCKET_T         sockfd;
    struct sockaddr_ll  sal;
    struct ifreq        ifr;
    int32_t             val;
    int32_t             ret;

    sockfd = netsock_create_custom(PF_PACKET, SOCK_RAW, htons(ETH_P_ARP));
    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, INVALID_SOCKET);

    do
    {
        val = 1;
        ret = ioctl(sockfd, FIONBIO, &val);
        BREAK_IF(ret != 0, NET_WARN);

        memset(&ifr, 0, sizeof(ifr));
        snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
        ret = ioctl(sockfd, SIOCGIFINDEX, &ifr);
        BREAK_IF(ret != 0, NET_WARN);

        memset(&sal, 0, sizeof(sal));
        sal.sll_family   = PF_PACKET;
        sal.sll_protocol = htons(ETH_P_ARP);
        sal.sll_ifindex  = ifr.ifr_ifindex;
        ret = bind(sockfd, (struct sockaddr *)&sal, sizeof(sal));
        BREAK_IF(ret != 0, NET_WARN);
    }
    while ( 0 );

    if ( ret != 0 )
    {
        NET_WARN("%s create ARP socket failed(%d): %d<%s>", ifname, ret, errno, strerror(errno));
        pi_closesock(sockfd);
        sockfd = INVALID_SOCKET;
    }
    return sockfd;
}

/**
 * @brief       Process arp packet to check address conflicts for this network interface(ifname).
 * @param[in]   ifname      : The interface name.
 * @param[in]   sockfd      : The measure link-local-address and set to this buffer.
 * @param[in]   probe_addr  : The probe address.
 * @param[in]   local_addr  : The local address.
 * @param[in]   mac         : The local MAC.
 * @param[in]   interval    : The interval time.
 * @return      Check result
 * @retval      != 0        : has address conflicts\n
 * @retval      == 0        : no conflicts\n
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static int32_t process_arp(const char* ifname, PI_SOCKET_T sockfd, uint32_t probe_addr, uint32_t local_addr, uint8_t* mac, int32_t interval)
{
    struct sockaddr_ll  sal;
    struct arp_msg      arp;
    struct ifreq        ifr;
    struct timeval      tmv = {.tv_sec = (__time_t)interval, .tv_usec = 0};
    int32_t             ret = 0;
    fd_set              fds;

    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
    RETURN_VAL_IF(ioctl(sockfd, SIOCGIFINDEX, &ifr) < 0, NET_WARN, 0);

    memset(&arp, 0, sizeof(arp));
    memset(arp.dst_mac, 0xff, 6);               ///< MAC DA
    memcpy(arp.src_mac, mac,  6);               ///< MAC SA
    arp.proto       = htons(ETH_P_ARP);         ///< protocol type (Ethernet)
    arp.htype       = htons(ARPHRD_ETHER);      ///< hardware type
    arp.ptype       = htons(ETH_P_IP);          ///< protocol type (ARP message)
    arp.hlen        = 6;                        ///< hardware address length
    arp.plen        = 4;                        ///< protocol address length
    arp.operation   = htons(ARPOP_REQUEST);     ///< ARP op code
    arp.target_addr = probe_addr;               ///< target IP address
    arp.sender_addr = local_addr;               ///< sender IP address
    memcpy(arp.sender_mac, mac, 6);             ///< sender hardware address
    NET_DEBUG("probe addr: 0x%08x(%s)", probe_addr, inet_ntoa(*((struct in_addr *)&probe_addr)));
    NET_DEBUG("from addr:  0x%08x(%s)", local_addr, inet_ntoa(*((struct in_addr *)&local_addr)));

    memset(&sal, 0, sizeof(sal));
    sal.sll_ifindex = ifr.ifr_ifindex;
    sal.sll_family  = PF_PACKET;
    sendto(sockfd, &arp, sizeof(arp), 0, (struct sockaddr*)(&sal), sizeof(sal));

    while ( tmv.tv_sec > 0 || (tmv.tv_sec == 0 && tmv.tv_usec > 0) )
    {
        FD_ZERO(&fds);
        FD_SET(sockfd, &fds);
        if ( select(sockfd + 1, &fds, NULL, NULL, &tmv) > 0 )
        {
            if ( read(sockfd, &arp, sizeof(arp)) < ARP_MSG_SIZE )
            {
                NET_WARN("read arp failed %d<%s>", errno, strerror(errno));
                continue;
            }
            else
            {
                if ( arp.sender_addr == 0 && arp.target_addr == probe_addr ) /* 所收包的目标IP和当前探测的IP地址一致 */
                {
                    NET_INFO("%02x:%02x:%02x:%02x:%02x:%02x is probing this address(%s)",
                            arp.sender_mac[0], arp.sender_mac[1], arp.sender_mac[2],
                            arp.sender_mac[3], arp.sender_mac[4], arp.sender_mac[5],
                            inet_ntoa(*((struct in_addr *)&probe_addr)));
                    ret = 1;
                    break;
                }
                else if ( arp.sender_addr == probe_addr && memcmp(arp.sender_mac, mac, 6) != 0 ) /* 所收包的源IP和当前探测的IP地址一致 && 源MAC和本地MAC不一致 */
                {
                    NET_INFO("%02x:%02x:%02x:%02x:%02x:%02x is using this address(%s)",
                            arp.sender_mac[0], arp.sender_mac[1], arp.sender_mac[2],
                            arp.sender_mac[3], arp.sender_mac[4], arp.sender_mac[5],
                            inet_ntoa(*((struct in_addr *)&probe_addr)));
                    ret = 1;
                    break;
                }
            }
        }
    }

    return ret;
}

/**
 * @brief       The link-local-address probing for this network interface(ifname).
 * @param[in]   ifname      : The interface name.
 * @param[in]   lladdr      : The link-local-address.
 * @param[in]   mac         : The MAC address.
 * @return      probing result
 * @retval      1           : has address conflicts\n
 *              0           : no conflicts\n
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static int32_t link_local_addr_probing(const char* ifname, const char* lladdr, uint8_t* mac)
{
    PI_SOCKET_T sockfd;
    int32_t     ret;

    RETURN_VAL_IF((sockfd = create_arp_socket(ifname)) == INVALID_SOCKET, NET_WARN, 0);
    NET_DEBUG("%s lladdr(%s)", ifname, lladdr);

    for ( int32_t i = 0; i < ARP_PROBE_NUM; ++i )
    {
        ret = process_arp(ifname, sockfd, inet_addr(lladdr), 0, mac, ARP_PROBE_INTERVAL);
        BREAK_IF(ret == 1, NET_INFO);
    }
    pi_closesock(sockfd);

    return ret;
}

/**
 * @brief       The link-local-address announcing for this network interface(ifname).
 * @param[in]   ifname      : The interface name.
 * @param[in]   lladdr      : The link-local-address.
 * @param[in]   mac         : The MAC address.
 * @return      announcing result
 * @retval      1           : has address conflicts\n
 *              0           : no conflicts\n
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static int32_t link_local_addr_announcing(const char* ifname, const char* lladdr, uint8_t* mac)
{
    PI_SOCKET_T sockfd;
    int32_t     ret;

    RETURN_VAL_IF((sockfd = create_arp_socket(ifname)) == INVALID_SOCKET, NET_WARN, 0);
    NET_DEBUG("%s lladdr(%s)", ifname, lladdr);

    for ( int32_t i = 0; i < ARP_ANNOUNCE_NUM; ++i )
    {
        ret = process_arp(ifname, sockfd, inet_addr(lladdr), inet_addr(lladdr), mac, ARP_ANNOUNCE_INTERVAL);
        NET_DEBUG("announcing[%d] result(%d)", i, ret);
    }
    pi_closesock(sockfd);

    return ret;
}

/**
 * @brief       The link-local-address defend for this network interface(ifname).
 * @param[in]   ifname      : The interface name.
 * @param[in]   lladdr      : The link-local-address.
 * @param[in]   mac         : The MAC address.
 * @return      defend result
 * @retval      != 0        : has address conflicts\n
 * @retval      == 0        : no conflicts\n
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static int32_t link_local_addr_defend(const char* ifname, const char* lladdr, uint8_t* mac)
{
    PI_SOCKET_T     sockfd;
    struct arp_msg  arp;
    struct timeval  tmv = {.tv_sec = 2, .tv_usec = 0};
    int32_t         ret = 0;
    fd_set          fds;

    RETURN_VAL_IF((sockfd = create_arp_socket(ifname)) == INVALID_SOCKET, NET_WARN, 0);

    FD_ZERO(&fds);
    FD_SET(sockfd, &fds);
    if ( select(sockfd + 1, &fds, NULL, NULL, &tmv) > 0 )
    {
        if ( read(sockfd, &arp, sizeof(arp)) >= ARP_MSG_SIZE )
        {
            if ( arp.proto == htons(ETH_P_ARP) && arp.htype == htons(ARPHRD_ETHER) && arp.ptype == htons(ETH_P_IP) && arp.operation == htons(ARPOP_REPLY) )
            {
                /* 检测IP冲突的判据：ARP包的源IP地址和本地一致，但源MAC地址和本地不一致，认为检测到IP冲突 */
                if ( arp.sender_addr == inet_addr(lladdr) && (memcmp(arp.sender_mac, mac, 6) != 0) )
                {
                    NET_DEBUG("'%s' address(%s) maybe conflicts, probing confirm", ifname, lladdr);
                    ret = process_arp(ifname, sockfd, inet_addr(lladdr), 0, mac, ARP_DEFEND_INTERVAL);
                    NET_DEBUG("defend result(%d)", ret);
                }
            }
        }
    }
    pi_closesock(sockfd);

    return ret;
}

/**
 * @brief       The link-local-address handling thread for this network interface index(ifid).
 * @param[in]   arg         : The context of this thread(ifid).
 * @return      NULL
 * <AUTHOR> Xin
 * @date        2023-9-19
 */
static void* link_local_addr_handler(void* arg)
{
    LLA_STEP_E  step = LLA_STEP_ADDR_USE_DHCP;
    IFACE_ID_E  ifid = (IFACE_ID_E)arg;
    const char* ifname = IFACE_NAME(ifid);
    char        lladdr[IPV4_ADDR_LEN];
    char        tmpstr[IPV4_ADDR_LEN];
    int32_t     conflict_num = 0;
    uint8_t     mac[6];

    pi_msleep(24 * 1000); /* 规格中为30秒后生成link local address，此处先延时24秒，冲突检测阶段三次ARP探测如无冲突每次2S，刚好30S */
    RETURN_VAL_IF(net_ifctl_get_mac(ifname, mac, sizeof(mac)) != 0, NET_WARN, NULL);

    while ( 1 )
    {
        memset(tmpstr, 0, sizeof(tmpstr));
        get_ipv4_addr(ifname, tmpstr, sizeof(tmpstr));
        if ( tmpstr[0] == '\0' ) /* DHCP开启且24秒内未分配到DHCP IP地址，生成初始link local IP地址并开始ARP探测 */
        {
            if ( step == LLA_STEP_ADDR_USE_DHCP )
            {
                generate_default_lladdr(lladdr, sizeof(lladdr), mac);
                NET_DEBUG("%s default link local address(%s)", ifname, lladdr);
                step = LLA_STEP_ADDR_PROBING;
            }
        }
        else if ( strncmp(tmpstr, "169.254", 7) != 0 ) /* 已分配到DHCP地址 */
        {
            if ( step != LLA_STEP_ADDR_USE_DHCP )
            {
                NET_DEBUG("%s DHCP address(%s)", ifname, tmpstr);
                step = LLA_STEP_ADDR_USE_DHCP;
            }
            pi_msleep(2000);
            continue;
        }

        switch ( step )
        {
        case LLA_STEP_ADDR_PROBING: /* 本地链路IP探测是否有冲突 */
            {
                NET_DEBUG("%s probing link local address(%s)", ifname, lladdr);
                if ( link_local_addr_probing(ifname, lladdr, mac) == 1 )
                {
                    if ( (++conflict_num) < ARP_MAX_CONFLICTS )
                    {
                        pi_msleep(ARP_PROBE_WAIT * 1000);
                    }
                    else
                    {
                        pi_msleep(ARP_RATE_LIMIT_INTERVAL * 1000);
                    }
                    measure_lladdr(lladdr, sizeof(lladdr));
                    NET_DEBUG("%s measure link local address(%s), probe again.", ifname, lladdr);
                }
                else
                {
                    NET_DEBUG("%s address(%s) no conflict, bind it and announcing.", ifname, lladdr);
                    step = LLA_STEP_ADDR_ANNOUNCING;
                }
                break;
            }
        case LLA_STEP_ADDR_ANNOUNCING: /* 探测无冲突，IP绑定并发布公告 */
            {
                net_ipv4_set_addr(ifid, lladdr, "***********", NULL);
                NET_DEBUG("%s announcing link local address(%s)", ifname, lladdr);
                link_local_addr_announcing(ifname, lladdr, mac);
                step = LLA_STEP_ADDR_DEFENDING;
                break;
            }
        case LLA_STEP_ADDR_DEFENDING: /* 发布公告后，继续监听ARP广播 */
            {
                if ( link_local_addr_defend(ifname, lladdr, mac) == 1 )
                {
                    step = LLA_STEP_ADDR_RESET;
                }
                break;
            }
        case LLA_STEP_ADDR_RESET: /* DEFEND阶段监测到ARP冲突，10S后仍确认冲突，重置当前link local address，重新进入探测阶段 */
            {
                net_ipv4_set_addr(ifid, "", "", "");
                pi_msleep(ARP_PROBE_WAIT * 1000);
                conflict_num = 0;

                measure_lladdr(lladdr, sizeof(lladdr));
                NET_DEBUG("%s measure link local address(%s), back to probing.", ifname, lladdr);
                step = LLA_STEP_ADDR_PROBING;
                break;
            }
        default: break;
        }
    }

    return NULL;
}

void net_ipv4_start_dhcp(IFACE_ID_E ifid)
{
    const char* ifname = IFACE_NAME(ifid);
    char        hostname[HOSTNAME_LEN];
    char        pidfile[128];

    RETURN_IF(STRING_IS_EMPTY(ifname), NET_WARN);

    snprintf(pidfile, sizeof(pidfile), DHCP_PID_FILE_FORMAT, ifname);
    RETURN_IF(check_program_exist(pidfile), NET_WARN);

#if CONFIG_NET_WIFI
    if ( ifid == IFACE_ID_ETH || ifid == IFACE_ID_STA )
#else
    if ( ifid == IFACE_ID_ETH )
#endif
    {
        if ( s_lla_thread[ifid] == INVALIDTHREAD )
        {
            s_lla_thread[ifid] = pi_thread_create(link_local_addr_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, (void *)ifid, "link_local_addr_handler");
        }
        gethostname(hostname, sizeof(hostname));
        pi_runcmd(NULL, 0, 1, "udhcpc -a -t 10 -i %s -s " DHCP_IPV4_SCRIPT_FILE " -p %s -F '%s' -x hostname:'%s' > /dev/null 2>&1", ifname, pidfile, hostname, hostname);
        waiting_program_start(pidfile, 10, 200);
    }
#if CONFIG_NET_WIFI
    else if ( ifid == IFACE_ID_WFD )
    {
        /* create dhcpd.lease file. */
        FILE* stream = fopen(NET_BASE_DIR"/dhcpd.lease", "w+");
        RETURN_IF(stream == NULL, NET_WARN);
        fclose(stream);
        pi_runcmd(NULL, 0, 1, "dhcpd -d -lf "NET_BASE_DIR"/dhcpd.lease -pf %s %s > /dev/null 2>&1", pidfile, ifname);
        waiting_program_start(pidfile, 10, 200);
    }
#endif
    else
    {
        NET_WARN("'%s' unsupport DHCP", ifname);
    }
}

void net_ipv4_stop_dhcp(IFACE_ID_E ifid)
{
    const char* ifname = IFACE_NAME(ifid);
    char        pidfile[128];

    RETURN_IF(STRING_IS_EMPTY(ifname), NET_WARN);

    snprintf(pidfile, sizeof(pidfile), DHCP_PID_FILE_FORMAT, ifname);
    waiting_program_stop(pidfile, 5, 500);

    if ( s_lla_thread[ifid] != INVALIDTHREAD )
    {
        NET_DEBUG("destroy link_local_addr_handler(%s)", ifname);
        pi_thread_destroy(s_lla_thread[ifid]);
        s_lla_thread[ifid] = INVALIDTHREAD;
    }
}

int32_t net_ipv4_check_conflict(IFACE_ID_E ifid, const char* probe_addr)
{
    PI_SOCKET_T sockfd = INVALID_SOCKET;
    const char* ifname = IFACE_NAME(ifid);
    char        from_addr[IPV4_ADDR_LEN];
    int32_t     ret = 0;
    uint8_t     mac[6];

    RETURN_VAL_IF((STRING_IS_EMPTY(ifname)) || (probe_addr == NULL), NET_WARN, 0);
    RETURN_VAL_IF(net_ifctl_get_mac(ifname, mac, sizeof(mac)) != 0, NET_WARN, 0);

    memset(from_addr, 0, sizeof(from_addr));
    get_ipv4_addr(ifname, from_addr, sizeof(from_addr));

    sockfd = create_arp_socket(ifname);
    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, 0);

    ret = process_arp(ifname, sockfd, inet_addr(probe_addr), inet_addr(from_addr), mac, 1);
    pi_closesock(sockfd);

    return ret;
}

int32_t net_ipv4_set_addr(IFACE_ID_E ifid, const char* ipstr, const char* mask, const char* gateway)
{
    PI_SOCKET_T         sockfd;
    struct sockaddr_in* sin;
    struct rtentry      rt;
    struct ifreq        ifr;
    const char*         ifname = IFACE_NAME(ifid);
    int32_t             ret = -1;

    NET_DEBUG("setting %s address, ip(%s) mask(%s) gateway(%s)", ifname, ipstr, mask, gateway);
    RETURN_VAL_IF(STRING_IS_EMPTY(ifname), NET_WARN, -1);

    sockfd = netsock_create_custom(AF_INET, SOCK_DGRAM, IPPROTO_IP);
    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, -1);

    do
    {
        memset(&ifr, 0, sizeof(ifr));
        snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
        sin = (struct sockaddr_in *)&ifr.ifr_addr;

        /* setting IP address */
        sin->sin_family = AF_INET;
        if ( STRING_IS_EMPTY(ipstr) || strcmp(ipstr, "0.0.0.0") == 0 )
        {
            sin->sin_addr.s_addr = INADDR_ANY; /* if ipstr is empty, clear the current address. */
        }
        else if ( inet_aton(ipstr, &sin->sin_addr) == 0 )
        {
            NET_WARN("inet_aton %s IP address(%s) failed: %d<%s>", ifname, ipstr, errno, strerror(errno));
            break;
        }

        if ( ioctl(sockfd, SIOCSIFADDR, &ifr) != 0 )
        {
            NET_WARN("ioctl %s SIOCSIFADDR 0x%08X failed: %d<%s>", ifname, sin->sin_addr.s_addr, errno, strerror(errno));
            break;
        }

        if ( STRING_NO_EMPTY(mask) && strcmp(mask, "0.0.0.0") != 0 )
        {
            sin->sin_family = AF_INET;
            if ( inet_aton(mask, &sin->sin_addr) == 0 )
            {
                NET_WARN("inet_aton %s mask address(%s) failed: %d<%s>", ifname, mask, errno, strerror(errno));
                break;
            }

            if ( ioctl(sockfd, SIOCSIFNETMASK, &ifr) != 0 )
            {
                NET_WARN("ioctl %s SIOCSIFNETMASK 0x%08X failed: %d<%s>", ifname, sin->sin_addr.s_addr, errno, strerror(errno));
                break;
            }
        }

        if ( STRING_NO_EMPTY(gateway) && strcmp(gateway, "0.0.0.0") != 0 )
        {
            memset(&rt, 0, sizeof(struct rtentry));
            rt.rt_flags = (RTF_UP | RTF_GATEWAY);
            rt.rt_dev = (char *)ifname;

            sin = (struct sockaddr_in *)&rt.rt_gateway;
            sin->sin_family = AF_INET;
            if ( inet_aton(gateway, &sin->sin_addr) == 0 )
            {
                NET_WARN("inet_aton %s gateway address(%s) failed: %d<%s>", ifname, gateway, errno, strerror(errno));
                break;
            }

            sin = (struct sockaddr_in *)&rt.rt_dst;
            sin->sin_addr.s_addr = INADDR_ANY;
            sin->sin_family = AF_INET;

            sin = (struct sockaddr_in *)&rt.rt_genmask;
            sin->sin_addr.s_addr = INADDR_ANY;
            sin->sin_family = AF_INET;

            if ( ioctl(sockfd, SIOCADDRT, &rt) != 0 )
            {
                NET_WARN("ioctl %s SIOCADDRT failed: %d<%s>", ifname, errno, strerror(errno));
                break;
            }
        }

        ret = 0;
    }
    while ( 0 );

    pi_closesock(sockfd);
    return ret;
}

void net_ipv4_update_autodns(IFACE_ID_E ifid)
{
    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);

    pi_runcmd(NULL, 0, 0, "udhcpc -n -q -R -i %s -s " DHCP_DNS_SCRIPT_FILE, IFACE_NAME(ifid));
}

/* 将DNS地址信息写入resolv.conf */
int32_t net_ipv4_set_dns(IFACE_ID_E ifid, char (*dns)[IPV4_ADDR_LEN])
{
    char    suffix[32];
    char    line[128];
    char    buf[1024];
    char*   ptr = buf;
    FILE*   stream;

    RETURN_VAL_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN, -1);

    snprintf(suffix, sizeof(suffix), "# %s IPv4", IFACE_NAME(ifid));
    memset(buf, 0, sizeof(buf));

    stream = fopen(RESOLV_CONF_FILE, "r");
    if ( stream != NULL )
    {
        while ( fgets(line, sizeof(line), stream) )
        {
            if ( line[0] != '\n' && strstr(line, suffix) == NULL )  /* 按后缀"# ifname IPv4"过滤，剩余部分存入buf */
            {
                ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "%s", line);
            }
        }
        fclose(stream);
    }

    if ( dns != NULL )
    {
        if ( STRING_NO_EMPTY(dns[0]) )
        {
            ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "nameserver %s %s\n", dns[0], suffix);
        }
        if ( STRING_NO_EMPTY(dns[1]) )
        {
            ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "nameserver %s %s\n", dns[1], suffix);
        }
    }

    /* 清空当前文件内容，写入DNS buf */
    stream = fopen(RESOLV_CONF_FILE, "w+");
    if ( stream != NULL )
    {
        fwrite(buf, 1, strlen(buf), stream);
        fclose(stream);
    }

    return 0;
}

/* 从resolv.conf读取DNS地址 */
int32_t net_ipv4_get_dns(IFACE_ID_E ifid, char (*dns)[IPV4_ADDR_LEN])
{
    FILE*   stream;
    char    suffix[32];
    char    addr[IPV4_ADDR_LEN];
    char    line[128];
    int32_t num = 0;
    int32_t ret;

    RETURN_VAL_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN, -1);

    snprintf(suffix, sizeof(suffix), "# %s IPv4", IFACE_NAME(ifid));

    stream = fopen(RESOLV_CONF_FILE, "r");
    if ( stream != NULL )
    {
        while ( num < 2 && fgets(line, sizeof(line), stream) )
        {
            if ( strstr(line, suffix) != NULL )
            {
                memset(addr, 0, sizeof(addr));
                ret = sscanf(line, "nameserver %15[0-9.]", addr);
                if ( ret == 1 )
                {
                    snprintf(dns[num], IPV4_ADDR_LEN, "%s", addr);
                    NET_DEBUG("DNSv4[%d]: %s", num, dns[num]);
                    num++;
                }
            }
        }
        fclose(stream);
    }

    return 0;
}
/**
 *@}
 */
