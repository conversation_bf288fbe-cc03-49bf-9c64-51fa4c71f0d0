/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netoid.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-9-18
 * @brief network register ACL-OID command
 */
#include "nettypes.h"
#include "netctx.h"

#include "acl/acl_attribute.h" /* from common/acl module */

static NET_CTX_S* s_oid_net_ctx = NULL;

/**
 * @brief       Parse OID_WIFI_INTF_SIGNAL_STRENGTH to get signal strength of WiFi-Station.
 * @param[out]  val             : get signal strength and set to val.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR>
 * @date        2023-9-18
 */
static int32_t netoid_get_wifi_sig_strength(uint32_t* val)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);

    *val = netdata_get_sta_rssi(s_oid_net_ctx->data_mgr);
    NET_DEBUG("val(%u)", *val);

    return PARSER_SUCCESS;
}

/**
 * @brief       Parse OID_WIFI_WFD_SUPPORTED to get WiFi-Direct mode.
 * @param[out]  val             : get WiFi-Direct mode and set to val.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_get_wifi_wfd_support(uint32_t* val)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);

#if CONFIG_NET_WIFI
    *val = netdata_get_iface_switch(s_oid_net_ctx->data_mgr, IFACE_ID_WFD);
    NET_DEBUG("val(%u)", *val);
#endif

    return PARSER_SUCCESS;
}

/**
 * @brief       Parse OID_WIFI_INTF_ENABLED to get WiFi-Direct mode.
 * @param[in]   idx             : the index of this oid. only support OID_WIFI_INTF_ENABLED.1
 * @param[out]  val             : get WiFi-Direct mode and set to val.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_get_wifi_enabled(uint32_t idx, uint32_t* val)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(idx != 1, NET_WARN, PARSER_ERROR);

    *val = netdata_get_iface_switch(s_oid_net_ctx->data_mgr, idx);
    NET_DEBUG("val[%u](%u)", idx, *val);

    return PARSER_SUCCESS;
}

/**
 * @brief       Parse OID_WIFI_INTF_SSID to get WiFi-Direct mode.
 * @param[in]   idx             : the index of this oid. only support OID_WIFI_INTF_SSID.0
 * @param[out]  val             : get WiFi-Station ssid and set to val.
 * @param[in]   len             : the value max length.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_get_wifi_ssid(uint32_t idx, char* val, uint32_t len)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(len == 0, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(idx != 0, NET_WARN, PARSER_ERROR);

    netdata_get_sta_ssid(s_oid_net_ctx->data_mgr, val, len);

    return PARSER_SUCCESS;
}

/**
 * @brief       Parse OID_NETWORK_IPV4_ADDRESS to get IPv4 address.
 * @param[in]   idx             : the index of this oid.
 * @param[out]  val             : get IPv4 address and set to val.
 * @param[in]   len             : the value max length.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_get_ipv4_addr(uint32_t idx, char* val, uint32_t len)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(len == 0, NET_WARN, PARSER_ERROR);

    netdata_get_ipv4_addr(s_oid_net_ctx->data_mgr, idx, val, len);
    NET_DEBUG("val[%u](%s)", idx, val);

    return PARSER_SUCCESS;
}

/**
 * @brief       Parse OID_NETWORK_MAC_ADDRESS to set MAC address.
 * @param[in]   idx             : the index of this oid, only support OID_NETWORK_MAC_ADDRESS.0
 * @param[out]  val             : set MAC address is val.
 * @param[in]   len             : the val length.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_set_mac_addr(uint32_t idx, char* val, uint32_t len)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(STRING_IS_EMPTY(val), NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(idx != IFACE_ID_ETH, NET_WARN, PARSER_ERROR);

    NET_DEBUG("val[%u](%s)", idx, val);
    return ((netctx_update_mac_addr(s_oid_net_ctx, idx, val) == 0) ? PARSER_SUCCESS : PARSER_ERROR);
}

/**
 * @brief       Parse OID_NETWORK_MAC_ADDRESS to get MAC address.
 * @param[in]   idx             : the index of this oid
 * @param[out]  val             : get MAC address and set to val.
 * @param[in]   len             : the value max length.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_get_mac_addr(uint32_t idx, char* val, uint32_t len)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(len == 0, NET_WARN, PARSER_ERROR);

    netdata_get_mac_addr(s_oid_net_ctx->data_mgr, idx, val, len);
    NET_DEBUG("val[%u](%s)", idx, val);

    return PARSER_SUCCESS;
}

/**
 * @brief       Parse OID_NETWORK_HOST_NAME to set MAC address.
 * @param[out]  val             : set hostname is val.
 * @param[in]   len             : the val length.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_set_hostname(char* val, uint32_t len)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(STRING_IS_EMPTY(val), NET_WARN, PARSER_ERROR);

    NET_DEBUG("val(%s)", val);
    return ((netctx_update_hostname(s_oid_net_ctx, val) == 0) ? PARSER_SUCCESS : PARSER_ERROR);
}

/**
 * @brief       Parse OID_NETWORK_MAC_ADDRESS to get hostname.
 * @param[out]  val             : get hostname and set to val.
 * @param[in]   len             : the value max length.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netoid_get_hostname(char* val, uint32_t len)
{
    RETURN_VAL_IF(s_oid_net_ctx == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(val == NULL, NET_WARN, PARSER_ERROR);
    RETURN_VAL_IF(len == 0, NET_WARN, PARSER_ERROR);

    netdata_get_hostname(s_oid_net_ctx->data_mgr, val, len);
    NET_DEBUG("val(%s)", val);

    return PARSER_SUCCESS;
}

void netoid_init(NET_CTX_S* net_ctx)
{
    RETURN_IF(s_oid_net_ctx != NULL || net_ctx == NULL, NET_DEBUG);

    s_oid_net_ctx = net_ctx;

    acl_attribute_uint32_value_register("OID_WIFI_INTF_SIGNAL_STRENGTH",    NULL,                   netoid_get_wifi_sig_strength);
    acl_attribute_uint32_value_register("OID_WIFI_WFD_SUPPORTED",           NULL,                   netoid_get_wifi_wfd_support);
    acl_attribute_uint32_index_register("OID_WIFI_INTF_ENABLED",            NULL,                   netoid_get_wifi_enabled);
    acl_attribute_string_index_register("OID_WIFI_INTF_SSID",               NULL,                   netoid_get_wifi_ssid);
    acl_attribute_string_index_register("OID_NETWORK_IPV4_ADDRESS",         NULL,                   netoid_get_ipv4_addr);
    acl_attribute_string_index_register("OID_NETWORK_MAC_ADDRESS",          netoid_set_mac_addr,    netoid_get_mac_addr);
    acl_attribute_string_value_register("OID_NETWORK_HOST_NAME",            netoid_set_hostname,    netoid_get_hostname);
}
/**
 *@}
 */
