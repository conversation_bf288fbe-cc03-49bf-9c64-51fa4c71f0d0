#ifndef PEDK_ADDRESS_BOOK_H
#define PEDK_ADDRESS_BOOK_H

#include <stdbool.h>

//MAIL
#define PEDK_MAIL_NAME_LEN          16
#define PEDK_MAIL_ADDR_LEN          64
#define PEDK_MAIL_ADDR_NUM_MAX      60

//MAIL_GROUP
#define PEDK_MAIL_GROUP_NAME_LEN    16
#define PEDK_MAIL_GROUP_MAX         10

//FTP
#define PEDK_FTP_USER_NAME_LEN      32
#define PEDK_FTP_SERVER_ADDR_LEN    32
#define PEDK_FTP_SERVER_PATH_LEN    64
#define PEDK_FTP_LOGIN_NAME_LEN     64
#define PEDK_FTP_PASSWORD_LEN       32
#define PEDK_FTP_PARM_MAX           60

//SMB
#define PEDK_SMB_USER_NAME_LEN      33
#define PEDK_SMB_SERVER_ADDR_LEN    33
#define PEDK_SMB_SERVER_PATH_LEN    129
#define PEDK_SMB_LOGIN_NAME_LEN     129
#define PEDK_SMB_PASSWORD_LEN       33
#define PEDK_SMB_PARM_MAX           60

typedef struct
{
    int32_t         pedk_mail_index;
    char            pedk_mail_name[PEDK_MAIL_NAME_LEN];
    char            pedk_mail_addr[PEDK_MAIL_ADDR_LEN];
    int32_t         pedk_group_num[PEDK_MAIL_GROUP_MAX];
}
PEDK_MAIL_PARM;

typedef struct
{
    int32_t      pedk_group_index;                                           //群组号索引
    char         pedk_group_name[PEDK_MAIL_GROUP_NAME_LEN];                  //群组名
    int32_t      pedk_mail_index[PEDK_MAIL_ADDR_NUM_MAX];                    //群组成员索引
}
PEDK_MAIL_GROUP_MGR;

typedef struct
{
    int32_t     pedk_ftp_index;                                        //索引号
    char        pedk_ftp_name[PEDK_FTP_USER_NAME_LEN];                 //用户别名scan
    char        pedk_ftp_addr[PEDK_FTP_SERVER_ADDR_LEN];               //服务器IP
    char        pedk_ftp_subdirectory[PEDK_FTP_SERVER_PATH_LEN];        //ftp服务器路径
    int32_t     pedk_ftp_port;                                         //端口号
    bool        pedk_ftp_anonymous;                                    //是否匿名
    char        pedk_ftp_login_name[PEDK_FTP_LOGIN_NAME_LEN];          //登录名
    char        pedk_ftp_login_pwd[PEDK_FTP_PASSWORD_LEN];             //密码

}
PEDK_FTP_PARM;

typedef struct
{
    int32_t     pedk_smb_index;                                        //索引号
    char        pedk_smb_name[PEDK_SMB_USER_NAME_LEN];                 //用户别名scan
    char        pedk_smb_addr[PEDK_SMB_SERVER_ADDR_LEN];               //服务器IP
    char        pedk_smb_subdirectory[PEDK_SMB_SERVER_PATH_LEN];       //子目录
    int32_t     pedk_smb_port;                                         //端口号
    bool        pedk_smb_anonymous;                                    //是否匿名
    char        pedk_smb_login_name[PEDK_SMB_LOGIN_NAME_LEN];          //登录名
    char        pedk_smb_login_pwd[PEDK_SMB_PASSWORD_LEN];             //密码
}
PEDK_SMB_PARM;

typedef struct
{
    PEDK_MAIL_PARM mail_parm[PEDK_MAIL_ADDR_NUM_MAX];
    int32_t total;
    int32_t id;
}
ADDRESSBOOK_MAIL;

typedef struct
{
    PEDK_MAIL_GROUP_MGR group_parm[PEDK_MAIL_GROUP_MAX];
    int32_t total;
    int32_t id;
}
ADDRESSBOOK_GROUP;

typedef struct
{
    PEDK_FTP_PARM ftp_parm[PEDK_FTP_PARM_MAX];
    int32_t total;
    int32_t id;
}
ADDRESSBOOK_FTP;

typedef struct
{
    PEDK_SMB_PARM smb_parm[PEDK_SMB_PARM_MAX];
    int32_t total;
    int32_t id;
}
ADDRESSBOOK_SMB;

int32_t add_email_addr(PEDK_MAIL_PARM* mail_info);
int32_t get_email_addr(int32_t mail_index, PEDK_MAIL_PARM* mail_info, size_t* mail_size);
int32_t get_email_addr_num(void);
int32_t is_email_addr_full(void);
int32_t modify_email_addr(PEDK_MAIL_PARM* mail_info);
int32_t remove_email_addr(int32_t mail_index);
int32_t get_email_addr_list(PEDK_MAIL_PARM* mail_info_list, size_t arry_size, size_t* mail_size);
int32_t creat_email_group(PEDK_MAIL_GROUP_MGR* group_info);
int32_t get_email_group(int32_t group_index, PEDK_MAIL_GROUP_MGR* group_info, size_t* group_size);
int32_t get_email_group_num(void);
int32_t is_email_group_full(void);
int32_t add_email_to_group(int32_t group_id, int32_t mail_id);
int32_t remove_email_from_group(int32_t group_id, int32_t mail_id);
int32_t get_email_group_list(PEDK_MAIL_GROUP_MGR* group_info_list, size_t arry_size, size_t* group_size);
int32_t modify_email_group(int32_t group_id,PEDK_MAIL_GROUP_MGR* group_info);
int32_t remove_email_group(int32_t group_index);
int32_t add_ftp_addr(PEDK_FTP_PARM* ftp_info);
int32_t get_ftp_addr(int32_t ftp_index, PEDK_FTP_PARM* ftp_info, size_t* ftp_size);
int32_t get_ftp_addr_list(PEDK_FTP_PARM* ftp_info_list, size_t arry_size, size_t* ftp_size);
int32_t get_ftp_addr_num(void);
int32_t is_ftp_addr_full(void);
int32_t remove_ftp_addr(int32_t ftp_index);
int32_t modify_ftp_addr(PEDK_FTP_PARM* ftp_info);
int32_t add_smb_addr(PEDK_SMB_PARM* smb_info);
int32_t get_smb_addr(int32_t smb_index, PEDK_SMB_PARM* smb_info, size_t* smb_size);
int32_t get_smb_addr_list(PEDK_SMB_PARM* smb_info_list, size_t arry_size, size_t* smb_size);
int32_t get_smb_addr_num(void);
int32_t is_smb_addr_full(void);
int32_t remove_smb_addr(int32_t smb_index);
int32_t modify_smb_addr(PEDK_SMB_PARM* smb_info);



#endif
