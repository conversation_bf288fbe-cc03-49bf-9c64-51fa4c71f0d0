#ifndef _PEDK_UI_
#define _PEDK_UI_

#include <quickjs.h>

#define     PEDK_APP_ID_LENGTH                      256
#define     PEDK_APP_PATH_LENGTH                    256

#define     PEDK_WIDGET_ID_LENGTH                   64
#define     PEDK_WIDGET_TYPE_LENGTH                 64
#define     PEDK_WIDGET_IMAGE_PATH_LENGTH           256
#define     PEDK_WIDGET_IMAGE_FORMAT_LENGTH         32
#define     PEDK_WIDGET_TEXT_LENGTH                 256
#define     PEDK_WIDGET_TOUCH_RELEASE               0
#define     PEDK_WIDGET_TOUCH_PRESS                 1

#define     PEDK_WIDGET_STYLE_COLOR_LENGTH          32
#define     PEDK_WIDGET_STYLE_TEXT_ALIGN_LENGTH     32
#define     PEDK_WIDGET_STYLE_FONT_SIZE_LENGTH      32
#define     PEDK_WIDGET_STYLE_TEXT_DIRECTION_LENGTH 32

#define     PEDK_NATIVE_WINDOW_LENGTH               32
#define     PEDK_WIDGET_DISPLAY_MODE_LENGTH         32

typedef struct {
    char text_color[PEDK_WIDGET_STYLE_COLOR_LENGTH];
    char text_align[PEDK_WIDGET_STYLE_TEXT_ALIGN_LENGTH];
    char text_direction[PEDK_WIDGET_STYLE_TEXT_DIRECTION_LENGTH];
    char font_size[PEDK_WIDGET_STYLE_FONT_SIZE_LENGTH];
}JSStyleSheet;
typedef struct
{
    char img_res[PEDK_WIDGET_IMAGE_PATH_LENGTH];           //图像资源，字符串类型，比如相对路径“/resource/img/bg.bmp”。
    char img_format[PEDK_WIDGET_IMAGE_FORMAT_LENGTH];        //图像格式，包括“bmp”、“jpg”、“png”、“gif”（暂不支持）
}JSImageData;

typedef struct {
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int  x;
    int  y;
    int  w;
    int  h;
    JSStyleSheet style_sheet;
} JSScreen;

typedef struct {
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int  x;
    int  y;
    int  w;
    int  h;
    JSStyleSheet style_sheet;
} JSPanel;

typedef struct {
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
} JSKEYBOARD;


/*按钮文本显示结构*/
typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    int is_valid;                  //是否有效
    char text[PEDK_WIDGET_TEXT_LENGTH];
    int is_checked;                //按下状态
    JSImageData imgs[3];  //图像数组。[0] : 压下状态图片 [1] : 抬起状态图片 [2] : 无效状态图片
    //void* cb_pressed;    //短压压下按钮
    //void* cb_released;   //短压抬起按钮
}JSButton;

/*按钮文本显示结构*/
typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    int is_valid;                  //是否有效
    char text[PEDK_WIDGET_TEXT_LENGTH];
    int is_checked;                //按下状态
    JSImageData imgs[4];  //图像数组。[0] : 压下状态图片 [1] : 抬起状态图片 [2] : 无效状态图片
    //void* cb_pressed;    //短压压下按钮
    //void* cb_released;   //短压抬起按钮
}JSCheckButton;

/*按钮文本显示结构*/
typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    void * list_item_group;
    int * select_value;
}JSListButton;

/*按钮文本显示结构*/
typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    int is_valid;                  //是否有效
    char text[PEDK_WIDGET_TEXT_LENGTH];
    int is_checked;                //按下状态
    JSImageData imgs[4];  //图像数组。[0] : 压下状态图片 [1] : 抬起状态图片 [2] : 无效状态图片
    //void* cb_pressed;    //短压压下按钮
    //void* cb_released;   //短压抬起按钮
}JSRadioButton;

/*按钮文本显示结构*/
typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    int is_valid;                  //是否有效
    int select_value;
    JSRadioButton *buttons;
}JSRadioGroup;

typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    char text[PEDK_WIDGET_TEXT_LENGTH];
}JSLabel;

typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    JSImageData img;
}JSImage;

/*按钮文本显示结构*/
typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    int is_valid;                  //是否有效
    char text[PEDK_WIDGET_TEXT_LENGTH];
    char display_mode[PEDK_WIDGET_DISPLAY_MODE_LENGTH];
    
    //void* cb_editing_finished;    //编辑结束
}JSLineEdit;

typedef struct{
    char id[PEDK_WIDGET_ID_LENGTH];
    char type[PEDK_WIDGET_TYPE_LENGTH];
    int x;
    int y;
    int w;
    int h;
    JSStyleSheet style_sheet;
    JSImageData* imgs;
    int freq;
}JSAnimalImage;
/*
    声明 QuickJS C 函数由于初始化回调
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv);
//int js_ui_init(JSContext *ctx, JSValueConst global);
JSValue js_screen_draw(JSContext *ctx, JSValueConst this_val,
							  int argc, JSValueConst *argv);
JSValue js_button_draw(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv);
JSValue js_label_draw(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv);

/*
    声明 QuickJS C 函数由于初始化回调
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv);
int js_ui_init(JSContext *ctx, JSValueConst global);
#endif /* _PEDK_UI_ */

