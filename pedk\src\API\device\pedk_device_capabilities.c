#include "pedk_device_capabilities.h"
#include "PEDK_event.h"
#include <quickjs.h>
#include <string.h>

#define CAPA_LOG(fmt, ...)    printf("[Capability RE]:%s:%d " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#define countof(x) (sizeof(x) / sizeof((x)[0]))

/*tray1|tray2|tray3|tray4|external_tray|install_tray|multi_tray*/
JSPaperSizeWithTray   paper_size_with_tray[] =
{
   { "MEDIA_SIZE_A6",                { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_JIS_B6",            { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_STATEMENT",         { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_A5",                { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_JIS_B5",            { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_EXECUTIVE",         { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_BIG_16K",           { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_A5_LEF",            { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_A4",                { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_STATEMENT_LEF",     { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_LETTER",            { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_FOLIO",             { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_LEGAL",             { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_JIS_B5_LEF",        { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_JIS_B4",            { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_EXECUTIVE_LEF",     { 0,0,0,0,0,0,1 } },
   { "MEDIA_SIZE_BIG_16K_LEF",       { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_8K",                { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_LETTER_LEF",        { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_LEDGER",            { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_A4_LEF",            { 1,1,1,1,1,1,1 } },
   { "MEDIA_SIZE_A3",                { 1,1,1,1,0,0,1 } },
   { "MEDIA_SIZE_A3_WIDE",           { 0,1,0,0,0,0,1 } },
   { "MEDIA_SIZE_SRA3",              { 0,1,0,0,0,0,1 } },
};

/*tray1|tray2|tray3|tray4|external_tray|install_tray|multi_tray*/
JSPaperTypeWithTray paper_type_with_tray[] =
{
    { "MEDIA_TYPE_PLAIN",             { 1,1,1,1,1,1,1 } },
    { "MEDIA_TYPE_PLAIN_PLUS",        { 1,1,1,1,1,1,1 } },
    { "MEDIA_TYPE_THICK_PAPER1",      { 1,1,1,1,1,1,1 } },
    { "MEDIA_TYPE_THICK_PAPER1_PLUS", { 1,1,1,1,1,1,1 } },
    { "MEDIA_TYPE_THICK_PAPER2",      { 1,1,1,1,1,1,1 } },
    { "MEDIA_TYPE_THICK_PAPER3",      { 1,1,1,1,1,1,1 } },
    { "MEDIA_TYPE_TRANSPARENCY",      { 0,0,0,0,0,0,1 } },
    { "MEDIA_TYPE_POSTCARD",          { 0,0,0,0,0,0,1 } },
    { "MEDIA_TYPE_ENVELOPE",          { 0,0,0,0,0,0,1 } },
    { "MEDIA_TYPE_LABEL",             { 0,0,0,0,0,0,1 } },
};

TRAY_IN_E js_print_ctl_pedk_tray_in(char *tray_in)
{
    TRAY_IN_E                    tray_input                          = TRAY_IN_INVALID;

    if( NULL == tray_in )
    {
        printf("tray_in is null\n");
        return tray_input;
    }

    if( 0 == strcmp(tray_in,"MULTI_FUNCTION_TRAY") )
    {
        tray_input = MULTI_FUNCTION_TRAY;
    }
    else if( 0 == strcmp(tray_in,"OPTION_TRAY1") )
    {
        tray_input = OPTION_TRAY1;
    }
    else if( 0 == strcmp(tray_in,"OPTION_TRAY2") )
    {
        tray_input = OPTION_TRAY2;
    }
    else if( 0 == strcmp(tray_in,"OPTION_TRAY3") )
    {
        tray_input = OPTION_TRAY3;
    }
    else if( 0 == strcmp(tray_in,"OPTION_TRAY4") )
    {
        tray_input = OPTION_TRAY4;
    }
    else if( 0 == strcmp(tray_in,"EXTERNAL_HIGH_CAPACITY_TRAY") )
    {
        tray_input = EXTERNAL_HIGH_CAPACITY_TRAY;
    }
    else if( 0 == strcmp(tray_in,"INSTALL_HIGH_CAPACITY_TRAY") )
    {
        tray_input = INSTALL_HIGH_CAPACITY_TRAY;
    }
    else
    {
        printf("unsupport tray in %s \n",tray_in);
        tray_input = TRAY_IN_INVALID;
    }
    printf("tray_in = %s,tray_input = %d\n",tray_in,tray_input);

    return tray_input;
}



JSValue js_get_capability_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{

    int32_t ret;
    int32_t tmp;
    int32_t list_len;
    char* json_str = NULL;
    JSValue obj = JS_UNDEFINED;


    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LEN, 0, 0, NULL);            //获取能力列表长度
    ret = RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LEN, &list_len, NULL, &tmp, 5);
    CAPA_LOG("ret %d, len %d\n", ret, list_len);

    if( ret == 0 && list_len > 0 )
    {
        json_str = (char*)malloc(1 + list_len);
        if ( NULL == json_str )
        {
            return obj;
        }
        SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LIST, 0, 0, NULL);//获取能力列表

        ret = RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LIST, &tmp, json_str, &list_len, 5);
        if( ret != 0 )
        {
            CAPA_LOG("recv list error\n");
        }
        else
        {
            json_str[list_len] = '\0';
            CAPA_LOG("return capability list = [%s]\n", json_str);
            obj = JS_NewString(ctx, json_str);
        }
        free(json_str);
        json_str = NULL;
    }
    return obj;
}


JSValue js_get_capability_tray_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    JSValue obj = JS_NewObject(ctx);
    JSValue array = JS_NewArray(ctx);

    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "OPTION_TRAY1"));
    JS_SetPropertyUint32(ctx, array, 0, obj);
    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "OPTION_TRAY2"));
    JS_SetPropertyUint32(ctx, array, 1, obj);
    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "OPTION_TRAY3"));
    JS_SetPropertyUint32(ctx, array, 2, obj);
    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "OPTION_TRAY4"));
    JS_SetPropertyUint32(ctx, array, 3, obj);
    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "EXTERNAL_HIGH_CAPACITY_TRAY"));
    JS_SetPropertyUint32(ctx, array, 4, obj);
    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "INSTALL_HIGH_CAPACITY_TRAY"));
    JS_SetPropertyUint32(ctx, array, 5, obj);
    JS_SetPropertyStr(ctx, obj, "tray", JS_NewString(ctx, "MULTI_FUNCTION_TRAY"));
    JS_SetPropertyUint32(ctx, array, 6, obj);

    return array;
}

JSValue js_get_capability_tray_paper_size_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    char                            list[1024]             = {0};
    char                            buf[32] = {0};
    int                             tray_num                            = 0;
    int                             array_num                           = 23;
    int ret = 0;
    int len = 0;
    int respone = -1;
    int i = 0;

    char * tary_in;

    tary_in = JS_ToCString(ctx, argv[0]);
    if(NULL == tary_in)
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,tary_in = %s\n",__LINE__,__func__,tary_in);

    tray_num = js_print_ctl_pedk_tray_in( tary_in );
    if ( TRAY_IN_INVALID == tray_num )
    {
        printf("[line:%d]  error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    memset((char *)list,0,1024);
    for(i = 0; i<array_num; i++)
    {
        if( 1 == paper_size_with_tray[i].tray_support[tray_num])
        {
            memset(buf, 0, 32);
            sprintf(buf, ", %s",paper_size_with_tray[i].paper_size);
            strcat((char *)list,buf );
        }
    }

    printf("[line:%d] %s,%s\n",__LINE__,__func__,list);

    return JS_NewString(ctx, list);
}


JSValue js_get_capability_tray_media_type_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    char                            list[1024]                          = {0};
    char                            buf[32] = {0};
    int                             tray_num                            = 0;
    int                             array_num                           = 10;
    int ret = 0;
    int len = 0;
    int respone = -1;
    int i = 0;
    char * tary_in;

    tary_in = JS_ToCString(ctx, argv[0]);
    if( NULL == tary_in )
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,tary_in = %s\n",__LINE__,__func__,tary_in);

    tray_num = js_print_ctl_pedk_tray_in( tary_in );
    if( TRAY_IN_INVALID == tray_num )
    {
        printf("[line:%d]  error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    memset((char *)list,0,1024);
    for(i = 0; i<array_num; i++)
    {
        if( 1 == paper_type_with_tray[i].tray_support[tray_num])
        {
            memset(buf, 0, 32);
            sprintf(buf, ", %s",paper_type_with_tray[i].paper_type );
            strcat((char *)list,buf);
        }
    }

    printf("[line:%d] %s,%s\n",__LINE__,__func__,list);

    return JS_NewString(ctx, list);
}


typedef struct JSCFunctionList
{
    const char  *name;
    int32_t     length;
    JSCFunction *func;
}JSCFunctionList;

static const JSCFunctionList pedk_capability_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */
    {"js_getSystemCapabilitiesList",      0,      js_get_capability_list},
};

const JSCFunctionList* get_capability_JSCFunctionList(int32_t *length)
{
    *length = countof(pedk_capability_funcs);
    return pedk_capability_funcs;
}

int32_t js_capability_init(JSContext *ctx, JSValueConst global)
{
    /* creat the classes */
    int32_t i;
    int32_t count = 0;
    const JSCFunctionList* pedk_funcs = get_capability_JSCFunctionList(&count);
    CAPA_LOG("count:%d\n",count);
    for ( i = 0; i < count; i++ )
    {
        JS_SetPropertyStr(ctx, global, pedk_funcs[i].name,
                            JS_NewCFunction(ctx, pedk_funcs[i].func, pedk_funcs[i].name, pedk_funcs[i].length));

    }
    return 0;
}

