/**
 * @copyright 2024 Shenzhen Pantum Technology Co.Ltd all rights reserved
 * @file print_parser_config.c
 * @addtogroup print_parser_module
 * @{
 * @addtogroup print_parser_config_module
 * <AUTHOR>
 * @date 2024-07-09
 * @version v0.1
 * @brief print parser config module
 */

#ifndef PRINT_PARSER_CONFIG_H
#define PRINT_PARSER_CONFIG_H

#include "pol/pol_string.h"
#include "pol/pol_threads.h"
#include "pol/pol_list.h"
#include "utilities/parsercommon.h"
#include "msgrouter_main.h"
#include "utilities/msgrouter.h"

/*** Head file area ***************************************************/

/**********************************************************************/

/*** Macro description area *******************************************/
/**********************************************************************/

 /*** Enumerate description area ***************************************/
typedef enum
{
    eCONFIG_FUNC_PWG_PRE_PARSER = 0,                        ///< PWG预解析
    eCONFIG_FUNC_URF_PRE_PARSER,                            ///< URF预解析
    eCONFIG_FUNC_IPS_PRE_PARSER,                            ///< IPS预解析
    eCONFIG_FUNC_PINCDE_PRE_PARSER,                         ///< 密码打印预解析
    eCONFIG_FUNC_UDISK_PRE_PARSER,                          ///< U盘打印
    eCONFIG_FUNC_SAMPLE_PRE_PARSER,                         ///< 样本打印
    eCONFIG_FUNC_DELAY_PRE_PARSER,                          ///< 延迟打印
    eCONFIG_FUNC_INTERNAL_PAGE_DEMO_PRE_PARSER,             ///< demo页
    eCONFIG_FUNC_INTERNAL_PAGE_INFO_PRE_PARSER,             ///< 信息页
    eCONFIG_FUNC_INTERNAL_PAGE_MENU_STRUCT_PRE_PARSER,      ///< 菜单结构页
    eCONFIG_FUNC_INTERNAL_PAGE_PRINT_QUALITY_PRE_PARSER,    ///< 质量测试页
    eCONFIG_FUNC_INTERNAL_PAGE_NET_CONFIG_PRE_PARSER,       ///< 网络配置页
    eCONFIG_FUNC_INTERNAL_PAGE_EVENT_LOG_PRE_PARSER,        ///< 事件日志页
    eCONFIG_FUNC_INTERNAL_PAGE_WIFI_WIZARD_PRE_PARSER,      ///< wifi向导页
    eCONFIG_FUNC_TYPE_INTERNAL_PAGE_WEB_PD_PRE_PARSER,      ///< web下载PDF
    eCONFIG_FUNC_INVALID_PRE_PARSER_MAX,
}PRINT_PARSER_CONFIG_FUNC_E;

typedef enum
{
    eCONFIG_STATUS_ENABLE = 0,
    eCONFIG_STATUS_DISABLE = 1,
}PRINT_PARSER_CONFIG_STATUS_E;

 /**********************************************************************/


/*** Variable description area ****************************************/
// ...
/**********************************************************************/


/*** Function description area ****************************************/
#ifdef __cplusplus
extern "C" {
#endif
/**
* @brief        module diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2024-07-08
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_module_enable(const MODULE_ID_E module_id);

/**
* @brief        function diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2024-07-08
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_function_enable(const PRINT_PARSER_CONFIG_FUNC_E efunction);


/**
* @brief        module initiation enbale
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2024-07-08
* @note         N/A
*/

PRINT_PARSER_CONFIG_STATUS_E print_parser_config_module_initiation_enable(const MODULE_ID_E module_id);


/**
* @brief        module funtion enbale
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2024-07-08
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_module_function_enable(const MODULE_ID_E module_id,
    const PRINT_PARSER_CONFIG_FUNC_E efunction);

/**
* @brief        pincode job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_pincode_job_enable(void);

/**
* @brief        semple job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_sample_job_enable(void);

/**
* @brief        zeromargin job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_zeromargin_job_enable(void);


/**
* @brief        delay job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_delay_job_enable(void);


/**
* @brief        finisher job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_finisher_job_enable(void);

/**
* @brief        urf job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_urf_job_enable(void);

/**
* @brief        pwg job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_pwg_job_enable(void);

/**
* @brief        PDF job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-05-19
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_pdf_job_enable(void);

/**
* @brief        JPEG job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-05-19
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_jpeg_job_enable(void);


/**
* @brief        pincode job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_pincode_job_enable(void);

/**
* @brief        sample job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_sample_job_enable(void);

/**
* @brief        zeromargin job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_zeromargin_job_enable(void);


/**
* @brief        delay job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_delay_job_enable(void);


/**
* @brief        finisher job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_finisher_job_enable(void);

/**
* @brief        urf job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_urf_job_enable(void);

/**
* @brief        pwg job diable of enable
* @param[in]    void
* @param[out]   N/A
* @return       int8_t
* @retval       0: disable
* @retval       1: enable
* <AUTHOR>
* @date         2025-02-24
* @note         N/A
*/
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_pwg_job_enable(void);

/**
 * @brief        pjl diable or enable
 * @param[in]    void
 * @param[out]   N/A
 * @return       int8_t
 * @retval       0: disable
 * @retval       1: enable
 * <AUTHOR>
 * @date         2025-06-11
 * @note         N/A
 */
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_pjl_ep_enable(void);

/**
 * @brief        xor diable or enable
 * @param[in]    void
 * @param[out]   N/A
 * @return       int8_t
 * @retval       0: disable
 * @retval       1: enable
 * <AUTHOR>
 * @date         2025-06-11
 * @note         N/A
 */
PRINT_PARSER_CONFIG_STATUS_E print_parser_config_get_xor_enable(void);

/**
* @brief print parser config debug
* @param[in] arg - param num
* @param[in] argv - param string
* @param[out]
* @return int32
* @retval 0 success
* @retval !0 failure
* <AUTHOR> @date
* @note  N/A
*/
int32_t  print_parser_config_debug(int argc, char *argv[]);

#ifdef __cplusplus
}
#endif

/**********************************************************************/

#endif

/**
*@}
*/

