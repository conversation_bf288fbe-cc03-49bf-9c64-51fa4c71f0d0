/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file prnsdk_state_timer.c
 * @addtogroup mainapp
 * @{
 * @brief PRINT SDK state timer module
 * <AUTHOR>
 * @version 1.0
 * @date 2024-12-09
 */
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <stdio.h>
#include <stdint.h>
#include <sys/eventfd.h>
#include "prnsdk_state_timer.h"
#include "pol/pol_io.h"
#include "pol/pol_log.h"

static void* state_timer_thread (void *arg)
{
    STATE_TIMER_S *stimer  = (STATE_TIMER_S*)arg;
    unsigned long long val = 0;
    struct timeval timeout;
    int32_t ret   = 0;
    int32_t start = 0;
    fd_set rset;                ///< 句柄集合

    pi_log_d("_internal_thread\n");

    FD_ZERO(&rset);
    FD_SET(stimer->tfd , &rset);      ///< 将定时器句柄tf加入集合中
    timeout = stimer->time_arg;
    while ( 1 )
    {
        pi_mutex_lock(&stimer->mtx);
        if ( stimer->bExit )
        {
            pi_mutex_unlock(&stimer->mtx);
            break;
        }
        timeout = stimer->time_arg;
        start = stimer->bStart;
        pi_mutex_unlock(&stimer->mtx);

        ret = pi_select(stimer->tfd + 1 , &rset , NULL , NULL , start?&timeout:NULL);
        if ( ret == -1 )    ///< 返回值校验
        {
            pi_fprintf(stderr , "ctimer error , %s\n" , strerror(errno));
            break;
        }
        else if ( ret == 0 )
        {
            stimer->timeout_callback(stimer->arg);

            FD_ZERO(&rset);
            FD_SET(stimer->tfd , &rset);

            pi_mutex_lock(&stimer->mtx);
            timeout = stimer->time_arg;
            if ( !stimer->bCircu )
            {
                stimer->bStart = 0;
            }

            pi_mutex_unlock(&stimer->mtx);
        }
        else
        {
            pi_read(stimer->tfd , &val , sizeof(val));
        }
    } ///< end while

    return NULL;
}

int32_t state_timer_init(STATE_TIMER_S *stimer ,
                            void (*timeout_callback) (void *data) ,
                            void *arg ,
                            struct timeval timeout_arg ,
                            int32_t bCircu
                        )
{
    if ( !stimer || !timeout_callback || (timeout_arg.tv_sec <= 0 && timeout_arg.tv_usec <= 0) )
    {
        return -1;
    }

    stimer->timeout_callback = timeout_callback;
    stimer->tfd = eventfd(0 , EFD_CLOEXEC|EFD_NONBLOCK);        ///< 非阻塞
    if ( stimer->tfd == -1 )
    {
        pi_log_e("create eventfd failed , %s\n" , strerror(errno)) ;
        return -1;
    }
    stimer->bCircu      = bCircu;
    stimer->bStart      = 0;
    stimer->bExit       = 0;
    stimer->time_arg    = timeout_arg;
    stimer->arg         = arg;
    stimer->mtx         = pi_mutex_create();
    if ( stimer->mtx == INVALIDMTX )
    {
        pi_log_e("create mutex failed\n");
        pi_close(stimer->tfd);
        stimer->tfd = -1;
        return -1;
    }
    stimer->timer_pid = 0;
    pi_log_d("bCircu = %d\n",stimer->bCircu);
    stimer->timer_pid = pi_thread_create(state_timer_thread, PI_NORMAL_STACK, NULL, PI_LEVEL_PRIORITY, stimer , "prnsdk_state_timer");
    if ( stimer->timer_pid == INVALIDTHREAD )
    {
        pi_log_e("create thread failed\n");
        pi_mutex_destroy(stimer->mtx);
        stimer->mtx = INVALIDMTX;
        pi_close(stimer->tfd);
        stimer->tfd = -1;
        return -1;
    }

    return 0;
}

int32_t state_timer_start(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(&stimer->mtx);
    if (stimer->bStart)
    {
        pi_mutex_unlock(&stimer->mtx);
        return 0;
    }
    stimer->bStart = 1;
    pi_mutex_unlock(&stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    return 0;
}

int32_t state_timer_active(STATE_TIMER_S *stimer)
{
    int32_t ret = 0;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(&stimer->mtx);
    ret = stimer->bStart;
    pi_mutex_unlock(&stimer->mtx);

    return ret;
}

int32_t state_timer_stop(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(&stimer->mtx);
    if ( !stimer->bStart )
    {
        pi_mutex_unlock(&stimer->mtx);
        return 0;
    }
    stimer->bStart = 0;
    stimer->bCircu = 0;
    pi_mutex_unlock(&stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    return 0;
}

int32_t state_timer_restart(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if (!stimer)
    {
        return -1;
    }

    pi_mutex_lock(&stimer->mtx);
    stimer->bStart = 0;
    pi_mutex_unlock(&stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    pi_mutex_lock(&stimer->mtx);
    stimer->bStart = 1;
    pi_mutex_unlock(&stimer->mtx);
    pi_write(stimer->tfd, &val, sizeof(val));

    return 0;
}

int32_t state_timer_update(STATE_TIMER_S *stimer , struct timeval timeout_arg)
{
    if ( !stimer || (timeout_arg.tv_sec <= 0 && timeout_arg.tv_usec <= 0) )
    {
        return -1;
    }

    pi_mutex_lock(&stimer->mtx);
    if  ( stimer->bStart )
    {
        pi_mutex_unlock(&stimer->mtx);
        return -1;
    }

    stimer->time_arg = timeout_arg;
    pi_mutex_unlock(&stimer->mtx);

    return 0;
}

int32_t state_timer_destroy(STATE_TIMER_S *stimer)
{
    uint64_t val = 1;

    if ( !stimer )
    {
        return -1;
    }

    pi_mutex_lock(&stimer->mtx);
    stimer->bStart = 0;
    stimer->bExit  = 1;
    pi_mutex_unlock(&stimer->mtx);
    pi_write(stimer->tfd , &val , sizeof(val));

    pi_sleep(1);

    if (stimer->timer_pid != INVALIDTHREAD)
    {
        pi_thread_destroy(stimer->timer_pid);
        stimer->timer_pid = INVALIDTHREAD;
    }
    if (stimer->mtx != INVALIDMTX)
    {
        pi_mutex_destroy(&stimer->mtx);
        stimer->mtx = INVALIDMTX;
    }
    if (stimer->tfd != -1)
    {
        close(stimer->tfd);
        stimer->tfd = -1;
    }
    pi_log_d("state timer %lu be destroyed\n", stimer->timer_pid);

    return 0;
}
/**
 * @}
 */

