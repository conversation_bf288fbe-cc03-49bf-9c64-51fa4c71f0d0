#include "nettypes.h"
#include "netctx.h"
#include "pedk_addressbook.h"
#include "pedk_ssl.h"
#include "pedk_mgr.h"
#include "pedk_http.h"

static void net_pedkapi_handler(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t* buf, void* ctx)
{

    NET_CTX_S*  net_ctx = (NET_CTX_S *)ctx;
    int32_t     ret = 0;

    NET_IPV4_CONF_S ipv4_conf;

    switch ( sub )
    {
    case MSG_NET_SUB_ADD_EMAIL_ADDR:
        {
            int32_t add_index = -1;
            add_index = add_email_addr((PEDK_MAIL_PARM*)buf);
            if ( add_index < 0 )
            {
                ret = -1;
            }
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_ADD_EMAIL_ADDR, ret, (uint8_t*)&add_index, sizeof(int32_t));
            break;
        }
    case MSG_NET_SUB_GET_EMAIL_ADDR:
        {
            PEDK_MAIL_PARM  email_addr;
            size_t          mail_size;
            int32_t         index = *buf;

            ret = get_email_addr(index, &email_addr, &mail_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR, -1, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR, 0, (uint8_t*)&email_addr, sizeof(PEDK_MAIL_PARM));
            }
            break;
        }
    case MSG_NET_SUB_GET_EMAIL_ADDR_LIST:
        {
            size_t         mail_size;
            int32_t        array_num = 0;

            array_num = get_email_addr_num();

            PEDK_MAIL_PARM email_list[array_num];
            ret = get_email_addr_list(email_list, array_num, &mail_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_LIST, 0, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_LIST, array_num, (uint8_t*)&email_list, (int32_t)mail_size);
            }
            break;
        }
    case MSG_NET_SUB_GET_EMAIL_ADDR_NUM:
        {
            int32_t email_num = 0;
            email_num = get_email_addr_num();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_NUM, 0, (uint8_t*)&email_num, sizeof(email_num));
            break;
        }
    case MSG_NET_SUB_IS_EMAIL_ADDR_FULL:
        {
            int32_t email_full = 0;
            email_full = is_email_addr_full();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_IS_EMAIL_ADDR_FULL, 0, (uint8_t*)&email_full, sizeof(email_full));
            break;
        }
    case MSG_NET_SUB_MODIFY_EMAIL_ADDR:
        {
            ret = modify_email_addr((PEDK_MAIL_PARM*)buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_EMAIL_ADDR, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_REMOVE_EMAIL_ADDR:
        {
            int32_t index = *buf;
            ret = remove_email_addr(index);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_ADDR, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_ADD_EMAIL_TO_GROUP:
        {
            int32_t *p = (int32_t*)buf;
            ret = add_email_to_group(p[0], p[1]);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_ADD_EMAIL_TO_GROUP, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_CREAT_EMAIL_GROUP:
        {
            int32_t add_index = -1;

            add_index = creat_email_group((PEDK_MAIL_GROUP_MGR*)buf);
            if ( add_index < 0 )
            {
                ret = -1;
            }

            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_CREAT_EMAIL_GROUP, ret, (uint8_t*)&add_index, sizeof(int32_t));
            break;
        }
    case MSG_NET_SUB_GET_EMAIL_GROUP:
        {
            PEDK_MAIL_GROUP_MGR group_addr;
            int32_t             index = *buf;
            size_t              mail_group_size;

            ret = get_email_group(index, &group_addr, &mail_group_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP, -1, NULL, 0 );
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP, 0, (uint8_t*)&group_addr, sizeof(PEDK_MAIL_GROUP_MGR));
            }
            break;
        }
    case MSG_NET_SUB_GET_EMAIL_GROUP_LIST:
        {
            size_t      mail_group_size;
            int32_t     array_num = 0;

            array_num = get_email_group_num();
            PEDK_MAIL_GROUP_MGR group_list[array_num];
            ret = get_email_group_list(group_list, array_num, &mail_group_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_LIST, -1, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_LIST, array_num, (uint8_t*)&group_list, (int32_t)mail_group_size);
            }
            break;
        }
    case MSG_NET_SUB_GET_EMAIL_GROUP_NUM:
        {
            int32_t group_num = 0;
            group_num = get_email_group_num();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_NUM, 0, (uint8_t*)&group_num, sizeof(group_num));
            break;
        }
    case MSG_NET_SUB_IS_EMAIL_GROUP_FULL:
        {
            int32_t group_full = 0;
            group_full = is_email_group_full();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_IS_EMAIL_GROUP_FULL, 0, (uint8_t*)&group_full, sizeof(group_full));
            break;
        }
    case MSG_NET_SUB_MODIFY_EMAIL_GROUP:
        {
            ret = modify_email_group(((PEDK_MAIL_GROUP_MGR*)buf)->pedk_group_index, (PEDK_MAIL_GROUP_MGR*)buf);
            NET_DEBUG("MSG_NET_SUB_MODIFY_EMAIL_GROUP %d ", ret);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_EMAIL_GROUP, ret, NULL, 0);
            NET_DEBUG("MSG_NET_SUB_MODIFY_EMAIL_GROUP1 %d ", ret);
            break;
        }
    case MSG_NET_SUB_REMOVE_EMAIL_FROM_GROUP:
        {
            int32_t *p = (int32_t*)buf;
            ret = remove_email_from_group(p[0], p[1]);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_FROM_GROUP, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_REMOVE_EMAIL_GROUP:
        {
            int32_t index = (int32_t)*buf;
            ret = remove_email_group(index);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_GROUP, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_ADD_FTP_ADDR:
        {
            int32_t add_index = -1;
            add_index = add_ftp_addr((PEDK_FTP_PARM*)buf);
            if ( add_index < 0 )
            {
                ret = -1;
            }

            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_ADD_FTP_ADDR, ret, (uint8_t*)&add_index, sizeof(int32_t));
            break;
        }
    case MSG_NET_SUB_GET_FTP_ADDR:
        {
            PEDK_FTP_PARM   ftp_addr;
            size_t          ftp_addr_size;
            int32_t         index = *buf;

            ret = get_ftp_addr(index, &ftp_addr, &ftp_addr_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR, -1, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR, 0, (uint8_t*)&ftp_addr, sizeof(PEDK_FTP_PARM));
            }
            break;
        }
    case MSG_NET_SUB_GET_FTP_ADDR_LIST:
        {
            size_t        ftp_list_size;
            int32_t       array_num = 0;
            array_num = get_ftp_addr_num();

            PEDK_FTP_PARM ftp_list[array_num];
            ret = get_ftp_addr_list(ftp_list, array_num, &ftp_list_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_LIST, 0, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_LIST, array_num, (uint8_t*)&ftp_list, (int32_t)ftp_list_size);
            }
            break;
        }
    case MSG_NET_SUB_GET_FTP_ADDR_NUM:
        {
            int32_t ftp_num = 0;
            ftp_num = get_ftp_addr_num();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_NUM, 0, (uint8_t*)&ftp_num, sizeof(ftp_num));
            break;
        }
    case MSG_NET_SUB_IS_FTP_ADDR_FULL:
        {
            int32_t ftp_full = 0;
            ftp_full = is_ftp_addr_full();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_IS_FTP_ADDR_FULL, 0, (uint8_t*)&ftp_full, sizeof(ftp_full));
            break;
        }
    case MSG_NET_SUB_MODIFY_FTP_ADDR:
        {
            ret = modify_ftp_addr((PEDK_FTP_PARM*)buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_FTP_ADDR, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_REMOVE_FTP_ADDR:
        {
            int32_t index = *buf;
            ret = remove_ftp_addr(index);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_FTP_ADDR, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_ADD_SMB_ADDR:
        {
            int32_t add_index = -1;
            add_index = add_smb_addr((PEDK_SMB_PARM*)buf);
            if ( add_index < 0 )
            {
                ret = -1;
            }
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_ADD_SMB_ADDR, ret, (uint8_t*)&add_index, sizeof(int32_t));
            break;
        }
    case MSG_NET_SUB_GET_SMB_ADDR:
        {
            PEDK_SMB_PARM   smb_addr;
            int32_t         index = *buf;
            size_t          smb_size;

            ret = get_smb_addr(index, &smb_addr, &smb_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR, -1, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR, 0, (uint8_t*)&smb_addr, sizeof(PEDK_SMB_PARM));
            }
            break;
        }
    case MSG_NET_SUB_GET_SMB_ADDR_LIST:
        {
            size_t        smb_list_size;
            int32_t       array_num = 0;

            array_num = get_smb_addr_num();
            PEDK_SMB_PARM smb_list[array_num];
            ret = get_smb_addr_list(smb_list, array_num, &smb_list_size);
            if ( ret != 0 )
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_LIST, 0, NULL, 0);
            }
            else
            {
                pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_LIST, array_num, (uint8_t*)&smb_list, (int32_t)smb_list_size);
            }
            break;
        }
    case MSG_NET_SUB_GET_SMB_ADDR_NUM:
        {
            int32_t smb_num = 0;
            smb_num = get_smb_addr_num();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_NUM, 0, (uint8_t*)&smb_num, sizeof(smb_num));
            break;
        }
    case MSG_NET_SUB_IS_SMB_ADDR_FULL:
        {
            int32_t smb_full = 0;
            smb_full = is_smb_addr_full();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_IS_SMB_ADDR_FULL, 0, (uint8_t*)&smb_full, sizeof(smb_full));
            break;
        }
    case MSG_NET_SUB_MODIFY_SMB_ADDR:
        {
            ret = modify_smb_addr((PEDK_SMB_PARM*)buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_SMB_ADDR, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_REMOVE_SMB_ADDR:
        {
            int32_t index = *buf;
            ret = remove_smb_addr(index);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_SMB_ADDR, ret, NULL, 0);
            break;
        }
#if 0
    case MSG_NET_SUB_SEND_EMAIL:
        {
            SEND_EMAIL_INFO* smtp_send_info = (SEND_EMAIL_INFO*)buf;
            ret = send_email(&smtp_send_info->smtp_server_info, smtp_send_info->recipient_email, smtp_send_info->send_from, smtp_send_info->subject, smtp_send_info->body);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SEND_EMAIL, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_FTP_CONNECT:
        {
            PEDK_FTP_PARAMETER_SET* ftp_connect_info = (PEDK_FTP_PARAMETER_SET*)buf;
            ret = ftp_connetct(ftp_connect_info);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_FTP_CONNECT, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_FTP_DISCONNECT:
        {
            ret = ftp_disconnect();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_FTP_DISCONNECT, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SMB_CONNECT:
        {
            PEDK_SMB_PARAMETER_SET* smb_connect_info = (PEDK_SMB_PARAMETER_SET*)buf;
            ret = smb_connetct(smb_connect_info);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SMB_CONNECT, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SMB_DISCONNECT:
        {
            ret = smb_disconnect();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SMB_DISCONNECT, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_GET_SMTP_EMAIL_ENCRYPT_MODE:
        {
            int32_t return_int;
            return_int = get_smtp_email_encrypt_mode();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_EMAIL_ENCRYPT_MODE, 0, &return_int, sizeof(return_int));
            break;
        }
    case MSG_NET_SUB_GET_SMTP_LOGIN_NAME:
        {
            char *return_buf = get_smtp_login_name();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_LOGIN_NAME, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_SMTP_LOGIN_PWD:
        {
            char *return_buf = get_smtp_login_pwd();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_LOGIN_PWD, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_SMTP_PORT:
        {
            int32_t return_int;
            return_int = get_smtp_port();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_PORT, 0, &return_int, sizeof(return_int));
            break;
        }
    case MSG_NET_SUB_GET_SMTP_SERVER_NAME:
        {
            char *return_buf = get_smtp_server_name();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_SERVER_NAME, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_SET_SMTP_EMAIL_ENCRYPT_MODE:
        {
            int32_t set_int = *buf;
            ret = set_smtp_email_encrypt_mode(set_int);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_EMAIL_ENCRYPT_MODE, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_SMTP_LOGIN_NAME:
        {
            int32_t* set_buf = buf;
            ret = set_smtp_login_name(set_buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_LOGIN_NAME, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_SMTP_LOGIN_PWD:
        {
            int32_t* set_buf = buf;
            ret = set_smtp_login_pwd(set_buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_LOGIN_PWD, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_SMTP_PORT:
        {
            int32_t set_int = *buf;
            ret = set_smtp_port(set_int);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_PORT, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_SMTP_SERVER_NAME:
        {
            int32_t* set_buf = buf;
            ret = set_smtp_server_name(set_buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_SERVER_NAME, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_GET_WIRED_CONNECT_INFO:
        {
            int32_t return_int;
            return_int = get_wired_connect_info();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_CONNECT_INFO, 0, &return_int, sizeof(return_int));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO:
        {
            char *return_buf = get_wired_mac_addr_info();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_802_PROTOCOL_SWITCH:
        {
            int32_t return_int;
            return_int = get_wired_net_802_protocol_switch();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_802_PROTOCOL_SWITCH, 0, &return_int, sizeof(return_int));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR:
        {
            char *return_buf = get_wired_net_ipv4_addr();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY:
        {
            char *return_buf = get_wired_net_ipv4_gateway();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_HOSTNAME:
        {
            char *return_buf = get_wired_net_ipv4_hostname();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_HOSTNAME, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK:
        {
            char *return_buf = get_wired_net_ipv4_mask();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK, 0, return_buf, strlen(return_buf + 1));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE:
        {
            int32_t return_int;
            return_int = get_wired_net_ipv4_mode();
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE, 0, &return_int, sizeof(return_int));
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_802_PROTOCOL_SWITCH:
        {
            int32_t set_int = *buf;
            ret = set_wired_net_802_protocol_switch(set_int);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_802_PROTOCOL_SWITCH, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR:
        {
            int32_t* set_buf = buf;
            ret = set_wired_net_ipv4_addr(set_buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_HOSTNAME:
        {
            int32_t* set_buf = buf;
            ret = set_wired_net_ipv4_hostname(set_buf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_HOSTNAME, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE:
        {
            int32_t set_int = *buf;
            ret = set_wired_net_ipv4_mode(set_int);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV6_SWITCH:
        {
            int32_t set_int = *buf;
            ret = set_wired_net_ipv6_switch(set_int);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV6_SWITCH, ret, NULL, 0);
            break;
        }
#endif
    case MSG_NET_SUB_GEN_CERT:
        {
            TLS_CERT_CONF_S*  info = (TLS_CERT_CONF_S *)buf;
            const char* ret_s = NULL;
            int32_t     len = 0;

            NET_DEBUG("[pedkssl] pedk_make_ssl_certificate");
            ret_s = pedk_ssl_make_certificate(info);
            len = strlen(ret_s) + 1;
            NET_DEBUG("[pedkssl] pedk_make_ssl_certificate ret_val:(%s) ; len = (%d)", ret_s, len);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET,MSG_NET_SUB_GEN_CERT, 0, (unsigned char*)ret_s, len);
            //NET_DEBUG("Print recv data size is %d\n", buf_size);

            break;
        }

    case MSG_NET_SUB_SSL_LOAD_CERT:
        {
            PEDK_SSL_LOAD_INFO_S*   param = (PEDK_SSL_LOAD_INFO_S *)buf;
            const char*             ret_l = NULL;
            int32_t                 len = 0;

            NET_DEBUG("[pedkssl] pedk_loadSsl_certificate");
            ret_l = pedk_ssl_load_certificate(param->keypath, param->certpath, param->password);
            len = strlen(ret_l) + 1;
            NET_DEBUG("[pedkssl] loadSsl ret_val:(%s) ; len = (%d)", ret_l, len);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SSL_LOAD_CERT, 0, (unsigned char*)ret_l, len);

            break;
        }

    case MSG_NET_SUB_SSL_CREATE_CONN:
        {
            PEDK_SSL_CONNECT_INFO_S* creat_info = (PEDK_SSL_CONNECT_INFO_S *)buf;
            const char*         ret_c = NULL;
            int32_t             len = 0;

            NET_DEBUG("[pedkssl] createSslConnection");
            ret_c = pedk_ssl_create_connection(creat_info);
            len = strlen(ret_c) + 1;
            NET_DEBUG("[pedkssl] createSsl ret_val:(%s) ; len = (%d)", ret_c, len);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SSL_CREATE_CONN, 0, (unsigned char*)ret_c, len);

            break;
        }

    case MSG_NET_SUB_SSL_RECV:
        {
            PEDK_SSL_CONNECT_DATA_S*  ssl_recv = (PEDK_SSL_CONNECT_DATA_S *)buf;
            int                       ret_r;

            NET_DEBUG("[pedkssl] pedk_sslrecv");
            ret_r = pedk_ssl_connect_recv(ssl_recv);
            NET_DEBUG("[pedkssl] pedk_sslrecv ret_val:(%d)", ret_r);
            break;
        }

    case MSG_NET_SUB_SSL_SEND:
        {
            PEDK_SSL_CONNECT_DATA_S*  ssl_send = (PEDK_SSL_CONNECT_DATA_S *)buf;
            const char*         ret_d = NULL;
            int32_t             len = 0;

            NET_DEBUG("[pedkssl] pedk_sslsend");
            ret_d = pedk_ssl_connect_send(ssl_send);
            len = strlen(ret_d) + 1;
            NET_DEBUG("[pedkssl] pedk_sslsend ret_val:(%s); len = (%d)", ret_d, len);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SSL_SEND, 0, (unsigned char*)ret_d, len);
            break;
        }

    case MSG_NET_SUB_SSL_CLOSE:
        {
            PEDK_SSL_CONNECT_DATA_S*  ssl_close = (PEDK_SSL_CONNECT_DATA_S *)buf;
            const char*         ret_e = NULL;
            int32_t             len = 0;

            NET_DEBUG("[pedkssl] pedk_sslclose");
            ret_e = pedk_ssl_connect_close(ssl_close);
            len = strlen(ret_e) + 1;
            NET_DEBUG("[pedkssl] pedk_sslclose ret_val:(%s) ; len = (%d)", ret_e, len);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SSL_CLOSE, 0, (unsigned char*)ret_e, len);
            break;
        }
    case MSG_NET_SUB_SSL_INSTALL_CERT:
        {
            PEDK_SSL_CERT_INFO_S*  ssl_close = (PEDK_SSL_CERT_INFO_S *)buf;

            NET_DEBUG("[pedkssl] pedk_install_cert");
            NET_DEBUG("[pedkssl] cert_content %s, key_content %s, key_password %s",ssl_close->cert_content,
                                                                                   ssl_close->key_content,
                                                                                   ssl_close->key_password);
            // 写入证书
            if (pedk_ssl_write_certificate(ssl_close->cert_content) != 0)
            {
                NET_DEBUG("[pedkssl] Failed to write certificate file\n");
                ret = -1;
            }

            if(ssl_close->export_flag)
            {
                // 写入私钥
                if (pedk_ssl_write_private_key(ssl_close->key_content, ssl_close->key_password) != 0)
                {
                    NET_DEBUG("[pedkssl] Failed to write private key file\n");
                    ret = -2;
                }
            }

            NET_DEBUG("[pedkssl] MSG_NET_SUB_SSL_INSTALL_CERT ret %d", ret);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SSL_INSTALL_CERT, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_HTTPS_SET_CONFIG_PARAM:
        {
            HTTPS_CONFIG_PARAM_S* https_config = (HTTPS_CONFIG_PARAM_S*)buf;
            NET_DEBUG("[pedkssl] pedk_set config param");
            ret = pedk_http_set_https_config(https_config);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_HTTPS_SET_CONFIG_PARAM, ret, NULL, 0);
            break;
        }
    case MSG_NET_SUB_HTTPS_GET_CONFIG_PARAM:
        {
            HTTPS_CONFIG_PARAM_S https_config;
            memset(&https_config, 0, sizeof(https_config));
            NET_DEBUG("[pedkssl] pedk_get config param");
            ret = pedk_http_get_https_config(&https_config);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_HTTPS_GET_CONFIG_PARAM, ret, (uint8_t *)&https_config, sizeof(https_config));

            break;
        }
    case MSG_SETTING_SUB_SET_SCAN_HTTP_PARAMS:
        {
            SCAN_HTTP_PARAM_S *scan_http_param = (SCAN_HTTP_PARAM_S *)buf;
            NET_DEBUG("[mfp] Received scan HTTP parameters:");
            NET_DEBUG("[mfp] URL: %s", scan_http_param->url);
            NET_DEBUG("[mfp] Headers: %s", scan_http_param->headers);
            NET_DEBUG("[mfp] file_name_prefix: %s", scan_http_param->file_name_prefix);
            NET_DEBUG("[mfp] custom_field: %s", scan_http_param->custom_field);

            ret = pedk_http_set_url_header_params(scan_http_param);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_SETTING_SUB_SET_SCAN_HTTP_PARAMS, ret, NULL, 0);
            break;
        }
    case MSG_SETTING_SUB_GET_SCAN_HTTP_PARAMS:
        {
            SCAN_HTTP_PARAM_S scan_http_param;
            memset(&scan_http_param, 0, sizeof(scan_http_param));
            NET_DEBUG("[pedkssl] pedk_get config param");
            ret = pedk_http_get_url_header_params(&scan_http_param);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_SETTING_SUB_GET_SCAN_HTTP_PARAMS, ret, (uint8_t *)&scan_http_param, sizeof(scan_http_param));

            break;
        }

    case MSG_NET_SUB_GET_PRODUCT_UUID:
        {
            char    uuid[64] = {0};
            ret = netdata_get_uuid(net_ctx->data_mgr, uuid, sizeof(uuid));
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_PRODUCT_UUID, ret, (unsigned char*)uuid, sizeof(uuid));
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE://UINT32_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index)
        {
            uint32_t ipv4_mode;
            ipv4_mode = netdata_get_ipv4_usedhcp(net_ctx->data_mgr, IFACE_ID_ETH);
            NET_DEBUG("[pedkapi] get ipv4_mode %u,sizeof %d\n",ipv4_mode, sizeof(ipv4_mode));

            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE, 0, (uint8_t *)&ipv4_mode, sizeof(ipv4_mode));
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE://UINT32_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, uint32_t val)
        {
            uint32_t dhcp_enabled = *buf;
            NET_DEBUG("[pedkapi] set ipv4_mode %u\n",dhcp_enabled);
            memset(&ipv4_conf, 0x0, sizeof(NET_IPV4_CONF_S));
            ipv4_conf.dhcp_enabled = dhcp_enabled;
            netctx_update_ipv4_config(net_ctx, IFACE_ID_ETH, &ipv4_conf);

            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE, 0, NULL, 0);
            break;
        }

    case MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, char* str, size_t size)
        {
            char str_ipv4_addr[32] = {0};
            int32_t len = netdata_get_ipv4_addr(net_ctx->data_mgr, IFACE_ID_ETH, str_ipv4_addr, sizeof(str_ipv4_addr));
            NET_DEBUG("[pedkapi] get ipv4_addr %s\n",str_ipv4_addr);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR, 0, (uint8_t *)str_ipv4_addr, sizeof(str_ipv4_addr));
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, const char* str)
        {
            NET_DEBUG("[pedkapi] set ipv4_addr %s\n",buf);
            memset(&ipv4_conf, 0x0, sizeof(NET_IPV4_CONF_S));
            memcpy(ipv4_conf.address, buf, sizeof(ipv4_conf.address));
            ipv4_conf.changed = IPV4_CONF_ADDRESS;
            netctx_update_ipv4_config(net_ctx, IFACE_ID_ETH, &ipv4_conf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR, 0, NULL, 0);
            break;
        }
    case MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, char* str, size_t size)
        {
            char str_ipv4_mask[64] = {0};
            int32_t len = netdata_get_ipv4_mask(net_ctx->data_mgr, IFACE_ID_ETH, str_ipv4_mask, sizeof(str_ipv4_mask));
            NET_DEBUG("[pedkapi] get ipv4_mask %s\n",str_ipv4_mask);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK, 0, (uint8_t *)str_ipv4_mask, sizeof(str_ipv4_mask));
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_MASK://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, const char* str)
        {
            NET_DEBUG("[pedkapi] set ipv4_mask %s\n",buf);
            memset(&ipv4_conf, 0x0, sizeof(NET_IPV4_CONF_S));
            memcpy(ipv4_conf.mask, buf, sizeof(ipv4_conf.mask));
            ipv4_conf.changed = IPV4_CONF_MASK;
            netctx_update_ipv4_config(net_ctx, IFACE_ID_ETH, &ipv4_conf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MASK, 0, NULL, 0);
            break;
        }

    case MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, char* str, size_t size)
        {
            char str_ipv4_gateway[32] = {0};
            int32_t len = netdata_get_ipv4_gtwy(net_ctx->data_mgr, IFACE_ID_ETH, str_ipv4_gateway, sizeof(str_ipv4_gateway));
            NET_DEBUG("[pedkapi] get ipv4_gateway %s\n",str_ipv4_gateway);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY, 0, (uint8_t *)str_ipv4_gateway, sizeof(str_ipv4_gateway));
            break;
        }
    case MSG_NET_SUB_SET_WIRED_NET_IPV4_GATEWAY://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, const char* str)
        {
            NET_DEBUG("[pedkapi] set ipv4_gateway %s\n",buf);
            memset(&ipv4_conf, 0x0, sizeof(NET_IPV4_CONF_S));
            memcpy(ipv4_conf.gateway, buf, sizeof(ipv4_conf.gateway));
            ipv4_conf.changed = IPV4_CONF_GATEWAY;
            netctx_update_ipv4_config(net_ctx, IFACE_ID_ETH, &ipv4_conf);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_GATEWAY, ret, NULL, 0);
            break;
        }

    case MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO://STRING_ARRAY ---- (DATA_MGR_S* thiz, uint32_t index, char* str, size_t size)
        {
            char str_mac_addr[32] = {0};
            int32_t len = netdata_get_mac_addr(net_ctx->data_mgr, IFACE_ID_ETH, str_mac_addr, sizeof(str_mac_addr));
            NET_DEBUG("[pedkapi] get str_mac_addr %s\n",str_mac_addr);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO, 0, (uint8_t *)str_mac_addr, sizeof(str_mac_addr));
            break;
        }

    default:
        {
            NET_WARN("invalid SUB_MSG(%d)", (int32_t)sub);
            break;
        }
    }

}

int32_t net_pedkapi_init(NET_CTX_S* net_ctx)
{
    int32_t ret = 0;

    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);
    ret = pedk_mgr_register_handler(MSG_MODULE_NET, net_pedkapi_handler, (void *)net_ctx);
    RETURN_VAL_IF(ret != 0, NET_WARN, ret);

    return ret;
}

void net_pedkapi_deinit(void)
{
    pedk_mgr_unregister_handler(MSG_MODULE_NET);
}
