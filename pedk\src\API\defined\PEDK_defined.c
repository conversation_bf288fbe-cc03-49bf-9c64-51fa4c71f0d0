#include "PEDK_defined.h"

#include <quickjs.h>
#include "PEDK_lowpower.h"
#include "PEDK_total_pages.h"

#define JS_INIT_MODULE js_init_module
#define countof(x) (sizeof(x) / sizeof((x)[0]))

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

	/* demo */
    {"testAdd", 2, js_test_add},
    {"testAddStr", 2, js_test_add_str},
    {"helloWrold", 0, js_test_HelloWrold},
	{"threadInit", 0, js_test_threadInit},
	{"send2printer", 1, js_test_send2printer},
    {"getPrinterEvent", 0, js_test_getPrinterEvent},
	{"send2printercopy", 1, js_test_send2printercopy},

    //lowpower
    {"getCurrentState",0,js_lowpower_get_CurrentState},
    {"requestStateChange",1,js_lowpower_set_CurrentState},

	/* demo */

    /* status */
    {"getStatusIdListLength", 1, js_getStatusIdListLength},
    {"getStatusIdList", 1, js_getStatusIdList},
    {"getTopStatusId", 1, js_getTopStatusId},
    {"getStatusType", 1, js_getStatusType},
    {"getStatusModule", 1, js_getStatusModule},
    {"getStatusPriority", 1, js_getStatusPriority},

    {"getTotalColorPrintPage", 0, js_getTotalColorPrintPage},
    {"getTotalMonoPrintPage", 0, js_getTotalMonoPrintPage},
    {"getTotalPagesByMediaSize", 1, js_getTotalPagesByMediaSize},
    {"getTotalPrintedSheets", 0, js_getTotalPrintedSheets},
};

const JSCFunctionList* getJSCFunctionList(int *length) {
	*length = countof(pesf_funcs);
	return pesf_funcs;
}

int addGlobalAPI(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;

   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = getJSCFunctionList(&count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   return 0;
}

#if 0
/* 定义API的函数入口名称及列表 */
static const JSCFunctionListEntry pesf_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */

	/* demo */
    JS_CFUNC_DEF("testAdd", 2, js_test_add),
    JS_CFUNC_DEF("testAddStr", 2, js_test_add_str),
    JS_CFUNC_DEF("helloWrold", 0, js_test_HelloWrold),
	JS_CFUNC_DEF("threadInit", 0, js_test_threadInit),
	JS_CFUNC_DEF("send2printer", 1, js_test_send2printer),
    JS_CFUNC_DEF("getPrinterEvent", 0, js_test_getPrinterEvent),
	JS_CFUNC_DEF("send2printercopy", 1, js_test_send2printercopy),
	/* demo */
};

/* 定义初始化回调方法（由系统调用，入参格式固定），将函数入口列表 在模块中暴露 */
static int pesf_init(JSContext *ctx, JSModuleDef *m)
{
    return JS_SetModuleExportList(ctx, m, pesf_funcs,
                                  countof(pesf_funcs));
}

/* 定义初始化模块方法，由系统自动调用，且函数名称不可更改 */
JSModuleDef *JS_INIT_MODULE(JSContext *ctx, const char *module_name)
{
    JSModuleDef *m;
    m = JS_NewCModule(ctx, module_name, pesf_init);
    if (!m)
        return NULL;
    JS_AddModuleExportList(ctx, m, pesf_funcs, countof(pesf_funcs));
    return m;
}
#endif