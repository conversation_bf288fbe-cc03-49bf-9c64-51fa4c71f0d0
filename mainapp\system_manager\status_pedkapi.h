/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file status_pedk.h
 * @addtogroup system_manager
 * @{
 * @addtogroup status_pedk
 * <AUTHOR>
 * @date   2024-06-06
 * @brief  PEDK API about system status
 *
 */

#ifndef __STATUS_PEDKAPI_H__
#define __STATUS_PEDKAPI_H__

#define STATUS_MAX_LEN          64              ///< RE(PEDK)-system status string length

/*
 *@brief mfp status and pedk status Table
 */
typedef struct pedk_system_status_info
{
     uint32_t   sys_id;                         ///< MFP-system status ID
     char       pedk_id[STATUS_MAX_LEN];        ///< RE(PEDK)-system status ID
     char       pedk_type[STATUS_MAX_LEN];      ///< RE(PEDK)-system status type
     char       pedk_module[STATUS_MAX_LEN];    ///< RE(PEDK)-system status moudule
     char       pedk_pri[STATUS_MAX_LEN];       ///< RE(PEDK)-system status priority

}STPEDK_SYS_STATUS_INFO;

void status_event_callback(char *data, uint32_t len);

/**
 * @brief System status-PEDK API module init
 * @retval 0  init successfully
 * @retval -1 init failed
 * @autor liangshiqin
 * @date 2024-06-06
 */
int32_t status_pedkapi_init(void);

/**
 * @brief System status-PEDK API module deinit
 * @retval 0  init successfully
 * @retval -1 init failed
 * @autor liangshiqin
 * @date 2024-06-06
 */
void    status_pedkapi_deinit(void);

#endif
