#include <string.h>
#include <stdio.h>
#include "nettypes.h"
#include "wsdef.h"
#include "wsdjob.h"
#include "netjob.h"
#include "soap.h"
#include "wsdservice.h"

int wsd_strtol(const char* buf, int* out_val)
{
    char* pchar = NULL;
    int val;

    val = strtol(buf, &pchar, 0);
    if (buf[0] == '\0' || (*pchar != '\0'))
    {
        return -1;
    }
    *out_val = val;
    return 0;
}

void wsd_ticket_ref(WSD_TICKET_S* pticket)
{
    if (pticket)
    {
        ++pticket->ref_cnt;
    }
}

void wsd_ticket_unref(WSD_SVC_S* svc_type, WSD_TICKET_S* pticket)
{
    if (pticket)
    {
        --pticket->ref_cnt;
        if (pticket->ref_cnt == 0)
        {
            NET_DEBUG("ticket %d ref count == 0, destroying", pticket->job_id);
            svc_type->ticket_destroy(pticket);
        }
    }
}

void wsd_cancel_job(WSD_SVC_S* svc_type, WSD_TICKET_S* pticket)
{
    ROUTER_MSG_S    message = { .msgSender = MID_PORT_NET };
    uint32_t        count = 0;
    int             ticket_state = pticket->state;

    NET_DEBUG("job %d state: %d(%s)", pticket->job_id, ticket_state, netjob_state_string(ticket_state));
    if ( ticket_state >= NETJOB_STATE_PROCESSING && ticket_state < NETJOB_STATE_CANCELED )
    {
        message.msgType = MSG_CTRL_JOB_CANCEL;
        message.msg1    = pticket->job_id;
        message.msg2    = 0;
        message.msg3    = NULL;
        task_msg_send_by_router(MID_SYS_JOB_MGR, &message);
    }
    pticket->state = NETJOB_STATE_CANCELED;
    //wsd_ticket_unref(svc_type, pticket);
}

int WSDcreateFaultResponse(WSD_SERVICE_DATA_S* srv_data, const char* code, const char* subcode_xmlns, const char* subcode, const char* reason)
{
    int rc, count, len;
    char *pchar;
    char buf[WSD_MAX_ENVELOPE_SIZE];

    rc = 0;
    len = count = 0;
    pchar = strstr(srv_data->soap_header, "<wsa:Action>");
    if (pchar)
    {
        pchar += strlen("<wsa:Action>");
        len = pchar - srv_data->soap_header;
        pchar = strstr(pchar, "</wsa:Action>");
        if (pchar)
        {
            count = sizeof(srv_data->soap_header) - len;
            memcpy(buf, pchar, strlen(pchar));
            snprintf(srv_data->soap_header + len, count, "%s%s",
                "http://schemas.xmlsoap.org/ws/2004/08/addressing/fault",
                pchar);
        }
    }

    // format response
    //
    rc = soap_format_fault(
                WSD_NAME_SPACES,
                srv_data->soap_header,
                code,
                subcode_xmlns,
                subcode,
                reason,
                srv_data->soap_reply,
                sizeof(srv_data->soap_reply)
                );
    NET_DEBUG("WSD FAULT REPLY:\ncode:%s, subcode:%s, reason:%s\n", code, subcode, reason);
    if (rc)
    {
        NET_ERROR("And can't even format a response\n");
    }
    return rc;
}

int wsd_reply_with_fault(HTTP_TASK_S* ptask, WSD_SERVICE_DATA_S* srv_data, const char* code, const char* subcode_xmlns, const char* subcode, const char* reason)
{
    const char*     reply_code = http_status_string(200);
    int32_t         reply_len = 0;
    const char*     reply_type = MIME_TYPE_SOAP_XML;

    if ( WSDcreateFaultResponse(srv_data, code, subcode_xmlns, subcode, reason) )
    {
        NET_WARN("And can't even format a response\n");
        return -1;
    }
    reply_len = strlen(srv_data->soap_reply);
    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        http_task_send(ptask, srv_data->soap_reply, reply_len);
    }
    return 0;
}

int wsd_reply_with_soap_content(HTTP_TASK_S* ptask, WSD_SERVICE_DATA_S* srv_data)
{
    const char*     reply_code = http_status_string(200);
    int32_t         reply_len = 0;
    const char*     reply_type = MIME_TYPE_SOAP_XML;

    reply_len = strlen(srv_data->soap_reply);

    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        http_task_send(ptask, srv_data->soap_reply, reply_len);
    }
    return 0;
}

ACTION_HANDLER_DEFINITION(wsd_srvcomm_probe_handler)
{
    NET_DEBUG("m_src:\n%s\n", pxml->m_src);
    return 0;
#if 0
    PPRBMATCH pMatch;
    int rc;
    struct in6_addr ip;

    // parse out the particulars of the probe with the regular
    // discovery xml parser
    //
    rc = WSDparseDiscoveryXML(pdev, pxml->m_src, IPV4, &ip, 0, &pMatch);
    if (rc)
    {
        NET_DEBUG("No probe in Probe!\n");
        return 0;
    }
    if (! pMatch)
    {
        NET_DEBUG("No probematch for Probe\n");
        return 0;
    }
    // format the reply as if we'll send it udp but don't send it
    // the SOAP goes right into the buffer supplied to this call
    //
    rc = WSDprobeMatch(pdev, ps, pMatch, srv_data->soap_reply, sizeof(srv_data->soap_reply));

    WSDfreeMatch(pdev, pMatch);

    return rc;
#endif
}

ACTION_HANDLER_DEFINITION(wsd_srvcomm_get_job_elements_handler)
{
    QXML_S xml;
    SOAP_VAR_S *pResponse, *pVar, *pdata;
    WSD_TICKET_S* pticket = NULL;
    char *pe, *pv;
    char  job_idString[64] = {0};
    int   job_id;
    char  req[128] = {0};
    int   rc, parseLevel;
    int   requests;
    int   valid_element;
    char  buf[128] = {0};

    NET_DEBUG("Handling GetJobElementsRequest\n");

    rc = 1;
    pResponse = NULL;
    pVar = NULL;
    pxml = NULL;

    do // TRY
    {
        QXMLparserSyncTo(pxml, &xml);
        strcpy(job_idString, "-1");

        rc = soap_get_var_value_from_xml(
                pxml,
                "job_id",
                job_idString, sizeof(job_idString)
                );
        if (rc)
        {
            NET_DEBUG("No job ID\n");
            rc = 0; // let this one go, next will fail
        }
        job_id = strtol(job_idString, NULL, 0);
        pticket = wsd_ticket_get_from_job_id(svc_type, job_id);
        if (! pticket)
        {
            NET_DEBUG("No job for element\n");

            rc = soap_format_fault(
                    WSD_NAME_SPACES,
                    srv_data->soap_header,
                    WSD_SOAP_RECEIVER,
                    svc_type->xmlns,
                    "OperationFailed",
                    "No Such Job",
                    srv_data->soap_reply,
                    sizeof(srv_data->soap_reply)
                    );
            if (rc)
            {
                NET_ERROR("And can't even format a response\n");
                break;
            }
            rc = 0;
            break;
        }
        // parse forward to requested elements
        //
        rc = soap_find_var_in_xml(pxml, "GetJobElementsRequest.RequestedElements");
        if (rc)
        {
            NET_ERROR("No requested elements\n");
            break;
        }
        // parse the requested elements, and add replies for
        // each element in the request
        //
        pe = QXMLchildElement(pxml);
        parseLevel = pxml->m_level;
        requests = 0;

        rc = 0;

        while (pe && parseLevel <= pxml->m_level && rc == 0)
        {
            if (! QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Name"))
            {
                pv = QXMLnextValue(pxml);
                if (! pv)
                {
                    NET_ERROR("No value for Name\n");
                    rc = -3;
                    break;
                }
                requests++;
                QXMLvalueCopy(pxml, req, pv, sizeof(req));

                pVar = NULL;
                pdata = NULL;
                valid_element = 0;

                NET_DEBUG("GetJobElement: %s\n", QXMLelementName(pxml, req));
                rc = svc_type->get_job_elements_handler(pticket, pxml, &pdata, &valid_element);
                if (valid_element)
                {
                    char elename[128];
                    snprintf(buf, sizeof(buf), "%s:ElementData", svc_type->xmlns);
                    // format element data
                    snprintf(elename, sizeof(elename), "%s:%s", svc_type->xmlns, QXMLelementName(pxml, req));

                    pVar = soap_create_var(buf,dtchar, 0, "", 2, "Name", elename, "Valid", "true");
                    if (! pVar)
                    {
                        NET_ERROR("Can't create element data\n");
                        rc = -1;
                        break;
                    }
                    pVar->m_child = soap_create_var(elename, dtchar, 0, NULL, 0);
                    if (! pVar->m_child)
                    {
                        NET_ERROR("Can't create element data\n");
                        rc = -1;
                        break;
                    }
                    // put any actual element data under this
                    //
                    pVar->m_child->m_child = pdata;
                    rc = 0;
                }
                else
                {
                    NET_WARN("Unhandled JobElement %s\n", QXMLelementName(pxml, req));

                    snprintf(buf, sizeof(buf), "%s:ElementData", svc_type->xmlns);
                    pVar = soap_create_var( buf, dtchar, 0, NULL, 2, "Name", req, "Valid", "false");
                }
                if (pVar)
                {
                    if (pResponse && pResponse->m_child)
                    {
                        SOAP_VAR_S* pvx;

                        // enlist the new element on the reply list
                        // as sibling of last element
                        //
                        for (pvx = pResponse->m_child; pvx->m_next;)
                        {
                            pvx = pvx->m_next;
                        }
                        pvx->m_next = pVar;
                    }
                    else
                    {
                        if (pResponse)
                        {
                            // can't happen, but...
                            soap_delete_var(pVar);
                        }
                        // create the element list and set element as first child
                        //
                        snprintf(buf, sizeof(buf), "%s:JobElements", svc_type->xmlns);
                        pResponse = soap_create_var( buf, dtchar, 0, "", 0);
                        if (! pResponse)
                        {
                            soap_delete_var(pVar);
                            rc = -1;
                            break;
                        }
                        pResponse->m_child = pVar;
                    }
                }
            }
            pe = QXMLnextElement(pxml);
        }
        if (rc)
        {
            break;
        }
        if (requests <= 0)
        {
            NET_ERROR("No elements in requested elements\n");
            rc = -3;
            break;
        }
        // format response
        //
        rc = soap_format_response(
                pResponse,
                WSD_NAME_SPACES,
                "GetJobElementsResponse",
                srv_data->soap_header,
                svc_type->xmlns,
                svc_type->xmlns_url,
                srv_data->soap_reply,
                sizeof(srv_data->soap_reply)
                );
        if (rc)
        {
            NET_ERROR("Can't format SOAP\n");
        }
    }
    while (0); // CATCH

    if(pResponse)
    {
        soap_delete_var(pResponse);
    }
    if (pxml)
    {
        QXMLparserDelete(pxml);
    }
    return rc;
}

ACTION_HANDLER_DEFINITION(wsd_srvcomm_get_active_job_handler)
{
    SOAP_VAR_S* pResponse, *pv, *px; //pvv;
    WSD_TICKET_S* pticket = NULL;
    char  buf[128] = {0};
    int rc = -1;

    NET_DEBUG("Handling GetActiveJobsRequest\n");

    do  // TRY
    {
        snprintf(buf, sizeof(buf), "%s:ActiveJobs", svc_type->xmlns);
        pResponse = soap_create_var(buf, dtchar, 0, NULL, 0);
        BREAK_IF (! pResponse, NET_WARN);

        rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "GetActiveJobsResponse",
                        srv_data->soap_header,
                        svc_type->xmlns,
                        svc_type->xmlns_url,
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
    }
    while(0); // CATCH

    if (pResponse)
    {
        soap_delete_var(pResponse);
    }

    return rc;
}

ACTION_HANDLER_DEFINITION(wsd_srvcomm_get_job_history_handler)
{
#if 0
    SOAP_VAR_S* pResponse, pv, px;
    PWSDPRINTTICKET pprtticket;
    PWSDSCANTICKET  pscnticket;
    int job_id, prevjob_id;
    int jobcount;
    int rc;

    NET_DEBUG("Handling GetJobHistoryRequest\n");

    rc = 0;

    do  // TRY
    {
#if CONFIG_SCAN
        pResponse = soap_create_var((ps->type == WSD_SVC_SCAN) ? "wscn:JobHistory" : "wprt:JobHistory", dtchar, 0, NULL, 0);
#else
        pResponse = soap_create_var("wprt:JobHistory", dtchar, 0, NULL, 0);
#endif
        if (! pResponse)
        {
            rc = -1;
            break;
        }
        // iterate through any wsd print-ticket jobs in the jobs list
        //
        prevjob_id = 0;
        pv = NULL;
        jobcount = 0;
        do
        {
            pprtticket = NULL;
            pscnticket = NULL;
			/* TODO JOB_STATUS_DEPENDING */
            //JOBgetNextJob(prevjob_id, &job_id);//FIX DXS
            if (job_id > 0)
            {
                uint32_t typeTag = 0;
                void  *userData = NULL;

                prevjob_id = job_id;

                // there could be a lot of jobs only some of which are WSD
                //
				/* TODO JOB_STATUS_DEPENDING */
                if ( 0 )//JOBgetUserData(job_id, &typeTag, &userData))//FIX DXS
                {
                    // no user data for this job, not an IPP job
                    job_id = 0;
                }
                else if (userData)
                {
                    if (ps->type == WSD_SVC_PRINT && typeTag == WSD_PRINT_TAG)
                    {
                        pprtticket = (PWSDPRINTTICKET)userData;
                    }
#if CONFIG_SCAN
                    else if (ps->type == WSD_SVC_SCAN && typeTag == WSD_SCAN_TAG)
                    {
                        pscnticket = (PWSDSCANTICKET)userData;
                    }
#endif
                    else
                    {
                        // not a WSD printer/scanner job type
                        //
                        job_id = 0;
                    }
                }
                else
                {
                    job_id = 0;
                }
            }
            else
            {
                prevjob_id = -1; // no more, stop looping
            }
            if (job_id > 0 && (pprtticket || pscnticket))
            {
                jobcount++;
                if (jobcount > WSD_MAX_JOB_HISTORY)
                {
                    NET_DEBUG("stopping job iteration at %d jobs\n", jobcount);
                    break;
                }
                NET_DEBUG("Add Job Id %d to job history reponse\n", job_id);
#if CONFIG_SCAN
                px = soap_create_var((ps->type == WSD_SVC_SCAN) ? "wscn:JobSummary" : "wprt:JobSummary", dtchar, 0, NULL, 0);
#else
                px = soap_create_var("wprt:JobSummary", dtchar, 0, NULL, 0);
#endif
                if (! px)
                {
                    rc = -1;
                    break;
                }
                if (pv)
                {
                    pv->m_next = px;
                    pv = pv->m_next;
                }
                else
                {
                    pResponse->m_child = px;
                    pv = pResponse->m_child;
                }

#if CONFIG_SCAN
                if (ps->type == WSD_SVC_SCAN)
                {
                    rc = WSDscanJobInfoVar(ps, &px, job_id, 0, 1);
                }
                else
#endif
                {
                    rc = WSDprinterJobInfoVar(ps, &px, job_id, 0, 1);
                }
                if (rc)
                {
                    break;
                }
                pv->m_child = px;
            }
        }
        while (rc == 0 && prevjob_id > 0);

        if (rc)
        {
            NET_ERROR("Can't add job to reponse\n");
            soap_delete_var(pResponse);
            break;
        }
        // format response
        //
        rc = soap_format_response(
                        WSD_NAME_SPACES,
                        "GetJobHistoryResponse",
                        srv_data->soap_header,
#if CONFIG_SCAN
                        (ps->type == WSD_SVC_SCAN) ? "wscn" : "wprt",
                        (ps->type == WSD_SVC_SCAN) ? "http://schemas.microsoft.com/windows/2006/08/wdp/scan" : "http://schemas.microsoft.com/windows/2006/08/wdp/print",
#else
                        "wprt",
                        "http://schemas.microsoft.com/windows/2006/08/wdp/print",
#endif
                        pResponse,
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
        soap_delete_var(pResponse);
    }
    while(0); // CATCH

    return rc;
#endif
    return 0;
}

ACTION_HANDLER_DEFINITION(wsd_srvcomm_set_event_rate_handler)
{
    char rate_str[32];
    int rate;
    int rc;

    NET_DEBUG("Handling SetEventRateRequest\n");

    rc = 1;

    do  // TRY
    {
        // find even rate
        //
        rc = soap_get_var_value_from_xml( pxml, "EventRate", rate_str, sizeof(rate_str));
        if (rc)
        {
            NET_DEBUG("No EventRate\n");
            break;
        }
        BREAK_IF(wsd_strtol(rate_str, &rate), NET_WARN);

        rc = soap_format_response(
                        NULL,
                        WSD_NAME_SPACES,
                        "SetEventRateResponse",
                        srv_data->soap_header,
                        svc_type->xmlns,
                        svc_type->xmlns_url,
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );

        svc_type->event_rate = rate;
    }
    while(0);

    return rc;
}

ACTION_HANDLER_DEFINITION(wsd_srvcomm_cancel_job_handler)
{
    WSD_TICKET_S* pticket;
    const char *fault_code   = WSD_SOAP_RECEIVER;
    const char *fault_sucode = "OperationFailed";
    const char *fault_reason = "Busy";
    char job_idString[64];
    int job_id;
    int rc;

    NET_DEBUG("Handling CancelJobRequest\n");
    rc = 1;
    do  // TRY
    {
        // find job ID
        rc = soap_get_var_value_from_xml(pxml, "JobId", job_idString, sizeof(job_idString));
        if (rc || wsd_strtol(job_idString, &job_id))
        {
            NET_WARN("invalid job ID; %s", job_idString);
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "InvalidArgs";
            fault_reason = "Jod ID format is not correct";
            break;
        }
        pticket = wsd_ticket_get_from_job_id(svc_type, job_id);
        if (pticket == NULL)
        {
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "ClientErrorJobIdNotFound";
            fault_reason = "InvalidArgs";
            break;
        }
        wsd_cancel_job(svc_type, pticket);

        rc = soap_format_response(
                NULL,
                WSD_NAME_SPACES,
                "CancelJobResponse",
                srv_data->soap_header,
                svc_type->xmlns,
                svc_type->xmlns_url,
                srv_data->soap_reply,
                sizeof(srv_data->soap_reply)
                );
        return rc;
    }
    while(0); // CATCH

    if (rc)
    {
        rc = WSDcreateFaultResponse(srv_data, fault_code, svc_type->xmlns, fault_sucode, fault_reason);
        if (rc)
        {
            NET_WARN("Can't even format a response\n");
        }
    }
    return rc;
}

int32_t wsd_process_service(WSD_SVC_S* svc_type, HTTP_TASK_S* ptask, char* req_body, WSD_SERVICE_DATA_S* srv_data)
{
    char            *soap_action;
    char            *pdata;
    char            *pe, *pv;
    QXML_S*         pxml = NULL;

    char            action[128] = {0};
    char            msg_id [64] = {0};
    char            identifier[128] = {0};
    char            element[64] = {0};
    char            msgbuf[64] = {0};
    char            hostname[HOSTNAME_LEN];
    int             action_match;

    int32_t         ret = -1;
    const char*     reply_code = http_status_string(200);
    int32_t         reply_len = 0;
    const char*     reply_type = MIME_TYPE_SOAP_XML;

    pxml = wsd_qxml_parser(req_body);
    if (pxml == NULL)
    {
        NET_WARN("Can't create XML parser\n");
        return -1;
    }

    do {
        SOAP_CHILD_ELEM_S child_elems[] = {
            { "Action", action, sizeof(action), dtstring},
            { "MessageID", msg_id, sizeof(msg_id), dtstring},
            { "Identifier", identifier, sizeof(identifier), dtstring},
        };
        BREAK_IF(soap_find_child_elements(pxml, "Envelope.Header", child_elems, ARRAY_SIZE(child_elems)) != 0, NET_WARN);

        wsd_generate_uuid(msgbuf, sizeof(msgbuf));
        snprintf(srv_data->soap_header, sizeof(srv_data->soap_header), SOAP_RESPONSE_HEADER, action, msgbuf, msg_id);

        // get just the last string in action header
        pe = strrchr(action, '/');
        soap_action = pe ? ++pe : action;
        NET_DEBUG("soap_action: %s", soap_action);

        if (wsd_is_subscription_action(soap_action))
        {
            ret = wsd_do_subscription(pxml, soap_action, identifier, srv_data, svc_type->subs_list);
        }
        else
        {
            action_match = 0;
            for(ACTION_HNDL_S* handler = svc_type->action_hndl_tbl; handler->action; ++handler)
            {
                if ( strcmp(soap_action, handler->action) == 0 )
                {
                    ret = handler->handle(svc_type, ptask, pxml, srv_data, (char**)&reply_code);
                    NET_DEBUG("action: %s handler %s", soap_action, ret == 0 ? "success" : "fail" );
                    action_match = 1;
                    break;
                }
            }

            if (action_match == 0)
            {
                NET_DEBUG("Unknown Request: %s", action);
                ret = soap_format_fault(WSD_NAME_SPACES, srv_data->soap_header, WSD_SOAP_SENDER, WSA_XMLNS, "ActionNotSupported",
                                        "The [wsa:action] cannot be processed at the receiver",
                                         srv_data->soap_reply, sizeof(srv_data->soap_reply));
                if (ret)
                {
                    NET_WARN("response even format failed");
                }
            }
        }

    }
    while (0);

    if (pxml)
    {
        QXMLparserDelete(pxml);
        pxml = NULL;
    }

    if (srv_data->http_finished)
    {
        return 0;
    }

    if (ret)
    {
        reply_len = snprintf(srv_data->soap_reply, sizeof(srv_data->soap_reply), "<html><body><h2>Failed</h2></body></html>");
        reply_type = MIME_TYPE_HTML;
        reply_code = http_status_string(501);
    }
    else
    {
        reply_len = strlen(srv_data->soap_reply);
    }

    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        http_task_send(ptask, srv_data->soap_reply, reply_len);
    }

    return 0;
}
