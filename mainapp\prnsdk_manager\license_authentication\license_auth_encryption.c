/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file license_authentication.h
 * @addtogroup license_authentication
 * @{
 * @addtogroup license_authentication
 * @brief Data storage frame layer for encryption and decryption functionalities.
 *
 * This file provides various cryptographic operations including:
 * - AES symmetric encryption and decryption
 * - RSA asymmetric encryption and decryption
 * - File signature generation and verification
 * - Base64 encoding and decoding
 * - Decrypting cJSON objects
 *
 * All functions aim to support a secure software license authentication framework.
 */

#include "license_auth_encryption.h"

/* Base64 encoding table */
const char *const LICENSE_AUTH_BASE64_TABLE = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

/**
 * @brief Decrypt a file using an AES key.
 *
 * This function uses the specified AES key and IV to decrypt a given input file.
 *
 * @param[in] infile The encrypted input file path.
 * @param[in] aeskey Path to the AES key file or a string containing the key.
 * @param[out] outfile The decrypted output file path.
 * @return Returns the path to the decrypted file, or NULL on failure.
 */
static char *license_auth_aes_decrypt_file(const char *infile, const char *aeskey, const char *outfile) {
    if (!infile || !aeskey || !outfile)
    {
        LICENSE_AUTH_ERROR_NULL; // 错误处理：参数为空
    }

    char *key_string = license_auth_get_string(aeskey);

    if (!key_string)
    {
        LICENSE_AUTH_ERROR_NULL; // 错误处理：获取 AES 密钥失败
    }

    pi_runcmd(NULL, 0, 0, "openssl-sm enc -d %s -K %s -iv %s -in %s -out %s", LICENSE_AUTH_AES_ALGORITHM, key_string, LICENSE_AUTH_IV, infile, outfile);

    LICENSE_AUTH_SAFE_FREE(key_string);

    return (char *)outfile;
}

/**
 * @brief Encrypt a file using an RSA public key.
 *
 * Encrypts the content of the specified input file using the given RSA public key and outputs the encrypted content to a file.
 *
 * @param keyfile Path to the input file to be encrypted.
 * @param pubkey Path to the RSA public key file.
 * @param outfile Path to the output file for the encrypted content.
 * @return Returns the path to the encrypted file, or NULL on failure.
 */
static char *license_auth_rsa_encrypt_file(const char *keyfile, const char *pubkey, const char *outfile)
{
    const char *result = NULL;
    if (keyfile == NULL || pubkey == NULL || outfile == NULL)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    // 选择加密命令，根据是否是测试环境动态切换
    result = pi_runcmd(NULL, 0, 0, "openssl-sm rsautl -encrypt -pubin -inkey %s -in %s -out %s", pubkey, keyfile, outfile);
    if (strcmp(result, "OK") != 0)
    {
        pi_log_d("pi_runcmd failed: %s\n", result);
        LICENSE_AUTH_ERROR_NULL;
    }

    return (char *)outfile;
}

/**
 * @brief Decrypt a file using an RSA private key.
 *
 * Decrypts the content of the specified input file using the given RSA private key and outputs the decrypted content to a file.
 *
 * @param[in] infile The encrypted input file path.
 * @param[in] pri_key Path to the RSA private key file.
 * @param[out] outfile The decrypted output file path.
 * @return Returns the path to the decrypted file, or NULL on failure.
 */
static char *license_auth_rsa_decrypt_file(const char *infile, const char *pri_key, const char *outfile) {
    if (!infile || !pri_key || !outfile)
    {
        LICENSE_AUTH_ERROR_NULL; // 错误处理：参数为空
    }

    pi_runcmd(NULL, 0, 0, "openssl-sm rsautl -decrypt -inkey %s -in %s -out %s", pri_key, infile, outfile);

    return (char *)outfile;
}

/**
 * @brief Generate a signature file using an RSA private key.
 *
 * Signs the specified input file using the given RSA private key and outputs the signature to a file.
 *
 * @param input_file Path to the input file to be signed.
 * @param private_key Path to the RSA private key file.
 * @param signature_file Path to the output file for the signature.
 * @return Returns the path to the signature file, or NULL on failure.
 */
static char *license_auth_sign_file(const char *input_file, const char *private_key, const char *signature_file)
{
    if (input_file == NULL || private_key == NULL || signature_file == NULL)
    {
        LICENSE_AUTH_ERROR_NULL; // 参数无效
    }

    pi_runcmd(NULL, 0, 0, "openssl-sm dgst -sign %s -sha256 -out %s %s", private_key, signature_file, input_file);

    return (char *)signature_file;
}

/**
 * @brief Convert a file to Base64 format.
 *
 * Encodes the content of the specified input file into Base64 format and outputs it to another file.
 *
 * @param input_file Path to the input file to be encoded.
 * @param output_file Path to the output file for the Base64-encoded content.
 * @return Returns the path to the Base64-encoded file, or NULL on failure.
 */
static char *license_auth_encrypt_to_base64(const char *input_file, const char *output_file)
{
    if (input_file == NULL || output_file == NULL)
    {
        LICENSE_AUTH_ERROR_NULL; // 输入参数检查
    }

    const char *result = NULL;
    result = pi_runcmd(NULL, 0, 0, "openssl enc -base64 -in %s -out %s", input_file, output_file);
    if (strcmp(result, "OK") != 0)
    {
        pi_log_d("Error: Failed to encrypt file to Base64 format. Command returned %s.\n", result);
        return NULL;
    }
    return (char *)output_file;
}

/**
 * @brief Decode a Base64-encoded file.
 *
 * Decodes the content of the specified Base64-encoded input file into its original binary format and outputs it to another file.
 *
 * @param input_file Path to the Base64-encoded input file.
 * @param output_file Path to the output file for the decoded content.
 * @return Returns the path to the decoded file, or NULL on failure.
 */
static char *license_auth_decrypt_base64(const char *input_file, const char *output_file)
{
    if (input_file == NULL || output_file == NULL)
    {
        LICENSE_AUTH_ERROR_NULL; // 输入参数检查
    }

    const char *result = NULL;
    result = pi_runcmd(NULL, 0, 0, "openssl enc -base64 -d -in %s -out %s", input_file, output_file);
    if (strcmp(result, "OK") != 0)
    {
        pi_log_d("Error: Failed to decode Base64 file. Command returned %s.\n", result);
        return NULL;
    }
    return (char *)output_file;
}

/**
 * @brief Decrypt a cJSON object.
 *
 * Verifies the signature of a cJSON object, decrypts its fields, and returns the resulting cJSON object.
 * Supports both symmetric (AES) and hybrid (RSA + AES) decryption methods.
 *
 * @param[in] input_json Pointer to the cJSON object to be decrypted.
 * @param[in] private_key Path to the RSA private key file for hybrid decryption, or NULL for symmetric decryption.
 * @return Returns the decrypted cJSON object, or NULL on failure.
 */
cJSON *license_auth_decrypt_cjson(cJSON *input_json, const char *private_key)
{
    if (input_json == NULL)
    {
        LICENSE_AUTH_ERROR_NULL; // 输入 cJSON 为空
    }

    // 提取公钥字段并保存到文件
    if (license_auth_get_item_file_from_object(input_json, LICENSE_AUTH_PUBKEY_FIELD, 0, LICENSE_AUTH_TMP_PUBKEY) == NULL)
    {
        LOG_ERROR_NULL("Error: Failed to extract public key field.");
    }

    // 解码并写入签名文件
    if (license_auth_decode_base64_to_file(license_auth_get_string_from_object(input_json, LICENSE_AUTH_SIGN_FIELD), LICENSE_AUTH_TMP_DE_SIGN) == NULL)
    {
        LOG_ERROR_NULL("Error: Failed to decode signature field.");
    }
    cJSON_DeleteItemFromObject(input_json, LICENSE_AUTH_SIGN_FIELD);

    // 写入签名数据到文件
    if (license_auth_write_cjson_to_file(input_json, LICENSE_AUTH_TMP_SIGNED) == NULL)
    {
        LOG_ERROR_NULL("Error: Failed to write signed data to file.");
    }

    // 验证签名
    if (license_auth_verify_sign(LICENSE_AUTH_TMP_SIGNED, LICENSE_AUTH_TMP_DE_SIGN, LICENSE_AUTH_TMP_PUBKEY) != 0)
    {
        LOG_ERROR_NULL("Error: Signature verification failed.");
    }

    // 解码 License 和 AES 密钥
    if (license_auth_decode_base64_to_file(license_auth_get_string_from_object(input_json, LICENSE_AUTH_LICENSE_FIELD), LICENSE_AUTH_TMP_EN_MAIN) == NULL ||
        license_auth_decode_base64_to_file(license_auth_get_string_from_object(input_json, LICENSE_AUTH_AESKEY_FIELD), LICENSE_AUTH_TMP_EN_AES_KEY) == NULL)
    {
        LOG_ERROR_NULL("Error: Failed to decode License or AES key.");
    }
    #if 0
    //DOTO 重复释放
    if(input_json)
    {
        pi_log_d("=========Line %d\n",__LINE__);
        LICENSE_AUTH_SAFE_DELETE_JSON(input_json);
        input_json = NULL;
    }
    #endif

    // 解密文件
    if (private_key == NULL)
    {
        // 对称解密 //DOTO 卡在这
        if (license_auth_aes_decrypt_file(LICENSE_AUTH_TMP_EN_MAIN, LICENSE_AUTH_TMP_EN_AES_KEY, LICENSE_AUTH_TMP_SDK_LICENSE) == NULL)
        {
            LOG_ERROR_NULL("Error: AES decryption failed.");
        }
    }
    else
    {
        // 非对称解密和对称解密结合
        if (license_auth_rsa_decrypt_file(LICENSE_AUTH_TMP_EN_AES_KEY, private_key, LICENSE_AUTH_TMP_DE_AES_KEY) == NULL ||
            license_auth_aes_decrypt_file(LICENSE_AUTH_TMP_EN_MAIN, LICENSE_AUTH_TMP_DE_AES_KEY, LICENSE_AUTH_TMP_SDK_LICENSE) == NULL)
        {
            LOG_ERROR_NULL("Error: RSA or AES decryption failed.");
        }
    }

    // 从文件中读取解密后的 cJSON 数据
    cJSON *decrypted_json = license_auth_get_cjson_from_file(LICENSE_AUTH_TMP_SDK_LICENSE);
    if (decrypted_json == NULL)
    {
        LOG_ERROR_NULL("Error: Failed to load decrypted JSON from file.");
    }
    pi_log_d("===%d======decrypted_json %p",__LINE__,decrypted_json);

    return decrypted_json;
}

/**
 * @brief Encrypt a file using an AES key.
 *
 * Encrypts the content of the specified input file using the given AES key and outputs the encrypted content to a file.
 *
 * @param infile Path to the input file to be encrypted.
 * @param aes_key String containing the AES encryption key (hexadecimal format).
 * @param outfile Path to the output file for the encrypted content.
 * @return Returns the path to the encrypted file, or NULL on failure.
 */
char *license_auth_aes_encrypt_file(const char *infile, const char *aes_key, const char *outfile)
{
    if (infile == NULL || aes_key == NULL || outfile == NULL)
    {
        pi_log_d("Error: Invalid parameters. infile, aes_key, and outfile must not be NULL.\n");
        return NULL;
    }

    char command[512] = {0};
    int32_t ret = snprintf(command, sizeof(command), "openssl-sm enc %s -K %s -iv %s -in %s -out %s",
             LICENSE_AUTH_AES_ALGORITHM, aes_key, LICENSE_AUTH_IV, infile, outfile);
    if (ret < 0 || ret >= sizeof(command))
    {
        pi_log_d("Error: Command buffer overflow.\n");
        return NULL;
    }
    pi_runcmd(NULL, 0, 0, command);

    return (char *)outfile;
}

/**
 * @brief Verify the signature of a file.
 *
 * Verifies the validity of a signature file against the input file using the specified RSA public key.
 *
 * @param[in] input_file Path to the input file.
 * @param[in] signature_file Path to the signature file.
 * @param[in] public_key Path to the RSA public key file.
 * @return Returns 0 on success, or -1 on failure.
 */
int32_t license_auth_verify_sign(const char *input_file, const char *signature_file, const char *public_key)
{
    if (!input_file || !signature_file || !public_key)
    {
        LICENSE_AUTH_ERROR_INT;
    }

    char command_output_buffer[128] = {0};

    pi_runcmd(command_output_buffer, sizeof(command_output_buffer), 0, "openssl-sm dgst -verify %s -sha256 -signature %s %s", public_key, signature_file, input_file);

    if (strncmp(command_output_buffer, "Verified OK", strlen("Verified OK")) == 0)
    {
        return 0; // 验签成功
    }

    return -1; // 验签失败
}

/**
 * @brief Base64 encoding function.
 *
 * Encodes binary data into Base64 format.
 *
 * @param[in] binary_data Pointer to the input binary data.
 * @param[in] binary_length Length of the binary data.
 * @param[out] base64_buffer Buffer to store the Base64-encoded data.
 * @return Returns the length of the Base64-encoded data.
 */
int32_t license_auth_base64_encode(const unsigned char *binary_data, int32_t binary_length, char *base64_buffer)
{
    if (binary_data == NULL || base64_buffer == NULL || binary_length <= 0)
    {
        LICENSE_AUTH_ERROR_INT; // 参数校验失败
    }

    unsigned char char_index = 0;
    int32_t i = 0, length = 0;

    for (i = 0, length = 0; i < binary_length; i += 3)
    {
        // 处理第一个 6 位
        char_index = (binary_data[i] >> 2) & 0x3F;
        base64_buffer[length++] = LICENSE_AUTH_BASE64_TABLE[char_index];

        // 处理第二个 6 位
        char_index = (binary_data[i] << 4) & 0x30;
        if ((i + 1) >= binary_length)
        {
            base64_buffer[length++] = LICENSE_AUTH_BASE64_TABLE[char_index];
            base64_buffer[length++] = '=';
            base64_buffer[length++] = '=';
            break;
        }

        char_index |= (binary_data[i + 1] >> 4) & 0x0F;
        base64_buffer[length++] = LICENSE_AUTH_BASE64_TABLE[char_index];

        // 处理第三个 6 位
        char_index = (binary_data[i + 1] << 2) & 0x3C;
        if ((i + 2) >= binary_length)
        {
            base64_buffer[length++] = LICENSE_AUTH_BASE64_TABLE[char_index];
            base64_buffer[length++] = '=';
            break;
        }

        char_index |= (binary_data[i + 2] >> 6) & 0x03;
        base64_buffer[length++] = LICENSE_AUTH_BASE64_TABLE[char_index];

        // 处理第四个 6 位
        char_index = binary_data[i + 2] & 0x3F;
        base64_buffer[length++] = LICENSE_AUTH_BASE64_TABLE[char_index];
    }

    return length;
}

/**
 * @brief Base64 decoding function.
 *
 * Decodes Base64-encoded data into binary format.
 *
 * @param[in] base64_buffer Pointer to the Base64-encoded input data.
 * @param[out] binary_data Buffer to store the decoded binary data.
 * @return Returns the length of the decoded binary data.
 */
int32_t license_auth_base64_decode(char *base64_buffer, unsigned char *binary_data)
{
    if (base64_buffer == NULL || binary_data == NULL)
    {
        LICENSE_AUTH_ERROR_INT; // 参数校验失败
    }

    int32_t i = 0, length = 0;
    unsigned char char_index = 0;
    unsigned char base64_values[4] = {0}; // 改为更具语义的名称

    while (base64_buffer[i] != '\0')
    {
        memset(base64_values, 0xFF, sizeof(base64_values));

        for (char_index = 0; char_index < 64; char_index++)
        {
            if (LICENSE_AUTH_BASE64_TABLE[char_index] == base64_buffer[i])
            {
                base64_values[0] = char_index;
            }
            if (LICENSE_AUTH_BASE64_TABLE[char_index] == base64_buffer[i + 1])
            {
                base64_values[1] = char_index;
            }
            if (LICENSE_AUTH_BASE64_TABLE[char_index] == base64_buffer[i + 2])
            {
                base64_values[2] = char_index;
            }
            if (LICENSE_AUTH_BASE64_TABLE[char_index] == base64_buffer[i + 3])
            {
                base64_values[3] = char_index;
            }
        }

        binary_data[length++] = ((base64_values[0] << 2) & 0xFC) | ((base64_values[1] >> 4) & 0x03);
        if (base64_buffer[i + 2] == '=')
        {
            break;
        }

        binary_data[length++] = ((base64_values[1] << 4) & 0xF0) | ((base64_values[2] >> 2) & 0x0F);
        if (base64_buffer[i + 3] == '=')
        {
            break;
        }

        binary_data[length++] = ((base64_values[2] << 6) & 0xC0) | (base64_values[3] & 0x3F);

        i += 4;
    }

    return length;
}




/**
 *@}
 */
