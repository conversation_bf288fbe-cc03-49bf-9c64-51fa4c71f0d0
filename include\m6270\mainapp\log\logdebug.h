/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file logdebug.h
 * @addtogroup utilities
 * @{
 * @addtogroup logdebug
 * <AUTHOR>
 * @date 2023-06-21
 * @brief log function startup module
 *
 */
#ifndef __LOG_DEBUG_H__
#define __LOG_DEBUG_H__

/*
 @brief debug log module start up
 @param[in] void void valude
 @return started successfully or started failed
 @retval 0 started successfully
 @retval -1 started failed
 <AUTHOR>
 @date 2023-06-21
*/
int32_t debug_prolog(void);

#endif
/**
 *@}
 */
