/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file websrv_fwupgrade.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2024-7-3
 * @brief WEB Server for firmware upgrade
 */

#include "nettypes.h"
#include "netctx.h"
#include "netsts.h"
#include "websrv_private.h"
#include "ringbuf.h"

#define MULTIPART_FORM                  "multipart/form-data"
#define CONTENT_TYPE_OCTET_STREAM       "Content-Type: application/octet-stream\r\n\r\n"
#define HEADER_CONTENT_TYPE             "content-type"
#define LEN(_chars)                     (sizeof(_chars) - 1)
#define BODY_LEAST_LEN                  2048
#define CRLF2                           "\r\n\r\n"
#define QIO_RING_BUF_LEN                (TCP_CHUNK_SIZE * 16)
#define MSLEEP_DURATION                 200
#define SECOND                          1000
#define SECOND                          1000
#define UPGRADE_UPDATE_DOWNING_FAILED_REBOOT  3   //copy from upgrade.c

#define UPGRADE_FILE_PREAMBLE           "\xf5\xd4\xc3\xb2"
#define ACL_HEAD                        "\x1b%-12345X@PJL ENTER LANGUAGE=ACL\xD\xA"
#define ACL_UPGRADE                     "\x00\xac\x00\x0f\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00"

#define HTTP_SYSUPGRADE_OK              -17
#define HTTP_SYSUPGRADE_FAIL            -18

#define UPGRADE_TRY_LOCK()             \
     ({                                 \
        typeof(s_fwupgrade_ctx.flg_upgrading) _val = 0;                         \
        __atomic_compare_exchange_n(&s_fwupgrade_ctx.flg_upgrading, (void *)&_val, 1, false, __ATOMIC_ACQUIRE, __ATOMIC_RELAXED);   \
    })

#define UPGRADE_UNLOCK()                __atomic_store_n(&s_fwupgrade_ctx.flg_upgrading, 0, __ATOMIC_RELAXED)

#define ATOMIC_TRY_LOCK(_val)             \
     ({                                 \
        __typeof__(_val) _expected = 0;                         \
        __atomic_compare_exchange_n(&_val, (void *)&_expected, 1, false, __ATOMIC_ACQUIRE, __ATOMIC_RELAXED);   \
    })

#define ATOMIC_UNLOCK(_val)                __atomic_store_n(&_val, 0, __ATOMIC_RELAXED)

typedef struct websrv_fwupgrade_private
{
    uint32_t            ntotal_written;
    QIO_S*              pqio;
} FWUPGRADE_PRIV_S;

typedef struct websrv_fwupgrade_context
{
    NET_CTX_S*          net_ctx;
    volatile uint8_t    flg_download_failed;
    volatile uint8_t    flg_upgrading;
    volatile uint8_t    eventfd_finished;     // FIXME: wait/notify machanism for fwupgrade finished
} WEBSRV_FWGRADE_CTX;

#define GET_FIELD(p, field)       ((p && p->field) ? p->field : NULL)

static WEBSRV_FWGRADE_CTX s_fwupgrade_ctx;

static void websrv_fwupgrade_destroy(void* user_data)
{
    FWUPGRADE_PRIV_S* priv = (FWUPGRADE_PRIV_S*)user_data;
    pi_free(priv);
}

static int32_t websrv_rbuf_write_all(RING_BUF_S* rbuf, const char* buffer, size_t count)
{
    size_t      wtotal = 0;
    int32_t     nwrite = 0;

    while ( wtotal < count )
    {
        nwrite = ringbuf_write(rbuf, buffer + wtotal, count - wtotal);
        if ( nwrite > 0 )
        {
            wtotal += nwrite;
            continue;
        }
        NET_DEBUG("ring buffer is full, write waiting for %d milliseconds to retry", SECOND);
        pi_msleep(SECOND);
    }

    return wtotal;
}

static int32_t websrv_upgrade_firmware(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    int http_status;

    RETURN_VAL_IF(priv == NULL, NET_WARN, 0);

    *rcode = http_status_string(200);
    http_status = HTTP_SYSUPGRADE_OK;
#if 0
    http_status = HTTP_SYSUPGRADE_FAIL;
#define RETRY_TIME 3600  // wwait for one hour

    for(size_t i = 0; i < RETRY_TIME; ++i)   // wait for websrv_qio_close to be called
    {
        if (s_fwupgrade_ctx.eventfd_finished)
        {
            s_fwupgrade_ctx.eventfd_finished = 0;
            http_status = HTTP_SYSUPGRADE_OK;
            break;
        }
        pi_msleep(SECOND);
    }
#endif

    return snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Result':'%d'}", http_status);
}

static int32_t websrv_qio_close(QIO_S* pqio)
{
    RETURN_VAL_IF(pqio == NULL, NET_WARN, -1);
    ringbuf_destroy(pqio->priv);
    s_fwupgrade_ctx.eventfd_finished = 1;
    ATOMIC_UNLOCK(s_fwupgrade_ctx.flg_upgrading);
    return 0;
}

static int32_t websrv_qio_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    int32_t   block = 0;
    int32_t   ms = 0;
    int32_t   rc = 0;

    RING_BUF_S* rbuf = GET_FIELD(pqio, priv);

    RETURN_VAL_IF(rbuf == NULL, NET_WARN, -1);

    if ( what & QIO_POLL_READ ) /* 可读检测时，判断rbuf中是否有可读数据 */
    {
        if ( tos > 0 || (tos == 0 && tous >= 0) )
        {
            ms += (tos  > 0 ? (tos  * 1000) : 0);
            ms += (tous > 0 ? (tous / 1000) : 0);
        }
        else
        {
            block = 1;
        }

        do
        {
            rc = ringbuf_readable(rbuf);
            if ( rc != 0 )
            {
                break;
            }
            pi_msleep(MSLEEP_DURATION);
            ms -= MSLEEP_DURATION;
        }
        while ( ms > 0 || block );
    }
    else
    {
        NET_WARN("unsupported operation: %d", what);
        rc = -1;
    }

    return rc;
}

static int32_t websrv_qio_read(QIO_S* pqio, void* buf, size_t nbuf)
{
    RING_BUF_S* rbuf = GET_FIELD(pqio, priv);

    RETURN_VAL_IF(rbuf == NULL, NET_WARN, QIOEOF);

    return ringbuf_read(rbuf, buf, nbuf);
}

static QIO_S* websrv_qio_create()
{
    QIO_S*          pqio;

    pqio = (QIO_S *)pi_zalloc(sizeof(*pqio));
    RETURN_VAL_IF(pqio == NULL, NET_WARN, NULL);

    pqio->priv = ringbuf_create(QIO_RING_BUF_LEN);
    RETURN_VAL_IF(pqio->priv == NULL, NET_WARN, NULL);

    pqio->close = websrv_qio_close;
    pqio->poll  = websrv_qio_poll;
    pqio->read  = websrv_qio_read;
    return pqio;
}

static void websrv_error_response(HTTP_TASK_S* ptask)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    RETURN_IF(priv == NULL, NET_WARN);

    int32_t     reply_len;
    reply_len = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Result':'%d'}", HTTP_SYSUPGRADE_FAIL);
    http_task_reply_resp_headers(ptask, http_status_string(200), MIME_TYPE_JSON, reply_len);
    http_task_send(ptask, priv->iobuf, reply_len);
}

static void websrv_update_sysstat_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    NETSTS_PACKET_S  packet = { .count = 0 };

    if (netsts_take_packet(&packet) == 0)
    {
        // 检测到固件端下载错误时不再继续发送数据
        for ( size_t i = 0; i < packet.count; ++i )
        {
            if ( packet.array[i].status_id == STATUS_I_FWUPDATE_RESULT &&
                 packet.array[i].parameter1 == UPGRADE_UPDATE_DOWNING_FAILED_REBOOT )
            {
                s_fwupgrade_ctx.flg_download_failed = 1;
                return;
            }
        }
    }
}

static int32_t websrv_fwupgrade_reqbody_continue(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    FWUPGRADE_PRIV_S* upgrade_priv = GET_FIELD(priv, user_data);
    RING_BUF_S*       rbuf = NULL;
    NETSTS_PACKET_S   packet;

    RETURN_VAL_IF(upgrade_priv == NULL, NET_WARN, -1);
    rbuf = upgrade_priv->pqio->priv;
    RETURN_VAL_IF(rbuf == NULL, NET_WARN, -1);

    if (s_fwupgrade_ctx.flg_download_failed)
    {
        websrv_error_response(ptask);
        ATOMIC_UNLOCK(s_fwupgrade_ctx.flg_upgrading);
        return -1;
    }

    upgrade_priv->ntotal_written += websrv_rbuf_write_all(rbuf, data, ndata);
    NET_TRACE("fwupgrade, total written to qio bytes is %u", upgrade_priv->ntotal_written);

    return 0;
}

static int32_t websrv_fwupgrade_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    FWUPGRADE_PRIV_S*   upgrade_priv = GET_FIELD(priv, user_data);
    uint8_t             *p, *end;
    const char*         data_left = NULL;
    size_t              ndata_left = 0;
    ROUTER_MSG_S        send_msg;
    QIO_S*              pqio;
    JOB_REQUEST_S       *job_request;

    RETURN_VAL_IF(upgrade_priv == NULL, NET_WARN, -1);

    // unlikely, if true, only need to copy BODY_LEAST_LEN firstly
    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        data_left = data + BODY_LEAST_LEN;
        ndata_left = ndata - BODY_LEAST_LEN;
        ndata = BODY_LEAST_LEN;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;
    RETURN_VAL_IF(priv->received < BODY_LEAST_LEN, NET_NONE, 0);

    end = (uint8_t*)priv->iobuf + priv->received;
    do {
        p = memmem(priv->iobuf, priv->received, CONTENT_TYPE_OCTET_STREAM, LEN(CONTENT_TYPE_OCTET_STREAM));
        BREAK_IF(p == NULL, NET_WARN);

        p += LEN(CONTENT_TYPE_OCTET_STREAM);
        if (memcmp(p, "\xf5\xd4\xc3\xb2", 4))
        {
            NET_DEBUG("the data is not a upgrade file %x%x%x%x!!!", p[0], p[1], p[2], p[3]);
            break;
        }

        BREAK_IF(upgrade_priv->pqio != NULL, NET_WARN);
        pqio = upgrade_priv->pqio = websrv_qio_create();
        BREAK_IF(pqio == NULL || pqio->priv == NULL, NET_WARN);

        job_request = (JOB_REQUEST_S*)pi_zalloc(sizeof(JOB_REQUEST_S));
        BREAK_IF(job_request == NULL, NET_WARN);

        job_request->io_class = IO_CLASS_PRINT;
        job_request->io_via = IO_VIA_NET;
        job_request->pqio = (void*)pqio;
        job_request->job_config_param = NULL;
        job_request->notify = NULL;

        websrv_rbuf_write_all(pqio->priv, ACL_HEAD ACL_UPGRADE, sizeof(ACL_HEAD ACL_UPGRADE) - 1);
        //real data begin
        upgrade_priv->ntotal_written = websrv_rbuf_write_all(pqio->priv, (const char*)p, end - p);
        if (data_left)
        {
            upgrade_priv->ntotal_written += websrv_rbuf_write_all(pqio->priv, data_left, ndata_left);
        }

        send_msg.msgType = MSG_CTRL_JOB_REQUEST;
        send_msg.msg1 = 0;
        send_msg.msg2 = 0;
        send_msg.msg3 = job_request;
        send_msg.msgSender = MID_PORT_NET;

        BREAK_IF( 0 != task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg), NET_WARN);
        ptask->reqbody_received_callback = websrv_fwupgrade_reqbody_continue;

        return 0;
    }
    while (0);

    ATOMIC_UNLOCK(s_fwupgrade_ctx.flg_upgrading);
    websrv_error_response(ptask);
    return -1;
}

static int32_t websrv_fwupgrade_header_cb(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    const char*         content_type;
    FWUPGRADE_PRIV_S*   upgrade_priv;

    RETURN_VAL_IF( priv == NULL || priv->user_data, NET_WARN, -1);

    upgrade_priv = (FWUPGRADE_PRIV_S*)pi_zalloc(sizeof(*upgrade_priv));
    RETURN_VAL_IF(upgrade_priv == NULL, NET_WARN, -1);
    priv->user_data = upgrade_priv;
    priv->user_data_destroy_fn = websrv_fwupgrade_destroy;

    do  // get multipart/form-data boundary for websrv_fwupgrade
    {
        content_type = http_task_search_header_field(ptask, HEADER_CONTENT_TYPE);
        BREAK_IF(STRING_IS_EMPTY(content_type), NET_DEBUG);
        BREAK_IF(strstr(content_type, MULTIPART_FORM) == NULL, NET_WARN);

        BREAK_IF(!ATOMIC_TRY_LOCK(s_fwupgrade_ctx.flg_upgrading), NET_DEBUG);
        s_fwupgrade_ctx.flg_download_failed = 0;
        ptask->reqbody_received_callback = websrv_fwupgrade_reqbody;
        return 0;
    }
    while (0);

    websrv_error_response(ptask);
    return -1;
}

static WEBSRV_URL_TABLE_S fwupgrade_tbl __attr_section(url_table) =
    { "/fwupgrade" , HTTP_POST, websrv_upgrade_firmware, websrv_fwupgrade_header_cb, 0x7, 1 }; ///< 固件升级

static int32_t websrv_fwupgrade_init(NET_CTX_S* net_ctx)
{
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    memset(&s_fwupgrade_ctx, 0, sizeof(s_fwupgrade_ctx));
    s_fwupgrade_ctx.net_ctx = net_ctx;

    netctx_add_sysstat_observer(net_ctx, websrv_update_sysstat_callback, NULL);
    return 0;
}

FUNC_EXPORT(init, websrv_fwupgrade_init);
