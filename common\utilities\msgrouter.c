/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file msgrouter.c
 * @addtogroup utilities
 * @{
 * @addtogroup msgrouter
 * <AUTHOR>
 * @date 2023-06-21
 * @version v1.0
 * @brief msg router module
 */
#include <pthread.h>
#include <stdarg.h>
#include <errno.h>

#include "utilities/msgrouter.h"
#include "pol/pol_mem.h"
#include "pol/pol_log.h"

#define MSGRT_DEBUG(fmt, ...)           pi_log_d(fmt "\n", ##__VA_ARGS__)
#define MSGRT_ERROR(fmt, ...)           pi_log_e(fmt "\n", ##__VA_ARGS__)

#define msg_router_check_module(module_id)                                      \
{                                                                               \
    if ( module_id >= s_mail_count )                                            \
    {                                                                           \
        MSGRT_ERROR("module(%u) is overflow(%u)", module_id, s_mail_count);     \
        return RET_FAIL;                                                        \
    }                                                                           \
                                                                                \
    if ( s_mail_array[module_id] == NULL )                                      \
    {                                                                           \
        MSGRT_ERROR("module(%u) has been unregistered", module_id);             \
        return RET_FAIL;                                                        \
    }                                                                           \
}                                                                               \

pthread_mutex_t     s_mail_mutex = PTHREAD_MUTEX_INITIALIZER;
PI_MAILBOX_T*       s_mail_array = NULL;
uint32_t            s_mail_count = 0;

int32_t msg_router_prolog(uint32_t module_num)
{
    int32_t ret = RET_FAIL;

    pthread_mutex_lock(&s_mail_mutex);
    do
    {
        BREAK_IF(s_mail_array != NULL, MSGRT_ERROR);

        s_mail_array = (PI_MAILBOX_T *)pi_zalloc(module_num * sizeof(PI_MAILBOX_T));
        BREAK_IF(s_mail_array == NULL, MSGRT_ERROR);

        s_mail_count = module_num;
        ret = RET_SUCCESS;
    }
    while ( 0 );
    pthread_mutex_unlock(&s_mail_mutex);

    return ret;
}

void msg_router_epilog(void)
{
    pthread_mutex_lock(&s_mail_mutex);
    if ( s_mail_array != NULL )
    {
        pi_free(s_mail_array);
    }
    s_mail_array = NULL;
    s_mail_count = 0;
    pthread_mutex_unlock(&s_mail_mutex);
}

int32_t msg_router_register_custom(int32_t n, uint32_t module_id, ...)
{
    uint32_t    msg_count = PI_NUM_QELEMENTS;
    int32_t     ret = RET_FAIL;
    va_list     arg;

    if ( n > 1 )
    {
        va_start(arg, module_id);
        msg_count = va_arg(arg, uint32_t);
        va_end(arg);
    }

    pthread_mutex_lock(&s_mail_mutex);
    do
    {
        BREAK_IF(s_mail_array == NULL, MSGRT_ERROR);

        if ( module_id >= s_mail_count )
        {
            MSGRT_ERROR("module_id(%u) is overflow(%u)", module_id, s_mail_count);
            break;
        }

        if ( s_mail_array[module_id] != NULL )
        {
            MSGRT_ERROR("module_id(%u) has been registered", module_id);
            break;
        }

        s_mail_array[module_id] = pi_msgq_create_custom(msg_count);
        if ( s_mail_array[module_id] == NULL )
        {
            MSGRT_ERROR("create msgq[%u] failed: %d<%s>", module_id, errno, strerror(errno));
            break;
        }

        ret = RET_SUCCESS;
    }
    while ( 0 );
    pthread_mutex_unlock(&s_mail_mutex);

    return ret;
}

void msg_router_unregister(uint32_t module_id)
{
    pthread_mutex_lock(&s_mail_mutex);
    do
    {
        BREAK_IF(s_mail_array == NULL, MSGRT_ERROR);

        if ( module_id >= s_mail_count )
        {
            MSGRT_ERROR("module_id(%u) is overflow(%u)", module_id, s_mail_count);
            break;
        }

        if ( s_mail_array[module_id] == NULL )
        {
            MSGRT_ERROR("module_id(%u) has been unregistered", module_id);
            break;
        }

        pi_msgq_destroy(s_mail_array[module_id]);
        s_mail_array[module_id] = NULL;
    }
    while ( 0 );
    pthread_mutex_unlock(&s_mail_mutex);
}

int32_t task_msg_send_by_router(uint32_t module_id, ROUTER_MSG_S* rtmsg)
{
    uint64_t    value = 0;
    MSG_S       msg = {0};

    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    msg.msg1 = rtmsg->msgType;
    msg.msg2 = rtmsg->msg1;
    msg.msg3 = rtmsg->msg2;
    value    = ((uint64_t)(rtmsg->msgSender) << 32) & 0xFFFFFFFF00000000;
    msg.msg4.ullval = (uint64_t)(value | (uint32_t)rtmsg->msg3);

    return pi_msg_send(s_mail_array[module_id], &msg);
}

int32_t task_msg_send_forever_by_router(uint32_t module_id, ROUTER_MSG_S* rtmsg)
{
    uint64_t    value = 0;
    int32_t     flag = 0;
    int32_t     ret = 0;
    MSG_S       msg = {0};

    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    msg.msg1 = rtmsg->msgType;
    msg.msg2 = rtmsg->msg1;
    msg.msg3 = rtmsg->msg2;
    value    = ((uint64_t)(rtmsg->msgSender) << 32) & 0xFFFFFFFF00000000;
    msg.msg4.ullval = (uint64_t)(value | (uint32_t)rtmsg->msg3);

    while ( 1 )
    {
        ret = pi_msg_send(s_mail_array[module_id], &msg);
        if( 0 == ret )
        {
            break;
        }
        else if( -2 == ret ) //mailbox full,send failed,wait some time then try again
        {
            if( flag == 0 )
            {
                MSGRT_ERROR("mailbox[%u] full with msg(%d)", module_id, msg.msg1);
                flag = 1;
            }
            pi_msleep(10);
        }
        else
        {
            MSGRT_ERROR("mailbox[%u] error with msg(%d)", module_id, msg.msg1);
            break;
        }
    }

    return ret;
}

int32_t task_msg_wait_forever_by_router(uint32_t module_id, ROUTER_MSG_S* rtmsg)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(s_mail_array[module_id], &msg, -1, -1);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}

int32_t task_msg_wait_timeout_by_router(uint32_t module_id, ROUTER_MSG_S* rtmsg, int32_t wait_secs, int32_t wait_usecs)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(s_mail_array[module_id], &msg, wait_secs, wait_usecs);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}

int32_t task_msg_timeout_by_router(uint32_t module_id, ROUTER_MSG_S* rtmsg, TIMEVAL_S* tv)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(tv == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(s_mail_array[module_id], &msg, tv->secs, tv->usecs);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}

int32_t task_msg_tryto_get_msg_count(uint32_t module_id)
{
    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    return pi_msg_count_get(s_mail_array[module_id]);
}

int32_t task_msg_tryto_get_msg_size(uint32_t module_id)
{
    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    return pi_msgq_size_get(s_mail_array[module_id]);
}

int32_t task_msg_tryto_get_by_router(uint32_t module_id, ROUTER_MSG_S* rtmsg)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(s_mail_array == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    msg_router_check_module(module_id);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(s_mail_array[module_id], &msg, 0, 0);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}

int32_t task_msg_send(PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg)
{
    uint64_t    value;
    MSG_S       msg;

    RETURN_VAL_IF(mailbox == INVALIDMSGQ, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    msg.msg1 = rtmsg->msgType;
    msg.msg2 = rtmsg->msg1;
    msg.msg3 = rtmsg->msg2;
    value    = ((uint64_t)(rtmsg->msgSender) << 32) & 0xFFFFFFFF00000000;
    msg.msg4.ullval = (uint64_t)(value | (uint32_t)rtmsg->msg3);

    return pi_msg_send(mailbox, &msg);
}

int32_t task_msg_timeout(PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg, TIMEVAL_S* tv)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(mailbox == INVALIDMSGQ, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(tv == NULL, MSGRT_ERROR, RET_FAIL);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(mailbox, &msg, tv->secs, tv->usecs);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}

int32_t task_msg_wait_forever(PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(mailbox == INVALIDMSGQ, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(mailbox, &msg, -1, -1);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}

int32_t task_msg_tryto_get(PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg)
{
    int32_t ret;
    MSG_S   msg;

    RETURN_VAL_IF(mailbox == INVALIDMSGQ, MSGRT_ERROR, RET_FAIL);
    RETURN_VAL_IF(rtmsg == NULL, MSGRT_ERROR, RET_FAIL);

    memset(&msg, 0, sizeof(msg));
    ret = pi_msg_recv(mailbox, &msg, 0, 0);
    rtmsg->msgType   = msg.msg1;
    rtmsg->msg1      = msg.msg2;
    rtmsg->msg2      = msg.msg3;
    rtmsg->msg3      = (void *)((uint32_t)(msg.msg4.ullval & 0xFFFFFFFF));
    rtmsg->msgSender = (uint32_t)((msg.msg4.ullval >> 32) & 0xFFFFFFFF);

    return ret;
}
/**
 *@}
 */
