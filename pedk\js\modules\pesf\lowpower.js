var lp_Module_Main = globalThis.pedk.Module_Main
var lp_Module_Sub = globalThis.pedk.Module_Sub
var lowpower_listeners = []
class LowPowerStateChangeListener{
    constructor()
    {
        let instance = new globalThis.pedk.ObserverManager();
        instance.addListeners(this, this.SetState, lp_Module_Main.MSG_MODULE_LOWPOWER, lp_Module_Sub.MSG_LOWPOWER_SUB_NOTIFY);
    }

    notify(state)
    {
    }

    SetState(obj,respond,data)
    {
        obj.updateJobState(respond)

    }

    updateJobState(newJobState)
    {
        console.log('->newJobState',newJobState)
        for(const listener of lowpower_listeners)
        {
            listener.notify(newJobState)
        }
    }
}

globalThis.pedk.device.powersave.addListener = function addListener(listener)
{
    if (listener instanceof LowPowerStateChangeListener !== true) {
        throw TypeError('EINVALIDPARAM')
    }
    lowpower_listeners.push(listener)
    return "EXIT_SUCCESS"
}

globalThis.pedk.device.powersave.removeListener = function removeListener(listener)
{
    if (listener instanceof LowPowerStateChangeListener !== true) {
        throw TypeError('EINVALIDPARAM')
    }
    lowpower_listeners.pop(listener)
    return "EXIT_SUCCESS"
}


globalThis.pedk.device.powersave.LowPowerStateChangeListener = LowPowerStateChangeListener
globalThis.pedk.device.powersave.getCurrentState = getCurrentState
globalThis.pedk.device.powersave.requestStateChange = requestStateChange