#include "PEDK_event.h"
#include "PEDK_ui.h"
#include "cJSON.h"
#include "app_manager.h"


#define Log(format, ...) printf("[ui] [RE] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);
#define Error(format, ...) printf("[ui_error] [RE] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

#define countof(x) (sizeof(x) / sizeof((x)[0]))

static char APP_path_s[PEDK_APP_PATH_LENGTH] = {0};

void get_app_path( JSContext *ctx )
{
    char *path = NULL;
    path = GetAppWorkspace( ctx );
    if( NULL != path )
    {
        memset( APP_path_s, 0, sizeof(APP_path_s) );
        snprintf(APP_path_s, PEDK_APP_PATH_LENGTH, "%s", path );
    }
}

void get_js_widget_base_data(JSContext *ctx, JSValueConst js_widget_obj, cJSON *ObjFirst,
                            int* x, int* y, int* w, int* h, char* id, char* type )
{
    char* str = NULL;
    JSValueConst js_widget_id = JS_GetPropertyStr( ctx, js_widget_obj, "id");
    JSValueConst js_widget_type = JS_GetPropertyStr( ctx, js_widget_obj, "type");
    JSValueConst js_widget_x = JS_GetPropertyStr( ctx, js_widget_obj, "x");
    JSValueConst js_widget_y = JS_GetPropertyStr( ctx, js_widget_obj, "y");
    JSValueConst js_widget_w = JS_GetPropertyStr( ctx, js_widget_obj, "w");
    JSValueConst js_widget_h = JS_GetPropertyStr( ctx, js_widget_obj, "h");

    str = JS_ToCString(ctx, js_widget_id);
    snprintf( id, PEDK_WIDGET_ID_LENGTH, "%s", str );
    JS_FreeCString( ctx, str );        //释放内存

    str = JS_ToCString(ctx, js_widget_type);
    snprintf(type, PEDK_WIDGET_TYPE_LENGTH, "%s", str);
    JS_FreeCString( ctx, str);     
    
    JS_ToInt32(ctx, x, js_widget_x);
    JS_ToInt32(ctx, y, js_widget_y);
    JS_ToInt32(ctx, w, js_widget_w);
    JS_ToInt32(ctx, h, js_widget_h);

    //新建一个json数组，每个元素是一个对象
    //先生成每个对象
    cJSON_AddStringToObject(ObjFirst,"id", id);
    cJSON_AddStringToObject(ObjFirst,"type",type);
    cJSON_AddNumberToObject(ObjFirst,"x",*x);
    cJSON_AddNumberToObject(ObjFirst,"y",*y);
    cJSON_AddNumberToObject(ObjFirst,"w",*w);
    cJSON_AddNumberToObject(ObjFirst,"h",*h);

   //将对象添加到数组
   // cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}

void get_js_widget_iamge_data( JSContext *ctx, JSValueConst js_widget_obj, cJSON *cJSON_ObjArray )
{
    char* str           = NULL;
    char img_res[PEDK_WIDGET_IMAGE_PATH_LENGTH]   = {0};
    char img_format[PEDK_WIDGET_IMAGE_FORMAT_LENGTH] = {0};

    JSValueConst js_widget_img_res = JS_GetPropertyStr( ctx, js_widget_obj, "img_res");
    JSValueConst js_widget_img_format = JS_GetPropertyStr( ctx, js_widget_obj, "img_format");

    snprintf( img_res, PEDK_WIDGET_IMAGE_PATH_LENGTH, "%s", APP_path_s );
    
    str = JS_ToCString( ctx, js_widget_img_res );

    snprintf( img_res + strlen(img_res), PEDK_WIDGET_IMAGE_PATH_LENGTH - strlen(img_res), "%s", str );

    JS_FreeCString( ctx, str );

    str = JS_ToCString(ctx, js_widget_img_format);
    snprintf( img_format, PEDK_WIDGET_IMAGE_FORMAT_LENGTH, "%s", str );
    JS_FreeCString( ctx,str);

    cJSON *cJSON_img = cJSON_CreateObject();
    cJSON_AddStringToObject( cJSON_img,"img_res", img_res );
    cJSON_AddStringToObject( cJSON_img,"img_format", img_format );
    cJSON_AddItemToArray(cJSON_ObjArray,cJSON_img);
}

void get_js_widget_style_sheet( JSContext *ctx, JSValueConst js_widget_style_sheet, cJSON *ObjFirst )
{
    char *str = NULL;
    char text_color[PEDK_WIDGET_STYLE_COLOR_LENGTH] = {0}, font_size[PEDK_WIDGET_STYLE_FONT_SIZE_LENGTH] = {0}, text_align[PEDK_WIDGET_STYLE_TEXT_ALIGN_LENGTH] = {0}, bg_color[PEDK_WIDGET_STYLE_COLOR_LENGTH] = {0};
    if( JS_IsObject(js_widget_style_sheet) && NULL != js_widget_style_sheet )
    {
        JSValueConst js_widget_style_sheet_text_color = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_color");
        JSValueConst js_widget_style_sheet_font_size = JS_GetPropertyStr( ctx, js_widget_style_sheet, "font_size");
        JSValueConst js_widget_style_sheet_text_align = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_align");
        JSValueConst js_widget_style_sheet_bg_color = JS_GetPropertyStr( ctx, js_widget_style_sheet, "bg_color");

        str = JS_ToCString(ctx, js_widget_style_sheet_text_color);
        if( str )
        {
            snprintf( text_color, PEDK_WIDGET_STYLE_COLOR_LENGTH, "%s", str );
            JS_FreeCString( ctx,str);
        }
        else
        {
            snprintf( text_color, PEDK_WIDGET_STYLE_COLOR_LENGTH, "%s", "0x000000" );
        }

        str = JS_ToCString(ctx, js_widget_style_sheet_bg_color);
        if( str )
        {
            snprintf( bg_color, PEDK_WIDGET_STYLE_COLOR_LENGTH, "%s", str );
            JS_FreeCString( ctx,str);
        }
        else
        {
            snprintf( bg_color, PEDK_WIDGET_STYLE_COLOR_LENGTH, "%s", "0xFFFFFF" );
        }

        str = JS_ToCString(ctx, js_widget_style_sheet_bg_color);
        if( str )
        {
            snprintf( bg_color, sizeof( bg_color ), "%s", str );
            JS_FreeCString( ctx,str);
        }
        else
        {
            snprintf( bg_color, sizeof( bg_color ), "%s", "0xFFFFFF" );
        }

        str = JS_ToCString(ctx, js_widget_style_sheet_font_size);
        snprintf( font_size, PEDK_WIDGET_STYLE_FONT_SIZE_LENGTH, "%s", str );
        JS_FreeCString( ctx,str);
        
        str = JS_ToCString(ctx, js_widget_style_sheet_text_align);
        snprintf(text_align, PEDK_WIDGET_STYLE_TEXT_ALIGN_LENGTH, "%s", str);
        JS_FreeCString( ctx,str);
    }
        cJSON *cJSON_style_sheet = cJSON_CreateObject();
        cJSON_AddStringToObject( cJSON_style_sheet, "text_color", text_color );
        cJSON_AddStringToObject( cJSON_style_sheet, "font_size", font_size );
        cJSON_AddStringToObject( cJSON_style_sheet, "text_align", text_align );
        cJSON_AddStringToObject( cJSON_style_sheet, "bg_color", bg_color );
        cJSON_AddItemToObject( ObjFirst, "style_sheet", cJSON_style_sheet );
}

JSValue js_Keyboard_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSPanel panel = {0};
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * str = NULL;

    int ret;

    JSValueConst js_widget_id = JS_GetPropertyStr( ctx, js_widget_obj, "id");
    JSValueConst js_widget_type = JS_GetPropertyStr( ctx, js_widget_obj, "type");

    str = JS_ToCString(ctx, js_widget_id);
    snprintf( panel.id, PEDK_WIDGET_ID_LENGTH, "%s", str );
    JS_FreeCString( ctx, str );        //释放内存

    str = JS_ToCString(ctx, js_widget_type);
    snprintf(panel.type, PEDK_WIDGET_TYPE_LENGTH, "%s", str);
    JS_FreeCString( ctx, str);     
    

    //新建一个json数组，每个元素是一个对象
    //先生成每个对象

    cJSON *ObjFirst = cJSON_CreateObject();
    cJSON_AddStringToObject(ObjFirst,"id",panel.id);
    cJSON_AddStringToObject(ObjFirst,"type",panel.type);

   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}

JSValue js_screen_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSScreen screen = {0};

    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &screen.x, &screen.y, &screen.w, &screen.h, screen.id, screen.type  );

    JSValueConst  js_widget_style_sheet = JS_GetPropertyStr( ctx, js_widget_obj, "style_sheet");
    get_js_widget_style_sheet( ctx, js_widget_style_sheet, ObjFirst );

   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}

                            
JSValue js_panel_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSPanel panel = {0};
    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &panel.x, &panel.y, &panel.w, &panel.h, panel.id, panel.type  );
                            
    JSValueConst  js_widget_style_sheet = JS_GetPropertyStr( ctx, js_widget_obj, "style_sheet");
    get_js_widget_style_sheet( ctx, js_widget_style_sheet, ObjFirst );

   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}

JSValue js_button_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{
    JSButton button = {0};
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * app_path = NULL;
    char* str = NULL;

    int ret;

    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &button.x, &button.y, &button.w, &button.h, button.id, button.type  );

    JSValueConst js_widget_is_valid = JS_GetPropertyStr( ctx, js_widget_obj, "is_valid");
    JSValueConst js_widget_text = JS_GetPropertyStr( ctx, js_widget_obj, "text");
    JSValueConst js_widget_check_able = JS_GetPropertyStr( ctx, js_widget_obj, "check_able");
    JSValueConst js_widget_is_checked = JS_GetPropertyStr( ctx, js_widget_obj, "is_checked");
    
    JSValueConst  js_widget_style_sheet = JS_GetPropertyStr( ctx, js_widget_obj, "style_sheet");
    //get_js_widget_style_sheet( ctx, js_widget_style_sheet, ObjFirst );

    if( JS_IsObject(js_widget_style_sheet) && NULL != js_widget_style_sheet )
    {
        JSValueConst js_widget_style_sheet_text_color = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_color");
        JSValueConst js_widget_style_sheet_font_size = JS_GetPropertyStr( ctx, js_widget_style_sheet, "font_size");
        JSValueConst js_widget_style_sheet_text_align = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_align");

        str = JS_ToCString(ctx, js_widget_style_sheet_text_color);
        strcpy( button.style_sheet.text_color, str );
        JS_FreeCString( ctx,str);

        str = JS_ToCString(ctx, js_widget_style_sheet_font_size);
        strcpy( button.style_sheet.font_size, str );
        JS_FreeCString( ctx,str);
        
        str = JS_ToCString(ctx, js_widget_style_sheet_text_align);
        strcpy(button.style_sheet.text_align, str);
        JS_FreeCString( ctx,str);
    }
    
    JSValueConst js_widget_imgs = JS_GetPropertyStr( ctx, js_widget_obj, "imgs");

    cJSON *cJSON_ImageArray = cJSON_CreateArray();

    int array_len = 0;
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_widget_imgs, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("imgs length  is null\n"); 
        for( int i = 0; i < 4; ++i )
        {
            cJSON *cJSON_ImageObj = cJSON_CreateObject();
            cJSON_AddStringToObject( cJSON_ImageObj,"img_res", "" );
            cJSON_AddStringToObject( cJSON_ImageObj,"img_format", "" );
            cJSON_AddItemToArray( cJSON_ImageArray, cJSON_ImageObj );
        }
    }else
    {
        JS_ToInt32( ctx, &array_len, obj_mem);
        //遍历数组，处理每个对象
        for( int i = 0; i < array_len; i++ )
        {
            JSValueConst js_widget_img_obj = JS_GetPropertyUint32( ctx, js_widget_imgs, i);
            
            if( i >= 3)
            {
                break;
            }
            if( !JS_IsObject( js_widget_img_obj ) )
            {
                cJSON *cJSON_ImageObj = cJSON_CreateObject();
                cJSON_AddStringToObject( cJSON_ImageObj,"img_res", "" );
                cJSON_AddStringToObject( cJSON_ImageObj,"img_format", "" );
                cJSON_AddItemToArray( cJSON_ImageArray, cJSON_ImageObj );
            }else
            {
                get_js_widget_iamge_data( ctx, js_widget_img_obj, cJSON_ImageArray );
            }
        }
    }    

    ret = JS_ToInt32(ctx, &button.is_valid, js_widget_is_valid);
    
    str = JS_ToCString(ctx, js_widget_text);
    snprintf( button.text, PEDK_WIDGET_TEXT_LENGTH, "%s", str);
    JS_FreeCString( ctx,str);
    
    ret = JS_ToInt32(ctx, &button.is_checked, js_widget_is_checked);

    //新建一个json数组，每个元素是一个对象
    //先生成每个对象
    cJSON *style = cJSON_CreateObject();
    
    cJSON_AddItemToObject(ObjFirst,"style_sheet",style);
    cJSON_AddStringToObject(style,"text_color",button.style_sheet.text_color);
    cJSON_AddStringToObject(style,"font_size",button.style_sheet.font_size);
    cJSON_AddStringToObject(style,"text_align",button.style_sheet.text_align);
    
    cJSON_AddNumberToObject(ObjFirst,"is_valid",button.is_valid);
    cJSON_AddStringToObject(ObjFirst,"text",button.text);
    cJSON_AddNumberToObject(ObjFirst,"is_checked",button.is_checked);

    cJSON_AddItemToObject(ObjFirst,"imgs",cJSON_ImageArray);

   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);
}

JSValue js_label_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSLabel label = {0};
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * str = NULL;

    int ret;

    
    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &label.x, &label.y, &label.w, &label.h, label.id, label.type  );
    
    JSValueConst js_widget_style_sheet = JS_GetPropertyStr( ctx, js_widget_obj, "style_sheet");
    JSValueConst js_widget_style_sheet_text_color = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_color");
    JSValueConst js_widget_style_sheet_font_size = JS_GetPropertyStr( ctx, js_widget_style_sheet, "font_size");
    
    JSValueConst js_widget_text = JS_GetPropertyStr( ctx, js_widget_obj, "text");

    if( JS_ToCString(ctx, js_widget_style_sheet_text_color) != NULL )
    {
        str = JS_ToCString(ctx, js_widget_style_sheet_text_color);
        strcpy( label.style_sheet.text_color, str);
        JS_FreeCString( ctx,str);
    }
    if( JS_ToCString(ctx, js_widget_style_sheet_font_size) != NULL )
    {
        str = JS_ToCString(ctx, js_widget_style_sheet_font_size);
        strcpy( label.style_sheet.font_size, str);
        JS_FreeCString( ctx,str);
    }

    str = JS_ToCString(ctx, js_widget_text);
    snprintf(label.text, PEDK_WIDGET_TEXT_LENGTH, "%s", str);
    JS_FreeCString( ctx,str);
    //新建一个json数组，每个元素是一个对象
    //先生成每个对象
    cJSON *style = cJSON_CreateObject();
    cJSON_AddStringToObject(ObjFirst,"text",label.text);
    
    cJSON_AddItemToObject(ObjFirst,"style_sheet",style);
    cJSON_AddStringToObject(style,"text_color",label.style_sheet.text_color);
    cJSON_AddStringToObject(style,"font_size",label.style_sheet.font_size);
   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}


JSValue js_image_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSImage image = {0};
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * str = NULL;

    int ret;
    
    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &image.x, &image.y, &image.w, &image.h, image.id, image.type  );
    
    JSValueConst js_widget_img = JS_GetPropertyStr( ctx, js_widget_obj, "img");
    JSValueConst js_widget_img_res = JS_GetPropertyStr( ctx, js_widget_img, "img_res");
    JSValueConst js_widget_img_format = JS_GetPropertyStr( ctx, js_widget_img, "img_format");


    if(NULL == js_widget_img_res || NULL == js_widget_img || NULL == js_widget_img_format)
    {
        printf("image data error\n");
        return;
    }

    snprintf( image.img.img_res, PEDK_WIDGET_IMAGE_PATH_LENGTH, "%s", APP_path_s );
    str = JS_ToCString(ctx, js_widget_img_res);
    snprintf( image.img.img_res + strlen( image.img.img_res ), PEDK_WIDGET_IMAGE_PATH_LENGTH - strlen( image.img.img_res ), "%s", str );
    JS_FreeCString( ctx,str);
    //snprintf( image.img.img_res,JS_ToCString(ctx, js_widget_img_res) );
    str = JS_ToCString(ctx, js_widget_img_format);
    snprintf( image.img.img_format, PEDK_WIDGET_IMAGE_FORMAT_LENGTH, "%s", str);
    JS_FreeCString( ctx,str);

    cJSON *img = cJSON_CreateObject();
    cJSON_AddItemToObject(ObjFirst,"img",img);
    cJSON_AddStringToObject(img,"img_res",image.img.img_res);
    cJSON_AddStringToObject(img,"img_format",image.img.img_format);
   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}

JSValue js_gif_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSImage image = {0};
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * str = NULL;

    int ret;
    
    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &image.x, &image.y, &image.w, &image.h, image.id, image.type  );
    
    JSValueConst js_widget_img = JS_GetPropertyStr( ctx, js_widget_obj, "img");
    JSValueConst js_widget_img_res = JS_GetPropertyStr( ctx, js_widget_img, "img_res");
    JSValueConst js_widget_img_format = JS_GetPropertyStr( ctx, js_widget_img, "img_format");


    if(NULL == js_widget_img_res || NULL == js_widget_img || NULL == js_widget_img_format)
    {
        printf("image data error\n");
        return;
    }

    snprintf( image.img.img_res, PEDK_WIDGET_IMAGE_PATH_LENGTH, "%s", APP_path_s );
    str = JS_ToCString(ctx, js_widget_img_res);
    snprintf( image.img.img_res + strlen( image.img.img_res ), PEDK_WIDGET_IMAGE_PATH_LENGTH - strlen( image.img.img_res ), "%s", str );
    JS_FreeCString( ctx,str);
    //snprintf( image.img.img_res,JS_ToCString(ctx, js_widget_img_res) );
    str = JS_ToCString(ctx, js_widget_img_format);
    snprintf( image.img.img_format, PEDK_WIDGET_IMAGE_FORMAT_LENGTH, "%s", str);
    JS_FreeCString( ctx,str);

    cJSON *img = cJSON_CreateObject();
    cJSON_AddItemToObject(ObjFirst,"img",img);
    cJSON_AddStringToObject(img,"img_res",image.img.img_res);
    cJSON_AddStringToObject(img,"img_format",image.img.img_format);
   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}

JSValue js_animal_image_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSAnimalImage animal_image = {0};
    cJSON *cJSON_ImageArray = cJSON_CreateArray();

    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char* app_path = GetAppWorkspace( ctx );
    char* str = NULL;

    int ret;
    
    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &animal_image.x, &animal_image.y, &animal_image.w, &animal_image.h, animal_image.id, animal_image.type  );

    JSValueConst js_widget_freq = JS_GetPropertyStr( ctx, js_widget_obj, "freq");
    JSValueConst js_widget_imgs = JS_GetPropertyStr( ctx, js_widget_obj, "imgs");

    ret = JS_ToInt32(ctx, &animal_image.freq, js_widget_freq);
    
    int array_len = 0;
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_widget_imgs, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("Property not exist\n"); 
        return JS_EXCEPTION;
    }
    JS_ToInt32( ctx, &array_len, obj_mem);

    //遍历数组，处理每个对象
    for( int i = 0; i < array_len; i++ )
    {
        JSValueConst js_widget_img_obj = JS_GetPropertyUint32( ctx, js_widget_imgs, i);
        if( !JS_IsObject( js_widget_img_obj ) )
        {
            return JS_EXCEPTION;
        }
        get_js_widget_iamge_data( ctx, js_widget_img_obj, cJSON_ImageArray );
    }

    cJSON_AddNumberToObject( ObjFirst,"freq", animal_image.freq );
         
    cJSON_AddItemToObject( ObjFirst,"imgs", cJSON_ImageArray );
    
   //将对象添加到数组
    cJSON_AddItemToArray( obj_widget_array, ObjFirst );

}

JSValue js_line_edit_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{

    JSLineEdit line_edit = {0};
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * input_str = NULL;
    char * str = NULL;
    int ret;

    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &line_edit.x, &line_edit.y, &line_edit.w, &line_edit.h, line_edit.id, line_edit.type  );
    
    JSValueConst js_widget_style_sheet = JS_GetPropertyStr( ctx, js_widget_obj, "style_sheet");
    JSValueConst js_widget_style_sheet_text_color = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_color");
    JSValueConst js_widget_style_sheet_font_size = JS_GetPropertyStr( ctx, js_widget_style_sheet, "font_size");
    JSValueConst js_widget_style_sheet_text_align = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_align");
    JSValueConst js_widget_text = JS_GetPropertyStr( ctx, js_widget_obj, "text");
    JSValueConst js_widget_is_valid = JS_GetPropertyStr( ctx, js_widget_obj, "is_valid");
    JSValueConst js_widget_display_mode = JS_GetPropertyStr( ctx, js_widget_obj, "display_mode");

    ret = JS_ToInt32(ctx, &line_edit.is_valid, js_widget_is_valid);

    str = JS_ToCString(ctx, js_widget_style_sheet_text_color);
    strcpy( line_edit.style_sheet.text_color,str );
    JS_FreeCString( ctx, str);
    
    str = JS_ToCString(ctx, js_widget_style_sheet_font_size);
    strcpy( line_edit.style_sheet.font_size, str);
    JS_FreeCString(ctx, str);

    str = JS_ToCString(ctx, js_widget_style_sheet_text_align);
    strcpy( line_edit.style_sheet.text_align, str);
    JS_FreeCString(ctx, str);

    str = JS_ToCString(ctx, js_widget_display_mode);
    snprintf(line_edit.display_mode, PEDK_WIDGET_DISPLAY_MODE_LENGTH, "%s", str);
    JS_FreeCString( ctx, str);

    //检查输入字符是否超长
    input_str = JS_ToCString(ctx, js_widget_text);
    if( strlen(input_str) >= sizeof(line_edit.text) )
    {
        strncpy(line_edit.text,input_str,sizeof(line_edit.text)-1);
        Log("lineedit input string out of lenght\n");
    }
    else
    {
        snprintf(line_edit.text, PEDK_WIDGET_TEXT_LENGTH, "%s", input_str);
    }
    JS_FreeCString( ctx, input_str);

    cJSON *style = cJSON_CreateObject();
    cJSON_AddStringToObject(ObjFirst,"text",line_edit.text);
    cJSON_AddNumberToObject(ObjFirst,"is_valid",line_edit.is_valid);
    cJSON_AddStringToObject(ObjFirst,"display_mode",line_edit.display_mode);
    
    cJSON_AddItemToObject(ObjFirst,"style_sheet",style);
    cJSON_AddStringToObject(style,"text_color",line_edit.style_sheet.text_color);
    cJSON_AddStringToObject(style,"font_size",line_edit.style_sheet.font_size);
    cJSON_AddStringToObject(style,"text_align",line_edit.style_sheet.text_align);
   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);

}


JSValue js_radio_button_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{
    JSRadioButton radio_button = {0};
    cJSON *cJSON_ImageArray = cJSON_CreateArray();
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char* app_path = GetAppWorkspace( ctx );
    char* str = NULL;

    int ret;

    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &radio_button.x, &radio_button.y, &radio_button.w, &radio_button.h, radio_button.id, radio_button.type  );

    JSValueConst js_widget_is_valid = JS_GetPropertyStr( ctx, js_widget_obj, "is_valid");
    JSValueConst js_widget_check_able = JS_GetPropertyStr( ctx, js_widget_obj, "check_able");
    JSValueConst js_widget_is_checked = JS_GetPropertyStr( ctx, js_widget_obj, "is_checked");
    JSValueConst js_widget_imgs = JS_GetPropertyStr( ctx, js_widget_obj, "icons");

    int array_len = 0;
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_widget_imgs, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("imgs length  is null\n"); 
        for( int i = 0; i < 4; ++i )
        {
            cJSON *cJSON_ImageObj = cJSON_CreateObject();
            cJSON_AddStringToObject( cJSON_ImageObj,"img_res", "" );
            cJSON_AddStringToObject( cJSON_ImageObj,"img_format", "" );
            cJSON_AddItemToArray( cJSON_ImageArray, cJSON_ImageObj );
        }
    }else
    {
        JS_ToInt32( ctx, &array_len, obj_mem);
        //遍历数组，处理每个对象
        for( int i = 0; i < array_len; i++ )
        {
            JSValueConst js_widget_img_obj = JS_GetPropertyUint32( ctx, js_widget_imgs, i);
            
            if( i >= 4)
            {
                break;
            }
            if( !JS_IsObject( js_widget_img_obj ) )
            {
                cJSON *cJSON_ImageObj = cJSON_CreateObject();
                cJSON_AddStringToObject( cJSON_ImageObj,"img_res", "" );
                cJSON_AddStringToObject( cJSON_ImageObj,"img_format", "" );
                cJSON_AddItemToArray( cJSON_ImageArray, cJSON_ImageObj );
                //return JS_EXCEPTION;
            }else
            {
                get_js_widget_iamge_data( ctx, js_widget_img_obj, cJSON_ImageArray );
            }
        }
    }
    
    ret = JS_ToInt32(ctx, &radio_button.is_valid, js_widget_is_valid);
    ret = JS_ToInt32(ctx, &radio_button.is_checked, js_widget_is_checked);

    //新建一个json数组，每个元素是一个对象
    //先生成每个对象
    cJSON *style = cJSON_CreateObject();

    cJSON_AddNumberToObject(ObjFirst,"is_valid",radio_button.is_valid);
    cJSON_AddNumberToObject(ObjFirst,"is_checked",radio_button.is_checked);

    cJSON_AddItemToObject( ObjFirst, "icons", cJSON_ImageArray );

   //将对象添加到数组
    cJSON_AddItemToArray( obj_widget_array, ObjFirst );
}


JSValue js_radio_group_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{
    JSRadioGroup  radio_group  = {0};

    char* str = NULL;
    int ret = -1;
    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &radio_group.x, &radio_group.y, &radio_group.w, &radio_group.h, radio_group.id, radio_group.type  );

    JSValueConst js_widget_is_valid = JS_GetPropertyStr( ctx, js_widget_obj, "is_valid");
    JSValueConst js_widget_select_value = JS_GetPropertyStr( ctx, js_widget_obj, "select_value");

    ret = JS_ToInt32(ctx, &radio_group.is_valid, js_widget_is_valid);
    ret = JS_ToInt32(ctx, &radio_group.select_value, js_widget_select_value);

    cJSON_AddNumberToObject(ObjFirst,"is_valid",radio_group.is_valid);
    cJSON_AddNumberToObject(ObjFirst,"select_value",radio_group.select_value);

    
    JSValueConst js_radio_group_buttons = JS_GetPropertyStr( ctx, js_widget_obj, "buttons");
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_radio_group_buttons, "length");
    int array_len = 0;
    if( JS_IsException(obj_mem) )
    {
    }
    else
    {
        JS_ToInt32( ctx, &array_len, obj_mem);
        //遍历数组，处理每个对象
        cJSON *ObjButtons = cJSON_CreateArray();
        for( int i = 0; i < array_len; i++ )
        {
            JSValueConst js_widget_img_obj = JS_GetPropertyUint32( ctx, js_radio_group_buttons, i);
            js_radio_button_decode( ctx, js_widget_img_obj, ObjButtons );
        }
        cJSON_AddItemToObject(ObjFirst,"buttons",ObjButtons);
    }
    
   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);
}

JSValue js_check_button_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{
    JSCheckButton check_button  = {0};
    int ret;

    cJSON *ObjFirst             = cJSON_CreateObject();
    cJSON *cJSON_ImageArray     = cJSON_CreateArray();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &check_button.x, &check_button.y, &check_button.w, &check_button.h, check_button.id, check_button.type  );

    JSValueConst js_widget_is_valid   = JS_GetPropertyStr( ctx, js_widget_obj, "is_valid");
    JSValueConst js_widget_check_able = JS_GetPropertyStr( ctx, js_widget_obj, "check_able");
    JSValueConst js_widget_is_checked = JS_GetPropertyStr( ctx, js_widget_obj, "is_checked");
    JSValueConst js_widget_imgs       = JS_GetPropertyStr( ctx, js_widget_obj, "icons");

    int array_len = 0;
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_widget_imgs, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("imgs length  is null\n"); 
        for( int i = 0; i < 3; ++i )
        {
            cJSON *cJSON_ImageObj = cJSON_CreateObject();
            cJSON_AddStringToObject( cJSON_ImageObj,"img_res", "" );
            cJSON_AddStringToObject( cJSON_ImageObj,"img_format", "" );
            cJSON_AddItemToArray( cJSON_ImageArray, cJSON_ImageObj );
        }
    }else
    {
        JS_ToInt32( ctx, &array_len, obj_mem);
        //遍历数组，处理每个对象
        for( int i = 0; i < array_len; i++ )
        {
            JSValueConst js_widget_img_obj = JS_GetPropertyUint32( ctx, js_widget_imgs, i);
            
            if( i >= 4)
            {
                break;
            }
            if( !JS_IsObject( js_widget_img_obj ) )
            {
                Log("img Property not exist\n");
                cJSON *cJSON_ImageObj = cJSON_CreateObject();
                cJSON_AddStringToObject( cJSON_ImageObj,"img_res", "" );
                cJSON_AddStringToObject( cJSON_ImageObj,"img_format", "" );
                cJSON_AddItemToArray( cJSON_ImageArray, cJSON_ImageObj );
            }else
            {
                get_js_widget_iamge_data( ctx, js_widget_img_obj, cJSON_ImageArray );
            }
        }
    }
    
    ret = JS_ToInt32(ctx, &check_button.is_valid, js_widget_is_valid);
    ret = JS_ToInt32(ctx, &check_button.is_checked, js_widget_is_checked);

    cJSON_AddNumberToObject(ObjFirst,"is_valid",check_button.is_valid);
    cJSON_AddNumberToObject(ObjFirst,"is_checked",check_button.is_checked);

    cJSON_AddItemToObject( ObjFirst, "icons", cJSON_ImageArray );

   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);
}

void js_optionItem_decode( JSContext *ctx, JSValueConst js_widget_obj, cJSON *cJSON_ObjArray )
{
    int height = 0;
    int item_status = 0;
    char* str = NULL;
    char id[PEDK_WIDGET_ID_LENGTH] = "list_string";
    char type[PEDK_WIDGET_TYPE_LENGTH] = "list";
    char img_res[PEDK_WIDGET_IMAGE_PATH_LENGTH] = {0};
    char img_format[PEDK_WIDGET_IMAGE_FORMAT_LENGTH] = {0};
    char text[PEDK_WIDGET_TEXT_LENGTH] = {0};
    char text_color[PEDK_WIDGET_STYLE_COLOR_LENGTH] = {0};
    char font_size[PEDK_WIDGET_STYLE_FONT_SIZE_LENGTH] = {0};


    JSValueConst js_widget_id               = JS_GetPropertyStr( ctx, js_widget_obj, "id");
    JSValueConst js_widget_type             = JS_GetPropertyStr( ctx, js_widget_obj, "type");
    JSValueConst js_widget_height           = JS_GetPropertyStr( ctx, js_widget_obj, "height");

    JSValueConst js_widget_style_sheet      = JS_GetPropertyStr( ctx, js_widget_obj, "style_sheet");
    JSValueConst js_widget_style_sheet_text_color = JS_GetPropertyStr( ctx, js_widget_style_sheet, "text_color");
    JSValueConst js_widget_style_sheet_font_size = JS_GetPropertyStr( ctx, js_widget_style_sheet, "font_size");

    JSValueConst js_widget_item_status      = JS_GetPropertyStr( ctx, js_widget_obj, "item_status");

    JSValueConst js_widget_imgs             = JS_GetPropertyStr( ctx, js_widget_obj, "img");
        JSValueConst js_widget_imgs_res             = JS_GetPropertyStr( ctx, js_widget_imgs, "img_res");
        JSValueConst js_widget_imgs_format          = JS_GetPropertyStr( ctx, js_widget_imgs, "img_format");

    JSValueConst js_widget_text             = JS_GetPropertyStr( ctx, js_widget_obj, "text");

    str = JS_ToCString( ctx, js_widget_id );
    snprintf( id, PEDK_WIDGET_ID_LENGTH, "%s", str );
    JS_FreeCString( ctx, str );

    str = JS_ToCString(ctx, js_widget_type);
    snprintf( type, PEDK_WIDGET_TYPE_LENGTH, "%s", str );
    JS_FreeCString( ctx,str);

    JS_ToInt32(ctx, &height, js_widget_height);
    JS_ToInt32(ctx, &item_status, js_widget_item_status);

    snprintf( img_res, PEDK_WIDGET_IMAGE_PATH_LENGTH, "%s", APP_path_s );
    str = JS_ToCString(ctx, js_widget_imgs_res);
    snprintf( img_res + strlen(img_res), PEDK_WIDGET_IMAGE_PATH_LENGTH - strlen(img_res), "%s", str  );
    JS_FreeCString( ctx,str);
    //snprintf( image.img.img_res,JS_ToCString(ctx, js_widget_img_res) );
    str = JS_ToCString(ctx, js_widget_imgs_format);
    snprintf( img_format, PEDK_WIDGET_IMAGE_FORMAT_LENGTH, "%s", str);
    JS_FreeCString( ctx,str);


    str = JS_ToCString(ctx, js_widget_text);
    snprintf( text, PEDK_WIDGET_TEXT_LENGTH, "%s", str );
    JS_FreeCString( ctx,str);

    str = JS_ToCString(ctx, js_widget_style_sheet_text_color);
    strcpy( text_color, str );
    JS_FreeCString( ctx,str);

    str = JS_ToCString(ctx, js_widget_style_sheet_font_size);
    strcpy( font_size, str );
    JS_FreeCString( ctx,str);



    cJSON *ObjFirst             = cJSON_CreateObject();

    cJSON_AddStringToObject( ObjFirst,"id", id );
    cJSON_AddStringToObject( ObjFirst,"type", type );
    cJSON_AddNumberToObject( ObjFirst,"height", height );

    cJSON_AddNumberToObject( ObjFirst,"item_status",item_status );

    cJSON *cJSON_style_sheet = cJSON_CreateObject();

    cJSON *cJSON_img = cJSON_CreateObject();
    cJSON_AddStringToObject( cJSON_img,"img_res", img_res );
    cJSON_AddStringToObject( cJSON_img,"img_format", img_format );
    cJSON_AddItemToObject( ObjFirst, "img", cJSON_img );

    cJSON_AddStringToObject( ObjFirst,"text", text );

    cJSON *cJSON_style = cJSON_CreateObject();
    cJSON_AddStringToObject(cJSON_style,"text_color",text_color);
    cJSON_AddStringToObject(cJSON_style,"font_size",font_size);
    cJSON_AddItemToObject( ObjFirst, "style_sheet", cJSON_style );

    cJSON_AddItemToArray( cJSON_ObjArray, ObjFirst );

}

void js_list_string_decode( JSContext *ctx, JSValueConst js_widget_obj, cJSON *cJSON_ObjArray )
{
    int height = 30;
    int item_status = 0;
    char* str = NULL;
    char id[PEDK_WIDGET_ID_LENGTH] = "list_string";
    char type[PEDK_WIDGET_TYPE_LENGTH] = "list";
    char img_res[PEDK_WIDGET_IMAGE_PATH_LENGTH] = {0};
    char img_format[PEDK_WIDGET_IMAGE_FORMAT_LENGTH] = {0};
    char text[PEDK_WIDGET_TEXT_LENGTH] = {0};
    char text_color[PEDK_WIDGET_STYLE_COLOR_LENGTH] = {0};
    char font_size[PEDK_WIDGET_STYLE_FONT_SIZE_LENGTH] = {0};



    str = JS_ToCString( ctx, js_widget_obj );
    snprintf( text, PEDK_WIDGET_TEXT_LENGTH, "%s", str );
    JS_FreeCString( ctx,str);

    cJSON *ObjFirst             = cJSON_CreateObject();

    cJSON_AddStringToObject( ObjFirst,"id", id );
    cJSON_AddStringToObject( ObjFirst,"type", type );
    cJSON_AddNumberToObject( ObjFirst,"height", height );

    cJSON_AddNumberToObject( ObjFirst,"item_status",item_status );

    cJSON *cJSON_style_sheet = cJSON_CreateObject();

    cJSON *cJSON_img = cJSON_CreateObject();
    cJSON_AddStringToObject( cJSON_img,"img_res", img_res );
    cJSON_AddStringToObject( cJSON_img,"img_format", img_format );
    cJSON_AddItemToObject( ObjFirst, "img", cJSON_img );

    cJSON_AddStringToObject( ObjFirst,"text", text );

    cJSON *cJSON_style = cJSON_CreateObject();
    cJSON_AddStringToObject(cJSON_style,"text_color",text_color);
    cJSON_AddStringToObject(cJSON_style,"font_size",font_size);
    cJSON_AddItemToObject( ObjFirst, "style_sheet", cJSON_style );

    cJSON_AddItemToArray( cJSON_ObjArray, ObjFirst );

}


JSValue js_list_button_decode(JSContext *ctx, JSValueConst js_widget_obj,
                            cJSON * obj_widget_array)
{
    JSListButton list_button = {0};
    char list_item_group_text[256] = {0};
    int list_item_select_value = 0;
    JSValue obj = JS_UNDEFINED;
    int ret;
    char* str = NULL;
    
    //新建一个json数组，每个元素是一个对象
    //先生成每个对象
    cJSON *style = cJSON_CreateObject();

    cJSON *ObjFirst = cJSON_CreateObject();

    get_js_widget_base_data( ctx, js_widget_obj, ObjFirst,
                            &list_button.x, &list_button.y, &list_button.w, &list_button.h, list_button.id, list_button.type  );

    JSValueConst js_widget_group        = JS_GetPropertyStr( ctx, js_widget_obj, "list_item_group");

    cJSON *list_item_group = cJSON_CreateArray();

    int array_len = 0;
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_widget_group, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("list length  is null\n");         
    }else
    {

        JS_ToInt32( ctx, &array_len, obj_mem);
        
        //遍历数组，处理每个对象
        for( int i = 0; i < array_len; i++ )
        {
            JSValueConst js_widget_group_optionItem = JS_GetPropertyUint32( ctx, js_widget_group, i);

            if( JS_IsString( js_widget_group_optionItem ) )
            {
                js_list_string_decode( ctx, js_widget_group_optionItem, list_item_group );
            }
            else if( JS_IsObject(js_widget_group_optionItem) )
            {
                js_optionItem_decode( ctx, js_widget_group_optionItem, list_item_group );
            }
        }
    }

    array_len = 0;
    JSValueConst js_widget_select_value = JS_GetPropertyStr( ctx, js_widget_obj, "select_value");
    cJSON *select_value    = cJSON_CreateArray();

    obj_mem = JS_GetPropertyStr( ctx, js_widget_select_value, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("list length  is null\n");         
    }else
    {
        JS_ToInt32( ctx, &array_len, obj_mem);
        //遍历数组，处理每个对象
        for( int i = 0; i < array_len; i++ )
        {
            JSValueConst js_select_value_int = JS_GetPropertyUint32( ctx, js_widget_select_value, i);

            if( JS_IsNumber( js_select_value_int ) )
            {
                int val = 0;
                JS_ToInt32( ctx, &val, js_select_value_int);
                cJSON_AddItemToArray( select_value, cJSON_CreateNumber(val) );
            }
        }
    }

    cJSON_AddItemToObject(ObjFirst,"list_item_group",list_item_group);
    cJSON_AddItemToObject(ObjFirst,"select_value",select_value);

   //将对象添加到数组
    cJSON_AddItemToArray(obj_widget_array,ObjFirst);
}


static JSValue js_screenCtrl_draw(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{

    int ret = -1;
    char type[PEDK_WIDGET_TYPE_LENGTH];
    cJSON * jsonroot = cJSON_CreateObject();
    cJSON * ObjArr = cJSON_CreateArray();
    char * test_str = NULL;

    
    
    Log("start draw\n");
    //解析参数数组
    JSValueConst js_widgets_array = argv[0];

    //检查是否数组

    if(!JS_IsArray( ctx, js_widgets_array))
    {
        return JS_EXCEPTION;
    }
    //获取数组长度
    int array_len = 0;
    JSValue obj_mem = JS_GetPropertyStr( ctx, js_widgets_array, "length");
    if( JS_IsException(obj_mem) )
    {
        Log("Property not exist\n"); 
        return JS_EXCEPTION;
    }

    JS_ToInt32( ctx, &array_len, obj_mem);
    Log("array_len:%d\n",array_len);

    //遍历数组，处理每个对象
    for( int i = 0; i < array_len; i++ )
    {
        JSValueConst js_widget_obj = JS_GetPropertyUint32( ctx, js_widgets_array, i);
        if( !JS_IsObject( js_widget_obj ) )
        {
            return JS_EXCEPTION;
        }
        JSValueConst js_widget_type = JS_GetPropertyStr( ctx, js_widget_obj, "type");

        
        //判断控件类型
        snprintf( type, PEDK_WIDGET_TYPE_LENGTH, "%s", JS_ToCString(ctx, js_widget_type) );
        
        if( 0 == strcmp( type,"screen" ) )
        {
            js_screen_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"panel" ) )
        {
            js_screen_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"label" ) )
        {   
            js_label_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"button" ) )
        {   
            js_button_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"image" ) )
        {
            js_image_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"gif" ) )
        {
            js_gif_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"lineedit" ) )
        {   
            js_line_edit_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"checkbtn" ) )
        {   
            js_check_button_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"radio" ) )
        {   
            js_radio_button_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"radiogroup" ) )
        {   
            js_radio_group_decode( ctx, js_widget_obj, ObjArr );
        }

        else if( 0 == strcmp( type,"list" ) )
        {
            js_list_button_decode( ctx, js_widget_obj, ObjArr );
        }
        else if( 0 == strcmp( type,"animationimage" ) )
        {   
            js_animal_image_decode( ctx, js_widget_obj, ObjArr );
        }        
        else if( 0 == strcmp( type,"panel" ) )
        {   
            js_panel_decode( ctx, js_widget_obj, ObjArr );
        }
        
        else if( 0 == strcmp( type, "keyboard") )
        {
            js_Keyboard_decode( ctx, js_widget_obj, ObjArr );
        }        
    }   
    
    //将ObjArr加入到jsonroot
    cJSON_AddItemToObject(jsonroot,"UI_SCR_LAYOUT_T",ObjArr);
    test_str = cJSON_Print(jsonroot);
    //printf("test_str:%s\n",test_str);
    Log("send to mfp\n");
    
    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_DRAW,0,strlen(test_str),test_str);
    cJSON_Delete(jsonroot);
    
    js_free( ctx, test_str );
    
    return JS_NewInt32( ctx, ret );
}

static JSValue js_screenCtrl_redraw(JSContext *ctx, JSValueConst this_val,
                              int argc, JSValueConst *argv)
{

   int ret = -1;
   char type[PEDK_WIDGET_TYPE_LENGTH];
   cJSON * jsonroot = cJSON_CreateObject();
   cJSON * ObjArr = cJSON_CreateArray();
   char * test_str = NULL;

   
   
   Log("start draw\n");
   //解析参数数组
   JSValueConst js_widgets_array = argv[0];

   //检查是否数组

   if(!JS_IsArray( ctx, js_widgets_array))
   {
       return JS_EXCEPTION;
   }
   //获取数组长度
   int array_len = 0;
   JSValue obj_mem = JS_GetPropertyStr( ctx, js_widgets_array, "length");
   if( JS_IsException(obj_mem) )
   {
       Log("Property not exist\n"); 
       return JS_EXCEPTION;
   }

   JS_ToInt32( ctx, &array_len, obj_mem);
   Log("array_len:%d\n",array_len);

   //遍历数组，处理每个对象
   for( int i = 0; i < array_len; i++ )
   {
       JSValueConst js_widget_obj = JS_GetPropertyUint32( ctx, js_widgets_array, i);
       if( !JS_IsObject( js_widget_obj ) )
       {
           return JS_EXCEPTION;
       }
       JSValueConst js_widget_type = JS_GetPropertyStr( ctx, js_widget_obj, "type");
       
       //Log("type:%s\n",type);

       //判断控件类型
       snprintf( type, PEDK_WIDGET_TYPE_LENGTH, "%s", JS_ToCString(ctx, js_widget_type) );
       if( 0 == strcmp( type,"screen" ) )
       {
           js_screen_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"label" ) )
       {   
           js_label_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"button" ) )
       {   
           js_button_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"image" ) )
       {
           js_image_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"gif" ) )
       {
           js_gif_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"lineedit" ) )
       {   
           js_line_edit_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"checkbtn" ) )
       {   
           js_check_button_decode( ctx, js_widget_obj, ObjArr );
       }
       
       if( 0 == strcmp( type,"radio" ) )
       {   
            int is_radio_group = JS_HasProperty( ctx, js_widget_obj, "buttons");
            if( is_radio_group )
            {
                js_radio_group_decode( ctx, js_widget_obj, ObjArr );
            }
            else
            {
                js_radio_button_decode( ctx, js_widget_obj, ObjArr );
            }
       }
       if( 0 == strcmp( type,"animalimage" ) )
       {   
           js_animal_image_decode( ctx, js_widget_obj, ObjArr );
       }        
       if( 0 == strcmp( type,"panel" ) )
       {   
           js_panel_decode( ctx, js_widget_obj, ObjArr );
       }
       if( 0 == strcmp( type,"list" ) )
       {
            js_list_button_decode( ctx, js_widget_obj, ObjArr );
       }       
   }   
   
   //将ObjArr加入到jsonroot
   cJSON_AddItemToObject(jsonroot,"UI_SCR_LAYOUT_T",ObjArr);
   test_str = cJSON_Print(jsonroot);
   //printf("test_str:%s\n",test_str);
   Log("send to mfp\n");
   

    //redraw 仅重绘界面，不重新创建窗口
   ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_REDRAW,0,strlen(test_str),test_str);
   cJSON_Delete(jsonroot);
   
   return JS_NewInt32( ctx, ret );
}

  
static JSValue js_keyboard_draw(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    JSKEYBOARD *keyboard;
    JSValue obj = JS_UNDEFINED;
    JSValue proto;
    char * test_str = NULL;
    cJSON * jsonroot = cJSON_CreateObject();
    cJSON * ObjArr = cJSON_CreateArray();

    int ret;
    keyboard = js_mallocz(ctx, sizeof(*keyboard));

    snprintf(keyboard->id, PEDK_WIDGET_ID_LENGTH, "%s", JS_ToCString(ctx, argv[0]));
    snprintf(keyboard->type, PEDK_WIDGET_TYPE_LENGTH, "%s", JS_ToCString(ctx, argv[1]));

    //新建一个json数组，每个元素是一个对象
    //先生成每个对象

    cJSON *ObjFirst = cJSON_CreateObject();
    cJSON_AddStringToObject(ObjFirst,"id",keyboard->id);
    cJSON_AddStringToObject(ObjFirst,"type",keyboard->type);

   //将对象添加到数组
    cJSON_AddItemToArray(ObjArr,ObjFirst);

    //将ObjArr加入到jsonroot
    cJSON_AddItemToObject(jsonroot,"UI_SCR_LAYOUT_T",ObjArr);
    test_str = cJSON_Print(jsonroot);
    printf("test_str kb : %s\n",test_str);

    SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_DRAW_KEYBOARD,0,strlen(test_str),test_str);
    js_free(ctx, keyboard);
    cJSON_Delete(jsonroot);
    free(test_str);
}


static JSValue js_screen_brightness(JSContext *ctx, JSValueConst this_val,
                              int argc, JSValueConst *argv)
{

   int ret = -1;
   int brightness = 0;
      
   JS_ToInt32( ctx, &brightness, argv[0] );
   Log( "js set brightness %d\n", brightness );

   ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_BRIGHTNESS, 0 ,sizeof(brightness), &brightness);
   
   return JS_NewInt32( ctx, ret );
}

static JSValue js_screen_off(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{

 int ret = -1;
 int brightness = 0;
 
 Log( "js set brightness %d\n", brightness );

 ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_SCREEN_ON_OFF, 0 ,sizeof(brightness), &brightness);
 
 return JS_NewInt32( ctx, ret );
}

static JSValue js_screen_on(JSContext *ctx, JSValueConst this_val,
                              int argc, JSValueConst *argv)
{

   int ret = -1;
   int brightness = 50;
   
   Log( "js set brightness %d\n", brightness );

   ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_SCREEN_ON_OFF, 0 ,sizeof(brightness), &brightness);
   
   return JS_NewInt32( ctx, ret );
}
                              

static JSValue js_buzzerOff(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{

 int ret = -1;
 int buzzer = 0;
 
 Log( "js set buzzer %d\n", buzzer );

 ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_BUZZER, 0 ,sizeof(buzzer), &buzzer);
 
 return JS_NewInt32( ctx, ret );
}
                            
static JSValue js_buzzerOn(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{

 int ret = -1;
 int buzzer = 1;
 
 Log( "js set buzzer %d\n", buzzer );

 ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_BUZZER, 0 ,sizeof(buzzer), &buzzer);
 
 return JS_NewInt32( ctx, ret );
}
                            
static JSValue js_ledOff(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{

    int ret = -1;
    int led_id = 1;
    int led_color = 1;
    int data_buf[3] = {0};
    
    JS_ToInt32( ctx, &led_id, argv[0] );
    Log( "js turn off led_id %d\n", led_id );

    data_buf[0] = 0;
    data_buf[1] = led_id;
    data_buf[2] = led_color;

    //kanas只有一个状态灯，临时使用respond代表LED off
    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_LED, 0 ,sizeof(data_buf), data_buf);

    return JS_NewInt32( ctx, ret );
}
static JSValue js_ledOn(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{

    int ret = -1;
    int led_id = 1;
    int led_color = 1;
    int data_buf[3] = {0};
    
    JS_ToInt32( ctx, &led_id, argv[0] );
    JS_ToInt32( ctx, &led_color, argv[1] );
    Log( "js turn off led_id %d\n", led_id );
    Log( "js turn off led_color %d\n", led_color );

    data_buf[0] = 1;
    data_buf[1] = led_id;
    data_buf[2] = led_color;

        //kanas只有一个状态灯，临时使用respond代表LED on
    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_LED, 0 ,sizeof(data_buf), data_buf);

    return JS_NewInt32( ctx, ret );
}

static JSValue js_screen_drawExit(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1;
    
    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_LINK, 2 ,0, NULL);

    return JS_NewInt32( ctx, ret );
}
                            
static JSValue js_appEnded(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1;
    char app_id[PEDK_APP_ID_LENGTH];
    
    JSValueConst js_app_id = argv[0];
    snprintf( app_id, PEDK_APP_ID_LENGTH, "%s", JS_ToCString(ctx, js_app_id) );

    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_LINK, 1 ,strlen(app_id), app_id);

    return JS_NewInt32( ctx, ret );
}
                            
static JSValue js_appStarted(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1;
    char app_id[PEDK_APP_ID_LENGTH];
    
    JSValueConst js_app_id = argv[0];
    snprintf( app_id, PEDK_APP_ID_LENGTH, "%s", JS_ToCString(ctx, js_app_id) );

    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CTRL_LINK, 0 ,strlen(app_id), app_id);

    return JS_NewInt32( ctx, ret );
}

static JSValue js_setTimeoutForAppReturn(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1;
    int timeout = 0;
    
    JS_ToInt32( ctx, &timeout, argv[0] );

    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_SET_BACK_TIMEOUT, 1 ,sizeof(timeout), &timeout);
    Log(" ret:%d\n",ret);
    return JS_NewInt32( ctx, ret );
}

static JSValue js_getTimeoutForAppReturn(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1, timeout = 0;
    int buff_size = sizeof(timeout);



    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_GET_BACK_TIMEOUT, 1 ,sizeof(timeout), &timeout);
    if ( ret >= 0 )
    {
        ret = RecvMsgToMfp(MSG_MODULE_PANEL, MSG_PANEL_SUB_GET_BACK_TIMEOUT, &ret, &timeout, &buff_size, 5);
        if( ret >= 0 )
        {
            Log("get timeout success timeout:%d, buff_size:%d\n", timeout, buff_size );
            return JS_NewInt32( ctx, timeout );
        }
        else
        {
            Log("recv get timeout fail\n");
            return JS_NewInt32( ctx, ret );
        }
    }
    else
    {
        Log("get timeout fail\n");
        return JS_NewInt32( ctx, ret );
    }
}

static JSValue js_deleteTimeoutForAppReturn(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1;
    unsigned int timeout_switch = 0;
    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_DELETE_BACK_TIMEOUT, 1 ,sizeof(timeout_switch), &timeout_switch);
    Log(" ret:%d\n",ret);
    return JS_NewInt32( ctx, ret );
}

static JSValue js_changeToNativeWindow(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = -1;
    char native_window[PEDK_NATIVE_WINDOW_LENGTH] = "";
    JSValueConst js_native_window = argv[0];
    
    snprintf( native_window, PEDK_NATIVE_WINDOW_LENGTH, "%s", JS_ToCString(ctx, js_native_window) );
    ret = SendMsgToMfp(MSG_MODULE_PANEL,MSG_PANEL_SUB_CHANGE_TO_NATIVE_WINDOW, 1 ,strlen(native_window), native_window);
    Log(" ret:%d\n",ret);

    return JS_NewInt32( ctx, ret );
}



typedef struct JSCFunctionList
{
   const char *name;
   int length;
   JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_ui_funcs[] = {
   /* 函数入口名称，入参个数，QuickJS C 函数 */

   /* demo */
   
   {"js_screenCtrl_draw",       1,      js_screenCtrl_draw},
   {"js_screen_brightness",     1,      js_screen_brightness},
   {"js_screen_off",            0,      js_screen_off},
   {"js_screen_on",             0,      js_screen_on},
   {"js_buzzerOff",             0,      js_buzzerOff},
   {"js_buzzerOn",              0,      js_buzzerOn},
   {"js_ledOff",                1,      js_ledOff},
   {"js_ledOn",                 2,      js_ledOn},
   {"js_screen_drawExit",       0,      js_screen_drawExit},
   {"js_appStarted",            1,      js_appStarted},
   {"js_appEnded",              1,      js_appEnded},
   {"js_screenCtrl_redraw",     1,      js_screenCtrl_redraw},
   {"js_setTimeoutForAppReturn",     1,      js_setTimeoutForAppReturn},
   {"js_getTimeoutForAppReturn",     0,      js_getTimeoutForAppReturn},
   {"js_deleteTimeoutForAppReturn",  0,      js_deleteTimeoutForAppReturn},
   {"js_changeToNativeWindow",  1,      js_changeToNativeWindow},
       

};
///
const JSCFunctionList* getUiJSCFunctionList(int *length) {
   *length = countof(pesf_ui_funcs);
   return pesf_ui_funcs;
}

int js_ui_init(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;

   Log(" ui module init\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = getUiJSCFunctionList(&count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   get_app_path( ctx );
   return 0;
}
