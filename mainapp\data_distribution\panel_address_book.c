/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_address_book.c
 * @addtogroup panel_dc
 * @{
 * @brief panel dc get and change address book
 * <AUTHOR> 
 * @date 2024-02-24
 */

#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "event_manager/event_mgr.h"
#include "pol/pol_log.h"
#include "panel_event.h"
#include "public/msgrouter_main.h"
#include "pol/pol_string.h"
#include "panel_address_book.h"

#define PANLE_ADDR_DEBUG            1

static MAIL_ADDRBOOK_S email_address_book_s = {0};
static MAIL_GROUPLIST_S email_group_s = {0};
static FTP_ADDRBOOK_S ftp_book_s = {0};
static SMB_ADDRBOOK_S smb_book_s = {0};

/**
 * @brief panel set address book
 */
 
 int set_address_book(ADDRBOOK_OP_S* addr_op, uint32_t data_len)
{
    EVT_MGR_CLI_S* panel_event_client = get_panel_event_client();
    ADDRBOOK_OP_S addr_oprate = {0};
    pi_log_d("ADDRBOOK_OP_S size =>type:%d, =>oprate:%d\n",sizeof(addr_oprate.type),sizeof(addr_oprate.operation));
    
    if( NULL == addr_op || data_len != sizeof(ADDRBOOK_OP_S))
    {
        pi_log_d("oprate address book data error\n");
        return -1;
    }
    pi_log_d("panel oprate addr book,type:%d,oprate:%d\n",addr_op->type,addr_op->operation);
#if PANLE_ADDR_DEBUG
/*
    pi_strncpy(addr_op.u.mail_info.addr, "<EMAIL>",pi_strlen("<EMAIL>"));
    pi_strncpy(addr_op.u.mail_info.name, "aaa",pi_strlen("aaa"));
    addr_op.u.mail_info.group_id[0] = 0;
    addr_op.operation = AB_OP_ADD;
    addr_op.type = AB_TYPE_MAIL;
    pi_log_d("panel set mail addr name:%s,addr:%s\n",addr_op.u.mail_info.name,addr_op.u.mail_info.addr);
    */
#endif
    
    pi_log_d("panel set mail addr name:%s,addr:%s\n",addr_op->u.mail_info.name,addr_op->u.mail_info.addr);
    pi_event_mgr_notify( panel_event_client, EVT_TYPE_NET_ADDRESS_BOOK_REQUEST, addr_op, sizeof(ADDRBOOK_OP_S) );
    return 1 ;
}


/**
 * @brief panel update email address book
 * @param[in] mail_book 
 * @param[in] mail_book data size 
 */
 int update_email_addr_book(MAIL_ADDRBOOK_S* mail_book, uint32_t data_len)
{
    int ret = 0;
    if( mail_book == NULL || data_len != sizeof(MAIL_ADDRBOOK_S))
    {
        pi_log_e("panel get email address book error!\n");
        return -1;
    }
    pi_memcpy( &email_address_book_s, mail_book, data_len);

#if PANLE_ADDR_DEBUG
    pi_log_d("get email addr book, num:%d, idx:%d\n", email_address_book_s.num, email_address_book_s.idx);
    for( int i = 0; i <(sizeof(email_address_book_s.info)/sizeof(MAIL_ADDRINFO_S)); i++ )
    {
        pi_log_d("email[%d] name:%s, addr:%s,group:%d,record_id:%d\n", i, email_address_book_s.info[i].name, \
         email_address_book_s.info[i].addr,email_address_book_s.info[i].group_id[0],email_address_book_s.info[i].record_id);
    }
#endif
    ret = panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_EMAIL_ADDRESS_BOOK, NOTIFICATION_RESPONSE, &email_address_book_s, sizeof(email_address_book_s));

    return ret ;
}

/**
* @brief panel update email group book
* @param[in] mail_group 
* @param[in] mail_group data size 
*/
int update_email_group_book(MAIL_GROUPLIST_S* mail_group, uint32_t data_len)
{
    int ret = 0;
    if( data_len != sizeof(MAIL_GROUPLIST_S))
    {
     pi_log_e("panel get email group book error!\n");
     return -1;
    }
    pi_memcpy( &email_group_s, mail_group, data_len);

#if PANLE_ADDR_DEBUG
#endif

    ret = panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_EMAIL_GROUP_BOOK, NOTIFICATION_RESPONSE, &email_group_s, sizeof(email_group_s));

    return ret ;
}

/**
* @brief panel update ftp book
* @param[in] ftp_book 
* @param[in] ftp_book data size 
*/
int update_ftp_book(FTP_ADDRBOOK_S* ftp_book, uint32_t data_len)
{
    int ret = 0;
    if( data_len != sizeof(FTP_ADDRBOOK_S))
    {
      pi_log_e("panel get ftp book error!\n");
      return -1;
    }
    pi_memcpy( &ftp_book_s, ftp_book, data_len);

#if PANLE_ADDR_DEBUG
#endif

    ret = panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_FTP_ADDRESS_BOOK, NOTIFICATION_RESPONSE, &ftp_book_s, sizeof(ftp_book_s));

    return ret ;
}

/**
* @brief panel update ftp book
* @param[in] ftp_book 
* @param[in] ftp_book data size 
*/
int update_smb_book(SMB_ADDRBOOK_S* smb_book, uint32_t data_len)
{
    int ret = 0;
    if( data_len != sizeof(SMB_ADDRBOOK_S))
    {
       pi_log_e("panel get smb book error!\n");
       return -1;
    }
    pi_memcpy( &smb_book_s, smb_book, data_len);

#if PANLE_ADDR_DEBUG
#endif
    ret = panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_SMB_ADDRESS_BOOK, NOTIFICATION_RESPONSE, &smb_book_s, sizeof(smb_book_s));

    return ret ;

}

/**
* @brief update all addr book to panel when wakeup
*/
void update_all_address_book( void )
{
    panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_EMAIL_ADDRESS_BOOK, NOTIFICATION_RESPONSE, &email_address_book_s, sizeof(email_address_book_s));
    panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_EMAIL_GROUP_BOOK, NOTIFICATION_RESPONSE, &email_group_s, sizeof(email_group_s));
    panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_FTP_ADDRESS_BOOK, NOTIFICATION_RESPONSE, &ftp_book_s, sizeof(ftp_book_s));
    panel_send_data_u8(SETTING, SETTING_CMD_SCAN_UPDATE_SMB_ADDRESS_BOOK, NOTIFICATION_RESPONSE, &smb_book_s, sizeof(smb_book_s));

}




 
 /**                                                                                                                                                                  
  * @}
  */


