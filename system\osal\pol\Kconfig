config POL_DEBUG
	bool "ENABLE PANTUM OS LAYER DEBUG"
	depends on POL
	default n
	choice
		prompt "pantum os layer debug mode"
		depends on POL_DEBUG
		default POL_DEBUG_MODE_CALLER

	#config POL_DEBUG_MODE_BACKTRACE
	#    bool "backtrace mode"
	#    depends on POL_DEBUG
	#    help 
	#      DEBUG INFO WITH BACKTRACE INFO       

	config POL_DEBUG_MODE_CALLER
		bool "caller mode" 
		depends on POL_DEBUG 
		help 
		  DEBUG INFO WITH FILE AND LINE OF CALLER 
	endchoice

config POL_IO_DEBUG
	bool "ENABLE IO RELATIVE SYSTEM INTERFACE DEBUG "
    depends on POL_DEBUG
    default n

config POL_MEM_DEBUG
	bool "ENABLE MEM RETVALUE SYSTEM INTERFACE DEBUG "
    depends on POL_DEBUG
    default n
    
config POL_MEM_RECORD
	bool "ENABLE MEM RECORD "
    depends on POL_MEM_DEBUG
    default n

#config POL_MEM_RECORD_PATH
#	string " MEM RECORE PATH"
#    depends on POL_MEM_RECORD
#    default "/tmp/mem_record"

config POL_MEM_TRACE
	bool "ENABLE PANTUM OS LAYER MEM TRACE"
	default n
	depends on POL_MEM_DEBUG&&RBTREE 

config POL_MEM_TRACE_BACKTRACE_LV
	int "PANTUM OS LAYER MEM TRACE BACKTRACE LEVEL"
	range 0 10
	default 0
	depends on POL_MEM_TRACE
	help
      If IS 0 ,would not backtrace the caller;otherwise backtrace the caller with the number level


config POL_STRING_DEBUG
	bool "ENABLE STRING RELATIVE SYSTEM INTERFACE DEBUG "
    depends on POL_DEBUG
    default n

config POL_TIME_DEBUG
	bool "ENABLE TIME RETVALUE SYSTEM INTERFACE DEBUG "
    depends on POL_DEBUG
    default n

config POL_INPUT_CHECK
	bool "ENABLE PANTUM OS LAYER INPUT CHECK"
	default n
	depends on POL_DEBUG
	
config POL_INPUT_CHECK_ASSERT
	bool "ENABLE PANTUM OS LAYER INPUT CHECK ASSERT"
	default n
	depends on POL_INPUT_CHECK

config POL_RET_CHECK
	bool "ENABLE PANTUM OS LAYER RETVALUE CHECK"
	default n
	depends on POL_DEBUG 

config POL_RET_CHECK_ASSERT
	bool "ENABLE PANTUM OS LAYER RETVALUE CHECK ASSERT"
	default n
	depends on POL_RET_CHECK

