# EWS后端根目录
config NET_EWS_HOME_DIR
    string "WEB Service home directory"
    depends on NET
    default "/web/pantumweb"
    help
        "EWS home directory"

# 安全机三员管理，限制HTTP（80）端口
config NET_SAFETY_MACHINE
    bool "Safety machine specifications"
    depends on NET
    help
        "safety machine,limit http 80 port support"

# Bonjour模块
config NET_BONJOUR
    bool "Bonjour ----- base on mDNSResponder-1790.80.10"
    depends on NET
    help
        "bonjour support"

# eSCL service模块
config NET_ESCLSRV
    bool "ESCLSRV ----- The service base on eSCL(Electronic Scanner Control Language)"
    depends on NET
    select NET_IPPSRV
    help
        "escl support"

# FTP client模块
config NET_FTP
    bool "FTP --------- The client base on FTP(File Transfer Protocol)"
    depends on NET
    help
        "ftp support"

# IPP service模块
config NET_IPPSRV
    bool "IPPSRV ------ The service base on IPP(Internet Printing Protocol)"
    depends on NET
    help
        "ipp support"

# LPR/LPD打印协议处理模块（端口：515）
config NET_LPD
    bool "LPD --------- Line Printer Daemon"
    depends on NET
    help
        "lpd support"

# LLMNR协议处理模块
config NET_LLMNR
    bool "LLMNR ------- Link Local Multicast Name Resolution"
    depends on NET
    help
        "llmnr support"

# NetBIOS协议处理模块
config NET_NETBIOS
    bool "NetBIOS"
    depends on NET
    help
        "netbios support"

config NET_PCLOUD
    bool "PCloud ------ Pantum Cloud"
    depends on NET
    help
        "pcloud support"

# 9120端口管理模块
config NET_PORT9120
    bool "PORT9120"
    depends on NET
    help
        "port9120 support"

# RAW打印协议处理模块（端口：9100）
config NET_RAWPRINT
    bool "RAWPrint ---- RAWPrint by port 9100"
    depends on NET
    help
        "rawprint support"

# RAW扫描协议处理开关（端口：9200）
config NET_RAWSCAN
    bool "RAWScan ----- RAWPrint by port 9200"
    depends on NET
    help
        "rawscan support"

# SLP协议处理模块
config NET_SLP
    bool "SLP --------- Service Location Protocol"
    depends on NET
    help
        "slp support"

# SMTP client模块
config NET_SMTP
    bool "SMTP -------- The client base on SMTP(Simple Mail Transfer Protocol)"
    depends on NET
    help
        "smtp support"

# SNMP协议处理模块
config NET_SNMP
    bool "SNMP -------- Simple Network Management Protocol"
    depends on NET
    help
        "snmp support"

# 是否支持web端升级功能
config NET_UPGRADE_FIRMWARE
    bool "net upgrade firmware"
    depends on NET
    help
        "net upgrade firmware support"

# 是否支持白名单功能
config NET_WHITELIST
    bool "WHITELIST"
    depends on NET
    help
        "whitelist support"
        
# 是否支持扫描到网页
config NET_WEBSCAN
    bool "WEBSCAN"
    depends on NET
    help
        "webscan support"

# 是否支持WiFi
config NET_WIFI
    bool "WiFi"
    depends on NET
    help
        "wifi support"

# 是否支持有线网络
config NET_WIRED
    bool "wired"
    depends on NET
    help
        "wired support"

# WSD协议处理模块
config NET_WSD
    bool "WSD"
    depends on NET
    help
        "wsd support"
		
# 是否支持PLOG
config NET_PLOG
    bool "PLOG"
    depends on NET
    help
        "plog support"
