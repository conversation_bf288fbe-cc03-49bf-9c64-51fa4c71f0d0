#ifndef __HTTC_AUDIT_H_
#define __HTTC_AUDIT_H_

#include <linux/netlink.h>

#ifdef __cplusplus
extern "C" {
#endif

#define NETLINK_AUDITZONE	31

#define UDP_IP				"127.0.0.1"
#define UDP_PORT			9300

#define HASH_LEN			40
#define TIME_LEN			64
#define NAME_LEN			256

#define _BACKLOG_ 10


#define TPCM_HASH_SIZE			32
#define TPCM_NONCE_SIZE			32
#define BOOT_MEASURE_PROCESSNAME 32
#define REPORT_TIME_SIZE		32
#define DEFAULT_SIZE_64			64
#define PRO_HASH_SIZE			66


enum {
	AUDITZONE_TYPE_SET	= 1,
	AUDITZONE_TYPE_GET	= 2,
	AUDITZONE_TYPE_RECV	= 3,
	AUDITZONE_TYPE_ADD	= 4,
	AUDITZONE_TYPE_QUE	= 5,
};

enum {
	TYPE_WHITELIST		= 0,
	TYPE_FILE_ACCESS	= 1,
	TYPE_EXTERNAL_DEVICE	= 2,
	TYPE_DMEASURE		= 3,
	TYPE_BMEASURE		= 7,
	TYPE_AUDIT_SUM		,
};

enum {
	EXEC_TRIGGER		= 0x01,
	DYN_TRIGGER		= 0x02,
	FILE_TRIGGER		= 0x04,
	MODULE_TRIGGER		= 0x08,
	PERIODICITY_DMEASURE	= 0x10,
};

enum {
	OPERATE_EXEC		= 0x0001,
	OPERATE_WRITE		= 0x0002,
	OPERATE_READ		= 0x0004,
	OPERATE_APPEND		= 0x0008,
	OPERATE_DELETE		= 0x0010,
	OPERATE_MEASURE		= 0x0020,
	OPERATE_MOUNT		= 0x0040,
	OPERATE_CREATE		= 0x0080,  
	OPERATE_RENAME		= 0x0100,
	OPERATE_SYSCALL		= 0x0200,
	OPERATE_IDT		= 0x0400,
	OPERATE_NET		= 0x0800,
	OPERATE_FILESYSTEM	= 0x1000,
	OPERATE_MODULE		= 0x2000,
	OPERATE_TASK		= 0x4000,
	OPERATE_SECTION		= 0x8000,
};

enum {
	RESULT_SUCCESS		= 1,
	RESULT_FAIL		= 2,
};

typedef enum
{
	HTTC_SUCCESS_LOG		= 1,
	HTTC_FAIL_LOG		    = 2,
}HTTC_LOG_TYPE;

struct netlink_data
{
	struct nlmsghdr msg;
	char data[1024];
};

struct st_auditzone_data {
	int len;
	char data[0];
};

struct st_audit_msg_head {
	__u16 type;
	__u16 operate;
	__u16 result;
	unsigned int userid;
	int pid;
	long time;
	int total_len;
	int len_subject;
	int len_object;
	char sub_hash[HASH_LEN];
	char data[0];
} __attribute__((packed));

struct log_msg_st {
	unsigned char time[TIME_LEN];
	unsigned short int type;
	//unsigned short int operate;
	int8_t operate[5];/*type为白名单类型时operate为固定值“E”，表示”执行”操作；type为动态度量类型时，operate为固定值PD，表示周期度量的含义，无实际意义；
					   type为文件访问控制类型时，有如下含义：W-写 R-读 A-追加 D-删除 M-度量 O-挂载 C-创建 B-重命名 W|A-追加*/
	unsigned int userid;
	unsigned int pid;
	unsigned char hash[HASH_LEN];
	unsigned char subject[NAME_LEN];
	unsigned char object[NAME_LEN];
	unsigned short int result;
} __attribute__((packed));

enum {
	RECORD_SUCCESS	= 1,
	RECORD_FAIL	= 2,
	RECORD_NO	= 4,
	RECORD_ALL	= 8,
};

struct log_policy_st {
	int audit[TYPE_AUDIT_SUM];
};

typedef enum{
    ENUM_HTTC_START_OK,
    ENUM_HTTC_START_FAIL,
    ENUM_HTTC_NOT_START,
}HTTC_START_STATUS;

#define HTTC_FAIL_LOG_FILE_PATH        "/settings/httc_fail_log"   // 可信失败日志存放路径
#define HTTC_SUCESS_LOG_FILE_PATH      "/tmp/httc_success_log"   // 可信成功日志存放路径
#define HTTC_TEMP_LOG_FILE_PATH        "/settings/httc_temp_log"   // 可信临时日志存放路径
#define HTTC_WEB_FAIL_LOG_PATH         "/tmp/httc_web_fail_log"   // webpage 导出失败可信日志路径
#define HTTC_WEB_SUCESS_LOG_PATH       "/tmp/httc_web_success_log"   // webpage 导出成功可信日志路径

/* 获取当前日志条数 */
extern int HttcGetLogNumber(HTTC_LOG_TYPE log_type);
/* 记录日志 */
//int HttcSaveLogToFlash(HTTC_LOG_TYPE log_type, struct log_msg_st *pLog);
/* 导出日志 */
/*
    Uint32 export_start         index of start secure log
    Uint32 export_end          index of end secure log
    return  0       -- success
    non-0   -- failed
*/
extern int HttcExportLog(HTTC_LOG_TYPE log_type, unsigned int  export_start,unsigned int  export_end);
extern int Httc_Socket_prolog(void);
extern int HttcGetVersion(char * _tpcm_ver);
extern void HttcServerStart(void);
extern void HttcServerStop(void);
extern void HTTC_GetVersion(char* tpcm_version, int tpcm_version_len, char* tsb_version, int tsb_version_len);

#if CONFIG_HTTC
int get_httc_result();
int httc_export_logfile(HTTC_LOG_TYPE log_type, const char *file_path);
#else
#define get_httc_result()
#define httc_export_logfile(a,b)
#endif

#ifdef __cplusplus
extern "C" {
#endif
#endif	/* _HTTC_LOG_H_ */
