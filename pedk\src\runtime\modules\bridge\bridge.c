/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file bridge.c
 * @addtogroup bridge
 * @{
 * @addtogroup bridge
 * @autor 
 * @date 2024-06-11
 * @brief bridge init
 */
 
#include "runtime/modules/bridge/bridge.h"
#include "runtime/modules/module_manager.h"
#include <quickjs.h>
#include "runtime/runtime.h"
#include "trans/transmission.h"
#include "manager/format.h"

/**
 * @brief 通过bridge模块把数据发送到JS里
 * 
 * @param prt ：运行时
 * @param data_length ：数据长度
 * @param data ：数据（字符串）
 */
void send_to_bridge(PeSFRunTime* prt, uint16_t data_length, const char* msg)
{
    JSValue value = JS_NewArrayBufferCopy(prt->qjs_ctx, (const unsigned char*)msg, data_length);
    JSValue argv[] = { value };
    
    JSValue global = JS_GetGlobalObject(prt->qjs_ctx);
    JSValue bridge = JS_GetPropertyStr(prt->qjs_ctx, global, "bridge");

    if (!JS_IsObject(bridge)) {
        LOG_E("bridge","bridge is not object");
        return;
    }

    JSValue on_msg = JS_GetPropertyStr(prt->qjs_ctx, bridge, "on_msg");
    if (JS_IsFunction(prt->qjs_ctx, on_msg)) {
        LOG_D("bridge","befor bridge call");
        JS_Call(prt->qjs_ctx, on_msg, bridge, sizeof(argv)/sizeof(argv[0]), argv);
    }

    JS_FreeValue(prt->qjs_ctx, on_msg);
    JS_FreeValue(prt->qjs_ctx, bridge);
    JS_FreeValue(prt->qjs_ctx, global);
}

JSValue js_on_msg(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    return JS_UNDEFINED;
}

static int32_t a2p_msg_send(uint8_t rtid, uint16_t data_length, uint8_t* data)
{
    // 此处注意，因为多app线程存在共同使用外部传输接口的时机，所以在发送前要加锁。
    // 1.制作发送消息
    uint16_t msg_length;
    uint8_t* sendbuf = (uint8_t*)malloc(data_length + 3);
    make_a2p_msg(sendbuf, &msg_length, data, data_length, rtid);
    // 2.发送
    transport_send(sendbuf, msg_length);

    free(sendbuf);
}

JSValue js_send_msg(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    const char* str = NULL;
    size_t str_len = 0;

    LOG_D("bridge","js_send_msg");
    if (!JS_IsString(argv[0])) {
        return JS_UNDEFINED;
    }
    str = JS_ToCStringLen(ctx, &str_len, argv[0]);

    a2p_msg_send(prt->dynamic_property.rtid, (uint16_t)str_len, (uint8_t*)str);

    return JS_UNDEFINED;
}

//bridge模块初始化函数
static void bridge_init()
{
    LOG_I("bridge","bridge_init\n");
}

//bridge模块释放函数
static void bridge_release()
{
    LOG_I("bridge","bridge_release\n");
}

//bridge模块实例化函数
static void bridge_instance(PeSFRunTime* prt)
{
    JSContext* ctx = prt->qjs_ctx;
    JSValue global = JS_GetGlobalObject(ctx);

    // 注册 console.log 到 global 对象中
    JSValue bridge = JS_NewObject(ctx);
    JS_SetPropertyStr(ctx, bridge, "on_msg", JS_NewCFunction(ctx, js_on_msg, "on_msg", 1));
    JS_SetPropertyStr(ctx, bridge, "send_msg", JS_NewCFunction(ctx, js_send_msg, "send_msg", 1));
    JS_SetPropertyStr(ctx, global, "bridge", bridge);

    // 释放 global 对象的引用
    JS_FreeValue(ctx, global);
}

//bridge模块去实例化函数
static void bridge_generalization(PeSFRunTime* prt)
{
}

//bridge模块注册
static ModuleContext bridge = {
    .module_name = "bridge",
    .module_init = bridge_init,
    .module_release = bridge_release,
    .module_instance = bridge_instance,
    .module_generalization = bridge_generalization
};
MODULE_REGISTER(bridge);
/**
 * @}
 */
 
