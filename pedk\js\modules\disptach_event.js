/*eventProcess*/
const Module_Main = {
	MSG_MODULE_PANEL : 0,
    MSG_MODULE_COPY : 1,
    MSG_MODULE_PRINT : 2,
    MSG_MODULE_SCAN : 3,
    MSG_MODULE_LOWPOWER : 4,
    MSG_MODULE_USBH : 5,
    MSG_MODULE_USBD : 6,
    MSG_MODULE_CAPABILITIESLIST : 7,
    MSG_MODULE_WHITELIST : 8,
    MSG_MOUDLE_USB_ICCARD : 9,
    MSG_MOUDLE_BEEP : 10,
    MSG_MODULE_NET : 11,
    MSG_MODULE_STATUS : 12,
	MSG_MODULE_JOBCTL : 13,
    MSG_MODULE_TOTAL_PAGES : 15,
    MSG_MODULE_HTTP : 16,
    MSG_MODULE_TEST : 101,
    MSG_MODULE_ERR : 102
};
const Module_Sub = {
        //panel 0~99
        MSG_PANEL_SUB_DRAW : 0,
        MSG_PANEL_SUB_DRAW_SCREEN : 1,
        MSG_PANEL_SUB_DRAW_LABEL : 2,
        MSG_PANEL_SUB_DRAW_BUTTON : 3,
        MSG_PANEL_SUB_DRAW_IMAGE : 4,
        MSG_PANEL_SUB_DRAW_CHECK_BUTTON : 5,
        MSG_PANEL_SUB_DRAW_RADIO_BUTTON : 6,
        MSG_PANEL_SUB_DRAW_LINE_EDIT : 7,
        MSG_PANEL_SUB_DRAW_ANIMAL_IMAGE : 8,
        MSG_PANEL_SUB_CTRL : 9,
        MSG_PANEL_SUB_CTRL_BRIGHTNESS : 10,
        MSG_PANEL_SUB_CTRL_SCREEN_STATUS : 11,
        MSG_PANEL_SUB_CTRL_BUZZER : 12,
        MSG_PANEL_SUB_CTRL_KEY : 13,
        MSG_PANEL_SUB_CTRL_LED : 14,
        MSG_PANEL_SUB_CTRL_LINK : 15,
        MSG_PANEL_SUB_TOUCH_CB : 16,

        //COPY 100~199
        MSG_COPY_SUB  :  100,
        MSG_COPY_SUB_JOB_START  :  101,
        MSG_COPY_SUB_JOB_CANCEL :  102,

        //PRINT 200~299
        MSG_PRINT_SUB_JOB_START : 200,
        MSG_PRINT_SUB_JOB_CANCEL : 201,
        MSG_PRINT_SUB_JOB_STATE :  202,

        //SCAN 300~399
        MSG_SCAN_SUB_JOB_START : 300,
        MSG_SCAN_SUB_JOB_CANCEL : 301,
        MSG_SCAN_SUB_GET_ADF_PAGE_NUM : 302,
        MSG_SCAN_SUB_SENT_ADF_PAGE_NUM : 303,
        MSG_SCAN_SUB_GET_FB_PAGE_NUM : 304,
        MSG_SCAN_SUB_SENT_FB_PAGE_NUM : 305,
        MSG_SCAN_SUB_GET_SCAN_PAGE_NUM : 306,
        MSG_SCAN_SUB_SENT_SCAN_PAGE_NUM : 307,
        MSG_SCAN_SUB_GET_FRONT_FPGAVERSION : 308,
        MSG_SCAN_SUB_SENT_FRONT_FPGAVERSION : 309,
        MSG_SCAN_SUB_GET_BACK_FPGAVERSION : 310,
        MSG_SCAN_SUB_SENT_BACK_FPGAVERSION : 311,

        //LOWPOWER 400~499
        MSG_LOWPOWER_SUB_GET  :  400 ,
        MSG_LOWPOWER_SUB_SET  :  401,
		MSG_LOWPOWER_SUB_REPORT  :402,

        //USBH  500~599
        MSG_USBH_SUB  :  500,

        //USBD  600~699
        MSG_USB_DEVICE_ENABLE  :  600,

        //CAPABILITIESLIST 700~799
        MSG_CAPABILITIESLIST_SUB_GET_LEN  :  700,
        MSG_CAPABILITIESLIST_SUB_GET_LIST  :  701,


        //WHITELIST 800~899
        MSG_WHITELIST_SUB_GET : 800,
        MSG_WHITELIST_SUB_SET : 801,
        MSG_WHITELIST_SUB_GET_MAC : 802,
        MSG_WHITELIST_SUB_SET_MAC : 803,

        //ICCARD 900~999
        MSG_GET_ICCAED_INFO : 900,

        //BEEP 1000~1099
        MSG_BEEP_GET_DURATION : 1000,
        MSG_BEEP_SET_DURATION : 1001,
        MSG_BEEP_GET_FREQUENCY : 1002,
        MSG_BEEP_SET_FREQUENCY : 1003,
        MSG_BEEP_GET_VOLUME : 1004,
        MSG_BEEP_SET_VOLUME : 1005,
        MSG_BEEP_START_WORK : 1006,
        MSG_BEEP_STOP_WORK : 1007,
        MSG_BEEP_GET_WORK_STATUS : 1008,

        //NET 1100~1199
        MSG_NET_SUB_GET_EMAIL_ADDR : 1100,
        MSG_NET_SUB_SET_EMAIL_ADDR : 1101,

        //STATUS 1200~1299
        MSG_STATUS_SUB : 1200,
        MSG_STATUS_SUB_ID : 1201,
	    MSG_STATUS_SUB_TYPE : 1202,
	    MSG_STATUS_SUB_MODULE : 1203,
	    MSG_STATUS_SUB_PRI : 1204,
	    MSG_STATUS_SUB_NOTIFY_PUSH : 1205,
		MSG_STATUS_SUB_NOTIFY_REMOVE : 1206,
		MSG_STATUS_SUB_LIST_LEN : 1207,

		//JOBCTL
		MSG_JOBCTL_SUB : 1300,
        MSG_JOBCTL_SUB_NOTIFY : 1301,
        MSG_JOBCTL_SUB_CANCEL : 1302,
        MSG_JOBCTL_SUB_NET_UPLOAD_INFO : 1307,

        MSG_ERR_SUB : 1400,

        MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_COLOR : 1500,
        MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MONO : 1501,
        MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MEDIA_SIZE : 1502,
        MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_SHEETS : 1503,

	MSG_HTTP_SUB : 1600,

        //TEST
        MSG_TEST_SUB :  10000

}
/*定义结构体*/
class ObserverStruct{
	constructor(obj, callback, main, sub){
		this.obj = obj;
		this.func = callback;
		this.main = main; //
		this.sub = sub;
	}
	DisptachEvent(main, sub, respond, size, data){
		//将信息状态通知给模块
		if(main === this.main && sub === this.sub){
			this.func(this.obj, respond, data)
		}
	}
	IsThisFunc(obj, callback, main, sub){
		if(obj === this.obj && callback === this.func && main === this.main && sub === this.sub){
			return true;
		}
		return false;
	}
}

/*创建单例类*/
class ObserverManager{
	constructor(){
		if(ObserverManager.instance)
		{
			return ObserverManager.instance;
		}
		ObserverManager.instance = this;
		this.listeners = [];
	}

	addListeners(obj, callback, main, sub){
		if(typeof callback !== 'function'){
			throw TypeError("Listener Function")
		}
		this.listeners.push( new ObserverStruct(obj, callback, main, sub) )
		return true;
	}

	removeListeners(obj, callback, main, sub){
		this.listeners = this.listeners.filter(listener => !listener.IsThisFunc(obj, callback, main, sub))

		return true;
	}

	notify(main, sub, respond, size, data){
		this.listeners.forEach(listener => listener.DisptachEvent(main, sub, respond, size, data));
	}
}

function swap32(val){
    return ((val & 0xFF) << 24) | ((val & 0xFF00) << 8) | ((val >> 8) & 0xFF00) | ((val >> 24) & 0xFF);
}

bridge.on_msg = (info)=> {
        // 数据会以多个参数的形式传入，这里的处理仅仅是举例，实际项目在这基础上封装
		console.log('js function execute')

        //解析数据流
        let dataView = new DataView(info);
        let value1 = swap32(dataView.getInt32(0));
        let value2 = swap32(dataView.getInt32(4));
        let value3 = swap32(dataView.getInt32(8));
        let value4 = swap32(dataView.getInt32(12));

        let value5 = '';
        for(let i=0;i<dataView.byteLength-16; i++){
            value5 += String.fromCharCode(dataView.getUint8(i+16));
        }


		//一致开始分发事件
		let instance = new ObserverManager();
		instance.notify(value1, value2, value3, value4, value5);


		return "ok";

 }

globalThis.pedk.ObserverManager = ObserverManager
globalThis.pedk.Module_Main = Module_Main
globalThis.pedk.Module_Sub = Module_Sub


/*以下是demo*/

/*
class CopyJob{
	constructor(){

	}
	SetState(obj, respond, data){
		console.log('copy Job Get respond:', respond)
		console.log('copy Job Get State:', data)
	}
}
*/



