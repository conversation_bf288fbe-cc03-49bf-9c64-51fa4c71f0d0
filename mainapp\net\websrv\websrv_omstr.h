#ifndef __WEBSRV_OMSTR__
#define __WEBSRV_OMSTR__

#define WEB_SEC_ADMIN_NAME          "SecAdmin"
#define WEB_AUD_ADMIN_NAME          "AuditAdmin"
#define WEB_SYS_ADMIN_NAME          "SysAdmin"

#define CONSTRUCT_ADDRESS_CONFIG(obj, type, var, func, ifid)                        \
    if ( obj == NULL ) {                                                            \
        obj = (type *)pi_zalloc(sizeof(type));                                      \
        RETURN_IF(obj == NULL, NET_WARN);                                           \
        if ( ifid != IFACE_ID_ANY ) {                                               \
            obj->var = (uint8_t)(!!func(DATA_MGR_OF(webomctx), ifid));              \
        }                                                                           \
    }

#define CONSTRUCT_STACONN_CONFIG(obj)                                               \
    if ( obj == NULL ) {                                                            \
        obj = (WIFI_CONN_CONF_S *)pi_zalloc(sizeof(WIFI_CONN_CONF_S));              \
        RETURN_IF(obj == NULL, NET_WARN);                                           \
        netdata_get_sta_ssid(DATA_MGR_OF(webomctx), obj->ssid, sizeof(obj->ssid));  \
        netdata_get_sta_psk(DATA_MGR_OF(webomctx), obj->psk, sizeof(obj->psk));     \
        obj->sec_mode = netdata_get_sta_sec_mode(DATA_MGR_OF(webomctx));            \
    }

#define CONSTRUCT_SYSTIME_CONFIG(obj)                                               \
    if ( obj == NULL ) {                                                            \
        obj = (struct tm *)pi_zalloc(sizeof(struct tm));                            \
        RETURN_IF(obj == NULL, NET_WARN);                                           \
        systime_get(obj);                                                           \
    }

#define DESTRUCT_CONFIG(obj)                                                        \
    if ( obj != NULL ) {                                                            \
        pi_free(obj);                                                               \
        obj = NULL;                                                                 \
    }

typedef enum
{
    WEBSRV_CONN_CLOSE = -60,
    WEBSRV_OTHERERROR_START = -50,
    WEBSRV_RESET_FAIL = -43,

    WEBSRV_ADDRESS_REPEAT = -30,
    WEBSRV_EXPORT_LOG_FAIL = -29,
    WEBSRV_EXPORT_LOG_OK = -28,

    WEBSRV_GET_WIFI_OK = -24,
    WEBSRV_SET_WIFI_FAIL = -23,
    WEBSRV_SET_WIFI_OK = -22,

    WEBSRV_SET_OID_FAIL = -19,

    WEBSRV_SYSUPGRADE_FAIL = -18,
    WEBSRV_SYSUPGRADE_OK = -17,

    WEBSRV_CERTIFICATE_MAKE = -16,
    WEBSRV_CERTIFICATE_FAIL = -15,
    WEBSRV_CERTIFICATE_OK = -14,

    WEBSRV_FILE_UPLOAD_TOOBIG = -13,
    WEBSRV_FILE_UPLOAD_OK = -12,
    WEBSRV_FILE_UPLOAD_FAIL = -11,

    WEBSRV_WPS_CONNECTING = -10,
    WEBSRV_WPS_CONNECTION_OK = -9,
    WEBSRV_WPS_CONNECTION_FAIL = -8,

    WEBSRV_SET_HOSTNAME_INUSED = -7,
    WEBSRV_SET_SLEEPTIME_FAIL = -6,
    WEBSRV_RESERT_NETWORK_OK = -5,
    WEBSRV_SET_IPV4_INUSED = -4,
    WEBSRV_OTHERERROR_END = -3,

    WEBSRV_CHECK_PASSWORD_OK = -3,
    WEBSRV_CHECK_PASSWORD_FAIL = -2,
    WEBSRV_FAIL = -1,
    WEBSRV_OK = 0
}
WEBSRV_RCODE_E;

struct websrv_omstr_table;
typedef struct websrv_omstr_table   WEBOM_TABLE_S;

struct websrv_omstr_group;
typedef struct websrv_omstr_group   WEBOM_GROUP_S;

typedef struct websrv_omstr_context
{
    NET_CTX_S*          net_ctx;
    NET_IPV4_CONF_S*    ipv4_conf[IFACE_ID_NUM];
    NET_DNSV4_CONF_S*   dnsv4_conf[IFACE_ID_NUM];
    NET_IPV6_CONF_S*    ipv6_conf[IFACE_ID_NUM];
    WIFI_CONN_CONF_S*   wifi_conf;
    struct tm*          time_conf;
    int32_t             admin_id;
    int32_t             modfiy_data;
}
WEBOM_CTX_S;

int32_t         websrv_omstr_set_val        (WEBOM_CTX_S* webomctx, const char* omstr, int32_t index, const char* val);

int32_t         websrv_omstr_get_val        (WEBOM_CTX_S* webomctx, WEBOM_GROUP_S* group, const char* omstr, int32_t index, char* buf, size_t buf_size);

WEBOM_GROUP_S*  websrv_omstr_get_group      (const char* module);

int32_t         websrv_omstr_page_support_init(NET_CTX_S* net_ctx);


#endif /* __WEBSRV_OMSTR__ */
