#include <unistd.h>
#include <stdio.h>
#include <gtest/gtest.h>
#include <pol/pol_types.h>
#include <pol/pol_threads.h>
#include "cmd.h"

#define cmd_callback(name)     \
    int32_t name(int32_t argc, char *argv[]) \
{ \
    int32_t i=0;\
    printf("[%d]enter %s,argc is %d \n",getpid(),__func__,argc);\
    while(i<argc)\
    printf("%s\n",argv[i++]);\
}

    cmd_callback(test1_case1)
    cmd_callback(test1_case2)
    cmd_callback(test2_case1)
    cmd_callback(test2_case2)

//demo
TEST(cmd,test0)
{    
    //gtest断言比较 接口的返回值是否和预期值一致
    EXPECT_EQ(0, pi_threads_prolog());
    EXPECT_EQ(0, cmd_prolog("cmd1"));
    EXPECT_EQ(0, cmd_register("test1","case1",test1_case1,NULL));
    EXPECT_EQ(0, cmd_register("test1","case2",test1_case2,NULL));
    EXPECT_EQ(0, cmd_register("test2","case1",test2_case1,NULL));
    EXPECT_EQ(0, cmd_register("test2","case2",test2_case2,NULL));

}

