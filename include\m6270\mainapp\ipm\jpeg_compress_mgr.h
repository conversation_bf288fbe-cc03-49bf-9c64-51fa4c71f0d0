/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       jpeg_compress_mgr.c
 * @addtogroup ipm
 * @{
 * @addtogroup ipm_unit_submodule
 * <AUTHOR>
 * @date       2023-6-7
 * @version    v1.0
 * @details    jpeg compress mgr
 */


#ifndef     _JPEG_COMPRESS_MGR_H
#define     _JPEG_COMPRESS_MGR_H

#include <sys/types.h>
#include <sys/stat.h>

#include "jpeghw_api.h"
#include "jpeghw_lib.h"
#include "jpeghw_dbg.h"
#include "jpeghw_dbg.h"
#include "jhw_mem_test.h"
#include "memAPI.h"
#include "test_common.h"
#include "platform_api.h"
#include "BigBuffer.h"
#include "scan_status.h"
#include <argp.h>
#include "jhw_dev.h"
#include "pol/pol_log.h"

#define         Log(a,fmt,...)                pi_log_i(fmt,##__VA_ARGS__)
#define         Error(fmt,...)                pi_log_w(fmt,##__VA_ARGS__)

/**
 * @brief jpeg encode out callback context
 */
typedef struct jpeg_encode_strip_out_s
{
    void*       data;
    uint32_t    datalen;

    uint32_t    last_buffer;

}JPEG_ENCODE_OUT;

/**
* @brief jpeg_encode done callback
* @param[in] contex jpeg callback context
* @param[in] jpeg_encode_out jpeg encode out data
* @return int \n
* @retval 0
* <AUTHOR>
* @date 2023-6-7
*/
typedef int (*JPEG_ENCODE_DONE_CALLBACK)(void *contex,JPEG_ENCODE_OUT jpeg_encode_out);

/**
 * @brief jpeg encode context
 */
typedef struct
{
    MEMOBJ_P                 mem_handle;
    uint32_t                 out_buffer_len;
    void *                    context;
    JPEG_ENCODE_DONE_CALLBACK done_callback;

}JPEG_ENCODE_CONTEXT_DATA;

/**
 * @brief jpeg decode out callback context
 */
typedef struct jpeg_decode_strip_out_s
{
    void*       data;
    uint32_t    datalen;

    uint32_t    last_buffer;

}JPEG_DECODE_OUT;

/**
 * @brief jpeg decode context
 */
typedef struct
{
    MEMOBJ_P                 mem_handle;
    void *                    context;
    struct BigBuffer_s **in_buf_strip;
    uint32_t num_strips;
    uint32_t index;

}JPEG_DECODE_CONTEXT_DATA;

/**
* @brief jpeg_decode done callback
* @param[in] contex jpeg callback context
* @param[in] jpeg_decode_out jpeg decode out data
* @return int \n
* @retval 0
* <AUTHOR>
* @date 2023-6-7
*/
typedef int (*JPEG_DECODE_DONE_CALLBACK)(void *contex,JPEG_DECODE_OUT jpeg_decode_out);

/**
* @brief jpeg_decode input done callback
* @param[in] input_buffer input buffer
* @param[in] bytes_in_buffer buffer bytes
* @return int \n
* @retval 0
* <AUTHOR>
* @date 2023-6-7
*/
typedef void (*JPEG_DECODE_INPUT_CALLBACK)(struct BigBuffer_s *input_buffer,uint32_t bytes_in_buffer);

/**
 * @brief get out buffer func
 * @param[in] jhwcinfo  jpeghw compress struct
 * @return BigBuffer_s* \n
 * @retval BigBuffer_s object
 * <AUTHOR>
 * @date 2023-6-7
 */
struct BigBuffer_s* get_outbuf_fnc(struct jpeghw_compress_struct * jhwcinfo);      //获取准备输出数据

/**
 * @brief scanline timeout
 * @param[in] info  jpeghw common struct
 * @param[in] time_in_seconds  timeout time
 * @return bool \n
 * @retval false
 * <AUTHOR>
 * @date 2023-6-7
 */
bool scanline_timeout(struct jpeghw_common_struct *info, uint32_t time_in_seconds) ;        //扫描数据超时


/**
 * @brief jpeg compress image
 * @param[in] jpeg_jhwcinfo jpeghw compress struct
 * @param[in] eoi last band
 * @param[in] band heigth
 * @param[in] big_buffer BigBuffer_s object
 * @return bool \n
 * @retval true : success / false : fail
 * <AUTHOR>
 * @date 2023-6-7
 */
bool jpeg_compress_image(struct jpeghw_compress_struct *jpeg_jhwcinfo,int eoi,int strip_lines ,struct BigBuffer_s *big_buffer);

/**
 * @brief jpeg close compress image
 * @param[in] jpeg_jhwcinfo jpeghw compress struct
 * @param[in] abort colse or not jpeg compress
 * @return void \n
 * @retval null
 * <AUTHOR>
 * @date 2023-6-7
 */
void jpeg_close_compress_image(struct jpeghw_compress_struct *jpeg_jhwcinfo,bool abort);

/**
* @brief jpeg open compress image
* @param[in] bpp  image depth
* @param[in] width  image width
* @param[in] height  image heigth
* @param[in] quanlity  compress the quality of the image
* @param[in] out_buffer_len  out buffer length
* @param[in] mem_handle  MEMOBJ_P object
* @param[in] JPEG_ENCODE_DONE_CALLBACK  jpeg_encode callback func
* @param[in] contex  callback context
* @return struct jpeghw_compress_struct* \n
* @retval  jpeg_jhwcinfo : success / null : fail
* <AUTHOR>
* @date 2023-6-7
*/
struct jpeghw_compress_struct * jpeg_open_compress_image(int bpp,int width,int height ,int quanlity,
                                        uint32_t out_buffer_len,void* mem_handle,JPEG_ENCODE_DONE_CALLBACK done_callback,void *contex);

/**
 * @brief get dma buffer from pix
 * @param[in] jhwcinfo  jpeghw compress struct
 * @param[in] jpegoutput_buffer  BigBuffer_s object
 * @param[in] bytes_in_buffer  buffer bytes
 * @return int \n
 * @retval 0 : success / -1 : fail
 * <AUTHOR>
 * @date 2023-6-7
 */
int jpeg_write_compress_image(struct jpeghw_compress_struct * jhwcinfo,struct BigBuffer_s* jpegoutput_buffer,uint32_t bytes_in_buffer);

/**
 * @brief outbuf ready func
 * @param[in] jhwcinfo  jpeghw compress struct
 * @param[in] output_buffer  BigBuffer_s object
 * @param[in] bytes_in_buffer  buffer bytes
 * @return bool \n
 * @retval true
 * <AUTHOR>
 * @date 2023-6-7
 */
bool outbuf_ready_fnc(struct jpeghw_compress_struct * jhwcinfo,
                              struct BigBuffer_s* output_buffer, uint32_t bytes_in_buffer);

/**
* @brief jpeg need input func
* @param[in] jhwdinfo jpeghw decompress struct
* @param[in] bytes buffer bytes
* @return BigBuffer_s* \n
* @retval BigBuffer_s object
* <AUTHOR>
* @date 2023-6-7
*/
struct BigBuffer_s * need_input_fnc(struct jpeghw_decompress_struct * jhwdinfo, uint32_t *bytes);

/**
 * @brief jpeg header decompress
 * @param[in] hdr_info JPEG header struct
 * @param[in] head_buffer head buffer
 * @param[in] bufflen head_buffer bytes
 * @param[in] head_end_pos jpeg head bytes
 * @return int \n
 * @retval 1 : success / 0 : fail
 * <AUTHOR>
 * @date 2023-6-7
 */
int jpeg_header_decompress(JPEG_header_struct *hdr_info,uint8_t *head_buffer,int bufflen,int *head_end_pos);

/**
* @brief jpeg open decompress image
* @param[in] hdr_info  JPEG_header struct
* @param[in] out_strip_height  out image height
* @param[in] num_strips  in image band nums
* @param[in] in_buf_strip  in image band buffer
* @param[in] mem_handle  MEMOBJ_P object
* @param[in] done_callback  jpeg decompress done callback func
* @param[in] contex  callback context
* @return struct jpeghw_decompress_struct* \n
* @retval  jpeg_decompress_jhwcinfo : success / null : fail
* <AUTHOR>
* @date 2023-6-7
*/
struct jpeghw_decompress_struct *  jpeg_open_decompress_image(JPEG_header_struct hdr_info,uint32_t out_strip_height,int num_strips,
                                                  struct BigBuffer_s *in_buf_strip[],void* mem_handle,
                                                  JPEG_DECODE_DONE_CALLBACK done_callback,void *contex);
/**
* @brief jpeg close compress image
* @param[in] jpeg_jhwcinfo jpeghw decompress struct
* @param[in] abort colse or not jpeg decompress
* @return void \n
* @retval null
* <AUTHOR>
* @date 2023-6-7
*/
void jpeg_close_decompress_image(struct jpeghw_decompress_struct *jpeg_jhwcinfo,bool abort);

/**
* @brief jpeghwd parse SOF
* @param[in] jpeginfo jpeghw JPEG_header struct
* @param[in] buff_ptr header buffer
* @param[in] buff_len header buffer bytes
* @return uint8_t* \n
* @retval  : height_address success / 0 : fail
* <AUTHOR>
* @date 2023-6-7
*/
uint8_t* jpeghwd_parse_sof(JPEG_header_struct* jpeginfo, uint8_t *buff_ptr, uint32_t buff_len);

/**
* @brief jpeghwd parse SOS
* @param[in] jpeginfo jpeghw JPEG_header struct
* @param[in] buff_ptr header buffer
* @param[in] buff_len header buffer bytes
* @return uint8_t* \n
* @retval  : OK success / FAIL : fail
* <AUTHOR>
* @date 2023-6-7
*/
int jpeghwd_parse_sos(JPEG_header_struct* jpeginfo, uint8_t *buff_ptr, uint32_t buff_len);

/**
* @brief jpeghwd parse DQT
* @param[in] jpeginfo jpeghw JPEG_header struct
* @param[in] buff_ptr header buffer
* @param[in] buff_len header buffer bytes
* @return int \n
* @retval  : OK success / FAIL : fail
* <AUTHOR>
* @date 2023-6-7
*/
int jpeghwd_parse_dqt(JPEG_header_struct* jpeginfo, uint8_t *buff_ptr, uint32_t buff_len);

/**
* @brief jpeghwd parse DHT
* @param[in] jpeginfo jpeghw JPEG_header struct
* @param[in] buff_ptr header buffer
* @param[in] buff_len header buffer bytes
* @return int \n
* @retval  : OK success / FAIL : fail
* <AUTHOR>
* @date 2023-6-7
*/
int jpeghwd_parse_dht(JPEG_header_struct* jpeginfo, uint8_t *buff_ptr, uint32_t buff_len);

/**
* @brief jpeghwd parse dri
* @param[in] jpeginfo jpeghw JPEG_header struct
* @param[in] buff_ptr header buffer
* @param[in] buff_len header buffer bytes
* @return int \n
* @retval  : OK success / FAIL : fail
* <AUTHOR>
* @date 2023-6-7
*/
int jpeghwd_parse_dri(JPEG_header_struct* jpeginfo, uint8_t *buff_ptr, uint32_t buff_len);

#endif
