/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netbios.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief NetBIOS
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netmisc.h"
#include "netsock.h"

#define NETBIOS_MLOCK_UN()          { if (s_nb_ctx != NULL) pi_mutex_unlock(s_nb_ctx->mutex); }
#define NETBIOS_MLOCK_EX()          { if (s_nb_ctx != NULL) pi_mutex_lock(s_nb_ctx->mutex);   }

#define NETBIOS_BUFFER_SIZE         ( 0x4000 ) /* 16 * 1024 */

typedef struct netbios_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         thread;
    PI_MUTEX_T          mutex;
    PI_SOCKET_T         sockfd[IFACE_ID_NUM];
    char                ipstr[IFACE_ID_NUM][IPV4_ADDR_LEN];
    uint8_t             changed;
    uint8_t             inited;
}
NETBIOS_CTX_S;

static NETBIOS_CTX_S*   s_nb_ctx = NULL;

/**
 * @brief       The function of decoding the netbios protocol name.
 * @param[in]   src     : The name of the before decoding.
 * @param[out]  dst     : The name of the decoding.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void netbios_decode_name(char* src, char* dst)
{
    int32_t i;

    for ( i = 0; i < 15; ++i )
    {
        dst[i] = (((src[2*i] - 'A') << 4) | (src[2*i+1] - 'A'));
        if ( dst[i] == 0x20 )
        {
            break;
        }
    }
    dst[i] = '\0';
}

/**
 * @brief       The function of processing netbios request.
 * @param[in]   buf     : The LLMNR request data received buff.
 * @param[in]   nbuf    : LLMNR requests the length of the data received.
 * @param[in]   from    : The LLMNR request information source structure.
 * @param[in]   ifid    : Network link type.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void netbios_process_request(char* buf, size_t nbuf, struct sockaddr_storage* from, IFACE_ID_E ifid)
{
    char    hostname[HOSTNAME_LEN];
    char    req_name[HOSTNAME_LEN];
    size_t  len = 50;
    int32_t ret;
    uint8_t op_code;

    if ( nbuf < 50 || (buf[4] == 0 && buf[5] == 0) )
    {
        NET_WARN("invalid request: nbuf = %u, buf[4] = %d, buf[5] = %d", nbuf, buf[4], buf[5]);
        return;
    }

    gethostname(hostname, sizeof(hostname));
    netbios_decode_name(&buf[13], req_name);
    RETURN_IF(strcasecmp(hostname, req_name) != 0, NET_NONE);

    op_code = (buf[2] >> 3) & 0x0f;
    if ( op_code == 5 )
    {
        buf[2] = 0xad;
        buf[3] = 0x87;
    }
    else if ( op_code == 0 )
    {
        buf[2] = 0x85;
        buf[3] = 0x00;
    }

    buf[4]  = 0x00;
    buf[5]  = 0x00;
    buf[6]  = 0x00;
    buf[7]  = 0x01;
    buf[8]  = 0x00;
    buf[9]  = 0x00;
    buf[10] = 0x00;
    buf[11] = 0x00;

    buf[len++] = 0x00;
    buf[len++] = 0x00;
    buf[len++] = 0x00;
    buf[len++] = 0x00;
    buf[len++] = 0x00;
    buf[len++] = 0x06;
    buf[len++] = 0x00;
    buf[len++] = 0x00;

    inet_pton(AF_INET, s_nb_ctx->ipstr[ifid], buf + len);
    len += 4;

    NET_TRACE("NetBIOS response: send '%s' to '%s' by %s", s_nb_ctx->ipstr[ifid], inet_ntoa(((struct sockaddr_in *)from)->sin_addr), IFACE_NAME(ifid));
    ret = sendto(s_nb_ctx->sockfd[ifid], buf, len, 0, (struct sockaddr *)from, (socklen_t)sizeof(*from));
    if ( ret < 0 )
    {
        NET_DEBUG("NetBIOS sendto failed: %d<%s>", errno, strerror(errno));
    }
}

/**
 * @brief       The function of determine whether you need to update the netbios connection status.
 * @param[in]   changed : It needs to be updated.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void netbios_update_link_status(uint8_t changed)
{
    NETBIOS_MLOCK_EX();
    s_nb_ctx->changed |= changed;
    NETBIOS_MLOCK_UN();
}

/**
 * @brief       The callback function of update the callback of the netbios protocol connection state
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void netbios_update_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(s_nb_ctx == NULL || s_nb_ctx->inited == 0, NET_WARN);
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    netbios_update_link_status((uint8_t)(s->subject_status & 0xFF));
}

/**
 * @brief       The function of reload netbios protocol socket
 * @param[in]   readfds : Read socket
 * @return      Reassigned read socket
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static int32_t netbios_reload_socket(fd_set* readfds)
{
    const char* ifname = NULL;
    int32_t     maxfd = 0;
    uint8_t     changed;

    NETBIOS_MLOCK_EX();
    changed = s_nb_ctx->changed;
    s_nb_ctx->changed = 0;
    NETBIOS_MLOCK_UN();

    FD_ZERO(readfds);
    for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        ifname = IFACE_NAME(ifid);
        if ( STRING_IS_EMPTY(ifname) )
        {
            continue;
        }

        /* 当前链路状态有变更，重载socket */
        if ( (LINK_CHANGE_IPV4_BASE << ifid) & changed )
        {
            NET_DEBUG("'%s' has changed, socket reload", ifname);
            if ( s_nb_ctx->sockfd[ifid] != INVALID_SOCKET )
            {
                pi_closesock(s_nb_ctx->sockfd[ifid]);
                s_nb_ctx->sockfd[ifid] = INVALID_SOCKET;
            }

            netdata_get_ipv4_addr(DATA_MGR_OF(s_nb_ctx), ifid, s_nb_ctx->ipstr[ifid], sizeof(s_nb_ctx->ipstr[ifid]));
            if ( (netdata_get_iface_running(DATA_MGR_OF(s_nb_ctx), ifid) == 0) || STRING_IS_EMPTY(s_nb_ctx->ipstr[ifid]) )
            {
                NET_DEBUG("'%s' is skipped", ifname);
                continue;
            }

            NET_DEBUG("load '%s' address '%s' to NetBIOS service", ifname, s_nb_ctx->ipstr[ifid]);
            s_nb_ctx->sockfd[ifid] = netsock_create_multicast(NETBIOS_PORT, ifid, IPV4);
            if ( s_nb_ctx->sockfd[ifid] == INVALID_SOCKET )
            {
                continue;
            }
        }

        if ( s_nb_ctx->sockfd[ifid] != INVALID_SOCKET )
        {
            FD_SET(s_nb_ctx->sockfd[ifid], readfds);
            if ( maxfd < s_nb_ctx->sockfd[ifid] )
            {
                maxfd = s_nb_ctx->sockfd[ifid];
            }
        }
    }

    return maxfd;
}

/**
 * @brief       The netbios handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void* netbios_thread_handler(void* arg)
{
    IFACE_ID_E              ifid;
    fd_set                  rfds;
    socklen_t               slen;
    struct sockaddr_storage from;
    struct timeval          tv;
    char                    buf[NETBIOS_BUFFER_SIZE];
    int32_t                 maxfd;
    ssize_t                 rlen;
    int32_t                 ret;

    NET_DEBUG("Starting NetBIOS thread handler");
    while (1)
    {
        if ( (maxfd = netbios_reload_socket(&rfds)) <= 0 )
        {
            pi_msleep(2000);
            continue;
        }

        tv.tv_sec  = 2;
        tv.tv_usec = 0;
        ret = select(maxfd + 1, &rfds, NULL, NULL, &tv);
        if ( ret < 0 )
        {
            NET_WARN("select failed: %d<%s>", errno, strerror(errno));
            netbios_update_link_status(LINK_CHANGE_IPV4_ALL);
            continue;
        }
        else if ( ret == 0 )
        {
            continue;
        }

        for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
        {
            if ( s_nb_ctx->sockfd[ifid] != INVALID_SOCKET && FD_ISSET(s_nb_ctx->sockfd[ifid], &rfds) )
            {
                memset(buf, 0, sizeof(buf));
                slen = (socklen_t)sizeof(from);
                rlen = recvfrom(s_nb_ctx->sockfd[ifid], buf, sizeof(buf), 0, (struct sockaddr *)&from, &slen);
                if ( rlen > 0 )
                {
                    netbios_process_request(buf, rlen, &from, ifid);
                }
                else
                {
                    NET_WARN("recvfrom %s failed(%d): %d<%s>", IFACE_NAME(ifid), rlen, errno, strerror(errno));
                    netbios_update_link_status(LINK_CHANGE_IPV4_ALL);
                }
            }
        }
    }

    return NULL;
}

int32_t netbios_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_nb_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_nb_ctx = (NETBIOS_CTX_S *)pi_zalloc(sizeof(NETBIOS_CTX_S));
    RETURN_VAL_IF(s_nb_ctx == NULL, NET_WARN, -1);

    do
    {
        memset(s_nb_ctx->sockfd, INVALID_SOCKET, sizeof(s_nb_ctx->sockfd)); /* initial value is INVALID_SOCKET(-1) */
        s_nb_ctx->changed = LINK_CHANGE_IPV4_ALL;
        s_nb_ctx->net_ctx = net_ctx;

        BREAK_IF((s_nb_ctx->mutex = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        s_nb_ctx->thread = pi_thread_create(netbios_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "netbios_thread_handler");
        BREAK_IF(s_nb_ctx->thread == INVALIDTHREAD, NET_WARN);

        s_nb_ctx->inited = 1;
        ret = 0;
    }
    while ( 0 );

    NET_INFO("NetBIOS initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netlink_observer(net_ctx, netbios_update_callback, NULL);
    }
    else
    {
        netbios_epilog();
    }
    return ret;
}

void netbios_epilog(void)
{
    if ( s_nb_ctx != NULL )
    {
        s_nb_ctx->inited = 0;
        if ( s_nb_ctx->thread != INVALIDTHREAD )
        {
            pi_thread_destroy(s_nb_ctx->thread);
        }
        if ( s_nb_ctx->mutex != INVALIDMTX )
        {
            pi_mutex_destroy(s_nb_ctx->mutex);
        }
        pi_free(s_nb_ctx);
        s_nb_ctx = NULL;
    }
}
/**
 *@}
 */
