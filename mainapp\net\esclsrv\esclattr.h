#ifndef _ESCL_ATTR_H_
#define _ESCL_ATTR_H_

struct escl_attribute;
typedef struct escl_attribute   ESCL_ATTR_S;

void*               esclattr_convert_request_data   (ESCL_ATTR_S* pattr, int32_t jobid, int32_t module_id);

SCAN_MODE_E         esclattr_get_input_source       (ESCL_ATTR_S* pattr);

FILE_TYPE_E         esclattr_get_file_type          (ESCL_ATTR_S* pattr);

ESCL_ATTR_S*        esclattr_parse_scan_settings    (const char* xml);

#endif /* _ESCL_ATTR_H_ */
