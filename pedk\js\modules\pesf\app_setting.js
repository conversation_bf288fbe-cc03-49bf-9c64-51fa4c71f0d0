export function getAllAppSettingValue()
    {
		console.log('[debug]getAllAppSettingValue' );
		let retstr = js_getAllAppSettingValue();

		console.log(retstr);
		const retobj = JSON.parse(retstr);

		for(let key in retobj)
		{
			retobj[key] = JSON.parse(retobj[key]);
		}

		const map = new Map(Object.entries(retobj));
		console.log(map);

		return map;
    }

export function getAppSettingValue(name)
	{
		console.log('[debug]get App Setting Value' );
		return JSON.parse(js_getAppSettingValue(name));
	}
export function deleteAppSettingValue(name)
	{
		console.log('[debug]delete App Setting Value' );
		return js_deleteAppSettingValue(name);
	}
export function setAppSettingValue(cstring,cobject)
	{
		console.log('[debug]set App Setting Value' );
		const str = JSON.stringify(cobject);
		console.log(str);
		return js_setAppSettingValue(cstring,str);
	}

globalThis.pedk.device.appsetting.getAllAppSettingValue = getAllAppSettingValue
globalThis.pedk.device.appsetting.getAppSettingValue = getAppSettingValue
globalThis.pedk.device.appsetting.deleteAppSettingValue = deleteAppSettingValue
globalThis.pedk.device.appsetting.setAppSettingValue = setAppSettingValue


