threadInit();
let test = new globalThis.pesf.MailAddressBook();

let email ={};
email.mail_name = "789";
email.mail_addr = "<EMAIL>";
email.group_num = [10,10,10,10,10,10,10,10,10,10];


var email_get_test = test.getEmailAddr(1);
console.log("email_get_test.pesf_mail_index ", email_get_test.pesf_mail_index);
console.log("email_get_test.pesf_mail_name ", email_get_test.pesf_mail_name);
console.log("email_get_test.pesf_mail_addr ", email_get_test.pesf_mail_addr);
console.log("email_get_test.pesf_group_num ", email_get_test.pesf_group_num);

var email_add_test = test.addEmailAddr(email);
console.log("email_addr_test", email_add_test);


