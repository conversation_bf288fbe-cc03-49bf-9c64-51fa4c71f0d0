/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pedk_http.c
 * @pedk net
 * @{
 * @http netmodules
 * <AUTHOR>
 * @date 2024-12-24
 * @brief pedk http function for pedk
 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "pedk_http.h"

// 全局 HTTPS 配置，默认关闭所有验证
HTTPS_CONFIG_PARAM_S global_https_config = {
    .verify_certificate = 1,    // 默认验证证书
    .verify_host_mode = 2,      // 默认验证主机名
    .client_cert_path = "",     // 默认无证书路径
    .client_key_path = "",      // 默认无私钥路径
    .key_password = ""          // 默认无密码
};

static SCAN_HTTP_PARAM_S g_scan_http_param;

// 设置 HTTPS 配置参数
int pedk_http_set_https_config(const HTTPS_CONFIG_PARAM_S *config)
{
    if (!config)
    {
        printf("Error: Configuration parameter is NULL.\n");
        return -1;
    }

    // 检查参数有效性
    if (config->verify_certificate < 0 || config->verify_certificate > 1)
    {
        printf("Error: Invalid verify_certificate value: %d\n", config->verify_certificate);
        return -1;
    }

    if (config->verify_host_mode < 0 || config->verify_host_mode > 2)
    {
        printf("Error: Invalid verify_host_mode value: %d\n", config->verify_host_mode);
        return -1;
    }

    // 更新全局配置
    memcpy(&global_https_config, config, sizeof(HTTPS_CONFIG_PARAM_S));

    printf("HTTPS configuration updated successfully:\n");
    printf("verify_certificate: %d\n", global_https_config.verify_certificate);
    printf("verify_host_mode: %d\n", global_https_config.verify_host_mode);
    printf("client_cert_path: %s\n", global_https_config.client_cert_path);
    printf("client_key_path: %s\n", global_https_config.client_key_path);
    printf("key_password: %s\n", global_https_config.key_password);

    return 0;
}

// 获取 HTTPS 配置参数
int pedk_http_get_https_config(HTTPS_CONFIG_PARAM_S *config)
{
    if (!config)
    {
        printf("Error: Output parameter is NULL.\n");
        return -1;
    }

    // 返回全局配置
    memcpy(config, &global_https_config, sizeof(HTTPS_CONFIG_PARAM_S));
    return 0;
}

// 验证 URL 和 Headers 的合法性
static int validate_scan_http_param(const SCAN_HTTP_PARAM_S *param)
{
    if (param == NULL)
    {
        return -1; // 参数为空
    }

    // 检查 URL 是否为空
    if (strlen(param->url) == 0)
    {
        return -2; // URL 为空
    }

    // 检查 Headers 长度是否超限
    if (strlen(param->headers) >= sizeof(param->headers))
    {
        return -3; // Headers 超限
    }

    return 0; // 验证通过
}

// 验证并存储参数
int pedk_http_set_url_header_params(SCAN_HTTP_PARAM_S *scan_http_param)
{
    if (!scan_http_param)
    {
        printf("Error: invalid scan_http_param.\n");
        return -1; // 参数无效
    }

    // 验证参数
    int ret = validate_scan_http_param(scan_http_param);
    if (ret != 0)
    {
        return ret; // 验证失败，返回错误码
    }

    // 存储参数
    memcpy(&g_scan_http_param, scan_http_param, sizeof(SCAN_HTTP_PARAM_S));

    return 0; // 成功
}

//获取已存储的参数
int pedk_http_get_url_header_params(SCAN_HTTP_PARAM_S *scan_http_param)
{
    if (!scan_http_param)
    {
        return -1; // 参数无效
    }

    // 检查是否已存储有效参数
    if (strlen(g_scan_http_param.url) == 0)
    {
        return -2; // 没有有效数据
    }

    // 返回存储的参数
    memcpy(scan_http_param, &g_scan_http_param, sizeof(SCAN_HTTP_PARAM_S));

    return 0; // 成功
}

/**
 *@}
 */
