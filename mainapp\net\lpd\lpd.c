﻿/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file lpd.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-25
 * @brief Line-Printer-Daemon
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netsock.h"
#include "ringbuf.h"

#define LPD_REPLY_CODE(v)               ( {uint8_t _v = (uint8_t)v; int32_t _ret = QIO_WRITE(ptask->pqiotcp, &_v, sizeof(_v)); _ret;} )
#define LPD_LOCAL_PORT                  ( (uint16_t)(netdata_get_lpd_switch(DATA_MGR_OF(s_lpd_ctx)) ? LPD_PORT : 0) )

/*
 * @brief For LPD_STATE_REQUEST_COMMAND
 */
#define LPD_REQ_START_PRINT             ( 0x01 )
#define LPD_REQ_RECV_JOB                ( 0x02 )
#define LPD_REQ_SEND_SHORT_QUEUE_STATE  ( 0x03 )
#define LPD_REQ_SEND_LONG_QUEUE_STATE   ( 0x04 )
#define LPD_REQ_REMOVE_JOB              ( 0x05 )
                                               
/*
 * @brief For LPD_STATE_JOB_SUB_COMMAND
 */
#define LPD_JOB_CANCEL                  ( 0x01 )
#define LPD_JOB_RECV_CTRL_FILE          ( 0x02 )
#define LPD_JOB_RECV_DATA_FILE          ( 0x03 )

#define LPD_NAME_SIZE                   ( 140 )

typedef enum
{
    LPD_STATE_REQUEST_COMMAND = 0,
    LPD_STATE_JOB_SUB_COMMAND,
    LPD_STATE_JOB_CTRL_STREAM,
    LPD_STATE_JOB_DATA_STREAM,
    LPD_STATE_END,
    LPD_STATE_ERR
}
LPD_STATE_E;

typedef struct lpd_task
{
    RING_BUF_S*     ringbuf;    ///< The ring buffer is used to save the data received from the TCP stream.
    QIO_S*          pqiotask;   ///< The QIO_S object pointer of task data stream.
    QIO_S*          pqiotcp;    ///< The QIO_S object pointer of TCP data stream.
    int32_t         rwtotal;    ///< The bytes total of receive from TCP stream and write to ring buffer.
    int32_t         cfasize;    ///< The data size of ctrl file.
    int32_t         dfasize;    ///< The data size of data file.
    uint8_t         working;    ///< The LPD task working flag.
    uint8_t         job_end;    ///< The LPD job end flag.
    uint8_t         job_err;    ///< The LPD job err flag.
}
LPD_TASK_S;

typedef struct lpd_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     srv_tid;
    int32_t         pfd[2];
}
LPD_CTX_S;

static LPD_CTX_S*   s_lpd_ctx = NULL;

/**
 * @brief       The callback function of QIO_CLOSE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current LPD task.
 * @return      Close result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t lpd_task_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, LPD_TASK_S, ptask);

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    NET_DEBUG("LPD job end");
    ptask->working = 0;

    return 0;
}

/**
 * @brief       The callback function of QIO_READABLE(pqio) or QIO_WRITEABLE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current LPD task.
 * @return      Poll result
 * @retval      > 0     : this QIO_S object can be read or written\n
 *              =+0     : poll timeout\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t lpd_task_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, LPD_TASK_S, ptask);
    int32_t block = 0;
    int32_t ms = 0;
    int32_t rv = 0;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( what & QIO_POLL_READ )
    {
        if ( tos > 0 || (tos == 0 && tous >= 0) )
        {
            ms += (tos  > 0 ? (tos  * 1000) : 0);
            ms += (tous > 0 ? (tous / 1000) : 0);
        }
        else
        {
            block = 1;
        }

        do
        {
            rv = ringbuf_readable(ptask->ringbuf);
            if ( rv != 0 )
            {
                break;
            }
            else if ( ptask->job_err || ptask->job_end )
            {
                NET_WARN("err(%u) end(%u)", ptask->job_err, ptask->job_end);
                rv = -1;
                break;
            }
            pi_msleep(100);
            ms -= 100;
        }
        while ( ms > 0 || block );
    }
    else
    {
        NET_INFO("unsupported operation(%d)", what);
        rv = -1;
    }

    return rv;
}

/**
 * @brief       The callback function of QIO_READ(pqio), read data from ring buffer.
 * @param[in]   pqio    : The QIO_S object pointer for current LPD task.
 * @return      Read result
 * @retval      >=0     : read the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t lpd_task_read(QIO_S* pqio, void* buf, size_t count)
{
    DECL_PRIV(pqio, LPD_TASK_S, ptask);
    int32_t rv = 0;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    rv = ringbuf_read(ptask->ringbuf, buf, count);
    if ( rv == 0 && ptask->job_err )
    {
        NET_INFO("err(%u)", ptask->job_err);
        rv = -1;
    }

    return rv;
}

/**
 * @brief       The callback function of QIO_WRITE(pqio), write data to TCP stream.
 * @param[in]   pqio    : The QIO_S object pointer for current LPD task.
 * @return      Write result
 * @retval      >=0     : write the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t lpd_task_write(QIO_S* pqio, void* buf, size_t count)
{
    NET_INFO("lpd ring buffer cannot be written");
    return 0;
}

/**
 * @brief       The callback function of QIO_SEEK(pqio), unsupported.
 * @param[in]   pqio    : The QIO_S object pointer for current LPD task.
 * @return      Seek result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t lpd_task_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

/**
 * @brief       LPD task handling function, when current state is LPD_STATE_REQUEST_COMMAND.
 * @param[in]   ptask   : The LPD_TASK_S object pointer.
 * @param[in]   buf     : buffer pointer.
 * @param[in]   len     : buffer length.
 * @return      The next state of this state machine.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static LPD_STATE_E lpd_parse_request_command(LPD_TASK_S* ptask, char* buf, int32_t len)
{
    LPD_STATE_E next_state = LPD_STATE_REQUEST_COMMAND;
    char        queuename[LPD_NAME_SIZE];
    char        attribute[LPD_NAME_SIZE];
    uint8_t     cmd = (uint8_t)buf[0];
    int32_t     ret = 0;

    NET_DEBUG("receive cmd: 0x%02x + %s", cmd, buf + 1);
    RETURN_VAL_IF(len < 2 || buf[len - 1] != '\n', NET_WARN, LPD_STATE_ERR);

    switch ( cmd )
    {
    case LPD_REQ_START_PRINT: /* 0x01 + queuename + '\n' */
        {
            ret = sscanf(buf, "\x01%139[^\n]\n", queuename);
            NET_DEBUG("LPD_REQ_START_PRINT ret(%d) queuename(%s)", ret, queuename);
            break;
        }
    case LPD_REQ_RECV_JOB:
        {
            ret = sscanf(buf, "\x02%139[^\n]\n", queuename);
            NET_DEBUG("LPD_REQ_RECV_JOB ret(%d) queuename(%s)", ret, queuename);
            ret = LPD_REPLY_CODE(0);
            if ( ret == 1 )
            {
                next_state = LPD_STATE_JOB_SUB_COMMAND;
            }
            else
            {
                next_state = LPD_STATE_ERR;
            }
            break;
        }
    case LPD_REQ_SEND_SHORT_QUEUE_STATE:
    case LPD_REQ_SEND_LONG_QUEUE_STATE:
        {
            memset(attribute, 0, sizeof(attribute));
            ret = sscanf(buf, "%c%139[^ \n] %139[^\n]\n", &cmd, queuename, attribute);
            NET_DEBUG("LPD_REQ_SEND_QUEUE_STATE(0x%X) ret(%d) queuename(%s) attribute(%s)", cmd, ret, queuename, attribute);
            QIO_WRITE(ptask->pqiotcp, "LPT1 no entries\n", strlen("LPT1 no entries\n")); /* 不限制队列名称 */
            break;
        }
    case LPD_REQ_REMOVE_JOB:
        {
            memset(attribute, 0, sizeof(attribute));
            ret = sscanf(buf, "\x05%139[^ \n] %139[^\n]\n", queuename, attribute);
            NET_DEBUG("LPD_REQ_REMOVE_JOB ret(%d) queuename(%s) attribute(%s)", ret, queuename, attribute);
            break;
        }
    default:
        {
            NET_WARN("Unknown req command(0x%X)", cmd);
            break;
        }
    }

    return next_state;
}

/**
 * @brief       LPD task handling function, when current state is LPD_STATE_JOB_SUB_COMMAND.
 * @param[in]   ptask   : The LPD_TASK_S object pointer.
 * @param[in]   buf     : buffer pointer.
 * @param[in]   len     : buffer length.
 * @return      The next state of this state machine.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static LPD_STATE_E lpd_parse_job_sub_command(LPD_TASK_S* ptask, char* buf, int32_t len)
{
    LPD_STATE_E next_state = LPD_STATE_JOB_SUB_COMMAND;
    char        src_name[LPD_NAME_SIZE];
    int32_t     job_id = -1;
    int32_t     ret = 0;
    uint8_t     cmd = (uint8_t)buf[0];

    NET_DEBUG("receive sub cmd: 0x%02x + %s", cmd, buf + 1);
    RETURN_VAL_IF(len < 2 || buf[len - 1] != '\n', NET_WARN, LPD_STATE_ERR);

    switch ( cmd )
    {
    case LPD_JOB_CANCEL:
        {
            NET_DEBUG("receive cancel subcammand from client");
            next_state = LPD_STATE_END;
            break;
        }
    case LPD_JOB_RECV_CTRL_FILE:
        {
            ret = sscanf(buf + 1, "%d cfA%d%31[^\n]\n", &ptask->cfasize, &job_id, src_name);
            ptask->cfasize += 1; /* receive data will be append '\0' from LPR */
            NET_DEBUG("ret(%d) cfasize(%d) job id(%d) src name(%s)", ret, ptask->cfasize, job_id, src_name);
            if ( ptask->cfasize >= TCP_CHUNK_SIZE || ptask->cfasize < 0 )
            {
                NET_WARN("cfasize(%d) invalid, try to receive the maximum buffer(%d)", ptask->cfasize, TCP_CHUNK_SIZE - 1);
                ptask->cfasize = (TCP_CHUNK_SIZE - 1);
            }

            ret = LPD_REPLY_CODE(0);
            if ( ret == 1 )
            {
                next_state = LPD_STATE_JOB_CTRL_STREAM;
            }
            else
            {
                next_state = LPD_STATE_ERR;
            }
            break;
        }
    case LPD_JOB_RECV_DATA_FILE:
        {
            ret = sscanf(buf + 1, "%d dfA%d%31[^\n]\n", &ptask->dfasize, &job_id, src_name);
            ptask->dfasize += 1; /* receive data will be append '\0' from LPR */
            NET_DEBUG("ret(%d) dfasize(%d) job id(%d), src name(%s)", ret, ptask->dfasize, job_id, src_name);
            if ( ptask->dfasize < 0 )
            {
                NET_WARN("dfasize(%d) invalid, try to receive the maximum buffer(%d)", ptask->dfasize, 0x7fffffff);
                ptask->dfasize = 0x7fffffff;
            }

            ret = LPD_REPLY_CODE(0);
            if ( ret == 1 )
            {
                next_state = LPD_STATE_JOB_DATA_STREAM;
            }
            else
            {
                next_state = LPD_STATE_ERR;
            }
            break;
        }
    default:
        {
            NET_WARN("Unknown sub command(0x%X)", cmd);
            break;
        }
    }

    return next_state;
}

/**
 * @brief       LPD task handling function, when current state is LPD_STATE_JOB_CTRL_STREAM.
 * @param[in]   ptask   : The LPD_TASK_S object pointer.
 * @param[in]   buf     : buffer pointer.
 * @param[in]   len     : buffer length.
 * @return      The next state of this state machine.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static LPD_STATE_E lpd_parse_job_ctrl_stream(LPD_TASK_S* ptask, char* buf, int32_t len)
{
    LPD_STATE_E next_state = LPD_STATE_JOB_SUB_COMMAND;
    const char* head = (const char *)buf;
    const char* tail = strchr(buf, '\n');
    char        tmpstr[LPD_NAME_SIZE];
    size_t      line_count = 0;
    int32_t     ret = 0;

    if ( len != ptask->cfasize )
    {
        NET_WARN("cfasize mismatch(%d - %d)", ptask->cfasize, len);
    }
    buf[len] = '\0';

    while ( tail != NULL )
    {
        line_count = (size_t)(tail - head);
        if ( line_count < 2 || line_count > LPD_NAME_SIZE )
        {
            NET_WARN("invalid line(%s) line_count(%u)", head, line_count);
            continue;
        }

        switch ( head[0] )
        {
        case 'H':
            snprintf(tmpstr, line_count, "%s", head + 1);
            NET_DEBUG("src name(%s)", tmpstr);
            break;
        case 'J':
            snprintf(tmpstr, line_count, "%s", head + 1);
            NET_DEBUG("job name(%s)", tmpstr);
            break;
        case 'P':
            snprintf(tmpstr, line_count, "%s", head + 1);
            NET_DEBUG("usr name(%s)", tmpstr);
            break;
        default:
            NET_DEBUG("ignoring ctrl: '%c(0x02x)' + '%s'", head[0], head + 1);
            snprintf(tmpstr, line_count, "%s", head + 1);
            NET_DEBUG("tmpstr(%s)", tmpstr);
            break;
        }

        head = tail + 1;
        tail = strchr(head, '\n');
    }

    ret = LPD_REPLY_CODE(0);
    if ( ret == 1 )
    {
        next_state = LPD_STATE_JOB_SUB_COMMAND;
    }
    else
    {
        next_state = LPD_STATE_ERR;
    }
    return next_state;
}

/**
 * @brief       LPD task handling function, when current state is LPD_STATE_JOB_DATA_STREAM.
 * @param[in]   ptask   : The LPD_TASK_S object pointer.
 * @param[in]   buf     : buffer pointer.
 * @param[in]   len     : buffer length.
 * @return      The next state of this state machine.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static LPD_STATE_E lpd_parse_job_data_stream(LPD_TASK_S* ptask, char* buf, int32_t len)
{
    ROUTER_MSG_S    sendmsg = { .msgSender = MID_PORT_NET };
    JOB_REQUEST_S*  pjobreq = NULL;
    int32_t         wtotal = 0;
    int32_t         nwrite = 0;

    while ( ptask->working && (wtotal < len) )
    {
        if ( ringbuf_writable(ptask->ringbuf) )
        {
            nwrite = ringbuf_write(ptask->ringbuf, buf + wtotal, (size_t)(len - wtotal));
            if ( nwrite > 0 )
            {
                wtotal += nwrite;
                continue;
            }
        }
        NET_INFO("ring buffer is full, retry after 1s");
        pi_msleep(1000);
    }

    if ( len != wtotal )
    {
        NET_WARN("write LPD job data to ringbuf failed, expect(%d) wtotal(%d) rwtotal(%d)", len, wtotal, ptask->rwtotal);
        return LPD_STATE_ERR;
    }

    if ( ptask->rwtotal == 0 ) /* 收到第一包数据并写入ringbuf后，再将task给到connect_mgr */
    {
        NET_DEBUG("send task to MID_SYS_JOB_MGR");
        pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
        RETURN_VAL_IF(pjobreq == NULL, NET_WARN, LPD_STATE_ERR);

        pjobreq->io_class = IO_CLASS_PRINT;
        pjobreq->io_via   = IO_VIA_NET;
        pjobreq->pqio     = ptask->pqiotask;

        sendmsg.msgType   = MSG_CTRL_JOB_REQUEST;
        sendmsg.msg1      = 0;
        sendmsg.msg2      = 0;
        sendmsg.msg3      = pjobreq;
        task_msg_send_by_router(MID_SYS_JOB_MGR, &sendmsg);
    }

    ptask->rwtotal += wtotal;
    if ( ptask->rwtotal >= ptask->dfasize )
    {
        NET_DEBUG("receive all %d bytes, job end", ptask->rwtotal);
        LPD_REPLY_CODE(0);
        return LPD_STATE_END;
    }

    return LPD_STATE_JOB_DATA_STREAM;
}

/**
 * @brief       LPD state machine handling function, receive data from LPD port and parsing.
 * @param[in]   ptask   : The LPD_TASK_S object pointer.
 * @param[in]   tos     : The job timeout seconds.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void lpd_state_machine(LPD_TASK_S* ptask, int32_t tos)
{
    LPD_STATE_E lpd_state = LPD_STATE_REQUEST_COMMAND;
    size_t      recv_size;
    time_t      then;
    int32_t     rv;
    char*       buf;

    buf = (char *)pi_zalloc(TCP_CHUNK_SIZE);
    RETURN_IF(buf == NULL, NET_WARN);

    ptask->working = 1;
    pi_time(&then);
    while ( ptask->working )
    {
        rv = QIO_READABLE(ptask->pqiotcp, 2, 0);
        if ( rv < 0 )
        {
            NET_WARN("select LPR client failed: %d<%s>", errno, strerror(errno));
            ptask->job_err = 1;
            break;
        }
        else if ( rv == 0 )
        {
            if ( pi_time(NULL) > then + tos )
            {
                NET_WARN("LPD timeout(%d), so think the task unusual quit!!!", tos);
                ptask->job_err = 1;
                break;
            }
            continue;
        }

        if ( lpd_state == LPD_STATE_JOB_DATA_STREAM )
        {
            recv_size = (size_t)((ptask->dfasize - ptask->rwtotal) < TCP_CHUNK_SIZE ? (ptask->dfasize - ptask->rwtotal) : TCP_CHUNK_SIZE);
        }
        else if ( lpd_state == LPD_STATE_JOB_CTRL_STREAM )
        {
            recv_size = (size_t)ptask->cfasize;
        }
        else
        {
            recv_size = (size_t)TCP_CHUNK_SIZE;
        }

        rv = QIO_READ(ptask->pqiotcp, buf, recv_size);
        if ( rv < 0 )
        {
            NET_WARN("recv from TCP connection failed: %d<%s>", errno, strerror(errno));
            ptask->job_err = 1;
            break;
        }
        else if ( rv == 0 )
        {
            NET_WARN("current state(%d), receive 0 bytes, the client of TCP connection has been closed!", lpd_state);
            ptask->job_end = 1;
            break;
        }

        switch ( lpd_state )
        {
        case LPD_STATE_REQUEST_COMMAND:
            NET_DEBUG("LPD_STATE_REQUEST_COMMAND, len(%d)", rv);
            lpd_state = lpd_parse_request_command(ptask, buf, rv);
            break;
        case LPD_STATE_JOB_SUB_COMMAND:
            NET_DEBUG("LPD_STATE_JOB_SUB_COMMAND, len(%d)", rv);
            lpd_state = lpd_parse_job_sub_command(ptask, buf, rv);
            break;
        case LPD_STATE_JOB_CTRL_STREAM:
            NET_DEBUG("LPD_STATE_JOB_CTRL_STREAM, len = %d", rv);
            lpd_state = lpd_parse_job_ctrl_stream(ptask, buf, rv);
            break;
        case LPD_STATE_JOB_DATA_STREAM:
            NET_DEBUG("LPD_STATE_JOB_DATA_STREAM, len = %d", rv);
            lpd_state = lpd_parse_job_data_stream(ptask, buf, rv);
            break;
        default:
            break;
        }

        if ( lpd_state == LPD_STATE_END )
        {
            ptask->job_end = 1;
            break;
        }
        if ( lpd_state == LPD_STATE_ERR )
        {
            ptask->job_err = 1;
            break;
        }
        pi_time(&then);
    }
    NET_DEBUG("state machine end. working(%d) lpd state(%d)", ptask->working, lpd_state);
    pi_free(buf);
}

/**
 * @brief       The threads pool handling function for once connect to the LPD port.
 * @param[in]   arg     : The threads pool context(NET_CONN_S object pointer).
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void lpd_connection(void* arg)
{
    NET_CONN_S* pnc = (NET_CONN_S *)arg;
    LPD_TASK_S* ptask = NULL;
    int32_t     tos;

    do
    {
        tos = (int32_t)netdata_get_io_timeout(DATA_MGR_OF(s_lpd_ctx));
        NET_DEBUG("LPD connection(%s : %u) start, timeout(%d)", pnc->remote_addr, pnc->remote_port, tos);
        BREAK_IF(netsock_peek_connection(pnc, tos) <= 0, NET_WARN)

        ptask = (LPD_TASK_S *)pi_zalloc(sizeof(LPD_TASK_S));
        BREAK_IF(ptask == NULL, NET_WARN);

        ptask->pqiotask = (QIO_S *)pi_zalloc(sizeof(QIO_S));
        BREAK_IF(ptask->pqiotask == NULL, NET_WARN);

        ptask->pqiotask->close = lpd_task_close;
        ptask->pqiotask->poll  = lpd_task_poll;
        ptask->pqiotask->read  = lpd_task_read;
        ptask->pqiotask->write = lpd_task_write;
        ptask->pqiotask->seek  = lpd_task_seek;
        ptask->pqiotask->priv  = (void *)ptask;

        ptask->ringbuf = ringbuf_create(TCP_CHUNK_SIZE * 16);
        BREAK_IF(ptask->ringbuf == NULL, NET_WARN);

        ptask->pqiotcp = qio_tcp_create(pnc->sockfd);
        BREAK_IF(ptask->pqiotcp == NULL, NET_WARN);

        pnc->sockfd = INVALID_SOCKET; /* sockfd流转到pqiotcp，随QIO_CLOSE释放 */
        lpd_state_machine(ptask, tos);
        NET_DEBUG("TCP connection(%s : %u) is disconnected, close local QIO_S<%p>", pnc->remote_addr, pnc->remote_port, ptask->pqiotcp);
        QIO_CLOSE(ptask->pqiotcp);
        ptask->pqiotcp = NULL;

        while ( ptask->working && (ptask->rwtotal > 0) )
        {
            if ( pi_time(NULL) % 10 == 0 )
            {
                NET_DEBUG("waiting LPD job end from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
            }
            pi_msleep(1000);
        }
        NET_DEBUG("LPD job end from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
    }
    while ( 0 );

    if ( ptask != NULL )
    {
        if ( ptask->ringbuf != NULL );
        {
            ringbuf_destroy(ptask->ringbuf);
        }
        if ( ptask->pqiotask != NULL )
        {
            ptask->pqiotask->priv = NULL;
            pi_free(ptask->pqiotask);
        }
        pi_free(ptask);
    }
    netsock_close_connection(pnc);
}

/**
 * @brief       LPD handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void* lpd_thread_handler(void *arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    int32_t         pipefd = s_lpd_ctx->pfd[0];
    int32_t         update = 0;
    int32_t         status;
    int32_t         max_fd;

    while ( 1 )
    {
        FD_ZERO(&rfds);
        FD_SET(pipefd, &rfds);
        max_fd = pipefd;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, LPD_LOCAL_PORT, 1);
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        if ( FD_ISSET(pipefd, &rfds) )
        {
            read(pipefd, &status, sizeof(status));
            NET_DEBUG("recv(0x%X), reload service socket", status);
            update = 1;
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    NET_DEBUG("new connection %d from %s : %u to %u", pnc->sockfd, pnc->remote_addr, pnc->remote_port, pnc->local_port);
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_lpd_ctx), lpd_connection, pnc) < 0 )
                    {
                        NET_WARN("add lpd_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}

/**
 * @brief       The callback function when the subject notify.
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void lpd_update_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_lpd_ctx == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_LPD )
    {
        write(s_lpd_ctx->pfd[1], &(s->subject_status), sizeof(s->subject_status));
    }
}

int32_t lpd_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_lpd_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_lpd_ctx = (LPD_CTX_S *)pi_zalloc(sizeof(LPD_CTX_S));
    RETURN_VAL_IF(s_lpd_ctx == NULL, NET_WARN, -1);

    do
    {
        s_lpd_ctx->net_ctx = net_ctx;
        pipe(s_lpd_ctx->pfd);

        s_lpd_ctx->srv_tid = pi_thread_create(lpd_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "lpd_thread_handler");
        BREAK_IF(s_lpd_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("LPD initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netport_observer(net_ctx, lpd_update_callback, NULL);
    }
    else
    {
        lpd_epilog();
    }
    return ret;
}

void lpd_epilog(void)
{
    if ( s_lpd_ctx != NULL )
    {
        if ( s_lpd_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_lpd_ctx->srv_tid);
        }
        pi_close(s_lpd_ctx->pfd[0]);
        pi_close(s_lpd_ctx->pfd[1]);
        pi_free(s_lpd_ctx);
        s_lpd_ctx = NULL;
    }
}
/**
 *@}
 */
