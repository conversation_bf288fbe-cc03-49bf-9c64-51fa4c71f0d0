/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file format.h
 * @addtogroup manager
 * @{
 * @addtogroup manager
 * @autor 
 * @date 2024-06-11
 * @brief define format
 */
#ifndef _FORMAT_H_
#define _FORMAT_H_
#include <stdio.h>
#include "basic/config.h"

/* management instruction */
typedef enum {
    E_PING,
    E_START,
    E_START_RES,
    E_END,
    E_END_RES,
    E_PAUSE,
    E_PAUSE_RES,
    E_RESUME,
    E_RESUME_RES,
    E_PRINTER_TO_APP,
    E_APP_TO_PRINTER,
    E_GET_STATIC_PROPERTY,
    E_RET_STATIC_PROPERTY,
    E_GET_DYNAMIC_PROPERTY,
    E_RET_DYNAMIC_PROPERTY,

    E_TYPE_MAX
} E_MANAGER_CMD;

/* management instruction format */
typedef struct FORMAT {
    E_MANAGER_CMD type;///<instruction type
    uint16_t length;   //<instruction len
    uint8_t* data;     //<instruction data
} FORMAT;
/**
 * @brief   the incoming data flow is formatted as a structured message
 * @param[in] *buffer :input data flow,data flow from the transmission module
 * @param[in] *des    :the structure of the manager module
 * @param[in] *length :data flow len
 * @return  Construct result
 * @retval  =0 :sucess
 * @retval  <0 :error
 * <AUTHOR> @date    2024-06-11
 */
int format_conversion_to_pkt(uint8_t* buffer, FORMAT* des, uint16_t length);

/**
 * @brief   make ping pkt
 * @param[in] *buff   :input data flow,data flow from the transmission module
 * @param[in] *length :data flow len
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int make_ping_pkt(uint8_t* buff, uint16_t* length);

/**
 * @brief   make start res
 * @param[in] *buff   :input data flow,data flow from the transmission module
 * @param[in] *length :data flow len
 * @param[in] rtid    :app rtid num
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int make_start_res(uint8_t* buff, uint16_t* length, uint8_t rtid);

/**
 * @brief   make end res 
 * @param[in] *buff   :input data flow,data flow from the transmission module
 * @param[in] *length :data flow len
 * @param[in] rtid    :app rtid num
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int make_end_res(uint8_t* buff, uint16_t* length, uint8_t rtid);

/**
 * @brief   make app to pedk msg
 * @param[in] *buff_out   :ouput data flow,data flow from the transmission module
 * @param[in] *length_out :output data flow len
 * @param[in] *buff_in    :input data flow,data flow from the transmission module
 * @param[in] *length_in  :input data flow len
 * @param[in] rtid    :rtid num
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int make_a2p_msg(uint8_t* buff_out, uint16_t* length_out, uint8_t* buff_in, uint16_t length_in, uint8_t rtid);

/**
 * @brief   make the dynamic attribute response package
 * @param[in] *buff   :input data flow,data flow from the transmission module
 * @param[in] *length :data flow len
 * @param[in] rtid    :rtid num
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int make_dynamic_buf(uint8_t* buf, char *json_str, uint16_t length);

/**
 * @brief   make the reset printer response package
 * @param[in] *buff   :input data flow,data flow from the transmission module
 * @param[in] *length :data flow len
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int make_reset_printer_buf(uint8_t* buf, uint16_t *length);

#endif /*_FORMAT_H_*/
/**
 * @}
 */
 
