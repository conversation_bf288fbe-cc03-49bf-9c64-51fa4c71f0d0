## 介绍
各文件简单介绍
Makefile: 位于pol_test目录下，独立于buildroot，可以直接在当前目录下进行编译
pol_test.c: 用例测试主进程文件
pol_io_test.c: io相关类用例
pol_mem_test.c: mem相关类用例
pol_string_test.c: string相关类用例
pol_time_test.c: time相关类用例

测试说明：
测试分为三种测试模式：
1 func test       单个测试函数，其中有两种模式，1、single模式 仅测试某单一用例。
												2、multiple模式，可以通过配置目录下的functest.ini文件(如果没有则直接创建)配置多个用例。
2 module test     模块测试。对整个模块进行测试，有以下模块（io, mem, sting, time）
3 all test		  整体测试，对所有用例进行测试

## 编译
### 编译X86版本
直接在pol_test目录下，执行`make`命令，将生成一个pol文件
### 编译ARM版本
如果之前已经存在X86版本的pol文件了，则需要先执行`make clean`命令，再执行`make CROSS=1`命令，同样生成pol文件

## 运行
### X86版本
直接在pol_test目录下，执行‘./pol’进入测试主程序
