#include "runtime/utils/msgq_utils.h"
#include <uv.h>
#include <stdlib.h>
#include "runtime/modules/bridge/bridge.h"
#include "runtime/runtime.h"

void clean_msg_queue(INNER_MSG *inner_msg)
{
    inner_msg->data_length = 0;
    inner_msg->next = NULL;
    if(inner_msg->data != NULL){
        free(inner_msg->data);
    }
    if(inner_msg != NULL){
        free(inner_msg);
    }
}

int32_t send_data_to_queue(PeSFRunTime* prt, INNER_MSG* inner_msg)
{
    LOG_D("msgq_utils","send_data_to_queue before uv_mutex");
    uv_mutex_lock(prt->uv_mutex);
    LOG_D("msgq_utils","send_data_to_queue in uv_mutex");
    //此处要另起一个消息，做成链表，等用完再放
    MsgQueue *msgq = &(prt->msgq);
    INNER_MSG *msg = (INNER_MSG*)malloc(sizeof(INNER_MSG));
    msg->next = NULL;

    *msg = *inner_msg;//直接复制
    LOG_D("msgq_utils","msg type = %d", (int)inner_msg->type);
    //复制消息
    if(0 == msgq->msg_cont){
        msgq->msg_tail = msg;
        msgq->msg_head = msg;
    }else if(inner_msg->type == E_INNER_HEARTBEAT){
        msg->next = msgq->msg_head;
        msgq->msg_head = msg;
    }else{
        msgq->msg_tail->next = msg;
        msgq->msg_tail = msg;
    }
    msgq->msg_cont++;
    uv_mutex_unlock(prt->uv_mutex);
    LOG_D("msgq_utils","send_data_to_queue after uv_mutex");

    //发送消息
    prt->uv_async->data = prt;
    uv_async_send(prt->uv_async);

    return 0;
}

void receive_data_from_queue(uv_async_t* handle)
{
    LOG_D("msgq_utils","receiv from que");

    PeSFRunTime* prt = (PeSFRunTime*)handle->data;
    MsgQueue *msgq = &(prt->msgq);
    INNER_MSG *recv_msg;

    while(msgq->msg_cont > 0){
        LOG_D("msgq_utils","receive_data_from_queue befor uv_mutex");
        uv_mutex_lock(prt->uv_mutex);
        LOG_D("msgq_utils","receive_data_from_queue in uv_mutex");
        //取消息
        recv_msg = msgq->msg_head;
        msgq->msg_head =  msgq->msg_head->next;
        recv_msg->next = NULL;
        msgq->msg_cont--;
        uv_mutex_unlock(prt->uv_mutex);
        LOG_D("msgq_utils","receive_data_from_queue after uv_mutex");
        //各子线程收到消息后，去进行处理
        app_msg_exec(prt, recv_msg);

        //清消息
        uv_mutex_lock(prt->uv_mutex);
        clean_msg_queue(recv_msg); 
        uv_mutex_unlock(prt->uv_mutex);
    }
}