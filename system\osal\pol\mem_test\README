#介绍
本目录为内存接口辅助调试功能测试用例，及编译脚本
{
    multi_thread_case.c         多线程测试程序
    multi_process_case.c        多进程测试程序
    single_thread_case.c        单线程测试程序
    mem_test_case.c             测试用例
    

}
# 编译
可支持主机及目标环境测试运行



#编译方法
1. 打开内存调试配置
CONFIG_POL_DEBUG=y
CONFIG_POL_MEM_DEBUG=y
CONFIG_POL_MEM_RECORD=y
CONFIG_POL_MEM_TRACE=y
CONFIG_POL_MEM_TRACE_BACKTRACE_LV=5 
2.编译完成某项目
3.进入buildroot/output/build/appsrc-custom/common/pol/mem_test/
4.测试程序编译
非交叉编译，在主机测试：
make 
交叉编译，在目标环境测试
make CROSS=1

#测试
./multi_thread_case 多线程内存跟踪功能测试
./single_thread_case 单线程内存跟踪功能测试
./multi_process_case 多进程内存跟踪功能测试






