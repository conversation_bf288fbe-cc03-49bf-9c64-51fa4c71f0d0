#include <PEDK_net_http.h>
#include "PEDK_event.h"

#include <quickjs.h>

#define HTTP_LOG(fmt, ...) printf("[HTTP info]: %s:%d " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#define Log(format, ...) printf("[HTTP] [RE] [%d->%s] \n"format, __LINE__, __func__, ##__VA_ARGS__);
#define countof(x) (sizeof(x) / sizeof((x)[0]))

#define countof(x) (sizeof(x) / sizeof((x)[0]))

typedef struct JSCFunctionList
{
    const char *name;
    int param_num;
    JSCFunction *func;
}JSCFunctionList;

// HTTPS 配置参数结构体
typedef struct {
    int verify_certificate;      // 0: 不验证，1: 验证 (默认)
    int verify_host_mode;        // 0: 不验证，2: 严格验证 (默认)
    char client_cert_path[256];  // 客户端证书路径
    char client_key_path[256];   // 客户端私钥路径
    char key_password[128];      // 私钥密码
} HttpsConfigParam;

// 默认存储的配置
static HttpsConfigParam current_config = {
    .verify_certificate = 1,
    .verify_host_mode = 2,
    .client_cert_path = "",
    .client_key_path = "",
    .key_password = ""
};

/*
    定义 QuickJS C 函数
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/

JSValue js_receiveData_Response(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    printf("js_receiveData_Response\n");

    if (!JS_IsString(argv[0]))
    {
       return JS_EXCEPTION;
    }
    int ret = 0;
    const char* responseData = JS_ToCString(ctx, argv[0]);

    ret = SendMsgToMfp(MSG_MODULE_HTTP, MSG_HTTP_SUB, 0, strlen(responseData)+1, responseData);
    JS_FreeCString(ctx, responseData);

    return JS_NewBool(ctx, ret);
}

JSValue js_set_https_config_params(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    printf("js_set_https_config_params\n");

    if (argc < 1) {
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    const char *param_str = JS_ToCString(ctx, argv[0]);
    if (!param_str) {
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    HttpsConfigParam new_config;
    memset(&new_config, 0, sizeof(new_config));

    cJSON *json = cJSON_Parse(param_str);
    JS_FreeCString(ctx, param_str);

    if (!json) {
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    cJSON *verify_certificate = cJSON_GetObjectItem(json, "verify_certificate");
    cJSON *verify_host_mode = cJSON_GetObjectItem(json, "verify_host_mode");
    cJSON *client_cert_path = cJSON_GetObjectItem(json, "client_cert_path");
    cJSON *client_key_path = cJSON_GetObjectItem(json, "client_key_path");
    cJSON *key_password = cJSON_GetObjectItem(json, "key_password");

    new_config.verify_certificate = (cJSON_IsNumber(verify_certificate)) ? verify_certificate->valueint : 0;
    new_config.verify_host_mode = (cJSON_IsNumber(verify_host_mode)) ? verify_host_mode->valueint : 0;

    if (cJSON_IsString(client_cert_path) && client_cert_path->valuestring[0]) {
        strncpy(new_config.client_cert_path, client_cert_path->valuestring, sizeof(new_config.client_cert_path) - 1);
        new_config.client_cert_path[sizeof(new_config.client_cert_path) - 1] = '\0';
    }

    if (cJSON_IsString(client_key_path) && client_key_path->valuestring[0]) {
        strncpy(new_config.client_key_path, client_key_path->valuestring, sizeof(new_config.client_key_path) - 1);
        new_config.client_key_path[sizeof(new_config.client_key_path) - 1] = '\0';
    }

    if (cJSON_IsString(key_password) && key_password->valuestring[0]) {
        strncpy(new_config.key_password, key_password->valuestring, sizeof(new_config.key_password) - 1);
        new_config.key_password[sizeof(new_config.key_password) - 1] = '\0';
    }

    cJSON_Delete(json);

    printf("HTTPS configuration updated successfully:\n");
    printf("verify_certificate: %d\n", new_config.verify_certificate);
    printf("verify_host_mode: %d\n", new_config.verify_host_mode);
    printf("client_cert_path: %s\n", new_config.client_cert_path);
    printf("client_key_path: %s\n", new_config.client_key_path);
    printf("key_password: %s\n", new_config.key_password ? new_config.key_password : "(null)");

    unsigned char receive_data[32];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_HTTPS_SET_CONFIG_PARAM, 0, sizeof(new_config), &new_config);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_HTTPS_SET_CONFIG_PARAM, &respond, receive_data, &receive_cnt, 10);

    printf(" respond: %d\n", respond);

    if (respond < 0) {
        return JS_NewString(ctx, "EINVALIDPARAM");
    } else {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }

}

JSValue js_get_https_config_params(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    printf("js_get_https_config_params\n");

    // 创建一个空的 JavaScript 对象
    JSValue obj = JS_NewObject(ctx);

    HttpsConfigParam https_config_param;
    int32_t receive_cnt = sizeof(HttpsConfigParam);
    int32_t respond = -1;

    // 初始化结构体
    memset(&https_config_param, 0, sizeof(HttpsConfigParam));

    // 发送消息并接收数据
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_HTTPS_GET_CONFIG_PARAM, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_HTTPS_GET_CONFIG_PARAM, &respond, &https_config_param, &receive_cnt, 5);

    // 检查响应结果
    if (respond < 0) {
        printf("Error: Failed to get HTTPS config params (respond = %d)\n", respond);
        JS_FreeValue(ctx, obj); // 释放对象
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    // 打印接收到的参数
    printf("Received HTTPS Config Params:\n");
    printf("verify_certificate: %d\n", https_config_param.verify_certificate);
    printf("verify_host_mode: %d\n", https_config_param.verify_host_mode);

    if (https_config_param.client_cert_path[0]) {
        printf("cert path: %s\n", https_config_param.client_cert_path);
    } else {
        printf("cert path: (null)\n");
        strncpy(https_config_param.client_cert_path, "", sizeof(https_config_param.client_cert_path) - 1);
        https_config_param.client_cert_path[sizeof(https_config_param.client_cert_path) - 1] = '\0';
    }

    if (https_config_param.client_key_path[0]) {
        printf("key path: %s\n", https_config_param.client_key_path);
    } else {
        printf("key path: (null)\n");
        strncpy(https_config_param.client_key_path, "", sizeof(https_config_param.client_key_path) - 1);
        https_config_param.client_key_path[sizeof(https_config_param.client_key_path) - 1] = '\0';
    }

    if (https_config_param.key_password[0]) {
        printf("key pass: %s\n", https_config_param.key_password);
    } else {
        printf("key pass: (null)\n");
        strncpy(https_config_param.key_password, "", sizeof(https_config_param.key_password) - 1);
        https_config_param.key_password[sizeof(https_config_param.key_password) - 1] = '\0';
    }

    // 映射结构体字段到对象属性
    JS_SetPropertyStr(ctx, obj, "verify_certificate", JS_NewInt32(ctx, https_config_param.verify_certificate));
    JS_SetPropertyStr(ctx, obj, "verify_host_mode", JS_NewInt32(ctx, https_config_param.verify_host_mode));
    JS_SetPropertyStr(ctx, obj, "client_cert_path", JS_NewString(ctx, https_config_param.client_cert_path));
    JS_SetPropertyStr(ctx, obj, "client_key_path", JS_NewString(ctx, https_config_param.client_key_path));
    JS_SetPropertyStr(ctx, obj, "key_password", JS_NewString(ctx, https_config_param.key_password));

    return obj;


}

/* 定义API的函数入口名称及列表 */
//static const JSCFunctionList pesf_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    /* http */
//    {"receiveData_Response", 1, js_receiveData_Response},

//};
    /*

const JSCFunctionList* getJSCFunctionListHttp(int *length) {
    *length = countof(pesf_funcs);
    return pesf_funcs;
}

int addNetHttp(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;

   /* creat the classes */
/*   int count = 0;
   const JSCFunctionList* pesf_funcs = getJSCFunctionListHttp(&count);
   Log("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   return 0;
}
*/
/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_http_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    //http
    {"receiveData_Response",      1,    js_receiveData_Response},
    {"set_https_config_params",   1,    js_set_https_config_params},
    {"get_https_config_params",   0,    js_get_https_config_params},

};

const JSCFunctionList* get_http_JSCFunctionList(int *length) {
	*length = countof(pesf_http_funcs);
	return pesf_http_funcs;
}

int js_http_init(JSContext *ctx, JSValueConst global)
{
    int i = 0;

    printf("*********start http init module*******\n");
    /* creat the classes */
    int count = 0;
    const JSCFunctionList* pesf_funcs = get_http_JSCFunctionList(&count);
    printf("count:%d\n",count);
    for(int i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                            JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].param_num));

    }
    printf("*********end http init module*******\n");
    return 0;
}

