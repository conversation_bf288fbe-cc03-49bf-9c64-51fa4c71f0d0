/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file security_aclregister.h
 * @addtogroup platform
 * @{
 * @addtogroup platform_aclregister
 * <AUTHOR>
 * @date 2022-02-16
 * @brief acl module  settings local acl parser will do some machine settings,
 *        for example: get mac address burn flash, and so on.
 */
#ifndef __SECURITY_REGISTER_H__
#define __SECURITY_REGISTER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "pol/pol_types.h"
#include "utilities/parsercommon.h"
#include "acl/acl.h"
#include "qio/qio_general.h"
#include "job_manager.h"
#include "pol/pol_endian.h"
#include "security/rc4.h"

#define SECURITY_ERR            -1
#define SECURITY_OK              0
#define SOLE_SERIES_LEN          32
#define PRINT_NAME_LEN          32
#define SOLE_SERIALNUM_LEN       17

/**
 *@brief operator
 */
typedef enum
{
    SECURITY_OPERATE_PANEL_INIT_FALAG,              /* 面板初始化标志                                         */
    SECURITY_OPERATE_SLEEP_MODE,                    /* 设置休眠模式                                           */
    SECURITY_OPERATE_LCD_BACKLIGHT,                 /* 屏幕亮度                                               */
    SECURITY_OPERATE_SYS_VOLUE,                     /* 按键音 0-20                                               */
    SECURITY_OPERATE_QUIET_MODE,                    /* 静音模式                                               */
    SECURITY_OPERATE_USB_CONTROL,                   /* USB端口管控                                            */
    SECURITY_OPERATE_NET_CONTROL,                   /* 网络端口管控                                           */
    SECURITY_OPERATE_PRINT_DEFAULT_TRAY,            /* 默认纸盒                                               */
    SECURITY_OPERATE_PRINT_TRAY_MULTI_SIZE,         /* 多功能进纸器纸型                                       */
    SECURITY_OPERATE_PRINT_TRAY_MULTI_TYPE,         /* 多功能进纸器纸张种类                                   */
    SECURITY_OPERATE_PRINT_COLOR_BALANCE_C,         /* 青色色彩平衡 1-11                                      */
    SECURITY_OPERATE_PRINT_COLOR_BALANCE_M,         /* 品红色色彩平衡 1-11                                    */
    SECURITY_OPERATE_PRINT_COLOR_BALANCE_Y,         /* 黄色色彩平衡 1-11                                      */
    SECURITY_OPERATE_PRINT_COLOR_BALANCE_K,         /* 黑色色彩平衡 1-11                                      */
    SECURITY_OPERATE_PRINT_IMAGE_SATURATION,        /* 画像调整-饱和度                                        */
    SECURITY_OPERATE_PRINT_IMAGE_BRIGTHNESS,        /* 画像调整-明暗度 1-11                                   */
    SECURITY_OPERATE_PRINT_IMAGE_CONTRAST,          /* 画像调整-对比度 1-11                                   */
    SECURITY_OPERATE_PRINT_TONER_DENISTY,           /* 打印浓度 1-11                                          */
    SECURITY_OPERATE_PRINT_SAVE_TONER_MODE,         /* 省墨模式 0-off, 1-on                                   */
    SECURITY_OPERATE_SYS_TIME_MODIFY,               /* 修改系统时间                                           */

    SECURITY_OPERATE_AMBIENT_LIGHT,                 /* 氛围灯，0-off 1-on                                     */
    SECURITY_OPERATE_JOB_ERROR_PROCESS_MODE,        /* 作业出错处理方式 0-恢复继续 1-立即删除 2-延时删除      */
    SECURITY_OPERATE_JOB_ERROR_DELETE_TIME,         /* 作业出错延迟删除时间                                   */
    SECURITY_OPERATE_PRINT_TRAY_LCT_IN_SIZE,        /* 内置大容量纸张尺寸                                     */
    SECURITY_OPERATE_PRINT_TRAY_LCT_IN_TYPE,        /* 内置大容量纸张介质                                     */
    SECURITY_OPERATE_PRINT_IMAGE_ORIGINAL_TYPE,     /* 打印画像原稿类型 3-文+图 2-图像 1-文本                 */
    SECURITY_OPERATE_SCREENSAVER_TIMEOUT,           /* 超时时间设置 0:15秒,1:30秒,2:1分钟,3:3分钟,4:5分钟     */
    SECURITY_OPERATE_ERROR_STATUS_VOLUME,           /* 错误提示音 0-20                                        */
    SECURITY_OPERATE_JOB_END_VOLUME,                /* 作业结束提示音 0-20                                    */
    SECURITY_OPERATE_COPY_PARAM_RESET,              /* 复印设置自动还原 0-off, 1-on                           */
    SECURITY_OPERATE_CUSTOM1_SIZE_LENGTH,           /* 多功能进纸器自定义1宽                                  */
    SECURITY_OPERATE_CUSTOM2_SIZE_LENGTH,           /* 多功能进纸器自定义2宽                                  */
    SECURITY_OPERATE_CUSTOM3_SIZE_LENGTH,           /* 多功能进纸器自定义3宽                                  */
    SECURITY_OPERATE_CUSTOM1_SIZE_HEIGHT,           /* 多功能进纸器自定义1高                                  */
    SECURITY_OPERATE_CUSTOM2_SIZE_HEIGHT,           /* 多功能进纸器自定义2高                                  */
    SECURITY_OPERATE_CUSTOM3_SIZE_HEIGHT,           /* 多功能进纸器自定义3高                                  */
    SECURITY_OPERATE_COPY_JOB_CONTROL,              /* 复印作业管控 0-off, 1-on                               */
    SECURITY_OPERATE_SCAN_JOB_CONTROL,              /* 扫描作业管控 0-off, 1-on                               */
    SECURITY_OPERATE_PRINT_JOB_CONTROL,             /* 打印作业管控 0-off, 1-on                               */
    SECURITY_OPERATE_LOCK_SCREEN_SWITCH,            /* 锁屏开关 0-off, 1-on                                   */
    SECURITY_OPERATE_LOCK_SCREEN_TIME,              /* 锁屏时间  0:15秒,1:30秒,2:1分钟,3:2分钟,4:5分钟        */
    SECURITY_OPERATE_LOCK_SCREEN_PASSWORD,          /* 修改锁屏密码                                           */

    SECURITY_OPERATE_SET_MULTI_TRAY_SIZE,           /* 多功能进纸器纸张尺寸                                   */
    SECURITY_OPERATE_SET_MULTI_TRAY_TYPE,           /* 多功能进纸器纸张类型                                   */
    SECURITY_OPERATE_SET_TRAY1_TYPE,                /* 纸盒1纸张类型                                          */
    SECURITY_OPERATE_SET_TRAY2_TYPE,                /* 纸盒2纸张类型                                          */
    SECURITY_OPERATE_SET_TRAY3_TYPE,                /* 纸盒3纸张类型                                          */
    SECURITY_OPERATE_SET_TRAY4_TYPE,                /* 纸盒4纸张类型                                          */
    SECURITY_OPERATE_SET_LCT_IN_TRAY_SIZE,          /* 内置大容量纸盒纸张尺寸                                 */
    SECURITY_OPERATE_SET_LCT_IN_TRAY_TYPE,          /* 内置大容量纸盒纸张类型                                 */

    SECURITY_OPERATE_SET_COLOR_COPY_ENABLE,         /* 彩色复印管控 0-off, 1-on                               */
    SECURITY_OPERATE_SET_COLOR_COPY_PASSWORD,       /* 彩色复印密码                                           */
    SECURITY_OPERATE_SET_SHORTCUT_COPY,             /* 复印快捷方式 null                                      */
    SECURITY_OPERATE_SET_OVERLAY_COPY_DATA,         /* 叠图         null                                      */
    SECURITY_OPERATE_SET_COPY_RANGE_RESET,          /* 保留复印设置-高级画像 0-off, 1-on                      */
    SECURITY_OPERATE_SET_JOB_ADVANCE_IMAGE,         /* 复印/扫描/U盘打印画像高级设置 null                     */
    SECURITY_OPERATE_SET_ADF_PAPER_IN_VOLUME,       /* ADF放纸提示音 0-20                                     */

}SECURITY_OPERATE_TYPE_E;

typedef struct {
    SECURITY_OPERATE_TYPE_E op_type;                ///< operate type
    char                    op_value[32];           // 对应修改该类型修改的具体值
}SECURITY_OPERATE_INFO_S;

typedef struct {
    char httc_tpcm[32];
    char httc_tsb[32];
}SECURITY_HTTC_VERSION_S;

/**
 *@brief acl response read log file for security
 */
#pragma pack(1)
typedef struct
{
    ACL_RESPONSE_BASE_CLASS_MEMBERS;        ///< define the base items
    uint32_t len;                           ///< the data length
    uint8_t temp[6];                        ///< has to be expanded to 16 bytes total, this does that
} ACL_RESPONSE_READ_LOG_FILE_S;
#pragma pack()

typedef struct
{
    uint32_t            boardType;                  //sample format: SCAN_SUB_BOARD
    unsigned char       boardId[SOLE_SERIES_LEN];   //sample format:SCXX123456,EGXX123456,PLXX123456...
    unsigned char       HwVersion[SOLE_SERIES_LEN]; //sample format:S.1.0.1
    unsigned char       FwVersion[SOLE_SERIES_LEN]; //sample format:S.1.0.1
}SUB_BOARD_ATTRIBUTE_S;

//security param info
typedef struct {
    char                sole_series_name[SOLE_SERIES_LEN];              //sec sole series name
    char                printer_name[PRINT_NAME_LEN];                   //printer name
    char                product_serail_num[SOLE_SERIES_LEN];            //product serail num
    uint32_t            total_cancel_page_count;                        //print tatal
    uint32_t            last_total_pages_count;                         //last time print total pages
}SECURITY_PARAM_INFO_S;

//security param info
typedef struct {
    char                ytoner_serialnumber[SOLE_SERIES_LEN];           //yellow toner serialnumber
    char                mtoner_serialnumber[SOLE_SERIES_LEN];           //magenta toner serialnumber
    char                ctoner_serialnumber[SOLE_SERIES_LEN];           //cyan toner serialnumber
    char                ktoner_serialnumber[SOLE_SERIES_LEN];           //black toner serialnumber
    char                wastetoner_serialnumber[SOLE_SERIES_LEN];       //wastetoner serialnumber
    char                image_serialnumber[SOLE_SERIES_LEN];            //image serialnumber
    char                engine_board_serialnumber[SOLE_SERIES_LEN];     //engine serialnumber
    char                engine_board_HwVersion[SOLE_SERIES_LEN];        //engine HwVersion
    char                engine_board_FwVersion[SOLE_SERIES_LEN];        //engine FwVersion
    char                scan_board_serialnumber[SOLE_SERIES_LEN];       //scan serialnumber
    char                scan_board_HwVersion[SOLE_SERIES_LEN];          //engine HwVersion
    char                scan_board_FwVersion[SOLE_SERIES_LEN];          //engine FwVersion
    char                panel_board_serialnumber[SOLE_SERIES_LEN];      //panel serialnumber
    char                panel_board_HwVersion[SOLE_SERIES_LEN];         //engine HwVersion
    char                panel_board_FwVersion[SOLE_SERIES_LEN];         //engine FwVersion
}SECURITY_MACHINE_INFO_S;

//toner cartridge info
typedef enum
{
    YELLOW_TONER_CHO       = 0,
    MAGENTA_TONER_CHO      = 1,
    CYAN_TONER_CHO         = 2,
    BLACK_TONER_CHO        = 3,
    WASTE_TONER_CHO        = 4,
    IMAGEINGUNIT_CHO       = 5,
    SUPPLY_ENUM_MAX        = 6
}COMPONENT_TYPE_E;

typedef struct {
    COMPONENT_TYPE_E type;
    char             serialnumber[SOLE_SERIES_LEN];
}TONER_INFO_S;


typedef struct
{
	time_t				fwupgrade_time;
	unsigned char		fw_cur_version[8];
    unsigned char		fw_new_version[8];
}FW_UPGRADE_LOG_S;

/**
 *@brief get audit log file record info number of rows.
 *<AUTHOR>
 */
int32_t get_auditlog_number(void);

/**
 *@brief export audit log file content. for webpage
 *<AUTHOR>
 */
int32_t export_auditlog                  // 返回值为0,表示成功； 非0，表示导出失败
(
    uint32_t  export_start,         // 导出起始日志
    uint32_t  export_end            // 导出日志条数
);

#if CONFIG_AUDIT_RECORD
/**
 *@brief export all audit log file for webpage
 *@param[in] export audit log save file path
 *<AUTHOR>
 */
uint32_t export_auditlog_to_webpage(const char *file_path);

/**
*@brief export sub board verify Event log.  for webpage
*@param[in] export sub board verify event log file path.
*<AUTHOR>
*/
int32_t export_subboard_verify_event_log_to_webpage(const char *file_path);

/**
 *@brief export all operate log file for webpage
 *@param[in] export operate log save file path
 *<AUTHOR>
 */
uint32_t export_operatelog_to_webpage(const char *file_path);

/**
*@brief export fw upgrade log to webpage.
*<AUTHOR>
*/
int32_t export_fw_upgrade_log_to_webpage(const char *file_path);

#else

#define export_auditlog_to_webpage(file_path)

#define export_subboard_verify_event_log_to_webpage(file_path)

#define export_operatelog_to_webpage(file_path)

#define export_fw_upgrade_log_to_webpage(file_path)

#endif


/**
 *@brief export audit log file content to manage.
 *<AUTHOR>
 */
int32_t export_auditlog_to_manage();


/**
 *@brief get operate log file record info number of rows.
 *<AUTHOR>
 */
uint32_t get_operatelog_number(void);

/**
 *@brief export operate log file content. for webpage
 *@param[in] export operate log start row number
 *@param[in] export operate log end row number
 *<AUTHOR>
 */
uint32_t export_operatelog(uint32_t export_start, uint32_t export_end);

/**
 *@brief export operate log file content to manage.
 *<AUTHOR>
 */
uint32_t export_operatelog_to_manage();


/**
*@brief get sub board verify record info number of rows in file.
*<AUTHOR>
*/
int32_t get_subboard_verify_log_number(void);

/**
*@brief get sub board verify event lognumber of rows in file.
*<AUTHOR>
*/
int32_t get_subboard_verify_event_log_number(void);

/**
*@brief export sub board verify log.  for webpage
*@param[in] export sub board verify log file path.
*<AUTHOR>
*/
int32_t export_subboard_verify_log_to_webpage(const char *file_path);

/**
*@brief export sub board verify log.  for webpage
*@param[in] export sub board verify log start row number.
*@param[in] export sub board verify log end row number.
*<AUTHOR>
*/
int32_t export_subboard_verify_log(uint32_t  export_start, uint32_t  export_end);

/**
*@brief export sub board verify Event log.  for webpage
*@param[in] export sub board verify event log start row number.
*@param[in] export sub board verify event log end row number.
*<AUTHOR>
*/
int32_t export_subboard_verify_event_log(uint32_t  export_start, uint32_t  export_end);

/**
*@brief export sub board verify Event log to manage.
*<AUTHOR>
*/
int32_t export_subboard_verify_event_log_to_manage(void);


/**
*@brief export fw upgrade log to manage.
*<AUTHOR>
*/
int32_t export_fw_upgrade_log_to_manage(void);

/**
 *@brief set security module struct param info.
 *@param[in] SECURITY_PARAM_INFO_S security module param info .
 *@return void
 *<AUTHOR>
 */
void get_secuity_param_info(SECURITY_PARAM_INFO_S *get_sec_param_info);

/**
 *@brief set security module struct param info.
 *@param[in] SUB_BOARD_ATTRIBUTE_S security module param info .
 *@return void
 *<AUTHOR>
 */
void get_sec_sub_board_info(SUB_BOARD_ATTRIBUTE_S *g_sec_sub_board_info, uint16_t board_type);

/**
 *@brief set serial number for security chip.
 *@param[in] pgqio io data stream from USB
 *@param[in] acl_cmd acl command info
 *@param[in] cmd_data acl command data
 *@return ParserStatus
 *@retval PARSER_SUCCESS acl read file success.
 *@retval PARSER_ERROR acl read file fail.
 *<AUTHOR>
 */
int32_t acl_set_sole_serialnumber(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data);

/**
 *@brief read  serial number form security chip.
 *@param[in] pgqio io data stream from USB
 *@param[in] acl_cmd acl command info
 *@param[in] cmd_data acl command data
 *@return ParserStatus
 *@retval PARSER_SUCCESS acl read file success.
 *@retval PARSER_ERROR acl read file fail.
 *<AUTHOR>
 */
int32_t acl_get_sole_serialnumber(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data);

/**
 *@brief register acl cmds for seruirty.
 *<AUTHOR>
 */
void security_register_acl_cmds();



/**
 *@brief direct response log acl.
 *<AUTHOR>
 */
ACL_RESPONSE_LOG_STRUCT_S *construct_log_acl_response_buffer(uint16_t acl_cmd);


/**
 *@brief direct response log acl.
 *<AUTHOR>
 */
void direct_response_log_acl(GQIO_S *pgqio, ACL_CMD_BASE_STRUCT_S *aclCmd, uint16_t status, uint8_t *responseData, uint32_t dataLen);

/**
 *@brief response log acl.
 *<AUTHOR>
 */
void response_log_acl(GQIO_S *pgqio, ACL_RESPONSE_LOG_STRUCT_S *Buffer, uint8_t *responseData, uint32_t dataLen);



/**
 *@brief notify job cancle pages.
 *<AUTHOR>
 */
void notify_job_cancel_pages(uint32_t pages);

void security_prolog(void);

#ifdef __cplusplus
}
#endif
#endif
/**
 * @}
 */
