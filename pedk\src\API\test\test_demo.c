#include "test_demo.h"
#include "PEDK_event.h"

#include <quickjs.h>
#include "stdio.h"
#include "stdlib.h"
#include "string.h"

/* 自定义原生C函数 */
static void test_send2printer(const char *a)
{
	printf("\033[47;31send2printer\033[0m\n");
	
	//SendMsgToMfp(MSG_MOUDLE_PANEL, MSG_PANEL_SUB, a);
	//
	
    return;
}

static void test_threadInit()
{
	client_thread_init();
    return;
}

static void test_getPrinterEvent(char *a, int length)
{
	//RecvMsgToMfp(MSG_MOUDLE_PANEL, MSG_PANEL_SUB, a, length);
    return;
}

static void test_send2printercopy(const char *a)
{
	printf("\033[47;31test_send2printercopy\033[0m\n");
	//SendMsgToMfp(MSG_MOUDLE_COPY, MSG_COPY_SUB, "");
	//
	
    return;
}

/* 
    定义 QuickJS C 函数 
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
JSValue js_test_threadInit(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	test_threadInit();
    return JS_NewString(ctx, "OK");
}

JSValue js_test_send2printer(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if (!JS_IsString(argv[0]))
    {
        return JS_EXCEPTION;
    }
    const char *jscstr = JS_ToCString(ctx, argv[0]);
    test_send2printer(jscstr);
    return JS_NewString(ctx, "OK");
}

JSValue js_test_getPrinterEvent(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	char buf[1024]={0};
	test_getPrinterEvent(buf, sizeof(buf));
	printf("\033[47;31 js_test_getPrinterEvent %s \033[0m\n", buf);
    return JS_NewString(ctx, buf);
}

JSValue js_test_send2printercopy(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if (!JS_IsString(argv[0]))
    {
        return JS_EXCEPTION;
    }
    const char *jscstr = JS_ToCString(ctx, argv[0]);
    test_send2printercopy(jscstr);
    return JS_NewString(ctx, "OK");
}