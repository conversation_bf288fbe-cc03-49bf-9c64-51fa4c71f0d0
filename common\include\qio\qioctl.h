/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qioctl.h
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-25
 * @brief qio ioctl header
 */
#ifndef __QIOCTL_H__
#define __QIOCTL_H__

/*
 * @brief structure used to pass info for reg get/set and dsp pc get/set dsp pc get/set
 */
struct q_regio
{
    unsigned int        reg;                    ///< register to set/get, use actual physical address
    unsigned int        val;                    ///< val to set or val gotten
};

/*
 * @brief structure used to pass co-processor (DSP/TCC/SBE/etc.) completion info back to a user application
 */
struct q_doneinfo
{
    unsigned int        msg;                    ///< return message/value
    unsigned int        detail;                 ///< detail for message
    unsigned long long  endtime;                ///< finish time absolute in HRT usec
    unsigned long long  cycles;                 ///< cycle count where appropriate
};

/*
 * @brief Generic get co-processor done info
 *
 * struct q_doneinfo doneinfo;
 * int    rc;
 *
 * rc = ioctl(fs, QGETDONE, &doneinfo);
 */
#define QGETDONE                    0x9D00

/*
 * @brief generic messages in .msg member of doneinfo
 */
#define QMSG_DONE                   0xff000000
#define QMSG_MSG                    0x01000000
#define QMSG_ABORT                  0x10000000

/*
 * @brief Generic wait for co-processor complete
 *
 * rc = ioctl(fs, QWAITDONE, &doneinfo);
 */
#define QWAITDONE                   0x9D01

/*
 * @brief Generic enable irq (if disabled)
 */
#define QENABLEIRQ                  0x9D02

/*
 * @brief Generic read/write a single 32 bit register
 *
 * struct q_regio regio;
 * int rc;
 *
 * regio.reg = REGADDRESS
 * regio.val = val
 *
 * rc = ioctl(fs, QSETREG, &regio);
 */
#define QSETREG                     0x9A01
#define QGETREG                     0x9A02

/*
 * @brief Multiple register write, for efficiency
 */
#define QSETREGS                    0x9A03
#define QGETREGS                    0x9A04

/*
 * @brief Flexrisc driver
 */
#define QFLEX_MAJOR                 207

/*
 * @brief parameters to flexrisc for command and back from flex for response/notification
 */
struct q_flexr_io
{
    unsigned char       msg;                    ///< message, command, response, etc. code
    int                 args;                   ///< count of valid parms/results in args
    unsigned char       val[15];                ///< values/parameters from/to flexrisc
};

/*
 * @brief Flexrisc ioctls
 *
 * send command
 *
 * struct g_flexr_io cmd;
 * int rc;
 *
 * cmd.msg = cmdtosend
 * cmd.args = 0
 * rc = ioctl(hflex, QFLEXR_SEND_COMMAND, &cmd);
 * rc = ioctl(hflex, QFLEXR_GET_RESPONSE, &cmd);
 * rc = ioctl(hflex, QFLEXR_GET_RESPONSE, &cmd);
 */
#define QFLEXR_SEND_COMMAND         0x9A04
#define QFLEXR_GET_RESPONSE         0x9A05
#define QFLEXR_GET_NOTIFICATION     0x9A06

/*
 * @brief check/wait for a response (like select)
 *
 * int block = 0; // 1 for blocking wait
 * int rc;
 *
 * rc = ioctl(hflex, QFLEXR_WAIT_RESPONSE,     &block);
 * rc = ioctl(hflex, QFLEXR_WAIT_NOTIFICATION, &block);
 */
#define QFLEXR_WAIT_RESPONSE        0x9A08
#define QFLEXR_WAIT_NOTIFICATION    0x9A09

/*
 * @brief GPDMA driver
 */
#define QGPDMA_MAJOR                208

#define QVIRT2PHYS                  0x9E00
#define QPHYS2VIRT                  0x9E01

struct q_gpdma_xfer
{
    void*               virt_addr;
    void*               phys_addr;
    unsigned int        bytes;
};

/*
 * @brief DSP Driver
 */
#define QDSP_MAJOR                  209

/*
 * @brief done messages from dsp to arm
 */
#define QDSP_HALTED                 QMSG_DONE   ///< dsp has halted

#define QDSP_MSG_ABT                QMSG_ABORT  ///< dsp has aborted (trap)
#define QDSP_MSG                    QMSG_MSG    ///< dsp has message, detail in detail
#define QDSP_MSG_REG                0x10000000  ///< dsp has a "print register" message
#define QDSP_MSG_STR                0x11000000  ///< dsp has a "print string" message

/*
 * @brief DSP Start Message: start the DSP running at address.  Passes the
 * text and data segment params as well, assumes the DSP driver has
 * access (cached) to those segments already.  The text and data
 * addresses are the *user-mode* addresses as known by the application
 * and are used only as a lookup tag, not as an address
 */
struct q_dsp_start
{
    unsigned int        start_addr;
    void*               text_addr;
    unsigned int        text_size;
    void*               data_addr;
    unsigned int        data_size;
    unsigned int        nparams;
    unsigned int        params[32];
};

/*
 * @brief set the DSP's code base register to a physical address after copying the code and data as specified in a q_dsp_start structure
 */
#define QDSPLOADMODULE              0x9A10

/*
 * @brief get the DSP instruction at PC in regio.reg  (offset from base code) into regio.val
 */
#define QDSPGETINST                 0x9A11

/*
 * @brief set a register with value, uses struct q_regio* as parameter set reg to actual physical address of register
 */
#define QDSPSETREG                  QSETREG

/*
 * @brief get a value from a register, uses struct qscan_regio* as parameter
 */
#define QDSPGETREG                  QGETREG

/*
 * @brief start the DSP with params (see struct q_dsp_start above) after loaded (or typically setting from cache) text and data segments as per the q_dsp_start struct
 *
 * int ret;
 * struct q_dsp_start strt;
 * <fill in strt>
 * ret = ioctl(fd, QDSPEXECUTE, &strt);
 */
#define QDSPEXECUTE                 0x9A03

/*
 * @brief get the least recent mailbox entry (done info)
 */
#define QDSPGETDONEINFO             0x9A04

/*
 * @brief Scanner Driver
 */
#define QSCAN_MAJOR                 210
#define QSCAN_PUSH_CMD              0x9B00


/*
 * @brief ring buffer for flexrisc notifications and responses
 */
#define MAX_FLEXNOTES               128

struct q_scanio
{
    int                 scanop;                 /// opcode to do (see qscan.h)
    unsigned long       iparm;
    void*               pparm;
};

/*
 * @brief parameters to/from flexrisc for scanning
 */
struct q_scanfrio
{
    unsigned long       x0;
    unsigned long       x1;
    unsigned long       x2;
    unsigned long       x3;
};

/*
 * @brief Printer band-dma Driver
 */
#define QPRINT_MAJOR                211

#define QPRINT_BIG_ENDIAN           0x0001
#define QPRINT_BIT_SWAPPED          0x0002
#define QPRINT_JBIG_COMPRESSED      0x0004


/*
 * @brief DMA and BAND Memory Driver
 */
#define QMEM_MAJOR                  212

/*
 * @brief structure to pass back allocation data
 */
struct q_mem_alloc
{
    unsigned int        req_size;               ///< requested size in bytes
    unsigned int        ret_size;               ///< actual alloced size in bytes
    void*               ret_addr;               ///< returned physical address
    int                 cached;                 ///< should the allocated memory be cacheable
};

/*
 * @brief Allocate a block of physcialy contiguous (kernel) memory.  Pass in the size in bytes and get back a physical address pointer
 *
 * struct q_mem_alloc val = { size, 0, NULL, 0 };
 * ioctl(fd, QMEMALLOC, (void*)&val);
 */
#define QMEMALLOC                   0x9C21

/*
 * @brief Allocates from coherent "DMA" uncached area
 */
#define QMEMALLOCDMA                0x9C22

/*
 * @brief Allocates from on chip scratch pad area (will cause it to be mapped the first alloc)
 */
#define QMEMALLOCSPAD               0x9C23

/* 
 * @brief frees the mem.  Pass in the physical address returned from QMEMALLOC
 */
#define QMEMFREE                    0x9C26
#define QMEMCLEANUP                 0x9c27


#define QMEMGETMAXALLOCSIZE         0x9C30
#define QMEMGETMAXALLOCS            0x9C31
#define QMEMGETCURALLOCS            0x9C32

#define QMEMGETAVAILABLE            0x9C34
#define QMEMGETSPADAVAILABLE        0x9C35
#define QMEMGETTOTALAVAILABLE       0x9C36

/* 
 * @brief for cached buffers that have to be seen by hardware 
 */
#define QMEMFLUSH                   0x9C40
#define QMEMINVALIDATE              0x9C41
#define QMEMFLUSHINVALIDATE         0x9C42

/* 
 * @brief access to L2 cache controller statistics 
 */
#define QMEML2STATSCONTROL          0x9C50      ///< stop, count hits, count misses
#define QMEML2STATSGET              0x9C51      ///< get current stats

#define QMEML2STATS_COUNT_STOP      0
#define QMEML2STATS_COUNT_HITS      1
#define QMEML2STATS_COUNT_MISSES    2

/* 
 * @brief structure to pass back L2 cache controller statistics
 */
struct q_l2_stats
{
    unsigned int        cycles;
    unsigned int        hits;
    unsigned int        misses;
    unsigned int        waits;
    unsigned int        mhits;                  ///< multiple hits
    unsigned int        mhits_detected;         ///< multiple hits detected
};

/* 
 * @brief printer status
 */
#define QFLASH_DYNAMIC_STATUS       0x9BA0
#define QFLASH_STATIC_STATUS        0x9BA1
#define QSET_SERIAL                 0x9BA2
#define QREAD_USB_1284_STRING       0x9BA3
#define QSET_USB_1284_STRING        0x9BA4
#define QFLUSH_USER_LIST            0x9BA5
#define QGET_SOLE_SERIAL_NUMBER     0x9BAA      ///< 用于隔离器获取设备唯一序列号
#define QFLASH_AUDIT_JOBS_INFO      0x9BAB      ///< 用于软固件进行审计信息交互
#define QUSBD_SET_CILIB_DATA        0x9BAD
#define QUSBD_GET_CILIB_DATA        0x9BAE

/*
 * @brief struct used to transfer data from app to driver
 */
typedef struct tag_ifreq_s
{
    unsigned int        len;
    void*               data;
}
USB_IFREQ_S;

#define QTCC_MAJOR                  213         ///< TCC driver
#define QSBE_MAJOR                  214         ///< SBE driver
#define QJBIG_MAJOR                 215         ///< JBIG driver
#define QIJAC_MAJOR                 216         ///< IJAC driver
#define QFIR_MAJOR                  217         ///< FILTER h/w block
#define QSCAL_MAJOR                 218         ///< SCALER h/w block
#define QSCRN_MAJOR                 219         ///< SCREENER h/w block
#define QM3_MAJOR                   227         ///< Cortex M3 driver
#define QM3_MAX_ARGS                30
#define QADC_MAJOR                  242         ///< ADC driver
#define QLCD_MAJOR                  243         ///< LCD driver

/*
 * @brief parameters to Cortex M3 for command and back for response/notification
 */
struct q_m3_io
{
    unsigned int        msg;                    ///< message, command, response, etc. code
    int                 args;                   ///< count of valid parms/results in args
    unsigned int        val[QM3_MAX_ARGS];      ///< values/parameters from/to flexrisc
};

struct q_m3_io2
{
    unsigned int        len;                    ///< message len
    unsigned int        val[QM3_MAX_ARGS];      ///< message buf
};

/*
 * @brief m3 ioctls - note same as flexrisc numbers
 *
 * struct g_m3_io cmd;
 * int rc;
 *
 * cmd.msg = cmdtosend
 * cmd.args = 0
 * rc = ioctl(hm3, QFLEXR_SEND_COMMAND, &cmd);
 * rc = ioctl(hm3, QFLEXR_GET_RESPONSE, &cmd);
 */
#define QM3_SEND_COMMAND            0x9A04
#define QM3_GET_RESPONSE            0x9A05
#define QM3_GET_NOTIFICATION        0x9A06
#define QM3_SEND_COMMAND_SECOND     0x9A07
#define QM3_GET_ENABLED_PRINT       0x9C00
#define QM3_SET_SLEEP_STATUS        0x9C01
#define QM3_SET_PRINT_MODE          0x9C02

/*
 * @brief check/wait for a response (like select)
 *
 * int block = 0; // 1 for blocking wait
 * int rc;
 *
 * rc = ioctl(hm3, QM3_WAIT_RESPONSE,     &block);
 * rc = ioctl(hm3, QM3_WAIT_NOTIFICATION, &block);
 */
#define QM3_WAIT_RESPONSE           0x9A08
#define QM3_WAIT_NOTIFICATION       0x9A09

/*
 * @brief Start/stop the M3 executing
 *
 * int start = 1; // 1 = run, use 0 for stop
 * int rc = ioctl(hm3, QM3_EXECUTE, &start);
 */
#define QM3_EXECUTE                 0x9A03

#define QM3_PORRST                  0x9A0A

/*
 * @brief Security Protocol Accelerator driver
 */
#define QSECUR_MAJOR                228

/*
 * @brief printk a mapping between task name and thread id
 */
#define SCDLOGMAP                   0x9e00
#define SCDLOG_MAP_LEN              19

/*
 * @brief printk an event
 */
#define SCDLOGEVENT                 0x9e01

/*
 * @brief System bus access driver
 */
#define QSYSBUS_MAJOR               231

#define QSETREG8                    0x9C01
#define QGETREG8                    0x9C02

struct q_regio8
{
    unsigned int        reg;                    ///< register to set/get, use actual physical address
    unsigned char       val;                    ///< val to set or val gotten
};

#define QSYSBUS_MAP                 0x9C03

struct q_sysbus_map
{
    unsigned int        phys;                   ///< physical address to map
    unsigned int        size;                   ///< size in bytes to map
};

/*
 * @brief Vopu driver (appears as /dev/fb0, i.e.: c 29 0
 */
#define QVOPU2_GET_TOUCH            0x9D04
#define QVOPU2_WAIT_TOUCH           0x9D05

/*
 * @brief Mali (GPU) driver
 */
#define QMALI_MAJOR                 232

/*
 * @brief consumable I2c driver
 */
#define QCONSUMABLE_MAJOR           246
#define QCON_WRITE_CMD              0x9D08
#define QCON_READ_CMD               0x9D09

#define QOTP_MAJOR                  238

/* 
 * @brief Use 'O' as magic number
 */
#define QOTP_IOC_MAGIC              'O'

#define QOTP_IOCWRIET               _IOW(QOTP_IOC_MAGIC, 1, int)
#define QOTP_IOCREAD                _IOR(QOTP_IOC_MAGIC, 2, int)
#define QOTP_SET_FLASHMAGIC         _IOW(QOTP_IOC_MAGIC, 3, int)
#define QOTP_GET_FLASHMAGIC         _IOR(QOTP_IOC_MAGIC, 4, int)

#define QOTP_IOC_MAXNR              4

/*
 * @brief gpio driver
 */
#define QGPIO_MAJOR                 245
#define QGPIO_WIFI_POWER_ON         0x9D06
#define QGPIO_WIFI_POWER_OFF        0x9D07
#define QGPIO_POWER_OFF             0x9D08

/*
 * @brief hsk driver
 */
struct q_hscio_s
{
    unsigned int        reg;
    unsigned int        val;
};

#define QHSK_MAJOR                  247
#define QHSK_GPIO_CNT               0x9D20
#define QHSK_FUNC_RUN               0x9D21      ///< (QHSK_GPIO_CNT          + 1)
#define QHSK_GETSTATE               0x9D22      ///< (QHSK_FUNC_RUN          + 1)
#define QHSK_RESULT                 0x9D23      ///< (QHSK_GETSTATE          + 1)
#define QHSK_GET_KERNEL_KEY         0x9D24      ///< (QHSK_RESULT            + 1)
#define QHSK_CUSTOM_ALGO_CAL_1      0x9D25      ///< (QHSK_GET_KERNEL_KEY    + 1)
#define QHSK_GET_USER_KEY           0x9D26      ///< (QHSK_CUSTOM_ALGO_CAL_1 + 1)
#define QHSK_POWER_ON               0x9D27      ///< (QHSK_GET_USER_KEY      + 1)
#define QHSK_POWER_OFF              0x9D28      ///< (QHSK_POWER_ON          + 1)
#define QHSK_CUSTOM_ALGO_CAL_2      0x9D29      ///< (QHSK_POWER_OFF         + 1)
#define QHSK_UPGRADE_KEY            0x9D2A      ///< (QHSK_CUSTOM_ALGO_CAL_2 + 1)
#define QHSK_GET_VERSION            0x9D2B      ///< (QHSK_UPGRADE_KEY       + 1)
#define QHSK_CUSTOM_ALGO_CAL_3      0x9D2C      ///< (QHSK_GET_VERSION       + 1)
#define QHSK_CUSTOM_ALGO_CAL_4      0x9D2D      ///< (QHSK_CUSTOM_ALGO_CAL_3 + 1)
#define QHSK_CUSTOM_ALGO_CAL_5      0x9D2E      ///< (QHSK_CUSTOM_ALGO_CAL_4 + 1)
#define QHSK_CUSTOM_ALGO_CAL_6      0x9D2F      ///< (QHSK_CUSTOM_ALGO_CAL_5 + 1)
#define QHSK_CUSTOM_ALGO_CAL_7      0x9D30      ///< (QHSK_CUSTOM_ALGO_CAL_6 + 1)

#define QHSF_MAJOR                  248
#define QHSF_GPIO_CNT               0x9D30
#define QHSF_FUNC_RUN               0x9D31      ///< (QHSF_GPIO_CNT  + 1)
#define QHSF_GETSTATE               0x9D32      ///< (QHSF_FUNC_RUN  + 1)
#define QHSF_RESULT                 0x9D33      ///< (QHSF_GETSTATE  + 1)
#define QHSF_CHECK_GET              0x9D34      ///< (QHSF_RESULT    + 1)
#define QHSF_CHECK_SET              0x9D35      ///< (QHSF_CHECK_GET + 1)
#define QHSF_IIC_ERRCNT             0x9D36      ///< (QHSF_CHECK_SET + 1)

#define QUSBD_CONNECT_STATUS        0x9F00
#define QUSBD_PCDRIVER_ERR          0x9F01
#define QUSBD_PC_NO_RESPONSE        0x9F02
#define QUSBD_DEV_SOFT_PLUG         0x9F03
#define QUSBD_PC_DUMMY_STATUS       0x9F04

#define PLED_BACKLIGHT_CONTROL      0x9F10
#define PLPM_FAN_SPEED_CTL          0x9F1b
#define PLCD_DATA_EN_CTL            0x9F1c
#define PLEDX_PANEL_SLEEP           0x9F17

int test_send_calibration_data(void);

#endif /* __QIOCTL_H__ */
/**
 *@}
 */
