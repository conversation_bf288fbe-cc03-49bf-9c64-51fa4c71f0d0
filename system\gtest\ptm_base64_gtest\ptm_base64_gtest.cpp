#include <unistd.h>
#include <stdio.h>
#include <gtest/gtest.h>
#include <pol/pol_types.h>

#include "ptm_base64.h"

char org_str_1[]="this is a demo test string";

char org_str_2[] = "eyJhZGRyIjoiMTExLjExMS4xMS4xMSIsICJwYXRoIjoiLyIsICJwb3J0IjoiMjEiLCAiYW5v\nbnkiOiIxIiwgInVzZXIiOiJhbm9ueW1vdXMiLCAicHN3ZCI6InlvdUBlbWFpbC5jb20iLCAi\nbmFtZSI6IjExIiwgImlkeCI6IjEiLCAicmVjb3JkX2lkIjoiMSJ9LHsiYWRkciI6IjIyLjIy\x0ALjIyLjIyIiwgInBhdGgiOiIvIiwgInBvcnQiOiIyMSIsICJhbm9ueSI6IjEiLCAidXNlciI6\x0AImFub255bW91cyIsICJwc3dkIjoieW91QGVtYWlsLmNvbSIsICJuYW1lIjoiMjIiLCAiaWR4\x0AIjoiMiIsICJyZWNvcmRfaWQiOiIyIn0=";

char org_str_3[]="{\"addr\":\"111.111.11.11\", \"path\":\"/\", \"port\":\"21\", \"anony\":\"1\", \"user\":\"anonymous\", \"pswd\":\"<EMAIL>\", \"name\":\"11\", \"idx\":\"1\", \"record_id\":\"1\"},{\"addr\":\"22.22.22.22\", \"path\":\"/\", \"port\":\"21\", \"anony\":\"1\", \"user\":\"anonymous\", \"pswd\":\"<EMAIL>\", \"name\":\"22\", \"idx\":\"2\", \"record_id\":\"2\"}";

char encode_str[2000];
char decode_str[2000];



void encode_decode_str(char * str)
{

    size_t encode_str_len,decode_str_len;
    memset(encode_str,0, sizeof(encode_str));
    memset(decode_str,0, sizeof(decode_str));

    printf("org_str:%s\n",str);
    printf("org_str len:%d\n",strlen(str));

    //对字串编码
    encode_str_len = base64_encode(str,strlen(str),encode_str,sizeof(encode_str));
    printf("encode_str_len is:%u,real_len is: %d\n",encode_str_len,strlen(encode_str));
    printf("encode_str is:%s\n",encode_str);
    printf("encode_str last byte  is:%d\n",encode_str[encode_str_len]);

    //对字串解码
    decode_str_len = base64_decode(encode_str,encode_str_len,decode_str, sizeof(decode_str));
    printf("decode_str_len is:%u,real_len is: %d\n",decode_str_len,strlen(decode_str));
    printf("org_str is:%s    decode_str is:%s\n",str, decode_str);
}

TEST(base64,encode_decode_case_1) //此处按照测试用例名字命名
{
    encode_decode_str(org_str_1);
    EXPECT_STREQ(decode_str, org_str_1);
}

TEST(base64,encode_decode_case_2) //此处按照测试用例名字命名
{
    encode_decode_str(org_str_2);
    EXPECT_STREQ(decode_str, org_str_2);
}

TEST(base64,encode_decode_case_3) //此处按照测试用例名字命名
{
    encode_decode_str(org_str_3);
    EXPECT_STREQ(decode_str, org_str_3);
}
