/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ippsrv.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-9-27
 * @brief IPP(Internet-Printing-Protocol) Server for port 631
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netsock.h"
#include "netmisc.h"
#include "ringbuf.h"
#include "http_task.h"
#include "ippprot.h"
#include "ippx.h"
#include "power_manager/power_manager.h"

#define BCD_DECODE(d)           ((d >= '0' && d <= '9') ? (d - '0') : ((d >= 'a' && d <= 'f') ? (d - 'a' + 10) : (d - 'A' + 10)))

#define DECL_IO_CTX(o, v)       IO_CTX_S* v = (IO_CTX_S *)((o && o->priv) ? ((PARSER_DATA_S *)(o->priv))->context : NULL);

#define IPP_LOCAL_PORT          ( (uint16_t)(netdata_get_ipp_switch(DATA_MGR_OF(s_ippsrv_ctx)) ? IPP_PORT : 0) )

typedef struct ipp_io_context
{
    RING_BUF_S* ringbuf;
    int32_t     job_end;
    int32_t     job_id;
}
IO_CTX_S;

static int32_t ippsrv_process_jobbody(HTTP_TASK_S* ptask, const char* data, size_t ndata);
static int32_t ippsrv_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata);

static char s_request_user_name[128] = {0};

typedef struct ippsrv_task_private
{
    IPP_CTX_S*      ipp_ctx;
}
PRIV_INFO_S;

typedef struct ippsrv_context
{
    NET_CTX_S*  net_ctx;
    PI_THREAD_T srv_tid;
    char        srv_name[80];
    int32_t     reload_fd[2];
    uint16_t    srv_port;
}
IPPSRV_CTX_S;

static IPPSRV_CTX_S*    s_ippsrv_ctx = NULL;

static int ipp_clear_request_user_name(void)
{
    if ( strlen(s_request_user_name) > 0 )
    {
        NET_DEBUG("clear s_request_user_name");
        memset(s_request_user_name, 0x00, sizeof(s_request_user_name));
    }
    return 0;
}

static int32_t ipp_qio_close(QIO_S* pqio)
{
    DECL_IO_CTX(pqio, pioctx);

    RETURN_VAL_IF(pioctx == NULL, NET_WARN, -1);
    NET_INFO("job %d finish", pioctx->job_id);

    return netjob_mark_finish(pioctx->job_id);
}

static int32_t ipp_qio_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_IO_CTX(pqio, pioctx);
    int32_t   block = 0;
    int32_t   ms = 0;
    int32_t   rc = 0;

    RETURN_VAL_IF(pioctx == NULL, NET_WARN, -1);

    if ( what & QIO_POLL_READ ) /* 可读检测时，判断rbuf中是否有可读数据 */
    {
        if ( tos > 0 || (tos == 0 && tous >= 0) )
        {
            ms += (tos  > 0 ? (tos  * 1000) : 0);
            ms += (tous > 0 ? (tous / 1000) : 0);
        }
        else
        {
            block = 1;
        }

        do
        {
            rc = ringbuf_readable(pioctx->ringbuf);
            if ( rc != 0 )
            {
                break;
            }
            else if ( pioctx->job_end )
            {
                NET_DEBUG("job %d end", pioctx->job_id);
                rc = 1;
                break;
            }
            else if ( rc == 0 )
            {
                NET_DEBUG("job %d rc 0", pioctx->job_id);
            }
            pi_msleep(100);
            ms -= 100;
        }
        while ( ms > 0 || block );
    }
    else
    {
        NET_WARN("unsupported operation: %d", what);
        rc = -1;
    }

    return rc;
}

static int32_t ipp_qio_read(QIO_S* pqio, void* buf, size_t nbuf)
{
    DECL_IO_CTX(pqio, pioctx);

    RETURN_VAL_IF(pioctx == NULL, NET_WARN, QIOEOF);

    return ringbuf_read(pioctx->ringbuf, buf, nbuf);
}

static int32_t ipp_qio_write(QIO_S* pqio, void* buf, size_t nbuf)
{
    NET_WARN("unsupported!");
    return QIOEOF;
}

static int32_t ipp_qio_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    NET_WARN("unsupported!");
    return QIOEOF;
}

static int32_t ipp_qio_copy(QIO_S* pqio, RING_BUF_S* rbuf)
{
    DECL_IO_CTX(pqio, pioctx);
    char*     tmpbuf = NULL;
    int32_t   recv_total;
    int32_t   send_total;
    int32_t   send_count;
    int32_t   rc = 0;

    RETURN_VAL_IF(pioctx == NULL || rbuf == NULL, NET_WARN, -1);

    tmpbuf = (char *)pi_zalloc(TCP_CHUNK_SIZE);
    RETURN_VAL_IF(tmpbuf == NULL, NET_WARN, -1);

    while ( ringbuf_readable(rbuf) > 0 )
    {
        recv_total = ringbuf_read(rbuf, tmpbuf, TCP_CHUNK_SIZE);
        NET_INFO("job %d write %d bytes into rbuf", pioctx->job_id, recv_total);
        for ( send_total = 0; send_total < recv_total; send_total += send_count )
        {
            if ( netjob_get_finish_flag(pioctx->job_id) != 0 )
            {
                NET_INFO("job %d is finished", pioctx->job_id);
                rc = -1;
                break;
            }
            if ( ringbuf_writable(pioctx->ringbuf) )
            {
                send_count = ringbuf_write(pioctx->ringbuf, tmpbuf + send_total, (size_t)recv_total - send_total);
                if ( send_count < 0 )
                {
                    NET_WARN("job %d write %d bytes into rbuf failed!!!", pioctx->job_id, recv_total - send_total);
                    rc = -1;
                    break;
                }
            }
            else
            {
                NET_TRACE("job %d rbuf is full, write waiting...", pioctx->job_id);
                pi_msleep(50);
                send_count = 0;
            }
        }
    }
    pi_free(tmpbuf);

    return rc;
}

/* IPP作业数据接收完成，修改job_end标志 */
static void ipp_qio_end(QIO_S* pqio)
{
    DECL_IO_CTX(pqio, pioctx);

    if ( pioctx != NULL )
    {
        pioctx->job_end = 1;
    }
}

static void ipp_qio_release(QIO_S* pqio)
{
    PARSER_DATA_S*  parser_data;
    IO_CTX_S*       pioctx;

    if ( pqio != NULL )
    {
        if ( pqio->priv != NULL )
        {
            parser_data = (PARSER_DATA_S *)pqio->priv;
            if ( parser_data->context != NULL )
            {
                pioctx = (IO_CTX_S *)parser_data->context;
                if ( pioctx->ringbuf != NULL )
                {
                    ringbuf_destroy(pioctx->ringbuf);
                }
                pi_free(parser_data->context);
                parser_data->context = NULL;
            }
            pi_free(pqio->priv);
            pqio->priv = NULL;
        }
        pi_free(pqio);
    }
}

static QIO_S* ipp_qio_create(void)
{
    PARSER_DATA_S*  parser_data;
    IO_CTX_S*       pioctx;
    QIO_S*          pqio;

    parser_data = (PARSER_DATA_S *)pi_zalloc(sizeof(PARSER_DATA_S));
    if ( parser_data == NULL )
    {
        NET_WARN("alloc PARSER_DATA_S failed: %d<%s>", errno, strerror(errno));
        return NULL;
    }

    parser_data->context = pi_zalloc(sizeof(IO_CTX_S));
    if ( parser_data->context == NULL )
    {
        NET_WARN("alloc IO_CTX_S failed: %d<%s>", errno, strerror(errno));
        pi_free(parser_data);
        return NULL;
    }

    pioctx = (IO_CTX_S *)parser_data->context;
    pioctx->ringbuf = ringbuf_create(TCP_CHUNK_SIZE * 8);
    if ( pioctx->ringbuf == NULL )
    {
        NET_WARN("create ring buffer failed");
        pi_free(parser_data->context);
        pi_free(parser_data);
        return NULL;
    }

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        NET_WARN("alloc QIO_S failed: %d<%s>", errno, strerror(errno));
        ringbuf_destroy(pioctx->ringbuf);
        pi_free(parser_data->context);
        pi_free(parser_data);
        return NULL;
    }

    pqio->close = ipp_qio_close;
    pqio->poll  = ipp_qio_poll;
    pqio->read  = ipp_qio_read;
    pqio->write = ipp_qio_write;
    pqio->seek  = ipp_qio_seek;
    pqio->priv  = parser_data;

    return pqio;
}

static int32_t ipp_job_handler(void* pset)
{
    JOB_REQUEST_S*  pjobreq = NULL;
    ROUTER_MSG_S    sendmsg;
    QIO_S*          pqio;
    void*           pattr;
    int32_t         count;
    int32_t         type;
    int32_t         jobid;
    time_t          now;

    RETURN_VAL_IF(IPPATTRgetJobAttribute(pset, "job-id", &type, &pattr, 0, &count) != 0 || pattr == NULL, NET_WARN, -1);
    jobid = *(int32_t *)pattr;
    RETURN_VAL_IF(jobid <= 0, NET_WARN, -1);

    pqio = netjob_get_pqio(jobid);
    RETURN_VAL_IF(pqio == NULL, NET_WARN, -1);

    RETURN_VAL_IF(ippjob_set_job_properties(jobid) != IPP_OK, NET_INFO, 0);

    pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
    RETURN_VAL_IF(pjobreq == NULL, NET_WARN, -1);

    pjobreq->io_class = IO_CLASS_PRINT;
    pjobreq->io_via   = IO_VIA_IPP;
    pjobreq->pqio     = pqio;

    sendmsg.msgType   = MSG_CTRL_JOB_REQUEST;
    sendmsg.msg1      = jobid;
    sendmsg.msg2      = 0;
    sendmsg.msg3      = pjobreq;
    sendmsg.msgSender = MID_PORT_NET;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &sendmsg);

    pi_time(&now);
    NET_INFO("job %d start, pqio(%p->%p), state(%s)", jobid, pqio, pqio->priv, netjob_state_string(netjob_get_state(jobid)));
    while ( netjob_get_finish_flag(jobid) == 0 || netjob_get_state(jobid) <= NETJOB_STATE_RUNNING )
    {
        if ( pi_time(NULL) - now > 5 )
        {
            NET_DEBUG("job %d running, pqio(%p->%p), state(%s)", jobid, pqio, pqio->priv, netjob_state_string(netjob_get_state(jobid)));
            pi_time(&now);
        }
        pi_msleep(500);
    }
    NET_INFO("job %d end, pqio(%p->%p), state(%s)", jobid, pqio, pqio->priv, netjob_state_string(netjob_get_state(jobid)));

    pi_time(&now);
    IPPATTRsetJobAttribute(pset, "time-at-completed", (void*)&now, 0);

    netjob_free(jobid, 10);
    ipp_clear_request_user_name();
    ipp_qio_release(pqio);

    return 0;
}

static IPP_CTX_S* ipp_create_context(HTTP_TASK_S* ptask)
{
    IPP_CTX_S* ppp;

    ppp = (IPP_CTX_S *)pi_zalloc(sizeof(IPP_CTX_S));
    RETURN_VAL_IF(ppp == NULL, NET_WARN, NULL);

    ppp->outbuf = (char *)pi_zalloc(IPP_MAX_LENGTH);
    if ( ppp->outbuf == NULL )
    {
        NET_DEBUG("alloc outbuf failed: %d<%s>", errno, strerror(errno));
        pi_free(ppp);
        return NULL;
    }

    ppp->rbuf = ringbuf_create(IPP_MAX_LENGTH);
    if ( ppp->rbuf == NULL )
    {
        NET_WARN("create ring buffer failed");
        pi_free(ppp->outbuf);
        pi_free(ppp);
        return NULL;
    }

    return ppp;
}

static void ipp_destroy_context(IPP_CTX_S* ppp)
{
    if ( ppp->rbuf != NULL)
    {
        ringbuf_destroy(ppp->rbuf);
    }
    if ( ppp->outbuf != NULL )
    {
        pi_free(ppp->outbuf);
    }
    pi_free(ppp);
}

static int32_t get_uint8(IPP_CTX_S* ppp, uint8_t* pval)
{
    RETURN_VAL_IF(ringbuf_readable(ppp->rbuf) < 1, NET_WARN, -1);

    ringbuf_read(ppp->rbuf, pval, sizeof(*pval));

    return 0;
}

static int32_t get_uint16(IPP_CTX_S* ppp, uint16_t* pval)
{
    uint16_t val;

    RETURN_VAL_IF(ringbuf_readable(ppp->rbuf) < 2, NET_WARN, -1);

    ringbuf_read(ppp->rbuf, &val, sizeof(val));
    *pval = ntohs(val);

    return 0;
}

static int32_t get_uint32(IPP_CTX_S* ppp, uint32_t* pval)
{
    uint32_t val;

    RETURN_VAL_IF(ringbuf_readable(ppp->rbuf) < 4, NET_WARN, -1);

    ringbuf_read(ppp->rbuf, &val, sizeof(val));
    *pval = ntohl(val);

    return 0;
}

static int32_t get_string(IPP_CTX_S* ppp, int32_t slen, char* dest, int32_t ndest)
{
    RETURN_VAL_IF(ringbuf_readable(ppp->rbuf) < slen, NET_WARN, -1);
    RETURN_VAL_IF(slen >= ndest, NET_WARN, 1);

    ringbuf_read(ppp->rbuf, dest, slen);
    dest[slen] = '\0';

    return 0;
}

static int32_t get_range(IPP_CTX_S* ppp, char* buf, int32_t bufsize)
{
    uint32_t rx;
    uint32_t ry;

    RETURN_VAL_IF(bufsize < 10, NET_WARN, 1);

    RETURN_VAL_IF(get_uint32(ppp, &rx) != 0, NET_WARN, -1);
    RETURN_VAL_IF(get_uint32(ppp, &ry) != 0, NET_WARN, -1);

    snprintf(buf, bufsize, "%u,%u", rx, ry);

    return 0;
}

static int32_t get_resolution(IPP_CTX_S* ppp, char* buf, int32_t bufsize)
{
    uint32_t rx, ry;
    uint8_t  term;

    RETURN_VAL_IF(bufsize < 10, NET_WARN, 1);

    RETURN_VAL_IF(get_uint32(ppp, &rx)  != 0, NET_WARN, -1);
    RETURN_VAL_IF(get_uint32(ppp, &ry)  != 0, NET_WARN, -1);
    RETURN_VAL_IF(get_uint8(ppp, &term) != 0, NET_WARN, -1);

    snprintf(buf, bufsize, "%03ux%03ux%1u", rx, ry, term);

    return 0;
}

static int32_t unset(IPP_CTX_S* ppp, int32_t bytes)
{
    RETURN_VAL_IF(ppp->outcnt < bytes, NET_WARN, -1);

    if ( ppp->outhead >= bytes )
    {
        ppp->outhead -= bytes;
    }
    else
    {
        bytes -= ppp->outhead;
        ppp->outhead = IPP_MAX_LENGTH;
        ppp->outhead -= bytes;
    }
    ppp->outcnt -= bytes;

    return 0;
}

static int32_t set_uint8(IPP_CTX_S* ppp, uint8_t val)
{
    RETURN_VAL_IF(ppp->outcnt + 1 >= IPP_MAX_LENGTH, NET_WARN, -1);

    ppp->outcnt++;
    ppp->outbuf[ppp->outhead++] = (char)val;
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }

    return 0;
}

static int32_t set_uint16(IPP_CTX_S* ppp, uint16_t val)
{
    RETURN_VAL_IF(ppp->outcnt + 2 >= IPP_MAX_LENGTH, NET_WARN, -1);

    ppp->outcnt += 2;
    ppp->outbuf[ppp->outhead++] = (char)(val >> 8);
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }
    ppp->outbuf[ppp->outhead++] = (char)(val & 0xFF);
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }

    return 0;
}

static int32_t set_uint32(IPP_CTX_S* ppp, uint32_t val)
{
    RETURN_VAL_IF(ppp->outcnt + 4 >= IPP_MAX_LENGTH, NET_WARN, -1);

    ppp->outcnt += 4;
    ppp->outbuf[ppp->outhead++] = (char)(val >> 24);
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }
    ppp->outbuf[ppp->outhead++] = (char)(val >> 16);
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }
    ppp->outbuf[ppp->outhead++] = (char)(val >> 8);
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }
    ppp->outbuf[ppp->outhead++] = (char)(val & 0xFF);
    if ( ppp->outhead >= IPP_MAX_LENGTH )
    {
        ppp->outhead = 0;
    }

    return 0;
}

static int32_t set_string(IPP_CTX_S* ppp, const char* psrc, int32_t slen)
{
    RETURN_VAL_IF(ppp->outcnt + slen >= IPP_MAX_LENGTH, NET_WARN, -1);

    ppp->outcnt += slen;
    while ( slen-- > 0 )
    {
        ppp->outbuf[ppp->outhead++] = *psrc++;
        if ( ppp->outhead >= IPP_MAX_LENGTH )
        {
            ppp->outhead = 0;
        }
    }

    return 0;
}

static int32_t set_length_add_string(IPP_CTX_S* ppp, const char* psrc)
{
    int32_t slen;

    slen = (int32_t)strlen(psrc);
    set_uint16(ppp, (short)slen);
    set_string(ppp, psrc, slen);

    return 0;
}

static int32_t set_length_and_encode_octets(IPP_CTX_S* ppp, const char* psrc)
{
    int32_t slen = 0;
    uint8_t octets[IPP_MAX_TEXT];
    uint8_t a, b;

    while ( slen < IPP_MAX_TEXT - 1 && psrc[0] && psrc[1] )
    {
        a = (uint8_t)*psrc++;
        b = (uint8_t)*psrc++;
        octets[slen++] = (BCD_DECODE(a) << 4) | BCD_DECODE(b);
    }
    octets[slen] = 0;
    set_uint16(ppp, (short)slen);
    set_string(ppp, (char*)octets, slen);

    return 0;
}

static int32_t get_id_from_uri(const char* uri)
{
    char* pstr = strrchr(uri, '/');

    RETURN_VAL_IF(pstr == NULL, NET_WARN, -1);

    return (int32_t)strtol(pstr + 1, NULL, 0);
}

static char* check_resident_uri(IPP_CTX_S* ppp, const char* name, char* uri, char* uribuf, size_t bufsize)
{
    URI_ELEMS_S*    uri_elems;
    const char*     uritags[] = { "printer-uri-supported" , "printer-uri" , "printer-more-info" , "printer-supply-info-uri" , "printer-icons" , NULL };
    const uint16_t  uriport[] = {  IPP_PORT               ,  IPP_PORT     ,  HTTP_PORT          ,  TLS_PORT                 ,  HTTP_PORT      , 0    };
    const char*     hostval;
    int32_t         hostlen;
    int32_t         reqport;

    RETURN_VAL_IF(ppp == NULL || ppp->httptask == NULL, NET_WARN, uri);

    hostval = http_task_search_header_field(ppp->httptask, "host");
    NET_DEBUG("name(%s) uri(%s) hostval(%s)", name, uri, hostval);
    RETURN_VAL_IF(STRING_IS_EMPTY(hostval), NET_WARN, uri);

    hostlen = (int32_t)strlen(hostval);
    RETURN_VAL_IF(hostlen >= IPP_MAX_URI, NET_WARN, uri);

    while ( --hostlen > 0 )
    {
        RETURN_VAL_IF(hostval[hostlen] == ']', NET_WARN, uri); ///< no port in request, no check needed?
        BREAK_IF(hostval[hostlen] == ':', NET_NONE);
    }
    reqport = (int32_t)strtol(hostval + hostlen + 1, NULL, 0);

    uri_elems = (URI_ELEMS_S *)pi_zalloc(sizeof(URI_ELEMS_S));
    RETURN_VAL_IF(uri_elems == NULL, NET_WARN, uri);

    for ( int32_t i = 0; uritags[i] != NULL; i++ )
    {
        if ( strcmp(uritags[i], name) == 0 )
        {
            parse_uri(uri, uri_elems);
            NET_TRACE("uri(%s) protocol(%s) host(%s) port(%u) path(%s) param(%s)", uri, uri_elems->proto, uri_elems->host, uri_elems->port, uri_elems->path, uri_elems->parm);
            if ( i == 0 && (strcmp(uri_elems->proto, "ipps") == 0) )
            {
                NET_DEBUG("port changed %u to TLS_PORT for name(%s)", uri_elems->port, uritags[i]);
                uri_elems->port = TLS_PORT;
            }
            else if ( uri_elems->port != uriport[i] )
            {
                NET_DEBUG("port changed %u to uriport[%d](%u)", uri_elems->port, i, uriport[i]);
                uri_elems->port = uriport[i];
            }

            if ( uri_elems->port == HTTP_PORT && strcmp(uri_elems->proto, "http") != 0 )
            {
                NET_DEBUG("protocol changed (%s) to (http)", uri_elems->proto);
                snprintf(uri_elems->proto, sizeof(uri_elems->proto), "http");
            }

            if ( hostlen < sizeof(uri_elems->host) && strncmp(uri_elems->host, hostval, hostlen) != 0 )
            {
                NET_DEBUG("host changed (%s) to (%s)", uri_elems->host, hostval);
                snprintf(uri_elems->host, hostlen + 1, "%s", hostval);
            }

            if ( (reqport != IPP_PORT) && (reqport != TLS_PORT) ) // for IPP-USB requests, force port to same as in host
            {
                NET_DEBUG("port changed %u to %d in host(%s)", uri_elems->port, reqport, hostval);
                uri_elems->port = (uint16_t)reqport;
            }

            snprintf(uribuf, bufsize, "%s://%s:%u%s%s", uri_elems->proto, uri_elems->host, uri_elems->port, uri_elems->path, uri_elems->parm);
            NET_DEBUG("uribuf(%s)", uribuf);
            uri = uribuf;
            break;
        }
    }
    pi_free(uri_elems);

    return uri;
}

static int32_t GetRequestedAttributesCallback(void* cookie, const char* name, int32_t type, void* value)
{
    IPP_CTX_S*  ppp = (IPP_CTX_S *)cookie;
    int32_t     intval;
    char        uribuf[IPP_MAX_URI];
    char*       puv;

    RETURN_VAL_IF(ppp == NULL, NET_WARN, -1);

    NET_TRACE("attr name(%s) type(0x%02X)", name ? name : "", type);
    set_uint8(ppp, (uint8_t)type);
    if ( name )
    {
        // set attr name
        set_length_add_string(ppp, name);
        strncpy(ppp->attrName, name, IPP_MAX_NAME - 1);
        ppp->attrName[IPP_MAX_NAME - 1] = '\0';
    }
    else
    {
        set_uint16(ppp, (short)0);
    }
    // depending on var's type, set the value
    switch (type)
    {
    case IPP_TAG_INTEGER:
    case IPP_TAG_BOOLEAN:
    case IPP_TAG_ENUM:
        intval = *(int *)value;
        if (type == IPP_TAG_BOOLEAN)
        {
            set_uint16(ppp, 1);
            set_uint8(ppp, (intval ? 1 : 0));
        }
        else
        {
            set_uint16(ppp, 4);
            set_uint32(ppp, intval);
        }
        break;
    case IPP_TAG_RANGE:
        set_uint16(ppp, 8);
        intval = strtoul((char*)value, &puv, 0);
        set_uint32(ppp, intval);
        if (puv)
        {
            while (*puv && (*puv == ' ' || *puv == ',' || *puv == '\t' || *puv == ':'))
            {
                puv++;
            }
            intval = strtoul((char*)puv, &puv, 0);
        }
        else
        {
            NET_DEBUG("Missing end of range");
        }
        set_uint32(ppp, intval);
        break;
    case IPP_TAG_RESOLUTION:
        puv = (char *)value;
        set_uint16(ppp, 9);
        intval = strtol(puv, &puv, 10);
        set_uint32(ppp, intval);
        if (puv)
        {
            intval = strtol(puv + 1, &puv, 10);
        }
        set_uint32(ppp, intval);
        if (puv)
        {
            intval = strtol(puv + 1, &puv, 10);
        }
        set_uint8(ppp, (uint8_t)intval);
        break;
    case IPP_TAG_URI:
        value = check_resident_uri(ppp, ppp->attrName, value, uribuf, sizeof(uribuf));
        set_length_add_string(ppp, (char *)value);
        break;
    case IPP_TAG_STRING:
    case IPP_TAG_DATE:
        if (((char*)value)[0] == 't' || ((char*)value)[0] == 'T')
        {
            // octets encoded as text strings
            set_length_add_string(ppp, (char *)value + 1);
        }
        else
        {
            // BCD encoded octets
            set_length_and_encode_octets(ppp, (char*)value);
        }
        break;
    case IPP_TAG_TEXT:
    case IPP_TAG_NAME:
    case IPP_TAG_KEYWORD:
    case IPP_TAG_URISCHEME:
    case IPP_TAG_CHARSET:
    case IPP_TAG_LANGUAGE:
    case IPP_TAG_MIMETYPE:
        set_length_add_string(ppp, (char *)value);
        break;
    case IPP_TAG_BEGIN_COLLECTION:
    case IPP_TAG_END_COLLECTION:
        // expect to be called recursively
        set_uint16(ppp, 0);
        break;
    case IPP_TAG_UNSUPPORTED_VALUE:
        set_uint16(ppp, 0);
        break;
    case IPP_TAG_MEMBERNAME:
        set_length_add_string(ppp, (char *)value);
        break;
    case IPP_TAG_NOVALUE:
        set_uint16(ppp, 0);
        break;
    default:
        NET_WARN("Not a valid type for an attribute: %d", type);
        return 1;
    }
    return 0;
}

static int32_t ipp_is_printer_operation(uint16_t reqcode)
{
    int32_t ret;

    switch ( reqcode )
    {
    case IPP_PRINT_JOB:
    case IPP_PRINT_URI:
    case IPP_VALIDATE_JOB:
    case IPP_CREATE_JOB:
    case IPP_SEND_DOCUMENT:
    case IPP_SEND_URI:
    case IPP_GET_JOB_ATTRIBUTES:
    case IPP_GET_JOBS:
    case IPP_GET_PRINTER_ATTRIBUTES:
    case IPP_PAUSE_PRINTER:
    case IPP_RESUME_PRINTER:
    case IPP_PURGE_JOBS:
    case IPP_SET_PRINTER_ATTRIBUTES:
    case IPP_SET_JOB_ATTRIBUTES:
    case IPP_GET_PRINTER_SUPPORTED_VALUES:
    case IPP_CREATE_PRINTER_SUBSCRIPTION:
    case IPP_CREATE_JOB_SUBSCRIPTION:
    case IPP_GET_SUBSCRIPTION_ATTRIBUTES:
    case IPP_GET_SUBSCRIPTIONS:
    case IPP_RENEW_SUBSCRIPTION:
    case IPP_CANCEL_SUBSCRIPTION:
    case IPP_GET_NOTIFICATIONS:
    case IPP_SEND_NOTIFICATIONS:
    case IPP_GET_PRINT_SUPPORT_FILES:
    case IPP_ENABLE_PRINTER:
    case IPP_DISABLE_PRINTER:
    case IPP_PAUSE_PRINTER_AFTER_CURRENT_JOB:
    case IPP_DEACTIVATE_PRINTER:
    case IPP_ACTIVATE_PRINTER:
    case IPP_RESTART_PRINTER:
    case IPP_SHUTDOWN_PRINTER:
    case IPP_STARTUP_PRINTER:
        ret = 1;
        break;
    default:
        ret = 0;
        break;
    }

    return ret;
}

static int32_t ipp_return_operation_attributes(IPP_CTX_S* ppp)
{
    const char* pattr;
    int32_t     count;
    int32_t     type;

    ppp->outcnt  = sizeof(IPP_HEADER_S);
    ppp->outhead = ppp->outcnt;

    set_uint8(ppp, IPP_TAG_OPERATION);
    set_uint8(ppp, IPP_TAG_CHARSET);
    set_length_add_string(ppp, "attributes-charset");
    if ( IPPATTRgetOperationAttribute(ppp, "attributes-charset", &type, (void *)&pattr, 0, &count) != IPP_OK )
    {
        pattr = "utf-8";
    }
    set_length_add_string(ppp, pattr);
    set_uint8(ppp, IPP_TAG_LANGUAGE);
    set_length_add_string(ppp, "attributes-natural-language");
    if ( IPPATTRgetOperationAttribute(ppp, "attributes-natural-language", &type, (void *)&pattr, 0, &count) != IPP_OK )
    {
        pattr = "en";
    }
    set_length_add_string(ppp, pattr);
    set_uint8(ppp, IPP_TAG_END);

    return IPP_OK;
}

static int32_t ipp_return_job_attributes(IPP_CTX_S* ppp, void *pSet)
{
    const char* preq;
    int32_t     unsupported = 0;
    int32_t     reqcount;
    int32_t     type;
    int32_t     rc;
    int32_t     i;

    IPPATTRrequestAllJobAttributes(pSet, 0);

    rc = IPPATTRgetOperationAttribute(ppp, "requested-attributes", &type, (void *)&preq, 0, &reqcount);
    if ( rc )
    {
        if ( ppp->req.opcode != IPP_GET_JOBS )
        {
            preq = "all";
        }
        else
        {
            preq = "";
            IPPATTRrequestJobAttribute(pSet, "job-id");
            IPPATTRrequestJobAttribute(pSet, "job-uri");
        }
    }

    if ( strcmp(preq, "all") == 0 )
    {
        IPPATTRrequestAllJobAttributes(pSet, 1);
    }
    else if ( strlen(preq) > 0 )
    {
        for ( i = 0; i < reqcount; i++ )
        {
            rc = IPPATTRgetOperationAttribute(ppp, "requested-attributes", &type, (void*)&preq, i, &reqcount);
            if ( rc )
            {
                break;
            }

            if ( strcmp(preq, "all") == 0 )
            {
                IPPATTRrequestAllJobAttributes(pSet, 1);
            }
            else if ( strcmp(preq, "job-template") == 0 )
            {
                IPPATTRrequestAllJobAttributes(pSet, 1);
            }
            else if ( strcmp(preq, "job-description") == 0 )
            {
                IPPATTRrequestAllJobAttributes(pSet, 1);
            }
            else
            {
                if ( IPPATTRrequestJobAttribute(pSet, preq) != IPP_OK && IPPATTRaddUnsupportedAttribute(ppp, preq) == 0 )
                {
                    unsupported++;
                }
                rc = 0;
            }
        }
    }

    unset(ppp, 1);
    if ( unsupported )
    {
        set_uint8(ppp, IPP_TAG_UNSUPPORTED_GROUP);
        IPPATTRgetUnsupportedAttributes(ppp, GetRequestedAttributesCallback, ppp);
    }
    set_uint8(ppp, IPP_TAG_JOB);
    IPPATTRgetRequestedJobAttributes(pSet, GetRequestedAttributesCallback, ppp);
    set_uint8(ppp, IPP_TAG_END);

    return 0;
}

static int ipp_save_request_user_name(IPP_CTX_S* ppp)
{
    int    type;
    int    count;
    void*  pattr;

    if ( !IPPATTRgetOperationAttribute(ppp, "requesting-user-name", &type, &pattr, 0, &count) )
    {
        memcpy(s_request_user_name, (char *)pattr, sizeof(s_request_user_name));
    }
    if( strlen(s_request_user_name) )
    {
        NET_DEBUG("s_request_user_name is %s", s_request_user_name);
    }

    return 0;
}

int ipp_get_request_user_name(char *pdata)
{
    RETURN_VAL_IF(pdata == NULL, NET_WARN, -1);

    NET_DEBUG("s_request_user_name is %s %d", s_request_user_name, sizeof(s_request_user_name));
    memcpy(pdata, s_request_user_name, sizeof(s_request_user_name));

    return 0;
}

static int32_t ipp_create_print_job(IPP_CTX_S* ppp, void** ppset)
{
    IO_CTX_S*   pioctx;
    QIO_S*      pqio;
    void*       pattr;
    void*       pset;
    char        uuid[IPP_MAX_NAME];
    char        uri[IPP_MAX_NAME];
    int32_t     jobid;
    int32_t     count;
    int32_t     type;
    int32_t     len;
    int32_t     rc;
    time_t      now;

    RETURN_VAL_IF(netjob_has_free_task() == 0, NET_WARN, IPP_PRINTER_BUSY);

    RETURN_VAL_IF(IPPATTRcreateJobAttributeSet(&pset) != 0, NET_WARN, IPP_INTERNAL_ERROR);
    if ( ppset != NULL )
    {
        *ppset = pset;
    }

    rc = ippjob_init_job_attributes(ppp, pset);
    if (rc != 0)
    {
        NET_WARN("ippjob_init_job_attributes failed, rc = %d", rc);
        IPPATTRdestroyJobAttributeSet(pset);
        return rc;
    }

    rc = ippjob_set_document_properties(ppp, pset);
    if ( rc != 0 )
    {
        NET_WARN("ippjob_set_document_properties failed, rc = %d", rc);
        IPPATTRdestroyJobAttributeSet(pset);
        return rc;
    }

    pqio = ipp_qio_create();
    if ( pqio == NULL )
    {
        NET_WARN("ipp_qio_create failed");
        IPPATTRdestroyJobAttributeSet(pset);
        return IPP_DEVICE_ERROR;
    }
    pioctx = (IO_CTX_S *)(((PARSER_DATA_S *)pqio->priv)->context);

    jobid = netjob_alloc(NETJOB_TYPE_PRINT, pqio);
    if ( jobid <= 0 )
    {
        NET_WARN("netjob_alloc for QIO_S<%p> failed", pqio);
        IPPATTRdestroyJobAttributeSet(pset);
        ipp_qio_release(pqio);
        return IPP_PRINTER_BUSY;
    }

    if ( IPPATTRsetJobAttribute(pset, "job-id", (void *)&jobid, 0) != 0 )
    {
        NET_WARN("job %d set job-id into job attributes failed", jobid);
        IPPATTRdestroyJobAttributeSet(pset);
        ipp_qio_release(pqio);
        netjob_free(jobid, 0);
        return IPP_DEVICE_ERROR;
    }

    if ( ippjob_set_attribute_handle(jobid, pset) != 0 )
    {
        NET_WARN("job %d set attributes handle failed", jobid);
        IPPATTRdestroyJobAttributeSet(pset);
        ipp_qio_release(pqio);
        netjob_free(jobid, 0);
        return IPP_NOT_POSSIBLE;
    }

    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "printer-uri-supported", &type, &pattr, 0, &count) == 0 )
    {
        snprintf(uri, sizeof(uri), "%s/%d", (char *)pattr, jobid);
        IPPATTRsetJobAttribute(pset, "job-printer-uri", pattr, 0);
        IPPATTRsetJobAttribute(pset, "job-uri", uri, 0);
    }

    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "printer-uuid", &type, &pattr, 0, &count) == 0 )
    {
        len = snprintf(uuid, sizeof(uuid), "%s", (char *)pattr);
        if ( len > 8 && len < sizeof(uuid) )
        {
            snprintf(uuid + len - 8, 9, "%08x", jobid);
        }
        NET_DEBUG("job-uuid = %s", uuid);
        IPPATTRsetJobAttribute(pset, "job-uuid", uuid, 0);
    }

    pi_time(&now);
    IPPATTRsetJobAttribute(pset, "time-at-processing", (void *)&now, 0);

    pioctx->job_id  = jobid;
    ppp->jobid      = jobid;

    return IPP_OK;
}

static int32_t ipp_print_job_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    QIO_S*  pqio;
    void*   pset;
    int32_t rc;

    rc = ipp_create_print_job(ppp, &pset);
    RETURN_VAL_IF(rc != 0, NET_WARN, rc);

    do
    {
        pqio = netjob_get_pqio(ppp->jobid);
        if ( ipp_qio_copy(pqio, ppp->rbuf) < 0 ) /* 检查ppp->rbuf剩余数据并拷贝到pioctx->rbuf */
        {
            NET_WARN("job %d can't copy document into pqio(%p->%p) ringbuf", ppp->jobid, pqio, pqio->priv);
            rc = IPP_NOT_POSSIBLE;
            break;
        }

        if ( netjob_start_task(ppp->jobid, ipp_job_handler, pset) < 0 )
        {
            NET_WARN("netjob_start_task %d failed", ppp->jobid);
            rc = IPP_PRINTER_BUSY;
            break;
        }

        ptask->reqbody_received_callback = ippsrv_process_jobbody;
        ippjob_update_job_state(ppp->jobid);
        ppp->lastDocument = 1;
        ppp->handlingData = 1;
    }
    while ( 0 );

    if ( rc != IPP_OK )
    {
        netjob_free(ppp->jobid, 0);
        ipp_clear_request_user_name();
        ipp_qio_release(pqio);
    }
    return rc;
}

static int32_t ipp_validate_job_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    return IPP_OK;
}

static int32_t ipp_create_job_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    void*   pset;
    int32_t rc;

    ipp_save_request_user_name(ppp);

    rc = ipp_create_print_job(ppp, &pset);
    RETURN_VAL_IF(rc != IPP_OK, NET_WARN, rc);

    ippjob_update_job_state(ppp->jobid);

    return ipp_return_job_attributes(ppp, pset);
}

static int32_t ipp_send_document_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    QIO_S*  pqio;
    void*   pattr;
    void*   pset;
    int32_t reqcount;
    int32_t type;
    int32_t rc;
    int32_t ducument_jobid;
    int32_t count;

    RETURN_VAL_IF(ippjob_get_attribute_handle(ppp->jobid, &pset) != IPP_OK, NET_WARN, IPP_NOT_FOUND);
    RETURN_VAL_IF(IPPATTRgetJobAttribute(pset, "job-id", &type, &pattr, 0, &count) != 0 || pattr == NULL, NET_WARN, -1);
    ducument_jobid = *(int32_t *)pattr;

    RETURN_VAL_IF(ppp->jobid <= 0 || IPPATTRgetOperationAttribute(ppp, "last-document", &type, &pattr, 0, &reqcount) != IPP_OK || pattr == NULL, NET_WARN, IPP_BAD_REQUEST);
    NET_DEBUG("job (%d) ducument_jobid (%d) last-document: %d", ppp->jobid, ducument_jobid, *(int32_t *)pattr);
    ppp->lastDocument = (*(int32_t *)pattr != 0) ? 1 : 0;

    /* bug118571 同个作业中有多份文档问题处理 */
    if(netjob_get_finish_flag(ppp->jobid) != 0 && ppp->lastDocument == 1 && ppp->jobid == ducument_jobid)
    {
        ippjob_update_job_state(ppp->jobid);
        ipp_return_job_attributes(ppp, pset);
        if ( netjob_get_state(ppp->jobid) == NETJOB_STATE_ABORTED )
        {
            NET_DEBUG("job (%d) NETJOB_STATE_ABORTED", ppp->jobid);
            return IPP_DOCUMENT_UNPRINTABLE_ERROR;
        }
        NET_DEBUG("job (%d) netjob_get_finish_flag", ppp->jobid);
        return IPP_OK;
    }
    RETURN_VAL_IF(netjob_get_state(ppp->jobid) > NETJOB_STATE_RUNNING, NET_WARN, IPP_NOT_POSSIBLE);
    RETURN_VAL_IF((pqio = netjob_get_pqio(ppp->jobid)) == NULL, NET_WARN, IPP_NOT_POSSIBLE);

    do
    {
        rc = ippjob_set_document_properties(ppp, pset);
        if ( rc != IPP_OK )
        {
            NET_WARN("job %d can't set document properties, rc(0x%04X)", ppp->jobid, rc);
            break;
        }

        rc = ippjob_set_job_properties(ppp->jobid);
        if ( rc != IPP_OK )
        {
            NET_WARN("job %d can't set document properties, rc(0x%04X)", ppp->jobid, rc);
            break;
        }

        if ( ipp_qio_copy(pqio, ppp->rbuf) < 0 ) /* 检查ppp->rbuf剩余数据并拷贝到pioctx->rbuf */
        {
            NET_WARN("job %d can't copy document into pqio(%p->%p) ringbuf", ppp->jobid, pqio, pqio->priv);
            rc = IPP_NOT_POSSIBLE;
            break;
        }

        if ( netjob_get_state(ppp->jobid) < NETJOB_STATE_PROCESSING )
        {
            if ( netjob_start_task(ppp->jobid, ipp_job_handler, pset) < 0 )
            {
                NET_WARN("netjob_start_task %d failed", ppp->jobid);
                rc = IPP_PRINTER_BUSY;
                break;
            }
        }

        ptask->reqbody_received_callback = ippsrv_process_jobbody;
        ippjob_update_job_state(ppp->jobid);
        ppp->handlingData = 1;
    }
    while ( 0 );

    if ( rc != IPP_OK )
    {
        netjob_mark_cancel(ppp->jobid);
        netjob_free(ppp->jobid, 10);
        ipp_clear_request_user_name();
        ipp_qio_release(pqio);
    }
    return rc;
}

static int32_t ipp_cancel_job_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    ROUTER_MSG_S    sendmsg = { .msgSender = MID_PORT_NET };

    RETURN_VAL_IF(ppp->jobid <= 0 || netjob_get_state(ppp->jobid) > NETJOB_STATE_RUNNING, NET_WARN, IPP_NOT_POSSIBLE);

    netjob_mark_cancel(ppp->jobid);
    sendmsg.msgType = MSG_CTRL_JOB_CANCEL;
    sendmsg.msg1    = ppp->jobid;
    sendmsg.msg2    = 0;
    sendmsg.msg3    = NULL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &sendmsg);

    return IPP_OK;
}

static int32_t ipp_get_job_attributes_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    void*   pset;

    RETURN_VAL_IF(ppp->jobid <= 0, NET_WARN, IPP_BAD_REQUEST);

    ippjob_update_job_state(ppp->jobid);
    if ( ippjob_get_attribute_handle(ppp->jobid, &pset) == IPP_OK )
    {
        ipp_return_job_attributes(ppp, pset);
    }

    return IPP_OK;
}

static int32_t ipp_get_jobs_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    char*   whichjobs;
    char*   username;
    char*   jobuser;
    void*   pattr;
    void*   pset;
    int32_t myjobs;
    int32_t jobid;
    int32_t limit;
    int32_t type;
    int32_t reqcount;
    int32_t completed;
    int32_t ippstate;

    if ( IPPATTRgetOperationAttribute(ppp, "requesting-user-name", &type, (void **)&username, 0, &reqcount) != 0 )
    {
        username = NULL;
    }

    if ( IPPATTRgetOperationAttribute(ppp, "which-jobs", &type, (void **)&whichjobs, 0, &reqcount) != 0 )
    {
        whichjobs = "";
    }

    if ( IPPATTRgetOperationAttribute(ppp, "limit", &type, &pattr, 0, &reqcount) != 0 )
    {
        limit = 5;
    }
    else
    {
        limit = *(int32_t *)pattr;
    }

    if ( IPPATTRgetOperationAttribute(ppp, "my-jobs", &type, &pattr, 0, &reqcount) != 0 )
    {
        myjobs = 0;
        username = NULL;
    }
    else
    {
        myjobs = *(int32_t *)pattr;
        if ( myjobs == 0 )
        {
            username = NULL;
        }
    }

    NET_DEBUG("username(%s), whichjobs(%s), limit(%d), myjobs(%d)", username, whichjobs, limit, myjobs);
    if ( strcmp(whichjobs, "completed") == 0 )
    {
        completed = 1;
    }
    else
    {
        completed = 0;
    }

    for ( jobid = netjob_get_next_jobid(0) ; ( jobid > 0 && limit > 0 ) ; jobid = netjob_get_next_jobid(jobid) )
    {
        if ( ippjob_get_attribute_handle(jobid, &pset) != IPP_OK )
        {
            continue;
        }

        NET_DEBUG("jobid(%d) pset(%p)", jobid, pset);
        if ( IPPATTRgetJobAttribute(pset, "job-originating-user-name", &type, (void **)&jobuser, 0, &reqcount) == 0 &&
                jobuser && username && strcmp(jobuser, username) != 0 )
        {
            continue;
        }

        ippjob_update_job_state(jobid);
        if ( IPPATTRgetJobAttribute(pset, "job-state", &type, &pattr, 0, &reqcount) != 0 )
        {
            ippstate = IPP_JOB_ABORTED;
        }
        else
        {
            ippstate = *(int32_t *)pattr;
        }

        if ( ( completed == 1 && ippstate >= IPP_JOB_CANCELED ) || ( completed == 0 && ippstate < IPP_JOB_CANCELED ) )
        {
            ipp_return_job_attributes(ppp, pset);
            limit--;
        }
    }

    return IPP_OK;
}

static int32_t ipp_get_printer_attributes_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    const char* preq;
    int32_t     unsupported = 0;
    int32_t     reqcount;
    int32_t     type;
    int32_t     rc;
    int32_t     i;

    RETURN_VAL_IF(ringbuf_readable(ppp->rbuf) > 0, NET_INFO, IPP_BAD_REQUEST);

    rc = IPPATTRupdatePrinterAttributes();
    RETURN_VAL_IF(rc != 0, NET_INFO, rc);

    rc = IPPATTRgetOperationAttribute(ppp, "requested-attributes", &type, (void *)&preq, 0, &reqcount);
    if ( rc != IPP_OK )
    {
        NET_DEBUG("[L%d] IPPATTRgetOperationAttribute: %d", __LINE__, rc);
        preq = "all";
    }
    NET_DEBUG("[L%d] preq: '%s'", __LINE__, preq);

    if ( strcmp(preq, "all") == 0 )
    {
        IPPATTRrequestAllPrinterAttributes(ppp, 1);
    }
    else if ( strlen(preq) > 0 )
    {
        for ( i = 0; i < reqcount; i++ )
        {
            rc = IPPATTRgetOperationAttribute(ppp, "requested-attributes", &type, (void*)&preq, i, &reqcount);
            if ( rc != IPP_OK )
            {
                NET_DEBUG("[L%d] IPPATTRgetOperationAttribute: %d", __LINE__, rc);
                break;
            }

            if ( strcmp(preq, "all") == 0 )
            {
                IPPATTRrequestAllPrinterAttributes(ppp, 1);
            }
            else if ( strcmp(preq, "job-template") == 0 )
            {
            }
            else if (strcmp(preq, "printer-description") == 0)
            {
                IPPATTRrequestAllPrinterAttributes(ppp, 1);
            }
            else
            {
                if ( IPPATTRrequestPrinterAttribute(ppp, preq) != IPP_OK && IPPATTRaddUnsupportedAttribute(ppp, preq) == 0 )
                {
                    unsupported++;
                }
                rc = 0;
            }
        }
    }

    unset(ppp, 1);
    if ( unsupported )
    {
        set_uint8(ppp, IPP_TAG_UNSUPPORTED_GROUP);
        IPPATTRgetUnsupportedAttributes(ppp, GetRequestedAttributesCallback, ppp);
    }
    set_uint8(ppp, IPP_TAG_PRINTER);
    IPPATTRgetRequestedPrinterAttributes(ppp, GetRequestedAttributesCallback, ppp);
    set_uint8(ppp, IPP_TAG_END);

    return IPP_OK;
}

static int32_t ipp_identify_printer_operation(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    NET_CTX_S*  net_ctx = s_ippsrv_ctx->net_ctx;
    int32_t     action = AIRPRINT_IDENTIFY_ACTION_NONE;
    int32_t     count;
    int32_t     type;
    char*       pact;

    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, IPP_OK);

    for ( int32_t i = 0; i < IPP_IDENTIFY_ACTIONS_COUNT; ++i )
    {
        BREAK_IF(IPPATTRgetOperationAttribute(ppp, "identify-actions", &type, (void*)&pact, i, &count) != 0, NET_WARN);
        BREAK_IF(type != IPP_TAG_KEYWORD, NET_WARN);

        NET_DEBUG("pact[%u](%s) count(%d)", strlen(pact), pact, count);
        if ( strstr(pact, "display") )
        {
            action |= AIRPRINT_IDENTIFY_ACTION_DISPLAY;
        }
        if ( strstr(pact, "flash") )
        {
            action |= AIRPRINT_IDENTIFY_ACTION_FLASH;
        }
        if ( strstr(pact, "sound") )
        {
            action |= AIRPRINT_IDENTIFY_ACTION_SOUND;
        }
    }

    NETEVT_NOTIFY_I(EVT_TYPE_NET_IPP_IDENTIFY_ACTION_CHANGED, action);
    NET_DEBUG("identify action: 0x%x", action);

    return IPP_OK;
}

static int32_t ipp_parse_operation_attributes(IPP_CTX_S* ppp, uint8_t* next_tag)
{
    char        nambuf[IPP_MAX_NAME];
    char        valbuf[IPP_MAX_NAME];
    char*       pattr;
    int32_t     index = 0;
    int32_t     rc = IPP_OK;
    int32_t     count;
    int32_t     type;
    uint32_t    valint;
    uint16_t    vallen;
    uint16_t    namlen;
    uint8_t     valchar;
    uint8_t     valtag;

    do
    {
        RETURN_VAL_IF(get_uint8(ppp, &valtag) != 0, NET_WARN, IPP_BAD_REQUEST);
        if ( valtag < IPP_TAG_UNSUPPORTED_VALUE )
        {
            break;
        }

        RETURN_VAL_IF(get_uint16(ppp, &namlen) != 0, NET_WARN, IPP_BAD_REQUEST);
        if ( namlen > 0 )
        {
            index = 0;
            RETURN_VAL_IF(get_string(ppp, namlen, nambuf, sizeof(nambuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
        }
        else
        {
            index++;
        }

        RETURN_VAL_IF(get_uint16(ppp, &vallen) != 0, NET_WARN, IPP_BAD_REQUEST);
        switch ( valtag )
        {
        case IPP_TAG_CHARSET:
            if ( IPPATTRgetOperationAttribute(ppp, "attributes-natural-language", &type, (void*)&pattr, 0, &count) == 0
                || IPPATTRgetOperationAttribute(ppp, "printer-uri", &type, (void*)&pattr, 0, &count) == 0 )
            {
                NET_WARN("job %d charset not 1st opattr in req(0x%04X, %u)", ppp->jobid, ppp->req.opcode, ppp->req.id);
                return IPP_BAD_REQUEST;
            }
            RETURN_VAL_IF(get_string(ppp, vallen, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            rc = IPPATTRsetOperationAttribute(ppp, "attributes-charset", valbuf, index);
            break;
        case IPP_TAG_LANGUAGE:
            if ( IPPATTRgetOperationAttribute(ppp, "attributes-charset", &type, (void*)&pattr, 0, &count) != 0
                || IPPATTRgetOperationAttribute(ppp, "printer-uri", &type, (void*)&pattr, 0, &count) == 0 )
            {
                NET_WARN("job %d Natural language not 2nd opattr in req(0x%04X, %u)", ppp->jobid, ppp->req.opcode, ppp->req.id);
                return IPP_BAD_REQUEST;
            }
            RETURN_VAL_IF(get_string(ppp, vallen, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            rc = IPPATTRsetOperationAttribute(ppp, "attributes-natural-language", valbuf, index);
            break;
        case IPP_TAG_URI:
            if ( IPPATTRgetOperationAttribute(ppp, "attributes-natural-language", &type, (void*)&pattr, 0, &count) != 0
                 || IPPATTRgetOperationAttribute(ppp, "attributes-charset", &type, (void*)&pattr, 0, &count) != 0)
            {
                NET_WARN("job %d URI before charset or nat-lang in req(0x%04X, %u)", ppp->jobid, ppp->req.opcode, ppp->req.id);
                return IPP_BAD_REQUEST;
            }
            RETURN_VAL_IF(get_string(ppp, vallen, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            if ( strcmp(nambuf, "job-uri") == 0 )
            {
                NET_DEBUG("parse job-uri(%s)", valbuf);
                ppp->jobid = get_id_from_uri(valbuf);
            }
            rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
            break;
        case IPP_TAG_STRING:
        case IPP_TAG_DATE:
        case IPP_TAG_TEXT:
        case IPP_TAG_NAME:
        case IPP_TAG_URISCHEME:
        case IPP_TAG_MIMETYPE:
        case IPP_TAG_KEYWORD:
            RETURN_VAL_IF(get_string(ppp, vallen, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_OPERATION, valtag) : IPP_OK );
            if ( rc == IPP_OK )
            {
                rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
            }
            break;
        case IPP_TAG_INTEGER:
        case IPP_TAG_ENUM:
            RETURN_VAL_IF(get_uint32(ppp, &valint) != 0, NET_WARN, IPP_BAD_REQUEST);
            rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_OPERATION, valtag) : IPP_OK );
            if ( rc == IPP_OK )
            {
                if ( strcmp(nambuf, "job-id") == 0 )
                {
                    NET_DEBUG("parse job-id(%u)", valint);
                    ppp->jobid = (int32_t)valint;
                }
                rc = IPPATTRsetOperationAttribute(ppp, nambuf, &valint, index);
            }
            break;
        case IPP_TAG_BOOLEAN:
            RETURN_VAL_IF(get_uint8(ppp, &valchar) != 0, NET_WARN, IPP_BAD_REQUEST);
            valint = (uint32_t)valchar;
            rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_OPERATION, valtag) : IPP_OK );
            if ( rc == IPP_OK )
            {
                rc = IPPATTRsetOperationAttribute(ppp, nambuf, &valint, index);
            }
            break;
        case IPP_TAG_RANGE:
            RETURN_VAL_IF(get_range(ppp, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_OPERATION, valtag) : IPP_OK );
            if ( rc == IPP_OK )
            {
                rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
            }
            break;
        case IPP_TAG_RESOLUTION:
            RETURN_VAL_IF(get_resolution(ppp, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_OPERATION, valtag) : IPP_OK );
            if ( rc == IPP_OK )
            {
                rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
            }
            break;
        default:
            NET_WARN("job %d bad operation group attribute valtag(0x%X)", ppp->jobid, valtag);
            rc = IPP_BAD_REQUEST;
            break;
        }
    }
    while ( rc == IPP_OK );

    if ( rc == IPP_OK )
    {
        *next_tag = valtag;
    }
    return rc;
}

static int32_t ipp_parse_job_attributes(IPP_CTX_S* ppp, uint8_t* next_tag)
{
    char        valbuf[IPP_MAX_NAME + 2];
    char        nambuf[IPP_MAX_NAME];
    char        membuf[IPP_MAX_NAME];
    int32_t     inmember = 0;
    int32_t     collev = 0;
    int32_t     index = 0;
    int32_t     rc = IPP_OK;
    uint32_t    valint;
    uint16_t    vallen;
    uint16_t    namlen;
    uint8_t     valchar;
    uint8_t     valtag;

    do
    {
        RETURN_VAL_IF(get_uint8(ppp, &valtag) != 0, NET_WARN, IPP_BAD_REQUEST);
        if (valtag < IPP_TAG_UNSUPPORTED_VALUE)
        {
            break;
        }

        RETURN_VAL_IF(get_uint16(ppp, &namlen) != 0, NET_WARN, IPP_BAD_REQUEST);
        if ( namlen > 0 )
        {
            index = 0;
            RETURN_VAL_IF(get_string(ppp, namlen, nambuf, sizeof(nambuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
        }
        else
        {
            if ( collev <= 0 || inmember != 0 )
            {
                index++;
            }
        }

        RETURN_VAL_IF(get_uint16(ppp, &vallen) != 0, NET_WARN, IPP_BAD_REQUEST);
        switch ( valtag )
        {
        case IPP_TAG_CHARSET:
        case IPP_TAG_LANGUAGE:
            NET_WARN("job %d unexpected charset or language in job template attrs", ppp->jobid);
            return IPP_BAD_REQUEST;
        case IPP_TAG_URI:
        case IPP_TAG_STRING:
        case IPP_TAG_DATE:
        case IPP_TAG_TEXT:
        case IPP_TAG_NAME:
        case IPP_TAG_URISCHEME:
        case IPP_TAG_MIMETYPE:
        case IPP_TAG_KEYWORD:
            RETURN_VAL_IF(get_string(ppp, vallen, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            if ( collev <= 0 )
            {
                rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_JOB, valtag) : IPP_OK );
                if ( rc == IPP_OK )
                {
                    if ( strcmp(nambuf, "job-uri") == 0 )
                    {
                        NET_DEBUG("parse job-uri(%s)", valbuf);
                        ppp->jobid = get_id_from_uri(valbuf);
                    }
                    rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
                }
            }
            else
            {
                rc = IPPATTRaddCollectionMember(ppp, collev, membuf, valtag, valbuf, index);
                inmember = 1;
            }
            break;
        case IPP_TAG_INTEGER:
        case IPP_TAG_ENUM:
            RETURN_VAL_IF(get_uint32(ppp, &valint) != 0, NET_WARN, IPP_BAD_REQUEST);
            if ( collev <= 0 )
            {
                rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_JOB, valtag) : IPP_OK );
                if ( rc == IPP_OK )
                {
                    if ( strcmp(nambuf, "job-id") == 0 )
                    {
                        NET_DEBUG("parse job-id(%u)", valint);
                        ppp->jobid = (int32_t)valint;
                    }
                    rc = IPPATTRsetOperationAttribute(ppp, nambuf, &valint, index);
                }
            }
            else
            {
                rc = IPPATTRaddCollectionMember(ppp, collev, membuf, valtag, &valint, index);
                inmember = 1;
            }
            break;
        case IPP_TAG_BOOLEAN:
            RETURN_VAL_IF(get_uint8(ppp, &valchar) != 0, NET_WARN, IPP_BAD_REQUEST);
            valint = (uint32_t)valchar;
            if ( collev <= 0 )
            {
                rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_JOB, valtag) : IPP_OK );
                if ( rc == IPP_OK )
                {
                    rc = IPPATTRsetOperationAttribute(ppp, nambuf, &valint, index);
                }
            }
            else
            {
                rc = IPPATTRaddCollectionMember(ppp, collev, membuf, valtag, valbuf, index);
                inmember = 1;
            }
            break;
        case IPP_TAG_RANGE:
            RETURN_VAL_IF(get_range(ppp, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            if ( collev <= 0 )
            {
                rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_JOB, valtag) : IPP_OK );
                if ( rc == IPP_OK )
                {
                    rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
                }
            }
            else
            {
                rc = IPPATTRaddCollectionMember(ppp, collev, membuf, valtag, valbuf, index);
                inmember = 1;
            }
            break;
        case IPP_TAG_RESOLUTION:
            RETURN_VAL_IF(get_resolution(ppp, valbuf, sizeof(valbuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            if ( collev <= 0 )
            {
                rc = ( IPP_PEDANTIC ? IPPATTRcheckOperationAttribute(ppp, nambuf, 1, IPP_TAG_JOB, valtag) : IPP_OK );
                if ( rc == IPP_OK )
                {
                    rc = IPPATTRsetOperationAttribute(ppp, nambuf, valbuf, index);
                }
            }
            else
            {
                rc = IPPATTRaddCollectionMember(ppp, collev, membuf, valtag, valbuf, index);
                inmember = 1;
            }
            break;
        case IPP_TAG_BEGIN_COLLECTION:
            if ( collev >= 0 && collev < IPP_MAX_COLLECTION_DEPTH )
            {
                ppp->colldex[collev] = index;
                ppp->collpmb[collev] = inmember;
            }
            RETURN_VAL_IF(IPPATTRbeginCollection(ppp, &collev, (collev ? membuf : nambuf), index) != IPP_OK, NET_WARN, IPP_BAD_REQUEST);
            inmember = 0;
            break;
        case IPP_TAG_END_COLLECTION:
            if ( collev > 0 && collev <= IPP_MAX_COLLECTION_DEPTH )
            {
                index = ppp->colldex[collev - 1];
                inmember = ppp->collpmb[collev - 1];
            }
            rc = IPPATTRendCollection(ppp, &collev, (collev > 1 ? "<pop>" : nambuf), index);
            break;
        case IPP_TAG_MEMBERNAME:
            RETURN_VAL_IF(get_string(ppp, vallen, membuf, sizeof(membuf)) != 0, NET_WARN, IPP_BAD_REQUEST);
            inmember = 0;
            index = 0;
            break;
        case IPP_TAG_UNSUPPORTED_VALUE:
            NET_DEBUG("job %d unsupported value len(%d)", ppp->jobid, vallen);
            while ( vallen-- > 0 )
            {
                rc = get_uint8(ppp, &valchar);
            }
            rc = IPP_BAD_REQUEST;
            break;
        default:
            NET_WARN("job %d bad job group attribute valtag(0x%X)", ppp->jobid, valtag);
            rc = IPP_BAD_REQUEST;
        }
    }
    while ( rc == IPP_OK );

    if ( rc == IPP_OK )
    {
        *next_tag = valtag;
    }
    return rc;
}

static int32_t ipp_parse_attributes(IPP_CTX_S* ppp)
{
    char*   pattr;
    int32_t count;
    int32_t type;
    int32_t rc;
    uint8_t tag;

    RETURN_VAL_IF(get_uint8(ppp, &tag) != 0, NET_WARN, IPP_BAD_REQUEST);
    IPPATTRrequestAllPrinterAttributes(ppp, 0);

    do
    {
        switch ( tag )
        {
        case IPP_TAG_OPERATION:
            rc = ipp_parse_operation_attributes(ppp, &tag);
            break;
        case IPP_TAG_JOB:
            rc = ipp_parse_job_attributes(ppp, &tag);
            break;
        case IPP_TAG_END:
            if ( IPPATTRgetOperationAttribute(ppp, "attributes-natural-language", &type, (void*)&pattr, 0, &count) != 0
                || IPPATTRgetOperationAttribute(ppp, "attributes-charset", &type, (void*)&pattr, 0, &count) != 0)
            {
                NET_WARN("job %d not enough attributes in req(0x%04X, %u)", ppp->jobid, ppp->req.opcode, ppp->req.id);
                rc = IPP_BAD_REQUEST;
            }
            else
            {
                rc = IPP_OK;
            }
            break;
        default:
            NET_WARN("job %d bad group tag %d (0x%X)", ppp->jobid, tag, tag);
            rc = IPP_BAD_REQUEST;
            break;
        }
    }
    while ( tag != IPP_TAG_END && rc == IPP_OK );

    return rc;
}

static int32_t ipp_parse(HTTP_TASK_S* ptask, IPP_CTX_S* ppp)
{
    void*   pattr;
    int32_t count;
    int32_t type;

    ppp->responseCode = IPP_BAD_REQUEST;
    do
    {
        BREAK_IF(get_uint8(ppp, &(ppp->req.major_version)) != 0, NET_INFO);
        BREAK_IF(get_uint8(ppp, &(ppp->req.minor_version)) != 0, NET_INFO);
        BREAK_IF(get_uint16(ppp, &(ppp->req.opcode)) != 0, NET_INFO);
        BREAK_IF(get_uint32(ppp, &(ppp->req.id)) != 0, NET_INFO);

        NET_DEBUG("reqid(%u) reqcode(0x%04X) from client(%u->%u)", ppp->req.id, ppp->req.opcode, ptask->r_port, ptask->l_port);
        if ( (ppp->req.major_version > IPP_MAX_MAJOR) || (ppp->req.major_version < 1) || (ppp->req.minor_version > IPP_MAX_MINOR) )
        {
            NET_WARN("IPP version %u.%u not supported", ppp->req.major_version, ppp->req.minor_version);
            ppp->responseCode       = IPP_VERSION_NOT_SUPPORTED;
            ppp->req.major_version  = IPP_MAX_MAJOR;
            ppp->req.minor_version  = IPP_MAX_MINOR;
            ipp_return_operation_attributes(ppp);
            break;
        }

        if ( ppp->req.id <= 0 )
        {
            NET_WARN("invalid request id(%u)", ppp->req.id);
            ipp_return_operation_attributes(ppp);
            break;
        }

        // 开始解析Request中的属性(包括operation-attributes-tag、job-attributes-tag、end-of-attributes-tag)
        ppp->responseCode = ipp_parse_attributes(ppp);
        if ( ppp->responseCode != IPP_OK )
        {
            ipp_return_operation_attributes(ppp);
            break;
        }

        // 设定response中operation-attributes-tag部分
        ipp_return_operation_attributes(ppp);

        if ( ipp_is_printer_operation(ppp->req.opcode) )
        {
            if ( IPPATTRgetOperationAttribute(ppp, "printer-uri", &type, (void*)&pattr, 0, &count) != IPP_OK )
            {
                if ( IPPATTRgetOperationAttribute(ppp, "job-uri", &type, (void*)&pattr, 0, &count) != IPP_OK )
                {
                    NET_WARN("printer-uri or job-uri not included in request");
                    ppp->responseCode  = IPP_BAD_REQUEST;
                    break;
                }
            }
        }

        switch ( ppp->req.opcode )
        {
        case IPP_PRINT_JOB:
            ppp->responseCode = ipp_print_job_operation(ptask, ppp);
            break;
        case IPP_VALIDATE_JOB:
            ppp->responseCode = ipp_validate_job_operation(ptask, ppp);
            break;
        case IPP_CREATE_JOB:
            ppp->responseCode = ipp_create_job_operation(ptask, ppp);
            break;
        case IPP_SEND_DOCUMENT:
            ppp->responseCode = ipp_send_document_operation(ptask, ppp);
            break;
        case IPP_CANCEL_JOB:
            ppp->responseCode = ipp_cancel_job_operation(ptask, ppp);
            break;
        case IPP_GET_JOB_ATTRIBUTES:
            ppp->responseCode = ipp_get_job_attributes_operation(ptask, ppp);
            break;
        case IPP_GET_JOBS:
            ppp->responseCode = ipp_get_jobs_operation(ptask, ppp);
            break;
        case IPP_GET_PRINTER_ATTRIBUTES:
            ppp->responseCode = ipp_get_printer_attributes_operation(ptask, ppp);
            break;
        case IPP_IDENTIFY_PRINTER:
            ppp->responseCode = ipp_identify_printer_operation(ptask, ppp);
            break;
        default:
            ppp->responseCode = IPP_OPERATION_NOT_SUPPORTED;
            break;
        }
    }
    while (0);

    NET_DEBUG("reqid(%u) respcode(0x%X)", ppp->req.id, ppp->responseCode);
    ppp->parse_completed = 1;

    return 0;
}

static int32_t ippsrv_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV2(ptask, PRIV_INFO_S, priv);
    int32_t ret = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( strncmp(url, "/ipp", 4 ) == 0 || (method == HTTP_POST && strcmp(url, "/") == 0) )
    {
        // 源代码逻辑:若Bonjour未开启,则直接返回-1,即Bonjour未启用则IPP也无法使用
        // 原因猜测可能是若果将Bonjour关闭,就需要关闭Airprint、Mopria
        // 但是Airprint、Mopria没有开关,就把IPP请求直接干掉了。。。
        // 现逻辑正确判断为IPP请求后在判断Bonjour是否开启
        RETURN_VAL_IF(netdata_get_bonjour_switch(DATA_MGR_OF(s_ippsrv_ctx)) == 0, NET_INFO, -1);
        ptask->reqbody_received_callback = ippsrv_process_reqbody;

        if ( priv->ipp_ctx == NULL )
        {
            NET_DEBUG("create IPP context(%u) for client(%u -> %u)", sizeof(IPP_CTX_S), ptask->r_port, ptask->l_port);
            priv->ipp_ctx = ipp_create_context(ptask);
        }
        RETURN_VAL_IF(priv->ipp_ctx == NULL, NET_WARN, -1);

        IPPATTRclearOperationAttributes(priv->ipp_ctx);
        IPPATTRclearUnsupportedAttributes(priv->ipp_ctx, 1);
        ringbuf_reset(priv->ipp_ctx->rbuf);
        priv->ipp_ctx->responseCode    = IPP_INTERNAL_ERROR;
        priv->ipp_ctx->serviceType     = IPPSERVICE_PRINT;
        priv->ipp_ctx->outhead         = sizeof(IPP_HEADER_S);
        priv->ipp_ctx->outcnt          = sizeof(IPP_HEADER_S);
        priv->ipp_ctx->httptask        = ptask;
        priv->ipp_ctx->parse_completed = 0;
        priv->ipp_ctx->handlingData    = 0;
    }
    else if ( strncmp(url, "/eSCL", 5 ) == 0 )
    {
        esclsrv_construct(ptask);
    }
    else
    {
        websrv_construct(ptask);
    }

    if ( ptask->headers_complete_callback != ippsrv_process_headers ) /* 确认callback被重载，避免无效嵌套 */
    {
        ret = ptask->headers_complete_callback(ptask, method, url, content_length);
    }
    return ret;
}

static int32_t ippsrv_process_jobbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV2(ptask, PRIV_INFO_S, priv);
    IPP_CTX_S*  ppp = ( priv ? priv->ipp_ctx : NULL );
    QIO_S*      pqio = NULL;
    int32_t     wtotal = 0;
    int32_t     nwrite = 0;
    int32_t     result = 0;

    RETURN_VAL_IF(priv == NULL || ppp == NULL, NET_WARN, -1);

    while ( wtotal < (int32_t)ndata )
    {
        pqio = netjob_get_pqio(ppp->jobid);
        DECL_IO_CTX(pqio, pioctx);
        BREAK_IF(pioctx == NULL, NET_WARN);

        if ( ringbuf_writable(pioctx->ringbuf) )
        {
            nwrite = ringbuf_write(pioctx->ringbuf, data + wtotal, (size_t)(ndata - wtotal));
            if ( nwrite > 0 )
            {
                wtotal += nwrite;
                continue;
            }
        }
        NET_DEBUG("job %d ring buffer is full, write waiting...", ppp->jobid);
        pi_msleep(50);
    }

    if ( wtotal != ndata )
    {
        NET_WARN("write IPP job data to ringbuf failed, expect(%d) wtotal(%d)", ndata, wtotal);
        result = -1;
    }
    return result;
}

static int32_t ippsrv_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV2(ptask, PRIV_INFO_S, priv);
    IPP_CTX_S* ppp;

    RETURN_VAL_IF(priv == NULL || priv->ipp_ctx == NULL, NET_WARN, -1);

    ppp = priv->ipp_ctx;
    if ( ppp->handlingData == 0 )
    {
        RETURN_VAL_IF(ppp->parse_completed, NET_TRACE, 0);

        ringbuf_write(ppp->rbuf, data, ndata);
        if ( ringbuf_readable(ppp->rbuf) > TCP_CHUNK_SIZE ) /* more than 64KB */
        {
            NET_WARN("received %d bytes, start to parsing from client(%u->%u)", ringbuf_readable(ppp->rbuf), ptask->r_port, ptask->l_port);
            ipp_parse(ptask, ppp);
        }
    }

    return 0;
}

static int32_t ippsrv_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV2(ptask, PRIV_INFO_S, priv);
    IPP_CTX_S*  ppp = ( priv ? priv->ipp_ctx : NULL );
    const char* reply_type = "application/ipp";
    const char* reply_code = "200 OK";
    const char* reply_body = NULL;
    int32_t     reply_len  = -1;
    void*       pset;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( ppp != NULL )
    {
        if ( ppp->parse_completed == 0 )
        {
            NET_DEBUG("parse ipp context data(%d) for url(%s)", ringbuf_readable(ppp->rbuf), url);
            ipp_parse(ptask, ppp);
        }

        if ( ppp->jobid > 0 && ppp->lastDocument ) /* 最后一份文档数据接收完毕 */
        {
            NET_DEBUG("job %d end from client(%u -> %u)", ppp->jobid, ptask->r_port, ptask->l_port);
            ipp_qio_end(netjob_get_pqio(ppp->jobid));
        }

        if ( ppp->handlingData )
        {
            ippjob_update_job_state(ppp->jobid);
            ppp->responseCode = ippjob_get_attribute_handle(ppp->jobid, &pset);
            if ( ppp->responseCode == IPP_OK )
            {
                ppp->responseCode = ipp_return_job_attributes(ppp, pset);
            }
            if ( ppp->responseCode == IPP_DOCUMENT_UNPRINTABLE_ERROR )
            {
                ipp_return_job_attributes(ppp, pset);
            }
            ppp->handlingData = 0;
        }
        ringbuf_reset(ppp->rbuf);

        if ( ppp->outcnt )
        {
            if ( ppp->outcnt % 512 == 0 ) /* FIXBUG:20599 */
            {
                set_uint8(ppp, '\0');
                NET_WARN("ppp->outbuf[%d] %x", ppp->outcnt, ppp->outbuf[ppp->outcnt]);
            }
            ppp->outhead = 0;
            ppp->outcnt -= 1;
            set_uint8(ppp, ppp->req.major_version);
            ppp->outcnt -= 1;
            set_uint8(ppp, ppp->req.minor_version);
            ppp->outcnt -= 2;
            set_uint16(ppp, ppp->responseCode);
            ppp->outcnt -= 4;
            set_uint32(ppp, ppp->req.id);
        }
        NET_DEBUG("job %d reqid(%u) reqcode(0x%04X) respcode(0x%04X) len(%d)", ppp->jobid, ppp->req.id, ppp->req.opcode, ppp->responseCode, ppp->outcnt);
        reply_body = ppp->outbuf;
        reply_len  = ppp->outcnt;
    }
    else
    {
        reply_code = "500 OUCH";
    }

    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_body != NULL && reply_len > 0 )
    {
        http_task_send(ptask, reply_body, (size_t)reply_len);
    }

    return 0;
}

static int32_t ippsrv_process_chunked(HTTP_TASK_S* ptask, uint32_t chunk_count, uint16_t chunk_size)
{
    DECL_PRIV2(ptask, PRIV_INFO_S, priv);
    IPP_CTX_S* ppp;
    int32_t    len;

    RETURN_VAL_IF(priv == NULL || priv->ipp_ctx == NULL, NET_WARN, -1);

    ppp = priv->ipp_ctx;
    if ( chunk_count == 2 && ppp->parse_completed == 0 ) /* Transfer-Encoding: chunked, 接收完第2个chunk后，执行ipp_parse */
    {
        len = ringbuf_readable(ppp->rbuf);
        NET_DEBUG("chunk has %u bytes, received %d bytes, start to parsing from client(%u->%u)", chunk_size, len, ptask->r_port, ptask->l_port);
        RETURN_VAL_IF(len < (int32_t)chunk_size, NET_WARN, 0);
        ipp_parse(ptask, ppp);
    }

    return 0;
}

static void ippsrv_update_attribute(HTTP_TASK_S* ptask)
{
    char    hostname[64];
    char    srv_name[80];
    char    pdt_name[32];
    char    uri[256];
    int32_t index = 0;

    netdata_get_hostname(DATA_MGR_OF(s_ippsrv_ctx), hostname, sizeof(hostname));
    netdata_get_pdt_name(DATA_MGR_OF(s_ippsrv_ctx), pdt_name, sizeof(pdt_name));

    snprintf(srv_name, sizeof(srv_name), "%s.local", hostname);
    if ( strcmp(srv_name, s_ippsrv_ctx->srv_name) != 0 || ptask->l_port != s_ippsrv_ctx->srv_port )
    {
        NET_DEBUG("IPP service port change %u to %u", s_ippsrv_ctx->srv_port, ptask->l_port);
        s_ippsrv_ctx->srv_port = ptask->l_port;

        NET_DEBUG("IPP service name change '%s' to '%s'", s_ippsrv_ctx->srv_name, srv_name);
        snprintf(s_ippsrv_ctx->srv_name, sizeof(s_ippsrv_ctx->srv_name), "%s", srv_name);

        snprintf(uri, sizeof(uri), "ipp://%s:%u/ipp/print", srv_name, IPP_PORT);
        NET_DEBUG("IPP printer-uri %s", uri);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "uri-authentication-supported", "none", index);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "uri-security-supported", "none", index);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-uri-supported", uri, index);
        index++;

        if ( ptask->l_port != 0 ) /* is not IPP-Over-USB */
        {
            snprintf(uri, sizeof(uri), "ipps://%s:%u/ipp/print", srv_name, TLS_PORT);
            NET_DEBUG("IPPS printer-uri %s", uri);
            ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "uri-authentication-supported", "none", index);
            ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "uri-security-supported", "tls", index);
            ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "printer-uri-supported", uri, index);
            index++;
        }

        /* From printer webpage address for printer-more-info */
        snprintf(uri, sizeof(uri), "http://%s:%u/index.html#airprint-configure", srv_name, HTTP_PORT);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-more-info", uri, 0);

        /* Setup printer-icon uri */
        snprintf(uri, sizeof(uri), "http://%s:%u/img/%s48.png", srv_name, HTTP_PORT, pdt_name);
        NET_DEBUG("printer icon uri48: %s", uri);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-icons", uri, 0);
        snprintf(uri, sizeof(uri), "http://%s:%u/img/%s128.png", srv_name, HTTP_PORT, pdt_name);
        NET_DEBUG("printer icon uri128: %s", uri);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-icons", uri, 1);
        snprintf(uri, sizeof(uri), "http://%s:%u/img/%s512.png", srv_name, HTTP_PORT, pdt_name);
        NET_DEBUG("printer icon uri512: %s", uri);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-icons", uri, 2);

        snprintf(uri, sizeof(uri), "https://%s:%u/index.html", srv_name, TLS_PORT);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-supply-info-uri", uri, 0);
    }
}

int32_t ippsrv_construct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    ippsrv_update_attribute(ptask);
    if ( ptask->priv_subclass[2] == NULL )
    {
        ptask->priv_subclass[2] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[2] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = ippsrv_process_headers;
    ptask->reqbody_received_callback = ippsrv_process_reqbody;
    ptask->request_complete_callback = ippsrv_process_request;
    ptask->chunked_complete_callback = ippsrv_process_chunked;

    return 0;
}

void ippsrv_destruct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    if ( ptask != NULL && ptask->priv_subclass[2] != NULL )
    {
        PRIV_INFO_S* priv = (PRIV_INFO_S *)ptask->priv_subclass[2];
        IPP_CTX_S* ppp = priv->ipp_ctx;
        if ( ppp != NULL )
        {
            if ( ppp->req.opcode == IPP_PRINT_JOB || ppp->req.opcode == IPP_SEND_DOCUMENT )
            {
                NET_DEBUG("job %d end from client(%u -> %u)", ppp->jobid, ptask->r_port, ptask->l_port);
                ipp_qio_end(netjob_get_pqio(ppp->jobid));
            }
            ipp_destroy_context(ppp);
        }
        pi_free(ptask->priv_subclass[2]);
        ptask->priv_subclass[2] = NULL;
    }
    esclsrv_destruct(ptask);
    websrv_destruct(ptask);
}

static void ippsrv_connection(void* arg)
{
    NET_CONN_S*     pnc = (NET_CONN_S *)arg;
    HTTP_TASK_S*    ptask;
    QIO_S*          pqtcp;

    pqtcp = qio_tcp_create(pnc->sockfd, NETQIO_SOCK_NOCLOSE);
    if ( pqtcp != NULL )
    {
        ptask = http_task_create(pqtcp, pnc->remote_port, pnc->local_port);
        if ( ippsrv_construct(ptask) == 0 )
        {
            http_task_process(ptask);
        }
        ippsrv_destruct(ptask);
        http_task_destroy(ptask);
        QIO_CLOSE(pqtcp);
    }
    netsock_close_connection(pnc);
}

static void* ippsrv_thread_handler(void* arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };    /* PORT: 631 */
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    int32_t         max_fd;
    int32_t         pipefd = s_ippsrv_ctx->reload_fd[0];
    int32_t         update = 0;
    int32_t         status;

    while ( 1 )
    {
        FD_ZERO(&rfds);
        FD_SET(pipefd, &rfds);
        max_fd = pipefd;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, IPP_LOCAL_PORT, IPP_MAX_TCP_SERVERS);

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>", errno, strerror(errno));
            update = 1;
            continue;
        }

        if ( FD_ISSET(pipefd, &rfds) )
        {
            read(pipefd, &status, sizeof(status));
            NET_DEBUG("recv(0x%X), reload service socket", status);
            update = 1;
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_ippsrv_ctx), ippsrv_connection, pnc) < 0 )
                    {
                        NET_WARN("add ippsrv_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}

#if CONFIG_USBDEVICE && CONFIG_USBHOST
static void ippusb_connection(void* arg)
{
    USBDCONNECTION_S*   pusbdc = (USBDCONNECTION_S *)arg;
    HTTP_TASK_S*        ptask;
    QIO_S*              pqusb;
    int32_t             devfd;

    devfd = pi_usbd_server_get_hander(pusbdc);
    RETURN_IF(devfd < 0, NET_WARN);

    pqusb = qio_usbd_create(devfd, NULL, NULL);
    if ( pqusb != NULL )
    {
        ptask = http_task_create(pqusb, 0, 0);
        if ( ippsrv_construct(ptask) == 0 )
        {
            http_task_process(ptask);
        }
        ippsrv_destruct(ptask);
        http_task_destroy(ptask);
        QIO_CLOSE(pqusb);
    }
    pi_usbd_server_end_connection(pusbdc);
}

static int32_t ippusb_callback(USBDCONNECTION_S* pusbdc)
{
    NET_DEBUG("IPP connection from pusbdc(%p)", pusbdc);
    RETURN_VAL_IF(pusbdc == NULL, NET_WARN, -1);

    threads_pool_add_task(THREADS_POOL_OF(s_ippsrv_ctx), ippusb_connection, pusbdc);

    return 0;
}
#endif
static void ippsrv_init_attribute(void)
{
    char    printer_series[64] = {0};
    char    printer_uuid[64] = {0};
    char    service_name[64] = {0};
    char    location[256] = {0};
    char    pdt_name[32] = {0};
    char    mfg_name[32] = {0};
    char    uuid[64] = {0};
    char    finishings_str[512];
    char    finishings_val[512];
    int32_t finishings_arr[128];
    int32_t index = 0;
    int32_t ret;

    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "V1.5",   index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "W8",     index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "IS1",    index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "CP99",   index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "PQ4",    index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "OB10",   index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "RS600",  index++);
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "DM1",    index++);
#if CONFIG_COLOR
    ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", "SRGB24", index++);
#endif
    netdata_get_ipp_finisher_info(DATA_MGR_OF(s_ippsrv_ctx), finishings_arr, sizeof(finishings_arr));
    memset(finishings_val, 0, sizeof(finishings_val));
    ret = array_to_string(finishings_arr, sizeof(finishings_arr)/sizeof(int), finishings_str, sizeof(finishings_str));
    if ( ret > 0 )
    {
        snprintf(finishings_val, sizeof(finishings_val), "FN%s", finishings_str);
        NET_DEBUG("finishings_str(%s)", finishings_val);
        ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "urf-supported", finishings_val,index++);
    }

    index = 0;
    for ( int i = 0; i < sizeof(finishings_arr)/sizeof(int); ++i )
    {
        if ( finishings_arr[i] != 0 )
        {
            ippattr_set_printer_attribute_internal(IPPSERVICE_PRINT, "finishings-supported", (void*)&finishings_arr[i], index++);
            ippattr_set_finishings_col(finishings_arr[i], index);
        }
    }

    index = 0;
    for ( int i = 0; i < sizeof(finishings_arr)/sizeof(int); ++i )
    {
        if ( finishings_arr[i] != 0 )
        {
            if (ippattr_set_finishings_col(finishings_arr[i], index) == 0)
            {
                index++;
            }
        }
    }

    if ( index > 0 )
    {
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-finisher", "type=staple;unit=sheets;maxcapacity=50;capacity=-2", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-finisher", "type=saddle-stitch;unit=sheets;maxcapacity=-2;capacity=-2", 1);

        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-finisher-description", "BT-123", 0);
        ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-finisher-description", "BT-ABC", 1);
    }
    netdata_get_mfg_name(DATA_MGR_OF(s_ippsrv_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(DATA_MGR_OF(s_ippsrv_ctx), pdt_name, sizeof(pdt_name));
    snprintf(printer_series, sizeof(printer_series), "%s %s Series", mfg_name, pdt_name);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-make-and-model", printer_series, 0);

    netdata_get_uuid(DATA_MGR_OF(s_ippsrv_ctx), uuid, sizeof(uuid));
    snprintf(printer_uuid, sizeof(printer_uuid), "urn:uuid:%s", uuid);
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-uuid", printer_uuid, 0);

    netdata_get_bonjour_server(DATA_MGR_OF(s_ippsrv_ctx), service_name, sizeof(service_name));
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-dns-sd-name", service_name, 0);

    netdata_get_location(DATA_MGR_OF(s_ippsrv_ctx), location, sizeof(location));
    ippattr_set_printer_attribute_internal(IPPSERVICE_ALL, "printer-location", location, 0);
}

static void ipp_update_port_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    NET_INFO("ipp_update_port_callback");
    if ( s->subject_status & PORT_UPDATE_IPP )
    {
        ippsrv_init_attribute();
        write(s_ippsrv_ctx->reload_fd[1], &(s->subject_status), sizeof(s->subject_status));
    }
}

int32_t ippsrv_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_ippsrv_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_ippsrv_ctx = (IPPSRV_CTX_S *)pi_zalloc(sizeof(IPPSRV_CTX_S));
    RETURN_VAL_IF(s_ippsrv_ctx == NULL, NET_WARN, -1);

    do
    {
        s_ippsrv_ctx->net_ctx = net_ctx;
        pipe(s_ippsrv_ctx->reload_fd);

        BREAK_IF(ippattr_prolog(net_ctx) != 0, NET_WARN);
        ippsrv_init_attribute();

        s_ippsrv_ctx->srv_tid = pi_thread_create(ippsrv_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "ippsrv_thread_handler");
        BREAK_IF(s_ippsrv_ctx->srv_tid == INVALIDTHREAD, NET_WARN);
#if CONFIG_USBDEVICE && CONFIG_USBHOST
        pi_usbd_server_init_ippusb(ippusb_callback);
#endif 
        ret = 0;
    }
    while ( 0 );

    NET_INFO("ippsrv initialize result(%d)", ret);
    if ( ret != 0 )
    {
        ippsrv_epilog();
    }
    else
    {
        netctx_add_netport_observer(net_ctx, ipp_update_port_callback, NULL);
    }
    return ret;
}

void ippsrv_epilog(void)
{
    if ( s_ippsrv_ctx != NULL )
    {
#if CONFIG_USBDEVICE && CONFIG_USBHOST
        pi_usbd_server_release_ippusb();
#endif
        if ( s_ippsrv_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_ippsrv_ctx->srv_tid);
        }
        ippattr_epilog();
        pi_close(s_ippsrv_ctx->reload_fd[0]);
        pi_close(s_ippsrv_ctx->reload_fd[1]);
        pi_free(s_ippsrv_ctx);
        s_ippsrv_ctx = NULL;
    }
}
/**
 *@}
 */
