#include "curl_utils.h"
//#include "pesf_defined_context.h"
#include "runtime/runtime.h"

#include "net_private.h"
#include <pthread.h>

#include <sys/syscall.h>
#define gettid() syscall(__NR_gettid)

#undef NET_LOG_DEBUG
#define NET_LOG_DEBUG printf

typedef struct {
    uv_poll_t poll;
    curl_socket_t sockfd;
    STPeSFCurlPrivate* private;
} STPeSFUVPollContext;

static pthread_once_t curl__init_once = PTHREAD_ONCE_INIT;

static void pesfCurlGlobalInitOnce(void) {
    curl_global_init(CURL_GLOBAL_ALL);
}

static void pesfCurlGlobalInit(void) {
    pthread_once(&curl__init_once, pesfCurlGlobalInitOnce);
}

CURL* pesfCurlEasyInit(CURL* curl_h) {
    pesfCurlGlobalInit();

    if (curl_h == NULL)
        curl_h = curl_easy_init();

    curl_easy_setopt(curl_h, CURLOPT_USERAGENT, "pesf/1.0/"__DATE__);
    curl_easy_setopt(curl_h, CURLOPT_FOLLOWLOCATION, 1L);
    /* only allow HTTP */
#if LIBCURL_VERSION_NUM >= 0x075500 /* added in 7.85.0 */
    curl_easy_setopt(curl_h, CURLOPT_PROTOCOLS_STR, "http,https");
    curl_easy_setopt(curl_h, CURLOPT_REDIR_PROTOCOLS_STR, "http,https");
#else
    curl_easy_setopt(curl_h, CURLOPT_PROTOCOLS, CURLPROTO_HTTP | CURLPROTO_HTTPS);
    curl_easy_setopt(curl_h, CURLOPT_REDIR_PROTOCOLS, CURLPROTO_HTTP | CURLPROTO_HTTPS);
#endif
    /* use TLS v1.1 or higher */
#if LIBCURL_VERSION_NUM >= 0x072200 /* added in 7.34.0 */
    curl_easy_setopt(curl_h, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_1);
#endif

    /* TODO 是否验证服务器证书，1：验证CA，指定CURLOPT_CAPATH，是否预置部分根ca，；0：不验证 */
    curl_easy_setopt(curl_h, CURLOPT_SSL_VERIFYPEER, 0L);

    return curl_h;
}

static void checkCurlmultiInfo(STPeSFCurlPrivate* private) {
    CURLMsg* message;
    int pending = -1;

    while ((message = curl_multi_info_read(private->curlm_h, &pending))) {
        switch (message->msg) {
            case CURLMSG_DONE: {
                /* Do not use message data after calling curl_multi_remove_handle() and
                   curl_easy_cleanup(). As per curl_multi_info_read() docs:
                   "WARNING: The data the returned pointer points to will not survive
                   calling curl_multi_cleanup, curl_multi_remove_handle or
                   curl_easy_cleanup." */
                CURL* easy_handle = message->easy_handle;
                RETURN_IF_FAIL(easy_handle);

                /*
                 * 每个class对象独占一个curlm后，private与curl_private中的内容一致，arg指向的x是同一个
                 */
                //STPeSFCurlPrivate* curl_private = NULL;
                //curl_easy_getinfo(easy_handle, CURLINFO_PRIVATE, &curl_private);
                //NET_LOG_DEBUG("c p %p == %p, c x %p == %p\n", curl_private, private, curl_private->arg, private->arg);

                RETURN_IF_FAIL(private->done_cb);
                private->done_cb(message, private->arg);

                curl_multi_remove_handle(private->curlm_h, easy_handle);
                curl_easy_cleanup(easy_handle);
                easy_handle = NULL;
                break;
            }
            default:
                NET_LOG_ERROR("bad case %d\n", message->msg);
                break;
        }
    }
    NET_LOG_DEBUG("pending %d, x=%p\n", pending, private->arg);
}

static STPeSFUVPollContext* createUvPollCtx(curl_socket_t sockfd, STPeSFCurlPrivate* private) {
    STPeSFUVPollContext* poll_ctx;
    poll_ctx = malloc(sizeof(*poll_ctx));
    if (!poll_ctx)
        return NULL;
    int rc = uv_poll_init_socket(private->uv_loop, &poll_ctx->poll, sockfd);
    if (PESF_UNLIKELY(0 != rc)) {
        free(poll_ctx);
        poll_ctx = NULL;
        return poll_ctx;
    }
    poll_ctx->private = private;
    poll_ctx->sockfd = sockfd;
    poll_ctx->poll.data = poll_ctx;
    NET_LOG_DEBUG("create poll %p\n", poll_ctx);
    return poll_ctx;
}

static void uvPollCloseCallback(uv_handle_t* handle) {
    STPeSFUVPollContext* poll_ctx = handle->data;
    RETURN_IF_FAIL(poll_ctx);
    poll_ctx->private = NULL;
    NET_LOG_DEBUG("free poll %p\n", poll_ctx);
    free(poll_ctx);
    poll_ctx = NULL;
}

static void uvPollCallback(uv_poll_t* handle, int status, int events) {
    STPeSFUVPollContext* poll_ctx = handle->data;
    RETURN_IF_FAIL(poll_ctx);
    STPeSFCurlPrivate* private = poll_ctx->private;
    RETURN_IF_FAIL(private);
    RETURN_IF_FAIL(private->curlm_h);

    int flags = 0;
    if (events & UV_READABLE)
        flags |= CURL_CSELECT_IN;
    if (events & UV_WRITABLE)
        flags |= CURL_CSELECT_OUT;

    int running_handles;
    curl_multi_socket_action(private->curlm_h, poll_ctx->sockfd, flags, &running_handles);
    NET_LOG_DEBUG("%d sockfd %d, flags 0x%x, handles %d, status %d\n", gettid(), poll_ctx->sockfd, flags, running_handles, status);
    // 日志输出过快容易重叠
    //uv_sleep(50);

    checkCurlmultiInfo(private);
}

static int curlHandleSocket(CURL *easy, curl_socket_t s, int action, void* userp, void* socketp) {
    int rc = 0;
    STPeSFCurlPrivate* private = userp;
    RETURN_VAL_IF_FAIL(private, -1);
    RETURN_VAL_IF_FAIL(private->uv_loop, -1);
    NET_LOG_DEBUG("socket %d, action %d\n", s, action);

    switch (action) {
        case CURL_POLL_IN:
        case CURL_POLL_OUT:
        case CURL_POLL_INOUT: {
            STPeSFUVPollContext* poll_ctx = socketp ? socketp : createUvPollCtx(s, private);
            RETURN_VAL_IF_FAIL(poll_ctx, -1);
            curl_multi_assign(private->curlm_h, s, (void *) poll_ctx);

            int events = 0;
            if (action != CURL_POLL_IN)
                events |= UV_WRITABLE;
            if (action != CURL_POLL_OUT)
                events |= UV_READABLE;

            rc = uv_poll_start(&poll_ctx->poll, events, uvPollCallback);
            break;
        }
        case CURL_POLL_REMOVE:
            if (socketp) {
                STPeSFUVPollContext* poll_ctx = socketp;
                uv_poll_stop(&poll_ctx->poll);
                curl_multi_assign(private->curlm_h, s, NULL);
                uv_close((uv_handle_t* ) &poll_ctx->poll, uvPollCloseCallback);
            }
            break;
        default:
            NET_LOG_ERROR("bad action %d\n", action);
            break;
    }

    return rc;
}

/**
 * xhr.c:349 xhr_constructor
 * x->curl_private.uv_timer.data = p;
 * @param handle STPeSFCurlPrivate*
 */
static void uvTimerCallback(uv_timer_t* handle) {
    int running_handles = -1;
    STPeSFCurlPrivate* private;

    RETURN_IF_FAIL(handle);
    private = handle->data;
    RETURN_IF_FAIL(private);

    curl_multi_socket_action(private->curlm_h, CURL_SOCKET_TIMEOUT, 0, &running_handles);
    NET_LOG_DEBUG("handles %d\n", running_handles);

    checkCurlmultiInfo(private);
}

static int curlUvStartStop(CURLM* multi, long timeout_ms, void* userp) {
    int rc = -1;
    STPeSFCurlPrivate* p = userp;
    RETURN_VAL_IF_FAIL(p, 0);
    NET_LOG_DEBUG("timeout_ms %ld, private %p\n", timeout_ms, p);

    if (timeout_ms < 0) {
        rc = uv_timer_stop(&(p->uv_timer));
    } else {
        if (timeout_ms == 0)
            timeout_ms = 1; 

        rc = uv_timer_start(&(p->uv_timer), uvTimerCallback, timeout_ms, 0);
    }
    if (PESF_UNLIKELY(0 != rc)) {
        NET_LOG_ERROR("rc %d\n", rc);
    }

    return 0;
}

CURLM* pesfGetCurlm(STPeSFCurlPrivate* private) {
    RETURN_VAL_IF_FAIL(private, NULL);

    pesfCurlGlobalInit();

    CURLM* curlm_h = curl_multi_init();
    RETURN_VAL_IF_FAIL(curlm_h, NULL);
    curl_multi_setopt(curlm_h, CURLMOPT_SOCKETFUNCTION, curlHandleSocket);
    curl_multi_setopt(curlm_h, CURLMOPT_SOCKETDATA, private);
    curl_multi_setopt(curlm_h, CURLMOPT_TIMERFUNCTION, curlUvStartStop);
    curl_multi_setopt(curlm_h, CURLMOPT_TIMERDATA, private);

    return curlm_h;
}
