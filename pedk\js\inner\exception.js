const {
  Screen,
  //   Panel,
  Label,
  //   LineEdit,
  Button,
  //   RadioButton,
  //   CheckButton,
  //   ImageData,
  //   StyleSheet,
  //   AnimalImage,
  //   Image,
} = pedk.ui.widget;
const {
  ScreenCtrl,
  // LedCtrl,
  // KeyCtrl,
  // BuzzerCtrl
} = pedk.ui;
const screenCtrl = new ScreenCtrl();
const screenRes = screenCtrl.getResolution();
const { x: screenResX, y: screenResY } = screenRes;
const fontSize = 16;
const cell = screenResX / 24;
const screenMargin = 0.5 * cell;
let widgets = [];
const screen = new Screen();
widgets.push(screen);

const appLabel = new Label();
const appLabelText = "[" + process.getAppName() + "]";
const appLabelW = appLabelText.length * fontSize + 8;
const appLabelX = screenResX / 5;
const appLabelY = screenResY / 4 - fontSize / 2;
appLabel.id = "app-label";
appLabel.text = appLabelText;
appLabel.x = appLabelX;
appLabel.y = appLabelY;
appLabel.w = appLabelW;
widgets.push(appLabel);

const msgLabel = new Label();
const msgLabelText = "失去响应，请重启打印机";
const msgLabelW = msgLabelText.length * (fontSize + 4);
const msgLabelX = screenResX / 2 - msgLabelW / 2;
const msgLabelY = screenResY / 2 - fontSize / 2;
msgLabel.id = "msg-label";
msgLabel.text = msgLabelText;
msgLabel.x = msgLabelX;
msgLabel.y = msgLabelY;
msgLabel.w = msgLabelW;
widgets.push(msgLabel);

// let btn = new Button();
// btn.id = "confirm-button";
// const buttonW = 4 * cell;
// const buttonH = 2 * cell;
// const buttonX = screenResX / 2 - buttonW / 2;
// const buttonY = screenResY - buttonH - screenMargin;
// btn.x = buttonX;
// btn.y = buttonY;
// btn.w = buttonW;
// btn.h = buttonH;
// btn.is_valid = true;
// btn.text = "重启";
// btn.is_checked = false;
// btn.cb_released = () => {
  // process.resetPrinter();
// };
// widgets.push(btn);

screenCtrl.draw(widgets);
