/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_string.h
 * @addtogroup string
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal string interface set
 */

#ifndef __POL_STRING_H__
#define __POL_STRING_H__

#include <string.h>
#include <stdio.h>
#include "pol/pol_types.h"
#include <pol/pol_define.h>

PT_BEGIN_DECLS

#if defined(CONFIG_POL_STRING_DEBUG)

#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

/**
 * @brief Calculates the length of the string `s` using the Pi library's string length calculation mechanism.
 * @param[in] s Pointer to the null-terminated string.
 */
size_t pi_strlen(const char *s);

/**
 * @brief Appends at most `n` characters from the
 * string `src` to the string `dest`, overwriting
 * the null character at the end of `dest`, and
 * adds a new null character at the end of the
 * result using the Pi library's string concatenation mechanism.
 * @param[in] dest Pointer to the destination string.
 * @param[in] src Pointer to the source string.
 * @param[in] n The maximum number of characters to be appended.
 */
char* pi_strncat(char *dest, const char *src, size_t n);

/**
 * @brief Copies at most `n` characters from the string `src`
 * to the string `dest`, stopping if a null character is encountered
 * before `n` characters are copied, and adds null characters to `dest
 * ` if `n` is greater than the length of `src` using the Pi
 * library's string copying mechanism.
 * @param[in] dest Pointer to the destination string.
 * @param[in] src Pointer to the source string.
 * @param[in] n The maximum number of characters to be copied.
 */
char* pi_strncpy(char *dest, const char *src, size_t n);

/**
 * @brief Compares the two strings `s1` and `s2` using the Pi library's string comparison mechanism.
 * @param[in] s1 Pointer to the first string to compare.
 * @param[in] s1 Pointer to the second string to compare.
 */
int pi_strcmp(const char *s1, const char *s2);

/**
 * @brief Compares at most `n` characters of the two strings `s1` and `s2` using the Pi library's string comparison mechanism.
 * @param[in] s1 Pointer to the first string to compare.
 * @param[in] s1 Pointer to the second string to compare.
 * @param[in] n The maximum number of characters to compare.
 */
int pi_strncmp(const char *s1, const char *s2, size_t n);

/**
 * @brief  Performs a case-insensitive comparison of at most `n` characters of the two
 * strings `s1` and `s2` using the Pi library's case-insensitive string comparison mechanism.
 * @param[in] s1 Pointer to the first string to compare.
 * @param[in] s1 Pointer to the second string to compare.
 * @param[in] n The maximum number of characters to compare.
 */
int pi_strncasecmp(const char *s1, const char *s2, size_t n);

/**
 * @brief  Searches the string `haystack` for the first
 * occurrence of the substring `needle` using the Pi library's
 * string searching mechanism.
 * @param[in] haystack Pointer to the string to be searched.
 * @param[in] needle Pointer to the substring to search for.
 */
char* pi_strstr(const char *haystack, const char *needle);

/**
 * @brief  Performs a case-insensitive search of the
 * string `haystack` for the first occurrence of the
 * substring `needle` using the Pi library's case-insensitive
 * string searching mechanism.
 * @param[in] haystack Pointer to the string to be searched.
 * @param[in] needle Pointer to the substring to search for.
 */
char* pi_strcasestr(const char *haystack, const char *needle);

/**
 * @brief  Writes formatted output to a string buffer `str` of
 * maximum size `size` based on the format string `format` using
 * the Pi library's formatted printing mechanism.
 * @param[in] str Pointer to the output buffer where the formatted output will be written.
 * @param[in] size The maximum size of the output buffer.
 * @param[in] format The format string that specifies the desired format of the output.
 */
int pi_snprintf(char *str, size_t size, const char *format, ...);

/**
 * @brief Performs a case-insensitive comparison of
 * the two strings `s1` and `s2` using the Pi library's
 * case-insensitive string comparison mechanism.
 * @param[in] s1 Pointer to the first string to compare.
 * @param[in] s1 Pointer to the second string to compare.
 */
int pi_strcasecmp(const char *s1, const char *s2);

/**
 * @brief Creates a duplicate of the string `s` using the Pi library's string duplication mechanism.
 * @param[in] s Pointer to the null-terminated string to be duplicated.
 */
int pi_strdup(const char *s);

/**
 * @brief Allocates a new string and writes formatted output
 * to it based on the format string `format` using the Pi
 * library's dynamic string allocation mechanism.
 * @param[in] strp Pointer to a pointer that will store the address of the newly allocated string.
 * @param[in] format The format string that specifies the desired format of the output.
 */
int pi_asprintf(char **strp, const char *format, ...);

#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

size_t _pi_strlen(const char *callfile ,const uint32_t callline, const char *s);
#define pi_strlen(s)            _pi_strlen(__FILE__,__LINE__,s)

char* _pi_strncat(const char *callfile ,const uint32_t callline, char *dest, const char *src, size_t n);
#define pi_strncat(d,s,n)            _pi_strncat(__FILE__,__LINE__,d,s,n)

char* _pi_strncpy(const char *callfile ,const uint32_t callline, char *dest, const char *src, size_t n);
#define pi_strncpy(d,s,n)            _pi_strncpy(__FILE__,__LINE__,d,s,n)

int _pi_strcmp(const char *callfile ,const uint32_t callline, const char *s1, const char *s2);
#define pi_strcmp(s1,s2)            _pi_strcmp(__FILE__,__LINE__,s1,s2)

int _pi_strncmp(const char *callfile ,const uint32_t callline, const char *s1, const char *s2, size_t n);
#define pi_strncmp(s1,s2,n)            _pi_strncmp(__FILE__,__LINE__,s1,s2,n)

int _pi_strncasecmp(const char *callfile ,const uint32_t callline, const char *s1, const char *s2, size_t n);
#define pi_strncasecmp(s1,s2,n)            _pi_strncasecmp(__FILE__,__LINE__,s1,s2,n)

char* _pi_strstr(const char *callfile ,const uint32_t callline, const char *haystack, const char *needle);
#define pi_strstr(haystack,needle)            _pi_strstr(__FILE__,__LINE__,haystack,needle)

char* _pi_strcasestr(const char *callfile ,const uint32_t callline, const char *haystack, const char *needle);
#define pi_strcasestr(haystack,needle)            _pi_strcasestr(__FILE__,__LINE__,haystack,needle)

int _pi_snprintf(const char *callfile ,const uint32_t callline, char *str, size_t size, const char *format, ...);
#define pi_snprintf(str,size,format,args...)            _pi_snprintf(__FILE__,__LINE__,str,size,format,##args)

int _pi_strcasecmp(const char *callfile, const uint32_t callline, const char *s1, const char *s2);
#define pi_strcasecmp(s1, s2)            _pi_strcasecmp(__FILE__, __LINE__, s1, s2)

char* _pi_strdup(const char *callfile, const uint32_t callline, const char *s);
#define pi_strdup(s)      _pi_strdup(__FILE__, __LINE__, s)

int _pi_asprintf(const char *callfile, const uint32_t callline, char **strp, const char *format, ...);
#define pi_asprintf(strp, fmt, args...)      _pi_asprintf(__FILE__, __LINE__, strp, fmt, ##args)

#endif /* defined(CONFIG_POL_DEBUG_MODE_BACKTRACE) || defined(CONFIG_POL_DEBUG_MODE_CALLER) */

#else /* defined(CONFIG_POL_STRING_DEBUG) */

#define pi_strlen      	strlen
#define pi_strncat     	strncat
#define pi_strncpy     	strncpy
#define pi_strcmp      	strcmp
#define pi_strncmp     	strncmp
#define pi_strncasecmp  strncasecmp
#define pi_strstr      	strstr
#define pi_strcasestr   strcasestr
#define pi_snprintf		snprintf
#define pi_strcasecmp	strcasecmp
#define pi_strdup	    strdup
#define pi_asprintf	    asprintf

#endif /* defined(CONFIG_POL_STRING_DEBUG) */

PT_END_DECLS

#endif /* __POL_STRING_H__ */
/**
 *@}
 */
