#include <stdio.h>
#include <unistd.h>
#include "cmd.h"
#include "pol_mem_trace.h"
#include "pol_threads.h"
#include "pol_mem.h"
#include "pol_io.h"

#include <assert.h>
#include <errno.h>
#include "case.h"
#define CHILD_SLEEP_INTERVAL		10
#define PARENT_SLEEP_INTERVAL		2
typedef enum {
    CHILD_ID_0,
    CHILD_ID_1,
    CHILD_ID_2,
    CHILD_ID_3,
    CHILD_ID_4,
    CHILD_ID_MAX,
};

int parent_loop(void );
int child_0_loop( void );
int child_1_loop( void );
int child_2_loop( void );
int child_3_loop( void );
int child_4_loop( void );


int array_t[16] = {0};
int main(void)
{
    pid_t pid;
    int8_t i;


    assert(CONFIG_POL_MEM_TRACE==1);
    assert(CONFIG_POL_MEM_TRACE_BACKTRACE_LV>0);
    parent_loop();


}

extern void memtrace_heap_dump_node_to_file( char *path );


/*#define MALLOC_CMD		"malloc"
#define REALLOC_CMD		"realloc"
#define ZALLOC_CMD		"zalloc"
#define FREE_CMD		"free"*/
#define DUMP_CMD		"dump"
#define DUMP_FREE_CMD	"dumpfree"


int cmd_hdl(char *cmd,char *param)
{
    if(!strcasecmp(cmd,DUMP_CMD))
    {

        memtrace_heap_dump_node_to_file(param);

    }

    if(!strcasecmp(cmd,DUMP_FREE_CMD))
    {

        memtrace_heap_dump_freelist_to_file(param);

    }

}

#define PARENT_INPUT_PATH	"p_in"

int32_t cmd_recieve_and_handle( int fd  )
{

    char cmd[16];
    char param[16];
    char buf[32];

    pi_memset(buf, 0, sizeof(buf));	
    pi_memset(cmd, 0, sizeof(cmd));
    pi_memset(param, 0, sizeof(param));
    pi_memcmp(param,cmd,8);

    ssize_t ret;
    if(ret = read(fd,buf,sizeof(buf))){
        log("[%s]read string:%s\n",__func__,buf);	
        if(sscanf(buf,"%15s %15s", cmd,param))
        {
            log("[%s]read  cmd:%s,param:%s\n",__func__,cmd,param);	
            return cmd_hdl(cmd,param);
        }
    }
    if(ret == -1){
        log("[%s] pid::%d read error:%s\n",__func__,getpid(),strerror(errno));	
        return -1;
    }
    else {
        return 0;
    }
}



int parent_loop(void )
{	
    void* ptr;

    static uint32_t count = 0;
    int32_t t_id=0,case0_num;
    case0_func c0_func;
    printf("%s\n",__func__);
    mem_trace_init();
    case0_num =get_case_sets_num();

    while(t_id<case0_num)
    {
        c0_func = case0_sets[t_id++];
        if(c0_func()!=PASS)
            sleep(10);

    }
    printf("%s:end\n",__func__);
}



