CC = $(CROSS_COMPILE)gcc

CFLAGS += -fPIC -O2 -Wall -Werror -std=gnu99 -Wno-unused
CFLAGS += -DLinux
CFLAGS += -I./src/inc/private
CFLAGS += -I./src/inc

LDFLAGS += -L./

DEMO_OBJS += src/demo/wifi_unit_test.o

SRV_OBJS += src/srv/event_mgr_service.o src/srv/main.o

CLI_OBJS += src/cli/event_mgr.o

all: libevent_mgr.so event_mgr_service wifi_unit_test

wifi_unit_test: $(DEMO_OBJS) libevent_mgr.so
	$(CC) $(LDFLAGS) -o $@ $(DEMO_OBJS) -levent_mgr $(LINK_LIBS)

event_mgr_service: $(SRV_OBJS)
	$(CC) $(LDFLAGS) -o $@ $^ $(LINK_LIBS)

libevent_mgr.so: $(CLI_OBJS)
	$(CC) $(LDFLAGS) -shared -o $@ $^

clean:
	-rm $(CLI_OBJS)  libevent_mgr.so
	-rm $(SRV_OBJS)  event_mgr_service
	-rm $(DEMO_OBJS) wifi_unit_test
