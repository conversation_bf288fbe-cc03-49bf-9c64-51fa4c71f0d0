/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_crypto.h
 * @addtogroup pol
 * @{
 * @addtogroup crypto
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pol log crypto head file
 */
#ifndef	 __POL_CRYPTO_H__
#define  __POL_CRYPTO_H__

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <openssl/aes.h>
#include <openssl/evp.h>


#define CRYPTO_START_MARKER "ABCD"      ///<crypto start marker
#define CRYPTO_END_MARKER "DCBA"        ///<crypto end marker

/**
 * @brief aes 256 byte ctr encrypt
 * @param[in] *key          : encrypto key
 * @param[in] *plaintext    : encrypto text
 * @param[in] plaintext_len : encrypto text len
 * @param[in] **ciphertext  : encryted text
 * @param[in] *iv           : encrypto iv
 * @retval    > 0         : success
 * @retval    = 0         : fail
 * <AUTHOR> Qi
 * @date      2024-07-23
 */
int32_t aes_256_ctr_encrypt(const unsigned char *key, const unsigned char *plaintext, int32_t plaintext_len, unsigned char **ciphertext, unsigned char *iv);

/**
 * @brief read and decrypt logs
 * @param[in] *key          : encrypto key
 * @param[in] *in_filename  : encrypto log file
 * @param[in] *out_filename : decrypto log file
 * @param[in] *iv           : encrypto iv
 * @retval    = 0         : success
 * @retval    < 0         : fail
 * <AUTHOR> Qi
 * @date      2024-07-23
 */
int32_t read_and_decrypt_logs(const char *in_filename, const unsigned char *key, const unsigned char *iv, const char* out_filename);

#endif /* __POL_CRYPTO_H__ */

/**
 *@}
 */
