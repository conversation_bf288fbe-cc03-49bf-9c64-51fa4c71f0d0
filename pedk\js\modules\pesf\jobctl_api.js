/**
 * @namespace pedk.jobctl
 */

var Module_Main = globalThis.pedk.Module_Main
var Module_Sub = globalThis.pedk.Module_Sub
var listeners = []
var mfp_jobinfo_list = [];
var jobobj = [];

/**
 * 作业信息类
 */
class JobInfo {
    /**
     * 构造函数
     */
    constructor(joblist) {
            console.log('new jobinfo id: ',joblist.id)

            this.id = joblist.id;
            this.type = joblist.type;
            this.state = joblist.state;
            this.user_name = joblist.user_name;
            this.job_name = joblist.job_name;
            this.time = joblist.time;
            this.t_pages = joblist.t_pages;
            this.s_pages = joblist.s_pages;
            this.p_pages = joblist.p_pages;
            this.r_pages = joblist.r_pages;
            this.c_pages = joblist.c_pages;
            this.wonum = joblist.wonum;
    }

    /**
     * 获取作业ID
     * @returns {Number} 作业ID
     */
    getJobId() {
        return this.id;
    }

    /**
     * 获取工单号
     * @returns {Number} 工单号
     */
    getWoNum() {
        return this.wonum;
    }

     /**
     * 获取作业状态
     * @returns {String} 作业状态
     */
    getJobState() {
        return this.state;
    }

    /**
     * 获取用户名
     * @returns {String} 用户名
     */
    getUserName()
    {
        return this.user_name;
    }

    /**
     * 获取作业名
     * @returns {String} 作业名
     */
    getDocumentName() {
        return this.job_name;
    }
    /**
     * 获取启动作业时间
     * @returns {String} 启动作业时间
     */
    getStartTime() {
        this.time = 'EOPNOTSUPP';
        return this.time;
    }
    /**
     * 获取作业类型
     * @returns {String} 作业类型
     */
    getJobType() {
        return this.type;
    }

    /**
     * 获取已打印的页数
     * @returns {Number} 作业已打印的页数
     */
    getPrintPageNum() {
        return this.p_pages;
    }
    /**
     * 获取已打印作业的总页数
     * @returns {Number} 指定作业打印的总页数
     */
    getPrintPageTotalNum() {
        return this.r_pages;
    }

    /**
     * 获取已复印的总页数
     * @returns {Number} 指定作业已复印的页数
     */
    getCopyPageNum() {

        return this.c_pages;
    }
    /**
     * 获取复印作业的总页数
     * @returns {Number} 指定作业复印的总页数
     */
    getCopyPageTotalNum() {

        return this.t_pages;

    }

    /**
     * 获取已扫描的页数
     * @returns {Number} 指定作业已扫描的页数
     */
    getScanPageNum() {

        return this.s_pages;

    }

}


// JobNetInfo 类定义
class JobNetInfo {
    constructor(info) {
        this.job_type = info.job_type || "";
        this.document_name = info.document_name || "";
        this.submission_time = info.submission_time || "";
        this.data_receipt_status = info.data_receipt_status || "";
    }
}

function getNetJobHistoryList(net_job_type) {
    // 调用绑定的 native 函数
    const result = getNetJobHistoryListFromDC(net_job_type);

    // 检查返回值类型
    if (Array.isArray(result)) {
        // 如果已经是数组，直接转换为 JobNetInfo 实例数组
        return result.map(info => new JobNetInfo(info));
    } else if (typeof result === "object" && result !== null) {
        // 如果是对象，尝试转换为数组
        return Object.values(result).map(info => new JobNetInfo(info));
    } else if (typeof result === "string") {
        // 如果是字符串，尝试解析为 JSON
        try {
            const jobArray = JSON.parse(result);
            if (Array.isArray(jobArray)) {
                return jobArray.map(info => new JobNetInfo(info));
            } else {
                console.log("Unexpected result format:", result);
                return [];
            }
        } catch (e) {
            console.log("Failed to parse job history result:", e);
            return [];
        }
    } else {
        console.error("Unexpected return type:", typeof result);
        return [];
    }
}

function strToJson(str) {

    var json = eval("(" + str + ")");

    return json;
}

/**
 * 获取作业列表
 * @returns {Array} 作业列表信息
 */
function getJobList() {

    var i = 0, j = 0;
    var jsonstr = getJobListFromDC();

    if(jsonstr.length === 0) {
        console.log('getJobList is Null , jobinfo_list: ' + mfp_jobinfo_list.length);
        mfp_jobinfo_list = [];
        return mfp_jobinfo_list;

    }
    var jsonarr = strToJson(jsonstr);
    for(i; i < jsonarr.length; i++) {
        console.log('jobobj length is ',jobobj.length);
        for(j=0;j<jobobj.length;j++) {
            console.log('Jobwonum is ',jobobj[j].getWoNum(),' | ',jsonarr[i].wonum);
            if(jobobj[j].getWoNum() == jsonarr[i].wonum) {
                jsonarr[i].type = jobobj[j].getJobType();
                break;
            }
        }
        for (var k = 0; k < mfp_jobinfo_list.length; k++)
        {
            if (mfp_jobinfo_list[k].id == jsonarr[i].id)
            {
                mfp_jobinfo_list.splice(k , 1);
                break;
            }
        }
        mfp_jobinfo_list.push( new JobInfo(jsonarr[i]) );
    }

    return mfp_jobinfo_list;
}


/**
 * 根据工单号取消作业
 * @param {Number} wo_num 工单号
 * @returns {Boolean}
 * <pre>
 * true：取消成功
 * false：取消失败
 * </pre>
 */
export function cancelJobByWoNum(wo_num) {
    var i = 0;

    if(typeof wo_num !== 'number') {
        throw TypeError("Must be only one number parmeter")
    }
    for(i = 0; i < mfp_jobinfo_list.length; i++) {
        if(mfp_jobinfo_list[i].wonum == wo_num) {
            break;
        }
    }
    if(i >= mfp_jobinfo_list.length) {
        console.log('Find Job wo_num [',wo_num,'] Fail' );
        return false;
    }
    mfp_jobinfo_list[i].state = 'JBSts_Cancelling'; //修改作业列表中，该作业状态为JBSts_Cancelling
    console.log('cancel jobid is ', mfp_jobinfo_list[i].id)
    cancelJob(mfp_jobinfo_list[i].id);
    mfp_jobinfo_list.slice(i , 1);

    return true;
}

/*定义结构体*/
class JobctlStruct{
    constructor(obj, wonum){
        this.obj = obj;
        this.wonum = wonum;
    }
    DisptachEvent(wonum, respond, data){
        //将信息状态通知给模块
        if( wonum > 0 && wonum === this.wonum){
            console.log('DisptachEvent wonum:', this.wonum);
            this.obj.notify(respond, data)
        } else
        {
            console.log('DisptachEvent wonum:', this.wonum, ' Fail');
        }
    }
    IsThisFunc(obj, wonum){
        if(obj === this.obj && wonum === this.wonum ){
            return true;
        }
        return false;
    }
}

/*创建单例类*/
class JobctlManager{
    wonum = [];
    constructor(){

        if(this.instance)
        {
            return this.instance
        }
        this.instance = new globalThis.pedk.ObserverManager();
        this.instance.addListeners(this, this.updateJobInfo, Module_Main.MSG_MODULE_JOBCTL, Module_Sub.MSG_JOBCTL_SUB_NOTIFY);
        this.instance.addListeners(this, this.JobInfoChange, Module_Main.MSG_MODULE_JOBCTL, Module_Sub.MSG_JOBCTL_SUB_NET_UPLOAD_INFO);
        this.listeners = []
    }

    addListeners(obj, t_wonum){

        if(t_wonum <= 0 || this.wonum.includes(t_wonum)) {
            console.log('addListeners fail : wonum [', t_wonum, '] already exists');
            return false
        }

        this.wonum.push(t_wonum)
        jobobj.push(obj)
        this.listeners.push( new JobctlStruct(obj, t_wonum) )
        return true;
    }

    removeListeners(obj, t_wonum){
        this.listeners = this.listeners.filter(listener => !listener.IsThisFunc(obj, t_wonum))
        this.wonum = this.wonum.splice(this.wonum.indexOf(t_wonum),1)
        jobobj = jobobj.splice(jobobj.indexOf(obj),1)
        return true;
    }

    notify(t_wonum, respond, data){
        this.listeners.forEach(listener => listener.DisptachEvent(t_wonum, respond, data));
    }
    updateJobInfo(obj,respond,data) {
        var i = 0;
        var jsonarr = strToJson(data);
        SetJobInfo(jsonarr);
        for(const listener of listeners) {
            if (!(listener instanceof JobNetInfoListener)) {
                listener.notify(new JobInfo(jsonarr[0]))
            }
        }

        if(respond) {
            for(i=0 ; i < jsonarr.length; i++) {
                console.log('-- job wonum ', jsonarr[i].wonum, ' update info')
                obj.notify(jsonarr[i].wonum, jsonarr[i].id, jsonarr[i].state)
            }
        }
    }
    JobInfoChange(obj,respond,data) {
        console.log("-- JobInfoChange");
        var jsonarr = strToJson(data);
        for(const listener of listeners) {
            if (listener instanceof JobNetInfoListener) {
                listener.notify(jsonarr)
            }
        }
    }
}
function SetJobInfo(newJobInfo) {
        var i = 0, j = 0;

        //mfp_jobinfo_list = [];
        for(i; i < newJobInfo.length; i++) {
            console.log('newJobInfo len:',newJobInfo.length, ' i:', i)
                for(j=0;j<jobobj.length;j++) {
                    if(jobobj[j].getWoNum() == newJobInfo[i].wonum) {
                        console.log('jobobj',jobobj[j].getJobType(), ' j:', j)
                            newJobInfo[i].type = jobobj[j].getJobType();
                        break;
                    }
                }
            //if(JSON.stringify(mfp_jobinfo_list[i]) === JSON.stringify(newJobInfo[i]))
            //      console.log('same :',i)
            for (var k = 0; k < mfp_jobinfo_list.length; k++)
            {
                if (mfp_jobinfo_list[k].id == newJobInfo[i].id)
                {
                    mfp_jobinfo_list.splice(k , 1);
                    break;
                }
            }
            console.log('newJobInfo push mfp_jobinfo_list')
                mfp_jobinfo_list.push( new JobInfo(newJobInfo[i]) );
            console.log('newJobInfo push mfp_jobinfo_list OK')
        }

    }
/**
 * 作业监听类
 */
class JobListener {
    /**
     * 构造函数
     */
    constructor() {
        let instance = new globalThis.pedk.jobctl.JobctlManager();
        /*let instance = new globalThis.pedk.ObserverManager();
        instance.addListeners(this, this.updateJobInfo, Module_Main.MSG_MODULE_JOBCTL, Module_Sub.MSG_JOBCTL_SUB_NOTIFY);*/
    }
    /**
     * 作业变化回调函数
     * @param {JobInfo} info 作业信息
     */
    notify(info) {
        /*if(typeof info !== 'array') {
            throw TypeError("Must be only one string parmeter")
        }
       console.log('job list info change!!!');*/;

    }

    updateJobInfo(obj,respond,data) {

        /*var i = 0;
        var jsonarr = strToJson(data);
        SetJobInfo(jsonarr);
        for(const listener of listeners) {
            listener.notify(jsonarr)
        }

        if(respond) {
            for(i=0 ; i < jsonarr.length; i++) {
                console.log('job wonum ', jsonarr[i].wonum, ' update info')
                let instance_job = new JobctlManager()
                instance_job.notify(jsonarr[i].wonum, jsonarr[i].id, jsonarr[i].state)
            }
        }*/
    }
}

class JobNetInfoListener {
    constructor() {
        let instance = new globalThis.pedk.jobctl.JobctlManager();
    }

    notify(info) {
        console.log("++ notify:" + info);
    }
}


 /**
 * 注册作业监听器
 * @param {JobListener} listener 作业监听器回调函数
 * @returns {Boolean}
 * <pre>
 * true：注册成功
 * false：注册失败
 * </pre>
 */
function addJobListener(listener) {
	if (typeof listener !== 'object') {
		throw TypeError("Must be only one JobListener parmeter")
	}
	listeners.push(listener)
	return true

}

/**
 * 取消注册作业监听器
 * @param {JobListener} listener 作业监听器回调函数
 * @returns {Boolean}
 * <pre>
 * true：取消成功
 * false：取消失败
 * </pre>
 */
function removeJobListener(listener) {
	if(typeof listener !== 'object') {
		throw TypeError("Must be only one JobListener parmeter")
	}

	listeners.pop(listener)
	return true
}

function getJobHistoryList() {

    var jsonstr = getJobHistoryListFromDC();
    var jsonarr = [];

    if(jsonstr.length === 0) {
        console.log('getJobHistoryList is Null');
        return jsonarr;
    }
    var jsonarr = strToJson(jsonstr);
    return jsonarr;
}

function getJobLastHistory() {

    var jsonstr = getJobLastHistoryFromDC();
    var jsonarr = [];

    if(jsonstr.length === 0) {
        console.log('getJobLastHistory is Null');
        return jsonarr;
    }
    var jsonarr = strToJson(jsonstr);
    return jsonarr;
}

globalThis.pedk.jobctl.getJobList = getJobList
globalThis.pedk.jobctl.JobInfo = JobInfo
globalThis.pedk.jobctl.cancelJob = cancelJob
globalThis.pedk.jobctl.cancelAllJob = cancelAllJob
globalThis.pedk.jobctl.cancelJobByWoNum = cancelJobByWoNum
globalThis.pedk.jobctl.JobListener = JobListener
globalThis.pedk.jobctl.JobNetInfoListener = JobNetInfoListener
globalThis.pedk.jobctl.JobctlManager = JobctlManager
globalThis.pedk.jobctl.addJobListener = addJobListener
globalThis.pedk.jobctl.removeJobListener = removeJobListener
globalThis.pedk.jobctl.getJobHistoryList = getJobHistoryList
globalThis.pedk.jobctl.getNetJobHistoryList = getNetJobHistoryList;
globalThis.pedk.jobctl.getJobLastHistory = getJobLastHistory;
globalThis.pedk.jobctl.clearJobHistoryList = clearJobHistoryList;


