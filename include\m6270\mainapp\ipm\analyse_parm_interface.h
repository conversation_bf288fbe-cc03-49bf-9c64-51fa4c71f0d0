/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       analyse_parm_interface.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-11-05
 * @version    v1.0
 * @details    interface for the analysing parameter
 */
#ifndef ANALYSE_PARM_INTERFACE_H
#define ANALYSE_PARM_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "memmgr/memmgr.h"
#include "step_core.h"

/**
 * @brief set step param(string)
 * @param[in] pstep current step bject
 * @param[in] name step name
 * @param[in] param step param
 * @param[in] value param value
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
int pi_analyse_parm_set_step_param_for_string(IP_STEP_P pstep, char* name, char* param, char* value);

/**
 * @brief set step param(double)
 * @param[in] IP_STEP_P current step bject
 * @param[in] name step name
 * @param[in] param step param
 * @param[double] value param value
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
int pi_analyse_parm_set_step_param_for_double(IP_STEP_P pstep, char* name, char* param, double value);

/**
 * @brief set step param(int)
 * @param[in] IP_STEP_P current step bject
 * @param[in] name step name
 * @param[in] param step param
 * @param[double] value param value
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
int pi_analyse_parm_set_step_param_for_int(IP_STEP_P pstep, char* name, char* param, int value);

/**
 * @brief get steps value
 * @param[in] parms param str
 * @param[in] parm param
 * @param[in] def_value define value
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
int pi_analyse_parm_get_value_with_default_for_int(char* parms,char *parm,int def_value);

/**
 * @brief get steps value
 * @param[in] parms param str
 * @param[in] parm param
 * @param[in] value define value
 * @param[in] value_len  value length
 * @param[in] def_string define string value
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
void pi_analyse_parm_get_value_with_default_for_string(char* parms,char *parm,
                                            char *value,int value_len,char *def_string);

/**
 * @brief get steps value
 * @param[in] parms param str
 * @param[in] parm param
 * @param[in] def_value define value
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
double pi_analyse_parm_get_value_with_default_for_double(char* parms,char *parm,double def_value);

/**
 * @brief get steps value
 * @param[in] parms param str
 * @param[in] parm param
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
void *pi_analyse_parm_get_value_for_pointer(char* parms,char *parm);

/**
 * @brief check parm existence
 * @param[in] parms param str
 * @param[in] parm param
 * @return int \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR>
 * @date 2021-10-29
 */
int pi_analyse_parm_existence(char* parms,char *parm);


/**
 * @brief get cc value
 * @param[in] parms param str
 * @param[in] parm param
 * @return COLOR_COMPONENT_E \n
 *          … …
 * @retval cc type
 * <AUTHOR>
 * @date 2021-10-29
 */
COLOR_COMPONENT_E pi_analyse_parm_get_cc_value(char* parms,char* parm);


#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* ANALYSE_PARM_INTERFACE_H */

/**
 *@}
 */

