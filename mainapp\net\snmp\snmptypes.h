/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file snmptypes.h
 * @addtogroup net
 * @{
 * @addtogroup snmp
 * <AUTHOR> Xin
 * @date 2023-09-04
 * @brief snmp type defined
 */
#ifndef __SNMPTYPES_H__
#define __SNMPTYPES_H__

enum hrDeviceStatus
{
    hrDeviceStatus_unknown                                      = 1,
    hrDeviceStatus_running                                      = 2,
    hrDeviceStatus_warning                                      = 3,
    hrDeviceStatus_testing                                      = 4,
    hrDeviceStatus_down                                         = 5,
};

enum hrPrinterStatus
{
    hrPrinterStatus_other                                       = 1,
    hrPrinterStatus_unknown                                     = 2,
    hrPrinterStatus_idle                                        = 3,
    hrPrinterStatus_printing                                    = 4,
    hrPrinterStatus_warmup                                      = 5,
};

enum hrPrinterDetectedErrorState
{
    hrPrinterDetectedErrorState_lowPaper                        = 0x0080,
    hrPrinterDetectedErrorState_noPaper                         = 0x0040,
    hrPrinterDetectedErrorState_lowToner                        = 0x0020,
    hrPrinterDetectedErrorState_noToner                         = 0x0010,
    hrPrinterDetectedErrorState_doorOpen                        = 0x0008,
    hrPrinterDetectedErrorState_jammed                          = 0x0004,
    hrPrinterDetectedErrorState_offline                         = 0x0002,
    hrPrinterDetectedErrorState_serviceRequested                = 0x0001,
    hrPrinterDetectedErrorState_inputTrayMissing                = 0x8000,
    hrPrinterDetectedErrorState_outputTrayMissing               = 0x4000,
    hrPrinterDetectedErrorState_markerSupplyMissing             = 0x2000,
    hrPrinterDetectedErrorState_outputNearFull                  = 0x1000,
    hrPrinterDetectedErrorState_outputFull                      = 0x0800,
    hrPrinterDetectedErrorState_inputTrayEmpty                  = 0x0400,
    hrPrinterDetectedErrorState_overduePreventMaint             = 0x0200,
};

enum hrDiskStorageAccess
{
    hrDiskStorageAccess_readWrite                               = 1,
    hrDiskStorageAccess_readOnly                                = 2,
};

enum hrDiskStorageMedia
{
    hrDiskStorageMedia_other                                    = 1,
    hrDiskStorageMedia_unknown                                  = 2,
    hrDiskStorageMedia_hardDisk                                 = 3,
    hrDiskStorageMedia_floppyDisk                               = 4,
    hrDiskStorageMedia_opticalDiskROM                           = 5,
    hrDiskStorageMedia_opticalDiskWORM                          = 6,
    hrDiskStorageMedia_opticalDiskRW                            = 7,
    hrDiskStorageMedia_ramDisk                                  = 8,
};

enum hrGeneralBoolean
{
    hrGeneralBoolean_true                                       = 1,
    hrGeneralBoolean_false                                      = 2,
};

enum prtGeneralReset
{
    prtGeneralReset_notResetting                                = 3,
    prtGeneralReset_powerCycleReset                             = 4,
    prtGeneralReset_resetToNVRAM                                = 5,
    prtGeneralReset_resetToFactoryDefaults                      = 6,
};

enum prtConsoleDisable
{
    prtConsoleDisable_enabled                                   = 3,
    prtConsoleDisable_disabled                                  = 4,
};

enum prtCoverStatus
{
    prtCoverStatus_other                                        = 1,
    prtCoverStatus_coverOpen                                    = 3,
    prtCoverStatus_coverClosed                                  = 4,
    prtCoverStatus_interlockOpen                                = 5,
    prtCoverStatus_interlockClosed                              = 6,
};

enum prtInputType
{
    prtInputType_other                                          = 1,
    prtInputType_unknown                                        = 2,
    prtInputType_sheetFeedAutoRemovableTray                     = 3,
    prtInputType_sheetFeedAutoNonRemovableTray                  = 4,
    prtInputType_sheetFeedManual                                = 5,
    prtInputType_continuousRoll                                 = 6,
    prtInputType_continuousFanFold                              = 7,
};

enum prtOutputType
{
    prtOutputType_other                                         = 1,
    prtOutputType_unknown                                       = 2,
    prtOutputType_removableBin                                  = 3,
    prtOutputType_unRemovableBin                                = 4,
    prtOutputType_continuousRollDevice                          = 5,
    prtOutputType_mailBox                                       = 6,
    prtOutputType_continuousFanFold                             = 7,
};

enum prtOutputStackingOrder
{
    prtOutputStackingOrder_unknown                              = 2,
    prtOutputStackingOrder_firstToLast                          = 3,
    prtOutputStackingOrder_lastToFirst                          = 4,
};

enum prtOutputPageDeliveryOrientation
{
    prtOutputPageDeliveryOrientation_faceUp                     = 3,
    prtOutputPageDeliveryOrientation_faceDown                   = 4,
};

enum prtGeneralUnit
{
    prtGeneralUnit_tenThousandthsOfInches                       = 3,
    prtGeneralUnit_micrometers                                  = 4,
};

enum prtCapacityUnit
{
    prtCapacityUnit_other                                       = 1,
    prtCapacityUnit_unknown                                     = 2,
    prtCapacityUnit_tenThousandthsOfInches                      = 3,
    prtCapacityUnit_micrometers                                 = 4,
    prtCapacityUnit_sheets                                      = 8,
    prtCapacityUnit_feet                                        = 16,
    prtCapacityUnit_meters                                      = 17,
    prtCapacityUnit_items                                       = 18,
    prtCapacityUnit_percent                                     = 19,
};

enum prtPresentOnOff
{
    prtPresentOnOff_other                                       = 1,
    prtPresentOnOff_on                                          = 3,
    prtPresentOnOff_off                                         = 4,
    prtPresentOnOff_notPresent                                  = 5,
};

enum prtMarkerMarkTech
{
    prtMarkerMarkTech_other                                     = 1,
    prtMarkerMarkTech_unknown                                   = 2,
    prtMarkerMarkTech_electrophotographicLED                    = 3,
    prtMarkerMarkTech_electrophotographicLaser                  = 4,
    prtMarkerMarkTech_electrophotographicOther                  = 5,
    prtMarkerMarkTech_impactMovingHeadDotMatrix9pin             = 6,
    prtMarkerMarkTech_impactMovingHeadDotMatrix24pin            = 7,
    prtMarkerMarkTech_impactMovingHeadDotMatrixOther            = 8,
    prtMarkerMarkTech_impactMovingHeadFullyFormed               = 9,
    prtMarkerMarkTech_impactBand                                = 10,
    prtMarkerMarkTech_impactOther                               = 11,
    prtMarkerMarkTech_inkjetAqueous                             = 12,
    prtMarkerMarkTech_inkjetSolid                               = 13,
    prtMarkerMarkTech_inkjetOther                               = 14,
    prtMarkerMarkTech_pen                                       = 15,
    prtMarkerMarkTech_thermalTransfer                           = 16,
    prtMarkerMarkTech_thermalSensitive                          = 17,
    prtMarkerMarkTech_thermalDiffusion                          = 18,
    prtMarkerMarkTech_thermalOther                              = 19,
    prtMarkerMarkTech_electroerosion                            = 20,
    prtMarkerMarkTech_electrostatic                             = 21,
    prtMarkerMarkTech_photographicMicrofiche                    = 22,
    prtMarkerMarkTech_photographicImagesetter                   = 23,
    prtMarkerMarkTech_photographicOther                         = 24,
    prtMarkerMarkTech_ionDeposition                             = 25,
    prtMarkerMarkTech_eBeam                                     = 26,
    prtMarkerMarkTech_typesetter                                = 27,
};

enum prtMarkerCounterUnit
{
    prtMarkerCounterUnit_tenThousandthsOfInches                 = 3,
    prtMarkerCounterUnit_micrometers                            = 4,
    prtMarkerCounterUnit_characters                             = 5,
    prtMarkerCounterUnit_lines                                  = 6,
    prtMarkerCounterUnit_impressions                            = 7,
    prtMarkerCounterUnit_sheets                                 = 8,
    prtMarkerCounterUnit_dotRow                                 = 9,
    prtMarkerCounterUnit_hours                                  = 11,
    prtMarkerCounterUnit_feet                                   = 16,
    prtMarkerCounterUnit_meters                                 = 17,
};

enum prtMarkerColorantRole
{
    prtMarkerColorantRole_other                                 = 1,
    prtMarkerColorantRole_process                               = 3,
    prtMarkerColorantRole_spot                                  = 4,
};

enum prtMediaPathMaxSpeedPrintUnit
{
    prtMediaPathMaxSpeedPrintUnit_tenThousandthsOfInchesPerHour = 3,
    prtMediaPathMaxSpeedPrintUnit_micrometersPerHour            = 4,
    prtMediaPathMaxSpeedPrintUnit_charactersPerHour             = 5,
    prtMediaPathMaxSpeedPrintUnit_linesPerHour                  = 6,
    prtMediaPathMaxSpeedPrintUnit_impressionsPerHour            = 7,
    prtMediaPathMaxSpeedPrintUnit_sheetsPerHour                 = 8,
    prtMediaPathMaxSpeedPrintUnit_dotRowPerHour                 = 9,
    prtMediaPathMaxSpeedPrintUnit_feetPerHour                   = 16,
    prtMediaPathMaxSpeedPrintUnit_metersPerHour                 = 17,
};

enum prtChannelType
{
    prtChannelType_other                                        = 1,
    prtChannelType_chSerialPort                                 = 3,
    prtChannelType_chParallelPort                               = 4,
    prtChannelType_chIEEE1284Port                               = 5,
    prtChannelType_chSCSIPort                                   = 6,
    prtChannelType_chAppleTalkPAP                               = 7,
    prtChannelType_chLPDServer                                  = 8,
    prtChannelType_chNetwareRPrinter                            = 9,
    prtChannelType_chNetwarePServer                             = 10,
    prtChannelType_chPort9100                                   = 11,
    prtChannelType_chAppSocket                                  = 12,
    prtChannelType_chFTP                                        = 13,
    prtChannelType_chTFTP                                       = 14,
    prtChannelType_chDLCLLCPort                                 = 15,
    prtChannelType_chIBM3270                                    = 16,
    prtChannelType_chIBM5250                                    = 17,
    prtChannelType_chFax                                        = 18,
    prtChannelType_chIEEE1394                                   = 19,
    prtChannelType_chTransport1                                 = 20,
    prtChannelType_chCPAP                                       = 21,
    prtChannelType_chDCERemoteProcCall                          = 22,
    prtChannelType_chONCRemoteProcCall                          = 23,
    prtChannelType_chOLE                                        = 24,
    prtChannelType_chNamedPipe                                  = 25,
    prtChannelType_chPCPrint                                    = 26,
    prtChannelType_chServerMessageBlock                         = 27,
    prtChannelType_chDPMF                                       = 28,
    prtChannelType_chDLLAPI                                     = 29,
    prtChannelType_chVxDAPI                                     = 30,
    prtChannelType_chSystemObjectManager                        = 31,
    prtChannelType_chDECLAT                                     = 32,
    prtChannelType_chNPAP                                       = 33,
    prtChannelType_chUSB                                        = 34,
    prtChannelType_chIRDA                                       = 35,
    prtChannelType_chPrintXChange                               = 36,
    prtChannelType_chPortTCP                                    = 37,
    prtChannelType_chBidirPortTCP                               = 38,
    prtChannelType_chUNPP                                       = 39,
    prtChannelType_chAppleTalkADSP                              = 40,
    prtChannelType_chPortSPX                                    = 41,
    prtChannelType_chPortHTTP                                   = 42,
    prtChannelType_chNDPS                                       = 43,
    prtChannelType_chIPP                                        = 44,
    prtChannelType_chSMTP                                       = 45,
};

enum prtInterpreterLangFamily
{
    prtInterpreterLangFamily_other                              = 1,
    prtInterpreterLangFamily_unknown                            = 2,
    prtInterpreterLangFamily_langPCL                            = 3,
    prtInterpreterLangFamily_langHPGL                           = 4,
    prtInterpreterLangFamily_langPJL                            = 5,
    prtInterpreterLangFamily_langPS                             = 6,
    prtInterpreterLangFamily_langIPDS                           = 7,
    prtInterpreterLangFamily_langPPDS                           = 8,
    prtInterpreterLangFamily_langEscapeP                        = 9,
    prtInterpreterLangFamily_langEpson                          = 10,
    prtInterpreterLangFamily_langDDIF                           = 11,
    prtInterpreterLangFamily_langInterpress                     = 12,
    prtInterpreterLangFamily_langISO6429                        = 13,
    prtInterpreterLangFamily_langLineData                       = 14,
    prtInterpreterLangFamily_langMODCA                          = 15,
    prtInterpreterLangFamily_langREGIS                          = 16,
    prtInterpreterLangFamily_langSCS                            = 17,
    prtInterpreterLangFamily_langSPDL                           = 18,
    prtInterpreterLangFamily_langTEK4014                        = 19,
    prtInterpreterLangFamily_langPDS                            = 20,
    prtInterpreterLangFamily_langIGP                            = 21,
    prtInterpreterLangFamily_langCodeV                          = 22,
    prtInterpreterLangFamily_langDSCDSE                         = 23,
    prtInterpreterLangFamily_langWPS                            = 24,
    prtInterpreterLangFamily_langLN03                           = 25,
    prtInterpreterLangFamily_langCCITT                          = 26,
    prtInterpreterLangFamily_langQUIC                           = 27,
    prtInterpreterLangFamily_langCPAP                           = 28,
    prtInterpreterLangFamily_langDecPPL                         = 29,
    prtInterpreterLangFamily_langSimpleText                     = 30,
    prtInterpreterLangFamily_langNPAP                           = 31,
    prtInterpreterLangFamily_langDOC                            = 32,
    prtInterpreterLangFamily_langimPress                        = 33,
    prtInterpreterLangFamily_langPinwriter                      = 34,
    prtInterpreterLangFamily_langNPDL                           = 35,
    prtInterpreterLangFamily_langNEC201PL                       = 36,
    prtInterpreterLangFamily_langAutomatic                      = 37,
    prtInterpreterLangFamily_langPages                          = 38,
    prtInterpreterLangFamily_langLIPS                           = 39,
    prtInterpreterLangFamily_langTIFF                           = 40,
    prtInterpreterLangFamily_langDiagnostic                     = 41,
    prtInterpreterLangFamily_langPSPrinter                      = 42,
    prtInterpreterLangFamily_langCaPSL                          = 43,
    prtInterpreterLangFamily_langEXCL                           = 44,
    prtInterpreterLangFamily_langLCDS                           = 45,
    prtInterpreterLangFamily_langXES                            = 46,
    prtInterpreterLangFamily_langPCLXL                          = 47,
    prtInterpreterLangFamily_langART                            = 48,
    prtInterpreterLangFamily_langTIPSI                          = 49,
    prtInterpreterLangFamily_langPrescribe                      = 50,
    prtInterpreterLangFamily_langLinePrinter                    = 51,
    prtInterpreterLangFamily_langIDP                            = 52,
    prtInterpreterLangFamily_langXJCL                           = 53,
    prtInterpreterLangFamily_langPDF                            = 54,
    prtInterpreterLangFamily_langRPDL                           = 55,
    prtInterpreterLangFamily_langIntermecIPL                    = 56,
    prtInterpreterLangFamily_langUBIFingerprint                 = 57,
    prtInterpreterLangFamily_langUBIDirectProtocol              = 58,
    prtInterpreterLangFamily_langFujitsu                        = 59,
    prtInterpreterLangFamily_langCGM                            = 60,
    prtInterpreterLangFamily_langJPEG                           = 61,
    prtInterpreterLangFamily_langCALS1                          = 62,
    prtInterpreterLangFamily_langCALS2                          = 63,
    prtInterpreterLangFamily_langNIRS                           = 64,
    prtInterpreterLangFamily_langC4                             = 65,
};

enum prtInterpreterDefaultOrientation
{
    prtInterpreterDefaultOrientation_other                      = 1,
    prtInterpreterDefaultOrientation_portrait                   = 3,
    prtInterpreterDefaultOrientation_landscape                  = 4,
};

enum prtInterpreterTwoWay
{
    prtInterpreterTwoWay_yes                                    = 3,
    prtInterpreterTwoWay_no                                     = 4,
};

enum IANACharset
{
    IANACharset_other                                           = 1,
    IANACharset_unknown                                         = 2,
    IANACharset_csASCII                                         = 3,
    IANACharset_csISOLatin1                                     = 4,
    IANACharset_csISOLatin2                                     = 5,
    IANACharset_csISOLatin3                                     = 6,
    IANACharset_csISOLatin4                                     = 7,
    IANACharset_csISOLatinCyrillic                              = 8,
    IANACharset_csISOLatinArabic                                = 9,
    IANACharset_csISOLatinGreek                                 = 10,
    IANACharset_csISOLatinHebrew                                = 11,
    IANACharset_csISOLatin5                                     = 12,
    IANACharset_csISOLatin6                                     = 13,
    IANACharset_csISOTextComm                                   = 14,
    IANACharset_csHalfWidthKatakana                             = 15,
    IANACharset_csJISEncoding                                   = 16,
    IANACharset_csShiftJIS                                      = 17,
    IANACharset_csEUCPkdFmtJapanese                             = 18,
    IANACharset_csEUCFixWidJapanese                             = 19,
    IANACharset_csISO4UnitedKingdom                             = 20,
    IANACharset_csISO11SwedishForNames                          = 21,
    IANACharset_csISO15Italian                                  = 22,
    IANACharset_csISO17Spanish                                  = 23,
    IANACharset_csISO21German                                   = 24,
    IANACharset_csISO60DanishNorwegian                          = 25,
    IANACharset_csISO69French                                   = 26,
    IANACharset_csISO10646UTF1                                  = 27,
    IANACharset_csISO646basic1983                               = 28,
    IANACharset_csINVARIANT                                     = 29,
    IANACharset_csISO2IntlRefVersion                            = 30,
    IANACharset_csNATSSEFI                                      = 31,
    IANACharset_csNATSSEFIADD                                   = 32,
    IANACharset_csNATSDANO                                      = 33,
    IANACharset_csNATSDANOADD                                   = 34,
    IANACharset_csISO10Swedish                                  = 35,
    IANACharset_csKSC56011987                                   = 36,
    IANACharset_csISO2022KR                                     = 37,
    IANACharset_csEUCKR                                         = 38,
    IANACharset_csISO2022JP                                     = 39,
    IANACharset_csISO2022JP2                                    = 40,
    IANACharset_csISO13JISC6220jp                               = 41,
    IANACharset_csISO14JISC6220ro                               = 42,
    IANACharset_csISO16Portuguese                               = 43,
    IANACharset_csISO18Greek7Old                                = 44,
    IANACharset_csISO19LatinGreek                               = 45,
    IANACharset_csISO25French                                   = 46,
    IANACharset_csISO27LatinGreek1                              = 47,
    IANACharset_csISO5427Cyrillic                               = 48,
    IANACharset_csISO42JISC62261978                             = 49,
    IANACharset_csISO47BSViewdata                               = 50,
    IANACharset_csISO49INIS                                     = 51,
    IANACharset_csISO50INIS8                                    = 52,
    IANACharset_csISO51INISCyrillic                             = 53,
    IANACharset_csISO54271981                                   = 54,
    IANACharset_csISO5428Greek                                  = 55,
    IANACharset_csISO57GB1988                                   = 56,
    IANACharset_csISO58GB231280                                 = 57,
    IANACharset_csISO61Norwegian2                               = 58,
    IANACharset_csISO70VideotexSupp1                            = 59,
    IANACharset_csISO84Portuguese2                              = 60,
    IANACharset_csISO85Spanish2                                 = 61,
    IANACharset_csISO86Hungarian                                = 62,
    IANACharset_csISO87JISX0208                                 = 63,
    IANACharset_csISO88Greek7                                   = 64,
    IANACharset_csISO89ASMO449                                  = 65,
    IANACharset_csISO90                                         = 66,
    IANACharset_csISO91JISC62291984a                            = 67,
    IANACharset_csISO92JISC62991984b                            = 68,
    IANACharset_csISO93JIS62291984badd                          = 69,
    IANACharset_csISO94JIS62291984hand                          = 70,
    IANACharset_csISO95JIS62291984handadd                       = 71,
    IANACharset_csISO96JISC62291984kana                         = 72,
    IANACharset_csISO2033                                       = 73,
    IANACharset_csISO99NAPLPS                                   = 74,
    IANACharset_csISO102T617bit                                 = 75,
    IANACharset_csISO103T618bit                                 = 76,
    IANACharset_csISO111ECMACyrillic                            = 77,
    IANACharset_csa71                                           = 78,
    IANACharset_csa72                                           = 79,
    IANACharset_csISO123CSAZ24341985gr                          = 80,
    IANACharset_csISO88596E                                     = 81,
    IANACharset_csISO88596I                                     = 82,
    IANACharset_csISO128T101G2                                  = 83,
    IANACharset_csISO88598E                                     = 84,
    IANACharset_csISO88598I                                     = 85,
    IANACharset_csISO139CSN369103                               = 86,
    IANACharset_csISO141JUSIB1002                               = 87,
    IANACharset_csISO143IECP271                                 = 88,
    IANACharset_csISO146Serbian                                 = 89,
    IANACharset_csISO147Macedonian                              = 90,
    IANACharset_csISO150                                        = 91,
    IANACharset_csISO151Cuba                                    = 92,
    IANACharset_csISO6937Add                                    = 93,
    IANACharset_csISO153GOST1976874                             = 94,
    IANACharset_csISO8859Supp                                   = 95,
    IANACharset_csISO10367Box                                   = 96,
    IANACharset_csISO158Lap                                     = 97,
    IANACharset_csISO159JISX02121990                            = 98,
    IANACharset_csISO646Danish                                  = 99,
    IANACharset_csUSDK                                          = 100,
    IANACharset_csDKUS                                          = 101,
    IANACharset_csKSC5636                                       = 102,
    IANACharset_csUnicode11UTF7                                 = 103,
    IANACharset_csISO2022CN                                     = 104,
    IANACharset_csISO2022CNEXT                                  = 105,
    IANACharset_csUTF8                                          = 106,
    IANACharset_csISO885913                                     = 109,
    IANACharset_csISO885914                                     = 110,
    IANACharset_csISO885915                                     = 111,
    IANACharset_csISO885916                                     = 112,
    IANACharset_csGBK                                           = 113,
    IANACharset_csGB18030                                       = 114,
    IANACharset_csOSDEBCDICDF0415                               = 115,
    IANACharset_csOSDEBCDICDF03IRV                              = 116,
    IANACharset_csOSDEBCDICDF041                                = 117,
    IANACharset_csISO115481                                     = 118,
    IANACharset_csKZ1048                                        = 119,
    IANACharset_csUnicode                                       = 1000,
    IANACharset_csUCS4                                          = 1001,
    IANACharset_csUnicodeASCII                                  = 1002,
    IANACharset_csUnicodeLatin1                                 = 1003,
    IANACharset_csUnicodeJapanese                               = 1004,
    IANACharset_csUnicodeIBM1261                                = 1005,
    IANACharset_csUnicodeIBM1268                                = 1006,
    IANACharset_csUnicodeIBM1276                                = 1007,
    IANACharset_csUnicodeIBM1264                                = 1008,
    IANACharset_csUnicodeIBM1265                                = 1009,
    IANACharset_csUnicode11                                     = 1010,
    IANACharset_csSCSU                                          = 1011,
    IANACharset_csUTF7                                          = 1012,
    IANACharset_csUTF16BE                                       = 1013,
    IANACharset_csUTF16LE                                       = 1014,
    IANACharset_csUTF16                                         = 1015,
    IANACharset_csCESU8                                         = 1016,
    IANACharset_csUTF32                                         = 1017,
    IANACharset_csUTF32BE                                       = 1018,
    IANACharset_csUTF32LE                                       = 1019,
    IANACharset_csBOCU1                                         = 1020,
    IANACharset_csUTF7IMAP                                      = 1021,
    IANACharset_csWindows30Latin1                               = 2000,
    IANACharset_csWindows31Latin1                               = 2001,
    IANACharset_csWindows31Latin2                               = 2002,
    IANACharset_csWindows31Latin5                               = 2003,
    IANACharset_csHPRoman8                                      = 2004,
    IANACharset_csAdobeStandardEncoding                         = 2005,
    IANACharset_csVenturaUS                                     = 2006,
    IANACharset_csVenturaInternational                          = 2007,
    IANACharset_csDECMCS                                        = 2008,
    IANACharset_csPC850Multilingual                             = 2009,
    IANACharset_csPCp852                                        = 2010,
    IANACharset_csPC8CodePage437                                = 2011,
    IANACharset_csPC8DanishNorwegian                            = 2012,
    IANACharset_csPC862LatinHebrew                              = 2013,
    IANACharset_csPC8Turkish                                    = 2014,
    IANACharset_csIBMSymbols                                    = 2015,
    IANACharset_csIBMThai                                       = 2016,
    IANACharset_csHPLegal                                       = 2017,
    IANACharset_csHPPiFont                                      = 2018,
    IANACharset_csHPMath8                                       = 2019,
    IANACharset_csHPPSMath                                      = 2020,
    IANACharset_csHPDesktop                                     = 2021,
    IANACharset_csVenturaMath                                   = 2022,
    IANACharset_csMicrosoftPublishing                           = 2023,
    IANACharset_csWindows31J                                    = 2024,
    IANACharset_csGB2312                                        = 2025,
    IANACharset_csBig5                                          = 2026,
    IANACharset_csMacintosh                                     = 2027,
    IANACharset_csIBM037                                        = 2028,
    IANACharset_csIBM038                                        = 2029,
    IANACharset_csIBM273                                        = 2030,
    IANACharset_csIBM274                                        = 2031,
    IANACharset_csIBM275                                        = 2032,
    IANACharset_csIBM277                                        = 2033,
    IANACharset_csIBM278                                        = 2034,
    IANACharset_csIBM280                                        = 2035,
    IANACharset_csIBM281                                        = 2036,
    IANACharset_csIBM284                                        = 2037,
    IANACharset_csIBM285                                        = 2038,
    IANACharset_csIBM290                                        = 2039,
    IANACharset_csIBM297                                        = 2040,
    IANACharset_csIBM420                                        = 2041,
    IANACharset_csIBM423                                        = 2042,
    IANACharset_csIBM424                                        = 2043,
    IANACharset_csIBM500                                        = 2044,
    IANACharset_csIBM851                                        = 2045,
    IANACharset_csIBM855                                        = 2046,
    IANACharset_csIBM857                                        = 2047,
    IANACharset_csIBM860                                        = 2048,
    IANACharset_csIBM861                                        = 2049,
    IANACharset_csIBM863                                        = 2050,
    IANACharset_csIBM864                                        = 2051,
    IANACharset_csIBM865                                        = 2052,
    IANACharset_csIBM868                                        = 2053,
    IANACharset_csIBM869                                        = 2054,
    IANACharset_csIBM870                                        = 2055,
    IANACharset_csIBM871                                        = 2056,
    IANACharset_csIBM880                                        = 2057,
    IANACharset_csIBM891                                        = 2058,
    IANACharset_csIBM903                                        = 2059,
    IANACharset_csIBBM904                                       = 2060,
    IANACharset_csIBM905                                        = 2061,
    IANACharset_csIBM918                                        = 2062,
    IANACharset_csIBM1026                                       = 2063,
    IANACharset_csIBMEBCDICATDE                                 = 2064,
    IANACharset_csEBCDICATDEA                                   = 2065,
    IANACharset_csEBCDICCAFR                                    = 2066,
    IANACharset_csEBCDICDKNO                                    = 2067,
    IANACharset_csEBCDICDKNOA                                   = 2068,
    IANACharset_csEBCDICFISE                                    = 2069,
    IANACharset_csEBCDICFISEA                                   = 2070,
    IANACharset_csEBCDICFR                                      = 2071,
    IANACharset_csEBCDICIT                                      = 2072,
    IANACharset_csEBCDICPT                                      = 2073,
    IANACharset_csEBCDICES                                      = 2074,
    IANACharset_csEBCDICESA                                     = 2075,
    IANACharset_csEBCDICESS                                     = 2076,
    IANACharset_csEBCDICUK                                      = 2077,
    IANACharset_csEBCDICUS                                      = 2078,
    IANACharset_csUnknown8BiT                                   = 2079,
    IANACharset_csMnemonic                                      = 2080,
    IANACharset_csMnem                                          = 2081,
    IANACharset_csVISCII                                        = 2082,
    IANACharset_csVIQR                                          = 2083,
    IANACharset_csKOI8R                                         = 2084,
    IANACharset_csHZGB2312                                      = 2085,
    IANACharset_csIBM866                                        = 2086,
    IANACharset_csPC775Baltic                                   = 2087,
    IANACharset_csKOI8U                                         = 2088,
    IANACharset_csIBM00858                                      = 2089,
    IANACharset_csIBM00924                                      = 2090,
    IANACharset_csIBM01140                                      = 2091,
    IANACharset_csIBM01141                                      = 2092,
    IANACharset_csIBM01142                                      = 2093,
    IANACharset_csIBM01143                                      = 2094,
    IANACharset_csIBM01144                                      = 2095,
    IANACharset_csIBM01145                                      = 2096,
    IANACharset_csIBM01146                                      = 2097,
    IANACharset_csIBM01147                                      = 2098,
    IANACharset_csIBM01148                                      = 2099,
    IANACharset_csIBM01149                                      = 2100,
    IANACharset_csBig5HKSCS                                     = 2101,
    IANACharset_csIBM1047                                       = 2102,
    IANACharset_csPTCP154                                       = 2103,
    IANACharset_csAmiga1251                                     = 2104,
    IANACharset_csKOI7switched                                  = 2105,
    IANACharset_csBRF                                           = 2106,
    IANACharset_csTSCII                                         = 2107,
    IANACharset_csCP51932                                       = 2108,
    IANACharset_cswindows874                                    = 2109,
    IANACharset_cswindows1250                                   = 2250,
    IANACharset_cswindows1251                                   = 2251,
    IANACharset_cswindows1252                                   = 2252,
    IANACharset_cswindows1253                                   = 2253,
    IANACharset_cswindows1254                                   = 2254,
    IANACharset_cswindows1255                                   = 2255,
    IANACharset_cswindows1256                                   = 2256,
    IANACharset_cswindows1257                                   = 2257,
    IANACharset_cswindows1258                                   = 2258,
    IANACharset_csTIS620                                        = 2259,
    IANACharset_cs50220                                         = 2260,
    IANACharset_reserved                                        = 3000,
};

enum ptAlertSeverityLevel
{
    ptAlertSeverityLevel_default = 0,
    ptAlertSeverityLevel_fatal   = 1,
    ptAlertSeverityLevel_error   = 2,
    ptAlertSeverityLevel_warning = 3,
};

enum ptAlertGroup
{
    ptAlertGroup_default = 0,
    ptAlertGroup_print   = 1,
    ptAlertGroup_scan    = 2,
    ptAlertGroup_fax     = 3,
};

enum ptAlertGroupIndex
{
    ptAlertGroupIndex_default           = 0,
    ptAlertGroupIndex_engine            = 1,
    ptAlertGroupIndex_iCoverOpen        = 2,
    ptAlertGroupIndex_iCartridgeStatus  = 3,
    ptAlertGroupIndex_iPaperEmpty       = 4,
    ptAlertGroupIndex_iPaperJam         = 5,
    ptAlertGroupIndex_iPaperMismatch    = 6,
    ptAlertGroupIndex_iPapersourceMatch = 7,
    ptAlertGroupIndex_iCS               = 8,
    ptAlertGroupIndex_iPaperPutMismatch = 9,
    ptAlertGroupIndex_iWasterToner      = 10,
    ptAlertGroupIndex_other             = 11,
    ptAlertGroupIndex_iTray             = 12,
};

enum ptAlertLocation
{
    ptAlertLocation_default = 0,
    ptAlertLocation_input   = 1,
    ptAlertLocation_middle  = 2,
    ptAlertLocation_output  = 3,
};

#define PT_ALERT_TABLE(out) {                                                                       \
    out( PT_ALERT_I_DEFAULT                           , 0   , ""                                  ) \
    out( PT_ALERT_I_BACK_COVER_OPEN                   , 101 , "COVER OPEN BACK"                   ) \
    out( PT_ALERT_I_RIGHT_COVER_OPEN                  , 102 , "COVER OPEN RIGHT"                  ) \
    out( PT_ALERT_I_FRONT_COVER_OPEN                  , 103 , "COVER OPEN FRONT"                  ) \
    out( PT_ALERT_I_TRAY2_COVER_OPEN                  , 104 , "INPUT OPTION1 IS OPEN"             ) \
    out( PT_ALERT_I_TRAY3_COVER_OPEN                  , 105 , "INPUT OPTION2 IS OPEN"             ) \
    out( PT_ALERT_I_TONER_EMPTY_C                     , 111 , "TONER LIFE END C"                  ) \
    out( PT_ALERT_I_TONER_EMPTY_M                     , 112 , "TONER LIFE END M"                  ) \
    out( PT_ALERT_I_TONER_EMPTY_Y                     , 113 , "TONER LIFE END Y"                  ) \
    out( PT_ALERT_I_TONER_EMPTY_K                     , 114 , "TONER LIFE END K"                  ) \
    out( PT_ALERT_I_TONER_MISSING_C                   , 121 , "TONER NOT INSTALL C"               ) \
    out( PT_ALERT_I_TONER_MISSING_M                   , 122 , "TONER NOT INSTALL M"               ) \
    out( PT_ALERT_I_TONER_MISSING_Y                   , 123 , "TONER NOT INSTALL Y"               ) \
    out( PT_ALERT_I_TONER_MISSING_K                   , 124 , "TONER NOT INSTALL K"               ) \
    out( PT_ALERT_I_TONER_MISMATCH_C                  , 131 , "TONER  MISMATCH C"                 ) \
    out( PT_ALERT_I_TONER_MISMATCH_M                  , 132 , "TONER  MISMATCH M"                 ) \
    out( PT_ALERT_I_TONER_MISMATCH_Y                  , 133 , "TONER  MISMATCH Y"                 ) \
    out( PT_ALERT_I_TONER_MISMATCH_K                  , 134 , "TONER  MISMATCH K"                 ) \
    out( PT_ALERT_I_DRUM_LIFE_END_C                   , 141 , "CARTRIDGE LIFE END C"              ) \
    out( PT_ALERT_I_DRUM_LIFE_END_M                   , 142 , "CARTRIDGE LIFE END M"              ) \
    out( PT_ALERT_I_DRUM_LIFE_END_Y                   , 143 , "CARTRIDGE LIFE END Y"              ) \
    out( PT_ALERT_I_DRUM_LIFE_END_K                   , 144 , "CARTRIDGE LIFE END K"              ) \
    out( PT_ALERT_I_DRUM_MISSING_C                    , 151 , "CARTRIDGE NOT INSTALL C"           ) \
    out( PT_ALERT_I_DRUM_MISSING_M                    , 152 , "CARTRIDGE NOT INSTALL M"           ) \
    out( PT_ALERT_I_DRUM_MISSING_Y                    , 153 , "CARTRIDGE NOT INSTALL Y"           ) \
    out( PT_ALERT_I_DRUM_MISSING_K                    , 154 , "CARTRIDGE NOT INSTALL K"           ) \
    out( PT_ALERT_I_DRUM_MISMATCH_C                   , 161 , "CARTRIDGE MISMATCH C"              ) \
    out( PT_ALERT_I_DRUM_MISMATCH_M                   , 162 , "CARTRIDGE MISMATCH M"              ) \
    out( PT_ALERT_I_DRUM_MISMATCH_Y                   , 163 , "CARTRIDGE MISMATCH Y"              ) \
    out( PT_ALERT_I_DRUM_MISMATCH_K                   , 164 , "CARTRIDGE MISMATCH K"              ) \
    out( PT_ALERT_I_WASTE_TONER_FULL                  , 171 , "WASTE TONER FULL"                  ) \
    out( PT_ALERT_I_WASTE_TONER_MISSING               , 172 , "WASTE TONER MISSING"               ) \
    out( PT_ALERT_I_STANDARD_BIN_FULL                 , 181 , "STANDARD BIN FULL"                 ) \
    out( PT_ALERT_I_TRAY_MISSING_STANDARD_TRAY        , 191 , "MISSING STANDARD TRAY"             ) \
    out( PT_ALERT_I_TRAY_MISSING_OPTIONAL_TRAY_1      , 192 , "MISSING OPTIONAL TRAY 1"           ) \
    out( PT_ALERT_I_TRAY_MISSING_OPTIONAL_TRAY_2      , 193 , "MISSING OPTIONAL TRAY 2"           ) \
    out( PT_ALERT_I_TRAY_EMPTY_STANDARD_TRAY          , 201 , "INPUT TRAY2 EMPTY"                 ) \
    out( PT_ALERT_I_TRAY_EMPTY_MULTI_FUNCTION_TRAY    , 202 , "INPUT TRAY1 EMPTY"                 ) \
    out( PT_ALERT_I_TRAY_EMPTY_OPTIONAL_TRAY_1        , 203 , "INPUT OPTION1 EMPTY"               ) \
    out( PT_ALERT_I_TRAY_EMPTY_OPTIONAL_TRAY_2        , 204 , "INPUT OPTION2 EMPTY"               ) \
    out( PT_ALERT_I_TRAY1_PAPER_SIZE_MISMATCH         , 211 , "INPUT TRAY2 PAPER PUTMISMATCH"     ) \
    out( PT_ALERT_I_MPTRAY_PAPER_SIZE_MISMATCH        , 212 , "INPUT TRAY1 PAPER PUTMISMATCH"     ) \
    out( PT_ALERT_I_TRAY1_PAPER_TYPE_MISMATCH         , 213 , "TRAY1 PAPER TYPE MISMATCH"         ) \
    out( PT_ALERT_I_MPTRAY_PAPER_TYPE_MISMATCH        , 214 , "MPTRAY PAPER TYPE MISMATCH"        ) \
    out( PT_ALERT_I_TRAY_2_PAPER_SIZE_MISMATCH        , 215 , "TRAY 2 PAPER SIZE MISMATCH"        ) \
    out( PT_ALERT_I_TRAY_3_PAPER_SIZE_MISMATCH        , 216 , "TRAY 3 PAPER SIZE MISMATCH"        ) \
    out( PT_ALERT_I_TRAY_2_PAPER_TYPE_MISMATCH        , 217 , "TRAY 2 PAPER TYPE MISMATCH"        ) \
    out( PT_ALERT_I_TRAY_3_PAPER_TYPE_MISMATCH        , 218 , "TRAY 3 PAPER TYPE MISMATCH"        ) \
    out( PT_ALERT_I_ENGINE_NOT_READY                  , 221 , "ENGINE NOT READY"                  ) \
    out( PT_ALERT_I_ENGINE_COMMUNICATION_FAILED       , 222 , "ENGINE COMMUNICATION FAILED"       ) \
    out( PT_ALERT_I_VIDEO_DRIVE                       , 231 , "VIDEO DRIVE"                       ) \
    out( PT_ALERT_I_VIDEO_BANDING                     , 232 , "VIDEO BANDING"                     ) \
    out( PT_ALERT_I_FUSER_MISSING                     , 241 , "FUSER MISSING"                     ) \
    out( PT_ALERT_I_FUSER_LIFE_END                    , 242 , "FUSER LIFE END"                    ) \
    out( PT_ALERT_I_JAM_NOT_FEED_STANDARD_TRAY        , 251 , "JAM NOT FEED STANDARD TRAY"        ) \
    out( PT_ALERT_I_JAM_NOT_FEED_MULTI_FUNCTION_TRAY  , 252 , "JAM NOT FEED MULTI FUNCTION TRAY"  ) \
    out( PT_ALERT_I_JAM_NOT_FEED_DUPLEX_UINT          , 253 , "JAM NOT FEED DUPLEX UINT"          ) \
    out( PT_ALERT_I_JAM_NOT_FEED_OPTIONAL_TRAY_1      , 254 , "JAM NOT FEED OPTIONAL TRAY 1"      ) \
    out( PT_ALERT_I_JAM_NOT_FEED_OPTIONAL_TRAY_2      , 255 , "JAM NOT FEED OPTIONAL TRAY 2"      ) \
    out( PT_ALERT_I_JAM_UNATTAIN_CALIBRATE_SENSOR     , 256 , "JAM UNATTAIN CALIBRATE SENSOR"     ) \
    out( PT_ALERT_I_JAM_UNATTAIN_OUTPUT_SENSOR        , 257 , "JAM UNATTAIN OUTPUT SENSOR"        ) \
    out( PT_ALERT_I_JAM_UNATTAIN_OPTIONAL_TRAY_1      , 258 , "JAM UNATTAIN OPTIONAL TRAY 1"      ) \
    out( PT_ALERT_I_JAM_UNATTAIN_OPTIONAL_TRAY_2      , 259 , "JAM UNATTAIN OPTIONAL TRAY 2"      ) \
    out( PT_ALERT_I_JAM_UNATTAIN_FUSER                , 260 , "JAM UNATTAIN FUSER"                ) \
    out( PT_ALERT_I_JAM_UNATTAIN_DUPLEX_INPUT         , 261 , "JAM UNATTAIN DUPLEX INPUT"         ) \
    out( PT_ALERT_I_JAM_STRANDED_CALIBRATE_SENSOR     , 262 , "JAM STRANDED CALIBRATE SENSOR"     ) \
    out( PT_ALERT_I_JAM_STRANDED_OUTPUT_SENSOR        , 263 , "JAM STRANDED OUTPUT SENSOR"        ) \
    out( PT_ALERT_I_JAM_STRANDED_OPTIONAL_TRAY_1      , 264 , "JAM STRANDED OPTIONAL TRAY 1"      ) \
    out( PT_ALERT_I_JAM_STRANDED_OPTIONAL_TRAY_2      , 265 , "JAM STRANDED OPTIONAL TRAY 2"      ) \
    out( PT_ALERT_I_JAM_STRANDED_FUSER                , 266 , "PRINT JAM STRANDED FUSER"          ) \
    out( PT_ALERT_I_JAM_STRANDED_DUPLEX_INPUT         , 267 , "JAM STRANDED DUPLEX INPUT"         ) \
    out( PT_ALERT_I_JAM_RESIDUAL_FUSER                , 268 , "JAM RESIDUAL FUSER"                ) \
    out( PT_ALERT_I_JAM_RESIDUAL_DUPLEX_INPUT         , 269 , "JAM RESIDUAL DUPLEX INPUT"         ) \
    out( PT_ALERT_I_JAM_RESIDUAL_OUTPUT_SENSOR        , 270 , "JAM RESIDUAL OUTPUT SENSOR"        ) \
    out( PT_ALERT_I_JAM_RESIDUAL_CALIBRATE_SENSOR     , 271 , "JAM RESIDUAL CALIBRATE SENSOR"     ) \
    out( PT_ALERT_I_JAM_RESIDUAL_OPTIONAL_TRAY_2      , 272 , "JAM RESIDUAL OPTIONAL TRAY 2"      ) \
    out( PT_ALERT_I_JAM_RESIDUAL_OPTIONAL_TRAY_1      , 273 , "JAM RESIDUAL OPTIONAL TRAY 1"      ) \
    out( PT_ALERT_I_JAM_RESIDUAL_MULTI_FUNCTION_TRAY  , 274 , "JAM RESIDUAL MULTI FUNCTION TRAY"  ) \
    out( PT_ALERT_I_JAM_RESIDUAL_STANDARD_TRAY        , 275 , "JAM RESIDUAL STANDARD TRAY"        ) \
    out( PT_ALERT_I_TIMEPRINT_LIST_FULL               , 276 , "TIMEPRINT LIST FULL"               ) \
    out( PT_ALERT_I_TRAY_PAPER_SIZE_ERROR             , 277 , "TRAY PAPER SIZE ERROR"             ) \
    out( PT_ALERT_I_DRUM_CARRIER_EMPTY_C              , 281 , "DRUM CARRIER EMPTY C"              ) \
    out( PT_ALERT_I_DRUM_CARRIER_EMPTY_M              , 282 , "DRUM CARRIER EMPTY M"              ) \
    out( PT_ALERT_I_DRUM_CARRIER_EMPTY_Y              , 283 , "DRUM CARRIER EMPTY Y"              ) \
    out( PT_ALERT_I_DRUM_CARRIER_EMPTY_K              , 284 , "DRUM CARRIER EMPTY K"              ) \
    out( PT_ALERT_I_REALAY_ROLLER_LIFE_END            , 285 , "REALAY ROLLER LIFE END"            ) \
    out( PT_ALERT_I_ITU_NEAR_LIFE_END                 , 286 , "ITU NEAR LIFE END"                 ) \
    out( PT_ALERT_I_ILLEGAL_PAGE                      , 291 , "ILLEGAL PAGE"                      ) \
    out( PT_ALERT_I_LSU_FAN_ERROR                     , 301 , "LSU FAN ERROR"                     ) \
    out( PT_ALERT_I_FUSER_EXHAUST_FAN_ERROR           , 302 , "FUSER EXHAUST FAN ERROR"           ) \
    out( PT_ALERT_I_LSU_FAN_ERROR_2                   , 303 , "LSU FAN ERROR 2"                   ) \
    out( PT_ALERT_I_MAIN_FAN_ERROR                    , 304 , "MAIN FAN ERROR"                    ) \
    out( PT_ALERT_I_FUSER_TEMP_RAISED_SLOW            , 305 , "FUSER TEMP RAISED SLOW"            ) \
    out( PT_ALERT_I_FUSER_THERMISTOR_ERROR            , 311 , "FUSER THERMISTOR ERROR"            ) \
    out( PT_ALERT_I_FUSER_OVER_HEAT                   , 312 , "FUSER OVER HEAT"                   ) \
    out( PT_ALERT_I_FUSER_HW_OVER_HEAT                , 313 , "FUSER HW OVER HEAT"                ) \
    out( PT_ALERT_I_FUSER_THERMISTOR_ERROR_3          , 314 , "FUSER THERMISTOR ERROR 3"          ) \
    out( PT_ALERT_I_FUSER_OVER_HEAT_3                 , 315 , "FUSER OVER HEAT 3"                 ) \
    out( PT_ALERT_I_FUSER_HW_OVER_HEAT_3              , 316 , "FUSER HW OVER HEAT 3"              ) \
    out( PT_ALERT_I_FUSER_TEMPERATURE_LOW_1           , 317 , "TEMPERATURE LOW 1"                 ) \
    out( PT_ALERT_I_FUSER_TEMPERATURE_LOW_2           , 318 , "TEMPERATURE LOW 2"                 ) \
    out( PT_ALERT_I_FUSER_TEMPERATURE_LOW_3           , 319 , "TEMPERATURE LOW 3"                 ) \
    out( PT_ALERT_I_THERMISTOR_DAMAGE_1               , 321 , "THERMISTOR DAMAGE 1"               ) \
    out( PT_ALERT_I_THERMISTOR_DAMAGE_2               , 322 , "THERMISTOR DAMAGE 2"               ) \
    out( PT_ALERT_I_THERMISTOR_DAMAGE_3               , 323 , "THERMISTOR DAMAGE 3"               ) \
    out( PT_ALERT_I_FUSER_MOTOR_ERROR                 , 331 , "FUSER MOTOR ERROR"                 ) \
    out( PT_ALERT_I_COLOR_DEVELOP_MOTOR_ERROR         , 332 , "COLOR DEVELOP MOTOR ERROR"         ) \
    out( PT_ALERT_I_BLACKDEVELOP_MOTOR_ERROR          , 333 , "BLACKDEVELOP MOTOR ERROR"          ) \
    out( PT_ALERT_I_MULTI_PRISM_MOTOR_ON_TIMEOUT      , 334 , "MULTI PRISM MOTOR ON TIMEOUT"      ) \
    out( PT_ALERT_I_MULTI_PRISM_MOTOR_OFF_TIMEOUT     , 335 , "MULTI PRISM MOTOR OFF TIMEOUT"     ) \
    out( PT_ALERT_I_MULTI_PRISM_MOTOR_SIGNAL_ERROR    , 336 , "MULTI PRISM MOTOR SIGNAL ERROR"    ) \
    out( PT_ALERT_I_LD0_COCURRENT_K_ERROR             , 341 , "LD0 COCURRENT K ERROR"             ) \
    out( PT_ALERT_I_LD0_COCURRENT_Y_ERROR             , 342 , "LD0 COCURRENT Y ERROR"             ) \
    out( PT_ALERT_I_MCU_COMMUNICATION_ERROR           , 351 , "MCU COMMUNICATION ERROR"           ) \
    out( PT_ALERT_I_TRAY_COMMUNICATION_ERROR          , 352 , "TRAY COMMUNICATION ERROR"          ) \
    out( PT_ALERT_I_EEPROM_IIC_COMMUNICATION_ERROR    , 353 , "EEPROM IIC COMMUNICATION ERROR"    ) \
    out( PT_ALERT_I_HUMITURE_IIC_COMMUNICATION_ERROR  , 354 , "HUMITURE IIC COMMUNICATION ERROR"  ) \
    out( PT_ALERT_I_CTL_VIDEO_OK_NOTIFY_ERROR         , 361 , "CTL VIDEO OK NOTIFY ERROR"         ) \
    out( PT_ALERT_I_EC_WARMUP_CANNOT_STOP             , 371 , "EC WARMUP CANNOT STOP"             ) \
    out( PT_ALERT_I_EC_PAGE_BUFFER_FULL               , 372 , "EC PAGE BUFFER FULL"               ) \
    out( PT_ALERT_I_EC_PAGE_CANNOT_START              , 373 , "EC PAGE CANNOT START"              ) \
    out( PT_ALERT_I_EC_PAGE_CANNOT_STOP               , 374 , "EC PAGE CANNOT STOP"               ) \
    out( PT_ALERT_I_EC_PRINT_CANNOT_STOP              , 375 , "EC PRINT CANNOT STOP"              ) \
    out( PT_ALERT_I_ENGINE_PRARAM_FILE                , 376 , "ENGINE PRARAM FILE"                ) \
    out( PT_ALERT_I_FACTORY_FILE                      , 377 , "FACTORY FILE"                      ) \
    out( PT_ALERT_I_CALIBRATION_PARAM                 , 378 , "CALIBRATION PARAM"                 ) \
    out( PT_ALERT_I_TEMPERATURE_UNSTABLE_1            , 381 , "TEMPERATURE UNSTABLE 1"            ) \
    out( PT_ALERT_I_FUSER_THERMISTOR_ERROR_2          , 391 , "FUSER THERMISTOR ERROR 2"          ) \
    out( PT_ALERT_I_FUSER_OVER_HEAT_2                 , 392 , "FUSER OVER HEAT 2"                 ) \
    out( PT_ALERT_I_FUSER_THERMISTOR_DAMAGE_2         , 393 , "FUSER THERMISTOR DAMAGE 2"         ) \
    out( PT_ALERT_I_FUSER_PRESSURE_DECOMPRESS_TIMEOUT , 394 , "FUSER PRESSURE DECOMPRESS TIMEOUT" ) \
    out( PT_ALERT_I_CHARGEING_OUTPUT_ERROR_K          , 401 , "CHARGEING OUTPUT ERROR K"          ) \
    out( PT_ALERT_I_CHARGEING_OUTPUT_ERROR_M          , 402 , "CHARGEING OUTPUT ERROR M"          ) \
    out( PT_ALERT_I_CHARGEING_OUTPUT_ERROR_Y          , 403 , "CHARGEING OUTPUT ERROR Y"          ) \
    out( PT_ALERT_I_CHARGEING_OUTPUT_ERROR_C          , 404 , "CHARGEING OUTPUT ERROR C"          ) \
    out( PT_ALERT_I_DEVELOP_HIGH_PRESSURE_ERROR_K     , 411 , "DEVELOP HIGH PRESSURE ERROR K"     ) \
    out( PT_ALERT_I_DEVELOP_HIGH_PRESSURE_ERROR_M     , 412 , "DEVELOP HIGH PRESSURE ERROR M"     ) \
    out( PT_ALERT_I_DEVELOP_HIGH_PRESSURE_ERROR_Y     , 413 , "DEVELOP HIGH PRESSURE ERROR Y"     ) \
    out( PT_ALERT_I_DEVELOP_HIGH_PRESSURE_ERROR_C     , 414 , "DEVELOP HIGH PRESSURE ERROR C"     ) \
    out( PT_ALERT_I_TONER_SUPPLY_MOTOR_ERROR          , 421 , "TONER SUPPLY MOTOR ERROR"          ) \
    out( PT_ALERT_I_BK_PHOTORECEPTOR_MOTOR_ERROR      , 422 , "BK PHOTORECEPTOR MOTOR ERROR"      ) \
    out( PT_ALERT_I_COLOR_PHOTORECEPTRO_MOTOR_ERROR   , 423 , "COLOR PHOTORECEPTRO MOTOR ERROR"   ) \
    out( PT_ALERT_I_1TB_COMPRESS_MOTOR_ERROR          , 424 , "1TB COMPRESS MOTOR ERROR"          ) \
    out( PT_ALERT_I_NEW_DRUM_EXCHANGE_ERROR           , 425 , "NEW DRUM EXCHANGE ERROR"           ) \
    out( PT_ALERT_I_TC_SENSE_UPPER_LIMIT_ERROR_K      , 431 , "TC SENSE UPPER LIMIT ERROR K"      ) \
    out( PT_ALERT_I_TC_SENSE_UPPER_LIMIT_ERROR_M      , 432 , "TC SENSE UPPER LIMIT ERROR M"      ) \
    out( PT_ALERT_I_TC_SENSE_UPPER_LIMIT_ERROR_C      , 433 , "TC SENSE UPPER LIMIT ERROR C"      ) \
    out( PT_ALERT_I_TC_SENSE_UPPER_LIMIT_ERROR_Y      , 434 , "TC SENSE UPPER LIMIT ERROR Y"      ) \
    out( PT_ALERT_I_TC_SENSE_LOWER_LIMIT_ERROR_K      , 435 , "TC SENSE LOWER LIMIT ERROR K"      ) \
    out( PT_ALERT_I_TC_SENSE_LOWER_LIMIT_ERROR_M      , 436 , "TC SENSE LOWER LIMIT ERROR M"      ) \
    out( PT_ALERT_I_TC_SENSE_LOWER_LIMIT_ERROR_C      , 437 , "TC SENSE LOWER LIMIT ERROR C"      ) \
    out( PT_ALERT_I_TC_SENSE_LOWER_LIMIT_ERROR_Y      , 438 , "TC SENSE LOWER LIMIT ERROR Y"      ) \
    out( PT_ALERT_I_TC_SENSE_INIT_ADJUST_ERROR_K      , 439 , "TC SENSE INIT ADJUST ERROR K"      ) \
    out( PT_ALERT_I_TC_SENSE_INIT_ADJUST_ERROR_M      , 440 , "TC SENSE INIT ADJUST ERROR M"      ) \
    out( PT_ALERT_I_TC_SENSE_INIT_ADJUST_ERROR_C      , 441 , "TC SENSE INIT ADJUST ERROR C"      ) \
    out( PT_ALERT_I_TC_SENSE_INIT_ADJUST_ERROR_Y      , 442 , "TC SENSE INIT ADJUST ERROR Y"      ) \
    out( PT_ALERT_I_TC_SENSE_SAMPLING_ERROR_K         , 443 , "TC SENSE SAMPLING ERROR K"         ) \
    out( PT_ALERT_I_TC_SENSE_SAMPLING_ERROR_M         , 444 , "TC SENSE SAMPLING ERROR M"         ) \
    out( PT_ALERT_I_TC_SENSE_SAMPLING_ERROR_C         , 445 , "TC SENSE SAMPLING ERROR C"         ) \
    out( PT_ALERT_I_TC_SENSE_SAMPLING_ERROR_Y         , 446 , "TC SENSE SAMPLING ERROR Y"         ) \
    out( PT_ALERT_I_TRANS_HIGH_VOLTAGE_ERROR_1        , 451 , "TRANS HIGH VOLTAGE ERROR 1"        ) \
    out( PT_ALERT_I_TRANS_MOTOR_ERROR_1               , 452 , "TRANS MOTOR ERROR 1"               ) \
    out( PT_ALERT_I_TRANS_HIGH_VOLTAGE_ERROR_2        , 453 , "TRANS HIGH VOLTAGE ERROR 2"        ) \
    out( PT_ALERT_I_TRANS_SEPARATION_MOTOR_ERROR_2    , 454 , "TRANS SEPARATION MOTOR ERROR 2"    ) \
    out( PT_ALERT_I_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2  , 455 , "TRANS HIGH VOL CON VOL OUT ERR 2"  ) \
    out( PT_ALERT_I_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2  , 456 , "TRANS HIGH VOL CON CUR OUT ERR 2"  ) \
    out( PT_ALERT_I_TRANS_SEPARATION_HIGH_VOL_ERROR   , 457 , "TRANS SEPARATION HIGH VOL ERROR"   ) \
    out( PT_ALERT_I_REMOVE_LAMP_ERROR                 , 458 , "REMOVE LAMP ERROR"                 ) \
    out( PT_ALERT_I_HIGH_VOL_PLATE_INSERT_ERROR       , 459 , "HIGH VOL PLATE INSERT ERROR"       ) \
    out( PT_ALERT_I_HIGH_VOL_DETECTION_ERROR          , 460 , "HIGH VOL DETECTION ERROR"          ) \
    out( PT_ALERT_I_LSU_CABLE_NOT_CONNECTED           , 461 , "LSU CABLE NOT CONNECTED"           ) \
    out( PT_ALERT_I_DEVELOP_CO_HIGH_ERROR_K           , 462 , "DEVELOP CO HIGH ERROR K"           ) \
    out( PT_ALERT_I_CO_SENSOR_VOL_LOWER_LIMIT_K       , 471 , "CO SENSOR VOL LOWER LIMIT K"       ) \
    out( PT_ALERT_I_CO_SENSOR_VOL_UPPER_LIMIT_K       , 472 , "CO SENSOR VOL UPPER LIMIT K"       ) \
    out( PT_ALERT_I_CO_SENSOR_DETECT_ERROR_K          , 473 , "CO SENSOR DETECT ERROR K"          ) \
    out( PT_ALERT_I_TONER_SUPPLY_UPPER_TEN_ERROR_K    , 481 , "TONER SUPPLY UPPER TEN ERROR K"    ) \
    out( PT_ALERT_I_TONER_SUPPLY_LOWER_TEN_ERROR_K    , 482 , "TONER SUPPLY LOWER TEN ERROR K"    ) \
    out( PT_ALERT_I_TONER_SUPPLY_LOWER_FIVE_ERROR_K   , 483 , "TONER SUPPLY LOWER FIVE ERROR K"   ) \
    out( PT_ALERT_I_ENV_TEMP_BOTH_ERROR               , 491 , "ENV TEMP BOTH ERROR"               ) \
    out( PT_ALERT_I_LD0_COCURRENT_K_UNSTABLE          , 501 , "LD0 COCURRENT K UNSTABLE"          ) \
    out( PT_ALERT_I_LD0_COCURRENT_Y_UNSTABLE          , 502 , "LD0 COCURRENT Y UNSTABLE"          ) \
    out( PT_ALERT_I_EC_CALIBRATION_CANNOT_STOP        , 511 , "EC CALIBRATION CANNOT STOP"        ) \
}

extern const char* s_snmp_language_alias[];
extern uint32_t s_snmp_language_alias_count;

extern const char* s_snmp_country_alias[];
extern uint32_t s_snmp_country_alias_count;


static inline const char* snmp_language_alias(uint32_t language)
{
    RETURN_VAL_IF(language == 0 || language > s_snmp_language_alias_count, NET_WARN, "zh");

    return s_snmp_language_alias[language - 1];
}

static inline const char* snmp_country_alias(uint32_t country)
{
    RETURN_VAL_IF(country == 0 || country > s_snmp_country_alias_count, NET_WARN, "CN");

    return s_snmp_country_alias[country - 1];
}

#endif /* __SNMPTYPES_H__ */
/**
 *@}
 */
