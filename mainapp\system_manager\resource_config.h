/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file resource_config.h
 * @addtogroup system_manager
 * @{
 * @brief resource config 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef _RESOURCE_CONFIG_H
#define _RESOURCE_CONFIG_H

#include "channels.h"
#include "pol/pol_types.h"

enum RESOURCE_INDEX
{
    JOB = 0,
    RESOURCE = 1
};

struct job_item
{
    uint8_t *name;
    int32_t job_type;
    uint32_t job_class;
    uint32_t image_memory_required;
    uint32_t image_memory_lowest;
    uint32_t image_memory_fragment;
    uint32_t video_memory_required;
    uint32_t video_memory_lowest;
    uint32_t video_memory_fragment;
    uint32_t resource_io_id;
    uint32_t start_module_id;
    int32_t mono_type;
    int32_t wait_type;
    int32_t default_priority;
    int32_t multiple_job_depend_ssd;
    int32_t list_num;
    struct channels depend_list[2];
    struct channels msgrouter_source;
};

struct resource_item
{
    uint8_t *name;
    uint32_t id;
    int32_t number;
    int32_t reuse;
};

struct resource_config
{
    struct channels config[2]; // 0:job , 1:resource
    const char *file;
};

/**
 * @brief resource configuration item initialization 
 *
 * @param file configuration file path
 *
 * @return valid address on success , NULL on error 
 */
struct resource_config* resource_init(const char *file);

/**
 * @brief resource configuration item destroy 
 *
 * @param rec resource configuration pointer
 */
void resource_destroy(struct resource_config *rec);

/**
 * @brief print configuration item
 *
 * @param rec resource configuration pointer
 */
void resource_show(struct resource_config *rec);


#endif //_RESOURCE_CONFIG_H

/**
 * @}
 */
