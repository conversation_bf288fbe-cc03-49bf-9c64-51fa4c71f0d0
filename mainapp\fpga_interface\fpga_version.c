#include "fpga_interface.h"

#include <stdint.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <linux/types.h>
#include <linux/spi/spidev.h>
#include <string.h>
#include <stdio.h>

#include "pol/pol_threads.h"
#include "pol/pol_log.h"

#define LOG_LEVEL   5

static char fpga_version_front[FPGA_VERSION_STRING_LEN];
static char fpga_version_back[FPGA_VERSION_STRING_LEN];

void update_fpga_version(int tag, uint32_t version)
{
    char *ptr = NULL;

    if (tag == 0)
        ptr = fpga_version_front;
    else if (tag == 1)
        ptr = fpga_version_back;
    else
    {
        pi_log_e("invalid tag %d\n", tag);
    }

    if (version == 0 || version == 0xffffffff)
    {
        snprintf(ptr, FPGA_VERSION_STRING_LEN, "Unknown");
    }
    else
    {
        snprintf(ptr, FPGA_VERSION_STRING_LEN, "%u.%u",
                version >> 16, version & 0xffff);
    }

    pi_log_i("fpga[%d] version 0x%.8x %s\n", tag, version, ptr);
}

char *get_front_fpga_version()
{
    pi_log_i("%s\n", fpga_version_front);
    return fpga_version_front;
}

char *get_back_fpga_version()
{
    pi_log_i("%s\n", fpga_version_back);
    return fpga_version_back;
}

