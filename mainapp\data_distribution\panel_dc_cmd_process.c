/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
  @file panel_dc_cmd_process.c
 * @addtogroup panel_dc
 * @{
 * @brief process cmd receive from panel
 * <AUTHOR>
 * @version 1.0
 * @date 2023-06-3
 */


#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "qio/qio_general.h"
#include "public/msgrouter_main.h"
#include "public_data_proc.h"
#include "job_manager.h"
#include "pol/pol_log.h"
#include "platform_api.h"
#include "panel_event.h"
#include "pol/pol_string.h"
#include "event_manager/event_mgr.h"
#include "platform_api.h"
#include "nvram.h"
#include "moduleid.h"
#include "event_manager/event_msg_typedef.h"
#include "panel_public.h"
#include "panel_config.h"
#include "panel_maintenance_mode.h"

#include "scan_event.h"
#include "hal_storage.h"
#include "panel_address_book.h"
#include "panel_udisk_print.h"
#include "pedk_mgr.h"
#include "../pedk_manager/app_manager/app_manager.h"
#include "panel_overlay_copy.h"
#include "security_register.h"
#include "panel_file.h"

#ifdef CONFIG_NET_PLOG
#include "plog/plog.h"
#endif

#define PATH_SIZE           64

/**
 * @brief The struct of sample copy info
 */
typedef struct
{
    uint32_t        job_id;             ///< job id
    uint32_t        copies;             ///< copies
}SAMPLE_COPY_JOB_INFO_S ;


/**
 * @brief The struct of sample copy info
 */
typedef struct
{
    uint32_t        result;             ///< 1 for success,0 for error
    uint64_t        total;              ///< storage total space
    uint64_t        remain;             ///< storage free space
    uint64_t        sys_used;           ///< system used
}STORAGE_REMAIN_INFO_S ;

/**
 * @brief This is the data info structure of pin code id
 */
typedef struct
{
    uint32_t job_id;    ///< job id
    char save_path[PATH_SIZE]; ///< save path
}PANEL_REQUEST_PINCODE_S;

/**
 * @brief This is the data info structure of pin code id
 */
typedef struct
{
    uint32_t job_id;    ///< job id
    char save_path[PATH_SIZE]; ///< save path
}PANEL_REQUEST_DELAY_PRINT_S;

/**
 * @brief This is the data info structure of sample print
 */
typedef struct
{
    uint32_t job_id;    ///< job id
    PRINT_SAMPLE_S params; ///< params
}PANEL_REQUEST_SAMPLE_PRINT_S;

/**
 * @brief panel reuqest to uninstall storage mount point
 * @author: madechang
 */
uint32_t panel_request_uninstall_storage( void* data, uint32_t data_len )
{
    int ret = 0;
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();

    ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_UNINSTALL_REQUEST, data, data_len);

    return ret;
}

/**
 * @brief panel reuqest to repaire storage mount point
 * @author: madechang
 */
uint32_t panel_request_repaire_storage( void* data, uint32_t data_len )
{
    int ret = 0;
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();

    ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_REPAIR_REQUEST, data, data_len);

    return ret;

}

/**
 * @brief panel reuqest to repaire storage mount point
 * @author: madechang
 */
uint32_t panel_request_format_storage( void* data, uint32_t data_len )
{
    int ret = 0;
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();

    ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_FORMAT_REQUEST, data, data_len);

    return ret;
}

/**
 * @brief panel reuqest to repaire storage mount point
 * @author: madechang
 */
uint32_t panel_request_clear_storage_error( void* data, uint32_t data_len )
{
    //char mount_point[128] = { 0 };
    //int32_t ret = 0;
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();

    if( NULL == data || 0 == data_len )
    {
        pi_log_e("get panel storage uninstall data error\n");
        return FALSE;
    }

    //ret = pi_hal_storage_get_mount_point( data, mount_point, sizeof(mount_point) );
    //if( ret != 0 )
    //{
    //    pi_log_e("get storage mount point error\n");
    //}

    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_CLEAR_ERR_REQUEST, data, data_len);
    return TRUE;
}

/**
 * @brief panel reuqest to repaire storage mount point
 * @author: madechang
 */
uint32_t panel_request_SATASSD1_CLEAR_ALL_DATA( void* data, uint32_t data_len )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();

    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_CLEAR_ALL_DATA, data, data_len);
    return TRUE;
}


/**
 * @brief panel reuqest exec scan miantenance
 * @author: madechang
 */
void panel_request_scan_maintenance_mode( uint16_t panel_dc_cmd, void* data, uint32_t data_len )
{
    EVT_MGR_CLI_S* panel_event_client = NULL;

    panel_event_client = get_panel_event_client();

    switch ( panel_dc_cmd )
    {

    case SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SENSOR:
        if( data_len != sizeof(SCAN_FB_ADF_SENSOR_S) || !data )
        {
            pi_log_d("recv panel scan fb/adf sensor data error\n");
            return;
        }
        pi_event_mgr_notify(panel_event_client, EVT_TYPE_SCAN_FB_ADF_SENSOR_REQUEST, data, data_len);
        break;

    case SETTING_CMD_MAINTENANCE_SCAN_ADF_ENGINE_PARAM_SET:
        if( data_len != sizeof(SCAN_ADF_ENGINE_PARAM_SET_S) || !data )
        {
            pi_log_d("recv panel scan adf engine data error\n");
            return;
        }
        pi_event_mgr_notify(panel_event_client, EVT_TYPE_SCAN_ADF_ENGINE_PARAM_SET_REQUEST, data, data_len);
        break;

    case SETTING_CMD_MAINTENANCE_SCAN_FB_ENGINE_PARAM_SET:
        if( data_len != sizeof(SCAN_FB_ENGINE_PARAM_SET_S) || !data )
        {
            pi_log_d("recv panel scan fb engine data error\n");
            return;
        }
        pi_event_mgr_notify(panel_event_client, EVT_TYPE_SCAN_FB_ENGINE_PARAM_SET_REQUEST, data, data_len);

        break;
    case SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SCANING_TEST:
        if( data_len != sizeof(SCAN_FB_ADF_SCANING_S) || !data )
        {
            pi_log_d("recv panel scan fb/adf scanning data error\n");
            return;
        }
        pi_event_mgr_notify(panel_event_client, EVT_TYPE_SCAN_FB_ADF_SCANING_REQUEST, data, data_len);
        break;

    case SETTING_CMD_MAINTENANCE_SCAN_ENGINE_ERROR_CLEAR:
        if( data_len != sizeof(SCAN_ENGINE_ERROR_CLEAR_S) || !data )
        {
            pi_log_d("recv panel scan engine error clear data error\n");
            return;
        }
        pi_event_mgr_notify(panel_event_client, EVT_TYPE_SCAN_ENGINE_ERROR_CLEAR_REQUEST, data, data_len);
        break;

    default:
        break;

    }
}

/**
 * @brief panel reuqest to restore factory default
 * @author: madechang
 */
uint32_t panel_request_restore_factory( void )
{
    int32_t ret = 0;
    uint32_t restore_flag = 1;
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();
    if( NULL == panel_evt_client )
    {
        pi_log_d("panel client is null\n");
    }
    ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST, &restore_flag, sizeof(restore_flag));
    if( ret < 0 )
    {
        pi_log_d("notify error\n");
    }

    return 0;
}

/**
 * @brief panel reuqest clear paper size error and continue print
 * @author: madechang
 */
uint32_t panel_request_clear_print_errors( uint32_t data )
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_CLEAR_ERRORS;
    send_msg.msg1        = data;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_PRINT_IF, &send_msg );

    return 0;
}

/**
 * @brief panel reuqest start upgrade
 * @author: madechang
 */
uint32_t panel_request_start_upgrade( uint32_t data )
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_FW_UPGRATE_CONFIRM;
    send_msg.msg1        = data;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_UPGRADE, &send_msg );

    return 0;
}

/**
 * @brief panel reuqest to scan next page
 * @author: madechang
 */
uint32_t panel_request_scan_next_page(uint32_t job_id)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_PAGE_NEXT;
    send_msg.msg1        = SYS_REQUEST;
    send_msg.msg2        = job_id;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_SCAN_JOB_MGR, &send_msg );

    return 0;
}

/**
 * @brief panel reuqest to end scan
 * @author: madechang
 */
uint32_t panel_request_scan_done( uint32_t job_id )
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_JOB_DONE;
    send_msg.msg1        = SYS_REQUEST;
    send_msg.msg2        = job_id;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_SCAN_JOB_MGR, &send_msg );

    return 0;
}

/**
 * @brief panel reuqest to cancel job
 * @author: madechang
 */
void panel_job_cancel(uint32_t job_id)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_JOB_CANCEL;
    send_msg.msg1        = job_id;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest to abort job
 * @author: madechang
 */
void panel_job_abort(uint32_t job_id)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_JOB_ABORT;
    send_msg.msg1        = job_id;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest to remove job
 * @author: longxuan
 */
void panel_job_remove(uint32_t job_id)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_JOB_REMOVE;
    send_msg.msg1 = job_id;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest to priority print job
 * @author: longxuan
 */
void panel_job_priority_print(uint32_t job_id)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_JOB_INSERT;
    send_msg.msg1 = job_id;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest to suspend print job
 * @author: longxuan
 */
void panel_job_suspend_print(void)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_INTERRUPT_MODE;
    send_msg.msg1 = 0;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest to resume print job
 * @author: longxuan
 */
void panel_job_resume_print(void)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_INTERRUPT_RELIEVE;
    send_msg.msg1 = 0;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest to mono print when color toner empty
 * @author: madechang
 */
void panel_request_mono_print(void)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_IAMGE_IPS_TONER_EMPTY_TURN_MONO;
    send_msg.msg1        = 0;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_PRINT_PARSER_IF, &send_msg );

}

/**
 * @brief panel reuqest to force print
 * @author: madechang
 */
void panel_request_force_print(void* data, uint32_t data_len )
{
    uint32_t force_print_tray = 0;
    uint32_t force_print_paper_size = 0;
    uint32_t force_print_paper_type = 0;

    if( !data || data_len != 12 )    //tow uint
    {
        pi_log_e("panel request force print data error\n");
        return;
    }

    force_print_tray = *( (uint32_t*) data);
    force_print_paper_size = *( (uint32_t*) (data + 4) );
    force_print_paper_type = *( (uint32_t*) (data + 8) );
    pi_log_d("panel request force print to tray:%d, paper size:%d, paper type:%d\n",
                force_print_tray, force_print_paper_size, force_print_paper_type);
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_FORCE_PRINT;
    send_msg.msg1        = force_print_tray;
    send_msg.msg2        = force_print_paper_size;
    send_msg.msg3        = (void *)force_print_paper_type;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_PRINT_IF, &send_msg );

}

/**
 * @brief panel reuqest to change job attribute
 * @author: madechang
 */
void panel_request_change_job_attr(void )
{
    pi_log_d("panel request to change job attribute\n");
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_CTRL_CHANGE_JOB_ATTR;
    send_msg.msg1        = 0;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_PRINT_IF, &send_msg );

}

/**
 * @brief panel reuqest to remove finisher
 * @author: madechang
 */
void panel_request_finisher_remove( void* data, uint32_t data_len )
{
    if( NULL == data || data_len != sizeof(uint32_t))
    {
        pi_log_e("panel request to change job attribute: data error!\n");
        return;
    }
    uint32_t bind_punch_type = *( (uint32_t*) data);
    pi_log_d("panel request to change job attribute:%d\n",bind_punch_type);
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_DATA_FINISHER_REMOVE;
    send_msg.msg1        = bind_punch_type;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_PRINT_IF, &send_msg );

}

/**
 * @brief panel reuqest copy job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @param[out] ROUTER_MSG_S s_panel_evt_client: pointer of panel event client
 * @author: madechang
 */
void copy_job_start_process( void *data ,int data_len )
{
    COPY_JOB_REQUEST_DATA_S *copy_job_param = NULL;
    JOB_REQUEST_S *panel_request_copy_job = NULL;
    ROUTER_MSG_S send_msg;

    if (!data || data_len < 0)
    {
        return;
    }
    if( data_len != sizeof(COPY_JOB_REQUEST_DATA_S) )
    {
        pi_log_e("panel copy param error!\n");
        return;
    }
    copy_job_param = pi_malloc (sizeof( COPY_JOB_REQUEST_DATA_S ));
    if( !copy_job_param )
    {
      return;
    }
    panel_request_copy_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_copy_job )
    {
        pi_free( copy_job_param );
        return;
    }

    pi_memset( copy_job_param, 0, sizeof( COPY_JOB_REQUEST_DATA_S ));
    pi_memcpy( copy_job_param, data, data_len);
    pi_memset( panel_request_copy_job, 0, sizeof( JOB_REQUEST_S ));

    //补充叠图信息
    panel_overlay_job_process( (OVERLAY_IMG_INFO_S*)(&copy_job_param->oly_img_info) );

    ///black machine color set to black
    #ifndef CONFIG_COLOR
    copy_job_param->color_mode = 0;
    #endif

    panel_request_copy_job->io_class = 0;
    panel_request_copy_job->io_via =0;
    panel_request_copy_job->pqio =NULL;
    panel_request_copy_job->job_config_param = copy_job_param;

    pi_log_i("copies:%d,color_mode:%d,scan_source:%d,scan_size:%d\n",copy_job_param->copies,copy_job_param->color_mode,copy_job_param->scan_source,copy_job_param->scan_size);
    pi_log_i("print_tray:%d,print_size:%d,print_mode:%d\n",copy_job_param->print_tray,copy_job_param->print_size,copy_job_param->print_mode);

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = 0;
    send_msg.msg2 = COPY_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_copy_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest scan job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @param[out] ROUTER_MSG_S message: job param from panel
 * @author: madechang
 */
void scan_job_start_process( void *data ,int data_len )
{
    SCAN_JOB_REQUEST_DATA_S *scan_job_param = NULL;
    JOB_REQUEST_S *panel_request_scan_job = NULL;
    ROUTER_MSG_S send_msg;
    uint32_t send_router = 0;

    if (!data || data_len < 0)
    {
        return;
    }
    if(data_len != sizeof( SCAN_JOB_REQUEST_DATA_S ))
    {
        pi_log_e("scan param error %d, %d\n", sizeof( SCAN_JOB_REQUEST_DATA_S ), data_len);
        return;
    }
    scan_job_param = pi_malloc (sizeof( SCAN_JOB_REQUEST_DATA_S ));
    if(!scan_job_param )
    {
        return;
    }
    panel_request_scan_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_scan_job)
    {
        pi_free( scan_job_param );
        return;
    }
    pi_memset( scan_job_param, 0, sizeof( SCAN_JOB_REQUEST_DATA_S ));
    pi_memcpy( scan_job_param, data, data_len);
    pi_memset( panel_request_scan_job, 0, sizeof( JOB_REQUEST_S ));
    panel_request_scan_job->io_class = 0;
    panel_request_scan_job->io_via = 0;
    for( uint32_t i = 0; i < SCAN_TO_MAX; ++i )
    {
        send_router = scan_job_param->scan_config_param.scan_send_router[i];
        if( (SCAN_TO_PC == send_router) || (SCAN_TO_U_DISK == send_router) )
        {
            if( IO_VIA_NET == panel_request_scan_job->io_via )
            {
                panel_request_scan_job->io_via = 0;
                break;
            }
            else
            {
                panel_request_scan_job->io_via = IO_VIA_USB;
            }
        }
        else if( (SCAN_TO_EMAIL == send_router) || (SCAN_TO_FTP == send_router) || (SCAN_TO_SMB == send_router) )
        {
            if( IO_VIA_USB == panel_request_scan_job->io_via )
            {
                panel_request_scan_job->io_via = 0;
                break;
            }
            else
            {
                panel_request_scan_job->io_via = IO_VIA_NET;
            }
        }
        else if( SCAN_TO_INVALID == send_router )
        {
            break;
        }
    }

    panel_request_scan_job->pqio =NULL;
    panel_request_scan_job->job_config_param = scan_job_param;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = 0;
    send_msg.msg2 = SCAN_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_scan_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest scan job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @param[out] ROUTER_MSG_S message: job param from panel
 * @author: madechang
 */
void udisk_print_job_start_process( void *data ,int data_len )
{
    PANEL_PRINT_JOB_CONFIG_PARAM_S *print_config_job_param = NULL;
    JOB_REQUEST_S *panel_request_udisk_print_job = NULL;
    ROUTER_MSG_S send_msg;

    if (!data || data_len < 0)
    {
        return;
    }
    if(data_len != sizeof( PANEL_REQUEST_UDISK_PRINT_JOB_DATA_S ))
    {
        pi_log_e("udisk print param error!\n");
        return;
    }
    print_config_job_param = pi_malloc (sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));
    if(!print_config_job_param )
    {
        return;
    }
    panel_request_udisk_print_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_udisk_print_job)
    {
        pi_free( print_config_job_param );
        return;
    }
    pi_memset( print_config_job_param, 0, sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));
    pi_memcpy( &print_config_job_param->print_job_data.udisk_data, data, data_len);
    pi_memset( panel_request_udisk_print_job, 0, sizeof( JOB_REQUEST_S ));
    print_config_job_param->io_via = IO_VIA_UDISK;
    panel_request_udisk_print_job->io_class = IO_CLASS_PRINT;
    panel_request_udisk_print_job->io_via = IO_VIA_UDISK;
    panel_request_udisk_print_job->pqio =NULL;
    panel_request_udisk_print_job->job_config_param = print_config_job_param;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = 0;
    send_msg.msg2 = PRINT_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_udisk_print_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest pincode job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: longxuan
 */
void pincode_print_job_start_process( void *data ,int data_len )
{
    PANEL_PRINT_JOB_CONFIG_PARAM_S *print_config_job_param = NULL;
    JOB_REQUEST_S *panel_request_pincode_print_job = NULL;
    PANEL_REQUEST_PINCODE_S *req_data = (PANEL_REQUEST_PINCODE_S *)data;
    ROUTER_MSG_S send_msg;

    if (!req_data || data_len <= 0)
    {
        return;
    }
    if(data_len != sizeof( PANEL_REQUEST_PINCODE_S ))
    {
        pi_log_e("pincode print param error! %d|%d\n", data_len, sizeof( PANEL_REQUEST_PINCODE_S ));
        return;
    }
    if(sizeof(req_data->save_path) > sizeof( PANEL_PRINT_PINCODE_JOB_CTRL_S ))
    {
        pi_log_e("pincode print save path size out of range!\n");
        return;
    }

    print_config_job_param = pi_malloc (sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));
    if(!print_config_job_param )
    {
        return;
    }
    pi_memset( print_config_job_param, 0, sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));

    panel_request_pincode_print_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_pincode_print_job)
    {
        pi_free( print_config_job_param );
        return;
    }
    pi_memset( panel_request_pincode_print_job, 0, sizeof( JOB_REQUEST_S ));

    pi_memcpy( &print_config_job_param->print_job_data.pincode_ctrl, req_data->save_path, sizeof(req_data->save_path));
    print_config_job_param->io_via = IO_VIA_FILE;
    print_config_job_param->type = PANEL_PRINT_JOB_TYPE_PINCODE;

    panel_request_pincode_print_job->io_class = IO_CLASS_PRINT;
    panel_request_pincode_print_job->io_via = IO_VIA_FILE;
    panel_request_pincode_print_job->pqio =NULL;
    panel_request_pincode_print_job->job_config_param = print_config_job_param;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = req_data->job_id;
    send_msg.msg2 = PRINT_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_pincode_print_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest delay job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: longxuan
 */
void delay_print_job_start_process( void *data ,int data_len )
{
    PANEL_PRINT_JOB_CONFIG_PARAM_S *print_config_job_param = NULL;
    JOB_REQUEST_S *panel_request_delay_print_job = NULL;
    PANEL_REQUEST_DELAY_PRINT_S *req_data = (PANEL_REQUEST_DELAY_PRINT_S *)data;
    ROUTER_MSG_S send_msg;
    uint32_t path_size = 0;

    if (!req_data || data_len <= 0)
    {
        return;
    }
    if(data_len != sizeof( PANEL_REQUEST_DELAY_PRINT_S ))
    {
        pi_log_e("delay print param error! %d|%d\n", data_len, sizeof( PANEL_REQUEST_DELAY_PRINT_S ));
        return;
    }

    print_config_job_param = pi_malloc (sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));
    if(!print_config_job_param )
    {
        return;
    }
    pi_memset( print_config_job_param, 0, sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));

    panel_request_delay_print_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_delay_print_job)
    {
        pi_free( print_config_job_param );
        return;
    }
    pi_memset( panel_request_delay_print_job, 0, sizeof( JOB_REQUEST_S ));

    path_size = MIN(sizeof(req_data->save_path), sizeof(print_config_job_param->print_job_data.delay_ctrl.file_path));
    pi_memcpy( print_config_job_param->print_job_data.delay_ctrl.file_path, req_data->save_path, path_size);
    print_config_job_param->io_via = IO_VIA_FILE;
    print_config_job_param->type = PANEL_PRINT_JOB_TYPE_DELAY_PRINT;

    panel_request_delay_print_job->io_class = IO_CLASS_PRINT;
    panel_request_delay_print_job->io_via = IO_VIA_FILE;
    panel_request_delay_print_job->pqio =NULL;
    panel_request_delay_print_job->job_config_param = print_config_job_param;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = req_data->job_id;
    send_msg.msg2 = PRINT_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_delay_print_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );

}

/**
 * @brief panel reuqest sample print job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: longxuan
 */
void sample_print_job_start_process( void *data ,int data_len )
{
    PANEL_PRINT_JOB_CONFIG_PARAM_S *print_config_job_param = NULL;
    JOB_REQUEST_S *panel_request_sample_print_job = NULL;
    PANEL_REQUEST_SAMPLE_PRINT_S *req_data = (PANEL_REQUEST_SAMPLE_PRINT_S *)data;
    ROUTER_MSG_S send_msg;

    if (!req_data || data_len <= 0)
    {
        return;
    }
    if(data_len != sizeof( PANEL_REQUEST_SAMPLE_PRINT_S ))
    {
        pi_log_e("sample print param error! %d|%d\n", data_len, sizeof( PANEL_REQUEST_SAMPLE_PRINT_S ));
        return;
    }

    print_config_job_param = pi_malloc (sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));
    if(!print_config_job_param )
    {
        return;
    }
    pi_memset( print_config_job_param, 0, sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));

    panel_request_sample_print_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_sample_print_job)
    {
        pi_free( print_config_job_param );
        return;
    }
    pi_memset( panel_request_sample_print_job, 0, sizeof( JOB_REQUEST_S ));

    pi_memcpy( &print_config_job_param->print_job_data.print_sample, &(req_data->params), sizeof(req_data->params));
    print_config_job_param->io_via = IO_VIA_FILE;
    print_config_job_param->type = PANEL_PRINT_JOB_TYPE_SAMPLE_PRINT;

    panel_request_sample_print_job->io_class = IO_CLASS_PRINT;
    panel_request_sample_print_job->io_via = IO_VIA_FILE;
    panel_request_sample_print_job->pqio =NULL;
    panel_request_sample_print_job->job_config_param = print_config_job_param;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = req_data->job_id;
    send_msg.msg2 = PRINT_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_sample_print_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );
}

/**
 * @brief panel reuqest to continue sample copy job
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @param[out] ROUTER_MSG_S message: job param from panel
 * @author: madechang
 */
void sample_copy_job_continue( void *data ,int data_len )
{
    SAMPLE_COPY_JOB_INFO_S *sample_copy_job_param = NULL;
    ROUTER_MSG_S send_msg;

    if (!data || data_len < 0)
    {
        return;
    }
    if(data_len != sizeof( SAMPLE_COPY_JOB_INFO_S ))
    {
        pi_log_e("sample copy param error!\n");
        return;
    }
    sample_copy_job_param = (SAMPLE_COPY_JOB_INFO_S*)data;

    pi_log_d("sample_copy_job_param jd_id:%d,copies:%d\n", sample_copy_job_param->job_id,sample_copy_job_param->copies);
    send_msg.msgType     = MSG_CTRL_JOB_RESTART;
    send_msg.msg1        = sample_copy_job_param->job_id;
    send_msg.msg2        = sample_copy_job_param->copies;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router(MID_PRINT_IF, &send_msg );

}


/**
 * @brief get panel version from panel
 * @author: madechang
 */
void get_panel_version( void *data ,int data_len )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();
    pi_log_d("get panel version: %s\n", data);
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY, data, data_len );
}

/**
 * @brief panel request to print info page
 * @author: madechang
 */
void print_info_page_job( void *data ,int data_len )
{
    PANEL_PRINT_JOB_CONFIG_PARAM_S *print_config_job_param = NULL;
    JOB_REQUEST_S *panel_request_info_page_print_job = NULL;
    uint32_t info_page_mode = 0;
    PANEL_REQUEST_PINCODE_S *req_data = (PANEL_REQUEST_PINCODE_S *)data;
    ROUTER_MSG_S send_msg;

    if (!data || data_len <= 0)
    {
        return;
    }
    info_page_mode = *((uint32_t*)data);

    print_config_job_param = pi_malloc (sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));
    if(!print_config_job_param )
    {
        return;
    }
    pi_memset( print_config_job_param, 0, sizeof( PANEL_PRINT_JOB_CONFIG_PARAM_S ));

    panel_request_info_page_print_job = pi_malloc( sizeof(JOB_REQUEST_S) );
    if( !panel_request_info_page_print_job)
    {
        pi_free( print_config_job_param );
        return;
    }
    pi_memset( panel_request_info_page_print_job, 0, sizeof( JOB_REQUEST_S ));

    pi_log_i("panel request print info page job, mode = %d\n", info_page_mode);
    print_config_job_param->io_via = IO_VIA_INTERNAL;
    print_config_job_param->type = info_page_mode;
    print_config_job_param->print_job_data.internal_page.language_type = 1;

    panel_request_info_page_print_job->io_class = IO_CLASS_PRINT;
    panel_request_info_page_print_job->io_via = IO_VIA_INTERNAL;
    panel_request_info_page_print_job->pqio =NULL;
    panel_request_info_page_print_job->job_config_param = print_config_job_param;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = 0;
    send_msg.msg2 = PRINT_OBJ; ///JOB_CONFIG_OBJ_E use to div job struct param
    send_msg.msg3 = panel_request_info_page_print_job;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router( MID_SYS_JOB_MGR, &send_msg );
}

/**
 * @brief panel request to get storage remain
 * @author: madechang
 */
void panel_get_storage_remain(  )
{
    STORAGE_REMAIN_INFO_S storage_remain_info = {0};
    int ret = 0;

    /*
    media_type参考storage_string_map.h中传STO_MEDIA_TYPE_SSD，media_num传1
    */
    ret = pi_hal_storage_media_ssd_get_size(&storage_remain_info.total, &storage_remain_info.remain, &storage_remain_info.sys_used);
    if( 0 == ret )
    {
        storage_remain_info.result = TRUE;
        pi_log_d("panel get storage space,total:%llu B,remain:%llu B, sys:%llu B\n",storage_remain_info.total, storage_remain_info.remain, storage_remain_info.sys_used );
    }
    else
    {
        storage_remain_info.result = FALSE;
        pi_log_e("panel get storage remain error!\n");
    }
    panel_send_data_u8( OPERATE, OPERATE_CMD_GET_STORAGE_REMAIN, NOTIFICATION_RESPONSE, &storage_remain_info, sizeof(STORAGE_REMAIN_INFO_S));

}

/**
 * @brief panel request to get storage remain
 * @author: madechang
 */
void panel_get_udisk_remain( void )
{
    STORAGE_REMAIN_INFO_S udisk_remain_info = {0};
    int ret = 0;

    /*
    media_type参考storage_string_map.h中传STO_MEDIA_TYPE_SSD，media_num传1
    */
    ret = pi_hal_storage_media_udisk_get_size(&udisk_remain_info.total, &udisk_remain_info.remain, &udisk_remain_info.sys_used);
    if( 0 == ret )
    {
        udisk_remain_info.result = TRUE;
        pi_log_d("panel get udisk space,total:%llu B,remain:%llu B, sys:%llu B\n",udisk_remain_info.total, udisk_remain_info.remain, udisk_remain_info.sys_used );
    }
    else
    {
        udisk_remain_info.result = FALSE;
        pi_log_e("panel get udisk remain error!\n");
    }
    panel_send_data_u8( SETTING, SETTING_CMD_GET_UDISK_REMAIN, NOTIFICATION_RESPONSE, &udisk_remain_info, sizeof(STORAGE_REMAIN_INFO_S));

}

/**
 * @brief panel request to disable/enable udisk
 * @author: madechang
 */
void panel_request_disable_udisk(void *data ,int data_len  )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();
    pi_log_d("panel request disable udisk: %d\n", *(uint32_t*)data);
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_USBDISK_DISABLE, data, data_len );

}

/**
 * @brief panel request to offline upgrade
 * @author: madechang
 */
void panel_request_offline_upgrade(void *data ,int data_len  )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;

    panel_evt_client = get_panel_event_client();
    pi_log_d( "panel request offline upgrade\n" );
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_UPGRADE_OFFLINE_UPGRADE_MODE_REQUEST, data, data_len );

}

/**
 * @brief process information cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void information_cmd_process(unsigned short cmd ,void *data ,int data_len)
{

    switch ( cmd )
    {
        case INFO_CMD_GET_PANEL_VERSION:
            get_panel_version( (void *) data, data_len );
            break;
        default:
            break;
    }
}

static void panel_get_job_list_callback(const JOBINFO_S *job , void *context)
{
    if( !job )
    {
        pi_log_w("invalid params of get job list callback\n");
        return;
    }

    panel_send_data_u8(OPERATE, OPERATE_CMD_JOB_LIST_DATA_SYNC, NOTIFICATION_RESPONSE,
                        (void *)job, sizeof(JOBINFO_S));
}

/**
 * @brief process job cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void job_cmd_process(unsigned short cmd ,void *data ,int data_len)
{

    switch ( cmd )
    {
        case JOB_CMD_COPY_START:
            pi_log_i("panel request copy job\n");
            copy_job_start_process( (void *) data, data_len);
            break;
        case JOB_CMD_SCAN_START:
            pi_log_i("panel request scan job\n");
            scan_job_start_process( (void *) data , data_len);
            break;
        case JOB_CMD_PRINT_INFO_PAGE:
            print_info_page_job((void *) data , data_len );
            break;

        case JOB_CMD_UDISK_PRINT_START:
            pi_log_i("panel request udisk print job\n");
            udisk_print_job_start_process((void *) data , data_len);
            break;

        case JOB_CMD_PINCODE_PRINT_START:
            pi_log_i("panel request pincode print job\n");
            pincode_print_job_start_process(data , data_len);
            break;

        case JOB_CMD_DELAY_PRINT_START:
            pi_log_i("panel request delay print job\n");
            delay_print_job_start_process(data , data_len);
            break;

        case JOB_CMD_SAMPLE_PRINT_START:
            pi_log_i("panel request sample print job\n");
            sample_print_job_start_process(data , data_len);
            break;

        case JOB_CMD_SAMPLE_COPY_CONTINUE:
            pi_log_i("panel request sample copy job\n");
            sample_copy_job_continue((void *) data , data_len);
            break;

        default:
            break;
    }

}


/**
 * @brief panel request to check ssd compatch
 * @author: madechang
 */
void panel_request_ssd_compat_check( void )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;
    panel_evt_client = get_panel_event_client();

    pi_log_d("panel request to check ssd compatch\n");
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_COMPATCHECK_REQUEST, NULL, 0 );
}

/**
 * @brief panel request to check ssd health
 * @author: madechang
 */
void panel_request_ssd_health_check( void )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;
    panel_evt_client = get_panel_event_client();

    pi_log_d("panel request to check ssd health\n");
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_REQUEST, NULL, 0 );
}

/**
 * @brief panel request to format ssd
 * @author: madechang
 */
void panel_request_ssd_format( void )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;
    panel_evt_client = get_panel_event_client();

    pi_log_d("panel request to format ssd\n");
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_FORMAT_REQUEST , NULL, 0 );

}

/**
 * @brief panel request to activate ssd
 * @author: madechang
 */
void panel_request_ssd_activate( void* data, uint32_t data_len )
{
    EVT_MGR_CLI_S* panel_evt_client = NULL;
    panel_evt_client = get_panel_event_client();

    pi_log_d("panel request to format ssd\n");
    pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_ACTIVATE_REQUEST , data, data_len );

}

/**
 * @brief panel request to clear print data
 * @author: longxuan
 */
static void panel_request_print_data_clear( void )
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType     = MSG_PRINT_ENGINE_OPC_CLEAR;
    send_msg.msg1        = 0;
    send_msg.msg2        = 0;
    send_msg.msg3        = NULL;
    send_msg.msgSender   = MID_PANEL;
    task_msg_send_by_router( MID_PRINT_IF, &send_msg );
}

/**
 * @brief panel send upgrade operate cmd
 * @author: longxuan
 */
static void panel_send_upgrade_operate(uint32_t data)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_FW_UPGRATE_CONFIRM_SEC;
    send_msg.msg1 = data;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_PANEL;
    task_msg_send_by_router(MID_UPGRADE, &send_msg );

}

/**
 * @brief process operate cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void operate_cmd_process(unsigned short cmd ,void *data ,int data_len)
{
    uint32_t data_buff = 0;

    switch ( cmd )
    {
        case OPERATE_CMD_CANCEL_JOB_REQUEST:
            data_buff = *( (uint32_t *) data  );
            pi_log_d("panel request to cancel job_id:%d\n",data_buff);
            panel_job_cancel(data_buff);
            break;

         case OPERATE_CMD_SCAN_NEXT_REQUEST:

            data_buff = *( ( uint32_t * ) data );
            pi_log_d("panel request to scan next page, job_id:%d\n",data_buff);
            panel_request_scan_next_page( data_buff );
            break;

         case OPERATE_CMD_SCAN_NEXT_DONE_REQUEST:
             data_buff = *( ( uint32_t * ) data );
             pi_log_d("panel request to scan done, job_id:%d\n",data_buff);
             panel_request_scan_done( data_buff );
            break;

         case OPERATE_CMD_REQUEST_RESTORE_FACTORY_DEFAULT:
            panel_request_restore_factory();
            break;

         case OPERATE_CMD_SLEEP_FIRST_REQUEST:
            panel_request_sleep();
            break;

        case OPERATE_CMD_SLEEP_SECOND_REQUEST:
            panel_request_sleep();
           break;

       case OPERATE_CMD_WAKEUP_REQUEST:
            panel_request_wakeup();
            break;

       case OPERATE_CMD_POWER_OFF_REQUEST:
            panel_request_machine_power_off();
            break;

       case OPERATE_CMD_CANCEL_POWER_OFF:
            panel_request_cancel_power_off();
            break;


       case OPERATE_CMD_CLEAR_PRINT_ERRORS:
            data_buff = *( ( uint32_t * ) data );
            pi_log_d("panel request to clear print error [%d]\n",data_buff);
            panel_request_clear_print_errors( data_buff );
            break;

        case OPERATE_CMD_UPGRADE_START_PROC:
            data_buff = *( (uint8_t *) data  );
            panel_request_start_upgrade( data_buff );
             break;

         case OPERATE_CMD_REQUEST_MONO_PRINT:
            panel_request_mono_print();
            break;

        case OPERATE_CMD_REMOVE_JOB:
            data_buff = *( ( uint32_t * ) data );
            panel_job_remove(data_buff);
            break;

        case OPERATE_CMD_STORAGE_UNINSTALL_REQUEST  :
            pi_log_d("panel request to uninstall storage\n");
            panel_request_uninstall_storage( data, data_len );
            break;
        case OPERATE_CMD_STORAGE_REPAIRE_REQUEST    :
            pi_log_d("panel request to repaire storage\n");
            panel_request_repaire_storage( data, data_len );
            break;
        case OPERATE_CMD_STORAGE_FORMAT_REQUEST     :
            pi_log_d("panel request to format storage\n");
            panel_request_format_storage( data, data_len );
            break;
        case OPERATE_CMD_STORAGE_CLEAR_ERROR_REQUEST:
            pi_log_d("panel request to clear storage error\n");
            panel_request_clear_storage_error( data, data_len );
            break;

        case OPERATE_CMD_FORCE_PRINT:
            panel_request_force_print( data, data_len );
            break;

        case OPERATE_CMD_CHANGE_JOB_ATTR:
            panel_request_change_job_attr();
            break;

        case OPERATE_CMD_FINISHER_REMOVE:
            panel_request_finisher_remove( data, data_len );
            break;

        case OPERATE_CMD_GET_UDISK_FILE_REQUEST:
            panel_get_udisk_file_info((const char*)data);
            break;

        case OPERATE_CMD_SATASSD1_CLEAR_ALL_DATA:
            panel_request_SATASSD1_CLEAR_ALL_DATA( data, data_len );
            break;

        case OPERATE_CMD_GET_STORAGE_REMAIN:
            panel_get_storage_remain();
            break;

        case OPERATE_CMD_SYSTEM_REBOOT_REQUEST:
            printf("panel request to reboot\n");
            system("reboot");
            break;

        case OPERATE_CMD_STORAGEMGR_USBDISK_DISABLE:
            panel_request_disable_udisk( data, data_len );
            break;

        case OPERATE_CMD_PRIORITY_PRINT:
            data_buff = *( (uint32_t *) data  );
            pi_log_d("panel request to priority print job_id:%d\n",data_buff);
            panel_job_priority_print(data_buff);
            break;

        case OPERATE_CMD_SUSPEND_PRINT:
            pi_log_d("panel request to suspend print\n");
            panel_job_suspend_print();
            break;

        case OPERATE_CMD_RESUME_PRINT:
            pi_log_d("panel request to resume print\n");
            panel_job_resume_print();
            break;

        case OPERATE_CMD_JOB_LIST_DATA_SYNC:
        {
            uint32_t ret = job_manager_get_specify_joblist(JOB_TYPE_PRINT_DELAY, NULL, panel_get_job_list_callback);
            pi_log_d("get delay print job size %d\n", ret);
            ret = job_manager_get_specify_joblist(JOB_TYPE_PRINT_PINCODE, NULL, panel_get_job_list_callback);
            pi_log_d("get pincode job size %d\n", ret);
            ret = job_manager_get_specify_joblist(JOB_TYPE_PRINT_SAMPLE, NULL, panel_get_job_list_callback);
            pi_log_d("get sample print job size %d\n", ret);
            break;
        }

        case OPERATE_CMD_OFFLINE_UPGRADE_MODE:
            panel_request_offline_upgrade( data, data_len );
            break;

        case OPERATE_CMD_JOB_ABORT:
            data_buff = *( (uint32_t *) data  );
            pi_log_d("panel request to abort job_id:%d\n",data_buff);
            panel_job_abort( data_buff );
            break;

        case OPERATE_CMD_STORAGEMGR_SATASSD1_COMPAT_CHECK:
            panel_request_ssd_compat_check();
            break;

        case OPERATE_CMD_STORAGEMGR_SATASSD1_HEALTH_CHECK:
            panel_request_ssd_health_check();
            break;

        case OPERATE_CMD_STORAGEMGR_SATASSD1_FORMAT:
            panel_request_ssd_format();
            break;

        case OPERATE_CMD_STORAGEMGR_SATASSD1_ACTIVATE:
            panel_request_ssd_activate( data, data_len );
            break;

        case OPERATE_CMD_PRINT_DATA_CLEAR:
            panel_request_print_data_clear();
            break;

        case OPERATE_CMD_UPGRADE_OPERATE:
            panel_send_upgrade_operate( *(uint32_t*)data );
            break;

        default:
            break;
    }

}

/**
 * @brief process setting cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void setting_cmd_process(unsigned short cmd ,void *data ,int data_len)
{
    uint32_t ret = 0;
    NET_IPV6_CONF_S* ipv6_data = NULL;
    EVT_MGR_CLI_S* panel_evt_client = NULL;
    NET_IPV4_CONF_S* ipv4_data = NULL;
    PANEL_PRINT_DEFAULT_CONFIG_S* default_print_config = NULL;
    PANEL_PRINT_IMAGE_CONFIG_S* print_image_config = NULL;
    PANEL_CONFIG_TABLE_S* panel_config_p = get_panel_config_table();

    if (!data || data_len < 0)
    {
        return;
    }
    panel_evt_client = get_panel_event_client();

    switch ( cmd )
    {
    case SETTING_CMD_DATA_SYNC:
        //面板重连时proxy发送该帧作为重连标志，用于节能唤醒的数据同步
        panel_data_sync();
        break;
    case SETTING_CMD_SET_SYS_TIME:
        panel_set_system_time( (UI_DATE_TIME_S *) data );
        break;

    case SETTING_CMD_SET_SLEEP_TIME:
        ret = panel_set_setting_to_nvram_u32( data, data_len, cmd );
        if( ret == 0 )
        {
            ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY, data, data_len);
        }
        break;

        /// these setting just need to save
    case SETTING_CMD_SET_SLEEP_MODE:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_SLEEP_MODE_MODIFY, data, data_len );
        break;
    case SETTING_CMD_SET_TIME_FORMAT:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_TIME_FORMAT_MODIFY, data, data_len );
        break;
    case SETTING_CMD_SET_DATE_FORMAT:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_DATE_FORMAT_MODIFY, data, data_len );
        break;
    case SETTING_CMD_SET_BRIGHTNESS:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_LCD_BACKLIGHT_MODIFY, data, data_len );
        break;
    case SETTING_CMD_SET_BEEP_VOLUE:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_SYS_VOLUE_MODIFY, data, data_len );
        break;
    case SETTING_CMD_SET_JOB_ERROR_PROCESS_MODE:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_MODIFY, &(panel_config_p->job_error_process_mode), sizeof(panel_config_p->job_error_process_mode) );
        break;
    case SETTING_CMD_SET_JOB_ERROR_DELETE_TIME:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_MODIFY, &(panel_config_p->job_error_delete_time), sizeof(panel_config_p->job_error_delete_time) );
        break;

    case SETTING_CMD_SET_MUTE_MODE:
    case SETTING_CMD_SET_AMBIENT_LIGHT:
    case SETTING_CMD_SET_ERROR_STATUS_VOLUME:
    case SETTING_CMD_SET_JOB_END_VOLUME:
    case SETTING_CMD_SET_LOCK_SCREEN_TIME:
    case SETTING_CMD_SET_SYSTEM_SET_TIMEOUT:
    case SETTING_CMD_SET_LOCK_SCREEN_SWITCH:
    case SETTING_CMD_SET_COPY_PARAM_RESET:
    case SETTING_CMD_SET_COPY_RESET_RANGE_SWITCH:
    case SETTING_CMD_SET_ADF_GET_PAPER_VOLUME:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        break;
    case SETTING_CMD_PRINT_SET_CUSTOM1_SIZE_HEIGHT:
        panel_config_p->print_multi_custom_size.custom_1.length = *(uint32_t*) data;
        pi_log_d("panel set print custom_1 height: %d\n", panel_config_p->print_multi_custom_size.custom_1.length);
        notify_security_operate( SECURITY_OPERATE_CUSTOM1_SIZE_LENGTH, data, data_len );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST, &panel_config_p->print_multi_custom_size, sizeof(panel_config_p->print_multi_custom_size));
        break;
    case SETTING_CMD_PRINT_SET_CUSTOM1_SIZE_WIDTH:
        panel_config_p->print_multi_custom_size.custom_1.width = *(uint32_t*) data;
        pi_log_d("panel set print custom_1 width: %d\n", panel_config_p->print_multi_custom_size.custom_1.width);
        notify_security_operate( SECURITY_OPERATE_CUSTOM1_SIZE_HEIGHT, data, data_len );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST, &panel_config_p->print_multi_custom_size, sizeof(panel_config_p->print_multi_custom_size));
        break;
    case SETTING_CMD_PRINT_SET_CUSTOM2_SIZE_HEIGHT:
        panel_config_p->print_multi_custom_size.custom_2.length = *(uint32_t*) data;
        pi_log_d("panel set print custom_2 height: %d\n", panel_config_p->print_multi_custom_size.custom_2.length);
        notify_security_operate( SECURITY_OPERATE_CUSTOM2_SIZE_LENGTH, data, data_len );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST, &panel_config_p->print_multi_custom_size, sizeof(panel_config_p->print_multi_custom_size));
        break;
    case SETTING_CMD_PRINT_SET_CUSTOM2_SIZE_WIDTH:
        panel_config_p->print_multi_custom_size.custom_2.width = *(uint32_t*) data;
        pi_log_d("panel set print custom_2 width: %d\n", panel_config_p->print_multi_custom_size.custom_2.width);
        notify_security_operate( SECURITY_OPERATE_CUSTOM2_SIZE_HEIGHT, data, data_len );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST, &panel_config_p->print_multi_custom_size, sizeof(panel_config_p->print_multi_custom_size));
        break;
    case SETTING_CMD_PRINT_SET_CUSTOM3_SIZE_HEIGHT:
        panel_config_p->print_multi_custom_size.custom_3.length = *(uint32_t*) data;
        pi_log_d("panel set print custom_3 height: %d\n", panel_config_p->print_multi_custom_size.custom_3.length);
        notify_security_operate( SECURITY_OPERATE_CUSTOM3_SIZE_LENGTH, data, data_len );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST, &panel_config_p->print_multi_custom_size, sizeof(panel_config_p->print_multi_custom_size));
        break;
    case SETTING_CMD_PRINT_SET_CUSTOM3_SIZE_WIDTH:
        panel_config_p->print_multi_custom_size.custom_3.width = *(uint32_t*) data;
        pi_log_d("panel set print custom_3 width: %d\n", panel_config_p->print_multi_custom_size.custom_3.width);
        notify_security_operate( SECURITY_OPERATE_CUSTOM3_SIZE_HEIGHT, data, data_len );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_REQUEST, &panel_config_p->print_multi_custom_size, sizeof(panel_config_p->print_multi_custom_size));
        break;

    case SETTING_CMD_SET_LOCK_SCREEN_PASSWORD:
        panel_set_setting_to_nvram_str( data, data_len, cmd );
        pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_SAFETY_MANAGEMENT_PSW_MODIFY, data, data_len);
        break;

    case SETTING_CMD_SET_USB_CONTROL:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        panel_set_usb_control( data, data_len);
        break;

    case SETTING_CMD_SET_NETWORK_HOST_NAME:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_HOST_NAME_REQUEST, data, sizeof(data));
        break;
    case SETTING_CMD_SET_NETWORK_IPV6_SWITCH:
        ipv6_data = set_ipv6_config( data );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_ETH_IPV6_CONFIG_REQUEST, ipv6_data, sizeof(NET_IPV6_CONF_S));
        break;
    case SETTING_CMD_SET_NETWORK_IPV4_CONFIG:
        ipv4_data = (NET_IPV4_CONF_S *) data;
        pi_log_d("panel set ipv4 dhcp:%d, addr:%s\n",ipv4_data->dhcp_enabled,ipv4_data->address);
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_ETH_IPV4_CONFIG_REQUEST, data, data_len);
        break;
    case SETTING_CMD_SET_NET_CONTROL:
        panel_set_setting_to_nvram_u32( data, data_len, cmd );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_ETH_SWITCH_REQUEST, data, data_len);
        break;

    case SETTING_CMD_MAINTENANCE_GET_HARD_DISK_R_W_RET:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_RWCHECK_REQUEST, data, data_len);
        break;

    /*打印设置 默认设置 需推送至print*/
    case SETTING_CMD_PRINT_SET_DEFAULT_TRAY:
        default_print_config = get_panel_print_config();
        panel_set_setting_to_nvram_u32( data ,data_len, cmd );
        pi_log_d("panel set default tray:%d\n", default_print_config->default_tray);

        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_PRINT_DEFAULT_CONFIG_MODIFY, data ,data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY_MULTI_SIZE:
        default_print_config = get_panel_print_config();
        pi_log_d("SET_MULTI_TRAY_SIZE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_MULTI_TRAY_SIZE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_MULTI_TRAY_SIZE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY_MULTI_TYPE:
        default_print_config = get_panel_print_config();
        pi_log_d("SET_TRAY_MULTI_TYPE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_MULTI_TRAY_TYPE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_MULTI_TRAY_TYPE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY_LCT_IN_SIZE:
        default_print_config = get_panel_print_config();
        pi_log_d("SET_TRAY_LCT_IN_SIZE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_LCT_IN_TRAY_SIZE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_LCT_IN_TRAY_SIZE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY_LCT_IN_TYPE:

        default_print_config = get_panel_print_config();
        pi_log_d("SET_TRAY_LCT_IN_TYPE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_LCT_IN_TRAY_TYPE, data, data_len );
            //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_LCT_IN_TRAY_TYPE, data, data_len );

        break;

    case SETTING_CMD_PRINT_SET_TRAY_LCT_OUT_SIZE:
        default_print_config = get_panel_print_config();
        pi_log_d("SET_TRAY_LCT_OUT_SIZE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_LCT_OUT_TRAY_SIZE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_LCT_IN_TRAY_SIZE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY_LCT_OUT_TYPE:

        default_print_config = get_panel_print_config();
        pi_log_d("SET_TRAY_LCT_OUT_TYPE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_LCT_OUT_TRAY_TYPE, data, data_len );
            //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_LCT_IN_TRAY_TYPE, data, data_len );

        break;

    case SETTING_CMD_PRINT_SET_TRAY1_TYPE:

        default_print_config = get_panel_print_config();
        pi_log_d("SET_TRAY1_TYPE:%d\n",*( (uint32_t*)data ) );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_TRAY1_TYPE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_TRAY1_TYPE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY2_TYPE:
        default_print_config = get_panel_print_config();
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_TRAY2_TYPE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_TRAY2_TYPE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY3_TYPE:
        default_print_config = get_panel_print_config();
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_TRAY3_TYPE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_TRAY3_TYPE, data, data_len );
        break;
    case SETTING_CMD_PRINT_SET_TRAY4_TYPE:
        default_print_config = get_panel_print_config();
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_SET_TRAY4_TYPE, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SET_TRAY4_TYPE, data, data_len );
        break;

    /*打印设置-高级画像 需推送至print*/
    case SETTING_CMD_PRINT_SET_COLOR_BALANCE_C:
    case SETTING_CMD_PRINT_SET_COLOR_BALANCE_M:
    case SETTING_CMD_PRINT_SET_COLOR_BALANCE_Y:
    case SETTING_CMD_PRINT_SET_COLOR_BALANCE_K:
    case SETTING_CMD_PRINT_SET_IMAGE_CONTRAST:
    case SETTING_CMD_PRINT_SET_IMAGE_SATURATION:
    case SETTING_CMD_PRINT_SET_IMAGE_BRIGTHNESS:
    case SETTING_CMD_PRINT_SET_TONER_DENISTY:
    case SETTING_CMD_PRINT_SET_SAVE_TONER_MODE:
    case SETTING_CMD_PRINT_SET_IMAGE_ORIGINAL_TYPE:
        print_image_config = get_print_image_config();
        panel_set_setting_to_nvram_u32( data ,data_len, cmd );
        pi_log_d("color balance c:%d,save toner:%d\n",print_image_config->color_balance_c,print_image_config->toner_save);
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_ADVANCED_IMAGE_CONFIG_MODIFY, print_image_config, sizeof( PANEL_PRINT_IMAGE_CONFIG_S ) );
        if(SETTING_CMD_PRINT_SET_SAVE_TONER_MODE == cmd)
        {
            ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PANEL_SAVE_TONER_MODE_MODIFY, data, data_len );
        }
        break;

    //scan engine maintenance mode
    case SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SENSOR:
    case SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SCANING_TEST:
    case SETTING_CMD_MAINTENANCE_SCAN_ADF_ENGINE_PARAM_SET:
    case SETTING_CMD_MAINTENANCE_SCAN_FB_ENGINE_PARAM_SET:
    case SETTING_CMD_MAINTENANCE_SCAN_ENGINE_ERROR_CLEAR:
    case SETTING_CMD_MAINTENANCE_EVENT_LOG:
        panel_request_scan_maintenance_mode( cmd , data , data_len );
        break;

    /****print engine maintenance mode***/
    case SETTING_CMD_MAINTENANCE_SET_FUSER_TMP ... SETTING_CMD_MAINTENANCE_EXEC_SENSOR_CHECK:
    case SETTING_CMD_MAINTENANCE_EXEC_NEW_UNIT_CONFIRM:
    case SETTING_CMD_MAINTENANCE_GET_LD_ADJUST:
    case SETTING_CMD_MAINTENANCE_GET_PAPER_FEED_DIRECTION_ADJUST:
    case SETTING_CMD_MAINTENANCE_GET_PAPER_SEPARATION_ADJUST:
    case SETTING_CMD_MAINTENANCE_SET_LD_ADJUST:
    case SETTING_CMD_MAINTENANCE_SET_PAPER_FEED_DIRECTION_ADJUST:
    case SETTING_CMD_MAINTENANCE_SET_PAPER_SEPARATION_ADJUST:
    case SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST:
    case SETTING_CMD_MAINTENANCE_EXEC_SIMPLEX_FEED:
        pi_log_d("set maintenance function\n");
        panel_request_execute_func(cmd, data, data_len );
        break;
    case SETTING_CMD_MAINTENANCE_ENTER:
        pi_log_d("enter maintenance mode\n");
        panel_request_enter_maintenance_mode();
        break;
    case SETTING_CMD_MAINTENANCE_EXIT:
        pi_log_d("exit maintenance mode\n");
        panel_request_exit_maintenance_mode();
        break;

    case SETTING_CMD_SCAN_SET_ADDRESS_BOOK:
        set_address_book(data, data_len);
        break;
    case SETTING_CMD_SET_WIFI_SWITCH      :
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_SWITCH_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WIFI_SCAN_SSID   :
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_SCAN_SSID_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WIFI_CONNECT     :
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_CONNECT_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WIFI_WPS         :
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_WPS_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WIFI_IPV4_CONFIG :
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_IPV4_CONFIG_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WIFI_DNSV4_CONFIG:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_DNSV4_CONFIG_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WIFI_IPV6_CONFIG :
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_IPV6_CONFIG_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_WFD_SWITCH:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WFD_SWITCH_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_NET_WFD_RESPONSE:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WFD_RESPONSE_REQUEST, data, data_len );
        break;
    case SETTING_CMD_SET_HARD_DISK:
        panel_config_p->hard_disk_control = *(uint32_t*)data;
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_ENABLE, data, data_len );
        break;
    case SETTING_CMD_SET_HARD_DISK_ENCRYPT:
        panel_config_p->hard_disk_encrypt = *(uint32_t*)data;
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_ENCRYPT_ENABLE, data, data_len );
        break;
    case SETTING_CMD_UPDATE_NET_ETH_SPEED:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_ETH_SPEED_REQUEST, data, data_len );
        break;

    case SETTING_CMD_UPDATE_SHORTCUT_COPY:
        panel_set_setting_to_nvram_struct( data, data_len, cmd );
        break;

    case SETTING_CMD_MAINTENANCE_GET_LOG_TO_WEBPAGE_CHOICE:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_EXPORT_LOG_REQUEST, data, data_len );
        break;

    case SETTING_CMD_SET_SYS_LANGUAGE:
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PLATFORM_LANGUAGE_CODE_REQUEST, data, data_len );
        panel_config_p = get_panel_config_table();
        panel_config_p->system_language_code = *(uint32_t*)data;
        pi_log_d( "panel set language:%d\n", panel_config_p->system_language_code );
        break;

    case SETTING_CMD_OVERLAY_COPY_DATA:
        panel_modify_delete_overlay_data( data, data_len );
        break;

    case SETTING_CMD_SET_SMTP_CONFIG:
        panel_config_p = get_panel_config_table();
        if( data_len == sizeof( panel_config_p->smtp_config ) )
        {
            pi_memcpy( &panel_config_p->smtp_config, data, data_len );
            ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_SMTP_CONFIG_REQUEST, data, data_len );
            pi_log_d( "panel set smtp sender_addr:%s,server_addr:%s, server_port:%d, server_auth:%d, sec_mode:%d, username:%s, password:%s \n",
                        panel_config_p->smtp_config.sender_addr, panel_config_p->smtp_config.server_addr, panel_config_p->smtp_config.server_port,
                        panel_config_p->smtp_config.server_auth, panel_config_p->smtp_config.sec_mode, panel_config_p->smtp_config.username, panel_config_p->smtp_config.password
                        );
        }
        else
        {
            pi_log_e( "panel set smtp data error! size:%d\n", sizeof( panel_config_p->smtp_config ) );
        }
        break;
    case SETTING_CMD_SET_WHITELIST_SWITCH:
        pi_log_d( "panel request whiteList switch request:%d\n", *(uint32_t*)data );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WHITELIST_SWITCH_REQUEST, data, data_len );
        break;

    case SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_SCAN:
        pi_log_d( "panel request scan job control:%d\n", *(uint32_t*)data );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_SCAN_JOB_CONTROL_REQUEST, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_SCAN_JOB_CONTROL, data, data_len );
        break;
    case SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_PRINT:
        pi_log_d( "panel request print job control:%d\n", *(uint32_t*)data );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_PRINT_JOB_CONTROL_REQUEST, data, data_len );        
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_PRINT_JOB_CONTROL, data, data_len );
        break;
    case SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_COPY:
        pi_log_d( "panel request print job control:%d\n", *(uint32_t*)data );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_COPY_JOB_CONTROL_REQUEST, data, data_len );
        //需要推送至操作记录
        notify_security_operate( SECURITY_OPERATE_COPY_JOB_CONTROL, data, data_len );
        break;

    case SETTING_CMD_SET_JOB_ADVANCE_IMAGE:
        panel_set_setting_to_nvram_struct( data, data_len, cmd );
        break;

    case SETTING_CMD_SET_WIFI_WPS_REQUEST:
        pi_log_d( "panel request wifi wps:%d\n", *(uint32_t*)data );
        ret = pi_event_mgr_notify(panel_evt_client, EVT_TYPE_NET_WIFI_WPS_REQUEST, data, data_len );
        break;

    case SETTING_CMD_GET_UDISK_REMAIN:
        panel_get_udisk_remain();
        break;

#ifdef CONFIG_NET_PLOG
    case SETTING_CMD_MAINTENANCE_GET_LOG_SAVE_SWITCH:
        pi_log_d( "panel set log save switch:%d\n", *(uint32_t*)data );
        panel_set_setting_to_nvram_u32( data ,data_len, cmd );
        pi_plog_switch( *(PLOG_STATE_E*)data );
        break;
#endif

    default:

        break;
    }
    if(ret < 0)
    {
        pi_log_d("notify fail!\n");
    }
}

/**
 * @brief process resource cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: longxuan
 */
void resource_cmd_process( unsigned short cmd ,void *data ,int data_len )
{
    uint32_t data_buff = 0;

    switch( cmd )
    {
    case RESOURCE_CMD_FILE_ACCESS:
    case RESOURCE_CMD_FILE_OPEN:
    case RESOURCE_CMD_FILE_READ:
    case RESOURCE_CMD_FILE_WRITE:
    case RESOURCE_CMD_FILE_SEEK:
    case RESOURCE_CMD_FILE_CLOSE:
        panel_file_recv_result_proc( data, data_len );
        break;

    default:
        break;
    }
}

/**
 * @brief process other cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void other_cmd_process(unsigned short cmd ,void *data ,int data_len)
{
    int ret = 0;

    switch ( cmd )
    {
#if CONFIG_SDK_PEDK
        case OTHER_CMD_PEDK_TOUCH:
            pi_log_i("panel send touch to pedk app:%s,data_len:%d\n", (unsigned char *)data,  data_len );
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PANEL, MSG_PANEL_SUB_DRAW, 0, (unsigned char *)data, data_len);
            break;

        case OTHER_CMD_PEDK_START:
            pi_log_i("panel send touch to pedk app:%s,data_len:%d\n", (unsigned char *)data,  data_len );
            am_start_app( data );
            break;

        case OTHER_CMD_PEDK_KEY:
            pi_log_i("panel send key to pedk app:%s,data_len:%d\n", (unsigned char *)data,  data_len );
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PANEL, MSG_PANEL_SUB_CTRL_KEY, 0, (unsigned char *)data, data_len);
            break;

        case OTHER_CMD_PEDK_GET_BACK_TIMEOUT_TIME:
            pi_log_i("panel send timeout time to pedk app:%d\n", *(unsigned int*)data );
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_PANEL, MSG_PANEL_SUB_GET_BACK_TIMEOUT, 0, (unsigned char *)data, data_len);
            break;

#endif // CONFIG_SDK_PEDK
        default:
            break;
    }
}


/**
 * @}
 */

