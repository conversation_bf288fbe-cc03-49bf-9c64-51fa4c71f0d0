/*
 * @Author: your name
 * @Date: 2023-12-22 09:38:35
 * @LastEditTime: 2024-01-23 17:36:08
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \runtime\src\main.c
 */
#include <memory.h>

#include <quickjs.h>
#include <quickjs-libc.h>
#include <uv.h>
#include "manager/heartbeat.h"
#include "basic/log/log.h"
#include "manager/manager.h"
#include "trans/transmission.h"
#include "basic/config.h"
#include "runtime/modules/module_manager.h"

#include "basic/sys/sys.h"
#include <pthread.h>

//启动内部app
static void start_inner_app()
{
    //启动心跳
    heartbeat_init();
}

int main(int argc, char **argv)
{

    LOG_I("main", "start");
	
    if (argc < 1)
    {
        fprintf(stderr, "Usage: %s <filename>\n", argv[0]);
        return 1;
    }
	
	if (argc > 1)
    {
		int time = 0;
		sscanf(argv[1], "%d", &time);
		fprintf(stderr, "=========time: %d \n", time);
		sleep(time);
    }
	


    uint8_t buffer[BUFFER_MAX_LENGTH] = {0};
    uint16_t length = 0;
    int32_t ret;

    /* 初始化 */
    transport_init();//初始化传输模块
    modules_init_all();//初始化全部子模块
    /* 握手 */
    while(1){
        make_ping_pkt(buffer, &length);                     //制作ping包
        transport_send( buffer, length);                 //发送ping消息
        ret = transport_receive(buffer, &length, 1000);//等待接收，最多等1s，否则退出
        if(0 == ret){
            SHOW_MSG("Handshake OK\n");
            LOG_I("main","Handshake OK\n");
            break; //等到ping响应，退出握手，进入主循环
        }
    }

    //启动内部app
    start_inner_app();

    FORMAT pkt;
    /* 主循环 */
    while(1){
        //清缓存
        memset(buffer, 0, BUFFER_MAX_LENGTH);
        SHOW_MSG("Waiting to receive message :\n");

        /* 等待接收消息 */
        ret = transport_receive(buffer, &length, 0);//死等
        if(0 != ret){
            LOG_E("main","pkr receive error\n");
        }

        /* 进入格式转换 */
        ret = format_conversion_to_pkt(buffer, &pkt, length);
        if(0 != ret){
            LOG_E("main","format conversion error\n");
        }

        //进入消息动作
        ret = manager_execute(pkt);
        if(0 != ret){
            LOG_E("main","manager execute error\n");
        }
    }

    /*退出流程*/

    return 0;
}
