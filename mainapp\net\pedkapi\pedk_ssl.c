/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pedk_ssl.c
 * @addtogroup net
 * @{
 * @ssl netmodules
 * <AUTHOR>
 * @date 2024-6-11
 * @brief pedk ssl function for pedk
 */
#include "nettypes.h"
#include "pedk_ssl.h"
#include "pedk_mgr.h"
#include <sys/stat.h>
#include <sys/types.h>
#include <regex.h>

#include <openssl/x509.h>
#include <openssl/x509v3.h>
#include <openssl/pem.h>
#include <openssl/md5.h>

#include "netmisc.h"

#define SSL_CONNECT_BUF_MAX     1024
#define SSL_CONNECT_CLOSE_ON    1
#define SSL_CONNECT_CLOSE_OFF   0
#define PEDK_TLS_CERT_FILE      "/tmp/tlscert.pem"     ///< 证书私钥 + 签名

// 定义证书和私钥文件的保存路径
#define CERT_FILE_PATH "/pesf_data/custom_cert.pem"
#define KEY_FILE_PATH  "/pesf_data/custom_key.pem"
#define TEMP_KEY_FILE  "/tmp/tmp_key.pem"

static pthread_t                s_ssl_tid;
static PEDK_SSL_CONNECT_LIST_S* s_ssl_connect_head;
static char*                    s_ssl_recv_buf = NULL;
static int                      s_ssl_close_enable = 0;

PEDK_SSL_CONNECT_LIST_S* create_ssl_connect_list()
{
    NET_DEBUG("[pedkssl] create ssl connetc list ");
    PEDK_SSL_CONNECT_LIST_S *Head = (PEDK_SSL_CONNECT_LIST_S *)pi_zalloc(sizeof(PEDK_SSL_CONNECT_LIST_S));
    Head->port = 0;
    Head->ssl_sockfd = 0;
    Head->hostname[0] = 0;
    Head->con_ssl_ctx = NULL;
    Head->con_ssl = NULL;
    Head->next = NULL;

    return Head;
}

void add_ssl_connect(PEDK_SSL_CONNECT_LIST_S* head, int port, char* hostname, int sockfd, SSL_CTX* con_ssl_ctx, SSL* con_ssl)
{
    if ( head == NULL )
    {
        NET_WARN("[pedkssl] sslconnect is NULL, delete fail ");
        return;
    }
    NET_DEBUG("[pedkssl] start add sslconn ");
    PEDK_SSL_CONNECT_LIST_S* temp = (PEDK_SSL_CONNECT_LIST_S *)pi_zalloc(sizeof(PEDK_SSL_CONNECT_LIST_S));

    temp->port = port;
    temp->ssl_sockfd = sockfd;
    strcpy(temp->hostname,hostname);
    temp->con_ssl = con_ssl;
    temp->con_ssl_ctx = con_ssl_ctx;
    temp->next = NULL;
    temp->next = head->next;
    head->next = temp;
    NET_DEBUG("[pedkssl] add sslconn succcess ");

    while ( head->next != NULL )
    {
        head = head->next;
        NET_DEBUG("[pedkssl] head_port = %d ",head->port);
    }
}

int remove_ssl_connect(PEDK_SSL_CONNECT_LIST_S* head, int port, char* hostname)
{
    NET_DEBUG("[pedkssl] remove_port = %d ",port);
    if ( head == NULL )
    {
        NET_DEBUG("[pedkssl] sslconnect is NULL, delete fail ");
        return -1;
    }
    PEDK_SSL_CONNECT_LIST_S *pre = head;
    PEDK_SSL_CONNECT_LIST_S *cur = head->next;
    while ( cur != NULL )
    {
        if ( cur->port == port && strcmp(cur->hostname,hostname) == 0 )
        {
            cur = cur->next;
            free(pre->next);
            pre->next = cur;
            NET_DEBUG("[pedkssl] free pre_next success ");
            return 0;
        }
        else
        {
            pre = cur;
            cur = cur->next;
            NET_DEBUG("[pedkssl] enter remove_ssl_connect else ");
        }
    }
    NET_DEBUG("[pedkssl] remove sslconnect success ");

    return -1;
}

PEDK_SSL_CONNECT_LIST_S* seek_ssl_connect(int port, char* hostname)
{
    NET_DEBUG("[pedkssl] seek sslconn;port = %d;hostname = %s", port, hostname);
    if ( s_ssl_connect_head == NULL )
    {
        NET_DEBUG("[pedkssl] s_ssl_connect_head is NULL ");
        return NULL;
    }
    PEDK_SSL_CONNECT_LIST_S* seek_sslconn = s_ssl_connect_head;

    NET_DEBUG("[pedkssl] seek_sslconn_port = %d, seek_sslconn_hostname = %s ", seek_sslconn->port, seek_sslconn->hostname);
    if ( seek_sslconn->next == NULL )
    {
        NET_DEBUG("[pedkssl] seek_sslconn is NULL");
        return NULL;
    }
    while ( seek_sslconn->next != NULL )
    {
        NET_DEBUG("[pedkssl] sslconn_port = %d, sslconn_hostname = %s ", seek_sslconn->port, seek_sslconn->hostname);
        seek_sslconn = seek_sslconn->next;
        if ( seek_sslconn->port == port && strcmp(seek_sslconn->hostname, hostname) == 0 )
        {
            NET_DEBUG("[pedkssl] ssl_port = %d, ssl_hostname = %s ", seek_sslconn->port,seek_sslconn->hostname);
            return seek_sslconn;
        }
        else
        {
            NET_DEBUG("[pedkssl] No sslconnect ");
        }
    }

    return NULL;
}

static void* ssl_recv_thread(void* arg)
{
    PEDK_SSL_CONNECT_LIST_S* ssl_con;
    int len = 0;

    if (arg == NULL)
    {
        NET_ERROR("[pedkssl] Invalid argument");
        return NULL;
    }

    PEDK_SSL_CONNECT_DATA_S *seeksslconn = (PEDK_SSL_CONNECT_DATA_S *)arg;
    NET_DEBUG("[pedkssl] port = %d, hostname = %s ", seeksslconn->sslcon_port, seeksslconn->sslcon_hostname);

    ssl_con = seek_ssl_connect(seeksslconn->sslcon_port, seeksslconn->sslcon_hostname);
    if ( ssl_con == NULL )
    {
        NET_DEBUG("[pedkssl] ssl_con is NULL ");
        return NULL;
    }
    NET_DEBUG("[pedkssl] port = %d ; ssl_con_port = %d ", seeksslconn->sslcon_port, ssl_con->port);
    s_ssl_recv_buf = (char *)pi_zalloc(SSL_CONNECT_BUF_MAX+1);

    /* 接收消息 */
    memset(s_ssl_recv_buf, 0, SSL_CONNECT_BUF_MAX+1);
    NET_DEBUG("[pedkssl]s_ssl_recv_buf len = %d ", strlen(s_ssl_recv_buf));
    NET_DEBUG("[pedkssl]p = %p ", ssl_con->con_ssl);
    while ( 1 )
    {
        len = SSL_read(ssl_con->con_ssl, s_ssl_recv_buf, SSL_CONNECT_BUF_MAX);
        //NET_DEBUG("[pedkssl] read len:%d ",len);
        if ( len > 0 )
        {
            NET_DEBUG("[pedkssl]recv msg: %s success,len:%d ", s_ssl_recv_buf, len);
            pedk_mgr_send_msg_to_runenv(MSG_MODULE_NET, MSG_NET_SUB_SSL_RECV, 0, (unsigned char*)s_ssl_recv_buf, len+1);
            NET_DEBUG("[pedkssl]notify end ");
            memset(s_ssl_recv_buf, 0, SSL_CONNECT_BUF_MAX+1);
            //free(s_ssl_recv_buf);
        }
        else
        {
            //NET_DEBUG("[pedkssl]recv msg failed ");
            if ( s_ssl_close_enable == SSL_CONNECT_CLOSE_ON )
            {
                s_ssl_close_enable = SSL_CONNECT_CLOSE_OFF;
                NET_DEBUG("[pedkssl]sslclose end");
                //pthread_cancel(s_ssl_tid);
                //break;
            }
        }
    }
    free(ssl_con);

    return NULL;
}

static int ssl_recv_handle(PEDK_SSL_CONNECT_DATA_S* recvinfo)
{
    NET_DEBUG("[pedkssl] Create sslrecvThread ");
    pthread_create(&s_ssl_tid, NULL, ssl_recv_thread, recvinfo);
    NET_DEBUG("[pedkssl] pthread ID = %lu ", pthread_self());

    return 0;
}

const char* pedk_ssl_connect_close(PEDK_SSL_CONNECT_DATA_S* close_data)
{
    int ret;
    NET_DEBUG("[pedkssl] sslclose_port = %d ; sslclose_hostname = %s ", close_data->sslcon_port, close_data->sslcon_hostname);
    s_ssl_close_enable = SSL_CONNECT_CLOSE_ON;

    PEDK_SSL_CONNECT_LIST_S* ssl_con = seek_ssl_connect(close_data->sslcon_port, close_data->sslcon_hostname);
    if ( ssl_con == NULL )
    {
        NET_DEBUG("[pedkssl] No sslconnect  ");
        return "EXIT_FAILURE";
    }
    ret = pthread_cancel(s_ssl_tid);
    NET_DEBUG("[pedkssl] cancel thread ,ret = %d ", ret);
    if ( ssl_con->con_ssl )
    {
        NET_DEBUG("[pedkssl][pedk_ssl_close_connect]close and free ssl ;close sockfd:%d ", ssl_con->ssl_sockfd);
        SSL_shutdown(ssl_con->con_ssl);
        SSL_free(ssl_con->con_ssl);
        NET_DEBUG("[pedkssl] end close ");
    }
    if ( ssl_con->ssl_sockfd )
    {
        close(ssl_con->ssl_sockfd);
        NET_DEBUG("[pedkssl] end close sockfd ");
    }
    if ( ssl_con->con_ssl_ctx )
    {
        SSL_CTX_free(ssl_con->con_ssl_ctx);
        NET_DEBUG("[pedkssl] end free con_ssl_ctx ");
    }
    if ( s_ssl_recv_buf )
    {
        free(s_ssl_recv_buf);
        NET_DEBUG("[pedkssl] end free s_ssl_recv_buf ");
    }
    remove_ssl_connect(s_ssl_connect_head, close_data->sslcon_port, close_data->sslcon_hostname);
    NET_DEBUG("[pedkssl] remove sslconn ");

    return "EXIT_SUCCESS";
}

int pedk_ssl_connect_recv(PEDK_SSL_CONNECT_DATA_S* recv_data)
{
    int ret;

    NET_DEBUG("[pedkssl] sslrecv_port = %d, sslrecv_hostname = %s ", recv_data->sslcon_port, recv_data->sslcon_hostname);
    PEDK_SSL_CONNECT_LIST_S *ssl_con = seek_ssl_connect(recv_data->sslcon_port, recv_data->sslcon_hostname);
    if ( ssl_con == NULL )
    {
        NET_DEBUG("[pedkssl] No sslconnect");
        return 0;
    }
    ret = ssl_recv_handle(recv_data);
    NET_DEBUG("[pedkssl] ret = %d ", ret);

    return 1;
}

const char* pedk_ssl_connect_send(PEDK_SSL_CONNECT_DATA_S* send_data)
{
    char buf[SSL_CONNECT_BUF_MAX+1];
    int  len = 0;

    NET_DEBUG("[pedkssl]send data:%s ; port = %d ; hostname = %s ", send_data->ssl_data, send_data->sslcon_port, send_data->sslcon_hostname);
    memset(buf, 0, sizeof(buf));
    strcpy(buf, send_data->ssl_data);

    PEDK_SSL_CONNECT_LIST_S *ssl_con = seek_ssl_connect(send_data->sslcon_port, send_data->sslcon_hostname);

    /* 发送数据 */
    if ( ssl_con == NULL )
    {
        NET_DEBUG("[pedkssl]No sslconnect  ");
        return "EXIT_FAILURE";
    }
    len = SSL_write(ssl_con->con_ssl,buf, strlen(buf));
    if ( len < 0 )
    {
        NET_DEBUG("[pedkssl]No data,send msg failed ");
        return "EXIT_FAILURE";
    }
    else
    {
        NET_DEBUG("[pedkssl]send msg:%s success,len:%d ", buf,len);
    }

    return "EXIT_SUCCESS";
}

const char* pedk_ssl_create_connection(PEDK_SSL_CONNECT_INFO_S* connect_info)
{
    const SSL_METHOD* meth;
    SSL_CTX*          s_ssl_ctx = NULL;
    SSL*              ssl = NULL;
    X509*             server_cert;
    char              errbuf[512];
    int               sslerr;
    static int        sslsockfd;

    NET_DEBUG("[pedkssl] [start ssl connect]hostname = %s, port = %d, keypath = %s, certpath = %s, password = %s ",
        connect_info->hostname, connect_info->port, connect_info->keypath, connect_info->certpath, connect_info->password);

    /* SSL库初始化 */
    SSL_library_init();
    SSL_load_error_strings();
    ERR_load_BIO_strings();
    OpenSSL_add_all_algorithms();

    /* 设置client端使用的SSL版本 */
    meth = SSLv23_client_method();

    /* 创建一个SSL上下文环境 */
    s_ssl_ctx   = SSL_CTX_new((SSL_METHOD *)meth);
    if ( s_ssl_ctx == NULL  )
    {
        NET_ERROR("[pedkssl]Can't create SSL context");
    }
    NET_DEBUG("[pedkssl]SSL version: %s ", SSLeay_version(SSLEAY_VERSION));

    /* 设置信任根证书 */
    if (!(SSL_CTX_load_verify_locations(s_ssl_ctx, connect_info->certpath, NULL)))
    {
        NET_ERROR("[pedkssl]Can't read CA list");
    }
    else
    {
        NET_DEBUG("[pedkssl]read CA success ");
    }
    /* 设置证书文件口令 */
    SSL_CTX_set_default_passwd_cb_userdata(s_ssl_ctx, connect_info->password);
    //加载证书
    if ( SSL_CTX_use_certificate_file(s_ssl_ctx, connect_info->certpath, SSL_FILETYPE_PEM) <= 0 )
    {
        NET_ERROR("[pedkssl]Can't load certificate");
    }
    else
    {
        NET_DEBUG("[pedkssl]load certificate success ");
    }
    /* 载入私钥 */
    if ( SSL_CTX_use_PrivateKey_file(s_ssl_ctx, connect_info->keypath, SSL_FILETYPE_PEM) <= 0 )
    {
        NET_ERROR("[pedkssl]Can't load PrivateKey");
    }
    else
    {
        NET_DEBUG("[pedkssl]load PrivateKey success ");
    }
    /* 检查证书与私钥是否配对 */
    if ( !SSL_CTX_check_private_key(s_ssl_ctx) )
    {
        NET_ERROR("[pedkssl]Check private_key error");
    }
    else
    {
        NET_DEBUG("[pedkssl]Check private_key success ");
    }

    /* 创建Socket用于通信 */
    //int sslsockfd;
    struct sockaddr_in s_addr;
    if ( (sslsockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0 )
    {
        NET_ERROR("[pedkssl]Socket error");
        return "EXIT_FAILURE";
    }
    bzero(&s_addr, sizeof(s_addr));
    s_addr.sin_family = AF_INET;
    s_addr.sin_addr.s_addr = inet_addr(connect_info->hostname);
    s_addr.sin_port =  htons(connect_info->port);

    if ( connect(sslsockfd, (struct sockaddr*)&s_addr, sizeof(s_addr)) != 0 )
    {
        NET_ERROR("[pedkssl]Connect error");
        return "EXIT_FAILURE";
    }
    else
    {
        NET_DEBUG("[pedkssl]TCP server connected! ");
    }

    /* TCP连接已建立，建立SSL */
    ssl = SSL_new(s_ssl_ctx);
    NET_DEBUG("[pedkssl]p = %p ",ssl);

    SSL_set_fd(ssl, sslsockfd);
    if ( SSL_connect(ssl) == -1 )
    {
        NET_ERROR("[pedkssl]ssl_connect error");
        return "EXIT_FAILURE";
    }
    else
    {
        NET_DEBUG("[pedkssl]connect success!! ");
    }

    /* 得到服务端证书 */
    char* info;

    server_cert = SSL_get_peer_certificate(ssl);
    if ( server_cert != NULL )
    {
        NET_DEBUG("[pedkssl] certificate INFO ");

        info = X509_NAME_oneline(X509_get_subject_name(server_cert), NULL, 0);
        NET_DEBUG("[pedkssl] [server_cert]subject_name: %s ", info);
        free(info);
        info = X509_NAME_oneline(X509_get_issuer_name(server_cert), NULL, 0);
        NET_DEBUG("[pedkssl] [server_cert]subject_name: %s ", info);
        free(info);
        X509_free(server_cert);
    }
    else
    {
        NET_DEBUG("[pedkssl] NO certificate INFO ");
    }

    /* 验证证书 */
    if ( SSL_get_verify_result(ssl) != X509_V_OK )
    {
        sslerr = ERR_get_error();
        NET_INFO("SSL Certificate fail to verify: %s ", ERR_error_string(sslerr, errbuf));
    }

    if ( s_ssl_connect_head == NULL )
    {
        s_ssl_connect_head = create_ssl_connect_list();
        NET_DEBUG("[pedkssl] create s_ssl_connect_head ");
    }

    add_ssl_connect(s_ssl_connect_head,connect_info->port, connect_info->hostname, sslsockfd, s_ssl_ctx, ssl);
    if ( s_ssl_connect_head == NULL )
    {
        NET_DEBUG("[pedkssl] s_ssl_connect_head is NULL ");
    }

    return "EXIT_SUCCESS";
}

const char* pedk_ssl_load_certificate(const char* keypath, const char* certpath, char* password)
{
    char        prikey_path[CERT_PATH_LEN] = {0};
    char        cert_path[CERT_PATH_LEN] = {0};

    RETURN_VAL_IF((keypath == NULL) || (certpath == NULL) || (password == NULL), NET_WARN, "EXIT_FAILURE");

    NET_DEBUG("[pedkssl] keypath = %s,certpath = %s, password = %s ",keypath, certpath, password);

    snprintf(prikey_path, CERT_PATH_LEN, "/tmp/%s", keypath);
    snprintf(cert_path, CERT_PATH_LEN, "/tmp/%s", certpath);

    remove(PEDK_TLS_CERT_FILE);

    RETURN_VAL_IF(copy_file(PEDK_TLS_CERT_FILE, prikey_path, "w+")  != 0, NET_WARN, "EXIT_FAILURE");
    RETURN_VAL_IF(copy_file(PEDK_TLS_CERT_FILE, cert_path, "a+")  != 0, NET_WARN, "EXIT_FAILURE");

    if ( nettls_install_cert(PEDK_TLS_CERT_FILE, password) == 0 )
    {
        return "EXIT_SUCCESS";
    }

    return "EXIT_FAILURE";
}

const char* pedk_ssl_make_certificate(TLS_CERT_CONF_S* info)
{
    RETURN_VAL_IF(info == NULL, NET_WARN, "EXIT_FAILURE");

    NET_DEBUG("[pedkssl] type = %d, length = %d, days = %d, psd = %s; [pedkssl] pub_keypath = %s, keypath = %s, certpath = %s",
            info->type, info->key_length, info->days, info->password, info->pub_key_path, info->key_path, info->cert_path);

    if ( nettls_generate_cert(info) == 0 )
    {
        return "EXIT_SUCCESS";
    }

    return "EXIT_FAILURE";
}


// 写入证书文件
int pedk_ssl_write_certificate(const char *cert_content)
{
    FILE *cert_file = fopen(CERT_FILE_PATH, "w");
    if (!cert_file) {
        NET_DEBUG("Failed to open certificate file");
        return -1;
    }
    if (fputs(cert_content, cert_file) == EOF) {
        NET_DEBUG("Failed to write certificate content");
        fclose(cert_file);
        return -1;
    }
    fclose(cert_file);
    printf("Certificate written to %s\n", CERT_FILE_PATH);
    return 0;
}

// 检测私钥格式
static const char* pedk_ssl_detect_key_format(const char* key_content)
{
    if (strstr(key_content, "-----BEGIN RSA PRIVATE KEY-----"))
    {
        return "RSA";
    }
    else if (strstr(key_content, "-----BEGIN ENCRYPTED PRIVATE KEY-----"))
    {
        return "PKCS8";
    }
    else
    {
        return NULL; // 未知格式
    }
}

// 写入私钥文件
int pedk_ssl_write_private_key(const char *key_content, const char *key_password)
{
    printf("%d [pedkssl] key_content %s,key_password %s\n", __LINE__,key_content,key_password);
    // 检测私钥格式
    const char* key_format = pedk_ssl_detect_key_format(key_content);
    if (!key_format)
    {
        NET_DEBUG("Error: Unsupported or unrecognized key format.\n");
        return -1;
    }
    printf("%d [pedkssl] write_private_key\n", __LINE__);

    // 将私钥内容写入临时文件
    FILE* temp_file = fopen(TEMP_KEY_FILE, "w");
    if (!temp_file)
    {
        NET_DEBUG("Error: Failed to create temp key file");
        return -1;
    }
    printf("%d [pedkssl] write_private_key\n", __LINE__);
    if (fprintf(temp_file, "%s", key_content) < 0)
    {
        NET_DEBUG("Error: Failed to write to temp key file");
        fclose(temp_file);
        unlink(TEMP_KEY_FILE);
        return -1;
    }
    fclose(temp_file);
    printf("%d [pedkssl] write_private_key\n", __LINE__);

    // 构建 OpenSSL 命令
    char command[512];
    if (strcmp(key_format, "RSA") == 0) {
        snprintf(command, sizeof(command),
                 "openssl rsa -in %s -passin pass:%s -noout 2>/dev/null", TEMP_KEY_FILE, key_password);
    } else if (strcmp(key_format, "PKCS8") == 0) {
        snprintf(command, sizeof(command),
                 "openssl pkey -in %s -passin pass:%s -noout 2>/dev/null", TEMP_KEY_FILE, key_password);
    }

    // 运行命令验证密码
    int result = system(command);
    if (result != 0) {
        fprintf(stderr, "Error: Invalid password or unsupported decryption method.\n");
        unlink(TEMP_KEY_FILE); // 删除临时文件
        return -1;
    }

    printf("Debug: Password verified successfully.\n");

    // 验证成功，将原始私钥写入输出文件
    FILE* output_file = fopen(KEY_FILE_PATH, "w");
    if (!output_file)
    {
        NET_DEBUG("Error: Failed to create output key file");
        unlink(TEMP_KEY_FILE); // 删除临时文件
        return -1;
    }
    if (fprintf(output_file, "%s", key_content) < 0)
    {
        NET_DEBUG("Error: Failed to write to output key file");
        fclose(output_file);
        unlink(TEMP_KEY_FILE);
        return -1;
    }
    fclose(output_file);

    printf("Private key successfully written to %s\n", KEY_FILE_PATH);

    // 删除临时文件
    unlink(TEMP_KEY_FILE);

    return 0;

}

/**
 *@}
 */
