#ifndef __PARSER_IPS_INTERFACE_H__
#define __PARSER_IPS_INTERFACE_H__

#include "ips/ips_interation_public_data.h"

/**
 * @brief get system status for mono
 * @param[in] ips_render_mode 0: color 1:mono
 * @param[out] NA/
 * @return unsigned long
 * @retval 0:base on PC driver
 * @retval 1:mono
 * <AUTHOR>
 * @date 2023-12-05
 * @note  N/A
 */
unsigned long print_ips_get_system_status_for_mono(int32_t ips_render_mode);

/**
 * @brief app get machine speed
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_param_get_machine_speed( void );

/**
 * @brief app get machine color
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_param_get_machine_color( void );

/**
 * @brief the finisher is set ?
 * @param[in]  void
 * @param[out] pcValue 1-finisher install 0-finisher no install
 * @return uint32_t
 * @retval 1 - finisher install
 * @retval 0 - finisher no install
 * <AUTHOR>
 * @date 2024-04-15
 * @note  N/A
 */
uint32_t print_ips_config_get_finisher_install(void);

/**
 * @brief print_ips_interation_udisk_paper_size_valid_check
 * @param[in] ips_paper_size
 * @param[out] N/A
 * @return int8_t
 * @retval 0: support
 * @retval -1: no support
 * <AUTHOR>
 * @date 2024-01-08
 * @note  N/A
 */
int32_t print_ips_udisk_paper_size_valid_check(const uint32_t ips_paper_size);

/**
 * @brief app get tray install status
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0 - the tray uninstall
 * @retval 1 - the tray install
 * <AUTHOR>
 * @date 2024-04-16
 * @note  N/A
 */
int32_t print_ips_param_get_tray_install(const IPS_INPUT_TRAY_E ips_input_tray);

/**
 * @brief app get tray_in empty status
 * @param[in] void
 * @param[out] N/A
 * @return uint8_t
 * @retval  0 - empty
 * @retval  1 - not empty
 * <AUTHOR>
 * @date 2024-04-16
 * @note  N/A
 */
uint32_t print_ips_param_get_tray_in_empty_status(const IPS_INPUT_TRAY_E ips_input_tray);

/**
 * @brief get papse size by tray_in
 * @param[in]  ips_input_tray
 * @param[out] media_size_ptr
 * @param[out] custom_type
 * @param[out] width
 * @param[out] height
 * @return uint8_t
 * @retval 0 success
 * @retval 1 failure
 * <AUTHOR>
 * @date 2024-04-17
 * @note  N/A
 */
uint32_t print_ips_param_ips_get_paper_size(const IPS_INPUT_TRAY_E ips_input_tray,
                            IPS_MEDIA_SIZE_E *media_size_ptr, IPS_PRINT_CUSTOM_TYPE_E* custom_type,uint32_t *width, uint32_t *height);

/**
 * @brief get paper type
 * @param[in]  ips_input_tray ips_tray_input
 * @param[in]  ips_input_tray ips_paper_type
 * @param[out]   N/A
 * @return int32_t
 * @retval N/A
 * <AUTHOR>
 * @date 2024-05-16
 * @note N/A
 */
int32_t print_ips_param_pass_tray_get_paper_type(uint32_t ips_tray_input, uint32_t *ips_paper_type);

/**
 * @brief it is the end of job
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_end_of_job(void);

/**
 * @brief if it is parsing
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_ips_in_use(void);

/**
 * @brief ips stop read data,call it
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_stop_read_data(void);

/**
 * @brief function:cross shift,stop read data
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_stop_file_read_data(void);

/**
 * @brief function:cross shift,get prn readable flag
 * @param[in] void
 * @param[out] N/A
 * @return read enable flag
 * @retval 0 disable
 * @retval 1 enable
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_get_prn_read_enable_flag(void);

/**
 * @brief get job job id
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval 0: without job id
 * @retval >0: job id
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
uint32_t print_ips_param_get_job_id_value(void);

/**
 * @brief ips eoj,if ips is timeout,it will call it
 * @param[in] job_number
 * @param[out] N/A
 * @return  int
 * @retval  0 - success
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_post_job_end_msg(int32_t job_number);

#if 0 // GIO共享内存接口读取prn数据
int32_t print_ips_io_read_data(char *buffer, int32_t bufsize);
#endif

/**
 * @brief ips eoj,if ips is timeout,it will call it
 * @param[in] name
 * @param[in] bytes
 * @param[in] job_id
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_post_job_pipelint_name(char *name, int32_t bytes, uint32_t job_id);

/**
 * @brief  get tray_in and edge if job start or paper size changes
 * @param[in] ips_media_size
 * @param[in] ips_media_type
 * @param[in] ips_input_tray_original
 * @param[in] print_mode
 * @param[in] ips_bookbinding
 * @param[out] edge
 * @param[out] ips_input_tray_in
 * @return char
 * @retval 0: success
 * @retval -1: failure
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_get_tray_in_and_edge(const IPS_MEDIA_SIZE_E ips_media_size, const IPS_MEDIA_TYPE_E ips_media_type,
                                            const IPS_INPUT_TRAY_E ips_input_tray_original, IPS_EDGE_E *edge, IPS_INPUT_TRAY_E *ips_input_tray_in,
                                            const IPS_PRINT_MODE_E print_mode, const IPS_BOOKBINDING_S ips_bookbinding);

/**
 * @brief it is not used,just the api that ips use
 * @param[in] ips_media_size
 * @param[out] N/A
 * @return unsigned long
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
unsigned long print_ips_get_system_support_media_size(int32_t ips_media_size);

/**
 * @brief the parse timeout alue
 * @param[in] N/A
 * @param[out] timeout
 * @return  0 -success
 * @retval OM_ERROR_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
OM_ERROR_E print_ips_inf_get_timeout_value_int(int32_t *timeout);

#if 0  // 共享内存放入band数据
PAGE_OUTPUT_QUEUE_STATUS_E print_ips_add_band_to_page_output_queue_page(uint32_t plane, char *page_data, uint32_t num_bytes);
#endif

/**
 * @brief IPS band create over ，call it \n
        it needs to configure page param，confirm next page is normal \n
        and then send message to the handle module to tell it that render completely
 * @param[in]  old_set - POQ set point
 * @param[out]  N/A
 * @return PAGE_OUTPUT_QUEUE_STATUS_E
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
PAGE_OUTPUT_QUEUE_STATUS_E print_ips_finished_adding_to_page_output_queue_page(void);

/**
 * @brief   page output queue - abbreviation POQ， \n
            POQ of one set \n
            it create POQ SET, ips call it \n
            we need to save this param
 * @param[in]  copies           - POQ set copies
 * @param[in]  collate          -
 * @param[in]  producing_app    - data produce ,it must be ips
 * @param[in]  incoming_format  - band format
 * @param[in]  storage_format   - data save format
 * @param[in]  freeFunc         - release function,it can be NULL
 * @param[in]  callback         - callback function，it will call in the time of POQ set submit，or excepions happens
 * @param[out]  N/A
 * @return uint32_t
 * @retval 1:sucess
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_create_page_output_queue_set(uint32_t  copies, int32_t collate, PAGE_OUTPUT_QUEUE_APP_E producing_app,
                                                   PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E incoming_format, PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E storage_format);

/**
 * @brief  output page data of POQ SET , IPS call \n
          ips will out one page of poq set
 * @param[in]  desc
 * @param[in]  page_number
 * @param[out]  N/A
 * @return uint32_t
 * @retval 1:success
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_add_page_to_page_output_queue_set(PAGE_OUTPUT_QUEUE_PAGE_DESC_S* desc, uint32_t page_number);

/**
 * @brief  completes adding POQ SET ，IPS call this
 * @param[in] void
 * @param[out]  N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
void print_ips_finished_adding_pages_to_page_output_queue_set(void);

/**
 * @brief get ips lib param
 * @param[in] void
 * @param[out] N/A
 * @return IPS_LIB_PARAM_S
 * @retval IPS_LIB_PARAM_S
 * <AUTHOR>
 * @date 2023-12-05
 * @note  N/A
 */
const IPS_LIB_PARAM_S print_ips_param_get_ips_lib_param( void );

/**
 * @brief get job type
 * @param[in] void
 * @param[out] N/A
 * @return unsigned long
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
unsigned long print_ips_get_job_setting_type(void); //IPS_GetJobSettingType

/**
 * @brief set job job id
 * @param[in] timeout_flag
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-01-09
 * @note N/A
 */
void print_ips_set_timeout_flag (uint32_t timeout_flag);

/**
 * @brief get job job id
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: without job id
 * @retval >0: job id
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
uint32_t print_ips_get_timeout_flag(void);

/**
 * @brief if mfp ready,ips starts
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval 0:not ready
 * @retval 1:ready
 * <AUTHOR>
 * @date 2024-12-11
 * @note  N/A
 */
uint32_t print_ips_get_mfp_ready(void);

/**
 * @brief get memobj_id
 * @param[in]  void
 * @param[out] N/A
 * @return int32_t
 * @retval memobj_id
 * <AUTHOR>
 * @date 2025-1-11
 * @note  N/A
 */
int32_t print_ips_get_memobj_id(void);

/**
 * @brief plane data
 * @param[in] band_id
 * @param[out]  N/A
 * @return   PAGE_OUTPUT_QUEUE_STATUS_E
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-1-11
 * @note  N/A
 */
int32_t print_ips_add_band_to_page_output_queue_page_new(uint32_t bind_id);

/**
 * @brief DO exception
 * @param[in] count
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-07-11
 * @note  N/A
 */
void print_ips_set_DO_encrypt_warn(int32_t count); //void IPSioSetDOEncryptWarn(int32_t count)

/**
 * @brief DO unsupport file foramt
 * @param[in] count
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_DO_unsupported_file_format(int32_t count); // IPSioSetDOUnsupportedFileFormat

/**
 * @brief This flag is only callable from IPS and inferno.
 * @param[in] func
 * @param[out] N/A
 * @return returns IPS_CANCEL_ON or IPS_CANCEL_OFF (1 or 0)
 * @retval IPS_CANCEL_FLAG_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
IPS_CANCEL_FLAG_E print_ips_inf_get_cancel_flag(char* func);

/**
 * @brief not used,just because of ips calls
 * @param[in] N/A
 * @param[out] N/A
 * @return int32
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_PJLPS_data_stream(void);

/**
 * @brief get USB dummy
 * @param[in] void
 * @param[out] N/A
 * @return  int
 * @retval 0 - FALSE
 * @retval 1 - TRUE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_get_USB_dummy(void);

/**
 * @brief IPS EOJ  IPS call in the time when reading data timeout
 * @param[in] type  0:update flag，don't upload status。1:update flag，upload timeout status。
 * @param[out] N/A
 * @return  void
 * @retval  N/A
 * <AUTHOR>
 * @date 2024-05-28
 * @note  N/A
 */
void print_ips_io_input_timeout(int32_t type);

/**
 * @brief get qio error
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_get_qio_error(void);

/**
 * @brief io always in sniffer
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_always_in_sniffer(void);

/**
 * @brief IPS rewind data to gqio
 * @param[in]  char *buffer    - rewind data
 * @param[in]  int bufsize     - rewind data len
 * @param[in]  flag
 * @param[out] N/A
 * @return  int32_t
 * @retval -1 falure
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_rewind_data(char *buffer, int32_t bytes, int32_t flag);

/**
 * @brief set pdf font missing warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_pdf_font_missing_warn(void);

/**
 * @brief set pdf font encrypt warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_pdf_encrypt_warn(void);

/**
 * @brief set pdf font invalid warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_pdf_font_invalid_warn(void);

/**
 * @brief is air print pdf job
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_air_print_pdf_job(void);

/**
 * @brief airprint pdf job read over
 * @param[in] void
 * @param[out] N/A
 * @return int32_T
 * @retval TURE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_air_print_pdf_read_over(void);

/**
 * @brief get udisk param/airprint param
 * @param[in] void
 * @param[out] N/A
 * @return IPS_UDISK_PARAM_S
 * @retval IPS_PRINT_PARAM_S
 * <AUTHOR>
 * @date 2024-01-08
 * @note  N/A
 */
IPS_PRINT_PARAM_S print_ips_get_udisk_param(void);

/**
 * @brief get custom paper size tabel
 * @param[in] void
 * @param[out] N/A
 * @return IPS_CUSTOM_TYPE_TABLE_S
 * @retval IPS_CUSTOM_TYPE_TABLE_S
 * <AUTHOR>
 * @date 2024-11-28
 * @note  N/A
 */
IPS_CUSTOM_TYPE_TABLE_S print_ips_get_custom_size_table( void );

/**
 * @brief IPS read data form file gqio
 * @param[in] char *buffer
 * @param[in] int bufsize
 * @param[out] N/A
 * @return int
 * @retval -1 : no data
 * @retval >0 : read data size
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int print_ips_io_file_read_data(char *buffer, int bufsize);

/**
 * @brief get page number of saved prn
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval the first print page numbers
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int print_ips_get_first_pass_prn_pages(void);

/**
 * @brief get sample param
 * @param[in] N/A
 * @param[out] N/A
 * @return IPS_SAMPLE_PARAM_S
 * @retval sample param
 * <AUTHOR>
 * @date 2024-05-13
 * @note N/A
 */
IPS_SAMPLE_PARAM_S print_ips_param_get_sample_param(void);

/**
 * @brief get job suspend status
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: normal
 * @retval >0: suspend
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
int32_t print_ips_param_get_job_suspend_status(void);

/**
 * @brief set pdf encrypt warn
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_encrypt_warn(void);

/**
 * @brief set file large warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_large_warn(void);

/**
 * @brief set font missing warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_font_missing_warn(void);

/**
 * @brief set file unsupport warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_unsupport_warn(void);

/**
 * @brief enable parse again flag
 * @param[in] flag
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
void print_ips_set_parse_again_flag(int flag);

/**
 * @brief get parser again flag
 * @param[in] void
 * @param[out] N/A
 * @return  int
 * @retval  int
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
int print_ips_get_parse_again_flag(void);

/**
 * @brief answer suspend ack by ips
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-02-21
 * @note N/A
 */
void print_ips_answer_suspend_ack_by_ips(void);

/**
 * @brief imagemem release
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
void print_ips_set_imagemem_release(void);

/**
 * @brief init cbinder service
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
int32_t print_ips_init(void);

/**
 * @brief stop cbinder service
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
void print_ips_deinit(void);

#endif /* __PARSER_IPS_INTERFACE_H__ */
