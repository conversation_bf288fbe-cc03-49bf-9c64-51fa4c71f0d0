/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_mutex.c
 * @addtogroup threads
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief Pantum mutex API functions
 */
#include "pol/pol_threads.h"
#include "pol/pol_log.h"

#include "pol_threads_private.h"
#include "pol_inner.h"

#define get_mutex(m)    (POL_MUTEX_S *)(((POL_MUTEX_S *)m >= &s_mutex_pool[0] && (POL_MUTEX_S *)m <= &s_mutex_pool[PI_NUM_MTXS - 1]) ? m : NULL)

typedef struct pol_mutex
{
    void*   os_mtx;
}
POL_MUTEX_S;

static POL_MUTEX_S  s_mutex_pool[PI_NUM_MTXS];
static void*        s_mutex_base = NULL;

PI_MUTEX_T pi_mutex_create_ent(const char* caller)
{
    POL_MUTEX_S* pmtx = NULL;

    RETURN_VAL_SHOW_CALLER_IF(s_mutex_base == NULL, pi_log_e, caller, INVALIDMTX);

    pol_mutex_lock(s_mutex_base);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_mutex_pool); ++i )
    {
        if ( s_mutex_pool[i].os_mtx == NULL )
        {
            RETURN_VAL_SHOW_CALLER_IF((s_mutex_pool[i].os_mtx = pol_mutex_create()) == NULL, pi_log_e, caller, INVALIDMTX);

            pmtx = &s_mutex_pool[i];
            break;
        }
    }
    pol_mutex_unlock(s_mutex_base);

    if ( pmtx == NULL )
    {
        pi_log_e("[CALL:%s]mutex pool full!!!\n", caller);
    }
    return (PI_MUTEX_T)pmtx;
}

int32_t pi_mutex_destroy_ent(PI_MUTEX_T mtx, const char* caller)
{
    POL_MUTEX_S* pmtx = get_mutex(mtx);

    RETURN_VAL_SHOW_CALLER_IF(s_mutex_base == NULL, pi_log_e, caller, -1);

    pol_mutex_lock(s_mutex_base);
    if ( pmtx == NULL || pmtx->os_mtx == NULL )
    {
        pi_log_e("[CALL:%s] no such mutex(%p)!!!\n", caller, mtx);
    }
    else
    {
        pol_mutex_destroy(pmtx->os_mtx);
        pmtx->os_mtx = NULL;
    }
    pol_mutex_unlock(s_mutex_base);

    return 0;
}

int32_t pi_mutex_lock_ent(PI_MUTEX_T mtx, const char* caller)
{
    POL_MUTEX_S* pmtx = get_mutex(mtx);
    int32_t      ret;

    RETURN_VAL_SHOW_CALLER_IF(pmtx == NULL || pmtx->os_mtx == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_mutex_base == NULL, pi_log_e, caller, -1);

    ret = pol_mutex_lock(pmtx->os_mtx);
    if ( ret != 0 )
    {
        pi_log_e("[CALL:%s] pol_mutex_lock failed(%d): %d<%s>\n", caller, ret, errno, strerror(errno));
    }
    return ret;
}

int32_t pi_mutex_timedlock_ent(PI_MUTEX_T mtx, int32_t secs, int32_t usecs, const char* caller)
{
    POL_MUTEX_S* pmtx = get_mutex(mtx);
    int32_t      ret;

    RETURN_VAL_SHOW_CALLER_IF(pmtx == NULL || pmtx->os_mtx == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_mutex_base == NULL, pi_log_e, caller, -1);

    if ( secs > 0 || (secs == 0 && usecs >= 0) )
    {
        ret = pol_mutex_timedlock(pmtx->os_mtx, secs, usecs);
    }
    else
    {
        ret = pol_mutex_lock(pmtx->os_mtx);
    }

    if ( ret != 0 )
    {
        pi_log_e("[CALL:%s] pol_mutex_timedlock(%d, %d) failed(%d): %d<%s>\n", caller, secs, usecs, ret, errno, strerror(errno));
    }
    return ret;
}

int32_t pi_mutex_unlock_ent(PI_MUTEX_T mtx, const char* caller)
{
    POL_MUTEX_S* pmtx = get_mutex(mtx);
    int32_t      ret;

    RETURN_VAL_SHOW_CALLER_IF(pmtx == NULL || pmtx->os_mtx == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_mutex_base == NULL, pi_log_e, caller, -1);

    ret = pol_mutex_unlock(pmtx->os_mtx);
    if ( ret != 0 )
    {
        pi_log_e("[CALL:%s] pol_mutex_unlock failed(%d): %d<%s>\n", caller, ret, errno, strerror(errno));
    }
    return ret;
}

int32_t pi_mutex_prolog(void)
{
    RETURN_VAL_IF(s_mutex_base != NULL, pi_log_e, 0); /* 以互斥锁作是否初始化的标识，避免同一进程中多次调用重复初始化 */
    RETURN_VAL_IF((s_mutex_base = pol_mutex_create()) == NULL, pi_log_e, -1);

    pol_mutex_lock(s_mutex_base);
    memset(&s_mutex_pool, 0, sizeof(s_mutex_pool));
    pol_mutex_unlock(s_mutex_base);

    return 0;
}

void pi_mutex_epilog(void)
{
    RETURN_IF(s_mutex_base == NULL, pi_log_e);

    pol_mutex_lock(s_mutex_base);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_mutex_pool); ++i )
    {
        if ( s_mutex_pool[i].os_mtx != NULL )
        {
            pol_mutex_destroy(s_mutex_pool[i].os_mtx);
        }
        memset(&s_mutex_pool, 0, sizeof(s_mutex_pool));
    }
    pol_mutex_unlock(s_mutex_base);

    pol_mutex_destroy(s_mutex_base);
    s_mutex_base = NULL;
}
/**
 *@}
 */
