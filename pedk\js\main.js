// import * as std from "std";
// import * as os from "os";
// 
// import { getPrinterEvent, send2printer, threadInit, send2printercopy } from "/lib/libpesf.so";

/*
显示就绪界面: 
{"UI_SCR_LAYOUT_T":[{"type":"label","name":"uiLauncherLabel","pos_x":10,"pos_y":10,"width":440,"height":30,"text":"MPS HOME"},{"type":"button","name":"uiLauncherEntryCopy","pos_x":40,"pos_y":40,"width":40,"height":30,"text":"Copy"}],"msgType":"show_ui"}
点击复印按钮后返回
{"type":"click_button", "button_name":"uiLauncherEntryCopy"}

显示复印中: 
{"UI_SCR_LAYOUT_T":[{"type":"label","name":"uiLauncherLabel","pos_x":10,"pos_y":10,"width":440,"height":30,"text":"Copying"},{"type":"button","name":"uiCancelCopy","pos_x":40,"pos_y":40,"width":40,"height":30,"text":"Cancel"}],"msgType":"show_ui"}
send2printer: {"msgType":"func_copy"}

点击取消按钮
{"type":"click_button", "button_name":"uiCancelCopy"}

var test_count = 0;
function send2printer(msg) {
    print("send2printer: " + msg);
}

function getPrinterEvent() 
{
    var m = '{}';
    switch (test_count)
    {
        case 0:
            m =  '{"type":"click_button", "button_name":"uiLauncherEntryCopy"}';
            break;
        case 1:
            m =  '{"type":"click_button", "button_name":"uiCancelCopy"}';
            break;
        case 2:
            m =  '{"type":"click_button", "button_name":"OK"}';
            break;
            
    }
    ++test_count;
    return m;
}

/*----------------*/

function send2panel(msg) {
    msg.msgType = "show_ui";
    //print("send2panel: " + msg);
    send2printer(JSON.stringify(msg));
}

function uiInitLauncher()
{
    const msg = {
        UI_SCR_LAYOUT_T: [
        {
            type: "label",
            name: "uiLauncherLabel",
            pos_x: 10,
            pos_y: 10,
            width: 440,
            height: 30,
            text: "MPS HOME"
        },
        {
            type: "button",
            name: "uiLauncherEntryCopy",
            pos_x: 40,
            pos_y: 40,
            width: 40,
            height: 30,
            text: "Copy"
        }
        ]
    }
    
    send2panel(msg);
}

function uiShowCopying()
{
    const msg = {
        UI_SCR_LAYOUT_T: [
        {
            type: "label",
            name: "uiLauncherLabel",
            pos_x: 10,
            pos_y: 10,
            width: 440,
            height: 30,
            text: "Copying"
        },
        {
            type: "button",
            name: "uiCancelCopy",
            pos_x: 40,
            pos_y: 40,
            width: 40,
            height: 30,
            text: "Cancel"
        }
        ]
    }
    
    send2panel(msg);
}

function uiShowError(info)
{
    const msg = {
        UI_SCR_LAYOUT_T: [
        {
            type: "label",
            name: "uiLauncherLabel",
            pos_x: 10,
            pos_y: 10,
            width: 440,
            height: 30,
            text: info
        },
        {
            type: "button",
            name: "uiShowErrorOK",
            pos_x: 40,
            pos_y: 40,
            width: 40,
            height: 30,
            text: "OK"
        }
        ]
    }
    
    send2panel(msg);
}


function doCopy() {
    var msg = {
        msgType: 'func_copy'
    }
    
    send2printercopy(JSON.stringify(msg));
}

function onClickButton(name)
{
    switch (name) 
    {
        case 'uiLauncherEntryCopy':
            uiShowCopying();
            doCopy();
            break;
        case 'uiShowErrorOK':
        case 'uiCancelCopy':
        case 'uiShowErrorOK':
            uiInitLauncher();
            break;
        case '':
            break;
    }
}

function eventProcess(eventMsg) 
{
    const obj = JSON.parse(eventMsg);
    switch (obj.type)
    {
        case 'click_button':
            onClickButton(obj.button_name);
            break;
        case 'error_info':
            uiShowErrorOK(obj.info);
            break;
    }
}


function mainLoop()
{
    var eventMsg = getPrinterEvent();
    // print("aaa" + eventMsg);
    if ( (eventMsg != undefined) && (eventMsg.length > 2) )
        eventProcess(eventMsg);
    setTimeout(function () { mainLoop();}, 1000);
}
 




// print("sss");
threadInit();
uiInitLauncher();
// mainLoop();

// print("end");
