/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file cmd.c
 * @addtogroup cmd
 * @{
 * <AUTHOR> zhoushujing
 * @date 2023-4-18
 * @brief ternimal cmd debug module
 **/
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdint.h>
#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "pol/pol_io.h"
#include "pol/pol_mem.h"
#include "pol/pol_string.h"
#include "pol/pol_time.h"
#include "cmd.h"

#define CMD_PARAM_NUMBER      16                                      ///< cmd param number
#define CMD_PARAM_LENGTH      256                                      ///< cmd param length
#define CMD_STRING_MAX        (CMD_PARAM_LENGTH * CMD_PARAM_NUMBER)   ///< for blank and last '\0'

#define CMD_HELP_STRING       "help"                                  ///< cmd help string
#define CMD_HELP_INFO_DEFAULT "(no help info)"                        ///< default help info

#define MAXLEN 8                                                      ///< max length

typedef struct tag_cmd_subcmd
{
    char *subcmd;
    int32_t (*subcmd_process)(int32_t argc, char *argv[]);
    const char *subcmd_help;
    struct tag_cmd_subcmd *next;
} CMD_SUBCMD_S, *CMD_SUBCMD_P;

typedef struct tag_cmd_main_cmd
{
    char *main_cmd;
    CMD_SUBCMD_S *subcmd_list;
    int32_t subcmd_count;
    struct tag_cmd_main_cmd *next;
} CMD_MAIN_CMD_S, *CMD_MAIN_CMD_P;

typedef struct
{
    int32_t param_count;
    char param_list[CMD_PARAM_NUMBER][CMD_PARAM_LENGTH];
    char *param_name[CMD_PARAM_NUMBER];
} CMD_PARSER_LIST_S;

typedef struct tag_cmd_mgr
{
    CMD_MAIN_CMD_S *main_cmd_list;
    int32_t main_cmd_count;
    CMD_PARSER_LIST_S parser_result;
} CMD_MGR_S;

static PI_THREAD_T s_cmd_task = INVALIDTHREAD;
static CMD_MGR_S s_cmd_mgr;

/**
 * @brief  register a debug cmd for a module.
 * @param[in] main_cmd_string: module name,such as fax/print/scan/copy/usb...
 * @param[in] subcmd_string:   module function name or an operation name
 * @param[in] subcmd_process:  specfic the function callback for the debug cmd
 * @param[in] subcmd_help:     how to use the cmd,what should user input
 * @return    register result
 *
 * @retval    0: if register success  \n
 *           -1: if register failed
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t cmd_register(char *main_cmd_string, char *subcmd_string, int32_t (*subcmd_process)(int32_t argc, char *argv[]),
        const char *subcmd_help)
{
    CMD_MAIN_CMD_S *pmain_cmd = NULL;
    CMD_SUBCMD_S *psubcmd = NULL;

    //input param check
    if (!main_cmd_string)
    {
        pi_log_e("main cmd string empty\n");
        return -1;
    }

    if (!subcmd_string || !subcmd_process)
    {
        pi_log_e("subcmd process null\n");
        return -1;
    }

    //search for the main cmd
    for (pmain_cmd = s_cmd_mgr.main_cmd_list; pmain_cmd; pmain_cmd = pmain_cmd->next)
    {
        if (0 == pi_strncmp(pmain_cmd->main_cmd, main_cmd_string, pi_strlen(main_cmd_string)))
        {
            if (pi_strlen(pmain_cmd->main_cmd) != pi_strlen(main_cmd_string)) {
                continue;
            }
            //printf("found main cmd\n");
            break;
        }
    }

    //if cmd is not exsit,create it firstly
    if (!pmain_cmd)
    {
        //printf("cmd not exist,create it\n");
        pmain_cmd = (CMD_MAIN_CMD_S *)pi_malloc(sizeof(CMD_MAIN_CMD_S));
        if (!pmain_cmd)
        {
            pi_log_e("main cmd create failed\n");
            return -1;
        }

        pi_memset(pmain_cmd, 0x00, sizeof(CMD_MAIN_CMD_S));

        //add main cmd to main list
        pmain_cmd->main_cmd = main_cmd_string;
        pmain_cmd->subcmd_list = NULL;
        pmain_cmd->subcmd_count = 0;
        pmain_cmd->next = s_cmd_mgr.main_cmd_list;
        s_cmd_mgr.main_cmd_list = pmain_cmd;
        s_cmd_mgr.main_cmd_count++;

        //printf("main cmd register ok = %s\n", main_cmd_string );
    }

    //search for the subcmd
    for (psubcmd = pmain_cmd->subcmd_list; psubcmd; psubcmd = psubcmd->next)
    {
        if (0 == pi_strncmp( psubcmd->subcmd, subcmd_string, pi_strlen(subcmd_string)))
        {
            printf("subcmd = %s has exist,ignore\n", subcmd_string);
            return 0;
        }
    }

    //if cmd is not exsit,create it firstly
    if (!psubcmd)
    {
        //printf("subcmd not exsit,create it\n");
        psubcmd = (CMD_SUBCMD_S *)pi_malloc(sizeof(CMD_SUBCMD_S));
        if( !psubcmd )
        {
            pi_log_e("subcmd create failed\n");
            return -1;
        }
        pi_memset(psubcmd, 0x00, sizeof(CMD_SUBCMD_S));

        //add main cmd to main list
        psubcmd->subcmd = subcmd_string;
        psubcmd->subcmd_help = subcmd_help ;
        psubcmd->subcmd_process = subcmd_process;
        psubcmd->next = pmain_cmd->subcmd_list;
        pmain_cmd->subcmd_list = psubcmd;
        pmain_cmd->subcmd_count++;

        printf("cmd register ok = %s - %s\n", main_cmd_string, subcmd_string);
    }

    return 0;
}

static int32_t cmd_clean_buffer(void)
{
    int32_t i = 0;

    CMD_PARSER_LIST_S *parser_buffer = &s_cmd_mgr.parser_result;

    pi_memset(parser_buffer, 0, sizeof(CMD_PARSER_LIST_S));
    /* initial the param name list to fixed valid address,or if user input params less than expected,
       the program maybe corrupt,that is a big risk. */
    for (i = 0; i < CMD_PARAM_NUMBER; i++)
    {
        parser_buffer->param_name[i] = parser_buffer->param_list[i];
    }

    return 0;
}

static int32_t cmd_push_subcmd_string(char *subcmd, int32_t length)
{
    CMD_PARSER_LIST_S *parser_buffer = &s_cmd_mgr.parser_result;

    if (!subcmd || !length || length >= CMD_PARAM_LENGTH)
    {
        return -1;
    }

    if (parser_buffer->param_count >= CMD_PARAM_NUMBER)
    {
        printf("cmd param too many,the limit is %d\n", CMD_PARAM_NUMBER );
        return -2;
    }

    pi_strncpy(parser_buffer->param_list[parser_buffer->param_count], subcmd,  length);

    //printf("param[%d]=%s\n", parser_buffer->param_count,
    //parser_buffer->param_list[parser_buffer->param_count] );
    parser_buffer->param_count++;

    return 0;
}

static int32_t cmd_mainhelp(void)
{
    int32_t i = 0;
    CMD_MAIN_CMD_S *pmain_cmd = NULL;

    for (pmain_cmd = s_cmd_mgr.main_cmd_list; pmain_cmd; pmain_cmd = pmain_cmd->next)
    {
        printf("%15s", pmain_cmd->main_cmd);
        i++;
        //every line show 5 cmds
        if (0 == i % 5)
        {
            printf("\n");
        }
    }

    if (0 != i % 5)
    {
        printf("\n");
    }

    return 0;
}

static int32_t cmd_subcmd_help(CMD_MAIN_CMD_S *pmain_cmd, int32_t show_help_string)
{
    CMD_SUBCMD_S *psubcmd = NULL;

    if (!pmain_cmd)
    {
        return -1;
    }

    if (show_help_string)
    {
        printf("\"%s\" supports subcmds below:\n", pmain_cmd->main_cmd);
        for (psubcmd = pmain_cmd->subcmd_list; psubcmd; psubcmd = psubcmd->next)
        {
            printf("%s %s\n", pmain_cmd->main_cmd, psubcmd->subcmd);
            if (psubcmd->subcmd_help)
            {
                printf("    %s\n", psubcmd->subcmd_help );
            }
            else
            {
                printf("    %s\n", CMD_HELP_INFO_DEFAULT );
            }
        }
    }
    else
    {
        int32_t i = 0;
        printf("%s ", pmain_cmd->main_cmd );
        for (psubcmd = pmain_cmd->subcmd_list; psubcmd; psubcmd = psubcmd->next)
        {
            printf("%s", psubcmd->subcmd);
            i++;
            if (psubcmd->next)
            {
                printf(" | ");
            }
            else
            {
                printf(" ...");
            }
            //every line show 5 cmds
            if (0 == i % 5)
            {
                printf("\n   ");
            }
        }

        if (0 != i % 5)
        {
            printf("\n");
        }
    }

    return 0;
}

static int32_t cmd_parser_string(char *input_string)
{
    int32_t i = 0, marker = 0;
    int32_t ret = 0;

    if (!input_string)
    {
        printf("input string empty\n");
        return -1;
    }

    //jump blank space
    i = 0;
    while ((0x20 == *(input_string+i) || 0x00 == *(input_string + i)) && (++i < CMD_STRING_MAX / 2));

    if (i >= CMD_STRING_MAX)
    {
        //printf("too many space before your cmd\n");
        return -1;
    }

    //clean buffer
    cmd_clean_buffer();

    marker = i;
    //start parsing string chars
    for (; i < CMD_STRING_MAX-1; i++)
    {
        //search a param gap marker or a string end marker
        if (0x20 == *(input_string + i) || 0x00 == *(input_string + i))
        {
            if (i - marker >= CMD_PARAM_LENGTH)
            {
                printf("cmd param too long,the limit is %d\n", CMD_PARAM_LENGTH );
                return -1;
            }
            else if (0 == i - marker)
            {
                //printf("jump a blank\n");
            }
            else
            {
                ret = cmd_push_subcmd_string(input_string + marker, i - marker);
                if (ret < 0)
                {
                    return -2;
                }
            }
            marker = i + 1;
        }

        if (0x00 == *(input_string + i))
        {
            //printf("cmd end\n");
            break;
        }
    }
    return ret;
}

 /**
 * @brief  cmd module execute
 * @return debug result
 * @retval 0:   cmd execute success
 * @retval -1:  cmd execute failed
 * <AUTHOR> liangshiqin
 * @data   2023-4-18
*/
static int32_t cmd_execute(void)
{
    int32_t ret = 0;
    CMD_MAIN_CMD_S *pmain_cmd = NULL;
    CMD_SUBCMD_S *psubcmd = NULL;
    CMD_PARSER_LIST_S *parser_buffer = &s_cmd_mgr.parser_result;

    if (parser_buffer->param_count < 1)
    {
        //printf("cmd string parser error\n");
        return -1;
    }

    //it is a help main cmd
    if (0 == pi_strncmp(CMD_HELP_STRING, parser_buffer->param_name[0], pi_strlen(CMD_HELP_STRING)))
    {
        return cmd_mainhelp();
    }

    //search main cmd
    for (pmain_cmd = s_cmd_mgr.main_cmd_list; pmain_cmd; pmain_cmd = pmain_cmd->next)
    {
        if (0 == pi_strncmp(pmain_cmd->main_cmd, parser_buffer->param_name[0], pi_strlen(pmain_cmd->main_cmd)))
        {
            if (pi_strlen(parser_buffer->param_name[0]) != pi_strlen(pmain_cmd->main_cmd)) {
                continue;
            }
            //main cmd match
            break;
        }
    }

    //main cmd ok for subcmd
    if (pmain_cmd)
    {
        //main cmd without subcmd,show help string
        if (parser_buffer->param_count < 2)
        {
            printf("invalid subcmd,try below!\n");
            return cmd_subcmd_help(pmain_cmd, 0);
        }

        //found main cmd,check this is a help subcmd
        if (0 == pi_strncmp(CMD_HELP_STRING, parser_buffer->param_name[1], pi_strlen( CMD_HELP_STRING)))
        {
            return cmd_subcmd_help(pmain_cmd, 1);
        }

        //search subcmd and execute
        for (psubcmd = pmain_cmd->subcmd_list; psubcmd; psubcmd = psubcmd->next)
        {
            //printf("%s\n", debug_method_table[i].cmd_string);
            if (0 == pi_strncmp(psubcmd->subcmd, parser_buffer->param_name[1], pi_strlen(psubcmd->subcmd)))
            {
                //cmd match then run subcmd
                ret = psubcmd->subcmd_process(parser_buffer->param_count - 2, parser_buffer->param_name + 2);
                if (ret < 0)
                {
                    if (psubcmd->subcmd_help)
                    {
                        printf("run failed,try below!\n" );
                        printf("%s\n", psubcmd->subcmd_help);
                    }
                    else
                    {
                        printf("%s\n", CMD_HELP_INFO_DEFAULT );
                    }
                    return 0;
                }
                else
                {
                    //printf("cmd process ok\n");
                    return 1;
                }
            }
        }

        if (!psubcmd)
        {
            printf("invalid subcmd,try below!\n");
            cmd_subcmd_help( pmain_cmd, 0 );
            ret = -1;
        }
    }
    else
    {
        printf("invalid cmd!\n");
        ret = -2;
    }

    return ret;
}

/**
 * @brief  cmd module string thread, make a job start msg and data
 * @param[in] input_string : cmd module parser string
 * @return debug result
 * @retval 0:  success
 * @retval -1:  failed
 * <AUTHOR> liangshiqin
 * @data   2023-4-18
*/
static int32_t cmd_string_process(char *input_string)
{
    int32_t ret = 0;

    ret = cmd_parser_string(input_string);
    if (ret < 0)
    {
        return -1;
    }

    return cmd_execute();
}

static void cmd_sleep(int32_t milliseconds)
{
    struct timespec ts;
    int32_t result;

    ts.tv_sec = milliseconds / 1000;
    ts.tv_nsec = (milliseconds - (ts.tv_sec * 1000)) * 1000000;
    do
    {
        result = pi_nanosleep(&ts, &ts);
    }
    while (result == -1 && errno == EINTR);
}

 /**
 * @brief  cmd module debug thread
 * @param[in] name  : specfic the fifo name in /tmp/ dir, examples-- \n
 *                    echo print 10 > /tmp/print_ut
 * @return debug result
 * @retval 0:  success  \n
 *         other:  failed
 * <AUTHOR> liangshiqin
 * @data   2023-4-18
*/
static int32_t cmd_process_thread(void* name)
{
    int32_t fd = -1;
    char cmd_string_buffer[CMD_STRING_MAX] = {0};
    char fifo_name[32]={0};

    pi_snprintf(fifo_name,32,"/tmp/%s",(char*)name);

    if (mkfifo(fifo_name, O_CREAT|O_RDWR|0666) < 0)
    {
        pi_log_e("Create cmd pipe failed:%s \n",fifo_name);
        return 0;
    }

    fd = pi_open(fifo_name, O_RDONLY);
    if (fd < 0)
    {
        pi_log_e("Open cmd pipe:%s failed\n",fifo_name);
        return 0;
    }

    while (1)
    {
        cmd_sleep(100);

        pi_memset(cmd_string_buffer, 0, sizeof(cmd_string_buffer));
        if (pi_read(fd, cmd_string_buffer, CMD_STRING_MAX - 1) > 0)
        {
            //put a string end flag at the end of buffer
            cmd_string_buffer[CMD_STRING_MAX - 1] = '\0';

            cmd_string_process(cmd_string_buffer);
        }
    }

    return 0;
}
/**
 * @brief  cmd module initialize
 * @param[in] name  : specfic the fifo name in /tmp/ dir, examples-- \n
 * if name is "cmd", the real fifo pathname is "/tmp/cmd",the max name string length is 8
 * @return initialize result
 * @retval 0: if initialize success  \n
 *        -1: if initialize failed
 * <AUTHOR> zhoushujing
 * @data   2023-4-18
 * @note   each process just invoke initialize once and only once, and ensure with the different name

*/
int32_t cmd_prolog(char* name )
{
    if(s_cmd_task != INVALIDTHREAD)
    {
        pi_log_e("the cmd task has been created!\n");
        return 0;
    }

    if(name == NULL)
    {
        pi_log_e("%s para name is NULL\n",__func__);
        return -1;
    }

    if(strlen(name)>MAXLEN)
    {
        pi_log_e("%s para name len is overflow\n",__func__);
        return -1;
    }

    s_cmd_mgr.main_cmd_count = 0;
    s_cmd_mgr.main_cmd_list = NULL;

    //start our debug thread

    s_cmd_task = pi_thread_create(
            (void * (*)(void *))cmd_process_thread,
            PI_LARGE_STACK,
            NULL,
            15,
            name,
            "cmd process thread"
            );

    if (s_cmd_task == INVALIDTHREAD)
    {
        pi_log_e("Can't start thread\n");
        return -1;
    }

    return 0;
}

static int32_t cmd_epilog(void)
{
    return 0;
}

/**
 *@}
 */
