/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pedksrv.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief pedk service for port 443
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "http_task.h"

#include "pedk_download.h"
#include "pedk_mgr.h"

#define USERNAME                    "username"
#define PASSWORD                    "password"
#define TOKEN_CHARSET               "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
#define BUNDLE_NAME                 "bundleName"
#define BEARER_PREFIX               "Bearer "
#define CRLF2                       "\r\n\r\n"
#define BODY_NOT_FIND               "app name is not find!!!"

#define MSG_GET_FAIL                "{\"message\":\"get failed\"}"
#define MSG_SUCCESS                 "{\"message\":\"success\"}"
#define MSG_UNINSTALL_FAIL          "{\"message\":\"uninstall fail\"}"
#define MSG_UPLOAD_FAIL             "{\"message\":\"upload failed\"}"
#define MSG_LOGOUT_SUCCESS          "{\"message\":\"logout success\"}"

#define HEADER_CONTENT_TYPE         "content-type"
#define HEADER_CONTENT_LENGTH       "content-length"
#define HEADER_TRANSFER_ENCODING    "transfer-encoding"
#define HEADER_AUTHORIZATION        "authorization"
#define HEADER_CHUNKED              "chunked"

#define MULTIPART_FORM_BOUNDARY         "multipart/form-data; boundary="
#define MULTIPART_FORM_HEADER_FORMAT    "Content-Disposition: form-data; name=\"%s\""
#define MULTIPART_FORM_HEADER_FILENAME  "; filename=\""
#define MULTIPART_FORM_CONTENT_TYPE     "\r\nContent-Type: "

#define MAXIMUM_MEMORY_LIMIT         (unsigned)2*MB
#define MAXIMUM_FILE_LIMIT           (unsigned)100*MB

#define NOTIFY_TIMEOUT               5
#define LEN(_chars)                  (sizeof(_chars) - 1)
#define OUT

#define _autofree_cjson_ __attribute__ ((__cleanup__(cjson_cleanup)))
#define _autofree_ __attribute__ ((__cleanup__(free_cleanup)))

#define _SET_BITS(a1, a2, a3, a4, a5, ...)       ((uint64_t)1) << (a1) | ((uint64_t)1) << (a2) | ((uint64_t)1) << (a3) | ((uint64_t)1) << (a4) | ((uint64_t)1) << (a5)
#define SET_BITS(...)                            _SET_BITS(__VA_ARGS__, 62, 62, 62, 62, 62)
#define SET_BIT(a1)                              ((uint64_t)1) << (a1)

#define KB  1024
#define MB  1024*KB
#define GB  1024*MB
#define URL_PREFIX               "/pedk/"
#define TOKEN_LENGTH 10

#define HANDLE_DEFINITION_SIGNATURE (HTTP_TASK_S* ptask, OUT const char** rcontent, OUT const char** rcode, OUT const char** rtype)

#define HANDLE_DEFINITION(__signature)  \
static __attribute__((nonnull)) int (__signature)HANDLE_DEFINITION_SIGNATURE

enum EREQUEST_FLAGS
{
    ePRIVILEGE = 1 << 0,   // need to have privilege to use the resource( ie: have token )
};

struct url_table;
typedef struct pedksrv_task_private
{
    const char*         url;
    const char*         url_parms;
    struct url_table*   table_entry;
    char                body_fname[64];
    FILE*               body_fp;
    int64_t             body_received;
    int64_t             body_ctlen;   // body content-length
    size_t              body_buflen;   // body_buf len, malloced
    uint8_t             body_buf[0];
}
PRIV_INFO_S;

typedef __attribute__((nonnull)) int (*HANDLE_FUNC)HANDLE_DEFINITION_SIGNATURE;

typedef struct url_table
{
    const char*  request_url;
    uint64_t     request_method_mask;
    uint32_t     request_flags;
    HANDLE_FUNC  request_handle;
}
URL_TABLE_S;

typedef struct notify_entry_s
{
    struct list_head  list;
    time_t            tv_sec;    // for simplicity, just record the tv_sec of struct timespec
    cJSON*            data;
} NOTIFY_ENTRY_S;

typedef struct pedksrv_context
{
    NET_CTX_S*          net_ctx;

    // corresponding to one session, when session begin, generatoe token, when session end, reset token
    char                token[TOKEN_LENGTH+1];

    pthread_condattr_t  notify_cattr;    // condattr   for clock_monotonic
    pthread_mutex_t     notify_mutex;    // mutex      for pedk_apps_notify
    pthread_cond_t      notify_cond;     // cond       for pedk_apps_notify
    struct list_head    notify_head;
}
PEDKSRV_CTX_S;

static PEDKSRV_CTX_S* s_pedksrv_ctx;

__attribute__((always_inline))
static inline void cjson_cleanup(cJSON** ppcjson)
{
    if (*ppcjson)
    {
        cJSON_Delete(*ppcjson);
        *ppcjson = NULL;
    }
}

__attribute__((always_inline))
static inline void free_cleanup(char** ptr)
{
    if (*ptr)
    {
        free(*ptr);
        *ptr = NULL;
    }
}

static int is_pedk_url(const char* url)
{
    int ret = 0;

    if ( strncmp(url, URL_PREFIX, LEN(URL_PREFIX)) == 0 )
    {
        ret = 1;
    }
    return ret;
}

static void generate_token()
{
    srand(time(NULL)); // 使用当前时间作为随机数种子

    for (int i = 0; i < TOKEN_LENGTH; ++i)
    {
        s_pedksrv_ctx->token[i] = TOKEN_CHARSET[rand() % LEN(TOKEN_CHARSET)];
    }
    //token[TOKEN_LENGTH] = '\0';
}

static void clear_token()
{
    memset(s_pedksrv_ctx->token, 0, TOKEN_LENGTH);
}

static const char* get_token()
{
    return s_pedksrv_ctx->token;
}

static int check_token(const char* bearer_token)
{
    RETURN_VAL_IF(STRING_IS_EMPTY(bearer_token) || s_pedksrv_ctx->token[0] == '\0', NET_WARN, -1);

    if ( (strncmp(bearer_token, BEARER_PREFIX, LEN(BEARER_PREFIX)) == 0) &&
         (strncmp(bearer_token + LEN(BEARER_PREFIX), s_pedksrv_ctx->token, TOKEN_LENGTH) == 0) )
    {
        return 0;
    }
    return -1;
}

static int get_url_parms_val_by_key(OUT char *dst, const size_t dstsize, const char *src, const char *key)
{
    size_t      length;
    char*       p;
    const char* q;
    RETURN_VAL_IF(unlikely(dst == NULL || src == NULL || dstsize == 0), NET_WARN, -1);

    q = strstr(src, key);
    RETURN_VAL_IF(q == NULL, NET_WARN, -1);
    q += strlen(key);
    RETURN_VAL_IF(*q != '=', NET_WARN, -1);
    /*
      Copy src to dst within bounds of size-1, stop when encounter '&’ or '\0'
    */
    for ( p=dst, ++q, length=0;
          (*q != '\0' && *q != '&') && (length < dstsize-1);
          length++, p++, q++ )
    {
        *p = *q;
    }

    dst[length]='\0';
    return 0;
}

void notify_entry_free(NOTIFY_ENTRY_S* entry)
{
    RETURN_IF(entry == NULL, NET_WARN);

    if (entry->data)
    {
        cJSON_Delete(entry->data);
        entry->data = NULL;
    }
    pi_free(entry);
}

void http_pedkapi_handler(SUB_MSG_E sub, int32_t respond, int32_t buf_size, unsigned char* buf, void *ctx )
{
    struct timespec ts;
    NOTIFY_ENTRY_S     *new, *node;
    cJSON*             json_data;
    struct list_head   *pos, *n;

    new = pi_malloc(sizeof(NOTIFY_ENTRY_S));
    RETURN_IF(new == NULL, NET_WARN);

    json_data = cJSON_Parse((const char*)buf);
    pthread_mutex_lock(&s_pedksrv_ctx->notify_mutex);
    clock_gettime(CLOCK_MONOTONIC, &ts);
    new->tv_sec = ts.tv_sec;
    new->data = json_data;

    // first remove the timeout(which should be consumed) entry
    pi_list_for_each_safe(pos, n, &s_pedksrv_ctx->notify_head)
    {
        node = pi_list_entry(pos, NOTIFY_ENTRY_S, list);
        if (node->tv_sec + NOTIFY_TIMEOUT < ts.tv_sec)
        {
            pi_list_del_entry(pos);
            notify_entry_free(node);
        }
    }
    pi_list_add_tail(&new->list, &s_pedksrv_ctx->notify_head);

    pthread_cond_signal(&s_pedksrv_ctx->notify_cond);
    pthread_mutex_unlock(&s_pedksrv_ctx->notify_mutex);
}

HANDLE_DEFINITION(pedk_users_logout)
{
    clear_token();
    *rcontent = MSG_LOGOUT_SUCCESS;
    return LEN(MSG_LOGOUT_SUCCESS);
}

HANDLE_DEFINITION(pedk_users_login)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    char     web_usrname[256] = { 0 };
    char     web_password[256] = { 0 };
    char     input_usrname[256];
    char     input_password[256];
    int      ret;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    // /users/login?username=admin&password=asd12333 --
    ret = get_url_parms_val_by_key(input_usrname, sizeof(input_usrname), priv->url_parms, USERNAME);
    RETURN_VAL_IF(ret!=0, NET_WARN, -1);
    ret = get_url_parms_val_by_key(input_password, sizeof(input_password), priv->url_parms, PASSWORD);
    RETURN_VAL_IF(ret!=0, NET_WARN, -1);

    netdata_get_web_user(DATA_MGR_OF(s_pedksrv_ctx), web_usrname, sizeof(web_usrname));
    netdata_get_web_pswd(DATA_MGR_OF(s_pedksrv_ctx), web_password, sizeof(web_password));
    if ( strcmp((char*)input_usrname, web_usrname) || strcmp((char*)(input_password), web_password) )
    {
        NET_WARN("verify usrname and password are not OK, return");
        *rcode = http_status_string(422);
        return 0;
    }
    else
    {
        generate_token();
    }

    ret = snprintf((char*)priv->body_buf, priv->body_buflen, "{\"uid\":0,\"token\":\"%s\"}", get_token());
    *rcontent = (char*)priv->body_buf;

    return ret;
}

HANDLE_DEFINITION(pedk_apps_info)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    char*     token;
    char      usrname[256] = { 0 };
    char      password[256] = { 0 };
    char      input_bundle[256];
    int       ret;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    ret = get_url_parms_val_by_key(input_bundle, sizeof(input_bundle), priv->url_parms, BUNDLE_NAME);
    RETURN_VAL_IF(ret!=0, NET_WARN, -1);

    ret = pedk_download_get_app_info(input_bundle, (char*)priv->body_buf, priv->body_buflen);
    if (ret == 0)
    {
        *rcontent = (char*)priv->body_buf;
        ret = strlen(*rcontent);
    }
    else
    {
        *rcontent = MSG_GET_FAIL;
        ret = LEN(MSG_GET_FAIL);
    }
    return ret;
}

// refer to RFC2183
static int multipart_form_get_data(PRIV_INFO_S* priv, const char* delimiter, int delimiter_len, const char* name, OUT char* val, size_t val_size)
{
    uint8_t   *p, *q, *end;
    char      form_header[64];
    int       form_header_len;
    int       form_val_len;
    uint8_t   buf[TCP_CHUNK_SIZE];
    uint8_t*  iobuf = priv->body_buf;
    size_t    iobuf_len = priv->body_buflen;

    end = iobuf + iobuf_len;
    form_header_len = snprintf(form_header, sizeof(form_header), MULTIPART_FORM_HEADER_FORMAT, name);
    if ( unlikely(form_header_len >= sizeof(form_header)) )
    {
        NET_WARN("multipart_form_get_data overflow, name = %s, len = %d", name, form_header_len);
        return -1;
    }
    p = memmem(iobuf, iobuf_len, form_header, form_header_len);
    RETURN_VAL_IF(p == NULL, NET_WARN, -1);
    p += form_header_len;
    //RETURN_VAL_IF(strncmp((char*)p, CRLF2, LEN(CRLF2)), NET_WARN, -1);    //FIXME: other packing tag?
    p += LEN(CRLF2);

    RETURN_VAL_IF(p >= end, NET_WARN, -1);

    q = memmem(p, end - p, delimiter, delimiter_len);
    RETURN_VAL_IF(q == NULL, NET_WARN, -1);

    // form_header  val   \r\n
    form_val_len = q - p - 2 /* last two char are \r\n */;
    RETURN_VAL_IF(p >= end, NET_WARN, -1);

    if ( unlikely(form_val_len >= val_size) )
    {
        NET_WARN("multipart_form_get_data val overflow, name = %s, val_len = %d", name, form_val_len);
        return -1;
    }

    memcpy(val, p, form_val_len);
    val[form_val_len] = '\0';
    return 0;
}

static int multipart_form_write_file(PRIV_INFO_S* priv, const char* delimiter, int delimiter_len, const char* name, OUT char* fname, size_t fname_size, OUT char* app_path, size_t app_path_size)
{
    uint8_t     *p, *q, *end;
    char        form_header[128];
    char        app_whitelist[1024] = {0};
    int         form_header_len;
    int         form_val_len;
    size_t      len;
    int64_t     written;
    int32_t     need_to_written;
    uint8_t*    iobuf = priv->body_buf;
    size_t      iobuf_len = priv->body_buflen;
    FILE*       fp;
    int         ret = -1;
    int64_t     total_left;

    end = iobuf + iobuf_len;
    form_header_len = snprintf(form_header, sizeof(form_header), MULTIPART_FORM_HEADER_FORMAT, name);
    if ( form_header_len >= sizeof(form_header) )
    {
        NET_WARN("multipart_form_get_data overflow, name = %s, len = %d", name, form_header_len);
        return -1;
    }
    p = memmem(iobuf, iobuf_len, form_header, form_header_len);
    RETURN_VAL_IF(p == NULL, NET_WARN, -1);
    p += form_header_len;
    RETURN_VAL_IF(strncmp((char*)p, MULTIPART_FORM_HEADER_FILENAME, LEN(MULTIPART_FORM_HEADER_FILENAME)), NET_WARN, -1);
    p += LEN(MULTIPART_FORM_HEADER_FILENAME);

    // get the filename
    for (size_t i=0; (p < end) && (i < fname_size); ++i, ++p)
    {
        if ( (fname[i] = *p) == '"' )
        {
            fname[i] = '\0';
            break;
        }
    }
    RETURN_VAL_IF(*p != '"', NET_WARN, -1);

    snprintf(app_path, app_path_size, "/tmp/%s", fname);
    if (access(app_path, F_OK) == 0)
    {
        NET_WARN("File '%s' is exist", app_path);
        unlink(app_path);
    }
    if (strstr(app_path, ".tar") == NULL)
    {
        NET_WARN("file is not *.tar!");
        return -1;
    }

    // walk through content-type
    RETURN_VAL_IF(strncmp((char*)(++p), MULTIPART_FORM_CONTENT_TYPE, LEN(MULTIPART_FORM_CONTENT_TYPE)), NET_WARN, -1);
    p = memmem(p, end - p, CRLF2, LEN(CRLF2));
    RETURN_VAL_IF( unlikely(p == NULL), NET_WARN, -1 );

    p += LEN(CRLF2);
    RETURN_VAL_IF( unlikely(p >= end), NET_WARN, -1);

    /*将安装包写入到tmp目录下，让后端解析文件*/
    if ( (fp = fopen(app_path, "wb")) == NULL)
    {
        NET_WARN("open error!");
        return -1;
    }

    do
    {
        if (priv->body_fp)  // we assume the file the last multipart form header
        {
            written = p - iobuf;
            // 无符号数和有符号数运算时会先转为无符号数，这里注意total_left和written都定义为有符号数
            total_left = priv->body_received - delimiter_len - 6 - written;     //  \r\ndelimiter--\r\n
            BREAK_IF(total_left < 0, NET_WARN);
            if (total_left + written <= iobuf_len)  // all loaded into iobuf allright
            {
                written = fwrite(p, 1, total_left, fp);
                BREAK_IF(written != total_left, NET_WARN);
            }
            else
            {
                // first write the left in the first iobuf
                need_to_written = end - p;
                written = fwrite(p, 1, need_to_written, fp);
                BREAK_IF(written != need_to_written, NET_WARN);

                // the write the leftover
                while ( (total_left -= written) > 0 )
                {
                    need_to_written = (total_left > iobuf_len ? iobuf_len : total_left);
                    written = fread(iobuf, 1, need_to_written, priv->body_fp);
                    written = fwrite(iobuf, 1, written, fp);
                    BREAK_IF(written != need_to_written, NET_WARN);
                }
            }
        }
        else  // file is small and not chunked
        {
            q = memmem(p, end - p, delimiter, delimiter_len);
            BREAK_IF(q == NULL, NET_WARN);
            BREAK_IF(p+2 == q, NET_WARN);  // file is 0 byte
            BREAK_IF(p+2 > q, NET_WARN);  // \r\ndelimiter

            need_to_written = q - p - 2;
            written = fwrite(p, 1, need_to_written, fp);
            BREAK_IF(written != need_to_written, NET_WARN);
        }
        ret = 0;
    }
    while(0);
    fclose(fp);

    return ret;
}

HANDLE_DEFINITION(pedk_apps_upload)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    cJSON*      root_obj _autofree_cjson_ = NULL;
    char*       form_boundary;
    char        boundary_delimiter[72];
    int         boundary_delimiter_len;
    const char* content_type;
    char        app_path[128] = {0};
    char        file_name[64];
    char        auto_boot[64];
    char        env[128];
    int32_t     len = 0;
    int32_t     lenth = 0;
    int32_t     ret = -1;
    char        app_whitelist[1024] = {0};
    char*       json_str _autofree_ = NULL;

    if (priv->body_fp)
    {
        // load the very beginning content of file to memory
        rewind(priv->body_fp);
        fread(priv->body_buf, 1, MIN(priv->body_buflen, priv->body_received), priv->body_fp);
    }

    do
    {  /*while 0*/
        BREAK_IF(priv->body_received == 0, NET_WARN);

        content_type = http_task_search_header_field(ptask, HEADER_CONTENT_TYPE);
        //FIXME: only support multipart/form-data
        BREAK_IF(STRING_IS_EMPTY(content_type), NET_WARN);
        form_boundary = strstr(content_type, MULTIPART_FORM_BOUNDARY);
        BREAK_IF(form_boundary == NULL, NET_WARN);
        form_boundary += LEN(MULTIPART_FORM_BOUNDARY);

        boundary_delimiter_len = snprintf(boundary_delimiter, sizeof(boundary_delimiter), "--%s", form_boundary);
        BREAK_IF(boundary_delimiter_len < 0, NET_WARN);
        if ( unlikely(boundary_delimiter_len >= sizeof(boundary_delimiter)) )
        {
            NET_WARN("multipart_form_get_data boundary_delimiter overflow, boundary = %s", boundary_delimiter);
            break;
        }

        if(access(PEDK_ROOT_DIR_PATH_TMP, F_OK) != 0)
        {
            if(mkdir(PEDK_ROOT_DIR_PATH_TMP, 0777) != 0)
            {
                perror("mkdir");
                return -1;
            }
        }

        ret = multipart_form_get_data(priv, boundary_delimiter, boundary_delimiter_len, "auto-boot", auto_boot, sizeof(auto_boot));
        BREAK_IF(ret == -1, NET_WARN);
        ret = multipart_form_get_data(priv, boundary_delimiter, boundary_delimiter_len, "env", env, sizeof(env));
        if (ret == -1)
        {
            NET_DEBUG("name=env = NULL");
        }
        else
        {
            pedk_download_write_env2file(PEDK_DOWNLOAD_ENV_JSON, env);
        }

        ret = multipart_form_write_file(priv, boundary_delimiter, boundary_delimiter_len, "file", file_name, sizeof(file_name), app_path, sizeof(app_path));
        ret = pedk_download_app_install(app_path, app_whitelist, sizeof(app_whitelist), auto_boot);
        NET_WARN(" pedk_download_app_install (%s) (%d)!", app_whitelist, ret);
        BREAK_IF(ret == -1, NET_WARN);
    }
    while(0);

    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);
    if (ret == 0)
    {
        cJSON_AddStringToObject(root_obj, "name", file_name);
        cJSON_AddStringToObject(root_obj, "auto-boot", auto_boot);

        json_str = cJSON_PrintUnformatted(root_obj);
        RETURN_VAL_IF(unlikely(json_str == NULL), NET_WARN, -1);
        ret = snprintf((char*)priv->body_buf, priv->body_buflen, "%s", json_str);
        *rcontent = (char*)priv->body_buf;
        return ret;
    }
    else
    {
        *rcode = http_status_string(422);
    }

    return 0;
}

HANDLE_DEFINITION(pedk_apps_list)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    char*       json_str _autofree_ = NULL;
    int         ret;

    cJSON* ret_obj = pedk_download_applist_get_all_entries();
    json_str = cJSON_PrintUnformatted(ret_obj);
    pedk_download_applist_free_entries_array(ret_obj);

    RETURN_VAL_IF(unlikely(json_str == NULL), NET_WARN, -1);
    ret = snprintf((char*)priv->body_buf, priv->body_buflen, "%s", json_str);
    *rcontent = (char*)priv->body_buf;
    return ret;
}

HANDLE_DEFINITION(pedk_apps_delete)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    cJSON*      get_obj _autofree_cjson_ = NULL;
    cJSON*      data_obj;
    char*       pjson = NULL;
    int         ret = -1;

    RETURN_VAL_IF( (priv == NULL) || (priv->body_fp != NULL), NET_WARN, -1);

    get_obj = cJSON_Parse((char*)priv->body_buf);
    if (get_obj != NULL)
    {
        data_obj = cJSON_GetObjectItem(get_obj, "bundleName");
        if (data_obj != NULL)
        {
            NET_DEBUG("bundelName---%s--- \n", data_obj->valuestring);
            ret = pedk_download_app_uninstall(data_obj->valuestring);
        }
    }
    else
    {
        NET_WARN("cJSON_GetErrorPtr: %s", cJSON_GetErrorPtr());
    }

    if (ret != 0)
    {
        *rcontent = MSG_UNINSTALL_FAIL;
        ret = LEN(MSG_UNINSTALL_FAIL);
    }
    else
    {
        *rcontent = MSG_SUCCESS;
        ret = LEN(MSG_SUCCESS);
    }
    return ret;
}

HANDLE_DEFINITION(pedk_apps_delete_many)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    cJSON*      root_obj _autofree_cjson_ = NULL;
    cJSON*      get_obj _autofree_cjson_  = NULL;
    cJSON       *data_obj, *item;
    char*       pjson = NULL;
    char        server_buf[128] = {0};
    int         ret = -1;
    unsigned    size = 0, i;
    char*       json_str _autofree_ = NULL;

    RETURN_VAL_IF( (priv == NULL) || (priv->body_fp != NULL), NET_WARN, -1);
    RETURN_VAL_IF((root_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    get_obj = cJSON_Parse((char*)priv->body_buf);
    if (get_obj != NULL)
    {
        cJSON* get_array = cJSON_GetObjectItem(get_obj, "bundleNames");
        if (get_array != NULL)
        {
            size = cJSON_GetArraySize(get_array);
            NET_DEBUG("size %d\n", size);
            cJSON *success_array = cJSON_CreateArray();
            cJSON *fail_array = cJSON_CreateArray();

            for(i = 0; i < size; i++)
            {
                item = cJSON_GetArrayItem(get_array,i);
                NET_DEBUG("item (%d) (%s)\n", i, item->valuestring);
                ret = pedk_download_app_uninstall(item->valuestring);
                NET_DEBUG("uninstall [%d]:[%d]\n", i, ret);

                RETURN_VAL_IF((data_obj = cJSON_CreateObject()) == NULL, NET_WARN, -1);
                cJSON_AddStringToObject(data_obj, "bundleName", item->valuestring);
                cJSON_AddStringToObject(data_obj, "version", "0.0.1");
                if (ret == 0)
                {
                    cJSON_AddItemToArray(success_array, data_obj);
                }
                else
                {
                    cJSON_AddItemToArray(fail_array, data_obj);
                }
            }

            cJSON_AddItemToObject(root_obj, "successList", success_array);
            cJSON_AddItemToObject(root_obj, "failList", fail_array);
        }
    }
    else
    {
        NET_WARN("cJSON_GetErrorPtr: %s\n", cJSON_GetErrorPtr());
        cJSON_AddStringToObject(root_obj, "message","delete fail");
    }

    json_str = cJSON_PrintUnformatted(root_obj);
    RETURN_VAL_IF(unlikely(json_str == NULL), NET_WARN, -1);
    ret = snprintf((char*)priv->body_buf, priv->body_buflen, "%s", json_str);
    *rcontent = (char*)priv->body_buf;
    return ret;
}

static int pedk_app_notify_data(HTTP_TASK_S* ptask)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    cJSON*      json_hdrs _autofree_cjson_ = NULL;
    cJSON*      json_root _autofree_cjson_ = NULL;
    char*       hdrs_str  _autofree_ = NULL;
    char*       json_str  _autofree_ = NULL;
    const char  (*title)[FIELD_TITLE_LEN];
    const char  (*value)[FIELD_VALUE_LEN];
    char        app_info[1024];
    char        app_name[256];
    size_t      url_least_len;
    uint32_t    hdrs_cnt;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    // url:  /pedk/app_notify/APP_NAME/
    url_least_len = LEN(URL_PREFIX) + strlen(priv->table_entry->request_url) + 1;
    RETURN_VAL_IF(strlen(priv->url) <= url_least_len, NET_WARN, -1);

    snprintf(app_name, sizeof(app_name), "%s", priv->url + url_least_len);
    for ( int32_t i = 0; app_name[i] != '\0'; ++i )
    {
        if ( app_name[i] == '/' )
        {
            app_name[i] = '\0';
        }
    }
    NET_DEBUG("app_name(%s)", app_name);

    RETURN_VAL_IF((pedk_download_get_app_info(app_name, app_info, sizeof(app_info))) != 0, NET_WARN, -1);

    //拼字符串{url: String, method: String, header: Headers, body: RequestBody}
    json_hdrs = cJSON_CreateObject();
    RETURN_VAL_IF(json_hdrs == NULL, NET_WARN, -1);

    hdrs_cnt = http_task_get_headers_field(ptask, &title, &value);
    for ( uint32_t i = 0; i < hdrs_cnt; ++i )
    {
        cJSON_AddStringToObject(json_hdrs, title[i], value[i]);
    }

    hdrs_str = cJSON_PrintUnformatted(json_hdrs);
    RETURN_VAL_IF(hdrs_str == NULL, NET_WARN, -1);

    json_root = cJSON_CreateObject();
    RETURN_VAL_IF(json_root == NULL, NET_WARN, -1);

    cJSON_AddStringToObject(json_root, "url", priv->url);
    cJSON_AddStringToObject(json_root, "method", "Post");
    cJSON_AddStringToObject(json_root, "header", hdrs_str);
    cJSON_AddStringToObject(json_root, "body", (char *)priv->body_buf);

    json_str = cJSON_PrintUnformatted(json_root);
    NET_DEBUG("json_str(%s)", json_str);

    pedk_mgr_send_msg_to_runenv(MSG_MODULE_HTTP, MSG_HTTP_SUB, 0, (uint8_t *)json_str, strlen(json_str));

    return 0;
}

HANDLE_DEFINITION(pedk_app_notify)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    int32_t         ret = -1;
    NOTIFY_ENTRY_S* reply;
    struct timespec ts;
    int             rc;
    int             init_tv_sec;
    cJSON           *root, *data;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    //RETURN_VAL_IF(priv->body_received <= 0, NET_WARN, 0);
    //RETURN_VAL_IF(priv->body_fp != NULL, NET_WARN, -1);

    do
    {
        ret = pedk_app_notify_data(ptask);
        BREAK_IF(ret == -1, NET_NONE);

        pthread_mutex_lock(&s_pedksrv_ctx->notify_mutex);

        clock_gettime(CLOCK_MONOTONIC, &ts);

        init_tv_sec = ts.tv_sec;
        ts.tv_sec += 5;  // wait for at most 5 seconds
        do
        {
            rc = pthread_cond_timedwait(&s_pedksrv_ctx->notify_cond, &s_pedksrv_ctx->notify_mutex, &ts);
        }
        while ( pi_list_empty(&s_pedksrv_ctx->notify_head) && (rc == 0) ); // timeout rc !=0 , rc = ETIMEDOUT

        ret = -1;
        if (rc == 0)
        {
            reply = pi_list_first_entry_or_null(&s_pedksrv_ctx->notify_head, NOTIFY_ENTRY_S, list);
            if (reply != NULL)
            {
                pi_list_del_entry(&reply->list);
            }
        }
        pthread_mutex_unlock(&s_pedksrv_ctx->notify_mutex);

        BREAK_IF( (rc != 0) || (reply == NULL), NET_WARN);
        root = reply->data;

        if ( root && (reply->tv_sec >= init_tv_sec ) )
        {
            data = cJSON_GetObjectItem(root, "code");
            if (data)
            {
                *rcode = http_status_string(data->valueint);
            }
            data = cJSON_GetObjectItem(root, "body");
            if (data)
            {
                ret = snprintf((char*)priv->body_buf, priv->body_buflen, "%s", data->valuestring);
                *rcontent = (char*)priv->body_buf;
            }
        }
        notify_entry_free(reply);
    }
    while (0);

    if (ret <=0 )
    {
        *rcontent = BODY_NOT_FIND;
        ret = LEN(BODY_NOT_FIND);
    }

    return ret;
}

static URL_TABLE_S   s_request_table[] =
{
    { "users/login",       SET_BITS(HTTP_GET, HTTP_POST),             0,    pedk_users_login},
    { "users/logout",      SET_BIT(HTTP_GET),                ePRIVILEGE,    pedk_users_logout},
    { "apps/info",         SET_BIT(HTTP_GET),                ePRIVILEGE,    pedk_apps_info},
    { "apps/upload",       SET_BIT(HTTP_POST),               ePRIVILEGE,    pedk_apps_upload},
    { "apps/list",         SET_BIT(HTTP_GET),                        0,     pedk_apps_list},
    { "apps/delete",       SET_BIT(HTTP_DELETE),             ePRIVILEGE,    pedk_apps_delete},
    { "apps/deleteMany",   SET_BIT(HTTP_DELETE),             ePRIVILEGE,    pedk_apps_delete_many},
    { "app_notify",        SET_BITS(HTTP_GET, HTTP_POST),            0,     pedk_app_notify},
};

static int url_table_compar(const void *x, const void *y)
{
    const URL_TABLE_S* a1 = x;
    const URL_TABLE_S* a2 = y;
    return strcmp(a1->request_url, a2->request_url);
}

static int url_compar(const void *x, const void *y)
{
    const char*        a1 = x;
    const URL_TABLE_S* a2 = y;
    int ret = strncmp(a1, a2->request_url, strlen(a2->request_url));
    NET_DEBUG("a1:(%s), -request url:(%s),ret %d", a1, a2->request_url, ret);
    return ret;
}

static URL_TABLE_S* url_match(const char* url, enum http_method method, const char* token)
{
    URL_TABLE_S* entry;
    entry = bsearch(url + LEN(URL_PREFIX), s_request_table, ARRAY_SIZE(s_request_table), sizeof(s_request_table[0]), url_compar);
    RETURN_VAL_IF(entry == NULL, NET_NONE, NULL);

    RETURN_VAL_IF( !( SET_BIT(method) & entry->request_method_mask ), NET_WARN, NULL );
    RETURN_VAL_IF( (entry->request_flags & ePRIVILEGE) && check_token(token), NET_WARN, NULL );

    return entry;
}

/**
 * @brief       pedk service process headers.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   method  : http method.
 * @param[in]   url     : url.
 * @param[in]   version : The HTTP version.
 * @param[in]   content_length : The HTTP content_length.
 * @return      reply value
 * @retval      == 0    : process success\n
 *              <  0    : process fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
static int32_t pedksrv_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    const char*   bearer_token;
    URL_TABLE_S*  table_entry;
    uint64_t      body_len;
    size_t        body_buf_len;
    const char*   transfer_encoding;
    int           need_to_create_file;
    int           need_to_malloc = 1;

    bearer_token = http_task_search_header_field(ptask, HEADER_AUTHORIZATION);
    table_entry = url_match(url, method, bearer_token);
    RETURN_VAL_IF(table_entry == NULL, NET_WARN, -1);

    NET_DEBUG("url(%s), method = %d,  content_length = %llu", url, method, (unsigned long long)content_length);
    body_len = (content_length != ULLONG_MAX ? content_length : 0);
    body_buf_len = (body_len > 0 ? body_len+1 : 0);   // last byte for '\0';
    need_to_create_file = 0;

    if ( unlikely(body_len > MAXIMUM_FILE_LIMIT) )
    {
        NET_WARN("content_length is large than %u", MAXIMUM_FILE_LIMIT);
        return -1;
    }
    else if (body_len >= MAXIMUM_MEMORY_LIMIT)
    {
        need_to_create_file = 1;
    }
    else
    {
        transfer_encoding = http_task_search_header_field(ptask, HEADER_TRANSFER_ENCODING);
        if (transfer_encoding && strcmp(transfer_encoding, HEADER_CHUNKED) == 0)   // for chunked create file to hold it
        {
            need_to_create_file = 1;
        }
    }

    if ( unlikely(need_to_create_file) )
    {
        body_buf_len = MAXIMUM_MEMORY_LIMIT;
    }

    if (body_buf_len < TCP_CHUNK_SIZE )
    {
        body_buf_len = TCP_CHUNK_SIZE;
    }

    if(priv != NULL)
    {
        if (priv->body_fp)
        {
            fclose(priv->body_fp);
            unlink(priv->body_fname);
            priv->body_fp = NULL;
        }

        if (body_buf_len <= priv->body_buflen)
        {
            need_to_malloc = 0;
            memset(priv, 0, sizeof(PRIV_INFO_S) + body_buf_len);
        }
    }

    if (need_to_malloc)
    {
        if (priv)
        {
            pi_free(priv);
        }
        priv = pi_zalloc(sizeof(PRIV_INFO_S) + body_buf_len);
        ptask->priv_subclass[5] = priv;
        RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    }

    priv->url = url;
    priv->body_ctlen = body_len;
    priv->body_buflen = body_buf_len;
    priv->table_entry = table_entry;

    if ( unlikely(need_to_create_file) )
    {
        snprintf(priv->body_fname, sizeof(priv->body_fname), "/tmp/pedk_body_%s", get_token());
        priv->body_fp = fopen(priv->body_fname, "wb+");
        //RETURN_VAL_IF(priv->body_fp == NULL, NET_WARN, -1);
        if (priv->body_fp == NULL)
        {
            // can't not open file, for a compromise try to write the content to memory
            // when the writtend date reach MAXIMUM_MEMORY_LIMIT, just quit
            priv->body_ctlen = body_buf_len;
        }
    }

    return 0;
}

/**
 * @brief       pedk service process reqbody.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   data    : The body data .
 * @param[in]   ndata   : The  length of body data.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
static int32_t pedksrv_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    size_t written;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if (priv->body_fp)
    {
        if ( unlikely(priv->body_received + ndata > MAXIMUM_FILE_LIMIT ))
        {
            NET_WARN("post body overlength(%zu) from client(%u->%u)\n", ndata, ptask->r_port, ptask->l_port);
            return -1;
        }
        written = fwrite(data, 1, ndata, priv->body_fp);
        if ( unlikely(written != ndata) )
        {
            NET_WARN("need to write %zu bytes to file, but only written %zu", ndata, written);
            return -1;
        }
    }
    else
    {
        if ( unlikely(priv->body_received + ndata > priv->body_ctlen) )
        {
            NET_WARN("post body overlength(%lu) from client(%u->%u)\n", (unsigned long)(priv->body_received + ndata), ptask->r_port, ptask->l_port);
            return -1;
        }
        memcpy(priv->body_buf + priv->body_received, data, ndata);
    }
    priv->body_received += ndata;

    return 0;
}

/**
 * @brief       pedk service process request.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   method  : method .
 * @param[in]   url     : http request url .
 * @param[in]   parms   : parms .
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
static int32_t pedksrv_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV5(ptask, PRIV_INFO_S, priv);
    const char*  reply_code = http_status_string(200);
    const char*  reply_type = MIME_TYPE_JSON;
    const char*  reply_body = NULL;
    int32_t      reply_len = -1;

    RETURN_VAL_IF(priv == NULL || priv->table_entry == NULL, NET_WARN, -1);
    NET_DEBUG("method = %d, url = %s, parms = %s", method, url, parms);
    priv->url_parms = parms;
    reply_len = priv->table_entry->request_handle(ptask, &reply_body, &reply_code, &reply_type);
    RETURN_VAL_IF(reply_len < 0, NET_WARN, -1);

    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        http_task_send(ptask, reply_body, reply_len);
    }
    return 0;
}

int32_t pedksrv_construct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    ptask->headers_complete_callback = pedksrv_process_headers;
    ptask->reqbody_received_callback = pedksrv_process_reqbody;
    ptask->request_complete_callback = pedksrv_process_request;

    return 0;
}

void pedksrv_destruct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;
    PRIV_INFO_S* priv;

    if ( ptask != NULL && ptask->priv_subclass[5] != NULL )
    {
        priv = (PRIV_INFO_S *)ptask->priv_subclass[5];
        if ( priv->body_fp )
        {
            fclose(priv->body_fp);
            unlink(priv->body_fname);
        }
        pi_free(ptask->priv_subclass[5]);
        ptask->priv_subclass[5] = NULL;
    }
}

int32_t pedksrv_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_pedksrv_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    ret = pedk_mgr_register_handler(MSG_MODULE_HTTP, http_pedkapi_handler, (void *)net_ctx);
    RETURN_VAL_IF(ret != 0, NET_WARN, ret);
    s_pedksrv_ctx = (PEDKSRV_CTX_S *)pi_zalloc(sizeof(PEDKSRV_CTX_S));
    RETURN_VAL_IF(s_pedksrv_ctx == NULL, NET_WARN, -1);

    do
    {
        s_pedksrv_ctx->net_ctx = net_ctx;
        pi_init_list_head(&s_pedksrv_ctx->notify_head);
        BREAK_IF(pthread_mutex_init(&s_pedksrv_ctx->notify_mutex, NULL), NET_WARN);
        BREAK_IF(pthread_condattr_init(&s_pedksrv_ctx->notify_cattr), NET_WARN);
        BREAK_IF(pthread_condattr_setclock(&s_pedksrv_ctx->notify_cattr, CLOCK_MONOTONIC), NET_WARN);
        BREAK_IF(pthread_cond_init(&s_pedksrv_ctx->notify_cond, &s_pedksrv_ctx->notify_cattr), NET_WARN);

        qsort(s_request_table, ARRAY_SIZE(s_request_table), sizeof(s_request_table[0]), url_table_compar);
        ret = 0;
    }
    while(0);

    if ( ret != 0 )
    {
        pedksrv_epilog();
    }
    NET_INFO("pedksrv initialize result(%d)", ret);

    return 0;
}

void pedksrv_epilog(void)
{
    if ( s_pedksrv_ctx != NULL )
    {
        pthread_mutex_destroy(& s_pedksrv_ctx->notify_mutex);
        pthread_condattr_destroy(&s_pedksrv_ctx->notify_cattr);
        pthread_cond_destroy(&s_pedksrv_ctx->notify_cond);
        pi_free(s_pedksrv_ctx);
        s_pedksrv_ctx = NULL;
    }
}
/**
 *@}
 */
