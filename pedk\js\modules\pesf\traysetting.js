var ERROR_NO = globalThis.pedk.common.ERROR_NO

export function get_Tray_PaperSize(tray)
{
	const retstr = get_TrayPaperSize(tray);
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

export function get_Tray_PaperType(tray)
{
	const retstr = get_TrayPaperType(tray);
	if(retstr === "EXIT_FAILURE")
	{
		return ERROR_NO.EXIT_FAILURE
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	return retstr;
}

class traySetting{

	getTrayPaperSize(tray)
	{
		return get_Tray_PaperSize(tray);
	}

	getTrayPaperType(tray)
	{
		return get_Tray_PaperType(tray);
	}
}

globalThis.pedk.device.setting.traySetting					 = traySetting
globalThis.pedk.device.setting.traySetting.getTrayPaperSize  = get_Tray_PaperSize
globalThis.pedk.device.setting.traySetting.getTrayPaperType  = get_Tray_PaperType

