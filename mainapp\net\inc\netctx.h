/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netctx.c
 * @addtogroup net
 * @{
 * @addtogroup netctx
 * <AUTHOR>
 * @date 2023-5-10
 * @brief Network context manager
 */
#ifndef __NETCTX_H__
#define __NETCTX_H__

#include "threads_pool.h"
#include "netdata.h"

typedef struct network_context
{
    THREADS_POOL_S* threads_pool;       ///< threads pool object pointer
    EVT_MGR_CLI_S*  event_client;       ///< event client onject pointer
    DATA_MGR_S*     data_mgr;           ///< netdata mgr object pointer
    void*           priv;               ///< private member pointer
}
NET_CTX_S;

PT_BEGIN_DECLS

/**
 * @brief       Add a observer to focus the system status update.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   callback    : The callback function of this observer.
 * @param[in]   data        : The data of this observer.
 * <AUTHOR>
 * @date        2023-5-10
 */
void            netctx_add_sysstat_observer         (NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data);

void            netctx_add_sysjob_observer         (NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data);

/**
 * @brief       Add a observer to focus the netport update.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   callback    : The callback function of this observer.
 * @param[in]   data        : The data of this observer.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_add_netport_observer         (NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data);

/**
 * @brief       Add a observer to focus the link update.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   callback    : The callback function of this observer.
 * @param[in]   data        : The data of this observer.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_add_netlink_observer         (NET_CTX_S* net_ctx, NOTIFY_CALLBACK callback, void* data);

/**
 * @brief       Push the subject of system status update to all observers attached.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   status      : system status.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_push_sysstat_subject         (NET_CTX_S* net_ctx, int32_t status);

void            netctx_push_sysjob_subject          (NET_CTX_S* net_ctx, void* data);

/**
 * @brief       Push the subject of netport update to all observers attached.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   port        : Port update(PORT_UPDATE_XXXX in "nettypes.h").
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_push_netport_subject         (NET_CTX_S* net_ctx, int32_t port);

/**
 * @brief       Push the subject of netlink update to all observers attached.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   link        : Link change(LINK_CHANGE_XXXX in "nettypes.h").
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_push_netlink_subject         (NET_CTX_S* net_ctx, int32_t link);

/**
 * @brief       Update wired network speed mode(IFACE_SPEED_E), save to DATA_MGR_S and notify event manager(EVT_TYPE_NET_ETH_SPEED_CHANGED).
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   mode        : speed mode(IFACE_SPEED_E).
 * @return      Update result
 * @retval      ==0         : success\n
 *              !=0         : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t         netctx_update_wired_speed           (NET_CTX_S* net_ctx, IFACE_SPEED_E mode);

/**
 * @brief       Update hostname, save to DATA_MGR_S and notify event manager(EVT_TYPE_NET_HOST_NAME_CHANGED).
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   hostname    : hostname.
 * @return      Update result
 * @retval      ==0         : success\n
 *              !=0         : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t         netctx_update_hostname              (NET_CTX_S* net_ctx, const char* hostname);

/**
 * @brief       Update hostname, save to DATA_MGR_S and notify event manager(EVT_TYPE_NET_DOMAIN_NAME_CHANGED).
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   domain      : domainname string.
 * @return      Update result
 * @retval      ==0         : success\n
 *              !=0         : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t         netctx_update_domain                (NET_CTX_S* net_ctx, const char* domain);

/**
 * @brief       Update mac address(only support eth0), save to DATA_MGR_S and notify event manager(EVT_TYPE_NET_ETH_MAC_CHANGED).
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   str         : mac attress string(XX:XX:XX:XX:XX:XX).
 * @return      Update result
 * @retval      ==0         : success\n
 *              !=0         : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t         netctx_update_mac_addr              (NET_CTX_S* net_ctx, IFACE_ID_E ifid, const char* str);

/**
 * @brief       Update network interface switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   ifid        : Interface ID(IFACE_ID_E).
 * @param[in]   on          : 0 - off; 1 - on(auto for IFACE_ID_WFD); 2 - on(manual for IFACE_ID_WFD).
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_iface_switch          (NET_CTX_S* net_ctx, IFACE_ID_E ifid, uint32_t on);

/**
 * @brief       Update network interface IPv4 configuration, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   ifid        : Interface ID(IFACE_ID_E).
 * @param[in]   ipv4_conf   : IPv4 configuration.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_ipv4_config           (NET_CTX_S* net_ctx, IFACE_ID_E ifid, NET_IPV4_CONF_S* ipv4_conf);

/**
 * @brief       Update network interface IPv4 DNS configuration, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   ifid        : Interface ID(IFACE_ID_E).
 * @param[in]   dnsv4_conf  : DNSv4 configuration.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_dnsv4_config          (NET_CTX_S* net_ctx, IFACE_ID_E ifid, NET_DNSV4_CONF_S* dnsv4_conf);

/**
 * @brief       Update network interface IPv6 configuration, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   ifid        : Interface ID(IFACE_ID_E).
 * @param[in]   ipv6_conf   : IPv6 configuration.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_ipv6_config           (NET_CTX_S* net_ctx, IFACE_ID_E ifid, NET_IPV6_CONF_S* ipv6_conf);

/**
 * @brief       Start or Stop to scan WiFi SSID. If scan done, save (ap_list) to DATA_MGR_S and notify (WIFI_SCAN_RESULT_S) event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   on          : 0 - stop; 1 - start.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_scan_wifi_ssid               (NET_CTX_S* net_ctx, uint32_t on);

/**
 * @brief       Connect to WiFi by WiFi-Station mode.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   conn        : Connect configuration.
 * @param[in]   sync        : 0 - Asynchronous calling, return after joining the process queue;\n
 *                            1 - return after process function is executed，sta_status should be connecting or disconnected(error);\n
 *                            2 - return after this connection is over.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_connect_wifi_station         (NET_CTX_S* net_ctx, WIFI_CONN_CONF_S* conn, int32_t sync);

/**
 * @brief       Send WPS command to connect WiFi.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   wps_cmd     : WPS mode("pin"/"pbc"/"cancel").
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_request_wps_command          (NET_CTX_S* net_ctx, const char* wps_cmd);

/**
 * @brief       Update WiFi-Direct SSID, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   suffix      : WiFi-Direct SSID suffix.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_wfd_ssid              (NET_CTX_S* net_ctx, const char* suffix);

/**
 * @brief       Update WiFi-Direct password of the AP, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   psk         : WiFi-Direct password.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_wfd_psk               (NET_CTX_S* net_ctx, const char* psk);

/**
 * @brief       Update rawprint switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_rawprint_switch       (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update rawprint port, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : port number.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_rawprint_port         (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update LPD switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_lpd_switch            (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update WSD switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_wsd_switch            (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update SLP switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_slp_switch            (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update bonjour switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_bonjour_switch        (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update bonjour service name, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : service name.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_bonjour_server        (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update IPP switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2024-12-12
 */
void netctx_update_ipp_switch(NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update SNMP switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_switch           (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update SNMP v1v2c switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v1v2c_switch     (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update SNMP v3 switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v3_switch        (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update SNMP v1 community, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : community name.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v1_community     (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update SNMP v2c community, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : community name.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v2_community     (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update SNMP v3 community, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : community name.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v3_community     (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update SNMP v3 user name, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : User name.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v3_user_name     (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update SNMP v3 authorized password, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : Authorized password.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v3_auth_pswd     (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update SNMP v3 Private password, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : Private password.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_snmp_v3_priv_pswd     (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update STMP sender address, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : sender address.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_sender_addr      (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update STMP server address, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : server address.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_server_addr      (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update STMP server port, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : server port.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_server_port      (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update STMP login auth mode, save to DATA_MGR_S and notify event manager.
 * @param[in]   jnet_ctx    : The network context pointer.
 * @param[in]   jvalue      : 0 - disabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_server_auth      (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update STMP security mode, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : SMTP_SEC_E(0 - NONE; 1 - SSL; 2 - STARTTLS).
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_sec_mode         (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update STMP login username, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : username.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_username         (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update STMP login password, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : password.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_smtp_password         (NET_CTX_S* net_ctx, const char* value);

/**
 * @brief       Update STMP login password, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   index       : The index of client address.
 * @param[in]   value       : Email address of client.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_alarm_client_addr     (NET_CTX_S* net_ctx, int32_t index, const char* value);

/**
 * @brief       Enable/Disable email alarm for paper empty, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - disabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_alarm_paper_empty     (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Enable/Disable email alarm for paper jam, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - disabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_alarm_paper_jam       (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Enable/Disable email alarm for toner empty, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - disabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_alarm_toner_empty     (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Enable/Disable email alarm for toner low, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - disabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_alarm_toner_low       (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Enable/Disable email alarm for waste toner, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - disabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_alarm_waste_toner     (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Update mail grouplist, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   op          : address book operation.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t            netctx_update_mail_grouplist        (NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op);

/**
 * @brief       Update mail addrbook, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   op          : address book operation.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t            netctx_update_mail_addrbook         (NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op);

/**
 * @brief       Update FTP addrbook, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   op          : address book operation.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t            netctx_update_ftp_addrbook          (NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op);

/**
 * @brief       Update SMB addrbook, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   op          : address book operation.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t            netctx_update_smb_addrbook          (NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op);

/**
 * @brief       Update whitelist addrbook, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   op          : address book operation.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_whitelist_addrbook    (NET_CTX_S* net_ctx, ADDRBOOK_OP_S* op);

/**
 * @brief       Update custom water mark, save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   index       : The index of custom water mark.
 * @param[in]   value       : Custom water mark.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_water_mark            (NET_CTX_S* net_ctx, uint32_t index, const char* value);

/**
 * @brief       Notify network module event first in the initialization phase of the network module.
 * @param[in]   net_ctx     : The network context pointer.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_notify_event_first           (NET_CTX_S* net_ctx);

/**
 * @brief       Update WHITELIST switch(ON/OFF), save to DATA_MGR_S and notify event manager.
 * @param[in]   net_ctx     : The network context pointer.
 * @param[in]   value       : 0 - dieabled; 1 - enabled.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netctx_update_whitelist_switch           (NET_CTX_S* net_ctx, uint32_t value);

/**
 * @brief       Construct the network context, it used singleton pattern.
 * @return      The network context pointer.
 * @retval      != NULL     : NET_CTX_S object(success)\n
 *              == NULL     : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
NET_CTX_S*      network_context_construct           (void);

/**
 * @brief       Destruct the network context object.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            network_context_destruct            (void);

PT_END_DECLS

#endif /* __NETCTX_H__ */
/**
 *@}
 */
