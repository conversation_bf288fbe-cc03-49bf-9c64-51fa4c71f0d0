#include <stdint.h>
#include "pol/pol_define.h"

const char* s_snmp_language_alias[] =
{
    "zh",   ///< 1   简体中文
    "en",   ///< 2   英语
    "fr",   ///< 3   法语
    "de",   ///< 4   德语
    "he",   ///< 5   希伯来语
    "it",   ///< 6   意大利语
    "ja",   ///< 7   日语
    "pl",   ///< 8   波兰语
    "ru",   ///< 9   俄语
    "es",   ///< 10  西班牙语
    "tw",   ///< 11  繁体中文
    "tr",   ///< 12  土耳其语
    "ar",   ///< 13  阿拉伯语
    "pt",   ///< 14  葡萄牙语
    "ko",   ///< 15  韩语
    "cs",   ///< 16  捷克语
    "hu",   ///< 17  匈牙利语
    "el",   ///< 18  希腊语
    "ro",   ///< 19  罗马尼亚语
    "th",   ///< 20  泰语
    "ti"    ///< 21  越南语
};

uint32_t s_snmp_language_alias_count = ARRAY_SIZE(s_snmp_language_alias);

const char* s_snmp_country_alias[] =
{
    "AR",   ///< 1   阿根廷
    "AU",   ///< 2   澳大利亚
    "AT",   ///< 3   奥地利
    "BY",   ///< 4   白俄罗斯
    "BE",   ///< 5   比利时
    "BO",   ///< 6   玻利维亚
    "BR",   ///< 7   巴西
    "BN",   ///< 8   文莱
    "BG",   ///< 9   保加利亚
    "KH",   ///< 10  柬埔寨
    "CA",   ///< 11  加拿大
    "TD",   ///< 12  乍得
    "CL",   ///< 13  智利
    "CN",   ///< 14  中国
    "CO",   ///< 15  哥伦比亚
    "KM",   ///< 16  哥斯达黎加
    "HR",   ///< 17  克罗地亚
    "CU",   ///< 18  古巴
    "CY",   ///< 19  塞浦路斯
    "CZ",   ///< 20  捷克
    "DK",   ///< 21  丹麦
    "DM",   ///< 22  多米尼加
    "EC",   ///< 23  厄瓜多尔
    "EG",   ///< 24  埃及
    "UK",   ///< 25  英格兰
    "EE",   ///< 26  爱沙尼亚
    "FJ",   ///< 27  斐济
    "FI",   ///< 28  芬兰
    "FR",   ///< 29  法国
    "DE",   ///< 30  德国
    "GR",   ///< 31  希腊
    "GT",   ///< 32  危地马拉
    "HN",   ///< 33  洪都拉斯
    "HK",   ///< 34  香港
    "HU",   ///< 35  匈牙利
    "IS",   ///< 36  冰岛
    "IN",   ///< 37  印度
    "ID",   ///< 38  印尼
    "IR",   ///< 39  伊朗
    "IQ",   ///< 40  伊拉克
    "IE",   ///< 41  爱尔兰
    "IL",   ///< 42  以色列
    "IT",   ///< 43  意大利
    "JP",   ///< 44  日本
    "JO",   ///< 45  约旦
    "KZ",   ///< 46  哈萨克斯坦
    "KP",   ///< 47  韩国
    "KW",   ///< 48  科威特
    "LV",   ///< 49  拉脱维亚
    "LB",   ///< 50  黎巴嫩
    "LI",   ///< 51  列支敦斯登
    "LT",   ///< 52  立陶宛
    "LU",   ///< 53  卢森堡
    "MK",   ///< 54  马其顿
    "MY",   ///< 55  马来西亚
    "MT",   ///< 56  马耳他
    "MX",   ///< 57  墨西哥
    "MC",   ///< 58  摩纳哥
    "MN",   ///< 59  蒙古
    "MA",   ///< 60  摩洛哥
    "NL",   ///< 61  荷兰
    "NZ",   ///< 62  新西兰
    "NA",   ///< 63  北非
    "NO",   ///< 64  挪威
    "OM",   ///< 65  阿曼
    "PK",   ///< 66  巴基斯坦
    "PA",   ///< 67  巴拿马
    "PY",   ///< 68  巴拉圭
    "PE",   ///< 69  秘鲁
    "PH",   ///< 70  菲律宾
    "PL",   ///< 71  波兰
    "PT",   ///< 72  葡萄牙
    "RO",   ///< 73  罗马尼亚
    "RU",   ///< 74  俄罗斯
    "WS",   ///< 75  萨摩亚
    "SA",   ///< 76  沙特阿拉伯
    "SG",   ///< 77  新加坡
    "SK",   ///< 78  斯洛伐克
    "SI",   ///< 79  斯洛文尼亚
    "ZA",   ///< 80  南非
    "KR",   ///< 81  南韩
    "ES",   ///< 82  西班牙
    "LK",   ///< 83  斯里兰卡
    "SD",   ///< 84  苏丹
    "SE",   ///< 85  瑞典
    "CH",   ///< 86  瑞士
    "SY",   ///< 87  叙利亚
    "TW",   ///< 88  台湾
    "TH",   ///< 89  泰国
    "TN",   ///< 90  突尼斯
    "TR",   ///< 91  土耳其
    "AE",   ///< 92  阿联酋
    "UK",   ///< 93  英国
    "UA",   ///< 94  乌克兰
    "US",   ///< 95  美国
    "UY",   ///< 96  乌拉圭
    "VE",   ///< 97  委内瑞拉
    "VN",   ///< 98  越南
    "YE",   ///< 99  也门
    "GE",   ///< 100 格鲁吉亚
    "AZ",   ///< 101 阿塞拜疆
    "LA",   ///< 102 拉丁美洲
    "CG",   ///< 103 刚果
    "JM",   ///< 104 牙买加
    "RS",   ///< 105 塞尔维亚
    "MV",   ///< 106 马尔代夫
    "DZ",   ///< 107 阿尔及利亚
    "BD",   ///< 108 孟加拉国
    "BA",   ///< 109 波黑
    "ME",   ///< 110 黑山
    "XK"    ///< 111 科索沃
};

uint32_t s_snmp_country_alias_count = ARRAY_SIZE(s_snmp_country_alias);
