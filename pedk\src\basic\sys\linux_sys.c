/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file linux_sys.c
 * @addtogroup basic
 * @{
 * @addtogroup basic
 * @autor 
 * @date 2024-06-11
 * @brief log config
 */
 
#include<sys/select.h>
#include<sys/select.h>
#include<time.h>
#include<pthread.h>
#include<string.h>
#include<stdio.h>
#include<unistd.h>
#include<stdlib.h>

#include"basic/sys/sys.h"
#include"basic/log/log.h"

#ifdef TRANS_MONITOR
/**
 * @brief 终端输入
 * 
 * @param buffer :终端输入数据值；输出
 * @param length :终端输入数据长度；输出
 * @param ms     :等待终端输入的最长时间。如果ms为0，则死等；输入
 * @return int32_t ：0 ：输入成功
 *                   -1：输入失败
 */
int32_t monitor_in(char *buffer, uint16_t *length, uint32_t ms)
{
    fd_set rfds;
    struct timeval tv;
    int32_t retval;
    int32_t  ret = -1;

    //清空文件描述符集合
    FD_ZERO(&rfds);
    //将标准输入文件描述符添加到集合中
    FD_SET(0,&rfds);

    fflush(stdin);//清除标准输入缓存
    fflush(stdout);
    //设置超时时间
    if(0 == ms){
        //监视标准输入是否有输入，死等
        retval = select(1,&rfds,NULL,NULL,NULL);
    }else{
        tv.tv_sec = ms / 1000;
        tv.tv_usec = ms % 1000;
        //监视标准输入是否有输入，超时退出
        retval = select(1,&rfds,NULL,NULL,&tv);
    }

    //检查输入
    if(retval == -1){
        LOG_E("linux_sys","select error");
    }else if(retval){
        //标准输入有输入
        char temp[BUFFER_MAX_LENGTH];
        char *r;
        r = fgets(temp, BUFFER_MAX_LENGTH, stdin);
        *length = (uint16_t)strlen((char*)temp) + 1;
        memcpy(buffer, temp, *length);
        ret = 0;
    }else{
        //超时
    }

    return ret;
}
#endif

/**
 * @brief 创建新线程
 * 
 * @param prt ：运行时；输入
 * @return int32_t ：pthread_create的返回值。
 */
int32_t pesf_new_thread(PeSFRunTime *prt)
{
    pthread_t tid;
    int32_t ret = 0;

    LOG_D("sys","pesf_new_thread start");
    ret = pthread_create(&tid, NULL, prt->start_func, prt);
    if(0 != ret){
        LOG_E("sys","create thread error %d\n", ret);
    }
    else{
        prt->thread_handle = (uint64_t)tid;
    }

    return ret;
}

/**
 * @brief   pthread try join np
 * @param[in] thread    :thread tid
 * @param[in] **retval  :return val
 * @return  Construct result
 * @retval  char* : char str return
 * <AUTHOR> @date    2024-06-11
 */
static int pthread_try_join_np(pthread_t thread, void **retval) {
    int err;
    if ((err = pthread_join(thread, retval)) == 0) {
        return 0;
    } else if (err == EDEADLK) {
        return EBUSY;  // EDEADLK表示线程已经退出或者不能被加入，这里将其转换为EBUSY表示线程还在运行
    } else {
        return err;
    }
}

/**
 * @brief   pthread try join np timeot
 * @param[in] thread    :thread tid
 * @param[in] **retval  :return val
 * @param[in] *abstime  :timeout val
 * @return  Construct result
 * @retval  char* : char str return
 * <AUTHOR> @date    2024-06-11
 */

static int pthread_timed_join_np(pthread_t thread, void **retval, const struct timespec *abstime) 
{
    int err;
    struct timespec now;
    struct timespec timeout;
    clock_gettime(CLOCK_REALTIME, &now);
    timeout.tv_sec = abstime->tv_sec - now.tv_sec;
    timeout.tv_nsec = abstime->tv_nsec - now.tv_nsec;
    if (timeout.tv_nsec < 0) {
        timeout.tv_sec--;
        timeout.tv_nsec += 1000000000;
    }

    if ((err = pthread_try_join_np(thread, retval)) == 0) {
        return 0;
    } else if (err == EBUSY) {
        pthread_cond_t cond = PTHREAD_COND_INITIALIZER;
        pthread_mutex_t mutex = PTHREAD_MUTEX_INITIALIZER;
        pthread_mutex_lock(&mutex);
        int ret = pthread_cond_timedwait(&cond, &mutex, abstime);
        pthread_mutex_unlock(&mutex);
        return ret;
    } else {
        return err;
    }
}

/**
 * @brief 线程退出检查函数
 *        检查指定tid的线程是否在END_WAIT_TIME时间内退出，如果没有成功退出，则强制杀掉该线程
 * @param prt ：运行时；IN
 * @return int32_t 
 */
int32_t join_thread_exit(PeSFRunTime *prt)
{
    struct timespec timeout;
    pthread_t tid = (pthread_t)prt->thread_handle;
    int ret;

    LOG_D("sys","join_thread_exit start");
    clock_gettime(CLOCK_REALTIME, &timeout);
    timeout.tv_sec += END_WAIT_TIME;
    
    //监视线程是否退出
    void *retval;
    //检查指定tid的线程是否在END_WAIT_TIME时间内退出，如果没有成功退出，则强制杀掉该线程
    ret = pthread_timed_join_np(tid, &retval, &timeout);
    if(ret == ETIMEDOUT){
        //超时没有退出，则强制关闭该线程
        pthread_cancel(tid);
        LOG_I("sys","exit thread timeout\n");
        ret = 0;
    }else if(ret == 0){
        //在指定时间内退出了
        LOG_I("sys","exit thread success\n");
    }else{
        //退出的过程中出错了
        LOG_E("sys","pthread_timedjoin_np");
        ret = -1;
    }

    return ret;
}
/**
 * @brief 强制删除指定运行的线程
 * 
 * @param prt 
 * @return int32_t 
 */
int32_t thread_exit(PeSFRunTime *prt)
{
    pthread_t tid = (pthread_t)prt->thread_handle;
    
    //强制关闭该线程
    pthread_cancel(tid);
}

/**
 * @brief 获取系统时间，格式化字符串返回
 * 
 * @return char* 
 */
int64_t get_sys_time(void)
{
    LOG_D("sys","get_sys_time start");
    return (int64_t)time(NULL);;
}

char* sys_time_to_str(int64_t tm)
{
    LOG_D("sys","sys_time_to_str start");
    char *c_time_string;
    char *ret_str;

    c_time_string = ctime((time_t*)&tm);

    ret_str = (char*)malloc(strlen(c_time_string) + 1);
    strcpy(ret_str,c_time_string);

    //此时最后一个字符会多一个'\n',后续需要存到json中，不需要'\n'
    ret_str[strlen(c_time_string) - 1] = '\0';

    return ret_str;
}

/**
 * @}
 */
