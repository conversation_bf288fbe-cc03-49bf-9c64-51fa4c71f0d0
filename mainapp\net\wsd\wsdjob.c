#include "pol/pol_define.h"
#include "pol/pol_threads.h"
#include "nettypes.h"
#include "wsdjob.h"
#include "netjob.h"
#include "netmodules.h"
#include "netport.h"
#include "netmisc.h"
#include "wsdef.h"
#include "wsd.h"
#include "qxml.h"
#include "soap.h"

//#define RETURN_VAL_IF(condition, log, val)  return val

#define POOL_JOB_CAPACITY 8
static uint64_t s_evtdata = 1;

typedef struct wsd_job_pool
{
    PI_MUTEX_T pool_mutex;
    unsigned      job_idx;
    unsigned      capacity;
    WSD_TICKET_S* job_arr[0];
} WSD_JOB_POOL_S;

typedef struct wsd_job_context
{
    WSD_JOB_POOL_S* job_pool;
    PI_THREAD_T     tid;
} WSD_JOB_CTX_S;

static WSD_JOB_CTX_S* s_wsd_job_ctx;

int wsd_job_pool_init(WSD_JOB_POOL_S* pool)
{
    return 0;
}

void wsd_job_pool_destroy(WSD_JOB_POOL_S* pool)
{
    if (pool)
    {
        if (pool->pool_mutex != INVALIDMTX)
        {
            pi_mutex_destroy(pool->pool_mutex);
        }
        pi_free(pool);
    }
}


WSD_JOB_POOL_S* wsd_job_pool_create(int pool_capacity)
{
    WSD_JOB_POOL_S* pool = (WSD_JOB_POOL_S*)pi_zalloc( sizeof(*pool) + pool_capacity * sizeof(WSD_TICKET_S));
    RETURN_VAL_IF(pool == NULL || pool_capacity <= 0, NET_WARN, NULL);

    do
    {
        pool->capacity = pool_capacity;
        pool->pool_mutex = pi_mutex_create();
        BREAK_IF(pool->pool_mutex == NULL, NET_WARN);
        return pool;
    } while (0);
    wsd_job_pool_destroy(pool);
    return NULL;
}

/* @fn     wsd_job_register
 * @brief  注册一个作业, 并且分配一个jobid
 * @para   ticket
 * @return 0 - 成功
 *        -1 - 失败
 * <AUTHOR>
 * */
int wsd_job_register(WSD_SVC_S* svc_type, WSD_TICKET_S* ticket)
{
    int found_idx = -1;
    WSD_JOB_POOL_S* pool = svc_type->job_pool;
    pi_mutex_lock(pool->pool_mutex);
    if ( pool->job_idx >= pool->capacity )
    {
        for(size_t i = 0; i < pool->capacity; i++)
        {
            if (pool->job_arr[i]->state == NETJOB_STATE_DONE ||
                pool->job_arr[i]->state == NETJOB_STATE_CANCELED ||
                pool->job_arr[i]->state == NETJOB_STATE_ABORTED
               )
            {
                found_idx = i;
                svc_type->ticket_destroy(pool->job_arr[i]);
                break;
            }
        }
        if (found_idx < 0)
        {
            NET_DEBUG("Too many WSD jobs, reuse one\n");
            for(size_t i = 0; i < pool->capacity; i++)
            {
                NET_DEBUG("job: %d, state: %d", pool->job_arr[i]->job_id, pool->job_arr[i]->state);
            }
            found_idx = 0;
        }
    }
    else
    {
        found_idx = pool->job_idx++;
    }

    if (found_idx >= 0)
    {
        pool->job_arr[found_idx] = ticket;
        NET_DEBUG("Register wsd job %d", found_idx);
    }

    pi_mutex_unlock(pool->pool_mutex);
    return found_idx >= 0 ? 0 : -1;
}

/* @fn     wsd_job_unregister
 * @brief  注册一个作业
 * @para   ticket
 * @return 0 - 成功
 *        -1 - 失败
 * <AUTHOR>
 * */
#if 0
int wsd_job_unregister(WSD_TICKET_S* ticket)
{
    int ret = -1;

    WSD_JOB_POOL_S* pool = s_wsd_job_ctx->job_pool;
    pi_mutex_lock(pool->pool_mutex);
    FOREACH_ARRAY(pool->job_arr)
    {
        if (pool->job_arr[_i] == ticket)
        {
            NET_DEBUG("Unregistered %p\n", ticket);
            pool->job_arr[_i]= NULL;
            --pool->job_cnt;
            ret = 0;
            break;
        }
    }
    if (ret)
    {
        NET_ERROR("WSD job %p has not registered\n", ticket);
    }
    pi_mutex_unlock(pool->pool_mutex);
    return ret;
}
#endif

/* @fn     WSD_TICKET_S*get_from_system_job_id
 * @brief  通过作业Id取作业ticket
 * @para   id_- WSD的系统作业ID
 * @return ticket or NULL
 * */
WSD_TICKET_S* wsd_ticket_get_from_job_id(WSD_SVC_S* svc_type, int job_id)
{
    WSD_TICKET_S*   ticket = NULL;
    WSD_TICKET_S*   cursor = NULL;
    WSD_JOB_POOL_S* pool = svc_type->job_pool;

    pi_mutex_lock(pool->pool_mutex);
    for(size_t i = 0; i < pool->capacity; i++)
    {
        cursor = pool->job_arr[i] ;
        if (cursor && cursor->job_id == job_id)
        {
            ticket = cursor;
        }
    }
    pi_mutex_unlock(pool->pool_mutex);
    return ticket;
}

#if 0
/* @fn     wsd_job_end_notice
 * @brief  通知一个WSD作业的数据已接收完毕
 * @para   该作业关联的QIO
 * @return 无
 * <AUTHOR> Jiaxian
 * */
void wsd_job_end_notice(QIO_S* qio)
{
    int i;

    if (wsd_job_pool_init())
    {
        return;
    }

    WJOB_LOCK();
    for (i = 0; i < JOB_POOL_NUM; i++)
    {
        if (s_job_pool[i].in_use)
        {
            if (s_job_pool[i].qio == qio)
            {
                s_job_pool[i].job_end = 1;
                WJOB_UNLOCK();
                return;
            }
        }
    }
    NET_ERROR("qio %p has not registered in pool\n", qio);
    WJOB_UNLOCK();
    return;
}

/* @fn     wsd_job_set_error
 * @brief  为WSD作业设置错误标志
 * @para   该作业关联的QIO
 * @return 无
 * <AUTHOR> Jiaxian
 * */
void wsd_job_set_error(QIO_S* qio)
{
    int i;

    if (wsd_job_pool_init())
    {
        return;
    }

    WJOB_LOCK();
    for (i = 0; i < JOB_POOL_NUM; i++)
    {
        if (s_job_pool[i].in_use)
        {
            if (s_job_pool[i].qio == qio)
            {
                s_job_pool[i].is_err = 1;
                WJOB_UNLOCK();
                return;
            }
        }
    }
    NET_ERROR("qio %p has not registered in pool\n", qio);
    WJOB_UNLOCK();
    return;
}

/* @fn     wsd_job_get_qio_from_job
 * @brief  get job qio by job id
 * @para   jobId
 * @return PQIO
 * <AUTHOR>
 * */
QIO_S* wsd_job_get_qio_from_job(int jobId)
{
    int i;
    QIO_S* pqio = NULL;

    if (wsd_job_pool_init())
    {
        return pqio;
    }

    WJOB_LOCK();
    for (i = 0; i < JOB_POOL_NUM; i++)
    {
        if (  s_job_pool[i].in_use && s_job_pool[i].ticket
           && s_job_pool[i].ticket->jobId == jobId  )
        {
            pqio = s_job_pool[i].qio;
            break;
        }
    }
    WJOB_UNLOCK();

    return pqio;
}

/* @fn     wsd_jobid_set_error
 * @brief  为WSD作业设置错误标志
 * @para   该作业关联的Job ID
 * @return 无
 * <AUTHOR> Jiaxian
 * */
void wsd_jobid_set_error(int jobId)
{
    int i;

    if (wsd_job_pool_init())
    {
        return;
    }

    WJOB_LOCK();
    for (i = 0; i < JOB_POOL_NUM; i++)
    {
        if (s_job_pool[i].in_use)
        {
            if (s_job_pool[i].ticket && (s_job_pool[i].ticket->jobId == jobId))
            {
                s_job_pool[i].is_err = 1;
                WJOB_UNLOCK();
                return;
            }
        }
    }
    NET_ERROR("job %d has not registered in pool\n", jobId);
    WJOB_UNLOCK();
    return;
}

/* @fn     wsd_job_is_error
 * @brief  返回WSD作业的错误状态
 * @para   该作业关联的QIO
 * @return 0 - OK
 *         1 - Error
 * <AUTHOR> Jiaxian
 * */
int wsd_job_is_error(QIO_S* qio)
{
    int is_err;
    int i;

    if (wsd_job_pool_init())
    {
        return 1;
    }

    WJOB_LOCK();
    for (i = 0; i < JOB_POOL_NUM; i++)
    {
        if (s_job_pool[i].in_use)
        {
            if (s_job_pool[i].qio == qio)
            {
                is_err = s_job_pool[i].is_err;
                WJOB_UNLOCK();
                return is_err;
            }
        }
    }
    NET_ERROR("qio %p has not registered in pool\n", qio);
    WJOB_UNLOCK();
    return 1;
}
#endif

#if 0
static void wsd_job_epilog(void)
{
    if ( s_wsd_job_ctx != NULL )
    {
        pi_free(s_wsd_job_ctx);
        s_wsd_job_ctx = NULL;
    }
}

static int32_t wsd_job_prolog(WSD_CTX_S* wsd_ctx)
{
    int32_t ret = -1;

    s_wsd_job_ctx = (WSD_JOB_CTX_S*)pi_zalloc(sizeof(WSD_JOB_CTX_S));
    RETURN_VAL_IF(s_wsd_job_ctx == NULL, NET_WARN, -1);

    do
    {
        s_wsd_job_ctx->job_pool = wsd_job_pool_create();
        BREAK_IF(s_wsd_job_ctx->job_pool == NULL, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WSD initialize result(%d)", ret);
    if ( ret != 0 )
    {
        wsd_job_epilog();
    }
    return ret;
}

FUNC_EXPORT(init, wsd_job_prolog);
FUNC_EXPORT(deinit, wsd_job_epilog);
#endif
