/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file printer_param.h
 * @addtogroup printer_status
 * @{
 * @addtogroup printer_param
 * <AUTHOR>
 * @date 2023-06-12
 * @brief Some status information includes printer dynamic and printer static characeristics,
 *        and some other status property information.
 */

#ifndef _PRINTER_PARAM_H_
#define _PRINTER_PARAM_H_

/* @brief printer dynamic status member:status data
 */
typedef struct tag_status_data
{
    char id[4];                                    ///< status id
    char param[4];                                 ///< status param
} StatusData;

#pragma pack(1)

/* @brief struct for printer dynamic status
 */
typedef struct tag_dynamic_status
{
    unsigned char       struct_ver[2];              ///< struct version number
    unsigned char       printer_status;             ///< current printer status
    unsigned char       sleep_time1;                ///< sleep time1
    unsigned char       sleep_time2;                ///< sleep time2
    unsigned char       sleep_time3;                ///< sleep time3
    unsigned char       toner_k_remain;             ///< Toner surplus--black
    unsigned char       toner_c_remain;             ///< Toner surplus--cyan
    unsigned char       toner_m_remain;             ///< Toner surplus--magenta
    unsigned char       toner_y_remain;             ///< Toner surplus--yellow
    unsigned char       cartridge_k_remain;         ///< Toner cartridge remaining---black
    unsigned char       cartridge_c_remain;         ///< Toner cartridge remaining---cyan
    unsigned char       cartridge_m_remain;         ///< Toner cartridge remaining---magenta
    unsigned char       cartridge_y_remain;         ///< Toner cartridge remaining---yellow
    unsigned char       res_1[8];
    /* job info */
    unsigned char       job_attr[32];               ///< @PJL SET JOBATTR
    unsigned char       job_id[4];                  ///< Job ID
    unsigned char       job_sn[2];                  ///< Job SN
    unsigned char       job_status[2];              ///< job status
    unsigned char       job_type[4];                ///< job type,0-print，1-copy，2-scan,3-fax
    unsigned char       job_copies[4];              ///< job copies number
    unsigned char       job_pages[4];               ///< job pages
    /* print status  */
    StatusData          status_list[20];            ///< statuses list(1~20)
    unsigned char       res_2[40];
    unsigned char       error_mode;                 ///< error handling mode
    unsigned char       duplex;                     ///< duplex print flags
    unsigned char       finisher;                   ///< finisher support info
    unsigned char       tray_install;               ///< tray installation information
    unsigned char       system_time[4];             ///< system time
    unsigned char       water_enable;               ///< water enable
    unsigned char       water_config;               ///< water config
    unsigned char       water_len;                  ///< the length of the hidden watermark
    unsigned char       res_3;
    unsigned char       water_data[64];             ///< water data
    unsigned char       finger_config;              ///< finger print configuration information
    unsigned char       finger_enroll;              ///< finger enroll
    unsigned char       finger_delete;              ///< delete finger
    unsigned char       finger_m_status;            ///< fingerprint module status
    unsigned char       finger_job_id[4];           ///< fingerprint job ID
    unsigned char       job_delete_req;             ///< job delete request
    unsigned char       res_4;
    unsigned char       host_id[32];                ///< host ID
    unsigned char       fns_install[2];             ///< finisher installation information
    unsigned char       fns_ex[4];                  ///< finisher Expanded items installation information
}DYNAMIC_STATUS_S, *DYNAMIC_STATUS_P;

#pragma pack()

#pragma pack(1)
/* @brief struct for printer static status
*/
typedef struct tag_static_status
{
    unsigned char       struct_ver[2];                  ///< struct version number
    unsigned char       firmware_ver[8];                ///< firmware version
    unsigned char       boot_ver[8];                    ///< boot version
    unsigned char       kernel_ver[8];                  ///< kernel versio
    unsigned char       card_memorysize[4];             ///< memory card size(/M)
    unsigned char       product_date[16];               ///< date of manufacture of product
    unsigned char       product_batch_number[32];       ///< product batches number
    unsigned char       engine_ver[8];                  ///< engine firmware version
    unsigned char       serial_num[32];                 ///< product serial number
    unsigned char       ipaddress[4];                   ///< printer ip address
    unsigned char       ipv6_linklocal[16];             ///< IPV6 LinkLocal address
    unsigned char       ipv6_stateless[16];             ///< IPV6 Stateless address
    unsigned char       ipv6_statefull[16];             ///< IPV6 Statefull address
    unsigned char       total_page_count[4];            ///< total printed pages count
    unsigned char       mono_page_count[4];             ///< mono printed pages count
    unsigned char       color_page_count[4];            ///< color printed pages count
    unsigned char       duplex_page_count[4];           ///< duplex printed pages count
    unsigned char       custom_page_count[4];           ///< user defined paper pages count
    unsigned char       a3_page_count[4];               ///< A3 printed pages count
    unsigned char       a4_page_count[4];               ///< A4 printed pages count
    unsigned char       a5_page_count[4];               ///< A5 printes pages count
    unsigned char       a6_page_count[4];               ///< A6 printes pages count
    unsigned char       b4_page_count[4];               ///< B4 printed pages count
    unsigned char       jisB5_page_count[4];            ///< JIS B5 printed pages count
    unsigned char       isoB5_page_count[4];            ///< ISO B5 printed pages count
    unsigned char       isoB6_page_count[4];            ///< ISO B6 printed pages count
    unsigned char       p11x17_page_count[4];           ///< 11 "x17" printed pages count
    unsigned char       letter_page_count[4];           ///< Letter printed pages count
    unsigned char       legal_page_count[4];            ///< Legal printed pages count
    unsigned char       folio_page_count[4];            ///< Folio printed pages count
    unsigned char       oficio_page_count[4];           ///< Oficio printed pages count
    unsigned char       executive_page_count[4];        ///< Executive printed pages count
    unsigned char       statement_page_count[4];        ///< Statement printed pages count
    unsigned char       p8k_page_count[4];              ///< 8K printed pages count
    unsigned char       p16k_page_count[4];             ///< 16k printed pages count
    unsigned char       big_16k_page_count[4];          ///< BIG 16k printed pages count
    unsigned char       p32k_page_count[4];             ///< 32k printed pages count
    unsigned char       big_32k_page_count[4];          ///< BIG 32k printed pages count
    unsigned char       env10_page_count[4];            ///< No.10 Env printed pages count
    unsigned char       envmon_page_count[4];           ///< Monarch Env printed pages count
    unsigned char       envc6_page_count[4];            ///< C6 Env printed pages count
    unsigned char       envc5_page_count[4];            ///< C5 Env printed pages count
    unsigned char       envdl_page_count[4];            ///< DL Env printed pages count
    unsigned char       envzl_page_count[4];            ///< ZL Env printed pages count
    unsigned char       yougata2_page_count[4];         ///< Yougata2 Env printed pages count
    unsigned char       yougata3_page_count[4];         ///< Yougata3 printed pages count
    unsigned char       yougata4_page_count[4];         ///< Yougata4 printed pages count
    unsigned char       nafagata3_page_count[4];        ///< Nagagata3 printed pages count
    unsigned char       postcard_page_count[4];         ///< Postcard printed pages count
    unsigned char       japan_postcard_page_count[4];   ///< Japanese Postcard printed pages count
    unsigned char       res_1[12];                      ///< other paper pages count
    unsigned char       cancel_page_count[4];           ///< cancel pages count
    unsigned char       jam_page_count[4];              ///< jam page count
    unsigned char       cyan_life[4];                   ///< cyan toner cartridge life
    unsigned char       magenta_life[4];                ///< magenta toner cartridge life
    unsigned char       yellow_life[4];                 ///< yellow toner cartridge life
    unsigned char       black_life[4];                  ///< black toner cartridge life
    unsigned char       cyan_printed_count[4];          ///< cyan toner cartridge printed pages
    unsigned char       magenta_printed_count[4];       ///< magenta toner cartridge printed pages
    unsigned char       yellow_printed_count[4];        ///< yellow toner cartridge printed pages
    unsigned char       black_printed_count[4];         ///< black toner cartridge printed pages
    unsigned char       engine_ver2[32];                ///< Third party of engine firmware version
    unsigned char       sole_serial_num[16];            ///< printer sole serial number
}STATIC_STATUS_S, *STATIC_STATUS_P;

#pragma pack()


/*
 * @brief Information about an audit job
 */
typedef struct audit_job_info
{
    unsigned int        job_id;                     ///< current job ID
    unsigned int        copies;                     ///< current job copies
    unsigned int        total_pages;                ///< current job total pages
    unsigned int        print_pages;                ///< current job print pages
    unsigned int        job_source;                 ///< current job source
    char                job_owner[32];              ///< current job owner
}
AUDIT_JOB_INFO_S;

/*
 * @brief The last three audit jobs information
 */
typedef struct audit_jobs_info
{
    AUDIT_JOB_INFO_S    job_info[3];
}
AUDIT_JOBS_INFO_S;

typedef struct tag_trc_info
{
    char                trc_c[256];                 ///< C ch calibration vurve data
    char                trc_m[256];                 ///< M ch calibration vurve data
    char                trc_y[256];                 ///< Y ch calibration vurve data
    char                trc_k[256];                 ///< K ch calibration vurve data
    unsigned char       color_mode;                 ///< color mode        1-color 0-mono
    unsigned char       toner_save;                 ///< save toner mode   1-open  0-close
    unsigned char       destiny;                    ///< destiny           1(light)-5(dark)
    unsigned char       res[5];                     ///< res
    unsigned int        crc;                        ///< CRC
}
TRC_INFO_S;

#endif /* _PRINTER_PARAM_H_ */
/**
 * @}
 */

