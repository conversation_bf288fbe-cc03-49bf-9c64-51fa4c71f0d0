/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file nettypes.h
 * @addtogroup net
 * @{
 * @addtogroup nettypes
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network common type defined
 */
#ifndef __NETTYPES_H__
#define __NETTYPES_H__

#include <pol/pol_convert.h>
#include <pol/pol_define.h>
#include <pol/pol_endian.h>
#include <pol/pol_io.h>
#include <pol/pol_mem.h>
#include <pol/pol_socket.h>
#include <pol/pol_string.h>
#include <pol/pol_threads.h>
#include <pol/pol_time.h>
#include <pol/pol_list.h>
#include <pol/pol_log.h>
#include <ptm_base64.h>
#include <hal.h>

#if defined (Linux)

#include <stdarg.h>
#include <signal.h>
#include <sys/queue.h>
#include <sys/cdefs.h>
#include <sys/wait.h>
#include <sys/ioctl.h>
#include <arpa/inet.h>
#include <netinet/tcp.h>
#include <netinet/in.h>
#include <net/route.h>
#include <net/if_arp.h>
#include <net/if.h>
#include <netdb.h>
#include <pthread.h>
#include <linux/if_packet.h>
#include <linux/ethtool.h>
#include <linux/netlink.h>
#include <linux/rtnetlink.h>
#include <linux/sockios.h>
#include <libgen.h>
#include <errno.h>

#define _PTC_   '/'
#define _PTS_   "/"

#endif /* defined(Linux) */

#include "cjson/cJSON.h"

/*
 * brief from libcommon
 */
#include "event_manager/event_msg_typedef.h"
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_mgr.h"
#include "qio/qio_general.h"
#include "qio/qioctl.h"
#include "security/des.h"
#include "utilities/event_observer.h"
#include "utilities/parsercommon.h"
#include "utilities/msgrouter.h"
#include "utilities/systime.h"

/*
 * brief from other module in mainapp
 */
#include "parser/parser_public.h"
#include "parser/print_parser_internal_page_store.h" ///< for EWS export device report
#include "security_register.h"
#include "public_data_proc.h"
#include "msgrouter_main.h"
#include "platform_api.h"
#include "location_api.h"
#include "printer_param.h"
#include "printer_info.h"
#include "usbdservice.h"
#include "network.h"
#include "statusid.h"
#include "httc_audit.h"
#include "scan_status.h"
#include "scan_event.h"
#include "scan_var.h"
#include "print.h"

/*
 * brief from other common module in net
 */
#include "netifctl.h"
#include "netqio.h"

extern int32_t g_net_log_lvl;

#define NET_FATAL(fmt, ...)     do { if(g_net_log_lvl >= NET_LOG_LVL_F){printf("%-32s - [FATAL]"fmt"\n", __func__, ##__VA_ARGS__);} pi_log_f(fmt "\n", ##__VA_ARGS__); } while(0)
#define NET_ERROR(fmt, ...)     do { if(g_net_log_lvl >= NET_LOG_LVL_E){printf("%-32s - [ERROR]"fmt"\n", __func__, ##__VA_ARGS__);} pi_log_e(fmt "\n", ##__VA_ARGS__); } while(0)
#define NET_WARN(fmt, ...)      do { if(g_net_log_lvl >= NET_LOG_LVL_W){printf("%-32s - [WARN ]"fmt"\n", __func__, ##__VA_ARGS__);} pi_log_w(fmt "\n", ##__VA_ARGS__); } while(0)
#define NET_INFO(fmt, ...)      do { if(g_net_log_lvl >= NET_LOG_LVL_I){printf("%-32s - [INFO ]"fmt"\n", __func__, ##__VA_ARGS__);} pi_log_i(fmt "\n", ##__VA_ARGS__); } while(0)
#define NET_DEBUG(fmt, ...)     do { if(g_net_log_lvl >= NET_LOG_LVL_D){printf("%-32s - [DEBUG]"fmt"\n", __func__, ##__VA_ARGS__);} pi_log_d(fmt "\n", ##__VA_ARGS__); } while(0)
#define NET_TRACE(fmt, ...)     do { if(g_net_log_lvl >= NET_LOG_LVL_T){printf("%-32s - [TRACE]"fmt"\n", __func__, ##__VA_ARGS__);} pi_log_t(fmt "\n", ##__VA_ARGS__); } while(0)
#define NET_NONE(fmt, ...)      NULL

#define NETEVT_NOTIFY_S(e, v)   if ( e > 0 )    {                                           \
    const char* _v = (const char *)(v);                                                     \
    if ( _v ) {                                                                             \
        pi_event_mgr_notify(net_ctx->event_client, e, _v, (uint32_t)strlen(_v));            \
        NET_DEBUG("notify event(0x%08x) str(%s)", e, _v);                                   \
    } else {                                                                                \
        pi_event_mgr_notify(net_ctx->event_client, e, _v, 0);                               \
        NET_DEBUG("notify event(0x%08x) NULL", e);                                          \
    }                                                                                       \
}

#define NETEVT_NOTIFY_I(e, v)   if ( e > 0 )    {                                           \
    uint32_t _v = (uint32_t)(v);                                                            \
    pi_event_mgr_notify(net_ctx->event_client, e, &(_v), (uint32_t)sizeof(_v));             \
    NET_DEBUG("notify event(0x%08x) val(%u)", e, _v);                                       \
}

#define NETEVT_NOTIFY_T(e, v)   if ( e > 0 )    {                                           \
    pi_event_mgr_notify(net_ctx->event_client, e, &(v), (uint32_t)sizeof(v));               \
    NET_DEBUG("notify event(0x%08x) size(%u)", e, sizeof(v));                               \
}

#define cJSON_AddExtendToObject(o, n, fmt, ...) {                                           \
    snprintf(temp_str, sizeof(temp_str), fmt, ##__VA_ARGS__);                               \
    cJSON_AddStringToObject(o, n, temp_str);                                                \
}

#define CJSON_GET_VALUESTRING(obj, title, val) {                                            \
        json_temp = cJSON_GetObjectItem(obj, title);                                        \
        if ( json_temp && json_temp->valuestring ) {                                        \
            snprintf(val, sizeof(val), "%s", json_temp->valuestring);                       \
            NET_DEBUG("title(%s) valuestring(%s)", title, val);                             \
        } else {                                                                            \
            NET_DEBUG("not title(%s)", title);                                              \
            memset(val, 0, sizeof(val));                                                    \
        }                                                                                   \
}

#define DECL_PRIV0(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[0] : NULL)
#define DECL_PRIV1(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[1] : NULL)
#define DECL_PRIV2(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[2] : NULL)
#define DECL_PRIV3(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[3] : NULL)
#define DECL_PRIV4(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[4] : NULL)
#define DECL_PRIV5(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[5] : NULL)
#define DECL_PRIV6(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[6] : NULL)
#define DECL_PRIV7(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[7] : NULL)
#define DECL_PRIV8(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[8] : NULL)
#define DECL_PRIV9(obj, type, var)              type * var = (type *)((obj) ? (obj)->priv_subclass[9] : NULL)
#define DECL_PRIV(obj, type, var)               type * var = (type *)((obj) ? (obj)->priv : NULL)

#define RESOLV_CONF_FILE                        "/tmp/resolv.conf"
#define NET_BASE_DIR                            "/tmp/net"

#if CONFIG_NET_WIFI
#define NET_EVT_ARRAY3(e)                       { EVT_TYPE_NET_ETH_ ##e, EVT_TYPE_NET_WIFI_ ##e, EVT_TYPE_NET_WFD_ ##e }
#define NET_EVT_ARRAY2(e)                       { EVT_TYPE_NET_ETH_ ##e, EVT_TYPE_NET_WIFI_ ##e, 0 }
#else
#define NET_EVT_ARRAY3(e)                       { EVT_TYPE_NET_ETH_ ##e }
#define NET_EVT_ARRAY2(e)                       { EVT_TYPE_NET_ETH_ ##e }
#endif

#define NETSERVICE_TASK_PRIORITY                ( PI_MEDIUM_PRIORITY )
#define NETSERVICE_STACK_SIZE                   ( 0x80000 )         ///< 512 * 1024
#define TCP_CHUNK_SIZE                          ( 0x10000 )         ///< 64 * 1024
#define IFACE_MAC_LEN                           ( 18 )
#define IPV4_ADDR_LEN                           ( 16 )
#define IPV6_ADDR_LEN                           ( 64 )
#define LOCALNAME_LEN                           ( 64 )
#define IEEE1284_LEN                            ( 1024 )
#define HOSTNAME_LEN                            ( 16 )
#define FW_VER_LEN                              ( 32 )
#define MAX_AIRPRINT_USERINFO                   ( 6 )

#define PORT_UPDATE_BASE                        ( (int32_t)0x01 )
#define PORT_UPDATE_RAWPRINT                    ( PORT_UPDATE_BASE <<  0 )
#define PORT_UPDATE_RAWSCAN                     ( PORT_UPDATE_BASE <<  1 )
#define PORT_UPDATE_LPD                         ( PORT_UPDATE_BASE <<  2 )
#define PORT_UPDATE_IPP                         ( PORT_UPDATE_BASE <<  3 )
#define PORT_UPDATE_WSD                         ( PORT_UPDATE_BASE <<  4 )
#define PORT_UPDATE_SLP                         ( PORT_UPDATE_BASE <<  5 )
#define PORT_UPDATE_BONJOUR                     ( PORT_UPDATE_BASE <<  6 )
#define PORT_UPDATE_NETBIOS                     ( PORT_UPDATE_BASE <<  7 )
#define PORT_UPDATE_LLMNR                       ( PORT_UPDATE_BASE <<  8 )
#define PORT_UPDATE_SNMP                        ( PORT_UPDATE_BASE <<  9 )
#define PORT_UPDATE_SMTP                        ( PORT_UPDATE_BASE << 10 )

#define LINK_CHANGE_IPV4_BASE                   ( (uint8_t)0x01 )
#define LINK_CHANGE_IPV4_ETH                    ( LINK_CHANGE_IPV4_BASE << IFACE_ID_ETH )
#if CONFIG_NET_WIFI
#define LINK_CHANGE_IPV4_STA                    ( LINK_CHANGE_IPV4_BASE << IFACE_ID_STA )
#define LINK_CHANGE_IPV4_WFD                    ( LINK_CHANGE_IPV4_BASE << IFACE_ID_WFD )
#endif /* CONFIG_NET_WIFI */

#define LINK_CHANGE_IPV6_BASE                   ( LINK_CHANGE_IPV4_BASE << IFACE_ID_NUM )
#define LINK_CHANGE_IPV6_ETH                    ( LINK_CHANGE_IPV6_BASE << IFACE_ID_ETH )
#if CONFIG_NET_WIFI
#define LINK_CHANGE_IPV6_STA                    ( LINK_CHANGE_IPV6_BASE << IFACE_ID_STA )
#endif /* CONFIG_NET_WIFI */

#if CONFIG_NET_WIFI
#define LINK_CHANGE_IPV4_ALL                    ( LINK_CHANGE_IPV4_ETH | LINK_CHANGE_IPV4_STA | LINK_CHANGE_IPV4_WFD )
#define LINK_CHANGE_IPV6_ALL                    ( LINK_CHANGE_IPV6_ETH | LINK_CHANGE_IPV6_STA )
#else
#define LINK_CHANGE_IPV4_ALL                    ( LINK_CHANGE_IPV4_ETH )
#define LINK_CHANGE_IPV6_ALL                    ( LINK_CHANGE_IPV6_ETH )
#endif /* CONFIG_NET_WIFI */

#define LINK_CHANGE_ETH                         ( LINK_CHANGE_IPV4_ETH | LINK_CHANGE_IPV6_ETH )
#define LINK_CHANGE_ALL                         ( LINK_CHANGE_IPV4_ALL | LINK_CHANGE_IPV6_ALL )

#define THREADS_POOL_OF(pctx)                   ( (pctx) ? pctx->net_ctx->threads_pool : NULL )
#define DATA_MGR_OF(pctx)                       ( (pctx) ? pctx->net_ctx->data_mgr     : NULL )

#define IPVER_NAME(v)                           ( v == IPV4 ? "ipv4" : "ipv6" )

typedef enum
{
    IPV_ANY = -1,
    IPV4 = 0,
    IPV6,
    IPVER_NUM,
}
IP_VERSION_E;

typedef enum
{
    NET_LOG_LVL_S = 0,                          ///< use system log level

    NET_LOG_LVL_F,                              ///< force to output the FATAL level logs of net module
    NET_LOG_LVL_E,                              ///< force to output the ERROR level logs of net module
    NET_LOG_LVL_W,                              ///< force to output the WARN  level logs of net module
    NET_LOG_LVL_I,                              ///< force to output the INFO  level logs of net module
    NET_LOG_LVL_D,                              ///< force to output the DEBUG level logs of net module
    NET_LOG_LVL_T,                              ///< force to output the TRACE level logs of net module
}
NET_LOG_E;

/*
 *@brief data format used by netdata_get_alarm_status_flags
 */
typedef enum
{
    EMAIL_ALARM_PAPER_EMPTY = 1 << 0,
    EMAIL_ALARM_TONER_LOW   = 1 << 1,
    EMAIL_ALARM_PAPER_JAM   = 1 << 2,
    EMAIL_ALARM_TONER_EMPTY = 1 << 3,
    EMAIL_ALARM_WASTE_TONER = 1 << 4,
}
EMAIL_ALARM_STATUS_E;

typedef enum
{
    SNMP_SUPPORT_NONE   = 0,
    SNMP_SUPPORT_V1     = 1 << 0,
    SNMP_SUPPORT_V2C    = 1 << 1,
    SNMP_SUPPORT_V3     = 1 << 2,

    SNMP_SUPPORT_V1V2C  = SNMP_SUPPORT_V1 | SNMP_SUPPORT_V2C,
    SNMP_SUPPORT_ALL    = SNMP_SUPPORT_V1V2C | SNMP_SUPPORT_V3,
    SNMP_SUPPORT_MASK   = SNMP_SUPPORT_ALL
}
SNMP_VERSION_E;

typedef struct network_connection
{
    PI_SOCKET_T     sockfd;             ///< socket handle user for this connection
    IFACE_ID_E      ifid;               ///< interface used for this connection
    IP_VERSION_E    ipver;              ///< IP verion user for this connection
    char            remote_addr[64];    ///< remote IP address when this connection
    uint16_t        remote_port;        ///< remote port for this connection
    char            local_addr[64];     ///< local IP address when this connection
    uint16_t        local_port;         ///< local port for this connection
}
NET_CONN_S;

#endif /* __NETTYPES_H__ */
/**
 *@}
 */
