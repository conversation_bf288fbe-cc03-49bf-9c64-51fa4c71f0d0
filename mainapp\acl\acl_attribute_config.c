#include "pol/pol_list.h"
#include "acl/acl_attribute.h"
#include "utilities/parsercommon.h"
#include "pol/pol_endian.h"
#include "pol/pol_string.h"
#include "pol/pol_log.h"
#include "pol/pol_convert.h"

//#include "acl_attribute_demo.h"
//#include "engine_err_statistics.h"
//#include "print_app_var.h"

//#include "platform_api.h"
//#include "interface_control.h"
//#include "print_app_statistics.h"
//#include "print_app_quality.h"
//#include "usb/usbhservice.h"

#if CONFIG_COPY
extern uint32_t pi_copy_get_pages_totalcount(uint32_t *count);
extern uint32_t pi_copy_set_pages_totalcount(uint32_t count);
#endif /* CONFIG_COPY */

extern int32_t pi_print_app_set_hv_calib_param(uint32_t index, char *data, int32_t len);
extern int32_t pi_print_app_get_hv_calib_param(uint32_t index, char *data, int32_t len);

//#include "panel_app_var.h"



#if 1
typedef struct _tag_uint32_value_register
{
    struct list_head    uint32_value_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_UINT32_VALUE    func_set;
    GET_UINT32_VALUE    func_get;
}ATTR_UINT32_VALUE_S;
struct list_head s_uint32_value_list;

typedef struct _tag_uint32_index_register
{
    struct list_head    uint32_index_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_UINT32_INDEX    func_set;
    GET_UINT32_INDEX    func_get;
}ATTR_UINT32_INDEX_S;
struct list_head  s_uint32_index_list;

typedef struct _tag_string_value_register
{
    struct list_head    string_value_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_STRING_VALUE    func_set;
    GET_STRING_VALUE    func_get;
}ATTR_STRING_VALUE_S;
struct list_head  s_string_value_list;

typedef struct _tag_string_index_register
{
    struct list_head    string_index_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_STRING_INDEX    func_set;
    GET_STRING_INDEX    func_get;
}ATTR_STRING_INDEX_S;
struct list_head  s_string_index_list;


int32_t acl_attribute_uint32_value_register(char* attr_key,
                                                SET_UINT32_VALUE func_set,
                                                GET_UINT32_VALUE func_get)
{
    ATTR_UINT32_VALUE_S*  attr_exist   = NULL;
    ATTR_UINT32_VALUE_S*  attr_regst   = NULL;

   	struct list_head *pos, *n;
    pi_list_for_each_safe(pos, n, &s_uint32_value_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_UINT32_VALUE_S, uint32_value_list);
        if(NULL == attr_exist)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if( 0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)) )
        {
            pi_log_d( "the attr key <%s> had beed registered in uint32 value list before!\n", attr_key);
            return PARSER_SUCCESS;
        }
    }

    attr_regst = (ATTR_UINT32_VALUE_S*)pi_malloc(sizeof(ATTR_UINT32_VALUE_S));
    if (NULL == attr_regst)
    {
        pi_log_e("can't get the memory for the key <%s>!\n", attr_key);
        return PARSER_ERROR;
    }

    pi_memset(attr_regst, 0x00, sizeof(ATTR_UINT32_VALUE_S));
    pi_strncpy(attr_regst->attr_key, attr_key, pi_strlen(attr_key));
	attr_regst->func_set = func_set;
    attr_regst->func_get = func_get;
    pi_list_add_tail(&(attr_regst->uint32_value_list), &s_uint32_value_list);
    pi_log_d( "register the attr key <%s> to the uint32 value list OK\n", attr_key);

    return PARSER_SUCCESS;
}

int32_t acl_attribute_uint32_index_register(char* attr_key,
                                                SET_UINT32_INDEX func_set,
                                                GET_UINT32_INDEX func_get)
{
    ATTR_UINT32_INDEX_S*  attr_exist    = NULL;
    ATTR_UINT32_INDEX_S*  attr_regst    = NULL;
   	struct list_head *pos, *n;
    pi_list_for_each_safe(pos, n, &s_uint32_index_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_UINT32_INDEX_S, uint32_index_list);
        if(NULL == attr_exist)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if( 0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)) )
        {
            pi_log_d( "the attr key <%s> had beed registered in uint32 index list before!\n", attr_key);
            return PARSER_SUCCESS;
        }
    }

    attr_regst = (ATTR_UINT32_INDEX_S*)pi_malloc(sizeof(ATTR_UINT32_INDEX_S));
    if (NULL == attr_regst)
    {
        pi_log_e("can't get the memory for the key <%s>!\n", attr_key);
        return PARSER_ERROR;
    }

    pi_memset(attr_regst, 0x00, sizeof(ATTR_UINT32_INDEX_S));
    pi_strncpy(attr_regst->attr_key, attr_key, pi_strlen(attr_key));
	attr_regst->func_set = func_set;
    attr_regst->func_get = func_get;
    pi_list_add_tail(&(attr_regst->uint32_index_list), &s_uint32_index_list);
    pi_log_d( "register the attr key <%s> to the uint32 index list OK\n", attr_key);

    return PARSER_SUCCESS;
}

int32_t acl_attribute_string_value_register(char* attr_key,
                                                SET_STRING_VALUE func_set,
                                                GET_STRING_VALUE func_get)
{
    ATTR_STRING_VALUE_S*  attr_exist    = NULL;
    ATTR_STRING_VALUE_S*  attr_regst    = NULL;
   	struct list_head *pos, *n;
    pi_list_for_each_safe(pos, n, &s_string_value_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_STRING_VALUE_S, string_value_list);
        if(NULL == attr_exist)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if( 0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "the attr key <%s> had beed registered in string value list before!\n", attr_key);
            return PARSER_SUCCESS;
        }
    }

    attr_regst = (ATTR_STRING_VALUE_S*)pi_malloc(sizeof(ATTR_STRING_VALUE_S));
    if (NULL == attr_regst)
    {
        pi_log_e("can't get the memory for the key <%s>!\n", attr_key);
        return PARSER_ERROR;
    }

    pi_memset(attr_regst, 0x00, sizeof(ATTR_STRING_VALUE_S));
    pi_strncpy(attr_regst->attr_key, attr_key, pi_strlen(attr_key));
	attr_regst->func_set = func_set;
    attr_regst->func_get = func_get;
    pi_list_add_tail(&(attr_regst->string_value_list), &s_string_value_list);
    pi_log_d( "register the attr key <%s> to the string value list OK\n", attr_key);

    return PARSER_SUCCESS;
}

int32_t acl_attribute_string_index_register(char* attr_key,
                                                SET_STRING_INDEX func_set,
                                                GET_STRING_INDEX func_get)
{
    ATTR_STRING_INDEX_S*  attr_exist    = NULL;
    ATTR_STRING_INDEX_S*  attr_regst    = NULL;
   	struct list_head *pos, *n;
    pi_list_for_each_safe(pos, n, &s_string_index_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_STRING_INDEX_S, string_index_list);
        if(NULL == attr_exist)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if( 0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)) )
        {
            pi_log_d( "the attr key <%s> had beed registered in string index list before!\n", attr_key);
            return PARSER_SUCCESS;
        }
    }

    attr_regst = (ATTR_STRING_INDEX_S*)pi_malloc(sizeof(ATTR_STRING_INDEX_S));
    if (NULL == attr_regst)
    {
        pi_log_e("can't get the memory for the key <%s>!\n", attr_key);
        return PARSER_ERROR;
    }

    pi_memset(attr_regst, 0x00, sizeof(ATTR_STRING_INDEX_S));
    pi_strncpy(attr_regst->attr_key, attr_key, pi_strlen(attr_key));
	attr_regst->func_set = func_set;
    attr_regst->func_get = func_get;
    pi_list_add_tail(&(attr_regst->string_index_list), &s_string_index_list);
    pi_log_d( "register the attr key <%s> to the string index list OK\n", attr_key);

    return PARSER_SUCCESS;
}

int32_t acl_attribute_get_callback(char* attr_key, ACL_ATTR_VALUE_S* attr_info)
{
    ATTR_UINT32_VALUE_S* attr_exist_uint32_value = NULL;
    ATTR_UINT32_INDEX_S* attr_exist_uint32_index = NULL;
    ATTR_STRING_VALUE_S* attr_exist_string_value = NULL;
    ATTR_STRING_INDEX_S* attr_exist_string_index = NULL;

    pi_log_d( "looking for the attr key <%s> in the register list!\n", attr_key );

    /* find the reffered key */
   	struct list_head *pos, *n;
	pi_list_for_each_safe(pos, n, &s_uint32_value_list)
    {
    	attr_exist_uint32_value = pi_list_entry(pos, ATTR_UINT32_VALUE_S, uint32_value_list);
        if(NULL == attr_exist_uint32_value)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if(0 == pi_strncmp(attr_exist_uint32_value->attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the uint32 value list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_UINT32_VALUE;
            attr_info->func_set     = attr_exist_uint32_value->func_set;
            attr_info->func_get     = attr_exist_uint32_value->func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_list_for_each_safe(pos, n, &s_uint32_index_list)
    {
    	attr_exist_uint32_index = pi_list_entry(pos, ATTR_UINT32_INDEX_S, uint32_index_list);
        if(NULL == attr_exist_uint32_index)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if(0 == pi_strncmp(attr_exist_uint32_index->attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the uint32 index list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_UINT32_INDEX;
            attr_info->func_set     = attr_exist_uint32_index->func_set;
            attr_info->func_get     = attr_exist_uint32_index->func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_list_for_each_safe(pos, n, &s_string_value_list)
    {
    	attr_exist_string_value = pi_list_entry(pos, ATTR_STRING_VALUE_S, string_value_list);
        if(NULL == attr_exist_string_value)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if(0 == pi_strncmp(attr_exist_string_value->attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the string value list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_STRING_VALUE;
            attr_info->func_set     = attr_exist_string_value->func_set;
            attr_info->func_get     = attr_exist_string_value->func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_list_for_each_safe(pos, n, &s_string_index_list)
    {
    	attr_exist_string_index = pi_list_entry(pos, ATTR_STRING_INDEX_S, string_index_list);
        if(NULL == attr_exist_string_index)
        {
            pi_log_e("no node in the list!\n");
            break;
        }

        if(0 == pi_strncmp(attr_exist_string_index->attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the string index list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_STRING_INDEX;
            attr_info->func_set     = attr_exist_string_index->func_set;
            attr_info->func_get     = attr_exist_string_index->func_get;

            return PARSER_SUCCESS;
        }
    }

    return PARSER_ERROR;
}


int32_t acl_attribute_init( void )
{
    pi_log_d( "acl attribute initialize!\n");

    pi_init_list_head(&s_uint32_value_list);
    pi_init_list_head(&s_uint32_index_list);
    pi_init_list_head(&s_string_value_list);
    pi_init_list_head(&s_string_index_list);

    return PARSER_SUCCESS;
}

int32_t acl_attribute_value_register( char* key_str, uint8_t attr_type, void* func_set, void* func_get)
{
    int32_t result = PARSER_SUCCESS;
    if(ATTR_TYPE_NULL == attr_type)
    {
        pi_log_d("The attribute value register type Error! \n");
        return PARSER_ERROR;
    }

    if(ATTR_TYPE_UINT32_VALUE == attr_type)
    {
        result = acl_attribute_uint32_value_register( key_str, func_set , func_get );
    }
    else if(ATTR_TYPE_UINT32_INDEX == attr_type)
    {
        result = acl_attribute_uint32_index_register( key_str, func_set , func_get );
    }
    else if(ATTR_TYPE_STRING_VALUE == attr_type)
    {
        result = acl_attribute_string_value_register( key_str, func_set , func_get );
    }
    else if(ATTR_TYPE_STRING_INDEX == attr_type)
    {
        result = acl_attribute_string_index_register( key_str, func_set , func_get );
    }

    return result;
}
#else
typedef struct _tag_uint32_value_register
{
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_UINT32_VALUE    func_set;
    GET_UINT32_VALUE    func_get;
}ATTR_UINT32_VALUE_S;

typedef struct _tag_uint32_index_register
{
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_UINT32_INDEX    func_set;
    GET_UINT32_INDEX    func_get;
}ATTR_UINT32_INDEX_S;

typedef struct _tag_string_value_register
{
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_STRING_VALUE    func_set;
    GET_STRING_VALUE    func_get;
}ATTR_STRING_VALUE_S;

typedef struct _tag_string_index_register
{
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_STRING_INDEX    func_set;
    GET_STRING_INDEX    func_get;
}ATTR_STRING_INDEX_S;

static const ATTR_UINT32_VALUE_S s_attr_uint32_value[] =
{
    {"OID_DEMO_UINT32_VALUE",               demo_set_uint32_value,                      demo_get_uint32_value                       },
    {"OID_PRINT_CALIBRATION_PARAM_CLEAN",   pi_print_calibration_param_clean,           NULL                                        },
    {"OID_PRINT_CALIBRATION_RESULT"		,   pi_print_calibration_result_set,            pi_print_calibration_result_get                                        },
	{"OID_PLATFORM_ACR_FACTORY_FLAG",       pi_print_app_var_acr_factory_flag_set,   	pi_print_app_var_acr_factory_flag_get       },
    {"OID_PLATFORM_CALIBRATION_PROGRESS",   pi_print_app_var_calibration_progress_set,  pi_print_app_var_calibration_progress_get   },
    {"OID_PLATFORM_SLEEP_TIME",             pi_platform_set_sleep_time,                 pi_platform_get_sleep_time                  },
    {"OID_PLATFORM_PRODUCT_MODEL",          pi_platform_set_machine_type,               pi_platform_get_machine_type                },
    {"OID_PLATFORM_COUNTRY_CODE",           pi_platform_set_countrycode,                pi_platform_get_countrycode                 },
    {"OID_PLATFORM_AUTO_SHUTDOWN_ENABLED",  pi_platform_set_auto_shutdown_enable,       pi_platform_get_auto_shutdown_enable        },
    {"OID_PLATFORM_AUTO_SHUTDOWN_TIME",     pi_platform_set_auto_shutdown_time,         pi_platform_get_auto_shutdown_time          },

#if CONFIG_SCAN_FB || CONFIG_SCAN_ADF
    {"OID_SCAN_EMPTY_MARGIN_TOP_ADF",       pi_scan_acl_set_adf_top_margin,          	pi_scan_acl_get_adf_top_margin           	},
    {"OID_SCAN_EMPTY_MARGIN_LEFT_ADF",      pi_scan_acl_set_adf_left_margin,         	pi_scan_acl_get_adf_left_margin          	},
    {"OID_SCAN_EMPTY_MARGIN_TOP_FB",        pi_scan_acl_set_fb_top_margin,           	pi_scan_acl_get_fb_top_margin            	},
    {"OID_SCAN_EMPTY_MARGIN_LEFT_FB",       pi_scan_acl_set_fb_left_margin,          	pi_scan_acl_get_fb_left_margin           	},
    {"OID_SCAN_TOTAL_PAGE_COUNT",      		pi_scan_acl_set_scan_total_page,         	pi_scan_acl_get_scan_total_page          	},
    {"OID_SCAN_ADF_PAPER_STATUS",      		pi_scan_acl_set_adf_paper_status,         	pi_scan_acl_get_adf_paper_status          	},
    {"OID_SCAN_ADF_COVER_STATUS",      		pi_scan_acl_set_adf_cover_status,         	pi_scan_acl_get_adf_cover_status          	},
    {"OID_SCAN_PAPER_SENSOR_DECETION",      pi_scan_acl_set_paper_sensor_detection,     pi_scan_acl_get_paper_sensor_detection      },
    {"OID_SCAN_HOME_SENSOR_DECETION",       pi_scan_acl_set_home_sensor_detection,      pi_scan_acl_get_home_sensor_detection       },
    {"OID_SCAN_FB_FUNCTION_SWITCH",         pi_scan_acl_set_fb_function_switch,         pi_scan_acl_get_fb_function_switch    		},
    {"OID_SCAN_ADF_FUNCTION_SWITCH",       	pi_scan_acl_set_adf_function_switch,        pi_scan_acl_get_adf_function_switch    		},
    {"OID_SCAN_FB_SCANNER_SENSOR",       	pi_scan_acl_set_fb_Scanner_sensor,          pi_scan_acl_get_fb_Scanner_sensor    		},
    {"OID_SCAN_ADF_TRANSPORT_TEST",       	pi_scan_acl_set_adf_transport_test,         pi_scan_acl_get_adf_transport_test    		},
    {"OID_SCAN_ADF_VERTICAL_AMPLIFICATION", pi_scan_acl_set_adf_vertical_amplification, pi_scan_acl_get_adf_vertical_amplification  },
    {"OID_SCAN_FB_VERTICAL_AMPLIFICATION",  pi_scan_acl_set_fb_vertical_amplification,  pi_scan_acl_get_fb_vertical_amplification   },
#endif
    {"OID_PANEL_TEST_CMD",                  pi_panel_set_test_mode,                     NULL                                        },
    {"OID_PANEL_TEST_RESULT",               NULL,                                       pi_panel_get_test_mode                      },
#if CONFIG_NET
    {"OID_WIFI_INTF_SIGNAL_STRENGTH",       NULL,                                       pi_netoid_get_wifi_sig_strength             },
    {"OID_WIFI_WFD_SUPPORTED",              NULL,                                       pi_netoid_get_wifi_wfd_support              },
#endif /* CONFIG_NET */

    {"OID_PRINT_COLOR_PAGE_COUNT",          NULL,                               pi_print_statistics_get_printed_pages_color_count},
    {"OID_PRINT_MONO_PAGE_COUNT",           NULL,                               pi_print_statistics_get_printed_pages_black_count},
    {"OID_PRINT_TOTAL_PAGE_COUNT",          pi_print_statistics_set_printed_pages_type_totalcount,  pi_print_statistics_get_printed_pages_type_totalcount},
    {"OID_PRINT_A4_LETTER_PAGE_COUNT",      pi_print_statistics_set_printed_pages_size_a4count,                              NULL},
    {"OID_PLATFORM_RESTORE_DEFAULT",        platvars_restore_factory_default,                                                NULL},
    {"OID_PLATFORM_LANGUAGE_CODE",          pi_platform_set_language_code,              pi_platform_get_language_code            },
    {"OID_PLATFORM_AUTO_SHUTDOWN_CONDITION",pi_platform_set_auto_shutdown_condition,    pi_platform_get_auto_shutdown_condition  },
#if CONFIG_COPY
    {"OID_COPY_TOTAL_PAGE_COUNT",           pi_copy_set_pages_totalcount,               pi_copy_get_pages_totalcount             },
#endif /* CONFIG_COPY */
    {"OID_PDL_USER_TOP_MARGIN",             pi_print_app_quality_info_set_topmargin,    NULL                                     },
    {"OID_PDL_USER_LEFT_MARGIN",            pi_print_app_quality_info_set_leftmargin,   NULL                                     },
    {"OID_PLATFORM_UART_ENABLE_FLAG",       pi_platform_set_uart_enable,                NULL                                     },
#if CONFIG_USB_HOST
    {"OID_PLATFORM_UDISK_FRONTOREND_FLAG",            NULL,                    pi_get_udisk_status              },
#endif /* CONFIG_USB_HOST */
};

static const ATTR_UINT32_INDEX_S s_attr_uint32_index[] =
{
    {"OID_DEMO_UINT32_INDEX",               demo_set_uint32_index,                      demo_get_uint32_index                       },
    {"OID_ENGINE_ERR",               		pi_engine_err_statistics_set,               pi_engine_err_statistics_get                },

#if CONFIG_NET
    {"OID_WIFI_INTF_ENABLED",               NULL,                                       pi_netoid_get_wifi_enabled			        },
#endif /* CONFIG_NET */

    {"OID_CONSUMABLE_PERCENT_REMAINING",    NULL,                                       pi_print_app_var_get_toner_remain           },
    {"OID_CONSUMABLE_TOTAL_ACT_PRINTED",    NULL,                                       pi_print_app_var_get_toner_pages_counter    },
};

static const ATTR_STRING_VALUE_S s_attr_string_value[] =
{
    {"OID_DEMO_STRING_VALUE",               demo_set_string_value,                      demo_get_string_value                       },
    {"OID_PLATFORM_PRODUCT_SERIAL_NUM",     platform_set_serial_number,                 platform_get_serial_number                  },
    {"OID_PLATFORM_FW_VERSION",             NULL,                                       pi_platform_get_firmware_version            },
    {"OID_PLATFORM_ENG_FW_VERSION",         NULL,                                       pi_platform_get_engine_firmware_version     },
    {"OID_PLATFORM_PANEL_FW_VERSION",       NULL,                                       pi_platform_get_panel_fw_version            },
    {"OID_PLATFORM_PRINT_NAME_STRING",      NULL,                                       pi_platform_get_print_name_string           },
    {"OID_PLATFORM_PRODUCT_DATE",           pi_platform_set_product_date,               pi_platform_get_product_date                },
    {"OID_PLATFORM_KERNEL_VERSION",         NULL,                                       pi_platform_get_kernel_version              },

#if CONFIG_NET
    {"OID_NETWORK_HOST_NAME",               pi_netoid_set_hostname,                     pi_netoid_get_hostname                      },
#endif /* CONFIG_NET */
};

static const ATTR_STRING_INDEX_S s_attr_string_index[] =
{
    {"OID_DEMO_STRING_INDEX",               demo_set_string_index,                      demo_get_string_index                       },
#if CONFIG_NET
    {"OID_NETWORK_MAC_ADDRESS",             pi_netoid_set_mac_address,                  pi_netoid_get_mac_address                   },
    {"OID_NETWORK_IPV4_ADDRESS",            NULL,                                       pi_netoid_get_ipv4_addres			        },
    {"OID_WIFI_INTF_SSID",                  NULL,                                       pi_netoid_get_wifi_ssid 			        },
#endif /* CONFIG_NET */
    {"OID_CONSUMABLE_MODEL_NAME",           NULL,                                       pi_print_app_var_get_consumable_model_name  },
    {"OID_CONSUMABLE_SERIAL_NUMBER",        NULL,                                       pi_print_app_var_get_consumable_series_num  },
    {"OID_PLATFORM_SYSTEM_TIME",            pi_platform_set_system_time,                pi_platform_get_system_time                 },
    {"OID_PRINT_HV_CALIB_PARAM",            pi_print_app_set_hv_calib_param,            pi_print_app_get_hv_calib_param             },
};

#define NUM_ATTR_UINT32_VALUE     (sizeof(s_attr_uint32_value)/sizeof(ATTR_UINT32_VALUE_S))
#define NUM_ATTR_UINT32_INDEX     (sizeof(s_attr_uint32_index)/sizeof(ATTR_UINT32_INDEX_S))
#define NUM_ATTR_STRING_VALUE     (sizeof(s_attr_string_value)/sizeof(ATTR_STRING_VALUE_S))
#define NUM_ATTR_STRING_INDEX     (sizeof(s_attr_string_index)/sizeof(ATTR_STRING_INDEX_S))


int32_t acl_attribute_get_callback(char* attr_key, ACL_ATTR_VALUE_S* attr_info)
{
    pi_log_d( "looking for the attr key <%s> in the register list!\n", attr_key );

    int i = 0;
    for(i = 0; i < NUM_ATTR_UINT32_VALUE; i++)
    {
        if(0 == pi_strncmp(s_attr_uint32_value[i].attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the uint32 value list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_UINT32_VALUE;
            attr_info->func_set     = s_attr_uint32_value[i].func_set;
            attr_info->func_get     = s_attr_uint32_value[i].func_get;

            return PARSER_SUCCESS;
        }
    }

    for(i = 0; i < NUM_ATTR_UINT32_INDEX; i++)
    {
        if(0 == pi_strncmp(s_attr_uint32_index[i].attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the uint32 index list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_UINT32_INDEX;
            attr_info->func_set     = s_attr_uint32_index[i].func_set;
            attr_info->func_get     = s_attr_uint32_index[i].func_get;

            return PARSER_SUCCESS;
        }
    }

    for(i = 0; i < NUM_ATTR_STRING_VALUE; i++)
    {
        if(0 == pi_strncmp(s_attr_string_value[i].attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the string value list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_STRING_VALUE;
            attr_info->func_set     = s_attr_string_value[i].func_set;
            attr_info->func_get     = s_attr_string_value[i].func_get;

            return PARSER_SUCCESS;
        }
    }

    for(i = 0; i < NUM_ATTR_STRING_INDEX; i++)
    {
        if(0 == pi_strncmp(s_attr_string_index[i].attr_key, attr_key, pi_strlen(attr_key)))
        {
            pi_log_d( "find the attr key <%s> in the string value list!\n", attr_key);
            attr_info->attr_type    = ATTR_TYPE_STRING_INDEX;
            attr_info->func_set     = s_attr_string_index[i].func_set;
            attr_info->func_get     = s_attr_string_index[i].func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_log_e("can't find the attr key <%s> in the all lists\n", attr_key);
    return PARSER_ERROR;
}

#endif
