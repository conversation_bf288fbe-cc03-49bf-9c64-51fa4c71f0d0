/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi_ctrl.h
 * @addtogroup net
 * @{
 * @addtogroup wifi_ctrl
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WiFi control common defined
 */
#ifndef __WIFI_CTRL_H__
#define __WIFI_CTRL_H__

#include "wpa_ctrl.h" /* from wpa_supplicant */

#define WPA_CTRL_DIR                    NET_BASE_DIR "/wpa_supplicant"

#define WPA_STA_CFG_FILE                NET_BASE_DIR "/wpa.cfg." IFACE_STA  /* wpa_supplicant STA 配置文件路径 */
#define WPA_STA_LOG_FILE                NET_BASE_DIR "/wpa.log." IFACE_STA  /* wpa_supplicant STA 日志文件路径 */
#define WPA_STA_PID_FILE                NET_BASE_DIR "/wpa.pid." IFACE_STA  /* wpa_supplicant STA PID文件路径 */

#define WPA_WFD_CFG_FILE                NET_BASE_DIR "/wpa.cfg." IFACE_WFD  /* wpa_supplicant WFD 配置文件路径 */
#define WPA_WFD_LOG_FILE                NET_BASE_DIR "/wpa.log." IFACE_WFD  /* wpa_supplicant WFD 日志文件路径 */
#define WPA_WFD_PID_FILE                NET_BASE_DIR "/wpa.pid." IFACE_WFD  /* wpa_supplicant WFD PID文件路径 */

#if CONFIG_SCAN
#define DEVICE_CATEGORY                 "5"
#else
#define DEVICE_CATEGORY                 "1"
#endif /* CONFIG_SCAN */

#define WPA_DEF_CFG_FORMAT              "ctrl_interface=" WPA_CTRL_DIR "\n"                                         /* Ctrl Interface套接字通信目录 */  \
                                        "update_config=1\n"                                                         /* 允许更新/覆盖配置            */  \
                                        "device_name=%s\n"                                                          /* 设备名称                     */  \
                                        "manufacturer=%s\n"                                                         /* 厂商名称                     */  \
                                        "model_name=%s Series\n"                                                    /* 型号名称                     */  \
                                        "model_number=%s\n"                                                         /* 型号                         */  \
                                        "serial_number=%s\n"                                                        /* 序列号                       */  \
                                        "country=%s\n"                                                              /* 国家代码                     */  \
                                        "device_type=3-0050F204-" DEVICE_CATEGORY "\n"                              /* WPS用的设备类型OUI           */  \
                                        "config_methods=virtual_display physical_push_button virtual_push_button\n" /* WPS的设置方式 */

#define SEC_MODE_OPEN_FORMAT            "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    key_mgmt=NONE\n"               \
                                        "}\n"

#define SEC_MODE_WPA_FORMAT0            "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    psk=\"%s\"\n"                  \
                                        "}\n"

#define SEC_MODE_WPA_FORMAT1            "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    psk=%s\n"                      \
                                        "}\n"

#define SEC_MODE_WPA2_FORMAT0           "pmf=%d\n"                          \
                                        "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    psk=\"%s\"\n"                  \
                                        "    key_mgmt=%s\n"                 \
                                        "    ieee80211w=%d\n"               \
                                        "}\n"

#define SEC_MODE_WPA2_FORMAT1           "pmf=%d\n"                          \
                                        "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    psk=%s\n"                      \
                                        "    key_mgmt=%s\n"                 \
                                        "    ieee80211w=%d\n"               \
                                        "}\n"

#define SEC_MODE_WPA3_FORMAT0           "pmf=%d\n"                          \
                                        "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    psk=\"%s\"\n"                  \
                                        "    key_mgmt=SAE\n"                \
                                        "    ieee80211w=2\n"                \
                                        "}\n"

#define SEC_MODE_WPA3_FORMAT1           "pmf=%d\n"                          \
                                        "\nnetwork={\n"                     \
                                        "    ssid=\"%s\"\n"                 \
                                        "    scan_ssid=1\n"                 \
                                        "    psk=%s\n"                      \
                                        "    key_mgmt=SAE\n"                \
                                        "    ieee80211w=2\n"                \
                                        "}\n"

#define MAX_RETRY_TIMES                 100
#define wpa_ctrl_cmd(c, r, n, cmd)      wpa_ctrl_req(c, r, n, cmd, __func__, __LINE__)

static inline void wpa_ctrl_req(struct wpa_ctrl* ctrl, char* res, size_t nres, const char* cmd, const char* func, int32_t line)
{
    RETURN_SHOW_CALLER_IF(ctrl == NULL, NET_WARN, func);

    if ( func )
    {
        printf("\033[35m" "[%s:%d] cmd: '%s'" "\033[0m" "\n", func, line, cmd);
    }

    --nres;
    wpa_ctrl_request(ctrl, cmd, strlen(cmd), res, &nres, NULL);
    res[nres] = '\0';

    if ( func )
    {
        printf("\033[35m" "[%s:%d] cmd: '%s', res(%u): '%s'" "\033[0m" "\n", func, line, cmd, nres, res);
    }
}

#endif /* _WIFI_CTRL_H_ */
/**
 *@}
 */
