/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netjob.c
 * @addtogroup net
 * @{
 * @addtogroup netjob
 * <AUTHOR>
 * @date 2023-4-20
 * @brief netjob member manager API
 */
#include "nettypes.h"
#include "netjob_task.h"
#include "netjob.h"

#define NETJOB_GET_MEMBER(member, member_type, default_value)   \
    member_type netjob_get_ ##member (int32_t jobid)            \
    {                                                           \
        struct netjob_member* pjob;                             \
                                                                \
        NETJOB_MLOCK_EX();                                      \
        pjob = netjob_search(jobid);                            \
        NETJOB_MLOCK_UN();                                      \
                                                                \
        return ( pjob != NULL ? pjob->member : default_value ); \
    }

#define NETJOB_MLOCK_UN()   pi_mutex_unlock(s_netjob_mutex_lock)
#define NETJOB_MLOCK_EX()   pi_mutex_lock(s_netjob_mutex_lock)
#define NETJOB_NUM_MAX      20

struct netjob_member
{
    struct list_head        list;
    NETJOB_STATE_E          state;                                          /* job state                                          */
    NETJOB_TYPE_E           type;                                           /* job type                                           */
    QIO_S*                  pqio;                                           /* pointer of QIO_S                                   */
    int32_t                 jobid;                                          /* job ID                                             */
    uint32_t                pages_total;                                    /* count of the pages total                           */
    uint32_t                pages_done;                                     /* count of the pages print done                      */
    uint32_t                userdata_type;                                  /* tag for user data type for finding particular tags */
    void*                   userdata;                                       /* something outsiders can hang on this job           */
    void                    (*userdata_destroy)(void *, uint32_t);          /* call to free userData                              */
    int16_t                 start_counter;                                  /* job start counter                                  */
    uint8_t                 finish_flag;                                    /* QIO_CLOSE(pqio) has been called                    */
    uint8_t                 cancel_flag;                                    /* user cancel by panel or remote                     */
};

static struct netjob_member s_netjob_pool[NETJOB_NUM_MAX];
static struct list_head     s_netjob_free_list  = PI_LIST_HEAD_INIT(s_netjob_free_list);
static struct list_head     s_netjob_alloc_list = PI_LIST_HEAD_INIT(s_netjob_alloc_list);
static PI_MUTEX_T           s_netjob_mutex_lock = INVALIDMTX;
static int32_t              s_netjob_alloc_num  = 0;

#define NAME_OUT(n) #n,
static const char*          s_netjob_state[]    = NETJOB_STATE_MAP(NAME_OUT);
static const char*          s_netjob_type[]     = NETJOB_TYPE_MAP(NAME_OUT);
#undef  NAME_OUT

static struct netjob_member* netjob_search(int32_t jobid)
{
    struct netjob_member*   pjob;
    struct list_head*       pos;
    struct list_head*       n;

    pi_list_for_each_safe(pos, n, &s_netjob_alloc_list)
    {
        pjob = pi_list_entry(pos, struct netjob_member, list);
        if ( pjob->jobid == jobid )
        {
            return pjob;
        }
    }

    NET_WARN("no search jobid(%d)", jobid);
    return NULL;
}

static void netjob_release(struct netjob_member* pjob)
{
    pi_list_del_entry(&pjob->list);

    if ( pjob->userdata_destroy )
    {
        pjob->userdata_destroy(pjob->userdata, pjob->userdata_type);
    }
    NET_DEBUG("netjob %d release", pjob->jobid);
    memset(pjob, 0, sizeof(struct netjob_member));

    pi_list_add_tail(&pjob->list, &s_netjob_free_list);
    s_netjob_alloc_num--;
}

const char* netjob_state_string(NETJOB_STATE_E state)
{
    return ( (state >= 0 && state < NETJOB_STATE_MAX) ? s_netjob_state[state] : "<unknown>" );
}

const char* netjob_type_string(NETJOB_TYPE_E type)
{
    return ( (type >= 0 && type < NETJOB_TYPE_MAX) ? s_netjob_type[type] : "<unknown>" );
}

NETJOB_GET_MEMBER(state,         NETJOB_STATE_E, NETJOB_STATE_UNKNOWN)
NETJOB_GET_MEMBER(type,          NETJOB_TYPE_E,  NETJOB_TYPE_UNKNOWN)
NETJOB_GET_MEMBER(pqio,          QIO_S*,         NULL)
NETJOB_GET_MEMBER(pages_total,   uint32_t,       0)
NETJOB_GET_MEMBER(pages_done,    uint32_t,       0)
NETJOB_GET_MEMBER(finish_flag,   uint8_t,        1)
NETJOB_GET_MEMBER(cancel_flag,   uint8_t,        0)

int32_t netjob_get_userdata(int32_t jobid, uint32_t* type, void** data)
{
    struct netjob_member*   pjob;
    int32_t                 ret;

    NETJOB_MLOCK_EX();
    pjob = netjob_search(jobid);
    if ( pjob != NULL )
    {
        if ( type != NULL )
        {
            *type = pjob->userdata_type;
        }
        if ( data != NULL )
        {
            *data = pjob->userdata;
        }
        ret = 0;
    }
    else
    {
        if ( type != NULL )
        {
            *type = 0;
        }
        if ( data != NULL )
        {
            *data = NULL;
        }
        ret = -1;
    }
    NETJOB_MLOCK_UN();

    return ret;
}

int32_t netjob_get_next_jobid(int32_t jobid)
{
    struct netjob_member*   pjob;

    NETJOB_MLOCK_EX();
    if ( jobid <= 0 )
    {
        pjob = pi_list_first_entry_or_null(&s_netjob_alloc_list, struct netjob_member, list);
    }
    else
    {
        pjob = netjob_search(jobid);
        if ( pjob != NULL )
        {
            if ( pjob->list.next != &s_netjob_alloc_list )
            {
                pjob = pi_list_entry(pjob->list.next, struct netjob_member, list);
            }
            else
            {
                pjob = NULL;
            }
        }
    }
    NETJOB_MLOCK_UN();

    return ( pjob != NULL ? pjob->jobid : 0 );
}

int32_t netjob_mark_finish(int32_t jobid)
{
    struct netjob_member*   pjob;
    int32_t                 ret;

    NETJOB_MLOCK_EX();
    pjob = netjob_search(jobid);
    if ( pjob != NULL )
    {
        NET_DEBUG("netjob %d mark finish, current state(%s)", pjob->jobid, s_netjob_state[pjob->state]);
        pjob->finish_flag = 1;
        ret = 0;
    }
    else
    {
        ret = -1;
    }
    NETJOB_MLOCK_UN();

    return ret;
}

int32_t netjob_mark_cancel(int32_t jobid)
{
    struct netjob_member*   pjob;
    int32_t                 ret;

    NETJOB_MLOCK_EX();
    pjob = netjob_search(jobid);
    if ( pjob != NULL )
    {
        NET_DEBUG("netjob %d mark cancel, current state(%s)", pjob->jobid, s_netjob_state[pjob->state]);
        pjob->cancel_flag = 1;
        ret = 0;
    }
    else
    {
        ret = -1;
    }
    NETJOB_MLOCK_UN();

    return ret;
}

int32_t netjob_set_userdata(int32_t jobid, uint32_t type, void* data, void (*destroy)(void *, uint32_t))
{
    struct netjob_member*   pjob;
    int32_t                 ret;

    NETJOB_MLOCK_EX();
    pjob = netjob_search(jobid);
    if ( pjob != NULL )
    {
        NET_DEBUG("jobid(%d), type(0x%x), userdata(%p)", jobid, type, data);
        pjob->userdata_destroy = destroy;
        pjob->userdata_type    = type;
        pjob->userdata         = data;
        ret = 0;
    }
    else
    {
        ret = -1;
    }
    NETJOB_MLOCK_UN();

    return ret;
}

int32_t netjob_set_page_done(int32_t jobid, int32_t page_count)
{
    struct netjob_member*   pjob;
    int32_t                 ret;

    NET_TRACE("netjob %d update page done %d", jobid, page_count);
    NETJOB_MLOCK_EX();
    pjob = netjob_search(jobid);
    if ( pjob != NULL )
    {
        pjob->pages_done = page_count;
        if(pjob->pages_total < pjob->pages_done)
        {
            pjob->pages_total = pjob->pages_done;
        }
        ret = 0;
    }
    else
    {
        ret = -1;
    }
    NETJOB_MLOCK_UN();

    return ret;
}

int32_t netjob_set_state(int32_t jobid, NETJOB_STATE_E state)
{
    struct netjob_member*   pjob;
    int32_t                 ret;

    RETURN_VAL_IF(state < 0 || state >= NETJOB_STATE_MAX, NET_WARN, -1);

    NETJOB_MLOCK_EX();
    pjob = netjob_search(jobid);
    if ( pjob != NULL )
    {
        NET_DEBUG("netjob %d change state (%s) to (%s)", jobid, s_netjob_state[pjob->state], s_netjob_state[state]);
        pjob->state = state;
        ret = 0;
    }
    else
    {
        ret = -1;
    }
    NETJOB_MLOCK_UN();

    return ret;
}

int32_t netjob_alloc(NETJOB_TYPE_E type, QIO_S* pqio)
{
    struct netjob_member*   pjob;

    NETJOB_MLOCK_EX();
    pjob = pi_list_first_entry_or_null(&s_netjob_free_list, struct netjob_member, list);
    if ( pjob != NULL )
    {
        pi_list_del_entry(&pjob->list);

        memset(pjob, 0, sizeof(struct netjob_member));
        pjob->jobid  = (int32_t)job_manager_apply_jobid(IO_VIA_IPP , 0);
        pjob->state  = NETJOB_STATE_NEW;
        pjob->type   = type;
        pjob->pqio   = pqio;

        NET_DEBUG("netjob alloc jobid(%d) type(%s)", pjob->jobid, netjob_type_string(pjob->type));
        pi_list_add_head(&pjob->list, &s_netjob_alloc_list);
        s_netjob_alloc_num++;
    }
    else
    {
        NET_WARN("netjob pool all busy");
    }
    NETJOB_MLOCK_UN();

    RETURN_VAL_IF(pjob == NULL, NET_WARN, -1);
    return pjob->jobid;
}

void netjob_free(int32_t jobid, int32_t reserved)
{
    struct netjob_member*   pjob;
    struct list_head*       pos;
    struct list_head*       n;

    NET_DEBUG("netjob %d will be free", jobid);
    NETJOB_MLOCK_EX();
    if ( reserved > 0 )
    {
        pjob = netjob_search(jobid);
        if ( pjob != NULL )
        {
            if ( pjob->cancel_flag )
            {
                NET_DEBUG("netjob %d should canceled, current state(%s)", pjob->jobid, s_netjob_state[pjob->state]);
                pjob->state = NETJOB_STATE_CANCELED;
            }
            pjob->pqio = NULL;
        }

        pi_list_for_each_prev_safe(pos, n, &s_netjob_alloc_list)
        {
            NET_DEBUG("netjob alloc number %d", s_netjob_alloc_num);
            if ( s_netjob_alloc_num <= reserved )
            {
                break;
            }

            pjob = pi_list_entry(pos, struct netjob_member, list);
            if ( pjob->state > NETJOB_STATE_RUNNING )
            {
                netjob_release(pjob);
            }
        }
    }
    else
    {
        pjob = netjob_search(jobid);
        if ( pjob != NULL )
        {
            netjob_release(pjob);
        }
    }
    NETJOB_MLOCK_UN();
}

int32_t netjob_init(void)
{
    int32_t i;

    RETURN_VAL_IF(s_netjob_mutex_lock != INVALIDMTX, NET_WARN, -1);

    s_netjob_mutex_lock = pi_mutex_create();
    RETURN_VAL_IF(s_netjob_mutex_lock == INVALIDMTX, NET_WARN, -1);

    memset(s_netjob_pool, 0, sizeof(s_netjob_pool));
    for ( i = 0; i < NETJOB_NUM_MAX; ++i )
    {
        pi_list_add_tail(&s_netjob_pool[i].list, &s_netjob_free_list);
    }

    return netjob_init_tasks();
}

void netjob_deinit(void)
{
    struct netjob_member*   pjob;
    struct list_head*       pos;
    struct list_head*       n;

    if ( s_netjob_mutex_lock != INVALIDMTX )
    {
        netjob_deinit_tasks();

        NETJOB_MLOCK_EX();
        pi_list_for_each_safe(pos, n, &s_netjob_alloc_list)
        {
            pjob = pi_list_entry(pos, struct netjob_member, list);
            if (pjob->state == NETJOB_STATE_RUNNING)
            {
                pjob->cancel_flag = 1;
            }
        }
        NETJOB_MLOCK_UN();

        pi_mutex_destroy(s_netjob_mutex_lock);
        s_netjob_mutex_lock = INVALIDMTX;
    }
}
/**
 *@}
 */
