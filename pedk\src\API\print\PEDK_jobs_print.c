#include "PEDK_jobs_print.h"
#include "PEDK_event.h"

#include <quickjs.h>


#define Print_Log(format, ...) printf("[PRINT][RE] [L:%d %s] "format, __LINE__, __func__, ##__VA_ARGS__)
#define Print_Error(format, ...) printf("[PRINTERROR][RE] [L:%d %s] "format, __LINE__, __func__, ##__VA_ARGS__)
#define countof(x) (sizeof(x) / sizeof((x)[0]))

#define                             PRINT_PEDK_COLOR_JOB                1
#define                             PRINT_PEDK_MONO_JOB                 0


typedef JSValue (*PJS_CONSTRUCTOR)(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
typedef struct
{
    JSClassID *class_id;
    JSClassDef *pclass;
    const JSCFunctionListEntry *PEDK_funs;
    int fun_len;
    PJS_CONSTRUCTOR ctor;
    int ctor_parm_num;
    const char* name;
}JSCreatClassParam;

static JSValue JS_ExtendClass(JSContext *ctx, JSValueConst this_val, void *obj_data, JSClassID classid)
{
    JSValue obj = JS_UNDEFINED;
    JSValue proto;

    /* using this_val to get the prototype is necessary when the class is extended. */
    proto = JS_GetPropertyStr(ctx, this_val, "prototype");
    if (JS_IsException(proto))
        goto fail;
    obj = JS_NewObjectProtoClass(ctx, proto, classid);
    JS_FreeValue(ctx, proto);
    if (JS_IsException(obj))
        goto fail;
    JS_SetOpaque(obj, obj_data);
    return obj;
 fail:
    js_free(ctx, obj_data);
    JS_FreeValue(ctx, obj);

    return JS_UNDEFINED;
}

/* Staple Config */
static JSClassID js_staple_config_class_id;

typedef struct
{
    char staple[32];
    char punch[32];
    char fold[32];
    char shift[32];
}JSStapleConfigMode;

static void js_staple_config_finalizer(JSRuntime *rt, JSValue val)
{
    JSStapleConfigMode *mode = JS_GetOpaque(val, js_staple_config_class_id);

    js_free_rt(rt, mode);
}

static JSClassDef js_staple_config_class =
{
    "StepleConfig",
     .finalizer = js_staple_config_finalizer,
};



/* CollateMode */
static JSClassID js_collate_mode_class_id;

typedef struct
{
    int mode;
}JSCollateMode;

static void js_collate_mode_finalizer(JSRuntime *rt, JSValue val)
{
    JSCollateMode *mode = JS_GetOpaque(val, js_collate_mode_class_id);

    js_free_rt(rt, mode);
}

static JSClassDef js_collate_mode_class =
{
    "CollateMode",
     .finalizer = js_collate_mode_finalizer,
};


/* Copies */
static JSClassID js_copies_class_id;

typedef struct
{
    int num;
}JSCopies;

static void js_copies_finalizer(JSRuntime *rt, JSValue val)
{
    JSCopies *num = JS_GetOpaque(val, js_copies_class_id);

    js_free_rt(rt, num);
}

static JSClassDef js_copies_class =
{
    "Copies",
    .finalizer = js_copies_finalizer,
};

/* color mode*/
static JSClassID js_color_mode_class_id;
typedef struct
{
    int mode;
}JSColor;

static void js_color_mode_finalizer(JSRuntime *rt, JSValue val)
{
    JSCopies *num = JS_GetOpaque(val, js_color_mode_class_id);

    js_free_rt(rt, num);
}

static JSClassDef js_color_mode_class =
{
    "ColorMode",
    .finalizer = js_color_mode_finalizer,
};


/* DuplexPrintMode */
static JSClassID js_duplex_print_mode_class_id;

typedef struct
{
    int mode;
}JSDuplexPrintMode;

static void js_duplex_print_mode_finalizer(JSRuntime *rt, JSValue val)
{
    JSDuplexPrintMode *mode = JS_GetOpaque(val, js_duplex_print_mode_class_id);

    js_free_rt(rt, mode);
}

static JSClassDef js_duplex_print_mode_class =
{
    "DuplexPrintMode",
    .finalizer = js_duplex_print_mode_finalizer,
};



/* ImageOrientationMode */
static JSClassID js_image_orientation_mode_class_id;

typedef struct
{
    int mode;
}JSImageOrientationMode;

static void js_image_orientation_mode_finalizer(JSRuntime *rt, JSValue val)
{
    JSImageOrientationMode *mode = JS_GetOpaque(val, js_image_orientation_mode_class_id);

    js_free_rt(rt, mode);
}

static JSClassDef js_image_orientation_mode_class =
{
    "ImageOrientationMode",
    .finalizer = js_image_orientation_mode_finalizer,
};



/* OutputTray */
static JSClassID js_output_tray_class_id;

typedef struct
{
    char out_tray[32];
    char paper_size[32];
}JSOutPutTray;

static void js_output_tray_finalizer(JSRuntime *rt, JSValue val)
{
    JSOutPutTray *tray = JS_GetOpaque(val, js_output_tray_class_id);

    js_free_rt(rt, tray);
}

static JSClassDef js_output_tray_class =
{
    "OutPutTray",
    .finalizer = js_output_tray_finalizer,
};




/* PaperSaveMode */
static JSClassID js_paper_save_mode_class_id;

typedef struct
{
    int mode;
}JSPaperSaveMode;

static void js_paper_save_mode_finalizer(JSRuntime *rt, JSValue val)
{
    JSPaperSaveMode *mode = JS_GetOpaque(val, js_paper_save_mode_class_id);

    js_free_rt(rt, mode);
}

static JSClassDef js_paper_save_mode_class =
{
    "PaperSaveMode",
    .finalizer = js_paper_save_mode_finalizer,
};




/* PaperType */
static JSClassID js_paper_type_class_id;

typedef struct
{
    char type[32];
}JSPaperType;

static void js_paper_type_finalizer(JSRuntime *rt, JSValue val)
{
    JSPaperType *type = JS_GetOpaque(val, js_paper_type_class_id);

    js_free_rt(rt, type);
}

static JSClassDef js_paper_type_class =
{
    "PaperType",
    .finalizer = js_paper_type_finalizer,
};


/* JSPrintParameterSet */
static JSClassID js_print_parameter_class_id;

typedef struct
{
    JSCopies                copies;
    JSPaperType             paper_type;
    JSOutPutTray            tray;
    JSCollateMode           collate_mode;
    JSDuplexPrintMode       duplex_mode;
    JSPaperSaveMode         paper_save_mode;
    JSImageOrientationMode  image_orientation_mode;
    JSStapleConfigMode      staple_config_mode;
    JSColor                 color;
}JSPrintParameter;

static void js_print_parameter_finalizer(JSRuntime *rt, JSValue val)
{
    JSPrintParameter *para = JS_GetOpaque(val, js_print_parameter_class_id);

    js_free_rt(rt, para);
}

static JSClassDef js_print_parameter_class =
{
    "PrintParameterSet",
     .finalizer = js_print_parameter_finalizer,
};

typedef struct
{
    int                 job_id;
    int                 job_woNum;
    char                job_type[32];
    JSPrintParameter    job_param;
    union
    {
        int  page_type;             //< For internal page printing
        char file_path[512];        //< For usb memory printing
        char url[512];              //< For url printing
    };
}JSPrintJob;


/* JobPrint */
static JSClassID js_print_job_class_id;

static void js_print_job_finalizer(JSRuntime *rt, JSValue val)
{
    JSPrintJob *job = JS_GetOpaque(val, js_print_job_class_id);

    js_free_rt(rt, job);
}

static JSClassDef js_print_job_class =
{
    "PrintJob",
     .finalizer = js_print_job_finalizer,
};


void re_print_jod_info(JSPrintJob* job)
{
    Print_Log("job->job_id [%d]\n",job->job_id);
    Print_Log("job->job_type [%s]\n",job->job_type);
    Print_Log("job->job_param.copies.num [%d]\n",job->job_param.copies.num);
    Print_Log("job->job_param.paper_type.type [%s]\n",job->job_param.paper_type.type);
    Print_Log("job->job_param.tray.out_tray [%s]\n",job->job_param.tray.out_tray);
    Print_Log("job->job_param.tray.paper_size [%s]\n",job->job_param.tray.paper_size);
    Print_Log("job->job_param.collate_mode.mode [%d]\n",job->job_param.collate_mode.mode);
    Print_Log("job->job_param.duplex_mode.mode [%d]\n",job->job_param.duplex_mode.mode);
    Print_Log("job->job_param.paper_save_mode.mode [%d]\n",job->job_param.paper_save_mode.mode);
    Print_Log("job->job_param.image_orientation_mode.mode [%d]\n",job->job_param.image_orientation_mode.mode);

    return;
}



static int print_job_start_to_mfp(JSPrintJob* print_job)
{
    int  respond = 0;

    return SendMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_START,respond, sizeof(JSPrintJob), print_job);
}

static int print_job_cancel_to_mfp(JSPrintJob* print_job)
{
    int  respond = 0;

    return SendMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_CANCEL,respond, sizeof(JSPrintJob), print_job);
}

//static int print_job_get_state_from_mfp(int respond, unsigned char *sendBuf,int sendBufSize,unsigned char *recvBuf, int *recvBufSize)
//{
//    int ret = -1;
//    SendMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_STATE, respond, sendBufSize,sendBuf );
//    RecvMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_STATE, &ret, recvBuf, recvBufSize, 50);
//   return ret;
//}

static JSValue js_print_collate_mode_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_BOOL != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        Print_Error("argc %d, Must be only one bool parmeter\n", argc);
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    JSCollateMode  *collate_mode = js_malloc(ctx, sizeof(*collate_mode));
    if(!collate_mode)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }

    memset(collate_mode,0,sizeof(*collate_mode));

    int ret = JS_ToBool(ctx, argv[0]);

    if( -1 == ret )
    {
        Print_Error("JS_ToBool failed!\n");
        js_free(ctx, collate_mode);
        return JS_EXCEPTION;
    }

    collate_mode->mode = ret;

    return JS_ExtendClass(ctx, this_val, collate_mode, js_collate_mode_class_id);
}


static JSValue js_print_getCollateMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSCollateMode  *collate_mode = JS_GetOpaque2(ctx, this_val, js_collate_mode_class_id);
    if(!collate_mode)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewBool(ctx, collate_mode->mode);
}


static JSValue js_print_setCollateMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsBool(argv[0]))
    {
        Print_Error("argc %d, Must be only one bool parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    JSCollateMode *collate_mode = JS_GetOpaque2(ctx, this_val, js_collate_mode_class_id);
    if(!collate_mode)
    {
        return JS_FALSE;
    }

    int mode = JS_ToBool(ctx, argv[0]);
    if(mode == -1)
    {
        Print_Error("JS_ToBool failed\n");
        return JS_FALSE;
    }

    collate_mode->mode = mode;

    return JS_TRUE;
}

static JSValue js_print_color_mode_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSColor  *color_mode = js_malloc(ctx, sizeof(*color_mode));
    if(!color_mode)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(color_mode,0,sizeof(*color_mode));

    const char *type = JS_ToCString(ctx, argv[0]);
    if(0 == strcmp(type,"COLOR_MODE_COLOR"))
    {
        color_mode->mode = PRINT_PEDK_COLOR_JOB;
    }
    else if(0 == strcmp(type,"COLOR_MODE_BLACK_WHITE"))
    {
        color_mode->mode = PRINT_PEDK_MONO_JOB;
    }
    else
    {
        color_mode->mode = PRINT_PEDK_MONO_JOB;
    }

    return JS_ExtendClass(ctx, this_val, color_mode, js_color_mode_class_id);
}

static JSValue js_print_getColorMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSColor  *color_mode = JS_GetOpaque2(ctx, this_val, js_color_mode_class_id);
    if(!color_mode)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewInt32(ctx, color_mode->mode);
}

static JSValue js_print_setColorMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSColor  *color_mode = JS_GetOpaque2(ctx, this_val, js_color_mode_class_id);
    if(!color_mode)
    {
        return JS_FALSE;
    }

    const char *type = JS_ToCString(ctx, argv[0]);
    if(0 == strcmp(type,"COLOR_MODE_COLOR"))
    {
        color_mode->mode = PRINT_PEDK_COLOR_JOB;
    }
    else if(0 == strcmp(type,"COLOR_MODE_BLACK_WHITE"))
    {
        color_mode->mode = PRINT_PEDK_MONO_JOB;
    }
    else
    {
        color_mode->mode = PRINT_PEDK_MONO_JOB;
    }

    return JS_TRUE;
}


static JSValue js_print_copies_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one bool parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSCopies  *copies = js_malloc(ctx, sizeof(*copies));
    if(!copies)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(copies,0,sizeof(*copies));

    int num = 0;
    if(JS_ToInt32(ctx, &num, argv[0]))
    {
        Print_Error("JS_ToInt32 failed\n");
        js_free(ctx, copies);
        return JS_EXCEPTION;
    }

    copies->num = num;

    return JS_ExtendClass(ctx, this_val, copies, js_copies_class_id);
}


static JSValue js_print_getCopies(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSCopies  *copies = JS_GetOpaque2(ctx, this_val, js_copies_class_id);
    if(!copies)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewInt32(ctx, copies->num);
}


static JSValue js_print_setCopies(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one bool parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    JSCopies *copies = JS_GetOpaque2(ctx, this_val, js_copies_class_id);
    if(!copies)
    {
        return JS_FALSE;
    }

    int num;
    if(JS_ToInt32(ctx, &num, argv[0]))
    {
        Print_Error("JS_ToBool failed\n");
        return JS_FALSE;
    }

    copies->num = num;

    return JS_TRUE;
}



static JSValue js_print_duplex_print_mode_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSDuplexPrintMode  *duplex_mode = js_malloc(ctx, sizeof(*duplex_mode));
    if(!duplex_mode)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(duplex_mode,0,sizeof(*duplex_mode));

    int mode = 0;
    if(JS_ToInt32(ctx, &mode, argv[0]))
    {
        Print_Error("JS_ToInt32 failed\n");
        js_free(ctx, duplex_mode);
        return JS_EXCEPTION;
    }

    if(mode < ePRINT_PEDK_DUPLEX_MODE_OFF || mode >= ePRINT_PEDK_DUPLEX_MODE_MAX)
    {
        Print_Error("mode %d Excess threshold\n",mode);
        return JS_FALSE;
    }

    duplex_mode->mode = mode;

    return JS_ExtendClass(ctx, this_val, duplex_mode, js_duplex_print_mode_class_id);
}



static JSValue js_print_getDuplexPrintMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSDuplexPrintMode  *duplex_mode = JS_GetOpaque2(ctx, this_val, js_duplex_print_mode_class_id);
    if(!duplex_mode)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewInt32(ctx, duplex_mode->mode);
}


static JSValue js_print_setDuplexPrintMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSDuplexPrintMode *duplex_mode = JS_GetOpaque2(ctx, this_val, js_duplex_print_mode_class_id);
    if(!duplex_mode)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    int mode;
    if(JS_ToInt32(ctx, &mode, argv[0]))
    {
        Print_Error("JS_ToBool failed\n");
        return JS_FALSE;
    }

    if(mode < ePRINT_PEDK_DUPLEX_MODE_OFF || mode >= ePRINT_PEDK_DUPLEX_MODE_MAX)
    {
        Print_Error("mode %d Excess threshold\n",mode);
        return JS_FALSE;
    }

    duplex_mode->mode = mode;

    return JS_TRUE;
}







static JSValue js_print_image_orientation_mode_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSImageOrientationMode  *image_orientation_mdoe = js_malloc(ctx, sizeof(*image_orientation_mdoe));
    if(!image_orientation_mdoe)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(image_orientation_mdoe,0,sizeof(*image_orientation_mdoe));

    int mode = 0;
    if(JS_ToInt32(ctx, &mode, argv[0]))
    {
        Print_Error("JS_ToInt32 failed\n");
        js_free(ctx, image_orientation_mdoe);
        return JS_EXCEPTION;
    }

    if(mode < eIMAGE_ORIENTATION_VERTIAL || mode >= eIMAGE_ORIENTATION_MAX)
    {
        Print_Error("mode %d Excess threshold\n",mode);
        return JS_FALSE;
    }

    image_orientation_mdoe->mode = mode;

    return JS_ExtendClass(ctx, this_val, image_orientation_mdoe, js_image_orientation_mode_class_id);
}



static JSValue js_print_getImageOrientationMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSImageOrientationMode  *image_orientation_mdoe = JS_GetOpaque2(ctx, this_val, js_image_orientation_mode_class_id);
    if(!image_orientation_mdoe)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewInt32(ctx, image_orientation_mdoe->mode);
}


static JSValue js_print_setImageOrientationMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSImageOrientationMode  *image_orientation_mdoe = JS_GetOpaque2(ctx, this_val, js_image_orientation_mode_class_id);
    if(!image_orientation_mdoe)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    int mode;
    if(JS_ToInt32(ctx, &mode, argv[0]))
    {
        Print_Error("JS_ToBool failed\n");
        return JS_FALSE;
    }


    if(mode < eIMAGE_ORIENTATION_VERTIAL || mode >= eIMAGE_ORIENTATION_MAX)
    {
        Print_Error("mode %d Excess threshold\n",mode);
        return JS_FALSE;
    }

    image_orientation_mdoe->mode = mode;

    return JS_TRUE;
}


static JSValue js_print_output_tray_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 2 || !JS_IsString(argv[0]) || !JS_IsString(argv[1]))
    {
        Print_Error("argc %d, Must be only two string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only two string parmeter");
    }

    JSOutPutTray  *output_tray = js_malloc(ctx, sizeof(*output_tray));
    if(!output_tray)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }

    memset(output_tray,0,sizeof(*output_tray));

    const char *tary = JS_ToCString(ctx, argv[0]);
    const char *paper_size = JS_ToCString(ctx, argv[1]);

    strncpy(output_tray->out_tray,tary,strlen(tary));
    strncpy(output_tray->paper_size,paper_size,strlen(paper_size));


	JS_FreeCString(ctx, tary);
	JS_FreeCString(ctx, paper_size);

    return JS_ExtendClass(ctx, this_val, output_tray, js_output_tray_class_id);
}

static JSValue js_print_getTray(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSOutPutTray  *output_tray = JS_GetOpaque2(ctx, this_val, js_output_tray_class_id);
    if(!output_tray)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    return JS_NewString(ctx, output_tray->out_tray);
}

static JSValue js_print_getPaperSize(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSOutPutTray  *output_tray = JS_GetOpaque2(ctx, this_val, js_output_tray_class_id);
    if(!output_tray)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    return JS_NewString(ctx, output_tray->paper_size);
}


static JSValue js_print_setPaperSize(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSOutPutTray  *output_tray = JS_GetOpaque2(ctx, this_val, js_output_tray_class_id);
    if(!output_tray)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *paper_size = JS_ToCString(ctx, argv[0]);
    strncpy(output_tray->paper_size,paper_size,strlen(paper_size));

	JS_FreeCString(ctx, paper_size);

    return JS_TRUE;
}


static JSValue js_print_setTray(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSOutPutTray  *output_tray = JS_GetOpaque2(ctx, this_val, js_output_tray_class_id);
    if(!output_tray)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *tary = JS_ToCString(ctx, argv[0]);
    strncpy(output_tray->out_tray,tary,strlen(tary));

	JS_FreeCString(ctx, tary);
    return JS_TRUE;
}


static JSValue js_print_paper_save_mode_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSPaperSaveMode  *paper_save = js_malloc(ctx, sizeof(*paper_save));
    if(!paper_save)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(paper_save,0,sizeof(*paper_save));
    int mode = 0;
    if(JS_ToInt32(ctx, &mode, argv[0]))
    {
        Print_Error("JS_ToInt32 failed\n");
        js_free(ctx, paper_save);
        return JS_EXCEPTION;
    }

    paper_save->mode = mode;

    return JS_ExtendClass(ctx, this_val, paper_save, js_paper_save_mode_class_id);
}



static JSValue js_print_getPaperSaveMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSPaperSaveMode  *paper_save = JS_GetOpaque2(ctx, this_val, js_paper_save_mode_class_id);
    if(!paper_save)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewInt32(ctx, paper_save->mode);
}


static JSValue js_print_setPaperSaveMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    JSPaperSaveMode  *paper_save = JS_GetOpaque2(ctx, this_val, js_paper_save_mode_class_id);
    if(!paper_save)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    int mode;
    if(JS_ToInt32(ctx, &mode, argv[0]))
    {
        Print_Error("JS_ToBool failed\n");
        return JS_FALSE;
    }

    paper_save->mode = mode;

    return JS_TRUE;
}


static JSValue js_print_paper_type_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSPaperType  *paper_type = js_malloc(ctx, sizeof(*paper_type));
    if(!paper_type)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(paper_type,0,sizeof(*paper_type));

    const char *type = JS_ToCString(ctx, argv[0]);
    strncpy(paper_type->type,type,strlen(type));
	JS_FreeCString(ctx, type);
    return JS_ExtendClass(ctx, this_val, paper_type, js_paper_type_class_id);
}

static JSValue js_print_setPaperType(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSPaperType  *paper_type = JS_GetOpaque2(ctx, this_val, js_paper_type_class_id);
    if(!paper_type)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *type = JS_ToCString(ctx, argv[0]);
    strncpy(paper_type->type,type,strlen(type));
    JS_FreeCString(ctx, type);
    return JS_TRUE;
}

static JSValue js_print_getPaperType(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSPaperType  *paper_type = JS_GetOpaque2(ctx, this_val, js_paper_type_class_id);
    if(!paper_type)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    return JS_NewString(ctx, paper_type->type);
}


static JSValue js_print_parameter_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSPrintParameter *para = js_malloc(ctx, sizeof(*para));
    if(!para)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(para,0,sizeof(*para));

    return JS_ExtendClass(ctx, this_val, para, js_print_parameter_class_id);
}

static JSValue js_print_staple_config_ctor(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 4 || !JS_IsString(argv[0]) || !JS_IsString(argv[1]) ||
        !JS_IsString(argv[2]) || !JS_IsString(argv[3]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *staple = JS_ToCString(ctx, argv[0]);
    const char *punch = JS_ToCString(ctx, argv[1]);
    const char *fold = JS_ToCString(ctx, argv[2]);
    const char *shift = JS_ToCString(ctx, argv[3]);
    strncpy(staple_config->staple,staple,strlen(staple));
    strncpy(staple_config->punch,punch,strlen(punch));
    strncpy(staple_config->fold,fold,strlen(fold));
    strncpy(staple_config->shift,shift,strlen(shift));

	JS_FreeCString(ctx, staple);
    JS_FreeCString(ctx, punch);
    JS_FreeCString(ctx, fold);
    JS_FreeCString(ctx, shift);

    return JS_ExtendClass(ctx, this_val, staple_config, js_output_tray_class_id);
}


static JSValue js_print_setStapleMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *staple = JS_ToCString(ctx, argv[0]);
    strncpy(staple_config->staple,staple,strlen(staple));

	JS_FreeCString(ctx, staple);
    return JS_TRUE;
}

static JSValue js_print_getStapleMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    return JS_NewString(ctx, staple_config->staple);
}

static JSValue js_print_punch_mode_ctor(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *punch = JS_ToCString(ctx, argv[0]);
    strncpy(staple_config->punch,punch,strlen(punch));

	JS_FreeCString(ctx, punch);

    return JS_ExtendClass(ctx, this_val, staple_config, js_output_tray_class_id);
}


static JSValue js_print_setPunchMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *punch = JS_ToCString(ctx, argv[0]);
    strncpy(staple_config->punch,punch,strlen(punch));

	JS_FreeCString(ctx, punch);
    return JS_TRUE;
}

static JSValue js_print_getPunchMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    return JS_NewString(ctx, staple_config->punch);
}

static JSValue js_print_fold_mode_ctor(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *fold = JS_ToCString(ctx, argv[0]);
    strncpy(staple_config->fold,fold,strlen(fold));

	JS_FreeCString(ctx, fold);
    return JS_ExtendClass(ctx, this_val, staple_config, js_output_tray_class_id);
}


static JSValue js_print_setFoldMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *fold = JS_ToCString(ctx, argv[0]);
    strncpy(staple_config->fold,fold,strlen(fold));

	JS_FreeCString(ctx, fold);
    return JS_TRUE;
}

static JSValue js_print_getFoldMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    return JS_NewString(ctx, staple_config->fold);
}

static JSValue js_print_setShiftMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one number parmeter");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *shift = JS_ToCString(ctx, argv[0]);
    strncpy(staple_config->shift,shift,strlen(shift));

	JS_FreeCString(ctx, shift);
    return JS_TRUE;
}

static JSValue js_print_getShiftMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSStapleConfigMode  *staple_config = JS_GetOpaque2(ctx, this_val, js_staple_config_class_id);
    if(!staple_config)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    return JS_NewString(ctx, staple_config->shift);
}



static JSValue js_print_addParameter(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 2 || !JS_IsString(argv[0]) || !JS_IsObject(argv[1]))
    {
        Print_Error("argc %d, The parmeter are the prop_key(string) and prop_val(object)", argc);
        return JS_FALSE;
    }

    JSPrintParameter *para = JS_GetOpaque2(ctx, this_val, js_print_parameter_class_id);
    if(!para)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *prop_key = JS_ToCString(ctx, argv[0]);

    if(!strcmp(prop_key, "PRINT_PARAM_COLLATEMODE"))
    {
        JSCollateMode  *collate_mode = JS_GetOpaque2(ctx, argv[1], js_collate_mode_class_id);
        if(!collate_mode)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }

        para->collate_mode = *collate_mode;
        Print_Log("para->collate_mode %d\n",para->collate_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_COPIES"))
    {
        JSCopies  *copies = JS_GetOpaque2(ctx, argv[1], js_copies_class_id);
        if(!copies)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }

        para->copies = *copies;
        Print_Log("para->copies %d\n",para->copies.num);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_DUPLEXPRINTMODE"))
    {
        JSDuplexPrintMode  *duplex_mode = JS_GetOpaque2(ctx, argv[1], js_duplex_print_mode_class_id);
        if(!duplex_mode)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }

        para->duplex_mode = *duplex_mode;
        Print_Log("para->duplex_mode %d\n",para->duplex_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_OUTPUTTRAY"))
    {
        JSOutPutTray  *output_tary = JS_GetOpaque2(ctx, argv[1], js_output_tray_class_id);
        if(!output_tary)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }
        memcpy(&para->tray,output_tary,sizeof(JSOutPutTray));
        Print_Log("para->tray.out_tray %s,para->tray.paper_size %s\n",para->tray.out_tray,para->tray.paper_size);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_PAPERSAVEMODE"))
    {
        JSPaperSaveMode  *paper_save_mode = JS_GetOpaque2(ctx, argv[1], js_paper_save_mode_class_id);
        if(!paper_save_mode)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }

        para->paper_save_mode = *paper_save_mode;
        Print_Log("para->paper_save_mode %d\n",para->paper_save_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_IMAGEORIENTATIONMODE"))
    {
        JSImageOrientationMode  *image_orientation_mode = JS_GetOpaque2(ctx, argv[1], js_image_orientation_mode_class_id);
        if(!image_orientation_mode)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }

        para->image_orientation_mode = *image_orientation_mode;
        Print_Log("para->image_orientation_mode %d\n",para->image_orientation_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_PAPERTYPE"))
    {
        JSPaperType  *paper_type = JS_GetOpaque2(ctx, argv[1], js_paper_type_class_id);
        if(!paper_type)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }
        memcpy(&para->paper_type,paper_type,sizeof(JSPaperType));
        Print_Log("para->paper_type %s\n",para->paper_type.type);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_COLORMODE"))
    {
        JSColor  *color_mode = JS_GetOpaque2(ctx, argv[1], js_color_mode_class_id);
        if(!color_mode)
        {
            Print_Error("JS_GetOpaque2 failed\n");
			JS_FreeCString(ctx, prop_key);
            return JS_FALSE;
        }
        para->color = *color_mode;
        Print_Log("para->color_mode %d \n",para->color.mode);
    }
    else
    {
        Print_Error("prop_key not support -> %s\n",prop_key);
        JS_FreeCString(ctx, prop_key);
        return JS_FALSE;
    }

    JS_FreeCString(ctx, prop_key);

    return JS_TRUE;

}



JSValue js_print_removeParameter(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, The parmeter is the prop_key(string)", argc);
        return JS_FALSE;
    }

    JSPrintParameter *para = JS_GetOpaque2(ctx, this_val, js_print_parameter_class_id);
    if(!para)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_FALSE;
    }

    const char *prop_key = JS_ToCString(ctx, argv[0]);

    if(!strcmp(prop_key, "PRINT_PARAM_COLLATEMODE"))
    {
        para->collate_mode.mode = 0;
        Print_Log("para->collate_mode %d\n",para->collate_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_COPIES"))
    {
        para->copies.num = 0;
        Print_Log("para->copies %d\n",para->copies.num);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_DUPLEXPRINTMODE"))
    {
        para->duplex_mode.mode = 0;
        Print_Log("para->duplex_mode %d\n",para->duplex_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_OUTPUTTRAY"))
    {
        memset(&para->tray.out_tray,0,sizeof(para->tray.out_tray));
        memset(&para->tray.paper_size,0,sizeof(para->tray.paper_size));
        Print_Log("para->tray.out_tray %s,para->tray.paper_size %s\n",para->tray.out_tray,para->tray.paper_size);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_PAPERSAVEMODE"))
    {
        para->paper_save_mode.mode = 0;;
        Print_Log("para->paper_save_mode %d\n",para->paper_save_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_IMAGEORIENTATIONMODE"))
    {
        para->image_orientation_mode.mode = 0;
        Print_Log("para->image_orientation_mode %d\n",para->image_orientation_mode.mode);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_PAPERTYPE"))
    {
        memset(&para->paper_type.type,0,sizeof(para->paper_type.type));
        Print_Log("para->paper_type %s\n",para->paper_type.type);
    }
    else if(!strcmp(prop_key, "PRINT_PARAM_COLORMODE"))
    {
        para->color.mode = 0;
        Print_Log("para->color_mode %d\n",para->color.mode);
    }
    else
    {
        Print_Error("prop_key not support -> %s\n",prop_key);
        JS_FreeCString(ctx, prop_key);
        return JS_FALSE;
    }

    JS_FreeCString(ctx, prop_key);

    return JS_TRUE;

}


static JSValue js_print_job_ctor(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSPrintJob *job = js_malloc(ctx, sizeof(*job));
    if(!job)
    {
        Print_Error("js_malloc failed\n");
        return JS_EXCEPTION;
    }
    memset(job,0,sizeof(*job));

    return JS_ExtendClass(ctx, this_val, job, js_print_job_class_id);
}

static JSValue js_print_urlJobInit(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    const char *url_str = JS_ToCString(ctx, argv[0]);

    strncpy(job->url,url_str,strlen(url_str));
    strncpy(job->job_type,"PRINT_URL",strlen("PRINT_URL"));
	JS_FreeCString(ctx, url_str);
    return JS_TRUE;
}


static JSValue js_print_usbMemoryJobInit(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    const char *file_path = JS_ToCString(ctx, argv[0]);

    strncpy(job->file_path,file_path,strlen(file_path));
    strncpy(job->job_type,"PRINT_USBMEMORY",strlen("PRINT_USBMEMORY"));
	JS_FreeCString(ctx, file_path);
    return JS_TRUE;
}

static JSValue js_print_pathJobInit(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        Print_Error("argc %d, Must be only one string parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    const char *file_path = JS_ToCString(ctx, argv[0]);

    strncpy(job->file_path,file_path,strlen(file_path));
    strncpy(job->job_type,"PRINT_PATH",strlen("PRINT_PATH"));
	JS_FreeCString(ctx, file_path);
    return JS_TRUE;
}


static JSValue js_print_interanlPageJobInit(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one number parmeter", argc);
        return JS_FALSE;
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    int type = argv[0];
    #if 0
    if(JS_ToInt32(ctx, &type, argv[0]))
    {
        Print_Error("JS_ToInt32 failed\n");
        return JS_EXCEPTION;
    }
    #endif

    strncpy(job->job_type,"PRINT_LOCAL",strlen("PRINT_LOCAL"));

    job->page_type = type;
    Print_Log("zc internale apge type = %d\n",job->page_type);

    return JS_TRUE;

}


static JSValue js_print_encryptJobInit(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required\n", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    strncpy(job->job_type,"PRINT_ENCRYPT",strlen("PRINT_ENCRYPT"));

    return JS_TRUE;

}

static  JSValue js_print_JobStart(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int value = -1;
    int ret = 0;
    char data[12] = {0};
    int data_size = sizeof( data );

    if(argc != 3 || !JS_IsNumber(argv[0]) || !JS_IsObject(argv[1]))
    {
        Print_Error("The parmeter are the param(object) and quota(object/null) argc %d, tag %d\n", argc, JS_VALUE_GET_TAG(argv[0]));
        return JS_NewInt32(ctx, -1);
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_NewInt32(ctx, -1);
    }

    int wo_num = 0;
    if(JS_ToInt32(ctx, &wo_num, argv[0]))
    {
        Print_Error("JS_ToInt32 failed\n");
        return JS_NewInt32(ctx, -1);
    }

    Print_Log("job wo_num %d\n",wo_num);

    JSPrintParameter *para = JS_GetOpaque2(ctx, argv[1], js_print_parameter_class_id);
    if(!para)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_NewInt32(ctx, -1);
    }

    memcpy(&job->job_param,para,sizeof(JSPrintParameter));

    job->job_id = 0;
    job->job_woNum = wo_num;

    Print_Log("job_type:%s\n", job->job_type);

    re_print_jod_info(job);

    if(print_job_start_to_mfp(job) < 0 )
    {
        Print_Error("print_job_start_to_mfp failed\n");
        return JS_ThrowTypeError(ctx, "print_job_start_to_mfp failed");
    }


    ret = RecvMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_START, &value,data,&data_size,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    job->job_id = atoi(data);

    printf("js_print_JobStart: job_id = %d\n",job->job_id);

    //return JS_NewInt32(ctx, 0);
    return JS_NewInt32(ctx, job->job_id);
}

static  JSValue js_print_setJobId(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{

    if(argc != 1 || !JS_IsNumber(argv[0]))
    {
        Print_Error("argc %d, Must be only one bool parmeter", argc);
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }


    int job_id;
    if(JS_ToInt32(ctx, &job_id, argv[0]))
    {
        Print_Error("JS_ToBool failed\n");
        return JS_FALSE;
    }

    job->job_id = job_id;

    return JS_NewInt32(ctx, job->job_id);

}


static  JSValue js_print_getJobId(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int value = -1;
    int ret = 0;
    int data = 0;
    int data_size = sizeof( data );

    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    printf("[line:%d] %s\n",__LINE__,__func__);
    ret = SendMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_ID, value,0,NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }

    ret = RecvMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_ID, &value,&data,&data_size,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    job->job_id = data;

    if( 0 == job->job_id)
    {
       return JS_UNDEFINED;
    }

    return JS_NewInt32(ctx, job->job_id);
}

JSValue js_print_getJobState(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char job_state[32] = {0};

    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    len = sizeof(job_state);
    SendMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_STATE, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_PRINT, MSG_PRINT_SUB_JOB_STATE, &respone,job_state,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,job_state = %s\n",__LINE__,__func__,job_state);

    return JS_NewString(ctx, job_state);
}


static  JSValue js_print_JobCancel(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
    Print_Log(" entry\n");
    JSPrintJob *job = JS_GetOpaque2(ctx, this_val, js_print_job_class_id);
    if(!job)
    {
        Print_Error("JS_GetOpaque2 failed\n");
        return JS_EXCEPTION;
    }

    if(print_job_cancel_to_mfp(job) < 0 )
    {
        Print_Error("print_job_cancel_to_mfp failed\n");
        return JS_ThrowTypeError(ctx, "print_job_cancel_to_mfp failed");
    }

    return JS_UNDEFINED;

}

static JSValue js_print_getEncryptJobUserNameList(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        Print_Error("argc %d, No input parameters are required\n", argc);
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    return JS_UNDEFINED;
}


static JSValue js_print_getEncryptJobList(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if(argc != 2 || !JS_IsString(argv[0]) || !JS_IsString(argv[1]))
    {
        Print_Error("argc %d, tag %d\n", argc, JS_VALUE_GET_TAG(argv[0]));
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    const char *name =  JS_ToCString(ctx, argv[0]);
    const char *password =  JS_ToCString(ctx, argv[1]);

    Print_Log("name %s,password %s\n",name,password);

	JS_FreeCString(ctx, name);
	JS_FreeCString(ctx, password);

    return JS_UNDEFINED;
}

static const JSCFunctionListEntry PEDK_js_collate_mode_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getCollateMode", 0, js_print_getCollateMode),
    JS_CFUNC_DEF("setCollateMode", 1, js_print_setCollateMode),
};

static const JSCFunctionListEntry PEDK_js_copies_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getCopies", 0, js_print_getCopies),
    JS_CFUNC_DEF("setCopies", 1, js_print_setCopies),
};

static const JSCFunctionListEntry PEDK_js_duplex_print_mode_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getDuplexPrintMode", 0, js_print_getDuplexPrintMode),
    JS_CFUNC_DEF("setDuplexPrintMode", 1, js_print_setDuplexPrintMode),
};

static const JSCFunctionListEntry PEDK_js_color_mode_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getColorMode", 0, js_print_getColorMode),
    JS_CFUNC_DEF("setColorMode", 1, js_print_setColorMode),
};

static const JSCFunctionListEntry PEDK_js_image_orientation_mode_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getImageOrientationMode", 0, js_print_getImageOrientationMode),
    JS_CFUNC_DEF("setImageOrientationMode", 1, js_print_setImageOrientationMode),
};

static const JSCFunctionListEntry PEDK_js_output_tary_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("setPaperSize", 1, js_print_setPaperSize),
    JS_CFUNC_DEF("setTray",      1, js_print_setTray),
    JS_CFUNC_DEF("getPaperSize", 0, js_print_getPaperSize),
    JS_CFUNC_DEF("getTray",      0, js_print_getTray),

};


static const JSCFunctionListEntry PEDK_js_paper_save_mode_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getPaperSaveMode", 0, js_print_getPaperSaveMode),
    JS_CFUNC_DEF("setPaperSaveMode", 1, js_print_setPaperSaveMode),
};


static const JSCFunctionListEntry PEDK_js_paper_type_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getPaperType", 0, js_print_getPaperType),
    JS_CFUNC_DEF("setPaperType", 1, js_print_setPaperType),
};

static const JSCFunctionListEntry PEDK_js_staple_config_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("getStapleMode", 0, js_print_getStapleMode),
    JS_CFUNC_DEF("setStapleMode", 1, js_print_setStapleMode),
    JS_CFUNC_DEF("getPunchMode", 0, js_print_getPunchMode),
    JS_CFUNC_DEF("setPunchMode", 1, js_print_setPunchMode),
    JS_CFUNC_DEF("getFoldMode", 0, js_print_getFoldMode),
    JS_CFUNC_DEF("setFoldMode", 1, js_print_setFoldMode),
    JS_CFUNC_DEF("getShiftMode", 0, js_print_getShiftMode),
    JS_CFUNC_DEF("setShiftMode", 1, js_print_setShiftMode),
};

static const JSCFunctionListEntry PEDK_js_set_parameter_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("addParameter",    2, js_print_addParameter),
    JS_CFUNC_DEF("removeParameter", 1, js_print_removeParameter),
};



static const JSCFunctionListEntry PEDK_js_print_job_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("URLJobPrint",          1, js_print_urlJobInit),
    JS_CFUNC_DEF("PrintFromUSBMemoryJob",1, js_print_usbMemoryJobInit),
    JS_CFUNC_DEF("PrintFromPath",        1, js_print_pathJobInit),
    JS_CFUNC_DEF("InternalPagePrint",    1, js_print_interanlPageJobInit),
    JS_CFUNC_DEF("EncryptJobPrint",      0, js_print_encryptJobInit),
    JS_CFUNC_DEF("getUserNameList",      0, js_print_getEncryptJobUserNameList),
    JS_CFUNC_DEF("getEncryptJobList",    2, js_print_getEncryptJobList),
    JS_CFUNC_DEF("setJobId",             1, js_print_setJobId),
    JS_CFUNC_DEF("getJobId",             0, js_print_getJobId),
    JS_CFUNC_DEF("getJobState",          0, js_print_getJobState),
    JS_CFUNC_DEF("start",                3, js_print_JobStart),
    JS_CFUNC_DEF("cancel",               0, js_print_JobCancel),
};


static JSCreatClassParam PEDK_print_class_list[] = {
    {&js_collate_mode_class_id,           &js_collate_mode_class,           PEDK_js_collate_mode_funcs,           countof(PEDK_js_collate_mode_funcs),           js_print_collate_mode_ctor,           1, "PEDKCollateMode"},
    {&js_copies_class_id,                 &js_copies_class,                 PEDK_js_copies_funcs,                 countof(PEDK_js_copies_funcs),                 js_print_copies_ctor,                 1, "PEDKCopies"},
    {&js_duplex_print_mode_class_id,      &js_duplex_print_mode_class,      PEDK_js_duplex_print_mode_funcs,      countof(PEDK_js_duplex_print_mode_funcs),      js_print_duplex_print_mode_ctor,      1, "PEDKDuplexPrintMode"},
    {&js_image_orientation_mode_class_id, &js_image_orientation_mode_class, PEDK_js_image_orientation_mode_funcs, countof(PEDK_js_image_orientation_mode_funcs), js_print_image_orientation_mode_ctor, 1, "PEDKImageOrientationMode"},
    {&js_output_tray_class_id,            &js_output_tray_class,            PEDK_js_output_tary_funcs,            countof(PEDK_js_output_tary_funcs),            js_print_output_tray_ctor,            2, "PEDKOutPutTray"},
    {&js_paper_save_mode_class_id,        &js_paper_save_mode_class,        PEDK_js_paper_save_mode_funcs,        countof(PEDK_js_paper_save_mode_funcs),        js_print_paper_save_mode_ctor,        1, "PEDKPaperSaveMode"},
    {&js_paper_type_class_id,             &js_paper_type_class,             PEDK_js_paper_type_funcs,             countof(PEDK_js_paper_type_funcs),             js_print_paper_type_ctor,             1, "PEDKPaperType"},
    {&js_print_parameter_class_id,        &js_print_parameter_class,        PEDK_js_set_parameter_funcs,          countof(PEDK_js_set_parameter_funcs),          js_print_parameter_ctor,              0, "PEDKPrintParameterSet"},
    {&js_print_job_class_id,              &js_print_job_class,              PEDK_js_print_job_funcs,              countof(PEDK_js_print_job_funcs),              js_print_job_ctor,                    0, "PEDKPrintJob"},
    {&js_staple_config_class_id,          &js_staple_config_class,          PEDK_js_staple_config_funcs,          countof(PEDK_js_staple_config_funcs),          js_print_staple_config_ctor,          4, "PEDKStapleConfig"},
    {&js_color_mode_class_id,             &js_color_mode_class,             PEDK_js_color_mode_funcs,             countof(PEDK_js_color_mode_funcs),             js_print_color_mode_ctor,             1, "PEDKColorMode"},
};

int js_print_init(JSContext *ctx, JSValueConst global)
{
    JSValue print_proto, print_constructor;
    int i = 0;

    /* creat the classes */
    for(i = 0; i < sizeof(PEDK_print_class_list)/sizeof(PEDK_print_class_list[0]); i++)
    {
        JS_NewClassID(PEDK_print_class_list[i].class_id);
        JS_NewClass(JS_GetRuntime(ctx), *PEDK_print_class_list[i].class_id, PEDK_print_class_list[i].pclass);

        print_proto = JS_NewObject(ctx);

        JS_SetPropertyFunctionList(ctx, print_proto, PEDK_print_class_list[i].PEDK_funs, PEDK_print_class_list[i].fun_len);

        JS_SetClassProto(ctx, *PEDK_print_class_list[i].class_id, print_proto);

        print_constructor = JS_NewCFunction2(ctx, PEDK_print_class_list[i].ctor, PEDK_print_class_list[i].name,
                                                                PEDK_print_class_list[i].ctor_parm_num, JS_CFUNC_constructor, 0);
        JS_SetConstructor(ctx, print_constructor, print_proto);


        JS_DefinePropertyValueStr(ctx, global, PEDK_print_class_list[i].name, print_constructor, JS_PROP_C_W_E);
    }

    return 0;
}
