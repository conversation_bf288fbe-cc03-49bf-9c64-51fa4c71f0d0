
/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pesf.c
 * @addtogroup pesf
 * @{
 * @addtogroup runtime
 * @autor
 * @date 2024-06-11
 * @brief pesf init
 */

#include "runtime/modules/std/std.h"
#include "runtime/utils/quickjs_utils.h"
#include "runtime/modules/module_manager.h"
#include "PEDK_defined.h"
//#include "pesf_net_socket.h"

#define PESF_JS_FILE_PATH(FILE_NAME) ("/pesf/js/modules/" FILE_NAME)

static const char *js_files[] = {

    //    "modules/PESF/demo.js",
    PESF_JS_FILE_PATH("common.js"),
    PESF_JS_FILE_PATH("disptach_event.js"),
    PESF_JS_FILE_PATH("jobctl_api.js"),
    PESF_JS_FILE_PATH("print_api.js"),
    PESF_JS_FILE_PATH("scan_api.js"),
    PESF_JS_FILE_PATH("copy_api.js"),
    PESF_JS_FILE_PATH("ui_api.js"),
    PESF_JS_FILE_PATH("addressbook.js"),
    PESF_JS_FILE_PATH("beeper.js"),
    PESF_JS_FILE_PATH("iccard.js"),
    PESF_JS_FILE_PATH("capability.js"),
    PESF_JS_FILE_PATH("whitelist.js"),
    PESF_JS_FILE_PATH("lowpower.js"),
    PESF_JS_FILE_PATH("app_setting.js"),
    PESF_JS_FILE_PATH("status_api.js"),
    PESF_JS_FILE_PATH("smb.js"),
    PESF_JS_FILE_PATH("ftp.js"),
    PESF_JS_FILE_PATH("/email.js"),
    PESF_JS_FILE_PATH("setting.js"),
    PESF_JS_FILE_PATH("system_setting.js"),
    PESF_JS_FILE_PATH("usbdevice.js"),
    PESF_JS_FILE_PATH("total_pages.js"),
    PESF_JS_FILE_PATH("http.js"),
    PESF_JS_FILE_PATH("deviceinfo.js"),
    PESF_JS_FILE_PATH("productinfo.js"),
    PESF_JS_FILE_PATH("storage.js"),
    PESF_JS_FILE_PATH("traysetting.js"),
    };

static void pesf_init()
{
     LOG_I("pesf","pesf_init\n");
}

static void pesf_release()
{
    LOG_I("pesf","pesf_release\n");
}

static void pesf_instance(PeSFRunTime *prt)
{
	printf("start init all module\n");
    JSContext *ctx = prt->qjs_ctx;

    JSValue global = JS_GetGlobalObject(ctx);

     // 注册 PEDK 到 global 对象中
        JSValue pedk = JS_NewObject(ctx);
        JSValue device = JS_NewObject(ctx);
        JSValue addressbook = JS_NewObject(ctx);
        JSValue jobs = JS_NewObject(ctx);
        JSValue scan = JS_NewObject(ctx);
        JSValue print = JS_NewObject(ctx);
        JSValue copy = JS_NewObject(ctx);
        JSValue jobctl = JS_NewObject(ctx);
        JSValue ui = JS_NewObject(ctx);
        JSValue widget = JS_NewObject(ctx);
        JSValue usbh = JS_NewObject(ctx);
        JSValue iccard = JS_NewObject(ctx);
        JSValue usbd = JS_NewObject(ctx);
        JSValue usbdevice = JS_NewObject(ctx);
        JSValue powersave = JS_NewObject(ctx);
        JSValue setting = JS_NewObject(ctx);
        JSValue SystemSetting = JS_NewObject(ctx);
        JSValue netsetting = JS_NewObject(ctx);
        JSValue appsetting = JS_NewObject(ctx);
        JSValue status = JS_NewObject(ctx);
        JSValue auth = JS_NewObject(ctx);
        JSValue macfilter = JS_NewObject(ctx);
        JSValue whitelist = JS_NewObject(ctx);
        JSValue net = JS_NewObject(ctx);
    //    JSValue http = JS_NewObject(ctx);
        JSValue ssl = JS_NewObject(ctx);
        JSValue net_socket = JS_NewObject(ctx);
        JSValue ProductInfo = JS_NewObject(ctx);
        JSValue storage = JS_NewObject(ctx);
        JSValue DeviceInfo = JS_NewObject(ctx);
		JSValue traySetting = JS_NewObject(ctx);

        // 注册 scan 到 jobs 对象中
        JS_SetPropertyStr(ctx, jobs, "scan", scan);
        // 注册 print 到 jobs 对象中
        JS_SetPropertyStr(ctx, jobs, "print", print);
        // 注册 copy 到 jobs 对象中
        JS_SetPropertyStr(ctx, jobs, "copy", copy);

        // 注册 jobs 到 PEDK 对象中
        JS_SetPropertyStr(ctx, pedk, "jobs", jobs);
        // 注册 addressbook 到 PEDK 对象中
        JS_SetPropertyStr(ctx, pedk, "addressbook", addressbook);
        // 注册 jobctl 到 PEDK 对象中
        JS_SetPropertyStr(ctx, pedk, "jobctl", jobctl);
        // 注册 whitelist 到 auth 对象中
        JS_SetPropertyStr(ctx, auth, "whitelist", whitelist);
        // 注册 auth 到 PEDK 对象中
        JS_SetPropertyStr(ctx, pedk, "auth", auth);
        // 注册 macfilter 到 auth 对象中
        JS_SetPropertyStr(ctx, auth, "macfilter", macfilter);
        // 注册 ui 到 PEDK 对象中
        JS_SetPropertyStr(ctx, pedk, "ui", ui);
        // 注册 widgets 到 ui 对象中
        JS_SetPropertyStr(ctx, ui, "widget", widget);

        // 注册 usbh 到 PEDK 对象中
         JS_SetPropertyStr(ctx, pedk, "usbh", usbh);
         // 注册 iccard 到 usbh 对象中
         JS_SetPropertyStr(ctx, usbh, "iccard", iccard);
        // 注册 usbd 到 PEDK 对象中
         JS_SetPropertyStr(ctx, pedk, "usbd", usbd);
         // 注册 usbdevice 到 usbh 对象中
         JS_SetPropertyStr(ctx, usbd, "usbdevice", usbdevice);
        // 注册 status 到 device 对象中
        JS_SetPropertyStr(ctx, device, "status", status);
        // 注册 setting 到 device 对象中
        JS_SetPropertyStr(ctx, device, "setting", setting);

        // 注册 systemSetting 到 setting 对象中
        JS_SetPropertyStr(ctx, setting, "SystemSetting", SystemSetting);
        // 注册 netsetting 到 setting 对象中
        JS_SetPropertyStr(ctx, setting, "netsetting", netsetting);
        // 注册 productinfo 到 setting 对象中
        JS_SetPropertyStr(ctx, setting, "ProductInfo", ProductInfo);
        // 注册 deviceinfo 到 setting 对象中
        JS_SetPropertyStr(ctx, setting, "DeviceInfo", DeviceInfo);
		// 注册 traySetting 到 setting 对象中
        JS_SetPropertyStr(ctx, setting, "traySetting", traySetting);

        // 注册 appsetting 到 device 对象中
        JS_SetPropertyStr(ctx, device, "appsetting", appsetting);
         // 注册 device 到 PEDK 对象中
         JS_SetPropertyStr(ctx, pedk, "device", device);
         // 注册 powersave 到 device 对象中
         JS_SetPropertyStr(ctx, device, "powersave", powersave);
         // 注册 storage 到 device 对象中
         JS_SetPropertyStr(ctx, device, "storage", storage);

        js_print_init(ctx, global);
        js_copy_init(ctx, global);
        js_scan_api_init(ctx, global);
        js_ui_init(ctx, global);
        js_net_init(ctx, global);
        js_capability_init(ctx, global);
        //js_beep_init(ctx, global);
        js_usbd_init(ctx, global);
        js_whitelist_init(ctx, global);
        js_lowpower_init(ctx, global);
        js_status_init(ctx, global);
        js_system_setting_init(ctx, global);
        js_setting_system_beep_init(ctx, global);
        js_setting_init(ctx, global);
        js_appsetting_init(ctx, global);
        js_device_net_setting_init(ctx, global);
        js_jobctl_init(ctx, global);
        js_device_deviceinfo_init(ctx, global);
        js_device_productinfo_init(ctx, global);
        js_tray_setting_init(ctx, global);
        js_storage_init(ctx, global);
        js_http_init(ctx, global);
        // 注册 PEDK 到 global 对象中
        JS_SetPropertyStr(ctx, global, "pedk", pedk);
    	addGlobalAPI(ctx, global);

        printf("start end \n");

        /* net */
        add_xhr(ctx);
        addSocket(ctx, net_socket);
        JS_SetPropertyStr(ctx, net, "socket", net_socket);
        JS_SetPropertyStr(ctx, net, "ssl", ssl);
        JS_SetPropertyStr(ctx, pedk, "net", net);
        js_ssl_init(ctx, ssl);
        js_ssl_cert_init(ctx,global);

        // 释放 global 对象的引用
        JS_FreeValue(ctx, global);
}

static void pesf_generalization(PeSFRunTime *prt)
{

}

static ModuleContext pesf = {
    .module_name = "pesf",
    .module_init = pesf_init,
    .module_release = pesf_release,
    .module_instance = pesf_instance,
    .module_generalization = pesf_generalization
};

MODULE_REGISTER(pesf);

/**
 * @}
 */

