/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_types.h
 * @addtogroup data_types
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal data typedef set
 */
#ifndef __PANTUM_TYPE_H__
#define __PANTUM_TYPE_H__

#ifdef __STDC__

#if __STDC_VERSION__ >= 199901L
#include <stdint.h>
#else
#include <bits/wordsize.h>

/* Signed. */
typedef signed char int8_t;
typedef short int   int16_t;
typedef int         int32_t;
#if __WORDSIZE == 64
typedef long int    int64_t;
#else
__extension__ typedef long long int int64_t;
#endif


/* Unsigned. */
typedef unsigned char       uint8_t;
typedef unsigned short int  uint16_t;
typedef unsigned int        uint32_t;
#if __WORDSIZE == 64
typedef unsigned long int   uint64_t;
#else
__extension__ typedef unsigned long long int uint64_t;
#endif


/* Types for 'void *' pointers. */
#if __WORDSIZE == 64
typedef long int intptr_t;
typedef unsigned long int uintptr_t;
#else
typedef int intptr_t;
typedef unsigned int uintptr_t;
#endif
#endif

#else

#error "__STDC__ undefined"
/*__STDC__ not define*/
#endif


#endif
/**
 *@}
 */
