/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file hal_load.c
 * @addtogroup hal
 * @{
 * <AUTHOR>
 * @date 2024-07-16
 * @brief hal module prolog/epilog function.
 */

#include "hal.h"

int32_t hal_prolog(void)
{
    int ret = 0;
    void* wifi_handle = NULL;

    ret = pi_hal_init();
    if ( ret < 0 )
    {
        return ret;
    }

    /* load wifi driver */
    ret = pi_hal_wifi_request(&wifi_handle, HAL_REQUEST_FLAG_NONBLOCK);
    if ( ret < 0 )
    {
        return ret;
    }

    pi_hal_wifi_load(wifi_handle);
    pi_hal_wifi_free(&wifi_handle);

    return ret;
}

void hal_epilog(void)
{
    pi_hal_exit();
}

/**
 *@}
 */
