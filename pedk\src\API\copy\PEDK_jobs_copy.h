#ifndef _PEDK_JOBS_COPY_H_
#define _PEDK_JOBS_COPY_H_

#include <quickjs.h>

/*******************copy firmware adaptor*******************/
#include "pthread.h"
#include "stdio.h"
#include "string.h"

/***** common param define start *****/

#ifndef uint8_t
typedef unsigned char uint8_t;
#endif

#ifndef uint16_t
typedef unsigned short uint16_t;
#endif

#ifndef uint32_t
typedef unsigned int uint32_t;
#endif

#define USE_PUBLIC_HEADER_FILE 0

#if USE_PUBLIC_HEADER_FILE

#include "public_data_proc.h"

#else

typedef enum
{
    COPY_JOB_REQUEST_ADAPTOR_7265 = 0,
    COPY_JOB_REQUEST_PEDK = 1,
    COPY_JOB_REQUEST_7265 = 2,
    COPY_JOB_REQUEST_MAX = 0xFFFFFFFF
}COPY_JOB_REQUEST_TYPE;

typedef struct
{
    int         paper_size;
    uint16_t    width;
    uint16_t    height;
}PAPER_SIZE_DETAIL_S;

typedef enum
{
    PAPER_SIZE_7265_A4                   = 0,///< 210.0 x 297.0
    PAPER_SIZE_7265_A5,                      ///< 148.5 x 210.0
    PAPER_SIZE_7265_A5L,                     ///< 210.0 x 148.0
    PAPER_SIZE_7265_B5,
    PAPER_SIZE_7265_FULL_PLATEN,
    PAPER_SIZE_7265_LETTER,                  ///< 215.9 x 279.4
    PAPER_SIZE_7265_CARD,
    PAPER_SIZE_7265_FOLIO,                   ///< 216.0 x 330.0
    PAPER_SIZE_7265_ISO_B5,                  ///< 176.0 x 250.0
    PAPER_SIZE_7265_A6,                      ///< 105.0 x 148.0
    PAPER_SIZE_7265_USER_DEFINE,             ///< 75~218x148~356,long paper above 356~1200
    PAPER_SIZE_7265_LEGAL13,                 ///< 215.9 x 330.0
    PAPER_SIZE_7265_LEGAL14,                 ///< 215.9 x 355.6
    PAPER_SIZE_7265_JIS_B5,                  ///< 182.0 x 257.0
    PAPER_SIZE_7265_ENV_MONARCH,             ///< 098.4 x 190.5
    PAPER_SIZE_7265_ENV_DL,                  ///< 110.0 x 220.0
    PAPER_SIZE_7265_ENV_C5,                  ///< 162.0 x 229.0
    PAPER_SIZE_7265_ENV_10,                  ///< 104.8 x 241.3
    PAPER_SIZE_7265_YOUKEI_SIZE4,            ///< 105.0 x 234.0
    PAPER_SIZE_7265_JAPANESE_POSTCARD,       ///< 100.0 x 148.0
    PAPER_SIZE_7265_CHOUKEI_SIZE3,           ///< 120.0 x 235.0
    PAPER_SIZE_7265_CUSTOM_16K,              ///< 185.0 x 260.0
    PAPER_SIZE_7265_CUSTOM_BIG_16K,          ///< 195.0 x 270.0
    PAPER_SIZE_7265_CUSTOM_32K,              ///< 130.0 x 185.0
    PAPER_SIZE_7265_CUSTOM_BIG_32K,          ///< 135.0 x 195.0
    PAPER_SIZE_7265_EXECUTIVE,               ///< 184.0 x 267.0
    PAPER_SIZE_7265_OFICIO,                  ///< 216.0 x 343.0
    PAPER_SIZE_7265_STATEMENT,               ///< 140.0 x 216.0
    PAPER_SIZE_7265_ENV_C6,                  ///< 114.3 x 162.0
    PAPER_SIZE_7265_ZL,                      ///< 120.0 x 230.0
    PAPER_SIZE_7265_B6,                      ///< 125.0 x 176.0
    PAPER_SIZE_7265_ENV_B6,                  ///< 125.0 x 176.0
    PAPER_SIZE_7265_POSTCARD,                ///< 148.0 x 200.0
    PAPER_SIZE_7265_YOUGATA2,                ///< 114.0 x 162.0
    PAPER_SIZE_7265_NAGAGATA3,               ///< 120.0 x 235.0
    PAPER_SIZE_7265_YOUNAGA3,                ///< 120.0 x 235.0
    PAPER_SIZE_7265_YOUGATA4,                ///< 105.0 x 235.0
    PAPER_SIZE_7265_LONG,                    ///< 210.0 x 1200.0
    PAPER_SIZE_7265_A3,                      ///< 297.0 x 420.0
    PAPER_SIZE_7265_A4L,                     ///< 297.0 x 210.0
    PAPER_SIZE_7265_JIS_B6,                  ///< 128.0 x 182.0
    PAPER_SIZE_7265_JIS_B4,                  ///< 257.0 x 364.0
    PAPER_SIZE_7265_4X6_INCH,                ///< 101.6 x 152.4 / 4'' x 6''
    PAPER_SIZE_7265_INVOICE,                 ///< 139.7 x 215.9 / 5.5'' x 8.5''
    PAPER_SIZE_7265_QUARTO,                  ///< 254.0 x 203.2 / 10'' x 8''
    PAPER_SIZE_7265_G_LETTER,                ///< 266.0 x 203.2 /10.5'' x 8''
    PAPER_SIZE_7265_11X14_INCH,              ///< 279.4 x 355.6 / 11'' x 14''
    PAPER_SIZE_7265_LEDGER,                  ///< 279.4 x 431.8 /11'' x 17''
    PAPER_SIZE_7265_8K,                      ///< 270.0 x 390.0
    PAPER_SIZE_7265_SRA3 ,                   ///< 320.0 x 450.0
    PAPER_SIZE_7265_FOOLSCAP1,               ///< 203.0 x 330.2 / 8''x13''
    PAPER_SIZE_7265_FOOLSCAP2,               ///< 209.6 x 330.2 / 8.25''x13''
    PAPER_SIZE_7265_FOOLSCAP3,               ///< 215.9 x 330.2 / 8.5''x13''
    PAPER_SIZE_7265_FOOLSCAP4,               ///< 220.0 x 330.0 / 8.65''x13''
    PAPER_SIZE_7265_FOOLSCAP5,               ///< 206.4 x 336.6 / 8.125''x13.25''
    PAPER_SIZE_7265_A3_WIDE1,                ///< 304.8 x 457.2 / 12''x18''
    PAPER_SIZE_7265_A3_WIDE2 ,               ///< 311.1 x 457.2 / 12.25''x18''
    PAPER_SIZE_7265_CUSTOM_BIG_16KL,         ///< 270.0 x 195.0
    PAPER_SIZE_7265_JIS_B5L ,                ///< 257.0 x 182.0
    PAPER_SIZE_7265_INVOICE_L,               ///< 215.9 x 139.7 / 8.5'' x 5.5''
    PAPER_SIZE_7265_EXECUTIVE_L,             ///< 266.7 x 184.2 / 10.5''x 7.25''
    PAPER_SIZE_7265_QUARTO_L	,               ///< 254.0 x 203.2 / 10'' x 8''
    PAPER_SIZE_7265_G_LETTER_L,              ///< 266.7 x 203.2 / 10.5''x 8''
    PAPER_SIZE_7265_LETTER_L,                ///< 279.4 x 215.9 / 11" x 8.5''
    PAPER_SIZE_7265_ISO_B5L,                 ///< 250.0 x 176.0

    PAPER_SIZE_7265_USER_DEFINE1,
    PAPER_SIZE_7265_USER_DEFINE2,
    PAPER_SIZE_7265_USER_DEFINE3,
    PAPER_SIZE_7265_USER_DEFINE4,
    PAPER_SIZE_7265_USER_DEFINE5,
    PAPER_SIZE_7265_B4 ,
    PAPER_SIZE_7265_A6CARD,

    PAPER_SIZE_7265_GENERAL,              ///< general size
    PAPER_SIZE_7265_MIXED  ,              ///< paper size by mixed
    PAPER_SIZE_7265_STATEMENT_L ,

    PAPER_SIZE_7265_B5L,
    PAPER_SIZE_7265_BIG_16K,
    PAPER_SIZE_7265_BIG_16KL,

    PAPER_SIZE_7265_AUTO,                           ///< paper size is auto selected
    PAPER_SIZE_7265_UNKOWN,
    PAPER_SIZE_7265_FULL_TABLE,                     ///< scan full table

    PAPER_SIZE_7265_INVALID              = 0xFF,    ///< please keep it in the end of the enum --- invalid data
    PAPER_SIZE_7265_MAX                  = 0xFFFFFFFF
}COPY_PARAM_PAPER_SIZE_7265_E;


typedef enum
{
    UNIT_INCH,
    UNIT_MM,
    UNIT_PIXEL,
    UNIT_MAX = 0xFFFFFFFF

}PAPER_UNIT_E;

typedef enum
{
    COPY_PARAM_JOB_TYPE_ID_CARD = 0,
    COPY_PARAM_JOB_TYPE_BILL,
    COPY_PARAM_JOB_TYPE_SCALE,
    COPY_PARAM_JOB_TYPE_NUP,
    COPY_PARAM_JOB_TYPE_CLONE,
    COPY_PARAM_JOB_TYPE_POSTER,
    COPY_PARAM_JOB_TYPE_SPILT,
    COPY_PARAM_JOB_TYPE_BOOKLET,
    COPY_PARAM_JOB_TYPE_MIX,
    COPY_PARAM_JOB_TYPE_PAGE_SPLIT,
    COPY_PARAM_7265_JOB_TYPE_MAX = 0xFFFFFFFF,
}COPY_PARAM_JOB_TYPE;

/*typedef enum
{
    COPY_PARAM_QUALITY_AUTO = 0,
    COPY_PARAM_QUALITY_MIXED,
    COPY_PARAM_QUALITY_PICTURE,
    COPY_PARAM_QUALITY_TXT,
    COPY_PARAM_7265_QUALITY_MAX = 0xFFFFFFFF,
}COPY_PARAM_QUALITY_TYPE;
    */
typedef enum
{
    COPY_QUALITY_NONE_AUTO   = 0,         ///< scan image quality auto
    COPY_QUALITY_TXT         = 1,         ///< scan image quality by text
    COPY_QUALITY_MIXED       = 2,         ///< scan image quality by text mixed picture
    COPY_QUALITY_PICTURE     = 3,         ///< scan image quality by picture
    COPY_QUALITY_MAX         = 0xFFFFFFFF

}COPY_QUALITY_MODE_7265_E;

typedef enum
{
    COPY_PARAM_COLOR_MONO = 0,     ///< color by mono
    COPY_PARAM_COLOR_RGB  = 1,     ///< color by rgb
    COPY_PARAM_COLOR_GRAY = 2,     ///< color by gray
    COPY_PARAM_COLOR_CMYK = 3,     ///< color by cmyk
    COPY_PARAM_COLOR_RK   = 4,     ///< color by red&black
    COPY_PARAM_COLOR_MAX  = 0xFFFFFFFF
}COPY_PARAM_COLOR_MODE_E;

typedef enum
{
    COPY_ORIGINAL_ORIENTATION_UP        = 0,        ///< up
    COPY_ORIGINAL_ORIENTATION_DOWN      = 1,        ///< down
    COPY_ORIGINAL_ORIENTATION_LEFT      = 2,        ///< left
    COPY_ORIGINAL_ORIENTATION_RIGHT     = 3,        ///< right
    COPY_ORIGINAL_ORIENTATION_INVALID   = 4,        ///< invalid data
    COPY_ORIGINAL_ORIENTATION_MAX       = 0XFFFFFFFF
}COPY_ORIGINAL_ORIENTATION_E;

typedef enum
{
    TRAY_RECEIVE_STANDARD                       = 0x00,                                         ///< the Additional tray
    TRAY_RECEIVE_1,                                                                             ///< the 1st tray
    TRAY_RECEIVE_2,                                                                             ///< the 2nd tray
    TRAY_RECEIVE_3,                                                                             ///< the saddle tray
    TRAY_RECEIVE_USE_PRINTER_CONFIG,                                                            ///< follow printer's panel config
    TRAY_RECEIVE_INVALID,                                                                       ///< please keep it in the end of the enum --- invalid data
    TRAY_RECEIVE_MAX = 0xFFFFFFFF
}TRAY_RECEIVE_E;

typedef enum
{
    SHIFT_MODE_OFF                              = 0x00,                                         ///< shift mode off
    SHIFT_MODE_ON,                                                                              ///< shift mode on
    SHIFT_MODE_USE_PRINTER_CONFIG,                                                              ///< use printer's config
    SHIFT_MODE_1,                                                                               ///< shift mode 1
    SHIFT_MODE_2,                                                                               ///< shift mode 2
    SHIFT_MODE_INVALID,                                                                         ///< please keep it in the end of the enum --- invalid data

    SHIFT_MODE_MAX = 0xFFFFFFFF
}SHIFT_MODE_E;

typedef enum
{
    COPY_POSTER_SIZE_NONE           = 0,            ///< poster off
    COPY_POSTER_SIZE_A3             = 1,            ///< poster A3 size
    COPY_POSTER_SIZE_A2             = 2,            ///< poster A2 size
    COPY_POSTER_SIZE_A1             = 3,            ///< poster A1 size
    COPY_POSTER_SIZE_A0             = 4,            ///< poster A0 size
    COPY_POSTER_SIZE_A0X2           = 5,            ///< poster A0x2 size
    COPY_POSTER_SIZE_B3             = 6,            ///< poster B3 size
    COPY_POSTER_SIZE_B2             = 7,            ///< poster B2 size
    COPY_POSTER_SIZE_B1             = 8,            ///< poster B1 size
    COPY_POSTER_SIZE_B0             = 9,            ///< poster B0 size
    COPY_POSTER_SIZE_48X64          = 10,           ///< poster 48X64 inch size
    COPY_POSTER_SIZE_44X68          = 11,           ///< poster 44X68 inch size
    COPY_POSTER_SIZE_36X48          = 12,           ///< poster 36X48 inch size
    COPY_POSTER_SIZE_34X44          = 13,           ///< poster 34X44 inch size
    COPY_POSTER_SIZE_24X36          = 14,           ///< poster 24X36 inch size
    COPY_POSTER_SIZE_22X34          = 15,           ///< poster 22X34 inch size
    COPY_POSTER_SIZE_18X24          = 16,           ///< poster 18X24 inch size
    COPY_POSTER_SIZE_17X22          = 17,           ///< poster 17X22 inch size
    COPY_POSTER_SIZE_11X17          = 18,           ///< poster 11X17 inch size
    COPY_POSTER_SIZE_USER_DEFINE    = 19,           ///< poster user define size
    COPY_POSTER_SIZE_MAX    = 0XFFFFFFFF
}COPY_POSTER_SIZE_E;

/******************************************************* common param define end ****************************************************************/


/******************************************************* kanas param define start *************************************************************/

typedef enum
{
    COPY_KANAS_NUP_CLOSE = 0,
    COPY_KANAS_NUP_2IN1,
    COPY_KANAS_NUP_2IN1_LANDSCAPE,
    COPY_KANAS_NUP_2IN1_PORTRAIT,
    COPY_KANAS_NUP_4IN1_LANDSCAPE,
    COPY_KANAS_NUP_4IN1_PORTRAIT,
    COPY_KANAS_NUP_MAX = 0xFFFFFFFF,
}COPY_KANAS_NUP_TYPE;
/******************************************************* kanas param define end *************************************************************/

/***** 7265 param define start *****/

typedef enum
{
    PRINT_MODE_7265_SINGLE = 0,              ///< single print
    PRINT_MODE_7265_AUTO_DUPLEX,             ///< auto double print
    PRINT_MODE_7265_MANUAL_DUPLEX,           ///< manual double print
    PRINT_MODE_7265_INVALID,                 ///< please keep it in the end of the enum --- invalid data
    PRINT_MODE_7265_MAX = 0xFFFFFFFF
}PRINT_MODE_7265_E;

typedef enum
{
    SCAN_MODE_7265_AUTO = 0,             ///< auto selection flat glass or glass
    SCAN_MODE_7265_FB   = 1,             ///< paper source by flat glass
    SCAN_MODE_7265_ADF  = 2,             ///< paper source by single ADF
    SCAN_MODE_7265_MADF = 3,             ///< paper source by manual ADF
    SCAN_MODE_7265_DADF = 4,             ///< paper source by double DADF
    SCAN_MODE_7265_RADF = 5,             ///< paper source by RADF
    //SCAN_MODE_DADF_R =6,          ///< ADF 自动双面（旋转进纸）
    SCAN_MODE_7265_SEP  = 7,             ///< 分离扫描
    SCAN_MODE_7265_DSEP = 8,             ///< 分离扫描(双面)
    SCAN_MODE_7265_MAX = 0xFFFFFFFF
}SCAN_MODE_7265_E;


typedef enum
{
    STAPLE_NUMBER_7265_NONE = 0x00,
    STAPLE_NUMBER_7265_ONE  = 0x01,
    STAPLE_NUMBER_7265_TWO  = 0x02,

    STAPLE_NUMBER_7265_MAX = 0xFFFFFFFF
}STAPLE_NUMBER_7265_E;

typedef enum
{
    STAPLE_ANGLE_7265_OFF                                                    = 0x00,
    STAPLE_ANGLE_7265_AUTO,                                                                          ///< The nail Angle is automatic
    STAPLE_ANGLE_7265_ZERO,                                                                          ///< The nail Angle is zero

    STAPLE_ANGLE_7265_INVALID = 0xFFFFFFFF                                                                        ///< Keep this value at the end of the enumeration, which is invalid
}STAPLE_ANGLE_7265_E;

typedef enum
{
    PUNCH_NUMBER_7265_NONE = 0x00,
    PUNCH_NUMBER_7265_TWO   = 0x02,
    PUNCH_NUMBER_7265_FOUR  = 0x04,

    PUNCH_NUMBER_7265_MAX = 0xFFFFFFFF
}PUNCH_NUMBER_7265_E;

typedef enum
{
    PUNCH_MODE_7265_OFF                              = 0x00,                                         ///< punch off
    PUNCH_MODE_7265_TWO_HOLES,
    PUNCH_MODE_7265_FOUR_HOLES,
    PUNCH_MODE_7265_USE_PRINTER_CONFIG,                                                              ///< use printer's config
    PUNCH_7265_LEFT_TWO_HOLES,                                                                       ///< Left 2 holes
    PUNCH_7265_LEFT_FOUR_HOLES,                                                                      ///< Left 4 holes
    PUNCH_7265_RIGHT_TWO_HOLES,                                                                      ///< Right 2 holes
    PUNCH_7265_RIGHT_FOUR_HOLES,                                                                     ///< Right 4 holes
    PUNCH_7265_TOP_TWO_HOLES,                                                                        ///< top 2 holes
    PUNCH_7265_TOP_FOUR_HOLES,                                                                       ///< top 4 holes
    PUNCH_7265_DOWN_TWO_HOLES,                                                                       ///< bottom 2 holes
    PUNCH_7265_DOWN_FOUR_HOLES,                                                                      ///< bottom 4 holes
    PUNCH_MODE_7265_INVALID,                                                                         ///< please keep it in the end of the enum --- invalid data
    PUNCH_MODE_7265_MAX = 0xFFFFFFFF
}PUNCH_MODE_7265_E;

typedef enum
{
    STAPLE_MODE_7265_OFF = 0x00,                                                                     ///< staple invaild param
    STAPLE_MODE_7265_LEFT_AUTO,                                                                      ///< Binding on the left
    STAPLE_MODE_7265_RIGHT_AUTO,                                                                     ///< Binding on the right
    STAPLE_MODE_7265_RIGHT_ZREO,                                                                     ///< Binding zreo on the right
    STAPLE_MODE_7265_LEFT_ZREO,                                                                      ///< Binding zreo on the left
    STAPLE_MODE_7265_DOUBLE,                                                                         ///< Double binding
    STAPLE_MODE_7265_MIDDLE,                                                                         ///< inter binding
    STAPLE_MODE_7265_USE_PRINTER_CONFIG,                                                             ///< use printer's config
    STAPLE_MODE_7265_TOP_LEFT_AUTO,                                                                  ///< Top left binding
    STAPLE_MODE_7265_TOP_RIGHT_AUTO,                                                                 ///< Top right binding
    STAPLE_MODE_7265_BOTTOM_LEFT_AUTO,                                                               ///< Bottom left binding
    STAPLE_MODE_7265_BOTTOM_RIGHT_AUTO,                                                              ///< Bottom right binding
    STAPLE_MODE_7265_TOP_LEFT_ZERO,                                                                  ///< Top left binding
    STAPLE_MODE_7265_TOP_RIGHT_ZERO,                                                                 ///< Top right binding
    STAPLE_MODE_7265_BOTTOM_LEFT_ZERO,                                                               ///< Bottom left binding
    STAPLE_MODE_7265_BOTTOM_RIGHT_ZERO,                                                              ///< Bottom right binding
    STAPLE_MODE_7265_DOUBLE_LEFT,                                                                    ///< Double binding
    STAPLE_MODE_7265_DOUBLE_TOP,                                                                     ///< Top double binding
    STAPLE_MODE_7265_DOUBLE_RIGHT,                                                                   ///< right Double binding
    STAPLE_MODE_7265_DOUBLE_BOTTOM,                                                                  ///< Bottom double binding
    STAPLE_MODE_7265_INVALID,                                                                        ///< please keep it in the end of the enum --- invalid data

    STAPLE_MODE_7265_MAX = 0xFFFFFFFF
}STAPLE_MODE_7265_E;

typedef enum
{
    FLIP_7265_MODE_OFF       = 0,            ///< flip mode is off
    FLIP_7265_LEFT_MODE      = 1,            ///< flip mode is left
    FLIP_7265_RIGHT_MODE     = 2,            ///< flip mode is right
    FLIP_7265_UP_MODE        = 3,            ///< flip mode is up
    FLIP_7265_MODE_INVALID   = 4,            ///< invalid data
    FLIP_7265_MAX            = 0XFFFFFFFF
}FLIP_MODE_7265_E;

typedef enum
{
    COPY_NUP_7265_CLOSE     = 0,
    COPY_NUP_7265_2IN1      = 1,
    COPY_NUP_7265_4IN1      = 2,
    COPY_NUP_7265_INVALID   = 3,
    COPY_NUP_7265_MAX       = 0XFFFFFFFF
}COPY_NUP_TYPE_7265_E;

typedef enum
{
    COPY_NUP_COMBINATION_7265_OFF         = 0,            ///< combination off
    COPY_NUP_COMBINATION_7265_HORIZONTAL  = 1,            ///< horizontal type
    COPY_NUP_COMBINATION_7265_PORTRAIT    = 2,            ///< portrait type
    COPY_NUP_COMBINATION_7265_INVALID     = 3,            ///< invalid data
    COPY_NUP_COMBINATION_7265_MAX         = 0XFFFFFFFF
}COPY_NUP_COMBINATION_TYPE_7265_E;


typedef enum
{
    FOLD_MODE_7265_OFF                               = 0x00,                                         ///< fold off
    FOLD_MODE_7265_2,                                                                                ///< Double fold (press) each assignment together
    FOLD_MODE_7265_3,                                                                                ///< three folding (Ann chang) can be folded together with n pieces of paper
    FOLD_MODE_7265_Z,                                                                                ///< Z folding
    FOLD_7265_MIDDLE_2_STAPLE,                                                                       ///< Fold and bind (according to the copy) for each assignment
    FOLD_7265_USE_PRINTER_CONFIG,                                                                    ///< follow printer's config
    FOLD_MODE_7265_INVALID,                                                                          ///< please keep it in the end of the enum --- invalid data
	FOLD_MODE_7265_MAX = 0xFFFFFFFF
}FOLD_MODE_7265_E;

typedef enum
{
    TRAY_INPUT_7265_STANDARD = 0x00,         ///< 00H, 1st Input Source
    TRAY_INPUT_7265_2 = 0x01,                ///< 01H, 2nd Input Source
    TRAY_INPUT_7265_3 = 0x02,                ///< 02H, 3rd Input Source
    TRAY_INPUT_7265_4 = 0x03,                ///< 03H, 4th Input Source
    TRAY_INPUT_7265_External_LCT_IN = 0x04,  ///< 04H, LCT(External LCT) Input Source
    TRAY_INPUT_7265_External_LCT_OUT = 0x05, ///< 05H, LCT(External LCT) Output Source
    TRAY_INPUT_7265_MULTIFUNCTION = 0x06,    ///< 06H, Multipe Manual Feed Source
    TRAY_INPUT_7265_REFEED = 0x07,           ///< 07H, Re-feed Input Source
    TRAY_INPUT_7265_AUTO = 0x64,             ///< 64H, Auto Input Source. We should find the true tray when tray_input value is "0x64".
    TRAY_INPUT_7265_INVALID,                 ///< please keep it in the end of the enum --- invalid data

    TRAY_INPUT_7265_MAX = 0xFFFFFFFF

}TRAY_INPUT_7265_E;

typedef enum
{

    PAPER_TYPE_7265_ORDINARY = 0,                // plain paper, 75~80 -> 60~90 gsm
    PAPER_TYPE_7265_ORDINARY_P,                  // plain paper+,
    PAPER_TYPE_7265_THICK1,                      // thick paper 1, 91~120 gsm
    PAPER_TYPE_7265_THICK1_P,                    // thick paper 1+,
    PAPER_TYPE_7265_THICK2,                      // thick paper 2, 121~157 gsm
    PAPER_TYPE_7265_THICK3,                      // thick paper 3, 158~209 gsm
    PAPER_TYPE_7265_THICK4,                      // thick paper 4, 210~256 gsm
    PAPER_TYPE_7265_FILM,                        // transparent film paper
    PAPER_TYPE_7265_POST_CARD,                   // postcard paper
    PAPER_TYPE_7265_LETTER_HEAD,                 // letterhead paper
    PAPER_TYPE_7265_TAB,                         // tab paper
    PAPER_TYPE_7265_LABEL,                       // label paper
    PAPER_TYPE_7265_ROLL,                        // roll paper
    PAPER_TYPE_7265_THICK,                       // thick paper 90~163g
    PAPER_TYPE_7265_ENVELOP,                     // envelope paper
    PAPER_TYPE_7265_CARD,                        // card paper
    PAPER_TYPE_7265_THIN,                        // thin paper 60~70g
    PAPER_TYPE_7265_VINYL,                       // vinyl paper
    PAPER_TYPE_7265_ORDINARY_SILENCE,            // plain paper for silent printing
    PAPER_TYPE_7265_THIN_SILENCE,                // thin paper for silent printing

    PAPER_TYPE_7265_RECYCLE,                     // recycle paper
    PAPER_TYPE_7265_COATED,                      // coated paper
    PAPER_TYPE_7265_SINGLE_SIDE_ONLY,            // single side only paper
    PAPER_TYPE_7265_COLOR,                       // color paper
    PAPER_TYPE_7265_SPECIAL,                     // special paper
    PAPER_TYPE_7265_AUTO             = 0x80,
    PAPER_TYPE_7265_INVALID          = 0xFF,     // please keep it in the end of the enum --- invalid data
    PAPER_TYPE_7265_MAX              = 0xFFFFFFFF
}PAPER_TYPE_7265_E;

typedef enum
{
    COPY_QUALITY_7265_AUTO = 0,      ///< copy quality auto
    COPY_QUALITY_7265_MIXED,         ///< copy quality is mixed
    COPY_QUALITY_7265_PICTURE,       ///< copy quality is picture
    COPY_QUALITY_7265_TXT,           ///< copy quality is text
    COPY_QUALITY_7265_MAX = 0XFFFFFFFF,           ///< copy quality max
}COPY_QUALITY_TYPE_7265_E;

typedef enum
{
    COPY_COLORBALANCE_AUTO   = 0,             ///< copy colorbalance auto
    COPY_COLORBALANCE_LEVEL_7265_1 = 1,            ///< copy colorbalance level 1
    COPY_COLORBALANCE_LEVEL_7265_2 = 2,            ///< copy colorbalance level 2
    COPY_COLORBALANCE_LEVEL_7265_3 = 3,            ///< copy colorbalance level 3
    COPY_COLORBALANCE_LEVEL_7265_4 = 4,            ///< copy colorbalance level 4
    COPY_COLORBALANCE_LEVEL_7265_5 = 5,            ///< copy colorbalance level 5
    COPY_COLORBALANCE_LEVEL_7265_6 = 6,            ///< copy colorbalance level 6
    COPY_COLORBALANCE_LEVEL_7265_7 = 7,            ///< copy colorbalance level 7
    COPY_COLORBALANCE_LEVEL_7265_8 = 8,            ///< copy colorbalance level 8
    COPY_COLORBALANCE_LEVEL_7265_9 = 9,            ///< copy colorbalance level 9
    COPY_COLORBALANCE_LEVEL_7265_10 = 10,          ///< copy colorbalance level 10
    COPY_COLORBALANCE_LEVEL_7265_11 = 11,          ///< copy colorbalance level 11
    COPY_COLORBALANCE_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy colorbalance max
}COPY_COLORBALANCE_TYPE_7265_E;

typedef enum
{
    COPY_BRIGHTNESS_7265_AUTO    = 0x80,         ///< copy brightness auto
    COPY_BRIGHTNESS_LEVEL_7265_1 = 1,            ///< copy brightness level 1
    COPY_BRIGHTNESS_LEVEL_7265_2 = 2,            ///< copy brightness level 2
    COPY_BRIGHTNESS_LEVEL_7265_3 = 3,            ///< copy brightness level 3
    COPY_BRIGHTNESS_LEVEL_7265_4 = 4,            ///< copy brightness level 4
    COPY_BRIGHTNESS_LEVEL_7265_5 = 5,            ///< copy brightness level 5
    COPY_BRIGHTNESS_LEVEL_7265_6 = 6,            ///< copy brightness level 6
    COPY_BRIGHTNESS_LEVEL_7265_7 = 7,            ///< copy brightness level 7
    COPY_BRIGHTNESS_LEVEL_7265_8 = 8,            ///< copy brightness level 8
    COPY_BRIGHTNESS_LEVEL_7265_9 = 9,            ///< copy brightness level 9
    COPY_BRIGHTNESS_LEVEL_7265_10 = 10,          ///< copy brightness level 10
    COPY_BRIGHTNESS_LEVEL_7265_11 = 11,          ///< copy brightness level 11
    COPY_BRIGHTNESS_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy brightness max
}COPY_BRIGHTNESS_TYPE_7265_E;

/**
 * @brief copy saturation\n
 *
 */
typedef enum
{
    COPY_SATURATION_7265_AUTO   = 0,             ///< copy saturation auto
    COPY_SATURATION_LEVEL_7265_1 = 1,            ///< copy saturation level 1
    COPY_SATURATION_LEVEL_7265_2 = 2,            ///< copy saturation level 2
    COPY_SATURATION_LEVEL_7265_3 = 3,            ///< copy saturation level 3
    COPY_SATURATION_LEVEL_7265_4 = 4,            ///< copy saturation level 4
    COPY_SATURATION_LEVEL_7265_5 = 5,            ///< copy saturation level 5
    COPY_SATURATION_LEVEL_7265_6 = 6,            ///< copy saturation level 6
    COPY_SATURATION_LEVEL_7265_7 = 7,            ///< copy saturation level 7
    COPY_SATURATION_LEVEL_7265_8 = 8,            ///< copy saturation level 8
    COPY_SATURATION_LEVEL_7265_9 = 9,            ///< copy saturation level 9
    COPY_SATURATION_LEVEL_7265_10 = 10,          ///< copy saturation level 10
    COPY_SATURATION_LEVEL_7265_11 = 11,          ///< copy saturation level 11
    COPY_SATURATION_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy saturation max
}COPY_SATURATION_TYPE_7265_E;

/**
 * @brief copy hue \n
 *
 */
typedef enum
{
    COPY_HUE_LEVEL_7265_1 = 1,            ///< copy hue level 1
    COPY_HUE_LEVEL_7265_2 = 2,            ///< copy hue level 2
    COPY_HUE_LEVEL_7265_3 = 3,            ///< copy hue level 3
    COPY_HUE_LEVEL_7265_4 = 4,            ///< copy hue level 4
    COPY_HUE_LEVEL_7265_5 = 5,            ///< copy hue level 5
    COPY_HUE_LEVEL_7265_6 = 6,            ///< copy hue level 6
    COPY_HUE_LEVEL_7265_7 = 7,            ///< copy hue level 7
    COPY_HUE_LEVEL_7265_8 = 8,            ///< copy hue level 8
    COPY_HUE_LEVEL_7265_9 = 9,            ///< copy hue level 9
    COPY_HUE_LEVEL_7265_10 = 10,          ///< copy hue level 10
    COPY_HUE_LEVEL_7265_11 = 11,          ///< copy hue level 11
    COPY_HUE_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy hue max
}COPY_HUE_TYPE_7265_E;

/**
 * @brief copy contrast\n
 *
 */
typedef enum
{
    COPY_CONTRAST_7265_AUTO   = 0,             ///< copy contrast auto
    COPY_CONTRAST_LEVEL_7265_1 = 1,            ///< copy contrast level 1
    COPY_CONTRAST_LEVEL_7265_2 = 2,            ///< copy contrast level 2
    COPY_CONTRAST_LEVEL_7265_3 = 3,            ///< copy contrast level 3
    COPY_CONTRAST_LEVEL_7265_4 = 4,            ///< copy contrast level 4
    COPY_CONTRAST_LEVEL_7265_5 = 5,            ///< copy contrast level 5
    COPY_CONTRAST_LEVEL_7265_6 = 6,            ///< copy contrast level 6
    COPY_CONTRAST_LEVEL_7265_7 = 7,            ///< copy contrast level 7
    COPY_CONTRAST_LEVEL_7265_8 = 8,            ///< copy contrast level 8
    COPY_CONTRAST_LEVEL_7265_9 = 9,            ///< copy contrast level 9
    COPY_CONTRAST_LEVEL_7265_10 = 10,          ///< copy contrast level 10
    COPY_CONTRAST_LEVEL_7265_11 = 11,          ///< copy contrast level 11
    COPY_CONTRAST_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy contrast max
}COPY_CONTRAST_TYPE_7265_E;

typedef enum
{
    COPY_SHARPNESS_7265_AUTO   = 0,             ///< copy sharpness auto
    COPY_SHARPNESS_LEVEL_7265_1 = 1,            ///< copy sharpness level 1
    COPY_SHARPNESS_LEVEL_7265_2 = 2,            ///< copy sharpness level 2
    COPY_SHARPNESS_LEVEL_7265_3 = 3,            ///< copy sharpness level 3
    COPY_SHARPNESS_LEVEL_7265_4 = 4,            ///< copy sharpness level 4
    COPY_SHARPNESS_LEVEL_7265_5 = 5,            ///< copy sharpness level 5
    COPY_SHARPNESS_LEVEL_7265_6 = 6,            ///< copy sharpness level 6
    COPY_SHARPNESS_LEVEL_7265_7 = 7,            ///< copy sharpness level 7
    COPY_SHARPNESS_LEVEL_7265_8 = 8,            ///< copy sharpness level 8
    COPY_SHARPNESS_LEVEL_7265_9 = 9,            ///< copy sharpness level 9
    COPY_SHARPNESS_LEVEL_7265_10 = 10,          ///< copy sharpness level 10
    COPY_SHARPNESS_LEVEL_7265_11 = 11,          ///< copy sharpness level 11
    COPY_SHARPNESS_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy sharpness max
}COPY_SHARPNESS_TYPE_7265_E;

typedef enum
{
    COPY_BACKGROUNDREMOVE_7265_AUTO   = 0,             ///< copy backgroundermove auto
    COPY_BACKGROUNDREMOVE_LEVEL_7265_1 = 1,            ///< copy backgroundermove level 1
    COPY_BACKGROUNDREMOVE_LEVEL_7265_2 = 2,            ///< copy backgroundermove level 2
    COPY_BACKGROUNDREMOVE_LEVEL_7265_3 = 3,            ///< copy backgroundermove level 3
    COPY_BACKGROUNDREMOVE_LEVEL_7265_4 = 4,            ///< copy backgroundermove level 4
    COPY_BACKGROUNDREMOVE_LEVEL_7265_5 = 5,            ///< copy backgroundermove level 5
    COPY_BACKGROUNDREMOVE_LEVEL_7265_6 = 6,            ///< copy backgroundermove level 6
    COPY_BACKGROUNDREMOVE_LEVEL_7265_7 = 7,            ///< copy backgroundermove level 7
    COPY_BACKGROUNDREMOVE_LEVEL_7265_8 = 8,            ///< copy backgroundermove level 8
    COPY_BACKGROUNDREMOVE_LEVEL_7265_9 = 9,            ///< copy backgroundermove level 9
    COPY_BACKGROUNDREMOVE_LEVEL_7265_10 = 10,          ///< copy backgroundermove level 10
    COPY_BACKGROUNDREMOVE_LEVEL_7265_11 = 11,          ///< copy backgroundermove level 11
    COPY_BACKGROUNDREMOVE_LEVEL_7265_MAX = 0XFFFFFFFF, ///< copy backgroundermove max
}COPY_BACKGROUNDREMOVE_TYPE_7265_E;

typedef enum
{
    TONER_SAVE_7265_OFF = 0,
    TONER_SAVE_7265_ON,
    TONER_SAVE_7265_MAX = 0xFFFFFFFF,
}TONER_SAVE_7265_E;

typedef enum
{
    COPY_CLONE_7265_OFF = 0,
    COPY_CLONE_7265_2x2 = 2,
    COPY_CLONE_7265_3x3,
    COPY_CLONE_7265_4x4,
    COPY_CLONE_7265_MAX = 0xFFFFFFFF,
}COPY_CLONE_TYPE_7265_E;

typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_LEFT = 0, ///< page header page footer in left
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_MID,      ///< page header page footer in mid
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_RIGHT = 0xFFFFFFFF,    ///< page header page footer in right
}COPY_PAGE_HEADER_PAGE_FOOTER_7265_POSITION_E;

typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_ALL_PAGE = 0,       ///< apply to all page
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_ONLY_FIRST_PAGE,    ///< only apply to first_page
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_WITHOUT_FIRST_PAGE = 0xFFFFFFFF, ///< apply to without first_page
}COPY_PAGE_HEADER_PAGE_FOOTER_7265_PAGINATION_E;

typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_DATE_1 = 0,   ///< date style "`23/3/23"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_DATE_2,       ///< date style "16 March,2023"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_DATE_3,       ///< date style "March 16,2023"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_DATE_4,       ///< date style "16/3/`23"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_DATE_5,       ///< date style "3/16/`23"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_TIME_1,       ///< time style "13：23"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_TIME_2,       ///< time style "1:23 PM"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_PAGINATION_1, ///< pagination style "P1，P2…"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_PAGINATION_2, ///< pagination style "1/5,2/5…"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_PAGINATION_3, ///< pagination style "1,2,3…"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_PAGINATION_4, ///< pagination style "-1-,-2-…"
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_USER_DEFINE = 0xFFFFFFFF,  ///< user define style
}COPY_PAGE_HEADER_PAGE_FOOTER_7265_TEXT_TYPE_E;

typedef struct
{
    int enable;
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_POSITION_E   position;   ///< page header page footer position
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_PAGINATION_E pagination; ///< pagination apply scene
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_TEXT_TYPE_E  text_type;  ///< text style
    char text[128]; ///< text data
}COPY_PAGE_HEADER_PAGE_FOOTER_7265_S;

typedef enum
{
    FILE_TIFF           = 0,                  ///< TIFF format
    FILE_JPEG           = 1,                  ///< JPEG format
    FILE_PDF            = 2,                  ///< PDF format
    FILE_PDF_NEXT_PAGE  = 3,
    FILE_PDF_TMP        = 4,
    FILE_BMP            = 5,                  ///< BMP format
    FILE_OFD            = 6,                  ///< OFD format
    FILE_MAX            = 0xFFFFFFFF

}FILE_TYPE_E;

/**
 * @brief scan send router
*/
typedef enum
{
    SCAN_TO_INVALID = 0,
    SCAN_TO_PC ,        ///< scan to pc
    SCAN_TO_FTP,        ///< scan to ftp
    SCAN_TO_EMAIL,      ///< scan to email
    SCAN_TO_SMB,        ///< scan to smb
    SCAN_TO_U_DISK,     ///< scan to udisk
    SCAN_TO_WSD,
    SCAN_TO_HTTP,
    SCAN_TO_APP,
    SCAN_TO_AIRSCAN,
    SCAN_TO_FAX,
    SCAN_TO_MAX

} SCAN_SEND_ROUTER_E;

typedef enum
{
    COVER_OFF                = 0,             ///< cover off
    SIGNAL_OUT_SIDE          = 1,             ///< signal on the outside
    SIGNAL_IN_SIDE           = 2,             ///< signal on the inside
    DOUBLE_SIDE              = 3,             ///< two sides cover
    COVER_SIDE_INVALID       = 4,             ///< invalid data
    COVER_SIDE_MAX          = 0xFFFFFFFF
}COVER_MODE_E;

/**
 * @brief copy overlay image color\n
 *
 */
typedef enum {
    OVERLAY_IMG_COLOR_DEFAULT = 0,         ///< all color
    OVERLAY_IMG_COLOR_BLACK,               ///< black
    OVERLAY_IMG_COLOR_RED,                 ///< read
    OVERLAY_IMG_COLOR_BLUE,                ///< blue
    OVERLAY_IMG_COLOR_GREEN,               ///< green
    OVERLAY_IMG_COLOR_YELLOW,              ///< yellow
    OVERLAY_IMG_COLOR_CYAN,                ///< cyan
    OVERLAY_IMG_COLOR_MAGENTA,             ///< magenta
    OVERLAY_IMG_COLOR_INVALID=0xFFFFFFFF   ///< invalid color
} OVERLAY_IMG_COLOR_E;

/**
 * @brief copy overlay image count\n
 *
 */
typedef enum {
    OVERLAY_IMG_COUNT_ALL_PAGE = -1,       ///< all page use overlay image
    OVERLAY_IMG_COUNT_ONLY_FIRST_PAGE = 1,  ///< only first page use overlay image
    OVERLAY_IMG_COUNT_INVALID = 0x7FFFFFFF
} OVERLAY_IMG_COUNT_E;

/**
 * @brief copy overlay image style\n
 *
 */
typedef enum {
    OVERLAY_IMG_STYLE_TRANSPARENCY = 0,    ///< overlay image transparency combine
    OVERLAY_IMG_STYLE_FOREGROUND,          ///< overlay image foreground combine
    OVERLAY_IMG_STYLE_BACKGROUND,          ///< overlay image background combine
    OVERLAY_IMG_STYLE_INVALID = 0xFFFFFFFF ///< overlay image invalid combine
} OVERLAY_IMG_STYLE_E;

/**
 * @brief copy overlay image mode\n
 *
 */
typedef enum {
    OVERLAY_IMG_MODE_OFF = 0,          ///< overlay image off
    OVERLAY_IMG_MODE_ONLY_REGISTER,    ///< only register overlay image
    OVERLAY_IMG_MODE_DEFAULT_COMBINE,  ///< default overlay using the first image as the overlay image
    OVERLAY_IMG_MODE_ONLY_COMBINE,     ///< overlay using the registered image as the overlay image
    OVERLAY_IMG_MODE_INVALID = 0xFFFFFFFF ///< overlay mode invalid
} OVERLAY_IMG_MODE_E;

/**
 * @brief copy overlay image info\n
 *
 */
typedef struct
{
    OVERLAY_IMG_MODE_E     mode;       ///< overlay mode
    char       register_id[128];       ///< register id of overlay image
    char      front_use_id[128];       ///< registered image id of front page use
    char       back_use_id[128];       ///< registered image id of back page use
    OVERLAY_IMG_STYLE_E   style;       ///< overlay style
    OVERLAY_IMG_COLOR_E   color;       ///< overlay color
    OVERLAY_IMG_COUNT_E   count;       ///< overlay count
    int                 density;       ///< overlay density
    int                offset_x;       ///< overlay offset x
    int                offset_y;       ///< overlay offset y
}OVERLAY_IMG_INFO_S;

/**
 * @brief copy insert image info\n
 *
 */
typedef struct
{
    char     insert_number[128];       ///< arrary of insert image number
    int      vaild_count;       ///< insert image vaild count
}INSERT_IMG_INFO_S;

typedef enum
{
    TRAY_NO_TWO_SIDE_PAPER      = 0,        ///< no long or short side paper
    TRAY_HAVE_ONE_SIDE_PAPER    = 1,        ///< only long or short side paper
    TRAY_HAVE_TWO_SIDE_PAPER    = 2,        ///< have long and short side paper
    TRAY_PAPER_MAX              = 0XFFFFFFFF
}OFFSET_TRAY_PAPER_E;

/**
 * @brief idcard copy type\n
 *
 */
typedef enum
{
    COPY_ID_OFF           = 0,
    COPY_ID_FULL_PORTRAIT,
    COPY_ID_HALF_PORTRAIT,
    COPY_ID_HALF_LANDSCAPE,
    COPY_ID_A5_LANDSCAPE,
    COPY_ID_MAX = 0XFFFFFFFF
}COPY_ID_TYPE_E;

/**
*@brief book copy type
*/
typedef enum
{
    BOOK_COPY_TYPE_OFF = 0,             ///< book copy off
    BOOK_COPY_TYPE_OPEN,                          ///< full image
    BOOK_COPY_TYPE_SPLIT,                         ///< split mode
    BOOK_COPY_TYPE_WITH_COVER,                    ///< scan cover first
    BOOK_COPY_TYPE_WITH_BOTH_COVER,               ///< scan back cover second
    BOOK_COPY_TYPE_MAX = 0xFFFFFFFF     ///< max enum
}BOOK_COPY_TYPE_E;

/***** 7265 param define end *****/
typedef struct PEDK_7265_copy_job_request_s
{
    COPY_PARAM_JOB_TYPE                 copy_type;                      ///< the copy type for the copy job
    int                                 copies;                         ///< the copies for the copy job
    COPY_PARAM_COLOR_MODE_E             color_mode;                     ///< the mode for color
    SCAN_MODE_7265_E                    scan_source;                    ///< the source for scan module.
    COPY_PARAM_PAPER_SIZE_7265_E        scan_size;                      ///< the paper type for scan module
    COPY_ORIGINAL_ORIENTATION_E         image_orientation;              ///< the orientation for image
    TRAY_INPUT_7265_E                   print_tray;                     ///< the tray for print module
    COPY_PARAM_PAPER_SIZE_7265_E        print_size;                     ///< the paper type for print module
    PAPER_TYPE_7265_E                   page_type;                      ///< the media type for page
    PRINT_MODE_7265_E                   print_mode;                     ///< single or duplex for print module
    uint32_t                            auto_scale_mode;                ///< the scaler is auto for copy job
    COPY_QUALITY_MODE_7265_E            quality;                ///< the quality for image
    COPY_COLORBALANCE_TYPE_7265_E       color_balance_c;        ///< the color balance for c
    COPY_COLORBALANCE_TYPE_7265_E       color_balance_m;        ///< the color balance for M
    COPY_COLORBALANCE_TYPE_7265_E       color_balance_y;        ///< the color balance for Y
    COPY_COLORBALANCE_TYPE_7265_E       color_balance_k;        ///< the color balance for K
    COPY_BRIGHTNESS_TYPE_7265_E         image_brightness;       ///< brightness
    COPY_SATURATION_TYPE_7265_E         image_saturation;       ///< saturation
    COPY_CONTRAST_TYPE_7265_E           image_contrast;         ///< contrast
    COPY_SHARPNESS_TYPE_7265_E          image_sharpness;        ///< sharpness
    COPY_HUE_TYPE_7265_E                image_hue;              ///< hue
    COPY_BACKGROUNDREMOVE_TYPE_7265_E   backgroundmove_level;   ///< backgroundmove level(1-5)
    TONER_SAVE_7265_E                   save_toner_mode;                ///< the mode for the save toner
    int                                 horizontal_margin;              ///< the horizontal margin
    int                                 vertical_margin;                ///< the vertical margin
    uint32_t                            edge_to_edge_mode;              ///<
    uint32_t                            collate;                        ///< the collated mode
    uint32_t                            separator;                      ///< the separated mode
    COPY_NUP_TYPE_7265_E                nup_type;                       ///< the nup type
    uint32_t                            nup_combination;                ///< the nup combinatin
    COPY_POSTER_SIZE_E                  poster_size;                    ///< the poster size
    uint32_t                            watermark_mode;                 ///< the watermark mode
    char                                watermark_string[128];          ///< the watermark string
    uint32_t                            booklet_duplex;                 ///< single or duplex for booklet type.
    uint32_t                            mirror_mode;                    ///< the mirror mode
    COPY_CLONE_TYPE_7265_E              clone_mode;                     ///< the clone mode
    uint32_t                            skewing_mode;                   ///< the skewing mode
    OVERLAY_IMG_INFO_S                  oly_img_info;                   ///< the overlay image info
    INSERT_IMG_INFO_S                   insert_img_info;                ///< the insert image info
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_S page_header;           ///< the type for the page header
    COPY_PAGE_HEADER_PAGE_FOOTER_7265_S page_footer;           ///< the type for the page footer
    uint32_t                            backup_mode;                    ///<
    uint32_t                            sample_mode;                    ///<
    uint32_t                            scale_percent;                  ///< from 25%~400%
    uint32_t                            auto_id_correction;             ///< id copy correction
    uint32_t                            use_color_inversion;           ///< colour inversion

    uint32_t                            use_copy_scan_meantime;         ///< support to scan original paper when copy job
    FILE_TYPE_E                         scan_file_type;
    SCAN_SEND_ROUTER_E                  scan_send_router[SCAN_TO_MAX];

    uint32_t                            use_removel_color;
    uint32_t                            remove_color_plane;             ///< (1:R 1->remove,2:G 1->remove,3:B 1->remove)

    uint32_t                            use_edge_clean;
    uint32_t                            filter_edge_margin_top;         ///<.the top margin for the filter edge mode
    uint32_t                            filter_edge_margin_left;        ///<.the left margin for the filter edge mode
    uint32_t                            filter_edge_margin_right;       ///<.the right margin for the filter edge mode
    uint32_t                            filter_edge_margin_bottom;      ///<.the bottom margin for the filter edge mode
    FLIP_MODE_7265_E                    original_flip;                  ///<.original page flip mode
    FLIP_MODE_7265_E                    copies_flip;                    ///<.copies flip mode
    COVER_MODE_E                        cover;                          ///<.copies cover mode
    COVER_MODE_E                        cover_back;                     ///<.copies back cover mode
    TRAY_INPUT_7265_E                   cover_tray_in;                  ///< the tray for cover type.
    COPY_PARAM_PAPER_SIZE_7265_E        cover_paper_size;               ///< the paper size for cover page
    PAPER_TYPE_7265_E                   cover_paper_type;               ///< the paper_type for cover page.
    TRAY_INPUT_7265_E                   back_cover_tray_in;             ///< the tray for back cover type.
    COPY_PARAM_PAPER_SIZE_7265_E        back_cover_paper_size;          ///< the paper size for back cover page
    PAPER_TYPE_7265_E                   back_cover_paper_type;          ///< the paper_type for back cover page.
    ///<.staple
    STAPLE_NUMBER_7265_E                staple_num;                     ///< the staple num
    STAPLE_ANGLE_7265_E                 staple_angle;                   ///< the staple angle
    PUNCH_NUMBER_7265_E                 punch_num;                      ///< the punch num
    STAPLE_MODE_7265_E                  staple_mode;                    ///< the staple mode
    PUNCH_MODE_7265_E                   punch_mode;                     ///< the punch mode
    FOLD_MODE_7265_E                    fold_mode;                      ///< the fold mode
    TRAY_RECEIVE_E                      tray_receive;                   ///< the tray mode
    SHIFT_MODE_E                        shift_mode;                     ///< the shift mode
    uint32_t                            fold_number;                    ///< the fold number
    uint32_t                            have_stapler;                   ///< whether have stapler ?

    uint32_t                            scan_width;                     ///< user define scan area, width mm
    uint32_t                            scan_height;                    ///< user define scan area, height mm
    uint32_t                            print_width;                    ///< user define print area, width mm
    uint32_t                            print_height;                   ///< user define print area, height mm
    TRAY_INPUT_7265_E                   separator_tray_in;              ///< the tray for separator type.
    OFFSET_TRAY_PAPER_E                 offset_tray_paper;              ///< tray has which paper for offset use
    uint32_t                            image_bit_depth;                ///< the value is 1 or 2
    COPY_ID_TYPE_E                      id_type;                        ///< the idcard composing type

    uint32_t                            chapter_on_off;                 ///< chapter switch
    char                                chapter_page[256];              ///< the chapter_page string like that : "3#25#57#88#134"
    COPY_PARAM_PAPER_SIZE_7265_E                   chapter_paper_size;             ///< the paper size for chapter page
    PAPER_TYPE_7265_E                   chapter_paper_type;             ///< the paper type for chapter page.
    TRAY_INPUT_7265_E                   chapter_tray_in;                ///< the tray in for chapter page.

    uint32_t                            ohp_insert_on_off;              ///< ohp_insert switch
    COPY_PARAM_PAPER_SIZE_7265_E                   ohp_insert_paper_size;          ///< the paper size for ohp_insert page
    PAPER_TYPE_7265_E                   ohp_insert_paper_type;          ///< the paper type for ohp_insert page.
    TRAY_INPUT_7265_E                   ohp_insert_tray_in;             ///< the tray in for ohp_insert page.

    uint32_t                            insert_page_on_off;             ///< insert_page switch
    uint32_t                            insert_page_use;                ///< insert_page use : 0=use blank page 1:use for print original
    char                                insert_page[256];               ///< the insert_page string like that : "3#25#57#88#134"
    COPY_PARAM_PAPER_SIZE_7265_E                   insert_page_paper_size;         ///< the paper size for insert_page
    PAPER_TYPE_7265_E                   insert_page_paper_type;         ///< the paper type for insert_page.
    TRAY_INPUT_7265_E                   insert_page_tray_in;            ///< the tray in for insert_page.

    BOOK_COPY_TYPE_E                    book_copy_type;                 ///< book copy type
    uint32_t                            non_image_clean;                ///< only image
    uint32_t                            center_clean;                   ///< center
    uint32_t                            image_in_center;                ///< keep in center
    uint32_t                            wonum;                          ///< pedk request every job has work number
    ///< .reserve
    uint8_t                             reserve[480];                   ///< reserve buff.

    int                                 id;
    void*                               next;
    struct PEDK_7265_copy_job_request_s* PEDK_7265_pre_copy_job_request;
}PEDK_7265_COPY_JOB_REQUEST;



typedef enum
{
    /*  7265    */
    SINGLE_COPY_PARAM_COPY_TYPE = 1,
    SINGLE_COPY_PARAM_COPIES,
    SINGLE_COPY_PARAM_COLOR_MODE,
    SINGLE_COPY_PARAM_SCAN_SOURCE,
    SINGLE_COPY_PARAM_SCAN_SIZE,
    SINGLE_COPY_PARAM_IMAGE_ORIENTATION,
    SINGLE_COPY_PARAM_PRINT_TRAY,
    SINGLE_COPY_PARAM_PRINT_SIZE,
    SINGLE_COPY_PARAM_PAGE_TYPE,
    SINGLE_COPY_PARAM_PRINT_MODE,
    SINGLE_COPY_PARAM_AUTO_SCALE_MODE,
    SINGLE_COPY_PARAM_QUALITY,
    SINGLE_COPY_PARAM_COLOR_BALANCE_C,
    SINGLE_COPY_PARAM_COLOR_BALANCE_M,
    SINGLE_COPY_PARAM_COLOR_BALANCE_Y,
    SINGLE_COPY_PARAM_COLOR_BALANCE_K,
    SINGLE_COPY_PARAM_IMAGE_BRIGHTNESS,
    SINGLE_COPY_PARAM_IMAGE_SATURATION,
    SINGLE_COPY_PARAM_IMAGE_CONTRAST,
    SINGLE_COPY_PARAM_IMAGE_SHARPNESS,
    SINGLE_COPY_PARAM_IMAGE_HUE,
    SINGLE_COPY_PARAM_BACKGROUNDMOVE_LEVEL,
    SINGLE_COPY_PARAM_SAVE_TONER_MODE,
    SINGLE_COPY_PARAM_HORIZONTAL_MARGIN,
    SINGLE_COPY_PARAM_VERTICAL_MARGIN,
    SINGLE_COPY_PARAM_EDGE_TO_EDGE_MODE,
    SINGLE_COPY_PARAM_COLLATE,
    SINGLE_COPY_PARAM_SEPARATOR,
    SINGLE_COPY_PARAM_NUP_TYPE,
    SINGLE_COPY_PARAM_NUP_COMBINATION,
    SINGLE_COPY_PARAM_POSTER_SIZE,
    SINGLE_COPY_PARAM_WATERMARK_MODE,
    SINGLE_COPY_PARAM_WATERMARK_STRING,
    SINGLE_COPY_PARAM_BOOKLET_DUPLEX,
    SINGLE_COPY_PARAM_MIRROR_MODE,
    SINGLE_COPY_PARAM_CLONE_MODE,
    SINGLE_COPY_PARAM_SKEWING_MODE,
    SINGLE_COPY_PARAM_PAGE_HEADER_ENABLE,
    SINGLE_COPY_PARAM_PAGE_HEADER_POSITION,
    SINGLE_COPY_PARAM_PAGE_HEADER_PAGINATION,
    SINGLE_COPY_PARAM_PAGE_HEADER_TEXT_TYPE,
    SINGLE_COPY_PARAM_PAGE_HEADER_TEXT,
    SINGLE_COPY_PARAM_PAGE_FOOTER_ENABLE,
    SINGLE_COPY_PARAM_PAGE_FOOTER_POSITION,
    SINGLE_COPY_PARAM_PAGE_FOOTER_PAGINATION,
    SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT_TYPE,
    SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT,
    SINGLE_COPY_PARAM_BACKUP_MODE,
    SINGLE_COPY_PARAM_SAMPLE_MODE,
    SINGLE_COPY_PARAM_SCALE_PERCENT,
    SINGLE_COPY_PARAM_AUTO_ID_CORRECTION,
    SINGLE_COPY_PARAM_USE_COLOR_INVERSION,
    SINGLE_COPY_PARAM_USE_COPY_SCAN_MEANTIME,
    SINGLE_COPY_PARAM_SCAN_FILE_TYPE,
    SINGLE_COPY_PARAM_SCAN_SEND_ROUTER,
    SINGLE_COPY_PARAM_USE_REMOVEL_COLOR,
    SINGLE_COPY_PARAM_REMOVE_COLOR_PLANE,
    SINGLE_COPY_PARAM_USE_EDGE_CLEAN,
    SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_TOP,
    SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_LEFT,
    SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_RIGHT,
    SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_BOTTOM,
    SINGLE_COPY_PARAM_ORIGINAL_FLIP,
    SINGLE_COPY_PARAM_COPIES_FLIP,
    SINGLE_COPY_PARAM_COVER,
    SINGLE_COPY_PARAM_COVER_BACK,
    SINGLE_COPY_PARAM_COVER_TRAY_IN,
    SINGLE_COPY_PARAM_STAPLE_NUM,
    SINGLE_COPY_PARAM_STAPLE_ANGLE,
    SINGLE_COPY_PARAM_PUNCH_NUM,
    SINGLE_COPY_PARAM_STAPLE_MODE,
    SINGLE_COPY_PARAM_PUNCH_MODE,
    SINGLE_COPY_PARAM_FOLD_MODE,
    SINGLE_COPY_PARAM_TRAY_RECEIVE,
    SINGLE_COPY_PARAM_SHIFT_MODE,
    SINGLE_COPY_PARAM_FOLD_NUMBER,
    SINGLE_COPY_PARAM_HAVE_STAPLER,
    SINGLE_COPY_PARAM_COVER_PAPER_TYPE,
    SINGLE_COPY_PARAM_SCAN_WIDTH,
    SINGLE_COPY_PARAM_SCAN_HEIGHT,
    SINGLE_COPY_PARAM_PRINT_WIDTH,
    SINGLE_COPY_PARAM_PRINT_HEIGHT,
    SINGLE_COPY_PARAM_SEPARATOR_TRAY_IN,
    SINGLE_COPY_PARAM_7265_MAX,
    SINGLE_COPY_PARAM_COPY_PARAM_MAX   =   0xFFFFFFFF,
}SINGLE_COPY_PARAM_E;

typedef enum
{
    COPY_PARAM_MUTEX_NUP_PRINT_MODE,
    COPY_PARAM_MUTEX_BOOKLET_PRINT_MODE,
    COPY_PARAM_MUTEX_MIX_PRINT_MODE,
    COPY_PARAM_MUTEX_IDCARD_PRINT_MODE,

    COPY_PARAM_MUTEX_BOOKLET_SCALE,
    COPY_PARAM_MUTEX_MIX_SCALE,
    COPY_PARAM_MUTEX_NUP_SCALE,
    COPY_PARAM_MUTEX_IDCARD_SCALE,
    COPY_PARAM_MUTEX_SCAN_SOURCE_SCALE,
    COPY_PARAM_MUTEX_SCALE_PERCENT_SCALE,

    COPY_PARAM_MUTEX_BOOKLET_NUP,
    COPY_PARAM_MUTEX_MIX_NUP,
    COPY_PARAM_MUTEX_EDGE_ADJUST_NUP,
    COPY_PARAM_MUTEX_PRINT_MODE_NUP,
    COPY_PARAM_MUTEX_COPY_TYPE_NUP,
    COPY_PARAM_MUTEX_SCALE_NUP,

    COPY_PARAM_MUTEX_BOOKLET_STAPLE,
    COPY_PARAM_MUTEX_MIX_STAPLE,
    COPY_PARAM_MUTEX_SEPARATOR_STAPLE,
    COPY_PARAM_MUTEX_PAPER_TYPE_STAPLE,
    COPY_PARAM_MUTEX_PRINT_SIZE_STAPLE,
    COPY_PARAM_MUTEX_COLLATE_STAPLE,
    COPY_PARAM_MUTEX_SHIFT_STAPLE,
    COPY_PARAM_MUTEX_TRAY_RECEIVE_STAPLE,

    COPY_PARAM_MUTEX_BOOKLET_SHIFT,
    COPY_PARAM_MUTEX_MIX_SHIFT,
    COPY_PARAM_MUTEX_SEPARATOR_SHIFT,
    COPY_PARAM_MUTEX_STAPLE_SHIFT,
    COPY_PARAM_MUTEX_FOLD_SHIFT,
    COPY_PARAM_MUTEX_PAPER_TYPE_SHIFT,
    COPY_PARAM_MUTEX_PRINT_SIZE_SHIFT,
    COPY_PARAM_MUTEX_COLLATE_SHIFT,
    COPY_PARAM_MUTEX_TRAY_RECEIVE_SHIFT,

    COPY_PARAM_MUTEX_BOOKLET_PUNCH,
    COPY_PARAM_MUTEX_MIX_PUNCH,
    COPY_PARAM_MUTEX_SEPARATOR_PUNCH,
    COPY_PARAM_MUTEX_PAPER_TYPE_PUNCH,
    COPY_PARAM_MUTEX_PRINT_SIZE_PUNCH,
    COPY_PARAM_MUTEX_TRAY_RECEIVE_PUNCH,

    COPY_PARAM_MUTEX_BOOKLET_FOLD,
    COPY_PARAM_MUTEX_MIX_FOLD,
    COPY_PARAM_MUTEX_SEPARATOR_FOLD,
    COPY_PARAM_MUTEX_PRINT_SIZE_FOLD,
    COPY_PARAM_MUTEX_PAPER_TYPE_FOLD,
    COPY_PARAM_MUTEX_COLLATE_FOLD,
    COPY_PARAM_MUTEX_SHIFT_FOLD,
    COPY_PARAM_MUTEX_TRAY_RECEIVE_FOLD,

    COPY_PARAM_MUTEX_NUP_BOOKLET,
    COPY_PARAM_MUTEX_MIX_BOOKLET,
    COPY_PARAM_MUTEX_STAPLE_BOOKLET,
    COPY_PARAM_MUTEX_SHIFT_BOOKLET,
    COPY_PARAM_MUTEX_PUNCH_BOOKLET,
    COPY_PARAM_MUTEX_FOLD_BOOKLET,
    COPY_PARAM_MUTEX_COLLATE_BOOKLET,
    COPY_PARAM_MUTEX_EDGE_ADJUST_BOOKLET,
    COPY_PARAM_MUTEX_SCALE_BOOKLET,

    COPY_PARAM_MUTEX_BOOKLET_COVER,

    COPY_PARAM_MUTEX_NUP_EDGE_ADJUST,
    COPY_PARAM_MUTEX_EDGE_TO_EDGE_EDGE_ADJUST,
    COPY_PARAM_MUTEX_MIX_EDGE_ADJUST,
    COPY_PARAM_MUTEX_BOOKLET_EDGE_ADJUST,
    COPY_PARAM_MUTEX_SCAN_ARRANGE_EDGE_ADJUST,

    COPY_PARAM_MUTEX_EDGE_TO_EDGE_EDGE_CLEAR,
    COPY_PARAM_MUTEX_SCAN_ARRANGE_EDGE_CLEAR,

    COPY_PARAM_MUTEX_EDGE_CLEAR_EDGE_TO_EDGE,
    COPY_PARAM_MUTEX_EDGE_ADJUST_EDGE_TO_EDGE,
    COPY_PARAM_MUTEX_MIX_EDGE_TO_EDGE,

    COPY_PARAM_MUTEX_SCALE_MIX,
    COPY_PARAM_MUTEX_NUP_MIX,
    COPY_PARAM_MUTEX_STAPLE_MIX,
    COPY_PARAM_MUTEX_SHIFT_MIX,
    COPY_PARAM_MUTEX_PUNCH_MIX,
    COPY_PARAM_MUTEX_FOLD_MIX,
    COPY_PARAM_MUTEX_BOOKLET_MIX,
    COPY_PARAM_MUTEX_EDGE_ADJUST_MIX,
    COPY_PARAM_MUTEX_EDGE_TO_EDGE_MIX,
    COPY_PARAM_MUTEX_SCAN_SOURCE_MIX,
    COPY_PARAM_MUTEX_SINGLE_TO_DUPLEX_MIX,
    COPY_PARAM_MUTEX_TRAY_MIX,

    COPY_PARAM_MUTEX_SCAN_SIZE_IDCARD,
    COPY_PARAM_MUTEX_IDCARD_SCAN_SIZE,

    COPY_PARAM_MUTEX_STAPLE_SEPARATOR,
    COPY_PARAM_MUTEX_SHIFT_SEPARATOR,
    COPY_PARAM_MUTEX_PUNCH_SEPARATOR,
    COPY_PARAM_MUTEX_FOLD_SEPARATOR,
    COPY_PARAM_MUTEX_TRAY_RECEIVE_SEPARATOR,

    COPY_PARAM_MUTEX_SCALE_SCAN_SOURCE,
    COPY_PARAM_MUTEX_IDCARD_SCAN_SOURCE,
    COPY_PARAM_MUTEX_MIX_SCAN_SOURCE,

    COPY_PARAM_MUTEX_BOOKLET_COLLATE,
    COPY_PARAM_MUTEX_STAPLE_COLLATE,
    COPY_PARAM_MUTEX_FOLD_COLLATE,
    COPY_PARAM_MUTEX_SHIFT_COLLATE,

    COPY_PARAM_MUTEX_BOOKLET_COPIES_FLIP,

    COPY_PARAM_MUTEX_STAPLE_PAPER_TYPE,
    COPY_PARAM_MUTEX_PRINT_SIZE_PAPER_TYPE,
    COPY_PARAM_MUTEX_PRINT_TRAY_PAPER_TYPE,
    COPY_PARAM_MUTEX_PUNCH_PAPER_TYPE,
    COPY_PARAM_MUTEX_FOLD_PAPER_TYPE,
    COPY_PARAM_MUTEX_SHIFT_PAPER_TYPE,

    COPY_PARAM_MUTEX_PRINT_SIZE_PRINT_TRAY,
    COPY_PARAM_MUTEX_PAPER_TYPE_PRINT_TRAY,

    COPY_PARAM_MUTEX_STAPLE_TRAY_RECEIVE,
    COPY_PARAM_MUTEX_PUNCH_TRAY_RECEIVE,
    COPY_PARAM_MUTEX_FOLD_TRAY_RECEIVE,
    COPY_PARAM_MUTEX_SHIFT_TRAY_RECEIVE,
    COPY_PARAM_MUTEX_PRINT_SIZE_TRAY_RECEIVE,
    COPY_PARAM_MUTEX_SEPARATOR_TRAY_RECEIVE,

    COPY_PARAM_MUTEX_PRINT_TRAY_PRINT_SIZE,
    COPY_PARAM_MUTEX_PAPER_TYPE_PRINT_SIZE,
    COPY_PARAM_MUTEX_STAPLE_PRINT_SIZE,
    COPY_PARAM_MUTEX_PUNCH_PRINT_SIZE,
    COPY_PARAM_MUTEX_FOLD_PRINT_SIZE,
    COPY_PARAM_MUTEX_SHIFT_PRINT_SIZE,
    COPY_PARAM_MUTEX_TRAY_RECEIVE_PRINT_SIZE,


    COPY_PARAM_MUTEX_MAX = 0xFFFFFFFF,
}COPY_PARAM_MUTEX_TYPE;

typedef struct
{
    COPY_PARAM_MUTEX_TYPE mutex_type;
    char* description;
}COPY_PARAM_MUTEX_TIPS;


typedef struct
{
    SINGLE_COPY_PARAM_E copy_param;
    int (*set_copy_param)( void*, int, int);
}SET_COPY_PARAM_INDEX;

typedef struct
{
    SINGLE_COPY_PARAM_E copy_param;
    int (*copy_param_is_muxtex)(SINGLE_COPY_PARAM_E,void*,int,int);
}COPY_PARAM_MUTEX_INDEX;

/*************************** outside interface ************************************************/
int copy_param_init(int param_id);
int PEDK_firmware_adaptor_set_copy_param(SINGLE_COPY_PARAM_E copy_param, void* value, int len,int param_id);
int get_copy_adaptor_job_request( void* job_request_data, int* len );


/*******************copy firmware adaptor*******************/







/**
 * @const {String} 参数:原稿尺寸
 */
#define COPY_PARAM_INPUTPAPERSIZE "COPY_PARAM_INPUTPAPERSIZE"
/**
 * @const {String} 参数:出纸纸盒
 */
#define COPY_PARAM_OUTPUTTRAY "COPY_PARAM_OUTPUTTRAY"
#define COPY_PARAM_OUTPUTTRAY_TRAY "COPY_PARAM_OUTPUTTRAY_TRAY"
#define COPY_PARAM_OUTPUTTRAY_SIZE "COPY_PARAM_OUTPUTTRAY_SIZE"
/**
 * @const {String} 参数:缩放率
 */
#define COPY_PARAM_ZOOM "COPY_PARAM_ZOOM"
/**
 * @const {String} 参数:画像色彩
 */
#define COPY_PARAM_IMAGE_COLOR "COPY_PARAM_IMAGE_COLOR"
#define COPY_PARAM_IMAGE_COLOR_BRIGHTNESS "COPY_PARAM_IMAGE_COLOR_BRIGHTNESS"
#define COPY_PARAM_IMAGE_COLOR_CONTRAST "COPY_PARAM_IMAGE_COLOR_CONTRAST"
#define COPY_PARAM_IMAGE_COLOR_HUE "COPY_PARAM_IMAGE_COLOR_HUE"
#define COPY_PARAM_IMAGE_COLOR_SATURATION "COPY_PARAM_IMAGE_COLOR_SATURATION"
#define COPY_PARAM_IMAGE_COLOR_SHARPNESS "COPY_PARAM_IMAGE_COLOR_SHARPNESS"
#define COPY_PARAM_IMAGE_COLOR_BACKGROUNDMOVE_LEVEL "COPY_PARAM_IMAGE_COLOR_BACKGROUNDMOVE_LEVEL"
/**
 * @const {String} 参数:双面复印模式
 */
#define COPY_PARAM_COPYMODE "COPY_PARAM_COPYMODE"
/**
 * @const {String} 参数:逐份模式
 */
#define COPY_PARAM_COLLATEMODE "COPY_PARAM_COLLATEMODE"
/**
 * @const {String} 参数:复印画质
 */
#define COPY_PARAM_QUALITYTYPE "COPY_PARAM_QUALITYTYPE"
/**
 * @const {String} 参数:水印内容
 */
#define COPY_PARAM_WATERMARK "COPY_PARAM_WATERMARK"
#define COPY_PARAM_WATERMARK_MODE "COPY_PARAM_WATERMARK_MODE"
#define COPY_PARAM_WATERMARK_STR "COPY_PARAM_WATERMARK_STR"
/**
 * @const {String} 参数:份数
 */
#define COPY_PARAM_COPIES "COPY_PARAM_COPIES"
/**
 * @const {String} 参数:自动纠偏
 */
#define COPY_PARAM_AUTOCORRECTIONMODE "COPY_PARAM_AUTOCORRECTIONMODE"
/**
 * @const {String} 参数:排列方式
 */
#define COPY_PARAM_ARRANGEMENTMODE "COPY_PARAM_ARRANGEMENTMODE"
/**
 * @const {String} 参数:多页合一
 */
#define COPY_PARAM_NUPMODE "COPY_PARAM_NUPMODE"
/**
 * @const {String} 参数:克隆
 */
#define COPY_PARAM_CLONEMODE "COPY_PARAM_CLONEMODE"
/**
 * @const {String} 参数:海报
 */
#define COPY_PARAM_POSTER "COPY_PARAM_POSTER"
/**
 * @const {String} 参数:色彩模式
 */
#define COPY_COLOR_MODE "COPY_COLOR_MODE"
/**
 * @const {String} 参数:复印扫描纸张来源
 */
#define COPY_SCAN_SOURCE "COPY_SCAN_SOURCE"
/**
 * @const {String} 参数:复印画像方向
 */
#define COPY_IMAGE_ORIENTATION "COPY_IMAGE_ORIENTATION"
/**
 * @const {String} 参数:复印色彩平衡
 */
#define COPY_COLOR_BALANCE "COPY_COLOR_BALANCE"
#define COPY_COLOR_BALANCE_C "COPY_COLOR_BALANCE_C"
#define COPY_COLOR_BALANCE_M "COPY_COLOR_BALANCE_M"
#define COPY_COLOR_BALANCE_Y "COPY_COLOR_BALANCE_Y"
#define COPY_COLOR_BALANCE_K "COPY_COLOR_BALANCE_K"
/**
 * @const {String} 参数:复印水平边距
 */
#define COPY_HORIZONTAL_MARGIN "COPY_HORIZONTAL_MARGIN"
/**
 * @const {String} 参数:复印垂直边距
 */
#define COPY_VERTICAL_MARGIN "COPY_VERTICAL_MARGIN"
/**
 * @const {String} 参数:复印边对边模式
 */
#define COPY_EDGE_TO_EDGE_MODE "COPY_EDGE_TO_EDGE_MODE"
/**
 * @const {String} 参数:复印分隔页
 */
#define COPY_SEPARATOR "COPY_SEPARATOR"
/**
 * @const {String} 参数:复印多合一复印合并模式
 */
#define COPY_NUP_COMBINATION "COPY_NUP_COMBINATION"
/**
 * @const {String} 参数:复印双面小册子模式
 */
#define COPY_BOOKLET_DUPLEX "COPY_BOOKLET_DUPLEX"
/**
 * @const {String} 参数:复印镜像模式
 */
#define COPY_MIRROR_MODE "COPY_MIRROR_MODE"
/**
 * @const {String} 参数:复印页眉使能
 */
#define COPY_PAGE_HEADER_ENABLE "COPY_PAGE_HEADER_ENABLE"
/**
 * @const {String} 参数:复印页眉使能
 */
#define COPY_PAGE_HEADER_POSITION "COPY_PAGE_HEADER_POSITION"
/**
 * @const {String} 参数:复印页眉页码模式
 */
#define COPY_PAGE_HEADER_PAGINATION "COPY_PAGE_HEADER_PAGINATION"
/**
 * @const {String} 参数:复印页眉内容类型
 */
#define COPY_PAGE_HEADER_TEXT_TYPE "COPY_PAGE_HEADER_TEXT_TYPE"
/**
 * @const {String} 参数:复印页眉自定义内容
 */
#define COPY_PAGE_HEADER_TEXT "COPY_PAGE_HEADER_TEXT"
/**
 * @const {String} 参数:复印页脚使能
 */
#define COPY_PAGE_FOOTER_ENABLE "COPY_PAGE_FOOTER_ENABLE"
/**
 * @const {String} 参数:复印页脚使能
 */
#define COPY_PAGE_FOOTER_POSITION "COPY_PAGE_FOOTER_POSITION"
/**
 * @const {String} 参数:复印页脚页码模式
 */
#define COPY_PAGE_FOOTER_PAGINATION "COPY_PAGE_FOOTER_PAGINATION"
/**
 * @const {String} 参数:复印页脚内容类型
 */
#define COPY_PAGE_FOOTER_TEXT_TYPE "COPY_PAGE_FOOTER_TEXT_TYPE"
/**
 * @const {String} 参数:复印页脚自定义内容
 */
#define COPY_PAGE_FOOTER_TEXT "COPY_PAGE_FOOTER_TEXT"
/**
 * @const {String} 参数:复印备份模式
 */
#define COPY_BACKUP_MODE "COPY_BACKUP_MODE"
/**
 * @const {String} 参数:样本复印模式
 */
#define COPY_SAMPLE_MODE "COPY_SAMPLE_MODE"
/**
 * @const {String} 参数:颜色反显模式
 */
#define COPY_USE_COLOR_INVERSION "COPY_USE_COLOR_INVERSION"
/**
 * @const {String} 参数:扫描时复印模式(同一个作业，复印的时候同时扫描到)
 */
#define COPY_USE_COPY_SCAN_MEANTIME "COPY_USE_COPY_SCAN_MEANTIME"
/**
 * @const {String} 参数:颜色滤除模式
 */
#define COPY_USE_REMOVE_COLOR "COPY_USE_REMOVE_COLOR"
/**
 * @const {String} 参数:边缘消除模式
 */
#define COPY_USE_EDGE_CLEAN "COPY_USE_EDGE_CLEAN"
/**
 * @const {String} 参数:边缘消除距离
 */
#define COPY_FILTER_EDGE_MARGIN "COPY_FILTER_EDGE_MARGIN"
#define COPY_FILTER_EDGE_MARGIN_UP      "COPY_FILTER_EDGE_MARGIN_UP"
#define COPY_FILTER_EDGE_MARGIN_DOWN    "COPY_FILTER_EDGE_MARGIN_DOWN"
#define COPY_FILTER_EDGE_MARGIN_LEFT    "COPY_FILTER_EDGE_MARGIN_LEFT"
#define COPY_FILTER_EDGE_MARGIN_RIGHT   "COPY_FILTER_EDGE_MARGIN_RIGHT"
/**
 * @const {String} 参数:复印翻页方式
 */
#define COPY_COPIES_FLIP "COPY_COPIES_FLIP"
/**
 * @const {String} 参数:原稿翻页方式
 */
#define COPY_ORIGINAL_FLIP "COPY_ORIGINAL_FLIP"
/**
 * @const {String} 参数:封面
 */
#define COPY_COVER "COPY_COVER"
/**
 * @const {String} 参数:封底
 */
#define COPY_COVER_BACK "COPY_COVER_BACK"
/**
 * @const {String} 参数:封面/底进纸盒
 */
#define COPY_COVER_TRAY_IN "COPY_COVER_TRAY_IN"
/**
 * @const {String} 参数:装订模式
 */
#define COPY_STAPLE_MODE "COPY_STAPLE_MODE"
/**
 * @const {String} 参数:打孔模式
 */
#define COPY_PUNCH_MODE "COPY_PUNCH_MODE"
/**
 * @const {String} 参数:折叠模式
 */
#define COPY_FOLD_MODE "COPY_FOLD_MODE"
/**
 * @const {String} 参数:接纸架
 */
#define COPY_TRAY_RECEIVE "COPY_TRAY_RECEIVE"
/**
 * @const {String} 参数:装订模式
 */
#define COPY_SHIFT_MODE "COPY_SHIFT_MODE"
/**
 * @const {String} 参数:折叠纸张数量
 */
#define COPY_FLOD_NUMBER "COPY_FLOD_NUMBER"
/**
 * @const {String} 参数:装订器
 */
#define COPY_HAVE_STAPLER "COPY_HAVE_STAPLER"
/**
 * @const {String} 参数:封面/封底纸张
 */
#define COPY_COVER_PAPER_TYPE "COPY_COVER_PAPER_TYPE"
/**
 * @const {String} 参数:分隔页进纸盒
 */
#define COPY_SEPARATOR_TRAY_IN "COPY_SEPARATOR_TRAY_IN"
/**
 * @const {String} 参数:用户自定义复印扫描纸张参数
 */
#define COPY_USER_DEFINE_SCAN_PAPER_PARAM "COPY_USER_DEFINE_SCAN_PAPER_PARAM"
#define COPY_USER_DEFINE_SCAN_PAPER_PARAM_W "COPY_USER_DEFINE_SCAN_PAPER_PARAM_W"
#define COPY_USER_DEFINE_SCAN_PAPER_PARAM_H "COPY_USER_DEFINE_SCAN_PAPER_PARAM_H"
/**
 * @const {String} 参数:用户自定义复印打印纸张参数
 */
#define COPY_USER_DEFINE_PRINT_PAPER_PARAM "COPY_USER_DEFINE_PRINT_PAPER_PARAM"
#define COPY_USER_DEFINE_PRINT_PAPER_PARAM_W "COPY_USER_DEFINE_PRINT_PAPER_PARAM_W"
#define COPY_USER_DEFINE_PRINT_PAPER_PARAM_H "COPY_USER_DEFINE_PRINT_PAPER_PARAM_H"
/**
 * @const {String} 参数:打印纸张介质
 */
#define COPY_PAGE_TYPE "COPY_PAGE_TYPE"
/**
 * @const {String} 参数:纠偏模式
 */
#define COPY_SKEWING_MODE "COPY_SKEWING_MODE"
/**
 * @const {String} 参数:省墨模式
 */
#define COPY_TONER_MODE "COPY_TONER_MODE"



#define COPY_NORMAL         "COPY_NORMAL"
#define COPY_ID_CARD        "COPY_ID_CARD"
#define COPY_BILL           "COPY_BILL"
#define COPY_CLONE          "COPY_CLONE"
#define COPY_POSTER         "COPY_POSTER"
#define COPY_NUP            "COPY_NUP"
#define COPY_BOOKLET        "COPY_BOOKLET"
#define COPY_WATERMARK      "COPY_WATERMARK"
#define COPY_SCALE          "COPY_SCALE"
#define COPY_MIX            "COPY_MIX"


typedef enum
{
    PAPER_SIZE_A4                   = 0,///< 210.0 x 297.0
    PAPER_SIZE_A5,                      ///< 148.5 x 210.0
    PAPER_SIZE_A5L,                     ///< 210.0 x 148.0
    PAPER_SIZE_B5,
    PAPER_SIZE_FULL_PLATEN,
    PAPER_SIZE_LETTER,                  ///< 215.9 x 279.4
    PAPER_SIZE_CARD,
    PAPER_SIZE_FOLIO,                   ///< 216.0 x 330.0
    PAPER_SIZE_ISO_B5,                  ///< 176.0 x 250.0
    PAPER_SIZE_A6,                      ///< 105.0 x 148.0
    PAPER_SIZE_USER_DEFINE,             ///< 75~218x148~356,long paper above 356~1200
    PAPER_SIZE_LEGAL13,                 ///< 215.9 x 330.0
    PAPER_SIZE_LEGAL14,                 ///< 215.9 x 355.6
    PAPER_SIZE_JIS_B5,                  ///< 182.0 x 257.0
    PAPER_SIZE_ENV_MONARCH,             ///< 098.4 x 190.5
    PAPER_SIZE_ENV_DL,                  ///< 110.0 x 220.0
    PAPER_SIZE_ENV_C5,                  ///< 162.0 x 229.0
    PAPER_SIZE_ENV_10,                  ///< 104.8 x 241.3
    PAPER_SIZE_YOUKEI_SIZE4,            ///< 105.0 x 234.0
    PAPER_SIZE_JAPANESE_POSTCARD,       ///< 100.0 x 148.0
    PAPER_SIZE_CHOUKEI_SIZE3,           ///< 120.0 x 235.0
    PAPER_SIZE_CUSTOM_16K,              ///< 185.0 x 260.0
    PAPER_SIZE_CUSTOM_BIG_16K,          ///< 195.0 x 270.0
    PAPER_SIZE_CUSTOM_32K,              ///< 130.0 x 185.0
    PAPER_SIZE_CUSTOM_BIG_32K,          ///< 135.0 x 195.0
    PAPER_SIZE_EXECUTIVE,               ///< 184.0 x 267.0
    PAPER_SIZE_OFICIO,                  ///< 216.0 x 343.0
    PAPER_SIZE_STATEMENT,               ///< 140.0 x 216.0
    PAPER_SIZE_ENV_C6,                  ///< 114.3 x 162.0
    PAPER_SIZE_ZL,                      ///< 120.0 x 230.0
    PAPER_SIZE_B6,                      ///< 125.0 x 176.0
    PAPER_SIZE_ENV_B6,                  ///< 125.0 x 176.0
    PAPER_SIZE_POSTCARD,                ///< 148.0 x 200.0
    PAPER_SIZE_YOUGATA2,                ///< 114.0 x 162.0
    PAPER_SIZE_NAGAGATA3,               ///< 120.0 x 235.0
    PAPER_SIZE_YOUNAGA3,                ///< 120.0 x 235.0
    PAPER_SIZE_YOUGATA4,                ///< 105.0 x 235.0
    PAPER_SIZE_LONG,                    ///< 210.0 x 1200.0
    PAPER_SIZE_A3,                      ///< 297.0 x 420.0
    PAPER_SIZE_A4L,                     ///< 297.0 x 210.0
    PAPER_SIZE_JIS_B6,                  ///< 128.0 x 182.0
    PAPER_SIZE_JIS_B4,                  ///< 257.0 x 364.0
    PAPER_SIZE_4X6_INCH,                ///< 101.6 x 152.4 / 4'' x 6''
    PAPER_SIZE_INVOICE,                 ///< 139.7 x 215.9 / 5.5'' x 8.5''
    PAPER_SIZE_QUARTO,                  ///< 254.0 x 203.2 / 10'' x 8''
    PAPER_SIZE_G_LETTER,                ///< 266.0 x 203.2 /10.5'' x 8''
    PAPER_SIZE_11X14_INCH,              ///< 279.4 x 355.6 / 11'' x 14''
    PAPER_SIZE_LEDGER,                  ///< 279.4 x 431.8 /11'' x 17''
    PAPER_SIZE_8K,                      ///< 270.0 x 390.0
    PAPER_SIZE_SRA3 ,                   ///< 320.0 x 450.0
    PAPER_SIZE_FOOLSCAP1,               ///< 203.0 x 330.2 / 8''x13''
    PAPER_SIZE_FOOLSCAP2,               ///< 209.6 x 330.2 / 8.25''x13''
    PAPER_SIZE_FOOLSCAP3,               ///< 215.9 x 330.2 / 8.5''x13''
    PAPER_SIZE_FOOLSCAP4,               ///< 220.0 x 330.0 / 8.65''x13''
    PAPER_SIZE_FOOLSCAP5,               ///< 206.4 x 336.6 / 8.125''x13.25''
    PAPER_SIZE_A3_WIDE1,                ///< 304.8 x 457.2 / 12''x18''
    PAPER_SIZE_A3_WIDE2 ,               ///< 311.1 x 457.2 / 12.25''x18''
    PAPER_SIZE_CUSTOM_BIG_16KL,         ///< 270.0 x 195.0
    PAPER_SIZE_JIS_B5L ,                ///< 257.0 x 182.0
    PAPER_SIZE_INVOICE_L,               ///< 215.9 x 139.7 / 8.5'' x 5.5''
    PAPER_SIZE_EXECUTIVE_L,             ///< 266.7 x 184.2 / 10.5''x 7.25''
    PAPER_SIZE_QUARTO_L	,               ///< 254.0 x 203.2 / 10'' x 8''
    PAPER_SIZE_G_LETTER_L,              ///< 266.7 x 203.2 / 10.5''x 8''
    PAPER_SIZE_LETTER_L,                ///< 279.4 x 215.9 / 11" x 8.5''
    PAPER_SIZE_ISO_B5L,                 ///< 250.0 x 176.0

    PAPER_SIZE_USER_DEFINE1,
    PAPER_SIZE_USER_DEFINE2,
    PAPER_SIZE_USER_DEFINE3,
    PAPER_SIZE_USER_DEFINE4,
    PAPER_SIZE_USER_DEFINE5,
    PAPER_SIZE_B4 ,
    PAPER_SIZE_A6CARD,

    PAPER_SIZE_GENERAL,              ///< general size
    PAPER_SIZE_MIXED  ,              ///< paper size by mixed
    PAPER_SIZE_STATEMENT_L ,

    PAPER_SIZE_B5L,
    PAPER_SIZE_BIG_16K,
    PAPER_SIZE_BIG_16KL,

    PAPER_SIZE_AUTO,                           ///< paper size is auto selected
    PAPER_SIZE_UNKOWN,
    PAPER_SIZE_FULL_TABLE,                     ///< scan full table

    PAPER_SIZE_INVALID              = 0xFF,    ///< please keep it in the end of the enum --- invalid data
    PAPER_SIZE_MAX                  = 0xFFFFFFFF
}PAPER_SIZE_7265_E;

typedef enum
{
    PEDK_MEDIASIZE_A3                        = 1,
    PEDK_MEDIASIZE_A4                        = 2,
    PEDK_MEDIASIZE_A4_LEF                    = 3,
    PEDK_MEDIASIZE_A5                        = 4,
    PEDK_MEDIASIZE_A5_LEF                    = 5,
    PEDK_MEDIASIZE_JIS_B4                    = 6,
    PEDK_MEDIASIZE_JIS_B5                    = 7,
    PEDK_MEDIASIZE_JIS_B5_LEF                = 8,
    PEDK_MEDIASIZE_JIS_B6                    = 9,
    PEDK_MEDIASIZE_JIS_B6_LEF                = 10,
    PEDK_MEDIASIZE_ISO_B4                    = 11,
    PEDK_MEDIASIZE_ISO_B4_LEF                = 12,
    PEDK_MEDIASIZE_ISO_B5                    = 13,
    PEDK_MEDIASIZE_ISO_B5_LEF                = 14,
    PEDK_MEDIASIZE_ISO_B6                    = 15,
    PEDK_MEDIASIZE_ISO_B6_LEF                = 16,
    PEDK_MEDIASIZE_8K                        = 17,
    PEDK_MEDIASIZE_Big_16K                   = 18,
    PEDK_MEDIASIZE_Big_16K_LEF               = 19,
    PEDK_MEDIASIZE_16K                       = 20,
    PEDK_MEDIASIZE_16K_LEF                   = 21,
    PEDK_MEDIASIZE_Big_32K                   = 22,
    PEDK_MEDIASIZE_Big_32K_LEF               = 23,
    PEDK_MEDIASIZE_32K                       = 24,
    PEDK_MEDIASIZE_32K_LEF                   = 25,
    PEDK_MEDIASIZE_11X17                     = 26,
    PEDK_MEDIASIZE_Letter                    = 27,
    PEDK_MEDIASIZE_Letter_LEF                = 28,
    PEDK_MEDIASIZE_Legal                     = 29,
    PEDK_MEDIASIZE_Folio                     = 30,
    PEDK_MEDIASIZE_Oficio                    = 31,
    PEDK_MEDIASIZE_Executive                 = 32,
    PEDK_MEDIASIZE_Executive_LEF             = 33,
    PEDK_MEDIASIZE_Statement                 = 34,
    PEDK_MEDIASIZE_Statement_LEF             = 35,
    PEDK_MEDIASIZE_A6                        = 36,
    PEDK_MEDIASIZE_A6_LEF                    = 37,
    PEDK_MEDIASIZE_No_10_Env                 = 38,
    PEDK_MEDIASIZE_Monarch_Env               = 39,
    PEDK_MEDIASIZE_Monarch_Env_LEF           = 40,
    PEDK_MEDIASIZE_C6_Env                    = 41,
    PEDK_MEDIASIZE_C6_Env_LEF                = 42,
    PEDK_MEDIASIZE_C5_En_7                   = 43,
    PEDK_MEDIASIZE_C5_Env_LEF_7              = 44,
    PEDK_MEDIASIZE_C4_Env                    = 45,
    PEDK_MEDIASIZE_DL_Env_5                  = 46,
    PEDK_MEDIASIZE_B6                        = 47,
    PEDK_MEDIASIZE_B6_LEF                    = 48,
    PEDK_MEDIASIZE_ZL_6                      = 49,
    PEDK_MEDIASIZE_ZL_LEF6                   = 50,
    PEDK_MEDIASIZE_Yougata4                  = 51,
    PEDK_MEDIASIZE_Younaga3                  = 52,
    PEDK_MEDIASIZE_Nagagata3                 = 53,
    PEDK_MEDIASIZE_Yougata2                  = 54,
    PEDK_MEDIASIZE_Yougata2_LEF              = 55,
    PEDK_MEDIASIZE_Postcard                  = 56,
    PEDK_MEDIASIZE_Postcard_LEF              = 57,
    PEDK_MEDIASIZE_Japanese_Postcard         = 58,
    PEDK_MEDIASIZE_Japanese_Postcard_LEF     = 59,
    PEDK_MEDIASIZE_User_Define               = 60,
    PEDK_MEDIASIZE_Ledger                    = 61,
    PEDK_MEDIASIZE_Big_8K                    = 62,
    PEDK_MEDIASIZE_Env_B6                    = 63,
    PEDK_MEDIASIZE_FoolScaps                 = 64,
    PEDK_MEDIASIZE_Invoice                   = 65,
    PEDK_MEDIASIZE_Invoice_LEF               = 66,
    PEDK_MEDIASIZE_A3_Wide                   = 67,
    PEDK_MEDIASIZE_Legal13                   = 68,
    PEDK_MEDIASIZE_Legal14                   = 69,
    PEDK_MEDIASIZE_Youkei_Size4              = 70,
    PEDK_MEDIASIZE_Choukei_Size3             = 71,
    PEDK_MEDIASIZE_Sra3                      = 72,
    PEDK_MEDIASIZE_Card                      = 73,
    PEDK_MEDIASIZE_SCAN_FULL_PLATEN          = 1001,
    PEDK_MEDIASIZE_AUTO_PAPER_CHECK          = 1002,


}PEDK_MEDIASIZE_E;


/**
 * @brief page header page footer position\n
 *
 */
typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_LEFT = 0, ///< page header page footer in left
    COPY_PAGE_HEADER_PAGE_FOOTER_MID,      ///< page header page footer in mid
    COPY_PAGE_HEADER_PAGE_FOOTER_RIGHT,    ///< page header page footer in right
}COPY_PAGE_HEADER_PAGE_FOOTER_POSITION_E;

/**
 * @brief page header page footer content type\n
 *
 */
typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_1 = 0,   ///< date style "`23/3/23"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_2,       ///< date style "16 March,2023"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_3,       ///< date style "March 16,2023"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_4,       ///< date style "16/3/`23"
    COPY_PAGE_HEADER_PAGE_FOOTER_DATE_5,       ///< date style "3/16/`23"
    COPY_PAGE_HEADER_PAGE_FOOTER_TIME_1,       ///< time style "13：23"
    COPY_PAGE_HEADER_PAGE_FOOTER_TIME_2,       ///< time style "1:23 PM"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_1, ///< pagination style "P1，P2…"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_2, ///< pagination style "1/5,2/5…"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_3, ///< pagination style "1,2,3…"
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_4, ///< pagination style "-1-,-2-…"
    COPY_PAGE_HEADER_PAGE_FOOTER_USER_DEFINE,  ///< user define style
}COPY_PAGE_HEADER_PAGE_FOOTER_TEXT_TYPE_E;

/**
 * @brief page header page footer pagination\n
 *
 */
typedef enum
{
    COPY_PAGE_HEADER_PAGE_FOOTER_ALL_PAGE = 0,       ///< apply to all page
    COPY_PAGE_HEADER_PAGE_FOOTER_ONLY_FIRST_PAGE,    ///< only apply to first_page
    COPY_PAGE_HEADER_PAGE_FOOTER_WITHOUT_FIRST_PAGE, ///< apply to without first_page
}COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_E;

/**
 * @brief page header page footer \n
 *
 */
 typedef struct
{
    int enable;
    COPY_PAGE_HEADER_PAGE_FOOTER_POSITION_E   position;   ///< page header page footer position
    COPY_PAGE_HEADER_PAGE_FOOTER_PAGINATION_E pagination; ///< pagination apply scene
    COPY_PAGE_HEADER_PAGE_FOOTER_TEXT_TYPE_E  text_type;  ///< text style
    char text[128]; ///< text data
}COPY_PAGE_HEADER_PAGE_FOOTER_S;

typedef struct
{
    int mode;
    int format_type;
    char url[256];
    char headers[256];
    char file_name_prefix[256];
    char protocol[256];
    int level;
	char custom_field[1024];
}COPY_RETENTION_PARAM_S;


//SMB
#define SMB_USER_NAME_LEN		64  //Windy modify form 33 to 256 in 20200708
#define SMB_SERVER_NAME_LEN		64
#define SMB_SERVER_PATH_LEN		129
#define SMB_LOGIN_NAME_LEN		129
#define SMB_PASSWORD_LEN		64
#define SMB_PARM_MAX			60

#define MAIL_LINKMAN_NAME_LEN   16  ///< mail linkman name len
#define MAIL_ADDR_LEN           64  ///< mail addr len
#define MAIL_ADDR_NUM_MAX       60  ///< mail adde num max
#define MAIL_GROUP_NAME_LEN     16  ///< mail group name len
#define MAIL_GROUP_MAX          10  ///< mail group max


typedef struct
{
	char     smb_user_name[SMB_USER_NAME_LEN];								//用户别名scan
	char     smb_server_name[SMB_SERVER_NAME_LEN];							//smb服务器名
	char     smb_login_name[SMB_LOGIN_NAME_LEN];							//登录名
	char     smb_server_path[SMB_SERVER_PATH_LEN];							//smb服务器路径
	char     smb_password[SMB_PASSWORD_LEN];								//密码
	uint16_t smb_port;														//端口号
	uint16_t is_anonymity;													//是否匿名
}SMB_PARM_S, *PSMB_PARM;


typedef struct
{
    char        server_name[32];    ///< FTP服务器名称
    char        server_addr[32];    ///< FTP服务器地址
    char        server_path[64];    ///< FTP服务器子目录
    uint16_t    server_port;        ///< FTP服务器端口
    uint16_t    anonymity;          ///< 是否匿名登陆
    char        login_name[64];     ///< 登陆用户名
    char        login_pswd[32];     ///< 登陆密码
    int32_t     record_id;          ///< FTP唯一标识
} FTP_PARAM_S, *FTP_PARAM_P;

typedef struct
{
    uint32_t            copy_type;                      ///< the copy type for the copy job
    uint32_t            copies;                         ///< the copies for the copy job
    uint32_t            color_mode;                     ///< the mode for color
    uint32_t            scan_source;                    ///< the source for scan module.
    uint32_t            scan_size;                      ///< the paper type for scan module
    uint32_t            image_orientation;              ///< the orientation for image
    uint32_t            print_tray;                     ///< the tray for print module
    uint32_t            print_size;                     ///< the paper type for print module
    uint32_t            page_type;                      ///< the media type for page
    uint32_t            print_mode;                     ///< single or duplex for print module
    uint32_t            auto_scale_mode;                ///< the scaler is auto for copy job
    uint32_t            quality;                        ///< the quality for image
    uint32_t            color_balance_c;                ///< the color balance for c
    uint32_t            color_balance_m;                ///< the color balance for M
    uint32_t            color_balance_y;                ///< the color balance for Y
    uint32_t            color_balance_k;                ///< the color balance for K
    uint32_t            image_brightness;               ///< brightness
    uint32_t            image_saturation;               ///< saturation
    uint32_t            image_contrast;                 ///< contrast
    uint32_t            image_sharpness;                ///< sharpness
    uint32_t            image_hue;                      ///< hue
    uint32_t            backgroundmove_level;           ///< backgroundmove level(1-5)
    uint32_t            save_toner_mode;                ///< the mode for the save toner
    int                 horizontal_margin;              ///< the horizontal margin
    int                 vertical_margin;                ///< the vertical margin
    uint32_t            edge_to_edge_mode;              ///<
    uint32_t            collate;                        ///< the collated mode
    uint32_t            separator;                      ///< the separated mode
    uint32_t            nup_type;                       ///< the nup type
    uint32_t            nup_combination;                ///< the nup combinatin
    uint32_t            poster_size;                    ///< the poster size
    uint32_t            watermark_mode;                 ///< the watermark mode
    char                watermark_string[128];          ///< the watermark string
    uint32_t            booklet_duplex;                 ///< single or duplex for booklet type.
    uint32_t            mirror_mode;                    ///< the mirror mode
    uint32_t            clone_mode;                     ///< the clone mode
    uint32_t            skewing_mode;                   ///< the skewing mode
    OVERLAY_IMG_INFO_S  oly_img_info;                   ///< the overlay image info
    INSERT_IMG_INFO_S   insert_img_info;                ///< the insert image info
    COPY_PAGE_HEADER_PAGE_FOOTER_S           page_header;           ///< the type for the page header
    COPY_PAGE_HEADER_PAGE_FOOTER_S           page_footer;           ///< the type for the page footer
    uint32_t            backup_mode;                    ///<
    uint32_t            sample_mode;                    ///<
    uint32_t            scale_percent;                  ///< from 25%~400%
    uint32_t            auto_id_correction;             ///< id copy correction
    uint32_t            use_color_inversion;           ///< colour inversion

    uint32_t            use_copy_scan_meantime;         ///< support to scan original paper when copy job
    uint32_t            scan_file_type;
    uint32_t            scan_send_router[SCAN_TO_MAX];

    uint32_t            use_removel_color;
    uint32_t            remove_color_plane;             ///< 0x7:remove all,(bit0:R 1->remove,bit1:G 1->remove,bit2:B 1->remove)

    uint32_t            use_edge_clean;
    uint32_t            filter_edge_margin_top;         ///<.the top margin for the filter edge mode
    uint32_t            filter_edge_margin_left;        ///<.the left margin for the filter edge mode
    uint32_t            filter_edge_margin_right;       ///<.the right margin for the filter edge mode
    uint32_t            filter_edge_margin_bottom;      ///<.the bottom margin for the filter edge mode
    uint32_t            original_flip;                  ///<.original page flip mode
    uint32_t            copies_flip;                    ///<.copies flip mode
    uint32_t            cover;                          ///<.copies cover mode
    uint32_t            cover_back;                     ///<.copies back cover mode
    uint32_t            cover_tray_in;                  ///< the tray for cover type.
    uint32_t            cover_paper_size;               ///< the paper size for cover page
    uint32_t            cover_paper_type;               ///< the paper_type for cover page.
    uint32_t            back_cover_tray_in;             ///< the tray for back cover type.
    uint32_t            back_cover_paper_size;          ///< the paper size for back cover page
    uint32_t            back_cover_paper_type;          ///< the paper_type for back cover page.

    uint32_t            staple_num;                     ///< the staple num
    uint32_t            staple_angle;                   ///< the staple angle
    uint32_t            punch_num;                      ///< the punch num
    uint32_t            staple_mode;                    ///< the staple mode
    uint32_t            punch_mode;                     ///< the punch mode
    uint32_t            fold_mode;                      ///< the fold mode
    uint32_t            tray_receive;                   ///< the tray mode
    uint32_t            shift_mode;                     ///< the shift mode
    uint32_t            fold_number;                    ///< the fold number
    uint32_t            have_stapler;                   ///< whether have stapler ?

    uint32_t            scan_width;                     ///< user define scan area, width mm
    uint32_t            scan_height;                    ///< user define scan area, height mm
    uint32_t            print_width;                     ///< user define print area, width mm
    uint32_t            print_height;                    ///< user define print area, height mm
    uint32_t            separator_tray_in;               ///< the tray for separator type.
    uint32_t            offset_tray_paper;              ///< tray has which paper for offset use
    uint32_t            image_bit_depth;                ///< the value is 1 or 2
    uint32_t            id_type;                        ///< the idcard composing type

    uint32_t            chapter_on_off;                 ///< chapter switch
    char                chapter_page[256];              ///< the chapter_page string like that : "3#25#57#88#134"
    uint32_t            chapter_paper_size;             ///< the paper size for chapter page
    uint32_t            chapter_paper_type;             ///< the paper type for chapter page.
    uint32_t            chapter_tray_in;                ///< the tray in for chapter page.

    uint32_t            ohp_insert_on_off;              ///< ohp_insert switch
    uint32_t            ohp_insert_paper_size;          ///< the paper size for ohp_insert page
    uint32_t            ohp_insert_paper_type;          ///< the paper type for ohp_insert page.
    uint32_t            ohp_insert_tray_in;             ///< the tray in for ohp_insert page.

    uint32_t            insert_page_on_off;             ///< insert_page switch
    uint32_t            insert_page_use;                ///< insert_page use : 0=use blank page 1:use for print original
    char                insert_page[256];               ///< the insert_page string like that : "3#25#57#88#134"
    uint32_t            insert_page_paper_size;         ///< the paper size for insert_page
    uint32_t            insert_page_paper_type;         ///< the paper type for insert_page.
    uint32_t            insert_page_tray_in;            ///< the tray in for insert_page.

    uint32_t            book_copy_type;                 ///< book copy type
    uint32_t            non_image_clean;                ///< only image
    uint32_t            center_clean;                   ///< center
    uint32_t            image_in_center;                ///< keep in center

    uint32_t            wonum;                          ///< pedk request every job has work number
    uint32_t            separator_paper_size;           ///< the paper size for separator page
    uint32_t            separator_paper_type;           ///< the paper type for separator page.
    uint32_t            separate_scan_mode;             ///< separate scan mode  0:OFF 1:ON

    uint32_t            email_count;
    char                email_address[MAIL_ADDR_NUM_MAX][MAIL_ADDR_LEN];
    FTP_PARAM_S         ftp_param;
    SMB_PARM_S          smb_param;
    uint32_t            scan_duplex;                    ///< single or duplex for scan module

    uint8_t             reserve[460];                   ///< reserve buff.
}
COPY_JOB_REQUEST_DATA_S;

#endif
typedef struct
{
    COPY_JOB_REQUEST_DATA_S copy_job_req;
    COPY_JOB_REQUEST_DATA_S pre_copy_job_req;
    uint32_t id;
    void* next;
}PEDK_COPY_JOB_REQUEST_DATA_S;

#endif /* _PEDK_JOBS_COPY_H_ */




