#include <quickjs.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include "runtime.h"
#include "cJSON.h"

#define Log(format, ...) printf("[whiltelist] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);
#define Error(format, ...) printf("[whiltelist_error] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

#define WORKSPACE "/pesf/"
#define PESF_FUNCLIST_JSON "funclist.json"


#define EXIT_FAILURE_PESF -1
#define EXIT_SUCCESS_PESF 0

cJSON* FuncList;

enum FuncEnable {
	CLOSE,//(0, "等待中")
	OPEN,//(1, "已启动")
};

static int get_file_len(char* target)
{
	int len = 0;
	FILE *file = fopen(target, "rb");
	if(file == NULL)
	{	
		Error("can't open %s", target);		
		return EXIT_FAILURE_PESF;
	}

	fseek(file, 0, SEEK_END);
	len = ftell(file);	
	fseek(file, 0, SEEK_SET);
	//printf("file len : %d", len);		
	fclose(file);

	return len;
}

static int get_file_content(char* apppath, char** app_buffer, long fileSize)
{
	
	FILE *file = fopen(apppath, "r+");	
	if(file == NULL){
		Log("open file %s fail\n", apppath); 		
		return EXIT_FAILURE_PESF;
	}

	fread(*app_buffer, 1, fileSize, file);
	fclose(file);
	
	return EXIT_SUCCESS_PESF;
}


static int get_app_funclist(cJSON** buffer, char* path)
{
	long fileSize = get_file_len(path);
	if(fileSize == EXIT_FAILURE_PESF){
		*buffer = NULL;
		return EXIT_FAILURE_PESF;
	}
	
	char* app_buffer = malloc(fileSize + 1);
	int ret = get_file_content(path, &app_buffer, fileSize);
	if(ret == EXIT_FAILURE_PESF){
		Log("get %s fail\n", path);
	}
	
	*buffer = cJSON_Parse(app_buffer);
	
	Log("buffer %s\n", cJSON_Print(*buffer));

	free(app_buffer);
	return EXIT_SUCCESS_PESF;
}


static cJSON* am_get_app_funclist(char* path)
{
	cJSON* json_data = cJSON_CreateObject();
	get_app_funclist(&json_data, path);
	if(json_data == NULL){
		cJSON_Delete(json_data);
		return NULL;
	}
	return json_data;
}


static char* GetAppfuncspace(JSContext *ctx)
{
	
	PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
	
	char* func_path = NULL;
	int func_path_len = strlen(prt->app_path.work_space) + strlen("/") + strlen(PESF_FUNCLIST_JSON) + 1;
	func_path = calloc(func_path_len, sizeof(char));//分配空间即初始化，解决cppcheck的buff being written
	if(func_path == NULL){
		Log( "calloc error\n");
		return NULL;
	}

    strncpy(func_path, prt->app_path.work_space, strlen(prt->app_path.work_space));
	strcat(func_path, "/");
	strcat(func_path, PESF_FUNCLIST_JSON);

	return func_path;
}

void am_init_whitelist_json(JSContext *ctx)
{
	char* file_path = GetAppfuncspace(ctx);
	FuncList = am_get_app_funclist(file_path);	
    if (!FuncList) {
		Log( "get func file error\n");
        return;
    }
}

void am_free_whitelist_json(void)
{
	cJSON_Delete(FuncList);
}

/*
获取app功能权限
返回值：无功能列表（-1），支持（1），不支持（0）
入参：功能key值
*/
int am_get_whitelist_enable(char* key, JSContext *ctx)
{

    if(ctx == NULL){
        Log( "ctx NULL\n");
        return EXIT_FAILURE_PESF;
    }
    char* file_path = GetAppfuncspace(ctx);
    if(file_path == NULL)
    {
        return EXIT_FAILURE_PESF;
    }

    //Log( "file_path = %s\n", file_path);
    // 获取JSON字符串
    cJSON* root = am_get_app_funclist(file_path);
    // 是否获取成功
    if (!root)
    {
        Log( "get func file error\n");
        return EXIT_FAILURE_PESF;
    }

    // 遍历JSON对象并查找键值对
    int ret = 0;
    cJSON *jsonItem = root->child;
    //Log( "key = %s\n", key);
    while (jsonItem != NULL) {
        //Log( "jsonItem->string = %s\n", jsonItem->string);
        if (0 == strcmp(jsonItem->string, key))
        {
            ret = jsonItem->valueint;
            //Log( "find [%s]:key[%d]\n",jsonItem->string, ret);
            break;
        }
        jsonItem = jsonItem->next;
    }

    // 释放JSON对象
    free(file_path);
    cJSON_Delete(root);
    return ret;

}

/*取得app工作区*/
char* GetAppWorkspace(JSContext *ctx)
{
	//TODO:从ctx上下文中获取，app的工作区，例子：object_storage.c,待大连协议移植完成之后才可实现。
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    return prt->app_path.work_space;
}
