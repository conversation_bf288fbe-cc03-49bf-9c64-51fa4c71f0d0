#ifndef _ESCL_JOB_H_
#define _ESCL_JOB_H_

typedef enum
{
    AIRSCAN_INVALID         = -1,
    AIRSCAN_TRAN_SUCCESS    = 0,    //ESCL 网络传输完成, JOB可以结束
    AIRSCAN_TRAN_ERROR,             //ESCL 网络传输异常
    AIRSCAN_CANCEL,                 //ESCL 取消处理完成
    AIRSCAN_EOTHER                  //ESCL 其他错误
}
AIRSCAN_STATUS_E;

struct escl_job;
typedef struct  escl_job    ESCL_JOB_S;

int32_t         escljob_get_jobs_info   (char* buf, size_t buf_size);

int32_t         escljob_get_page_num    (ESCL_JOB_S* pjob, int32_t* code);

int32_t         escljob_takes_page      (ESCL_JOB_S* pjob, char* file, size_t size, int32_t* job_end);

int32_t         escljob_page_list_empty (ESCL_JOB_S* pejob);

int32_t         escljob_get_file_type   (ESCL_JOB_S* pjob);

int32_t         escljob_delete          (ESCL_JOB_S* pjob, AIRSCAN_STATUS_E sts);

int32_t         escljob_start           (const char* xml);

void            escljob_finish          (ESCL_JOB_S* pejob);

int32_t         escljob_timer_restart   (ESCL_JOB_S* pejob);

int32_t         escljob_timer_stop      (ESCL_JOB_S* pejob);

void            escljob_timer_delete    (ESCL_JOB_S* pejob);

ESCL_JOB_S*     escljob_search          (int32_t jobid);

int             escljob_thread_create();

void            escljob_thread_destroy();

#endif /* _ESCL_JOB_H_ */
