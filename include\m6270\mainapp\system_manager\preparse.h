/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file job_manager.h
 * @addtogroup system_manager
 * @{
 * @brief system job management module
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18
 */


#ifndef _PREPARSE_H
#define _PREPARSE_H

#include "job_manager.h"
#include "qio/qio_general.h"

#if 0
#define FPL_PRIORITY 1
#define IPS_PRIORITY  2
#define URF_PRIORITY  3
#define GDI_PRIORITY  4
#define ACL_PRIORITY  5
#define PWG_PRIORITY  6
#define SCAN_PRIORITY 7
#define FAX_PRIORITY  8
#define PINCODE_PRIORITY 9
#define JPEG_PRIORITY  10
#endif

typedef enum
{
    SCAN_PARSER = 1 ,
    FPL_PARSER ,
    IPS_PARSER ,
    URF_PARSER ,
    GDI_PARSER ,
    PWG_PARSER ,
    FAX_PARSER ,
    PIN<PERSON>DE_PARSER ,
    JPEG_PARSER,
    DELAY_PARSER,
    ACL_PARSER
}PARSER_FLAGS_E;

typedef enum{
    PREPARSER_REGISTER_SUCCESS,
    PREPARSER_REGISTER_ERROR
}PREPARSER_REGISTER_E;

typedef struct{
    JOB_TYPE_E  job_type;
    uint32_t priority; //0 means normal job , 1 means priority job
    void *priv_args;//Preserve the private data generated in the pre-parsed callback function; 
                    //if the address is allocated by malloc, the creator must free it themselves, 
                    // as the job manager does not handle the free operation.
    void *gqio; //return a gqio pointing to the file , which can be NULL
    uint32_t memsize;//job need ptmem size
}PREPARSER_RESULT_S,*PREPARSER_RESULT_P;

/**
 * @brief  parse the qio stream and return the job information
 *
 * @param pData[in] stream data buffer
 * @param buff_size[in] buffer size
 * @param pgqio[in] gqio pointer
 * @param result[in] job information
 *
 * @return 0 on success , 1 on error
 */
typedef int32_t (*PPRE_PARSER1) (unsigned char *pData,
        uint32_t buff_size,
        void *pgqio ,
        PREPARSER_RESULT_P result);


/**
 * @brief register the callback function of the pre-parsing of the job delivered by qio stream
 *
 * @param function[in] qio stream pre-parsing callback function
 * @param min_buff_size[in] minimum read length
 * @param flags[in] flags of the callback function
 *
 * @return PREPARSER_REGISTER_SUCCESS on success , PREPARSER_REGISTER_ERROR on error
 */
PREPARSER_REGISTER_E pre_parser_register1( PPRE_PARSER1 function, uint32_t min_buff_size , PARSER_FLAGS_E flags);


/**
 * @brief  parse the structure parameters and return the job information
 *
 * @param job_config_param[in] structural parameters delivered by the panel
 * @param result[in] job information
 *
 * @return 0 on success , 1 on error
 */
typedef int32_t (*PPRE_PARSER2) (void *job_config_param, PREPARSER_RESULT_P result);

/**
 * @brief register the callback function of the pre-parsing of the job delivered by the panel
 *
 * @param function[in] corresponding structure parameter pre-parsing callback function
 * @param job_obj[in] structure parameters, used to distinguish between copy/print/scan jobs
 *
 * @return PREPARSER_REGISTER_SUCCESS on success , PREPARSER_REGISTER_ERROR on error
 */
PREPARSER_REGISTER_E pre_parser_register2( PPRE_PARSER2 function, JOB_CONFIG_OBJ_E job_obj);


/**
 * @brief  preparser initialization
 *
 * @return 0 on success , -1 on error
 */
int32_t pre_parser_prolog(void);

/**
 * @brief preparser deinitialization
 */
void pre_parser_epilog(void);

/**
 * @brief obtain the job type from the structure parameter \n
 *          delivered by panel or qio stream
 *
 * @param handle[in] gqio pointer or JOB_CONFIG_OBJ_E pointer
 * @param source_mid[in] source module
 * @param job_obj[in] see JOB_CONFIG_OBJ_E
 * @param parser_result[in]  job information
 *
 * @return 0 on success , -1 on error
 */
int32_t pre_parser_get_job_type(void *handle ,
        uint32_t source_mid ,
        uint32_t job_obj ,
        PREPARSER_RESULT_P parser_result);

/**
 * @brief obtain the job type from the structure parameter \n
 *          delivered by qio stream
 *
 * @param handle[in] gqio pointer or JOB_CONFIG_OBJ_E pointer
 * @param parser_result[in]  job information
 *
 * @return 0 on success , -1 on error
 */
int32_t pre_parser_get_qio_job_type(void *handle ,
        PREPARSER_RESULT_P parser_result);



#endif //_PREPARSE_H
/**
 * @}
 */
