/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file format.c
 * @addtogroup manager
 * @{
 * @addtogroup manager
 * @autor 
 * @date 2024-06-11
 * @brief define format
 */
#include "manager/format.h"
#include <string.h>

#define FORMAT_TAG "format"
/**
 * @brief 制作ping消息
 * 
 * @param buff 
 * @param length 
 * @return int 
 */
int make_ping_pkt(uint8_t* buff, uint16_t* length)
{
    buff[0] = 0x00;
    buff[1] = 0x00;
    buff[2] = 0x00;
    *length = 3;
}

/**
 * @brief 制作启动响应消息
 * 
 * @param buff 
 * @param length 
 * @param rtid 
 * @return int 
 */
int make_start_res(uint8_t* buff, uint16_t* length, uint8_t rtid)
{
    buff[0] = 0x02;
    buff[1] = 0x00;
    buff[2] = 0x01;
    buff[3] = rtid;
    *length = 4;
}

/**
 * @brief 制作结束响应消息
 * 
 * @param buff 
 * @param length 
 * @param rtid 
 * @return int 
 */
int make_end_res(uint8_t* buff, uint16_t* length, uint8_t rtid)
{
    buff[0] = 0x04;
    buff[1] = 0x00;
    buff[2] = 0x02;
    buff[3] = rtid;
    buff[4] = 0x00; // 目前总认为成功
    *length = 5;
}

/**
 * @brief 制作app向打印机发送消息
 * 
 * @param buff_out   ：out 打包好的消息数据
 * @param length_out ：out 打包好的消息数据长度
 * @param buff_in    : in  从bridge下来的数据
 * @param length_in  ：in  从bridge下来的数据长度
 * @param rtid       ：in  app的rtid，指示是哪个app发来的消息
 * @return int 
 */
int make_a2p_msg(uint8_t* buff_out, uint16_t* length_out, uint8_t* buff_in, uint16_t length_in, uint8_t rtid)
{
    buff_out[0] = 0x0a;
    buff_out[3] = rtid;
    memcpy(&buff_out[4], buff_in, length_in);

    length_in += 1;
    buff_out[1] = (uint8_t)((length_in & 0xFF00) >> 8);
    buff_out[2] = (uint8_t)((length_in & 0x00FF));

    *length_out = length_in + 3;
}

/**
 * @brief 制作动态属性响应包
 * 
 * @param buf 
 * @param json_str 
 * @param length 
 * @return int 
 */
int make_dynamic_buf(uint8_t* buf, char *json_str, uint16_t length)
{
    buf[0] = 0x0e;
    buf[1] = (uint8_t)((length & 0xFF00) >> 8);
    buf[2] = (uint8_t)(length & 0x00FF);
    strcpy(&buf[3], json_str);
}

/**
 * @brief 制作重启打印机包
 * 
 * @param buf 
 * @param length 
 * @return int 
 */
int make_reset_printer_buf(uint8_t* buf, uint16_t *length)
{
    buf[0] = 0x0f;
    buf[1] = 0x00;
    buf[2] = 0x00;
    *length = 3;
}
/**
 * @brief 将收到的数据流，格式化成结构体消息
 * 
 * @param buffer :in  输入数据流，从传输模块获取的数据流
 * @param des    :out manager模块可以理解的结构体
 * @param length :in  数据流长度
 * @return int 
 */
int format_conversion_to_pkt(uint8_t* buffer, FORMAT* des, uint16_t length)
{
    int ret = -1;

    // 参数检查
    if (length < 3) {
        // 如果消息长度小3，则出错
        LOG_E(FORMAT_TAG, "PKT len < 3\n");
    }

    do {
        // 格式转换
        switch (buffer[0]) {
        case 0x00:
            des->type = E_PING;
            break;
        case 0x01:
            des->type = E_START;
            break;
        case 0x02:
            des->type = E_START_RES;
            break;
        case 0x03:
            des->type = E_END;
            break;
        case 0x04:
            des->type = E_END_RES;
            break;
        case 0x05:
            des->type = E_PAUSE;
            break;
        case 0x06:
            des->type = E_PAUSE_RES;
            break;
        case 0x07:
            des->type = E_RESUME;
            break;
        case 0x08:
            des->type = E_RESUME_RES;
            break;
        case 0x09:
            des->type = E_PRINTER_TO_APP;
            break;
        case 0x0a:
            des->type = E_APP_TO_PRINTER;
            break;
        case 0x0b:    
            des->type = E_GET_STATIC_PROPERTY;
            break;
        case 0x0c:
            des->type = E_RET_STATIC_PROPERTY;
            break;
        case 0x0d:
            des->type = E_GET_DYNAMIC_PROPERTY;
            break;
        case 0x0e:
            des->type = E_RET_DYNAMIC_PROPERTY;
            break;
        default:
            des->type = E_TYPE_MAX;
            break;
        }

        // 如果没匹配到合适类型，则退出
        if (des->type == E_TYPE_MAX) {
            des->length = 0;
            des->data = NULL;
            break;
        }

        // 获取数据长度
        uint16_t temp = 0;
        temp = 0x00FF & buffer[1];
        temp <<= 8;
        temp |= buffer[2];
        des->length = temp;

        // 获取数据指针
        des->data = &buffer[3];
        ret = 0;
    } while (0);

    return ret;
}


/**
 * @}
 */
