/**************************************************************
Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:     pedk download
file name:       pedk_download.h
author:          <PERSON> (<EMAIL>)
date:            2023-12-21
description:     pedk download API
**************************************************************/
#ifndef __PEDK_DOWNLOAD_H__
#define __PEDK_DOWNLOAD_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <unistd.h>
#include <sys/stat.h>
#include "cjson/cJSON.h"
#include <stdlib.h>
#include <string.h>
#include <dirent.h>
#include "utilities/msgrouter.h"


#define PEDK_APP_JSON "app.json"
#define PEDK_FUNCLIST_JSON "funclist.json"

#define PEDK_PATH   "/pesf_data"
#define PEDK_TEST_PATH   "/pesf_data/test"

#define PEDK_DOWNLOAD_RSA_PRIVATE_KEY       "/root/rsa.priv"

#define PEDK_DOWNLOAD_ROOT_CA_CRT           "/root/root_ca.crt"
#define PEDK_DOWNLOAD_INTERMEDIATE_CA_CRT   "/root/intermediate_ca.crt"

#define PEDK_DOWNLOAD_APP_JS_ENC            "/pesf_data/tmp/app.js.enc"
#define PEDK_DOWNLOAD_APP_JSON              "/pesf_data/tmp/app.json"
#define PEDK_DOWNLOAD_KEY_ENC               "/pesf_data/tmp/key.enc"
#define PEDK_DOWNLOAD_ENV_JSON              "/pesf_data/tmp/env.json"

#define PEDK_ROOT_DIR_PATH                  "/pesf_data/"
#define PEDK_ROOT_DIR_PATH_TMP              "/pesf_data/tmp/"

#define PEDK_DOWNLOAD_ADD_STR(part)         PEDK_ROOT_DIR_PATH part

#define PEDK_DOWNLOAD_APP_JS                "/pesf_data/tmp/app.js"
#define PEDK_DOWNLOAD_KEY_LOCAL             "/pesf_data/tmp/key.local"

#define PEDK_DOWNLOAD_NAME_LIST_FILE        "/pesf_data/name_list.txt"
#define PEDK_DOWNLOAD_TEMP_NAME_LIST_FILE   "/pesf_data/temp_name_list.txt"

#define PEDK_APP_LIST_FILE                  "/pesf_data/app_list.cfg"
#define PEDK_VERIFY_SIGN_PUBKEY             "/pesf_data/tmp/pesf_data.pub.key"

// 官方安装包相关宏定义
#define PEDK_DOWNLOAD_OFFICIAL_DAT_FILENAME "offically.dat"
#define PEDK_DOWNLOAD_OFFICALLY_CRT         "/root/offically.crt"
#define PEDK_VERIFY_SIGN_PUBKEY_OFFICIAL    "/pesf_data/tmp/offically.pub.key"

#define PEDK_DOWNLOAD_MAX_NAME_LEN      64

#define PEDK_DOWNLOAD_KEY_SINGLE_SIZE   32
#define PEDK_DOWNLOAD_KEY_TOTAL_SIZE    64

#define EXIT_FAILURE_PEDK -1
#define EXIT_SUCCESS_PEDK 0

typedef enum {
    PEDK_DOWNLOAD_INSTALL,                   ///< update system status to panel
    PEDK_DOWNLOAD_UNINSTALL,                   ///< update job status to panel

    PEDK_DOWNLOAD_MAX,
} PEDK_DOWNLOAD_INSTALL_E;

typedef struct {
    char name[128];
    char bundleName[128];
    char auto_root[12];
} PEDK_DOWNLOAD_APPLIST;


int get_file_len(char* target);
int set_file_content(char* apppath, char* app_buffer, long fileSize);
int get_file_content(char* apppath, char** app_buffer, long fileSize);


/**
 * @brief 安装app，目前是将接收到的文件解压、解密到/pesf_data目录下
 * @param[in] app_name, app名称，注意需要传入绝对路径 如/tmp/demo.tar
 * @author:yangzikun
 * @return int
 * retval 0 for success
 * retval != 0 for failure.
 */
int pedk_download_app_install (char* app_name, char *app_json_data, size_t data_len, char *auto_boot);

/**
 * @brief 卸载app，删除list中同名name + 对应的同名文件夹
 * @param[in] app_name, app名称，注意需要传入文件名 如demo_js
 * @author:yangzikun
 * @return int
 * retval 0 for success
 * retval != 0 for failure.
 */
int pedk_download_app_uninstall(char* appid);

/**
 * @brief 获取applist，json格式，内容包括name+bundleName+version
 * @param[in] void
 * @author:yangzikun
 * @return cJSON
 * eg:[{“name”: “my_app1”, "bundleName":"com.company.pesfdemo", “version”:”1.0.0”}]
 */
cJSON* pedk_download_applist_get_all_entries(void);

/**
 * @brief 获取单个app信息
 * @param[in] appid
 * @param[out] app_info_data，开发人员自己用于存储app info的buff
 * @param[in] data_len 开发人员自己用于存储app info的buff大小，建议512字节以上
 * @author:yangzikun
 * @return int
 */
int pedk_download_get_app_info(char* appid, char *app_info_data, size_t data_len);


/**
 * @brief 更新app信息,通过appid，修改auto_boot + whitelist
 * @param[in] appid
 * @param[in] new_data {"appId":"XX"，"auto_boot":"true","whitelist":"xxxxxx"}
 * @author:yangzikun
 * @return int
 */
int pedk_download_appinfo_update_data(const char *new_data);

/**
 * @brief 释放调用pedk_download_applist_get_all_entries接口时申请的内存空间
 * @param[in] entriesArray:cJSON *entriesArray = pedk_download_applist_get_all_entries();
 * @author:yangzikun
 * @return void
 */
void pedk_download_applist_free_entries_array(cJSON* entriesArray);

cJSON *pedk_download_applist_read_json(void);

/**
 * @brief 查找/pesf_data/app_list中的 app列表中是否有auto_boot为true项，如果有则返回该项的appid
 * @param[in] pedk_download_applist_read_json获得的jsondata
 * @param[out] appid,有则返回字串，无则为NULL
 * @author:yangzikun
 * @return void
 */
void pedk_download_find_auto_boot(const char *json_data, char *appId);

int pedk_download_write_env2file(const char *file_path, const char *env_data);


#ifdef __cplusplus
}
#endif

#endif


