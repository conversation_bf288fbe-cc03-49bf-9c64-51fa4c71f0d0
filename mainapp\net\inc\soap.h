#ifndef __SOAP_H__
#define __SOAP_H__

#define SOAP_BAD_PARAMETER -1
#define SOAP_OVERFLOW      -2
#define SOAP_NO_MEMORY     -3
#define SOAP_OK             0

#include "qxml.h"

typedef enum
{
   dtunknown, dtfunction,
   dtui1, dtui2, dtui4,
   dti1,  dt12,  dti4,
   dtint, dtr4,  dtr8,
   dtnumber, dtfixed14, dtfloat,
   dtchar, dtstring, dtdata,
   dtdatetime, dtdatetimetz, dttime, dttimetz,
   dtbool, dtbase64, dthex, dturi, dtuuid
}
soapDataType;

#define SOAP_VAR_DIRTY     1
#define SOAP_VAR_EVENTED   2
#define SOAP_VAR_ISARG     4

typedef struct soap_attr
{
   char*                m_name;
   char*                m_val;
   struct soap_attr*    m_next;
}
SOAP_ATTR_S;

typedef struct soap_var
{
   char*                m_name;
   void*                m_val;
   soapDataType         m_type;
   unsigned int         m_flags;
   struct soap_attr*    m_attr;
   struct soap_var*     m_next;
   struct soap_var*     m_child;
   struct soap_var*     m_child_last;
}
SOAP_VAR_S;

#define SOAP_MAX_RESPONSE       16384

#define SOAP_MAX_ARG_NAME       256
#define SOAP_MAX_ARG_LEN        2048
#define SOAP_MAX_ARG_COUNT      32

#define SOAP_MIN_REPLY_ALLOC    0x10000     // 64k
#define SOAP_MAX_REPLY_ALLOC    0x800000    // 8mb

int32_t soap_format_fault(const char* extra_namespaces, const char* header, const char* code, const char* subcode_xmlns, const char* subcode, const char* reason, char* buf, size_t nbuf);

int32_t soap_format_response(SOAP_VAR_S* pvar, const char* extra_namespaces, const char* action, const char* header, const char* xmlns, const char* xmlnsurl, char* buf, size_t nbuf);

SOAP_VAR_S* soap_create_var(const char* varname, soapDataType type, uint32_t flags, const char* ival, int32_t attr_count, ...);

SOAP_VAR_S* soap_var_append_child(SOAP_VAR_S* pvar, SOAP_VAR_S* child);

void soap_delete_var(SOAP_VAR_S* pvar);

int soap_get_var_value_from_xml(QXML_S* pxml, const char *pVarName, char *varValue, int varValueSize);

int32_t soap_walk_header(QXML_S * qxml);

typedef struct soap_child_element
{
    char*          selector;
    void*          elem;
    size_t         elem_len;
    soapDataType   dtype;
    uint8_t        flg_must      : 1;
    uint8_t        flg_found     : 1;
    uint8_t        flg_val_exist : 1;
} SOAP_CHILD_ELEM_S;

#define SOAP_CHILD_ELEM_RESET(_var, _sel, _elem, _nelem, _dtype)  \
do {          \
    _var.selector = _sel;   \
    _var.elem= _elem;   \
    _var.elem_len = _nelem;   \
    _var.dtype = _dtype;   \
    _var.flg_must = _var.flg_found = _var.flg_val_exist = 0;  \
} while(0)

int soap_find_var_in_xml(QXML_S* pxml, const char *pVarName);
char* soap_iterate_child_elements(QXML_S * pxml, SOAP_CHILD_ELEM_S* child_elems, size_t child_elems_len);
int soap_find_child_elements(QXML_S * pxml, const char *elem_name, SOAP_CHILD_ELEM_S* child_elems, size_t child_elems_len);
int soap_next_element_case_equal(QXML_S * pxml, const char* elem_name);

#endif
