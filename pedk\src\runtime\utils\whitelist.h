#ifndef _WHITELIST_H_
#define _WHITELIST_H_
#include "basic/config.h"
#include "runtime/runtime.h"

/**
 * @brief 
 * 
 * @param wl 将模块权限添加到白名单中
 * @param name 
 * @param value 
 * @return int32_t 
 */
int32_t add_whitelist(WhileList** wl, char *name, int32_t value);

/**
 * @brief 获取白名单权限，找到了，则将权限的存储值返回，没找到则返回0
 * 
 * @param prt 
 * @param name 
 * @return int32_t 1：有权限
 *                 0：没权限
 */
int32_t get_whitelist_authority(PeSFRunTime* prt, char *name);
//int32_t show_whitelist_authority(WhileList* wl);

#endif // _WHITELIST_H_