/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file job_assistance.h
 * @addtogroup system_manager
 * @{
 * @brief job assistance 
 * <AUTHOR> 
 * @version 1.0
 * @date 2024-04-16
 */

#ifndef _JOB_ASSISTANCE_H
#define _JOB_ASSISTANCE_H

#include "job_common.h"

enum memory_feature
{
    PTMEM_IMAGE = 0,
    PTMEM_VIDEO ,
    OTHER_ADDR
};

void process_status(void);

void memory_status(void);

void memory_reference_prolog(void);

int memory_set_reference(int class , int feature , void *address);

void* memory_get_reference(int class , int feature);

int memory_unreference(void *address);

void memory_reference_epilog(void);

JOB_CONFIG_OBJ_E jobtype_to_jobobj(JOB_TYPE_E jobtype);

int32_t module_first_ready(ROUTER_MSG_S *msg , volatile int8_t *print_ready);

uint32_t module_ready(JOB_CONFIG_OBJ_E obj , EVT_MGR_CLI_S *client , volatile int8_t *ssd_health_check);

void power_ready(EVT_MGR_CLI_S *client , uint8_t *energy_saving_rouse);

#endif //_JOB_ASSISTANCE_H

/**
 * @}
 */
