/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_address_book.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2024-02-24
 * @brief panel dc save and process overlay copy param
 */

#ifndef _PANEL_OVERLAY_COPY_H
#define _PANEL_OVERLAY_COPY_H

#define                 OVERLAY_NAME_LEN            128                 ///< 叠图名长度
#define                 OVERLAY_NUM                 100                 ///< 叠图数量

#define                 CONFIG_OVERLAY_IMAGE        "overlay_image"     ///< 叠图文件夹


typedef enum
{
    PANEL_OVERLAY_NEW       = 0,            ///< 新建叠图 暂时无用
    PANEL_OVERLAY_DELETE,                   ///< 删除叠图
    PANEL_OVERLAY_MODIFY,                   ///< 修改叠图
    PANEL_OVERLAY_DELETE_ALL,               ///< 删除全部

    PANEL_OVERLAY_MAX       = 0xFFFFFFFF,
}PANEL_OPERATE_OVERLAY_E;

//叠图信息结构体
typedef struct 
{
    uint32_t id;                            ///< 叠图id，用于标识叠图文件
    char name[OVERLAY_NAME_LEN];            ///<叠图名称，用于面板显示
}PANLE_OVERLAY_COPY_DATA_S;

//增删改结构体
typedef struct
{
    PANEL_OPERATE_OVERLAY_E operate;       ///< 0-add,1-delete,2-modify,3-delete all
    PANLE_OVERLAY_COPY_DATA_S overlay_info;///< 要修改的结构体，新建无需id
}PANEL_OPERATE_OVERLAY_S;

 
 /**
 * @brief panel get overlay mount point
 * @param[in] data overlay file mount point in emmc
 * @param[in] data_len data size 
 */
void panel_get_overlay_mount_point( void* data, uint32_t data_len );

void panel_overlay_job_process( OVERLAY_IMG_INFO_S* overlay_img_info );

int32_t overlay_cmd_test_callback(int32_t argc, char *argv[]);

PANLE_OVERLAY_COPY_DATA_S* get_overlay_data();

void panel_modify_delete_overlay_data( void* data, uint32_t data_len );

void panel_get_overlay_copy_data();

#endif /* _PANEL_OVERLAY_COPY_H */

/**
 *@}
 */



