/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file network.c
 * @addtogroup net
 * @{
 * @addtogroup network
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network main program
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netevent.h"
#include "netmisc.h"
#include "netsock.h"
#include "network.h"
#include "nettls.h"
#include "netjob.h"
#include "ipv4.h"
#include "ipv6.h"
#include "webscan.h"

#if CONFIG_NET_WHITELIST
#include "whitelist.h"
#endif

#if CONFIG_NET_WIFI
#include "wifi.h"
#endif

#if CONFIG_SDK_PEDK
#include "net_pedkapi.h"
#endif

int32_t g_net_log_lvl = NET_LOG_LVL_D;

static void push_wired_status(NET_CTX_S* net_ctx, NETLINK_STATUS_E status)
{
    NETEVT_NOTIFY_I(EVT_TYPE_NET_ETH_CONNECTION_CHANGED, status);
}

static void parse_rtattr(struct rtattr** tb, uint32_t max, struct rtattr* attr, uint32_t len)
{
    for ( ; RTA_OK(attr, len); attr = RTA_NEXT(attr, len) )
    {
        if ( attr->rta_type <= max )
        {
            tb[attr->rta_type] = attr;
        }
    }
}

static void network_interface_connect(NET_CTX_S* net_ctx, IFACE_ID_E ifid)
{
    char ipv4_addr[IPV4_ADDR_LEN] = {0};
    char ipv4_mask[IPV4_ADDR_LEN] = {0};
    char ipv4_gtwy[IPV4_ADDR_LEN] = {0};

    /* IPv4 config */
    if ( netdata_get_ipv4_usedhcp(net_ctx->data_mgr, ifid) )
    {
        NET_INFO("%s using DHCP address on IPv4", IFACE_NAME(ifid));
        net_ipv4_start_dhcp(ifid);
    }
    else
    {
        NET_INFO("%s using static address on IPv4", IFACE_NAME(ifid));
        netdata_get_ipv4_addr(net_ctx->data_mgr, ifid, ipv4_addr, sizeof(ipv4_addr));
        netdata_get_ipv4_mask(net_ctx->data_mgr, ifid, ipv4_mask, sizeof(ipv4_mask));
        netdata_get_ipv4_gtwy(net_ctx->data_mgr, ifid, ipv4_gtwy, sizeof(ipv4_gtwy));
        if ( STRING_NO_EMPTY(ipv4_addr) && strcmp(ipv4_addr, "0.0.0.0") != 0 )
        {
            net_ipv4_set_addr(ifid, ipv4_addr, ipv4_mask, ipv4_gtwy);
        }
        else
        {
            NET_WARN("current IPv4 address is empty!!! start dhcp client.");
            netdata_set_ipv4_usedhcp(net_ctx->data_mgr, ifid, 1);
            net_ipv4_start_dhcp(ifid);
        }
    }

    /* IPv6 config */
    if ( netdata_get_ipv6_switch(net_ctx->data_mgr, ifid) )
    {
        NET_INFO("%s turn on IPv6", IFACE_NAME(ifid));
        net_ipv6_switch(ifid, IPV6_SWITCH_ON);

        if ( netdata_get_ipv6_usedhcp(net_ctx->data_mgr, ifid) )
        {
            NET_INFO("%s start DHCPv6", IFACE_NAME(ifid));
            net_ipv6_start_dhcp(ifid);
        }
    }

    if ( netdata_get_netlink_flags(net_ctx->data_mgr) == (1 << ifid) )
    {
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
        NET_DEBUG("netlink(%s) connected", IFACE_NAME(ifid));
    }
}

static void network_interface_disconnect(NET_CTX_S* net_ctx, IFACE_ID_E ifid)
{
    EVT_TYPE_E ipv4_dns0_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV4_PRIMARY_DNS_CHANGED);
    EVT_TYPE_E ipv4_dns1_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV4_SECONDARY_DNS_CHANGED);

    if ( netdata_get_ipv4_usedhcp(net_ctx->data_mgr, ifid) )
    {
        net_ipv4_stop_dhcp(ifid);
    }

    if ( netdata_get_ipv4_autodns(net_ctx->data_mgr, ifid) )
    {
        if ( netdata_set_ipv4_dns0(net_ctx->data_mgr, ifid, "") == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_dns0_event[ifid], "");
        }
        if ( netdata_set_ipv4_dns1(net_ctx->data_mgr, ifid, "") == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_dns1_event[ifid], "");
        }
    }
    /* 清空自动DNS的备份数据 */
    netdata_set_ipv4_dns0_bak(net_ctx->data_mgr, ifid, "");
    netdata_set_ipv4_dns1_bak(net_ctx->data_mgr, ifid, "");
    /* 清空网卡IP地址信息 */
    net_ipv4_set_addr(ifid, "", "", "");
    /* 清空DNS文件信息 */
    net_ipv4_set_dns(ifid, NULL);

    NET_INFO("%s turn off IPv6", IFACE_NAME(ifid));
    net_ipv6_switch(ifid, IPV6_SWITCH_OFF);
    if ( netdata_get_ipv6_usedhcp(net_ctx->data_mgr, ifid) )
    {
        NET_INFO("%s stop DHCPv6", IFACE_NAME(ifid));
        net_ipv6_stop_dhcp(ifid);
    }

    if ( netdata_get_netlink_flags(net_ctx->data_mgr) == 0 )
    {
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
        NET_DEBUG("netlink all disconnected");
    }
}

#if CONFIG_NET_WIFI
static void network_wfd_connect(NET_CTX_S* net_ctx)
{
    const char* wfd_addr = "*************";

    net_ipv4_set_addr(IFACE_ID_WFD, wfd_addr, NULL, NULL);

    net_ipv4_start_dhcp(IFACE_ID_WFD);
    if ( netdata_set_ipv4_gtwy(net_ctx->data_mgr, IFACE_ID_WFD, wfd_addr) == 0 )
    {
        NETEVT_NOTIFY_S(EVT_TYPE_NET_WFD_IPV4_GATEWAY_CHANGED, wfd_addr);
    }
    wifi_ctrl_wfd_running();

    if ( netdata_get_netlink_flags(net_ctx->data_mgr) == (1 << IFACE_ID_WFD) )
    {
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
        NET_DEBUG("netlink(%s) connected", IFACE_NAME(IFACE_ID_WFD));
    }
}

static void network_wfd_disconnect(NET_CTX_S* net_ctx)
{
    net_ipv4_stop_dhcp(IFACE_ID_WFD);

    net_ipv4_set_addr(IFACE_ID_WFD, "", "", "");

     if ( netdata_get_netlink_flags(net_ctx->data_mgr) == 0 )
    {
        netctx_push_netport_subject(net_ctx, PORT_UPDATE_SNMP);
        NET_DEBUG("netlink(wfd) disconnected");
    }
}
#endif

static void network_add_ipv4_addr(NET_CTX_S* net_ctx, IFACE_ID_E ifid, const char* addr, const char* mask)
{
    EVT_TYPE_E          ipv4_addr_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_ADDRESS_CHANGED);
    EVT_TYPE_E          ipv4_mask_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_MASK_CHANGED);
    NET_DNSV4_CONF_S    dnsv4_conf = { .changed = 0 };
    char                dns[2][IPV4_ADDR_LEN];
    uint32_t            usedhcp;

    NET_DEBUG("%s add addr(%s) mask(%s)", IFACE_NAME(ifid), addr, mask);
    usedhcp = netdata_get_ipv4_usedhcp(net_ctx->data_mgr, ifid);

    if ( usedhcp )
    {
        if ( netdata_set_ipv4_addr(net_ctx->data_mgr, ifid, addr) == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_addr_event[ifid], addr);
        }
        if ( netdata_set_ipv4_mask(net_ctx->data_mgr, ifid, mask) == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_mask_event[ifid], mask);
        }
    }

#if CONFIG_NET_WIFI
    RETURN_IF(ifid > IFACE_ID_STA, NET_TRACE);
#else
    RETURN_IF(ifid > IFACE_ID_ETH, NET_TRACE);
#endif

    netdata_get_ipv4_dns0_bak(net_ctx->data_mgr, ifid, dns[0], sizeof(dns[0]));
    netdata_get_ipv4_dns1_bak(net_ctx->data_mgr, ifid, dns[1], sizeof(dns[1]));
    if ( usedhcp && STRING_IS_EMPTY(dns[0]) && STRING_IS_EMPTY(dns[1]) ) /* DHCP IP模式下，首次更新IP时，从resolv.conf中获取自动DNS并备份 */
    {
        memset(dns, 0, sizeof(dns));
        net_ipv4_get_dns(ifid, dns);
        NET_DEBUG("%s update autodns: %s|%s", IFACE_NAME(ifid), dns[0], dns[1]);
        netdata_set_ipv4_dns0_bak(net_ctx->data_mgr, ifid, dns[0]);
        netdata_set_ipv4_dns1_bak(net_ctx->data_mgr, ifid, dns[1]);
    }

    dnsv4_conf.autodns = ( netdata_get_ipv4_autodns(net_ctx->data_mgr, ifid) ? 1 : 0 );
    netctx_update_dnsv4_config(net_ctx, ifid, &dnsv4_conf);
}

static void network_del_ipv4_addr(NET_CTX_S* net_ctx, IFACE_ID_E ifid, const char* addr, const char* mask)
{
    EVT_TYPE_E ipv4_addr_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_ADDRESS_CHANGED);
    EVT_TYPE_E ipv4_mask_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_MASK_CHANGED);
    EVT_TYPE_E ipv4_gtwy_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_GATEWAY_CHANGED);

    NET_DEBUG("%s del addr(%s) mask(%s)", IFACE_NAME(ifid), addr, mask);
    if ( netdata_get_ipv4_usedhcp(net_ctx->data_mgr, ifid) )
    {
        if ( netdata_set_ipv4_addr(net_ctx->data_mgr, ifid, "") == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_addr_event[ifid], "");
        }
        if ( netdata_set_ipv4_mask(net_ctx->data_mgr, ifid, "") == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_mask_event[ifid], "");
        }
        if ( netdata_set_ipv4_gtwy(net_ctx->data_mgr, ifid, "") == 0 )
        {
            NETEVT_NOTIFY_S(ipv4_gtwy_event[ifid], "");
        }
    }
}

static void network_add_ipv6_addr(NET_CTX_S* net_ctx, IFACE_ID_E ifid, const char* addr, uint32_t prefixlen, uint32_t scope)
{
    EVT_TYPE_E  ipv6_link_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_LINK_ADDRESS_CHANGED);
    EVT_TYPE_E  ipv6_stls_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_STLS_ADDRESS_CHANGED);
    EVT_TYPE_E  ipv6_site_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_DHCP_ADDRESS_CHANGED);
    EVT_TYPE_E  ipv6_dns0_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_PRIMARY_DNS_CHANGED);
    EVT_TYPE_E  ipv6_dns1_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_SECONDARY_DNS_CHANGED);
    char        ipv6_dns[2][IPV6_ADDR_LEN];

    NET_DEBUG("%s add addr(%s) prefixlen(%u) scope(%u)", IFACE_NAME(ifid), addr, prefixlen, scope);
    if ( scope == RT_SCOPE_LINK )
    {
        if ( netdata_set_ipv6_link(net_ctx->data_mgr, ifid, addr) == 0 )
        {
            NETEVT_NOTIFY_S(ipv6_link_event[ifid], addr);
        }
    }
    else if ( scope == RT_SCOPE_SITE || scope == RT_SCOPE_UNIVERSE )
    {
        if ( net_ipv6_is_stls_addr(ifid, addr) )
        {
            if ( netdata_set_ipv6_stls(net_ctx->data_mgr, ifid, addr) == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_stls_event[ifid], addr);
            }
        }
        else
        {
            if ( netdata_set_ipv6_site(net_ctx->data_mgr, ifid, addr) == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_site_event[ifid], addr);
            }

            memset(ipv6_dns, 0, sizeof(ipv6_dns));
            net_ipv6_get_dns(ifid, ipv6_dns);
            if ( netdata_set_ipv6_dns0(net_ctx->data_mgr, ifid, ipv6_dns[0]) == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_dns0_event[ifid], ipv6_dns[0]);
            }
            if ( netdata_set_ipv6_dns1(net_ctx->data_mgr, ifid, ipv6_dns[1]) == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_dns1_event[ifid], ipv6_dns[1]);
            }
        }
    }
    else
    {
        NET_WARN("unknown scope(%u) IPv6 address", scope);
    }
}

static void network_del_ipv6_addr(NET_CTX_S* net_ctx, IFACE_ID_E ifid, const char* addr, uint32_t prefixlen, uint32_t scope)
{
    EVT_TYPE_E ipv6_link_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_LINK_ADDRESS_CHANGED);
    EVT_TYPE_E ipv6_stls_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_STLS_ADDRESS_CHANGED);
    EVT_TYPE_E ipv6_site_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_DHCP_ADDRESS_CHANGED);
    EVT_TYPE_E ipv6_dns0_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_PRIMARY_DNS_CHANGED);
    EVT_TYPE_E ipv6_dns1_event[IFACE_ID_NUM] = NET_EVT_ARRAY2(IPV6_SECONDARY_DNS_CHANGED);

    NET_DEBUG("%s del addr(%s) prefixlen(%u) scope(%u)", IFACE_NAME(ifid), addr, prefixlen, scope);
    if ( scope == RT_SCOPE_LINK )
    {
        if ( netdata_set_ipv6_link(net_ctx->data_mgr, ifid, "") == 0 )
        {
            NETEVT_NOTIFY_S(ipv6_link_event[ifid], "");
        }
    }
    else if ( scope == RT_SCOPE_SITE || scope == RT_SCOPE_UNIVERSE )
    {
        if ( net_ipv6_is_stls_addr(ifid, addr) )
        {
            if ( netdata_set_ipv6_stls(net_ctx->data_mgr, ifid, "") == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_stls_event[ifid], "");
            }
        }
        else
        {
            if ( netdata_set_ipv6_site(net_ctx->data_mgr, ifid, "") == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_site_event[ifid], "");
            }

            if ( netdata_set_ipv6_dns0(net_ctx->data_mgr, ifid, "") == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_dns0_event[ifid], "");
            }
            if ( netdata_set_ipv6_dns1(net_ctx->data_mgr, ifid, "") == 0 )
            {
                NETEVT_NOTIFY_S(ipv6_dns1_event[ifid], "");
            }
            net_ipv6_clear_dns(ifid);
        }
    }
    else
    {
        NET_WARN("unknown scope(%u) IPv6 address", scope);
    }
}

static void network_update_ifinfo(NET_CTX_S* net_ctx, struct nlmsghdr* nlh)
{
    IFACE_ID_E          ifid = IFACE_ID_ANY;
    struct ifinfomsg*   ifinfo = NLMSG_DATA(nlh);
    struct rtattr*      tb[IFLA_MAX + 1];
    char                ifname[IF_NAMESIZE];
    uint32_t            running;
    uint32_t            active;
    uint32_t            len;

    memset(tb, 0, sizeof(tb));

    len = nlh->nlmsg_len - NLMSG_SPACE(sizeof(*ifinfo));
    parse_rtattr(tb, IFLA_MAX, IFLA_RTA(ifinfo), len);

    if ( tb[IFLA_IFNAME] != NULL )
    {
        snprintf(ifname, sizeof(ifname), "%s", (char *)RTA_DATA(tb[IFLA_IFNAME]));
    }
    else
    {
        if_indextoname(ifinfo->ifi_index, ifname);
    }
    NET_DEBUG("%s ifi_index(%d) ifi_change(0x%x) ifi_flags(0x%x)", ifname, ifinfo->ifi_index, ifinfo->ifi_change, ifinfo->ifi_flags);
    ifid = IFACE_ENUM(ifname);
    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);

    running = (!!(ifinfo->ifi_flags & IFF_RUNNING));
    active  = (!!(ifinfo->ifi_flags & IFF_UP));
    NET_DEBUG("%s active(%u) running(%u)", ifname, active, running);

    switch ( ifid )
    {
    case IFACE_ID_ETH:
        if ( netdata_set_iface_running(net_ctx->data_mgr, IFACE_ID_ETH, running) == 0 )
        {
            if ( running == 0 )
            {
                push_wired_status(net_ctx, NETLINK_STATUS_DISCONNECTED);
                network_interface_disconnect(net_ctx, ifid);
            }
            else
            {
                push_wired_status(net_ctx, NETLINK_STATUS_CONNECTED);
                network_interface_connect(net_ctx, ifid);
            }
        }
        break;
#if CONFIG_NET_WIFI
    case IFACE_ID_STA:
        if ( netdata_set_iface_running(net_ctx->data_mgr, IFACE_ID_STA, running) == 0 )
        {
            running ? network_interface_connect(net_ctx, ifid) : network_interface_disconnect(net_ctx, ifid);
        }
        break;
    case IFACE_ID_WFD:
        if ( netdata_set_iface_running(net_ctx->data_mgr, IFACE_ID_WFD, running) == 0 )
        {
            running ? network_wfd_connect(net_ctx) : network_wfd_disconnect(net_ctx);
        }
        break;
#endif
    default:
        break;
    }
}

static void network_update_ifaddr(NET_CTX_S* net_ctx, struct nlmsghdr* nlh)
{
    IFACE_ID_E          ifid = IFACE_ID_ANY;
    struct ifaddrmsg*   ifaddr = NLMSG_DATA(nlh);
    struct rtattr*      tb[IFA_MAX + 1];
    char                ifname[IF_NAMESIZE];
    char                addr[IPV6_ADDR_LEN];
    char                mask[IPV4_ADDR_LEN];
    uint32_t            mask_addr = 0xFFFFFFFF;
    uint32_t            len = 0;

    memset(ifname, 0, sizeof(ifname));
    memset(addr,   0, sizeof(addr));
    memset(mask,   0, sizeof(mask));
    memset(tb,     0, sizeof(tb));

    len = nlh->nlmsg_len - NLMSG_SPACE(sizeof(*ifaddr));
    parse_rtattr(tb, IFA_MAX, IFA_RTA(ifaddr), len);

    RETURN_IF(tb[IFA_ADDRESS] == NULL, NET_TRACE);

    if ( tb[IFLA_IFNAME] != NULL )
    {
        snprintf(ifname, sizeof(ifname), "%s", (char *)RTA_DATA(tb[IFLA_IFNAME]));
    }
    else
    {
        if_indextoname(ifaddr->ifa_index, ifname);
    }
    ifid = IFACE_ENUM(ifname);
    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);

    inet_ntop(ifaddr->ifa_family, RTA_DATA(tb[IFA_ADDRESS]), addr, sizeof(addr));
    if ( ifaddr->ifa_family == AF_INET )
    {
        /* Calculate the mask address string */
        NET_DEBUG("IPv4 prefixlen(%u)", ifaddr->ifa_prefixlen);
        mask_addr <<= (32 - ifaddr->ifa_prefixlen);
        snprintf(mask, sizeof(mask), "%u.%u.%u.%u", (mask_addr >> 24) & 0xFF, (mask_addr >> 16) & 0xFF, (mask_addr >> 8) & 0xFF, mask_addr & 0xFF);

        if ( nlh->nlmsg_type == RTM_NEWADDR )
        {
            network_add_ipv4_addr(net_ctx, ifid, addr, mask);
        }
        else if ( nlh->nlmsg_type == RTM_DELADDR )
        {
            network_del_ipv4_addr(net_ctx, ifid, addr, mask);
        }

        /* IPv4地址变更，重载相关协议线程 */
        netctx_push_netlink_subject(net_ctx, LINK_CHANGE_IPV4_BASE << ifid);
    }
    else if ( ifaddr->ifa_family == AF_INET6 )
    {
        if ( nlh->nlmsg_type == RTM_NEWADDR )
        {
            network_add_ipv6_addr(net_ctx, ifid, addr, (uint32_t)ifaddr->ifa_prefixlen, (uint32_t)ifaddr->ifa_scope);
        }
        else if ( nlh->nlmsg_type == RTM_DELADDR )
        {
            network_del_ipv6_addr(net_ctx, ifid, addr, (uint32_t)ifaddr->ifa_prefixlen, (uint32_t)ifaddr->ifa_scope);
        }

        /* IPv6地址变更，重载相关协议线程 */
        if ( ifaddr->ifa_scope == RT_SCOPE_LINK )
        {
            netctx_push_netlink_subject(net_ctx, LINK_CHANGE_IPV6_BASE << ifid);
        }
    }
}

static void network_update_ifroute(NET_CTX_S* net_ctx, struct nlmsghdr* nlh)
{
    EVT_TYPE_E      ipv4_gtwy_event[IFACE_ID_NUM] = NET_EVT_ARRAY3(IPV4_GATEWAY_CHANGED);
    IFACE_ID_E      ifid = IFACE_ID_ANY;
    struct rtmsg*   rt = NLMSG_DATA(nlh);
    struct rtattr*  tb[RTA_MAX + 1];
    char            gateway[IPV4_ADDR_LEN];
    uint32_t        len;

    memset(gateway, 0, sizeof(gateway));
    memset(tb, 0, sizeof(tb));

    len = nlh->nlmsg_len - NLMSG_SPACE(sizeof(*rt));
    parse_rtattr(tb, RTA_MAX, RTM_RTA(rt), len);

    RETURN_IF(rt->rtm_family != AF_INET || tb[RTA_GATEWAY] == NULL, NET_TRACE);

    ifid = net_ifctl_get_id_by_gtwy(*(uint32_t *)RTA_DATA(tb[RTA_GATEWAY]));
    RETURN_IF(ifid < IFACE_ID_ETH || ifid >= IFACE_ID_NUM, NET_WARN);

    inet_ntop(AF_INET, RTA_DATA(tb[RTA_GATEWAY]), gateway, sizeof(gateway));
    if ( netdata_set_ipv4_gtwy(net_ctx->data_mgr, ifid, gateway) == 0 )
    {
        NETEVT_NOTIFY_S(ipv4_gtwy_event[ifid], gateway);
        NET_DEBUG("update %s default gateway(%s)", IFACE_NAME(ifid), gateway);
    }
}

static void* network_netlink_handler(void* arg)
{
    NET_CTX_S*          net_ctx = (NET_CTX_S *)arg;
    PI_SOCKET_T         sockfd;
    fd_set              fds;
    struct sockaddr_nl  sa;
    struct nlmsghdr*    nlh;
    char                buff[2048];
    int32_t             ret;

    pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS, NULL);
    pthread_setcancelstate(PTHREAD_CANCEL_ENABLE, NULL);

    sockfd = netsock_create_custom(AF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, NULL);

    memset(&sa, 0, sizeof(sa));
    sa.nl_family = AF_NETLINK;
    sa.nl_groups = RTMGRP_LINK | RTMGRP_NOTIFY | RTMGRP_IPV4_IFADDR | RTMGRP_IPV4_ROUTE | RTMGRP_IPV6_IFADDR | RTMGRP_IPV6_ROUTE;
    ret = bind(sockfd, (struct sockaddr *)&sa, sizeof(sa));
    if ( ret < 0 )
    {
        NET_WARN("netlink bind(%d) failed: %d<%s>", ret, errno, strerror(errno));
        pi_closesock(sockfd);
        return NULL;
    }

    NET_INFO("netlink handler start");
    while ( 1 )
    {
        FD_ZERO(&fds);
        FD_SET(sockfd, &fds);
        ret = select(sockfd + 1, &fds, NULL, NULL, NULL);
        if ( ret <= 0 )
        {
            NET_INFO("netlink select(%d): %d<%s>", ret, errno, strerror(errno));
            continue;
        }

        ret = (int32_t)recvfrom(sockfd, buff, sizeof(buff), 0, NULL, NULL);
        for ( nlh = (struct nlmsghdr *)buff; NLMSG_OK(nlh, ret); nlh = NLMSG_NEXT(nlh, ret) )
        {
            switch (nlh->nlmsg_type)
            {
            case NLMSG_DONE:
            case NLMSG_ERROR:
                break;
            case RTM_NEWLINK: /* 16 */
            case RTM_DELLINK: /* 17 */
            case RTM_GETLINK: /* 18 */
            case RTM_SETLINK: /* 19 */
                network_update_ifinfo(net_ctx, nlh);
                break;
            case RTM_NEWADDR: /* 20 */
            case RTM_DELADDR: /* 21 */
                network_update_ifaddr(net_ctx, nlh);
                break;
            case RTM_NEWROUTE: /* 24 */
                network_update_ifroute(net_ctx, nlh);
                break;
            case RTM_DELROUTE: /* 25 */
            case RTM_GETROUTE: /* 26 */
                break;
            default:
                break;
            }
        }
    }
    NET_WARN("netlink handler end");
    pi_closesock(sockfd);

    return NULL;
}

static int32_t network_generate_uuid(NET_CTX_S* net_ctx)
{
    char    uuid[64];
    FILE*   stream;

    netdata_get_uuid(net_ctx->data_mgr, uuid, sizeof(uuid));
    if ( uuid[0] == '\0' )
    {
        NET_DEBUG("this machine is first started, ready to generate UUID...");
        stream = pi_fopen("/proc/sys/kernel/random/uuid", "r");
        RETURN_VAL_IF(stream == NULL, NET_WARN, -1);

        fgets(uuid, sizeof(uuid), stream);
        if ( uuid[0] != '\0' && uuid[strlen(uuid) - 1] == '\n' )
        {
            uuid[strlen(uuid) - 1] = '\0';
        }
        pi_fclose(stream);

        RETURN_VAL_IF(STRING_IS_EMPTY(uuid), NET_WARN, -1);
        netdata_set_uuid(net_ctx->data_mgr, uuid);
        NET_DEBUG("generate UUID(%s)", uuid);
    }

    return 0;
}

static int32_t network_check_self(NET_CTX_S* net_ctx, uint32_t* has_wired, uint32_t* has_wifi)
{
    uint32_t    status[4] = { STATUS_E_NET_WIFI_INIT_ERROR, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF };

    netevent_client_waiting_platform_ready(); /* 等待platform模块上报当前机型的网络硬件配置 */

    *has_wired = netdata_get_support_wired(net_ctx->data_mgr);
    *has_wifi  = netdata_get_support_wifi(net_ctx->data_mgr);

    NET_DEBUG("network support wired(%u) wifi(%u)", *has_wired, *has_wifi);
    RETURN_VAL_IF((*has_wired == 0) && (*has_wifi == 0), NET_WARN, -1); /* 有线网络和WiFi均不支持 */
    RETURN_VAL_IF(network_generate_uuid(net_ctx) < 0,    NET_WARN, -1); /* 检查/生成UUID */

    if ( HAS_NO_FILE(NET_BASE_DIR) )
    {
        mkdir(NET_BASE_DIR, 0775);
    }

    if ( *has_wired )
    {
        RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_ETH, IFACE_SWITCH_ON)  < 0, NET_WARN, -1);
    }

#if CONFIG_NET_WIFI
    if ( *has_wifi )
    {
        if ( net_ifctl_is_valid(IFACE_STA) )
        {
            net_ifctl_switch_safe(IFACE_STA, IFACE_SWITCH_ON);
            net_ifctl_switch_safe(IFACE_WFD, IFACE_SWITCH_ON);
        }
        else
        {
            NET_WARN("WiFi driver load failed !!!");
            *has_wifi = 0;
            NETEVT_NOTIFY_T(EVT_TYPE_SYSTEMSTATUS_FROM_NET, status);
            netdata_set_support_wifi(net_ctx->data_mgr, 0);
        }
    }
#else
    netdata_set_support_wifi(net_ctx->data_mgr, 0);
#endif

    netctx_notify_event_first(net_ctx);

    push_wired_status(net_ctx, NETLINK_STATUS_DISCONNECTED);
    net_ifctl_switch_safe(IFACE_ETH, IFACE_SWITCH_OFF);
    net_ipv6_switch(IFACE_ID_ETH, IPV6_SWITCH_OFF);
#if CONFIG_NET_WIFI
    net_ifctl_switch_safe(IFACE_STA, IFACE_SWITCH_OFF);
    net_ifctl_switch_safe(IFACE_WFD, IFACE_SWITCH_OFF);
    net_ipv6_switch(IFACE_ID_STA, IPV6_SWITCH_OFF);
    net_ipv6_switch(IFACE_ID_WFD, IPV6_SWITCH_OFF);
#endif
    return 0;
}

static int32_t network_init_wired(NET_CTX_S* net_ctx)
{
    char     hostname[HOSTNAME_LEN];
    char     mfg_name[16];
    uint8_t  mac[6];

    netdata_get_hostname(net_ctx->data_mgr, hostname, sizeof(hostname));
    if ( STRING_IS_EMPTY(hostname) )
    {
        RETURN_VAL_IF(net_ifctl_get_mac(IFACE_ETH, mac, sizeof(mac)) != 0, NET_WARN, -1);
        netdata_get_mfg_name(net_ctx->data_mgr, mfg_name, sizeof(mfg_name));
        snprintf(hostname, sizeof(hostname), "%s-%02X%02X%02X", mfg_name, mac[3], mac[4], mac[5]);
        NET_DEBUG("generate hostname(%s) base on %s MAC address", hostname, IFACE_ETH);
        netctx_update_hostname(net_ctx, hostname);
    }

    if ( netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_ETH) )
    {
        RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_ETH, IFACE_SWITCH_ON) < 0, NET_WARN, -1);
    }
    else
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_ETH_SWITCH_CHANGED, 0);
    }

    return 0;
}

static pthread_t    s_netlink_thread = 0;
static uint32_t     s_network_inited = 0;

int32_t network_prolog(void)
{
    NET_CTX_S*  net_ctx = NULL;
    uint32_t    has_wired = 0;
    uint32_t    has_wifi = 0;
    int32_t     ret = -1;

    RETURN_VAL_IF(s_network_inited == 1, NET_INFO, 0);

    do
    {
        s_network_inited = 1;

        net_ctx = network_context_construct();
        BREAK_IF(net_ctx == NULL, NET_WARN);

        ret = network_check_self(net_ctx, &has_wired, &has_wifi);
        BREAK_IF(ret != 0, NET_WARN);

        ret = pthread_create(&s_netlink_thread, NULL, network_netlink_handler, (void *)net_ctx);
        BREAK_IF(ret != 0, NET_WARN);

        if ( has_wired )
        {
            network_init_wired(net_ctx);
        }

#if CONFIG_NET_WIFI
        if ( has_wifi )
        {
            wifi_prolog(net_ctx);
        }
#endif

        nettls_init(net_ctx);
        netjob_init();
#if CONFIG_NET_WHITELIST
        whitelist_init(net_ctx);
#endif
        prnsdk_prolog(net_ctx);
        pedksrv_prolog(net_ctx);
        esclsrv_prolog(net_ctx);
        ippsrv_prolog(net_ctx);
        websrv_prolog(net_ctx);
#if CONFIG_NET_WEBSCAN
        webscan_prolog(net_ctx);
#endif
        tlssrv_prolog(net_ctx);

        port9120_prolog(net_ctx);
        rawprint_prolog(net_ctx);
        rawscan_prolog(net_ctx);
        lpd_prolog(net_ctx);
        wsd_prolog(net_ctx);
        slp_prolog(net_ctx);
        bonjour_prolog(net_ctx);
        netbios_prolog(net_ctx);
        llmnr_prolog(net_ctx);
        snmp_prolog(net_ctx);
        smtp_prolog(net_ctx);
        ftp_prolog(net_ctx);

#if CONFIG_SDK_PEDK
        net_pedkapi_init(net_ctx);  /* PEDK API处理函数注册 */
#endif

        netacl_init(net_ctx);       /* ACL 命令字注册 */
        netoid_init(net_ctx);       /* ACL-OID 命令字注册 */
        netcmd_init(net_ctx);       /* CMD 命令字注册 */

        g_net_log_lvl = NET_LOG_LVL_S;
        ret = 0;
    }
    while ( 0 );

    if ( ret != 0 )
    {
        NET_WARN("network init failed(%d): %d<%s>!", ret, errno, strerror(errno));
        network_epilog();
    }
    return ret;
}

void network_epilog(void)
{
    if ( s_network_inited )
    {
#if CONFIG_SDK_PEDK
        net_pedkapi_deinit();
#endif
        tlssrv_epilog();
        websrv_epilog();
#if CONFIG_NET_WEBSCAN
        webscan_epilog();
#endif
        ippsrv_epilog();
        esclsrv_epilog();
        pedksrv_epilog();
        prnsdk_epilog();

        port9120_epilog();
        rawprint_epilog();
        rawscan_epilog();
        lpd_epilog();
        wsd_epilog();
        slp_epilog();
        bonjour_epilog();
        netbios_epilog();
        llmnr_epilog();
        snmp_epilog();
        smtp_epilog();
        ftp_epilog();

        nettls_deinit();

        if ( s_netlink_thread != 0 )
        {
            if ( pthread_cancel(s_netlink_thread) != 0 )
            {
                NET_WARN("pthread_cancel netlink(%ld) failed: %d<%s>", s_netlink_thread, errno, strerror(errno));
            }
            if ( pthread_join(s_netlink_thread, NULL) != 0 )
            {
                NET_WARN("pthread_join netlink(%ld) failed: %d<%s>", s_netlink_thread, errno, strerror(errno));
            }
            s_netlink_thread = 0;
        }
        network_context_destruct();
        s_network_inited = 0;
    }
}
/**
 *@}
 */
