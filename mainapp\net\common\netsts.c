/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netsts.c
 * @addtogroup net
 * @{
 * @addtogroup netsts
 * <AUTHOR>
 * @date 2023-8-19
 * @brief system status manager API in NET module
 */
#include "nettypes.h"
#include "netsts.h"

#define RETURN_CASE(s)  case s: NET_DEBUG("Detect the status(%s)", #s); return s

#define NETSTS_UNLOCK() pthread_mutex_unlock(&s_netsts_mutex)
#define NETSTS_LOCK()   pthread_mutex_lock(&s_netsts_mutex)

static pthread_mutex_t  s_netsts_mutex  = PTHREAD_MUTEX_INITIALIZER;
static NETSTS_PACKET_S  s_netsts_packet = { .count = 0 };

void netsts_save_packet(NETSTS_UNIT_S* sts, size_t num)
{
    RETURN_IF(sts == NULL || num == 0, NET_WARN)

    NETSTS_LOCK();
    NET_DEBUG("uptate syssts num(%u):", num);
    for ( size_t i = 0; i < num; ++i )
    {
        NET_DEBUG("syssts[0x%08x, %d, %d, %d]", sts[i].status_id, sts[i].parameter1, sts[i].parameter2, sts[i].parameter3);
    }
    if ( num > NETSTS_MAX_NUM )
    {
        NET_WARN("stssys num(%u) more than %u", num, NETSTS_MAX_NUM);
        num = NETSTS_MAX_NUM;
    }
    memcpy(s_netsts_packet.array, sts, sizeof(NETSTS_UNIT_S) * num);
    s_netsts_packet.count = num;
    NETSTS_UNLOCK();
}

int32_t netsts_take_packet(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL, NET_WARN, -1);

    NETSTS_LOCK();
    memcpy(packet, &s_netsts_packet, sizeof(NETSTS_PACKET_S));
    NETSTS_UNLOCK();

    return 0;
}

#define TYPE_MODULE_MASK (STATUS_ID_TYPE_MASK | STATUS_ID_MODULE_MASK)
uint32_t netsts_check_module_error(NETSTS_PACKET_S* packet, uint32_t module)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        RETURN_VAL_IF((packet->array[i].status_id & TYPE_MODULE_MASK) == (STATUS_ID_TYPE_FATAL | module), NET_DEBUG, packet->array[i].status_id);
        RETURN_VAL_IF((packet->array[i].status_id & TYPE_MODULE_MASK) == (STATUS_ID_TYPE_ERROR | module), NET_DEBUG, packet->array[i].status_id);
    }

    return 0;
}

uint32_t netsts_check_status_level(NETSTS_PACKET_S* packet, uint32_t level)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        if ( (packet->array[i].status_id & STATUS_ID_TYPE_MASK) == level )
        {
            return packet->array[i].status_id;
        }
    }

    return 0;
}

uint32_t netsts_check_scan_running(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_I_SCAN_PROCESSING);
            RETURN_CASE(STATUS_I_SCAN_RUNNING);
            RETURN_CASE(STATUS_I_SCAN_CANCELING);
            RETURN_CASE(STATUS_I_SCAN_TOFILE_SENDING);
            RETURN_CASE(STATUS_I_SCAN_NEXT_PAGE_WAITING);
            RETURN_CASE(STATUS_I_SCAN_TO_FILE_UDISK_SAVING);
            RETURN_CASE(STATUS_I_SCAN_LOCKED);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_trayin_missing(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_W_PRINT_TRAY_1_OPEN);
            RETURN_CASE(STATUS_W_PRINT_TRAY_2_OPEN);
            RETURN_CASE(STATUS_W_PRINT_TRAY_3_OPEN);
            RETURN_CASE(STATUS_W_PRINT_TRAY_4_OPEN);
            RETURN_CASE(STATUS_W_PRINT_LCT_IN_OPEN);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_trayin_near_empty(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_LCT_EX_PAPER_NEAR_EMPTY);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_trayout_full(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_W_PRINT_LCT_IN_PAPER_FULL);
            RETURN_CASE(STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL);
            RETURN_CASE(STATUS_E_PRINT_TRAY_RECEIVE_1_FULL);
            RETURN_CASE(STATUS_E_PRINT_TRAY_RECEIVE_2_FULL);
            RETURN_CASE(STATUS_E_PRINT_TRAY_RECEIVE_3_FULL);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_cover_open(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_E_PRINT_FRONT_DOOR_OPEN);
            RETURN_CASE(STATUS_E_PRINT_SIDE_DOOR_OPEN);
            RETURN_CASE(STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN);
            RETURN_CASE(STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN);
            RETURN_CASE(STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN);
            RETURN_CASE(STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN);
            RETURN_CASE(STATUS_E_PRINT_FNS_TOP_COVER_OPEN);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_paper_empty(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_TRAY_1_PAPER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_TRAY_2_PAPER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_TRAY_3_PAPER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_TRAY_4_PAPER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_LCT_IN_PAPER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_LCT_EX_PAPER_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_1_PAPER_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_2_PAPER_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_3_PAPER_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_TRAY_4_PAPER_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_LCT_IN_PAPER_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_LCT_EX_PAPER_EMPTY);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_paper_jam(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch ( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_1_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_2_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_3_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_4_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_LCT_IN_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_LCT_EX_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_MANUAL_FEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_REFEEDER_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1);
            RETURN_CASE(STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1);
            RETURN_CASE(STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1);
            RETURN_CASE(STATUS_E_PRINT_JAM_OUTPUT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_VER_TRANSPORT_SECTION2);
            RETURN_CASE(STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2);
            RETURN_CASE(STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_2ND_DISCHARGE_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_3RD_TRAY_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_HOR_TRANSPORT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRANSPORT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION);
            RETURN_CASE(STATUS_E_PRINT_JAM_SADDLE_STITCHER_EN_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_SADDLE_STITCHER_EX_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_FS_SADDLE_EN_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_ZU_LARGE_SIZE_MODE);
            RETURN_CASE(STATUS_E_PRINT_JAM_ZU_ALL_MODE);
            RETURN_CASE(STATUS_E_PRINT_JAM_PUNCH);
            RETURN_CASE(STATUS_E_PRINT_JAM_FNS_STAPLE);
            RETURN_CASE(STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE);
            RETURN_CASE(STATUS_E_PRINT_JAM_SADDLE_EN_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_MAIN_PADDLE_HOME_SNR);
            RETURN_CASE(STATUS_E_PRINT_JAM_EJECT_GRIP);
            RETURN_CASE(STATUS_E_PRINT_JAM_ETAMPER);
            RETURN_CASE(STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON);
            RETURN_CASE(STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_PUNCH_REAR_DETECTION_OFF);
            RETURN_CASE(STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_OFF);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_toner_empty(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_E_PRINT_Y_TB_UNINSTALL);
            RETURN_CASE(STATUS_E_PRINT_M_TB_UNINSTALL);
            RETURN_CASE(STATUS_E_PRINT_C_TB_UNINSTALL);
            RETURN_CASE(STATUS_E_PRINT_K_TB_UNINSTALL);

            RETURN_CASE(STATUS_E_PRINT_Y_TB_MISMATCH);
            RETURN_CASE(STATUS_E_PRINT_M_TB_MISMATCH);
            RETURN_CASE(STATUS_E_PRINT_C_TB_MISMATCH);
            RETURN_CASE(STATUS_E_PRINT_K_TB_MISMATCH);

            RETURN_CASE(STATUS_E_PRINT_Y_TONER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_M_TONER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_C_TONER_EMPTY);
            RETURN_CASE(STATUS_E_PRINT_K_TONER_EMPTY);

            RETURN_CASE(STATUS_W_PRINT_TONER_EMPTY_Y);
            RETURN_CASE(STATUS_W_PRINT_TONER_EMPTY_M);
            RETURN_CASE(STATUS_W_PRINT_TONER_EMPTY_C);
            RETURN_CASE(STATUS_W_PRINT_TONER_EMPTY_K);

            default: break;

        }
    }

    return 0;
}

uint32_t netsts_check_toner_low(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_W_PRINT_Y_TONER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_M_TONER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_C_TONER_NEAR_EMPTY);
            RETURN_CASE(STATUS_W_PRINT_K_TONER_NEAR_EMPTY);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_check_waste_toner(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);

    for ( size_t i = 0; i < packet->count; ++i )
    {
        switch( packet->array[i].status_id )
        {
            RETURN_CASE(STATUS_W_PRINT_WASTE_TONER_NEAR_FULL);
            RETURN_CASE(STATUS_E_PRINT_WASTE_TONER_FULL);
            RETURN_CASE(STATUS_E_PRINT_W_TB_UNINSTALL);
            default: break;
        }
    }

    return 0;
}

uint32_t netsts_get_top_type(NETSTS_PACKET_S* packet)
{
    RETURN_VAL_IF(packet == NULL || packet->count >= NETSTS_MAX_NUM, NET_WARN, 0);
    uint32_t top_status_type = 0;

    for ( size_t i = 0; i < packet->count; ++i )
    {
        if ( (packet->array[i].status_id & 0xF0000000) > (top_status_type & 0xF0000000) )
        {
            top_status_type = packet->array[i].status_id;
        }
    }
    NET_DEBUG("top_status_type [0x%08x]", top_status_type);

    return top_status_type;
}
/**
 *@}
 */
