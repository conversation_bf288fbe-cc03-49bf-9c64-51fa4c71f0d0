/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ringbuf.h
 * @addtogroup net
 * @{
 * @addtogroup ringbuf
 * <AUTHOR>
 * @date 2023-9-18
 * @brief ring buffer API
 */
#include "nettypes.h"
#include "ringbuf.h"

struct ring_buffer
{
    PI_MUTEX_T  mutex;  ///< mutex lock
    uint8_t*    head;   ///< head pointer of this ring buffer
    uint8_t*    put;    ///< put pointer of this ring buffer(write from here)
    uint8_t*    get;    ///< get pointer of this ring buffer(read from here)
    uint32_t    nput;   ///< current number of bytes in this buffer
    uint32_t    size;   ///< maximum size of this ring buffer
};

int32_t ring_buffer_readable(RING_BUF_S* rbuf, const char* caller)
{
    int32_t res;

    RETURN_VAL_SHOW_CALLER_IF(rbuf == NULL || rbuf->head == NULL, NET_WARN, caller, 0);

    pi_mutex_lock(rbuf->mutex);
    res = (int32_t)rbuf->nput;
    pi_mutex_unlock(rbuf->mutex);

    return res;
}

int32_t ring_buffer_writable(RING_BUF_S* rbuf, const char* caller)
{
    int32_t res;

    RETURN_VAL_SHOW_CALLER_IF(rbuf == NULL || rbuf->head == NULL, NET_WARN, caller, 0);

    pi_mutex_lock(rbuf->mutex);
    res = (int32_t)(rbuf->size > rbuf->nput ? rbuf->size - rbuf->nput : 0);
    pi_mutex_unlock(rbuf->mutex);

    return res;
}

int32_t ring_buffer_read(RING_BUF_S* rbuf, void* buffer, size_t count, const char* caller)
{
    uint8_t* ptr = (uint8_t *)buffer;
    int32_t  tail_bytes;
    int32_t  get_bytes;

    RETURN_VAL_SHOW_CALLER_IF(rbuf == NULL || rbuf->head == NULL, NET_WARN, caller, 0);
    RETURN_VAL_SHOW_CALLER_IF(buffer == NULL, NET_WARN, caller, 0);

    pi_mutex_lock(rbuf->mutex);
    if (rbuf->nput == 0)
    {
        NET_WARN("%s ringbuf is empty, unable to read", caller);
        pi_mutex_unlock(rbuf->mutex);
        return 0;
    }
    get_bytes  = (int32_t)((count <= rbuf->nput) ? count : rbuf->nput);
    tail_bytes = (int32_t)(rbuf->size + rbuf->head - rbuf->get);
    /*                     '-': free                 '=': nput                                  */
    /* <head>==================<put>-------------------------<get>======[tail_bytes]=========== */
    /* 如上所示，get指针在前，且rbuf尾部剩余空间不够取出get_bytes，先取完tail_bytes，然后从头取 */
    if ( get_bytes > tail_bytes )
    {
        memcpy(ptr, rbuf->get,  tail_bytes);
        ptr += tail_bytes;
        memcpy(ptr, rbuf->head, get_bytes - tail_bytes);
        rbuf->get = rbuf->head + get_bytes - tail_bytes;
    }
    else
    {
        memcpy(ptr, rbuf->get,  get_bytes);
        rbuf->get += get_bytes;
    }
    rbuf->nput -= get_bytes;
    pi_mutex_unlock(rbuf->mutex);

    return get_bytes;
}

int32_t ring_buffer_write(RING_BUF_S* rbuf, const void* buffer, size_t count, const char* caller)
{
    uint8_t* ptr = (uint8_t *)buffer;
    int32_t  tail_bytes;
    int32_t  put_bytes;

    RETURN_VAL_SHOW_CALLER_IF(rbuf == NULL || rbuf->head == NULL, NET_WARN, caller, 0);
    RETURN_VAL_SHOW_CALLER_IF(buffer == NULL, NET_WARN, caller, 0);
    RETURN_VAL_SHOW_CALLER_IF(rbuf->size <= rbuf->nput, NET_WARN, caller, 0);

    pi_mutex_lock(rbuf->mutex);
    if (rbuf->size <= rbuf->nput)
    {
        NET_WARN("%s ringbuf is full, unable to write", caller);
        pi_mutex_unlock(rbuf->mutex);
        return 0;
    }
    put_bytes  = (int32_t)((count <= rbuf->size - rbuf->nput) ? count : (rbuf->size - rbuf->nput));
    tail_bytes = (int32_t)(rbuf->size + rbuf->head - rbuf->put);
    /*                     '-': free                 '=': nput                                  */
    /* <head>---------------<get>=========================<put>----------[tail_bytes]---------- */
    /* 如上所示，put指针在前，且rbuf尾部剩余空间不足存放put_bytes，先补满tail_bytes，然后从头存 */
    if ( put_bytes > tail_bytes )
    {
        memcpy(rbuf->put,  ptr, tail_bytes);
        ptr += tail_bytes;
        memcpy(rbuf->head, ptr, put_bytes - tail_bytes);
        rbuf->put = rbuf->head + put_bytes - tail_bytes;
    }
    else
    {
        memcpy(rbuf->put,  ptr, put_bytes);
        rbuf->put += put_bytes;
    }
    rbuf->nput += put_bytes;
    pi_mutex_unlock(rbuf->mutex);

    return put_bytes;
}

int32_t ring_buffer_reset(RING_BUF_S* rbuf, const char* caller)
{
    RETURN_VAL_SHOW_CALLER_IF(rbuf == NULL || rbuf->head == NULL, NET_WARN, caller, -1);

    pi_mutex_lock(rbuf->mutex);
    rbuf->put  = rbuf->head;
    rbuf->get  = rbuf->head;
    rbuf->nput = 0;
    pi_mutex_unlock(rbuf->mutex);

    return 0;
}

void ring_buffer_destroy(RING_BUF_S* rbuf, const char* caller)
{
    RETURN_SHOW_CALLER_IF(rbuf == NULL, NET_WARN, caller);

    if ( rbuf->mutex != INVALIDMTX )
    {
        pi_mutex_destroy(rbuf->mutex);
    }
    if ( rbuf->head != NULL )
    {
        pi_free(rbuf->head);
    }
    pi_free(rbuf);
}

RING_BUF_S* ring_buffer_create(uint32_t size, const char* caller)
{
    RING_BUF_S* rbuf = NULL;

    rbuf = (RING_BUF_S *)pi_zalloc(sizeof(RING_BUF_S));
    if ( rbuf == NULL )
    {
        NET_WARN("[%s] alloc RING_BUF_S(%u) failed: %d<%s>", caller, sizeof(RING_BUF_S), errno, strerror(errno));
        return NULL;
    }

    rbuf->head = pi_zalloc(size * sizeof(uint8_t));
    if ( rbuf->head == NULL )
    {
        NET_WARN("[%s] alloc size %u failed: %d<%s>", caller, size, errno, strerror(errno));
        pi_free(rbuf);
        return NULL;
    }

    rbuf->mutex = pi_mutex_create();
    if ( rbuf->mutex == INVALIDMTX )
    {
        NET_WARN("[%s] create mutex failed", caller);
        pi_free(rbuf->head);
        pi_free(rbuf);
        return NULL;
    }

    rbuf->put  = rbuf->head;
    rbuf->get  = rbuf->head;
    rbuf->nput = 0;
    rbuf->size = size;

    return rbuf;
}
/**
 *@}
 */
