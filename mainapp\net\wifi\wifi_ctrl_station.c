/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi_ctrl_station.c
 * @addtogroup net
 * @{
 * @addtogroup wifi_ctrl_station
 * <AUTHOR> <PERSON>n
 * @date 2023-4-20
 * @brief WiFi-Station control API
 */
#include "nettypes.h"
#include "netmisc.h"
#include "netctx.h"
#include "wifi_country_alias.h"
#include "wifi_process_queue.h"
#include "wifi_ctrl.h"
#include "wifi.h"

#define MAX_AP_SIG_LEVEL                5
#define MIN_AP_SIG_LEVEL                0

typedef enum
{
    WIFI_WPS_STATUS_OFF = 0,
    WIFI_WPS_STATUS_PBC,
    WIFI_WPS_STATUS_PIN
}
WIFI_WPS_STATUS_E;

#define cJSON_ReplaceDigitsToObject(object, name, digit)    {                       \
    snprintf(tmpbuf, sizeof(tmpbuf), "%d", (int32_t)(digit));                       \
    cJSON_ReplaceItemInObject(object, name, cJSON_CreateString(tmpbuf));            \
}

#define cJSON_ReplaceStringToObject(object, name, s)        {                       \
    cJSON_ReplaceItemInObject(object, name, cJSON_CreateString(s));                 \
}

void wifi_set_status_value(NET_CTX_S* net_ctx, WIFI_CONNECTION_DETAIL_E show_id);

#define STA_UPDATE_STATUS(s0, s1, s2, s3, s4)               {                       \
    NET_DEBUG("update WiFi connection status(%d)", s0);                             \
    NET_DEBUG("update WiFi connection detail(%d)", s2);                             \
    NET_DEBUG("update WiFi system status(%s)", #s1);                                \
    s_conn_info.status = s0;                                                        \
    s_conn_info.detail = s2;                                                        \
    s_sys_status[0]    = s1;                                                        \
    s_sys_status[1]    = s2;                                                        \
    s_sys_status[2]    = s3;                                                        \
    s_sys_status[3]    = s4;                                                        \
    wifi_set_status_value(net_ctx,s2);                                              \
    if ( s_conn_info.status == NETLINK_STATUS_CONNECTED ) {                         \
        netdata_set_sta_sec_mode(net_ctx->data_mgr, s_conn_info.sec_mode);          \
        netdata_set_sta_ssid(net_ctx->data_mgr, s_conn_info.ssid);                  \
        netdata_set_sta_psk(net_ctx->data_mgr, s_last_psk);                         \
    }                                                                               \
    netdata_set_sta_detail(net_ctx->data_mgr, s_conn_info.detail);                  \
    if ( netdata_set_sta_status(net_ctx->data_mgr, s_conn_info.status) == 0 )       \
    {                                                                               \
        NETEVT_NOTIFY_T(EVT_TYPE_NET_WIFI_CONNECTION_CHANGED, s_conn_info);         \
    }                                                                               \
    NETEVT_NOTIFY_T(EVT_TYPE_SYSTEMSTATUS_FROM_NET, s_sys_status);                 \
}

struct sta_ap_info
{
    struct list_head        list;
    char                    orig_ssid[128];
    char                    utf8_ssid[128];
    char                    bssid[18];
    uint8_t                 sec_mode;
    uint8_t                 channel;
    uint32_t                frequency;
    uint8_t                 sig_level;
    uint8_t                 pmf_mode;
    uint8_t                 mode;
    uint8_t                 wps_on;
};

static struct wpa_ctrl*     s_sta_wpa_ctrl  = NULL;
static struct list_head     s_ap_info_list[MAX_AP_SIG_LEVEL + 1]; /* 按WiFi信号强度等级（0 ~ 5）分别建立链表，省去后续排序过程 */
static WIFI_CONN_INFO_S     s_conn_info;
static PI_THREAD_T          s_sta_wpa_tid   = INVALIDTHREAD;
static uint32_t             s_sys_status[4] = { 0, 0, 0, 0};
static uint32_t             s_fg_connecting = 0; ///< 在收到扫描结果后，需要再次执行前台连接
static uint32_t             s_bg_connecting = 0; ///< 在收到扫描结果后，需要再次执行后台连接
static uint32_t             s_bg_reconnect  = 0; ///< 在未找到 SSID 时，标记后台重连
static uint32_t             s_need_results  = 0; ///< 在收到扫描结果后，需要上报扫描结果
static uint32_t             s_scan_started  = 0; ///< wpa_supplicant开始执行扫描
static char                 s_last_psk[68];

static void* sta_wpa_monitor(void* arg);

void wifi_set_status_value(NET_CTX_S* net_ctx, WIFI_CONNECTION_DETAIL_E show_id)
{
    NET_DEBUG("wifi connection detail (%d)\n",show_id);
     switch(show_id)
    {
        case WIFI_CONNECTING_DETAIL_WPS_PBC:
            netdata_set_wps_status(net_ctx->data_mgr, WIFI_WPS_STATUS_PBC);
            netdata_set_wps_start_time(net_ctx->data_mgr, (uint32_t)pi_time(NULL));
            break;
        case WIFI_CONNECTING_DETAIL_WPS_PIN:
            netdata_set_wps_status(net_ctx->data_mgr, WIFI_WPS_STATUS_PIN);
            netdata_set_wps_start_time(net_ctx->data_mgr, (uint32_t)pi_time(NULL));
            break;
        case WIFI_DISCONNECTED_DETAIL_WPS_CANCEL:
            netdata_set_wps_status(net_ctx->data_mgr, WIFI_WPS_STATUS_OFF);
            netdata_set_wps_start_time(net_ctx->data_mgr, 0);
            break;
        case WIFI_CONNECTED_DETAIL_WPS_SUCCESS:
            netdata_set_wps_status(net_ctx->data_mgr, WIFI_WPS_STATUS_OFF);
            netdata_set_wps_start_time(net_ctx->data_mgr, 0);
            break;
        default:
            //netdata_set_wps_status(net_ctx->data_mgr, WIFI_WPS_STATUS_OFF);
            break;
    }
}

/* 通过当前AP的工作频率计算信道 */
static uint8_t get_channel_from_frequency(uint32_t frequency)
{
    uint32_t    channel;

    if ( frequency > 5000 )
    {
        channel = (frequency - 5000) / 5;
        if ( channel > 170 )
        {
            NET_INFO("invalid frequency(%u)", frequency);
            channel = 170;
        }
    }
    else
    {
        channel = (frequency - 2407) / 5;
        if ( channel > 13 )
        {
            NET_INFO("invalid frequency(%u)", frequency);
            channel = 13;
        }
    }

    return (uint8_t)channel;
}

static int32_t get_info_from_status_msg(const char* title, char** ppstr)
{
    char* pstr;

    pstr = strstr(*ppstr, title);
    if ( pstr == NULL )
    {
        NET_WARN("no search (%s) from (%s)", title, *ppstr);
        return -1;
    }

    *ppstr = pstr + strlen(title);
    return 0;
}

/* 从wpa_monitor接收的event字符串中获取设备MAC地址(BSSID) */
static int32_t get_bssid_from_recv_msg(const char* s, char* mac)
{
    char* pstr;

    pstr = strchr(s, ':'); /* 找到bssid(xx:xx:xx:xx:xx:xx)中的第一个':'字符 */
    if ( pstr == NULL || strlen(pstr) < 15 )
    {
        NET_WARN("no search bssid from (%s)", s);
        return -1;
    }
    memcpy(mac, pstr - 2, 17); /* 基于':'位置向前偏移2bytes，获取完整bssid */
    mac[17] = '\0';

    return 0;
}

/* 从WPA_STA_CFG_FILE中提取psk信息，适用于WPS连接完成后 */
static int32_t get_psk_from_conf(char* buf, size_t buf_size)
{
    FILE*   stream;
    char*   pstr;
    char    line_buf[256];
    size_t  len;
    int32_t ret = -1;

    RETURN_VAL_IF((stream = pi_fopen(WPA_STA_CFG_FILE, "r")) == NULL, NET_WARN, -1);

    while ( fgets(line_buf, sizeof(line_buf), stream) )
    {
        pstr = line_buf;
        len  = strlen(pstr);
        if ( pstr[len - 1] == '\n' )
        {
            pstr[len - 1] = '\0';
        }

        while ( *pstr == ' ' || *pstr == '\t' )
        {
            pstr++; /* 过滤行首空白字符 */
        }

        if ( *pstr == '\0' )
        {
            continue; /* 过滤空白行 */
        }

        if ( strncmp(pstr, "psk=", strlen("psk=")) == 0 )
        {
            NET_DEBUG("pstr(%s)", pstr);
            pstr += strlen("psk=");
            len   = strlen(pstr);
            if ( pstr[0] == '"' && pstr[len - 1] == '"' )
            {
                pstr[len - 1] = '\0';
                pstr++;
            }
            snprintf(buf, buf_size, "%s", pstr);
            ret = 0;
            break;
        }
    }
    pi_fclose(stream);

    return ret;
}

/* 从信号强度不小于current->sig_level的链表中检索是否有相同SSID */
static int32_t check_ap_valid(struct sta_ap_info* current)
{
    struct sta_ap_info* node;
    struct list_head*   pos;
    struct list_head*   n;
    int32_t             sig;

    for ( sig = MAX_AP_SIG_LEVEL; sig >= (int32_t)(current->sig_level); --sig )
    {
        pi_list_for_each_safe(pos, n, &(s_ap_info_list[sig]))
        {
            node = pi_list_entry(pos, struct sta_ap_info, list);
            if ( node == current ) /* 检索到当前节点位置结束 */
            {
                break;
            }
            if ( node != NULL && strcmp(node->utf8_ssid, current->utf8_ssid) == 0 )
            {
                return 0;
            }
        }
    }

    return 1;
}

/* 将解析后的s_ap_info_list按WIFI_SCAN_RESULT_S格式组装 */
static int32_t packing_scan_result(WIFI_SCAN_RESULT_S* result)
{
    struct sta_ap_info* node;
    struct list_head*   pos;
    struct list_head*   n;
    int32_t sig;

    for (  sig = MAX_AP_SIG_LEVEL, result->ap_count = 0; sig >= MIN_AP_SIG_LEVEL; --sig )
    {
        pi_list_for_each_safe(pos, n, &(s_ap_info_list[sig]))
        {
            node = pi_list_entry(pos, struct sta_ap_info, list);
            BREAK_IF(node == NULL, NET_WARN);

            if ( check_ap_valid(node) )
            {
                snprintf(result->ap_info[result->ap_count].ssid,  sizeof(result->ap_info[result->ap_count].ssid),  "%s", node->utf8_ssid);
                snprintf(result->ap_info[result->ap_count].bssid, sizeof(result->ap_info[result->ap_count].bssid), "%s", node->bssid);
                result->ap_info[result->ap_count].sec_mode = node->sec_mode;
                result->ap_info[result->ap_count].sig_lvl  = node->sig_level;
                result->ap_info[result->ap_count].channel  = node->channel;
                result->ap_info[result->ap_count].mode     = node->mode;
                result->ap_count++;
                RETURN_VAL_IF(result->ap_count >= ARRAY_SIZE(result->ap_info), NET_WARN, 0);
            }
        }
    }

    return 0;
}

/* 处理wpa_supplicant扫描热点返回的SSID中的转义字符'\' */
static int32_t parse_escape_character(char* src, char* dst, size_t dst_size)
{
    char*   pdst = dst;
    char*   psrc = src;
    char*   ptr = NULL;
    char    tmp_code[3] = {0, 0, 0};
    size_t  len = 0;

    while ( (*psrc) && ((size_t)pdst < (size_t)dst + dst_size) )
    {
        /* 检查源字符串中是否有转义符 */
        ptr = strchr(psrc, '\\');
        if ( ptr == NULL )
        {
            pdst += snprintf(pdst, (size_t)(dst_size + dst - pdst), "%s", psrc);
            break;
        }

        /* 拷贝转义字符前的内容到dst */
        len = (size_t)(ptr - psrc);
        if ( len > 0 )
        {
            memcpy(pdst, psrc, len);
            pdst += len;
            psrc += len;
        }

        if ( strlen(ptr) >= 4 && ptr[1] == 'x' && isxdigit(ptr[2]) && isxdigit(ptr[3]) ) /* eg. \xfe */
        {
            /* 处理非ASCII字符，比如4字节字符串"\xfe"转为1字节16进制数0xfe */
            memcpy(tmp_code, ptr + 2, 2);
            *pdst = (char)strtol(tmp_code, NULL, 16);
            pdst += 1;
            psrc += 4;
        }
        else if ( strlen(ptr) >= 2 && ptr[1] == '\\' )
        {
            /* 处理被转义的'\' */
            *pdst = '\\';
            pdst += 1;
            psrc += 2;
        }
        else
        {
            /* 异常逻辑，不应该存在单一的转义字符！！！ */
            NET_WARN("[L%d] ssid(%s) maybe invalid!", __LINE__, src);
            *pdst = '\\';
            pdst += 1;
            psrc += 1;
        }
    }
    *pdst = '\0';

    return 0;
}

/* 从s_ap_info_list中检索指定ssid */
static struct sta_ap_info* search_ap_info_by_ssid(const char* ssid)
{
    struct sta_ap_info* node;
    struct list_head*   pos;
    struct list_head*   n;
    int32_t             sig;

    for ( sig = MAX_AP_SIG_LEVEL; sig >= MIN_AP_SIG_LEVEL; --sig )
    {
        pi_list_for_each_safe(pos, n, &(s_ap_info_list[sig]))
        {
            node = pi_list_entry(pos, struct sta_ap_info, list);
            if ( node != NULL && strcmp(node->utf8_ssid, ssid) == 0 )
            {
                return node;
            }
        }
    }

    return NULL;
}

/* 处理单行热点信息 */
static struct sta_ap_info* parse_scan_line(const char* line_buf)
{
    struct sta_ap_info* info;
    char                orig_ssid[128] = {0};
    char                other_tag[128] = {0};
    int32_t             signal;
    int32_t             siglvl;
    int32_t             ret;

    NET_TRACE("parse line(%s)", line_buf);
    RETURN_VAL_IF((info = (struct sta_ap_info *)pi_zalloc(sizeof(struct sta_ap_info))) == NULL, NET_WARN, NULL);

    ret = sscanf(line_buf, "%17[^\t]%*[\t]%u%*[\t]%d%*[\t]%127[^\t]%*[\t]%127[^\n]", info->bssid, &info->frequency, &signal, other_tag, orig_ssid);
    parse_escape_character(orig_ssid, info->orig_ssid, sizeof(info->orig_ssid));
    if ( ret != 5 || info->orig_ssid[0] == '\0' || convert_str_to_utf8(info->orig_ssid, info->utf8_ssid, sizeof(info->utf8_ssid)) != 0 )
    {
        NET_INFO("orig_ssid(%s) invalid(%d), skip", orig_ssid, ret);
        pi_free(info);
        return NULL;
    }

    /* 信道 */
    info->channel = get_channel_from_frequency(info->frequency);

    /* 信号强度 */
    siglvl = (signal + 100) / 10;
    if ( siglvl > MAX_AP_SIG_LEVEL )
    {
        info->sig_level = MAX_AP_SIG_LEVEL;
    }
    else if ( siglvl < MIN_AP_SIG_LEVEL )
    {
        info->sig_level = MIN_AP_SIG_LEVEL;
    }
    else
    {
        info->sig_level = (uint8_t)siglvl;
    }

    /* 安全模式 */
    if ( strstr(other_tag, "WPA2-SAE-CCMP") != NULL )
    {
        info->sec_mode = SEC_WPA3;
        info->pmf_mode = PMF_REQUIRED;
    }
    else if ( strstr(other_tag, "WPA2-SAE+PSK-SHA256-CCMP") != NULL )
    {
        info->sec_mode = SEC_WPA2WPA3;
        info->pmf_mode = PMF_REQUIRED;
    }
    else if ( strstr(other_tag, "WPA2-PSK+SAE-CCMP") != NULL )
    {
        info->sec_mode = SEC_WPA2WPA3;
        info->pmf_mode = PMF_CAPABLE;
    }
    else if ( strstr(other_tag, "WPA2-PSK-CCMP") != NULL && strstr(other_tag, "WPA-PSK") != NULL )
    {
        info->sec_mode = SEC_WPAWPA2;
        info->pmf_mode = PMF_DISABLE;
    }
    else if ( (strstr(other_tag, "WPA2-PSK-SHA256-CCMP") != NULL) || (strstr(other_tag,"WPA2-PSK+PSK-SHA256-CCMP") != NULL) )
    {
        info->sec_mode = SEC_WPA2;
        info->pmf_mode = PMF_REQUIRED;
    }
    else if ( strstr(other_tag, "WPA2-PSK-CCMP") != NULL )
    {
        info->sec_mode = SEC_WPA2;
        info->pmf_mode = PMF_CAPABLE;
    }
    else if ( strstr(other_tag, "WPA-PSK") != NULL )
    {
        info->sec_mode = SEC_WPA;
        info->pmf_mode = PMF_DISABLE;
    }
    else if ( strstr(other_tag, "WEP") != NULL || strstr(other_tag, "EAP") != NULL )
    {
        NET_DEBUG("unsupport ap mode(%s)", other_tag);
        pi_free(info);
        return NULL;
    }
    else
    {
        info->sec_mode = SEC_OPEN;
        info->pmf_mode = PMF_DISABLE;
    }

    /* 组网模式 */
    if ( strstr(other_tag, "P2P") != NULL )
    {
        info->mode = WIFI_P2P;
    }
    else if ( strstr(other_tag, "IBSS") != NULL )
    {
        info->mode = WIFI_ADH;
    }
    else
    {
        info->mode = WIFI_INF;
    }

    /* 是否支持 WPS */
    if ( strstr(other_tag, "WPS") != NULL )
    {
        info->wps_on = 1;
    }
    else
    {
        info->wps_on = 0;
    }

    return info;
}

/* 解析热点扫描结果，并按信号强度分别挂载到对应的s_ap_info_list链表中*/
static int32_t parse_scan_result(char* recv_buf)
{
    struct sta_ap_info* node;
    struct list_head*   pos;
    struct list_head*   n;
    char*               phead;
    char*               ptail;
    char                line_buf[256];
    int32_t             ap_num = 0;
    int32_t             sig;

    /* 解析前清空所有链表 */
    for ( sig = MAX_AP_SIG_LEVEL; sig >= MIN_AP_SIG_LEVEL; --sig )
    {
        pi_list_for_each_safe(pos, n, &(s_ap_info_list[sig]))
        {
            node = pi_list_entry(pos, struct sta_ap_info, list);
            if ( node != NULL )
            {
                pi_list_del_entry(&(node->list));
                pi_free(node);
            }
        }
    }

    phead = strchr(recv_buf, '\n'); /* 跳过recv_buf的首行（标题行） */
    RETURN_VAL_IF(STRING_IS_EMPTY(phead), NET_WARN, 0);

    while ( (ptail = strchr(phead + 1, '\n')) != NULL )
    {
        *ptail = '\0';
        snprintf(line_buf, sizeof(line_buf), "%s", phead + 1);
        phead = ptail;

        node = parse_scan_line(line_buf);
        if ( node != NULL )
        {
            pi_list_add_tail(&(node->list), &(s_ap_info_list[node->sig_level]));
            ap_num++;
        }
    }

    NET_DEBUG("ap_num(%d)", ap_num);
    return ap_num;
}

static int32_t prepacking_cfg_file(NET_CTX_S* net_ctx, FILE* stream)
{
    char    hostname[HOSTNAME_LEN];
    char    mfg_name[16];
    char    pdt_name[32];
    char    pdt_sn[32];

    netdata_get_hostname(net_ctx->data_mgr, hostname, sizeof(hostname));
    netdata_get_mfg_name(net_ctx->data_mgr, mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(net_ctx->data_mgr, pdt_name, sizeof(pdt_name));
    netdata_get_pdt_sn  (net_ctx->data_mgr, pdt_sn,   sizeof(pdt_sn));

    fprintf(stream, WPA_DEF_CFG_FORMAT, hostname, mfg_name, pdt_name, pdt_name, pdt_sn, wifi_country_alias(netdata_get_country(net_ctx->data_mgr)));
    fprintf(stream, "ap_scan=1\n");

    return 0;
}

static int32_t make_connection_reconfigure(NET_CTX_S* net_ctx, struct sta_ap_info* ap_info, const char* sta_psk)
{
    char        result[64];
    FILE*       stream;

    RETURN_VAL_IF((stream = fopen(WPA_STA_CFG_FILE, "w")) == NULL, NET_WARN, -1);

    NET_DEBUG("packing config: ssid(%s) psk(%s) sec_mode(%u) pmf_mode(%u)", ap_info->orig_ssid, sta_psk, ap_info->sec_mode, ap_info->pmf_mode);
    prepacking_cfg_file(net_ctx, stream);
    switch ( ap_info->sec_mode )
    {
    case SEC_OPEN:
        fprintf(stream, SEC_MODE_OPEN_FORMAT, ap_info->orig_ssid);
        break;
    case SEC_WPAWPA2:
    case SEC_WPA:
        fprintf(stream, (strlen(sta_psk) < 64 ? SEC_MODE_WPA_FORMAT0 : SEC_MODE_WPA_FORMAT1),
                ap_info->orig_ssid,
                sta_psk);
        break;
    case SEC_WPA2:
        fprintf(stream, (strlen(sta_psk) < 64 ? SEC_MODE_WPA2_FORMAT0 : SEC_MODE_WPA2_FORMAT1),
                ap_info->pmf_mode,
                ap_info->orig_ssid,
                sta_psk,
                ap_info->pmf_mode == PMF_REQUIRED ? "WPA-PSK-SHA256" : "WPA-PSK",
                ap_info->pmf_mode);
        break;
    case SEC_WPA2WPA3:
        fprintf(stream, (strlen(sta_psk) < 64 ? SEC_MODE_WPA2_FORMAT0 : SEC_MODE_WPA2_FORMAT1),
                ap_info->pmf_mode,
                ap_info->orig_ssid,
                sta_psk,
                "SAE WPA-PSK",
                ap_info->pmf_mode);
        break;
    case SEC_WPA3:
        fprintf(stream, (strlen(sta_psk) < 64 ? SEC_MODE_WPA3_FORMAT0 : SEC_MODE_WPA3_FORMAT1),
                ap_info->pmf_mode,
                ap_info->orig_ssid,
                sta_psk);
        break;
    default:
        NET_WARN("SSID(%s) mode(%u) is not supported", ap_info->utf8_ssid, ap_info->sec_mode);
        break;
    }
    fclose(stream);

    wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "RECONFIGURE");
    RETURN_VAL_IF(strncasecmp(result, "OK", 2) != 0, NET_WARN, -1);

    return 0;
}

static int32_t process_sta_wpa_reconfigure(NET_CTX_S* net_ctx, void* arg)
{
    int32_t detail = (int32_t)arg;
    char    result[16];
    FILE*   stream;

    RETURN_VAL_IF((stream = fopen(WPA_STA_CFG_FILE, "w")) == NULL, NET_WARN, -1);

    prepacking_cfg_file(net_ctx, stream);
    fclose(stream);

    do
    {
        if ( s_sta_wpa_ctrl == NULL ) /* 检查wpa_ctrl接口是否打开 */
        {
            /* 检查wpa_supplicant是否启动 */
            if ( check_program_exist(WPA_STA_PID_FILE) <= 0 )
            {
                NET_DEBUG("starting wpa_supplicant on "IFACE_STA);
                pi_runcmd(NULL, 0, 0, "wpa_supplicant -B -i "IFACE_STA" -c "WPA_STA_CFG_FILE" -P "WPA_STA_PID_FILE" -f "WPA_STA_LOG_FILE);
                waiting_program_start(WPA_STA_PID_FILE, 10, 200);
            }

            s_sta_wpa_ctrl = wpa_ctrl_open(WPA_CTRL_DIR"/"IFACE_STA);
            if ( s_sta_wpa_ctrl == NULL )
            {
                NET_WARN("wpa_ctrl_open failed: %d<%s>", errno, strerror(errno));
                detail = 0xFFFFFFFF;
                break;
            }

            if ( s_sta_wpa_tid != INVALIDTHREAD )
            {
                NET_WARN("WARNING: sta_wpa_monitor thread should not be running!!!");
                pi_thread_destroy(s_sta_wpa_tid);
            }

            /* 启动wpa_ctrl状态监视线程 */
            s_sta_wpa_tid = pi_thread_create(sta_wpa_monitor, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, net_ctx, "sta_wpa_monitor");
            if ( s_sta_wpa_tid == INVALIDTHREAD )
            {
                NET_WARN("create sta_wpa_monitor thread failed");
                detail = 0xFFFFFFFF;
                break;
            }

            for ( int32_t i = 0; i <= MAX_AP_SIG_LEVEL; ++i )
            {
                pi_init_list_head(&(s_ap_info_list[i]));
            }
        }

        wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "RECONFIGURE");
        RETURN_VAL_IF(strncasecmp(result, "OK", 2) != 0, NET_WARN, -1);
    }
    while ( 0 );

    if ( detail == 0xFFFFFFFF )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_INIT_ERROR, detail, 0xFFFFFFFF, 0xFFFFFFFF);
        wifi_process_queue_deinit();
    }
    else if ( detail == WIFI_DISCONNECTED_DETAIL_TIMEOUT )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_CONNECT_TIMEOUT, WIFI_DISCONNECTED_DETAIL_TIMEOUT, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    else if ( detail == WIFI_DISCONNECTED_DETAIL_NO_SSID )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_CONNECT_NO_SSID, WIFI_DISCONNECTED_DETAIL_NO_SSID, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    else if ( detail == WIFI_DISCONNECTED_DETAIL_ERR_PSK )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_CONNECT_ERR_PSK, WIFI_DISCONNECTED_DETAIL_ERR_PSK, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    else if ( detail == WIFI_DISCONNECTED_DETAIL_UNKNOWN )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_CONNECT_FAIL, WIFI_DISCONNECTED_DETAIL_UNKNOWN, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    else if ( detail == WIFI_DISCONNECTED_DETAIL_UNRECORD )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_CONNECT_NO_RECORD, WIFI_DISCONNECTED_DETAIL_UNRECORD, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    else if ( detail == WIFI_CONNECTION_DETAIL_NONE )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_DISCONNECT, detail, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    netdata_set_sta_freq(net_ctx->data_mgr, 0);    /* 工作频率清零 */
    netdata_set_sta_rssi(net_ctx->data_mgr, 0);    /* 信号强度清零 */
    s_fg_connecting = 0;
    s_bg_connecting = 0;

    return 0;
}

static int32_t process_sta_switch(NET_CTX_S* net_ctx, void* arg)
{
    IFACE_SWITCH_E active = (arg ? IFACE_SWITCH_ON : IFACE_SWITCH_OFF);

    RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_STA, active) < 0, NET_WARN, -1);

    if ( netdata_set_iface_switch(net_ctx->data_mgr, IFACE_ID_STA, active) == 0 )
    {
        process_sta_wpa_reconfigure(net_ctx, (void*)WIFI_CONNECTION_DETAIL_INIT);
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WIFI_SWITCH_CHANGED, active);
    }

    return 0;
}

static int32_t process_sta_scan_ssid(NET_CTX_S* net_ctx, void* arg)
{
    const char* ssid = (const char *)arg;
    char        scan_cmd[268];
    char        result[16];
    int32_t     offset;

    offset = snprintf(scan_cmd, sizeof(scan_cmd), "SCAN ssid ");
    RETURN_VAL_IF(offset > sizeof(scan_cmd), NET_WARN, -1);

    wpa_ctrl_req(s_sta_wpa_ctrl, result, sizeof(result), "BSS_FLUSH ", NULL, 0);
    pi_sleep(2);

    /* 组装SCAN SSID命令，SSID需转码为HEX字符串 */
    convert_hexbytes_to_string((const uint8_t *)ssid, strlen(ssid), scan_cmd + offset, sizeof(scan_cmd) - offset);
    wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), scan_cmd);
    RETURN_VAL_IF(strncasecmp(result, "OK", 2) != 0, NET_WARN, -1);

    return 0;
}

static int32_t process_sta_scan(NET_CTX_S* net_ctx, void* arg)
{
    char result[16];

    s_need_results = (uint32_t)arg;
    if ( s_need_results )
    {
        if ( s_scan_started == 0 )
        {
            RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_STA, IFACE_SWITCH_ON) < 0, NET_WARN, -1);
            wpa_ctrl_req(s_sta_wpa_ctrl, result, sizeof(result), "BSS_FLUSH ", NULL, 0);
            pi_sleep(2);
            wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "SCAN");
            NET_DEBUG("scan start");
            s_scan_started = 1;
        }
        else
        {
            NET_DEBUG("scanning");
        }
    }
    else
    {
        NET_DEBUG("scan cancel");
    }

    return 0;
}

static int32_t process_sta_fg_connect(NET_CTX_S* net_ctx, void* arg)
{
    struct sta_ap_info* ap_info = NULL;
    int32_t*            pstatus = (int32_t *)arg;
    char                gbk_ssid[128];
    char                sta_ssid[128];
    char                sta_psk[68];

    do
    {
        netdata_get_sta_ssid(net_ctx->data_mgr, sta_ssid, sizeof(sta_ssid));
        netdata_get_sta_psk (net_ctx->data_mgr, sta_psk,  sizeof(sta_psk));
        NET_DEBUG("ssid(%s) psk(%s)", sta_ssid, sta_psk);
        s_bg_connecting = 0;
        s_bg_reconnect  = 0;

        if ( sta_ssid[0] == '\0' )
        {
            if ( s_conn_info.ssid[0] == '\0' ) /* 手动重连上一次的热点时，没有连接记录 */
            {
                process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_UNRECORD);
                break;
            }

            /* 重连至最后一次连接成功的WiFi热点 */
            snprintf(sta_ssid, sizeof(sta_ssid), "%s", s_conn_info.ssid);
            snprintf(sta_psk,  sizeof(sta_psk),  "%s", s_last_psk);
            NET_DEBUG("last ssid(%s) psk(%s)", sta_ssid, sta_psk);
        }

        /* 检查IFACE_STA是否打开 */
        if ( net_ifctl_switch_safe(IFACE_STA, IFACE_SWITCH_ON) < 0 )
        {
            NET_WARN("turn on "IFACE_STA" failed!!!");
            process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_UNKNOWN);
            break;
        }

        if ( s_conn_info.status != NETLINK_STATUS_CONNECTING )
        {
            STA_UPDATE_STATUS(NETLINK_STATUS_CONNECTING, STATUS_I_NET_WIFI_STA_CONNECTING, WIFI_CONNECTING_DETAIL_STATION, 0xFFFFFFFF, 0xFFFFFFFF); /* 上报状态 WiFi正在连接中 */
        }

        ap_info = search_ap_info_by_ssid(sta_ssid);
        if ( ap_info == NULL ) /* 当前s_ap_info_list中未找到匹配的SSID，发起热点搜索，在收到SCAN_RESULT时再次尝试连接 */
        {
            s_fg_connecting++;
            if ( s_fg_connecting == 1 )
            {
                NET_DEBUG("scan sta_ssid(%s) for connecting", sta_ssid);
                process_sta_scan_ssid(net_ctx, (void *)sta_ssid);
            }
            else if ( s_fg_connecting == 2 && get_coding_format(sta_ssid) == CODING_FORMAT_UTF8 ) /* ASCII编码的SSID无需二次搜索 */
            {
                convert_coding_format("utf-8", "gbk", sta_ssid, strlen(sta_ssid), gbk_ssid, sizeof(gbk_ssid));
                NET_DEBUG("scan gbk_ssid(%s) for connecting", gbk_ssid);
                process_sta_scan_ssid(net_ctx, (void *)gbk_ssid);
            }
            else
            {
                process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_NO_SSID);
                s_bg_reconnect = 1; /* 未找到SSID，标记后台连接 */
            }
            break;
        }
        s_fg_connecting = 0;

        if ( ap_info->sec_mode >= SEC_WPA && ap_info->sec_mode <= SEC_WPA3 && strlen(sta_psk) < 8 )
        {
            NET_WARN("ssid(%s) sec_mode(%d) psk(%s) invalid", ap_info->utf8_ssid, ap_info->sec_mode, sta_psk);
            process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_ERR_PSK);
            break;
        }

        if ( make_connection_reconfigure(net_ctx, ap_info, sta_psk) != 0 )
        {
            process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_UNKNOWN);
        }
    }
    while ( 0 );

    if ( s_conn_info.status == NETLINK_STATUS_DISCONNECTED && s_conn_info.detail != WIFI_DISCONNECTED_DETAIL_NO_SSID )
    {
        netdata_set_sta_sec_mode(net_ctx->data_mgr, 0);
        netdata_set_sta_ssid(net_ctx->data_mgr, "");
        netdata_set_sta_psk(net_ctx->data_mgr, "");
    }
    if ( pstatus )
    {
        *pstatus = s_conn_info.status;
    }
    return 0;
}

static int32_t process_sta_disconnect(NET_CTX_S* net_ctx, void* arg)
{
    RETURN_VAL_IF(s_conn_info.status == NETLINK_STATUS_DISCONNECTED, NET_DEBUG, 0);

    return process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_DISCONNECT);
}

static int32_t process_sta_wps_pbc(NET_CTX_S* net_ctx, void* arg)
{
    char result[64];

    RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_STA, IFACE_SWITCH_ON) < 0, NET_WARN, -1);

    wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "WPS_PBC");
    RETURN_VAL_IF(strncasecmp(result, "OK", 2) != 0, NET_WARN, -1);

    STA_UPDATE_STATUS(NETLINK_STATUS_CONNECTING, STATUS_I_NET_WIFI_WPS_PBC, WIFI_CONNECTING_DETAIL_WPS_PBC, 0xFFFFFFFF, 0xFFFFFFFF);

    return 0;
}

static int32_t process_sta_wps_pin(NET_CTX_S* net_ctx, void* arg)
{
    char result[128];
    char pin[9];

    RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_STA, IFACE_SWITCH_ON) < 0, NET_WARN, -1);

    wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "WPS_PIN any");
    NET_DEBUG("wps pin result(%s)", result);
    snprintf(pin, sizeof(pin), "%s", result);
    netdata_set_wps_pin(net_ctx->data_mgr, pin);
    NETEVT_NOTIFY_S(EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED, pin);

    STA_UPDATE_STATUS(NETLINK_STATUS_CONNECTING, STATUS_I_NET_WIFI_WPS_PIN, WIFI_CONNECTING_DETAIL_WPS_PIN, 0xFFFFFFFF, 0xFFFFFFFF);

    return 0;
}

static int32_t process_sta_wps_cancel(NET_CTX_S* net_ctx, void* arg)
{
    char result[64];

    wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "WPS_CANCEL");
    RETURN_VAL_IF(strncasecmp(result, "OK", 2) != 0, NET_WARN, -1);

    netdata_set_wps_pin(net_ctx->data_mgr, "");
    NETEVT_NOTIFY_S(EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED, "");

    STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_I_NET_WIFI_WPS_CANCEL, WIFI_DISCONNECTED_DETAIL_WPS_CANCEL, 0xFFFFFFFF, 0xFFFFFFFF);

    return 0;
}

static int32_t process_sta_bg_connect(NET_CTX_S* net_ctx, void* arg)
{
    struct sta_ap_info* ap_info = NULL;
    char                gbk_ssid[128];
    char                sta_ssid[128];
    char                sta_psk[68];
    uint32_t            reconn = 0;

    RETURN_VAL_IF(netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_STA) == IFACE_SWITCH_OFF, NET_INFO, 0);
    RETURN_VAL_IF(s_bg_reconnect == 0, NET_INFO, 0);

    do
    {
        BREAK_IF(netdata_get_sta_status(net_ctx->data_mgr) != NETLINK_STATUS_DISCONNECTED, NET_INFO); /* WiFi 已连接或连接中，不再执行自动重连 */

        netdata_get_sta_ssid(net_ctx->data_mgr, sta_ssid, sizeof(sta_ssid));
        netdata_get_sta_psk (net_ctx->data_mgr, sta_psk,  sizeof(sta_psk));
        NET_DEBUG("ssid(%s) psk(%s)", sta_ssid, sta_psk);
        BREAK_IF(STRING_IS_EMPTY(sta_ssid), NET_INFO);

        ap_info = search_ap_info_by_ssid(sta_ssid);
        if ( ap_info == NULL ) /* 当前s_ap_info_list中未找到匹配的SSID，发起热点搜索，在收到SCAN_RESULT时再次尝试连接 */
        {
            s_bg_connecting++;
            if ( s_bg_connecting == 1 )
            {
                NET_DEBUG("scan sta_ssid(%s) for reconnecting", sta_ssid);
                process_sta_scan_ssid(net_ctx, (void *)sta_ssid);
            }
            else if ( s_bg_connecting == 2 && get_coding_format(sta_ssid) == CODING_FORMAT_UTF8 ) /* ASCII编码的SSID无需二次搜索 */
            {
                convert_coding_format("utf-8", "gbk", sta_ssid, strlen(sta_ssid), gbk_ssid, sizeof(gbk_ssid));
                NET_DEBUG("scan gbk_ssid(%s) for reconnecting", gbk_ssid);
                process_sta_scan_ssid(net_ctx, (void *)gbk_ssid);
            }
            else
            {
                NET_DEBUG("no search ssid(%s)", sta_ssid);
                s_bg_connecting = 0;
            }
            reconn = 1;
            break;
        }
        s_bg_connecting = 0;

        if ( ap_info->sec_mode >= SEC_WPA && ap_info->sec_mode <= SEC_WPA3 && strlen(sta_psk) < 8 )
        {
            NET_WARN("ssid(%s) sec_mode(%d) psk(%s) invalid", ap_info->utf8_ssid, ap_info->sec_mode, sta_psk);
            break;
        }

        if ( make_connection_reconfigure(net_ctx, ap_info, sta_psk) != 0 )
        {
            process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_CONNECTION_DETAIL_NONE);
        }
    }
    while ( 0 );

    s_bg_reconnect = reconn;
    if ( s_bg_reconnect == 0 )
    {
        s_bg_connecting = 0;
    }
    return 0;
}

static int32_t process_sta_info_refresh(NET_CTX_S* net_ctx, void* arg)
{
    int32_t frequency;
    int32_t siglvl;
    int32_t signal;
    char    result[128];
    char*   pstr;

    if ( s_conn_info.status == NETLINK_STATUS_CONNECTED )
    {
        wpa_ctrl_req(s_sta_wpa_ctrl, result, sizeof(result), "SIGNAL_POLL", NULL, 0);

        pstr = strstr(result, "FREQUENCY="); /* 更新当前连接的工作频率 */
        if ( pstr != NULL )
        {
            frequency = atoi(pstr + strlen("FREQUENCY="));
            netdata_set_sta_freq(net_ctx->data_mgr, frequency);
        }

        pstr = strstr(result, "RSSI="); /* 更新当前连接的信号强度 */
        if ( pstr != NULL )
        {
            signal = atoi(pstr + strlen("RSSI="));
            siglvl = (signal + 100) / 10;
            if ( siglvl > MAX_AP_SIG_LEVEL )
            {
                siglvl = MAX_AP_SIG_LEVEL;
            }
            else if ( siglvl < MIN_AP_SIG_LEVEL )
            {
                siglvl = MIN_AP_SIG_LEVEL;
            }
            netdata_set_sta_rssi(net_ctx->data_mgr, siglvl);
        }
    }

    return 0;
}

static int32_t process_sta_conn_timeout(NET_CTX_S* net_ctx, void* arg)
{
    if ( s_conn_info.status == NETLINK_STATUS_CONNECTING )
    {
        switch ( s_conn_info.detail )
        {
        case WIFI_CONNECTING_DETAIL_STATION:
            netdata_set_sta_ssid(net_ctx->data_mgr, "");
            process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_TIMEOUT);
            break;
        case WIFI_CONNECTING_DETAIL_WPS_PBC:
        case WIFI_CONNECTING_DETAIL_WPS_PIN:
            process_sta_wps_cancel(net_ctx, NULL);
            break;
        default:
            NET_DEBUG("detail(%u)", s_conn_info.detail);
            break;
        }
    }

    return 0;
}

static int32_t process_sta_event_disconnected(NET_CTX_S* net_ctx, void* arg)
{
    NET_DEBUG("disconnect to (%s), current status(0x%X)", (char *)arg, s_conn_info.status);
    RETURN_VAL_IF(arg == NULL, NET_WARN, -1);
    if ( s_conn_info.status == NETLINK_STATUS_CONNECTED && strcasecmp(s_conn_info.bssid, (char *)arg) == 0 )
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_DISCONNECTED, STATUS_E_NET_WIFI_DISCONNECT, WIFI_DISCONNECTED_DETAIL_DISCONNECT, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    pi_free(arg);

    return 0;
}

static int32_t process_sta_event_connected(NET_CTX_S* net_ctx, void* arg)
{
    char        result[1024] = {0};
    char        bssid[18] = {0};
    char        ssid[128] = {0};
    char        orig[128] = {0};
    char*       pstr = result;
    uint32_t    freq;

    RETURN_VAL_IF(arg == NULL, NET_WARN, -1);
    s_bg_connecting = 0;
    s_bg_reconnect  = 0;

    NET_DEBUG("connected to bssid(%s)", (char *)arg);
    snprintf(bssid, sizeof(bssid), "%s", (char *)arg);
    pi_free(arg);

    RETURN_VAL_IF(s_conn_info.status == NETLINK_STATUS_CONNECTED && strcmp(s_conn_info.bssid, bssid) == 0, NET_DEBUG, 0);

    wpa_ctrl_cmd(s_sta_wpa_ctrl, result, sizeof(result), "STATUS");
    RETURN_VAL_IF(strstr(result, "wpa_state=COMPLETED") == NULL, NET_WARN, -1);

    if ( netdata_set_iface_switch(net_ctx->data_mgr, IFACE_ID_STA, 1) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WIFI_SWITCH_CHANGED, 1);
    }

    /* bssid */
    if ( get_info_from_status_msg("bssid=", &pstr) == 0 )
    {
        sscanf(pstr, "%17[^\n]", s_conn_info.bssid);
        NET_DEBUG("bssid(%s)", s_conn_info.bssid);
    }

    /* 信道 */
    if ( get_info_from_status_msg("freq=", &pstr) == 0 )
    {
        freq = (uint32_t)atoi(pstr);
        netdata_set_sta_freq(net_ctx->data_mgr, freq);
        NET_DEBUG("frequency(%u)", freq);

        s_conn_info.channel = (uint16_t)get_channel_from_frequency(freq);
        NET_DEBUG("channel(%u)", s_conn_info.channel);
    }

    /* ssid */
    if ( get_info_from_status_msg("ssid=", &pstr) == 0 )
    {
        memset(orig, 0x00, sizeof(orig));
        sscanf(pstr, "%127[^\n]", orig);
        parse_escape_character(orig, ssid, sizeof(ssid));
        convert_str_to_utf8(ssid, s_conn_info.ssid, sizeof(s_conn_info.ssid));
        NET_DEBUG("orig(%s) ssid(%s)", orig, s_conn_info.ssid);
    }

    /* 模式 */
    if ( get_info_from_status_msg("mode=", &pstr) == 0 )
    {
        if ( strcasestr(pstr, "station") )
        {
            s_conn_info.mode = WIFI_INF;
        }
        else if ( strcasestr(pstr, "p2p") )
        {
            s_conn_info.mode = WIFI_P2P;
        }
        else
        {
            s_conn_info.mode = WIFI_ERR;
        }
        NET_DEBUG("mode(%u)", s_conn_info.mode);
    }

    /* 安全模式 */
    if ( get_info_from_status_msg("key_mgmt=", &pstr) == 0 )
    {
        if ( strstr(pstr, "WPA3-PSK") || strstr(pstr, "SAE") )
        {
            s_conn_info.sec_mode = SEC_WPA3;
        }
        else if ( strstr(pstr, "WPA2-PSK") )
        {
            s_conn_info.sec_mode = SEC_WPA2;
        }
        else if ( strstr(pstr, "WPA-PSK") )
        {
            s_conn_info.sec_mode = SEC_WPA;
        }
        else
        {
            s_conn_info.sec_mode = SEC_OPEN;
        }
        NET_DEBUG("sec(%u)", s_conn_info.sec_mode);
    }

    if ( get_psk_from_conf(s_last_psk, sizeof(s_last_psk)) == 0 )
    {
        NET_DEBUG("psk(%s)", s_last_psk);
    }

    if ( s_conn_info.status == NETLINK_STATUS_CONNECTING )
    {
        if ( s_conn_info.detail == WIFI_CONNECTING_DETAIL_WPS_PBC || s_conn_info.detail == WIFI_CONNECTING_DETAIL_WPS_PIN )
        {
            STA_UPDATE_STATUS(NETLINK_STATUS_CONNECTED, STATUS_I_NET_WIFI_WPS_SUCCESS, WIFI_CONNECTED_DETAIL_WPS_SUCCESS, 0xFFFFFFFF, 0xFFFFFFFF);
        }
        else
        {
            STA_UPDATE_STATUS(NETLINK_STATUS_CONNECTED, STATUS_I_NET_WIFI_CONNECT_SUCCESS, WIFI_CONNECTED_DETAIL_STA_SUCCESS, 0xFFFFFFFF, 0xFFFFFFFF);
        }
    }
    else
    {
        STA_UPDATE_STATUS(NETLINK_STATUS_CONNECTED, STATUS_I_NET_WIFI_CONNECT_SUCCESS, WIFI_CONNECTED_DETAIL_CONNECTED, 0xFFFFFFFF, 0xFFFFFFFF);
    }
    NET_DEBUG("connect to %s completed", s_conn_info.ssid);
    process_sta_info_refresh(net_ctx, NULL);
    wifi_ctrl_wfd_p2p_group(0);
    wifi_ctrl_wfd_p2p_group(1);

    return 0;
}

static int32_t process_sta_event_scan_started(NET_CTX_S* net_ctx, void* arg)
{
    NET_DEBUG("scan started");
    s_scan_started = 1;
    return 0;
}

static int32_t process_sta_event_scan_results(NET_CTX_S* net_ctx, void* arg)
{
    WIFI_SCAN_RESULT_S  scan_result = { .ap_count = 0 };
    char                result[1024*16];
    int32_t             ap_num;

    wpa_ctrl_req(s_sta_wpa_ctrl, result, sizeof(result), "SCAN_RESULTS", (s_need_results ? __func__ : 0), __LINE__);
    ap_num = parse_scan_result(result);

    if ( s_fg_connecting )
    {
        process_sta_fg_connect(net_ctx, NULL);
    }

    if ( s_bg_connecting )
    {
        process_sta_bg_connect(net_ctx, NULL);
    }

    if ( s_need_results )
    {
        if ( ap_num > 0 )
        {
            packing_scan_result(&scan_result);
        }
        netdata_set_ap_list(net_ctx->data_mgr, (void *)&scan_result, sizeof(scan_result));
        netdata_set_ap_num(net_ctx->data_mgr, scan_result.ap_count);
        NETEVT_NOTIFY_T(EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED, scan_result);
        s_need_results = 0;
    }
    s_scan_started = 0;

    return 0;
}

static int32_t process_sta_handshake_failed(NET_CTX_S* net_ctx, void* arg)
{
    NET_DEBUG("current status(0x%X)", s_conn_info.status);
    if ( s_conn_info.status == NETLINK_STATUS_CONNECTING )
    {
        process_sta_wpa_reconfigure(net_ctx, (void *)WIFI_DISCONNECTED_DETAIL_ERR_PSK);
        netdata_set_sta_ssid(net_ctx->data_mgr, "");
    }

    return 0;
}

static void* sta_wpa_monitor(void* arg)
{
    NET_CTX_S*          net_ctx = (NET_CTX_S *)arg;
    struct wpa_ctrl*    wpa_recv_ctrl = NULL;
    uint32_t            new_status = NETLINK_STATUS_DISCONNECTED;
    uint32_t            old_status = NETLINK_STATUS_DISCONNECTED;
    uint32_t            sta_detail = 0;
    uint32_t            counter = 0;
    char*               pstr = NULL;
    char                bssid[18];
    char                buf[512];
    size_t              len;

    wpa_recv_ctrl = wpa_ctrl_open(WPA_CTRL_DIR"/"IFACE_STA);
    RETURN_VAL_IF(wpa_recv_ctrl == NULL, NET_WARN, NULL);

    NET_DEBUG("Register as an event monitor for the "IFACE_STA" control interface");
    if ( wpa_ctrl_attach(wpa_recv_ctrl) != 0 )
    {
        NET_WARN("wpa_ctrl_attach "IFACE_STA" failed: %d<%s>", errno, strerror(errno));
        wpa_ctrl_close(wpa_recv_ctrl);
        return NULL;
    }

    while ( 1 )
    {
        if ( wpa_ctrl_pending(wpa_recv_ctrl) > 0 )
        {
            len = sizeof(buf) - 1;
            wpa_ctrl_recv(wpa_recv_ctrl, buf, &len);
            buf[len] = '\0';

            if ( strstr(buf, "CTRL-EVENT-BSS-ADDED") == NULL && strstr(buf, "CTRL-EVENT-BSS-REMOVED") == NULL ) /* 过滤不关注的事件，减少LOG刷屏 */
            {
                NET_DEBUG("len(%u), buf(%s)", len, buf);
            }

            if ( (pstr = strstr(buf, "CTRL-EVENT-DISCONNECTED")) != NULL ) /* 断开连接 <3>CTRL-EVENT-DISCONNECTED bssid=3c:ce:73:8e:24:3d reason... */
            {
                if ( get_bssid_from_recv_msg(pstr, bssid) == 0 )
                {
                    PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_event_disconnected, strdup(bssid));
                }
            }
            else if ( (pstr = strstr(buf, "CTRL-EVENT-CONNECTED")) != NULL ) /* 连接成功 <3>CTRL-EVENT-CONNECTED - Connection to xx:xx:xx:xx:xx:xx completed ... */
            {
                if ( get_bssid_from_recv_msg(pstr, bssid) == 0 )
                {
                    PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_event_connected, strdup(bssid));
                }
            }
            else if ( strstr(buf, "CTRL-EVENT-SCAN-STARTED") != NULL ) /* 开始扫描 */
            {
                PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_event_scan_started, NULL);
            }
            else if ( strstr(buf, "CTRL-EVENT-SCAN-RESULTS") != NULL ) /* 扫描完成 */
            {
                PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_event_scan_results, NULL);
            }
            else if ( strstr(buf, "4-Way Handshake failed") != NULL ) /* 密码错误 */
            {
                PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_handshake_failed, NULL);
            }
        }
        else
        {
            new_status = netdata_get_sta_status(net_ctx->data_mgr);
            if ( new_status != old_status ) /* 状态变更，重置计时器 */
            {
                old_status = new_status;
                counter = 0;
            }

            switch ( new_status )
            {
            case NETLINK_STATUS_DISCONNECTED: /* 未连接，判断是否执行后台自动重连，重连间隔40S */
                if ( netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_STA) )
                {
                    if ( s_bg_reconnect && counter++ > 40 )
                    {
                        NET_DEBUG("process connect background");
                        PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_bg_connect, NULL);
                        counter = 0;
                    }
                }
                else if ( counter > 0 )
                {
                    counter = 0;
                }
                break;
            case NETLINK_STATUS_CONNECTED: /* 已连接，刷新当前连接AP的信号强度，刷新间隔10秒 */
                if ( counter++ > 10 )
                {
                    PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_info_refresh, NULL);
                    counter = 0;
                }
                break;
            case NETLINK_STATUS_CONNECTING: /* 连接中，检查是否超时 */
                sta_detail = netdata_get_sta_detail(net_ctx->data_mgr);
                if ( ((sta_detail == WIFI_CONNECTING_DETAIL_WPS_PBC || sta_detail == WIFI_CONNECTING_DETAIL_WPS_PIN) && counter++ > 123)
                    || (sta_detail == WIFI_CONNECTING_DETAIL_STATION && counter++ > 30) )
                {
                    NET_DEBUG("process connecting(%u) timeout", sta_detail);
                    PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_conn_timeout, NULL);
                    counter = 0;
                }
                break;
            default:
                break;
            }
            pi_msleep(1000);
        }
    }

    return NULL;
}

int32_t wifi_ctrl_sta_wpa_reconfigure(void)
{
    NET_DEBUG("process_sta_wpa_reconfigure");
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_wpa_reconfigure, (void *)WIFI_CONNECTION_DETAIL_INIT);
}

int32_t wifi_ctrl_sta_switch(uint32_t on)
{
    NET_DEBUG("process_sta_switch(%u)", on);
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_switch, (void *)on);
}

int32_t wifi_ctrl_sta_scan_ssid(uint32_t on)
{
    NET_DEBUG("process_sta_scan(%u)", on);
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_scan, (void *)on);
}

/* sync: 0 - 异步调用； 1 - 等待process_sta_fg_connect执行完成后返回； 2 - 等待本次连接结果后返回 */
int32_t wifi_ctrl_sta_connect(int32_t sync)
{
    int32_t ret = 0;
    int32_t rs = -1;

    NET_DEBUG("process_sta_fg_connect(%d)", sync);
    ret = PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_fg_connect, (void *)(sync ? &rs : NULL));
    RETURN_VAL_IF(sync == 0 || ret < 0, NET_NONE, ret); /* 异步调用 || push到process queue失败 */

    while ( rs == -1 ) /* 等待wifi_process_queue_handler执行完callback，rs为callback返回时的WiFi状态 */
    {
        pi_msleep(500);
    }

    RETURN_VAL_IF(sync == 1 || rs != NETLINK_STATUS_CONNECTING, NET_DEBUG, rs); /* process_sta_fg_connect执行完成，返回WiFi状态 */

    while ( s_conn_info.status == NETLINK_STATUS_CONNECTING )
    {
        pi_msleep(500);
    }

    return s_conn_info.status;
}

int32_t wifi_ctrl_sta_disconnect(void)
{
    NET_DEBUG("process_sta_disconnect");
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_disconnect, NULL);
}

int32_t wifi_ctrl_sta_wps_pbc(void)
{
    NET_DEBUG("process_sta_wps_pbc");
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_wps_pbc, NULL);
}

int32_t wifi_ctrl_sta_wps_pin(void)
{
    NET_DEBUG("process_sta_wps_pin");
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_wps_pin, NULL);
}

int32_t wifi_ctrl_sta_wps_cancel(void)
{
    NET_DEBUG("process_sta_wps_cancel");
    return PUSH_TASK_TO_STA_PROCESS_QUEUE(process_sta_wps_cancel, NULL);
}
/**
 *@}
 */
