#include "PEDK_event.h"


#define SERVER_ADDR "127.0.0.1"
#define SERVER_PORT 50999
#define RECV_SIZE (1024)

/*实现消息队列*/
static struct MessageQueue m_queue;
static int clientWriteFlag = 0; //需要extern
static struct strSocketProtocol *sendInfo=NULL;
static pthread_mutex_t mutex,m_queue_mutex,m_list_mutex; //定义互斥锁
static JSContext* g_ctx = NULL;
static pthread_t client_tid,dispatch_tid;

static struct strListMsg msgListHead;

static syncMsg syncArr[]={
	{ MSG_MODULE_LOWPOWER, MSG_LOWPOWER_SUB_GET },
    { MSG_MODULE_TEST    , MSG_TEST_SUB },
	{ MSG_MOUDLE_BEEP, MSG_BEEP_GET_DURATION },
	{ MSG_MOUDLE_BEEP, MSG_BEEP_GET_FREQUENCY },
	{ MSG_MOUDLE_BEEP, MSG_BEEP_GET_VOLUME },
	{ MSG_MOUDLE_BEEP, MSG_BEEP_GET_WORK_STATUS },
    { MSG_MODULE_STATUS,    MSG_STATUS_SUB },
    { MSG_MODULE_STATUS,    MSG_STATUS_SUB_ID },
    { MSG_MODULE_STATUS,    MSG_STATUS_SUB_TYPE },
    { MSG_MODULE_STATUS,    MSG_STATUS_SUB_MODULE },
    { MSG_MODULE_STATUS,    MSG_STATUS_SUB_PRI },
    { MSG_MODULE_STATUS,    MSG_STATUS_SUB_LIST_LEN },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_SETFUNCTIONSWITCH },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_TRAY_PAPER_SIZE },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_TRAY_PAPER_TYPE },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_C_TONER_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_M_TONER_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_Y_TONER_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_K_TONER_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_C_DRUM_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_M_DRUM_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_Y_DRUM_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_K_DRUM_STATUS },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_POWER_BOX_MODEL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_C_TONER_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_M_TONER_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_Y_TONER_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_K_TONER_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_C_DRUM_MODEL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_M_DRUM_MODEL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_Y_DRUM_MODEL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_K_DRUM_MODEL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_C_DRUM_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_M_DRUM_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_Y_DRUM_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_K_DRUM_REMAIN_VAL },
    { MSG_MODULE_SETTING,   MSG_SETTING_SUB_AVERAGE_COVERAGE_RATE },
    { MSG_MODULE_NET,   MSG_NET_SUB_ADD_EMAIL_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_EMAIL_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_EMAIL_ADDR_LIST},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_EMAIL_ADDR_NUM},
    { MSG_MODULE_NET,   MSG_NET_SUB_IS_EMAIL_ADDR_FULL},
    { MSG_MODULE_NET,   MSG_NET_SUB_MODIFY_EMAIL_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_REMOVE_EMAIL_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_ADD_EMAIL_TO_GROUP},
    { MSG_MODULE_NET,   MSG_NET_SUB_CREAT_EMAIL_GROUP},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_EMAIL_GROUP},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_EMAIL_GROUP_LIST},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_EMAIL_GROUP_NUM},
    { MSG_MODULE_NET,   MSG_NET_SUB_IS_EMAIL_GROUP_FULL},
    { MSG_MODULE_NET,   MSG_NET_SUB_MODIFY_EMAIL_GROUP},
    { MSG_MODULE_NET,   MSG_NET_SUB_REMOVE_EMAIL_FROM_GROUP},
    { MSG_MODULE_NET,   MSG_NET_SUB_REMOVE_EMAIL_GROUP},
    { MSG_MODULE_NET,   MSG_NET_SUB_ADD_FTP_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_FTP_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_FTP_ADDR_LIST},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_FTP_ADDR_NUM},
    { MSG_MODULE_NET,   MSG_NET_SUB_IS_FTP_ADDR_FULL},
    { MSG_MODULE_NET,   MSG_NET_SUB_MODIFY_FTP_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_REMOVE_FTP_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_ADD_SMB_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_SMB_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_SMB_ADDR_LIST},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_SMB_ADDR_NUM},
    { MSG_MODULE_NET,   MSG_NET_SUB_IS_SMB_ADDR_FULL},
    { MSG_MODULE_NET,   MSG_NET_SUB_MODIFY_SMB_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_REMOVE_SMB_ADDR},
    //{ MSG_MODULE_CAPABILITIESLIST,   MSG_CAPABILITIESLIST_SUB_GET_LEN},
    //{ MSG_MODULE_CAPABILITIESLIST,   MSG_CAPABILITIESLIST_SUB_GET_LIST},
    { MSG_MODULE_PLATFORM,   MSG_CAPABILITIESLIST_SUB_SUPPORT_PAPER_SIZE_LIST},
    { MSG_MODULE_PLATFORM,   MSG_CAPABILITIESLIST_SUB_SUPPORT_MEDIA_TYPE_LIST},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE},
    { MSG_MODULE_NET,   MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK},
    { MSG_MODULE_NET,   MSG_NET_SUB_SET_WIRED_NET_IPV4_MASK},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY},
    { MSG_MODULE_NET,   MSG_NET_SUB_SET_WIRED_NET_IPV4_GATEWAY},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO},
    { MSG_MODULE_NET,   MSG_NET_SUB_GET_PRODUCT_UUID},
    { MSG_MODULE_NET,   MSG_NET_SUB_SSL_INSTALL_CERT},
    { MSG_MODULE_NET,   MSG_NET_SUB_HTTPS_SET_CONFIG_PARAM},
    { MSG_MODULE_NET,   MSG_NET_SUB_HTTPS_GET_CONFIG_PARAM},
    { MSG_MODULE_NET,   MSG_SETTING_SUB_GET_SCAN_HTTP_PARAMS },
    { MSG_MODULE_NET,   MSG_SETTING_SUB_SET_SCAN_HTTP_PARAMS },
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LEN},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LIST},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_DEVICEINFO_GET_DCFWVERSION},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_DEVICEINFO_GET_ECFWVERSION},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA1VERSION},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA2VERSION},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTNAME},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTSN},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTPOSITION},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_PRODUCTINFO_GET_LANGUAGE},
    { MSG_MODULE_PLATFORM,   MSG_PLATFORM_SUB_PRODUCTINFO_SET_LANGUAGE},

    { MSG_MODULE_JOBCTL,   MSG_JOBCTL_SUB },
    { MSG_MODULE_JOBCTL,   MSG_JOBCTL_SUB_CANCEL },
    { MSG_MODULE_JOBCTL,   MSG_JOBCTL_SUB_APPLYID },
    { MSG_MODULE_JOBCTL,   MSG_JOBCTL_SUB_HISTORY_LIST },
    { MSG_MODULE_JOBCTL,   MSG_JOBCTL_SUB_HISTORY_LAST },
    { MSG_MODULE_JOBCTL,   MSG_JOBCTL_SUB_HISTORY_CLEAR },
    
    { MSG_MODULE_TOTAL_PAGES,   MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_COLOR },
    { MSG_MODULE_TOTAL_PAGES,   MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MONO },
    { MSG_MODULE_TOTAL_PAGES,   MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MEDIA_SIZE },
    { MSG_MODULE_TOTAL_PAGES,   MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_SHEETS },
    { MSG_MODULE_NET,   MSG_NET_SUB_GEN_CERT },
    { MSG_MODULE_NET,   MSG_NET_SUB_SSL_CREATE_CONN },
    { MSG_MODULE_NET,   MSG_NET_SUB_SSL_LOAD_CERT },
    { MSG_MODULE_COPY,   MSG_COPY_SUB_ADF_COPY_PAGE_NUM },
    { MSG_MODULE_COPY,   MSG_COPY_SUB_FB_COPY_PAGE_NUM  },
    { MSG_MODULE_SCAN,   MSG_SCAN_SUB_SENT_FB_PAGE_NUM },
    { MSG_MODULE_SCAN,   MSG_SCAN_SUB_SENT_ADF_PAGE_NUM  },
    { MSG_MODULE_SCAN,   MSG_SCAN_SUB_JOB_APP  },
    { MSG_MODULE_PANEL,   MSG_PANEL_SUB_GET_BACK_TIMEOUT  },
    { MSG_MODULE_PRINT,   MSG_PRINT_SUB_JOB_STATE },
    { MSG_MODULE_PRINT,   MSG_PRINT_SUB_JOB_ID },
    { MSG_MODULE_PRINT,   MSG_PRINT_SUB_JOB_START },

};



void SetJSContext(JSContext* ctx)
{
	if(ctx != NULL)
		g_ctx = ctx;
}


/*err:-1; success:0*/
int dispatchEvent(MAIN_MSG main, SUB_MSG sub, int respond, unsigned char *recvBuf, int recvBufSize)
{
	if(g_ctx == NULL){
        return -1;
       }

	JSValueConst argv[] = {		JS_NewInt32(g_ctx, main), JS_NewInt32(g_ctx, sub), JS_NewInt32(g_ctx, respond), \
								JS_NewInt32(g_ctx, recvBufSize), JS_NewString(g_ctx, recvBuf) };
	send_to_bridge(g_ctx, sizeof(argv)/sizeof(argv[0]), argv);
    return 0;
}

void init_queue()
{
	m_queue.tail = NULL;
	m_queue.head = NULL;
	m_queue.size = 0;

    /*链表*/
    msgListHead.msg = NULL;
    msgListHead.next = NULL;
}

void destory_queue()
{
	if(m_queue.size == 0)
		return;

	struct MessageQueueNode* node = m_queue.head;
	struct MessageQueueNode* next_node;

	while(node != NULL){

		next_node = node->next;

		if(node->msg != NULL)
		{
			free(node->msg);
		}

		free(node);

		node = next_node;
	}
	m_queue.size = 0;

    /*链表*/
    struct strListMsg* head = &msgListHead;
    while(1)
    {
        struct strListMsg* node = head->next;
        if(node == NULL)
        {
            break;
        }
        else
        {
            head->next = node->next;
            free(node);
            continue;
        }
    }
}

/*成功返回结构体指针，失败返回null*/
struct strSocketProtocol* packetProtocolStruct(MAIN_MSG main, SUB_MSG sub, int respond, int recvBufSize, unsigned char *recvBuf, int direction )
{
	struct strSocketProtocol *package = NULL;
	package = (struct strSocketProtocol*) malloc(sizeof(struct strSocketProtocol)+sizeof(struct strSocketMsg)+recvBufSize);
	if(package == NULL)
	{
		printf("Error:%s malloc failed\n",__func__);
		return NULL;
	}
    package->cmd = direction?CMD_APP_TO_PRINTER:CMD_PRINTER_TO_APP;
    unsigned short len = sizeof(struct strSocketMsg)+recvBufSize;
    package->lengthH = (len & 0xFF00) >> 8;
    package->lengthL = len & 0xFF;

    struct strSocketMsg *msg = (struct strSocketMsg *)package->data;
    msg->rtid = 0x01;//合入多app后修改
	msg->main = main;
	msg->sub = sub;
	msg->respond = respond;
	msg->size = recvBufSize;
	if(recvBuf){
		memcpy(msg->data, recvBuf, recvBufSize);
	}

    //memcpy(package->data,msg,len);

	return package;
}

/*成功返回结构体指针，失败返回null*/
struct strSocketProtocol* packetProtocolCmdStruct(int cmd, unsigned char* buf, unsigned short bufSize)
{
	struct strSocketProtocol *package = NULL;
    package = (struct strSocketProtocol*) malloc(sizeof(struct strSocketProtocol)+bufSize);
	if(package == NULL)
	{
		printf("Error:%s malloc failed\n",__func__);
		return NULL;
	}
    package->cmd = cmd;
    unsigned short len = bufSize;
    package->lengthH = (len & 0xFF00) >> 8;
    package->lengthL = len & 0xFF;


	if(bufSize >0 && buf != NULL){
		memcpy(package->data, buf, len);
	}

	return package;
}
/*******************************
**链表控制
**********************************/

int pushList(struct strSocketProtocol* msg)
{
    struct strListMsg* NewNode = (struct strListMsg*)malloc(sizeof(struct strListMsg));
    if(NewNode == NULL)
    {
        return -1;
    }
    NewNode->msg = msg;
    NewNode->next = NULL;

    //找到链表尾
    pthread_mutex_lock(&m_list_mutex);
    struct strListMsg* node = &msgListHead;
    while(node->next != NULL)
    {
        node = node->next;
    }
    node->next = NewNode;
    pthread_mutex_unlock(&m_list_mutex);
    return 0;
}

struct strSocketProtocol* popListHead()
{
    struct strSocketProtocol*msg = NULL;
    pthread_mutex_lock(&m_list_mutex);
    struct strListMsg*head = &msgListHead;
    struct strListMsg*node = msgListHead.next;
    if(node != NULL)
    {
        msg = node->msg;
        head->next = node->next;
        free(node);
    }
    pthread_mutex_unlock(&m_list_mutex);

    return msg;
}

/*******************************
**链表控制
**********************************/


/*******************************
**队列控制
**********************************/
int push(struct strSocketMsg* msg) //头插
{
	if(msg == NULL)
	{
		printf("Error:%s struct empty\n",__func__);
		return -1;
	}
	struct MessageQueueNode* node = (struct MessageQueueNode*)malloc(sizeof(struct MessageQueueNode));
	if(node == NULL)
	{
		printf("Error:%s malloc failed\n",__func__);
		return -1;
	}
	node->msg = msg;
	node->next = NULL;
	pthread_mutex_lock(&m_queue_mutex);
	if(m_queue.tail == NULL || m_queue.head == NULL)//第一次
	{
		m_queue.head = node;
		m_queue.tail = node;
	}
	else
	{
		m_queue.tail->next = node;
		m_queue.tail = node;
	}
	m_queue.size++;
	pthread_mutex_unlock(&m_queue_mutex);
	return 0;
}

int pop_sync_msg(MAIN_MSG main, SUB_MSG sub, int *respond, unsigned char *recvBuf, int* recvBufSize)
{

	pthread_mutex_lock(&m_queue_mutex);
	int ret = -1;
	struct MessageQueueNode *pre_node,*node,*next_node;
	pre_node = node = m_queue.head;
	if(node == NULL)
	{
		printf("m_queue.head = null\n");
		pthread_mutex_unlock(&m_queue_mutex);
		return -1;
	}
	next_node = node->next;
	for(node=m_queue.head; (m_queue.size > 0) && (node != NULL); )//头遍历
	{
		if(node->msg->main == main && node->msg->sub == sub)//
		{
			//删除节点
			if(node == m_queue.head)//头结点
			{
				m_queue.head = next_node;
			}
			else //非头结点
			{
				pre_node->next = next_node;
				if(m_queue.tail == node)//尾节点
				{
					m_queue.tail = pre_node;
				}
				else//中间节点
				{
					m_queue.tail = next_node;
				}
			}
			//Get RecvBuf
			if(respond != NULL){
				*respond = node->msg->respond;
			}
			if(recvBufSize != NULL){
				*recvBufSize = *recvBufSize>node->msg->size?node->msg->size:*recvBufSize;
			}
			if(recvBuf != NULL){
				memcpy( recvBuf, &node->msg->data, *recvBufSize);
			}
			free(node->msg);
			free(node);

			m_queue.size--;
			ret = 0;
			node = next_node;
			if(node != NULL)
				next_node = node->next;
			break;
		}
		if(node != m_queue.head)
			pre_node = node;
		node = next_node;
		if(node != NULL && m_queue.size > 0)
			next_node = node->next;
	}
	pthread_mutex_unlock(&m_queue_mutex);

	return ret;
}
int pop_async_msg()
{
	pthread_mutex_lock(&m_queue_mutex);
	struct MessageQueueNode* pre_node,*node,*next_node;

	int arrSize = sizeof(syncArr) / sizeof(syncArr[0]);
	int i;
	pre_node = node = m_queue.head;
	if(node == NULL)
	{
		pthread_mutex_unlock(&m_queue_mutex);
		return -1;
	}
	next_node = node->next;
	for(node=m_queue.head;(m_queue.size > 0) && (node != NULL);)
	{
		int isSyncMsg = 0;
		for(i=0; i<arrSize; i++)
		{
			if(node->msg->main == syncArr[i].main && node->msg->sub == syncArr[i].sub)//同步接收的模块消息
			{
				isSyncMsg = 1;
				//printf("find sync msg,module[%d,%d]\n",node->msg->main,node->msg->sub);
				break;
			}
		}
		if(isSyncMsg == 0)//分发异步消息
		{
			//printf("find async msg,module[%d,%d]\n",node->msg->main,node->msg->sub);
			unsigned char recvBuf[RECV_SIZE]={0};
			memcpy( recvBuf, &node->msg->data, node->msg->size);
			dispatchEvent(node->msg->main, node->msg->sub, node->msg->respond, recvBuf, node->msg->size);
			printf("dispatchEvent module[%d] success\n",node->msg->main);
			free(node->msg);
			free(node);

			m_queue.size--;
			printf("queue size is %d\n",m_queue.size);
			//删除头节点
			if(node == m_queue.head)
			{
				m_queue.head = pre_node = node = next_node;
			}
			else//删除非头节点
			{
				pre_node->next = next_node;
				if(m_queue.tail == node)//尾节点
				{
					m_queue.tail = pre_node;
				}
				else//中间节点
				{
					m_queue.tail = next_node;
				}
				//下次遍历
				node = next_node;
			}
			if(node == NULL)
			{
				break;
			}
			else
			{
				next_node = node->next;
				continue;
			}

		}

		if(node != m_queue.head)
			pre_node = node;
		node = next_node;
		if(node != NULL && m_queue.size > 0)
			next_node = node->next;
	}



	pthread_mutex_unlock(&m_queue_mutex);
	return 0;
}

/*******************************
**队列控制
**********************************/

void error_handing(char* message)
{
    fputs(message, stderr);
    fputc('\n', stderr);
	destory_queue();
    exit(1);
}


void client_thread_init()
{

    init_queue();
    pthread_mutex_init(&m_list_mutex, NULL);
    pthread_mutex_init(&mutex, NULL);
    pthread_mutex_init(&m_queue_mutex, NULL);
    pthread_create(&client_tid, NULL, client_thread, NULL);//接收线程
    pthread_create(&dispatch_tid, NULL, dispatch_thread, NULL);//分发事件线程

}

void transport_init_for_runtime()
{
    client_thread_init();
}

void transport_release_for_runtime()
{
    pthread_cancel(client_tid);
    pthread_cancel(dispatch_tid);
    pthread_join(dispatch_tid, NULL);
    pthread_join(client_tid, NULL);
    destory_queue();
}

void* dispatch_thread(void* arg)//分发异步消息
{

	while(1)
	{
		//发送队列里面的异步消息
		if(m_queue.size > 0 && m_queue.head != NULL)
		{
			//printf("pop_async msg start,queue size:%d\n",m_queue.size);
			int ret = pop_async_msg();
			//printf("pop_async msg end,result:%d\n",ret);

		}
		usleep(1000000);//稍微站一下就行
	}
}

void* client_thread(void* arg)
{
	printf("client thread start\n");
    int sock;
    struct sockaddr_in serv_addr;
    //char message[30];
    int str_len;
 	int ret = -1;
	char buf[RECV_SIZE] = {0};

    sock = socket(AF_INET, SOCK_STREAM, 0);
    if(sock == -1)
    {
        error_handing("socket() error");
    }
	printf("client socket create\n");
    bzero(&serv_addr, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_addr.s_addr = inet_addr(SERVER_ADDR);
    serv_addr.sin_port = htons(SERVER_PORT);

    if (connect(sock, (struct sockaddr*)&serv_addr, sizeof(serv_addr)) == -1)
    {
        error_handing("connect() error");
    }
	printf("client connect\n");
	fcntl(sock, F_SETFL, SOCK_NONBLOCK);
	while(1)
	{
		struct strSocketProtocol recvStruct;
		memset(&recvStruct, 0, sizeof(struct strSocketProtocol));
		if(clientWriteFlag && sendInfo != NULL)
		{
			if(write(sock, sendInfo, sizeof(struct strSocketProtocol)+(((sendInfo->lengthH << 8) & 0xFF00) | (sendInfo->lengthL & 0xFF))) > 0)
			{
				printf("Client send MSG success\n");
				pthread_mutex_lock(&mutex);
				clientWriteFlag = 0;//加锁解锁
				free(sendInfo);
				sendInfo=NULL;
				pthread_mutex_unlock(&mutex);
				printf("Client send MSG free\n");
			}
		}
		int reamin_len = read(sock, &recvStruct, sizeof(struct strSocketProtocol));

		if(reamin_len > 0 && reamin_len == sizeof(struct strSocketProtocol)) //parser
		{
		    int msgLen = ((recvStruct.lengthH << 8) & 0xFF00) | (recvStruct.lengthL & 0xFF);
		    //区分命令
		    switch(recvStruct.cmd)
            {
                case CMD_PING://ping
                case CMD_APP_START://Printer to Runtime
                case CMD_APP_END://Printer to Runtime
                case CMD_APP_PAUSE://Printer to Runtime
                case CMD_APP_CONTINUE://Printer to Runtime
                case CMD_PRINTER_TO_APP://Printer to Runtime
                    {
                        printf("client recv cmd:0x%x\n",recvStruct.cmd);

                        struct strSocketProtocol *package = (struct strSocketProtocol*) malloc(sizeof(struct strSocketProtocol)+msgLen);
                        if(package == NULL)
                        {
                            error_handing("malloc() error");
                        }
                        memcpy(package, &recvStruct, sizeof(struct strSocketProtocol));
                        if(msgLen > 0 && read(sock, package->data, msgLen) != msgLen)
                        {
                            error_handing("struct data recv error");
                        }
                        /*下面操作是为了保证RecvMsgToMfp可以使用*/
                        int isSync = 0;
                        /*区分是否是同步消息*/
                        if(recvStruct.cmd == CMD_PRINTER_TO_APP)
                        {
                            struct strSocketMsg *recvInfo = (struct strSocketMsg *)package->data;
                            int arrSize = sizeof(syncArr) / sizeof(syncArr[0]);
                            int i;
                            for(i=0; i<arrSize; i++)
                            {
                                if(recvInfo->main == syncArr[i].main && recvInfo->sub == syncArr[i].sub)//同步接收的模块消息
                                {
                                    isSync = 1;
                                    break;
                                }
                            }
                        }
                        if(isSync == 1)
                        {
                            struct strSocketMsg *recvInfo = (struct strSocketMsg *)malloc(msgLen);
                            if(msgLen > 0)
                                memcpy(recvInfo, package->data, msgLen);
                            free(package);
                            if(push(recvInfo) != 0)
                            {
                                error_handing("PushList error");
                            }
                        }
                        else
                        {
                            if(pushList(package) != 0)
                            {
                                error_handing("PushList error");
                            }
                        }
                    }
                    break;
                /* 被弃用的数据结构,消息队列,反馈的消息存放到链表,通过transport_receive_for_runtime读取
                {
                    //printf("client recv message\n");
                    //组合成一个完整的包
                    int msgLen = ((recvStruct.lengthH << 8) & 0xFF00) | (recvStruct.lengthL & 0xFF);

                    struct strSocketMsg *recvInfo = (struct strSocketMsg *)malloc(msgLen);
                    //printf("struct strSocketMsg:%d\n",sizeof(struct strSocketMsg));
                    if(recvInfo == NULL)
                    {
                        error_handing("malloc() error");
                    }
                    if(read(sock, recvInfo, msgLen) != msgLen)
                    {
                        error_handing("struct data recv error");
                    }
                    //将消息放入消息队列
                    ret = push(recvInfo);
                    printf("pesf recv message suceess:%d,module[%d,%d]\n",ret,recvInfo->main, recvInfo->sub);
                    if(ret == -1)
                    {
                        printf("push msg failed\n");
                        continue;
                    }
                }
                    break;
                */
                case CMD_GET_PROPERTY_LIST://Printer to Runtime
                    break;
                case CMD_GET_APP_PROPERTY://Printer to Runtime
                    break;

                case CMD_APP_END_RETURN://Runtime to Printer
                case CMD_APP_START_RETURN://Runtime to Printer
                case CMD_APP_PAUSE_RETURN://Runtime to Printer
                case CMD_APP_CONTINUE_RETURN://Runtime to Printer
                case CMD_APP_TO_PRINTER://Runtime to Printer
                case CMD_RETURN_PROPERTY_LIST://Runtime to Printer
                case CMD_RETURN_APP_PROPERTY://Runtime to Printer
                    break;
                default:
                    break;
            }
			//memcpy(recvInfo, &recvStruct, sizeof(struct strSocketMsg));




		}
		usleep(10000);//稍微站一下就行
	}

    close(sock);

}

/*return :0 is success, -1 is failed*/
int SendCmdToMfp(int cmd, unsigned char* buf, unsigned short size)
{
	int ret=-1;
	while(1)
	{
		if(clientWriteFlag == 0)
		{
			//加锁
			pthread_mutex_lock(&mutex);
			//封装
			sendInfo = packetProtocolCmdStruct(cmd, buf, size);
			clientWriteFlag = 1;
			//解锁
			pthread_mutex_unlock(&mutex);
			ret = 0;
			break;
		}
		usleep(10000);
	}
	return ret;
}


/*return :0 is success, -1 is failed*/
int SendMsgToMfp(MAIN_MSG main, SUB_MSG sub, int respond, int size, const unsigned char *param)
{
	int ret=-1;
	while(1)
	{
		if(clientWriteFlag == 0)
		{
			//加锁
			pthread_mutex_lock(&mutex);
			//封装
			sendInfo = packetProtocolStruct(main, sub, respond, size, (unsigned char *)param, 1);
			clientWriteFlag = 1;
			//解锁
			pthread_mutex_unlock(&mutex);
			ret = 0;
			break;
		}
		usleep(10000);
	}
	return ret;
}



/*return :0 is success, -1 is failed*/
int RecvMsgToMfp(MAIN_MSG main, SUB_MSG sub, int* respond, unsigned char *recvBuf, int *recvBufSize, int timeout)
{
	//gettime
	struct timespec start,end;
    clock_gettime(CLOCK_MONOTONIC, &start);
	//time_t first,second;
	//first = time(NULL);
	//printf("client Get first time\n");
	//从消息队列里面找消息
	do{

		if((m_queue.size > 0) && (0 ==pop_sync_msg(main, sub, respond, recvBuf, recvBufSize)))
		{
			//printf("recv data len is %d,data is %s\n", *recvBufSize, recvBuf);
			return 0;
		}
		clock_gettime(CLOCK_MONOTONIC, &end);
		usleep(10000);//稍微站一下就行
		//second = time(NULL);
	}while( (end.tv_sec - start.tv_sec) < timeout);
	printf("Client recv timeout\n");
	return -1;
}

int transport_send_for_runtime(const unsigned char *buffer, unsigned short length)
{
    if(buffer == NULL || length < 3)//length是buffer的长度，不是后面data的长度
    {
        return -1;
    }
    struct strSocketProtocol* sendMsg = (struct strSocketProtocol*)buffer;
    return SendCmdToMfp(sendMsg->cmd, sendMsg->data, length-3);
}
int transport_receive_for_runtime(unsigned char *buffer, unsigned short *length, int ms)
{
    if(buffer == NULL || *length < 0 || ms < 0)
    {
        return -1;
    }
    int outTimeFlag = 0;
    int outTime = 0;
    if(ms == 0){
        outTimeFlag = 1;
        outTime = 0;
    }
    else{
        outTimeFlag = 0;
        outTime = ms / 1000;
    }
    struct timespec start,end;
    clock_gettime(CLOCK_MONOTONIC, &start);
    do{
        struct strSocketProtocol* msg = popListHead();
        if(NULL != msg)
        {
            *length = ((msg->lengthH << 8) & 0xFF00) | (msg->lengthL & 0xFF) +sizeof(struct strSocketProtocol);
            memcpy(buffer, msg, *length);
            free(msg);
            return 0;
        }
        clock_gettime(CLOCK_MONOTONIC, &end);
        if(ms > 0){
            outTimeFlag = ((end.tv_sec - start.tv_sec) < outTime?1:0);
        }
        usleep(10000);//稍微站一下就行
    }while(outTimeFlag);
    return -1;
}
