/*
 *==========================================================================
 *
 *      crc.h
 *
 *      Interface for the CRC algorithms.
 *
 *==========================================================================
 * SPDX-License-Identifier:	eCos-2.0
 *==========================================================================
 *#####DESCRIPTIONBEGIN####
 *
 * Author(s):    Andrew Lunn
 * Contributors: Andrew Lunn
 * Date:         2002-08-06
 * Purpose:
 * Description:
 *
 * This code is part of eCos (tm).
 *
 *####DESCRIPTIONEND####
 *
 *==========================================================================
 */

#ifndef _PLATFORM_CRC_H_
#define _PLATFORM_CRC_H_

#include <stdint.h>

/* 16 bit CRC with polynomial x^16+x^12+x^5+1 (CRC-CCITT) */

uint16_t platform_crc16_ccitt(uint16_t crc_start, unsigned char *s, int len);

#endif /* _PLATFORM_CRC_H_ */
