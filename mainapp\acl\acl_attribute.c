#include "pol/pol_convert.h"
#include "pol/pol_list.h"
#include "acl/acl_attribute.h"
#include "utilities/parsercommon.h"
#include "pol/pol_endian.h"        //#include "pol/mtd_swab.h"
#include "pol/pol_string.h"
#include "pol/pol_log.h"
#include "utilities/secure_upgrade.h"

#define OK      0
#define FAILED  -1

/* These macro for AclGetPrinterAttributes */
//
#define ACL_PRINTER_ATTR_INPUT_MAX_LEN	4096
#define ACL_PRINTER_ATTR_OUTPUT_MAX_LEN	4096

#define SSI_TAG_START       "<!--#ssi"
#define SSI_TAG_END         "-->"
#define HTTP_MAX_SSI_TAG    256


#if 0
typedef struct _tag_uint32_value_register
{
    struct list_head    uint32_value_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_UINT32_VALUE    func_set;
    GET_UINT32_VALUE    func_get;
}ATTR_UINT32_VALUE_S;

typedef struct _tag_uint32_index_register
{
    struct list_head    uint32_index_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_UINT32_INDEX    func_set;
    GET_UINT32_INDEX    func_get;
}ATTR_UINT32_INDEX_S;

typedef struct _tag_string_value_register
{
    struct list_head    string_value_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_STRING_VALUE    func_set;
    GET_STRING_VALUE    func_get;
}ATTR_STRING_VALUE_S;

typedef struct _tag_string_index_register
{
    struct list_head    string_index_list;
    char                attr_key[MAX_STRING_KEY_LEN];
    SET_STRING_INDEX    func_set;
    GET_STRING_INDEX    func_get;
}ATTR_STRING_INDEX_S;

static ATTR_UINT32_VALUE_S s_uint32_value_list;
static ATTR_UINT32_INDEX_S s_uint32_index_list;
static ATTR_STRING_VALUE_S s_string_value_list;
static ATTR_STRING_INDEX_S s_string_index_list;

int32_t acl_attribute_uint32_value_register(char* attr_key,
        SET_UINT32_VALUE func_set,
        GET_UINT32_VALUE func_get)
{
    ATTR_UINT32_VALUE_S*  attribule_exist   = NULL;
    ATTR_UINT32_VALUE_S*  attribule_new     = NULL;

    /* the module has beed registered, need not to register again */
    struct list_head *pos, *n;
    pi_list_for_each_safe(pos, n, &s_uint32_value_list)
    {
        attribule_exist = pi_list_entry(pos, ATTR_UINT32_VALUE_S, uint32_value_list);
        if(NULL == attribule_exist)
        {
            pi_log_e("no exist attribute in the list\n");
            break;
        }

        if( 0 == pi_strncmp(attribule_exist->attr_key, attr_key, pi_strlen(attr_key))
                {
                pi_log_e("this key %s has been registered !\n", attr_key);
                return PARSER_SUCCESS;   //"parser already registered"
                }
                }

                /* a new module want to register the call back function */
                attribule_new = (ATTR_UINT32_VALUE_S*)pi_malloc(sizeof(ATTR_UINT32_VALUE_S));
                if (NULL == attribule_new)
                {
                pi_log_e("memory malloc error!\n");
                return PARSER_ERROR; //"memory malloc error"
                }
                pi_memset(attribule_new, 0x00, sizeof(ATTR_UINT32_VALUE_S));

                /* the registering the module information to he registerd list */
                pi_strncpy(attribule_new->attr_key, attr_key, pi_strlen(attr_key));
                attribule_new->func_set = func_set;
                attribule_new->func_get = func_get;

                pi_list_add_tail(&(attribule_new->uint32_value_list), &s_uint32_value_list);
                pi_log_d( "Added key register %s to acl attribute\n", attribule_new->attr_key);

                return PARSER_SUCCESS;
}


int32_t acl_attribute_uint32_index_register(char* key_str,
        SET_UINT32_INDEX func_set,
        GET_UINT32_INDEX func_get);


int32_t acl_attribute_string_value_register(char* key_str,
        SET_STRING_VALUE func_set,
        GET_STRING_VALUE func_get);

int32_t acl_attribute_string_index_register(char* key_str,
        SET_STRING_INDEX func_set,
        GET_STRING_INDEX func_get);

#endif

#if 0

#define     ATTR_TYPE_NULL               (uint8_t)0
#define     ATTR_TYPE_UINT32_VALUE       (uint8_t)1
#define     ATTR_TYPE_UINT32_INDEX       (uint8_t)2
#define     ATTR_TYPE_STRING_VALUE       (uint8_t)1
#define     ATTR_TYPE_STRING_INDEX       (uint8_t)2

typedef struct acl_attr_value
{
    char*       attr_key;
    uint32_t    attr_idx;
    uint32_t    attr_type;
    void*       func_set;
    void*       func_get;
    char*       value;
    char        value_old[MAX_STRING_VALUE_LEN];
}ACL_ATTR_VALUE_S;


#define DEFINE_ATTR_STRC(attrtype, ATTRTYPE)                                            \
    typedef struct _tag_##attrtype_register                                                 \
{                                                                                       \
    struct list_head    ##attrtype_list;                                                \
    char                attr_key[MAX_STRING_KEY_LEN];                                   \
    SET_##ATTRTYPE      func_set;                                                       \
    GET_##ATTRTYPE      func_get;                                                       \
}ATTR_##ATTRTYPE_S;                                                                     \
static ATTR_##ATTRTYPE_S    s_##attrtype_list;



#define REGISTER_ATTR_FUNC(attrtype, ATTRTYPE)                                          \
    int32_t acl_attribute_##attrtype_register(char* attr_key,                               \
            SET_##ATTRTYPE func_set,                    \
            GET_##ATTRTYPE func_get)                    \
{                                                                                       \
    ATTR_##ATTRTYPE_S*  attribule_exist   = NULL;                                       \
    ATTR_##ATTRTYPE_S*  attribule_new     = NULL;                                       \
    struct list_head *pos, *n;                                                          \
    pi_list_for_each_safe(pos, n, &s_##attrtype_list)                                   \
    {                                                                                   \
        attribule_exist = pi_list_entry(pos, ATTR_##ATTRTYPE_S, ##attrtype_list);       \
        if(NULL == attribule_exist)                                                     \
        {                                                                               \
            break;                                                                      \
        }                                                                               \
        if( 0 == pi_strncmp(attribule_exist->attr_key, attr_key, pi_strlen(attr_key))   \
                {                                                                               \
                return PARSER_SUCCESS;                                                      \
                }                                                                               \
                }                                                                                   \                                                            \
                attribule_new = (ATTR_##ATTRTYPE_S*)pi_malloc(sizeof(ATTR_##ATTRTYPE_S));           \
                if (NULL == attribule_new)                                                          \
                {                                                                                   \
                return PARSER_ERROR;                                                            \
                }                                                                                   \
                pi_memset(attribule_new, 0x00, sizeof(ATTR_##ATTRTYPE_S));                          \
                pi_strncpy(attribule_new->attr_key, attr_key, pi_strlen(attr_key));                 \
                attribule_new->func_set = func_set;                                                 \
                attribule_new->func_get = func_get;                                                 \
                pi_list_add_tail(&(attribule_new->##attrtype_list), &s_##attrtype_list);            \
                return PARSER_SUCCESS;                                                              \
                }

                DEFINE_ATTR_STRC(uint32_value, UINT32_VALUE);
                DEFINE_ATTR_STRC(uint32_index, UINT32_INDEX);
                DEFINE_ATTR_STRC(string_value, STRING_VALUE);
        DEFINE_ATTR_STRC(string_index, STRING_INDEX);

        REGISTER_ATTR_FUNC(uint32_value, UINT32_VALUE);
        REGISTER_ATTR_FUNC(uint32_index, UINT32_INDEX);
        REGISTER_ATTR_FUNC(string_value, STRING_VALUE);
        REGISTER_ATTR_FUNC(string_index, STRING_INDEX);

static int32_t acl_attribute_get_callback(char* attr_key, ACL_ATTR_VALUE_S* attr_info)
{
    ACL_ATTR_VALUE_S*  attr_exist        = NULL;

    /* find the reffered key */
    struct list_head *pos, *n;
    pi_list_for_each_safe(pos, n, &s_uint32_value_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_UINT32_VALUE_S, uint32_value_list);

        if(0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)))
        {
            attr_info->attr_key     = attr_exist->attr_key;
            attr_info->attr_idx     = attr_exist->attr_idx
                attr_info->attr_type    = ATTR_TYPE_UINT32_VALUE;
            attr_info->func_set     = attr_exist->func_set;
            attr_info->func_get     = attr_exist->func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_list_for_each_safe(pos, n, &s_uint32_index_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_UINT32_INDEX_S, uint32_index_list);

        if(0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)))
        {
            attr_info->attr_key     = attr_exist->attr_key;
            attr_info->attr_idx     = attr_exist->attr_idx
                attr_info->attr_type    = ATTR_TYPE_UINT32_INDEX;
            attr_info->func_set     = attr_exist->func_set;
            attr_info->func_get     = attr_exist->func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_list_for_each_safe(pos, n, &s_string_value_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_STRING_VALUE_S, string_value_list);

        if(0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)))
        {
            attr_info->attr_key     = attr_exist->attr_key;
            attr_info->attr_idx     = attr_exist->attr_idx
                attr_info->attr_type    = ATTR_TYPE_STRING_VALUE;
            attr_info->func_set     = attr_exist->func_set;
            attr_info->func_get     = attr_exist->func_get;

            return PARSER_SUCCESS;
        }
    }

    pi_list_for_each_safe(pos, n, &s_string_index_list)
    {
        attr_exist = pi_list_entry(pos, ATTR_STRING_INDEX_S, string_index_list);

        if(0 == pi_strncmp(attr_exist->attr_key, attr_key, pi_strlen(attr_key)))
        {
            attr_info->attr_key     = attr_exist->attr_key;
            attr_info->attr_idx     = attr_exist->attr_idx
                attr_info->attr_type    = ATTR_TYPE_STRING_INDEX;
            attr_info->func_set     = attr_exist->func_set;
            attr_info->func_get     = attr_exist->func_get;

            return PARSER_SUCCESS;
        }
    }

    return PARSER_ERROR;
}
#endif

/******************************************************************************
 * Function Name: acl_buffer_to_attribute
 *
 * Created by: oujian 20220402
 *
 * \param[in]  @input_string: A pointer to data buffer
 *   which is format of "key1=value1&key2=value2...".
 *
 * \param[out] @out_attr_values: key/value pairs will stored here if success.
 *
 * \param[out] @num: number of key/value pairs.
 *
 * \return PARSER_SUCCESS/PARSER_ERROR
 *
 * Description: parse the @input_string and
 *  save the key/value pairs in @out_attr_values.
 *
 ******************************************************************************/
static uint32_t acl_buffer_to_attribute(char* input_string, ACL_ATTR_VALUE_S *out_attr_values, uint32_t max_param_num)
{
    int     param_num = 0;      /*param number from parsing the input string  format: key1=value1&key2=value2...*/
    char*   key = NULL;
    char*   value = NULL;
    char*   index_str = NULL;

    ACL_ATTR_VALUE_S*    current = NULL;

    /*int put param checking*/
    char    *pu = input_string;
    if( NULL == pu )
    {
        pi_log_e("The para pointer is null\n");
        return PARSER_SUCCESS;
    }

    while (*pu)
    {
        key = pu;
        value = pu;
        while (*value && *value != '=')
        {
            value++;
        }

        /*find the charactor = to point to the value of current param*/
        if (*value == '=')
        {
            *value++ = '\0';
        }
        else
        {
            pi_log_e("no '=' found in key/value data buffer\n");
            return 0;
        }

        pu = value;
        while (*pu && *pu != '&')
        {
            pu++;
        }

        /*find the charactor & to point to next param*/
        if (*pu == '&')
        {
            *pu++ = '\0';
        }

        //save the current key value to the out_key_values
        current = &out_attr_values[param_num++];
        current->attr_key = key;
        current->value = value;

        /*split the key to param string and param index*/
        if(NULL != (index_str = strstr(key, ".")))
        {
            *index_str++ = '\0';
            current->attr_idx = (uint32_t)strtol(index_str, NULL, 10) ;
            pi_log_d( "Set %s.%d value to %s\n", current->attr_key, current->attr_idx, value);
        }
        else
        {
            current->attr_idx = 0;
            pi_log_d( "Set %s value to %s\n", current->attr_key, value);
        }

        if(PARSER_SUCCESS == acl_attribute_get_callback(current->attr_key, current))
        {
            pi_log_d( "get the current oid <%s.%d> info: type<%d> func<%p><%p>\n", current->attr_key,
                    current->attr_idx,
                    current->attr_type,
                    current->func_set,
                    current->func_get);
        }
        else
        {
            pi_log_e("ERROR key %s is not detected in register list\n", key);
            current->attr_type  = ATTR_TYPE_NULL;
            current->func_set   = NULL;
            current->func_get   = NULL;
        }

        if(param_num >= max_param_num)
        {
            pi_log_e("can't store more key/value pairs\n");
            break;
        }
    }

    return param_num;
}

static int acl_get_attribute_value(char *output, ACL_ATTR_VALUE_S *attr_value)
{
    ParserStatus_E status = PARSER_SUCCESS;
    uint32_t value_int = 0;

    if((NULL == attr_value) || (NULL == output))
    {
        pi_log_e("a NULL attr value!\n");
        return PARSER_ERROR;
    }

    if(ATTR_TYPE_UINT32_VALUE == attr_value->attr_type)
    {
        if(NULL != attr_value->func_get)
        {
            GET_UINT32_VALUE acl_func_get = (GET_UINT32_VALUE)(attr_value->func_get);
            if(acl_func_get(&value_int) < 0)
            {
                pi_log_e("Get <%s> value failed!\n", attr_value->attr_key);
                status = PARSER_ERROR;
            }
            else
            {
                (void)pi_snprintf(  output,
                        MAX_STRING_VALUE_LEN,
                        "%u",
                        value_int );
                pi_log_d( "Get <%s> value is %s\n", attr_value->attr_key, output);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_SUCCESS;
        }
    }
    else if(ATTR_TYPE_UINT32_INDEX == attr_value->attr_type)
    {
        if(NULL != attr_value->func_get)
        {
            GET_UINT32_INDEX acl_func_get = (GET_UINT32_INDEX)(attr_value->func_get);
            if(acl_func_get(attr_value->attr_idx, &value_int) < 0)
            {
                pi_log_e("Get <%s.%d> value failed!\n", attr_value->attr_key, attr_value->attr_idx);
                status = PARSER_ERROR;
            }
            else
            {
                (void)pi_snprintf(  output,
                        MAX_STRING_VALUE_LEN,
                        "%u",
                        value_int );
                pi_log_d( "Get <%s.%d> value is %s\n", attr_value->attr_key, attr_value->attr_idx, output);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_SUCCESS;
        }
    }
    else if(ATTR_TYPE_STRING_VALUE == attr_value->attr_type)
    {
        if(NULL != attr_value->func_get)
        {
            GET_STRING_VALUE acl_func_get = (GET_STRING_VALUE)(attr_value->func_get);
            if(acl_func_get(output, MAX_STRING_VALUE_LEN) < 0)
            {
                pi_log_e("Get <%s> value failed!\n", attr_value->attr_key);
                status = PARSER_ERROR;
            }
            else
            {
                pi_log_d( "Get <%s> value is %s\n", attr_value->attr_key, output);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_SUCCESS;
        }
    }
    else if(ATTR_TYPE_STRING_INDEX == attr_value->attr_type)
    {
        if(NULL != attr_value->func_get)
        {
            GET_STRING_INDEX acl_func_get = (GET_STRING_INDEX)(attr_value->func_get);
            if(acl_func_get(attr_value->attr_idx, output, MAX_STRING_VALUE_LEN) < 0)
            {
                pi_log_e("Get <%s.%d> value failed!\n", attr_value->attr_key, attr_value->attr_idx);
                status = PARSER_ERROR;
            }
            else
            {
                pi_log_d( "Get <%s.%d> value is %s\n", attr_value->attr_key, attr_value->attr_idx, output);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_SUCCESS;
        }
    }
    else
    {
        pi_log_e("error attribute type %d\n", attr_value->attr_type);
        status = PARSER_ERROR;
    }

    return status;
}

/******************************************************************************
 * Function Name: acl_set_attribute_value
 *
 * Created by: oujian 20220402
 *
 * \param[in]  @attr_value: A pointer to reffered attribute info
 *
 * \return PARSER_SUCCESS/PARSER_ERROR
 *
 * Description: set the attribute value of the @attr_value
 *
 ******************************************************************************/
static int acl_set_attribute_value(ACL_ATTR_VALUE_S *attr_value)
{
    ParserStatus_E status = PARSER_SUCCESS;

    char*       value_setting;        /*the value of need to be set*/
    uint32_t    value_uint32;         /*string need to be trans to be uint32_t to be set*/

    if(NULL == attr_value)
    {
        pi_log_e("a NULL attr value!\n");
        return PARSER_ERROR;
    }

    value_setting = attr_value->value;
    pi_log_d( "attr<%s.%d>, attr_type<%d>, param_value_setting<%s>\n",
            attr_value->attr_key, attr_value->attr_idx, attr_value->attr_type, value_setting);

    if(ATTR_TYPE_UINT32_VALUE == attr_value->attr_type)
    {
        value_uint32 = (uint32_t)pi_strtoll(value_setting, NULL, 10);
        if(NULL != attr_value->func_set)
        {
            SET_UINT32_VALUE acl_func_set = (SET_UINT32_VALUE)(attr_value->func_set);
            if(acl_func_set(value_uint32) < 0)
            {
                pi_log_e("Set <%s> value to %d failed!\n", attr_value->attr_key, value_uint32);
                status = PARSER_ERROR;
            }
            else
            {

                pi_log_d( "Set <%s> value to %d OK!\n", attr_value->attr_key, value_uint32);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_ERROR;
        }
    }
    else if(ATTR_TYPE_UINT32_INDEX == attr_value->attr_type)
    {
        value_uint32 = (uint32_t)pi_strtol(value_setting, NULL, 10);

        if(NULL != attr_value->func_set)
        {
            SET_UINT32_INDEX acl_func_set = (SET_UINT32_INDEX)(attr_value->func_set);
            if(acl_func_set(attr_value->attr_idx, value_uint32) < 0)
            {
                pi_log_e("Set <%s.%d> value to %d failed!\n", attr_value->attr_key, attr_value->attr_idx, value_uint32);
                status = PARSER_ERROR;
            }
            else
            {

                pi_log_d( "Set <%s.%d> value to %d OK!\n", attr_value->attr_key, attr_value->attr_idx, value_uint32);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_ERROR;
        }
    }
    else if(ATTR_TYPE_STRING_VALUE == attr_value->attr_type)
    {
        if(NULL != attr_value->func_set)
        {
            SET_STRING_VALUE acl_func_set = (SET_STRING_VALUE)(attr_value->func_set);
            if(acl_func_set(value_setting, pi_strlen(value_setting)) < 0)
            {
                pi_log_e("Set <%s> value to %s failed!\n", attr_value->attr_key, value_setting);
                status = PARSER_ERROR;
            }
            else
            {

                pi_log_d( "Set <%s> value to %s OK!\n", attr_value->attr_key, value_setting);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_ERROR;
        }
    }
    else if(ATTR_TYPE_STRING_INDEX == attr_value->attr_type)
    {
        if(NULL != attr_value->func_set)
        {
            SET_STRING_INDEX acl_func_set = (SET_STRING_INDEX)(attr_value->func_set);
            if(acl_func_set(attr_value->attr_idx, value_setting, pi_strlen(value_setting)) < 0)
            {
                pi_log_e("Set <%s.%d> value to %s failed!\n", attr_value->attr_key, attr_value->attr_idx, value_setting);
                status = PARSER_ERROR;
            }
            else
            {

                pi_log_d( "Set <%s.%d> value to %s OK!\n", attr_value->attr_key, attr_value->attr_idx, value_setting);
                status = PARSER_SUCCESS;
            }
        }
        else
        {
            pi_log_e("a null callback for %s\n", attr_value->attr_key);
            status = PARSER_ERROR;
        }
    }
    else
    {
        pi_log_e("error attribute type %d\n", attr_value->attr_type);
        status = PARSER_ERROR;
    }

    return status;
}

/******************************************************************************
 * Function Name: oid_restore_from_old_values
 * Created by: tanxuan @2014/01/04
 *
 * \param[in]  oidKeyValues: A pointer to oid key/value pairs.
 *
 * \param[in]  num: number of key/value pairs.
 *
 * \return PARSER_SUCCESS/PARSER_ERROR
 *
 * Description: restore the oids from old value.
 *
 ******************************************************************************/
static int acl_restore_attribute_values_form_old(ACL_ATTR_VALUE_S *attr_value, uint32_t param_num)
{
    ParserStatus_E status = PARSER_SUCCESS;

    int i;
    uint32_t    value_uint32;
    char*       value_old;

    for(i = 0; i < param_num; i++)
    {
        ACL_ATTR_VALUE_S* attr_value_t = attr_value + i;
        if(NULL == attr_value_t)
        {
            pi_log_e("i <%d> is a NULL ATTR VALUE!\n", i);
            status = PARSER_ERROR;
            break;
        }

        value_old = attr_value_t->value_old;

        if(ATTR_TYPE_UINT32_VALUE == attr_value_t->attr_type)
        {
            /*get the old key value*/
            value_uint32 = (uint32_t)pi_strtol(value_old, NULL, 10) ;
            SET_UINT32_VALUE acl_func_set = (SET_UINT32_VALUE)(attr_value_t->func_set);
            if (acl_func_set != NULL) {
                acl_func_set(value_uint32);
                pi_log_d( "restore <%s> to old value %d\n", attr_value_t->attr_key, value_uint32);
            }
        }
        else if(ATTR_TYPE_UINT32_INDEX == attr_value_t->attr_type)
        {
            /*get the old key value*/
            value_uint32 = (uint32_t)pi_strtol(value_old, NULL, 10) ;
            SET_UINT32_INDEX acl_func_set = (SET_UINT32_INDEX)(attr_value_t->func_set);
            if (acl_func_set != NULL) {
                acl_func_set(attr_value_t->attr_idx, value_uint32);
                pi_log_d( "restore <%s.%d> to old value %d\n", attr_value_t->attr_key, attr_value_t->attr_idx, value_uint32);
            }
        }
        else if(ATTR_TYPE_STRING_VALUE == attr_value_t->attr_type)
        {
            SET_STRING_VALUE acl_func_set = (SET_STRING_VALUE)(attr_value_t->func_set);
            if (acl_func_set != NULL) {
                acl_func_set(value_old, pi_strlen(value_old));
                pi_log_d( "restore <%s> to old value %s\n", attr_value_t->attr_key, value_old);
            }
        }
        else if(ATTR_TYPE_STRING_INDEX == attr_value_t->attr_type)
        {
            SET_STRING_INDEX acl_func_set = (SET_STRING_INDEX)(attr_value_t->func_set);
            if (acl_func_set != NULL) {
                acl_func_set(attr_value_t->attr_idx, value_old, pi_strlen(value_old));
                pi_log_d( "restore <%s.%d> to old value %s\n", attr_value_t->attr_key, attr_value_t->attr_idx, value_old);
            }
        }
        else
        {
            pi_log_e("error attribute type %d\n", attr_value_t->attr_type);
            status = PARSER_ERROR;
            break;
        }
    }

    return status;
}

/******************************************************************************
 * Function Name: acl_get_attribute_values_to_old
 *
 * Created by: oujian 20220402
 *
 * \param[in]  @attr_value: A pointer to attribute list head
 *                          which include all the attributes information saved
 *
 * \param[out] @out_attr_values: key/value pairs will stored here if success.
 *
 * \param[out] @param_num: number of key/value pairs.
 *
 * \return PARSER_SUCCESS/PARSER_ERROR
 *
 * Description: get all the attributes old value to save to the attr_value's oid value
 *
 ******************************************************************************/
static int acl_get_attribute_values_to_old(ACL_ATTR_VALUE_S *attr_value, uint32_t param_num)
{
    ParserStatus_E status = PARSER_SUCCESS;

    int i;
    for( i = 0; i < param_num; i++)
    {
        ACL_ATTR_VALUE_S* attr_value_t = &(attr_value[i]);

        pi_memset(attr_value_t->value_old, 0, MAX_STRING_VALUE_LEN);

        status = acl_get_attribute_value(attr_value_t->value_old, attr_value_t);
        if(PARSER_SUCCESS != status)
        {
            pi_log_e("Get <%s.%d> old value Failed\n", attr_value_t->attr_key, attr_value_t->attr_idx);
            break;
        }
        else
        {
            pi_log_d( "Get <%s.%d> old value is %s\n", attr_value_t->attr_key, attr_value_t->attr_idx, attr_value_t->value_old);
        }
    }

    return status;
}

/******************************************************************************
 * Function Name: acl_set_attribute_value_all
 *
 * Created by: oujian 20220402
 *
 * \param[in]  @attr_value: A pointer to attribute list head
 *                          which include all the attributes information saved
 *
 * \param[out] @param_num: number of key/value pairs.
 *
 * \return PARSER_SUCCESS/PARSER_ERROR
 *
 * Description: set all the attributes value in the attribute list
 *
 ******************************************************************************/
static int acl_set_attribute_value_all(ACL_ATTR_VALUE_S *attr_value, uint32_t param_num)
{
    ParserStatus_E status = PARSER_SUCCESS;

    int i;
    //char*       value_setting;        /*the value of need to be set*/
    //uint32_t    value_uint32;         /*string need to be trans to be uint32_t to be set*/

    if( NULL == attr_value )
    {
        pi_log_e("The para pointer is null\n");
        return PARSER_SUCCESS;
    }

    if( 0 == param_num )
    {
        return PARSER_SUCCESS;
    }

    for(i = 0; i < param_num; i++)
    {
        ACL_ATTR_VALUE_S* attr_value_t = &(attr_value[i]);
        if(NULL == attr_value_t)
        {
            pi_log_e("i<%d> is a NULL ATTR VALUE!\n", i);
            status = PARSER_ERROR;
            break;
        }

        status = acl_set_attribute_value(attr_value_t);
        if(status != PARSER_SUCCESS)
        {
            pi_log_e("i<%d> set return failed\n");
            break;
        }
    }

    return status;
}


/* FUNCTION NAME: http_ssi_get_args*/

/**
 * \brief Get all the arg from format:
 *             <!--#ssi IDs strcode -->
 *             <!--#ssi IDr_op oid -->
 *             <!--#ssi IDc_op oid oper val output -->
 *             <!--#ssi IF_op oid oper val -->
 * \param[out] char** arg:Point to the content in SSI Tag.
 * \param[in]  char* tag :The SSI Tag.
 * \return void
 * \author:TanXuan
 **/
static void http_ssi_get_args(char** arg, char* tag)
{
    char* pCur = tag;
    char* tmpArg = NULL;
    //scan until find #ssi

    while ( *pCur != '#' && *pCur != 0 )
    {
        pCur ++;
    }

    //invalid tag return with no args
    if ( *pCur == 0 )
    {
        return;
    }
    //move index to right after the "ssi"
    pCur += 4;

    while ( *pCur != 0 )
    {
        //skip leading white spaces
        while ( (*pCur == ' ') && (*pCur != 0) )
        {
            pCur ++;
        }

        //done if hit end of string
        if ( *pCur == 0 )
        {
            pi_log_d( "Invalid Arg: %s\n", tag );
            return;
        }

        //skip the tag head like 'IDs'
        while ( (*pCur != ' ') && (*pCur != 0) )
        {
            pCur ++;
        }

        //done if hit end of string
        if ( *pCur == 0 )
        {
            pi_log_d( "Invalid Arg: %s\n", tag );
            return;
        }

        //skip the white spaces after the tag like 'IDs'
        while ( (*pCur == ' ') && (*pCur != 0) )
        {
            pCur ++;
        }

        //done if hit end of string
        if ( *pCur == 0 )
        {
            pi_log_d( "Invalid Arg: %s\n", tag );
            return;
        }

        //if we find a quotation mark, the arg is all the chars until the next quotation mark,
        //including white space
        if ( *pCur == '\"' )
        {
            //skip the quote
            pCur ++;
            tmpArg = pCur;

            while ( (*pCur != '\"') && (*pCur != 0) )
            {
                pCur ++;
            }
        }
        //find next white space
        else
        {
            //save beginning of arg
            tmpArg = pCur;
            pi_log_d( "SSItag: pCur = '%s'\n", pCur);
            while ( (*pCur != ' ') && (*pCur != 0) )
            {
                pCur ++;
            }
        }

        if ( *pCur != 0 )
        {
            //null terminate the tag and move on
            *pCur = 0;
            pCur ++;
            break;
        }
    }
    *arg = tmpArg;
}


/******************************************************************************
 * Function Name: http_ssi_tag_process
 * Created by: tanxuan 12/29/2014
 *
 * \param[out] output: A pointer to the data buffer that receives the
 * string data..
 *
 * \param[in]  tag: A pointer to the data buffer that this func to parse.
 *
 * Description: Parse the SSI tag
 *
 ******************************************************************************/
static ParserStatus_E http_ssi_tag_process (char *output, uint32_t max_outout_len, const char *tag)
{
    char* arg = NULL;

    char*           acl_str = NULL;
    ParserStatus_E  status = PARSER_SUCCESS;

    char        temp_tag[HTTP_MAX_SSI_TAG];
    uint64_t    acl_sub_index = 0;
    uint32_t    finded = 0;

    ACL_ATTR_VALUE_S  attr_value;
    pi_memset(&attr_value, 0, sizeof(ACL_ATTR_VALUE_S));

    pi_log_d( "Parse Tag: %s\n", tag );
    if (NULL == output || NULL == tag)
    {
        pi_log_e("Para pointer is NULL\n");
        return PARSER_ERROR;
    }

    //assert(pi_strlen(tag) < HTTP_MAX_SSI_TAG);        //20230411
    if(pi_strlen(tag) >= HTTP_MAX_SSI_TAG)
    {
        pi_log_e("Para len is too long more than HTTP_MAX_SSI_TAG \n");
        return PARSER_ERROR;
    }

    output[0] = '\0';
    pi_strncpy(temp_tag, tag, HTTP_MAX_SSI_TAG);

    //Kick off the ssi tag, get the OID string-formated
    http_ssi_get_args(&arg, (char*)temp_tag);

    acl_str = arg;
    if(acl_str)
    {
        char *dot_pos = NULL;
        char *acl_index_string = NULL;
        if(NULL != (dot_pos = pi_strstr(acl_str, ".")))
        {
            acl_index_string = dot_pos + 1;
            acl_sub_index = (uint64_t)atol(acl_index_string);
            *dot_pos = '\0';
        }

        pi_log_d( "get ssi tag <%s.%d>\n", acl_str, acl_sub_index);

        attr_value.attr_key = acl_str;
        attr_value.attr_idx = acl_sub_index;
        if(PARSER_SUCCESS == acl_attribute_get_callback(acl_str, &attr_value))
        {
            pi_log_d( "get the current attr <%s.%d> info: type<%d> func<%p><%p>\n", attr_value.attr_key,
                    attr_value.attr_idx,
                    attr_value.attr_type,
                    attr_value.func_set,
                    attr_value.func_get);
            finded = 1;
        }
        else
        {
            pi_log_e("can't find the register acl key %s\n", acl_str);
            return PARSER_ERROR;
        }
    }

    if(!finded)
    {
        pi_log_d( "SSItag: find no tag named '%s'\n", acl_str);
        status = PARSER_ERROR;
    }
    else
    {
        //Maplist node call callbackfunction
        pi_log_d( "SSItag: <%s.%d>\n", acl_str, acl_sub_index);
        status = acl_get_attribute_value(output, &attr_value);
        if(PARSER_SUCCESS != status)
        {
            pi_log_e("Get <%s.%d> value Failed\n", attr_value.attr_key, attr_value.attr_idx);
        }
        else
        {
            pi_log_d( "Get <%s.%d> value is %s\n", attr_value.attr_key, attr_value.attr_idx, output);
        }
    }

    return status;
}


/**
 * Function Prototypes
 **/

/******************************************************************************
 * Function Name: http_ssi_tag_exchange
 * Created by: tanxuan 07/02/2014
 *
 * \param[in] output A pointer to the data buffer that receives the
 * string data..
 *
 * \param[in] input A pointer to the data buffer that this func will replace
 *                  the SSI tags in it.
 *
 * \return int ParseStatus
 * Description: Parse the SSI tags in input buffer,
 *              and store the result in output buffer.
 * For example:
 <xml>
 <omHostName><!--#ssi IDr OID_NETWORK_HOST_NAME --></omHostName>
 <omFWVersion><!--#ssi IDr OID_PLATFORM_FW_VERSION --></omFWVersion>
 <omPrintCount><!--#ssi IDr OID_PRINT_TOTAL_PAGE_COUNT --></omPrintCount>
 <omSerialNum><!--#ssi IDr OID_PLATFORM_PRODUCT_SERIAL_NUM --></omSerialNum>
 <omPrintName><!--#ssi IDr OID_PLATFORM_PRINT_NAME_STRING --></omPrintName>
 <omMacAddr><!--#ssi IDr OID_NETWORK_MAC_ADDRESS --></omMacAddr>
 <omWifiStat><!--#ssi IDr OID_WIFI_INTF_ENABLED.1 --></omWifiStat>
 <omUapStat><!--#ssi IDr OID_WIFI_INTF_ENABLED.0 --></omUapStat>
 <omStaSSID><!--#ssi IDr OID_WIFI_INTF_SSID.0 --></omStaSSID>
 <omUapSSID><!--#ssi IDr OID_WIFI_INTF_SSID.1 --></omUapSSID>
 </xml>
 will translated to:
 <xml>
 <omHostName>Pantum-5551A1</omHostName>
 <omFWVersion>*******</omFWVersion>
 <omPrintCount>923</omPrintCount>
 <omSerialNum>AA2A000000</omSerialNum>
 <omPrintName>P2500W series</omPrintName>
 <omMacAddr>44:33:4c:55:51:a1</omMacAddr>
 <omWifiStat>0</omWifiStat>
 <omUapStat>1</omUapStat>
 <omStaSSID>Pantum-AP-1B2A0E</omStaSSID>
 <omUapSSID>5551A1</omUapSSID>
 </xml>
 *
 ******************************************************************************/
static ParserStatus_E http_ssi_tag_exchange(char *output, uint32_t out_max_len, const char *input)
{
    ParserStatus_E  ret_val = PARSER_SUCCESS;
    char*           file_buffer = NULL;
    uint32_t        pos_byte = 0, output_pos = 0;
    uint32_t        input_len = 0, rel_len = 0;
    char*           next_ssi_tag;
    char            temp_buff[MAX_STRING_VALUE_LEN];

    if(NULL == output || NULL == input)
    {
        pi_log_e("the para pointer is null\n");
        return PARSER_ERROR;
    }

    input_len = pi_strlen(input) + 1;

    do
    {
        file_buffer = (char *)input + pos_byte;
        //Log(5,"pos_byte === %d ,buff: %s\n", pos_byte, file_buffer);

        rel_len = input_len - pos_byte;

        // What's next? Page data or an SSI tag?
        next_ssi_tag = pi_strstr( file_buffer, SSI_TAG_START );
        if ( next_ssi_tag != file_buffer )
        {
            uint32_t bytes_to_send;

            // No SSI start Tag found in the SSI buffer
            if ( next_ssi_tag == NULL )
            {
                bytes_to_send = rel_len;
            }
            // Found SSI start tag in the middle of the buffer. Send data before tag.
            else
            {
                bytes_to_send = (uint32_t)(next_ssi_tag - file_buffer);
            }

            if(output_pos + bytes_to_send <= out_max_len)
            {
                pi_memcpy(output + output_pos, file_buffer, bytes_to_send);

                pos_byte += bytes_to_send;
                output_pos += bytes_to_send;
            }
            else
            {
                pi_log_e( "HTTP SSI:output buffer length(%d) is larger then the limit(%d)",
                        output_pos + bytes_to_send, out_max_len);
                ret_val = PARSER_ERROR;
            }

        }
        else
        {
            char *ssi_tag, *end_of_tag;
            unsigned int   tag_len;

            // We're at an SSI tag at the start of buffer, look for the end of tag
            end_of_tag = pi_strstr( next_ssi_tag, SSI_TAG_END );

            // Corner case: if End of SSI tag is not in the file buffer, read till the end of file.
            if ( !end_of_tag )
            {
                pi_memcpy(output, file_buffer, rel_len);
                pos_byte += rel_len;
                output_pos += rel_len;
                pi_log_e("SSItag: Find no ssi end tag!!!\n");
                return PARSER_ERROR;
            }

            tag_len = end_of_tag - next_ssi_tag + pi_strlen( SSI_TAG_END );

            // Create a buffer that contains ONLY the SSI tag.  This buffer is modified by subsequent calls.
            ssi_tag = ( char* )pi_malloc( tag_len + 1);
            if ( ssi_tag )
            {
                pi_memset(ssi_tag, 0x00, tag_len + 1);

                pi_memcpy( ssi_tag, next_ssi_tag, tag_len );
                ssi_tag[ tag_len ] = '\0';

                pi_memset(temp_buff, 0, sizeof(temp_buff));

                /* Replace the SSI Tag with the appropriate value */
                (void)http_ssi_tag_process( temp_buff, sizeof(temp_buff) - 1, (const char *)ssi_tag );
                temp_buff[sizeof(temp_buff) - 1] = '\0';
                pi_log_d( "temp_buff = %s\n", temp_buff);

                // Release the buffer containing the tag, since it has now been processed
                pi_free( ssi_tag );
                ssi_tag = NULL;

                if(output_pos + pi_strlen(temp_buff) <= out_max_len)
                {
                    pi_strncpy(output + output_pos, temp_buff, pi_strlen(temp_buff));
                    // The tag has been added to the context, so move past it
                    pos_byte += tag_len;
                    output_pos += pi_strlen(temp_buff);
                }
                else
                {
                    pi_log_e("HTTP SSI:output buffer length(%d) is larger then the limit(%d)",
                            output_pos + pi_strlen(temp_buff), out_max_len);
                    ret_val = PARSER_ERROR;
                }

            }
            else
            {
                pi_log_e("%s - mem allocation failed! \n", __FUNCTION__ );
                ret_val = MEM_MALLOC_ERROR;
                break;
            }
        }
    }while((pos_byte < input_len) && (ret_val == PARSER_SUCCESS));

    pi_log_d( "output = %s\n", output);

    return ret_val;
}



/******************************************************************************
 * Function Name: acl_common_get_printer_attributes
 * Created by: @2022/02/09
 *
 * \param[in]  pgqio: io data stream from USB.
 *
 * \param[in]  AclCmd: acl cmd info.
 *
 * \param[in]  cmd_data[unused]: acl cmd data.
 *
 * \return int ParseStatus
 *
 * Description: get printer attributes acl cmd callback function.
 *
 ******************************************************************************/
int acl_get_printer_attributes(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    pi_log_d("Enter function acl_common_get_printer_attributes \n");

    ParserStatus_E status = PARSER_SUCCESS;
    int e_res;

    uint32_t    recv_len = 0;               /*data length read from qio*/
    uint32_t    data_len;                   /*data length want to be read*/

    uint16_t    output_buff_len = 0;
    uint8_t     input_buff[ACL_PRINTER_ATTR_INPUT_MAX_LEN];

    //create a buffer to response status
    ACL_GET_PRINTER_ATTRIBUTE_REQUEST_CMD_S *cmd_buffer = (ACL_GET_PRINTER_ATTRIBUTE_REQUEST_CMD_S *) acl_cmd;
    if( NULL == cmd_buffer )
    {
        pi_log_e("Cmd buffer is NULL\n");
        return -1;
    }

    data_len = be16_to_cpu(cmd_buffer->data_length);  //BIG ENDIAN swape
    if (data_len > ACL_PRINTER_ATTR_INPUT_MAX_LEN)
    {
        pi_log_e("Cmd data len error %d\n", data_len);
        return -1;
    }

    /*read the set data from param string*/
    pi_memset(input_buff, 0,sizeof(input_buff));
    recv_len = (uint32_t)parser_common_read(pgqio, input_buff, data_len, PARSER_USB_TIMEOUT);
    if( recv_len != data_len )
    {
        pi_log_e("ACLGet: test data read error,have got = %d\n", recv_len );
        status = PARSER_NODATA_ERROR;
        return -1;
    }
    else
    {
        pi_log_d( "ACLGet: data read 0x%x bytes sucessfully\n", recv_len);
    }

    //malloc for out put buffer
    int time_retry = 0;

    uint8_t *output_buff = NULL;
    while((output_buff = (uint8_t *)pi_malloc(ACL_PRINTER_ATTR_OUTPUT_MAX_LEN)) == NULL)
    {
        if(time_retry >= 3)
        {
            pi_log_e("malloc failed!!!\n");
            status = MEM_MALLOC_ERROR;
            return -1;
        }

        time_retry++;
        (void)pi_sleep(1);
    }

    pi_memset(output_buff, 0x00, ACL_PRINTER_ATTR_OUTPUT_MAX_LEN);  // check malloc result. by dezhi
    if(PARSER_SUCCESS == status)
    {
        //Change the ssi tags in input_buff into the real value of OID
        e_res = http_ssi_tag_exchange((char*)output_buff, ACL_PRINTER_ATTR_OUTPUT_MAX_LEN, (const char*)input_buff/*TEST_SSI_BUFF*/);
        if(e_res != PARSER_SUCCESS)
        {
            pi_log_e("http_ssi_tag_exchange error!!!\n\n");
            status = PARSER_ERROR;
        }
        else
        {
            //Log(5, "http_ssi_tag_exchange OK:\nInput:\n%s\n\n", input_buff);
            //Log(5, "http_ssi_tag_exchange OK:\nOutput:\n%s\n\n", output_buff);
            output_buff_len = (uint16_t)pi_strlen((char *)output_buff)+ 1;
        }
    }

    //Response through USB
    if((status == PARSER_SUCCESS) && (output_buff_len > 0))
    {
        acl_direct_response(pgqio,
                (ACL_CMD_BASE_STRUCT_S *)cmd_buffer,
                ACLCMD_STATUS_SUCCESS,
                output_buff,
                output_buff_len);
    }
    else
    {
        acl_direct_response(pgqio,
                (ACL_CMD_BASE_STRUCT_S *)cmd_buffer,
                ACLCMD_STATUS_FAILURE,
                NULL,
                0);
    }

    if(output_buff)
    {
        pi_free(output_buff);
    }

    return PARSER_SUCCESS;
}


/******************************************************************************
 * Function Name: acl_set_printer_attributes
 * Created by: @2022/04/09
 *
 * \param[in]  pgqio: io data stream from USB.
 *
 * \param[in]  AclCmd: acl cmd info.
 *
 * \param[in]  cmd_data[unused]: acl cmd data.
 *
 * \return int ParseStatus
 *
 * Description: set printer attributes acl cmd callback function.
 *
 ******************************************************************************/
int acl_set_printer_attributes(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    pi_log_d("Enter function acl_set_printer_attributes \n");

    ParserStatus_E status = PARSER_SUCCESS;

    uint32_t    recv_len = 0;               /*data length read from qio*/
    uint32_t    data_len;                   /*data length want to be read*/
    uint8_t     input_buff[ACL_PRINTER_ATTR_INPUT_MAX_LEN] = {0};

    /*can set most 10 param in one time by attribute*/
    ACL_ATTR_VALUE_S acl_attr_values[MAX_PARAM_NUMBER]; //new define maplist node

    /*acl response format*/
    char acl_response[64];
    pi_memset(acl_response, 0x00, sizeof(acl_response));

    /*get the cmd info*/
    ACL_GET_PRINTER_ATTRIBUTE_REQUEST_CMD_S *cmd_buffer = (ACL_GET_PRINTER_ATTRIBUTE_REQUEST_CMD_S *) acl_cmd;
    if (NULL == cmd_buffer)
    {
        pi_log_e("Cmd buffer is NULL\n");
        return -1;
    }

    data_len = be16_to_cpu(cmd_buffer->data_length);
    if (data_len > ACL_PRINTER_ATTR_INPUT_MAX_LEN)
    {
        pi_log_e("Cmd data len error %d\n", data_len);
        return -1;
    }

    /*read the set data from param string*/
    pi_memset(input_buff, 0, sizeof(input_buff));
    recv_len = (uint32_t)parser_common_read(pgqio, input_buff, data_len, PARSER_USB_TIMEOUT);
    if( recv_len != data_len )
    {
        pi_log_e("ACLGet: data read error,need %d, got %d\n",data_len, recv_len );
        status = PARSER_NODATA_ERROR;
        return -1;
    }
    else
    {
        pi_log_d( "ACLGet: data read %d bytes sucessfully\n", recv_len);
    }

    /*get the param key(string) and the value of all the params(most 10 params)*/
    int32_t param_count = 0;
    pi_memset(acl_attr_values, 0, sizeof(acl_attr_values));
    param_count = acl_buffer_to_attribute((char *)input_buff, acl_attr_values, MAX_PARAM_NUMBER);
    if (0 == param_count)
    {
        pi_log_e("Error occur when AclBufferToOid\n");
        status = (ParserStatus_E)ACLCMD_STATUS_FAILURE;
        //strcpy(acl_response, "Param error");
        pi_strncpy(acl_response, "Param error", 11);
        goto ACL_SET_END;
    }

    /*save the old value of the key , if set failed, the value can be restored*/
    status = (ParserStatus_E)acl_get_attribute_values_to_old(acl_attr_values, (uint32_t)param_count);
    if (PARSER_SUCCESS != status)
    {
        pi_log_e("set old value failed\n");
        pi_strncpy(acl_response, "Param error", 11);
        goto ACL_SET_END;
    }

    /*set the attribule in the list(if have sevral attributes to set) to new value.*/
    status  = (ParserStatus_E)acl_set_attribute_value_all(acl_attr_values, (uint32_t)param_count);
    if (PARSER_SUCCESS != status)
    {
        pi_log_e("set failed to restore!\n");

        /*if set error, all the attributes in the list need to be restored to old value*/
        (void)acl_restore_attribute_values_form_old(acl_attr_values, (uint32_t)param_count);
        pi_strncpy(acl_response, "set fail", 8);
    }

ACL_SET_END:
    acl_direct_response(pgqio,
            (ACL_CMD_BASE_STRUCT_S *)cmd_buffer,
            status,
            (pi_strlen(acl_response) == 0 ? NULL : (uint8_t *)acl_response),
            pi_strlen(acl_response));

#if 0
    /*set machine type need to be rebooted to make the settting effective*/
    if( 0 == pi_strncmp((char*)input_buff, "OID_PLATFORM_PRODUCT_MODEL", pi_strlen("OID_PLATFORM_PRODUCT_MODEL"))
            &&(PARSER_SUCCESS == status))
    {
        pi_log_d( "printer will reboot automatically after be set a new machine type!\n");
        pi_sleep(2);
#if CONFIG_XC4_UPGRADE
        pi_log_d("acl set system sec_reboot !\n");
        (void)secure_reboot();
#else
        (void)system("reboot -f");
#endif
    }
#endif

    return PARSER_SUCCESS;
}
