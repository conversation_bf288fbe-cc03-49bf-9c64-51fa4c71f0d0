/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netdata.h
 * @addtogroup net
 * @{
 * @addtogroup netdata
 * <AUTHOR> <PERSON>n
 * @date 2023-4-20
 * @brief Network data manager API
 */
#ifndef __NETDATA_H__
#define __NETDATA_H__

#include "nettypes.h"
struct network_data_manager;
typedef struct network_data_manager DATA_MGR_S;

typedef struct
{
    char userinfo[MAX_AIRPRINT_USERINFO][256];
}
AIRPRINT_USERLIST_S;

typedef enum marker_index
{
    MARKER_ID_K = 0,
    MARKER_ID_Y,
    MARKER_ID_M,
    MARKER_ID_C,
    MARKER_NUM,
}
MARKER_ID_E;


typedef enum page_counter_index
{
    PC_IDX_PRINT_TOTAL = 0,         ///< 打印计数-总数
    PC_IDX_PRINT_DUPLEX,            ///< 打印计数-双面
    PC_IDX_PRINT_COLOR,             ///< 打印计数-彩色
    PC_IDX_PRINT_MONO,              ///< 打印计数-黑白

    PC_IDX_PRINT_COLOR_A3,          ///< 打印计数-彩色A3
    PC_IDX_PRINT_COLOR_A4,          ///< 打印计数-彩色A4
    PC_IDX_PRINT_COLOR_A5,          ///< 打印计数-彩色A5
    PC_IDX_PRINT_COLOR_A6,          ///< 打印计数-彩色A6
    PC_IDX_PRINT_COLOR_BIG16K,      ///< 打印计数-彩色Big16K
    PC_IDX_PRINT_COLOR_B4,          ///< 打印计数-彩色B4
    PC_IDX_PRINT_COLOR_ENVC5,       ///< 打印计数-彩色EnvC5
    PC_IDX_PRINT_COLOR_ENVC6,       ///< 打印计数-彩色EnvC6
    PC_IDX_PRINT_COLOR_ENVDL,       ///< 打印计数-彩色EnvDL
    PC_IDX_PRINT_COLOR_ENVMON,      ///< 打印计数-彩色EnvMon
    PC_IDX_PRINT_COLOR_ENV10,       ///< 打印计数-彩色Env10
    PC_IDX_PRINT_COLOR_EQUPAGES,    ///< 打印计数-彩色EquPages
    PC_IDX_PRINT_COLOR_EXECUTIVE,   ///< 打印计数-彩色Executive
    PC_IDX_PRINT_COLOR_FOLIO,       ///< 打印计数-彩色Folio
    PC_IDX_PRINT_COLOR_FOOLSCAP3,   ///< 打印计数-彩色Foolscap3
    PC_IDX_PRINT_COLOR_INVOICE,     ///< 打印计数-彩色Invoice
    PC_IDX_PRINT_COLOR_ISO_B5,      ///< 打印计数-彩色ISO B5
    PC_IDX_PRINT_COLOR_JIS_B4,      ///< 打印计数-彩色JIS B4
    PC_IDX_PRINT_COLOR_JIS_B5,      ///< 打印计数-彩色JIS B5
    PC_IDX_PRINT_COLOR_JIS_B6,      ///< 打印计数-彩色JIS B6
    PC_IDX_PRINT_COLOR_LEDGER,      ///< 打印计数-彩色Ledger
    PC_IDX_PRINT_COLOR_LETTER,      ///< 打印计数-彩色Letter
    PC_IDX_PRINT_COLOR_LEGAL,       ///< 打印计数-彩色Legal
    PC_IDX_PRINT_COLOR_OFICIO,      ///< 打印计数-彩色Oficio
    PC_IDX_PRINT_COLOR_POSTERCARD,  ///< 打印计数-彩色PosterCard
    PC_IDX_PRINT_COLOR_STATEMENT,   ///< 打印计数-彩色Statement
    PC_IDX_PRINT_COLOR_SRA3,        ///< 打印计数-彩色Sra3
    PC_IDX_PRINT_COLOR_USERDEF,     ///< 打印计数-彩色自定义
    PC_IDX_PRINT_COLOR_11X17,       ///< 打印计数-彩色11x17
    PC_IDX_PRINT_COLOR_8K,          ///< 打印计数-彩色8K

    PC_IDX_PRINT_MONO_A3,           ///< 打印计数-黑白A3
    PC_IDX_PRINT_MONO_A4,           ///< 打印计数-黑白A4
    PC_IDX_PRINT_MONO_A5,           ///< 打印计数-黑白A5
    PC_IDX_PRINT_MONO_A6,           ///< 打印计数-黑白A6
    PC_IDX_PRINT_MONO_BIG16K,       ///< 打印计数-黑白Big16K
    PC_IDX_PRINT_MONO_B4,           ///< 打印计数-黑白B4
    PC_IDX_PRINT_MONO_ENVC5,        ///< 打印计数-黑白EnvC5
    PC_IDX_PRINT_MONO_ENVC6,        ///< 打印计数-黑白EnvC6
    PC_IDX_PRINT_MONO_ENVDL,        ///< 打印计数-黑白EnvDL
    PC_IDX_PRINT_MONO_ENVMON,       ///< 打印计数-黑白EnvMon
    PC_IDX_PRINT_MONO_ENV10,        ///< 打印计数-黑白Env10
    PC_IDX_PRINT_MONO_EQUPAGES,     ///< 打印计数-黑白EquPages
    PC_IDX_PRINT_MONO_EXECUTIVE,    ///< 打印计数-黑白Executive
    PC_IDX_PRINT_MONO_FOLIO,        ///< 打印计数-黑白Folio
    PC_IDX_PRINT_MONO_FOOLSCAP3,    ///< 打印计数-黑白Foolscap3
    PC_IDX_PRINT_MONO_INVOICE,      ///< 打印计数-黑白Invoice
    PC_IDX_PRINT_MONO_ISO_B5,       ///< 打印计数-黑白ISO B5
    PC_IDX_PRINT_MONO_JIS_B4,       ///< 打印计数-黑白JIS B4
    PC_IDX_PRINT_MONO_JIS_B5,       ///< 打印计数-黑白JIS B5
    PC_IDX_PRINT_MONO_JIS_B6,       ///< 打印计数-黑白JIS B6
    PC_IDX_PRINT_MONO_LEDGER,       ///< 打印计数-黑白Ledger
    PC_IDX_PRINT_MONO_LETTER,       ///< 打印计数-黑白Letter
    PC_IDX_PRINT_MONO_LEGAL,        ///< 打印计数-黑白Legal
    PC_IDX_PRINT_MONO_OFICIO,       ///< 打印计数-黑白Oficio
    PC_IDX_PRINT_MONO_POSTERCARD,   ///< 打印计数-黑白PosterCard
    PC_IDX_PRINT_MONO_STATEMENT,    ///< 打印计数-黑白Statement
    PC_IDX_PRINT_MONO_SRA3,         ///< 打印计数-黑白Sra3
    PC_IDX_PRINT_MONO_USERDEF,      ///< 打印计数-黑白自定义
    PC_IDX_PRINT_MONO_11X17,        ///< 打印计数-黑白11x17
    PC_IDX_PRINT_MONO_8K,           ///< 打印计数-黑白8K

    PC_IDX_COPY_TOTAL,              ///< 复印计数-总数
    PC_IDX_COPY_DUPLEX,             ///< 复印计数-双面
    PC_IDX_COPY_COLOR,              ///< 复印计数-彩色
    PC_IDX_COPY_MONO,               ///< 复印计数-黑白

    PC_IDX_COPY_COLOR_A3,           ///< 复印计数-彩色A3
    PC_IDX_COPY_COLOR_A4,           ///< 复印计数-彩色A4
    PC_IDX_COPY_COLOR_A5,           ///< 复印计数-彩色A5
    PC_IDX_COPY_COLOR_A6,           ///< 复印计数-彩色A6
    PC_IDX_COPY_COLOR_BIG16K,       ///< 复印计数-彩色Big16K
    PC_IDX_COPY_COLOR_B4,           ///< 复印计数-彩色B4
    PC_IDX_COPY_COLOR_ENVC5,        ///< 复印计数-彩色EnvC5
    PC_IDX_COPY_COLOR_ENVC6,        ///< 复印计数-彩色EnvC6
    PC_IDX_COPY_COLOR_ENVDL,        ///< 复印计数-彩色EnvDL
    PC_IDX_COPY_COLOR_ENVMON,       ///< 复印计数-彩色EnvMon
    PC_IDX_COPY_COLOR_ENV10,        ///< 复印计数-彩色Env10
    PC_IDX_COPY_COLOR_EQUPAGES,     ///< 复印计数-彩色EquPages
    PC_IDX_COPY_COLOR_EXECUTIVE,    ///< 复印计数-彩色Executive
    PC_IDX_COPY_COLOR_FOLIO,        ///< 复印计数-彩色Folio
    PC_IDX_COPY_COLOR_FOOLSCAP3,    ///< 复印计数-彩色Foolscap3
    PC_IDX_COPY_COLOR_INVOICE,      ///< 复印计数-彩色Invoice
    PC_IDX_COPY_COLOR_ISO_B5,       ///< 复印计数-彩色ISO B5
    PC_IDX_COPY_COLOR_JIS_B4,       ///< 复印计数-彩色JIS B4
    PC_IDX_COPY_COLOR_JIS_B5,       ///< 复印计数-彩色JIS B5
    PC_IDX_COPY_COLOR_JIS_B6,       ///< 复印计数-彩色JIS B6
    PC_IDX_COPY_COLOR_LEDGER,       ///< 复印计数-彩色Ledger
    PC_IDX_COPY_COLOR_LETTER,       ///< 复印计数-彩色Letter
    PC_IDX_COPY_COLOR_LEGAL,        ///< 复印计数-彩色Legal
    PC_IDX_COPY_COLOR_OFICIO,       ///< 复印计数-彩色Oficio
    PC_IDX_COPY_COLOR_POSTERCARD,   ///< 复印计数-彩色PosterCard
    PC_IDX_COPY_COLOR_STATEMENT,    ///< 复印计数-彩色Statement
    PC_IDX_COPY_COLOR_SRA3,         ///< 复印计数-彩色Sra3
    PC_IDX_COPY_COLOR_USERDEF,      ///< 复印计数-彩色自定义
    PC_IDX_COPY_COLOR_11X17,        ///< 复印计数-彩色11x17
    PC_IDX_COPY_COLOR_8K,           ///< 复印计数-彩色8K

    PC_IDX_COPY_MONO_A3,            ///< 复印计数-黑白A3
    PC_IDX_COPY_MONO_A4,            ///< 复印计数-黑白A4
    PC_IDX_COPY_MONO_A5,            ///< 复印计数-黑白A5
    PC_IDX_COPY_MONO_A6,            ///< 复印计数-黑白A6
    PC_IDX_COPY_MONO_BIG16K,        ///< 复印计数-黑白Big16K
    PC_IDX_COPY_MONO_B4,            ///< 复印计数-黑白B4
    PC_IDX_COPY_MONO_ENVC5,         ///< 复印计数-黑白EnvC5
    PC_IDX_COPY_MONO_ENVC6,         ///< 复印计数-黑白EnvC6
    PC_IDX_COPY_MONO_ENVDL,         ///< 复印计数-黑白EnvDL
    PC_IDX_COPY_MONO_ENVMON,        ///< 复印计数-黑白EnvMon
    PC_IDX_COPY_MONO_ENV10,         ///< 复印计数-黑白Env10
    PC_IDX_COPY_MONO_EQUPAGES,      ///< 复印计数-黑白EquPages
    PC_IDX_COPY_MONO_EXECUTIVE,     ///< 复印计数-黑白Executive
    PC_IDX_COPY_MONO_FOLIO,         ///< 复印计数-黑白Folio
    PC_IDX_COPY_MONO_FOOLSCAP3,     ///< 复印计数-黑白Foolscap3
    PC_IDX_COPY_MONO_INVOICE,       ///< 复印计数-黑白Invoice
    PC_IDX_COPY_MONO_ISO_B5,        ///< 复印计数-黑白ISO B5
    PC_IDX_COPY_MONO_JIS_B4,        ///< 复印计数-黑白JIS B4
    PC_IDX_COPY_MONO_JIS_B5,        ///< 复印计数-黑白JIS B5
    PC_IDX_COPY_MONO_JIS_B6,        ///< 复印计数-黑白JIS B6
    PC_IDX_COPY_MONO_LEDGER,        ///< 复印计数-黑白Ledger
    PC_IDX_COPY_MONO_LETTER,        ///< 复印计数-黑白Letter
    PC_IDX_COPY_MONO_LEGAL,         ///< 复印计数-黑白Legal
    PC_IDX_COPY_MONO_OFICIO,        ///< 复印计数-黑白Oficio
    PC_IDX_COPY_MONO_POSTERCARD,    ///< 复印计数-黑白PosterCard
    PC_IDX_COPY_MONO_STATEMENT,     ///< 复印计数-黑白Statement
    PC_IDX_COPY_MONO_SRA3,          ///< 复印计数-黑白Sra3
    PC_IDX_COPY_MONO_USERDEF,       ///< 复印计数-黑白自定义
    PC_IDX_COPY_MONO_11X17,         ///< 复印计数-黑白11x17
    PC_IDX_COPY_MONO_8K,            ///< 复印计数-黑白8K

    PC_IDX_SCAN_ADF_TO_COPY,        ///< 扫描计数-ADF扫描到复印
    PC_IDX_SCAN_ADF_TOTAL,          ///< 扫描计数-ADF扫描
    PC_IDX_SCAN_FB_TO_COPY,         ///< 扫描计数-FB扫描到复印
    PC_IDX_SCAN_FB_TOTAL,           ///< 扫描计数-FB扫描
    PC_IDX_SCAN_TOTAL,              ///< 扫描计数-总数
    PC_IDX_SCAN_DUPLEX,             ///< 扫描计数-双面
    PC_IDX_SCAN_COLOR,              ///< 扫描计数-彩色
    PC_IDX_SCAN_MONO,               ///< 扫描计数-黑白

    PC_IDX_SCAN_COLOR_A3,           ///< 扫描计数-彩色A3
    PC_IDX_SCAN_COLOR_A4,           ///< 扫描计数-彩色A4
    PC_IDX_SCAN_COLOR_A5,           ///< 扫描计数-彩色A5
    PC_IDX_SCAN_COLOR_A6,           ///< 扫描计数-彩色A6
    PC_IDX_SCAN_COLOR_BIG16K,       ///< 扫描计数-彩色Big16K
    PC_IDX_SCAN_COLOR_B4,           ///< 扫描计数-彩色B4
    PC_IDX_SCAN_COLOR_ENVC5,        ///< 扫描计数-彩色EnvC5
    PC_IDX_SCAN_COLOR_ENVC6,        ///< 扫描计数-彩色EnvC6
    PC_IDX_SCAN_COLOR_ENVDL,        ///< 扫描计数-彩色EnvDL
    PC_IDX_SCAN_COLOR_ENVMON,       ///< 扫描计数-彩色EnvMon
    PC_IDX_SCAN_COLOR_ENV10,        ///< 扫描计数-彩色Env10
    PC_IDX_SCAN_COLOR_EQUPAGES,     ///< 扫描计数-彩色EquPages
    PC_IDX_SCAN_COLOR_EXECUTIVE,    ///< 扫描计数-彩色Executive
    PC_IDX_SCAN_COLOR_FOLIO,        ///< 扫描计数-彩色Folio
    PC_IDX_SCAN_COLOR_ISO_B5,       ///< 扫描计数-彩色ISO B5
    PC_IDX_SCAN_COLOR_JIS_B5,       ///< 扫描计数-彩色JIS B5
    PC_IDX_SCAN_COLOR_JIS_B6,       ///< 扫描计数-彩色JIS B6
    PC_IDX_SCAN_COLOR_LETTER,       ///< 扫描计数-彩色Letter
    PC_IDX_SCAN_COLOR_LEGAL,        ///< 扫描计数-彩色Legal
    PC_IDX_SCAN_COLOR_OFICIO,       ///< 扫描计数-彩色Oficio
    PC_IDX_SCAN_COLOR_POSTERCARD,   ///< 扫描计数-彩色PosterCard
    PC_IDX_SCAN_COLOR_STATEMENT,    ///< 扫描计数-彩色Statement
    PC_IDX_SCAN_COLOR_USERDEF,      ///< 扫描计数-彩色自定义
    PC_IDX_SCAN_COLOR_11X17,        ///< 扫描计数-彩色11x17
    PC_IDX_SCAN_COLOR_8K,           ///< 扫描计数-彩色8K

    PC_IDX_SCAN_MONO_A3,            ///< 扫描计数-黑白A3
    PC_IDX_SCAN_MONO_A4,            ///< 扫描计数-黑白A4
    PC_IDX_SCAN_MONO_A5,            ///< 扫描计数-黑白A5
    PC_IDX_SCAN_MONO_A6,            ///< 扫描计数-黑白A6
    PC_IDX_SCAN_MONO_BIG16K,        ///< 扫描计数-黑白Big16K
    PC_IDX_SCAN_MONO_B4,            ///< 扫描计数-黑白B4
    PC_IDX_SCAN_MONO_ENVC5,         ///< 扫描计数-黑白EnvC5
    PC_IDX_SCAN_MONO_ENVC6,         ///< 扫描计数-黑白EnvC6
    PC_IDX_SCAN_MONO_ENVDL,         ///< 扫描计数-黑白EnvDL
    PC_IDX_SCAN_MONO_ENVMON,        ///< 扫描计数-黑白EnvMon
    PC_IDX_SCAN_MONO_ENV10,         ///< 扫描计数-黑白Env10
    PC_IDX_SCAN_MONO_EQUPAGES,      ///< 扫描计数-黑白EquPages
    PC_IDX_SCAN_MONO_EXECUTIVE,     ///< 扫描计数-黑白Executive
    PC_IDX_SCAN_MONO_FOLIO,         ///< 扫描计数-黑白Folio
    PC_IDX_SCAN_MONO_ISO_B5,        ///< 扫描计数-黑白ISO B5
    PC_IDX_SCAN_MONO_JIS_B5,        ///< 扫描计数-黑白JIS B5
    PC_IDX_SCAN_MONO_JIS_B6,        ///< 扫描计数-黑白JIS B6
    PC_IDX_SCAN_MONO_LETTER,        ///< 扫描计数-黑白Letter
    PC_IDX_SCAN_MONO_LEGAL,         ///< 扫描计数-黑白Legal
    PC_IDX_SCAN_MONO_OFICIO,        ///< 扫描计数-黑白Oficio
    PC_IDX_SCAN_MONO_POSTERCARD,    ///< 扫描计数-黑白PosterCard
    PC_IDX_SCAN_MONO_STATEMENT,     ///< 扫描计数-黑白Statement
    PC_IDX_SCAN_MONO_USERDEF,       ///< 扫描计数-黑白自定义
    PC_IDX_SCAN_MONO_11X17,         ///< 扫描计数-黑白11x17
    PC_IDX_SCAN_MONO_8K,            ///< 扫描计数-黑白8K

    PC_IDX_NUM,
}
PC_IDX_E;

PT_BEGIN_DECLS

#define NET_DATA_MEMBER_TABLE( UINT32_ARRAY_OUT, UINT32_VALUE_OUT, STRING_ARRAY_OUT, STRING_VALUE_OUT, BUFFER_BYTES_OUT ) \
    UINT32_VALUE_OUT( factory_status            , NETWORK_ID_FACTORY_STATUS                                             ) \
                                                                                                                          \
    UINT32_ARRAY_OUT( iface_switch              , NETWORK_ID_IFACE_SWITCH           , IFACE_ID_NUM                      ) \
    UINT32_ARRAY_OUT( ipv4_usedhcp              , NETWORK_ID_IPV4_USEDHCP           , IFACE_ID_NUM                      ) \
    UINT32_ARRAY_OUT( ipv4_autodns              , NETWORK_ID_IPV4_AUTODNS           , IFACE_ID_NUM                      ) \
    UINT32_ARRAY_OUT( ipv6_switch               , NETWORK_ID_IPV6_SWITCH            , IFACE_ID_NUM                      ) \
    UINT32_ARRAY_OUT( ipv6_usedhcp              , NETWORK_ID_IPV6_USEDHCP           , IFACE_ID_NUM                      ) \
    STRING_ARRAY_OUT( ipv4_addr                 , NETWORK_ID_IPV4_ADDR              , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv4_mask                 , NETWORK_ID_IPV4_MASK              , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv4_gtwy                 , NETWORK_ID_IPV4_GTWY              , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv4_dns0                 , NETWORK_ID_IPV4_DNS0              , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv4_dns1                 , NETWORK_ID_IPV4_DNS1              , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
                                                                                                                          \
    UINT32_ARRAY_OUT( iface_running             , 0                                 , IFACE_ID_NUM                      ) \
    STRING_ARRAY_OUT( mac_addr                  , 0                                 , IFACE_ID_NUM  , 32                ) \
    STRING_ARRAY_OUT( ipv4_dns0_bak             , 0                                 , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv4_dns1_bak             , 0                                 , IFACE_ID_NUM  , IPV4_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv6_link                 , 0                                 , IFACE_ID_NUM  , IPV6_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv6_stls                 , 0                                 , IFACE_ID_NUM  , IPV6_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv6_site                 , 0                                 , IFACE_ID_NUM  , IPV6_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv6_dns0                 , 0                                 , IFACE_ID_NUM  , IPV6_ADDR_LEN     ) \
    STRING_ARRAY_OUT( ipv6_dns1                 , 0                                 , IFACE_ID_NUM  , IPV6_ADDR_LEN     ) \
                                                                                                                          \
    UINT32_VALUE_OUT( sta_status                , 0                                                                     ) \
    UINT32_VALUE_OUT( sta_detail                , 0                                                                     ) \
    UINT32_VALUE_OUT( sta_freq                  , 0                                                                     ) \
    UINT32_VALUE_OUT( sta_rssi                  , 0                                                                     ) \
    UINT32_VALUE_OUT( sta_pmf                   , 0                                                                     ) \
    UINT32_VALUE_OUT( wps_status                , 0                                                                     ) \
    UINT32_VALUE_OUT( wps_start_time            , 0                                                                     ) \
    STRING_VALUE_OUT( wps_pin                   , 0                                 , 16                                ) \
    UINT32_VALUE_OUT( wfd_att_num               , 0                                                                     ) \
    BUFFER_BYTES_OUT( ap_list                   , 0                                 , 0                                 ) \
    UINT32_VALUE_OUT( ap_num                    , 0                                                                     ) \
                                                                                                                          \
    UINT32_VALUE_OUT( sta_sec_mode              , NETWORK_ID_STA_SEC_MODE                                               ) \
    STRING_VALUE_OUT( sta_ssid                  , NETWORK_ID_STA_SSID               , 128                               ) \
    STRING_VALUE_OUT( sta_psk                   , NETWORK_ID_STA_PSK                , 68                                ) \
    STRING_VALUE_OUT( wfd_ssid_prefix           , NETWORK_ID_WFD_SSID_PREFIX        , 32                                ) \
    STRING_VALUE_OUT( wfd_ssid_suffix           , NETWORK_ID_WFD_SSID_SUFFIX        , 32                                ) \
    STRING_VALUE_OUT( wfd_psk                   , NETWORK_ID_WFD_PSK                , 64                                ) \
                                                                                                                          \
    UINT32_VALUE_OUT( wired_speed               , NETWORK_ID_WIRED_SPEED                                                ) \
    STRING_VALUE_OUT( hostname                  , NETWORK_ID_HOSTNAME               , HOSTNAME_LEN                      ) \
    STRING_VALUE_OUT( domain                    , NETWORK_ID_DOMAIN                 , 64                                ) \
    STRING_VALUE_OUT( uuid                      , NETWORK_ID_UUID                   , 64                                ) \
    STRING_VALUE_OUT( web_sec_pswd              , NETWORK_ID_WEB_SEC_PSWD           , 64                                ) \
    STRING_VALUE_OUT( web_aud_pswd              , NETWORK_ID_WEB_AUD_PSWD           , 64                                ) \
    STRING_VALUE_OUT( web_sys_pswd              , NETWORK_ID_WEB_SYS_PSWD           , 64                                ) \
    STRING_VALUE_OUT( web_user                  , NETWORK_ID_WEB_USERNAME           , 64                                ) \
    STRING_VALUE_OUT( web_pswd                  , NETWORK_ID_WEB_PASSWORD           , 64                                ) \
                                                                                                                          \
    UINT32_VALUE_OUT( rawprint_switch           , NETWORK_ID_RAWPRINT_SWITCH                                            ) \
    UINT32_VALUE_OUT( rawprint_port             , NETWORK_ID_RAWPRINT_PORT                                              ) \
    UINT32_VALUE_OUT( rawscan_switch            , NETWORK_ID_RAWSCAN_SWITCH                                             ) \
    UINT32_VALUE_OUT( rawscan_port              , NETWORK_ID_RAWSCAN_PORT                                               ) \
    UINT32_VALUE_OUT( lpd_switch                , NETWORK_ID_LPD_SWITCH                                                 ) \
    UINT32_VALUE_OUT( wsd_switch                , NETWORK_ID_WSD_SWITCH                                                 ) \
    UINT32_VALUE_OUT( slp_switch                , NETWORK_ID_SLP_SWITCH                                                 ) \
    UINT32_VALUE_OUT( bonjour_switch            , NETWORK_ID_BONJOUR_SWITCH                                             ) \
    STRING_VALUE_OUT( bonjour_server            , NETWORK_ID_BONJOUR_SERVER         , 128                               ) \
    UINT32_VALUE_OUT( ipp_switch                , NETWORK_ID_IPP_SWITCH                                                 ) \
                                                                                                                          \
    UINT32_VALUE_OUT( snmp_switch               , NETWORK_ID_SNMP_SWITCH                                                ) \
    UINT32_VALUE_OUT( snmp_version_flags        , NETWORK_ID_SNMP_VERSION_FLAGS                                         ) \
    STRING_VALUE_OUT( snmp_v1_community         , NETWORK_ID_SNMP_V1_COMMUNITY      , 64                                ) \
    STRING_VALUE_OUT( snmp_v2_community         , NETWORK_ID_SNMP_V2_COMMUNITY      , 64                                ) \
    STRING_VALUE_OUT( snmp_v3_community         , NETWORK_ID_SNMP_V3_COMMUNITY      , 64                                ) \
    STRING_VALUE_OUT( snmp_v3_user_name         , NETWORK_ID_SNMP_V3_USER_NAME      , 64                                ) \
    STRING_VALUE_OUT( snmp_v3_auth_pswd         , NETWORK_ID_SNMP_V3_AUTH_PSWD      , 36                                ) \
    STRING_VALUE_OUT( snmp_v3_priv_pswd         , NETWORK_ID_SNMP_V3_PRIV_PSWD      , 36                                ) \
    UINT32_VALUE_OUT( snmp_v1v2c_switch         , 0                                                                     ) \
    UINT32_VALUE_OUT( snmp_v3_switch            , 0                                                                     ) \
                                                                                                                          \
    UINT32_VALUE_OUT( smb_ntlmv1_switch         , NETWORK_ID_SMB_NTLMV1_SWITCH                                          ) \
    UINT32_VALUE_OUT( smb_ntlm_auto             , NETWORK_ID_SMB_NTLM_AUTO                                              ) \
                                                                                                                          \
    STRING_VALUE_OUT( smtp_sender_addr          , NETWORK_ID_SMTP_SENDER_ADDR       , 64                                ) \
    STRING_VALUE_OUT( smtp_server_addr          , NETWORK_ID_SMTP_SERVER_ADDR       , 64                                ) \
    UINT32_VALUE_OUT( smtp_server_port          , NETWORK_ID_SMTP_SERVER_PORT                                           ) \
    UINT32_VALUE_OUT( smtp_server_auth          , NETWORK_ID_SMTP_SERVER_AUTH                                           ) \
    UINT32_VALUE_OUT( smtp_sec_mode             , NETWORK_ID_SMTP_SEC_MODE                                              ) \
    STRING_VALUE_OUT( smtp_username             , NETWORK_ID_SMTP_USERNAME          , 64                                ) \
    STRING_VALUE_OUT( smtp_password             , NETWORK_ID_SMTP_PASSWORD          , 21                                ) \
    STRING_VALUE_OUT( smtp_test_result          , 0                                 , 128                               ) \
                                                                                                                          \
    STRING_VALUE_OUT( alarm_client_addr1        , NETWORK_ID_ALARM_CLIENT_ADDR1     , 64                                ) \
    STRING_VALUE_OUT( alarm_client_addr2        , NETWORK_ID_ALARM_CLIENT_ADDR2     , 64                                ) \
    STRING_VALUE_OUT( alarm_client_addr3        , NETWORK_ID_ALARM_CLIENT_ADDR3     , 64                                ) \
    STRING_VALUE_OUT( alarm_client_addr4        , NETWORK_ID_ALARM_CLIENT_ADDR4     , 64                                ) \
    UINT32_VALUE_OUT( alarm_status_flags        , NETWORK_ID_ALARM_STATUS_FLAGS                                         ) \
    UINT32_VALUE_OUT( alarm_paper_empty         , 0                                                                     ) \
    UINT32_VALUE_OUT( alarm_paper_jam           , 0                                                                     ) \
    UINT32_VALUE_OUT( alarm_toner_empty         , 0                                                                     ) \
    UINT32_VALUE_OUT( alarm_toner_low           , 0                                                                     ) \
    UINT32_VALUE_OUT( alarm_waste_toner         , 0                                                                     ) \
    /* 网络白名单开关*/                                                                                                   \
    UINT32_VALUE_OUT( whitelist_switch          , NETWORK_ID_WHITELIST_SWITCH                                           ) \
                                                                                                                          \
    BUFFER_BYTES_OUT( mail_grouplist            , NETWORK_ID_MAIL_GROUPLIST         , sizeof(MAIL_GROUPLIST_S)          ) \
    BUFFER_BYTES_OUT( mail_addrbook             , NETWORK_ID_MAIL_ADDRBOOK          , sizeof(MAIL_ADDRBOOK_S)           ) \
    BUFFER_BYTES_OUT( ftp_addrbook              , NETWORK_ID_FTP_ADDRBOOK           , sizeof(FTP_ADDRBOOK_S)            ) \
    BUFFER_BYTES_OUT( smb_addrbook              , NETWORK_ID_SMB_ADDRBOOK           , sizeof(SMB_ADDRBOOK_S)            ) \
    BUFFER_BYTES_OUT( whitelist_addrbook        , NETWORK_ID_WHITELIST_ADDRBOOK     , sizeof(WHITELIST_ADDRBOOK_S)      ) \
    BUFFER_BYTES_OUT( airprint_userlist         , NETWORK_ID_AIRPRINT_USERLIST      , sizeof(AIRPRINT_USERLIST_S)       ) \
    STRING_VALUE_OUT( printer_longitude         , NETWORK_ID_PRINTER_LONGITUDE      , 32                                ) \
    STRING_VALUE_OUT( printer_latitude          , NETWORK_ID_PRINTER_LATITUDE       , 32                                ) \
    /* 证书私钥密码 */                                                                                                    \
    STRING_VALUE_OUT( tls_priv_pswd             , NETWORK_ID_TLS_PRIV_PSWD          , 128                               ) \
    /* 证书私钥+签名信息 */                                                                                               \
    STRING_VALUE_OUT( tls_priv_cert             , NETWORK_ID_TLS_PRIV_CERT          , 8192                              ) \
    /* CA证书私钥 */                                                                                                      \
    STRING_VALUE_OUT( tls_ca_pkey               , NETWORK_ID_TLS_CA_PKEY            , 2048                              ) \
    /* CA证书签名 */                                                                                                      \
    STRING_VALUE_OUT( tls_ca_cert               , NETWORK_ID_TLS_CA_CERT            , 2048                              ) \
    /* 自定义水印 */                                                                                                      \
    STRING_VALUE_OUT( water_mark1               , NETWORK_ID_WATER_MARK1            , 128                               ) \
    STRING_VALUE_OUT( water_mark2               , NETWORK_ID_WATER_MARK2            , 128                               ) \
    STRING_VALUE_OUT( water_mark3               , NETWORK_ID_WATER_MARK3            , 128                               ) \
    STRING_VALUE_OUT( water_mark4               , NETWORK_ID_WATER_MARK4            , 128                               ) \
    STRING_VALUE_OUT( water_mark5               , NETWORK_ID_WATER_MARK5            , 128                               ) \
    STRING_VALUE_OUT( water_mark6               , NETWORK_ID_WATER_MARK6            , 128                               ) \
                                                                                                                          \
    UINT32_VALUE_OUT( copy_audit_switch         , NETWORK_ID_COPY_AUDIT_SWITCH                                          ) \
    STRING_VALUE_OUT( copy_audit_server         , NETWORK_ID_COPY_AUDIT_SERVER      , 64                                ) \
                                                                                                                          \
    UINT32_VALUE_OUT( sec_alert_switch          , NETWORK_ID_SEC_ALERT_SWITCH                                           ) \
    STRING_VALUE_OUT( sec_alert_unit            , NETWORK_ID_SEC_ALERT_UNIT         , 128                               ) \
    STRING_VALUE_OUT( sec_alert_department      , NETWORK_ID_SEC_ALERT_DEPARTMENT   , 128                               ) \
    STRING_VALUE_OUT( sec_alert_owner           , NETWORK_ID_SEC_ALERT_OWNER        , 128                               ) \
    STRING_VALUE_OUT( sec_alert_server1         , NETWORK_ID_SEC_ALERT_SERVER1      , 128                               ) \
    STRING_VALUE_OUT( sec_alert_server2         , NETWORK_ID_SEC_ALERT_SERVER2      , 128                               ) \
    /* webpage支持多语言 */                                                                                               \
    UINT32_VALUE_OUT( support_language          , 0                                                                     ) \
    /* webpage是否显示wifi界面 */                                                                                         \
    UINT32_VALUE_OUT( support_wireless          , 0                                                                     ) \
    /* webpage是否显示wsd界面 */                                                                                          \
    UINT32_VALUE_OUT( support_wsd               , 0                                                                     ) \
    /* webpage是否显示raw界面 */                                                                                          \
    UINT32_VALUE_OUT( support_raw               , 0                                                                     ) \
    /* webpage是否显示slp界面 */                                                                                          \
    UINT32_VALUE_OUT( support_slp               , 0                                                                     ) \
    /* webpage是否显示whitelist界面 */                                                                                    \
    UINT32_VALUE_OUT( support_whitelist         , 0                                                                     ) \
    /* webpage是否显示snmp界面 */                                                                                         \
    UINT32_VALUE_OUT( support_snmp              , 0                                                                     ) \
    /* webpage是否显示bonjour界面 */                                                                                      \
    UINT32_VALUE_OUT( support_bonjour           , 0                                                                     ) \
    /* webpage是否显示mobile_print界面 */                                                                                 \
    UINT32_VALUE_OUT( support_mobile_print      , 0                                                                     ) \
    /* webpage是否显示airprint界面 */                                                                                     \
    UINT32_VALUE_OUT( support_airprint          , 0                                                                     ) \
    /* webpage是否显示ippsrv界面 */                                                                                       \
    UINT32_VALUE_OUT( support_ippsrv            , 0                                                                     ) \
    /* webpage是否显示addressbook界面 */                                                                                  \
    UINT32_VALUE_OUT( support_addressbook       , 0                                                                     ) \
    /* webpage是否显示ftp_addressbook界面 */                                                                              \
    UINT32_VALUE_OUT( support_ftp_addressbook   , 0                                                                     ) \
    /* webpage是否显示smb_addressbook界面 */                                                                              \
    UINT32_VALUE_OUT( support_smb_addressbook   , 0                                                                     ) \
    /* webpage是否显示smtp界面 */                                                                                         \
    UINT32_VALUE_OUT( support_smtp              , 0                                                                     ) \
    /* webpage是否显示smb界面 */                                                                                          \
    UINT32_VALUE_OUT( support_smb               , 0                                                                     ) \
    /* webpage是否显示sntp界面 */                                                                                         \
    UINT32_VALUE_OUT( support_sntp              , 0                                                                     ) \
    /* webpage是否显示休眠模式界面 */                                                                                     \
    UINT32_VALUE_OUT( support_sleep_mode        , 0                                                                     ) \
    /* webpage是否显示邮件通知界面 */                                                                                     \
    UINT32_VALUE_OUT( support_email_notification, 0                                                                     ) \
    /* webpage是否显示复印水印界面 */                                                                                     \
    UINT32_VALUE_OUT( support_watermark         , 0                                                                     ) \
    /* webpage是否显示远程控制界面 */                                                                                     \
    UINT32_VALUE_OUT( support_remote_control    , 0                                                                     ) \
    /* webpage是否显示设备报告界面 */                                                                                     \
    UINT32_VALUE_OUT( support_equipment_report  , 0                                                                     ) \
    /* webpage是否显示安全日志界面 */                                                                                     \
    UINT32_VALUE_OUT( support_safety_log        , 0                                                                     ) \
    /* webpage是否显示固件升级界面 */                                                                                     \
    UINT32_VALUE_OUT( support_firmware_upgrade  , 0                                                                     ) \
    /* webpage是否显示在线升级界面 */                                                                                     \
    UINT32_VALUE_OUT( support_online_upgrade    , 0                                                                     ) \
    /* webpage是否显示离线升级界面 */                                                                                     \
    UINT32_VALUE_OUT( support_offline_upgrade   , 0                                                                     ) \
    /* webpage支持作业管控 */                                                                                             \
    UINT32_VALUE_OUT( support_job_control       , 0                                                                     ) \
    /* 日志下载功能开关 */                                                                                                \
    UINT32_VALUE_OUT( export_log_switch         , 0                                                                     ) \
    /* mdns不注册TLS服务，便于分析抓包数据 */                                                                             \
    UINT32_VALUE_OUT( mdns_debug                , 0                                                                     ) \
                                                                                                                          \
    /* IEEE-1284字符串 */                                                                                                 \
    STRING_VALUE_OUT( ieee1284                  , 0                                 , IEEE1284_LEN                      ) \
    /* 厂商名称 */                                                                                                        \
    STRING_VALUE_OUT( mfg_name                  , 0                                 , MFG_NAME_LEN                      ) \
    /* 产品系列名称 */                                                                                                    \
    STRING_VALUE_OUT( ser_name                  , 0                                 , SERIES_NAME_LEN                   ) \
    /* 产品名称 */                                                                                                        \
    STRING_VALUE_OUT( pdt_name                  , 0                                 , PDT_NAME_LEN                      ) \
    /* 产品序列号 */                                                                                                      \
    STRING_VALUE_OUT( pdt_sn                    , 0                                 , 32                                ) \
    /* 固件版本号 */                                                                                                      \
    STRING_VALUE_OUT( fw_ver                    , 0                                 , 32                                ) \
    /* 位置信息 */                                                                                                        \
    STRING_VALUE_OUT( location                  , 0                                 , 256                               ) \
    /* 联系人 */                                                                                                          \
    STRING_VALUE_OUT( contacts                  , 0                                 , 128                               ) \
    /* 财产编号 */                                                                                                        \
    STRING_VALUE_OUT( prop_num                  , 0                                 , 64                                ) \
    /* 面板语言 */                                                                                                        \
    UINT32_VALUE_OUT( language                  , 0                                                                     ) \
    /* 国家代码 */                                                                                                        \
    UINT32_VALUE_OUT( country                   , 0                                                                     ) \
    /* 产品ID */                                                                                                          \
    UINT32_VALUE_OUT( pdt_id                    , 0                                                                     ) \
    /* 系统时区 */                                                                                                        \
    UINT32_VALUE_OUT( timezone                  , 0                                                                     ) \
    /* 是否支持扫描 */                                                                                                    \
    UINT32_VALUE_OUT( support_scan              , 0                                                                     ) \
    /* 是否支持双面打印 */                                                                                                \
    UINT32_VALUE_OUT( support_duplex            , 0                                                                     ) \
    /* 是否支持彩色打印 */                                                                                                \
    UINT32_VALUE_OUT( support_color             , 0                                                                     ) \
    /* 硬件是否支持有线网络 */                                                                                            \
    UINT32_VALUE_OUT( support_wired             , 0                                                                     ) \
    /* 硬件是否支持无线网络 */                                                                                            \
    UINT32_VALUE_OUT( support_wifi              , 0                                                                     ) \
    /* 打印超时时间 */                                                                                                    \
    UINT32_VALUE_OUT( io_timeout                , 0                                                                     ) \
    /* 打印语言 */                                                                                                        \
    UINT32_VALUE_OUT( print_language            , 0                                                                     ) \
    /* 打印速度 */                                                                                                        \
    UINT32_VALUE_OUT( print_speed               , 0                                                                     ) \
    /* 省墨设置 */                                                                                                        \
    UINT32_VALUE_OUT( save_toner                , 0                                                                     ) \
    /* 日期格式 */                                                                                                        \
    UINT32_VALUE_OUT( date_format               , 0                                                                     ) \
    /* 时间格式 */                                                                                                        \
    UINT32_VALUE_OUT( time_format               , 0                                                                     ) \
    /* 系统音量 */                                                                                                        \
    UINT32_VALUE_OUT( sys_volume                , 0                                                                     ) \
    /* 屏幕背光亮度 */                                                                                                    \
    UINT32_VALUE_OUT( lcd_backlight             , 0                                                                     ) \
    /* 文印SDK使能 */                                                                                                     \
    UINT32_VALUE_OUT( prnsdk_enabled            , 0                                                                     ) \
    /* 页数统计 */                                                                                                        \
    UINT32_ARRAY_OUT( page_counter              , 0                                 , PC_IDX_NUM                        ) \
                                                                                                                          \
    /* 休眠时间 */                                                                                                        \
    UINT32_VALUE_OUT( sleep_time                , 0                                                                     ) \
    /* 休眠模式 */                                                                                                        \
    UINT32_VALUE_OUT( sleep_mode                , 0                                                                     ) \
    /* 彩色复印管控开关 */                                                                                                \
    UINT32_VALUE_OUT( copy_color_ctrl           , 0                                                                     ) \
    /* 彩色复印密码 */                                                                                                    \
    STRING_VALUE_OUT( copy_color_pswd           , 0                                 , 64                                ) \
                                                                                                                          \
    /* USB HID开关 */                                                                                                     \
    UINT32_VALUE_OUT( usb_hid_switch            , 0                                                                     ) \
    /* USB Device是否有连接 */                                                                                            \
    UINT32_VALUE_OUT( usb_dev_status            , 0                                                                     ) \
                                                                                                                          \
    /* 纸盒是否安装 */                                                                                                    \
    UINT32_ARRAY_OUT( tray_install              , 0                                 , TRAY_INPUT_MULTIFUNCTION+1        ) \
    /* 纸盒是否放纸 */                                                                                                    \
    UINT32_ARRAY_OUT( tray_status               , 0                                 , TRAY_INPUT_MULTIFUNCTION+1        ) \
    /* 纸盒纸张尺寸 */                                                                                                    \
    UINT32_ARRAY_OUT( paper_size                , 0                                 , TRAY_INPUT_MULTIFUNCTION+1        ) \
    /* 纸盒纸张类型 */                                                                                                    \
    UINT32_ARRAY_OUT( paper_type                , 0                                 , TRAY_INPUT_MULTIFUNCTION+1        ) \
                                                                                                                          \
    /* 碳粉盒序列号 */                                                                                                    \
    STRING_ARRAY_OUT( tb_serial                 , 0                                 , MARKER_NUM    , 32                ) \
    /* 碳粉盒型号 */                                                                                                      \
    STRING_ARRAY_OUT( tb_model                  , 0                                 , MARKER_NUM    , 32                ) \
    /* 碳粉盒最大打印页数 */                                                                                              \
    UINT32_ARRAY_OUT( tb_maxpages               , 0                                 , MARKER_NUM                        ) \
    /* 碳粉盒余量 */                                                                                                      \
    UINT32_ARRAY_OUT( tb_remain                 , 0                                 , MARKER_NUM                        ) \
    /* 碳粉盒状态 */                                                                                                      \
    UINT32_ARRAY_OUT( tb_status                 , 0                                 , MARKER_NUM                        ) \
                                                                                                                          \
    /* 鼓组件序列号 */                                                                                                    \
    STRING_ARRAY_OUT( dr_serial                 , 0                                 , MARKER_NUM    , 32                ) \
    /* 鼓组件型号 */                                                                                                      \
    STRING_ARRAY_OUT( dr_model                  , 0                                 , MARKER_NUM    , 32                ) \
    /* 鼓组件最大打印页数 */                                                                                              \
    UINT32_ARRAY_OUT( dr_maxpages               , 0                                 , MARKER_NUM                        ) \
    /* 鼓组件余量 */                                                                                                      \
    UINT32_ARRAY_OUT( dr_remain                 , 0                                 , MARKER_NUM                        ) \
    /* 鼓组件状态 */                                                                                                      \
    UINT32_ARRAY_OUT( dr_status                 , 0                                 , MARKER_NUM                        ) \
                                                                                                                          \
    /* 显影组件余量 */                                                                                                    \
    UINT32_ARRAY_OUT( dv_remain                 , 0                                 , MARKER_NUM                        ) \
    /* 转印辊余量 */                                                                                                      \
    UINT32_VALUE_OUT( xfer_roller_remain        , 0                                                                     ) \
    /* 转印带余量 */                                                                                                      \
    UINT32_VALUE_OUT( xfer_belt_remain          , 0                                                                     ) \
    /* 定影器余量 */                                                                                                      \
    UINT32_VALUE_OUT( fuser_remain              , 0                                                                     ) \
    /* 废粉瓶状态 */                                                                                                      \
    UINT32_VALUE_OUT( wtb_status                , 0                                                                     ) \
                                                                                                                          \
    /* ipp装订信息 */                                                                                                     \
    BUFFER_BYTES_OUT( ipp_finisher_info         , 0                                 , 512                               ) \
                                                                                                                          \
    /* 打印机静态特性 */                                                                                                  \
    BUFFER_BYTES_OUT( static_feature            , 0                                 , 0                                 ) \
    /* 打印机动态特性 */                                                                                                  \
    BUFFER_BYTES_OUT( dynamic_feature           , 0                                 , 0                                 ) \
    /* 审计作业信息 */                                                                                                    \
    BUFFER_BYTES_OUT( audit_jobs_info           , 0                                 , 0                                 ) \
    /* 唯一序列号 */                                                                                                      \
    STRING_VALUE_OUT( sole_serial_number        , 0                                 , 32                                ) \
    /* 600dpi校准曲线 */                                                                                                  \
    BUFFER_BYTES_OUT( trc_600_info              , 0                                 , 0                                 ) \
    /* 1200dpi校准曲线 */                                                                                                 \
    BUFFER_BYTES_OUT( trc_1200_info             , 0                                 , 0                                 ) \
    /* 2400dpi校准曲线 */                                                                                                 \
    BUFFER_BYTES_OUT( trc_2400_info             , 0                                 , 0                                 ) \
    /* 标识是哪个用户登录 */                                                                                                      \
    UINT32_VALUE_OUT( web_login_sit             , 0                                                                     ) \
    /* 安全管理密码 */                                                                                                       \
    STRING_VALUE_OUT( secure_password           , 0                                 , 32                                ) \
    /* 三员管理用户ID */                                                                                                      \
    UINT32_VALUE_OUT( web_admin_id              , 0                                                                     ) \
    /* 出错作业处理模式 */                                                                                                      \
    UINT32_VALUE_OUT( err_deal_mode             , 0                                                                     ) \
    /* 出错作业延时删除时间 */                                                                                                     \
    UINT32_VALUE_OUT( err_deal_time             , 0                                                                     ) \
    /* 面板板卡序列号 */                                                                                                      \
    STRING_VALUE_OUT( bord_id                   , 0                                 , 32                                ) \
    /* 面板固件版本号 */                                                                                                     \
    STRING_VALUE_OUT( panl_fw_ver               , 0                                 , 32                                )

#define NET_DATA_UINT32_ARRAY_FUNCTION_DECLARE(member)                                              \
    uint32_t    netdata_get_ ##member  (DATA_MGR_S* thiz, uint32_t index);                          \
    int32_t     netdata_set_ ##member  (DATA_MGR_S* thiz, uint32_t index, uint32_t val);

#define NET_DATA_UINT32_VALUE_FUNCTION_DECLARE(member)                                              \
    uint32_t    netdata_get_ ##member  (DATA_MGR_S* thiz);                                          \
    int32_t     netdata_set_ ##member  (DATA_MGR_S* thiz, uint32_t val);

#define NET_DATA_STRING_ARRAY_FUNCTION_DECLARE(member)                                              \
    int32_t     netdata_get_ ##member  (DATA_MGR_S* thiz, uint32_t index, char* str, size_t size);  \
    int32_t     netdata_set_ ##member  (DATA_MGR_S* thiz, uint32_t index, const char* str);

#define NET_DATA_STRING_VALUE_FUNCTION_DECLARE(member)                                              \
    int32_t     netdata_get_ ##member  (DATA_MGR_S* thiz, char* str, size_t size);                  \
    int32_t     netdata_set_ ##member  (DATA_MGR_S* thiz, const char* str);

#define NET_DATA_BUFFER_BYTES_FUNCTION_DECLARE(member)                                              \
    int32_t     netdata_get_ ##member  (DATA_MGR_S* thiz, void* buf, size_t size);                  \
    int32_t     netdata_set_ ##member  (DATA_MGR_S* thiz, void* buf, size_t size);

#define out1(_member, _nvid, _i)        NET_DATA_UINT32_ARRAY_FUNCTION_DECLARE(_member)
#define out2(_member, _nvid)            NET_DATA_UINT32_VALUE_FUNCTION_DECLARE(_member)
#define out3(_member, _nvid, _i, _s)    NET_DATA_STRING_ARRAY_FUNCTION_DECLARE(_member)
#define out4(_member, _nvid, _s)        NET_DATA_STRING_VALUE_FUNCTION_DECLARE(_member)
#define out5(_member, _nvid, _s)        NET_DATA_BUFFER_BYTES_FUNCTION_DECLARE(_member)
NET_DATA_MEMBER_TABLE(out1, out2, out3, out4, out5)
#undef  out1
#undef  out2
#undef  out3
#undef  out4
#undef  out5

/**
 * @brief       Get the network interface index by the ipv4 address.
 * @param[in]   thiz    : The DATA_MGR_S object pointer.
 * @param[in]   ipstr   : The DATA_MGR_S object pointer.
 * @return      The network interface index.
 * @retval      >=0     : The value in IFACE_ID_E\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
IFACE_ID_E      netdata_get_ifid_by_ipv4_addr           (DATA_MGR_S* thiz, const char* ipstr);

/**
 * @brief       Get the network interface index by the ipv4 address.
 * @param[in]   thiz    : The DATA_MGR_S object pointer.
 * @param[in]   ipstr   : The DATA_MGR_S object pointer.
 * @return      The network interface index.
 * @retval      >=0     : The value in IFACE_ID_E\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
IFACE_ID_E      netdata_get_ifid_by_ipv6_addr           (DATA_MGR_S* thiz, const char* ipstr);

uint32_t        netdata_get_netlink_flags               (DATA_MGR_S* thiz);

/**
 * @brief       Destroy DATA_MGR_S object.
 * @param[in]   thiz    : The DATA_MGR_S object pointer.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netdata_manager_destroy                 (DATA_MGR_S* thiz);

/**
 * @brief       Create DATA_MGR_S object.
 * @return      The network interface index.
 * @retval      !=NULL  : The DATA_MGR_S object pointer\n
 *              ==NULL  : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
DATA_MGR_S*     netdata_manager_create                  (void);

PT_END_DECLS

#endif /* __NETDATA_H__ */
/**
 *@}
 */
