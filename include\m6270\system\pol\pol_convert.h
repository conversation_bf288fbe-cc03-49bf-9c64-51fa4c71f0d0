/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_convert.h
 * @addtogroup convert
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal data format convert interface set
 */
#ifndef __POL_CONVERT_H__
#define __POL_CONVERT_H__
#include <stdlib.h>
#include <inttypes.h>
#include <ctype.h>
#include <arpa/inet.h>
#include <pol/pol_define.h>
PT_BEGIN_DECLS

#define pi_strtol			strtol
#define pi_strtoll          strtoll
#define pi_strtoul          strtoul
#define pi_strtoull         strtoull
#define pi_strtod           strtod
#define pi_strtof           strtof
#define pi_strtold          strtold
#define pi_strtoimax        strtoimax
#define pi_strtoumax        strtoumax
#define pi_tolower          tolower
#define pi_toupper          toupper
#define pi_htonl            htonl
#define pi_htons            htons
#define pi_ntohl            ntohl
#define pi_ntohs            ntohs

PT_END_DECLS
#endif

/**
 *@}
 */
