#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "PEDK_event.h"

#include <quickjs.h>

#include "PEDK_net_server.h"
#include "pedk_addressbook.h"
//#include "PEDK_net_ssl.h"
//#include "PEDK_net_email.h"
//#include "PEDK_net_ftp.h"
//#include "PEDK_net_smb.h"
//#include "PEDK_net_setting.h"

#define countof(x) (sizeof(x) / sizeof((x)[0]))

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;


/* 定义API的函数入口名称及列表 */
static const JSCFunctionList PEDK_net_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */
    //email_addressbook
    {"add_email_addr", 1, js_add_email_addr},
    {"get_email_addr", 1, js_get_email_addr},
    {"get_email_addr_list", 0, js_get_email_addr_list},
    {"get_email_addr_num", 0, js_get_email_addr_num},
    {"is_email_addr_full", 0, js_is_email_addr_full},
    {"modify_email_addr", 1, js_modify_email_addr},
    {"remove_email_addr", 1, js_remove_email_addr},

    //group_addressbook
    {"add_email_to_group", 2, js_add_email_to_group},
    {"creat_email_group", 1, js_creat_email_group},
    {"get_email_group", 1, js_get_email_group},
    {"get_email_group_list", 0, js_get_email_group_list},
    {"get_email_group_num", 0, js_get_email_group_num},
    {"is_email_group_full", 0, js_is_email_group_full},
    {"modify_email_group", 2, js_modify_email_group},
    {"remove_email_from_group", 2, js_remove_email_from_group},
    {"remove_email_group", 1, js_remove_email_group},

    //ftp_addressbook
    {"add_ftp_addr", 1, js_add_ftp_addr},
    {"get_ftp_addr", 1, js_get_ftp_addr},
    {"get_ftp_addr_list", 0, js_get_ftp_addr_list},
    {"get_ftp_addr_num", 0, js_get_ftp_addr_num},
    {"is_ftp_addr_full", 0, js_is_ftp_addr_full},
    {"modify_ftp_addr", 1, js_modify_ftp_addr},
    {"remove_ftp_addr", 1, js_remove_ftp_addr},

    //smb_addressbook
    {"add_smb_addr", 1, js_add_smb_addr},
    {"get_smb_addr", 1, js_get_smb_addr},
    {"get_smb_addr_list", 0, js_get_smb_addr_list},
    {"get_smb_addr_num", 0, js_get_smb_addr_num},
    {"is_smb_addr_full", 0, js_is_smb_addr_full},
    {"modify_smb_addr", 1, js_modify_smb_addr},
    {"remove_smb_addr", 1, js_remove_smb_addr},

// //email_send
// {"send_email", 1, js_send_email},
// //ftp_connect -disconnect
// {"ftp_connect", 1, js_ftp_connect},
// {"ftp_disconnect", 0, js_ftp_disconnect},
// //smb_connect -disconnect
// {"smb_connect", 1, js_smb_connect},
// {"smb_disconnect", 0, js_smb_disconnect},
//
// //netsetting
// {"get_smtp_email_encrypt_mode", 0, js_get_smtp_email_encrypt_mode},
// {"get_smtp_login_name", 0, js_get_smtp_login_name},
// {"get_smtp_login_pwd", 0, js_get_smtp_login_pwd},
// {"get_smtp_port", 0, js_get_smtp_port},
// {"get_smtp_server_name", 0, js_get_smtp_server_name},
// {"set_smtp_email_encrypt_mode", 1, js_set_smtp_email_encrypt_mode},
// {"set_smtp_login_name", 1, js_set_smtp_login_name},
// {"set_smtp_login_pwd", 1, js_set_smtp_login_pwd},
// {"set_smtp_port", 1, js_set_smtp_port},
// {"set_smtp_server_name", 1, js_set_smtp_server_name},
// {"get_wired_connect_info", 0, js_get_wired_connect_info},
// {"get_wired_mac_addr_info", 0, js_get_wired_mac_addr_info},
// {"get_wired_net_802_protocol_switch", 0, js_get_wired_net_802_protocol_switch},
// {"get_wired_net_ipv4_addr", 0, js_get_wired_net_ipv4_addr},
// {"get_wired_net_ipv4_gateway", 0, js_get_wired_net_ipv4_gateway},
// {"get_wired_net_ipv4_hostname", 0, js_get_wired_net_ipv4_hostname},
// {"get_wired_net_ipv4_mask", 0, js_get_wired_net_ipv4_mask},
// {"get_wired_net_ipv4_mode", 0, js_set_wired_net_802_protocol_switch},
// {"set_wired_net_802_protocol_switch", 1, js_set_smtp_port},
// {"set_wired_net_ipv4_addr", 1, js_set_wired_net_ipv4_addr},
// {"set_wired_net_ipv4_hostname", 1, js_set_wired_net_ipv4_hostname},
// {"set_wired_net_ipv4_mode", 1, js_set_wired_net_ipv4_mode},
// {"set_wired_net_ipv6_switch", 1, js_set_wired_net_ipv6_switch},

//    {"generate_cert", 1, js_generate_cert},
//    {"loadssl_cert", 3, js_loadssl_cert},
//    {"createssl_conn", 1, js_createssl_conn},
//    {"fetch_data", 2, js_fetch_data},
//    {"append_httpheader", 2, js_append_httpheader},
//    {"create_httpheader", 2, js_create_httpheader},
//    {"create_httpbody", 2, js_create_httpbody},
//    {"create_httpbody", 2, js_create_httpbody},
//    {"create_httprequest", 2, js_create_httprequest},
//    {"delete_httpheader", 2, js_delete_httpheader},
//    {"get_httpheader", 2, js_get_httpheader},
//    {"get_all_keys", 2, js_get_all_keys},
//    {"get_all_values", 2, js_get_all_values},
//    {"get_set_cookie", 2, js_get_set_cookie},
//    {"set_httpheader", 2, js_set_httpheader},
//    {"set_timeout", 1, js_set_Timeout},
//    {"get_timeout", 2, js_getTimeout},
//    {"ssl_recv", 1, js_ssl_recv},
//    {"ssl_send", 1, js_ssl_send},
//    {"ssl_close", 1, js_ssl_close},
//    {"create_tcpsocket", 1, js_create_tcpsocket},
//    {"create_tcpconn", 2, js_create_tcpconn},
//    {"tcp_recv", 1, js_tcp_recv},
//    {"tcp_send", 1, js_tcp_send},
//    {"tcp_close", 1, js_tcp_close},
//    {"create_udpsocket", 1, js_create_udpsocket},
//    {"create_udpconn", 1, js_create_udpconn},
//    {"udp_send", 1, js_udp_send},
//    {"udp_recv", 1, js_udp_recv},
//    {"udp_close", 1, js_udp_close},

};
///
const JSCFunctionList* get_net_JSCFunctionList(int *length) {
	*length = countof(PEDK_net_funcs);
	return PEDK_net_funcs;
}

int js_net_init(JSContext *ctx, JSValueConst global)
{
    int i = 0;

    printf("*********start net module*******\n");
    /* creat the classes */
    int count = 0;
    const JSCFunctionList* PEDK_funcs = get_net_JSCFunctionList(&count);
    printf("count:%d\n",count);
    for(i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, PEDK_funcs[i].name,
                            JS_NewCFunction(ctx, PEDK_funcs[i].func, PEDK_funcs[i].name, PEDK_funcs[i].length));

    }
    printf("*********start net init end*******\n");
    return 0;
}


