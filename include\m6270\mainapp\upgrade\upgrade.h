/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file upgrade.h
 * @addtogroup upgrade 
 * @{
 * @brief register upgrade acl cmd
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-11-03
 */

#ifndef _UPGRADE_H
#define _UPGRADE_H

struct upgrade_record
{
    time_t start;
    time_t end;
    int result;
    char fwversion[32];
};

/**
 * @brief upgrade initialization 
 */
void upgrade_prolog(void);

/**
 * @brief upgrade deinitialization
 */
void upgrade_epilog(void);

#endif //_UPGRADE_H
