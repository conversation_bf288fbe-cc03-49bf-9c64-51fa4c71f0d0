/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file main.c
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2023-07-13
 * @brief binder service manager base on AOSP
 */

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_string.h"
#include "pol/pol_list.h"
#include "pol/pol_mem.h"
#include "pol/pol_io.h"
#include "pol/pol_time.h"
#include "cbinder/cbinder.h"
#include <pthread.h>
#include <errno.h>
#include <fcntl.h>

#define LOGI(x...) fprintf(stdout, "svcmgr: " x)
#define LOGE(x...) fprintf(stderr, "svcmgr: " x)

#define AID_MEDIA   1
#define AID_DRM     1
#define AID_NFC     1
#define AID_RADIO   1
#define AID_SYSTEM  1
#define AID_DRMIO   1
#define AID_MAINAPP 999

/* TODO:
 * These should come from a config file or perhaps be
 * based on some namespace rules of some sort (media
 * uid can register media.*, etc)
 */
static struct {
    unsigned uid;
    const char *name;
} allowed[] = {
#ifdef LVMX
    { AID_MEDIA, "com.lifevibes.mx.ipc" },
#endif
    { AID_MEDIA, "media.audio_flinger" },
    { AID_MEDIA, "media.player" },
    { AID_MEDIA, "media.camera" },
    { AID_MEDIA, "media.audio_policy" },
    { AID_DRMIO, "drm.drmIOService" },
    { AID_DRM,   "drm.drmManager" },
    { AID_NFC,   "nfc" },
    { AID_RADIO, "radio.phone" },
    { AID_RADIO, "radio.sms" },
    { AID_RADIO, "radio.phonesubinfo" },
    { AID_RADIO, "radio.simphonebook" },
/* TODO: remove after phone services are updated: */
    { AID_RADIO, "phone" },
    { AID_RADIO, "sip" },
    { AID_RADIO, "isms" },
    { AID_RADIO, "iphonesubinfo" },
    { AID_RADIO, "simphonebook" },
    { AID_MAINAPP,"MAINAPP"},
};

void *svcmgr_handle;

const char *str8(uint16_t *x)
{
    static char buf[128];
    unsigned max = 127;
    char *p = buf;

    if (x) {
        while (*x && max--) {
            *p++ = *x++;
        }
    }
    *p++ = 0;
    return buf;
}

int str16eq(uint16_t *a, const char *b)
{
    while (*a && *b)
        if (*a++ != *b++) return 0;
    if (*a || *b)
        return 0;
    return 1;
}

int svc_can_register(unsigned uid, uint16_t *name)
{
    unsigned n;
    
    if ((uid == 0) || (uid == AID_SYSTEM))
        return 1;

    for (n = 0; n < sizeof(allowed) / sizeof(allowed[0]); n++)
        if (str16eq(name, allowed[n].name)){
            if (uid == allowed[n].uid)
                return 1;
            else
                return 0;
        }

    return 1;
}

struct svcinfo 
{
    struct list_head list;
    void *ptr;
    struct binder_death death;
    unsigned len;
    uint16_t name[0];
};

static struct svcinfo svclist;
static pthread_mutex_t svc_mutex = PTHREAD_MUTEX_INITIALIZER;

struct svcinfo *find_svc(uint16_t *s16, unsigned len)
{
    struct svcinfo *tmp;
    struct svcinfo *si = NULL;
    struct list_head *pos;
    pthread_mutex_lock(&svc_mutex);
    pi_list_for_each(pos, &svclist.list){
        tmp = pi_list_entry(pos, struct svcinfo, list);
        if ((len == tmp->len) &&
            !memcmp(s16, tmp->name, len * sizeof(uint16_t))) {
            si = tmp;
            break;
        }
    }
    pthread_mutex_unlock(&svc_mutex);
    return si;
}

void del_svc(struct svcinfo *si)
{
    struct svcinfo *tmp;
    struct list_head *pos, *q;
    pthread_mutex_lock(&svc_mutex);
    pi_list_for_each_safe(pos, q, &svclist.list){
        tmp = pi_list_entry(pos, struct svcinfo, list);
        if (tmp == si) {
            pi_list_del_entry(pos);
            free(tmp);
            break;
        }
    }
    pthread_mutex_unlock(&svc_mutex);
}

void add_svc(struct svcinfo *si)
{
    pthread_mutex_lock(&svc_mutex);
    pi_list_add_tail(&(si->list), &svclist.list);
    pthread_mutex_unlock(&svc_mutex);
}

void svcinfo_death(struct binder_state *bs, void *ptr)
{
    struct svcinfo *si = ptr;
    LOGI("service '%s' died\n", str8(si->name));
    if (si->ptr) {
        binder_release(bs, si->ptr);
        si->ptr = 0;
    }
    del_svc(si);
}

uint16_t svcmgr_id[] = { 
    'a','n','d','r','o','i','d','.','o','s','.',
    'I','S','e','r','v','i','c','e','M','a','n','a','g','e','r' 
};
  

void *do_find_service(struct binder_state *bs, uint16_t *s, unsigned len)
{
    struct svcinfo *si;
    si = find_svc(s, len);

//    LOGI("check_service('%s') ptr = %p\n", str8(s), si ? si->ptr : 0);
    if (si && si->ptr) {
        return si->ptr;
    } else {
        return 0;
    }
}

int do_add_service(struct binder_state *bs, uint16_t *s, unsigned len,
                   void *ptr, unsigned uid)
{
    struct svcinfo *si;
//    LOGI("add_service('%s',%p) uid=%d\n", str8(s), ptr, uid);

    if (!ptr || (len == 0) || (len > 127))
        return -1;

    if (!svc_can_register(uid, s)) {
        LOGE("add_service('%s',%p) uid=%d - PERMISSION DENIED\n",
             str8(s), ptr, (int32_t)uid);
        return -1;
    }

    si = find_svc(s, len);
    if (si) {
        if (si->ptr) {
            LOGE("add_service('%s',%p) uid=%d - ALREADY REGISTERED\n",
                 str8(s), ptr, (int32_t)uid);
            return -1;
        }
        si->ptr = ptr;
    } else {
        si = malloc(sizeof(*si) + (len + 1) * sizeof(uint16_t));
        if (!si) {
            LOGE("add_service('%s',%p) uid=%d - OUT OF MEMORY\n",
                 str8(s), ptr, (int32_t)uid);
            return -1;
        }
        si->ptr = ptr;
        si->len = len;
        memcpy(si->name, s, (len + 1) * sizeof(uint16_t));
        si->name[len] = '\0';
        si->death.func = svcinfo_death;
        si->death.ptr = si;
        
        add_svc(si);
        LOGI("add_service('%s',%p) uid=%d successful\n",
                 str8(s), ptr, (int32_t)uid);
    }

    binder_acquire(bs, ptr);
    binder_link_to_death(bs, ptr, &si->death);
    return 0;
}

int do_del_service(struct binder_state *bs, uint16_t *s, unsigned len,
                   void *ptr)
{
    struct svcinfo *si;
    if (!ptr || (len == 0) || (len > 127))
        return -1;
    si = find_svc(s, len);
    if (si && si->ptr == ptr) {
        svcinfo_death(bs, si);
        return 0;
    }
    else {
        return -1;
    }
}

int svcmgr_handler(struct binder_state *bs,
                   struct binder_txn *txn,
                   struct binder_io *msg,
                   struct binder_io *reply,
                   void *data)
{
    struct svcinfo *si;
    uint16_t *s;
    unsigned len;
    void *ptr;
    uint32_t strict_policy;

//    LOGI("target=%p code=%d pid=%d uid=%d\n",
//         txn->target, txn->code, txn->sender_pid, txn->sender_euid);

    if (txn->target != svcmgr_handle)
        return -1;

    // Equivalent to Parcel::enforceInterface(), reading the RPC
    // header with the strict mode policy mask and the interface name.
    // Note that we ignore the strict_policy and don't propagate it
    // further (since we do no outbound RPCs anyway).
    strict_policy = bio_get_uint32(msg);
    s = bio_get_string16(msg, &len);
    if ((len != (sizeof(svcmgr_id) / 2)) ||
        memcmp(svcmgr_id, s, sizeof(svcmgr_id))) {
        fprintf(stderr,"invalid id %s\n", str8(s));
        return -1;
    }

    switch(txn->code) {
    case SVC_MGR_GET_SERVICE:
    case SVC_MGR_CHECK_SERVICE:
        s = bio_get_string16(msg, &len);
        ptr = do_find_service(bs, s, len);
        if (!ptr)
            break;
        bio_put_ref(reply, ptr);
        return 0;

    case SVC_MGR_ADD_SERVICE:
        s = bio_get_string16(msg, &len);
        ptr = bio_get_ref(msg);
        if (do_add_service(bs, s, len, ptr, txn->sender_euid))
            return -1;
        break;

    case SVC_MGR_LIST_SERVICES: {
        unsigned n = bio_get_uint32(msg);
        struct svcinfo *si = NULL;
        struct list_head *pos;
        
        pthread_mutex_lock(&svc_mutex);
        pi_list_for_each(pos, &svclist.list){
            si = pi_list_entry(pos, struct svcinfo, list);
            if (n-- <= 0) {
                break;
            }
        }
        pthread_mutex_unlock(&svc_mutex);

        if (si) {
            bio_put_string16(reply, si->name);
            return 0;
        }
        return -1;
    }
    case SVC_MGR_DEL_SERVICE: {
        s = bio_get_string16(msg, &len);
        ptr = bio_get_ref(msg);
        if (do_del_service(bs, s, len, ptr))
            return -1;
        break;
    }
    default:
        LOGE("unknown code %u\n", txn->code);
        return -1;
    }

    bio_put_uint32(reply, 0);
    return 0;
}

int touch(const char *path)
{
    int fd;
    fd = open(path, O_WRONLY|O_CREAT, 0000);
    close(fd);
    return 0;
}

int main(int argc, char **argv)
{
    void *svcmgr = BINDER_SERVICE_MANAGER;
    struct binder_state *bs;
    
    pi_init_list_head(&svclist.list);

    bs = binder_start(1);

    if (binder_become_context_manager(bs)) {
        LOGE("cannot become context manager (%s)\n", strerror(errno));
        return -1;
    }

    svcmgr_handle = svcmgr;
    binder_add_target(bs, svcmgr, svcmgr_handler, 0);
    
    touch("/tmp/.BINDER_SERVICE_MANAGER_DONE");
    while(1) {
        sleep(100000);
    }
    binder_stop(bs);
    return 0;
}
