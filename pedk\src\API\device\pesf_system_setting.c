#include "pesf_system_setting.h"
#include "PEDK_event.h"

#define countof(x) (sizeof(x) / sizeof((x)[0]) )
#define Log(format, ...) printf("[setting] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

/* 自定义原生C函数 */
//static void xxx()
//{
//    return;
//}

/*
    定义 QuickJS C 函数
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv)
//{
//    xxx();
//    return JS_NewString(ctx, "OK");
//}

JSValue js_get_current_language(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char receive_data[10];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("js_get_current_language\n");
    memset(receive_data, 0, sizeof(receive_data));
    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_LANGUAGE, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_GET_LANGUAGE, &respond, receive_data, &receive_cnt, 3);
    printf("[pedk-productinfo] recv from mfp datat :current_language[%s],respond %d\n", receive_data,respond);
    uint32_t language_code = *(uint32_t *)receive_data;
    printf("gett---> language_code %ud\n", language_code);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewInt32(ctx, language_code);
    }

}

JSValue js_set_current_language(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int32_t respond = -1;

    printf("js_set_current_language\n");
    if (!JS_IsNumber(argv[0]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }
    uint32_t language_set = 0;
    JS_ToInt32(ctx, &language_set, argv[0]);
    printf("set language %ud\n", language_set);

    if (language_set < 1 || language_set > 28) {
        printf("Invalid encapsulated language type: %u\n", language_set);
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_SET_LANGUAGE, 0, sizeof(language_set),  (const unsigned char *)&language_set);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_PRODUCTINFO_SET_LANGUAGE, &respond, NULL, NULL, 3);

    return JS_NewString(ctx, "EXIT_SUCCESS");

}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_system_setting_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    /*system setting */
    {"get_current_language", 0, js_get_current_language},
    {"set_current_language", 1, js_set_current_language},
};

const JSCFunctionList* getSystemSettingJSCFunctionList(int *length)
{
    *length = countof(pesf_system_setting_funcs);
    return pesf_system_setting_funcs;
}

int js_system_setting_init(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;

   Log("*********start system setting module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = getSystemSettingJSCFunctionList(&count);
   Log("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   Log("*********start system setting init end**********\n");
   return 0;
}

