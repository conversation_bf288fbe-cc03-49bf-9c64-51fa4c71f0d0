#!/bin/bash

# 输出菜单标题
echo "#"
echo "# For a description of the syntax of this configuration file,"
echo "# see Documentation/kbuild/kconfig-language.txt."
echo "#"
echo ""
echo "mainmenu \"Mainapp Configuration\""

# 遍历configs目录下所有.kconfig文件
for file in $(find configs/kconfigs -name '*.kconfig' | sort)
do
	# 获取文件名，去掉路径和扩展名
	filename=$(basename "$file" .kconfig)
	
	# 输出菜单和源文件路径
	echo -e "\nmenu \"$filename\""
	echo -e "\tsource \"$file\""
	echo -e "endmenu"
done