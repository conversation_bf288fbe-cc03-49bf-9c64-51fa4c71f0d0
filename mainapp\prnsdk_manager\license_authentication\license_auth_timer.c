/**
 * @file license_auth_timer.c
 * @brief License authentication timer implementation.
 * @details Implements timer and threading operations for license authentication, including periodic time reduction and license expiration handling.
 * @addtogroup license_authentication
 * @{
 * @modifier yangzikun
 * @date 2024-12-11
 */

#include "license_auth_timer.h"

/**
 * @var license_auth_timer_cuttime
 * @brief Timer for periodic license time reduction.
 */
static timer_t license_auth_timer_cuttime;

/**
 * @var license_auth_thread_cuttime
 * @brief Thread for managing the cut-time timer.
 */
static pthread_t license_auth_thread_cuttime;

/**
 * @brief Timer callback for reducing license time.
 * @details This callback is triggered periodically to check and reduce the license expiration time. If the license expires, it removes the license and reboots the system.
 * @param[in] a Timer callback argument (not used).
 */
static void license_auth_cut_time(union sigval a)
{
    char buf[64] = {0};
    long long time_val = license_auth_check_time(LICENSE_AUTH_TIME_LIMITATION_FILE);
    truncate(LICENSE_AUTH_TIME_LIMITATION_FILE, 0);
    pi_log_d("Cutting time!\n");
    if (time_val > LICENSE_AUTH_PERIOD_TIME) {
        time_val -= LICENSE_AUTH_PERIOD_TIME;
        sprintf(buf, "%lld", time_val);
        license_auth_write_to_file(buf, LICENSE_AUTH_TIME_LIMITATION_FILE);
    }
	else
	{
        pi_log_d("License expired, removing!\n");
        license_auth_remove_sdk_license();
        timer_delete(license_auth_timer_cuttime);
        pthread_cancel(license_auth_thread_cuttime);
        system("reboot"); // Per requirements, reboot on expiration
    }
}

/**
 * @brief 配置并启动定时器任务。
 *
 * 此函数创建并配置一个定时器，以周期性触发指定的回调函数。
 *
 * @param timer_id 定时器 ID 的指针，用于存储新创建的定时器句柄。
 * @param start_sec 定时器的初始延迟时间（秒）。
 * @param period_sec 定时器的周期时间（秒）。如果为 0，则定时器不会周期性触发。
 * @param timer_callback 定时器触发时调用的回调函数。
 * @param value 传递给回调函数的用户定义数据。
 * @return 成功返回 0；失败返回 -1。
 */
static int32_t license_auth_timer_task(timer_t *timer_id, unsigned long start_sec, unsigned long period_sec, void (*timer_callback)(union sigval), void *value) {
    if (period_sec == 0)
	{
        pi_log_d("Error: period_sec cannot be zero.\n");
        return 0;
    }

    struct sigevent sevp = {0};
    struct itimerspec its = {0};

    sevp.sigev_notify = SIGEV_THREAD;
    sevp.sigev_notify_attributes = NULL;
    sevp.sigev_value.sival_ptr = timer_id;
    sevp.sigev_notify_function = timer_callback;

    if (timer_create(CLOCK_MONOTONIC, &sevp, timer_id) == -1)
	{
        pi_log_d("Timer error!\n");
        return -1;
    }

    its.it_value.tv_sec = start_sec;
    its.it_value.tv_nsec = 0;
    its.it_interval.tv_sec = period_sec;
    its.it_interval.tv_nsec = 0;
    if(timer_settime(*timer_id, 0, &its, NULL) == -1)
    {
        pi_log_d("Set time error!\n");
        return -1;
    }

    while (1)
	{
        sleep(1);
    }
    return 0;
}

/**
 * @brief Starts a reboot thread.
 * @details Creates a thread to manage system reboot operations.
 * @param[in] str Pointer to thread data (not used in this function).
 * @return Always returns NULL.
 */
static void *license_auth_start_pthread_reboot(void *str)
{
    LICENSE_AUTH_PT_CB_S *value = (LICENSE_AUTH_PT_CB_S *)str;
    pi_log_d("In license_auth_start_pthread_reboot!\n");

    return NULL;
}

/**
 * @brief Checks the expiration time from a file.
 * @details Reads the expiration time from the specified file and converts it to a long long value.
 * @param[in] infile Path to the file containing the expiration time.
 * @return The expiration time as a long long value. Returns 0 on error or if the time is invalid.
 */
long long license_auth_check_time(const char *infile)
{
    if (infile == NULL) {
        pi_log_d("Error: infile is NULL.\n");
        return 0; // 返回默认值，表示无效时间
    }

    char *time_str = license_auth_get_string(infile);
    if (time_str == NULL) {
        pi_log_d("Error: Failed to retrieve string from infile.\n");
        return 0; // 返回默认值，表示无效时间
    }

    long long time_val = atoll(time_str);
    pi_log_d("Check_Time: time = %lld\n", time_val);
    LICENSE_AUTH_SAFE_FREE(time_str);
    return time_val;
}

/**
 * @brief Starts the cut-time pthread.
 * @details Creates a thread that manages the cut-time timer, periodically reducing the license time.
 * @param[in] str Pointer to thread configuration data (`LICENSE_AUTH_PT_CB_S`).
 * @return Always returns NULL.
 */
void *license_auth_start_pthread_cuttime(void *str)
{
    LICENSE_AUTH_PT_CB_S *value = (LICENSE_AUTH_PT_CB_S *)str;
    pi_log_d("In license_auth_start_pthread_cuttime!\n");
    license_auth_timer_task(&license_auth_timer_cuttime, LICENSE_AUTH_PERIOD_TIME, LICENSE_AUTH_PERIOD_TIME, license_auth_cut_time, NULL);
    return NULL;
}

/**
 * @brief Creates a pthread for the timer.
 * @details Initializes a new thread for managing license timers.
 * @param[in] value Pointer to a `LICENSE_AUTH_PT_CB_S` structure containing thread configuration.
 * @return 0 on success, -1 on failure.
 */
int32_t license_auth_create_timer_thread(LICENSE_AUTH_PT_CB_S *value)
{
    int32_t ret = pthread_create(&license_auth_thread_cuttime, NULL, value->start_routine, value);
    if (ret != 0) {
        pi_log_d("Error: Failed to create thread. Return code: %d\n", ret);
        return -1; // 失败返回 -1
    }

    // 将线程设置为分离状态，避免资源泄漏
    ret = pthread_detach(license_auth_thread_cuttime);
    if (ret != 0) {
        pi_log_d("Error: Failed to detach thread. Return code: %d\n", ret);
        return -1; // 分离失败
    }

    return 0; // 成功返回 0
}

/**
 *@}
 */
