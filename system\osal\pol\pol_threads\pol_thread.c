/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_thread.c
 * @addtogroup threads
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief Pantum thread API functions
 */
#include <stdlib.h>
#include <stdarg.h>
#include <signal.h>
#include <sys/wait.h>

#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"

#include "pol_threads_private.h"
#include "pol_inner.h"

typedef struct pol_thread
{
    void*       os_thread;  ///< ptr to underlying os thread data
    const char* name;       ///< thread name
}
POL_THREAD_S;

static POL_THREAD_S s_thread_pool[PI_NUM_THREADS];
static void*        s_thread_mutex = NULL;

PI_THREAD_T pi_thread_create(void *(*entry)(void *), uint32_t stacksize, void* stack, uint32_t priority, void* arg, const char* name)
{
    POL_THREAD_S*   pt = NULL;

    RETURN_VAL_IF(s_thread_mutex == NULL, pi_log_e, INVALIDTHREAD);

    pol_mutex_lock(s_thread_mutex);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_thread_pool); ++i )
    {
        if ( s_thread_pool[i].os_thread == NULL )
        {
            s_thread_pool[i].os_thread = pol_thread_create(entry, stacksize, stack, priority, arg, name);
            if ( s_thread_pool[i].os_thread == NULL )
            {
                pi_log_e("pol_thread_create(%s) failed\n", name);
            }
            else
            {
                s_thread_pool[i].name = name;
                pt = &s_thread_pool[i];
            }
            break;
        }
    }
    pol_mutex_unlock(s_thread_mutex);

    if ( pt == NULL )
    {
        pi_log_e("create thread(%s) failed, threads pool may be full\n", name);
    }
    return (PI_THREAD_T)pt;
}

int32_t pi_thread_destroy(PI_THREAD_T thread)
{
    POL_THREAD_S*   pt = NULL;
    int32_t         rv = -1;

    RETURN_VAL_IF(s_thread_mutex == NULL, pi_log_e, -1);

    pol_mutex_lock(s_thread_mutex);
    if ( (POL_THREAD_S *)thread >= &s_thread_pool[0] && (POL_THREAD_S *)thread <= &s_thread_pool[PI_NUM_THREADS - 1] )
    {
        pt = (POL_THREAD_S *)thread;
    }

    if ( pt == NULL || pt->os_thread == NULL )
    {
        pi_log_e("No such thread(%p)\n", thread);
    }
    else
    {
        if ( pol_thread_is_self(pt->os_thread) )
        {
            pi_log_e("thread(%s) can't kill self\n", pt->name);
        }
        else
        {
            pol_thread_destroy(pt->os_thread);
            pt->os_thread = NULL;
            pt->name      = NULL;

            rv = 0;
        }
    }
    pol_mutex_unlock(s_thread_mutex);

    return rv;
}

int32_t pi_thread_destroy_self(void)
{
    POL_THREAD_S*   pt = NULL;
    int32_t         rv = -1;

    RETURN_VAL_IF(s_thread_mutex == NULL, pi_log_e, -1);

    pol_mutex_lock(s_thread_mutex);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_thread_pool); ++i )
    {
        if ( s_thread_pool[i].os_thread && pol_thread_is_self(s_thread_pool[i].os_thread) )
        {
            pt = &s_thread_pool[i];
            rv = 0;
            break;
        }
    }

    if ( pt == NULL )
    {
        pi_log_e("no search thread in threads pool!!!\n");
    }
    else
    {
        pol_thread_detach(pt->os_thread);
        pt->os_thread = NULL;
        pt->name      = NULL;
    }
    pol_mutex_unlock(s_thread_mutex);

    return rv;
}

PI_THREAD_T pi_thread_self(void)
{
    POL_THREAD_S*   pt = NULL;

    RETURN_VAL_IF(s_thread_mutex == NULL, pi_log_e, INVALIDTHREAD);

    pol_mutex_lock(s_thread_mutex);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_thread_pool); ++i )
    {
        if ( s_thread_pool[i].os_thread && pol_thread_is_self(s_thread_pool[i].os_thread) )
        {
            pt = &s_thread_pool[i];
            break;
        }
    }
    pol_mutex_unlock(s_thread_mutex);

    if ( pt == NULL )
    {
        pi_log_e("no search thread in threads pool!!!\n");
    }
    return (PI_THREAD_T)pt;
}

int32_t pi_thread_prolog(void)
{
    RETURN_VAL_IF(s_thread_mutex != NULL, pi_log_e, 0); /* 以互斥锁作是否初始化的标识，避免同一进程中多次调用重复初始化 */
    RETURN_VAL_IF((s_thread_mutex = pol_mutex_create()) == NULL, pi_log_e, -1);

    pol_mutex_lock(s_thread_mutex);
    memset(&s_thread_pool, 0, sizeof(s_thread_pool));
    pol_mutex_unlock(s_thread_mutex);

    return 0;
}

void pi_thread_epilog(void)
{
    RETURN_IF(s_thread_mutex == NULL, pi_log_e);

    pol_mutex_lock(s_thread_mutex);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_thread_pool); ++i )
    {
        if ( s_thread_pool[i].os_thread != NULL )
        {
            pol_thread_destroy(s_thread_pool[i].os_thread);
            s_thread_pool[i].os_thread = NULL;
            s_thread_pool[i].name      = NULL;
        }
    }
    pol_mutex_unlock(s_thread_mutex);

    pol_mutex_destroy(s_thread_mutex);
    s_thread_mutex = NULL;
}

/************************************************************************************************************/
#define RUNCMD_TIMEOUT  (200)
#define CMD_BUF_SIZE    (20 * 1024)
#define CMD_ERROR       "CMDERROR"
#define CMD_OK          "OK"

const char* pi_runcmd(char* buf, size_t nbuf, int32_t background, const char* format, ...)
{
    const char* res = CMD_ERROR;
    char cmd[CMD_BUF_SIZE];
    va_list arg;
    int32_t pout[2];
    ssize_t n;
    size_t len;
    time_t start;
    pid_t cmdpid;

    /* 生成要执行的命令 */
    va_start(arg, format);
    vsnprintf(cmd, CMD_BUF_SIZE, format, arg);
    va_end(arg);
    printf("\033[35m" "runcmd: %s" "\033[0m" "\n", cmd);

    /* 管道，用于读取命令输出 */
    if (pipe(pout) < 0)
    {
        perror("pipe:");

        if (buf != NULL)
        {
            pi_snprintf(buf, nbuf, CMD_ERROR);
            res = buf;
        }

        return res;
    }

    cmdpid = vfork();
    if (cmdpid < 0)
    {
        perror("vfork:");
        close(pout[0]);
        close(pout[1]);

        if (buf != NULL)
        {
            pi_snprintf(buf, nbuf, CMD_ERROR);
            res = buf;
        }

        return res;
    }
    else if (cmdpid == 0)
    {
        /* 置于后台运行时，在命令行的结尾处加 &，使得在通过sh执行命令时能生成一个子进程运行它 */
        if (background)
        {
            len = pi_strlen(cmd);
            pi_snprintf(cmd + len, sizeof(cmd) - len, " &");
        }

        /* 使用sh来执行该命令 */
        dup2(pout[1], STDOUT_FILENO);
        execl("/bin/sh", "sh", "-c", cmd, NULL);

        /*退出子进程 */
        exit(EXIT_FAILURE);
    }
    else
    {
        start = pi_time(NULL);
        len = 0;

        pi_close(pout[1]);
        if (background)
        {
            fcntl(pout[0], F_SETFL, O_NONBLOCK);
        }

        if (buf != NULL)
        {
            memset(buf, 0, nbuf);
        }

        while (1)
        {
            if (buf)
            {
                n = pi_read(pout[0], buf + len, nbuf - len - 1);
                if ((n <= 0) && (errno != EAGAIN) && (errno != EWOULDBLOCK))
                {
                    break;
                }

                len += (size_t)n;
                if (len + 1 >= nbuf)
                {
                    break;
                }

                pi_msleep(50);
            }
            else
            {
                waitpid(cmdpid, NULL, WUNTRACED | WNOHANG);
                if (kill(cmdpid, 0) < 0)
                {
                    break; /* 进程已结束 */
                }
                pi_msleep(100);
            }

            if (pi_time(NULL) - start >= RUNCMD_TIMEOUT)
            {
                printf("%s: '%s' timeout\n", __func__, cmd);
                if (buf)
                {
                    pi_snprintf(buf, nbuf, "CMDTIMEOUT");
                }
                break;
            }
            else if (background)
            {
                break;
            }
        }
        close(pout[0]);

        if (background == 0)
        {
            kill(cmdpid, SIGKILL); /* 不管是否超时，此时该命令都应该执行结束，通过kill来保证 */
        }
        waitpid(cmdpid, NULL, WUNTRACED); /* 对于后台命令，wait的是sh程序，避免出现僵尸进程 */
    }

    res = (buf != NULL ? buf : CMD_OK);

    return res;
}
/**
 *@}
 */
