/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_public.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-07-27
 * @brief panel public interface
 */

#ifndef _PANEL_PUBLIC_H
#define _PANEL_PUBLIC_H

#include <pol/pol_types.h>
#include "qio/qio_general.h"
#include "public_data_proc.h"

/**
 * @brief print default config from panel
 * @author: madechang
 */
typedef struct
{
    unsigned int    default_tray;                       ///< default print tray
    // unsigned int    multifunction_tray_size;            ///< default multifunction tray paper size
    // unsigned int    multifunction_tray_type;            ///< default multifunction tray paper type
    // unsigned int    lct_in_tray_size;                   ///< lct in tray paper size
    // unsigned int    lct_in_tray_type;                   ///< lct  in tray paper type

    // unsigned int    tray1_type;                         ///< tray 1 paper type
    // unsigned int    tray2_type;                         ///< tray 2 paper type
    // unsigned int    tray3_type;                         ///< tray 3 paper type
    // unsigned int    tray4_type;                         ///< tray 4 paper type

}PANEL_PRINT_DEFAULT_CONFIG_S;

/**
 * @brief image setting from panel
 * @author: madechang
 */
typedef struct
{
    unsigned int    color_balance_c;                            ///< color balance C, 1-10
    unsigned int    color_balance_m;                            ///< color balance M, 1-10
    unsigned int    color_balance_y;                            ///< color balance Y, 1-10
    unsigned int    color_balance_k;                            ///< color balance K, 1-10
    unsigned int    toner_save;                                 ///< toner save mode, 0-off,1-on
    unsigned int    toner_density;                              ///< toner density, 1-10
    unsigned int    image_brightness;                           ///< image brightness, 1-10
    unsigned int    image_saturation;                           ///< image saturation, 1-10
    unsigned int    image_contrast;                             ///< image contrast, 1-10
    unsigned int    original_type;                              ///< image quality , 1-10

}PANEL_PRINT_IMAGE_CONFIG_S;

 /**
 * @brief pincode_job_panel_control struct
 */
typedef struct tag_panel_print_pincode_job_ctl
{
    char                        file_path[ 1024 ];              ///< 密码打印路径
}PANEL_PRINT_PINCODE_JOB_CTRL_S;

/**
 * @brief internal setting from panel
 */
typedef struct
{
    uint32_t    language_type;                                  ///< DEMO 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
                                                                ///< INFO 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
                                                                ///< MENU_STRUCT 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
                                                                ///< QUALITY: 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
                                                                ///< NET_CONFIG: 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
                                                                ///< EVENT_CONFIG: 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
                                                                ///< WIFI_WIZARD: 1:SYS_LANGUAGE_CHINESE  2:SYS_LANGUAGE_ENGLISH
}PANEL_PRINT_INTERNAL_PAGE_CONFIG_S;


typedef enum
{
    PANEL_PRINT_JOB_TYPE_UDISK = 0,                             ///< U盘打印
    PANEL_PRINT_JOB_TYPE_PINCODE,                               ///< 密码打印
    PANEL_PRINT_JOB_TYPE_SAMPLE_PRINT,                          ///< 样本打印
    PANEL_PRINT_JOB_TYPE_DELAY_PRINT,                           ///< 延迟打印
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_DEMO,                    ///< demo页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_INFO,                    ///< 信息页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_MENU_STRUCT,             ///< 菜单结构页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_PRINT_QUALITY,           ///< 质量测试页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_NET_CONFIG,              ///< 网络配置页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_EVENT_LOG,               ///< 事件日志页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_WIFI_WIZARD,             ///< wifi向导页
    PANEL_PRINT_JOB_TYPE_INTERNAL_PAGE_WEB_PDF,                 ///< web下载PDF
    PANEL_PRINT_JOB_TYPE_INVALID,
    PANEL_PRINT_JOB_TYPE_MAX,
}PANEL_PRINT_JOB_TYPE_E;



/**
 * @brief The struct of udisk print job request data
 */
typedef struct
{
    char                        file_path[ 1024 ];              ///< 路径
    uint32_t                    copies;                         ///< 份数
    uint32_t                    color;                          ///< 颜色
    uint32_t                    tray_setup;                     ///< 纸盒设置
    uint32_t                    duplex_mode;                    ///< 双面模式
    char                        optional_page_input[65];        ///< 可选页面打印
    uint32_t                    document_size;                  ///< 文档尺寸
    uint32_t                    document_size_width;            ///< 文档尺寸自定义宽
    uint32_t                    document_size_height;           ///< 文档尺寸自定义高
    uint32_t                    document_type;                  ///< 文档类型
    uint32_t                    document_paper_type;            ///< 文档介质
    uint32_t                    collate_mode;                   ///< 逐份
    uint32_t                    nup_mode;                       ///< 多页合一
    uint32_t                    nup_order;                      ///< 多页合一排列方式
    uint16_t                    c;                              ///< c
    uint16_t                    m;                              ///< m
    uint16_t                    y;                              ///< y
    uint16_t                    k;                              ///< k
    uint16_t                    luminosity;                     ///< 明暗度
    uint16_t                    contrast;                       ///< 对比度
    uint16_t                    saturation;                     ///< 饱和度
    uint16_t                    toner_deep;                     ///< 打印浓度
    uint32_t                    portrait_orientation;           ///< 画像方向
    uint32_t                    compart_page;                   ///< 分隔页
    uint32_t                    compart_page_tray;              ///< 分隔页纸盒
    uint32_t                    binding_mode;                   ///< 装订
    uint32_t                    fold;                           ///< 折叠
    uint32_t                    fold_pages;                     ///< 折叠页数
    uint32_t                    punch;                          ///< 打孔
    uint32_t                    paper_tray;                     ///< 接纸架
    uint32_t                    offset;                         ///< 偏移
    uint32_t                    print_scale_mode;               ///< 缩放模式
    uint32_t                    print_ipp_fidelity;             ///< 保真度
}PANEL_REQUEST_UDISK_PRINT_JOB_DATA_S;


typedef struct
{
    IO_VIA_E io_via;                                            ///< 作业来源
    uint32_t type;                                              ///< 作业类型 - 作业来源同一个，类型有多种有效
    union
    {
        PANEL_REQUEST_UDISK_PRINT_JOB_DATA_S    udisk_data;     ///< U盘打印参数
        PANEL_PRINT_PINCODE_JOB_CTRL_S          pincode_ctrl;   ///< 密码打印参数
        PRINT_SAMPLE_S                          print_sample;   ///< 样本打印参数
        PANEL_PRINT_INTERNAL_PAGE_CONFIG_S      internal_page;  ///< 内部页
        DELAY_JOB_PANEL_CTRL_S                  delay_ctrl;     ///< 延时打印
    }print_job_data;
}PANEL_PRINT_JOB_CONFIG_PARAM_S;

/**
 * @brief The struct of shortcut copy param
 *        后续参数的新增不要在中间插入，必须在最后加，否则升级后读取会出错
 *        不用的参数也不要删除，否则顺序乱了，读取的数据就不对了
 */
typedef struct
{
    char                            is_valid;                       ///< 结构体参数是否有效
    char                            name[ 256 ];    ///< name，大小不要改变，否则会导致后续数据错位
    uint32_t                        copies;                         ///< copies: 1~9999
    uint32_t                        color;                          ///< color, EUI_COLOR
    uint32_t                        original_size;                  ///< original paper size, EUI_PAPER_SIZE
    uint32_t                        original_dir;                   ///< original paper direction, EUI_COPY_PAPER_ORIG_DIR
    uint32_t                        copy_to_tray;                   ///< copy to tray, EUI_TRAY_INPUT_TYPE
    uint32_t                        zoom_sw;                        ///< zoom switch state EUI_COPY_SCALE_TYPE
    uint32_t                        zoom_manaul_val;                ///< zoom manaul val 25-400
    uint32_t                        duplex;                         ///< duplex mode, EUI_COPY_DUPLEX_TYPE
    uint32_t                        original_flip;                  ///< original flip, EUI_COPY_FLIP_OVER_TYPE
    uint32_t                        copies_flip;                    ///< copies flip mode, EUI_COPY_FLIP_OVER_TYPE
    uint32_t                        collate;                        ///< the collated mode, EUI_COPY_COLLATION
    uint32_t                        nin1_sw;                        ///< nin1 switch state, EUI_SWITCH
    uint32_t                        nin1_mode;                      ///< nin1 mode, EUI_COPY_NUP_TYPE
    uint32_t                        nin1_pos;                       ///< nin1 pos, EUI_COPY_NUP_POS
    uint32_t                        horizontal_margin;              ///< the horizontal margin
    uint32_t                        vertical_margin;                ///< the vertical margin
    uint32_t                        filter_edge_margin_top;         ///< the top margin for the filter edge mode
    uint32_t                        filter_edge_margin_left;        ///< the left margin for the filter edge mode
    uint32_t                        filter_edge_margin_right;       ///< the right margin for the filter edge mode
    uint32_t                        filter_edge_margin_bottom;      ///< the bottom margin for the filter edge mode
    uint32_t                        edge_to_edge_mode;              ///< edge to edge copy, close: 0, open: 1
    uint32_t                        watermark_sw;                   ///< watermark switch state, EUI_SWITCH
    uint32_t                        watermark_type;                 ///< the watermark mode, EUI_WATERMARK_TYPE
    uint32_t                        watermark_time_sw;              ///< the watermark time switch, EUI_SWITCH
    uint32_t                        quality;                        ///< quality, EUI_QUALITY_TYPE
    uint32_t                        color_balance_c;                ///< the color balance for c, EUI_IMAGE_LEVEL_VAL
    uint32_t                        color_balance_m;                ///< the color balance for m, EUI_IMAGE_LEVEL_VAL
    uint32_t                        color_balance_y;                ///< the color balance for y, EUI_IMAGE_LEVEL_VAL
    uint32_t                        color_balance_k;                ///< the color balance for k, EUI_IMAGE_LEVEL_VAL
    uint32_t                        brightness;                     ///< the level of the brightness, EUI_IMAGE_LEVEL_VAL
    uint32_t                        saturation;                     ///< the level of the saturation, EUI_IMAGE_LEVEL_VAL
    uint32_t                        contrast;                       ///< the level of the contrast, EUI_IMAGE_LEVEL_VAL
    uint32_t                        sharpness;                      ///< the level of the sharpness, EUI_IMAGE_LEVEL_VAL
    uint32_t                        hue;                            ///< the level of the hue, EUI_IMAGE_LEVEL_VAL
    uint32_t                        backgroundmove_level;           ///< backgroudmove level, EUI_IMAGE_LEVEL_VAL
    uint32_t                        save_toner_mode;                ///< the mode for the save toner, close: 0, open: 1
    uint32_t                        booklet_sw;                     ///< booklet switch state, EUI_SWITCH
    uint32_t                        booklet_duplex;                 ///< booklet duplex, EUI_COPY_DUPLEX_TYPE
    uint32_t                        booklet_fold_mode;              ///< booklet fold mode, EUI_FOLD_MODE
    uint32_t                        booklet_cover_sw;               ///< booklet cover switch, EUI_SWITCH
    uint32_t                        booklet_cover;                  ///< booklet cover mode, EUI_BOOKLET_COVER_TYPE
    uint32_t                        booklet_back_cover_sw;          ///< booklet back cover switch, EUI_SWITCH
    uint32_t                        booklet_back_cover;             ///< booklet back cover mode, EUI_BOOKLET_COVER_TYPE
    uint32_t                        booklet_cover_src;              ///< booklet cover source, EUI_TRAY_INPUT_TYPE
    uint32_t                        separator_sw;                   ///< the separator switch, EUI_SWITCH
    uint32_t                        separator_type;                 ///< the separator, EUI_SEPARATOR_PAGE_TYPE
    uint32_t                        separator_src;                  ///< the separator source, EUI_TRAY_INPUT_TYPE
    uint32_t                        landscape_sw;                   ///< landscape switch state, EUI_SWITCH
    uint32_t                        clone_sw;                       ///< clone switch state, EUI_SWITCH
    uint32_t                        clone_mode;                     ///< the clone mode, EUI_COPY_CLONE
    uint32_t                        color_filter_sw;                ///< color filter switch state, EUI_SWITCH
    uint32_t                        color_filter;                   ///< color filter, EUI_COLOR_FILTER
    uint32_t                        color_reverse_sw;               ///< color reverse switch, EUI_SWITCH
    uint32_t                        mirror_sw;                      ///< mirror switch, EUI_SWITCH
    uint32_t                        staple_sw;                      ///< staple switch, EUI_SWITCH
    uint32_t                        staple_num;                     ///< staple num, EUI_STAPLE_NUM
    uint32_t                        staple_angle;                   ///< the staple angle, EUI_STAPLE_ANGLE_MODE
    uint32_t                        punch_sw;                       ///< punch switch, EUI_SWITCH
    uint32_t                        punch_num;                      ///< the punch num, EUI_COPY_PUNCH_NUM
    uint32_t                        fold_sw;                        ///< fold switch, EUI_SWITCH
    uint32_t                        fold_mode;                      ///< the fold mode, EUI_FOLD_MODE
    uint32_t                        fold_set;                       ///< the fold set, EUI_FOLD_SET
    uint32_t                        fold_pages_num;                 ///< the fold pages num
    uint32_t                        offset_sw;                      ///< fold switch, EUI_SWITCH
    uint32_t                        custom2_orig_sw;                ///< custom2 original switch, EUI_SWITCH
    uint32_t                        insert_page_on_off;             ///< insert_page switch
    uint32_t                        insert_blank_sw;                ///< insert black sw
    char                            insert_page[256];               ///< the insert_page string like that : "3#25#57#88#134"
    uint32_t                        insert_page_paper_size;         ///< the paper size for insert_page, EUI_PAPER_SIZE
    uint32_t                        insert_page_paper_type;         ///< the paper type for insert_page, EUI_PAPER_TYPE
    uint32_t                        insert_page_tray_in;            ///< the tray in for insert_page, EUI_TRAY_INPUT_TYPE
    uint32_t                        insert_number_sw;               ///< insert image number sw
    char                            insert_number[128];              ///< arrary of insert image number
    uint32_t                        chapter_on_off;                 ///< chapter switch
    char                            chapter_page[256];              ///< the chapter_page string like that : "3#25#57#88#134"
    uint32_t                        chapter_paper_size;             ///< the paper size for chapter page, EUI_PAPER_SIZE
    uint32_t                        chapter_paper_type;             ///< the paper type for chapter page, EUI_PAPER_TYPE
    uint32_t                        chapter_tray_in;                ///< the tray in for chapter page, EUI_TRAY_INPUT_TYPE
    uint32_t                        ohp_insert_on_off;              ///< ohp_insert switch
    uint32_t                        ohp_insert_paper_size;          ///< the paper size for ohp_insert page, EUI_PAPER_SIZE
    uint32_t                        ohp_insert_paper_type;          ///< the paper type for ohp_insert page, EUI_PAPER_TYPE
    uint32_t                        ohp_insert_tray_in;             ///< the tray in for ohp_insert page, EUI_TRAY_INPUT_TYPE
    uint32_t                        overlay_mode;                   ///< overlay mode, EOVERLAY_IMG_MODE
    char                            overlay_register_id[128];       ///< register id of overlay image
    char                            overlay_front_use_id[128];      ///< registered image id of front page use
    char                            overlay_back_use_id[128];       ///< registered image id of back page use
    uint32_t                        overlay_style;                  ///< overlay style, EOVERLAY_IMG_STYLE
    uint32_t                        overlay_color;                  ///< overlay color, EOVERLAY_IMG_COLOR
    uint32_t                        overlay_count;                  ///< overlay count, EOVERLAY_IMG_COUNT
    int                             overlay_density;                ///< overlay density
    int                             overlay_offset_x;               ///< overlay offset x
    int                             overlay_offset_y;               ///< overlay offset y
    uint32_t                        separation_scan;                ///< separation scan
    uint32_t                        sample_copy;                    ///< sample copy
    uint32_t                        save_src_img_status;            ///< save src img status
    uint32_t                        save_src_img_type;              ///< save src img type
    uint32_t                        save_src_file_format;           ///< save src file format
    uint32_t                        overlay_sw;                     ///< overlay switch
    char                            watermark_string[128];          ///< the watermark string
    uint32_t                        copy_book_status;               ///< copy book status
    uint32_t                        copy_book_type;                 ///< book copy type, EBOOK_COPY_TYPE
    uint32_t                        copy_book_non_image_clean;      ///< only image
    uint32_t                        copy_book_center_clean;         ///< center
    uint32_t                        copy_book_image_in_center;      ///< keep in center

    uint32_t                        tray_auto_type;                 ///< tray auto type
    uint32_t                        poster_status;                  ///< poster status
    uint32_t                        poster_mode;                    ///< poster mode
    uint32_t                        poster;                         ///< poster
    uint32_t                        poster_size;                    ///< poster size
    uint32_t                        header_status;                  ///< header status
    uint32_t                        header;                         ///< header
    uint32_t                        footer_status;                  ///< footer status
    uint32_t                        footer;                         ///< footer
    uint32_t                        header_type;                    ///< header print to
    uint32_t                        header_print_to;                ///< header print to
    uint32_t                        footer_type;                    ///< footer type
    uint32_t                        footer_print_to;                ///< footer print to
    uint32_t                        cover_page;                     ///< cover page
    uint32_t                        cover_page_cover_status;        ///< cover page cover status
    uint32_t                        cover_page_back_cover_status;   ///< cover page back cover status
    uint32_t                        cover_page_cover_type;          ///< cover page cover type
    uint32_t                        cover_page_back_cover_type;     ///< cover page back cover type
    uint32_t                        cover_page_cover_src;           ///< cover page cover src
    uint32_t                        cover_page_back_cover_src;      ///< cover page back cover src
    uint32_t                        booklet_orig_flip;              ///< booklet ori flip
    char                            header_type_custom[128];        ///< header type custom
    char                            footer_type_custom[128];        ///< footer type custom
    uint32_t                        book_cpy_edge_common;           ///< book copy edge common
    uint32_t                        book_cpy_edge_clear;            ///< book copy edge clear
    uint32_t                        book_cpy_clear_top_edge;        ///< book copy clear top edge
    uint32_t                        book_cpy_clear_bottom_edge;     ///< book copy clear bottom edge
    uint32_t                        book_cpy_clear_left_edge;       ///< book copy clear left edge
    uint32_t                        book_cpy_clear_right_edge;      ///< book copy clear right edge
    uint32_t                        book_cpy_page_turn_mode;        ///< book copy page turn mode

    uint8_t                         reserve[512];                   ///< reserve buff
} SHORTCUT_COPY_PARAM_S;

typedef struct
{
    SHORTCUT_COPY_PARAM_S shorcut_copy_params[10];
}PANLE_SHORTCUT_COPY_PARAM_S;

/**
 * @brief This is the copy/scan/udisk_print job image advance settings 
 */

typedef struct
{
    uint32_t copy_image_quality;         ///< 图像质量
    uint32_t copy_color_balance_c;       ///< 色彩平衡
    uint32_t copy_color_balance_m;
    uint32_t copy_color_balance_y;
    uint32_t copy_color_balance_k;       
    uint32_t copy_brightness;            ///< 明亮度
    uint32_t copy_saturation;            ///< 饱和度
    uint32_t copy_contrast;              ///< 对比度
    uint32_t copy_hue;                   ///< 色相
    uint32_t copy_background_removal;    ///< 原稿背景消除
    uint32_t copy_sharpness;             ///< 锐度
    uint32_t copy_save_toner;            ///< 省墨

    uint32_t id_copy_brightness;         ///< 明亮度
    uint32_t bill_copy_brightness;       ///< 明亮度

    uint32_t scan_image_quality;         ///< 图像质量
    uint32_t scan_brightness;            ///< 明亮度
    uint32_t scan_saturation;            ///< 饱和度
    uint32_t scan_contrast;              ///< 对比度
    uint32_t scan_hue;                   ///< 色相
    uint32_t scan_background_removal;    ///< 原稿背景消除
    uint32_t scan_sharpness;             ///< 锐度

    uint32_t udisk_print_image_quality;         ///< 图像质量
    uint32_t udisk_print_image_direction;        ///< 画像方向
    uint32_t udisk_print_color_balance_c;       ///< 色彩平衡
    uint32_t udisk_print_color_balance_m;
    uint32_t udisk_print_color_balance_y;
    uint32_t udisk_print_color_balance_k;       
    uint32_t udisk_print_brightness;            ///< 明亮度
    uint32_t udisk_print_saturation;            ///< 饱和度
    uint32_t udisk_print_contrast;              ///< 对比度
    uint32_t udisk_print_print_density;         ///< 打印浓度

}PANEL_JOB_ADVANCE_IMAGE_S;

/**
 * @brief The enum of error job proc mode
 */
typedef enum 
{
    UI_ERR_JOB_PROC_MODE_CONTINUE = 0,                 ///< continue
    UI_ERR_JOB_PROC_MODE_DELETE,                       ///< delete
    UI_ERR_JOB_PROC_MODE_DELETE_DELAY,                 ///< delete delay
}UI_ERR_JOB_PROC_MODE_E;

#endif /* _PANEL_PUBLIC_H */

/**
 *@}
 */


