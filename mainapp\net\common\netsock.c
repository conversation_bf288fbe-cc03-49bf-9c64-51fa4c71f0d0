/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netsock.c
 * @addtogroup net
 * @{
 * @addtogroup netsock
 * <AUTHOR>
 * @date 2023-4-20
 * @brief network socket API
 */
#include "netsock.h"

#define NETSOCK_CLOSE_IF(_cond, _log)   if (_cond) { _log("[%s] "#_cond" errno: %d<%s>", caller, errno, strerror(errno)); pi_closesock(sockfd); sockfd = INVALID_SOCKET; break; }
#define NETSOCK_BREAK_IF(_cond, _log)   if (_cond) { _log("[%s] "#_cond" errno: %d<%s>", caller, errno, strerror(errno)); break; }
#define NETSOCK_DEBUG_IF(_cond, _log)   if (_cond) { _log("[%s] "#_cond" errno: %d<%s>", caller, errno, strerror(errno)); }

#define ACCEPT(s, a, b, c)              do { s = pi_accept(a, b, &c); } while ( s == INVALID_SOCKET && errno == EINTR )

#define TCP_SNDBUF_SIZE                 (0x28000)   ///< 160 * 1024 Bytes
#define TCP_RCVBUF_SIZE                 (0x28000)   ///< 160 * 1024 Bytes

PI_SOCKET_T net_socket_create_custom(int32_t domain, int32_t type, int32_t protocol, const char* caller)
{
    PI_SOCKET_T         sockfd;
    int32_t             v = 1;

    do
    {
        NETSOCK_BREAK_IF((sockfd = pi_socket(domain, type, protocol)) == INVALID_SOCKET,  NET_WARN);
        NETSOCK_CLOSE_IF(setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &v, sizeof(v)) < 0, NET_WARN);
    }
    while ( 0 );

    return sockfd;
}

PI_SOCKET_T net_socket_create_multicast(uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* caller)
{
    PI_SOCKET_T         sockfd;
    struct sockaddr_in6 sa_v6;
    struct sockaddr_in  sa_v4;
    struct ifreq        ifr;
    int32_t             val;

    do
    {
        sockfd = net_socket_create_custom((ipver == IPV6) ? AF_INET6 : AF_INET, SOCK_DGRAM, IPPROTO_UDP, caller);
        NETSOCK_BREAK_IF(sockfd == INVALID_SOCKET, NET_WARN);

        NETSOCK_CLOSE_IF(fcntl(sockfd, F_SETFD, FD_CLOEXEC) < 0, NET_WARN);

        val = 1;
        NETSOCK_CLOSE_IF(ioctl(sockfd, FIONBIO, &val) < 0, NET_WARN);

        memset(&ifr, 0, sizeof(ifr));
        snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", IFACE_NAME(ifid));
        NETSOCK_DEBUG_IF(setsockopt(sockfd, SOL_SOCKET, SO_BINDTODEVICE, &ifr, sizeof(ifr)) < 0, NET_WARN);

        if ( ipver == IPV6 )
        {
            val = 1;
            NETSOCK_DEBUG_IF(setsockopt(sockfd, IPPROTO_IPV6, IPV6_V6ONLY, &val, sizeof(val)) < 0, NET_WARN); /* IPv6端口只监听IPv6报文 */

            val = 255;
            NETSOCK_DEBUG_IF(setsockopt(sockfd, IPPROTO_IPV6, IPV6_MULTICAST_HOPS, &val, sizeof(val)) < 0, NET_WARN);

            memset(&sa_v6, 0, sizeof(sa_v6));
            sa_v6.sin6_addr = in6addr_any;
            sa_v6.sin6_port = htons(port);
            sa_v6.sin6_family = PF_INET6;
            NETSOCK_CLOSE_IF(bind(sockfd, (struct sockaddr *)&sa_v6, (socklen_t)sizeof(sa_v6)) < 0, NET_WARN);
        }
        else
        {
            val = 255;
            NETSOCK_DEBUG_IF(setsockopt(sockfd, IPPROTO_IP, IP_MULTICAST_TTL, &val, sizeof(val)) < 0, NET_WARN);

            memset(&sa_v4, 0, sizeof(sa_v4));
            sa_v4.sin_addr.s_addr = INADDR_ANY;
            sa_v4.sin_port = htons(port);
            sa_v4.sin_family = PF_INET;
            NETSOCK_CLOSE_IF(bind(sockfd, (struct sockaddr *)&sa_v4, (socklen_t)sizeof(sa_v4)) < 0, NET_WARN);
        }
    }
    while ( 0 );

    return sockfd;
}

PI_SOCKET_T net_socket_create_by_url(const char* url, uint16_t port, int32_t tos, int32_t tous, const char* caller)
{
    PI_SOCKET_T         sockfd = INVALID_SOCKET;
    IFACE_ID_E          ifid;
    struct addrinfo     hints;
    struct addrinfo*    res;
    struct addrinfo*    rp;
    struct ifreq        ifr;
    const char*         ifname;
    void*               addr;
    char                resolv_ip[IPV6_ADDR_LEN];
    char                port_str[8];
    size_t              val;
    int32_t             ret;
    socklen_t           len;

    RETURN_VAL_SHOW_CALLER_IF(url == NULL || port == 0, NET_WARN, caller, INVALID_SOCKET);

    memset(&hints, 0, sizeof(struct addrinfo));
    hints.ai_family   = AF_UNSPEC;
    hints.ai_socktype = SOCK_STREAM;
    hints.ai_protocol = IPPROTO_TCP;
    hints.ai_flags    = 0;

    snprintf(port_str, sizeof(port_str), "%d", port);
    RETURN_VAL_SHOW_CALLER_IF(getaddrinfo(url, port_str, &hints, &res) != 0, NET_WARN, caller, INVALID_SOCKET);

    for ( rp = res; (rp != NULL && sockfd == INVALID_SOCKET); rp = rp->ai_next )
    {
        memset(resolv_ip, 0, sizeof(resolv_ip));
        addr = (rp->ai_family == AF_INET6) ? (void *)&((struct sockaddr_in6 *)rp->ai_addr)->sin6_addr : (void *)&((struct sockaddr_in *)rp->ai_addr)->sin_addr;
        inet_ntop(rp->ai_family, addr, resolv_ip, sizeof(resolv_ip));
        NET_DEBUG("resolve url(%s) to addr(%s)", url, resolv_ip);

        for ( ifid = IFACE_ID_ETH; ifid <= IFACE_ID_NUM; ++ifid )
        {
            ifname = IFACE_NAME(ifid);
            if ( STRING_IS_EMPTY(ifname) )
            {
                continue;
            }

            sockfd = net_socket_create_custom(rp->ai_family, rp->ai_socktype, rp->ai_protocol, caller);
            NETSOCK_BREAK_IF(sockfd == INVALID_SOCKET, NET_WARN);

            do
            {
                memset(&ifr, 0, sizeof(ifr));
                snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
                NETSOCK_CLOSE_IF(ioctl(sockfd, SIOCGIFFLAGS, &ifr) < 0 || (ifr.ifr_flags & IFF_RUNNING) == 0, NET_DEBUG);
                NETSOCK_CLOSE_IF(setsockopt(sockfd, SOL_SOCKET, SO_BINDTODEVICE, &ifr, sizeof(ifr)) < 0, NET_WARN);
                NETSOCK_CLOSE_IF(fcntl(sockfd, F_SETFD, FD_CLOEXEC) < 0, NET_WARN);

                val = 1;
                NETSOCK_CLOSE_IF(ioctl(sockfd, FIONBIO, &val) < 0, NET_WARN);

                val = 1;
                NETSOCK_CLOSE_IF(setsockopt(sockfd, IPPROTO_TCP, TCP_NODELAY, &val, sizeof(val)) < 0, NET_WARN);

                ret = connect(sockfd, rp->ai_addr, rp->ai_addrlen);
                if ( ret == 0 )
                {
                    break;
                }
                else if ( errno == EWOULDBLOCK || errno == EINPROGRESS || errno == EALREADY )
                {
                    if ( netsock_select(sockfd, 0, 1, 0, tos, tous) > 0 )
                    {
                        len = (socklen_t)sizeof(val);
                        ret = getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &val, &len);
                        NET_DEBUG("getsockopt ret(%d) val(%u)", ret, val);
                        if ( ret == 0 && val == 0 )
                        {
                            break;
                        }
                    }
                }

                pi_closesock(sockfd);
                sockfd = INVALID_SOCKET;
            }
            while ( 0 );

            if ( sockfd != INVALID_SOCKET )
            {
                NET_DEBUG("connect to %s : %d by %s sueecssfully", resolv_ip, port, ifname);
                break;
            }
        }
    }
    freeaddrinfo(res);

    return sockfd;
}

PI_SOCKET_T net_socket_create_tcpserver(const char* ipaddr, uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver, int32_t max_conn, const char* caller)
{
    PI_SOCKET_T         sockfd = INVALID_SOCKET;
    struct sockaddr_in6 sa_v6;
    struct sockaddr_in  sa_v4;
    int32_t             val;
    socklen_t           len;

    do
    {
        sockfd = net_socket_create_custom(ipver == IPV6 ? AF_INET6 : AF_INET, SOCK_STREAM, 0, caller);
        NETSOCK_BREAK_IF(sockfd == INVALID_SOCKET, NET_WARN);

        NETSOCK_CLOSE_IF(fcntl(sockfd, F_SETFD, FD_CLOEXEC) < 0, NET_WARN);

        val = 1;
        NETSOCK_CLOSE_IF(ioctl(sockfd, FIONBIO, &val) < 0, NET_WARN);

        val = 1;
        NETSOCK_CLOSE_IF(setsockopt(sockfd, IPPROTO_TCP, TCP_NODELAY, &val, sizeof(val)) < 0, NET_WARN);

        if ( ipver == IPV6 )
        {
            val = 1;
            NETSOCK_DEBUG_IF(setsockopt(sockfd, IPPROTO_IPV6, IPV6_V6ONLY, &val, sizeof(val)) < 0, NET_WARN); /* IPv6端口只监听IPv6报文 */

            memset(&sa_v6, 0, sizeof(sa_v6));
            sa_v6.sin6_family = AF_INET6;
            sa_v6.sin6_port = htons(port);
            if ( ipaddr != NULL && strcmp(ipaddr, "::") != 0 )
            {
                sa_v6.sin6_scope_id = if_nametoindex(IFACE_NAME(ifid));
                inet_pton(AF_INET6, ipaddr, &sa_v6.sin6_addr);
            }
            else
            {
                sa_v6.sin6_addr = in6addr_any;
            }
            NETSOCK_CLOSE_IF(bind(sockfd, (struct sockaddr *)&sa_v6, (socklen_t)sizeof(sa_v6)) < 0, NET_WARN);
        }
        else
        {
            memset(&sa_v4, 0, sizeof(sa_v4));
            sa_v4.sin_family = AF_INET;
            sa_v4.sin_port = htons(port);
            if ( ipaddr == NULL || strcmp(ipaddr, "0.0.0.0") == 0 )
            {
                sa_v4.sin_addr.s_addr = INADDR_ANY;
            }
            else
            {
                sa_v4.sin_addr.s_addr = inet_addr(ipaddr);
            }
            NETSOCK_CLOSE_IF(bind(sockfd, (struct sockaddr *)&sa_v4, (socklen_t)sizeof(sa_v4)) < 0, NET_WARN);
        }
        NETSOCK_CLOSE_IF(listen(sockfd, max_conn) < 0, NET_WARN);

        len = sizeof(val);
        NETSOCK_BREAK_IF(getsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &val, &len) < 0, NET_INFO);
        if ( val < TCP_SNDBUF_SIZE )
        {
            val = TCP_SNDBUF_SIZE;
            setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &val, sizeof(val));
        }

        len = sizeof(val);
        NETSOCK_BREAK_IF(getsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &val, &len) < 0, NET_INFO);
        if ( val < TCP_RCVBUF_SIZE )
        {
            val = TCP_RCVBUF_SIZE;
            setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &val, sizeof(val));
        }
    }
    while ( 0 );

    return sockfd;
}

void net_socket_reload_tcpserver(PI_SOCKET_T sockfd[IPVER_NUM], fd_set* readfds, int32_t* maxfd, int32_t update, uint16_t port, int32_t max_conn, const char* caller)
{
    IP_VERSION_E    ipver;

    for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
    {
        if ( update && sockfd[ipver] != INVALID_SOCKET )  /* 强制更新，关闭当前socket */
        {
            NET_DEBUG("[%s] destroy service(%d) for port(%u) %s", caller, sockfd[ipver], port, IPVER_NAME(ipver));
            pi_closesock(sockfd[ipver]);
            sockfd[ipver] = INVALID_SOCKET;
        }

        if ( port == 0 )
        {
            continue;
        }

        if ( sockfd[ipver] == INVALID_SOCKET )
        {
            sockfd[ipver] = net_socket_create_tcpserver(NULL, port, IFACE_ID_ANY, ipver, max_conn, caller);
            if ( sockfd[ipver] == INVALID_SOCKET )
            {
                NET_WARN("[%s] create service failed for port(%u) %s", caller, port, IPVER_NAME(ipver));
                continue;
            }
            NET_DEBUG("[%s] create service(%d) for port(%u) %s", caller, sockfd[ipver], port, IPVER_NAME(ipver));
        }

        FD_SET(sockfd[ipver], readfds);
        if ( *maxfd < sockfd[ipver] )
        {
            *maxfd = sockfd[ipver];
        }
    }
}

int32_t net_socket_join_multicast_group(PI_SOCKET_T sockfd, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* local_addr, const char* mcast_addr, const char* caller)
{
    struct ipv6_mreq    param_v6;
    struct ip_mreq      param_v4;
    int32_t             ret;

    RETURN_VAL_SHOW_CALLER_IF(sockfd == INVALID_SOCKET || local_addr == NULL || mcast_addr == NULL, NET_WARN, caller, -1);

    if ( ipver == IPV6 )
    {
        memset(&param_v6, 0, sizeof(param_v6));
        inet_pton(AF_INET6, mcast_addr, &param_v6.ipv6mr_multiaddr);
        param_v6.ipv6mr_interface = if_nametoindex(IFACE_NAME(ifid));
        ret = pi_setsockopt(sockfd, IPPROTO_IPV6, IPV6_JOIN_GROUP, &param_v6, sizeof(param_v6));
    }
    else
    {
        memset(&param_v4, 0, sizeof(param_v4));
        param_v4.imr_multiaddr.s_addr = inet_addr(mcast_addr);
        param_v4.imr_interface.s_addr = inet_addr(local_addr);
        ret = pi_setsockopt(sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &param_v4, sizeof(param_v4));
    }

    if ( ret != 0 )
    {
        NET_WARN("[%s] %s address(%s) join multicast group(%s) failed: %d<%s>", caller, IFACE_NAME(ifid), local_addr, mcast_addr, errno, strerror(errno));
    }
    else
    {
        NET_DEBUG("[%s] %s address(%s) join multicast group(%s) sueecssfully", caller, IFACE_NAME(ifid), local_addr, mcast_addr);
    }

    return ret;
}

int32_t net_socket_leave_multicast_group(PI_SOCKET_T sockfd, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* mcast_addr, const char* caller)
{
    struct ipv6_mreq    param_v6;
    struct ip_mreq      param_v4;
    int32_t             ret;

    if ( ipver == IPV6 )
    {
        memset(&param_v6, 0, sizeof(param_v6));
        inet_pton(AF_INET6, mcast_addr, &param_v6.ipv6mr_multiaddr);
        param_v6.ipv6mr_interface = 0;
        ret = pi_setsockopt(sockfd, IPPROTO_IPV6, IPV6_LEAVE_GROUP, &param_v6, sizeof(param_v6));
    }
    else
    {
        memset(&param_v4, 0, sizeof(param_v4));
        param_v4.imr_multiaddr.s_addr = inet_addr(mcast_addr);
        param_v4.imr_interface.s_addr = INADDR_ANY;
        ret = pi_setsockopt(sockfd, IPPROTO_IP, IP_DROP_MEMBERSHIP, &param_v4, sizeof(param_v4));
    }

    if ( ret != 0 )
    {
        NET_INFO("[%s] %s leave multicast group(%s) failed: %d<%s>", caller, IFACE_NAME(ifid), mcast_addr, errno, strerror(errno));
    }
    else
    {
        NET_DEBUG("[%s] %s leave multicast group(%s) sueecssfully", caller, IFACE_NAME(ifid), mcast_addr);
    }

    return 0;
}

int32_t net_socket_select(PI_SOCKET_T sockfd, uint32_t read, uint32_t write, uint32_t error, int32_t tos, int32_t tous, const char* caller)
{
    struct timeval  tv = { .tv_sec = (__time_t)tos, .tv_usec = (__suseconds_t)tous };
    fd_set          fds;

    RETURN_VAL_SHOW_CALLER_IF(sockfd == INVALID_SOCKET, NET_WARN, caller, -1);

    FD_ZERO(&fds);
    FD_SET(sockfd, &fds);

    return select(sockfd + 1, (read  ? &fds : NULL), (write ? &fds : NULL), (error ? &fds : NULL), ((tos < 0 || (tos == 0 && tous < 0)) ? NULL : &tv));
}

int32_t net_socket_peek_readable_number(PI_SOCKET_T sockfd, const char* caller)
{
    char buf[4];

    RETURN_VAL_SHOW_CALLER_IF(sockfd == INVALID_SOCKET, NET_WARN, caller, 0);

    return recv(sockfd, buf, sizeof(buf), MSG_PEEK);
}

NET_CONN_S* net_socket_accept_connection(PI_SOCKET_T listen_sockfd, IP_VERSION_E ipver, const char* caller)
{
    PI_SOCKET_T         sockfd = INVALID_SOCKET;
    NET_CONN_S*         pnc;
    struct sockaddr_in6 sa_v6;
    struct sockaddr_in  sa_v4;
    struct linger       lng;
    int32_t             val;
    socklen_t           len;

    RETURN_VAL_SHOW_CALLER_IF(listen_sockfd == INVALID_SOCKET, NET_WARN, caller, NULL);

    pnc = (NET_CONN_S *)pi_zalloc(sizeof(NET_CONN_S));
    RETURN_VAL_SHOW_CALLER_IF(pnc == NULL, NET_WARN, caller, NULL);

    do
    {
        if ( ipver == IPV6 )
        {
            len = sizeof(struct sockaddr_in6);
            memset(&sa_v6, 0, len);
            sockfd = accept(listen_sockfd, (struct sockaddr *)&sa_v6, (socklen_t *)&len);
            NETSOCK_BREAK_IF(sockfd == INVALID_SOCKET, NET_WARN);

            inet_ntop(AF_INET6, (void *)&sa_v6.sin6_addr, pnc->remote_addr, IPV6_ADDR_LEN);
            pnc->remote_port = ntohs(sa_v6.sin6_port);

            len = (socklen_t)sizeof(struct sockaddr_in6);
            memset(&sa_v6, 0, len);
            getsockname(sockfd, (struct sockaddr *)&sa_v6, &len);

            inet_ntop(AF_INET6, (void *)&sa_v6.sin6_addr, pnc->local_addr, IPV6_ADDR_LEN);
            pnc->local_port = ntohs(sa_v6.sin6_port);
        }
        else
        {
            len = sizeof(struct sockaddr_in);
            memset(&sa_v4, 0, len);
            sockfd = accept(listen_sockfd, (struct sockaddr *)&sa_v4, (socklen_t *)&len);
            NETSOCK_BREAK_IF(sockfd == INVALID_SOCKET, NET_WARN);

            inet_ntop(AF_INET, (void *)&sa_v4.sin_addr, pnc->remote_addr, IPV4_ADDR_LEN);
            pnc->remote_port = ntohs(sa_v4.sin_port);

            len = (socklen_t)sizeof(struct sockaddr_in);
            memset(&sa_v4, 0, len);
            getsockname(sockfd, (struct sockaddr *)&sa_v4, &len);
            inet_ntop(AF_INET, (void *)&sa_v4.sin_addr, pnc->local_addr, IPV4_ADDR_LEN);
            pnc->local_port = ntohs(sa_v4.sin_port);
        }

        NETSOCK_CLOSE_IF(fcntl(sockfd, F_SETFD, FD_CLOEXEC) < 0, NET_WARN);

        val = 1;
        NETSOCK_CLOSE_IF(ioctl(sockfd, FIONBIO, &val) < 0, NET_WARN);

        val = 1;
        NETSOCK_CLOSE_IF(setsockopt(sockfd, IPPROTO_TCP, TCP_NODELAY, &val, sizeof(val)) < 0, NET_WARN);

        lng.l_onoff  = 1;
        lng.l_linger = 30;
        NETSOCK_CLOSE_IF(setsockopt(sockfd, SOL_SOCKET, SO_LINGER, &lng, sizeof(lng)) < 0, NET_WARN);

        len = sizeof(val);
        NETSOCK_BREAK_IF(getsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &val, &len) < 0, NET_INFO);
        if ( val < TCP_SNDBUF_SIZE )
        {
            NET_DEBUG("[%s] set SO_SNDBUF(%dK -> %dK) for connection(%u->%u)", caller, val/1024, TCP_SNDBUF_SIZE/1024, pnc->remote_port, pnc->local_port);
            val = TCP_SNDBUF_SIZE;
            setsockopt(sockfd, SOL_SOCKET, SO_SNDBUF, &val, sizeof(val));
        }

        len = sizeof(val);
        NETSOCK_BREAK_IF(getsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &val, &len) < 0, NET_INFO);
        if ( val < TCP_RCVBUF_SIZE )
        {
            NET_DEBUG("[%s] set SO_RCVBUF(%dK -> %dK) for connection(%u->%u)", caller, val/1024, TCP_RCVBUF_SIZE/1024, pnc->remote_port, pnc->local_port);
            val = TCP_RCVBUF_SIZE;
            setsockopt(sockfd, SOL_SOCKET, SO_RCVBUF, &val, sizeof(val));
        }
    }
    while ( 0 );

    if ( sockfd != INVALID_SOCKET )
    {
        NET_DEBUG("[%s] connection(%s : %u to %u) accept, sockfd(%d)", caller, pnc->remote_addr, pnc->remote_port, pnc->local_port, sockfd);
        pnc->sockfd = sockfd;
        pnc->ipver  = ipver;
    }
    else
    {
        pi_free(pnc);
        pnc = NULL;
    }
    return pnc;
}

int32_t net_socket_peek_connection(NET_CONN_S* pnc, int32_t timeout, const char* caller)
{
    int32_t ret = -1;

    do
    {
        NETSOCK_BREAK_IF(pnc == NULL || pnc->sockfd == INVALID_SOCKET, NET_WARN);

        ret = net_socket_select(pnc->sockfd, 1, 0, 0, timeout, 0, caller);
        if ( ret < 0 )
        {
            NET_INFO("[%s] connection(%s : %u to %u) unreadable(%d), sockfd(%d)", caller, pnc->remote_addr, pnc->remote_port, pnc->local_port, ret, pnc->sockfd);
            break;
        }
        else if ( ret == 0 )
        {
            NET_INFO("[%s] connection(%s : %u to %u) timeout, sockfd(%d)", caller, pnc->remote_addr, pnc->remote_port, pnc->local_port, pnc->sockfd);
            break;
        }

        ret = net_socket_peek_readable_number(pnc->sockfd, caller);
        if ( ret <= 0 )
        {
            NET_DEBUG("[%s] connection(%s : %u to %u) has been closed(%d), sockfd(%d)", caller, pnc->remote_addr, pnc->remote_port, pnc->local_port, ret, pnc->sockfd);
            break;
        }

        NET_DEBUG("[%s] connection(%s : %u to %u) start, sockfd(%d)", caller, pnc->remote_addr, pnc->remote_port, pnc->local_port, pnc->sockfd);
    }
    while ( 0 );

    return ret;
}

void net_socket_close_connection(NET_CONN_S* pnc, const char* caller)
{
    RETURN_SHOW_CALLER_IF(pnc == NULL, NET_WARN, caller);

    NET_DEBUG("[%s] connection(%s : %u to %u) end, sockfd(%d)", caller, pnc->remote_addr, pnc->remote_port, pnc->local_port, pnc->sockfd);
    if ( pnc->sockfd != INVALID_SOCKET )
    {
        pi_closesock(pnc->sockfd);
    }
    pi_free(pnc);
}
/**
 *@}
 */
