/*
 * @Author: your name
 * @Date: 2023-12-22 09:38:35
 * @LastEditTime: 2024-01-19 17:15:00
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \runtime\src\runtime\modules\std\std.c
 */
#include "runtime/modules/std/std.h"
#include <uv.h>
#include "runtime/modules/std/console.h"
#include "runtime/modules/std/timer.h"
#include "runtime/modules/std/object_storage.h"
#include "runtime/runtime.h"
#include "runtime/modules/module_manager.h"

static void std_init()
{
    LOG_I("std","std_init\n");
}

static void std_release()
{
    LOG_I("std","std_release\n");
}

static void std_instance(PeSFRunTime *prt)
{
    JSContext *js_ctx = prt->qjs_ctx;

    // 注册 console 到 global 对象中
    js_console_instance(js_ctx);

    JSValue global = JS_GetGlobalObject(js_ctx);
    // 注册 setTimeout 到 global 对象中
    JS_SetPropertyStr(js_ctx, global, "setTimeout", JS_NewCFunction(js_ctx, js_setTimeout, "setTimeout", 2));

    // 注册 clearTimeout 到 global 对象中
    JS_SetPropertyStr(js_ctx, global, "clearTimeout", JS_NewCFunction(js_ctx, js_clearTimeout, "clearTimeout", 1));

    // 注册 setInterval 到 global 对象中
    JS_SetPropertyStr(js_ctx, global, "setInterval", JS_NewCFunction(js_ctx, js_setInterval, "setInterval", 2));

    // 注册 clearTimeout 到 global 对象中
    JS_SetPropertyStr(js_ctx, global, "clearInterval", JS_NewCFunction(js_ctx, js_clearInterval, "clearInterval", 1));


	
    JSValue object = JS_GetPropertyStr(js_ctx, global, "Object");
    // 注册 saveObject 到 global 对象中
    JS_SetPropertyStr(js_ctx, object, "save", JS_NewCFunction(js_ctx, js_object_save, "save", 2));
  
    // 注册 loadObject 到 global 对象中
    JS_SetPropertyStr(js_ctx, object, "load", JS_NewCFunction(js_ctx, js_object_load, "load", 1));

    // 释放对象的引用
    JS_FreeValue(js_ctx, object);
    JS_FreeValue(js_ctx, global);
}

void std_generalization(PeSFRunTime *prt)
{
    timer_close(prt->qjs_ctx);
}

static ModuleContext std = {
    .module_name = "std",
    .module_init = std_init,
    .module_release = std_release,
    .module_instance = std_instance,
    .module_generalization = std_generalization
};

MODULE_REGISTER(std);