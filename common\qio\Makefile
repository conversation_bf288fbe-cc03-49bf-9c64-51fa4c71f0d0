obj-$(CONFIG_QIO) += ofd/ioapi.o
obj-$(CONFIG_QIO) += ofd/ofdlib.o
obj-$(CONFIG_QIO) += ofd/zip.o
obj-$(CONFIG_QIO) += qio_ofd.o

obj-$(CONFIG_QIO) += xps/xps_tools.o
obj-$(CONFIG_QIO) += qio_xps.o

obj-$(CONFIG_QIO) += qio_file.o
obj-$(CONFIG_QIO) += qio_general.o
obj-$(CONFIG_QIO) += qio_mem.o
obj-$(CONFIG_QIO) += qio_pdf.o
obj-$(CONFIG_QIO) += qio_storage_file.o
obj-$(CONFIG_QIO) += qio_tiff.o
obj-$(CONFIG_QIO) += qio_tty.o
obj-$(CONFIG_QIO) += qio_usbd.o

obj-$(CONFIG_QIO) += qio_shm.o
