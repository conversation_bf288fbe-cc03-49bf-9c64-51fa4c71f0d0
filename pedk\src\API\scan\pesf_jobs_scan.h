#ifndef _PESF_JOBS_SCAN_
#define _PESF_JOBS_SCAN_

#include <quickjs.h>

/*
    声明 QuickJS C 函数由于初始化回调
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv);


//////////////////////////////////////////////////////////
//
//
//             Scan
//
//////////////////////////////////////////////////////////

#define PESF_SCAN_SHARPNESS         0
#define PESF_SCAN_SATURATION        1
#define PESF_SCAN_CONTRAST          2
#define PESF_SCAN_BRIGHTNESS        3
#define PESF_SCAN_HUE               4
#define PESF_SCAN_BACKGROUNDREMOVE  5

#define PESF_SCAN_AREA_A3            "MEDIA_SIZE_A3"//1
#define PESF_SCAN_AREA_A4            "MEDIA_SIZE_A4"//2
#define PESF_SCAN_AREA_A4L           "MEDIA_SIZE_A4_LEF"//3
#define PESF_SCAN_AREA_A5            "MEDIA_SIZE_A5"//4
#define PESF_SCAN_AREA_A5L           "MEDIA_SIZE_A5_LEF"//5
#define PESF_SCAN_AREA_B4            "MEDIA_SIZE_JIS_B4"//6 //jis b4
#define PESF_SCAN_AREA_B5            "MEDIA_SIZE_JIS_B5"//7 //JIS b5
#define PESF_SCAN_AREA_B5L           "MEDIA_SIZE_JIS_B5_LEF"//8
#define PESF_SCAN_AREA_JIS_B6        "MEDIA_SIZE_JIS_B6"//9
#define PESF_SCAN_AREA_ISO_B5        "MEDIA_SIZE_ISO_B5"//13
#define PESF_SCAN_AREA_8K            "MEDIA_SIZE_8K"//17 // 185.0 *  260.0
#define PESF_SCAN_AREA_BIG_16K       "MEDIA_SIZE_BIG_16K"//18 // 195.0 * 270.0
#define PESF_SCAN_AREA_BIG_16KL      "MEDIA_SIZE_BIG_16K_LEF"//19 // 270.0 * 195.0
#define PESF_SCAN_AREA_16K           "MEDIA_SIZE_16K"//20 // 270.0 * 390.0
#define PESF_SCAN_AREA_LETTER        "MEDIA_SIZE_LETTER"//27
#define PESF_SCAN_AREA_LETTER_L      "MEDIA_SIZE_LETTER_LEF"//28
#define PESF_SCAN_AREA_LEGAL         "MEDIA_SIZE_LEGAL"//29
#define PESF_SCAN_AREA_FOLIO         "MEDIA_SIZE_FOLIO"//30
#define PESF_SCAN_AREA_EXECUTIVE     "MEDIA_SIZE_EXECUTIVE"//32
#define PESF_SCAN_AREA_INVOICE       "MEDIA_SIZE_INVOICE"//34 //statement
#define PESF_SCAN_AREA_INVOICE_L     "MEDIA_SIZE_INVOICE_LEF"//35
#define PESF_SCAN_AREA_A6            "MEDIA_SIZE_A6"//36
#define PESF_SCAN_AREA_B6            "MEDIA_SIZE_B6"//47
#define PESF_SCAN_AREA_USER_DEFINE   "MEDIA_SIZE_USERDEFINE"//60
#define PESF_SCAN_AREA_LEDGER        "MEDIA_SIZE_LEDGER"//61
#define PESF_SCAN_AREA_A3_WIDE       "MEDIA_SIZE_A3_WIDE"//67
#define PESF_SCAN_AREA_SRA3          "MEDIA_SIZE_SRA3"//72
#define PESF_SCAN_AREA_CARD          "MEDIA_SIZE_CARD"//73
//#define PESF_SCAN_AREA_FULL_PLATEN   "MEDIA_SIZE_A3"//1001

#define FTP_USER_NAME_LEN		129
#define FTP_SERVER_NAME_LEN		32
#define FTP_SERVER_PATH_LEN		64
#define FTP_LOGIN_NAME_LEN		64
#define FTP_PASSWORD_LEN		32
#define FTP_PARM_MAX			60

//SMB
#define SMB_USER_NAME_LEN		128+1  //Windy modify form 33 to 256 in 20200708
#define SMB_SERVER_NAME_LEN		32+1
#define SMB_SERVER_PATH_LEN		129
#define SMB_LOGIN_NAME_LEN		129
#define SMB_PASSWORD_LEN		32+1
#define SMB_PARM_MAX			60


#define MAIL_LINKMAN_NAME_LEN	16
#define MAIL_ADDR_LEN			64
#define MAIL_ADDR_NUM_MAX		60
#define MAIL_GROUP_NAME_LEN		16
#define MAIL_GROUP_MAX			10

#define PEDK_SCAN_APP_FILE      50

#define HTTP_URL_PATH_LEN	       512
#define HTTP_HEADERS_PATH_LEN	   1024




//scan status list by severity order
typedef enum
{

//SCAN STATUS BEGIN
    STATUS_I_SCAN_INIT =    0x10200000 ,
    STATUS_I_SCAN_IDLE =    0x10200001 ,
    STATUS_I_SCAN_SLEEP =   0x10200002 ,
    STATUS_I_SCAN_PROCESSING =  0x10200003 ,
    STATUS_I_SCAN_RUNNING = 0x10200004 ,
    STATUS_I_SCAN_CANCELING =   0x10200005 ,
    STATUS_I_SCAN_TOFILE_SENDING =  0x10200006 ,
    STATUS_I_SCAN_NEXT_PAGE_WAITING =   0x10200007 ,
    STATUS_I_SCAN_FINISHED =    0x10200008 ,
    STATUS_I_SCAN_TO_FILE_UDISK_SAVING =    0x10200009 ,
    STATUS_I_SCAN_TO_FILE_SENT =    0x1020000A ,
    STATUS_I_SCAN_LOCKED =  0x1020000B ,
    STATUS_I_SCAN_ADF_PAPER_PRESENT =   0x10201000 ,
    STATUS_I_SCAN_ADF_PAPER_REMOVED =   0x10201001 ,
    STATUS_I_SCAN_PUT_PAPER_TO_ADF =    0x10201002 ,
    STATUS_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF =   0x10201003 ,
    STATUS_I_SCAN_OUT_TO_EML_SUCCESS =  0x10203000 ,
    STATUS_I_SCAN_OUT_TO_FTP_SUCCESS =  0x10203001 ,
    STATUS_I_SCAN_OUT_TO_UDISK_SUCCESS =    0x10203002 ,
    STATUS_I_SCAN_OUT_TO_WSD_SUCCESS =  0x10203003 ,
    STATUS_I_SCAN_OUT_TO_AIRSCAN_SUCCESS =  0x10203004 ,
    STATUS_I_SCAN_OUT_TO_EML_CANCEL =   0x10203005 ,
    STATUS_I_SCAN_OUT_TO_FTP_CANCEL =   0x10203006 ,
    STATUS_I_SCAN_OUT_TO_UDISK_CANCEL = 0x10203007 ,
    STATUS_I_SCAN_OUT_TO_WSD_CANCEL =   0x10203008 ,
    STATUS_I_SCAN_OUT_TO_AIRSCAN_CANCEL =   0x10203009 ,
    STATUS_E_SCAN_ADF_PAPER_OUT =   0x30200000 ,
    STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT = 0x30200001 ,
    STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK =  0x30200002 ,
    STATUS_E_SCAN_ADF_COVER_OPEN =  0x30200003 ,
    STATUS_E_SCAN_ADF_PAPER_MISMATCH =  0x30200004 ,
    STATUS_E_SCAN_FB_COVER_OPEN =   0x30200005 ,
    STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN =   0x30200007 ,
    STATUS_E_SCAN_MEMORY_LOW =  0x30202000 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_21  =   0x30202001 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_22  =   0x30202002 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_23  =   0x30202003 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_24  =   0x30202004 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_25  =   0x30202005 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_26  =   0x30202006 ,
    STATUS_E_SCAN_COMMUNICATION_ERR_27  =   0x30202007 ,
    STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT   =  0x30202008 ,
    STATUS_E_SCAN_ADF_BOTTOM_COVER_OPEN = 0x30202009,
    STATUS_E_SCAN_TO_FILE_ABORTED = 0x30203000 ,
    STATUS_E_SCAN_TO_FILE_PASSWORD_WRONG =  0x30203001 ,
    STATUS_E_SCAN_TO_FILE_FILE_OVERSIZE =   0x30203002 ,
    STATUS_E_SCAN_TO_FILE_SERVER_OVERSIZE = 0x30203003 ,
    STATUS_E_SCAN_TO_FILE_SEND_FAILED = 0x30203004 ,
    STATUS_E_SCAN_TO_FILE_VOLUME_LOW =  0x30203005 ,
    STATUS_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE =    0x30203006 ,
    STATUS_E_SCAN_TO_FILE_UDISK_ABORTED =   0x30203007 ,
    STATUS_E_SCAN_TO_FILE_PC_NO_RESPONSE =  0x30203008

} STATUS_ID_E;


/*scan job type enum*/

typedef enum
{
    SCAN_JOB_TYPE_NONE        = 0,           ///< scan job type invaild param
    SCAN_JOB_TYPE_HOST_PULL   = 1,           ///< scan pull by pc
    SCAN_JOB_TYPE_HOST_PUSH   = 2,           ///< scan push by panel
    SCAN_JOB_TYPE_FTP         = 3,           ///< scan to ftp
    SCAN_JOB_TYPE_SMB         = 4,           ///< scan to ftp
    SCAN_JOB_TYPE_EMAIL       = 5,           ///< scan to email
    SCAN_JOB_TYPE_U_DISK      = 6,           ///< scan to udisk
    SCAN_JOB_TYPE_COPY        = 7,           ///< scan to copy
    SCAN_JOB_TYPE_FAX         = 8,           ///< scan to fax
    SCAN_JOB_TYPE_AIRSCAN     = 9,           ///< air scan
    SCAN_JOB_TYPE_WSDSCAN     = 10,          ///< wsd scan
    SCAN_JOB_TYPE_HTTP        = 11,          ///< http scan
    SCAN_JOB_TYPE_APP         = 12,          ///< app scan
    SCAN_JOB_TYPE_MAX         = 0xFFFFFFFF

}SCAN_JOB_TYPE;

typedef enum
{
    SCAN_MODE_AUTO = 0,             ///< auto selection flat glass or glass
    SCAN_MODE_FB   = 1,             ///< paper source by flat glass
    SCAN_MODE_ADF  = 2,             ///< paper source by single ADF
    SCAN_MODE_MADF = 3,             ///< paper source by manual ADF
    SCAN_MODE_DADF = 4,             ///< paper source by double DADF
    SCAN_MODE_RADF = 5,             ///< paper source by RADF
    SCAN_MODE_MAX = 0xFFFFFFFF

}SCAN_MODE;



typedef enum
{
    SCAN_COLOR_MONO = 0,     ///< color by mono
    SCAN_COLOR_RGB  = 1,     ///< color by rgb,
    SCAN_COLOR_GRAY = 2,     ///< color by gray,
    SCNA_COLOR_OTHER
}SCAN_COLOR_MODE;

typedef enum
{
    DPI_75     = 75,        ///< 75 dpi
    DPI_150    = 150,       ///< 150 dpi
    DPI_200    = 200,       ///< 200 dpi
    DPI_300    = 300,       ///< 300 dpi
    DPI_600    = 600,       ///< 600 dpi
    DPI_1200   = 1200,      ///< 1200 dpi
    DPI_2400CQ = 2400,
    DPI_MAX    = 0xFFFFFFFF
}SCAN_RESOLUTION;

typedef enum
{
    QUALITY_NONE    = 0,         ///< scan image quality invaild param
    QUALITY_TXT     = 1,         ///< scan image quality by text
    QUALITY_MIXED   = 2,         ///< scan image quality by text mixed picture
    QUALITY_PICTURE = 3,         ///< scan image quality by picture
    QUALITY_MAX     = 0xFFFFFFFF

}SCAN_QUALITY_MODE;


typedef enum
{
    SCAN_PAPER_SIZE_A4                   = 0,///< 210.0 x 297.0
    SCAN_PAPER_SIZE_A5,                      ///< 148.5 x 210.0
    SCAN_PAPER_SIZE_A5L,                     ///< 210.0 x 148.0
    SCAN_PAPER_SIZE_B5,
    SCAN_PAPER_SIZE_FULL_PLATEN,
    SCAN_PAPER_SIZE_LETTER,                  ///< 215.9 x 279.4
    SCAN_PAPER_SIZE_CARD,
    SCAN_PAPER_SIZE_FOLIO,                   ///< 216.0 x 330.0
    SCAN_PAPER_SIZE_ISO_B5,                  ///< 176.0 x 250.0
    SCAN_PAPER_SIZE_A6,                      ///< 105.0 x 148.0
    SCAN_PAPER_SIZE_USER_DEFINE,             ///< 75~218x148~356,long paper above 356~1200
    SCAN_PAPER_SIZE_LEGAL13,                 ///< 215.9 x 330.0
    SCAN_PAPER_SIZE_LEGAL14,                 ///< 215.9 x 355.6
    SCAN_PAPER_SIZE_JIS_B5,                  ///< 182.0 x 257.0
    SCAN_PAPER_SIZE_ENV_MONARCH,             ///< 098.4 x 190.5
    SCAN_PAPER_SIZE_ENV_DL,                  ///< 110.0 x 220.0
    SCAN_PAPER_SIZE_ENV_C5,                  ///< 162.0 x 229.0
    SCAN_PAPER_SIZE_ENV_10,                  ///< 104.8 x 241.3
    SCAN_PAPER_SIZE_YOUKEI_SIZE4,            ///< 105.0 x 234.0
    SCAN_PAPER_SIZE_JAPANESE_POSTCARD,       ///< 100.0 x 148.0
    SCAN_PAPER_SIZE_CHOUKEI_SIZE3,           ///< 120.0 x 235.0
    SCAN_PAPER_SIZE_CUSTOM_16K,              ///< 185.0 x 260.0
    SCAN_PAPER_SIZE_CUSTOM_BIG_16K,          ///< 195.0 x 270.0
    SCAN_PAPER_SIZE_CUSTOM_32K,              ///< 130.0 x 185.0
    SCAN_PAPER_SIZE_CUSTOM_BIG_32K,          ///< 135.0 x 195.0
    SCAN_PAPER_SIZE_EXECUTIVE,               ///< 184.0 x 267.0
    SCAN_PAPER_SIZE_OFICIO,                  ///< 216.0 x 343.0
    SCAN_PAPER_SIZE_STATEMENT,               ///< 140.0 x 216.0
    SCAN_PAPER_SIZE_ENV_C6,                  ///< 114.3 x 162.0
    SCAN_PAPER_SIZE_ZL,                      ///< 120.0 x 230.0
    SCAN_PAPER_SIZE_B6,                      ///< 125.0 x 176.0
    SCAN_PAPER_SIZE_ENV_B6,                  ///< 125.0 x 176.0
    SCAN_PAPER_SIZE_POSTCARD,                ///< 148.0 x 200.0
    SCAN_PAPER_SIZE_YOUGATA2,                ///< 114.0 x 162.0
    SCAN_PAPER_SIZE_NAGAGATA3,               ///< 120.0 x 235.0
    SCAN_PAPER_SIZE_YOUNAGA3,                ///< 120.0 x 235.0
    SCAN_PAPER_SIZE_YOUGATA4,                ///< 105.0 x 235.0
    SCAN_PAPER_SIZE_LONG,                    ///< 210.0 x 1200.0
    SCAN_PAPER_SIZE_A3,                      ///< 297.0 x 420.0
    SCAN_PAPER_SIZE_A4L,                     ///< 297.0 x 210.0
    SCAN_PAPER_SIZE_JIS_B6,                  ///< 128.0 x 182.0
    SCAN_PAPER_SIZE_JIS_B4,                  ///< 257.0 x 364.0
    SCAN_PAPER_SIZE_4X6_INCH,                ///< 101.6 x 152.4 / 4'' x 6''
    SCAN_PAPER_SIZE_INVOICE,                 ///< 139.7 x 215.9 / 5.5'' x 8.5''
    SCAN_PAPER_SIZE_QUARTO,                  ///< 254.0 x 203.2 / 10'' x 8''
    SCAN_PAPER_SIZE_G_LETTER,                ///< 266.0 x 203.2 /10.5'' x 8''
    SCAN_PAPER_SIZE_11X14_INCH,              ///< 279.4 x 355.6 / 11'' x 14''
    SCAN_PAPER_SIZE_LEDGER,                  ///< 279.4 x 431.8 /11'' x 17''
    SCAN_PAPER_SIZE_8K,                      ///< 270.0 x 390.0
    SCAN_PAPER_SIZE_SRA3 ,                   ///< 320.0 x 450.0
    SCAN_PAPER_SIZE_FOOLSCAP1,               ///< 203.0 x 330.2 / 8''x13''
    SCAN_PAPER_SIZE_FOOLSCAP2,               ///< 209.6 x 330.2 / 8.25''x13''
    SCAN_PAPER_SIZE_FOOLSCAP3,               ///< 215.9 x 330.2 / 8.5''x13''
    SCAN_PAPER_SIZE_FOOLSCAP4,               ///< 220.0 x 330.0 / 8.65''x13''
    SCAN_PAPER_SIZE_FOOLSCAP5,               ///< 206.4 x 336.6 / 8.125''x13.25''
    SCAN_PAPER_SIZE_A3_WIDE1,                ///< 304.8 x 457.2 / 12''x18''
    SCAN_PAPER_SIZE_A3_WIDE2 ,               ///< 311.1 x 457.2 / 12.25''x18''
    SCAN_PAPER_SIZE_CUSTOM_BIG_16KL,         ///< 270.0 x 195.0
    SCAN_PAPER_SIZE_JIS_B5L ,                ///< 257.0 x 182.0
    SCAN_PAPER_SIZE_INVOICE_L,               ///< 215.9 x 139.7 / 8.5'' x 5.5''
    SCAN_PAPER_SIZE_EXECUTIVE_L,             ///< 266.7 x 184.2 / 10.5''x 7.25''
    SCAN_PAPER_SIZE_QUARTO_L	,               ///< 254.0 x 203.2 / 10'' x 8''
    SCAN_PAPER_SIZE_G_LETTER_L,              ///< 266.7 x 203.2 / 10.5''x 8''
    SCAN_PAPER_SIZE_LETTER_L,                ///< 279.4 x 215.9 / 11" x 8.5''
    SCAN_PAPER_SIZE_ISO_B5L,                 ///< 250.0 x 176.0

    SCAN_PAPER_SIZE_USER_DEFINE1,
    SCAN_PAPER_SIZE_USER_DEFINE2,
    SCAN_PAPER_SIZE_USER_DEFINE3,
    SCAN_PAPER_SIZE_USER_DEFINE4,
    SCAN_PAPER_SIZE_USER_DEFINE5,
    SCAN_PAPER_SIZE_B4 ,
    SCAN_PAPER_SIZE_A6CARD,

    SCAN_PAPER_SIZE_GENERAL,              ///< general size
    SCAN_PAPER_SIZE_MIXED  ,              ///< paper size by mixed
    SCAN_PAPER_SIZE_STATEMENT_L ,

    SCAN_PAPER_SIZE_B5L,
    SCAN_PAPER_SIZE_BIG_16K,
    SCAN_PAPER_SIZE_BIG_16KL,

    SCAN_PAPER_SIZE_AUTO,                           ///< paper size is auto selected
    SCAN_PAPER_SIZE_UNKOWN,
    SCAN_PAPER_SIZE_FULL_TABLE,                     ///< scan full table

    SCAN_PAPER_SIZE_INVALID              = 0xFF,    ///< please keep it in the end of the enum --- invalid data
    SCAN_PAPER_SIZE_MAX                  = 0xFFFFFFFF
}SCAN_AREA_TYPE;


typedef enum
{
    SCAN_FILE_TIFF           = 0,                  ///< TIFF format
    SCAN_FILE_JPEG           = 1,                  ///< JPEG format
    SCAN_FILE_PDF            = 2,                  ///< PDF format
    SCAN_FILE_PDF_NEXT_PAGE  = 3,
    SCAN_FILE_PDF_TMP        = 4,
    SCAN_FILE_BMP            = 5,                  ///< BMP format
    SCAN_FILE_OFD            = 6,                  ///< OFD format
    SCAN_FILE_OFD_NEXT_PAGE,
    SCAN_FILE_OFD_TMP,

    SCAN_FILE_XPS,                                 ///< XPS format
    SCAN_FILE_XPS_NEXT_PAGE,
    SCAN_FILE_XPS_TMP,
    SCAN_FILE_MAX            = 0xFFFFFFFF


}SCAN_FILE_TYPE;

typedef struct
{
	char     ftp_user_name[FTP_USER_NAME_LEN];
	char     ftp_server_name[FTP_SERVER_NAME_LEN];
	char     ftp_login_name[FTP_LOGIN_NAME_LEN];
	char     ftp_server_path[FTP_SERVER_PATH_LEN];
	char     ftp_password[FTP_PASSWORD_LEN];
	uint16_t ftp_port;
	uint16_t is_anonymity;
	uint16_t ftp_index;
}PEDK_SCAN_FTP_PARM;


typedef struct
{
	char     smb_user_name[SMB_USER_NAME_LEN];
	char     smb_server_name[SMB_SERVER_NAME_LEN];
	char     smb_login_name[SMB_LOGIN_NAME_LEN];
	char     smb_server_path[SMB_SERVER_PATH_LEN];
	char     smb_password[SMB_PASSWORD_LEN];
	uint16_t smb_port;
	uint16_t is_anonymity;
	uint16_t smb_index;

}PEDK_SCAN_SMB_PARM;


typedef struct
{
    char scanToHttpPath[HTTP_URL_PATH_LEN];
    char scanToHttpheaders[HTTP_HEADERS_PATH_LEN];
    int  is_certified;
}PEDK_SCAN_HTTP_PARM;


typedef struct
{
    int  mail_index;
    char mail_addr[MAIL_ADDR_NUM_MAX][MAIL_ADDR_LEN];

    PEDK_SCAN_FTP_PARM  ftp;

    PEDK_SCAN_SMB_PARM  smb;

    PEDK_SCAN_HTTP_PARM http;

}JSAddressBookParam;


typedef struct
{
    int  result_code;
    char name[PEDK_SCAN_APP_FILE];
} AppParam;


typedef struct {
    SCAN_JOB_TYPE           job;
    SCAN_MODE               scan_mode;
    SCAN_COLOR_MODE         scan_color;
    SCAN_RESOLUTION         xres;
    SCAN_RESOLUTION         yres;
    SCAN_AREA_TYPE          scan_area;
    SCAN_FILE_TYPE          file_type;
    unsigned int            area_width;
    unsigned int            area_height;

    SCAN_QUALITY_MODE       quality;
    int                     combination;
    unsigned int            sharpness;
    unsigned int            brightness;
    unsigned int            contrast;
    unsigned int            hue;
	uint32_t                backgroundremove;
	uint32_t                saturation;
    int                     quota;
    int                     wo_num ;
    JSAddressBookParam      bookparam;

} PESF_SCAN_DATA;

JSModuleDef* js_scan_api_init( JSContext* ctx,  JSValueConst this_obj);

//////////////////////////////////////////////////////////
//
//
//             Scan
//
//////////////////////////////////////////////////////////


#endif /* _PESF_JOBS_SCAN_ */
