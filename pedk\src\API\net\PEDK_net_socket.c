#include "PEDK_net_socket.h"
#include "net_private.h"
//#include "pesf_event.h"

#include <string.h>
#include <strings.h>
#include <quickjs.h>
#include <stdbool.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <signal.h> // for signal(SIGPIP<PERSON>, SIG_IGN);
#include "errno.h"

#define DEFINE_VALUE_CONVERT_TO_INT32(js_val, to_name)      int32_t to_name = 0; \
                                                            if (JS_ToInt32(ctx, &to_name, js_val)) { \
                                                                return JS_ThrowTypeError(ctx, "EINVALIDPARAM"); \
                                                            }
#define COUNT_OF(x) (sizeof(x) / sizeof((x)[0]))
#define RETURN_VALUE_IF_EXCEPTION(js_val, ret_val)      if (JS_IsException(js_val)) { \
                                                            NET_LOG_ERROR("("#js_val") exception, %s\n", PRETTY_FUNCTION_NAME); \
                                                            return ret_val;\
                                                        }

typedef struct {
    int fd;
} STNetSocketData;

static JSClassID g_net_socket_class_id;

static JSValue jsSysNetSocket(JSContext *ctx, JSValueConst new_target, int argc, JSValueConst *argv) {
    STNetSocketData* s;
    JSValue obj = JS_UNDEFINED;
    JSValue proto;

    NET_LOG_DEBUG("argc %d", argc);
    if( (argc < 3) || !JS_IsNumber(argv[0]) || !JS_IsNumber(argv[2]) || !JS_IsNumber(argv[1]) )
    {
        return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
    }
    DEFINE_VALUE_CONVERT_TO_INT32(argv[0], domain);
    DEFINE_VALUE_CONVERT_TO_INT32(argv[1], type);
    DEFINE_VALUE_CONVERT_TO_INT32(argv[2], protocol);

    /* 限制只能使用普通IPv4v6 TCP/UDP socket, 不提供操作内核接口的能力 */
    domain &= (AF_INET|AF_INET6);
    type &= (SOCK_STREAM|SOCK_DGRAM);
    if (!domain || !type) {
        return JS_ThrowTypeError(ctx, "ENOTALLOWED");
    }

    /* using new_target to get the prototype is necessary when the
    class is extended. */
    proto = JS_GetPropertyStr(ctx, new_target, "prototype");
    RETURN_VALUE_IF_EXCEPTION(proto, JS_EXCEPTION);
    obj = JS_NewObjectProtoClass(ctx, proto, g_net_socket_class_id);
    JS_FreeValue(ctx, proto);
    RETURN_VALUE_IF_EXCEPTION(obj, JS_EXCEPTION);

    s = js_mallocz(ctx, sizeof(*s));
    if ( PESF_UNLIKELY(s == NULL) ) {
        JS_FreeValue(ctx, obj);
        return JS_EXCEPTION;
    }
    s->fd = socket(domain, type, protocol);
    NET_LOG_DEBUG("fd %d\n", s->fd);

    JS_SetOpaque(obj, s);
    return obj;
}

/**
 *
 * @param ctx
 * @param this_val
 * @param argc
 * @param argv 0: ip address string(ipv4 or ipv6); 1: port;
 * @return
 */
static JSValue jsSysNetConnect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {
    int rc = 0;
    int domain = 0;
    const char* ipaddr = NULL;
    void* pbuf;
    struct sockaddr_storage dst_addr;

    if( (argc < 2) || !JS_IsString(argv[0]) || !JS_IsNumber(argv[1])
        || ( NULL == (ipaddr = JS_ToCString(ctx, argv[0])) ) )
    {
        return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
    }
    DEFINE_VALUE_CONVERT_TO_INT32(argv[1], port);
    //DEFINE_VALUE_CONVERT_TO_INT32(argv[2], fd);
    bzero(&dst_addr, sizeof(dst_addr));
    /* 包含冒号认为是ipv6地址 */
    if (memchr(ipaddr, ':', strlen(ipaddr))) {
        domain = AF_INET6;
        struct sockaddr_in6* ipv6 = (struct sockaddr_in6*)(&dst_addr);
        pbuf = &(ipv6->sin6_addr.s6_addr);
        ipv6->sin6_family = AF_INET6;
        ipv6->sin6_port = htons(port);
    } else {
        domain = AF_INET;
        struct sockaddr_in* ipv4 = (struct sockaddr_in*)(&dst_addr);
        pbuf = &(ipv4->sin_addr.s_addr);
        ipv4->sin_family = AF_INET;
        ipv4->sin_port = htons(port);
    }
    rc = inet_pton(domain, ipaddr, pbuf);
    if (rc <= 0) {
        JS_FreeCString(ctx, ipaddr);
        return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
    }

    STNetSocketData *s = JS_GetOpaque2(ctx, this_val, g_net_socket_class_id);
    RETURN_VAL_IF_FAIL(s, JS_EXCEPTION);

    rc = connect(s->fd, (const struct sockaddr*)(&dst_addr), sizeof(dst_addr));
    NET_LOG_DEBUG("connect return %d, errno %d: %s\n", rc, errno, strerror(errno));
    if (rc) {
        NET_LOG_INFO("connect failed %d\n", rc);
        close(s->fd);
        s->fd = -1;
    }

    JS_FreeCString(ctx, ipaddr);
    return JS_NewInt32(ctx, rc);
}

static JSValue jsSysNetRecvSend(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv, int magic) {
    int fd;
    JSValue val;
    JSValue buff;
    ssize_t rc = 0;
    size_t buf_len = 0;
    char* buf = NULL;
    bool need_free = false;

    if( (argc < 2) || !JS_IsNumber(argv[1]) )
    {
        return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
    }

    DEFINE_VALUE_CONVERT_TO_INT32(argv[1], flags);
    //DEFINE_VALUE_CONVERT_TO_INT32(argv[0], fd);
    STNetSocketData *s = JS_GetOpaque2(ctx, this_val, g_net_socket_class_id);
    RETURN_VAL_IF_FAIL(s, JS_EXCEPTION);
    fd = s->fd;

    if ( PESF_UNLIKELY(fd < 0) )
    {
        NET_LOG_ERROR("bad fd %d\n", fd);
        return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
    }

    buff = argv[0];
    if (JS_IsString(buff)) {
        buf = (char*)JS_ToCStringLen(ctx, &buf_len, buff);
        if (!buf[0])
            buf_len = 1;
        need_free = true;
    } else {
        buf = (char*)JS_GetArrayBuffer(ctx, &buf_len, buff);
    }
    if (PESF_UNLIKELY(!buf))
        return JS_EXCEPTION;
    if (magic) {
        rc = send(fd, buf, buf_len, flags);
    } else {
        rc = recv(fd, buf, buf_len, flags);
    }

    if ( need_free ) {
        JS_FreeCString(ctx, buf);
    }
    val = JS_NewInt64(ctx, rc);

    return val;
}

JSValue jsSysCloseFd(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {
    JSValue val;
    STNetSocketData *s = JS_GetOpaque2(ctx, this_val, g_net_socket_class_id);
    RETURN_VAL_IF_FAIL(s, JS_EXCEPTION);
    val = JS_NewInt32(ctx, close(s->fd));
    s->fd = -1;

    return val;
}

static void js_point_finalizer(JSRuntime *rt, JSValue val)
{
    STNetSocketData *s = JS_GetOpaque(val, g_net_socket_class_id);
    /* Note: 's' can be NULL in case JS_SetOpaque() was not called */
    RETURN_IF_FAIL(s);
    if (s->fd > 0) {
        close(s->fd);
    }
    s->fd = -1;

    js_free_rt(rt, s);
}

static JSClassDef g_net_socket_class = {
    "Socket",
    .finalizer = js_point_finalizer,
};

#define OS_FLAG(x) JS_PROP_INT32_DEF(#x, x, JS_PROP_CONFIGURABLE )
static const JSCFunctionListEntry g_net_socket_defines[] = {
    OS_FLAG(AF_INET),
    OS_FLAG(AF_INET6),
    OS_FLAG(SOCK_STREAM),
    OS_FLAG(SOCK_DGRAM),
    OS_FLAG(MSG_DONTWAIT),
    OS_FLAG(MSG_ERRQUEUE),
    OS_FLAG(MSG_OOB),
    OS_FLAG(MSG_PEEK),
    OS_FLAG(MSG_WAITALL),
    OS_FLAG(MSG_CONFIRM),
    OS_FLAG(MSG_MORE),
    OS_FLAG(MSG_NOSIGNAL),
};

static const JSCFunctionListEntry g_net_socket_funcs[] = {
    JS_CFUNC_DEF("connect", 2, jsSysNetConnect),
    JS_CFUNC_MAGIC_DEF("recv", 2, jsSysNetRecvSend, 0),
    JS_CFUNC_MAGIC_DEF("send", 2, jsSysNetRecvSend, 1),
    JS_CFUNC_DEF("close", 0, jsSysCloseFd),
};


void addSocket(JSContext* ctx, JSValue container) {
    JSValue proto, socket_class;

    /* Socket class */
    JS_NewClassID(&g_net_socket_class_id);
    JS_NewClass(JS_GetRuntime(ctx), g_net_socket_class_id, &g_net_socket_class);

    proto = JS_NewObject(ctx);
    JS_SetPropertyFunctionList(ctx, proto, g_net_socket_funcs, COUNT_OF(g_net_socket_funcs));
    NET_LOG_DEBUG("count %d\n", COUNT_OF(g_net_socket_funcs));

    socket_class = JS_NewCFunction2(ctx, jsSysNetSocket, "Socket", 3, JS_CFUNC_constructor, 0);
    JS_SetConstructor(ctx, socket_class, proto);
    JS_SetClassProto(ctx, g_net_socket_class_id, proto);

    JSValue global = JS_GetGlobalObject(ctx);
    JS_DefinePropertyValueStr(ctx, container, "Socket", socket_class, JS_PROP_C_W_E);

    // 定义全局的只读变量
    JS_SetPropertyFunctionList(ctx, global, g_net_socket_defines, COUNT_OF(g_net_socket_defines));
    /*
     * JSValue val = JS_NewInt32(ctx, AF_INET);
     * JS_SetPropertyStr(ctx, global, "AF_INET", val);
     * 这种方式也可以，但不是只读的
     * console.log(AF_INET);// 2
     * AF_INET = 11;
     * console.log(AF_INET);// 11
     **/
    JS_FreeValue(ctx, global);

    signal(SIGPIPE, SIG_IGN);
}
