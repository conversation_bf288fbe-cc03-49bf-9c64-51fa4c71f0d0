#include <unistd.h>
#include <stdio.h>
#include <gtest/gtest.h>
#include <pol/pol_types.h>
#include <pol/pol_threads.h>
#include "nvram.h"

struct test
{
    int a;
    int b;
};

static struct test t1 = 
{
    .a = 10,
    .b = 11
};


static NVRAM_TABLE_S t_tables[] = 
{
    {1,VTYPE_UINT,(void *)0,0,0} ,
    {2,VTYPE_INT,(void *)14354611,0,1} ,
    {3,VTYPE_STRING,(void*)"hello world",11,0} ,
    {4,VTYPE_STRUCT,(void*)&t1,sizeof(t1),0}
};

TEST(nvram,nvram_set)
{
    unsigned int number = sizeof(t_tables)/sizeof(t_tables[0]);
    EXPECT_EQ(0, pi_threads_prolog());
    EXPECT_EQ(0, pi_nvram_init((void*)t_tables , number));
    EXPECT_EQ(0, pi_nvram_set(t_tables[0].id , t_tables[0].tp , (void*)10 , 0 , 1 , NULL));
    EXPECT_EQ(0, pi_nvram_set(t_tables[1].id , t_tables[1].tp , (void*)-10 , 0 , 1 , NULL));
    EXPECT_EQ(0, pi_nvram_set(t_tables[2].id , t_tables[2].tp , (void*)"nvram" , 5 , 1 , NULL));
    struct test value;
    value.a = 2;
    value.b = 2;
    EXPECT_EQ(0, pi_nvram_set(t_tables[3].id , t_tables[3].tp , (void*)&value , sizeof(value) , 1 , NULL));
}

TEST(nvram,nvram_get)
{
    int number = sizeof(t_tables)/sizeof(t_tables[0]);
    EXPECT_EQ(0, pi_threads_prolog());
    EXPECT_EQ(0, pi_nvram_init(t_tables , number));

    unsigned int value1 = 0;
    pi_nvram_get(t_tables[0].id , t_tables[0].tp , &value1 , sizeof(value1));
    EXPECT_EQ(10 , value1);
    
    int value2 = 0;
    pi_nvram_get(t_tables[1].id , t_tables[1].tp , &value2 , sizeof(value2));
    EXPECT_EQ(-10 , value2);

    char buf[128];
    pi_nvram_get(t_tables[2].id , t_tables[2].tp , buf , sizeof(buf));
    EXPECT_STREQ("nvram" , buf);

    struct test value3;
    pi_nvram_get(t_tables[3].id , t_tables[3].tp , &value3 , sizeof(value3));
    EXPECT_EQ(2 , value3.a);
    EXPECT_EQ(2 , value3.b);
}

TEST(nvram,nvram_factory)
{
    int number = sizeof(t_tables)/sizeof(t_tables[0]);
    EXPECT_EQ(0, pi_threads_prolog());
    EXPECT_EQ(0, pi_nvram_init(t_tables , number));
    EXPECT_EQ(0, pi_nvram_recover_factory());

    unsigned int value1 = 0;
    pi_nvram_get(t_tables[0].id , t_tables[0].tp , &value1 , sizeof(value1));
    EXPECT_EQ(0 , value1);
    
    int value2 = 0;
    pi_nvram_get(t_tables[1].id , t_tables[1].tp , &value2 , sizeof(value2));
    EXPECT_EQ(-10 , value2);

    char buf[128];
    pi_nvram_get(t_tables[2].id , t_tables[2].tp , buf , sizeof(buf));
    EXPECT_STREQ("hello world" , buf);

    struct test value3;
    pi_nvram_get(t_tables[3].id , t_tables[3].tp , &value3 , sizeof(value3));
    EXPECT_EQ(10 , value3.a);
    EXPECT_EQ(11 , value3.b);
}
