/**************************************************************
Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:     PEDK setting
file name:       PEDK_setting.h
date:            2024-10-25
description:     pedk setting API
**************************************************************/
#ifndef __PEDK_SETTING_H__
#define __PEDK_SETTING_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "pedk_mgr.h"

typedef enum
{
    FUNC_T_USBPORT_PRINT = 0,
    FUNC_T_PULL_SCAN ,
    FUNC_T_PUSH_SCAN ,
    FUNC_T_COPY ,
    FUNC_T_IDCOPY ,
    FUNC_T_BILL ,
    FUNC_T_COLOR_PRINT ,
    FUNC_T_COLOR_COPY ,
    FUNC_T_FAX ,
    FUNC_T_NET_COMMUNICATION ,
    FUNC_T_MENU_SETTING ,
    FUNC_T_MENU_SYSTEM_SETTING ,
    FUNC_T_MENU_PRINT_SETTING ,
    FUNC_T_MENU_TRAY_SETTING ,
    FUNC_T_MENU_NETWORK_SETTING ,
    FUNC_T_MENU_SAMPLE_PRINT_SETTING ,
    FUNC_T_MENU_SHORTCUT_SETTING ,
    FUNC_T_MENU_JOB_LIST_SETTING ,
    FUNC_T_MENU_ADDRESS_BOOK_SETTING ,
    FUNC_T_MENU_CUSTOM_DESKTOP_SETTING ,
    FUNC_T_SECURE_PRINT ,
    FUNC_T_SCAN_TO_USB ,
    FUNC_T_SCAN_TO_PC ,
    FUNC_T_SCAN_TO_SMB ,
    FUNC_T_SCAN_TO_FTP ,
    FUNC_T_SCAN_TO_EMAIL ,
    FUNC_T_NET_PRINT ,
    FUNC_T_MONO_PRINT ,
    FUNC_T_USB_ENABLE ,
}FUNCT_TYPE_E;

typedef enum
{
    FUNC_DISABLE = 0,
    FUNC_ENABLE  = 1,
}FUNC_SWITCH_E;

typedef struct
{
    FUNCT_TYPE_E  funcion;
    FUNC_SWITCH_E switch_flag;
}SET_FUNC_SWITCH_S;

int32_t setting_pedk_init( void );

#ifdef __cplusplus
}
#endif

#endif


