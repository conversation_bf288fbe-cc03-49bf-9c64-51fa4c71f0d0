/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi_main.c
 * @addtogroup net
 * @{
 * @addtogroup wifi_main
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WiFi main program
 */
#include "nettypes.h"
#include "netmisc.h"
#include "netctx.h"
#include "wifi_process_queue.h"
#include "wifi_ctrl.h"
#include "wifi.h"
#include "ipv6.h"
#include "ipv4.h"

static PI_THREAD_T s_wifi_tid = INVALIDTHREAD;

static void get_random(char* buf, size_t count)
{
    FILE*  stream = NULL;
    size_t value = 0;
    size_t i = 0;
    char   c;

    stream = pi_fopen("/dev/urandom", "rb");
    RETURN_IF(stream == NULL, NET_WARN);

    do
    {
        fread(&value, 1, sizeof(value), stream);
        c = (char)(value % 123);
        if ( isalnum(c) )
        {
            buf[i++] = c;
        }
    }
    while ( i < count );

    pi_fclose(stream);
}

static int32_t check_necessary_components(void)
{
    RETURN_VAL_IF(access("/usr/sbin/wpa_supplicant", X_OK) != 0, NET_WARN, -1);

    return 0;
}

static void wifi_report_fatal_error(NET_CTX_S* net_ctx)
{
    WIFI_CONN_INFO_S conn_info = { .status = NETLINK_STATUS_DISCONNECTED, .detail = WIFI_CONNECTION_DETAIL_NONE };
    uint32_t         status[4] = { STATUS_E_NET_WIFI_INIT_ERROR, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF };

    netdata_set_sta_status(net_ctx->data_mgr, conn_info.status);
    NETEVT_NOTIFY_T(EVT_TYPE_NET_WIFI_CONNECTION_CHANGED, conn_info);
    NETEVT_NOTIFY_T(EVT_TYPE_SYSTEMSTATUS_FROM_NET, status);
}

static int32_t wifi_init_station(NET_CTX_S* net_ctx)
{
    char        sta_ssid[128];
    uint32_t    enabled;

    /* wpa_supplicant初始化 */
    netdata_set_sta_pmf(net_ctx->data_mgr, PMF_AUTO);
    wifi_ctrl_sta_wpa_reconfigure();

    /* 获取重启前的IFACE_STA开关状态 */
    enabled = netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_STA);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_WIFI_SWITCH_CHANGED, enabled);
    wifi_ctrl_sta_switch(enabled);

    netdata_get_sta_ssid(net_ctx->data_mgr, sta_ssid, sizeof(sta_ssid));
    if ( enabled && sta_ssid[0] != '\0' )
    {
        wifi_ctrl_sta_connect(0); /* 重启后发起自动重连 */
    }

    return 0;
}

static int32_t wifi_init_direct(NET_CTX_S* net_ctx)
{
    DATA_MGR_S*     data_mgr = net_ctx->data_mgr;
    char            wfd_ssid_prefix[32];
    char            wfd_ssid_suffix[32];
    char            mfg_name[16];
    char            ser_name[32];
    char            wfd_psk[64];
    char            random_str[3];
    uint32_t        val;

    /* 检查wfd_ssid_prefix，首次启动时为空串，基于厂商名称mfg_name生成默认值，DIRECT-[2 bytes random]-[mfg_name] */
    netdata_get_wfd_ssid_prefix(data_mgr, wfd_ssid_prefix, sizeof(wfd_ssid_prefix));
    if ( STRING_IS_EMPTY(wfd_ssid_prefix) )
    {
        netdata_get_mfg_name(data_mgr, mfg_name, sizeof(mfg_name));
        memset(random_str, 0, sizeof(random_str));
        get_random(random_str, 2);
        snprintf(wfd_ssid_prefix, sizeof(wfd_ssid_prefix), "DIRECT-%s-%s", random_str, mfg_name);
        netdata_set_wfd_ssid_prefix(data_mgr, wfd_ssid_prefix);
    }
    NET_DEBUG("wfd_ssid_prefix(%s)", wfd_ssid_prefix);
    NETEVT_NOTIFY_S(EVT_TYPE_NET_WFD_SSID_PREFIX_CHANGED, wfd_ssid_prefix);

    /* 检查wfd_ssid_suffix，首次启动或恢复出厂时为空串，基于产品系列名称ser_name生成默认值，[ser_name] Series */
    netdata_get_wfd_ssid_suffix(data_mgr, wfd_ssid_suffix, sizeof(wfd_ssid_suffix));
    if ( STRING_IS_EMPTY(wfd_ssid_suffix) )
    {
        netdata_get_ser_name(data_mgr, ser_name, sizeof(ser_name));
        snprintf(wfd_ssid_suffix, sizeof(wfd_ssid_suffix), "%s Series", ser_name);
        netdata_set_wfd_ssid_suffix(data_mgr, wfd_ssid_suffix);
    }
    NET_DEBUG("wfd_ssid_suffix(%s)", wfd_ssid_suffix);
    NETEVT_NOTIFY_S(EVT_TYPE_NET_WFD_SSID_SUFFIX_CHANGED, wfd_ssid_suffix);

    netdata_get_wfd_psk(data_mgr, wfd_psk, sizeof(wfd_psk));
    NETEVT_NOTIFY_S(EVT_TYPE_NET_WFD_PASSWORD_CHANGED, wfd_psk);

    /* 获取重启前的IFACE_WFD开关状态 */
    val = netdata_get_iface_switch(data_mgr, IFACE_ID_WFD);
    NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_SWITCH_CHANGED, val);
    wifi_ctrl_wfd_switch(val);

    return 0;
}

static void* wifi_thread(void* arg)
{
    NET_CTX_S* net_ctx = (NET_CTX_S *)arg;

    if ( wifi_process_queue_init() != 0 )
    {
        NET_WARN("wifi process queue init failed");
        wifi_report_fatal_error(net_ctx);
        return NULL;
    }

    if ( wifi_init_direct(net_ctx) != 0 || wifi_init_station(net_ctx) != 0 )
    {
        NET_WARN("wifi station or direct init failed");
        wifi_report_fatal_error(net_ctx);
        wifi_process_queue_deinit();
    }

    wifi_process_queue_run(net_ctx);
    NET_WARN("exit!!!");

    return NULL;
}

int32_t wifi_prolog(NET_CTX_S* net_ctx)
{
    if ( check_necessary_components() < 0 )
    {
        NET_WARN("wifi necessary components missing");
        wifi_report_fatal_error(net_ctx);
        return -1;
    }

    s_wifi_tid = pi_thread_create(wifi_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, net_ctx, "wifi_thread");
    if ( s_wifi_tid == INVALIDTHREAD )
    {
        NET_WARN("create wifi thread failed");
        wifi_report_fatal_error(net_ctx);
        return -1;
    }

#if CONFIG_NET_BLUETOOTH
    bluetooth_prolog(net_ctx);
#endif

    return 0;
}

void wifi_epilog(void)
{
    if ( s_wifi_tid != INVALIDTHREAD )
    {
        pi_thread_destroy(s_wifi_tid);
        s_wifi_tid = INVALIDTHREAD;
    }
}
/**
 *@}
 */
