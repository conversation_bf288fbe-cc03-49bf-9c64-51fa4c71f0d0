/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_common.c
 * @addtogroup panel_dc
 * @{
 * @brief the common function and set config for panel_dc
 * <AUTHOR>
 * @date 2023-08-12
 */


#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "qio/qio_general.h"
#include "pol/pol_log.h"
#include "platform_api.h"
#include "panel_event.h"
#include "pol/pol_string.h"
#include "event_manager/event_mgr.h"
#include "platform_api.h"
#include "nvram.h"
#include "moduleid.h"

#include "event_manager/event_msg_typedef.h"
#include "panel_public.h"
#include "panel_event.h"
#include "panel_config.h"
#include "hal.h"
#include "security_register.h"
#include "acl/acl.h"
#include "pol/pol_endian.h"
#include "statusid.h"
#include "status_manager.h"
#include "acl/acl_attribute.h"
#include "panel_address_book.h"
#include "panel_pedk_mfp.h"
#include "panel_overlay_copy.h"
#include "panel_dc_main.h"

#define ACLCMD_SYSTEM_PARAMETER_OPERATE 0xFF28

#define ACLCMD_SET_SLEEP_TIME           0xFF82    ///< set sleep time
#define ACLCMD_GET_SLEEP_TIME           0xFF83    ///< get sleep time

#define WATER_MARK_NUM                  6

static PANEL_CONFIG_TABLE_S s_panel_config_table = {0};   /// save panel config for sync
static PANEL_STATUS_TABLE_S s_panel_status_table = {0};

static CUSTOM_WATER_MARK_S  s_panel_water_mark[WATER_MARK_NUM] = {0};

//static PANEL_PRINT_DEFAULT_CONFIG_S s_panel_print_default_config = {0};     ///save default print setting
//static PANEL_PRINT_IMAGE_CONFIG_S s_panel_print_image_config = {0};         ///save the print image config

/**
 * @brief Time of system sleeping, uint second
 *
 */
typedef enum
{
    PANEL_SLEEP_1MINUTES = 0,
    PANEL_SLEEP_2MINUTES,
    PANEL_SLEEP_3MINUTES,
    PANEL_SLEEP_4MINUTES,
    PANEL_SLEEP_5MINUTES,
    PANEL_SLEEP_10MINUTES,
    PANEL_SLEEP_15MINUTES,
    PANEL_SLEEP_20MINUTES,
    PANEL_SLEEP_25MINUTES,
    PANEL_SLEEP_30MINUTES,
    PANEL_SLEEP_40MINUTES,
    PANEL_SLEEP_60MINUTES,
    PANEL_SLEEP_2HOUR,
    PANEL_SLEEP_4HOUR,
    PANEL_SLEEP_6HOUR,
    PANEL_SLEEP_8HOUR,
    PANEL_SLEEP_ON,
    PANEL_SLEEP_OFF,
    PANEL_SLEEP_NEVER = PANEL_SLEEP_OFF

}PANEL_SLEEP_TIME_E;

typedef struct
{
    uint16_t cmd_id;
    uint32_t nvram_id;
    uint32_t* panel_config_id;
}PANEL_CMD_NV_U32_S;

typedef struct
{
    uint32_t nvram_id;
    uint32_t panel_security_operate_type;
}PANEL_SECURITY_OPERATE_S;

typedef struct
{

    uint16_t cmd_id;
    uint32_t nvram_id;
    char* data_str;
    uint32_t data_len;

}PANEL_CMD_NV_STRING_S;

typedef struct
{

    uint16_t cmd_id;
    uint32_t nvram_id;
    void* data_struct;
    uint32_t data_len;

}PANEL_CMD_NV_STRUCT_S;


/*
 *此表用于关联面板通信命令,nv id,及全局变量数据缓存
*/
PANEL_CMD_NV_U32_S cmd_nvid_uint32_table[] =
{
    { SETTING_CMD_SET_SLEEP_TIME,                    UI_ID_PANEL_SLEEP_TIME,              &s_panel_config_table.sleep_time },
    { SETTING_CMD_SET_SLEEP_MODE,                    UI_ID_PANEL_SLEEP_MODE,              &s_panel_config_table.sleep_mode },
    { SETTING_CMD_SET_TIME_FORMAT,                   UI_ID_PANEL_TIME_FORMAT,             &s_panel_config_table.time_format },
    { SETTING_CMD_SET_DATE_FORMAT,                   UI_ID_PANEL_DATE_FORMAT,             &s_panel_config_table.date_format  },
    { SETTING_CMD_SET_BRIGHTNESS,                    UI_ID_PANEL_LCD_BACKLIGHT,           &s_panel_config_table.lcd_backlight_set  },
    { SETTING_CMD_SET_BEEP_VOLUE,                    UI_ID_PANEL_SYS_VOLUE,               &s_panel_config_table.system_volume  },
    { SETTING_CMD_SET_MUTE_MODE,                     UI_ID_PANEL_QUIET_MODE,              &s_panel_config_table.quiet  },
    { SETTING_CMD_SET_USB_CONTROL,                   UI_ID_PANEL_USB_CONTROL,             &s_panel_config_table.usb_control  },
    { SETTING_CMD_SET_NET_CONTROL,                   UI_ID_PANEL_NET_CONTROL,             &s_panel_config_table.net_control },

    { SETTING_CMD_PRINT_SET_DEFAULT_TRAY,            UI_ID_PANEL_PRINT_DEFAULT_TRAY,      &s_panel_config_table.panel_print_default_config.default_tray },
    // { SETTING_CMD_PRINT_SET_TRAY_MULTI_SIZE,         UI_ID_PANEL_PRINT_TRAY_MULTI_SIZE,   &s_panel_config_table.panel_print_default_config.multifunction_tray_size  },
    // { SETTING_CMD_PRINT_SET_TRAY_MULTI_TYPE,         UI_ID_PANEL_PRINT_TRAY_MULTI_TYPE,   &s_panel_config_table.panel_print_default_config.multifunction_tray_type  },

    { SETTING_CMD_PRINT_SET_COLOR_BALANCE_C,         UI_ID_PANEL_PRINT_COLOR_BALANCE_C,   &s_panel_config_table.panel_print_image_config.color_balance_c },
    { SETTING_CMD_PRINT_SET_COLOR_BALANCE_M,         UI_ID_PANEL_PRINT_COLOR_BALANCE_M,   &s_panel_config_table.panel_print_image_config.color_balance_m  },
    { SETTING_CMD_PRINT_SET_COLOR_BALANCE_Y,         UI_ID_PANEL_PRINT_COLOR_BALANCE_Y,   &s_panel_config_table.panel_print_image_config.color_balance_y  },
    { SETTING_CMD_PRINT_SET_COLOR_BALANCE_K,         UI_ID_PANEL_PRINT_COLOR_BALANCE_K,   &s_panel_config_table.panel_print_image_config.color_balance_k  },
    { SETTING_CMD_PRINT_SET_IMAGE_SATURATION,        UI_ID_PANEL_PRINT_IMAGE_SATURATION,  &s_panel_config_table.panel_print_image_config.image_saturation },
    { SETTING_CMD_PRINT_SET_IMAGE_BRIGTHNESS,        UI_ID_PANEL_PRINT_IMAGE_BRIGTHNESS,  &s_panel_config_table.panel_print_image_config.image_brightness  },
    { SETTING_CMD_PRINT_SET_IMAGE_CONTRAST,          UI_ID_PANEL_PRINT_IMAGE_CONTRAST,    &s_panel_config_table.panel_print_image_config.image_contrast  },
    { SETTING_CMD_PRINT_SET_TONER_DENISTY,           UI_ID_PANEL_PRINT_TONER_DENISTY,     &s_panel_config_table.panel_print_image_config.toner_density  },
    { SETTING_CMD_PRINT_SET_SAVE_TONER_MODE,         UI_ID_PANEL_PRINT_SAVE_TONER_MODE,   &s_panel_config_table.panel_print_image_config.toner_save  },

    { SETTING_CMD_SET_AMBIENT_LIGHT,                 UI_ID_PANEL_AMBIENT_LIGHT,           &s_panel_config_table.ambient_light  },
    { SETTING_CMD_SET_JOB_ERROR_PROCESS_MODE,        UI_ID_PANEL_JOB_ERROR_PROCESS_MODE,  &s_panel_config_table.job_error_process_mode  },
    { SETTING_CMD_SET_JOB_ERROR_DELETE_TIME,         UI_ID_PANEL_JOB_ERROR_DELETE_TIME,   &s_panel_config_table.job_error_delete_time  },
    // { SETTING_CMD_PRINT_SET_TRAY_LCT_IN_SIZE,        UI_ID_PANEL_PRINT_TRAY_LCT_IN_SIZE,  &s_panel_config_table.panel_print_default_config.lct_in_tray_size  },
    // { SETTING_CMD_PRINT_SET_TRAY_LCT_IN_TYPE,        UI_ID_PANEL_PRINT_TRAY_LCT_IN_TYPE,  &s_panel_config_table.panel_print_default_config.lct_in_tray_type  },
    { SETTING_CMD_PRINT_SET_IMAGE_ORIGINAL_TYPE,     UI_ID_PANEL_PRINT_IMAGE_ORIGINAL_TYPE,&s_panel_config_table.panel_print_image_config.original_type  },

    // { SETTING_CMD_PRINT_SET_TRAY1_TYPE,              UI_ID_PANEL_PRINT_TRAY1_TYPE,   &s_panel_config_table.panel_print_default_config.tray1_type  },
    // { SETTING_CMD_PRINT_SET_TRAY2_TYPE,              UI_ID_PANEL_PRINT_TRAY2_TYPE,   &s_panel_config_table.panel_print_default_config.tray2_type  },
    // { SETTING_CMD_PRINT_SET_TRAY3_TYPE,              UI_ID_PANEL_PRINT_TRAY3_TYPE,   &s_panel_config_table.panel_print_default_config.tray3_type  },
    // { SETTING_CMD_PRINT_SET_TRAY4_TYPE,              UI_ID_PANEL_PRINT_TRAY4_TYPE,   &s_panel_config_table.panel_print_default_config.tray4_type  },

    { SETTING_CMD_SET_ERROR_STATUS_VOLUME,           UI_ID_PANEL_ERROR_STATUS_VOLUE,   &s_panel_config_table.error_status_volume  },
    { SETTING_CMD_SET_JOB_END_VOLUME,                UI_ID_PANEL_JOB_END_VOLUE,   &s_panel_config_table.job_end_volume  },

    { SETTING_CMD_SET_COLOR_COPY_ENABLE,             UI_ID_PANEL_COLOR_COPY_ENABLE,   &s_panel_config_table.color_copy_enable  },

    { SETTING_CMD_SET_LOCK_SCREEN_TIME,              UI_ID_PANEL_LOCK_SCREEN_TIME,   &s_panel_config_table.lock_screen_time  },
    { SETTING_CMD_SET_SYSTEM_SET_TIMEOUT,            UI_ID_PANEL_SCREENSAVER_TIMEOUT,   &s_panel_config_table.timeout_time  },

    { SETTING_CMD_SET_LOCK_SCREEN_SWITCH,            UI_ID_PANEL_LOCK_SCREEN_SWITCH,   &s_panel_config_table.lock_screen_switch  },
    { SETTING_CMD_SET_COPY_PARAM_RESET,              UI_ID_PANEL_COPY_PARAM_RESET,   &s_panel_config_table.copy_param_reset  },
    { SETTING_CMD_SET_COPY_RESET_RANGE_SWITCH,              UI_ID_PANEL_COPY_RANGE_RESET,   &s_panel_config_table.copy_range_reset  },
    { SETTING_CMD_SET_ADF_GET_PAPER_VOLUME,              UI_ID_PANEL_ADF_PAPER_IN_VOLUME,   &s_panel_config_table.adf_paper_in_volume  },
    { SETTING_CMD_MAINTENANCE_GET_LOG_SAVE_SWITCH,   UI_ID_PANEL_LOG_SAVE_SWITCH, &s_panel_config_table.log_save_switch },
};

PANEL_CMD_NV_STRING_S cmd_nvid_string_table[] =
{
    { SETTING_CMD_SET_LOCK_SCREEN_PASSWORD,          UI_ID_PANEL_LOCK_SCREEN_PASSWORD,      s_panel_config_table.lock_screen_password,      9  },
    { SETTING_CMD_SET_COLOR_COPY_PASSWORD,           UI_ID_PANEL_COLOR_COPY_PASSWORD,       s_panel_config_table.color_copy_pwd,           8  },
};

PANEL_CMD_NV_STRUCT_S cmd_nvid_struct_table[] =
{
    { SETTING_CMD_UPDATE_SHORTCUT_COPY,          UI_ID_PANEL_SHORTCUT_COPY,      &s_panel_config_table.panel_shorcut_copy_param_s,      sizeof(PANLE_SHORTCUT_COPY_PARAM_S)  },
    { SETTING_CMD_SET_JOB_ADVANCE_IMAGE,          UI_ID_PANEL_JOB_ADVANCE_IMAGE,      &s_panel_config_table.job_advance_image,      sizeof(PANEL_JOB_ADVANCE_IMAGE_S)  },
};


PANEL_SECURITY_OPERATE_S panel_security_operate[] =
{
    { UI_ID_PANEL_SLEEP_MODE            , SECURITY_OPERATE_SLEEP_MODE      },
    { UI_ID_PANEL_LCD_BACKLIGHT         , SECURITY_OPERATE_LCD_BACKLIGHT,        },
    { UI_ID_PANEL_SYS_VOLUE             , SECURITY_OPERATE_SYS_VOLUE,            },
    { UI_ID_PANEL_QUIET_MODE            , SECURITY_OPERATE_QUIET_MODE,           },
    { UI_ID_PANEL_USB_CONTROL           , SECURITY_OPERATE_USB_CONTROL,          },
    { UI_ID_PANEL_NET_CONTROL           , SECURITY_OPERATE_NET_CONTROL,          },
    { UI_ID_PANEL_PRINT_DEFAULT_TRAY    , SECURITY_OPERATE_PRINT_DEFAULT_TRAY,   },
    { UI_ID_PANEL_PRINT_TRAY_MULTI_SIZE , SECURITY_OPERATE_PRINT_TRAY_MULTI_SIZE,},
    { UI_ID_PANEL_PRINT_TRAY_MULTI_TYPE , SECURITY_OPERATE_PRINT_TRAY_MULTI_TYPE,},
    { UI_ID_PANEL_PRINT_COLOR_BALANCE_C , SECURITY_OPERATE_PRINT_COLOR_BALANCE_C,},
    { UI_ID_PANEL_PRINT_COLOR_BALANCE_M , SECURITY_OPERATE_PRINT_COLOR_BALANCE_M,},
    { UI_ID_PANEL_PRINT_COLOR_BALANCE_Y , SECURITY_OPERATE_PRINT_COLOR_BALANCE_Y,},
    { UI_ID_PANEL_PRINT_COLOR_BALANCE_K , SECURITY_OPERATE_PRINT_COLOR_BALANCE_K,},
    { UI_ID_PANEL_PRINT_IMAGE_SATURATION, SECURITY_OPERATE_PRINT_IMAGE_SATURATION},
    { UI_ID_PANEL_PRINT_IMAGE_BRIGTHNESS, SECURITY_OPERATE_PRINT_IMAGE_BRIGTHNESS},
    { UI_ID_PANEL_PRINT_IMAGE_CONTRAST  , SECURITY_OPERATE_PRINT_IMAGE_CONTRAST, },
    { UI_ID_PANEL_PRINT_TONER_DENISTY   , SECURITY_OPERATE_PRINT_TONER_DENISTY,  },
    { UI_ID_PANEL_PRINT_SAVE_TONER_MODE , SECURITY_OPERATE_PRINT_SAVE_TONER_MODE,},

    { UI_ID_PANEL_AMBIENT_LIGHT                 ,    SECURITY_OPERATE_AMBIENT_LIGHT             },
    { UI_ID_PANEL_JOB_ERROR_PROCESS_MODE        ,    SECURITY_OPERATE_JOB_ERROR_PROCESS_MODE    },
    { UI_ID_PANEL_JOB_ERROR_DELETE_TIME         ,    SECURITY_OPERATE_JOB_ERROR_DELETE_TIME     },
    { UI_ID_PANEL_PRINT_TRAY_LCT_IN_SIZE        ,    SECURITY_OPERATE_PRINT_TRAY_LCT_IN_SIZE    },
    { UI_ID_PANEL_PRINT_TRAY_LCT_IN_TYPE        ,    SECURITY_OPERATE_PRINT_TRAY_LCT_IN_TYPE    },
    { UI_ID_PANEL_PRINT_IMAGE_ORIGINAL_TYPE     ,    SECURITY_OPERATE_PRINT_IMAGE_ORIGINAL_TYPE },

    {  UI_ID_PANEL_SCREENSAVER_TIMEOUT          ,    SECURITY_OPERATE_SCREENSAVER_TIMEOUT    },
    {  UI_ID_PANEL_ERROR_STATUS_VOLUE           ,    SECURITY_OPERATE_ERROR_STATUS_VOLUME    },
    {  UI_ID_PANEL_JOB_END_VOLUE                ,    SECURITY_OPERATE_JOB_END_VOLUME    },
    {  UI_ID_PANEL_COPY_PARAM_RESET             ,    SECURITY_OPERATE_COPY_PARAM_RESET    },
    {  UI_ID_PANEL_PRINT_CUSTOM1_SIZE_LENGHT    ,    SECURITY_OPERATE_CUSTOM1_SIZE_LENGTH    },
    {  UI_ID_PANEL_PRINT_CUSTOM2_SIZE_LENGHT    ,    SECURITY_OPERATE_CUSTOM2_SIZE_LENGTH    },
    {  UI_ID_PANEL_PRINT_CUSTOM3_SIZE_LENGHT    ,    SECURITY_OPERATE_CUSTOM3_SIZE_LENGTH    },
    {  UI_ID_PANEL_PRINT_CUSTOM1_SIZE_HEIGHT    ,    SECURITY_OPERATE_CUSTOM1_SIZE_HEIGHT    },
    {  UI_ID_PANEL_PRINT_CUSTOM2_SIZE_HEIGHT    ,    SECURITY_OPERATE_CUSTOM2_SIZE_HEIGHT    },
    {  UI_ID_PANEL_PRINT_CUSTOM3_SIZE_HEIGHT    ,    SECURITY_OPERATE_CUSTOM3_SIZE_HEIGHT    },
    {  UI_ID_PANEL_LOCK_SCREEN_SWITCH           ,    SECURITY_OPERATE_LOCK_SCREEN_SWITCH    },
    {  UI_ID_PANEL_LOCK_SCREEN_TIME             ,    SECURITY_OPERATE_LOCK_SCREEN_TIME    },
    {  UI_ID_PANEL_LOCK_SCREEN_PASSWORD         ,    SECURITY_OPERATE_LOCK_SCREEN_PASSWORD    },

    {  UI_ID_PANEL_COLOR_COPY_ENABLE            ,    SECURITY_OPERATE_SET_COLOR_COPY_ENABLE    },
    {  UI_ID_PANEL_COLOR_COPY_PASSWORD          ,    SECURITY_OPERATE_SET_COLOR_COPY_PASSWORD    },
    {  UI_ID_PANEL_ADF_PAPER_IN_VOLUME          ,    SECURITY_OPERATE_SET_ADF_PAPER_IN_VOLUME    },

};

int32_t get_panel_security_operate_type_by_nvid(uint32_t nvram_id)
{
    for( int i = 0; i < sizeof(panel_security_operate)/sizeof(panel_security_operate[0]); i++ )
    {
        if( panel_security_operate[i].nvram_id == nvram_id )
        {
            return panel_security_operate[i].panel_security_operate_type ;
        }
    }
    return -1;
}

void notify_security_operate( uint32_t operate_type, void* operate_value, uint32_t value_len )
{
    EVT_MGR_CLI_S* panel_event_client = NULL;
    SECURITY_OPERATE_INFO_S security_operate = {0};

    panel_event_client = get_panel_event_client();

    security_operate.op_type = operate_type;
    if( (NULL != operate_value ) && (0 != value_len ) && ( value_len <=sizeof(security_operate.op_value) ) )
    {
        pi_memcpy( &(security_operate.op_value), operate_value, value_len );
    }
    pi_log_d("panel security operate type:%d, value_len:%d\n",operate_type, value_len );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_SECURITY_OPERATE_RECORD, &security_operate, sizeof( SECURITY_OPERATE_INFO_S ) );
}

PANEL_CONFIG_TABLE_S* get_panel_config_table( void )
{
    return &s_panel_config_table;
}

PANEL_STATUS_TABLE_S* get_panel_status_table( void )
{
    return &s_panel_status_table;
}

PANEL_PRINT_DEFAULT_CONFIG_S* get_panel_print_config()
{
    return &s_panel_config_table.panel_print_default_config;
}

PANEL_PRINT_IMAGE_CONFIG_S* get_print_image_config()
{
    return &s_panel_config_table.panel_print_image_config;
}

NET_IPV6_CONF_S* set_ipv6_config( uint32_t* ipv6_enable )
{
    s_panel_config_table.ipv6_config.enabled = *ipv6_enable;
    return &(s_panel_config_table.ipv6_config);
}

void panel_set_usb_control( uint32_t* data, uint32_t data_len )
{
    s_panel_config_table.usb_control = *data;
    void* handle = NULL;
    HAL_USBD_CTRL_E ctrl = HAL_USBD_DISABLE;
    int ret = 0;

    ret = pi_hal_usbd_request(HAL_USBD_PORT0, &handle, HAL_REQUEST_FLAG_NONBLOCK);
    if ( HAL_SUCCESS != ret )
    {
        pi_log_d("Failed to request virtual resource!!\n");
        return ;
    }

    if( s_panel_config_table.usb_control == 1 )
    {
        pi_log_d("panel request to enable usb port\n");

        ret = pi_hal_usbd_ctrl(handle, HAL_USBD_ENABLE);
        if ( HAL_SUCCESS != ret )
        {
            pi_hal_usbd_free(&handle);
            return ;
        }
    }
    else
    {
        pi_log_d("panel request to disable usb port\n");
        ret = pi_hal_usbd_ctrl(handle, HAL_USBD_DISABLE);
        if ( HAL_SUCCESS != ret )
        {
            pi_hal_usbd_free(&handle);
            return ;
        }
    }

    pi_hal_usbd_free(&handle);
}

int32_t panel_set_setting_to_nvram_u32( uint32_t* data, uint32_t data_len , uint16_t panel_dc_cmd)
{
    int32_t ret = -1;
    int i = 0;
    int32_t security_operate = 0 ;

    for( i = 0; i < sizeof(cmd_nvid_uint32_table)/sizeof(cmd_nvid_uint32_table[0]); i++ )
    {
        if( panel_dc_cmd == cmd_nvid_uint32_table[i].cmd_id )
        {
            *( (uint32_t* )cmd_nvid_uint32_table[i].panel_config_id ) = *( (uint32_t*) data );
            ret = pi_nvram_set( cmd_nvid_uint32_table[i].nvram_id, VTYPE_UINT, (void *)data, data_len, 1, NULL );
            pi_log_d( "panel set nv_id %d,data %d\n", cmd_nvid_uint32_table[i].nvram_id, *( (uint32_t* )cmd_nvid_uint32_table[i].panel_config_id ) );

            //record the operate when write to nv
            security_operate = get_panel_security_operate_type_by_nvid( cmd_nvid_uint32_table[i].nvram_id );
            if( security_operate >= 0 )
            {
                notify_security_operate( security_operate, data, data_len );
            }
            else
            {
                pi_log_d( "can not found security_operate\n");
            }

            break;
        }
    }
    if(  i == sizeof(cmd_nvid_uint32_table)/sizeof(cmd_nvid_uint32_table[0]) )
    {
        pi_log_d("can not found nv id\n");
    }
    return ret;
}

int32_t panel_set_setting_to_nvram_str( char* data, uint32_t data_len , uint16_t panel_dc_cmd)
{
    int32_t ret = -1;
    int i = 0;
    int32_t security_operate = 0 ;

    for( i = 0; i < sizeof(cmd_nvid_string_table)/sizeof(cmd_nvid_string_table[0]); i++ )
    {
        if( panel_dc_cmd == cmd_nvid_string_table[i].cmd_id )
        {
            if( data_len != cmd_nvid_string_table[i].data_len )
            {
                pi_log_e( "panel set nv_id %d data len ERROR\n", cmd_nvid_string_table[i].nvram_id );
                return -1;
            }
            //*( (uint32_t* )cmd_nvid_string_table[i].panel_config_id ) = *( (uint32_t*) data );
            ret = pi_nvram_set( cmd_nvid_string_table[i].nvram_id, VTYPE_STRING, (void *)data, data_len, 1, NULL );
            if( ret < 0 )
            {
                pi_log_e( "panel set nv_id %d ERROR\n", cmd_nvid_string_table[i].nvram_id);
                return ret;
            }
            pi_strncpy(cmd_nvid_string_table[i].data_str, data, data_len);
            pi_log_d( "panel set nv_id %d,data %s\n", cmd_nvid_string_table[i].nvram_id, cmd_nvid_string_table[i].data_str );

            //record the operate when write to nv
            security_operate = get_panel_security_operate_type_by_nvid( cmd_nvid_string_table[i].nvram_id );
            if( security_operate >= 0 )
            {
                notify_security_operate( security_operate, data, data_len );
            }
            else
            {
                pi_log_d( "can not found security_operate\n");
            }
            break;
        }
    }
    if(  i == sizeof(cmd_nvid_string_table)/sizeof(cmd_nvid_string_table[0]) )
    {
        pi_log_d("can not found nv id\n");
    }
    return ret;
}

void dc_get_nvram_to_panel_struct( void );


int32_t panel_set_setting_to_nvram_struct( char* data, uint32_t data_len , uint16_t panel_dc_cmd)
{
    int32_t ret = -1;
    int i = 0;
    int32_t security_operate = 0 ;

    for( i = 0; i < sizeof(cmd_nvid_struct_table)/sizeof(cmd_nvid_struct_table[0]); i++ )
    {
        if( panel_dc_cmd == cmd_nvid_struct_table[i].cmd_id )
        {
            if( data_len != cmd_nvid_struct_table[i].data_len )
            {
                pi_log_e( "panel set nv_id %d data len ERROR\n", cmd_nvid_struct_table[i].nvram_id );
                return -1;
            }
            ret = pi_nvram_set( cmd_nvid_struct_table[i].nvram_id, VTYPE_STRUCT, (void *)data, data_len, 1, NULL );
            if( ret < 0 )
            {
                pi_log_e( "panel set nv_id %d ERROR\n", cmd_nvid_struct_table[i].nvram_id);
                return ret;
            }
            pi_memcpy(cmd_nvid_struct_table[i].data_struct, data, data_len);

            //record the operate when write to nv
            security_operate = get_panel_security_operate_type_by_nvid( cmd_nvid_struct_table[i].nvram_id );
            if( security_operate >= 0 )
            {
                notify_security_operate( security_operate, NULL, 0 );   //结构体内容不记录，只记录修改操作
            }
            else
            {
                pi_log_d( "can not found security_operate\n");
            }

            break;
        }
    }
    if(  i == sizeof(cmd_nvid_struct_table)/sizeof(cmd_nvid_struct_table[0]) )
    {
        pi_log_d("can not found nv id\n");
    }
    return ret;
}



void dc_get_nvram_to_panel_u32( void )
{
    uint32_t u32_data = 0;

    for( int i = 0; i < sizeof(cmd_nvid_uint32_table)/sizeof(cmd_nvid_uint32_table[0]); i++ )
    {
        pi_nvram_get( cmd_nvid_uint32_table[i].nvram_id, VTYPE_UINT, &u32_data, sizeof(u32_data) );
        *( (uint32_t* )cmd_nvid_uint32_table[i].panel_config_id ) = u32_data;
        pi_log_d("panel get nvram_id:%d, cmd_id:%d, data:%d\n",cmd_nvid_uint32_table[i].nvram_id, cmd_nvid_uint32_table[i].cmd_id, *( (uint32_t* )cmd_nvid_uint32_table[i].panel_config_id ) );
        panel_send_data_u8( SETTING, cmd_nvid_uint32_table[i].cmd_id, NOTIFICATION_RESPONSE, &u32_data, sizeof(u32_data) );
    }

}

void dc_get_nvram_to_panel_str( void )
{

    for( int i = 0; i < sizeof(cmd_nvid_string_table)/sizeof(cmd_nvid_string_table[0]); i++ )
    {
        pi_nvram_get( cmd_nvid_string_table[i].nvram_id, VTYPE_STRING, cmd_nvid_string_table[i].data_str, cmd_nvid_string_table[i].data_len );
        pi_log_d("panel get nvram_id:%d, cmd_id:%d, data:%s\n",cmd_nvid_string_table[i].nvram_id, cmd_nvid_string_table[i].cmd_id, cmd_nvid_string_table[i].data_str );
        panel_send_data_u8( SETTING, cmd_nvid_string_table[i].cmd_id, NOTIFICATION_RESPONSE, cmd_nvid_string_table[i].data_str, cmd_nvid_string_table[i].data_len );
    }

}

void dc_get_nvram_to_panel_struct( void )
{

    for( int i = 0; i < sizeof(cmd_nvid_struct_table)/sizeof(cmd_nvid_struct_table[0]); i++ )
    {
        pi_nvram_get( cmd_nvid_struct_table[i].nvram_id, VTYPE_STRUCT, cmd_nvid_struct_table[i].data_struct, cmd_nvid_struct_table[i].data_len );
        panel_send_data_u8( SETTING, cmd_nvid_struct_table[i].cmd_id, NOTIFICATION_RESPONSE, cmd_nvid_struct_table[i].data_struct, cmd_nvid_struct_table[i].data_len );
    }

}



/**
 * @brief dc send uint8 data to panel
 * @param[in] uint8_t head_type:cmd type
 * @param[in] uint16_t head_id:cmd id
 * @param[in] uint8_t head_msg_tpye:cmd messege
 * @param[in] void *data : uint8 data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
uint32_t panel_send_data_u8( uint8_t head_type, uint16_t head_id, uint8_t head_msg_tpye, void* data, uint32_t data_len )
{
    unsigned char *payload = NULL ;
    if( NULL == data )
    {
        return FALSE;
    }
    payload = pi_malloc( data_len + sizeof(uint32_t) );
    if (payload)
    {
        /* load cmd head */
        pi_memset( payload , 0 , data_len + sizeof(uint32_t) );
        payload[0] = head_type;     /// cmd type
        *( unsigned short* )( payload + 1 ) = head_id;     ///cmd msg id
        payload[3] = head_msg_tpye;     ///msg type

        /* load data */
        pi_memcpy(payload + sizeof(uint32_t) , data, data_len);


        proxy_send( payload , data_len + sizeof(uint32_t) , NULL );    /// send data
        pi_log_d( "dc send cmd to panel,head_type:%d,head_id:%d,data_size:%d\n", head_type, head_id, data_len );
        pi_free( payload );
        payload = NULL;

        return TRUE;
    }
    else
    {
        pi_log_e("malloc payload memory fail!\n");
        payload = NULL;
        return FALSE;
    }

}

/**
 * @brief dc send uint8 data to panel
 * @param[in] uint8_t head_type:cmd type
 * @param[in] uint16_t head_id:cmd id
 * @param[in] uint8_t head_msg_tpye:cmd messege
 * @author: madechang
 */
uint32_t panel_send_cmd( uint8_t head_type, uint16_t head_id, uint8_t head_msg_tpye )
{
    unsigned char *payload = NULL ;
    payload = pi_malloc( sizeof(uint32_t) );
    if (payload)
    {
        /* load cmd head */
        payload[0] = head_type;     /// cmd type
        *( unsigned short* )( payload + 1 ) = head_id;     ///cmd msg id
        payload[3] = head_msg_tpye;     ///msg type

        proxy_send( payload , sizeof(uint32_t) , NULL );    /// send data
        pi_free( payload );
        payload = NULL;

        return TRUE;
    }
    else
    {
        pi_log_e("malloc payload memory fail!\n");
        payload = NULL;
        return FALSE;
    }

}

int32_t panel_acl_set_sleep_time(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    int32_t ret = PARSER_SUCCESS;
    uint8_t cmddata[4] = {0};

    uint32_t sleep_mode = 0;
    uint32_t sleep_time = 0;
    EVT_MGR_CLI_S* panel_event_client = NULL;

    panel_event_client = get_panel_event_client();
    ACL_RESPONSE_BASE_STRUCT_S response_cmd = {0};

    if(NULL != acl_cmd)
    {
        pi_memcpy(cmddata, acl_cmd->temp, sizeof(cmddata));

        sleep_mode = (uint32_t)( (cmddata[0]<<8) + cmddata[1] );
        sleep_time = (uint32_t)( (cmddata[2]<<8) + cmddata[3] );
        pi_log_d("ACL set sleep time:%d \n",sleep_time);

        //set sleep time
        if( sleep_time != 0 )
        {
            switch ( sleep_time )
            {
                case 1:
                    sleep_time = PANEL_SLEEP_1MINUTES;
                    break;

                case 5:
                    sleep_time = PANEL_SLEEP_5MINUTES;
                    break;

                case 15:
                    sleep_time = PANEL_SLEEP_15MINUTES;
                    break;

                case 30:
                    sleep_time = PANEL_SLEEP_30MINUTES;
                    break;

                case 60:
                    sleep_time = PANEL_SLEEP_60MINUTES;
                    break;

                case 480:
                    sleep_time = PANEL_SLEEP_8HOUR;
                    break;
                default:
                    pi_log_d("ACL set panel sleep time data error:%d\n",sleep_time);
                    //pi_nvram_get( UI_ID_PANEL_SLEEP_TIME, VTYPE_UINT, &sleep_time, sizeof(sleep_time) );
                    acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
                    ret = PARSER_ERROR;
                    break;
            }
            if( PARSER_SUCCESS != ret )
            {
                return ret;     //parasoft检查不可在switch中return
            }
            panel_set_setting_to_nvram_u32( &sleep_time, sizeof(sleep_time), SETTING_CMD_SET_SLEEP_TIME );
            pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY, &(s_panel_config_table.sleep_time), sizeof(s_panel_config_table.sleep_time));
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SLEEP_TIME, NOTIFICATION_RESPONSE, &(s_panel_config_table.sleep_time), sizeof(s_panel_config_table.sleep_time) );

        }
        if( sleep_mode != 0 )
        {
            ///set sleep mode
            switch ( sleep_mode )
            {
                case 1:
                    sleep_mode = 0;     ///first sleep
                    break;
                case 2:
                    sleep_mode = 1;     ///deep sleep
                    break;
                default:
                    pi_log_d("ACL set panel sleep mode data error:%d\n",sleep_mode);
                    // pi_nvram_get( UI_ID_PANEL_SLEEP_MODE, VTYPE_UINT, &sleep_mode, sizeof(sleep_mode) );
                     acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
                     ret = PARSER_ERROR;
                    break;
            }
            if( PARSER_SUCCESS != ret )
            {
                return ret;     //parasoft检查不可在switch中return
            }
            panel_set_setting_to_nvram_u32( &sleep_mode, sizeof(sleep_mode), SETTING_CMD_SET_SLEEP_MODE );
            pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SLEEP_MODE_MODIFY, &(s_panel_config_table.sleep_mode), sizeof(s_panel_config_table.sleep_mode));
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SLEEP_MODE, NOTIFICATION_RESPONSE, &(s_panel_config_table.sleep_mode), sizeof(s_panel_config_table.sleep_mode) );
        }

        response_cmd.prefix = acl_cmd->prefix;
        response_cmd.cmd_id = acl_cmd->cmd_id;
        response_cmd.cmd_status = cpu_to_be16( ACLCMD_STATUS_SUCCESS );
        //驱动不设置休眠类型
        response_cmd.data_len = cpu_to_be16( sleep_mode );      //bytes 6-7
        acl_response( pgqio, &response_cmd, NULL, 0 );
        ret = PARSER_SUCCESS;

    }
    else
    {
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        ret = PARSER_ERROR;
    }
    return ret;
}

int32_t panel_acl_get_sleep_time(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    int32_t ret = PARSER_SUCCESS;
    uint32_t sleep_time = 0;
    uint32_t sleep_mode = 0;
    ACL_RESPONSE_BASE_STRUCT_S* buffer = (ACL_RESPONSE_BASE_STRUCT_S*)acl_cmd;

    /* get sleep time from paltform */
    if(NULL != acl_cmd)
    {
        //(void)oid_get_uint32(OID_PLATFORM_SLEEP_TIME, 0, &sleep_time);
        pi_nvram_get( UI_ID_PANEL_SLEEP_TIME, VTYPE_UINT, &sleep_time, sizeof(sleep_time) );
        switch ( sleep_time )
        {
            case PANEL_SLEEP_1MINUTES:
                sleep_time = 1;
                break;

            case PANEL_SLEEP_5MINUTES:
                sleep_time = 5;
                break;

            case PANEL_SLEEP_15MINUTES:
                sleep_time = 15;
                break;

            case PANEL_SLEEP_30MINUTES:
                sleep_time = 30;
                break;

            case PANEL_SLEEP_60MINUTES:
                sleep_time = 60;
                break;

            case PANEL_SLEEP_8HOUR:
                sleep_time = 480;
                break;
            default:
                pi_log_e("panel get sleep time error:%d\n",sleep_time);
                acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
                ret = PARSER_ERROR;
                break;
        }
        if( PARSER_SUCCESS != ret )
        {
            return ret;     //parasoft检查不可在switch中return
        }

        pi_log_d( "sleep time is %d minutes\n", sleep_time);

        pi_nvram_get( UI_ID_PANEL_SLEEP_MODE, VTYPE_UINT, &sleep_mode, sizeof(sleep_mode) );

        //panel save sleep mode for 0,1,acl cmd set 1,2
        switch (sleep_mode)
        {
            case 0:
                sleep_mode = 1;
                break;
            case 1:
                sleep_mode = 2;
                break;
            default:
//                acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
                pi_log_e("panel get sleep mode error:%d\n",sleep_mode);
//                return PARSER_ERROR;
                break;
        }

        /*
         *--void acl_direct_response(PGQIO pgqio, ACL_CMD_BASE_STRUCT_S *aclCmd, int16_t status, uint8_t *responseData, uint32_t dataLen)
         *--Everyone will feel confuse about this: " uint32_t dataLen "  correspond to " sleep_time " ?
         *--I just want to say:This is an exception.
         *--The PC driver need compatible with the previous version,and I think change acl_direct_response() is not a good idear.
         *--And the same thing happend in AclGetQuiet().
         *--wangbo 2016-March-3
         */
        //acl_direct_response(pgqio, acl_cmd, (int16_t)(ACLCMD_STATUS_SUCCESS), NULL, sleep_time);

        // 睡眠/节能
        if( 1 == sleep_mode )
        {
            buffer->cmd_status = cpu_to_be16((uint16_t)sleep_time);             //bytes 4-5 first sleeptime
        }
        else if( 2 == sleep_mode )
        {
            buffer->data_len = cpu_to_be16((uint16_t)sleep_time);       //bytes 6-7 second sleeptime
        }
        acl_response( pgqio, buffer, NULL, 0);
        ret = PARSER_SUCCESS;
        /// acl_direct_response(pgqio, acl_cmd, (int16_t)(sleep_time/60), NULL, 0);
    }
    else
    {
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        ret = PARSER_ERROR;
    }

    return ret;
}

static int32_t panel_acl_system_param_operate( GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* acl_cmd, void* cmd_data )
{
    int32_t ret = PARSER_ERROR;
    EVT_MGR_CLI_S* panel_event_client = NULL;
    PANEL_PRINT_IMAGE_CONFIG_S* print_image_config = NULL;
    ACL_PARAMETER_OPERATE_S* operator = ( ACL_PARAMETER_OPERATE_S* )acl_cmd;
    uint16_t operate = 0;
    uint32_t save_toner = 0;
    uint32_t sleep_time = 0;
    uint32_t sleep_time_cnt = 0;
    uint32_t quiet_print = 0;
    uint32_t set_save_toner = 0;
    uint32_t set_sleep_time = 0;
    uint32_t set_quiet_print = 0;
    uint8_t rsp_buf[ 3 ] = { 0 };
    const uint32_t sleep_time_table[] =
    { PANEL_SLEEP_OFF, PANEL_SLEEP_1MINUTES, PANEL_SLEEP_5MINUTES, PANEL_SLEEP_15MINUTES,
      PANEL_SLEEP_30MINUTES, PANEL_SLEEP_60MINUTES, PANEL_SLEEP_8HOUR };
    uint32_t sleep_time_table_size = sizeof( sleep_time_table ) / sizeof( sleep_time_table[0] );

    if( !acl_cmd )
    {
        return ret;
    }

    operate = be16_to_cpu(operator->operator_type);

    switch( operate )
    {
    case ACL_OPERATOR_TYPE_GET_PARA:
        pi_nvram_get( UI_ID_PANEL_PRINT_SAVE_TONER_MODE, VTYPE_UINT, &save_toner, sizeof(save_toner) );
        pi_nvram_get( UI_ID_PANEL_SLEEP_TIME, VTYPE_UINT, &sleep_time, sizeof(sleep_time) );
        pi_nvram_get( UI_ID_PANEL_QUIET_MODE, VTYPE_UINT, &quiet_print, sizeof(quiet_print) );
        for( sleep_time_cnt = 0; sleep_time_cnt < sleep_time_table_size; ++sleep_time_cnt )
        {
            if( sleep_time_table[sleep_time_cnt] == sleep_time )
            {
                break;
            }
        }

        if( (0 == sleep_time_cnt) || (sleep_time_cnt >= sleep_time_table_size) )
        {
            pi_log_e( "panel get sleep time error:%d\n", sleep_time );
            acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0 );
        }
        else
        {
            rsp_buf[ 0 ] = ( uint8_t )( save_toner & 0xFF );
            rsp_buf[ 1 ] = ( uint8_t )( sleep_time_cnt & 0xFF );
            rsp_buf[ 2 ] = ( uint8_t )( quiet_print & 0xFF );
            rc4_acl_data_process( rsp_buf, sizeof(rsp_buf) );
            acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, rsp_buf, sizeof(rsp_buf) );
            ret = PARSER_SUCCESS;
        }
        break;

    case ACL_OPERATOR_TYPE_SET_PARA:
        if( parser_common_read(pgqio, rsp_buf, sizeof(rsp_buf), PARSER_USB_TIMEOUT) != sizeof(rsp_buf) )
        {
            pi_log_e( "panel get param data failed!\n" );
            acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0 );
        }
        else
        {
            rc4_acl_data_process( rsp_buf, sizeof(rsp_buf) );

            if( (rsp_buf[0] != 0) && (rsp_buf[0] != 1) )
            {
                pi_log_e( "panel get acl save toner mode failed, mode = %d\n", rsp_buf[0] );
                acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0 );
            }
            else if( (0 == rsp_buf[1]) || (rsp_buf[1] >= sleep_time_table_size) )
            {
                pi_log_e( "panel get acl sleep time failed, time_cnt = %d\n", rsp_buf[1] );
                acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0 );
            }
            else if( (rsp_buf[2] != 0) && (rsp_buf[2] != 1) )
            {
                pi_log_e( "panel get acl quiet print mode failed, mode = %d\n", rsp_buf[2] );
                acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0 );
            }
            else
            {
                pi_log_d( "panel recv acl set pram data[%d|%d|%d]\n", rsp_buf[0], rsp_buf[1], rsp_buf[2] );
                panel_event_client = get_panel_event_client();

                pi_nvram_get( UI_ID_PANEL_PRINT_SAVE_TONER_MODE, VTYPE_UINT, &save_toner, sizeof(save_toner) );
                pi_nvram_get( UI_ID_PANEL_SLEEP_TIME, VTYPE_UINT, &sleep_time, sizeof(sleep_time) );
                pi_nvram_get( UI_ID_PANEL_QUIET_MODE, VTYPE_UINT, &quiet_print, sizeof(quiet_print) );
                if( save_toner != rsp_buf[0] )
                {
                    save_toner = rsp_buf[ 0 ];
                    print_image_config = get_print_image_config();
                    panel_set_setting_to_nvram_u32( &save_toner, sizeof(save_toner),
                                        SETTING_CMD_PRINT_SET_SAVE_TONER_MODE );
                    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_ADVANCED_IMAGE_CONFIG_MODIFY,
                                        print_image_config, sizeof( PANEL_PRINT_IMAGE_CONFIG_S ) );
                    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SAVE_TONER_MODE_MODIFY,
                                        &save_toner, sizeof(save_toner) );
                    panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_SAVE_TONER_MODE, NOTIFICATION_RESPONSE,
                        &(s_panel_config_table.panel_print_image_config.toner_save),
                        sizeof(s_panel_config_table.panel_print_image_config.toner_save) );
                }
                if( sleep_time != sleep_time_table[rsp_buf[1]] )
                {
                    sleep_time = sleep_time_table[ rsp_buf[1] ];
                    panel_set_setting_to_nvram_u32( &sleep_time, sizeof(sleep_time), SETTING_CMD_SET_SLEEP_TIME );
                    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY,
                        &(s_panel_config_table.sleep_time), sizeof(s_panel_config_table.sleep_time));
                    panel_send_data_u8( SETTING, SETTING_CMD_SET_SLEEP_TIME, NOTIFICATION_RESPONSE,
                        &(s_panel_config_table.sleep_time), sizeof(s_panel_config_table.sleep_time) );
                }
                if( quiet_print != rsp_buf[2] )
                {
                    quiet_print = rsp_buf[ 2 ];
//                    panel_set_setting_to_nvram_u32( &quiet_print, sizeof(quiet_print), SETTING_CMD_SET_MUTE_MODE );
//                    panel_send_data_u8( SETTING, SETTING_CMD_SET_MUTE_MODE, NOTIFICATION_RESPONSE,
//                        &(s_panel_config_table.quiet), sizeof(s_panel_config_table.quiet) );
                }

                acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, NULL, 0 );
                ret = PARSER_SUCCESS;
            }
        }
        break;

    default:
        pi_log_e( "illegal operate type!\n" );
        acl_direct_response( pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0 );
        break;
    }

    return ret;
}

int32_t panel_set_sleep_time_oid(uint32_t data)
{
    int ret = 0;
    EVT_MGR_CLI_S* panel_event_client = NULL;

    panel_event_client = get_panel_event_client();
    ret = panel_set_setting_to_nvram_u32( &data, sizeof(data), SETTING_CMD_SET_SLEEP_TIME );
    if( ret == N_SUCCESS )
    {
        panel_send_data_u8( SETTING, SETTING_CMD_SET_SLEEP_TIME, NOTIFICATION_RESPONSE, &data, sizeof(data) );
        pi_event_mgr_notify( panel_event_client, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY, &data, sizeof(data) );
    }
    return ret;
}
int32_t panel_get_sleep_time_oid(uint32_t *data)
{
    int32_t ret = 0;
    if( NULL == data )
    {
        return -1;
    }
    ret = pi_nvram_get( UI_ID_PANEL_SLEEP_TIME, VTYPE_UINT,data, sizeof(uint32_t) );

    //工步oid按秒计算
    switch ( *data )
    {
        case PANEL_SLEEP_1MINUTES:
            *data = 60;
            break;

        case PANEL_SLEEP_5MINUTES:
            *data = 300;
            break;

        case PANEL_SLEEP_15MINUTES:
            *data = 900;
            break;

        case PANEL_SLEEP_30MINUTES:
            *data = 1800;
            break;

        case PANEL_SLEEP_60MINUTES:
            *data = 3600;
            break;

        case PANEL_SLEEP_8HOUR:
            *data = 28800;
            break;
        default:
            break;
    }

    return ret;
}



void panel_acl_cmd_register(  void)
{
    pi_log_d("panel register sleep time ACL cmd\n");
    acl_register_cmd(ACLCMD_SET_SLEEP_TIME,  panel_acl_set_sleep_time, (void *)0);
    acl_register_cmd(ACLCMD_GET_SLEEP_TIME,  panel_acl_get_sleep_time, (void *)0);
    acl_register_cmd(ACLCMD_SYSTEM_PARAMETER_OPERATE, panel_acl_system_param_operate, (void *)0);
}
void panel_acl_oid_register(  void)
{
    pi_log_d("panel register sleep time oid\n");
    acl_attribute_uint32_value_register("OID_PLATFORM_SLEEP_TIME",             NULL,                     panel_get_sleep_time_oid );
}

void panel_set_water_mark( CUSTOM_WATER_MARK_S* water_mark )
{
    s_panel_water_mark[ water_mark->index -1].index = water_mark->index;
    snprintf( s_panel_water_mark[ water_mark->index -1].mark, sizeof(water_mark->mark), "%s", water_mark->mark );
    pi_log_d("panel recv water mark:index[%d],%s\n", water_mark->index, (char*)water_mark->mark ); /// 4 bytes for id
}

void send_watermark_to_panel()
{

    for( int i = 0; i < WATER_MARK_NUM; i++ )
    {
        pi_log_d("panel send water mark:index[%d],%s\n", s_panel_water_mark[i].index, (char*)(s_panel_water_mark[i].mark) ); /// 4 bytes for id
        panel_send_data_u8( SETTING, SETTING_CMD_GET_WATER_MARK, NOTIFICATION_RESPONSE, &s_panel_water_mark[i], sizeof(s_panel_water_mark[i]) );
    }
}

//开机启动后同步数据给各模块,否则其他模块拿不到面板存在nv内的设置项
void panel_default_param( void )
{
    EVT_MGR_CLI_S* panel_event_client = NULL;

    panel_event_client = get_panel_event_client();

    //获取nv数据并发送给面板
    dc_get_nvram_to_panel_u32();
    dc_get_nvram_to_panel_str();
    dc_get_nvram_to_panel_struct();

    //初始化时间并发送给面板
    //panel_init_system_time();
    panel_get_system_time();

    s_panel_status_table.system_sleep_status = 1;   //PM_STATUS_READY

    //部分默认设置推送给其他模块，否则若面板不重新设置，其他模块拿不到
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY, &(s_panel_config_table.sleep_time), sizeof(s_panel_config_table.sleep_time));
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SLEEP_MODE_MODIFY, &(s_panel_config_table.sleep_mode), sizeof(s_panel_config_table.sleep_mode));
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_TIME_FORMAT_MODIFY, &(s_panel_config_table.time_format), sizeof(s_panel_config_table.time_format) );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_DATE_FORMAT_MODIFY, &(s_panel_config_table.date_format), sizeof(s_panel_config_table.date_format) );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_LCD_BACKLIGHT_MODIFY, &(s_panel_config_table.lcd_backlight_set), sizeof(s_panel_config_table.lcd_backlight_set) );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SYS_VOLUE_MODIFY, &(s_panel_config_table.system_volume), sizeof(s_panel_config_table.system_volume) );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SAVE_TONER_MODE_MODIFY, &(s_panel_config_table.panel_print_image_config.toner_save), sizeof(s_panel_config_table.panel_print_image_config.toner_save) );

    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_MODIFY, &(s_panel_config_table.job_error_process_mode), sizeof(s_panel_config_table.job_error_process_mode) );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_MODIFY, &(s_panel_config_table.job_error_delete_time), sizeof(s_panel_config_table.job_error_delete_time) );

    panel_set_usb_control( &(s_panel_config_table.usb_control), sizeof(s_panel_config_table.usb_control) );
    panel_send_data_u8( SETTING, SETTING_CMD_SET_USB_CONTROL, NOTIFICATION_RESPONSE, &(s_panel_config_table.usb_control), sizeof(s_panel_config_table.usb_control) );

    pi_log_d("get default tray:%d\n",s_panel_config_table.panel_print_default_config.default_tray);
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_PRINT_DEFAULT_CONFIG_MODIFY, &s_panel_config_table.panel_print_default_config.default_tray, sizeof( s_panel_config_table.panel_print_default_config.default_tray ) );

    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_ADVANCED_IMAGE_CONFIG_MODIFY, &s_panel_config_table.panel_print_image_config, sizeof( PANEL_PRINT_IMAGE_CONFIG_S ) );

    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_COLOR_COPY_ENABLE_MODIFY, &s_panel_config_table.color_copy_enable, sizeof( s_panel_config_table.color_copy_enable ) );
    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_MODIFY, &s_panel_config_table.color_copy_pwd, sizeof( s_panel_config_table.color_copy_pwd ) );

    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SAFETY_MANAGEMENT_PSW_MODIFY, &s_panel_config_table.lock_screen_password, sizeof(s_panel_config_table.lock_screen_password) );

    //notify the security operate that panel is ready
    notify_security_operate( SECURITY_OPERATE_PANEL_INIT_FALAG, NULL, 0 );

    panel_acl_cmd_register();
    panel_acl_oid_register();


    #ifdef CONFIG_COLOR
            s_panel_config_table.config_color_print = 1;        ///color mode

    #else
            s_panel_config_table.config_color_print = 0;        ///black mode
    #endif
    pi_log_d("config_color_print = %d\n",s_panel_config_table.config_color_print);
    panel_send_data_u8( SETTING, SETTING_CMD_GET_MACHINE_COLOR_MODE, NOTIFICATION_RESPONSE, &(s_panel_config_table.config_color_print), sizeof(s_panel_config_table.config_color_print) );

    pi_event_mgr_notify(panel_event_client, EVT_TYPE_PANEL_SLEEP_MODE_MODIFY, &s_panel_config_table.sleep_mode, sizeof( s_panel_config_table.sleep_mode ) );

    #if CONFIG_SDK_PEDK
    //获取pedk安装信息
    panel_update_pedk_install_info();
    #endif

    panel_get_overlay_copy_data();


}

//面板节能唤醒后同步数据至面板 (面板断开重连)
void panel_data_sync()
{
    uint8_t* status = NULL;
    uint32_t data_len = 0;

    pi_log_d("panel init sync\n");


    panel_get_system_time();
    panel_send_data_u8( SETTING, SETTING_CMD_DATA_SYNC, NOTIFICATION_RESPONSE, &s_panel_config_table, sizeof(PANEL_CONFIG_TABLE_S) );

    panel_send_cmd( OPERATE, OPERATE_CMD_DC_INIT_DONE_NOTIFY, NOTIFICATION_RESPONSE );

    //重新推送状态
    status_manager_get( STATUS_ID_MODULE_ALL, &status, &data_len);
    if( status )
    {
        panel_send_data_u8(STATUS, STATUS_CMD_STATUS_UPDATE, NOTIFICATION_RESPONSE, status, data_len);
        pi_free( status );
    }

    update_all_address_book();

    #if CONFIG_SDK_PEDK
    //获取pedk安装信息
    panel_update_pedk_install_info();
    #endif

    panel_get_overlay_copy_data();

    send_watermark_to_panel();

    panel_notify_ready();

}

/**
 * @}
 */


