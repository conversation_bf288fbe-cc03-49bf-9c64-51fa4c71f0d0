#ifndef _PEDK_ADDRESSBOOK_
#define _PEDK_ADDRESSBOOK_

#include <quickjs.h>
#include <stdbool.h>

//MAIL
#define PEDK_MAIL_NAME_LEN          16
#define PEDK_MAIL_ADDR_LEN          64
#define PEDK_MAIL_ADDR_NUM_MAX      60

//MAIL_GROUP
#define PEDK_MAIL_GROUP_NAME_LEN    16
#define PEDK_MAIL_GROUP_MAX         10

//FTP
#define PEDK_FTP_USER_NAME_LEN      32
#define PEDK_FTP_SERVER_ADDR_LEN    32
#define PEDK_FTP_SERVER_PATH_LEN    64
#define PEDK_FTP_LOGIN_NAME_LEN     64
#define PEDK_FTP_PASSWORD_LEN       32
#define PEDK_FTP_PARM_MAX           60

//SMB
#define PEDK_SMB_USER_NAME_LEN      33
#define PEDK_SMB_SERVER_ADDR_LEN    33
#define PEDK_SMB_SERVER_PATH_LEN    129
#define PEDK_SMB_LOGIN_NAME_LEN     129
#define PEDK_SMB_PASSWORD_LEN       33
#define PEDK_SMB_PARM_MAX           60

typedef struct
{
    int32_t         pedk_mail_index;
    char            pedk_mail_name[PEDK_MAIL_NAME_LEN];
    char            pedk_mail_addr[PEDK_MAIL_ADDR_LEN];
    int32_t         pedk_group_num[PEDK_MAIL_GROUP_MAX];
}
PEDK_MAIL_PARM;

typedef struct
{
    int32_t      pedk_group_index;                                           //群组号索引
    char         pedk_group_name[PEDK_MAIL_GROUP_NAME_LEN];                  //群组名
    int32_t      pedk_mail_index[PEDK_MAIL_ADDR_NUM_MAX];                    //群组成员索引
}
PEDK_MAIL_GROUP_MGR;

typedef struct
{
    int32_t     pedk_ftp_index;                                        //索引号
    char        pedk_ftp_name[PEDK_FTP_USER_NAME_LEN];                 //用户别名scan
    char        pedk_ftp_addr[PEDK_FTP_SERVER_ADDR_LEN];               //服务器IP
    char        pedk_ftp_subdirectory[PEDK_FTP_SERVER_PATH_LEN];        //ftp服务器路径
    int32_t     pedk_ftp_port;                                         //端口号
    bool        pedk_ftp_anonymous;                                    //是否匿名
    char        pedk_ftp_login_name[PEDK_FTP_LOGIN_NAME_LEN];          //登录名
    char        pedk_ftp_login_pwd[PEDK_FTP_PASSWORD_LEN];             //密码

}
PEDK_FTP_PARM;

typedef struct
{
    int32_t     pedk_smb_index;                                        //索引号
    char        pedk_smb_name[PEDK_SMB_USER_NAME_LEN];                 //用户别名scan
    char        pedk_smb_addr[PEDK_SMB_SERVER_ADDR_LEN];               //服务器IP
    char        pedk_smb_subdirectory[PEDK_SMB_SERVER_PATH_LEN];       //子目录
    int32_t     pedk_smb_port;                                         //端口号
    bool        pedk_smb_anonymous;                                    //是否匿名
    char        pedk_smb_login_name[PEDK_SMB_LOGIN_NAME_LEN];          //登录名
    char        pedk_smb_login_pwd[PEDK_SMB_PASSWORD_LEN];             //密码
}
PEDK_SMB_PARM;



//email
JSValue js_add_email_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_email_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_email_addr_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_email_addr_num(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_is_email_addr_full(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_modify_email_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_remove_email_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

//group
JSValue js_add_email_to_group(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_creat_email_group(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_email_group(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_email_group_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_email_group_num(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_is_email_group_full(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_modify_email_group(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_remove_email_from_group(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_remove_email_group(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);



//ftp
JSValue js_add_ftp_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_ftp_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_ftp_addr_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_ftp_addr_num(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_is_ftp_addr_full(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_modify_ftp_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_remove_ftp_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

//smb
JSValue js_add_smb_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smb_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smb_addr_list(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smb_addr_num(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_is_smb_addr_full(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_modify_smb_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_remove_smb_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);











#endif /* _PEDK_ADDRESSBOOK_ */
