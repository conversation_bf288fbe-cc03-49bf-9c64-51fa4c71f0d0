/**
* @copyright 2024 Shenzhen Pantum Technology Co.Ltd all rights reserved
* @file job_pedkapi.h
* @addtogroup pedk jobctl
* @{
* @addtogroup pedk jobctl
* <AUTHOR>
* @date 2024-06-03
* @version v0.1
* @brief pedk jobctl
*
*/

#ifndef JOB_CTL_PEDK_H
#define JOB_CTL_PEDK_H

/**
 * @brief pedk job control init 
 * @param void
 *
 * @return non return value
 */
int32_t pedk_jobctl_init(void);

/**
 * @brief pedk job control deinit 
 * @param void
 *
 * @return non return value
 */
void pedk_jobctl_deinit(void);

/**
 * @brief update job info to pedk
 * @param job_id
 * @param flag
 *
 * @return non return value
 */
void pedk_jobinfo_update(void *arg);

#endif

/**
*@}
*/
