/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ftpclient.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief File-Transfer-Protocol client
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "netmisc.h"
#include "pedk_http.h"

#include <curl/curl.h>

#include "public_data_proc.h"
#include "pedk_mgr.h"

#define HEADERS_FILE_PDF        "Content-Type: application/pdf"

#define MAX_FILE_SIZE 3 * 10 * 1024 // 30k
#define SCAN_HTTP_SESSION_FILE "/pesf_data/scan_http_session.txt"
#define COPY_HTTP_SESSION_FILE "/pesf_data/copy_http_session.txt"

// 定义 JOB_NET_RECEIPT_* 常量
#define JOB_NET_RECEIPT_SUCCESS "JOB_NET_RECEIPT_SUCCESS"
#define JOB_NET_RECEIPT_ETRAN "JOB_NET_RECEIPT_ETRAN"
#define JOB_NET_RECEIPT_CANCEL "JOB_NET_RECEIPT_CANCEL"
#define JOB_NET_RECEIPT_TIMEOUT "JOB_NET_RECEIPT_TIMEOUT"
#define JOB_NET_RECEIPT_NAME_PASSWORD_ERROR "JOB_NET_RECEIPT_NAME_PASSWORD_ERROR"
#define JOB_NET_RECEIPT_UNKNOWN "JOB_NET_RECEIPT_UNKNOWN"

typedef enum
{
    WEBSCAN_INVALID = -1,       ///< 错误状态
    WEBSCAN_SUCCESS = 0,
}
WEBSCAN_STATUS_E;

typedef enum
{
    WEBSCAN_SCAN_JOB = 0,
    WEBSCAN_SCAN_BACKUP,
    WEBSCAN_COPY_BACKUP,
}
WEBSCAN_FLOW_E;

typedef struct webscan_client
{
    CURL*               curl;
    struct curl_slist*  headers;
}
WEBSCAN_CLI_S;

typedef struct webscan_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         cli_tid;
    uint32_t            backup_flag;
    WEBSCAN_CLI_S*      scan_obj;
    WEBSCAN_CLI_S*      scan_backup_obj;
    WEBSCAN_CLI_S*      copy_backup_obj;
    uint32_t            job_id;
    char custom_field[1024];
}
WEBSCAN_CTX_S;

typedef struct {
    char job_type[32];            // 作业类型，例如 "scan" 或 "copy"
    char document_name[256];      // 文档名称
    char submission_time[64];     // 提交时间，本机时间
    char data_receipt_status[128];// 服务器接收结果，例如 HTTP 状态码或错误信息
} JobNetInfo;

static WEBSCAN_CTX_S* s_webscan_ctx = NULL;



// 1. 首先定义一个联合体来处理不同的参数
typedef union {
    HTTP_PARM_S scan_param;
    COPY_RETENTION_PARAM_S copy_param;
} WEBSCAN_PARAM_U;

// 2. 创建一个结构体来存储通用的请求参数
typedef struct {
    const char* url;
    const char* headers;
    WEBSCAN_FLOW_E flow_type;
    uint32_t job_id;
} WEBSCAN_REQUEST_PARAM_S;

// 函数前向声明
static WEBSCAN_STATUS_E webscan_client_start(const WEBSCAN_PARAM_U* param,
                                           uint32_t job_id,
                                           WEBSCAN_FLOW_E flow_type);

static WEBSCAN_STATUS_E webscan_client_start_scan(HTTP_PARM_S* webscan_data,
                                                uint32_t job_id,
                                                WEBSCAN_FLOW_E flow_type);

static WEBSCAN_STATUS_E webscan_client_start_copy(COPY_RETENTION_PARAM_S* copy_retention_data,
                                                uint32_t job_id,
                                                WEBSCAN_FLOW_E flow_type);
static void webscan_add_headers_to_curl(const char *headers, struct curl_slist **curl_headers);

// 3. HTTPS 配置函数
static void webscan_setup_https_config(CURL* curl)
{
    RETURN_IF(curl == NULL, NET_WARN);

    // 获取 HTTPS 配置
    HTTPS_CONFIG_PARAM_S https_config;
    memset(&https_config, 0, sizeof(https_config));
    pedk_http_get_https_config(&https_config);

    NET_INFO("HTTPS Config: verify_cert=%d, verify_host=%d, cert_path=%s, key_path=%s",
             https_config.verify_certificate,
             https_config.verify_host_mode,
             https_config.client_cert_path,
             https_config.client_key_path);

    // 1. 设置基本的 SSL 验证选项
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, (long)https_config.verify_certificate);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, (long)https_config.verify_host_mode);

    // 2. 处理证书验证
    if (https_config.verify_certificate && strlen(https_config.client_cert_path) > 0)
    {
        // 双向认证：设置客户端证书和私钥
        NET_INFO("Setting up client certificate authentication");
        curl_easy_setopt(curl, CURLOPT_SSLCERT, https_config.client_cert_path);
        curl_easy_setopt(curl, CURLOPT_SSLKEY, https_config.client_key_path);

        if (strlen(https_config.key_password) > 0)
        {
            curl_easy_setopt(curl, CURLOPT_SSLCERTPASSWD, https_config.key_password);
        }
    }
    else
    {
        // 单向认证或禁用证书验证
        NET_INFO("not verify certificate\n");
        curl_easy_setopt(curl, CURLOPT_CAINFO, https_config.client_cert_path);
        //curl_easy_setopt(curl, CURLOPT_CAPATH, https_config.client_cert_path);
    }
}

// 4. 通用的 CURL 初始化函数
static WEBSCAN_CLI_S* webscan_init_curl_client(const WEBSCAN_REQUEST_PARAM_S* request_param)
{
    WEBSCAN_CLI_S* pcli = (WEBSCAN_CLI_S*)pi_zalloc(sizeof(WEBSCAN_CLI_S));
    RETURN_VAL_IF(request_param->url[0] == '\0', NET_WARN, NULL);

    curl_global_init(CURL_GLOBAL_DEFAULT);
    pcli->curl = curl_easy_init();
    if (pcli->curl == NULL)
    {
        pi_free(pcli);
        return NULL;
    }

    // 创建并设置 headers
    webscan_add_headers_to_curl(request_param->headers, &pcli->headers);

    // 基本 CURL 设置
    curl_easy_setopt(pcli->curl, CURLOPT_URL, request_param->url);
    curl_easy_setopt(pcli->curl, CURLOPT_POST, 1L);
    curl_easy_setopt(pcli->curl, CURLOPT_HTTPHEADER, pcli->headers);

    // 设置 HTTPS 配置
    webscan_setup_https_config(pcli->curl);

    return pcli;
}

// 5. 重写的统一客户端启动函数
static WEBSCAN_STATUS_E webscan_client_start(const WEBSCAN_PARAM_U* param,
                                           uint32_t job_id,
                                           WEBSCAN_FLOW_E flow_type)
{
    WEBSCAN_REQUEST_PARAM_S request_param = {0};

    // 根据不同的 flow_type 设置参数
    switch (flow_type)
    {
        case WEBSCAN_SCAN_JOB:
        {
            SCAN_HTTP_PARAM_S scan_http_param;
            int ret = pedk_http_get_url_header_params(&scan_http_param);
            RETURN_VAL_IF(ret, NET_WARN, WEBSCAN_INVALID);

            request_param.url = scan_http_param.url;
            request_param.headers = scan_http_param.headers;  // 现在是字符串形式的 headers
            if (scan_http_param.custom_field[0] != '\0') {
                memcpy(s_webscan_ctx->custom_field, scan_http_param.custom_field,
                       sizeof(scan_http_param.custom_field));
            }
            s_webscan_ctx->backup_flag = 0;
            break;
        }
        case WEBSCAN_SCAN_BACKUP:
            request_param.url = param->scan_param.scanToHttpPath;
            request_param.headers = param->scan_param.scanToHttpheaders;
            s_webscan_ctx->backup_flag = 1;
            break;
        case WEBSCAN_COPY_BACKUP:
            request_param.url = param->copy_param.url;
            request_param.headers = param->copy_param.headers;
            memcpy(s_webscan_ctx->custom_field, param->copy_param.custom_field,
                   sizeof(param->copy_param.custom_field));
            s_webscan_ctx->backup_flag = 2;
            break;
        default:
            return WEBSCAN_INVALID;
    }

    request_param.flow_type = flow_type;
    request_param.job_id = job_id;

    // 初始化 CURL 客户端
    WEBSCAN_CLI_S* pcli = webscan_init_curl_client(&request_param);
    if (pcli == NULL)
    {
        return WEBSCAN_INVALID;
    }

    // 根据 flow_type 保存客户端对象
    switch (flow_type)
    {
        case WEBSCAN_SCAN_JOB:
            s_webscan_ctx->scan_obj = pcli;
            break;
        case WEBSCAN_SCAN_BACKUP:
            s_webscan_ctx->scan_backup_obj = pcli;
            break;
        case WEBSCAN_COPY_BACKUP:
            s_webscan_ctx->copy_backup_obj = pcli;
            break;
    }

    s_webscan_ctx->job_id = job_id;
    return WEBSCAN_SUCCESS;
}

// 映射函数：根据 CURLcode 和 HTTP 响应码设置 data_receipt_status
const char* webscan_map_status_to_receipt(CURLcode res, long http_code)
{
    if (res != CURLE_OK)
    {
        switch (res)
        {
            case CURLE_COULDNT_CONNECT:
                return JOB_NET_RECEIPT_TIMEOUT;
            case CURLE_REMOTE_ACCESS_DENIED:
                return JOB_NET_RECEIPT_NAME_PASSWORD_ERROR;
            default:
                return JOB_NET_RECEIPT_ETRAN;
        }
    }

    // 根据 HTTP 响应码进一步判断
    if (http_code >= 200 && http_code < 300)
    {
        return JOB_NET_RECEIPT_SUCCESS;
    }
    else if (http_code >= 400 && http_code < 500)
    {
        return JOB_NET_RECEIPT_NAME_PASSWORD_ERROR;
    }
    else
    {
        return JOB_NET_RECEIPT_UNKNOWN;
    }
}

// 将记录写入文件（JSON 格式），并管理文件大小
static void webscan_write_job_info_to_file(const char *file_path, const JobNetInfo *job_info)
{
    FILE *file = fopen(file_path, "r+");
    if (!file)
    {
        file = fopen(file_path, "w");
        if (!file)
        {
            printf("Failed to open or create file: %s\n", file_path);
            return;
        }
    }

    // 读取文件内容到内存
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    char *buffer = NULL;
    if (file_size > 0) {
        buffer = (char *)malloc(file_size + 1);
        if (!buffer)
        {
            printf("Failed to allocate memory for file buffer.\n");
            fclose(file);
            return;
        }
        fread(buffer, 1, file_size, file);
        buffer[file_size] = '\0';
    }

    // 创建新记录的 JSON 格式
    cJSON *json_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(json_obj, "job_type", job_info->job_type);
    cJSON_AddStringToObject(json_obj, "document_name", job_info->document_name);
    cJSON_AddStringToObject(json_obj, "submission_time", job_info->submission_time);
    cJSON_AddStringToObject(json_obj, "data_receipt_status", job_info->data_receipt_status);
    char *new_record = cJSON_PrintUnformatted(json_obj);
    cJSON_Delete(json_obj);

    // 如果文件超过 30k，则删除最后一条记录
    if (file_size + strlen(new_record) + 1 > MAX_FILE_SIZE && buffer)
    {
        // 找到最后一条记录的起始位置
        char *last_line_start = buffer;
        char *next_line = NULL;
        while ((next_line = strchr(last_line_start, '\n')) != NULL)
        {
            if (*(next_line + 1) == '\0' || next_line == buffer + file_size - 1)
            {
                *last_line_start = '\0'; // 删除最后一条记录
                break;
            }
            last_line_start = next_line + 1;
        }
    }

    // 打开文件用于写入
    file = freopen(file_path, "w", file);
    if (!file) 
    {
        printf("Failed to reopen file: %s\n", file_path);
        free(new_record);
        if (buffer) 
        {
            free(buffer);
        }
        return;
    }

    // 写入最新记录到文件开头
    fprintf(file, "%s\n", new_record);

    // 如果存在旧记录，则继续写入旧记录
    if (buffer)
    {
        fprintf(file, "%s", buffer);
        free(buffer);
    }

    free(new_record);
    fclose(file);
}

// 解析 headers 并添加到 cURL 请求中
static void webscan_add_headers_to_curl(const char *headers, struct curl_slist **curl_headers)
{
    if (headers == NULL || strlen(headers) == 0 || curl_headers == NULL)
    {
        NET_WARN("Headers is empty or curl_headers is NULL.\n");
        return;
    }

    // 解析 JSON
    cJSON *json = cJSON_Parse(headers);
    if (json == NULL) {
        NET_WARN("Failed to parse headers as JSON.\n");
        return;
    }

    // 遍历 JSON 数据并添加到 cURL 自定义 header 列表
    cJSON *item = NULL;
    cJSON_ArrayForEach(item, json)
    {
        if (cJSON_IsString(item))
        {
            char header[1024] = {0};
            snprintf(header, sizeof(header), "%s: %s", item->string, item->valuestring);

            // 保存 curl_slist_append 的返回值
            struct curl_slist *new_headers = curl_slist_append(*curl_headers, header);
            if (new_headers != NULL)
            {
                *curl_headers = new_headers;  // 更新头指针
                NET_INFO("Added header: %s\n", header);
            }
            else
            {
                NET_WARN("Failed to append header: %s\n", header);
            }
        }
    }

    // 释放 JSON 对象
    cJSON_Delete(json);
}

static WEBSCAN_STATUS_E webscan_client_put_file(char * file, WEBSCAN_FLOW_E flow_type)
{
    WEBSCAN_STATUS_E    status = WEBSCAN_SUCCESS;
    FILE*               stream = NULL;
    long                stream_size = 0;
    CURLcode            res;
    WEBSCAN_CLI_S*      pcli = NULL;
    char                file_name[256];

    curl_mime *form = NULL;
    curl_mimepart *field = NULL;

    // 初始化作业信息结构体
    JobNetInfo job_info;
    memset(&job_info, 0, sizeof(job_info));
    strncpy(job_info.document_name, file, sizeof(job_info.document_name) - 1);
    NET_INFO("document_name %s\n", job_info.document_name);

    char *last_slash = strrchr(job_info.document_name, '/');
    if (last_slash)
    {
        // 将文件名移动到路径分隔符后的部分
        memmove(job_info.document_name, last_slash + 1, strlen(last_slash + 1) + 1);
    }
    NET_INFO("Trimmed document_name %s\n", job_info.document_name);

    const char *file_path = NULL;
    if ( flow_type == WEBSCAN_SCAN_JOB )
    {
        strncpy(job_info.job_type, "SCAN_TO_HTTP", sizeof(job_info.job_type) - 1);
        file_path = SCAN_HTTP_SESSION_FILE;
        pcli = s_webscan_ctx->scan_obj;
    }
    else if ( flow_type == WEBSCAN_SCAN_BACKUP )
    {
        strncpy(job_info.job_type, "SCAN_TO_HTTP", sizeof(job_info.job_type) - 1);
        file_path = SCAN_HTTP_SESSION_FILE;
        pcli = s_webscan_ctx->scan_backup_obj;
    }
    else if ( flow_type == WEBSCAN_COPY_BACKUP )
    {
        strncpy(job_info.job_type, "COPY_NORMAL", sizeof(job_info.job_type) - 1);
        file_path = COPY_HTTP_SESSION_FILE;
        pcli = s_webscan_ctx->copy_backup_obj;
    }
    else
    {
        return WEBSCAN_INVALID;
    }

    if ( pcli == NULL || pcli->curl == NULL )
    {
        return WEBSCAN_INVALID;
    }

    NET_INFO("post s_webscan_ctx->custom_field %s, len %zu\n", s_webscan_ctx->custom_field, strlen(s_webscan_ctx->custom_field));

    form = curl_mime_init(pcli->curl);

    field = curl_mime_addpart(form);
    curl_mime_name(field, "file");
    curl_mime_filedata(field, file);

    field = curl_mime_addpart(form);
    curl_mime_name(field, "file_name");
    curl_mime_data(field, file, CURL_ZERO_TERMINATED);

    if(strlen(s_webscan_ctx->custom_field) > 0)
    {
        field = curl_mime_addpart(form);
        curl_mime_name(field, "custom_field");
        curl_mime_data(field, s_webscan_ctx->custom_field, CURL_ZERO_TERMINATED);
        NET_INFO("Adding custom_field to form data \n");
    }

    curl_easy_setopt(pcli->curl, CURLOPT_MIMEPOST, form);

    // 获取当前时间作为提交时间
    time_t now = time(NULL);
    strftime(job_info.submission_time, sizeof(job_info.submission_time), "%Y-%m-%d %H:%M:%S", localtime(&now));

    // 执行请求并获取服务器回复
    res = curl_easy_perform(pcli->curl);
    if (res != CURLE_OK)
    {
        snprintf(job_info.data_receipt_status, sizeof(job_info.data_receipt_status), "JOB_NET_RECEIPT_TIMEOUT");
        status = WEBSCAN_INVALID;
    }
    else
    {
        long http_code = 0;
        curl_easy_getinfo(pcli->curl, CURLINFO_RESPONSE_CODE, &http_code);
        //snprintf(job_info.data_receipt_status, sizeof(job_info.data_receipt_status), "HTTP Code: %ld", http_code);

        // 映射状态到规格化字符串
        strncpy(job_info.data_receipt_status,
                webscan_map_status_to_receipt(res, http_code),
                sizeof(job_info.data_receipt_status) - 1);
        if (res != CURLE_OK)
        {
            NET_DEBUG("Error: %s\n", curl_easy_strerror(res));
            status = WEBSCAN_INVALID;
        }
        else
        {
            NET_DEBUG("HTTP Code: %ld\n", http_code);
        }

    }

    if ( form != NULL )
    {
        curl_mime_free(form);
    }

    // 调试输出
    NET_DEBUG("Job Info - Type: %s, Document: %s, Time: %s, Status: %s\n",
              job_info.job_type, job_info.document_name, job_info.submission_time, job_info.data_receipt_status);

    // 写入文件
    webscan_write_job_info_to_file(file_path, &job_info);

    uint32_t job_id = s_webscan_ctx->job_id;
    // 通过 pedk_mgr_send_msg_to_runenv 发送信息
    char net_upload_info[256];
    memset(net_upload_info, 0, sizeof(net_upload_info));
    snprintf(net_upload_info, sizeof(net_upload_info), "{\"job_id\":\"%u\"}",s_webscan_ctx->job_id);
    pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, MSG_JOBCTL_SUB_NET_UPLOAD_INFO, 0, (unsigned char*)net_upload_info, strlen(net_upload_info));

    NET_INFO("send net_upload_info %s\n", net_upload_info);

    return status;
}

static void webscan_client_release_job(WEBSCAN_FLOW_E flow_type)
{
    WEBSCAN_CLI_S*      pcli = NULL;
    if ( flow_type == WEBSCAN_SCAN_JOB )
    {
        pcli = s_webscan_ctx->scan_obj;
    }
    else if ( flow_type == WEBSCAN_SCAN_BACKUP )
    {
        pcli = s_webscan_ctx->scan_backup_obj;
    }
    else if ( flow_type == WEBSCAN_COPY_BACKUP )
    {
        pcli = s_webscan_ctx->copy_backup_obj;
    }
    else
    {
        return ;
    }
    RETURN_IF(pcli == NULL, NET_WARN);

    if ( pcli->curl != NULL )
    {
        curl_easy_cleanup(pcli->curl);
        pcli->curl = NULL;
    }
    if ( pcli != NULL )
    {
        pi_free(pcli);
    }
    if ( flow_type == WEBSCAN_SCAN_JOB )
    {
        s_webscan_ctx->scan_obj = NULL;
    }
    else if ( flow_type == WEBSCAN_SCAN_BACKUP )
    {
        s_webscan_ctx->scan_backup_obj = NULL;
    }
    else if ( flow_type == WEBSCAN_COPY_BACKUP )
    {
        s_webscan_ctx->copy_backup_obj = NULL;
    }
}

static void* webscan_client_thread(void* arg)
{
    ROUTER_MSG_S        message;
    WEBSCAN_STATUS_E    status = WEBSCAN_SUCCESS;
    int32_t             scanjobend = 0;
    char*               file = NULL;

    while ( 1 )
    {
        NET_INFO("WEBSCAN client start");
        message.msgType = MSG_NULL;
        file = NULL;
        if ( task_msg_wait_forever_by_router(MID_PORT_WEBSCAN, &message) < 0 )
        {
            NET_WARN("wait MID_PORT_WEBSCAN failed");
            break;
        }
        switch( message.msgType )
        {
            case MSG_CTRL_JOB_START:
            {
                status = webscan_client_start_scan((HTTP_PARM_S *)message.msg3,
                                                 message.msg2,
                                                 WEBSCAN_SCAN_JOB);
                scanjobend = 0;
                break;
            }
            case MSG_DATA_PAGE_END:
            {
                file = (char *)message.msg3;
                if ( file == NULL )
                {
                    status = WEBSCAN_INVALID;
                    break;
                }
                NET_DEBUG("message.msgType %ud\n", message.msgType);
                status = webscan_client_put_file(file, WEBSCAN_SCAN_JOB);
                break;
            }
            case MSG_DATA_JOB_END:
            {
                if(message.msg2 == FILE_PDF)
                {
                    file = (char *)message.msg3;
                    if ( file == NULL )
                    {
                        status = WEBSCAN_INVALID;
                        break;
                    }
                    NET_DEBUG("put_file %s\n", file);
                    status = webscan_client_put_file(file, WEBSCAN_SCAN_JOB);
                }
                webscan_client_release_job(WEBSCAN_SCAN_JOB);
                scanjobend = (status != WEBSCAN_SUCCESS) ? 0 : -1;
                break;
            }
            case MSG_CTRL_JOB_CANCEL:
            {
                webscan_client_release_job(WEBSCAN_SCAN_JOB);
                status = WEBSCAN_INVALID;
                scanjobend = (status != WEBSCAN_SUCCESS) ? 0 : -2;
                break;
            }
            case MSG_COPY_BACKUP_START:
            {
                NET_INFO("WEBSCAN client copy back start");
                status = webscan_client_start_copy((COPY_RETENTION_PARAM_S *)message.msg3,
                                                 message.msg2,
                                                 WEBSCAN_COPY_BACKUP);
                break;
            }
            case MSG_COPY_BACKUP_TRANS:
            {
                NET_INFO("WEBSCAN client copy back trans");
                file = (char *)message.msg3;
                if ( file == NULL )
                {
                    status = WEBSCAN_INVALID;
                    break;
                }
                status = webscan_client_put_file(file, WEBSCAN_COPY_BACKUP);
                break;
            }
            case MSG_COPY_BACKUP_END:
            {
                webscan_client_release_job(WEBSCAN_COPY_BACKUP);
                break;
            }
            default:
            {
                NET_WARN("msgtype(%u) invalid", message.msgType);
                break;
            }
        }
        if ( s_webscan_ctx->backup_flag == 0 )
        {
            if ( (scanjobend == 0 && status != WEBSCAN_SUCCESS) || (scanjobend < 0 && status == WEBSCAN_SUCCESS) )
            {
                NET_WARN(" scan error status(%d) endstatus(%d)", status, scanjobend);
                message.msgType   = MSG_SCAN_STATUS_UPDATE;
                message.msg1      = LOCAL_REQUEST;
                message.msg2      = status;
                message.msg3      = NULL;
                message.msgSender = MID_PORT_WEBSCAN;
                task_msg_send_by_router(MID_SCAN_OUT, &message);
                scanjobend = 1;
            }
        }
        else if ( s_webscan_ctx->backup_flag == 2 && file != NULL)
        {
            /*复印留底暂时不上报状态
            message.msgType   = MSG_COPY_STATUS_UPDATE;
            message.msg1      = LOCAL_REQUEST;
            message.msg2      = status;
            message.msg3      = 0;
            message.msgSender = MID_PORT_WEBSCAN;
            task_msg_send_by_router(MID_COPY_MGR, &message);
                */
        }
    }
    NET_WARN("WEBSCAN Scan Thread Exit!!!");

    return NULL;
}

void webscan_epilog(void)
{
    if ( s_webscan_ctx != NULL )
    {
        if ( s_webscan_ctx->cli_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_webscan_ctx->cli_tid);
        }
        msg_router_unregister(MID_PORT_WEBSCAN);
        pi_free(s_webscan_ctx);
        s_webscan_ctx = NULL;
    }
}

int32_t webscan_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_webscan_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_webscan_ctx = (WEBSCAN_CTX_S *)pi_zalloc(sizeof(WEBSCAN_CTX_S));
    RETURN_VAL_IF(s_webscan_ctx == NULL, NET_WARN, -1);

    do
    {
        s_webscan_ctx->net_ctx = net_ctx;
        BREAK_IF(msg_router_register(MID_PORT_WEBSCAN) < 0, NET_WARN);
        s_webscan_ctx->cli_tid = pi_thread_create(webscan_client_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "webscan_client_thread");
        BREAK_IF(s_webscan_ctx->cli_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WEBSCAN client initialize result(%d)", ret);
    if ( ret != 0 )
    {
        webscan_epilog();
    }
    return ret;
}

static WEBSCAN_STATUS_E webscan_client_start_scan(HTTP_PARM_S* webscan_data,
                                                uint32_t job_id,
                                                WEBSCAN_FLOW_E flow_type)
{
    WEBSCAN_PARAM_U param;
    memcpy(&param.scan_param, webscan_data, sizeof(HTTP_PARM_S));
    return webscan_client_start(&param, job_id, flow_type);
}

static WEBSCAN_STATUS_E webscan_client_start_copy(COPY_RETENTION_PARAM_S* copy_retention_data,
                                                uint32_t job_id,
                                                WEBSCAN_FLOW_E flow_type)
{
    WEBSCAN_PARAM_U param;
    memcpy(&param.copy_param, copy_retention_data, sizeof(COPY_RETENTION_PARAM_S));
    return webscan_client_start(&param, job_id, flow_type);
}

/**
 *@}
 */
