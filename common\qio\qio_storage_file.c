/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_storage_file.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief QIO object wrapper for file I/O
 */
#include "qiox.h"
#include "hal.h"

typedef struct priv_info_storage
{
    char    name[256];
    void*   handle;
}
PRIV_INFO_S;

static int32_t qio_storage_file_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    int32_t ret = 1;
    int32_t mode;
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->handle == NULL, QIO_WARN, QIOEOF);

    if( (what & QIO_POLL_READ) == QIO_POLL_READ )
    {
        mode = R_OK;
    }
    else if( (what & QIO_POLL_WRITE) == QIO_POLL_WRITE )
    {
        mode = W_OK;
    }
    else
    {
        return -1;
    }

    if( pi_hal_storage_file_dir_access(priv->name, mode) != 0 )
    {
        QIO_WARN("access file(%s) error\n", priv->name);
        //ret = -1;
    }

    return ret;
}

static int32_t qio_storage_file_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->handle == NULL, QIO_WARN, QIOEOF);

    return pi_hal_storage_fileread(priv->handle, buffer, (uint32_t)nbuf);
}

static int32_t qio_storage_file_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->handle == NULL, QIO_WARN, QIOEOF);

    return pi_hal_storage_filewrite(priv->handle, buffer, (uint32_t)nbuf);
}

static int32_t qio_storage_file_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    int32_t ret;
    off_t loc;
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->handle == NULL, QIO_WARN, QIOEOF);

    ret = pi_hal_storage_filelseek(priv->handle, offset, whence, &loc);
    if( 0 != ret )
    {
        QIO_WARN("seek file errorNo:%d\n", ret);
        return ret;
    }
    else
    {
        return loc;
    }
}

static int32_t qio_storage_file_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( priv )
    {
        QIO_DEBUG("close STORAGE_FILE(%s) QIO<%p> priv<%p> handle<%p>", priv->name, pqio, priv, priv->handle);
        if ( priv->handle != NULL )
        {
            pi_hal_storage_filerelease(priv->handle);
        }
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_storage_file_create(const char* stream, int32_t flags)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;
    int32_t         ret;

    RETURN_VAL_IF(stream == NULL, QIO_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    ret = pi_hal_storage_filerequest(&priv->handle, stream, (int)( flags | O_BINARY), 0666);
    if ( (priv->handle == NULL) ||(ret != 0))
    {
        QIO_WARN("open (%s) failed: %d<%s>", stream, errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }
    snprintf(priv->name, sizeof(priv->name), "%s", stream);

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        QIO_WARN("alloc STORAGE_FILE(%s) QIO failed: %d<%s>", stream, errno, strerror(errno));
        pi_hal_storage_filerelease(priv->handle);
        pi_free(priv);
        return NULL;
    }

    QIO_DEBUG("create STORAGE_FILE(%s) QIO<%p> priv<%p>,handle<%p>", stream, pqio, priv, priv->handle);
    pqio->close = qio_storage_file_close;
    pqio->poll  = qio_storage_file_poll;
    pqio->read  = qio_storage_file_read;
    pqio->write = qio_storage_file_write;
    pqio->seek  = qio_storage_file_seek;
    pqio->priv  = priv;

    return pqio;
}
/**
 *@}
 */
