/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file snmp.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-7-18
 * @brief Simple-Network-Management-Protocol
 */
#include "netsnmp-module/snmpipc.h"
#include "nettypes.h"
#include "netmodules.h"
#include "netmisc.h"
#include "netsts.h"
#include "snmptypes.h"
#include "snmpconf.h"

#define SNMP_FLOCK_EX()     { if (s_snmp_ctx != NULL) flock(s_snmp_ctx->shm_fd, LOCK_EX); }
#define SNMP_FLOCK_UN()     { if (s_snmp_ctx != NULL) flock(s_snmp_ctx->shm_fd, LOCK_UN); }

#define SNMP_MLOCK_EX()     { if (s_snmp_ctx != NULL) pi_mutex_lock(s_snmp_ctx->srv_mtx);   }
#define SNMP_MLOCK_UN()     { if (s_snmp_ctx != NULL) pi_mutex_unlock(s_snmp_ctx->srv_mtx); }

#define MEMBER_CHECK(s)     ( s[0] ? s : "unknown" )
#define NOT_SUPPORT_NUMBER  -2

#define SNMP_CONF_PATH      NET_BASE_DIR"/snmpd.conf"
#define SNMP_PID_FILE       NET_BASE_DIR"/snmpd.pid"
#define SNMP_RESET_TIME     1800


#define ENUM_OUT(e, v, s)   e,
typedef enum PT_ALERT_TABLE(ENUM_OUT) PT_ALERT_INDEX_E;
#undef  ENUM_OUT

#define DESC_OUT(e, v, s)   s,
static const char*  s_pt_alert_desc[] = PT_ALERT_TABLE(DESC_OUT);
#undef  DESC_OUT

#define CODE_OUT(e, v, s)   v,
static uint32_t     s_pt_alert_code[] = PT_ALERT_TABLE(CODE_OUT);
#undef  CODE_OUT

#define UINT32_FROM_BYTES(bytes) \
    ((uint32_t)(bytes)[0] | \
      ((uint32_t)(bytes)[1] << 8) | \
      ((uint32_t)(bytes)[2] << 16) | \
      ((uint32_t)(bytes)[3] << 24))

typedef struct snmp_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     srv_tid;
    PI_MUTEX_T      srv_mtx;
    SNMP_MAP_S*     snmp_map;
    char            ieee1284[IEEE1284_LEN];
    char            pdt_name[32];
    int32_t         shm_fd;
    uint8_t         reload;
}
SNMP_CTX_S;

static SNMP_CTX_S*  s_snmp_ctx = NULL;

static void* snmp_ipc_shm(int32_t* shm_fd, const char* shm_name, size_t size)
{
    void*   ptr;
    int32_t fd;

    fd = shm_open(shm_name, O_RDWR | O_CREAT, 0644);
    if ( fd < 0 )
    {
        NET_WARN("shm_open(%s) failed: %d<%s>", shm_name, errno, strerror(errno));
        return NULL;
    }

    ftruncate(fd, size);
    ptr = mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if ( ptr == NULL )
    {
        NET_WARN("mmap(%s) failed: %d<%s>", shm_name, errno, strerror(errno));
        close(fd);
        shm_unlink(shm_name);
    }
    else
    {
        *shm_fd = fd;
    }

    return ptr;
}

/**
 * @brief   CONSUMPTION State would be converted to SNMP MIB value
 *          [tb]INPUT_FORMAT:  0-NORMAL, 1-MISSING, 2-LOW,      3-EMPTY,     4-EMPTY_STOP, 5-DISMATCH
 *          [tb]MIB_FORMAT:    0-NORMAL, 1-MISSING, 2-DISMATCH, 3-LIFE_STOP, 4-LOW
 *          [dr]INPUT_FORMAT:  0-NORMAL, 1-MISSING, 2-LOW,      3-EMPTY,     4-DISMATCH
 *          [dr]MIB_FORMAT:    0-NORMAL, 1-MISSING, 2-DISMATCH, 3-LIFE_STOP, 4-NEAR_EMPTY
 *          [wtb]INPUT_FORMAT: 0-NORMAL, 1-FULL,    2-NEAR_FULL,3-MISSING
 *          [wtb]MIB_FORMAT:   0-NORMAL, 1-MISSING, 2-FULL,     3-NEAR_FULL
 * @param[in]   marker_index    : Color from MARKER_ID_E
 * @param[in]   type            : 0 - tb 1 - dr 2-wtb
 * @return      ret consumption state in SNMP MIB format
 * <AUTHOR>
 * @date        2024-8-23
 */
static uint8_t snmp_parser_consumption_state(uint8_t marker_index, uint8_t type)
{
    uint8_t ret = 0;

    if ( type == 0 )
    {
        ret = netdata_get_tb_status(DATA_MGR_OF(s_snmp_ctx), marker_index);
        switch ( ret )
        {
        case 2:             // V_ID_TONER_LOW
            ret = 4;        // MIB_LOW
            break;
        case 4:             // V_ID_TONER_EMPTY_STOP
            ret = 3;        // MIB_LIFE_STOP
            break;
        case 5:             // V_ID_TONER_DISMATCH
            ret = 2;        // MIB_DISMATCH
            break;
        default:
            break;
        }
    }
    else if ( type == 1 )
    {
        ret = netdata_get_dr_status(DATA_MGR_OF(s_snmp_ctx), marker_index);
        switch ( ret )
        {
        case 2:             // V_ID_DRUM_LOW
            ret = 4;        // MIB_NEAR_EMPTY
            break;
        case 4:             // V_ID_DRUM_DISMATCH
            ret = 2;        // MIB_DISMATCH
            break;
        default:
            break;
        }
    }
    else if ( type == 2 )
    {
        ret = netdata_get_wtb_status(DATA_MGR_OF(s_snmp_ctx));
        switch ( ret )
        {

        case 1:             // V_ID_WASTE_TONER_FULL
            ret = 2;        // MIB_FULL
            break;
        case 2:             // V_ID_WASTE_TONER_NEAR_FULL
            ret = 3;        // MIB_NEAR_FULL
            break;
        case 3:             // V_ID_WASTE_TONER_MISSING
            ret = 1;        // MIB_MISSING
            break;
        default:
            break;
        }
    }
    else
    {
        NET_DEBUG("Unknown consumption type :%d", type);
    }
    return ret;
}

/* Pantum-MPS-MIB .*******.4.1.40093.10 */
static void snmp_init_priv_mibs(const char* mfg_name, const char* pdt_sn, uint32_t support_color, uint32_t print_speed)
{
    STATIC_STATUS_S*    static_info = &(s_snmp_ctx->snmp_map->_ptQueryStaticFeature);
    SNMP_MAP_S*         snmp_map    = s_snmp_ctx->snmp_map;

    netdata_get_static_feature(DATA_MGR_OF(s_snmp_ctx), static_info, sizeof(STATIC_STATUS_S));
    /* .1.1.(1 ~ 16) */
    snprintf(snmp_map->_ptFirmwareVersion,      sizeof(snmp_map->_ptFirmwareVersion),   "%s", static_info->firmware_ver);
    snmp_map->_ptCardMemory                     = UINT32_FROM_BYTES(static_info->card_memorysize);
    snprintf(snmp_map->_ptEngineVersion,        sizeof(snmp_map->_ptEngineVersion),     "%s", static_info->engine_ver);
    snprintf(snmp_map->_ptSerialNumber,         sizeof(snmp_map->_ptSerialNumber),      "%s", pdt_sn);
    snprintf(snmp_map->_ptManufacturerName,     sizeof(snmp_map->_ptManufacturerName),  "%s", mfg_name);
    snprintf(snmp_map->_ptProductDate,          sizeof(snmp_map->_ptProductDate),       "%s", static_info->product_date);
    snprintf(snmp_map->_ptProductName,          sizeof(snmp_map->_ptProductName),       "%s", s_snmp_ctx->pdt_name);
    snprintf(snmp_map->_ptBootVersion,          sizeof(snmp_map->_ptBootVersion),       "%s", static_info->boot_ver);
    snprintf(snmp_map->_ptKernelVersion,        sizeof(snmp_map->_ptKernelVersion),     "%s", static_info->kernel_ver);
    for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        netdata_get_mac_addr(DATA_MGR_OF(s_snmp_ctx), ifid, snmp_map->_ptMACAddress, sizeof(snmp_map->_ptMACAddress));
        if ( STRING_NO_EMPTY(snmp_map->_ptMACAddress) )
        {
            break;
        }
    }
    snmp_map->_ptPrintColor                     = ( support_color ? 2 : 1 );
    snmp_map->_ptPrintSpeed                     = print_speed;
    snprintf(snmp_map->_ptGeneralProduceConfig, sizeof(snmp_map->_ptGeneralProduceConfig),          "%s", "Unknown");
    snprintf(snmp_map->_ptDeviceMibMpsVer,      sizeof(snmp_map->_ptDeviceMibMpsVer),               "%s", "Unknown");
    snmp_map->_ptDeviceMibPrintControlEnable    = 0;
    snprintf(snmp_map->_ptDeviceMibPrintControlVer, sizeof(snmp_map->_ptDeviceMibPrintControlVer),  "%s", "Unknown");
    /* .1.3.2 */
    snmp_map->_ptPrintSpeedMode                 = NOT_SUPPORT_NUMBER;
    /* .1.3.4.(4、6 ~ 9) */
    snmp_map->_ptMutePrintSetting               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptShutdownConditonSetting        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptShutdownDelaySetting           = NOT_SUPPORT_NUMBER;
    snmp_map->_ptClesnFixingPageSetting         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptImageCheapSetting              = NOT_SUPPORT_NUMBER;
    /* .********.1.(1 ~ 2) */
    snmp_map->_ptVolumeIndex                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptVolumeSetting                  = NOT_SUPPORT_NUMBER;
    /* .********.1.15 */
    snmp_map->_ptOnlineUpdateSetting            = NOT_SUPPORT_NUMBER;
    /* .********.1.16.(1 ~ 3) */
    snmp_map->_ptWindowsLogInEnable             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptServerAuthentication           = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWindowsLogIn                   = NOT_SUPPORT_NUMBER;
    /* .*******.1.(1 ~ 4) */
    snmp_map->_ptTrayIndex                      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptTrayPlacingPaperTips           = 1;
    snmp_map->_ptPaperSizeSetting               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPaperTypeSetting               = NOT_SUPPORT_NUMBER;
    /* .*******.(1 ~ 6) */
    snmp_map->_ptPaperSourceSetting             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintingQuantitySetting        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintDuplexSetting             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptImageOrientationSetting        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintConcentrationSetting      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptResolutionSetting              = NOT_SUPPORT_NUMBER;
    /* .1.3.6.(1 ~ 5) */
    snmp_map->_ptWPSModeEnable                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWirelessFrequencySetting       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWirelessDirectConnectSetting   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptEmailEncryptionSetting         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptNetworkContactsEnable          = NOT_SUPPORT_NUMBER;
    /* .1.3.7.(1 ~ 6) */
    snmp_map->_ptUserLoginEnable                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPanelSessionTimeoutTimeSetting = NOT_SUPPORT_NUMBER;
    snmp_map->_ptHardwarePortsEnable            = 1;
    snmp_map->_ptForbidScanTOEmailSmbFtpEnable  = 0;
    snmp_map->_ptWebEncryptionEnable            = NOT_SUPPORT_NUMBER;
    snmp_map->_ptMemoryResetSetting             = NOT_SUPPORT_NUMBER;
    /* .1.5.1  */
    snmp_map->_ptPrintGeneralStatus             = NOT_SUPPORT_NUMBER;
    /* .*******.1.(1 ~ 4) */
    snprintf(snmp_map->_ptToneindexK,           sizeof(snmp_map->_ptToneindexK),        "%s", "K");
    snprintf(snmp_map->_ptToneindexC,           sizeof(snmp_map->_ptToneindexC),        "%s", "C");
    snprintf(snmp_map->_ptToneindexM,           sizeof(snmp_map->_ptToneindexM),        "%s", "M");
    snprintf(snmp_map->_ptToneindexY,           sizeof(snmp_map->_ptToneindexY),        "%s", "Y");
    /* .*******.2.(1 ~ 4) */
    snprintf(snmp_map->_ptTonerUnitK,           sizeof(snmp_map->_ptTonerUnitK),        "%s", "%");
    snprintf(snmp_map->_ptTonerUnitC,           sizeof(snmp_map->_ptTonerUnitC),        "%s", "%");
    snprintf(snmp_map->_ptTonerUnitM,           sizeof(snmp_map->_ptTonerUnitM),        "%s", "%");
    snprintf(snmp_map->_ptTonerUnitY,           sizeof(snmp_map->_ptTonerUnitY),        "%s", "%");
    /* .*******.8.(1 ~ 4) */
    snprintf(snmp_map->_ptTonerDescriptionK,    sizeof(snmp_map->_ptTonerDescriptionK), "%s", "black");
    snprintf(snmp_map->_ptTonerDescriptionC,    sizeof(snmp_map->_ptTonerDescriptionC), "%s", "cyan");
    snprintf(snmp_map->_ptTonerDescriptionM,    sizeof(snmp_map->_ptTonerDescriptionM), "%s", "magenta");
    snprintf(snmp_map->_ptTonerDescriptionY,    sizeof(snmp_map->_ptTonerDescriptionY), "%s", "yellow");
    /* .*******.1.(1 ~ 4) */
    snprintf(snmp_map->_ptCartridgeUnitK,       sizeof(snmp_map->_ptCartridgeUnitK),    "%s", "%");
    snprintf(snmp_map->_ptCartridgeUnitC,       sizeof(snmp_map->_ptCartridgeUnitC),    "%s", "%");
    snprintf(snmp_map->_ptCartridgeUnitM,       sizeof(snmp_map->_ptCartridgeUnitM),    "%s", "%");
    snprintf(snmp_map->_ptCartridgeUnitY,       sizeof(snmp_map->_ptCartridgeUnitY),    "%s", "%");
    /* .*******.5.(1 ~ 4) */
    snprintf(snmp_map->_ptCartridgeSerialK,     sizeof(snmp_map->_ptCartridgeSerialK),  "%s", "Unknown");              ///< K硒鼓序列号
    snprintf(snmp_map->_ptCartridgeSerialC,     sizeof(snmp_map->_ptCartridgeSerialC),  "%s", "Unknown");              ///< C硒鼓序列号
    snprintf(snmp_map->_ptCartridgeSerialM,     sizeof(snmp_map->_ptCartridgeSerialM),  "%s", "Unknown");              ///< M硒鼓序列号
    snprintf(snmp_map->_ptCartridgeSerialY,     sizeof(snmp_map->_ptCartridgeSerialY),  "%s", "Unknown");              ///< Y硒鼓序列号
    /* .1.5.4.1.(2 ~ 5) */
    snmp_map->_ptWasterTonerRemain              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWasterTonerMaximum             = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptWasterTonerModel,     sizeof(snmp_map->_ptWasterTonerModel),  "%s", "Unknown");
    snprintf(snmp_map->_ptWasterTonerSerial,    sizeof(snmp_map->_ptWasterTonerSerial), "%s", "Unknown");
    /* .1.(6 ~ 7) */
    snmp_map->_ptCopyScanMode                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobType                    = NOT_SUPPORT_NUMBER;
    /* .2.1.1.(1 ~ 11) */
    snmp_map->_ptJobIndex                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobID                          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobCurPrintptgeNumber          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobTray                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobptperSize                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobptperMedia                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobMemory                      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobVia                         = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptJobOwner,             sizeof(snmp_map->_ptJobOwner),          "%s", "Unknown");
    snmp_map->_ptPrintJobID                     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobAliveFlag                   = NOT_SUPPORT_NUMBER;
    /* .2.1.2.(1 ~ 11) */
    snmp_map->_ptScanJobIndex                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanJobID                      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobCurScanpageNumber           = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanJobTray                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanJobpaperSize               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanJobpaperMedia              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanJobMemory                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanJobVia                     = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptScanJobOwner,         sizeof(snmp_map->_ptScanJobOwner),          "%s", "Unknown");
    snmp_map->_ptScanJobAliveFlag               = NOT_SUPPORT_NUMBER;
    /* .2.1.3.(1 ~ 11) */
    snmp_map->_ptCopyJobIndex                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobID                      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobCurCopypageNumber           = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobTray                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobpaperSize               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobpaperMedia              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobMemory                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyJobVia                     = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptCopyJobOwner,         sizeof(snmp_map->_ptCopyJobOwner),          "%s", "Unknown");
    snmp_map->_ptCopyJobAliveFlag               = NOT_SUPPORT_NUMBER;
    /* .2.1.4.(1 ~ 11) */
    snmp_map->_ptFaxJobIndex                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobID                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptJobCurFaxpageNumber            = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobTray                     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobpaperSize                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobpaperMedia               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobMemory                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobVia                      = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptFaxJobOwner,          sizeof(snmp_map->_ptFaxJobOwner),          "%s", "Unknown");
    snmp_map->_ptFaxJobID                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxJobAliveFlag                = NOT_SUPPORT_NUMBER;
    /* .3.1.(9、11、16、23  ~ 29) */
    snmp_map->_ptPrintCounterB4                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterIOSB5              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterOficio             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterEnv10              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterEnvMon             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterEnvC6              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterEnvC5              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterEnvDL              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterPosterCard         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintCounterEquPages           = NOT_SUPPORT_NUMBER;
    /* .3.2.(9、10、11、14、15、16、22  ~ 28) */
    snmp_map->_ptScanCounterB4                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterIOSB5               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounter11x17               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterFolio               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterOficio              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterExec                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterStatement           = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterUserdef             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterEnv10               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterEnvMon              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterEnvC6               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterEnvC5               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterEnvDL               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterPosterCard          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanCounterEquPages            = NOT_SUPPORT_NUMBER;
    /* .3.3.(10、11、15、22  ~ 27) */
    snmp_map->_ptCopyCounterIOSB5               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterOficio              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterEnv10               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterEnvMon              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterEnvC6               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterEnvC5               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterEnvDL               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterPosterCard          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyCounterEquPages            = NOT_SUPPORT_NUMBER;
    /* .3.4.(1 ~ 30) */
    snmp_map->_ptFaxCounterTotal                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterColor                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterMono                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterDuplex               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterA3                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterA4                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterA5                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterB4                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterJISB5                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterIOSB5                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounter11x17                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterLetter               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterLegal                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterFolio                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterOficio               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterExec                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterStatement            = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounter8k                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounter16k                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterUserdef              = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterA6                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterEnv10                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterEnvMon               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterEnvC6                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterEnvC5                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterEnvDL                = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterPosterCard           = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterEquPages             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterColor_A3             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFaxCounterMono_A3              = NOT_SUPPORT_NUMBER;
    /* .5.1.2 */
    snmp_map->_ptIpMethod                       = 1;
    /* .5.2.4.( 3 ~ 8 ) */
    snprintf(snmp_map->_ptWiredIpv6ManuConfigureAddr,           sizeof(snmp_map->_ptWiredIpv6ManuConfigureAddr),        "%s", "Unknown");
    snprintf(snmp_map->_ptWiredIpv6ManuGatewayAddr,             sizeof(snmp_map->_ptWiredIpv6ManuGatewayAddr),          "%s", "Unknown");
    snprintf(snmp_map->_ptWiredIpv6AutoConfigureAddr1,          sizeof(snmp_map->_ptWiredIpv6AutoConfigureAddr1),       "%s", "Unknown");
    snprintf(snmp_map->_ptWiredIpv6AutoGatewayAddr,             sizeof(snmp_map->_ptWiredIpv6AutoGatewayAddr),          "%s", "Unknown");
    snprintf(snmp_map->_ptWiredIpv6ManuConfigureAddrMark,       sizeof(snmp_map->_ptWiredIpv6ManuConfigureAddrMark),    "%s", "Unknown");
    snprintf(snmp_map->_ptWiredIpv6AutoConfigureAddrMark1,      sizeof(snmp_map->_ptWiredIpv6AutoConfigureAddrMark1),   "%s", "Unknown");
    /* .5.3.4.( 3 ~ 8 ) */
    snprintf(snmp_map->_ptWirelessIpv6ManuConfigureAddr,        sizeof(snmp_map->_ptWirelessIpv6ManuConfigureAddr),     "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessIpv6ManuGatewayAddr,          sizeof(snmp_map->_ptWirelessIpv6ManuGatewayAddr),       "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessIpv6AutoConfigureAddr1,       sizeof(snmp_map->_ptWirelessIpv6AutoConfigureAddr1),    "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessIpv6AutoGatewayAddr,          sizeof(snmp_map->_ptWirelessIpv6AutoGatewayAddr),       "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessIpv6ManuConfigureAddrMark,    sizeof(snmp_map->_ptWirelessIpv6ManuConfigureAddrMark), "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessIpv6AutoConfigureAddrMark1,   sizeof(snmp_map->_ptWirelessIpv6AutoConfigureAddrMark1),"%s", "Unknown");
    /* .5.4.1.( 1 ~ 3 ) */
    snmp_map->_ptDHCPPort                       = 68;
    snmp_map->_ptDHCPEnable                     = 1;
    snmp_map->_ptDHCPv6Enable                   = 1;
    /* .5.4.2.( 1 ~ 2 ) */
    snmp_map->_ptDHCPSPort                      = 67;
    snmp_map->_ptDHCPSEnable                    = 1;
    /* .5.4.3.( 1 ~ 9 ) */
    snmp_map->_ptHTTPPort                       = 80;
    snmp_map->_ptHTTPEnable                     = 1;
    snmp_map->_ptHTTPNumLinks                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptHTTPBytesRemaining             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptHTTPLinksIndex                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptHTTPLinksStatus                = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptHTTPLinksLabel,                       sizeof(snmp_map->_ptHTTPLinksLabel),                    "%s", "Unknown");
    snprintf(snmp_map->_ptHTTPLinksURL,                         sizeof(snmp_map->_ptHTTPLinksURL),                      "%s", "Unknown");
    snmp_map->_ptHTTPConfigEnable               = NOT_SUPPORT_NUMBER;

    /* .5.4.4.( 1 ~ 2 ) */
    snmp_map->_ptHTTPSPort                      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptHTTPSEnable                    = NOT_SUPPORT_NUMBER;
#if CONFIG_NET_LPD
    /* .5.4.5.1 */
    snmp_map->_ptLPDPort                        = 515;
#else
    snmp_map->_ptLPDPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptLPDEnable                      = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_LPD

#if CONFIG_NET_WSD
    /* .5.4.6.( 1 ~ 2 ) */
    snmp_map->_ptWSDPort                        = 3702;
#else
    snmp_map->_ptWSDPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWSDEnable                      = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_WSD

#if CONFIG_NET_SNMP
    /* .5.4.7.1 */
    snmp_map->_ptSNMPPort                       = 161;
#else
    snmp_map->_ptSNMPPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptSNMPEnable                     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptSNMPProtocolVer                = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_SNMP

#if CONFIG_NET_SMTP
    /* .5.4.8.2 */
    snmp_map->_ptSMTPEnable                     = 1;
#else
    snmp_map->_ptSMTPPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptSMTPEnable                     = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_SMTP

#if CONFIG_NET_FTP
    /* .5.4.9.( 1 ~ 2 ) */
    snmp_map->_ptFTPPort                        = 21;
    snmp_map->_ptFTPEnable                      = 1;
#else
    snmp_map->_ptFTPPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptFTPEnable                      = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_FTP

    /* .5.4.10.( 1 ~ 3 ) */
    snmp_map->_ptSMBPort                        = 139;
    snmp_map->_ptSMBEnable                      = 1;

#if CONFIG_NET_SLP
    /* .******** */
    snmp_map->_ptSLPPort                        = 427;
#else
    snmp_map->_ptSLPPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptSLPEnable                      = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_SLP

#if CONFIG_NET_IPPSRV
    /* .******** */
    snmp_map->_ptIPPPort                        = 631;
#else
    snmp_map->_ptIPPPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptIPPEnable                      = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_IPPSRV

    /* .5.4.13.( 1 ~ 2 )  */
    snmp_map->_ptSNTPPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptSNTPEnable                     = NOT_SUPPORT_NUMBER;
#if CONFIG_NET_BONJOUR
    /* .******** */
    snmp_map->_ptmDNSPort                       = 5353;
    /* .******** */
    snmp_map->_ptBonjourPort                    = 5353;
#else
    snmp_map->_ptmDNSPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptmDNSEnable                     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptBonjourPort                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptBonjourEnable                  = NOT_SUPPORT_NUMBER;
#endif
    /* .5.4.15.( 1 ~ 2 ) */
    snmp_map->_ptDNSPort                        = 53;
    snmp_map->_ptDNSEnable                      = 1;
    /* .5.4.17.( 1 ~ 2 ) */
    snmp_map->_ptTelnetPort                     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptTelnetEnable                   = NOT_SUPPORT_NUMBER;
    /* .5.4.18.( 1 ~ 2 ) */
    snmp_map->_ptUDPPort                        = 53;
    snmp_map->_ptUDPEnable                      = 1;

    /* .5.4.20.( 1 ~ 2 ) */
    snmp_map->_ptSoapPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptSoapEnable                     = NOT_SUPPORT_NUMBER;

#if CONFIG_NET_NETBIOS
    /* .5.4.21.( 1 ~ 2 ) */
    snmp_map->_ptNetBiosPort                    = 137;
    snmp_map->_ptNetBiosEnable                  = 1;
#else
    snmp_map->_ptNetBiosPort                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptNetBiosEnable                  = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_NETBIOS

#if CONFIG_NET_LLMNR
    /* .5.4.22.( 1 ~ 2 ) */
    snmp_map->_ptLLMNRPort                      = 5355;
    snmp_map->_ptLLMNREnable                    = 1;
#else
    snmp_map->_ptLLMNRPort                      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptLLMNREnable                    = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_LLMNR

    /* .5.4.23.( 1 ~ 2 ) */
    snmp_map->_ptOpenSSLPort                    = 443;
    snmp_map->_ptOpenSSLEnable                  = 1;
    /* .5.4.24.( 1 ~ 2 ) */
#if CONFIG_NET_IPPSRV
    snmp_map->_ptAirPrintPort                   = 631;
    snmp_map->_ptAirPrintEnable                 = 1;
    /* .5.4.25.( 1 ~ 2 ) */
    snmp_map->_ptAirScanPort                    = 80;
    snmp_map->_ptAirScanEnable                  = 1;
    /* .5.4.26.( 1 ~ 2 ) */
    snmp_map->_ptMopriaPort                     = 631;
    snmp_map->_ptMopriaEnable                   = 1;
#else
    snmp_map->_ptAirPrintPort                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptAirPrintEnable                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptAirScanPort                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptAirScanEnable                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptMopriaPort                     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptMopriaEnable                   = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_IPPSRV

    /* .5.4.27.( 1 ~ 2 ) */
    snmp_map->_ptLLTDPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptLLTDEnable                     = NOT_SUPPORT_NUMBER;
    /* .5.4.28.( 1 ~ 2 ) */
    snmp_map->_ptNetScanPort                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptNetScanEnable                  = NOT_SUPPORT_NUMBER;
    /* .5.4.29.( 1 ~ 2 ) */
    snmp_map->_ptARPPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptARPEnable                      = NOT_SUPPORT_NUMBER;
    /* .5.4.30.( 1 ~ 2 ) */
    snmp_map->_ptLLAPort                        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptLLAEnable                      = NOT_SUPPORT_NUMBER;
    /* .5.4.31.( 1 ~ 2 ) */
    snmp_map->_ptTFTPPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptTFTPEnable                     = NOT_SUPPORT_NUMBER;
    /* .5.4.32.( 1 ~ 2 ) */
    snmp_map->_ptNetservicePort                 = 139;
    snmp_map->_ptNetserviceEnable               = 1;
    /* .5.4.33.( 1 ~ 2 ) */
    snmp_map->_ptWi_FiPort                      = NOT_SUPPORT_NUMBER;
    /* .5.4.34.( 1 ~ 2 ) */
    snmp_map->_ptBluetoothPort                  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptBluetoothEnable                = NOT_SUPPORT_NUMBER;
    /* .5.4.35.( 1 ~ 2 ) */
    snmp_map->_ptEthernetPort                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptEthernetEnable                 = 1;
    /* .5.4.36.( 1 ~ 2 ) */
    snmp_map->_pt802_1XPort                     = NOT_SUPPORT_NUMBER;
    snmp_map->_pt802_1XEnable                   = NOT_SUPPORT_NUMBER;
    snmp_map->_pt802_1XAuthenticationMethod     = NOT_SUPPORT_NUMBER;
    /* .5.4.38.( 1 ~ 2 ) */
    snmp_map->_ptLDAPPort                       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptLDAPEnable                     = NOT_SUPPORT_NUMBER;

    /* .6.( 1 ~ 3 ) ptStatistic */
    snmp_map->_ptPrintStatAllSizeBlackSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatAllSizeBlackDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatAllSizeColorSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatAllSizeColorDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatUserdefTotalSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatUserdefTotalDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatUserdefBlackSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatUserdefBlackDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatUserdefColorSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatUserdefColorDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA3TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA3TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA3BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA3BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA3ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA3ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA4TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA4TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA4BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA4BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA4ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA4ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA5TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA5TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA5BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA5BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA5ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA5ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatB4TotalAll            = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4BlackAll            = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4ColorAll            = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4TotalSingle         = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4TotalDuplex         = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4BlackSingle         = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4BlackDuplex         = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4ColorSingle         = NOT_SUPPORT_NUMBER;   // MIB平台规格：IOS B4,不包含JIS B4
    snmp_map->_ptPrintStatB4ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatJISB5TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatJISB5TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatJISB5BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatJISB5BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatJISB5ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatJISB5ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatIOSB5ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat11x17TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat11x17TotalDuplex      = NOT_SUPPORT_NUMBER;
    //snmp_map->_ptPrintStat11x17BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat11x17BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat11x17BlackDuplex      = NOT_SUPPORT_NUMBER;
    //snmp_map->_ptPrintStat11x17ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat11x17ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat11x17ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLetterTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLetterTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLetterBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLetterBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLetterColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLetterColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLegalTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLegalTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLegalBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLegalBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLegalColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatLegalColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatFilioTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatFilioTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatFilioBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatFilioBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatFilioColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatFilioColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioTotalAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioBlackAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioColorAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatOficioColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatExecutiveTotalSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatExecutiveTotalDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatExecutiveBlackSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatExecutiveBlackDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatExecutiveColorSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatExecutiveColorDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatStatementTotalSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatStatementTotalDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatStatementBlackSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatStatementBlackDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatStatementColorSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatStatementColorDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat8KTotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat8KTotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat8KBlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat8KBlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat8KColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat8KColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat16KTotalSingle        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat16KTotalDuplex        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat16KBlackSingle        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat16KBlackDuplex        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat16KColorSingle        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStat16KColorDuplex        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA6TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA6TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA6BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA6BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA6ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatA6ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnv10ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonTotalAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonBlackAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonColorAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvMonColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC6ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvC5ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLTotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLBlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatEnvDLColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardTotalAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardTotalSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardTotalDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardBlackAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardBlackSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardBlackDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardColorAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardColorSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptPrintStatPostcardColorDuplex   = NOT_SUPPORT_NUMBER;

    snmp_map->_ptScanStatAllSizeBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatAllSizeBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatAllSizeColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatAllSizeColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefTotalAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefBlackAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefColorAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatUserdefColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA3TotalSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA3TotalDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA3BlackSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA3BlackDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA3ColorSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA3ColorDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA4TotalSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA4TotalDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA4BlackSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA4BlackDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA4ColorSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA4ColorDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA5TotalSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA5TotalDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA5BlackSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA5BlackDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA5ColorSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA5ColorDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4TotalAll             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4BlackAll             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4ColorAll             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4TotalSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4TotalDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4BlackSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4BlackDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4ColorSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatB4ColorDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatJISB5TotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatJISB5TotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatJISB5BlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatJISB5BlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatJISB5ColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatJISB5ColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5TotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5TotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5TotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5BlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5BlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5BlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5ColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5ColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatIOSB5ColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17TotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17TotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17TotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17BlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17BlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17BlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17ColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17ColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat11x17ColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLetterTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLetterTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLetterBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLetterBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLetterColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLetterColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLegalTotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLegalTotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLegalBlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLegalBlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLegalColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatLegalColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioTotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioTotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioTotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioBlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioBlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioBlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatFilioColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioTotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioBlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatOficioColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveTotalAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveTotalSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveTotalDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveBlackAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveBlackSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveBlackDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveColorAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveColorSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatExecutiveColorDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementTotalAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementBlackAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementColorAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementTotalSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementTotalDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementBlackSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementBlackDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementColorSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatStatementColorDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat8KTotalSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat8KTotalDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat8KBlackSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat8KBlackDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat8KColorSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat8KColorDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat16KTotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat16KTotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat16KBlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat16KBlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat16KColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStat16KColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA6TotalSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA6TotalDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA6BlackSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA6BlackDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA6ColorSingle          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatA6ColorDuplex          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10TotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10TotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10TotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10BlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10BlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10BlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10ColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10ColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnv10ColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonTotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonBlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvMonColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6TotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6TotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6TotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6BlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6BlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6BlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6ColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6ColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC6ColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5TotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5TotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5TotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5BlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5BlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5BlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5ColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5ColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvC5ColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLTotalAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLTotalSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLTotalDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLBlackAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLBlackSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLBlackDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLColorAll          = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLColorSingle       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatEnvDLColorDuplex       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardTotalAll       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardTotalSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardTotalDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardBlackAll       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardBlackSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardBlackDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardColorAll       = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardColorSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptScanStatPostcardColorDuplex    = NOT_SUPPORT_NUMBER;

    snmp_map->_ptCopyStatAllSizeBlackSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatAllSizeBlackDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatAllSizeColorSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatAllSizeColorDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatUserdefTotalSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatUserdefTotalDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatUserdefBlackSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatUserdefBlackDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatUserdefColorSingle    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatUserdefColorDuplex    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA3TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA3TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA3BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA3BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA3ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA3ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA4TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA4TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA4BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA4BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA4ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA4ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA5TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA5TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA5BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA5BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA5ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA5ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4TotalAll            = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4BlackAll            = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4ColorAll            = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatB4ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatJISB5TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatJISB5TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatJISB5BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatJISB5BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatJISB5ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatJISB5ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatIOSB5ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat11x17TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat11x17TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat11x17BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat11x17BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat11x17ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat11x17ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLetterTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLetterTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLetterBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLetterBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLetterColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLetterColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLegalTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLegalTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLegalBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLegalBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLegalColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatLegalColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatFilioTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatFilioTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatFilioBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatFilioBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatFilioColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatFilioColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioTotalAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioBlackAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioColorAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatOficioColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatExecutiveTotalSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatExecutiveTotalDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatExecutiveBlackSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatExecutiveBlackDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatExecutiveColorSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatExecutiveColorDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatStatementTotalSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatStatementTotalDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatStatementBlackSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatStatementBlackDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatStatementColorSingle  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatStatementColorDuplex  = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat8KTotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat8KTotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat8KBlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat8KBlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat8KColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat8KColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat16KTotalSingle        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat16KTotalDuplex        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat16KBlackSingle        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat16KBlackDuplex        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat16KColorSingle        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStat16KColorDuplex        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA6TotalSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA6TotalDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA6BlackSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA6BlackDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA6ColorSingle         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatA6ColorDuplex         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnv10ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonTotalAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonTotalSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonTotalDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonBlackAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonBlackSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonBlackDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonColorAll        = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonColorSingle     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvMonColorDuplex     = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC6ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5TotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5TotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5TotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5BlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5BlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5BlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5ColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5ColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvC5ColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLTotalAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLTotalSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLTotalDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLBlackAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLBlackSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLBlackDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLColorAll         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLColorSingle      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatEnvDLColorDuplex      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardTotalAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardTotalSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardTotalDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardBlackAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardBlackSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardBlackDuplex   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardColorAll      = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardColorSingle   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptCopyStatPostcardColorDuplex   = NOT_SUPPORT_NUMBER;

}

/* HOST-RESOURCES-MIB .*******.2.1.25 */
static void snmp_init_hres_mibs(const char* ser_name)
{
    STATIC_STATUS_S*    static_info = &(s_snmp_ctx->snmp_map->_ptQueryStaticFeature);
    HRES_MIB_S*         hres_mib    = &(s_snmp_ctx->snmp_map->hres);
    /* .1.6 */
    hres_mib->_hrSystemProcesses            = 0;
    /* .1.7 */
    hres_mib->_hrSystemMaxProcesses         = NOT_SUPPORT_NUMBER;
    /* .2.2 */
    hres_mib->_hrMemorySize                 = (UINT32_FROM_BYTES(static_info->card_memorysize) * 1024); //KB
    /* .3.1.(1 ~ )*/
    hres_mib->_hrStorageIndex               = NOT_SUPPORT_NUMBER;
    hres_mib->_hrStorageType                = NOT_SUPPORT_NUMBER;
    snprintf(hres_mib->_hrStorageDescr,     sizeof(hres_mib->_hrStorageDescr),  "Unknown");
    hres_mib->_hrStorageAllocationUnits     = NOT_SUPPORT_NUMBER;
    hres_mib->_hrStorageSize                = NOT_SUPPORT_NUMBER;
    hres_mib->_hrStorageUsed                = NOT_SUPPORT_NUMBER;
    hres_mib->_hrStorageAllocationFailures  = NOT_SUPPORT_NUMBER;
    /* .3.2.1.(1 ~ 6) */
    hres_mib->_hrDeviceIndex                = 1;
    snprintf(hres_mib->_hrDeviceType,       sizeof(hres_mib->_hrDeviceType),    ".*******.2.1.25.3.1.5");
    snprintf(hres_mib->_hrDeviceDescr,      sizeof(hres_mib->_hrDeviceDescr),   "%s", ser_name);
    snprintf(hres_mib->_hrDeviceID,         sizeof(hres_mib->_hrDeviceID),      "%s", s_snmp_ctx->ieee1284);
    hres_mib->_hrDeviceStatus               = hrDeviceStatus_running;
    hres_mib->_hrDeviceErrors               = NOT_SUPPORT_NUMBER;
    /* .3.3.1.(1 ~ 2) */
    hres_mib->_hrProcessorFrwID             = NOT_SUPPORT_NUMBER;
    hres_mib->_hrProcessorLoad              = NOT_SUPPORT_NUMBER;
    /* .3.4.1.1 */
    hres_mib->_hrNetworkIfIndex             = NOT_SUPPORT_NUMBER;
    /* .3.5.1.(1 ~ 2) */
    hres_mib->_hrPrinterStatus              = hrPrinterStatus_idle;
    memset(hres_mib->_hrPrinterDetectedErrorState, 0, sizeof(hres_mib->_hrPrinterDetectedErrorState));
    /* .3.6.1.(1 ~ 4) */
    hres_mib->_hrDiskStorageAccess          = hrDiskStorageAccess_readWrite;
    hres_mib->_hrDiskStorageMedia           = hrDiskStorageMedia_hardDisk;
    hres_mib->_hrDiskStorageRemoveble       = hrGeneralBoolean_false;
    hres_mib->_hrDiskStorageCapacity        = (20 * 1024 * 1024);
    /* .3.7.1.(1 ~ 6) */
    hres_mib->_hrPartitionIndex             = NOT_SUPPORT_NUMBER;
    snprintf(hres_mib->_hrPartitionLabel,   sizeof(hres_mib->_hrPartitionLabel),  "Unknown");
    snprintf(hres_mib->_hrPartitionID,      sizeof(hres_mib->_hrPartitionID),     "Unknown");
    hres_mib->_hrPartitionSize              = NOT_SUPPORT_NUMBER;
    hres_mib->_hrPartitionFSIndex           = NOT_SUPPORT_NUMBER;
    /* .3.8.1.(1 ~ 9) */
    hres_mib->_hrFSIndex                    = NOT_SUPPORT_NUMBER;
    snprintf(hres_mib->_hrFSMountPoint,         sizeof(hres_mib->_hrFSMountPoint),          "Unknown");
    snprintf(hres_mib->_hrFSRemoteMountPoint,   sizeof(hres_mib->_hrFSRemoteMountPoint),    "Unknown");
    hres_mib->_hrFSType                     = NOT_SUPPORT_NUMBER;
    hres_mib->_hrFSAccess                   = NOT_SUPPORT_NUMBER;
    hres_mib->_hrFSBootable                 = NOT_SUPPORT_NUMBER;
    hres_mib->_hrFSStorageIndex             = NOT_SUPPORT_NUMBER;
    /* .4.1.1*/
    hres_mib->_hrSWOSIndex                  = NOT_SUPPORT_NUMBER;
    /* .4.2.1.(1 ~ 7)*/
    hres_mib->_hrSWRunIndex                            = NOT_SUPPORT_NUMBER;
    snprintf(hres_mib->_hrSWRunName,         sizeof(hres_mib->_hrSWRunName),          "Unknown");
    snprintf(hres_mib->_hrSWRunID,           sizeof(hres_mib->_hrSWRunID),            "Unknown");
    snprintf(hres_mib->_hrSWRunPath,         sizeof(hres_mib->_hrSWRunPath),          "Unknown");
    snprintf(hres_mib->_hrSWRunParameters,   sizeof(hres_mib->_hrSWRunParameters),    "Unknown");
    hres_mib->_hrSWRunType                  = NOT_SUPPORT_NUMBER;
    hres_mib->_hrSWRunStatus                = NOT_SUPPORT_NUMBER;
    /* .5.1.1.(1 ~ 2)*/
    hres_mib->_hrSWRunPerfCPU               = NOT_SUPPORT_NUMBER;
    hres_mib->_hrSWRunPerfMem               = NOT_SUPPORT_NUMBER;
    /* .6.3.1.(1 ~ 5)*/
    hres_mib->_hrSWInstalledIndex           = NOT_SUPPORT_NUMBER;
    snprintf(hres_mib->_hrSWInstalledName,   sizeof(hres_mib->_hrSWInstalledName),   "Unknown");
    hres_mib->_hrSWInstalledID              = NOT_SUPPORT_NUMBER;
    hres_mib->_hrSWInstalledType            = NOT_SUPPORT_NUMBER;
    hres_mib->_hrSWInstalledDate            = NOT_SUPPORT_NUMBER;

}

/* Printer-MIB .*******.2.1.43 */
static void snmp_init_prt_mibs(const char* mfg_name, const char* pdt_sn, uint32_t support_color)
{
    PRT_MIB_S*  prt_mib = &(s_snmp_ctx->snmp_map->prt);

    /* .5.1.1.1 */
    prt_mib->_prtGeneralConfigChanges               = 0;
    /* .5.1.1.(3 ~ 9) */
    prt_mib->_prtGeneralReset                       = prtGeneralReset_notResetting;
    snprintf(prt_mib->_prtGeneralCurrentOperator,   sizeof(prt_mib->_prtGeneralCurrentOperator),    "Network Administrator");
    snprintf(prt_mib->_prtGeneralServicePerson,     sizeof(prt_mib->_prtGeneralServicePerson),      "%s", mfg_name);
    prt_mib->_prtInputDefaultIndex                  = 2;                    ///< 标准纸盒
    prt_mib->_prtOutputDefaultIndex                 = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerDefaultIndex                 = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathDefaultIndex              = NOT_SUPPORT_NUMBER;
    /* .5.1.1.(11 ~ 13) */
    prt_mib->_prtConsoleNumberOfDisplayLines        = NOT_SUPPORT_NUMBER;
    prt_mib->_prtConsoleNumberOfDisplayChars        = NOT_SUPPORT_NUMBER;
    prt_mib->_prtConsoleDisable                     = prtConsoleDisable_enabled;
    /* .5.1.1.(16 ~ 17) */
    snprintf(prt_mib->_prtGeneralPrinterName,       sizeof(prt_mib->_prtGeneralPrinterName),        "%s", s_snmp_ctx->pdt_name);
    snprintf(prt_mib->_prtGeneralSerialNumber,      sizeof(prt_mib->_prtGeneralSerialNumber),       "%s", pdt_sn);
    /* .5.2.1.(1 ~ 2) */
    prt_mib->_prtStorageRefSeqNumber                = NOT_SUPPORT_NUMBER;
    prt_mib->_prtStorageRefIndex                    = NOT_SUPPORT_NUMBER;
    /* .5.3.1.(1 ~ 2) */
    prt_mib->_prtDeviceRefSeqNumber                 = NOT_SUPPORT_NUMBER;
    prt_mib->_prtDeviceRefIndex                     = NOT_SUPPORT_NUMBER;
    /* .6.1.1.(1 ~ 3) */
    prt_mib->_prtCoverIndex                         = 1;
    snprintf(prt_mib->_prtCoverDescription1,        sizeof(prt_mib->_prtCoverDescription1),         "Front Cover");
    snprintf(prt_mib->_prtCoverDescription2,        sizeof(prt_mib->_prtCoverDescription2),         "Rear Cover");
    prt_mib->_prtCoverStatus1                       = prtCoverStatus_coverClosed;
    prt_mib->_prtCoverStatus2                       = prtCoverStatus_coverClosed;
    /* .7.1.1.(1, 4) */
    prt_mib->_prtLocalizationIndex                  = 1;
    prt_mib->_prtLocalizationCharacterSet           = IANACharset_csUTF8;
    /* .8.2.1.(1 ~ 23) */
    prt_mib->_prtInputIndex                         = 1;
    prt_mib->_prtInputType                          = prtInputType_sheetFeedAutoRemovableTray;
    prt_mib->_prtInputDimUnit                       = prtGeneralUnit_micrometers;
    prt_mib->_prtInputMediaDimFeedDirDeclared       = 297;
    prt_mib->_prtInputMediaDimXFeedDirDeclared      = 432;
    prt_mib->_prtInputMediaDimFeedDirChosen         = 140;
    prt_mib->_prtInputMediaDimXFeedDirChosen        = 180;
    prt_mib->_prtInputCapacityUnit                  = prtCapacityUnit_sheets;
    prt_mib->_prtInputMaxCapacity                   = 250;
    prt_mib->_prtInputCurrentLevel                  = NOT_SUPPORT_NUMBER;
    prt_mib->_prtInputStatus                        = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtInputMediaName,           sizeof(prt_mib->_prtInputMediaName),            "Unknown");
    snprintf(prt_mib->_prtInputName,                sizeof(prt_mib->_prtInputName),                 "Tray 1");
    snprintf(prt_mib->_prtInputVendorName,          sizeof(prt_mib->_prtInputVendorName),           "%s", mfg_name);
    snprintf(prt_mib->_prtInputModel,               sizeof(prt_mib->_prtInputModel),                "Unknown");
    snprintf(prt_mib->_prtInputVersion,             sizeof(prt_mib->_prtInputVersion),              "Unknown");
    snprintf(prt_mib->_prtInputSerialNumber,        sizeof(prt_mib->_prtInputSerialNumber),         "Unknown");
    snprintf(prt_mib->_prtInputDescription,         sizeof(prt_mib->_prtInputDescription),          "Tray 1");
    prt_mib->_prtInputSecurity                      = prtPresentOnOff_notPresent;
    prt_mib->_prtInputMediaWeight                   = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtInputMediaType,           sizeof(prt_mib->_prtInputMediaType),            "Unknown");
    snprintf(prt_mib->_prtInputMediaColor,          sizeof(prt_mib->_prtInputMediaColor),           "Unknown");
    prt_mib->_prtInputMediaFormParts                = NOT_SUPPORT_NUMBER;
    /* .9.2.1.(1 ~ 24) */
    prt_mib->_prtOutputIndex                        = 0;
    prt_mib->_prtOutputType                         = prtOutputType_unRemovableBin;
    prt_mib->_prtOutputCapacityUnit                 = prtCapacityUnit_sheets;
    prt_mib->_prtOutputMaxCapacity                  = 150;
    prt_mib->_prtOutputRemainingCapacity            = NOT_SUPPORT_NUMBER;
    prt_mib->_prtOutputStatus                       = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtOutputName,               sizeof(prt_mib->_prtOutputName),                "Standard Bin");
    snprintf(prt_mib->_prtOutputVendorName,         sizeof(prt_mib->_prtOutputVendorName),          "%s", mfg_name);
    snprintf(prt_mib->_prtOutputModel,              sizeof(prt_mib->_prtOutputModel),               "Unknown");
    snprintf(prt_mib->_prtOutputVersion,            sizeof(prt_mib->_prtOutputVersion),             "Unknown");
    snprintf(prt_mib->_prtOutputSerialNumber,       sizeof(prt_mib->_prtOutputSerialNumber),        "Unknown");
    snprintf(prt_mib->_prtOutputDescription,        sizeof(prt_mib->_prtOutputDescription),         "Standard Bin");
    prt_mib->_prtOutputSecurity                     = prtPresentOnOff_notPresent;
    prt_mib->_prtOutputDimUnit                      = prtGeneralUnit_micrometers;
    prt_mib->_prtOutputMaxDimFeedDir                = 432000;
    prt_mib->_prtOutputMaxDimXFeedDir               = 216000;
    prt_mib->_prtOutputMinDimFeedDir                = 139700;
    prt_mib->_prtOutputMinDimXFeedDir               = 64000;
    prt_mib->_prtOutputStackingOrder                = prtOutputStackingOrder_firstToLast;
    prt_mib->_prtOutputPageDeliveryOrientation      = prtOutputPageDeliveryOrientation_faceDown;
    prt_mib->_prtOutputBursting                     = prtPresentOnOff_notPresent;
    prt_mib->_prtOutputDecollating                  = prtPresentOnOff_notPresent;
    prt_mib->_prtOutputPageCollated                 = prtPresentOnOff_notPresent;
    prt_mib->_prtOutputOffsetStacking               = prtPresentOnOff_notPresent;
    /* .9.2.1.(1 ~ 24) */
    prt_mib->_prtMarkerIndex                        = 1;
    prt_mib->_prtMarkerMarkTech                     = prtMarkerMarkTech_electrophotographicLaser;
    prt_mib->_prtMarkerCounterUnit                  = prtMarkerCounterUnit_impressions;
    prt_mib->_prtMarkerPowerOnCount                 = 0;
    prt_mib->_prtMarkerProcessColorants             = ( support_color ? 4 : 1 );
    prt_mib->_prtMarkerSpotColorants                = ( support_color ? 4 : 1 );
    prt_mib->_prtMarkerAddressabilityUnit           = prtGeneralUnit_tenThousandthsOfInches;
    prt_mib->_prtMarkerAddressabilityFeedDir        = 600;
    prt_mib->_prtMarkerAddressabilityXFeedDir       = 600;
    prt_mib->_prtMarkerNorthMargin                  = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerSouthMargin                  = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerWestMargin                   = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerEastMargin                   = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerStatus                       = NOT_SUPPORT_NUMBER;
    /* .11.1.1.(1 ~ 9) */
    prt_mib->_prtMarkerSuppliesIndex                = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerSuppliesMarkerIndex          = NOT_SUPPORT_NUMBER;
    /* .12.1.1.(1 ~ 5) */
    prt_mib->_prtMarkerColorantIndex                = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMarkerColorantMarkerIndex          = 1;
    prt_mib->_prtMarkerColorantRole                 = prtMarkerColorantRole_process;
    snprintf(prt_mib->_prtMarkerColorantValue,      sizeof(prt_mib->_prtMarkerColorantValue),       "black");
    prt_mib->_prtMarkerColorantTonality             = NOT_SUPPORT_NUMBER;
    /* .13.4.1.(1 ~ 11) */
    prt_mib->_prtMediaPathIndex                     = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathMaxSpeedPrintUnit         = prtMediaPathMaxSpeedPrintUnit_impressionsPerHour;
    prt_mib->_prtMediaPathMediaSizeUnit             = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathMaxSpeed                  = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathMaxMediaFeedDir           = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathMaxMediaXFeedDir          = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathMinMediaFeedDir           = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathMinMediaXFeedDir          = NOT_SUPPORT_NUMBER;
    prt_mib->_prtMediaPathType                      = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtMediaPathDescription,     sizeof(prt_mib->_prtMediaPathDescription),      "Unknown");
    prt_mib->_prtMediaPathStatus                    = NOT_SUPPORT_NUMBER;
    /* .14.1.1.(1 ~ 8) */
    prt_mib->_prtChannelIndex                       = NOT_SUPPORT_NUMBER;
    prt_mib->_prtChannelType                        = prtChannelType_chSerialPort;
    snprintf(prt_mib->_prtChannelProtocolVersion,   sizeof(prt_mib->_prtChannelProtocolVersion),    "Unknown");
    prt_mib->_prtChannelCurrentJobCntlLangIndex     = NOT_SUPPORT_NUMBER;
    prt_mib->_prtChannelDefaultPageDescLangIndex    = NOT_SUPPORT_NUMBER;
    prt_mib->_prtChannelState                       = NOT_SUPPORT_NUMBER;
    prt_mib->_prtChannelIfIndex                     = NOT_SUPPORT_NUMBER;
    prt_mib->_prtChannelStatus                      = NOT_SUPPORT_NUMBER;
    /* .15.1.1.(1 ~ 12) */
    prt_mib->_prtInterpreterIndex                   = NOT_SUPPORT_NUMBER;
    prt_mib->_prtInterpreterLangFamily              = prtInterpreterLangFamily_langPCL;
    snprintf(prt_mib->_prtInterpreterLangLevel,     sizeof(prt_mib->_prtInterpreterLangLevel),      "2.0");
    snprintf(prt_mib->_prtInterpreterLangVersion,   sizeof(prt_mib->_prtInterpreterLangVersion),    "3.0");
    snprintf(prt_mib->_prtInterpreterDescription,   sizeof(prt_mib->_prtInterpreterDescription),    "ORD");
    snprintf(prt_mib->_prtInterpreterVersion,       sizeof(prt_mib->_prtInterpreterVersion),        "4.0");
    prt_mib->_prtInterpreterDefaultOrientation      = prtInterpreterDefaultOrientation_landscape;
    prt_mib->_prtInterpreterFeedAddressability      = NOT_SUPPORT_NUMBER;
    prt_mib->_prtInterpreterXFeedAddressability     = NOT_SUPPORT_NUMBER;
    prt_mib->_prtInterpreterDefaultCharSetIn        = NOT_SUPPORT_NUMBER;
    prt_mib->_prtInterpreterDefaultCharSetOut       = NOT_SUPPORT_NUMBER;
    prt_mib->_prtInterpreterTwoWay                  = prtInterpreterTwoWay_no;
    /* .16.5.1.(1 ~ 2) */
    prt_mib->_prtConsoleDisplayBufferIndex          = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtConsoleDisplayBufferText, sizeof(prt_mib->_prtConsoleDisplayBufferText),  "Unknown");
    /* .17.6.1.(1 ~ 5) */
    prt_mib->_prtConsoleLightIndex                  = NOT_SUPPORT_NUMBER;
    prt_mib->_prtConsoleOnTime                      = NOT_SUPPORT_NUMBER;
    prt_mib->_prtConsoleOffTime                     = NOT_SUPPORT_NUMBER;
    prt_mib->_prtConsoleColor                       = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtConsoleDescription,       sizeof(prt_mib->_prtConsoleDescription),        "Unknown");
    /* .18.1.1.(1 ~ 9) */
    prt_mib->_prtAlertIndex                         = NOT_SUPPORT_NUMBER;
    prt_mib->_prtAlertSeverityLevel                 = NOT_SUPPORT_NUMBER;
    prt_mib->_prtAlertTrainingLevel                 = NOT_SUPPORT_NUMBER;
    prt_mib->_prtAlertGroup                         = NOT_SUPPORT_NUMBER;
    prt_mib->_prtAlertGroupIndex                    = NOT_SUPPORT_NUMBER;
    prt_mib->_prtAlertLocation                      = NOT_SUPPORT_NUMBER;
    prt_mib->_prtAlertCode                          = NOT_SUPPORT_NUMBER;
    snprintf(prt_mib->_prtAlertDescription,         sizeof(prt_mib->_prtAlertDescription),          "Unknown");
    prt_mib->_prtAlertTime                          = NOT_SUPPORT_NUMBER;
}

/* PRINTER-PORT-MONITOR-MIB .*******.4.1.2699.1.2.1 */
static void snmp_init_ppm_mibs(void)
{
    PPM_MIB_S*  ppm_mib = &(s_snmp_ctx->snmp_map->ppm);

    /* .1.(2 ~ 3) */
    ppm_mib->_ppmGeneralNumberOfPrinters            = 1;
    ppm_mib->_ppmGeneralNumberOfPorts               = 1;
    /* .2.1.1.(1 ~ 6, 8) */
    ppm_mib->_ppmPrinterIndex                       = 1;
    snprintf(ppm_mib->_ppmPrinterName,              sizeof(ppm_mib->_ppmPrinterName),               "%s", s_snmp_ctx->pdt_name);
    snprintf(ppm_mib->_ppmPrinterIEEE1284DeviceId,  sizeof(ppm_mib->_ppmPrinterIEEE1284DeviceId),   "%s", s_snmp_ctx->ieee1284);
    ppm_mib->_ppmPrinterNumberOfPorts               = 1;
    ppm_mib->_ppmPrinterPreferredPortIndex          = 1;
    ppm_mib->_ppmPrinterHrDeviceIndex               = 1;
    ppm_mib->_ppmPrinterSnmpQueryEnabled            = 1;
    /* .3.1.1.(1 ~ 5, 7 ~ 9) */
    ppm_mib->_ppmPortIndex                          = 1;
    ppm_mib->_ppmPortEnabled                        = 1;
    snprintf(ppm_mib->_ppmPortName,                 sizeof(ppm_mib->_ppmPortName),                  "TCP-IP");
    snprintf(ppm_mib->_ppmPortServiceNameOrURI,     sizeof(ppm_mib->_ppmPortServiceNameOrURI),      "Unknown");
    ppm_mib->_ppmPortProtocolType                   = 11;
    ppm_mib->_ppmPortProtocolAltSourceEnabled       = 2;
    ppm_mib->_ppmPortPrtChannelIndex                = 1;
    ppm_mib->_ppmPortLprByteCountEnabled            = 2;
}

/**
 * @brief       The callback function of init the snmp protocol mib.
 * @return      No return.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void snmp_init_mibs(void)
{
    char        mfg_name[32];
    char        ser_name[32];
    char        pdt_sn[32];
    char        fw_ver[32];
    uint32_t    support_color;
    uint32_t    print_speed;

    netdata_get_mfg_name(DATA_MGR_OF(s_snmp_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_ser_name(DATA_MGR_OF(s_snmp_ctx), ser_name, sizeof(ser_name));
    netdata_get_pdt_sn  (DATA_MGR_OF(s_snmp_ctx), pdt_sn,   sizeof(pdt_sn));
    netdata_get_fw_ver  (DATA_MGR_OF(s_snmp_ctx), fw_ver,   sizeof(fw_ver));
    support_color = netdata_get_support_color(DATA_MGR_OF(s_snmp_ctx));
    print_speed   = netdata_get_print_speed(DATA_MGR_OF(s_snmp_ctx));

    SNMP_FLOCK_EX();

    snmp_init_priv_mibs(mfg_name, pdt_sn, support_color, print_speed);
    snmp_init_hres_mibs(ser_name);
    snmp_init_prt_mibs(mfg_name, pdt_sn, support_color);
    snmp_init_ppm_mibs();

    SNMP_FLOCK_UN();
}

/**
 * @brief       The callback function of update the snmp protocol mib.
 * @return      No return.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void snmp_update_mibs(void)
{
    SNMP_MAP_S*         snmp_map     = s_snmp_ctx->snmp_map;
    DYNAMIC_STATUS_S*   dynamic_info = &(snmp_map->_ptQueryDynamicFeature);
    STATIC_STATUS_S*    static_info  = &(snmp_map->_ptQueryStaticFeature);
    HRES_MIB_S*         hres_mib     = &(snmp_map->hres);
    PRT_MIB_S*          prt_mib      = &(snmp_map->prt);
    PPM_MIB_S*          ppm_mib      = &(snmp_map->ppm);
    uint32_t            language     = netdata_get_language(DATA_MGR_OF(s_snmp_ctx));
    uint32_t            country      = netdata_get_country(DATA_MGR_OF(s_snmp_ctx));

    netdata_get_static_feature (DATA_MGR_OF(s_snmp_ctx), &(snmp_map->_ptQueryStaticFeature),  sizeof(STATIC_STATUS_S));
    netdata_get_dynamic_feature(DATA_MGR_OF(s_snmp_ctx), &(snmp_map->_ptQueryDynamicFeature), sizeof(DYNAMIC_STATUS_S));
    netdata_get_audit_jobs_info(DATA_MGR_OF(s_snmp_ctx), &(snmp_map->_ptQueryAuditJobsInfo),  sizeof(AUDIT_JOBS_INFO_S));
    netdata_get_trc_600_info   (DATA_MGR_OF(s_snmp_ctx), &(snmp_map->_ptQueryTRC600Info),     sizeof(TRC_INFO_S));
    netdata_get_trc_1200_info  (DATA_MGR_OF(s_snmp_ctx), &(snmp_map->_ptQueryTRC1200Info),    sizeof(TRC_INFO_S));
    netdata_get_trc_2400_info  (DATA_MGR_OF(s_snmp_ctx), &(snmp_map->_ptQueryTRC2400Info),    sizeof(TRC_INFO_S));

    /* Pantum-MPS-MIB .*******.4.1.40093.10 */
    /* .1.1.(1,3,8,9) 版本信息可能滞后，需随静态特性刷新 */
    snprintf(snmp_map->_ptFirmwareVersion,                  sizeof(snmp_map->_ptFirmwareVersion),   "%s", static_info->firmware_ver);
    snprintf(snmp_map->_ptEngineVersion,                    sizeof(snmp_map->_ptEngineVersion),     "%s", static_info->engine_ver2); //静态特性-引擎版本号
    snprintf(snmp_map->_ptBootVersion,                      sizeof(snmp_map->_ptBootVersion),       "%s", static_info->boot_ver);
    snprintf(snmp_map->_ptKernelVersion,                    sizeof(snmp_map->_ptKernelVersion),     "%s", static_info->kernel_ver);

    /* .1.2.1 打印机状态*/
    snmp_map->_ptDeviceStatus                               = dynamic_info->printer_status;
    /* .1.3.1 打印机休眠时间 */
    snmp_map->_ptSleepTime                                  = netdata_get_sleep_time(DATA_MGR_OF(s_snmp_ctx));
    /* .1.3.3 作业超时时间 */
    snmp_map->_ptJobTimeOut                                 = netdata_get_io_timeout(DATA_MGR_OF(s_snmp_ctx));
    /* .1.3.4.1 省墨设置*/
    snmp_map->_ptInkSavingSetting                           = netdata_get_save_toner(DATA_MGR_OF(s_snmp_ctx));
    /* .1.3.4.2 语言设置 */
    snmp_map->_ptLanguageSetting                            = netdata_get_language(DATA_MGR_OF(s_snmp_ctx));
    /* .1.3.4.5 屏幕背光亮度 */
    snmp_map->_ptScreenBrightnessSetting                    = netdata_get_lcd_backlight(DATA_MGR_OF(s_snmp_ctx));
    /* .1.3.4.11 日期格式 */
    snmp_map->_ptDataFormat                                 = netdata_get_date_format(DATA_MGR_OF(s_snmp_ctx));
    /* .1.3.4.12 时间格式 */
    snmp_map->_ptTimeFormat                                 = netdata_get_time_format(DATA_MGR_OF(s_snmp_ctx)) - 1;
    /* .1.3.4.13 时间/日期 */
    time_t nowtm;
    struct tm *timeinfo;
    time(&nowtm);
    timeinfo = localtime(&nowtm);
    strftime(snmp_map->_ptDateTimeSetting, sizeof(snmp_map->_ptDateTimeSetting), "%H-%M-%S / %Y-%m-%d", timeinfo);
    /* .1.3.4.14 时区 */
    snmp_map->_ptTimezoneSetting                            = netdata_get_timezone(DATA_MGR_OF(s_snmp_ctx));
    /* .1.4.1.1 USBD连接标志 */
    snmp_map->_ptUsbConnectedFlag                           = (!!netdata_get_usb_dev_status(DATA_MGR_OF(s_snmp_ctx)));
    /* .1.4.2.1 网络连接标志 */
    snmp_map->_ptNWConnectedFlag                            = (!!netdata_get_netlink_flags(DATA_MGR_OF(s_snmp_ctx)));
    /* .*******.3.(1 ~ 4) */
    snmp_map->_ptTonerRemainK                               = netdata_get_tb_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K);                          ///< K粉盒余量
    snmp_map->_ptTonerRemainC                               = netdata_get_tb_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C);                          ///< C粉盒余量
    snmp_map->_ptTonerRemainM                               = netdata_get_tb_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M);                          ///< M粉盒余量
    snmp_map->_ptTonerRemainY                               = netdata_get_tb_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y);                          ///< Y粉盒余量
    /* .*******.4.(1 ~ 4) */
    snmp_map->_ptTonerMaximumK                              = netdata_get_tb_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K);                        ///< K粉盒最大打印页数
    snmp_map->_ptTonerMaximumC                              = netdata_get_tb_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C);                        ///< C粉盒最大打印页数
    snmp_map->_ptTonerMaximumM                              = netdata_get_tb_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M);                        ///< M粉盒最大打印页数
    snmp_map->_ptTonerMaximumY                              = netdata_get_tb_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y);                        ///< Y粉盒最大打印页数
    /* .*******.5.(1 ~ 4) */
    netdata_get_tb_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K, snmp_map->_ptTonerModelK,  sizeof(snmp_map->_ptTonerModelK));                       ///< K粉盒型号
    netdata_get_tb_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C, snmp_map->_ptTonerModelC,  sizeof(snmp_map->_ptTonerModelC));                       ///< C粉盒型号
    netdata_get_tb_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M, snmp_map->_ptTonerModelM,  sizeof(snmp_map->_ptTonerModelM));                       ///< M粉盒型号
    netdata_get_tb_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y, snmp_map->_ptTonerModelY,  sizeof(snmp_map->_ptTonerModelY));                       ///< Y粉盒型号
    /* .*******.6.(1 ~ 4) */
    netdata_get_tb_serial(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K, snmp_map->_ptTonerSerialK, sizeof(snmp_map->_ptTonerSerialK));                      ///< K粉盒序列号
    netdata_get_tb_serial(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C, snmp_map->_ptTonerSerialC, sizeof(snmp_map->_ptTonerSerialC));                      ///< C粉盒序列号
    netdata_get_tb_serial(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M, snmp_map->_ptTonerSerialM, sizeof(snmp_map->_ptTonerSerialM));                      ///< M粉盒序列号
    netdata_get_tb_serial(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y, snmp_map->_ptTonerSerialY, sizeof(snmp_map->_ptTonerSerialY));                      ///< Y粉盒序列号
    /* .*******.7.(1 ~ 4) */
    /* snmp:0=normal 1=missing 2=dismatch 3=empty 4=low */
    snmp_map->_ptTonerStateK                                = snmp_parser_consumption_state(MARKER_ID_K, 0);                                        ///< K粉盒状态
    snmp_map->_ptTonerStateC                                = snmp_parser_consumption_state(MARKER_ID_C, 0);                                        ///< C粉盒状态
    snmp_map->_ptTonerStateM                                = snmp_parser_consumption_state(MARKER_ID_M, 0);                                        ///< M粉盒状态
    snmp_map->_ptTonerStateY                                = snmp_parser_consumption_state(MARKER_ID_Y, 0);                                        ///< Y粉盒状态
    /* .*******.2.(1 ~ 4) */
    snmp_map->_ptCartridgeRemainK                           = netdata_get_dr_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K);                          ///< K硒鼓余量
    snmp_map->_ptCartridgeRemainC                           = netdata_get_dr_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C);                          ///< C硒鼓余量
    snmp_map->_ptCartridgeRemainM                           = netdata_get_dr_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M);                          ///< M硒鼓余量
    snmp_map->_ptCartridgeRemainY                           = netdata_get_dr_remain(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y);                          ///< Y硒鼓余量
    /* .*******.3.(1 ~ 4) */
    snmp_map->_ptCartridgeMaximumK                          = netdata_get_dr_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K);                        ///< K硒鼓最大打印页数
    snmp_map->_ptCartridgeMaximumC                          = netdata_get_dr_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C);                        ///< C硒鼓最大打印页数
    snmp_map->_ptCartridgeMaximumM                          = netdata_get_dr_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M);                        ///< M硒鼓最大打印页数
    snmp_map->_ptCartridgeMaximumY                          = netdata_get_dr_maxpages(DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y);                        ///< Y硒鼓最大打印页数
    /* .*******.4.(1 ~ 4) */
    netdata_get_dr_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_K, snmp_map->_ptCartridgeModelK,  sizeof(snmp_map->_ptCartridgeModelK));               ///< K硒鼓型号
    netdata_get_dr_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_C, snmp_map->_ptCartridgeModelC,  sizeof(snmp_map->_ptCartridgeModelC));               ///< C硒鼓型号
    netdata_get_dr_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_M, snmp_map->_ptCartridgeModelM,  sizeof(snmp_map->_ptCartridgeModelM));               ///< M硒鼓型号
    netdata_get_dr_model (DATA_MGR_OF(s_snmp_ctx), MARKER_ID_Y, snmp_map->_ptCartridgeModelY,  sizeof(snmp_map->_ptCartridgeModelY));               ///< Y硒鼓型号
    /* .*******.6.(1 ~ 4) */
    snmp_map->_ptCartridgeStateC                            = snmp_parser_consumption_state(MARKER_ID_C, 1);                                        ///< C硒鼓状态
    snmp_map->_ptCartridgeStateM                            = snmp_parser_consumption_state(MARKER_ID_M, 1);                                        ///< M硒鼓状态
    snmp_map->_ptCartridgeStateY                            = snmp_parser_consumption_state(MARKER_ID_Y, 1);                                        ///< Y硒鼓状态
    snmp_map->_ptCartridgeStateK                            = snmp_parser_consumption_state(MARKER_ID_K, 1);                                        ///< K硒鼓状态
    /* .1.5.4.1.1 */
    snmp_map->_ptWasterTonerState                           = snmp_parser_consumption_state(MARKER_ID_K, 2);                             ///< 废粉瓶状态

    /* .3.1.(1 ~ 31) */                                     // 除双面打印/复印取页数（sheet）, 其他均取面数（page）
    snmp_map->_ptEngineCounter                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_TOTAL);                ///< 引擎已打印页数
    snmp_map->_ptPrintCounterTotal                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_TOTAL);                ///< 总体打印计数
    snmp_map->_ptPrintCounterColor                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR);
    snmp_map->_ptPrintCounterMono                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO);
    snmp_map->_ptPrintCounterDuplex                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_DUPLEX);
    snmp_map->_ptPrintCounterA3                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A3)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A3);
    snmp_map->_ptPrintCounterA4                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A4)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A4);
    snmp_map->_ptPrintCounterA5                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A5)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A5);
    snmp_map->_ptPrintCounter11x17                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LEDGER)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LEDGER);      // 打印规格：11x17纸型统计为LEDGER纸型
    snmp_map->_ptPrintCounterJISB5                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_JIS_B5)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_JIS_B5);
    snmp_map->_ptPrintCounterLetter                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LETTER)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LETTER);
    snmp_map->_ptPrintCounterLegal                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LEGAL)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LEGAL);
    snmp_map->_ptPrintCounterFolio                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_FOOLSCAP3)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_FOOLSCAP3);   // 打印规格：Folio纸型会转化成FOOLSCAP3下发
    snmp_map->_ptPrintCounterExec                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_EXECUTIVE)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_EXECUTIVE);
    snmp_map->_ptPrintCounterStatement                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_INVOICE)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_INVOICE);     // 打印规格：Statement纸型会转化成INVOICE或INVOICE_L下发
    snmp_map->_ptPrintCounter8k                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_8K)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_8K);
    snmp_map->_ptPrintCounter16k                            = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_BIG16K)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_BIG16K);
    snmp_map->_ptPrintCounterUserdef                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_USERDEF)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_USERDEF);
    snmp_map->_ptPrintCounterA6                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A6)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A6);
    snmp_map->_ptPrintCounterColor_A3                       = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A3);
    snmp_map->_ptPrintCounterMono_A3                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A3);

    /* .3.2.(1 ~ 30) */
    snmp_map->_ptScanCounterTotal                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_TOTAL);
    snmp_map->_ptScanCounterColor                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR);
    snmp_map->_ptScanCounterMono                            = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO);
    snmp_map->_ptScanCounterDuplex                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_DUPLEX);
    snmp_map->_ptScanCounterA3                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A3)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A3);
    snmp_map->_ptScanCounterA4                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A4)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A4);
    snmp_map->_ptScanCounterA5                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A5)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A5);
    snmp_map->_ptScanCounterJISB5                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_JIS_B5)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_JIS_B5);
    snmp_map->_ptScanCounterLetter                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_LETTER)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_LETTER);
    snmp_map->_ptScanCounterLegal                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_LEGAL)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_LEGAL);
    snmp_map->_ptScanCounter8k                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_8K)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_8K);
    snmp_map->_ptScanCounter16k                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_BIG16K)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_BIG16K);
    snmp_map->_ptScanCounterA6                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A6)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A6);
    snmp_map->_ptScanCounterColor_A3                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A3);
    snmp_map->_ptScanCounterMono_A3                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A3);

    /* .3.3.(1 ~ 30) */
    snmp_map->_ptCopyCounterTotal                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_TOTAL);
    snmp_map->_ptCopyCounterColor                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR);
    snmp_map->_ptCopyCounterMono                            = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO);
    snmp_map->_ptCopyCounterDuplex                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_DUPLEX);
    snmp_map->_ptCopyCounterA3                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A3)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A3);
    snmp_map->_ptCopyCounterA4                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A4)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A4);
    snmp_map->_ptCopyCounterA5                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A5)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A5);
    snmp_map->_ptCopyCounterB4                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_B4)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_B4);
    snmp_map->_ptCopyCounterJISB5                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_JIS_B5)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_JIS_B5);
    snmp_map->_ptCopyCounter11x17                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LEDGER)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LEDGER);      // 打印规格：11x17纸型统计为LEDGER纸型
    snmp_map->_ptCopyCounterLetter                          = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LETTER)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LETTER);
    snmp_map->_ptCopyCounterLegal                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LEGAL)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LEGAL);
    snmp_map->_ptCopyCounterFolio                           = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_FOOLSCAP3)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_FOOLSCAP3);   // 打印规格：Folio纸型会转化成FOOLSCAP3下发
    snmp_map->_ptCopyCounterExec                            = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_EXECUTIVE)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_EXECUTIVE);
    snmp_map->_ptCopyCounterStatement                       = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_INVOICE)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_INVOICE);     // 打印规格：Statement纸型会转化成INVOICE或INVOICE_L下发
    snmp_map->_ptCopyCounter8k                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_8K)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_8K);
    snmp_map->_ptCopyCounter16k                             = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_BIG16K)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_BIG16K);
    snmp_map->_ptCopyCounterUserdef                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_USERDEF)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_USERDEF);
    snmp_map->_ptCopyCounterA6                              = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A6)
                                                            + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A6);
    snmp_map->_ptCopyCounterColor_A3                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A3);
    snmp_map->_ptCopyCounterMono_A3                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A3);


    /* .5.1.1 */
    netdata_get_hostname(DATA_MGR_OF(s_snmp_ctx),           snmp_map->_ptPrinterHostname,           sizeof(snmp_map->_ptPrinterHostname));

    /* .5.2 */
    if ( net_ifctl_is_running(IFACE_ETH) )
    {
        snmp_map->_ptNWConnectedFlag                        = 1;
        snmp_map->_ptWiredConnectStatus                     = 1;
        snmp_map->_ptWiredIpMode                            = netdata_get_ipv6_switch(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH);
        snmp_map->_ptWiredIpv6ManuEnableFlag                = netdata_get_ipv6_switch(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH);
        snmp_map->_ptDHCPEnable                             = netdata_get_ipv4_usedhcp(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_ETH);
        snmp_map->_ptDHCPv6Enable                           = netdata_get_ipv6_usedhcp(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_ETH);
        netdata_get_ipv4_addr(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH, snmp_map->_ptWiredIPAddress,           sizeof(snmp_map->_ptWiredIPAddress));
        netdata_get_ipv4_mask(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH, snmp_map->_ptWiredSubnetMask,          sizeof(snmp_map->_ptWiredSubnetMask));
        netdata_get_ipv4_gtwy(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH, snmp_map->_ptWiredGateway,             sizeof(snmp_map->_ptWiredGateway));
        netdata_get_ipv4_dns0(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH, snmp_map->_ptWiredDNSServerAddress,    sizeof(snmp_map->_ptWiredDNSServerAddress));
        netdata_get_ipv6_link(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_ETH, snmp_map->_ptWiredIpv6LinkLocalAddress, sizeof(snmp_map->_ptWiredIpv6LinkLocalAddress));

        snmp_map->_ptWiredInterfaceRate                     = netdata_get_wired_speed(DATA_MGR_OF(s_snmp_ctx));

    }

    /* .5.3 */
#if CONFIG_NET_WIFI
    if ( net_ifctl_is_running(IFACE_STA) )
    {
        snmp_map->_ptWi_FiEnable                            = 1;
        snmp_map->_ptNWConnectedFlag                        = 1;
        snmp_map->_ptWirelessStatus                         = 1;
        snmp_map->_ptWirelessIpMode                         = netdata_get_ipv6_switch(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_STA);
        snmp_map->_ptDHCPEnable                             = netdata_get_ipv4_usedhcp(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_STA);
        snmp_map->_ptDHCPv6Enable                           = netdata_get_ipv6_usedhcp(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_STA);
        netdata_get_ipv4_addr(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_STA, snmp_map->_ptWirelessIPAddress,        sizeof(snmp_map->_ptWirelessIPAddress));
        netdata_get_ipv4_mask(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_STA, snmp_map->_ptWirelessSubnetMask,       sizeof(snmp_map->_ptWirelessSubnetMask));
        netdata_get_ipv4_gtwy(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_STA, snmp_map->_ptWirelessGateway,          sizeof(snmp_map->_ptWirelessGateway));
        netdata_get_ipv4_dns0(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_STA, snmp_map->_ptWirelessDNSServerAddr,    sizeof(snmp_map->_ptWirelessDNSServerAddr));
        netdata_get_ipv6_link(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_STA, snmp_map->_ptWirelessIpv6LinkLocalAddress, sizeof(snmp_map->_ptWirelessIpv6LinkLocalAddress));
        netdata_get_sta_ssid (DATA_MGR_OF(s_snmp_ctx),                snmp_map->_ptWirelessSSID,             sizeof(snmp_map->_ptWirelessSSID));
    }
    else if ( net_ifctl_is_running(IFACE_WFD) )
    {
        snmp_map->_ptWi_FiEnable                            = 1;
        snmp_map->_ptNWConnectedFlag                        = 1;
        snmp_map->_ptWirelessStatus                         = 1;
        snmp_map->_ptWirelessIpMode                         = netdata_get_ipv6_switch(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_WFD);
        snmp_map->_ptDHCPEnable                             = netdata_get_ipv4_usedhcp(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_WFD);
        snmp_map->_ptDHCPv6Enable                           = netdata_get_ipv6_usedhcp(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_WFD);
        netdata_get_ipv4_addr(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_WFD, snmp_map->_ptWirelessIPAddress,        sizeof(snmp_map->_ptWirelessIPAddress));
        netdata_get_ipv4_mask(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_WFD, snmp_map->_ptWirelessSubnetMask,       sizeof(snmp_map->_ptWirelessSubnetMask));
        netdata_get_ipv4_gtwy(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_WFD, snmp_map->_ptWirelessGateway,          sizeof(snmp_map->_ptWirelessGateway));
        netdata_get_ipv4_dns0(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_WFD, snmp_map->_ptWirelessDNSServerAddr,    sizeof(snmp_map->_ptWirelessDNSServerAddr));
        netdata_get_ipv6_link(DATA_MGR_OF(s_snmp_ctx),  IFACE_ID_WFD, snmp_map->_ptWirelessIpv6LinkLocalAddress, sizeof(snmp_map->_ptWirelessIpv6LinkLocalAddress));
    }
#else
    snmp_map->_ptWirelessStatus                             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWirelessIpMode                             = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWirelessIpv6ManuEnableFlag                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWirelessType                               = NOT_SUPPORT_NUMBER;
    snmp_map->_ptEncryptionProtocol                         = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWEPIndex                                   = NOT_SUPPORT_NUMBER;
    snmp_map->_ptWi_FiEnable                                = NOT_SUPPORT_NUMBER;
    snprintf(snmp_map->_ptWirelessIPAddress,                sizeof(snmp_map->_ptWirelessIPAddress),             "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessSubnetMask,               sizeof(snmp_map->_ptWirelessSubnetMask),            "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessGateway,                  sizeof(snmp_map->_ptWirelessGateway),               "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessDNSServerAddr,            sizeof(snmp_map->_ptWirelessDNSServerAddr),         "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessIpv6LinkLocalAddress,     sizeof(snmp_map->_ptWirelessIpv6LinkLocalAddress),  "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessSSID,                     sizeof(snmp_map->_ptWirelessSSID),                  "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessWEPPassword,              sizeof(snmp_map->_ptWirelessWEPPassword),           "%s", "Unknown");
    snprintf(snmp_map->_ptWirelessWPAPSKPassword,           sizeof(snmp_map->_ptWirelessWPAPSKPassword),        "%s", "Unknown");

#endif

#if CONFIG_NET_LPD
    /* .******* */
    snmp_map->_ptLPDEnable                                  = netdata_get_lpd_switch(DATA_MGR_OF(s_snmp_ctx));
#endif  //  CONFIG_NET_LPD

#if CONFIG_NET_WSD
    /* .******* */
    snmp_map->_ptWSDEnable                                  = netdata_get_wsd_switch(DATA_MGR_OF(s_snmp_ctx));
#endif  //  CONFIG_NET_WSD

#if CONFIG_NET_SNMP
    /* .5.4.7.( 2 ~ 3 ) */
    snmp_map->_ptSNMPEnable                                 = netdata_get_snmp_switch(DATA_MGR_OF(s_snmp_ctx));
    if (0 == (netdata_get_snmp_v1v2c_switch(DATA_MGR_OF(s_snmp_ctx)) ||  netdata_get_snmp_v3_switch(DATA_MGR_OF(s_snmp_ctx))) )
    {
        snmp_map->_ptSNMPProtocolVer                        = 0;
    }
    else
    {
        if (0 == (netdata_get_snmp_v1v2c_switch(DATA_MGR_OF(s_snmp_ctx)) &&   netdata_get_snmp_v3_switch(DATA_MGR_OF(s_snmp_ctx))) )
        {
            if ( 0 == netdata_get_snmp_v3_switch(DATA_MGR_OF(s_snmp_ctx)) )
            {
                snmp_map->_ptSNMPProtocolVer                = 1;
            }
            else
            {
                snmp_map->_ptSNMPProtocolVer                = 2;
            }
        }
        else
        {
            snmp_map->_ptSNMPProtocolVer                    = 3;
        }
    }
#endif  // CONFIG_NET_SNMP

#if CONFIG_NET_SMTP
    /* .5.4.8.1 */
    snmp_map->_ptSMTPPort                                   = netdata_get_smtp_server_port(DATA_MGR_OF(s_snmp_ctx));
#endif  // CONFIG_NET_SMTP

    /* .5.4.10.3 */
    if ( 1 == netdata_get_smb_ntlm_auto(DATA_MGR_OF(s_snmp_ctx)) )
    {
        snmp_map->_ptSMBAuthentication                      = 0;
    }
    else if ( 1 == netdata_get_smb_ntlmv1_switch(DATA_MGR_OF(s_snmp_ctx)) )
    {
        snmp_map->_ptSMBAuthentication                      = 1;
    }
    else
    {
        snmp_map->_ptSMBAuthentication                      = NOT_SUPPORT_NUMBER;
    }

#if CONFIG_NET_SLP
    /* .******** */
    snmp_map->_ptSLPEnable                                  = netdata_get_slp_switch(DATA_MGR_OF(s_snmp_ctx));
#endif  // CONFIG_NET_SLP

#ifdef CONFIG_NET_IPPSRV
    /* .******** */
    snmp_map->_ptIPPEnable                                  = netdata_get_ipp_switch(DATA_MGR_OF(s_snmp_ctx));
#endif  // CONFIG_NET_IPPS

    /* .******** */
    snmp_map->_ptWiredIPv4DNSAllocationMethod               = (netdata_get_ipv4_autodns(DATA_MGR_OF(s_snmp_ctx), IFACE_ID_ETH) ?  0 : 1 );
    /* .******** */
    snmp_map->_ptBonjourEnable                              = netdata_get_bonjour_switch(DATA_MGR_OF(s_snmp_ctx));
    snmp_map->_ptmDNSEnable                                 = netdata_get_bonjour_switch(DATA_MGR_OF(s_snmp_ctx));

#if CONFIG_NET_RAWPRINT
    /* .5.4.19.( 1 ~ 2 ) */
    snmp_map->_pt9100Port                                   = netdata_get_rawprint_port(DATA_MGR_OF(s_snmp_ctx));
    snmp_map->_pt9100Enable                                 = netdata_get_rawprint_switch(DATA_MGR_OF(s_snmp_ctx));
    /* .5.4.37.( 1 ~ 2 ) */
    snmp_map->_ptRAWPort                                    = netdata_get_rawprint_port(DATA_MGR_OF(s_snmp_ctx));
    snmp_map->_ptRAWEnable                                  = netdata_get_rawprint_switch(DATA_MGR_OF(s_snmp_ctx));
#else
    snmp_map->_pt9100Port                                   = NOT_SUPPORT_NUMBER;
    snmp_map->_pt9100Enable                                 = NOT_SUPPORT_NUMBER;
    snmp_map->_ptRAWPort                                    = NOT_SUPPORT_NUMBER;
    snmp_map->_ptRAWEnable                                  = NOT_SUPPORT_NUMBER;
#endif  // CONFIG_NET_RAWPRINT

    /* .6.1 */
    snmp_map->_ptPrintStatAllSizeTotalAll                   = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_TOTAL);
    snmp_map->_ptPrintStatAllSizeTotalSingle                = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_TOTAL) - netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_DUPLEX);
    snmp_map->_ptPrintStatAllSizeTotalDuplex                = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_DUPLEX);
    snmp_map->_ptPrintStatAllSizeBlackTotal                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO);
    snmp_map->_ptPrintStatAllSizeColorAll                   = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR);
    snmp_map->_ptPrintStatUserdefTotalAll                   = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_USERDEF) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_USERDEF);
    snmp_map->_ptPrintStatUserdefBlackAll                   = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_USERDEF);
    snmp_map->_ptPrintStatUserdefColorAll                   = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_USERDEF);
    snmp_map->_ptPrintStatA3TotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A3) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A3);
    snmp_map->_ptPrintStatA3BlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A3);
    snmp_map->_ptPrintStatA3ColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A3);
    snmp_map->_ptPrintStatA4TotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A4) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A4);
    snmp_map->_ptPrintStatA4BlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A4);
    snmp_map->_ptPrintStatA4ColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A4);
    snmp_map->_ptPrintStatA5TotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A5) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A5);
    snmp_map->_ptPrintStatA5BlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A5);
    snmp_map->_ptPrintStatA5ColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A5);
    snmp_map->_ptPrintStatJISB5TotalAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_JIS_B5) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_JIS_B5);
    snmp_map->_ptPrintStatJISB5BlackAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_JIS_B5);
    snmp_map->_ptPrintStatJISB5ColorAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_JIS_B5);
    snmp_map->_ptPrintStat11x17TotalAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LEDGER) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LEDGER);       // 打印规格：11x17纸型统计为LEDGER纸型
    snmp_map->_ptPrintStat11x17BlackAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LEDGER);
    snmp_map->_ptPrintStat11x17ColorAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LEDGER);
    snmp_map->_ptPrintStatLetterTotalAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LETTER) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LETTER);
    snmp_map->_ptPrintStatLetterBlackAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LETTER);
    snmp_map->_ptPrintStatLetterColorAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LETTER);
    snmp_map->_ptPrintStatLegalTotalAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LEGAL) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LEGAL);
    snmp_map->_ptPrintStatLegalBlackAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_LEGAL);
    snmp_map->_ptPrintStatLegalColorAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_LEGAL);
    snmp_map->_ptPrintStatFilioTotalAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_FOOLSCAP3) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_FOOLSCAP3);  // 打印规格：Folio纸型会转化成FOOLSCAP3下发
    snmp_map->_ptPrintStatFilioBlackAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_FOOLSCAP3);
    snmp_map->_ptPrintStatFilioColorAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_FOOLSCAP3);
    snmp_map->_ptPrintStatExecutiveTotalAll                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_EXECUTIVE) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_EXECUTIVE);
    snmp_map->_ptPrintStatExecutiveBlackAll                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_EXECUTIVE);
    snmp_map->_ptPrintStatExecutiveColorAll                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_EXECUTIVE);
    snmp_map->_ptPrintStatStatementTotalAll                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_INVOICE) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_INVOICE);     // 打印规格：Statement纸型会转化成INVOICE或INVOICE_L下发
    snmp_map->_ptPrintStatStatementBlackAll                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_INVOICE);
    snmp_map->_ptPrintStatStatementColorAll                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_INVOICE);
    snmp_map->_ptPrintStat8KTotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_8K) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_8K);
    snmp_map->_ptPrintStat8KBlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_8K);
    snmp_map->_ptPrintStat8KColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_8K);
    snmp_map->_ptPrintStat16KTotalAll                       = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_BIG16K) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_BIG16K);
    snmp_map->_ptPrintStat16KBlackAll                       = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_BIG16K);
    snmp_map->_ptPrintStat16KColorAll                       = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_BIG16K);
    snmp_map->_ptPrintStatA6TotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A6) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A6);
    snmp_map->_ptPrintStatA6BlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_MONO_A6);
    snmp_map->_ptPrintStatA6ColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_COLOR_A6);
    /* .6.2 */
    snmp_map->_ptScanStatAllSizeTotalAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_TOTAL);
    snmp_map->_ptScanStatAllSizeTotalSingle                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_TOTAL) - netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_DUPLEX);
    snmp_map->_ptScanStatAllSizeTotalDuplex                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_DUPLEX);
    snmp_map->_ptScanStatAllSizeBlackTotal                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO);
    snmp_map->_ptScanStatAllSizeColorAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR);
    snmp_map->_ptScanStatA3TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A3) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A3);
    snmp_map->_ptScanStatA3BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A3);
    snmp_map->_ptScanStatA3ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A3);
    snmp_map->_ptScanStatA4TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A4) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A4);
    snmp_map->_ptScanStatA4BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A4);
    snmp_map->_ptScanStatA4ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A4);
    snmp_map->_ptScanStatA5TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A5) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A5);
    snmp_map->_ptScanStatA5BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A5);
    snmp_map->_ptScanStatA5ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A5);
    snmp_map->_ptScanStatJISB5TotalAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_JIS_B5) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_JIS_B5);
    snmp_map->_ptScanStatJISB5BlackAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_JIS_B5);
    snmp_map->_ptScanStatJISB5ColorAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_JIS_B5);
    snmp_map->_ptScanStatLetterTotalAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_LETTER) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_LETTER);
    snmp_map->_ptScanStatLetterBlackAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_LETTER);
    snmp_map->_ptScanStatLetterColorAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_LETTER);
    snmp_map->_ptScanStatLegalTotalAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_LEGAL) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_LEGAL);
    snmp_map->_ptScanStatLegalBlackAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_LEGAL);
    snmp_map->_ptScanStatLegalColorAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_LEGAL);
    snmp_map->_ptScanStat8KTotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_8K) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_8K);
    snmp_map->_ptScanStat8KBlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_8K);
    snmp_map->_ptScanStat8KColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_8K);
    snmp_map->_ptScanStat16KTotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_BIG16K) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_BIG16K);
    snmp_map->_ptScanStat16KBlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_BIG16K);
    snmp_map->_ptScanStat16KColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_BIG16K);
    snmp_map->_ptScanStatA6TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A6) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A6);
    snmp_map->_ptScanStatA6BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_MONO_A6);
    snmp_map->_ptScanStatA6ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_SCAN_COLOR_A6);
    /* .6.3 */
    snmp_map->_ptCopyStatAllSizeTotalAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_TOTAL);
    snmp_map->_ptCopyStatAllSizeTotalSingle                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_TOTAL) - netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_DUPLEX);
    snmp_map->_ptCopyStatAllSizeTotalDuplex                 = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_DUPLEX);
    snmp_map->_ptCopyStatAllSizeBlackTotal                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO);
    snmp_map->_ptCopyStatAllSizeColorAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR);
    snmp_map->_ptCopyStatUserdefTotalAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_USERDEF) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_USERDEF);
    snmp_map->_ptCopyStatUserdefBlackAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_USERDEF);
    snmp_map->_ptCopyStatUserdefColorAll                    = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_USERDEF);
    snmp_map->_ptCopyStatA3TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A3) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A3);
    snmp_map->_ptCopyStatA3BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A3);
    snmp_map->_ptCopyStatA3ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A3);
    snmp_map->_ptCopyStatA4TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A4) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A4);
    snmp_map->_ptCopyStatA4BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A4);
    snmp_map->_ptCopyStatA4ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A4);
    snmp_map->_ptCopyStatA5TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A5) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A5);
    snmp_map->_ptCopyStatA5BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A5);
    snmp_map->_ptCopyStatA5ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A5);
    snmp_map->_ptCopyStatJISB5TotalAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_JIS_B5) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_JIS_B5);
    snmp_map->_ptCopyStatJISB5BlackAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_JIS_B5);
    snmp_map->_ptCopyStatJISB5ColorAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_JIS_B5);
    snmp_map->_ptCopyStat11x17TotalAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LEDGER) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LEDGER);
    snmp_map->_ptCopyStat11x17BlackAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LEDGER);
    snmp_map->_ptCopyStat11x17ColorAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LEDGER);
    snmp_map->_ptCopyStatLetterTotalAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LETTER) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LETTER);
    snmp_map->_ptCopyStatLetterBlackAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LETTER);
    snmp_map->_ptCopyStatLetterColorAll                     = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LETTER);
    snmp_map->_ptCopyStatLegalTotalAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LEGAL) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LEGAL);
    snmp_map->_ptCopyStatLegalBlackAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_LEGAL);
    snmp_map->_ptCopyStatLegalColorAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_LEGAL);
    snmp_map->_ptCopyStatFilioTotalAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_FOOLSCAP3) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_FOOLSCAP3);   // 打印规格：Folio纸型会转化成FOOLSCAP3下发
    snmp_map->_ptCopyStatFilioBlackAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_FOOLSCAP3);
    snmp_map->_ptCopyStatFilioColorAll                      = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_FOOLSCAP3);
    snmp_map->_ptCopyStatExecutiveTotalAll                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_EXECUTIVE) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_EXECUTIVE);
    snmp_map->_ptCopyStatExecutiveBlackAll                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_EXECUTIVE);
    snmp_map->_ptCopyStatExecutiveColorAll                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_EXECUTIVE);
    snmp_map->_ptCopyStatStatementTotalAll                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_INVOICE) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_INVOICE);     // 打印规格：Statement纸型会转化成INVOICE或INVOICE_L下发
    snmp_map->_ptCopyStatStatementColorAll                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_INVOICE);
    snmp_map->_ptCopyStatStatementBlackAll                  = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_INVOICE);
    snmp_map->_ptCopyStat8KTotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_8K) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_8K);
    snmp_map->_ptCopyStat8KBlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_8K);
    snmp_map->_ptCopyStat8KColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_8K);
    snmp_map->_ptCopyStat16KTotalAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_BIG16K) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_BIG16K);
    snmp_map->_ptCopyStat16KBlackAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_BIG16K);
    snmp_map->_ptCopyStat16KColorAll                        = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_BIG16K);
    snmp_map->_ptCopyStatA6TotalAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A6) + netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A6);
    snmp_map->_ptCopyStatA6BlackAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_MONO_A6);
    snmp_map->_ptCopyStatA6ColorAll                         = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_COPY_COLOR_A6);
    /* PRINTER-PORT-MONITOR-MIB .*******.4.1.2699.1.2.1 */
    snprintf(ppm_mib->_ppmGeneralNaturalLanguage,           sizeof(ppm_mib->_ppmGeneralNaturalLanguage),    "%s", snmp_language_alias(language));   ///< .1.1
    netdata_get_snmp_v1_community(DATA_MGR_OF(s_snmp_ctx),  ppm_mib->_ppmPrinterSnmpCommunityName, sizeof(ppm_mib->_ppmPrinterSnmpCommunityName));  ///< .2.1.1.7
    ppm_mib->_ppmPortProtocolTargetPort                     = netdata_get_rawprint_port(DATA_MGR_OF(s_snmp_ctx));                                   ///< .3.1.1.6

    /* Printer-MIB .*******.2.1.43 */
    prt_mib->_prtGeneralCurrentLocalization                 = (int32_t)country;                                                                     ///< .5.1.1.2
    prt_mib->_prtConsoleLocalization                        = (int32_t)country;                                                                     ///< .********
    snprintf(prt_mib->_prtLocalizationLanguage,             sizeof(prt_mib->_prtLocalizationLanguage),      "%s", snmp_language_alias(language));   ///< .*******
    snprintf(prt_mib->_prtLocalizationCountry,              sizeof(prt_mib->_prtLocalizationCountry),       "%s", snmp_country_alias(country));     ///< .*******
    prt_mib->_prtMarkerLifeCount                            = netdata_get_page_counter(DATA_MGR_OF(s_snmp_ctx), PC_IDX_PRINT_TOTAL);                ///< .********

    /* HOST-RESOURCES-MIB .*******.2.1.25 */
    switch ( dynamic_info->printer_status )
    {
    case PRINTER_ST_INIT:
    case PRINTER_ST_WARMING:
        hres_mib->_hrPrinterStatus = hrPrinterStatus_warmup;
        break;
    case PRINTER_ST_SLEEP:
    case PRINTER_ST_READY:
        hres_mib->_hrPrinterStatus = hrPrinterStatus_idle;
        break;
    case PRINTER_ST_PROCESSING:
    case PRINTER_ST_PRINTING:
        hres_mib->_hrPrinterStatus = hrPrinterStatus_printing;
        break;
    default:
        hres_mib->_hrPrinterStatus = hrPrinterStatus_other;
        break;
    }
}

/**
 * @brief       The callback function of update the snmp protocol status.
 * @return      No return.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void snmp_update_stat(void)
{
    SNMP_MAP_S*         snmp_map = s_snmp_ctx->snmp_map;
    HRES_MIB_S*         hres_mib = &(snmp_map->hres);
    NETSTS_PACKET_S     packet;
    uint32_t            status_id;
    uint32_t            index;

    RETURN_IF(netsts_take_packet(&packet) < 0, NET_WARN);
    do
    {
        status_id = netsts_check_status_level(&packet, STATUS_ID_TYPE_FATAL);
        if ( status_id > 0 )
        {
            snmp_map->_ptAlertSeverityLevel = ptAlertSeverityLevel_fatal;
            hres_mib->_hrDeviceStatus       = hrDeviceStatus_down;
            break;
        }

        status_id = netsts_check_status_level(&packet, STATUS_ID_TYPE_ERROR);
        if ( status_id > 0 )
        {
            snmp_map->_ptAlertSeverityLevel = ptAlertSeverityLevel_error;
            hres_mib->_hrDeviceStatus       = hrDeviceStatus_down;
            break;
        }

        status_id = netsts_check_status_level(&packet, STATUS_ID_TYPE_WARN);
        if ( status_id > 0 )
        {
            snmp_map->_ptAlertSeverityLevel = ptAlertSeverityLevel_warning;
            hres_mib->_hrDeviceStatus       = hrDeviceStatus_warning;
            break;
        }

        snmp_map->_ptAlertSeverityLevel     = ptAlertSeverityLevel_default;
        hres_mib->_hrDeviceStatus           = hrDeviceStatus_running;
    }
    while ( 0 );

    switch ( status_id & STATUS_ID_MODULE_OFFSET )
    {
        case STATUS_ID_MODULE_PRINT: snmp_map->_ptAlertGroup = ptAlertGroup_print;   break;
        case STATUS_ID_MODULE_SCAN:  snmp_map->_ptAlertGroup = ptAlertGroup_scan;    break;
        case STATUS_ID_MODULE_FAX:   snmp_map->_ptAlertGroup = ptAlertGroup_fax;     break;
        default:                     snmp_map->_ptAlertGroup = ptAlertGroup_default; break;
    }

    switch ( status_id )
    {
        case STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN:         index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN:  index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN:  index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_PRINT_FNS_TOP_COVER_OPEN:             index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_PRINT_3RD_TRAY_COVER_OPEN:            index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_SCAN_ADF_COVER_OPEN:                  index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_SCAN_FB_COVER_OPEN:                   index = PT_ALERT_I_BACK_COVER_OPEN;                 break;
        case STATUS_E_PRINT_SIDE_DOOR_OPEN:                 index = PT_ALERT_I_RIGHT_COVER_OPEN;                break;
        case STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN:            index = PT_ALERT_I_FRONT_COVER_OPEN;                break;
        case STATUS_E_PRINT_FRONT_DOOR_OPEN:                index = PT_ALERT_I_FRONT_COVER_OPEN;                break;
        case STATUS_E_PRINT_C_TONER_EMPTY:                  index = PT_ALERT_I_TONER_EMPTY_C;                   break;
        case STATUS_E_PRINT_M_TONER_EMPTY:                  index = PT_ALERT_I_TONER_EMPTY_M;                   break;
        case STATUS_E_PRINT_Y_TONER_EMPTY:                  index = PT_ALERT_I_TONER_EMPTY_Y;                   break;
        case STATUS_E_PRINT_K_TONER_EMPTY:                  index = PT_ALERT_I_TONER_EMPTY_K;                   break;
        case STATUS_E_PRINT_C_TB_UNINSTALL:                 index = PT_ALERT_I_TONER_MISSING_C;                 break;
        case STATUS_E_PRINT_M_TB_UNINSTALL:                 index = PT_ALERT_I_TONER_MISSING_M;                 break;
        case STATUS_E_PRINT_Y_TB_UNINSTALL:                 index = PT_ALERT_I_TONER_MISSING_Y;                 break;
        case STATUS_E_PRINT_K_TB_UNINSTALL:                 index = PT_ALERT_I_TONER_MISSING_K;                 break;
        case STATUS_E_PRINT_C_TB_MISMATCH:                  index = PT_ALERT_I_TONER_MISMATCH_C;                break;
        case STATUS_E_PRINT_M_TB_MISMATCH:                  index = PT_ALERT_I_TONER_MISMATCH_M;                break;
        case STATUS_E_PRINT_Y_TB_MISMATCH:                  index = PT_ALERT_I_TONER_MISMATCH_Y;                break;
        case STATUS_E_PRINT_K_TB_MISMATCH:                  index = PT_ALERT_I_TONER_MISMATCH_K;                break;
        case STATUS_E_PRINT_C_DR_LIFE_STOP:                 index = PT_ALERT_I_DRUM_LIFE_END_C;                 break;
        case STATUS_E_PRINT_M_DR_LIFE_STOP:                 index = PT_ALERT_I_DRUM_LIFE_END_M;                 break;
        case STATUS_E_PRINT_Y_DR_LIFE_STOP:                 index = PT_ALERT_I_DRUM_LIFE_END_Y;                 break;
        case STATUS_E_PRINT_K_DR_LIFE_STOP:                 index = PT_ALERT_I_DRUM_LIFE_END_K;                 break;
        case STATUS_E_PRINT_C_DR_UNINSTALL:                 index = PT_ALERT_I_DRUM_MISSING_C;                  break;
        case STATUS_E_PRINT_M_DR_UNINSTALL:                 index = PT_ALERT_I_DRUM_MISSING_M;                  break;
        case STATUS_E_PRINT_Y_DR_UNINSTALL:                 index = PT_ALERT_I_DRUM_MISSING_Y;                  break;
        case STATUS_E_PRINT_K_DR_UNINSTALL:                 index = PT_ALERT_I_DRUM_MISSING_K;                  break;
        case STATUS_E_PRINT_C_DR_MISMATCH:                  index = PT_ALERT_I_DRUM_MISMATCH_C;                 break;
        case STATUS_E_PRINT_M_DR_MISMATCH:                  index = PT_ALERT_I_DRUM_MISMATCH_M;                 break;
        case STATUS_E_PRINT_Y_DR_MISMATCH:                  index = PT_ALERT_I_DRUM_MISMATCH_Y;                 break;
        case STATUS_E_PRINT_K_DR_MISMATCH:                  index = PT_ALERT_I_DRUM_MISMATCH_K;                 break;
        case STATUS_E_PRINT_WASTE_TONER_FULL:               index = PT_ALERT_I_WASTE_TONER_FULL;                break;
        case STATUS_E_PRINT_W_TB_UNINSTALL:                 index = PT_ALERT_I_WASTE_TONER_MISSING;             break;
        case STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL:     index = PT_ALERT_I_STANDARD_BIN_FULL;               break;
        case STATUS_E_PRINT_LCT_IN_OPEN:                    index = PT_ALERT_I_TRAY_MISSING_STANDARD_TRAY;      break;
        case STATUS_E_PRINT_TRAY_1_OPEN:                    index = PT_ALERT_I_TRAY_MISSING_STANDARD_TRAY;      break;
        case STATUS_E_PRINT_TRAY_2_OPEN:                    index = PT_ALERT_I_TRAY_MISSING_STANDARD_TRAY;      break;
        case STATUS_E_PRINT_TRAY_3_OPEN:                    index = PT_ALERT_I_TRAY_MISSING_OPTIONAL_TRAY_1;    break;
        case STATUS_E_PRINT_TRAY_4_OPEN:                    index = PT_ALERT_I_TRAY_MISSING_OPTIONAL_TRAY_2;    break;
        case STATUS_E_PRINT_LCT_IN_PAPER_EMPTY:             index = PT_ALERT_I_TRAY_EMPTY_STANDARD_TRAY;        break;
        case STATUS_E_PRINT_TRAY_1_PAPER_EMPTY:             index = PT_ALERT_I_TRAY_EMPTY_STANDARD_TRAY;        break;
        case STATUS_E_PRINT_TRAY_2_PAPER_EMPTY:             index = PT_ALERT_I_TRAY_EMPTY_STANDARD_TRAY;        break;
        case STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY:        index = PT_ALERT_I_TRAY_EMPTY_MULTI_FUNCTION_TRAY;  break;
        case STATUS_E_PRINT_TRAY_3_PAPER_EMPTY:             index = PT_ALERT_I_TRAY_EMPTY_OPTIONAL_TRAY_1;      break;
        case STATUS_E_PRINT_TRAY_4_PAPER_EMPTY:             index = PT_ALERT_I_TRAY_EMPTY_OPTIONAL_TRAY_2;      break;
        default:                                            index = PT_ALERT_I_DEFAULT;                         break;
    }

    switch ( index )
    {
        case PT_ALERT_I_FRONT_COVER_OPEN:
        case PT_ALERT_I_RIGHT_COVER_OPEN:
        case PT_ALERT_I_BACK_COVER_OPEN:
        case PT_ALERT_I_TRAY2_COVER_OPEN:
        case PT_ALERT_I_TRAY3_COVER_OPEN:
            hres_mib->_hrPrinterDetectedErrorState[1] |= hrPrinterDetectedErrorState_doorOpen;
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iCoverOpen;
            snmp_map->_ptAlertLocation   = ptAlertLocation_default;
            break;
        case PT_ALERT_I_TONER_MISSING_K:
        case PT_ALERT_I_TONER_MISSING_C:
        case PT_ALERT_I_TONER_MISSING_M:
        case PT_ALERT_I_TONER_MISSING_Y:
        case PT_ALERT_I_TONER_MISMATCH_K:
        case PT_ALERT_I_TONER_MISMATCH_C:
        case PT_ALERT_I_TONER_MISMATCH_M:
        case PT_ALERT_I_TONER_MISMATCH_Y:
        case PT_ALERT_I_TONER_EMPTY_K:
        case PT_ALERT_I_TONER_EMPTY_C:
        case PT_ALERT_I_TONER_EMPTY_M:
        case PT_ALERT_I_TONER_EMPTY_Y:
            hres_mib->_hrPrinterDetectedErrorState[1] |= hrPrinterDetectedErrorState_noToner;
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iCartridgeStatus;
            snmp_map->_ptAlertLocation   = ptAlertLocation_default;
            break;
        case PT_ALERT_I_DRUM_LIFE_END_C:
        case PT_ALERT_I_DRUM_LIFE_END_M:
        case PT_ALERT_I_DRUM_LIFE_END_Y:
        case PT_ALERT_I_DRUM_LIFE_END_K:
        case PT_ALERT_I_DRUM_MISSING_C:
        case PT_ALERT_I_DRUM_MISSING_M:
        case PT_ALERT_I_DRUM_MISSING_Y:
        case PT_ALERT_I_DRUM_MISSING_K:
        case PT_ALERT_I_DRUM_MISMATCH_C:
        case PT_ALERT_I_DRUM_MISMATCH_M:
        case PT_ALERT_I_DRUM_MISMATCH_Y:
        case PT_ALERT_I_DRUM_MISMATCH_K:
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iCS;
            snmp_map->_ptAlertLocation   = ptAlertLocation_default;
            break;
        case PT_ALERT_I_WASTE_TONER_FULL:
        case PT_ALERT_I_WASTE_TONER_MISSING:
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iWasterToner;
            snmp_map->_ptAlertLocation   = ptAlertLocation_default;
            break;
        case PT_ALERT_I_STANDARD_BIN_FULL:
            hres_mib->_hrPrinterDetectedErrorState[1] |= hrPrinterDetectedErrorState_outputFull;
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation   = ptAlertLocation_output;
            break;
        case PT_ALERT_I_TRAY_MISSING_STANDARD_TRAY:
        case PT_ALERT_I_TRAY_MISSING_OPTIONAL_TRAY_1:
        case PT_ALERT_I_TRAY_MISSING_OPTIONAL_TRAY_2:
            hres_mib->_hrPrinterDetectedErrorState[1] |= hrPrinterDetectedErrorState_inputTrayMissing;
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iTray;
            snmp_map->_ptAlertLocation   = ptAlertLocation_input;
            break;
        case PT_ALERT_I_TRAY_EMPTY_STANDARD_TRAY:
        case PT_ALERT_I_TRAY_EMPTY_MULTI_FUNCTION_TRAY:
        case PT_ALERT_I_TRAY_EMPTY_OPTIONAL_TRAY_1:
        case PT_ALERT_I_TRAY_EMPTY_OPTIONAL_TRAY_2:
            hres_mib->_hrPrinterDetectedErrorState[1] |= hrPrinterDetectedErrorState_markerSupplyMissing;
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_iPaperEmpty;
            snmp_map->_ptAlertLocation   = ptAlertLocation_input;
            break;
        default:
            memset(hres_mib->_hrPrinterDetectedErrorState, 0, sizeof(hres_mib->_hrPrinterDetectedErrorState));
            snmp_map->_ptAlertGroupIndex = ptAlertGroupIndex_default;
            snmp_map->_ptAlertLocation   = ptAlertLocation_default;
            break;
    }
    snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "%s", s_pt_alert_desc[index]);
    snmp_map->_ptAlertCode = s_pt_alert_code[index];

#if 0
    switch(s_system_status)
    {
        case STATUS_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY1_PAPER_SIZE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "INPUT TRAY2 PAPER PUTMISMATCH");
            break;
        case STATUS_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_MPTRAY_PAPER_SIZE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "INPUT TRAY1 PAPER PUTMISMATCH");
            break;
        case STATUS_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY1_PAPER_TYPE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TRAY1 PAPER TYPE MISMATCH");
            break;
        case STATUS_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_MPTRAY_PAPER_TYPE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "MPTRAY PAPER TYPE MISMATCH");
            break;
        case STATUS_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY_2_PAPER_SIZE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TRAY 2 PAPER SIZE MISMATCH");
            break;
        case STATUS_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY_3_PAPER_SIZE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TRAY 3 PAPER SIZE MISMATCH");
            break;
        case STATUS_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY_2_PAPER_TYPE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TRAY 2 PAPER TYPE MISMATCH");
            break;
        case STATUS_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY_3_PAPER_TYPE_MISMATCH;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TRAY 3 PAPER TYPE MISMATCH");
            break;
        case STATUS_E_PRINT_ENGINE_NOT_READY :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_engine;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_ENGINE_NOT_READY;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "ENGINE NOT READY");
            break;
        case STATUS_E_PRINT_ENGINE_COMMUNICATION_FAILED :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_engine;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_ENGINE_COMMUNICATION_FAILED;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "ENGINE COMMUNICATION FAILED");
            break;
        case STATUS_E_PRINT_VIDEO_DRIVE :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_engine;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_VIDEO_DRIVE;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "VIDEO DRIVE");
            break;
        case STATUS_E_PRINT_VIDEO_BANDING :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_engine;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_VIDEO_BANDING;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "VIDEO BANDING");
            break;
        case STATUS_E_PRINT_FUSER_MISSING :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_FUSER_MISSING;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "FUSER MISSING");
            break;
        case STATUS_E_PRINT_FUSER_LIFE_END :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_FUSER_LIFE_END;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "FUSER LIFE END");
            break;
        case STATUS_E_PRINT_JAM_NOT_FEED_STANDARD_TRAY :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_NOT_FEED_STANDARD_TRAY;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM NOT FEED STANDARD TRAY");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_NOT_FEED_MULTI_FUNCTION_TRAY :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_NOT_FEED_MULTI_FUNCTION_TRAY;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM NOT FEED MULTI FUNCTION TRAY");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_NOT_FEED_DUPLEX_UINT :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_NOT_FEED_DUPLEX_UINT;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM NOT FEED DUPLEX UINT");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_1 :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_NOT_FEED_OPTIONAL_TRAY_1;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM NOT FEED OPTIONAL TRAY 1");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_2 :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_NOT_FEED_OPTIONAL_TRAY_2;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM NOT FEED OPTIONAL TRAY 2");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_UNATTAIN_CALIBRATE_SENSOR :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_UNATTAIN_CALIBRATE_SENSOR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM UNATTAIN CALIBRATE SENSOR");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_UNATTAIN_OUTPUT_SENSOR :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_UNATTAIN_OUTPUT_SENSOR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM UNATTAIN OUTPUT SENSOR");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_1 :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_UNATTAIN_OPTIONAL_TRAY_1;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM UNATTAIN OPTIONAL TRAY 1");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_2 :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_UNATTAIN_OPTIONAL_TRAY_2;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM UNATTAIN OPTIONAL TRAY 2");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_UNATTAIN_FUSER :
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_UNATTAIN_FUSER;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM UNATTAIN FUSER");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_INPUT:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_UNATTAIN_DUPLEX_INPUT;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM UNATTAIN DUPLEX INPUT");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_STRANDED_CALIBRATE_SENSOR:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_STRANDED_CALIBRATE_SENSOR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM STRANDED CALIBRATE SENSOR");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_STRANDED_OUTPUT_SENSOR:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_STRANDED_OUTPUT_SENSOR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM STRANDED OUTPUT SENSOR");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_1:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_STRANDED_OPTIONAL_TRAY_1;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM STRANDED OPTIONAL TRAY 1");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_2:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_STRANDED_OPTIONAL_TRAY_2;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM STRANDED OPTIONAL TRAY 2");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_STRANDED_FUSER:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_STRANDED_FUSER;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "PRINT JAM STRANDED FUSER");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_STRANDED_DUPLEX_INPUT:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_STRANDED_DUPLEX_INPUT;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM STRANDED DUPLEX INPUT");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_FUSER:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_FUSER;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL FUSER");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_DUPLEX_INPUT;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL DUPLEX INPUT");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_OUTPUT_SENSOR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL OUTPUT SENSOR");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_CALIBRATE_SENSOR:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_CALIBRATE_SENSOR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL CALIBRATE SENSOR");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_OPTIONAL_TRAY_2;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL OPTIONAL TRAY 2");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_OPTIONAL_TRAY_1;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL OPTIONAL TRAY 1");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_MULTI_FUNCTION_TRAY;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL MULTI FUNCTION TRAY");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_input;
            snmp_map->_ptAlertCode             = PT_ALERT_I_JAM_RESIDUAL_STANDARD_TRAY;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "JAM RESIDUAL STANDARD TRAY");
            ErrState[0] |= hrPrinterDetectedErrorState_jammed;
            device_status = DEV_DOWN;
            break;
        case STATUS_E_PRINT_TIMEPRINT_LIST_FULL:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperJam;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TIMEPRINT_LIST_FULL;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TIMEPRINT LIST FULL");
            break;
        case STATUS_E_PRINT_TRAY_PAPER_SIZE_ERROR:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_iPaperPutMismatch;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_TRAY_PAPER_SIZE_ERROR;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "TRAY PAPER SIZE ERROR");
            break;
        case STATUS_E_PRINT_DRUM_CARRIER_EMPTY_C:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_DRUM_CARRIER_EMPTY_C;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "DRUM CARRIER EMPTY C");
            break;
        case STATUS_E_PRINT_DRUM_CARRIER_EMPTY_M:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_DRUM_CARRIER_EMPTY_M;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "DRUM CARRIER EMPTY M");
            break;
        case STATUS_E_PRINT_DRUM_CARRIER_EMPTY_Y:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_DRUM_CARRIER_EMPTY_Y;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "DRUM CARRIER EMPTY Y");
            break;
        case STATUS_E_PRINT_DRUM_CARRIER_EMPTY_K:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_DRUM_CARRIER_EMPTY_K;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "DRUM CARRIER EMPTY K");
            break;
        case STATUS_E_PRINT_REALAY_ROLLER_LIFE_END:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_REALAY_ROLLER_LIFE_END;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "REALAY ROLLER LIFE END");
            break;
        case STATUS_E_PRINT_ITU_NEAR_LIFE_END:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_other;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_ITU_NEAR_LIFE_END;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "ITU NEAR LIFE END");
            break;
        case STATUS_E_PRINT_ILLEGAL_PAGE:
            snmp_map->_ptAlertSeverityLevel    = ptAlertSeverityLevel_error;
            snmp_map->_ptAlertGroup            = ptAlertGroup_print;
            snmp_map->_ptAlertGroupIndex       = ptAlertGroupIndex_engine;
            snmp_map->_ptAlertLocation         = ptAlertLocation_default;
            snmp_map->_ptAlertCode             = PT_ALERT_I_ILLEGAL_PAGE;
            snprintf(snmp_map->_ptAlertdescription, sizeof(snmp_map->_ptAlertdescription), "ILLEGAL PAGE");
            break;
        default:
            break;
    }
#endif
}

/**
 * @brief       The callback function of set the SNMP reload flag bit.
 * @return      No return.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void snmp_update_reload_flag(void)
{
    RETURN_IF(s_snmp_ctx == NULL, NET_WARN);

    SNMP_MLOCK_EX();
    s_snmp_ctx->reload = 1;
    SNMP_MLOCK_UN();
}

/**
 * @brief       The callback function of update the return of the snmp protocol port.
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void snmp_update_port_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_SNMP )
    {
        snmp_update_reload_flag();
    }
}

/**
 * @brief       The callback function of reload slp protocol.
 * @param[in]   enabled    : SNMP protocol switch.
 * @return      No return.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void snmp_reload_snmpd(uint32_t enabled)
{
    char        location[256];
    char        contacts[256];
    char        v1comm[64];
    char        v2comm[64];
    char        v3comm[64];
    char        v3user[64];
    char        v3auth[32];
    char        v3priv[32];
    uint32_t    netlink;
    uint32_t    version;
    FILE*       stream;

    NET_DEBUG("SNMP reload snmpd");
    waiting_program_stop(SNMP_PID_FILE, 5, 500); /* snmpd退出，更新配置后重启 */

    version = netdata_get_snmp_version_flags(DATA_MGR_OF(s_snmp_ctx));
    netlink = netdata_get_netlink_flags(DATA_MGR_OF(s_snmp_ctx));
    if ( enabled == 0 || (version & SNMP_SUPPORT_MASK) == SNMP_SUPPORT_NONE || netlink == 0 )
    {
        NET_INFO("enabled(%u) version(0x%x) netlink(0x%x), SNMP daemon stopped", enabled, version, netlink);
        return;
    }

    netdata_get_snmp_v1_community(DATA_MGR_OF(s_snmp_ctx), v1comm, sizeof(v1comm));
    netdata_get_snmp_v2_community(DATA_MGR_OF(s_snmp_ctx), v2comm, sizeof(v2comm));
    netdata_get_snmp_v3_community(DATA_MGR_OF(s_snmp_ctx), v3comm, sizeof(v3comm));
    netdata_get_snmp_v3_user_name(DATA_MGR_OF(s_snmp_ctx), v3user, sizeof(v3user));
    netdata_get_snmp_v3_auth_pswd(DATA_MGR_OF(s_snmp_ctx), v3auth, sizeof(v3auth));
    netdata_get_snmp_v3_priv_pswd(DATA_MGR_OF(s_snmp_ctx), v3priv, sizeof(v3priv));
    netdata_get_location(DATA_MGR_OF(s_snmp_ctx), location, sizeof(location));
    netdata_get_contacts(DATA_MGR_OF(s_snmp_ctx), contacts, sizeof(contacts));

    NET_DEBUG("refresh snmpd for version(0x%x)", version);
    stream = pi_fopen(SNMP_CONF_PATH, "w");
    RETURN_IF(stream == NULL, NET_WARN);

    fprintf(stream, SNMP_CONF_HEAD, s_snmp_ctx->ieee1284, MEMBER_CHECK(location), MEMBER_CHECK(contacts), s_snmp_ctx->pdt_name);

    if ( (version & SNMP_SUPPORT_MASK) == SNMP_SUPPORT_ALL )
    {
        fprintf(stream, SNMPD_ALL_CONF, v1comm, v1comm, v2comm, v2comm, v3user, v3auth, v3priv, v2comm, v1comm, v3comm, v3user);
    }
    else if ( version & SNMP_SUPPORT_V3 )
    {
        fprintf(stream, SNMPD_ONLY_V3_CONF, v3user, v3auth, v3priv, v3user);
    }
    else
    {
        fprintf(stream, SNMPD_V1_V2_CONF, v1comm, v1comm, v2comm, v2comm, v2comm, v1comm);
    }
    pi_fclose(stream);

    pi_runcmd(NULL, 0, 1, "snmpd -V -M /usr/share/snmp/mibs -p %s -c %s", SNMP_PID_FILE, SNMP_CONF_PATH);
    if ( waiting_program_start(SNMP_PID_FILE, 5, 500) != 0 )
    {
        NET_WARN("start snmpd failed: %d<%s>", errno, strerror(errno));
        snmp_update_reload_flag();
    }
}

/**
 * @brief       The snmp handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void* snmp_thread_handler(void* arg)
{
    uint32_t    enabled;
    uint8_t     reload;
    time_t      then;
    /* 创建共享内存并初始化共享的MIB数据 */
    while ( s_snmp_ctx->snmp_map == NULL )
    {
        s_snmp_ctx->snmp_map = (SNMP_MAP_S *)snmp_ipc_shm(&s_snmp_ctx->shm_fd, SNMP_MAP_NAME, sizeof(SNMP_MAP_S));
        if ( s_snmp_ctx->snmp_map == NULL )
        {
            NET_INFO("create share memory(size %u) failed, retry after 2 minutes", sizeof(SNMP_MAP_S));
            pi_sleep(120);
        }
    }
    snmp_init_mibs();

    then = time(NULL);
    while (1)
    {
        SNMP_MLOCK_EX();
        reload = s_snmp_ctx->reload;
        s_snmp_ctx->reload = 0;
        SNMP_MLOCK_UN();

        enabled = netdata_get_snmp_switch(DATA_MGR_OF(s_snmp_ctx));
        if ( enabled ) /* 刷新MIB数据 */
        {
            SNMP_FLOCK_EX();

            snmp_update_mibs();
            snmp_update_stat();

            SNMP_FLOCK_UN();
        }
        if ( (time(NULL) - then) > SNMP_RESET_TIME )
        {
            snmp_update_reload_flag();
        }

        if ( reload ) /* 重载snmpd进程 */
        {
            snmp_reload_snmpd(enabled);
            then = time(NULL);
        }
        pi_msleep(1000);
    }

    return NULL;
}

int32_t snmp_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_snmp_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_snmp_ctx = (SNMP_CTX_S *)pi_zalloc(sizeof(SNMP_CTX_S));
    RETURN_VAL_IF(s_snmp_ctx == NULL, NET_WARN, -1);

    do
    {
        s_snmp_ctx->net_ctx = net_ctx;
        s_snmp_ctx->shm_fd  = -1;
        s_snmp_ctx->reload  = 1;

        netdata_get_ieee1284(DATA_MGR_OF(s_snmp_ctx), s_snmp_ctx->ieee1284, sizeof(s_snmp_ctx->ieee1284));
        netdata_get_pdt_name(DATA_MGR_OF(s_snmp_ctx), s_snmp_ctx->pdt_name, sizeof(s_snmp_ctx->pdt_name));

        BREAK_IF((s_snmp_ctx->srv_mtx = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        s_snmp_ctx->srv_tid = pi_thread_create(snmp_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "snmp_thread_handler");
        BREAK_IF(s_snmp_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("SNMP initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netport_observer(net_ctx, snmp_update_port_callback, NULL);
    }
    else
    {
        snmp_epilog();
    }
    return ret;
}

void snmp_epilog(void)
{
    pi_runcmd(NULL, 0, 0, "killall snmpd");
    if ( s_snmp_ctx != NULL )
    {
        if ( s_snmp_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_snmp_ctx->srv_tid);
        }
        if ( s_snmp_ctx->srv_mtx != INVALIDMTX )
        {
            pi_mutex_destroy(s_snmp_ctx->srv_mtx);
        }
        if ( s_snmp_ctx->shm_fd >= 0 )
        {
            close(s_snmp_ctx->shm_fd);
        }
        pi_free(s_snmp_ctx);
        s_snmp_ctx = NULL;
    }
}
/**
 *@}
 */
