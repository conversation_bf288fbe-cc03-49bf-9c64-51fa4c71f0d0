/*
 * @Author: your name
 * @Date: 2024-01-23 15:49:04
 * @LastEditTime: 2024-01-23 17:45:25
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \runtime\src\trans\transmission.c
 */
#include "trans/transmission.h"

#ifdef TRANS_MONITOR
#include "trans/monitor/monitor.h"
#include "basic/sys/sys.h"

uv_mutex_t mutex;

void transport_init()
{
    uv_mutex_init(&mutex);
}

int32_t transport_send(const uint8_t* buffer, uint16_t length)
{
    int32_t ret;

    uv_mutex_lock(&mutex);
    ret = send_to_human_monitor(buffer, length);
    uv_mutex_unlock(&mutex);

    return ret;
}

int32_t transport_receive(uint8_t* buffer, uint16_t* length, uint32_t ms)
{
    return receive_from_human_monitor(buffer, length, ms);
}

void trans_release()
{
}
#else
#include "PEDK_event.h"
void transport_init()
{
    transport_init_for_runtime();
}

int32_t transport_send(const uint8_t* buffer, uint16_t length)
{
    return transport_send_for_runtime(buffer, length);
}

int32_t transport_receive(uint8_t* buffer, uint16_t* length, uint32_t ms)
{
    LOG_D("trans","transport_receive。 length = %d\n",*length);
    return transport_receive_for_runtime(buffer, length, ms);
}

void trans_release()
{
    transport_release_for_runtime();
}
#endif