#include "pedk_storage.h"
#include <errno.h>
#include <unistd.h>
#include <stdlib.h>
#include <string.h>
#include "app_manager.h"

#define SUBPATH ("/storage/")
#define LOG(format, ...) printf("[pedk_storage] <%s:%d> "format, __func__, __LINE__, ##__VA_ARGS__);

struct jsc_func_sets {
    const char  *name;
    int          param_num;
    JSCFunction *func;
};

static JSValue js_delete_storage_file(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if (!ctx || argc != 1) {
        LOG("invalid param\n");
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    JSValue arg = argv[0];
    if (!JS_IsString(arg)) {
        LOG("invalid param 2\n");
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    const char *pathname = JS_ToCString(ctx, arg);
    int ret = remove(pathname);
    if (ret == -1) {
        LOG("%s remove failed cause %s\n", pathname, strerror(errno));
        JS_FreeCString(ctx, pathname);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    LOG("file %s delete success\n", pathname);
    JS_FreeCString(ctx, pathname);

    return JS_NewString(ctx, "EXIT_SUCCESS");
}

static JSValue js_check_storage_file_exist(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if (!ctx || argc != 1) {
        LOG("JSCtx is null or no param\n");
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    JSValue arg = argv[0];
    if (!JS_IsString(arg)) {
        LOG("param not string\n");
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    char  *rel_path, *app_path, *abs_path;
    int    ret;
    size_t size;

    app_path = GetAppWorkspace(ctx);
    if (!app_path) {
        LOG("get app work path failed\n");
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    rel_path = JS_ToCString(ctx, arg);
    if (strncmp(rel_path, SUBPATH, strlen(SUBPATH)) != 0) {
        LOG("rel_path=%s with subpath=%s not match\n", rel_path, SUBPATH);
        JS_FreeCString(ctx, rel_path);
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    size = strlen(app_path) + strlen(rel_path) + 1;
    abs_path = (char *)malloc(size);
    if (!abs_path) {
        LOG("malloc %d sz failed\n", size);
        JS_FreeCString(ctx, rel_path);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    strcpy(abs_path, app_path);
    strcpy(abs_path + strlen(app_path), rel_path);
    abs_path[size - 1] = '\0';

    ret = access(abs_path, F_OK);
    if (ret == -1) {
        LOG("file %s access failed cause %s\n", abs_path, strerror(errno));
        JS_FreeCString(ctx, rel_path);
        memset(abs_path, 0, size);
        free(abs_path);
        return JS_NewBool(ctx, 0);
    }


    LOG("file %s exist\n", abs_path);
    JS_FreeCString(ctx, rel_path);
    memset(abs_path, 0, size);
    free(abs_path);

    return JS_NewBool(ctx, 1);
}

static JSValue js_rename_storage_file(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    if (!ctx || argc != 3) {
        LOG("invalid param\n");
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    return JS_NewString(ctx, "EXIT_SUCCESS");
}

static const struct jsc_func_sets s_jsc_storage_func_sets[] = {
    {"delete_storage_file", 1, js_delete_storage_file},
    {"check_storage_file_exist", 1, js_check_storage_file_exist}
    //{"rename_storage_file", 3, js_rename_storage_file}
};

int js_storage_init(JSContext *ctx, JSValueConst global)
{
    int i, count;

    LOG("storage init ...\n");
    struct jsc_func_sets *sets = s_jsc_storage_func_sets;
    count = sizeof(s_jsc_storage_func_sets) / sizeof(struct jsc_func_sets);
    LOG("func sets count=%d\n", count);
    for (i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, sets[i].name,
            JS_NewCFunction(ctx, sets[i].func, sets[i].name, sets[i].param_num));
    }
    LOG("storage init ok...\n");
    return 0;
}
