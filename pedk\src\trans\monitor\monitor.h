/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file monitor.h
 * @addtogroup trans
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief monitor init 
 */
#ifndef _MONITOR_H_
#define _MONITOR_H_
#include "basic/config.h"
#include "basic/sys/sys.h"
#include <string.h>

/**
 * @brief   send to human monitor
 * @param[in] *buffer :buffer cache
 * @param[in] length :buffer length
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t send_to_human_monitor(const uint8_t *buffer, uint16_t length);
/**
 * @brief   receive from human monitor
 * @param[in] *buffer :buffer cache
 * @param[in] length :buffer length
 * @param[in] ms :outtime timers
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t receive_from_human_monitor(uint8_t *buffer, uint16_t *length, uint32_t ms);
#endif // _MONITOR_H_
/**
 * @}
 */
