/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd
 * @file license_auth_encryption.h
 * @ingroup license_authentication
 * @{
 * @modifier yangzikun
 * @date 2023-04-15
 * @brief Encryption-related functions and definitions for license authentication.
 */

#ifndef _LICENSE_AUTH_ENCRYPTION_H_
#define _LICENSE_AUTH_ENCRYPTION_H_

#include "license_auth_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/** @brief AES encryption algorithm used. */
#define LICENSE_AUTH_AES_ALGORITHM "-aes-256-cbc"

/** @brief Initialization Vector (IV) for AES encryption. */
#define LICENSE_AUTH_IV "b15f975b55f8515cc50b3c71b3116633"

/** @brief Base64 encoding table for use in encoding and decoding functions. */
extern const char *const LICENSE_AUTH_BASE64_TABLE;

/** @name Temporary File Definitions
 *  Temporary files created during encryption and decryption processes.
 *  @{
 */
#define LICENSE_AUTH_TMP_FILE LICENSE_AUTH_TEMP_LICENSE(ztmp_license)
#define LICENSE_AUTH_TMP_AES_KEY LICENSE_AUTH_TEMP_LICENSE(ztmp_license_AES.key)
#define LICENSE_AUTH_TMP_EN_MAIN LICENSE_AUTH_TEMP_LICENSE(ztmp_license_en_main.enc)
#define LICENSE_AUTH_TMP_DE_AES_KEY LICENSE_AUTH_TEMP_LICENSE(ztmp_license_de_AES.key)
#define LICENSE_AUTH_TMP_EN_AES_KEY LICENSE_AUTH_TEMP_LICENSE(ztmp_license_en_AES.enc)
#define LICENSE_AUTH_TMP_SIGNED LICENSE_AUTH_TEMP_LICENSE(ztmp_license_signed)
#define LICENSE_AUTH_TMP_DE_SIGN LICENSE_AUTH_TEMP_LICENSE(ztmp_license_de_sign.sig)
#define LICENSE_AUTH_TMP_PUBKEY LICENSE_AUTH_TEMP_LICENSE(ztmp_license_pubkey.pem)
#define LICENSE_AUTH_TMP_DE_BASE64 LICENSE_AUTH_TEMP_LICENSE(ztmp_license_de_base64)
/** @} */

/** @name Access Authentication Temporary Files
 *  Temporary files used during access authentication.
 *  @{
 */
#define LICENSE_AUTH_ACCESS_PUB_KEY_FILE LICENSE_AUTH_TEMP_ACCESS(ztmp_access_pubkey)
#define LICENSE_AUTH_ACCESS_SIGN_FILE LICENSE_AUTH_TEMP_ACCESS(ztmp_access_sign)
#define LICENSE_AUTH_ACCESS_SIGNED_FILE LICENSE_AUTH_TEMP_ACCESS(ztmp_access_signed)
#define LICENSE_AUTH_ACCESS_UNFORMAT_SIGNED LICENSE_AUTH_TEMP_ACCESS(ztmp_access_unformat_signed)
/** @} */

/**
 * @brief Verify the signature of a file.
 *
 * @param[in] input_file Path to the input file.
 * @param[in] signature_file Path to the signature file.
 * @param[in] public_key Path to the public key file.
 *
 * @return 0 on successful verification, -1 on failure.
 */
int32_t license_auth_verify_sign(const char *input_file, const char *signature_file, const char *public_key);

/**
 * @brief Decrypt a cJSON object.
 *
 * @param[in] input_json The input cJSON object to decrypt.
 * @param[in] private_key Path to the private key file. If NULL, symmetric decryption is used.
 *
 * @return The decrypted cJSON object (caller must free memory) or NULL on failure.
 */
cJSON *license_auth_decrypt_cjson(cJSON *input_json, const char *private_key);

/**
 * @brief Encode binary data to Base64 format.
 *
 * @param[in] binary_data Input binary data.
 * @param[in] binary_length Length of the binary data.
 * @param[out] base64_buffer Buffer to hold the Base64-encoded data.
 *
 * @return Length of the encoded Base64 data.
 */
int32_t license_auth_base64_encode(const unsigned char *binary_data, int32_t binary_length, char *base64_buffer);

/**
 * @brief Decode Base64 data to binary format.
 *
 * @param[in] base64_buffer Input Base64-encoded data.
 * @param[out] binary_data Buffer to hold the decoded binary data.
 *
 * @return Length of the decoded binary data.
 */
int32_t license_auth_base64_decode(char *base64_buffer, unsigned char *binary_data);


#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_ENCRYPTION_H_ */

/** @} */
