export function get_WiredNetIPv4Mode()
    {
        return get_wired_net_ipv4_mode();
    }
export function set_WiredNetIPv4Mode(mode)
    {
        return set_wired_net_ipv4_mode(mode);
    }
export function get_WiredNetIPv4Addr()
    {
        return get_wired_net_ipv4_addr();
    }
export function set_WiredNetIPv4Addr(ipv4_addr)
    {
        return set_wired_net_ipv4_addr(ipv4_addr);
    }
export function get_WiredNetIPv4Mask()
    {
        return get_wired_net_ipv4_mask();
    }
export function set_WiredNetIPv4Mask(ipv4_mask)
    {
        return set_wired_net_ipv4_mask(ipv4_mask);
    }
export function get_WiredNetIPv4GateWay()
    {
        return get_wired_net_ipv4_gateway();
    }
export function set_WiredNetIPv4GateWay(ipv4_gateway)
    {
        return set_wired_net_ipv4_gateway(ipv4_gateway);
    }
export function get_WiredMacAddrInfo()
    {
        return get_wired_mac_addr_info();
    }

class netsetting{
	getWiredNetIPv4Mode(){
		return get_WiredNetIPv4Mode();
	}
	setWiredNetIPv4Mode(mode){
		return set_WiredNetIPv4Mode(mode);
	}
	getWiredNetIPv4Addr(){
		return get_WiredNetIPv4Addr();
	}
	setWiredNetIPv4Addr(ipv4_addr){
		return set_WiredNetIPv4Addr(ipv4_addr);
	}
	getWiredNetIPv4Mask(){
		return get_WiredNetIPv4Mask();
	}
	setWiredNetIPv4Mask(ipv4_mask){
		return set_WiredNetIPv4Mask(ipv4_mask);
	}
	getWiredNetIPv4GateWay(){
		return get_WiredNetIPv4GateWay();
	}
	setWiredNetIPv4GateWay(ipv4_gateway){
		return set_WiredNetIPv4GateWay(ipv4_gateway);
	}

	getWiredMacAddrInfo(){
		return get_WiredMacAddrInfo();
	}
}

globalThis.pedk.device.setting.netsetting                     = netsetting
globalThis.pedk.device.setting.netsetting.getWiredNetIPv4Mode = get_WiredNetIPv4Mode
globalThis.pedk.device.setting.netsetting.setWiredNetIPv4Mode = set_WiredNetIPv4Mode
globalThis.pedk.device.setting.netsetting.getWiredNetIPv4Addr = get_WiredNetIPv4Addr
globalThis.pedk.device.setting.netsetting.setWiredNetIPv4Addr = set_WiredNetIPv4Addr
globalThis.pedk.device.setting.netsetting.getWiredNetIPv4Mask = get_WiredNetIPv4Mask
globalThis.pedk.device.setting.netsetting.setWiredNetIPv4Mask = set_WiredNetIPv4Mask
globalThis.pedk.device.setting.netsetting.getWiredNetIPv4GateWay = get_WiredNetIPv4GateWay
globalThis.pedk.device.setting.netsetting.setWiredNetIPv4GateWay = set_WiredNetIPv4GateWay
globalThis.pedk.device.setting.netsetting.getWiredMacAddrInfo = get_WiredMacAddrInfo
