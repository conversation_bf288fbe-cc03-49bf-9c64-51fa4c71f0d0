#include "pesf_device_net_setting.h"
#include "PEDK_event.h"

#include <quickjs.h>

/* 自定义原生C函数 */
//static void xxx()
//{
//    return;
//}

/*
    定义 QuickJS C 函数
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
//JSValue js_xxx(JSContext *ctx, JSValueConst this_val,
//                               int argc, JSValueConst *argv)
//{
//    xxx();
//    return JS_NewString(ctx, "OK");
//}

#define countof(x) (sizeof(x) / sizeof((x)[0]))

JSValue js_get_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char receive_data[10];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("js_get_wired_net_ipv4_mode\n");
    memset(receive_data, 0, sizeof(receive_data));
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE, &respond, receive_data, &receive_cnt, 3);
    printf("get ipv4_mode receive_data %s,respond %d\n", receive_data,respond);
    uint32_t ipv4_mode = *(uint32_t *)receive_data;
    printf("gett---> ipv4_mode %ud\n", ipv4_mode);

    if ( respond < 0 )
    {
        return JS_NewInt32(ctx, -1);
    }
    else
    {
        return JS_NewInt32(ctx, ipv4_mode);
    }
}

JSValue js_set_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int32_t respond = -1;

    printf("js_set_wired_net_ipv4_mode\n");
    if (!JS_IsNumber(argv[0]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }
    uint32_t ipv4_mode = 0;
    JS_ToInt32(ctx, &ipv4_mode, argv[0]);
    printf("set ipv4_mode %ud\n", ipv4_mode);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE, 0, sizeof(ipv4_mode),  (const unsigned char *)&ipv4_mode);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE, &respond, NULL, NULL, 3);

    return JS_NewString(ctx, "EXIT_SUCCESS");
}

JSValue js_get_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[64];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("js_get_wired_net_ipv4_addr\n");

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR, &respond, receive_data, &receive_cnt, 3);

    printf("get ipv4_addr %s\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}
//-----
JSValue js_set_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int32_t respond = -1;

    printf("js_set_wired_net_ipv4_addr\n");
	if (!JS_IsString(argv[0]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }
    char *jscstr = JS_ToCString(ctx, argv[0]);
    jscstr[strlen(jscstr)] = '\0';

    printf("set ipv4_addr %s,len %d\n", jscstr, strlen(jscstr));

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR, 0, strlen(jscstr) + 1, (const unsigned char *)jscstr);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR, &respond, NULL, NULL, 3);

    return JS_NewString(ctx, "EXIT_SUCCESS");
}

JSValue js_get_wired_net_ipv4_mask(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[64];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("js_get_wired_net_ipv4_mask\n");

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK, &respond, receive_data, &receive_cnt, 3);

    printf("get ipv4_mask %s\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}
//-----
JSValue js_set_wired_net_ipv4_mask(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int32_t respond = -1;

    printf("js_set_wired_net_ipv4_mask\n");
	if (!JS_IsString(argv[0]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }
    char *jscstr = JS_ToCString(ctx, argv[0]);
    jscstr[strlen(jscstr)] = '\0';

    printf("set ipv4_mask %s\n", jscstr);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MASK, 0, strlen(jscstr) + 1, (const unsigned char *)jscstr);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MASK, &respond, NULL, NULL, 3);


    return JS_NewString(ctx, "EXIT_SUCCESS");
}


JSValue js_get_wired_net_ipv4_gateway(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[64];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("js_get_wired_net_ipv4_gateway\n");

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY, &respond, receive_data, &receive_cnt, 3);

    printf("get ipv4_gateway %s\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}
//-----
JSValue js_set_wired_net_ipv4_gateway(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int32_t respond = -1;

    printf("js_set_wired_net_ipv4_gateway\n");
	if (!JS_IsString(argv[0]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }
    char *jscstr = JS_ToCString(ctx, argv[0]);
    jscstr[strlen(jscstr)] = '\0';

    printf("set ipv4_gateway %s\n", jscstr);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_GATEWAY, 0, strlen(jscstr) + 1, (const unsigned char *)jscstr);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_GATEWAY, &respond, NULL, NULL, 3);

    return JS_NewString(ctx, "EXIT_SUCCESS");
}

JSValue js_get_wired_mac_addr_info(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[64];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("js_get_wired_mac_addr_info\n");

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO, &respond, receive_data, &receive_cnt, 3);

    printf("get mac addr info %s\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_setting_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    /* setting */

    {"get_wired_net_ipv4_mode",     0,  js_get_wired_net_ipv4_mode},
    {"set_wired_net_ipv4_mode",     1,  js_set_wired_net_ipv4_mode},
    {"get_wired_net_ipv4_addr",     0,  js_get_wired_net_ipv4_addr},
    {"set_wired_net_ipv4_addr",     1,  js_set_wired_net_ipv4_addr},
    {"get_wired_net_ipv4_mask",     0,  js_get_wired_net_ipv4_mask},
    {"set_wired_net_ipv4_mask",     1,  js_set_wired_net_ipv4_mask},
    {"get_wired_net_ipv4_gateway",  0,  js_get_wired_net_ipv4_gateway},
    {"set_wired_net_ipv4_gateway",  1,  js_set_wired_net_ipv4_gateway},

    {"get_wired_mac_addr_info",     0,  js_get_wired_mac_addr_info},

};

const JSCFunctionList* device_net_setting_JSCFunctionList(int *length)
{
    *length = countof(pesf_setting_funcs);
    return pesf_setting_funcs;
}

int js_device_net_setting_init(JSContext *ctx, JSValueConst global)
{
   printf("*********start device net setting module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = device_net_setting_JSCFunctionList(&count);
   printf("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   printf("*********start device net setting setting init end**********\n");
   return 0;
}

