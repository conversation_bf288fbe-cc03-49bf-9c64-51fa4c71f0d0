/**
 * HttpsConfigParam class definition
 */
class HttpsConfigParam {
    constructor() {
        this.verify_certificate = 1; // Default: 1 (verify certificate)
        this.verify_host_mode = 2;   // Default: 2 (strict verification)
        this.client_cert_path = ""; // Default: empty
        this.client_key_path = "";  // Default: empty
        this.key_password = "";     // Default: empty
    }
}



var global =
  (typeof globalThis !== 'undefined' && globalThis) ||
  (typeof self !== 'undefined' && self) ||
  (typeof global !== 'undefined' && global);

var support = {
  searchParams: 'URLSearchParams' in global,
  iterable: 'Symbol' in global && 'iterator' in Symbol,
  blob:
	'Blob' in global &&
	(function() {
	  try {
		new Blob();
		return true
	  } catch (e) {
		return false
	  }
	})(),
  formData: 'FormData' in global,
  arrayBuffer: 'ArrayBuffer' in global
};


function normalizeName(name) {
  if (typeof name !== 'string') {
	name = String(name);
  }
  if (/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(name) || name === '') {
	throw new TypeError('Invalid character in header field name: "' + name + '"')
  }
  return name.toLowerCase()
}

function normalizeValue(value) {
  if (typeof value !== 'string') {
	value = String(value);
  }
  return value
}

// Build a destructive iterator for the value list
function iteratorFor(items) {
  var iterator = {
	next: function() {
	  var value = items.shift();
	  return {done: value === undefined, value: value}
	}
  };

  if (support.iterable) {
	iterator[Symbol.iterator] = function() {
	  return iterator
	};
  }

  return iterator
}

function Headers(headers, value) {
  this.map = {};

  if (headers instanceof Headers) {
	headers.forEach(function(value, name) {
	  this.append(name, value);
	}, this);
  } else if (Array.isArray(headers)) {
	headers.forEach(function(header) {
	  this.append(header[0], header[1]);
	}, this);
  } else if (headers) {
	  if (typeof headers === 'string' && value) {
		  this.append(headers, value);
	  } else {
		  Object.getOwnPropertyNames(headers).forEach(function (name) {
			  this.append(name, headers[name]);
		  }, this);
	  }
  }
}

Headers.prototype.append = function(name, value) {
  name = normalizeName(name);
  value = normalizeValue(value);
  var oldValue = this.map[name];
  this.map[name] = oldValue ? oldValue + ', ' + value : value;
};

Headers.prototype['delete'] = function(name) {
  delete this.map[normalizeName(name)];
};

Headers.prototype.get = function(name) {
  name = normalizeName(name);
  return this.has(name) ? this.map[name] : null
};

Headers.prototype.getSetCookie = function() {
	var str = this.get('Set-Cookie');
	return str ? str.split(',') : [];
};

Headers.prototype.has = function(name) {
  return this.map.hasOwnProperty(normalizeName(name))
};

Headers.prototype.set = function(name, value) {
  this.map[normalizeName(name)] = normalizeValue(value);
};

Headers.prototype.forEach = function(callback, thisArg) {
  for (var name in this.map) {
	if (this.map.hasOwnProperty(name)) {
	  callback.call(thisArg, this.map[name], name, this);
	}
  }
};

Headers.prototype.getAllKeys = function() {
  var items = [];
  this.forEach(function(value, name) {
	items.push(name);
  });
  return iteratorFor(items)
};

Headers.prototype.getAllValues = function() {
  var items = [];
  this.forEach(function(value) {
	items.push(value);
  });
  return iteratorFor(items)
};

Headers.prototype.entries = function() {
  var items = [];
  this.forEach(function(value, name) {
	items.push([name, value]);
  });
  return iteratorFor(items)
};

if (support.iterable) {
  Headers.prototype[Symbol.iterator] = Headers.prototype.entries;
}

function RequestBody(body) {
	if (!body) {
	  this._bodyText = '';
	} else if (typeof body === 'string') {
	  this._bodyText = body;
	}  else {
	  this._bodyJsonText = JSON.stringify(body);
	}
}

// HTTP methods whose capitalization should be normalized
var methods = ['DELETE', 'GET', 'HEAD', 'OPTIONS', 'POST', 'PUT'];

function normalizeMethod(method) {
  var upcased = method.toUpperCase();
  return methods.indexOf(upcased) > -1 ? upcased : method
}

function Request(url, method, headers, body) {
	this.timeout = 0;
	this.url = String(url);
	this.method = normalizeMethod(method || this.method || 'GET');

        if ((this.method === 'GET' || this.method === 'HEAD') && body) {
            this.method = 'POST';
        }

	if (headers || !this.headers) {
		this.headers = new Headers(headers);
	}

	if (body || !this.body) {
		if (body instanceof RequestBody) {
			if ('_bodyJsonText' in body) {
				this.body = body._bodyJsonText;
				if (!this.headers.get('content-type')) {
					this.headers.set('Content-Type', 'application/json');
				}
			} else {
				this.body = body._bodyText;
			}
		} else {
			this.body = body;
		}
	}

}

Request.prototype.setTimeout = function(ms) {
	if (typeof ms !== 'number') {
		throw new TypeError("invalid parameter");
	}
	if (ms < 0) ms = 0;
	this.timeout = ms;
}

Request.prototype.getTimeout = function() {
	return this.timeout;
}


function Response(code, headers, body) {
	this.code	 = code;
	this.headers = headers;
	this.body	 = body;
};

function parseHeaders(rawHeaders) {
  var headers = new Headers();
  // Replace instances of \r\n and \n followed by at least one space or horizontal tab with a space
  // https://tools.ietf.org/html/rfc7230#section-3.2
  var preProcessedHeaders = rawHeaders.replace(/\r?\n[\t ]+/g, ' ');

  preProcessedHeaders
	.split('\r')
	.map(function(header) {
	  return header.indexOf('\n') === 0 ? header.substr(1, header.length) : header
	})
	.forEach(function(line) {
	  var parts = line.split(':');
	  var key = parts.shift().trim();
	  if (key) {
		var value = parts.join(':').trim();
		headers.append(key, value);
	  }
	});
  return headers
}

function fetchData(request, callback) {
	if (!(request instanceof Request)) {
		throw new TypeError("invalid parameter");
	}
	if (typeof callback !== 'function') {
		throw TypeError("not a function");
	}

	//console.log('JS:DEBUG: fetchData start ' + request.url);
	var xhr = new XMLHttpRequest();
	if (request.getTimeout() > 0)
		xhr.timeout = request.getTimeout();

	xhr.onload = function() {
		let headers = parseHeaders(xhr.getAllResponseHeaders() || '');
		let body = 'response' in xhr ? xhr.response : xhr.responseText;
		//var url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');
	  	//console.log('JS:DEBUG: onload ' + url + ", " + xhr.statusText);
		var statusText = String(xhr.statusText);
		var respObj = new Response(xhr.status, headers, body);
	  	setTimeout(function() {
		callback(statusText, respObj);
		xhr = null;
		//console.log('JS:DEBUG: onload end ' + statusText);
		}, 1);
	};

	xhr.onerror = function() {
	  setTimeout(function() {
		callback("Connection Refused", new Response(-2));
	  }, 0);
	};

	xhr.ontimeout = function() {
	  setTimeout(function() {
		callback("No Internet/Timeout", new Response(-1));
	  }, 0);
	};

	xhr.open(request.method, request.url, true);

	request.headers.forEach(function(value, name) {
		xhr.setRequestHeader(name, value);
	});
	if ('responseType' in xhr) {
		if (
			support.arrayBuffer &&
			request.headers.get('Content-Type') &&
			request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1
		)
		    {
			    xhr.responseType = 'arraybuffer';
            }
			else if (
                request.headers.get('Content-Type') &&
                request.headers.get('Content-Type').indexOf('application/json') !== -1
            )
            {
                xhr.responseType = 'json';
			}
	}

	xhr.send(typeof request.body === 'undefined' ? null : request.body);

}

function wget(url, save_path, callback, overwrite) {
        if (!(typeof url === 'string') || !(typeof save_path === 'string')) {
            throw new TypeError("invalid parameter");
        }
        if (typeof callback !== 'function') {
            throw TypeError("not a function");
        }
        if ( !( save_path.startsWith("/temp/") || save_path.startsWith("/storage/") )
            || save_path.includes("./") || save_path.includes("..") ) {
            throw TypeError("not allow other path");
        }
        if ( save_path === "/temp/" || save_path === "/storage/" ) {
            throw TypeError("need a file name");
        }

        var xhr = new XMLHttpRequest();
        xhr.timeout = 5*60*1000;
        xhr.userSavePath = save_path;
        xhr.overwrite = overwrite;

        xhr.onload = function() {
            let body = xhr.response;
            //var url = 'responseURL' in xhr ? xhr.responseURL : options.headers.get('X-Request-URL');
            //console.log('JS:DEBUG: onload ' + url + ", " + xhr.statusText);
            var statusText = body ? String(xhr.statusText) : "Download failed";
            setTimeout(function() {
                callback(statusText, save_path);
                xhr = null;
                //console.log('JS:DEBUG: onload end ' + statusText);
            }, 1);
        };

        xhr.onerror = function() {
            setTimeout(function() {
                callback("Connection Refused", save_path);
            }, 0);
        };

        xhr.ontimeout = function() {
            setTimeout(function() {
                callback("No Internet/Timeout", save_path);
            }, 0);
        };

        xhr.open("GET", url, true);
        xhr.send(null);
    }

 function receiveData(request) {

    if (!(request instanceof Request)) {
        console.log("invalid parameter");
    }
    var response_data = new Response( "200", request.header, "receive Data is null!");

    return response_data;

 }

 function updatereceivedata(obj,respond,data) {

	console.log("[updatereceivedata] data:", data);
	var newData =  eval("(" + data + ")");
	var headerData = new Headers(eval("(" + newData.header + ")"));
	var newRequest = new Request(newData.url, newData.method, headerData, newData.body);
	let receiveData = globalThis.pedk.net.http.receiveData

	if(receiveData !== undefined && receiveData !== null )
	{
		var response_data = receiveData(newRequest);
	}
	if(response_data.headers !== undefined) {
		var response_header = JSON.stringify(response_data.headers.map);
		response_data = new Response( response_data.code, response_header, response_data.body)
	}
	var ResponseJson = JSON.stringify(response_data)
	console.log("[receive Json] ResponseJson:", ResponseJson);

	receiveData_Response(ResponseJson);
 }

/**
 * Set HTTPS configuration parameters
 * @param {HttpsConfigParam} https_config_params - HTTPS configuration parameters
 * @returns {string} Execution result
 */
function setHttpsConfigParams(https_config_params) {
    // 调用 C 实现绑定的 native 函数
    const result = set_https_config_params(JSON.stringify(https_config_params));
    return result;
}
/**
 * Get HTTPS configuration parameters
 * @returns {HttpsConfigParam | string} HTTPS configuration parameters or execution result
 */
function getHttpsConfigParams() {
    const result = get_https_config_params();

    try {
        return JSON.parse(result); // 尝试将结果解析为 JSON 格式
    } catch (e) {
        return result; // 如果解析失败，返回原始字符串
    }
}

let instance = new globalThis.pedk.ObserverManager();
instance.addListeners(this, updatereceivedata, globalThis.pedk.Module_Main.MSG_MODULE_HTTP, globalThis.pedk.Module_Sub.MSG_HTTP_SUB);

    if (undefined === globalThis.pedk.net.wget) {
        globalThis.pedk.net.wget = {};
    }
	globalThis.pedk.net.wget.wget = wget;

//if (undefined === globalThis.pesf.net) {
//	globalThis.pesf.net = {};
//}
globalThis.pedk.net.http = {};

globalThis.pedk.net.http.Headers = Headers;
globalThis.pedk.net.http.Request = Request;
globalThis.pedk.net.http.RequestBody = RequestBody;
globalThis.pedk.net.http.Response = Response;
globalThis.pedk.net.http.fetchData = fetchData;
globalThis.pedk.net.http.receiveData = receiveData;


globalThis.pedk.net.http.HttpsConfigParam = HttpsConfigParam;
globalThis.pedk.net.http.setHttpsConfigParams = setHttpsConfigParams;
globalThis.pedk.net.http.getHttpsConfigParams = getHttpsConfigParams;
