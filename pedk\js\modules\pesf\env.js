/**
 * load key-values from env.json and set to global
 */

const ENV_JSON_FILENAME = "env.json";

/**
 * Load env json
 * @returns { Object } env env value.
 */
function loadEnv() {
  try {
    const env = Object.load(ENV_JSON_FILENAME);
    if (env) {
      return env;
    } else {
      console.log(`WARNING: Load ${ENV_JSON_FILENAME} is empty`);
    }
  } catch (e) {
    console.log(`WARNING: Load ${ENV_JSON_FILENAME} failed: ${e}`);
  }

  return {};
}

globalThis.env = loadEnv();
