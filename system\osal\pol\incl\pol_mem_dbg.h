/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_mem_dbg.h
 * @addtogroup pol
 * @{
 * @addtogroup mem_debug
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pol inner mem debug header file
 */
#ifndef	__POL_MEM_DBG_H__
#define __POL_MEM_DBG_H__

#include "pol_mem_trace.h"
#include "pol_mem_record.h"
#include "pol/pol_unistd.h"

PT_BEGIN_DECLS

#define   free_dbg_ops(ptr,callfile,callline)		{mem_free_trace(ptr);\
													mem_free_record(ptr, pi_gettid(),callfile,callline);}


#define   alloc_dbg_ops(s,e,callfile,callline)		{mem_alloc_trace(s,e);\
													mem_alloc_record(s, e, pi_gettid(),callfile,callline);}



#define   realloc_dbg_ops(s,e,callfile,callline)		{mem_realloc_trace(s,e);\
													mem_realloc_record(s, e, pi_gettid(),callfile,callline);}



#define   mmap_dbg_ops(s,e,callfile,callline)			{mem_mmap_trace(s,e);\
													mem_mmap_record(s,e, pi_gettid(),callfile,callline);}	



#define   munmap_dbg_ops(s,e,callfile,callline)		{mem_munmap_trace(s,e);\
													mem_munmap_record(s,e, pi_gettid(),callfile,callline);}	

PT_END_DECLS
#endif
/**
 *@}
 */
