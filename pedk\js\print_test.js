let print_job

class PrintJobStateListener extends pedk.jobs.print.JobStateListener {
    constructor() {
        super();
        this.job = undefined;
    }

    notify(state) {
        console.log('PrintJobStateListener notify job state ' + print_job.getJobState());
		console.log('PrintJobStateListener notify job id ' + print_job.getJobId());
    }
}

console.log( 'print_test  start  \n' );

// Create print job parameters
const {PrintParameterSet} = pedk.jobs.print;
let param = new PrintParameterSet();

// Set the required parameters
const {PaperType} = pedk.jobs.print;
let paper_type = new PaperType("MEDIA_TYPE_PLAIN");
param.addParameter('PRINT_PARAM_PAPERTYPE', paper_type);

const {OutputTray} = pedk.jobs.print;
let tray_out = new OutputTray("STANDAR_TRAY","MEDIA_SIZE_A4");
param.addParameter('PRINT_PARAM_OUTPUTTRAY', tray_out);

const {CollateMode} = pedk.jobs.print;
let collate_mode = new CollateMode(true);
param.addParameter('PRINT_PARAM_COLLATEMODE', collate_mode);

const {DuplexPrintMode} = pedk.jobs.print;
let duplex_mode = new DuplexPrintMode(0);
param.addParameter('PRINT_PARAM_DUPLEXPRINTMODE', duplex_mode);

const {ImageOrientationMode} = pedk.jobs.print;
let Image_Orientation_mode = new ImageOrientationMode(1);
param.addParameter('PRINT_PARAM_IMAGEORIENTATIONMODE', Image_Orientation_mode);

const {Copies} = pedk.jobs.print;
let copies_num = new Copies(1);
param.addParameter('PRINT_PARAM_COPIES', copies_num);

const {ColorMode} = pedk.jobs.print;
let color_mode = new ColorMode('COLOR_MODE_BLACK_WHITE');
param.addParameter('PRINT_PARAM_COLORMODE', color_mode);

// Create the desired print job type and start printing
const {PrintFromPath} = pedk.jobs.print;
print_job = new PrintFromPath("/emmcimage/file_path_test.pdf");
//print_job.setJobId(10086);

console.log( 'job no start ');
console.log( 'zc:job_id '+print_job.getJobId());
console.log('zc:job num '+print_job.getWoNum());
console.log( 'zc:job_type '+print_job.getJobType());
console.log( 'zc:job_state '+print_job.getJobState());

let start_state = print_job.start(100, param ,null);
console.log( 'job start ');
console.log( 'zc:job_id '+print_job.getJobId());
console.log('zc:job num '+print_job.getWoNum());
console.log( 'zc:job_type '+print_job.getJobType());
console.log( 'zc:job_state '+print_job.getJobState());
console.log('zc:start_state '+start_state);

let listener = new PrintJobStateListener();
console.log('print_test addListener');
let ret = print_job.addListener(listener);
console.log('print_test addListener ret is ', ret);

let ret3 = print_job.removeListener(listener);
console.log('print_test removeListener ret3 is ', ret3);


//print_job.cancel();

console.log( 'print_end   \n' );