/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file rawprint.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief RAWPrint base on Pantum PJL
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "ringbuf.h"

#define RAW_LOCAL_PORT  ( (uint16_t)(netdata_get_rawprint_switch(DATA_MGR_OF(s_rawp_ctx)) ? netdata_get_rawprint_port(DATA_MGR_OF(s_rawp_ctx)) : 0) )

#define JOB_HEADER_LEN 1024
#define MAX_CONNECTION 10

typedef struct rawprint_task
{
    RING_BUF_S*         ringbuf;    ///< The ring buffer is used to save the data received from the TCP stream.
    QIO_S*              pqiotask;   ///< The QIO_S object pointer of task data stream.
    QIO_S*              pqiotcp;    ///< The QIO_S object pointer of TCP data stream.
    uint64_t            rwtotal;    ///< The bytes total of receive from TCP stream and write to ring buffer.
    uint8_t             working;    ///< The RAWPrint task working flag.
    uint8_t             job_end;    ///< The RAWPrint job end flag.
    uint8_t             job_err;    ///< The RAWPrint job err flag.
}
RAW_TASK_S;

typedef struct rawprint_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         srv_tid;
    int32_t             pfd[2];
    int                 conn_cnt;  // for maximum parallelism
}
RAWPRINT_CTX_S;

static RAWPRINT_CTX_S*  s_rawp_ctx = NULL;

/**
 * @brief       The callback function of QIO_CLOSE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current RAWPrint task.
 * @return      Close result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawprint_task_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    NET_DEBUG("RAWPrint job end");
    ptask->working = 0;

    return 0;
}

/**
 * @brief       The callback function of QIO_READABLE(pqio) or QIO_WRITEABLE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current RAWPrint task.
 * @return      Poll result
 * @retval      > 0     : this QIO_S object can be read or written\n
 *              ==0     : poll timeout\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawprint_task_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);
    int32_t block = 0;
    int32_t ms = 0;
    int32_t rv = 0;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( what & QIO_POLL_READ ) /* 可读检测时，判断ringbuf中是否有可读数据 */
    {
        if ( tos > 0 || (tos == 0 && tous >= 0) )
        {
            ms += (tos  > 0 ? (tos  * 1000) : 0);
            ms += (tous > 0 ? (tous / 1000) : 0);
        }
        else
        {
            block = 1;
        }

        do
        {
            rv = ringbuf_readable(ptask->ringbuf);
            if ( rv != 0 )
            {
                break;
            }
            else if ( ptask->job_err || ptask->job_end )
            {
                NET_DEBUG("err(%u) end(%u)", ptask->job_err, ptask->job_end);
                rv = -1;
                break;
            }
            pi_msleep(100);
            ms -= 100;
        }
        while ( ms > 0 || block );
    }
    else if ( what & QIO_POLL_WRITE ) /* QIO_WRITEABLE可写检测时，判断pqiotcp是否可写入 */
    {
        rv = QIO_WRITEABLE(ptask->pqiotcp, tos, tous);
    }
    else
    {
        NET_INFO("unsupported operation(%d)", what);
        rv = -1;
    }

    return rv;
}

/**
 * @brief       The callback function of QIO_READ(pqio), read data from ring buffer.
 * @param[in]   pqio    : The QIO_S object pointer for current RAWPrint task.
 * @return      Read result
 * @retval      >=0     : read the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawprint_task_read(QIO_S* pqio, void* buf, size_t count)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);
    int32_t rv;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    rv = ringbuf_read(ptask->ringbuf, buf, count);
    if ( rv == 0 && ptask->job_err )
    {
        NET_INFO("err(%u)", ptask->job_err);
        rv = -1;
    }

    return rv;
}

/**
 * @brief       The callback function of QIO_WRITE(pqio), write data to TCP stream.
 * @param[in]   pqio    : The QIO_S object pointer for current RAWPrint task.
 * @return      Write result
 * @retval      >=0     : write the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawprint_task_write(QIO_S* pqio, void* buf, size_t count)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);

    RETURN_VAL_IF(ptask == NULL || ptask->pqiotcp == NULL, NET_WARN, -1);

    /* 处理模块通过QIO_WRITE写入数据时，直接转交pqiotcp回传 */
    return QIO_WRITE(ptask->pqiotcp, buf, count);
}

/**
 * @brief       The callback function of QIO_SEEK(pqio), unsupported.
 * @param[in]   pqio    : The QIO_S object pointer for current RAWPrint task.
 * @return      Seek result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawprint_task_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

static void rawprint_start_job(QIO_S* pqio)
{
    ROUTER_MSG_S    sendmsg = { .msgSender = MID_PORT_NET };
    JOB_REQUEST_S*  pjobreq;

    NET_DEBUG("send task to MID_SYS_JOB_MGR");
    pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
    RETURN_IF(pjobreq == NULL, NET_WARN);

    pjobreq->io_class = IO_CLASS_PRINT;
    pjobreq->io_via   = IO_VIA_NET;
    pjobreq->pqio     = pqio;

    sendmsg.msgType   = MSG_CTRL_JOB_REQUEST;
    sendmsg.msg1      = 0;
    sendmsg.msg2      = 0;
    sendmsg.msg3      = pjobreq;
    task_msg_send_by_router(MID_SYS_JOB_MGR, &sendmsg);
}

/**
 * @brief       RAWPrint task handling function, receive data from RAW port and save to ring buffer.
 * @param[in]   ptask   : The RAW_TASK_S object pointer.
 * @param[in]   tos     : The job timeout seconds.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void rawprint_task_handler(RAW_TASK_S* ptask, int32_t tos)
{
    int32_t         wtotal;
    int32_t         nwrite;
    int32_t         rv;
    time_t          then;
    char*           buf;

    buf = (char *)pi_zalloc(TCP_CHUNK_SIZE);
    RETURN_IF(buf == NULL, NET_WARN);

#ifdef CONFIG_SDK_EWS
#include "prnsdk.h"

    if ( netdata_get_prnsdk_enabled(DATA_MGR_OF(s_rawp_ctx)) )
    {
        int ret = -1;
        GQIO_S* pgqio;

        pgqio = gqio_create(ptask->pqiotcp, IO_CLASS_PRINT, IO_VIA_NET);
        RETURN_IF(pgqio == NULL, NET_WARN);

        do {
            rv = parser_common_read(pgqio, (uint8_t*)buf, JOB_HEADER_LEN, 5);  // FIXME: wait how long time? 10 seconds to comply with job_manager.c, for now wait 5 secones
            BREAK_IF(rv <= 0, NET_ERROR);

            BREAK_IF(prnsdk_check_remote_print(buf, rv) < 0, NET_WARN);

            if ( ringbuf_write(ptask->ringbuf, buf, rv) != rv )
            {
                NET_ERROR("ringbuf write %d byte failed", rv);
                break;
            }
            ret = 0;
        }
        while (0);
        GQIO_CLOSE(pgqio);

        RETURN_IF(ret != 0, NET_NONE);
        ptask->rwtotal += rv;
        rawprint_start_job(ptask->pqiotask);
    }
#endif

    pi_time(&then);
    ptask->working = 1;
    while ( ptask->working )
    {
        rv = QIO_READABLE(ptask->pqiotcp, 2, 0);
        if ( rv < 0 )
        {
            NET_WARN("select RAW client failed: %d<%s>", errno, strerror(errno));
            ptask->job_err = 1;
            break;
        }
        else if ( rv == 0 )
        {
            if ( pi_time(NULL) > then + tos )
            {
                NET_WARN("RAWPrint timeout(%d), so think the task unusual quit!!!", tos);
                ptask->job_err = 1;
                break;
            }
            continue;
        }

        rv = QIO_READ(ptask->pqiotcp, buf, TCP_CHUNK_SIZE);
        if ( rv < 0 )
        {
            NET_WARN("recv from TCP connection failed: %d<%s>", errno, strerror(errno));
            ptask->job_err = 1;
            break;
        }
        else if ( rv == 0 )
        {
            NET_INFO("receive 0 bytes, the client of TCP connection has been closed!");
            ptask->job_end = 1;
            break;

        }

        wtotal = 0;
        while ( ptask->working && (wtotal < rv) )
        {
            if ( ringbuf_writable(ptask->ringbuf) )
            {
                nwrite = ringbuf_write(ptask->ringbuf, buf + wtotal, (size_t)(rv - wtotal));
                if ( nwrite > 0 )
                {
                    wtotal += nwrite;
                    continue;
                }
            }
            NET_INFO("ring buffer is full, retry after 1s");
            pi_msleep(50);
        }

        if ( rv != wtotal )
        {
            NET_WARN("write RAWPrint job data to ringbuf failed, expect(%d) wtotal(%d) rwtotal(%llu)", rv, wtotal, (unsigned long long)ptask->rwtotal);
            ptask->job_err = 1;
            break;
        }

        if ( ptask->rwtotal == 0 ) /* 收到第一包数据并写入ringbuf后，再将task给到system_job */
        {
            rawprint_start_job(ptask->pqiotask);
        }
        ptask->rwtotal += wtotal;
        pi_time(&then);
    }
    pi_free(buf);
}

/**
 * @brief       The threads pool handling function for once connect to the RAWPrint port.
 * @param[in]   arg     : The threads pool context(NET_CONN_S object pointer).
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void rawprint_connection(void* arg)
{
    NET_CONN_S* pnc = (NET_CONN_S *)arg;
    RAW_TASK_S* ptask = NULL;
    int32_t     tos;

    do
    {
        tos = (int32_t)netdata_get_io_timeout(DATA_MGR_OF(s_rawp_ctx));
        NET_DEBUG("RAWPrint connection(%s : %u) start, timeout(%d)", pnc->remote_addr, pnc->remote_port, tos);
        BREAK_IF(netsock_peek_connection(pnc, tos) <= 0, NET_DEBUG);

        ptask = (RAW_TASK_S *)pi_zalloc(sizeof(RAW_TASK_S));
        BREAK_IF(ptask == NULL, NET_WARN);

        ptask->pqiotask = (QIO_S *)pi_zalloc(sizeof(QIO_S));
        BREAK_IF(ptask->pqiotask == NULL, NET_WARN);

        ptask->pqiotask->close = rawprint_task_close;
        ptask->pqiotask->poll  = rawprint_task_poll;
        ptask->pqiotask->read  = rawprint_task_read;
        ptask->pqiotask->write = rawprint_task_write;
        ptask->pqiotask->seek  = rawprint_task_seek;
        ptask->pqiotask->priv  = (void *)ptask;

        ptask->ringbuf = ringbuf_create(TCP_CHUNK_SIZE * 16);
        BREAK_IF(ptask->ringbuf == NULL, NET_WARN);

        ptask->pqiotcp = qio_tcp_create(pnc->sockfd);
        BREAK_IF(ptask->pqiotcp == NULL, NET_WARN);

        pnc->sockfd = INVALID_SOCKET; /* sockfd流转到pqiotcp，随QIO_CLOSE释放 */
        rawprint_task_handler(ptask, tos);
        NET_DEBUG("TCP connection(%s : %u) is disconnected, close local QIO_S<%p>", pnc->remote_addr, pnc->remote_port, ptask->pqiotcp);
        QIO_CLOSE(ptask->pqiotcp); /* 当前任务处理结束，立刻关闭TCP socket */
        ptask->pqiotcp = NULL;

        /* TCP链路有接收数据并给到system_job，所以当前线程需等待作业结束调用rawprint_task_close. */
        while ( ptask->working && (ptask->rwtotal > 0) )
        {
            if ( pi_time(NULL) % 10 == 0 )
            {
                NET_DEBUG("waiting RAWPrint job end from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
            }
            pi_msleep(1000);
        }
        NET_DEBUG("RAWPrint job end rwtotal(%llu), working(%d)", (unsigned long long)ptask->rwtotal, ptask->working);
        NET_DEBUG("RAWPrint job end from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
    }
    while ( 0 );

    if ( ptask != NULL )
    {
        if ( ptask->ringbuf != NULL )
        {
            ringbuf_destroy(ptask->ringbuf);
        }
        if ( ptask->pqiotask != NULL )
        {
            ptask->pqiotask->priv = NULL;
            pi_free(ptask->pqiotask);
        }
        pi_free(ptask);
    }
    netsock_close_connection(pnc);

    __atomic_fetch_sub(&s_rawp_ctx->conn_cnt, 1, __ATOMIC_SEQ_CST);
}

/**
 * @brief       The RAWPrint handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void* rawprint_thread_handler(void* arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    int32_t         pipefd = s_rawp_ctx->pfd[0];
    int32_t         update = 0;
    int32_t         status;
    int32_t         max_fd;

    while ( 1 )
    {
        FD_ZERO(&rfds);
        FD_SET(pipefd, &rfds);
        max_fd = pipefd;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, RAW_LOCAL_PORT, 1);
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        if ( FD_ISSET(pipefd, &rfds) )
        {
            read(pipefd, &status, sizeof(status));
            NET_DEBUG("recv(0x%X), reload service socket", status);
            update = 1;
            continue;
        }

        if ( __atomic_load_n(&s_rawp_ctx->conn_cnt, __ATOMIC_SEQ_CST) > MAX_CONNECTION )
        {
            sleep(2);   // wait for other rawprint thread complete
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    NET_DEBUG("new connection %d from %s : %u to %u", pnc->sockfd, pnc->remote_addr, pnc->remote_port, pnc->local_port);
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_rawp_ctx), rawprint_connection, pnc) < 0 )
                    {
                        NET_WARN("add rawprint_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                        continue;
                    }
                    __atomic_fetch_add(&s_rawp_ctx->conn_cnt, 1, __ATOMIC_SEQ_CST);
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}

/**
 * @brief       The callback function when the subject notify.
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void rawprint_update_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_rawp_ctx == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_RAWPRINT )
    {
        write(s_rawp_ctx->pfd[1], &(s->subject_status), sizeof(s->subject_status));
    }
}

int32_t rawprint_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_rawp_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_rawp_ctx = (RAWPRINT_CTX_S *)pi_zalloc(sizeof(RAWPRINT_CTX_S));
    RETURN_VAL_IF(s_rawp_ctx == NULL, NET_WARN, -1);

    do
    {
        s_rawp_ctx->net_ctx = net_ctx;
        pipe(s_rawp_ctx->pfd);

        s_rawp_ctx->srv_tid = pi_thread_create(rawprint_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "rawprint_thread_handler");
        BREAK_IF(s_rawp_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("RAWPrint initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netport_observer(net_ctx, rawprint_update_callback, NULL);
    }
    else
    {
        rawprint_epilog();
    }
    return ret;
}

void rawprint_epilog(void)
{
    if ( s_rawp_ctx != NULL )
    {
        if ( s_rawp_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_rawp_ctx->srv_tid);
        }
        pi_close(s_rawp_ctx->pfd[0]);
        pi_close(s_rawp_ctx->pfd[1]);
        pi_free(s_rawp_ctx);
        s_rawp_ctx = NULL;
    }
}
/**
 *@}
 */
