#ifndef _PESF_DEFINED_
#define _PESF_DEFINED_

/* 测试代码 */
#include "test_add.h"
#include "test_helloworld.h"
#include "test_demo.h"

/* API声明 */
#include "pesf_common.h"
#include "PEDK_ui.h"
#include "pedk_device_status.h"
#include "pesf_device_setting.h"
#include "PEDK_jobs_copy.h"
#include "pesf_jobs_scan.h"
#include "PEDK_jobs_print.h"
#include "pesf_jobs_fax.h"
#include "pedk_jobctl.h"
#include "pesf_device_powersave.h"
#include "PEDK_usbh_udisk.h"
#include "PEDK_usbd.h"
#include "pesf_device.h"
#include "pesf_device_log.h"
#include "pedk_device_beep.h"
#include "PEDK_net_email.h"
#include "PEDK_net_ftp.h"
#include "PEDK_net_smb.h"
#include "pesf_device_settings.h"
#include "pedk_device_capabilities.h"
#include "pesf_crypto.h"
#include "PEDK_net_ssl.h"
#include "pesf_shortcuts.h"
#include "PEDK_auth_whitelist.h"
#include "PEDK_net_server.h"
#include "pesf_device_net_setting.h"
#include "pedk_deviceinfo.h"
#include "pedk_productinfo.h"
#include "pedk_traysetting.h"
#include "pedk_storage.h"

typedef struct JSCFunctionList {
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

const JSCFunctionList* getJSCFunctionList(int *length);
int addGlobalAPI(JSContext *ctx, JSValueConst global);

#endif /* _PESF_DEFINED_ */
