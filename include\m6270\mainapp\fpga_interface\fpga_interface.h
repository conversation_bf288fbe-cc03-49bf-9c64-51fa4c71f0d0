/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  fpga_interface.h
* @addtogroup scan
*
* @{
* @addtogroup  scan
* <AUTHOR>
* @date   2022-5-24
* @version  v1.0
* @brief   this is a main scan job msg process for the moudle
**/

#ifndef FPGA_INTERFACE_H
#define FPGA_INTERFACE_H

#define FPGA_INFO_TRANSFER_LEN  12  ///< fpga info transfer led
#define FPGA_SPI_BITS           32  ///< fpga spi bits
#define FPGA_TXRX_VERBOSE       0   ///< fpga txrx verbase

#define FPGA_VERSION_STRING_LEN 32  ///< fpga version string len

/**
 * @brief update fpga version
 * @param[in] tag fpga tag
 * @param[in] version fpga version
 * @return void \n
 * @retval void
 * <AUTHOR>
 * @date 2021-10-29
 */
void update_fpga_version(int tag, unsigned int version);

/**
 * @brief get front fpga version
 * @return char * \n
 * @retval version
 * <AUTHOR>
 * @date 2021-10-29
 */
char *get_front_fpga_version();

/**
 * @brief get back fpga version
 * @return char * \n
 * @retval version
 * <AUTHOR>
 * @date 2021-10-29
 */
char *get_back_fpga_version();


#endif

/**
 *@}
 */

