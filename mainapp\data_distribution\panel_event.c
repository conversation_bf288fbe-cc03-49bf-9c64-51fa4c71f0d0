/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_event.c
 * @addtogroup panel_dc
 * @{
 * @brief panel event register and callback,process status and storage parameter
 * <AUTHOR>
 * @version 1.0
 * @date 2023-06-13
 */


#include <string.h>
#include <stdio.h>
#include "panel_event.h"
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "public/msgrouter_main.h"
#include "pol/pol_threads.h"
#include "panel_dc_main.h"
#include "qio/qio_general.h"
#include "public_data_proc.h"
#include "pol/pol_log.h"
#include "platform_api.h"
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_mgr.h"
#include "moduleid.h"
#include "nvram.h"
#include "cmd.h"
#include "platform_api.h"
#include "panel_dc_cmd_process.h"
#include "pol/pol_string.h"
#include "status_test.h"
#include "statusid.h"
#include "print.h"
#include "panel_config.h"
#include "security_register.h"
#include "panel_address_book.h"
#include "panel_pedk_mfp.h"

#include "storage_string_map.h"
#include "hal.h"
#include "panel_overlay_copy.h"
#include "scan_event.h"

#define PANEL_KEY_SHORT_PRESS            1      ///< panel key short press event
#define PANEL_KEY_LONG_PRESS             2      ///< panel key long press event
#define TOGGLE_SWITCH_KEY_PRESS          3      ///< toggle switch press event

#define PANEL_REQUEST_POWER_OFF          5      ///< poweroff mode send to power manager
#define PANEL_REQUEST_POWER_OFF_CANCEL   6      ///< cancel poweroff mode send to power manager

#define JOB_MGR_BUSY      1 ///< job manager received the job

static EVT_MGR_CLI_S* s_panel_evt_client = NULL;    /// panel event manager client

/**
 * @brief power manager status
 */
typedef enum
{
    PM_STATUS_INIT = 0,
    PM_STATUS_WAKEUP = 1,
    PM_STATUS_SLEEP = 2,
    PM_STATUS_DEEPSLEEP = 3,
    PM_STATUS_SLEEPING = 4,
    PM_STATUS_WAKINGUP = 5,
} PANEL_POWER_MANAGER_STATUS_E;

/**
 * @brief system status struct
 */
typedef struct
{
    uint32_t status_id;
    int32_t  status_param1;
    int32_t  status_param2;
    int32_t  status_param3;
}SYSTEM_STATUS_S;

/**
 * @brief get panel event client
 * @return panel event client pointer
 */
EVT_MGR_CLI_S* get_panel_event_client( void )
{
    return s_panel_evt_client;
}

/**
 * @brief set system time to rtc
 * @param[in] system time struct from panel
 */
void panel_set_system_time( UI_DATE_TIME_S* recv_time )
{

    void* handle = NULL;
    struct tm tm_time;
    time_t rawtime;

    tm_time.tm_year = recv_time->year;
    tm_time.tm_mon  = recv_time->month;
    tm_time.tm_mday = recv_time->day;
    tm_time.tm_hour = recv_time->hour;
    tm_time.tm_min  = recv_time->minute;
    tm_time.tm_sec  = recv_time->second;

    //写RTC时间
    pi_hal_rtc_request(&handle, HAL_REQUEST_FLAG_BLOCK);
    pi_hal_rtc_time_write(handle,tm_time);
    pi_hal_rtc_free(&handle);

    //写入系统时间
	tm_time.tm_year = tm_time.tm_year - 1900;
    tm_time.tm_mon = tm_time.tm_mon - 1;
    rawtime = mktime(&tm_time);
    stime(&rawtime);

    notify_security_operate( SECURITY_OPERATE_SYS_TIME_MODIFY, NULL, 0 );
}

/**
 * @brief get system time from rtc
 */
void panel_init_system_time()
{
    void* handle = NULL;
    struct tm tm_time;
    time_t rawtime;

    //读取RTC时间
    pi_hal_rtc_request(&handle, HAL_REQUEST_FLAG_BLOCK);
    pi_hal_rtc_time_read(handle,&rawtime);
    pi_hal_rtc_free( &handle );

    //设置系统时间
    //stime(&rawtime);
}

/**
 * @brief get system time from rtc and send to panel
 */
void panel_get_system_time()
{
    uint32_t ret = 0;
    UI_DATE_TIME_S panel_time;      ///< this struct from panel

    time_t timep;
    struct tm *p_tm;
    timep = time(NULL);
    p_tm = gmtime(&timep); /*Get GMT Time*/

    panel_time.year     = p_tm->tm_year + 1900;
    panel_time.month    = p_tm->tm_mon + 1;
    panel_time.day      = p_tm->tm_mday;
    panel_time.hour     = p_tm->tm_hour;
    panel_time.minute   = p_tm->tm_min;
    panel_time.second   = p_tm->tm_sec;

    ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_SYS_TIME, NOTIFICATION_RESPONSE, &panel_time, sizeof(panel_time) );
    if( ret > 0)
    {
        pi_log_d( "year:%d,mon:%d,day:%d,hour:%d,min:%d,sec:%d\n",panel_time.year,panel_time.month,panel_time.day,panel_time.hour,panel_time.minute,panel_time.second );
    }
}



/**
 * @brief shake hand with panel when initing
 */
void dc_request_shake_hand(  )
{
    uint32_t data = 1;      /// no use

    panel_send_data_u8( OPERATE, OPERATE_CMD_DC_INIT_DONE_NOTIFY, NOTIFICATION_RESPONSE, &data, sizeof(data) );
}

/**
 * @brief notify the power manager to sleep
 */
void panel_request_sleep( void )
{
    uint32_t sleep_mode = 0;
    PANEL_CONFIG_TABLE_S* panel_config = NULL;

    panel_config = get_panel_config_table();

/*
    if( JOB_MGR_BUSY == job_manager_busy() )
    {
        pi_log_d("job busy,dont sleep\n" );
        return;
    }

    if( PM_STATUS_READY != panel_status->system_sleep_status )
    {
        pi_log_d("power status not ready,can not sleep\n");
        return;
    }
*/
    if( panel_config->sleep_mode == 0 )
    {
        sleep_mode = PM_EVENT_SLEEP;
        pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_POWERMGR_CONFIG, &sleep_mode, sizeof(sleep_mode));
        pi_log_d("panel request to sleep \n" );
    }
    if( panel_config->sleep_mode == 1 )
    {
        sleep_mode = PM_EVENT_DEEPSLEEP;
        pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_POWERMGR_CONFIG, &sleep_mode, sizeof(sleep_mode));
        pi_log_d("panel request to deep sleep \n" );
    }

}

/**
 * @brief notify the power manager to wakeup
 */
void panel_request_wakeup( void )
{
    uint32_t sleep_mode = 0;
    PANEL_STATUS_TABLE_S* panel_status = NULL;

    panel_status = get_panel_status_table();
    if( PM_STATUS_SLEEP == panel_status->system_sleep_status )
    {
        sleep_mode = PM_EVENT_WAKEUP;
        pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_POWERMGR_CONFIG, &sleep_mode, sizeof(sleep_mode));
        pi_log_d("panel request to wake from first sleep\n");
    }
    else if( panel_status->system_sleep_status == PM_STATUS_DEEPSLEEP )
    {
        sleep_mode = PM_EVENT_DEEP_WAKEUP;
        pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_POWERMGR_CONFIG, &sleep_mode, sizeof(sleep_mode));
        pi_log_d("panel request to wake from deep sleep\n");
    }
}

/**
 * @brief notify panel to wakeup or sleep or power off by panel key type
 */
void panel_key_press_process( void * data, uint32_t data_len )
{
    uint32_t key_type = 0;
    PANEL_STATUS_TABLE_S* panel_status = NULL;

    panel_status = get_panel_status_table();

    key_type = *( (uint32_t*) data );
    if( PANEL_KEY_SHORT_PRESS == key_type )
    {
        pi_log_d( "panel key short press\n" );
        panel_send_data_u8( OPERATE, OPERATE_CMD_PANEL_KEY_SHORT_PRESS, NOTIFICATION_RESPONSE, data, data_len );
        /*
        if( panel_status->system_sleep_status == PM_STATUS_READY )
        {
            panel_request_sleep();
        }*/
        if( PM_STATUS_DEEPSLEEP == panel_status->system_sleep_status )
        {
            panel_request_wakeup();
        }

    }
    else if( PANEL_KEY_LONG_PRESS == key_type)
    {
        pi_log_d("panel key long press\n");
        if( PM_STATUS_SLEEP == panel_status->system_sleep_status || PM_STATUS_DEEPSLEEP == panel_status->system_sleep_status )
        {
            panel_request_wakeup();
        }
        ///when recv long press from sleep,pm status will change to poweroff,but it should wakeup
        else
        {
            if( JOB_MGR_BUSY == job_manager_busy() )
            {
                pi_log_d("job busy,cancel poweroff\n" );
                panel_request_cancel_power_off();
                return;
            }
            if( PM_STATUS_SLEEPING == panel_status->system_sleep_status || PM_STATUS_WAKINGUP == panel_status->system_sleep_status )
            {
                pi_log_d("power status in sleeping/waking,can not power off\n");
                return;
            }
            pi_log_d("panel request to poweroff\n");
            panel_send_data_u8( OPERATE, OPERATE_CMD_POWER_OFF_REQUEST, NOTIFICATION_RESPONSE, data, data_len );
        }
    }

}

/**
 * @brief notify the power manager to power off
 */
void panel_request_machine_power_off( void )
{
    uint32_t poweroff_mode = 0;
    poweroff_mode = PANEL_REQUEST_POWER_OFF;
    pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_POWEROFFMGR_CONFIG, &poweroff_mode, sizeof(poweroff_mode));
}

/**
 * @brief notify the power manager to cancel power off
 */
void panel_request_cancel_power_off( void )
{
    uint32_t poweroff_mode = 0;
    poweroff_mode = PANEL_REQUEST_POWER_OFF_CANCEL;
    pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_POWEROFFMGR_CONFIG, &poweroff_mode, sizeof(poweroff_mode));
}

void FB_ADF_open_request_to_wakeup( void* data, uint32_t data_len)
{
    //SCAN_FB_ADF_COVER_STATUS_S* FB_ADF_cover = NULL;
    PANEL_STATUS_TABLE_S* panel_status = NULL;

    if( data_len != sizeof(SCAN_FB_ADF_COVER_STATUS_S) )
    {
        pi_log_e("fb/adf cover data error!\n");
        return;
    }

    //FB_ADF_cover = (SCAN_FB_ADF_COVER_STATUS_S*)data;
    panel_status = get_panel_status_table();

    if( PM_STATUS_SLEEP == panel_status->system_sleep_status || PM_STATUS_DEEPSLEEP == panel_status->system_sleep_status )
    {
        panel_request_wakeup();
    }


}

/**
 * @brief notify the power manager to cancel power off
 */
void panel_recv_print_static_page_info_copy( PRINT_CONFIG_STATISTIC_INFO_S* page_statistic_info )
{
    PANEL_CONFIG_TABLE_S* panel_config = NULL;

    panel_config = get_panel_config_table();



        panel_config->copy_statistic_info.statical_type = 2;
        panel_config->copy_statistic_info.a3_print_sum = page_statistic_info->page_paper_size_mono_counter.a3_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a3_counter;

        panel_config->copy_statistic_info.a4_print_sum = page_statistic_info->page_paper_size_mono_counter.a4l_counter +
                                                           page_statistic_info->page_paper_size_mono_counter.a4_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a4l_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a4_counter;

        panel_config->copy_statistic_info.a5_print_sum = page_statistic_info->page_paper_size_mono_counter.a5l_counter +
                                                           page_statistic_info->page_paper_size_mono_counter.a5_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a5l_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a5_counter;

        panel_config->copy_statistic_info.color_print_sum = page_statistic_info->page_color_counter;
        panel_config->copy_statistic_info.mono_print_sum = page_statistic_info->page_mono_counter;
        panel_config->copy_statistic_info.total_print_sum = page_statistic_info->page_total_counter;

        pi_log_d("panel recv copy statistic info,total sum:%d,color:%d,mono%d\n", panel_config->copy_statistic_info.total_print_sum,
                    panel_config->copy_statistic_info.color_print_sum, panel_config->copy_statistic_info.mono_print_sum);
        panel_send_data_u8( SETTING, SETTING_CMD_GET_PRINT_STATISTIC_INFO, NOTIFICATION_RESPONSE, &(panel_config->copy_statistic_info), sizeof(panel_config->copy_statistic_info) );
}

/**
 * @brief notify the power manager to cancel power off
 */
void panel_recv_print_static_page_info_print( PRINT_CONFIG_STATISTIC_INFO_S* page_statistic_info )
{
    PANEL_CONFIG_TABLE_S* panel_config = NULL;

    panel_config = get_panel_config_table();


        panel_config->print_statistic_info.statical_type = 1;
        panel_config->print_statistic_info.a3_print_sum = page_statistic_info->page_paper_size_mono_counter.a3_counter + page_statistic_info->page_paper_size_color_counter.a3_counter;

        panel_config->print_statistic_info.a4_print_sum = page_statistic_info->page_paper_size_mono_counter.a4l_counter +
                                                           page_statistic_info->page_paper_size_mono_counter.a4_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a4l_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a4_counter;

        panel_config->print_statistic_info.a5_print_sum = page_statistic_info->page_paper_size_mono_counter.a5l_counter +
                                                           page_statistic_info->page_paper_size_mono_counter.a5_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a5l_counter +
                                                           page_statistic_info->page_paper_size_color_counter.a5_counter;

        panel_config->print_statistic_info.color_print_sum = page_statistic_info->page_color_counter;
        panel_config->print_statistic_info.mono_print_sum = page_statistic_info->page_mono_counter;
        panel_config->print_statistic_info.total_print_sum = page_statistic_info->page_total_counter;

        pi_log_d("panel recv print statistic info,total sum:%d,color:%d,mono%d\n", panel_config->print_statistic_info.total_print_sum,
                panel_config->print_statistic_info.color_print_sum, panel_config->print_statistic_info.mono_print_sum);
        panel_send_data_u8( SETTING, SETTING_CMD_GET_PRINT_STATISTIC_INFO, NOTIFICATION_RESPONSE, &(panel_config->print_statistic_info), sizeof(panel_config->print_statistic_info) );
}

void panel_get_storagemgr_hotplugdev_state_changed ( void* data, uint32_t data_len )
{
    char* data_byte = (char*)data;
    pi_log_d("panel get storage hotplugdev state changed,storage type:%d,status:%d,id:%d,reserve:%d\n",data_byte[0],data_byte[1],data_byte[2],data_byte[3] );
    static uint32_t storagemgr_hotplugdev_state_flag = 0;
    PANEL_CONFIG_TABLE_S* panel_config = get_panel_config_table();


    //硬盘不支持热插拔，开机推一次
    if( STO_MEDIA_TYPE_SSD == data_byte[0] && storagemgr_hotplugdev_state_flag == FALSE)
    {
        panel_config->hard_disk_insert_status = data_byte[1];
        storagemgr_hotplugdev_state_flag = TRUE;

        panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED, NOTIFICATION_RESPONSE, data, data_len );
        pi_log_d("first receive storagemgr hotplugdev state:%d\n", panel_config->hard_disk_insert_status);
    }

    if( data_byte[0] != STO_MEDIA_TYPE_SSD )
    {
        panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED, NOTIFICATION_RESPONSE, data, data_len );
        if( STO_MEDIA_TYPE_UDISK == data_byte[0] )
        {
            panel_config->udisk_insert_status = data_byte[1];
            pi_log_d("udisk insert status:%d\n", panel_config->udisk_insert_status);
        }
    }

}

/************************************************************
func: panel_event_callback
param: void
return: void
description: attention events
author: mazhenhuan
************************************************************/
static void panel_event_callback( const EVT_MSG_S* msg, void* ctx )
{
    SYSTEM_STATUS_S* syatem_status = NULL;
    uint32_t module_id = msg->module_id;
    uint32_t event_type = msg->event_type;
    uint32_t data_u32 = 0;
    int32_t  data_i32 = 0;
    uint32_t ret = 0 ;
    JOBINFO_S *job_info;
    PRINT_CONFIG_ENGINE_INFO_S* print_machine_info = NULL;
    PRINT_CONFIG_TRAY_INFO_S* print_tray_info = NULL;
    PRINT_CONFIG_STATISTIC_INFO_S* print_statistic_info = NULL;
    PANEL_CONFIG_TABLE_S* panel_config = NULL;
    PANEL_STATUS_TABLE_S* panel_status = NULL;

    WIFI_CONN_INFO_S* wifi_info = NULL;
    WIFI_SCAN_RESULT_S* wifi_scan_result = NULL;
    static SCAN_PAPER_SIZE_NUM_S scan_paper_color_s = {0};
    static SCAN_PAPER_SIZE_NUM_S scan_paper_mono_s = {0};


    panel_config = get_panel_config_table();

    pi_log_d( "panel get module: %u request event type: 0x%x, msg_len: %d\n", module_id, event_type, msg->data_length );

    switch ( event_type )
    {
        /*system status event start*/
        case EVT_TYPE_SYSTEMSTATUS_UPDATE:
            ret = panel_send_data_u8(STATUS, STATUS_CMD_STATUS_UPDATE, NOTIFICATION_RESPONSE, msg->data, msg->data_length);
            syatem_status = (SYSTEM_STATUS_S*)msg->data;
            for( int i = 0; i < msg->data_length/sizeof(SYSTEM_STATUS_S) ;i++ )
            {
                    if( STATUS_I_POWERMGR_SLEEP == syatem_status[i].status_id || STATUS_I_POWERMGR_WAKEUP == syatem_status[i].status_id \
                        || STATUS_I_POWERMGR_SLEEPING == syatem_status[i].status_id || STATUS_I_POWERMGR_WAKINGUP == syatem_status[i].status_id )
                    {
                        panel_status = get_panel_status_table();
                        panel_status->last_sleep_status = panel_status->system_sleep_status;

                        if( STATUS_I_POWERMGR_SLEEP == syatem_status[i].status_id &&  0 == syatem_status[i].status_param1  )
                        {
                            panel_status->system_sleep_status = PM_STATUS_SLEEP;    /// save sleep status
                        }
                        if( STATUS_I_POWERMGR_SLEEP == syatem_status[i].status_id &&  1 == syatem_status[i].status_param1  )
                        {
                            panel_status->system_sleep_status = PM_STATUS_DEEPSLEEP;    /// save sleep status
                        }
                        if( STATUS_I_POWERMGR_WAKEUP == syatem_status[i].status_id )
                        {
                            panel_status->system_sleep_status = PM_STATUS_WAKEUP;    /// save sleep status
                        }
                        if( STATUS_I_POWERMGR_SLEEPING == syatem_status[i].status_id )
                        {
                            panel_status->system_sleep_status = PM_STATUS_SLEEPING;    /// save sleep status
                        }
                        if( STATUS_I_POWERMGR_WAKINGUP == syatem_status[i].status_id )
                        {
                            panel_status->system_sleep_status = PM_STATUS_WAKINGUP;    /// save sleep status
                        }
                        pi_log_i( "panel set sleep status :%d\n",syatem_status[i].status_param1);
                    }
                    ///save status for panel when wakeup from deep sleep
                    pi_log_i( "panel recv sys status:0x%x#%d,%d,%d\n",syatem_status[i].status_id ,syatem_status[i].status_param1, \
                                       syatem_status[i].status_param2,syatem_status[i].status_param3);
            }
            break;

        case EVT_TYPE_SYSTEMJOB_UPDATE:
            if( ( msg->data_length != sizeof(JOBINFO_S) ) || msg->data == NULL )
            {
                pi_log_e("panel recv error data\n");
                return;
            }
            job_info = ( JOBINFO_S* )msg->data;
            pi_log_i("panel recv job status msg,job_id:%d, status:%d, obj:%d, copies:%d,current page:%d,total page:%d\n",job_info->job_id, job_info->status, job_info->obj, job_info->copies, job_info->current_pages, job_info->total_pages);
            ret = panel_send_data_u8( STATUS, STATUS_CMD_JOB_STATUS_UPDATE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        /*system status event end*/

        /*platform event start*/

        case EVT_TYPE_PLATFORM_FIRMWARE_VERSION_MODIFY:
            pi_log_i( "send dc firmware version :%s\n", msg->data );
            pi_memset( panel_config->firmware_version, 0, sizeof(panel_config->firmware_version) );
            pi_strncpy( panel_config->firmware_version, msg->data, msg->data_length );
            ret = panel_send_data_u8( INFORMATION, INFO_CMD_GET_FW_VERSION, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY:
            pi_log_i( "send panel firmware version :%s\n", msg->data );
            ret = panel_send_data_u8( INFORMATION, INFO_CMD_GET_PANEL_VERSION, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PLATFORM_PDT_NAME_MODIFY:
            pi_log_i( "pdt name :%s\n", msg->data );
            pi_memset( panel_config->ptd_name, 0, sizeof(panel_config->ptd_name) );
            pi_strncpy( panel_config->ptd_name, msg->data, msg->data_length );
            ret = panel_send_data_u8(INFORMATION, INFO_CMD_GET_DEVICE_NAME, NOTIFICATION_RESPONSE, msg->data, msg->data_length);
            break;
        /*platform event end*/

        /*network event start*/
        case EVT_TYPE_NET_ETH_IPV4_DHCP_SWITCH_CHANGED:
            data_u32 = *( (unsigned int*) msg->data );
            panel_config->ipv4_config.dhcp_enabled = data_u32;
            pi_log_i( "IPV4 dhcp :%d\n", data_u32 );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_NETWORK_IPV4_DHCP_SWITCH, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_ETH_IPV4_ADDRESS_CHANGED:
            pi_log_i( "IPV4 addr :%s\n", msg->data );
            pi_memset( panel_config->ipv4_config.address, 0, sizeof(panel_config->ipv4_config.address) );
            pi_strncpy( panel_config->ipv4_config.address, msg->data, msg->data_length );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_NETWORK_IPV4_ADDR, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            if( ret == FALSE )
            {
                pi_log_e( "panel dc send IPV4 addr fail!\n" );
            }
            break;
        case EVT_TYPE_NET_ETH_IPV4_MASK_CHANGED:
            pi_log_i( "IPV4 mask :%s\n", msg->data );
            pi_memset( panel_config->ipv4_config.mask, 0, sizeof(panel_config->ipv4_config.mask) );
            pi_strncpy( panel_config->ipv4_config.mask, msg->data, msg->data_length );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_NETWORK_IPV4_MASK, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            if( ret == FALSE )
            {
                pi_log_e( "panel dc send IPV4 mask fail!\n" );
            }
            break;
        case EVT_TYPE_NET_ETH_IPV4_GATEWAY_CHANGED:
            pi_log_i( "IPV4 gateway :%s\n", msg->data );
            pi_memset( panel_config->ipv4_config.gateway, 0, sizeof(panel_config->ipv4_config.gateway) );
            pi_strncpy( panel_config->ipv4_config.gateway, msg->data, msg->data_length );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_NETWORK_IPV4_GATEWAY, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            if(ret == FALSE)
            {
                pi_log_e( "panel dc send IPV4 gateway fail!\n" );
            }
            break;
        case EVT_TYPE_NET_HOST_NAME_CHANGED:
            pi_log_i( "net host name :%s\n", msg->data );
            pi_memset( panel_config->host_name, 0, sizeof(panel_config->host_name) );
            pi_strncpy( panel_config->host_name, msg->data, msg->data_length );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_NETWORK_HOST_NAME, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            if( ret == FALSE )
            {
                pi_log_e( "panel dc send net host name fail!\n" );
            }
            break;
        case EVT_TYPE_NET_ETH_IPV6_SWITCH_CHANGED:
            data_u32 = *( (unsigned int*) msg->data );
            panel_config->ipv6_config.enabled = data_u32;
            pi_log_i( "IPV6 switch :%d\n", data_u32 );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_NETWORK_IPV6_SWITCH, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_ETH_IPV6_DHCP_SWITCH_CHANGED:
            data_u32 = *( (unsigned int*) msg->data );
            panel_config->ipv6_config.dhcp_enabled = data_u32;
            pi_log_i( "IPV6 dhcp :%d\n", data_u32 );
            break;
        /*network event end*/

        case EVT_TYPE_PANEL_KEY_PRESS:
            panel_key_press_process( msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_ENGINE_INFO:
            if( ( msg->data_length != sizeof(PRINT_CONFIG_ENGINE_INFO_S) ) || msg->data == NULL )
            {
                pi_log_e("panel recv error data\n");
                return;
            }
            pi_log_i( "panel get print machine info\n" );
            print_machine_info = (PRINT_CONFIG_ENGINE_INFO_S*) msg->data;
            pi_memcpy( &panel_config->print_machine_config, msg->data, msg->data_length );
            printf("engine ver:%s\n",print_machine_info->eng_firmware_version);
            ret = panel_send_data_u8( INFORMATION, INFO_CMD_GET_PRINT_ENG_INFO, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_TRAY_INFO:
            if( ( msg->data_length != sizeof(PRINT_CONFIG_TRAY_INFO_S) ) || msg->data == NULL )
            {
                pi_log_e("panel recv error data\n");
                return;
            }
            print_tray_info = ( PRINT_CONFIG_TRAY_INFO_S* )msg->data;
            pi_memcpy( &(panel_config->print_tray_config), msg->data, sizeof(PRINT_CONFIG_TRAY_INFO_S) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_GET_PRINT_TRAY, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_i( "get print tray size info:tray1:%d,tray2:%d,tray3:%d,tray4:%d,multi:%d\n",
                print_tray_info->tray_1_paper_size, print_tray_info->tray_2_paper_size, print_tray_info->tray_3_paper_size, print_tray_info->tray_4_paper_size, print_tray_info->tray_multi_paper_size);
            pi_log_i( "get print tray type info:tray1:%d,tray2:%d,tray3:%d,tray4:%d,multi:%d\n",
                print_tray_info->tray_1_paper_type, print_tray_info->tray_2_paper_type, print_tray_info->tray_3_paper_type, print_tray_info->tray_4_paper_type, print_tray_info->tray_multi_paper_type);

            pi_log_i( "get print tray remain,tray1:%d,tray2:%d,tray3:%d,tray4:%d,multi:%d\n",
                print_tray_info->tray_1_paper_remain, print_tray_info->tray_2_paper_remain, print_tray_info->tray_3_paper_remain, print_tray_info->tray_4_paper_remain, print_tray_info->tray_multi_paper_remain);

            panel_status = get_panel_status_table();
            if( panel_status->print_tray_multi_remain  == 0 && print_tray_info->tray_multi_paper_remain == 1 )
            {
                ret = panel_send_data_u8( STATUS, STATUS_CMD_GET_PRINT_MULTI_TRAY_PAPER_IN, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                pi_log_i( "multi paper in\n");
            }
            panel_status->print_tray_multi_remain = print_tray_info->tray_multi_paper_remain;
            break;

        case EVT_TYPE_PRINT_CONSUMPTION_INFO:
            if( ( msg->data_length != sizeof(PRINT_CONFIG_CONSUMPTION_INFO_S) ) || msg->data == NULL )
            {
                return;
            }
            pi_memcpy( &(panel_config->print_consumption), msg->data, sizeof(PRINT_CONFIG_CONSUMPTION_INFO_S) );
            pi_log_i( "pnael get consum remian,C:%d, M:%d, Y:%d, K:%d\n",panel_config->print_consumption.tb_c_remain,panel_config->print_consumption.tb_m_remain,panel_config->print_consumption.tb_y_remain,panel_config->print_consumption.tb_k_remain );
            ret = panel_send_data_u8(SETTING, SETTING_CMD_GET_PRINT_CONSUM, NOTIFICATION_RESPONSE, msg->data, msg->data_length);
            break;


        case EVT_TYPE_SCAN_AREA_SIZE_MODIFY:
            if( ( msg->data_length != sizeof(SCAN_PAPER_SIZE_S) ) || msg->data == NULL )
            {
                return;
            }
            pi_memcpy( &(panel_config->adf_fb_size), msg->data, sizeof(SCAN_PAPER_SIZE_S) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_GET_SCAN_AREA_SIZE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            panel_request_wakeup();
            pi_log_d( "get ADF/FB paper size, ADF_size:%d, FB_size:%d \n",panel_config->adf_fb_size.adf_size, panel_config->adf_fb_size.fb_size );
            break;


        case EVT_TYPE_PANEL_SLEEP_TIME_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_SLEEP_TIME );
            if( ret == N_SUCCESS )
            {
                panel_send_data_u8( SETTING, SETTING_CMD_SET_SLEEP_TIME, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                pi_event_mgr_notify( s_panel_evt_client, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY, msg->data, msg->data_length );
            }
            break;

        case EVT_TYPE_NET_ETH_IPV6_DHCP_ADDRESS_CHANGED:
            ///panel don't show ipv6 dhcp, just for send to net
            data_u32 = *( (unsigned int*) msg->data );
            panel_config->ipv6_config.dhcp_enabled = data_u32;
            break;

        case EVT_TYPE_NET_ETH_SWITCH_CHANGED:
            data_u32 = *( (unsigned int*) msg->data );
            panel_config->net_control = data_u32;
            pi_log_d( "eth changed :%d\n",data_u32 );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_NETWORK_ETH_SWITCH, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_STATISTIC_INFO:
            print_statistic_info = ( PRINT_CONFIG_STATISTIC_INFO_S* ) msg->data;
            panel_recv_print_static_page_info_print( print_statistic_info );
            break;

        case EVT_TYPE_PRINT_STATISTIC_COPY_INFO:
            print_statistic_info = ( PRINT_CONFIG_STATISTIC_INFO_S* ) msg->data;
            panel_recv_print_static_page_info_copy( print_statistic_info );
            break;

        case EVT_TYPE_NET_CUSTOM_WATER_MARK_CHANGED:
            panel_set_water_mark( msg->data );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_GET_WATER_MARK, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_SCAN_ENGINE_VERSION_MODIFY:
            pi_log_d("panel get scan engine version\n");
            pi_memcpy( &(panel_config->scan_firmware_version), msg->data, msg->data_length );
            ret = panel_send_data_u8( INFORMATION, INFO_CMD_GET_SCANNER_ENG_VERSION, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_INSTALL_INFO:
            pi_memcpy( &(panel_config->print_install_config), msg->data, msg->data_length );

            pi_log_d("panel recv print install info:%d,%d,%d,%d,%d,%d\n", panel_config->print_install_config.tray_1, panel_config->print_install_config.tray_2,
            panel_config->print_install_config.tray_3,panel_config->print_install_config.tray_4,panel_config->print_install_config.tray_lct_in,panel_config->print_install_config.tray_multi);

            ret = panel_send_data_u8( SETTING, SETTING_CMD_GET_PRINT_TRAY_INSTALL, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_CONSUMPTION_PARAM:
            pi_memcpy( &(panel_config->print_consumption_param), msg->data, msg->data_length );
            pi_log_d("panel recv print consum param:tb c id:%s,\n", panel_config->print_consumption_param.tb_c_id);
            ret = panel_send_data_u8( SETTING, SETTING_CMD_GET_PRINT_CONSUM_PARAM, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_MODIFY:
            pi_memcpy( &(panel_config->print_multi_custom_size), msg->data, msg->data_length );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_CUSTOM1_SIZE_HEIGHT, NOTIFICATION_RESPONSE, (void*)&panel_config->print_multi_custom_size.custom_1.length , sizeof(panel_config->print_multi_custom_size.custom_1.length) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_CUSTOM1_SIZE_WIDTH, NOTIFICATION_RESPONSE, (void*)&panel_config->print_multi_custom_size.custom_1.width, sizeof(panel_config->print_multi_custom_size.custom_1.width) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_CUSTOM2_SIZE_HEIGHT, NOTIFICATION_RESPONSE, (void*)&panel_config->print_multi_custom_size.custom_2.length, sizeof(panel_config->print_multi_custom_size.custom_2.length) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_CUSTOM2_SIZE_WIDTH, NOTIFICATION_RESPONSE, (void*)&panel_config->print_multi_custom_size.custom_2.width, sizeof(panel_config->print_multi_custom_size.custom_2.width) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_CUSTOM3_SIZE_HEIGHT, NOTIFICATION_RESPONSE, (void*)&panel_config->print_multi_custom_size.custom_3.length, sizeof(panel_config->print_multi_custom_size.custom_3.length) );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_PRINT_SET_CUSTOM3_SIZE_WIDTH, NOTIFICATION_RESPONSE, (void*)&panel_config->print_multi_custom_size.custom_3.width, sizeof(panel_config->print_multi_custom_size.custom_3.width) );
            pi_log_d("panel recv print custom paper size:(%u,%u),(%u,%u),(%u,%u)\n", panel_config->print_multi_custom_size.custom_1.length, panel_config->print_multi_custom_size.custom_1.width,
            panel_config->print_multi_custom_size.custom_2.length, panel_config->print_multi_custom_size.custom_2.width, panel_config->print_multi_custom_size.custom_3.length, panel_config->print_multi_custom_size.custom_3.width);
            break;

        case EVT_TYPE_USBDEVICE_CONNECTION_CHANGED:
            data_u32 = *( (uint32_t *)msg->data );
            pi_log_d("panel recv usb connect status:%d\n", data_u32);
            panel_config->usb_connect_status = data_u32;
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_USBDEVICE_CONNECT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_ETH_CONNECTION_CHANGED:
            data_u32 = *( (uint32_t *)msg->data );
            pi_log_d("panel recv net connect status:%d\n", *( (uint32_t *)msg->data ));
            panel_config->net_connect_status = data_u32;
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_ETH_CONNECT_CHANGE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_SCAN_FB_ADF_COVER_STATUS_MODIFY:
            pi_memcpy( &panel_config->scan_fb_adf_cover_state, msg->data, msg->data_length );
            pi_log_d( "panel recv fb open:%d, adf open:%d\n", panel_config->scan_fb_adf_cover_state.fb_cover_status, panel_config->scan_fb_adf_cover_state.adf_cover_status );
            ret = panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_FB_ADF_COVER_STATE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            FB_ADF_open_request_to_wakeup( msg->data, msg->data_length );
            break;

        case EVT_TYPE_SCAN_FB_ADF_SENSOR_MODIFY:
            pi_log_d("panel recv fb/adf sensor data\n");
            ret = panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SENSOR, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_SCAN_FB_ADF_SCANING_MODIFY:
            pi_log_d("panel recv fb/adf sensor data\n");
            ret = panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SCANING_TEST, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_SCAN_ADF_ENGINE_PARAM_SET_MODIFY:
            pi_log_d("panel recv maintenance adf engine param\n");
            ret = panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_SCAN_ADF_ENGINE_PARAM_SET, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case  EVT_TYPE_SCAN_FB_ENGINE_PARAM_SET_MODIFY:
                pi_log_d("panel recv maintenance adf engine param\n");
                ret = panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_SCAN_FB_ENGINE_PARAM_SET, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                break;

        case  EVT_TYPE_SCAN_ENGINE_ERROR_CLEAR_MODIFY:
                pi_log_d("panel recv scan maintenance engine error clear\n");
                ret = panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_SCAN_ENGINE_ERROR_CLEAR, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                break;

        case EVT_TYPE_PLATFORM_SYSTEM_TIME_SYNC:
                panel_get_system_time();
            break;

        case EVT_TYPE_PANEL_SLEEP_MODE_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_SLEEP_MODE );
            if( ret == N_SUCCESS )
            {
                panel_send_data_u8( SETTING, SETTING_CMD_SET_SLEEP_MODE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                pi_event_mgr_notify( s_panel_evt_client, EVT_TYPE_PANEL_SLEEP_MODE_MODIFY, msg->data, msg->data_length );
            }
            break;

        case EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST:

            //notify that restore factory to panel
            panel_send_data_u8( OPERATE, OPERATE_CMD_REQUEST_RESTORE_FACTORY_DEFAULT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED:
            update_email_group_book( msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED:
            update_email_addr_book( msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED:
            update_ftp_book( msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_SMB_ADDRESS_BOOK_CHANGED:
            update_smb_book( msg->data, msg->data_length );
            break;


        case EVT_TYPE_NET_WIFI_SWITCH_CHANGED             :
            pi_log_d("WIFI SWITCH:%d\n",*((uint32_t*)msg->data));
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_SWITCH_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_MAC_CHANGED                :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_MAC_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_CONNECTION_CHANGED         :
            wifi_info = msg->data;
            pi_log_d("wifi connection change info ssid:%s\n",wifi_info->ssid);
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_CONNECTION_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED        :
            wifi_scan_result = msg->data;
            pi_log_d("wifi scan result info count:%d\n",wifi_scan_result->ap_count);
            for(int i = 0; i < wifi_scan_result->ap_count; i++)
            {
                pi_log_d("ssid[%d]:%s\n",i,wifi_scan_result->ap_info[i].ssid);
            }
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_SCAN_RESULT_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED            :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_WPS_PIN_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_WIFI_IPV4_DHCP_SWITCH_CHANGED   :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_DHCP_SWITCH_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV4_ADDRESS_CHANGED       :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_ADDRESS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV4_MASK_CHANGED          :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_MASK_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV4_GATEWAY_CHANGED       :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_GATEWAY_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV4_AUTODNS_SWITCH_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_AUTODNS_SWITCH_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV4_PRIMARY_DNS_CHANGED   :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_PRIMARY_DNS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV4_SECONDARY_DNS_CHANGED :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV4_SECONDARY_DNS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_SWITCH_CHANGED        :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_SWITCH_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_DHCP_SWITCH_CHANGED   :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_DHCP_SWITCH_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_LINK_ADDRESS_CHANGED  :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_LINK_ADDRESS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_STLS_ADDRESS_CHANGED  :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_STLS_ADDRESS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_DHCP_ADDRESS_CHANGED  :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_DHCP_ADDRESS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_PRIMARY_DNS_CHANGED   :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_PRIMARY_DNS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WIFI_IPV6_SECONDARY_DNS_CHANGED :
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_IPV6_SECONDARY_DNS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_SWITCH_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_SWITCH_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_MAC_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_MAC_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_SSID_PREFIX_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_SSID_PREFIX_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_SSID_SUFFIX_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_SSID_SUFFIX_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_PASSWORD_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_PASSWORD_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_IPV4_ADDRESS_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_IPV4_ADDRESS_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_IPV4_MASK_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_IPV4_MASK_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_IPV4_GATEWAY_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_IPV4_GATEWAY_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_REQUEST_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_REQUEST_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WFD_ATTACHED_DEVICES_COUNT_CHANGED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PLATFORM_WIRELESS_ENABLE_MODIFY:
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_SUPPORT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_STORAGEMGR_GET_SATASSD1_ENABLE:
            panel_config->hard_disk_control = *(uint32_t*)msg->data;
            panel_send_data_u8( SETTING, SETTING_CMD_SET_HARD_DISK, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_STORAGEMGR_GET_SATASSD1_ENCRYPT_ENABLE:
            panel_config->hard_disk_encrypt = *(uint32_t*)msg->data;
            panel_send_data_u8( SETTING, SETTING_CMD_SET_HARD_DISK_ENCRYPT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_STORAGEMGR_PUSH_USBDISK_MOUNT_POINT:
            {
                //uint32_t* tmp = (uint32_t*)msg->data;
                const char* path = (const char *)msg->data;

    //            s_udisk_path = (const char*)path;
                pi_log_d("panel recv udisk mount:%s\n",(const char*)path);
                panel_send_data_u8( SETTING, SETTING_CMD_UDISK_MOUNT_PATH, NOTIFICATION_RESPONSE, (void*)path, pi_strlen( (const char*)path ) );
                pi_strncpy( panel_config->udisk_mount_path, (const char*)path, pi_strlen( (const char*)path ) );
                break;
            };

        case EVT_TYPE_NET_ETH_SPEED_CHANGED:
            panel_config->net_init_done = *(uint32_t*)msg->data;
            panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_NET_ETH_SPEED, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_CLEAR_ALL_DATA_END:
            panel_send_data_u8( OPERATE, OPERATE_CMD_SATASSD1_CLEAR_ALL_DATA, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PANEL_COLOR_COPY_ENABLE_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_COLOR_COPY_ENABLE );
            if( N_SUCCESS == ret )
            {
                ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_COLOR_COPY_ENABLE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            }
            break;

        case EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_REQUEST:
            ret = panel_set_setting_to_nvram_str( msg->data, msg->data_length, SETTING_CMD_SET_COLOR_COPY_PASSWORD );
            if( N_SUCCESS == ret )
            {
                ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_COLOR_COPY_PASSWORD, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            }
            break;
        case EVT_TYPE_PANEL_LCD_BACKLIGHT_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_BRIGHTNESS );
            if( N_SUCCESS == ret )
            {
                ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_BRIGHTNESS, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            }
            break;
        case EVT_TYPE_PANEL_SYS_VOLUE_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_BEEP_VOLUE );
            if( N_SUCCESS == ret )
            {
                ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_BEEP_VOLUE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            }
            break;
        case EVT_TYPE_PANEL_DATE_FORMAT_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_DATE_FORMAT );
            if( N_SUCCESS == ret )
            {
                ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_DATE_FORMAT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            }
            break;
        case EVT_TYPE_PANEL_TIME_FORMAT_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_TIME_FORMAT );
            if( N_SUCCESS == ret )
            {
                ret = panel_send_data_u8( SETTING, SETTING_CMD_SET_TIME_FORMAT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            }
            break;
#if CONFIG_SDK_PEDK
        case EVT_TYPE_PEDKMGR_APP_INSTALL_MODIFY:
            panel_recv_app_install_msg( 1, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PEDKMGR_APP_UNINSTALL_MODIFY:
            panel_recv_app_install_msg( 0, msg->data, msg->data_length );
            break;
#endif // CONFIG_SDK_PEDK
        case EVT_TYPE_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED:
            panel_get_storagemgr_hotplugdev_state_changed( msg->data, msg->data_length );
            break;

        case EVT_TYPE_STORAGEMGR_GET_USBDISK_DISABLE:
            panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_USBDISK_DISABLE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_EXPORT_LOG_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_GET_LOG_TO_WEBPAGE_CHOICE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY:
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SYS_LANGUAGE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            panel_config->system_language_code = *(uint16_t*)msg->data;
            pi_log_d("panel recv language:%d\n", panel_config->system_language_code );
            break;

        case EVT_TYPE_STORAGEMGR_PUSH_EMMC_MOUNT_POINT:
            panel_get_overlay_mount_point( msg->data, msg->data_length );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_RWCHECK_MODIFY:
            panel_send_data_u8( SETTING, SETTING_CMD_MAINTENANCE_GET_HARD_DISK_R_W_RET, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_SMTP_SENDER_ADDRESS_CHANGED:
            pi_memset( panel_config->smtp_config.sender_addr, 0, sizeof(panel_config->smtp_config.sender_addr) );
            pi_strncpy( panel_config->smtp_config.sender_addr, (char*)msg->data, pi_strlen((char*)msg->data) );
            pi_log_d( "panel get smtp sender address:%s\n", panel_config->smtp_config.sender_addr );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_SENDER_ADDRESS, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_SMTP_SERVER_ADDRESS_CHANGED:
            pi_memset( panel_config->smtp_config.server_addr, 0, sizeof(panel_config->smtp_config.server_addr) );
            pi_strncpy( panel_config->smtp_config.server_addr, (char*)msg->data, pi_strlen((char*)msg->data) );
            pi_log_d( "panel get smtp server_addr:%s\n", panel_config->smtp_config.server_addr );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_SERVER_ADDRESS, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_SMTP_SERVER_PORT_CHANGED:
            panel_config->smtp_config.server_port = *(uint32_t*)msg->data;
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_SERVER_PORT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_SMTP_SERVER_AUTH_CHANGED:
            panel_config->smtp_config.server_auth = *(uint32_t*)msg->data;
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_SERVER_AUTH, NOTIFICATION_RESPONSE, msg->data, msg->data_length );

            break;

        case EVT_TYPE_NET_SMTP_SEC_MODE_CHANGED:
            panel_config->smtp_config.sec_mode = *(uint32_t*)msg->data;
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_SEC_MODE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_SMTP_USERNAME_CHANGED:
            pi_memset( panel_config->smtp_config.username, 0, sizeof(panel_config->smtp_config.username) );
            pi_strncpy( panel_config->smtp_config.username, (char*)msg->data, pi_strlen((char*)msg->data) );
            pi_log_d( "panel get smtp username:%s\n", panel_config->smtp_config.username );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_USERNAME, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_SMTP_PASSWORD_CHANGED:
            pi_memset( panel_config->smtp_config.password, 0, sizeof(panel_config->smtp_config.password) );
            pi_strncpy( panel_config->smtp_config.password, (char*)msg->data, pi_strlen((char*)msg->data) );
            pi_log_d( "panel get smtp password:%s\n", panel_config->smtp_config.password );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SMTP_PASSWORD, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_PLATFORM_RESTORE_FLAG_MODIFY:
            pi_log_d("panel recv restore flag: %d\n", *(uint8_t*)msg->data );
            panel_send_data_u8( OPERATE, OPERATE_CMD_REQUEST_RESTORE_FACTORY_DEFAULT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_WHITELIST_SWITCH_CHANGED:
            panel_config->whiteList_switch = *(uint32_t*)msg->data;
            pi_log_d( "panel recv net whitlist switch:%d\n", panel_config->whiteList_switch );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_WHITELIST_SWITCH, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PRINT_JOB_CONTROL_MODIFY:
            panel_config->print_job_control = *(uint32_t*)msg->data;
            pi_log_d( "panel recv print job control:%d\n", panel_config->print_job_control );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_PRINT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_SCAN_JOB_CONTROL_MODIFY:
            panel_config->scan_job_control = *(uint32_t*)msg->data;
            pi_log_d( "panel recv scan job control:%d\n", panel_config->scan_job_control );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_SCAN, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;
        case EVT_TYPE_COPY_JOB_CONTROL_MODIFY:
            panel_config->copy_job_control = *(uint32_t*)msg->data;
            pi_log_d( "panel recv copy job control:%d\n", panel_config->copy_job_control );
            panel_send_data_u8( SETTING, SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_COPY, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_NET_IPP_IDENTIFY_ACTION_CHANGED:
            panel_send_data_u8( SETTING, SETTING_CMD_SET_NET_IPP_IDENTIFY_ACTION, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_USBDEVICE_DRIVER_LOAD_STATE:
            pi_log_i( "panel recv usb device load\n" );
            panel_set_usb_control( &(panel_config->usb_control), sizeof(panel_config->usb_control) );
            break;

        case EVT_TYPE_SCAN_PAPER_SIZE_MONO_NUM_MODIFY:
            if( sizeof(scan_paper_mono_s) == msg->data_length )
            {
                pi_memcpy( &scan_paper_mono_s, msg->data,  msg->data_length );
                panel_config->scan_paper_count.a3_num = scan_paper_mono_s.a3_num + scan_paper_color_s.a3_num;
                panel_config->scan_paper_count.a4_num = scan_paper_mono_s.a4_num + scan_paper_color_s.a4_num;
                panel_config->scan_paper_count.a5_num = scan_paper_mono_s.a5_num + scan_paper_color_s.a5_num;
                panel_config->scan_paper_count.a6_num = scan_paper_mono_s.a6_num + scan_paper_color_s.a6_num;
                panel_config->scan_paper_count.jisb5_num = scan_paper_mono_s.jisb5_num + scan_paper_color_s.jisb5_num;
                panel_config->scan_paper_count.jisb6_num = scan_paper_mono_s.jisb6_num + scan_paper_color_s.jisb6_num;
                panel_config->scan_paper_count.letter_num = scan_paper_mono_s.letter_num + scan_paper_color_s.letter_num;
                panel_config->scan_paper_count.legal_num = scan_paper_mono_s.legal_num + scan_paper_color_s.legal_num;
                panel_config->scan_paper_count.paper_8k_num = scan_paper_mono_s.paper_8k_num + scan_paper_color_s.paper_8k_num;
                panel_config->scan_paper_count.big16k_num = scan_paper_mono_s.big16k_num + scan_paper_color_s.big16k_num;
                panel_config->scan_paper_count.other_num = scan_paper_mono_s.other_num + scan_paper_color_s.other_num;
                panel_send_data_u8( SETTING, SETTING_CMD_GET_SCAN_STATISTIC_INFO, NOTIFICATION_RESPONSE, &panel_config->scan_paper_count, msg->data_length );

                pi_log_i( "panel recv scan total page cont:%d\n", panel_config->scan_paper_count.a3_num +  panel_config->scan_paper_count.a4_num +
                panel_config->scan_paper_count.a5_num +  panel_config->scan_paper_count.jisb5_num + panel_config->scan_paper_count.a6_num +
                panel_config->scan_paper_count.jisb6_num + panel_config->scan_paper_count.letter_num + panel_config->scan_paper_count.legal_num +
                panel_config->scan_paper_count.paper_8k_num + panel_config->scan_paper_count.big16k_num + panel_config->scan_paper_count.other_num     );
            }
            else
            {
                pi_log_d("panel recv SCAN_PAPER_SIZE_MONO_NUM error!\n");
            }
            break;
        case EVT_TYPE_SCAN_PAPER_SIZE_COLOR_NUM_MODIFY:
            if( sizeof(scan_paper_color_s) == msg->data_length )
            {
                pi_memcpy( &scan_paper_color_s, msg->data,  msg->data_length );
                panel_config->scan_paper_count.a3_num = scan_paper_mono_s.a3_num + scan_paper_color_s.a3_num;
                panel_config->scan_paper_count.a4_num = scan_paper_mono_s.a4_num + scan_paper_color_s.a4_num;
                panel_config->scan_paper_count.a5_num = scan_paper_mono_s.a5_num + scan_paper_color_s.a5_num;
                panel_config->scan_paper_count.a6_num = scan_paper_mono_s.a6_num + scan_paper_color_s.a6_num;
                panel_config->scan_paper_count.jisb5_num = scan_paper_mono_s.jisb5_num + scan_paper_color_s.jisb5_num;
                panel_config->scan_paper_count.jisb6_num = scan_paper_mono_s.jisb6_num + scan_paper_color_s.jisb6_num;
                panel_config->scan_paper_count.letter_num = scan_paper_mono_s.letter_num + scan_paper_color_s.letter_num;
                panel_config->scan_paper_count.legal_num = scan_paper_mono_s.legal_num + scan_paper_color_s.legal_num;
                panel_config->scan_paper_count.paper_8k_num = scan_paper_mono_s.paper_8k_num + scan_paper_color_s.paper_8k_num;
                panel_config->scan_paper_count.big16k_num = scan_paper_mono_s.big16k_num + scan_paper_color_s.big16k_num;
                panel_config->scan_paper_count.other_num = scan_paper_mono_s.other_num + scan_paper_color_s.other_num;
                panel_send_data_u8( SETTING, SETTING_CMD_GET_SCAN_STATISTIC_INFO, NOTIFICATION_RESPONSE, &panel_config->scan_paper_count, msg->data_length );
                pi_log_i( "panel recv scan total page cont:%d\n", panel_config->scan_paper_count.a3_num +  panel_config->scan_paper_count.a4_num +
                panel_config->scan_paper_count.a5_num +  panel_config->scan_paper_count.jisb5_num + panel_config->scan_paper_count.a6_num +
                panel_config->scan_paper_count.jisb6_num + panel_config->scan_paper_count.letter_num + panel_config->scan_paper_count.legal_num +
                panel_config->scan_paper_count.paper_8k_num + panel_config->scan_paper_count.big16k_num + panel_config->scan_paper_count.other_num     );
            }
            else
            {
                pi_log_d("panel recv SCAN_PAPER_SIZE_COLOR_NUM error!\n");
            }
            break;

        case EVT_TYPE_PRINT_CONSUMPTION_STATUS:
            pi_memcpy( &panel_config->print_consumption_status, msg->data,  msg->data_length );
            panel_send_data_u8( SETTING, SETTING_CMD_GET_PRINT_CONSUMPTION_STATUS, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_i( "panel recv print consumption status,tb_c_status:%d, tb_m_status:%d, tb_y_status:%d, tb_k_status:%d, dr_c_status:%d,\
                        dr_m_status:%d, dr_y_status:%d, dr_k_status:%d, waste_toner_status:%d \n", panel_config->print_consumption_status.tb_c_status, \
                        panel_config->print_consumption_status.tb_m_status, panel_config->print_consumption_status.tb_y_status, \
                        panel_config->print_consumption_status.tb_k_status, panel_config->print_consumption_status.dr_c_status,
                        panel_config->print_consumption_status.dr_m_status, panel_config->print_consumption_status.dr_y_status, \
                        panel_config->print_consumption_status.dr_k_status, panel_config->print_consumption_status.waste_toner_status );
            break;
        case EVT_TYPE_NET_INITIALIZE_DONE:
            pi_log_d("panel recv net init done\n");
            panel_config->net_init_done = 1;
            panel_send_data_u8( SETTING, SETTING_CMD_GET_NET_INITIALIZE_DONE, NOTIFICATION_RESPONSE, &panel_config->net_init_done, sizeof( panel_config->net_init_done ) );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_COMPATCHECK_MODIFY:
            panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_SATASSD1_COMPAT_CHECK, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_d("panel recv COMPATCHECK:%d\n", *(int*)msg->data );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_MODIFY:
            panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_SATASSD1_HEALTH_CHECK, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_d("panel recv HEALTHCHECK:%d\n", *(int*)msg->data );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_FORMAT_MODIFY:
            panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_SATASSD1_FORMAT, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_d("panel recv ssd format:%d\n", *(int*)msg->data );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_FIXSTATE_MODFIY:
            panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_SATASSD1_FIXSTATE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_d("panel recv ssd fix state:%d\n", *(int*)msg->data );
            break;

        case EVT_TYPE_STORAGEMGR_SATASSD1_ACTIVATE_MODIFY:
            panel_send_data_u8( OPERATE, OPERATE_CMD_STORAGEMGR_SATASSD1_ACTIVATE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            pi_log_d("panel recv ssd activate state:%d\n", *(int*)msg->data );
            break;

        case EVT_TYPE_SCAN_ADF_PARAM_SET_SHOW:
            ret = panel_send_data_u8( OPERATE, OPERATE_CMD_ADF_ENGINE_PARAM_SHOW, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        case EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_JOB_ERROR_PROCESS_MODE );
            if( N_SUCCESS == ret )
            {
                panel_send_data_u8( SETTING, SETTING_CMD_SET_JOB_ERROR_PROCESS_MODE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_MODIFY, msg->data, msg->data_length );
            }
            break;

        case EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_REQUEST:
            ret = panel_set_setting_to_nvram_u32( msg->data, msg->data_length, SETTING_CMD_SET_JOB_ERROR_DELETE_TIME );
            if( N_SUCCESS == ret )
            {
                panel_send_data_u8( SETTING, SETTING_CMD_SET_JOB_ERROR_DELETE_TIME, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
                pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_MODIFY, msg->data, msg->data_length );
            }
            break;

        case EVT_TYPE_PLATFORM_MACHINE_TYPE_MODIFY:
            ret = panel_send_data_u8( OPERATE, OPERATE_CMD_MACHINE_TYPE_CHANGE, NOTIFICATION_RESPONSE, msg->data, msg->data_length );
            break;

        default:
            pi_log_e( "unknown event type: %u\n", event_type );
            break;
    }
}

/**
 * @brief register events what panel attention
 */
EVT_MGR_CLI_S * panel_event_register()
{
    EVT_MGR_CLI_S * cli_ptr = pi_event_mgr_create_client( EVT_MODULE_PANEL, panel_event_callback, NULL, NULL );
    if ( cli_ptr == NULL ) {
        return NULL;
    }

    uint32_t modify_event_array[] = {

        /*system status event start*/
        EVT_TYPE_SYSTEMSTATUS_UPDATE,
        EVT_TYPE_SYSTEMJOB_UPDATE,
        /*system status event end*/

        /* platform event start */
        EVT_TYPE_PLATFORM_COUNTRY_CODE_MODIFY,
        EVT_TYPE_PLATFORM_RESTORE_FLAG_MODIFY,

        EVT_TYPE_PLATFORM_FIRMWARE_VERSION_MODIFY,
        EVT_TYPE_PLATFORM_PANEL_FW_VERSION_MODIFY,
        EVT_TYPE_PLATFORM_PDT_NAME_MODIFY,
        EVT_TYPE_PLATFORM_WIRELESS_ENABLE_MODIFY,
        /* platform event end */

        /*net event start*/
        EVT_TYPE_NET_ETH_IPV4_ADDRESS_CHANGED,
        EVT_TYPE_NET_ETH_IPV4_MASK_CHANGED,
        EVT_TYPE_NET_ETH_IPV4_GATEWAY_CHANGED,
        EVT_TYPE_NET_ETH_IPV4_DHCP_SWITCH_CHANGED,
        EVT_TYPE_NET_HOST_NAME_CHANGED,
        EVT_TYPE_NET_ETH_SWITCH_CHANGED,
        EVT_TYPE_NET_ETH_IPV6_SWITCH_CHANGED,
        EVT_TYPE_NET_ETH_IPV6_DHCP_SWITCH_CHANGED,
        EVT_TYPE_NET_CUSTOM_WATER_MARK_CHANGED,
        EVT_TYPE_NET_ETH_CONNECTION_CHANGED,
        EVT_TYPE_NET_ETH_SPEED_CHANGED,

        //address book
        EVT_TYPE_NET_EMAIL_GROUP_LIST_CHANGED,
        EVT_TYPE_NET_EMAIL_ADDRESS_BOOK_CHANGED,
        EVT_TYPE_NET_FTP_ADDRESS_BOOK_CHANGED,
        EVT_TYPE_NET_SMB_ADDRESS_BOOK_CHANGED,

        //wifi
        EVT_TYPE_NET_WIFI_SWITCH_CHANGED             ,
        EVT_TYPE_NET_WIFI_MAC_CHANGED                ,
        EVT_TYPE_NET_WIFI_CONNECTION_CHANGED         ,
        EVT_TYPE_NET_WIFI_SCAN_RESULT_CHANGED        ,
        EVT_TYPE_NET_WIFI_WPS_PIN_CHANGED            ,
        EVT_TYPE_NET_WIFI_IPV4_DHCP_SWITCH_CHANGED   ,
        EVT_TYPE_NET_WIFI_IPV4_ADDRESS_CHANGED       ,
        EVT_TYPE_NET_WIFI_IPV4_MASK_CHANGED          ,
        EVT_TYPE_NET_WIFI_IPV4_GATEWAY_CHANGED       ,
        EVT_TYPE_NET_WIFI_IPV4_AUTODNS_SWITCH_CHANGED,
        EVT_TYPE_NET_WIFI_IPV4_PRIMARY_DNS_CHANGED   ,
        EVT_TYPE_NET_WIFI_IPV4_SECONDARY_DNS_CHANGED ,
        EVT_TYPE_NET_WIFI_IPV6_SWITCH_CHANGED        ,
        EVT_TYPE_NET_WIFI_IPV6_DHCP_SWITCH_CHANGED   ,
        EVT_TYPE_NET_WIFI_IPV6_LINK_ADDRESS_CHANGED  ,
        EVT_TYPE_NET_WIFI_IPV6_STLS_ADDRESS_CHANGED  ,
        EVT_TYPE_NET_WIFI_IPV6_DHCP_ADDRESS_CHANGED  ,
        EVT_TYPE_NET_WIFI_IPV6_PRIMARY_DNS_CHANGED   ,
        EVT_TYPE_NET_WIFI_IPV6_SECONDARY_DNS_CHANGED ,
        EVT_TYPE_NET_WFD_SWITCH_CHANGED              ,
        EVT_TYPE_NET_WFD_MAC_CHANGED                 ,
        EVT_TYPE_NET_WFD_SSID_PREFIX_CHANGED         ,
        EVT_TYPE_NET_WFD_SSID_SUFFIX_CHANGED         ,
        EVT_TYPE_NET_WFD_PASSWORD_CHANGED            ,
        EVT_TYPE_NET_WFD_IPV4_ADDRESS_CHANGED        ,
        EVT_TYPE_NET_WFD_IPV4_MASK_CHANGED           ,
        EVT_TYPE_NET_WFD_IPV4_GATEWAY_CHANGED        ,
        EVT_TYPE_NET_WFD_REQUEST_CHANGED             ,
        EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED ,
        EVT_TYPE_NET_EXPORT_LOG_CHANGED,
        EVT_TYPE_NET_IPP_IDENTIFY_ACTION_CHANGED,

        /*key event start*/
        EVT_TYPE_PANEL_KEY_PRESS,
        /*key event end*/

        /*print event start*/
        EVT_TYPE_PRINT_ENGINE_INFO,
        EVT_TYPE_PRINT_TRAY_INFO,
        EVT_TYPE_PRINT_CONSUMPTION_INFO,
        EVT_TYPE_PRINT_STATISTIC_INFO,
        EVT_TYPE_PRINT_STATISTIC_COPY_INFO,
        EVT_TYPE_PRINT_INSTALL_INFO,
        EVT_TYPE_PRINT_CONSUMPTION_PARAM,
        EVT_TYPE_PRINT_GET_CUSTOM_PAPERSIZE_MODIFY,
        /*print event end*/

        EVT_TYPE_SCAN_AREA_SIZE_MODIFY,
        EVT_TYPE_SCAN_ENGINE_VERSION_MODIFY,

        EVT_TYPE_PANEL_SLEEP_TIME_REQUEST,

        EVT_TYPE_USBDEVICE_CONNECTION_CHANGED,

        EVT_TYPE_SCAN_FB_ADF_COVER_STATUS_MODIFY,

        EVT_TYPE_SCAN_FB_ADF_SENSOR_MODIFY,
        EVT_TYPE_SCAN_FB_ADF_SCANING_MODIFY,
        EVT_TYPE_SCAN_ADF_ENGINE_PARAM_SET_MODIFY,
        EVT_TYPE_SCAN_FB_ENGINE_PARAM_SET_MODIFY,
        EVT_TYPE_SCAN_ENGINE_ERROR_CLEAR_MODIFY,
        EVT_TYPE_PLATFORM_SYSTEM_TIME_SYNC,
        EVT_TYPE_PANEL_SLEEP_MODE_REQUEST,
        EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST,

        /* storage manager */
        EVT_TYPE_STORAGEMGR_GET_SATASSD1_ENABLE,
        EVT_TYPE_STORAGEMGR_GET_SATASSD1_ENCRYPT_ENABLE,
        EVT_TYPE_STORAGEMGR_PUSH_USBDISK_MOUNT_POINT,
        EVT_TYPE_STORAGEMGR_SATASSD1_CLEAR_ALL_DATA_END,
        EVT_TYPE_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED,
        EVT_TYPE_STORAGEMGR_GET_USBDISK_DISABLE,
        EVT_TYPE_STORAGEMGR_PUSH_EMMC_MOUNT_POINT,
        EVT_TYPE_STORAGEMGR_SATASSD1_RWCHECK_MODIFY,

        EVT_TYPE_PANEL_COLOR_COPY_ENABLE_REQUEST,
        EVT_TYPE_PANEL_COLOR_COPY_PASSWORD_REQUEST,
        EVT_TYPE_PANEL_LCD_BACKLIGHT_REQUEST,
        EVT_TYPE_PANEL_SYS_VOLUE_REQUEST,
        EVT_TYPE_PANEL_DATE_FORMAT_REQUEST,
        EVT_TYPE_PANEL_TIME_FORMAT_REQUEST,

        EVT_TYPE_PEDKMGR_APP_INSTALL_MODIFY,
        EVT_TYPE_PEDKMGR_APP_UNINSTALL_MODIFY,
        EVT_TYPE_PLATFORM_LANGUAGE_CODE_MODIFY,

        EVT_TYPE_NET_SMTP_SENDER_ADDRESS_CHANGED,
        EVT_TYPE_NET_SMTP_SERVER_ADDRESS_CHANGED,
        EVT_TYPE_NET_SMTP_SERVER_PORT_CHANGED,
        EVT_TYPE_NET_SMTP_SERVER_AUTH_CHANGED,
        EVT_TYPE_NET_SMTP_SEC_MODE_CHANGED,
        EVT_TYPE_NET_SMTP_USERNAME_CHANGED,
        EVT_TYPE_NET_SMTP_PASSWORD_CHANGED,
        EVT_TYPE_NET_WHITELIST_SWITCH_CHANGED,
        EVT_TYPE_PLATFORM_RESTORE_FLAG_MODIFY,
        EVT_TYPE_COPY_JOB_CONTROL_MODIFY,
        EVT_TYPE_SCAN_JOB_CONTROL_MODIFY,
        EVT_TYPE_PRINT_JOB_CONTROL_MODIFY,

        EVT_TYPE_USBDEVICE_DRIVER_LOAD_STATE,

        EVT_TYPE_PRINT_CONSUMPTION_STATUS,
        EVT_TYPE_NET_INITIALIZE_DONE,
        EVT_TYPE_STORAGEMGR_SATASSD1_COMPATCHECK_MODIFY,
        EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_MODIFY,
        EVT_TYPE_STORAGEMGR_SATASSD1_FORMAT_MODIFY,
        EVT_TYPE_STORAGEMGR_SATASSD1_FIXSTATE_MODFIY,
        EVT_TYPE_STORAGEMGR_SATASSD1_ACTIVATE_MODIFY,
        EVT_TYPE_SCAN_PAPER_SIZE_MONO_NUM_MODIFY,
        EVT_TYPE_SCAN_PAPER_SIZE_COLOR_NUM_MODIFY,
        EVT_TYPE_SCAN_ADF_PARAM_SET_SHOW,

        EVT_TYPE_PANEL_JOB_ERROR_PROCESS_MODE_REQUEST,
        EVT_TYPE_PANEL_JOB_ERROR_DELETE_TIME_REQUEST,
        EVT_TYPE_PLATFORM_MACHINE_TYPE_MODIFY,
    };

    int32_t event_cuont = sizeof( modify_event_array ) / sizeof( modify_event_array[0] );

    pi_event_mgr_register( cli_ptr, modify_event_array, event_cuont );

    return cli_ptr;
}

/**
 * @brief cmd callback for test the system status
 */
 int32_t status_test_callback(int32_t argc, char *argv[])
{   uint32_t status;
    uint32_t status_value;
    uint32_t databuf[2];
    //uint32_t ret = FALSE;

    uint32_t status_type = atoi(argv[0]);
    uint32_t status_module = atoi(argv[1]);
    uint32_t status_id = atoi(argv[2]);
    status_value = atoi(argv[3]);

    status = (status_type<<28 & 0xF0000000) | (status_module<<20 & 0x0FF00000) | (status_id & 0x000FFFFF);

    pi_log_i("test status = %x\n",status);
    pi_log_i("test status = %x\n",status_value);

    databuf[0] = status;
    databuf[1] = status_value;
    panel_send_data_u8( STATUS, STATUS_CMD_STATUS_UPDATE, NOTIFICATION_RESPONSE, databuf, sizeof(databuf) );


    return 0;
}


 /**
  * @brief cmd callback for test the job status
  */
int32_t job_status_test_callback(int32_t argc, char *argv[])
{
    uint32_t ret = FALSE;
    JOBINFO_S job_info;

    job_info.job_id         = atoi(argv[0]);
    job_info.obj            = atoi(argv[1]);
    job_info.status         = atoi(argv[2]);
    job_info.copies        = 1;
    job_info.current_pages  = 888;
    job_info.total_pages    = 999;

    ret = panel_send_data_u8(STATUS, STATUS_CMD_JOB_STATUS_UPDATE, NOTIFICATION_RESPONSE, &job_info, sizeof(job_info));
    if(ret == 0 )
    {
        pi_log_e("notify status fail\n");
    }


    return 0;
}


/**
 * @brief cmd callback for test the setting value
 */
int32_t data_test_callback(int32_t argc, char *argv[])
{   uint32_t cmd;
    uint32_t data_value;

     cmd = atoi(argv[0]);
     data_value = atoi(argv[1]);
     //set_get = atoi(argv[2]);

    setting_cmd_process( cmd, &data_value, sizeof(data_value));
    return 0;
}

/**
 * @brief cmd callback for test the panel init cmd
 */
int32_t init_test_callback( int32_t argc, char *argv[] )
{
    dc_request_shake_hand();
    return 0;
}


/*
int32_t set_wifi(int32_t argc, char *argv[])
{

    WIFI_SCAN_RESULT_S scan_result = {0};
    WIFI_CONN_INFO_S wifi_connect = {0};
    scan_result.ap_count = 3;
    pi_strncpy( scan_result.ap_info[0].ssid, "aaaa", pi_strlen("aaaa") );
    pi_strncpy( scan_result.ap_info[1].ssid, "bbbb", pi_strlen("bbbb") );
    pi_strncpy( scan_result.ap_info[2].ssid, "cccc", pi_strlen("cccc") );

    pi_log_d("wifi scan result info count:%d,sizeof scan_result:%d\n",scan_result.ap_count,sizeof(scan_result));
    for(int i = 0; i < scan_result.ap_count; i++)
    {
        pi_log_d("ssid[%d]:%s\n",i,scan_result.ap_info[i].ssid);
    }
    panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_SCAN_RESULT_CHANGED, NOTIFICATION_RESPONSE, &scan_result, sizeof(scan_result) );

    wifi_connect.status = 1;
    pi_strncpy( wifi_connect.ssid, scan_result.ap_info[1].ssid, pi_strlen(scan_result.ap_info[1].ssid) );
    pi_log_d("wifi connection change info ssid:%s,size wifi_connect:%d\n",wifi_connect.ssid,sizeof(wifi_connect));
    panel_send_data_u8( SETTING, SETTING_CMD_UPDATE_WIFI_CONNECTION_CHANGED, NOTIFICATION_RESPONSE, &wifi_connect, sizeof(wifi_connect) );

    return 1;

}
*/
int32_t panel_cmd_test( int32_t argc, char *argv[] )
{

    int32_t data_0 = atoi(argv[0]);
    int32_t data_1 = atoi(argv[1]);
    char* data_2 = argv[2];

    uint32_t tmp = 1;
    switch (data_0)
    {
        //new
        case 0:
            pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_COMPATCHECK_REQUEST, &tmp, sizeof(tmp));
            pi_log_d("panel request to COMPATCHECK \n" );
            break;
        case 1:
            pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_REQUEST, &tmp, sizeof(tmp));
            pi_log_d("panel request to HEALTHCHECK \n" );
            break;

        case 2:
            pi_event_mgr_notify(s_panel_evt_client, EVT_TYPE_STORAGEMGR_SATASSD1_FORMAT_REQUEST, &tmp, sizeof(tmp));
            pi_log_d("panel request to format ssd \n" );
            break;

        case 3:
            break;

        case 4:
            break;

        default:
            break;
    }

    return 0;

}
/**
 * @brief register the test cmd
 */
void panel_test_cmd( void )
{
    cmd_register( "panel" , "test",  panel_cmd_test,     NULL );

    cmd_register( "panel" , "status",  status_test_callback,     NULL );

    cmd_register( "panel" , "data",  data_test_callback,     NULL );

    cmd_register( "panel" , "job",  job_status_test_callback,     NULL );

    cmd_register( "panel" , "init", init_test_callback,     NULL );

//    cmd_register( "panel" , "wifi", set_wifi,     NULL );
cmd_register( "panel" , "overlay",  overlay_cmd_test_callback,     NULL );


}


/**
 * @brief panel event module register
 */
void panel_event_prolog( void )
{
    s_panel_evt_client = panel_event_register();
    if( s_panel_evt_client == NULL)
    {
        pi_log_e( "panel event init err\n");
        return;
    }


}

/**
 * @brief panel dc init prolog
 */
void panel_init( void )
{
    panel_event_prolog();

    ///cmd for debug
    panel_test_cmd();

    ///init nvram data
    panel_default_param();

    ///shake with panel
    //dc_request_shake_hand();

}


/**
 * @}
 */


