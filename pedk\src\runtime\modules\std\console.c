/*
 * @Author: your name
 * @Date: 2023-12-22 09:38:35
 * @LastEditTime: 2024-01-19 17:12:11
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \runtime\src\runtime\modules\std\console.c
 */
#include "console.h"

#include <stdlib.h>
#include <memory.h>

#include "basic/config.h"
#include "basic/log/log.h"
#include "runtime/utils/quickjs_utils.h"
#include "runtime/runtime.h"

#define JS_LOG_BUF_SIZE 1024

#define CONSOLE "console"

typedef struct LOG_INSTANCE_DATA {
    uint8_t log_buf[JS_LOG_BUF_SIZE];
} LOG_INSTANCE_DATA;

JSValue js_console_log(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    //PeSFRunTime* prt = GET_PESF_RUNTIME(ctx); // 获取运行时
    //LOG_INSTANCE_DATA* console_instance_data; // 定义实例数据指针
    //console_instance_data = prt->get_instance_data(prt, "console"); // 从运行时上获取实例数据

    const char* str = NULL;
    size_t str_len = 0;
    //int cur = 0;
    //size_t n = 0;
    LOG_JS("console","");
    for (int i = 0; i < argc; i++) {
        str = JS_ToCStringLen(ctx, &str_len, argv[i]);
        if (!str) {
            return JS_EXCEPTION;
        }
        //char *tbuf = (char*)malloc(str_len + 1);

        /*if (str_len >= JS_LOG_BUF_SIZE - cur) {
            n = JS_LOG_BUF_SIZE - cur;
        } else {
            n = str_len;
        }*/

        //memcpy(console_instance_data->log_buf + cur, str, n);
        SHOW_MSG("%s", str);
       
        JS_FreeCString(ctx, str);
       
       // cur += n;
    }
    SHOW_MSG("\n");
    return JS_UNDEFINED;
}

static const JSCFunctionListEntry js_console_funcs[] = {
    JS_CFUNC_DEF("log", -1, js_console_log),
};

static const JSCFunctionListEntry js_json_obj[] = {
    JS_OBJECT_DEF("console", js_console_funcs, countof(js_console_funcs), JS_PROP_WRITABLE | JS_PROP_CONFIGURABLE),
};

void js_console_instance(JSContext* ctx)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    LOG_INSTANCE_DATA* console_instance_data;

    JSValue global = JS_GetGlobalObject(ctx);
    JS_SetPropertyFunctionList(ctx, global, js_json_obj, countof(js_json_obj));
    JS_FreeValue(ctx, global);

    // 内部分配实例数据空间，要在generalization时释放调
    console_instance_data = (LOG_INSTANCE_DATA*)malloc(sizeof(LOG_INSTANCE_DATA));
    // 将实例数据指针映射到运行时上
    prt->add_instance_data(prt, "console", (void*)console_instance_data);
}

void js_console_generalization(JSContext* ctx)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx); // 获取运行时
    LOG_INSTANCE_DATA* console_instance_data; // 定义实例数据指针
    console_instance_data = prt->get_instance_data(prt, "console"); // 从运行时上获取实例数据

    // 内部分配实例数据空间，要内部释放
    free(console_instance_data->log_buf);

    // 最后从运行时上删除自己的标记
    prt->del_instance_data(prt, "console");
}