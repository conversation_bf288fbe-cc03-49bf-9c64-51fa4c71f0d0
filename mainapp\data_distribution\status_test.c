/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file status_test.c
 * @addtogroup panel_dc
 * @{
 * @brief provide cmd for test system status
 * <AUTHOR>
 * @date 2023-07-18
 */

#include "status_test.h"
#include "statusid.h"
#include "event_manager/event_mgr.h"
#include <unistd.h>
#include <stdio.h>
#include "panel_event.h"

struct status_id_item
{
    STATUS_ID_E id;
    char *str;
};

static struct status_id_item s_id_item[] =
{
    { STATUS_I_PRINT_INIT , "STATUS_I_PRINT_INIT" },///<打印模块初始化
    { STATUS_I_PRINT_READY   , "STATUS_I_PRINT_READY" },///<打印模块空闲
    { STATUS_I_PRINT_SLEEP   , "STATUS_I_PRINT_SLEEP" },///<打印模块睡眠
    { STATUS_I_PRINT_WARMING , "STATUS_I_PRINT_WARMING" },   ///<打印模块预热
    { STATUS_I_PRINT_PROCESSING         , "STATUS_I_PRINT_PROCESSING" },       ///<打印模块处理中
    { STATUS_I_PRINT_PRINTING           , "STATUS_I_PRINT_PRINTING" },   ///<打印中
    { STATUS_I_PRINT_CANCELING          , "STATUS_I_PRINT_CANCELING" },   ///<打印作业取消中
    { STATUS_I_PRINT_PAUSING , "STATUS_I_PRINT_PAUSING" },   ///<打印作业暂停中
    { STATUS_I_PRINT_ACR_CALIBRATION    , "STATUS_I_PRINT_ACR_CALIBRATION" },///<打印模块校准中
    { STATUS_I_PRINT_WAITING , "STATUS_I_PRINT_WAITING" },   ///<打印模块等待中
    { STATUS_W_PRINT_WASTE_TONER_NEAR_FULL          , "STATUS_W_PRINT_WASTE_TONER_NEAR_FULL" },    ///<废碳粉仓即将满仓
    { STATUS_E_PRINT_WASTE_TONER_FULL   , "STATUS_E_PRINT_WASTE_TONER_FULL" },///<废碳粉仓满仓
    { STATUS_E_PRINT_PARSER_TIMEOUT     , "STATUS_E_PRINT_PARSER_TIMEOUT" },///<解析超时
    { STATUS_E_PRINT_PARSER_ERROR       , "STATUS_E_PRINT_PARSER_ERROR" },       ///<解析错误
    { STATUS_E_PRINT_PARSER_DISCONNECT  , "STATUS_E_PRINT_PARSER_DISCONNECT" },///<连接断开
    { STATUS_E_PRINT_VIDEO   , "STATUS_E_PRINT_VIDEO" },///<图像输出单元异常
    { STATUS_E_PRINT_IMAGE_PROCESS_ERROR            , "STATUS_E_PRINT_IMAGE_PROCESS_ERROR" },    ///<图像处理错误
    { STATUS_E_PRINT_ATTRIBUTE_INCORRECT            , "STATUS_E_PRINT_ATTRIBUTE_INCORRECT" },    ///<非法作业
    { STATUS_E_PRINT_ATTRIBUTE_MUTEX_INCORRECT      , "STATUS_E_PRINT_ATTRIBUTE_MUTEX_INCORRECT" },          ///<非法互斥错误
    { STATUS_E_PRINT_ATTRIBUTE_COMPONENT_INCORRECT  , "STATUS_E_PRINT_ATTRIBUTE_COMPONENT_INCORRECT" },  ///<非法部件错误
    { STATUS_E_PRINT_TRAY_OPEN          , "STATUS_E_PRINT_TRAY_OPEN" },     ///<纸盒打开错误
    { STATUS_E_PRINT_TRAY_EMPTY         , "STATUS_E_PRINT_TRAY_EMPTY" },     ///<纸盒空错误
    { STATUS_E_PRINT_TRAY_SIZE_MISMATCH , "STATUS_E_PRINT_TRAY_SIZE_MISMATCH" }, ///<纸盒纸张不匹错误
    { STATUS_E_PRINT_Y_TB_MISMATCH      , "STATUS_E_PRINT_Y_TB_MISMATCH" },       ///<黄色碳粉盒不匹配
    { STATUS_E_PRINT_M_TB_MISMATCH      , "STATUS_E_PRINT_M_TB_MISMATCH" },       ///<品红色碳粉盒不匹配
    { STATUS_E_PRINT_C_TB_MISMATCH      , "STATUS_E_PRINT_C_TB_MISMATCH" },       ///<青色碳粉盒不匹配
    { STATUS_E_PRINT_K_TB_MISMATCH      , "STATUS_E_PRINT_K_TB_MISMATCH" },       ///<黑色碳粉盒不匹配
    { STATUS_E_PRINT_Y_DR_MISMATCH      , "STATUS_E_PRINT_Y_DR_MISMATCH" },       ///<黄色鼓组件不匹配
    { STATUS_E_PRINT_M_DR_MISMATCH      , "STATUS_E_PRINT_M_DR_MISMATCH" },       ///<品红色鼓组件不匹配
    { STATUS_E_PRINT_C_DR_MISMATCH      , "STATUS_E_PRINT_C_DR_MISMATCH" },       ///<青色鼓组件不匹配
    { STATUS_E_PRINT_K_DR_MISMATCH      , "STATUS_E_PRINT_K_DR_MISMATCH" },       ///<黑色鼓组件不匹配
    { STATUS_E_PRINT_PRINTRUNONEMPTY_Y  , "STATUS_E_PRINT_PRINTRUNONEMPTY_Y" },///<Y彩粉尽作业
    { STATUS_E_PRINT_PRINTRUNONEMPTY_M  , "STATUS_E_PRINT_PRINTRUNONEMPTY_M" },///<M彩粉尽作业
    { STATUS_E_PRINT_PRINTRUNONEMPTY_C  , "STATUS_E_PRINT_PRINTRUNONEMPTY_C" },///<C彩粉尽作业
    { STATUS_E_PRINT_PRINTRUNONEMPTY_WAR            , "STATUS_E_PRINT_PRINTRUNONEMPTY_WAR" },    ///<纸盒里纸张尺寸和任务要求的纸张尺寸不一致
    { STATUS_I_PRINT_TRAY_NEED_CLOSE    , "STATUS_I_PRINT_TRAY_NEED_CLOSE" },///<纸盒未关闭
    { STATUS_I_PRINT_EMMC_NORMAL        , "STATUS_I_PRINT_EMMC_NORMAL" },       ///<正常状态，只有该状态才能支持逐份作业。事实上     IDLE意味着正常，故此状态可无
    { STATUS_I_PRINT_EMMC_FORMATTING    , "STATUS_I_PRINT_EMMC_FORMATTING" },///<内部存储器初始化中，该状态下需要等待格式化完成才能下发作业，作业中不会出现该状态，在等待过程中面板需要提示用户内部存储器正在格式化
    { STATUS_I_PRINT_SAVING  , "STATUS_I_PRINT_SAVING" },   ///<存储中
    { STATUS_E_PRINT_EMMC_FULL          , "STATUS_E_PRINT_EMMC_FULL" },   ///<内部存储器已满，如果是下发作业前出现该状态，说明存储器寿命尽，不能支持逐份，如果是作业中出现该状态，说明逐份页数超出限制，面板需要提示用户取消作业
    { STATUS_E_PRINT_EMMC_ERROR         , "STATUS_E_PRINT_EMMC_ERROR" },       ///<内部存储器发生错误，如果出现该状态，不能支持逐份，面板需要提示用户内部存储器损坏，等待用户点击确定后取消作业
    { STATUS_E_PRINT_UDISK_PARSE_ERROR  , "STATUS_E_PRINT_UDISK_PARSE_ERROR" },///<U盘打印解析错误
    { STATUS_E_PRINT_NETWORK_ACESS_ALARM            , "STATUS_E_PRINT_NETWORK_ACESS_ALARM" },    ///<外网接入警报
    { STATUS_E_PRINT_ENGINE_SYSTEM_ABNORMAL         , "STATUS_E_PRINT_ENGINE_SYSTEM_ABNORMAL" },        ///<引擎系统异常
    { STATUS_E_PRINT_JAM_TRAY_1_SECTION , "STATUS_E_PRINT_JAM_TRAY_1_SECTION" },    ///<纸盒1卡纸
    { STATUS_E_PRINT_JAM_TRAY_2_SECTION , "STATUS_E_PRINT_JAM_TRAY_2_SECTION" },    ///<纸盒2卡纸
    { STATUS_E_PRINT_JAM_TRAY_3_SECTION , "STATUS_E_PRINT_JAM_TRAY_3_SECTION" },    ///<纸盒3卡纸
    { STATUS_E_PRINT_JAM_TRAY_4_SECTION , "STATUS_E_PRINT_JAM_TRAY_4_SECTION" },    ///<纸盒4卡纸
    { STATUS_E_PRINT_JAM_LCT_IN_SECTION , "STATUS_E_PRINT_JAM_LCT_IN_SECTION" },    ///<内置大容量纸盒卡纸
    { STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION        , "STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION" },        ///<多功能进纸器卡纸
    { STATUS_E_PRINT_TRAY_1_OPEN        , "STATUS_E_PRINT_TRAY_1_OPEN" },       ///<纸盒1未插入
    { STATUS_E_PRINT_TRAY_2_OPEN        , "STATUS_E_PRINT_TRAY_2_OPEN" },       ///<纸盒2未插入
    { STATUS_E_PRINT_TRAY_3_OPEN        , "STATUS_E_PRINT_TRAY_3_OPEN" },       ///<纸盒3未插入
    { STATUS_E_PRINT_TRAY_4_OPEN        , "STATUS_E_PRINT_TRAY_4_OPEN" },       ///<纸盒4未插入
    { STATUS_E_PRINT_LCT_IN_OPEN        , "STATUS_E_PRINT_LCT_IN_OPEN" },       ///<内置大容量纸盒未插入
    { STATUS_E_PRINT_TRAY_LCT_IN_SET_ERROR          , "STATUS_E_PRINT_TRAY_LCT_IN_SET_ERROR" },    ///<内置大容量纸盒安装出错
    { STATUS_E_PRINT_TRAY_1_PAPER_EMPTY , "STATUS_E_PRINT_TRAY_1_PAPER_EMPTY" },    ///<纸盒1缺纸
    { STATUS_E_PRINT_TRAY_2_PAPER_EMPTY , "STATUS_E_PRINT_TRAY_2_PAPER_EMPTY" },    ///<纸盒2缺纸
    { STATUS_E_PRINT_TRAY_3_PAPER_EMPTY , "STATUS_E_PRINT_TRAY_3_PAPER_EMPTY" },    ///<纸盒3缺纸
    { STATUS_E_PRINT_TRAY_4_PAPER_EMPTY , "STATUS_E_PRINT_TRAY_4_PAPER_EMPTY" },    ///<纸盒4缺纸
    { STATUS_E_PRINT_LCT_IN_PAPER_EMPTY , "STATUS_E_PRINT_LCT_IN_PAPER_EMPTY" },    ///<内置大容量纸盒缺纸
    { STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY        , "STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY" },        ///<多功能进纸器缺纸
    { STATUS_E_PRINT_TRAY_1_SIZE_ERROR  , "STATUS_E_PRINT_TRAY_1_SIZE_ERROR" },///<纸盒1纸张尺寸不匹配
    { STATUS_E_PRINT_TRAY_2_SIZE_ERROR  , "STATUS_E_PRINT_TRAY_2_SIZE_ERROR" },///<纸盒2纸张尺寸不匹配
    { STATUS_E_PRINT_TRAY_3_SIZE_ERROR  , "STATUS_E_PRINT_TRAY_3_SIZE_ERROR" },///<纸盒3纸张尺寸不匹配
    { STATUS_E_PRINT_TRAY_4_SIZE_ERROR  , "STATUS_E_PRINT_TRAY_4_SIZE_ERROR" },///<纸盒4纸张尺寸不匹配
    { STATUS_E_PRINT_TRAY_MANUAL_SIZE_ERROR         , "STATUS_E_PRINT_TRAY_MANUAL_SIZE_ERROR" },        ///<多功能进纸器纸张尺寸不匹配
    { STATUS_E_PRINT_LCT_IN_SIZE_ERROR  , "STATUS_E_PRINT_LCT_IN_SIZE_ERROR" },///<内置大容量纸盒纸张尺寸不匹配
    { STATUS_W_PRINT_TRAY_1_OPEN        , "STATUS_W_PRINT_TRAY_1_OPEN" },       ///<纸盒1未插入
    { STATUS_W_PRINT_TRAY_2_OPEN        , "STATUS_W_PRINT_TRAY_2_OPEN" },       ///<纸盒2未插入
    { STATUS_W_PRINT_TRAY_3_OPEN        , "STATUS_W_PRINT_TRAY_3_OPEN" },       ///<纸盒3未插入
    { STATUS_W_PRINT_TRAY_4_OPEN        , "STATUS_W_PRINT_TRAY_4_OPEN" },       ///<纸盒4未插入
    { STATUS_W_PRINT_LCT_IN_OPEN        , "STATUS_W_PRINT_LCT_IN_OPEN" },       ///<内置大容量纸盒未插入
    { STATUS_W_PRINT_TRAY_1_PAPER_EMPTY , "STATUS_W_PRINT_TRAY_1_PAPER_EMPTY" },    ///<纸盒1缺纸
    { STATUS_W_PRINT_TRAY_2_PAPER_EMPTY , "STATUS_W_PRINT_TRAY_2_PAPER_EMPTY" },    ///<纸盒2缺纸
    { STATUS_W_PRINT_TRAY_3_PAPER_EMPTY , "STATUS_W_PRINT_TRAY_3_PAPER_EMPTY" },    ///<纸盒3缺纸
    { STATUS_W_PRINT_TRAY_4_PAPER_EMPTY , "STATUS_W_PRINT_TRAY_4_PAPER_EMPTY" },    ///<纸盒4缺纸
    { STATUS_W_PRINT_LCT_IN_PAPER_EMPTY , "STATUS_W_PRINT_LCT_IN_PAPER_EMPTY" },    ///<内置大容量纸盒缺纸
    { STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY        , "STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY" },        ///<纸盒1将缺纸
    { STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY        , "STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY" },        ///<纸盒2将缺纸
    { STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY        , "STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY" },        ///<纸盒3将缺纸
    { STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY        , "STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY" },        ///<纸盒4将缺纸
    { STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY        , "STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY" },        ///<内置大容量纸盒将缺纸
    { STATUS_W_PRINT_LCT_IN_MOVING      , "STATUS_W_PRINT_LCT_IN_MOVING" },       ///<内建大容量纸盒正在移动纸张
    { STATUS_W_PRINT_LCT_IN_PAPER_NEAR_FULL         , "STATUS_W_PRINT_LCT_IN_PAPER_NEAR_FULL" },        ///<内置大容量纸盒将满
    { STATUS_W_PRINT_LCT_IN_PAPER_FULL  , "STATUS_W_PRINT_LCT_IN_PAPER_FULL" },///<内置大容量纸盒满
    { STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL     , "STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL" },///<标准接纸架满
    { STATUS_E_PRINT_TRAY_RECEIVE_1_FULL            , "STATUS_E_PRINT_TRAY_RECEIVE_1_FULL" },    ///<接纸架1满
    { STATUS_E_PRINT_TRAY_RECEIVE_2_FULL            , "STATUS_E_PRINT_TRAY_RECEIVE_2_FULL" },    ///<接纸架2满
    { STATUS_E_PRINT_TRAY_RECEIVE_3_FULL            , "STATUS_E_PRINT_TRAY_RECEIVE_3_FULL" },    ///<接纸架3满
    { STATUS_E_PRINT_FRONT_DOOR_OPEN    , "STATUS_E_PRINT_FRONT_DOOR_OPEN" },///<前盖打开(盖门A打开)
    { STATUS_E_PRINT_SIDE_DOOR_OPEN     , "STATUS_E_PRINT_SIDE_DOOR_OPEN" },///<侧门打开(盖门C打开)
    { STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN         , "STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN" },        ///<内建大容量盖门打开(盖门D打开)
    { STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN  , "STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN" },///<粉盒补给门打开
    { STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN  , "STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN" },///<装订器水平传送单元门开(盖门G打开)
    { STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN            , "STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN" },    ///<装订器前门(盖门H打开)
    { STATUS_E_PRINT_FNS_TOP_COVER_OPEN , "STATUS_E_PRINT_FNS_TOP_COVER_OPEN" },    ///<装订器顶盖(盖门J打开)
    { STATUS_E_PRINT_3RD_TRAY_COVER_OPEN            , "STATUS_E_PRINT_3RD_TRAY_COVER_OPEN" },    ///<第三托盘盖打开
    { STATUS_E_PRINT_Y_TONER_EMPTY      , "STATUS_E_PRINT_Y_TONER_EMPTY" },       ///<黄色碳粉盒空
    { STATUS_E_PRINT_M_TONER_EMPTY      , "STATUS_E_PRINT_M_TONER_EMPTY" },       ///<品红色碳粉盒空
    { STATUS_E_PRINT_C_TONER_EMPTY      , "STATUS_E_PRINT_C_TONER_EMPTY" },       ///<青色碳粉盒空
    { STATUS_E_PRINT_K_TONER_EMPTY      , "STATUS_E_PRINT_K_TONER_EMPTY" },       ///<黑色碳粉盒空
    { STATUS_E_PRINT_W_TB_UNINSTALL     , "STATUS_E_PRINT_W_TB_UNINSTALL" },///<废粉盒缺失或者不响应
    { STATUS_E_PRINT_Y_TB_UNINSTALL     , "STATUS_E_PRINT_Y_TB_UNINSTALL" },///<黄色碳粉盒缺失或者不响应
    { STATUS_E_PRINT_M_TB_UNINSTALL     , "STATUS_E_PRINT_M_TB_UNINSTALL" },///<品红色碳粉盒缺失或者不响应
    { STATUS_E_PRINT_C_TB_UNINSTALL     , "STATUS_E_PRINT_C_TB_UNINSTALL" },///<青色碳粉盒缺失或者不响应
    { STATUS_E_PRINT_K_TB_UNINSTALL     , "STATUS_E_PRINT_K_TB_UNINSTALL" },///<黑色碳粉盒缺失或者不响应
    { STATUS_W_PRINT_Y_TONER_NEAR_EMPTY , "STATUS_W_PRINT_Y_TONER_NEAR_EMPTY" },    ///<黄色碳粉盒粉量低
    { STATUS_W_PRINT_M_TONER_NEAR_EMPTY , "STATUS_W_PRINT_M_TONER_NEAR_EMPTY" },    ///<品红色碳粉盒粉量低
    { STATUS_W_PRINT_C_TONER_NEAR_EMPTY , "STATUS_W_PRINT_C_TONER_NEAR_EMPTY" },    ///<青色碳粉盒粉量低
    { STATUS_W_PRINT_K_TONER_NEAR_EMPTY , "STATUS_W_PRINT_K_TONER_NEAR_EMPTY" },    ///<黑色碳粉盒粉量低
    { STATUS_E_PRINT_Y_DR_UNINSTALL     , "STATUS_E_PRINT_Y_DR_UNINSTALL" },///<黄色鼓组件缺失或者不响应
    { STATUS_E_PRINT_M_DR_UNINSTALL     , "STATUS_E_PRINT_M_DR_UNINSTALL" },///<品红色鼓组件缺失或者不响应
    { STATUS_E_PRINT_C_DR_UNINSTALL     , "STATUS_E_PRINT_C_DR_UNINSTALL" },///<青色鼓组件缺失或者不响应
    { STATUS_E_PRINT_K_DR_UNINSTALL     , "STATUS_E_PRINT_K_DR_UNINSTALL" },///<黑色鼓组件缺失或者不响应
    { STATUS_E_PRINT_Y_DR_LIFE_STOP     , "STATUS_E_PRINT_Y_DR_LIFE_STOP" },///<黄色鼓组件寿命尽
    { STATUS_E_PRINT_M_DR_LIFE_STOP     , "STATUS_E_PRINT_M_DR_LIFE_STOP" },///<品红色鼓组件寿命尽
    { STATUS_E_PRINT_C_DR_LIFE_STOP     , "STATUS_E_PRINT_C_DR_LIFE_STOP" },///<青色鼓组件寿命尽
    { STATUS_E_PRINT_K_DR_LIFE_STOP     , "STATUS_E_PRINT_K_DR_LIFE_STOP" },///<黑色鼓组件寿命尽
    { STATUS_W_PRINT_Y_DR_NEAR_LIFE     , "STATUS_W_PRINT_Y_DR_NEAR_LIFE" },///<黄色鼓组件寿命将尽
    { STATUS_W_PRINT_M_DR_NEAR_LIFE     , "STATUS_W_PRINT_M_DR_NEAR_LIFE" },///<品红色鼓组件寿命将尽
    { STATUS_W_PRINT_C_DR_NEAR_LIFE     , "STATUS_W_PRINT_C_DR_NEAR_LIFE" },///<青色鼓组件寿命将尽
    { STATUS_W_PRINT_K_DR_NEAR_LIFE     , "STATUS_W_PRINT_K_DR_NEAR_LIFE" },///<黑色鼓组件寿命将尽
    { STATUS_E_PRINT_Y_DV_LIFE_STOP     , "STATUS_E_PRINT_Y_DV_LIFE_STOP" },///<黄色显影组件寿命尽
    { STATUS_E_PRINT_M_DV_LIFE_STOP     , "STATUS_E_PRINT_M_DV_LIFE_STOP" },///<品红色显影组件寿命尽
    { STATUS_E_PRINT_C_DV_LIFE_STOP     , "STATUS_E_PRINT_C_DV_LIFE_STOP" },///<青色显影组件寿命尽
    { STATUS_E_PRINT_K_DV_LIFE_STOP     , "STATUS_E_PRINT_K_DV_LIFE_STOP" },///<黑色显影组件寿命尽
    { STATUS_E_PRINT_Y_DV_UNINSTALL     , "STATUS_E_PRINT_Y_DV_UNINSTALL" },///<黄色显影组件缺失或者不响应
    { STATUS_E_PRINT_M_DV_UNINSTALL     , "STATUS_E_PRINT_M_DV_UNINSTALL" },///<品红色显影组件缺失或者不响应
    { STATUS_E_PRINT_C_DV_UNINSTALL     , "STATUS_E_PRINT_C_DV_UNINSTALL" },///<青色显影组件缺失或者不响应
    { STATUS_E_PRINT_K_DV_UNINSTALL     , "STATUS_E_PRINT_K_DV_UNINSTALL" },///<黑色显影组件缺失或者不响应
    { STATUS_W_PRINT_Y_DV_NEAR_END      , "STATUS_W_PRINT_Y_DV_NEAR_END" },       ///<黄色显影组件寿命将尽
    { STATUS_W_PRINT_M_DV_NEAR_END      , "STATUS_W_PRINT_M_DV_NEAR_END" },       ///<品红色显影组件寿命将尽
    { STATUS_W_PRINT_C_DV_NEAR_END      , "STATUS_W_PRINT_C_DV_NEAR_END" },       ///<青色显影组件寿命将尽
    { STATUS_W_PRINT_K_DV_NEAR_END      , "STATUS_W_PRINT_K_DV_NEAR_END" },       ///<黑色显影组件寿命将尽
    { STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION  , "STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION" },///<纸盒1进纸口卡纸
    { STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION  , "STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION" },///<纸盒2进纸口卡纸
    { STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION  , "STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION" },///<纸盒3进纸口卡纸
    { STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION  , "STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION" },///<纸盒4进纸口卡纸
    { STATUS_E_PRINT_JAM_TRAY_MANUAL_PAPER_FEED_SECTION        , "STATUS_E_PRINT_JAM_TRAY_MANUAL_PAPER_FEED_SECTION" },        ///<多功能进纸器卡纸
    { STATUS_E_PRINT_JAM_REFEEDER_SECTION           , "STATUS_E_PRINT_JAM_REFEEDER_SECTION" },    ///<重新进纸处卡纸
    { STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1           , "STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1" },    ///<纵向传输处卡纸
    { STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1     , "STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1" },///<第二转印处卡纸SECTION1
    { STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1  , "STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1" },///<双面传输处卡纸
    { STATUS_E_PRINT_JAM_OUTPUT_SECTION , "STATUS_E_PRINT_JAM_OUTPUT_SECTION" },    ///<出纸处卡纸
    { STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION2           , "STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION2" },    ///<纵向传输处卡纸SECTION2
    { STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2      , "STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2" },        ///<第二转印处卡纸
    { STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION         , "STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION" },        ///<出纸处卡纸
    { STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION        , "STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION" },        ///<重新进纸处卡纸
    { STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2  , "STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2" },///<双面传输处卡纸
    { STATUS_E_PRINT_FUSING_UNIT_LIFE_STOP          , "STATUS_E_PRINT_FUSING_UNIT_LIFE_STOP" },    ///<定影组件寿命尽
    { STATUS_E_PRINT_TRANSFER_BELT_UNIT_LIFE_STOP   , "STATUS_E_PRINT_TRANSFER_BELT_UNIT_LIFE_STOP" },///<转印带寿命尽
    { STATUS_E_PRINT_TRANSFER_ROLLER_UNIT_LIFE_STOP , "STATUS_E_PRINT_TRANSFER_ROLLER_UNIT_LIFE_STOP" },    ///<转印辊寿命尽
    { STATUS_E_PRINT_TONER_FILTER_LIFE_STOP         , "STATUS_E_PRINT_TONER_FILTER_LIFE_STOP" },        ///<粉盒过滤网寿命尽
    { STATUS_W_PRINT_TRANSFER_ROLLER_UNIT_LIFE_END  , "STATUS_W_PRINT_TRANSFER_ROLLER_UNIT_LIFE_END" },///<转印辊寿命将尽
    { STATUS_W_PRINT_TONER_FILTER_LIFE_END          , "STATUS_W_PRINT_TONER_FILTER_LIFE_END" },    ///<粉盒过滤网寿命将尽
    { STATUS_W_PRINT_FUSING_UNIT_LIFE_END           , "STATUS_W_PRINT_FUSING_UNIT_LIFE_END" },    ///<定影组件寿命将尽
    { STATUS_W_PRINT_TRANSFER_BELT_UNIT_LIFE_END    , "STATUS_W_PRINT_TRANSFER_BELT_UNIT_LIFE_END" },///<转印带寿命将尽
    { STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_TRAY_SECTION          , "STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_TRAY_SECTION" },    ///<第一处理托盘处卡纸
    { STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION  , "STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION" },///<接纸架1出纸处卡纸
    { STATUS_E_PRINT_JAM_FNS_2ND_TRAY_DISCHARGE_SECTION        , "STATUS_E_PRINT_JAM_FNS_2ND_TRAY_DISCHARGE_SECTION" },        ///<第二托盘排纸处卡纸
    { STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1         , "STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1" },        ///<传输部分1卡纸
    { STATUS_E_PRINT_JAM_FNS_3RD_TRAY_SECTION       , "STATUS_E_PRINT_JAM_FNS_3RD_TRAY_SECTION" },        ///<第三托盘处
    { STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_TRAY_SECTION          , "STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_TRAY_SECTION" },    ///<第二处理托盘处卡纸
    { STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION       , "STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION" },        ///<装订器入口处卡纸
    { STATUS_E_PRINT_JAM_FNS_HORIZONTAL_TRANSPORT_SECTION      , "STATUS_E_PRINT_JAM_FNS_HORIZONTAL_TRANSPORT_SECTION" },        ///<水平传输处卡纸
    { STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION    , "STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION" },///<折叠通过处卡纸
    { STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION    , "STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION" },///<折叠出纸处卡纸
    { STATUS_E_PRINT_JAM_TRANSPORT_SECTION          , "STATUS_E_PRINT_JAM_TRANSPORT_SECTION" },    ///<传送处卡纸
    { STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION  , "STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION" },///<内建大容量纸盒卡纸
    { STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION   , "STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION" },///<内建大容量纸盒传输处卡纸
    { STATUS_E_PRINT_JAM_SADDLE_STITCHER_ENTRANCE_NOT_ON       , "STATUS_E_PRINT_JAM_SADDLE_STITCHER_ENTRANCE_NOT_ON" },        ///<鞍式装订入口传感器未接通卡纸
    { STATUS_E_PRINT_JAM_SADDLE_STITCHER_EJECTION_NOT_OFF      , "STATUS_E_PRINT_JAM_SADDLE_STITCHER_EJECTION_NOT_OFF" },        ///<鞍式装订出口传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON        , "STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON" },        ///<装订器入口传感器未接通卡纸
    { STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF , "STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF" },    ///<装订器入口传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON  , "STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON" },///<主托盘出纸传感器未接通卡纸
    { STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_OFF , "STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_OFF" },    ///<主托盘出纸传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF , "STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF" },    ///<平订纸张检测传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON       , "STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON" },        ///<次托盘出纸传感器未接通卡纸
    { STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF      , "STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF" },        ///<次托盘出纸传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON       , "STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON" },        ///<折叠出纸传感器未接通卡纸
    { STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF      , "STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF" },        ///<折叠出纸传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_FNS_SADDLE_STITCHER_ENTRANCE_NOT_OFF  , "STATUS_E_PRINT_JAM_FNS_SADDLE_STITCHER_ENTRANCE_NOT_OFF" }, ///<FS534鞍式装订入口传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_TRAY_NOT_ON         , "STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_TRAY_NOT_ON" },    ///<居中对折纸张检测传感器未接通卡纸
    { STATUS_E_PRINT_JAM_ZU_LARGE_SIZE_MODE         , "STATUS_E_PRINT_JAM_ZU_LARGE_SIZE_MODE" },        ///<第一折叠尖端切换卡纸
    { STATUS_E_PRINT_JAM_ZU_ALL_MODE    , "STATUS_E_PRINT_JAM_ZU_ALL_MODE" },///<第二折叠尖端L折卡纸
    { STATUS_E_PRINT_JAM_PUNCH          , "STATUS_E_PRINT_JAM_PUNCH" },   ///<打孔卡纸
    { STATUS_E_PRINT_JAM_FNS_STAPLE     , "STATUS_E_PRINT_JAM_FNS_STAPLE" },///<装订卡纸
    { STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE     , "STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE" },///<鞍式装订卡纸
    { STATUS_E_PRINT_JAM_SADDLE_STITCHER_ENTRANCE_NOT_OFF      , "STATUS_E_PRINT_JAM_SADDLE_STITCHER_ENTRANCE_NOT_OFF" },        ///<鞍式装订入口传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_ON        , "STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_ON" },        ///<托盘3输送传感器未打开卡纸
    { STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_OFF       , "STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_OFF" },        ///<托盘3输送传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_MAIN_PADDLE_HOME_SNR       , "STATUS_E_PRINT_JAM_MAIN_PADDLE_HOME_SNR" },        ///<MAIN_PADDLE_HOME_SNR
    { STATUS_E_PRINT_JAM_EJECT_GRIP     , "STATUS_E_PRINT_JAM_EJECT_GRIP" },///<EJECT_GRIP
    { STATUS_E_PRINT_JAM_ETAMPER        , "STATUS_E_PRINT_JAM_ETAMPER" },       ///<ETAMPER
    { STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON         , "STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON" },        ///<过桥单元入口传感器未接通卡纸
    { STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF        , "STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF" },        ///<过桥单元入口传感器未关闭卡纸
    { STATUS_E_PRINT_JAM_PUNCH_REAR_DETECTION_OFF   , "STATUS_E_PRINT_JAM_PUNCH_REAR_DETECTION_OFF" },///<打空检测传感器未关闭卡纸
    { STATUS_E_PRINT_CONTROL_JAM_1      , "STATUS_E_PRINT_CONTROL_JAM_1" },       ///<控制卡纸1
    { STATUS_E_PRINT_CONTROL_JAM_2      , "STATUS_E_PRINT_CONTROL_JAM_2" },       ///<控制卡纸2
    { STATUS_E_PRINT_CONTROL_JAM_3      , "STATUS_E_PRINT_CONTROL_JAM_3" },       ///<控制卡纸3
    { STATUS_E_PRINT_CONTROL_JAM_4      , "STATUS_E_PRINT_CONTROL_JAM_4" },       ///<控制卡纸4
    { STATUS_E_PRINT_CONTROL_JAM_5      , "STATUS_E_PRINT_CONTROL_JAM_5" },       ///<控制卡纸5
    { STATUS_E_PRINT_CONTROL_JAM_6      , "STATUS_E_PRINT_CONTROL_JAM_6" },       ///<控制卡纸6
    { STATUS_E_PRINT_CONTROL_JAM_7      , "STATUS_E_PRINT_CONTROL_JAM_7" },       ///<控制卡纸7
    { STATUS_E_PRINT_CONTROL_JAM_8      , "STATUS_E_PRINT_CONTROL_JAM_8" },       ///<控制卡纸8
    { STATUS_E_PRINT_CONTROL_JAM_9      , "STATUS_E_PRINT_CONTROL_JAM_9" },       ///<控制卡纸9
    { STATUS_E_PRINT_CONTROL_JAM_10     , "STATUS_E_PRINT_CONTROL_JAM_10" },///<控制卡纸10
    { STATUS_E_PRINT_CONTROL_JAM_11     , "STATUS_E_PRINT_CONTROL_JAM_11" },///<控制卡纸11
    { STATUS_E_PRINT_CONTROL_JAM_12     , "STATUS_E_PRINT_CONTROL_JAM_12" },///<控制卡纸12
    { STATUS_E_PRINT_CONTROL_JAM_13     , "STATUS_E_PRINT_CONTROL_JAM_13" },///<控制卡纸13
    { STATUS_E_PRINT_CONTROL_JAM_14     , "STATUS_E_PRINT_CONTROL_JAM_14" },///<控制卡纸14
    { STATUS_E_PRINT_CONTROL_JAM_15     , "STATUS_E_PRINT_CONTROL_JAM_15" },///<控制卡纸15
    { STATUS_W_PRINT_SIDE_STITCHING_STAPLE_EMPTY    , "STATUS_W_PRINT_SIDE_STITCHING_STAPLE_EMPTY" },///<平订订书钉空
    { STATUS_W_PRINT_STAPLE_NEAR_EMPTY  , "STATUS_W_PRINT_STAPLE_NEAR_EMPTY" },///<平订订书钉将空
    { STATUS_W_PRINT_SADDLE_STITCHING_FRONT_STAPLE_EMPTY       , "STATUS_W_PRINT_SADDLE_STITCHING_FRONT_STAPLE_EMPTY" },        ///<鞍式装订订书钉（前）空
    { STATUS_W_PRINT_SADDLE_STITCHING_REAR_STAPLE_EMPTY        , "STATUS_W_PRINT_SADDLE_STITCHING_REAR_STAPLE_EMPTY" },        ///<鞍式装订订书钉（后）空
    { STATUS_W_PRINT_PUNCH_WASTE_NOT_SET            , "STATUS_W_PRINT_PUNCH_WASTE_NOT_SET" },    ///<打孔废料盒未安装
    { STATUS_W_PRINT_PUNCH_TRASH_BOX_FULL           , "STATUS_W_PRINT_PUNCH_TRASH_BOX_FULL" },    ///<打孔废料盒满
    { STATUS_W_PRINT_NEEDLE_SCARP_OVER  , "STATUS_W_PRINT_NEEDLE_SCARP_OVER" },///<平钉废盒状态
    { STATUS_W_PRINT_MAIN_ABNORMAL_LOWER_STATE      , "STATUS_W_PRINT_MAIN_ABNORMAL_LOWER_STATE" },        ///<主托盘异常下降状态异常
    { STATUS_W_PRINT_MEDIA_SENSOR_ERROR1            , "STATUS_W_PRINT_MEDIA_SENSOR_ERROR1" },    ///<介质传感器异常1
    { STATUS_W_PRINT_BAROMETRIC_PRESSURE_ABNORMALITY           , "STATUS_W_PRINT_BAROMETRIC_PRESSURE_ABNORMALITY" },    ///<气压传感器异常
    { STATUS_W_PRINT_MEDIA_SENSOR_ERROR2            , "STATUS_W_PRINT_MEDIA_SENSOR_ERROR2" },    ///<介质传感器异常2
    { STATUS_W_PRINT_LD_ABNORMALITY     , "STATUS_W_PRINT_LD_ABNORMALITY" },///<激光二极管异常
    { STATUS_W_PRINT_SKEW_ADJUSTMENT_ABNORMALITY    , "STATUS_W_PRINT_SKEW_ADJUSTMENT_ABNORMALITY" },///<纠偏异常
    { STATUS_W_PRINT_IDC_SENSOR_FRONT_ABNORMALITY   , "STATUS_W_PRINT_IDC_SENSOR_FRONT_ABNORMALITY" },///<IDC传感器(前)异常
    { STATUS_W_PRINT_C_DR_DV_ABNORMALITY            , "STATUS_W_PRINT_C_DR_DV_ABNORMALITY" },    ///<青色鼓组件或显影组件异常
    { STATUS_W_PRINT_M_DR_DV_ABNORMALITY            , "STATUS_W_PRINT_M_DR_DV_ABNORMALITY" },    ///<品红色鼓组件或显影组件异常
    { STATUS_W_PRINT_Y_DR_DV_ABNORMALITY            , "STATUS_W_PRINT_Y_DR_DV_ABNORMALITY" },    ///<黄色鼓组件或显影组件异常
    { STATUS_W_PRINT_K_DR_DV_ABNORMALITY            , "STATUS_W_PRINT_K_DR_DV_ABNORMALITY" },    ///<黑色鼓组件或显影组件异常
    { STATUS_W_PRINT_IDC_SENSOR_BACK_ABNORMALITY    , "STATUS_W_PRINT_IDC_SENSOR_BACK_ABNORMALITY" },///<IDC传感器(后)异常
    { STATUS_W_PRINT_COLOR_ERROR_ABNORMAL_TEST_PATTERN         , "STATUS_W_PRINT_COLOR_ERROR_ABNORMAL_TEST_PATTERN" },    ///<色彩对位测试样式异常
    { STATUS_W_PRINT_COLOR_ERROR_ABNORMALITY_CORRECTION_AMOUNT , "STATUS_W_PRINT_COLOR_ERROR_ABNORMALITY_CORRECTION_AMOUNT" }, ///<色彩对位调整异常
    { STATUS_W_PRINT_PH_OPTICAL_SYSTEM_DIRT         , "STATUS_W_PRINT_PH_OPTICAL_SYSTEM_DIRT" },        ///<PH光学系统污染
    { STATUS_W_PRINT_PAPER_WIDTH_ERROR  , "STATUS_W_PRINT_PAPER_WIDTH_ERROR" },///<纸张宽度传感器异常
    { STATUS_W_PRINT_PAPER_TEMPERATURE_ERROR        , "STATUS_W_PRINT_PAPER_TEMPERATURE_ERROR" },        ///<纸张温度传感器异常
    { STATUS_W_PRINT_PH_TEMPERATURE_ERROR           , "STATUS_W_PRINT_PH_TEMPERATURE_ERROR" },    ///<PH温度传感器异常
    { STATUS_W_PRINT_ABNORMAL_2ND_TRANSFER_ATVC     , "STATUS_W_PRINT_ABNORMAL_2ND_TRANSFER_ATVC" },///<第二转印ATVC异常
    { STATUS_W_PRINT_INFLIGHT_TEMPERATURE_ERROR     , "STATUS_W_PRINT_INFLIGHT_TEMPERATURE_ERROR" },///<机内温度传感器异常
    { STATUS_W_PRINT_TACKING_FAN_ABNORMALITY        , "STATUS_W_PRINT_TACKING_FAN_ABNORMALITY" },        ///<平钉风扇异常
    { STATUS_W_PRINT_ABNORMAL_FUSER_SENSOR_TEMPERATURE_DETECTION      , "STATUS_W_PRINT_ABNORMAL_FUSER_SENSOR_TEMPERATURE_DETECTION" },     ///<定影单元温度传感器温度检测异常
    { STATUS_W_PRINT_Y_TONER_EMPTY      , "STATUS_W_PRINT_Y_TONER_EMPTY" },        ///<Y粉盒寿命尽警告
    { STATUS_W_PRINT_M_TONER_EMPTY      , "STATUS_W_PRINT_M_TONER_EMPTY" },        ///<M粉盒寿命尽警告
    { STATUS_W_PRINT_C_TONER_EMPTY      , "STATUS_W_PRINT_C_TONER_EMPTY" },        ///<C粉盒寿命尽警告
    { STATUS_W_PRINT_K_TONER_EMPTY      , "STATUS_W_PRINT_K_TONER_EMPTY" },        ///<K粉盒寿命尽警告
    { STATUS_W_PRINT_STAPLE_EMPTY_3     , "STATUS_W_PRINT_STAPLE_EMPTY_3" }, ///<钉空3警告
    { STATUS_W_PRINT_SELECTED_OUTPUT_SOURCE_FULL    , "STATUS_W_PRINT_SELECTED_OUTPUT_SOURCE_FULL" }, ///<选择出口源满
    { STATUS_W_PRINT_STAPLE_EMPTY_2     , "STATUS_W_PRINT_STAPLE_EMPTY_2" }, ///<钉空2警告
    { STATUS_W_PRINT_FLOD_PAPER_TRAY_LIFT           , "STATUS_W_PRINT_FLOD_PAPER_TRAY_LIFT" },     ///<折叠纸盒抬升警告
    { STATUS_W_PRINT_SADDLE_STITCHING_STAPLE_ACTION_MOVING     , "STATUS_W_PRINT_SADDLE_STITCHING_STAPLE_ACTION_MOVING" },         ///<鞍式装订移动
    { STATUS_W_PRINT_FLAT_BINDING_STAPLER_MOVING    , "STATUS_W_PRINT_FLAT_BINDING_STAPLER_MOVING" }, ///<平钉钉书器移动
    { STATUS_E_PRINT_DUPLEX_DISABLE     , "STATUS_E_PRINT_DUPLEX_DISABLE" },///<禁止双面打印
    { STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_NOT_ON           , "STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_NOT_ON" },    ///<接纸架3出纸传感器未接通卡纸
    { STATUS_E_PRINT_TONER_EMPTY        , "STATUS_E_PRINT_TONER_EMPTY" },        ///<粉尽错误
    { STATUS_E_PRINT_Y_DV_MISMATCH      , "STATUS_E_PRINT_Y_DV_MISMATCH" },        ///<Y通道显影组件不匹配
    { STATUS_E_PRINT_M_DV_MISMATCH      , "STATUS_E_PRINT_M_DV_MISMATCH" },        ///<M通道显影组件不匹配
    { STATUS_E_PRINT_C_DV_MISMATCH      , "STATUS_E_PRINT_C_DV_MISMATCH" },        ///<C通道显影组件不匹配
    { STATUS_E_PRINT_K_DV_MISMATCH      , "STATUS_E_PRINT_K_DV_MISMATCH" },        ///<K通道显影组件不匹配
    { STATUS_E_PRINT_STANDARD_FEEDTRAY_TRAY_SET_ERROR_STATUS   , "STATUS_E_PRINT_STANDARD_FEEDTRAY_TRAY_SET_ERROR_STATUS" },  ///<标准进纸盒设置错误状态
    { STATUS_E_PRINT_FEEDTRAY_2_TRAY_SET_ERROR_STATUS          , "STATUS_E_PRINT_FEEDTRAY_2_TRAY_SET_ERROR_STATUS" },     ///<第二进纸盒设置错误状态
    { STATUS_E_PRINT_FEEDTRAY_LCT_IN_TRAY_SET_ERROR_STATUS     , "STATUS_E_PRINT_FEEDTRAY_LCT_IN_TRAY_SET_ERROR_STATUS" },         ///<内置大容量设置错误状态
    { STATUS_E_PRINT_OPTION_TRAY_FULL   , "STATUS_E_PRINT_OPTION_TRAY_FULL" }, ///<选择纸盒满
    { STATUS_E_PRINT_PROCESS_TRAY_1ST_BIN_FULL      , "STATUS_E_PRINT_PROCESS_TRAY_1ST_BIN_FULL" },         ///<第一处理纸盒满
    { STATUS_E_PRINT_PROCESS_TRAY_2ND_BIN_FULL      , "STATUS_E_PRINT_PROCESS_TRAY_2ND_BIN_FULL" },         ///<第二处理纸盒满
    { STATUS_F_PRINT_A_31_2  , "STATUS_F_PRINT_A_31_2" },   ///<A级致命错误31_2
    { STATUS_F_PRINT_A_34_25 , "STATUS_F_PRINT_A_34_25" },   ///<A级致命错误34_25
    { STATUS_F_PRINT_A_38_22 , "STATUS_F_PRINT_A_38_22" },   ///<A级致命错误38_22
    { STATUS_F_PRINT_A_38_25 , "STATUS_F_PRINT_A_38_25" },   ///<A级致命错误38_25
    { STATUS_F_PRINT_A_38_26 , "STATUS_F_PRINT_A_38_26" },   ///<A级致命错误38_26
    { STATUS_F_PRINT_A_37_22 , "STATUS_F_PRINT_A_37_22" },   ///<A级致命错误37_22
    { STATUS_F_PRINT_A_37_25 , "STATUS_F_PRINT_A_37_25" },   ///<A级致命错误37_25
    { STATUS_F_PRINT_A_37_26 , "STATUS_F_PRINT_A_37_26" },   ///<A级致命错误37_26
    { STATUS_F_PRINT_A_37_32 , "STATUS_F_PRINT_A_37_32" },   ///<A级致命错误37_32
    { STATUS_F_PRINT_A_37_36 , "STATUS_F_PRINT_A_37_36" },   ///<A级致命错误37_36
    { STATUS_F_PRINT_A_37_37 , "STATUS_F_PRINT_A_37_37" },   ///<A级致命错误37_37
    { STATUS_F_PRINT_A_39_22 , "STATUS_F_PRINT_A_39_22" },   ///<A级致命错误39_22
    { STATUS_F_PRINT_A_39_25 , "STATUS_F_PRINT_A_39_25" },   ///<A级致命错误39_25
    { STATUS_F_PRINT_A_39_26 , "STATUS_F_PRINT_A_39_26" },   ///<A级致命错误39_26
    { STATUS_F_PRINT_A_39_2B , "STATUS_F_PRINT_A_39_2B" },   ///<A级致命错误39_2B
    { STATUS_F_PRINT_A_3B_2  , "STATUS_F_PRINT_A_3B_2" },   ///<A级致命错误3B_2
    { STATUS_F_PRINT_A_3B_3  , "STATUS_F_PRINT_A_3B_3" },   ///<A级致命错误3B_3
    { STATUS_F_PRINT_A_3B_7  , "STATUS_F_PRINT_A_3B_7" },   ///<A级致命错误3B_7
    { STATUS_F_PRINT_A_3B_9  , "STATUS_F_PRINT_A_3B_9" },   ///<A级致命错误3B_9
    { STATUS_F_PRINT_B_51_3  , "STATUS_F_PRINT_B_51_3" },   ///<B级致命错误51_3
    { STATUS_F_PRINT_B_22_4  , "STATUS_F_PRINT_B_22_4" },   ///<B级致命错误22_4
    { STATUS_F_PRINT_B_22_53 , "STATUS_F_PRINT_B_22_53" },   ///<B级致命错误22_53
    { STATUS_F_PRINT_B_22_54 , "STATUS_F_PRINT_B_22_54" },   ///<B级致命错误22_54
    { STATUS_F_PRINT_B_22_55 , "STATUS_F_PRINT_B_22_55" },   ///<B级致命错误22_55
    { STATUS_F_PRINT_B_22_56 , "STATUS_F_PRINT_B_22_56" },   ///<B级致命错误22_56
    { STATUS_F_PRINT_B_53_55 , "STATUS_F_PRINT_B_53_55" },   ///<B级致命错误53_55
    { STATUS_F_PRINT_B_53_60 , "STATUS_F_PRINT_B_53_60" },   ///<B级致命错误53_60
    { STATUS_F_PRINT_B_53_61 , "STATUS_F_PRINT_B_53_61" },   ///<B级致命错误53_61
    { STATUS_F_PRINT_B_53_62 , "STATUS_F_PRINT_B_53_62" },   ///<B级致命错误53_62
    { STATUS_F_PRINT_B_53_63 , "STATUS_F_PRINT_B_53_63" },   ///<B级致命错误53_63
    { STATUS_F_PRINT_B_53_58 , "STATUS_F_PRINT_B_53_58" },   ///<B级致命错误53_58
    { STATUS_F_PRINT_B_53_4  , "STATUS_F_PRINT_B_53_4" },   ///<B级致命错误53_4
    { STATUS_F_PRINT_B_33_2  , "STATUS_F_PRINT_B_33_2" },   ///<B级致命错误33_2
    { STATUS_F_PRINT_B_33_6  , "STATUS_F_PRINT_B_33_6" },   ///<B级致命错误33_6
    { STATUS_F_PRINT_B_33_7  , "STATUS_F_PRINT_B_33_7" },   ///<B级致命错误33_7
    { STATUS_F_PRINT_B_23_50 , "STATUS_F_PRINT_B_23_50" },   ///<B级致命错误23_50
    { STATUS_F_PRINT_B_23_57 , "STATUS_F_PRINT_B_23_57" },   ///<B级致命错误23_57
    { STATUS_F_PRINT_B_53_51 , "STATUS_F_PRINT_B_53_51" },   ///<B级致命错误53_51
    { STATUS_F_PRINT_B_53_6  , "STATUS_F_PRINT_B_53_6" },   ///<B级致命错误53_6
    { STATUS_F_PRINT_B_23_55 , "STATUS_F_PRINT_B_23_55" },   ///<B级致命错误23_55
    { STATUS_F_PRINT_B_32_1  , "STATUS_F_PRINT_B_32_1" },   ///<B级致命错误32_1
    { STATUS_F_PRINT_B_32_2  , "STATUS_F_PRINT_B_32_2" },   ///<B级致命错误32_2
    { STATUS_F_PRINT_B_1_6   , "STATUS_F_PRINT_B_1_6" },///<B级致命错误1_6
    { STATUS_F_PRINT_B_1_7   , "STATUS_F_PRINT_B_1_7" },///<B级致命错误1_7
    { STATUS_F_PRINT_B_1_8   , "STATUS_F_PRINT_B_1_8" },///<B级致命错误1_8
    { STATUS_F_PRINT_B_1_9   , "STATUS_F_PRINT_B_1_9" },///<B级致命错误1_9
    { STATUS_F_PRINT_B_21_52 , "STATUS_F_PRINT_B_21_52" },   ///<B级致命错误21_52
    { STATUS_F_PRINT_B_21_53 , "STATUS_F_PRINT_B_21_53" },   ///<B级致命错误21_53
    { STATUS_F_PRINT_B_21_54 , "STATUS_F_PRINT_B_21_54" },   ///<B级致命错误21_54
    { STATUS_F_PRINT_B_21_55 , "STATUS_F_PRINT_B_21_55" },   ///<B级致命错误21_55
    { STATUS_F_PRINT_B_21_56 , "STATUS_F_PRINT_B_21_56" },   ///<B级致命错误21_56
    { STATUS_F_PRINT_B_31_1  , "STATUS_F_PRINT_B_31_1" },   ///<B级致命错误31_1
    { STATUS_F_PRINT_B_31_03 , "STATUS_F_PRINT_B_31_03" },   ///<B级致命错误31_03
    { STATUS_F_PRINT_B_31_04 , "STATUS_F_PRINT_B_31_04" },   ///<B级致命错误31_04
    { STATUS_F_PRINT_B_41_1  , "STATUS_F_PRINT_B_41_1" },   ///<B级致命错误41_1
    { STATUS_F_PRINT_B_45_1  , "STATUS_F_PRINT_B_45_1" },   ///<B级致命错误45_1
    { STATUS_F_PRINT_B_3B_8  , "STATUS_F_PRINT_B_3B_8" },   ///<B级致命错误3B_8
    { STATUS_F_PRINT_B_2_6   , "STATUS_F_PRINT_B_2_6" },///<B级致命错误2_6
    { STATUS_F_PRINT_B_2_4   , "STATUS_F_PRINT_B_2_4" },///<B级致命错误2_4
    { STATUS_F_PRINT_B_2_2   , "STATUS_F_PRINT_B_2_2" },///<B级致命错误2_2
    { STATUS_F_PRINT_B_2_16  , "STATUS_F_PRINT_B_2_16" },   ///<B级致命错误2_16
    { STATUS_F_PRINT_B_2_8   , "STATUS_F_PRINT_B_2_8" },///<B级致命错误2_8
    { STATUS_F_PRINT_B_2_11  , "STATUS_F_PRINT_B_2_11" },   ///<B级致命错误2_11
    { STATUS_F_PRINT_B_2_10  , "STATUS_F_PRINT_B_2_10" },   ///<B级致命错误2_10
    { STATUS_F_PRINT_B_2_14  , "STATUS_F_PRINT_B_2_14" },   ///<B级致命错误2_14
    { STATUS_F_PRINT_B_11_E1 , "STATUS_F_PRINT_B_11_E1" },   ///<B级致命错误11_E1
    { STATUS_F_PRINT_B_11_E2 , "STATUS_F_PRINT_B_11_E2" },   ///<B级致命错误11_E2
    { STATUS_F_PRINT_B_11_A1 , "STATUS_F_PRINT_B_11_A1" },   ///<B级致命错误11_A1
    { STATUS_F_PRINT_B_11_A2 , "STATUS_F_PRINT_B_11_A2" },   ///<B级致命错误11_A2
    { STATUS_F_PRINT_B_11_C5 , "STATUS_F_PRINT_B_11_C5" },   ///<B级致命错误11_C5
    { STATUS_F_PRINT_B_13_1  , "STATUS_F_PRINT_B_13_1" },   ///<B级致命错误13_1
    { STATUS_F_PRINT_B_25_51 , "STATUS_F_PRINT_B_25_51" },   ///<B级致命错误25_51
    { STATUS_F_PRINT_B_25_52 , "STATUS_F_PRINT_B_25_52" },   ///<B级致命错误25_52
    { STATUS_F_PRINT_B_25_53 , "STATUS_F_PRINT_B_25_53" },   ///<B级致命错误25_53
    { STATUS_F_PRINT_B_25_54 , "STATUS_F_PRINT_B_25_54" },   ///<B级致命错误25_54
    { STATUS_F_PRINT_B_25_55 , "STATUS_F_PRINT_B_25_55" },   ///<B级致命错误25_55
    { STATUS_F_PRINT_B_25_56 , "STATUS_F_PRINT_B_25_56" },   ///<B级致命错误25_56
    { STATUS_F_PRINT_B_25_57 , "STATUS_F_PRINT_B_25_57" },   ///<B级致命错误25_57
    { STATUS_F_PRINT_B_25_58 , "STATUS_F_PRINT_B_25_58" },   ///<B级致命错误25_58
    { STATUS_F_PRINT_B_25_59 , "STATUS_F_PRINT_B_25_59" },   ///<B级致命错误25_59
    { STATUS_F_PRINT_B_25_5A , "STATUS_F_PRINT_B_25_5A" },   ///<B级致命错误25_5A
    { STATUS_F_PRINT_B_25_5B , "STATUS_F_PRINT_B_25_5B" },   ///<B级致命错误25_5B
    { STATUS_F_PRINT_B_25_5C , "STATUS_F_PRINT_B_25_5C" },   ///<B级致命错误25_5C
    { STATUS_F_PRINT_B_25_61 , "STATUS_F_PRINT_B_25_61" },   ///<B级致命错误25_61
    { STATUS_F_PRINT_B_25_62 , "STATUS_F_PRINT_B_25_62" },   ///<B级致命错误25_62
    { STATUS_F_PRINT_B_25_63 , "STATUS_F_PRINT_B_25_63" },   ///<B级致命错误25_63
    { STATUS_F_PRINT_B_25_64 , "STATUS_F_PRINT_B_25_64" },   ///<B级致命错误25_64
    { STATUS_F_PRINT_B_11_2  , "STATUS_F_PRINT_B_11_2" },   ///<B级致命错误11_2
    { STATUS_F_PRINT_B_11_3  , "STATUS_F_PRINT_B_11_3" },   ///<B级致命错误11_3
    { STATUS_F_PRINT_B_11_4  , "STATUS_F_PRINT_B_11_4" },   ///<B级致命错误11_4
    { STATUS_F_PRINT_B_11_5  , "STATUS_F_PRINT_B_11_5" },   ///<B级致命错误11_5
    { STATUS_F_PRINT_B_11_6  , "STATUS_F_PRINT_B_11_6" },   ///<B级致命错误11_6
    { STATUS_F_PRINT_B_11_9  , "STATUS_F_PRINT_B_11_9" },   ///<B级致命错误11_9
    { STATUS_F_PRINT_B_11_12 , "STATUS_F_PRINT_B_11_12" },   ///<B级致命错误11_12
    { STATUS_F_PRINT_B_11_13 , "STATUS_F_PRINT_B_11_13" },   ///<B级致命错误11_13
    { STATUS_F_PRINT_B_11_14 , "STATUS_F_PRINT_B_11_14" },   ///<B级致命错误11_14
    { STATUS_F_PRINT_B_11_15 , "STATUS_F_PRINT_B_11_15" },   ///<B级致命错误11_15
    { STATUS_F_PRINT_B_11_24 , "STATUS_F_PRINT_B_11_24" },   ///<B级致命错误11_24
    { STATUS_F_PRINT_B_11_25 , "STATUS_F_PRINT_B_11_25" },   ///<B级致命错误11_25
    { STATUS_F_PRINT_B_11_27 , "STATUS_F_PRINT_B_11_27" },   ///<B级致命错误11_27
    { STATUS_F_PRINT_B_11_30 , "STATUS_F_PRINT_B_11_30" },   ///<B级致命错误11_30
    { STATUS_F_PRINT_B_11_31 , "STATUS_F_PRINT_B_11_31" },   ///<B级致命错误11_31
    { STATUS_F_PRINT_B_11_32 , "STATUS_F_PRINT_B_11_32" },   ///<B级致命错误11_32
    { STATUS_F_PRINT_B_11_40 , "STATUS_F_PRINT_B_11_40" },   ///<B级致命错误11_40
    { STATUS_F_PRINT_B_11_41 , "STATUS_F_PRINT_B_11_41" },   ///<B级致命错误11_41
    { STATUS_F_PRINT_B_11_42 , "STATUS_F_PRINT_B_11_42" },   ///<B级致命错误11_42
    { STATUS_F_PRINT_B_11_43 , "STATUS_F_PRINT_B_11_43" },   ///<B级致命错误11_43
    { STATUS_F_PRINT_B_11_44 , "STATUS_F_PRINT_B_11_44" },   ///<B级致命错误11_44
    { STATUS_F_PRINT_B_11_45 , "STATUS_F_PRINT_B_11_45" },   ///<B级致命错误11_45
    { STATUS_F_PRINT_B_11_46 , "STATUS_F_PRINT_B_11_46" },   ///<B级致命错误11_46
    { STATUS_F_PRINT_B_11_52 , "STATUS_F_PRINT_B_11_52" },   ///<B级致命错误11_52
    { STATUS_F_PRINT_B_11_56 , "STATUS_F_PRINT_B_11_56" },   ///<B级致命错误11_56
    { STATUS_F_PRINT_B_11_84 , "STATUS_F_PRINT_B_11_84" },   ///<B级致命错误11_84
    { STATUS_F_PRINT_B_11_95 , "STATUS_F_PRINT_B_11_95" },   ///<B级致命错误11_95
    { STATUS_F_PRINT_B_11_96 , "STATUS_F_PRINT_B_11_96" },   ///<B级致命错误11_96
    { STATUS_F_PRINT_B_11_97 , "STATUS_F_PRINT_B_11_97" },   ///<B级致命错误11_97
    { STATUS_F_PRINT_B_24_11 , "STATUS_F_PRINT_B_24_11" },   ///<B级致命错误24_11
    { STATUS_F_PRINT_B_24_12 , "STATUS_F_PRINT_B_24_12" },   ///<B级致命错误24_12
    { STATUS_F_PRINT_B_24_13 , "STATUS_F_PRINT_B_24_13" },   ///<B级致命错误24_13
    { STATUS_F_PRINT_B_24_14 , "STATUS_F_PRINT_B_24_14" },   ///<B级致命错误24_14
    { STATUS_F_PRINT_B_2A_11 , "STATUS_F_PRINT_B_2A_11" },   ///<B级致命错误2A_11
    { STATUS_F_PRINT_B_2A_12 , "STATUS_F_PRINT_B_2A_12" },   ///<B级致命错误2A_12
    { STATUS_F_PRINT_B_2A_13 , "STATUS_F_PRINT_B_2A_13" },   ///<B级致命错误2A_13
    { STATUS_F_PRINT_B_2A_14 , "STATUS_F_PRINT_B_2A_14" },   ///<B级致命错误2A_14
    { STATUS_F_PRINT_B_51_2  , "STATUS_F_PRINT_B_51_2" },   ///<B级致命错误51_2
    { STATUS_F_PRINT_C_55_1  , "STATUS_F_PRINT_C_55_1" },   ///<C级致命错误55_1
    { STATUS_F_PRINT_C_39_2A , "STATUS_F_PRINT_C_39_2A" },   ///<C级致命错误39_2A
    { STATUS_F_PRINT_C_C1_63 , "STATUS_F_PRINT_C_C1_63" },   ///<C级致命错误C1_63
    { STATUS_F_PRINT_C_40_A1 , "STATUS_F_PRINT_C_40_A1" },   ///<C级致命错误40_A1
    { STATUS_F_PRINT_C_00_02 , "STATUS_F_PRINT_C_00_02" },   ///<C级致命错误00_02
    { STATUS_F_PRINT_C_56_03 , "STATUS_F_PRINT_C_56_03" },   ///<C级致命错误56_03
    { STATUS_F_PRINT_C_56_20 , "STATUS_F_PRINT_C_56_20" },   ///<C级致命错误56_20
    { STATUS_F_PRINT_C_40_91 , "STATUS_F_PRINT_C_40_91" },   ///<C级致命错误40_91
    { STATUS_F_PRINT_C_40_A2 , "STATUS_F_PRINT_C_40_A2" },   ///<C级致命错误40_A2
    { STATUS_F_PRINT_C_40_A3 , "STATUS_F_PRINT_C_40_A3" },   ///<C级致命错误40_A3
    { STATUS_F_PRINT_C_40_A4 , "STATUS_F_PRINT_C_40_A4" },   ///<C级致命错误40_A4
    { STATUS_F_PRINT_C_40_A5 , "STATUS_F_PRINT_C_40_A5" },   ///<C级致命错误40_A5
    { STATUS_F_PRINT_C_40_A6 , "STATUS_F_PRINT_C_40_A6" },   ///<C级致命错误40_A6
    { STATUS_F_PRINT_C_10_3  , "STATUS_F_PRINT_C_10_3" },   ///<C级致命错误10_3
    { STATUS_F_PRINT_C_10_4  , "STATUS_F_PRINT_C_10_4" },   ///<C级致命错误10_4
    { STATUS_F_PRINT_C_10_14 , "STATUS_F_PRINT_C_10_14" },   ///<C级致命错误10_14
    { STATUS_F_PRINT_C_10_81 , "STATUS_F_PRINT_C_10_81" },   ///<C级致命错误10_81
    { STATUS_F_PRINT_C_10_82 , "STATUS_F_PRINT_C_10_82" },   ///<C级致命错误10_82
    { STATUS_F_PRINT_C_2A_21 , "STATUS_F_PRINT_C_2A_21" },   ///<C级致命错误2A_21
    { STATUS_F_PRINT_C_2A_22 , "STATUS_F_PRINT_C_2A_22" },   ///<C级致命错误2A_22
    { STATUS_F_PRINT_C_2A_23 , "STATUS_F_PRINT_C_2A_23" },   ///<C级致命错误2A_23
    { STATUS_F_PRINT_C_2A_24 , "STATUS_F_PRINT_C_2A_24" },   ///<C级致命错误2A_24
    { STATUS_F_PRINT_C_26_50 , "STATUS_F_PRINT_C_26_50" },   ///<C级致命错误26_50
    { STATUS_F_PRINT_C_26_53 , "STATUS_F_PRINT_C_26_53" },   ///<C级致命错误26_53
    { STATUS_F_PRINT_C_26_52 , "STATUS_F_PRINT_C_26_52" },   ///<C级致命错误26_52
    { STATUS_F_PRINT_C_26_51 , "STATUS_F_PRINT_C_26_51" },   ///<C级致命错误26_51
    { STATUS_F_PRINT_C_26_54 , "STATUS_F_PRINT_C_26_54" },   ///<C级致命错误26_54
    { STATUS_F_PRINT_C_2A_03 , "STATUS_F_PRINT_C_2A_03" },   ///<C级致命错误2A_03
    { STATUS_F_PRINT_C_2A_02 , "STATUS_F_PRINT_C_2A_02" },   ///<C级致命错误2A_02
    { STATUS_F_PRINT_C_2A_01 , "STATUS_F_PRINT_C_2A_01" },   ///<C级致命错误2A_01
    { STATUS_F_PRINT_C_2A_04 , "STATUS_F_PRINT_C_2A_04" },   ///<C级致命错误2A_04
    { STATUS_F_PRINT_C_D7_01 , "STATUS_F_PRINT_C_D7_01" },   ///<C级致命错误D7_01
    { STATUS_F_PRINT_C_D7_02 , "STATUS_F_PRINT_C_D7_02" },   ///<C级致命错误D7_02
    { STATUS_F_PRINT_C_D7_03 , "STATUS_F_PRINT_C_D7_03" },   ///<C级致命错误D7_03
    { STATUS_F_PRINT_C_D7_04 , "STATUS_F_PRINT_C_D7_04" },   ///<C级致命错误D7_04
    { STATUS_F_PRINT_C_D7_05 , "STATUS_F_PRINT_C_D7_05" },   ///<C级致命错误D7_05
    { STATUS_F_PRINT_C_D7_06 , "STATUS_F_PRINT_C_D7_06" },   ///<C级致命错误D7_06
    { STATUS_F_PRINT_C_56_1  , "STATUS_F_PRINT_C_56_1" },   ///<C级致命错误56_1
    { STATUS_F_PRINT_C_56_6  , "STATUS_F_PRINT_C_56_6" },   ///<C级致命错误56_6
    { STATUS_F_PRINT_C_56_10 , "STATUS_F_PRINT_C_56_10" },   ///<C级致命错误56_10
    { STATUS_F_PRINT_C_14_2  , "STATUS_F_PRINT_C_14_2" },   ///<C级致命错误14_2
    { STATUS_F_PRINT_C_14_3  , "STATUS_F_PRINT_C_14_3" },   ///<C级致命错误14_3
    { STATUS_F_PRINT_C_C1_55 , "STATUS_F_PRINT_C_C1_55" },   ///<C级致命错误C1_55
    { STATUS_F_PRINT_C_C1_5B , "STATUS_F_PRINT_C_C1_5B" },   ///<C级致命错误C1_5B
    { STATUS_F_PRINT_C_71_06 , "STATUS_F_PRINT_C_71_06" },   ///<C级致命错误71_06
    { STATUS_F_PRINT_C_71_07 , "STATUS_F_PRINT_C_71_07" },   ///<C级致命错误71_07
    { STATUS_F_PRINT_C_71_11 , "STATUS_F_PRINT_C_71_11" },   ///<C级致命错误71_11
    { STATUS_F_PRINT_C_71_12 , "STATUS_F_PRINT_C_71_12" },   ///<C级致命错误71_12
    { STATUS_F_PRINT_C_71_31 , "STATUS_F_PRINT_C_71_31" },   ///<C级致命错误71_31
    { STATUS_F_PRINT_C_71_33 , "STATUS_F_PRINT_C_71_33" },   ///<C级致命错误71_33
    { STATUS_F_PRINT_C_71_35 , "STATUS_F_PRINT_C_71_35" },   ///<C级致命错误71_35
    { STATUS_F_PRINT_C_71_37 , "STATUS_F_PRINT_C_71_37" },   ///<C级致命错误71_37
    { STATUS_F_PRINT_C_71_32 , "STATUS_F_PRINT_C_71_32" },   ///<C级致命错误71_32
    { STATUS_F_PRINT_C_71_34 , "STATUS_F_PRINT_C_71_34" },   ///<C级致命错误71_34
    { STATUS_F_PRINT_C_71_39 , "STATUS_F_PRINT_C_71_39" },   ///<C级致命错误71_39
    { STATUS_F_PRINT_C_71_41 , "STATUS_F_PRINT_C_71_41" },   ///<C级致命错误71_41
    { STATUS_F_PRINT_C_71_51 , "STATUS_F_PRINT_C_71_51" },   ///<C级致命错误71_51
    { STATUS_F_PRINT_C_71_52 , "STATUS_F_PRINT_C_71_52" },   ///<C级致命错误71_52
    { STATUS_F_PRINT_C_71_53 , "STATUS_F_PRINT_C_71_53" },   ///<C级致命错误71_53
    { STATUS_F_PRINT_C_71_60 , "STATUS_F_PRINT_C_71_60" },   ///<C级致命错误71_60
    { STATUS_F_PRINT_C_71_62 , "STATUS_F_PRINT_C_71_62" },   ///<C级致命错误71_62
    { STATUS_F_PRINT_C_71_63 , "STATUS_F_PRINT_C_71_63" },   ///<C级致命错误71_63
    { STATUS_F_PRINT_C_71_64 , "STATUS_F_PRINT_C_71_64" },   ///<C级致命错误71_64
    { STATUS_F_PRINT_C_72_02 , "STATUS_F_PRINT_C_72_02" },   ///<C级致命错误72_02
    { STATUS_F_PRINT_C_72_06 , "STATUS_F_PRINT_C_72_06" },   ///<C级致命错误72_06
    { STATUS_F_PRINT_C_72_0A , "STATUS_F_PRINT_C_72_0A" },   ///<C级致命错误72_0A
    { STATUS_F_PRINT_C_72_0B , "STATUS_F_PRINT_C_72_0B" },   ///<C级致命错误72_0B
    { STATUS_F_PRINT_C_72_41 , "STATUS_F_PRINT_C_72_41" },   ///<C级致命错误72_41
    { STATUS_F_PRINT_C_72_42 , "STATUS_F_PRINT_C_72_42" },   ///<C级致命错误72_42
    { STATUS_F_PRINT_C_72_43 , "STATUS_F_PRINT_C_72_43" },   ///<C级致命错误72_43
    { STATUS_F_PRINT_C_72_51 , "STATUS_F_PRINT_C_72_51" },   ///<C级致命错误72_51
    { STATUS_F_PRINT_C_73_01 , "STATUS_F_PRINT_C_73_01" },   ///<C级致命错误73_01
    { STATUS_F_PRINT_C_73_02 , "STATUS_F_PRINT_C_73_02" },   ///<C级致命错误73_02
    { STATUS_F_PRINT_C_73_04 , "STATUS_F_PRINT_C_73_04" },   ///<C级致命错误73_04
    { STATUS_F_PRINT_C_73_05 , "STATUS_F_PRINT_C_73_05" },   ///<C级致命错误73_05
    { STATUS_F_PRINT_C_73_10 , "STATUS_F_PRINT_C_73_10" },   ///<C级致命错误73_10
    { STATUS_F_PRINT_C_73_11 , "STATUS_F_PRINT_C_73_11" },   ///<C级致命错误73_11
    { STATUS_F_PRINT_C_73_12 , "STATUS_F_PRINT_C_73_12" },   ///<C级致命错误73_12
    { STATUS_F_PRINT_C_73_13 , "STATUS_F_PRINT_C_73_13" },   ///<C级致命错误73_13
    { STATUS_F_PRINT_C_73_14 , "STATUS_F_PRINT_C_73_14" },   ///<C级致命错误73_14
    { STATUS_F_PRINT_C_73_15 , "STATUS_F_PRINT_C_73_15" },   ///<C级致命错误73_15
    { STATUS_F_PRINT_C_73_17 , "STATUS_F_PRINT_C_73_17" },   ///<C级致命错误73_17
    { STATUS_F_PRINT_C_74_01 , "STATUS_F_PRINT_C_74_01" },   ///<C级致命错误74_01
    { STATUS_F_PRINT_C_74_02 , "STATUS_F_PRINT_C_74_02" },   ///<C级致命错误74_02
    { STATUS_F_PRINT_C_75_01 , "STATUS_F_PRINT_C_75_01" },   ///<C级致命错误75_01
    { STATUS_F_PRINT_C_75_02 , "STATUS_F_PRINT_C_75_02" },   ///<C级致命错误75_02
    { STATUS_F_PRINT_C_76_01 , "STATUS_F_PRINT_C_76_01" },   ///<C级致命错误76_01
    { STATUS_F_PRINT_C_76_02 , "STATUS_F_PRINT_C_76_02" },   ///<C级致命错误76_02
    { STATUS_F_PRINT_C_76_03 , "STATUS_F_PRINT_C_76_03" },   ///<C级致命错误76_03
    { STATUS_F_PRINT_C_76_04 , "STATUS_F_PRINT_C_76_04" },   ///<C级致命错误76_04
    { STATUS_F_PRINT_C_76_05 , "STATUS_F_PRINT_C_76_05" },   ///<C级致命错误76_05
    { STATUS_F_PRINT_C_76_06 , "STATUS_F_PRINT_C_76_06" },   ///<C级致命错误76_06
    { STATUS_F_PRINT_C_76_07 , "STATUS_F_PRINT_C_76_07" },   ///<C级致命错误76_07
    { STATUS_F_PRINT_C_76_0A , "STATUS_F_PRINT_C_76_0A" },   ///<C级致命错误76_0A
    { STATUS_F_PRINT_C_76_0B , "STATUS_F_PRINT_C_76_0B" },   ///<C级致命错误76_0B
    { STATUS_F_PRINT_C_76_0C , "STATUS_F_PRINT_C_76_0C" },   ///<C级致命错误76_0C
    { STATUS_F_PRINT_C_76_0D , "STATUS_F_PRINT_C_76_0D" },   ///<C级致命错误76_0D
    { STATUS_F_PRINT_C_76_0E , "STATUS_F_PRINT_C_76_0E" },   ///<C级致命错误76_0E
    { STATUS_F_PRINT_C_76_0F , "STATUS_F_PRINT_C_76_0F" },   ///<C级致命错误76_0F
    { STATUS_F_PRINT_C_76_10 , "STATUS_F_PRINT_C_76_10" },   ///<C级致命错误76_10
    { STATUS_F_PRINT_C_76_22 , "STATUS_F_PRINT_C_76_22" },   ///<C级致命错误76_22
    { STATUS_F_PRINT_C_76_23 , "STATUS_F_PRINT_C_76_23" },   ///<C级致命错误76_23
    { STATUS_F_PRINT_C_76_24 , "STATUS_F_PRINT_C_76_24" },   ///<C级致命错误76_24
    { STATUS_F_PRINT_C_76_31 , "STATUS_F_PRINT_C_76_31" },   ///<C级致命错误76_31
    { STATUS_F_PRINT_C_76_33 , "STATUS_F_PRINT_C_76_33" },   ///<C级致命错误76_33
    { STATUS_F_PRINT_C_56_5  , "STATUS_F_PRINT_C_56_5" },   ///<C级致命错误56_5
    { STATUS_F_PRINT_A_37_38 , "STATUS_F_PRINT_A_37_38" },   ///<A级致命错误37_38
    //PRINT STATUS END
    //SCAN STATUS BEGIN
    { STATUS_I_SCAN_INIT  , "STATUS_I_SCAN_INIT" },///<扫描模块初始化中
    { STATUS_I_SCAN_IDLE  , "STATUS_I_SCAN_IDLE" },///<扫描模块就绪
    { STATUS_I_SCAN_SLEEP , "STATUS_I_SCAN_SLEEP" },///<扫描模块睡眠
    { STATUS_I_SCAN_PROCESSING          , "STATUS_I_SCAN_PROCESSING" },   ///<扫描处理中
    { STATUS_I_SCAN_RUNNING  , "STATUS_I_SCAN_RUNNING" },   ///<扫描中
    { STATUS_I_SCAN_CANCELING           , "STATUS_I_SCAN_CANCELING" },   ///<扫描取消中
    { STATUS_I_SCAN_TOFILE_SENDING      , "STATUS_I_SCAN_TOFILE_SENDING" },       ///<扫描文件正在发送中....
    { STATUS_I_SCAN_NEXT_PAGE_WAITING   , "STATUS_I_SCAN_NEXT_PAGE_WAITING" },///<等待扫描下一页状态
    { STATUS_I_SCAN_FINISHED , "STATUS_I_SCAN_FINISHED" },   ///<扫描完成
    { STATUS_I_SCAN_TO_FILE_UDISK_SAVING            , "STATUS_I_SCAN_TO_FILE_UDISK_SAVING" },    ///<扫描文件正在写入U盘
    { STATUS_I_SCAN_TO_FILE_SENT        , "STATUS_I_SCAN_TO_FILE_SENT" },       ///<扫描文件发送完成
    { STATUS_I_SCAN_LOCKED   , "STATUS_I_SCAN_LOCKED" },///<扫描仪已加锁
    { STATUS_I_SCAN_ADF_PAPER_PRESENT   , "STATUS_I_SCAN_ADF_PAPER_PRESENT" },///<ADF纸张存在
    { STATUS_I_SCAN_ADF_PAPER_REMOVED   , "STATUS_I_SCAN_ADF_PAPER_REMOVED" },///<ADF纸张移除
    { STATUS_I_SCAN_PUT_PAPER_TO_ADF    , "STATUS_I_SCAN_PUT_PAPER_TO_ADF" },///<请放纸到ADF
    { STATUS_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF       , "STATUS_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF" },        ///<等待ADF手动双面翻面确认
    { STATUS_I_SCAN_OUT_TO_EML_SUCCESS  , "STATUS_I_SCAN_OUT_TO_EML_SUCCESS" },///<扫描到email成功
    { STATUS_I_SCAN_OUT_TO_FTP_SUCCESS  , "STATUS_I_SCAN_OUT_TO_FTP_SUCCESS" },///<扫描到FTP成功
    { STATUS_I_SCAN_OUT_TO_UDISK_SUCCESS            , "STATUS_I_SCAN_OUT_TO_UDISK_SUCCESS" },    ///<扫描到U盘成功
    { STATUS_I_SCAN_OUT_TO_WSD_SUCCESS  , "STATUS_I_SCAN_OUT_TO_WSD_SUCCESS" },///<扫描到WSD成功
    { STATUS_I_SCAN_OUT_TO_AIRSCAN_SUCCESS          , "STATUS_I_SCAN_OUT_TO_AIRSCAN_SUCCESS" },    ///<扫描到AIRSCAN成功
    { STATUS_I_SCAN_OUT_TO_EML_CANCEL   , "STATUS_I_SCAN_OUT_TO_EML_CANCEL" },///<扫描到email取消
    { STATUS_I_SCAN_OUT_TO_FTP_CANCEL   , "STATUS_I_SCAN_OUT_TO_FTP_CANCEL" },///<扫描到FTP取消
    { STATUS_I_SCAN_OUT_TO_UDISK_CANCEL , "STATUS_I_SCAN_OUT_TO_UDISK_CANCEL" },    ///<扫描到U盘取消
    { STATUS_I_SCAN_OUT_TO_WSD_CANCEL   , "STATUS_I_SCAN_OUT_TO_WSD_CANCEL" },///<扫描到WSD取消
    { STATUS_I_SCAN_OUT_TO_AIRSCAN_CANCEL           , "STATUS_I_SCAN_OUT_TO_AIRSCAN_CANCEL" },    ///<扫描到AIRSCAN取消
    { STATUS_E_SCAN_ADF_PAPER_OUT       , "STATUS_E_SCAN_ADF_PAPER_OUT" },       ///<ADF缺纸
    { STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT         , "STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT" },        ///<ADF正面进纸失败
    { STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK          , "STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK" },    ///<ADF背面进纸失败
    { STATUS_E_SCAN_ADF_COVER_OPEN      , "STATUS_E_SCAN_ADF_COVER_OPEN" },       ///<ADF开盖
    { STATUS_E_SCAN_ADF_PAPER_MISMATCH  , "STATUS_E_SCAN_ADF_PAPER_MISMATCH" },///<ADF纸型不匹配
    { STATUS_E_SCAN_FB_COVER_OPEN       , "STATUS_E_SCAN_FB_COVER_OPEN" },       ///<FB开盖
    { STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN       , "STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN" },       ///<ADF卡纸
    { STATUS_E_SCAN_MEMORY_LOW          , "STATUS_E_SCAN_MEMORY_LOW" },   ///<扫描模块内存低
    { STATUS_E_SCAN_COMMUNICATION_ERR_21	        , " STATUS_E_SCAN_COMMUNICATION_ERR_21	" },      ///<通讯失败: 21 扫描数据传输超时
    { STATUS_E_SCAN_COMMUNICATION_ERR_22	        , " STATUS_E_SCAN_COMMUNICATION_ERR_22	" },      ///<通讯失败: 22 无效的指令
    { STATUS_E_SCAN_COMMUNICATION_ERR_23	        , " STATUS_E_SCAN_COMMUNICATION_ERR_23	" },      ///<通讯失败: 23 无效的参数
    { STATUS_E_SCAN_COMMUNICATION_ERR_24	        , " STATUS_E_SCAN_COMMUNICATION_ERR_24	" },      ///<通讯失败: 24 (保留)
    { STATUS_E_SCAN_COMMUNICATION_ERR_25	        , " STATUS_E_SCAN_COMMUNICATION_ERR_25	" },      ///<通讯失败: 25 USB连接出错
    { STATUS_E_SCAN_COMMUNICATION_ERR_26	        , " STATUS_E_SCAN_COMMUNICATION_ERR_26	" },      ///<通讯失败: 26 网络连接出错
    { STATUS_E_SCAN_COMMUNICATION_ERR_27	        , " STATUS_E_SCAN_COMMUNICATION_ERR_27	" },      ///<通讯失败: 27 wifi连接出错
    { STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT    , "STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT " }, ///<PCIE通讯超时
    { STATUS_E_SCAN_TO_FILE_ABORTED     , "STATUS_E_SCAN_TO_FILE_ABORTED" },///<扫描到文件出错
    { STATUS_E_SCAN_TO_FILE_PASSWORD_WRONG          , "STATUS_E_SCAN_TO_FILE_PASSWORD_WRONG" },    ///<用户名或密码错误
    { STATUS_E_SCAN_TO_FILE_FILE_OVERSIZE           , "STATUS_E_SCAN_TO_FILE_FILE_OVERSIZE" },    ///<文件大小超出设定范围，过大
    { STATUS_E_SCAN_TO_FILE_SERVER_OVERSIZE         , "STATUS_E_SCAN_TO_FILE_SERVER_OVERSIZE" },        ///<文件大小超过服务器限制
    { STATUS_E_SCAN_TO_FILE_SEND_FAILED , "STATUS_E_SCAN_TO_FILE_SEND_FAILED" },    ///<文件发送失败
    { STATUS_E_SCAN_TO_FILE_VOLUME_LOW  , "STATUS_E_SCAN_TO_FILE_VOLUME_LOW" },///<扫描到文件容量不足
    { STATUS_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE    , "STATUS_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE" },///<U盘容量不足
    { STATUS_E_SCAN_TO_FILE_UDISK_ABORTED           , "STATUS_E_SCAN_TO_FILE_UDISK_ABORTED" },    ///<扫描到U盘写文件失败
    { STATUS_E_SCAN_TO_FILE_PC_NO_RESPONSE          , "STATUS_E_SCAN_TO_FILE_PC_NO_RESPONSE" },    ///<PC无响应
    { STATUS_E_SCAN_EML_ESYS , "STATUS_E_SCAN_EML_ESYS" },   ///<扫描到email系统故障
    { STATUS_E_SCAN_EML_EUSER           , "STATUS_E_SCAN_EML_EUSER" },   ///<扫描到email用户名错误
    { STATUS_E_SCAN_EML_ECONN           , "STATUS_E_SCAN_EML_ECONN" },   ///<扫描到email连接失败
    { STATUS_E_SCAN_EML_ETRAN           , "STATUS_E_SCAN_EML_ETRAN" },   ///<扫描到email传输失败
    { STATUS_E_SCAN_EML_EPASS           , "STATUS_E_SCAN_EML_EPASS" },   ///<扫描到email密码错误
    { STATUS_E_SCAN_EML_EFROM           , "STATUS_E_SCAN_EML_EFROM" },   ///<扫描到email发件人错误
    { STATUS_E_SCAN_EML_ETO  , "STATUS_E_SCAN_EML_ETO" },   ///<扫描到email收件人错误
    { STATUS_E_SCAN_EML_EATT_ACCESS     , "STATUS_E_SCAN_EML_EATT_ACCESS" },///<扫描到email附件不可访问（文件不存在等）
    { STATUS_E_SCAN_EML_EATT_TOO_BIG    , "STATUS_E_SCAN_EML_EATT_TOO_BIG" },///<扫描到email附件过大
    { STATUS_E_SCAN_EML_ELIMIT          , "STATUS_E_SCAN_EML_ELIMIT" },   ///<扫描到email邮件大小超过了服务器限制
    { STATUS_E_SCAN_EML_ESERVER         , "STATUS_E_SCAN_EML_ESERVER" },       ///<扫描到email服务器响应了其它错误信息
    { STATUS_E_SCAN_EML_MEM_LOW         , "STATUS_E_SCAN_EML_MEM_LOW" },       ///<扫描到email内存不足
    { STATUS_E_SCAN_SMB_SERVER_OVERSIZE , "STATUS_E_SCAN_SMB_SERVER_OVERSIZE" },    ///<扫描到SMB服务器磁盘满
    { STATUS_E_SCAN_SMB_SENDFAIL        , "STATUS_E_SCAN_SMB_SENDFAIL" },       ///<扫描到SMB文件上传失败
    { STATUS_E_SCAN_SMB_DIR  , "STATUS_E_SCAN_SMB_DIR" },   ///<扫描到SMB目录错误
    { STATUS_E_SCAN_SMB_HOSTNAME        , "STATUS_E_SCAN_SMB_HOSTNAME" },       ///<扫描到SMB主机名错误
    { STATUS_E_SCAN_SMB_USER_PASS       , "STATUS_E_SCAN_SMB_USER_PASS" },       ///<扫描到SMB用户名或密码错误
    { STATUS_E_SCAN_SMB_SERVER_DISCONN  , "STATUS_E_SCAN_SMB_SERVER_DISCONN" },///<扫描到SMB服务器网络断开
    { STATUS_E_SCAN_SMB_NET_DISCONN     , "STATUS_E_SCAN_SMB_NET_DISCONN" },///<扫描到SMB打印机网络断开
    { STATUS_E_SCAN_FTP_ESYS , "STATUS_E_SCAN_FTP_ESYS" },   ///<扫描到FTP系统故障
    { STATUS_E_SCAN_FTP_ECONN           , "STATUS_E_SCAN_FTP_ECONN" },   ///<扫描到FTP连接失败
    { STATUS_E_SCAN_FTP_ETRAN           , "STATUS_E_SCAN_FTP_ETRAN" },   ///<扫描到FTP传输失败
    { STATUS_E_SCAN_FTP_EUSER           , "STATUS_E_SCAN_FTP_EUSER" },   ///<扫描到FTP用户名错误
    { STATUS_E_SCAN_FTP_EPASS           , "STATUS_E_SCAN_FTP_EPASS" },   ///<扫描到FTP密码错误
    { STATUS_E_SCAN_FTP_EFILE_ACCESS    , "STATUS_E_SCAN_FTP_EFILE_ACCESS" },///<扫描到FTP文件访问失败
    { STATUS_E_SCAN_FTP_ESERVPATH       , "STATUS_E_SCAN_FTP_ESERVPATH" },       ///<扫描到FTP服务器路径错误
    { STATUS_E_SCAN_FTP_ESERVER         , "STATUS_E_SCAN_FTP_ESERVER" },       ///<扫描到FTP服务器响应了其它错误信息
    { STATUS_E_SCAN_WSD_QIOERR          , "STATUS_E_SCAN_WSD_QIOERR" },   ///<扫描到WSD读取IO错误
    { STATUS_E_SCAN_WSD_COMM , "STATUS_E_SCAN_WSD_COMM" },   ///<扫描到WSD连接失败
    { STATUS_E_SCAN_WSD_LOWMEM          , "STATUS_E_SCAN_WSD_LOWMEM" },   ///<扫描到WSD内存不足
    { STATUS_E_SCAN_WSD_FILE , "STATUS_E_SCAN_WSD_FILE" },   ///<扫描到WSD文件失败
    { STATUS_E_SCAN_AIRSCAN_ESYS        , "STATUS_E_SCAN_AIRSCAN_ESYS" },       ///<扫描到Airscan系统故障
    { STATUS_E_SCAN_AIRSCAN_QIOERR      , "STATUS_E_SCAN_AIRSCAN_QIOERR" },       ///<扫描到Airscan读取IO错误
    { STATUS_E_SCAN_AIRSCAN_ECONN       , "STATUS_E_SCAN_AIRSCAN_ECONN" },       ///<扫描到Airscan连接失败
    { STATUS_E_SCAN_AIRSCAN_ETRAN       , "STATUS_E_SCAN_AIRSCAN_ETRAN" },       ///<扫描到Airscan传输失败
    { STATUS_F_SCAN_ADF_COOLING_FAN     , "STATUS_F_SCAN_ADF_COOLING_FAN" },///<ADF冷却风扇异常
    { STATUS_F_SCAN_ADF_CANTACT_RETRACT_MECH          , "STATUS_F_SCAN_ADF_CANTACT_RETRACT_MECH" },      ///<ADF扫描入口前方连接装置异常
    { STATUS_F_SCAN_ADF_BRUSH_MOVEMENT  , "STATUS_F_SCAN_ADF_BRUSH_MOVEMENT" },///<ADF刷子偏移
    { STATUS_F_SCAN_ADF_INIT_TIMEOUT    , "STATUS_F_SCAN_ADF_INIT_TIMEOUT" },///<ADF初始化超时
    { STATUS_F_SCAN_FB_EXPOSURE_ON_FAIL  , "STATUS_F_SCAN_FB_EXPOSURE_ON_FAIL" },  ///<FB校准灯光开启失败
    { STATUS_F_SCAN_FB_EXPOSURE_LAMP_IPL     , "STATUS_F_SCAN_FB_EXPOSURE_LAMP_IPL" },        ///<FB曝光灯无规律亮灯
    { STATUS_F_SCAN_FB_DRIVE_SYS_HOME_ABNORMAL       , "STATUS_F_SCAN_FB_DRIVE_SYS_HOME_ABNORMAL" },        ///<FB驱动系统home传感器异常
    { STATUS_F_SCAN_FB_SLIDER_OVERRUNNING           , "STATUS_F_SCAN_FB_SLIDER_OVERRUNNING" },    ///<FB滑动器超限
    { STATUS_F_SCAN_FB_SCAN_SEQ_TROUBLE_1   , "STATUS_F_SCAN_FB_SCAN_SEQ_TROUBLE_1" },///<FB序列错误1
    { STATUS_F_SCAN_FB_EMMC_ABNORMAL    , "STATUS_F_SCAN_FB_EMMC_ABNORMAL" },///<FB-EMMC异常
    { STATUS_F_SCAN_FB_CCD_GAIN_ADJ_ABNORMAL      , "STATUS_F_SCAN_FB_CCD_GAIN_ADJ_ABNORMAL" },        ///<FB-CCD灯光校准异常
    { STATUS_F_SCAN_FB_ABN_IMAGE_PROCESS_CLK    , "STATUS_F_SCAN_FB_ABN_IMAGE_PROCESS_CLK" },///<FB图像处理同步时钟输入异常
    { STATUS_F_SCAN_FB_CCD_ABNORMAL_POWER , "STATUS_F_SCAN_FB_CCD_ABNORMAL_POWER" }, ///<FB-CCD电源电压异常
    { STATUS_F_SCAN_FB_DF_EXPOSURE_ON_FAIL   , "STATUS_F_SCAN_FB_DF_EXPOSURE_ON_FAIL" },  ///<FB-DF曝光灯光开启异常
    { STATUS_F_SCAN_FB_DF_EXPOSURE_LAMP_IPL  , "STATUS_F_SCAN_FB_DF_EXPOSURE_LAMP_IPL" }, ///<FB-DF曝光灯无规律亮灯
    { STATUS_F_SCAN_FB_DF_HOME_ABNORMAL      , "STATUS_F_SCAN_FB_DF_HOME_ABNORMAL" },     ///<FB-DF校准仪器home传感器异常
    { STATUS_F_SCAN_FB_DF_BOARD_HOME_ABNORMAL           , "STATUS_F_SCAN_FB_DF_BOARD_HOME_ABNORMAL" },  ///<FB-DF校准仪器板载home传感器异常
    { STATUS_F_SCAN_FB_DF_CIS_CLA_ADJ_ABNORMAL        , "STATUS_F_SCAN_FB_DF_CIS_CLA_ADJ_ABNORMAL" },        ///<FB-DF灯光校准异常
    { STATUS_F_SCAN_FB_DF_CIS_GAIN_ADJ_ABNORMA          , "STATUS_F_SCAN_FB_DF_CIS_GAIN_ADJ_ABNORMA" },    ///<FB-DF-GAIN校准异常
    { STATUS_F_SCAN_FB_CONNECT_ENGINE_TIMEOUT       , "STATUS_F_SCAN_FB_CONNECT_ENGINE_TIMEOUT" },        ///<FB连接引擎超时
    { STATUS_F_SCAN_FB_FATAL_IMAGE_PROCESS_CLK , "STATUS_F_SCAN_FB_FATAL_IMAGE_PROCESS_CLK" },   ///<FB图像处理同步时钟输入致命异常
    { STATUS_F_SCAN_FB_INIT_TIMEOUT     , "STATUS_F_SCAN_FB_INIT_TIMEOUT" },///<FB初始化超时
    //SCAN STATUS END
    //COPY STATUS BEGIN
    { STATUS_I_COPY_IDLE  , "STATUS_I_COPY_IDLE" },///<复印空闲中
    { STATUS_I_COPY_PROCESSING          , "STATUS_I_COPY_PROCESSING" },   ///<复印中
    { STATUS_I_COPY_CANCELING           , "STATUS_I_COPY_CANCELING" },   ///<复印取消中
    { STATUS_I_COPY_MANUAL_DUPLEX_CONFIRM           , "STATUS_I_COPY_MANUAL_DUPLEX_CONFIRM" },    ///<手动双面通知
    { STATUS_I_COPY_ID_CARD_CONFIRM     , "STATUS_I_COPY_ID_CARD_CONFIRM" },///<身份证翻面通知
    { STATUS_I_COPY_MEM_LOW  , "STATUS_I_COPY_MEM_LOW" },   ///<复印内存不足
    { STATUS_I_COPY_SAMPLE_CONFIRM      , "STATUS_I_COPY_SAMPLE_CONFIRM" },       ///<样本复印，复印一份后提示用户的确认信息
    { STATUS_I_COPY_SAMPLE_FINISH       , "STATUS_I_COPY_SAMPLE_FINISH" },       ///<样本复印，复印剩余份后提示用户是否删除数据
    { STATUS_I_COPY_NEXT_ORIGINAL_CONFIRM           , "STATUS_I_COPY_NEXT_ORIGINAL_CONFIRM" },    ///<复印下一张原稿
    { STATUS_I_COPY_PUT_PAPER_TO_ADF    , "STATUS_I_COPY_PUT_PAPER_TO_ADF" },///<ADF缺纸
    { STATUS_I_COPY_STATISTIC_ITEM_ERROR            , "STATUS_I_COPY_STATISTIC_ITEM_ERROR" },    ///<复印统计项不存在
    { STATUS_W_COPY_PARAMETER_ERROR     , "STATUS_W_COPY_PARAMETER_ERROR" },///<复印配置参数有误
    { STATUS_W_COPY_PERMISSION_NOT_ALLOWED          , "STATUS_W_COPY_PERMISSION_NOT_ALLOWED" },    ///<复印权限不允许
    { STATUS_E_COPY_IPM_ERROR           , "STATUS_E_COPY_IPM_ERROR" },   ///<图像处理错误
     //COPY STATUS END
    //POWERMGR BEGIN
    { STATUS_I_POWERMGR_STATUS_UPDATE   , "STATUS_I_POWERMGR_STATUS_UPDATE" },
    //POWERMGR END
    //UPGRADE BEGIN
    { STATUS_I_UPGRADE_PANEL_START      , "STATUS_I_UPGRADE_PANEL_START" },        ///<Panel升级开始
    { STATUS_I_UPGRADE_DOWNLOAD_START   , "STATUS_I_UPGRADE_DOWNLOAD_START" }, ///<固件下载开始
    { STATUS_I_UPGRADE_VALUE_UPDETE     , "STATUS_I_UPGRADE_VALUE_UPDETE" },///<基准值更新
    { STATUS_I_UPGRADE_FREE_STATUS      , "STATUS_I_UPGRADE_FREE_STATUS" },       ///<升级空闲状态
    { STATUS_I_UPGRADE_STATUS_ALL       , "STATUS_I_UPGRADE_STATUS_ALL" },       ///<升级状态综合
    //UPGRADE END
    //USB BEGIN
    { STATUS_I_USBD_CONNECTED           , "STATUS_I_USBD_CONNECTED" },  ///<USBD已连接
    { STATUS_I_USBD_DISCONNECTED        , "STATUS_I_USBD_DISCONNECTED" },    ///<USBD断开/未连接
    //USB END
    //ETH BEGIN
    { STATUS_I_ETH_DISCONNECTED         , "STATUS_I_ETH_DISCONNECTED" },///< 有线网络断开/未连接
    { STATUS_I_ETH_CONNECTED , "STATUS_I_ETH_CONNECTED" },///< 有线网络已连接
    //ETH END
    //WIFI BEGIN
    { STATUS_F_WIFI_MODULE_UNUSABLE     , "STATUS_F_WIFI_MODULE_UNUSABLE" },///< WiFi模块不可用
    { STATUS_I_WIFI_DISCONNECTED        , "STATUS_I_WIFI_DISCONNECTED" },///< WiFi断开/未连接
    { STATUS_I_WIFI_CONNECTED           , "STATUS_I_WIFI_CONNECTED" },///< WiFi已连接
    { STATUS_I_WIFI_CONNECTING          , "STATUS_I_WIFI_CONNECTING" },///< WiFi正在连接
    //WIFI END
    //STORAGE BEGIN
    { STATUS_F_STORAGE_DEMAGE           , "STATUS_F_STORAGE_DEMAGE" },///< 存储设备/分区损坏无法正常使用
    { STATUS_F_STORAGE_UNRECOGNIZABLE   , "STATUS_F_STORAGE_UNRECOGNIZABLE" },///< 存储设备/分区无法识别
    { STATUS_E_STORAGE_PROCESS_FAILED   , "STATUS_E_STORAGE_PROCESS_FAILED" },///< 存储设备功能处理失败
    { STATUS_W_STORAGE_PROCESS          , "STATUS_W_STORAGE_PROCESS" },///< 存储设备行为处理警告
    { STATUS_W_STORAGE_HEALTH           , "STATUS_W_STORAGE_HEALTH" },///< 存储设备健康程度低警告
    { STATUS_I_STORAGE_STATUS           , "STATUS_I_STORAGE_STATUS" },///< 存储设备行为结果信息
    { STATUS_I_STORAGE_IDLE  , "STATUS_I_STORAGE_IDLE" },///< 存储管理模块IDLE信息
    //STORAGE END
    { STATUS_ID_ALL       , "STATUS_ID_ALL" },
    { STATUS_ID_MASK      , "STATUS_ID_MASK" },
};

int32_t status_test(int32_t argc, char *argv[])
{

    uint32_t status_num = atoi(argv[0]);
    if( status_num >= (sizeof(s_id_item)/sizeof(s_id_item[0])) )
    {
        printf("input status number error!\n");
        return 0;
    }

    EVT_MGR_CLI_S *client = get_panel_event_client();
    struct
    {
        int id;
        int arg;
    }value;
    value.id = s_id_item[status_num].id;
    value.arg = 0xffffffff;
    printf("notify %s ..\n" , s_id_item[status_num].str);
    if (value.id & STATUS_ID_MODULE_PRINT)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_PRINT , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_SCAN)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_SCAN , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_COPY)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_COPY , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_FWUPDATE)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_FWUPDATE , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_ETH)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_ETH , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_WIFI)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_WIFI , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_POWERMGR)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_POWERMGR , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_USBDEVICE)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_USBDEVICE , &value, sizeof(value));
    }else if (value.id & STATUS_ID_MODULE_STORAGE)
    {
        pi_event_mgr_notify(client , EVT_TYPE_SYSTEMSTATUS_FROM_STORAGE , &value, sizeof(value));
    }else
    {
        printf("--- unknow %s \n" , s_id_item[status_num].str);
    }
    return 0;
}

/**
 * @}
 */

