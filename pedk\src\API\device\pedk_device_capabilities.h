#ifndef _PEDK_DEVICE_CAPABILITIES_
#define _PEDK_DEVICE_CAPABILITIES_

#include <quickjs.h>

typedef enum
{
    OPTION_TRAY1 = 0x00,
    OPTION_TRAY2 = 0x01,
    OPTION_TRAY3 = 0x02,
    OPTION_TRAY4 = 0x03,
    EXTERNAL_HIGH_CAPACITY_TRAY = 0x04,
    INSTALL_HIGH_CAPACITY_TRAY = 0x05,
    MULTI_FUNCTION_TRAY = 0x06,
    TRAY_IN_INVALID = 0xff,
}TRAY_IN_E;

typedef struct
{
    char                        paper_size[32];
    uint32_t                    tray_support[7];  /*tray1|tray2|tray3|tray4|external_tray|install_tray|multi_tray*/
}JSPaperSizeWithTray;


typedef struct
{
    char                        paper_type[32];
    uint32_t                    tray_support[7]; /*tray1|tray2|tray3|tray4|external_tray|install_tray|multi_tray*/
}JSPaperTypeWithTray;


JSValue js_get_capability_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);

int32_t js_capability_init(JSContext *ctx, JSValueConst global);

#endif /* _PEDK_DEVICE_CAPABILITIES_ */