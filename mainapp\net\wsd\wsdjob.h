#ifndef __WSDJOB_H__
#define __WSDJOB_H__

#include "wsdservice.h"

typedef struct wsd_job_pool WSD_JOB_POOL_S;

WSD_JOB_POOL_S* wsd_job_pool_create(int pool_capacity);
void wsd_job_pool_destroy(WSD_JOB_POOL_S* pool);
int wsd_job_register(WSD_SVC_S* svc_type, WSD_TICKET_S* ticket);
WSD_TICKET_S* wsd_ticket_get_from_job_id(WSD_SVC_S* svc_type, int job_id);
//int wsd_job_unregister(WSD_TICKET_S* ticket);

#endif
