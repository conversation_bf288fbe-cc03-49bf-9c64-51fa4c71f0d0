#ifndef _JOB_COMMON_H
#define _JOB_COMMON_H

#include "preparse.h"
#include "job_manager.h"
#include "status_manager.h"
#include "channels.h"
#include "pol/pol_types.h"
#include "pol/pol_threads.h"
#include "pol/pol_io.h"
#include "pol/pol_mem.h"
#include "pol/pol_string.h"
#include "qio/qio_general.h"
#include "qio/qio.h"
#include "utilities/msgrouter.h"
#include "event_manager/event_mgr.h"

#define CONFIG_FILE "/root/system_resource_config.ini"
#define MEM_DECREASE_STEP   4


#define contain_of(ptr,type,member) \
    { (type*)((char*)ptr - ((char*)&((type*)(ptr))->member - (char*)ptr)) }


typedef enum
{
    ALLOC_STATE = 1 ,
    PARSE_STATE ,
    GIVEINFO_STATE ,
    ACQUIRE_RESOURCE_STATE ,
    ACQUIRE_PTMEM_STATE ,
    JUDGE_STATE ,
    RESOURCE_START_STATE,
    MAX_STATE = 0xff
}JOB_HANDLE_STATE_E;

typedef enum
{
    USING = 0,
    CANCELED = 1,
    FREE = 2, 
    STOP = 3,
    HANG_UP = 4,
    CONTINUE = 5
}RESOURCE_STATE_E;

typedef struct
{
    int32_t state; ///<对应RESOURCE_STATE_E，表示每个模块资源的阶段性变化，用于取消、暂停和挂起等
    int32_t resource_id;
    int32_t send_number;
    CURRENT_RESOURCE_DATA_P current_resource_data;
}ONE_RESOURCE_STATE_S , *ONE_RESOURCE_STATE_P;

typedef struct 
{
    JOBINFO_S job_info;
    JOB_PARAM_U job_params;
} JOB_HISTORY_S;

typedef struct 
{
    uint32_t _old_jobid;///<旧的jobid，对应样本打印作业、密码作业和延迟作业
    uint16_t _resource_io_id;
    uint8_t _io_end;
    uint8_t _cur_state;///<对应JOB_HANDLE_STATE_E，表示当前作业已经处理到哪一阶段。新作业会因为内存资源或模块资源不足等，在后台隔断时间尝试发起
    uint8_t _forbid_startup;///<标识当前作业是否满足发起条件
    uint8_t _start_code;///<JOBID_RET_CODE
    JOBINFO_S _job_info;
    JOB_PARAM_U _job_params;
    PREPARSER_RESULT_S _parser_result;
    ROUTER_MSG_S _job_msg;
    struct channels _resource_link;///<存储CURRENT_RESOURCE_DATA_P结构体
    JOB_RESOURCES_DATA_S *_job_resource_data;
}ONE_JOB_INFO_S,*ONE_JOB_INFO_P;

//由于BM605ADN典型能耗不满足规格，需要系统作业添加ejob处理方案
typedef struct 
{
    uint8_t assign_pdt; ///<是否是满足能耗的机型
    uint8_t energy_saving_rouse;///<是否从节能中唤醒
    uint8_t print_job;///<是否为打印作业
    uint8_t cancel_job;///<作业是否被取消
    uint32_t job_id;
}EJOB_POWER_CONDITION_S,*EJOB_POWER_CONDITION_P;

typedef struct
{
    struct channels _job_set[5];///<作业队列集合，其索引对应JOB_STATUS_E
    struct resource_config *_config;
    EVT_MGR_CLI_S *_client;
    PI_THREAD_T _th;
    PI_MUTEX_T _mtx;
    PI_MUTEX_T _mtx_mode;
    volatile int8_t _isexit;
    volatile int8_t _busy;
    volatile int8_t _mode;///< JOB_PRIORITY_E
    volatile int8_t _emmc_ready;
    volatile int8_t _print_ready;
    volatile int8_t _mount_ssd;
    volatile int8_t _ssd_health_check;
    volatile int8_t _interrupted_relieve_flag;
    uint32_t job_id;
    EJOB_POWER_CONDITION_S _ejob;
}SYSTEM_DATA_S,*SYSTEM_DATA_P;


#endif //_JOB_COMMON_H
