/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file logdebug.c
 * @addtogroup utilities
 * @{
 * @addtogroup logdebug
 * <AUTHOR>
 * @date 2023-06-21
 * @version v1.0
 * @brief log function srartup module
 *
 */
#include <stdlib.h>
#include <stdio.h>

#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "cmd.h"
#include "nvram.h"

#define PLATFORM_ID_LOG_LEVEL   10060
#define PLATFORM_ID_LOG_MODE    10061
static void log_lvl_mode_init()
{
    uint32_t output_lvl_set;
    uint32_t output_mode_set;
    // printf("NV before *** s_output_mode_set = %u, s_output_lvl_set = %u\n",output_mode_set,output_lvl_set);
    pi_nvram_get(PLATFORM_ID_LOG_LEVEL, VTYPE_UINT, &output_lvl_set, sizeof(uint32_t));
    pi_nvram_get(PLATFORM_ID_LOG_MODE, VTYPE_UINT, &output_mode_set, sizeof(uint32_t));
    // printf("NV after *** s_output_mode_set = %u, s_output_lvl_set = %u\n",output_mode_set,output_lvl_set);
    pi_log_lvl_mode_init(output_lvl_set, output_mode_set);
}
static int32_t debug_log_set(int32_t argc, char *argv[])
{
    if ( argc > 0 )
    {
        log_set_level(atoi(argv[0]));
    }

    if ( argc > 1 )
    {
        log_set_mode(atoi(argv[1]));
    }

    return 0;
}

static int32_t debug_log_get(int32_t argc, char *argv[])
{
    log_show_level();
    log_show_mode();

    return 0;
}

static int32_t debug_log_set_nv(int32_t argc, char *argv[])
{
    uint32_t log_lvl = 0;
    uint32_t log_mode = 0;
    if( argc != 2 )
    {
        printf("param num error, input again!!!\n");
        return -1;
    }
    log_lvl = atoi(argv[0]);
    log_mode = atoi(argv[1]);
    pi_nvram_set(PLATFORM_ID_LOG_LEVEL, VTYPE_UINT, &log_lvl, sizeof(uint32_t), 0, NULL);
    pi_nvram_set(PLATFORM_ID_LOG_MODE, VTYPE_UINT, &log_mode, sizeof(uint32_t), 0, NULL);
    return 0;
}

int32_t debug_prolog()
{
	int32_t ret = 0;

    log_lvl_mode_init();
	ret = cmd_register("log", "set", debug_log_set, NULL);
	ret = cmd_register("log", "get", debug_log_get, NULL);
    ret = cmd_register("log", "set_nv",debug_log_set_nv, NULL);

    return ret;
}
/**
 *@}
 */

