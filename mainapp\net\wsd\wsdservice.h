#ifndef __WSD_SERVICE_H__
#define __WSD_SERVICE_H__

#include "nettypes.h"
#include "http_task.h"
#include "qxml.h"
#include "soap.h"
#include "wsdsubs.h"

struct wsd_ticket;
struct action_handler;
struct wsd_job_pool;
typedef struct wsd_service
{
    const char* xmlns;
    const char* xmlns_url;
    volatile int event_rate;
    struct action_handler* action_hndl_tbl;
    WSD_SUBS_LIST_S* subs_list;
    struct wsd_job_pool* job_pool;
    int (*get_job_elements_handler)(struct wsd_ticket* pticket, QXML_S* qxml, OUT SOAP_VAR_S** out_pdata, OUT int* out_valid);
    void (*ticket_destroy)(struct wsd_ticket* pticket);
}
WSD_SVC_S;

#define ACTION_HANDLER_DEFINITION_SIGNATURE (WSD_SVC_S* svc_type, HTTP_TASK_S* ptask, QXML_S* pxml, WSD_SERVICE_DATA_S* srv_data, char** reply_code)
#define ACTION_HANDLER_DEFINITION(_name)  \
    int (_name)ACTION_HANDLER_DEFINITION_SIGNATURE

typedef int (*HANDLER_FUNC)ACTION_HANDLER_DEFINITION_SIGNATURE;

typedef struct action_handler
{
    const char*   action;
    HANDLER_FUNC  handle;
}
ACTION_HNDL_S;

typedef enum {
    WSD_JOB_CREATE = 0,
    WSD_JOB_RUNNING = 1,
    WSD_JOB_WILLEND = 2,
    WSD_JOB_CANCELED = 3,

    WSD_ERR_JobIdNotFound = 100,
    WSD_ERR_NoImagesAvailable = 101,
    WSD_ERR_InvalidJobToken = 102,
    WSD_ERR_JobCancelled = 103,
    WSD_ERR_OperationFailed = 104
}
WSD_JOBSTATUS_TYPE;


#define END_OF_CHUNK            "0\r\n\r\n"
#define WSD_SOAP_SENDER         "soap:Sender"
#define WSD_SOAP_RECEIVER       "soap:Receiver"
#define WSA_XMLNS               "wsa"
#define WSD_DEFAULT_EVENT_RATE  1

ACTION_HANDLER_DEFINITION(wsd_srvcomm_probe_handler);
ACTION_HANDLER_DEFINITION(wsd_srvcomm_get_job_elements_handler);
ACTION_HANDLER_DEFINITION(wsd_srvcomm_get_active_job_handler);
ACTION_HANDLER_DEFINITION(wsd_srvcomm_get_job_history_handler);
ACTION_HANDLER_DEFINITION(wsd_srvcomm_cancel_job_handler);
ACTION_HANDLER_DEFINITION(wsd_srvcomm_set_event_rate_handler);
ACTION_HANDLER_DEFINITION(wsd_srvcomm_probe_handler);

void wsd_cancel_job(WSD_SVC_S* srv_type, WSD_TICKET_S* pticket);
void wsd_ticket_ref(WSD_TICKET_S* pticket);
void wsd_ticket_unref(WSD_SVC_S* srv_type, WSD_TICKET_S* pticket);
int WSDcreateFaultResponse(WSD_SERVICE_DATA_S* srv_data, const char* code, const char* subcode_xmlns, const char* subcode, const char* reason);
int wsd_reply_with_fault(HTTP_TASK_S* ptask, WSD_SERVICE_DATA_S* srv_data, const char* code, const char* subcode_xmlns, const char* subcode, const char* reason);
int wsd_strtol(const char* buf, OUT int* out_val);
int32_t wsd_process_service(WSD_SVC_S* srv_typ, HTTP_TASK_S* ptask, char* req_body, WSD_SERVICE_DATA_S* srv_data);
int wsd_reply_with_soap_content(HTTP_TASK_S* ptask, WSD_SERVICE_DATA_S* srv_data);
#endif

