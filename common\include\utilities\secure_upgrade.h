/**
 * @copyright 2025 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file secure_upgrade.h
 * @addtogroup utilities
 * @{
 * <AUTHOR>
 * @date 2025-07-2
 * @version v1.0
 * @brief Disables trusted measurement for the secure boot secure_reboot head file
 */


#ifndef SECURE_REBOOT_H
#define SECURE_REBOOT_H


/**
 * @brief Disables trusted measurement for the secure boot to prevent measurement failure on reboot.
 * <AUTHOR>
 * @return int The status of the operation.
 * @retval -1 On failure.
 */
int secure_reboot();

#endif
