1.传输层提供标准的调用接口

4个接口如下：
```c
void transport_init();
int32_t transport_send(const uint8_t *buffer, uint16_t length);
int32_t transport_receive(uint8_t *buffer, uint16_t *length, uint32_t ms);
void trans_release();
```
这四个接口是PeSF与外部进程通信的接口，接口的内部实现可以根据需求自定义：比如IPC通信、网络socket、与终端标准输入输出。

2.当前为方便开发仅实现了部分与monitor的通信。

```c
#ifdef TRANS_MONITOR
#include "trans/monitor/monitor.h"

void transport_init()
{
    
}

int32_t transport_send(const uint8_t *buffer, uint16_t length)
{
    return send_to_human_monitor(buffer, length);
}

int32_t transport_receive(uint8_t *buffer, uint16_t *length, uint32_t ms)
{
   return receive_from_human_monitor(buffer,length,ms);
}

void trans_release()
{

}
#endif
```

3.与外部通信方式遵守PeSF通信协议.xlsx，在PPT文档中的excel中有表述。