/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file main.c
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2023-07-13
 * @brief event manager service
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <signal.h>

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "cbinder/cbinder.h"
#include "event_mgr_typedef.h"
#include "event_mgr_private.h"
#include "event_mgr_service.h"

static int32_t event_mgr_handler(struct binder_state* bs, struct binder_txn* txn, struct binder_io* msg, struct binder_io* reply, void* user_data)
{
    EVT_MGR_SVC_S*  svc = (EVT_MGR_SVC_S *)user_data;
    uint32_t        module_id;
    uint32_t        length;
    int32_t         ret;

    switch ( txn->code )
    {
    case EVENT_MGR_CMD_CONNECT:
        {
            int32_t write_fd;
            void*   object;

            object    = bio_get_ref(msg);
            module_id = bio_get_uint32(msg);
            write_fd  = bio_get_fd(msg);

            if ( module_id == EVT_MODULE_DEBUG )
            {
                char    cmdfile[64];
                FILE*   stream;

                snprintf(cmdfile, sizeof(cmdfile), "/proc/%u/cmdline", txn->sender_pid);
                stream = fopen(cmdfile, "r");
                if ( stream != NULL )
                {
                    char cmdline[64];

                    fgets(cmdline, sizeof(cmdline), stream);
                    EVT_LOG("cmdline(%s)", cmdline);
                    if ( strstr(cmdline, "mfp.afx") )
                    {
                        EVT_ERR("Not allowed to create EVT_MODULE_DEBUG client in mfp.afx!!!");
                        bio_put_uint32(reply, (uint32_t)-1);
                        break;
                    }
                }
            }

            ret = event_mgr_svc_dispatch_connect(svc, bs, module_id, write_fd, object);

            bio_put_uint32(reply, (uint32_t)ret);
            break;
        }
    case EVENT_MGR_CMD_DISCONNECT:
        {
            module_id = bio_get_uint32(msg);

            ret = event_mgr_svc_dispatch_disconnect(svc, bs, module_id);

            bio_put_uint32(reply, (uint32_t)ret);
            break;
        }
    case EVENT_MGR_CMD_REGISTER:
    case EVENT_MGR_CMD_UNREGISTER:
        {
            uint32_t*   event_array;
            uint32_t    event_count;

            module_id   = bio_get_uint32(msg);
            event_array = (uint32_t *)bio_get_buffer(msg, &length);
            event_count = length / sizeof(uint32_t);

            ret = event_mgr_svc_dispatch_register(svc, module_id, event_array, event_count, txn->code);

            bio_put_uint32(reply, (uint32_t)ret);
            break;
        }
    case EVENT_MGR_CMD_NOTIFY:
        {
            uint32_t    event_type;
            char*       buffer;

            module_id  = bio_get_uint32(msg);
            event_type = bio_get_uint32(msg);
            buffer     = bio_get_buffer(msg, &length);

            ret = event_mgr_svc_dispatch_notify(svc, module_id, event_type, buffer, length);

            bio_put_uint32(reply, (uint32_t)ret);
            break;
        }
    default:
        {
            EVT_ERR("invalid cmd(%x)", txn->code);
            break;
        }
    }

    return 0;
}

static void signal_ignore(int signum, siginfo_t* info, void* ctx)
{
    EVT_ERR("ignore signal: %d", signum);
}

static void signal_catch(void)
{
    struct sigaction action;
    
    action.sa_flags     = SA_SIGINFO;
    action.sa_sigaction = signal_ignore;

    sigaction(SIGPIPE, &action, NULL);
}

int32_t main(int32_t argc, char** args)
{
    EVT_MGR_SVC_S* svc = NULL;
    struct binder_state* bs = NULL;
    unsigned event_token;
    int32_t ret = 0;

    signal_catch();

    bs = binder_start(1);

    ret = svcmgr_add_service(bs, BINDER_SERVICE_MANAGER, "pantum.event.manager", &event_token);
    RETURN_VAL_IF(ret != 0, EVT_ERR, -1);

    svc = event_mgr_svc_create();
    RETURN_VAL_IF(svc == 0, EVT_ERR, -1);

    binder_add_target(bs, &event_token, event_mgr_handler, svc);

    event_mgr_svc_start_loop(svc);

    event_mgr_svc_destroy(svc);

    return 0;
}
/**
 *@}
 */
