#ifndef _PEDK_DEVICE_SETTING_DEVICEINFO_
#define _PEDK_DEVICE_SETTING_DEVICEINFO_

#include <quickjs.h>

JSValue js_get_dcfw_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_ecfw_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_fpga1_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_fpga2_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

int js_device_deviceinfo_init(JSContext *ctx, JSValueConst global);

#endif /* _PEDK_DEVICE_SETTING_DEVICEINFO_ */

