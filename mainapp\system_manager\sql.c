/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file sql.c
 * @addtogroup system_manager
 * @{
 * @brief Database Operation Interface
 * <AUTHOR> 
 * @version 1.0
 * @date 2024-05-28
 */

#include "sql.h"
#include "ulog.h"
#include <libio.h>
#include <stdio.h>
#include <string.h>
#include <sqlite3.h>
#include <stdlib.h>
#include <errno.h>
#include <unistd.h>

struct table_sync_config
{
    sqlite3 *db;
    const char *table;
};

void* sql_prolog(const char *name)
{
    if (!name)
        return NULL;

    sqlite3 *db = NULL;
    if (sqlite3_open(name , &db))
    {
        ULOG_ERROR(SYS_JOB_LOG , "open %s failed:%s\n" , name , sqlite3_errmsg(db));
    }

    return db;
}

int sql_epilog(void *handle)
{
    if (!handle)
        return -1;

    sqlite3_close(handle);
    return 0;
}

int sql_integrity_check(void *handle)
{
    if (!handle)
        return -1;

    int ret = 1 , code;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;

    code = sqlite3_exec(db , "PRAGMA integrity_check" , NULL , NULL , NULL );
    if (code != SQLITE_OK)
    {
        if (code == SQLITE_CORRUPT)
        {
            ret = 0;
        }
        ULOG_ERROR(SYS_JOB_LOG , "Integrity Check Result:%s\n" , sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);
    return ret;
}

static int _sync_table_item(const void *context , const void *data , unsigned int size)
{
    struct table_sync_config *tsc = (struct table_sync_config*)context;

    if (*(tsc->table) == 'T')
        sql_set_table_item(tsc->db , tsc->table , *(unsigned int*)data  , data , size);
    else if (strcmp(tsc->table , "jobinfo") == 0)
        sql_set_table_item(tsc->db , tsc->table , 1  , data , size);
    else
        sql_set_table_item(tsc->db , tsc->table , *(unsigned int*)(data + 4)  , data , size);
    return 0;
}

static int _table_sync(sqlite3 *rdb , sqlite3 *wdb)
{
    int ret = 0;
    sqlite3_stmt *stmt = NULL; 
    const char *cmd = "SELECT name FROM sqlite_master WHERE sql NOT NULL AND type=='table' AND name!='sqlite_sequence'";

    if (sqlite3_prepare_v2(rdb, cmd , strlen(cmd), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(rdb));
    }

    if (!ret && sqlite3_column_count(stmt) != 1)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_column_count error:%s\n" , sqlite3_errmsg(rdb));
    }

    while(!ret)
    {
        int rc = sqlite3_step(stmt);

        if (rc == SQLITE_ROW)
        {
            const char *table = (const char*)sqlite3_column_text(stmt , 0);
            struct table_sync_config tsc;

            if (table)
            {
                tsc.db = wdb;
                tsc.table = table;
                sql_create_table(wdb , table);
                ret = sql_get_table_items(rdb , table , &tsc , _sync_table_item);
                if (ret >= 0)
                {
                    ret = 0;
                }
            }
        }
        else if (rc == SQLITE_DONE)
        {
            break;
        }
        else
        {
            ret = -1;
            ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(rdb));
            break;
        }
    }

    sqlite3_finalize(stmt);
    return ret;
}

int sql_try_repair(void **handle , const char *file)
{
    if (!handle || !file)
        return -1;

    unsigned int len = 0;
    char *t_file = NULL;
    sqlite3 *wdb = NULL;
    sqlite3 *rdb = *(sqlite3**)handle;
    sql_epilog(rdb);

    rdb = (sqlite3*)sql_prolog(file);
    if (!rdb)
    {
        return -1;
    }
    sqlite3_exec(rdb , "PRAGMA writable_schema=ON" , NULL , NULL , NULL );
    len = strlen(file) + 4;
    t_file = (char*)malloc(len);
    if (!t_file)
    {
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed!\n");
        sql_epilog(rdb);
        *handle = NULL;
        return -1;
    }
    memset(t_file , 0 , len);
    snprintf(t_file , len , "%s-n" , file);
    unlink(t_file);
    wdb = sql_prolog(t_file);
    if (!wdb)
    {
        free(t_file);
        sql_epilog(rdb);
        *handle = NULL;
        return -1;
    }
    if (_table_sync(rdb , wdb) == 0)
    {
        sql_epilog(rdb);
        sql_epilog(wdb);
        if (rename(t_file , file) == -1)
        {
            ULOG_ERROR(SYS_JOB_LOG , "repair %s failed , %s\n" , file , strerror(errno));
            unlink(t_file);
        }
        else
        {
            ULOG_WARN(SYS_JOB_LOG , "repair %s finish!\n" , file);
        }
        free(t_file);
        *handle = sql_prolog(file);
    }
    else
    {
        sql_epilog(rdb);
        sql_epilog(wdb);
        free(t_file);
        *handle = NULL;
    }

    return *handle ? 0 : -1;
}

int sql_create_table(const void *handle , const char *name)
{
    if (!handle || !name)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;

    char *buf = sqlite3_mprintf("CREATE TABLE %s (id INTEGER primary key, val BLOB)" ,  name);
    if (!buf)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
    }
    ULOG_DEBUG(SYS_JOB_LOG , "create table:%s\n" , name);

    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_step(stmt) != SQLITE_DONE)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    return ret;
}

int sql_find_table(const void *handle , const char *name)
{
    if (!handle || !name)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;

    char *buf = sqlite3_mprintf("select name from sqlite_master where type='table' and name='%s'" ,  name);
    if (!buf)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
    }

    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_step(stmt) != SQLITE_ROW)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_column_count(stmt) != 1)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_column_count error:%s\n" , sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    return ret;
}

int sql_get_tables(const void *handle , 
        void *context , 
        void (*callabck)(void* context , const unsigned char* table))
{
    if (!handle)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;

    char *buf = sqlite3_mprintf("select name from sqlite_master where type='table'");
    if (!buf)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
    }

    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_column_count(stmt) != 1)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_column_count error:%s\n" , sqlite3_errmsg(db));
    }

    while(!ret)
    {
        int rc = sqlite3_step(stmt);

        if (rc == SQLITE_ROW)
        {
            const unsigned char *table = sqlite3_column_text(stmt , 0);

            if (callabck && table)
            {
                callabck(context , table);
            }
        }
        else if (rc == SQLITE_DONE)
        {
            break;
        }
        else
        {
            ret = -1;
            ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
        }
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    return ret;
}

int sql_set_table_item(const void *handle , 
        const char *table,
        const unsigned int id , 
        const void *data , 
        const unsigned int size)

{
    if (!handle || !table || !data || !size)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;
    char *buf = sqlite3_mprintf("REPLACE INTO %s VALUES (%d, ?)" ,  table , id);

    if (!buf)
    {
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
        ret = -1;
    }

    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_bind_blob(stmt, 1 , data , size , NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_bind_blob set value error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_step(stmt) != SQLITE_DONE)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    sqlite3_db_cacheflush(db);
    sqlite3_db_release_memory(db);
    return ret;
}

int sql_get_table_item(const void *handle , 
        const char *table ,
        const unsigned int id , 
        void *data , 
        unsigned int size)
{
    if (!handle || !table || !data || !size)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;
    char *buf = sqlite3_mprintf("SELECT val FROM %s WHERE id=%d" ,  table , id);

    if (!buf)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
    }
    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }
    if (!ret && sqlite3_step(stmt) != SQLITE_ROW)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
    }

    if (!ret && sqlite3_column_count(stmt) == 1)
    {
        unsigned int blob_size = 0;
        const void *blob = NULL;

        blob_size = sqlite3_column_bytes(stmt, 0);
        blob = sqlite3_column_blob(stmt, 0);
        if (blob)
        {
            memcpy(data , blob , blob_size > size ? size:blob_size);
        }
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    return ret;
}

int sql_del_table_item(const void *handle , const char *table , const unsigned int id)
{
    if (!handle || !table)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;

    char *buf = sqlite3_mprintf("DELETE FROM %s WHERE id=%d" ,  table , id);
    if (!buf)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
    }

    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }
    if (!ret && sqlite3_step(stmt) != SQLITE_DONE)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    return ret;
}

int sql_del_table(const void *handle , const char *table)
{
    if (!handle || !table)
        return -1;

    int ret = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;

    char *buf = sqlite3_mprintf("DROP TABLE %s" ,  table);
    if (!buf)
    {
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
        ret = -1;
    }

    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }
    if (!ret && sqlite3_step(stmt) != SQLITE_DONE)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);
    return ret;
}

unsigned int sql_get_table_items(const void *handle , 
        const char *table , 
        const void *callback_context ,
        int(*callback)(const void *context , const void *data , unsigned int size))
{
    if (!handle || !table)
        return 0;

    int ret = 0;
    unsigned int items = 0;
    sqlite3_stmt *stmt = NULL;
    sqlite3 *db = (sqlite3*)handle;
    char *buf = sqlite3_mprintf("SELECT val FROM %s ORDER BY id DESC" , table);

    if (!buf)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
    }
    if (!ret && sqlite3_prepare_v2(db, buf , strlen(buf), &stmt, NULL))
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_prepare_v2 error:%s\n" , sqlite3_errmsg(db));
    }
    if (!ret && sqlite3_column_count(stmt) != 1)
    {
        ret = -1;
        ULOG_ERROR(SYS_JOB_LOG , "sqlite3_column_count error:%s\n" , sqlite3_errmsg(db));
    }
    while(!ret)
    {
        int rc = sqlite3_step(stmt);
    
        if (rc == SQLITE_ROW)
        {
            ++items;
            if (callback && callback(callback_context , 
                            sqlite3_column_blob(stmt, 0) ,
                            sqlite3_column_bytes(stmt, 0)) )
            {
                break;
            }
        }
        else if (rc == SQLITE_DONE)
        {
            break;
        }
        else
        {
            ret = -1;
            ULOG_ERROR(SYS_JOB_LOG , "sqlite3_step error:%s\n" , sqlite3_errmsg(db));
        }
    }

    sqlite3_finalize(stmt);
    sqlite3_free(buf);

    return items; 
}

/**
 * @}
 */
