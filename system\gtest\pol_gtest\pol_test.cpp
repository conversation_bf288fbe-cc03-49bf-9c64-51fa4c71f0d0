#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include <signal.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <stdlib.h>
#include <string.h>
#include <strings.h>
#include <stdint.h>
#include <gtest/gtest.h>

#include "pol_test.h"

extern TEST_POOL_S io_pool;
extern TEST_POOL_S mem_pool;
extern TEST_POOL_S string_pool;
extern TEST_POOL_S time_pool;

TEST_POOL_P test_pool[] = {
       &io_pool,
       &mem_pool,
       &string_pool,
       &time_pool
};
/*
 * 
 *return :  -1 fail;  >=0 ok
 */
int32_t find_interface(TEST_ITEM_P sub_pool,uint32_t cnt, char* interface_name )
{
    int32_t i;
    for (i = 0;  i < cnt; i++) {
        if (!strcasecmp(interface_name, sub_pool[i].interface_name)) {
            return i;
        }
    }
    return -1;
}

/*
 *
 *return :  -1 fail;  >=0 ok
 */
int32_t find_pool(char* interface_type)
{
    int32_t i;
    int32_t cnt = sizeof(test_pool) / sizeof(TEST_POOL_P);
    for (i = 0; i < cnt; i++) {
        if (!strcasecmp(interface_type, test_pool[i]->pool_type)) {
            return i;
        }
    }
    return -1;
}

int32_t unit_test(char *interface_type, char* interface_name)
{
    int32_t idx = find_pool(interface_type);
    int32_t pos;
    test_func0 func = NULL;
    int32_t result;
    if (idx == -1) {
        printf("the interface type:%s not support\n",interface_type);
        return NOT_SUPPORT;
    } else {
        if ((pos = find_interface(test_pool[idx]->pool, test_pool[idx]->pool_cnt, interface_name)) == -1) {
            printf("the interface name : %s is not in the interfacetype %s\n",interface_name,interface_type);
            return NOT_SUPPORT;
        } else {
			printf("go to test [%s]\n",interface_name);
            func = ((test_pool[idx])->pool[pos].func);
            result = func();
            return  result;
        }
    }
}

int32_t find_func_name_in_pool(char *name)
{
	int32_t pool_size = sizeof(test_pool) /sizeof(test_pool[0]);
	int32_t i, ret;
	for (i = 0; i < pool_size; i++) {
		ret = find_interface(test_pool[i]->pool, test_pool[i]->pool_cnt, name);
		if (ret != -1) {
			return 0;
		}
	}
	return -1;
}

int32_t func_test_call(char *name)
{
	int32_t i, ret;

	int32_t pool_size = sizeof(test_pool) /sizeof(test_pool[0]);
	for (i = 0; i < pool_size; i++) {
		ret = find_interface(test_pool[i]->pool, test_pool[i]->pool_cnt, name);
		if (ret == -1) {
			continue;
		}
		break;
	}
	if (i == pool_size) {
		printf("the called func not find.\n");
		return -1;
	}

	test_func0 func = ((test_pool[i])->pool[ret].func);
	if (func == NULL) {
		printf("func not exsit!!!\n");
		return -1;
	}
	printf("call func:%s\n", test_pool[i]->pool[ret].interface_name);

	return func();
}



int32_t test_func(char *func_name)
{
	int32_t ret;

	if (func_name == NULL) {
		return -1;
	}
	int32_t pipe_fd[2] = {0};
	int32_t pipe_ret = pipe(pipe_fd);
	if (pipe_ret != 0) {
		printf("pipe create fail\n");
		return -1;
	}

	int32_t status;
	int32_t result;
	pid_t pid = fork();
	if (pid < 0) {
		printf("fork fail!!\n");
		return -1;
	} else if (pid == 0) {
		char buf[4] = {0};
		close(pipe_fd[0]);
		ret = func_test_call(func_name);
		if (ret == NOT_SUPPORT) {
			printf("not support this func!\n");
		} else if (ret == PASS) {
			printf("func test ok!\n");
		} else {
			printf("func test fail!\n");
		}
		snprintf(buf, 4, "%d", ret);
		write(pipe_fd[1], buf, 4);

		close(pipe_fd[1]);
		_exit(0);

	} else {
		close(pipe_fd[1]);
		char read_buf[4] = {0};
		read(pipe_fd[0], read_buf, 4);
		result = atoi(read_buf);
		close(pipe_fd[0]);

		do {
			 int32_t w = waitpid(pid, &status, WUNTRACED | WCONTINUED);
			 if (w == -1) {
				 perror("waitpid");
				 exit(EXIT_FAILURE);
			 }

			/* if (WIFEXITED(status)) {
				 printf("exited, status=%d\n", WEXITSTATUS(status));
			 } else if (WIFSIGNALED(status)) {
				 printf("killed by signal %d\n", WTERMSIG(status));
			 } else if (WIFSTOPPED(status)) {
				 printf("stopped by signal %d\n", WSTOPSIG(status));
			 } else if (WIFCONTINUED(status)) {
				 printf("continued\n");
			 }*/
		 } while (!WIFEXITED(status) && !WIFSIGNALED(status));

	}
	printf("-----------------------------------\n");
    return result;
}

int32_t test_module(int32_t idx)
{

	int32_t i;
	int32_t pass_count = 0;
	int32_t fail_count = 0;
	int32_t no_support = 0;
	int32_t sub_pool_size = test_pool[idx]->pool_cnt;
	int32_t func_ret;

	for (i = 0; i < sub_pool_size; i++) {
		func_ret = test_func(test_pool[idx]->pool[i].interface_name);
		if (func_ret == PASS) {
			pass_count++;
		} else if(func_ret == FAIL) {
			fail_count++;
		} else if (func_ret == NOT_SUPPORT){
			no_support++;
		} else {
			pass_count++;
		}

	}

	/*result*/
	printf("total: %d. pass: %d. fail: %d. no_support: %d.\n", sub_pool_size, pass_count, fail_count, no_support);
	return 0;
}

int32_t multiple_func_test()
{
	int32_t ret = access("./functest.ini", F_OK);
	if (ret != 0) {
		printf("please create the file(functest.ini),and add func to test!\n");
		return -1;
	}

	FILE *fp = fopen("./functest.ini", "r");
	if (fp == NULL) {
		printf("open file fail\n");
		return -1;
	}

	fseek(fp, 0, SEEK_SET);

	char buf[124];
	char *find;
	int32_t total = 0;
	int32_t pass_count = 0;
	int32_t fail_count = 0;
	int32_t no_support = 0;
	int32_t func_ret;

	while (fgets(buf, sizeof(buf), fp)) {
		total++;
		printf("multiple1: %s\n", buf);
		find = strchr(buf, '\r');
		if (find != NULL) {
			*find = '\0';
		}
		printf("multiple: %s\n", buf);
		func_ret = test_func(buf);
		if (func_ret == PASS) {
			pass_count++;
		} else if(func_ret == FAIL) {
			fail_count++;
		} else if (func_ret == NOT_SUPPORT){
			no_support++;
		} else {
			pass_count++;
		}
		memset(buf, 0, sizeof(buf));
	}
	fclose(fp);
	printf("total: %d. pass: %d. fail: %d. no_support: %d.\n", total, pass_count, fail_count, no_support);

	return 0;
}

int32_t switch_func_test()
{
	int32_t mode;
	printf("a single func or multiple funcs. (1.single 2.multiple)\n");
	scanf("%d", &mode);

	if (mode != 1 && mode != 2) {
		return -1;
	}

	if (mode == 2) {
		/*read from config file to test*/
		int32_t ret = multiple_func_test();
		return ret;
	}

	printf("input func name u want test. such as: pi_open\n");
	char func_name[124] = {0};
	int32_t i;
	for (i = 0; i < 3; i++) {
		scanf("%123s", func_name);
		if (find_func_name_in_pool(func_name) == 0) {
			test_func(func_name);
			break;
		} else {
			printf("func name not find, please try again!!!\n");
			continue;
		}
	}
	if (i == 3) {
		printf("func not in pool, return!\n");
		return -1;
	}
	return 0;
}

int32_t switch_module_test()
{
	printf("input module name u want test. (io mem time string)\n");
	char module_name[36] = {0};
	int32_t i, pool_idx;
	for (i = 0; i < 3; i++) {
		scanf("%35s", module_name);
		if ((pool_idx = find_pool(module_name)) != -1) {
			test_module(pool_idx);
			break;
		} else {
			printf("module not find, please try again!!!\n");
			continue;
		}
	}
	if (i == 3) {
		printf("module not in pool, return!\n");
		return -1;
	}
	return 0;
}

int32_t switch_all_test()
{
	int32_t pass_count = 0;
	int32_t fail_count = 0;
	int32_t no_support = 0;
	int32_t total =0;
    int32_t cnt = sizeof(test_pool) / sizeof(TEST_POOL_P);
	int32_t i, n;
	int32_t func_ret;

    for (i = 0; i < cnt; i++) {
        for (n = 0; n < test_pool[i]->pool_cnt; n++) {
			total++;
		    func_ret = test_func(test_pool[i]->pool[n].interface_name);
			if (func_ret == PASS) {
				pass_count++;
			} else if(func_ret == FAIL) {
				fail_count++;
			} else if (func_ret == NOT_SUPPORT){
				no_support++;
			} else {
				pass_count++;
			}

        }

    }
	printf("total: %d.  pass: %d.  fail: %d. no_support: %d.\n", total, pass_count, fail_count, no_support);
	return 0;

}

//以下三项可选择性运行
TEST(pol_test,func_test) 
{
	EXPECT_EQ(0, switch_func_test());
}

TEST(pol_test,module_test) 
{
	EXPECT_EQ(0, switch_module_test());
}

TEST(pol_test,all_test) 
{
	EXPECT_EQ(0, switch_all_test());
}


