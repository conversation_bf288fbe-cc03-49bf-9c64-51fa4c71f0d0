/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       private.h
 * @addtogroup 
 * @{
 * <AUTHOR>
 * @date    2023/11/28
 * @version v1.0
 * @details 
 * 
 */

#ifndef PESF_NET_PRIVATE_H
#define PESF_NET_PRIVATE_H


#ifdef __cplusplus
extern "C" {
#endif

#define NET_LOG_ERROR(fmt, ...) printf("ERROR: %s:%d "fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#define NET_LOG_INFO(fmt, ...)  printf("INFO: %s:%d " fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#define NET_LOG_DEBUG(fmt, ...)
    // printf("DEBUG: %s:%d "fmt, __FUNCTION__, __LINE__, ##__VA_ARGS__)

#ifdef __GNUC__
#define PESF_LIKELY(expr)    __builtin_expect(!!(expr), 1)
#define PESF_UNLIKELY(expr)  __builtin_expect(!!(expr), 0)
#define PRETTY_FUNCTION_NAME __PRETTY_FUNCTION__
#else
#define PESF_LIKELY(expr)    expr
#define PESF_UNLIKELY(expr)  expr
#define PRETTY_FUNCTION_NAME ""
#endif

#define RETURN_VAL_IF_FAIL(c, val)                                                                                     \
    do {                                                                                                               \
        if ( PESF_UNLIKELY( !(c) ) ) {                                                                                 \
            NET_LOG_ERROR("condition \"if ("#c")\" failed, %s ret%d\n", PRETTY_FUNCTION_NAME, val);                    \
            return val;                                                                                                \
        }                                                                                                              \
    } while (0)

#define RETURN_IF_FAIL(c)                                                                                              \
    do {                                                                                                               \
        if ( PESF_UNLIKELY( !(c) ) ) {                                                                                 \
            NET_LOG_ERROR("condition \"if ("#c")\" failed, %s \n", PRETTY_FUNCTION_NAME);                              \
            return ;                                                                                                   \
        }                                                                                                              \
    } while (0)

#define RETURN_JS_VALUE_IF_FAIL(c, val)                                                                                \
    do {                                                                                                               \
        if ( PESF_UNLIKELY( !(c) ) ) {                                                                                 \
            NET_LOG_ERROR("condition \"if ("#c")\" failed, %s ret%llx\n", PRETTY_FUNCTION_NAME, val);                    \
            return val;                                                                                                \
        }                                                                                                              \
    } while (0)

#ifdef __cplusplus
}  /* End of the 'extern "C"' block */
#endif

#endif //PESF_NET_PRIVATE_H

/**
*@}
*/