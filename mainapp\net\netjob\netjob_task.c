#include "nettypes.h"
#include "netjob_task.h"
#include "netjob.h"

#define NETJOB_TASK_INITIALIZER { .tid = INVALIDTHREAD, .sem = INVALIDSEM, .handler = NULL, .cookie = NULL, .jobid = 0 }

#define NETJOB_TASK_MLOCK_UN()  pi_mutex_unlock(s_netjob_task_lock);
#define NETJOB_TASK_MLOCK_EX()  pi_mutex_lock(s_netjob_task_lock);

typedef int32_t (*NETJOB_FUNC) (void *);

typedef struct netjob_task
{
    PI_THREAD_T             tid;
    PI_SEMAPHORE_T          sem;
    NETJOB_FUNC             handler;
    void*                   cookie;
    int32_t                 jobid;
}
NETJOB_TASK_S;

static NETJOB_TASK_S        s_netjob_task_pool[5] = { NETJOB_TASK_INITIALIZER, NETJOB_TASK_INITIALIZER, NETJOB_TASK_INITIALIZER, NETJOB_TASK_INITIALIZER, NETJOB_TASK_INITIALIZER };
static PI_MUTEX_T           s_netjob_task_lock = INVALIDMTX;

static void* netjob_task_handler(void* arg)
{
    NETJOB_TASK_S*  ptask = (NETJOB_TASK_S *)arg;
    time_t          start;
    time_t          end;

    while ( 1 )
    {
        pi_sem_wait(ptask->sem);

        if ( ptask->jobid <= 0 )
        {
            NET_INFO("invalid jobid(%d)", ptask->jobid);
            continue;
        }

        netjob_set_state(ptask->jobid, NETJOB_STATE_PROCESSING);
        pi_time(&start);
        if ( ptask->handler != NULL )
        {
            ptask->handler(ptask->cookie);
        }
        pi_time(&end);
        NET_DEBUG("netjob %d handler end in %d s", ptask->jobid, (int32_t)(end - start));

        NETJOB_TASK_MLOCK_EX();
        ptask->handler = NULL;
        ptask->cookie  = NULL;
        ptask->jobid   = 0;
        NETJOB_TASK_MLOCK_UN();
    }

    return NULL;
}

static NETJOB_TASK_S* netjob_task_search(int32_t jobid)
{
    NETJOB_TASK_S*  ptask = NULL;

    for ( size_t i = 0; i < ARRAY_SIZE(s_netjob_task_pool); ++i )
    {
        if ( s_netjob_task_pool[i].jobid == jobid )
        {
            ptask = &(s_netjob_task_pool[i]);
            break;
        }
    }

    return ptask;
}

int32_t netjob_start_task(int32_t jobid, int32_t (*handler)(void *), void* cookie)
{
    NETJOB_TASK_S*  ptask = NULL;

    NETJOB_TASK_MLOCK_EX();
    ptask = netjob_task_search(0);
    if ( ptask != NULL )
    {
        ptask->handler = handler;
        ptask->cookie  = cookie;
        ptask->jobid   = jobid;
    }
    NETJOB_TASK_MLOCK_UN();

    RETURN_VAL_IF(ptask == NULL, NET_INFO, -1);
    pi_sem_post(ptask->sem);
    return 0;
}

int32_t netjob_has_free_task(void)
{
    int32_t ret;

    NETJOB_TASK_MLOCK_EX();
    ret = ( netjob_task_search(0) ? 1 : 0 );
    NETJOB_TASK_MLOCK_UN();

    return ret;
}

int32_t netjob_init_tasks(void)
{
    NETJOB_TASK_S*  ptask = NULL;
    int32_t         ret = 0;
    size_t          i;

    RETURN_VAL_IF(s_netjob_task_lock != INVALIDMTX, NET_DEBUG, 0);

    s_netjob_task_lock = pi_mutex_create();
    RETURN_VAL_IF(s_netjob_task_lock == INVALIDMTX, NET_WARN, -1);

    for ( i = 0; i < ARRAY_SIZE(s_netjob_task_pool); ++i )
    {
        ptask = (NETJOB_TASK_S *)(&s_netjob_task_pool[i]);

        BREAK_IF((ptask->sem = pi_sem_create(0)) == INVALIDSEM, NET_WARN);

        ptask->tid = pi_thread_create(netjob_task_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, ptask, "netjob_task_handler"); 
        BREAK_IF(ptask->tid == INVALIDTHREAD, NET_WARN);
    }

    if ( i < ARRAY_SIZE(s_netjob_task_pool) )
    {
        netjob_deinit_tasks();
        ret = -1;
    }
    return ret;
}

void netjob_deinit_tasks(void)
{
    NETJOB_TASK_S*  ptask;

    if ( s_netjob_task_lock != INVALIDMTX )
    {
        NETJOB_TASK_MLOCK_EX();
        for ( size_t i = 0; i < ARRAY_SIZE(s_netjob_task_pool); ++i )
        {
            ptask = (NETJOB_TASK_S *)(&s_netjob_task_pool[i]);
            if ( ptask->tid != INVALIDTHREAD )
            {
                pi_thread_destroy(ptask->tid);
                ptask->tid = INVALIDTHREAD;
            }
            if ( ptask->sem != INVALIDSEM )
            {
                pi_sem_destroy(ptask->sem);
                ptask->sem = INVALIDSEM;
            }
            ptask->handler = NULL;
            ptask->cookie  = NULL;
            ptask->jobid   = 0;
        }
        NETJOB_TASK_MLOCK_UN();

        pi_mutex_destroy(s_netjob_task_lock);
        s_netjob_task_lock = INVALIDMTX;
    }
}

