/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file sys.h
 * @addtogroup basic
 * @{
 * @addtogroup basic
 * @autor
 * @date 2024-06-11
 * @brief The C Standard Library <stdint.h> header.
 */

#pragma once
#define _STDINT

#include <vcruntime.h>

#if _VCRT_COMPILER_PREPROCESSOR

#pragma warning(push)
#pragma warning(disable: _VCRUNTIME_DISABLED_WARNINGS)

typedef signed char        int8_t;
typedef short              int16_t;
typedef int                int32_t;
typedef long long          int64_t;
typedef unsigned char      uint8_t;
typedef unsigned short     uint16_t;
typedef unsigned int       uint32_t;
typedef unsigned long long uint64_t;

typedef signed char        int_least8_t;
typedef short              int_least16_t;
typedef int                int_least32_t;
typedef long long          int_least64_t;
typedef unsigned char      uint_least8_t;
typedef unsigned short     uint_least16_t;
typedef unsigned int       uint_least32_t;
typedef unsigned long long uint_least64_t;

typedef signed char        int_fast8_t;
typedef int                int_fast16_t;
typedef int                int_fast32_t;
typedef long long          int_fast64_t;
typedef unsigned char      uint_fast8_t;
typedef unsigned int       uint_fast16_t;
typedef unsigned int       uint_fast32_t;
typedef unsigned long long uint_fast64_t;

typedef long long          intmax_t;
typedef unsigned long long uintmax_t;

// These macros must exactly match those in the Windows SDK's intsafe.h.
#define INT8_MIN         (-127i8 - 1)                   ///<INT8_MIN
#define INT16_MIN        (-32767i16 - 1)                ///<INT16_MIN
#define INT32_MIN        (-2147483647i32 - 1)           ///<INT32_MIN
#define INT64_MIN        (-9223372036854775807i64 - 1)  ///<INT64_MIN
#define INT8_MAX         127i8                          ///<INT8_MAX
#define INT16_MAX        32767i16                       ///<INT16_MAX
#define INT32_MAX        2147483647i32                  ///<INT32_MAX
#define INT64_MAX        9223372036854775807i64         ///<INT64_MAX
#define UINT8_MAX        0xffui8                        ///<UINT8_MAX
#define UINT16_MAX       0xffffui16                     ///<UINT16_MAX
#define UINT32_MAX       0xffffffffui32                 ///<UINT32_MAX
#define UINT64_MAX       0xffffffffffffffffui64         ///<UINT64_MAX

#define INT_LEAST8_MIN   INT8_MIN                       ///<INT_LEAST8_MIN
#define INT_LEAST16_MIN  INT16_MIN                      ///<INT_LEAST16_MIN
#define INT_LEAST32_MIN  INT32_MIN                      ///<INT_LEAST32_MIN
#define INT_LEAST64_MIN  INT64_MIN                      ///<INT_LEAST64_MIN
#define INT_LEAST8_MAX   INT8_MAX                       ///<INT_LEAST8_MAX
#define INT_LEAST16_MAX  INT16_MAX                      ///<INT_LEAST16_MAX
#define INT_LEAST32_MAX  INT32_MAX                      ///<INT_LEAST32_MAX
#define INT_LEAST64_MAX  INT64_MAX                      ///<INT_LEAST64_MAX
#define UINT_LEAST8_MAX  UINT8_MAX                      ///<UINT_LEAST8_MAX
#define UINT_LEAST16_MAX UINT16_MAX                     ///<UINT_LEAST16_MAX
#define UINT_LEAST32_MAX UINT32_MAX                     ///<UINT_LEAST32_MAX
#define UINT_LEAST64_MAX UINT64_MAX                     ///<UINT_LEAST64_MAX

#define INT_FAST8_MIN    INT8_MIN                       ///<INT_FAST8_MIN
#define INT_FAST16_MIN   INT32_MIN                      ///<INT_FAST16_MIN
#define INT_FAST32_MIN   INT32_MIN                      ///<INT_FAST32_MIN
#define INT_FAST64_MIN   INT64_MIN                      ///<INT_FAST64_MIN
#define INT_FAST8_MAX    INT8_MAX                       ///<INT_FAST8_MAX
#define INT_FAST16_MAX   INT32_MAX                      ///<INT_FAST16_MAX
#define INT_FAST32_MAX   INT32_MAX                      ///<INT_FAST32_MAX
#define INT_FAST64_MAX   INT64_MAX                      ///<INT_FAST64_MAX
#define UINT_FAST8_MAX   UINT8_MAX                      ///<UINT_FAST8_MAX
#define UINT_FAST16_MAX  UINT32_MAX                     ///<UINT_FAST16_MAX
#define UINT_FAST32_MAX  UINT32_MAX                     ///<UINT_FAST32_MAX
#define UINT_FAST64_MAX  UINT64_MAX                     ///<UINT_FAST64_MAX

#ifdef _WIN64
    #define INTPTR_MIN   INT64_MIN                      ///<INTPTR_MIN
    #define INTPTR_MAX   INT64_MAX                      ///<INTPTR_MAX
    #define UINTPTR_MAX  UINT64_MAX                     ///<UINTPTR_MAX
#else
    #define INTPTR_MIN   INT32_MIN                      ///<INTPTR_MIN
    #define INTPTR_MAX   INT32_MAX                      ///<INTPTR_MAX
    #define UINTPTR_MAX  UINT32_MAX                     ///<UINTPTR_MAX
#endif

#define INTMAX_MIN       INT64_MIN                      ///<INTMAX_MIN
#define INTMAX_MAX       INT64_MAX                      ///<INTMAX_MAX
#define UINTMAX_MAX      UINT64_MAX                     ///<UINTMAX_MAX

#define PTRDIFF_MIN      INTPTR_MIN                     ///<PTRDIFF_MIN
#define PTRDIFF_MAX      INTPTR_MAX                     ///<PTRDIFF_MAX

#ifndef SIZE_MAX
    /* SIZE_MAX definition must match exactly with limits.h for modules support. */
    #ifdef _WIN64
        #define SIZE_MAX 0xffffffffffffffffui64         ///<SIZE_MAX
    #else
        #define SIZE_MAX 0xffffffffui32                 ///<SIZE_MAX
    #endif
#endif

#define SIG_ATOMIC_MIN   INT32_MIN                      ///<SIG_ATOMIC_MIN
#define SIG_ATOMIC_MAX   INT32_MAX                      ///<SIG_ATOMIC_MAX

#define WCHAR_MIN        0x0000                         ///<WCHAR_MIN
#define WCHAR_MAX        0xffff                         ///<WCHAR_MAX

#define WINT_MIN         0x0000                         ///<WINT_MIN
#define WINT_MAX         0xffff                         ///<WINT_MAX

#define INT8_C(x)    (x)                                ///<INT8_C
#define INT16_C(x)   (x)                                ///<INT16_C
#define INT32_C(x)   (x)                                ///<INT32_C
#define INT64_C(x)   (x ## LL)                          ///<INT64_C

#define UINT8_C(x)   (x)                                ///<UINT8_C
#define UINT16_C(x)  (x)                                ///<UINT16_C
#define UINT32_C(x)  (x ## U)                           ///<UINT32_C
#define UINT64_C(x)  (x ## ULL)                         ///<UINT64_C

#define INTMAX_C(x)  INT64_C(x)                         ///<INTMAX_C
#define UINTMAX_C(x) UINT64_C(x)                        ///<UINTMAX_C

#pragma warning(pop) /* _VCRUNTIME_DISABLED_WARNINGS */

#endif /* _VCRT_COMPILER_PREPROCESSOR */
/**
 * @}
 */
