#ifndef __WSDEF_H__
#define __WSDEF_H__

#define WSD_PRT_COMPATIBLE_ID           "http://schemas.microsoft.com/windows/2006/08/wdp/print/PrinterServiceType"
#define WSD_PRT_SERVICE_TYPE            "wprt:PrinterServiceType"

#define WSD_SCN_COMPATIBLE_ID           "http://schemas.microsoft.com/windows/2006/08/wdp/scan/ScannerServiceType"
#define WSD_SCN_SERVICE_TYPE            "wscn:ScannerServiceType"

#define WSD_UUID_DEFAULT                "6861d9b0-a100-11e0-8264-%02x%02x%02x%02x%02x%02x"

#define WSD_DEVICE_URL                  "http://www.pantum.com"

#if CONFIG_PRINT && CONFIG_SCAN /* 多功能 */
#define WSDISCO_DEVICE_TYPE             "wprt:PrintDeviceType wscn:ScanDeviceType wsdp:Device"
#define WSDMETA_CATEGORIES              "Printers MFP Scanners"
#elif CONFIG_PRINT /* 仅打印 */
#define WSDISCO_DEVICE_TYPE             "wprt:PrintDeviceType wsdp:Device"
#define WSDMETA_CATEGORIES              "Printers"
#elif CONFIG_SCAN /* 仅扫描 */
#define WSDISCO_DEVICE_TYPE             "wscn:ScanDeviceType wsdp:Device"
#define WSDMETA_CATEGORIES              "Scanners"
#else
#define WSDISCO_DEVICE_TYPE             "wsdp:Device"
#define WSDMETA_CATEGORIES              ""
#endif

#define WSD_NAME_SPACES                                                                                                 \
"    xmlns:wsa=\"http://schemas.xmlsoap.org/ws/2004/08/addressing\"\r\n"                                                \
"    xmlns:wsd=\"http://schemas.xmlsoap.org/ws/2005/04/discovery\"\r\n"                                                 \
"    xmlns:wse=\"http://schemas.xmlsoap.org/ws/2004/08/eventing\"\r\n"                                                  \
"    xmlns:wsx=\"http://schemas.xmlsoap.org/ws/2004/09/mex\"\r\n"                                                       \
"    xmlns:wst=\"http://schemas.xmlsoap.org/ws/2004/09/transfer\"\r\n"                                                  \
"    xmlns:wsdp=\"http://schemas.xmlsoap.org/ws/2006/02/devprof\"\r\n"                                                  \
"    xmlns:xop=\"http://www.w3.org/2004/08/xop/include\"\r\n"                                                           \
"    xmlns:wprt=\"http://schemas.microsoft.com/windows/2006/08/wdp/print\"\r\n"                                         \
"    xmlns:wscn=\"http://schemas.microsoft.com/windows/2006/08/wdp/scan\"\r\n"                                          \
"    xmlns:pnpx=\"http://schemas.microsoft.com/windows/pnpx/2005/10\"\r\n"

#define MATCH_SOAP_FORMAT                                                                                               \
"<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n"                                                                        \
"<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"\r\n"                                             \
"    xmlns:wsa=\"http://schemas.xmlsoap.org/ws/2004/08/addressing\"\r\n"                                                \
"    xmlns:wsd=\"http://schemas.xmlsoap.org/ws/2005/04/discovery\"\r\n"                                                 \
"    xmlns:wsdp=\"http://schemas.xmlsoap.org/ws/2006/02/devprof\"\r\n"                                                  \
"    xmlns:wprt=\"http://schemas.microsoft.com/windows/2006/08/wdp/print\"\r\n"                                         \
"    xmlns:wscn=\"http://schemas.microsoft.com/windows/2006/08/wdp/scan\"\r\n"                                          \
">\r\n"                                                                                                                 \
"<soap:Header>\r\n"                                                                                                     \
"<wsa:To>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:To>\r\n"                                  \
"<wsa:Action>http://schemas.xmlsoap.org/ws/2005/04/discovery/%sMatches</wsa:Action>\r\n" /* probe or Resolve */         \
"<wsa:MessageID>urn:uuid:%s</wsa:MessageID>\r\n" /* message ID */                                                       \
"<wsa:RelatesTo>%s</wsa:RelatesTo>\r\n" /* original probe message ID */                                                 \
"    <wsd:AppSequence InstanceId=\"%d\" MessageNumber=\"%d\"></wsd:AppSequence>\r\n" /* instance and message count */   \
"</soap:Header>\r\n"                                                                                                    \
"<soap:Body>\r\n"                                                                                                       \
"<wsd:%sMatches>\r\n" /* probe or Resolve */                                                                            \
"<wsd:%sMatch>\r\n" /* probe or Resolve */                                                                              \
"<wsa:EndpointReference>\r\n"                                                                                           \
"<wsa:Address>urn:uuid:%s</wsa:Address>\r\n" /* UUID address */                                                         \
"</wsa:EndpointReference>\r\n"                                                                                          \
"<wsd:Types>" WSDISCO_DEVICE_TYPE "</wsd:Types>\r\n"                                                                    \
"<wsd:XAddrs>%s</wsd:XAddrs>\r\n" /* server url */                                                                      \
"<wsd:MetadataVersion>1</wsd:MetadataVersion>\r\n"                                                                      \
"</wsd:%sMatch>\r\n" /* probe or Resolve */                                                                             \
"</wsd:%sMatches>\r\n" /* probe or Resolve*/                                                                            \
"</soap:Body>\r\n"                                                                                                      \
"</soap:Envelope>"

#define SOAP_RESPONSE_HEADER                                                                                            \
"<soap:Header>\r\n"                                                                                                     \
"    <wsa:To>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:To>\r\n"                              \
"    <wsa:Action>%sResponse</wsa:Action>\r\n" /* incoming action++"Response" */                                         \
"    <wsa:MessageID>urn:uuid:%s</wsa:MessageID>\r\n" /* outgoing Message ID */                                          \
"    <wsa:RelatesTo>%s</wsa:RelatesTo>\r\n" /* incoming original messageID */                                           \
"</soap:Header>\r\n"

#define SOAP_METADATA                                                                                                   \
"<?xml version=\"1.0\" encoding=\"utf-8\" ?>\r\n"                                                                       \
"<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\"\r\n"                                             \
WSD_NAME_SPACES /* extra namespaces */                                                                                  \
">\r\n"                                                                                                                 \
"<soap:Header>\r\n"                                                                                                     \
"    <wsa:To>http://schemas.xmlsoap.org/ws/2004/08/addressing/role/anonymous</wsa:To>\r\n"                              \
"    <wsa:Action>http://schemas.xmlsoap.org/ws/2004/09/transfer/GetResponse</wsa:Action>\r\n"                           \
"    <wsa:MessageID>urn:uuid:%s</wsa:MessageID>\r\n" /* outgoing message ID */                                          \
"    <wsa:RelatesTo>%s</wsa:RelatesTo>\r\n" /* incoming (get) message ID */                                             \
"</soap:Header>\r\n"                                                                                                    \
"<soap:Body>\r\n"                                                                                                       \
"    <wsx:Metadata>\r\n"                                                                                                \
"        <wsx:MetadataSection Dialect=\"http://schemas.xmlsoap.org/ws/2006/02/devprof/ThisDevice\">\r\n"                \
"            <wsdp:ThisDevice>\r\n"                                                                                     \
"                <wsdp:FriendlyName>%s (%s series)</wsdp:FriendlyName>\r\n" /* hostname (product name series) */        \
"                <wsdp:FirmwareVersion>%s</wsdp:FirmwareVersion>\r\n" /* firmware version */                            \
"                <wsdp:SerialNumber>%s</wsdp:SerialNumber>\r\n" /* serial number */                                     \
"            </wsdp:ThisDevice>\r\n"                                                                                    \
"        </wsx:MetadataSection>\r\n"                                                                                    \
"        <wsx:MetadataSection Dialect=\"http://schemas.xmlsoap.org/ws/2006/02/devprof/ThisModel\">\r\n"                 \
"            <wsdp:ThisModel>\r\n"                                                                                      \
"                <wsdp:Manufacturer>%s</wsdp:Manufacturer>\r\n" /* manufacturer name */                                 \
"                <wsdp:ManufacturerUrl>" WSD_DEVICE_URL "</wsdp:ManufacturerUrl>\r\n"                                   \
"                <wsdp:ModelName>%s series</wsdp:ModelName>\r\n" /* pruduct name */                                     \
"                <wsdp:ModelNumber>%s series</wsdp:ModelNumber>\r\n" /* product name */                                 \
"                <wsdp:ModelUrl>" WSD_DEVICE_URL "</wsdp:ModelUrl>\r\n"                                                 \
"                <wsdp:PresentationUrl>http://%s:80/</wsdp:PresentationUrl>\r\n" /* the url of UI webpage */            \
"                <pnpx:DeviceCategory>" WSDMETA_CATEGORIES "</pnpx:DeviceCategory>\r\n" /* device category */           \
"            </wsdp:ThisModel>\r\n"                                                                                     \
"        </wsx:MetadataSection>\r\n"                                                                                    \
"        <wsx:MetadataSection Dialect=\"http://schemas.xmlsoap.org/ws/2006/02/devprof/Relationship\">\r\n"              \
"            <wsdp:Relationship Type=\"http://schemas.xmlsoap.org/ws/2006/02/devprof/host\">\r\n"                       \
"%s" /* all SOAP_SERVICEDATA to here */                                                                                 \
"            </wsdp:Relationship>\r\n"                                                                                  \
"        </wsx:MetadataSection>\r\n"                                                                                    \
"    </wsx:Metadata>\r\n"                                                                                               \
"</soap:Body>\r\n"                                                                                                      \
"</soap:Envelope>"

#define SOAP_SERVICEDATA                                                                                                \
"                <wsdp:Hosted>\r\n"                                                                                     \
"                    <wsa:EndpointReference>\r\n"                                                                       \
"                        <wsa:Address>http://%s:%u/%u</wsa:Address>\r\n" /* url for service */                          \
"                    </wsa:EndpointReference>\r\n"                                                                      \
"                    <wsdp:Types>%s</wsdp:Types>\r\n" /* service type */                                                \
"                    <wsdp:ServiceId>" WSD_DEVICE_URL "/%d</wsdp:ServiceId>\r\n" /* service number */                   \
"                    <pnpx:CompatibleId>%s</pnpx:CompatibleId>\r\n"                                                     \
"                </wsdp:Hosted>\r\n"

#endif /* __WSDEF_H__ */
