/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  scan_event.h
* @addtogroup scan
*
* @{
* @addtogroup  scan
* <AUTHOR>
* @date   2022-5-24
* @version  v1.0
* @brief   this is a main scan event job process for the moudle scan_event
**/

#ifndef _SCAN_EVENT_H_
#define _SCAN_EVENT_H_
#include "moduleid.h"
#include "nvram.h"
#include "statusid.h"
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_mgr.h"

#include "scan_var.h"

#define SCNA_EVENT_FTP_ADDR_NUM   60    ///< scan event ftp addr number
#define SCAN_EVENT_EMAIL_ADDR_NUM 60    ///< scan event email addr number
#define SCAN_EVENT_GROUP_ADDR_NUM 10    ///< scan event group addr number
#define SCAN_EVENT_PARAM_NAME_LEN 64    ///< scan event param name len

#define VERSION_LENGTH 32

/**
 * @brief data operation
 */
typedef enum
{
    OP_INVALID, ///< invaild
    OP_ADD,     ///< add
    OP_DELETE,  //< delete
    OP_UPDATE,  ///< updata
    OP_MAX      ///< max
} DATA_OPERATION_E;

/**
 * @brief scan dest
 */
typedef enum
{
    OP_DEST_INVALID,    ///< invald
    OP_DEST_FTP,        ///< dest ftp
    OP_DEST_MAIL,       ///< dest mail
    OP_DEST_GROUP       ///< dest gourp
} OP_DEST_E;

/**
 * @brief scan param
 */
typedef struct
{
    char            param_name[SCAN_EVENT_PARAM_NAME_LEN];  ///< param name
    void*            param_value;                           ///< param value
    uint32_t        param_size;                             ///< use for nv size and event size too
    uint32_t        nv_id;                                  ///< use for nv size and event size too
    VARIABLE_TYPE_E nv_type;                                ///< use for nv size and event size too
    uint32_t        notify_evt_id;                          //< scan notify other module the value is change.
    uint32_t        req_evt_id;                             ///< other module set value to scan

} SCAN_PARAM_S;

/**
 * @brief ftp param edit
 */
typedef struct
{
    DATA_OPERATION_E data_operation;    ///< data operation
    FTP_PARM_S       ftp_param;         ///< ftp params
    uint32_t         ftp_multi_flag;    ///< ftp multi flag
    uint32_t         ftp_array[FTP_PARM_MAX];   ///< ftp array
} FTP_PARM_EDIT_S;

/**
 * @brief mail param edit
 */
typedef struct
{
    DATA_OPERATION_E data_operation;    ///< data operation
    MAIL_PARM_S      mail_param;        ///< mail params
    uint32_t         mail_multi_flag;   ///< mail params
    uint32_t         mail_array[MAIL_ADDR_NUM_MAX]; ///< mail array
} MAIL_PARM_EDIT_S;

/**
 * @brief group param edit
 */
typedef struct
{
    DATA_OPERATION_E data_operation;    ///< data operation
    MAIL_GROUP_MGR_S group_mail_param;  ///< group params
    uint32_t         group_multi_flag;  ///< group params
    uint32_t         group_array[MAIL_GROUP_MAX];///< group array
} GROUP_PARM_EDIT_S;

/**
* @brief scan source page num count, include scan to pc/emial/udisk...
*/
typedef struct
{
    uint32_t    scan2copy_num;                      ///< scan to copy page num
    uint32_t    scan2pc_num;                        ///< scan to pc page num
    uint32_t    scan2udisk_num;                     ///< scan to udisk page num
    uint32_t    scan2net_num;                       ///< scan to net page num
    uint32_t    scan2smb_num;                       ///< scan to smb page num
    uint32_t    scan2email_num;                     ///< scan to email page num
    uint32_t    scan2ftp_num;                       ///< scan to ftp page num
}SCAN_SOURCE_PAGE_NUM_S;

#define PAGES_COUNTER_SUM(s)    ( \
        (s)->a3_num             + \
        (s)->a4_num             + \
        (s)->a5_num             + \
        (s)->jisb5_num          + \
        (s)->a6_num             + \
        (s)->jisb6_num          + \
        (s)->letter_num         + \
        (s)->legal_num          + \
        (s)->paper_8k_num       + \
        (s)->big16k_num         + \
        (s)->other_num )

/**
* @brief scan paper size page num
*/
typedef struct
{
    uint32_t    a3_num;                             ///< scan a3 num
    uint32_t    a4_num;                             ///< scan a4 num
    uint32_t    a5_num;                             ///< scan a5 num
    uint32_t    jisb5_num;                          ///< scan jisb5 num
    uint32_t    a6_num;                             ///< scan a6 num
    uint32_t    jisb6_num;                          ///< scan jisb6 num
    uint32_t    letter_num;                         ///< scan letter num
    uint32_t    legal_num;                          ///< scan legal num
    uint32_t    paper_8k_num;                       ///< scan 8k num
    uint32_t    big16k_num;                         ///< scan big16k num
    uint32_t    other_num;                          ///< other paper size num
}SCAN_PAPER_SIZE_NUM_S;

/**
 * @brief get firmware version
 * <AUTHOR>
 * @date 2023-08-14
 */
typedef struct
{
    char engine_version[VERSION_LENGTH];
    char front_fpga_version[VERSION_LENGTH];
    char back_fpga_version[VERSION_LENGTH];
    char front_6220_firmware_version[VERSION_LENGTH];
    char back_6220_firmware_version[VERSION_LENGTH];
}FIRMWARE_VERSION_S;

/*scanner maintain mode param define start*/

/**
 * @brief fb scan sensor test
 * <AUTHOR>
 * @date 2023-11-1
 */
typedef struct
{
    uint32_t fb_doc_size_sensor1;
    uint32_t fb_doc_size_sensor2;
    uint32_t fb_home_pos_sensor;
    uint32_t df_shading_home_pos_sensor;
    uint32_t fb_cover_0_degree_sensor;
    uint32_t fb_cover_20_degree_sensor;

}SCAN_FB_SENSOR_S;

/**
 * @brief adf scan sensor test
 * <AUTHOR>
 * @date 2023-11-1
 */
typedef struct
{
    uint32_t adf_paper_sensor;
    uint32_t adf_behind_separation_sensor;
    uint32_t adf_resist_loop_sensor;
    uint32_t adf_before_scan_slit_sensor;
    uint32_t adf_exit_sensor;
    uint32_t adf_take_up_section_cover_sensor;
    uint32_t adf_cover_sensor;
    uint32_t adf_cis_guide_section_sensor;
    uint32_t adf_mix_size_sensor1;
    uint32_t adf_mix_size_sensor2;
    uint32_t adf_mix_size_sensor3;
    uint32_t adf_length_sensor1;
    uint32_t adf_length_sensor2;
    uint32_t adf_contact_retract_sensor;
    uint32_t adf_fan_lock_sensor;
    uint32_t adf_lift_up_limit_sensor;
    uint32_t adf_lift_low_limit_sensor;
    uint32_t adf_brush_home_pos_sensor;

}SCAN_ADF_SENSOR_S;

/**
 * @brief fb/adf scan sensor test
 * <AUTHOR>
 * @date 2023-10-14
 */
typedef struct
{
    /*ui set param by req event*/
    int      sensor_op;        /*0 invalid, 1 start, 2 end*/
    int      sensor_src;       /*0 invalid, 1 FB, 2 ADF*/

    /*scanner rsp param by modify event*/
    int      scan_op_rsp;    /*-1 failed, 0 success*/
    SCAN_FB_SENSOR_S    fb_sensor;
    SCAN_ADF_SENSOR_S   adf_sensor;

}SCAN_FB_ADF_SENSOR_S;

/**
 * @brief fb/adf scaning test
 * <AUTHOR>
 * @date 2023-10-14
 */
typedef struct
{
    /*ui set param by req event*/
    int scan_op;        /*0 invalid, 1 start, 2 cancel*/

    /*scanner rsp param by modify event*/
    int scan_op_rsp;    /*-1 failed, 0 success*/
    int scan_fb_num;
    int scan_adf_num;
}SCAN_FB_ADF_SCANING_S;

/**
 * @brief scan adf engine param set
 * <AUTHOR>
 * @date 2023-10-14
 */
typedef struct
{
    /*ui set param by req event*/
    int main_scanner_front_margin;
    int sub_scanner_front_margin;

    int main_scanner_back_margin;
    int sub_scanner_back_margin;

    int front_scale;
    int back_scale;

    int adf_skew;   /* -5~5 */

    /*scanner rsp param by modify event*/
    int adf_param_rsp;  /*-1 failed, 0 success*/
}SCAN_ADF_ENGINE_PARAM_SET_S;

/**
 * @brief scan fb engine param set
 * <AUTHOR>
 * @date 2023-10-14
 */
typedef struct
{
    /*ui set param by req event*/
    int top_margin;
    int left_margin;
    int vertical_scale;
    int horizontal_scale;
    int df_position;

    /*scanner rsp param by modify event*/
    int fb_param_rsp;  /*-1 failed, 0 success*/
}SCAN_FB_ENGINE_PARAM_SET_S;

typedef struct
{
    /*ui set scan error of needing to set by req event*/
    SCAN_ERROR_TYPE_E   scan_error;

    /*scanner rsp param by modify event*/
    int                 clear_error_rsp;
}SCAN_ENGINE_ERROR_CLEAR_S;

/*scanner maintain mode param define end*/

/**
 * @brief scan_event_param_set_value_by_evt_id
 * @param[in] evt_id event id
 * @param[in] value event value
 * @return int  \n
 * @retval success:0, failed:-1;
 * <AUTHOR>
 * @date 2022-5-24
 */
int scan_event_param_set_value_by_evt_id( uint32_t evt_id, void* value );

/**
 * @brief scan_event_param_set_int_value_by_nv_id
 * @param[in] evt_id event id
 * @param[in] value event value
 * @return int  \n
 * @retval success:0, failed:-1;
 * <AUTHOR>
 * @date 2022-5-24
 */
int scan_event_param_set_int_value_by_nv_id( uint32_t nv_id, int32_t value );

/**
 * @brief scan_event_param_set_value_by_nv_id
 * @param[in] evt_id event id
 * @param[in] value event value
 * @return int  \n
 * @retval success:0, failed:-1;
 * <AUTHOR>
 * @date 2022-5-24
 */
int scan_event_param_set_value_by_nv_id( uint32_t nv_id, void* value );

/**
 * @brief scan_event_param_get_value_by_nv_id
 * @param[in] evt_id event id
 * @return void*  \n
 * @retval get value
 * <AUTHOR>
 * @date 2022-5-24
 */
void* scan_event_param_get_value_by_nv_id( uint32_t nv_id );

/**
 * @brief scan_event_param_get_int_value_by_nv_id
 * @param[in] evt_id event id
 * @return int \n
 * @retval get value
 * <AUTHOR>
 * @date 2022-5-24
 */
#ifdef CONFIG_SCAN
int scan_event_param_get_int_value_by_nv_id( uint32_t nv_id );
#else
static inline int scan_event_param_get_int_value_by_nv_id( uint32_t nv_id ){return 0;};
#endif


/**
 * @brief scan_event_state_notify_system
 * @param[in] scanner_state scanner status
 * @return int \n
 * @retval success:0, failed:-1;
 * <AUTHOR>
 * @date 2022-5-24
 */
int scan_event_state_notify_system( void* data, uint32_t data_size );

/**
 * @brief scan_event_type_notify
 * @param[in] evt_id event id
 * @param[in] data data
 * @param[in] data_len data lenth
 * @return int \n
 * @retval success:0, failed:-1;
 * <AUTHOR>
 * @date 2022-5-24
 */
int scan_event_type_notify( EVT_TYPE_E scan_event, void* data, uint32_t data_len );

/**
 * @brief scan_event_ftp_addr_get
 * @param[in] ftp_index ftp index
 * @return FTP_PARM_P \n
 * @retval ftp information
 * <AUTHOR>
 * @date 2022-5-24
 */
FTP_PARM_P scan_event_ftp_addr_get( uint32_t ftp_index );

/**
 * @brief scan_event_email_addr_get
 * @param[in] email_index email index
 * @return MAIL_PARM_P \n
 * @retval email information
 * <AUTHOR>
 * @date 2022-5-24
 */
MAIL_PARM_P scan_event_email_addr_get( uint32_t email_index );

/**
 * @brief scan_event_group_addr_get
 * @param[in] group_index group index
 * @return MAIL_GROUP_MGR_S \n
 * @retval group information
 * <AUTHOR>
 * @date 2022-5-24
 */
MAIL_GROUP_MGR_S* scan_event_group_addr_get( uint32_t group_index );

#if 0
int32_t pi_scan_acl_set_adf_top_margin( uint32_t value );
int32_t pi_scan_acl_get_adf_top_margin( uint32_t* value );
int32_t pi_scan_acl_set_adf_left_margin( uint32_t value );
int32_t pi_scan_acl_get_adf_left_margin( uint32_t* value );
int32_t pi_scan_acl_set_fb_top_margin( uint32_t value );
int32_t pi_scan_acl_get_fb_top_margin( uint32_t* value );
int32_t pi_scan_acl_set_fb_left_margin( uint32_t value );
int32_t pi_scan_acl_get_fb_left_margin( uint32_t* value );
int32_t pi_scan_acl_set_scan_total_page( uint32_t value );
int32_t pi_scan_acl_get_scan_total_page( uint32_t* value );
int32_t pi_scan_acl_set_adf_paper_status( uint32_t value );
int32_t pi_scan_acl_get_adf_paper_status( uint32_t* value );
int32_t pi_scan_acl_set_adf_cover_status( uint32_t value );
int32_t pi_scan_acl_get_adf_cover_status( uint32_t* value );
int32_t pi_scan_acl_get_paper_sensor_detection( uint32_t* value );
int32_t pi_scan_acl_set_paper_sensor_detection( uint32_t value );
int32_t pi_scan_acl_set_home_sensor_detection( uint32_t value );
int32_t pi_scan_acl_get_home_sensor_detection( uint32_t* value );
int32_t pi_scan_acl_set_fb_function_switch( uint32_t value );
int32_t pi_scan_acl_get_fb_function_switch( uint32_t* value );
int32_t pi_scan_acl_set_adf_function_switch( uint32_t value );
int32_t pi_scan_acl_get_adf_function_switch( uint32_t* value );
int32_t pi_scan_acl_set_fb_Scanner_sensor( uint32_t value );
int32_t pi_scan_acl_get_fb_Scanner_sensor( uint32_t* value );
int32_t pi_scan_acl_set_adf_transport_test( uint32_t value );
int32_t pi_scan_acl_get_adf_transport_test( uint32_t* value );
int32_t pi_scan_acl_set_adf_vertical_amplification( uint32_t value );
int32_t pi_scan_acl_get_adf_vertical_amplification( uint32_t* value );
int32_t pi_scan_acl_set_fb_vertical_amplification( uint32_t value );
int32_t pi_scan_acl_get_fb_vertical_amplification( uint32_t* value );
int scan_try_wakeup( void );
#endif

int scan_event_notify( uint32_t event_type, const void* data, uint32_t data_length );

const char* scan_event_storage_path_get( void );

int scan_event_job_control_get( void );

void scan_event_notify_all_param( void );

/**
 * @brief scan_event_prolog
 * @return MAIL_GROUP_MGR_S \n
 * @retval success:0, failed:-1;
 * <AUTHOR>
 * @date 2022-5-24
 */
int scan_event_prolog( void );

#endif

/**
 *@}
 */

