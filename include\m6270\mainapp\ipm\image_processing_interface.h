/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       image_processing_interface.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process interface
 * <AUTHOR> (<EMAIL>)
 * @date       2021-11-01
 * @version    v1.0
 * @details    image processing API
 */

#ifndef IMAGE_PROCESSING_INTERFACE_H
#define IMAGE_PROCESSING_INTERFACE_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "memmgr/memmgr.h"
#include "step_core.h"
#include "image_process_common.h"
#include "pipeline.h"
#include "pipelines_mgr.h"

#define MAX_PIPELINES        4  ///< max pipeline number

/**
 * @brief image processing interface
 */
typedef void* IMAGE_PROCESSING_INTERFACE_P;

/**
 * @brief tag interface
 */
struct tag_interface;

/**
 * @brief state call back function
 */
typedef int32_t (*INRERFACE_CALLBACK_STATE)  (IMAGE_PROCESSING_INTERFACE_P interface,uint32_t state);

/**
 * @brief dataout call back function
 */
typedef int32_t (*INRERFACE_CALLBACK_DATAOUT) (IMAGE_PROCESSING_INTERFACE_P interface,uint32_t stage,void *data,STEP_COMMON_PARAM_P parm_info);

/**
 * @brief interface object
 */
typedef  struct tag_interface
{
    //interface info
    uint32_t             id;                        ///<interface id
    uint32_t             state;                     ///<interface state
    PIPE_LINE_P          pipeline;                  ///<pipeline object
    char                 pipe_rule[2048];           ///<pipeline rule
    PIPE_LINE_MSG_P      mailbox_id;                ///<mailbox id
    MEMOBJ_P             mem_handle;                ///<memory  object
    void                 *context;                  ///< point to location of being analysed
    INRERFACE_CALLBACK_STATE     interface_state;     ///< state call back function
    INRERFACE_CALLBACK_DATAOUT   interface_dataout; ///< dataout call back function
}IMAGE_PROCESSING_INTERFACE_S;

/**
 * @brief interface important information object
 */
typedef struct tag_info
{
    char     *pipe_rule;                             ///<pipeline rule
    MEMOBJ_P  mem_handle;                           ///<memory  object
    INRERFACE_CALLBACK_STATE interface_state;       ///< state call back function
    INRERFACE_CALLBACK_DATAOUT interface_dataout;   ///< dataout call back function
    void *context;
}IMAGE_PROCESSING_INFO_S,*IMAGE_PROCESSING_INFO_P;


/**
 * @brief image processing interface msg
 */
typedef enum
{
    MSG_IPM_DATAFLOW_START = 0,         ///<dataflow start
    MSG_IPM_PAGE_START,                   ///< page start
    MSG_IPM_PAGE_DATA,                  ///< page data
    MSG_IPM_PAGE_END,                   ///< page end
    MSG_IPM_DATAFLOW_STOP,              ///< dataflow stop
    MSG_IPM_DATAFLOW_CANCEL,            ///< dataflow cancel
}IMAGE_PROCESSING_INTERFACE_MSG_E;

/**
 * @brief image processing interface status
 */
typedef enum
{
    INTERFACE_IDLE = 0,       ///<idle status
    INTERFACE_NOMAL,          ///< normal status
    INTERFACE_ERROR,          ///< error status
}IMAGE_PROCESSING_INTERFACE_STATUS_E;

/**
 * @brief interface pipeline msg
 */
typedef struct tag_interface_pipeline_msg
{
    uint32_t    action_type;    ///< action type
    uint32_t    msg2;           ///< msg2
    uint32_t    msg3;           ///< msg3
    union
    {
        uint64_t  ullval;     ///< Fourth parameter, can by up to 64 bits
        void   *ptrval;     ///< or, a pointer parameter if needed
    }msg4;
}INTERFACE_PIPE_LINE_MSG_S, *INTERFACE_PIPE_LINE_MSG_P;

#ifdef CONFIG_IPM

/**
 * @brief creat interface
 * @param[in] information  interface information
 * @return IMAGE_PROCESSING_INTERFACE_P \n
 * @retval image process inetrface object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
IMAGE_PROCESSING_INTERFACE_P pi_image_processing_create_interface(IMAGE_PROCESSING_INFO_P information);

/**
 * @brief dataflow_start
 * @param[in] interface_handler image process inetrface object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_dataflow_start(IMAGE_PROCESSING_INTERFACE_P interface_handler);

/**
 * @brief page start
 * @param[in] interface_handler image process inetrface object
 * @param[in] image image information
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_page_start(IMAGE_PROCESSING_INTERFACE_P interface_handler,PAGE_DATA_S *page_info);

/**
 * @brief page data
 * @param[in] interface_handler image process inetrface object
 * @param[in] pband band information
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_page_data(IMAGE_PROCESSING_INTERFACE_P interface_handler,BAND_P pband);


/**
 * @brief page end
 * @param[in] interface_handler image process inetrface object
 * @param[in] image image information
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_page_end(IMAGE_PROCESSING_INTERFACE_P interface_handler,PAGE_DATA_S* page_info);

/**
 * @brief dataflow_stop
 * @param[in] interface_handler image process inetrface object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_dataflow_stop(IMAGE_PROCESSING_INTERFACE_P interface_handler);


/**
 * @brief destroy interface
 * @param[in] interface_handler image process inetrface object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_destroy_interface(IMAGE_PROCESSING_INTERFACE_P interface_handler);

/**
 * @brief get interface mailbox
 * @param[in] interface_handler image process inetrface object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
PI_MAILBOX_T pi_image_processing_get_interface_mailbox(IMAGE_PROCESSING_INTERFACE_P interface_handler);

/**
 * @brief get interface mailbox
 * @param[in] interface_handler image process inetrface object
 * @param[in] stage image process inetrface object
 * @param[in] data image process inetrface object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int pi_image_processing_get_interface_dataout(IMAGE_PROCESSING_INTERFACE_P interface_handler,uint32_t stage,void *data,STEP_COMMON_PARAM_P parm_info);

/**
 * @brief get interface context
 * @param[in] interface_handler image process inetrface object
 * @return void * \n
 * @retval point to context
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void *pi_image_processing_get_interface_context(IMAGE_PROCESSING_INTERFACE_P interface_handler);

/**
 * @brief pipeline init
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_image_processing_pipeline_init(void);

/**
 * @brief get pipe eoi
 * @param[in] pstep step core object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int pi_image_processing_get_pipe_eoi(IP_STEP_P pstep);

/**
 * @brief get next page
 * @param[in] pstep step core object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int pi_image_processing_get_next_page(IP_STEP_P pstep);

/**
 * @brief set next page
 * @param[in] pstep step core object
 * @param[in] value next page value
 * @return void \n
 * @retval void
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void pi_image_processing_set_next_page(IP_STEP_P pstep, int value);


#else
/*IMAGE_PROCESSING_INTERFACE_P pi_image_processing_create_interface(IMAGE_PROCESSING_INFO_P information){};
int32_t pi_image_processing_dataflow_start(IMAGE_PROCESSING_INTERFACE_P interface){};
int32_t pi_image_processing_page_start(IMAGE_PROCESSING_INTERFACE_P interface,IMAGE_P image){};
int32_t pi_image_processing_page_data(IMAGE_PROCESSING_INTERFACE_P interface,BAND_P pband){};
int32_t pi_image_processing_page_end(IMAGE_PROCESSING_INTERFACE_P interface,IMAGE_P image){};
int32_t pi_image_processing_dataflow_stop(IMAGE_PROCESSING_INTERFACE_P interface){};
int32_t pi_image_processing_destroy_interface(IMAGE_PROCESSING_INTERFACE_P interface){};
*/#endif

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* IMAGE_PROCESSING_INTERFACE_H */

/**
* @}
**/

