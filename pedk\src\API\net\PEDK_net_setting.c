#include <string.h>
#include <stdio.h>
#include "PEDK_net_setting.h"
#include "PEDK_event.h"

#include <quickjs.h>

JSValue js_get_smtp_email_encrypt_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int receive_data[512];
    int receive_cnt = sizeof(receive_data);
    int encrypt_mode;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_EMAIL_ENCRYPT_MODE, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_EMAIL_ENCRYPT_MODE, &respond, receive_data, &receive_cnt, 3);
    encrypt_mode = receive_data[0];
    printf("anjing encrypt_mode %d respond %d \n", encrypt_mode, respond);

    return JS_NewInt32(ctx, encrypt_mode);
}

JSValue js_get_smtp_login_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_LOGIN_NAME, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_LOGIN_NAME, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_smtp_login_pwd(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_LOGIN_PWD, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_LOGIN_PWD, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_smtp_port(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int receive_data[512];
    int receive_cnt = sizeof(receive_data);
    int port;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_PORT, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_PORT, &respond, receive_data, &receive_cnt, 3);
    port = receive_data[0];
    printf("anjing port %d respond %d \n", port, respond);

    return JS_NewInt32(ctx, port);
}

JSValue js_get_smtp_server_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_SERVER_NAME, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMTP_SERVER_NAME, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_set_smtp_email_encrypt_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int encrypt_mode = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_EMAIL_ENCRYPT_MODE, 0, sizeof(encrypt_mode), &encrypt_mode);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_EMAIL_ENCRYPT_MODE, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_smtp_login_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char* set_string = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_LOGIN_NAME, 0, strlen(set_string + 1), set_string);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_LOGIN_NAME, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_smtp_login_pwd(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char* set_string = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_LOGIN_PWD, 0, strlen(set_string + 1), set_string);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_LOGIN_PWD, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_smtp_port(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int port = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_PORT, 0, sizeof(port), &port);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_PORT, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_smtp_server_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char* set_string = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_SERVER_NAME, 0, strlen(set_string + 1), set_string);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_SMTP_SERVER_NAME, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}


JSValue js_get_wired_connect_info(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int receive_data[512];
    int receive_cnt = sizeof(receive_data);
    int status;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_CONNECT_INFO, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_CONNECT_INFO, &respond, receive_data, &receive_cnt, 3);
    status = receive_data[0];
    printf("anjing port %d respond %d \n", status, respond);

    return JS_NewInt32(ctx, status);
}

JSValue js_get_wired_mac_addr_info(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_wired_net_802_protocol_switch(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int receive_data[512];
    int receive_cnt = sizeof(receive_data);
    int enable;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_802_PROTOCOL_SWITCH, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_802_PROTOCOL_SWITCH, &respond, receive_data, &receive_cnt, 3);
    enable = receive_data[0];
    printf("anjing port %d respond %d \n", enable, respond);

    return JS_NewInt32(ctx, enable);
}

JSValue js_get_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_wired_net_ipv4_gateway(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_wired_net_ipv4_hostname(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_HOSTNAME, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_HOSTNAME, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_wired_net_ipv4_mask(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char receive_data[1024];
    int receive_cnt = sizeof(receive_data);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK, &respond, receive_data, &receive_cnt, 3);
    printf("anjing receive_data %s respond %d \n", receive_data, respond);
    return JS_NewString(ctx, "receive_data");
}

JSValue js_get_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int mode = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE, 0, sizeof(mode), &mode);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}


JSValue js_set_wired_net_802_protocol_switch(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int enable = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_802_PROTOCOL_SWITCH, 0, sizeof(enable), &enable);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_802_PROTOCOL_SWITCH, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}


JSValue js_set_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char* set_string = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR, 0, strlen(set_string + 1), set_string);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_wired_net_ipv4_hostname(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    char* set_string = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_HOSTNAME, 0, strlen(set_string + 1), set_string);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_HOSTNAME, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int mode = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE, 0, sizeof(mode), &mode);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}

JSValue js_set_wired_net_ipv6_switch(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;
    int enable = argv[0];
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV6_SWITCH, 0, sizeof(enable), &enable);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SET_WIRED_NET_IPV6_SWITCH, &respond, NULL, NULL, 3);
    printf("anjing respond %d \n", respond);
    if(respond > 0)
    {
        return JS_NewInt32(ctx, 1);
    }
    else
    {
        return JS_NewInt32(ctx, 0);
    }
}



