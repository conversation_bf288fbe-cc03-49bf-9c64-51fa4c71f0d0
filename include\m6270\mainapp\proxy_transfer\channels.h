/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file channels.h
 * @addtogroup proxy
 * @{
 * @brief proxy queue interface 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef _CHANNELS_H
#define _CHANNELS_H


struct option
{
    void*(*alloc_callback)(void*);
    void (*free_callback)(void*);
};

struct channels
{
    struct option opt;
    struct fragment *head;
    int number;
};

/**
 * @brief queue initialization
 *
 * @param chs[in] queue handle
 * @param alloc_callback[in] the node data memory request is completed by the callback function
 * @param free_callback[in] the node data memory destruction is completed by the callback function
 *
 * @return 0 on success , -1 on error 
 */
int channels_init(struct channels *chs , 
        void*(*alloc_callback)(void*) ,
        void (*free_callback)(void*)
        );

/**
 * @brief queue destruct
 *
 * @param chs[in] queue handle
 *
 * @return 0 on success , -1 on error 
 */
int channels_destroy(struct channels *chs);

/**
 * @brief Data is inserted into the queue header 
 *
 * @param chs[in] queue handle
 * @param feature[in] feature values for associated data
 * @param data[in] stored data 
 * @param dup_insert[in] wheter to allow insertion of data
 *                         with the same feature value
 *
 * @return 0 on success , -1 on error 
 */
int channels_head_insert(struct channels *chs , 
        int feature , 
        void *data , 
        int dup_insert);

/**
 * @brief insert data into the tail of the queue 
 *
 * @param chs[in] queue handle
 * @param feature[in] feature values for associated data
 * @param data[in] stored data 
 * @param dup_insert[in] wheter to allow insertion of data
 *                         with the same feature value
 *
 * @return 0 on success , -1 on error 
 */
int channels_tail_insert(struct channels *chs , 
        int feature , 
        void *data , 
        int dup_insert);

/**
 * @brief insert data behind the specified feature data
 *
 * @param chs[in] queue handle
 * @param dest_feature[in] target feature value
 * @param feature[in] new feature values for associated data 
 * @param data[in] stored data 
 *
 * @return 0 on success , -1 on error 
 */
int channels_fragment_insert(struct channels *chs , 
        int dest_feature,
        int feature ,
        void *data);

/**
 * @brief finds data for the specified feature value
 *
 * @param chs[in] queue handle
 * @param feature[in] feature values for associated data
 *
 * @return the stored element address
 * @retval NULL on error or the queue is empty
 * @retval valid address on success
 */
void* channels_find(struct channels *chs , int feature);

/**
 * @brief search by specified match 
 *
 * @param chs[in] queue handle
 * @param _callback[in] match method callback , the input parameter is \n 
 *            the node element in the queue , if the match is successful , \n
 *            please return 1 , otherwise return 0
 * @param options[in] this parameter is used as the second paramter of _callback
 *
 * @return the stored element address
 * @retval NULL on error or the queue is empty
 * @retval valid address on success
 */
void* channels_find_match(struct channels *chs , int (*_callback)(void* , void*) , void *options);

/**
 * @brief find data for all specified feature values in the queue
 *
 * @param chs[in] queue handle
 * @param data[out]
 * @param feature[in] feature values for associated data
 * @param dup_find[in] wheter to allow find data
 *                         with the same feature value
 *
 * @return the number of specified feature values found 
 * @retval -1 on error
 */
int channels_find_shallowcopy(struct channels *chs , 
        void ***data , 
        int feature , 
        int dup_find);

/**
 * @brief pop the head element from queue
 *
 * @param chs[in] queue handle
 *
 * @return the stored element address
 * @retval NULL on error or the queue is empty
 * @retval valid address on success
 */
void* channels_head_pop(struct channels *chs);

/**
 * @brief pop the tail element from queue
 *
 * @param chs[in] queue handle
 *
  * @return the stored element address
 * @retval NULL on error or the queue is empty
 * @retval valid address on success
 */
void* channels_tail_pop(struct channels *chs);

/**
 * @brief delete data for the specified feature value in the queue 
 *
 * @param chs[in] queue handle
 * @param feature[in] feature values for associated data
 * @param dup_remove[in] wheter to allow remove data
 *                         with the same feature value
 *
 * @return 0 on success , -1 on error 
 */
int channels_fragment_remove(struct channels *chs , 
        int feature , 
        int dup_remove);

/**
 * @brief delete data from the queue for the specified element address
 *
 * @param chs[in] queue handle
 * @param data[in] the element address
 * @param is_free_data[in] 1 means free data pointer , 0 means reserve data pointer
 *
 * @return 0 on success , -1 on error 
 */
int channels_fragment_remove2(struct channels *chs , 
        void *data ,
        int is_free_data);

/**
 * @brief gets the contents of the first element of the queue 
 *
 * @param chs[in] queue handle
 *
 * @note the interface does not delete the first element 
 *
 * @return the stored element address
 * @retval NULL on error or the queue is empty
 * @retval valid address on success
 */
void* channels_head(struct channels *chs);

/**
 * @brief sort the elements inside the queue 
 *
 * @param chs[in] queue handle
 * @param compare_callback[in] this callback function is used to compare\n 
 *                              the size of two elements,and return 1 or 0.\n
 *                              When the callback function is empty,it \n
 *                              will be sorted by feature
 *
 * @return 0 on success , -1 on error 
 */
int channels_sort(struct channels *chs , int (*compare_callback)(void* , void*));

/**
 * @brief get the data stored in the fragment, and the next fragment pointer is\n 
 *         used to traverse the inner elements 
 *
 * @param cur[in] the fragment address
 * @param next[out] the pointer address used to store the next fragment 
 *
 * @return the stored element address
 * @retval NULL on error or the queue is empty
 * @retval valid address on success
 */
void* channels_iterator(struct fragment *cur , struct fragment **next);

/**
 * @brief reverse the elements in the queue
 *
 * @param chs[in] queue handle
 *
 * @return 0 on success , -1 on error 
 */
int channels_reversion(struct channels *chs);

/**
 * @brief insert the data in the specified order into the appropriate position.
 *
 * @param chs[in] queue handle
 * @param feature[in] feature values for associated data
 * @param data[in] stored data 
 * @param direction[in] >= 1 indicates inserting in descending order;\n
 *                  < 1 indicates insertion in ascending order 
 *
 * @note  the queue must be empty or sorted
 *
 * @return 0 on success , -1 on error 
 */
int channels_order_insert(struct channels *chs , int feature , void *data , int direction);


#endif //_CHANNELS_H

/**                                                                                                                                                                  
 * @}
 */
