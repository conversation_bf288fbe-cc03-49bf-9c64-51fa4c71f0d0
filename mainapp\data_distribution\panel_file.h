/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_file.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2025-03-20
 * @brief DC accesses the UI file interface
 */

#ifndef PANEL_FILE_H
#define PANEL_FILE_H

#include <stdint.h>
#include <unistd.h>
#include <sys/stat.h>

/**
 * @brief access panel board file
 */
int32_t panel_file_access( const char* pathname, int32_t mode, int32_t* err_num );

/**
 * @brief open panel board file
 */
int32_t panel_file_open( const char* pathname, int32_t flags, mode_t mode, int32_t* err_num );

/**
 * @brief read panel board file
 */
ssize_t panel_file_read( int32_t fd, void *buf, size_t count, int32_t* err_num );

/**
 * @brief write panel board file
 */
ssize_t panel_file_write( int32_t fd, const void* buf, size_t count, int32_t* err_num );

/**
 * @brief seek panel board file
 */
off_t panel_file_seek( int32_t fd, off_t offset, int32_t whence, int32_t* err_num );

/**
 * @brief close panel board file
 */
int32_t panel_file_close( int32_t fd, int32_t* err_num );

/**
 * @brief panel board file recv result proc
 */
void panel_file_recv_result_proc( void* data, int32_t data_len );

#if 1
/**
 * @brief debug cmd register
 */
void panel_file_debug_cmd_register( void );
#endif

#endif /* PANEL_FILE_H */

/**
 *@}
 */

