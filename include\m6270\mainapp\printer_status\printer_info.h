 /**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file printer_info.h
 * @addtogroup mainapp
 * @{
 * @addtogroup printer_status
 * <AUTHOR>
 * @version v1.0
 * @date 2023-07-03
 * @brief printer status monitor, when the driver issues a USB command, \n
*       dynamic and static status are sent to driver.
 */

#ifndef _PRINTER_INFO_H_
#define _PRINTER_INFO_H_

/*
*@brief printer status
*/
typedef enum
{
    PRINTER_ST_INIT             = 0,        ///< init
    PRINTER_ST_SLEEP            = 1,        ///< sleep
    PRINTER_ST_WARMING          = 2,        ///< warming
    PRINTER_ST_READY            = 3,        ///< ready
    PRINTER_ST_PRINTING         = 4,        ///< printing
    PRINTER_ST_ERROR            = 5,        ///< error
    PRINTER_ST_CANCELING        = 6,        ///< canceling
    PRINTER_ST_PROCESSING       = 7,        ///< processing
    PRINTER_ST_ACR_CALIBRATION  = 8,        ///< ACR calibration

    PRINTER_ST_MAX
}PRINTER_STATUS;

/*
*@brief printer sleep time
*/
typedef enum
{
    ST_SLEEP_1                  = 1,        ///< 1 minute
    ST_SLEEP_5                  = 2,        ///< 5 minutes
    ST_SLEEP_15                 = 3,        ///< 15 minutes
    ST_SLEEP_30                 = 4,        ///< 30 minutes
    ST_SLEEP_60                 = 5,        ///< 60 minutes
    ST_SLEEP_480                = 6,        ///< 480 mintues
    ST_SLEEP_MAX
}ST_SLEEP_TIME;

/*
*@brief print job status
*/
typedef enum
{
    ST_JOB_NORMAL               = 0,        ///< job not processed
    ST_JOB_PROCESSING           = 1,        ///< job processing
    ST_JOB_FINISH               = 2,        ///< job finished
    ST_JOB_ERROR                = 3,        ///< job error
}ST_JOB_STATUS;

/*
*@brief print job type
*/
typedef enum
{
    ST_PRINT                    = 0,        ///< job type is print
    ST_COPY                     = 1,        ///< job type is copy
    ST_SCAN                     = 2,        ///< job type is scan
    ST_FAX                      = 3,        ///< job type is fax
    ST_TYPE_MAX
}ST_JOB_TYPE;

/*
*@brief print job error delay processing mode
*/
typedef enum
{
    ST_NANDING_PROMPT           = 0,        ///< prompt handing
    ST_DELETE_IMMED             = 1,        ///< immediately
    ST_DELETE_DELAY             = 2,        ///< delay
    ST_RECOVERY_CONTINUE        = 3,        ///< continue
}ST_HANDING_ERROR;

/*
*@brief duplex print flags
*/
typedef enum
{
    ST_NOJOB                    = 0,        ///< no jobs
    ST_SINGLE                   = 1,        ///< single side
    ST_AUTO_DOUBLE              = 2,        ///< automatic double-sided
    ST_MANUAL_DOUBLE            = 3,        ///< manual double-sided
}ST_DUPLEX_FIAG;

/*
*@brief printer install info
*/
typedef enum
{
    ST_NO_FINISHER              = 0x0000,   ///< finisher not installed
    ST_F533                     = 0x0001,   ///< install 533 finisher
    ST_F534                     = 0x0002,   ///< install 534 finisher
    ST_F536                     = 0x0003,   ///< install 536 finisher
    ST_F540                     = 0x0004,   ///< install 540 finisher
    ST_F540SD                   = 0x0005,   ///< install 540 SD finisher
}ST_FINISHER_INFO;

/*
*@brief printer tray info
*/
typedef enum
{
    ST_TRAY_NORMAL              = 0x0000,   ///< normal
    ST_TRAY_AUTO                = 0x0001,   ///< auto-tray built info
    ST_TRAY_MANUAL              = 0x0002,   ///< manual tray built info
    ST_TRAY_IN                  = 0x0004,   ///< built-in high-capacity tray
    ST_TRAY_OUT                 = 0x0008,   ///< built-out high-capacity tray
    ST_TRAY1                    = 0x0010,   ///< tray1 built info
    ST_TRAY2                    = 0x0020,   ///< tray2 built info
    ST_TRAY3                    = 0x0040,   ///< tray3 built info
    ST_TRAY4                    = 0x0080,   ///< tray4 built info
}ST_TRAY_INFO;

/**
 * @brief start the function,when the printer status changes , the \n
 *       corresponding status is modifed and saved ,and the status is \n
 *       reported when the driver issues a command.
 * @param[in] void
 * @autor liangshiqin
 * @date 2023-07-20
 */
void printerst_prolog(void);

#endif /* _PRINTER_INFO_H_ */
/**
 * @}
 */


