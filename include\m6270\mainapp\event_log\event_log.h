#ifndef __EVENT_LOG_H__
#define __EVENT_LOG_H__



#include <stdlib.h>
#include "statusid.h"
#include "print.h"

// **************************************
// 日志系统说明
// 1 EVENT_LOG 简写 EL或el
// 2 Manger    简写 mgr
// **************************************

//#define __EL_OUTPUT_DEBUG__

// 注： EL_DTAT_FILE_MAX  EL_DTAT_FILE_INDEX_MAX 决定日志系统最大记录条数
#define EL_DTAT_FILE_MAX       10   // 数据文件的保留最大个数
#define EL_DTAT_FILE_INDEX_MAX 100  // 单个数据文件存储日志的最大条数


// **************************************
// 日志系统return 类
// **************************************
#define EL_ERROR 0 // 错误
#define EL_OK    1 // 成功
#define EL_END   2 // 日志读取正常结束
#define EL_NULL  3 // 日志记录为空 即0条记录

#define FIRMWARE_BUF_SIZE 32

#ifndef uint32_t
typedef unsigned int    uint32_t;
#endif

enum event_log_name {
    EL_NAME_IDEL_READY,
    EL_NAME_POWER_ON,
    EL_NAME_POWER_DOWN,
    EL_NAME_WARNING,
    EL_NAME_ERROR,

//COPY
    EL_NAME_COPY_IDLE,
    EL_NAME_OUT_OF_MEMORY,
    EL_NAME_MAX,
    EL_NAME_SIZE = 0xFFFFFFFF
};

enum event_log_category {
    EL_CATEGORY_STATUS,           // 系统状态类 （注：各种错误码）
    EL_CATEGORY_JOB,              // 作业类 （注：打印页数、ADF扫描页数、FB扫描页数等）
    EL_CATEGORY_CONSUMABLE,       // 耗材类 （注：粉筒更换、成像组件更换）
    EL_CATEGORY_FIRMWARE,         // 固件类 （注：固件升级、数据版本固件、引擎版本固件、扫描版本、面板版本）
    EL_CATEGORY_MAX,              // 类型最大值 用于边界判断

    EL_CATEGORY_SIZE = 0xFFFFFFFF // 最大值4字节 1-确保存储数据的数据结构做到中四字节对齐
                                  //             2-支持扩展不影响存储数据的数据结构总字节数
};

// **************************************
// 事件类型
// **************************************

//作业事件类型
//系统状态参考statusid.h
//耗材参考print.h中PRINT_CONSUMPTION_E
//固件无事件类型
typedef enum event_log_type_job {
    EL_TYPE_JOB_UNKNOWN = 0,
    EL_TYPE_JOB_USB,
    EL_TYPE_JOB_ETHERNET,
    EL_TYPE_JOB_IPP,
    EL_TYPE_JOB_GCP,
    EL_TYPE_JOB_WIFI,
    EL_TYPE_JOB_FILE,
    EL_TYPE_JOB_INTERNAL,
    EL_TYPE_JOB_FAX,
    EL_TYPE_JOB_AIRSCAN,
    EL_TYPE_JOB_UDISK,
    EL_TYPE_JOB_COPY,
    EL_TYPE_JOB_IMAGE,
    EL_TYPE_JOB_OTHER,
    EL_TYPE_JOB_DEBUG,

    EL_TYPE_JOB_SIZE = 0xFFFFFFFF
}EVENT_LOG_TYPE_JOB_E;

// **************************************
// 事件名称
// **************************************


//固件事件名称
//耗材参考print.h中PRINT_CONSUMPTION_E
//系统状态参考statusid.h
enum event_log_name_firmware {
    EL_NAME_FIRMWARE_UPGRADE,

    EL_NAME_FIRMWARE_SIZE = 0xFFFFFFFF
};

//作业事件名称
enum event_log_name_job {
    EL_NAME_JOB_PRINT=1,
    EL_NAME_JOB_COPY,
    EL_NAME_JOB_SCAN,

    EL_NAME_JOB_SIZE = 0xFFFFFF
};

typedef enum event_log_page_count_value {
    EL_PAGE_ADF_COUNT,
    EL_PAGE_FB_COUNT,
    EL_PAGE_TOTAL_PRINT_COUNT,
    EL_PAGE_TOTAL_COPY_COUNT,

    EL_PAGE_COUNT_SIZE = 0xFFFFFF
}EVENT_LOG_PAGE_COUNT_VALUE;

typedef struct event_log_page_count
{
    unsigned int adf_count;           // 事件发生时，ADF已扫描的总页数 注：双面扫描按2页计算
    unsigned int fb_count;            // 事件发生时，FB已扫描的总页数
    unsigned int print_count;         // 事件发生时，已打印的总页数 注：双面打印按2页计算
    unsigned int copy_count;          // 事件发生时，已复印的总页数 注：双面复印按2页计算
    unsigned int total_count;         // 事件发生时，复印+打印总页数

}EVENT_LOG_PAGE_COUNT_S;

typedef struct common_param{
    uint32_t module_id;

    unsigned int total_print_count; // 事件发生时，已打印的总页数 注：双面打印按2页计算
    unsigned int adf_count;         // 事件发生时，ADF已扫描的总页数 注：双面扫描按2页计算
    unsigned int fb_count;          // 事件发生时，FB已扫描的总页数

    char time_stamp[32];            // 事件发生时的系统时间 注：
                                    //  1-格式 “2019-10-29 09:10:55-0400”
                                    //  2-"0400"是时区
                                    //  3-格式包含结尾符总计25个字符，考虑4字节对齐 配置32字节
}EVENT_LOG_COMMON_PARAM_S;

// 数据结构信息：固件
struct event_log_category_firmware {
    enum event_log_category category;       // 事件类别 用于解析时确认本条信息的长度

    enum event_log_name_firmware type;      // 事件类型

    EVENT_LOG_COMMON_PARAM_S event_log_common_param;

    char version_6270[FIRMWARE_BUF_SIZE];                  //6270版本号
    char version_6220_a[FIRMWARE_BUF_SIZE];                //6220a版本号
    char version_6220_b[FIRMWARE_BUF_SIZE];                //6220b版本号
    char version_fpga1[FIRMWARE_BUF_SIZE];                 //fpga1版本号
    char version_fpga2[FIRMWARE_BUF_SIZE];                 //fpga2版本号
    char version_printer_engine[FIRMWARE_BUF_SIZE];        //打印引擎版本号
    char version_panel[FIRMWARE_BUF_SIZE];                 //面板版本号

};


// 数据结构信息：作业
struct event_log_category_job {
    enum event_log_category category;                   // 事件类别 用于解析时确认本条信息的长度
    enum event_log_name_job job_module;                 // 事件名称，包括打印、复印、扫描等
    enum event_log_type_job type;                       // 事件类型，包括usb、net等

    EVENT_LOG_COMMON_PARAM_S event_log_common_param;    //通用参数

    unsigned int current_page_count;                    // 当前页数，打印或者扫描
};



// 数据结构信息：耗材
struct event_log_category_consumable {
    enum event_log_category category;                   // 事件类别 用于解析时确认本条信息的长度

    EVENT_LOG_COMMON_PARAM_S event_log_common_param;    //通用参数

    uint32_t consumable_type;                           // 耗材类型
};

// 数据结构信息：系统状态
struct event_log_category_system_status {
    enum event_log_category category;                   // 事件类别 用于解析时确认本条信息的长度

    EVENT_LOG_COMMON_PARAM_S event_log_common_param;    //通用参数

    int32_t status_id;                                  // 状态id 错误码
};



int event_log_prolog(void);

int event_log_read_reset_first_index(void);
int event_log_category_read(enum event_log_category* category, enum event_log_name* name, void* data, int* data_len);
char* event_log_status_id_to_string(STATUS_ID_E status_id);
const char* event_log_job_type_to_string(enum event_log_name_job job_type);
const char* event_log_consumable_type_to_string(PRINT_CONSUMPTION_E type);
const char* event_log_firmware_to_string(void);

int event_log_export_log_summary(const char* summary_file_path);

#endif

