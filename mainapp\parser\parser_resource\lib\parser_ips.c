/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file parser_ips.c
 * @addtogroup event_manager
 * @{
 * <AUTHOR> <PERSON>
 * @date 2025-01-07
 * @brief parser ips API
 */

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_threads.h"
#include "pol/pol_string.h"
#include "cbinder/cbinder.h"

#include "parser_ips.h"

static struct binder_state* bs = NULL;
static void* srv = NULL;

/**
 * @brief get data
 * @param[in] bio
 * @param[in] data_len
 * @param[out] buffer
 * @return bool
 * @retval false
 * @retval true
 * <AUTHOR>
 * @date 2025-01-16
 * @note  N/A
 */
/* get specific data from binder_io struct ,such as uint8\int8\uint16\int16*/
static bool bio_get_speci_data( struct binder_io *bio, void *buffer, size_t data_len)
{
    if (bio == NULL || buffer == NULL || data_len > bio->data_avail )
    {
        printf("bio_get_speci_data failed\n");
        return false;
    }
    pi_memcpy(buffer, bio->data, data_len);

    //更新 binder_io struct
    bio->data += data_len;
    bio->data_avail -= data_len;

    return true;
}

/**
 * @brief put data
 * @param[in] bio
 * @param[in] buffer
 * @param[int] data_len
 * @param[int] filename
 * @return bool
 * @retval false
 * @retval true
 * <AUTHOR>
 * @date 2025-01-16
 * @note  N/A
 */
/* put specific data to binder_io struct ,such as uint8\int8\uint16\int16\int32*/
static bool bio_put_speci_data(struct binder_io *bio, void *buffer, size_t data_len,const char* filename)
{
    if (bio == NULL || buffer == NULL || data_len > bio->data_avail )
    {
        printf("[%s] bio_put_speci_data failed\n",filename);
        return false;
    }
    pi_memcpy(bio->data, buffer, data_len);

    //更新 binder_io struct
    bio->data += data_len;
    bio->data_avail -= data_len;

    return true;
}

/**
 * @brief get system status for mono
 * @param[in] ips_render_mode 0: color 1:mono
 * @param[out] N/A
 * @return unsigned long
 * @retval 0:base on PC driver
 * @retval 1:mono
 * <AUTHOR>
 * @date 2023-12-05
 * @note  N/A
 */
unsigned long print_ips_get_system_status_for_mono(int32_t ips_render_mode)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    unsigned long       result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0)

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_render_mode);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_SYSTEM_STATUS_FOR_MONO);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_SYSTEM_STATUS_FOR_MONO) failed(%d)\n", ret);
    }
    else
    {
        result = (unsigned long)bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief app get machine speed
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_param_get_machine_speed( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_MACHINE_SPEED);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_MACHINE_SPEED) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief app get machine color
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_param_get_machine_color( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_MACHINE_COLOR);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_MACHINE_COLOR) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief the finisher is set ?
 * @param[in]  void
 * @param[out] pcValue 1-finisher install 0-finisher no install
 * @return uint32_t
 * @retval 1 - finisher install
 * @retval 0 - finisher no install
 * <AUTHOR>
 * @date 2024-04-15
 * @note  N/A
 */
uint32_t print_ips_config_get_finisher_install(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[32];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_FINISHER_INSRALL);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_FINISHER_INSRALL) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief print_ips_interation_udisk_paper_size_valid_check
 * @param[in] ips_paper_size
 * @param[out] N/A
 * @return int8_t
 * @retval 0: support
 * @retval -1: no support
 * <AUTHOR>
 * @date 2024-01-08
 * @note  N/A
 */
int32_t print_ips_udisk_paper_size_valid_check(const uint32_t ips_paper_size)
{
    struct binder_io    msg, reply;
    size_t              iodata[32];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_paper_size);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_UDISK_PAPERSIZE_VALID_CHECK);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_UDISK_PAPERSIZE_VALID_CHECK) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_UDISK_PAPERSIZE_VALID_CHECK) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief app get tray install status
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0 - the tray uninstall
 * @retval 1 - the tray install
 * <AUTHOR>
 * @date 2024-04-16
 * @note  N/A
 */
int32_t print_ips_param_get_tray_install(const IPS_INPUT_TRAY_E ips_input_tray)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_input_tray);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_TRAY_INSTALL);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_TRAY_INSTALL) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief app get tray_in empty status
 * @param[in] void
 * @param[out] N/A
 * @return uint8_t
 * @retval  0 - empty
 * @retval  1 - not empty
 * <AUTHOR>
 * @date 2024-04-16
 * @note  N/A
 */
uint32_t print_ips_param_get_tray_in_empty_status(const IPS_INPUT_TRAY_E ips_input_tray)
{
    struct binder_io    msg, reply;
    size_t              iodata[32];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_input_tray);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_TRAY_INEMPTY_STATUS);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_TRAY_INEMPTY_STATUS) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get papse size by tray_in
 * @param[in]  ips_input_tray
 * @param[out] media_size_ptr
 * @param[out] custom_type
 * @param[out] width
 * @param[out] height
 * @return uint8_t
 * @retval 0 success
 * @retval 1 failure
 * <AUTHOR>
 * @date 2024-04-17
 * @note  N/A
 */
uint32_t print_ips_param_ips_get_paper_size(const IPS_INPUT_TRAY_E ips_input_tray,
                            IPS_MEDIA_SIZE_E *media_size_ptr,IPS_PRINT_CUSTOM_TYPE_E* custom_type, uint32_t *width,uint32_t *height)
{
    struct binder_io    msg, reply;
    size_t              iodata[32];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_input_tray);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_PAPER_SIZE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_PAPER_SIZE) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        /*出参处理*/
        *media_size_ptr = bio_get_uint32(&reply);
        *custom_type    = bio_get_uint32(&reply);
        *width          = bio_get_uint32(&reply);
        *height         = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get paper type
 * @param[in]  ips_input_tray ips_tray_input
 * @param[in]  ips_input_tray ips_paper_type
 * @param[out]   N/A
 * @return int32_t
 * @retval N/A
 * <AUTHOR>
 * @date 2024-05-16
 * @note N/A
 */
int32_t print_ips_param_pass_tray_get_paper_type(uint32_t ips_tray_input, uint32_t *ips_paper_type)
{

    struct binder_io    msg, reply;
    size_t              iodata[32];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_tray_input);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_PAPER_TYPE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_PAPER_TYPE) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_GET_PAPER_TYPE) failed(%d)\n", result);
        }
        *ips_paper_type = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief it is the end of job
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_end_of_job(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_END_OF_JOB);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_END_OF_JOB) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_END_OF_JOB) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief if it is parsing
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_ips_in_use(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_IPS_IN_USE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_IPS_IN_USE) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_IPS_IN_USE) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief ips stop read data,call it
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_stop_read_data(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_STOP_READ_DATA);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_STOP_READ_DATA) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result))  )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_STOP_READ_DATA) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief function:cross shift,stop read data
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_stop_file_read_data(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_STOP_FILE_READ_DATA);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_STOP_FILE_READ_DATA) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_STOP_FILE_READ_DATA) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief function:cross shift,get prn readable flag
 * @param[in] void
 * @param[out] N/A
 * @return read enable flag
 * @retval 0 disable
 * @retval 1 enable
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_get_prn_read_enable_flag(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_PRN_READ_ENABLE_FLAG);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_PRN_READ_ENABLE_FLAG) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_PRN_READ_ENABLE_FLAG) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get job job id
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval 0: without job id
 * @retval >0: job id
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
uint32_t print_ips_param_get_job_id_value(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_JOB_ID_VALUE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_JOB_ID_VALUE) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief ips eoj,if ips is timeout,it will call it
 * @param[in] job_number
 * @param[out] N/A
 * @return  int
 * @retval  0 - success
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_post_job_end_msg(int32_t job_number)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_POST_JOB_END_MSG);
    bio_put_speci_data(&msg, &job_number, sizeof(job_number),__func__);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_POST_JOB_END_MSG) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_POST_JOB_END_MSG) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

#if 0  // GIO共享内存接口读取prn数据
int32_t print_ips_io_read_data(char *buffer, int32_t bufsize)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_READ_DATA);
    bio_put_speci_data(&msg, &bufsize, sizeof(bufsize));
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_READ_DATA) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_READ_DATA) failed(%d)\n", result);
        }
        buffer = bio_get_buffer(&reply, (uint32_t *)&bufsize);

        binder_done(bs, &msg, &reply);
    }

    return result;
}
#endif

/**
 * @brief ips eoj,if ips is timeout,it will call it
 * @param[in] name
 * @param[in] bytes
 * @param[in] job_id
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_post_job_pipelint_name(char *name, int32_t bytes, uint32_t job_id)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;
    uint32_t            len = 0;
    char *              tmpp = NULL;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_string8(&msg, name);
    bio_put_speci_data(&msg, &bytes, sizeof(int32_t),__func__);
    bio_put_speci_data(&msg, &job_id, sizeof(job_id),__func__);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_POST_JOB_PIPELINT_NAME);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_POST_JOB_PIPELINT_NAME) failed(%d)\n", ret);
    }
    else
    {
       binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief  get tray_in and edge if job start or paper size changes
 * @param[in] ips_media_size
 * @param[in] ips_media_type
 * @param[in] ips_input_tray_original
 * @param[in] print_mode
 * @param[in] ips_bookbinding
 * @param[out] edge
 * @param[out] ips_input_tray_in
 * @return char
 * @retval 0: success
 * @retval -1: failure
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_get_tray_in_and_edge(const IPS_MEDIA_SIZE_E ips_media_size, const IPS_MEDIA_TYPE_E ips_media_type,
                                            const IPS_INPUT_TRAY_E ips_input_tray_original, IPS_EDGE_E *edge, IPS_INPUT_TRAY_E *ips_input_tray_in,
                                            const IPS_PRINT_MODE_E print_mode, const IPS_BOOKBINDING_S ips_bookbinding)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = -1;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_media_size);
    bio_put_uint32(&msg, ips_media_type);
    bio_put_uint32(&msg, ips_input_tray_original);
    bio_put_uint32(&msg, print_mode);
    bio_put_uint32(&msg, ips_bookbinding.ips_fold_mode);
    bio_put_uint32(&msg, ips_bookbinding.ips_punching);
    bio_put_uint32(&msg, ips_bookbinding.ips_lstaple);
    bio_put_uint32(&msg, ips_bookbinding.ips_orientation);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_TRAY_IN_AND_EDGE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_TRAY_IN_AND_EDGE) failed(%d)\n", ret);
    }
    else
    {
      #if 0
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_GET_TRAY_IN_AND_EDGE) failed(%d)\n", result);
        }
      #endif
        result = bio_get_uint32(&reply);
       *edge = bio_get_uint32(&reply);
       *ips_input_tray_in = bio_get_uint32(&reply);
       binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief it is not used,just the api that ips use
 * @param[in] ips_media_size
 * @param[out] N/A
 * @return unsigned long
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
unsigned long print_ips_get_system_support_media_size(int32_t ips_media_size)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    unsigned long       result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, ips_media_size);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_SYSTEM_SUPPORT_MEDIA_SIZE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_SYSTEM_SUPPORT_MEDIA_SIZE) failed(%d)\n", ret);
    }
    else
    {
        result = (unsigned long)bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief the parse timeout alue
 * @param[in] N/A
 * @param[out] timeout
 * @return  0 -success
 * @retval OM_ERROR_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
OM_ERROR_E print_ips_inf_get_timeout_value_int(int32_t *timeout)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    OM_ERROR_E          result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_TIMEOUT_VALUE_INT);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_TIMEOUT_VALUE_INT) failed(%d)\n", ret);
    }
    else
    {
        result = (OM_ERROR_E)bio_get_uint32(&reply);
        *timeout = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

#if 0  // 共享内存放入band数据
PAGE_OUTPUT_QUEUE_STATUS_E print_ips_add_band_to_page_output_queue_page(uint32_t plane, char *page_data, uint32_t num_bytes)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    PAGE_OUTPUT_QUEUE_STATUS_E result = 0;
    int32_t             ret = 0;
    uint32_t            len = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, plane);
    bio_put_uint32(&msg, num_bytes);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_ADD_BAND_TOPAGE_OUTPUT_QUEUE_PAGE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_ADD_BAND_TOPAGE_OUTPUT_QUEUE_PAGE) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        page_data = bio_get_buffer(&reply, &len);

        binder_done(bs, &msg, &reply);
    }

    return result;
}
#endif

/**
 * @brief IPS band create over ，call it \n
        it needs to configure page param，confirm next page is normal \n
        and then send message to the handle module to tell it that render completely
 * @param[in]  old_set - POQ set point
 * @param[out]  N/A
 * @return PAGE_OUTPUT_QUEUE_STATUS_E
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
PAGE_OUTPUT_QUEUE_STATUS_E print_ips_finished_adding_to_page_output_queue_page(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    PAGE_OUTPUT_QUEUE_STATUS_E  result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, POQS_UNKNOWN_ERROR);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_FINISHED_ADDING_TOPAGE_OUTPUT_QUEUE_PAGE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_FINISHED_ADDING_TOPAGE_OUTPUT_QUEUE_PAGE) failed(%d)\n", ret);
    }
    else
    {
        result = (PAGE_OUTPUT_QUEUE_STATUS_E)bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief   page output queue - abbreviation POQ， \n
            POQ of one set \n
            it create POQ SET, ips call it \n
            we need to save this param
 * @param[in]  copies           - POQ set copies
 * @param[in]  collate          -
 * @param[in]  producing_app    - data produce ,it must be ips
 * @param[in]  incoming_format  - band format
 * @param[in]  storage_format   - data save format
 * @param[in]  freeFunc         - release function,it can be NULL
 * @param[in]  callback         - callback function，it will call in the time of POQ set submit，or excepions happens
 * @param[out]  N/A
 * @return uint32_t
 * @retval 1:sucess
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_create_page_output_queue_set(uint32_t copies, int32_t collate, PAGE_OUTPUT_QUEUE_APP_E  producing_app,
                                                  PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E incoming_format, PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E storage_format)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, copies);
    bio_put_speci_data(&msg, &collate, sizeof(collate),__func__);
    bio_put_uint32(&msg, producing_app);
    bio_put_uint32(&msg, incoming_format);
    bio_put_uint32(&msg, storage_format);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_CREATE_PAGE_OUTPUT_QUEUE_SET);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_CREATE_PAGE_OUTPUT_QUEUE_SET) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief  output page data of POQ SET , IPS call \n
          ips will out one page of poq set
 * @param[in]  desc
 * @param[in]  page_number
 * @param[out]  N/A
 * @return uint32_t
 * @retval 1:success
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
uint32_t print_ips_add_page_to_page_output_queue_set(PAGE_OUTPUT_QUEUE_PAGE_DESC_S* desc, uint32_t page_number)
{
    struct binder_io    msg, reply;
    size_t*             iodata = NULL;
    int32_t             data_len = sizeof(PAGE_OUTPUT_QUEUE_PAGE_DESC_S) + 32;
    uint32_t            result = 0;
    int32_t             ret = 0;
    uint32_t            sz = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    do
    {
        iodata =  (size_t *)pi_malloc(data_len);
        if(NULL == iodata)
        {
            pi_msleep(1);
        }
    }while(NULL == iodata);
    bio_init(&msg, iodata, data_len, 4);
    bio_put_buffer(&msg, (char *)desc, sizeof(PAGE_OUTPUT_QUEUE_PAGE_DESC_S));
    bio_put_uint32(&msg, page_number);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_ADD_PAGE_TO_PAGE_OUTPUT_QUEUE_SET);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_ADD_PAGE_TO_PAGE_OUTPUT_QUEUE_SET) failed(%d)\n", ret);
    }
    else
    {

        result = bio_get_uint32(&reply);

        *desc = *((PAGE_OUTPUT_QUEUE_PAGE_DESC_S*)bio_get_buffer(&reply,&sz));
    #if 0
        if( !bio_get_speci_data(&reply, desc, sizeof(PAGE_OUTPUT_QUEUE_PAGE_DESC_S)) )
        {
             printf("bio_get_speci_data(PAGE_OUTPUT_QUEUE_PAGE_DESC_S) failed\n");
        }
    #endif

        binder_done(bs, &msg, &reply);
    }
    pi_free(iodata);

    return result;
}

/**
 * @brief  completes adding POQ SET ，IPS call this
 * @param[in] void
 * @param[out]  N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
void print_ips_finished_adding_pages_to_page_output_queue_set(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_FINISHED_ADDING_PAGES_TO_PAGE_OUTPUT_QUEUE_SET);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_FINISHED_ADDING_PAGES_TO_PAGE_OUTPUT_QUEUE_SET) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }
    return;
}

/**
 * @brief get ips lib param
 * @param[in] void
 * @param[out] N/A
 * @return IPS_LIB_PARAM_S
 * @retval IPS_LIB_PARAM_S
 * <AUTHOR>
 * @date 2023-12-05
 * @note  N/A
 */
const IPS_LIB_PARAM_S print_ips_param_get_ips_lib_param( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[256];
    IPS_LIB_PARAM_S     result = {0};
    int32_t             ret = 0;
    uint32_t            len = 0;
    char *              tmp = NULL;

    if ( bs == NULL || srv == NULL )
    {
        printf("binder can't init (PARSER_IPS_CMD_GET_IPS_LIB_PARAM) failed\n");
        return result;
    }
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_IPS_LIB_PARAM);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_IPS_LIB_PARAM) failed(%d)\n", ret);
    }
    else
    {
        tmp = bio_get_buffer(&reply, &len);
        if ( len == sizeof(IPS_LIB_PARAM_S) )
        {
            pi_memcpy(&result, tmp, sizeof(IPS_LIB_PARAM_S));
        }
        binder_done(bs, &msg, &reply);
    }

    return result;

}

/**
 * @brief get job type
 * @param[in] void
 * @param[out] N/A
 * @return unsigned long
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
unsigned long print_ips_get_job_setting_type(void) //IPS_GetJobSettingType
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    unsigned long       result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_JOB_SETTING_TYPE);
    if ( ret != 0 )
    {
       printf("binder_call(PARSER_IPS_CMD_GET_JOB_SETTING_TYPE) failed(%d)\n", ret);
    }
    else
    {
       result = (unsigned long)bio_get_uint32(&reply);
       binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief set job job id
 * @param[in] timeout_flag
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-01-09
 * @note N/A
 */
void print_ips_set_timeout_flag (uint32_t timeout_flag)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, timeout_flag);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_SET_TIMEOUT_FLAG);
    if ( ret != 0 )
    {
       printf("binder_call(PARSER_IPS_CMD_SET_TIMEOUT_FLAG) failed(%d)\n", ret);
    }
    else
    {
       binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief get job job id
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: without job id
 * @retval >0: job id
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
uint32_t print_ips_get_timeout_flag ( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_TIMEOUT_FLAG);
    if ( ret != 0 )
    {
       printf("binder_call(PARSER_IPS_CMD_GET_TIMEOUT_FLAG) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief if mfp ready,ips starts
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval 0:not ready
 * @retval 1:ready
 * <AUTHOR>
 * @date 2024-12-11
 * @note  N/A
 */
uint32_t print_ips_get_mfp_ready ( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_MFP_READY);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_MFP_READY) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }
    return result;
}

/**
 * @brief get memobj_id
 * @param[in]  void
 * @param[out] N/A
 * @return int32_t
 * @retval memobj_id
 * <AUTHOR>
 * @date 2025-1-11
 * @note  N/A
 */
int32_t print_ips_get_memobj_id ( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_MEMOBJ_ID);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_MEMOBJ_ID) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_GET_MEMOBJ_ID) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief plane data
 * @param[in] band_id
 * @param[out]  N/A
 * @return   PAGE_OUTPUT_QUEUE_STATUS_E
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-1-11
 * @note  N/A
 */
int32_t print_ips_add_band_to_page_output_queue_page_new(uint32_t bind_id)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, bind_id);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_ADD_BAND_TO_PAGE_OUTPUT_QUEUE_PAGE_NEW);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_ADD_BAND_TO_PAGE_OUTPUT_QUEUE_PAGE_NEW) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_ADD_BAND_TO_PAGE_OUTPUT_QUEUE_PAGE_NEW) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief DO exception
 * @param[in] count
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-07-11
 * @note  N/A
 */
void print_ips_set_DO_encrypt_warn(int32_t count) // IPSioSetDOEncryptWarn
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, count);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_SET_DO_ENCRYPT_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_SET_DO_ENCRYPT_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief DO unsupport file foramt
 * @param[in] count
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_DO_unsupported_file_format(int32_t count) // IPSioSetDOUnsupportedFileFormat
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, count);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_SET_DO_UNSUPPORT_FILE_FORMAT);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_SET_DO_ENCRYPT_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief This flag is only callable from IPS and inferno.
 * @param[in] func
 * @param[out] N/A
 * @return returns IPS_CANCEL_ON or IPS_CANCEL_OFF (1 or 0)
 * @retval IPS_CANCEL_FLAG_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
IPS_CANCEL_FLAG_E print_ips_inf_get_cancel_flag(char* func)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    uint32_t            result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_string8(&msg, func);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_INF_GET_CANCEL_FLAG);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_INF_GET_CANCEL_FLAG) failed(%d)\n", ret);
    }
    else
    {
        result = bio_get_uint32(&reply);
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief not used,just because of ips calls
 * @param[in] N/A
 * @param[out] N/A
 * @return int32
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_PJLPS_data_stream(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_IS_PJLPS_DATA_STREAM);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_IS_PJLPS_DATA_STREAM) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_IS_PJLPS_DATA_STREAM) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get USB dummy
 * @param[in] void
 * @param[out] N/A
 * @return  int
 * @retval 0 - FALSE
 * @retval 1 - TRUE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_get_USB_dummy(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_GET_USB_DUMMY);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_GET_USB_DUMMY) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_GET_USB_DUMMY) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief IPS EOJ  IPS call in the time when reading data timeout
 * @param[in] type  0:update flag，don't upload status。1:update flag，upload timeout status。
 * @param[out] N/A
 * @return  void
 * @retval  N/A
 * <AUTHOR>
 * @date 2024-05-28
 * @note  N/A
 */
void print_ips_io_input_timeout(int32_t type)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, type);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_INPUT_TIMEOUT);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_INPUT_TIMEOUT) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief get qio error
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_get_qio_error(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_GET_QIO_ERROR);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_GET_QIO_ERROR) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_GET_QIO_ERROR) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief io always in sniffer
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_always_in_sniffer(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief IPS rewind data to gqio
 * @param[in]  char *buffer    - rewind data
 * @param[in]  int bufsize     - rewind data len
 * @param[in]  flag
 * @param[out] N/A
 * @return  int32_t
 * @retval -1 falure
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_rewind_data(char *buffer, int32_t bytes, int32_t flag)
{
    struct binder_io    msg, reply;
    size_t              *iodata = NULL;
    int32_t             result = 0;
    int32_t             ret = 0;
    uint32_t            data_len = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    data_len = bytes + 32;

    do
    {
        iodata =  (size_t *)pi_malloc(data_len);
        if(NULL == iodata)
        {
            pi_msleep(1);
        }
    }while(NULL == iodata);

    bio_init(&msg, iodata, data_len, 4);
    bio_put_buffer(&msg, buffer,bytes);
    bio_put_uint32(&msg, bytes);
    bio_put_uint32(&msg, flag);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_REWIND_DATA);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_REWIND_DATA) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_REWIND_DATA) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }
    pi_free(iodata);

    return result;
}

/**
 * @brief set pdf font missing warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_pdf_font_missing_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_PDF_FONT_MISSING_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_PDF_FONT_MISSING_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief set pdf font encrypt warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_pdf_encrypt_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_PDF_ENCRYPT_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_PDF_ENCRYPT_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief set pdf font invalid warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_pdf_font_invalid_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_PDF_FONT_INVALID_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_PDF_FONT_INVALID_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief is air print pdf job
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_air_print_pdf_job(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_JOB);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_JOB) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_JOB) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief airprint pdf job read over
 * @param[in] void
 * @param[out] N/A
 * @return int32_T
 * @retval TURE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int32_t print_ips_io_is_air_print_pdf_read_over(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_READ_OVER);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_READ_OVER) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_READ_OVER) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get udisk param/airprint param
 * @param[in] void
 * @param[out] N/A
 * @return IPS_UDISK_PARAM_S
 * @retval IPS_PRINT_PARAM_S
 * <AUTHOR>
 * @date 2024-01-08
 * @note  N/A
 */
IPS_PRINT_PARAM_S print_ips_get_udisk_param(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[1024];
    IPS_PRINT_PARAM_S   result = {0};
    int32_t             ret = 0;
    uint32_t            len = 0;
    char *              tmp = NULL;

    if ( bs == NULL || srv == NULL )
    {
        printf("binder can't init (print_ips_get_udisk_param) failed\n");
        return result;
    }
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_UDISK_PARAM);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_UDISK_PARAM) failed(%d)\n", ret);
    }
    else
    {
        tmp = bio_get_buffer(&reply, &len);
        if ( len == sizeof(IPS_PRINT_PARAM_S) )
        {
            pi_memcpy(&result, tmp, sizeof(IPS_PRINT_PARAM_S));
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get custom paper size tabel
 * @param[in] void
 * @param[out] N/A
 * @return IPS_CUSTOM_TYPE_TABLE_S
 * @retval IPS_CUSTOM_TYPE_TABLE_S
 * <AUTHOR>
 * @date 2024-11-28
 * @note  N/A
 */
IPS_CUSTOM_TYPE_TABLE_S print_ips_get_custom_size_table( void )
{
    struct binder_io    msg, reply;
    size_t              iodata[1024];
    IPS_CUSTOM_TYPE_TABLE_S   result = {0};
    int32_t             ret = 0;
    uint32_t            len = 0;
    char *              tmp = NULL;

    if ( bs == NULL || srv == NULL )
    {
        printf("binder can't init (print_ips_get_custom_size_table) failed\n");
        return result;
    }
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_CUSTOM_SIZE_TABLE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_CUSTOM_SIZE_TABLE) failed(%d)\n", ret);
    }
    else
    {
        tmp = bio_get_buffer(&reply, &len);
        if ( len == sizeof(IPS_CUSTOM_TYPE_TABLE_S) )
        {
            pi_memcpy(&result, tmp, sizeof(IPS_CUSTOM_TYPE_TABLE_S));
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief IPS read data form file gqio
 * @param[in] char *buffer
 * @param[in] int bufsize
 * @param[out] N/A
 * @return int
 * @retval -1 : no data
 * @retval >0 : read data size
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int print_ips_io_file_read_data(char *buffer, int bufsize)
{
    struct binder_io    msg, reply;
    size_t              *iodata = NULL;
    int32_t             result = 0;
    int32_t             ret = 0;
    uint32_t            data_len = 0;
    char                *new_buffer = NULL;
    uint32_t            sz =0;
    int                 bytes = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL || buffer == NULL || bufsize <= 0, printf, -1);

    data_len = bufsize + 32;

    do
    {
        iodata =  (size_t *)pi_malloc(data_len);
        if(NULL == iodata)
        {
            pi_msleep(1);
        }
    }while(NULL == iodata);

    bio_init(&msg, iodata, data_len, 4);
    bio_put_buffer(&msg, buffer,bufsize);
    bio_put_uint32(&msg, bufsize);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_FILE_READ_DATA);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_FILE_READ_DATA) failed(%d)\n", ret);
    }
    else
    {
        new_buffer = bio_get_buffer(&reply,&sz);
        bytes = (int) bio_get_uint32(&reply);
        result = (int) bio_get_uint32(&reply);
        pi_memcpy(buffer,new_buffer,bytes);
        binder_done(bs, &msg, &reply);
    }
    pi_free(iodata);

    return result;
}

/**
 * @brief get page number of saved prn
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval the first print page numbers
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
int print_ips_get_first_pass_prn_pages(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_GET_FIRSET_PASS_PRN_PAGES);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_GET_FIRSET_PASS_PRN_PAGES) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get sample param
 * @param[in] N/A
 * @param[out] N/A
 * @return IPS_SAMPLE_PARAM_S
 * @retval sample param
 * <AUTHOR>
 * @date 2024-05-13
 * @note N/A
 */
IPS_SAMPLE_PARAM_S print_ips_param_get_sample_param(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[1024];
    IPS_SAMPLE_PARAM_S   result = {0};
    int32_t             ret = 0;
    uint32_t            len = 0;
    char *              tmp = NULL;

    if ( bs == NULL || srv == NULL )
    {
        printf("binder can't init (print_ips_get_udisk_param) failed\n");
        return result;
    }
    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_GET_SAMPLE_PARAM);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_GET_SAMPLE_PARAM) failed(%d)\n", ret);
    }
    else
    {
        tmp = bio_get_buffer(&reply, &len);
        if ( len == sizeof(IPS_SAMPLE_PARAM_S) )
        {
            pi_memcpy(&result, tmp, sizeof(IPS_SAMPLE_PARAM_S));
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief get job suspend status
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: normal
 * @retval >0: suspend
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
int32_t print_ips_param_get_job_suspend_status(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, 0);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_GET_JOB_SUSPEND_STATUS);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_GET_JOB_SUSPEND_STATUS) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }

    return result;
}

/**
 * @brief set pdf encrypt warn
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_encrypt_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_FILE_ENCRYPT_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_FILE_ENCRYPT_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief set file large warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_large_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_FILE_LARGE_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_FILE_LARGE_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }
    return;
}

/**
 * @brief set font missing warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_font_missing_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_FILE_MISSING_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_FILE_MISSING_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }
    return;
}

/**
 * @brief set file unsupport warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
void print_ips_io_set_file_unsupport_warn(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_IO_SET_FILE_UNSUPPORT_WARN);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_IO_SET_FILE_UNSUPPORT_WARN) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }
    return;
}

/**
 * @brief enable parse again flag
 * @param[in] flag
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
void print_ips_set_parse_again_flag(int flag)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, flag);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_SET_PARSER_AGAIN_FLAG);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_SET_PARSER_AGAIN_FLAG) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief get parser again flag
 * @param[in] void
 * @param[out] N/A
 * @return  int
 * @retval  int
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
int print_ips_get_parse_again_flag(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             result = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(bs == NULL || srv == NULL, printf, -1);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_GET_PARSER_AGAIN_FLAG);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_GET_PARSER_AGAIN_FLAG) failed(%d)\n", ret);
    }
    else
    {
        if ( !bio_get_speci_data(&reply, &result, sizeof(result)) )
        {
            printf("bio_get(PARSER_IPS_CMD_GET_PARSER_AGAIN_FLAG) failed(%d)\n", result);
        }
        binder_done(bs, &msg, &reply);
    }
    return (int)result;
}

/**
 * @brief answer suspend ack by ips
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-02-21
 * @note N/A
 */
void print_ips_answer_suspend_ack_by_ips(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_ANSWER_SUSPEND_ACK_BY_IPS);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_ANSWER_SUSPEND_ACK_BY_IPS) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }
    return;
}

/**
 * @brief imagemem release
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
void print_ips_set_imagemem_release(void)
{
    struct binder_io    msg, reply;
    size_t              iodata[128];
    int32_t             ret = 0;

    RETURN_IF(bs == NULL || srv == NULL, printf);

    bio_init(&msg, iodata, sizeof(iodata), 4);
    ret = binder_call(bs, &msg, &reply, srv, PARSER_IPS_CMD_SET_IMAGEMEM_RELEASE);
    if ( ret != 0 )
    {
        printf("binder_call(PARSER_IPS_CMD_SET_IMAGEMEM_RELEASE) failed(%d)\n", ret);
    }
    else
    {
        binder_done(bs, &msg, &reply);
    }

    return;
}

/**
 * @brief init cbinder service
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
int32_t print_ips_init(void)
{
    printf("print_ips_init\n");
    bs = binder_start(1);
    RETURN_VAL_IF(bs == NULL, printf, -1);

    printf("print_ips_init\n");

    srv = svcmgr_get_service(bs, BINDER_SERVICE_MANAGER, "pantum.parser.resource");
    RETURN_VAL_IF(srv == NULL, printf, -1);

    return 0;
}

/**
 * @brief stop cbinder service
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
void print_ips_deinit(void)
{
    binder_stop(bs);
    srv = NULL;
    bs = NULL;
}
