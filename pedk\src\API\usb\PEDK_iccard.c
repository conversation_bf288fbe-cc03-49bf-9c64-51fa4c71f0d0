#include "PEDK_iccard.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define countof(x) (sizeof(x) / sizeof((x)[0]))
#define Log(format, ...) printf("[PEDK_iccard] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

/* 
    定义 QuickJS C 函数 
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
JSValue js_get_iccard_info(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    SendMsgToMfp(MSG_MOUDLE_USB_ICCARD, MSG_GET_ICCAED_INFO, 1, 0, NULL);
    return JS_NewString(ctx, "OK");
}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList PEDK_iccard_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */
    {"geticcardinfo", 0, js_get_iccard_info},

};
///
const JSCFunctionList* get_iccard_JSCFunctionList(int *length) {
	*length = countof(PEDK_iccard_funcs);
	return PEDK_iccard_funcs;
}

int js_iccard_init(JSContext *ctx, JSValueConst global)
{
    int i = 0;

    Log("*********start iccard module*******\n");
    /* creat the classes */
    int count = 0;
    const JSCFunctionList* PEDK_funcs = get_iccard_JSCFunctionList(&count);
    //printf("count:%d\n",count);
    for(i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, PEDK_funcs[i].name,
                            JS_NewCFunction(ctx, PEDK_funcs[i].func, PEDK_funcs[i].name, PEDK_funcs[i].length));

    }
    Log("*********start iccard init end*******\n");
    return 0;
}

