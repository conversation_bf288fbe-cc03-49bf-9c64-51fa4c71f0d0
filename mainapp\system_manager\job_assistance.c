/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file job_assistance.c
 * @addtogroup system_manager
 * @{
 * @brief job assistance 
 * <AUTHOR> 
 * @version 1.0
 * @date 2024-04-16
 */

#include "job_assistance.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "ulog.h"
#include "channels.h"
#include "memmgr/memmgr.h"

static int get_value(const char *line)
{
    const char *ptr = strchr(line , ':');
    while(ptr && *ptr != '\0')
    {
        if (*ptr == ' ' || *ptr == ':')
        {
            ++ptr;
        }
        else 
        {
            return atoi(ptr);
        }
    }
    return 0;
}

void memory_status(void)
{
    FILE *fp = fopen("/proc/meminfo" , "r");
    if (!fp)
        return;

    struct {
        const char *key;
        int value;
    }keys[] = {
        {
            .key = "MemTotal" ,
            .value = 0 
        },    
        {
            .key = "MemFree" ,
            .value = 0
        },
        {
            .key = "MemAvailable" ,
            .value = 0
        },
        {
            .key = "Buffers" ,
            .value = 0
        },
        {
            .key = "Cached" ,
            .value = 0
        },
        {
            .key = "HighFree" , 
            .value = 0
        },
        {
            .key = "LowFree" ,
            .value = 0
        },
        {
            .key = "Shmem" ,
            .value = 0
        },
        {
            .key = "SReclaimable" ,
            .value = 0
        },
        {
            .key = "SUnreclaim" ,
            .value = 0
        }
    };
    char line[64];
    int index = 0 , used = 0 , cache = 0;

    memset(line , 0 , sizeof(line));
    while(fgets(line , sizeof(line) , fp))
    {
        for (index = 0; index < sizeof(keys)/sizeof(keys[0]); ++index)
        {
            if (*line == *keys[index].key && strstr((const char*)line , keys[index].key))
            {
                keys[index].value = get_value(line);
                break;
            }
        }
        memset(line , 0 , sizeof(line));
    }

    cache = keys[3].value + keys[4].value + keys[8].value + keys[9].value;
    used = keys[0].value - keys[1].value - cache;

    ULOG_DEBUG(SYS_JOB_LOG , "Mem total:%dKB , used:%dKB , free:%dKB , shared:%dKB , buffer/cache:%dKB , available:%dKB , HighFree:%dKB , LowFree:%dKB\n" , 
            keys[0].value , used , keys[1].value , keys[7].value , cache , keys[2].value , keys[5].value , keys[6].value);

    fclose(fp);
}


void process_status(void)
{
    FILE *fp = fopen("/proc/self/stat" , "r");
    if (!fp)
        return ;

    char line[1024] , status;
    int thread_num = 0 , vsize = 0, rss = 0; 
    
    memset(line , 0 , sizeof(line));
    while(!fgets(line , sizeof(line) , fp));

    fclose(fp);

    sscanf(line , "%*s %*s %c %*s %*s %*s %*s %*s %*s %*s " 
            "%*s %*s %*s %*s %*s %*s %*s %*s %*s %d "
            "%*s %*s %d %d "
            , &status , &thread_num , &vsize , &rss);
    ULOG_DEBUG(SYS_JOB_LOG , "Proc status:%c , thread_num:%d , vsize:%dKB , rss:%dKB\n" , status , thread_num , vsize >> 10 , rss << 2);
}

struct memory_object
{
    int class;
    int feature;
    int num;
    void *address;
};

static struct channels chs;

static void *alloc_obj(void *data)
{
    struct memory_object *out_obj = (struct memory_object*)data;
    struct memory_object *n = (struct memory_object*)malloc(sizeof(struct memory_object));

    if (n)
    {
        n->class = out_obj->class;
        n->feature = out_obj->feature;
        n->address = out_obj->address;
        n->num = 1;
    }
    else
    {
        ULOG_ERROR(SYS_JOB_LOG , "malloc feailed\n");
    }

    return n;
}

static void free_obj(void *data)
{
    struct memory_object *out_obj = (struct memory_object*)data;
    if (out_obj)
    {
        ULOG_DEBUG(SYS_JOB_LOG , "release address:%p\n" , out_obj->address); 
        free(out_obj); 
    }
}

void memory_reference_prolog(void)
{
    channels_init(&chs , alloc_obj , free_obj);
}

static int _match_memory_by_all_args(void *data , void *options)
{
    struct memory_object *in_obj = (struct memory_object*)data;
    struct memory_object *out_obj = (struct memory_object*)options;
    
    if (in_obj->class != out_obj->class ||
        in_obj->feature != out_obj->feature ||
        in_obj->address != out_obj->address)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

static int _match_memory_by_classes(void *data , void *options)
{
    struct memory_object *in_obj = (struct memory_object*)data;
    struct memory_object *out_obj = (struct memory_object*)options;
    
    if (in_obj->class != out_obj->class ||
        in_obj->feature != out_obj->feature)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

static int _match_memory_by_address(void *data , void *options)
{
    struct memory_object *in_obj = (struct memory_object*)data;
    struct memory_object *out_obj = (struct memory_object*)options;
    
    if (in_obj->address != out_obj->address)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

int memory_set_reference(int class , int feature , void *address)
{
    struct memory_object f , *ref = NULL;

    f.class = class;
    f.feature = feature;
    f.address = address;
    ref = (struct memory_object*)channels_find_match(&chs , _match_memory_by_all_args , &f);
    if (ref)
    {
        ULOG_DEBUG(SYS_JOB_LOG , "class:%d , address:%p , reference count:%d\n" , class , address , ref->num+1);
        return ++ref->num;
    }
    else
    {
        ULOG_DEBUG(SYS_JOB_LOG , "class:%d , address:%p\n" , class , address);
        channels_head_insert(&chs , (int)address , &f , 0);
        return 1;
    }        
}

void* memory_get_reference(int class , int feature)
{
    struct memory_object f , *ref = NULL;

    f.class = class;
    f.feature = feature;
    ref = (struct memory_object*)channels_find_match(&chs , _match_memory_by_classes , &f);
    return ref ? ref->address : NULL;
}

int memory_unreference(void *address)
{
    struct memory_object f , *ref = NULL;

    f.address = address;
    ref = (struct memory_object*)channels_find_match(&chs , _match_memory_by_address , &f);
    if (ref)
    {
        --ref->num;
        ULOG_DEBUG(SYS_JOB_LOG , "class:%d , address:%p , reference count:%d\n" , ref->class , address , ref->num);
        if (ref->num <= 0)
        {
            channels_fragment_remove2(&chs , ref , 1);
            return 0;
        }
        return ref->num;
    }
    else
    {
        return -1;
    }
}

void memory_reference_epilog(void)
{
    channels_destroy(&chs);
}

JOB_CONFIG_OBJ_E jobtype_to_jobobj(JOB_TYPE_E jobtype)
{
    if (jobtype >= JOB_TYPE_PRINT_CGDI &&
        jobtype < JOB_TYPE_PRINT_ACL)
    {
        return PRINT_OBJ;
    }
    else if (jobtype >= JOB_TYPE_SCAN_PUSH &&
            jobtype < JOB_TYPE_COPY)
    {
        return SCAN_OBJ;
    }
    else if (jobtype >= JOB_TYPE_COPY &&
            jobtype < JOB_TYPE_MAX)
    {
        return COPY_OBJ;
    }
    else if (jobtype == JOB_TYPE_PRINT_PINCODE || jobtype == JOB_TYPE_PRINT_DELAY)
    {
        return FILE_OBJ;
    }
    else if (jobtype == JOB_TYPE_PRINT_CREATE_PDF)
    {
        return CREATE_PDF_OBJ;
    }
    else
    {
        return 0;
    }
}

int32_t module_first_ready(ROUTER_MSG_S *msg , volatile int8_t *print_ready)
{
    JOB_REQUEST_S *job_request = msg->msg3;
    int32_t ret = 1;

    if (msg->msg2 == PRINT_OBJ || job_request->io_class == IO_CLASS_PRINT)
    {
        if (*print_ready == 0)
        {
            ULOG_WARN(SYS_JOB_LOG , "print not ready!\n");
            ret = 0;
        }
    }
    else if (msg->msg2 == SCAN_OBJ || job_request->io_class == IO_CLASS_SCAN)
    {
        if (status_manager_first_ready(STATUS_ID_MODULE_SCAN) == 0)
        {
            ULOG_WARN(SYS_JOB_LOG , "scan not ready!\n");
            ret = 0;
        }
    }
    else if (msg->msg2 == COPY_OBJ)
    {
        if (status_manager_first_ready(STATUS_ID_MODULE_COPY) == 0)
        {
            ULOG_WARN(SYS_JOB_LOG , "copy not ready!\n");
            ret = 0;
        }
        if (status_manager_first_ready(STATUS_ID_MODULE_PRINT) == 0)
        {
            ULOG_WARN(SYS_JOB_LOG , "print not ready!\n");
            ret = 0;
        }
        if (status_manager_first_ready(STATUS_ID_MODULE_SCAN) == 0)
        {
            ULOG_WARN(SYS_JOB_LOG , "scan not ready!\n");
            ret = 0;
        }
    }
    if (ret == 0)
    {
        sleep(1);
        ROUTER_MSG_S send_msg;

        pi_memcpy(&send_msg , msg , sizeof(ROUTER_MSG_S));
        if (task_msg_send_by_router(MID_SYS_JOB_MGR , &send_msg))
        {
            ULOG_ERROR(SYS_JOB_LOG , "send MID_SYS_JOB_MGR failed\n");
            ret = 1;
        }
    }
    return ret;
}

uint32_t module_ready(JOB_CONFIG_OBJ_E obj , EVT_MGR_CLI_S *client , volatile int8_t *ssd_health_check)
{
    uint8_t *storage_addr = NULL;
    uint32_t storage_len = 0 , ret = 1;
    STATUS_ID_MODULE_E module;

    if (obj && obj != CREATE_PDF_OBJ)//排除PDF下载报告作业
    {
        while(*ssd_health_check)
        {
            ULOG_DEBUG(SYS_JOB_LOG , " ssd health check..\n");
            sleep(1);
        }
        status_manager_get(STATUS_ID_MODULE_STORAGE , &storage_addr , &storage_len);
        if (storage_addr)
        {
            for (int i = 0; i * 16 < storage_len; ++i)
            {
                uint32_t status = *(uint32_t*)(storage_addr + i * 16);//16 = 4(statusid) + 4(statusargs1) + 4(statusargs2) + 4(statusargs3)
                
                if (status == STATUS_W_STORAGE_INNER_CAPACITY_FULL||
                    status == STATUS_E_STORAGE_INNER_CAPACITY_FULL)
                {
                    ULOG_WARN(SYS_JOB_LOG , "storage warning:%x\n" , status);
                    ret = 0;
                    pi_event_mgr_notify(client , EVT_TYPE_STORAGEMGR_INNER_FULL_CHANGE_REQUEST , NULL , 0);
                    break;
                }
                else if (status == STATUS_W_STORAGE_HARDDISK_CAPACITY_FULL||
                         status == STATUS_E_STORAGE_HARDDISK_CAPACITY_FULL)
                {
                    ULOG_WARN(SYS_JOB_LOG , "storage warning:%x\n" , status);
                    ret = 0;
                    pi_event_mgr_notify(client , EVT_TYPE_STORAGEMGR_SATASSD1_FULL_CHANGE_REQUEST , NULL , 0);
                    break;
                }
            }
        }
    }

    pi_free(storage_addr);
    return ret;
}

void power_ready(EVT_MGR_CLI_S *client , uint8_t *energy_saving_rouse)
{
    do
    {
        uint8_t *addr = NULL;
        uint32_t len = 0 , id = 0 , arg = 0 , data = 0;

        status_manager_get(STATUS_ID_MODULE_POWERMGR , &addr , &len);
        if (len == 0)
        {
            ULOG_WARN(SYS_JOB_LOG , "Cannot get powermanager status.\n");
            break;
        }
        id = *(uint32_t*)(addr);
        arg = *(uint32_t*)(addr + sizeof(uint32_t));
        pi_free(addr);
        addr = NULL;
        ULOG_DEBUG(SYS_JOB_LOG , "[%x]state parameter value:%x\n" , id , arg);
        if (id == STATUS_I_POWERMGR_WAKEUP) //就绪状态
        {
            break;
        }
        else if (id == STATUS_I_POWERMGR_SLEEP) //休眠状态
        {
            if (arg == 0x0)//睡眠
            {
                data = 0x3;
            }
            else if (arg == 0x1)//节能
            {
                data = 0x4;
                if (energy_saving_rouse)
                {
                    //从节能下唤醒置该标志
                    *energy_saving_rouse = 1;
                }
            }
            if (data)
            {
                pi_event_mgr_notify(client , EVT_TYPE_POWERMGR_CONFIG , &data , sizeof(data));
            }
        }
        else
        {
            ULOG_INFO(SYS_JOB_LOG , "wait for next state.\n");
        }
        sleep(2);
    }while(1);
}



/**
 * @}
 */
