#include <string.h>
#include <stdio.h>
#include "PEDK_net_ftp.h"
#include "PEDK_event.h"

#include <quickjs.h>

JSValue js_ftp_connect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    PEDK_FTP_PARAMETER_SET ftp_connect_info;
    int respond = -1;
    int receive_data[512];
    int receive_cnt = sizeof(receive_data);

    if( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];

    JSValueConst host = JS_GetPropertyStr( ctx, obj, "host");
    JSValueConst port = JS_GetPropertyStr( ctx, obj, "port");
    JSValueConst path = JS_GetPropertyStr( ctx, obj, "path");
    JSValueConst username = JS_GetPropertyStr( ctx, obj, "username");
    JSValueConst password = JS_GetPropertyStr( ctx, obj, "password");
    JSValueConst anonymous = JS_GetPropertyStr( ctx, obj, "anonymous");

    const char *c_host = JS_ToCString(ctx, host);
    const char *c_path = JS_ToCString(ctx, path);
    const char *c_username = JS_ToCString(ctx, username);
    const char *c_password = JS_ToCString(ctx, password);

    strncpy(ftp_connect_info.ftp_addr, c_host, strlen(c_host));
    ftp_connect_info.ftp_addr[strlen(c_host)] = '\0';
    strncpy(ftp_connect_info.ftp_server_path, c_path, strlen(c_path));
    ftp_connect_info.ftp_server_path[strlen(c_path)] = '\0';
    strncpy(ftp_connect_info.ftp_login_name, c_username, strlen(c_username));
    ftp_connect_info.ftp_login_name[strlen(c_username)] = '\0';
    strncpy(ftp_connect_info.ftp_login_pwd, c_password, strlen(c_password));
    ftp_connect_info.ftp_login_pwd[strlen(c_password)] = '\0';


    JS_ToInt32(ctx, &ftp_connect_info.ftp_port, port);
    JS_ToInt32(ctx, &ftp_connect_info.ftp_anonymity, anonymous);

    JS_FreeCString(ctx, c_host);
    JS_FreeCString(ctx, c_username);
    JS_FreeCString(ctx, c_password);
    JS_FreeCString(ctx, c_path);
    JS_FreeValue(ctx, obj);
    JS_FreeValue(ctx, host);
    JS_FreeValue(ctx, port);
    JS_FreeValue(ctx, path);
    JS_FreeValue(ctx, username);
    JS_FreeValue(ctx, password);
    JS_FreeValue(ctx, anonymous);


    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_FTP_CONNECT, 0, sizeof(PEDK_FTP_PARAMETER_SET), &ftp_connect_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_FTP_CONNECT, &respond, NULL, NULL, 3);

    if(respond < 0)
    {
        return JS_NewString(ctx, "connect ftp server fail");
    }
    else
    {
        return JS_NewString(ctx, "connect ftp server success");
    }
}


JSValue js_ftp_disconnect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int respond = -1;

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_FTP_DISCONNECT, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_FTP_DISCONNECT, &respond, NULL, NULL, 3);

    if(respond < 0)
    {
        return JS_NewString(ctx, "disconnect ftp server fail");
    }
    else
    {
        return JS_NewString(ctx, "disconnect ftp server success");
    }
}