/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file data_distribution.c
 * @addtogroup proxy
 * @{
 * @brief inter-board communication,distributing the data of the peer
 *         to the corresponding module
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18
 */


#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "public_data_proc.h"
#include "public/msgrouter_main.h"
#include "utilities/msgrouter.h"
#include "qio/qio_general.h"
#include "job_manager.h"
#include "panel_dc_cmd_process.h"
#include "panel_dc_main.h"
#include "pol/pol_log.h"
#include "panel_event.h"

#define     PANEL_DC_CMD_HEAD       4       ///< board communication cmd head lenght

/**
 * @brief board communication msg struct
 * <AUTHOR>
 * @data   2023-06-08
*/
typedef struct
{
    uint8_t CMD_TPYE;
    uint16_t CMD_VALUE;
    uint8_t CMD_MSG_TYPE;
}PANEL_CMD_HEADER_S;

/**
 * @brief send back to panel when recv cmd no support
 * @param[in] ptr:       recv data
 * @param[in] len:       data len
 * <AUTHOR>
 * @data   2023-06-08
*/
static void no_support_cmd(unsigned char *ptr , unsigned int len)
{
    unsigned char value[ PANEL_DC_CMD_HEAD ];

    (void)len;
    memcpy(value , ptr , sizeof(value));
    value[3] = FAIL_RESPONSE;

    proxy_send(value , sizeof(value) , NULL);
}

/**
 * @brief recv data from panel and distribution data to other moudle
 * @param[in] data       recv data
 * @param[in] len       data len
 * <AUTHOR>
 * @data   2023-06-08
*/
void data_distribution(void *data , unsigned int len)
{
    if ( !data || len < PANEL_DC_CMD_HEAD )
        return;

    PANEL_CMD_HEADER_S Head;
    uint8_t *ptr= (uint8_t*)data;

    Head.CMD_TPYE =  ptr[0];
    Head.CMD_VALUE = *(unsigned short*)(ptr + 1);
    Head.CMD_MSG_TYPE = ptr[3];
    pi_log_i("cmd_type:%u , cmd_value:%d , msg_type:%u\n" , Head.CMD_TPYE , Head.CMD_VALUE , Head.CMD_MSG_TYPE);

    switch(Head.CMD_TPYE)
    {
        case INFORMATION:
            information_cmd_process( Head.CMD_VALUE, (void *)( ptr + PANEL_DC_CMD_HEAD), len - PANEL_DC_CMD_HEAD );
            break;

        case SETTING:
            setting_cmd_process( Head.CMD_VALUE, (void *)( ptr + PANEL_DC_CMD_HEAD), len - PANEL_DC_CMD_HEAD );
            break;

        case JOB:
            job_cmd_process( Head.CMD_VALUE, (void *)( ptr + PANEL_DC_CMD_HEAD), len - PANEL_DC_CMD_HEAD );
            break;

        case OPERATE:
            operate_cmd_process( Head.CMD_VALUE, (void *)( ptr + PANEL_DC_CMD_HEAD), len - PANEL_DC_CMD_HEAD );
            break;

        case STATUS:
            break;
        case RESOURCE:
            resource_cmd_process( Head.CMD_VALUE, (void *)( ptr + PANEL_DC_CMD_HEAD), len - PANEL_DC_CMD_HEAD );
            break;
        case UPGRADE:
            break;
        case OTHER:
            other_cmd_process( Head.CMD_VALUE, (void *)( ptr + PANEL_DC_CMD_HEAD), len - PANEL_DC_CMD_HEAD );
            break;

        default:
            no_support_cmd(ptr , len);
            break;
    }
}
/**
 * @}
 */
