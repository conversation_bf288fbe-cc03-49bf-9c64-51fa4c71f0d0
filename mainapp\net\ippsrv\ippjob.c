#include "nettypes.h"
#include "netctx.h"

#include "ippx.h"

#define IPP_JOB_TAG 0x49505000  /* IPP\0 */

static void ippjob_destroy_attribute_handle(void* pset, uint32_t type)
{
    RETURN_IF(type != IPP_JOB_TAG || pset == NULL, NET_WARN);

    IPPATTRdestroyJobAttributeSet(pset);
}

int32_t ippjob_set_attribute_handle(int32_t jobid, void* pset)
{
    RETURN_VAL_IF(netjob_set_userdata(jobid, IPP_JOB_TAG, pset, ippjob_destroy_attribute_handle) != 0, NET_WARN, IPP_NOT_FOUND);

    return IPP_OK;
}

int32_t ippjob_get_attribute_handle(int32_t jobid, void** ppset)
{
    uint32_t type;

    RETURN_VAL_IF(jobid <= 0 || ppset == NULL, NET_WARN, IPP_INTERNAL_ERROR);
    RETURN_VAL_IF(netjob_get_userdata(jobid, &type, ppset) != 0, NET_WARN, IPP_NOT_FOUND);
    RETURN_VAL_IF(type != IPP_JOB_TAG, NET_WARN, IPP_NOT_POSSIBLE);

    return IPP_OK;
}

int32_t ippjob_init_job_attributes(IPP_CTX_S* ppp, void* pset)
{
    void*   pattr;
    int32_t value;
    int32_t count;
    int32_t type;
    time_t  now;

    // clear all req/set flags, and all data to 0 (a blank set)
    IPPATTRclearJobAttributes(pset);

    value = IPP_JOB_PENDING;
    IPPATTRsetJobAttribute(pset, "job-state", &value, 0);
    IPPATTRsetJobAttribute(pset, "job-state-reasons", "none", 0);
    IPPATTRsetJobAttribute(pset, "job-state-message", "none", 0);

    // Initialize job attributes with defaults, since the print-job could have
    // asked us to return all these, and they need to have proper values to return
    // plus, I can use the values as defaults for setting job properties
    value = 1;
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "copies-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)&value;
    }
    IPPATTRsetJobAttribute(pset, "copies", pattr, 0);

    // finishings
    value = 1;
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "finishings-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)&value;
    }
    IPPATTRsetJobAttribute(pset, "finishings", pattr, 0);

    // fidelity
    value = 0;
    pattr = &value;
    IPPATTRsetJobAttribute(pset, "ipp-attribute-fidelity", pattr, 0);

    // media
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "media-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)"";
    }
    IPPATTRsetJobAttribute(pset, "media", pattr, 0);

    // media-col
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "media-col-default", &type, &pattr, 0, &count) != 0)
    {
        pattr = NULL;
    }
    else
    {
        IPPATTRsetJobAttribute(pset, "media-col", pattr, 0);
    }

    // orientation-requested
    value = 7; // none
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "orientation-requested-default", &type, &pattr, 0, &count) != 0)
    {
        pattr = (void *)&value;
    }
    IPPATTRsetJobAttribute(pset, "orientation-requested", pattr, 0);

    // output-bin
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "output-bin-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)"top";
    }
    IPPATTRsetJobAttribute(pset, "output-bin", pattr, 0);

    // multiple-document-handling
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "multiple-document-handling-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)"separate-documents-collated-copies";//==修改属性默认值,对于bug2897 2016.5.24 zw
    }
    IPPATTRsetJobAttribute(pset, "multiple-document-handling", pattr, 0);

    // print-color-mode
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "print-color-mode-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)"auto";
    }
    IPPATTRsetJobAttribute(pset, "print-color-mode", pattr, 0);

    // print-quality
    value = 4; // normal
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "print-quality-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = &value;
    }
    IPPATTRsetJobAttribute(pset, "print-quality", pattr, 0);

    // print-scaling
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "print-scaling-default", &type, &pattr, 0, &count) != 0)
    {
        pattr = "auto";
    }
    IPPATTRsetJobAttribute(pset, "print-scaling", pattr, 0);

    //----- sides
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "sides-default", &type, &pattr, 0, &count) != 0)
    {
        pattr = "one-sided";
    }
    IPPATTRsetJobAttribute(pset, "sides", pattr, 0);

    // Clear the set/requested flag on all the default attributes so far.
    // we will only report them if they are set after this (by us
    // or by the operations attributes)
    IPPATTRrequestAllJobAttributes(pset, 0);
    IPPATTRsetAllJobAttributes(pset, 0);
    IPPATTRsetJobAttributesFromSetOperationAttributes(ppp, pset);

    // Now, some job attributes are derived from the operation attributes
    // but not named the same, so for those that are present, translate
    if ( IPPATTRgetOperationAttribute(ppp, "requesting-user-name", &type, &pattr, 0, &count) != 0)
    {
        pattr = (void *)"no-user";
    }
    IPPATTRsetJobAttribute(pset, "job-originating-user-name", pattr, 0);

    // And some job attributes are not JOB_TEMPLATE_ATTRIBUTE so aren't copied
    // in the setJobAttributesFromSetOperationAttributes above but need to
    // be reported in get-job-attributes anyway
    //
    if ( IPPATTRgetOperationAttribute(ppp, "job-name", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)"no-name";
    }
    IPPATTRsetJobAttribute(pset, "job-name", pattr, 0);

    // printer-resolution
    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "printer-resolution-default", &type, &pattr, 0, &count) != 0 )
    {
        pattr = (void *)"600x600x3";
    }
    IPPATTRsetJobAttribute(pset, "printer-resolution", pattr, 0);

    if ( IPPATTRgetPrinterAttribute(ppp->serviceType, "printer-up-time", &type, &pattr, 0, &count) != 0 )
    {
        pattr = NULL;
    }
    IPPATTRsetJobAttribute(pset, "job-printer-up-time", pattr, 0);

    pi_time(&now);
    IPPATTRsetJobAttribute(pset, "time-at-creation", (void *)&now, 0);

    return IPP_OK;
}

int32_t ippjob_set_document_properties(IPP_CTX_S* ppp, void* pset)
{
    void*   pattr = NULL;
    int32_t docpdl = PrintPDL_Unknown;
    int32_t count;
    int32_t type;
    int32_t rc = IPP_OK;

    if ( IPPATTRgetOperationAttribute(ppp, "document-format", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        if ( strcmp(pattr, "application/octet-stream") == 0 )
        {
            docpdl = PrintPDL_Unknown;
        }
#if CONFIG_URF
        else if ( strcmp(pattr, "image/urf") == 0 )
        {
            docpdl = PrintPDL_URF;
        }
#endif
#if CONFIG_PDF
        else if ( strcmp(pattr, "image/pdf") == 0 || strcmp(pattr, "application/pdf") == 0 )
        {
            docpdl = PrintPDL_PDF;
        }
#endif
#if CONFIG_PWG
        else if ( strcmp(pattr, "image/pwg") == 0 || strcmp(pattr, "image/pwg-raster") == 0 )
        {
            docpdl = PrintPDL_PWG;
        }
#endif
#if CONFIG_JPEG
        else if ( strcmp(pattr, "image/jpeg") == 0 )
        {
            docpdl = PrintPDL_JFIF;
        }
#endif
        else
        {
            rc = IPP_DOCUMENT_FORMAT;
        }

        if ( rc == IPP_OK )
        {
            IPPATTRsetJobAttribute(pset, "pdl-set", (void*)&docpdl, 0);
        }
        else
        {
            NET_DEBUG("unsupport printer language %s", (char *)pattr);
        }
    }

    return rc;
}

int32_t ippjob_set_job_properties(int32_t jobid)
{
    IPP_ATTR_S* pmc = NULL;
    QIO_S*      pqio;
    void*       pset;
    void*       pattr;
    char*       pstr;
    char*       find;
    int32_t     type;
    int32_t     count;
    int32_t     val;
    int32_t     job_orientation;
    int32_t     i;
    int32_t     scalemodeIsSet = 0;
    int32_t     mediacolIsSet = 0;
    int32_t     mediaIsSet = 0;
    int32_t     scalemode = 0;
    int32_t     total_len = 0;
    int32_t     recv_len = 0;
    int32_t     add_len = 0;
    int32_t     index;
    int32_t     rc;
    char        page_ranges[128]= {0};

    RETURN_VAL_IF(ippjob_get_attribute_handle(jobid, &pset) != 0, NET_WARN, IPP_NOT_FOUND);

    pqio = netjob_get_pqio(jobid);
    RETURN_VAL_IF(pqio == NULL, NET_WARN, IPP_NOT_FOUND);

    DECL_PRIV(pqio, PARSER_DATA_S, parser_data);
    RETURN_VAL_IF(parser_data == NULL, NET_WARN, IPP_NOT_POSSIBLE);

    //memset(pio->request_user_name, 0x00, sizeof(pio->request_user_name));
    //if ( !ipp_get_request_user_name(pio->request_user_name) )
    //{
    //    NET_DEBUG("request_user_name: %s\n", pio->request_user_name);
    //}

    parser_data->fidelity_mode = 0; //保真参数
    if ( IPPATTRgetJobAttribute(pset, "ipp-attribute-fidelity", &type, &pattr, 0, &count) == 0 )
    {
        parser_data->fidelity_mode = *(int32_t*)pattr;
        NET_DEBUG("get fidelity: (%d) parser_data->fidelity_mode: (%d)\n", *(int32_t*)pattr, parser_data->fidelity_mode);
    }

    /* 份数 */
    if ( IPPATTRgetJobAttribute(pset, "copies", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        val = *(int32_t*)pattr;
        parser_data->copies = ((val > 0) ? val : 1);
    }
    else
    {
        parser_data->copies = 1;
    }
    NET_DEBUG("copies(%u)", parser_data->copies);

    /* 逐份打印 1 - 开启；0 - 关闭 */
    if ( IPPATTRgetJobAttribute(pset, "multiple-document-handling", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        pstr = (char *)pattr;
        NET_DEBUG("multiple-document-handling(%s)", pstr);
        if ( strcmp(pstr, "separate-documents-collated-copies") == 0 )
        {
            parser_data->collate_mode = 1;
        }
        else
        {
            parser_data->collate_mode = 0;
        }
    }
    else
    {
        parser_data->collate_mode = 1;
    }
    NET_DEBUG("collate_mode(%u)", parser_data->collate_mode);

    IPPATTRgetJobAttributeIsSet(pset, "media-col", &mediacolIsSet);
    IPPATTRgetJobAttributeIsSet(pset, "media", &mediaIsSet);
    NET_DEBUG("mediaIsSet(%d) mediacolIsSet(%d)", mediaIsSet, mediacolIsSet);

    if ( IPPATTRgetJobAttribute(pset, "media", &type, &pattr, 0, &count) == 0 )
    {
        pmc = IPPATTRmediaStringToProperty((const char *)pattr, parser_data);
    }

    if ( mediacolIsSet != 0 || mediaIsSet == 0 )
    {
        if ( IPPATTRgetJobAttribute(pset, "media-col", &type, &pattr, 0, &count) == 0 )
        {
            pmc = (IPP_ATTR_S *)pattr;
        }
    }

    if ( pmc != NULL )
    {
        if ( IPPATTRmediaPropertyToParam(pmc, parser_data, mediacolIsSet) != 0 )
        {
            NET_INFO("get a media in job attributes failed\n");
//            return IPP_NOT_POSSIBLE;
        }
    }

    // 品质参数
    parser_data->quality = PARSER_QUALITY_AUTO;
    if ( IPPATTRgetJobAttribute(pset, "print-quality", &type, &pattr, 0, &count) == 0 )
    {
        val = *(int32_t*)pattr;
        NET_DEBUG("print-quality(%d)", val);
        switch ( val )
        {
            case 3 : parser_data->quality = PARSER_QUALITY_DRAFT;  break;
            case 4 : parser_data->quality = PARSER_QUALITY_NORMAL; break;
            case 5 : parser_data->quality = PARSER_QUALITY_HIGH;   break;
            default: break;
        }
    }

    /* 方向参数     */
    /* iphon15  竖->3  横->5 */
    /* mopria   竖->3  横->4 */
    parser_data->orientation = PARSER_IMAGE_DIRECT_AUTO;
    if ( IPPATTRgetJobAttribute(pset, "orientation-requested", &type, &pattr, 0, &count) == 0 )
    {
        job_orientation = *(int32_t*)pattr;
        NET_DEBUG("orientation-requested: %d\n", job_orientation);
        switch ( job_orientation )
        {
            case  3:
                parser_data->orientation = PARSER_IMAGE_DIRECT_PORTRAIT;
                break;
            case  4:
                parser_data->orientation = PARSER_IMAGE_DIRECT_LANDSCAPE;
                break;
            case  5:
                parser_data->orientation = PARSER_IMAGE_DIRECT_LANDSCAPE;
                break;
            default:
                parser_data->orientation = PARSER_IMAGE_DIRECT_AUTO;
                break;
        }

    }
    NET_DEBUG("orientation set to %d\n", parser_data->orientation);

    /* 装订参数     */
    parser_data->staple_mode = STAPLE_MODE_OFF;
    parser_data->punch_mode = PUNCH_MODE_OFF;
    parser_data->fold_mode = FOLD_MODE_OFF;

    /* finishings-col */
    if ( IPPATTRgetJobAttribute(pset, "finishings-col", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        val = ippattr_get_finishings_col_num(pattr);
        if ( val != -1 )
        {
            NET_DEBUG("finishings_col = (%d)", val);
            IPPATTRfinishStringToProperty(val, job_orientation, parser_data);
        }
    }
    else
    {
        NET_DEBUG("finishings-col is none");
    }
    NET_DEBUG("finishings_col - staple_mode(%d) punch_mode(%d) fold_mode(%d)", parser_data->staple_mode, parser_data->punch_mode, parser_data->fold_mode);

    /* finishings */
    for (i = 0; i < 3; ++i)
    {
        if ( IPPATTRgetJobAttribute(pset, "finishings", &type, &pattr, i, &count) == 0 )
        {
            val = *(int32_t*)pattr;
            NET_DEBUG("finishings val[%d] = (%d)", i, val);
            NET_DEBUG("finishings_col_num(%d)", val);
            IPPATTRfinishStringToProperty(val, job_orientation, parser_data);
        }
        else
        {
            NET_DEBUG("finishings count(%d)", count);
            break;
        }
    }
    NET_DEBUG("staple_mode(%d) punch_mode(%d) fold_mode(%d)  ", parser_data->staple_mode, parser_data->punch_mode, parser_data->fold_mode);

    // Scale Mode TODO: 缩放参数，PARSER_DATA_S暂无对应属性
    parser_data->scale_mode = PARSER_SCALE_MODE_AUTO; // IPS_PRINT_SCALE_MODE_AUTO;//072024:20151026 aap 1.5 default mode = auto
    if ( IPPATTRgetJobAttribute(pset, "print-scaling", &type, &pattr, 0, &count) == 0 )
    {
        NET_DEBUG("print-scaling: '%s'\n", (char *)pattr);

       if ( strcmp(pattr, "auto") == 0 )
        {
            // default behaviour is to fit to margin paper and fill margin-less paper
            // scalemode = margins ? PrintScaleMode_Fit : PrintScaleMode_Fill;
            scalemode = PARSER_SCALE_MODE_AUTO; // IPS_PRINT_SCALE_MODE_AUTO;

            // if the request really set print-scaling and this isn't our
            // default, then turn auto-fit off if fidelity is not tue
           IPPATTRgetJobAttributeIsSet(pset, "print-scaling", &scalemodeIsSet);
            if (scalemodeIsSet && parser_data->fidelity_mode)
           {
                scalemode = PARSER_SCALE_MODE_AUTOFIT; //IPS_PRINT_SCALE_MODE_AUTOFIT;
           }
        }
        else if ( strcmp(pattr, "auto-fit") == 0 )
        {
            if (parser_data->fidelity_mode)
            {
                scalemode = PARSER_SCALE_MODE_FIT; // IPS_PRINT_SCALE_MODE_FIT;
            }
            else
            {
               scalemode = PARSER_SCALE_MODE_AUTOFIT; // IPS_PRINT_SCALE_MODE_AUTOFIT;
            }
        }
        else if ( strcmp(pattr, "fill") == 0 )
        {
           scalemode = PARSER_SCALE_MODE_FILL; // IPS_PRINT_SCALE_MODE_FILL;
        }
        else if ( strcmp(pattr, "fit") == 0 )
        {
            scalemode = PARSER_SCALE_MODE_FIT; // IPS_PRINT_SCALE_MODE_FIT;
        }
        else
        {
            scalemode = PARSER_SCALE_MODE_CENTER; // IPS_PRINT_SCALE_MODE_CENTER;
        }
        parser_data->scale_mode = scalemode;
    //    parser_data->inuse |= IPP_INUSE_SCALE;
    }
    NET_DEBUG("print-scaling: (%d)\n", parser_data->scale_mode);

    /* 分辨率模式 0-300DPI 1-600DPI */
    parser_data->resolution_mode = 0;
    if ( IPPATTRgetJobAttribute(pset, "printer-resolution", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        int32_t xres, yres;

        pstr = (char *)pattr;
        NET_DEBUG("printer-resolution(%s)", pstr);
        xres = yres = strtoul(pstr, &pstr, 10);
        if ( pstr != NULL )
        {
            yres = strtoul(pstr + 1, NULL, 10);
        }
        NET_DEBUG("xres(%d) yres(%d)", xres, yres);
        switch ( xres )
        {
            case 300: parser_data->resolution_mode = 0; break;
            case 600: parser_data->resolution_mode = 1; break;
            default: break;
        }
    }

    if ( IPPATTRgetJobAttribute(pset, "pdl-set", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        val = *(int32_t *)pattr;
        NET_DEBUG("pdl-set(%d)", val);
        //pio->pdl_set = val;
        //pio->inuse |= IPP_INUSE_PDL_SET;
    }

    parser_data->print_mode = 0; /* 默认单面打印 */
    if ( IPPATTRgetJobAttribute(pset, "sides", &type, &pattr, 0, &count) == 0 && pattr != NULL )
    {
        pstr = (char *)pattr;
        NET_DEBUG("sides(%s)", (char *)pstr);
        if ( strcmp(pstr, "one-sided") == 0 )
        {
            parser_data->print_mode = 0; /* 默认单面打印 */
        }
        else if ( strcmp(pstr, "two-sided-short-edge") == 0 )
        {
            parser_data->print_mode = 1; /* 双面短边打印 */
        }
        else if ( strcmp(pstr, "two-sided-long-edge") == 0 )
        {
            parser_data->print_mode = 2; /* 双面长边打印 */
        }
        else
        {
            NET_WARN("unsupported sides(%s)", pstr);
        }
    }

    parser_data->color = PARSER_COLOR_MODE_AUTO;
    if ( IPPATTRgetJobAttribute(pset, "print-color-mode", &type, &pattr, 0, &count) == 0 )
    {
        NET_DEBUG("print-color-mode: '%s'\n", (char *)pattr);
        if ( strcmp(pattr, "color") == 0 )
        {
            parser_data->color = PARSER_COLOR_MODE_COLOR;
        }
        else if ( strcmp(pattr, "auto-monochrome") == 0 )
        {
            parser_data->color = PARSER_COLOR_MODE_MONOCHROME;
        }
        else if ( strcmp(pattr, "monochrome") == 0 )
        {
            parser_data->color = PARSER_COLOR_MODE_MONOCHROME;
        }
        else if ( strcmp(pattr, "auto") == 0 )
        {
            parser_data->color = PARSER_COLOR_MODE_AUTO;
        }
    }

    if( IPPATTRgetJobAttribute(pset, "job-name", &type, &pattr, 0, &count) == 0 )
    {
        NET_DEBUG("job-name: '%s'\n", (char*)pattr);
        snprintf(parser_data->job_name, sizeof(parser_data->job_name), "%s", (char*)pattr);
    }

    pi_memset(parser_data->page_range, '\0', sizeof(parser_data->page_range));
    if ( IPPATTRgetJobAttribute(pset, "page-ranges", &type, &pattr, 0, &count) == 0 )
    {
        for ( index = 0, rc = 0; index < count && rc == 0; index ++ )
        {
            NET_DEBUG("index:=[%d]", index);
            if ( index != 0 && total_len < sizeof(page_ranges) )
            {
                page_ranges[total_len] = ',';
                total_len += 1;
            }

            if ( IPPATTRgetJobAttribute(pset, "page-ranges", &type, &pattr, index, &count) == 0 )
            {
                recv_len = strlen((char*)pattr);
                add_len = (total_len + recv_len) < sizeof(page_ranges) ? recv_len : (sizeof(page_ranges) - total_len);
                add_len = add_len > 0 ? add_len : 0;

                NET_DEBUG("get attr <page-ranges> index<%d> of count<%d>, value:=[%s], sizeof(page_ranges):=[%d], recv_len:=[%d], addlen:=[%d], total_len:=[%d]",
                index, count, (char *)pattr, sizeof(page_ranges), recv_len, add_len, total_len);

                pi_memcpy(page_ranges + total_len, pattr, add_len);
                find = strchr((page_ranges + total_len), ',');
                if ( STRING_NO_EMPTY(find) )
                {
                    *find = '-';
                }
                total_len += add_len;
            }
            else
            {
                NET_WARN("get attr <page-ranges> index<%d> of count<%d> failed, rc:=[%d]", index, count, rc);
            }
            NET_DEBUG("age_ranges: '%s'", page_ranges);
            pi_snprintf(parser_data->page_range, sizeof(parser_data->page_range), "%s", page_ranges);
            NET_DEBUG("parser_data->page_ranges: '%s'", parser_data->page_range);
        }
    }

    return IPP_OK;
}

int32_t ippjob_update_job_state(int32_t jobid)
{
    NETJOB_STATE_E  jobstate;
    const char*     ipprealreason = NULL;
    const char*     ippreason = NULL;
    void*           pset;
    int32_t         ippstate;
    int32_t         pages;
	uint32_t        pagestotal;
    int32_t         rc;

    RETURN_VAL_IF(ippjob_get_attribute_handle(jobid, &pset) != 0, NET_WARN, 0);

    jobstate = netjob_get_state(jobid);
    NET_DEBUG("jobid: %d, jobstate: %s\n", jobid, netjob_state_string(jobstate));
    switch (jobstate)
    {
        case NETJOB_STATE_NEW:
        case NETJOB_STATE_INIT:
            ippstate = IPP_JOB_PENDING;
            break;
        case NETJOB_STATE_PAUSED:
            ippstate = IPP_JOB_HELD;
            break;
        case NETJOB_STATE_PROCESSING:
        case NETJOB_STATE_RUNNING:
            ippstate = IPP_JOB_PROCESSING;
            break;
        case NETJOB_STATE_CANCELED:
            ippstate = IPP_JOB_CANCELED;
            ippreason = "job-canceled-by-user";
            break;
        case NETJOB_STATE_DONE:
            ippstate = IPP_JOB_COMPLETED;
            break;
        case NETJOB_STATE_ABORTED:
        default:
            ippstate = IPP_JOB_ABORTED;
            ippreason = "job-completed-with-errors";
            break;
    }
    NET_DEBUG("ippstate: %d, ippreason: '%s'\n", ippstate, ippreason);

    pagestotal = netjob_get_pages_total(jobid);
    if (pagestotal > 0)
	{
		IPPATTRsetJobAttribute(pset, "job-impressions", (void *)&pagestotal, 0);
	}
	else
	{
		pagestotal = 3;
		IPPATTRsetJobAttribute(pset, "job-impressions", (void *)&pagestotal, 0);
	}

    pages = netjob_get_pages_done(jobid);;
    IPPATTRsetJobAttribute(pset, "job-impressions-completed", (void *)&pages, 0);
    IPPATTRsetJobAttribute(pset, "job-media-sheets-completed", (void *)&pages, 0);
    NET_DEBUG("jobid(%d) ippstate(%d) pagestotal(%u) pageprinted(%d)", jobid, ippstate, pagestotal, pages);

    rc = IPPATTRsetJobAttribute(pset, "job-state", (void *)&ippstate, 0);
    if (ippreason)
    {
        rc += IPPATTRsetJobAttribute(pset, "job-state-message", (void *)ippreason, 0);
        rc += IPPATTRsetJobAttribute(pset, "job-state-reasons", (void *)ippreason, 0);
        if (ippstate == IPP_JOB_ABORTED)
        {
            if (ipprealreason == NULL)
            {
                ipprealreason = "document-format-error";
            }
            NET_DEBUG("ipprealreason: %s\n", ipprealreason);
            rc += IPPATTRsetJobAttribute(pset, "job-state-reasons", (void *)ipprealreason, 1);
        }
    }
    else
    {
        rc += IPPATTRsetJobAttribute(pset, "job-state-message", (void *)"none", 0);
        rc += IPPATTRsetJobAttribute(pset, "job-state-reasons", (void *)"none", 0);
    }

    return rc;
}


