/**************************************************************
Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:     PEDK parser
file name:       PEDK_parser.h
author:          <PERSON> (<EMAIL>)
date:            2023-12-21
description:     authority adapt API
**************************************************************/
#ifndef __PEDK_PARSER_H__
#define __PEDK_PARSER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "cjson/cJSON.h"

typedef struct {
    char **model;
    char **type;
    char **srceen_type;
}PEDK_PRINTER_INFO_S;

// 定义结构体
typedef struct {
    char *name;
    char *bundleName;
    char *version;
    char *api_ver;
    char *ui_ver;
    char *main;
    char *icon;
    char *license;
    char *description;
    char *author;
    int rootApp;
    //PEDK_PRINTER_INFO_S printer_info;
    //char **whitelist;
} PEDK_APP_CONFIG_S;

/**
 * @brief 解析app.json
 * @param[in] filename：app.json名称，注意需要传入绝对路径 如/pesf/app.json
 * @author:yangzikun
 * @return PEDK_APP_CONFIG_S
 * retval !NULL for success
 * retval NULL for failure.
 */
PEDK_APP_CONFIG_S *parse_json_file(const char *filename);

/**
 * @brief 释放从app.json获取到的数据资源
 * @param[in] app_config：PEDK_APP_CONFIG_S 数据结构
 * @author:yangzikun
 * @return void
 */
void free_app_config(PEDK_APP_CONFIG_S *app_config);

/**
 * @brief 解析打印机信息
 * @param[in] printer_info： json格式的字符串
 * @param[in] app_config：PEDK_APP_CONFIG_S 数据结构
 * @author:yangzikun
 * @return void
 */
void parse_printer_info(cJSON *printer_info, PEDK_APP_CONFIG_S *app_config);

cJSON *parse_read_file(const char *filename);

#ifdef __cplusplus
}
#endif

#endif


