/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file license_auth_data_processing.h
 * @brief Header file for data processing in license authentication.
 * @details This header defines structures, constants, and function declarations for processing license data in the authentication system.
 * @addtogroup license_authentication
 * @{
 * <AUTHOR>
 * @date 2024-12-11
 */

#ifndef _LICENSE_AUTH_DATA_PROCESSING_H_
#define _LICENSE_AUTH_DATA_PROCESSING_H_

#include "license_auth_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @def LICENSE_AUTH_LICENSE_COUNT
 * @brief Maximum number of license data entries.
 */
#define LICENSE_AUTH_LICENSE_COUNT          32

/**
 * @def LICENSE_AUTH_LICENSE_BUF_LENGTH
 * @brief Buffer length for a single license data entry.
 */
#define LICENSE_AUTH_LICENSE_BUF_LENGTH     1024

/**
 * @struct LICENSE_DATA_S
 * @brief Structure representing a single license data entry.
 */
typedef struct {
    int32_t license_index;                                  /**< Index of the license. */
    char license_data[LICENSE_AUTH_LICENSE_BUF_LENGTH]; /**< License data. */
} LICENSE_DATA_S;

/**
 * @struct LICENSE_COLLECTION_S
 * @brief Structure representing a collection of license data entries.
 */
typedef struct {
    LICENSE_DATA_S licenses[LICENSE_AUTH_LICENSE_COUNT]; /**< Array of license data entries. */
} LICENSE_COLLECTION_S;

/**
 * @brief Retrieves an AES key string.
 * @details If the file exists, the key is read from the file; otherwise, the input string is returned.
 * @param[in] input_path Path to the input file.
 * @return Pointer to the retrieved or processed string (caller must free the memory).
 */
char *license_auth_get_string(const char *input_path);

/**
 * @brief Writes license public key data to NVRAM.
 * @param[in] index Index to store the license data.
 * @param[in] license_data License data string.
 * @param[in,out] license_collection Pointer to the license data collection structure.
 * @return 0 on success, negative value on failure.
 */
int32_t license_auth_lice_data_proc(int32_t index, const char *license_data, LICENSE_COLLECTION_S *license_collection);

/**
 * @brief Retrieves a string value from a cJSON object.
 * @details Extracts the value of the specified key from a cJSON object.
 * @param[in] json_item Pointer to the cJSON object.
 * @param[in] key Key name to retrieve the value for.
 * @return Pointer to the retrieved string on success, NULL on failure.
 */
char *license_auth_get_string_from_object(cJSON *json_item, const char *key);

/**
 * @brief Extracts key-value data from a cJSON object and writes it to a file.
 * @details The cJSON node can be deleted based on the mode parameter.
 * @param[in] json_item Pointer to the cJSON object.
 * @param[in] key Key name to extract the value for.
 * @param[in] mode 0 to keep the node, 1 to delete the node.
 * @param[out] output_file Path to the output file.
 * @return Pointer to the file path on success, NULL on failure.
 */
char *license_auth_get_item_file_from_object(cJSON *json_item, const char *key, int32_t mode, const char *output_file);

/**
 * @brief Processes a license string by adding a newline every 64 characters.
 * @param[in] input_str Input license string.
 * @param[out] output_str Output buffer to store the processed string.
 * @param[in] max_len Maximum length of the output buffer.
 * @return Length of the processed output string.
 */
int32_t license_auth_process_license_string(const char *input_str, char *output_str, size_t max_len);

#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_DATA_PROCESSING_H_ */
/**
 * @}
 */
