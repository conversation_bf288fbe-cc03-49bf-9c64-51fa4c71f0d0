//*****************************************************************************
//  Copyright (C) 2012 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/ftp/ftpclient.c#14 $
//  $Change: 244438 $ $Date: 2014/11/24 $
//
/// @file
/// FTP (File Transport Protocol) client
///
/// @ingroup Network
///
/// <AUTHOR> <<EMAIL>>
//
//*****************************************************************************
#include "nettypes.h"
#include "netsock.h"
#include "netmodules.h"
#include "netmisc.h"
#include "netsts.h"
#include "whitelist.h"

// 定义用户与内核交互私有幻数，当前幻数需根据实际项目情况定义
// 提供内核获取打印机网络白名单策略数据用
#define IOCTL_NET_WHITELIST_SYNC (SIOCDEVPRIVATE+14)
#define IOCTL_NET_WHITELIST_EN   (SIOCDEVPRIVATE+15)

// 白名单最大成员量
#define WHITELIST_NODE_LIMIT    999
#define WHITELIST_POLICY_IP     1 << 1
#define WHITELIST_POLICY_MAC    1 << 0

typedef struct net_whitelist_node
{
    char policy;        // eg:   ip | mac
    char ip[16];        // eg:   xxx.xxx.xxx.xxx
    char mac[18];       // eg:   XX:XX:XX:XX:XX:XX
}
WHITE_NODE_S;

typedef struct net_whitelist
{
    int32_t         enable;
    WHITE_NODE_S    nodes[WHITELIST_NODE_LIMIT];
}
WHITELIST_S;

static NET_CTX_S* s_whitelist_ctx = NULL;

static int32_t whitelist_notify_info(void* list)
{
    struct ifreq    ifreq;
    PI_SOCKET_T     sock;
    int32_t         ret = 0;

    sock = pi_socket(AF_INET, SOCK_DGRAM, 0);
    if ( sock < 0 )
    {
        NET_WARN("Net white list create socket failure !!!\n");
        return -1;
    }

    memset(&ifreq, 0, sizeof(struct ifreq));
    strcpy(ifreq.ifr_name, IFACE_ETH);
    ifreq.ifr_data = list;

    if ( ioctl(sock, IOCTL_NET_WHITELIST_SYNC, &ifreq)  == -1 )
    {
        NET_WARN("Net white list notify failure !!!\n");
        ret = -1;
    }
    else
    {
        NET_DEBUG("ioctl whitelist_notify_info ok !!!\n");
    }

    close(sock);
    return ret;
}

int32_t whitelist_update_enable_to_kernel(uint32_t enable)
{
    struct ifreq    ifreq;
    PI_SOCKET_T     sock;
    int32_t         ret = 0;
    uint32_t        whitelist_enable = enable;

    sock = pi_socket(AF_INET, SOCK_DGRAM, 0);
    if ( sock < 0 )
    {
        NET_WARN("Net white list create socket failure !!!\n");
        return -1;
    }

    memset(&ifreq, 0, sizeof(struct ifreq));
    strcpy(ifreq.ifr_name, IFACE_ETH);
    ifreq.ifr_data = (void *)&whitelist_enable;

    if ( ioctl(sock, IOCTL_NET_WHITELIST_EN, &ifreq)  == -1 )
    {
        NET_WARN("Net white enable notify fail !!!\n");
        ret = -1;
    }
    else
    {
        NET_DEBUG("ioctl whitelist_update_enable_to_kernel ok !!!\n");
    }

    close(sock);
    return ret;
}


int32_t whitelist_update_info_to_kernel(WHITELIST_ADDRBOOK_S* addrbook)
{
    WHITELIST_S whitelist;
    int32_t     i;
    int32_t     ret = 0;

    memset(&whitelist, 0, sizeof(WHITELIST_S));
    for( i = 0; i < WHITELIST_NODE_LIMIT; i++ )
    {
        whitelist.nodes[i].policy = 0x00;
        snprintf(whitelist.nodes[i].ip, sizeof(whitelist.nodes[i].ip), "%s", addrbook->info[i].ip);
        if ( whitelist.nodes[i].ip[0] != '\0' )
        {
            whitelist.nodes[i].policy |= WHITELIST_POLICY_IP;
        }
        snprintf(whitelist.nodes[i].mac, sizeof(whitelist.nodes[i].mac), "%s", addrbook->info[i].mac);
        if ( whitelist.nodes[i].mac[0] != '\0' )
        {
            whitelist.nodes[i].policy |= WHITELIST_POLICY_MAC;
        }
    }
    ret = whitelist_notify_info((void*)&(whitelist.nodes));
    return ret;
}

int32_t whitelist_check_ipv4_is_exist(char* ipv4_src)
{
    WHITELIST_ADDRBOOK_S    addrbook;
    int32_t     i;
    RETURN_VAL_IF(netdata_get_whitelist_switch(s_whitelist_ctx->data_mgr) == 0, NET_NONE, 0);
    RETURN_VAL_IF(netdata_get_whitelist_addrbook(s_whitelist_ctx->data_mgr, &addrbook, sizeof(addrbook)) < 0, NET_WARN, -1);
    for( i = 0; i < WHITELIST_NODE_LIMIT; i++ )
    {
        RETURN_VAL_IF(addrbook.info[i].ip[0] != '\0' && !strncmp(addrbook.info[i].ip, ipv4_src, strlen(addrbook.info[i].ip)), NET_DEBUG, 1);
	}
	return 0;
}

int32_t whitelist_init(NET_CTX_S* net_ctx)
{
    uint32_t                enable;
    WHITELIST_ADDRBOOK_S    addrbook;
    s_whitelist_ctx = net_ctx;

    enable = netdata_get_whitelist_switch(net_ctx->data_mgr);
    RETURN_VAL_IF(netdata_get_whitelist_addrbook(net_ctx->data_mgr, &addrbook, sizeof(addrbook)) < 0, NET_WARN, -1);
    RETURN_VAL_IF(whitelist_update_info_to_kernel(&addrbook) < 0, NET_WARN, -1);
    RETURN_VAL_IF(whitelist_update_enable_to_kernel(enable) < 0, NET_WARN, -1);
    NET_DEBUG("whitelist init ok !!!\n");
    return 0;
}


