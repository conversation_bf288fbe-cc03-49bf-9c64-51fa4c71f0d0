/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file llmnr.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Link-Location-Multicast-Name-Resolution
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netmisc.h"
#include "netsock.h"
#if CONFIG_NET_WHITELIST
#include "whitelist.h"
#endif

#define LLMNR_QR_MASK           0x8000  /* top bit means request or response (0 = request; 1 = response) */
#define LLMNR_OP_MASK           0x7800  /* the packet type is 4 bits in the opcode field. */
#define LLMNR_RCODE_MASK        0x000F  /* Response code */

#define LLMNR_PKT_REG           0x2800  /* 5 registration */
#define LLMNR_PKT_REL           0x3000  /* 6 release */
#define LLMNR_PKT_WACK          0x3800  /* 7 WACK */
#define LLMNR_PKT_REFRESH       0x4800  /* 8 refresh */

#define LLMNR_RECUR_REQ         0x0100  /* recursion requested. */
#define LLMNR_AUTH_ANSWER       0x0400  /* Authoritative Answer */
#define LLMNR_BROADCAST         0x0010  /* broadcast packet */

/* response codes */
#define LLMNR_RCODE_SUCCESS     0x0000
#define LLMNR_RCODE_FMT_ERR     0x0001  /* Format error - Bad format on request */
#define LLMNR_RCODE_SRV_ERR     0x0002  /* Server failure - Can't process */
#define LLMNR_RCODE_NAM_ERR     0x0003  /* Name Error - The name doesn't exist (only on queries) */
#define LLMNR_RCODE_IMP_ERR     0x0004  /* Unsupported Request error */
#define LLMNR_RCODE_RFS_ERR     0x0005  /* Refused Error - Server refuses to complete the request */
#define LLMNR_RCODE_ACT_ERR     0x0006  /* Active Error - Name is active on another node. */
#define LLMNR_RCODE_CFT_ERR     0x0007  /* Conflict Error - Name is in conflict */

#define LLMNR_TYPE_NS           0x0020
#define LLMNR_TYPE_NONE         0x000A
#define LLMNR_CLASS             0x0001
#define LLMNR_NAME_PTR          0xc00c  /* hardcode since always in the same spot in the packet. */
#define LLMNR_NB_FLAGS          0x2000  /* p node */

#define LLMNR_IPV4_MCAST_ADDR   "***********"
#define LLMNR_IPV6_MCAST_ADDR   "FF02::1:3"

#define LLMNR_MLOCK_UN()        { if (s_llmnr_ctx != NULL) pi_mutex_unlock(s_llmnr_ctx->mutex); }
#define LLMNR_MLOCK_EX()        { if (s_llmnr_ctx != NULL) pi_mutex_lock(s_llmnr_ctx->mutex);   }

#define LLMNR_BUFFER_SIZE       ( 0x4000 ) /* 16 * 1024 */

struct llmrn_header
{
    uint16_t tran_id;
    uint16_t op_code;
    uint16_t num_queries;
    uint16_t num_answers;
    uint16_t num_authority;
    uint16_t num_additional;
}
#ifdef _MSC_VER
#pragma pack(1)
#else
__attribute__((__packed__))
#endif
;

struct llmnr_queries_name
{
    uint8_t  len;       // always 32 or this won't work :-)
    char     name[33];  // for some reason we say the length is 32 but put in 32 plus a null
}
#ifdef _MSC_VER
#pragma pack(1)
#else
__attribute__((__packed__))
#endif
;

struct llmnr_queries_attr
{
    uint16_t type;
    uint16_t class;
}
#ifdef _MSC_VER
#pragma pack(1)
#else
__attribute__((__packed__))
#endif
;

struct llmnr_answers_name
{
    uint8_t len; // always 32 or this won't work :-)
    char  name[33]; // for some reason we say the length is 32 but put in 32 plus a null
}
#ifdef _MSC_VER
#pragma pack(1)
#else
__attribute__((__packed__))
#endif
;

struct llmnr_answers_attr
{
    uint16_t type;
    uint16_t class;
    uint32_t ttl;
    uint16_t rlen;
    //uint32_t addr;
}
#ifdef _MSC_VER
#pragma pack(1)
#else
__attribute__((__packed__))
#endif
;

typedef struct llmrn_header             LLMNR_HEADER_S;
typedef struct llmnr_queries_name       LLMNR_QUERIES_NAME_S;
typedef struct llmnr_queries_attr       LLMNR_QUERIES_ATTR_S;
typedef struct llmnr_answers_name       LLMNR_ANSWERS_NAME_S;
typedef struct llmnr_answers_attr       LLMNR_ANSWERS_ATTR_S;

typedef struct llmnr_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     thread;
    PI_MUTEX_T      mutex;
    PI_SOCKET_T     sockfd[IPVER_NUM][IFACE_ID_NUM];
    char            ipstr[IPVER_NUM][IFACE_ID_NUM][IPV6_ADDR_LEN];
    uint8_t         changed;
    uint8_t         inited;
}
LLMNR_CTX_S;

static LLMNR_CTX_S* s_llmnr_ctx = NULL;

/**
 * @brief       The function of processing llmnr request.
 * @param[in]   buf     : The LLMNR request data received buff.
 * @param[in]   nbuf    : LLMNR requests the length of the data received.
 * @param[in]   from    : The LLMNR request information source structure.
 * @param[in]   ifid    : Network link type.
 * @param[in]   ipver   : IPV4 or IPV6.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static void llmnr_process_request(char* buf, size_t nbuf, struct sockaddr_storage* from, IFACE_ID_E ifid, IP_VERSION_E ipver)
{
    LLMNR_HEADER_S* header = (LLMNR_HEADER_S *)buf;
    LLMNR_QUERIES_NAME_S* queries_name = NULL;
    LLMNR_QUERIES_ATTR_S* queries_attr = NULL;
    LLMNR_ANSWERS_NAME_S* answers_name = NULL;
    LLMNR_ANSWERS_ATTR_S* answers_attr = NULL;
    char*    ptr = buf;
    void*    src = NULL;
    char     from_addr[IPV6_ADDR_LEN];
    char     hostname[HOSTNAME_LEN];
    size_t   len;
    int32_t  ret;
    uint16_t queries_type;
#if CONFIG_NET_WHITELIST
    int32_t  whitelist_check_res = 0;
    char     ipaddr_str[IPV6_ADDR_LEN];
#endif

    if ( nbuf < sizeof(LLMNR_HEADER_S) || ((LLMNR_QR_MASK | LLMNR_OP_MASK) & ntohs(header->op_code)) != 0 )
    {
        return;
    }

#if 0 /* 不过滤广播，让局域网内机器可以通过广播找到本机 */
    if ( ntohs(header->op_code) & LLMNR_BROADCAST )
    {
        return;
    }
#endif

    gethostname(hostname, sizeof(hostname));
    /* move ptr to LLMNR_QUERIES_NAME_S */
    ptr += sizeof(LLMNR_HEADER_S);
    queries_name = (LLMNR_QUERIES_NAME_S *)ptr;
    if ( strcasecmp(hostname, queries_name->name) != 0 )
    {
        return; /* 查询的主机名不匹配，忽略 */
    }

    header->op_code |= htons(LLMNR_QR_MASK); /* set the response bit. */

    header->num_queries = htons(1);
    header->num_answers = htons(1);

    /* move ptr to LLMNR_QUERIES_ATTR_S */
    ptr += (sizeof(queries_name->len) + strlen(queries_name->name) + 1);
    queries_attr = (LLMNR_QUERIES_ATTR_S *)ptr;
    queries_type = ntohs(queries_attr->type);

    /* move ptr to LLMNR_ANSWERS_NAME_S */
    ptr += sizeof(LLMNR_QUERIES_ATTR_S); /* LLMNR_ANSWER */
    answers_name = (LLMNR_ANSWERS_NAME_S *)ptr;
    answers_name->len  = (uint8_t)strlen(hostname);
    snprintf(answers_name->name, sizeof(answers_name->name), "%s", hostname);

    /* move ptr to LLMNR_ANSWERS_ATTR_S */
    ptr += (sizeof(answers_name->len) + strlen(answers_name->name) + 1);
    answers_attr = (LLMNR_ANSWERS_ATTR_S *)ptr;
    answers_attr->type  = queries_attr->type;
    answers_attr->class = htons(1);
    answers_attr->ttl   = htonl(30);

#if CONFIG_NET_WHITELIST
    // 新增网络安全策略:网络白名单启用后，如策略绑定了IPV4地址，则仅返回IPV4地址信息
    void *srcaddr;
    if( ((struct sockaddr *)from)->sa_family == AF_INET )
    {
        srcaddr = (void *)&((struct sockaddr_in *)from)->sin_addr;
    }
    else
    {
        srcaddr = (void *)&((struct sockaddr_in6 *)from)->sin6_addr;
    }

    // IPV4地址检查白名单策略
    if ( srcaddr )
    {
        inet_ntop(((struct sockaddr *)from)->sa_family, srcaddr, ipaddr_str, sizeof(ipaddr_str));
        whitelist_check_res = whitelist_check_ipv4_is_exist(ipaddr_str);
    }
#endif
    /* move ptr to Answer address */
    ptr += sizeof(LLMNR_ANSWERS_ATTR_S);
    if ( queries_type == 1 )
    {
        answers_attr->rlen = htons(sizeof(struct in_addr));
        len = (size_t)(ptr - buf + sizeof(struct in_addr));
        inet_pton(AF_INET, s_llmnr_ctx->ipstr[ipver][ifid], ptr);
    }
    else if ( queries_type == 28 )
    {
#if CONFIG_NET_WHITELIST
        // 网络白名单新增策略，如策略绑定了IPV4地址，不再回复IPV6地址信息
        if ( whitelist_check_res )
        {
            return;
        }
#endif
        answers_attr->rlen = htons(sizeof(struct in6_addr));
        len = (size_t)(ptr - buf + sizeof(struct in6_addr));
        inet_pton(AF_INET6, s_llmnr_ctx->ipstr[ipver][ifid], ptr);
    }
    else
    {
        NET_WARN("invalid queries type(%u)", queries_type);
        return;
    }

    src = (((struct sockaddr *)from)->sa_family == AF_INET ? ((void *)&((struct sockaddr_in *)from)->sin_addr) : ((void *)&((struct sockaddr_in6 *)from)->sin6_addr));
    inet_ntop(((struct sockaddr *)from)->sa_family, src, from_addr, sizeof(from_addr));
    //NET_DEBUG("LLMNR send '%s' to '%s' by %s.%s", s_llmnr_ctx->ipstr[ipver][ifid], from_addr, IFACE_NAME(ifid), IPVER_NAME(ipver));

    ret = sendto(s_llmnr_ctx->sockfd[ipver][ifid], buf, len, 0, (struct sockaddr *)from, (socklen_t)sizeof(*from));
    if ( ret < 0 )
    {
        NET_WARN("LLMNR send '%s' to '%s' by %s.%s failed: %d<%s>", s_llmnr_ctx->ipstr[ipver][ifid], from_addr, IFACE_NAME(ifid), IPVER_NAME(ipver), errno, strerror(errno));
    }

    return;
}

/**
 * @brief       The function of determine whether you need to update the llmnr connection status.
 * @param[in]   changed : It needs to be updated.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static void llmnr_update_link_status(uint8_t changed)
{
    LLMNR_MLOCK_EX();
    s_llmnr_ctx->changed |= changed;
    LLMNR_MLOCK_UN();
}

/**
 * @brief       The callback function of update the callback of the llmnr protocol connection state
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static void llmnr_update_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(s_llmnr_ctx == NULL || s_llmnr_ctx->inited == 0, NET_WARN);
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    llmnr_update_link_status((uint8_t)(s->subject_status & 0xFF));
}

/**
 * @brief       The function of reload llmnr protocol socket.
 * @param[in]   readfds : Read socket.
 * @return      Reassigned read socket
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static int32_t llmnr_reload_socket(fd_set* readfds)
{
    const char*     mcast_addr[IPVER_NUM] = { LLMNR_IPV4_MCAST_ADDR , LLMNR_IPV6_MCAST_ADDR };
    const char*     ifname = NULL;
    int32_t         maxfd = 0;
    uint8_t         link_base = LINK_CHANGE_IPV4_BASE;
    uint8_t         changed;

    LLMNR_MLOCK_EX();
    changed = s_llmnr_ctx->changed;
    s_llmnr_ctx->changed = 0;
    LLMNR_MLOCK_UN();

    FD_ZERO(readfds);
    for ( IP_VERSION_E ipver = IPV4; ipver < IPVER_NUM; ++ipver )
    {
        for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
        {
            ifname = IFACE_NAME(ifid);
            if ( STRING_IS_EMPTY(ifname) )
            {
                continue;
            }

#ifdef CONFIG_NET_WIFI
            if ( ipver == IPV6 && ifid == IFACE_ID_WFD )
            {
                continue;
            }
#endif
            /* 当前链路状态有变更，重载socket */
            if ( (link_base << ifid) & changed )
            {
                NET_DEBUG("'%s.%s' has changed, socket reload", ifname, IPVER_NAME(ipver));
                if ( s_llmnr_ctx->sockfd[ipver][ifid] != INVALID_SOCKET )
                {
                    netsock_leave_multicast_group(s_llmnr_ctx->sockfd[ipver][ifid], ifid, ipver, mcast_addr[ipver]);
                    pi_closesock(s_llmnr_ctx->sockfd[ipver][ifid]);
                    s_llmnr_ctx->sockfd[ipver][ifid] = INVALID_SOCKET;
                }

                if ( ipver == IPV6 )
                {
                    netdata_get_ipv6_link(DATA_MGR_OF(s_llmnr_ctx), ifid, s_llmnr_ctx->ipstr[ipver][ifid], sizeof(s_llmnr_ctx->ipstr[ipver][ifid]));
                }
                else
                {
                    netdata_get_ipv4_addr(DATA_MGR_OF(s_llmnr_ctx), ifid, s_llmnr_ctx->ipstr[ipver][ifid], sizeof(s_llmnr_ctx->ipstr[ipver][ifid]));
                }

                if ( netdata_get_iface_running(DATA_MGR_OF(s_llmnr_ctx), ifid) == 0 || STRING_IS_EMPTY(s_llmnr_ctx->ipstr[ipver][ifid]) )
                {
                    NET_DEBUG("'%s.%s' is skipped", ifname, IPVER_NAME(ipver));
                    continue;
                }

                NET_DEBUG("load '%s' address '%s' to LLMNR multicast group", ifname, s_llmnr_ctx->ipstr[ipver][ifid]);
                s_llmnr_ctx->sockfd[ipver][ifid] = netsock_create_multicast(LLMNR_PORT, ifid, ipver);
                if ( s_llmnr_ctx->sockfd[ipver][ifid] == INVALID_SOCKET )
                {
                    continue;
                }

                if ( netsock_join_multicast_group(s_llmnr_ctx->sockfd[ipver][ifid], ifid, ipver, s_llmnr_ctx->ipstr[ipver][ifid], mcast_addr[ipver]) != 0 )
                {
                    pi_closesock(s_llmnr_ctx->sockfd[ipver][ifid]);
                    s_llmnr_ctx->sockfd[ipver][ifid] = INVALID_SOCKET;
                    continue;
                }
            }

            if ( s_llmnr_ctx->sockfd[ipver][ifid] != INVALID_SOCKET )
            {
                FD_SET(s_llmnr_ctx->sockfd[ipver][ifid], readfds);
                if ( maxfd < s_llmnr_ctx->sockfd[ipver][ifid] )
                {
                    maxfd = s_llmnr_ctx->sockfd[ipver][ifid];
                }
            }
        }
        link_base = (link_base << IFACE_ID_NUM);
    }

    return maxfd;
}

/**
 * @brief       The llmnr handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static void* llmnr_thread_handler(void* arg)
{
    IP_VERSION_E            ipver;
    IFACE_ID_E              ifid;
    fd_set                  rfds;
    socklen_t               slen;
    struct sockaddr_storage from;
    struct timeval          tv;
    char                    buf[LLMNR_BUFFER_SIZE];
    int32_t                 maxfd;
    ssize_t                 rlen;
    int32_t                 ret;

    NET_DEBUG("Starting LLMNR thread handler");

    while ( 1 )
    {
        if ( (maxfd = llmnr_reload_socket(&rfds)) <= 0 )
        {
            pi_msleep(2000);
            continue;
        }

        tv.tv_sec  = 2;
        tv.tv_usec = 0;
        ret = select(maxfd + 1, &rfds, NULL, NULL, &tv);
        if ( ret < 0 )
        {
            NET_WARN("select failed: %d<%s>", errno, strerror(errno));
            llmnr_update_link_status(LINK_CHANGE_ALL);
            continue;
        }
        else if ( ret == 0 )
        {
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ipver++ )
        {
            for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ifid++ )
            {
                if ( s_llmnr_ctx->sockfd[ipver][ifid] != INVALID_SOCKET && FD_ISSET(s_llmnr_ctx->sockfd[ipver][ifid], &rfds) )
                {
                    memset(buf, 0, sizeof(buf));
                    slen = (socklen_t)sizeof(from);
                    rlen = recvfrom(s_llmnr_ctx->sockfd[ipver][ifid], buf, sizeof(buf), 0, (struct sockaddr *)&from, &slen);
                    if ( rlen > 0 )
                    {
                        llmnr_process_request(buf, rlen, &from, ifid, ipver);
                    }
                    else
                    {
                        NET_WARN("recvfrom %s.%s failed(%d): %d<%s>", IFACE_NAME(ifid), IPVER_NAME(ipver), rlen, errno, strerror(errno));
                        llmnr_update_link_status(LINK_CHANGE_ALL);
                    }
                }
            }
        }
    }

    return NULL;
}

int32_t llmnr_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_llmnr_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_llmnr_ctx = (LLMNR_CTX_S *)pi_zalloc(sizeof(LLMNR_CTX_S));
    RETURN_VAL_IF(s_llmnr_ctx == NULL, NET_WARN, -1);

    do
    {
        memset(s_llmnr_ctx->sockfd, INVALID_SOCKET, sizeof(s_llmnr_ctx->sockfd)); /* initial value is INVALID_SOCKET(-1) */
        s_llmnr_ctx->changed = LINK_CHANGE_ALL;
        s_llmnr_ctx->net_ctx = net_ctx;

        BREAK_IF((s_llmnr_ctx->mutex = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        s_llmnr_ctx->thread = pi_thread_create(llmnr_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "llmnr_thread_handler");
        BREAK_IF(s_llmnr_ctx->thread == INVALIDTHREAD, NET_WARN);

        s_llmnr_ctx->inited = 1;
        ret = 0;
    }
    while ( 0 );

    NET_INFO("LLMNR initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netlink_observer(net_ctx, llmnr_update_callback, NULL);
    }
    else
    {
        llmnr_epilog();
    }
    return ret;
}

void llmnr_epilog(void)
{
    if ( s_llmnr_ctx != NULL )
    {
        s_llmnr_ctx->inited = 0;
        if ( s_llmnr_ctx->thread != INVALIDTHREAD )
        {
            pi_thread_destroy(s_llmnr_ctx->thread);
        }
        if ( s_llmnr_ctx->mutex != INVALIDMTX )
        {
            pi_mutex_destroy(s_llmnr_ctx->mutex);
        }
        pi_free(s_llmnr_ctx);
        s_llmnr_ctx = NULL;
    }
}
/**
 *@}
 */
