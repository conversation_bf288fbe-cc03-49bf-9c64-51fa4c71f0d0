// ******************************************************************************
// Copyright (c) 2009-2014  Seine Technology. All Rights Reserved
//
//                        Seine Technology Confidential
// ******************************************************************************
// Function Description
// ------------------------------------------------------------------------------
// bmpfile.h
// ******************************************************************************
// Revising Records
// ------------------------------------------------------------------------------
// Author: ArvinYq
// Date: 15/04/2021
// Reason:create file
// ******************************************************************************
#ifndef _PRINT_PARSER_INTERNAL_PAGE_STORE_H_
#define _PRINT_PARSER_INTERNAL_PAGE_STORE_H_

#include <stdio.h>
#include "public_data_proc.h"

#define PDF_PATH_BUF 128

typedef enum
{
    NOT_SAVE = 0,
    PRI_SAVE,
    FTP_SAVE,
    SMB_SAVE,
    INVALID_SAVE_PDF_MODE
}SAVE_PDF_MODE_S,*SAVE_PDF_MODE_P;

typedef enum
{
    FILE_GENERATE_SUCCESS = 0,
    FILE_GENERATE_FAIL
}FILE_STORE_RESULT_S,*FILE_STORE_RESULT_P;

typedef enum
{
    ePRINT_PARSER_INTERNAL_PAGE_STORE_DEMO = 0,                             ///< demo page
    ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO,                         ///< printer info page
    ePRINT_PARSER_INTERNAL_PAGE_STORE_MENU_STRUCT,                          ///< menu struct page
    ePRINT_PARSER_INTERNAL_PAGE_STORE_QUALITY_TEST,                         ///< print quality page
    ePRINT_PARSER_INTERNAL_PAGE_STORE_NET_CONFIG,                           ///< net config page
    ePRINT_PARSER_INTERNAL_PAGE_STORE_EVENT_LOG,                            ///< event log page
    ePRINT_PARSER_INTERNAL_PAGE_STORE_WIFI_WIZARD,                          ///< wifi wizard
    ePRINT_PARSER_INTERNAL_PAGE_STORE_INVALID = 0xFFFFFFFF                  ///< invalid page type
} PRINT_PARSER_INTERNAL_PAGE_STORE_E;                                       ///< the type of internal page supported by the print module */


typedef struct{
    int                         fd_pdf;
    SAVE_PDF_MODE_S             sav_mode;
    char *                      file_path;

    unsigned char*              image_buff;
    int                         image_buff_len;
    void                        *resource_data;

}PDF_DATA_INFO_S,*PDF_DATA_INFO_P;

int internal_page_store_prolog( void );
int internal_page_store_set_file_path( const char* str);
void internal_page_store_set_cancel_flag( const bool flag);


#endif
