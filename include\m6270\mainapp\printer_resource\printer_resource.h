/**
 * @copyright 2023 Shenzhen Pantum Technology Co.Ltd all rights reserved
 * @file data_conversion.c
 * @addtogroup data conversion module
 * @{
 * @addtogroup data_conversion_module
 * <AUTHOR>
 * @date 2023-05-05
 * @version v0.2
 * @brief data conversion module
 */


#ifndef PRINTER_RESOURCE_H
#define PRINTER_RESOURCE_H


/*** Head file area ***************************************************/
#include "ctimer.h"
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_mgr.h"
/**********************************************************************/


/*** Macro description area *******************************************/
#define PRINTER_DELAY                            10

#define PRINTER_FINISHER_NONE                    3
#define PRINTER_FINISHER_STAPLE                  4
#define PRINTER_FINISHER_PUNCH                   5
#define PRINTER_FINISHER_FOLD                    10
#define PRINTER_FINISHER_SADDLE_STICH            8
#define PRINTER_FINISHER_STAPLE_BOTTOM_LEFT      20
#define PRINTER_FINISHER_STAPLE_BOTTOM_RIGHT     21
#define PRINTER_FINISHER_STAPLE_TOP_LEFT         22
#define PRINTER_FINISHER_STAPLE_TOP_RIGHT        23
#define PRINTER_FINISHER_BIND_LEFT               28
#define PRINTER_FINISHER_BIND_TOP                29
#define PRINTER_FINISHER_BIND_RIGHT              30
#define PRINTER_FINISHER_BIND_BOTTOM             31
#define PRINTER_FINISHER_PUNCH_DUAL_LEFT         74
#define PRINTER_FINISHER_PUNCH_DUAL_TOP          75
#define PRINTER_FINISHER_PUNCH_DUAL_RIGHT        76
#define PRINTER_FINISHER_PUNCH_DUAL_BOTTOM       77
#define PRINTER_FINISHER_PUNCH_QUAL_LEFT         82
#define PRINTER_FINISHER_PUNCH_QUAL_TOP          83
#define PRINTER_FINISHER_PUNCH_QUAL_RIGHT        84
#define PRINTER_FINISHER_PUNCH_QUAL_BOTTOM       85
#define PRINTER_FINISHER_FOLD_HALF               93
#define PRINTER_FINISHER_FOLD_LETTER             96
#define PRINTER_FINISHER_FOLD_Z                  100

/**********************************************************************/


/*** Enumerate description area ***************************************/
// ...
/**********************************************************************/


/*** Variable description area ****************************************/

typedef struct
{
    uint8_t                 none;
    uint8_t                 bind_mode;
    uint8_t                 punch_mode;
    uint8_t                 fold_mode;
    uint8_t                 shift_mode;
    uint8_t                 bind_top_left;
    uint8_t                 bind_top_right;
    uint8_t                 bind_bottom_rigth;
    uint8_t                 bind_bottom_left;
    uint8_t                 bind_two_left;
    uint8_t                 bind_two_right;
    uint8_t                 bind_two_top;
    uint8_t                 bind_two_bottom;
    uint8_t                 punch_top_two;
    uint8_t                 punch_top_four;
    uint8_t                 punch_bottom_two;
    uint8_t                 punch_bottom_four;
    uint8_t                 punch_left_two;
    uint8_t                 punch_left_four;
    uint8_t                 punch_right_two;
    uint8_t                 punch_right_four;
    uint8_t                 fold_2;
    uint8_t                 fold_3;
    uint8_t                 fold_z;
    uint8_t                 fold_middle_2_bind;
}PRINTER_RESOURCE_FIINISHER_ATTRYBUTE_S;

/**
 * @brief print timer struct
 */
typedef struct
{
    struct ctimer  suspend_timer;             ///< timer handler
    struct timeval timout;                    ///< timeout param
}PRINTER_RESOURCE_TIMER_CTL_S,*PRINTER_RESOURCE_TIMER_CTL_P;

/**
 * @brief print repert timer struct
 */
typedef struct
{
    struct ctimer  repeat_timer;              ///< timer handler
    struct timeval repeat_timout;             ///< timeout param
    int32_t repeat_times;                     ///< times record
}PRINTER_RESOURCE_REPEAT_TIMER_S, *PRINTER_RESOURCE_REPEAT_TIMER_P;


/**********************************************************************/


/*** Function description area *****************************************/

/**
 * @brief       get finisher attribute
 * @param[in]   void
 * @return      N/A
 * @retval      N/A
 * <AUTHOR>
 * @date        2024-07-17
 * @note        N/A
*/
PRINTER_RESOURCE_FIINISHER_ATTRYBUTE_S *printer_resource_get_finisher_attribute_msg(void);


/**
 * @brief       set finisher attribute
 * @param[in]   const PRINTER_RESOURCE_FIINISHER_ATTRYBUTE_S *finisher_attribute
 * @return      N/A
 * @retval      N/A
 * <AUTHOR>
 * @date        2024-07-17
 * @note        N/A
*/
void printer_resource_set_finisher_attribute_msg(const PRINTER_RESOURCE_FIINISHER_ATTRYBUTE_S *finisher_attribute);


/**
 * @brief       reverse map paper size attritube
 * @param[in]   int paper_size
 * @return      N/A
 * @retval      N/A
 * <AUTHOR>
 * @date        2024-07-17
 * @note        N/A
*/
void printer_resource_reverse_mapping_paper_size(  int paper_size);

/**
 * @brief       print resource init
 * @param[in]   N/A
 * @return      signed char
 * @retval      execute result
 * <AUTHOR>
 * @date        2024-07-17
 * @note        N/A
*/
signed char printer_resource_init( void );

/**
 * @brief       printer resource fun
 * @param[in]   N/A
 * @return      signed char
 * @retval      execute result
 * <AUTHOR>
 * @date        2024-07-17
 * @note        N/A
*/
void printer_resource_func();

/**
 * @brief       printer_resource_update_map_attritube
 * @param[in]   N/A
 * @return      signed char
 * @retval      execute result
 * <AUTHOR>
 * @date        2024-07-17
 * @note        N/A
*/
signed char printer_resource_update_map_attritube( void* val,int length , int  event_type);


#endif

/**
 *@}
 */
