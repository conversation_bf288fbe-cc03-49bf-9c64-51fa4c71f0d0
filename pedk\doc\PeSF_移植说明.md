# PeSF 项目移植说明

- [PeSF 项目移植说明](#pesf-项目移植说明)
  - [概述](#概述)
  - [PeSF 项目目录](#pesf-项目目录)
  - [移植步骤](#移植步骤)
    - [准备工作](#准备工作)
      - [下载第三方源码库](#下载第三方源码库)
        - [下载 QuickJS 源码](#下载-quickjs-源码)
      - [获取目标平台的编译工具链和依赖库](#获取目标平台的编译工具链和依赖库)
        - [编译工具链](#编译工具链)
        - [依赖库](#依赖库)
    - [配置编译环境](#配置编译环境)
      - [配置 QuickJS 编译环境](#配置-quickjs-编译环境)
    - [设置嵌入式接口](#设置嵌入式接口)
    - [编译和链接](#编译和链接)
    - [安装到固件](#安装到固件)

## 概述

本移植说明用于指导 PeSF 项目的移植

## PeSF 项目目录

```shell
├── assert
└── src
```

| 目录   | 作用                         |
| ------ | ---------------------------- |
| assert | 用于存放 PeSF 的 js 文件     |
| src    | PeSF 项目中参与编译的 C 代码 |

## 移植步骤

### 准备工作

#### 下载第三方源码库

##### 下载 QuickJS 源码

到 QuickJS 官方网站获得最新的 QuickJS 文档版源码。

QuickJS 官方网站: <https://bellard.org/quickjs/>

最新源码是 `quickjs-2021-03-27.tar.xz`

> 2023 项目使用的 buildroot 编译系统有 QuickJS 的包，但是要求版本是 2020-11-08，所以在 2023 等 buildroot 编译系统的项目上会使用 2020-11-08 版本

#### 获取目标平台的编译工具链和依赖库

##### 编译工具链

根据目标平台获取合适的编译工具链

##### 依赖库

请确保目标平台编译系统有下面列出的依赖库。

| 依赖库  | 描述           |
| ------- | -------------- |
| pthread | 线程库         |
| math    | 数学库         |
| dl      | 动态链接加载库 |

### 配置编译环境

#### 配置 QuickJS 编译环境

QuickJS 提供了一个 Makefile，用于在 Linux 或 MacOS / X 上编译 QuickJS 引擎。
根据目标平台修改编译系统，将 QuickJS 加入编译流程，生成目标平台的 libquickjs.a。

> 2023 项目使用 buildroot 编译系统

### 设置嵌入式接口

1. 在 PeSF 项目的 Makefile 、CMake 或者其他编译配置中引入 QuickJS 的头文件的目录和生成的 libquickjs.a 静态库文件。
2. 编写嵌入式接口代码，创建和配置 QuickJS 运行时。
   > 可以参考 2023 项目仓库中 feature/quickjs 分支的做法配置创建和配置 QuickJS 运行时。
3. 注册需要 bind 到 Javascript 环境的 C 函数到 QuickJS 的运行时。
   > 2023 项目仓库中 feature/quickjs 分支的做法仅仅是为了验证 quickjs 调用 c 函数在屏幕上描述 UI 绘制的可行性，仅供参考。

### 编译和链接

- 使用目标平台的编译工具链编译项目代码。
- 将 QuickJS 的库文件链接到项目中。

### 安装到固件

1. 根据目标平台修改编译系统，将 PeSF 项目的编译产物安装到固件的 romfs 中
2. 根据目标平台修改编译系统, 将 PeSF 项目的 `assert` 目录安装到固件的 romfs 中
