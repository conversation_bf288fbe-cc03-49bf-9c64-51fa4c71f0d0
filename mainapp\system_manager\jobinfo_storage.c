/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file jobinfo_storage.c
 * @addtogroup system_manager
 * @{
 * @brief Job information storage. 
 * <AUTHOR> 
 * @version 1.0
 * @date 2024-05-28
 */

#include <sys/time.h>
#include <pthread.h>
#include <errno.h>
#include <unistd.h>
#include "jobinfo_storage.h"
#include "job_assistance.h"
#include "ctimer.h"
#include "ulog.h"
#include "sql.h"


/*
 *
 * - dba数据库
 *   1. 至少存放一张表(jobinfo)，jobinfo表维护struct job_cache_info内容
 *   2. 根据作业id的大小决定存放哪张TXXX表，其中作业id不超过该表的编号，如:jobid为4存放T8192表，jobid为8193存放T16384表
 *   3. TXXXX表存放struct JOBINFO_S内容
 *  
 * - dbb数据库
 *   1. delay表，存放struct job_task内容
 *   2. sample表，存放struct job_task内容
 *   3. pincode表，存放struct job_task内容
 *
 */

#define TABLE_STEP (8192U)
#define JOBINFO_TABLE "jobinfo"
#define DELAY_TABLE "delay"
#define SAMPLE_TABLE "sample"
#define PINCODE_TABLE "pincode"
#define SQL_PREFIX_PATH "/emmcimage/sysjob/"

struct job_task
{
    time_t delay;
    JOBINFO_S jobinfo;
};

struct job_cache_info
{
    unsigned int running_jobid;///<记录作业发起时的jobid
    unsigned int start_jobid;///<每次开机后，作业起始id
    unsigned int start_tableid;///<每次开机后，存储作业的起始表id
};

struct jobinfo_storage_manager
{
   pthread_mutex_t mtx;
   struct ctimer timer; 
   struct channels chs[T_MAX];
   struct job_cache_info jci;
   EVT_MGR_CLI_S *client;
   void *dbA;
   void *dbB;
};

struct table_item_context
{
    void *context_by_caller;
    int(*callback_by_caller)(const JOBINFO_S* job , void *context, uint32_t size);
};

enum JOB_REMOVE_RESULT
{
    R_SUCCESS = 0,
    R_INVALID_JOB ,
    R_FILE_INEXISTENCE
};

static struct jobinfo_storage_manager jsm;

static void* alloc_table(void *data)
{
    const char *str = (const char*)data;
    
    if (str)
    {
        return (void*)strdup(str);
    }
    return NULL;
}

static void free_table(void *data)
{
    if (data)
    {
        free(data);
    }
}

static void store_table(void *context , const unsigned char *table)
{
    struct channels *chs = (struct channels*)context;

    if (chs && table && *table == 'T')//存放作业信息的表是以字母T开头的*/
    {
        channels_head_insert(chs , 1 , (void*)table , 1);
    }
}

static int table_item_handle(const void *context , const void *data , unsigned int size)
{
    (void)size;
    struct table_item_context *tic = (struct table_item_context*)context;

    if (tic->callback_by_caller)
    {
        return tic->callback_by_caller(data , tic->context_by_caller, size);
    }
    return 0;
}

static void* alloc_jobtask(void *data)
{
    struct job_task *jt = (struct job_task*)data;
    struct job_task *n = (struct job_task*)malloc(sizeof(struct job_task));

    if (n)
    {
        n->delay = jt->delay;
        memcpy(&n->jobinfo , &jt->jobinfo , sizeof(jt->jobinfo));
    }
    return n;
}

static void free_jobtask(void *data)
{
    struct job_task *jt = (struct job_task*)data;

    if (jt)
    {
        free(jt);
    }
}

static unsigned int _get_tableid(unsigned int jobid)
{
    unsigned int tableid = jsm.jci.start_tableid;

    while(jobid > tableid)
    {
        tableid += TABLE_STEP;
    }
    return tableid;
}

static int _transfer_job_request(struct job_task *jt)
{
    ULOG_INFO(SYS_JOB_LOG , "Start delay job[%u] %s" , jt->jobinfo.job_id , ctime(&jt->delay));

    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_JOB_REQUEST;
    send_msg.msg1 = jt->jobinfo.job_id;
    send_msg.msg2 = 0;
    send_msg.msg3 = jt->jobinfo.prn_file_path;
    send_msg.msgSender = MID_SYS_JOB_MGR;
    if (task_msg_send_by_router(MID_PARSER_DELAY , &send_msg))
    {
        ULOG_ERROR(SYS_JOB_LOG , "send MID_PARSER_DELAY failed\n");
        return -1;
    }
    else
    {
        return 0;
    }
}

static void _timeout_handle(void *arg , int *enable)
{
    (void)arg;
    *enable = 1;

    struct timeval timeout_arg;
    int differential = 2; 

    pthread_mutex_lock(&jsm.mtx);
    struct job_task *jt = (struct job_task*)channels_head(jsm.chs + T_DELAY);
    
    if (jt)
    {
        differential = time(NULL) - jt->delay;
        timeout_arg.tv_usec = 0;
        if (differential >= 0)
        {
            power_ready(jsm.client , NULL);
            if (access((const char*)jt->jobinfo.prn_file_path , F_OK) == -1)
            {
                ULOG_WARN(SYS_JOB_LOG , "file:%s lose..\n" , jt->jobinfo.prn_file_path);
                channels_fragment_remove2(jsm.chs + T_DELAY , jt , 1);
                timeout_arg.tv_sec = 2; 
            }           
            else
            {
                _transfer_job_request(jt);
                timeout_arg.tv_sec = 5; 
            }
        }
        else
        {
            differential = abs(differential);
            timeout_arg.tv_sec = differential > 5 ? 5 : differential;
        }
        ctimer_update(&jsm.timer , timeout_arg);
        ctimer_restart(&jsm.timer);
    }
    pthread_mutex_unlock(&jsm.mtx);
}

static void _update_timer_task(ONE_JOB_INFO_P job)
{
    if (job->_job_info.timed_task_timestamp == 0)
    {
        ULOG_WARN(SYS_JOB_LOG , "Not satisfied with the conditions for delayed tasks!\n");
        return ;
    }
    struct job_task jt;
    int enable = 1;

    jt.delay = job->_job_info.timed_task_timestamp;
    ULOG_INFO(SYS_JOB_LOG , "New Delay job[%u] %s" , job->_job_info.job_id , ctime(&jt.delay));
    memcpy(&jt.jobinfo , &job->_job_info , sizeof(job->_job_info));
    ctimer_stop(&jsm.timer);
    pthread_mutex_lock(&jsm.mtx);
    channels_order_insert(jsm.chs + T_DELAY , jt.delay , &jt , 0);
    pthread_mutex_unlock(&jsm.mtx);

    _timeout_handle(NULL , &enable);
    sql_set_table_item(jsm.dbB , DELAY_TABLE , job->_job_info.job_id , &jt , sizeof(jt));
}

static void _add_sample_task(ONE_JOB_INFO_P job)
{
    struct job_task jt;

    jt.delay = 0; // 样本作业没有定时的设置
    memcpy(&jt.jobinfo , &job->_job_info , sizeof(job->_job_info));
    pthread_mutex_lock(&jsm.mtx);
    channels_head_insert(jsm.chs + T_SAMPLE , job->_job_info.job_id , &jt , 0);
    pthread_mutex_unlock(&jsm.mtx);

    sql_set_table_item(jsm.dbB , SAMPLE_TABLE , job->_job_info.job_id , &jt , sizeof(jt));
}

static void _add_pincode_task(ONE_JOB_INFO_P job)
{
    struct job_task jt;

    jt.delay = 0; // 密码作业没有定时的设置
    memcpy(&jt.jobinfo , &job->_job_info , sizeof(job->_job_info));
    pthread_mutex_lock(&jsm.mtx);
    channels_head_insert(jsm.chs + T_PINCODE , job->_job_info.job_id , &jt , 0);
    pthread_mutex_unlock(&jsm.mtx);

    sql_set_table_item(jsm.dbB , PINCODE_TABLE , job->_job_info.job_id , &jt , sizeof(jt));
}

static void _delete_unnecessary_tables(void)
{
    struct channels chs; 
    const char *table = NULL;

    channels_init(&chs , alloc_table , free_table);
    sql_get_tables(jsm.dbA , &chs , store_table);
    //NOTE: 节省空间只保留一张最近的TXXXX表
    while(chs.number > 1)
    {
        unsigned int tableid = _get_tableid(jsm.jci.running_jobid);

        if (tableid - jsm.jci.running_jobid > 100)
            break;
        //第二张表接近满的时候，删除第一张表
        table = (const char*)channels_head(&chs);
        if (atoi(table + 1) == tableid)
        {
            table = (const char*)channels_tail_pop(&chs);
        }
        else
        {
            table = (const char*)channels_head_pop(&chs);
        }
        sql_del_table(jsm.dbA , table);
        ULOG_INFO(SYS_JOB_LOG , "remove table:%s\n" , table);
        free_table((void*)table);
    }
    channels_destroy(&chs);
}

static void _correct_abormal_jobinfo(void)
{
    unsigned int jobid = jsm.jci.start_jobid; 

    ULOG_DEBUG(SYS_JOB_LOG , "running_jobid:%u , start_jobid:%u\n" , jsm.jci.running_jobid , jsm.jci.start_jobid);
    if (jsm.jci.start_jobid > jsm.jci.running_jobid)
    { //NOTE: 出现这种情况的原因有：作业完成后执行reboot命令或断电，导致数据没来得及写入flash，因此需要需要将两者互换
        jsm.jci.start_jobid = jsm.jci.start_jobid ^ jsm.jci.running_jobid;
        jsm.jci.running_jobid = jsm.jci.start_jobid ^ jsm.jci.running_jobid;
        jsm.jci.start_jobid = jsm.jci.start_jobid ^ jsm.jci.running_jobid;
    }

    while(1)
    {
        JOBINFO_S jobinfo;
        unsigned int tableid = _get_tableid(jobid);
        char tablestr[64];

        snprintf(tablestr , sizeof(tablestr) , "T%u" , tableid);
        if (sql_get_table_item(jsm.dbA , tablestr , jobid , &jobinfo , sizeof(JOBINFO_S)) == 0)
        {
            if (jobinfo.status != JOB_FINISHED)
            {
                jobinfo.status = JOB_FINISHED;
                jobinfo.status_detail = JOB_ABORTED; 
                sql_set_table_item(jsm.dbA , tablestr , jobid , &jobinfo , sizeof(JOBINFO_S));
            }
            jsm.jci.start_jobid++;
            sql_set_table_item(jsm.dbA , JOBINFO_TABLE , 1 , &jsm.jci , sizeof(jsm.jci));
        }
        else
        {
            jsm.jci.running_jobid = jobid;
            jsm.jci.start_jobid = jobid;
            jsm.jci.start_tableid = tableid; 
            /*sql_set_table_item(jsm.dbA , JOBINFO_TABLE , 1 , &jsm.jci , sizeof(jsm.jci));*/
            break;
        }
        ++jobid;
    }

    _delete_unnecessary_tables();
}

static int _compare_time(void *d1 , void *d2)
{
    struct job_task *jt1 = *(struct job_task**)d1;
    struct job_task *jt2 = *(struct job_task**)d2;

    return jt1->delay < jt2->delay ? 1 : 0;
}

static int _initial_add_delay_task(const void *context , const void *data , unsigned int size)
{
    struct channels *chs = (struct channels*)context;
    struct job_task *jt = (struct job_task*)data;
    (void)size;

    if (jt)
    {
        if (access((const char*)jt->jobinfo.prn_file_path , F_OK) == 0)
        {
            channels_order_insert(chs , jt->delay , jt , 0);//按升序将task添加到队列中
        }
    }
    return 0;
}

static int _initial_add_task(const void *context , const void *data , unsigned int size)
{
    struct channels *chs = (struct channels*)context;
    struct job_task *jt = (struct job_task*)data;
    (void)size;

    if (jt)
    {
        if (access((const char*)jt->jobinfo.prn_file_path , F_OK) == 0)
        {
            channels_head_insert(chs , jt->jobinfo.job_id , jt , 0);
        }
    }
    return 0;
}

static int match_task_jobid(void *data , void *options)
{
    unsigned int jobid = *(unsigned int*)options;
    struct job_task *jt = (struct job_task*)data;

    return jobid == jt->jobinfo.job_id ? 1: 0;
}

static int _del_task(uint32_t jobid , JOBINFO_S *jobinfo)
{
    int ret = 0;
    pthread_mutex_lock(&jsm.mtx);
    struct job_task *delay_jt = (struct job_task*)channels_find_match(jsm.chs + T_DELAY , match_task_jobid  , &jobid);
    struct job_task *sample_jt = (struct job_task*)channels_find(jsm.chs + T_SAMPLE , jobid);
    struct job_task *pincode_jt = (struct job_task*)channels_find(jsm.chs + T_PINCODE , jobid);

    if (delay_jt)
    {
        int enable = 1;

        if (jobinfo)
        {
            memcpy(jobinfo , &delay_jt->jobinfo , sizeof(JOBINFO_S));
        }
        ctimer_stop(&jsm.timer);
        channels_fragment_remove2(jsm.chs + T_DELAY , delay_jt , 1);
        pthread_mutex_unlock(&jsm.mtx);
        _timeout_handle(NULL , &enable);
    }
    else if (sample_jt)
    {
        if (jobinfo)
        {
            memcpy(jobinfo , &sample_jt->jobinfo , sizeof(JOBINFO_S));
        }
        channels_fragment_remove2(jsm.chs + T_SAMPLE , sample_jt , 1);
        pthread_mutex_unlock(&jsm.mtx);
    }
    else if (pincode_jt)
    {
        if (jobinfo)
        {
            memcpy(jobinfo , &pincode_jt->jobinfo , sizeof(JOBINFO_S));
        }
        channels_fragment_remove2(jsm.chs + T_PINCODE , pincode_jt , 1);
        pthread_mutex_unlock(&jsm.mtx);
    }
    else
    {
        pthread_mutex_unlock(&jsm.mtx);
        ret = -1;
    }
    return ret;
}

static void _del_cache_file(uint32_t jobid , int *error)
{
    struct job_task jt;
    const char *table = NULL;
    int ret = 0;

    if (sql_get_table_item(jsm.dbB , DELAY_TABLE , jobid , &jt , sizeof(jt)) == 0)
    {
        table = DELAY_TABLE;
    }
    else
    if (sql_get_table_item(jsm.dbB , SAMPLE_TABLE , jobid , &jt , sizeof(jt)) == 0)
    {
        table = SAMPLE_TABLE;
    }
    else
    if (sql_get_table_item(jsm.dbB , PINCODE_TABLE , jobid , &jt , sizeof(jt)) == 0)
    {
        table = PINCODE_TABLE;
    }

    if (table)
    {

        unlink((const char*)jt.jobinfo.prn_file_path);
        unlink((const char*)jt.jobinfo.raster_file_path);
        sql_del_table_item(jsm.dbB , table , jobid);
    }
    else
    {
        ret = R_INVALID_JOB;
        ULOG_WARN(SYS_JOB_LOG , "jobid:%u invalid..\n" , jobid);
    }
    if (error)
    {
        *error = ret;
    }
}

static int circulation_mkdir(const char *path)
{
    int len = 64 , offset = 0 , ret = -1;
    const char *ptr = path;
    char *confpath = (char*)malloc(len);

    if (!confpath)
    {
        return -1;
    }
    memset(confpath , 0 , len);

    while(ptr && *ptr)
    {
        const char *start = ptr , *end = NULL;

        if (ptr[1] == 0x0)
        {
            break;
        }
        end = strchr(start + 1 , '/');
        if (!end)
        {
            break;
        }
        if (offset + (end - start) + 1 >= len)
        {
            len = (len + offset + (end - start) + 1) << 2;
            char *t = (char*)malloc(len);

            if (!t)
            {
                free(confpath);
                confpath = NULL;
                break;
            }
            memset(t , 0 , len);
            memcpy(t , confpath , offset);
            free(confpath);
            confpath = t; 
        }
        memcpy(confpath + offset , start , end - start + 1);
        offset += end - start + 1;
        for (int i = 0; i < 5; ++i)
        {
            if (mkdir(confpath , 0777) == -1 && errno != EEXIST)
            {
                ULOG_ERROR(SYS_JOB_LOG , "create %s failed , %s\n" , confpath , strerror(errno));
                if (errno == EROFS)
                    sleep(1);
            }
            else
            {
                ret = 0;
                break;
            }
        }
        ptr = end + 1;
    }
    free(confpath);
    return ret;
}


int jobinfo_storage_init(unsigned int *jobid , EVT_MGR_CLI_S *client)
{
    if (!jobid)
        return -1;

    const char *sqla = NULL , *sqlb = NULL;
    uint32_t number = 0;


    jsm.client = client;
    if (circulation_mkdir(SQL_PREFIX_PATH) == 0)
    {
        sqla = SQL_PREFIX_PATH "dba";
        sqlb = SQL_PREFIX_PATH "dbb";
    }
    if (!sqla || !sqlb)
    {
        sqla = "/tmp/dba";
        sqlb = "/tmp/dbb";
    }

    pthread_mutex_init(&jsm.mtx , NULL);
    for (int i = 0; i < sizeof(jsm.chs)/sizeof(jsm.chs[0]); ++i)
    {
        channels_init(jsm.chs + i , alloc_jobtask , free_jobtask);
    }

    jsm.dbA = sql_prolog(sqla);
    jsm.dbB = sql_prolog(sqlb);
    if (sql_integrity_check(jsm.dbA) == 0)
    {
        sql_try_repair(&jsm.dbA , sqla);
    }
    if (sql_integrity_check(jsm.dbB) == 0)
    {
        sql_try_repair(&jsm.dbB , sqlb);
    }

    jsm.jci.start_tableid = TABLE_STEP;
    jsm.jci.running_jobid = jsm.jci.start_jobid = 1;
    if (sql_find_table(jsm.dbA , JOBINFO_TABLE) == 0)
    {
        if (sql_get_table_item(jsm.dbA , JOBINFO_TABLE , 1 , &jsm.jci , sizeof(jsm.jci)) == 0)
        {
            //修正异常关机，有些正执行作业没有结束的作业信息或者还没完成写入到数据库
            _correct_abormal_jobinfo();
        }
    }
    else
    {
        sql_create_table(jsm.dbA , JOBINFO_TABLE);

        char tablestr[64];
        snprintf(tablestr , sizeof(tablestr) , "T%u" , TABLE_STEP);
        sql_create_table(jsm.dbA , tablestr);
    }
    *jobid = jsm.jci.running_jobid;

    if (sql_find_table(jsm.dbB , SAMPLE_TABLE))
    {
        sql_create_table(jsm.dbB , SAMPLE_TABLE);
    }
    else
    {
        number = sql_get_table_items(jsm.dbB , SAMPLE_TABLE , jsm.chs + T_SAMPLE , _initial_add_task);
        ULOG_DEBUG(SYS_JOB_LOG , "Sample job number:%u\n" , number);
    }
    if (sql_find_table(jsm.dbB , DELAY_TABLE))
    {
        sql_create_table(jsm.dbB , DELAY_TABLE);
    }
    else
    {
        //初始化定时器队列
        number = sql_get_table_items(jsm.dbB ,  DELAY_TABLE , jsm.chs + T_DELAY , _initial_add_delay_task);
        ULOG_DEBUG(SYS_JOB_LOG , "Delay job number:%u\n" , number);
    }
    if (sql_find_table(jsm.dbB , PINCODE_TABLE))
    {
        sql_create_table(jsm.dbB , PINCODE_TABLE);
    }
    else
    {
        number = sql_get_table_items(jsm.dbB , PINCODE_TABLE , jsm.chs + T_PINCODE , _initial_add_task);
        ULOG_DEBUG(SYS_JOB_LOG , "Pincode job number:%u\n" , number);
    }

    struct timeval targ;

    targ.tv_sec = 1;
    targ.tv_usec = 0;
    ctimer_init(&jsm.timer , _timeout_handle , NULL , targ , 0);
    ctimer_start(&jsm.timer);
    return 0;
}

int jobinfo_storage_destroy(void)
{
    ctimer_stop(&jsm.timer);
    ctimer_destroy(&jsm.timer);

    for (int i = 0; i < sizeof(jsm.chs)/sizeof(jsm.chs[0]); ++i)
    {
        channels_destroy(jsm.chs + i);
    }
    pthread_mutex_destroy(&jsm.mtx);

    sql_epilog(jsm.dbA);
    sql_epilog(jsm.dbB);
    return 0;
}

int jobinfo_storage_write(unsigned int jobid , void *data , unsigned int size)
{
    if (!data || size == 0)
        return -1;

    ONE_JOB_INFO_P one_job = contain_of(data , ONE_JOB_INFO_S , _job_info);
    JOB_HISTORY_S job_history;

    memcpy(&job_history.job_info, &one_job->_job_info, sizeof(JOBINFO_S));
    memcpy(&job_history.job_params, &one_job->_job_params, sizeof(JOB_PARAM_U));

    if (one_job->_job_info.status == JOB_RUNNING && jsm.jci.running_jobid < one_job->_job_info.job_id)
    {
        jsm.jci.running_jobid = one_job->_job_info.job_id;
        sql_set_table_item(jsm.dbA , JOBINFO_TABLE , 1 , &jsm.jci , sizeof(jsm.jci));
        _delete_unnecessary_tables();
    }

    unsigned int tableid = _get_tableid(one_job->_job_info.job_id);
    char tablestr[64];

    snprintf(tablestr , sizeof(tablestr) , "T%u" , tableid);
    if (sql_find_table(jsm.dbA , tablestr))
    {
        sql_create_table(jsm.dbA , tablestr);
    }

    if (one_job->_job_info.status == JOB_FINISHED)
    {
        if (jsm.jci.start_jobid + 1 == one_job->_job_info.job_id)
        {//NOTE:start_jobid必须是顺序递增，如果中间出现跳跃，则会导致下次开机自检修复时，误将已完成的作业当成异常结束的作业
            jsm.jci.start_jobid = one_job->_job_info.job_id;
            sql_set_table_item(jsm.dbA , JOBINFO_TABLE , 1 , &jsm.jci , sizeof(jsm.jci));
        }
        if (one_job->_parser_result.job_type == JOB_TYPE_PRINT_DELAY && one_job->_job_info.status_detail == JOB_END)
        {
            _update_timer_task(one_job);
        }
        else if (one_job->_parser_result.job_type == JOB_TYPE_PRINT_SAMPLE && one_job->_job_info.status_detail == JOB_END)
        {
            _add_sample_task(one_job);
        }
        else if (one_job->_parser_result.job_type == JOB_TYPE_PRINT_PINCODE && one_job->_job_info.status_detail == JOB_END)
        {
            _add_pincode_task(one_job);
        }
        else if (one_job->_job_info.obj == PRINT_OBJ && one_job->_old_jobid)
        {
            _del_cache_file(one_job->_old_jobid , NULL);
        }
        sql_set_table_item(jsm.dbA , tablestr , one_job->_job_info.job_id , &job_history, sizeof(JOB_HISTORY_S));
    }
    else
    {
        sql_set_table_item(jsm.dbA , tablestr , one_job->_job_info.job_id , &job_history, sizeof(JOB_HISTORY_S));
    }

    return 0;
}

int jobinfo_storage_read(unsigned int jobid , void *data , unsigned int size)
{
    if (!data || size == 0)
        return -1;

    unsigned int tableid = _get_tableid(jobid);
    char tablestr[64];

    snprintf(tablestr , sizeof(tablestr) , "T%u" , tableid);
    return sql_get_table_item(jsm.dbA , tablestr , jobid , data , size); 
}

unsigned int jobinfo_storage_get_history_records(void *context , int(*callback)(const JOBINFO_S* job , void *context, uint32_t size))
{
    struct channels chs; 
    const char *table = NULL;
    unsigned int item = 0;
    struct table_item_context tic;

    channels_init(&chs , alloc_table , free_table);
    sql_get_tables(jsm.dbA , &chs , store_table);
    tic.context_by_caller = context;
    tic.callback_by_caller = callback;
    while((table = (const char*)channels_head_pop(&chs)))
    {
        unsigned int ret = 0;

        ret = sql_get_table_items(jsm.dbA , table , &tic , table_item_handle);
        if (ret > 0)
        {
            item += ret;
        }
        free_table((void*)table);
    }

    channels_destroy(&chs);
    return item;
}

void jobinfo_storage_clear_history_records(void)
{
    struct channels chs; 
    const char *table = NULL;

    channels_init(&chs , alloc_table , free_table);
    sql_get_tables(jsm.dbA , &chs , store_table);

    while ((table = (const char*)channels_head_pop(&chs)))
    {
        sql_del_table(jsm.dbA, table);
        
        free_table((void*)table);
    }

    channels_destroy(&chs);
}

unsigned int jobinfo_storage_get_queue_records(enum QUEUE_TYPE type , void *context , void(*callback)(const JOBINFO_S* job , void *context))
{
    if (type >= T_MAX || type < 0)
        return 0;

    pthread_mutex_lock(&jsm.mtx);
    struct job_task *jt = NULL;
    unsigned int item = 0;
    struct fragment *cur = jsm.chs[type].head , *next = NULL;

    while(cur)
    {
        jt = (struct job_task*)channels_iterator(cur , &next);
        if (jt) 
        {
            ++item;
            if (callback)
            {
                callback(&jt->jobinfo , context);
            }
        }
        cur = next;
    }

    pthread_mutex_unlock(&jsm.mtx);

    return item;
}

int jobinfo_storage_remove_queue_record(unsigned int jobid , JOBINFO_S *jobinfo)
{
    return _del_task(jobid , jobinfo);
}

void jobinfo_storage_remove_queue_records(enum QUEUE_TYPE type)
{
    if (type >= T_MAX || type < 0)
        return ;

    pthread_mutex_lock(&jsm.mtx);
    struct job_task *jt = NULL;
    struct fragment *cur = jsm.chs[type].head , *next = NULL;
    pthread_mutex_unlock(&jsm.mtx);

    while(cur)
    {
        pthread_mutex_lock(&jsm.mtx);
        jt = (struct job_task*)channels_iterator(cur , &next);
        cur = next;
        pthread_mutex_unlock(&jsm.mtx);

        if (jt) 
        {
            jobinfo_storage_remove_job_and_file(jt->jobinfo.job_id);
        }
    }
}

int jobinfo_storage_remove_job_and_file(unsigned int jobid)
{
    int ret = 0;

    _del_cache_file(jobid , &ret);
    if (ret == R_SUCCESS)
    {
        (void)_del_task(jobid , NULL);
    }
    return ret;
}

/**
 * @}
 */
