#include "test_helloworld.h"

#include <quickjs.h>
#include "stdio.h"
#include "stdlib.h"
#include "string.h"
#include "PEDK_event.h"

/* 自定义原生C函数 */
static void test_HelloWrold()
{
    char a;
    int len = 1;
	printf("[pesf send] \t Hello Wrold\n");
    //printf("[pesf]  MSG_MODULE_TEST == %d , MSG_TEST_SUB == %d \n",MSG_MODULE_TEST, MSG_TEST_SUB);
    SendMsgToMfp(MSG_MODULE_TEST, MSG_TEST_SUB, 0, len, &a);
    return;
}

static void test_HelloWrold_recv()
{
    unsigned char b;
    int len;
	printf("[pesf recv] \t Hello Wrold\n");
    //printf("[pesf]  MSG_MODULE_TEST == %d , MSG_TEST_SUB == %d \n",MSG_MODULE_TEST, MSG_TEST_SUB);
    RecvMsgToMfp(MSG_MODULE_TEST, MSG_TEST_SUB, 0, &b, &len, 3);
    printf("[pesf recv] b == %c , len == %d ", b , len);
    return;
}

/* 
    定义 QuickJS C 函数 
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
JSValue js_test_HelloWrold(JSContext *ctx, JSValueConst this_val,
                           int argc, JSValueConst *argv)
{
	test_HelloWrold();
    test_HelloWrold_recv();
	return JS_NewString(ctx, "OK");
}
