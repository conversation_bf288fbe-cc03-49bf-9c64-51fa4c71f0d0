
console.log('start test.http.js : ');

var Headers = globalThis.pesf.net.http.Headers;
var Request = globalThis.pesf.net.http.Request;
var Response = globalThis.pesf.net.http.Response;
var fetchData = globalThis.pesf.net.http.fetchData;
// console.log('supports: ' + JSON.stringify(support) );

var hsg = new globalThis.pesf.net.http.Headers();
hsg.set("Accept", "text/html");
console.log('header set get: ' + hsg.get("Accept") );

var hcon = new Headers({"Accept": "*/*", "content":"app/json"});
console.log('header constructor get: ' + hcon.get("Accept") );
// console.log('header get "" : ' + hcon.get("") );
// Exception: TypeError: Invalid character in header field name: ""
console.log('header get null: ' + hcon.get(null) );
// header get null: null
console.log('header get some: ' + hcon.get("some") );
// header get some: null

// Create a test Headers object
const myHeaders = new Headers();
myHeaders.append("Content-Type", "text/xml");
myHeaders.append("Vary", "Accept-Language");

// Display the keys
for (const key of myHeaders.getAllKeys()) {
  console.log('key '+key);
}

for (const val of myHeaders.getAllValues()) {
  console.log('val '+ val);
}
/*
key content-type
key vary
val text/xml
val Accept-Language
*/

const headers = new Headers({
  "Set-Cookie": "name1=value1",
//   "Set-Cookie": "n3=v3"
});
headers.append("Set-Cookie", "name2=value2");
console.log('get ' + (typeof headers.get('Set-Cookie')) );
console.log('get Set-Cookie type: ' + (typeof headers.getSetCookie('Set-Cookie')) + ' array:' + (headers.getSetCookie() instanceof Array));
console.log('headers get Set-Cookie:' + headers.getSetCookie());
console.log('headers get Set-Cookie:' + headers.getSetCookie()[0]);
var emptyHeader = new Headers();
console.log('emptyHeader get Set-Cookie:' + emptyHeader.getSetCookie() + ', type '+(typeof emptyHeader.getSetCookie())
+ ' array:' + (emptyHeader.getSetCookie() instanceof Array));
/*
get string
get Set-Cookie type: object array:true
headers get Set-Cookie:name1=value1, name2=value2
headers get Set-Cookie:name1=value1
emptyHeader get Set-Cookie:, type object array:true
*/


var req = new Request('example.com');
console.log('req ' + req.method + ' ' + req.url);

fetchData(req, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + resp.headers.get("Content-Length") + (typeof resp.body));
});
// got 200 OK, resp 2001256string

req = new Request('example.com', 'post');
console.log('req ' + req.method + req.url);

fetchData(req, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + resp.headers.get("Content-Length") + (typeof resp.body));
});

try {
    req = new Request('example.com', 'HEAD', headers);
} catch(e) {
    console.log('catch ' + req.url + e);
}

req = new Request('example.com', 'post', null, "abc=345&cde=897");
console.log('req ' + req.url, req.headers);
console.log('req ' + '00url', req.headers.get("ss"));

fetchData(req, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + ' length' + resp.headers.get("Content-Length") + (typeof resp.body));
});

console.log("default timeout " + req.getTimeout());
req.setTimeout(33333);
console.log("timeout "+req.getTimeout());
try {
    req.setTimeout("32222");
    console.log("timeout "+req.getTimeout());
    req.setTimeout('sss');
} catch(e) {
    console.log('setTimeout catch ' + e);
}

/*
req GET example.com
req POSTexample.com
req example.com [object Object]
req 00url null
default timeout 0
timeout 33333
setTimeout catch TypeError: invalid parameter
*/

var resp = new Response(200);
console.log("resp " + resp.code + ", " + resp.headers + ", " + resp.body);

resp = new Response(200, null, "body html");
console.log("resp " + resp.code + ", " + resp.body);
/*
resp 200, undefined, undefined
resp 200, body html
*/


/* maybe 403*/
var headersJianShu = new Headers({
    'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0',
// 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
// 'Accept-Language': 'en-US,en;q=0.5',
// 'Accept-Encoding': 'gzip, deflate, br',
// 'Connection': 'keep-alive',
// 'Cookie': 'sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22184b85ba8e280-0b45029f295e9f8-9782731-2073600-184b85ba8e3426%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.sogou.com%2Flink%3Furl%3DhedJjaC291OV7dVab-QfvHtdr0qpeLU_Tm3YvIpvg1e_881dV1XU4A..%22%7D%2C%22%24device_id%22%3A%22184b85ba8e280-0b45029f295e9f8-9782731-2073600-184b85ba8e3426%22%7D; __bid_n=184b85bae0656224c74207; _ga=GA1.2.2102622518.1669540329',
// 'Upgrade-Insecure-Requests': '1',
// 'Sec-Fetch-Dest': 'document',
// 'Sec-Fetch-Mode': 'navigate',
// 'Sec-Fetch-Site': 'none',
// 'Sec-Fetch-User': '?1',
// 'Pragma': 'no-cache',
// 'Cache-Control': 'no-cache',
});
var requestJian = new Request('http://www.jianshu.com/favicon.ico', 'get', headersJianShu);
fetchData(requestJian, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + ' length' + resp.headers.get("Content-Length") + (typeof resp.body));
    console.log(' type ' + resp.headers.get("Content-type") + (typeof resp.body));
});
// got 403 Forbidden, resp 403 length150string
//  type text/htmlstring



try {
    fetchData("req");
} catch(e) {
    console.log('catch ' + e);
}

try {
    fetchData(req, "");
} catch(e) {
    console.log('catch ' + e);
}

try {
    fetchData(req);
} catch(e) {
    console.log('catch ' + req.url + e);
}
// catch TypeError: invalid parameter
// catch TypeError: not a function
// catch example.comTypeError: not a function

var requestNotExistHost = new Request('http://*************', 'get');
fetchData(requestNotExistHost, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + ' ' + (typeof resp.body));
});
// got Connection Refused, resp -2 undefined

var requestTimeoutHost = new Request('http://google.com', 'get');
requestTimeoutHost.setTimeout(10 * 1000);
fetchData(requestTimeoutHost, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + ' ' + (typeof resp.body));
});
// got No Internet/Timeout, resp -1 undefined

var RequestBody = globalThis.pesf.net.http.RequestBody;

// 简单表单请求，以下三行效果一致
// var requestJian = new Request('example.com', 'post', new Headers({'Content-Type':'application/octet-stream'}), new RequestBody("a=b&c=d&e=sssss"));
// var requestJian = new Request('example.com', 'post', null, new RequestBody("a=b&c=d&e=sssss"));
// var requestJian = new Request('example.com', 'post', null, "a=b&c=d&e=sssss");

// 提交json数据，以下三行效果一致
var requestJian = new Request('example.com', 'post', null, new RequestBody({"add":"bbb", "dd":"ee"}));
// var requestJian = new Request('example.com', 'post', new Headers({'Content-Type':'application/json'}), new RequestBody({"add":"bbb", "dd":"ee"}));
// var requestJian = new Request('example.com', 'post', new Headers({'Content-Type':'application/json'}), JSON.stringify({"add":"bbb", "dd":"ee"}));

// var requestJian = new Request('example.com', 'post');
fetchData(requestJian, function(error, resp) {
    console.log('got ' + error + ", resp " + resp.code + ' length' + resp.headers.get("Content-Length") + (typeof resp.body));
    console.log(' type ' + resp.headers.get("Content-type") + ( resp.body.byteLength));
});

console.log("======++++++======");
