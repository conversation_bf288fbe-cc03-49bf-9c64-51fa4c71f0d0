var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const EMPTY_FN = () => {
};
class Observable {
  /**
   * @param { SubscribeFn } [subscribe]
   * The function that is called when the Observable is
   * initially subscribed to. This function is given a Observer, to which new values
   * can be `next`ed, or an `error` method can be called to raise an error, or
   * `complete` can be called to notify of a successful completion.
   */
  constructor(subscribe) {
    /**
     * @private
     * @optional
     * @type { (o?: Observer<T>) => TeardownLogic }
     */
    __publicField(this, "_subscribe");
    this._subscribe = subscribe ? subscribe : EMPTY_FN;
  }
  /**
   * @param { ((value: any) => void) | Observer } observerOrNext
   * Either an {@link Observer} with some or all callback methods,
   * or the `next` handler that is called for each value emitted from the subscribed Observable.
   * @returns { TeardownLogic }
   */
  subscribe(observerOrNext = EMPTY_FN) {
    const observer = observerOrNext.next ? observerOrNext : { next: observerOrNext, error: EMPTY_FN, complete: EMPTY_FN };
    return this._trySubscribe(observer);
  }
  /**
   * Used to stitch together functional operators into a chain.
   *
   * @template N,M
   * @param { OperatorFunction<N,M> } operations
   * @returns { Observable }
   * The Observable result of all the operators having been called
   * in the order they were passed in.
   */
  pipe(...operations) {
    return operations.reduce((prev, fn) => fn(prev), this);
  }
  /**
   * @param   { Observer } observer
   * @returns { TeardownLogic }
   */
  _trySubscribe(observer) {
    try {
      return this._subscribe(observer);
    } catch (err) {
      observer.error(err);
    }
  }
}
class Subject extends Observable {
  constructor() {
    super();
    /**
     * @type { Observer[] }
     * @private
     */
    __publicField(this, "_observers", []);
    /**
     * @type { number }
     * @private
     */
    __publicField(this, "_nextId", 0);
  }
  /**
   * @param { ((value: any) => void) | Observer } observerOrNext
   * @returns { TeardownLogic }
   */
  subscribe(observerOrNext) {
    const observer = observerOrNext.next ? observerOrNext : { next: observerOrNext, error: EMPTY_FN, complete: EMPTY_FN };
    const id = this._nextId++;
    this._observers[id] = observer;
    return () => {
      delete this._observers[id];
    };
  }
  /**
   * Notify observers and deliver a data payload
   *
   * @param { any } [payload] The data payload to deliver to subscribers
   */
  next(payload) {
    this._observers.forEach((observer) => observer.next(payload));
  }
  /**
   * @param { any } [err]
   */
  error(err) {
    this._observers.forEach((observer) => observer.error(err));
  }
  complete() {
    this._observers.forEach((observer) => observer.complete());
  }
}
const map = (project) => (source2) => new Observable((destination) => {
  let index = 0;
  return source2.subscribe({
    next: (value) => {
      destination.next(project(value, index++));
    }
  });
});
function distinctUntilChanged(comparator = defaultCompare, keySelector = identity) {
  return (source2) => new Observable((destination) => {
    let previousKey;
    let first = true;
    return source2.subscribe({
      next: (value) => {
        const currentKey = keySelector(value);
        if (first || !comparator(previousKey, currentKey)) {
          first = false;
          previousKey = currentKey;
          destination.next(value);
        }
      }
    });
  });
}
function defaultCompare(a, b) {
  return a === b;
}
function identity(x) {
  return x;
}
const createStore = (reducer, initial = {}) => {
  let _state = initial;
  const _state$ = new Subject();
  return {
    /**
     * Get the appStore's current state.
     */
    getState: () => _state,
    /**
     * Dispatch an action to update the appStore.
     */
    dispatch: function(action) {
      if (typeof action === "function") {
        action(this.dispatch.bind(this), this.getState);
      } else {
        _state = reducer(_state, action);
        _state$.next(_state);
        console.log('appStore::dispatched action => "' + action.type + '", payload => ', JSON.stringify(action.payload));
      }
    },
    /**
     * Receive notifications when the store is updated.
     */
    subscribe: (observer) => _state$.subscribe(observer),
    /**
     * Select any derivative state
     */
    select: (selector) => _state$.pipe(
      map(selector),
      distinctUntilChanged()
    )
  };
};
const getReducer = (actions2) => {
  return (state, action) => {
    const handler = actions2[action.type];
    !handler && console.warn("reducer::notFound action => " + action.type);
    return handler ? handler(state, action.payload) : state;
  };
};
const actions = {
  bootstrap: "app/bootstrap",
  exit: "app/exit",
  copyJobState: "copy/jobState",
  copyJobResult: "copy/jobResult",
  copyInMediaSelect: "copy/inMediaSelect",
  copyOutMediaSelect: "copy/outMediaSelect",
  copySourceSelect: "copy/sourceSelect",
  copyModeSelect: "copy/modeSelect",
  copyShiftCopies: "copy/shiftCopies"
};
const copyState = {
  running: "JBSts_Running",
  finish: "JBSts_Finish"
};
const actionsRef = {
  [actions.copyJobState]: (state, payload) => ({
    ...state,
    copyJob: {
      ...state.copyJob,
      state: payload
    }
  }),
  [actions.copyJobResult]: (state, payload) => ({
    ...state,
    copyJob: {
      ...state.copyJob,
      wo: state.copyJob.wo + 1,
      result: payload
    }
  }),
  [actions.bootstrap]: (state, payload) => ({
    ...state,
    ...payload,
    bootstrap: true,
    copyJob: { state: copyState.finish, wo: 1 }
  }),
  [actions.copyInMediaSelect]: (state, payload) => ({
    ...state,
    inMedia: state.inMedia.map((item) => ({ ...item, selected: payload === item.id }))
  }),
  [actions.copyOutMediaSelect]: (state, payload) => ({
    ...state,
    outMedia: state.outMedia.map((item) => ({ ...item, selected: payload === item.id }))
  }),
  [actions.copySourceSelect]: (state, payload) => ({
    ...state,
    source: state.source.map((item) => ({ ...item, selected: payload === item.id }))
  }),
  [actions.copyModeSelect]: (state, payload) => ({
    ...state,
    mode: state.mode.map((item) => ({ ...item, selected: payload === item.id }))
  }),
  [actions.copyShiftCopies]: (state, payload) => ({
    ...state,
    copies: payload ? state.copies + 1 : state.copies > 1 ? state.copies - 1 : 1
  }),
  [actions.exit]: (state) => ({
    ...state,
    bootstrap: false
  })
};
const store = createStore(getReducer(actionsRef));
const {
  CopyParameterSet,
  InputPaperSize,
  CopyJob,
  JobStateListener,
  CopyScanSource,
  Copies,
  CopyMode,
  OutputTray,
  Zoom,
  COPY_NORMAL = "COPY_NORMAL",
  COPY_PARAM_INPUTPAPERSIZE = "COPY_PARAM_INPUTPAPERSIZE",
  COPY_PARAM_COPIES = "COPY_PARAM_COPIES",
  COPY_SCAN_SOURCE = "COPY_SCAN_SOURCE",
  COPY_PARAM_COPYMODE = "COPY_PARAM_COPYMODE",
  COPY_PARAM_ZOOM = "COPY_PARAM_ZOOM"
} = pedk.jobs.copy;
const { PRINT_PARAM_OUTPUTTRAY = "PRINT_PARAM_OUTPUTTRAY" } = pedk.jobs.print;
const trayType = {
  AUTO_SELECTION_TRAY: "AUTO_SELECTION_TRAY"
};
const AUTO_ZOOM = 24;
const scanSource = {
  AUTO: 0,
  DADF: 1,
  ADF: 2,
  FB: 3,
  MADF: 4
};
const scanMode = {
  SINGLE_TO_SINGLE: 1,
  SINGLE_TO_DOUBLE: 2,
  DOUBLE_TO_SINGLE: 3,
  DOUBLE_TO_DOUBLE: 4
};
class CopyJobWrapper {
  constructor(callback) {
    __publicField(this, "jobStateListener", new JobStateListener());
    this.jobStateListener.notify = callback;
  }
  start(params) {
    const { wo, mode: mode2, source: source2, copies, inMedia: inMedia2, outMedia: outMedia2 } = params;
    console.log("CopyJobWrapper::start params => " + JSON.stringify({ wo, mode: mode2, source: source2, copies, inMedia: inMedia2, outMedia: outMedia2 }));
    console.log('CopyJobWrapper::start COPY_NORMAL => "' + COPY_NORMAL + '"');
    console.log('CopyJobWrapper::start COPY_PARAM_INPUTPAPERSIZE => "' + COPY_PARAM_INPUTPAPERSIZE + '"');
    console.log('CopyJobWrapper::start COPY_SCAN_SOURCE => "' + COPY_SCAN_SOURCE + '"');
    console.log('CopyJobWrapper::start COPY_PARAM_COPIES => "' + COPY_PARAM_COPIES + '"');
    console.log('CopyJobWrapper::start COPY_PARAM_COPYMODE => "' + COPY_PARAM_COPYMODE + '"');
    console.log('CopyJobWrapper::start PRINT_PARAM_OUTPUTTRAY => "' + PRINT_PARAM_OUTPUTTRAY + '"');
    console.log('CopyJobWrapper::start COPY_PARAM_ZOOM => "' + COPY_PARAM_ZOOM + '"');
    const paramSet = new CopyParameterSet();
    const job = new CopyJob(COPY_NORMAL);
    job.addListener(this.jobStateListener);
    try {
      const autoZoom = new Zoom(AUTO_ZOOM);
      //paramSet.addParameter(COPY_PARAM_ZOOM, autoZoom);
    } catch (e) {
      console.log("exception::Zoom =>", e);
    }
    //paramSet.addParameter(PRINT_PARAM_OUTPUTTRAY, new OutputTray(trayType.AUTO_SELECTION_TRAY, outMedia2));
    //paramSet.addParameter(COPY_PARAM_INPUTPAPERSIZE, new InputPaperSize(inMedia2));
    //paramSet.addParameter(COPY_SCAN_SOURCE, new CopyScanSource(source2));
    //paramSet.addParameter(COPY_PARAM_COPIES, new Copies(copies));
    //paramSet.addParameter(COPY_PARAM_COPYMODE, new CopyMode(mode2));




	//Normal copy job: Set the current copy job as a normal copy job
	//var copy_job = new globalThis.pedk.jobs.copy.CopyJob('COPY_NORMAL');
	//var copy_job = new globalThis.pedk.jobs.copy.CopyJob('COPY_NUP');
	//var copy_job = new globalThis.pedk.jobs.copy.CopyJob('COPY_BOOKLET');////booklet copy need set //var collate_mode = new globalThis.pedk.jobs.copy.CollateMode(1);

	// Set color mode: Define the color mode of the copy, black and white, color, etc.
	//var color_mode = new globalThis.pedk.jobs.copy.ColorMode('COLOR_MODE_COLOR');
	var color_mode = new globalThis.pedk.jobs.copy.ColorMode('COLOR_MODE_BLACK_WHITE');
	paramSet.addParameter('COPY_COLOR_MODE', color_mode);

	//Set the number of copies: Set the number of copies attribute, i.e., how many copies are needed
	var copy_copies = new globalThis.pedk.jobs.copy.Copies(1);
	//var copy_copies = new globalThis.pedk.jobs.copy.Copies(2);
	paramSet.addParameter('COPY_PARAM_COPIES', copy_copies);

	//Set collate mode: When the number of copies is greater than 1, set whether the copies should be printed collated or uncollated as needed
	var collate_mode = new globalThis.pedk.jobs.copy.CollateMode(0);
	//var collate_mode = new globalThis.pedk.jobs.copy.CollateMode(1);
	//paramSet.addParameter('COPY_PARAM_COLLATEMODE', collate_mode);

	//Set scan source: The copy source can be a platen or ADF, set the scan source of the original according to the actual original placement
	//var copy_source = new globalThis.pedk.jobs.copy.CopyScanSource(2);//ADF
	var copy_source = new globalThis.pedk.jobs.copy.CopyScanSource(3);//FB
	//var copy_source = new globalThis.pedk.jobs.copy.CopyScanSource(1);//DADF
	paramSet.addParameter('COPY_SCAN_SOURCE', copy_source);

	//Set scan size: Define the size of the original, set according to the actual size of the original, otherwise the copy effect cannot be guaranteed
	var scan_size = new globalThis.pedk.jobs.copy.InputPaperSize('MEDIA_SIZE_A4');
	//var scan_size = new globalThis.pedk.jobs.copy.InputPaperSize('MEDIA_SIZE_A3');
	paramSet.addParameter('COPY_PARAM_INPUTPAPERSIZE', scan_size);

	// Set output tray: Define the output paper tray for the copy job, the copy will be printed from the specified tray
	var output_tray = new globalThis.pedk.jobs.copy.OutputTray('STANDAR_TRAY', 'MEDIA_SIZE_A4');
	//var output_tray = new globalThis.pedk.jobs.copy.OutputTray('OPTION_TRAY2', 'MEDIA_SIZE_A3');
	//var output_tray = new globalThis.pedk.jobs.copy.OutputTray('AUTO_SELECTION_TRAY', 'MEDIA_SIZE_A4');
	paramSet.addParameter('COPY_PARAM_OUTPUTTRAY', output_tray);

	// Paper tray media type: Define the type of media in the paper tray, such as plain paper, thick paper, etc.
	var page_type = new globalThis.pedk.jobs.copy.PageType('MEDIA_TYPE_PLAIN');
	//var page_type = new globalThis.pedk.jobs.copy.PageType('MEDIA_TYPE_THICK_PAPER1');
	paramSet.addParameter('COPY_PAGE_TYPE', page_type);

	//Set print duplex mode:   1: PRINT  SINGLE   2:PRINT DUPLEX
	var duplex_mode = new globalThis.pedk.jobs.copy.CopyMode(1);
	//var duplex_mode = new globalThis.pedk.jobs.copy.CopyMode(2);
	paramSet.addParameter('COPY_PARAM_COPYMODE', duplex_mode);

	//Zoom: Define the zoom ratio for scanning images in copy mode. 0-24 auto scale   25-400:user define scale  >400:no scale
	var zoom_value = new globalThis.pedk.jobs.copy.Zoom(0);
	//var zoom_value = new globalThis.pedk.jobs.copy.Zoom(70);
	paramSet.addParameter('COPY_PARAM_ZOOM', zoom_value);

	// Copy quality: Set different modes for different copying originals' image content: text, text + image, etc.
	//var quality_type = new globalThis.pedk.jobs.copy.QualityType(3);
	var quality_type = new globalThis.pedk.jobs.copy.QualityType(1);
	paramSet.addParameter('COPY_PARAM_QUALITYTYPE', quality_type);


	// Save toner mode: Define whether to enable the toner saving mode, which will print the copy with the lowest density when enabled
	var save_toner_mode = new globalThis.pedk.jobs.copy.SaveTonerMode(0);
	//var save_toner_mode = new globalThis.pedk.jobs.copy.SaveTonerMode(1);
	paramSet.addParameter('COPY_TONER_MODE', save_toner_mode);



	// Separator page: Define separator page mode, the separator page is to facilitate users to distinguish between copies
	var separator_mode = new globalThis.pedk.jobs.copy.Separator(0);
	//var separator_mode = new globalThis.pedk.jobs.copy.Separator(1);
	paramSet.addParameter('COPY_SEPARATOR', separator_mode);

	// Separator page paper tray: Define which paper tray the separator page uses, which can be defined as the same or different paper tray as the copy as needed
	var separator_tray_in = new globalThis.pedk.jobs.copy.SeparatorTrayIn('STANDAR_TRAY');
	paramSet.addParameter('COPY_SEPARATOR_TRAY_IN', separator_tray_in);

	// N-up mode: Define N-up mode, 2-in-1/4-in-1
	var nup_mode = new globalThis.pedk.jobs.copy.NupMode(0);
	//var nup_mode = new globalThis.pedk.jobs.copy.NupMode(1);
	paramSet.addParameter('COPY_PARAM_NUPMODE', nup_mode);


	//Set edge-to-edge mode: When the original has no margins, set edge-to-edge copy mode. The copy margin will not exceed 3mm
	var edge_to_edge = new globalThis.pedk.jobs.copy.EdgeToEdgeMode(0);
	paramSet.addParameter('COPY_EDGE_TO_EDGE_MODE', edge_to_edge);

	//Original flip: Define the flip method of the original for double-sided originals
	var origianl_flip = new globalThis.pedk.jobs.copy.OriginalFlip(1);
	paramSet.addParameter('COPY_ORIGINAL_FLIP', origianl_flip);
	//Copies flip: Define the flip method of the copies for double-sided printing
	var copies_flip = new globalThis.pedk.jobs.copy.CopiesFlip(1);
	paramSet.addParameter('COPY_COPIES_FLIP', copies_flip);
	//Original direction: Define the direction of the original placed on the platen or ADF (top, bottom, left, right) for user convenience
	var image_orientation = new globalThis.pedk.jobs.copy.ImageOrientation(0);
	paramSet.addParameter('COPY_IMAGE_ORIENTAITON', image_orientation);


	// Watermark copy: Define the watermark content, when enabled, the watermark content will be displayed on the copy
	var watermark_copy = new globalThis.pedk.jobs.copy.Watermark(0);
	//var watermark_copy = new globalThis.pedk.jobs.copy.Watermark(1);
	watermark_copy.setWatermark('Watermark_test');
	paramSet.addParameter('COPY_PARAM_WATERMARK', watermark_copy);




	// Set edge clean enable: Define whether to enable the edge clean function. After enabling, the edge clean amount needs to be set, and the edge of the original image will be removed during copying.
	var edge_clean_mode = new globalThis.pedk.jobs.copy.EdgeClean(0);
	//var edge_clean_mode = new globalThis.pedk.jobs.copy.EdgeClean(1);
	paramSet.addParameter('COPY_USE_EDGE_CLEAN', edge_clean_mode);
	// Edge clean distance: Define the edge clean amount for the four edges of the original document
	var edge_margin_clean = new globalThis.pedk.jobs.copy.EdgeMargin(0, 3);
	edge_margin_clean.setEdgeMargin(1, 3);
	edge_margin_clean.setEdgeMargin(2, 18);
	edge_margin_clean.setEdgeMargin(3, 18);
	paramSet.addParameter('COPY_FILTER_EDGE_MARGIN', edge_margin_clean);



	// Copy horizontal margin: Define the adjustment of the horizontal margin of the original document. After setting, the original document will be translated in the horizontal direction.
	var horizontal_margin = new globalThis.pedk.jobs.copy.HorizontalMargin(0);
	//var horizontal_margin = new globalThis.pedk.jobs.copy.HorizontalMargin(20);
	paramSet.addParameter('COPY_HORIZONTAL_MARGIN', horizontal_margin);
	// Copy vertical margin: Define the adjustment of the vertical margin of the original document. After setting, the original document will be translated in the vertical direction.
	var vertical_margein = new globalThis.pedk.jobs.copy.VerticalMargin(0);
	//var vertical_margein = new globalThis.pedk.jobs.copy.VerticalMargin(20);
	paramSet.addParameter('COPY_VERTICAL_MARGIN', vertical_margein);



	var CopyRetentionParams = new globalThis.pedk.device.setting.CopyRetentionParam();
	CopyRetentionParams.url = "http://192.168.2.101:8080/cgi-bin/upload.py";
	CopyRetentionParams.format_type = 2;
	CopyRetentionParams.level = 3;
	CopyRetentionParams.protocol = "protocolxxx";

	var headers = new globalThis.pedk.net.http.Headers();
	headers.set("x-customer-key","1234567890");
	CopyRetentionParams.headers = headers;

	globalThis.pedk.device.setting.setCopyRetentionParams(CopyRetentionParams);

	//globalThis.pedk.device.setting.setCopyRetentionSwitch(true);
	globalThis.pedk.device.setting.setCopyRetentionSwitch(false);

    return job.start(wo, paramSet, null);
  }
}
const { copyJobResult, copyJobState } = actions;
const { Button: Button$6, StyleSheet: StyleSheet$8 } = pedk.ui.widget;
const noop = () => {
};
const button = (btn = new Button$6(), style = new StyleSheet$8()) => ({ state, dispatch: dispatch2 }) => {
  const isCopyFinish = copyState.finish === state.copyJob.state;
  btn.id = "copy-button";
  btn.x = 20;
  btn.y = 10;
  btn.w = 135;
  btn.h = 74;
  btn.text = isCopyFinish ? "Copy" : "---";
  btn.cb_released = !isCopyFinish ? noop : (id) => {
    const wo = state.copyJob.wo;
    const { id: inMedia2 } = state.inMedia.find(({ selected }) => selected);
    const { id: outMedia2 } = state.outMedia.find(({ selected }) => selected);
    const { id: source2 } = state.source.find(({ selected }) => selected);
    const { id: mode2 } = state.mode.find(({ selected }) => selected);
    const { copies } = state;
    const copyJob = new CopyJobWrapper((state2) => {
      console.log("copyJob::state => ", state2);
      dispatch2({ type: copyJobState, payload: state2 });
    });
    const params = { mode: mode2, inMedia: inMedia2, outMedia: outMedia2, source: source2, copies, wo };
    const result = copyJob.start(params);
    console.log("copyJob::result => ", result);
    dispatch2({ type: copyJobResult, payload: result });
  };
  btn.style_sheet = style;
  btn.style_sheet.text_color = "#000";
  btn.style_sheet.font_size = "30";
  return btn;
};
const { Label: Label$6, StyleSheet: StyleSheet$7 } = pedk.ui.widget;
const factory = (lb = new Label$6(), styleSheet = new StyleSheet$7()) => ({ state: { device: device2 } }) => {
  lb.id = "login-service-scanLabel";
  lb.x = 30;
  lb.y = 570;
  lb.w = 1100;
  lb.text = `build: ${device2.build} ip: ${device2.ip} mac: ${device2.mac} serial: ${device2.sn}`;
  lb.style_sheet = styleSheet;
  lb.style_sheet.text_color = "#64656B";
  lb.style_sheet.font_size = 24;
  return lb;
};
const { Screen } = pedk.ui.widget;
const screen = (screen2 = new Screen()) => () => {
  screen2.x = 0;
  screen2.y = 0;
  return screen2;
};
const buttonCheckedDown = "/resources/media/buttonCheckedDown-508f2446.png";
const buttonCheckedUp = "/resources/media/buttonCheckedUp-d84bd25d.png";
const buttonUncheckedDown = "/resources/media/buttonUncheckedDown-c6768e57.png";
const buttonUncheckedUp = "/resources/media/buttonUncheckedUp-85c29338.png";
const { Label: Label$5, StyleSheet: StyleSheet$6, Button: Button$5 } = pedk.ui.widget;
const ROW_HEIGHT$4 = 60;
const MARGIN_TOP$4 = 250;
const MARGIN_LEFT$4 = 50;
const getLabel$4 = (lb, ss, name, y) => {
  lb.id = "copy-mode-list-item-label-" + y;
  lb.x = MARGIN_LEFT$4;
  lb.y = y;
  lb.w = 70;
  lb.h = ROW_HEIGHT$4;
  lb.text = name;
  lb.style_sheet = ss;
  lb.style_sheet.text_color = "#000";
  lb.style_sheet.font_size = 20;
  return lb;
};
const getCheckButton$3 = (cb, ss, { selected, id }, y, dispatch2) => {
  cb.id = "copy-mode-item-button-" + y;
  cb.x = MARGIN_LEFT$4 + 70;
  cb.y = y - 5;
  cb.w = ROW_HEIGHT$4 - 20;
  cb.h = ROW_HEIGHT$4 - 20;
  cb.style_sheet = ss;
  cb.style_sheet.text_color = "#000";
  cb.style_sheet.font_size = 20;
  cb.cb_released = (keyId) => {
    console.log("cb_released::" + keyId + ', itemId => "' + id + '"');
    !selected && dispatch2({ type: actions.copyModeSelect, payload: id });
  };
  cb.imgs = [
    { img_res: selected ? buttonCheckedDown : buttonUncheckedDown, img_format: "png" },
    { img_res: selected ? buttonCheckedUp : buttonUncheckedUp, img_format: "png" }
  ];
  return cb;
};
const getWidgetFactory$5 = () => ({ dispatch: dispatch2, state: { mode: mode2 } }) => {
  const { widgets } = mode2.reduce(({ widgets: widgets2, y, i }, item) => {
    const label = getLabel$4(new Label$5(), new StyleSheet$6(), item.name, y);
    const checkButton = getCheckButton$3(new Button$5(), new StyleSheet$6(), item, y, dispatch2);
    return { widgets: [...widgets2, label, checkButton], y: y + ROW_HEIGHT$4, i: i + 1 };
  }, { widgets: [], y: MARGIN_TOP$4, i: 0 });
  return widgets;
};
const modeListWidget = { getWidgetFactory: getWidgetFactory$5 };
const { Label: Label$4, StyleSheet: StyleSheet$5, Button: Button$4 } = pedk.ui.widget;
const ROW_HEIGHT$3 = 60;
const MARGIN_TOP$3 = 250;
const MARGIN_LEFT$3 = 470;
const getLabel$3 = (lb, ss, { name }, y) => {
  lb.id = "in-media-list-item-label-" + y;
  lb.x = MARGIN_LEFT$3;
  lb.y = y;
  lb.w = 260;
  lb.h = ROW_HEIGHT$3;
  lb.text = name;
  lb.style_sheet = ss;
  lb.style_sheet.text_color = "#000";
  lb.style_sheet.font_size = 20;
  return lb;
};
const getCheckButton$2 = (cb, ss, { selected, id }, y, dispatch2) => {
  cb.id = "in-media-list-item-button-" + y;
  cb.x = MARGIN_LEFT$3 + 150;
  cb.y = y - 5;
  cb.w = ROW_HEIGHT$3 - 20;
  cb.h = ROW_HEIGHT$3 - 20;
  cb.style_sheet = ss;
  cb.style_sheet.text_color = "#000";
  cb.style_sheet.font_size = 20;
  cb.cb_released = (keyId) => {
    console.log("cb_released::" + keyId + ', id => "' + id + '"');
    !selected && dispatch2({ type: actions.copyInMediaSelect, payload: id });
  };
  cb.imgs = [
    { img_res: selected ? buttonCheckedDown : buttonUncheckedDown, img_format: "png" },
    { img_res: selected ? buttonCheckedUp : buttonUncheckedUp, img_format: "png" }
  ];
  return cb;
};
const getWidgetFactory$4 = () => ({ dispatch: dispatch2, state: { inMedia: inMedia2 } }) => {
  const { widgets } = inMedia2.reduce(({ widgets: widgets2, y, i }, item) => {
    const label = getLabel$3(new Label$4(), new StyleSheet$5(), item, y);
    const checkButton = getCheckButton$2(new Button$4(), new StyleSheet$5(), item, y, dispatch2);
    return { widgets: [...widgets2, label, checkButton], y: y + ROW_HEIGHT$3, i: i + 1 };
  }, { widgets: [], y: MARGIN_TOP$3, i: 0 });
  return widgets;
};
const inMediaListWidget = { getWidgetFactory: getWidgetFactory$4 };
const { Label: Label$3, StyleSheet: StyleSheet$4, Button: Button$3 } = pedk.ui.widget;
const ROW_HEIGHT$2 = 60;
const MARGIN_TOP$2 = 250;
const MARGIN_LEFT$2 = 750;
const getLabel$2 = (lb, ss, { name }, y) => {
  lb.id = "out-media-list-item-label-" + y;
  lb.x = MARGIN_LEFT$2;
  lb.y = y;
  lb.w = 260;
  lb.h = ROW_HEIGHT$2;
  lb.text = name;
  lb.style_sheet = ss;
  lb.style_sheet.text_color = "#000";
  lb.style_sheet.font_size = 20;
  return lb;
};
const getCheckButton$1 = (cb, ss, { selected, id }, y, dispatch2) => {
  cb.id = "out-media-list-item-button-" + y;
  cb.x = MARGIN_LEFT$2 + 150;
  cb.y = y - 5;
  cb.w = ROW_HEIGHT$2 - 20;
  cb.h = ROW_HEIGHT$2 - 20;
  cb.style_sheet = ss;
  cb.style_sheet.text_color = "#000";
  cb.style_sheet.font_size = 20;
  cb.cb_released = (keyId) => {
    console.log("cb_released::" + keyId + ', id => "' + id + '"');
    !selected && dispatch2({ type: actions.copyOutMediaSelect, payload: id });
  };
  cb.imgs = [
    { img_res: selected ? buttonCheckedDown : buttonUncheckedDown, img_format: "png" },
    { img_res: selected ? buttonCheckedUp : buttonUncheckedUp, img_format: "png" }
  ];
  return cb;
};
const getWidgetFactory$3 = () => ({ dispatch: dispatch2, state: { outMedia: outMedia2 } }) => {
  const { widgets } = outMedia2.reduce(({ widgets: widgets2, y, i }, item) => {
    const label = getLabel$2(new Label$3(), new StyleSheet$4(), item, y);
    const checkButton = getCheckButton$1(new Button$3(), new StyleSheet$4(), item, y, dispatch2);
    return { widgets: [...widgets2, label, checkButton], y: y + ROW_HEIGHT$2, i: i + 1 };
  }, { widgets: [], y: MARGIN_TOP$2, i: 0 });
  return widgets;
};
const outMediaListWidget = { getWidgetFactory: getWidgetFactory$3 };
const { Label: Label$2, StyleSheet: StyleSheet$3, Button: Button$2 } = pedk.ui.widget;
const ROW_HEIGHT$1 = 60;
const MARGIN_TOP$1 = 250;
const MARGIN_LEFT$1 = 280;
const getLabel$1 = (lb, ss, name, y) => {
  lb.id = "copy-source-list-item-label-" + y;
  lb.x = MARGIN_LEFT$1;
  lb.y = y;
  lb.w = 70;
  lb.h = ROW_HEIGHT$1;
  lb.text = name;
  lb.style_sheet = ss;
  lb.style_sheet.text_color = "#000";
  lb.style_sheet.font_size = 20;
  return lb;
};
const getCheckButton = (cb, ss, { selected, id }, y, dispatch2) => {
  cb.id = "copy-source-item-button-" + y;
  cb.x = MARGIN_LEFT$1 + 70;
  cb.y = y - 5;
  cb.w = ROW_HEIGHT$1 - 20;
  cb.h = ROW_HEIGHT$1 - 20;
  cb.style_sheet = ss;
  cb.style_sheet.text_color = "#000";
  cb.style_sheet.font_size = 20;
  cb.cb_released = (keyId) => {
    console.log("cb_released::" + keyId + ', itemId => "' + id + '"');
    !selected && dispatch2({ type: actions.copySourceSelect, payload: id });
  };
  cb.imgs = [
    { img_res: selected ? buttonCheckedDown : buttonUncheckedDown, img_format: "png" },
    { img_res: selected ? buttonCheckedUp : buttonUncheckedUp, img_format: "png" }
  ];
  return cb;
};
const getWidgetFactory$2 = () => ({ dispatch: dispatch2, state: { source: source2 } }) => {
  const { widgets } = source2.reduce(({ widgets: widgets2, y, i }, item) => {
    const label = getLabel$1(new Label$2(), new StyleSheet$3(), item.name, y);
    const checkButton = getCheckButton(new Button$2(), new StyleSheet$3(), item, y, dispatch2);
    return { widgets: [...widgets2, label, checkButton], y: y + ROW_HEIGHT$1, i: i + 1 };
  }, { widgets: [], y: MARGIN_TOP$1, i: 0 });
  return widgets;
};
const sourceListWidget = { getWidgetFactory: getWidgetFactory$2 };
const buttonLeftDown = "/resources/media/buttonLeftDown-b4cf9d6a.png";
const buttonLeftUp = "/resources/media/buttonLeftUp-79790ecf.png";
const buttonRightDown = "/resources/media/buttonRightDown-6e61c7c5.png";
const buttonRightUp = "/resources/media/buttonRightUp-575a02d0.png";
const { Label: Label$1, StyleSheet: StyleSheet$2, Button: Button$1 } = pedk.ui.widget;
const ROW_HEIGHT = 60;
const MARGIN_TOP = 110;
const MARGIN_LEFT = 550;
const getLabel = (lb, ss, text) => {
  lb.id = "copy-copies-label";
  lb.x = MARGIN_LEFT + ROW_HEIGHT + 30;
  lb.y = MARGIN_TOP + 5;
  lb.w = ROW_HEIGHT;
  lb.h = ROW_HEIGHT;
  lb.text = text;
  lb.style_sheet = ss;
  lb.style_sheet.text_color = "#000";
  lb.style_sheet.font_size = 30;
  return lb;
};
const getShiftButton = (cb, ss, dispatch2, payload) => {
  cb.id = "copy-copies-shift-button-" + (payload ? "right" : "left");
  cb.x = payload ? MARGIN_LEFT + 2 * (ROW_HEIGHT + 20) : MARGIN_LEFT;
  cb.y = MARGIN_TOP;
  cb.w = ROW_HEIGHT - 20;
  cb.h = ROW_HEIGHT - 20;
  cb.style_sheet = ss;
  cb.style_sheet.text_color = "#000";
  cb.style_sheet.font_size = 20;
  cb.cb_released = (keyId) => {
    console.log("cb_released::" + keyId);
    dispatch2({ type: actions.copyShiftCopies, payload });
  };
  cb.imgs = [
    { img_res: payload ? buttonRightDown : buttonLeftDown, img_format: "png" },
    { img_res: payload ? buttonRightUp : buttonLeftUp, img_format: "png" }
  ];
  return cb;
};
const getWidgetFactory$1 = () => ({ dispatch: dispatch2, state: { copies } }) => [
  getShiftButton(new Button$1(), new StyleSheet$2(), dispatch2, false),
  getShiftButton(new Button$1(), new StyleSheet$2(), dispatch2, true),
  getLabel(new Label$1(), new StyleSheet$2(), copies)
];
const copiesShiftWidget = { getWidgetFactory: getWidgetFactory$1 };
const { Label, StyleSheet: StyleSheet$1 } = pedk.ui.widget;
const stateLabel = (state, label = new Label(), style = new StyleSheet$1()) => {
  label.x = 170;
  label.y = 25;
  label.w = 330;
  label.text = "state: " + (state.copyJob.state ?? "-");
  label.style_sheet = style;
  label.style_sheet.text_color = "#000";
  label.style_sheet.font_size = 32;
  return label;
};
const resultLabel = (state, label = new Label(), style = new StyleSheet$1()) => {
  label.x = 550;
  label.y = 25;
  label.w = 300;
  label.text = "result: " + (state.copyJob.result ?? "-");
  label.style_sheet = style;
  label.style_sheet.text_color = "#000";
  label.style_sheet.font_size = 32;
  return label;
};
const getWidgetFactory = () => ({ state }) => [stateLabel(state), resultLabel(state)];
const statusWidget = { getWidgetFactory };
const { LinkageMechanism } = pedk.ui;
class LMWrapper {
  constructor() {
    __publicField(this, "lm", new LinkageMechanism());
  }
  exit(id) {
    this.lm.appStarted(id);
    this.lm.appEnded(id);
  }
}
const { Button, StyleSheet } = pedk.ui.widget;
const scanButton = (btn = new Button(), style = new StyleSheet()) => ({ dispatch: dispatch2 }) => {
  btn.id = "exit-button";
  btn.x = 870;
  btn.y = 10;
  btn.w = 135;
  btn.h = 74;
  btn.text = "Exit";
  btn.cb_released = (id) => {
    console.log(`btn cb_released: id[${id}]`);
    dispatch2({ type: actions.exit });
  };
  btn.style_sheet = style;
  btn.style_sheet.text_color = "#000";
  btn.style_sheet.font_size = "30";
  return btn;
};
function filter(predicate, thisArg) {
  return (source2) => new Observable((destination) => {
    let index = 0;
    return source2.subscribe({
      next: (value) => {
        predicate.call(thisArg, value, index++) && destination.next(value);
      }
    });
  });
}
const { bootstrap } = actions;
const { ScreenCtrl } = pedk.ui;
const { ProductInfo, netsetting: NetSetting } = pedk.device.setting;
const {
  MEDIA_SIZE_A4_LEF = "MEDIA_SIZE_A4_LEF",
  MEDIA_SIZE_A4 = "MEDIA_SIZE_A4",
  MEDIA_SIZE_A3 = "MEDIA_SIZE_A3"
} = pedk.common.MEDIA_SIZE;
const { COMMIT_SHA } = { "COMMIT_SHA": "3fcf2f4" };
const scrCtrl = new ScreenCtrl();
const lmWrapper = new LMWrapper();
const dispatch = store.dispatch.bind(store);
store.subscribe((state) => {
  const widgets = [
    screen,
    button,
    scanButton,
    factory,
    statusWidget.getWidgetFactory,
    copiesShiftWidget.getWidgetFactory,
    modeListWidget.getWidgetFactory,
    sourceListWidget.getWidgetFactory,
    inMediaListWidget.getWidgetFactory,
    outMediaListWidget.getWidgetFactory
  ].map((factory2) => factory2()).reduce((acc, getWidget) => {
    const widget = getWidget({ state, dispatch });
    return Array.isArray(widget) ? [...acc, ...widget] : [...acc, widget];
  }, []);
  console.log("state => ", JSON.stringify(state));
  scrCtrl.draw(widgets);
});
const productInfo = new ProductInfo();
const netsetting = NetSetting ? new NetSetting() : { getWiredMacAddrInfo: () => "mock-mac", getWiredNetIPv4Addr: () => "mock-ip" };
const device = {
  ip: netsetting.getWiredNetIPv4Addr(),
  mac: netsetting.getWiredMacAddrInfo(),
  sn: productInfo.getProductSerialNumber(),
  build: COMMIT_SHA
};
const inMedia = [
  { id: MEDIA_SIZE_A4, name: "input A4", selected: true },
  { id: MEDIA_SIZE_A4_LEF, name: "input A4_LEF", selected: false },
  { id: MEDIA_SIZE_A3, name: "input A3", selected: false }
];
const outMedia = [
  { id: MEDIA_SIZE_A4, name: "output A4", selected: true },
  { id: MEDIA_SIZE_A4_LEF, name: "output A4_LEF", selected: false },
  { id: MEDIA_SIZE_A3, name: "output A3", selected: false }
];
const source = [
  { id: scanSource.AUTO, name: "AUTO", selected: true },
  { id: scanSource.DADF, name: "DADF", selected: false },
  { id: scanSource.ADF, name: "ADF", selected: false },
  { id: scanSource.FB, name: "FB", selected: false },
  { id: scanSource.MADF, name: "MADF", selected: false }
];
const mode = [
  { id: scanMode.SINGLE_TO_SINGLE, name: "1 -> 1", selected: true },
  { id: scanMode.SINGLE_TO_DOUBLE, name: "1 -> 2", selected: false },
  { id: scanMode.DOUBLE_TO_SINGLE, name: "2 -> 1", selected: false },
  { id: scanMode.DOUBLE_TO_DOUBLE, name: "2 -> 2", selected: false }
];
const bootstrapPayload = { device, mode, source, inMedia, outMedia, copies: 1 };
store.select(({ bootstrap: bootstrap2 }) => bootstrap2).pipe(filter((is) => !is)).subscribe(() => {
  setTimeout(() => {
    store.dispatch({ type: bootstrap, payload: bootstrapPayload });
    lmWrapper.exit("pantum-" + Date.now() / 1e3);
  }, 1e3);
});
store.dispatch({ type: bootstrap, payload: bootstrapPayload });
