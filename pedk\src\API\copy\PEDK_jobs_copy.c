#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "quickjs.h"
#include "PEDK_jobs_copy.h"
#include "PEDK_event.h"

#define copy_Log(format, ...) printf("\n[copy_log %s-L:%d] "format,__func__, __LINE__, ##__VA_ARGS__);

//#define PEDK_COPY_ADAPTOR_LOG(fmt,...)  printf("<copy adaptor>      [ %s-L:%d ]  "fmt,__func__, __LINE__,##__VA_ARGS__)

static PAPER_SIZE_DETAIL_S c_copy_adaptor_paper_size_detail_7265[] =
{
    {PAPER_SIZE_7265_A4,   210,    297},                 //0x00,     210.0 × 297.0
    {PAPER_SIZE_7265_A5,   148,    210},                 //0x01,     148.5 × 210.0
    {PAPER_SIZE_7265_A5L,   210,    148},                //0x02,     210.0 × 148.0
    {PAPER_SIZE_7265_B5,   176,    250},                 //0x03,     105.0 × 148.0
    {PAPER_SIZE_7265_FULL_PLATEN,     0,      0},        //0x04,
    {PAPER_SIZE_7265_LETTER,   216,    279},             //0x05,     215.9 × 279.4
    {PAPER_SIZE_7265_CARD,     0,      0},               //0x06,
    {PAPER_SIZE_7265_FOLIO,   216,    330},              //0x07,     216.0 × 330.0
    {PAPER_SIZE_7265_ISO_B5,   176,    250},             //0x08,     176.0 × 250.0
    {PAPER_SIZE_7265_A6,   105,    148},                 //0x09,     105.0 × 148.0
    {PAPER_SIZE_7265_USER_DEFINE,     0,      0},        //0x0A,     75~218× 148~356,long paper above 356-1200
    {PAPER_SIZE_7265_LEGAL13,   216,    330},            //0x0B,     215.9 × 330.0
    {PAPER_SIZE_7265_LEGAL14,   216,    356},            //0x0C,     215.9 × 355.6
    {PAPER_SIZE_7265_JIS_B5,   182,    257},             //0x0D,     182.0 × 257.0
    {PAPER_SIZE_7265_ENV_MONARCH,   98,    191},         //0x0E      098.4 × 190.5
    {PAPER_SIZE_7265_ENV_DL,   110,    220},             //0x0F,     110.0 × 220.0
    {PAPER_SIZE_7265_ENV_C5,   162,    229},             //0x10,     162.0 × 229.0
    {PAPER_SIZE_7265_ENV_10,   105,    241},             //0x11,     104.8 × 241.3
    {PAPER_SIZE_7265_YOUKEI_SIZE4,   105,    234},       //0x12,     105.0 × 234.0
    {PAPER_SIZE_7265_JAPANESE_POSTCARD,   100,    148},  //0x13,     100.0 × 148.0
    {PAPER_SIZE_7265_CHOUKEI_SIZE3,   120,    235},      //0x14,     120.0 × 235.0
    {PAPER_SIZE_7265_CUSTOM_16K,   185,    260},         //0x15,     185.0 × 260.0
    {PAPER_SIZE_7265_CUSTOM_BIG_16K,   195,    270},     //0x16,     195.0 × 270.0
    {PAPER_SIZE_7265_CUSTOM_32K,   130,    185},         //0x17,     130.0 × 185.0
    {PAPER_SIZE_7265_CUSTOM_BIG_32K,   135,    195},     //0x18,     135.0 × 195.0
    {PAPER_SIZE_7265_EXECUTIVE,   184,    267},          //0x19,     184.0 × 267.0
    {PAPER_SIZE_7265_OFICIO,   216,    343},             //0x1A,     216.0 × 343.0
    {PAPER_SIZE_7265_STATEMENT,   140,    216},          //0x1B,     140.0 × 216.0
    {PAPER_SIZE_7265_ENV_C6,   114,    162},             //0x1C,     114.3 × 162.0
    {PAPER_SIZE_7265_ZL,   120,    230},                 //0x1D,     120.0 × 230.0
    {PAPER_SIZE_7265_B6,   125,    176},                 //0x1E,     125.0 × 176.0
    {PAPER_SIZE_7265_ENV_B6,   125,    176},             //0x1F,     125.0 × 176.0
    {PAPER_SIZE_7265_POSTCARD,   148,    200},           //0x20,     148.0 × 200.0
    {PAPER_SIZE_7265_YOUGATA2,   114,    162},           //0x21,     114.0 × 162.0
    {PAPER_SIZE_7265_NAGAGATA3,   120,    235},          //0x22,     120.0 × 235.0
    {PAPER_SIZE_7265_YOUNAGA3,   120,    235},           //0x23,     120.0 × 235.0
    {PAPER_SIZE_7265_YOUGATA4,   105,    235},           //0x24,     105.0 × 235.0
    {PAPER_SIZE_7265_LONG,   210,   1200},               //0x25,     210.0 × 1200.0
    {PAPER_SIZE_7265_A3,   297,    420},                 //0x26,     297.0 × 420.0
    {PAPER_SIZE_7265_A4L,   297,    210},                //0x27,     297.0 × 210.0
    {PAPER_SIZE_7265_JIS_B6,   128,    182},             //0x28,     128.0 × 182.0
    {PAPER_SIZE_7265_JIS_B4,   257,    364},             //0x29,     257.0 × 364.0
    {PAPER_SIZE_7265_4X6_INCH,   102,    152},           //0x2A,     101.6 × 152.4 / 4" × 6"
    {PAPER_SIZE_7265_INVOICE,   140,    216},            //0x2B,     215.9 × 139.7 / 5.5" × 8.5"
    {PAPER_SIZE_7265_QUARTO,   254,    203},             //0x2C,     254.0 × 203.2 / 10" × 8"
    {PAPER_SIZE_7265_G_LETTER,   266,    203},           //0x2D,     266.0 × 203.2 /10.5" × 8"
    {PAPER_SIZE_7265_11X14_INCH,   297,    356},         //0x2E,     279.4 × 355.6 / 11" × 14"
    {PAPER_SIZE_7265_LEDGER,   279,    432},             //0x2F,     279.4 × 431.8 /11" × 17"
    {PAPER_SIZE_7265_8K,   270,    390},                 //0x30,     270.0 × 390.0
    {PAPER_SIZE_7265_SRA3,   320,    450},               //0x31,     320.0 × 450.0
    {PAPER_SIZE_7265_FOOLSCAP1,   203,    330},          //0x32,     203.0 × 330.2 / 8"×13"
    {PAPER_SIZE_7265_FOOLSCAP2,   210,    330},          //0x33,     209.6 × 330.2 / 8.25"×13"
    {PAPER_SIZE_7265_FOOLSCAP3,   216,    330},          //0x34,     215.9 × 330.2 / 8.5"×13"
    {PAPER_SIZE_7265_FOOLSCAP4,   220,    330},          //0x35,     220.0 × 330.0 / 8.65"×13"
    {PAPER_SIZE_7265_FOOLSCAP5,   206,    337},          //0x36,     206.4 × 336.6 / 8.125"×13.25"
    {PAPER_SIZE_7265_A3_WIDE1,   305,    457},           //0x37,     304.8 × 457.2 / 12"×18"
    {PAPER_SIZE_7265_A3_WIDE2,   311,    457},           //0x38,     311.1 × 457.2 / 12.25"×18"
    {PAPER_SIZE_7265_CUSTOM_BIG_16KL,   270,     195},   //0x39,     270.0 × 195.0 / 12.25"×18"
    {PAPER_SIZE_7265_JIS_B5L,   257,     182},           //0x3A,     257.0 × 182.0
    {PAPER_SIZE_7265_INVOICE_L,   216,     140},         //0x3B,     215.9 × 139.7 / 8.5" × 5.5"
    {PAPER_SIZE_7265_EXECUTIVE_L,   267,     184},       //0x3C,     266.7 × 184.2 / 10.5"×7.25"
    {PAPER_SIZE_7265_QUARTO_L,   254,     203},          //0x3D,     254.0 × 203.2 / 10"×8"
    {PAPER_SIZE_7265_G_LETTER_L,   267,     203},        //0x3E,     266.7 × 203.2 / 10.5"×8"
    {PAPER_SIZE_7265_LETTER_L,   279,     216},          //0x3F,     279.4 × 215.9 / 11" × 8.5"
    {PAPER_SIZE_7265_ISO_B5L,   250,     176},           //0x40,     250.0 × 176.0

    {PAPER_SIZE_7265_USER_DEFINE1,     0,      0},       //0x41,
    {PAPER_SIZE_7265_USER_DEFINE2,     0,      0},       //0x42,
    {PAPER_SIZE_7265_USER_DEFINE3,     0,      0},       //0x43,
    {PAPER_SIZE_7265_USER_DEFINE4,     0,      0},       //0x44,
    {PAPER_SIZE_7265_USER_DEFINE5,     0,      0},       //0x45,
    {PAPER_SIZE_7265_B4,   257,    364},                 //0x46,     257.0 × 364.0
    {PAPER_SIZE_7265_A6CARD,   105,    148},             //0x47,     105.0 × 148.0

    {PAPER_SIZE_7265_GENERAL,     0,      0},            //0x48,
    {PAPER_SIZE_7265_MIXED,     0,      0},              //0x49,
    {PAPER_SIZE_7265_STATEMENT_L,   216,    140},        //0x4A,     216.0 × 140.0

    {PAPER_SIZE_7265_B5L,   250,     176},               //0x4B,     250.0 × 176.0
    {PAPER_SIZE_7265_BIG_16K,   195,    270},            //0x4C,     295.0 × 270.0
    {PAPER_SIZE_7265_BIG_16KL,   270,     195},          //0x4D,     270.0 × 195.0

    {PAPER_SIZE_7265_AUTO,     0,      0},               //0x4E,
    {PAPER_SIZE_7265_UNKOWN,     0,      0},             //0x4F,
    {PAPER_SIZE_7265_FULL_TABLE,     0,      0},         //0x50,

};

int get_paper_h( int paper_size, PAPER_UNIT_E unit, int y_dpi, PAPER_SIZE_DETAIL_S* paper_size_array )
{
    int      i            = 0;
    int      array_num    = 0;
    uint16_t paper_height = 0;

    if( paper_size_array == NULL )
    {
        copy_Log( "no paper_size_array\n" );
        return -1;
    }

    array_num = sizeof( c_copy_adaptor_paper_size_detail_7265 ) / sizeof( c_copy_adaptor_paper_size_detail_7265[0] );

    for( i = 0; i < array_num; i++ )
    {
        if( paper_size_array[i].paper_size == paper_size )
        {
            if( UNIT_INCH == unit )
            {
                paper_height = paper_size_array[i].height / 25.4;

            }
            else if( UNIT_PIXEL == unit )
            {
                paper_height = paper_size_array[i].height * y_dpi / 25.4;
            }
            else if( UNIT_MM == unit )
            {
                paper_height = paper_size_array[i].height;
            }
            else
            {
                copy_Log( "uint err\n" );
                return -1;
            }
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err\n" );
        return -1;
    }

    return paper_height;
}

int get_paper_w( int paper_size, PAPER_UNIT_E unit, int x_dpi, PAPER_SIZE_DETAIL_S* paper_size_array )
{
    int      i            = 0;
    int      array_num    = 0;
    uint16_t paper_width  = 0;

    if( paper_size_array == NULL )
    {
        copy_Log( "no paper_size_array\n" );
        return -1;
    }

    array_num = sizeof( c_copy_adaptor_paper_size_detail_7265 ) / sizeof( c_copy_adaptor_paper_size_detail_7265[0] );
    for( i = 0; i < array_num; i++ )
    {
        if( paper_size_array[i].paper_size == paper_size )
        {
            if( UNIT_INCH == unit )
            {
                paper_width = paper_size_array[i].width / 25.4;

            }
            else if( UNIT_PIXEL == unit )
            {
                paper_width = paper_size_array[i].width * x_dpi / 25.4;
            }
            else if( UNIT_MM == unit )
            {
                paper_width = paper_size_array[i].width;
            }
            else
            {
                copy_Log( "uint err\n" );
                return -1;
            }
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err\n" );
        return -1;
    }

    return paper_width;
}

int get_paper_w_h( int paper_size, PAPER_UNIT_E unit, int x_dpi, int y_dpi, int* paper_width, int* paper_height, PAPER_SIZE_DETAIL_S* paper_size_array )
{
    int i = 0;
    int array_num    = 0;

    if( NULL == paper_width || NULL == paper_height )
    {
        copy_Log( "point is null\n" );
        return -1;
    }

    if( paper_size_array == NULL )
    {
        copy_Log( "no paper_size_array\n" );
        return -1;
    }

    array_num = sizeof( c_copy_adaptor_paper_size_detail_7265 ) / sizeof( c_copy_adaptor_paper_size_detail_7265[0] );
    for( i = 0; i < array_num; i++ )
    {
        if( paper_size_array[i].paper_size == paper_size )
        {
            if( UNIT_INCH == unit )
            {
                *paper_width  = paper_size_array[i].width / 25.4;
                *paper_height = paper_size_array[i].height / 25.4;

            }
            else if( UNIT_PIXEL == unit )
            {
                *paper_width  = paper_size_array[i].width * x_dpi / 25.4;
                *paper_height = paper_size_array[i].height * y_dpi / 25.4;
            }
            else if( UNIT_MM == unit )
            {
                *paper_width  = paper_size_array[i].width;
                *paper_height = paper_size_array[i].height;
            }
            else
            {
                copy_Log( "uint err\n" );
                return -1;
            }
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err\n" );
        return -1;
    }

    return 0;
}

int get_paper_size( int w, int h, PAPER_SIZE_DETAIL_S* paper_size_array )
{
    int i = 0;
    int array_num    = 0;

    if( paper_size_array == NULL )
    {
        copy_Log( "no paper_size_array\n" );
        return -1;
    }

    array_num = sizeof( c_copy_adaptor_paper_size_detail_7265 ) / sizeof( c_copy_adaptor_paper_size_detail_7265[0] );

    for( ; i < array_num; i++ )
    {
        if( abs( paper_size_array[i].width - w ) <= 5 && abs( paper_size_array[i].height - h ) <= 5 )
        {
            break;
        }
    }

    if( i >= sizeof( paper_size_array ) / sizeof( PAPER_SIZE_DETAIL_S ) )
    {
        return PAPER_SIZE_7265_INVALID;
    }

    return paper_size_array[i].paper_size;
}


//static PEDK_7265_COPY_JOB_REQUEST* s_PEDK_7265_copy_job_requset_head = NULL;
//static COPY_JOB_REQUEST_DATA_S* copy_job_requset_head_PEDK = NULL;
static PEDK_COPY_JOB_REQUEST_DATA_S* s_pedk_copy_job_request_head = NULL;

/*
int get_copy_job_request_id( COPY_JOB_REQUEST_TYPE type, void* copy_job_request )
{

    if( type == COPY_JOB_REQUEST_ADAPTOR_7265 )
    {
        PEDK_7265_COPY_JOB_REQUEST* copy_job_request_7265 = ( PEDK_7265_COPY_JOB_REQUEST* )copy_job_request;
        return copy_job_request_7265->id;
    }
    else if( type == COPY_JOB_REQUEST_PEDK || type == COPY_JOB_REQUEST_7265)
    {
        COPY_JOB_REQUEST_DATA_S* copy_job_request_if = ( COPY_JOB_REQUEST_DATA_S* )copy_job_request;
        return copy_job_request_if->id;
    }

    return 0;
}

void* get_copy_job_request_head( COPY_JOB_REQUEST_TYPE type )
{
    if( type == COPY_JOB_REQUEST_ADAPTOR_7265 )
    {
        return ( void* )s_PEDK_7265_copy_job_requset_head;
    }
    else if( type == COPY_JOB_REQUEST_PEDK)
    {
        return ( void* )copy_job_requset_head_PEDK;
    }
    else if( type == COPY_JOB_REQUEST_7265)
    {
        return ( void* )copy_job_requset_head_7265;
    }

    return 0;
}

void* get_copy_job_request_next( COPY_JOB_REQUEST_TYPE type, void* copy_job_request )
{
    if( copy_job_request == NULL )
        copy_Log("job request null\n");
    if( type == COPY_JOB_REQUEST_ADAPTOR_7265 )
    {
        PEDK_7265_COPY_JOB_REQUEST* copy_job_request_7265 = ( PEDK_7265_COPY_JOB_REQUEST* )copy_job_request;
        return copy_job_request_7265->next;
    }
    else if( type == COPY_JOB_REQUEST_PEDK || type == COPY_JOB_REQUEST_7265)
    {
        COPY_JOB_REQUEST_DATA_S* copy_job_request_if = ( COPY_JOB_REQUEST_DATA_S* )copy_job_request;
        return copy_job_request_if->next;
    }

    return NULL;
}

int set_copy_job_request_next( COPY_JOB_REQUEST_TYPE type, void* job_request_node, void* copy_job_request )
{
    if( type == COPY_JOB_REQUEST_ADAPTOR_7265 )
    {
        if(job_request_node!= NULL)
            ( ( PEDK_7265_COPY_JOB_REQUEST* )job_request_node )->next = NULL;
        ( ( PEDK_7265_COPY_JOB_REQUEST* )copy_job_request )->next = job_request_node;
    }
    else if( type == COPY_JOB_REQUEST_PEDK || type == COPY_JOB_REQUEST_7265)
    {
        if(job_request_node!= NULL)
            ( ( COPY_JOB_REQUEST_DATA_S* )job_request_node )->next = NULL;
        ( ( COPY_JOB_REQUEST_DATA_S* )copy_job_request )->next = job_request_node;
    }

    return NULL;
}

*/

PEDK_COPY_JOB_REQUEST_DATA_S* get_pedk_copy_job_request( int id )
{
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request;

    if( id < 0 )
    {
        return NULL;
    }

    pedk_copy_job_request = s_pedk_copy_job_request_head;
    if( pedk_copy_job_request == NULL )
    {
        copy_Log( "no copy_job_req in head list\n" );
        return NULL;
    }

    while( pedk_copy_job_request )
    {
        if( pedk_copy_job_request->id == id )
        {
            break;
        }
        pedk_copy_job_request = pedk_copy_job_request->next;
    }

    return pedk_copy_job_request;
}

int add_pedk_copy_job_request( PEDK_COPY_JOB_REQUEST_DATA_S* job_request_node )
{
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;

    if( job_request_node == NULL )
    {
        return -1;
    }

    pedk_copy_job_request = s_pedk_copy_job_request_head;
    if( pedk_copy_job_request == NULL )
    {
        s_pedk_copy_job_request_head = job_request_node;
        copy_Log("add head copy_job_request %p\n",job_request_node);
        return 0;
    }

    while( pedk_copy_job_request->next )
    {
        pedk_copy_job_request = pedk_copy_job_request->next;
    }
    pedk_copy_job_request->next = job_request_node;

    copy_Log("add copy_job_request %p\n",job_request_node);

    return 0;
}

int remove_pedk_copy_job_request( int id )
{
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request;
    void* pre_copy_job_request;
    void* head_copy_job_request;
    void* next_copy_job_request;

    if( id < 0 )
    {
        return -1;
    }

    pedk_copy_job_request = (void*)s_pedk_copy_job_request_head;
    head_copy_job_request = pedk_copy_job_request;
    pre_copy_job_request = pedk_copy_job_request;

    while( pedk_copy_job_request )
    {
        if( pedk_copy_job_request->id == id )
        {
            break;
        }
        else
        {
            pre_copy_job_request = pedk_copy_job_request;
            pedk_copy_job_request = pedk_copy_job_request->next;
        }
    }

    if( pedk_copy_job_request != NULL )
    {
        if( pedk_copy_job_request == head_copy_job_request )
        {
            s_pedk_copy_job_request_head = s_pedk_copy_job_request_head->next;
        }
        else
        {
            next_copy_job_request = pedk_copy_job_request->next;
            ( (PEDK_COPY_JOB_REQUEST_DATA_S*)pre_copy_job_request)->next = next_copy_job_request;
        }
    }
    else
    {
        copy_Log( "can not find id:%d job_request node\n", id );
    }

    return 0;
}


static COPY_PARAM_MUTEX_TIPS copy_param_mutex_tips[] =
{
    { COPY_PARAM_MUTEX_NUP_PRINT_MODE,              "nup mutex because print duplex"},
    { COPY_PARAM_MUTEX_BOOKLET_PRINT_MODE,          "booklet can not set single print mode"},
    { COPY_PARAM_MUTEX_MIX_PRINT_MODE,              "mix can not set single to duplex print"},
    { COPY_PARAM_MUTEX_IDCARD_PRINT_MODE,           "idcard only support single scan to single print"},

    { COPY_PARAM_MUTEX_BOOKLET_SCALE,               "booklet can not set scale"},
    { COPY_PARAM_MUTEX_MIX_SCALE,                   "mix can not set scale"},
    { COPY_PARAM_MUTEX_NUP_SCALE,                   "nup can not set scale"},
    { COPY_PARAM_MUTEX_IDCARD_SCALE,                "idcard can not set scale"},
    { COPY_PARAM_MUTEX_SCAN_SOURCE_SCALE,           "scale mutex because scan source"},
    { COPY_PARAM_MUTEX_SCALE_PERCENT_SCALE,         "can not set scale percent in auto scale"},

    { COPY_PARAM_MUTEX_BOOKLET_NUP,                 "nup mutex because booklet"},
    { COPY_PARAM_MUTEX_MIX_NUP,                     "nup mutex because mix"},
    { COPY_PARAM_MUTEX_EDGE_ADJUST_NUP,             "nup mutex because edge adjust"},
    { COPY_PARAM_MUTEX_PRINT_MODE_NUP,              "nup mutex because print mode"},
    { COPY_PARAM_MUTEX_COPY_TYPE_NUP,               "nup mutex because copy type"},
    { COPY_PARAM_MUTEX_SCALE_NUP,                   "nup only support auto scale"},

    { COPY_PARAM_MUTEX_BOOKLET_STAPLE,              "staple mutex because booklet"},
    { COPY_PARAM_MUTEX_MIX_STAPLE,                  "staple mutex because mix"},
    { COPY_PARAM_MUTEX_SEPARATOR_STAPLE,            "staple mutex because separator"},
    { COPY_PARAM_MUTEX_PAPER_TYPE_STAPLE,           "staple mutex because paper type"},
    { COPY_PARAM_MUTEX_PRINT_SIZE_STAPLE,           "staple mutex because print size"},
    { COPY_PARAM_MUTEX_COLLATE_STAPLE,              "staple mutex because collate"},
    { COPY_PARAM_MUTEX_SHIFT_STAPLE,                "staple mutex because shift"},
    { COPY_PARAM_MUTEX_TRAY_RECEIVE_STAPLE,         "staple mutex because tray receive"},

    { COPY_PARAM_MUTEX_BOOKLET_SHIFT,               "shift mutex because booklet"},
    { COPY_PARAM_MUTEX_MIX_SHIFT,                   "shift mutex because mix"},
    { COPY_PARAM_MUTEX_SEPARATOR_SHIFT,             "shift mutex because separator"},
    { COPY_PARAM_MUTEX_STAPLE_SHIFT,                "shift mutex because staple"},
    { COPY_PARAM_MUTEX_FOLD_SHIFT,                  "shift mutex because fold"},
    { COPY_PARAM_MUTEX_PAPER_TYPE_SHIFT,            "shift mutex because paper type"},
    { COPY_PARAM_MUTEX_PRINT_SIZE_SHIFT,            "shift mutex because print size"},
    { COPY_PARAM_MUTEX_COLLATE_SHIFT,               "shift mutex because collate"},
    { COPY_PARAM_MUTEX_TRAY_RECEIVE_SHIFT,          "shift mutex because tray receive"},

    { COPY_PARAM_MUTEX_BOOKLET_PUNCH,               "punch mutex because booklet"},
    { COPY_PARAM_MUTEX_MIX_PUNCH,                   "punch mutex because mix"},
    { COPY_PARAM_MUTEX_SEPARATOR_PUNCH,             "punch mutex because separator"},
    { COPY_PARAM_MUTEX_PAPER_TYPE_PUNCH,            "punch mutex because paper type"},
    { COPY_PARAM_MUTEX_PRINT_SIZE_PUNCH,            "punch mutex because print size"},
    { COPY_PARAM_MUTEX_TRAY_RECEIVE_PUNCH,          "punch mutex because tray receive"},

    { COPY_PARAM_MUTEX_BOOKLET_FOLD,                "fold mutex because booklet"},
    { COPY_PARAM_MUTEX_MIX_FOLD,                    "fold mutex because mix"},
    { COPY_PARAM_MUTEX_SEPARATOR_FOLD,              "fold mutex because separator"},
    { COPY_PARAM_MUTEX_PRINT_SIZE_FOLD,             "fold mutex because print size"},
    { COPY_PARAM_MUTEX_PAPER_TYPE_FOLD,             "fold mutex because paper type"},
    { COPY_PARAM_MUTEX_COLLATE_FOLD,                "fold mutex because collate"},
    { COPY_PARAM_MUTEX_SHIFT_FOLD,                  "fold mutex because shift"},
    { COPY_PARAM_MUTEX_TRAY_RECEIVE_FOLD,           "fold mutex because tray receive"},

    { COPY_PARAM_MUTEX_NUP_BOOKLET,                 "booklet mutex because nup"},
    { COPY_PARAM_MUTEX_MIX_BOOKLET,                 "booklet mutex because mix"},
    { COPY_PARAM_MUTEX_STAPLE_BOOKLET,              "booklet mutex because staple"},
    { COPY_PARAM_MUTEX_SHIFT_BOOKLET,               "booklet mutex because shift"},
    { COPY_PARAM_MUTEX_PUNCH_BOOKLET,               "booklet mutex because punch"},
    { COPY_PARAM_MUTEX_FOLD_BOOKLET,                "booklet mutex because fold"},
    { COPY_PARAM_MUTEX_COLLATE_BOOKLET,             "booklet only support collate"},
    { COPY_PARAM_MUTEX_EDGE_ADJUST_BOOKLET,         "booklet mutex because edge adjust"},
    { COPY_PARAM_MUTEX_SCALE_BOOKLET,               "booklet mutex because scale"},

    { COPY_PARAM_MUTEX_BOOKLET_COVER,               "cover must be set with booklet job"},

    { COPY_PARAM_MUTEX_NUP_EDGE_ADJUST,             "edge adjust mutex because nup"},
    { COPY_PARAM_MUTEX_EDGE_TO_EDGE_EDGE_ADJUST,    "edge adjust mutex because edge to edge"},
    { COPY_PARAM_MUTEX_MIX_EDGE_ADJUST,             "edge adjust mutex because mix"},
    { COPY_PARAM_MUTEX_BOOKLET_EDGE_ADJUST,         "edge adjust mutex because booklet"},
    { COPY_PARAM_MUTEX_SCAN_ARRANGE_EDGE_ADJUST,    "edge adjust arrange over size"},

    { COPY_PARAM_MUTEX_EDGE_TO_EDGE_EDGE_CLEAR,     "edge clear mutex because edge to edge"},
    { COPY_PARAM_MUTEX_SCAN_ARRANGE_EDGE_CLEAR,     "edge clear arrange over size"},

    { COPY_PARAM_MUTEX_EDGE_CLEAR_EDGE_TO_EDGE,     "edge to edge mutex because edge clear"},
    { COPY_PARAM_MUTEX_EDGE_ADJUST_EDGE_TO_EDGE,    "edge to edge mutex because edge adjust"},
    { COPY_PARAM_MUTEX_MIX_EDGE_TO_EDGE,            "edge to edge mutex because edge mix"},

    { COPY_PARAM_MUTEX_SCALE_MIX,                   "mix mutex because scale"},
    { COPY_PARAM_MUTEX_NUP_MIX,                     "mix mutex because nup"},
    { COPY_PARAM_MUTEX_STAPLE_MIX,                  "mix mutex because staple"},
    { COPY_PARAM_MUTEX_SHIFT_MIX,                   "mix mutex because shift"},
    { COPY_PARAM_MUTEX_PUNCH_MIX,                   "mix mutex because punch"},
    { COPY_PARAM_MUTEX_FOLD_MIX,                    "mix mutex because fold"},
    { COPY_PARAM_MUTEX_BOOKLET_MIX,                 "mix mutex because booklet"},
    { COPY_PARAM_MUTEX_EDGE_ADJUST_MIX,             "mix mutex because edge adjust"},
    { COPY_PARAM_MUTEX_EDGE_TO_EDGE_MIX,            "mix mutex because edge edge to edge"},
    { COPY_PARAM_MUTEX_SCAN_SOURCE_MIX,             "mix mutex because scan source FB"},
    { COPY_PARAM_MUTEX_SINGLE_TO_DUPLEX_MIX,        "mix mutex because single to duplex"},
    { COPY_PARAM_MUTEX_TRAY_MIX,                    "mix only support auto tray"},

    { COPY_PARAM_MUTEX_SCAN_SIZE_IDCARD,            "id copy scan area  must be A5"},
    { COPY_PARAM_MUTEX_IDCARD_SCAN_SIZE,            "id copy scan area  must be A5"},

    { COPY_PARAM_MUTEX_STAPLE_SEPARATOR,            "separator mutex because staple"},
    { COPY_PARAM_MUTEX_SHIFT_SEPARATOR,             "separator mutex because shift"},
    { COPY_PARAM_MUTEX_PUNCH_SEPARATOR,             "separator mutex because punch"},
    { COPY_PARAM_MUTEX_FOLD_SEPARATOR,              "separator mutex because fold"},
    { COPY_PARAM_MUTEX_TRAY_RECEIVE_SEPARATOR,      "separator mutex because tray receive"},

    { COPY_PARAM_MUTEX_SCALE_SCAN_SOURCE,           "scan source mutex because scale"},
    { COPY_PARAM_MUTEX_IDCARD_SCAN_SOURCE,          "scan source mutex because idcard"},
    { COPY_PARAM_MUTEX_MIX_SCAN_SOURCE,             "scan source mutex because mix" },

    { COPY_PARAM_MUTEX_BOOKLET_COLLATE,             "non-collate mutex because booklet" },
    { COPY_PARAM_MUTEX_STAPLE_COLLATE,              "non-collate mutex because staple" },
    { COPY_PARAM_MUTEX_FOLD_COLLATE,                "non-collate mutex because fold"},
    { COPY_PARAM_MUTEX_SHIFT_COLLATE,               "non-collate mutex because shift"},

    { COPY_PARAM_MUTEX_BOOKLET_COPIES_FLIP,         "booklet not support modifiy copies flip" },

    { COPY_PARAM_MUTEX_STAPLE_PAPER_TYPE,           "paper type mutex because staple" },
    { COPY_PARAM_MUTEX_PRINT_SIZE_PAPER_TYPE,       "paper type mutex because print size" },
    { COPY_PARAM_MUTEX_PRINT_TRAY_PAPER_TYPE,       "paper type mutex because print tray" },
    { COPY_PARAM_MUTEX_PUNCH_PAPER_TYPE,            "paper type mutex because punch" },
    { COPY_PARAM_MUTEX_FOLD_PAPER_TYPE,             "paper type mutex because fold" },
    { COPY_PARAM_MUTEX_SHIFT_PAPER_TYPE,            "paper type mutex because shift"},

    { COPY_PARAM_MUTEX_PRINT_SIZE_PRINT_TRAY,       "print tray mutex because print size"},
    { COPY_PARAM_MUTEX_PAPER_TYPE_PRINT_TRAY,       "print tray mutex because paper type"},

    { COPY_PARAM_MUTEX_STAPLE_TRAY_RECEIVE,         "tray receive mutex because staple" },
    { COPY_PARAM_MUTEX_PUNCH_TRAY_RECEIVE,          "tray receive mutex because punch" },
    { COPY_PARAM_MUTEX_FOLD_TRAY_RECEIVE,           "tray receive mutex because fold" },
    { COPY_PARAM_MUTEX_SHIFT_TRAY_RECEIVE,          "tray receive mutex because shift" },
    { COPY_PARAM_MUTEX_PRINT_SIZE_TRAY_RECEIVE,     "tray receive mutex because print size" },
    { COPY_PARAM_MUTEX_SEPARATOR_TRAY_RECEIVE,      "tray receive mutex because separator"},

    { COPY_PARAM_MUTEX_PRINT_TRAY_PRINT_SIZE,       "print size mutex because print tray" },
    { COPY_PARAM_MUTEX_PAPER_TYPE_PRINT_SIZE,       "print size mutex because paper type" },
    { COPY_PARAM_MUTEX_STAPLE_PRINT_SIZE,           "print size mutex because staple" },
    { COPY_PARAM_MUTEX_PUNCH_PRINT_SIZE,            "print size mutex because punch" },
    { COPY_PARAM_MUTEX_FOLD_PRINT_SIZE,             "print size mutex because fold" },
    { COPY_PARAM_MUTEX_SHIFT_PRINT_SIZE,            "print size mutex because shift" },
    { COPY_PARAM_MUTEX_TRAY_RECEIVE_PRINT_SIZE,     "print size mutex because tray_receive" },

    { COPY_PARAM_MUTEX_MAX,                         "copy param mutex but can not find the condition" },
};

static COPY_PARAM_MUTEX_TYPE s_copy_param_mutex_type;

int is_print_size_mutex_paper_type( COPY_PARAM_PAPER_SIZE_7265_E paper_size, PAPER_TYPE_7265_E paper_type )
{
    int is_mutex = 0;

    if( paper_size == PAPER_SIZE_7265_USER_DEFINE2 )
    {
        if( PAPER_TYPE_7265_THICK2 != paper_type )
        {
            is_mutex = 1;
        }
    }

    if( is_mutex )
    {
        copy_Log( " paper size:%d mutex paper type:%d ", paper_size, paper_type );
    }

    return is_mutex;

}

int is_print_tray_mutex_paper_type( TRAY_INPUT_7265_E print_tray, PAPER_TYPE_7265_E paper_type )
{
    int is_mutex = 0;

    if( print_tray != TRAY_INPUT_7265_MULTIFUNCTION )
    {
        if( paper_type == PAPER_TYPE_7265_FILM || paper_type == PAPER_TYPE_7265_POST_CARD ||
            paper_type == PAPER_TYPE_7265_LABEL || paper_type == PAPER_TYPE_7265_ENVELOP )
            is_mutex = 1;
    }

    if( is_mutex )
    {
        copy_Log( " tray:%d mutex paper type:%d ", print_tray, paper_type );
    }

    return is_mutex;
}

static int print_size_mutex_tray[24][8] =
{
    { PAPER_SIZE_7265_A4,                  0, 0, 0, 0, 0, 1, 0, },
    { PAPER_SIZE_7265_LETTER,              0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_LEGAL13,             0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_LEGAL14,             0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_JIS_B5,              0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_A5,                  0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_CUSTOM_BIG_16K,      1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_A5L,                 1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_A6,                  1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_EXECUTIVE,           1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_FOLIO,               0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_STATEMENT,           1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_A3,                  0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_JIS_B6,              0, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_JIS_B4,              0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_LEDGER,              0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_8K,                  0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_CUSTOM_BIG_16KL,     0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_EXECUTIVE_L,         1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_LETTER_L,            0, 0, 0, 0, 1, 1, 0, },
    { PAPER_SIZE_7265_USER_DEFINE1,        1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_USER_DEFINE2,        1, 1, 1, 1, 1, 1, 0, },
    { PAPER_SIZE_7265_USER_DEFINE3,        1, 1, 1, 1, 1, 1, 0, },
    {
        PAPER_SIZE_7265_MAX,
        TRAY_INPUT_7265_STANDARD,
        TRAY_INPUT_7265_2,
        TRAY_INPUT_7265_3,
        TRAY_INPUT_7265_4,
        TRAY_INPUT_7265_External_LCT_IN,
        TRAY_INPUT_7265_External_LCT_OUT,
        TRAY_INPUT_7265_MULTIFUNCTION,
    },
};

int is_print_size_mutex_tray( COPY_PARAM_PAPER_SIZE_7265_E paper_size, TRAY_INPUT_7265_E print_tray )
{
    int is_mutex = 0;
    int i;

    copy_Log( "paper_size:%d print_tray:%d\n", paper_size, print_tray );

    for( i = 0; i < 24; i++ )
    {
        if( print_size_mutex_tray[i][0] == paper_size )
        {
            is_mutex = print_size_mutex_tray[i][print_tray + 1];
        }

    }

    if( is_mutex )
    {
        copy_Log( " paper size:%d mutex tray:%d ", paper_size, print_tray );
    }

    return is_mutex;

}

int copy_param_is_mutex_print_mode( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    PRINT_MODE_7265_E print_mode;
    COPY_NUP_TYPE_7265_E     nup_type;
    COPY_PARAM_JOB_TYPE     copy_type;
    //COPY_PARAM_PAPER_SIZE_7265_E print_size;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    print_mode = copy_job_request->print_mode;
    nup_type = copy_job_request->nup_type;
    copy_type = copy_job_request->copy_type;
    //print_size = PEDK_7265_copy_job_request->print_size;

    if( nup_type != COPY_NUP_7265_CLOSE || copy_type == COPY_PARAM_JOB_TYPE_NUP )
    {
        if( print_mode == PRINT_MODE_7265_AUTO_DUPLEX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_NUP_PRINT_MODE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_mode = pre_copy_job_request->print_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_mode );
        }
        else if( print_mode == PRINT_MODE_7265_AUTO_DUPLEX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_NUP_PRINT_MODE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_mode = pre_copy_job_request->print_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_mode );
        }
    }
    else if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_PRINT_MODE;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->print_mode = pre_copy_job_request->print_mode;
        copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_mode );
    }
    else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
    {
        if( print_mode == PRINT_MODE_7265_AUTO_DUPLEX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_PRINT_MODE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_mode = pre_copy_job_request->print_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_mode );
        }
    }
    else if( copy_type == COPY_PARAM_JOB_TYPE_ID_CARD )
    {
        if( print_mode != PRINT_MODE_7265_SINGLE )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_IDCARD_PRINT_MODE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_mode = PRINT_MODE_7265_SINGLE;
            copy_Log( " set copy_param:%d\n", PRINT_MODE_7265_SINGLE );
        }
    }

    return copy_param_is_mutex;

}

int copy_param_is_mutex_scale( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    //copy param is 7265 project
    int copy_param_is_mutex = 0;
    //PRINT_MODE_7265_E print_mode;
    COPY_NUP_TYPE_7265_E     nup_type;
    COPY_PARAM_JOB_TYPE     copy_type;
    SCAN_MODE_7265_E         scan_source;
    int auto_scale_mode;
    //int separator;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    //print_mode = copy_job_request->print_mode;
    nup_type = copy_job_request->nup_type;
    copy_type = copy_job_request->copy_type;
    scan_source = copy_job_request->scan_source;
    auto_scale_mode = copy_job_request->auto_scale_mode;
    //separator = copy_job_request->separator;

    if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
    {
        if( auto_scale_mode == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_SCALE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->auto_scale_mode = pre_copy_job_request->auto_scale_mode;

            copy_job_request->scale_percent = pre_copy_job_request->scale_percent;
            copy_Log( " set copy_param scale_percent:%d auto_scale_mode:%d \n", 100, 1 );
        }
    }
    else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_SCALE;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->auto_scale_mode = 1;
        copy_job_request->scale_percent = 100;
        copy_Log( " set copy_param scale_percent:%d auto_scale_mode:%d \n", 100, 1 );
    }
    else if( copy_type == COPY_PARAM_JOB_TYPE_ID_CARD )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_IDCARD_SCALE;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->auto_scale_mode = 1;
        copy_job_request->scale_percent = 100;
        copy_Log( " set copy_param scale_percent:%u auto_scale_mode:%u \n", copy_job_request->scale_percent, copy_job_request->auto_scale_mode );
    }
    else if( scan_source != SCAN_MODE_7265_FB )
    {
        if( copy_job_request->auto_scale_mode == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCAN_SOURCE_SCALE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->auto_scale_mode = pre_copy_job_request->auto_scale_mode;

            copy_job_request->scale_percent = pre_copy_job_request->scale_percent;
            copy_Log( " set copy_param scale_percent:%u auto_scale_mode:%u \n", copy_job_request->scale_percent, copy_job_request->auto_scale_mode );
        }
    }
    else if( copy_type == COPY_PARAM_JOB_TYPE_NUP || nup_type != COPY_NUP_7265_CLOSE )
    {
        if( auto_scale_mode == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_NUP_SCALE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->auto_scale_mode = pre_copy_job_request->auto_scale_mode;

            copy_job_request->scale_percent = pre_copy_job_request->scale_percent;
            copy_Log( " set copy_param scale_percent:%d auto_scale_mode:%d \n", 100, 1 );
        }
    }

    if( copy_param == SINGLE_COPY_PARAM_SCALE_PERCENT && copy_param_is_mutex == 0 )
    {
        if( copy_job_request->auto_scale_mode != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCALE_PERCENT_SCALE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->auto_scale_mode = pre_copy_job_request->auto_scale_mode;

            copy_job_request->scale_percent = pre_copy_job_request->scale_percent;
            copy_Log( " set copy_param scale_percent:%u auto_scale_mode:%u \n", copy_job_request->scale_percent, copy_job_request->auto_scale_mode );
        }
    }



    return copy_param_is_mutex;
}

int copy_param_is_mutex_nup_type( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    PRINT_MODE_7265_E  print_mode;
    COPY_NUP_TYPE_7265_E     nup_type;
    COPY_PARAM_JOB_TYPE     copy_type;
    int horizontal_margin;
    int vertical_margin;
    int auto_scale_mode;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    print_mode = copy_job_request->print_mode;
    nup_type = copy_job_request->nup_type;
    copy_type = copy_job_request->copy_type;
    horizontal_margin = copy_job_request->horizontal_margin;
    vertical_margin = copy_job_request->vertical_margin;
    auto_scale_mode = copy_job_request->auto_scale_mode;

    int nup_combination = copy_job_request->nup_combination;

    if( copy_type == COPY_PARAM_JOB_TYPE_NUP || nup_type != COPY_NUP_7265_CLOSE )
    {
        if( print_mode == PRINT_MODE_7265_AUTO_DUPLEX )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_MODE_NUP;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );
            copy_param_is_mutex = 1;
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_NUP;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );
            copy_param_is_mutex = 1;
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_NUP;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );
            copy_param_is_mutex = 1;
        }
        else if( horizontal_margin != 0 || vertical_margin != 0 )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_ADJUST_NUP;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );
            copy_param_is_mutex = 1;
        }
        else if( auto_scale_mode == 0 )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCALE_NUP;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );
            copy_param_is_mutex = 1;
        }
    }
    /*else
    {
        if(  copy_param == SINGLE_COPY_PARAM_NUP_COMBINATION && nup_combination != 0)
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_COPY_TYPE_NUP;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );
            copy_param_is_mutex = 1;
        }
        //copy_Log("%s [%d]         is not nup copy job:%d\n",__func__,__LINE__,copy_type);
    }*/

    if( copy_param_is_mutex == 1 )
    {
        if( copy_param == SINGLE_COPY_PARAM_COPY_TYPE )
        {
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", copy_job_request->copy_type );
        }
        else if( copy_param == SINGLE_COPY_PARAM_NUP_TYPE || copy_param == SINGLE_COPY_PARAM_NUP_COMBINATION )
        {
            copy_job_request->nup_type = pre_copy_job_request->nup_type;
            copy_Log( " set copy_param:%u\n", copy_job_request->nup_type );
        }
        else
        {
            copy_Log( "copy param not find\n" );
        }
    }
    return copy_param_is_mutex;
}

int check_image_orientation_mutex_staple( COPY_PARAM_PAPER_SIZE_7265_E scan_size, COPY_PARAM_PAPER_SIZE_7265_E paper_size, STAPLE_MODE_7265_E staple_mode, int param_id )
{
    COPY_ORIGINAL_ORIENTATION_E image_orientation;
    int is_mutex = 0;
    int scan_w = 0, scan_h = 0;
    int scan_long_side = 0;
    int image_horizontal = 0;
    int print_long_side = 0;
    int print_w = 0, print_h = 0;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;

    image_orientation = copy_job_request->image_orientation;
    get_paper_w_h( scan_size, UNIT_MM, 300, 300, &scan_w, &scan_h, c_copy_adaptor_paper_size_detail_7265 );
    get_paper_w_h( paper_size, UNIT_MM, 300, 300, &print_w, &print_h, c_copy_adaptor_paper_size_detail_7265 );
    if( scan_w > scan_h )
    {
        scan_long_side = 1;
    }

    else
    {
        scan_long_side = 0;
    }

    copy_Log( "dbg1 scan_long_side:%d\n", scan_long_side );

    if( print_w > print_h )
        print_long_side = 1;
    else
        print_long_side = 0;

    copy_Log( "dbg1 print_long_side:%d\n", print_long_side );


    if( scan_long_side == 1 )
    {
        if( image_orientation == COPY_ORIGINAL_ORIENTATION_UP || image_orientation == COPY_ORIGINAL_ORIENTATION_DOWN )
        {
            image_horizontal = 0;
        }
        else if( image_orientation == COPY_ORIGINAL_ORIENTATION_LEFT || image_orientation == COPY_ORIGINAL_ORIENTATION_RIGHT )
        {
            image_horizontal = 1;
        }

    }
    else if( scan_long_side == 0 )
    {
        if( image_orientation == COPY_ORIGINAL_ORIENTATION_UP || image_orientation == COPY_ORIGINAL_ORIENTATION_DOWN )
        {
            image_horizontal = 1;
        }
        else if( image_orientation == COPY_ORIGINAL_ORIENTATION_LEFT || image_orientation == COPY_ORIGINAL_ORIENTATION_RIGHT )
        {
            image_horizontal = 0;
        }
    }
    copy_Log( "dbg1 image_horizontal:%d\n", image_horizontal );

    if( image_horizontal == 1 )
    {
        if( ( print_long_side == 1 && ( staple_mode == STAPLE_MODE_7265_DOUBLE_LEFT || staple_mode == STAPLE_MODE_7265_DOUBLE_RIGHT ) ) ||
            ( print_long_side == 0 && staple_mode == STAPLE_MODE_7265_DOUBLE_TOP ) )
        {
            is_mutex = 1;
        }
    }
    else if( image_horizontal == 0 )
    {
        if( ( print_long_side == 1 && staple_mode == STAPLE_MODE_7265_DOUBLE_TOP ) ||
            ( print_long_side == 0 && ( staple_mode == STAPLE_MODE_7265_DOUBLE_LEFT || staple_mode == STAPLE_MODE_7265_DOUBLE_RIGHT ) ) )
        {
            is_mutex = 1;
        }
        else if( print_long_side == 1 && ( staple_mode == STAPLE_MODE_7265_DOUBLE_LEFT || staple_mode == STAPLE_MODE_7265_DOUBLE_RIGHT ) )
        {
            if( PAPER_SIZE_7265_A5L == paper_size || PAPER_SIZE_7265_INVOICE_L == paper_size || PAPER_SIZE_7265_JIS_B5L == paper_size
                || PAPER_SIZE_7265_EXECUTIVE_L == paper_size || PAPER_SIZE_7265_CUSTOM_BIG_16KL == paper_size || PAPER_SIZE_7265_LETTER_L == paper_size
                || PAPER_SIZE_7265_A4L == paper_size
              )
                is_mutex = 1;
        }
    }

    return is_mutex;

}

int copy_param_is_mutex_staple( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    //copy param is 7265 project
    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE     copy_type;
    int separator;
    STAPLE_MODE_7265_E staple_mode;
    PAPER_TYPE_7265_E paper_type;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    COPY_PARAM_PAPER_SIZE_7265_E scan_size;
    int collate;
    SHIFT_MODE_E shift;
    TRAY_RECEIVE_E tray_receive;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type = copy_job_request->copy_type;
    separator = copy_job_request->separator;
    paper_type = copy_job_request->page_type;
    paper_size = copy_job_request->print_size;
    scan_size = copy_job_request->scan_size;
    collate = copy_job_request->collate;
    shift = copy_job_request->shift_mode;
    tray_receive = copy_job_request->tray_receive;

    staple_mode = copy_job_request->staple_mode;

    if( staple_mode != STAPLE_MODE_7265_OFF )
    {
        if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", copy_job_request->staple_mode );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->staple_mode );
        }
        else if( separator != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SEPARATOR_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->staple_mode );

        }
        else if( collate == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_COLLATE_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", copy_job_request->staple_mode );
        }
        else if( shift != SHIFT_MODE_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", copy_job_request->staple_mode );
        }
        else if( tray_receive != TRAY_RECEIVE_2 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_RECEIVE_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", copy_job_request->staple_mode );
        }
        else if( PAPER_TYPE_7265_FILM == paper_type || PAPER_TYPE_7265_POST_CARD == paper_type
                 || PAPER_TYPE_7265_ENVELOP == paper_type || PAPER_TYPE_7265_LABEL == paper_type )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PAPER_TYPE_STAPLE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
            copy_Log( " set copy_param:%u\n", copy_job_request->staple_mode );
        }
        else
        {
            if( check_image_orientation_mutex_staple( scan_size, paper_size, staple_mode, param_id ) )
            {
                copy_param_is_mutex = 1;
            }
            else if( PAPER_SIZE_7265_A6 == paper_size || PAPER_SIZE_7265_JIS_B6 == paper_size || PAPER_SIZE_7265_STATEMENT == paper_size
                     || PAPER_SIZE_7265_INVOICE == paper_size || PAPER_SIZE_7265_A5 == paper_size || PAPER_SIZE_7265_INVOICE_L == paper_size
                     || PAPER_SIZE_7265_A3_WIDE1 == paper_size || PAPER_SIZE_7265_USER_DEFINE == paper_size || PAPER_SIZE_7265_USER_DEFINE1 == paper_size
                     || PAPER_SIZE_7265_USER_DEFINE2 == paper_size || PAPER_SIZE_7265_USER_DEFINE3 == paper_size
                   )
            {
                copy_param_is_mutex = 1;
            }

            if( copy_param_is_mutex )
            {
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_STAPLE;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->staple_mode = pre_copy_job_request->staple_mode;
                copy_Log( " set copy_param:%u\n", copy_job_request->staple_mode );
            }
        }
    }

    return copy_param_is_mutex;

}

int copy_param_is_mutex_shift( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    //copy param is 7265 project

    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE     copy_type;
    int separator;
    SHIFT_MODE_E shift_mode;
    STAPLE_MODE_7265_E staple_mode;
    FOLD_MODE_7265_E fold_mode;
    PAPER_TYPE_7265_E paper_type;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    int collate;
    TRAY_RECEIVE_E tray_receive;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type = copy_job_request->copy_type;
    separator = copy_job_request->separator;
    staple_mode = copy_job_request->staple_mode;
    fold_mode = copy_job_request->fold_mode;
    paper_type = copy_job_request->page_type;
    paper_size = copy_job_request->print_size;
    collate = copy_job_request->collate;
    tray_receive = copy_job_request->tray_receive;

    shift_mode = copy_job_request->shift_mode;
    if( shift_mode != SHIFT_MODE_OFF )
    {
        if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = SHIFT_MODE_OFF;
            copy_Log( " set copy_param:%u\n", copy_job_request->shift_mode );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( separator != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SEPARATOR_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( staple_mode != STAPLE_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( fold_mode == FOLD_MODE_7265_2 || fold_mode == FOLD_7265_MIDDLE_2_STAPLE )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( paper_type == PAPER_TYPE_7265_FILM || paper_type == PAPER_TYPE_7265_POST_CARD || paper_type == PAPER_TYPE_7265_ENVELOP || paper_type == PAPER_TYPE_7265_LABEL )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PAPER_TYPE_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_JIS_B6 || paper_size == PAPER_SIZE_7265_STATEMENT ||
                 paper_size == PAPER_SIZE_7265_INVOICE || paper_size == PAPER_SIZE_7265_A5 || paper_size == PAPER_SIZE_7265_USER_DEFINE ||
                 paper_size == PAPER_SIZE_7265_USER_DEFINE1 || paper_size == PAPER_SIZE_7265_USER_DEFINE2 || paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( collate == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_COLLATE_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
        else if( tray_receive != TRAY_RECEIVE_2 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_RECEIVE_SHIFT;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->shift_mode = pre_copy_job_request->shift_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->shift_mode );
        }
    }


    return copy_param_is_mutex;

}

int copy_param_is_mutex_punch( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    //copy param is 7265 project

    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE     copy_type;
    int separator;
    PUNCH_MODE_7265_E punch_mode;
    PAPER_TYPE_7265_E paper_type;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    TRAY_RECEIVE_E tray_receive;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type = copy_job_request->copy_type;
    separator = copy_job_request->separator;
    paper_type = copy_job_request->page_type;
    paper_size = copy_job_request->print_size;
    tray_receive = copy_job_request->tray_receive;

    punch_mode = copy_job_request->punch_mode;
    if( punch_mode != PUNCH_MODE_7265_OFF )
    {
        if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_PUNCH;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->punch_mode = PUNCH_MODE_7265_OFF;
            copy_Log( " set copy_param:%u\n", copy_job_request->punch_mode );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_PUNCH;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->punch_mode = pre_copy_job_request->punch_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->punch_mode );
        }
        else if( separator != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SEPARATOR_PUNCH;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->punch_mode = pre_copy_job_request->punch_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->punch_mode );
        }
        else if( PAPER_TYPE_7265_FILM == paper_type || PAPER_TYPE_7265_POST_CARD == paper_type || PAPER_TYPE_7265_ENVELOP == paper_type
                 || PAPER_TYPE_7265_LABEL == paper_type )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PAPER_TYPE_PUNCH;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->punch_mode = pre_copy_job_request->punch_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->punch_mode );
        }
        else if( tray_receive == TRAY_RECEIVE_STANDARD || tray_receive == TRAY_RECEIVE_3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_RECEIVE_PUNCH;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->punch_mode = pre_copy_job_request->punch_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->punch_mode );
        }
        else
        {
            if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_JIS_B6 || paper_size == PAPER_SIZE_7265_STATEMENT ||
                paper_size == PAPER_SIZE_7265_INVOICE || paper_size == PAPER_SIZE_7265_A5
                || paper_size == PAPER_SIZE_7265_JIS_B5 ||
                paper_size == PAPER_SIZE_7265_USER_DEFINE || paper_size == PAPER_SIZE_7265_USER_DEFINE1 || paper_size == PAPER_SIZE_7265_USER_DEFINE2 ||
                paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
            {
                copy_param_is_mutex = 1;
            }
            else if( punch_mode == PUNCH_MODE_7265_FOUR_HOLES )
            {
                if( paper_size == PAPER_SIZE_7265_EXECUTIVE || paper_size == PAPER_SIZE_7265_CUSTOM_BIG_16K || paper_size == PAPER_SIZE_7265_A5L ||
                    paper_size == PAPER_SIZE_7265_A4 || paper_size == PAPER_SIZE_7265_INVOICE_L
                    || paper_size == PAPER_SIZE_7265_LETTER ||
                    paper_size == PAPER_SIZE_7265_FOLIO || paper_size == PAPER_SIZE_7265_FOOLSCAP3 || paper_size == PAPER_SIZE_7265_LEGAL13 )
                {
                    copy_param_is_mutex = 1;
                }
            }

            if( copy_param_is_mutex )
            {
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_PUNCH;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->punch_mode = pre_copy_job_request->punch_mode;
                copy_Log( " set copy_param:%u\n", pre_copy_job_request->punch_mode );
            }
        }
    }


    return copy_param_is_mutex;
}

int copy_param_is_mutex_fold( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    //copy param is 7265 project

    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE     copy_type;
    int separator;
    FOLD_MODE_7265_E fold_mode;
    PAPER_TYPE_7265_E paper_type;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    int collate;
    SHIFT_MODE_E shift;
    TRAY_RECEIVE_E tray_receive;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type = copy_job_request->copy_type;
    separator = copy_job_request->separator;
    paper_type = copy_job_request->page_type;
    paper_size = copy_job_request->print_size;
    collate = copy_job_request->collate;
    shift = copy_job_request->shift_mode;
    tray_receive = copy_job_request->tray_receive;

    fold_mode = copy_job_request->fold_mode;
    if( fold_mode !=  FOLD_MODE_7265_OFF )
    {
        if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_FOLD;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->fold_mode = FOLD_7265_MIDDLE_2_STAPLE;
            copy_Log( " set copy_param:%u\n", copy_job_request->fold_mode );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_FOLD;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
        }
        else if( separator != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SEPARATOR_FOLD;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
        }
        else if( fold_mode == FOLD_MODE_7265_2 || fold_mode == FOLD_7265_MIDDLE_2_STAPLE )
        {
            if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_JIS_B6 || paper_size == PAPER_SIZE_7265_STATEMENT ||
                paper_size == PAPER_SIZE_7265_INVOICE || paper_size == PAPER_SIZE_7265_A5
                || paper_size == PAPER_SIZE_7265_JIS_B5 ||
                paper_size == PAPER_SIZE_7265_EXECUTIVE || paper_size == PAPER_SIZE_7265_CUSTOM_BIG_16K || paper_size == PAPER_SIZE_7265_A5L ||
                paper_size == PAPER_SIZE_7265_INVOICE_L || paper_size == PAPER_SIZE_7265_JIS_B5L || paper_size == PAPER_SIZE_7265_CUSTOM_BIG_16KL ||
                paper_size == PAPER_SIZE_7265_LETTER_L || paper_size == PAPER_SIZE_7265_A4L || paper_size == PAPER_SIZE_7265_USER_DEFINE ||
                paper_size == PAPER_SIZE_7265_USER_DEFINE1 || paper_size == PAPER_SIZE_7265_USER_DEFINE2 || paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
            {
                copy_param_is_mutex = 1;
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_FOLD;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
                copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
            }
            else if( PAPER_TYPE_7265_FILM == paper_type || PAPER_TYPE_7265_POST_CARD == paper_type || PAPER_TYPE_7265_ENVELOP == paper_type
                     || PAPER_TYPE_7265_LABEL == paper_type )
            {
                copy_param_is_mutex = 1;
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_PAPER_TYPE_FOLD;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
                copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
            }
            else if( shift != SHIFT_MODE_OFF )
            {
                copy_param_is_mutex = 1;
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_SHIFT;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
                copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
            }
            else if( collate == 0 )
            {
                copy_param_is_mutex = 1;
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_COLLATE_FOLD;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
                copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
            }
            else if( tray_receive != TRAY_RECEIVE_3 )
            {
                copy_param_is_mutex = 1;
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_RECEIVE_FOLD;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->fold_mode = pre_copy_job_request->fold_mode;
                copy_Log( " set copy_param:%u\n", pre_copy_job_request->fold_mode );
            }
        }

    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_copy_type_booklet( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    //copy param is 7265 project

    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE     copy_type;
    //COPY_PARAM_JOB_TYPE     pre_copy_type;
    //PRINT_MODE_7265_E print_mode;
    //int auto_scale_mode;
    //int scale_percent;
    //int separator;
    FOLD_MODE_7265_E fold_mode;
    PUNCH_MODE_7265_E punch_mode;
    SHIFT_MODE_E shift_mode;
    STAPLE_MODE_7265_E staple_mode;
    COPY_NUP_TYPE_7265_E     nup_type;
    int collate;
    int horizontal_margin;
    int vertical_margin;
    int auto_scale_mode;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type = copy_job_request->copy_type;
    fold_mode =  copy_job_request->fold_mode;
    punch_mode = copy_job_request->punch_mode;
    shift_mode =  copy_job_request->shift_mode;
    staple_mode =  copy_job_request->staple_mode;
    nup_type =  copy_job_request->nup_type;
    collate = copy_job_request->collate;
    horizontal_margin = copy_job_request->horizontal_margin;
    vertical_margin = copy_job_request->vertical_margin;
    auto_scale_mode = copy_job_request->auto_scale_mode;

    if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
    {
        if( nup_type != COPY_NUP_7265_CLOSE )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_NUP_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( staple_mode != STAPLE_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( shift_mode != SHIFT_MODE_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( punch_mode != PUNCH_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PUNCH_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( fold_mode != FOLD_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( 0 == collate )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_COLLATE_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( horizontal_margin != 0 || vertical_margin != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_ADJUST_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else if( auto_scale_mode == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCALE_BOOKLET;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            //really set COPY_PARAM_JOB_TYPE_SCALE ?
            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->copy_type );
        }
        else
        {
            //copy_Log( "reset all param to default value\n" );
        }
    }
    return copy_param_is_mutex;

}

int copy_param_is_mutex_cover( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    //COPY_PARAM_JOB_TYPE     copy_type;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    //if( copy_type != COPY_PARAM_JOB_TYPE_BOOKLET )
    if( 0 )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_COVER;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        //really set COPY_PARAM_JOB_TYPE_SCALE ?
        copy_job_request->cover = COVER_OFF;
        copy_job_request->cover_back = COVER_OFF;
        copy_job_request->cover_paper_type = PAPER_TYPE_7265_ORDINARY;
        copy_job_request->cover_tray_in = TRAY_INPUT_7265_STANDARD;
        copy_Log( " set copy_param:%d\n", COPY_PARAM_JOB_TYPE_SCALE );
    }

    return copy_param_is_mutex;

}


int copy_param_is_mutex_edge_adjust( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE     copy_type;
    COPY_NUP_TYPE_7265_E     nup_type;
    int horizontal_margin;
    int vertical_margin;
    int edge_to_edge_mode;
    COPY_PARAM_PAPER_SIZE_7265_E scan_size;
    int scan_w = 0;
    int scan_h = 0;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    horizontal_margin = copy_job_request->horizontal_margin;
    vertical_margin = copy_job_request->vertical_margin;
    edge_to_edge_mode = copy_job_request->edge_to_edge_mode;
    copy_type = copy_job_request->copy_type;
    scan_size = copy_job_request->scan_size;
    nup_type = copy_job_request->nup_type;

    if( horizontal_margin != 0 || vertical_margin != 0 )
    {
        if( copy_type == COPY_PARAM_JOB_TYPE_NUP || nup_type != COPY_NUP_7265_CLOSE )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_NUP_EDGE_ADJUST;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->horizontal_margin = 0;
            copy_job_request->vertical_margin = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( edge_to_edge_mode != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_TO_EDGE_EDGE_ADJUST;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->horizontal_margin = 0;
            copy_job_request->vertical_margin = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_EDGE_ADJUST;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->horizontal_margin = 0;
            copy_job_request->vertical_margin = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_EDGE_ADJUST;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->horizontal_margin = 0;
            copy_job_request->vertical_margin = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else
        {

            get_paper_w_h( scan_size, UNIT_MM, 300, 300, &scan_w, &scan_h, c_copy_adaptor_paper_size_detail_7265 );

            copy_Log( " scan_w:%d,scan_h:%d\n", scan_w, scan_h );
            if( abs( horizontal_margin ) >= scan_w || abs( vertical_margin ) >= scan_h )
            {
                copy_param_is_mutex = 1;
                s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCAN_ARRANGE_EDGE_ADJUST;
                copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

                copy_job_request->horizontal_margin = pre_copy_job_request->horizontal_margin;
                copy_job_request->vertical_margin = pre_copy_job_request->vertical_margin;
                copy_Log( " set copy_param:%d\n", 0 );
            }
        }
    }

    return copy_param_is_mutex;

}

int copy_param_is_mutex_edge_clear( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    int edge_clean_top;
    int edge_clean_bottom;
    int edge_clean_left;
    int edge_clean_right;
    int edge_to_edge_mode;
    COPY_PARAM_PAPER_SIZE_7265_E scan_size;
    int scan_w = 0;
    int scan_h = 0;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    edge_clean_top = copy_job_request->filter_edge_margin_top;
    edge_clean_bottom = copy_job_request->filter_edge_margin_bottom;
    edge_clean_left = copy_job_request->filter_edge_margin_left;
    edge_clean_right = copy_job_request->filter_edge_margin_right;
    edge_to_edge_mode = copy_job_request->edge_to_edge_mode;
    scan_size = copy_job_request->scan_size;

    if( edge_clean_top != 0 || edge_clean_right != 0 || edge_clean_bottom != 0 || edge_clean_left != 0 )
    {
        if( edge_to_edge_mode != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_TO_EDGE_EDGE_CLEAR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->filter_edge_margin_top = 0;
            copy_job_request->filter_edge_margin_bottom = 0;
            copy_job_request->filter_edge_margin_left = 0;
            copy_job_request->filter_edge_margin_right = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }
    else
    {
        get_paper_w_h( scan_size, UNIT_MM, 300, 300, &scan_w, &scan_h, c_copy_adaptor_paper_size_detail_7265 );
        if( ( edge_clean_left + edge_clean_right ) >= scan_w || ( edge_clean_top + edge_clean_bottom ) >= scan_h )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCAN_ARRANGE_EDGE_CLEAR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->filter_edge_margin_top = pre_copy_job_request->filter_edge_margin_top;
            copy_job_request->filter_edge_margin_bottom = pre_copy_job_request->filter_edge_margin_bottom;
            copy_job_request->filter_edge_margin_left = pre_copy_job_request->filter_edge_margin_left;
            copy_job_request->filter_edge_margin_right = pre_copy_job_request->filter_edge_margin_right;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }
    return copy_param_is_mutex;

}

int copy_param_is_mutex_edge_to_edge( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    int edge_clean_top;
    int edge_clean_bottom;
    int edge_clean_left;
    int edge_clean_right;
    int edge_to_edge_mode;
    int horizontal_margin;
    int vertical_margin;
    COPY_PARAM_JOB_TYPE copy_type;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    edge_clean_top = copy_job_request->filter_edge_margin_top;
    edge_clean_bottom = copy_job_request->filter_edge_margin_bottom;
    edge_clean_left = copy_job_request->filter_edge_margin_left;
    edge_clean_right = copy_job_request->filter_edge_margin_right;
    edge_to_edge_mode = copy_job_request->edge_to_edge_mode;
    horizontal_margin = copy_job_request->horizontal_margin;
    vertical_margin = copy_job_request->vertical_margin;
    copy_type = copy_job_request->copy_type;

    if( edge_to_edge_mode != 0 )
    {
        if( edge_clean_top != 0 || edge_clean_bottom != 0 || edge_clean_left != 0 || edge_clean_right != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_CLEAR_EDGE_TO_EDGE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->edge_to_edge_mode = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( horizontal_margin != 0 || vertical_margin != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_ADJUST_EDGE_TO_EDGE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->edge_to_edge_mode = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_EDGE_TO_EDGE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->edge_to_edge_mode = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }
    return copy_param_is_mutex;
}

int copy_param_is_mutex_mix( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    int edge_to_edge_mode;
    int horizontal_margin;
    int vertical_margin;
    COPY_PARAM_JOB_TYPE copy_type;
    //COPY_PARAM_JOB_TYPE pre_copy_type;

    //COPY_PARAM_PAPER_SIZE_7265_E scan_size;
    int auto_scale_mode;
    int scale_percent;
    STAPLE_MODE_7265_E staple_mode;
    SHIFT_MODE_E shift_mode;
    PUNCH_MODE_7265_E punch_mode;
    FOLD_MODE_7265_E fold_mode;
    SCAN_MODE_7265_E scan_source;
    PRINT_MODE_7265_E print_mode;
    //TRAY_INPUT_7265_E print_tray;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    edge_to_edge_mode = copy_job_request->edge_to_edge_mode;
    horizontal_margin = copy_job_request->horizontal_margin;
    vertical_margin = copy_job_request->vertical_margin;
    copy_type = copy_job_request->copy_type;
    //pre_copy_type = pre_copy_job_request->copy_type;

    //scan_size = copy_job_request->scan_size;
    auto_scale_mode =  copy_job_request->auto_scale_mode;
    scale_percent =  copy_job_request->scale_percent;
    staple_mode =  copy_job_request->staple_mode;
    shift_mode =  copy_job_request->shift_mode;
    punch_mode = copy_job_request->punch_mode;
    fold_mode =  copy_job_request->fold_mode;
    scan_source = copy_job_request->scan_source;

    print_mode = copy_job_request->print_mode;
    //print_tray = copy_job_request->print_tray;

    if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
    {
        if( auto_scale_mode != 1 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCALE_MIX;
            copy_Log( "auto_scale_mode:%d  scale_percent:%d\n", auto_scale_mode, scale_percent );
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );

        }
        /*
        else if( pre_copy_type == COPY_PARAM_JOB_TYPE_NUP )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_NUP_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( pre_copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        */
        else if( staple_mode != STAPLE_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( shift_mode != SHIFT_MODE_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( punch_mode != PUNCH_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PUNCH_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( fold_mode != FOLD_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( horizontal_margin != 0 || vertical_margin != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_ADJUST_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( edge_to_edge_mode != 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_EDGE_TO_EDGE_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( scan_source == SCAN_MODE_7265_FB )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCAN_SOURCE_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( print_mode == PRINT_MODE_7265_AUTO_DUPLEX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SINGLE_TO_DUPLEX_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        /*else if( print_tray != TRAY_INPUT_7265_AUTO )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_MIX;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->copy_type = pre_copy_job_request->copy_type;
            copy_Log( " set copy_param:%d\n", 0 );
        }*/
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_scan_size( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE copy_type;
    COPY_PARAM_PAPER_SIZE_7265_E scan_size;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type =  copy_job_request->copy_type;
    scan_size = copy_job_request->scan_size;

    if( copy_type == COPY_PARAM_JOB_TYPE_ID_CARD )
    {
        if( scan_size != PAPER_SIZE_7265_A5 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_IDCARD_SCAN_SIZE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->scan_size = pre_copy_job_request->scan_size;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_idcard( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE copy_type;
    COPY_PARAM_PAPER_SIZE_7265_E scan_size;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type =  copy_job_request->copy_type;
    scan_size = copy_job_request->scan_size;

    if( copy_type == COPY_PARAM_JOB_TYPE_ID_CARD )
    {
        if( scan_size != PAPER_SIZE_7265_A5 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCAN_SIZE_IDCARD;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->scan_size = pre_copy_job_request->scan_size;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_separator( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    STAPLE_MODE_7265_E staple_mode;
    SHIFT_MODE_E shift_mode;
    PUNCH_MODE_7265_E punch_mode;
    FOLD_MODE_7265_E fold_mode;
    int separator;
    TRAY_RECEIVE_E tray_receive;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    staple_mode =  copy_job_request->staple_mode;
    shift_mode =  copy_job_request->shift_mode;
    punch_mode = copy_job_request->punch_mode;
    fold_mode =  copy_job_request->fold_mode;
    tray_receive = copy_job_request->tray_receive;

    separator =  copy_job_request->separator;

    if( separator != 0 )
    {
        if( staple_mode != STAPLE_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_SEPARATOR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->separator = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( shift_mode != SHIFT_MODE_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_SEPARATOR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->separator = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( punch_mode != PUNCH_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PUNCH_SEPARATOR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->separator = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( fold_mode != FOLD_MODE_7265_OFF )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_SEPARATOR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->separator = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( tray_receive == TRAY_RECEIVE_3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_RECEIVE_SEPARATOR;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->separator = 0;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_scan_source( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    int auto_scale_mode;
    //int scale_percent;
    SCAN_MODE_7265_E scan_source;
    COPY_PARAM_JOB_TYPE copy_type;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    auto_scale_mode =  copy_job_request->auto_scale_mode;
    //scale_percent =  copy_job_request->scale_percent;
    scan_source = copy_job_request->scan_source;
    copy_type = copy_job_request->copy_type;
    if( scan_source != SCAN_MODE_7265_FB )
    {
        if( auto_scale_mode == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SCALE_SCAN_SOURCE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->scan_source = pre_copy_job_request->scan_source;
            copy_Log( " set copy_param:%d\n", 0 );
        }
        else if( copy_type == COPY_PARAM_JOB_TYPE_ID_CARD )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_IDCARD_SCAN_SOURCE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->scan_source = SCAN_MODE_7265_FB;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }
    else
    {
        if( copy_type == COPY_PARAM_JOB_TYPE_MIX )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_MIX_SCAN_SOURCE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->scan_source = pre_copy_job_request->scan_source;
            copy_Log( " set copy_param:%d\n", 0 );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_collate( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    int collate;
    COPY_PARAM_JOB_TYPE copy_type;
    STAPLE_MODE_7265_E staple_mode;
    FOLD_MODE_7265_E fold_mode;
    SHIFT_MODE_E shift;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    collate =  copy_job_request->collate;
    copy_type = copy_job_request->copy_type;
    staple_mode = copy_job_request->staple_mode;
    fold_mode = copy_job_request->fold_mode;
    shift = copy_job_request->shift_mode;

    if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
    {
        if( collate == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_COLLATE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->collate = 1;
            copy_Log( " set copy_param:%d\n", 1 );
        }
    }
    else if( staple_mode != STAPLE_MODE_7265_OFF )
    {
        if( collate == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_COLLATE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->collate = 1;
            copy_Log( " set copy_param:%d\n", 1 );
        }
    }
    else if( fold_mode == FOLD_MODE_7265_2 || fold_mode == FOLD_7265_MIDDLE_2_STAPLE )
    {
        if( collate == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_COLLATE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->collate = 1;
            copy_Log( " set copy_param:%d\n", 1 );
        }
    }
    else if( shift != 0 )
    {
        if( collate == 0 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_COLLATE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->collate = pre_copy_job_request->collate;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->collate );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_copies_flip( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    COPY_PARAM_JOB_TYPE copy_type;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    copy_type = copy_job_request->copy_type;
    if( copy_type == COPY_PARAM_JOB_TYPE_BOOKLET )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_BOOKLET_COPIES_FLIP;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->copies_flip = pre_copy_job_request->copies_flip;
        copy_Log( " set copy_param:%d\n", 0 );
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_page_type( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    //COPY_PARAM_JOB_TYPE copy_type;
    PAPER_TYPE_7265_E paper_type;
    STAPLE_MODE_7265_E staple_mode;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    TRAY_INPUT_7265_E print_tray;
    PUNCH_MODE_7265_E punch_mode;
    FOLD_MODE_7265_E fold_mode;
    SHIFT_MODE_E shift;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    paper_type = copy_job_request->page_type;
    staple_mode = copy_job_request->staple_mode;
    paper_size = copy_job_request->print_size;
    print_tray = copy_job_request->print_tray;
    punch_mode = copy_job_request->punch_mode;
    fold_mode = copy_job_request->fold_mode;
    shift = copy_job_request->shift_mode;

    if( staple_mode != STAPLE_MODE_7265_OFF )
    {
        if( PAPER_TYPE_7265_FILM == paper_type || PAPER_TYPE_7265_POST_CARD == paper_type
            || PAPER_TYPE_7265_ENVELOP == paper_type || PAPER_TYPE_7265_LABEL == paper_type )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_PAPER_TYPE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->page_type = pre_copy_job_request->page_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->page_type );
        }
    }
    else if( punch_mode != PUNCH_MODE_7265_OFF )
    {
        if( PAPER_TYPE_7265_FILM == paper_type || PAPER_TYPE_7265_POST_CARD == paper_type || PAPER_TYPE_7265_ENVELOP == paper_type
            || PAPER_TYPE_7265_LABEL == paper_type )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PUNCH_PAPER_TYPE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->page_type = pre_copy_job_request->page_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->page_type );
        }
    }
    else if( fold_mode != FOLD_MODE_7265_OFF )
    {
        if( PAPER_TYPE_7265_FILM == paper_type || PAPER_TYPE_7265_POST_CARD == paper_type || PAPER_TYPE_7265_ENVELOP == paper_type
            || PAPER_TYPE_7265_LABEL == paper_type )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_PAPER_TYPE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->page_type = pre_copy_job_request->page_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->page_type );
        }
    }
    else if( shift != 0 )
    {
        if( paper_type == PAPER_TYPE_7265_FILM || paper_type == PAPER_TYPE_7265_POST_CARD ||
            paper_type == PAPER_TYPE_7265_ENVELOP || paper_type == PAPER_TYPE_7265_LABEL )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_PAPER_TYPE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->page_type = pre_copy_job_request->page_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->page_type );
        }
    }
    else
    {
        if( is_print_size_mutex_paper_type( paper_size, paper_type ) )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_PAPER_TYPE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->page_type = pre_copy_job_request->page_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->page_type );
        }
        else if( is_print_tray_mutex_paper_type( print_tray, paper_type ) )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_TRAY_PAPER_TYPE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->page_type = pre_copy_job_request->page_type;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->page_type );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_print_tray( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    //COPY_PARAM_JOB_TYPE copy_type;
    PAPER_TYPE_7265_E paper_type;
    //STAPLE_MODE_7265_E staple_mode;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    TRAY_INPUT_7265_E print_tray;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    paper_type = copy_job_request->page_type;
    //staple_mode = copy_job_request->staple_mode;
    paper_size = copy_job_request->print_size;
    print_tray = copy_job_request->print_tray;

    if( is_print_size_mutex_tray( paper_size, print_tray ) )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_PRINT_TRAY;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->print_tray = pre_copy_job_request->print_tray;
        copy_Log( " set copy_param:%u\n", copy_job_request->print_tray );
    }
    else if( is_print_tray_mutex_paper_type( print_tray, paper_type ) )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_PAPER_TYPE_PRINT_TRAY;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->print_tray = pre_copy_job_request->print_tray;
        copy_Log( " set copy_param:%u\n", copy_job_request->print_tray );
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_tray_receive( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    STAPLE_MODE_7265_E staple_mode;
    TRAY_RECEIVE_E tray_receive;
    PUNCH_MODE_7265_E punch_mode;
    FOLD_MODE_7265_E fold_mode;
    SHIFT_MODE_E shift;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    int separator;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    staple_mode = copy_job_request->staple_mode;
    punch_mode = copy_job_request->punch_mode;
    fold_mode = copy_job_request->fold_mode;
    tray_receive = copy_job_request->tray_receive;
    shift = copy_job_request->shift_mode;
    paper_size = copy_job_request->print_size;
    separator = copy_job_request->separator;

    if( staple_mode != STAPLE_MODE_7265_OFF )
    {
        if( tray_receive != TRAY_RECEIVE_2 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_TRAY_RECEIVE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->tray_receive = pre_copy_job_request->tray_receive;
            copy_Log( " set copy_param:%u\n", copy_job_request->tray_receive );
        }
    }
    else if( punch_mode != PUNCH_MODE_7265_OFF )
    {
        if( tray_receive == TRAY_RECEIVE_STANDARD || tray_receive == TRAY_RECEIVE_3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PUNCH_TRAY_RECEIVE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->tray_receive = pre_copy_job_request->tray_receive;
            copy_Log( " set copy_param:%u\n", copy_job_request->tray_receive );
        }
    }
    else if( fold_mode == FOLD_MODE_7265_2 || fold_mode == FOLD_7265_MIDDLE_2_STAPLE )
    {
        if( tray_receive != TRAY_RECEIVE_3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_TRAY_RECEIVE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->tray_receive = pre_copy_job_request->tray_receive;
            copy_Log( " set copy_param:%u\n", copy_job_request->tray_receive );
        }
    }
    else if( shift != 0 )
    {
        if( tray_receive != TRAY_RECEIVE_2 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_TRAY_RECEIVE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->tray_receive = pre_copy_job_request->tray_receive;
            copy_Log( " set copy_param:%u\n", copy_job_request->tray_receive );
        }
    }
    else if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_USER_DEFINE || paper_size == PAPER_SIZE_7265_USER_DEFINE1 ||
             paper_size == PAPER_SIZE_7265_USER_DEFINE2  || paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
    {
        if( tray_receive == TRAY_RECEIVE_2 || tray_receive == TRAY_RECEIVE_3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_SIZE_TRAY_RECEIVE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->tray_receive = pre_copy_job_request->tray_receive;
            copy_Log( " set copy_param:%u\n", copy_job_request->tray_receive );
        }
    }
    else if( separator != 0 )
    {
        if( tray_receive == TRAY_RECEIVE_3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SEPARATOR_TRAY_RECEIVE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->tray_receive = pre_copy_job_request->tray_receive;
            copy_Log( " set copy_param:%u\n", copy_job_request->tray_receive );
        }
    }

    return copy_param_is_mutex;
}

int copy_param_is_mutex_print_size( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int copy_param_is_mutex = 0;
    //COPY_PARAM_JOB_TYPE copy_type;
    PAPER_TYPE_7265_E paper_type;
    STAPLE_MODE_7265_E staple_mode;
    COPY_PARAM_PAPER_SIZE_7265_E paper_size;
    TRAY_INPUT_7265_E print_tray;
    COPY_PARAM_PAPER_SIZE_7265_E scan_size;
    PUNCH_MODE_7265_E punch_mode;
    FOLD_MODE_7265_E fold_mode;
    SHIFT_MODE_E shift;
    TRAY_RECEIVE_E tray_receive;

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log("can not find pedk copy job req\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;

    paper_type = copy_job_request->page_type;
    staple_mode = copy_job_request->staple_mode;
    paper_size = copy_job_request->print_size;
    print_tray = copy_job_request->print_tray;
    scan_size = copy_job_request->scan_size;
    punch_mode = copy_job_request->punch_mode;
    fold_mode = copy_job_request->fold_mode;
    shift = copy_job_request->shift_mode;
    tray_receive = copy_job_request->tray_receive;

    if( staple_mode != STAPLE_MODE_7265_OFF )
    {
        if( check_image_orientation_mutex_staple( scan_size, paper_size, staple_mode, param_id ) )
        {
            copy_param_is_mutex = 1;
        }
        else if( PAPER_SIZE_7265_A6 == paper_size || PAPER_SIZE_7265_JIS_B6 == paper_size || PAPER_SIZE_7265_STATEMENT == paper_size
                 || PAPER_SIZE_7265_INVOICE == paper_size || PAPER_SIZE_7265_A5 == paper_size || PAPER_SIZE_7265_INVOICE_L == paper_size
                 || PAPER_SIZE_7265_A3_WIDE1 == paper_size || PAPER_SIZE_7265_USER_DEFINE == paper_size || PAPER_SIZE_7265_USER_DEFINE1 == paper_size
                 || PAPER_SIZE_7265_USER_DEFINE2 == paper_size || PAPER_SIZE_7265_USER_DEFINE3 == paper_size )
        {
            copy_param_is_mutex = 1;
        }

        if( copy_param_is_mutex )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_STAPLE_PRINT_SIZE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_size = pre_copy_job_request->print_size;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
        }

    }
    else if( punch_mode != PUNCH_MODE_7265_OFF )
    {
        if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_JIS_B6 || paper_size == PAPER_SIZE_7265_STATEMENT ||
            paper_size == PAPER_SIZE_7265_INVOICE || paper_size == PAPER_SIZE_7265_A5
            || paper_size == PAPER_SIZE_7265_JIS_B5 ||
            paper_size == PAPER_SIZE_7265_USER_DEFINE || paper_size == PAPER_SIZE_7265_USER_DEFINE1 || paper_size == PAPER_SIZE_7265_USER_DEFINE2 ||
            paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
        {
            copy_param_is_mutex = 1;
        }
        else if( punch_mode == PUNCH_MODE_7265_FOUR_HOLES )
        {
            if( paper_size == PAPER_SIZE_7265_EXECUTIVE || paper_size == PAPER_SIZE_7265_CUSTOM_BIG_16K || paper_size == PAPER_SIZE_7265_A5L ||
                paper_size == PAPER_SIZE_7265_A4 || paper_size == PAPER_SIZE_7265_INVOICE_L
                || paper_size == PAPER_SIZE_7265_LETTER ||
                paper_size == PAPER_SIZE_7265_FOLIO || paper_size == PAPER_SIZE_7265_FOOLSCAP3 || paper_size == PAPER_SIZE_7265_LEGAL13 )
            {
                copy_param_is_mutex = 1;
            }
        }

        if( copy_param_is_mutex )
        {
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_PUNCH_PRINT_SIZE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_size = pre_copy_job_request->print_size;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
        }
    }
    else if( shift != 0 )
    {
        if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_JIS_B6 || paper_size == PAPER_SIZE_7265_STATEMENT ||
            paper_size == PAPER_SIZE_7265_INVOICE || paper_size == PAPER_SIZE_7265_A5 || paper_size == PAPER_SIZE_7265_USER_DEFINE ||
            paper_size == PAPER_SIZE_7265_USER_DEFINE1 || paper_size == PAPER_SIZE_7265_USER_DEFINE2 || paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_SHIFT_PRINT_SIZE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_size = pre_copy_job_request->print_size;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
        }
    }
    else if( fold_mode == FOLD_MODE_7265_2 || fold_mode == FOLD_7265_MIDDLE_2_STAPLE )
    {
        if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_JIS_B6 || paper_size == PAPER_SIZE_7265_STATEMENT ||
            paper_size == PAPER_SIZE_7265_INVOICE || paper_size == PAPER_SIZE_7265_A5
            || paper_size == PAPER_SIZE_7265_JIS_B5 ||
            paper_size == PAPER_SIZE_7265_EXECUTIVE || paper_size == PAPER_SIZE_7265_CUSTOM_BIG_16K || paper_size == PAPER_SIZE_7265_A5L ||
            paper_size == PAPER_SIZE_7265_INVOICE_L || paper_size == PAPER_SIZE_7265_JIS_B5L || paper_size == PAPER_SIZE_7265_CUSTOM_BIG_16KL ||
            paper_size == PAPER_SIZE_7265_LETTER_L || paper_size == PAPER_SIZE_7265_A4L || paper_size == PAPER_SIZE_7265_USER_DEFINE ||
            paper_size == PAPER_SIZE_7265_USER_DEFINE1 || paper_size == PAPER_SIZE_7265_USER_DEFINE2 || paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_FOLD_PRINT_SIZE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_size = pre_copy_job_request->print_size;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
        }
    }
    else if( is_print_size_mutex_paper_type( paper_size, paper_type ) )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_PAPER_TYPE_PRINT_SIZE;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->print_size = pre_copy_job_request->print_size;
        copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
    }
    else if( is_print_size_mutex_tray( paper_size, print_tray ) )
    {
        copy_param_is_mutex = 1;
        s_copy_param_mutex_type = COPY_PARAM_MUTEX_PRINT_TRAY_PRINT_SIZE;
        copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

        copy_job_request->print_size = pre_copy_job_request->print_size;
        copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
    }
    else if( tray_receive == TRAY_RECEIVE_2 || tray_receive == TRAY_RECEIVE_3 )
    {
        if( paper_size == PAPER_SIZE_7265_A6 || paper_size == PAPER_SIZE_7265_USER_DEFINE || paper_size == PAPER_SIZE_7265_USER_DEFINE1 ||
            paper_size == PAPER_SIZE_7265_USER_DEFINE2  || paper_size == PAPER_SIZE_7265_USER_DEFINE3 )
        {
            copy_param_is_mutex = 1;
            s_copy_param_mutex_type = COPY_PARAM_MUTEX_TRAY_RECEIVE_PRINT_SIZE;
            copy_Log( " %s\n", copy_param_mutex_tips[s_copy_param_mutex_type].description );

            copy_job_request->print_size = pre_copy_job_request->print_size;
            copy_Log( " set copy_param:%u\n", pre_copy_job_request->print_size );
        }
    }

    return copy_param_is_mutex;
}


/* single_copy_param type is 7265 project  */
COPY_PARAM_MUTEX_INDEX s_copy_param_mutex_table_7265[] =
{
    { SINGLE_COPY_PARAM_COPY_TYPE,          copy_param_is_mutex_nup_type },
    { SINGLE_COPY_PARAM_COPY_TYPE,          copy_param_is_mutex_copy_type_booklet },
    { SINGLE_COPY_PARAM_COPY_TYPE,          copy_param_is_mutex_mix },
    { SINGLE_COPY_PARAM_COPY_TYPE,          copy_param_is_mutex_idcard },

    { SINGLE_COPY_PARAM_NUP_TYPE,           copy_param_is_mutex_nup_type },
    { SINGLE_COPY_PARAM_NUP_COMBINATION,    copy_param_is_mutex_nup_type },

    { SINGLE_COPY_PARAM_PRINT_MODE,         copy_param_is_mutex_print_mode },

    { SINGLE_COPY_PARAM_SCALE_PERCENT,      copy_param_is_mutex_scale },
    { SINGLE_COPY_PARAM_AUTO_SCALE_MODE,    copy_param_is_mutex_scale },

    { SINGLE_COPY_PARAM_STAPLE_ANGLE,       copy_param_is_mutex_staple },
    { SINGLE_COPY_PARAM_STAPLE_MODE,        copy_param_is_mutex_staple },
    { SINGLE_COPY_PARAM_STAPLE_NUM,         copy_param_is_mutex_staple },

    { SINGLE_COPY_PARAM_SHIFT_MODE,         copy_param_is_mutex_shift },

    { SINGLE_COPY_PARAM_PUNCH_MODE,         copy_param_is_mutex_punch },
    { SINGLE_COPY_PARAM_PUNCH_NUM,          copy_param_is_mutex_punch },

    { SINGLE_COPY_PARAM_FOLD_NUMBER,        copy_param_is_mutex_fold },
    { SINGLE_COPY_PARAM_FOLD_MODE,          copy_param_is_mutex_fold },

    { SINGLE_COPY_PARAM_COVER,              copy_param_is_mutex_cover },
    { SINGLE_COPY_PARAM_COVER_BACK,         copy_param_is_mutex_cover },
    { SINGLE_COPY_PARAM_COVER_PAPER_TYPE,   copy_param_is_mutex_cover },
    { SINGLE_COPY_PARAM_COVER_TRAY_IN,      copy_param_is_mutex_cover },

    { SINGLE_COPY_PARAM_HORIZONTAL_MARGIN,  copy_param_is_mutex_edge_adjust },
    { SINGLE_COPY_PARAM_VERTICAL_MARGIN,    copy_param_is_mutex_edge_adjust },

    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_TOP,     copy_param_is_mutex_edge_clear },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_BOTTOM,  copy_param_is_mutex_edge_clear },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_LEFT,    copy_param_is_mutex_edge_clear },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_RIGHT,   copy_param_is_mutex_edge_clear },

    { SINGLE_COPY_PARAM_EDGE_TO_EDGE_MODE,          copy_param_is_mutex_edge_to_edge },

    { SINGLE_COPY_PARAM_SCAN_SIZE,                  copy_param_is_mutex_mix },
    { SINGLE_COPY_PARAM_SCAN_SIZE,                  copy_param_is_mutex_scan_size },

    { SINGLE_COPY_PARAM_SEPARATOR,                  copy_param_is_mutex_separator },

    { SINGLE_COPY_PARAM_SCAN_SOURCE,                copy_param_is_mutex_scan_source },

    { SINGLE_COPY_PARAM_COLLATE,                    copy_param_is_mutex_collate },

    { SINGLE_COPY_PARAM_COPIES_FLIP,                copy_param_is_mutex_copies_flip },

    { SINGLE_COPY_PARAM_PAGE_TYPE,                  copy_param_is_mutex_page_type },

    { SINGLE_COPY_PARAM_PRINT_SIZE,                 copy_param_is_mutex_print_size },

    { SINGLE_COPY_PARAM_PRINT_TRAY,                 copy_param_is_mutex_print_tray },

    { SINGLE_COPY_PARAM_TRAY_RECEIVE,               copy_param_is_mutex_tray_receive },

    { SINGLE_COPY_PARAM_COPY_PARAM_MAX,   NULL },

};




/**
* @brief   1. according machine type judge mutex \n
           2. if mutex set other param value to start job \n
               2.1.  7265 param to 7265 param  \n
               2.2.  set src(7265) param value  \n
               2.3.  set 7265 param value       \n
           3. for subsequent, set status???
* @param[in] SINGLE_COPY_PARAM_E copy param type
* @param[out] void
* @return result
* <AUTHOR>
* @date 2023-12-20
*/
int copy_param_relationship_mutex_7265( SINGLE_COPY_PARAM_E copy_param, void* src_param, int len, int param_id )
{
    int ret = 0;
    int i = 0;
    SINGLE_COPY_PARAM_E pre_param = s_copy_param_mutex_table_7265[0].copy_param;

    for( i = 0; i < sizeof( s_copy_param_mutex_table_7265 ) / sizeof( s_copy_param_mutex_table_7265[0] ); i++ )
    {
        if( s_copy_param_mutex_table_7265[i].copy_param == copy_param )
        {
            ret = s_copy_param_mutex_table_7265[i].copy_param_is_muxtex( copy_param, src_param, len, param_id );
            if( ret != 0 )
            {
                return ret;
            }
            pre_param = s_copy_param_mutex_table_7265[i].copy_param;
        }
        //must sure same copy_param is together
        if( pre_param == copy_param && pre_param != s_copy_param_mutex_table_7265[i].copy_param )
        {
            break;
        }
    }

    /*
    if( s_copy_param_mutex_table_7265[i].copy_param == SINGLE_COPY_PARAM_COPY_PARAM_MAX && pre_param != s_copy_param_mutex_table_7265[i - 1].copy_param )
    {
        copy_Log( "can not find mutex copy param:%d\n", copy_param );
        ret =  -1;
    }
    */

    return ret;
}



/**
* @brief   1. according machine type judge mutex \n
           2. if mutex set other param value to start job \n
               2.1.    set src param value \n
           3. for subsequent, set status???
* @param[in] SINGLE_COPY_PARAM_E copy param type
* @param[out] void
* @return result
* <AUTHOR>
* @date 2023-12-20
*/
/*
int copy_param_relationship_mutex_7265( SINGLE_COPY_PARAM_E copy_param, void* src_param, int len )
{

//wait to completed

    return 0;
}
*/

int copy_param_relationship_mutex( SINGLE_COPY_PARAM_E copy_param, void* src_param, int len, int param_id )
{
    int ret = 0;
    int i = 0;
    SINGLE_COPY_PARAM_E pre_param = s_copy_param_mutex_table_7265[0].copy_param;

    for( i = 0; i < sizeof( s_copy_param_mutex_table_7265 ) / sizeof( s_copy_param_mutex_table_7265[0] ); i++ )
    {
        if( s_copy_param_mutex_table_7265[i].copy_param == copy_param )
        {
            ret = s_copy_param_mutex_table_7265[i].copy_param_is_muxtex( copy_param, src_param, len, param_id );
            if( ret != 0 )
            {
                return ret;
            }
            pre_param = s_copy_param_mutex_table_7265[i].copy_param;
        }
        //must sure same copy_param is together
        if( pre_param == copy_param && pre_param != s_copy_param_mutex_table_7265[i].copy_param )
        {
            break;
        }
    }

    /*
    if( s_copy_param_mutex_table_7265[i].copy_param == SINGLE_COPY_PARAM_COPY_PARAM_MAX && pre_param != s_copy_param_mutex_table_7265[i - 1].copy_param )
    {
        copy_Log( "can not find mutex copy param:%d\n", copy_param );
        ret =  -1;
    }
    */

    return ret;
}


int set_copy_param_copy_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }

    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;

    pre_value = copy_job_request->copy_type;
    pedk_copy_job_request->pre_copy_job_req.copy_type = pre_value;
    memcpy( ( char* )&copy_job_request->copy_type, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COPY_TYPE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->copy_type, len );

    return ret;
}



int set_copy_param_copies( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->copies;
    pedk_copy_job_request->pre_copy_job_req.copies = pre_value;
    memcpy( ( char* )&copy_job_request->copies, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COPIES, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->copies, len );
    }

    return ret;
}

int set_copy_param_color_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->color_mode;
    pedk_copy_job_request->pre_copy_job_req.color_mode = pre_value;
    memcpy( ( char* )&copy_job_request->color_mode, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COLOR_MODE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->color_mode, len );
    }
    return ret;
}


int set_copy_param_scan_source( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->scan_source;
    pedk_copy_job_request->pre_copy_job_req.scan_source = pre_value;
    memcpy( ( char* )&copy_job_request->scan_source, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCAN_SOURCE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->scan_source, len );
    }
    copy_Log( "scan source:%u\n", copy_job_request->scan_source );

    return ret;
}


int set_copy_param_scan_size( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->scan_size;
    pedk_copy_job_request->pre_copy_job_req.scan_size = pre_value;
    memcpy( ( char* )&copy_job_request->scan_size, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCAN_SIZE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->scan_size, len );
        copy_Log( "scan_size:%u\n", copy_job_request->scan_size );
    }
    return ret;
}



int set_copy_param_image_orientation( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->image_orientation;
    pedk_copy_job_request->pre_copy_job_req.image_orientation = pre_value;
    memcpy( ( char* )&copy_job_request->image_orientation, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_IMAGE_ORIENTATION, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->image_orientation, len );
        copy_Log( "image_orientation:%u\n", copy_job_request->image_orientation );
    }
    return ret;
}

int set_print_tray_by_auto( COPY_JOB_REQUEST_DATA_S* copy_job_request )
{
    int ret = 0;

    //can not get tray status and tray paper size
    //so only print in standard in auto standard
    if( copy_job_request->print_tray == TRAY_INPUT_7265_AUTO )
    {
        copy_job_request->print_tray = TRAY_INPUT_7265_STANDARD;
    }

    return ret;
}

int set_copy_param_print_tray( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;

    pre_value = copy_job_request->print_tray;
    memcpy( ( char* )&copy_job_request->print_tray, ( char* )copy_param, len );

    ret = set_print_tray_by_auto( copy_job_request );
    pedk_copy_job_request->pre_copy_job_req.print_tray = pre_value;

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PRINT_TRAY, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->print_tray, len );
    }
    return ret;
}

int set_copy_param_print_size( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->print_size;
    pedk_copy_job_request->pre_copy_job_req.print_size = pre_value;
    memcpy( ( char* )&copy_job_request->print_size, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PRINT_SIZE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->print_size, len );
        copy_Log( "print_size:%u\n", copy_job_request->print_size );
    }
    return ret;
}

int set_copy_param_page_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_type;
    pedk_copy_job_request->pre_copy_job_req.page_type = pre_value;
    memcpy( ( char* )&copy_job_request->page_type, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_TYPE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_type, len );
    }
    return ret;
}

int set_copy_param_print_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->print_mode;
    pedk_copy_job_request->pre_copy_job_req.print_mode = pre_value;
    memcpy( ( char* )&copy_job_request->print_mode, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PRINT_MODE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->print_mode, len );
        copy_Log( "print_mode:%u\n", copy_job_request->print_mode );
    }
    return ret;
}

int set_copy_param_auto_scale_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->auto_scale_mode;
    pedk_copy_job_request->pre_copy_job_req.auto_scale_mode = pre_value;
    memcpy( ( char* )&copy_job_request->auto_scale_mode, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_AUTO_SCALE_MODE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->auto_scale_mode, len );
    }
    return ret;
}


int set_copy_param_quality( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->quality;
    pedk_copy_job_request->pre_copy_job_req.quality = pre_value;
    memcpy( ( char* )&copy_job_request->quality, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_QUALITY, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->quality, len );
    }
    return ret;
}

int set_copy_param_color_balance_c( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->color_balance_c;
    pedk_copy_job_request->pre_copy_job_req.color_balance_c = pre_value;
    memcpy( ( char* )&copy_job_request->color_balance_c, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COLOR_BALANCE_C, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->color_balance_c, len );
    }
    return ret;
}


int set_copy_param_color_balance_m( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->color_balance_m;
    pedk_copy_job_request->pre_copy_job_req.color_balance_m = pre_value;
    memcpy( ( char* )&copy_job_request->color_balance_m, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COLOR_BALANCE_M, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->color_balance_m, len );
    }
    return ret;
}

int set_copy_param_color_balance_y( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;

    pre_value = copy_job_request->color_balance_y;
    pedk_copy_job_request->pre_copy_job_req.color_balance_y = pre_value;
    memcpy( ( char* )&copy_job_request->color_balance_y, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COLOR_BALANCE_Y, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->color_balance_y, len );
    }
    return ret;
}

int set_copy_param_color_balance_k( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->color_balance_k;
    pedk_copy_job_request->pre_copy_job_req.color_balance_k = pre_value;
    memcpy( ( char* )&copy_job_request->color_balance_k, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COLOR_BALANCE_K, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->color_balance_k, len );
    }
    return ret;
}


int set_copy_param_image_brightness( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->image_brightness;
    pedk_copy_job_request->pre_copy_job_req.image_brightness = pre_value;
    memcpy( ( char* )&copy_job_request->image_brightness, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_IMAGE_BRIGHTNESS, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->image_brightness, len );
    }
    return ret;
}

int set_copy_param_image_saturation( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->image_saturation;
    pedk_copy_job_request->pre_copy_job_req.image_saturation = pre_value;
    memcpy( ( char* )&copy_job_request->image_saturation, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_IMAGE_SATURATION, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->image_saturation, len );
    }
    return ret;
}

int set_copy_param_image_contrast( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->image_contrast;
    pedk_copy_job_request->pre_copy_job_req.image_contrast = pre_value;
    memcpy( ( char* )&copy_job_request->image_contrast, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_IMAGE_CONTRAST, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->image_contrast, len );
    }
    return ret;
}

int set_copy_param_image_sharpness( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->image_sharpness;
    pedk_copy_job_request->pre_copy_job_req.image_sharpness = pre_value;
    memcpy( ( char* )&copy_job_request->image_sharpness, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_IMAGE_SHARPNESS, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->image_sharpness, len );
    }
    return ret;
}

int set_copy_param_image_hue( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->image_hue;
    pedk_copy_job_request->pre_copy_job_req.image_hue = pre_value;
    memcpy( ( char* )&copy_job_request->image_hue, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_IMAGE_HUE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->image_hue, len );
    }
    return ret;
}

int set_copy_param_backgroundmove_level( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->backgroundmove_level;
    pedk_copy_job_request->pre_copy_job_req.backgroundmove_level = pre_value;
    memcpy( ( char* )&copy_job_request->backgroundmove_level, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_BACKGROUNDMOVE_LEVEL, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->backgroundmove_level, len );
    }
    return ret;
}

int set_copy_param_save_toner_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->save_toner_mode;
    pedk_copy_job_request->pre_copy_job_req.save_toner_mode = pre_value;
    memcpy( ( char* )&copy_job_request->save_toner_mode, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SAVE_TONER_MODE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->save_toner_mode, len );
    }
    return ret;
}


int set_copy_param_horizontal_margin( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->horizontal_margin;
    pedk_copy_job_request->pre_copy_job_req.horizontal_margin = pre_value;
    memcpy( ( char* )&copy_job_request->horizontal_margin, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_HORIZONTAL_MARGIN, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->horizontal_margin, len );
    }
    return ret;
}

int set_copy_param_vertical_margin( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->vertical_margin;
    pedk_copy_job_request->pre_copy_job_req.vertical_margin = pre_value;
    memcpy( ( char* )&copy_job_request->vertical_margin, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_VERTICAL_MARGIN, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->vertical_margin, len );
    }
    return ret;
}


int set_copy_param_edge_to_edge_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->edge_to_edge_mode;
    pedk_copy_job_request->pre_copy_job_req.edge_to_edge_mode = pre_value;
    memcpy( ( char* )&copy_job_request->edge_to_edge_mode, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_EDGE_TO_EDGE_MODE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->edge_to_edge_mode, len );
    }
    return ret;
}


int set_copy_param_collate( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->collate;
    pedk_copy_job_request->pre_copy_job_req.collate = pre_value;
    memcpy( ( char* )&copy_job_request->collate, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COLLATE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->collate, len );
    }
    return ret;
}

int set_copy_param_separator( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->separator;
    pedk_copy_job_request->pre_copy_job_req.separator = pre_value;
    memcpy( ( char* )&copy_job_request->separator, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SEPARATOR, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->separator, len );
    }
    return ret;
}

int get_7265_nup_type_on_PEDK( COPY_JOB_REQUEST_DATA_S* copy_job_request )
{
    int ret = 0;

    if( copy_job_request->nup_combination == 0 )
    {
        copy_Log( "nup mode not open\n" );
        return ret;
    }

    if( copy_job_request->nup_type == COPY_KANAS_NUP_4IN1_LANDSCAPE )
    {
        copy_job_request->nup_type =  COPY_NUP_7265_4IN1;
        copy_job_request->nup_combination = COPY_NUP_COMBINATION_7265_HORIZONTAL;
    }
    else if( copy_job_request->nup_type == COPY_KANAS_NUP_4IN1_PORTRAIT )
    {
        copy_job_request->nup_type =  COPY_NUP_7265_4IN1;
        copy_job_request->nup_combination = COPY_NUP_COMBINATION_7265_PORTRAIT;
    }
    else if( copy_job_request->nup_type == COPY_KANAS_NUP_2IN1_LANDSCAPE || copy_job_request->nup_type == COPY_KANAS_NUP_2IN1_PORTRAIT)
    {
        copy_job_request->nup_type =  COPY_NUP_7265_2IN1;
        copy_job_request->nup_combination = COPY_NUP_COMBINATION_7265_HORIZONTAL;
    }
    else
    {
        copy_job_request->nup_type =  COPY_NUP_7265_CLOSE;
        copy_job_request->nup_combination = COPY_NUP_COMBINATION_7265_OFF;
    }

    return ret;
}

int set_copy_param_nup_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->nup_type;
    memcpy( ( char* )&copy_job_request->nup_type, ( char* )copy_param, len );

    //get_7265_nup_type_on_PEDK( copy_job_request );
    pedk_copy_job_request->pre_copy_job_req.nup_type = pre_value;

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_NUP_TYPE, copy_param, len, param_id );
        memcpy( ( char* )copy_param, ( char* )&copy_job_request->nup_type, len );
    }
    copy_Log( "nup combination:%u nup_type:%u\n", copy_job_request->nup_combination, copy_job_request->nup_type );
    return ret;
}

int set_copy_param_nup_combination( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->nup_combination;
    pedk_copy_job_request->pre_copy_job_req.nup_combination = pre_value;
    memcpy( ( char* )&copy_job_request->nup_combination, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_NUP_COMBINATION, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->nup_combination, len );
    }
    copy_Log( " nup combination:%u nup_type:%u\n", copy_job_request->nup_combination, copy_job_request->nup_type );
    return ret;
}

int set_copy_param_poster_size( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->poster_size;
    pedk_copy_job_request->pre_copy_job_req.poster_size = pre_value;
    memcpy( ( char* )&copy_job_request->poster_size, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_POSTER_SIZE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->poster_size, len );
    }
    return ret;
}


int set_copy_param_watermark_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->watermark_mode;
    pedk_copy_job_request->pre_copy_job_req.watermark_mode = pre_value;
    memcpy( ( char* )&copy_job_request->watermark_mode, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_WATERMARK_MODE, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->watermark_mode, len );
    }
    copy_Log( "copy_job_request->watermark_mode:%u\n", copy_job_request->watermark_mode );
    return ret;
}

int set_copy_param_watermark_string( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    char pre_value[128];
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    if( copy_job_request->watermark_mode == 0 )
    {
        ret = 1;
        copy_Log( "watermark mode not open\n" );
        memcpy( ( char* )copy_param, ( char* )&copy_job_request->watermark_string, len );
        return ret;
    }
    memcpy( pre_value, ( char* )copy_job_request->watermark_string, len );
    memcpy( ( char* )pedk_copy_job_request->pre_copy_job_req.watermark_string, pre_value, len );
    memcpy( ( char* )&copy_job_request->watermark_string, ( char* )copy_param, len );

    if( ret == 0 )
    {
        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_WATERMARK_STRING, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->watermark_string, len );
    }
    return ret;
}

int set_copy_param_booklet_duplex( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->booklet_duplex;
    pedk_copy_job_request->pre_copy_job_req.booklet_duplex = pre_value;
    memcpy( ( char* )&copy_job_request->booklet_duplex, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_BOOKLET_DUPLEX, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->booklet_duplex, len );

    return ret;
}

int set_copy_param_mirror_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->mirror_mode;
    pedk_copy_job_request->pre_copy_job_req.mirror_mode = pre_value;
    memcpy( ( char* )&copy_job_request->mirror_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_MIRROR_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->mirror_mode, len );

    return ret;
}

int set_copy_param_clone_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->clone_mode;
    pedk_copy_job_request->pre_copy_job_req.clone_mode = pre_value;
    memcpy( ( char* )&copy_job_request->clone_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_CLONE_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->clone_mode, len );

    return ret;
}

int set_copy_param_skewing_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->skewing_mode;
    pedk_copy_job_request->pre_copy_job_req.skewing_mode = pre_value;
    memcpy( ( char* )&copy_job_request->skewing_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SKEWING_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->skewing_mode, len );

    return ret;
}

int set_copy_param_page_header_enable( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_header.enable;
    pedk_copy_job_request->pre_copy_job_req.page_header.enable = pre_value;
    memcpy( ( char* )&copy_job_request->page_header.enable, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_HEADER_ENABLE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_header.enable, len );

    return ret;
}

int set_copy_param_page_header_position( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_header.position;
    pedk_copy_job_request->pre_copy_job_req.page_header.position = pre_value;
    memcpy( ( char* )&copy_job_request->page_header.position, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_HEADER_POSITION, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_header.position, len );

    return ret;
}

int set_copy_param_page_header_pagination( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_header.pagination;
    pedk_copy_job_request->pre_copy_job_req.page_header.pagination = pre_value;
    memcpy( ( char* )&copy_job_request->page_header.pagination, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_HEADER_PAGINATION, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_header.pagination, len );

    return ret;
}

int set_copy_param_page_header_text_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_header.text_type;
    pedk_copy_job_request->pre_copy_job_req.page_header.text_type = pre_value;
    memcpy( ( char* )&copy_job_request->page_header.text_type, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_HEADER_TEXT_TYPE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_header.text_type, len );

    return ret;
}

int set_copy_param_page_header_text( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    char pre_value[128];
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    memcpy( ( char* )pre_value, ( char* )copy_job_request->page_header.text, len );
    memcpy( ( char* )pedk_copy_job_request->pre_copy_job_req.page_header.text, ( char* )pre_value, len );
    memcpy( ( char* )copy_job_request->page_header.text, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_HEADER_TEXT, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )copy_job_request->page_header.text, len );

    return ret;
}

int set_copy_param_page_footer_enable( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_footer.enable;
    pedk_copy_job_request->pre_copy_job_req.page_footer.enable = pre_value;
    memcpy( ( char* )&copy_job_request->page_footer.enable, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_FOOTER_ENABLE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_footer.enable, len );

    return ret;
}

int set_copy_param_page_footer_position( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_footer.position;
    pedk_copy_job_request->pre_copy_job_req.page_footer.position = pre_value;
    memcpy( ( char* )&copy_job_request->page_footer.position, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_FOOTER_POSITION, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_footer.position, len );

    return ret;
}

int set_copy_param_page_footer_pagination( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_footer.pagination;
    pedk_copy_job_request->pre_copy_job_req.page_footer.pagination = pre_value;
    memcpy( ( char* )&copy_job_request->page_footer.pagination, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_FOOTER_PAGINATION, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_footer.pagination, len );

    return ret;
}

int set_copy_param_page_footer_text_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->page_footer.text_type;
    pedk_copy_job_request->pre_copy_job_req.page_footer.text_type = pre_value;
    memcpy( ( char* )&copy_job_request->page_footer.text_type, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT_TYPE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->page_footer.text_type, len );

    return ret;
}

int set_copy_param_page_footer_text( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    char pre_value[128];
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    memcpy( ( char* )pre_value, ( char* )copy_job_request->page_footer.text, len );
    memcpy( ( char* )pedk_copy_job_request->pre_copy_job_req.page_footer.text, ( char* )pre_value, len );
    memcpy( ( char* )copy_job_request->page_footer.text, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )copy_job_request->page_footer.text, len );

    return ret;
}

int set_copy_param_backup_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->backup_mode;
    pedk_copy_job_request->pre_copy_job_req.backup_mode = pre_value;
    memcpy( ( char* )&copy_job_request->backup_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_BACKUP_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->backup_mode, len );

    return ret;
}

int set_copy_param_sample_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->sample_mode;
    pedk_copy_job_request->pre_copy_job_req.sample_mode = pre_value;
    memcpy( ( char* )&copy_job_request->sample_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SAMPLE_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->sample_mode, len );

    return ret;
}

int set_copy_param_scale_percent( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->scale_percent;
    pedk_copy_job_request->pre_copy_job_req.scale_percent = pre_value;
    memcpy( ( char* )&copy_job_request->scale_percent, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCALE_PERCENT, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->scale_percent, len );

    return ret;
}

int set_copy_param_auto_id_correction( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->auto_id_correction;
    pedk_copy_job_request->pre_copy_job_req.auto_id_correction = pre_value;
    memcpy( ( char* )&copy_job_request->auto_id_correction, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_AUTO_ID_CORRECTION, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->auto_id_correction, len );

    return ret;
}

int set_copy_param_use_color_inversion( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->use_color_inversion;
    pedk_copy_job_request->pre_copy_job_req.use_color_inversion = pre_value;
    memcpy( ( char* )&copy_job_request->use_color_inversion, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_USE_COLOR_INVERSION, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->use_color_inversion, len );

    return ret;
}

int set_copy_param_use_copy_scan_meantime( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->use_copy_scan_meantime;
    pedk_copy_job_request->pre_copy_job_req.use_copy_scan_meantime = pre_value;
    memcpy( ( char* )&copy_job_request->use_copy_scan_meantime, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_USE_COPY_SCAN_MEANTIME, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->use_copy_scan_meantime, len );

    return ret;
}

int set_copy_param_scan_file_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->scan_file_type;
    pedk_copy_job_request->pre_copy_job_req.scan_file_type = pre_value;
    memcpy( ( char* )&copy_job_request->scan_file_type, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCAN_FILE_TYPE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->scan_file_type, len );

    return ret;
}

int set_copy_param_scan_send_router( void* value, int len, int param_id )
{
    char* copy_param = ( char* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    char* pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = (char*)&copy_job_request->scan_send_router[0];
    memcpy( (char*)&pedk_copy_job_request->pre_copy_job_req.scan_send_router[0], pre_value, len );
    memcpy( ( char* )&copy_job_request->scan_send_router[0], ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCAN_SEND_ROUTER, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->scan_send_router, len );

    return ret;
}

int set_copy_param_use_removel_color( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->use_removel_color;
    pedk_copy_job_request->pre_copy_job_req.use_removel_color = pre_value;
    memcpy( ( char* )&copy_job_request->use_removel_color, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_USE_REMOVEL_COLOR, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->use_removel_color, len );

    return ret;
}

int set_copy_param_remove_color_plane( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->remove_color_plane;
    pedk_copy_job_request->pre_copy_job_req.remove_color_plane = pre_value;
    memcpy( ( char* )&copy_job_request->remove_color_plane, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_REMOVE_COLOR_PLANE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->remove_color_plane, len );

    return ret;
}

int set_copy_param_use_edge_clean( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->use_edge_clean;
    pedk_copy_job_request->pre_copy_job_req.use_edge_clean = pre_value;
    memcpy( ( char* )&copy_job_request->use_edge_clean, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_USE_EDGE_CLEAN, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->use_edge_clean, len );

    return ret;
}

int set_copy_param_filter_edge_margin_top( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    if( copy_job_request->use_edge_clean == 1 )
    {
        pre_value = copy_job_request->filter_edge_margin_top;
        pedk_copy_job_request->pre_copy_job_req.filter_edge_margin_top = pre_value;
        memcpy( ( char* )&copy_job_request->filter_edge_margin_top, ( char* )copy_param, len );

        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_TOP, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->filter_edge_margin_top, len );
    }

    return ret;
}

int set_copy_param_filter_edge_margin_left( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    if( copy_job_request->use_edge_clean == 1 )
    {
        pre_value = copy_job_request->filter_edge_margin_left;
        pedk_copy_job_request->pre_copy_job_req.filter_edge_margin_left = pre_value;
        memcpy( ( char* )&copy_job_request->filter_edge_margin_left, ( char* )copy_param, len );

        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_LEFT, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->filter_edge_margin_left, len );
    }

    return ret;
}

int set_copy_param_filter_edge_margin_right( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    if( copy_job_request->use_edge_clean == 1 )
    {
        pre_value = copy_job_request->filter_edge_margin_right;
        pedk_copy_job_request->pre_copy_job_req.filter_edge_margin_right = pre_value;
        memcpy( ( char* )&copy_job_request->filter_edge_margin_right, ( char* )copy_param, len );

        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_RIGHT, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->filter_edge_margin_right, len );
    }

    return ret;
}

int set_copy_param_filter_edge_margin_bottom( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    if( copy_job_request->use_edge_clean == 1 )
    {
        pre_value = copy_job_request->filter_edge_margin_bottom;
        pedk_copy_job_request->pre_copy_job_req.filter_edge_margin_bottom = pre_value;
        memcpy( ( char* )&copy_job_request->filter_edge_margin_bottom, ( char* )copy_param, len );

        ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_BOTTOM, copy_param, len, param_id );

        memcpy( ( char* )copy_param, ( char* )&copy_job_request->filter_edge_margin_bottom, len );
    }

    return ret;
}

int set_copy_param_original_flip( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->original_flip;
    pedk_copy_job_request->pre_copy_job_req.original_flip = pre_value;
    memcpy( ( char* )&copy_job_request->original_flip, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_ORIGINAL_FLIP, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->original_flip, len );

    return ret;
}

int set_copy_param_copies_flip( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->copies_flip;
    pedk_copy_job_request->pre_copy_job_req.copies_flip = pre_value;
    memcpy( ( char* )&copy_job_request->copies_flip, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COPIES_FLIP, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->copies_flip, len );

    return ret;
}

int set_copy_param_cover( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->cover;
    pedk_copy_job_request->pre_copy_job_req.cover = pre_value;
    memcpy( ( char* )&copy_job_request->cover, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COVER, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->cover, len );

    return ret;
}

int set_copy_param_cover_back( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->cover_back;
    pedk_copy_job_request->pre_copy_job_req.cover_back = pre_value;
    memcpy( ( char* )&copy_job_request->cover_back, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COVER_BACK, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->cover_back, len );

    return ret;
}

int set_copy_param_cover_tray_in( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->cover_tray_in;
    pedk_copy_job_request->pre_copy_job_req.cover_tray_in = pre_value;
    memcpy( ( char* )&copy_job_request->cover_tray_in, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COVER_TRAY_IN, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->cover_tray_in, len );

    return ret;
}

int set_copy_param_staple_num( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->staple_num;
    pedk_copy_job_request->pre_copy_job_req.staple_num = pre_value;
    memcpy( ( char* )&copy_job_request->staple_num, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_STAPLE_NUM, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->staple_num, len );

    return ret;
}

int set_copy_param_staple_angle( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->staple_angle;
    pedk_copy_job_request->pre_copy_job_req.staple_angle = pre_value;
    memcpy( ( char* )&copy_job_request->staple_angle, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_STAPLE_ANGLE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->staple_angle, len );

    return ret;
}

int set_copy_param_punch_num( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->punch_num;
    pedk_copy_job_request->pre_copy_job_req.punch_num = pre_value;
    memcpy( ( char* )&copy_job_request->punch_num, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PUNCH_NUM, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->punch_num, len );

    return ret;
}

int set_copy_param_staple_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->staple_mode;
    pedk_copy_job_request->pre_copy_job_req.staple_mode = pre_value;
    memcpy( ( char* )&copy_job_request->staple_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_STAPLE_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->staple_mode, len );

    return ret;
}

int set_copy_param_punch_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->punch_mode;
    pedk_copy_job_request->pre_copy_job_req.punch_mode = pre_value;
    memcpy( ( char* )&copy_job_request->punch_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PUNCH_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->punch_mode, len );

    return ret;
}

int set_copy_param_fold_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->fold_mode;
    pedk_copy_job_request->pre_copy_job_req.fold_mode = pre_value;
    memcpy( ( char* )&copy_job_request->fold_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_FOLD_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->fold_mode, len );

    return ret;
}

int set_copy_param_tray_receive( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->tray_receive;
    pedk_copy_job_request->pre_copy_job_req.tray_receive = pre_value;
    memcpy( ( char* )&copy_job_request->tray_receive, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_TRAY_RECEIVE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->tray_receive, len );

    return ret;
}

int set_copy_param_shift_mode( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->shift_mode;
    pedk_copy_job_request->pre_copy_job_req.shift_mode = pre_value;
    memcpy( ( char* )&copy_job_request->shift_mode, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SHIFT_MODE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->shift_mode, len );

    return ret;
}

int set_copy_param_fold_number( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->fold_number;
    pedk_copy_job_request->pre_copy_job_req.fold_number = pre_value;
    memcpy( ( char* )&copy_job_request->fold_number, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_HAVE_STAPLER, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->fold_number, len );

    return ret;
}

int set_copy_param_have_stapler( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->have_stapler;
    pedk_copy_job_request->pre_copy_job_req.have_stapler = pre_value;
    memcpy( ( char* )&copy_job_request->have_stapler, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_HAVE_STAPLER, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->have_stapler, len );

    return ret;
}

int set_copy_param_cover_paper_type( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->cover_paper_type;
    pedk_copy_job_request->pre_copy_job_req.cover_paper_type = pre_value;
    memcpy( ( char* )&copy_job_request->cover_paper_type, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_COVER_PAPER_TYPE, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->cover_paper_type, len );

    return ret;
}


int set_copy_param_scan_width( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->scan_width;
    pedk_copy_job_request->pre_copy_job_req.scan_width = pre_value;
    memcpy( ( char* )&copy_job_request->scan_width, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCAN_WIDTH, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->scan_width, len );

    return ret;
}

int set_copy_param_scan_height( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->scan_height;
    pedk_copy_job_request->pre_copy_job_req.scan_height = pre_value;
    memcpy( ( char* )&copy_job_request->scan_height, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SCAN_HEIGHT, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->scan_height, len );

    return ret;
}


int set_copy_param_print_width( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->print_width;
    pedk_copy_job_request->pre_copy_job_req.print_width = pre_value;
    memcpy( ( char* )&copy_job_request->print_width, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PRINT_WIDTH, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->print_width, len );

    return ret;
}

int set_copy_param_print_height( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->print_height;
    pedk_copy_job_request->pre_copy_job_req.print_height = pre_value;
    memcpy( ( char* )&copy_job_request->print_height, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_PRINT_HEIGHT, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->print_height, len );

    return ret;
}

int set_copy_param_separator_tray_in( void* value, int len, int param_id )
{
    int* copy_param = ( int* )value;
    int ret =  0;

    if( copy_param == NULL )
    {
        copy_Log( " param NULL !\n" );
        ret = -1;
        return ret;
    }
    if( len <= 0 )
    {
        copy_Log( " param len <= 0 !\n" );
        ret = -2;
        return ret;
    }
    int pre_value;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;


    pre_value = copy_job_request->separator_tray_in;
    pedk_copy_job_request->pre_copy_job_req.separator_tray_in = pre_value;
    memcpy( ( char* )&copy_job_request->separator_tray_in, ( char* )copy_param, len );

    ret = copy_param_relationship_mutex( SINGLE_COPY_PARAM_SEPARATOR_TRAY_IN, copy_param, len, param_id );

    memcpy( ( char* )copy_param, ( char* )&copy_job_request->separator_tray_in, len );

    return ret;
}

int set_copy_param_7265_max( void* value, int len, int param_id )
{
    copy_Log( "not support copy_param\n" );

    return -1;
}



static SET_COPY_PARAM_INDEX set_copy_param_table[] =
{
    { SINGLE_COPY_PARAM_COPY_TYPE,  &set_copy_param_copy_type },
    { SINGLE_COPY_PARAM_COPIES, &set_copy_param_copies },
    { SINGLE_COPY_PARAM_COLOR_MODE, &set_copy_param_color_mode },
    { SINGLE_COPY_PARAM_SCAN_SOURCE, &set_copy_param_scan_source },
    { SINGLE_COPY_PARAM_SCAN_SIZE, &set_copy_param_scan_size },
    { SINGLE_COPY_PARAM_IMAGE_ORIENTATION, &set_copy_param_image_orientation },
    { SINGLE_COPY_PARAM_PRINT_TRAY, &set_copy_param_print_tray },
    { SINGLE_COPY_PARAM_PRINT_SIZE, &set_copy_param_print_size },
    { SINGLE_COPY_PARAM_PAGE_TYPE, &set_copy_param_page_type },
    { SINGLE_COPY_PARAM_PRINT_MODE, &set_copy_param_print_mode },
    { SINGLE_COPY_PARAM_AUTO_SCALE_MODE, &set_copy_param_auto_scale_mode },
    { SINGLE_COPY_PARAM_QUALITY, &set_copy_param_quality },
    { SINGLE_COPY_PARAM_COLOR_BALANCE_C, &set_copy_param_color_balance_c },
    { SINGLE_COPY_PARAM_COLOR_BALANCE_M, &set_copy_param_color_balance_m },
    { SINGLE_COPY_PARAM_COLOR_BALANCE_Y, &set_copy_param_color_balance_y },
    { SINGLE_COPY_PARAM_COLOR_BALANCE_K, &set_copy_param_color_balance_k },
    { SINGLE_COPY_PARAM_IMAGE_BRIGHTNESS, &set_copy_param_image_brightness },
    { SINGLE_COPY_PARAM_IMAGE_SATURATION, &set_copy_param_image_saturation },
    { SINGLE_COPY_PARAM_IMAGE_CONTRAST, &set_copy_param_image_contrast },
    { SINGLE_COPY_PARAM_IMAGE_SHARPNESS, &set_copy_param_image_sharpness },
    { SINGLE_COPY_PARAM_IMAGE_HUE, &set_copy_param_image_hue },
    { SINGLE_COPY_PARAM_BACKGROUNDMOVE_LEVEL, &set_copy_param_backgroundmove_level },
    { SINGLE_COPY_PARAM_SAVE_TONER_MODE, &set_copy_param_save_toner_mode },
    { SINGLE_COPY_PARAM_HORIZONTAL_MARGIN, &set_copy_param_horizontal_margin },
    { SINGLE_COPY_PARAM_VERTICAL_MARGIN, &set_copy_param_vertical_margin },
    { SINGLE_COPY_PARAM_EDGE_TO_EDGE_MODE, &set_copy_param_edge_to_edge_mode },
    { SINGLE_COPY_PARAM_COLLATE, &set_copy_param_collate },
    { SINGLE_COPY_PARAM_SEPARATOR, &set_copy_param_separator },
    { SINGLE_COPY_PARAM_NUP_TYPE, &set_copy_param_nup_type },
    { SINGLE_COPY_PARAM_NUP_COMBINATION, &set_copy_param_nup_combination },
    { SINGLE_COPY_PARAM_POSTER_SIZE, &set_copy_param_poster_size },
    { SINGLE_COPY_PARAM_WATERMARK_MODE, &set_copy_param_watermark_mode },
    { SINGLE_COPY_PARAM_WATERMARK_STRING, &set_copy_param_watermark_string },
    { SINGLE_COPY_PARAM_BOOKLET_DUPLEX, &set_copy_param_booklet_duplex },
    { SINGLE_COPY_PARAM_MIRROR_MODE, &set_copy_param_mirror_mode },
    { SINGLE_COPY_PARAM_CLONE_MODE, &set_copy_param_clone_mode },
    { SINGLE_COPY_PARAM_SKEWING_MODE, &set_copy_param_skewing_mode },
    { SINGLE_COPY_PARAM_PAGE_HEADER_ENABLE, &set_copy_param_page_header_enable },
    { SINGLE_COPY_PARAM_PAGE_HEADER_POSITION, &set_copy_param_page_header_position },
    { SINGLE_COPY_PARAM_PAGE_HEADER_PAGINATION, &set_copy_param_page_header_pagination },
    { SINGLE_COPY_PARAM_PAGE_HEADER_TEXT_TYPE, &set_copy_param_page_header_text_type },
    { SINGLE_COPY_PARAM_PAGE_HEADER_TEXT, &set_copy_param_page_header_text },
    { SINGLE_COPY_PARAM_PAGE_FOOTER_ENABLE, &set_copy_param_page_footer_enable },
    { SINGLE_COPY_PARAM_PAGE_FOOTER_POSITION, &set_copy_param_page_footer_position },
    { SINGLE_COPY_PARAM_PAGE_FOOTER_PAGINATION, &set_copy_param_page_footer_pagination },
    { SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT_TYPE, &set_copy_param_page_footer_text_type },
    { SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT, &set_copy_param_page_footer_text },
    { SINGLE_COPY_PARAM_BACKUP_MODE, &set_copy_param_backup_mode },
    { SINGLE_COPY_PARAM_SAMPLE_MODE, &set_copy_param_sample_mode },
    { SINGLE_COPY_PARAM_SCALE_PERCENT, &set_copy_param_scale_percent },
    { SINGLE_COPY_PARAM_AUTO_ID_CORRECTION, &set_copy_param_auto_id_correction },
    { SINGLE_COPY_PARAM_USE_COLOR_INVERSION, &set_copy_param_use_color_inversion },
    { SINGLE_COPY_PARAM_USE_COPY_SCAN_MEANTIME, &set_copy_param_use_copy_scan_meantime },
    { SINGLE_COPY_PARAM_SCAN_FILE_TYPE, &set_copy_param_scan_file_type },
    { SINGLE_COPY_PARAM_SCAN_SEND_ROUTER, &set_copy_param_scan_send_router },
    { SINGLE_COPY_PARAM_USE_REMOVEL_COLOR, &set_copy_param_use_removel_color },
    { SINGLE_COPY_PARAM_REMOVE_COLOR_PLANE, &set_copy_param_remove_color_plane },
    { SINGLE_COPY_PARAM_USE_EDGE_CLEAN, &set_copy_param_use_edge_clean },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_TOP, &set_copy_param_filter_edge_margin_top },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_LEFT, &set_copy_param_filter_edge_margin_left },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_RIGHT, &set_copy_param_filter_edge_margin_right },
    { SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_BOTTOM, &set_copy_param_filter_edge_margin_bottom },
    { SINGLE_COPY_PARAM_ORIGINAL_FLIP, &set_copy_param_original_flip },
    { SINGLE_COPY_PARAM_COPIES_FLIP, &set_copy_param_copies_flip },
    { SINGLE_COPY_PARAM_COVER, &set_copy_param_cover },
    { SINGLE_COPY_PARAM_COVER_BACK, &set_copy_param_cover_back },
    { SINGLE_COPY_PARAM_COVER_TRAY_IN, &set_copy_param_cover_tray_in },
    { SINGLE_COPY_PARAM_STAPLE_NUM, &set_copy_param_staple_num },
    { SINGLE_COPY_PARAM_STAPLE_ANGLE, &set_copy_param_staple_angle },
    { SINGLE_COPY_PARAM_PUNCH_NUM, &set_copy_param_punch_num },
    { SINGLE_COPY_PARAM_STAPLE_MODE, &set_copy_param_staple_mode },
    { SINGLE_COPY_PARAM_PUNCH_MODE, &set_copy_param_punch_mode },
    { SINGLE_COPY_PARAM_FOLD_MODE, &set_copy_param_fold_mode },
    { SINGLE_COPY_PARAM_TRAY_RECEIVE, &set_copy_param_tray_receive },
    { SINGLE_COPY_PARAM_SHIFT_MODE, &set_copy_param_shift_mode },
    { SINGLE_COPY_PARAM_FOLD_NUMBER, &set_copy_param_fold_number },
    { SINGLE_COPY_PARAM_HAVE_STAPLER, &set_copy_param_have_stapler },
    { SINGLE_COPY_PARAM_COVER_PAPER_TYPE, &set_copy_param_cover_paper_type },
    { SINGLE_COPY_PARAM_SCAN_WIDTH, &set_copy_param_scan_width },
    { SINGLE_COPY_PARAM_SCAN_HEIGHT, &set_copy_param_scan_height },
    { SINGLE_COPY_PARAM_PRINT_WIDTH, &set_copy_param_print_width },
    { SINGLE_COPY_PARAM_PRINT_HEIGHT, &set_copy_param_print_height },
    { SINGLE_COPY_PARAM_SEPARATOR_TRAY_IN, &set_copy_param_separator_tray_in },
    { SINGLE_COPY_PARAM_7265_MAX, &set_copy_param_7265_max},
};

const COPY_JOB_REQUEST_DATA_S const copy_job_request_default =
{
    .copy_type = COPY_PARAM_JOB_TYPE_SCALE,
    .copies = 1,
    .collate = 1,
    .color_mode = 0,
    .auto_scale_mode = 1,
    .color_balance_c = COPY_COLORBALANCE_LEVEL_7265_6,
    .color_balance_m = COPY_COLORBALANCE_LEVEL_7265_6,
    .color_balance_y = COPY_COLORBALANCE_LEVEL_7265_6,
    .color_balance_k = COPY_COLORBALANCE_LEVEL_7265_6,
    .image_brightness = COPY_BRIGHTNESS_LEVEL_7265_6,
    .image_saturation = COPY_SATURATION_LEVEL_7265_6,
    .image_contrast = COPY_CONTRAST_LEVEL_7265_6,
    .image_sharpness = COPY_SHARPNESS_LEVEL_7265_6,
    .image_hue = COPY_HUE_LEVEL_7265_6,
    .backgroundmove_level = COPY_BACKGROUNDREMOVE_LEVEL_7265_6,
    .scale_percent = 100,
    .auto_id_correction = 0,
    .original_flip = FLIP_7265_LEFT_MODE,
    .copies_flip = FLIP_7265_LEFT_MODE,
    .scan_source = SCAN_MODE_7265_FB,
    .image_bit_depth = 2,

};

int set_single_param_default_value(SINGLE_COPY_PARAM_E copy_param,void* value,int len, int param_id)
{
    int ret = 0;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_request = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        return 0;
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;

    if( copy_param < SINGLE_COPY_PARAM_COPY_TYPE || copy_param > SINGLE_COPY_PARAM_SEPARATOR_TRAY_IN )
    {
        ret = -1;
        return ret;
    }

    switch(copy_param)
    {
    case SINGLE_COPY_PARAM_COPY_TYPE:
        copy_job_request->copy_type = copy_job_request_default.copy_type;
        memcpy(value,&copy_job_request->copy_type,len);
    break;
    case SINGLE_COPY_PARAM_COPIES:
        copy_job_request->copies = copy_job_request_default.copies;
        memcpy(value,&copy_job_request->copies,len);
    break;
    case SINGLE_COPY_PARAM_COLLATE:
        copy_job_request->collate = copy_job_request_default.collate;
        memcpy(value,&copy_job_request->collate,len);
    break;
    case SINGLE_COPY_PARAM_COLOR_MODE:
        copy_job_request->color_mode = copy_job_request_default.color_mode;
        memcpy(value,&copy_job_request->color_mode,len);
    break;
    case SINGLE_COPY_PARAM_AUTO_SCALE_MODE:
        copy_job_request->auto_scale_mode = copy_job_request_default.auto_scale_mode;
        memcpy(value,&copy_job_request->auto_scale_mode,len);
    break;
    case SINGLE_COPY_PARAM_COLOR_BALANCE_C:
        copy_job_request->color_balance_c = copy_job_request_default.color_balance_c;
        memcpy(value,&copy_job_request->color_balance_c,len);
    break;
    case SINGLE_COPY_PARAM_COLOR_BALANCE_M:
        copy_job_request->color_balance_m = copy_job_request_default.color_balance_m;
        memcpy(value,&copy_job_request->color_balance_m,len);
    break;
    case SINGLE_COPY_PARAM_COLOR_BALANCE_Y:
        copy_job_request->color_balance_y = copy_job_request_default.color_balance_y;
        memcpy(value,&copy_job_request->color_balance_y,len);
    break;
    case SINGLE_COPY_PARAM_COLOR_BALANCE_K:
        copy_job_request->color_balance_k = copy_job_request_default.color_balance_k;
        memcpy(value,&copy_job_request->color_balance_k,len);
    break;
    case SINGLE_COPY_PARAM_IMAGE_BRIGHTNESS:
        copy_job_request->image_brightness = copy_job_request_default.image_brightness;
        memcpy(value,&copy_job_request->image_brightness,len);
    break;
    case SINGLE_COPY_PARAM_IMAGE_CONTRAST:
        copy_job_request->image_contrast = copy_job_request_default.image_contrast;
        memcpy(value,&copy_job_request->image_contrast,len);
    break;
    case SINGLE_COPY_PARAM_IMAGE_HUE:
        copy_job_request->image_hue = copy_job_request_default.image_hue;
        memcpy(value,&copy_job_request->image_hue,len);
    break;
    case SINGLE_COPY_PARAM_IMAGE_SATURATION:
        copy_job_request->image_saturation = copy_job_request_default.image_saturation;
        memcpy(value,&copy_job_request->image_saturation,len);
    break;
    case SINGLE_COPY_PARAM_IMAGE_SHARPNESS:
        copy_job_request->image_sharpness = copy_job_request_default.image_sharpness;
        memcpy(value,&copy_job_request->image_sharpness,len);
    break;
    case SINGLE_COPY_PARAM_BACKGROUNDMOVE_LEVEL:
        copy_job_request->backgroundmove_level = copy_job_request_default.backgroundmove_level;
        memcpy(value,&copy_job_request->backgroundmove_level,len);
    break;
    case SINGLE_COPY_PARAM_SCALE_PERCENT:
        copy_job_request->scale_percent = copy_job_request_default.scale_percent;
        memcpy(value,&copy_job_request->scale_percent,len);
    break;
    case SINGLE_COPY_PARAM_AUTO_ID_CORRECTION:
        copy_job_request->auto_id_correction = copy_job_request_default.auto_id_correction;
        memcpy(value,&copy_job_request->auto_id_correction,len);
    break;
    case SINGLE_COPY_PARAM_ORIGINAL_FLIP:
        copy_job_request->original_flip = copy_job_request_default.original_flip;
        memcpy(value,&copy_job_request->original_flip,len);
    break;
    case SINGLE_COPY_PARAM_COPIES_FLIP:
        copy_job_request->copies_flip = copy_job_request_default.copies_flip;
        memcpy(value,&copy_job_request->copies_flip,len);
    break;
    case SINGLE_COPY_PARAM_SCAN_SOURCE:
        copy_job_request->scan_source = copy_job_request_default.scan_source;
        memcpy(value,&copy_job_request->scan_source,len);
    break;

    default:
        copy_Log("copy param set default 0\n");
        memset(value,0,len);
        set_copy_param_table[copy_param].set_copy_param( value, len, param_id );
        break;
    }

    return ret;

}

int copy_param_init( int param_id )
{

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* pre_copy_job_request = NULL;
    int mem_size = sizeof(COPY_JOB_REQUEST_DATA_S);

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;
    pre_copy_job_request = &pedk_copy_job_req->pre_copy_job_req;
    memset( copy_job_request, 0, mem_size );
    memset( pre_copy_job_request, 0, mem_size );

    memcpy( copy_job_request, &copy_job_request_default, mem_size );
    memcpy( pre_copy_job_request, copy_job_request, mem_size );

    copy_Log( "copy param init done!\n" );

    return 0;

}



int PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_E copy_param, void* value, int len, int param_id )
{
    int i = 0;
    int cnt = 0;
    int ret = 0;

    cnt = sizeof( set_copy_param_table ) / sizeof( set_copy_param_table[0] );
    for( i = 0; i < cnt - 1; i++ )
    {
        if( copy_param == set_copy_param_table[i].copy_param )
        {
            break;
        }
    }
    if( set_copy_param_table[i].copy_param == SINGLE_COPY_PARAM_7265_MAX )
    {
        set_copy_param_table[i].set_copy_param( NULL, 0, param_id );
        ret = -1;
    }
    else
    {
        if( copy_param == SINGLE_COPY_PARAM_WATERMARK_STRING || copy_param == SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT || copy_param == SINGLE_COPY_PARAM_PAGE_HEADER_TEXT )
        {
            copy_Log( "set copy param:%d src_value:%s len:%d\n", copy_param, ( char* )value, len );
        }
        else
        {
            copy_Log( "set copy param:%d src_value:%d len:%d\n", copy_param, *( ( int* )value ), len );
        }
        ret = set_copy_param_table[i].set_copy_param( value, len, param_id );
    }

    return ret;
}

//#define X86_DEBUG 1

#define countof(x) (sizeof(x) / sizeof((x)[0]))


COPY_JOB_REQUEST_DATA_S g_copy_req_PEDK;
COPY_JOB_REQUEST_DATA_S g_copy_req_7265;

typedef struct
{
    char*  paper_size_PEDK;
    PAPER_SIZE_7265_E paper_size_7625;
} PAPER_SIZE_CONVERT_TABLE;



PAPER_SIZE_CONVERT_TABLE g_paper_size_convert_table[] =
{
    { "MEDIA_SIZE_A3", PAPER_SIZE_A3     },
    { "MEDIA_SIZE_A4", PAPER_SIZE_A4     },
    { "MEDIA_SIZE_A4_LEF", PAPER_SIZE_A4L     },
    { "MEDIA_SIZE_A5", PAPER_SIZE_A5     },
    { "MEDIA_SIZE_A5_LEF", PAPER_SIZE_A5L     },
    { "MEDIA_SIZE_JIS_B4", PAPER_SIZE_JIS_B4     },
    { "MEDIA_SIZE_JIS_B5", PAPER_SIZE_JIS_B5     },
    { "MEDIA_SIZE_JIS_B5_LEF", PAPER_SIZE_JIS_B5L     },
    { "MEDIA_SIZE_JIS_B6", PAPER_SIZE_JIS_B6     },
    { "MEDIA_SIZE_JIS_B6_LEF", PAPER_SIZE_JIS_B6     },                      ///
    { "MEDIA_SIZE_ISO_B4", PAPER_SIZE_B4     },                          ///
    { "MEDIA_SIZE_ISO_B4_LEF", PAPER_SIZE_B4     },                      ///
    { "MEDIA_SIZE_ISO_B5", PAPER_SIZE_B5     },
    { "MEDIA_SIZE_ISO_B5_LEF", PAPER_SIZE_B5L     },
    { "MEDIA_SIZE_ISO_B6", PAPER_SIZE_B6     },
    { "MEDIA_SIZE_ISO_B6_LEF", PAPER_SIZE_B6     },                      ////
    { "MEDIA_SIZE_8K", PAPER_SIZE_8K     },
    { "MEDIA_SIZE_BIG_16K", PAPER_SIZE_BIG_16K     },
    { "MEDIA_SIZE_BIG_16K_LEF", PAPER_SIZE_BIG_16KL     },
    { "MEDIA_SIZE_16K", PAPER_SIZE_BIG_16K     },                            //
    { "MEDIA_SIZE_16K_LEF", PAPER_SIZE_BIG_16KL     },                        //
    { "MEDIA_SIZE_BIG_32K", PAPER_SIZE_CUSTOM_32K     },                        //
    { "MEDIA_SIZE_BIG_32K_LEF", PAPER_SIZE_CUSTOM_32K     },                    //
    { "MEDIA_SIZE_32K", PAPER_SIZE_CUSTOM_32K     },
    { "MEDIA_SIZE_32K_LEF", PAPER_SIZE_CUSTOM_32K     },
    { "MEDIA_SIZE_11x17", PAPER_SIZE_LEDGER     },
    { "MEDIA_SIZE_LETTER", PAPER_SIZE_LETTER     },
    { "MEDIA_SIZE_LETTER_LEF", PAPER_SIZE_LETTER_L     },
    { "MEDIA_SIZE_LEGAL", PAPER_SIZE_LEGAL13    },
    { "MEDIA_SIZE_FOLIO", PAPER_SIZE_FOLIO     },
    { "MEDIA_SIZE_OFICIO", PAPER_SIZE_OFICIO     },
    { "MEDIA_SIZE_EXECUTIVE", PAPER_SIZE_EXECUTIVE     },
    { "MEDIA_SIZE_EXECUTIVE_LEF", PAPER_SIZE_EXECUTIVE_L     },
    { "MEDIA_SIZE_STATEMENT", PAPER_SIZE_STATEMENT     },
    { "MEDIA_SIZE_STATEMENT_LEF", PAPER_SIZE_STATEMENT_L     },
    { "MEDIA_SIZE_A6", PAPER_SIZE_A6     },
    { "MEDIA_SIZE_A6_LEF", PAPER_SIZE_A6     },
    { "MEDIA_SIZE_NO10_ENV", PAPER_SIZE_ENV_10    },
    { "MEDIA_SIZE_MONARCH_ENV", PAPER_SIZE_ENV_MONARCH     },
    { "MEDIA_SIZE_MONARCH_ENV_LEF", PAPER_SIZE_ENV_MONARCH     },
    { "MEDIA_SIZE_C6_ENV", PAPER_SIZE_ENV_C6     },
    { "MEDIA_SIZE_C6_ENV_LEF", PAPER_SIZE_ENV_C6     },
    { "MEDIA_SIZE_C5_ENV", PAPER_SIZE_ENV_C5    },
    { "MEDIA_SIZE_C5_ENV_LEF", PAPER_SIZE_ENV_C5     },
    { "MEDIA_SIZE_C4_ENV", PAPER_SIZE_ENV_DL     },
    { "MEDIA_SIZE_DL_ENV", PAPER_SIZE_ENV_DL     },
    { "MEDIA_SIZE_B6", PAPER_SIZE_B6     },
    { "MEDIA_SIZE_B6_LEF", PAPER_SIZE_B6     },
    { "MEDIA_SIZE_ZL", PAPER_SIZE_ZL     },
    { "MEDIA_SIZE_ZL_LEF", PAPER_SIZE_ZL     },
    { "MEDIA_SIZE_YOUGATA4", PAPER_SIZE_YOUGATA4     },
    { "MEDIA_SIZE_YOUNAGE3", PAPER_SIZE_YOUNAGA3     },
    { "MEDIA_SIZE_NAGAGATE3", PAPER_SIZE_NAGAGATA3     },
    { "MEDIA_SIZE_YOUGATA2", PAPER_SIZE_YOUGATA2     },
    { "MEDIA_SIZE_YOUGATA2_LEF", PAPER_SIZE_YOUGATA2     },
    { "MEDIA_SIZE_POSTCARD", PAPER_SIZE_POSTCARD     },
    { "MEDIA_SIZE_POSTCARD_LEF", PAPER_SIZE_POSTCARD     },
    { "MEDIA_SIZE_JAPANESE_POSTCARD", PAPER_SIZE_JAPANESE_POSTCARD     },
    { "MEDIA_SIZE_JAPANESE_POSTCARD_LEF", PAPER_SIZE_JAPANESE_POSTCARD     },
    { "MEDIA_SIZE_USERDEFINE", PAPER_SIZE_USER_DEFINE     },
    { "MEDIA_SIZE_LEDGER", PAPER_SIZE_LEDGER     },
    { "MEDIA_SIZE_BIG_8K", PAPER_SIZE_8K     },
    { "MEDIA_SIZE_ENV_B6", PAPER_SIZE_ENV_B6     },
    { "MEDIA_SIZE_FOOLSCAPS", PAPER_SIZE_FOOLSCAP1     },
    { "MEDIA_SIZE_INVOICE", PAPER_SIZE_INVOICE     },
    { "MEDIA_SIZE_INVOICE_LEF", PAPER_SIZE_INVOICE_L     },
    { "MEDIA_SIZE_A3_WIDE", PAPER_SIZE_A3_WIDE1     },
    { "MEDIA_SIZE_LEGAL13", PAPER_SIZE_LEGAL13     },
    { "MEDIA_SIZE_LEGAL14", PAPER_SIZE_LEGAL14     },
    { "MEDIA_SIZE_YOUKEI_SIZE4", PAPER_SIZE_YOUKEI_SIZE4     },
    { "MEDIA_SIZE_CHOUKEI_SIZE3", PAPER_SIZE_CHOUKEI_SIZE3     },
    { "MEDIA_SIZE_SRA3", PAPER_SIZE_SRA3     },
    { "MEDIA_SIZE_CARD", PAPER_SIZE_CARD     },
    { "SCAN_FULL_PLATEN", PAPER_SIZE_FULL_TABLE     },
    { "AUTO_PAPER_CHECK", PAPER_SIZE_AUTO     },

};



void printf_all_copy_parameter( COPY_JOB_REQUEST_DATA_S* parameter )
{
    printf( "copy_type                            \t%u\n", parameter->copy_type );
    printf( "copies                               \t%u\n", parameter->copies );
    printf( "color_mode                           \t%u\n", parameter->color_mode );
    printf( "scan_source                          \t%u\n", parameter->scan_source );
    printf( "scan_size                            \t%u\n", parameter->scan_size );
    printf( "image_orientation                    \t%u\n", parameter->image_orientation );
    printf( "print_tray                           \t%u\n", parameter->print_tray );
    printf( "print_size                           \t%u\n", parameter->print_size );
    printf( "page_type                            \t%u\n", parameter->page_type );
    printf( "print_mode                           \t%u\n", parameter->print_mode );
    printf( "auto_scale_mode                      \t%u\n", parameter->auto_scale_mode );
    printf( "quality                              \t%u\n", parameter->quality );
    printf( "color_balance_c                      \t%u\n", parameter->color_balance_c );
    printf( "color_balance_m                      \t%u\n", parameter->color_balance_m );
    printf( "color_balance_y                      \t%u\n", parameter->color_balance_y );
    printf( "color_balance_k                      \t%u\n", parameter->color_balance_k );
    printf( "image_brightness                     \t%u\n", parameter->image_brightness );
    printf( "image_saturation                     \t%u\n", parameter->image_saturation );
    printf( "image_contrast                       \t%u\n", parameter->image_contrast );
    printf( "image_sharpness                      \t%u\n", parameter->image_sharpness );
    printf( "image_hue                            \t%u\n", parameter->image_hue );
    printf( "backgroundmove_level                 \t%u\n", parameter->backgroundmove_level );
    printf( "save_toner_mode                      \t%u\n", parameter->save_toner_mode );
    printf( "horizontal_margin                    \t%d\n", parameter->horizontal_margin );
    printf( "vertical_margin                      \t%d\n", parameter->vertical_margin );
    printf( "edge_to_edge_mode                    \t%u\n", parameter->edge_to_edge_mode );
    printf( "collate                              \t%u\n", parameter->collate );
    printf( "separator                            \t%u\n", parameter->separator );
    printf( "nup_type                             \t%u\n", parameter->nup_type );
    printf( "nup_combination                      \t%u\n", parameter->nup_combination );
    printf( "poster_size                          \t%u\n", parameter->poster_size );
    printf( "watermark_mode                       \t%u\n", parameter->watermark_mode );
    printf( "watermark_string                     \t%s\n", parameter->watermark_string );
    printf( "booklet_duplex                       \t%u\n", parameter->booklet_duplex );
    printf( "mirror_mode                          \t%u\n", parameter->mirror_mode );
    printf( "clone_mode                           \t%u\n", parameter->clone_mode );
    printf( "skewing_mode                         \t%u\n", parameter->skewing_mode );
    printf( "staple_mode                          \t%u\n", parameter->staple_mode );
    printf( "punch_mode                           \t%u\n", parameter->punch_mode );
    printf( "fold_mode                            \t%u\n", parameter->fold_mode );
    printf( "tray_receive                         \t%u\n", parameter->tray_receive );
    printf( "shift_mode                           \t%u\n", parameter->shift_mode );
    printf( "fold_number                          \t%u\n", parameter->fold_number );
    printf( "page_header.position                 \t%d\n", parameter->page_header.position );
    printf( "page_header.pagination               \t%d\n", parameter->page_header.pagination );
    printf( "page_header.text_type                \t%d\n", parameter->page_header.text_type );
    printf( "page_header.text                     \t%s\n", parameter->page_header.text );
    printf( "page_footer.position                 \t%d\n", parameter->page_footer.position );
    printf( "page_footer.pagination               \t%d\n", parameter->page_footer.pagination );
    printf( "page_footer.text_type                \t%d\n", parameter->page_footer.text_type );
    printf( "page_footer.text                     \t%s\n", parameter->page_footer.text );
    printf( "backup_mode                          \t%u\n", parameter->backup_mode );
    printf( "sample_mode                          \t%u\n", parameter->sample_mode );
    printf( "scale_percent                        \t%u\n", parameter->scale_percent );
    printf( "auto_id_correction                   \t%u\n", parameter->auto_id_correction );
    printf( "use_color_inversion                  \t%u\n", parameter->use_color_inversion );
    printf( "use_copy_scan_meantime               \t%u\n", parameter->use_copy_scan_meantime );
    printf( "scan_file_type                       \t%u\n", parameter->scan_file_type );
    printf( "scan_send_router                     \t%u\n", parameter->scan_send_router[0] );
    printf( "use_removel_color                    \t%u\n", parameter->use_removel_color );
    printf( "remove_color_plane                   \t%u\n", parameter->remove_color_plane );
    printf( "use_edge_clean                       \t%u\n", parameter->use_edge_clean );
    printf( "filter_edge_margin_top               \t%u\n", parameter->filter_edge_margin_top );
    printf( "filter_edge_margin_left              \t%u\n", parameter->filter_edge_margin_left );
    printf( "filter_edge_margin_right             \t%u\n", parameter->filter_edge_margin_right );
    printf( "filter_edge_margin_bottom            \t%u\n", parameter->filter_edge_margin_bottom );
    printf( "original_flip                        \t%u\n", parameter->original_flip );
    printf( "copies_flip                          \t%u\n", parameter->copies_flip );
    printf( "cover                                \t%u\n", parameter->cover );
    printf( "cover_back                           \t%u\n", parameter->cover_back );
    printf( "cover_tray_in                        \t%u\n", parameter->cover_tray_in );
    printf( "cover_paper_type                     \t%u\n", parameter->cover_paper_type );
    printf( "separator_tray_in                    \t%u\n", parameter->separator_tray_in );
    printf( "image_bit_depth                      \t%u\n", parameter->image_bit_depth );

}



int convert_param_PEDK_2_7625_copy_type( int param_id , char* value )
{
    //get 7265 req by id

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if( strcmp( value, COPY_NORMAL ) == 0 )
    {
        copy_job_request->copy_type = 2;
    }
    else if( strcmp( value, COPY_ID_CARD ) == 0 )
    {
        copy_job_request->copy_type = 0;
    }
    else if( strcmp( value, COPY_BILL ) == 0 )
    {
        copy_job_request->copy_type = 1;
    }
    else if( strcmp( value, COPY_CLONE ) == 0 )
    {
        copy_job_request->copy_type = 4;
    }
    else if( strcmp( value, COPY_POSTER ) == 0 )
    {
        copy_job_request->copy_type = 5;
    }
    else if( strcmp( value, COPY_NUP ) == 0 )
    {
        copy_job_request->copy_type = 3;
    }
    else if( strcmp( value, COPY_BOOKLET ) == 0 )
    {
        copy_job_request->copy_type = 7;
    }
    else if( strcmp( value, COPY_WATERMARK ) == 0 )
    {
        copy_job_request->copy_type = 2;
    }
    else if( strcmp( value, COPY_SCALE ) == 0 )
    {
        copy_job_request->copy_type = 2;
    }
    else if( strcmp( value, COPY_MIX ) == 0 )
    {
        copy_job_request->copy_type = 8;
    }
    else
    {
        copy_Log( "copy type err  %s \n \n", value );
    }



    return 0;
}



int convert_param_PEDK_2_7625_inputpapersize( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    int      i            = 0;
    int      array_num    = 0;

    array_num = sizeof( g_paper_size_convert_table ) / sizeof( g_paper_size_convert_table[0] );

    for( i = 0; i < array_num; i++ )
    {
        if(strcmp(g_paper_size_convert_table[i].paper_size_PEDK , value) == 0)
        {
            copy_job_request->scan_size = g_paper_size_convert_table[i].paper_size_7625;
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err  %s \n", value );
        return -1;
    }


    return 0;
}



int convert_param_PEDK_2_7625_outputtray_size( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    int      i            = 0;
    int      array_num    = 0;

    array_num = sizeof( g_paper_size_convert_table ) / sizeof( g_paper_size_convert_table[0] );

    for( i = 0; i < array_num; i++ )
    {
        if(strcmp(g_paper_size_convert_table[i].paper_size_PEDK , value) == 0)
        {
            copy_job_request->print_size = g_paper_size_convert_table[i].paper_size_7625;
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err  %s \n", value );
    }

    return 0;
}

int convert_param_PEDK_2_7625_outputtray_tray( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

//    int      i            = 0;
//    int      array_num    = 0;


    if(strcmp( "AUTO_SELECTION_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x64;//TRAY_INPUT_AUTO;
    }
    else if(strcmp( "MULTI_FUNCTION_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x06;//TRAY_INPUT_MULTIFUNCTION;
    }
    else if(strcmp( "STANDAR_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x00;//TRAY_INPUT_STANDARD;
    }
    else if(strcmp( "OPTION_TRAY1" , value) == 0)
    {
        copy_job_request->print_tray = 0x00;//TRAY_INPUT_STANDARD;
    }
    else if(strcmp( "OPTION_TRAY2" , value) == 0)
    {
        copy_job_request->print_tray = 0x01;//TRAY_INPUT_2;
    }
    else if(strcmp( "OPTION_TRAY3" , value) == 0)
    {
        copy_job_request->print_tray = 0x02;//TRAY_INPUT_3;
    }
    else if(strcmp( "OPTION_TRAY4" , value) == 0)
    {
        copy_job_request->print_tray = 0x03;//TRAY_INPUT_4;
    }
    else if(strcmp( "OPTION_TRAY5" , value) == 0)
    {
        copy_job_request->print_tray = 0x64;//TRAY_INPUT_AUTO;
    }
    else if(strcmp( "EXTERNAL_HIGH_CAPACITY_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x05;//TRAY_INPUT_External_LCT_OUT;
    }
    else if(strcmp( "INSTALL_HIGH_CAPACITY_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x04;//TRAY_INPUT_External_LCT_IN;
    }
    else
    {
        copy_job_request->print_tray = 0x00;//TRAY_INPUT_STANDARD;
        copy_Log( "PEDK param err   %s \n", value );
    }

    return 0;
}

int convert_param_PEDK_2_7625_zoom( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;


    if( value >= 25 && value <= 400 )
    {
        copy_job_request->auto_scale_mode = 0;
        copy_job_request->scale_percent   = value;
    }
    else if( value >= 0 && value < 25 )
    {
        copy_job_request->auto_scale_mode = 1;
        copy_job_request->scale_percent   = 100;
    }
    else
    {
        copy_job_request->auto_scale_mode = 0;
        copy_job_request->scale_percent   = 100;
    }

    return 0;
}



int convert_param_PEDK_2_7625_image_color_brightness( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->image_brightness    = value;
    return 0;
}

int convert_param_PEDK_2_7625_image_color_saturation( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->image_saturation    = value;
    return 0;
}


int convert_param_PEDK_2_7625_image_color_contrast( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->image_contrast    = value;
    return 0;
}


int convert_param_PEDK_2_7625_image_color_sharpness( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->image_sharpness    = value;
    return 0;
}


int convert_param_PEDK_2_7625_image_color_hue( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;
    copy_job_request->image_hue    = value;
    return 0;
}


int convert_param_PEDK_2_7625_image_color_backgroundmove_level( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->backgroundmove_level    = value;
    return 0;
}



int convert_param_PEDK_2_7625_copymode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    switch( value )
    {
    case 1://Single-sided to single-sided
        copy_job_request->print_mode = 0;//PRINT_MODE_SINGLE
        copy_job_request->scan_duplex = 0;
        break;
    case 2://Single-sided to double-sided
        copy_job_request->print_mode = 1;//PRINT_MODE_AUTO_DUPLEX
        copy_job_request->scan_duplex = 0;
        break;
    case 3://Double-sided to single-sided
        copy_job_request->print_mode = 0;//PRINT_MODE_SINGLE
        copy_job_request->scan_duplex = 1;
        break;
    case 4://Double-sided to double-sided
        copy_job_request->print_mode = 1;//PRINT_MODE_AUTO_DUPLEX
        copy_job_request->scan_duplex = 1;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );
    }
    return 0;
}



int convert_param_PEDK_2_7625_collatemode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    switch( value )
    {
    case 0:
        copy_job_request->collate = 0;
        break;
    case 1:
        copy_job_request->collate = 1;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );
    }
    return 0;
}



int convert_param_PEDK_2_7625_qualitytype( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;


    /**
     * 1:文本
     * 2:文本+画像
     * 3:画像
     * 4:自动调节
     */

    switch( value )
    {
    case 1:
        copy_job_request->quality = 1;//COPY_QUALITY_TXT
        break;
    case 2:
        copy_job_request->quality = 2;//COPY_QUALITY_MIXED
        break;
    case 3:
        copy_job_request->quality = 3;//COPY_QUALITY_PICTURE;
        break;
    case 4:
        copy_job_request->quality = 0;// COPY_QUALITY_AUTO;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );
    }

    return 0;
}


int convert_param_PEDK_2_7625_watermark_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->watermark_mode = value;
    return 0;
}

int convert_param_PEDK_2_7625_watermark_str( int param_id , char *value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    strcpy( copy_job_request->watermark_string, value );
    return 0;
}

int convert_param_PEDK_2_7625_copies( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->copies = value;
    return 0;
}

int convert_param_PEDK_2_7625_autocorrectionmode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->auto_id_correction = value;
    return 0;
}

int convert_param_PEDK_2_7625_arrangementmode( int param_id , int value )
{
    //get 7265 req by id
    //COPY_JOB_REQUEST_DATA_S *copy_job_request = get_pedk_copy_job_request(param_id);

    //copy_job_request->auto_id_correction = value;
    return 0;
}

int convert_param_PEDK_2_7625_nupmode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    switch( value )
    {
    case 0:
        copy_job_request->nup_type = 0;//COPY_NUP_CLOSE
        copy_job_request->nup_combination = 0;
        break;
    case 1:
        copy_job_request->nup_type = 1;//COPY_NUP_2IN1
        copy_job_request->nup_combination = 0;
        break;
    case 2:
        copy_job_request->nup_type = 2;//COPY_NUP_4IN1
        copy_job_request->nup_combination = 1;
        break;
    case 3:
        copy_job_request->nup_type = 2;//COPY_NUP_4IN1
        copy_job_request->nup_combination = 2;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );
    }

    copy_Log( "   pedk_nup =%d nup_type= %u  nup_combination=%u\n",value, copy_job_request->nup_type,copy_job_request->nup_combination );

    return 0;
}

int convert_param_PEDK_2_7625_clonemode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;


    switch( value )
    {
    case 0:
        copy_job_request->clone_mode = 0;//COPY_CLONE_OFF
        break;
    case 1:
        copy_job_request->clone_mode = 1;//COPY_CLONE_2x2
        break;
    case 2:
        copy_job_request->clone_mode = 2;//COPY_CLONE_3x3
        break;
    case 3:
        copy_job_request->clone_mode = 3;//COPY_CLONE_4x4
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );
    }
    return 0;
}


int convert_param_PEDK_2_7625_poster( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if( value >= 0 && value <= 19 )
    {
        copy_job_request->poster_size = value;
    }
    else
    {
        copy_Log( "PEDK param err   %d \n", value );
    }
    return 0;
}

int convert_param_PEDK_2_7625_color_mode( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if(strcmp( "COLOR_MODE_BLACK_WHITE" , value) == 0)
    {
        copy_job_request->color_mode = 0;
    }
    else if(strcmp( "COLOR_MODE_COLOR" , value) == 0)
    {
        copy_job_request->color_mode = 1;
    }
    else if(strcmp( "COLOR_MODE_RED_BLACK" , value) == 0)
    {
        copy_job_request->color_mode = 4;
    }
    else
    {
        copy_job_request->color_mode = 1;
        copy_Log( "PEDK param err   %s \n", value );
    }


    return 0;
}

int convert_param_PEDK_2_7625_scan_source( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    /**
    * | 来源 || Kanas | 4020 |
    * |:--:|:--:|:--:|:--:|
    * | 自动 | 0 | 支持 | 支持 |
    * | DADF | 1 | 支持 | 支持 |
    * | ADF | 2 | 支持 | 支持 |
    * | FB | 3 | 支持 | 支持 |
    * | MADF | 4 | 不支持| 支持 |
    */

    switch( value )
    {
    case 0:
        copy_job_request->scan_source = 0;//SCAN_MODE_AUTO
        break;
    case 1:
        copy_job_request->scan_source = 4;//SCAN_MODE_DADF
        break;
    case 2:
        copy_job_request->scan_source = 2;//SCAN_MODE_ADF
        break;
    case 3:
        copy_job_request->scan_source = 1;//SCAN_MODE_FB
        break;
    case 4:
        copy_job_request->scan_source = 3;//SCAN_MODE_MADF
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );
    }
    return 0;
}



int convert_param_PEDK_2_7625_image_orientaiton( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->image_orientation = value;
    return 0;
}


int convert_param_PEDK_2_7625_color_balance_c( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->color_balance_c = value;
    return 0;
}

int convert_param_PEDK_2_7625_color_balance_m( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->color_balance_m = value;
    return 0;
}
int convert_param_PEDK_2_7625_color_balance_y( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->color_balance_y = value;
    return 0;
}
int convert_param_PEDK_2_7625_color_balance_k( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->color_balance_k = value;
    return 0;
}


int convert_param_PEDK_2_7625_horizontal_margin( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->horizontal_margin = value;
    return 0;
}


int convert_param_PEDK_2_7625_vertical_margin( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->vertical_margin = value;
    return 0;
}

int convert_param_PEDK_2_7625_edge_to_edge_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->edge_to_edge_mode = value;
    return 0;
}

int convert_param_PEDK_2_7625_separator( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->separator = value;
    return 0;
}

int convert_param_PEDK_2_7625_nup_combination( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->nup_combination = value;
    return 0;
}

int convert_param_PEDK_2_7625_booklet_duplex( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->booklet_duplex = value;
    return 0;
}

int convert_param_PEDK_2_7625_mirror_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->mirror_mode = value;
    return 0;
}

int convert_param_PEDK_2_7625_page_header_enable( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_header.enable = value;
    return 0;
}

int convert_param_PEDK_2_7625_page_header_position( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_header.position = value;
    return 0;
}

int convert_param_PEDK_2_7625_page_header_pagination( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_header.pagination = value;
    return 0;
}

int convert_param_PEDK_2_7625_page_header_text_type( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_header.text_type = value;
    return 0;
}

int convert_param_PEDK_2_7625_page_header_text( int param_id , char * value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    strcpy( copy_job_request->page_header.text, value );
    return 0;
}

int convert_param_PEDK_2_7625_page_footer_enable( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_footer.enable = value;
    return 0;
}

int convert_param_PEDK_2_7625_page_footer_position( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_footer.position = value;
    return 0;
}


int convert_param_PEDK_2_7625_page_footer_pagination( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_footer.pagination = value;
    return 0;
}


int convert_param_PEDK_2_7625_page_footer_text_type( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->page_footer.text_type = value;
    return 0;
}


int convert_param_PEDK_2_7625_page_footer_text( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    strcpy( copy_job_request->page_footer.text, value );
    return 0;
}


int convert_param_PEDK_2_7625_backup_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->backup_mode = value;
    return 0;
}


int convert_param_PEDK_2_7625_sample_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->sample_mode = value;
    return 0;
}


int convert_param_PEDK_2_7625_use_color_inversion( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->use_color_inversion = value;
    return 0;
}


int convert_param_PEDK_2_7625_use_copy_scan_meantime( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->use_copy_scan_meantime = value;
    return 0;
}


int convert_param_PEDK_2_7625_use_remove_color( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->use_removel_color = value;
    return 0;
}


int convert_param_PEDK_2_7625_use_edge_clean( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->use_edge_clean = value;
    return 0;
}


int convert_param_PEDK_2_7625_filter_edge_margin_top( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->filter_edge_margin_top      = value;
    return 0;
}

int convert_param_PEDK_2_7625_filter_edge_margin_bottom( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->filter_edge_margin_bottom   = value;
    return 0;
}

int convert_param_PEDK_2_7625_filter_edge_margin_left( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->filter_edge_margin_left     = value;
    return 0;
}
int convert_param_PEDK_2_7625_filter_edge_margin_right( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->filter_edge_margin_right    = value;
    return 0;
}

int convert_param_PEDK_2_7625_copies_flip( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->copies_flip = value;
    return 0;
}

int convert_param_PEDK_2_7625_original_flip( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->original_flip = value;
    return 0;
}

int convert_param_PEDK_2_7625_cover( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->cover = value;
    return 0;
}


int convert_param_PEDK_2_7625_cover_back( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->cover_back = value;
    return 0;
}


int convert_param_PEDK_2_7625_cover_tray_in( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if(strcmp( "AUTO_SELECTION_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x64;//TRAY_INPUT_AUTO;
    }
    else if(strcmp( "MULTI_FUNCTION_TRAY" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x06;//TRAY_INPUT_MULTIFUNCTION;
    }
    else if(strcmp( "STANDAR_TRAY" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x00;//TRAY_INPUT_STANDARD;
    }
    else if(strcmp( "OPTION_TRAY1" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x00;//TRAY_INPUT_STANDARD;
    }
    else if(strcmp( "OPTION_TRAY2" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x01;//TRAY_INPUT_2;
    }
    else if(strcmp( "OPTION_TRAY3" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x02;//TRAY_INPUT_3;
    }
    else if(strcmp( "OPTION_TRAY4" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x03;//TRAY_INPUT_4;
    }
    else if(strcmp( "OPTION_TRAY5" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x64;//TRAY_INPUT_AUTO;
    }
    else if(strcmp( "EXTERNAL_HIGH_CAPACITY_TRAY" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x05;//TRAY_INPUT_External_LCT_OUT;
    }
    else if(strcmp( "INSTALL_HIGH_CAPACITY_TRAY" , value) == 0)
    {
        copy_job_request->cover_tray_in = 0x04;//TRAY_INPUT_External_LCT_IN;
    }
    else
    {
        copy_job_request->cover_tray_in = 0x00;//TRAY_INPUT_STANDARD;
        copy_Log( "PEDK param err   %s \n", value );
    }

    return 0;
}


int convert_param_PEDK_2_7625_staple_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    switch( value )
    {
    case 0:
        copy_job_request->staple_mode = 0;//STAPLE_MODE_OFF
        break;
    case 1:
        copy_job_request->staple_mode = 1;//STAPLE_MODE_LEFT_AUTO
        break;
    case 2:
        copy_job_request->staple_mode = 2;//STAPLE_MODE_RIGHT_AUTO;
        break;
    case 3:
        copy_job_request->staple_mode = 10;//STAPLE_MODE_BOTTOM_LEFT_AUTO;
        break;
    case 4:
        copy_job_request->staple_mode = 11;//STAPLE_MODE_BOTTOM_RIGHT_AUTO;
        break;
    case 5:
        copy_job_request->staple_mode = 4;//STAPLE_MODE_LEFT_ZREO;
        break;
    case 6:
        copy_job_request->staple_mode = 3;//STAPLE_MODE_RIGHT_ZREO;
        break;
    case 7:
        copy_job_request->staple_mode = 12;//STAPLE_MODE_TOP_LEFT_ZERO;
        break;
    case 8:
        copy_job_request->staple_mode = 13;//STAPLE_MODE_TOP_RIGHT_ZERO;
        break;
    case 9:
        copy_job_request->staple_mode = 16;//STAPLE_MODE_DOUBLE_LEFT;
        break;
    case 10:
        copy_job_request->staple_mode = 17;//STAPLE_MODE_DOUBLE_TOP;
        break;
    case 11:
        copy_job_request->staple_mode = 18;//STAPLE_MODE_DOUBLE_RIGHT;
        break;
    case 12:
        copy_job_request->staple_mode = 19;//STAPLE_MODE_DOUBLE_BOTTOM;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", value );

    }



    return 0;
}


int convert_param_PEDK_2_7625_punch_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->punch_mode = value;
    return 0;
}


int convert_param_PEDK_2_7625_fold_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if(value == 0)//OFF
    {
        copy_job_request->fold_mode = 0;
    }
    else if(value == 1)
    {
        copy_job_request->fold_mode = 1;//FOLD_MODE_2;
    }
    else if(value == 2)
    {
        copy_job_request->fold_mode = 4;//FOLD_MIDDLE_2_STAPLE;
    }
    else
    {
        return -1;
    }
    return 0;
}


int convert_param_PEDK_2_7625_tray_receive( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->tray_receive = value;
    return 0;
}


int convert_param_PEDK_2_7625_shift_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->shift_mode = value;
    return 0;
}


int convert_param_PEDK_2_7625_flod_number( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->fold_number = value;
    return 0;
}


int convert_param_PEDK_2_7625_have_stapler( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->have_stapler = value;
    return 0;
}


int convert_param_PEDK_2_7625_cover_paper_type( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if(strcmp( "MEDIA_TYPE_PLAIN" , value) == 0)
    {
        copy_job_request->cover_paper_type = 0;//PAPER_TYPE_ORDINARY
    }
    else if(strcmp( "MEDIA_TYPE_PLAIN_PLUS" , value) == 0)
    {
        copy_job_request->cover_paper_type = 1;//PAPER_TYPE_ORDINARY_P
    }
    else if(strcmp( "MEDIA_TYPE_THICK_PAPER1" , value) == 0)
    {
        copy_job_request->cover_paper_type = 2;//PAPER_TYPE_THICK1
    }
    else if(strcmp( "MEDIA_TYPE_THICK_PAPER1_PLUS" , value) == 0)
    {
        copy_job_request->cover_paper_type = 3;//PAPER_TYPE_THICK1_P
    }
    else if(strcmp( "PAPER_TYPE_THICK2" , value) == 0)
    {
        copy_job_request->cover_paper_type = 4;//PAPER_TYPE_THICK2
    }
    else if(strcmp( "PAPER_TYPE_THICK3" , value) == 0)
    {
        copy_job_request->cover_paper_type = 5;//PAPER_TYPE_THICK3
    }
    else if(strcmp( "MEDIA_TYPE_TRANSPARENCY" , value) == 0)
    {
        copy_job_request->cover_paper_type = 6;//PAPER_TYPE_FILM
    }
    else if(strcmp( "MEDIA_TYPE_POSTCARD" , value) == 0)
    {
        copy_job_request->cover_paper_type = 7;//PAPER_TYPE_POST_CARD
    }
    else if(strcmp( "MEDIA_TYPE_ENVELOPE" , value) == 0)
    {
        copy_job_request->cover_paper_type = 14;//PAPER_TYPE_ENVELOP
    }
    else if(strcmp( "MEDIA_TYPE_LABEL" , value) == 0)
    {
        copy_job_request->cover_paper_type = 11;//PAPER_TYPE_LABEL
    }
    else if(strcmp( "MEDIA_TYPE_THIN" , value) == 0)
    {
        copy_job_request->cover_paper_type = 16;//PAPER_TYPE_THIN
    }
    else if(strcmp( "MEDIA_TYPE_THICK" , value) == 0)
    {
        copy_job_request->cover_paper_type = 13;//PAPER_TYPE_THICK
    }
    else if(strcmp( "MEDIA_TYPE_CARD_STORK" , value) == 0)
    {
        copy_job_request->cover_paper_type = 15;//PAPER_TYPE_CARD
    }
    else if(strcmp( "MEDIA_TYPE_THICKER" , value) == 0)
    {
        copy_job_request->cover_paper_type = 13;//PAPER_TYPE_THICK
    }
    else if(strcmp( "MEDIA_TYPE_RECYCLED_PAPER" , value) == 0)
    {
        copy_job_request->cover_paper_type = 20;//PAPER_TYPE_RECYCLE
    }
    else
    {
        copy_job_request->cover_paper_type = 0;//PAPER_TYPE_ORDINARY
        copy_Log( "PEDK param err   %s \n", value );
    }

    return 0;
}



int convert_param_PEDK_2_7625_separator_tray_in( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if(strcmp( "AUTO_SELECTION_TRAY" , value) == 0)
    {
        copy_job_request->print_tray = 0x64;//TRAY_INPUT_AUTO;
    }
    else if(strcmp( "MULTI_FUNCTION_TRAY" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x06;//TRAY_INPUT_MULTIFUNCTION;
    }
    else if(strcmp( "STANDAR_TRAY" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x00;//TRAY_INPUT_STANDARD;
    }
    else if(strcmp( "OPTION_TRAY1" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x00;//TRAY_INPUT_STANDARD;
    }
    else if(strcmp( "OPTION_TRAY2" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x01;//TRAY_INPUT_2;
    }
    else if(strcmp( "OPTION_TRAY3" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x02;//TRAY_INPUT_3;
    }
    else if(strcmp( "OPTION_TRAY4" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x03;//TRAY_INPUT_4;
    }
    else if(strcmp( "OPTION_TRAY5" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x64;//TRAY_INPUT_AUTO;
    }
    else if(strcmp( "EXTERNAL_HIGH_CAPACITY_TRAY" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x05;//TRAY_INPUT_External_LCT_OUT;
    }
    else if(strcmp( "INSTALL_HIGH_CAPACITY_TRAY" , value) == 0)
    {
        copy_job_request->separator_tray_in = 0x04;//TRAY_INPUT_External_LCT_IN;
    }
    else
    {
        copy_job_request->separator_tray_in = 0x00;//TRAY_INPUT_STANDARD;
        copy_Log( "PEDK param err   %s \n", value );
    }

    return 0;
}


int convert_param_PEDK_2_7625_user_define_scan_paper_param_w( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->scan_width  = value;
    return 0;
}


int convert_param_PEDK_2_7625_user_define_scan_paper_param_h( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->scan_height = value;
    return 0;
}



int convert_param_PEDK_2_7625_user_define_print_paper_param_w( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->print_width  = value;
    return 0;
}


int convert_param_PEDK_2_7625_user_define_print_paper_param_h( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->print_height = value;
    return 0;
}



int convert_param_PEDK_2_7625_page_type( int param_id , char* value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    if(strcmp( "MEDIA_TYPE_PLAIN" , value) == 0)
    {
        copy_job_request->page_type = 0;//PAPER_TYPE_ORDINARY
    }
    else if(strcmp( "MEDIA_TYPE_PLAIN_PLUS" , value) == 0)
    {
        copy_job_request->page_type = 1;//PAPER_TYPE_ORDINARY_P
    }
    else if(strcmp( "MEDIA_TYPE_THICK_PAPER1" , value) == 0)
    {
        copy_job_request->page_type = 2;//PAPER_TYPE_THICK1
    }
    else if(strcmp( "MEDIA_TYPE_THICK_PAPER1_PLUS" , value) == 0)
    {
        copy_job_request->page_type = 3;//PAPER_TYPE_THICK1_P
    }
    else if(strcmp( "PAPER_TYPE_THICK2" , value) == 0)
    {
        copy_job_request->page_type = 4;//PAPER_TYPE_THICK2
    }
    else if(strcmp( "PAPER_TYPE_THICK3" , value) == 0)
    {
        copy_job_request->page_type = 5;//PAPER_TYPE_THICK3
    }
    else if(strcmp( "MEDIA_TYPE_TRANSPARENCY" , value) == 0)
    {
        copy_job_request->page_type = 6;//PAPER_TYPE_FILM
    }
    else if(strcmp( "MEDIA_TYPE_POSTCARD" , value) == 0)
    {
        copy_job_request->page_type = 7;//PAPER_TYPE_POST_CARD
    }
    else if(strcmp( "MEDIA_TYPE_ENVELOPE" , value) == 0)
    {
        copy_job_request->page_type = 14;//PAPER_TYPE_ENVELOP
    }
    else if(strcmp( "MEDIA_TYPE_LABEL" , value) == 0)
    {
        copy_job_request->page_type = 11;//PAPER_TYPE_LABEL
    }
    else if(strcmp( "MEDIA_TYPE_THIN" , value) == 0)
    {
        copy_job_request->page_type = 16;//PAPER_TYPE_THIN
    }
    else if(strcmp( "MEDIA_TYPE_THICK" , value) == 0)
    {
        copy_job_request->page_type = 13;//PAPER_TYPE_THICK
    }
    else if(strcmp( "MEDIA_TYPE_CARD_STORK" , value) == 0)
    {
        copy_job_request->page_type = 15;//PAPER_TYPE_CARD
    }
    else if(strcmp( "MEDIA_TYPE_THICKER" , value) == 0)
    {
        copy_job_request->page_type = 13;//PAPER_TYPE_THICK
    }
    else if(strcmp( "MEDIA_TYPE_RECYCLED_PAPER" , value) == 0)
    {
        copy_job_request->page_type = 20;//PAPER_TYPE_RECYCLE
    }
    else
    {
        copy_job_request->page_type = 0;//PAPER_TYPE_ORDINARY
        copy_Log( "PEDK param err   %s \n", value );
    }

    return 0;
}


int convert_param_PEDK_2_7625_skewing_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->skewing_mode = value;
    return 0;
}


int convert_param_PEDK_2_7625_toner_mode( int param_id , int value )
{
    //get 7265 req by id
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;

    pedk_copy_job_req = get_pedk_copy_job_request( param_id );
    if( pedk_copy_job_req == NULL )
    {
        copy_Log( "pedk_copy_job_req NULL" );
        return -1;
    }

    copy_job_request = &pedk_copy_job_req->copy_job_req;

    copy_job_request->save_toner_mode = value;
    return 0;
}

#if 0
int convert_param_7625_2_PEDK_inputpapersize()
{

    int      i            = 0;
    int      array_num    = 0;

    array_num = sizeof( g_paper_size_convert_table ) / sizeof( g_paper_size_convert_table[0] );

    for( i = 0; i < array_num; i++ )
    {
        if( g_copy_req_7265.scan_size == g_paper_size_convert_table[i].paper_size_7625 )
        {
            g_copy_req_PEDK.scan_size = g_paper_size_convert_table[i].paper_size_PEDK;
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err  %d \n", g_copy_req_7265.scan_size );
        return -1;
    }


    return 0;
}



int convert_param_7625_2_PEDK_outputtray()
{
    int      i            = 0;
    int      array_num    = 0;

    array_num = sizeof( g_paper_size_convert_table ) / sizeof( g_paper_size_convert_table[0] );

    for( i = 0; i < array_num; i++ )
    {
        if( g_copy_req_7265.print_size == g_paper_size_convert_table[i].paper_size_7625 )
        {
            g_copy_req_PEDK.print_size = g_paper_size_convert_table[i].paper_size_PEDK;
            break;
        }
    }

    if( i >= array_num )
    {
        copy_Log( "pape size err  %d \n", g_copy_req_PEDK.print_size );
    }


    /**
        * | 纸盒类型 ||
        * |:--:|:--:|:--:
        * | 自动选择纸盒              | 0 |
        * | 多功能纸盒              | 1 |
        * | 标准纸盒                | 2 |
        * | 选配纸盒1               | 3 |
        * | 选配纸盒2               | 4 |
        * | 选配纸盒3               | 5 |
        * | 内置大容量纸盒            | 6 |
        * | 外置大容量纸盒            | 7 |
    **/

    switch( g_copy_req_7265.print_tray )
    {
    case 0x64:
        g_copy_req_PEDK.print_tray = 0;//TRAY_INPUT_AUTO;
        break;
    case 0x06:
        g_copy_req_PEDK.print_tray = 1;//TRAY_INPUT_MULTIFUNCTION;
        break;
//    case 0x00:
//        g_copy_req_PEDK.print_tray = 2;//TRAY_INPUT_STANDARD;
//        break;
//    case 0x00:
//        g_copy_req_PEDK.print_tray = 3;//TRAY_INPUT_STANDARD;
//        break;
    case 0x01:
        g_copy_req_PEDK.print_tray = 4;//TRAY_INPUT_2;
        break;
    case 0x02:
        g_copy_req_PEDK.print_tray = 5;//TRAY_INPUT_3;
        break;
    case 0x04:
        g_copy_req_PEDK.print_tray = 6;//TRAY_INPUT_External_LCT_IN;
        break;
    case 0x05:
        g_copy_req_PEDK.print_tray = 7;//TRAY_INPUT_External_LCT_OUT;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_PEDK.print_tray );
    }


    return 0;
}



int convert_param_7625_2_PEDK_zoom()
{

    if( g_copy_req_7265.auto_scale_mode == 0 )
    {
        g_copy_req_PEDK.auto_scale_mode = g_copy_req_7265.scale_percent;
    }
    else if( g_copy_req_7265.auto_scale_mode == 1 )
    {
        g_copy_req_PEDK.auto_scale_mode = 0;
        g_copy_req_PEDK.scale_percent   = g_copy_req_7265.scale_percent;
    }
    else
    {
        g_copy_req_PEDK.auto_scale_mode = 0;
        g_copy_req_PEDK.scale_percent   = 100;
    }

    return 0;
}



int convert_param_7625_2_PEDK_image_color()
{
    g_copy_req_PEDK.image_brightness    = g_copy_req_7265.image_brightness;
    g_copy_req_PEDK.image_saturation    = g_copy_req_7265.image_saturation;
    g_copy_req_PEDK.image_contrast      = g_copy_req_7265.image_contrast;
    g_copy_req_PEDK.image_sharpness     = g_copy_req_7265.image_sharpness;
    g_copy_req_PEDK.image_hue           = g_copy_req_7265.image_hue;
    g_copy_req_PEDK.backgroundmove_level = g_copy_req_7265.backgroundmove_level;
    return 0;
}

int convert_param_7625_2_PEDK_copymode()
{
    switch( g_copy_req_7265.print_mode )
    {
    case 0:
        if( g_copy_req_7265.scan_source == 1 || g_copy_req_7265.scan_source == 2 )
        {
            g_copy_req_PEDK.print_mode = 1;
        }
        else if( g_copy_req_7265.scan_source == 4 )
        {
            g_copy_req_PEDK.print_mode = 3;
        }
        break;
    case 1:
        if( g_copy_req_7265.scan_source == 1 || g_copy_req_7265.scan_source == 2 )
        {
            g_copy_req_PEDK.print_mode = 2;
        }
        else if( g_copy_req_7265.scan_source == 4 )
        {
            g_copy_req_PEDK.print_mode = 4;
        }
        break;
    default:
        copy_Log( "PEDK param err   %d  %d \n", g_copy_req_7265.print_mode, g_copy_req_7265.scan_source );
    }

    return 0;
}



int convert_param_7625_2_PEDK_collatemode()
{
    switch( g_copy_req_7265.collate )
    {
    case 0:
        g_copy_req_PEDK.collate = 0;
        break;
    case 1:
        g_copy_req_PEDK.collate = 1;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_PEDK.collate );
    }
    return 0;
}



int convert_param_7625_2_PEDK_qualitytype()
{

    /**
     * 1:文本
     * 2:文本+画像
     * 3:画像
     * 4:自动调节
     */

    switch( g_copy_req_7265.quality )
    {
    case 0:
        g_copy_req_PEDK.quality = 4;// COPY_QUALITY_AUTO;
        break;
    case 1:
        g_copy_req_PEDK.quality = 2;//COPY_QUALITY_TXT
        break;
    case 2:
        g_copy_req_PEDK.quality = 3;//COPY_QUALITY_MIXED
        break;
    case 3:
        g_copy_req_PEDK.quality = 1;//COPY_QUALITY_PICTURE;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.color_mode );
    }

    return 0;
}


int convert_param_7625_2_PEDK_watermark()
{
    g_copy_req_PEDK.watermark_mode = g_copy_req_7265.watermark_mode;
    strcpy( g_copy_req_PEDK.watermark_string, g_copy_req_7265.watermark_string );
    return 0;
}


int convert_param_7625_2_PEDK_copies()
{
    g_copy_req_PEDK.copies = g_copy_req_7265.copies;
    return 0;
}

int convert_param_7625_2_PEDK_autocorrectionmode()
{
    g_copy_req_PEDK.auto_id_correction = g_copy_req_7265.auto_id_correction;
    return 0;
}

int convert_param_7625_2_PEDK_arrangementmode()
{
    //g_copy_req_PEDK.auto_id_correction = g_copy_req_7265.auto_id_correction;
    return 0;
}

int convert_param_7625_2_PEDK_nupmode()
{
    switch( g_copy_req_7265.nup_type )
    {
    case 0:
        g_copy_req_PEDK.nup_type = 0;//COPY_NUP_CLOSE
        break;
    case 1:
        g_copy_req_PEDK.nup_type = 1;//COPY_NUP_2IN1
        break;
    case 2:
        if( g_copy_req_7265.nup_combination == 1 )
        {
            g_copy_req_PEDK.nup_type = 2;//COPY_NUP_4IN1
        }
        else if( g_copy_req_PEDK.nup_combination == 2 )
        {
            g_copy_req_PEDK.nup_type = 3;//COPY_NUP_4IN1
        }
        else
        {
            copy_Log( "7265 param err   %d \n", g_copy_req_7265.nup_combination );
        }
        break;
    default:
        copy_Log( "7265 param err   %d \n", g_copy_req_7265.nup_type );
    }

    return 0;
}

int convert_param_7625_2_PEDK_clonemode()
{

    switch( g_copy_req_7265.clone_mode )
    {
    case 0:
        g_copy_req_PEDK.clone_mode = 0;//COPY_CLONE_OFF
        break;
    case 1:
        g_copy_req_PEDK.clone_mode = 1;//COPY_CLONE_2x2
        break;
    case 2:
        g_copy_req_PEDK.clone_mode = 2;//COPY_CLONE_3x3
        break;
    case 3:
        g_copy_req_PEDK.clone_mode = 3;//COPY_CLONE_4x4
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.clone_mode );
    }
    return 0;
}


int convert_param_7625_2_PEDK_poster()
{
    if( g_copy_req_7265.poster_size >= 0 && g_copy_req_7265.poster_size <= 19 )
    {
        g_copy_req_PEDK.poster_size = g_copy_req_7265.poster_size;
    }
    else
    {
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.poster_size );
    }
    return 0;
}

int convert_param_7625_2_PEDK_color_mode()
{
    /**
       * | 色彩模式 || Kanas | 4020 |
       * |:--:|:--:|:--:|:--:|
       * | 黑白 | 0 | 支持 | 支持 |
       * | 彩色 | 1 | 支持 | 不支持|
       * | 红黑 | 2 | 支持 | 不支持|
       */

    switch( g_copy_req_7265.color_mode )
    {
    case 0:
        g_copy_req_PEDK.color_mode = 0;
        break;
    case 1:
        g_copy_req_PEDK.color_mode = 1;
        break;
    case 4:
        g_copy_req_PEDK.color_mode = 2;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.color_mode );
    }

    return 0;
}

int convert_param_7625_2_PEDK_scan_source()
{
    /**
    * | 来源 || Kanas | 4020 |
    * |:--:|:--:|:--:|:--:|
    * | 自动 | 0 | 支持 | 支持 |
    * | DADF | 1 | 支持 | 支持 |
    * | ADF | 2 | 支持 | 支持 |
    * | FB | 3 | 支持 | 支持 |
    * | MADF | 4 | 不支持| 支持 |
    */

    switch( g_copy_req_7265.scan_source )
    {
    case 0:
        g_copy_req_PEDK.scan_source = 0;//SCAN_MODE_AUTO
        break;
    case 1:
        g_copy_req_PEDK.scan_source = 3;//SCAN_MODE_FB
        break;
    case 2:
        g_copy_req_PEDK.scan_source = 2;//SCAN_MODE_ADF
        break;
    case 3:
        g_copy_req_PEDK.scan_source = 4;//SCAN_MODE_MADF
        break;
    case 4:
        g_copy_req_PEDK.scan_source = 1;//SCAN_MODE_DADF
        break;

    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.scan_source );
    }

    return 0;
}



int convert_param_7625_2_PEDK_image_orientaiton()
{
    g_copy_req_PEDK.image_orientation = g_copy_req_7265.image_orientation;
    return 0;
}


int convert_param_7625_2_PEDK_color_balance()
{
    g_copy_req_PEDK.color_balance_c = g_copy_req_7265.color_balance_c;
    g_copy_req_PEDK.color_balance_m = g_copy_req_7265.color_balance_m;
    g_copy_req_PEDK.color_balance_y = g_copy_req_7265.color_balance_y;
    g_copy_req_PEDK.color_balance_k = g_copy_req_7265.color_balance_k;
    return 0;
}



int convert_param_7625_2_PEDK_horizontal_margin()
{
    g_copy_req_PEDK.horizontal_margin = g_copy_req_7265.horizontal_margin;
    return 0;
}


int convert_param_7625_2_PEDK_vertical_margin()
{
    g_copy_req_PEDK.vertical_margin = g_copy_req_7265.vertical_margin;
    return 0;
}

int convert_param_7625_2_PEDK_edge_to_edge_mode()
{
    g_copy_req_PEDK.edge_to_edge_mode = g_copy_req_7265.edge_to_edge_mode;
    return 0;
}

int convert_param_7625_2_PEDK_separator()
{
    g_copy_req_PEDK.separator = g_copy_req_7265.separator;
    return 0;
}

int convert_param_7625_2_PEDK_nup_combination()
{
    g_copy_req_PEDK.nup_combination = g_copy_req_7265.nup_combination;
    return 0;
}

int convert_param_7625_2_PEDK_booklet_duplex()
{
    g_copy_req_PEDK.booklet_duplex = g_copy_req_7265.booklet_duplex;
    return 0;
}

int convert_param_7625_2_PEDK_mirror_mode()
{
    g_copy_req_PEDK.mirror_mode = g_copy_req_7265.mirror_mode;
    return 0;
}

int convert_param_7625_2_PEDK_page_header_enable()
{
    g_copy_req_PEDK.page_header.enable = g_copy_req_7265.page_header.enable;
    return 0;
}

int convert_param_7625_2_PEDK_page_header_position()
{
    g_copy_req_PEDK.page_header.position = g_copy_req_7265.page_header.position;
    return 0;
}

int convert_param_7625_2_PEDK_page_header_pagination()
{
    g_copy_req_PEDK.page_header.pagination = g_copy_req_7265.page_header.pagination;
    return 0;
}

int convert_param_7625_2_PEDK_page_header_text_type()
{
    g_copy_req_PEDK.page_header.text_type = g_copy_req_7265.page_header.text_type;
    return 0;
}

int convert_param_7625_2_PEDK_page_header_text()
{
    strcpy( g_copy_req_PEDK.page_header.text, g_copy_req_7265.page_header.text );
    return 0;
}

int convert_param_7625_2_PEDK_page_footer_enable()
{
    g_copy_req_PEDK.page_footer.enable = g_copy_req_7265.page_footer.enable;
    return 0;
}

int convert_param_7625_2_PEDK_page_footer_position()
{
    g_copy_req_PEDK.page_footer.position = g_copy_req_7265.page_footer.position;
    return 0;
}


int convert_param_7625_2_PEDK_page_footer_pagination()
{
    g_copy_req_PEDK.page_footer.pagination = g_copy_req_7265.page_footer.pagination;
    return 0;
}


int convert_param_7625_2_PEDK_page_footer_text_type()
{
    g_copy_req_PEDK.page_footer.text_type = g_copy_req_7265.page_footer.text_type;
    return 0;
}


int convert_param_7625_2_PEDK_page_footer_text()
{
    strcpy( g_copy_req_PEDK.page_footer.text, g_copy_req_7265.page_footer.text );
    return 0;
}


int convert_param_7625_2_PEDK_backup_mode()
{
    g_copy_req_PEDK.backup_mode = g_copy_req_7265.backup_mode;
    return 0;
}


int convert_param_7625_2_PEDK_sample_mode()
{
    g_copy_req_PEDK.sample_mode = g_copy_req_7265.sample_mode;
    return 0;
}


int convert_param_7625_2_PEDK_use_color_inversion()
{
    g_copy_req_PEDK.use_color_inversion = g_copy_req_7265.use_color_inversion;
    return 0;
}


int convert_param_7625_2_PEDK_use_copy_scan_meantime()
{
    g_copy_req_PEDK.use_copy_scan_meantime = g_copy_req_7265.use_copy_scan_meantime;
    return 0;
}


int convert_param_7625_2_PEDK_use_remove_color()
{
    g_copy_req_PEDK.use_removel_color = g_copy_req_7265.use_removel_color;
    return 0;
}


int convert_param_7625_2_PEDK_use_edge_clean()
{
    g_copy_req_PEDK.use_edge_clean = g_copy_req_7265.use_edge_clean;
    return 0;
}


int convert_param_7625_2_PEDK_filter_edge_margin()
{
    g_copy_req_PEDK.filter_edge_margin_top      = g_copy_req_7265.filter_edge_margin_top;
    g_copy_req_PEDK.filter_edge_margin_bottom   = g_copy_req_7265.filter_edge_margin_bottom;
    g_copy_req_PEDK.filter_edge_margin_left     = g_copy_req_7265.filter_edge_margin_left;
    g_copy_req_PEDK.filter_edge_margin_right    = g_copy_req_7265.filter_edge_margin_right;
    return 0;
}


int convert_param_7625_2_PEDK_copies_flip()
{
    g_copy_req_PEDK.copies_flip = g_copy_req_7265.copies_flip;
    return 0;
}

int convert_param_7625_2_PEDK_original_flip()
{
    g_copy_req_PEDK.original_flip = g_copy_req_7265.original_flip;
    return 0;
}

int convert_param_7625_2_PEDK_cover()
{
    g_copy_req_PEDK.cover = g_copy_req_7265.cover;
    return 0;
}


int convert_param_7625_2_PEDK_cover_back()
{
    g_copy_req_PEDK.cover_back = g_copy_req_7265.cover_back;
    return 0;
}


int convert_param_7625_2_PEDK_cover_tray_in()
{
    /**
        * | 纸盒类型 ||
        * |:--:|:--:|:--:
        * | 自动选择纸盒              | 0 |
        * | 多功能纸盒              | 1 |
        * | 标准纸盒                | 2 |
        * | 选配纸盒1               | 3 |
        * | 选配纸盒2               | 4 |
        * | 选配纸盒3               | 5 |
        * | 内置大容量纸盒            | 6 |
        * | 外置大容量纸盒            | 7 |
    **/
    switch( g_copy_req_7265.cover_tray_in )
    {
    case 0x64:
        g_copy_req_PEDK.cover_tray_in = 0;//TRAY_INPUT_AUTO;
        break;
    case 0x06:
        g_copy_req_PEDK.cover_tray_in = 1;//TRAY_INPUT_MULTIFUNCTION;
        break;
//    case 0x00:
//        g_copy_req_PEDK.cover_tray_in = 2;//TRAY_INPUT_STANDARD;
//        break;
//    case 0x00:
//        g_copy_req_PEDK.cover_tray_in = 3;//TRAY_INPUT_STANDARD;
//        break;
    case 0x01:
        g_copy_req_PEDK.cover_tray_in = 4;//TRAY_INPUT_2;
        break;
    case 0x02:
        g_copy_req_PEDK.cover_tray_in = 5;//TRAY_INPUT_3;
        break;
    case 0x04:
        g_copy_req_PEDK.cover_tray_in = 6;//TRAY_INPUT_External_LCT_IN;
        break;
    case 0x05:
        g_copy_req_PEDK.cover_tray_in = 7;//TRAY_INPUT_External_LCT_OUT;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.cover_tray_in );
    }

    return 0;
}


int convert_param_7625_2_PEDK_staple_mode()
{
    switch( g_copy_req_7265.staple_mode )
    {
    case 0:
        g_copy_req_PEDK.staple_mode = 0;//STAPLE_MODE_OFF
        break;
    case 1:
        g_copy_req_PEDK.staple_mode = 1;//STAPLE_MODE_LEFT_AUTO
        break;
    case 2:
        g_copy_req_PEDK.staple_mode = 2;//STAPLE_MODE_RIGHT_AUTO;
        break;
    case 10:
        g_copy_req_PEDK.staple_mode = 3;//STAPLE_MODE_BOTTOM_LEFT_AUTO;
        break;
    case 11:
        g_copy_req_PEDK.staple_mode = 4;//STAPLE_MODE_BOTTOM_RIGHT_AUTO;
        break;
    case 4:
        g_copy_req_PEDK.staple_mode = 5;//STAPLE_MODE_LEFT_ZREO;
        break;
    case 3:
        g_copy_req_PEDK.staple_mode = 6;//STAPLE_MODE_RIGHT_ZREO;
        break;
    case 12:
        g_copy_req_PEDK.staple_mode = 7;//STAPLE_MODE_TOP_LEFT_ZERO;
        break;
    case 13:
        g_copy_req_PEDK.staple_mode = 8;//STAPLE_MODE_TOP_RIGHT_ZERO;
        break;
    case 16:
        g_copy_req_PEDK.staple_mode = 9;//STAPLE_MODE_DOUBLE_LEFT;
        break;
    case 17:
        g_copy_req_PEDK.staple_mode = 10;//STAPLE_MODE_DOUBLE_TOP;
        break;
    case 18:
        g_copy_req_PEDK.staple_mode = 11;//STAPLE_MODE_DOUBLE_RIGHT;
        break;
    case 19:
        g_copy_req_PEDK.staple_mode = 12;//STAPLE_MODE_DOUBLE_BOTTOM;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.staple_mode );

    }

    return 0;
}


int convert_param_7625_2_PEDK_punch_mode()
{
    g_copy_req_PEDK.punch_mode = g_copy_req_7265.punch_mode;
    return 0;
}


int convert_param_7625_2_PEDK_fold_mode()
{
    g_copy_req_PEDK.fold_mode = g_copy_req_7265.fold_mode;
    return 0;
}


int convert_param_7625_2_PEDK_tray_receive()
{
    g_copy_req_PEDK.tray_receive = g_copy_req_7265.tray_receive;
    return 0;
}


int convert_param_7625_2_PEDK_shift_mode()
{
    g_copy_req_PEDK.shift_mode = g_copy_req_7265.shift_mode;
    return 0;
}


int convert_param_7625_2_PEDK_flod_number()
{
    g_copy_req_PEDK.fold_number = g_copy_req_7265.fold_number;
    return 0;
}


int convert_param_7625_2_PEDK_have_stapler()
{
    g_copy_req_PEDK.have_stapler = g_copy_req_7265.have_stapler;
    return 0;
}


int convert_param_7625_2_PEDK_cover_paper_type()
{

    /**
     * | 纸张类型 || Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | 普通纸 | 0 | 支持 | 支持 |
     * | 厚纸1 | 1 | 支持 | 支持 |
     * | 厚纸2 | 2 | 支持 | 不支持|
     * | 厚纸3 | 3 | 支持 | 不支持|
     * | 厚纸4 | 4 | 支持 | 不支持|
     * | 信封 | 5 | 支持 | 支持 |
     * | 透明胶片 | 6 | 不支持| 支持 |
     * | 明信片| 7 | 支持 | 支持 |
     * | 薄纸 | 8 | 不支持| 支持 |
     * | 标签纸| 9 | 支持 | 支持 |
     * | 较硬纸| 10 | 不支持| 支持 |
     * | tab| 11 | 支持 | 不支持|
     * | RECYCLED_SILENCE | 12 | 支持 | 不支持|
     * | ORDINARY_SILENCE | 13 | 支持 | 不支持|
     * | THIN_SILENCE | 14 | 支持 | 不支持|
     */

    switch( g_copy_req_7265.cover_paper_type )
    {
    case 0:
        g_copy_req_PEDK.cover_paper_type = 0;//PAPER_TYPE_ORDINARY
        break;
    case 3:
        g_copy_req_PEDK.cover_paper_type = 1;//PAPER_TYPE_THICK1
        break;
    case 5:
        g_copy_req_PEDK.cover_paper_type = 2;//PAPER_TYPE_THICK2;
        break;
    case 6:
        g_copy_req_PEDK.cover_paper_type = 3;//PAPER_TYPE_THICK3;
        break;
    case 7:
        g_copy_req_PEDK.cover_paper_type = 4;//PAPER_TYPE_THICK4;
        break;
    case 10:
        g_copy_req_PEDK.cover_paper_type = 5;//PAPER_TYPE_LETTER_HEAD;
        break;
//    case 0:
//        g_copy_req_PEDK.cover_paper_type = 6;//not support;
//        break;
    case 9:
        g_copy_req_PEDK.cover_paper_type = 7;//PAPER_TYPE_POST_CARD;
        break;
    /*case 0:
        g_copy_req_PEDK.cover_paper_type = 8;//not support;
        break;
    */
    case 12:
        g_copy_req_PEDK.cover_paper_type = 9;//PAPER_TYPE_LABEL;
        break;
    /*case 0:
        g_copy_req_PEDK.cover_paper_type = 10;//not support;
        break;
    */
    case 11:
        g_copy_req_PEDK.cover_paper_type = 11;//PAPER_TYPE_TAB;
        break;
    case 21:
        g_copy_req_PEDK.cover_paper_type = 12;//PAPER_TYPE_RECYCLE;
        break;
    case 19:
        g_copy_req_PEDK.cover_paper_type = 13;//PAPER_TYPE_ORDINARY_SILENCE;
        break;
    case 20:
        g_copy_req_PEDK.cover_paper_type = 14;//PAPER_TYPE_THIN_SILENCE;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.cover_paper_type );

    }
    return 0;
}



int convert_param_7625_2_PEDK_separator_tray_in()
{
    /**
        * | 纸盒类型 | |
        * |:--:|:--:|:--:
        * | 自动选择纸盒              | 0 |
        * | 多功能纸盒              | 1 |
        * | 标准纸盒                | 2 |
        * | 选配纸盒1               | 3 |
        * | 选配纸盒2               | 4 |
        * | 选配纸盒3               | 5 |
        * | 内置大容量纸盒            | 6 |
        * | 外置大容量纸盒            | 7 |
    **/
    switch( g_copy_req_7265.separator_tray_in )
    {
    case 0x64:
        g_copy_req_PEDK.separator_tray_in = 0;//TRAY_INPUT_AUTO;
        break;
    case 0x06:
        g_copy_req_PEDK.separator_tray_in = 1;//TRAY_INPUT_MULTIFUNCTION;
        break;
    case 0x00:
        g_copy_req_PEDK.separator_tray_in = 2;//TRAY_INPUT_STANDARD;
        break;
    case 0x01:
        g_copy_req_PEDK.separator_tray_in = 3;//TRAY_INPUT_2;
        break;
    case 0x02:
        g_copy_req_PEDK.separator_tray_in = 4;//TRAY_INPUT_3;
        break;
    case 0x03:
        g_copy_req_PEDK.separator_tray_in = 5;//TRAY_INPUT_4;
        break;
    case 0x04:
        g_copy_req_PEDK.separator_tray_in = 6;//TRAY_INPUT_External_LCT_IN;
        break;
    case 0x05:
        g_copy_req_PEDK.separator_tray_in = 7;//TRAY_INPUT_External_LCT_OUT;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.separator_tray_in );
    }


    return 0;
}


int convert_param_7625_2_PEDK_user_define_scan_paper_param()
{
    g_copy_req_PEDK.scan_width  = g_copy_req_7265.scan_width;
    g_copy_req_PEDK.scan_height = g_copy_req_7265.scan_height;
    return 0;
}



int convert_param_7625_2_PEDK_user_define_print_paper_param()
{
    g_copy_req_PEDK.print_width  = g_copy_req_7265.print_width;
    g_copy_req_PEDK.print_height = g_copy_req_7265.print_height;
    return 0;
}


int convert_param_7625_2_PEDK_page_type()
{
    /**
     * | 纸张类型 || Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | 普通纸 | 0 | 支持 | 支持 |
     * | 厚纸1 | 1 | 支持 | 支持 |
     * | 厚纸2 | 2 | 支持 | 不支持|
     * | 厚纸3 | 3 | 支持 | 不支持|
     * | 厚纸4 | 4 | 支持 | 不支持|
     * | 信封 | 5 | 支持 | 支持 |
     * | 透明胶片 | 6 | 不支持| 支持 |
     * | 明信片| 7 | 支持 | 支持 |
     * | 薄纸 | 8 | 不支持| 支持 |
     * | 标签纸| 9 | 支持 | 支持 |
     * | 较硬纸| 10 | 不支持| 支持 |
     * | tab| 11 | 支持 | 不支持|
     * | RECYCLED_SILENCE | 12 | 支持 | 不支持|
     * | ORDINARY_SILENCE | 13 | 支持 | 不支持|
     * | THIN_SILENCE | 14 | 支持 | 不支持|
     */

    switch( g_copy_req_7265.page_type )
    {
    case 0:
        g_copy_req_PEDK.page_type = 0;//PAPER_TYPE_ORDINARY
        break;
    case 3:
        g_copy_req_PEDK.page_type = 1;//PAPER_TYPE_THICK1
        break;
    case 5:
        g_copy_req_PEDK.page_type = 2;//PAPER_TYPE_THICK2;
        break;
    case 6:
        g_copy_req_PEDK.page_type = 3;//PAPER_TYPE_THICK3;
        break;
    case 7:
        g_copy_req_PEDK.page_type = 4;//PAPER_TYPE_THICK4;
        break;
    case 10:
        g_copy_req_PEDK.page_type = 5;//PAPER_TYPE_LETTER_HEAD;
        break;
//    case 0:
//        g_copy_req_PEDK.page_type = 6;//not support;
//        break;
    case 9:
        g_copy_req_PEDK.page_type = 7;//PAPER_TYPE_POST_CARD;
        break;
//    case 0:
//        g_copy_req_PEDK.page_type = 8;//not support;
//        break;
    case 12:
        g_copy_req_PEDK.page_type = 9;//PAPER_TYPE_LABEL;
        break;
//    case 0:
//        g_copy_req_PEDK.page_type = 10;//not support;
//        break;
    case 11:
        g_copy_req_PEDK.page_type = 11;//PAPER_TYPE_TAB;
        break;
    case 21:
        g_copy_req_PEDK.page_type = 12;//PAPER_TYPE_RECYCLE;
        break;
    case 19:
        g_copy_req_PEDK.page_type = 13;//PAPER_TYPE_ORDINARY_SILENCE;
        break;
    case 20:
        g_copy_req_PEDK.page_type = 14;//PAPER_TYPE_THIN_SILENCE;
        break;
    default:
        copy_Log( "PEDK param err   %d \n", g_copy_req_7265.page_type );
    }


    return 0;
}





int convert_param_7625_2_PEDK_skewing_mode()
{
    g_copy_req_PEDK.skewing_mode = g_copy_req_7265.skewing_mode;
    return 0;
}


int convert_param_7625_2_PEDK_toner_mode()
{
    g_copy_req_PEDK.save_toner_mode = g_copy_req_7265.save_toner_mode;
    return 0;
}

#endif

static JSValue copy_jsc_addParameter( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int ret = 0;
    char* param_str = 0;
    char* val_str = 0;
    int copy_param_id = 0;
//    int val = 0;


    param_str = JS_ToCString( ctx, argv[0] );
    JS_ToInt32( ctx, &copy_param_id, argv[1] );
    val_str = JS_ToCString( ctx, argv[2] );
    copy_Log( "add param id=[%d] ==> [%s]=[%s] ", copy_param_id, param_str, val_str );

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = get_pedk_copy_job_request(copy_param_id );
    if(pedk_copy_job_request == NULL)
    {
        ret = -1;
        return JS_NewInt32( ctx, ret );
    }
    COPY_JOB_REQUEST_DATA_S* copy_job_request = &pedk_copy_job_request->copy_job_req;

    if( strcmp( param_str, COPY_PARAM_INPUTPAPERSIZE ) == 0 )
    {
        convert_param_PEDK_2_7625_inputpapersize( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SCAN_SIZE, &copy_job_request->scan_size, sizeof( copy_job_request->scan_size ),copy_param_id );

    }
    else if( strcmp( param_str, COPY_PARAM_OUTPUTTRAY_TRAY ) == 0 )
    {
        convert_param_PEDK_2_7625_outputtray_tray( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PRINT_TRAY, &copy_job_request->print_tray, sizeof( copy_job_request->print_tray ),copy_param_id );

    }
    else if( strcmp( param_str, COPY_PARAM_OUTPUTTRAY_SIZE ) == 0 )
    {
        convert_param_PEDK_2_7625_outputtray_size( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PRINT_SIZE, &copy_job_request->print_size, sizeof( copy_job_request->print_size ),copy_param_id );

    }
    else if( strcmp( param_str, COPY_PARAM_ZOOM ) == 0 )
    {
        convert_param_PEDK_2_7625_zoom( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_AUTO_SCALE_MODE, &copy_job_request->auto_scale_mode, sizeof( copy_job_request->auto_scale_mode ),copy_param_id );
        if(copy_job_request->auto_scale_mode == 0)
        {
            copy_Log( "set scale percent\n" );
            ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SCALE_PERCENT, &copy_job_request->scale_percent, sizeof( copy_job_request->scale_percent ),copy_param_id );
        }
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_BRIGHTNESS ) == 0 )
    {
        convert_param_PEDK_2_7625_image_color_brightness( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_IMAGE_BRIGHTNESS, &copy_job_request->image_brightness, sizeof( copy_job_request->image_brightness ),copy_param_id );

    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_CONTRAST ) == 0 )
    {
        convert_param_PEDK_2_7625_image_color_contrast( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_IMAGE_CONTRAST, &copy_job_request->image_contrast, sizeof( copy_job_request->image_contrast ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_HUE ) == 0 )
    {
        convert_param_PEDK_2_7625_image_color_hue( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_IMAGE_HUE, &copy_job_request->image_hue, sizeof( copy_job_request->image_hue ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_SATURATION ) == 0 )
    {
        convert_param_PEDK_2_7625_image_color_saturation( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_IMAGE_SATURATION, &copy_job_request->image_saturation, sizeof( copy_job_request->image_saturation ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_SHARPNESS ) == 0 )
    {
        convert_param_PEDK_2_7625_image_color_sharpness( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_IMAGE_SHARPNESS, &copy_job_request->image_sharpness, sizeof( copy_job_request->image_sharpness ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_BACKGROUNDMOVE_LEVEL ) == 0 )
    {
        convert_param_PEDK_2_7625_image_color_backgroundmove_level( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_BACKGROUNDMOVE_LEVEL, &copy_job_request->backgroundmove_level, sizeof( copy_job_request->backgroundmove_level ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_COPYMODE ) == 0 )
    {
        convert_param_PEDK_2_7625_copymode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PRINT_MODE, &copy_job_request->print_mode, sizeof( copy_job_request->print_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_COLLATEMODE ) == 0 )
    {
        convert_param_PEDK_2_7625_collatemode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COLLATE, &copy_job_request->collate, sizeof( copy_job_request->collate ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_QUALITYTYPE ) == 0 )
    {
        convert_param_PEDK_2_7625_qualitytype( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_QUALITY, &copy_job_request->quality, sizeof( copy_job_request->quality ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_WATERMARK_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_watermark_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_WATERMARK_MODE, &copy_job_request->watermark_mode, sizeof( copy_job_request->watermark_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_WATERMARK_STR ) == 0 )
    {
        convert_param_PEDK_2_7625_watermark_str(copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_WATERMARK_STRING, copy_job_request->watermark_string, sizeof( copy_job_request->watermark_string ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_COPIES ) == 0 )
    {
        convert_param_PEDK_2_7625_copies( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COPIES, &copy_job_request->copies, sizeof( copy_job_request->copies ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_AUTOCORRECTIONMODE ) == 0 )
    {
        convert_param_PEDK_2_7625_autocorrectionmode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_AUTO_ID_CORRECTION, &copy_job_request->auto_id_correction, sizeof( copy_job_request->auto_id_correction ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_ARRANGEMENTMODE ) == 0 )
    {
        convert_param_PEDK_2_7625_arrangementmode( copy_param_id , atoi(val_str) );
        //ret = PEDK_firmware_adaptor_set_copy_param(SINGLE_COPY_PARAM_NUP_COMBINATION, &copy_job_request->nup_combination, sizeof(copy_job_request->nup_combination));
    }
    else if( strcmp( param_str, COPY_PARAM_NUPMODE ) == 0 )
    {
        convert_param_PEDK_2_7625_nupmode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_NUP_TYPE, &copy_job_request->nup_type, sizeof( copy_job_request->nup_type ),copy_param_id );
        ret = PEDK_firmware_adaptor_set_copy_param(SINGLE_COPY_PARAM_NUP_COMBINATION, &copy_job_request->nup_combination, sizeof(copy_job_request->nup_combination),copy_param_id);
    }
    else if( strcmp( param_str, COPY_PARAM_CLONEMODE ) == 0 )
    {
        convert_param_PEDK_2_7625_clonemode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_CLONE_MODE, &copy_job_request->clone_mode, sizeof( copy_job_request->clone_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_POSTER ) == 0 )
    {
        convert_param_PEDK_2_7625_poster( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_POSTER_SIZE, &copy_job_request->poster_size, sizeof( copy_job_request->poster_size ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_color_mode( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COLOR_MODE, &copy_job_request->color_mode, sizeof( copy_job_request->color_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SCAN_SOURCE ) == 0 )
    {
        convert_param_PEDK_2_7625_scan_source( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SCAN_SOURCE, &copy_job_request->scan_source, sizeof( copy_job_request->scan_source ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_IMAGE_ORIENTATION ) == 0 )
    {
        convert_param_PEDK_2_7625_image_orientaiton( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_IMAGE_ORIENTATION, &copy_job_request->image_orientation, sizeof( copy_job_request->image_orientation ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_C ) == 0 )
    {
        convert_param_PEDK_2_7625_color_balance_c( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COLOR_BALANCE_C, &copy_job_request->color_balance_c, sizeof( copy_job_request->color_balance_c ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_M ) == 0 )
    {
        convert_param_PEDK_2_7625_color_balance_m( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COLOR_BALANCE_M, &copy_job_request->color_balance_m, sizeof( copy_job_request->color_balance_m ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_Y ) == 0 )
    {
        convert_param_PEDK_2_7625_color_balance_y( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COLOR_BALANCE_Y, &copy_job_request->color_balance_y, sizeof( copy_job_request->color_balance_y ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_K ) == 0 )
    {
        convert_param_PEDK_2_7625_color_balance_k( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COLOR_BALANCE_K, &copy_job_request->color_balance_k, sizeof( copy_job_request->color_balance_k ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_HORIZONTAL_MARGIN ) == 0 )
    {
        convert_param_PEDK_2_7625_horizontal_margin( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_HORIZONTAL_MARGIN, &copy_job_request->horizontal_margin, sizeof( copy_job_request->horizontal_margin ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_VERTICAL_MARGIN ) == 0 )
    {
        convert_param_PEDK_2_7625_vertical_margin( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_VERTICAL_MARGIN, &copy_job_request->vertical_margin, sizeof( copy_job_request->vertical_margin ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_EDGE_TO_EDGE_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_edge_to_edge_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_EDGE_TO_EDGE_MODE, &copy_job_request->edge_to_edge_mode, sizeof( copy_job_request->edge_to_edge_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SEPARATOR ) == 0 )
    {
        convert_param_PEDK_2_7625_separator( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SEPARATOR, &copy_job_request->separator, sizeof( copy_job_request->separator ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_NUP_COMBINATION ) == 0 )
    {
        //convert_param_PEDK_2_7625_nup_combination( copy_param_id , atoi(val_str) );
        //ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_NUP_COMBINATION, &copy_job_request->nup_combination, sizeof( copy_job_request->nup_combination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_BOOKLET_DUPLEX ) == 0 )
    {
        //convert_param_PEDK_2_7625_booklet_duplex( copy_param_id , atoi(val_str) );
        //ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_BOOKLET_DUPLEX, &copy_job_request->nup_combination, sizeof( copy_job_request->nup_combination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_MIRROR_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_mirror_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_MIRROR_MODE, &copy_job_request->mirror_mode, sizeof( copy_job_request->mirror_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_ENABLE ) == 0 )
    {
        convert_param_PEDK_2_7625_page_header_enable( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_HEADER_ENABLE, &copy_job_request->page_footer.enable, sizeof( copy_job_request->page_footer.enable ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_POSITION ) == 0 )
    {
        convert_param_PEDK_2_7625_page_header_position( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_HEADER_POSITION, &copy_job_request->page_footer.position, sizeof( copy_job_request->page_footer.position ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_PAGINATION ) == 0 )
    {
        convert_param_PEDK_2_7625_page_header_pagination( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_HEADER_PAGINATION, &copy_job_request->page_footer.pagination, sizeof( copy_job_request->page_footer.pagination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_TEXT_TYPE ) == 0 )
    {
        convert_param_PEDK_2_7625_page_header_text_type( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_HEADER_TEXT_TYPE, &copy_job_request->page_footer.text_type, sizeof( copy_job_request->page_footer.text_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_TEXT ) == 0 )
    {
        convert_param_PEDK_2_7625_page_header_text( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_HEADER_TEXT, copy_job_request->page_footer.text, sizeof( copy_job_request->page_footer.text ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_ENABLE ) == 0 )
    {
        convert_param_PEDK_2_7625_page_footer_enable( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_FOOTER_ENABLE, &copy_job_request->page_footer.enable, sizeof( copy_job_request->page_footer.enable ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_POSITION ) == 0 )
    {
        convert_param_PEDK_2_7625_page_footer_position( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_FOOTER_POSITION, &copy_job_request->page_footer.position, sizeof( copy_job_request->page_footer.position ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_PAGINATION ) == 0 )
    {
        convert_param_PEDK_2_7625_page_footer_pagination( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_FOOTER_PAGINATION, &copy_job_request->page_footer.pagination, sizeof( copy_job_request->page_footer.pagination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_TEXT_TYPE ) == 0 )
    {
        convert_param_PEDK_2_7625_page_footer_text_type( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT_TYPE, &copy_job_request->page_footer.text_type, sizeof( copy_job_request->page_footer.text_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_TEXT ) == 0 )
    {
        convert_param_PEDK_2_7625_page_footer_text( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT, copy_job_request->page_footer.text, sizeof( copy_job_request->page_footer.text ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_BACKUP_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_backup_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_BACKUP_MODE, &copy_job_request->backup_mode, sizeof( copy_job_request->backup_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SAMPLE_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_sample_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SAMPLE_MODE, &copy_job_request->sample_mode, sizeof( copy_job_request->sample_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_COLOR_INVERSION ) == 0 )
    {
        convert_param_PEDK_2_7625_use_color_inversion( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_USE_COLOR_INVERSION, &copy_job_request->use_color_inversion, sizeof( copy_job_request->use_color_inversion ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_COPY_SCAN_MEANTIME ) == 0 )
    {
        convert_param_PEDK_2_7625_use_copy_scan_meantime( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_USE_COPY_SCAN_MEANTIME, &copy_job_request->use_copy_scan_meantime, sizeof( copy_job_request->use_copy_scan_meantime ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_REMOVE_COLOR ) == 0 )
    {
        convert_param_PEDK_2_7625_use_remove_color( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_USE_REMOVEL_COLOR, &copy_job_request->use_removel_color, sizeof( copy_job_request->use_removel_color ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_EDGE_CLEAN ) == 0 )
    {
        convert_param_PEDK_2_7625_use_edge_clean( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_USE_EDGE_CLEAN, &copy_job_request->use_edge_clean, sizeof( copy_job_request->use_edge_clean ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_UP ) == 0 )
    {
        convert_param_PEDK_2_7625_filter_edge_margin_top( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_TOP, &copy_job_request->filter_edge_margin_top, sizeof( copy_job_request->filter_edge_margin_top ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_DOWN ) == 0 )
    {
        convert_param_PEDK_2_7625_filter_edge_margin_bottom( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_BOTTOM, &copy_job_request->filter_edge_margin_bottom, sizeof( copy_job_request->filter_edge_margin_bottom ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_LEFT ) == 0 )
    {
        convert_param_PEDK_2_7625_filter_edge_margin_left( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_LEFT, &copy_job_request->filter_edge_margin_left, sizeof( copy_job_request->filter_edge_margin_left ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_RIGHT ) == 0 )
    {
        convert_param_PEDK_2_7625_filter_edge_margin_right( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_RIGHT, &copy_job_request->filter_edge_margin_right, sizeof( copy_job_request->filter_edge_margin_right ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COPIES_FLIP ) == 0 )
    {
        convert_param_PEDK_2_7625_copies_flip( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COPIES_FLIP, &copy_job_request->copies_flip, sizeof( copy_job_request->copies_flip ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_ORIGINAL_FLIP ) == 0 )
    {
        convert_param_PEDK_2_7625_original_flip( copy_param_id , atoi(val_str) );
        printf( "dgb1 PEDK orig flip:%u\n", copy_job_request->original_flip );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_ORIGINAL_FLIP, &copy_job_request->original_flip, sizeof( copy_job_request->original_flip ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER ) == 0 )
    {
        convert_param_PEDK_2_7625_cover( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COVER, &copy_job_request->cover, sizeof( copy_job_request->cover ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER_BACK ) == 0 )
    {
        convert_param_PEDK_2_7625_cover_back( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COVER_BACK, &copy_job_request->cover_back, sizeof( copy_job_request->cover_back ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER_TRAY_IN ) == 0 )
    {
        convert_param_PEDK_2_7625_cover_tray_in( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COVER_TRAY_IN, &copy_job_request->cover_tray_in, sizeof( copy_job_request->cover_tray_in ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_STAPLE_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_staple_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_STAPLE_MODE, &copy_job_request->staple_mode, sizeof( copy_job_request->staple_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PUNCH_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_punch_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PUNCH_MODE, &copy_job_request->punch_mode, sizeof( copy_job_request->punch_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FOLD_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_fold_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_FOLD_MODE, &copy_job_request->fold_mode, sizeof( copy_job_request->fold_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_TRAY_RECEIVE ) == 0 )
    {
        convert_param_PEDK_2_7625_tray_receive( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_TRAY_RECEIVE, &copy_job_request->tray_receive, sizeof( copy_job_request->tray_receive ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SHIFT_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_shift_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SHIFT_MODE, &copy_job_request->shift_mode, sizeof( copy_job_request->shift_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FLOD_NUMBER ) == 0 )
    {
        convert_param_PEDK_2_7625_flod_number( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_FOLD_NUMBER, &copy_job_request->fold_number, sizeof( copy_job_request->fold_number ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_HAVE_STAPLER ) == 0 )
    {
        convert_param_PEDK_2_7625_have_stapler( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_HAVE_STAPLER, &copy_job_request->have_stapler, sizeof( copy_job_request->have_stapler ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER_PAPER_TYPE ) == 0 )
    {
        convert_param_PEDK_2_7625_cover_paper_type( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COVER_PAPER_TYPE, &copy_job_request->cover_paper_type, sizeof( copy_job_request->cover_paper_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SEPARATOR_TRAY_IN ) == 0 )
    {
        convert_param_PEDK_2_7625_separator_tray_in( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SEPARATOR_TRAY_IN, &copy_job_request->separator_tray_in, sizeof( copy_job_request->separator_tray_in ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_SCAN_PAPER_PARAM_W ) == 0 )
    {
        convert_param_PEDK_2_7625_user_define_scan_paper_param_w( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SCAN_WIDTH, &copy_job_request->scan_width, sizeof( copy_job_request->scan_width ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_SCAN_PAPER_PARAM_H ) == 0 )
    {
        convert_param_PEDK_2_7625_user_define_scan_paper_param_h( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SCAN_HEIGHT, &copy_job_request->scan_height, sizeof( copy_job_request->scan_height ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_PRINT_PAPER_PARAM_W ) == 0 )
    {
        convert_param_PEDK_2_7625_user_define_print_paper_param_w( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PRINT_WIDTH, &copy_job_request->print_width, sizeof( copy_job_request->print_width ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_PRINT_PAPER_PARAM_H ) == 0 )
    {
        convert_param_PEDK_2_7625_user_define_print_paper_param_h( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PRINT_HEIGHT, &copy_job_request->print_height, sizeof( copy_job_request->print_height ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_TYPE ) == 0 )
    {
        convert_param_PEDK_2_7625_page_type( copy_param_id , val_str );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_PAGE_TYPE, &copy_job_request->page_type, sizeof( copy_job_request->page_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SKEWING_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_skewing_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SKEWING_MODE, &copy_job_request->skewing_mode, sizeof( copy_job_request->skewing_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_TONER_MODE ) == 0 )
    {
        convert_param_PEDK_2_7625_toner_mode( copy_param_id , atoi(val_str) );
        ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_SAVE_TONER_MODE, &copy_job_request->save_toner_mode, sizeof( copy_job_request->save_toner_mode ),copy_param_id );

    }
    else
    {
        copy_Log( "PEDK param set err  ===> [%s]  \n", param_str );
    }

    JS_FreeCString(ctx, param_str);
    JS_FreeCString(ctx, val_str);

    return JS_NewInt32( ctx, ret );

}


static JSValue copy_jsc_removeParameter( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int ret = 0;
    char* param_str = 0;
    int copy_param_id = 0;
//    int val = 0;

    param_str = JS_ToCString( ctx, argv[0] );
    JS_ToInt32( ctx, &copy_param_id, argv[1] );
    copy_Log( "remove param id=[%d] ==> [%s] \n", copy_param_id, param_str );

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = get_pedk_copy_job_request(copy_param_id );
    if(pedk_copy_job_request == NULL)
    {
        ret = -1;
        return JS_NewInt32( ctx, ret );
    }
    COPY_JOB_REQUEST_DATA_S* copy_job_request = &pedk_copy_job_request->copy_job_req;

    if( strcmp( param_str, COPY_PARAM_INPUTPAPERSIZE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SCAN_SIZE, &copy_job_request->scan_size, sizeof( copy_job_request->scan_size ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_OUTPUTTRAY_TRAY ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PRINT_TRAY, &copy_job_request->print_tray, sizeof( copy_job_request->print_tray ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_OUTPUTTRAY_SIZE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PRINT_SIZE, &copy_job_request->print_size, sizeof( copy_job_request->print_size ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_ZOOM ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_AUTO_SCALE_MODE, &copy_job_request->auto_scale_mode, sizeof( copy_job_request->auto_scale_mode ),copy_param_id );
        copy_Log( "set scale percent\n" );
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SCALE_PERCENT, &copy_job_request->scale_percent, sizeof( copy_job_request->scale_percent ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_BRIGHTNESS ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_IMAGE_BRIGHTNESS, &copy_job_request->image_brightness, sizeof( copy_job_request->image_brightness ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_CONTRAST ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_IMAGE_CONTRAST, &copy_job_request->image_contrast, sizeof( copy_job_request->image_contrast ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_HUE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_IMAGE_HUE, &copy_job_request->image_hue, sizeof( copy_job_request->image_hue ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_SATURATION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_IMAGE_SATURATION, &copy_job_request->image_saturation, sizeof( copy_job_request->image_saturation ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_SHARPNESS ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_IMAGE_SHARPNESS, &copy_job_request->image_sharpness, sizeof( copy_job_request->image_sharpness ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_IMAGE_COLOR_BACKGROUNDMOVE_LEVEL ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_BACKGROUNDMOVE_LEVEL, &copy_job_request->backgroundmove_level, sizeof( copy_job_request->backgroundmove_level ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_COPYMODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PRINT_MODE, &copy_job_request->print_mode, sizeof( copy_job_request->print_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_COLLATEMODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COLLATE, &copy_job_request->collate, sizeof( copy_job_request->collate ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_QUALITYTYPE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_QUALITY, &copy_job_request->quality, sizeof( copy_job_request->quality ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_WATERMARK_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_WATERMARK_MODE, &copy_job_request->watermark_mode, sizeof( copy_job_request->watermark_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_WATERMARK_STR ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_WATERMARK_STRING, copy_job_request->watermark_string, sizeof( copy_job_request->watermark_string ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_COPIES ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COPIES, &copy_job_request->copies, sizeof( copy_job_request->copies ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_AUTOCORRECTIONMODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_AUTO_ID_CORRECTION, &copy_job_request->auto_id_correction, sizeof( copy_job_request->auto_id_correction ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_ARRANGEMENTMODE ) == 0 )
    {
        //ret = set_single_param_default_value(SINGLE_COPY_PARAM_NUP_COMBINATION, &copy_job_request->nup_combination, sizeof(copy_job_request->nup_combination));
    }
    else if( strcmp( param_str, COPY_PARAM_NUPMODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_NUP_TYPE, &copy_job_request->nup_type, sizeof( copy_job_request->nup_type ),copy_param_id );
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_NUP_COMBINATION, &copy_job_request->nup_combination, sizeof(copy_job_request->nup_combination),copy_param_id);
    }
    else if( strcmp( param_str, COPY_PARAM_CLONEMODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_CLONE_MODE, &copy_job_request->clone_mode, sizeof( copy_job_request->clone_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PARAM_POSTER ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_POSTER_SIZE, &copy_job_request->poster_size, sizeof( copy_job_request->poster_size ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COLOR_MODE, &copy_job_request->color_mode, sizeof( copy_job_request->color_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SCAN_SOURCE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SCAN_SOURCE, &copy_job_request->scan_source, sizeof( copy_job_request->scan_source ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_IMAGE_ORIENTATION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_IMAGE_ORIENTATION, &copy_job_request->image_orientation, sizeof( copy_job_request->image_orientation ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_C ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COLOR_BALANCE_C, &copy_job_request->color_balance_c, sizeof( copy_job_request->color_balance_c ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_M ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COLOR_BALANCE_M, &copy_job_request->color_balance_m, sizeof( copy_job_request->color_balance_m ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_Y ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COLOR_BALANCE_Y, &copy_job_request->color_balance_y, sizeof( copy_job_request->color_balance_y ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COLOR_BALANCE_K ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COLOR_BALANCE_K, &copy_job_request->color_balance_k, sizeof( copy_job_request->color_balance_k ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_HORIZONTAL_MARGIN ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_HORIZONTAL_MARGIN, &copy_job_request->horizontal_margin, sizeof( copy_job_request->horizontal_margin ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_VERTICAL_MARGIN ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_VERTICAL_MARGIN, &copy_job_request->vertical_margin, sizeof( copy_job_request->vertical_margin ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_EDGE_TO_EDGE_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_EDGE_TO_EDGE_MODE, &copy_job_request->edge_to_edge_mode, sizeof( copy_job_request->edge_to_edge_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SEPARATOR ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SEPARATOR, &copy_job_request->separator, sizeof( copy_job_request->separator ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_NUP_COMBINATION ) == 0 )
    {
        //ret = set_single_param_default_value( SINGLE_COPY_PARAM_NUP_COMBINATION, &copy_job_request->nup_combination, sizeof( copy_job_request->nup_combination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_BOOKLET_DUPLEX ) == 0 )
    {
        //ret = set_single_param_default_value( SINGLE_COPY_PARAM_BOOKLET_DUPLEX, &copy_job_request->nup_combination, sizeof( copy_job_request->nup_combination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_MIRROR_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_MIRROR_MODE, &copy_job_request->mirror_mode, sizeof( copy_job_request->mirror_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_ENABLE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_HEADER_ENABLE, &copy_job_request->page_footer.enable, sizeof( copy_job_request->page_footer.enable ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_POSITION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_HEADER_POSITION, &copy_job_request->page_footer.position, sizeof( copy_job_request->page_footer.position ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_PAGINATION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_HEADER_PAGINATION, &copy_job_request->page_footer.pagination, sizeof( copy_job_request->page_footer.pagination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_TEXT_TYPE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_HEADER_TEXT_TYPE, &copy_job_request->page_footer.text_type, sizeof( copy_job_request->page_footer.text_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_HEADER_TEXT ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_HEADER_TEXT, copy_job_request->page_footer.text, sizeof( copy_job_request->page_footer.text ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_ENABLE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_FOOTER_ENABLE, &copy_job_request->page_footer.enable, sizeof( copy_job_request->page_footer.enable ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_POSITION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_FOOTER_POSITION, &copy_job_request->page_footer.position, sizeof( copy_job_request->page_footer.position ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_PAGINATION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_FOOTER_PAGINATION, &copy_job_request->page_footer.pagination, sizeof( copy_job_request->page_footer.pagination ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_TEXT_TYPE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT_TYPE, &copy_job_request->page_footer.text_type, sizeof( copy_job_request->page_footer.text_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_FOOTER_TEXT ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_FOOTER_TEXT, copy_job_request->page_footer.text, sizeof( copy_job_request->page_footer.text ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_BACKUP_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_BACKUP_MODE, &copy_job_request->backup_mode, sizeof( copy_job_request->backup_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SAMPLE_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SAMPLE_MODE, &copy_job_request->sample_mode, sizeof( copy_job_request->sample_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_COLOR_INVERSION ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_USE_COLOR_INVERSION, &copy_job_request->use_color_inversion, sizeof( copy_job_request->use_color_inversion ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_COPY_SCAN_MEANTIME ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_USE_COPY_SCAN_MEANTIME, &copy_job_request->use_copy_scan_meantime, sizeof( copy_job_request->use_copy_scan_meantime ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_REMOVE_COLOR ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_USE_REMOVEL_COLOR, &copy_job_request->use_removel_color, sizeof( copy_job_request->use_removel_color ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USE_EDGE_CLEAN ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_USE_EDGE_CLEAN, &copy_job_request->use_edge_clean, sizeof( copy_job_request->use_edge_clean ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_UP ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_TOP, &copy_job_request->filter_edge_margin_top, sizeof( copy_job_request->filter_edge_margin_top ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_DOWN ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_BOTTOM, &copy_job_request->filter_edge_margin_bottom, sizeof( copy_job_request->filter_edge_margin_bottom ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_LEFT ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_LEFT, &copy_job_request->filter_edge_margin_left, sizeof( copy_job_request->filter_edge_margin_left ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FILTER_EDGE_MARGIN_RIGHT ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_FILTER_EDGE_MARGIN_RIGHT, &copy_job_request->filter_edge_margin_right, sizeof( copy_job_request->filter_edge_margin_right ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COPIES_FLIP ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COPIES_FLIP, &copy_job_request->copies_flip, sizeof( copy_job_request->copies_flip ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_ORIGINAL_FLIP ) == 0 )
    {
        printf( "dgb1 PEDK orig flip:%u\n", copy_job_request->original_flip );
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_ORIGINAL_FLIP, &copy_job_request->original_flip, sizeof( copy_job_request->original_flip ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COVER, &copy_job_request->cover, sizeof( copy_job_request->cover ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER_BACK ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COVER_BACK, &copy_job_request->cover_back, sizeof( copy_job_request->cover_back ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER_TRAY_IN ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COVER_TRAY_IN, &copy_job_request->cover_tray_in, sizeof( copy_job_request->cover_tray_in ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_STAPLE_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_STAPLE_MODE, &copy_job_request->staple_mode, sizeof( copy_job_request->staple_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PUNCH_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PUNCH_MODE, &copy_job_request->punch_mode, sizeof( copy_job_request->punch_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FOLD_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_FOLD_MODE, &copy_job_request->fold_mode, sizeof( copy_job_request->fold_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_TRAY_RECEIVE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_TRAY_RECEIVE, &copy_job_request->tray_receive, sizeof( copy_job_request->tray_receive ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SHIFT_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SHIFT_MODE, &copy_job_request->shift_mode, sizeof( copy_job_request->shift_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_FLOD_NUMBER ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_FOLD_NUMBER, &copy_job_request->fold_number, sizeof( copy_job_request->fold_number ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_HAVE_STAPLER ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_HAVE_STAPLER, &copy_job_request->have_stapler, sizeof( copy_job_request->have_stapler ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_COVER_PAPER_TYPE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_COVER_PAPER_TYPE, &copy_job_request->cover_paper_type, sizeof( copy_job_request->cover_paper_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SEPARATOR_TRAY_IN ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SEPARATOR_TRAY_IN, &copy_job_request->separator_tray_in, sizeof( copy_job_request->separator_tray_in ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_SCAN_PAPER_PARAM_W ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SCAN_WIDTH, &copy_job_request->scan_width, sizeof( copy_job_request->scan_width ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_SCAN_PAPER_PARAM_H ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SCAN_HEIGHT, &copy_job_request->scan_height, sizeof( copy_job_request->scan_height ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_PRINT_PAPER_PARAM_W ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PRINT_WIDTH, &copy_job_request->print_width, sizeof( copy_job_request->print_width ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_USER_DEFINE_PRINT_PAPER_PARAM_H ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PRINT_HEIGHT, &copy_job_request->print_height, sizeof( copy_job_request->print_height ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_PAGE_TYPE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_PAGE_TYPE, &copy_job_request->page_type, sizeof( copy_job_request->page_type ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_SKEWING_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SKEWING_MODE, &copy_job_request->skewing_mode, sizeof( copy_job_request->skewing_mode ),copy_param_id );
    }
    else if( strcmp( param_str, COPY_TONER_MODE ) == 0 )
    {
        ret = set_single_param_default_value( SINGLE_COPY_PARAM_SAVE_TONER_MODE, &copy_job_request->save_toner_mode, sizeof( copy_job_request->save_toner_mode ),copy_param_id );
    }
    else
    {
        copy_Log( "PEDK param remove err  ===> [%s]  \n", param_str );
    }

    JS_FreeCString(ctx, param_str);

    return JS_NewInt32( ctx, ret );

}



//-----
//argv ==>0:switch  1:url 2:headers 3:format_type 4:level 5:protocol 6:file_name_prefix
static JSValue copy_jsc_set_retention_param( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    char *tmp_str=NULL;
    int param_id = 0;
    int ret   = 0;
    int wonum = 0;
    COPY_RETENTION_PARAM_S copy_retention;

    memset(&copy_retention,0,sizeof(COPY_RETENTION_PARAM_S));

    JS_ToInt32( ctx, &copy_retention.mode, argv[0] );


    tmp_str = JS_ToCString( ctx, argv[1] );
    strcpy(copy_retention.url,tmp_str);
    JS_FreeCString(ctx, tmp_str);

    tmp_str = JS_ToCString( ctx, argv[2] );
    strcpy(copy_retention.headers,tmp_str);
    JS_FreeCString(ctx, tmp_str);

    JS_ToInt32( ctx, &copy_retention.format_type, argv[3] );

    JS_ToInt32( ctx, &copy_retention.level, argv[4] );

    tmp_str = JS_ToCString( ctx, argv[5] );
    strcpy(copy_retention.protocol,tmp_str);
    JS_FreeCString(ctx, tmp_str);

    tmp_str = JS_ToCString( ctx, argv[6] );
    strcpy(copy_retention.file_name_prefix,tmp_str);
    JS_FreeCString(ctx, tmp_str);

    copy_Log( " mode=%d type=%d url=[%s] headers=[%s] file_name_prefix=[%s] protocol=[%s] level=%d \n ",\
                copy_retention.mode, copy_retention.format_type ,copy_retention.url,copy_retention.headers,copy_retention.file_name_prefix,copy_retention.protocol,copy_retention.level);


#ifndef X86_DEBUG
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_SET_COPY_RETENTION_PARAM, 0,sizeof(COPY_RETENTION_PARAM_S), &copy_retention );
#endif


    return JS_NewInt32( ctx, ret );
}



static JSValue copy_jsc_copyStart( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    char *copy_type=NULL;
    int param_id = 0;
    int ret   = 0;
    int wonum = 0;

    JS_ToInt32( ctx, &param_id, argv[0] );
    copy_type = JS_ToCString( ctx, argv[1] );
    JS_ToInt32( ctx, &wonum, argv[2] );
    copy_Log( " copy copy_type=%s param_id = %d wonum=%d\n ",copy_type, param_id ,wonum);

#if 0
    ret = am_get_whitelist_enable("Copy" , ctx);
    copy_Log("get jobctl whitelist Copy==> ret:%d\n\n\n\n" , ret);
    if (ret == 0)
    {
        return JS_NewInt32( ctx, 3 );
    }
#endif

    convert_param_PEDK_2_7625_copy_type(param_id,copy_type);
    JS_FreeCString(ctx, copy_type);

    int copy_job_request_size = 0;
    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_request = NULL;
    COPY_JOB_REQUEST_DATA_S* copy_job_request = NULL;
    pedk_copy_job_request = get_pedk_copy_job_request(param_id);
    if( pedk_copy_job_request == NULL )
    {
        copy_Log("can not find pedk copy job request\n");
        ret = 1;
        return JS_NewInt32( ctx, ret );
    }
    copy_job_request = &pedk_copy_job_request->copy_job_req;

    copy_Log( "========================== 7265 copy parameter ==========================\n" );
    printf_all_copy_parameter( copy_job_request );
    ret = PEDK_firmware_adaptor_set_copy_param( SINGLE_COPY_PARAM_COPY_TYPE, &copy_job_request->copy_type, sizeof( copy_job_request->copy_type ), param_id );
    if(ret)
    {
        copy_Log( " param err \n ");
        return JS_NewInt32( ctx, ret );
    }

    copy_job_request_size = sizeof( COPY_JOB_REQUEST_DATA_S );

    copy_Log( "copy_job_request_size:%d\n", copy_job_request_size );
    copy_Log( "send copy req to mfp ====\n" );

    copy_job_request->wonum = wonum;

#ifndef X86_DEBUG
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_JOB_START, 0,copy_job_request_size, copy_job_request );
#endif

    remove_pedk_copy_job_request( param_id );
    free( pedk_copy_job_request );
    pedk_copy_job_request = NULL;
    copy_job_request = NULL;

    return JS_NewInt32( ctx, ret );
}

static JSValue copy_jsc_copyCancel( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int value = 0;
    int ret = 0;

#if 0
    ret = am_get_whitelist_enable("Copy" , ctx);
    copy_Log("get jobctl whitelist Copy==> ret:%d\n\n\n\n" , ret);
    if (ret == 0)
    {
        return JS_NewInt32( ctx, 3 );
    }
#endif

    //send copy cancel to mfp
    copy_Log( "send copy cancel to mfp \n " );
#ifndef X86_DEBUG
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_JOB_CANCEL, 0, 0, 0 );
#endif


    return JS_NewInt32( ctx, value );
}

static JSValue copy_jsc_copy_next_page( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int value = 0;

    //send copy next_page to mfp
    copy_Log( "send copy next_page to mfp \n " );

#ifndef X86_DEBUG
    int ret = 0;
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_JOB_NEXT_PAGE, 0, 0, 0 );
#endif


    return JS_NewInt32( ctx, value );
}

static JSValue copy_jsc_copy_job_done( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int value = 0;

    //send copy job done to mfp
    copy_Log( "send copy  job done to mfp \n " );
#ifndef X86_DEBUG
    int ret = 0;
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_JOB_DONE, 0, 0, 0 );
#endif


    return JS_NewInt32( ctx, value );
}

static JSValue copy_jsc_get_ADF_copy_page_num( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    uint32_t value = 0;
    int len = sizeof(value);

#ifndef X86_DEBUG
    int ret = 0;
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_ADF_COPY_PAGE_NUM, 1, 0, 0 );
    ret = RecvMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_ADF_COPY_PAGE_NUM, 0, &value, &len, 2 );
#endif

    copy_Log( "MSG_COPY_SUB_ADF_COPY_PAGE_NUM =%u  \n ", value );

    return JS_NewInt32( ctx, value );
}


static JSValue copy_jsc_get_FB_copy_page_num( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    uint32_t value = 0;
    int len = sizeof(value);

#ifndef X86_DEBUG
    int ret = 0;
    ret = SendMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_FB_COPY_PAGE_NUM, 1, 0, 0 );
    ret = RecvMsgToMfp( MSG_MODULE_COPY, MSG_COPY_SUB_FB_COPY_PAGE_NUM, 0, &value, &len, 2 );
#endif

    copy_Log( "MSG_COPY_SUB_FB_COPY_PAGE_NUM =%u  \n ", value );

    return JS_NewInt32( ctx, value );
}


static JSValue copy_jsc_copy_parameter_set_init( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int ret = 0;
    int param_set_id = 0;

    JS_ToInt32( ctx, &param_set_id, argv[0] );
    copy_Log( "new param_set_id = %d \n ", param_set_id );

    PEDK_COPY_JOB_REQUEST_DATA_S* pedk_copy_job_req = NULL;
    pedk_copy_job_req = ( PEDK_COPY_JOB_REQUEST_DATA_S* )malloc( sizeof(PEDK_COPY_JOB_REQUEST_DATA_S) );
    if(pedk_copy_job_req == NULL )
    {
        ret = -1;
        copy_Log("malloc copy_job_request NULL");
        return JS_NewInt32( ctx, ret );
    }
    memset( pedk_copy_job_req, 0, sizeof(PEDK_COPY_JOB_REQUEST_DATA_S) );

    pedk_copy_job_req->id = param_set_id;
    pedk_copy_job_req->next = NULL;

    add_pedk_copy_job_request( pedk_copy_job_req );

    copy_param_init( param_set_id );

    copy_Log( "new param_set_id = %d \n ", param_set_id );

    return JS_NewInt32( ctx, ret );
}


static JSValue copy_jsc_test1( JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv )
{
    int param_1 = 0;
    int param_2 = 0;
    int param_3 = 0;
    int param_4 = 0;


    char* buf ;
    copy_Log( " argc = %d \n ", argc );

    JS_ToInt32( ctx, &param_1, argv[0] );
    JS_ToInt32( ctx, &param_2, argv[1] );
    JS_ToInt32( ctx, &param_3, argv[2] );
    JS_ToInt32( ctx, &param_4, argv[3] );


    copy_Log( " param_1 = %d \n ", param_1 );
    copy_Log( " param_2 = %d \n ", param_2 );
    copy_Log( " param_3 = %d \n ", param_3 );
    copy_Log( " param_4 = %d \n ", param_4 );
    copy_Log( " ================= " );

    return JS_NewInt32( ctx, param_4 );
}


typedef struct JSCFunctionList
{
    const char* name;
    int length;
    JSCFunction* func;
} JSCFunctionList;

static const JSCFunctionList g_copy_jsc_api_funs[] =
{

    //set copy parameter....
    {"copy_jsc_addParameter", 3, copy_jsc_addParameter },
    {"copy_jsc_removeParameter", 2, copy_jsc_removeParameter },
    {"copy_jsc_copy_parameter_set_init", 1, copy_jsc_copy_parameter_set_init},
    {"copy_jsc_set_retention_param", 7, copy_jsc_set_retention_param },



    //copy job control...
    {"copy_jsc_copyStart", 3, copy_jsc_copyStart },
    {"copy_jsc_copyCancel", 0, copy_jsc_copyCancel },
    {"copy_jsc_copy_next_page", 0, copy_jsc_copy_next_page },
    {"copy_jsc_copy_job_done", 0, copy_jsc_copy_job_done },
    {"getADFScanCopyedPage", 0, copy_jsc_get_ADF_copy_page_num },
    {"getFBScanCopyedPage", 0, copy_jsc_get_FB_copy_page_num },


    //for test....
    {"copy_jsc_test1", 4, copy_jsc_test1},

};

typedef struct
{
    char* copy_param_name;
    int ( *convert_param_7625_2_PEDK_func )();
} CONVERT_7265_2_PEDK_TABLE;

#if 0

CONVERT_7265_2_PEDK_TABLE convert_7265_2_PEDK_table[] =
{
    { COPY_PARAM_INPUTPAPERSIZE, convert_param_7625_2_PEDK_inputpapersize },
    { COPY_PARAM_OUTPUTTRAY, convert_param_7625_2_PEDK_outputtray },
    { COPY_PARAM_ZOOM, convert_param_7625_2_PEDK_zoom },
    { COPY_PARAM_IMAGE_COLOR, convert_param_7625_2_PEDK_image_color },
    { COPY_PARAM_COPYMODE, convert_param_7625_2_PEDK_copymode },
    { COPY_PARAM_COLLATEMODE, convert_param_7625_2_PEDK_collatemode },
    { COPY_PARAM_QUALITYTYPE, convert_param_7625_2_PEDK_qualitytype },
    { COPY_PARAM_WATERMARK, convert_param_7625_2_PEDK_watermark },
    { COPY_PARAM_COPIES, convert_param_7625_2_PEDK_copies },
    { COPY_PARAM_AUTOCORRECTIONMODE, convert_param_7625_2_PEDK_autocorrectionmode },
    { COPY_PARAM_ARRANGEMENTMODE, convert_param_7625_2_PEDK_arrangementmode },
    { COPY_PARAM_NUPMODE, convert_param_7625_2_PEDK_nupmode },
    { COPY_PARAM_CLONEMODE, convert_param_7625_2_PEDK_clonemode },
    { COPY_PARAM_POSTER, convert_param_7625_2_PEDK_poster },
    { COPY_COLOR_MODE, convert_param_7625_2_PEDK_color_mode },
    { COPY_SCAN_SOURCE, convert_param_7625_2_PEDK_scan_source },
    { COPY_IMAGE_ORIENTATION, convert_param_7625_2_PEDK_image_orientation },
    { COPY_COLOR_BALANCE, convert_param_7625_2_PEDK_color_balance },
    { COPY_HORIZONTAL_MARGIN, convert_param_7625_2_PEDK_horizontal_margin },
    { COPY_VERTICAL_MARGIN, convert_param_7625_2_PEDK_vertical_margin },
    { COPY_EDGE_TO_EDGE_MODE, convert_param_7625_2_PEDK_edge_to_edge_mode },
    { COPY_SEPARATOR, convert_param_7625_2_PEDK_separator },
    { COPY_NUP_COMBINATION, convert_param_7625_2_PEDK_nup_combination },
    { COPY_BOOKLET_DUPLEX, convert_param_7625_2_PEDK_booklet_duplex },
    { COPY_MIRROR_MODE, convert_param_7625_2_PEDK_mirror_mode },
    { COPY_PAGE_HEADER_ENABLE, convert_param_7625_2_PEDK_page_header_enable },
    { COPY_PAGE_HEADER_POSITION, convert_param_7625_2_PEDK_page_header_position },
    { COPY_PAGE_HEADER_PAGINATION, convert_param_7625_2_PEDK_page_header_pagination },
    { COPY_PAGE_HEADER_TEXT_TYPE, convert_param_7625_2_PEDK_page_header_text_type },
    { COPY_PAGE_HEADER_TEXT, convert_param_7625_2_PEDK_page_header_text },
    { COPY_PAGE_FOOTER_ENABLE, convert_param_7625_2_PEDK_page_footer_enable },
    { COPY_PAGE_FOOTER_POSITION, convert_param_7625_2_PEDK_page_footer_position },
    { COPY_PAGE_FOOTER_PAGINATION, convert_param_7625_2_PEDK_page_footer_pagination },
    { COPY_PAGE_FOOTER_TEXT_TYPE, convert_param_7625_2_PEDK_page_footer_text_type },
    { COPY_PAGE_FOOTER_TEXT, convert_param_7625_2_PEDK_page_footer_text },
    { COPY_BACKUP_MODE, convert_param_7625_2_PEDK_backup_mode },
    { COPY_SAMPLE_MODE, convert_param_7625_2_PEDK_sample_mode },
    { COPY_USE_COLOR_INVERSION, convert_param_7625_2_PEDK_use_color_inversion },
    { COPY_USE_COPY_SCAN_MEANTIME, convert_param_7625_2_PEDK_use_copy_scan_meantime },
    { COPY_USE_REMOVE_COLOR, convert_param_7625_2_PEDK_use_remove_color },
    { COPY_USE_EDGE_CLEAN, convert_param_7625_2_PEDK_use_edge_clean },
    { COPY_FILTER_EDGE_MARGIN, convert_param_7625_2_PEDK_filter_edge_margin },
    { COPY_COPIES_FLIP, convert_param_7625_2_PEDK_copies_flip },
    { COPY_ORIGINAL_FLIP, convert_param_7625_2_PEDK_original_flip },
    { COPY_COVER, convert_param_7625_2_PEDK_cover },
    { COPY_COVER_BACK, convert_param_7625_2_PEDK_cover_back },
    { COPY_COVER_TRAY_IN, convert_param_7625_2_PEDK_cover_tray_in },
    { COPY_STAPLE_MODE, convert_param_7625_2_PEDK_staple_mode },
    { COPY_PUNCH_MODE, convert_param_7625_2_PEDK_punch_mode },
    { COPY_FOLD_MODE, convert_param_7625_2_PEDK_fold_mode },
    { COPY_TRAY_RECEIVE, convert_param_7625_2_PEDK_tray_receive },
    { COPY_SHIFT_MODE, convert_param_7625_2_PEDK_shift_mode },
    { COPY_FLOD_NUMBER, convert_param_7625_2_PEDK_flod_number },
    { COPY_HAVE_STAPLER, convert_param_7625_2_PEDK_have_stapler },
    { COPY_COVER_PAPER_TYPE, convert_param_7625_2_PEDK_cover_paper_type },
    { COPY_SEPARATOR_TRAY_IN, convert_param_7625_2_PEDK_separator_tray_in },
    { COPY_USER_DEFINE_SCAN_PAPER_PARAM, convert_param_7625_2_PEDK_user_define_scan_paper_param },
    { COPY_USER_DEFINE_PRINT_PAPER_PARAM, convert_param_7625_2_PEDK_user_define_print_paper_param },
    { COPY_PAGE_TYPE, convert_param_7625_2_PEDK_page_type },
    { COPY_SKEWING_MODE, convert_param_7625_2_PEDK_skewing_mode },
    { COPY_TONER_MODE, convert_param_7625_2_PEDK_toner_mode },

};

#endif

JSModuleDef* js_copy_init( JSContext* ctx,  JSValueConst this_obj )
{
    JSModuleDef* jsmd = NULL;

    copy_Log( " js_copy_init \n" );

    for( int i = 0; i < countof( g_copy_jsc_api_funs ); i++ )
    {
        JS_SetPropertyStr( ctx, this_obj, g_copy_jsc_api_funs[i].name, JS_NewCFunction( ctx, g_copy_jsc_api_funs[i].func, g_copy_jsc_api_funs[i].name, g_copy_jsc_api_funs[i].length ) );
    }

    return jsmd;
}



