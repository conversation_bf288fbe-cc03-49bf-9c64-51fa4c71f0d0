/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_general.h
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-25
 * @brief qio_general public header file
 */
#ifndef __QIO_GENERAL_H__
#define __QIO_GENERAL_H__

#include "qio/qio.h"

#define GQIO_READABLE(q, t, u)  ( ((q) && (q)->poll)   ? (q)->poll(q, QIO_POLL_READ, t, u)  : QIOERR )
#define GQIO_WRITEABLE(q, t, u) ( ((q) && (q)->poll)   ? (q)->poll(q, QIO_POLL_WRITE, t, u) : QIOERR )
#define GQIO_READ(q, b, n)      ( ((q) && (q)->read)   ? (q)->read(q, b, n)                 : QIOERR )
#define GQIO_WRITE(q, b, n)     ( ((q) && (q)->write)  ? (q)->write(q, b, n)                : QIOERR )
#define GQIO_REWIND(q, b, n)    ( ((q) && (q)->rewind) ? (q)->rewind(q, b, n)               : QIOERR )
#define GQIO_SEEK(q, o, w)      ( ((q) && (q)->seek)   ? (q)->seek(q, o, w)                 : QIOERR )
#define GQIO_CLOSE(q)           ( ((q) && (q)->close)  ? (q)->close(q)                      : QIOERR )

typedef enum
{
    IO_CLASS_NONE = 0,
    IO_CLASS_PRINT,     ///< print class
    IO_CLASS_COPY,      ///< copy class
    IO_CLASS_SCAN,      ///< scan class
    IO_CLASS_FAX,       ///< fax class
    IO_CLASS_TEST,      ///< test class
    IO_OTHER_TEST,      ///< other class
    IO_CLASS_MAX
}
IO_CLASS_E;

typedef enum
{
    IO_VIA_UNKNOWN = 0,
    IO_VIA_USB,         ///< from usb
    IO_VIA_NET,         ///< from net
    IO_VIA_IPP,         ///< from AirPrint & Mopria
    IO_VIA_GCLOUD,      ///< from gcloud
    IO_VIA_WIFI,        ///< from wifi
    IO_VIA_FILE,        ///< from file
    IO_VIA_INTERNAL,    ///< from internal page
    IO_VIA_FAX,         ///< from fax
    IO_VIA_AIRSCN,      ///< from AirScan
    IO_VIA_UDISK,       ///< from UDisk
    IO_VIA_ACR,
    IO_VIA_PKI,
    IO_VIA_INTERNAL_PDF,
    IO_VIA_MAX
}
IO_VIA_E;

typedef struct gqio_rewind_obj
{
    uint8_t*    buffer;
    size_t      offset;
    size_t      size;
}
QREWIND_S;

typedef struct gqio_obj
{
    PI_MUTEX_T  rewind_lock;
    QREWIND_S   rewind_obj;
    QIO_S*      pqio;
    IO_CLASS_E  io_class;
    IO_VIA_E    io_via;

    int32_t     (*close)    (struct gqio_obj *);
    int32_t     (*poll)     (struct gqio_obj *, int32_t, int32_t, int32_t);
    int32_t     (*read)     (struct gqio_obj *, void *,  size_t);
    int32_t     (*write)    (struct gqio_obj *, void *,  size_t);
    int32_t     (*rewind)   (struct gqio_obj *, void *,  size_t);
    int32_t     (*seek)     (struct gqio_obj *, ssize_t, int32_t);

    int32_t     job_type;
    int32_t     pdl_type;
    int32_t     status;
    uint8_t     job_name[68];
    uint8_t     password[16];
}
GQIO_S;

/**
 * @brief       Wrap the GQIO object base a QIO.
 * @param[in]   pqio    : the QIO object.
 * @param[in]   io_class: IO class.
 * @param[in]   io_via  : IO VIA.
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
GQIO_S* gqio_create(QIO_S* pqio, IO_CLASS_E io_class, IO_VIA_E io_via);

/// how long to wait (millisecs) for bytes inside a single read/poll for
/// async stream reading.  Since print pipe steps read streams in a
/// pipe-loop they shouldn't block the loop for more than a typical DSP
/// or h/w band processing time which is in the low-milliseconds range
#define PDL_READ_DWELL  10

/// Since IO is done in a pipeline loop we don't want to block the pipe
/// waiting for data. This structure holds the needed incremental state
/// to handle long (many minutes) timeouts. This also provides a
/// physical memory buffer so that i/o bytes can be passed to DSP, etc.
/// and a virtual mapping for host cpu access.  Its really just a
/// ring buffer, but with the ability to fill a small physical buffer
/// from the ring.
typedef struct pdl_io_ctx_obj
{
    GQIO_S*     pgqio;      ///< stream used to fill this context (can change)
    uint8_t*    iobuf;      ///< io bytes, ring buffer
    int32_t     iohead;     ///< head index
    int32_t     iotail;     ///< tail index
    int32_t     iocnt;      ///< valid bytes in buf now
    int32_t     iosize;     ///< buffer allocated size
    int32_t     tosecs;     ///< seconds after tostart when request times out
    uint32_t    tostart;    ///< when, in system seconds, the last byte was read, or initialized
}
PDL_IO_CTX_S;

/**
 * @brief       Allocate and initialize a PDL stream reading context to enable long \n
 *              timeouts reading streams in pipe loops.  The toSecs parameter is used \n
 *              to set final timeout time from the time this function is called. \n
 *              Any bytes read from the stream resets the end time to this many seconds \n
 *              after the current time. Setting a stream with PDLreadSetStream() also \n
 *              resets the timeout timer.
 * @param[in]   pgqio   : stream to read from.
 * @param[in]   iosize  : size of ring buffer to allocate.
 * @param[in]   tos     : how many seconds to wait to fill buffer.
 * @return      a context for buffered pdl i/o
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
PDL_IO_CTX_S*   pdl_create_read_context     (GQIO_S* pgqio, int32_t iosize, int32_t tos);

/**
 * @brief       Delete a PDL read context. Both the qio and buffer passed in to the \n
 *              create call pdl_create_read_context are not altered closed or freed.
 * @param[in]   prx     : read context to destroy.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_destroy_read_context    (PDL_IO_CTX_S* prx);

/**
 * @brief       Incrementally read bytes from the stream into a buffer context \n
 *              that was created and initialized by pdl_create_read_context(). \n
 *              Returns the address of the first unread byte in both physical \n
 *              and virtual address spaces as well as the number of bytes available. \n
 *              Note that the PDL read context always garantees that the \n
 *              read data presented is contiguous, since h/w blocks need \n
 *              contiguous physical buffers.  Don't make the buffer too big \n
 *              in pdl_create_read_context() if you do a lot of single-byte stuff \n
 *              since the data might be moved each time. \n
 * @param[in]   prx     : read context to destroy.
 * @param[in]   count   : read context to destroy.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_read_update             (PDL_IO_CTX_S* prx, int32_t* count);

/**
 * @brief       Read bytes from prx into buffer, only up to bytes available in read context \n
 *              non-blocking, no timeout check.
 * @param[in]   prx     : context with io state
 * @param[out]  buffer  : buffer to fill
 * @param[in]   count   : bytes to read (max)
 * @return      bytes read into buffer or error.
 * @retval      >= 0    : bytes read into buffer\n
 *              <  0    : for errors
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_read_bytes              (PDL_IO_CTX_S* prx, uint8_t* buffer, int32_t count);

/**
 * @brief       Mark consumed bytes in the input buffer (advance tail).
 * @param[in]   prx     : context with io state
 * @param[in]   consume : number of input bytes to eat
 * @return      non-0 for errors.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_read_consume            (PDL_IO_CTX_S* prx, int32_t consume);

/**
 * @brief       Set a new stream into a read context.  resets timeouts but leaves current ring alone.
 * @param[in]   prx     : context with io state
 * @param[in]   pgqui   : stream to read
 * @return      non-0 for errors.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_read_set_stream         (PDL_IO_CTX_S* prx, GQIO_S* pgqui);

/**
 * @brief       Set a new stream timeout into a read context.
 * @param[in]   prx     : context with io state
 * @param[in]   timeout : new timeout in seconds
 * @return      non-0 for errors.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_read_set_timeout        (PDL_IO_CTX_S* prx, int32_t timeout);

/**
 * @brief       Get the current stream from a read context.
 * @param[in]   prx     : context with io state
 * @param[out]  ppgqui  : stream that pctx is using
 * @return      non-0 for errors.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_read_get_stream         (PDL_IO_CTX_S* prx, GQIO_S** ppgqui);

/**
 * @brief       Create an I/O buffer set (physical and virtual mapping) for use in a PDLs that need a physical buffer read context (for example)
 * @param[in]   size    : how big a buffer
 * @param[out]  actual_size: how big was made
 * @param[out]  pva_iobuf: the virtual address
 * @return      NULL on failure.
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
uint8_t*        pdl_create_io_buffer        (int32_t size, int32_t* actual_size, uint8_t** pva_iobuf);

/**
 * @brief       free an IO buffer-set created with pdl_create_io_buffer().
 * @param[in]   ppa     : the physical address alloced
 * @param[in]   pva     : the virtual address alloced
 * @param[in]   size    : the actual size from the alloc
 * @return      non-0 for errors.
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t         pdl_destroy_io_buffer       (uint8_t* ppa, uint8_t* pva, int32_t size);

#endif /* __QIO_GENERAL_H__ */
/**
 *@}
 */
