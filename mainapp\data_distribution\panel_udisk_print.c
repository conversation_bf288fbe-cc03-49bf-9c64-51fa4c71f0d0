/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_udisk_print.c
 * @addtogroup panel_dc
 * @{
 * @brief panel dc get udisk files information
 * <AUTHOR>
 * @date 2024-02-24
 */

#include <string.h>
#include <stdio.h>
#include "data_distribution.h"
#include "proxy_work.h"
#include "panel_dc_cmd_tpyedef.h"
#include "utilities/msgrouter.h"
#include "event_manager/event_mgr.h"
#include "pol/pol_log.h"
#include "panel_event.h"
#include "public/msgrouter_main.h"
#include "pol/pol_string.h"
#include "panel_udisk_print.h"
#include "hal_storage.h"

#define UDISK_PRINT_DEBUG       1

typedef enum __UI_UDISK_PRINT_FILE_TYPE_E
{
    UI_UDISK_PRINT_FILE_TYPE_INVALID = 0,
    UI_UDISK_PRINT_FILE_TYPE_FOLDER,
    UI_UDISK_PRINT_FILE_TYPE_TIFF,
    UI_UDISK_PRINT_FILE_TYPE_JPG,
    UI_UDISK_PRINT_FILE_TYPE_PDF,
    UI_UDISK_PRINT_FILE_TYPE_DOC,
    UI_UDISK_PRINT_FILE_TYPE_XLS,
    UI_UDISK_PRINT_FILE_TYPE_PPT,
    UI_UDISK_PRINT_FILE_TYPE_PNG,
} UI_UDISK_PRINT_FILE_TYPE_E;

static int panel_scandir(const char* dir_path, STO_DIR_CTX** file_list,
                            int (*selector)(STO_DIR_CTX*),
                            int (*compar)(const STO_DIR_CTX*, const STO_DIR_CTX*))
{
    if(!dir_path)
    {
        return -1;
    }

    STO_DIR_CTX* temp_file_list = NULL;
    STO_DIR_CTX file_info = { 0 };
    uint32_t temp_file_list_cnt = 0;
    uint32_t cur_cnt = 0;

    STO_DIR_CTX* new = NULL;

    void* handle = NULL;
    int ret = pi_hal_storage_dirrequest(&handle, dir_path);
    if( ret != 0 )
    {
        return -1;
    }

    int selector_ret = 1;
    do
    {
        ret = pi_hal_storage_dirread(handle, &file_info, 1);
        if(ret > 0)
        {
            if(selector)
            {
                selector_ret = selector(&file_info);
            }

            if(selector_ret)
            {
                /* 空间满了，重新分配空间 */
                if(cur_cnt == temp_file_list_cnt)
                {
                    if(0 == temp_file_list_cnt)
                    {
                        temp_file_list_cnt = 10;
                    }
                    else
                    {
                        temp_file_list_cnt *= 2;
                    }
                    new = (STO_DIR_CTX*)pi_realloc(temp_file_list,
                                                        temp_file_list_cnt * sizeof(STO_DIR_CTX));
                    if(!new)
                    {
                        goto ERR_PROC;
                    }

                    temp_file_list = new;
                }

                pi_memcpy(temp_file_list + cur_cnt, &file_info, sizeof(STO_DIR_CTX));
                ++cur_cnt;
            }
        }
    } while(ret);

    pi_hal_storage_dirrelease(handle);
    handle = NULL;

    if(compar)
    {
        qsort(temp_file_list, cur_cnt, sizeof(STO_DIR_CTX), (comparison_fn_t)compar);
    }
    *file_list = temp_file_list;
    return cur_cnt;

ERR_PROC:
    pi_hal_storage_dirrelease(handle);
    handle = NULL;

    if(temp_file_list)
    {
        pi_free(temp_file_list);
        temp_file_list = NULL;
    }

    return -1;
}

static UI_UDISK_PRINT_FILE_TYPE_E panel_get_udisk_file_type(const char* file_name)
{
    uint32_t size = 0;
    uint32_t i = 0;
    if(!file_name)
    {
        pi_log_d("udisk file name failed\n");
        return 0;
    }

    /* 隐藏文件、临时文件不显示 */
    if( ('.' == file_name[0]) || ( 0 == strncmp(file_name, "~$", 2) ) )
    {
        return 0;
    }

    /* 支持的文件后缀 */
    static struct
    {
        const char* file_extension;
        UI_UDISK_PRINT_FILE_TYPE_E type;
    } support_file_extension[] =
    {
        {".pdf", UI_UDISK_PRINT_FILE_TYPE_PDF},  {".jpg", UI_UDISK_PRINT_FILE_TYPE_JPG},
        {".docx", UI_UDISK_PRINT_FILE_TYPE_DOC}, {".doc", UI_UDISK_PRINT_FILE_TYPE_DOC},
        {".xlsx", UI_UDISK_PRINT_FILE_TYPE_XLS}, {".xls", UI_UDISK_PRINT_FILE_TYPE_XLS},
        {".pptx", UI_UDISK_PRINT_FILE_TYPE_PPT}, {".ppt", UI_UDISK_PRINT_FILE_TYPE_PPT},
        {".jpeg", UI_UDISK_PRINT_FILE_TYPE_JPG}, {".tiff", UI_UDISK_PRINT_FILE_TYPE_TIFF},
        {".tif", UI_UDISK_PRINT_FILE_TYPE_TIFF},
    };

    char *file_extension = strrchr(file_name, '.');
    if(file_extension)
    {
        size = sizeof(support_file_extension) / sizeof(support_file_extension[0]);
        for(i = 0; i < size; ++i)
        {
            if(0 == pi_strcasecmp(support_file_extension[i].file_extension, file_extension))
            {
                return support_file_extension[i].type;
            }
        }
    }

    return UI_UDISK_PRINT_FILE_TYPE_INVALID;
}

/**
 * @brief 过滤文件
 * @return 0: 过滤文件, 1: 不过滤文件
 */
static int panel_udisk_file_filter(STO_DIR_CTX* file_info)
{
    int ret = 0;
    UI_UDISK_PRINT_FILE_TYPE_E file_type = UI_UDISK_PRINT_FILE_TYPE_INVALID;
    if(!file_info)
    {
        return ret;
    }

    switch(file_info->type)
    {
    case STO_DIR_TYPE_REG:
        file_type = panel_get_udisk_file_type(file_info->name);
        break;

    case STO_DIR_TYPE_DIR:
        /* 当前目录，上级目录，U盘自动产生的System Volume Information不显示 */
        if((file_info->name[0] != '.')
            && (pi_strcasecmp(file_info->name, "System Volume Information") != 0))
        {
            file_type = UI_UDISK_PRINT_FILE_TYPE_FOLDER;
        }
        break;

    default:
        break;
    }

    /* 面板需要知道支持文件的具体类型，因此将type转成面板需要的类型传上去 */
    if (file_type != UI_UDISK_PRINT_FILE_TYPE_INVALID)
    {
        file_info->type = (unsigned char)file_type;
        ret = 1;
    }

    return ret;
}

/**
 * @brief 排序文件
 * @return 0: 相同, >0: 文件1排在文件2后面, <0: 文件1排在文件2前面
 */
static int panel_udisk_file_sort(const STO_DIR_CTX* file_info1, const STO_DIR_CTX* file_info2)
{
    if(!file_info1 || !file_info2)
    {
        return 0;
    }

    /* 目录要排在文件前面 */
    if(file_info1->type > file_info2->type)
    {
        return 1;
    }
    else if(file_info1->type == file_info2->type)
    {
        /* 同类型按字母顺序排 */
        return pi_strcasecmp(file_info1->name, file_info2->name);
    }

    return -1;
}

int panel_get_udisk_file_info(const char* dir_path)
{
    int ret = 0;
    STO_DIR_CTX *file_info_list = NULL;
    STO_DIR_CTX null_file = { 0 }; 
    int file_cnt = panel_scandir(dir_path, &file_info_list, panel_udisk_file_filter, panel_udisk_file_sort);
    if(file_cnt > 0)
    {
        pi_log_d("panel recv udisk file:%s\n", dir_path);

        ret = panel_send_data_u8(SETTING, SETTING_CMD_UPDATE_UDISK_FILE,
                                    NOTIFICATION_RESPONSE, file_info_list, sizeof(STO_DIR_CTX) * file_cnt);

#if UDISK_PRINT_DEBUG
    int i = 0;
        for(i = 0; i < file_cnt; ++i)
        {
            pi_log_d("file[%d]:%s, type:%d\n", i, file_info_list[i].name, file_info_list[i].type);
        }
#endif

        if(file_info_list)
        {
            pi_free(file_info_list);
            file_info_list = NULL;
        }

        return ret;
    }
    else if(0 == file_cnt)
    {
        /* 文件夹里面是空的，返回一个空结构体通知到面板 */
        int ret = panel_send_data_u8(SETTING, SETTING_CMD_UPDATE_UDISK_FILE,
                                    NOTIFICATION_RESPONSE, &null_file, sizeof(STO_DIR_CTX));

        return ret;
    }

    return -1;
}
 /**
  * @}
  */



