/**
 * @file license_auth_utils.h
 * @brief Utility functions for license authentication.
 * @details This file contains declarations of utility functions for file operations, cJSON handling, and Base64 processing in the license authentication module.
 * @addtogroup license_authentication
 * @{
 * @modifier yangzikun
 * @date 2024-12-11
 */

#ifndef _LICENSE_AUTH_UTILS_H_
#define _LICENSE_AUTH_UTILS_H_

#include "license_auth_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Retrieves the length of a file and resets the file pointer to the start.
 * @details Uses `lseek` to determine the length of the file. Ensures the file pointer is reset to the beginning after the operation.
 * @param[in] fd File descriptor identifying the file.
 * @return The length of the file in bytes.
 */
unsigned long license_auth_get_file_length(int32_t fd);

/**
 * @brief Writes a string to a file.
 * @details Creates or overwrites the specified file with the provided string content.
 * @param[in] string The string to write.
 * @param[in] output_file The file path where the string will be written.
 * @return On success, returns the file path. On failure, returns NULL.
 */
char *license_auth_write_to_file(const char *string, const char *output_file);

/**
 * @brief Parses cJSON data from a file.
 * @details Reads the file and parses its content into a cJSON object.
 * @param[in] path File path containing valid cJSON content.
 * @return On success, returns a pointer to the parsed cJSON object. On failure, returns NULL.
 */
cJSON *license_auth_get_cjson_from_file(const char *path);

/**
 * @brief Writes cJSON data to a file.
 * @details If the file path is NULL, it writes to a default path (`LICENSE_AUTH_TMP_FILE`).
 * @param[in] item The cJSON object to write.
 * @param[in] file The file path to write to, or NULL to use the default path.
 * @return On success, returns the file path. On failure, returns NULL.
 */
char *license_auth_write_cjson_to_file(cJSON *item, const char *file);

/**
 * @brief Writes unformatted cJSON data to a file.
 * @details Similar to `license_auth_write_cjson_to_file`, but writes the cJSON data in an unformatted manner.
 * @param[in] item The cJSON object to write.
 * @param[in] file The file path to write to, or NULL to use the default path.
 * @return On success, returns the file path. On failure, returns NULL.
 */
char *license_auth_write_unformatted_cjson_to_file(cJSON *item, const char *file);

/**
 * @brief Compares the contents of two files.
 * @details Compares up to 90% of the file contents to determine if they are identical.
 * @param[in] input_file The path to the first file.
 * @param[in] output_file The path to the second file.
 * @return Returns 0 if the files are identical, non-zero if they differ or if an error occurs.
 */
int32_t license_auth_compare_files(const char *input_file, const char *output_file);

/**
 * @brief Copies the content of one file to another.
 * @details Reads the content from the source file and writes it to the target file. Supports all file types.
 * @param[in] source_file The source file path.
 * @param[out] target_file The target file path.
 * @return On success, returns the target file path. On failure, returns NULL.
 */
char *license_auth_copy_file(const char *source_file, const char *target_file);

/**
 * @brief Decodes Base64 content and writes it to a file.
 * @details The input can be either a Base64-encoded string or a file path containing Base64 data.
 * @param[in] input_string_or_file The Base64 string or file path.
 * @param[in] output_file The output file path to write the decoded content.
 * @return On success, returns the output file path. On failure, returns NULL.
 */
char *license_auth_decode_base64_to_file(char *input_string_or_file, const char *output_file);

/**
 * @brief Writes the string value of a cJSON node to a file.
 * @details Only supports nodes of type `valuestring`.
 * @param[in] item The cJSON node containing the string value.
 * @param[in] file The file path to write the value to.
 * @return On success, returns the file path. On failure, returns NULL.
 */
char *license_auth_write_link_node_to_file(cJSON *item, const char *file);

/**
 * @brief Compares the contents of a file with NVRAM-stored data.
 * @details Verifies whether the specified file's contents match the corresponding NVRAM data.
 * @param[in] file_path The path to the file to compare.
 * @return Returns 0 if the contents match, non-zero otherwise.
 */
int32_t license_auth_compare_file_with_nvram(const char *file_path);

#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_UTILS_H_ */

/**
 * @}
 */
