#ifndef EVENT_MGR_SERVICE_H 
#define EVENT_MGR_SERVICE_H 

#define EVT_ERR(fmt, ...)       printf("[ERROR][%s]" fmt "\n", __func__, ##__VA_ARGS__)
#define EVT_LOG(fmt, ...)       printf("[DEBUG][%s]" fmt "\n", __func__, ##__VA_ARGS__)

struct evt_mgr_svc;
typedef struct evt_mgr_svc EVT_MGR_SVC_S;

EVT_MGR_SVC_S* event_mgr_svc_create         (void);

void    event_mgr_svc_start_loop            (EVT_MGR_SVC_S* thiz);

void    event_mgr_svc_destroy               (EVT_MGR_SVC_S* thiz);

int32_t event_mgr_svc_dispatch_connect      (EVT_MGR_SVC_S* thiz, struct binder_state* bs, uint32_t module_id, int32_t write_fd, void* obj);

int32_t event_mgr_svc_dispatch_disconnect   (EVT_MGR_SVC_S* thiz, struct binder_state* bs, uint32_t module_id);

int32_t event_mgr_svc_dispatch_register     (EVT_MGR_SVC_S* thiz, uint32_t module_id, uint32_t* event_array, uint32_t event_count, uint32_t cmd);

int32_t event_mgr_svc_dispatch_notify       (EVT_MGR_SVC_S* thiz, uint32_t module_id, uint32_t event_type, char* buffer, uint32_t length);

#endif /* EVENT_MGR_SERVICE_H */
