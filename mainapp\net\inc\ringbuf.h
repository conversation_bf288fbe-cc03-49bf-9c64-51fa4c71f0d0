/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ringbuf.h
 * @addtogroup net
 * @{
 * @addtogroup ringbuf
 * <AUTHOR>
 * @date 2023-9-18
 * @brief ring buffer header file
 */
#ifndef __RINGBUF_H__
#define __RINGBUF_H__

struct ring_buffer; ///< defined in net/common/ringbuf.c
typedef struct ring_buffer RING_BUF_S;

/**
 * @brief       Check if the ring buffer is readable. This function can be seen as\n
 *              int32_t ringbuf_readable(RING_BUF_S* rbuf);
 * @param[in]   rbuf    : The RING_BUF_S object pointer.
 * @return      check result
 * @retval      != 0    : yes\n
 *              == 0    : no
 * <AUTHOR> <PERSON>
 * @date        2023-9-18
 */
int32_t         ring_buffer_readable    (RING_BUF_S* rbuf, const char* caller);
#define         ringbuf_readable(r)     ring_buffer_readable(r, __func__)

/**
 * @brief       Check if the ring buffer is writable. This function can be seen as\n
 *              int32_t ringbuf_writable(RING_BUF_S* rbuf);
 * @param[in]   rbuf    : The RING_BUF_S object pointer.
 * @return      check result
 * @retval      != 0    : yes\n
 *              == 0    : no
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         ring_buffer_writable    (RING_BUF_S* rbuf, const char* caller);
#define         ringbuf_writable(r)     ring_buffer_writable(r, __func__)

/**
 * @brief       Write count bytes to this ring buffer. This function can be seen as\n
 *              int32_t ringbuf_write(RING_BUF_S* rbuf, void* buffer, size_t count);
 * @param[in]   rbuf    : The RING_BUF_S object pointer.
 * @param[in]   buffer  : Write data from this buffer to the ring buffer.
 * @param[in]   count   : The number of bytes expected to be written.
 * @return      The number of bytes already written or error.
 * @retval      >= 0    : bytes\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         ring_buffer_write       (RING_BUF_S* rbuf, const void* buffer, size_t count, const char* caller);
#define         ringbuf_write(r, b, c)  ring_buffer_write(r, b, c, __func__)

/**
 * @brief       Read count bytes from this ring buffer. This function can be seen as\n
 *              int32_t ringbuf_read(RING_BUF_S* rbuf, void* buffer, size_t count);
 * @param[in]   rbuf    : The RING_BUF_S object pointer.
 * @param[out]  buffer  : Read data from the ring buffer to this buffer.
 * @param[in]   count   : The number of bytes expected to be read.
 * @return      The number of bytes already read or error.
 * @retval      >= 0    : bytes\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         ring_buffer_read        (RING_BUF_S* rbuf, void* buffer, size_t count, const char* caller);
#define         ringbuf_read(r, b, c)   ring_buffer_read(r, b, c, __func__)

/**
 * @brief       Reset this ring buffer. This function can be seen as\n
 *              int32_t ringbuf_reset(RING_BUF_S* rbuf);
 * @param[in]   rbuf    : The RING_BUF_S object pointer.
 * @return      reset result.
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
int32_t         ring_buffer_reset       (RING_BUF_S* rbuf, const char* caller);
#define         ringbuf_reset(r)        ring_buffer_reset(r, __func__)

/**
 * @brief       Destroy this ring buffer. This function can be seen as\n
 *              int32_t ringbuf_destroy(RING_BUF_S* rbuf);
 * @param[in]   rbuf    : The RING_BUF_S object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
void            ring_buffer_destroy     (RING_BUF_S* rbuf, const char* caller);
#define         ringbuf_destroy(r)      ring_buffer_destroy(r, __func__)

/**
 * @brief       Create a ring buffer. This function can be seen as\n
 *              RING_BUF_S* ringbuf_create(size_t size);
 * @param[in]   size    : The size of RING_BUF_S object.
 * @return      create result.
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
RING_BUF_S*     ring_buffer_create      (uint32_t size, const char* caller);
#define         ringbuf_create(s)       ring_buffer_create(s, __func__)

#endif /* __RINGBUF_H__ */
/**
 *@}
 */
