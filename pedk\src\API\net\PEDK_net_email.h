#ifndef _PEDK_NET_EMAIL_
#define _PEDK_NET_EMAIL_

#include <quickjs.h>

#define SMTP_HOST_LEN                   128
#define SMTP_USERNAME_LEN               128
#define SMTP_PASSWORD_LEN               128
#define SMTP_RECIPIENT_LEN              128
#define SMTP_RECIPIENT_MAX              60
#define SMTP_SEND_FROM_LEN              128
#define SMTP_SUBJECT_LEN                256

typedef enum
{
    eml_none,
    eml_ssl,
    eml_tls
}
PEDK_EMAILTYPE;


typedef struct{
    char host[SMTP_HOST_LEN];             //smtp server
    int  port;                      //port
    char username[SMTP_USERNAME_LEN];         //sender password
    char password[SMTP_PASSWORD_LEN];             //email from
    int  iden_authen;               //auth_enable
    PEDK_EMAILTYPE type;        //none,ssl,startls
}
PEDK_SMTP_PARAMETER_SET;

typedef struct{
    PEDK_SMTP_PARAMETER_SET smtp_server_info;                           //smtp_server_info
    char recipient_email[SMTP_RECIPIENT_LEN * SMTP_RECIPIENT_MAX];      //smtp recipient_email
    char send_from[SMTP_SEND_FROM_LEN];                                 //send_from
    char subject[SMTP_SUBJECT_LEN];                                     //subject
    char* body;                                                         //body
}
SEND_EMAIL_INFO;

JSValue js_send_email(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);


#endif /* _PEDK_NET_EMAIL_ */
