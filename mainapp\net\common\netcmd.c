/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netcmd.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-9-18
 * @brief network register CMD command
 */
#include "nettypes.h"
#include "netctx.h"

#include "cmd.h" ///< form common/cmd module

static NET_CTX_S*   s_cmd_net_ctx = NULL;

#if CONFIG_NET_WIFI
#include "wifi.h"

/**
 * @brief       process cmd: "echo pmf enable [value] > /tmp/cmd".
 * @param[in]   argc    : Parameter number.
 * @param[in]   argv    : Parameter array.
 * @return      result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR>
 * @date   2023-9-18
 */
static int32_t netcmd_set_pmf_mode(int32_t argc, char* argv[])
{
    int32_t value;

    RETURN_VAL_IF(s_cmd_net_ctx == NULL || argc < 1, NET_WARN, -1);

    value = atoi(argv[0]);
    if ( value < PMF_DISABLE || value > PMF_REQUIRED )
    {
        value = PMF_AUTO;
    }
    netdata_set_sta_pmf(s_cmd_net_ctx->data_mgr, (uint32_t)value);

    return 0;
}

/**
 * @brief       process cmd: "echo pmf get > /tmp/cmd".
 * @param[in]   argc    : Parameter number.
 * @param[in]   argv    : Parameter array.
 * @return      result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date   2023-9-18
 */
static int32_t netcmd_get_pmf_mode(int32_t argc, char* argv[])
{
    RETURN_VAL_IF(s_cmd_net_ctx == NULL, NET_WARN, -1);

    NET_DEBUG("current PMF mode(%u)", netdata_get_sta_pmf(s_cmd_net_ctx->data_mgr));

    return 0;
}
#endif /* CONFIG_NET_WIFI */

/**
 * @brief       process cmd: "echo net_debug level [value] > /tmp/cmd".
 * @param[in]   argc    : Parameter number.
 * @param[in]   argv    : Parameter array.
 * @return      result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date   2023-9-18
 */
static int32_t netcmd_set_debug_level(int32_t argc, char* argv[])
{
    int32_t value;

    RETURN_VAL_IF(s_cmd_net_ctx == NULL || argc < 1, NET_WARN, -1);

    value = atoi(argv[0]);
    RETURN_VAL_IF(value < NET_LOG_LVL_S, NET_WARN, -1);
    RETURN_VAL_IF(value > NET_LOG_LVL_T, NET_WARN, -1);

    printf("network log level(%d)\n", value);
    g_net_log_lvl = value;

    return 0;
}

static int32_t netcmd_get_wired_speed(int32_t argc, char* argv[])
{
    printf("get %s speed mode(%u)\n", IFACE_ETH, netdata_get_wired_speed(s_cmd_net_ctx->data_mgr));
    return 0;
}

static int32_t netcmd_set_wired_speed(int32_t argc, char* argv[])
{
    RETURN_VAL_IF(argc <= 0 || argv[0] == NULL, NET_WARN, -1);

    netctx_update_wired_speed(s_cmd_net_ctx, (IFACE_SPEED_E)atoi(argv[0]));
    printf("set %s speed mode(%s)\n", IFACE_ETH, argv[0]);

    return 0;
}

static int32_t netcmd_set_mdns_debug(int32_t argc, char* argv[])
{
    RETURN_VAL_IF(argc <= 0 || argv[0] == NULL, NET_WARN, -1);

    netdata_set_mdns_debug(s_cmd_net_ctx->data_mgr, (uint32_t)atoi(argv[0]));
    netctx_push_netport_subject(s_cmd_net_ctx, PORT_UPDATE_BONJOUR);
    printf("set mdns debug mode(%s)\n", argv[0]);

    return 0;
}

/**
 * @brief       process cmd: "echo mac_addr set XX:XX:XX:XX:XX:XX > /tmp/cmd".
 * @param[in]   argc    : Parameter number.
 * @param[in]   argv    : Parameter array.
 * @return      result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date   2023-9-18
 */
static int32_t netcmd_set_mac_addr(int32_t argc, char* argv[])
{
    int32_t ret;

    RETURN_VAL_IF(s_cmd_net_ctx == NULL || argc < 1, NET_WARN, -1);

    ret = netctx_update_mac_addr(s_cmd_net_ctx, IFACE_ID_ETH, argv[0]);
    NET_DEBUG("set mac(%s) result(%d)\n", argv[0], ret);

    return ret;
}

void netcmd_init(NET_CTX_S* net_ctx)
{
    RETURN_IF(s_cmd_net_ctx != NULL || net_ctx == NULL, NET_WARN);

    s_cmd_net_ctx = net_ctx;

    RETURN_IF(cmd_register("net_debug", "level",  netcmd_set_debug_level, NULL) != 0, NET_WARN);
    RETURN_IF(cmd_register("net_speed", "get",    netcmd_get_wired_speed, NULL) != 0, NET_WARN);
    RETURN_IF(cmd_register("net_speed", "set",    netcmd_set_wired_speed, NULL) != 0, NET_WARN);
    RETURN_IF(cmd_register("mdns_debug","set",    netcmd_set_mdns_debug,  NULL) != 0, NET_WARN);
    RETURN_IF(cmd_register("mac",       "set",    netcmd_set_mac_addr,    NULL) != 0, NET_WARN);

#if CONFIG_NET_WIFI
    if ( netdata_get_support_wifi(s_cmd_net_ctx->data_mgr) )
    {
        RETURN_IF(cmd_register("pmf",   "enable", netcmd_set_pmf_mode, NULL) != 0, NET_WARN);
        RETURN_IF(cmd_register("pmf",   "get",    netcmd_get_pmf_mode, NULL) != 0, NET_WARN);
    }
#endif /* CONFIG_NET_WIFI */
}
/**
 *@}
 */
