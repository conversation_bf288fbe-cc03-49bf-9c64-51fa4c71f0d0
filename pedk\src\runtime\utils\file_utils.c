#include "runtime/utils/file_utils.h"

#include <stdio.h>
#include <unistd.h>
#include <string.h>

int is_path_exists(const char *path)
{
    if (path == NULL)
    {
        return 0;
    }

    return access(path, F_OK) == 0;
}

int read_file(const char *path, char *buf, size_t buf_size, size_t *read_bytes)
{
    if (path == NULL || buf == NULL || buf_size == 0 || read_bytes == NULL)
    {
        return -1;
    }

    FILE *file = fopen(path, "r");

    if (!file)
    {
        return -1;
    }

    memset(buf, 0x00, buf_size);

    *read_bytes = fread(buf, 1, buf_size, file);
    fclose(file);

    return 0;
}

size_t get_file_size(const char *file)
{
    FILE *pf;

    pf = fopen(file,"r");
    if(pf == NULL){
        return 0;
    }
    
    fseek(pf, 0, SEEK_END);
    size_t file_size = ftell(pf);
    fclose(pf);

    return file_size;
}
