#include <sys/eventfd.h>
#include <poll.h>
#include "nettypes.h"
#include "netmodules.h"
#include "http_task.h"
#include "netport.h"
#include "netmisc.h"
#include "netsock.h"
#include "netsts.h"
#include "netctx.h"
#include "wsdef.h"
#include "wsd.h"
#include "qxml.h"
#include "soap.h"
#include "public_data_proc.h"
#include "job_manager.h"
#include "netjob.h"
#include "wsdjob.h"
#include "wsdsubs.h"
#include "wsdservice.h"
#include "pol/pol_list.h"
#include "ringbuf.h"

// #define WSD_DEBUG
#define WSD_MESSAGE_JOB_FAILED 1

#define WSD_PRNT_XMLNS      "wprt"
#define WSD_PRNT_XMLNS_URL  "http://schemas.microsoft.com/windows/2006/08/wdp/print"

#define HEADER_CONTENT_TYPE        "content-type"
#define BOUNDARY_CONTENT_ID        "Content-ID:"
#define MULTIPART_RELATED          "multipart/related;"
#define MULTIPART_BOUNDARY         "boundary="
#define QIO_RING_BUF_LEN           (1024 * 1024)
#define MSLEEP_DURATION            200
#define SEND_DOCUMENT_HEADER_LEN   8192
#define SECOND                     1000
#define CRLF2                       "\r\n\r\n"

typedef struct wsd_print_ticket
{
    WSD_TICKET_S  ticket;
    uint32_t      page_cnt;
    int           ndocuments;
    int           finished;
    int64_t       bytes_processed;         ///< bytes xferred this job
}
WSD_PRNT_TICKET_S;

static char* s_complete_state[NETJOB_STATE_MAX] = {
    [NETJOB_STATE_ABORTED] = "Aborted",
    [NETJOB_STATE_CANCELED] = "Canceled",
    [NETJOB_STATE_DONE] = "Completed",
};

typedef struct wsd_print_qio_ctx
{
    volatile int ref_cnt;
    volatile int recv_finished;
    volatile int mark_closed;
    RING_BUF_S*  ringbuf;
}
WSD_PRNT_QIO_CTX_S;

typedef struct wsd_printer_context
{
    WSD_CTX_S*       wsd_ctx;
    int              efd;
    volatile const char*   state_str;
    volatile const char*   reason_str;
    PI_THREAD_T      evt_tid;
    PI_THREAD_T      net_tid;
    PI_MUTEX_T       mutex;

    // below for eventing
    volatile int     endstate_job_id;             ///< job id for endstate
    volatile uint8_t flg_JobEndState : 1;         // if set, need to send
}
WSD_PRNT_CTX_S;

typedef struct wsdconn_task_private
{
    char        iobuf[0x28080];   // must > TCP_RCVBUF_SIZE + multipart boundary len
    size_t      received;
    char        boundary[80];
    int         boundary_len;
    int         send_document_parsed;
    WSD_SERVICE_DATA_S service_data;
    QIO_S       *pqio;
#ifdef WSD_DEBUG
    FILE*       fp;
#endif
    uint32_t    ntotal_written;
}
PRIV_INFO_S;

static WSD_PRNT_CTX_S* s_wsd_prnt_ctx = NULL;

static int print_get_job_elements_handler(WSD_TICKET_S* pticket, QXML_S* qxml, OUT SOAP_VAR_S** out_pdata, OUT int* out_valid);
static int WSDprintJobInfoVar(WSD_TICKET_S* pticket, int endState, int summary, OUT SOAP_VAR_S **out_ppvar);
static int WSDprintTicketInfoVar(WSD_TICKET_S* pticket, OUT SOAP_VAR_S **out_ppvar);
static void PrintTicketDestroy(WSD_TICKET_S* pticket);
static ACTION_HANDLER_DEFINITION(wsd_print_send_document_handler);
static ACTION_HANDLER_DEFINITION(wsd_print_create_job_handler);
static ACTION_HANDLER_DEFINITION(wsd_print_add_document_handler);
static ACTION_HANDLER_DEFINITION(wsd_print_get_printer_elements_handler);
static QIO_S* wsd_prnt_qio_ref(QIO_S* pqio);

static ACTION_HNDL_S s_action_table[] =
{
    { "CreatePrintJob",      wsd_print_create_job_handler             },
    { "SendDocument",        wsd_print_send_document_handler          },
    { "AddDocument",         wsd_print_add_document_handler           },
    { "CancelJob",           wsd_srvcomm_cancel_job_handler           },
    { "GetPrinterElements",  wsd_print_get_printer_elements_handler   },
    { "GetJobElements",      wsd_srvcomm_get_job_elements_handler     },
    { "GetActiveJobs",       wsd_srvcomm_get_active_job_handler       },
    { "GetJobHistory",       wsd_srvcomm_get_job_history_handler      },
    { "SetEventRate",        wsd_srvcomm_set_event_rate_handler       },
    { "Probe",               wsd_srvcomm_probe_handler                },
    { NULL,                  NULL                                     },
};

static WSD_SVC_S s_print_service = {
    .xmlns = WSD_PRNT_XMLNS,
    .xmlns_url = WSD_PRNT_XMLNS_URL,
    .event_rate = WSD_DEFAULT_EVENT_RATE,
    .action_hndl_tbl = s_action_table,
    .get_job_elements_handler = print_get_job_elements_handler,
    .ticket_destroy = PrintTicketDestroy,
};

static void job_start_notify( uint32_t job_id, int32_t result , void *context )
{
    uint64_t evt_data;
    WSD_TICKET_S* pticket = context;

    RETURN_IF(pticket==NULL, NET_WARN);
    if (result == 0)
    {
        NET_DEBUG("wsd print job %u start successfully", job_id);
        pticket->state = NETJOB_STATE_PROCESSING;
    }
    else
    {
        pticket->state = NETJOB_STATE_ABORTED;
        NET_WARN("wsd print job %u failed to start, result = %d", job_id, result);
    }
}

static int32_t wsd_rbuf_write_all(RING_BUF_S* rbuf, const char* buffer, size_t count)
{
    size_t      wtotal = 0;
    int32_t     nwrite = 0;

    while ( wtotal < count )
    {
        nwrite = ringbuf_write(rbuf, buffer + wtotal, count - wtotal);
        if ( nwrite > 0 )
        {
            wtotal += nwrite;
            continue;
        }
        NET_DEBUG("ring buffer is full, write waiting for %d milliseconds to retry", SECOND);
        pi_msleep(SECOND);
    }

    return wtotal;
}

static int print_get_job_elements_handler(WSD_TICKET_S* pticket, QXML_S* pxml, OUT SOAP_VAR_S** out_pdata, OUT int* out_valid)
{
    int rc;
    char  req[128] = {0};
    if (! strcmp(QXMLelementName(pxml, req), "JobStatus"))
    {
        rc = WSDprintJobInfoVar(pticket, 0, 0, out_pdata);
        *out_valid = 1;
    }
    else if (! strcmp(QXMLelementName(pxml, req), "PrintTicket"))
    {
        rc = WSDprintTicketInfoVar(pticket, out_pdata);
        *out_valid = 1;
    }
    else
    {
        rc = 0;
        *out_valid = 0;
    }
    return rc;
}

static uint16_t inline wsd_printer_get_port()
{
    return netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_prnt_ctx)) ? WSD_PRN_PORT : 0;
}

static int WSDprintTicketInfoVar(WSD_TICKET_S* pticket, OUT SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S* pv, *pvv;
    int rc;

    *out_ppvar = NULL;
    rc = 1;
    do   // TRY
    {
        pv = soap_create_var("wprt:JobDescription", dtchar, 0, pticket->jobIdString, 0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        pvv = pv;
        pvv->m_child = soap_create_var("wprt:JobName", dtchar, 0, pticket->jobName, 0);
        if (! pvv->m_child)
        {
            break;
        }
        pvv = pvv->m_child;
        pvv->m_next = soap_create_var("wprt:JobOriginatingUserName", dtchar, 0, pticket->jobOriginator, 0);
        if (! pvv->m_next)
        {
            break;
        }
        pvv = pvv->m_next;
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int WSDprintJobInfoVar(WSD_TICKET_S* pticket, int endState, int summary, OUT SOAP_VAR_S **out_ppvar)
{
    WSD_PRNT_TICKET_S* prnt_ticket = (WSD_PRNT_TICKET_S*)pticket;
    RETURN_VAL_IF(pticket == NULL, NET_WARN, 1);

    SOAP_VAR_S* pv, *pvv;
    char *jobStateString = NULL;
    char numbuf[32];
    int jobPages = 0;
    int rc = -1;

    *out_ppvar = NULL;
    pv = NULL;

    do   // TRY
    {
        // look up print ticket by jobid
        //
        pv = soap_create_var(WSD_PRNT_XMLNS ":JobId", dtchar, 0, pticket->jobIdString, 0);
        BREAK_IF (! pv, NET_WARN);
        pvv = pv;

        if (endState)
        {
            jobStateString = s_complete_state[pticket->state];

            if (jobStateString == NULL)
            {
                jobStateString = "Processing";
            }

            pvv->m_next = soap_create_var("wprt:JobCompletedState", dtchar, 0, jobStateString, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wprt:JobCompletedStateReasons", dtchar, 0, NULL, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
        }
        else
        {
            pvv->m_next = soap_create_var("wprt:JobState", dtchar, 0, jobStateString, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wprt:JobStateReasons", dtchar, 0, NULL, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
        }
        if (strcmp(jobStateString, "Canceled") == 0)
            pvv->m_child = soap_create_var("wprt:JobStateReason", dtchar, 0, "JobCanceledAtDevice", 0);
        else
            pvv->m_child = soap_create_var("wprt:JobStateReason", dtchar, 0, "None", 0);
        if (! pvv->m_child)
        {
            break;
        }
        if (endState || summary)
        {
            pvv->m_next = soap_create_var("wprt:JobName", dtchar, 0,  pticket->jobName, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
            pvv->m_next = soap_create_var("wprt:JobOriginatingUserName", dtchar, 0, pticket->jobOriginator, 0);
            if (! pvv->m_next)
            {
                break;
            }
            pvv = pvv->m_next;
        }
        snprintf(numbuf, sizeof(numbuf), "%ld", (long)( prnt_ticket->bytes_processed / 1024) );
        pvv->m_next = soap_create_var("wprt:KOctetsProcessed", dtchar, 0, numbuf, 0);
        if (! pvv->m_next)
        {
            break;
        }
        pvv = pvv->m_next;
        //jobPages = jobstatus.job_print_pages;//FIX DXS
        /*
        if (jobPages > pticket->pageCount)
        {
            pticket->pageCount = jobPages;
            NET_INFO("Updating job pages printed to %d\n", jobPages);
        }
        */
        snprintf(numbuf, sizeof(numbuf), "%u", prnt_ticket->page_cnt);
        pvv->m_next = soap_create_var("wprt:MediaSheetsCompleted", dtint, 0, numbuf, 0);
        if (! pvv->m_next)
        {
            break;
        }
        pvv = pvv->m_next;
        snprintf(numbuf, sizeof(numbuf), "%d", prnt_ticket->ndocuments);
        pvv->m_next = soap_create_var("wprt:NumberOfDocuments", dtint, 0, numbuf, 0);
        if (! pvv->m_next)
        {
            break;
        }
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static QIO_S* wsd_prnt_qio_ref(QIO_S* pqio)
{
    RETURN_VAL_IF(pqio == NULL, NET_WARN, NULL);

    WSD_PRNT_QIO_CTX_S* qio_ctx = pqio->priv;

    RETURN_VAL_IF(qio_ctx == NULL, NET_WARN, NULL);
    ++qio_ctx->ref_cnt;
    return pqio;
}

static int32_t wsd_prnt_qio_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    int32_t   block = 0;
    int32_t   ms = 0;
    int32_t   rc = 0;

    WSD_PRNT_QIO_CTX_S* qio_ctx = pqio->priv;
    RETURN_VAL_IF(qio_ctx == NULL, NET_WARN, QIOEOF);

    RING_BUF_S* rbuf = qio_ctx->ringbuf;
    RETURN_VAL_IF(rbuf == NULL, NET_WARN, QIOEOF);

    if ( what & QIO_POLL_READ ) /* 可读检测时，判断rbuf中是否有可读数据 */
    {
        if ( tos > 0 || (tos == 0 && tous >= 0) )
        {
            ms += (tos  > 0 ? (tos  * 1000) : 0);
            ms += (tous > 0 ? (tous / 1000) : 0);
        }
        else
        {
            block = 1;
        }

        do
        {
            rc = ringbuf_readable(rbuf);
            if ( rc != 0 )
            {
                break;
            }
            else if ( qio_ctx->recv_finished )
            {
                NET_DEBUG("recv complete");
                rc = -1;
                break;
            }
            pi_msleep(MSLEEP_DURATION);
            ms -= MSLEEP_DURATION;
        }
        while ( ms > 0 || block );
    }
    else
    {
        NET_WARN("unsupported operation: %d", what);
        rc = -1;
    }

    return rc;
}

static int32_t wsd_prnt_qio_read(QIO_S* pqio, void* buf, size_t nbuf)
{
    WSD_PRNT_QIO_CTX_S* qio_ctx = pqio->priv;
    RETURN_VAL_IF(qio_ctx == NULL, NET_WARN, QIOEOF);

    RING_BUF_S* rbuf = qio_ctx->ringbuf;
    RETURN_VAL_IF(rbuf == NULL, NET_WARN, QIOEOF);
    return ringbuf_read(rbuf, buf, nbuf);
}

static int32_t wsd_prnt_qio_unref(QIO_S* pqio)
{
    WSD_PRNT_QIO_CTX_S* qio_ctx = pqio->priv;

    NET_DEBUG("wsd print qio unref");
    if (qio_ctx)
    {
        if (--qio_ctx->ref_cnt == 0)
        {
            NET_DEBUG("ref_cnt == 0, free wsd print qio");
            if (qio_ctx->ringbuf)
            {
                ringbuf_destroy(qio_ctx->ringbuf);
            }
            pi_free(qio_ctx);
            pi_free(pqio);
        }
    }
    else
    {
        pi_free(pqio);
    }
    return 0;
}

static int32_t wsd_prnt_qio_close(QIO_S* pqio)
{
    RETURN_VAL_IF(pqio == NULL, NET_WARN, QIOEOF);

    WSD_PRNT_QIO_CTX_S* qio_ctx = pqio->priv;

    if (qio_ctx)
    {
        qio_ctx->mark_closed = 1;
        wsd_prnt_qio_unref(pqio);
        return 0;
    }
    return QIOEOF;
}

static QIO_S* wsd_prnt_qio_create()
{
    QIO_S*                pqio;
    WSD_PRNT_QIO_CTX_S*   pqio_ctx;
    int ret = -1;

    pqio = (QIO_S *)pi_zalloc(sizeof(*pqio));
    RETURN_VAL_IF(pqio == NULL, NET_WARN, NULL);

    do {
        pqio_ctx = (WSD_PRNT_QIO_CTX_S*)pi_zalloc(sizeof(*pqio_ctx));
        BREAK_IF(pqio_ctx == NULL, NET_WARN);

        pqio_ctx->ringbuf = ringbuf_create(QIO_RING_BUF_LEN);
        BREAK_IF(pqio_ctx->ringbuf == NULL, NET_WARN);

        pqio->close = wsd_prnt_qio_close;
        pqio->poll  = wsd_prnt_qio_poll;
        pqio->read  = wsd_prnt_qio_read;
        pqio->priv  = pqio_ctx;
        pqio_ctx->ref_cnt = 1;
        ret = 0;
    }
    while (0);

    if (ret < 0)
    {
        if (pqio_ctx)
        {
            if (pqio_ctx->ringbuf)
            {
                ringbuf_destroy(pqio_ctx->ringbuf);
            }
            pi_free(pqio_ctx);
        }
        pi_free(pqio);
        return NULL;
    }

    return pqio;
}

static WSD_PRNT_TICKET_S* PrintTicketCreate()
{
    WSD_TICKET_S* pticket;
    WSD_PRNT_TICKET_S* prnt_ticket;

    prnt_ticket = (WSD_PRNT_TICKET_S *)pi_zalloc(sizeof(WSD_PRNT_TICKET_S));
    RETURN_VAL_IF(prnt_ticket == NULL, NET_WARN, NULL);

    do {
        pticket = (WSD_TICKET_S*)prnt_ticket;
        pticket->state = NETJOB_STATE_NEW;
        pticket->svc_type = &s_print_service;
        pticket->job_id = (int32_t)job_manager_apply_jobid(IO_VIA_NET , 0);
        BREAK_IF (pticket->job_id <= 0, NET_WARN);
        NET_DEBUG("create ticket:%p, ticket->id:%d", pticket, pticket->job_id);
        snprintf(pticket->jobIdString, sizeof(pticket->jobIdString), "%d", pticket->job_id);
        return prnt_ticket;
    }
    while(0);
    if (pticket)
    {
        PrintTicketDestroy(pticket);
    }
    return NULL;
}

static void PrintTicketDestroy(WSD_TICKET_S* pticket)
{
    RETURN_IF(pticket == NULL, NET_WARN);
    WSD_PRNT_TICKET_S* prnt_ticket = (WSD_PRNT_TICKET_S*)pticket;
    if (pticket->job_id)
    {
        netjob_free(pticket->job_id, 0);
    }

    pi_free(prnt_ticket);
}

static int WSDprinterConfigurationInfoVar(PRIV_INFO_S* priv, SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *root, *pv, *ptmp;
    char rate_str[8];
    int rc = -1;

    *out_ppvar = NULL;
    do   // TRY
    {
        root = soap_create_var( WSD_PRNT_XMLNS ":PrinterConfiguration", dtchar, 0, NULL, 0);
        BREAK_IF (!root, NET_WARN);

        snprintf(rate_str, sizeof(rate_str), "%d", s_print_service.event_rate);
        pv = soap_var_append_child(root, soap_create_var(WSD_PRNT_XMLNS ":PrinterEventRate", dtchar, 0, rate_str, 0) );
        BREAK_IF(!pv, NET_WARN);

        pv = soap_var_append_child(root, soap_create_var(WSD_PRNT_XMLNS ":Storage", dtchar, 0, NULL, 0) );
        BREAK_IF(!pv, NET_WARN);

        pv = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":StorageEntry", dtchar, 0, NULL, 1, "Name", "RAM") );
        BREAK_IF(!pv, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Type", dtchar, 0, "RAM", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Size", dtchar, 0, "1900000", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Free", dtchar, 0, "80", 0));
        BREAK_IF(!ptmp, NET_WARN);

        pv = soap_var_append_child(root, soap_create_var(WSD_PRNT_XMLNS ":Consumables", dtchar, 0, NULL, 0) );
        BREAK_IF(!pv, NET_WARN);
        pv = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":ConsumableEntry", dtchar, 0, NULL, 1, "Name", "Toner Cartridge (K)") );
        BREAK_IF(!pv, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Type", dtchar, 0, "Toner", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Color", dtchar, 0, "Black", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Level", dtchar, 0, "-1", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Model", dtchar, 0, "TonerBlaock", 0));
        BREAK_IF(!ptmp, NET_WARN);
        //
        //rc = soap_var_append_child(root, soap_create_var(WSD_PRNT_XMLNS ":Finishings", dtchar, 0, NULL, 0) );
        //BREAK_IF(!rc, NET_WARN);

        pv = soap_var_append_child(root, soap_create_var(WSD_PRNT_XMLNS ":InputBins", dtchar, 0, NULL, 0) );
        BREAK_IF(!pv, NET_WARN);

        pv = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":InputBinEntry", dtchar, 0, NULL, 1, "Name", "Manual") );
        BREAK_IF(!pv, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":FeedDirection", dtchar, 0, "ShortEdgeFirst", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":MediaSize", dtchar, 0, "iso_a4_210x297mm", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":MediaColor", dtchar, 0, "unknown", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Capacity", dtchar, 0, "500", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Level", dtchar, 0, "-1", 0));
        BREAK_IF(!ptmp, NET_WARN);

        pv = soap_var_append_child(root, soap_create_var(WSD_PRNT_XMLNS ":OutputBins", dtchar, 0, NULL, 0) );
        BREAK_IF(!pv, NET_WARN);

        pv = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":OutputBinEntry", dtchar, 0, NULL, 1, "Name", "Top") );
        BREAK_IF(!pv, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Capacity", dtchar, 0, "200", 0));
        BREAK_IF(!ptmp, NET_WARN);

        ptmp = soap_var_append_child(pv, soap_create_var(WSD_PRNT_XMLNS ":Level", dtchar, 0, "-1", 0));
        BREAK_IF(!ptmp, NET_WARN);

        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (root)
        {
            soap_delete_var(root);
        }
    }
    else
    {
        *out_ppvar = root;
    }
    return rc;
}

int WSDprinterStateInfoVar(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S **out_ppvar)
{
    SOAP_VAR_S *pv, *pvv;
    int   rc = -1;
    const char*state_str = (char*)s_wsd_prnt_ctx->state_str;
    const char*reason_str = (char*)s_wsd_prnt_ctx->reason_str;

    *out_ppvar = NULL;
    do   // TRY
    {
        pv = soap_create_var("wprt:PrinterState", dtchar, 0, state_str, 0);
        BREAK_IF(! pv, NET_WARN)
        pv->m_next = soap_create_var("wprt:PrinterPrimaryStateReason", dtchar, 0, reason_str, 0);
        BREAK_IF(! pv->m_next, NET_WARN)
        pvv = pv->m_next;
        pvv->m_next = soap_create_var("wprt:PrinterStateReasons", dtchar, 0, NULL, 0);
        BREAK_IF(! pvv->m_next, NET_WARN)
        pvv = pvv->m_next;
        pvv->m_child = soap_create_var("wprt:PrinterStateReason", dtchar, 0, reason_str, 0);
        BREAK_IF (! pvv->m_child, NET_WARN)
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *out_ppvar = pv;
    }
    return rc;
}

static int32_t wsd_print_get_status_element(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *pVar, *pVV, *pvs;
    char timestr[64];
    time_t now;
    struct tm *ct;
    int rc = 1;

    *out_ppvar = NULL;

    time(&now);
    ct = gmtime(&now);
    snprintf(timestr, sizeof(timestr), "%4d-%02d-%02dT%02d:%02d:%02dZ",
        ct->tm_year + 1900, ct->tm_mon, ct->tm_mday, ct->tm_hour, ct->tm_min, ct->tm_sec);

    do  // try
    {
        pVar = soap_create_var(
                "wprt:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wprt:PrinterStatus",
                "Valid", "true"
                );
        if (! pVar)
        {
            rc = -1;
            break;
        }
        pVar->m_child = soap_create_var(
                "wprt:PrinterStatus",
                dtchar,
                0,
                NULL,
                0);
        if (! pVar->m_child)
        {
            rc = -1;
            break;
        }
        pVV = pVar->m_child;
        pVV->m_child = soap_create_var("wprt:PrinterCurrentTime", dtchar, 0, timestr, 0);
        if (! pVV->m_child)
        {
            rc = -1;
            break;
        }
        pVV = pVV->m_child;

        // get printer status summary
        //
        rc = WSDprinterStateInfoVar(srv_data, &pvs);
        if (rc)
        {
            break;
        }
        if (! pvs)
        {
            rc = -1;
            break;
        }
        rc = 1;

        // and link it to the end of current sibling chain
        //
        pVV->m_next = pvs;
        while (pvs->m_next)
        {
            pvs = pvs->m_next;
        }
        pVV = pvs;

        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static int32_t wsd_print_get_configuration_element(PRIV_INFO_S* priv, SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S* pVar, *pVV;
    int rc = -1;
    *out_ppvar = NULL;

    do  // try
    {
        pVar = soap_create_var(
                WSD_PRNT_XMLNS ":ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", WSD_PRNT_XMLNS ":PrinterConfiguration",
                "Valid", "true"
                );
        BREAK_IF (! pVar, NET_WARN);

        rc = WSDprinterConfigurationInfoVar(priv, &pVV);
        if (rc)
        {
            break;
        }
        BREAK_IF (! pVV, NET_WARN);
        pVar->m_child = pVV;
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static int32_t wsd_print_get_description_element(SOAP_VAR_S** out_ppvar)
{
    SOAP_VAR_S *pVar, *pVV;
    int     rc = -1;
    char    namestr[256];
    char    hostname[HOSTNAME_LEN];
    char    pdt_name[PDT_NAME_LEN];
    char    prnt_name[HOSTNAME_LEN + PDT_NAME_LEN + 16];
    char    ieee1284[IEEE1284_LEN];
    char    location[256];
    char    int_str[10];

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsd_prnt_ctx), hostname, sizeof(hostname));
    netdata_get_pdt_name(DATA_MGR_OF_WSD(s_wsd_prnt_ctx), pdt_name, sizeof(pdt_name));
    netdata_get_ieee1284(DATA_MGR_OF_WSD(s_wsd_prnt_ctx), ieee1284, sizeof(ieee1284));
    netdata_get_location(DATA_MGR_OF_WSD(s_wsd_prnt_ctx), location, sizeof(location));

    *out_ppvar = NULL;
    do  // try
    {
        pVar = soap_create_var(
                WSD_PRNT_XMLNS ":ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", WSD_PRNT_XMLNS ":PrinterDescription",
                "Valid", "true"
                );
        BREAK_IF (! pVar, NET_WARN);

        pVar->m_child = soap_create_var(
                WSD_PRNT_XMLNS ":PrinterDescription",
                dtchar,
                0,
                NULL,
                0);
        BREAK_IF (! pVar->m_child, NET_WARN);

        pVV = pVar->m_child;

        // chain rest of printer description as siblings under description
        //
        pVV->m_child = soap_create_var(WSD_PRNT_XMLNS ":ColorSupported", dtchar, 0,
                                       netdata_get_support_color(DATA_MGR_OF_WSD(s_wsd_prnt_ctx)) ? "true" : "false", 0);
        BREAK_IF (! pVV->m_child, NET_WARN);
        pVV = pVV->m_child;
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":DeviceId", dtchar, 0, ieee1284, 0);
        BREAK_IF(! pVV->m_next, NET_WARN);
        pVV = pVV->m_next;
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":MultipleDocumentJobsSupported", dtchar, 0, "true", 0);
        BREAK_IF(! pVV->m_next, NET_WARN);
        pVV = pVV->m_next;
        netdata_get_print_speed(DATA_MGR_OF_WSD(s_wsd_prnt_ctx));
        snprintf(int_str, sizeof(int_str), "%u", netdata_get_print_speed(DATA_MGR_OF_WSD(s_wsd_prnt_ctx)) );
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":PagesPerMinute", dtint, 0, int_str, 0);
        BREAK_IF (! pVV->m_next, NET_WARN);
        pVV = pVV->m_next;
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":PagesPerMinuteColor", dtint, 0, int_str, 0);
        BREAK_IF (! pVV->m_next, NET_WARN);
        pVV = pVV->m_next;
        snprintf(prnt_name, sizeof(prnt_name), "%s (%s series)", hostname, pdt_name);
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":PrinterName", dtchar, 0, prnt_name, 0);
        BREAK_IF (! pVV->m_next, NET_WARN);

        pVV = pVV->m_next;
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":PrinterInfo", dtchar, 0, " ", 0);
        BREAK_IF (! pVV->m_next, NET_WARN);
#if 0
        pVV = pVV->m_next;
        pVV->m_next = soap_create_var(WSD_PRNT_XMLNS ":PrinterLocation", dtchar, 0, location, 0);
        BREAK_IF (! pVV->m_next, NET_WARN);
#endif
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *out_ppvar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static int WSDprinterDefaultTicketInfoVar(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S **ppvar)
{
    SOAP_VAR_S *pv, *pvv;
    int rc = -1;

    *ppvar = NULL;
    do   // TRY
    {
        pv = soap_create_var(
                "wprt:DefaultPrintTicket",
                dtchar,
                0,
                NULL,
                0);
        BREAK_IF(! pv, NET_WARN);
        pv->m_child = soap_create_var("wprt:DocumentProcessing", dtchar, 0, "", 0);
        BREAK_IF (! pv->m_child, NET_WARN);

        pvv = pv->m_child;
        pvv->m_next = soap_create_var("wprt:JobDescription", dtchar, 0, "", 0);
        BREAK_IF(! pvv->m_next, NET_WARN);
        pvv = pvv->m_next;
        pvv->m_next = soap_create_var("wprt:JobProcessing", dtchar, 0, "", 0);
        BREAK_IF(! pvv->m_next, NET_WARN);
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
           soap_delete_var(pv);
        }
    }
    else
    {
        *ppvar = pv;
    }
    return rc;
}

static int32_t wsd_print_get_default_ticket_element(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S **ppVar)
{
    SOAP_VAR_S *pVar, *pVV;
    int rc = -11;

    *ppVar = NULL;

    do  // try
    {
        pVar = soap_create_var(
                "wprt:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wprt:DefaultPrintTicket",
                "Valid", "true"
                );
        BREAK_IF( ! pVar, NET_WARN);

        rc = WSDprinterDefaultTicketInfoVar(srv_data, &pVV);
        BREAK_IF(rc, NET_WARN);
        BREAK_IF( ! pVV, NET_WARN);
        pVar->m_child = pVV;
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *ppVar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

int WSDprinterCapabilitiesInfoVar(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S** ppVar)
{
    SOAP_VAR_S *pv, *pvv, *pxx;
    int rc;

    *ppVar = NULL;
    rc = 1;
    do   // TRY
    {
        pv = soap_create_var(
                "wprt:PrinterCapabilities",
                dtchar,
                0,
                NULL,
                0);
        if (! pv)
        {
            rc = -1;
            break;
        }
        pv->m_child = soap_create_var("wprt:JobValues", dtchar, 0, "", 0);
        if (! pv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pxx = pv->m_child;
        pvv->m_child = soap_create_var("wprt:JobProcessing", dtchar, 0, "", 0);
        if (! pvv->m_child)
        {
            rc = -1;
            break;
        }
        pvv = pvv->m_child;
        pvv->m_next = soap_create_var("wprt:DocumentProcessing", dtchar, 0, "", 0);
        if (! pvv->m_next)
        {
            rc = -1;
            break;
        }
        pxx->m_next = soap_create_var("wprt:DocumentValues", dtchar, 0, "", 0);
        if (! pxx->m_next)
        {
            rc = -1;
            break;
        }
        rc = 0;
    }
    while(0); // CATCH

    if (rc)
    {
        if (pv)
        {
            soap_delete_var(pv);
        }
    }
    else
    {
        *ppVar = pv;
    }
    return rc;
}

static int32_t wsd_print_get_printer_capabilities(WSD_SERVICE_DATA_S* srv_data, SOAP_VAR_S** ppVar)
{
    SOAP_VAR_S *pVar, *pVV;
    int rc = -11;

    *ppVar = NULL;

    do  // try
    {
        pVar = soap_create_var(
                "wprt:ElementData",
                dtchar,
                0,
                "",
                2,
                "Name", "wprt:PrinterCapabilities",
                "Valid", "true"
                );
        BREAK_IF(! pVar, NET_WARN);

        rc = WSDprinterCapabilitiesInfoVar(srv_data, &pVV);
        BREAK_IF(rc, NET_WARN);

        if (! pVV)
        {
            rc = -1;
            break;
        }
        pVar->m_child = pVV;
        rc = 0;
    }
    while (0); // catch

    if (! rc)
    {
        *ppVar = pVar;
    }
    else if (pVar)
    {
        soap_delete_var(pVar);
    }
    return rc;
}

static ACTION_HANDLER_DEFINITION(wsd_print_get_printer_elements_handler)
{
    QXML_S xml;
    SOAP_VAR_S* pResponse, *pVar;
    char *pe, *pv;
    char  req[128];
    int   rc, parseLevel;
    int   requests;
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    NET_DEBUG("Handling GetprinterElementsRequest !");

    // parse forward to requested elements
    //
    rc = soap_find_var_in_xml(pxml, "GetPrinterElementsRequest.RequestedElements");
    if (rc)
    {
        NET_WARN("No requested elements\n");
        return rc;
    }
    QXMLparserSyncTo(pxml, &xml);
    rc = 0;

    pResponse = NULL;
    pVar = NULL;

    do  // TRY
    {
        // parse the requested elements, and add replies for
        // each element in the request
        //
        pe = QXMLchildElement(pxml);
        parseLevel = pxml->m_level;
        requests = 0;

        while (pe && parseLevel <= pxml->m_level && rc == 0)
        {
            if (! QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Name"))
            {
                pv = QXMLnextValue(pxml);
                if (! pv)
                {
                    NET_WARN("No value for Name\n");
                    rc = -3;
                    break;
                }
                requests++;
                QXMLvalueCopy(pxml, req, pv, sizeof(req));

                pVar = NULL;

                NET_DEBUG("GetPrinterElement: %s\n", QXMLelementName(pxml, req));

                if (! strcmp(QXMLelementName(pxml, req), "PrinterDescription"))
                {
                    rc = wsd_print_get_description_element(&pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "PrinterConfiguration"))
                {
                    rc = wsd_print_get_configuration_element(priv, &pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "PrinterStatus"))
                {
                    rc = wsd_print_get_status_element(srv_data, &pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "DefaultPrintTicket"))
                {
                    rc = wsd_print_get_default_ticket_element(srv_data, &pVar);
                }
                else if (pi_strcmp(QXMLelementName(pxml, req), "PrinterCapabilities") == 0)
                {
                    rc = wsd_print_get_printer_capabilities(srv_data, &pVar);
                }
                else if (! strcmp(QXMLelementName(pxml, req), "VendorSection"))
                {
                    pVar = soap_create_var(
                            "wprt:ElementData",
                            dtchar,
                            0,
                            "",
                            2,
                            "Name", "wprt:VendorSection",
                            "Valid", "true"
                            );
                    if (! pVar)
                    {
                        NET_ERROR("Can't create element data\n");
                        rc = -1;
                    }
                    rc = 0;
                }
                else
                {
                    NET_DEBUG("Unhandled printerElement %s\n", QXMLelementName(pxml, req));

                    pVar = soap_create_var(
                            "wprt:ElementData",
                            dtchar,
                            0,
                            NULL,
                            2,
                            "Name", req,
                            "Valid", "false"
                            );
                }
                if (pVar)
                {
                    if (pResponse && pResponse->m_child)
                    {
                        SOAP_VAR_S* pvx;

                        // enlist the new element on the reply list
                        // as sibling of last element
                        //
                        for (pvx = pResponse->m_child; pvx->m_next;)
                        {
                            pvx = pvx->m_next;
                        }
                        pvx->m_next = pVar;
                    }
                    else
                    {
                        if (pResponse)
                        {
                            // can't happen, but...
                            soap_delete_var(pResponse);
                        }
                        // create the element list and set element as first child
                        //
                        pResponse = soap_create_var(
                                "wprt:PrinterElements",
                                dtchar,
                                0,
                                "",
                                0
                                );
                        if (! pResponse)
                        {
                            soap_delete_var(pVar);
                            rc = -1;
                            break;
                        }
                        pResponse->m_child = pVar;
                    }
                }
            }
            pe = QXMLnextElement(pxml);
        }
        if (rc)
        {
            break;
        }
        if (requests <= 0)
        {
            NET_WARN("No elements in requested elements\n");
            rc = -3;
            break;
        }
        // format response
        //
        rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "GetPrinterElementsResponse",
                        srv_data->soap_header,
                        "wprt",
                        "http://schemas.microsoft.com/windows/2006/08/wdp/print",
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
        if (rc)
        {
            NET_WARN("Can't format SOAP\n");
        }
    }
    while (0); // CATCH

    if(pResponse)
    {
        soap_delete_var(pResponse);
    }
    return rc;
}

static ACTION_HANDLER_DEFINITION(wsd_print_create_job_handler)
{
    WSD_TICKET_S* pticket = NULL;
    WSD_PRNT_TICKET_S* prnt_ticket;
    SOAP_VAR_S *pResponse = NULL;
    int rc = -1;
    QXML_S xml;
    const char *fault_code   = WSD_SOAP_RECEIVER;
    const char *fault_sucode = "OperationFailed";
    const char *fault_reason = "Busy";
    SOAP_CHILD_ELEM_S child_elems[2];

    NET_DEBUG("Handling CreatePrintJobRequest");

    do  // TRY
    {
        rc = soap_find_var_in_xml(pxml, "PrintTicket");
        if (rc)
        {
            NET_WARN("No find %s !!!", "PrintTicket");
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = WSD_PRNT_XMLNS ":InvalidArgs";
            fault_reason = "Not find PrintTicket";
            break;
        }

        QXMLparserSyncTo(pxml, &xml);
        prnt_ticket = PrintTicketCreate();
        pticket = (WSD_TICKET_S*)prnt_ticket;
        if ( pticket == NULL || wsd_job_register(&s_print_service, pticket) )
        {
            NET_WARN("Can't create print ticket\n");
            fault_code   = WSD_SOAP_RECEIVER;
            fault_sucode = WSD_PRNT_XMLNS ":OperationFailed";
            fault_reason = "Busy";
            break;
        }

        SOAP_CHILD_ELEM_RESET( child_elems[0], "JobName", pticket->jobName, sizeof(pticket->jobName), dtstring );
        SOAP_CHILD_ELEM_RESET( child_elems[1], "JobOriginatingUserName", pticket->jobOriginator, sizeof(pticket->jobOriginator), dtstring );
        rc = soap_find_child_elements(&xml, "JobDescription", child_elems, 2);
        if (rc)
        {
            NET_DEBUG("No job JobDescription");
            rc = 0;
        }
        pResponse = soap_create_var("wprt:JobId", dtint, 0, pticket->jobIdString, 0);
        if (! pResponse)
        {
            rc = -1;
            break;
        }
        // format response
        //
        rc = soap_format_response(
                        pResponse,
                        WSD_NAME_SPACES,
                        "CreatePrintJobResponse",
                        srv_data->soap_header,
                        WSD_PRNT_XMLNS,
                        WSD_PRNT_XMLNS_URL,
                        srv_data->soap_reply,
                        sizeof(srv_data->soap_reply)
                        );
    }
    while(0); // CATCH

	if(pResponse)
    {
        soap_delete_var(pResponse);
    }

    if (rc)
    {
        if (pticket)
        {
            PrintTicketDestroy(pticket);
        }

        rc = 0;
        if ( WSDcreateFaultResponse(srv_data, fault_code, WSD_PRNT_XMLNS, fault_sucode, fault_reason) )
        {
            NET_WARN("And can't even format a response\n");
            rc = -1;
        }
    }
    else
    {
        NET_DEBUG("CreateScanJobRequest Success !!!");
    }
    return rc;
}

static ACTION_HANDLER_DEFINITION(wsd_print_send_document_handler)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);

    WSD_TICKET_S* pticket;
    WSD_PRNT_TICKET_S* prnt_ticket;
    SOAP_VAR_S *pResponse = NULL;
    char buf[64] = { 0 };
    int32_t  job_id;
    int rc = -1;
    QXML_S xml;
    const char *fault_code   = WSD_SOAP_RECEIVER;
    const char *fault_sucode = "OperationFailed";
    const char *fault_reason = "Busy";
    JOB_REQUEST_S*  pjobreq = NULL;
    ROUTER_MSG_S    sendmsg;
    QIO_S*          pqio;
    char            *p, *end_p;
    int nboundary_end;

    NET_DEBUG("Handling SendDocument request");
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    nboundary_end = priv->boundary_len + 4;  // \r\n boundary --

    // validate data, find data boundary
    end_p = priv->iobuf + priv->received;
    p = memmem(priv->iobuf, priv->received, priv->boundary, priv->boundary_len);
    RETURN_VAL_IF(p == NULL, NET_WARN, -1);
    p += priv->boundary_len;
    p = memmem(p, end_p - p, priv->boundary, priv->boundary_len);
    p += priv->boundary_len;
    RETURN_VAL_IF(p == NULL, NET_WARN, -1);

    p = memmem(p, end_p - p, BOUNDARY_CONTENT_ID, LEN(BOUNDARY_CONTENT_ID));
    RETURN_VAL_IF(p == NULL, NET_WARN, -1);
    p += LEN(BOUNDARY_CONTENT_ID);

    p = memmem(p, end_p - p, CRLF2, LEN(CRLF2));
    RETURN_VAL_IF(p == NULL, NET_WARN, -1);
    p += LEN(CRLF2);
    RETURN_VAL_IF(end_p - p < nboundary_end, NET_WARN, -1);
    // validate data end

    priv->send_document_parsed = 1;

    do  // TRY
    {
        rc = soap_get_var_value_from_xml(pxml, "SendDocumentRequest.JobId", buf, sizeof(buf));
        if (rc || wsd_strtol(buf, &job_id))
        {
            NET_WARN("invalid job ID; %s", buf);
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "InvalidArgs";
            fault_reason = "Jod ID format is not correct";
            rc = -1;
            break;
        }
        rc = -1;
        pticket = wsd_ticket_get_from_job_id(&s_print_service, job_id);
        if (pticket == NULL)
        {
            fault_code = WSD_SOAP_SENDER;
            fault_sucode = "ClientErrorJobIdNotFound";
            fault_reason = "InvalidArgs";
            break;
        }
        srv_data->pticket = pticket;
        prnt_ticket = (WSD_PRNT_TICKET_S*)pticket;
        ++prnt_ticket->ndocuments;

#ifdef WSD_DEBUG
        if ( (priv->fp = fopen("/backup/wsd_file.data", "wb")) == NULL)
        {
            NET_WARN("open error!");
            break;
        }
#endif

        pqio = wsd_prnt_qio_create();
        BREAK_IF(pqio == NULL, NET_WARN);

        pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
        BREAK_IF(pjobreq == NULL, NET_WARN);

        pjobreq->io_class = IO_CLASS_PRINT;
        pjobreq->io_via   = IO_VIA_NET;
        pjobreq->notify   = job_start_notify;
        pjobreq->context  = pticket;
        pjobreq->pqio     = pqio;

        sendmsg.msgType   = MSG_CTRL_JOB_REQUEST;
        sendmsg.msg1      = pticket->job_id;
        sendmsg.msg2      = 0;
        sendmsg.msg3      = pjobreq;
        sendmsg.msgSender = MID_PORT_NET;

        priv->pqio        = wsd_prnt_qio_ref(pqio);
        priv->ntotal_written += wsd_rbuf_write_all( ((WSD_PRNT_QIO_CTX_S*)pqio->priv)->ringbuf, p, end_p - p - nboundary_end );
#ifdef WSD_DEBUG
        fwrite(p, 1, end_p - p - nboundary_end, priv->fp);
#endif
        memmove(priv->iobuf, end_p - nboundary_end, nboundary_end);
        priv->received = nboundary_end;

        task_msg_send_by_router(MID_SYS_JOB_MGR, &sendmsg);
        rc = 0;
    }
    while(0);

    if (rc)
    {
        if (pqio)
        {
            wsd_prnt_qio_unref(pqio);
        }

        if ( WSDcreateFaultResponse(srv_data, fault_code, WSD_PRNT_XMLNS, fault_sucode, fault_reason) )
        {
            NET_WARN("And can't even format a response\n");
            rc = -1;
        }
        rc = 0;
    }
    else
    {
        srv_data->action_success = 1;
        srv_data->http_finished = 1;   // no need to send back to client right now, will do it in process_send_document_request
        NET_DEBUG("SendDocumentRequest Success !!!");
    }
    return rc;
}

static int32_t wsd_printer_process_send_document_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    WSD_PRNT_QIO_CTX_S* qio_ctx;
    WSD_SERVICE_DATA_S* srv_data = &priv->service_data;
    WSD_PRNT_TICKET_S* prnt_ticket;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    NET_DEBUG("process send document request!");

    if (!priv->send_document_parsed)
    {
        wsd_process_service(&s_print_service,  ptask, priv->iobuf, srv_data);
    }

    RETURN_VAL_IF(priv->pqio == NULL || priv->pqio->priv == NULL, NET_WARN, -1);

    qio_ctx = priv->pqio->priv;
    qio_ctx->recv_finished = 1;

#ifdef WSD_DEBUG
    if (priv->fp)
    {
        fclose(priv->fp);
    }
#endif
    priv->iobuf[priv->received] = '\0';
    NET_DEBUG("send document %u bytes total written data, final not written data: %s", priv->ntotal_written, priv->iobuf);

    if ( (prnt_ticket = (WSD_PRNT_TICKET_S*)priv->service_data.pticket) )
    {
        prnt_ticket->bytes_processed += priv->ntotal_written;
    }

    // when action failed, had been replyed in wsd_process_service
    if (srv_data->action_success)
    {
        soap_format_response(
                NULL,
                WSD_NAME_SPACES,
                "SendDocumentResponse",
                srv_data->soap_header,
                WSD_PRNT_XMLNS,
                WSD_PRNT_XMLNS_URL,
                srv_data->soap_reply,
                sizeof(srv_data->soap_reply)
                );
        wsd_reply_with_soap_content(ptask, srv_data);
    }
    wsd_prnt_qio_unref(priv->pqio);

    return 0;
}

static int32_t wsd_printer_process_send_document_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    WSD_SERVICE_DATA_S* srv_data;
    QIO_S *pqio;
    WSD_PRNT_QIO_CTX_S* qio_ctx;
    int nboundary_end;
    int ret = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    nboundary_end = priv->boundary_len + 4;  // \r\n boundary --
    srv_data = &priv->service_data;

    do {
        if ( priv->send_document_parsed )
        {
            pqio = priv->pqio;
            BREAK_IF ( !pqio || !srv_data, NET_WARN );

            qio_ctx = pqio->priv;
            BREAK_IF (qio_ctx == NULL || qio_ctx->mark_closed, NET_WARN );     // print job start failed

            memcpy(priv->iobuf + priv->received, data, ndata);
            priv->received += ndata;

            if (priv->received > nboundary_end)
            {
                priv->ntotal_written += wsd_rbuf_write_all(qio_ctx->ringbuf, priv->iobuf, priv->received - nboundary_end);
#ifdef WSD_DEBUG
                fwrite(priv->iobuf, 1, priv->received - nboundary_end, priv->fp);
#endif
                memmove(priv->iobuf, priv->iobuf + priv->received - nboundary_end, nboundary_end);
                priv->received = nboundary_end;
            }
            ret = 0;
            break;
        }

        if ( priv->received + ndata > sizeof(priv->iobuf) )
        {
            NET_WARN("request body overlength(%u + %u) from client(%u->%u)", priv->received, ndata, ptask->r_port, ptask->l_port);
            break;
        }

        memcpy(priv->iobuf + priv->received, data, ndata);
        priv->received += ndata;

        if ( priv->received > SEND_DOCUMENT_HEADER_LEN )
        {
            wsd_process_service(&s_print_service,  ptask, priv->iobuf, srv_data);
            priv->send_document_parsed = 1;
            BREAK_IF (!srv_data->action_success, NET_WARN);
        }
        ret = 0;
    }
    while (0);

    if (ret < 0 )
    {
        if (priv->pqio)
        {
            wsd_prnt_qio_unref(priv->pqio);
        }
        wsd_reply_with_fault(ptask, srv_data, WSD_SOAP_RECEIVER, WSD_PRNT_XMLNS, "OperationFailed", "Busy");
        return -1;
    }

    return 0;
}

static int32_t wsd_printer_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        NET_WARN("request body overlength(%u + %u) from client(%u->%u)", priv->received, ndata, ptask->r_port, ptask->l_port);
        return -1;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;

    return 0;
}

static int32_t wsd_printer_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    WSD_SERVICE_DATA_S* srv_data = &priv->service_data;
    char hostname[HOSTNAME_LEN];

    RETURN_VAL_IF(netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_prnt_ctx)) == 0, NET_WARN, -1);
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsd_prnt_ctx), hostname, sizeof(hostname));
    snprintf(srv_data->service_url, sizeof(srv_data->service_url),
            "http://%s:%u/%u", hostname, ptask->l_port, ptask->l_port);

    return wsd_process_service(&s_print_service, ptask, priv->iobuf, srv_data);
}

static int32_t wsd_printer_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    char desired_url[8];
    const char* content_type;
    char        *p, *boundary;

    RETURN_VAL_IF(netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_prnt_ctx)) == 0, NET_WARN, -1);
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    //reserve pticket;
    memset(priv, 0, sizeof(PRIV_INFO_S));

    snprintf(desired_url, sizeof(desired_url), "/%u", ptask->l_port);

    if (strcmp(url, desired_url))
    {
        NET_WARN("desired url: %s, but receive url: %s, discard", desired_url, url);
        return -1;
    }
    content_type = http_task_search_header_field(ptask, HEADER_CONTENT_TYPE);
    RETURN_VAL_IF(STRING_IS_EMPTY(content_type), NET_WARN, -1);

    p = strcasestr(content_type, MULTIPART_RELATED);
    if (p && (boundary = strstr(p, MULTIPART_BOUNDARY)))
    {
        priv->boundary[0] = '-';
        priv->boundary[1] = '-';
        priv->boundary_len = snprintf( priv->boundary+2, sizeof(priv->boundary) - 2, "%s", boundary+LEN(MULTIPART_BOUNDARY) ) + 2;
        p = strchr(priv->boundary, ';');
        if (p)
        {
            *p = '\0';
            priv->boundary_len = p - priv->boundary;
        }
        RETURN_VAL_IF(priv->boundary_len >= sizeof(priv->boundary), NET_WARN, -1);   // snprintf truncated

        priv->service_data.pticket = NULL;
        ptask->reqbody_received_callback = wsd_printer_process_send_document_reqbody;
        ptask->request_complete_callback = wsd_printer_process_send_document_request;
    }
    else
    {
        ptask->reqbody_received_callback = wsd_printer_process_reqbody;
    }

    return 0;
}

static int32_t wsd_printer_construct(HTTP_TASK_S* ptask)
{
    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[0] == NULL )
    {
        ptask->priv_subclass[0] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[0] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = wsd_printer_process_headers;
    ptask->request_complete_callback = wsd_printer_process_request;

    return 0;
}

static void wsd_printer_destruct(HTTP_TASK_S* ptask)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    if ( priv )
    {
        pi_free(priv);
        ptask->priv_subclass[0] = NULL;
    }
    return;
}


static void wsd_printer_connection(NET_CONN_S* pnc)
{
    HTTP_TASK_S*    ptask;
    QIO_S*          pqtcp;

    MUTEX_LOCK(s_wsd_prnt_ctx->mutex, {
            pqtcp = qio_tcp_create(pnc->sockfd, NETQIO_SOCK_NOCLOSE);
            if ( pqtcp != NULL )
            {
                ptask = http_task_create(pqtcp, pnc->remote_port, pnc->local_port);
                if ( wsd_printer_construct(ptask) == 0 )
                {
                    http_task_process(ptask);
                }
                wsd_printer_destruct(ptask);
                http_task_destroy(ptask);
                QIO_CLOSE(pqtcp);
            }
            netsock_close_connection(pnc);
    })
}

static void* wsd_printer_thread(void *arg)
{
    wsd_tcp_loop(s_wsd_prnt_ctx->efd, THREADS_POOL_OF(s_wsd_prnt_ctx->wsd_ctx), wsd_printer_get_port, (THREAD_TASK_FUNC)wsd_printer_connection);
    return NULL;
}

static void* wsd_printer_evt_thread(void *arg)
{
    SOAP_VAR_S *pv, *px;
    static char* state_str = "";
    static char* reason_str = "None";
    while(1)
    {
        pi_msleep(1000 * s_print_service.event_rate);
        if ( wsd_subs_list_is_empty(s_print_service.subs_list) )
        {
            continue;
        }

        if ( strcmp(state_str, (char*)s_wsd_prnt_ctx->state_str) || strcmp(reason_str, (char*)s_wsd_prnt_ctx->reason_str) )
        {
            NET_DEBUG("printer state(%s), reason(%s) change to state(%s), reason(%s)",
                    state_str, reason_str, s_wsd_prnt_ctx->state_str, s_wsd_prnt_ctx->reason_str);
            state_str  = (char*)s_wsd_prnt_ctx->state_str;
            reason_str = (char*)s_wsd_prnt_ctx->reason_str;

            pv = soap_create_var("wprt:StatusSummary", dtchar, 0, NULL, 0);
            if (! pv)
            {
                continue;
            }
            if ( !WSDprinterStateInfoVar(NULL, &px) )
            {
                soap_var_append_child(pv, px);
                wsd_subs_send_event(s_print_service.subs_list, evtPrinterStatus, pv, WSD_PRNT_XMLNS, WSD_PRNT_XMLNS_URL);
            }

            soap_delete_var(pv);
        }

        if (s_wsd_prnt_ctx->flg_JobEndState)
        {
            WSD_TICKET_S* pticket;
            pv = soap_create_var("wprt:JobEndState", dtchar, 0, NULL, 0);
            if (! pv)
            {
                continue;
            }
            pticket = wsd_ticket_get_from_job_id(&s_print_service, s_wsd_prnt_ctx->endstate_job_id);
            if (pticket && !WSDprintJobInfoVar(pticket, 1, 0, &px))
            {
                soap_var_append_child(pv, px);
                wsd_subs_send_event(s_print_service.subs_list, evtJobEndState, pv, WSD_PRNT_XMLNS, WSD_PRNT_XMLNS_URL);
            }
            soap_delete_var(pv);
            s_wsd_prnt_ctx->flg_JobEndState = 0;
            s_wsd_prnt_ctx->endstate_job_id = 0;
        }
    }
    return NULL;
}

static ACTION_HANDLER_DEFINITION(wsd_print_add_document_handler)
{
    int rc;

    // format response
    rc = soap_format_fault(
                WSD_NAME_SPACES,
                srv_data->soap_header,
                WSD_SOAP_RECEIVER,
                WSD_PRNT_XMLNS,
                "ServerErrorAddDocumentNotSupported",
                "Print Service does not support the AddDocument operation",
                srv_data->soap_reply,
                sizeof(srv_data->soap_reply)
                );
    if (rc)
    {
        NET_ERROR("And can't even format a response\n");
    }
    return rc;
}

static void wsd_printer_epilog(void)
{
    if ( s_wsd_prnt_ctx != NULL )
    {
        if (s_wsd_prnt_ctx->net_tid != INVALIDTHREAD)
        {
            pi_thread_destroy(s_wsd_prnt_ctx->net_tid);
        }
        if (s_wsd_prnt_ctx->evt_tid != INVALIDTHREAD)
        {
            pi_thread_destroy(s_wsd_prnt_ctx->evt_tid);
        }
        if ( s_wsd_prnt_ctx->mutex != INVALIDMTX )
        {
            pi_mutex_destroy(s_wsd_prnt_ctx->mutex);
        }
        if (s_wsd_prnt_ctx->efd)
        {
            pi_close(s_wsd_prnt_ctx->efd);
        }
        if (s_print_service.subs_list)
        {
            wsd_subs_list_destroy(s_print_service.subs_list);
        }
        if (s_print_service.job_pool)
        {
            wsd_job_pool_destroy(s_print_service.job_pool);
        }

        pi_free(s_wsd_prnt_ctx);
        s_wsd_prnt_ctx = NULL;
    }
}

static void wsd_update_sysjob_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    JOBINFO_S* jobinfo;
    WSD_TICKET_S* pticket;
    WSD_PRNT_TICKET_S* prnt_ticket;

    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    jobinfo = s->subject_data;
    RETURN_IF(jobinfo == NULL, NET_WARN);

    if ( jobinfo->io_via == IO_VIA_NET && jobinfo->status == JOB_FINISHED )
    {
        pticket = wsd_ticket_get_from_job_id(&s_print_service, jobinfo->job_id);
        RETURN_IF(pticket == NULL, NET_NONE);

        prnt_ticket = (WSD_PRNT_TICKET_S*)pticket;

        prnt_ticket->page_cnt = jobinfo->current_pages;

        prnt_ticket->finished = 1;
        switch ( jobinfo->status_detail )
        {
            case JOB_CANCELED :
                pticket->state = NETJOB_STATE_CANCELED;
                break;
            case JOB_ABORTED :
                pticket->state = NETJOB_STATE_ABORTED;
                break;
            case JOB_END :
                pticket->state = NETJOB_STATE_DONE;
                break;
            default:
                break;
        }
        s_wsd_prnt_ctx->flg_JobEndState = 1;
        s_wsd_prnt_ctx->endstate_job_id = jobinfo->job_id;
    }
}

static void wsd_update_sysstat_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    NETSTS_PACKET_S  packet = { .count = 0 };
    uint32_t status_id;
    char* state_str;
    char* reason_str;

    if (netsts_take_packet(&packet) == 0)
    {
        // 检测到固件端下载错误时不再继续发送数据
        for ( size_t i = 0; i < packet.count; ++i )
        {
            status_id = packet.array[i].status_id;
            if ( (status_id & STATUS_ID_MODULE_MASK) != STATUS_ID_MODULE_PRINT )
            {
                continue;
            }
            if ( (status_id & STATUS_ID_TYPE_MASK) >= STATUS_ID_TYPE_ERROR )
            {
                state_str = "Stopped";
                NET_WARN("status error id = 0x%x", status_id);
                switch (status_id)
                {
                    case STATUS_E_PRINT_JAM_TRAY_1_SECTION:
                    case STATUS_E_PRINT_JAM_TRAY_2_SECTION:
                    case STATUS_E_PRINT_JAM_TRAY_3_SECTION:
                    case STATUS_E_PRINT_JAM_TRAY_4_SECTION:
                    case STATUS_E_PRINT_JAM_LCT_IN_SECTION:
                    case STATUS_E_PRINT_JAM_LCT_EX_SECTION:
                    case STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION:
                        reason_str = "MediaJam";
                        break;
                    case STATUS_E_PRINT_TRAY_EMPTY_STANDARD_TRAY:
                    case STATUS_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY:
                    case STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1:
                    case STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2:
                    case STATUS_E_PRINT_TRAY_1_PAPER_EMPTY:
                    case STATUS_E_PRINT_TRAY_2_PAPER_EMPTY:
                    case STATUS_E_PRINT_TRAY_3_PAPER_EMPTY:
                    case STATUS_E_PRINT_TRAY_4_PAPER_EMPTY:
                    case STATUS_E_PRINT_LCT_IN_PAPER_EMPTY:
                    case STATUS_E_PRINT_LCT_EX_PAPER_EMPTY:
                    case STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY:
                        reason_str = "MediaEmpty";
                        break;
                    case STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL:
                    case STATUS_E_PRINT_TRAY_RECEIVE_1_FULL:
                    case STATUS_E_PRINT_TRAY_RECEIVE_2_FULL:
                    case STATUS_E_PRINT_TRAY_RECEIVE_3_FULL:
                        reason_str = "OutputAreaFull";
                        break;
                    case STATUS_E_PRINT_FRONT_DOOR_OPEN:
                    case STATUS_E_PRINT_SIDE_DOOR_OPEN:
                    case STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN:
                    case STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN:
                    case STATUS_E_PRINT_LCT_EX_OPEN:
                    case STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN:
                    case STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN:
                    case STATUS_E_PRINT_FNS_TOP_COVER_OPEN:
                    case STATUS_E_PRINT_3RD_TRAY_COVER_OPEN:
                        reason_str = "DoorOpen";
                        break;
                    case STATUS_E_PRINT_Y_TONER_EMPTY:
                    case STATUS_E_PRINT_M_TONER_EMPTY:
                    case STATUS_E_PRINT_C_TONER_EMPTY:
                    case STATUS_E_PRINT_K_TONER_EMPTY:
                    case STATUS_E_PRINT_W_TB_UNINSTALL:
                    case STATUS_E_PRINT_Y_TB_UNINSTALL:
                    case STATUS_E_PRINT_M_TB_UNINSTALL:
                    case STATUS_E_PRINT_C_TB_UNINSTALL:
                    case STATUS_E_PRINT_K_TB_UNINSTALL:
                    case STATUS_E_PRINT_Y_DR_UNINSTALL:
                    case STATUS_E_PRINT_M_DR_UNINSTALL:
                    case STATUS_E_PRINT_C_DR_UNINSTALL:
                    case STATUS_E_PRINT_K_DR_UNINSTALL:
                    case STATUS_E_PRINT_Y_DR_LIFE_STOP:
                    case STATUS_E_PRINT_M_DR_LIFE_STOP:
                    case STATUS_E_PRINT_C_DR_LIFE_STOP:
                    case STATUS_E_PRINT_K_DR_LIFE_STOP:
                    case STATUS_E_PRINT_Y_DV_LIFE_STOP:
                    case STATUS_E_PRINT_M_DV_LIFE_STOP:
                    case STATUS_E_PRINT_C_DV_LIFE_STOP:
                    case STATUS_E_PRINT_K_DV_LIFE_STOP:
                    case STATUS_E_PRINT_Y_DV_UNINSTALL:
                    case STATUS_E_PRINT_M_DV_UNINSTALL:
                    case STATUS_E_PRINT_C_DV_UNINSTALL:
                    case STATUS_E_PRINT_K_DV_UNINSTALL:
                        reason_str = "MarkerFailure";
                        break;
                    default:
                        reason_str = "AttentionRequired";
                }

                break;
            }
            else
            {
                switch (status_id)
                {
                    case STATUS_I_PRINT_WARMING:
                    case STATUS_I_PRINT_PROCESSING:
                    case STATUS_I_PRINT_PRINTING:
                    case STATUS_I_PRINT_CANCELING:
                    case STATUS_I_PRINT_PAUSING:
                    case STATUS_I_PRINT_ACR_CALIBRATION:
                    case STATUS_I_PRINT_WAITING:
                        state_str = "Processing";
                        reason_str = "None";
                        break;
                    default:
                        state_str = "Idle";
                        reason_str = "None";
                }
                continue;
            }
        }
        s_wsd_prnt_ctx->state_str = state_str;
        s_wsd_prnt_ctx->reason_str = reason_str;

    }
}

static int32_t wsd_printer_prolog(WSD_CTX_S* wsd_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_wsd_prnt_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(wsd_ctx == NULL, NET_WARN, -1);

    s_wsd_prnt_ctx = (WSD_PRNT_CTX_S*)pi_zalloc(sizeof(WSD_PRNT_CTX_S));
    RETURN_VAL_IF(s_wsd_prnt_ctx == NULL, NET_WARN, -1);
    s_wsd_prnt_ctx->wsd_ctx = wsd_ctx;

    do
    {
        s_print_service.subs_list = wsd_subs_list_create();
        BREAK_IF(s_print_service.subs_list == NULL, NET_WARN);

        s_print_service.job_pool = wsd_job_pool_create(8);
        BREAK_IF(s_print_service.job_pool== NULL, NET_WARN);

        s_wsd_prnt_ctx->efd = eventfd(0, EFD_NONBLOCK);
        BREAK_IF(s_wsd_prnt_ctx->efd == -1, NET_WARN);
        wsd_ctx->efd_arr[wsd_ctx->efd_cnt++] = s_wsd_prnt_ctx->efd;

        s_wsd_prnt_ctx->mutex = pi_mutex_create();
        BREAK_IF(s_wsd_prnt_ctx->mutex == INVALIDMTX, NET_WARN);

        s_wsd_prnt_ctx->net_tid = pi_thread_create(wsd_printer_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "wsd_printer_net_thread");
        BREAK_IF(s_wsd_prnt_ctx->net_tid == INVALIDTHREAD, NET_WARN);
        s_wsd_prnt_ctx->evt_tid = pi_thread_create(wsd_printer_evt_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "wsd_printer_evt_thread");
        BREAK_IF(s_wsd_prnt_ctx->evt_tid == INVALIDTHREAD, NET_WARN);

        s_wsd_prnt_ctx->state_str = "Idle";
        s_wsd_prnt_ctx->reason_str = "None";
        netctx_add_sysstat_observer(wsd_ctx->net_ctx, wsd_update_sysstat_callback, NULL);
        netctx_add_sysjob_observer(wsd_ctx->net_ctx, wsd_update_sysjob_callback, NULL);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WSD initialize result(%d)", ret);
    if ( ret != 0 )
    {
        wsd_printer_epilog();
    }
    return ret;
}

FUNC_EXPORT(init, wsd_printer_prolog);
FUNC_EXPORT(deinit, wsd_printer_epilog);
