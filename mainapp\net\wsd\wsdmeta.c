/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wsd.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WSD transfer, listening to 5753 port
 */
#include "nettypes.h"
#include "netmodules.h"
#include "http_task.h"
#include "netport.h"
#include "netmisc.h"
#include "netsock.h"
#include "wsdef.h"
#include "wsd.h"
#include "qxml.h"
#include "soap.h"
#include <sys/eventfd.h>

typedef struct wsdconn_task_private
{
    char        iobuf[TCP_CHUNK_SIZE];
    size_t      received;
}
PRIV_INFO_S;

typedef struct wsd_meta_context
{
    WSD_CTX_S*  wsd_ctx;
    PI_THREAD_T dev_tid;
    char        dev_url[128];
    int         efd;
}
WSD_META_CTX_S;

static WSD_META_CTX_S* s_wsd_meta_ctx = NULL;

static uint16_t wsd_meta_get_port()
{
    return netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_meta_ctx)) ? WSD_CONN_PORT : 0;
}

static int32_t wsdconn_process_meta(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    QXML_S* pxml = NULL;
    char    hostname[HOSTNAME_LEN];
    char    mfg_name[MFG_NAME_LEN];
    char    pdt_name[PDT_NAME_LEN];
    char    pdt_sn[32];
    char    fw_ver[32];
    char    uuid[64];
    char    meta_data[4096];
    char    element[64];
    char    action[128];
    char    msg_id[64];
    char*   pdata = meta_data;
    int32_t svc_num = 0;
    int32_t has_err = 1;
    int32_t parse_level;
    int32_t body_level;
    int32_t rlen;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    netdata_get_hostname(DATA_MGR_OF_WSD(s_wsd_meta_ctx), hostname, sizeof(hostname));
    netdata_get_mfg_name(DATA_MGR_OF_WSD(s_wsd_meta_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(DATA_MGR_OF_WSD(s_wsd_meta_ctx), pdt_name, sizeof(pdt_name));
    netdata_get_pdt_sn  (DATA_MGR_OF_WSD(s_wsd_meta_ctx), pdt_sn,   sizeof(pdt_sn));
    netdata_get_fw_ver  (DATA_MGR_OF_WSD(s_wsd_meta_ctx), fw_ver,   sizeof(fw_ver));
    do
    {
        pxml = wsd_qxml_parser(priv->iobuf);
        BREAK_IF(pxml == NULL, NET_WARN);

        SOAP_CHILD_ELEM_S child_elems[] = {
            { "Action", action, sizeof(action), dtstring },
            { "MessageID", msg_id, sizeof(msg_id), dtstring },
        };
        BREAK_IF(soap_find_child_elements(pxml, "Envelope.Header", child_elems, ARRAY_SIZE(child_elems)) != 0, NET_WARN);

        BREAK_IF(strstr(action, "/transfer/Get") == NULL, NET_WARN);

#if CONFIG_PRINT
        pdata += snprintf(pdata, sizeof(meta_data) + meta_data - pdata, SOAP_SERVICEDATA, hostname, WSD_PRN_PORT, WSD_PRN_PORT,
                WSD_PRT_SERVICE_TYPE, ++svc_num, WSD_PRT_COMPATIBLE_ID);
#endif

#if CONFIG_SCAN
        pdata += snprintf(pdata, sizeof(meta_data) + meta_data - pdata, SOAP_SERVICEDATA, hostname, WSD_SCN_PORT, WSD_SCN_PORT,
                WSD_SCN_SERVICE_TYPE, ++svc_num, WSD_SCN_COMPATIBLE_ID);
#endif
        has_err = 0;
    }
    while ( 0 );

    if ( pxml != NULL )
    {
        QXMLparserDelete(pxml);
    }

    if ( has_err == 0 )
    {
        netdata_get_uuid(DATA_MGR_OF_WSD(s_wsd_meta_ctx), uuid, sizeof(uuid));
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), SOAP_METADATA, uuid, msg_id, hostname, pdt_name, fw_ver, pdt_sn, mfg_name, pdt_name, pdt_name, hostname, meta_data);
        *rtype = MIME_TYPE_SOAP_XML;
        *rcode = http_status_string(200);
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "<html><body><h2>Dont look here</h2></body></html>");
        *rtype = MIME_TYPE_HTML;
        *rcode = http_status_string(501);
    }
    return rlen;
}

static int32_t wsdconn_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_meta_ctx)) == 0, NET_WARN, -1);
    RETURN_VAL_IF(strcmp(s_wsd_meta_ctx->dev_url, url) != 0, NET_DEBUG, -1);
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    priv->received = 0;

    return 0;
}

static int32_t wsdconn_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        NET_WARN("request body overlength(%u + %u) from client(%u->%u)", priv->received, ndata, ptask->r_port, ptask->l_port);
        return -1;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;

    return 0;
}

static int32_t wsdconn_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    const char* reply_type = "text/xml";
    const char* reply_code = "200 OK";
    int32_t     reply_len = 0;

    RETURN_VAL_IF(netdata_get_wsd_switch(DATA_MGR_OF_WSD(s_wsd_meta_ctx)) == 0, NET_WARN, -1);
    RETURN_VAL_IF(strcmp(s_wsd_meta_ctx->dev_url, url) != 0, NET_DEBUG, -1);
    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    priv->iobuf[priv->received] = '\0';

    NET_DEBUG("method(%s) url(%s) body received(%u) from client(%u->%u)", http_method_str(method), url, priv->received, ptask->r_port, ptask->l_port);
    reply_len = wsdconn_process_meta(ptask, &reply_code, &reply_type);
    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        http_task_send(ptask, priv->iobuf, reply_len);
    }

    return 0;
}

static int32_t wsdconn_construct(HTTP_TASK_S* ptask)
{
    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[0] == NULL )
    {
        ptask->priv_subclass[0] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[0] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = wsdconn_process_headers;
    ptask->reqbody_received_callback = wsdconn_process_reqbody;
    ptask->request_complete_callback = wsdconn_process_request;

    return 0;
}

static void wsdconn_destruct(HTTP_TASK_S* ptask)
{
    DECL_PRIV0(ptask, PRIV_INFO_S, priv);
    if ( priv )
    {
        pi_free(priv);
        ptask->priv_subclass[0] = NULL;
    }
    return;
}

static void wsd_connection(NET_CONN_S* pnc)
{
    HTTP_TASK_S*    ptask;
    QIO_S*          pqtcp;

    pqtcp = qio_tcp_create(pnc->sockfd, NETQIO_SOCK_NOCLOSE);
    if ( pqtcp != NULL )
    {
        ptask = http_task_create(pqtcp, pnc->remote_port, pnc->local_port);
        if ( wsdconn_construct(ptask) == 0 )
        {
            http_task_process(ptask);
        }
        wsdconn_destruct(ptask);
        http_task_destroy(ptask);
        QIO_CLOSE(pqtcp);
    }
    netsock_close_connection(pnc);
}

static void* wsd_thread_handler(void *arg)
{
    wsd_tcp_loop(s_wsd_meta_ctx->efd, THREADS_POOL_OF(s_wsd_meta_ctx->wsd_ctx), wsd_meta_get_port, (THREAD_TASK_FUNC)wsd_connection);
    return NULL;
}

static void wsd_meta_epilog(void)
{
    if ( s_wsd_meta_ctx != NULL )
    {
        if (s_wsd_meta_ctx->dev_tid != INVALIDTHREAD)
        {
            pi_thread_destroy(s_wsd_meta_ctx->dev_tid);
        }
        if (s_wsd_meta_ctx->efd)
        {
            pi_close(s_wsd_meta_ctx->efd);
        }
        pi_free(s_wsd_meta_ctx);
        s_wsd_meta_ctx = NULL;
    }
}

int32_t wsd_meta_prolog(WSD_CTX_S* wsd_ctx)
{
    int32_t ret = -1;
    uint8_t mac[6];

    RETURN_VAL_IF(s_wsd_meta_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(wsd_ctx == NULL, NET_WARN, -1);

    s_wsd_meta_ctx = (WSD_META_CTX_S *)pi_zalloc(sizeof(WSD_META_CTX_S));
    RETURN_VAL_IF(s_wsd_meta_ctx == NULL, NET_WARN, -1);

    do
    {
        s_wsd_meta_ctx->wsd_ctx = wsd_ctx;
        snprintf(s_wsd_meta_ctx->dev_url, sizeof(s_wsd_meta_ctx->dev_url), "/%s", wsd_ctx->default_uuid);
        s_wsd_meta_ctx->efd = eventfd(0, EFD_NONBLOCK);
        BREAK_IF(s_wsd_meta_ctx->efd == -1, NET_WARN);
        wsd_ctx->efd_arr[wsd_ctx->efd_cnt++] = s_wsd_meta_ctx->efd;

        s_wsd_meta_ctx->dev_tid = pi_thread_create(wsd_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "wsd_thread_handler");
        BREAK_IF(s_wsd_meta_ctx->dev_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WSD initialize result(%d)", ret);
    if ( ret != 0 )
    {
        wsd_meta_epilog();
    }
    return ret;
}

FUNC_EXPORT(init, wsd_meta_prolog);
FUNC_EXPORT(deinit, wsd_meta_epilog);
/**
 *@}
 */
