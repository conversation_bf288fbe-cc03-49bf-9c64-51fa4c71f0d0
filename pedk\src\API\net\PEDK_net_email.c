#include <string.h>
#include <stdio.h>
#include "PEDK_net_email.h"
#include "PEDK_event.h"

#include <quickjs.h>

JSValue js_send_email(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    SEND_EMAIL_INFO set_mail_info;
    int respond = -1;
    int receive_data[512];
    int receive_cnt = sizeof(receive_data);

    if( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];
    JSValueConst recipient_email = argv[1];
    JSValueConst send_from = argv[2];
    JSValueConst subject = argv[3];
    JSValueConst body = argv[4];

    JSValueConst host = JS_GetPropertyStr( ctx, obj, "host");
    JSValueConst port = JS_GetPropertyStr( ctx, obj, "port");
    JSValueConst username = JS_GetPropertyStr( ctx, obj, "username");
    JSValueConst password = JS_GetPropertyStr( ctx, obj, "password");
    JSValueConst iden_authen = JS_GetPropertyStr( ctx, obj, "iden_authen");
    JSValueConst type = JS_GetPropertyStr( ctx, obj, "type");

    const char *c_host = JS_ToCString(ctx, host);
    const char *c_username = JS_ToCString(ctx, username);
    const char *c_password = JS_ToCString(ctx, password);
    const char *c_recipient_email = JS_ToCString(ctx, recipient_email);
    const char *c_send_from = JS_ToCString(ctx, send_from);
    const char *c_subject = JS_ToCString(ctx, subject);
    const char *c_body = JS_ToCString(ctx, body);
    strncpy(set_mail_info.smtp_server_info.host, c_host, strlen(c_host));
    set_mail_info.smtp_server_info.host[strlen(c_host)] = '\0';
    strncpy(set_mail_info.smtp_server_info.username, c_username, strlen(c_username));
    set_mail_info.smtp_server_info.username[strlen(c_username)] = '\0';
    strncpy(set_mail_info.smtp_server_info.password, c_password, strlen(c_password));
    set_mail_info.smtp_server_info.password[strlen(c_password)] = '\0';
    strncpy(set_mail_info.recipient_email, c_recipient_email, strlen(c_recipient_email));
    set_mail_info.recipient_email[strlen(c_recipient_email)] = '\0';
    strncpy(set_mail_info.send_from, c_send_from, strlen(c_send_from));
    set_mail_info.send_from[strlen(c_send_from)] = '\0';
    strncpy(set_mail_info.subject, c_subject, strlen(c_subject));
    set_mail_info.subject[strlen(c_subject)] = '\0';
    strncpy(set_mail_info.body, c_body, strlen(c_body));
    set_mail_info.body[strlen(c_body)] = '\0';


    JS_ToInt32(ctx, &set_mail_info.smtp_server_info.port, port);
    JS_ToInt32(ctx, &set_mail_info.smtp_server_info.iden_authen, iden_authen);
    JS_ToInt32(ctx, &set_mail_info.smtp_server_info.type, type);

    JS_FreeCString(ctx, c_host);
    JS_FreeCString(ctx, c_username);
    JS_FreeCString(ctx, c_password);
    JS_FreeCString(ctx, c_recipient_email);
    JS_FreeCString(ctx, c_send_from);
    JS_FreeCString(ctx, c_subject);
    JS_FreeCString(ctx, c_body);
    JS_FreeValue(ctx, obj);
    JS_FreeValue(ctx, recipient_email);
    JS_FreeValue(ctx, send_from);
    JS_FreeValue(ctx, subject);
    JS_FreeValue(ctx, body);
    JS_FreeValue(ctx, host);
    JS_FreeValue(ctx, port);
    JS_FreeValue(ctx, username);
    JS_FreeValue(ctx, password);
    JS_FreeValue(ctx, iden_authen);
    JS_FreeValue(ctx, type);


    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SEND_EMAIL, 0, sizeof(SEND_EMAIL_INFO), &set_mail_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SEND_EMAIL, &respond, NULL, NULL, 3);

    if(respond < 0)
    {
        return JS_NewString(ctx, "send email fail");
    }
    else
    {
        return JS_NewString(ctx, "send email success");
    }
}
