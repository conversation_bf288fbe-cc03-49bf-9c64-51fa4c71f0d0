#ifndef _PEDK_DEVICE_SETTING_PRODUCTINFO_
#define _PEDK_DEVICE_SETTING_PRODUCTINFO_

#include <quickjs.h>

JSValue js_get_product_uuid(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_product_name(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_product_serial_number(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);
JSValue js_get_product_position(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

int js_device_productinfo_init(JSContext *ctx, JSValueConst global);


#endif /* _PEDK_DEVICE_SETTING_PRODUCTINFO_ */

