#ifndef _IPPJOB_H_
#define _IPPJOB_H_

int32_t ippjob_set_attribute_handle     (int32_t jobid, void* pset);

int32_t ippjob_get_attribute_handle     (int32_t jobid, void** ppset);

int32_t ippjob_init_job_attributes      (IPP_CTX_S* ppp, void* pset);

int32_t ippjob_set_document_properties  (IPP_CTX_S* ppp, void* pset);

int32_t ippjob_set_job_properties       (int32_t jobid);

int32_t ippjob_update_job_state         (int32_t jobid);

typedef enum {
			PrintPDL_Unknown = 0,
			PrintPDL_PDF = 1,
			PrintPDL_PS = 2,
			PrintPDL_PCL = 3,
			PrintPDL_XPS = 4,
			PrintPDL_OAKT = 5,
			PrintPDL_PWG = 6,
			PrintPDL_URF = 7,
			PrintPDL_JFIF = 8 
}
e_PrintPDL;

#endif /* _IPPJOB_H_ */

