/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_ofd.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-17
 * @brief QIO object that creates a PDF file from an image stream
 */
#include "qiox.h"
#include "ofd/zip.h"

/**
 * OFD struct
 * $ROOT
 *   | OFD.xml
 *   | Doc_0
 *       |- Document.xml
 *       |- DocumentRes.xml
 *       |- Pages
 *       |   |- Page_0
 *       |   |   |- Content.xml
 *       |   |- Page_1
 *       |   |   |- Content.xml
 *       |   |- Page_2
 *       |       |- Content.xml
 *       |- Res
 *           |- 0.jpeg
 *           |- 1.jpeg
 *           |- 2.jpeg
 */

#define OFD_HEADER_XML          "<?xml version=\"1.0\" encoding=\"UTF-8\"?>"                                            \
                                "<ofd:OFD xmlns:ofd=\"http://www.ofdspec.org/2016\" Version=\"1.1\" DocType=\"OFD\">"   \
                                "<ofd:DocBody>"                                                                         \
                                "<ofd:DocInfo>"                                                                         \
                                "<ofd:Creator>PantumOFDCreator</ofd:Creator>"                                           \
                                "<ofd:CreatorVersion>1.0.9167</ofd:CreatorVersion>"                                     \
                                "</ofd:DocInfo>"                                                                        \
                                "<ofd:DocRoot>Doc_0/Document.xml</ofd:DocRoot>"                                         \
                                "</ofd:DocBody>"                                                                        \
                                "</ofd:OFD>"

#define OFD_RES_HEADER_XML      "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"                                          \
                                "<ofd:Res xmlns:ofd=\"http://www.ofdspec.org/2016\" BaseLoc=\"Res\">\n"                 \
                                "<ofd:MultiMedias>\n"

#define OFD_RES_MULTIMEDIA_FMT  "   <ofd:MultiMedia ID=\"%d\" Type=\"Image\" Format=\"JPG\"><ofd:MediaFile>%d.jpeg</ofd:MediaFile></ofd:MultiMedia>\n"

#define OFD_RES_TAILER_XML      "</ofd:MultiMedias>\n"                                                                  \
                                "</ofd:Res>"

#define OFD_DOC_HEADER_FMT      "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"                                          \
                                "<ofd:Document xmlns:ofd=\"http://www.ofdspec.org/2016\">\n"                            \
                                "<ofd:CommonData>\n"                                                                    \
                                "<ofd:MaxUnitID>%d</ofd:MaxUnitID>\n"                                                   \
                                "<ofd:PageArea>\n"                                                                      \
                                "<ofd:PhysicalBox>0 0 210 297</ofd:PhysicalBox>\n"                                      \
                                "</ofd:PageArea>\n"                                                                     \
                                "<ofd:DocumentRes>DocumentRes.xml</ofd:DocumentRes>\n"                                  \
                                "</ofd:CommonData>\n"                                                                   \
                                "<ofd:Pages>\n"

#define OFD_DOC_PAGE_FMT        "  <ofd:Page ID=\"%d\" BaseLoc=\"Pages/Page_%d/Content.xml\"/>\n"

#define OFD_DOC_TAILER_XML      "</ofd:Pages>\n"                                                                        \
                                "</ofd:Document>"

#define OFD_CONTENT_XML_FMT     "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"                                          \
                                "<ofd:Page xmlns:ofd=\"http://www.ofdspec.org/2016\">\n"                                \
                                "<ofd:Area>%s</ofd:Area>\n"                                                             \
                                "<ofd:Content>\n"                                                                       \
                                "%s"                                                                                    \
                                "</ofd:Content>\n"                                                                      \
                                "</ofd:Page>"

#define OFD_PHYSICAL_BOX_FMT    "<ofd:PhysicalBox>0 0 %d %d</ofd:PhysicalBox>\n"

#define OFD_LAYER_FMT           "<ofd:Layer ID=\"%d\" Type=\"Body\"><ofd:ImageObject ID=\"%d\" CTM=\"%d 0 0 %d 0 0\" Boundary=\"0 0 %d %d\" ResourceID=\"%d\"/></ofd:Layer>\n"

#define OFD_BUF_SIZE            ( 0x100000 )    /* 1024 * 1024 */
#define OFD_MAX_PAGES           ( 2048 )       /* 2048 */

typedef struct ofd_page
{
    int32_t     resource_id;
    char        pathname[256];
}
OFD_PAGE_S;

typedef struct priv_info_ofd
{
    zipFile     zfile;                          ///< Actual output stream qio
    char        filename[FILE_PATH_MAX];        ///< target filename with path
    int32_t     imgcount;                       ///< total images read in
    int32_t     maxUnitID;                      ///< number of objects
    int32_t     w, h, d, s, f;                  ///< current image dimensions and format
    int32_t     xres, yres;                     ///< the resolution of the image, in DPI, where applicable
    int32_t     clipx, clipy;                   ///< max dimensions of output w/h
    OFD_PAGE_S  pages[OFD_MAX_PAGES];
}
PRIV_INFO_S;

static int32_t write_ofd_header_to_zip(PRIV_INFO_S* priv)
{
    zip_fileinfo    zip_finfo;
    struct tm*      ptm;
    time_t          ctm;
    int32_t         ret;

    time(&ctm);
    ptm = localtime(&ctm);

    zip_finfo.tmz_date.tm_sec  = ptm->tm_sec;
    zip_finfo.tmz_date.tm_min  = ptm->tm_min;
    zip_finfo.tmz_date.tm_hour = ptm->tm_hour;
    zip_finfo.tmz_date.tm_mday = ptm->tm_mday;
    zip_finfo.tmz_date.tm_mon  = ptm->tm_mon;
    zip_finfo.tmz_date.tm_year = 1900 + ptm->tm_year;
    zip_finfo.dosDate          = 0;
    zip_finfo.internal_fa      = 0;
    zip_finfo.external_fa      = 0;

    ret = zipOpenNewFileInZip(priv->zfile, "OFD.xml", &zip_finfo, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipOpenNewFileInZip OFD.xml failed: %d", ret);
        return -1;
    }

    ret = zipWriteInFileInZip(priv->zfile, OFD_HEADER_XML, strlen(OFD_HEADER_XML));
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipWriteInFileInZip OFD_HEADER_XML failed: %d", ret);
    }

    ret = zipCloseFileInZip(priv->zfile);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipCloseFileInZip OFD.xml failed: %d", ret);
        return -1;
    }

    return 0;
}

static int32_t write_ofd_res_to_zip(PRIV_INFO_S* priv)
{
    zip_fileinfo    zip_finfo;
    struct tm*      ptm;
    time_t          ctm;
    char            buf[256];
    int32_t         ret;
    int32_t         i;

    time(&ctm);
    ptm = localtime(&ctm);
    zip_finfo.tmz_date.tm_sec  = ptm->tm_sec;
    zip_finfo.tmz_date.tm_min  = ptm->tm_min;
    zip_finfo.tmz_date.tm_hour = ptm->tm_hour;
    zip_finfo.tmz_date.tm_mday = ptm->tm_mday;
    zip_finfo.tmz_date.tm_mon  = ptm->tm_mon;
    zip_finfo.tmz_date.tm_year = ptm->tm_year + 1900;
    zip_finfo.dosDate          = 0;
    zip_finfo.internal_fa      = 0;
    zip_finfo.external_fa      = 0;

    ret = zipOpenNewFileInZip(priv->zfile, "Doc_0/DocumentRes.xml", &zip_finfo, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipOpenNewFileInZip Doc_0/DocumentRes.xml failed: %d", ret);
        return -1;
    }

    do
    {
        ret = zipWriteInFileInZip(priv->zfile, OFD_RES_HEADER_XML, strlen(OFD_RES_HEADER_XML));
        if ( ret != ZIP_OK )
        {
            QIO_WARN("zipWriteInFileInZip OFD_RES_HEADER_XML failed: %d", ret);
            break;
        }

        QIO_DEBUG("imgcount(%d)", priv->imgcount);
        for ( i = 0; i < priv->imgcount; ++i )
        {
            snprintf(buf, sizeof(buf), OFD_RES_MULTIMEDIA_FMT, priv->pages[i].resource_id, i);
            ret = zipWriteInFileInZip(priv->zfile, buf, strlen(buf));
            if ( ret != ZIP_OK )
            {
                QIO_WARN("zipWriteInFileInZip OFD_RES_MULTIMEDIA_FMT(%d) failed: %d", i, ret);
                break;
            }
        }

        if ( ret != ZIP_OK )
        {
            break;
        }

        ret = zipWriteInFileInZip(priv->zfile, OFD_RES_TAILER_XML, strlen(OFD_RES_TAILER_XML));
        if ( ret != ZIP_OK )
        {
            QIO_WARN("zipWriteInFileInZip OFD_RES_TAILER_XML failed: %d", ret);
            break;
        }
    }
    while ( 0 );

    ret = zipCloseFileInZip(priv->zfile);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipCloseFileInZip Doc_0/DocumentRes.xml failed: %d", ret);
        return -1;
    }
    return 0;
}

static int32_t write_ofd_doc_to_zip(PRIV_INFO_S* priv)
{
    zip_fileinfo    zip_finfo;
    struct tm*      ptm;
    time_t          ctm;
    char            buf[1024];
    int32_t         ret;
    int32_t         i;

    time(&ctm);
    ptm = localtime(&ctm);
    zip_finfo.tmz_date.tm_sec  = ptm->tm_sec;
    zip_finfo.tmz_date.tm_min  = ptm->tm_min;
    zip_finfo.tmz_date.tm_hour = ptm->tm_hour;
    zip_finfo.tmz_date.tm_mday = ptm->tm_mday;
    zip_finfo.tmz_date.tm_mon  = ptm->tm_mon;
    zip_finfo.tmz_date.tm_year = ptm->tm_year + 1900;
    zip_finfo.dosDate          = 0;
    zip_finfo.internal_fa      = 0;
    zip_finfo.external_fa      = 0;

    ret = zipOpenNewFileInZip(priv->zfile, "Doc_0/Document.xml", &zip_finfo, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipOpenNewFileInZip Doc_0/DocumentRes.xml failed: %d", ret);
        return -1;
    }

    do
    {
        snprintf(buf, sizeof(buf), OFD_DOC_HEADER_FMT, priv->maxUnitID + priv->imgcount);
        ret = zipWriteInFileInZip(priv->zfile, buf, strlen(buf));
        if ( ret != ZIP_OK )
        {
            QIO_WARN("zipWriteInFileInZip OFD_DOC_HEADER_FMT(%d) failed: %d", ret, priv->maxUnitID + priv->imgcount);
            return -1;
        }

        QIO_DEBUG("imgcount(%d)", priv->imgcount);
        for ( i = 0; i < priv->imgcount; ++i )
        {
            snprintf(buf, sizeof(buf), OFD_DOC_PAGE_FMT, priv->maxUnitID++, i);
            ret = zipWriteInFileInZip(priv->zfile, buf, strlen(buf));
            if ( ret != ZIP_OK )
            {
                QIO_WARN("zipWriteInFileInZip OFD_DOC_HEADER_FMT(%d) failed: %d", i, ret);
                return -1;
            }
        }

        if ( ret != ZIP_OK )
        {
            break;
        }

        ret = zipWriteInFileInZip(priv->zfile, OFD_DOC_TAILER_XML, strlen(OFD_DOC_TAILER_XML));
        if ( ret != ZIP_OK )
        {
            QIO_WARN("zipWriteInFileInZip OFD_DOC_TAILER_XML failed: %d", ret);
            return -1;
        }
    }
    while ( 0 );

    ret = zipCloseFileInZip(priv->zfile);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipCloseFileInZip Doc_0/DocumentRes.xml failed: %d", ret);
        return -1;
    }
    return 0;
}

static int32_t qio_ofd_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    return QIOEOF;
}

static int32_t qio_ofd_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    return QIOEOF;
}

static int32_t qio_ofd_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    return QIOEOF;
}

static int32_t qio_ofd_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

static int32_t qio_ofd_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);
    int32_t ret;

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( priv )
    {
        QIO_DEBUG("close OFD<%s> QIO<%p>", priv->filename, pqio);
        if ( priv->zfile )
        {
            if ( write_ofd_res_to_zip(priv) < 0 )
            {
                QIO_WARN("write_ofd_res_to_zip (%s) failed", priv->filename);
            }
            if ( write_ofd_doc_to_zip(priv) < 0 )
            {
                QIO_WARN("write_ofd_doc_to_zip (%s) failed", priv->filename);
            }

            ret = zipClose(priv->zfile, NULL);
            if ( ret != ZIP_OK )
            {
                QIO_WARN("zipClose (%s) failed: %d", priv->filename, ret);
            }
        }
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_ofd_create(const char* stream, int32_t flags)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;

    RETURN_VAL_IF(stream == NULL, QIO_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    priv->zfile = zipOpen(stream, 0);
    if ( priv->zfile == NULL )
    {
        QIO_WARN("zipOpen OFD(%s) failed: %d<%s>", stream, errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }

    write_ofd_header_to_zip(priv);
    snprintf(priv->filename, sizeof(priv->filename), "%s", stream);
    priv->maxUnitID++;

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        QIO_WARN("alloc OFD(%s) failed: %d<%s>", stream, errno, strerror(errno));
        zipClose(priv->zfile, NULL);
        pi_free(priv);
        return NULL;
    }

    QIO_DEBUG("create OFD(%s) QIO<%p>\n", stream, pqio);
    pqio->close = qio_ofd_close;
    pqio->poll  = qio_ofd_poll;
    pqio->read  = qio_ofd_read;
    pqio->write = qio_ofd_write;
    pqio->seek  = qio_ofd_seek;
    pqio->priv  = (void*)priv;

    return pqio;
}

int32_t qio_ofd_begin_image(QIO_S* pqio, int32_t w, int32_t h, int32_t d, int32_t s, int32_t f, int32_t xres, int32_t yres, int32_t clipx, int32_t clipy)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, -1);

    if ( priv->imgcount > 0 )
    {
        RETURN_VAL_IF(priv->imgcount >= OFD_MAX_PAGES, QIO_WARN, -1);
    }

    priv->w     = w;
    priv->h     = h;
    priv->d     = d;
    priv->s     = s;
    priv->f     = f;
    priv->xres  = ((xres > 0) ? xres : 300);
    priv->yres  = ((yres > 0) ? yres : 300);
    priv->clipx = clipx;
    priv->clipy = clipy;

    return 0;
}

int32_t qio_ofd_add_image(QIO_S* pqio, const char* path)
{
    DECL_PRIV(pqio, priv);
    zip_fileinfo    zip_finfo;
    OFD_PAGE_S*     page;
    FILE*           stream = NULL;
    char*           buffer = NULL;
    struct tm*      ptm;
    time_t          ctm;
    size_t          rlen;
    char            tmp_path[FILE_PATH_MAX];
    char            content_xml[2048];
    char            physical_box[256];
    char            layer_xml[512];
    int32_t         w_mm;
    int32_t         h_mm;
    int32_t         ret;

    RETURN_VAL_IF(priv == NULL || path == NULL, QIO_WARN, -1);
    RETURN_VAL_IF(priv->imgcount >= OFD_MAX_PAGES, QIO_WARN, -1);

    time(&ctm);
    ptm = localtime(&ctm);
    zip_finfo.tmz_date.tm_sec  = ptm->tm_sec;
    zip_finfo.tmz_date.tm_min  = ptm->tm_min;
    zip_finfo.tmz_date.tm_hour = ptm->tm_hour;
    zip_finfo.tmz_date.tm_mday = ptm->tm_mday;
    zip_finfo.tmz_date.tm_mon  = ptm->tm_mon;
    zip_finfo.tmz_date.tm_year = 1900 + ptm->tm_year;
    zip_finfo.dosDate          = 0;
    zip_finfo.internal_fa      = 0;
    zip_finfo.external_fa      = 0;

    page = &(priv->pages[priv->imgcount]); // update PageInfo
    snprintf(page->pathname, sizeof(page->pathname), "%s", path);
    page->resource_id = priv->maxUnitID++;

    snprintf(tmp_path, sizeof(tmp_path), "Doc_0/Res/%d.jpeg", priv->imgcount); //Jpeg file copy to zipfile
    QIO_DEBUG("tmp_path(%s)", tmp_path);

    //create jpeg file in zipfile
    ret = zipOpenNewFileInZip(priv->zfile, tmp_path, &zip_finfo, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipOpenNewFileInZip(%s) failed: %d", tmp_path, ret);
        return -1;
    }

    do
    {
        buffer = (char *)pi_zalloc(OFD_BUF_SIZE);
        if ( buffer == NULL )
        {
            QIO_WARN("alloc buffer failed: %d<%s>", errno, strerror(errno));
            break;
        }

        stream = pi_fopen(path, "rb");
        if ( stream == NULL )
        {
            QIO_WARN("open file(%s) failed: %d<%s>", path, errno, strerror(errno));
            break;
        }

        while ( (feof(stream) == 0) && (ferror(stream) == 0) )
        {
            rlen = fread(buffer, 1, OFD_BUF_SIZE, stream);
            if ( rlen > 0 )
            {
                QIO_DEBUG("fread %u bytes from(%s)", rlen, path);
                ret = zipWriteInFileInZip(priv->zfile, buffer, rlen);
                if ( ret != ZIP_OK )
                {
                    QIO_WARN("zipWriteInFileInZip(%s) failed: %d", tmp_path, ret);
                    break;
                }
            }
        }
    }
    while ( 0 );

    if ( stream )
    {
        pi_fclose(stream);
    }

    if ( buffer )
    {
        pi_free(buffer);
    }

    ret = zipCloseFileInZip(priv->zfile);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipCloseFileInZip(%s) failed: %d", tmp_path, ret);
        return -1;
    }

    snprintf(tmp_path, sizeof(tmp_path), "Doc_0/Pages/Page_%d/Content.xml", priv->imgcount);
    QIO_DEBUG("tmp_path(%s)", tmp_path);

    ret = zipOpenNewFileInZip(priv->zfile, tmp_path, &zip_finfo, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipOpenNewFileInZip(%s) failed: %d", tmp_path, ret);
        return -1;
    }

    w_mm = priv->w / priv->xres * 25.4;
    h_mm = priv->h / priv->yres * 25.4;
    snprintf(physical_box, sizeof(physical_box), OFD_PHYSICAL_BOX_FMT, w_mm, h_mm);
    snprintf(layer_xml, sizeof(layer_xml), OFD_LAYER_FMT, priv->maxUnitID + 1, priv->maxUnitID + 2, w_mm, h_mm, w_mm, h_mm, page->resource_id);
    snprintf(content_xml, sizeof(content_xml), OFD_CONTENT_XML_FMT, physical_box, layer_xml);
    priv->maxUnitID += 2;

    ret = zipWriteInFileInZip(priv->zfile, content_xml, strlen(content_xml));
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipWriteInFileInZip(%s) failed: %d", tmp_path, ret);
    }

    ret = zipCloseFileInZip(priv->zfile);
    if ( ret != ZIP_OK )
    {
        QIO_WARN("zipCloseFileInZip(%s) failed: %d", tmp_path, ret);
        return -1;
    }

    priv->imgcount++;

    return 0;
}
/**
 *@}
 */
