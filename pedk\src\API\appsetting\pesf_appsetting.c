#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

#include "cJSON.h"

#include "pesf_appsetting.h"
//#include "pesf_event.h"

#include <quickjs.h>


#define EOPNOTSUPP      "EOPNOTSUPP"
#define EXIT_SUCCESS    "EXIT_SUCCESS"
#define EXIT_FAILURE    "EXIT_FAILURE"
#define ETIMEDOUT       "ETIMEDOUT"
#define EALLOWED        "EALLOWED"
#define ENOTALLOWED     "ENOTALLOWED"

#define PESF_APP_SETTINGS_CFG_FILE  "/pesf_data/app_settings.cfg"

#define countof(x) (sizeof(x) / sizeof((x)[0]))

typedef struct JSCFunctionList
{
    const char *name;
    int param_num;
    JSCFunction *func;
}JSCFunctionList;

// Helper function to read J<PERSON><PERSON> from file
static cJSON *pesf_appsetting_read_json(void) {
    FILE *file = fopen(PESF_APP_SETTINGS_CFG_FILE, "r");
    if (!file) {
        // File doesn't exist, return a new JSON object
        return cJSON_CreateObject();
    }

    fseek(file, 0, SEEK_END);
    long length = ftell(file);
    fseek(file, 0, SEEK_SET);

    if (length <= 0) {
        // File is empty, return a new JSON object
        fclose(file);
        return cJSON_CreateObject();
    }

    char *data = (char *)malloc(length + 1);
    if (!data) {
        fprintf(stderr, "Error: Memory allocation failed\n");
        fclose(file);
        return NULL;
    }

    fread(data, 1, length, file);
    data[length] = '\0';

    fclose(file);

    cJSON *root = cJSON_Parse(data);
    free(data);

    if (!root) {
        fprintf(stderr, "Error: Invalid JSON in file %s\n", PESF_APP_SETTINGS_CFG_FILE);
        return NULL;
    }

    return root;
}


// Helper function to write JSON to file
static int pesf_appsetting_write_json_file(cJSON *root) {
    FILE *file = fopen(PESF_APP_SETTINGS_CFG_FILE, "w");
    if (!file) {
        fprintf(stderr, "Error: Unable to open file %s for writing\n", PESF_APP_SETTINGS_CFG_FILE);
        return -1;
    }

    fprintf(file, "%s", cJSON_PrintUnformatted(root));

    fclose(file);
    return 0;
}

static void pesf_appsetting_read_json_from_file(const char *key, char *value, size_t buff_len) {
    cJSON *root = pesf_appsetting_read_json();
    if(root == NULL)
    {
        printf("-L%d pesf_appsetting_read_json error\n",__LINE__);
        return;
    }

    cJSON *json = cJSON_GetObjectItem(root, key);
    if (json) {
        strncpy(value, json->valuestring,fmin(strlen(json->valuestring),buff_len) + 1);
        printf("**********strlen %d   sizeof %zu\n",strlen(json->valuestring),buff_len);
    } else {
        strcpy(value, "Key not found");
    }

    cJSON_Delete(root);
}

static char* pesf_appsetting_add_json2file(const char *key, const char *value)
{
    cJSON *root = pesf_appsetting_read_json();
    if(root == NULL)
    {
        printf("pesf_appsetting_read_json error\n");
        return EXIT_FAILURE;
    }

    cJSON *json = cJSON_GetObjectItem(root, key);
    if (json) {
        cJSON_ReplaceItemInObject(root, key, cJSON_CreateString(value));
    } else {
        cJSON_AddItemToObject(root, key, cJSON_CreateString(value));
    }

    if (pesf_appsetting_write_json_file(root)) {
        printf("write error\n");

        cJSON_Delete(root);
        return EXIT_FAILURE;
    }

    cJSON_Delete(root);
    return EXIT_SUCCESS;
}

static char* pesf_appsetting_delete_json_from_file(const char *key) {
    cJSON *root = pesf_appsetting_read_json();
    if(root == NULL)
    {
        printf("pesf_appsetting_read_json error\n");
        return EXIT_FAILURE;
    }

    cJSON *detachItem = cJSON_DetachItemFromObject(root, key);
    if(detachItem == NULL) {
        cJSON_Delete(root);
        printf("detachItem == NULL\n");
        return EXIT_FAILURE;

    }
    if (pesf_appsetting_write_json_file(root)) {
        printf("write error\n");
        cJSON_Delete(detachItem);
        cJSON_Delete(root);
        return EXIT_FAILURE;
    }

    cJSON_Delete(detachItem);
    cJSON_Delete(root);
    return EXIT_SUCCESS;
}

static char* pesf_appsetting_update_json2file(const char *key, const char *new_value) {
    char* ret = EXIT_SUCCESS;
    cJSON *root = pesf_appsetting_read_json();
    if(root == NULL)
    {
        printf("pesf_appsetting_read_json error\n");
        return EXIT_FAILURE;
    }

    cJSON *json = cJSON_GetObjectItem(root, key);
    if (json) {
        cJSON_ReplaceItemInObject(root, key, cJSON_CreateString(new_value));

        if (!pesf_appsetting_write_json_file(root)) {
            printf("Updated value for key %s: %s\n", key, new_value);
        }
    } else {
        fprintf(stderr, "Error: Key %s not found\n", key);
        ret = EXIT_FAILURE;
    }

    cJSON_Delete(root);
    return ret;
}

// New function to get all keys
static void get_all_keys(char ***keys, int *num_keys) {
    cJSON *root = pesf_appsetting_read_json();
    if(root == NULL)
    {
        printf("pesf_appsetting_read_json error\n");
        return;
    }

    cJSON *json = root->child;
    int count = 0;

    // Count the number of keys
    while (json) {
        count++;
        json = json->next;
    }

    // Allocate memory for keys
    *keys = (char **)malloc(count * sizeof(char *));
    if (!*keys) {
        fprintf(stderr, "Error: Memory allocation failed\n");
        cJSON_Delete(root);
        *num_keys = 0;
        return;
    }

    // Copy keys
    json = root->child;
    for (int i = 0; i < count; i++) {
        (*keys)[i] = strdup(json->string);
        json = json->next;
    }

    *num_keys = count;

    cJSON_Delete(root);
}

// 获取某一项的 JSON 格式的 key + value 形式
static char *pesf_appsetting_get_json_single_item(const char *key) {
    static char buffer[256];
    cJSON *root = pesf_appsetting_read_json();
    if(root) {
        cJSON *json = cJSON_GetObjectItem(root, key);
        if (json) {
            strncpy(buffer, json->valuestring, sizeof(buffer) - 1);
            printf("json->valuestring %s\n",json->valuestring);

            buffer[sizeof(buffer) - 1] = '\0';

            printf("buffer %s\n",buffer);
            return buffer;
        } else {
            cJSON_Delete(root);
            return NULL;
        }
    } else {
        return NULL;
    }
}

// 获取所有项的 JSON 格式的 key + value 形式
static char *pesf_appsetting_get_json_all_items(void) {
    cJSON *root = pesf_appsetting_read_json();
    if (root) {
        cJSON *json = root->child;
        cJSON *resultRoot = cJSON_CreateObject();

        // 遍历所有项，将其添加到结果 JSON 对象中
        while (json) {
            cJSON_AddItemToObject(resultRoot, json->string, cJSON_CreateString(json->valuestring));
            json = json->next;
        }

        char *result = cJSON_PrintUnformatted(resultRoot);

        // 释放临时的 JSON 对象
        cJSON_Delete(resultRoot);
        cJSON_Delete(root);

        return result;
    } else {
        return NULL;
    }
}

JSValue js_deleteAppSettingValue(JSContext *ctx, JSValueConst this_val,
					 int argc, JSValueConst *argv)
{
	printf("js_deleteAppSettingValue\n");
    char *ret = NULL;
	if (!JS_IsString(argv[0]))
	{
	    printf("error:INVAILD value\n");
		return JS_EXCEPTION;
	}

    const char *jscstr = JS_ToCString(ctx, argv[0]);
	printf("delete str %s\n",jscstr);

    ret = pesf_appsetting_delete_json_from_file(jscstr);

    JS_FreeCString(ctx, jscstr);

    //返回成功或者失败的字符串
    return JS_NewString(ctx, ret);
}


//************************************************************************************
JSValue js_getAllAppSettingValue(JSContext *ctx, JSValueConst this_val,
					int argc, JSValueConst *argv)
{
	printf("js_getAllAppSettingValue\n");

	char* ret = NULL;
    JSValue obj = JS_UNDEFINED;

    ret = pesf_appsetting_get_json_all_items();
    if(ret != NULL) {
        printf("js_getAllAppSettingValue json:%s\n", ret);
        obj = JS_NewString(ctx, ret);
    }

    return obj;
}

JSValue js_getAppSettingValue(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    printf("js_getAppSettingValue\n");
    char *ret = NULL;
	if (!JS_IsString(argv[0]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }

    const char *jscstr = JS_ToCString(ctx, argv[0]);

    ret = pesf_appsetting_get_json_single_item(jscstr);

    JS_FreeCString(ctx, jscstr);

    if(ret) {
        return JS_NewString(ctx, ret);
    } else {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
}

JSValue js_setAppSettingValue(JSContext *ctx, JSValueConst this_val,
							  int argc, JSValueConst *argv)
{
    printf("js_setAppSettingValue\n");
    uint32_t value;
    char* ret = NULL;
    if (!JS_IsString(argv[0]) || !JS_IsString(argv[1]))
    {
        printf("error:INVAILD value\n");
	    return JS_EXCEPTION;
    }

    const char *jscstr_key = JS_ToCString(ctx, argv[0]);
    const char *jscstr_value = JS_ToCString(ctx, argv[1]);
    printf("jscstr_key %s,jscstr_value %s\n", jscstr_key,jscstr_value);

    ret = pesf_appsetting_add_json2file(jscstr_key, jscstr_value);
    printf("setAppSettingValue ret:%s\n", ret);

    JS_FreeCString(ctx, jscstr_key);
    JS_FreeCString(ctx, jscstr_value);

    return JS_NewString(ctx, ret);
}

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_appsetting_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    //appsetting
    {"js_getAllAppSettingValue",   0,  js_getAllAppSettingValue},
    {"js_getAppSettingValue",      1,  js_getAppSettingValue},
    {"js_deleteAppSettingValue",   1,  js_deleteAppSettingValue},
    {"js_setAppSettingValue",      2,  js_setAppSettingValue},

};

const JSCFunctionList* get_appsetting_JSCFunctionList(int *length) {
	*length = countof(pesf_appsetting_funcs);
	return pesf_appsetting_funcs;
}

int js_appsetting_init(JSContext *ctx, JSValueConst global)
{
    int i = 0;

    printf("*********start appsetting init module*******\n");
    /* creat the classes */
    int count = 0;
    const JSCFunctionList* pesf_funcs = get_appsetting_JSCFunctionList(&count);
    printf("count:%d\n",count);
    for(int i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                            JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].param_num));

    }
    printf("*********end appsetting init module*******\n");
    return 0;
}

