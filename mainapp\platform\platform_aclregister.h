/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file platform_aclregister.h
 * @addtogroup platform
 * @{
 * @addtogroup platform_aclregister
 * <AUTHOR>
 * @date 2022-02-16
 * @brief acl module  settings local acl parser will do some machine settings.\n
 *        for example: get mac address burn flash, and so on.
 */

#ifndef __PLATFORM_ACLREGISTER_H__
#define __PLATFORM_ACLREGISTER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "pol/pol_types.h"
#include "utilities/parsercommon.h"
#include "acl/acl.h"

/* platform acl cmd define */
#define ACLCMD_SET_SERIAL_NUMBER        0xFF04    ///< set serial number
#define ACLCMD_GET_SERIAL_NUMBER        0xFF05    ///< get serial number

#define ACLCMD_SET_PRODUCT_DATE         0xFF08    ///< set product date
#define ACLCMD_GET_PRODUCT_DATE         0xFF09    ///< get product date

#define ACLCMD_SET_PRODUCT_BATCH        0xFF0A    ///< set product batch
#define ACLCMD_GET_PRODUCT_BATCH        0xFF0B    ///< get product batch

#define ACLCMD_SET_ENGINE_TYPE          0xFF10    ///< set engine type
#define ACLCMD_GET_ENGINE_TYPE          0xFF11    ///< get engine type

#define ACLCMD_WRITE_FILE               0xFF12    ///< write file
#define ACLCMD_READ_FILE                0xFF13    ///< read file

#define ACLCMD_SET_PREBURNING_TEST      0xFF14    ///< set prebuilding test
#define ACLCMD_GET_PREBURNING_TEST      0xFF15    ///< get prebuilding test

#define ACLCMD_PORT_TEST                0xFF16    ///< port test

#define ACLCMD_SET_RESTORE_FACTORY      0xFF1A    ///< set resort factory

//#define ACLCMD_SET_SENCER_BULKIN        0xFF27    ///< set sencer bulkin

//#define ACLCMD_SET_PRINTER_REBOOT       0xFF29    ///< set printer reboot(LP2023指令,与获取打印机系统网络信息（0xFF29）冲突)

#define ACLCMD_SET_SLEEP_TIME           0xFF82    ///< set sleep time
#define ACLCMD_GET_SLEEP_TIME           0xFF83    ///< get sleep time

#define ACLCMD_SET_SYSTEM_CONFIG        0xFF88    ///< set system config
#define ACLCMD_GET_SYSTEM_CONFIG        0xFF89    ///< get system config

#define ACLCMD_SET_SYS_TIME             0xFFA0    ///< set system time

#define ACLCMD_SET_PRINTER_ATTRIBUTES   0xFF20    ///< set printer attributes
#define ACLCMD_GET_PRINTER_ATTRIBUTES   0xFF21    ///< get printer attributes

#define ACLCMD_PKI_FUNCTION             0xFF40    ///< pki function

#define ACLCMD_SET_SUB_BOARD_SSN        0xFFB0    ///< set sub board serial number
#define ACLCMD_GET_SUB_BOARD_SSN        0xFFB1    ///< get sub board serial number

#pragma pack(1)
/**
 *@brief serial number data
 */
typedef struct
{
    uint8_t primary_host; ///< primary_host tag
    uint8_t minor_host;   ///< minor_host tag
    uint8_t voltage;      ///< voltage tag
    uint8_t product_line; ///< product_line tag
    uint8_t serial[6];    ///< serial tag
    uint8_t tmp[3];       ///< reserve
} SERIAL_NUMBER_DATDA_STRUCT_S;

/**
 *@brief read file cmd
 */
typedef  struct // 16 bytes
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint16_t len;         ///< the file path string len
    char temp[10];        ///< reserve
} READ_FILE_CMD_S;

/**
 *@brief write file cmd
 */
typedef  struct // 16 bytes
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint16_t path_len;    ///< the file path string len
    uint32_t data_len;    ///< the data length
    char temp[6];         ///< reserve
} WRITE_FILE_CMD_S;

/**
 *@brief acl response read file
 */
typedef struct
{
    ACL_RESPONSE_BASE_CLASS_MEMBERS;        ///< define the base items
    uint32_t len;                           ///< the data length
    uint8_t temp[6];                        ///< has to be expanded to 16 bytes total, this does that
} ACL_RESPONSE_READ_FILE_STRUCT_S;

/**
 *@brief aging test 16 bytes
 */
typedef struct
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint8_t  mode;
    uint8_t  times;
    uint8_t  temp[10];
} AGING_TEST_S;

/**
 *@brief port test 16 bytes
 */
typedef  struct
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint32_t len;                             ///< the data length
    char temp[8];                             ///< reserve
} PORT_TEST_CMD_S;

#pragma pack()

/**
 *@brief Register these acl commands to platform module.
 *<AUTHOR>
 *@date 2023-07-17
 */
void platform_register_acl_cmds(void);

/**
 *@brief Register these oid attributes to platform module.
 *@return void
 *<AUTHOR>
 *@date 2023-07-17
 */
void platform_register_oid_attr(void);

#ifdef __cplusplus
}
#endif
#endif

/**
 *@}
 */
