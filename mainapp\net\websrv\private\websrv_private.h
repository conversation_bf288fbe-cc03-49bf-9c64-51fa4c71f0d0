#ifndef __WEBSRV__
#define __WEBSRV__

#include "http_task.h"
#include "../websrv_omstr.h"

typedef int32_t (*WEBSRV_REQ_FUNC)(HTTP_TASK_S* ptask, const char** rcode, const char** rtype);
typedef void (*USER_DATA_DESTROY_FUNC)(void* user_data);
typedef void (*INIT_FUNC)(NET_CTX_S* net_ctx);
typedef void (*SECTION_FUNC)(void);

typedef struct websrv_url_table
{
    const char*         request_url;      ///< The HTTP request URL
    enum http_method    request_method;   ///< The HTTP request method
    WEBSRV_REQ_FUNC     request_handle;   ///< The handling function of this request
    HTTP_HEADERS_FUNC   extra_header_cb;  ///< if existed, would be called by websrv_process_headers
    uint8_t             access_flags;     ///< access permission flags: SYS_ADMIN | AUD_ADMIN | SEC_ADMIN
    uint8_t             author_need;      ///< need to be authorized before this request
} __attribute__((aligned))
WEBSRV_URL_TABLE_S;

typedef struct websrv_task_private
{
    WEBOM_CTX_S            webomctx;
    char                   cookie_info[128];
    char                   author_info[128];
    char                   reply_hdrs[1024];
    char                   reply_file[FILE_PATH_MAX];
    char                   iobuf[TCP_CHUNK_SIZE * 2];
    size_t                 reqtotal;
    size_t                 received;
    uint8_t                authorized;
    uint8_t                flg_authorized_ok;
#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
    uint8_t                admin_id;
#endif
    WEBSRV_URL_TABLE_S     *tbl_entry;
    void*                  user_data;
    USER_DATA_DESTROY_FUNC user_data_destroy_fn;
}
PRIV_INFO_S;

#define EWS_SECTION_PREFIX    websrv_

#define _STR(x)              #x
#define STR(x)              _STR(x)
#define __EWS_SECTION_NAME(x, y)    x ## y
#define _EWS_SECTION_NAME(x, y)    __EWS_SECTION_NAME(x, y)
#define EWS_SECTION_NAME(_name)    _EWS_SECTION_NAME(EWS_SECTION_PREFIX, _name)
#define __attr_section(_name)   __attribute__((used, section(STR(EWS_SECTION_NAME(_name)))))

#define FUNC_EXPORT(_section_name, _func)    static SECTION_FUNC s_##_func __attr_section(_section_name) = (SECTION_FUNC)_func

#endif /* __WEBSRV__*/
