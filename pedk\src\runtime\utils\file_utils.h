/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file file_utils.h
 * @addtogroup utils
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief file_utils init 
 */
#ifndef __RUNTIME__UTILS__FILE_UTILS_H__
#define __RUNTIME__UTILS__FILE_UTILS_H__
#include <stddef.h>

/**
 * @brief   app process on front
 * @param[in] *path :file path
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int is_path_exists(const char *path);

/**
 * @brief   read file
 * @param[in] *path :file path
 * @param[in] *buf :buffer cache 
 * @param[in] buf_size :buf size
 * @param[in] *read_bytes :read data address
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int read_file(const char *path, char *buf, size_t buf_size, size_t *read_bytes);

/**
 * @brief   get file size
 * @param[in] *file :file point
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
size_t get_file_size(const char *file);

#endif // __RUNTIME__UTILS__FILE_UTILS_H__
/**
 * @}
 */
 
