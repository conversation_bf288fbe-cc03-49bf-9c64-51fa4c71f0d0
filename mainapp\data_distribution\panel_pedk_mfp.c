#include "pedk_mgr.h"
#include "panel_pedk_mfp.h"
#include "data_distribution.h"
#include "panel_dc_cmd_tpyedef.h"
#include "panel_config.h"
#include "../pedk_manager/app_manager/app_manager.h"
#include "pedk_download.h"

void panel_pedk_deinit()
{
    pedk_mgr_unregister_handler(MSG_MODULE_PANEL);
}

void panel_pedk_function_switch(void* function_switch, int size)
{
    panel_send_data_u8( OTHER, OTHER_CMD_PEDK_FUNCTION_SWITCH, NOTIFICATION_RESPONSE, function_switch, size );
}

void panel_update_pedk_install_info()
{
    cJSON* cJSON_app_name       = NULL;
    cJSON* cJSON_app_id         = NULL;
    cJSON* cJSON_app_icon       = NULL;
    PEDK_APP_INFO_S* app_list_p = NULL;

    cJSON* root = pedk_download_applist_get_all_entries();
    if(root == NULL)
    {
         pi_log_e("get pesf app list error\n");
         pedk_download_applist_free_entries_array(root);
         return;
    }
    char* app_info_str = cJSON_Print(root);
    printf("app_info_str:%s\n",app_info_str);
    uint32_t app_num = cJSON_GetArraySize(root);
    if( app_num > 0 )
    {
        app_list_p = pi_malloc( sizeof(PEDK_APP_INFO_S) * app_num );
        if( NULL != app_list_p )
        {
            pi_memset( app_list_p, 0, sizeof(PEDK_APP_INFO_S) * app_num );
        }
    }
    else
    {
        pedk_download_applist_free_entries_array(root);
        return;
    }

    //printf("app num:%d\n",app_num);
    
    for( int i = 0; i < app_num; i++ )
    {
        //获取数组中的每个内容， 如果对象则进一步解析
        cJSON * app_obj = cJSON_GetArrayItem(root,i);
        if( NULL == app_obj )
        {
            pi_log_e("Get app_obj error\n");           
            break;
        }
        if(app_obj->type == cJSON_Object)
        {
            cJSON_app_name = cJSON_GetObjectItem(app_obj, "name");///
            cJSON_app_id = cJSON_GetObjectItem(app_obj, "bundleName");              
            cJSON_app_icon = cJSON_GetObjectItem(app_obj, "icon");   

            if( NULL == cJSON_app_name || NULL == cJSON_app_id || NULL == cJSON_app_icon)   
            {
                pi_log_e("panel get pedk app info error\n");
                break;
            }    
          
            strcpy( app_list_p[i].app_name, cJSON_app_name->valuestring );
            strcpy( app_list_p[i].app_id, cJSON_app_id->valuestring );

            //APP图标路径
            strcpy( app_list_p[i].app_icon, "/pesf_data/" );
            strcat( app_list_p[i].app_icon, app_list_p[i].app_id );
            strcat(app_list_p[i].app_icon,cJSON_app_icon->valuestring);            
        }
        
    }
    pedk_download_applist_free_entries_array(root);
    //APP数据发送至面板
    panel_send_data_u8( OTHER, OTHER_CMD_PEDK_APP_INSTALL, NOTIFICATION_RESPONSE, app_list_p, sizeof(PEDK_APP_INFO_S) * app_num );
    pi_free( app_list_p );
    return;
}

void panel_recv_app_install_msg( uint32_t is_install, uint32_t* data, uint32_t data_len )
{
    if( NULL == data || data_len <= 0 )
    {
        return;
    }
    uint32_t result = *data;
    switch ( is_install )
    {
        case 0:
            if( 0 == result )
            {
                system("reboot -f");
            }
            break;

        case 1:
            panel_update_pedk_install_info();
            break;
        
        default:
            break;
    }
}

void panel_pedk_handler( uint32_t sub, int respond, int buf_size, unsigned char* buf, void* ctx )
{
    printf("panel_pedk_handler strat, sub:%u,respond:%d,buf_size:%d\n",sub,respond,buf_size);
    if( NULL == buf)
    {
        printf("buf is null\n");
    }
        
    switch ( sub )
    {
        case MSG_PANEL_SUB_DRAW:

            //printf("mfp recv data:%s\n",buf);       
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_DRAW, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;
            
        case MSG_PANEL_SUB_REDRAW:
            break;

        case MSG_PANEL_SUB_CTRL_BRIGHTNESS:
            break;

        case MSG_PANEL_SUB_CTRL_BUZZER:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_CTRL_BUZZER, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;

        case MSG_PANEL_SUB_CTRL_LED:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_CTRL_LED, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;
            
        case MSG_PANEL_SUB_CTRL_LINK:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_DRAW_EXIT, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;

        case MSG_PANEL_SUB_SCREEN_ON_OFF:
            break;

        case MSG_PANEL_SUB_SET_BACK_TIMEOUT:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_SET_BACK_TIMEOUT_TIME, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;

        case MSG_PANEL_SUB_GET_BACK_TIMEOUT:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_GET_BACK_TIMEOUT_TIME, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;

        case MSG_PANEL_SUB_DELETE_BACK_TIMEOUT:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_DELETE_BACK_TIMEOUT, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;

        case MSG_PANEL_SUB_CHANGE_TO_NATIVE_WINDOW:
            panel_send_data_u8( OTHER, OTHER_CMD_PEDK_CHANGE_TO_NATIVE_WINDOW, NOTIFICATION_RESPONSE, buf, buf_size );        
            break;

        default:
        break;
    }
}



/**
 * @brief cmd callback for test the setting value
 */
int32_t pedk_test_callback(int32_t argc, char *argv[])
{   

    uint32_t data = atoi(argv[0]);
    
    switch (data)
    {
        case 0:
            printf("panel start pedk app\n");
            am_start_app( "CM9105DN-EN" );
            break;
        case 1:
            printf("panel update pedk app info\n");
            panel_update_pedk_install_info();
            break;
        
        default:
            break;
    }

    return 0;
}


int32_t panel_pedk_init()
{
    int32_t ret = 0;

    printf( " panel_pedk_init  \n");

    ret = pedk_mgr_register_handler(MSG_MODULE_PANEL, panel_pedk_handler, NULL );

    
    cmd_register( "panel" , "pedk",  pedk_test_callback,     NULL );

    return ret;
}

void pedk_install_proc( PEDK_APP_INSTALL_MODE_E install_mode, PEDK_APP_OPERATION_RESULT result, char* app_name )
{
    PEDK_APP_INSTALL_INFO_S app_install = {0};
    app_install.mode = install_mode;
    app_install.result = result;
    if( app_name )
    {
        snprintf( app_install.app_name, PEDK_APP_INFO_LEN, "%s", app_name );
    }
    pi_log_i("pedk app name:%s, mode:%s,result:%s\n", app_name, (install_mode == PEDK_APP_INSTALL) ? "INSTALL" : "REMOVE" , \
             (result == PEDK_APP_OP_SUCCESS) ? "SUCCESS" : "FAILURE" );
    panel_send_data_u8( OTHER, OTHER_CMD_PEDK_APP_INSTALL, NOTIFICATION_RESPONSE, &app_install, sizeof( PEDK_APP_INSTALL_INFO_S ) );
}

