/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file http_task.c
 * @addtogroup net
 * @{
 * @addtogroup http
 * <AUTHOR>
 * @date 2023-4-20
 * @brief HTTP task manager
 */
#include "nettypes.h"
#include "netmisc.h"

#include "http_task.h"

#define DEFAULT_TIMEOUT     120

typedef struct http_task_private
{
    QIO_S*      pqio;
    char        resp_hdrs[1024];                                ///< HTTP响应时，除统一响应头以外，额外添加的自定义响应头(by http_task_append_resp_headers)
    char        version[4];                                     ///< HTTP请求的协议版本
    char        url[1024];                                      ///< HTTP请求的URL部分
    char        parms[512];                                     ///< HTTP请求的参数
    char        field_title[FIELD_COUNT_MAX][FIELD_TITLE_LEN];  ///< HTTP请求中headers的字段标签
    char        field_value[FIELD_COUNT_MAX][FIELD_VALUE_LEN];  ///< HTTP请求中headers的字段值
    uint32_t    field_count;                                    ///< HTTP请求中headers的字段个数
    int32_t     timeout_sec;
    uint32_t    chunk_count;
    uint16_t    chunk_size;
    uint8_t     err;
    uint8_t     end;
}
PRIV_INFO_S;

static void decode_parms(char* parms, size_t nparms, char* raw)
{
    char    nc;
    char    ac;
    char    v0;
    char    v1;
    size_t  i;

    for ( i = 0; i + 1 < nparms && *raw; ++i )
    {
        nc = *raw++;
        switch ( nc )
        {
            case '+':
                parms[i] = ' ';
                break;
            case '%':
                ac = *raw++;
                if ( ac >= 'a' && ac <= 'f' )       v0 = ( ac - ('a' - 10) );
                else if ( ac >= 'A' && ac <= 'F' )  v0 = ( ac - ('A' - 10) );
                else                                v0 = ( ac - '0');

                ac = *raw++;
                if ( ac >= 'a' && ac <= 'f' )       v1 = ( ac - ('a' - 10) );
                else if ( ac >= 'A' && ac <= 'F' )  v1 = ( ac - ('A' - 10) );
                else                                v1 = ( ac - '0');

                parms[i] = ( (v0 << 4) | v1 );
                break;
            default:
                parms[i] = nc;
                break;
        }
    }
    parms[i] = '\0';
}

/**
 * @brief The callback function of on_message_begin in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Xin
 * @date   2023-9-16
 */
static int32_t http_callback_message_begin(http_parser* p)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    memset(priv->resp_hdrs,   0, sizeof(priv->resp_hdrs));
    memset(priv->version,     0, sizeof(priv->version));
    memset(priv->url,         0, sizeof(priv->url));
    memset(priv->parms,       0, sizeof(priv->parms));
    memset(priv->field_title, 0, sizeof(priv->field_title));
    memset(priv->field_value, 0, sizeof(priv->field_value));
    priv->field_count = 0;
    priv->chunk_count = 0;
    priv->chunk_size  = 0;

    return 0;
}

/**
 * @brief The callback function of on_url_update in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @param[in] at     :The http_parser url.
 * @param[in] len    :Thel lenth of url.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-16
 */
static int32_t http_callback_url_update(http_parser* p, const char* at, size_t len)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    char*   pstr;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( sizeof(priv->url) - 1 < len )
    {
        NET_WARN("method(%s) url overlength(%u) from client(%u->%u)", http_method_str(p->method), len, ptask->r_port, ptask->l_port);
        return -1;
    }

    if ( priv->url[0] != '\0' )
    {
        NET_WARN("current url(%s), but receive new url from client(%u->%u)", priv->url, ptask->r_port, ptask->l_port);
    }
    snprintf(priv->url, len + 1, "%s", at);

    pstr = strrchr(priv->url, '?');
    if ( STRING_NO_EMPTY(pstr) )
    {
        *pstr++ = '\0';
        decode_parms(priv->parms, sizeof(priv->parms), pstr);
    }

    return 0;
}

/**
 * @brief The callback function of status_update in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @param[in] at     :The http_parser at.
 * @param[in] len    :Thel lenth of at.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_status_update(http_parser* p, const char* at, size_t len)
{
    char*   pstr;

    pstr = (char *)pi_zalloc(len + 1);
    if ( pstr != NULL )
    {
        memcpy(pstr, at, len);
        NET_DEBUG("status(%s)", pstr);
        pi_free(pstr);
    }

    return 0;
}

/**
 * @brief The callback function of header_field_add in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @param[in] at     :The http_parser at.
 * @param[in] len    :Thel lenth of at.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_header_field_add(http_parser* p, const char* at, size_t len)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    size_t  cur_len;

    RETURN_VAL_IF(priv == NULL || at == NULL || len == 0, NET_WARN, -1);
    RETURN_VAL_IF(priv->field_count >= FIELD_COUNT_MAX, NET_WARN, 0);

    cur_len = strlen(priv->field_title[priv->field_count]);
    if ( sizeof(priv->field_title[priv->field_count]) <= cur_len + len )
    {
        memcpy(priv->field_title[priv->field_count] + cur_len, at, sizeof(priv->field_title[priv->field_count]) - cur_len - 1);
        NET_WARN("field_title[%u] need %u + %u bytes, cut!!!", priv->field_count, cur_len, len);
    }
    else
    {
        memcpy(priv->field_title[priv->field_count] + cur_len, at, len);
    }
    NET_TRACE("new field_title[%u](%s) from client(%u->%u)", priv->field_count, priv->field_title[priv->field_count], ptask->r_port, ptask->l_port);

    return 0;
}

/**
 * @brief The callback function of header_field_add in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @param[in] at     :The http_parser at.
 * @param[in] len    :Thel lenth of at.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_header_value_add(http_parser* p, const char* at, size_t len)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    size_t  cur_cnt;
    size_t  cur_len;

    RETURN_VAL_IF(priv == NULL || at == NULL, NET_WARN, -1);

    if ( priv->field_count < FIELD_COUNT_MAX &&  priv->field_title[priv->field_count][0] != '\0' ) /* 解析到新的field_value，新增到priv->field_value */
    {
        cur_cnt = priv->field_count;
    }
    else /* 上一个field_value不完整，拼接新解析内容 */
    {
        RETURN_VAL_IF(priv->field_count == 0 || priv->field_value[priv->field_count - 1][0] == '\0', NET_WARN, -1);
        cur_cnt = priv->field_count - 1;
    }

    if ( len > 0 )
    {
        cur_len = strlen(priv->field_value[cur_cnt]);
        if ( sizeof(priv->field_value[cur_cnt]) <= cur_len + len )
        {
            memcpy(priv->field_value[cur_cnt] + cur_len, at, sizeof(priv->field_value[cur_cnt]) - cur_len - 1);
            NET_WARN("field_value[%u] need %u + %u bytes, cut!!!", priv->field_count, cur_len, len);
        }
        else
        {
            memcpy(priv->field_value[cur_cnt] + cur_len, at, len);
        }
        NET_TRACE("new field_value[%u](%s) from client(%u->%u)", cur_cnt, priv->field_value[cur_cnt], ptask->r_port, ptask->l_port);
    }
    priv->field_count = cur_cnt + 1;

    return 0;
}

/**
 * @brief The callback function of headers_complete in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_headers_complete(http_parser* p)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    int32_t ret = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->version, sizeof(priv->version), "%u.%u", p->http_major, p->http_minor);
    NET_TRACE("method(%s) version(%s) url(%s) from client(%u->%u)", http_method_str(p->method), priv->version, priv->url, ptask->r_port, ptask->l_port);
    if ( ptask->headers_complete_callback != NULL )
    {
        ret = ptask->headers_complete_callback(ptask, p->method, priv->url, p->content_length);
    }

    return ret;
}

/**
 * @brief The callback function of reqbody_received in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_reqbody_received(http_parser* p, const char* at, size_t len)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    int32_t ret = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( ptask->reqbody_received_callback != NULL )
    {
        NET_TRACE("content_length(%lld) received(%u) from client(%u->%u)", (long long)p->content_length, len, ptask->r_port, ptask->l_port);
        ret = ptask->reqbody_received_callback(ptask, at, len);
    }

    return ret;
}

/**
 * @brief The callback function of message_complete in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_message_complete(http_parser* p)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    const char* pval = NULL;
    int32_t     ret = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    NET_TRACE("method(%s) ver(%s) url(%s) parms(%s) from client(%u->%u)", http_method_str(p->method), priv->version, priv->url, priv->parms, ptask->r_port, ptask->l_port);
    if ( ptask->request_complete_callback != NULL )
    {
        ret = ptask->request_complete_callback(ptask, p->method, priv->url, priv->parms);
    }

    if ( ret == 0 )
    {
        if ( ptask->r_port == 0 && ptask->l_port == 0 ) /* IPP-over-USB链路，不适用长连接 */
        {
            priv->timeout_sec = 0;
        }
        else
        {
            pval = http_task_search_header_field(ptask, "Connection");
            NET_TRACE("version(%s) Connection(%s) ret(%d) from client(%u->%u)", priv->version, pval, ret, ptask->r_port, ptask->l_port);
            if ( strcmp(priv->version, "1.0") == 0 ) /* HTTP/1.0 默认使用短连接 */
            {
                if ( pval != NULL && strcasecmp(pval, "keep-alive") == 0 )
                {
                    priv->timeout_sec = 15;
                    ret = 0;
                }
                else
                {
                    priv->timeout_sec = 1;
                    priv->end = 1;
                    ret = -1;
                }
            }
            else /* HTTP/1.1 及以上版本默认使用长连接 */
            {
                if ( pval != NULL && strcasecmp(pval, "close") == 0 )
                {
                    priv->timeout_sec = 1;
                    priv->end = 1;
                    ret = -1;
                }
                else
                {
                    priv->timeout_sec = 15;
                    ret = 0;
                }
            }
        }
    }
    else
    {
        priv->timeout_sec = 1;
        priv->end = 1;
    }

    return ret;
}

/**
 * @brief The callback function of chunk_header in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_chunk_header(http_parser* p)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    NET_DEBUG("content_length(%lld) from client(%u->%u)", (long long)p->content_length, ptask->r_port, ptask->l_port);
    priv->chunk_size = (uint16_t)(p->content_length);
    priv->chunk_count++;

    return 0;
}

/**
 * @brief The callback function of chunk_complete in http_parser_settings.
 * @param[in] p     : The http_parser object pointer.
 * @return callback result
 * @retval ==0      : success\n
 *         !=0      : fail
 * <AUTHOR> Huanbin
 * @date   2023-10-26
 */
static int32_t http_callback_chunk_complete(http_parser* p)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)p->data;
    DECL_PRIV(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( ptask->chunked_complete_callback != NULL )
    {
        ptask->chunked_complete_callback(ptask, priv->chunk_count, priv->chunk_size);
    }

    return 0;
}

static http_parser_settings s_task_settings =
{
.on_message_begin    = http_callback_message_begin,
.on_url              = http_callback_url_update,
.on_status           = http_callback_status_update,
.on_header_field     = http_callback_header_field_add,
.on_header_value     = http_callback_header_value_add,
.on_headers_complete = http_callback_headers_complete,
.on_body             = http_callback_reqbody_received,
.on_message_complete = http_callback_message_complete,
.on_chunk_header     = http_callback_chunk_header,
.on_chunk_complete   = http_callback_chunk_complete,
};

uint32_t http_task_get_headers_field(HTTP_TASK_S* ptask, const char (**title)[FIELD_TITLE_LEN], const char (**value)[FIELD_VALUE_LEN])
{
    DECL_PRIV(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, 0);

    if ( title ) *title = priv->field_title;
    if ( value ) *value = priv->field_value;

    return priv->field_count;
}

const char* http_task_search_header_field(HTTP_TASK_S* ptask, const char* title)
{
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    uint32_t i;

    RETURN_VAL_IF(priv == NULL, NET_WARN, NULL);

    for ( i = 0; i < priv->field_count; ++i )
    {
        if ( strcasecmp(title, priv->field_title[i]) == 0 )
        {
            NET_TRACE("search header field(%s:%s) from client(%u->%u)", title, priv->field_value[i], ptask->r_port, ptask->l_port);
            return priv->field_value[i];
        }
    }

    NET_TRACE("no search header field(%s) from client(%u->%u)", title, ptask->r_port, ptask->l_port);
    return NULL;
}

int32_t http_task_append_resp_headers(HTTP_TASK_S* ptask, const char* title, const char* value)
{
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    size_t  offset;
    int32_t length;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(title) || STRING_IS_EMPTY(value), NET_WARN, -1);

    offset = strlen(priv->resp_hdrs);
    length = snprintf(priv->resp_hdrs + offset, sizeof(priv->resp_hdrs) - offset, "%s: %s\r\n", title, value);
    if ( sizeof(priv->resp_hdrs) <= offset + length )
    {
        NET_WARN("response heanders overlength(%u + %d)", offset, length);
    }
    else
    {
        NET_DEBUG("response headers:\n%s", priv->resp_hdrs);
    }

    return 0;
}

int32_t http_task_reply_resp_headers(HTTP_TASK_S* ptask, const char* rcode, const char* content_type, int32_t content_length)
{
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    char        hdrs_buf[4096];
    char*       pstr = hdrs_buf;
    const char* pval;
    struct tm   ntm;
    int32_t     ret;

    RETURN_VAL_IF(priv == NULL || priv->pqio == NULL, NET_WARN, -1);

    NET_TRACE("reply code(%s) content_type(%s) content_length(%d) to client(%u->%u)", rcode, content_type, content_length, ptask->l_port, ptask->r_port);
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "HTTP/%s %s\r\n", priv->version, rcode);

    if ( STRING_NO_EMPTY(priv->resp_hdrs) )
    {
        pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "%s", priv->resp_hdrs);
    }

    systime_get(&ntm);
    pstr += strftime(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Date: %a, %d %b %Y %H:%M:%S %Z\r\n", &ntm);

    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Cache-Control: no-cache\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Server: bsa/1.0\r\n");

    pval = http_task_search_header_field(ptask, "Connection");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Connection: %s\r\n", pval ? pval : "close");

    if ( content_type == NULL )
    {
        pval = "text/html; charset=\"utf-8\"";
    }
    else
    {
        pval = content_type;
        if ( strstr(pval, "gzip-") != NULL )
        {
            pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Content-Encoding: gzip\r\n");
            pval += strlen("gzip-");
        }
        if ( strcasecmp(pval, "application/force-download") == 0 )
        {
            pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Content-Disposition: attachment; filename=PrinterTroubleLog.txt\r\n");
        }
    }
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Content-Type: %s\r\n", pval);

    if ( content_length >= 0 && strcasestr(priv->resp_hdrs, "Transfer-Encoding: chunked") == NULL )
    {
        pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Content-Length: %d\r\n", content_length);
    }
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "X-Frame-Options: SAMEORIGIN\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "X-XSS-Protection: 1;mode=block\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "X-Content-Type-Options: nosniff\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Strict-Transport-Security: max-age=16070400\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "X-Download-Options: noopen\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "X-Permitted-Cross-Domain-Policies: value\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "Referrer-Policy: no-referrer\r\n");
    pstr += snprintf(pstr, sizeof(hdrs_buf) + hdrs_buf - pstr, "\r\n");

    NET_TRACE("reply headers to client(%u->%u):\n(%s) length(%d)", ptask->l_port, ptask->r_port, hdrs_buf, (int32_t)(pstr - hdrs_buf));
    ret = QIO_WRITE(priv->pqio, hdrs_buf, strlen(hdrs_buf));
    if ( ret < 0 )
    {
        NET_WARN("send hdrs_buf(%s) to client(%u->%u) failed!", hdrs_buf, ptask->l_port, ptask->r_port);
    }
    return ret;
}

int32_t http_task_send(HTTP_TASK_S* ptask, const char* buf, size_t len)
{
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    int32_t retries = 0;
    int32_t nwrite = 0;
    size_t  ntotal = 0;
    int32_t ret;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    while ( ntotal < len )
    {
        ret = QIO_WRITEABLE(priv->pqio, 1, 0);
        if ( ret < 0 )
        {
            NET_WARN("send buf(%u) to client(%u->%u) unwriteable(%d): %d<%s>", len, ptask->l_port, ptask->r_port, ret, errno, strerror(errno));
            break;
        }
        else if ( ret == 0 )
        {
            if ( retries++ > DEFAULT_TIMEOUT )
            {
                NET_WARN("send buf(%u) to client(%u->%u) timeout: %d<%s>", len, ptask->l_port, ptask->r_port, errno, strerror(errno));
                break;
            }
            continue;
        }

        nwrite = QIO_WRITE(priv->pqio, (void *)(buf + ntotal), (size_t)(len - ntotal));
        if ( nwrite <= 0 )
        {
            NET_INFO("send buf(%u) to client(%u->%u) nwrite(%d) ntotal(%u)", len, ptask->l_port, ptask->r_port, nwrite, ntotal);
            break;
        }
        ntotal += nwrite;
        retries = 0;
    }

    return ntotal;
}

void http_task_process(HTTP_TASK_S* ptask)
{
    DECL_PRIV(ptask, PRIV_INFO_S, priv);
    http_parser parser;
    size_t      nparsed;
    int32_t     recvlen;
    int32_t     ret;
    char*       buf;

    RETURN_IF(priv == NULL, NET_WARN);

    buf = (char *)pi_zalloc(TCP_CHUNK_SIZE);
    RETURN_IF(buf == NULL, NET_WARN);

    http_parser_init(&parser, HTTP_REQUEST);
    parser.data = (void *)ptask;

    while ( (priv->err == 0) && (priv->end == 0) )
    {
        ret = QIO_READABLE(priv->pqio, priv->timeout_sec, 0);
        if ( ret < 0 )
        {
            NET_WARN("select client(%u->%u) failed(%d): %d<%s>", ptask->r_port, ptask->l_port, ret, errno, strerror(errno));
            priv->err = 1;
            break;
        }
        else if ( ret == 0 )
        {
            NET_INFO("select client(%u->%u) timeout(%d) end(%u)", ptask->r_port, ptask->l_port, priv->timeout_sec, priv->end);
            priv->end = 1;
            break;
        }

        recvlen = QIO_READ(priv->pqio, buf, TCP_CHUNK_SIZE);
        if ( recvlen < 0 )
        {
            NET_WARN("recv client(%u->%u) failed: %d<%s>", ptask->r_port, ptask->l_port, errno, strerror(errno));
            priv->err = 1;
            break;
        }
        else if ( recvlen == 0 )
        {
            NET_DEBUG("recv 0 bytes, client(%u->%u) has been closed!", ptask->r_port, ptask->l_port);
            priv->end = 1;
            break;
        }

        NET_TRACE("recvlen(%d) from client(%u->%u) QIO<%p>", recvlen, ptask->r_port, ptask->l_port, priv->pqio);
        nparsed = http_parser_execute(&parser, &s_task_settings, buf, (size_t)recvlen);
        NET_TRACE("nparsed(%u) from client(%u->%u) QIO<%p>", nparsed, ptask->r_port, ptask->l_port, priv->pqio);
        if ( nparsed != recvlen )
        {
            NET_DEBUG("recvlen(%d) nparsed(%d), parsing end from client(%u->%u)", recvlen, nparsed, ptask->r_port, ptask->l_port);
            priv->err = 1;
            break;
        }
    }
    pi_free(buf);
    NET_DEBUG("process task end from client(%u->%u) err(%u) end(%u)", ptask->r_port, ptask->l_port, priv->err, priv->end);
}

void http_task_destroy(HTTP_TASK_S* ptask)
{
    if ( ptask != NULL )
    {
        if ( ptask->priv != NULL )
        {
            pi_free(ptask->priv);
        }
        pi_free(ptask);
    }
}

HTTP_TASK_S* http_task_create(QIO_S* pqio, uint16_t remote_port, uint16_t local_port)
{
    HTTP_TASK_S*    ptask = NULL;
    PRIV_INFO_S*    priv = NULL;
    int32_t         ret = -1;

    ptask = (HTTP_TASK_S *)pi_zalloc(sizeof(HTTP_TASK_S));
    RETURN_VAL_IF(ptask == NULL, NET_WARN, NULL);

    do
    {
        priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        BREAK_IF(priv == NULL, NET_WARN);

        priv->timeout_sec = DEFAULT_TIMEOUT;
        priv->pqio        = pqio;

        ptask->r_port     = remote_port;
        ptask->l_port     = local_port;
        ptask->priv       = priv;

        ret = 0;
    }
    while ( 0 );

    if ( ret != 0 )
    {
        http_task_destroy(ptask);
        ptask = NULL;
    }
    return ptask;
}
/**
 *@}
 */
