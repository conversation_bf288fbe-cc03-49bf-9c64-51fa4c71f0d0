#ifndef __PESF_APPSETTING_H__
#define __PESF_APPSETTING_H__

#include <quickjs.h>

//pesf_appsetting
int js_appsetting_init(JSContext *ctx, JSValueConst global);

JSValue js_deleteAppSettingValue(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

JSValue js_getAllAppSettingValue(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

JSValue js_getAppSettingValue(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

JSValue js_setAppSettingValue(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv);

#endif
