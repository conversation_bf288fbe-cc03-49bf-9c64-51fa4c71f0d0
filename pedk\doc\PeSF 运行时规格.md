---
title: PeSF 运行时规格
author: 
  - 大连研发中心-陈兆第
  - 大连研发中心-刘鑫
---

## 1. 索引

- [1. 索引](#1-索引)
- [2. 语法标准](#2-语法标准)
- [3. 标准库](#3-标准库)
  - [3.1. 原始值包装类型](#31-原始值包装类型)
    - [3.1.1. <PERSON><PERSON><PERSON> 类型](#311-bollean-类型)
      - [*******.  valueOf() 返回一个原始值 true 或者 false。](#3111--valueof-返回一个原始值-true-或者-false)
      - [*******.  toLocaleString() / toString() 返回字符串 “true” 或者 “false”。](#3112--tolocalestring--tostring-返回字符串-true-或者-false)
    - [3.1.2. Number 类型](#312-number-类型)
      - [3.1.2.1. toString() 返回相应基数的数值字符串](#3121-tostring-返回相应基数的数值字符串)
      - [3.1.2.2. toFixed() 返回指定小数点位数的字符串](#3122-tofixed-返回指定小数点位数的字符串)
      - [3.1.2.3. toExponential() 返回科学计数法表示的字符串](#3123-toexponential-返回科学计数法表示的字符串)
      - [3.1.2.4. toPrecision() 返回最适合的形式表示的字符串](#3124-toprecision-返回最适合的形式表示的字符串)
      - [3.1.2.5. Number.isInteger() 辨别是否保存为整数](#3125-numberisinteger-辨别是否保存为整数)
      - [3.1.2.6. Number.isSafeInteger() 辨别是否安全整数](#3126-numberissafeinteger-辨别是否安全整数)
    - [3.1.3. String 类型](#313-string-类型)
      - [3.1.3.1. concat() 字符串拼接](#3131-concat-字符串拼接)
      - [3.1.3.2. slice() 提取子字符串](#3132-slice-提取子字符串)
      - [3.1.3.3. substring() 提取子字符串](#3133-substring-提取子字符串)
      - [3.1.3.4. substr() 提取子字符串 （废弃）](#3134-substr-提取子字符串-废弃)
      - [3.1.3.5. indexOf() 查找子串位置](#3135-indexof-查找子串位置)
      - [3.1.3.6. lastIndexOf() 查找子串位置](#3136-lastindexof-查找子串位置)
      - [3.1.3.7. startsWith()](#3137-startswith)
      - [3.1.3.8. endsWith()](#3138-endswith)
      - [3.1.3.9. includes()](#3139-includes)
      - [3.1.3.10. trim() 删除前后空格](#31310-trim-删除前后空格)
      - [3.1.3.11. repeat() 字符串复制](#31311-repeat-字符串复制)
      - [3.1.3.12. padStart() 和 padEnd() 字符串填充](#31312-padstart-和-padend-字符串填充)
      - [3.1.3.13. 字符串迭代和解构](#31313-字符串迭代和解构)
      - [3.1.3.14. 字符串大小写转换](#31314-字符串大小写转换)
      - [3.1.3.15. match() 返回匹配到的结果数组](#31315-match-返回匹配到的结果数组)
      - [3.1.3.16. search() 返回一个匹配到的位置索引](#31316-search-返回一个匹配到的位置索引)
      - [3.1.3.17. replace()、replaceAll() 字符串替换](#31317-replacereplaceall-字符串替换)
      - [3.1.3.18. split() 字符串分割，返回数组](#31318-split-字符串分割返回数组)
      - [3.1.3.19. localeCompare() 比较两个字符串](#31319-localecompare-比较两个字符串)
  - [3.2. 集合引用类型](#32-集合引用类型)
    - [3.2.1. Object](#321-object)
      - [3.2.1.1. 显式创建Object实例的两种方式](#3211-显式创建object实例的两种方式)
      - [3.2.1.2. 对象属性的存取](#3212-对象属性的存取)
      - [3.2.1.3. 对象的存取](#3213-对象的存取)
    - [3.2.2. Array](#322-array)
      - [3.2.2.1. 创建数组](#3221-创建数组)
      - [*******. 数组空位](#3222-数组空位)
      - [*******. 数组索引](#3223-数组索引)
      - [3.2.2.4. 检测数组](#3224-检测数组)
      - [3.2.2.5. 迭代器方法](#3225-迭代器方法)
      - [3.2.2.6. 复制和填充](#3226-复制和填充)
      - [3.2.2.7. 转换方法](#3227-转换方法)
      - [*******. 栈方法](#3228-栈方法)
      - [*******. 队列方法](#3229-队列方法)
      - [3.2.2.10. 排序方法](#32210-排序方法)
      - [3.2.2.11. 操作方法](#32211-操作方法)
      - [3.2.2.12. 搜索和位置方法](#32212-搜索和位置方法)
      - [3.2.2.13. 迭代方法](#32213-迭代方法)
      - [3.2.2.14. 归并方法](#32214-归并方法)
    - [3.2.3. 定型数组](#323-定型数组)
      - [*******. ArrayBuffer](#3231-arraybuffer)
      - [3.2.3.2. DataView](#3232-dataview)
      - [3.2.3.3. 定型数组](#3233-定型数组)
    - [3.2.4. Map](#324-map)
      - [3.2.4.1. 基本API](#3241-基本api)
      - [3.2.4.2. 顺序与迭代](#3242-顺序与迭代)
    - [3.2.5. WeakMap](#325-weakmap)
      - [3.2.5.1. 基本API](#3251-基本api)
      - [3.2.5.2. 弱键](#3252-弱键)
      - [3.2.5.3. 不可迭代键](#3253-不可迭代键)
      - [*******. 使用弱映射](#3254-使用弱映射)
    - [3.2.6. Set](#326-set)
      - [*******. 基本API](#3261-基本api)
      - [3.2.6.2. 顺序与迭代](#3262-顺序与迭代)
      - [3.2.6.3. 定义正式集合操作](#3263-定义正式集合操作)
    - [3.2.7. WeakSet](#327-weakset)
      - [*******. 基本API](#3271-基本api)
      - [3.2.7.2. 弱值](#3272-弱值)
      - [3.2.7.3. 不可迭代值](#3273-不可迭代值)
      - [3.2.7.4. 使用弱集合](#3274-使用弱集合)
    - [3.2.8. 迭代与扩展操作](#328-迭代与扩展操作)
  - [3.3. Math（数学函数）](#33-math数学函数)
    - [3.3.1. 值](#331-值)
    - [3.3.2. 基本运算](#332-基本运算)
    - [3.3.3. 幂运算](#333-幂运算)
    - [3.3.4. 对数运算](#334-对数运算)
    - [3.3.5. 三角函数运算](#335-三角函数运算)
  - [3.4. Date（日期/时间）](#34-date日期时间)
    - [3.4.1. Date类型创建](#341-date类型创建)
    - [3.4.2. Date格式化方法](#342-date格式化方法)
      - [3.4.2.1. toDateString()](#3421-todatestring)
      - [3.4.2.2. toTimeString()](#3422-totimestring)
      - [3.4.2.3. toLocaleDateString()](#3423-tolocaledatestring)
      - [3.4.2.4.  toLocaleTimeString()](#3424--tolocaletimestring)
      - [3.4.2.5.  toUTCString()](#3425--toutcstring)
    - [3.4.3.  Date 组件方法](#343--date-组件方法)
      - [3.4.3.1. setTime(milliseconds)/getTime()](#3431-settimemillisecondsgettime)
      - [3.4.3.2. setFullYear(year)/getFullYear()](#3432-setfullyearyeargetfullyear)
      - [3.4.3.3. setUTCFullYear(year)/getUTCFullYear()](#3433-setutcfullyearyeargetutcfullyear)
      - [3.4.3.4. setMonth(month)/getMonth()](#3434-setmonthmonthgetmonth)
      - [3.4.3.5. setUTCMonth(month)/getUTCMonth()](#3435-setutcmonthmonthgetutcmonth)
      - [3.4.3.6. setDate(date)/getDate()](#3436-setdatedategetdate)
      - [3.4.3.7. setUTCDate(date)/getUTCDate()](#3437-setutcdatedategetutcdate)
      - [3.4.3.8. getDay()/getUTCDay()](#3438-getdaygetutcday)
      - [3.4.3.9. setHours(hours)/getHours()](#3439-sethourshoursgethours)
      - [3.4.3.10. setUTCHours(hours)/getUTCHours()](#34310-setutchourshoursgetutchours)
      - [3.4.3.11. setMinutes(minutes)/getMinutes()](#34311-setminutesminutesgetminutes)
      - [3.4.3.12. setUTCMinutes(minutes)/getUTCMinutes()](#34312-setutcminutesminutesgetutcminutes)
      - [3.4.3.13. setSeconds(seconds)/getSeconds()](#34313-setsecondssecondsgetseconds)
      - [3.4.3.14. setUTCSeconds(seconds)/getUTCSeconds()](#34314-setutcsecondssecondsgetutcseconds)
      - [3.4.3.15. setMilliseconds(milliseconds)/getMilliseconds()](#34315-setmillisecondsmillisecondsgetmilliseconds)
      - [3.4.3.16. setUTCMilliseconds(milliseconds)/getUTCMilliseconds()](#34316-setutcmillisecondsmillisecondsgetutcmilliseconds)
      - [3.4.3.17. getTimezoneOffset()](#34317-gettimezoneoffset)
  - [3.5. JSON（JSON 解析和序列化）](#35-jsonjson-解析和序列化)
    - [3.5.1. JSON对象](#351-json对象)
    - [3.5.2. 序列化选项](#352-序列化选项)
      - [*******. 过滤结果](#3521-过滤结果)
      - [*******. 字符串缩进](#3522-字符串缩进)
    - [3.5.3. 解析选项](#353-解析选项)
  - [3.6. RegExp（正则表达式）](#36-regexp正则表达式)
    - [3.6.1. 正则表达式的创建](#361-正则表达式的创建)
    - [3.6.2. 匹配模式的标记](#362-匹配模式的标记)
      - [3.6.2.1.  g : 全局模式](#3621--g--全局模式)
      - [3.6.2.2.  i : 不区分大小写](#3622--i--不区分大小写)
      - [3.6.2.3.  m : 多行模式](#3623--m--多行模式)
      - [3.6.2.4.  y : 粘附模式](#3624--y--粘附模式)
      - [3.6.2.5.  u : Unicode 模式](#3625--u--unicode-模式)
      - [3.6.2.6.  s : dotAll 模式](#3626--s--dotall-模式)
    - [3.6.3. RegExp 实例属性](#363-regexp-实例属性)
    - [3.6.4. RexExp 实例方法](#364-rexexp-实例方法)
      - [3.6.4.1. exec() 实例方法](#3641-exec-实例方法)
      - [3.6.4.2.  test() 实例方法](#3642--test-实例方法)
  - [3.7. Function（函数）](#37-function函数)
    - [3.7.1. 基本功能](#371-基本功能)
    - [3.7.2. 箭头函数](#372-箭头函数)
    - [3.7.3. 函数内部](#373-函数内部)
      - [*******. arguments](#3731-arguments)
      - [3.7.3.2. new.target](#3732-newtarget)
    - [3.7.4. 函数的属性与方法](#374-函数的属性与方法)
    - [3.7.5. 闭包](#375-闭包)
    - [3.7.6. 自执行函数](#376-自执行函数)
    - [3.7.7. 异步函数](#377-异步函数)
  - [3.8. HTTP 支持](#38-http-支持)
  - [3.9. bridge 模块](#39-bridge-模块)
    - [3.9.1. 为什么要有 bridge 模块？](#391-为什么要有-bridge-模块)
    - [3.9.2. bridge 模块如何实现？](#392-bridge-模块如何实现)
    - [3.9.3. bridge 模块为什么要使用 PeSF 运行时的事件队列？](#393-bridge-模块为什么要使用-pesf-运行时的事件队列)
    - [3.9.4. bridge 模块函数](#394-bridge-模块函数)
      - [*******. runtime_send_to_brige](#3941-runtime_send_to_brige)
      - [*******. on_msg 函数](#3942-on_msg-函数)
      - [*******. bridge 模块应用举例](#3943-bridge-模块应用举例)
  - [3.10. Process](#310-process)
    - [3.10.1. exit 结束 PeSF App](#3101-exit-结束-pesf-app)
  - [3.11. 定时功能](#311-定时功能)
    - [3.11.1. setTimeout](#3111-settimeout)
    - [3.11.2. clearTimeout](#3112-cleartimeout)
    - [3.11.3. setInterval](#3113-setinterval)
    - [3.11.4. clearInterval](#3114-clearinterval)
- [4. PeSF API](#4-pesf-api)
- [5. 环境变量](#5-环境变量)

## 2. 语法标准

PeSF 支持 JavaScript ES2020 的基本语言特性，如变量声明（var、let、const）、函数定义、箭头函数、模板字面量、解构赋值、类、模块导入和导出等。

PeSF 支持 Promise 和 async/await 语法，可以进行异步编程，并且提供了相关的 API，如 Promise.resolve、Promise.reject、Promise.all、Promise.race 等。

## 3. 标准库

PeSF 提供了一个基本的 JavaScript 标准库，包括字符串处理、日期和时间、正则表达式、文件系统、网络和 HTTP 请求等功能。

### 3.1. 原始值包装类型
为了方便操作原始值，PeSF 提供了3种特殊的引用类型：Boolean、Number 和 String。这些类型具有和各自原始类型对应的特殊行为。每当用到某个原始值的方法或者属性时，后台都会创建一个相应的原始包装类型对象，从而暴露出操作原始值的方法。
#### 3.1.1. Bollean 类型
Boolean 是对应布尔值的引用类型。要创建一个 Boolean 对象，就使用 Boolean() 构造函数并传入 true 或者 false。
```js
let obj = new Boolean(true);
```
##### *******.  valueOf() 返回一个原始值 true 或者 false。

Boolean 的实例会重写 valueOf() 方法，返回一个原始值 true 或者 false。

##### *******.  toLocaleString() / toString() 返回字符串 “true” 或者 “false”。

toLocaleString() 和 toString() 方法也被重写，返回字符串 “true” 或者 “false”。

例：
```js
let bool = new Boolean(true);
console.log(bool.valueOf());        // true
console.log(bool.toString());       // "true"
console.log(bool.toLocaleString()); // "true"
```

#### 3.1.2. Number 类型
Number 是对应数值的引用类型。要创建一个 Number 对象，就使用 Number 构造函数并传入一个数值。
```js
let obj = new Number(10);
```
##### 3.1.2.1. toString() 返回相应基数的数值字符串

toString() 方法可选地接收一个表示基数的参数，并返回相应基数的数值字符串。

例：
```js
let num = new Number(10)
console.log(num.toString(2));   // 1010
console.log(num.toString(8));   // 12
console.log(num.toString(16));  // a
```

##### 3.1.2.2. toFixed() 返回指定小数点位数的字符串
toFixed() 返回指定小数点位数的字符串
toFixed() 方法返回包含指定小数位数的字符串。位数不够的填0，位数超出则四舍五入。

例：
```js
let num = 12.345
console.log(num.toFixed(5));	// 12.34500
console.log(num.toFixed(2));	// 12.35
```

##### 3.1.2.3. toExponential() 返回科学计数法表示的字符串
toExponential() 返回科学计数法表示的字符串
toExponential() 方法返回科学计数法表示的字符串。也接收一个参数，表示结果中小数的位数。

例：
```js
let num = 12.0000000000345
console.log(num.toExponential());	// 1.20000000000345e+1
console.log(num.toExponential(2));	// 1.20e+1
```

##### 3.1.2.4. toPrecision() 返回最适合的形式表示的字符串
toPrecision() 返回最适合的形式表示的字符串
toPrecision() 根据情况返回最合理的输出结果。接收一个参数，表示结果中数字的总位数（不包含指数）。

本质上，toPrecision() 会根据数值和精度来决定调用 toFixed() 还是 toExponential()。
```js
let num1 = 12.000000000345;
let num2 = 12.345;
console.log(num1.toPrecision(1)); // 1e+1	（舍入为10）
console.log(num1.toPrecision(2)); // 12		（舍入为12）
console.log(num1.toPrecision(3)); // 12.0	（舍入为12.0）
console.log(num2.toPrecision(3)); // 12.3
```

解释：因为 num1 不能用1位数精确表示，所以这个方法会将 num1 舍入为 10。

##### 3.1.2.5. Number.isInteger() 辨别是否保存为整数
Number.isInteger() 辨别是否保存为整数
Number.isInteger() 方法可以用来辨别一个数是否保存为整数。
```js
console.log(Number.isInteger(10));      // true
console.log(Number.isInteger(10.0));    // true
console.log(Number.isInteger(10.1));    // false
```

##### 3.1.2.6. Number.isSafeInteger() 辨别是否安全整数
Number.isSafeInteger() 辨别是否安全整数
IEEE 754 格式有一个特殊的数值范围，这个范围中的二进制值可以表示为一个整数。这个数值范围是 ：Number.MIN_SAFE_INTEGER 到 Number.MAX_SAFE_INTEGER。超过了这个范围，即使保存为整数，IEEE 754 格式也意味着表示为完全不同的数值。

为了鉴别一个数是否在这个数值内，可以使用 Number.isSafeInteger() 方法。
```js
console.log(Number.isSafeInteger(2 ** 53 -1));	// true
console.log(Number.isSafeInteger(2 ** 54));		// false
```

#### 3.1.3. String 类型
String 是对应布尔值的引用类型。要创建一个 String 对象，就使用 String() 构造函数并传入字符串原始值。
```js
let obj = new String('string')
console.log(typeof obj);        // object
```

String 对象继承的三个方法 valueOf()、toLocaleString()、toString() 都返回对象的原始字符串表示。

##### 3.1.3.1. concat() 字符串拼接
concat() 方法，用于将一个或多个字符串拼接成一个新的字符串。concat() 会返回一个新值，而不会改变原始值。
```js
let s = "hello ";
console.log(s.concat("world"));    // hello world
console.log(s);                    // hello 
```

concat() 方法可以接受任意个数的参数，因此可以一次拼接多个字符串。
```js
let s = "hello";
console.log(s.concat(" ", "world", "!"));	// hello world!
```

虽然 concat() 方法可以拼接字符串，但是更多时候使用加号操作符（+）。多数情况下，使用加号更加方便。
```js
let s = "hello";
console.log(s + " " + "world" + "!");		// hello world! 
```

##### 3.1.3.2. slice() 提取子字符串
slice() 方法提取某个字符串的一部分，并返回一个新的字符串，且不会改动原字符串。slice() 方法接收两个参数，第一个参数表示子字符串的开始位置，第二个参数表示子字符串的结束位置。含头不含尾。
```js
let s = "12345678";
let sub = s.slice(1, 3);
console.log(sub);   // '23'
```
如果第二个参数缺失（不是0），则提取到字符串末尾。

当参数是负数时，slice() 方法将所有的负值参数都当成字符串长度加上负参数值。
```js
let s = "12345678";
let sub = s.slice(-5, -2); // 相当于s.slice(3,6)
console.log(sub); 	// "456"
```

如果加上字符串长度后，第一个参数仍是负数，则从第一位开始截取。
```js
let s = "12345678";
let sub = s.slice(-10, 5);	// -10加上8后仍是负数
console.log(sub);	// 12345
```

如果加上字符串长度后，第二个参数仍是负数，则返回空串。
```js
let s = "12345678";
let sub = s.slice(1, -10);	// -10加上8后仍是负数
console.log(sub);	// 空串
```

##### 3.1.3.3. substring() 提取子字符串
substring() 方法提取某个字符串的一部分，并返回一个新的字符串，且不会改动原字符串。substring() 方法接收两个参数，第一个参数表示子字符串的开始位置，第二个参数表示子字符串的结束位置。含头不含尾。
```js
let s = "12345678";
let sub = s.substring(1, 3);
console.log(sub);   // '23'
```
如果第二个参数缺失（不是0），则提取到字符串末尾。

当参数是负数时，substring() 方法把所有负参数值都转为0。
```js
let s = "12345678";
let sub = s.substring(-1, 6);
console.log(sub);	// 123456
```
```js
let s = "12345678";
let sub = s.substring(1, -6);
console.log(sub);	// 1
```
如果第二个参数小于第一个参数，则两参数就像调换一样。总是返回较小参数位置到较大参数位置的子串。

##### 3.1.3.4. substr() 提取子字符串 （废弃）
substr() 方法提取某个字符串的一部分，并返回一个新的字符串，且不会改动原字符串。substr() 方法接收两个参数，第一个参数表示子字符串的开始位置，第二个参数表示返回的字符串中字符的数量。
```js
let s = "12345678";
let sub = s.substr(1, 4);	
console.log(sub);		// 2345
```
如果第二个参数缺失（不是0），则提取到字符串末尾。

当参数是负数时，substr() 方法将第一个负参数值当成字符串长度加上该值，将第二个负参数值转换成0。
```js
let s = "12345678";
let sub = s.substr(-4, 2);
console.log(sub);   // 56
```
```js
let s = "12345678";
let sub = s.substr(4, -6);
console.log(sub); 	// 第二个参数如果为0，相当于空串
```
##### 3.1.3.5. indexOf() 查找子串位置
indexOf() 方法从字符串头开始查找子字符串，并返回子串的开始位置（如果没有找到，则返回-1）。
```js
let s = "aabcabcd";
console.log(s.indexOf("bc"));   	// 2
```
接收可选的第二个参数，表示搜索的开始位置。indexOf() 方法会从这个位置开始向尾搜索。

##### 3.1.3.6. lastIndexOf() 查找子串位置
lastIndexOf() 方法从字符串尾开始查找子字符串，并返回子串的开始位置（如果没有找到，则返回-1）。
```js
let s = "aabcabcd";
console.log(s.lastIndexOf("bc"));   // 5
```
接收可选的第二个参数，表示搜索的开始位置。lastIndexOf() 方法会从这个位置开始向头搜索。

找所有子串的位置
使用第二个参数并循环调用 indexOf() 或 lastIndexOf()，可以在字符串中找到所有目标子字符串。
```js
let s = "aabcabcdbc";
let sub = "bc";
let posArr = [];
let pos = s.indexOf(sub);

while (pos !== -1) {
  posArr.push(pos);
  pos = s.indexOf(sub, pos + 1);
}

console.log(posArr);	//  2, 5, 8 
```
```js
let s = "aabcabcdbc";
let sub = "bc";
let posArr = [];
let pos = s.lastIndexOf(sub);

while (pos !== -1) {
  posArr.push(pos);
  pos = s.lastIndexOf(sub, pos - 1);
}

console.log(posArr);	// 8, 5, 2
```

##### 3.1.3.7. startsWith()
startsWith() 方法用来判断当前字符串是否以另外一个给定的子字符串开头，并根据判断结果返回 true 或 false。startsWith()方法接收可选的第二个参数，表示开始搜索的位置。
```js
let s = 'afoobar'
console.log(s.startsWith('foo'));		// false
console.log(s.startsWith('foo',1));		// true
```
##### 3.1.3.8. endsWith()
endsWith() 方法用来判断当前字符串是否是以另外一个给定的子字符串结尾的，根据判断结果返回 true 或 false。endsWith() 方法接收可选的第二个参数，表示应该当作字符串结束的位置，如果不提供这个参数，默认是字符串的长度。
```js
let s = 'foobara'
console.log(s.endsWith('bar'));		// false
console.log(s.endsWith('bar',6));	// true
```

##### 3.1.3.9. includes()
includes() 方法用于判断一个字符串是否包含在另一个字符串中，根据情况返回 true 或 false。includes()方法接收可选的第二个参数，表示开始搜索的位置。
```js
let s = 'foobarfoo'
console.log(s.includes('bar'));		// true
```

##### 3.1.3.10. trim() 删除前后空格
trim() 方法返回一个从两头去掉空白字符的字符串，并不影响原字符串本身。
```js
let s1 = "    foobar ";
let s2 = s1.trim();
console.log(s1);    // 不改变原字符串
console.log(s2);    // foobar
```

trimEnd() ，别名 trimRight() 移除字符串末尾空格。
trimStart() ，别名 trimLeft() 移除字符串开头空格。

##### 3.1.3.11. repeat() 字符串复制
repeat() 构造并返回一个新字符串，接收一个参数表示将字符串重复多少次，然后返回拼接后的结果。
```js
let s = 'foo'
console.log(s.repeat(5));   // foofoofoofoofoo
```

##### 3.1.3.12. padStart() 和 padEnd() 字符串填充
padStart() 和 padEnd() 方法用另一个字符串填充当前字符串(如果需要的话，会重复多次)，以便产生的字符串达到给定的长度。padStart() 从左侧开始填充，padEnd() 从右侧开始填充。这两个方法第一个参数是长度，第二个参数是可选的填充字符串，默认空格。
```js
let s = "foo";
let s1 = s.padStart(10, "bar");
console.log(s1);                // barbarbfoo
let s2 = s.padEnd(10, "bar");
console.log(s2);                // foobarbarb
```
##### 3.1.3.13. 字符串迭代和解构
字符串原型上暴露了一个 @@iterator 方法，返回一个新的 Iterator 对象，它遍历字符串的代码点，返回每一个代码点的字符串值。
```js
let string = "abcde";
let strIter = string[Symbol.iterator]();
let objec = strIter.next();
console.log('{'+objec.value+','+ objec.done+'}'); // { value: 'a', done: false }
objec = strIter.next();
console.log('{'+objec.value+','+ objec.done+'}'); // { value: 'b', done: false }
objec = strIter.next();
console.log('{'+objec.value+','+ objec.done+'}'); // { value: 'c', done: false }
objec = strIter.next();
console.log('{'+objec.value+','+ objec.done+'}'); // { value: 'd', done: false }
objec = strIter.next();
console.log('{'+objec.value+','+ objec.done+'}'); // { value: 'e', done: false }
objec = strIter.next();
console.log('{'+objec.value+','+ objec.done+'}'); // { value: undefined, done: true }
```
使用 for-of 循环，通过迭代器按序访问每个字符。
```js
let string = "abcde";
for (const s of string) {
  console.log(s);		// a b c d e
}
```
有了这个迭代器，可以通过解构操作符来解构。比如：把字符串分割为字符数组。
```js
let string = "abcde";
console.log([...string]);   // [ 'a', 'b', 'c', 'd', 'e' ]
```

##### 3.1.3.14. 字符串大小写转换
* toLowerCase() 会将调用该方法的字符串值转为小写形式，并返回。
* toUpperCase() 方法将调用该方法的字符串转为大写形式，并返回。
* toLocaleLowerCase() 方法根据任何指定区域语言环境设置的大小写映射，返回调用字符串被转换为小写的格式。
* toLocaleUpperCase() 方法根据任何指定区域语言环境设置的大小写映射，返回调用字符串被转换为大写的格式。

```js
let stri = "aBcDe";
console.log(stri.toUpperCase());        //ABCDE
console.log(stri.toLowerCase());        //abcde
console.log(stri.toLocaleUpperCase());  //ABCDE
console.log(stri.toLocaleLowerCase());  //abcde
```
注意：
多数地区下，地区特定方法和通用方法相同，少数地区需要特定方法。
如果不知道代码涉及什么语言，最好使用地区特定方法。

##### 3.1.3.15. match() 返回匹配到的结果数组
match() 检索返回一个字符串匹配正则表达式的结果。接收一个参数，可以是一个正则表达式，也可以是一个RegExp 对象。返回一个数组。
```js
const paragraph = 'The quick brown fox jumps over the lazy dog. It barked.';
const regex = /[A-Z]/g;
const found = paragraph.match(regex);
console.log(found);     // [ 'T', 'I' ]
```
##### 3.1.3.16. search() 返回一个匹配到的位置索引
search() 方法执行正则表达式和 String 对象之间的一个搜索匹配。接收一个参数，可以是一个正则表达式，也可以是一个 RegExp 对象。返回第一个匹配位置的索引，如果没有找到则返回 -1。
```js
const paragraph = "the Quick brown fox jumps over the lazy dog. It barked.";
const regex = /[A-Z]/g;
const found = paragraph.search(regex);
console.log(found);     // 4
```

##### 3.1.3.17. replace()、replaceAll() 字符串替换
replace() 方法返回一个由替换值（replacement）替换部分或所有的模式 （pattern） 匹配项后的新字符串。模式可以是一个字符串或者一个正则表达式，替换值可以是一个字符串或者一个每次匹配都要调用的回调函数。如果 pattern 是字符串，则仅替换第一个匹配项。
```js
const p = 'The quick brown fox jumps over the lazy dog. If the dog reacted, was it really lazy?';

console.log(p.replace('dog', 'monkey'));
// expected output: "The quick brown fox jumps over the lazy monkey. If the dog reacted, was it really lazy?"

const regex = /dog/g;
console.log(p.replace(regex, 'ferret'));
// expected output: "The quick brown fox jumps over the lazy ferret. If the ferret reacted, was it really lazy?"
```
replaceAll() 方法返回一个新字符串，新字符串所有满足 pattern 的部分都已被 replacement 替换。 pattern 可以是一个字符串或一个 RegExp， replacement 可以是一个字符串或一个在每次匹配被调用的函数。原始字符串保持不变。
```js
const p = 'The quick brown fox jumps over the lazy dog. If the dog reacted, was it really lazy?';

console.log(p.replaceAll('dog', 'monkey'));
// expected output: "The quick brown fox jumps over the lazy monkey. If the monkey reacted, was it really lazy?"


// global flag required when calling replaceAll with regex
const regex = /Dog/ig;
console.log(p.replaceAll(regex, 'ferret'));
// expected output: "The quick brown fox jumps over the lazy ferret. If the ferret reacted, was it really lazy?"
```

##### 3.1.3.18. split() 字符串分割，返回数组
split() 方法使用指定的分隔符字符串将一个 String 对象分割成子字符串数组，以一个指定的分割字串来决定每个拆分的位置。
```js
const str = "The quick brown fox";
const words = str.split(" ");
console.log(words);             // [ 'The', 'quick', 'brown', 'fox' ]
const chars = str.split("");
console.log(chars[8]);          // k
const strCopy = str.split();
console.log(strCopy);           // [ 'The quick brown fox' ]
```

##### 3.1.3.19. localeCompare() 比较两个字符串
按照字母表排序，如果字符串应该排在字符串参数前面，则返回负值。（通常是 -1，具体看实现）
```js
const s1 = 'abc'
const s2 = 'bcd'
console.log(s1.localeCompare(s2));  // -65793
```
字符串和字符串参数相等，返回 0。
```js
const s1 = 'abcd'
const s2 = 'abcd'
console.log(s1.localeCompare(s2));  // 0
```
按照字母表排序，如果字符串应该排在字符串参数后面，则返回正值。（通常是 1，具体看实现）
```js
const s1 = 'bcd'
const s2 = 'abc'
console.log(s1.localeCompare(s2));  // 65793
```

提供字符串处理的方法，如拼接字符串、查找子字符串、替换、切割等操作。例如，可以使用 string.concat 进行字符串拼接，使用 string.indexOf 查找子字符串的位置，使用 string.split 进行字符串的分割等。

### 3.2. 集合引用类型

#### 3.2.1. Object

大多数引用值的示例使用的是 Object 类型。虽然 Object 的实例没有多少功能，但很适合存储和在应用程序间交换数据。

##### 3.2.1.1. 显式创建Object实例的两种方式

1.使用 new 操作符和 Object 构造函数

```js
let person = new Object(); 
person.name = "Nicholas"; 
person.age = 29;
```

1. 使用对象字面量（object literal）表示法。
对象字面量是对象定义的简写形式，目的是为了简化包含大量属性的对象的创建
```js
let person = { 
 name: "Nicholas",  //逗号用于在对象字面量中分隔属性
 age: 29, 
 5: true  // 属性名可以是字符串或数值,但是数值属性会自动转换为字符串
};
```

也可以用对象字面量表示法来定义一个只有默认属性和方法的对象，只要使用一对大括号，中 间留空就行了

```js
let person = {}; // 与 new Object()相同
person.name = "Nicholas"; 
person.age = 29;
```

##### 3.2.1.2. 对象属性的存取

1. 点语法存取
  
```js
console.log(person.name); // "Nicholas"  
```

2. 中括号存取

```js
console.log(person["name"]); // "Nicholas" 
```

中括号的主要优势就是可以通过变量访问属性，即中括号中可以为属性名也可以为值为属性名的变量

```js
let propertyName = "name"; 
console.log(person[propertyName]); // "Nicholas"
```

如果属性名中包含可能会导致语法错误的字符，或者包含关键字/保留字，包含非字母数字字符时只能使用中括号语法，例如：

```js
person["first name"] = "Nicholas";  // 属性名中包含空格，不能用点语法来访问
```

注意：通常，点语法是首选的属性存取方式，除非访问属性时必须使用变量。

##### 3.2.1.3. 对象的存取
对象存储是PeSF独有功能，可以使用Object.save(name,obj)函数将对象存储到文件中，再通过Object.load(name)函数，读取存储过的文件。

1. 存储。Object.save(name,obj)的第一个参数必须是字符串类型，第二个参数必须是对象类型。
   函数会将对象转换成JSON序列化的形式，存储在文件中，文件名由参数1提供。
   
```js
let str = "save.txt";
let opt = {
    name: "father",
    age:43,
    child:{name:"son",
           age:13}
};

Object.save(str,opt);//生成save.txt文件，文件内存储{"name":"father","age":43,"child":{"name":"son","age":13}}
```

2. 加载。Object.load(name)。
   函数是Object.save的逆过程，会根据提供的名字，加载已存储的对象文件，并逆序列化转换为object对象返回。
   
```js
let str = "save.txt";
let opt = {
    name: "father",
    age:43,
    child:{name:"son",
           age:13}
};
Object.save(str,opt);

let opt2 = Object.load(str);
console.log(JSON.stringify(opt2)); //{"name":"father","age":43,"child":{"name":"son","age":13}}
```


#### 3.2.2. Array

##### 3.2.2.1. 创建数组

1.使用Array构造函数

```js
let colors = new Array();  // 创建一个空数组
let colors = new Array(20);  // 传入一个数值，表示数组中元素的数量
let colors = new Array("red", "blue", "green");  // 传入数组中要保存的元素
let colors = new Array("red")  // 当传入的参数只有一个时，若非数值，则会表示数组中的元素
```
注意：使用 Array 构造函数时，也可以省略 new 操作符。

2. 使用数组字面量（array literal）表示法
数组字面量是在中括号中包含以逗号分隔的元素列表
```js
let colors = ["red", "blue", "green"]; // 创建一个包含 3 个元素的数组
let names = []; // 创建一个空数组
let values = [1,2,]; // 创建一个包含 2 个元素的数组
```
`
3.  静态方法from() ：用于将类数组结构转换为数组实例
`
```js
console.log(Array.from("Matt")); // ["M", "a", "t", "t"]

// 可以使用 from()将集合和映射转换为一个新数组
const m = new Map().set(1, 2) 
 .set(3, 4); 
const s = new Set().add(1) 
 .add(2) 
 .add(3) 
 .add(4); 
console.log(Array.from(m)); // [[1, 2], [3, 4]] 
console.log(Array.from(s)); // [1, 2, 3, 4]

// Array.from()对现有数组执行浅复制
const a1 = [1, 2, 3, 4]; 
const a2 = Array.from(a1); 
console.log(a1); // [1, 2, 3, 4] 
console.log(a1 === a2); // false

// 可以使用任何可迭代对象
const iter = { 
 *[Symbol.iterator]() { 
 yield 1; 
 yield 2; 
 yield 3; 
 yield 4; 
 } 
}; 
console.log(Array.from(iter)); // [1, 2, 3, 4]

// arguments 对象可以被轻松地转换为数组
function getArgsArray() { 
 return Array.from(arguments); 
} 
console.log(getArgsArray(1, 2, 3, 4)); // [1, 2, 3, 4]
 
// from()也能转换带有必要属性的自定义对象
const arrayLikeObject = { 
 0: 1, 
 1: 2, 
 2: 3, 
 3: 4, 
 length: 4 
}; 
console.log(Array.from(arrayLikeObject)); // [1, 2, 3, 4]
```
Array.from()还接收第二个可选的映射函数参数。这个函数可以直接增强新数组的值，而无须像调用 Array.from().map()那样先创建一个中间数组。还可以接收第三个可选参数，用于指定映射函数中 this 的值。但这个重写的 this 值在箭头函数中不适用。
```js
const a1 = [1, 2, 3, 4]; 
const a2 = Array.from(a1, x => x**2); 
const a3 = Array.from(a1, function(x) {return x**this.exponent}, {exponent: 2}); 
console.log(a2); // [1, 4, 9, 16] 
console.log(a3); // [1, 4, 9, 16]
```
4. 静态方法of()
用于将一组参数转换为数组实例。
```js
console.log(Array.of(1, 2, 3, 4)); // [1, 2, 3, 4]  
console.log(Array.of(undefined)); // [undefined] 
```

##### *******. 数组空位
1. 使用数组字面量初始化数组时，可以使用一串逗号来创建空位（hole）。ECMAScript 会将逗号之间相应索引位置的值当成空位
```js
const options = [,,,,,]; // 创建包含 5 个元素的数组
console.log(options.length); // 5 
console.log(options); // [,,,,,]
```

2. ES6 新增的方法和迭代器与早期 ECMAScript 版本中存在的方法行为不同。ES6 新增方法普遍将这些空位当成存在的元素，只不过值为 undefined：

```js
const options = [1,,,,5]; 
for (const option of options) { 
 console.log(option === undefined); 
} 
// false 
// true 
// true 
// true 
// false
```

注意：由于行为不一致和存在性能隐患，因此实践中要避免使用数组空位。如果确实需要空位，则可以显式地用 undefined 值代替。

##### *******. 数组索引

1. 要取得或设置数组的值，需要使用中括号并提供相应值的数字索引。
在中括号中提供的索引表示要访问的值。
如果索引小于数组包含的元素数，则返回存储在相应位置的元素，就像示例中 colors[0]示"red"一样。
设置数组的值方法也是一样的，就是替换指定位置的值。
如果把一个值设置给超过数组最大索引的索引，就像示例中的 colors[3]，则数组长度会自动扩展到该索引值加 1（示例中设置的索引 3，所以数组长度变成了 4）。

```js
let colors = ["red", "blue", "green"]; // 定义一个字符串数组
console.log(colors[0]); // 显示第一项
colors[2] = "black"; // 修改第三项
colors[3] = "brown"; // 添加第四项
```

2. 数组 length 属性不是只读的，可通过修改 length 属性，可以从数组末尾删除或添加元素。

```js
let colors = ["red", "blue", "green"]; // 创建一个包含 3 个字符串的数组
colors.length = 2;  // 更改length且小于原来的length，则末尾元素会被删除
console.log(colors[2]); // undefined

let colors = ["red", "blue", "green"]; // 创建一个包含 3 个字符串的数组
colors.length = 4; // 更改length且大于原来的length，则会向末尾添加元素undefined
console.log(colors[3]); // undefined
```
注意：注意 数组最多可以包含 4 294 967 295 个元素。如果尝试添加更多项，则会导致抛出错误。以这个最大值作为初始值创建数组，可能导致脚本 运行时间过长的错误。

##### 3.2.2.4. 检测数组

Array.isArray()方法：是确定一个值是否为数组
```js
if (Array.isArray(value)){ 
 // 操作数组
}
```

##### 3.2.2.5. 迭代器方法

Array 的原型上暴露了 3 个用于检索数组内容的方法 ： keys()、values() 和 entries()：

* keys()    ： 返回数组索引的迭代器
* values()  ： 返回数组元素的迭代器
* entries() ： 返回索引/值对的迭代器

```js
const a = ["foo", "bar", "baz", "qux"]; 
// 因为这些方法都返回迭代器，所以可以将它们的内容
// 通过 Array.from()直接转换为数组实例
const aKeys = Array.from(a.keys()); 
const aValues = Array.from(a.values()); 
const aEntries = Array.from(a.entries()); 
console.log(aKeys); // [0, 1, 2, 3] 
console.log(aValues); // ["foo", "bar", "baz", "qux"] 
console.log(aEntries); // [[0, "foo"], [1, "bar"], [2, "baz"], [3, "qux"]]
```

##### 3.2.2.6. 复制和填充

1. fill() 方法：向一个已有的数组中插入全部或部分相同的值。
开始索引用于指定开始填充的位置，它是可选的。
如果不提供结束索引，则一直填充到数组末尾。
负值索引从数组末尾开始计算。 也可以将负索引想象成数组长度加上它得到的一个正索引
fill() 静默忽略超出数组边界、零长度及方向相反的索引范围

```js
const zeroes = [0, 0, 0, 0, 0]; 
// 用 5 填充整个数组
zeroes.fill(5); 
console.log(zeroes); // [5, 5, 5, 5, 5] 
zeroes.fill(0); // 重置
// 用 6 填充索引大于等于 3 的元素
zeroes.fill(6, 3); 
console.log(zeroes); // [0, 0, 0, 6, 6] 
zeroes.fill(0); // 重置
// 用 7 填充索引大于等于 1 且小于 3 的元素
zeroes.fill(7, 1, 3); 
console.log(zeroes); // [0, 7, 7, 0, 0]; 
zeroes.fill(0); // 重置
// 用 8 填充索引大于等于 1 且小于 4 的元素
// (-4 + zeroes.length = 1) 
// (-1 + zeroes.length = 4) 
zeroes.fill(8, -4, -1); 
console.log(zeroes); // [0, 8, 8, 8, 0];
```

2. copyWithin() ： 会按照指定范围浅复制数组中的部分内容，然后将它们插入到指定索引开始的位置
第一个参数：插入的位置，默认为 0
第二个参数（可选） ： 开始复制的位置，默认为 0
第三个参数（可选） ： 结束复制的位置，默认为数组结尾

```js
let ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];

// 从 ints 中复制索引 0 开始的内容，插入到索引 5 开始的位置
// 在源索引或目标索引到达数组边界时停止
ints.copyWithin(5); 
console.log(ints); // [0, 1, 2, 3, 4, 0, 1, 2, 3, 4] 

// 从 ints 中复制索引 5 开始的内容，插入到索引 0 开始的位置
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(0, 5); 
console.log(ints); // [5, 6, 7, 8, 9, 5, 6, 7, 8, 9]

// 从 ints 中复制索引 0 开始到索引 3 结束的内容
// 插入到索引 4 开始的位置.
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(4, 0, 3); 
console.log(ints); // [0, 1, 2, 3, 0, 1, 2, 7, 8, 9] 

// JavaScript 引擎在插值前会完整复制范围内的值
// 因此复制期间不存在重写的风险
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(2, 0, 6); 
console.log(ints); // [0, 1, 0, 1, 2, 3, 4, 5, 8, 9] 

// 支持负索引值，与 fill()相对于数组末尾计算正向索引的过程是一样的
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(-4, -7, -3); 
console.log(ints); // [0, 1, 2, 3, 4, 5, 3, 4, 5, 6] 
```

copyWithin()静默忽略超出数组边界、零长度及方向相反的索引范围：

```js
// 索引过低，忽略
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(1, -15, -12); 
console.log(ints); // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]; 

// 索引过高，忽略
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(1, 12, 15); 
console.log(ints); // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]; 

// 索引反向，忽略
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(2, 4, 2); 
console.log(ints); // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]; 

// 索引部分可用，复制、填充可用部分
ints = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
ints.copyWithin(4, 7, 10) ;
console.log(ints); // [0, 1, 2, 3, 7, 8, 9, 7, 8, 9];
```

##### 3.2.2.7. 转换方法

1. valueOf() 返回的还是数组本身

2. toString() 返回由数组中每个值的等效字符串拼接而成的一个逗号分隔的字符串

```js
let colors = ["red", "blue", "green"]; // 创建一个包含 3 个字符串的数组
console.log(colors.toString()); // red,blue,green 
console.log(colors.valueOf()); // red,blue,green 
console.log(colors); // red,blue,green
```

3. toLocaleString() 方法也可能返回跟 toString() 和 valueOf() 相同的结果，但也不一定。在 调用数组的 toLocaleString() 方法时，会得到一个逗号分隔的数组值的字符串。它与另外两个方法唯一的区别是，为了得到最终的字符串，会调用数组每个值的 toLocaleString() 方法

##### *******. 栈方法

栈是一种后进先出（LIFO，Last-In-First-Out）的结构，也就是最近添加的项先被删除。数据项的插入（称为推入，push）和删除（称为弹出，pop）只在栈的一个地方发生，即栈顶。ECMAScript 数组提供了 push()和 pop()方法，以实现类似栈的行为。

1. push()方法：接收任意数量的参数，并将它们添加到数组末尾，返回数组的最新长度

2. pop()方法：则用于删除数组的最后一项，同时减少数组的 length 值，返回被删除的项。

```js
let colors = new Array(); // 创建一个数组
let count = colors.push("red", "green"); // 推入两项
console.log(count); // 2 
count = colors.push("black"); // 再推入一项
console.log(count); // 3 
let item = colors.pop(); // 取得最后一项
console.log(item); // black 
console.log(colors.length); // 2
```

##### *******. 队列方法
队列以先进先出（FIFO，First-In-First-Out）形式限制访问。队列在列表末尾添加数据，但从列表开头获取数据。使用 shift() 和 push()，可以把数组当成队列来使用。

1. push() 方法：接收任意数量的参数，并将它们添加到数组末尾，返回数组的最新长度

2. shift() 方法：它会删除数组的第一项并返回它，然后数组长度减 1。

```js
let colors = new Array(); // 创建一个数组
let count = colors.push("red", "green"); // 推入两项
console.log(count); // 2 
count = colors.push("black"); // 再推入一项
console.log(count); // 3 
let item = colors.shift(); // 取得第一项
console.log(item); // red 
console.log(colors.length); // 2
```

3. unshift() 方法：在数组开头添加任意多个值，然后返回新的数组长度。
  
通过使用 unshift() 和 pop()，可以在相反方向上模拟队列，即在数组开头添加新数据，在数组末尾取得数据。

```js
let colors = new Array(); // 创建一个数组
let count = colors.unshift("red", "green"); // 从数组开头推入两项
console.log(count); // 2 
count = colors.unshift("black"); // 再推入一项
console.log(count); // 3 
let item = colors.pop(); // 取得最后一项
console.log(item); // green 
console.log(colors.length); // 2
```

##### 3.2.2.10. 排序方法

1. reverse() 方法：将数组元素反向排列

```js
let values = [1, 2, 3, 4, 5];  
values.reverse();  
console.log(values); // 5,4,3,2,1 
```

2. sort() 方法：会按照升序重新排列数组元素，即最小的值在前面，最大的值在后面

未传递参数时，sort() 会在每一项上调用 String() 转型函数，然后比较字符串来决定顺序。即使数组的元素都是数值，也会先把数组转换为字符串再比较、排序。

```js
let values = [0, 1, 5, 10, 15]; 
values.sort(); 
//因为将所有数值都转换成了字符串，导致排序错误
console.log(values); // 0,1,10,15,5 
```
sort() 方法可以接收一个比较函数，用于判断哪个值应该排在前面。
比较函数接收两个参数：
* 如果第一个参数应该排在第二个参数前面，就返回负值；
* 如果两个参数相等，就返回 0；
* 如果第一个参数应该排在第二个参数后面，就返回正值。
* 
```js
function compare(value1, value2) { 
 if (value1 < value2) { 
 return -1; 
 } else if (value1 > value2) { 
 return 1; 
 } else { 
 return 0; 
 } 
}

let values = [0, 1, 5, 10, 15]; 
values.sort(compare); 
console.log(values); // 0,1,5,10,15

// 也可以使用箭头函数来作为参数
values = [0, 1, 5, 10, 15];
values.sort((a, b) => a < b ? 1 : a > b ? -1 : 0);
console.log(values); // 15,10,5,1,0
```

注意：reverse() 和 sort() 都返回调用它们的数组的引用。
——如果数组的元素是数值，或者是其 valueOf()方法返回数值的对象（如 Date 对象），这个比较函数还可以写得更简单，因为这时可以直接用第二个值减去第一个值：

```js
function compare(value1, value2){  
 return value2 - value1;  
} 
```

##### 3.2.2.11. 操作方法

1. concat()方法：在现有数组全部元素基础上创建一个新数组。

它首先会创建一个当前数组的副本，然后再把它的参数添加到副本末尾，最后返回这个新构建的数组。如果传入一个或多个数组，则 concat()会把这些数组的每一项都添加到结果数组。

```js
let colors = ["red", "green", "blue"]; 
let colors2 = colors.concat("yellow", ["black", "brown"]); 
console.log(colors); // ["red", "green","blue"] 
console.log(colors2); // ["red", "green", "blue", "yellow", "black", "brown"]
```

2. slice()方法：用于创建一个包含原有数组中一个或多个元素的新数组。

slice()方法可以接收一个或两个参数：返回元素的开始索引和结束索引。
如果只有一个参数，则 slice()会返回该索引到数组末尾的所有元素。
如果有两个参数，则 slice()返回从开始索引到结束索引对应的所有元素，其中不包含结束索引对应的元素。
注意 ：如果 slice()的参数有负值，那么就以数值长度加上这个负值的结果确定位置。

```js
let colors = ["red", "green", "blue", "yellow", "purple"]; 
let colors2 = colors.slice(1); 
let colors3 = colors.slice(1, 4); 
console.log(colors2); // green,blue,yellow,purple 
console.log(colors3); // green,blue,yellow
```

3. splice() 方法：主要目的是在数组中间插入元素

有三种不同的方式使用这个方法。
* **删除**。需要给 splice()传 2 个参数：删除的第一个元素的位置和要删除的元素数量。可以从数组中删除任意多个元素，比如 splice(0, 2) 会删除前两个元素。
* **插入**。需要给 splice() 传 3 个参数：开始位置、0（要删除的元素数量）和要插入的元素，可以在数组中指定的位置插入元素。第三个参数之后还可以传第四个、第五个参数，乃至任意多 个要插入的元素。比如，splice(2, 0,"red", "green") 会从数组位置 2 开始插入字符串 "red" 和 "green"。
* **替换**。splice() 在删除元素的同时可以在指定位置插入新元素，同样要传入 3 个参数：开始位置、要删除元素的数量和要插入的任意多个元素。要插入的元素数量不一定跟删除的元素数量一致。比如，splice(2, 1, "red", "green") 会在位置 2 删除一个元素，然后从该位置开始向数组中插入 "red" 和 "green"。

```js
let colors = ["red", "green", "blue"]; 
let removed = colors.splice(0,1); // 删除第一项
console.log(colors); // green,blue 
console.log(removed); // red，只有一个元素的数组
removed = colors.splice(1, 0, "yellow", "orange"); // 在位置 1 插入两个元素
console.log(colors); // green,yellow,orange,blue 
console.log(removed); // 空数组
removed = colors.splice(1, 1, "red", "purple"); // 插入两个值，删除一个元素
console.log(colors); // green,red,purple,orange,blue 
console.log(removed); // yellow，只有一个元素的数组
```

##### 3.2.2.12. 搜索和位置方法

1. 严格相等

提供了 3 个严格相等的搜索方法：indexOf()、lastIndexOf()和 includes()。
这些方法都接收两个参数：`要查找的元素`和`一个可选的起始搜索位置`。
在比较第一个参数跟数组每一项时，会使用全等（===）比较，也就是说两项必须严格相等。
* indexOf() ： 从数组前头（第一项）开始向后搜索，返回要查找的元素在数组中的位置，如果没找到则返回-1
* lastIndexOf() ： 从数组末尾（最后一项）开始向前搜索，返回要查找的元素在数组中的位置，如果没找到则返回-1。
* includes() ： 从数组前头（第一项）开始向后搜索，返回布尔值，表示是否至少找到一个与指定元素匹配的项。

```js
let numbers = [1, 2, 3, 4, 5, 4, 3, 2, 1]; 
console.log(numbers.indexOf(4)); // 3 
console.log(numbers.lastIndexOf(4)); // 5 
console.log(numbers.includes(4)); // true 
console.log(numbers.indexOf(4, 4)); // 5 
console.log(numbers.lastIndexOf(4, 4)); // 3 
console.log(numbers.includes(4, 7)); // false 
let person = { name: "Nicholas" }; 
let people = [{ name: "Nicholas" }]; 
let morePeople = [person]; 
console.log(people.indexOf(person)); // -1 
console.log(morePeople.indexOf(person)); // 0 
console.log(people.includes(person)); // false 
console.log(morePeople.includes(person)); // true
```

2. 断言函数

允许按照定义的断言函数搜索数组，每个索引都会调用这个函数。断言函数的返回值决定了相应索引的元素是否被认为匹配。
断言函数接收 3 个参数：元素、索引和数组本身。
其中元素是数组中当前搜索的元素，索引是当前元素的索引，而数组就是正在搜索的数组。断言函数返回真值，表示是否匹配。

* find()     :使用了断言函数,从数组的最小索引开始,返回第一个匹配的元素
* findIndex():使用了断言函数,从数组的最小索引开始,返回第一个匹配元素的索引。

这两个方法也都接收第二个可选的参数， 用于指定断言函数内部 this 的值。

```js
const people = [ 
 { 
 name: "Matt", 
 age: 27 
 }, 
 { 
 name: "Nicholas", 
 age: 29 
 } 
]; 
console.log(people.find((element, index, array) => element.age < 28)); 
// {27, Matt} 
console.log(people.findIndex((element, index, array) => element.age < 28)); 
// 0
```

##### 3.2.2.13. 迭代方法
 每个方法接收两个参数：以每一项为参数运行的函数， 以及可选的作为函数运行上下文的作用域对象（影响函数中 this 的值）。
 传给每个方法的函数接收 3 个参数：数组元素、元素索引和数组本身。(item, index, array)

* every()：对数组每一项都运行传入的函数，如果对每一项函数都返回 true，则这个方法返回 true。
* filter()：对数组每一项都运行传入的函数，函数返回 true 的项会组成数组之后返回。
* forEach()：对数组每一项都运行传入的函数，没有返回值。
* map()：对数组每一项都运行传入的函数，返回由每次函数调用的结果构成的数组。
* some()：对数组每一项都运行传入的函数，如果有一项函数返回 true，则这个方法返回 true。

```js
let numbers = [1, 2, 3, 4, 5, 4, 3, 2, 1]; 
let everyResult = numbers.every((item, index, array) => item > 2); 
console.log(everyResult); // false  ，因为并非每个元素都大于2

let someResult = numbers.some((item, index, array) => item > 2); 
console.log(someResult); // true， 因为数组中存在大于2 元素

let filterResult = numbers.filter((item, index, array) => item > 2); 
console.log(filterResult); // 3,4,5,4,3，返回数组中大于2的元素

let mapResult = numbers.map((item, index, array) => item * 2); 
console.log(mapResult); // 2,4,6,8,10,8,6,4,2，返回元素乘以2后的结果数组

numbers1.forEach((item, index, array) => { 
  // 执行某些操作 
  item = item + 1;
  console.log(item); 
  /*2
  3
  4
  5
  6
  5
  4
  3
  2*/
});
```

##### 3.2.2.14. 归并方法
reduce() 和 reduceRight()。这两个方法都会迭代数组的所有项，并在此基础上构建一个最终返回值。
* reduce() 方法：从数组第一项开始遍历到最后一项。
* reduceRight() 方法：从最后一项开始遍历至第一项。

——这两个方法都接收两个参数：对每一项都会运行的归并函数，以及可选的以之为归并起点的初始值。

传给 reduce() 和 reduceRight() 的函数接收 4 个参数：上一个归并值、当前项、当前项的索引和数组本身。(prev, cur, index, array)

这个函数返回的任何值都会作为下一次调用同一个函数的第一个参数。如果没有给这两个方法传入可选的第二个参数（作为归并起点值），则第一次迭代将从数组的第二项开始，因此传给归并函数的第一个参数是数组的第一项，第二个参数是数组的第二项。

```js
//使用 reduce()函数执行累加数组中所有数值的操作，比如：
let values = [1, 2, 3, 4, 5]; 
let sum = values.reduce((prev, cur, index, array) => prev + cur); 
console.log(sum); // 15

// reduceRight()方法与之类似，只是方向相反。来看下面的例子：
let values = [1, 2, 3, 4, 5]; 
let sum = values.reduceRight(function(prev, cur, index, array){ 
 return prev + cur; 
}); 
console.log(sum); // 15
```

#### 3.2.3. 定型数组
##### *******. ArrayBuffer

ArrayBuffer 是所有定型数组及视图引用的基本单位。

1. ArrayBuffer()是一个普通的 JavaScript 构造函数，可用于在内存中分配特定数量的字节空间。

```js
const buf = new ArrayBuffer(16); // 在内存中分配 16 字节
console.log(buf.byteLength); // 16
```

注意：ArrayBuffer 一经创建就不能再调整大小。

2. ArrayBuffer 某种程度上类似于 C++ 的 malloc()，但也有几个明显的区别。

* malloc()在分配失败时会返回一个 null 指针。ArrayBuffer 在分配失败时会抛出错误。
* malloc()可以利用虚拟内存，因此最大可分配尺寸只受可寻址系统内存限制。ArrayBuffer
分配的内存不能超过 Number.MAX_SAFE_INTEGER（2^53- 1）字节。
* malloc()调用成功不会初始化实际的地址。声明 ArrayBuffer 则会将所有二进制位初始化为 0。
* 通过 malloc()分配的堆内存除非调用 free()或程序退出，否则系统不能再使用。而通过声明 ArrayBuffer 分配的堆内存可以被当成垃圾回收，不用手动释放。

3. 不能仅通过对 ArrayBuffer 的引用就读取或写入其内容。要读取或写入 ArrayBuffer，就必须通过视图。视图有不同的类型，但引用的都是 ArrayBuffer 中存储的二进制数据。

##### 3.2.3.2. DataView

这个视图专为文件 I/O 和网络 I/O 设计，其 API 支持对缓冲数据的高度控制，但相比于其他类型的视图性能也差一些。DataView 对缓冲内容没有任何预设，也不能迭代。
必须在对已有的 ArrayBuffer 读取或写入时才能创建 DataView 实例。这个实例可以使用全部或部分 ArrayBuffer ,且维护着对该缓冲实例的引用，以及视图在缓冲中开始的位置。

```js
const buf = new ArrayBuffer(16); 
// DataView 默认使用整个 ArrayBuffer 
const fullDataView = new DataView(buf); 
console.log(fullDataView.byteOffset); // 0 
console.log(fullDataView.byteLength); // 16 
console.log(fullDataView.buffer === buf); // true 

// 构造函数接收一个可选的字节偏移量和字节长度
// byteOffset = 0 表示视图从缓冲起点开始
// byteLength = 8 限制视图为前 8 个字节
const firstHalfDataView = new DataView(buf, 0, 8); 
console.log(firstHalfDataView.byteOffset); // 0 
console.log(firstHalfDataView.byteLength); // 8 
console.log(firstHalfDataView.buffer === buf); // true 

// 如果不指定，则 DataView 会使用剩余的缓冲
// byteOffset=8 表示视图从缓冲的第 9 个字节开始
// byteLength 未指定，默认为剩余缓冲
const secondHalfDataView = new DataView(buf, 8); 
console.log(secondHalfDataView.byteOffset); // 8
console.log(secondHalfDataView.byteLength); // 8 
console.log(secondHalfDataView.buffer === buf); // true
```

要通过 DataView 读取缓冲，还需要几个组件。
* 首先是要读或写的字节偏移量。可以看成 DataView 中的某种“地址”。
* DataView 应该使用 ElementType 来实现 JavaScript 的 Number类型到缓冲内二进制格式的转换。
* 最后是内存中值的字节序。默认为大端字节序。

1 . ElementType

DataView 对存储在缓冲内的数据类型没有预设。它暴露的API 强制开发者在读、写时指定一个ElementType，然后 DataView 就会忠实地为读、写而完成相应的转换。

DataView 为上表中的每种类型都暴露了 get 和 set 方法，这些方法使用 byteOffset（字节偏移量）定位要读取或写入值的位置。

```js
// 在内存中分配两个字节并声明一个 DataView 
const buf = new ArrayBuffer(2); 
const view = new DataView(buf); 

// 说明整个缓冲确实所有二进制位都是 0 
// 检查第一个和第二个字符
console.log(view.getInt8(0)); // 0 
console.log(view.getInt8(1)); // 0 

// 检查整个缓冲
console.log(view.getInt16(0)); // 0 

// 将整个缓冲都设置为 1 
// 255 的二进制表示是 11111111（2^8 - 1）
view.setUint8(0, 255); 

// DataView 会自动将数据转换为特定的 ElementType 
// 255 的十六进制表示是 0xFF 
view.setUint8(1, 0xFF); 

// 现在，缓冲里都是 1 了
// 如果把它当成二补数的有符号整数，则应该是-1 
console.log(view.getInt16(0)); // -1
```

2. 字节序

“字节序”指的是计算系统维护的一种字节顺序的约定。DataView 只支持两种约定：大端字节序和小端字节序。

* 大端字节序：也称为“网络字节序”，最高有效位保存在第一个字节，而最低有效位保存在最后一个字节。
* 小端字节序：最低有效位保存在第一个字节，最高有效位保存在最后一个字节

对一段内存而言，DataView 是一个中立接口，它会遵循指定的字节序。DataView 的所有 API 方法都以大端字节序作为默认值，但接收一个可选的布尔值参数，设置为 true 即可启用小端字节序。

```js
// 在内存中分配两个字节并声明一个 DataView 
const buf = new ArrayBuffer(2); 
const view = new DataView(buf); 

// 填充缓冲，让第一位和最后一位都是 1 
view.setUint8(0, 0x80); // 设置最左边的位等于 1 
view.setUint8(1, 0x01); // 设置最右边的位等于 1
 
// 缓冲内容（为方便阅读，人为加了空格）
// 0x8 0x0 0x0 0x1 
// 1000 0000 0000 0001 
// 按大端字节序读取 Uint16 
// 0x80 是高字节，0x01 是低字节
// 0x8001 = 2^15 + 2^0 = 32768 + 1 = 32769 
console.log(view.getUint16(0)); // 32769 

// 按小端字节序读取 Uint16 
// 0x01 是高字节，0x80 是低字节
// 0x0180 = 2^8 + 2^7 = 256 + 128 = 384 
console.log(view.getUint16(0, true)); // 384 

// 按大端字节序写入 Uint16 
view.setUint16(0, 0x0004); 
// 缓冲内容（为方便阅读，人为加了空格）
// 0x0 0x0 0x0 0x4 
// 0000 0000 0000 0100 
console.log(view.getUint8(0)); // 0 
console.log(view.getUint8(1)); // 4 

// 按小端字节序写入 Uint16 
view.setUint16(0, 0x0002, true); 
// 缓冲内容（为方便阅读，人为加了空格）
// 0x0 0x2 0x0 0x0 
// 0000 0010 0000 0000 
console.log(view.getUint8(0)); // 2 
console.log(view.getUint8(1)); // 0
```

3.边界情形

DataView 完成读、写操作的前提是必须有充足的缓冲区，否则就会抛出 RangeError：

```js
const buf = new ArrayBuffer(6); 
const view = new DataView(buf); 
// 尝试读取部分超出缓冲范围的值
view.getInt32(4); 
// RangeError 
// 尝试读取超出缓冲范围的值
view.getInt32(8); 
// RangeError 
// 尝试读取超出缓冲范围的值
view.getInt32(-1); 
// RangeError 
// 尝试写入超出缓冲范围的值
view.setInt32(4, 123); 
// RangeError
DataView 在写入缓冲里会尽最大努力把一个值转换为适当的类型，后备为0。如果无法转换，则抛出错误：

const buf = new ArrayBuffer(1);  
const view = new DataView(buf);  
view.setInt8(0, 1.5);  
console.log(view.getInt8(0)); // 1  
view.setInt8(0, [4]);  
console.log(view.getInt8(0)); // 4  
view.setInt8(0, 'f');  
console.log(view.getInt8(0)); // 0  
view.setInt8(0, Symbol()); 
```

##### 3.2.3.3. 定型数组
定型数组是另一种形式的 ArrayBuffer 视图。它特定于一种 ElementType 且遵循系统原生的字节序。
创建定型数组的方式包括读取已有的缓冲、使用自有缓冲、填充可迭代结构，以及填充基于任意类型的定型数组。
另外，通过<ElementType>.from()和<ElementType>.of()也可以创建定型数组

```js
// 创建一个 12 字节的缓冲
const buf = new ArrayBuffer(12); 
// 创建一个引用该缓冲的 Int32Array 
const ints = new Int32Array(buf); 
// 这个定型数组知道自己的每个元素需要 4 字节
// 因此长度为 3 
console.log(ints.length); // 3

// 创建一个长度为 6 的 Int32Array 
const ints2 = new Int32Array(6); 
// 每个数值使用 4 字节，因此 ArrayBuffer 是 24 字节
console.log(ints2.length); // 6 
// 类似 DataView，定型数组也有一个指向关联缓冲的引用
console.log(ints2.buffer.byteLength); // 24 

// 创建一个包含[2, 4, 6, 8]的 Int32Array 
const ints3 = new Int32Array([2, 4, 6, 8]); 
console.log(ints3.length); // 4 
console.log(ints3.buffer.byteLength); // 16 
console.log(ints3[2]); // 6 

// 通过复制 ints3 的值创建一个 Int16Array 
const ints4 = new Int16Array(ints3); 
// 这个新类型数组会分配自己的缓冲
// 对应索引的每个值会相应地转换为新格式
console.log(ints4.length); // 4 
console.log(ints4.buffer.byteLength); // 8 
console.log(ints4[2]); // 6 

// 基于普通数组来创建一个 Int16Array 
const ints5 = Int16Array.from([3, 5, 7, 9]); 
console.log(ints5.length); // 4 
console.log(ints5.buffer.byteLength); // 8 
console.log(ints5[2]); // 7 

// 基于传入的参数创建一个 Float32Array 
const floats = Float32Array.of(3.14, 2.718, 1.618); 
console.log(floats.length); // 3 
console.log(floats.buffer.byteLength); // 12 
console.log(floats[2]); // 1.6180000305175781 
```

定型数组的构造函数和实例都有一个 BYTES_PER_ELEMENT 属性，返回该类型数组中每个元素的大小：

```js
console.log(Int16Array.BYTES_PER_ELEMENT); // 2 
console.log(Int32Array.BYTES_PER_ELEMENT); // 4 
const ints = new Int32Array(1), 
floats = new Float64Array(1); 
console.log(ints.BYTES_PER_ELEMENT); // 4 
console.log(floats.BYTES_PER_ELEMENT); // 8 
```
如果定型数组没有用任何值初始化，则其关联的缓冲会以 0 填充：


```js
const ints = new Int32Array(4); 
console.log(ints[0]); // 0 
console.log(ints[1]); // 0 
console.log(ints[2]); // 0 
console.log(ints[3]); // 0
```

#### 3.2.4. Map
##### 3.2.4.1. 基本API
1. 创建Map

使用 new 关键字和 Map 构造函数可以创建一个空映射：

```js
const m = new Map();
```

如果想在创建的同时初始化实例，可以给 Map 构造函数传入一个可迭代对象，需要包含键/值对数组。可迭代对象中的每个键/值对都会按照迭代顺序插入到新映射实例中：

```js
// 使用嵌套数组初始化映射
const m1 = new Map([ 
 ["key1", "val1"], 
 ["key2", "val2"], 
 ["key3", "val3"] 
]); 
console.log(m1.size); // 3 

// 使用自定义迭代器初始化映射
const m2 = new Map({ 
 [Symbol.iterator]: function*() { 
 yield ["key1", "val1"]; 
 yield ["key2", "val2"]; 
 yield ["key3", "val3"]; 
 } 
}); 
console.log(m2.size); // 3 

// 映射期待的键/值对，无论是否提供
const m3 = new Map([[]]); 
console.log(m3.has(undefined)); // true 
console.log(m3.get(undefined)); // undefined
```

2. 初始化之后可以使用以下API方法：

* set()   : 再添加键/值对。
* get()   : 进行查询键，返回实际值。
* has()   : 进行查询键，返回布尔值。
* size    : 通过size属性获取映射中的键/值对的数量。
* delete(): 删除值,只删除一个键/值对
* clear() : 删除值,清除这个映射实例中的所有键/值对

```js
const m = new Map(); 
console.log(m.has("firstName")); // false 
console.log(m.get("firstName")); // undefined 
console.log(m.size); // 0 
m.set("firstName", "Matt") 
 .set("lastName", "Frisbie"); 
console.log(m.has("firstName")); // true 
console.log(m.get("firstName")); // Matt 
console.log(m.size); // 2 
m.delete("firstName"); // 只删除这一个键/值对
console.log(m.has("firstName")); // false 
console.log(m.has("lastName")); // true 
console.log(m.size); // 1 
m.clear(); // 清除这个映射实例中的所有键/值对
console.log(m.has("firstName")); // false 
console.log(m.has("lastName")); // false 
console.log(m.size); // 0
```

set() 方法返回映射实例，因此可以把多个操作连缀起来，包括初始化声明：

```js
const m = new Map().set("key1", "val1");  
m.set("key2", "val2")  
 .set("key3", "val3");  
console.log(m.size); // 3 
```

3. Map 可以使用任何 JavaScript 数据类型作为键。Map 内部使用 SameValueZero 比较操作（ECMAScript 规范内部定义，语言中不能使用），基本上相当于使用严格对象相等的标准来检查键的匹配性。

```js
const m = new Map(); 
const functionKey = function() {}; 
const symbolKey = Symbol(); 
const objectKey = new Object(); 
m.set(functionKey, "functionValue"); 
m.set(symbolKey, "symbolValue"); 
m.set(objectKey, "objectValue"); 
console.log(m.get(functionKey)); // functionValue 
console.log(m.get(symbolKey)); // symbolValue 
console.log(m.get(objectKey)); // objectValue 
// SameValueZero 比较意味着独立实例不冲突
console.log(m.get(function() {})); // undefined
```

4. 与严格相等一样，在映射中用作键和值的对象及其他“集合”类型，在自己的内容或属性被修改时仍然保持不变：
```js
const m = new Map(); 
const objKey = {}, 
 objVal = {}, 
 arrKey = [], 
 arrVal = []; 
m.set(objKey, objVal); 
m.set(arrKey, arrVal); 
objKey.foo = "foo"; 
objVal.bar = "bar"; 
arrKey.push("foo"); 
arrVal.push("bar"); 
console.log(m.get(objKey)); // {bar: "bar"}  
console.log(m.get(arrKey)); // ["bar"]
```

##### 3.2.4.2. 顺序与迭代
Map 实例会维护键值对的插入顺序，因此可以根据插入顺序执行迭代操作。
映射实例可以提供一个迭代器 （Iterator），能以插入顺序生成 [key, value] 形式的数组。可以通过 entries() 方法（或者 Symbol.iterator 属性，它引用 entries()）取得这个迭代器：

```js
const m = new Map([ 
 ["key1", "val1"], 
 ["key2", "val2"], 
 ["key3", "val3"] 
]); 
console.log(m.entries === m[Symbol.iterator]); // true 
for (let pair of m.entries()) { 
 console.log(pair); 
} 
// [key1,val1] 
// [key2,val2] 
// [key3,val3] 
for (let pair of m[Symbol.iterator]()) { 
 console.log(pair); 
} 
// [key1,val1] 
// [key2,val2] 
// [key3,val3] 
```

因为 entries()是默认迭代器，所以可以直接对映射实例使用扩展操作，把映射转换为数组：

```js
const m = new Map([ 
 ["key1", "val1"], 
 ["key2", "val2"], 
 ["key3", "val3"] 
]); 
console.log([...m]); // [[key1,val1],[key2,val2],[key3,val3]] 
```

#### 3.2.5. WeakMap
##### 3.2.5.1. 基本API
1. 创建WeakMap
可以使用 new 关键字实例化一个空的 WeakMap：

```js
const wm = new WeakMap();  
```

弱映射中的键只能是 Object 或者继承自 Object 的类型，尝试使用非对象设置键会抛出 TypeError。值的类型没有限制。

如果想在初始化时填充弱映射，则构造函数可以接收一个可迭代对象，其中需要包含键/值对数组。 可迭代对象中的每个键/值都会按照迭代顺序插入新实例中：

```js
const key1 = {id: 1},  
 key2 = {id: 2}, 
 key3 = {id: 3}; 
// 使用嵌套数组初始化弱映射
const wm1 = new WeakMap([ 
 [key1, "val1"], 
 [key2, "val2"], 
 [key3, "val3"] 
]); 
console.log(wm1.get(key1)); // val1 
console.log(wm1.get(key2)); // val2 
console.log(wm1.get(key3)); // val3 
// 初始化是全有或全无的操作
// 只要有一个键无效就会抛出错误，导致整个初始化失败
const wm2 = new WeakMap([ 
 [key1, "val1"], 
 ["BADKEY", "val2"], 
 [key3, "val3"] 
]); 
// TypeError: Invalid value used as WeakMap key 
typeof wm2; 
```

2.初始化之后可以使用如下函数：
* set()    ：添加，添加键/值对，
* get()    ：查询，返回实际值。
* has()    ：查询，返回布尔值。
* delete() ：删除， 只删除一个键、值对。

```js
const wm = new WeakMap(); 
const key1 = {id: 1}, 
      key2 = {id: 2}; 
console.log(wm.has(key1)); // false 
console.log(wm.get(key1)); // undefined 
wm.set(key1, "Matt") 
  .set(key2, "Frisbie"); 
console.log(wm.has(key1)); // true 
console.log(wm.get(key1)); // Matt 
wm.delete(key1); // 只删除这一个键/值对
console.log(wm.has(key1)); // false 
console.log(wm.has(key2)); // true 
```

set()方法返回弱映射实例，因此可以把多个操作连缀起来，包括初始化声明：

```js
const key1 = {id: 1}, 
 key2 = {id: 2}, 
 key3 = {id: 3}; 
const wm = new WeakMap().set(key1, "val1");
wm.set(key2, "val2") 
 .set(key3, "val3"); 
console.log(wm.get(key1)); // val1 
console.log(wm.get(key2)); // val2 
console.log(wm.get(key3)); // val3
```

##### 3.2.5.2. 弱键
WeakMap 中“weak”表示弱映射的键是“弱弱地拿着”的。意思就是，这些键不属于正式的引用，不会阻止垃圾回收。

```js
const wm = new WeakMap();  
wm.set({}, "val");  
```

set()方法初始化了一个新对象并将它用作一个字符串的键。因为没有指向这个对象的其他引用， 所以当这行代码执行完成后，这个对象键就会被当作垃圾回收。然后，这个键/值对就从弱映射中消失了，使其成为一个空映射。在这个例子中，因为值也没有被引用，所以这对键/值被破坏以后，值本身也会成为垃圾回收的目标。

##### 3.2.5.3. 不可迭代键
因为 WeakMap 中的键/值对任何时候都可能被销毁，所以没必要提供迭代其键/值对的能力。

当然， 也用不着像 clear() 这样一次性销毁所有键/值的方法。WeakMap 确实没有这个方法。因为不可能迭代，所以也不可能在不知道对象引用的情况下从弱映射中取得值。即便代码可以访问 WeakMap 实例，也没办法看到其中的内容。

WeakMap 实例之所以限制只能用对象作为键，是为了保证只有通过键对象的引用才能取得值。如果允许原始值，那就没办法区分初始化时使用的字符串字面量和初始化之后使用的一个相等的字符串了。

##### *******. 使用弱映射
1. 私有变量:
弱映射造就了在 JavaScript 中实现真正私有变量的一种新方式。前提很明确：私有变量会存储在弱映射中，以对象实例为键，以私有成员的字典为值。
下面是一个示例实现：

```js
const wm = new WeakMap();  
class User {  
 constructor(id) {  
 this.idProperty = Symbol('id');  
 this.setId(id);  
 }  
 setPrivate(property, value) {  
 const privateMembers = wm.get(this) || {};  
 privateMembers[property] = value;  
 wm.set(this, privateMembers);  
 }  
 getPrivate(property) {  
 return wm.get(this)[property];  
 }  
 setId(id) {  
 this.setPrivate(this.idProperty, id);  
 }  
 getId() {  
 return this.getPrivate(this.idProperty);  
 }  
}  
const user = new User(123);  
console.log(user.getId()); // 123  
user.setId(456);  
console.log(user.getId()); // 456  
// 并不是真正私有的 
console.log(wm.get(user)[user.idProperty]); // 456  
```

慧眼独具的读者会发现，对于上面的实现，外部代码只需要拿到对象实例的引用和弱映射，就可以 取得“私有”变量了。为了避免这种访问，可以用一个闭包把 WeakMap 包装起来，这样就可以把弱映射与外界完全隔离开了：

```js
const User = (() => {  
 const wm = new WeakMap();  
 class User {  
 constructor(id) {  
 this.idProperty = Symbol('id'); 
this.setId(id); 
 } 
 setPrivate(property, value) { 
 const privateMembers = wm.get(this) || {}; 
 privateMembers[property] = value; 
 wm.set(this, privateMembers); 
 } 
 getPrivate(property) { 
 return wm.get(this)[property]; 
 } 
 setId(id) { 
 this.setPrivate(this.idProperty, id); 
 } 
 getId(id) { 
 return this.getPrivate(this.idProperty); 
 } 
 } 
 return User; 
})(); 
const user = new User(123); 
console.log(user.getId()); // 123 
user.setId(456); 
console.log(user.getId()); // 456
```

这样，拿不到弱映射中的健，也就无法取得弱映射中对应的值。虽然这防止了前面提到的访问，但整个代码也完全陷入了 ES6 之前的闭包私有变量模式。

2. DOM 节点元数据
因为 WeakMap 实例不会妨碍垃圾回收，所以非常适合保存关联元数据。来看下面这个例子，其中
使用了常规的 Map： 
```js
const m = new Map();  
const loginButton = document.querySelector('#login');  
// 给这个节点关联一些元数据 
m.set(loginButton, {disabled: true});  
```
假设在上面的代码执行后，页面被 JavaScript 改变了，原来的登录按钮从 DOM 树中被删掉了。但 由于映射中还保存着按钮的引用，所以对应的 DOM 节点仍然会逗留在内存中，除非明确将其从映射中删除或者等到映射本身被销毁。如果这里使用的是弱映射，如以下代码所示，那么当节点从 DOM 树中被删除后，垃圾回收程序就可以立即释放其内存（假设没有其他地方引用这个对象）：
```js
const wm = new WeakMap();  
const loginButton = document.querySelector('#login');  
// 给这个节点关联一些元数据 
wm.set(loginButton, {disabled: true}); 
```

#### 3.2.6. Set

##### *******. 基本API
1. 使用 new 关键字和 Set 构造函数可以创建一个空集合：

```js
const m = new Set();  
```

2. 如果想在创建的同时初始化实例，则可以给 Set 构造函数传入一个可迭代对象，其中需要包含插入到新集合实例中的元素：

```js
// 使用数组初始化集合 
const s1 = new Set(["val1", "val2", "val3"]);  
console.log(s1.size); // 3  
// 使用自定义迭代器初始化集合 
const s2 = new Set({  
 [Symbol.iterator]: function*() {  
 yield "val1";  
 yield "val2";  
 yield "val3";  
 }  
});  
console.log(s2.size); // 3  
```

3. 初始化之后，可使用以下API:
* add()   ：增加值。
* has()   : 查询值，返回布尔型。
* size    : 取得元素数量。
* delete(): 删除一个元素。
* clear() ：删除所有元素。

```js
const s = new Set();  
console.log(s.has("Matt")); // false  
console.log(s.size); // 0  
s.add("Matt")  
 .add("Frisbie");  
console.log(s.has("Matt")); // true  
console.log(s.size); // 2  
s.delete("Matt");  
console.log(s.has("Matt")); // false  
console.log(s.has("Frisbie")); // true  
console.log(s.size); // 1  
s.clear(); // 销毁集合实例中的所有值 
console.log(s.has("Matt")); // false  
console.log(s.has("Frisbie")); // false  
console.log(s.size); // 0  
```

4. add()返回集合的实例，所以可以将多个添加操作连缀起来，包括初始化。

```js
const s = new Set().add("val1"); 
s.add("val2") 
 .add("val3"); 
console.log(s.size); // 3
```

5. 集合也使用 SameValueZero 操作 （ECMAScript 内部定义，无法在语言中使用），基本上相当于使用严格对象相等的标准来检查值的匹配性。

```js
const s = new Set();  
const functionVal = function() {};  
const symbolVal = Symbol();  
const objectVal = new Object();  
s.add(functionVal);  
s.add(symbolVal);  
s.add(objectVal);  
console.log(s.has(functionVal)); // true  
console.log(s.has(symbolVal)); // true  
console.log(s.has(objectVal)); // true  
// SameValueZero 检查意味着独立的实例不会冲突 
console.log(s.has(function() {})); // false 
```

6. 与严格相等一样，用作值的对象和其他“集合”类型在自己的内容或属性被修改时也不会改变：

```js
const s = new Set();  
const objVal = {},  
      arrVal = [];  
s.add(objVal);  
s.add(arrVal);  
objVal.bar = "bar";  
arrVal.push("bar");  
console.log(s.has(objVal)); // true  
console.log(s.has(arrVal)); // true  
```

7. add()和 delete()操作是幂等的。delete()返回一个布尔值，表示集合中是否存在要删除的值：
```js
const s = new Set();  
s.add('foo');  
console.log(s.size); // 1  
s.add('foo');  
console.log(s.size); // 1  
// 集合里有这个值 
console.log(s.delete('foo')); // true  
// 集合里没有这个值 
console.log(s.delete('foo')); // false 
```

##### 3.2.6.2. 顺序与迭代
1. Set 会维护值插入时的顺序，因此支持按顺序迭代。

2. 集合实例可以提供一个迭代器（Iterator），能以插入顺序生成集合内容。可以通过 values() 方法及其别名方法 keys()（或者 Symbol.iterator 属性，它引用 values()）取得这个迭代器：

```js
const s = new Set(["val1", "val2", "val3"]);  
console.log(s.values === s[Symbol.iterator]); // true  
console.log(s.keys === s[Symbol.iterator]); // true  
for (let value of s.values()) {  
 console.log(value);  
}  
// val1  
// val2  
// val3  
for (let value of s[Symbol.iterator]()) {  
 console.log(value);  
}  
// val1  
// val2  
// val3
```

3. 因为 values() 是默认迭代器，所以可以直接对集合实例使用扩展操作，把集合转换为数组：

```js
const s = new Set(["val1", "val2", "val3"]);  
console.log([...s]); // ["val1", "val2", "val3"]  
```

4. 集合的 entries() 方法返回一个迭代器，可以按照插入顺序产生包含两个元素的数组，这两个元素是集合中每个值的重复出现：

```js
const s = new Set(["val1", "val2", "val3"]);  
for (let pair of s.entries()) {  
 console.log(pair);  
}  
// ["val1", "val1"]  
// ["val2", "val2"]  
// ["val3", "val3"]  
```

5. 如果不使用迭代器，而是使用回调方式，则可以调用集合的 forEach()方法并传入回调，依次迭代每个键/值对。传入的回调接收可选的第二个参数，这个参数用于重写回调内部 this 的值：

```js
const s = new Set(["val1", "val2", "val3"]);  
s.forEach((val, dupVal) => console.log(`${val} -> ${dupVal}`));  
// val1 -> val1  
// val2 -> val2  
// val3 -> val3
```

6. 修改集合中值的属性不会影响其作为集合值的身份：

```js
const s1 = new Set(["val1"]);  
// 字符串原始值作为值不会被修改 
for (let value of s1.values()) { 
value = "newVal"; 
 console.log(value); // newVal 
 console.log(s1.has("val1")); // true 
} 
const valObj = {id: 1}; 
const s2 = new Set([valObj]); 
// 修改值对象的属性，但对象仍然存在于集合中
for (let value of s2.values()) { 
 value.id = "newVal"; 
 console.log(value); // {id: "newVal"} 
 console.log(s2.has(valObj)); // true 
} 
console.log(valObj); // {id: "newVal"}
```

##### 3.2.6.3. 定义正式集合操作
唯一需要强调的就是集合的 API 对自身的简单操作。很多开发者都喜欢使用 Set 操作，但需要手动实现：或者是子类化 Set，或者是定义一 个实用函数库。要把两种方式合二为一，可以在子类上实现静态方法，然后在实例方法中使用这些静态方法。在实现这些操作时，需要考虑几个地方。

* 某些 Set 操作是有关联性的，因此最好让实现的方法能支持处理任意多个集合实例。
* Set 保留插入顺序，所有方法返回的集合必须保证顺序。
* 尽可能高效地使用内存。扩展操作符的语法很简洁，但尽可能避免集合和数组间的相互转换能够节省对象初始化成本。
* 不要修改已有的集合实例。union(a, b)或 a.union(b)应该返回包含结果的新集合实例。

```js
class XSet extends Set {  
  show() { 
    console.log("show"); 
  } 
}
 
const x = new XSet();
x.show();  //show
```

#### 3.2.7. WeakSet
“弱集合”（WeakSet）是一种新的集合类型。WeakSet 是 Set 的“兄弟”类型，其 API 也是 Set 的子集。WeakSet 中的“weak”（弱），描述的是 JavaScript 垃圾回收程序对待“弱集合”中值的方式。

##### *******. 基本API
1. 可以使用 new 关键字实例化一个空的 WeakSet：
```js
const ws = new WeakSet();  
```

2. 弱集合中的值只能是 Object 或者继承自 Object 的类型，尝试使用非对象设置值会抛出 TypeError。
如果想在初始化时填充弱集合，则构造函数可以接收一个可迭代对象，其中需要包含有效的值。可迭代对象中的每个值都会按照迭代顺序插入到新实例中：

```js
const val1 = {id: 1},  
 val2 = {id: 2},  
 val3 = {id: 3};  
// 使用数组初始化弱集合 
const ws1 = new WeakSet([val1, val2, val3]);  
console.log(ws1.has(val1)); // true  
console.log(ws1.has(val2)); // true  
console.log(ws1.has(val3)); // true  
```

```js
// 初始化是全有或全无的操作 
// 只要有一个值无效就会抛出错误，导致整个初始化失败 
const ws2 = new WeakSet([val1, "BADVAL", val3]);  
// TypeError: Invalid value used in WeakSet  
typeof ws2;  
// ReferenceError: ws2 is not defined  
// 原始值可以先包装成对象再用作值 
const stringVal = new String("val1");  
const ws3 = new WeakSet([stringVal]);  
console.log(ws3.has(stringVal)); // true
```

3. 初始化之后可以使用以下API:
* add() : 再添加新值.。
* has() : 查询，返回布尔值。 
* delete() ：删除，只删除一个值。 

```js  
const ws = new WeakSet();  
const val1 = {id: 1},  
 val2 = {id: 2};  
console.log(ws.has(val1)); // false  
ws.add(val1)
 .add(val2); 
console.log(ws.has(val1)); // true 
console.log(ws.has(val2)); // true 
ws.delete(val1); // 只删除这一个值
console.log(ws.has(val1)); // false 
console.log(ws.has(val2)); // true 
```

4. add()方法返回弱集合实例，因此可以把多个操作连缀起来，包括初始化声明：
```js
const val1 = {id: 1}, 
 val2 = {id: 2}, 
 val3 = {id: 3}; 
const ws = new WeakSet().add(val1); 
ws.add(val2) 
 .add(val3); 
console.log(ws.has(val1)); // true 
console.log(ws.has(val2)); // true 
console.log(ws.has(val3)); // true 
```

##### 3.2.7.2. 弱值

WeakSet 中“weak”表示弱集合的值是“弱弱地拿着”的。意思就是，这些值不属于正式的引用，不会阻止垃圾回收。
add()方法初始化了一个新对象，并将它用作一个值。因为没有指向这个对象的其他引用，所以当这行代码执行完成后，这个对象值就会被当作垃圾回收。然后，这个值就从弱集合中消失了，使其成为一个空集合：

```js
const ws = new WeakSet();  
const container = {  
 val: {}  
};  
ws.add(container.val);  
function removeReference() {  
 container.val = null;  
}  
```

这一次，container 对象维护着一个对弱集合值的引用，因此这个对象值不会成为垃圾回收的目标。不过，如果调用了 removeReference()，就会摧毁值对象的最后一个引用，垃圾回收程序就可以把这个值清理掉。

##### 3.2.7.3. 不可迭代值

因为 WeakSet 中的值任何时候都可能被销毁，所以没必要提供迭代其值的能力。当然，也用不着像 clear() 这样一次性销毁所有值的方法。WeakSet 确实没有这个方法。因为不可能迭代，所以也不可能在不知道对象引用的情况下从弱集合中取得值。即便代码可以访问 WeakSet 实例，也没办法看到其中的内容。WeakSet 之所以限制只能用对象作为值，是为了保证只有通过值对象的引用才能取得值。如果允许原始值，那就没办法区分初始化时使用的字符串字面量和初始化之后使用的一个相等的字符串了。

##### 3.2.7.4. 使用弱集合

相比于 WeakMap 实例，WeakSet 实例的用处没有那么大。不过，弱集合在给对象打标签时还是有价值的。
这里使用了一个普通 Set：

```js
const disabledElements = new Set();  
const loginButton = document.querySelector('#login');  
// 通过加入对应集合，给这个节点打上“禁用”标签 
disabledElements.add(loginButton);  
```

这样，通过查询元素在不在 disabledElements 中，就可以知道它是不是被禁用了。不过，假如元素从 DOM 树中被删除了，它的引用却仍然保存在 Set 中，因此垃圾回收程序也不能回收它。
为了让垃圾回收程序回收元素的内存，可以在这里使用 WeakSet：

```js
const disabledElements = new WeakSet();  
const loginButton = document.querySelector('#login');  
// 通过加入对应集合，给这个节点打上“禁用”标签 
disabledElements.add(loginButton);  
```

这样，只要 WeakSet 中任何元素从 DOM 树中被删除，垃圾回收程序就可以忽略其存在，而立即释放其内存（假设没有其他地方引用这个对象）。

#### 3.2.8. 迭代与扩展操作
有 4 种原生集合类型定义了默认迭代器：
* Array
* 所有定型数组
* Map
* Set

这意味着上述所有类型都支持顺序迭代，都可以传入 for-of 循环：

```js
let iterableThings = [  
 Array.of(1, 2),  
 Int16Array.of(3, 4),  
 new Map([[5, 6], [7, 8]]),  
 new Set([9, 10])  
];  
for (const iterableThing of iterableThings) {  
 for (const x of iterableThing) {  
 console.log(x);  
 }  
}  
// 1  
// 2  
// 3  
// 4  
// [5, 6]  
// [7, 8]  
// 9  
// 10  
```

这也意味着所有这些类型都兼容扩展操作符。扩展操作符在对可迭代对象执行浅复制时特别有用， 只需简单的语法就可以复制整个对象

```js
let arr1 =[1,2,3]
let arr2 = [...arr1]; 
console.log(arr1); // [1, 2, 3] 
console.log(arr2); // [1, 2, 3] 
console.log(arr1 === arr2); // false
```

对于期待可迭代对象的构造函数，只要传入一个可迭代对象就可以实现复制：

```js
let map1 = new Map([[1, 2], [3, 4]]);  
let map2 = new Map(map1);  
console.log(JSON.stringify([...map1.entries()])); // Map [[1 , 2], [3 , 4]]  
console.log(JSON.stringify([...map2.entries()])); // Map [[1 , 2], [3 , 4]] 
```

当然，也可以构建数组的部分元素：

```js
let arr1 = [1, 2, 3];  
let arr2 = [0, ...arr1, 4, 5];  
console.log(arr2); // [0, 1, 2, 3, 4, 5]  
```

浅复制意味着只会复制对象引用：

```js
let arr5 = [{}];  
let arr6 = [...arr5];  
arr5[0].foo = 'bar';  
console.log(JSON.stringify(arr6[0])); // {"foo":"bar"}
```

上面的这些类型都支持多种构建方法，比如 Array.of()和 Array.from()静态方法。在与扩展操 作符一起使用时，可以非常方便地实现互操作：

```js
let arr1 = [1, 2, 3]; 
// 把数组复制到定型数组
let typedArr1 = Int16Array.of(...arr1); 
let typedArr2 = Int16Array.from(arr1); 
console.log(typedArr1); // Int16Array [1, 2, 3] 
console.log(typedArr2); // Int16Array [1, 2, 3] 
// 把数组复制到映射
let map = new Map(arr1.map((x) => [x, 'val' + x])); 
console.log(JSON.stringify([...map.entries()])); // [[1,"val1"],[2,"val2"],[3,"val3"]] 
// 把数组复制到集合
let set = new Set(typedArr2); 
console.log(JSON.stringify([...set.entries()])); // [[1,1],[2,2],[3,3]] 
// 把集合复制回数组
let arr2 = [...set]; 
console.log(arr2); // [1, 2, 3]
```

提供了对 JavaScript 对象的操作方法，如属性的增删改查、对象的复制等。可以使用 Object.keys 获取对象的属性列表，使用 Object.assign 复制对象等。

提供了对数组进行操作的方法，如元素的增删改查、排序、过滤、遍历等。可以使用 array.push 向数组末尾添加元素，使用 array.pop 移除数组最后一个元素，使用 array.sort 对数组进行排序，使用 array.forEach 遍历数组等。


### 3.3. Math（数学函数）
PeSF提供数学计算相关的功能。包括数学运行的一些特殊的值、基本数学运算、幂运算、对数运算、三角函数运算。

#### 3.3.1. 值
PeSF提供以下特殊数学值：

* E      ：自然对数$E$的值
* LN10   ：$㏑10$的值
* LN2    ：$㏑2$的值  
* LOG10E ：$㏒_{10}E$ 的值xs
* LOG2E  ：$㏒_{2}E$  的值
* PI     ：圆周率$∏$的值
* SQRT1_2:根号$½$的值根号½
* SQRT2  :根号$2$的值

```js
console.log(Math.E);        //2.718281828459045
console.log(Math.LN10);     //2.302585092994046
console.log(Math.LN2);      //0.6931471805599453
console.log(Math.LOG10E);   //0.4342944819032518
console.log(Math.LOG2E);    //1.4426950408889634
console.log(Math.PI);       //3.141592653589793
console.log(Math.SQRT1_2);  //0.7071067811865476
console.log(Math.SQRT2);    //1.4142135623730951
```

#### 3.3.2. 基本运算
* abs(x)          : 绝对值，返回x的绝对值。
* ceil(x)         : 向上取整，返回大于或等于x的最小整数。
* floor(x)        : 向下取舍，返回小于或等于x的最大整数。
* fround(x)       : 单精度浮点数，返回一个数的单精度浮点数形式。
* hypot(x, y,...) : 平方和开方，返回参数的平方和的平方根。
* imul(x，y)      : 整数乘法，返回两个数以32位带符号整数形式相乘的结果。
* max(x, y, ...)  : 最大值，返回参数中的最大值。
* min(x, y, ...)  : 最小值，返回参数中的最小值。
* random(x)       : 随机数，返回0到1之间的随机数。
* round(x)        : 四舍五入，返回x的四舍五入值。
* trunc(x)        : 取整，返回x的整数部分。
* sign(x)         : 符号，返回x的符号，如果x为负数返回-1，如果x为正数返回1，如果x为0返回0。

```js
console.log(Math.abs(-10));           //10
console.log(Math.ceil(3.2));          //4
console.log(Math.clz32(2147483647));  //1
console.log(Math.floor(3.8));         //3
console.log(Math.fround(1.234));      //1.2339999675750732
console.log(Math.hypot(3,4));         //5
console.log(Math.imul(-2,3));         //-6
var max = Math.max(1,3,5,7,9);     
var min = Math.min(2,4,6,8,10);    
console.log(max);                     //9
console.log(min);                     //2
console.log(Math.random());           //0~1之间的随机值
console.log(Math.round(3.4));         //3
console.log(Math.round(3.5));         //4
console.log(Math.trunc(123.4567));    //123
console.log(Math.sign(-2));           //-1
```

#### 3.3.3. 幂运算
* cbrt(x)  : 返回x的立方根
* exp(x)   ：返回e的x次幂
* expm1(x) ：返回e^x次幂 - 1
* pow(x, y)：返回x的y次幂
* sqrt(x)  ： 返回x的平方根

```js
console.log(Math.cbrt(8));            //2
console.log(Math.exp(2));             //7.38905609893065
console.log(Math.expm1(2));           //6.38905609893065
console.log(Math.pow(2,3));           //8
console.log(Math.sqrt(16));           //4
```

#### 3.3.4. 对数运算
* log()  : 返回x的自然对数
* log1p()：返回1 + x的自然对数
* log10(): 返回x的以10为底的对数
* log2() : 返回x的以2为底的对数

```js
console.log(Math.log(Math.E));          //1
console.log(Math.log1p(Math.E - 1));    //1
console.log(Math.log10(10));            //1
console.log(Math.log2(4));              //2
```

#### 3.3.5. 三角函数运算
* acos(x)    : 反余弦，返回x的反余弦。
* acosh(x)   : 反双曲余弦，返回x的反双曲余弦值。
* asin(x)    : 反正弦，返回x的反正弦值。
* asinh(x)   : 反双曲正弦，返回x的反双曲正弦值。
* atan(x)    : 反正切，返回x的反正切值。
* atanh(x)   : 反双曲正切，返回x的反双曲正切值。
* atan2(y,x) : y/x的反正切，返回y/x的反正切值。
* cos(x)     : 余弦，返回x的余弦值。
* cosh(x)    : 双曲余弦，返回x的双曲余弦值。
* sin(x)     : 正弦，返回x的正弦值。
* sinh(x)    : 双曲正弦，返回x的双曲正弦值。
* tan(x)     : 正切，返回x的正切值。
* tanh(x)    : 双曲正切，返回x的双曲正切值。

```js
console.log(Math.acos(1/2));         //1.0471975511965979
console.log(Math.acosh(1));          //0
console.log(Math.asin(1/2));         //0.5235987755982989
console.log(Math.asinh(1/2));        //0.48121182505960347
console.log(Math.atan(1/2));         //0.4636476090008061
console.log(Math.atanh(1/2));        //0.5493061443340548
console.log(Math.atan2(1/2,1/2));    //0.7853981633974483
console.log(Math.cos(Math.PI / 3));  //0.5000000000000001
console.log(Math.cosh(Math.PI / 4)); //1.3246090892520057
console.log(Math.sin(Math.PI / 6));  //0.49999999999999994
console.log(Math.sinh(Math.PI / 4)); //0.8686709614860095
console.log(Math.tan(Math.PI / 4));  //0.9999999999999999
console.log(Math.tanh(Math.PI / 4)); //0.6557942026326724
```


### 3.4. Date（日期/时间）
提供了对日期 / 时间的操作方法。Date 类型采用自协调世界时（UTC,Universal Time Coordinated）的存储格式。这种格式可以精确表示 1970 年 1 月 1 日之前及之后 285616 年的精确时间。

#### 3.4.1. Date类型创建

使用 new 操作符来调用 Date 构造函数。直接可以获得当前的系统时间
例：
```js
let now = new Date();
console.log(now);
```
结果：
```
Thu Nov 30 2023 10:59:53 GMT+0800
```

#### 3.4.2. Date格式化方法

Date 类型提供几个专门用于格式化日期的方法，他们都会返回字符串。

##### 3.4.2.1. toDateString()

显示日期中的周几、月、日、年（使用XX XX XX XXXX格式）。
例：
```js
console.log(now.toDateString());
```
结果：
```
Thu Nov 30 2023
```

##### 3.4.2.2. toTimeString()

显示日期中的时、分、秒和时区（使用XX：XX：XX GMT+XXXX格式）。
例：
```js
console.log(now.toTimeString());
```

结果：
```
11:17:09 GMT+0800
```

##### 3.4.2.3. toLocaleDateString()

显示日期中的月、日、年（使用XX/XX/XXXX格式）。
例：
```js
console.log(now.toLocaleDateString());
```
结果：
```
11/30/2023
```

##### 3.4.2.4.  toLocaleTimeString()

显示日期中的时、分、秒、上下午（使用XX：XX：XX XX格式）。
例：
```js
console.log(now.toLocaleTimeString());
```
结果：
```
11:39:22 AM
```

##### 3.4.2.5.  toUTCString()

显示完整的 UTC 日期。
例：
```js
console.log(now.toUTCString());
```
结果：
```
Thu, 30 Nov 2023 03:51:16 GMT
```

#### 3.4.3.  Date 组件方法

组件方法直接涉及取得或设置日期值的特定部分。

##### 3.4.3.1. setTime(milliseconds)/getTime()

setTime : 设置日期的毫秒表示，修改整个日期。
getTime : 返回日期的毫秒表示。

例：
```js
now.setTime(123456789);
console.log(now.getTime())
```
结果：
```
123456789
```

##### 3.4.3.2. setFullYear(year)/getFullYear()

setFullYear : 设置日期的年，year 必须时 4 位数。
getFullYear : 返回 4 位数年
例：
```js
now.setFullYear(2023);
console.log(now.getFullYear())
```
结果：
```
2023
```

##### 3.4.3.3. setUTCFullYear(year)/getUTCFullYear()

setFullYear : 设置 UTC 日期的年，year 必须时 4 位数。
getFullYear ：返回 UTC 日期的 4 位数年
例：
```js
now.setUTCFullYear(2024);
console.log(now.getUTCFullYear())
```
结果：
```
2024
```

##### 3.4.3.4. setMonth(month)/getMonth()

setMonth : 设置日期的月。month 的范围是 0 ~ 11，0 代表 1 月，11 代表 12 月，大于 11 则加年。
getMonth ：返回日期的月。0 代表 1 月，11 代表 12 月。
例：
```js
console.log(now);
now.setMonth(13);
console.log(now);
console.log(now.getMonth())
```
结果： 
加年了
```
Tue Jan 02 2024 18:17:36 GMT+0800
Sun Feb 02 2025 18:17:36 GMT+0800
1
```

##### 3.4.3.5. setUTCMonth(month)/getUTCMonth()

setUTCMonth : 设置 UTC 日期的月。month 的范围是 0 ~ 11，0 代表 1 月，11 代表 12 月，大于 11 则加年。
getUTCMonth ：返回 UTC 日期的月。0 代表 1 月，11 代表 12 月。
例：
```js
now.setUTCMonth(12);
console.log(now.getUTCMonth())
```
结果： 
```
0
```

##### 3.4.3.6. setDate(date)/getDate()

setDate : 设置日期中的日。date 的范围是 1 ~ 31，如果 date 大于当月最大日期，则加月。
getDate : 返回日期中的日(1 ~ 31)。
例：
```js
console.log(now);
now.setDate(30)
console.log(now.getDate())
console.log(now);
```
结果： 
二月，加月了
```
Mon Feb 02 2026 18:17:36 GMT+0800
2
Mon Mar 02 2026 18:17:36 GMT+0800
```

##### 3.4.3.7. setUTCDate(date)/getUTCDate()

setDate : 设置 UTC 日期中的日。date 的范围是1 ~ 31，如果 date 大于当月最大日期，则加月。
getDate : 返回 UTC 日期中的日(1 ~ 31)。
例：
```js
console.log(now);
now.setUTCDate(31)
console.log(now.getUTCDate())
```
结果： 
```
Mon Mar 02 2026 18:17:36 GMT+0800
31
```

##### 3.4.3.8. getDay()/getUTCDay()

getDay : 返回日期值表示周几的数值（0 表示周日，6 表示周六）
getUTCDay : 返回UTC日期值表示周几的数值（0 表示周日，6 表示周六）
例：
```js
console.log(now.getDay())
console.log(now.getUTCDay())
```
结果： 
```
2
2
```

##### 3.4.3.9. setHours(hours)/getHours()

setHours : 设置日期中的时。hours 的范围是 0 ~ 23，如果 hours 大于 23,则加日。
getHours : 返回日期中的时(0 ~ 23)。
例：
```js
now.setHours(23)
console.log(now.getHours())
```
结果： 
```
23
```

##### 3.4.3.10. setUTCHours(hours)/getUTCHours() 

setUTCHours : 设置 UTC 日期中的时。hours 的范围是 0 ~ 23，如果 hours 大于 23,则加日。
getUTCHours : 返回 UTC 日期中的时(0 ~ 23)。
例：
```js
now.setUTCHours(24)
console.log(now.getUTCHours())
```
结果： 
```
0
```

##### 3.4.3.11. setMinutes(minutes)/getMinutes()

setMinutes : 设置日期中的分。minutes 的范围是 0 ~ 59，如果 minutes 大于 59,则加时。
getMinutes : 返回日期中的分(0 ~ 59)。
例：
```js
now.setMinutes(59)
console.log(now.getMinutes())
```
结果： 
```
59
```

##### 3.4.3.12. setUTCMinutes(minutes)/getUTCMinutes()

setUTCMinutes : 设置 UTC 日期中的分。minutes 的范围是0 ~ 59，如果 minutes 大于 59,则加时。
getUTCMinutes : 返回 UTC 日期中的分(0 ~ 59)。
例：
```js
now.setUTCMinutes(60)
console.log(now.getUTCMinutes())
```
结果： 
```
0
```

##### 3.4.3.13. setSeconds(seconds)/getSeconds()

setSeconds : 设置日期中的秒。seconds 的范围是 0 ~ 59，如果 seconds 大于 59,则加分。
getSeconds : 返回日期中的秒(0 ~ 59)。
例：
```js
now.setSeconds(59)
console.log(now.getSeconds())
```
结果： 
```
59
```

##### 3.4.3.14. setUTCSeconds(seconds)/getUTCSeconds()

setUTCSeconds : 设置 UTC 日期中的秒。seconds 的范围是0 ~ 59，如果 seconds 大于 59,则加分。
getUTCSeconds : 返回 UTC 日期中的秒(0 ~ 59)。
例：
```js
now.setUTCSeconds(60)
console.log(now.getUTCSeconds())
```
结果： 
```
0
```

##### 3.4.3.15. setMilliseconds(milliseconds)/getMilliseconds()

setMilliseconds : 设置日期中的毫秒。milliseconds 的范围是 0 ~ 999，如果 milliseconds 大于 999,则加秒。
getMilliseconds : 返回日期中的毫秒(0 ~ 999)。
例：
```js
now.setMilliseconds(999)
console.log(now.getMilliseconds())
```
结果： 
```
999
```

##### 3.4.3.16. setUTCMilliseconds(milliseconds)/getUTCMilliseconds()

setUTCMilliseconds : 设置 UTC 日期中的毫秒。milliseconds 的范围是 0 ~ 999，如果 milliseconds 大于 999,则加秒。
getUTCMilliseconds : 返回 UTC 日期中的毫秒(0~999)。
例：
```js
now.setUTCMilliseconds(1000)
console.log(now.getUTCMilliseconds())
```
结果： 
```
0
```

##### 3.4.3.17. getTimezoneOffset()

getTimezoneOffset : 返回以分钟计的 UTC 与本地时区的偏移量（如美国"东部标准时间"，返回 300；“北京时间”，返回 -480。）
例：
```js
console.log(now.getTimezoneOffset())
```
结果： 
```
-480
```


### 3.5. JSON（JSON 解析和序列化）
#### 3.5.1. JSON对象
JSON对象有两个方法：

* stringify()：将js序列化为JSON字符串；
* parse()：将JSON解析为js值。

1. JSON.stringify()把一个 JavaScript 对象序列化为一个 JSON 字符串：
默认情况下，JSON.stringify()会输出不包含空格或缩进的 JSON 字符串，jsonText 的值是这样的：
```js
const book = {
	title: "Professional JavaScript",
	authors:[
		"Nicholas C. Zakas", 
 		"Matt Frisbie"
	],
	edition: 4,
	year: 2017
};

let jsonText = JSON.stringify(book);
console.log(jsonText);
//{"title":"Professional JavaScript","authors":["Nicholas C. Zakas","Matt Frisbie"], "edition":4,"year":2017}

```


2. JSON 字符串可以直接传给 JSON.parse()，然后得到相应的 JavaScript 值：
```js
let bookCopy = JSON.parse(jsonText);
// bookCopy的数据格式和book一样
```

#### 3.5.2. 序列化选项
JSON.stringify()方法除了要序列化的对象，还可以接收两个参数。这两个参数可以用于指定其他序列化 JavaScript 对象的方式：

* 第一个参数是过滤器，可以是数组或函数；
* 第二个参数是用于缩进结果 JSON 字符串的选项。

##### *******. 过滤结果
1. 如果第二个参数是一个数组，那么JSON.stringify()返回的结果只会包含该数组中列出的对象属性：

```js
const book = { 
	title: "Professional JavaScript", 
	authors: [ 
		"Nicholas C. Zakas", 
		"Matt Frisbie" 
	], 
	edition: 4, 
	year: 2017 
};

let jsonText = JSON.stringify(book, ["title", "edition"]);
console.log(jsonText);
// jsonText: {"title":"Professional JavaScript","edition":4}
```

2. 如果第二个参数是一个函数，则行为又有不同。提供的函数接收两个参数：属性名（key）和属性
值（value）。可以根据这个 key 决定要对相应属性执行什么操作。这个 key 始终是字符串，只是在值不属于某个键/值对时会是空字符串（注意，返回 undefined 会导致属性被忽略。）：

```js
const book = { 
	title: "Professional JavaScript", 
	authors: [ 
		"Nicholas C. Zakas", 
		"Matt Frisbie" 
	], 
	edition: 4, 
	year: 2017 
};

let jsonText = JSON.stringify(book, (key, value) => {
    switch (key) {
        case "authors":
            return value.join(",");
        case "year":
            return 5000;
        case "edition":
            return undefined;
        default:
            return value;
    }
});
console.log(jsonText);

// 结果如下
//{"title":"Professional JavaScript","authors":"Nicholas C. Zakas,Matt Frisbie","year":5000}
```

##### *******. 字符串缩进
1. JSON.stringify()方法的第三个参数控制缩进和空格。在这个参数是数值时，表示每一级缩进的
空格数。例如，每级缩进 4 个空格，可以这样：

```js
const book = { 
	title: "Professional JavaScript", 
	authors: [ 
		"Nicholas C. Zakas", 
		"Matt Frisbie" 
	], 
	edition: 4, 
	year: 2017 
};

let jsonText = JSON.stringify(book, null, 4);
console.log(jsonText);
// jsonText 格式如下
/*{ 
    "title": "Professional JavaScript", 
    "authors": [ 
        "Nicholas C. Zakas", 
        "Matt Frisbie" 
    ], 
    "edition": 4, 
    "year": 2017 
}*/
```

注意，除了缩进，JSON.stringify()方法还为方便阅读插入了换行符。这个行为对于所有有效的缩进参数都会发生。最大缩进值为 10，大于 10 的值会自动设置为 10。

2. 如果缩进参数是一个字符串而非数值，那么 JSON 字符串中就会使用这个字符串而不是空格来缩进：

```js
const book = { 
	title: "Professional JavaScript", 
	authors: [ 
		"Nicholas C. Zakas", 
		"Matt Frisbie" 
	], 
	edition: 4, 
	year: 2017 
};

let jsonText = JSON.stringify(book, null, "--");
console.log(jsonText);
// 结果如下
/*{ 
--"title": "Professional JavaScript", 
--"authors": [ 
----"Nicholas C. Zakas", 
----"Matt Frisbie" 
--], 
--"edition": 4, 
--"year": 2017 
}*/
```

注意，使用字符串时同样有 10 个字符的长度限制。如果字符串长度超过 10，则会在第 10 个字符处截断。

#### 3.5.3. 解析选项
JSON.parse()方法也可以接收一个额外的参数，是一个函数，该函数也接收两个参数，属性名（key）和属性值（value），另外也需要返回值。

如果此函数返回 undefined，则结果中就会删除相应的键。如果返回了其他任何值，则该值就
会成为相应键的值插入到结果中。该函数经常被用于把日期字符串转换为 Date 对象。例如：

```js
const book = { 
	title: "Professional JavaScript", 
	authors: [ 
		"Nicholas C. Zakas", 
		"Matt Frisbie" 
	], 
	edition: 4, 
	year: 2017,
	releaseDate: new Date(2022, 4, 3)
};
let jsonText = JSON.stringify(book);
let bookCopy = JSON.parse(jsonText, (key, value) => key == "releaseDate" ? new Date(value) : value);
console.log(bookCopy.releaseDate.getFullYear()); // 2022
```


### 3.6. RegExp（正则表达式）

PeSF 通过 RegExp 类型支持正则表达式。 提供了对字符串进行匹配验证的功能。

#### 3.6.1. 正则表达式的创建

正则表达式使用类似 Perl 的简洁语法来创建：
```js
let expression =/pattern/flags；
```
这个正则表达式的 pattern (模式)可以是任何简单或复杂的正则表达式，包括字符类、限定符、分组、向前查找和反向引用。
每个正则表达式可以带零个或多个 flags (标记)，用于控制正则表达式的行为。

#### 3.6.2. 匹配模式的标记

下面给出匹配模式的标记：

##### 3.6.2.1.  g : 全局模式

表示查找字符串的全部内容，而不是找到第一个匹配的内容就结束。
例：
```js
let text = "abcdefghijklmnopqrstuovwxyz"
let pattern1 = /o/g;
console.log(pattern1.exec(text));
console.log(pattern1.exec(text));
console.log(pattern1.exec(text));
```
结果:
```
o
o
null
```

##### 3.6.2.2.  i : 不区分大小写

表示在查找匹配时忽略 pattern 和字符串的大小写
例：
```js
let text = "abcdefghijklmnopqrstuvwxyz"
let pattern2 = /Q/i;
console.log(pattern2.exec(text));
```
结果:
```
q
```

##### 3.6.2.3.  m : 多行模式

表示查找到一行文本的末尾时会继续查找。即使用边界符 ^ 或 $ 匹配每一行的开头或结尾时，以行作为开头和结尾的分割。而不是整个字符串的开头和结尾。

例：
```js
let text2 = "abcdefgh\nadcijklmnopqrstuvwxyz"
let pattern3 = /^a.*c/mg;
console.log(pattern3.exec(text2));
console.log(pattern3.exec(text2));
```
结果:
```
abc
adc
```

##### 3.6.2.4.  y : 粘附模式

表示只查找从 lastIndex 开始及之后的字符串。

例：
```js
let text = "abcdefghijklmnopqrstuvwxyz"
let pattern4 = /abc/y;
pattern4.lastIndex = 5;
console.log(pattern4.lastIndex);
console.log(pattern4.exec(text));
pattern4.lastIndex = 0;
console.log(pattern4.lastIndex);
console.log(pattern4.exec(text));

```
结果:
```
5
null
0
abc

```

##### 3.6.2.5.  u : Unicode 模式

启用 Unicode 匹配。

例：
```js
let text3 = "测试用的文字"
let pattern5 = /文字/u;
console.log(pattern5.exec(text3));
```
结果:
```
文字

```

##### 3.6.2.6.  s : dotAll 模式

表示元字符.匹配任何字符（包括 \n ）。
默认情况下圆点 . 是匹配除换行符 \n 之外的任何字符，加上 s 修饰符之后， . 中包含换行符 \n 

例：
```js
let text4 = "abc\ncdefg"
let pattern6 = /.cd/;
console.log(pattern6.exec(text4));
let pattern7 = /.cd/s;
console.log(pattern7.exec(text4));

```
结果:
```
null

cd
```

#### 3.6.3. RegExp 实例属性

每个 RegExp 实例都有下列属性，提供有关模式的各方面信息。
* global : 布尔值，表示是否设置了 g 标记。
* ignoreCase : 布尔值，表示是否设置了 i 标记。
* unicode : 布尔值，表示是否设置了 u 标记。
* sticky : 布尔值，表示是否设置了 y 标记。
* lastIndex : 整数，表示在源字符串下一次搜索的开始位置，始终从 0 开始。
* multiline : 布尔值，表示是否设置了 m 标记。
* dotAll : 布尔值，表示是否设置了 s 标记。
* source : 正则表达式的字面量字符串，没有开头和结尾的斜杠。
* flags : 正则表达式的标记字符串。始终以字面量而非传入构造函数的字符串模式形式返回（没有前后的斜杠）。

例：
```js
let pattern8 = /\[bc\]at/i;
console.log(pattern8.global);
console.log(pattern8.ignoreCase);
console.log(pattern8.multiline);
console.log(pattern8.lastIndex);
console.log(pattern8.source);
console.log(pattern8.flags);
```
结果
```
false
true
false
0
\[bc\]at
i
```

#### 3.6.4. RexExp 实例方法

##### 3.6.4.1. exec() 实例方法

RexExp 实例的主要方法是 exec(),主要配合捕获使用。这个方法只接收一个参数，即要应用的字符串。
如果找到了匹配项，则返回包含第一个匹配信息的数组；如果没找到匹配项，则返回 null。
返回的数组是Array的实例，包含两个额外属性：index 和 input。
* index 是字符串中匹配模式的起始位置
* input 是要查找的字符串。

例：
```js
let string = "abcdefg"
let patt1 = /q/g;
let patt2 = /c/;

console.log(patt1.exec(string));
console.log(patt2.exec(string));

let matches = patt2.exec(string);
console.log(matches.input);
console.log(matches.index);
```
结果：
```
null
c
abcdefg
2
```

##### 3.6.4.2.  test() 实例方法

RexExp 实例的另一个方法是 test（），接收一个字符串参数。如果输入的文本与模式匹配，则参数返回 true，否则返回 false。这个方法适用于只想测试模式是否匹配，而不需要实际匹配内容的情况。 test（）经常用在 if 语句中。

例：
```js
let string = "abcdefg"
let patt1 = /q/g;
let patt2 = /c/;
console.log(patt1.test(string));
console.log(patt2.test(string));
```
结果：
```
false
true
```


### 3.7. Function（函数）
#### 3.7.1. 基本功能
PeSF支持ES6的函数全部基本功能，包括函数声明、函数调用、函数参数、函数返回值、函数的作用域等等。自行参考JavaScript ES6标准

```js
var g = 0;
//函数声明
function fun(a,b){
    var g = 1;
    //函数返回值
    return a + b;
}
//函数调用
console.log(fun(1,2)); //3
//函数作用域
console.log(g) //0 
```

#### 3.7.2. 箭头函数
PeSF支持箭头函数的使用。箭头函数实例化的函数对象与正式的函数表达式创建的函数对象行为是相同的。任何可以使用函数表达式的地方，都可以使用箭头函数。

```js
let arrowSum = (a,b)=>{
    return a + b;
}
let Sum = function(a,b){
    return a + b;
}

console.log(arrowSum(3,5)); //8
console.log(Sum(3,5)); //8
```

#### 3.7.3. 函数内部
##### *******. arguments
1.函数内部可以访问arguments对象，从中取的传进来的每个参数值。arguments对象是一个类数组对象，因此可以使用中括号语法访问其中的元素。可以通过arguments.length属性的获得传进来的参数个数。

```js
console.log("arguments:") 
function sum(num){
    var ret = 0;
    for(var i = 0; i < arguments.length; i++){
        ret += arguments[i];
    }
    
    return ret;
}
console.log(sum(1,2,3,4,5)); //15
```

##### 3.7.3.2. new.target
函数可以作为构造函数实例化一个新对象，也可以作为普通函数被调用。
检查函数是否使用new关键字调用的，可以使用new.target属性。

```js
function King(){
    if(new.target){
        console.log('new');
    }
    else{
        console.log('not new');
    }
}

King();      //not new
new King();  //new
```

#### 3.7.4. 函数的属性与方法
每个函数都有自己的属性和方法:
* length          :返回函数的参数个数。
* name            :返回函数的名称
* prototype       :允许向对象添加属性和方法
* apply()         :调用一个函数，并指定其this值和参数数组
* bind()          :创建一个新函数，其中this关键字设置为提供的值，并在调用时有一个给定的初始参数列表。
* call()          :调用一个函数，其this值是指定的值，并且参数是以逗号分隔的参数列表。
* toString()      :返回表示函数源代码的字符串
* toLocaleString():返回表示函数源代码的本地化字符串。
* ValueOf()       :返回函数本身
* constructor     :返回对创建此对象的数组函数的引用

```js
console.log("函数属性与方法：");
console.log(King.length); //0
console.log(King.name);   //King
console.log(JSON.stringify(King.prototype)); //{}
King.prototype = {p1:2}; 
King.prototype.p2 = 3;
console.log(JSON.stringify(King.prototype)); //{"p1":2,"p2":3}

function sum2(a,b){
    return a + b;
}
const numbers = [1,2];
console.log(sum2.apply(null,numbers));  //3
console.log(sum2.call(null,2,3));       //5

function Hello(name){
    console.log('你好,' + name + '!');
}
const HelloBob = Hello.bind(null,"Bob");
HelloBob();       //Hello,Bob!

console.log(Hello.toString());        //Hello函数全部字符串
console.log(Hello.toLocaleString());  //Hello函数全部字符串
console.log(Hello.valueOf());         //Hello函数全部字符串

function Person(name){
    this.name = name;
}
const john = new Person('John');
console.log(john.constructor);         //返回john的构造函数
```

#### 3.7.5. 闭包  
闭包是指在一个函数内部定义的函数，它可以访问到该函数的变量，甚至是在该函数执行完毕之后仍然可以访问到这些变量。换句话说，闭包可以“记住”并访问其词法作用域，即使它在该作用域外部执行。

下面是一个简单的闭包示例：

```js
function outerFunction() {
  let outerVariable = 'I am from outerFunction';
  
  function innerFunction() {
    console.log(outerVariable);
  }
  
  return innerFunction;
}

const closure = outerFunction();
closure(); // I am from outerFunction
```

在这个例子中，innerFunction 是一个闭包，它可以访问 outerFunction 中定义的 outerVariable。即使 outerFunction 执行完毕后，我们依然可以通过调用 closure() 来访问 outerVariable。

闭包在 JavaScript 中有着广泛的应用，它可以用于封装私有变量、模块化代码、实现柯里化等。然而，过度使用闭包也可能导致内存泄漏问题，因此在使用闭包时需要谨慎考虑。


#### 3.7.6. 自执行函数
自执行函数，是一种在定义后立即执行的 JavaScript 函数。这种函数的语法形式是在函数定义后紧跟一对括号，将函数包裹起来，并在末尾再加上一对括号来立即执行函数。

下面是一个简单的自执行函数的示例：

```js
(function() {
  console.log('This is a self-executing function');
})(); //This is a self-executing function
```

在这个例子中，我们定义了一个匿名函数，并立即执行它。这种方式可以用于创建一个独立的作用域，避免污染全局命名空间，也可以用于初始化代码。

自执行函数还可以接受参数，例如：

```js
(function(name) {
  console.log('Hello, ' + name);
})('Alice'); //Hello, Alice
```

这种写法可以让我们在定义函数的同时立即传入参数并执行函数。
自执行函数在 JavaScript 中被广泛应用，特别是在模块化开发和命名空间隔离方面。通过自执行函数，可以有效地控制变量的作用域，避免变量污染和冲突。

#### 3.7.7. 异步函数

异步函数是指在执行过程中不会阻塞程序执行的函数。在 JavaScript 中，异步函数通常使用回调函数、Promise 对象或 async/await 关键字来实现。

1. 回调函数是一种常见的异步编程方式，它可以在函数执行完毕后将结果传递给另一个函数进行处理。例如：

```js
function fetchData(callback) {
    setTimeout(function() {
      const data = {name: 'Alice', age: 30};
      callback(data);
    }, 1000);
}
  
function processData(data) {
    console.log('Name: ' + data.name + ', Age: ' + data.age);
}
  
fetchData(processData);  //延迟1秒后： Name: Alice, Age: 30
```

在这个例子中，fetchData 函数使用 setTimeout 模拟异步操作，并在操作完成后将结果传递给回调函数 processData 进行处理。

2. Promise 对象是另一种常见的异步编程方式，它可以用于处理异步操作的成功或失败情况。例如：

```js
const  fetchData = ()=> {
    return new Promise((resolve, reject) => {
      setTimeout(()=> {
        const data = {name: 'Alice', age: 31};
        resolve(data);
      }, 2000);
    });
}
  
fetchData().then((data)=> {
    console.log('Name: ' + data.name + ', Age: ' + data.age);
});
//延迟2秒后： Name: Alice, Age: 31
```

在这个例子中，fetchData 返回一个 Promise 对象，并在异步操作完成后使用 resolve 方法将结果传递给 then 方法进行处理。then函数与Promise对象使用方法参考ES6。

3. async/await 关键字是可以让异步代码看起来像同步代码。例如：

```js
async function fetchData() {
    return new Promise(function(resolve, reject) {
      setTimeout(function() {
        const data = {name: 'Alice', age: 32};
        resolve(data);
      }, 3000);
    });
}
  
async function processData() {
    const data = await fetchData();
    console.log('Name: ' + data.name + ', Age: ' + data.age);
}
  
processData(); //延迟3秒后：Name: Alice, Age: 32
```

在这个例子中，fetchData 函数返回一个 Promise 对象，而 processData 函数使用 async/await 关键字来处理异步操作，使代码看起来更加简洁和易读。


### 3.8. HTTP 支持

PeSF 对 HTTP 请求提供了基本的支持。它可以发送各种类型的 HTTP 请求，如 GET、POST、PUT、DELETE 等。这些请求可以用于与远程服务器进行通信，发送数据，获取响应等。

### 3.9. bridge 模块

#### 3.9.1. 为什么要有 bridge 模块？

当 APP 程序需要响应一些外部事件时，比如面板的按键、接收网络消息等等。这些事件由外部触发，传递到 PeSF APP 程序。Bridge 模块就是负责实现外部事件传递到 PeSF APP 程序。

#### 3.9.2. bridge 模块如何实现？

如下图所示：

1. PeSF 程序通过 IPC 机制与外部模块通信，比如面板、网络、USB 等等。哪种 IPC 机制可以自由根据项目情况选定。
2. PC 模块通过调用 bridge 接口函数，将事件响应的字符串，传递给 PeSF 运行时的事件队列。
3. PeSF 运行时会逐个处理事件队列，从而调用 PeSF APP 中注册的回调函数，将事件传递给 PeSF APP。

![bridge 模块结构图](image/bridge模块.jpg)
Bridge 模块结构图

#### 3.9.3. bridge 模块为什么要使用 PeSF 运行时的事件队列？

如果不使用 PeSF 运行时的事件队列，IPC 的事件响应会立即调用 PeSF App 中的回调函数。当多事件同时发生时，会引起时序混乱。引入事件队列功能，各个外部事件会进入事件队列，并按顺序处理。

#### 3.9.4. bridge 模块函数

##### *******. runtime_send_to_brige

```
void runtime_send_to_brige(runtime_t *rt int argc, JSValueConst *argv)
```

runtime_send_to_brige 函数是 PeSF 运行时中使用的 C 语言函数，提供给 PeSF 运行时的开发者使用。
例：

```js
runtime_t rt;
// 自定义需要传递的字符串。
JSValue msg = JS_NewString(rt.qjs_ctx, "IPC_message");
JSValue args[] = {msg};
// 通过bridge 发送给 PeSF 运行时内 PeSF App
runtime_send_to_brige(&rt, 1, args);
// 是否 JS 变量的引用计数
JS_FreeValue(rt.qjs_ctx, msg);
```

##### *******. on_msg 函数

bridge.on_msg 是提供给 PeSF API 开发者注册的回调函数，JS API 开发者可以通过注册 bridge 模块的 on_msg 的回调函数，接收 PeSF 运行时外传递过来的数据。

```js
bridge.on_msg = (...infos)=> {
    // Do Something

    if(infos[0] === "IPC_message") {
        console.log("Recive IPC_message\n");
    }

    // Do Something
}
```

##### *******. bridge 模块应用举例

下文是一个 bridge 模块的应用举例，该例子通过使用信号量 SIGUSR1 与 SIGUSR2 作为 IPC 的触发方式，通过bridge 模块传递自定义字符串给 PeSF APP，PeSF App 程序使用 on_msg 函数接收字符串，并进行分发处理。

1. 注册两个信号量的回调函数 handler, 在handler回调函数中，根据不同的信号量，传递不同的字符串给untime_send_to_bridge 函数：

    ```c
    #include <signal.h>
    #include <sys/wait.h>
    #include <unistd.h>

    void call_send_bridge(const char *str)
    {
        JSValue msg = JS_NewString(rt.qjs_ctx, str);
        JSValue args[] = {msg};

        runtime_send_to_bridge(&rt, 1, args);

        JS_FreeValue(rt.qjs_ctx, msg);
    }

    void handler(int signo)
    {
        switch (signo){
            case SIGUSR1:
                call_send_bridge("SIGUSR1");
                break;
            case SIGUSR2:
                call_send_bridge("SIGUSR2");  
                break;
        }  
    }

    int main(int argc, char **argv)
    {
        // ......

        // 注册信号量回调函数, 实际项目中使用哪一种 IPC 方式根据项目而定
        if(signal(SIGUSR1,handler) == SIG_ERR){
            printf("set sigusr1 fail\n");
        }

        // 注册信号量回调函数, 实际项目中使用哪一种 IPC 方式根据项目而定
        if(signal(SIGUSR2,handler) == SIG_ERR){
            printf("set sigusr2 fail\n");
        }

        // ......
    }
    ```

2. PeSF APP 处理如下,通过判断接收到的参数，分发处理：

    ```js
    bridge.on_msg = (...infos)=> {
        // 数据会以多个参数的形式传入，这里的处理仅仅是举例，实际项目在这基础上封装
        for (let info of infos) {
            console.log(info);
        }
        if(infos[0] === "SIGUSR1") {
            console.log("Do SIGUSR1 Action...\n");
        } 

        if(infos[0] === "SIGUSR2") {
            console.log("Do SIGUSR2 Action...\n");
        } 

    }
    ```

3. 运行结果如下：

    通过 PeSF 运行时启动 JS 文件，并向 PeSF 运行时发送信号量 SIGUSR1

    PeSF App 打印出接收到的参数，并分发处理。

    ```shell
    SIGUSR1
    Do SIGUSR1 Action...
    ```

**bridge 模块的 IPC 方式可以自由选定，请根据项目的实际情况选择。**

### 3.10. Process

提供查询和控制 PeSF 运行时信息的方法。

#### 3.10.1. exit 结束 PeSF App

调用 `process.exit()` 函数会调用注册给PeSF的退出函数，并传出一个结束参数。

### 3.11. 定时功能

#### 3.11.1. setTimeout

设置一个超时定时器，到达超时时间之后运行指定函数，时间单位是毫秒，返回值是定时器 id 。

```javascript
// 1秒后打印 timeout
const id = setTimeout(()=>console.log("timeout"), 1000);
```

#### 3.11.2. clearTimeout

清除一个超时定时器。

```javascript
const id = setTimeout(()=>console.log("timeout"), 1000);
clearTimeout(id);
```

#### 3.11.3. setInterval

设置一个间隔定时器，每隔一个时间间隔运行指定函数，时间单位是毫秒，返回值是定时器 id 。

```javascript
// 设定间隔1秒打出心跳
const id = setInterval(()=>console.log("heatbeat"), 1000);
```

#### 3.11.4. clearInterval

清除一个间隔定时器。

```javascript
const id = setInterval(()=>console.log("heatbeat"), 1000);
clearInterval(id)
```

## 4. PeSF API

PeSF 提供了一些打印机设备 API，为 PeSF 应用提供调用打印机功能的能力

## 5. 环境变量