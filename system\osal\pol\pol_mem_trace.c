/**************************************************************
  Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
  module name :  	POL (PANTUM OS LAYER) MEM TRACE
  file   name :	pol_trace_mem.c 
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-03
description	:   heap trace for application on pol 
 ****************************************************************/
#include "pol_mem_trace.h"
#include <assert.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <pthread.h>
#include "pol_time.h"
#include "pol_inner.h"
#include "pol_unistd.h"

//#define ENABLE_MEM_TRACE_LOG                  

#ifdef ENABLE_MEM_TRACE_LOG 
#define pol_mem_trace_log(fmt,args...) 			pol_log(MODULE_MEM_TRACE,fmt,##args)
#else
#define pol_mem_trace_log(fmt,args...)
#endif
#define pol_mem_trace_err(fmt,args...)  		pol_err(MODULE_MEM_TRACE,fmt,##args)
#define pol_mem_trace_perrno( )					pol_perrno(MODULE_MEM_TRACE)
#define pol_mem_trace_perr( err_enum )			pol_perr(MODULE_MEM_TRACE,err_enum)




static HEAP_NODE_S	s_heap_node_arr[MAX_HEAPNODE_NUM];
static HEAP_STAT_S	s_heap_stat;	
static pthread_mutex_t s_atree_lock = PTHREAD_MUTEX_INITIALIZER;
static pthread_mutex_t s_flist_lock = PTHREAD_MUTEX_INITIALIZER;



static MEM_TRACE_STATE_E _mem_free_part_trace(HEAP_NODE_P node, void* start, void* end);




/************************************************************
func:			heap_freelist_init			
input:				 			
output:			
return: 		freelist head 
description:    heap node freelist init
author:			zhoushujing
 ************************************************************/
static void  heap_freelist_init()
{
    int i = 0;
    memset(s_heap_node_arr,0,sizeof(s_heap_node_arr));

    for(;i<MAX_HEAPNODE_NUM-1;i++)
    {

        pi_list_add_tail(&(s_heap_node_arr[i].fnode),&(s_heap_stat.f_list));
        //	  s_heap_node_arr[i].list.next = s_heap_node_arr+i+1;	  
        //	  s_heap_node_arr[i+1].list.prev = s_heap_node_arr+i;
    }	
    //	s_heap_node_arr[0].list.prev = NULL;
    //	s_heap_node_arr[MAX_HEAPNODE_NUM-1].list.next = NULL;



}





/************************************************************
func:			alloc_node_insert			
input:			root--root node of alloc tree	
p_node--heap node ptr which would be insert to alloc tree 
output:			none
return: 		TRUE--success
FALSE--fail if node key has exist in tree
description:    insert heap node to alloc tree
author:			zhoushujing
 ************************************************************/

static uint8_t alloc_node_insert(struct rb_root *root, HEAP_NODE_P p_node )
{
    struct rb_node **new = &(root->rb_node), *parent = NULL;

    /* Figure out where to put new node */
    while (*new) {
        HEAP_NODE_P this = container_of(*new, HEAP_NODE_S, node);
        intptr_t result = p_node->start - this->start;

        parent = *new;
        if (result < 0)
            new = &((*new)->rb_left);
        else if (result > 0)
            new = &((*new)->rb_right);
        else
            return FALSE;
    }

    /* Add new node and rebalance tree. */
    rb_link_node(&(p_node->node), parent, new);
    rb_insert_color(&(p_node->node), root);
    pol_mem_trace_log("heap_node:%p\n",p_node);

    return 1;
}


/************************************************************
func:			is_heap_empty			
input:			none
output:			start: 	the pointer to heap range start address
end: 	the pointer to heap range end address
return: 		0 : not empty ,1: heap region is empty 
description:    get the heap range with start and end address
author:			zhoushujing
 ************************************************************/

static uint8_t  heap_is_empty()
{
    return RB_EMPTY_ROOT(&s_heap_stat.a_root);
}


/************************************************************
func:			heap_range_get			
input:			none
output:			start: 	the pointer to heap range start address
end: 	the pointer to heap range end address
return: 		0 : get success ,-1: get fail because heap region is empty 
description:    get the heap range with start and end address
author:			zhoushujing
 ************************************************************/

static void	heap_range_get(void ** start, void** end)
{
    struct rb_node *start_rb, *end_rb;
    HEAP_NODE_P start_node, end_node;

    start_rb = rb_first(&s_heap_stat.a_root);
    end_rb = rb_last(&s_heap_stat.a_root);

    start_node = container_of(start_rb, HEAP_NODE_S, node); 
    end_node = container_of(end_rb, HEAP_NODE_S, node);

    *start = start_node->start;
    *end = end_node->end;
}


/************************************************************
func:			alloc_node_search			
input:			root--root node of alloc tree	
addr--search key addr 
output:			none
return: 		heap node ptr if search out the node
NULL if search nothing
description:    search heap node from alloc tree
author:			zhoushujing
 ************************************************************/
static HEAP_NODE_P alloc_node_search(struct rb_root *root, void* addr)
{
    struct rb_node *node;
	pthread_mutex_lock(&s_atree_lock);				
	node = root->rb_node;
    while (node) {
        HEAP_NODE_P p_node = container_of(node, HEAP_NODE_S, node);	
        intptr_t result = addr - p_node->start;
        if (result < 0)
            node = node->rb_left;
        else if (result > 0)
            node = node->rb_right;
        else{			
			pthread_mutex_unlock(&s_atree_lock);
            return p_node;
        }
    }
	pthread_mutex_unlock(&s_atree_lock);
    return NULL;
}





static HEAP_NODE_P free_node_prev_search (struct list_head *head, void* addr)
{
    HEAP_NODE_P p_node ;


    pthread_mutex_lock(&s_flist_lock);

    pi_list_for_each_entry_reverse(p_node,  head, fnode){
        if(p_node->start == addr)
            break;
    }
    pthread_mutex_unlock(&s_flist_lock);

    return p_node;
}


/************************************************************
func:			alloc_node_remove			
input:			root--root node of alloc tree	
addr--search key addr 
output:			none
return: 		heap node ptr if search out the node 
NULL if search nothing
description:    remove heap node with specific addr from alloc tree
author:			zhoushujing
 ************************************************************/
static HEAP_NODE_P alloc_node_remove(struct rb_root *root, void* addr)
{
    HEAP_NODE_P p_node ;

    p_node = alloc_node_search(root, addr);
    if (p_node) {
		pthread_mutex_lock(&s_atree_lock);			
        rb_erase(&(p_node->node), root);		
		s_heap_stat.a_count--;	
        pthread_mutex_unlock(&s_atree_lock);	
    }	
    return p_node;
}


static MEM_TRACE_STATE_E  node_insert_to_freelist_tail(HEAP_NODE_P p_node, struct list_head *head)
{	
    if(head&&p_node) {

	    pthread_mutex_lock(&s_flist_lock);			
        pi_list_add_tail(&(p_node->fnode),head);
	    s_heap_stat.f_count++;	
    	pthread_mutex_unlock(&s_flist_lock);		
        return TRACE_OK;
    }
    pol_mem_trace_err(" %s\n",__func__);
    return TRACE_FAIL;
}


/************************************************************
func:			alloc_node_from_freelist			
input:			head--the point to  list head ptr
output:			none
return: 		heap node ptr if search out the node 
NULL if search nothing
description:    remove heap node with specific addr from alloc tree
author:			zhoushujing
 ************************************************************/

static MEM_TRACE_STATE_E alloc_node_from_freelist(struct list_head *head,HEAP_NODE_P *p_node )
{

    struct list_head *entry;
    pthread_mutex_lock(&s_flist_lock);
    entry= pi_list_del_head(head);
    if(entry) {
		s_heap_stat.f_count--;
    }
    pthread_mutex_unlock(&s_flist_lock);

	if(!entry){ 
		pol_mem_trace_perr(TRACE_ERR_NODE_OVER_COUNT);
		return TRACE_ERR_NODE_OVER_COUNT;
	}		
	
    *p_node= container_of(entry, HEAP_NODE_S,fnode);
    return TRACE_OK;	
}



/************************************************************
func:			alloc_node_match			
input:			root--root node of alloc tree	
addr--search key addr 
output:			none
return: 		heap node ptr if match the mem region
NULL if NOT MATCH
description:    compare input region if is within the alloced heap region
author:			zhoushujing
 ************************************************************/
static HEAP_NODE_P alloc_node_match(struct rb_root *root,const void* start,const void* end)
{
    struct rb_node *p;
    HEAP_NODE_P m_node = NULL; /*m is the node that  input area 's start > node's start */
    HEAP_NODE_P p_node = NULL; /*the ptr to current node of alloc rb_tree*/


    pthread_mutex_lock(&s_atree_lock);	

    p = root->rb_node;

    while (p) {
        p_node = container_of(p, HEAP_NODE_S, node); 
        if (start < p_node->start){ 		
            p = p->rb_left;
        }
        else {			
            if(end<=p_node->end){		
                pthread_mutex_unlock(&s_atree_lock);	
                return p_node;
            }else{	
                if(start>p_node->start){
                    m_node = p_node;
                    p = p->rb_right;
                }else{
                    pthread_mutex_unlock(&s_atree_lock);	
                    return NULL;
                }			
            }
        }
    }

    if(m_node&&(end<=m_node->end)){
        pthread_mutex_unlock(&s_atree_lock);	
        return m_node;
    }	
    else{
        pthread_mutex_unlock(&s_atree_lock);			
        return NULL;
    }
}


/************************************************************
func:			heap_stat_init			
input:			none	 			
output:			none
return: 		none
description:    heap trace statistics init
author:			zhoushujing
 ************************************************************/
static void heap_stat_init(void)
{
    s_heap_stat.a_count = 0;
    s_heap_stat.f_count = MAX_HEAPNODE_NUM;
    s_heap_stat.a_root = RB_ROOT;
    pi_init_list_head(&(s_heap_stat.f_list));
    heap_freelist_init();

}



/*----------------------------------- mem trace api --------------------------------------------*/
/************************************************************
func:			_mem_realloc_trace			
input:			addr--search key addr 
output:			none
return: 		heap node ptr if search out the node
NULL if search nothing
description:    search heap node from alloc tree
author:			zhoushujing
 ************************************************************/


void _mem_realloc_trace (void* addr, void* end)
{
    HEAP_NODE_P p_node;

    if((p_node = alloc_node_search( &s_heap_stat.a_root,addr))!=NULL){		
        p_node->end = end;
    }
}




/*----------------------------------- mem trace api --------------------------------------------*/
/************************************************************
func:			_mem_unmap_trace			
input:			addr--search key addr 
output:			none
return: 		heap node ptr if search out the node
NULL if search nothing
description:    search heap node from alloc tree
author:			zhoushujing
 ************************************************************/


void  _mem_munmap_trace (void* start, void* end)
{
    HEAP_NODE_P p_node;
    struct rb_root *root = &s_heap_stat.a_root;

    printf("%s:%d\n",__func__,__LINE__);
    p_node = alloc_node_match( root, start,end);
    if(p_node!= NULL){
        void *tmp_end;
        printf("%s:%d\n",__func__,__LINE__);
        if((p_node->start == start)&&(p_node->end == end)){ 	/*release the node */	

            printf("%s:%d\n",__func__,__LINE__);
            _mem_free_trace(start);

        }else if (p_node->start == start){/*adjust the node region*/
            printf("%s:%d\n",__func__,__LINE__);
            _mem_free_part_trace(p_node,start,end);
            pthread_mutex_lock(&s_atree_lock);				
            p_node->start = end; 
            pthread_mutex_unlock(&s_atree_lock);	
        }else if((p_node->end == end)){/*adjust the node region*/
            printf("%s:%d\n",__func__,__LINE__);
            _mem_free_part_trace(p_node,start,end);
			
            pthread_mutex_lock(&s_atree_lock);	
            p_node->end = start;
            pthread_mutex_unlock(&s_atree_lock);	
        }else /* the node spilt two region, need add a node*/
        {
            printf("%s:%d\n",__func__,__LINE__);			
            _mem_free_part_trace(p_node,start,end);
            pthread_mutex_lock(&s_atree_lock);	
            tmp_end = p_node->end;
            p_node->end = start;
            pthread_mutex_unlock(&s_atree_lock);	
            printf("%s:%d\n",__func__,__LINE__);
            _mem_alloc_trace(end, tmp_end);
            printf("%s:%d\n",__func__,__LINE__);
        }
    }else{
        pol_mem_trace_perr(TRACE_ERR_UNMAP_NOT_MATCH);
    }
}


/************************************************************
func:			mem_alloc_trace			
input:			start	--access start address
end		--access end addr 
output:			none
return: 		trace status  : see MEM_TRACE_STATE_E definition
description:    trace the mem alloc operation
author:			zhoushujing
 ************************************************************/

MEM_TRACE_STATE_E _mem_alloc_trace(void* start, void* end )
{
    HEAP_NODE_P p_node;
	MEM_TRACE_STATE_E ret;
    pol_mem_trace_log("start:%p\n",start);


    // 1. alloc the node from  freelist
    if((ret = alloc_node_from_freelist(&(s_heap_stat.f_list), &p_node) )!= TRACE_OK)
		return ret;



    // 2. init the node data 
    p_node->start = start;	
    p_node->end = end;

    p_node->LWP = pi_gettid();
#if CONFIG_POL_MEM_TRACE_BACKTRACE_LV
    memset(p_node->entry,0,CONFIG_POL_MEM_TRACE_BACKTRACE_LV*sizeof(void*));
    p_node->bt_size = backtrace(p_node->entry,CONFIG_POL_MEM_TRACE_BACKTRACE_LV);
#endif	

    pthread_mutex_lock(&s_atree_lock);
    // 3. insert to alloc rbtree	
    if(!alloc_node_insert(&s_heap_stat.a_root,p_node)){
        pol_mem_trace_perr( TRACE_ERR_NODE_INSERT_REPEAT);
        return TRACE_ERR_NODE_INSERT_REPEAT;
    }
    s_heap_stat.a_count++;	
    pthread_mutex_unlock(&s_atree_lock);

    s_heap_stat.max_a_count = MAX(s_heap_stat.a_count,s_heap_stat.max_a_count);

    return TRACE_OK;

}

/************************************************************
func:			mem_free_trace			
input:			addr	--access start address
output:			none
return: 		trace status  : see MEM_TRACE_STATE_E definition
description:    trace the mem free operation
author:			zhoushujing
 ************************************************************/

MEM_TRACE_STATE_E _mem_free_trace(void* addr)
{

    MEM_TRACE_STATE_E ret;
    HEAP_NODE_P 	p_node;
    pol_mem_trace_log("addr:%p\n",addr);

    //1. search from alloc rbtree

    if(!(p_node =alloc_node_remove(&s_heap_stat.a_root,addr)))
    {
        // search nothing if has freed
        pol_mem_trace_err("TRACE_ERR_USER_DOUBLE_FREE\n");
#if (CONFIG_POL_MEM_TRACE_BACKTRACE_LV)
        if((p_node = free_node_prev_search(&(s_heap_stat.f_list),addr))!=NULL){
            pol_log(MODULE_MEM_TRACE, "find the last free place,the dump message is below\n");
            //			mem_trace_dump(p_node);
        }
#endif 	
        _pol_trace_assert;
        return TRACE_ERR_USER_DOUBLE_FREE;

    }
    //2. insert to freelist tail
    ret = node_insert_to_freelist_tail( p_node, &s_heap_stat.f_list);	
    return ret;

}


static void mem_trace_node_copy_entry(HEAP_NODE_P dst, HEAP_NODE_P src)
{
	dst->LWP = src->LWP;
	dst->bt_size = src->bt_size;
	memcpy(dst->entry,src->entry,CONFIG_POL_MEM_TRACE_BACKTRACE_LV*sizeof(void *));
}



/************************************************************
func:			mem_free_trace			
input:			addr	--access start address
output:			none
return: 		trace status  : see MEM_TRACE_STATE_E definition
description:    trace the mem free operation
author:			zhoushujing
 ************************************************************/

static MEM_TRACE_STATE_E _mem_free_part_trace(HEAP_NODE_P node, void* start, void* end)
{

    HEAP_NODE_P p_node;
	MEM_TRACE_STATE_E ret;

    // 1. alloc the node from  freelist
    if((ret = alloc_node_from_freelist(&(s_heap_stat.f_list), &p_node) )!= TRACE_OK)
		return ret;
	
    //3. copy node info to p_node 
	mem_trace_node_copy_entry(p_node, node);
	p_node->start =start;	
	p_node->end =end;


    //3. insert to freelist tail
    ret = node_insert_to_freelist_tail( p_node, &s_heap_stat.f_list);
   
    return ret;

}



/************************************************************
func:			mem_access_trace			
input:			start	--access start address
end		--access end addr 
output:			none
return: 		trace status  : see MEM_TRACE_STATE_E definition
description:    trace the mem access operation ,check if is the wild pointer
author:			zhoushujing
 ************************************************************/
MEM_TRACE_STATE_E _mem_access_trace(const void* start, const void* end)
{	
    void  *heap_s,*heap_e;
    //	uintptr_t start = s;
    //	uintptr_t end = e;


    assert(start<end);

    /*if  heap region IS EMPTY*/
    if(heap_is_empty())
    {
        return TRACE_OK; 	
    }else{
        /*if  heap region IS NOT EMPTY*/
        heap_range_get(&heap_s, &heap_e);
        if(start>=heap_s&&end<=heap_e) {
            /*in the heap range, check if match alloc node range*/
            if(alloc_node_match(&s_heap_stat.a_root,start,end))
                return TRACE_OK;
            else{				
                pol_mem_trace_err("TRACE_ERR_WILD_POINTER\n");				
                _pol_trace_assert;
                return TRACE_ERR_WILD_POINTER;				
            }
        }else if((start<heap_s&&end>=heap_s)||(start<=heap_e&&end>heap_e))
        {
            pol_mem_trace_err("TRACE_ERR_OVER_REGION\n");				
            _pol_trace_assert;
            return TRACE_ERR_OVER_REGION;
        }
        return TRACE_OK;
    }	
}

void  _mem_trace_init(void)
{
    return heap_stat_init();
}



#if (CONFIG_POL_MEM_TRACE_BACKTRACE_LV)



#define MEMTRACE_HEAP_DUMP_FILE_PATH			"/tmp/heap_dump"
#define POL_MEMTRACE_DUMPHEAP_HELP		"echo memtrace dumpheap <PID> > /tmp/cmd" 
#if 0

static int32_t memtrace_heap_dump_handler(int32_t argc, char *argv[])
{
    pid_t pid;

    pol_mem_trace_log("memtrace_heap_dump_handler argc --%d \n",argc);

    if(argc !=1)
        return -1;
    else 
        pid = (pid_t) pi_strtol(argv[0],NULL, 0);

    if(pid == getpid()){
        //		memtrace_heap_dump_node();
    }else{
        pol_mem_trace_log("not match  the pid, don't care \n");
    }
}

int32_t _memtrace_heap_dump_init( void )
{
    //	cmd_register("memtrace", "dumpheap",memtrace_heap_dump_handler,POL_MEMTRACE_DUMPHEAP_HELP );
    return 0;
}

#endif
static void print_time(FILE* fp )
{
    struct timespec tp;
    struct tm tm;
    pi_clock_gettime(CLOCK_REALTIME,&tp);
    pi_localtime_r(&tp.tv_sec,&tm);
    pi_fprintf(fp,"%4d-%02d-%02d %02d:%02d:%02d",tm.tm_year+1900,tm.tm_mon+1,tm.tm_mday,tm.tm_hour,tm.tm_min,tm.tm_sec);

}



/*******************************************************debug start************************************/
void memtrace_heap_dump_node_to_file( char *path )
{
    struct rb_node *node;
    HEAP_NODE_P p_node;
    long off;

    FILE* fp = pi_fopen(path,"a");
    int fd = pi_fileno(fp);

    if(!fp){
        pol_mem_trace_perrno();
        return;
    }
    if((off = ftell(fp))==-1){		
        pol_mem_trace_perrno();
        return;
    }else if(off == 0){
        pi_fprintf(fp,"LWP		START		END\n");		
    }

    pi_fprintf(fp,"\n<<alloctree alloccnt: %d freecnt: %d ", s_heap_stat.a_count, s_heap_stat.f_count);
	
    print_time(fp);

    node = rb_first(&s_heap_stat.a_root);

    while(node!=NULL)
    {

        p_node = container_of(node, HEAP_NODE_S, node); 
        pi_fprintf(fp,"\n%d  %p  %p\n",p_node->LWP,p_node->start,p_node->end);
        fflush(fp);		
        backtrace_symbols_fd(p_node->entry,p_node->bt_size,fd);
        node = rb_next(node);
    }
    pi_fprintf(fp,"--->>\n");
    pi_fclose(fp);

}


void memtrace_heap_dump_freelist_to_file( char *path )
{
    HEAP_NODE_P p_node;	
    long off;

    FILE* fp = pi_fopen(path,"a");
    int fd = pi_fileno(fp);

    if(!fp){
        pol_mem_trace_perrno();
        return;
    }
    if((off = ftell(fp))==-1){		
        pol_mem_trace_perrno();
        return;
    }else if(off == 0){
        pi_fprintf(fp,"LWP		START		END\n");		
    }
	
    pi_fprintf(fp,"\n<<freelist alloccnt: %d freecnt: %d ", s_heap_stat.a_count, s_heap_stat.f_count);
    print_time(fp);

    pi_list_for_each_entry_reverse(p_node, &(s_heap_stat.f_list), fnode)
    {	
        if(p_node->LWP){
            pi_fprintf(fp,"\n%d  %p  %p\n",p_node->LWP,p_node->start,p_node->end);
            fflush(fp);		
            backtrace_symbols_fd(p_node->entry,p_node->bt_size,fd);
        }
    }
    pi_fprintf(fp,"---free>>\n");
    pi_fclose(fp);

}

/*******************************************************debug end************************************/


#endif

