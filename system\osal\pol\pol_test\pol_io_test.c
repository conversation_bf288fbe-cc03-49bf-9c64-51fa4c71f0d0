
/**************************************************************
  Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
  module name :  	POL (PANTUM OS LAYER)  selftest 
  file   name :	io_test.c 
author		:	zhoushujing (<EMAIL>)
date		:	2021-09-27
description	:   selftest  for io system interface 
 ****************************************************************/
#define _GNU_SOURCE
#include <assert.h>
#include <stdio.h>
//#include <pol/pol.h>
#include "pol/pol_io.h"
#include "pol_test.h"
#define IO_ERR_HINT         "[IO-TEST]"      

static int32_t  should_return_positive_number_if_open_ok(  )
{
    int ret;
    // exist_file  open  test         
    ret =pi_open("./exist_file",0);
    if(ret < 0 )
    { 
        perror(IO_ERR_HINT);
    }
    assert(ret >= 0);

    // exist_file  open  test         
    ret = open("./exist_file", O_CREAT, 0);
    if(ret < 0 )
    { 
        perror(IO_ERR_HINT);
    }
    assert(ret >= 0 );

    return PASS;
}

static int32_t  should_return_negative_number_if_open_err()
{
    int ret;
    printf(" test:%s\n",__func__);

    // not exsit file open  test         
    ret = pi_open("not_exist_file",0);
    if(ret < 0 )
    { 
        perror(IO_ERR_HINT);
    }
    assert(ret == -1 );



    // empty  filename  open  test         
    ret = pi_open("",0);
    if(ret < 0 )
    { 
        perror(IO_ERR_HINT);
    }
    assert(ret == -1 );


    // no filename  open  test         
    ret =pi_open(NULL,0);
    if(ret < 0 )
    { 
        perror(IO_ERR_HINT);
    }
    assert(ret == -1 );
	
    return PASS;
}


static int32_t should_return_negative_number_if_creat_err()
{
    int32_t ret = pi_creat(NULL,0);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == -1);

    ret = pi_creat("",0);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == -1);
    return PASS;
}

static int32_t should_return_positive_number_if_creat_ok()
{
    int32_t ret = pi_creat("./exist_file", 0);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret >= 0);

    ret = pi_creat("not_exist_file",0);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret < 0);
    return PASS;
}


static int32_t should_return_negative_number_if_close_err()
{
    int32_t ret;
    
    ret = pi_close(-1);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == -1);
    return PASS;
}

static int32_t should_return_positive_number_if_close_ok()
{
    int32_t fd, ret;
    // exist_file  open  test         
    fd = pi_open("./exist_file", 0);
    if (fd < 0)
    { 
        perror(IO_ERR_HINT);
    }
    assert(fd >= 0);
    ret = pi_close(fd);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == 0);
    return PASS;
}

static int32_t should_return_negative_number_if_read_err()
{
	int32_t ret;
    char buf[1024];
	
    ret = pi_read(-1, buf, 1024);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == -1);
    return PASS;
}

static int32_t should_return_positive_number_if_read_ok()
{
	char buf[1024];
    // exist_file    
    int32_t fd =pi_open("./exist_file", O_RDWR);
    if (fd < 0) { 
        perror(IO_ERR_HINT);
    }
    assert(fd >= 0);
    int32_t ret = pi_read(fd, buf, 1024);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret >= 0);

	ret = pi_close(fd);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    return PASS;
}

static int32_t should_return_negative_number_if_write_err()
{
	int32_t ret;
    char buf[1024];
	
    ret = pi_write(-1, buf, 1024);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == -1);
    return PASS;
}

static int32_t should_return_positive_number_if_write_ok()
{
    int fd,ret;

    // exist_file    
    fd = pi_open("./exist_file", O_RDWR);
    if (fd < 0) { 
        perror(IO_ERR_HINT);
    }
    assert(fd >= 0);
    ret = pi_write(fd, "test buf", 1024);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret >= 0);
	
    return PASS;
}

static int32_t should_return_negative_number_if_opendir_err()
{
	DIR *dir_point;

    // not exsit file open  test         
    dir_point = pi_opendir("not_exist_dir");
    if(dir_point == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(dir_point == NULL);

    // empty  filename  open  test         
    dir_point = pi_opendir("");
    if(dir_point == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(dir_point == NULL);


    // no filename  open  test         
    dir_point = pi_opendir(NULL);
    if(dir_point == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(dir_point == NULL);

	return PASS;
}

static int32_t should_return_positive_number_if_opendir_ok()
{
    DIR *dir_point;
    // exist_dir open test         
    dir_point = pi_opendir("./exist_dir");
    if (dir_point == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(dir_point != NULL);

	int32_t ret = pi_closedir(dir_point);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    return PASS;
}

static int32_t should_return_negative_number_if_closedir_err()
{
	//no exit dir 
	int32_t ret = pi_closedir(NULL);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret < 0);
	
    return PASS;

}

static int32_t should_return_positive_number_if_closedir_ok()
{
	//exit dir 
	DIR *dir_point;
	dir_point = pi_opendir("./exist_dir");
    if (dir_point == NULL)
    { 
        perror(IO_ERR_HINT);
    }
	assert(dir_point != NULL);
	
    int32_t ret = pi_closedir(dir_point);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == 0);

	return PASS;
}

static int32_t should_return_negative_number_if_fopen_err()
{
    FILE *fp;
    // no exist file open test         
    fp = pi_fopen("", "rw");
    if(fp == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(fp == NULL);

    // exist_file  open  test         
    fp = pi_fopen("not_exist_file", "rw");
    if(fp == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(fp == NULL);
	
    return PASS;

}

static int32_t should_return_positive_number_if_fopen_ok()
{
    FILE *fp = NULL;
    // exist_file  open  test         
    fp = pi_fopen("./exist_file", "rw");
    if (fp == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(fp != NULL);

	int32_t ret = pi_fclose(fp);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }

    return PASS;

}

static int32_t should_return_negative_number_if_fclose_err()
{
    int32_t ret;
	
    ret = pi_fclose(NULL);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }

    assert(ret < 0);
    return PASS;
}

static int32_t should_return_positive_number_if_fclose_ok()
{
    FILE *fp;
	int32_t ret;

    // exist_file  open  test         
    fp = pi_fopen("./exist_file", "rw");
    if (fp == NULL)
    { 
        perror(IO_ERR_HINT);
    }
    assert(fp != NULL);
    ret = pi_fclose(fp);
    if (ret < 0){
        perror(IO_ERR_HINT);
    }
    assert(ret == 0);
    return PASS;

}

static int32_t should_return_negative_number_if_fread_err()
{
	int32_t ret;
    char buf[1024];

    ret = pi_fread(buf, sizeof(buf) / sizeof(buf[0]), 1, NULL);
    if (ret == 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret == 0);
    return PASS;
}

static int32_t should_return_positive_number_if_fread_ok()
{
	int32_t ret;
    char buf[1024];

    FILE *fp = pi_fopen("./exist_file", "rw");
    if (fp == NULL)
    { 
        perror(IO_ERR_HINT);
    }
	assert(fp != NULL);
    ret = pi_fread(buf, sizeof(buf) / sizeof(buf[0]), 1, fp);
    if (ret == 0) {
        perror(IO_ERR_HINT);
    }
    assert(ret > 0);
	pi_fclose(fp);

    return PASS;

}

static int32_t should_return_negative_number_if_fileno_err()
{
	int32_t fd = pi_fileno(NULL);
	if (fd < 0) {
		perror(IO_ERR_HINT);
	}
	assert(fd < 0);
	return PASS;
}

static int32_t should_return_positive_number_if_fileno_ok()
{
	FILE *fp = pi_fopen("./exist_file", "rw");
    if (fp == NULL)
    { 
        perror(IO_ERR_HINT);
    }
	assert(fp != NULL);

	int32_t fd = pi_fileno(fp);
	if (fd < 0) {
		perror(IO_ERR_HINT);
	}
	assert(fd >= 0);
	int32_t ret = pi_fclose(fp);
    if (ret < 0) {
        perror(IO_ERR_HINT);
    }

	return PASS;
}

static int32_t should_return_negative_number_if_fwrite_err()
{
	int32_t ret = pi_fwrite("fwrite test str", strlen("fwrite test str") + 1, 1, NULL);
	if (ret == 0) {
		perror(IO_ERR_HINT);
	}
	assert(ret == 0);
	return PASS;
}


static int32_t should_return_positive_number_if_fwrite_ok()
{

	FILE *fp = pi_fopen("./exist_file", "rw");
	if (fp == NULL)
	{ 
		perror(IO_ERR_HINT);
	}
	assert(fp != NULL);

	int32_t ret = pi_fwrite("fwrite test str", strlen("fwrite test str") + 1, 1, fp);
	if (ret == 0) {
		perror(IO_ERR_HINT);
	}
	assert(ret > 0);
	
	ret = pi_fclose(fp);
	if (ret < 0) {
		perror(IO_ERR_HINT);
	}

	return PASS;
}

static int32_t should_return_negative_number_if_ioctl_err()
{
	return PASS;
}

static int32_t should_return_positive_number_if_ioctl_ok()
{
	return PASS;
}

static int32_t should_return_negative_number_if_select_err()
{
	return PASS;
}

static int32_t should_return_positive_number_if_select_ok()
{
	return PASS;
}

static int32_t should_return_negative_number_if_sscanf_err()
{

	int32_t ret = pi_sscanf("test", NULL);
	if (ret == EOF) {
		perror(IO_ERR_HINT);
	}

	assert(ret == EOF);

	return PASS;
}

static int32_t should_return_positive_number_if_sscanf_ok()
{
	char buf[36] = {0};

	int32_t ret = pi_sscanf("test", "%s", buf);
	if (ret == EOF) {
		perror(IO_ERR_HINT);
	}

	assert(ret != EOF);

	return PASS;
}

static int32_t should_return_negative_number_if_fprintf_err()
{
	int32_t ret = pi_fprintf(NULL, "%s", "test");
	if (ret < 0) {
		perror(IO_ERR_HINT);
	}

	assert(ret < 0);
	return PASS;
}

static int32_t should_return_positive_number_if_fprintf_ok()
{
	FILE *stream = pi_fopen("./file.c", "w+");
	if (stream == NULL) {
		perror(IO_ERR_HINT);
		return FAIL;
	}
	fseek(stream, SEEK_SET, 0);
	int32_t ret = pi_fprintf(stream, "%s", "test file");	
	if (ret < 0) {
		perror(IO_ERR_HINT);
	}
	fflush(stream);

	assert(ret > 0);
	pi_close(stream);
	system("rm -rf ./file.c");
	return PASS;
}

TEST_ITEM_S io_test_pool[] = 
{
    {"pi_open_err", should_return_negative_number_if_open_err},
    {"pi_open", should_return_positive_number_if_open_ok},
    {"pi_creat_err", should_return_negative_number_if_creat_err},
    {"pi_creat", should_return_positive_number_if_creat_ok},
    {"pi_close_err", should_return_negative_number_if_close_err},
    {"pi_close", should_return_positive_number_if_close_ok},
    {"pi_read_err", should_return_negative_number_if_read_err},
    {"pi_read", should_return_positive_number_if_read_ok},
    {"pi_write_err", should_return_negative_number_if_write_err},
    {"pi_write", should_return_positive_number_if_write_ok},
    {"pi_opendir_err", should_return_negative_number_if_opendir_err},
    {"pi_opendir", should_return_positive_number_if_opendir_ok},    
    {"pi_closedir_err", should_return_negative_number_if_closedir_err},
    {"pi_closedir", should_return_positive_number_if_closedir_ok},
    {"pi_fopen_err", should_return_negative_number_if_fopen_err},
    {"pi_fopen", should_return_positive_number_if_fopen_ok},
    {"pi_fclose_err", should_return_negative_number_if_fclose_err},
    {"pi_fclose", should_return_positive_number_if_fclose_ok},
    {"pi_fread_err", should_return_negative_number_if_fread_err},
    {"pi_fread", should_return_positive_number_if_fread_ok},
    {"pi_fileno_err", should_return_negative_number_if_fileno_err},
    {"pi_fileno", should_return_positive_number_if_fileno_ok},
    {"pi_fwrite_err", should_return_negative_number_if_fwrite_err},
    {"pi_fwrite", should_return_positive_number_if_fwrite_ok},
    {"pi_ioctl_err", should_return_negative_number_if_ioctl_err},
    {"pi_ioctl", should_return_positive_number_if_ioctl_ok},
    {"pi_select_err", should_return_negative_number_if_select_err},
    {"pi_select", should_return_positive_number_if_select_ok},
    {"pi_sscanf_err", should_return_negative_number_if_sscanf_err},
    {"pi_sscanf", should_return_positive_number_if_sscanf_ok},
    {"pi_fprintf_err", should_return_negative_number_if_fprintf_err},
    {"pi_fprintf", should_return_positive_number_if_fprintf_ok},
};

TEST_POOL_S io_pool = {
    "io",
    io_test_pool,
    sizeof(io_test_pool)/sizeof(TEST_ITEM_S),
};

