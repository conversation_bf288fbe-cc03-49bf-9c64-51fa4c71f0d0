/*
 * test_mkdir.c
 *
 *  Created on: Apr 28, 2024
 *      Author: kylin
 */



#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>
#include "runtime/modules/std/net_private.h"

#ifndef PATH_MAX
# define PATH_MAX 512
#endif


/*
#define NET_LOG_DEBUG printf
#define NET_LOG_ERROR printf
static char* GET_APP_PATH() { return "/home/<USER>/temp/pesf_app/print_service_example-BM5201ADN-EN";}
*/

// 实现递归创建目录的函数
static int create_directories_recursive(const char *path) {
    char dir_path[PATH_MAX] = {'\0'};
    size_t len = path ? strlen(path) : 0;

    // 检查路径是否为空或长度过短
    if (len < 2 || path[0] != '/' || len > PATH_MAX) {
        return -1; // 不是目录路径或路径不合法
    }

    // 复制路径到dir_path，并去除末尾的'/'
    strncpy(dir_path, path, PATH_MAX - 1);
    if (dir_path[len - 1] == '/')
        dir_path[len - 1] = '\0';

    // 从根目录开始，逐级检查并创建目录
    char *ptr = strchr(dir_path + 1, '/'); // 跳过第一个字符（假设不是在根目录下创建一个文件夹）
    while (ptr) {
        *ptr = '\0'; // 截断路径到当前目录
        if (mkdir(dir_path, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) == -1) {
            if (errno != EEXIST) {
                return -1; // 创建失败，且不是因为目录已存在
            }
        }
        *ptr = '/'; // 恢复原来的'/'，继续遍历
        ptr = strchr(ptr + 1, '/');
    }

    // 最终尝试创建完整的目录路径
    if (mkdir(dir_path, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) == -1) {
        if (errno != EEXIST) {
            return -1; // 创建失败，且不是因为目录已存在
        }
    }

    return 0; // 成功创建
}

/*
 * 1.递归创建目录
 * 2.以/temp/开头的文件路径，创建软链接，文件夹实际创建在/tmp/pesf_app/app_name_dir下
 * e.g.
 * "/temp/img/aa.png", output: "/pesf_app/print_service_example-BM5201ADN-EN/temp/img/aa.png"
 *  --> "/tmp/pesf_app/print_service_example-BM5201ADN-EN/temp/img/aa.png"
 * "/storage/img/bb.png", output: "/pesf_app/print_service_example-BM5201ADN-EN/storage/img/bb.png"
 */
int net_wget_create_dir(const char *file_path, char *app_path) {
    int rc = 0;
    char *real_path = NULL;
    /* /pesf_app/print_service_example-BM5201ADN-EN */
    //char *app_path = GET_APP_PATH();
    char dir_path[256] = {'\0'};

    if ((NULL == file_path) || (NULL == app_path) || ('/' != app_path[0])) {
        NET_LOG_ERROR("args error\n");
        return -1;
    }

    char *last_split = strrchr(file_path, '/');
    int len = strlen(file_path);

    /* 检查路径是否为空或长度过短，最后一个字符不能为'/'  */
    if (len > sizeof(dir_path) || len < (sizeof("/temp/a") - 1) || last_split == NULL ||
        last_split == (file_path + len - 1)
        || strstr(file_path, "..") || strstr(file_path, "./")
        ) {
        return -1; // 不是文件路径或路径不合法
    }

    /* 复制路径到dir_path，并去除末尾的文件名'aa.png' */
    len = last_split - file_path;
    if ((len <= 0) || (len >= sizeof(dir_path))) {
        NET_LOG_ERROR("args error '%s', len %d\n", file_path, len);
        return -1;
    }
    ++len; /* 保留末尾的'/' */
    strncpy(dir_path, file_path, len);
    dir_path[len] = '\0';

    /* 只允许temp或storage开头，temp开头的在app目录下创建一个软链接，实际文件将来存在内存中(/tmp)，避免频繁写flash */
    if (strncmp(dir_path, "/temp/", 6) && strncmp(dir_path, "/storage/", 9)) {
        NET_LOG_ERROR("args error '%s'\n", dir_path);
        return -1;
    }

    real_path = (char *) malloc(PATH_MAX);
    /* 仅创建软链接，链到/tmp下的路径 */
    if (strncmp(dir_path, "/temp/", 6) == 0) {
        char *app_tmp = (char *) malloc(PATH_MAX);
        snprintf(app_tmp, PATH_MAX, "%s/temp", app_path);
        snprintf(real_path, PATH_MAX, "/tmp%s", app_path);
        rc = create_directories_recursive(real_path);
        NET_LOG_DEBUG("crate temp %d\n", rc);
        rc |= symlink(real_path, app_tmp);
        if (rc && (errno != EEXIST)) {
            NET_LOG_ERROR("ln real path %s, app_tmp %s, rc %d, %s\n", real_path, app_tmp, rc, strerror(errno));
        } else {
            NET_LOG_DEBUG("exist, reset 0\n");
            rc = 0;
        }
        free(app_tmp);
    }

    if (0 == rc) {
        snprintf(real_path, PATH_MAX, "%s%s", app_path, dir_path);
        rc = create_directories_recursive(real_path);
        NET_LOG_DEBUG("mkdir '%s' %d\n", real_path, rc);
        /* 成功创建文件夹，文件路径后续使用时重新拼接，避免后续使用完毕忘记释放内存
        if (0 == rc) {
            return real_path;
        }*/
    }
    free(real_path);

    return rc;
}

#if 0
int main() {
    const char* app_path = GET_APP_PATH();
    const char* img_path[] = {
//			"/temp/a",
/*
crate temp 0
ln real path /tmp/home/<USER>/temp/pesf_app/print_service_example-BM5201ADN-EN, app_tmp /home/<USER>/temp/pesf_app/print_service_example-BM5201ADN-EN/temp, rc -1, No such file or directory
===> 0 Failed to create directories, /temp/a: No such file or directory
*/

            "/storage/your/directory/subdir1/subdir2/a.png",
            "/temp/your/directory/subdir3/subdir4/a.png",
            "/temp//test3/b.png",
            "/temp/test4//test5/b.png",
            "/temp/test4//test6//b.png",
            "/temp/c.png",
            "/temp/a",
            /* bad path */
            "",
            "/",
            "/tempa/",
            "/tempab/",
            "/tempabc/",
            "/ss/../ddd.a",
            "/storage/ss/../ddd.a",
            "/temp/aaa./sss.a",
            "/temp/",
            "/storage/",

    };

    int rc;
    int i;
    int c = sizeof(img_path) / sizeof(img_path[0]);
    printf("count %d\n", c);
    for (i=0; i<c; ++i) {
        if (net_wget_create_dir(img_path[i])) {
            printf("===> %d Failed to create directories, %s: %s\n", i, img_path[i], strerror(errno));
        } else {
            char real[512];
            snprintf(real, sizeof(real), "%s%s", app_path, img_path[i]);
            FILE* fp = fopen(real, "wa");
            if (!fp) {
                printf("===> %d fopen failed %s\n", i, img_path[i]);
                continue;
            }
            rc = fprintf(fp, "%d\n", i);
            if (rc) {
                printf("===> %d OK %s\n", i, img_path[i]);
            } else {
                printf("===> %d write failed %s\n", i, img_path[i]);
            }

            fclose(fp);
        }

    }

    return 0;
}
/*
tree /tmp/home/<USER>/temp/
/tmp/home/
└── kylin
    └── temp
        └── pesf_app
            └── print_service_example-BM5201ADN-EN
                ├── a
                ├── c.png
                ├── test3
                │   └── b.png
                ├── test4
                │   ├── test5
                │   │   └── b.png
                │   └── test6
                │       └── b.png
                └── your
                    └── directory
                        └── subdir3
                            └── subdir4
                                └── a.png
/home/<USER>/temp/
├── hh.bin
├── pesf_app
│   └── print_service_example-BM5201ADN-EN
│       ├── storage
│       │   └── your
│       │       └── directory
│       │           └── subdir1
│       │               └── subdir2
│       │                   └── a.png
│       └── temp -> /tmp/home/<USER>/temp/pesf_app/print_service_example-BM5201ADN-EN
*/
#endif

