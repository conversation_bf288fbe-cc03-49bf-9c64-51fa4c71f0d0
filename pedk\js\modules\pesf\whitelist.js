//{
//   "MACaddress":[
//        "11:22:33:44:55:66",
//        "22:33:44:55:66:77"
//    ]
//}

export function getPermitMacList()
{
	console.log("#### getPermitMacList test ####\n")
	const retstr = js_getPermitMACList();
	if(retstr === "EXIT_FAILURE")
	{
		return retstr;
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	const ret_obj = JSON.parse(retstr);
    return ret_obj;
}

export function setPermitMacList(input)
{
	console.log("#### setPermitMacList test ####\n")
	for(let i = 0; i < input.length; i++){
		console.log((i+1)+'add Macaddress:'+input[i]+'\n');
		var retstr = js_setPermitMACList(input[i]);
		if(retstr === "EXIT_FAILURE"){
			return retstr;
		}
	}
    return retstr;
}

export function getProhibitMacList()
{
	console.log("#### getProhibitMacList test ####\n")
	const retstr = js_getProhibitMACList();
	if(retstr === "EXIT_FAILURE")
	{
		return retstr;
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	const ret_obj = JSON.parse(retstr);
    return ret_obj;
}

export function setProhibitMacList(input)
{
	console.log("#### setProhibitMacList test ####\n")
	for(let i = 0; i < input.length; i++){
		console.log((i+1)+'add Macaddress:'+input[i]+'\n');
		var retstr = js_setProhibitMACList(input[i]);
		if(retstr === "EXIT_FAILURE"){
			return retstr;
		}
	}
    return retstr;
}

export function getWhiteList()
{
	console.log("#### getWhiteList test ####\n")
	//const appname = "my_app"; //单个app
	//const retstr = js_getWhiteList(appname);
	const retstr = js_getWhiteList();
	if(retstr === "EXIT_FAILURE")
	{
		return retstr;
	}
	else{
		console.log("EXIT_SUCCESS\n");
	}
	const ret_obj = JSON.parse(retstr);
	//初始化返回Array
	const ret_array = [];
	//遍历对象的键值，并存入Array
	for(const key in ret_obj){
		if(ret_obj.hasOwnProperty(key)){
			ret_array.push([key, ret_obj[key]]);
		}
	}
    return ret_array;
}

export function setPermitFunction(func,value)
{
	console.log("#### setPermitFunction test ####")
    return js_setPermitFunction(func,value);
}

globalThis.pedk.auth.macfilter.getPermitMacList = getPermitMacList
globalThis.pedk.auth.macfilter.setPermitMacList = setPermitMacList
globalThis.pedk.auth.macfilter.getProhibitMacList  = getProhibitMacList
globalThis.pedk.auth.macfilter.setProhibitMacList = setProhibitMacList
globalThis.pedk.auth.whitelist.getWhiteList = getWhiteList
globalThis.pedk.auth.whitelist.setPermitFunction = setPermitFunction
