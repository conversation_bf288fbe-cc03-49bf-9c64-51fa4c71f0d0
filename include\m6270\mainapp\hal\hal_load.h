/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file hal_load.h
 * @addtogroup hal
 * @{
 * <AUTHOR>
 * @date 2024-07-16
 * @brief hal module prolog/epilog function.
 */
#ifndef __HAL_LOAD_H__
#define __HAL_LOAD_H__

/**
 * @brief hal module prolog(start and initialize).
 * @return result
 * @retval =0: success
 *         <0: fail
 * <AUTHOR>
 * @data   2023-5-8
 */
int32_t hal_prolog(void);

/**
 * @brief hal module epilog.
 * <AUTHOR>
 * @data   2023-5-8
 */
void    hal_epilog(void);

#endif /* __HAL_LOAD_H__ */
/**
 *@}
 */
