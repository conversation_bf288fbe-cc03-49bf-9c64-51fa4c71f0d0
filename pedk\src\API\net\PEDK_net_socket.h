#ifndef _PEDK_NET_SOCKET_
#define _PEDK_NET_SOCKET_
 
#include <quickjs.h>

void addSocket(JSContext* ctx, JSValue container);

/*
JSValue jsSysNetSocket(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
JSValue jsSysNetConnect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
JSValue jsSysNetRecv(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
JSValue jsSysNetSend(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
JSValue jsSysCloseFd(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
*/

#endif /* _PESF_NET_SOCKET_ */
