#ifndef _PESF_EVENT_
#define _PESF_EVENT_

#include "stdio.h"
#include "stdlib.h"
#include "string.h"

#include <sys/types.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <pthread.h>
#include <time.h>
#include "bridge.h"
#include "cjson/cJSON.h"

typedef enum{
    MSG_MODULE_PANEL=0,
    MSG_MODULE_COPY=1,
    MSG_MODULE_PRINT=2,
    MSG_MODULE_SCAN=3,
    MSG_MODULE_LOWPOWER=4,
    MSG_MODULE_USBH=5,
    MSG_MODULE_USBD=6,
    MSG_MODULE_PLATFORM=7,
    MSG_MODULE_WHITELIST=8,
    MSG_MOUDLE_USB_ICCARD=9,
    MSG_MOUDLE_BEEP=10,
    MSG_MODULE_NET=11,
    MSG_MODULE_STATUS=12,
    MSG_MODULE_JOBCTL=13,
    MSG_MODULE_SETTING=14,
    MSG_MODULE_TOTAL_PAGES=15,
    MSG_MODULE_HTTP=16,
    MSG_MODULE_TEST=101,
    MSG_MODULE_ERR,
}MAIN_MSG;

typedef enum{
    //panel 0~99
    MSG_PANEL_SUB_DRAW=0,
    MSG_PANEL_SUB_DRAW_SCREEN=1,
    MSG_PANEL_SUB_DRAW_LABEL=2,
    MSG_PANEL_SUB_DRAW_BUTTON=3,
    MSG_PANEL_SUB_DRAW_IMAGE=4,
    MSG_PANEL_SUB_DRAW_CHECK_BUTTON=5,
    MSG_PANEL_SUB_DRAW_RADIO_BUTTON=6,
    MSG_PANEL_SUB_DRAW_LINE_EDIT=7,
    MSG_PANEL_SUB_DRAW_ANIMAL_IMAGE=8,
    MSG_PANEL_SUB_CTRL=9,
    MSG_PANEL_SUB_CTRL_BRIGHTNESS=10,
    MSG_PANEL_SUB_CTRL_SCREEN_STATUS=11,
    MSG_PANEL_SUB_CTRL_BUZZER=12,
    MSG_PANEL_SUB_CTRL_KEY=13,
    MSG_PANEL_SUB_CTRL_LED=14,
    MSG_PANEL_SUB_CTRL_LINK=15,
    MSG_PANEL_SUB_TOUCH_CB=16,
    MSG_PANEL_SUB_REDRAW=17,
    MSG_PANEL_SUB_DRAW_KEYBOARD=18,
    MSG_PANEL_SUB_SCREEN_ON_OFF=19,
    MSG_PANEL_SUB_SET_BACK_TIMEOUT=20,
    MSG_PANEL_SUB_GET_BACK_TIMEOUT=21,
    MSG_PANEL_SUB_DELETE_BACK_TIMEOUT=22,
    MSG_PANEL_SUB_CHANGE_TO_NATIVE_WINDOW=23,


    //COPY 100~199
    MSG_COPY_SUB = 100,
    MSG_COPY_SUB_JOB_START = 101,
    MSG_COPY_SUB_JOB_CANCEL = 102,
    MSG_COPY_SUB_JOB_NEXT_PAGE = 103,
    MSG_COPY_SUB_JOB_DONE = 104,
    MSG_COPY_SUB_ADF_COPY_PAGE_NUM = 105,
    MSG_COPY_SUB_FB_COPY_PAGE_NUM = 106,

    MSG_COPY_SUB_SET_COPY_RETENTION_PARAM = 107,

    //PRINT 200~299
    MSG_PRINT_SUB_JOB_START=200,
    MSG_PRINT_SUB_JOB_CANCEL=201,
    MSG_PRINT_SUB_JOB_STATE= 202,
    MSG_PRINT_SUB_JOB_ID= 203,

    //SCAN 300~399
    MSG_SCAN_SUB_JOB_START=300,
    MSG_SCAN_SUB_JOB_CANCEL=301,
    MSG_SCAN_SUB_GET_ADF_PAGE_NUM=302,
    MSG_SCAN_SUB_SENT_ADF_PAGE_NUM=303,
    MSG_SCAN_SUB_GET_FB_PAGE_NUM=304,
    MSG_SCAN_SUB_SENT_FB_PAGE_NUM=305,
    MSG_SCAN_SUB_GET_SCAN_PAGE_NUM=306,
    MSG_SCAN_SUB_SENT_SCAN_PAGE_NUM=307,
    MSG_SCAN_SUB_GET_FRONT_FPGAVERSION=308,
    MSG_SCAN_SUB_SENT_FRONT_FPGAVERSION=309,
    MSG_SCAN_SUB_GET_BACK_FPGAVERSION=310,
    MSG_SCAN_SUB_SENT_BACK_FPGAVERSION=311,
    MSG_SCAN_SUB_JOB_STATUS=312,
    MSG_SCAN_SUB_JOB_QUOTA=313,
    MSG_SCAN_SUB_JOB_CONTINUE=314,
    MSG_SCAN_SUB_JOB_FINISH=315,
    MSG_SCAN_SUB_JOB_APP=316,

    //LOWPOWER 400~499
    MSG_LOWPOWER_SUB_GET = 400 ,
    MSG_LOWPOWER_SUB_SET = 401,
    MSG_LOWPOWER_SUB_REPORT = 402,

    //USBH  500~599
    MSG_USBH_SUB = 500,

    //USBD  600~699
    MSG_USB_DEVICE_ENABLE = 600,

    //PLATFORM 700~799
    MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LEN = 700,
    MSG_PLATFORM_SUB_CAPABILITIESLIST_GET_LIST = 701,
    MSG_PLATFORM_SUB_DEVICEINFO_GET_DCFWVERSION = 702,
    MSG_PLATFORM_SUB_DEVICEINFO_GET_ECFWVERSION = 703,
    MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA1VERSION = 704,
    MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA2VERSION = 705,
    MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTNAME = 706,
    MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTSN = 707,
    MSG_PLATFORM_SUB_PRODUCTINFO_GET_PRODUCTPOSITION = 708,
    MSG_PLATFORM_SUB_PRODUCTINFO_GET_LANGUAGE = 709,
    MSG_PLATFORM_SUB_PRODUCTINFO_SET_LANGUAGE = 710,

    MSG_CAPABILITIESLIST_SUB_SUPPORT_TRAY_LIST = 711,
    MSG_CAPABILITIESLIST_SUB_SUPPORT_PAPER_SIZE_LIST = 712,
    MSG_CAPABILITIESLIST_SUB_SUPPORT_MEDIA_TYPE_LIST = 713,

    //WHITELIST 800~899
    MSG_WHITELIST_SUB_GET=800,
    MSG_WHITELIST_SUB_SET=801,
    MSG_WHITELIST_SUB_GET_MAC=802,
    MSG_WHITELIST_SUB_SET_MAC=803,

    //ICCARD 900~999
    MSG_GET_ICCAED_INFO=900,

    //BEEP 1000~1099
    MSG_BEEP_GET_DURATION=1000,
    MSG_BEEP_SET_DURATION=1001,
    MSG_BEEP_GET_FREQUENCY=1002,
    MSG_BEEP_SET_FREQUENCY=1003,
    MSG_BEEP_GET_VOLUME=1004,
    MSG_BEEP_SET_VOLUME=1005,
    MSG_BEEP_START_WORK=1006,
    MSG_BEEP_STOP_WORK=1007,
    MSG_BEEP_GET_WORK_STATUS=1008,

    //NET 1100~1199
    //email_addressbook
    MSG_NET_SUB_ADD_EMAIL_ADDR=1100,
    MSG_NET_SUB_GET_EMAIL_ADDR=1101,
    MSG_NET_SUB_GET_EMAIL_ADDR_LIST=1102,
    MSG_NET_SUB_GET_EMAIL_ADDR_NUM=1103,
    MSG_NET_SUB_IS_EMAIL_ADDR_FULL=1104,
    MSG_NET_SUB_MODIFY_EMAIL_ADDR=1105,
    MSG_NET_SUB_REMOVE_EMAIL_ADDR=1106,

    //NET_group_addressbook
    MSG_NET_SUB_ADD_EMAIL_TO_GROUP=1107,
    MSG_NET_SUB_CREAT_EMAIL_GROUP=1108,
    MSG_NET_SUB_GET_EMAIL_GROUP=1109,
    MSG_NET_SUB_GET_EMAIL_GROUP_LIST=1110,
    MSG_NET_SUB_GET_EMAIL_GROUP_NUM=1111,
    MSG_NET_SUB_IS_EMAIL_GROUP_FULL=1112,
    MSG_NET_SUB_MODIFY_EMAIL_GROUP=1113,
    MSG_NET_SUB_REMOVE_EMAIL_FROM_GROUP=1114,
    MSG_NET_SUB_REMOVE_EMAIL_GROUP=1115,

    //NET_ftp_addressbook
    MSG_NET_SUB_ADD_FTP_ADDR=1116,
    MSG_NET_SUB_GET_FTP_ADDR=1117,
    MSG_NET_SUB_GET_FTP_ADDR_LIST=1118,
    MSG_NET_SUB_GET_FTP_ADDR_NUM=1119,
    MSG_NET_SUB_IS_FTP_ADDR_FULL=1120,
    MSG_NET_SUB_MODIFY_FTP_ADDR=1121,
    MSG_NET_SUB_REMOVE_FTP_ADDR=1122,

    //NET_smb_addressbook
    MSG_NET_SUB_ADD_SMB_ADDR=1123,
    MSG_NET_SUB_GET_SMB_ADDR=1124,
    MSG_NET_SUB_GET_SMB_ADDR_LIST=1125,
    MSG_NET_SUB_GET_SMB_ADDR_NUM=1126,
    MSG_NET_SUB_IS_SMB_ADDR_FULL=1127,
    MSG_NET_SUB_MODIFY_SMB_ADDR=1128,
    MSG_NET_SUB_REMOVE_SMB_ADDR=1129,

    //NET_email
    MSG_NET_SUB_SEND_EMAIL=1130,

    //NET_ftp
    MSG_NET_SUB_FTP_CONNECT=1131,
    MSG_NET_SUB_FTP_DISCONNECT=1132,

    //NET_smb
    MSG_NET_SUB_SMB_CONNECT=1133,
    MSG_NET_SUB_SMB_DISCONNECT=1134,

    //NET_setting
    MSG_NET_SUB_GET_SMTP_EMAIL_ENCRYPT_MODE=1135,
    MSG_NET_SUB_GET_SMTP_LOGIN_NAME=1136,
    MSG_NET_SUB_GET_SMTP_LOGIN_PWD=1137,
    MSG_NET_SUB_GET_SMTP_PORT=1138,
    MSG_NET_SUB_GET_SMTP_SERVER_NAME=1139,
    MSG_NET_SUB_SET_SMTP_EMAIL_ENCRYPT_MODE=1140,
    MSG_NET_SUB_SET_SMTP_LOGIN_NAME=1141,
    MSG_NET_SUB_SET_SMTP_LOGIN_PWD=1142,
    MSG_NET_SUB_SET_SMTP_PORT=1143,
    MSG_NET_SUB_SET_SMTP_SERVER_NAME=1144,
    MSG_NET_SUB_GET_WIRED_CONNECT_INFO=1145,
    MSG_NET_SUB_GET_WIRED_MAC_ADDR_INFO=1146,
    MSG_NET_SUB_GET_WIRED_NET_802_PROTOCOL_SWITCH=1147,
    MSG_NET_SUB_GET_WIRED_NET_IPV4_ADDR=1148,
    MSG_NET_SUB_GET_WIRED_NET_IPV4_GATEWAY=1149,
    MSG_NET_SUB_GET_WIRED_NET_IPV4_HOSTNAME=1150,
    MSG_NET_SUB_GET_WIRED_NET_IPV4_MASK=1151,
    MSG_NET_SUB_GET_WIRED_NET_IPV4_MODE=1152,
    MSG_NET_SUB_SET_WIRED_NET_802_PROTOCOL_SWITCH=1153,
    MSG_NET_SUB_SET_WIRED_NET_IPV4_ADDR=1154,
    MSG_NET_SUB_SET_WIRED_NET_IPV4_HOSTNAME=1155,
    MSG_NET_SUB_SET_WIRED_NET_IPV4_MODE=1156,
    MSG_NET_SUB_SET_WIRED_NET_IPV6_SWITCH=1157,
    MSG_NET_SUB_GET_PRODUCT_UUID=1158,
    MSG_NET_SUB_SET_WIRED_NET_IPV4_MASK=1159,
    MSG_NET_SUB_SET_WIRED_NET_IPV4_GATEWAY=1160,

    //NET http
    MSG_NET_SUB_HTTP_FETCH_DATA=1183,

    //NET tcp and udp
    MSG_NET_SUB_CREATE_TCP_SOCKET=1184,
    MSG_NET_SUB_CREATE_TCP_CONN=1185,
    MSG_NET_SUB_TCP_RECV=1186,
    MSG_NET_SUB_TCP_SEND=1187,
    MSG_NET_SUB_TCP_CLOSE=1188,
    MSG_NET_SUB_CREATE_UDP_SOCKET=1189,
    MSG_NET_SUB_CREATE_UDP_CONN=1190,
    MSG_NET_SUB_UDP_SEND=1191,
    MSG_NET_SUB_UDP_RECV=1192,
    MSG_NET_SUB_UDP_CLOSE=1193,

    //NET ssl
    MSG_NET_SUB_SSL_LOAD_CERT=1194,
    MSG_NET_SUB_SSL_CREATE_CONN=1195,
    MSG_NET_SUB_SSL_RECV=1196,
    MSG_NET_SUB_SSL_SEND=1197,
    MSG_NET_SUB_SSL_CLOSE=1198,

    MSG_NET_SUB_GEN_CERT=1199,


    //STATUS 1200~1250
    MSG_STATUS_SUB=1200,
    MSG_STATUS_SUB_ID=1201,
    MSG_STATUS_SUB_TYPE=1202,
    MSG_STATUS_SUB_MODULE=1203,
    MSG_STATUS_SUB_PRI=1204,
    MSG_STATUS_SUB_NOTIFY_PUSH=1205,
    MSG_STATUS_SUB_NOTIFY_REMOVE=1206,
    MSG_STATUS_SUB_LIST_LEN=1207,
    MSG_NET_SUB_SSL_INSTALL_CERT=1208,

    //NET https 1251~1299
    MSG_NET_SUB_HTTPS_SET_CONFIG_PARAM=1251,
    MSG_NET_SUB_HTTPS_GET_CONFIG_PARAM=1252,

    //JOBCTL 1300~1399
    MSG_JOBCTL_SUB=1300,
    MSG_JOBCTL_SUB_NOTIFY=1301,
    MSG_JOBCTL_SUB_CANCEL=1302,
    MSG_JOBCTL_SUB_APPLYID=1303,
    MSG_JOBCTL_SUB_HISTORY_LIST=1304,    
    MSG_JOBCTL_SUB_HISTORY_LAST=1305,
    MSG_JOBCTL_SUB_HISTORY_CLEAR=1306,
    MSG_JOBCTL_SUB_NET_UPLOAD_INFO=1307,

    //SETTING1400~1499
    MSG_SETTING_SUB_SETFUNCTIONSWITCH=1400,
    MSG_SETTING_SUB_TRAY_PAPER_SIZE = 1428,
    MSG_SETTING_SUB_TRAY_PAPER_TYPE = 1429,
    MSG_SETTING_SUB_C_TONER_STATUS = 1430,
    MSG_SETTING_SUB_M_TONER_STATUS = 1431,
    MSG_SETTING_SUB_Y_TONER_STATUS = 1432,
    MSG_SETTING_SUB_K_TONER_STATUS = 1433,
    MSG_SETTING_SUB_C_DRUM_STATUS = 1434,
    MSG_SETTING_SUB_M_DRUM_STATUS = 1435,
    MSG_SETTING_SUB_Y_DRUM_STATUS = 1436,
    MSG_SETTING_SUB_K_DRUM_STATUS = 1437,
    MSG_SETTING_SUB_POWER_BOX_MODEL = 1438,
    MSG_SETTING_SUB_C_TONER_REMAIN_VAL = 1439,
    MSG_SETTING_SUB_M_TONER_REMAIN_VAL = 1440,
    MSG_SETTING_SUB_Y_TONER_REMAIN_VAL = 1441,
    MSG_SETTING_SUB_K_TONER_REMAIN_VAL = 1442,
    MSG_SETTING_SUB_C_DRUM_MODEL = 1443,
    MSG_SETTING_SUB_M_DRUM_MODEL = 1444,
    MSG_SETTING_SUB_Y_DRUM_MODEL = 1445,
    MSG_SETTING_SUB_K_DRUM_MODEL = 1446,
    MSG_SETTING_SUB_C_DRUM_REMAIN_VAL = 1447,
    MSG_SETTING_SUB_M_DRUM_REMAIN_VAL = 1448,
    MSG_SETTING_SUB_Y_DRUM_REMAIN_VAL = 1449,
    MSG_SETTING_SUB_K_DRUM_REMAIN_VAL = 1450,
    MSG_SETTING_SUB_AVERAGE_COVERAGE_RATE = 1451,
    MSG_SETTING_SUB_GET_SCAN_HTTP_PARAMS = 1452,
    MSG_SETTING_SUB_SET_SCAN_HTTP_PARAMS = 1453,

    //TOTAL PAGES1500~1599
    MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_COLOR=1500,
    MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MONO=1501,
    MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_MEDIA_SIZE=1502,
    MSG_TOTAL_PAGES_SUB_GET_PRINT_PAGE_BY_SHEETS=1503,

    //HTTP1600~1699
    MSG_HTTP_SUB = 1600,

    MSG_ERR_SUB ,

    //TEST
    MSG_TEST_SUB= 10000,

}SUB_MSG;

typedef enum{
    CMD_PING = 0x00,
    CMD_APP_START,
    CMD_APP_START_RETURN,
    CMD_APP_END,
    CMD_APP_END_RETURN,
    CMD_APP_PAUSE,
    CMD_APP_PAUSE_RETURN,
    CMD_APP_CONTINUE,
    CMD_APP_CONTINUE_RETURN,
    CMD_PRINTER_TO_APP,
    CMD_APP_TO_PRINTER,
    CMD_GET_PROPERTY_LIST,
    CMD_RETURN_PROPERTY_LIST,
    CMD_GET_APP_PROPERTY,
    CMD_RETURN_APP_PROPERTY,

}Protocol_CMD;

typedef struct syncMsg{
	MAIN_MSG main;
	SUB_MSG sub;
}syncMsg;

struct strSocketProtocol
{
    unsigned char cmd;
    unsigned char lengthH;
    unsigned char lengthL;
    unsigned char data[0];//dataä¸ºmsgæˆ–è€…ä¸ºé€šä¿¡åè®®çš„å…¶ä»–æ•°æ®
};

#pragma pack(1)
struct strSocketMsg
{
    unsigned char rtid;
    /* data */
    MAIN_MSG main;  		//server to client: 发送者; clinet to server: 接收者
    SUB_MSG sub;
	int respond; 			//返回值
    int size;   			//字节数据的长度
    unsigned char data[0];	//字节数据的首字符地址
};
#pragma pack()

struct strListMsg{
    struct strSocketProtocol *msg;
    struct strListMsg* next;
};

struct MessageQueueNode
{
	struct strSocketMsg *msg;
	struct MessageQueueNode* next;
};

struct MessageQueue
{
	struct MessageQueueNode* head;
	struct MessageQueueNode* tail;
	int size;
};


void error_handing(char* messgae);

/*dtirection: 1 Pesf->Printer   0:Printer->Pesf */
struct strSocketProtocol * packetProtocolStruct(MAIN_MSG main, SUB_MSG sub, int respond, int recvBufSize, unsigned char *recvBuf, int direction);

void SetJSContext(JSContext* ctx);

//input main sub recvBuf recvBufSize

// /*return :0 is success, -1 is failed*/ input main sub, output recvBuf recvBufSize
int pop_sync_msg(MAIN_MSG main, SUB_MSG sub, int *respond, unsigned char *recvBuf, int* recvBufSize);


/*return :0 is success, -1 is failed*/
int SendMsgToMfp(MAIN_MSG main, SUB_MSG sub, int respond, int size, const unsigned char *param);

/*return :0 is success, -1 is failed*/
int SendCmdToMfp(int cmd,  unsigned char* buf, unsigned short size);

/*input param: main , sub, recvBufSize
	recvBufSize : recvBuf 's size
**output param:respond, recvBuf, recvBufSize
	recvBufSize:recv data 's size
**return :0 is success, -1 is failed*/
int RecvMsgToMfp(MAIN_MSG main, SUB_MSG sub, int *respond, unsigned char *recvBuf, int* recvBufSize, int timeout);

void init_queue();

void destory_queue();


//return value failed:-1 ;success:0
int push(struct strSocketMsg* msg);

void* client_thread(void* arg);
void* dispatch_thread(void* arg);
void  client_thread_init();

void transport_init_for_runtime();
int transport_send_for_runtime(const unsigned char *buffer, unsigned short length);
int transport_receive_for_runtime(unsigned char *buffer, unsigned short *length, int ms);
void transport_release_for_runtime();

#endif /* _PESF_EVENT_ */
