/**
 * @file license_authentication.c
 * @brief License authentication implementation.
 * @details This file implements the functions for license authentication, including license validation, activation, and random number generation.
 * @addtogroup license_authentication
 * @{
 * @modifier yangzikun
 * @date 2024-12-11
 */

#include "license_auth_access.h"


/**
 * @def LICENSE_ACTIVATE_ACLCMD
 * @brief Command for activating the license.
 */
#define LICENSE_ACTIVATE_ACLCMD             0xFF99

/**
 * @var license_auth_random
 * @brief Global variable to store a random string.
 */
#define LICENSE_AUTH_RANDOM_MAX_LENGTH 64

char license_auth_random[LICENSE_AUTH_RANDOM_MAX_LENGTH] = {0};

/**
 * @var license_auth_sdk_activated
 * @brief Indicates SDK activation status. 1 for activated, 0 for not activated.
 */
int32_t license_auth_sdk_activated = 0;


/**
 * @brief Activates a license.
 * @details Verifies and activates a specified Base64-encoded license file.
 * @param[in] input_file The path to the Base64-encoded license file.
 * @return Activation result.
 * @retval 0 Authentication passed.
 * @retval 1 SDK switch not enabled.
 * @retval 2 License has exceptions.
 * @retval 3 Information mismatch.
 * @retval 4 Printer model mismatch.
 * @retval 5 Unique serial number mismatch.
 * @retval 6 Time information exception.
 */
static int32_t license_auth_validate_license(cJSON *license_data)
{
    // 检查输入参数
    if (!license_data)
    {
        LICENSE_AUTH_ERROR_INT; // 输入数据为空
    }

    // 提取关键字段
    const char *application_name = license_auth_get_string_from_object(license_data, LICENSE_AUTH_APPNAME_FIELD);
    const char *issuer = license_auth_get_string_from_object(license_data, LICENSE_AUTH_ISSUER_FIELD);
    const char *printer_model = license_auth_get_string_from_object(license_data, LICENSE_AUTH_PRINTER_MODE_FIELD);
    const char *binding_mode = license_auth_get_string_from_object(license_data, LICENSE_AUTH_BIND_MODE_FIELD);
    const char *binding_data = license_auth_get_string_from_object(license_data, LICENSE_AUTH_BIND_DATA_FIELD);

    // 判断关键字段是否为空并记录具体字段名称
    if (!application_name)
    {
        pi_log_d("Error: Application name is NULL.\n");
        return LICENSE_AUTH_ERROR_BINDING;
    }
    if (!issuer)
    {
        pi_log_d("Error: Issuer is NULL.\n");
        return LICENSE_AUTH_ERROR_BINDING;
    }
    if (!printer_model)
    {
        pi_log_d("Error: Printer model is NULL.\n");
        return LICENSE_AUTH_ERROR_BINDING;
    }
    if (!binding_mode)
    {
        pi_log_d("Error: Binding mode is NULL.\n");
        return LICENSE_AUTH_ERROR_BINDING;
    }
    if (!binding_data)
    {
        pi_log_d("Error: Binding data is NULL.\n");
        return LICENSE_AUTH_ERROR_BINDING;
    }

    // 获取序列号和打印机名称
    char serial_number[32] = {0};
    char printer_name[64] = {0};
    pi_platform_get_print_name_string(printer_name, sizeof(printer_name));
    pi_platform_get_serial_number(serial_number, sizeof(serial_number));

    // 日志记录
    pi_log_d("binding_data = %s, printer_name = %s, serial_number = %s\n", binding_data, printer_name, serial_number);

    // 绑定模式和序列号验证
    if (strcmp(binding_mode, "b01") != 0)
	{
        pi_log_d("Error: Invalid binding mode: %s\n", binding_mode);
        return LICENSE_AUTH_ERROR_BINDING; // 绑定模式验证失败
    }
    size_t binding_data_len = strlen(binding_data);
    size_t serial_number_len = strlen(serial_number);
    if (serial_number_len == 0 || binding_data_len != serial_number_len || strncmp(binding_data, serial_number, serial_number_len) != 0)
	{
        pi_log_d("Error: Binding data and serial number mismatch.\n");
        return LICENSE_AUTH_ERROR_BINDING; // 序列号验证失败
    }

    // 打印机模式验证
    if (strncmp(printer_model, printer_name, strlen(printer_model)) != 0)
	{
        pi_log_d("Error: Printer model mismatch.\n");
        return LICENSE_AUTH_ERROR_PRINTER; // 打印机模式验证失败
    }

    // 应用名称和发行者验证
    if (strcmp(application_name, "pantumSDK") == 0 && strncmp(issuer, "pantumtools",strlen("pantumtools")) == 0)
	{
        pi_log_d("Check_License OK\n");
        return LICENSE_AUTH_SUCCESS; // 验证成功
    }
	else
	{
        pi_log_d("Check_License FAIL\n");
        license_auth_remove_sdk_license(); // 移除 License 标志位
        return LICENSE_AUTH_ERROR_APPNAME; // 应用名称或发行者验证失败
    }
}


/**
 * @brief 激活 License
 * @details 验证并激活指定的 License 文件
 * @param[in] input_file 输入 Base64 编码的 License 文件路径
 * @return 0: 认证通过 1: SDK 开关未打开 2: License 存在异常 3: 信息不匹配 4: 机型不匹配 5: 唯一序列号不匹配 6: 时间信息异常
 */
static int32_t license_auth_activate_license(char *input_file)
{
    if (!input_file)
    {
        pi_log_d("Error: Input file is NULL.\n");
        return 1; // 参数无效
    }

    int32_t error_code = 0;

    // 定义定时任务
    LICENSE_AUTH_PT_CB_S cut_time_task = {
        .time0 = LICENSE_AUTH_PERIOD_TIME,
        .time1 = LICENSE_AUTH_PERIOD_TIME,
        .start_routine = license_auth_start_pthread_cuttime
    };

    // 解码 Base64 文件为临时文件
    if (!license_auth_decode_base64_to_file(input_file, LICENSE_AUTH_TMP_DE_BASE64))
    {
        pi_log_d("Failed to decode Base64 file.\n");
        error_code = 2; // License 异常
        goto cleanup;
    }

    // 从临时文件中解析 cJSON 数据
    cJSON *json_item = license_auth_get_cjson_from_file(LICENSE_AUTH_TMP_DE_BASE64);
    if (!json_item)
    {
        pi_log_d("Failed to parse cJSON from file.\n");
        error_code = 2; // License 异常
        goto cleanup;
    }
    pi_log_d("1557 cjson_item addr %p\n",json_item);

    // 解密 cJSON 数据
    cJSON *decrypted_license = license_auth_decrypt_cjson(json_item, LICENSE_AUTH_PRIVATE1_KEY);

    LICENSE_AUTH_SAFE_DELETE_JSON(json_item);

    if (!decrypted_license)
    {
        pi_log_d("Failed to decrypt cJSON item.\n");
        error_code = 2; // License 校验失败
        goto cleanup;
    }

    // 提取并验证时间信息
    if (!license_auth_get_item_file_from_object(decrypted_license, LICENSE_AUTH_TIME_FIELD, 1, LICENSE_AUTH_LIMITATION_FILE))
    {
        pi_log_d("Failed to extract time field.\n");
        error_code = 6; // 时间信息异常

        LICENSE_AUTH_SAFE_DELETE_JSON(decrypted_license);

        goto cleanup;
    }

    // 删除旧 License 文件
    if (!access(LICENSE_AUTH_SDK_LICENSE, F_OK))
    {
        remove(LICENSE_AUTH_SDK_LICENSE);
    }

    // 验证 License 数据
    error_code = license_auth_validate_license(decrypted_license);
    LICENSE_AUTH_SAFE_DELETE_JSON(decrypted_license);

    if (error_code != 0)
    {
        goto cleanup;
    }

    // 检查时间限制并创建定时任务
    long long license_time = license_auth_check_time(LICENSE_AUTH_LIMITATION_FILE);
    if (license_time != 0)
    {
        license_auth_create_timer_thread(&cut_time_task);

        if (access(LICENSE_AUTH_TIME_LIMITATION_FILE, F_OK))
        {
            license_auth_copy_file(LICENSE_AUTH_LIMITATION_FILE, LICENSE_AUTH_TIME_LIMITATION_FILE);
        }
    }
    else
    {
        // License 为无限期
        remove(LICENSE_AUTH_TIME_LIMITATION_FILE);
        license_auth_copy_file(LICENSE_AUTH_LIMITATION_FILE, LICENSE_AUTH_TIME_LIMITATION_FILE);
    }

    // 清理临时路径
    system(LICENSE_AUTH_RM_PATH);

cleanup:
    return error_code;
}


/**
 * @brief 解析字符串为 LICENSE_AUTH_DST_S 数据结构
 * @param[in] input 输入的字符串
 * @return 返回 LICENSE_AUTH_DST_S 结构的指针，需调用者释放，解析失败返回 NULL
 */
static LICENSE_AUTH_DST_S *license_auth_parse_dst(const char *input)
{
    if (input == NULL || strlen(input) == 0)
    {
        pi_log_d("Error: Input is NULL or empty.\n");
        return NULL;
    }

    int32_t input_length = strlen(input);
    int32_t current_line = 0;
    int32_t total_lines = license_auth_get_line_count(input);
    pi_log_d("total_lines %d\n",total_lines);

    if (total_lines <= 0)
    {
        pi_log_d("Error: No valid lines found in input.\n");
        return NULL;
    }

    // 分配 LICENSE_AUTH_DST_S 数据结构和字符串缓冲区
    LICENSE_AUTH_DST_S *dst_data = (LICENSE_AUTH_DST_S *)malloc(total_lines * sizeof(LICENSE_AUTH_DST_S) + input_length);
    if (!dst_data)
    {
        return NULL;
    }

    memset(dst_data, 0, total_lines * sizeof(LICENSE_AUTH_DST_S) + input_length);

    // 将原始字符串存储到分配的内存中
    char *buffer = (char *)dst_data + total_lines * sizeof(LICENSE_AUTH_DST_S);
    strncpy(buffer, input, input_length);

    dst_data[current_line].name = buffer;

    int32_t processed_lines = 0;

    for (int32_t i = 0; i < input_length; ++i)
    {
        if (processed_lines == total_lines - 1)
        {
            break;
        }

        if (buffer[i] == ' ')
        {
            int32_t spaces_to_skip = license_auth_trim_leading_spaces(&buffer[i]);
            pi_log_d("license_auth_trim_leading_spaces %d\n",spaces_to_skip);

            buffer[i] = '\0';
            i += spaces_to_skip;
            continue;
        }
        else if (buffer[i] == ':')
        {
            if (buffer[i - 1] == '\0' || buffer[i - 1] == ' ')
            {
                dst_data[current_line].string = &buffer[i + 1];
                ++i;
                continue;
            }
            else
            {
                buffer[i] = '\0';
                dst_data[current_line].string = &buffer[i + 1];
            }

            continue;
        }
        else if (buffer[i] == '#')
        {
            ++current_line;

            while (i < input_length && buffer[i] != '\n')
            {
                ++i;
            }

            if (i < input_length && buffer[i] == '\n')
            {
                ++processed_lines;
                dst_data[current_line].name = &buffer[i + 1];
            }
        }
    }
        return dst_data;
}


/**
 * @brief 从 LICENSE_AUTH_DST_S 数据结构中获取指定名称对应的字符串
 * @param[in] data LICENSE_AUTH_DST_S 数据结构指针
 * @param[in] name 要查找的名称
 * @return 返回对应的字符串，若未找到或发生错误则返回 NULL
 */
static char *license_auth_get_string_from_dst(LICENSE_AUTH_DST_S *data, const char *name) {
    if (data == NULL || name == NULL)
    {
        pi_log_d("Error: Data or name is NULL.\n");
        return NULL;
    }

    pi_log_d("Looking for name '%s' in LICENSE_AUTH_DST_S.\n", name);

    int32_t i = 0;
    while (data[i].name != NULL)
    {
        // 检查 name 和 string 是否为 NULL
        if (data[i].name == NULL || data[i].string == NULL)
        {
            pi_log_d("Warning: Skipping LICENSE_AUTH_DST_S[%d] due to NULL value.\n", i);
            i++;
            continue;
        }

        pi_log_d("Checking LICENSE_AUTH_DST_S[%d]: name='%s', value='%s'\n", i, data[i].name, data[i].string);
        if (strncmp(data[i].name, name, strlen(name)) == 0)
        {
            return data[i].string;
        }
        i++;
    }

    pi_log_d("Error: Name '%s' not found in LICENSE_AUTH_DST_S.\n", name);
    return NULL;

}



// 检查所有 License 写入的接口，并打印内容
static int32_t cmd_test_get_license_from_nv( int32_t argc, char* argv[] )
{
    // 从 NVRAM 读取整个集合
    LICENSE_COLLECTION_S license_collection = {0};
    if (pi_nvram_get(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, &license_collection, sizeof(LICENSE_COLLECTION_S)) != 0)
    {
        pi_log_d("Error: Failed to read license collection from NVRAM.\n");
        return -1;
    }

    // 遍历每个 License 数据，打印索引和内容
    pi_log_d("Checking all licenses...\n");
    for (int32_t i = 0; i < LICENSE_AUTH_LICENSE_COUNT; ++i)
    {
        pi_log_d("license_collection.licenses[%d].license_index %d : %d\n",i,license_collection.licenses[i].license_index,i+1);
        pi_log_d("license_collection.licenses[%d].license_data %s\n",i,license_collection.licenses[i].license_data);

    }
    pi_log_d("Check complete.\n");

    char buffer[16] = {0};
    pi_log_d("1ret %d \n",license_auth_generate_random_number(buffer, sizeof(buffer), 16));
    pi_log_d("buffer %s\n",buffer);

    char buffer4[8] = {0};
    pi_log_d("44ret %d\n",license_auth_generate_random_number(buffer4, sizeof(buffer4), 9));
    pi_log_d("buffer4 %s\n",buffer4);

    return 0;
}

static int32_t cmd_test_set_license2nv( int32_t argc, char* argv[] )
{
    pi_log_d("License ff\n");

        int32_t result = 0;

        const char *public_key_buffer = NULL;

        // 定义所有公钥
        const char sdk_public_key_1[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                          "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAql4Qazvkcj0kd+DSy02LbDFmjvM55Ho6dfKljEoM5cPV"
                                                          "-----END PUBLIC KEY-----";

        const char sdk_public_key_2[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                          "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmtptpxKefNjlw/eQdyjk8xmb5cquWGe21v35ULvzeXKd"
                                                          "-----END PUBLIC KEY-----";

        //来源：自动化测试开发部
        const char sdk_public_key_3[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
            "MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAq8x8wurx5k/IgjtiFNFWs9EgbAjF1yTxK3xUNOtDPmA0"
            "-----END PUBLIC KEY-----";

        //来源：印点点
        const char sdk_public_key_4[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDH4rLjm1o8EDGGPGM1ZhZAvFF99C9AgWg7cP/4bO68crhgT1ov"
            "-----END PUBLIC KEY-----";

        //来源：北京研发中心
        const char sdk_public_key_5[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
            "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7CG64EDpURvjfau4CyZ7Ouh4oUBclqpj1YPokXAWzZy/"
            "-----END PUBLIC KEY-----";

        const char sdk_public_key_default[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "TO BE WRITEN";

        LICENSE_COLLECTION_S license_collection = {0};

        // 遍历所有公钥并写入 NVRAM
        for (int32_t i = 0; i < LICENSE_AUTH_LICENSE_COUNT; i++)
        {
            // 根据密钥 ID 获取相应的公钥
            switch (i)
            {
                pi_log_d("LICENSE I :%d\n", i);
                case LICENSE_AUTH_PUBKEY_1:
                    public_key_buffer = sdk_public_key_1;
                    break;

                case LICENSE_AUTH_PUBKEY_2:
                    public_key_buffer = sdk_public_key_2;
                    break;

                case LICENSE_AUTH_PUBKEY_3:
                    public_key_buffer = sdk_public_key_3;
                    break;

                case LICENSE_AUTH_PUBKEY_4:
                    public_key_buffer = sdk_public_key_4;
                    break;

                case LICENSE_AUTH_PUBKEY_5:
                    public_key_buffer = sdk_public_key_5;
                    break;
                default:
                    public_key_buffer = sdk_public_key_default;
                    break;
                }

            if(license_auth_lice_data_proc(i, public_key_buffer,&license_collection) != 0)
            {
                pi_log_d("Error: license_auth_lice_data_proc data.\n");

            }

        }

    if (pi_nvram_set(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S), 1, NULL) != 0) {
        pi_log_d("Error: pi_nvram_set =\n");

        return -1; // 存储失败
    }

    return 0;

}

static int32_t license_auth_get_lice_data(uint32_t idx, char* val, uint32_t len)
{
    pi_log_d("acl get licen data idx %ud,len %d\n",idx,len);

    LICENSE_COLLECTION_S license_collection = {0};

    if (pi_nvram_get(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S)) != 0)
    {
        pi_log_d("Error:pi_nvram_get LICENSE_AUTHENTICATION\n");
        return PARSER_ERROR; // 获取失败
    }

    pi_memcpy(val, license_collection.licenses[idx].license_data, MIN(len, pi_strlen(license_collection.licenses[idx].license_data)));

    //netdata_get_mac_addr(s_oid_net_ctx->data_mgr, idx, val, len);
    pi_log_d("val[%u](%s)", idx, val);

    return PARSER_SUCCESS;
}

static int32_t license_auth_set_lice_data(uint32_t idx, char* val, uint32_t len)
{
    if(val == NULL)
    {
        pi_log_d("Error:pi_nvram_get LICENSE_AUTHENTICATION\n");
        return PARSER_ERROR; // 获取失败

    }
    LICENSE_COLLECTION_S license_collection = {0};

    pi_log_d("acl set licen data idx %ud\n",idx);

    if (pi_nvram_get(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S)) != 0)
    {
        pi_log_d("Error:pi_nvram_get LICENSE_AUTHENTICATION\n");
        return PARSER_ERROR; // 获取失败
    }

    //向NV中写入license相关数据
    if(license_auth_lice_data_proc(idx, val,&license_collection) != 0)
    {
        pi_log_d("Error: license_auth_lice_data_proc data.\n");
    }

    pi_log_d("val[%u](%s)", idx, val);
    if (pi_nvram_set(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S), 1, NULL) != 0) {
        pi_log_d("Error: pi_nvram_set LICENSE_AUTHENTICATION\n");

        return PARSER_ERROR; // 存储失败
    }
    return PARSER_SUCCESS;
}

static int32_t license_auth_acl_lice_avtive(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
#if 0 //TODO
    // 检查面板 SDK 是否启用
    unsigned int32_t panel_SDK_status = 0;
    oid_get_uint32(OID_PLATFORM_SDK_ENABLE, OID_INDEX_DEFAULT, &panel_SDK_status);
    if (panel_SDK_status == 0)
    {
        pi_log_d("Panel SDK is disabled: %u\n", panel_SDK_status);
        return PARSER_ERROR;
    }
#endif

    char *sdk_file_path = "/event/License_SDK";
    int32_t file_descriptor = -1; // 文件描述符初始化
    char *read_buffer = NULL; // 动态分配的缓冲区指针

    // 检查 SDK 功能是否已启用
    if (access(sdk_file_path, F_OK) == 0)
    {
        pi_log_d("SDK function is already open.\n");
        return PARSER_ERROR;
    }

    // 获取 ACL 数据长度
    int32_t acl_data_length = *(int32_t *)(acl_cmd->temp);
    pi_log_d("ACL data length = %x\n", acl_data_length);

    // 分配内存缓冲区
    read_buffer = malloc(LICENSE_AUTH_LICENSE_BUF_LENGTH * 4);
    if (read_buffer == NULL)
    {
        pi_log_d("Memory allocation failed.\n");
        return PARSER_ERROR;
    }
    memset(read_buffer, 0, LICENSE_AUTH_LICENSE_BUF_LENGTH * 4);

    // 从 pgqio 中读取数据
    int32_t bytes_read = parser_common_read(pgqio, (uint8_t *)read_buffer, acl_data_length, PARSER_USB_TIMEOUT);
    if (bytes_read < 0)
    {
        pi_log_d("Error reading from pgqio.\n");
        LICENSE_AUTH_SAFE_FREE(read_buffer);
        return PARSER_ERROR;
    }
    pi_log_d("pgqio Read: %d bytes.\n", bytes_read);

    // 打开或创建文件
    file_descriptor = open(sdk_file_path, O_RDWR | O_CREAT, 0777);
    if (file_descriptor < 0)
    {
        pi_log_d("Failed to open file: %s, Error: %s\n", sdk_file_path, strerror(errno));
        LICENSE_AUTH_SAFE_FREE(read_buffer);
        return PARSER_ERROR;
    }

    // 写入文件
    if (bytes_read != write(file_descriptor, read_buffer, bytes_read))
    {
        pi_log_d("Failed to write file: %s, Error: %s\n", sdk_file_path, strerror(errno));
        LICENSE_AUTH_ERROR_CLOSE(file_descriptor);
        LICENSE_AUTH_SAFE_FREE(read_buffer);
        return PARSER_ERROR;
    }

    // 释放文件描述符和缓冲区
    LICENSE_AUTH_SAFE_CLOSE(file_descriptor);
    LICENSE_AUTH_SAFE_FREE(read_buffer);

    // 激活 License
    int32_t activation_result = license_auth_activate_license(sdk_file_path);
    if (activation_result != 0)
    {
        remove(sdk_file_path);
    }
    pi_log_d("Activation result = %d\n", activation_result);

    // 获取 SDK 时间版本并发送消息
    int32_t sdk_version_time = license_auth_get_license_version();
    //AclMsgSendToPanel(MSG_SDK_PANEL_INFO, activation_result, sdk_version_time);

    return PARSER_SUCCESS;
}


static int32_t cmd_test_get_licens_cmp( int32_t argc, char* argv[] )
{
    // 从 NVRAM 读取整个集合
    int32_t ret = license_auth_compare_file_with_nvram("/pesf_data/bb.c");

    pi_log_d("Check compare %d.\n",ret);
    return 0;
}


/**
 * @brief 打印 LICENSE_AUTH_DST_S 数据内容
 * @param[in] dst_data 指向 LICENSE_AUTH_DST_S 数据结构的指针
 */
static void print_dst_data(const LICENSE_AUTH_DST_S *dst_data) {
    if (dst_data == NULL)
    {
        pi_log_d("LICENSE_AUTH_DST_S data is NULL.\n");
        return;
    }

    const char *base_address = (const char *)dst_data;
    int32_t total_lines = (dst_data[0].name - base_address) / sizeof(LICENSE_AUTH_DST_S) - 1;

    pi_log_d("Printing LICENSE_AUTH_DST_S data (Total lines: %d):\n", total_lines);

    for (int32_t i = 0; i < total_lines; ++i)
    {
        const char *name = dst_data[i].name;
        const char *value = dst_data[i].string;

        if (name == NULL)
        {
            pi_log_d("Line %d: name is NULL\n", i);
        }
        else if (value == NULL)
        {
            pi_log_d("Line %d: name='%s', value is NULL\n", i, name);
        }
        else
        {
            pi_log_d("Line %d: name='%s', value='%s'\n", i, name, value);
        }
    }
}

/**
 * @brief 从文件初始化 LICENSE_AUTH_DST_S 数据结构
 * @param[in] file 输入文件路径
 * @return 返回初始化的 LICENSE_AUTH_DST_S 数据结构，需调用者释放，失败返回 NULL
 */
static LICENSE_AUTH_DST_S *license_auth_dst_config_init(const char *file)
{
    if (file == NULL)
    {
        return NULL;
    }

    int32_t file_descriptor = open(file, O_RDONLY, 0777);
    if (file_descriptor < 0)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    unsigned long file_length = license_auth_get_file_length(file_descriptor);
    char *file_buffer = malloc(file_length + 1);
    if (file_buffer == NULL)
    {
        LICENSE_AUTH_ERROR_CLOSE(file_descriptor);
        return NULL;
    }

    memset(file_buffer, 0, file_length + 1);

    if (file_length != read(file_descriptor, file_buffer, file_length))
    {
        LICENSE_AUTH_ERROR_CLOSE(file_descriptor);
        LICENSE_AUTH_SAFE_FREE(file_buffer);
        return NULL;
    }

    LICENSE_AUTH_DST_S *dst_data = license_auth_parse_dst(file_buffer);
    LICENSE_AUTH_SAFE_FREE(file_buffer);
    LICENSE_AUTH_SAFE_CLOSE(file_descriptor);
    print_dst_data(dst_data);
    return dst_data;
}


/**
 * @brief 删除 SDK 相关的许可文件。
 * 此函数负责清理与 SDK 授权相关的所有文件，包括限制文件、许可文件以及其他关联资源。
 * 调用该函数后，文件系统会被同步以确保数据一致性。
 * @note 该函数会直接操作文件系统并执行系统命令，因此调用前请确保具有相应权限。
 * @return int32_t 始终返回 0，表示操作完成。
 * @details 此函数执行以下操作：
 * - 删除限制文件 (`LICENSE_AUTH_LIMITATION_FILE`)。
 * - 删除 SDK 许可文件 (`LICENSE_AUTH_SDK_LICENSE`)。
 * - 删除时间限制文件 (`LICENSE_AUTH_TIME_LIMITATION_FILE`)。
 * - 删除硬编码的事件文件 (`/event/License_SDK`)。
 * - 使用 `sync` 命令同步文件系统，确保数据一致性。
 */
int32_t license_auth_remove_sdk_license(void)
{
    remove(LICENSE_AUTH_LIMITATION_FILE);
    remove(LICENSE_AUTH_SDK_LICENSE);
    remove(LICENSE_AUTH_TIME_LIMITATION_FILE);
    remove("/event/License_SDK");
    system("sync");
    return 0;
}

/**
 * @brief 查询 SDK 激活状态
 * @details 检查全局变量 `license_auth_sdk_activated` 的值，判断 SDK 是否已激活
 * @return 激活返回 1，未激活返回 0
 */
int32_t license_auth_is_sdk_activated(void)
{
    pi_log_d("SDK activation status: %s\n", license_auth_sdk_activated ? "Activated" : "Not Activated");
    return license_auth_sdk_activated;
}

/**
 * @brief Initializes the SDK license.
 * @details Checks and initializes the SDK license file. If it does not exist, activates the license and updates the status.
 * @return 1 if initialization succeeded, 0 otherwise.
 */
int32_t license_auth_initialize_sdk_license(void)
{
    // 检查 License 文件是否存在
    int32_t license_file_status = access("/event/License_SDK", F_OK);

    if (!license_file_status)
    {
        // License 文件不存在，清理相关文件
        remove(LICENSE_AUTH_LIMITATION_FILE);
        remove(LICENSE_AUTH_SDK_LICENSE);

        // 激活 License
        license_file_status = license_auth_activate_license("/event/License_SDK");
    }

    // 更新全局激活状态
    license_auth_sdk_activated = (license_file_status == 0);
    pi_log_d("sdk_activated: %d\n", license_auth_sdk_activated);

    return license_auth_sdk_activated;
}

/**
 * @brief 验证访问授权
 * @param[in] input_buffer 输入缓冲区，包含待验证的授权信息
 * @return 成功返回0
 */
int32_t license_auth_access_authentication(const char *input_buffer)
{
    system("rm -rf /tmp/ztmp_access*");

    if (!input_buffer)
    {
        pi_log_d("Invalid input buffer!\n");
        return -1;
    }
    pi_log_d("input_buffer %s!\n",input_buffer);

    // 跳过认证的特殊条件
    if (!access("/tmp/skipac", F_OK) || !access("/settings/skipac", F_OK))
    {
        cJSON *authentication_response = cJSON_CreateObject();
        cJSON_AddStringToObject(authentication_response, "type", "0");
        cJSON_AddStringToObject(authentication_response, "Version", "0.0.1");
        pi_log_d("/tmp/skipac or /settings/skipac exist\n");
        return -1;
    }

    pi_log_d("Start license_auth_access_authentication!\n");

    // 解析输入的 JSON 数据
    cJSON *license_info = cJSON_Parse(input_buffer);
    if (license_info == NULL)
    {
        pi_log_d("Invalid JSON format in input buffer!\n");
        return -1;
    }

    // 解码 LicenseInfo 数据
    unsigned char decoded_base64[2048] = {0};
    license_auth_base64_decode(license_auth_get_string_from_object(license_info, "LicenseInfo"), decoded_base64);

    cJSON *identity_info = cJSON_Parse((const char *)decoded_base64);
    if (identity_info == NULL)
    {
        pi_log_d("Decoded identity is not a valid JSON structure!\n");
        LICENSE_AUTH_SAFE_DELETE_JSON(license_info);

        return -1;
    }

    license_auth_write_cjson_to_file(identity_info, "/tmp/ztmp_access_info2");

    // 获取公钥文件路径
    char *pub_key_file = license_auth_get_item_file_from_object(identity_info, LICENSE_AUTH_JSON_PUB_KEY, 0, LICENSE_AUTH_ACCESS_PUB_KEY_FILE);
    pi_log_d("[debug] pub_key_file %s \n",pub_key_file);

    if (license_auth_compare_file_with_nvram(pub_key_file) != 0)
    {
        LICENSE_AUTH_SAFE_DELETE_JSON(identity_info);
        LICENSE_AUTH_SAFE_DELETE_JSON(license_info);

        return -1;
    }

    // 验签逻辑
    license_auth_decode_base64_to_file(license_auth_get_string_from_object(identity_info, LICENSE_AUTH_JSON_SIGN), LICENSE_AUTH_ACCESS_SIGN_FILE);
    cJSON_DeleteItemFromObject(identity_info, LICENSE_AUTH_JSON_SIGN);

    if (!license_auth_verify_sign(license_auth_write_cjson_to_file(identity_info, LICENSE_AUTH_ACCESS_SIGNED_FILE),LICENSE_AUTH_ACCESS_SIGN_FILE,pub_key_file) ||
		!license_auth_verify_sign(license_auth_write_unformatted_cjson_to_file(identity_info, LICENSE_AUTH_ACCESS_UNFORMAT_SIGNED),LICENSE_AUTH_ACCESS_SIGN_FILE,pub_key_file))
    {
        pi_log_d("sign ok\n");
    }
    else
    {
        pi_log_d("sign fail\n");
        LICENSE_AUTH_SAFE_DELETE_JSON(identity_info);
        LICENSE_AUTH_SAFE_DELETE_JSON(license_info);

        return -1;
    }

    // 加载配置数据
    LICENSE_AUTH_DST_S *config_data = license_auth_dst_config_init("./root/config");

    if (config_data == NULL)
    {
        pi_log_d("Failed to load configuration data!\n");
        LICENSE_AUTH_SAFE_DELETE_JSON(identity_info);
        LICENSE_AUTH_SAFE_DELETE_JSON(license_info);

        return -1;
    }

    // 提取验证相关字段
    char *type = license_auth_get_string_from_object(identity_info, LICENSE_AUTH_JSON_TYPE);
    char *id = license_auth_get_string_from_object(identity_info, LICENSE_AUTH_JSON_ID);
    char *version = license_auth_get_string_from_object(identity_info, LICENSE_AUTH_JSON_VERSION);
    char *random = license_auth_get_string_from_object(identity_info, LICENSE_AUTH_JSON_RANDOM);
    #if 0
    //pi_log_d("type: %s\n", type);
    printf("id: %s\n", id);
    printf("Version: %s\n", version);
    printf("random: %s\n", random);
    printf("license_auth_random: %s\n", license_auth_random);
    printf("sss: %s\n", license_auth_random);
    //printf("config_data: %s\n", config_data);

    #if 0
    printf("strncmp|version:%d\n", strncmp(version, license_auth_get_string_from_dst(config_data, LICENSE_AUTH_JSON_VERSION), strlen(version)));
    printf("strncmp|id:%d\n", (strncmp(id, license_auth_get_string_from_dst(config_data, "ID_1"), strlen(id)) & strncmp(id, license_auth_get_string_from_dst(config_data, "ID_2"), strlen(id))));
    printf("strncmp|ID_1:%d\n", strncmp(id, license_auth_get_string_from_dst(config_data, "ID_1"), strlen(id)));
    printf("strncmp|ID_2:%d\n", strncmp(id, license_auth_get_string_from_dst(config_data, "ID_2"), strlen(id)));
    #else
    char *version_value = license_auth_get_string_from_dst(config_data, LICENSE_AUTH_JSON_VERSION);
    char *id_1_value = license_auth_get_string_from_dst(config_data, "ID_1");
    char *id_2_value = license_auth_get_string_from_dst(config_data, "ID_2");

    if (version_value) {
        printf("strncmp|version:%d\n", strncmp(version, version_value, strlen(version_value)));
    } else {
        printf("Error: Retrieved value is NULL for field '%s'.\n", LICENSE_AUTH_JSON_VERSION);
    }

    if (id_1_value && id_2_value) {
        printf("strncmp|id:%d\n", strncmp(id, id_1_value, strlen(id_1_value)) &
                                    strncmp(id, id_2_value, strlen(id_2_value)));
        printf("strncmp|ID_1:%d\n", strncmp(id, id_1_value, strlen(id_1_value)));
        printf("strncmp|ID_2:%d\n", strncmp(id, id_2_value, strlen(id_2_value)));
    } else {
        if (!id_1_value) {
            printf("Error: Retrieved value is NULL for field 'ID_1'.\n");
        }
        if (!id_2_value) {
            printf("Error: Retrieved value is NULL for field 'ID_2'.\n");
        }
    }

    #endif
    #else
    //pi_log_d("type :%s\n", type);
    pi_log_d("id :%s\n", id);
    pi_log_d("Version :%s\n", version);
    pi_log_d("random :%s\n", random);
    pi_log_d("license_auth_random : %s\n", license_auth_random);
    pi_log_d("strncmp|version:%d\n", strncmp(version, license_auth_get_string_from_dst(config_data, LICENSE_AUTH_JSON_VERSION), strlen(version)));
    pi_log_d("strncmp|id:%d\n", (strncmp(id, license_auth_get_string_from_dst(config_data, "ID_1"), strlen(id)) &
                strncmp(id, license_auth_get_string_from_dst(config_data, "ID_2"), strlen(id))));
    pi_log_d("strncmp|ID_1:%d\n", strncmp(id, license_auth_get_string_from_dst(config_data, "ID_1"), strlen(id)));
    pi_log_d("strncmp|ID_2:%d\n", strncmp(id, license_auth_get_string_from_dst(config_data, "ID_2"), strlen(id)));
    #endif

    // 认证逻辑
    if (license_auth_random[0] != '\0')
    {
        if (strncmp(random, license_auth_random, strlen(license_auth_random)) != 0 ||
            (strncmp(id, license_auth_get_string_from_dst(config_data, "ID_1"), strlen(id)) != 0 &&
             strncmp(id, license_auth_get_string_from_dst(config_data, "ID_2"), strlen(id)) != 0 &&
             strncmp(id, license_auth_get_string_from_dst(config_data, "ID_3"), strlen(id)) != 0) ||
             strncmp(version, license_auth_get_string_from_dst(config_data, LICENSE_AUTH_JSON_VERSION), strlen(version)) != 0)
        {
            pi_log_d("Authentication failed!\n");
            LICENSE_AUTH_SAFE_FREE(config_data);
            LICENSE_AUTH_SAFE_DELETE_JSON(identity_info);
            LICENSE_AUTH_SAFE_DELETE_JSON(license_info);
            return -1;
        }

        pi_log_d("Authentication succeeded!\n");
    }

    LICENSE_AUTH_SAFE_FREE(config_data);
    LICENSE_AUTH_SAFE_DELETE_JSON(license_info);
    LICENSE_AUTH_SAFE_DELETE_JSON(identity_info);

    return 0;
}

/**
 * @brief Generates a random hexadecimal string of the specified length.
 * @param[in] buffer_size Target length of the hexadecimal string.
 * @param[in] length random of the hexadecimal string.
 * @param[out] buffer User-provided buffer to store the generated hexadecimal string.
 * @return Returns 0 on success, -1 on failure.
 */
int32_t license_auth_generate_random_number(char *buffer, size_t buffer_size, int32_t length)
{
    // Validate input parameters
    if (length <= 0 || buffer == NULL || buffer_size <= 0)
    {
        printf("Error: Invalid input parameters. Length: %d, Buffer size: %zu\n", length, buffer_size);
        return -1;
    }

    // Check if buffer can accommodate the requested length (including '\0')
    if (length > buffer_size)
    {
        printf("Error: Buffer size too small for requested length. Buffer size: %zu, Required: %d\n", buffer_size, length);
        return -1;
    }

    // Calculate the number of bytes needed (round up for odd lengths)
    int32_t byte_count = (length + 1) / 2;

    // Allocate temporary random buffer
    unsigned char *random_buffer = (unsigned char *)calloc(byte_count, sizeof(unsigned char));
    if (!random_buffer)
    {
        printf("Error: Memory allocation failed for random_buffer.\n");
        return -1;
    }

    // Generate random bytes
    srand((unsigned int)time(NULL));
    for (int32_t i = 0; i < byte_count; ++i)
    {
        random_buffer[i] = rand() % 256; // Generate random byte
    }

    // Convert random bytes to hexadecimal string
    for (int32_t i = 0; i < length / 2; ++i)
    {
        snprintf(buffer + 2 * i, 3, "%02x", random_buffer[i]); // Ensure no overflow
    }

    // Handle the last half-byte if length is odd
    if (length % 2 != 0) {
        snprintf(buffer + 2 * (length / 2), 2, "%01x", random_buffer[length / 2] >> 4);
    }

    // Ensure null-termination
    buffer[length] = '\0';

    // Save the result in the global buffer
    size_t buffer_length = strlen(buffer);
    if (buffer_length >= LICENSE_AUTH_RANDOM_MAX_LENGTH)
    {
        strncpy(license_auth_random, buffer, LICENSE_AUTH_RANDOM_MAX_LENGTH - 1);
        license_auth_random[LICENSE_AUTH_RANDOM_MAX_LENGTH - 1] = '\0';
    }
    else
    {
        strncpy(license_auth_random, buffer, buffer_length + 1);
    }

    // Free temporary random buffer
    LICENSE_AUTH_SAFE_FREE(random_buffer);

    return 0; // Success
}

int32_t license_auth_prolog(void)
{
#if 1
    cmd_register("pri", "licen_get", cmd_test_get_license_from_nv, NULL);
    cmd_register("pri", "licen_set", cmd_test_set_license2nv, NULL);
    cmd_register("pri", "cmp", cmd_test_get_licens_cmp, NULL);
#endif

    acl_register_cmd(LICENSE_ACTIVATE_ACLCMD, license_auth_acl_lice_avtive, (void *)0);
    acl_attribute_string_index_register("OID_LICENSE_PUBLICKEY", license_auth_set_lice_data, license_auth_get_lice_data);

    license_auth_initialize_sdk_license();

    int32_t result = 0;
    char authentication_license_id[32] = {0};

    const char *public_key_buffer = NULL;

    // 定义所有公钥
    const char sdk_public_key_1[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAql4Qazvkcj0kd+DSy02LbDFmjvM55Ho6dfKljEoM5cPV"
                                                    "UVnRd18RQVnnjW9XXhhHjposT4HdnwuXrlz1Utuf5vGDI3QX6PcoDih8vuU2mP5qNa6FMXFffGSr83REahur/JQo"
                                                    "SJ6nnJH2PJS1KOEUEXFH2r3v+C+z1looArCyxq48PycCfvhUmZD25r1TDsy7cNfbKQyenKLLa6sy1PwZUYBWLRKN"
                                                    "7Pv7ZWTF/KJm2+i4tAB+FRUwyVHjrlMlZtazMm29opCs1VergibALqsC4d2u+c7agmGbR7gqni3VGS5P897NOxqP"
                                                    "3Dts17K914TRrWQ3TNnuQjHdFqXN1I7Q5wIDAQAB"
                                                    "-----END PUBLIC KEY-----";

    const char sdk_public_key_2[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmtptpxKefNjlw/eQdyjk8xmb5cquWGe21v35ULvzeXKd"
                                                    "m6ECeVwYOLx6bv4+21RbIvv1PAKav1mT+fPeDZTvuXfvHSJkYJ2HA+fQESnefokGipiCfFdfYRTq8vxanH48kvQm"
                                                    "UkYpUZZZXRaVEiB4FYfymFdreU9gvL/mF6omvaShyILEQ74c3naBpboJhGhqJ5l5XOz/Mlpx1m9nmB3tT0H6Z1jO"
                                                    "J5+LqVen3KcgHAg1lbkg9YN7WU27dYpieiFFHvO00drBOvr+48w265b0AaoCLbU9TDTONdqzWs6pyWKhaKZhd0MV"
                                                    "mBoMFIKh8TUZVtCcaiC3gBlTCc7Nfo6brQIDAQAB"
                                                    "-----END PUBLIC KEY-----";

    //来源：自动化测试开发部
    const char sdk_public_key_3[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                    "MIIBojANBgkqhkiG9w0BAQEFAAOCAY8AMIIBigKCAYEAq8x8wurx5k/IgjtiFNFWs9EgbAjF1yTxK3xUNOtDPmA0"
                                                    "Oh+yl+3HGZ3TGwZq9YSTT0WxplEW6GZcPPOi0j1NNgqTp6vIm3B9CjTElj4zF/csuyKRTGm8IqGzTBVcAEFCo2Gv"
                                                    "othpxZS7/umeXbfMDRKIwuEEq6xyyx9ic20DGGMyeynpqPgsoWWrKA7Z0kgxdRLswRRpx567gwnuxGrDyUioYHHV"
                                                    "oTTFvFyyKgSUy/hY5fipPCFzcT8t9aCQ55p3Qa6ht+RKVCa4un/WT5V+n8KZfOdSUjpjXL3p9jjOJxWfVtflUdv4"
                                                    "jdpRN0txC/GHKXMDvLV14Cu50tU/0c3wyOzwgjhABktQt9qRo7bx1TAM+j+R7oIE4eAIJyfu2KsXlKZjykoWU1f3"
                                                    "e+YOTltxtWmbQWthE4WUVbSQYMeG4bE/mtATNx92dpq/ybUE8olBN4Qxh7EdZrfNIJNVwmIhzVEbDVfuv9G/HLI+"
                                                    "kOrhl0nYe88whqRqFcUS5Ktkitq1AgMBAAE="
                                                    "-----END PUBLIC KEY-----";

    //来源：印点点
    const char sdk_public_key_4[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDH4rLjm1o8EDGGPGM1ZhZAvFF99C9AgWg7cP/4bO68crhgT1ov"
                                                    "bLL06QNl5LsoMQWgnO5mGxbTVF8hJZoli7r4LqCc91GtmDJmmSrzsw6GcuUif3bx8F79D54UOH4XBH6JIAm0AUls"
                                                    "Se4zhhJws9tQXo4KnQ4d6G7LGZc6mCp81QIDAQAB"
                                                    "-----END PUBLIC KEY-----";

    //来源：北京研发中心
    const char sdk_public_key_5[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "-----BEGIN PUBLIC KEY-----"
                                                    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7CG64EDpURvjfau4CyZ7Ouh4oUBclqpj1YPokXAWzZy/"
                                                    "kXJGpwr4ZMaT5Oz2xcQbYhQd68pz9bemgYYKmQEMbcl9CasUYEeeAHszBWHR18Pq6JUCGAOEcQxHk24FeS3xqVGA"
                                                    "3j/qXK3rcKin9h+Gx4GyTiY+U1/BIP/EqpJh3Vj/KX5E/w1057loqwdaOhXT00aIEdB+hbD1iJ34JqktKdydnaOH"
                                                    "Apo3t0+N1TU+OHZMAxauQ+3/o4ZOZvQT4bkja4SuFX4QrOzOnDtLMX0qq8hB5FjHT0a40DlhxVjiBgHh7mjpPqt/"
                                                    "4qBiMG//Hl+jBSuyUK0PPRcstoNEkJco7wIDAQAB"
                                                    "-----END PUBLIC KEY-----";

    const char sdk_public_key_default[LICENSE_AUTH_LICENSE_BUF_LENGTH] = "TO BE WRITEN";

    //从nv中读取LICENSE_COLLECTION_S类型数组的第一个下标中的第一个元素
    LICENSE_COLLECTION_S license_collection = {0};
    if (pi_nvram_get(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S)) != 0)
    {
        pi_log_d("Error:pi_nvram_get LICENSE_AUTHENTICATION\n");
        return -1; // 获取失败
    }
    pi_log_d("license_collection.licenses[0].license_index %d\n",license_collection.licenses[0].license_index);
    if(license_collection.licenses[0].license_index != 0)
    {
        pi_log_d("data has been settings,return\n");
        return 0;
    }

    // 遍历所有公钥并写入 NVRAM
    for (int32_t index = 0; index < LICENSE_AUTH_LICENSE_COUNT; index++)
    {
        // 根据密钥 ID 获取相应的公钥
        switch (index)
        {
            case LICENSE_AUTH_PUBKEY_1:
                public_key_buffer = sdk_public_key_1;
                break;

            case LICENSE_AUTH_PUBKEY_2:
                public_key_buffer = sdk_public_key_2;
                break;

            case LICENSE_AUTH_PUBKEY_3:
                public_key_buffer = sdk_public_key_3;
                break;

            case LICENSE_AUTH_PUBKEY_4:
                public_key_buffer = sdk_public_key_4;
                break;

            case LICENSE_AUTH_PUBKEY_5:
                public_key_buffer = sdk_public_key_5;
                break;
            default:
                public_key_buffer = sdk_public_key_default;
                break;
            }

        //向NV中写入license相关数据
        if(license_auth_lice_data_proc(index, public_key_buffer,&license_collection) != 0)
        {
            pi_log_d("Error: license_auth_lice_data_proc data.\n");
        }

    }

    if (pi_nvram_set(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S), 1, NULL) != 0) {
        pi_log_d("Error: pi_nvram_set LICENSE_AUTHENTICATION\n");

        return -1; // 存储失败
    }

    return result;
}

/**
 * @brief Retrieves the license version.
 * @details Reads the string from the time limitation file and converts it to a numeric value.
 * @return 0 for permanent licenses, 1 for temporary licenses, -1 on failure.
 */
int32_t license_auth_get_license_version(void)
{
    // 获取时间限制文件中的字符串
    char *time_str = license_auth_get_string(LICENSE_AUTH_TIME_LIMITATION_FILE);
    if (time_str == NULL)
    {
        pi_log_d("Error: Failed to retrieve time string from %s\n", LICENSE_AUTH_TIME_LIMITATION_FILE);
        return -1; // -1 表示获取失败
    }

    // 将字符串转换为数字
    long long time_val = atoll(time_str);
    LICENSE_AUTH_SAFE_FREE(time_str);

    // 日志打印当前版本信息
    pi_log_d("SDK version = %lld\n", time_val);

    // 返回版本类型：0 表示永久，1 表示临时
    return time_val == 0 ? 0 : 1;
}




/**
 *@}
 */
