class Beeper {
    constructor() {

    }
    getDuration()
    {
        return getbeepduration();
    }
    setDuration(data)
    {
        setbeepduration(data);
    }
    getFrequency()
    {
        return getbeepfrequency();
    }
    setFrequency(data)
    {
        setbeepfrequency(data);
    }
    getVolume()
    {
        return getbeepvolume();
    }
    setVolume(data)
    {
        setbeepvolume(data);
    }
    start()
    {
        beepstartwork();
    }
    stop()
    {
        beepstopwork();
    }
    getStatus()
    {
        return getworkstatus();
    }
}

globalThis.pedk.Beeper = Beeper
