#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>



#define JS_SHARED_LIBRARY

#ifdef JS_SHARED_LIBRARY /* 生成JS C动态库备选 */
#include "quickjs.h"


#define countof(x) (sizeof(x) / sizeof((x)[0]))

static JSValue js_getCurrentState(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    char buf[16]={0};
    
    //RecvMsgToMfp(MSG_MOUDLE_POWERMANAGER, MSG_POWERMANAGER_SUB, buf, 16);
    
    return JS_NewString(ctx, buf);

}

static JSValue js_requestStateChange(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;
    if (!JS_IsString(argv[0]))
    {
        return JS_EXCEPTION;
    }
    const char *jscstr = JS_ToCString(ctx, argv[0]);

    //SendMsgToMfp(MSG_MOUDLE_PANEL, MSG_PANEL_SUB, jscstr);
    return JS_NewInt32(ctx, ret);
}

static const JSCFunctionListEntry g_js_pesf_lowpower_api_funs[] = {
    JS_CFUNC_DEF("getCurrentState", 0, js_getCurrentState),
    JS_CFUNC_DEF("requestStateChange", 1, js_requestStateChange),
};

static int js_pesf_lowpower_api_int(JSContext* ctx, JSModuleDef* jsmd)
{
    return JS_SetModuleExportList(ctx, jsmd, g_js_pesf_lowpower_api_funs, countof(g_js_pesf_lowpower_api_funs));
}

JSModuleDef *js_init_module(JSContext *ctx, const char *module_name)
{
    JSModuleDef* jsmd = NULL;

    jsmd = JS_NewCModule(ctx, module_name, js_pesf_lowpower_api_int);
    if (jsmd == NULL) {
        return NULL;
    }

    JS_AddModuleExportList(ctx, jsmd, g_js_pesf_lowpower_api_funs, countof(g_js_pesf_lowpower_api_funs));
    return jsmd;
}

#endif

