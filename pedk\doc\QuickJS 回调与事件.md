# PeSF 回调与事件

## 目录

- [PeSF 回调与事件](#pesf-回调与事件)
  - [目录](#目录)
  - [C 调用 JS 函数](#c-调用-js-函数)
    - [C 调用 JS 全局函数](#c-调用-js-全局函数)
    - [C 调用 JS 对象成员函数](#c-调用-js-对象成员函数)
  - [C 操作事件循环](#c-操作事件循环)
    - [事件循环中调用 C 函数](#事件循环中调用-c-函数)
    - [调用 JS 函数](#调用-js-函数)

## C 调用 JS 函数

### C 调用 JS 全局函数

假设有一个 JS 全局函数叫 `my_func_add`, 这个函数是一个 JS 全局函数。

`my_func_add` 函数原型如下:

```javascript
// JS 函数
function my_func_add(a, b) {
    return a + b
}
```
以下是调用的过程。

1. **取得函数对象**
    
    首先通过 JS_GetGlobalObject 函数取得 JS 全局对象。

    ```c
    JSValue js_global = JS_GetGlobalObject(js_context);
    ```

    然后通过 JS_GetPropertyStr 函数取得全局对象中的函数对象。

    通过下面的代码可以取得这个 JS 函数 `my_func_add`:
    ```c
    JSValue js_my_func_add = JS_GetPropertyStr(js_context, js_global, "my_func_add");
    ```

2. **调用 JS 函数**
    检查这个 js_my_func_add 是否是 JS 函数对象

    ```c
    if(JS_IsFunction(js_context, js_my_func_add)) {
        // js_my_func_add 是一个 JS 函数
    }
    else {
        // js_my_func_add 不是一个 JS 函数
    }
    ```

    如果 js_my_func_add 是 JS 函数对象，就可以调用了。
    通过 JS_Call 函数调用 JS 函数对象。

    ```c
    JSValue a = JS_NewInt32(js_context, 10);
    JSValue b = JS_NewInt32(js_context, 20);
    JSValue args[] = { a, b };
    JSValue ret_val = JS_Call(js_context, js_my_func_add, js_global, 2, args);
    ```

### C 调用 JS 对象成员函数

待确认
主要技术问题是如何取得 JS 对象

## C 操作事件循环

JS 是事件驱动的，多数情况下 C 与 JS 交互推荐采用这种方式。

### 事件循环中调用 C 函数

通过调用 JS_EnqueueJob 函数将 C 函数的调用交给JS的事件循环中处理, 

C 函数定义：
```c
JSValue js_event_handler(JSContext* ctx, int argc, JSValueConst* argv) {
    size_t len = 0;
    const char* str = JS_toCStringLen(ctx, &len, argv[0]);
    printf("%s", str);
    JS_FreeCString(ctx, str);
}
```

将 js_event_handler 函数的调用交给 JS 事件循环：

```
// 创建函数参数
JSValue js_msg = JS_NewString(js_context, "msg");
JSValue args[] = {js_msg};

/*
 * 将C函数添加到事件循环中处理
 * js_context 是js运行时上下文
 * js_event_handler 是 JSJobFunc类型
 * 1 表示一个参数
 * args 参数数组
 */

JS_EnqueueJob(js_context, js_event_handler, 1, args);

// 释放JS对象
JS_FreeValue(js_context, js_msg);
```

### 事件循环中调用 JS 函数

TODO




