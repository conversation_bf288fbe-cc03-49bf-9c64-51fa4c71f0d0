/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_observer.h
 * @addtogroup utilities
 * @{
 * @addtogroup event_observer
 * <AUTHOR> <PERSON>n
 * @date 2023-5-8
 * @brief Network common type defined
 */
#ifndef __EVENT_OBSERVER_H__
#define __EVENT_OBSERVER_H__

#include <pol/pol_types.h>
#include <pol/pol_threads.h>
#include <pol/pol_define.h>
#include <pol/pol_list.h>
#include <pol/pol_mem.h>

PT_BEGIN_DECLS

#define SUBJECT_PRINT_STS_ID        0x2001
#define SUBJECT_COPY_STS_ID         0x2002
#define SUBJECT_SCAN_STS_ID         0x2003
#define SUBJECT_FAX_STS_ID          0x2004
#define SUBJECT_WIFI_STS_ID         0x2005
#define SUBJECT_NET_STS_ID          0x2006
#define SUBJECT_SYSJOB_STS_ID       0x2007
#define SUBJECT_QIO_STS_ID          0x2008
#define SUBJECT_LOWPWR_STS_ID       0x2009
#define SUBJECT_FWUPGRADE_STS_ID    0x200A
#define SUBJECT_COPYJOB_STS_ID      0x200B

typedef struct event_observer   EVENT_OBSERVER_S;
typedef struct event_subject    EVENT_SUBJECT_S;

typedef void (*NOTIFY_CALLBACK)(EVENT_OBSERVER_S *, EVENT_SUBJECT_S *);

struct event_observer
{
    struct list_head    observer_node;
    uint32_t            observer_id;
    void*               observer_data;
    NOTIFY_CALLBACK     callback;
};

struct event_subject
{
    struct list_head    observer_head;
    uint32_t            subject_id;
    int32_t             subject_status;
    void*               subject_data;
    PI_MUTEX_T          mutex;
};

/**
 * @brief Construct an observer.
 * @param[in] id   : The ID of this observer.
 * @param[in] data : The data of this observer.
 * @param[in] cb   : The callback function of this observer.
 * @return The observer pointer
 * @retval != NULL : success\n
 *         == NULL : fail
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
EVENT_OBSERVER_S*   observer_construct              (uint32_t id, void* data, NOTIFY_CALLBACK cb);

/**
 * @brief Copy observer.
 * @param[in] observer : The source observer pointer.
 * @return The new observer pointer
 * @retval != NULL : success\n
 *         == NULL : fail
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
EVENT_OBSERVER_S*   observer_copy                   (EVENT_OBSERVER_S* observer);

/**
 * @brief Attach the observer to the subject.
 * @param[in] observer : The observer pointer.
 * @param[in] subject  : The subject pointer.
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
void                observer_attach_to_subject      (EVENT_OBSERVER_S* observer, EVENT_SUBJECT_S* subject);

/**
 * @brief Detach the observer to the subject.
 * @param[in] observer : The observer pointer.
 * @param[in] subject  : The subject pointer.
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
void                observer_detach_from_subject    (EVENT_OBSERVER_S* observer, EVENT_SUBJECT_S* subject);

/**
 * @brief Destruct the observer pointer.
 * @param[in] observer : The observer pointer.
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
void                observer_destruct               (EVENT_OBSERVER_S* observer);

/**
 * @brief Construct an subject.
 * @param[in] id   : The ID of this observer.
 * @param[in] data : The data of this observer.
 * @return The subject pointer
 * @retval != NULL : success\n
 *         == NULL : fail
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
EVENT_SUBJECT_S*    subject_construct               (uint32_t id, void* data);

/**
 * @brief notify this subject to all observer attached.
 * @param[in] subject : The subject pointer.
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
void                subject_notify_observers        (EVENT_SUBJECT_S* subject);

/**
 * @brief Destruct the subject pointer.
 * @param[in] subject : The subject pointer.
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
void                subject_destruct                (EVENT_SUBJECT_S* subject);

PT_END_DECLS

#endif /* __EVENT_OBSERVER_H__ */
/**
 *@}
 */
