#include <quickjs.h>

/* 
    声明 QuickJS C 函数由于初始化回调
*/
JSValue js_test_send2printer(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv);

JSValue js_test_getPrinterEvent(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv);
JSValue js_test_threadInit(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv);							   
							   
JSValue js_test_send2printercopy(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv);