#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_log.h"

#include "pol_threads_private.h"

int32_t pi_threads_prolog(void)
{
    int32_t rv;

    RETURN_VAL_IF((rv = pi_thread_prolog()) != 0, pi_log_e, rv);
    RETURN_VAL_IF((rv = pi_mutex_prolog())  != 0, pi_log_e, rv);
    RETURN_VAL_IF((rv = pi_sem_prolog())    != 0, pi_log_e, rv);
    RETURN_VAL_IF((rv = pi_msgq_prolog())   != 0, pi_log_e, rv);

    return 0;
}

int32_t pi_threads_epilog(void)
{
    pi_thread_epilog();
    pi_msgq_epilog();
    pi_sem_epilog();
    pi_mutex_epilog();

    return 0;
}

