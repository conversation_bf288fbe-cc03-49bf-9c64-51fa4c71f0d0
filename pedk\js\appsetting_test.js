threadInit();
let test = new globalThis.pesf.AppSetting();

let app_setting ={};
app_setting.key1 = "name1";
app_setting.value1 = "value1";

app_setting.key2 = "name2";
app_setting.value2 = "value2";

app_setting.key3 = "name3";
app_setting.value3 = "value3";

app_setting.key4 = "name4";
app_setting.value4 = "value4";

//console.log("getAllAppSettingValue.value ", app_setting.setting_name,app_setting.setting_value);

//新增设置项
var setting_set_test = test.setAppSettingValue(app_setting.key1,app_setting.value1);
console.log("setAppSettingValue ", setting_set_test);

setting_set_test = test.setAppSettingValue(app_setting.key2,app_setting.value2);
console.log("setAppSettingValue ", setting_set_test);

setting_set_test = test.setAppSettingValue(app_setting.key3,app_setting.value3);
console.log("setAppSettingValue ", setting_set_test);

setting_set_test = test.setAppSettingValue(app_setting.key4,app_setting.value4);
console.log("setAppSettingValue ", setting_set_test);

//获取所有的设置项
var setting_getall_test = test.getAllAppSettingValue();
console.log("getAllAppSettingValue.value ", setting_getall_test);

//获取单独的设置项
var setting_get_test = test.getAppSettingValue(app_setting.key1);
console.log("get single ", setting_get_test);

setting_get_test = test.getAppSettingValue(app_setting.key2);
console.log("get single ", setting_get_test);

setting_get_test = test.getAppSettingValue(app_setting.key3);
console.log("get single ", setting_get_test);

setting_get_test = test.getAppSettingValue(app_setting.key4);
console.log("get single ", setting_get_test);



//删除某一项
var setting_delete_test = test.deleteAppSettingValue(app_setting.key1);
console.log("delete result ->  ", setting_delete_test);

//获取所有的设置项
setting_getall_test = test.getAllAppSettingValue();
console.log("getAllAppSettingValue.value ", setting_getall_test);
