/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd
 * @file license_auth_access.h
 * @brief <PERSON><PERSON> file for license authentication access.
 * @details This file contains macros, enumerations, structures, and function declarations for license authentication access, including file handling and JSON parsing.
 * @addtogroup license authentication
 * @{
 * <AUTHOR>
 * @date 2024-12-11
 */

#ifndef _LICENSE_AUTH_ACCESS_H_
#define _LICENSE_AUTH_ACCESS_H_

#include "license_auth_common.h"

#ifdef __cplusplus
extern "C" {
#endif


/***************************************************************************/
/****************************** 宏定义 *************************************/
/***************************************************************************/

/**
 * @def LICENSE_AUTH_PRIVATE1_KEY
 * @brief Path to the private key file.
 */
#define LICENSE_AUTH_PRIVATE1_KEY           "/root/sdk_license_verify/license_private1.pem"

/**
 * @def LICENSE_AUTH_PUB_KEY_PATH
 * @brief Path to the public key directory.
 */
#define LICENSE_AUTH_PUB_KEY_PATH           "/root/SDK_pubkey"

/**
 * @def LICENSE_AUTH_ACCESS_CODE_AESKEY
 * @brief Path to the AES key file.
 */
#define LICENSE_AUTH_ACCESS_CODE_AESKEY     "/root/sdk_license_verify/code_AES.key"

/**
 * @def LICENSE_AUTH_START_TAG
 * @brief The start tag for public key files.
 */
#define LICENSE_AUTH_START_TAG              "-----BEGIN PUBLIC KEY-----"

/**
 * @def LICENSE_AUTH_END_TAG
 * @brief The end tag for public key files.
 */
#define LICENSE_AUTH_END_TAG                "-----END PUBLIC KEY-----"

/**
 * @def LICENSE_AUTH_JSON_* Fields
 * @brief Definitions for JSON field names used in license data.
 */
// License JSON fields
#define LICENSE_AUTH_LICENSE_FIELD          "license"
#define LICENSE_AUTH_AESKEY_FIELD           "AESkey"
#define LICENSE_AUTH_PUBKEY_FIELD           "pubkey"
#define LICENSE_AUTH_SIGN_FIELD             "sign"

// License structure fields
#define LICENSE_AUTH_VERSION_FIELD          "license_version"
#define LICENSE_AUTH_SN_FIELD               "license_SN"
#define LICENSE_AUTH_APPNAME_FIELD          "appName"
#define LICENSE_AUTH_ISSUER_FIELD           "issure"
#define LICENSE_AUTH_VALID_FROM_FIELD       "validfrom"
#define LICENSE_AUTH_VALID_TO_FIELD         "validto"
#define LICENSE_AUTH_OWNER_COUNTRY_FIELD    "ownercountry"
#define LICENSE_AUTH_OWNER_NAME_FIELD       "ownername"
#define LICENSE_AUTH_PRINTER_MODE_FIELD     "printermode"
#define LICENSE_AUTH_BIND_MODE_FIELD        "bindmode"
#define LICENSE_AUTH_BIND_DATA_FIELD        "binddata"
#define LICENSE_AUTH_TIME_FIELD             "time"

// Additional JSON fields
#define LICENSE_AUTH_JSON_SYSTEM            "system"
#define LICENSE_AUTH_JSON_LEVEL             "level"
#define LICENSE_AUTH_JSON_RESERVER          "reserver"
#define LICENSE_AUTH_JSON_TYPE              "type"
#define LICENSE_AUTH_JSON_VERSION           "Version"
#define LICENSE_AUTH_JSON_ID                "ID"
#define LICENSE_AUTH_JSON_CODE              "code"
#define LICENSE_AUTH_JSON_RANDOM            "random"
#define LICENSE_AUTH_JSON_PUB_KEY           "Pub_key"
#define LICENSE_AUTH_JSON_SIGN              "sign"

#define LICENSE_AUTH_RANDOM_LENGTH          64
/***************************************************************************/
/**************************** 枚举/结构体定义 ******************************/
/***************************************************************************/

/**
 * @enum license_auth_pubkey_e
 * @brief Enumeration for different public key sources.
 */
typedef enum {
    LICENSE_AUTH_PUBKEY_1,              // 内部模拟 SDK 接入时
    LICENSE_AUTH_PUBKEY_2,              // 来源：丝印
    LICENSE_AUTH_PUBKEY_3,              // 来源：自动化测试开发部
    LICENSE_AUTH_PUBKEY_4,              // 来源：印点点
    LICENSE_AUTH_PUBKEY_5,              // 来源：北京研发
    LICENSE_AUTH_PUBKEY_DEFAULT        // 默认
} license_auth_pubkey_e;

/**
 * @enum license_auth_error_e
 * @brief Enumeration for error codes in license authentication.
 */
typedef enum {
    LICENSE_AUTH_ERROR_UNKNOWN = -1, /**< Unknown error. */
    LICENSE_AUTH_SUCCESS = 0,        /**< Authentication succeeded. */
    LICENSE_AUTH_ERROR_NULL_FIELD = 1, /**< Field is null. */
    LICENSE_AUTH_ERROR_APPNAME = 3,    /**< Application name or issuer validation failed. */
    LICENSE_AUTH_ERROR_PRINTER = 4,    /**< Printer model validation failed. */
    LICENSE_AUTH_ERROR_BINDING = 5     /**< Binding mode or serial number validation failed. */
} license_auth_error_e;

/**
 * @struct LICENSE_AUTH_DST_S
 * @brief Structure for storing key-value pairs for license authentication.
 */
typedef struct
{
    char *name;   /**< Key name. */
    char *string; /**< Value string. */
} LICENSE_AUTH_DST_S;

/***************************************************************************/
/***************************** 函数声明 ************************************/
/***************************************************************************/

/**
 * @brief Removes SDK-related license files.
 * @details Cleans up all SDK-related license files, including restriction files, license files, and associated resources.
 * Synchronizes the file system to ensure consistency.
 * @note This function performs direct file operations and executes system commands, requiring proper permissions.
 * @return int32_t Always returns 0, indicating completion.
 */
int32_t license_auth_remove_sdk_license(void);

#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_ACCESS_H_ */
/**
 * @}
 */
