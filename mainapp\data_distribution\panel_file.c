/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_file.c
 * @addtogroup panel_dc
 * @{
 * @brief DC accesses the UI file interface
 * <AUTHOR>
 * @date 2025-03-20
 */
#include "panel_file.h"
#include <pthread.h>
#include <stdatomic.h>
#include "cmd.h"
#include "pol/pol_string.h"
#include "pol/pol_mem.h"
#include "pol/pol_log.h"
#include "pol/pol_define.h"
#include "panel_config.h"
#include "panel_dc_cmd_tpyedef.h"
#include "data_distribution.h"

#define PATHNAME_MAX_SIZE   512
#define FILE_MAX_COUNT      4096
#define WAKEUP_TIMEOUT      5

typedef struct _panel_file_resource_s
{
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    void* result;
    uint32_t result_size;
    uint32_t id;
    uint32_t is_completed;
    atomic_int ref_cnt;
    struct _panel_file_resource_s* next;
} panel_file_resource_s;

typedef struct _panel_file_res_list_s
{
    uint32_t last_res_id;
    panel_file_resource_s* head;
    panel_file_resource_s* tail;
    pthread_mutex_t mutex;
} panel_file_res_list_s;

/***********************request struct***********************/
typedef struct _panel_file_access_request_s
{
    uint32_t res_id;
    int32_t mode;
    char pathname[ PATHNAME_MAX_SIZE ];
} panel_file_access_request_s;

typedef struct _panel_file_open_request_s
{
    uint32_t res_id;
    int32_t flags;
    mode_t mode;
    char pathname[ PATHNAME_MAX_SIZE ];
} panel_file_open_request_s;

typedef struct _panel_file_read_request_s
{
    uint32_t res_id;
    int32_t fd;
    size_t count;
} panel_file_read_request_s;

typedef struct _panel_file_write_request_s
{
    uint32_t res_id;
    int32_t fd;
    size_t count;
    uint8_t buf[ FILE_MAX_COUNT ];
} panel_file_write_request_s;

typedef struct _panel_file_seek_request_s
{
    uint32_t res_id;
    int32_t fd;
    off_t offset;
    int32_t whence;
} panel_file_seek_request_s;

typedef struct _panel_file_close_request_s
{
    uint32_t res_id;
    int32_t fd;
} panel_file_close_request_s;

/***********************result struct***********************/
typedef struct _panel_file_common_result_s
{
    int32_t ret;
    int32_t err_num;
} panel_file_common_result_s;

typedef struct _panel_file_read_result_s
{
    ssize_t ret;
    int32_t err_num;
    uint8_t buf[ FILE_MAX_COUNT ];
} panel_file_read_result_s;

typedef struct _panel_file_write_result_s
{
    ssize_t ret;
    int32_t err_num;
} panel_file_write_result_s;

typedef struct _panel_file_seek_result_s
{
    off_t ret;
    int32_t err_num;
} panel_file_seek_result_s;

static panel_file_res_list_s gs_res_list =
{
    .last_res_id = 1,
    .head = NULL,
    .tail = NULL,
    .mutex = PTHREAD_MUTEX_INITIALIZER,
};

static void add_to_res_list_head( panel_file_resource_s* resource )
{
    if( !resource )
    {
        pi_log_e( "invalid param\n" );
        return;
    }

    pthread_mutex_lock( &gs_res_list.mutex );

    resource->id = gs_res_list.last_res_id;
    ++gs_res_list.last_res_id;

    resource->next = NULL;
    if( !gs_res_list.head )
    {
        gs_res_list.head = resource;
        gs_res_list.tail = resource;
    }
    else
    {
        resource->next = gs_res_list.head;
        gs_res_list.head = resource;
    }

    pthread_mutex_unlock( &gs_res_list.mutex );
}

static void add_to_res_list_tail( panel_file_resource_s* resource )
{
    if( !resource )
    {
        pi_log_e( "invalid param\n" );
        return;
    }

    pthread_mutex_lock( &gs_res_list.mutex );

    resource->id = gs_res_list.last_res_id;
    ++gs_res_list.last_res_id;

    resource->next = NULL;
    if( !gs_res_list.head )
    {
        gs_res_list.head = resource;
        gs_res_list.tail = resource;
    }
    else
    {
        gs_res_list.tail->next = resource;
        gs_res_list.tail = resource;
    }

    pthread_mutex_unlock( &gs_res_list.mutex );
}

static void delete_to_res_list( uint32_t id )
{
    panel_file_resource_s* cur_resource = NULL;
    panel_file_resource_s* pre_resource = NULL;

    pthread_mutex_lock( &gs_res_list.mutex );

    cur_resource = gs_res_list.head;
    while( cur_resource && (cur_resource->id != id) )
    {
        pre_resource = cur_resource;
        cur_resource = cur_resource->next;
    }

    if( cur_resource )
    {
        if( pre_resource )
        {
            pre_resource->next = cur_resource->next;
            if( cur_resource == gs_res_list.tail )
            {
                gs_res_list.tail = pre_resource->next;
            }
        }
        else
        {
            gs_res_list.head = cur_resource->next;
            if( !gs_res_list.head )
            {
                gs_res_list.tail = gs_res_list.head;
            }
        }
    }
    else
    {
        pi_log_e( "invalid resource id = %d\n", id );
    }

    pthread_mutex_unlock( &gs_res_list.mutex );
}

static panel_file_resource_s* get_resource_by_res_list( uint32_t id )
{
    panel_file_resource_s* cur_resource = NULL;

    pthread_mutex_lock( &gs_res_list.mutex );

    cur_resource = gs_res_list.head;
    while( cur_resource && (cur_resource->id != id) )
    {
        cur_resource = cur_resource->next;
    }

    if( cur_resource )
    {
        atomic_fetch_add( &cur_resource->ref_cnt, 1 );
    }

    pthread_mutex_unlock( &gs_res_list.mutex );

    return cur_resource;
}

static void init_panel_file_resource( panel_file_resource_s* resource )
{
    if( resource )
    {
        pthread_mutex_init( &resource->mutex, NULL );
        pthread_cond_init( &resource->cond, NULL );
        resource->result = NULL;
        resource->result_size = 0;
        resource->is_completed = 0;
        resource->id = 0;
        atomic_init( &resource->ref_cnt, 1 );
        resource->next = NULL;
    }
}

static void deinit_panel_file_resource( panel_file_resource_s* resource )
{
    if( resource )
    {
        pthread_mutex_destroy( &resource->mutex );
        pthread_cond_destroy( &resource->cond );
    }
}

static void try_free_panel_file_resource( panel_file_resource_s* resource )
{
    if( resource )
    {
        if( 1 == atomic_fetch_sub( &resource->ref_cnt, 1 ) )
        {
            pthread_mutex_lock( &resource->mutex );
            if( resource->result )
            {
                pi_free( resource->result );
                resource->result = NULL;
            }
            pthread_mutex_unlock( &resource->mutex );

            deinit_panel_file_resource( resource );
            pi_free( resource );
        }
    }
}

static uint32_t wait_panel_result( panel_file_resource_s* resource )
{
    uint32_t wait_ret = 0;
    struct timespec wait_time;

    if( !resource )
    {
        return wait_ret;
    }

    clock_gettime( CLOCK_REALTIME, &wait_time );
    wait_time.tv_sec += WAKEUP_TIMEOUT;

    while( !resource->is_completed && (wait_ret != ETIMEDOUT) )
    {
        wait_ret = pthread_cond_timedwait( &resource->cond, &resource->mutex, &wait_time );
    }

    if( ETIMEDOUT == wait_ret )
    {
        pi_log_e( "res [%d] wait timeout\n", resource->id );
    }

    return  wait_ret;
}

static int32_t panel_file_access_request( uint32_t res_id, const char* pathname, int32_t mode )
{
    int32_t ret = 0;
    uint32_t send_size = sizeof( panel_file_access_request_s );
    panel_file_access_request_s* send_data = ( panel_file_access_request_s* )pi_malloc( send_size );
    if( send_data )
    {
        pi_memset( send_data, 0, send_size );

        send_data->res_id = res_id;
        send_data->mode = mode;
        pi_memcpy( send_data->pathname, pathname, pi_strlen( pathname ) );

        panel_send_data_u8( RESOURCE, RESOURCE_CMD_FILE_ACCESS, REQUEST, send_data, send_size );

        pi_free( send_data );
        send_data = NULL;
    }
    else
    {
        pi_log_e( "send data malloc failed, malloc size = %d\n", send_size );
        ret = -1;
    }

    return ret;
}

static int32_t panel_file_open_request( uint32_t res_id, const char* pathname, int32_t flags, mode_t mode )
{
    int32_t ret = 0;
    uint32_t send_size = sizeof( panel_file_open_request_s );
    panel_file_open_request_s* send_data = ( panel_file_open_request_s* )pi_malloc( send_size );
    if( send_data )
    {
        pi_memset( send_data, 0, send_size );

        send_data->res_id = res_id;
        send_data->flags = flags;
        send_data->mode = mode;
        pi_memcpy( send_data->pathname, pathname, pi_strlen( pathname ) );

        panel_send_data_u8( RESOURCE, RESOURCE_CMD_FILE_OPEN, REQUEST, send_data, send_size );

        pi_free( send_data );
        send_data = NULL;
    }
    else
    {
        pi_log_e( "send data malloc failed, malloc size = %d\n", send_size );
        ret = -1;
    }

    return ret;
}

static int32_t panel_file_read_request( uint32_t res_id, int32_t fd, size_t count )
{
    int32_t ret = 0;
    panel_file_read_request_s send_data =
    {
        .res_id = res_id,
        .fd = fd,
        .count = MIN( count, FILE_MAX_COUNT ),
    };

    panel_send_data_u8( RESOURCE, RESOURCE_CMD_FILE_READ, REQUEST, &send_data, sizeof(send_data) );

    return ret;
}

static int32_t panel_file_write_request( uint32_t res_id, int32_t fd, const void* buf, size_t count )
{
    int32_t ret = 0;
    uint32_t send_size = sizeof( panel_file_write_request_s );
    panel_file_write_request_s* send_data = ( panel_file_write_request_s* )pi_malloc( send_size );
    if( send_data )
    {
        send_data->res_id = res_id;
        send_data->fd = fd;
        send_data->count = count;
        pi_memcpy( send_data->buf, buf, count );

        panel_send_data_u8( RESOURCE, RESOURCE_CMD_FILE_WRITE, REQUEST, send_data, send_size );

        pi_free( send_data );
        send_data = NULL;
    }
    else
    {
        pi_log_e( "send data malloc failed, malloc size = %d\n", send_size );
        ret = -1;
    }

    return ret;
}

static int32_t panel_file_seek_request( uint32_t res_id, int32_t fd, off_t offset, int32_t whence )
{
    int32_t ret = 0;
    panel_file_seek_request_s send_data =
    {
        .res_id = res_id,
        .fd = fd,
        .offset = offset,
        .whence = whence,
    };

    panel_send_data_u8( RESOURCE, RESOURCE_CMD_FILE_SEEK, REQUEST, &send_data, sizeof(send_data) );

    return ret;
}

static int32_t panel_file_close_request( uint32_t res_id, int32_t fd )
{
    int32_t ret = 0;
    panel_file_close_request_s send_data =
    {
        .res_id = res_id,
        .fd = fd,
    };

    panel_send_data_u8( RESOURCE, RESOURCE_CMD_FILE_CLOSE, REQUEST, &send_data, sizeof(send_data) );

    return ret;
}

int32_t panel_file_access( const char* pathname, int32_t mode, int32_t* err_num )
{
    int32_t ret = -1;
    panel_file_resource_s* resource = NULL;
    panel_file_common_result_s* result = NULL;

    if( !pathname )
    {
        pi_log_e( "pathname failed\n" );
        return ret;
    }

    resource = ( panel_file_resource_s* )pi_malloc( sizeof(panel_file_resource_s) );
    if( !resource )
    {
        pi_log_e( "request resource alloc failed, size = %d\n", sizeof(panel_file_resource_s) );
        return ret;
    }

    init_panel_file_resource( resource );
    add_to_res_list_tail( resource );

    if( !panel_file_access_request(resource->id, pathname, mode) )
    {
        pthread_mutex_lock( &resource->mutex );

        wait_panel_result( resource );
        if( resource->result )
        {
            if( resource->result_size == sizeof(panel_file_common_result_s) )
            {
                result = ( panel_file_common_result_s* )resource->result;
                ret = result->ret;
                if( err_num )
                {
                    *err_num = result->err_num;
                }
            }
            else
            {
                pi_log_e( "result size mismatch[%d|%d]\n", resource->result_size, sizeof(panel_file_common_result_s) );
            }
        }

        pthread_mutex_unlock( &resource->mutex );
    }

    delete_to_res_list( resource->id );
    try_free_panel_file_resource( resource );

    return ret;
}

int32_t panel_file_open( const char* pathname, int32_t flags, mode_t mode, int32_t* err_num )
{
    int32_t ret = -1;
    panel_file_resource_s* resource = NULL;
    panel_file_common_result_s* result = NULL;

    if( !pathname )
    {
        pi_log_e( "pathname failed\n" );
        return ret;
    }

    resource = ( panel_file_resource_s* )pi_malloc( sizeof(panel_file_resource_s) );
    if( !resource )
    {
        pi_log_e( "request resource alloc failed, size = %d\n", sizeof(panel_file_resource_s) );
        return ret;
    }

    init_panel_file_resource( resource );
    add_to_res_list_tail( resource );

    if( !panel_file_open_request(resource->id, pathname, flags, mode) )
    {
        pthread_mutex_lock( &resource->mutex );

        wait_panel_result( resource );
        if( resource->result )
        {
            if( resource->result_size == sizeof(panel_file_common_result_s) )
            {
                result = ( panel_file_common_result_s* )resource->result;
                ret = result->ret;
                if( err_num )
                {
                    *err_num = result->err_num;
                }
            }
            else
            {
                pi_log_e( "result size mismatch[%d|%d]\n", resource->result_size, sizeof(panel_file_common_result_s) );
            }
        }

        pthread_mutex_unlock( &resource->mutex );
    }

    delete_to_res_list( resource->id );
    try_free_panel_file_resource( resource );

    return ret;
}

ssize_t panel_file_read( int32_t fd, void *buf, size_t count, int32_t* err_num )
{
    ssize_t ret = -1;
    panel_file_resource_s* resource = NULL;
    panel_file_read_result_s* result = NULL;

    if( !buf || !count )
    {
        pi_log_e( "invalid param\n" );
        return ret;
    }

    resource = ( panel_file_resource_s* )pi_malloc( sizeof(panel_file_resource_s) );
    if( !resource )
    {
        pi_log_e( "request resource alloc failed, size = %d\n", sizeof(panel_file_resource_s) );
        return ret;
    }

    init_panel_file_resource( resource );
    add_to_res_list_tail( resource );

    if( !panel_file_read_request(resource->id, fd, count) )
    {
        pthread_mutex_lock( &resource->mutex );

        wait_panel_result( resource );
        if( resource->result )
        {
            if( resource->result_size == sizeof(panel_file_read_result_s) )
            {
                result = ( panel_file_read_result_s* )resource->result;
                ret = result->ret;
                pi_memcpy( buf, result->buf, MIN(ret, count) );
                if( err_num )
                {
                    *err_num = result->err_num;
                }
            }
            else
            {
                pi_log_e( "result size mismatch[%d|%d]\n", resource->result_size, sizeof(panel_file_read_result_s) );
            }
        }

        pthread_mutex_unlock( &resource->mutex );
    }

    delete_to_res_list( resource->id );
    try_free_panel_file_resource( resource );

    return ret;
}

ssize_t panel_file_write( int32_t fd, const void* buf, size_t count, int32_t* err_num )
{
    ssize_t ret = -1;
    panel_file_resource_s* resource = NULL;
    panel_file_write_result_s* result = NULL;

    if( !buf || !count )
    {
        pi_log_e( "invalid param\n" );
        return ret;
    }
    else if( count > FILE_MAX_COUNT )
    {
        pi_log_e( "count out of range[%d|%d]\n", count, FILE_MAX_COUNT );
        return ret;
    }

    resource = ( panel_file_resource_s* )pi_malloc( sizeof(panel_file_resource_s) );
    if( !resource )
    {
        pi_log_e( "request resource alloc failed, size = %d\n", sizeof(panel_file_resource_s) );
        return ret;
    }

    init_panel_file_resource( resource );
    add_to_res_list_tail( resource );

    if( !panel_file_write_request(resource->id, fd, buf, count) )
    {
        pthread_mutex_lock( &resource->mutex );

        wait_panel_result( resource );
        if( resource->result )
        {
            if( resource->result_size == sizeof(panel_file_write_result_s) )
            {
                result = ( panel_file_write_result_s* )resource->result;
                ret = result->ret;
                if( err_num )
                {
                    *err_num = result->err_num;
                }
            }
            else
            {
                pi_log_e( "result size mismatch[%d|%d]\n", resource->result_size, sizeof(panel_file_write_result_s) );
            }
        }

        pthread_mutex_unlock( &resource->mutex );
    }

    delete_to_res_list( resource->id );
    try_free_panel_file_resource( resource );

    return ret;
}

off_t panel_file_seek( int32_t fd, off_t offset, int32_t whence, int32_t* err_num )
{
    off_t ret = -1;
    panel_file_resource_s* resource = NULL;
    panel_file_seek_result_s* result = NULL;

    resource = ( panel_file_resource_s* )pi_malloc( sizeof(panel_file_resource_s) );
    if( !resource )
    {
        pi_log_e( "request resource alloc failed, size = %d\n", sizeof(panel_file_resource_s) );
        return ret;
    }

    init_panel_file_resource( resource );
    add_to_res_list_tail( resource );

    if( !panel_file_seek_request(resource->id, fd, offset, whence) )
    {
        pthread_mutex_lock( &resource->mutex );

        wait_panel_result( resource );
        if( resource->result )
        {
            if( resource->result_size == sizeof(panel_file_seek_result_s) )
            {
                result = ( panel_file_seek_result_s* )resource->result;
                ret = result->ret;
                if( err_num )
                {
                    *err_num = result->err_num;
                }
            }
            else
            {
                pi_log_e( "result size mismatch[%d|%d]\n", resource->result_size, sizeof(panel_file_seek_result_s) );
            }
        }

        pthread_mutex_unlock( &resource->mutex );
    }

    delete_to_res_list( resource->id );
    try_free_panel_file_resource( resource );

    return ret;
}

int32_t panel_file_close( int32_t fd, int32_t* err_num )
{
    int32_t ret = -1;
    panel_file_resource_s* resource = NULL;
    panel_file_common_result_s* result = NULL;

    resource = ( panel_file_resource_s* )pi_malloc( sizeof(panel_file_resource_s) );
    if( !resource )
    {
        pi_log_e( "request resource alloc failed, size = %d\n", sizeof(panel_file_resource_s) );
        return ret;
    }

    init_panel_file_resource( resource );
    add_to_res_list_tail( resource );

    if( !panel_file_close_request(resource->id, fd) )
    {
        pthread_mutex_lock( &resource->mutex );

        wait_panel_result( resource );
        if( resource->result )
        {
            if( resource->result_size == sizeof(panel_file_common_result_s) )
            {
                result = ( panel_file_common_result_s* )resource->result;
                ret = result->ret;
                if( err_num )
                {
                    *err_num = result->err_num;
                }
            }
            else
            {
                pi_log_e( "result size mismatch[%d|%d]\n", resource->result_size, sizeof(panel_file_common_result_s) );
            }
        }

        pthread_mutex_unlock( &resource->mutex );
    }

    delete_to_res_list( resource->id );
    try_free_panel_file_resource( resource );

    return ret;
}

void panel_file_recv_result_proc( void* data, int32_t data_len )
{
    void* result = NULL;
    uint32_t result_size = 0;
    panel_file_resource_s* resource = NULL;
    uint32_t res_id = 0;
    if( data_len <= sizeof(res_id) )
    {
        pi_log_e( "recv data invalid size = [%d]\n", data_len );
        return;
    }

    res_id = *( uint32_t* )data;
    result = ( void* )( (uint8_t*)data + sizeof(res_id) );
    result_size = data_len - sizeof( res_id );

    resource = get_resource_by_res_list( res_id );
    if( resource )
    {
        pthread_mutex_lock( &resource->mutex );

        if( resource->result )
        {
            pi_free( resource->result );
            resource->result = NULL;
        }

        resource->result_size = result_size;
        resource->result = pi_malloc( resource->result_size );
        if( resource->result )
        {
            pi_memcpy( resource->result, result, resource->result_size );
        }
        else
        {
            pi_log_e( "resource[%d] result alloc failed, size = %d\n", resource->id, resource->result_size );
            resource->result_size = 0;
        }

        resource->is_completed = 1;

        pthread_cond_signal( &resource->cond );
        pthread_mutex_unlock( &resource->mutex );

        try_free_panel_file_resource( resource );
    }
}

#if 1
static int32_t panel_file_access_debug( int32_t argc, char* argv[] )
{
    if( argc != 2 )
    {
        pi_log_d( "input error, eg: echo panel_file access pathname mode > /tmp/cmd" );
        return -1;
    }

    int32_t err_num = 0;
    int32_t ret = panel_file_access( argv[0], atoi(argv[1]), &err_num );

    pi_log_d( "ret = %d, err_num = %d\n", ret, err_num );

    return 0;
}

static int32_t panel_file_open_debug( int32_t argc, char* argv[] )
{
    if( argc != 3 )
    {
        pi_log_d( "input error, eg: echo panel_file open pathname flags mode > /tmp/cmd" );
        return -1;
    }

    int32_t err_num = 0;
    int32_t ret = panel_file_open( argv[0], atoi(argv[1]), atoi(argv[2]), &err_num );

    pi_log_d( "ret = %d, err_num = %d\n", ret, err_num );

    return 0;
}

static int32_t panel_file_read_debug( int32_t argc, char* argv[] )
{
    if( argc != 2 )
    {
        pi_log_d( "input error, eg: echo panel_file read fd count > /tmp/cmd" );
        return -1;
    }

    int32_t err_num = 0;
    char buf[ 257 ] = { 0 };
    ssize_t ret = panel_file_read( atoi(argv[0]), buf, MIN(atoi(argv[1]), 256), &err_num );

    pi_log_d( "ret = %d, err_num = %d\n", ret, err_num );
    pi_log_d( "data = [%s]\n", buf );

    return 0;
}

static int32_t panel_file_write_debug( int32_t argc, char* argv[] )
{
    if( argc != 3 )
    {
        pi_log_d( "input error, eg: echo panel_file write fd count > /tmp/cmd" );
        return -1;
    }

    int32_t err_num = 0;
    ssize_t ret = panel_file_write( atoi(argv[0]), argv[1], atoi(argv[2]), &err_num );

    pi_log_d( "ret = %d, err_num = %d\n", ret, err_num );

    return 0;
}

static int32_t panel_file_seek_debug( int32_t argc, char* argv[] )
{
    if( argc != 3 )
    {
        pi_log_d( "input error, eg: echo panel_file seek fd offset whence > /tmp/cmd" );
        return -1;
    }

    int32_t err_num = 0;
    off_t ret = panel_file_seek( atoi(argv[0]), atoi(argv[1]), atoi(argv[2]), &err_num );

    pi_log_d( "ret = %d, err_num = %d\n", ret, err_num );

    return 0;
}

static int32_t panel_file_close_debug( int32_t argc, char* argv[] )
{
    if( argc != 1 )
    {
        pi_log_d( "input error, eg: echo panel_file close fd > /tmp/cmd" );
        return -1;
    }

    int32_t err_num = 0;
    int32_t ret = panel_file_close( atoi(argv[0]), &err_num );

    pi_log_d( "ret = %d, err_num = %d\n", ret, err_num );

    return 0;
}

void panel_file_debug_cmd_register( void )
{
    cmd_register( "panel_file", "access", panel_file_access_debug, NULL );
    cmd_register( "panel_file", "open", panel_file_open_debug, NULL );
    cmd_register( "panel_file", "read", panel_file_read_debug, NULL );
    cmd_register( "panel_file", "write", panel_file_write_debug, NULL );
    cmd_register( "panel_file", "seek", panel_file_seek_debug, NULL );
    cmd_register( "panel_file", "close", panel_file_close_debug, NULL );
}
#endif
 /**
  * @}
  */



