# 打印支持
config PRINT
    bool "print"
    default y
    help
        "print support"

# 复印支持
config COPY
    bool "copy"
    depends on PRINT && SCAN
    help
        "copy support"

# 扫描支持
config SCAN
    bool "scan"
    help
        "scan support"

# 传真支持
config FAX
    bool "fax"
    help
        "fax support"

# 网络支持
config NET
    bool "net"
    default y
    help
        "net support"

# 默认国家码
config COUNTRY_CODE_DEFAULT
    int "country code default"
    default 14
    help
        "country code default"

# 彩色功能支持
config COLOR
    bool "color"
    help
        "color support"

# Airprint/Moprial支持
config AIRPRINT
    bool "airprint"
    help
        "airprint support PWG/URF/PDF/JPEG"

# PEDK功能支持
config SDK_PEDK
    bool "sdk pedk"
    help
        "sdk pedk support"

# 文印SDK支持
config SDK_EWS
    bool "sdk ews"
    help
        "sdk ews support"

# IPS支持 包括PCL、PS、DO
config IPS
    bool "ips"
    default y
    help
       "ips parser support"
       
# PWG支持
config PWG
    bool "pwg"
    help
        "print parser pwg support"

# URF支持
config URF
    bool "urf"
    help
        "print parser urf support"

# GDI支持
config GDI
    bool "gdi"
    help
        "print parser gdi support"

# PDF支持
config PDF
    bool "pdf"
    help
        "pdf support"

# JPEG支持
config JPEG
    bool "jpeg"
    help
        "jpeg support"

# 已确认保留
config EVENT_LOG
	bool "EVENT_LOG module "
    default y
	help
        "event log module"

# 已确认保留
config HTTC
	bool "HTTC module"
	help
        "httc module"

# 新增
config XC4_UPGRADE
	bool "XC4_UPGRADE module"
	help
        "XC4_UPGRADE module"

# 已确认保留
config IPM
	bool "ipm"
	default y
	help 
		"modules -> ipm"

# 已确认保留
config INTER_BOARD
    bool "Inter-board communication method between PANEL and DC"
    help 
        "Inter-board communication method between PANEL and DC"

# 已确认保留
config INTERPROCESS
    bool "Interprocess communication method between PANEL and DC"
    help 
        "Interprocess communication method between PANEL and DC"

# 已确认保留
config PRINTERINFO
    bool "printerinfo"
    default y
    help
        "printerinfo"

# EP0结构体版本号
config PRINTERINFO_VER
    int "printerinfo version"
	depends on PRINTERINFO
    default 2
    help
        "printerinfo struct version"

# 已确认保留
config AUDIT_RECORD
    bool "audit record"
    default y
    help
        "audit record"

# 已确认保留
config UPGRADE
	bool "upgrade"
    default y
	help 
		"system -> upgrade"

# 已确认保留
config TIME_STAMP
	bool "timestamp"
	default y
	help 
		"timestamp"

# 硬盘支持
config STORAGE_SSD
	bool "storage_ssd"
    default y
	help 
		"system -> storage_ssd"