/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ipv6.h
 * @addtogroup net
 * @{
 * @addtogroup ipv6
 * <AUTHOR>
 * @date 2023-9-16
 * @brief Network IPv6 address manager API header file.
 */
#ifndef __IPV6_H__
#define __IPV6_H__

typedef enum
{
    IPV6_SWITCH_OFF = 0,
    IPV6_SWITCH_ON,
}
IPV6_SWITCH_E;

/**
 * @brief       Switch IPv6 address enabled/disabled for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @param[in]   on      : value in IPV6_SWITCH_E.
 * @return      Setting result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR>
 * @date        2023-9-16
 */
int32_t         net_ipv6_switch         (IFACE_ID_E ifid, IPV6_SWITCH_E on);

/**
 * @brief       Start udhcpc6 progress to generate automatic IPv6 dhcp addresses for this network interface.
 * @param[in]   ifid    : The network interface index.
 * <AUTHOR>
 * @date        2023-9-16
 */
void            net_ipv6_start_dhcp     (IFACE_ID_E ifid);

/**
 * @brief       Stop udhcpc6 progress and clear IPv6 dhcp address for this network interface.
 * @param[in]   ifid    : The network interface index.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            net_ipv6_stop_dhcp      (IFACE_ID_E ifid);

/**
 * @brief       Check whether the address is the stateless address for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @param[in]   addr    : IPv6 address string, eg. "xx::xx:xx:xx:xx".
 * @return      Setting result
 * @retval      !=0     : yes\n
 *              ==0     : no
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv6_is_stls_addr   (IFACE_ID_E ifid, const char* addr);

/**
 * @brief       Get IPv6 DNS address for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @param[out]  dns     : The dns address.
 * @return      Setting result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv6_get_dns        (IFACE_ID_E ifid, char (*dns)[IPV6_ADDR_LEN]);

/**
 * @brief       Clear IPv6 DNS address for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @return      Setting result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv6_clear_dns      (IFACE_ID_E ifid);

#endif /* __IPV6_H__ */
/**
 *@}
 */
