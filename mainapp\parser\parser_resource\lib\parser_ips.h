#ifndef __PARSER_IPS_H__
#define __PARSER_IPS_H__

#include "parser_ips_interface.h"

enum
{
    PARSER_IPS_CMD_GET_SYSTEM_STATUS_FOR_MONO = 0,                  ///< get print color mode,0: default 1:mono
    PARSER_IPS_CMD_SET_DO_ENCRYPT_WARN,                             ///< error,file encrpyt
    PARSER_IPS_CMD_SET_DO_UNSUPPORT_FILE_FORMAT,                    ///< error, file formate is unsupport
    PARSER_IPS_CMD_GET_MACHINE_SPEED,                               ///< get machine speed,high or low
    PARSER_IPS_CMD_GET_MACHINE_COLOR,                               ///< get machine color，color or mono
    PARSER_IPS_CMD_GET_FINISHER_INSRALL,                            ///< finisher install status
    PARSER_IPS_CMD_UDISK_PAPERSIZE_VALID_CHECK,                     ///< check papsersize's validity
    PARSER_IPS_CMD_GET_TRAY_INSTALL,                                ///< tray_in status of install
    PARSER_IPS_CMD_GET_TRAY_INEMPTY_STATUS,                         ///< tray_in status of empty
    PARSER_IPS_CMD_GET_PAPER_SIZE,                                  ///< get parser size
    PARSER_IPS_CMD_GET_PAPER_TYPE,                                  ///< get paper type
    PARSER_IPS_CMD_ADD_BAND_TOPAGE_OUTPUT_QUEUE_PAGE,               ///<
    PARSER_IPS_CMD_FINISHED_ADDING_TOPAGE_OUTPUT_QUEUE_PAGE,        ///<
    PARSER_IPS_CMD_CREATE_PAGE_OUTPUT_QUEUE_SET,                    ///<
    PARSER_IPS_CMD_IO_END_OF_JOB,                                   ///< end of job
    PARSER_IPS_CMD_IO_IPS_IN_USE,                                   ///< if job is start
    PARSER_IPS_CMD_IO_STOP_READ_DATA,                               ///< ips parses over,and then stop read data
    PARSER_IPS_CMD_IO_STOP_FILE_READ_DATA,                          ///< it is relative to cross shift,ips parses over,stop read data
    PARSER_IPS_CMD_PRN_READ_ENABLE_FLAG,                            ///< it is relative to cross shift,if it can read
    PARSER_IPS_CMD_GET_JOB_ID_VALUE,                                ///< get job id ,if jobid changes,ips starts new job
    PARSER_IPS_CMD_IO_POST_JOB_END_MSG,                             ///<
    PARSER_IPS_CMD_IO_READ_DATA,                                    ///< it doesn't use,reversed
    PARSER_IPS_CMD_IO_POST_JOB_PIPELINT_NAME,                       ///<
    PARSER_IPS_CMD_GET_TRAY_IN_AND_EDGE,                            ///< before job start,get tray_in and edge
    PARSER_IPS_CMD_GET_SYSTEM_SUPPORT_MEDIA_SIZE,                   ///< it is the api that suits the progress
    PARSER_IPS_CMD_GET_TIMEOUT_VALUE_INT,                           ///< get the timeout of parse
    PARSER_IPS_CMD_ADD_PAGE_TO_PAGE_OUTPUT_QUEUE_SET,               ///<
    PARSER_IPS_CMD_FINISHED_ADDING_PAGES_TO_PAGE_OUTPUT_QUEUE_SET,  ///<
    PARSER_IPS_CMD_GET_IPS_LIB_PARAM,                               ///< get image param
    PARSER_IPS_CMD_GET_JOB_SETTING_TYPE,                            ///< get job type,common or airprint&modpria and so on
    PARSER_IPS_CMD_SET_TIMEOUT_FLAG,                                ///< if get no data in servral exception condition,it will set timeout flag
    PARSER_IPS_CMD_GET_TIMEOUT_FLAG,                                ///< geti timeout flag
    PARSER_IPS_CMD_GET_MFP_READY,                                   ///< ipsapp start ready
    PARSER_IPS_CMD_GET_MEMOBJ_ID,                                   ///< ipsapp create memobj,ips get the memobj,alloc memory
    PARSER_IPS_CMD_ADD_BAND_TO_PAGE_OUTPUT_QUEUE_PAGE_NEW,          ///<
    PARSER_IPS_CMD_INF_GET_CANCEL_FLAG,                             ///< get cancel flg
    PARSER_IPS_CMD_IO_IS_PJLPS_DATA_STREAM,                         ///<
    PARSER_IPS_CMD_IO_GET_USB_DUMMY,                                ///<
    PARSER_IPS_CMD_IO_INPUT_TIMEOUT,                                ///<
    PARSER_IPS_CMD_IO_GET_QIO_ERROR,                                ///<
    PARSER_IPS_CMD_IO_ALWAYS_IN_SNIFFER,                            ///<
    PARSER_IPS_CMD_IO_REWIND_DATA,                                  ///< rewind data
    PARSER_IPS_CMD_IO_SET_PDF_FONT_MISSING_WARN,                    ///< error, font missing
    PARSER_IPS_CMD_IO_SET_PDF_ENCRYPT_WARN,                         ///< error, pdf ecrypt
    PARSER_IPS_CMD_IO_SET_PDF_FONT_INVALID_WARN,                    ///< error,pdf font invalid
    PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_JOB,                          ///<
    PARSER_IPS_CMD_IO_IS_AIRPRINT_PDF_READ_OVER,                    ///<
    PARSER_IPS_CMD_GET_UDISK_PARAM,                                 ///< get udisk or airprint&modpria param
    PARSER_IPS_CMD_GET_CUSTOM_SIZE_TABLE,                           ///< custom size table
    PARSER_IPS_CMD_IO_FILE_READ_DATA,                               ///< it is relative to cross shift,read file data
    PARSER_IPS_CMD_IO_GET_FIRSET_PASS_PRN_PAGES,                    ///<
    PARSER_IPS_CMD_IO_GET_SAMPLE_PARAM,                             ///< sample print param
    PARSER_IPS_CMD_IO_GET_JOB_SUSPEND_STATUS,                       ///< get suspend ,0: normal 1: suspend
    PARSER_IPS_CMD_IO_SET_FILE_ENCRYPT_WARN,                        ///< error, file encrypt
    PARSER_IPS_CMD_IO_SET_FILE_LARGE_WARN,                          ///< error ,fiile large
    PARSER_IPS_CMD_IO_SET_FILE_MISSING_WARN,                        ///< error,file font missing
    PARSER_IPS_CMD_IO_SET_FILE_UNSUPPORT_WARN,                      ///< error,file format unsupport
    PARSER_IPS_CMD_SET_PARSER_AGAIN_FLAG,                           ///< it is relative to cross shift
    PARSER_IPS_CMD_GET_PARSER_AGAIN_FLAG,                           ///< it is relative to cross shift,
    PARSER_IPS_CMD_ANSWER_SUSPEND_ACK_BY_IPS,                       ///< if ips has suspended,it will call this api
    PARSER_IPS_CMD_SET_IMAGEMEM_RELEASE,                            ///< ips alloc memory over,and then call this api
};

#endif /* __PARSER_IPS_H__ */
