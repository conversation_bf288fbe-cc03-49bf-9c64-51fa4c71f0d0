/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_tcp.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief QIO object wrapper for TCP/IP sockets
 */
#include "nettypes.h"
#include "netsock.h"

typedef struct priv_info
{
    PI_SOCKET_T     sockfd;
    int32_t         flag;
}
PRIV_INFO_S;

/**
 * @brief       The callback function of QIO_CLOSE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for TCP connection.
 * @return      Close result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR>
 * @date        2023-9-16
 */
static int32_t qio_tcp_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);

    RETURN_VAL_IF(pqio == NULL, NET_DEBUG, QIOEOF);

    if ( priv )
    {
        NET_DEBUG("close TCP(%d %d) QIO<%p>", priv->sockfd, priv->flag, pqio);
        if ( priv->flag == NETQIO_SOCK_CLOSE && priv->sockfd != INVALID_SOCKET )
        {
            pi_closesock(priv->sockfd);
        }
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

/**
 * @brief       The callback function of QIO_READABLE(pqio) or QIO_WRITEABLE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for TCP connection.
 * @return      Poll result
 * @retval      > 0     : this QIO_S object can be read or written\n
 *              ==0     : poll timeout\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_tcp_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    struct timeval  tv = { .tv_sec = (__time_t)tos, .tv_usec = (__suseconds_t)tous };
    fd_set          fds;
    fd_set*         rfds = ( what & QIO_POLL_READ  ) ? &fds : NULL;
    fd_set*         wfds = ( what & QIO_POLL_WRITE ) ? &fds : NULL;
    fd_set*         efds = ( what & QIO_POLL_EVENT ) ? &fds : NULL;

    RETURN_VAL_IF(priv == NULL || priv->sockfd == INVALID_SOCKET, NET_WARN, QIOEOF);

    FD_ZERO(&fds);
    FD_SET(priv->sockfd, &fds);

    return select(priv->sockfd + 1, rfds, wfds, efds, (tos < 0 || (tos == 0 && tous < 0)) ? NULL : &tv);
}

/**
 * @brief       The callback function of QIO_READ(pqio), recv data from sockfd.
 * @param[in]   pqio    : The QIO_S object pointer for TCP connection.
 * @return      Read result
 * @retval      >=0     : recv the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_tcp_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    size_t  chunk_size = 0;
    int32_t rtotal = 0;
    int32_t rlen = 0;

    RETURN_VAL_IF(priv == NULL || priv->sockfd == INVALID_SOCKET, NET_WARN, QIOEOF);

    while ( nbuf > (size_t)rtotal )
    {
        chunk_size = (size_t)(((nbuf - rtotal) < TCP_CHUNK_SIZE) ? (nbuf - rtotal) : TCP_CHUNK_SIZE);
        rlen = (int32_t)recv(priv->sockfd, (char *)buffer + rtotal, chunk_size, 0);
        if ( rlen > 0 )
        {
            rtotal += (int32_t)rlen;
        }
        else if ( rlen == 0 || rtotal > 0 || errno == 0 || errno == EAGAIN || errno == EWOULDBLOCK )
        {
            /* rlen <  0 && rtotal >  0 && errno == 11 : 缓冲区数据已读完           */
            /* rlen == 0 && rtotal == 0 && errno == 0  : 对端关闭                   */
            /* rlen == 0 && rtotal >  0 && errno == 0  : 缓冲区数据已读完，对端关闭 */
            break;
        }
        else
        {
            NET_WARN("QIO_S<%p> rlen(%d) rtotal(%d) errno(%d<%s>)", pqio, rlen, rtotal, errno, strerror(errno));
            rtotal = (int32_t)rlen;
            break;
        }
    }

    return rtotal;
}

/**
 * @brief       The callback function of QIO_WRITE(pqio), send data to TCP stream.
 * @param[in]   pqio    : The QIO_S object pointer for TCP connection.
 * @return      Write result
 * @retval      >=0     : send the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_tcp_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, PRIV_INFO_S, priv);
    size_t  chunk_size = 0;
    int32_t retries = 0;
    int32_t wtotal = 0;
    int32_t wlen = 0;

    RETURN_VAL_IF(priv == NULL || priv->sockfd == INVALID_SOCKET, NET_WARN, QIOEOF);

    while ( nbuf > (size_t)wtotal && retries < 100 )
    {
        chunk_size = ((nbuf - wtotal) < TCP_CHUNK_SIZE) ? (nbuf - wtotal) : TCP_CHUNK_SIZE;
        wlen = (int32_t)send(priv->sockfd, (char *)buffer + wtotal, chunk_size, MSG_NOSIGNAL);
        if ( wlen > 0 )
        {
            wtotal += (size_t)wlen;
        }
        else if ( wlen == 0 || errno == EAGAIN || errno == EWOULDBLOCK )
        {
            if ( (++retries) % 10 == 0)
            {
                NET_WARN("QIO_S<%p> wlen(%d) wtotal(%d) retries(%d) errno(%d<%s>)", pqio, wlen, wtotal, retries, errno, strerror(errno));
            }
            pi_msleep(100);
        }
        else
        {
            NET_WARN("QIO_S<%p> wlen(%d) wtotal(%d) errno(%d<%s>)", pqio, wlen, wtotal, errno, strerror(errno));
            break;
        }
    }

    if ( wtotal == 0 && nbuf > 0 )
    {
        wtotal = -1;
    }
    return wtotal;
}

/**
 * @brief       The callback function of QIO_SEEK(pqio), unsupported.
 * @param[in]   pqio    : The QIO_S object pointer for TCP connection.
 * @return      Seek result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t qio_tcp_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

QIO_S* qio_tcp_create_custom(int32_t n, PI_SOCKET_T sockfd, ...)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;
    va_list         arg;

    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, NET_WARN, NULL);

    if ( n == 2 )
    {
        va_start(arg, sockfd);
        priv->flag = va_arg(arg, int32_t);
        va_end(arg);
    }
    else if ( n == 1 )
    {
        priv->flag = NETQIO_SOCK_CLOSE;
    }
    else
    {
        NET_WARN("invalid parameter count(%d)", n);
        pi_free(priv);
        return NULL;
    }
    priv->sockfd = sockfd;

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        NET_WARN("alloc TCP(%d %d) QIO failed: %d<%s>", priv->sockfd, priv->flag, errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }

    NET_DEBUG("create TCP(%d %d) QIO<%p>", priv->sockfd, priv->flag, pqio);
    pqio->close = qio_tcp_close;
    pqio->poll  = qio_tcp_poll;
    pqio->read  = qio_tcp_read;
    pqio->write = qio_tcp_write;
    pqio->seek  = qio_tcp_seek;
    pqio->priv  = (void *)priv;

    return pqio;
}
/**
 *@}
 */
