/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file resource_config.c
 * @addtogroup system_manager
 * @{
 * @brief resource config 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#include <string.h>
#include "resource_config.h"
#include "pol/pol_io.h"
#include "pol/pol_mem.h"
#include "cini.h"
#include <stdio.h>
#include <stdlib.h>

static const char* strip_chr(const char *str , unsigned int str_len , 
        const char *sub , unsigned int sub_len)
{
    unsigned int i = 0 , j = 0;

    for (j = 0; j < str_len; ++j )
    {
        for (i = 0; i < sub_len; ++i)
        {
            if (str[j] == sub[i])
            {
                break;
            }
        }
        if (i == sub_len)
            break;

    }
    return str+j;
}


static void get_subsection(const char *start , 
        const char *end , 
        struct channels *ch)
{
    const char *ptr = start + 1;

    do
    {
        ptr = strip_chr(ptr , end - ptr + 1 , " \t" , 2);
        if (!ptr || ptr >= end)
            break;
        int32_t id = atoi(ptr);
        channels_tail_insert(ch , id , (void*)id , 0);
        if (++ptr >= end)
            break;
        ptr = strchr(ptr , ',');
        if (!ptr || ptr >= end)
            break;
        ++ptr;
    }while(ptr && ptr < end);
}


static int get_list(const char *str , 
        const int len , 
        const int max_limit , 
        struct channels *ch)
{
    const char *start = str , *end = str + len - 1;
    const char *left = start , *right = NULL;
    int32_t index = 0;

    do
    {
        left = strchr(left , '{');
        if (!left || left >= end)
            break;
        right = strchr(left + 1 , '}');
        if (!right || right > end)
        {
            break;
        }
        get_subsection(left , right , ch + index);
        left = right + 1;
    }while(left && left < end && ++index < max_limit);
    
    return index >= max_limit ? --index : index; 
}

static void job_item_free(void *data)
{
    struct job_item *item = (struct job_item*)data;

    if (item)
    {
        if (item->name)
        {
            free(item->name);
        }
        for (int32_t i = 0; 
                i < sizeof(item->depend_list) / sizeof(item->depend_list[0]);
                ++i)
        {
            channels_destroy(&item->depend_list[i]);
        }
        channels_destroy(&item->msgrouter_source);
        pi_free(item);
    }
}

static int32_t job_conversion(struct section *sct , struct channels *job)
{
    struct job_item *item = (struct job_item*)pi_malloc(sizeof(struct job_item));
    if (!item)
    {
        return -1;
    }
    pi_memset(item , 0 , sizeof(struct job_item));

    char *value = NULL;

    if (sct->name)
    {
        item->name = (uint8_t*)strdup(sct->name);
    }

    value = cini_find_parameter(sct , "job_type");
    if (value)
    {
        item->job_type = atoi(value);
    }

    value = cini_find_parameter(sct , "job_class");
    if (value)
    {
        item->job_class = atoi(value);
    }

    value = cini_find_parameter(sct , "image_memory_required");
    if (value)
    {
        item->image_memory_required = atoi(value);
    }

    value = cini_find_parameter(sct , "image_memory_lowest");
    if (value)
    {
        item->image_memory_lowest = atoi(value);
    }

    value = cini_find_parameter(sct , "image_memory_fragment");
    if (value)
    {
        item->image_memory_fragment = atoi(value);
    }

    value = cini_find_parameter(sct , "video_memory_required");
    if (value)
    {
        item->video_memory_required = atoi(value);
    }

    value = cini_find_parameter(sct , "video_memory_lowest");
    if (value)
    {
        item->video_memory_lowest = atoi(value);
    }

    value = cini_find_parameter(sct , "video_memory_fragment");
    if (value)
    {
        item->video_memory_fragment = atoi(value);
    }

    value = cini_find_parameter(sct , "resource_io_id");
    if (value)
    {
        item->resource_io_id = atoi(value);
    }

    value = cini_find_parameter(sct , "start_module_id");
    if (value)
    {
        item->start_module_id = atoi(value);
    }

    value = cini_find_parameter(sct , "monop_type");
    if (value)
    {
        item->mono_type = atoi(value);
    }

    value = cini_find_parameter(sct , "wait_type");
    if (value)
    {
        item->wait_type = atoi(value);
    }

    value = cini_find_parameter(sct , "default_priority");
    if (value)
    {
        item->default_priority = atoi(value);
    }

    value = cini_find_parameter(sct , "multiple_job_depend_ssd");
    if (value)
    {
        item->multiple_job_depend_ssd = atoi(value);
    }

    value = cini_find_parameter(sct , "depend_resource_list");
    channels_init(item->depend_list , NULL , NULL); 
    channels_init(item->depend_list + 1 , NULL , NULL);

    if (value)
    {
        item->list_num = get_list(value , strlen(value) , 
                sizeof(item->depend_list)/sizeof(item->depend_list[0]) , 
                item->depend_list);
    }

    value = cini_find_parameter(sct , "msgrouter_source");
    channels_init(&item->msgrouter_source , NULL , NULL);
    if (value)
    {
        get_list(value , strlen(value) , 1 , &item->msgrouter_source);
    }

    if (channels_tail_insert(job , item->job_type , item , 0) == -1)
    {
        job_item_free(item);
    }

    return 0;
}

static void resource_item_free(void *data)
{
    struct resource_item *item = (struct resource_item*)data;
    if (item)
    {
        if (item->name)
        {
            free(item->name);
        }
        free(item);
    }
}


static int32_t resource_conversion(struct section *sct , struct channels *resource)
{
    struct resource_item *item = (struct resource_item*)pi_malloc(sizeof(struct resource_item));
    if (!item)
    {
        return -1;
    }

    pi_memset(item , 0 , sizeof(struct resource_item));

    if (sct->name)
    {
        item->name = (uint8_t*)strdup(sct->name);
    }

    char *value = cini_find_parameter(sct , "resource_id");
    if (value)
    {
        item->id = atoi(value);
    }

    value = cini_find_parameter(sct , "resource_number");
    if (value)
    {
        item->number = atoi(value);
    }

    value = cini_find_parameter(sct , "resource_reuse");
    if (value)
    {
        item->reuse = atoi(value);
    }

    if (channels_tail_insert(resource , item->id , item , 0) == -1)
    {
        resource_item_free(item);
    }

    return 0;
}

static int32_t init2resource(struct cini *cf , struct resource_config **config)
{
    struct resource_config *ret = (struct resource_config*)pi_malloc(sizeof(struct resource_config));
    if (!ret)
    {
        return -1;
    }
    pi_memset(ret , 0 , sizeof(struct resource_config));

    channels_init(&ret->config[JOB] , NULL , job_item_free);
    channels_init(&ret->config[RESOURCE] , NULL , resource_item_free);

    struct section *ptr = cf->head;

    while(ptr)
    {
        if (strlen(ptr->name) >= 4 && strncmp(ptr->name , "job_" , 4) == 0)
        {
            job_conversion(ptr , ret->config + JOB);
        }
        else if (strlen(ptr->name) >= 9 && strncmp(ptr->name , "resource_" , 9) == 0)
        {
            resource_conversion(ptr , ret->config + RESOURCE);
        }

        ptr = ptr->next;
    }

    *config = ret;

    return 0;
}

struct resource_config* resource_init(const char *file)
{
    if (!file)
        return NULL;

    struct resource_config *conf = NULL;
    struct cini cf;

    cini_init(&cf);

    if (parser_cini(file ,  &cf) == 0)
    {
        if (init2resource(&cf , &conf) == -1)
        {
            fprintf(stderr , "init2resource failed\n");
            resource_destroy(conf);
            return NULL;
        }
    }
    else
    {
        fprintf(stderr , "parse %s failed\n" , file);
        return NULL;
    }

    cini_destroy(&cf);
    conf->file = file;

    return conf;
}

void resource_destroy(struct resource_config *rec)
{
    if (rec)
    {
        for (int32_t i = 0; 
                i < sizeof(rec->config) / sizeof(rec->config[0]);
                ++i)
        {
            channels_destroy(&rec->config[i]);
        }
        free(rec);
    }
}

void resource_show(struct resource_config *rec)
{
    if (!rec)
    {
        return;
    }

    printf("Job config:\n");

    struct job_item *job_ptr = NULL;
    struct fragment *cur = rec->config[JOB].head , *next = NULL;
    
    while(cur)
    {
        job_ptr = (struct job_item*)channels_iterator(cur , &next);
        if (job_ptr)
        {
            printf("job_name:%s\n" , job_ptr->name);
            printf("\tjob_type:%d\n" , job_ptr->job_type);
            printf("\tjob_class:%u\n" , job_ptr->job_class);
            printf("\timage_memory_required:%u\n" , job_ptr->image_memory_required);
            printf("\timage_memory_lowest:%u\n" , job_ptr->image_memory_lowest);
            printf("\tvideo_memory_required:%u\n" , job_ptr->video_memory_required);
            printf("\tvideo_memory_lowest:%u\n" , job_ptr->video_memory_lowest);
            printf("\tresource_io_id:%u\n" , job_ptr->resource_io_id);
            printf("\tstart_module_id:%u\n" , job_ptr->start_module_id);
            printf("\tmono_type:%d\n" , job_ptr->mono_type);
            printf("\twait_type:%d\n" , job_ptr->wait_type);
            printf("\tdefault_priority:%d\n" , job_ptr->default_priority);
            printf("\tdepend_list:");
            for (int32_t i = 0; 
                    i < sizeof(job_ptr->depend_list)/sizeof(job_ptr->depend_list[0]) &&
                    job_ptr->depend_list[i].number;
                    ++i)
            {
                printf("{");
                struct fragment *_cur = job_ptr->depend_list[i].head , *_next = NULL;
                while(_cur)
                {
                    int32_t id = 0; 
                    id = (int32_t) channels_iterator(_cur , &_next);
                    if (id)
                    {
                        printf("%d " , id);
                    }
                    _cur = _next;
                }
                printf("}\t");
            }
            printf("\n");
        }

        cur = next;
    }

    printf("\nresource config:\n");

    cur = rec->config[RESOURCE].head;
    
    while(cur)
    {
        struct resource_item *resource_ptr = 
            (struct resource_item*)channels_iterator(cur , &next);

        if (resource_ptr)
        {
            printf("name:%s\n" , resource_ptr->name);
            printf("\tid:%u\n" , resource_ptr->id);
            printf("\tnumber:%d\n" , resource_ptr->number);
            printf("\treuse:%d\n" , resource_ptr->reuse);
        }

        cur = next;
    }
}

/**
 * @}
 */
