############################################################
# 项目名称以及生成应用程序名称
project_name  := pedk.elf


############################################################
# 代码文件路径
code_path :=src/   							\
			src/basic/sys/					\
			src/manager/					\
			src/runtime/modules/process/	\
			src/runtime/modules/std/		\
			src/runtime/modules/bridge/		\
			src/runtime/modules/pesf/		\
			src/runtime/modules/			\
			src/runtime/utils/				\
			src/runtime/					\
			src/trans/monitor/				\
			src/trans/						\

############################################################
# 头文件路径
include_path  := ./ \
				src/	\
				src/API/test	\
				src/API/event/					\
				src/API/test/					\
				src/API/common/				\
				src/API/ui/					\
				src/API/device/				\
				src/API/copy/					\
				src/API/scan/					\
				src/API/print/					\
				src/API/fax/					\
				src/API/jobs/					\
				src/API/Addressbook/			\
				src/API/net/              	 	\
				src/API/usb/					\
				src/API/shortcuts/				\
				src/API/auth_whitelist/		\
				src/API/crypto/				\
				src/API/storage/				\
				src/API/defined/			\
				src/runtime/modules/utils/	\
				$(STAGING_DIR)/usr/include/quickjs/	\
				$(STAGING_DIR)/usr/lib/libuv/include	\
				$(STAGING_DIR)/usr/include/ \
				$(STAGING_DIR)/usr/include/cjson \
				src/runtime/				\
				src/runtime/modules/bridge/	\

include_flags := $(patsubst %, -I%, $(include_path))

############################################################
# 库文件路径
lib_path      :=  .	\
				$(STAGING_DIR)/usr/lib/quickjs/ \
				$(STAGING_DIR)/usr/lib/libuv \
				$(STAGING_DIR)/usr/lib/	\
				src/API/	\
#				  ../tools/httc/ram-2g/Base \
#                 net/net_lib \

lib_flags     := $(patsubst %, -L%, $(lib_path))

############################################################
# 库文件
lib_file      := -lm 	\
				 -ldl	\
				 -lquickjs \
				 -luv_a		\
				 -lcurl		\
				 -lpedk		\
				 -lpthread	\
				 -lcjson
#                 -lmdnsresponder \


############################################################
# objs
obj-y :=
export obj-y


############################################################
# 包含子Makefile
sub_makefile_path = $(patsubst %, %makefile, $(code_path))
-include $(sub_makefile_path)


############################################################
#
objs_file    := $(patsubst %, %, $(obj-y))
deps_file    := $(patsubst %.o, %.d, $(obj-y))


############################################################
# 交叉编译变量
CC		  := $(CROSS_COMPILE)gcc
#AR      := $(CROSS_COMPILE)ar
#AS      := $(CROSS_COMPILE)as
#LD      := $(CROSS_COMPILE)ld
#NM      := $(CROSS_COMPILE)nm
#GCC 	   := $(CROSS_COMPILE)gcc
#CPP 	   := $(CROSS_COMPILE)cpp
#CXX 	   ?= $(CROSS_COMPILE)c++
#RANLIB  ?= $(CROSS_COMPILE)ranlib
#STRIP   ?= $(CROSS_COMPILE)strip
#READELF ?= $(CROSS_COMPILE)readelf
#OBJCOPY ?= $(CROSS_COMPILE)objcopy
#OBJDUMP ?= $(CROSS_COMPILE)objdump

############################################################
# 编译时间
build_time="\"`date '+%Y-%m-%d %H:%M:%S'`"\"
SVN_REV		:= -D'SVN_REV="$(shell svnversion -n)"'

############################################################
# 编译参数 -D__DATE__=$(build_date) -D__TIME__=$(build_time)
cc_flags = -rdynamic -funwind-tables -ffunction-sections -g -std=gnu99 -Wall -Dbuild_time=$(build_time) $(SVN_REV)

$(app_defines)
############################################################
# 编译执行
all: $(project_name)
-include $(deps_file)
$(project_name): $(objs_file) $(deps_file)
	@$(CC) $(objs_file) -o $(project_name) $(lib_flags) $(lib_file) 
%.o:%.c
	@echo " [CC]   $@"
	@$(CC) $(cc_flags) $(define_flags) $(include_flags) -c $< -o $@

%.d:%.c
	@$(CC) -MM $(include_flags) $< -o $@


############################################################
# 清除
.PHONY:clean
clean:
	@rm -rf $(objs_file) $(deps_file) $(project_name) *.c *.h *.so

############################################################
# 执行脚本
.PHONY:run_app
run_app:
	@./$(project_name)
