/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file job_manager.c
 * @addtogroup system_manager
 * @{
 * @brief system job manager
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18
 */

#include "job_common.h"
#include "ulog.h"
#include "resource_config.h"
/*#include "print.h"*/
#include "job_assistance.h"
#include "jobinfo_storage.h"
#include <sys/eventfd.h>
#include <unistd.h>
#include "hal.h"
#if CONFIG_SDK_PEDK
#include "job_pedkapi.h"
#endif

static struct ulog s_log = {
    .name = SYS_JOB_LOG, //SYS_JOB_LOG 定义在Makefile的KBUILD_CFLAGS中
    .file = NULL,
    .fd = -1 ,
    .limitsize = 0,
    .level = DEBUG,
    .o_time = 1,
    .o_file = 0,
    .o_func = 1,
    .o_line = 1,
    .o_module = 1,
    .o_level = 1,
    .o_pid = 0,
    .o_thread = 0,
    .o_color = 0,
    .next = NULL
};

static SYSTEM_DATA_S s_sys_data;

static void _release_ptmem(JOB_RESOURCES_DATA_S **resource)
{
    if ((*resource)->image_mem && memory_unreference((*resource)->image_mem) == 0)
    {
        pi_imagemem_destory((*resource)->image_mem);
        (*resource)->image_mem = NULL;
    }
    if ((*resource)->video_mem && memory_unreference((*resource)->video_mem) == 0)
    {
        pi_imagemem_destory((*resource)->video_mem);
        (*resource)->video_mem = NULL;
    }
}

static void _free_job_resource_data(JOB_RESOURCES_DATA_S **resource)
{
    if (!resource || !*resource)
        return;

    if ((*resource)->gqio)
    {
        GQIO_CLOSE((GQIO_S*)(*resource)->gqio);
        (*resource)->gqio = NULL;
    }
    _release_ptmem(resource);
    pi_free(*resource);
    *resource = NULL;
}

static void _free_one_resource_state(void *data)
{
    ONE_RESOURCE_STATE_P state = (ONE_RESOURCE_STATE_P)data;

    if (state)
    {
        struct resource_item *rit = (struct resource_item*)channels_find(
                s_sys_data._config->config + RESOURCE ,
                state->resource_id);

        if (rit)
        {
            rit->number++;
            ULOG_INFO(SYS_JOB_LOG , "%s resource recycle , number:%d\n" ,rit->name , rit->number);
        }
        if (state->current_resource_data)
        {
            pi_free(state->current_resource_data);
            state->current_resource_data = NULL;
        }
        pi_free(state);
    }
}

static void _job_request_fail_free(JOB_REQUEST_S **job_request)
{
    if (!job_request || !*job_request)
        return;

    if ((*job_request)->pqio)
    {
        QIO_CLOSE((QIO_S*)(*job_request)->pqio);
        (*job_request)->pqio = NULL;
    }
    if ((*job_request)->job_config_param)
    {
        pi_free((*job_request)->job_config_param);
        (*job_request)->job_config_param = NULL;
    }
    pi_free(*job_request);
    *job_request = NULL;
}

static void _free_one_job(void *data)
{
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;

    if (job)
    {
        if (memory_unreference(job->_job_msg.msg3) == 0)
        {
            _job_request_fail_free((JOB_REQUEST_S **)&job->_job_msg.msg3);
        }
        channels_destroy(&job->_resource_link);
        _free_job_resource_data(&job->_job_resource_data);
        pi_free(job);
    }
}

static uint32_t _get_job_id(void)
{
    uint32_t job_id = 0;

    pi_mutex_lock(s_sys_data._mtx);
    job_id = s_sys_data.job_id++;
    if (job_id == 0)
    {
        job_id = s_sys_data.job_id++;
    }
    pi_mutex_unlock(s_sys_data._mtx);
    return job_id;
}

static void _notify_job_info(ONE_JOB_INFO_P one_job , JOB_STATUS_E status)
{
    one_job->_job_info.status = status;
    if (one_job->_job_info.obj || one_job->_forbid_startup)
    {
        pi_event_mgr_notify(s_sys_data._client , EVT_TYPE_SYSTEMJOB_UPDATE , &one_job->_job_info , sizeof(JOBINFO_S));
        ULOG_INFO(SYS_JOB_LOG , "job[%u]-%d/%d info report..\n" , one_job->_job_info.job_id , one_job->_job_info.status , one_job->_job_info.status_detail);
#if CONFIG_AUDIT_RECORD
        if (status == JOB_FINISHED && one_job->_job_info.obj < FILE_OBJ)
        {
            pi_event_mgr_notify(s_sys_data._client , EVT_TYPE_SECURITY_AUDITJOB_RECORD , &one_job->_job_info , sizeof(JOBINFO_S));
            ULOG_INFO(SYS_JOB_LOG , "job info report security auditjob.\n");
        }
#endif
#if CONFIG_SDK_PEDK
        pedk_jobinfo_update(one_job);
#endif
    }
}

static int32_t _fill_link_resource(struct job_item *jit,
        struct fragment *cur ,
        ONE_JOB_INFO_P one_job ,
        int32_t index ,
        CURRENT_RESOURCE_DATA_P refer_pre_resource)
{
    int32_t ret = -1 , pre_resource = 0;
    struct fragment *next = NULL;

    if (refer_pre_resource)
    {
        pre_resource = refer_pre_resource->resource_id;
    }

    while(cur)
    {
        int32_t resource_id = (int32_t)channels_iterator(cur , &next);

        if (!resource_id)
        {
            ULOG_ERROR(SYS_JOB_LOG , "resource id invalid\n");
            ret = -1;
            break;
        }

        struct resource_item *rit = (struct resource_item*)channels_find(
                s_sys_data._config->config + RESOURCE , resource_id);

        if (!rit)
        {
            ULOG_ERROR(SYS_JOB_LOG , "resource id:%d nonexistent\n" , resource_id);
            ret = -1;
            break;
        }

        one_job->_resource_io_id = jit->resource_io_id;
        if (!rit->reuse && rit->number <= 0)
        {
            ULOG_ERROR(SYS_JOB_LOG , "%s:%d resource unusable , number:%d , reuse:%d\n" ,
                    rit->name , rit->id , rit->number , rit->reuse);
            ret = -1;
            break;
        }

        ONE_RESOURCE_STATE_P res_state = (ONE_RESOURCE_STATE_P)pi_malloc(sizeof(ONE_RESOURCE_STATE_S));

        if (!res_state)
        {
            ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
            ret = -1;
            break;
        }
        res_state->resource_id = rit->id;
        res_state->state = USING;
        res_state->current_resource_data = (CURRENT_RESOURCE_DATA_P)pi_malloc(sizeof(CURRENT_RESOURCE_DATA_S));
        if (!res_state->current_resource_data)
        {
            ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
            pi_free(res_state);
            ret = -1;
            break;
        }
        pi_memset(res_state->current_resource_data , 0 , sizeof(CURRENT_RESOURCE_DATA_S));
        res_state->current_resource_data->prev_module = pre_resource;
        res_state->current_resource_data->job_id = one_job->_job_info.job_id;
        res_state->current_resource_data->resource_id = resource_id;
        res_state->current_resource_data->job_resource_data = one_job->_job_resource_data;
        res_state->current_resource_data->job_resource_data->suspend_jobinfo = &one_job->_job_info;

        if (pre_resource &&
            refer_pre_resource != res_state->current_resource_data &&
            !refer_pre_resource->next_module[0] &&
            !refer_pre_resource->next_module[index])
        {
            refer_pre_resource->next_module[0] = resource_id;
        }
        pre_resource = resource_id;
        refer_pre_resource = res_state->current_resource_data;
        if (channels_tail_insert(&one_job->_resource_link , resource_id , res_state , 0) == -1)
        {
            ULOG_ERROR(SYS_JOB_LOG , "channels insert failed\n");
            channels_destroy(&one_job->_resource_link);
            ret = -1;
            break;
        }
        else
        {
            rit->number--;
            res_state->current_resource_data->use_resource_number++;
        }
        ULOG_INFO(SYS_JOB_LOG , "%s:%d resource usable\n" , rit->name , rit->id);
        cur = next;
        ret = 0;
    }
    return ret;
}

static int32_t _acquire_link_resource(struct job_item *jit ,
        struct channels *res_item ,
        ONE_JOB_INFO_P one_job ,
        int32_t index)
{
    CURRENT_RESOURCE_DATA_P refer_pre_resource = NULL;

    if (one_job->_resource_link.number == 0)
    {
        return _fill_link_resource(jit , res_item->head , one_job , index , refer_pre_resource);
    }
    else if (res_item->number == 0)
    {
        ULOG_WARN(SYS_JOB_LOG , " resource item number is 0 ..\n");
        return 0;
    }
    else
    {
        struct fragment *cur1 = one_job->_resource_link.head , *next1 = NULL;
        struct fragment *cur2 = res_item->head , *next2 = NULL;
        int32_t same = 0 , resource_id = 0 , pre_resource = 0;

        while(cur1 && cur2)
        {
            ONE_RESOURCE_STATE_P res_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur1 , &next1);
            resource_id = (int32_t)channels_iterator(cur2 , &next2);

            if (res_state->resource_id != resource_id)
            {
                break;
            }
            else
            {
                same = 1;
            }

            pre_resource = resource_id;
            refer_pre_resource = res_state->current_resource_data;
            cur1 = next1;
            cur2 = next2;
        }

        if (!same ||!cur2)
        {
            return 0;
        }
        else if (!cur1)
        {
            resource_id = (int32_t)channels_iterator(cur2 , &next2);
        }

        struct resource_item *rit = (struct resource_item*)channels_find(
                s_sys_data._config->config + RESOURCE , pre_resource);

        if (!rit)
        {
            ULOG_ERROR(SYS_JOB_LOG , "resource id:%d nonexistent\n" , pre_resource);
            return -1;
        }

        if (!rit->reuse && rit->number <= 0)
        {
            ULOG_ERROR(SYS_JOB_LOG , "%s:%d resource unusable , number:%d , reuse:%d\n" ,
                    rit->name , rit->id , rit->number , rit->reuse);
            return -1;
        }
        if (refer_pre_resource->next_module[0])
        {
            rit->number--;
            refer_pre_resource->use_resource_number++;
        }
        refer_pre_resource->next_module[index] = resource_id;
        return _fill_link_resource(jit , cur2 , one_job , index , refer_pre_resource);
    }
}

static int32_t _acquire_resource(struct job_item *jit , ONE_JOB_INFO_P one_job)
{
    for (int32_t i = 0;
         i < sizeof(jit->depend_list)/sizeof(jit->depend_list[0]);
         ++i)
    {
        if (_acquire_link_resource(jit , jit->depend_list + i , one_job , i) == -1)
            return -1;
    }
    return 0;
}

static void _notfiy_resource_job_continue(ONE_JOB_INFO_P one_job)
{
    struct fragment *cur = one_job->_resource_link.head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state;
    ROUTER_MSG_S send_msg;

    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state && one_resource_state->state != CONTINUE)
        {
            one_resource_state->state = CONTINUE;
            send_msg.msgType = MSG_CTRL_JOB_CONTINUE;
            send_msg.msg1 = one_job->_job_info.job_id;
            send_msg.msg2 = 0;
            send_msg.msg3 = NULL;
            send_msg.msgSender = MID_SYS_JOB_MGR;
            task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

            ULOG_DEBUG(SYS_JOB_LOG , "Notify %d continue job:%u\n" , one_resource_state->resource_id , one_job->_job_info.job_id);
        }
        cur = next;
    }
}

static int32_t _notify_resource_link(ONE_JOB_INFO_P one_job)
{
    struct fragment *cur = one_job->_resource_link.head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state;
    ROUTER_MSG_S send_msg;

    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state)
        {
            send_msg.msgType = MSG_CTRL_RESOURCE_LINK;
            send_msg.msg1 = 0;
            send_msg.msg2 = 0;
            send_msg.msg3 = one_resource_state->current_resource_data;
            send_msg.msgSender = MID_SYS_JOB_MGR;
            task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

            ULOG_DEBUG(SYS_JOB_LOG , "LINK[%p] %d %d:%d-%d\n" , one_resource_state->current_resource_data ,
                    one_resource_state->resource_id ,
                    one_resource_state->current_resource_data->prev_module ,
                    one_resource_state->current_resource_data->next_module[0] ,
                    one_resource_state->current_resource_data->next_module[1]);
        }
        cur = next;
    }

    struct job_item *jit = (struct job_item *)channels_find(s_sys_data._config->config + JOB ,
            one_job->_parser_result.job_type);
    if (jit && jit->start_module_id)
    {
        send_msg.msgType = MSG_CTRL_RESOURCE_START;
        send_msg.msg1 = one_job->_start_code;
        send_msg.msg2 = 0;
        send_msg.msg3 = NULL;
        send_msg.msgSender = MID_SYS_JOB_MGR;
        task_msg_send_by_router(jit->start_module_id, &send_msg);

        ULOG_DEBUG(SYS_JOB_LOG , "LINK START ->%d [%u]\n" , jit->start_module_id , one_job->_start_code);
    }

    return 0;
}

static MEMOBJ_P _acquire_common_memory(uint32_t *memory_required ,
        uint32_t memory_lowest ,
        uint32_t memory_fragment ,
        int class)
{
    MEMOBJ_P         job_memory = NULL;
    struct init_info mem_cache;

    pi_memset(&mem_cache, 0, sizeof(struct init_info));
    mem_cache.block_count    = 0;
    mem_cache.block_size     = 0;
    pi_snprintf(mem_cache.handle_name, sizeof(mem_cache.handle_name) , "sys_job_mgr%d", class);

    do
    {
        mem_cache.size               = (*memory_required) << 20;//将MB->bytes

        job_memory = pi_imagemem_create(mem_cache, JOB_BAND_COUNT_MIN << 1, 0);

        if(NULL == job_memory)
        {
            *memory_required -= MEM_DECREASE_STEP;
        }
        else
        {
            ULOG_INFO(SYS_JOB_LOG , "Create image memory %d successfully, with %d fragment memory\n",
                    mem_cache.size, mem_cache.fragment_pool_size);
            break;
        }

    }while (*memory_required >= memory_lowest);

    return job_memory;
}

static int32_t _acquire_ptmem(struct job_item *jit , ONE_JOB_INFO_P one_job)
{
    uint32_t memory_required = one_job->_parser_result.memsize?one_job->_parser_result.memsize:jit->image_memory_required;
    uint32_t memory_lowest = jit->image_memory_lowest;
    uint32_t memory_fragment = jit->image_memory_fragment;
    JOB_RESOURCES_DATA_S *job_resource_data = one_job->_job_resource_data;

    if (memory_required)
    {
        job_resource_data->image_mem = memory_get_reference(jit->job_class , PTMEM_IMAGE);
        if (!job_resource_data->image_mem &&
            !(job_resource_data->image_mem = _acquire_common_memory(&memory_required ,
                memory_lowest ,
                memory_fragment ,
                jit->job_class)))
        {
            return -1;
        }
        memory_set_reference(jit->job_class , PTMEM_IMAGE , job_resource_data->image_mem);
    }
    if (one_job->_parser_result.memsize &&
        one_job->_parser_result.memsize > memory_required)
    {
        one_job->_start_code = G_MEMORY_FAIL;
    }

    memory_required = jit->video_memory_required;
    memory_lowest = jit->video_memory_lowest;
    memory_fragment = jit->video_memory_fragment;
    if (memory_required)
    {
        job_resource_data->video_mem = memory_get_reference(jit->job_class , PTMEM_VIDEO);
        if (!job_resource_data->video_mem &&
            !(job_resource_data->video_mem = _acquire_common_memory(&memory_required ,
                memory_lowest ,
                memory_fragment ,
                jit->job_class)))
        {
            memory_unreference(job_resource_data->image_mem);
            job_resource_data->image_mem = NULL;
            return -1;
        }
        memory_set_reference(jit->job_class , PTMEM_VIDEO , job_resource_data->video_mem);
    }
    return 0;
}

static int32_t _again_acquire_ptmem(ONE_JOB_INFO_P one_job)
{
    JOB_RESOURCES_DATA_S *job_resource_data = one_job->_job_resource_data;
    JOB_TYPE_E job_type = one_job->_parser_result.job_type;
    struct job_item *jit = (struct job_item *)channels_find(s_sys_data._config->config + JOB , job_type);

    if (jit)
    {
        return _acquire_ptmem(jit , one_job);
    }
    else
    {
        return -1;
    }
}

static int _match_source_job(void *data , void *options)
{
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;
    int32_t job_type = (int32_t)options;

    return job->_parser_result.job_type == job_type ? 1 : 0;
}

static int _match_designated_object_job(void *data , void *options)
{
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;
    JOB_CONFIG_OBJ_E object = (JOB_CONFIG_OBJ_E)options;

    return job->_job_info.obj == object ? 1 : 0;
}

static int _match_cancel_source_job(void *data , void *options)
{
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;
    struct channels *chs = (struct channels*)options;

    struct job_item *jit = (struct job_item *)channels_find(chs ,
            job->_parser_result.job_type);

    if (!jit)
        return 0;

    return jit->msgrouter_source.number >= 2 ? 1 : 0;
}

static int _match_scan_or_copy_job(void *data , void *options)
{
    (void)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;
    return (job->_job_info.obj == COPY_OBJ || job->_job_info.obj == SCAN_OBJ) ? 1: 0;
}

static int _match_startup_job(void *data , void *options)
{
    (void)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;
    return job->_forbid_startup == 0 ? 1 : 0;
}

static int _match_noacl_config(void *data , void *options)
{
    (void)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;

    return job->_parser_result.job_type != JOB_TYPE_PRINT_ACL ? 1 : 0;
}

static int _match_normal_printjob(void *data , void *options)
{
    (void)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;

    return job->_job_info.obj == PRINT_OBJ && job->_job_info.priority == JOB_NORMAL ? 1 : 0;
}

static int _match_printjob(void *data , void *options)
{
    int only_find_printing = (int)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;

    if (only_find_printing)
    {
        return job->_job_info.obj == PRINT_OBJ && job->_job_info.status_detail == JOB_NONE ? 1 : 0;
    }
    else
    {
        return job->_job_info.obj == PRINT_OBJ ? 1 : 0;
    }
}

static int _match_priority_printjob(void *data , void *options)
{
    (void)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;

    return job->_job_info.obj == PRINT_OBJ && job->_job_info.priority == JOB_PRIORITIZED ? 1 : 0;
}

#if CONFIG_SDK_PEDK
static int _match_wonum_job(void *data , void *options)
{
    uint32_t wonum = (uint32_t)options;
    ONE_JOB_INFO_P job = (ONE_JOB_INFO_P)data;

    return job->_job_info.wonum == wonum ? 1 : 0;
}

static uint32_t _wonum_to_jobid(uint32_t wonum)
{
    uint32_t jobid = 0;
    ONE_JOB_INFO_P one_job = NULL;

    pi_mutex_lock(s_sys_data._mtx);
    for (int32_t i = 0; i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + i , _match_wonum_job , (void*)wonum);
        if (one_job)
        {
            jobid = one_job->_job_info.job_id;
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    return jobid;
}
#endif

static void _job_reinput_queue(ONE_JOB_INFO_P one_job)
{
    if (one_job->_job_info.job_id == 0)
    {
        one_job->_job_info.job_id = _get_job_id();
    }
    pi_mutex_lock(s_sys_data._mtx);
    ONE_JOB_INFO_P first_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
            _match_startup_job , NULL);
    if (first_job && first_job->_job_info.job_id > one_job->_job_info.job_id)
    {
        channels_head_insert(s_sys_data._job_set + JOB_WAITING , one_job->_job_info.job_id , one_job , 0);
    }
    else
    {
        channels_tail_insert(s_sys_data._job_set + JOB_WAITING , one_job->_job_info.job_id , one_job , 0);
    }
    pi_mutex_unlock(s_sys_data._mtx);
}

static void _internal_mode_set(JOB_PRIORITY_E priority)
{
    pi_mutex_lock(s_sys_data._mtx_mode);
    s_sys_data._mode = priority;
    pi_mutex_unlock(s_sys_data._mtx_mode);
}

static JOB_PRIORITY_E _internal_mode_get(void)
{
    JOB_PRIORITY_E priority = JOB_NORMAL;

    pi_mutex_lock(s_sys_data._mtx_mode);
    priority = s_sys_data._mode;
    pi_mutex_unlock(s_sys_data._mtx_mode);
    return priority;
}

static ONE_JOB_INFO_P _apply_job(uint32_t old_jobid)
{
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)pi_malloc(sizeof(ONE_JOB_INFO_S));

    if (!one_job)
    {
        return NULL;
    }
    pi_memset(one_job , 0 , sizeof(ONE_JOB_INFO_S));

    one_job->_cur_state = ALLOC_STATE;
    one_job->_forbid_startup = 1;
    one_job->_old_jobid = old_jobid;
    one_job->_job_info.status = JOB_WAITING;
    return one_job;
}

static void _job_abnormal_change(uint32_t jobid , ONE_JOB_INFO_P one_job)
{
    if (!jobid)
    {
        return ;
    }
    one_job->_job_info.status = JOB_FINISHED;
    one_job->_job_info.status_detail = JOB_ABORTED;

    pi_event_mgr_notify(s_sys_data._client , EVT_TYPE_SYSTEMJOB_UPDATE , &one_job->_job_info , sizeof(JOBINFO_S));
    ULOG_INFO(SYS_JOB_LOG , "job[%u]-%d/%d info report..\n" , one_job->_job_info.job_id , one_job->_job_info.status , one_job->_job_info.status_detail);
    jobinfo_storage_write(jobid , &one_job->_job_info , sizeof(one_job->_job_info));
}

static int32_t _job_request_handle(ONE_JOB_INFO_P one_job , ROUTER_MSG_S *msg , void *move_gqio)
{
    uint32_t job_id = 0 , err_code = G_SUCCESS;
    JOB_REQUEST_S *job_request = msg->msg3;
    JOBINFO_S tjob;
    void *callback_context = job_request->context;
    void (*notify_callback)(uint32_t jobid , int result , void *context) = job_request->notify;

    memset(&tjob , 0 , sizeof(tjob));
    if (!one_job)
    {
        if (msg->msg1 != 0)
        {
            job_id = msg->msg1;
            pi_mutex_lock(s_sys_data._mtx);
            one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + JOB_WAITING , job_id);
            channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 0);
            pi_mutex_unlock(s_sys_data._mtx);
            if (!one_job)
            {//如果现有队列中找不到时，需要在jobinfo模块已完成的记录中查找，
             //有可能是样本打印作业、密码作业或延迟作业，存在则将其从队列中移除
                if (jobinfo_storage_remove_queue_record(job_id , &tjob) == 0)
                {
                    one_job = _apply_job(job_id);
                    one_job->_job_info.job_type = tjob.job_type;
                    job_id = 0;//为了区别之前已经存储完成的作业(样本打印、密码和延迟)需重置jobid，然后使用新jobid
                }
                if (!one_job)
                {
                    err_code = G_JOB_INVALID;
                    _job_request_fail_free((JOB_REQUEST_S **)&msg->msg3);
                    ULOG_WARN(SYS_JOB_LOG , "%d jobid inexistence\n" , job_id);
                    goto END;
                }
            }
            if (tjob.timed_task_timestamp && one_job->_old_jobid == tjob.job_id)
            {//延迟作业发起时，须使用存储阶段的时间
                one_job->_job_info.timestamp = tjob.timestamp;
                one_job->_job_info.timed_task_timestamp = tjob.timed_task_timestamp;
                ULOG_DEBUG(SYS_JOB_LOG , "delay jobid:%u\n" , one_job->_old_jobid);
            }
            one_job->_forbid_startup = 0;
        }
        else
        {
            one_job = (ONE_JOB_INFO_P)pi_malloc(sizeof(ONE_JOB_INFO_S));
            if (!one_job)
            {
                err_code = G_MEMORY_FAIL;
                _job_request_fail_free((JOB_REQUEST_S **)&msg->msg3);
                ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
                goto END;
            }
            pi_memset(one_job , 0 , sizeof(ONE_JOB_INFO_S));
            one_job->_cur_state++;
        }
    }
    else
    {
        job_id = one_job->_job_info.job_id;
    }

    if (one_job->_cur_state == ALLOC_STATE)
    {
        pi_memcpy(&one_job->_job_msg , msg , sizeof(ROUTER_MSG_S));
        memory_set_reference(0 , OTHER_ADDR , one_job->_job_msg.msg3);
        channels_init(&one_job->_resource_link , NULL , _free_one_resource_state);
        one_job->_job_resource_data = (JOB_RESOURCES_DATA_S*)pi_malloc(sizeof(JOB_RESOURCES_DATA_S));
        if (!one_job->_job_resource_data)
        {
            err_code = G_MEMORY_FAIL;
            ULOG_ERROR(SYS_JOB_LOG , "malloc failed\n");
            _job_abnormal_change(job_id , one_job);
            _free_one_job(one_job);
            goto END;
        }
        pi_memset(one_job->_job_resource_data , 0 , sizeof(JOB_RESOURCES_DATA_S));
        one_job->_cur_state++;
    }
    if (one_job->_cur_state == PARSE_STATE)
    {
        int32_t ret = -1;

        if ( (msg->msgSender == MID_PANEL ||
             (msg->msgSender == MID_PORT_NET && !job_request->pqio)) && !move_gqio )
        {
            ret = pre_parser_get_job_type(job_request->job_config_param ,
                    msg->msgSender ,
                    msg->msg2 ,
                    &one_job->_parser_result);
            one_job->_job_resource_data->gqio = one_job->_parser_result.gqio;
        }
        else
        {
            if (!one_job->_job_resource_data->gqio && !move_gqio)
            {
                one_job->_job_resource_data->gqio = gqio_create((QIO_P)job_request->pqio ,
                        job_request->io_class ,
                        job_request->io_via);
                ret = pre_parser_get_job_type(one_job->_job_resource_data->gqio ,
                        msg->msgSender ,
                        msg->msg2 ,
                        &one_job->_parser_result);
            }
            else
            {
                if (move_gqio)
                {
                    one_job->_job_resource_data->gqio = move_gqio;
                }
                ret = pre_parser_get_qio_job_type(one_job->_job_resource_data->gqio ,
                        &one_job->_parser_result);
            }
        }
        ((JOB_REQUEST_S*)(one_job->_job_msg.msg3))->pqio = NULL;
        if (ret == -1)
        {
            err_code = G_UNKNOW;
            ULOG_ERROR(SYS_JOB_LOG , "parse job failed\n");
            _job_abnormal_change(job_id , one_job);
            _free_one_job(one_job);
            goto END;
        }
        one_job->_cur_state++;
    }

    struct job_item *jit = (struct job_item *)channels_find(s_sys_data._config->config + JOB ,
            one_job->_parser_result.job_type);
    if (!jit)
    {
        err_code = G_UNKNOW;
        ULOG_ERROR(SYS_JOB_LOG , "Unknow job type:%d\n" , one_job->_parser_result.job_type);
        _job_abnormal_change(job_id , one_job);
        _free_one_job(one_job);
        goto END;
    }

    ULOG_INFO(SYS_JOB_LOG , "job name:%s , job type:%d , memsize:%u\n" , jit->name , one_job->_parser_result.job_type , one_job->_parser_result.memsize);
    one_job->_job_info.obj = jobtype_to_jobobj(one_job->_parser_result.job_type);
    if (_internal_mode_get() == JOB_SILENT)
    {
        ULOG_WARN(SYS_JOB_LOG , "current silent mode!!\n");
        _job_reinput_queue(one_job);
        err_code = G_PRIORITY_LIMIT;
        goto END;
    }
    //NOTE:升级模式下只处理ACL指令
    else if (_internal_mode_get() == JOB_UPGRADE && one_job->_job_info.obj)
    {
        ULOG_WARN(SYS_JOB_LOG , "current upgrade mode!!\n");
        _job_reinput_queue(one_job);
        err_code = G_PRIORITY_LIMIT;
        goto END;
    }
    //NOTE:维修模式下只处理复印和内部页作业
    else if (_internal_mode_get() == JOB_MAINTAINED &&
             ( (one_job->_job_info.obj == PRINT_OBJ && job_request->io_via != IO_VIA_INTERNAL) ||
               (one_job->_job_info.obj != COPY_OBJ && one_job->_job_info.obj != PRINT_OBJ) ) )
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for initiating the job.\n" , _internal_mode_get());
        _job_reinput_queue(one_job);
        err_code = G_PRIORITY_LIMIT;
        goto END;
    }
    //NOTE:中断模式下只处理复印或扫描作业
    else if (_internal_mode_get() == JOB_INTERRUPTED && one_job->_job_info.obj != COPY_OBJ && one_job->_job_info.obj != SCAN_OBJ)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for initiating the job.\n" , _internal_mode_get());
        _job_reinput_queue(one_job);
        err_code = G_PRIORITY_LIMIT;
        goto END;
    }

    if (one_job->_cur_state == GIVEINFO_STATE)
    {
        one_job->_job_info.io_via = ((JOB_REQUEST_S*)(msg->msg3))->io_via;
        if (jit->msgrouter_source.number == 2 && msg->msgSender != (int32_t)channels_head(&jit->msgrouter_source))
        {
            //从等待队列中查找同作业类型的作业(上半部分)
            ULOG_WARN(SYS_JOB_LOG , "Receive an job:%d from source:%d\n" , one_job->_parser_result.job_type , msg->msgSender);

            pi_mutex_lock(s_sys_data._mtx);
            ONE_JOB_INFO_P source_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                    _match_source_job , (void*)one_job->_parser_result.job_type);
            if (source_job)
            {
                //找到上半部分后，组成一份作业，然后发起
                job_id = source_job->_job_info.job_id;
                //source_job->_job_info.io_via = one_job->_job_info.io_via;  不修改面板设置的iovia
                source_job->_job_resource_data->gqio = one_job->_job_resource_data->gqio;
                source_job->_forbid_startup = 0;
                source_job->_cur_state = RESOURCE_START_STATE + 1;
                one_job->_job_resource_data->gqio = NULL;
                channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , source_job , 0);
                channels_tail_insert(s_sys_data._job_set + JOB_RUNNING , source_job->_job_info.job_id , source_job , 0);
                _notify_resource_link(source_job);
            }
            else
            {
                ULOG_ERROR(SYS_JOB_LOG , "Not received the same type of job from the [MID:%d] before!\n" ,
                        (int32_t)channels_head(&jit->msgrouter_source));
                err_code = G_UNKNOW;
                _job_abnormal_change(job_id , one_job);
            }
            pi_mutex_unlock(s_sys_data._mtx);

            _free_one_job(one_job);
            one_job = source_job;
            goto END;
        }
        if (job_id == 0)
        {
            job_id = _get_job_id();
        }
        if (one_job->_job_info.job_type == 0)
        {
            one_job->_job_info.job_type = one_job->_parser_result.job_type;
        }
        if (one_job->_job_info.timestamp == 0)
        {
            one_job->_job_info.timestamp = time(NULL);
        }
        one_job->_job_info.job_id = job_id;
        one_job->_job_info.status = JOB_WAITING;
        //s_sys_data._ejob.print_job = 0;
        if (one_job->_job_info.obj == PRINT_OBJ)
        {
            s_sys_data._ejob.print_job = 1;
            s_sys_data._ejob.job_id = job_id;
        }
        one_job->_job_resource_data->job_type = one_job->_parser_result.job_type;
        one_job->_job_resource_data->job_class = jit->job_class;
        one_job->_job_resource_data->job_config_param = ((JOB_REQUEST_S*)(msg->msg3))->job_config_param;
        one_job->_job_resource_data->prepare_priv_args = one_job->_parser_result.priv_args;
        one_job->_job_resource_data->job_obj = msg->msg2;
        one_job->_cur_state++;
    }

    if (one_job->_cur_state == ACQUIRE_RESOURCE_STATE)
    {
        pi_mutex_lock(s_sys_data._mtx);
        ONE_JOB_INFO_P last_job = channels_find_match(s_sys_data._job_set + JOB_RUNNING ,
                _match_designated_object_job ,
                (void*)one_job->_job_info.obj);
        pi_mutex_unlock(s_sys_data._mtx);

        if (jit->multiple_job_depend_ssd && last_job && s_sys_data._mount_ssd == 0)
        {
            ULOG_ERROR(SYS_JOB_LOG , "Multiple job depend on SSD support!!\n");
            err_code = G_RESOURCE_FAIL;
            if (jit->wait_type)
            {
                _job_abnormal_change(job_id , one_job);
                _free_one_job(one_job);
            }
            else
            {
                _job_reinput_queue(one_job);
            }
            goto END;
        }
        if (_acquire_resource(jit , one_job))
        {
            ULOG_ERROR(SYS_JOB_LOG , "acquire resource failed\n");
            err_code = G_RESOURCE_FAIL;
            channels_destroy(&one_job->_resource_link);
            if (jit->wait_type)
            {
                _job_abnormal_change(job_id , one_job);
                _free_one_job(one_job);
            }
            else
            {
                _job_reinput_queue(one_job);
            }
            goto END;
        }
        one_job->_cur_state++;
    }

    if (one_job->_cur_state == ACQUIRE_PTMEM_STATE)
    {
        if (_acquire_ptmem(jit , one_job))
        {
            ULOG_ERROR(SYS_JOB_LOG , "acquire ptmem failed\n");
            err_code = G_MEMORY_FAIL;
            if (jit->wait_type)
            {
                _job_abnormal_change(job_id , one_job);
                _free_one_job(one_job);
            }
            else
            {
                _job_reinput_queue(one_job);
            }
            goto END;
        }
        one_job->_cur_state++;
    }

    if (one_job->_cur_state == JUDGE_STATE)
    {
        if (module_ready(one_job->_job_info.obj , s_sys_data._client , &s_sys_data._ssd_health_check) == 0)
        {
            err_code = G_JOB_INVALID;
            _job_abnormal_change(job_id , one_job);
            _free_one_job(one_job);
            goto END;
        }
        if (jit->msgrouter_source.number == 2 && msg->msgSender == (int32_t)channels_head(&jit->msgrouter_source))
        {
            pi_mutex_lock(s_sys_data._mtx);
            int32_t ret = channels_tail_insert(s_sys_data._job_set + JOB_WAITING , job_id , one_job , 0);
            one_job->_forbid_startup = 1;
            pi_mutex_unlock(s_sys_data._mtx);

            if (ret == -1)
            {
                err_code = G_MEMORY_FAIL;
                ULOG_ERROR(SYS_JOB_LOG , "insert channels failed\n");
                if (jit->wait_type)
                {
                    _job_abnormal_change(job_id , one_job);
                    _free_one_job(one_job);
                }
                else
                {
                    _job_reinput_queue(one_job);
                }
            }
            else
            {
                ULOG_WARN(SYS_JOB_LOG , "Wait pc job:%d ..\n" , one_job->_parser_result.job_type);
            }
            goto END;
        }
        if (one_job->_job_info.obj == PRINT_OBJ)
        {
            //当存在正在执行的打印作业时，新打印作业的状态只能为接收中
            pi_mutex_lock(s_sys_data._mtx);
            if (channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , NULL))
            {
                one_job->_job_info.status_detail = JOB_RECEIVING;
            }
            else
            {
                one_job->_job_info.status_detail = JOB_NONE;
            }
            pi_mutex_unlock(s_sys_data._mtx);
        }
        one_job->_cur_state++;
    }
    if (one_job->_cur_state == RESOURCE_START_STATE)
    {
        pi_mutex_lock(s_sys_data._mtx);
        int32_t ret = channels_tail_insert(s_sys_data._job_set + JOB_RUNNING , job_id , one_job , 0);
        pi_mutex_unlock(s_sys_data._mtx);
        if (ret == -1)
        {
            err_code = G_MEMORY_FAIL;
            ULOG_ERROR(SYS_JOB_LOG , "insert channels failed\n");
            if (jit->wait_type)
            {
                _job_abnormal_change(job_id , one_job);
                _free_one_job(one_job);
            }
            else
            {
                _job_reinput_queue(one_job);
            }
            goto END;
        }
        _notify_resource_link(one_job);
        one_job->_cur_state++;
    }

END:
    if (err_code == G_SUCCESS && one_job && one_job->_forbid_startup == 0)
    {
        _notify_job_info(one_job , JOB_RUNNING);
        jobinfo_storage_write(job_id , &one_job->_job_info , sizeof(one_job->_job_info));
    }
    if (notify_callback)
    {
        notify_callback(job_id , err_code , callback_context);
    }
    ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u , err_code:%u\n" , job_id , err_code);
    return err_code;
}

static void _notify_job_suspend_finish(void)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_INTERRUPT_MODE_ACK;;
    send_msg.msg1 = 0;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_SYS_JOB_MGR;
    task_msg_send_by_router(MID_PANEL , &send_msg);

    ULOG_INFO(SYS_JOB_LOG , "all job suspend finish.\n");
}

static void _notify_job_resume_finish(void)
{
    ROUTER_MSG_S send_msg;

    send_msg.msgType = MSG_CTRL_INTERRUPT_RELIEVE_ACK;
    send_msg.msg1 = 0;
    send_msg.msg2 = 0;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_SYS_JOB_MGR;
    task_msg_send_by_router(MID_PANEL , &send_msg);

    ULOG_INFO(SYS_JOB_LOG , "all job resume finish.\n");
    s_sys_data._interrupted_relieve_flag = 0;
}

static void _clear_resource_send_number(struct channels *chs)
{
    struct fragment *cur = chs->head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state = NULL;

    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state)
        {
            one_resource_state->send_number = 0;
        }
        cur = next;
    }
}

static void _consume_resource_number(struct channels *chs)
{
    struct fragment *cur = chs->head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state = NULL;

    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state && one_resource_state->state == HANG_UP)
        {
            struct resource_item *rit = (struct resource_item*)channels_find(
                    s_sys_data._config->config + RESOURCE ,
                    one_resource_state->resource_id);
            rit->number--;
        }
        cur = next;
    }
}

static int32_t _job_cancel_handle(uint32_t job_id , int32_t is_ack , uint32_t msgsender , uint32_t msgtype)
{
    if (job_id == 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id is invalid\n");
        return -1;
    }
    else if (job_id == 0xFFFFFFFF)
    {
        pi_mutex_lock(s_sys_data._mtx);
        ONE_JOB_INFO_P one_job = channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                _match_cancel_source_job ,
                s_sys_data._config + JOB);
        pi_mutex_unlock(s_sys_data._mtx);
        if (one_job)
        {
            ULOG_WARN(SYS_JOB_LOG , "destroy job[%u] by abort\n" , one_job->_job_info.job_id);
            JOB_PRIORITY_E model = _internal_mode_get();

            pi_mutex_lock(s_sys_data._mtx);
            one_job->_job_info.status_detail = JOB_ABORTED;
            _notify_job_info(one_job , JOB_FINISHED);
            channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 1);
            ULOG_INFO(SYS_JOB_LOG , " now model:%d\n" , model);
            if (model == JOB_NORMAL)
            {
                one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                        _match_startup_job , NULL);
            }
            else if (model == JOB_PRIORITIZED || model == JOB_INTERRUPTED)
            {
                one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                        _match_scan_or_copy_job , NULL);
            }
            channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 0);
            if (one_job)
            {
                _job_request_handle(one_job , &one_job->_job_msg , NULL);
            }
            pi_mutex_unlock(s_sys_data._mtx);
            return 0;
        }
    }

    ONE_JOB_INFO_P one_job = NULL;
    int32_t i = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (;i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , job_id);
        if (one_job)
        {
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u nonexistent\n" , job_id);
        return -1;
    }
    if (is_ack == 0 && i != JOB_CANCELING)
    {
        uint32_t old_status_detail = one_job->_job_info.status_detail;

        if (job_id == s_sys_data._ejob.job_id)
        {
            s_sys_data._ejob.cancel_job = 1;
        }
        if (i == JOB_SUSPENDING)
        {
            _consume_resource_number(&one_job->_resource_link);
        }
        if (msgsender)
        {
            if ((msgtype != MSG_CTRL_JOB_ABORT) &&
                (msgsender == MID_PANEL || msgsender == MID_PORT_NET))
            {
                one_job->_job_info.status_detail = JOB_CANCELED;
                ULOG_WARN(SYS_JOB_LOG , "job cancel by user!\n");
            }
            else
            {
                one_job->_job_info.status_detail = JOB_ABORTED;
                ULOG_WARN(SYS_JOB_LOG , "job cancel by abort!\n");
            }
        }
        pi_mutex_lock(s_sys_data._mtx);
        channels_fragment_remove2(s_sys_data._job_set + i , one_job , 0);
        channels_tail_insert(s_sys_data._job_set + JOB_CANCELING , job_id , one_job , 0);
        _notify_job_info(one_job , JOB_CANCELING);

        if (_internal_mode_get() == JOB_INTERRUPTED && 
            i == JOB_SUSPENDING && 
            old_status_detail == JOB_SUSPEND_NONE && 
            !channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , NULL))
        {//NOTE:中断模式下收到面板或者后端模块发起的取消，且当前只有一个打印作业正在挂起中时，须给面板回复SUSPEND_ACK
            _notify_job_suspend_finish();
        }
        else if (s_sys_data._interrupted_relieve_flag == 1 &&
                i == JOB_RUNNING &&
                old_status_detail == JOB_RESUMING && 
                s_sys_data._job_set[JOB_SUSPENDING].number == 0)
        {//NOTE:解除中断过程中收到面板或者后端模块发起的取消，且当前只有一个打印作业正在恢复中时，须给面板回复RESUME_ACK
            _notify_job_resume_finish();
        }
        pi_mutex_unlock(s_sys_data._mtx);
        _clear_resource_send_number(&one_job->_resource_link);
    }
    else if (is_ack == 1 && i != JOB_CANCELING)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u , invalid cancel_ack!\n" , job_id);
        return -1;
    }

    if (i == JOB_WAITING)
    {
        one_job->_job_info.status_detail = JOB_CANCELED;
        pi_mutex_lock(s_sys_data._mtx);
        _notify_job_info(one_job , JOB_FINISHED);
        if (channels_fragment_remove2(s_sys_data._job_set + i , one_job , 1) == -1)
        {
            channels_fragment_remove2(s_sys_data._job_set + JOB_CANCELING , one_job , 1);
        }
        pi_mutex_unlock(s_sys_data._mtx);
        ULOG_INFO(SYS_JOB_LOG , "destroy job[%u]\n" , job_id);
        return 0;
    }

    struct fragment *cur = one_job->_resource_link.head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state = NULL;

    if (is_ack)
    {
        //NOTE:有可能不是按先后顺序接收到对应模块的CANCEL_ACK，
        //如：先CANCEL IPS模块，但是可能先收到IPM模块的RESOURCE FREE(当做CANCEL_ACK)
        //这个时候不能再给IPS模块发送CANCEL的指令
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_find(&one_job->_resource_link , msgsender);
        if (one_resource_state)
        {
            one_resource_state->state = CANCELED;
            ULOG_DEBUG(SYS_JOB_LOG , "receive job_id:%u resource_id:%d cancel_ack\n" ,
                    job_id , one_resource_state->resource_id);
        }
        else
        {
            ULOG_WARN(SYS_JOB_LOG , "%u is not in the queue\n" , msgsender);
            return -1;
        }
    }
    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state && one_resource_state->state != CANCELED && one_resource_state->send_number == 0)
        {
            ROUTER_MSG_S send_msg;

            send_msg.msgType = MSG_CTRL_JOB_CANCEL;
            send_msg.msg1 = one_job->_job_info.job_id;
            send_msg.msg2 = one_resource_state->resource_id;
            send_msg.msg3 = NULL;
            send_msg.msgSender = MID_SYS_JOB_MGR;
            task_msg_send_by_router(one_resource_state->resource_id , &send_msg);
            ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u cancel resource_id:%d\n" ,
                    job_id , one_resource_state->resource_id);
            one_resource_state->send_number++;
            break;
        }
        cur = next;
    }
    return 0;
}

static void _modify_resource_state(struct fragment *cur , RESOURCE_STATE_E value)
{
    struct fragment *next = NULL;
    while(cur)
    {
        ONE_RESOURCE_STATE_P one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state)
        {
            one_resource_state->state = value;
        }
        cur = next;
    }
}

static int32_t _job_resume_handle(uint32_t job_id , uint32_t is_ack , uint32_t msgsender)
{
    if (job_id == 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id is invalid\n");
        return -1;
    }

    ONE_JOB_INFO_P one_job = NULL;
    int32_t i = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (;i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , job_id);
        if (one_job)
        {
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u nonexistent\n" , job_id);
        return -1;
    }
    if ((is_ack == 0 && i != JOB_SUSPENDING) ||
            (is_ack == 1 && i != JOB_RUNNING))
    {
        ULOG_WARN(SYS_JOB_LOG , "job_id:%u queue[%d] pause condition isn't meet!\n" , job_id , i);
        return -1;
    }
    if (i == JOB_SUSPENDING)
    {
        if (_again_acquire_ptmem(one_job) == -1)//恢复作业时，先申请ptmem资源
        {
            ULOG_WARN(SYS_JOB_LOG , "job_id:%u resume failed , request ptmem handle failed\n" , job_id);
            //NOTE:ptmem申请失败后，须将该作业还原回之前挂起的状态
            one_job->_job_info.status_detail = JOB_SUSPENDED;
            _notify_job_info(one_job , JOB_SUSPENDING);
            return -1;
        }
        pi_mutex_lock(s_sys_data._mtx);
        _modify_resource_state(one_job->_resource_link.head , HANG_UP);
        channels_fragment_remove2(s_sys_data._job_set + i , one_job , 0);
        channels_tail_insert(s_sys_data._job_set + JOB_RUNNING , job_id , one_job , 0);
        channels_reversion(&one_job->_resource_link);
        pi_mutex_unlock(s_sys_data._mtx);
        one_job->_job_info.status_detail = JOB_RESUMING;
        _notify_job_info(one_job , JOB_RUNNING);
        _clear_resource_send_number(&one_job->_resource_link);
    }

    struct fragment *cur = one_job->_resource_link.head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state = NULL;
    uint32_t ack_number = 0;

    if (is_ack)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_find(&one_job->_resource_link , msgsender);
        if (one_resource_state)
        {
            one_resource_state->state = USING;
            ULOG_DEBUG(SYS_JOB_LOG , "receive job_id:%u resource_id:%d resume_ack\n" ,
                    job_id , one_resource_state->resource_id);
            struct resource_item *rit = (struct resource_item*)channels_find(
                    s_sys_data._config->config + RESOURCE ,
                    one_resource_state->resource_id);
            rit->number--;
        }
        else
        {
            ULOG_WARN(SYS_JOB_LOG , "%u is not in the queue\n" , msgsender);
            return -1;
        }
    }
    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state && one_resource_state->state == HANG_UP && one_resource_state->send_number == 0)
        {
            ROUTER_MSG_S send_msg;

            send_msg.msgType = MSG_CTRL_JOB_RESUME;
            send_msg.msg1 = one_job->_job_info.job_id;
            send_msg.msg2 = 0;
            send_msg.msg3 = one_resource_state->current_resource_data;
            send_msg.msgSender = MID_SYS_JOB_MGR;
            task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

            ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u resume resource_id:%d\n" ,
                    job_id , one_resource_state->resource_id);
            one_resource_state->send_number++;
            break;
        }
        else if (one_resource_state && one_resource_state->state == USING)
        {
            ack_number++;
        }
        cur = next;
    }
    if (ack_number == one_job->_resource_link.number)
    {
        pi_mutex_lock(s_sys_data._mtx);
        if (!channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , (void*)1))
        {//查找不到正在执行的打印作业时，将当前作业详细状态置为打印中
            one_job->_job_info.status_detail = JOB_NONE;
        }
        else if (one_job->_resource_link.number > 1)
        {//当前作业依赖模块存在parser和print时，将当前作业详细状态置为接收中
            one_job->_job_info.status_detail = JOB_RECEIVING;
        }
        else
        {//否则将当前作业详细状态置为接收完成
            one_job->_job_info.status_detail = JOB_RECEIVED;
        }
        _notify_job_info(one_job , JOB_RUNNING);
        channels_reversion(&one_job->_resource_link);
        one_job = (ONE_JOB_INFO_P)channels_head(s_sys_data._job_set + JOB_SUSPENDING);
        pi_mutex_unlock(s_sys_data._mtx);
        if (one_job)//存在挂起作业时，按先后顺序恢复
        {
            _job_resume_handle(one_job->_job_info.job_id , 0 , 0);
        }
        else
        {
            if (s_sys_data._interrupted_relieve_flag == 1)//中断模式解除下才通知恢复完成
            {
                _notify_job_resume_finish();
            }
            pi_mutex_lock(s_sys_data._mtx);
            one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                    _match_startup_job , NULL);
            channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 0);
            pi_mutex_unlock(s_sys_data._mtx);

            if (one_job)
            {
                _job_request_handle(one_job , &one_job->_job_msg , NULL);
            }
        }
    }

    return 0;
}

static int32_t _job_suspend_handle(uint32_t job_id , uint32_t is_ack , uint32_t msgsender)
{
    if (job_id == 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id is invalid\n");
        return -1;
    }

    ONE_JOB_INFO_P one_job = NULL;
    int32_t i = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (;i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , job_id);
        if (one_job)
        {
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u nonexistent\n" , job_id);
        return -1;
    }
    if ((is_ack == 0 && i != JOB_RUNNING) ||
            (is_ack == 1 && i != JOB_SUSPENDING))
    {
        ULOG_WARN(SYS_JOB_LOG , "job_id:%u queue[%d] pause condition isn't meet!\n" , job_id , i);
        return -1;
    }
    if (i == JOB_RUNNING)
    {
        pi_mutex_lock(s_sys_data._mtx);
        channels_fragment_remove2(s_sys_data._job_set + i , one_job , 0);
        channels_tail_insert(s_sys_data._job_set + JOB_SUSPENDING , job_id , one_job , 0);
        pi_mutex_unlock(s_sys_data._mtx);
        _clear_resource_send_number(&one_job->_resource_link);
        one_job->_job_info.status_detail = JOB_SUSPEND_NONE;
        _notify_job_info(one_job , JOB_SUSPENDING);
    }

    struct fragment *cur = one_job->_resource_link.head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state = NULL;
    uint32_t ack_number = 0;

    if (is_ack)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_find(&one_job->_resource_link , msgsender);
        if (one_resource_state)
        {
            //NOTE:有可能不是按先后顺序接收到对应模块的SUSPEND_ACK，
            //如：先SUSPEND IPS模块，但是可能先收到IPM模块的RESOURCE FREE(当做SUSPEND_ACK)
            //这个时候不能再给IPS模块发送SUSPEND的指令
            one_resource_state->state = HANG_UP;
            ULOG_DEBUG(SYS_JOB_LOG , "receive job_id:%u resource_id:%d suspend_ack\n" ,
                    job_id , one_resource_state->resource_id);
            struct resource_item *rit = (struct resource_item*)channels_find(
                    s_sys_data._config->config + RESOURCE ,
                    one_resource_state->resource_id);
            rit->number++;
        }
        else
        {
            ULOG_WARN(SYS_JOB_LOG , "%u is not in the queue\n" , msgsender);
            return -1;
        }
    }
    while(cur)
    {
        one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
        if (one_resource_state && one_resource_state->state != HANG_UP && one_resource_state->send_number == 0)
        {
            ROUTER_MSG_S send_msg;

            send_msg.msgType = MSG_CTRL_JOB_SUSPEND;
            send_msg.msg1 = one_job->_job_info.job_id;
            send_msg.msg2 = 0;
            send_msg.msg3 = NULL;
            send_msg.msgSender = MID_SYS_JOB_MGR;
            task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

            ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u suspend resource_id:%d\n" ,
                    job_id , one_resource_state->resource_id);
            one_resource_state->send_number++;
            break;
        }
        else if (one_resource_state && one_resource_state->state == HANG_UP)
        {
            ack_number++;
        }
        cur = next;
    }
    if (ack_number == one_job->_resource_link.number)
    {
        pi_mutex_lock(s_sys_data._mtx);
        one_job->_job_info.status_detail = JOB_SUSPENDED;
        _notify_job_info(one_job , JOB_SUSPENDING);
        _release_ptmem(&one_job->_job_resource_data); // 后端模块都回复SUSPEND_ACK后，回收ptmem资源
        one_job->_job_resource_data->image_mem = NULL;
        one_job->_job_resource_data->video_mem = NULL;
        one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING ,
                _internal_mode_get() == JOB_INTERRUPTED? _match_printjob : NULL ,
                NULL);
        pi_mutex_unlock(s_sys_data._mtx);

        if (one_job)
        {
            _job_suspend_handle(one_job->_job_info.job_id , 0 , 0);
        }
        else if (_internal_mode_get() == JOB_PRIORITIZED)
        {
            //优先级模式，之前正在执行的打印作业挂起后，通知优先级作业依赖模块继续执行
            pi_mutex_lock(s_sys_data._mtx);
            one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_priority_printjob , NULL);
            pi_mutex_unlock(s_sys_data._mtx);
            if (one_job)
            {
                one_job->_job_info.status_detail = JOB_NONE;
                _notify_job_info(one_job , JOB_RUNNING);
                _notfiy_resource_job_continue(one_job);
            }
            else
            {//优先级作业不存在，说明已被删除，需要恢复普通模式
                _internal_mode_set(JOB_NORMAL);
                pi_mutex_lock(s_sys_data._mtx);
                one_job = (ONE_JOB_INFO_P)channels_head(s_sys_data._job_set + JOB_SUSPENDING);
                pi_mutex_unlock(s_sys_data._mtx);
                if (one_job)
                {
                    _job_resume_handle(one_job->_job_info.job_id , 0 , 0);
                }
            }
        }
        else if (_internal_mode_get() == JOB_INTERRUPTED)
        {
            _notify_job_suspend_finish();
        }
        if (!one_job)
        {
            pi_mutex_lock(s_sys_data._mtx);
            one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                    _match_startup_job , NULL);
            channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 0);
            pi_mutex_unlock(s_sys_data._mtx);

            if (one_job)
            {
                _job_request_handle(one_job , &one_job->_job_msg , NULL);
            }
        }
    }

    return 0;
}

static int32_t _job_resource_free_handle(uint32_t job_id , uint32_t resource_id)
{
    if (job_id == 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job id invalid\n");
        return -1;
    }

    ONE_JOB_INFO_P one_job = NULL;
    int32_t i = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (; i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , job_id);
        if (one_job)
        {
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u nonexistent\n" , job_id);
        return -1;
    }
    ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u , resource_id:%d\n" , job_id , resource_id);


    ONE_RESOURCE_STATE_P one_resource_state = (ONE_RESOURCE_STATE_P)channels_find(
            &one_job->_resource_link , resource_id);

    if (one_resource_state)
    {
        one_resource_state->current_resource_data->use_resource_number--;
        if (one_resource_state->current_resource_data->use_resource_number == 0)
        {
            if (one_job->_resource_io_id &&
                one_job->_resource_io_id == resource_id)
            {
                one_job->_io_end = 1;
                ULOG_WARN(SYS_JOB_LOG , " resource_io_id:%d end\n" , resource_id);
            }
            //NOTE:某些取消场景下，可能会出现没有等来该模块的CANCEL_ACK，只收到到该模块的RESOURCE FREE，则也认为该模块取消完成
            //    如：复印作业下，当前打印机缺纸，除了打印模块外，其他模块都工作完成，这时候用户点取消作业，就会出现该情况
            if (i == JOB_CANCELING && one_resource_state->state != CANCELED)
            {
               _job_cancel_handle(job_id , 1 , resource_id , 0);
            }
            //挂起作业时，也有可能出现与取消一样的场景
            if (i == JOB_SUSPENDING && one_resource_state->state != HANG_UP)
            {
                _job_suspend_handle(job_id , 1 , resource_id);
            }
            channels_fragment_remove2(&one_job->_resource_link , (void*)one_resource_state , 1);
            ULOG_INFO(SYS_JOB_LOG , "destroy job_id:%u-resource_id:%d\n" , job_id , resource_id);
        }
    }
    if (one_job->_job_resource_data->gqio && one_job->_io_end)
    {
        uint8_t buf[32];
        int32_t ret;

        if (i == JOB_RUNNING && one_job->_job_info.obj == PRINT_OBJ && one_job->_job_info.status_detail == JOB_RECEIVING)
        {
            one_job->_job_info.status_detail = JOB_RECEIVED;
            _notify_job_info(one_job , JOB_RUNNING);
        }

        ret = GQIO_READ((GQIO_S*)one_job->_job_resource_data->gqio, buf, sizeof(buf)/sizeof(buf[0]));
        if (ret > 0)
        {
            GQIO_REWIND((GQIO_S*)one_job->_job_resource_data->gqio, buf , ret);
            ULOG_WARN(SYS_JOB_LOG , "gqio readable..\n");
            one_job->_job_msg.msg1 = 0; //将jobid修改为0，防止引用已经完成的jobid
            _job_request_handle(NULL , &one_job->_job_msg , one_job->_job_resource_data->gqio);
        }
        else
        {
            GQIO_CLOSE((GQIO_S*)one_job->_job_resource_data->gqio);
        }
        one_job->_job_resource_data->gqio = NULL;
    }

    if (one_job->_resource_link.number == 0)
    {
        JOB_PRIORITY_E priority = one_job->_job_info.priority;
        JOB_CONFIG_OBJ_E obj = one_job->_job_info.obj;
        int32_t status_detail = one_job->_job_info.status_detail;

        pi_mutex_lock(s_sys_data._mtx);
        _notify_job_info(one_job , JOB_FINISHED);
        jobinfo_storage_write(job_id , &one_job->_job_info , sizeof(one_job->_job_info));
        channels_fragment_remove2(s_sys_data._job_set + i , one_job , 1);
        pi_mutex_unlock(s_sys_data._mtx);
        ULOG_INFO(SYS_JOB_LOG , "destroy job[%u]\n" , job_id);
        process_status();
        memory_status();
        //NOTE:如果当前模式为优先级模式，则需要自动降为普通模式，且按先后顺序恢复挂起的打印作业
        if (_internal_mode_get() == JOB_PRIORITIZED && priority == JOB_PRIORITIZED)
        {
            _internal_mode_set(JOB_NORMAL);
            ULOG_WARN(SYS_JOB_LOG , "Switch to normal mode automatically.\n");
        }

        one_job = NULL;
        if (_internal_mode_get() == JOB_NORMAL)
        {
            if ((obj == PRINT_OBJ) &&
                (status_detail == JOB_NONE || status_detail == JOB_CANCELED || status_detail == JOB_ABORTED))
            {
                pi_mutex_lock(s_sys_data._mtx);
                one_job = (ONE_JOB_INFO_P)channels_head(s_sys_data._job_set + JOB_SUSPENDING);
                pi_mutex_unlock(s_sys_data._mtx);
                if (one_job)//存在挂起作业时，按先后顺序恢复
                {
                    _job_resume_handle(one_job->_job_info.job_id , 0 , 0);
                    return 0;
                }
                pi_mutex_lock(s_sys_data._mtx);
                one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , (void*)1);
                if (one_job)
                {//存在正在执行的作业时，不能再将接收中或接收完的打印作业修改成工作中
                    _notfiy_resource_job_continue(one_job);
                    one_job = NULL;
                }
                else
                {
                    one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , NULL);
                }
                pi_mutex_unlock(s_sys_data._mtx);
                if (one_job) //普通模式下通知接收中或接收完的打印作业继续打印
                {
                    one_job->_job_info.status_detail = JOB_NONE;
                    _notify_job_info(one_job , JOB_RUNNING);
                    _notfiy_resource_job_continue(one_job);
                    return 0;
                }
            }
            else if (obj == SCAN_OBJ)
            {//NOTE: 可能存在的场景，挂起一个打印作业后，下发扫描作业，然后恢复打印作业，
            //       可能因申请不到ptmem，打印作业一直没法恢复，须等待扫描作业完成才恢复打印作业
                pi_mutex_lock(s_sys_data._mtx);
                one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , NULL);
                if (!one_job)
                {
                    one_job = (ONE_JOB_INFO_P)channels_head(s_sys_data._job_set + JOB_SUSPENDING);
                }
                else
                {
                    one_job = NULL;
                }
                pi_mutex_unlock(s_sys_data._mtx);
                if (one_job)//存在挂起作业时，按先后顺序恢复
                {
                    _job_resume_handle(one_job->_job_info.job_id , 0 , 0);
                    return 0;
                }
            }
        }
    }
    else if (one_job && one_job->_io_end)
    {
        one_job = NULL;
    }
    if (_internal_mode_get() == JOB_INTERRUPTED || !one_job)
    {//等待队列中存在作业时，按先后顺序发起
        pi_mutex_lock(s_sys_data._mtx);
        one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING ,
                _match_startup_job , NULL);
        channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 0);
        pi_mutex_unlock(s_sys_data._mtx);

        if (one_job)
        {
            _job_request_handle(one_job , &one_job->_job_msg , NULL);
        }
    }

    return 0;
}

static int32_t _job_pause_handle(uint32_t job_id , uint32_t is_ack)
{
    if (job_id == 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id is invalid\n");
        return -1;
    }

    ONE_JOB_INFO_P one_job = NULL;
    int32_t i = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (;i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , job_id);
        if (one_job)
        {
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u nonexistent\n" , job_id);
        return -1;
    }
    if ((is_ack == 0 && i != JOB_RUNNING) ||
        (is_ack == 1 && i != JOB_PAUSING))
    {
        ULOG_WARN(SYS_JOB_LOG , "job_id:%u queue[%d] pause condition isn't meet!\n" , job_id , i);
        return -1;
    }
    if (i == JOB_RUNNING)
    {
        pi_mutex_lock(s_sys_data._mtx);
        channels_fragment_remove2(s_sys_data._job_set + i , one_job , 0);
        channels_tail_insert(s_sys_data._job_set + JOB_PAUSING , job_id , one_job , 0);
        one_job->_job_info.status_detail = JOB_PAUSE_NONE;
        _notify_job_info(one_job , JOB_PAUSING);
        pi_mutex_unlock(s_sys_data._mtx);
    }

    struct fragment *cur = one_job->_resource_link.head , *next = NULL;
    ONE_RESOURCE_STATE_P one_resource_state = NULL;

    if (is_ack)
    {
        while(cur)
        {
            one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
            if (one_resource_state && one_resource_state->state != STOP)
            {
                one_resource_state->state = STOP;
                ULOG_DEBUG(SYS_JOB_LOG , "receive job_id:%u resource_id:%d pause_ack\n" ,
                        job_id , one_resource_state->resource_id);

                one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(next , NULL);
                if (one_resource_state)
                {
                    ROUTER_MSG_S send_msg;

                    send_msg.msgType = MSG_CTRL_JOB_PAUSE;
                    send_msg.msg1 = one_job->_job_info.job_id;
                    send_msg.msg2 = 0;
                    send_msg.msg3 = NULL;
                    send_msg.msgSender = MID_SYS_JOB_MGR;
                    task_msg_send_by_router(one_resource_state->resource_id , &send_msg);
                    ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u pause resource_id:%d\n" ,
                            job_id , one_resource_state->resource_id);
                }
                break;
            }
            cur = next;
        }
        if (!next)
        {
            /*pi_mutex_lock(s_sys_data._mtx);*/
            one_job->_job_info.status_detail = JOB_PAUSED;
            _notify_job_info(one_job , JOB_PAUSING);
            /*pi_mutex_unlock(s_sys_data._mtx);*/
        }
    }
    else
    {
        while(cur)
        {
            one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
            if (one_resource_state && one_resource_state->state != STOP)
            {
                ROUTER_MSG_S send_msg;

                send_msg.msgType = MSG_CTRL_JOB_PAUSE;
                send_msg.msg1 = one_job->_job_info.job_id;
                send_msg.msg2 = 0;
                send_msg.msg3 = NULL;
                send_msg.msgSender = MID_SYS_JOB_MGR;
                task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

                ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u pause resource_id:%d\n" ,
                        job_id , one_resource_state->resource_id);
                break;
            }
            cur = next;
        }
    }

    return 0;
}

static int32_t _job_restart_handle(uint32_t job_id , uint32_t is_ack)
{
    if (job_id == 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id is invalid\n");
        return -1;
    }

    ONE_JOB_INFO_P one_job = NULL;
    int32_t i = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (;i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , job_id);
        if (one_job)
        {
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "job_id:%u nonexistent\n" , job_id);
        return -1;
    }
    if ((is_ack == 0 && i != JOB_PAUSING) ||
        (is_ack == 1 && i != JOB_RUNNING))
    {
        ULOG_WARN(SYS_JOB_LOG , "job_id:%u queue[%d] pause condition isn't meet!\n" , job_id , i);
        return -1;
    }
    if (i == JOB_PAUSING)
    {
        pi_mutex_lock(s_sys_data._mtx);
        channels_fragment_remove2(s_sys_data._job_set + i , one_job , 0);
        channels_tail_insert(s_sys_data._job_set + JOB_RUNNING , job_id , one_job , 0);
        channels_reversion(&one_job->_resource_link);
        pi_mutex_unlock(s_sys_data._mtx);
        one_job->_job_info.status_detail = JOB_RESTARTING;
        _notify_job_info(one_job , JOB_RUNNING);
    }

    struct fragment *cur = one_job->_resource_link.head , *next = NULL;

    if (is_ack)
    {
        while(cur)
        {
            ONE_RESOURCE_STATE_P one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
            if (one_resource_state && one_resource_state->state == STOP)
            {
                one_resource_state->state = USING;
                ULOG_DEBUG(SYS_JOB_LOG , "receive job_id:%u resource_id:%d restart_ack\n" ,
                        job_id , one_resource_state->resource_id);

                one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(next , NULL);
                if (one_resource_state)
                {
                    ROUTER_MSG_S send_msg;

                    send_msg.msgType = MSG_CTRL_JOB_RESTART;
                    send_msg.msg1 = one_job->_job_info.job_id;
                    send_msg.msg2 = 0;
                    send_msg.msg3 = NULL;
                    send_msg.msgSender = MID_SYS_JOB_MGR;
                    task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

                    ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u restart resource_id:%d\n" ,
                            job_id , one_resource_state->resource_id);
                }
                break;
            }
            cur = next;
        }
        if (!next)
        {
            /*pi_mutex_lock(s_sys_data._mtx);*/
            one_job->_job_info.status_detail = JOB_NONE;
            _notify_job_info(one_job , JOB_RUNNING);
            /*pi_mutex_unlock(s_sys_data._mtx);*/
            channels_reversion(&one_job->_resource_link);
        }
    }
    else
    {
        while(cur)
        {
            ONE_RESOURCE_STATE_P one_resource_state = (ONE_RESOURCE_STATE_P)channels_iterator(cur , &next);
            if (one_resource_state && one_resource_state->state == STOP)
            {
                ROUTER_MSG_S send_msg;

                send_msg.msgType = MSG_CTRL_JOB_RESTART;
                send_msg.msg1 = one_job->_job_info.job_id;
                send_msg.msg2 = 0;
                send_msg.msg3 = NULL;
                send_msg.msgSender = MID_SYS_JOB_MGR;
                task_msg_send_by_router(one_resource_state->resource_id , &send_msg);

                ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u restart resource_id:%d\n" ,
                        job_id , one_resource_state->resource_id);
                break;
            }
            cur = next;
        }
    }

    return 0;
}

static void _job_priority_handle(uint32_t jobid)
{
    pi_mutex_lock(s_sys_data._mtx);
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + JOB_RUNNING , jobid);
    pi_mutex_unlock(s_sys_data._mtx);

    if (!one_job)
    {
        ULOG_ERROR(SYS_JOB_LOG , "jobid:%u not found\n" , jobid);
        return;
    }

    JOB_REQUEST_S *job_request = (JOB_REQUEST_S *)one_job->_job_msg.msg3;
    void *callback_context = job_request->context;
    void (*notify_callback)(uint32_t jobid , int result , void *context) = job_request->notify;

    if (_internal_mode_get() != JOB_NORMAL)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for job[%u].\n" , _internal_mode_get() , jobid);
        if (notify_callback)
        {
            notify_callback(jobid , G_PRIORITY_LIMIT , callback_context);
        }
        return;
    }
    _internal_mode_set(JOB_PRIORITIZED);
    one_job->_job_info.priority = JOB_PRIORITIZED;
    ULOG_WARN(SYS_JOB_LOG , "Switch to priority mode. jobid:%u\n" , jobid);

    pi_mutex_lock(s_sys_data._mtx);
    channels_fragment_remove2(s_sys_data._job_set + JOB_RUNNING , one_job , 0);
    channels_head_insert(s_sys_data._job_set + JOB_RUNNING , one_job->_job_info.job_id , one_job , 0);
    one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , (void*)1);
    pi_mutex_unlock(s_sys_data._mtx);

    if (one_job)
    {
        _job_suspend_handle(one_job->_job_info.job_id , 0 , 0);
    }
}

static void _interrupt_mode_set(void)
{
    if (_internal_mode_get() > JOB_INTERRUPTED)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for interrupt mode.\n" , _internal_mode_get());
        return ;
    }
    else
    {
        _internal_mode_set(JOB_INTERRUPTED);
        ULOG_WARN(SYS_JOB_LOG , "Currently in interrupt mode.\n");
    }

    pi_mutex_lock(s_sys_data._mtx);
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING , _match_printjob , (void*)1);
    if  (!one_job)
    {
        one_job = (ONE_JOB_INFO_P)channels_head(s_sys_data._job_set + JOB_RUNNING);
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (one_job)
    {
        _job_suspend_handle(one_job->_job_info.job_id , 0 , 0);
    }
    else
    {
        _notify_job_suspend_finish();
    }
}

static void _interrupt_mode_relieve_handle(void)
{
    if (_internal_mode_get() > JOB_INTERRUPTED)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for interrupt relieve.\n" , _internal_mode_get());
        return ;
    }
    s_sys_data._interrupted_relieve_flag = 1;
    pi_mutex_lock(s_sys_data._mtx);
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_SUSPENDING , _match_priority_printjob  , NULL);
    pi_mutex_unlock(s_sys_data._mtx);
    if (one_job)
    {
        _internal_mode_set(JOB_PRIORITIZED);
        ULOG_WARN(SYS_JOB_LOG , "Switch to priority mode automatically.\n");
        one_job->_job_info.status_detail = JOB_NONE;
        _notify_job_info(one_job , JOB_RUNNING);
        _job_resume_handle(one_job->_job_info.job_id , 0 , 0);
        return;
    }
    _internal_mode_set(JOB_NORMAL);
    ULOG_WARN(SYS_JOB_LOG , "Switch to normal mode automatically.\n");

    pi_mutex_lock(s_sys_data._mtx);
    one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_SUSPENDING , _match_normal_printjob  , NULL);
    if (one_job)
    {
        pi_mutex_unlock(s_sys_data._mtx);
        one_job->_job_info.status_detail = JOB_NONE;
        _notify_job_info(one_job , JOB_RUNNING);
        _job_resume_handle(one_job->_job_info.job_id , 0 , 0);
    }
    else
    {
        _notify_job_resume_finish();
        one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_WAITING , _match_startup_job  , NULL);
        channels_fragment_remove2(s_sys_data._job_set + JOB_WAITING , one_job , 0);
        pi_mutex_unlock(s_sys_data._mtx);

        if (one_job)
        {
            _job_request_handle(one_job , &one_job->_job_msg , NULL);
        }
    }
}

static void _remove_list_job(uint32_t jobid)
{
    ROUTER_MSG_S send_msg;
    int result = 0;

    result = jobinfo_storage_remove_job_and_file(jobid);
    send_msg.msgType = MSG_CTRL_JOB_REMOVE;
    send_msg.msg1 = jobid;
    send_msg.msg2 = result;
    send_msg.msg3 = NULL;
    send_msg.msgSender = MID_SYS_JOB_MGR;
    if (task_msg_send_by_router(MID_PANEL , &send_msg))
    {
        ULOG_ERROR(SYS_JOB_LOG , "send MID_PANEL failed\n");
    }
    ULOG_DEBUG(SYS_JOB_LOG , "remove job[%u] result:%d\n" , jobid , result);
}

static void* _msgroute_handle(void *args)
{
    (void)args;
    ROUTER_MSG_S msg;

    for (int i = 0; i < 100 && !s_sys_data._emmc_ready; ++i)
    {
        sleep(1);
    }
    jobinfo_storage_init(&s_sys_data.job_id , s_sys_data._client);

    while(!s_sys_data._isexit)
    {
        int32_t ret = task_msg_wait_timeout_by_router(MID_SYS_JOB_MGR , &msg , 5 , 0);
        if (ret > 0)
        {
            pi_mutex_lock(s_sys_data._mtx);
            if (s_sys_data._job_set[JOB_RUNNING].number == 0 &&
                    s_sys_data._job_set[JOB_CANCELING].number == 0 &&
                    s_sys_data._job_set[JOB_SUSPENDING].number == 0 &&
                    s_sys_data._job_set[JOB_PAUSING].number == 0)
            {
                s_sys_data._busy = 0;
                //满足典型能耗机型且是打印作业唤醒和没有错误/故障，也成功打印完，则通知电源管理模块进入节能状态
                if (s_sys_data._ejob.assign_pdt &&
                        s_sys_data._ejob.print_job &&
                        s_sys_data._ejob.energy_saving_rouse &&
                        !s_sys_data._ejob.cancel_job &&
                        !status_manager_check_abnormality(STATUS_ID_MODULE_PRINT))
                {
                    //通知电源管理模块进入节能模式
                    ret = 0x2;
                    pi_event_mgr_notify(s_sys_data._client , EVT_TYPE_POWERMGR_CONFIG , &ret , sizeof(ret));
                }
                s_sys_data._ejob.energy_saving_rouse = 0;
                s_sys_data._ejob.print_job = 0;
                s_sys_data._ejob.cancel_job = 0;
                s_sys_data._ejob.job_id = 0;
            }
            pi_mutex_unlock(s_sys_data._mtx);
            continue;

        }
        else if (ret < 0)
        {
            ULOG_ERROR(SYS_JOB_LOG , "msgroute error\n");
            break;
        }
        pi_mutex_lock(s_sys_data._mtx);
        s_sys_data._busy = 1;
        pi_mutex_unlock(s_sys_data._mtx);
        ULOG_INFO(SYS_JOB_LOG , "source:%d , msgtype:%d\n" , msg.msgSender , msg.msgType);
        switch(msg.msgType)
        {
            case MSG_CTRL_JOB_REQUEST:
                if (msg.msg3 && module_first_ready(&msg , &s_sys_data._print_ready))
                {
                    power_ready(s_sys_data._client , &s_sys_data._ejob.energy_saving_rouse);
                    _job_request_handle(NULL , &msg , NULL);
                }
               break;

            case MSG_CTRL_JOB_ABORT:
            case MSG_CTRL_JOB_CANCEL:
               {
#if CONFIG_SDK_PEDK
                    if (msg.msg1 == 0) //NOTE:针对PEDK项目取消流程的特殊处理
                    {
                        msg.msg1 = _wonum_to_jobid(msg.msg2);
                        ULOG_INFO(SYS_JOB_LOG , "wonum:%u , jobid:%u\n" , msg.msg2 , msg.msg1);
                    }
#endif
                    _job_cancel_handle(msg.msg1 , 0 , msg.msgSender , msg.msgType);
               }
               break;

            case MSG_CTRL_JOB_CANCEL_ACK:
               _job_cancel_handle(msg.msg1 , 1 , msg.msgSender , 0);
               break;

            case MSG_CTRL_JOB_PAUSE:
               _job_pause_handle(msg.msg1 , 0);
               break;

            case MSG_CTRL_JOB_PAUSE_ACK:
               _job_pause_handle(msg.msg1 , 1);
               break;

            case MSG_CTRL_JOB_RESTART:
               _job_restart_handle(msg.msg1 , 0);
               break;

            case MSG_CTRL_JOB_RESTART_ACK:
               _job_restart_handle(msg.msg1 , 1);
               break;

            case MSG_CTRL_JOB_SUSPEND:
               _job_suspend_handle(msg.msg1 , 0 , 0);
               break;

            case MSG_CTRL_JOB_SUSPEND_ACK:
               _job_suspend_handle(msg.msg1 , 1 , msg.msg2);
               break;

            case MSG_CTRL_JOB_RESUME:
               _job_resume_handle(msg.msg1 , 0 , 0);
               break;

            case MSG_CTRL_JOB_RESUME_ACK:
               _job_resume_handle(msg.msg1 , 1 , msg.msg2);
               break;

            case MSG_CTRL_JOB_INSERT:
               _job_priority_handle(msg.msg1);
               break;

            case MSG_CTRL_INTERRUPT_MODE:
               _interrupt_mode_set();
               break;

            case MSG_CTRL_INTERRUPT_RELIEVE:
                _interrupt_mode_relieve_handle();
               break;

            case MSG_CTRL_RESOURCE_FREE:
               _job_resource_free_handle(msg.msg1 , msg.msg2);
               break;
            case MSG_CTRL_JOB_REMOVE:
               _remove_list_job(msg.msg1);
               break;

            default:
               ULOG_ERROR(SYS_JOB_LOG , "Unsupported type\n");
               if (msg.msg3)
               {
                   pi_free(msg.msg3);
               }
               break;
        }

    }
    return NULL;
}

static void _scan_page_handle(PAGEINFO_S *page_info)
{
    ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u\ncopies:%d\ncurrent_pages:%d\ntotal_pages:%d\nscan_source:%d\n" ,
            page_info->job_id , page_info->copies , page_info->current_pages ,
            page_info->total_pages , page_info->scan_source);

    pi_mutex_lock(s_sys_data._mtx);
    int index = JOB_RUNNING;
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + index , page_info->job_id);

    if (!one_job)
    {
        index = JOB_CANCELING;
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + index , page_info->job_id);
    }

    if (one_job)
    {
        if (one_job->_job_info.obj == SCAN_OBJ)
        {
            one_job->_job_info.copies = page_info->copies;
            one_job->_job_info.current_copies = page_info->copies;
            one_job->_job_info.current_pages = page_info->current_pages;
            one_job->_job_info.total_pages = page_info->total_pages;
            /*one_job->_job_info.pre_total_pages = page_info->total_pages;*/
        }
        one_job->_job_info.scan_source = page_info->scan_source;
        if (index == JOB_RUNNING)
        {
            _notify_job_info(one_job , index);
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);
}

static void _param_info_handle(void *data, uint32_t size, uint32_t mid)
{
    if (size == sizeof(PARAMINFO_S))
    {
        PARAMINFO_S *param_info = (PARAMINFO_S *)data;
        ONE_JOB_INFO_P one_job = NULL;

        pi_mutex_lock(s_sys_data._mtx);
        for (int index = 0; index < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++index)
        {
            one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + index , param_info->job_id);
            if (one_job)
            {
                memcpy(&one_job->_job_params, &param_info->job_param, sizeof(one_job->_job_params));
                break;
            }
        }
        pi_mutex_unlock(s_sys_data._mtx);
    }
    else
    {
        ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
    }
}

static void _page_info_handle(void *data , uint32_t size , uint32_t mid)
{
    if (size == sizeof(PAGEINFO_S))
    {
        PAGEINFO_S *page_info = (PAGEINFO_S*)data;
        ONE_JOB_INFO_P one_job = NULL;

        ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u\ncopies:%d\ncurrent_copies:%d\n"
                "current_pages:%d\nprinted_total_pages:%d\ntotal_pages:%d\nscan_source:%d\n" ,
                page_info->job_id , page_info->copies , page_info->current_copies, page_info->current_pages ,
                page_info->printed_total_pages , page_info->total_pages , page_info->scan_source);

        pi_mutex_lock(s_sys_data._mtx);
        for (int index = 0; index < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++index)
        {
            one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + index , page_info->job_id);
            if (one_job)
            {
                one_job->_job_info.current_pages = page_info->current_pages;
                one_job->_job_info.total_pages = page_info->total_pages;
                one_job->_job_info.printed_total_pages = page_info->printed_total_pages;
                one_job->_job_info.copies = page_info->copies;
                one_job->_job_info.current_copies = page_info->current_copies;;
                one_job->_job_info.copies_pre_pages = page_info->copies_pre_pages;
                if ( mid == EVT_MODULE_SCAN)
                {
                    one_job->_job_info.scan_source = page_info->scan_source;
                }
                _notify_job_info(one_job , index);
                break;
            }
        }
        pi_mutex_unlock(s_sys_data._mtx);
    }
    else
    {
        ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
    }
}

static void _attribute_info_handle(void *data , uint32_t size , uint32_t mid)
{
    if (size == sizeof(ATTRIBUTE_INFO_S))
    {
        ATTRIBUTE_INFO_S *attr = (ATTRIBUTE_INFO_S*)data;
        ONE_JOB_INFO_P one_job = NULL;

        ULOG_DEBUG(SYS_JOB_LOG , "job_id:%u , wonum:%u , job_owner:%s , job_name:%s , raster_file_path:%s , prn_file_path:%s\n" ,
                attr->job_id , attr->wonum , attr->job_owner , attr->job_name , attr->raster_file_path , attr->prn_file_path);
        pi_mutex_lock(s_sys_data._mtx);
        for (int index = 0; index < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++index)
        {
            one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + index , attr->job_id);
            if (one_job)
            {
                pi_memcpy(one_job->_job_info.job_owner , attr->job_owner , sizeof(one_job->_job_info.job_owner));
                pi_memcpy(one_job->_job_info.job_name , attr->job_name , sizeof(one_job->_job_info.job_name));
                pi_memcpy(one_job->_job_info.raster_file_path , attr->raster_file_path , sizeof(one_job->_job_info.raster_file_path));
                pi_memcpy(one_job->_job_info.prn_file_path , attr->prn_file_path , sizeof(one_job->_job_info.prn_file_path));
                pi_memcpy(one_job->_job_info.username , attr->username , sizeof(one_job->_job_info.username));
                pi_memcpy(one_job->_job_info.password , attr->password , sizeof(one_job->_job_info.password));
                pi_memcpy(one_job->_job_info.sample_config_args , attr->sample_config_args , sizeof(one_job->_job_info.sample_config_args));
                one_job->_job_info.print_source = attr->print_source;
                one_job->_job_info.error_code = attr->error_code;
                if (one_job->_job_info.timed_task_timestamp == 0)
                {
                    one_job->_job_info.timed_task_timestamp = attr->timed_task_timestamp;
                }
                if (one_job->_job_info.wonum == 0)
                {
                    one_job->_job_info.wonum = attr->wonum;
                }
                _notify_job_info(one_job , index);
                break;
            }
        }
        pi_mutex_unlock(s_sys_data._mtx);
    }
    else
    {
        ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
    }
}

static void _ssd_mount_handle(uint8_t *data)
{
    if (data[0] == STO_MEDIA_TYPE_SSD)
    {
        if (data[1] == 0x1)
        {//ssd插入
            s_sys_data._mount_ssd = 1;
            ULOG_INFO(SYS_JOB_LOG , "-- plug in ssd --\n");
        }
        else if (data[1] == 0x2)
        {//ssd拔出
            s_sys_data._mount_ssd = 0;
            ULOG_INFO(SYS_JOB_LOG , "-- pull out ssd --\n");
        }
    }
}

static void _ssd_health_check_handle(uint8_t *data)
{
    if (*(int*)data == -1)
    {
        s_sys_data._ssd_health_check = 1;
        ULOG_INFO(SYS_JOB_LOG , "-- ssd health check start --\n");
    }
    else if (*(int*)data == 0 || *(int*)data == 1)
    {
        s_sys_data._ssd_health_check = 0;
        ULOG_INFO(SYS_JOB_LOG , "-- ssd health check end --\n");
    }
}

static void _maintenance_entry_handle(uint32_t data)
{
    JOB_PRIORITY_E ret = _internal_mode_get();

    if (data == 0x1)
    {
        if (ret == JOB_NORMAL)
        {
            _internal_mode_set(JOB_MAINTAINED);
            ULOG_DEBUG(SYS_JOB_LOG , "set current maintained mode.\n");
        }
        else
        {
            ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for maintained mode.\n" , ret);
        }
    }
    else
    {
        ULOG_WARN(SYS_JOB_LOG , "data:%u not support!\n" , data);
    }
}

static void _job_event_callback(const EVT_MSG_S *msg, void *ctx)
{
    (void)ctx;
    switch(msg->event_type)
    {
        case EVT_TYPE_SCAN_STATUS_NOTFFY_MODIFY:
            {
                if (msg->data_length != sizeof(PAGEINFO_S))
                {
                    ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
                }
                else
                {
                    _scan_page_handle(msg->data);
                }
            }
            break;
        case EVT_TYPE_SYSTEMJOB_ATTRIBUTE_INFO:
            _attribute_info_handle(msg->data , msg->data_length , msg->module_id);
            break;
        case EVT_TYPE_SYSTEMJOB_PARAM_INFO:
            _param_info_handle(msg->data, msg->data_length, msg->module_id);
            break;
        case EVT_TYPE_SYSTEMJOB_PAGES_INFO:
            _page_info_handle(msg->data , msg->data_length , msg->module_id);
            break;
        case EVT_TYPE_PLATFORM_PDT_NAME_MODIFY:
            if (pi_strcmp(msg->data , "BM605ADN") == 0 ||
                pi_strcmp(msg->data , "M9708DN") == 0 ||
                pi_strcmp(msg->data , "M9108DN") == 0 ||
                pi_strcmp(msg->data , "M9108DW") == 0)
            {
                s_sys_data._ejob.assign_pdt = 1;
                ULOG_WARN(SYS_JOB_LOG , "Meet ejob model!\n");
            };
            break;
        case EVT_TYPE_STORAGEMGR_PUSH_EMMC_MOUNT_POINT:
            {
                s_sys_data._emmc_ready = 1;
                ULOG_INFO(SYS_JOB_LOG , "emmc mount point ready!\n");
            }
            break;
        case EVT_TYPE_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED:
            {
                if (msg->data_length != sizeof(uint32_t))
                {
                    ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
                }
                else
                {
                    _ssd_mount_handle(msg->data);
                }
            }
            break;
        case EVT_TYPE_PANEL_ENTER_MAINTENANCE_MODIFY :
            {
                if (msg->data_length != sizeof(uint32_t))
                {
                    ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
                }
                else
                {
                    _maintenance_entry_handle(*(uint32_t*)msg->data);
                }
                break;
            }
        case EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_MODIFY:
            {
                if (msg->data_length != sizeof(uint32_t))
                {
                    ULOG_ERROR(SYS_JOB_LOG , "The length isn't satisfied.\n");
                }
                else
                {
                    _ssd_health_check_handle(msg->data);
                }
                break;
            }
        case EVT_TYPE_PRINT_GET_ENGINE_READY_MODIFY:
            {
                if (s_sys_data._print_ready == 0)
                {
                    s_sys_data._print_ready = 1;
                    ULOG_WARN(SYS_JOB_LOG , "print engine ready!\n");
                }
            }
            break;
        default:
            ULOG_ERROR(SYS_JOB_LOG , "Unknow type:%x\n" , msg->event_type);
            break;
    }
}

int32_t job_manager_prolog(void)
{
    int32_t ret = 0;

    pre_parser_prolog();
    ulog_register(&s_log);
    memory_reference_prolog();

    for (int32_t i = 0;
            i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);
            ++i)
    {
        channels_init(&s_sys_data._job_set[i] , NULL , _free_one_job);
    }

    s_sys_data._config = resource_init(CONFIG_FILE);
    if (!s_sys_data._config)
    {
        ULOG_ERROR(SYS_JOB_LOG , "resource init failed\n");
        return -1;
    }

    s_sys_data._client = pi_event_mgr_create_client(EVT_MODULE_SYSTEMJOB , _job_event_callback , NULL , &ret);
    if (!s_sys_data._client)
    {
        ULOG_ERROR(SYS_JOB_LOG , "create event client failed , ret:%d\n" , ret);
    }

    uint32_t event_array[] = {
        EVT_TYPE_SYSTEMJOB_PAGES_INFO,
        EVT_TYPE_SYSTEMJOB_ATTRIBUTE_INFO,
        EVT_TYPE_SYSTEMJOB_PARAM_INFO,
        EVT_TYPE_SCAN_STATUS_NOTFFY_MODIFY,
        EVT_TYPE_PLATFORM_PDT_NAME_MODIFY,
        EVT_TYPE_STORAGEMGR_PUSH_EMMC_MOUNT_POINT,
        EVT_TYPE_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED,
        EVT_TYPE_PANEL_ENTER_MAINTENANCE_MODIFY,
        EVT_TYPE_STORAGEMGR_SATASSD1_HEALTHCHECK_MODIFY,
        EVT_TYPE_PRINT_GET_ENGINE_READY_MODIFY
    };

    ret = pi_event_mgr_register(s_sys_data._client ,
            event_array ,
            sizeof(event_array)/sizeof(event_array[0]));
    if (ret)
    {
        ULOG_ERROR(SYS_JOB_LOG , "register event failed , ret:%d\n" , ret);
    }

    s_sys_data._mtx = pi_mutex_create();
    if (s_sys_data._mtx == INVALIDMTX)
    {
        ULOG_ERROR(SYS_JOB_LOG , "create mutex faild\n");
        resource_destroy(s_sys_data._config);
        return -1;
    }
    s_sys_data._mtx_mode = pi_mutex_create();
    if (s_sys_data._mtx_mode == INVALIDMTX)
    {
        ULOG_ERROR(SYS_JOB_LOG , "create mutex faild\n");
        pi_mutex_destroy(s_sys_data._mtx);
        resource_destroy(s_sys_data._config);
        return -1;
    }

    if (msg_router_register(MID_SYS_JOB_MGR) < 0)
    {
        ULOG_ERROR(SYS_JOB_LOG , "msgrouter register MID:%d failed\n" , MID_SYS_JOB_MGR);
    }

    s_sys_data._isexit = 0;
    pi_memset(&s_sys_data._ejob , 0 , sizeof(s_sys_data._ejob));

    s_sys_data._th = pi_thread_create(_msgroute_handle , PI_NORMAL_STACK , NULL , PI_LEVEL_PRIORITY, NULL , "job_manager");
    if (s_sys_data._th == INVALIDTHREAD)
    {
        ULOG_ERROR(SYS_JOB_LOG , "create thread failed\n");
        pi_mutex_destroy(s_sys_data._mtx);
        pi_mutex_destroy(s_sys_data._mtx_mode);
        resource_destroy(s_sys_data._config);
        return -1;
    }

#if CONFIG_SDK_PEDK
    /* PEDK API处理函数注册 */
    pedk_jobctl_init();
#endif

    s_sys_data.job_id = 1;
    s_sys_data._isexit = 0;
    pi_memset(&s_sys_data._ejob , 0 , sizeof(s_sys_data._ejob));

    return 0;
}

void job_manager_epilog(void)
{
    s_sys_data._isexit = 1;

#if CONFIG_SDK_PEDK
    pedk_jobctl_deinit();
#endif

    msg_router_unregister(MID_SYS_JOB_MGR);

    if (s_sys_data._th != INVALIDTHREAD)
    {
        pi_thread_destroy(s_sys_data._th);
        s_sys_data._th = INVALIDTHREAD;
    }

    if (s_sys_data._mtx != INVALIDMTX)
    {
        pi_mutex_destroy(s_sys_data._mtx);
        s_sys_data._mtx = INVALIDMTX;
    }
    if (s_sys_data._mtx_mode != INVALIDMTX)
    {
        pi_mutex_destroy(s_sys_data._mtx_mode);
        s_sys_data._mtx_mode = INVALIDMTX;
    }

    if (s_sys_data._client)
    {
        pi_event_mgr_destroy_client(s_sys_data._client);
        s_sys_data._client = NULL;
    }

    if (s_sys_data._config)
    {
        resource_destroy(s_sys_data._config);
        s_sys_data._config = NULL;
    }

    for (int32_t i = 0;
            i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]);
            ++i)
    {
        channels_destroy(&s_sys_data._job_set[i]);
    }

    pre_parser_epilog();
    ulog_unregister(s_log.name);
    memory_reference_epilog();
}

int32_t job_manager_busy(void)
{
    pi_mutex_lock(s_sys_data._mtx);
    int32_t ret = s_sys_data._busy;
    pi_mutex_unlock(s_sys_data._mtx);

    return ret;
}

int32_t job_manager_has_running_job(void)
{
    pi_mutex_lock(s_sys_data._mtx);
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)channels_find_match(s_sys_data._job_set + JOB_RUNNING ,
            _match_noacl_config , NULL);
    pi_mutex_unlock(s_sys_data._mtx);

    return one_job ? 1 : 0;
}

int32_t job_manager_set_upgrade_mode(void)
{
    int32_t ret = job_manager_has_running_job();

    if (ret)
    {
        ULOG_WARN(SYS_JOB_LOG , "Current job exists!\n");
        return -1;
    }
    ret = _internal_mode_get();
    if (ret > JOB_UPGRADE)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for upgrade mode.\n" , ret);
        return -1;
    }
    _internal_mode_set(JOB_UPGRADE);
    return 0;
}

int32_t job_manager_clear_upgrade_mode(void)
{
    int32_t ret = _internal_mode_get();

    if (ret != JOB_UPGRADE)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for clear upgrade mode.\n" , ret);
        return -1;
    }
    _internal_mode_set(JOB_NORMAL);
    return 0;
}

int32_t job_manager_set_silent_mode(void)
{
    int32_t ret = job_manager_has_running_job();

    if (ret)
    {
        ULOG_WARN(SYS_JOB_LOG , "Current job exists!\n");
        return -1;
    }
    ret = _internal_mode_get();
    if (ret >= JOB_UPGRADE)
    {
        ULOG_WARN(SYS_JOB_LOG , "current mode:%d, does not meet the requirements for silent mode.\n" , ret);
        return -1;
    }
    _internal_mode_set(JOB_SILENT);
    return 0;
}

uint32_t job_manager_apply_jobid(IO_VIA_E iovia , uint32_t wonum)
{
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)pi_malloc(sizeof(ONE_JOB_INFO_S));
    if (!one_job)
    {
        return 0;
    }
    pi_memset(one_job , 0 , sizeof(ONE_JOB_INFO_S));

    uint32_t job_id = _get_job_id();
    one_job->_cur_state = ALLOC_STATE;
    one_job->_forbid_startup = 1;
    one_job->_job_info.wonum = wonum;
    one_job->_job_info.job_id = job_id;
    one_job->_job_info.io_via = iovia;
    one_job->_job_info.status = JOB_WAITING;

    pi_mutex_lock(s_sys_data._mtx);
    int32_t ret = channels_tail_insert(s_sys_data._job_set + JOB_WAITING , job_id , one_job , 0);
    pi_mutex_unlock(s_sys_data._mtx);
    if (ret)
    {
        pi_free(one_job);
        ULOG_ERROR(SYS_JOB_LOG , "channels insert failed\n");
        return 0;
    }
    ULOG_DEBUG(SYS_JOB_LOG , "create job[%u-%u]:%d\n" , job_id , wonum , iovia);
    return job_id;
}

int32_t job_manager_find_job(uint32_t jobid , JOBINFO_S *jobinfo , uint8_t history_valid)
{
    ONE_JOB_INFO_P one_job = NULL;

    pi_mutex_lock(s_sys_data._mtx);
    for (int32_t i = 0; i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++i)
    {
        one_job = (ONE_JOB_INFO_P)channels_find(s_sys_data._job_set + i , jobid);
        if (one_job)
        {
            if (jobinfo)
            {
                memcpy(jobinfo , &one_job->_job_info , sizeof(JOBINFO_S));
            }
            break;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    if (one_job)
    {
        return 0;
    }
    else if (history_valid)
    {
        return jobinfo_storage_read(jobid , jobinfo , sizeof(JOBINFO_S));
    }

    return -1;
}

uint32_t job_manager_get_active_joblist(void *context , void(*callback)(const JOBINFO_S* job , void *context))
{
    int32_t number = 0;

    pi_mutex_lock(s_sys_data._mtx);
    for (int32_t i = 0; i < sizeof(s_sys_data._job_set)/sizeof(s_sys_data._job_set[0]); ++i)
    {
        struct fragment *cur = s_sys_data._job_set[i].head , *next = NULL;

        while(cur)
        {
            ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)channels_iterator(cur , &next);

            if (one_job)
            {
                if (callback)
                {
                    callback(&one_job->_job_info , context);
                }
                ++number;
            }
            cur = next;
        }
    }
    pi_mutex_unlock(s_sys_data._mtx);

    return number;
}

uint32_t job_manager_get_history_joblist(void *context , int(*callback)(const JOBINFO_S* job , void *context, uint32_t size))
{
    return jobinfo_storage_get_history_records(context , callback);
}

void job_manager_clear_history_joblist(void)
{
    jobinfo_storage_clear_history_records();
}

uint32_t job_manager_get_specify_joblist(JOB_TYPE_E job_type , void *context , void(*callback)(const JOBINFO_S* job , void *context))
{
    enum QUEUE_TYPE type;

    if (job_type == JOB_TYPE_PRINT_DELAY)
    {
        type = T_DELAY;
    }
    else if (job_type == JOB_TYPE_PRINT_SAMPLE)
    {
        type = T_SAMPLE;
    }
    else if (job_type == JOB_TYPE_PRINT_PINCODE)
    {
        type = T_PINCODE;
    }
    else
    {
        ULOG_ERROR(SYS_JOB_LOG , "Unsupported job type[%d].\n" , job_type)
        return 0;
    }

    return jobinfo_storage_get_queue_records(type , context , callback);
}

void job_manager_remove_specify_joblist(JOB_TYPE_E job_type)
{
    enum QUEUE_TYPE type;

    if (job_type == JOB_TYPE_PRINT_DELAY)
    {
        type = T_DELAY;
    }
    else if (job_type == JOB_TYPE_PRINT_SAMPLE)
    {
        type = T_SAMPLE;
    }
    else if (job_type == JOB_TYPE_PRINT_PINCODE)
    {
        type = T_PINCODE;
    }
    else
    {
        ULOG_ERROR(SYS_JOB_LOG , "Unsupported job type[%d].\n" , job_type)
        return;
    }

    jobinfo_storage_remove_queue_records(type);
}

/**
 * @}
 */
