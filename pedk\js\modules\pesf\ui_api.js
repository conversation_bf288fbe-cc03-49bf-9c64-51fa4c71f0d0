var ui_Module_Main = globalThis.pedk.Module_Main
var ui_Module_Sub = globalThis.pedk.Module_Sub
var ERROR_NO = globalThis.pedk.common.ERROR_NO
var NATIVE_WINDOW = globalThis.pedk.common.NATIVE_WINDOW
const JS_KEY_ID = 
{
    JS_KEY_ID_POWER :	0	,
    JS_KEY_ID_TONER_SAVE :	1	,
    JS_KEY_ID_WIFI :	2	,
    JS_KEY_ID_CANCEL :	3	,
    JS_KEY_ID_UP :	4	,
    JS_KEY_ID_DOWN :	5	,
    JS_KEY_ID_MENU :	6	,
    JS_KEY_ID_RETURN :	7	,
    J<PERSON>_KEY_ID_OK :	8	,
    JS_KEY_ID_HOME :	9	,
    JS_KEY_ID_HELP :	10	,
    JS_KEY_ID_1 :	11	,
    JS_KEY_ID_2 :	12	,
    J<PERSON>_<PERSON>EY_ID_3 :	13	,
    J<PERSON>_<PERSON>EY_ID_4 :	14	,
    <PERSON><PERSON>_<PERSON>EY_ID_5 :	15	,
    J<PERSON>_<PERSON><PERSON><PERSON>_ID_6 :	16	,
    J<PERSON>_<PERSON><PERSON>Y_ID_7 :	17	,
    J<PERSON>_<PERSON><PERSON>Y_ID_8 :	18	,
    J<PERSON>_<PERSON>EY_ID_9 :	19	,
    JS_KEY_ID_0 :	20	,
    JS_KEY_ID_STAR :  	21	,
    JS_KEY_ID_POUND :  	22	,
    JS_KEY_ID_DELETE_LEY:	23	,
    JS_KEY_ID_PAUSE :	24	,
    JS_KEY_ID_FRESH :	25	,
    JS_KEY_ID_ADD :        	26	,
    JS_KEY_ID_SUBTRAUT :   	27	,
    JS_KEY_ID_COPY :	28	,
    JS_KEY_ID_IDCOPY :	29	,
    JS_KEY_ID_BILL :	30	,
    JS_KEY_ID_SCAN :	31	,
    JS_KEY_ID_POWER_SAVE :	32	,
    JS_KEY_ID_START :	33	,
};
const JS_KEY_TYPE = 
{
    JS_KEY_TYPE_SHORT_PRESS:0,
    JS_KEY_TYPE_SHORT_RELEASE:1,
    JS_KEY_TYPE_LONG_PRESS:2,
    JS_KEY_TYPE_LONG_PRESS_HOLD:3,
    JS_KEY_TYPE_LONG_RELEASE:4,
}

const JS_LED_ID = 
{
    JS_LED_ID_POWER: 0,
    JS_LED_ID_SAVE_TONER: 1,
    JS_LED_ID_WIFI:2,
    JS_LED_ID_STATUS:3,
    JS_LED_ID_COPY:4,
    JS_LED_ID_ID_COPY:5,
    JS_LED_ID_BILL_COPY:6,
    JS_LED_ID_SCAN:7,
    JS_LED_ID_SAVE_POWER:8,
};

const JS_LED_COLOR = 
{
    JS_LED_COLOR_UNDEFINED:0,
    JS_LED_COLOR_GREEN:1,
    JS_LED_COLOR_RED:2,
    JS_LED_COLOR_ORANGE:3,
    JS_LED_COLOR_BLUE:4,
    JS_LED_COLOR_YELLOW:5,
    JS_LED_COLOR_WHITE:6,
}
function strToJson(str)
{
	var json = eval("(" + str + ")");
	return json;
}

let counter = 0;
function createUniqueID( type )
{
    counter++;
    return type + '_' + counter.toString();
}

class Widget
{
	draw()
	{

	}
}
class StyleSheet
{

    constructor(){
        this.text_color = "0x000000";
        this.text_align = "center";
        this.text_direction = "";
		this.font_size = "WIDGET_FONT_SIZE_DEFAULT"
        this.bg_color   = "0xFFFFFF";
    }
}

class ImageData
{

    constructor(){
        this.img_res = "";
        this.img_format = "bmp";
    }
}

class Keyboard extends Widget
{

	constructor(){
		super()
        this.id = createUniqueID("Keyboard");
        //this.type = "panel";
        //this.x = 0;
        //this.y = 0;
        //this.w = 0;
        //this.h = 0;
		this.cb_text_finished = null;
		this.cb_text_cancel = null;
    }
	#type = 'keyboard'
	get type()
    {
        return this.#type
    }
    set type( val )
    {
        return console.log('Cannot set property type of #<keyboard> which has only a getter')
    }
	draw()
	{
		//console.log('keyboard id is ' , this.id)
		//js_keyboard_draw(this.id,this.type);
	}

}

class OptionItem extends Widget
{
    constructor() {
		super()
        this.id = createUniqueID("OptionItem");
        this.height = 30;
        this.item_status = 0;
        this.style_sheet = new StyleSheet();
        this.text = "";
        this.img = new ImageData();
    }   
    #type = 'optionitem'
    get type() 
     {
         return this.#type
    }
    set type( val ) 
    {
        return console.log('Cannot set property type of #<label> which has only a getter')
    }
    

	draw()
	{
		 //js_label_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.style_sheet.text_color,this.style_sheet.text_align,this.style_sheet.text_direction,this.text);
	}

}

class Label extends Widget
{

    constructor() {
		super()
        this.id = createUniqueID("label");
        //this.type = "label";
        this.x = 0;
        this.y = 0;
        this.w = 50;
        this.h = 30;
        this.style_sheet = new StyleSheet();
        this.text = "";

    }   
    #type = 'label'
    get type() 
     {
         return this.#type
    }
    set type( val ) 
    {
        return console.log('Cannot set property type of #<label> which has only a getter')
    }
    

	draw()
	{

		 //js_label_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.style_sheet.text_color,this.style_sheet.text_align,this.style_sheet.text_direction,this.text);
	}

}

class Button extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("button");
       this.x = 0;
       this.y = 0;
       this.w = 0;
       this.h = 0;
       this.style_sheet = new StyleSheet();
       this.style_sheet.bg_color = "0xa4a5a8";
       this.is_valid = true;
       this.text = "";
       this.is_checked = false;
       this.imgs = [];
       this.cb_pressed = null;
       this.cb_released = null;

   }
   #type = 'button'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<button> which has only a getter')
   }
   
   draw()
   {
   		console.log('button id is ', this.id)
		console.log('button id type is ', this.type)
		//js_button_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.style_sheet.text_color,this.style_sheet.text_align,this.style_sheet.text_direction,this.is_valid,this.text, this.is_checked,this.imgs[0].img_res,this.imgs[0].img_format,this.imgs[1].img_res,this.imgs[1].img_format,this.imgs[2].img_res,this.imgs[2].img_format);
   }
}

class Screen extends Widget
{

	constructor(){
		super()
        this.id = createUniqueID("screen");
        this.x = 0;
        this.y = 0;
        this.w = 1024;
        this.h = 600;
        this.style_sheet = new StyleSheet();

    }
    #type = 'screen'
    get type() 
     {
         return this.#type
    }
    set type( val ) 
    {
        return console.log('Cannot set property type of #<screen> which has only a getter')
    }
	draw()
	{
		console.log('screen id is ' , this.id)
		console.log('screen id type is ' , this.type)
		//js_screen_draw(this.id,this.type,this.x,this.y,this.w,this.h);
	}

}
class Panel extends Widget
{

	constructor(){
		super()
        this.id = createUniqueID("panel");
        this.x = 0;
        this.y = 0;
        this.w = 50;
        this.h = 50;
        this.style_sheet = new StyleSheet();

    }
    #type = 'panel'
    get type() 
     {
         return this.#type
    }
    set type( val ) 
    {
        return console.log('Cannot set property type of #<panel> which has only a getter')
    }
	draw()
	{
		console.log('panel id is ' , this.id)
		console.log('panel id type is ' , this.type)
		//js_screen_draw(this.id,this.type,this.x,this.y,this.w,this.h);
	}

}
class Point {

    constructor(x, y) {
        if(typeof x !== 'number' || typeof y !== 'number' || !Number.isInteger(x) || !Number.isInteger(y))
        {
            throw("EINVALIDPARAM");
        }
        this.x = x;
        this.y = y;
    }
}

class ScreenInfo {
    constructor() {
        this.resolution = new Point(1024, 600);//TODO 屏幕分辨率根据机型变化？4020机型：480*320
        this.switch = true;
        this.brightness = 50;
        this.widget = [];
    }
}

class ScreenCtrl{

    //#app = undefined
    #screenInfo = new ScreenInfo
    #listeners

    constructor()
    {
		if(ScreenCtrl.instance)
        {
            return ScreenCtrl.instance
        }
        ScreenCtrl.instance = this
        this.#listeners = []

        let instance = new globalThis.pedk.ObserverManager()
        console.log('instance.addListener is',instance.addListeners(this,this.ui_ctrl_cb,pedk.Module_Main.MSG_MODULE_PANEL,pedk.Module_Sub.MSG_PANEL_SUB_DRAW))

    }

    draw(widgets)
    {
		//判断参数是否数组
        if(Array.isArray(widgets))
        {
            //判断数组中每个元素是否都是对象
            if(widgets.length === 0 || widgets.every(item => typeof item === 'object' && item !== null) == false||widgets[0].type !== 'screen')
            {
                console.log('draw paramter error')
                return -1;
            }else   //有且只能有一个screen
            {
                var screen_num = 0;
                for( let widget of widgets)
                {
                    if(widget instanceof Screen)
                    {
                        screen_num++;
                    }
                } 
                if(screen_num > 1)
                {

                    return -1;
                }
            }
            
        }else
        {
            console.log('draw paramter error')
            return -1;
        }
        
		for (let listener of this.#listeners)
		{
			this.removeListener(listener);
		}
		
		for (let widget of widgets)
		{
           this.addListener(widget);
		}
        
		return js_screenCtrl_draw(widgets);

        
    }

    redraw(widgets)
    {
        console.log("*********redraw*********");
		//判断参数是否数组
        if(Array.isArray(widgets))
        {
            //判断数组中每个元素是否都是对象
            if(widgets.length === 0 || widgets.every(item => typeof item === 'object' && item !== null) == false)
            {
                console.log('draw paramter error')
                return -1;
            }
            
        }else
        {
            console.log('draw paramter error')
            return -1;
        }
        
		for (let listener of this.#listeners)
		{
			this.removeListener(listener);
		}
		
		for (let widget of widgets)
		{
           this.addListener(widget);
		}
        
		return js_screenCtrl_redraw(widgets);

        
    }

    ui_ctrl_cb(that,response,data)
    {
        //console.log('ui_ctrl_cb data is',data);
        that.notify(data,response)
    }

    notify(data,response)
    {
        console.log('notify data is',data)
        var jsonarr = strToJson(data);
		
		//console.log('this.#listeners.length',this.#listeners.length)
		for(const listener of this.#listeners)
        {
			

			if(jsonarr[0].id == listener.id)
			{
				//console.log('click listener.id is ',listener.id);
				switch(listener.type)
				{
					case 'button':
						console.log('type is button')
						if(jsonarr[0].is_checked == 1)
						{
							console.log('button press')
							listener.cb_pressed(listener.id)
						}
						else
						{
							console.log('button release')
							listener.cb_released(listener.id)
						}
						break;
					case 'lineedit':
						console.log('type is lineedit')
						if(jsonarr[0].is_checked == 0)
						{
                            //listener.cb_focus_changed(listener.id,true)
							console.log('lineedit press ',listener.id)

                            //将其他lineedit焦点设置为false
                            for(const lineedit of this.#listeners)
                            {
                                if(lineedit.id !== listener.id && lineedit.type ==='lineedit')
                                {
                                    //console.log('lineedit id ',lineedit.id)
                                    //lineedit.cb_focus_changed(lineedit.id,false)
                                }
                            }
							listener.cb_focus_changed(listener.id,true)

						}
						break;
					case 'radio':
						console.log('type is radio')
						if(jsonarr[0].is_checked == 1)
						{
							console.log('radio press')
							listener.cb_pressed(listener.id)
						}
						else
						{
							console.log('radio release')
							listener.cb_released(listener.id)
						}
						break;
                        
					case 'checkbtn':
						console.log('type is checkbtn')
						if(jsonarr[0].is_checked == 1)
						{
							console.log('checkbtn press')
							listener.cb_pressed(listener.id)
						}
						else
						{
							console.log('checkbtn release')
							listener.cb_released(listener.id)
						}
						break;
                    case 'keyboard':
                        console.log('type is keyboard')
                        if(jsonarr[0].is_checked ==1 )
                        {
                            console.log('keyboard input text:',jsonarr[0].input_text)
                            listener.cb_text_finished(jsonarr[0].input_text)
                        }
                        else
                        {
                            console.log('checkbtn release')
                            listener.cb_text_cancel()
                        }
                        break;

                    case 'list':
                        console.log('type is list')
                        listener.cb_item_clicked( jsonarr[0].id, jsonarr[0].index);
                        break;
                    case 'radiogroup':
                        console.log('type is radiogroup')
						if(jsonarr[0].is_checked == 1)
						{
							console.log('radiogroup press')
							listener.cb_pressed(listener.id,jsonarr[0].index)
						}
						else
						{
							console.log('radiogroup release')
							listener.cb_released(listener.id, jsonarr[0].index)
						}
                        break;
					default:
						break;
				}
                return;


			}

        }
	}

    addListener(listener)
    {
	
      this.#listeners.push(listener)
        return true
    }
    removeListener(listener)
    {
        //this.#listeners.pop(listener)
        this.#listeners.splice(0);
        return true
    }
       
   getResolution() 
   {
        return this.#screenInfo.resolution
    }

    screenOn() {
        this.#screenInfo.switch = true
        return js_screen_on();
         
    }


    screenOff() {
    
        this.#screenInfo.switch = false
        
        return js_screen_off();
    }


    isScreenOn() {
        console.log('isScreenOn is ',this.#screenInfo.switch)
        return this.#screenInfo.switch;
    }


    setScreenBrightness(brightness) {
        if(typeof brightness !== 'number' || brightness < 1 || brightness > 100 || !Number.isInteger(brightness))
        {
            console.log('Function:',this.setScreenBrightness.name,'parameter error')
            return -1;
        }
    	console.log('setScreenBrightness is ',brightness)
        this.#screenInfo.brightness = brightness
        
        //this.#updateScreenInfo()
        return js_screen_brightness(brightness);
    }


    getScreenBrightness() {
    	console.log('ScreenBrightness is ',this.#screenInfo.brightness)
        return this.#screenInfo.brightness
    }
	drawExit(){
		console.log('js drawExit app window')
		return js_screen_drawExit()
	}

    setTimeoutForAppReturn( timeout )
    {
        if(typeof timeout !== 'number' || !Number.isInteger(timeout) || timeout < 5000 || timeout > 300000  )
        {
            console.log('Function:',this.setTimeoutForAppReturn.name,'parameter error')
            return ERROR_NO.EINVALIDPARAM;
        }
        console.log('setTimeoutForAppReturn is ',timeout)
        this.#screenInfo.timeout = timeout

        if( 0 == js_setTimeoutForAppReturn(timeout) )
        {
            return ERROR_NO.EXIT_SUCCESS;
        }
        else
        {
            console.log('setTimeoutForAppReturn is fail')
            return ERROR_NO.EXIT_FAILURE;
        }
    }

    getTimeoutForAppReturn( )
    {
        var timeout_dc = js_getTimeoutForAppReturn();
        if( timeout_dc >= 0 )
        {
            this.#screenInfo.timeout = timeout_dc;
            console.log('getTimeoutForAppReturn is ',timeout_dc)
            return timeout_dc;
        }
        else
        {
            console.log('getTimeoutForAppReturn is fail')
            return ERROR_NO.EXIT_FAILURE;
        }
    }
    deleteTimeoutForAppReturn( )
    {
        var timeout_switch_dc = js_deleteTimeoutForAppReturn();
        if( timeout_switch_dc >= 0 )
        {
            return ERROR_NO.EXIT_SUCCESS;
        }
        else
        {
            console.log('deleteTimeoutForAppReturn fail ')
            return ERROR_NO.EXIT_FAILURE;
        }
    }


    changeToNativeWindow( native_window )
    {
        if( typeof native_window !== 'string' )
        {
            console.log('changeToNativeWindow parameter error')
            return ERROR_NO.EINVALIDPARAM;
        }
        for( let key in NATIVE_WINDOW )
        {
            if( NATIVE_WINDOW[key] === native_window )
            {
                if( 0 === js_changeToNativeWindow( native_window ) )
                {
                    console.log('changeToNativeWindow:',native_window)
                    return ERROR_NO.EXIT_SUCCESS;        
                }
                else
                {
                    console.log('changeToNativeWindow:',native_window, 'error!')
                    return ERROR_NO.EXIT_FAILURE;        
                }
            }
        }
        console.log('changeToNativeWindow parameter error:',native_window)
        return ERROR_NO.EINVALIDPARAM;
    }    
}

class CheckButton extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("check");
       this.x = 0;
       this.y = 0;
       this.w = 30;
       this.h = 20;
       this.style_sheet = new StyleSheet();
       this.is_valid = true;
       this.text = "";
       this.is_checked = false;
       this.icons = [];
       this.cb_pressed = null;
       this.cb_released = null;

   }
   #type = 'checkbtn'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<checkbtn> which has only a getter')
   }
   
   draw()
   {
   		console.log('checkbtn id is ', this.id)
		console.log('checkbtn id type is ', this.type)
		//js_check_button_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.is_valid,this.text, this.is_checked,this.icons[0].img_res,this.icons[0].img_format,this.icons[1].img_res,this.icons[1].img_format,this.icons[2].img_res,this.icons[2].img_format);
   }
}

class RadioButton extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("radio");
       this.x = 0;
       this.y = 0;
       this.w = 20;
       this.h = 20;
       this.style_sheet = new StyleSheet();
       this.is_valid = true;
       //this.text = "";
       this.is_checked = false;
       this.icons = [];
       this.cb_pressed = null;
       this.cb_released = null;
   }   
   #type = 'radio'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<radio> which has only a getter')
   }
   draw()
   {
   		console.log('radio id is ', this.id)
		console.log('radio id type is ', this.type)
		//js_radio_button_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.is_valid,this.text, this.is_checked,this.imgs[0].img_res,this.imgs[0].img_format,this.imgs[1].img_res,this.imgs[1].img_format,this.imgs[2].img_res,this.imgs[2].img_format);
   }
}

class RadioGroup extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("radiogroup");
       this.x = 0;
       this.y = 0;
       this.w = 30;
       this.h = 30;
       this.style_sheet = new StyleSheet();
       this.is_valid = true;
       this.buttons = [];
       this.select_value = -1;
       this.cb_pressed = null;
       this.cb_released = null;
   }   
   #type = 'radiogroup'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<radio> which has only a getter')
   }
   draw()
   {
   		console.log('radio id is ', this.id)
		console.log('radio id type is ', this.type)
		//js_radio_button_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.is_valid,this.text, this.is_checked,this.imgs[0].img_res,this.imgs[0].img_format,this.imgs[1].img_res,this.imgs[1].img_format,this.imgs[2].img_res,this.imgs[2].img_format);
   }
}

class List extends Widget {
    constructor() {
        super()
        this.id = createUniqueID("list");
        this.x = 0;
        this.y = 0;
        this.w = 50;
        this.h = 60;
        this.style_sheet = new StyleSheet();
        this.list_item_group = [];
        this.select_value = [];
        this.cb_item_clicked = null;
    }
    #type = 'list'
    get type() 
    {
        return this.#type
    }
    set type( val ) 
    {
        return console.log('Cannot set property type of #<list> which has only a getter')
    }
    draw()
    {
            console.log('list id is ', this.id)
        console.log('list id type is ', this.type)
        //js_radio_button_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.is_valid,this.text, this.is_checked,this.imgs[0].img_res,this.imgs[0].img_format,this.imgs[1].img_res,this.imgs[1].img_format,this.imgs[2].img_res,this.imgs[2].img_format);
    }
}
        

class Image extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("image");
       this.x = 0;
       this.y = 0;
       this.w = 50;
       this.h = 50;
       this.style_sheet = new StyleSheet();

       this.img = new ImageData();
      
   }
   #type = 'image'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<image> which has only a getter')
   }
   draw()
   {
   		console.log('image id is ', this.id)
		console.log('image id type is ', this.type)
		//js_image_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.imgs.img_res,this.imgs.img_format);
   }
}
class Gif extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("gif");
       this.x = 0;
       this.y = 0;
       this.w = 50;
       this.h = 50;
       this.style_sheet = new StyleSheet();

       this.img = new ImageData();
        
    }
   #type = 'gif'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<gif> which has only a getter')
   }
   draw()
   {
   		console.log('image id is ', this.id)
		console.log('image id type is ', this.type)
		//js_image_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.imgs.img_res,this.imgs.img_format);
   }
}
class LineEdit extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("lineedit");
       this.x = 0;
       this.y = 0;
       this.w = 50;
       this.h = 20;
       this.style_sheet = new StyleSheet();
       this.is_valid = true;
       this.has_focus = false;
       this.text = "";
       this.display_mode = "";
       this.cb_editing_finished	= null;
       this.cb_focus_changed = null;
   }
   #type = 'lineedit'
   get type() 
    {
        return this.#type
   }
   set type( val ) 
   {
       return console.log('Cannot set property type of #<lineedit> which has only a getter')
   }
   draw()
   {
   		console.log('lineedit id is ', this.id)
		console.log('lineedit id type is ', this.type)
		//js_line_edit_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.is_valid);
   }
}

class AnimationImage extends Widget
{

   constructor(){
   	   super()
       this.id = createUniqueID("animationimage");
       this.x = 0;
       this.y = 0;
       this.w = 50;
       this.h = 50;
       this.style_sheet = new StyleSheet();;
       this.imgs = [];
       this.freq = 1000;


   }
   
	 #type = 'animationimage'
	get type() 
	 {
		 return this.#type
	}
	set type( val ) 
	{
		return console.log('Cannot set property type of #<animationimage> which has only a getter')
    }
	
   draw()
   {
   		console.log('animalimage id is ', this.id)
		console.log('animalimage id type is ', this.type)
		//js_animal_image_draw(this.id,this.type,this.x,this.y,this.w,this.h,this.freqthis.imgs);
   }
}

class LinkageMechanism
{
    constructor()
    {}
    appEnded( app_id )
    {
        if(typeof app_id !== 'string' || app_id === null || app_id === undefined || app_id.length === 0)
        {
            console.log('param error')
            return -1;
        }
        return js_appEnded(app_id);
    }
    appStarted( app_id )
    {
        if(typeof app_id !== 'string' || app_id === null || app_id === undefined || app_id.length === 0)
        {
            console.log('param error')
            return -1;
        }
        return js_appStarted(app_id)
    }
}

class BuzzerCtrl
{	 constructor()
    {

    }
	buzzerOff()
	{
		return js_buzzerOff();
	}
	buzzerOn()
	{
		return js_buzzerOn();
	}
    buzz()
    {
        throw "EOPNOTSUPP";
    }
}
class LedCtrl
{	 constructor()
    {

    }
	ledOff(led_id)
	{
        if(typeof led_id !== 'number' || !Number.isInteger(led_id)) 
        {
            //throw TypeError("Led id must be only one Number parmeter");
            console.log("Led id must be only one Number parmeter");
            throw("EINVALIDPARAM");
        }
        if(led_id < JS_LED_ID.JS_LED_ID_POWER || led_id > JS_LED_ID.JS_LED_ID_SAVE_POWER) 
        {
            //throw TypeError("led id undefined");
            console.log("Led id undefined");
            return -1;
        }
		return js_ledOff(led_id);
	}
	ledOn(led_id,color)
	{
        if(typeof led_id !== 'number' || typeof color !== 'number'|| !Number.isInteger(led_id)||!Number.isInteger(color)) 
        {
            //throw TypeError("Led id must be only one Number parmeter");
            console.log("Led id must be only one Number parmeter");
            throw("EINVALIDPARAM");
        }
        if(led_id < JS_LED_ID.JS_LED_ID_POWER || led_id > JS_LED_ID.JS_LED_ID_SAVE_POWER || color < JS_LED_COLOR.JS_LED_COLOR_UNDEFINED || color > JS_LED_COLOR.JS_LED_COLOR_WHITE) 
        {
            //throw TypeError("led id undefined");
            console.log("Led id  or type undefined");
            return -1;
        }


		return js_ledOn(led_id,color);
	}
    ledFlash(led_id, color, on_time, off_time, on_type)
    {
        throw "EOPNOTSUPP";
    }
}

class KeyCtrl_cb
{
	 constructor()
    {
		this.key_id = 0;
		this.key_type = 0;
    }
}

class KeyCtrlStruct{
	constructor(  key_id, key_type, callback){
		//this.obj = obj;
		this.func = callback;
		this.key_id = key_id; //
		this.key_type = key_type;
	}
	KeyCtrlCallBack(key_id, key_type){
		if(key_id === this.key_id && key_type === this.key_type){
			this.func(key_id, key_type) ;
		}
	}
}

class KeyCtrl
{
	#listeners

    constructor()
    {
        this.#listeners = []
        let instance = new globalThis.pedk.ObserverManager()
        console.log('key instance.addListener is',instance.addListeners(this,this.ui_key_cb,pedk.Module_Main.MSG_MODULE_PANEL,pedk.Module_Sub.MSG_PANEL_SUB_CTRL_KEY))

    }
	addListener(listener)
    {
      this.#listeners.push(listener)
        return true
    }
    removeListener(listener)
    {
        this.#listeners.pop(listener)
        return true
    }
	func(key_id , key_type)
	{
		
	}
	setCallBackFunc(key_id, key_type, callback_func) {
        if(typeof key_id !== 'number' || typeof key_type !== 'number' || typeof callback_func !== 'function' || !Number.isInteger(key_id) || !Number.isInteger(key_type) ) 
        {
            //throw TypeError("Led id must be only one Number parmeter");
            console.log("setCallBackFunc parmeter error");
            return -1;
        }
        if(key_type < JS_KEY_TYPE.JS_KEY_TYPE_SHORT_PRESS || key_type > JS_KEY_TYPE.JS_KEY_TYPE_LONG_RELEASE || key_id < JS_KEY_ID.JS_KEY_ID_POWER || key_id > JS_KEY_ID.JS_KEY_ID_START)
        {
            console.log(`key_id:${key_id},or key_type:${key_type} undefined`);
            return -1;
        }
        const unsurpport_key = [
            JS_KEY_ID.JS_KEY_ID_TONER_SAVE, 
            JS_KEY_ID.JS_KEY_ID_WIFI,
            JS_KEY_ID.JS_KEY_ID_DOWN,
            JS_KEY_ID.JS_KEY_ID_UP,
            JS_KEY_ID.JS_KEY_ID_MENU,
            JS_KEY_ID.JS_KEY_ID_RETURN,
            JS_KEY_ID.JS_KEY_ID_OK,
            JS_KEY_ID.JS_KEY_ID_HELP,
            JS_KEY_ID.JS_KEY_ID_COPY,
            JS_KEY_ID.JS_KEY_ID_IDCOPY,
            JS_KEY_ID.JS_KEY_ID_BILL,
            JS_KEY_ID.JS_KEY_ID_SCAN,
            JS_KEY_ID.JS_KEY_ID_POWER_SAVE,
        ];
        if(  key_type >=  JS_KEY_TYPE.JS_KEY_TYPE_LONG_PRESS || unsurpport_key.includes(key_id)  )
        {
            console.log(`key_id:${key_id}, or key_type:${key_type} UnsupportedS`);
            return -1;
        }

        

        for(const listener of this.#listeners)
        {
			

			if(key_id == listener.key_id && key_type == listener.key_type)
			{
                console.log("key_id:",key_id,"key_type:",key_type," will be Re-registered!");
                listener.func = callback_func;
                return 0;
            }
        }

		let key = new KeyCtrlStruct(key_id, key_type, callback_func);
		if(key !== null || key !== 'undefined' )
		{					
			
			console.log('key_id:',key_id,',key_type:',key_type,',key callback_func:',key.func.name)

		}else{
			console.log('key undefined')

		}
		this.addListener(key);
        return 0;
    }
	
	ui_key_cb(that,response,data)
    {
        that.notify(data,response)
    }
	notify(data,response)
    {
        //console.log('notify data is',data)
        var jsonarr = strToJson(data);
		
		console.log('this.#listeners.length:',this.#listeners.length)
		for(const listener of this.#listeners)
        {
			
			if(jsonarr[0].key_id == listener.key_id && listener.key_type == jsonarr[0].key_type)
			{
				console.log('click listener.key_id is:',jsonarr[0].key_id, ',key_type:',jsonarr[0].key_type);
                    listener.KeyCtrlCallBack(jsonarr[0].key_id,jsonarr[0].key_type);
                    break;
			}

        }
	}

}

globalThis.pedk.ui.ScreenCtrl = ScreenCtrl
globalThis.pedk.ui.widget.Screen = Screen
globalThis.pedk.ui.widget.Panel = Panel
globalThis.pedk.ui.widget.ImageData = ImageData
globalThis.pedk.ui.widget.StyleSheet = StyleSheet
globalThis.pedk.ui.widget.Button = Button
globalThis.pedk.ui.widget.Label = Label
globalThis.pedk.ui.widget.CheckButton = CheckButton

globalThis.pedk.ui.widget.RadioButton = RadioButton
globalThis.pedk.ui.widget.RadioGroup = RadioGroup
globalThis.pedk.ui.widget.List = List

globalThis.pedk.ui.widget.Image = Image
globalThis.pedk.ui.widget.LineEdit = LineEdit
globalThis.pedk.ui.widget.AnimationImage = AnimationImage
globalThis.pedk.ui.widget.Gif = Gif
globalThis.pedk.ui.Point = Point

globalThis.pedk.ui.BuzzerCtrl = BuzzerCtrl
globalThis.pedk.ui.KeyCtrl = KeyCtrl
globalThis.pedk.ui.LedCtrl = LedCtrl
globalThis.pedk.ui.LinkageMechanism = LinkageMechanism
globalThis.pedk.ui.widget.Keyboard = Keyboard
globalThis.pedk.ui.widget.OptionItem = OptionItem


