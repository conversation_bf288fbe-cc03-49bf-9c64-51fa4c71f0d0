/**
 * @file license_auth_file_utils.c
 * @brief Utility functions for file operations, Base64 encoding/decoding, and license data comparison.
 * @addtogroup license_authentication
 * @{
 * @modifier yangzikun
 * @date 2024-12-11
 */

#include "license_auth_file_utils.h"

/**
 * @brief Converts the content of a file to Base64 format and writes it to another file.
 * @param[in] input_file Path of the input file.
 * @param[in] output_file Path of the output file.
 * @return On success, returns the output file path. On failure, returns NULL.
 */
static char *license_auth_convert_file_to_base64(const char *input_file, const char *output_file)
{
    // Validates input
    if (input_file == NULL || output_file == NULL)
    {
        pi_log_d("Error: Input or output file is NULL.\n");
        return NULL;
    }

    // Opens input file
    int32_t input_fd = open(input_file, O_RDONLY, 0777);
    if (input_fd < 0)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    // Opens or creates output file
    int32_t output_fd = open(output_file, O_RDWR | O_CREAT, 0777);
    if (output_fd < 0)
    {
        LICENSE_AUTH_ERROR_CLOSE(input_fd);
        return NULL;
    }

    // Reads input file content
    unsigned long input_length = license_auth_get_file_length(input_fd);
    char *input_buffer = malloc(input_length + 1);
    if (input_buffer == NULL)
    {
        LICENSE_AUTH_ERROR_CLOSE(input_fd);
        LICENSE_AUTH_ERROR_CLOSE(output_fd);
        return NULL;
    }

    memset(input_buffer, 0, input_length + 1);
    if (read(input_fd, input_buffer, input_length) != input_length)
    {
        LICENSE_AUTH_ERROR_CLOSE(input_fd);
        LICENSE_AUTH_ERROR_CLOSE(output_fd);
        LICENSE_AUTH_SAFE_FREE(input_buffer);
        return NULL;
    }

    // Allocates Base64 buffer
    size_t base64_size = LICENSE_AUTH_BASE64_SIZE(input_length);
    char *base64_buffer = malloc(base64_size);
    if (base64_buffer == NULL)
    {
        LICENSE_AUTH_ERROR_CLOSE(input_fd);
        LICENSE_AUTH_ERROR_CLOSE(output_fd);
        LICENSE_AUTH_SAFE_FREE(input_buffer);
        return NULL;
    }

    memset(base64_buffer, 0, base64_size);
    license_auth_base64_encode((const unsigned char *)input_buffer, input_length, base64_buffer);

    // Writes encoded content to output file
    size_t encoded_length = strlen(base64_buffer);
    if (write(output_fd, base64_buffer, encoded_length) != encoded_length)
	{
        pi_log_d("Error: Failed to write to output file: %s\n", output_file);
        LICENSE_AUTH_ERROR_CLOSE(input_fd);
        LICENSE_AUTH_ERROR_CLOSE(output_fd);
        LICENSE_AUTH_SAFE_FREE(input_buffer);
        LICENSE_AUTH_SAFE_FREE(base64_buffer);
        return NULL;
    }

    // Releases resources
    LICENSE_AUTH_SAFE_FREE(input_buffer);
    LICENSE_AUTH_SAFE_FREE(base64_buffer);
    LICENSE_AUTH_SAFE_CLOSE(input_fd);
    LICENSE_AUTH_SAFE_CLOSE(output_fd);

    return (char *)output_file;
}

/**
 * @brief Retrieves the length of a file and resets the file pointer to the start.
 * @param[in] fd File descriptor identifying the file.
 * @return File length in bytes.
 */
unsigned long license_auth_get_file_length(int32_t fd)
{
    if (fd < 0)
    {
        pi_log_d("Invalid file descriptor: %d\n\n", fd);
        return 0;
    }

    // Retrieves file length
    if (lseek(fd, 0, SEEK_SET) == (off_t)-1)
    {
        pi_log_d("lseek error (SEEK_SET)\n");
        return 0;
    }

    off_t length = lseek(fd, 0, SEEK_END);
    if (length == (off_t)-1)
    {
        pi_log_d("lseek error (SEEK_END)\n");
        return 0;
    }

    // Resets file pointer to the start
    if (lseek(fd, 0, SEEK_SET) == (off_t)-1)
    {
        pi_log_d("lseek error (SEEK_SET)\n");
        return 0;
    }

    return (unsigned long)length;
}

/**
 * @brief Writes a string to a file.
 * @param[in] string The string to write.
 * @param[in] output_file The file path where the string will be written.
 * @return On success, returns the file path. On failure, returns NULL.
 */
char *license_auth_write_to_file(const char *string, const char *output_file)
{
    if (!string || !output_file)
    {
        pi_log_d("Error: Invalid input. String or file path is NULL.\n");
        return NULL;
    }

    // Opens file
    int32_t file_descriptor = open(output_file, O_RDWR | O_CREAT | O_TRUNC, 0777);
    if (file_descriptor < 0)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    // Writes string
    int32_t string_length = strlen(string);
    if (write(file_descriptor, string, string_length) != string_length)
    {
        LICENSE_AUTH_ERROR_CLOSE(file_descriptor);
        return NULL;
    }

    // Closes file
    LICENSE_AUTH_SAFE_CLOSE(file_descriptor);
    return (char *)output_file;
}

/**
 * @brief Reads and parses cJSON data from a file.
 * @param[in] path File path containing valid cJSON content.
 * @return On success, returns the parsed cJSON object. On failure, returns NULL.
 */
cJSON *license_auth_get_cjson_from_file(const char *path)
{
    if (!path)
    {
        LOG_ERROR_NULL("Error: File path is NULL.");
    }

    // Opens file
    int32_t fd = open(path, O_RDONLY, 0777);
    if (fd < 0)
    {
        LICENSE_AUTH_ERROR_NULL; // 文件打开失败
    }

    // Reads file content
    unsigned long length = license_auth_get_file_length(fd);
    if (length == 0)
    {
        LICENSE_AUTH_ERROR_CLOSE(fd);
        LOG_ERROR_NULL("Error: File is empty: %s", path);
    }

    char *buf = malloc(length + 1);
    if (!buf)
    {
        LICENSE_AUTH_ERROR_CLOSE(fd);
        LICENSE_AUTH_ERROR_NULL; // 内存分配失败
    }

    memset(buf, 0, length + 1);

    ssize_t read_byte = read(fd, buf, length);
    if(read_byte != (ssize_t)length)
    {
        pi_log_d("error: read byte %lu, actual %zd\n",length,read_byte);
        LICENSE_AUTH_SAFE_FREE(buf);
        LICENSE_AUTH_ERROR_CLOSE(fd); // 文件读取失败
        LICENSE_AUTH_ERROR_NULL; // 读取失败
    }

    LICENSE_AUTH_SAFE_CLOSE(fd);

    // Parses cJSON data
    cJSON *cjson_item = cJSON_Parse(buf);
    LICENSE_AUTH_SAFE_FREE(buf);
    if (!cjson_item)
    {
        pi_log_d("cJSON_Parse error,return NULL\n");
        return NULL;
    }

    return cjson_item;
}


/**
 * @brief Writes cJSON data to a file.
 * @param[in] item The cJSON object to write.
 * @param[in] file The file path to write to, or NULL to use the default path.
 * @return On success, returns the file path. On failure, returns NULL.
 */
char *license_auth_write_cjson_to_file(cJSON *item, const char *file)
{
    if (item == NULL)
    {
        pi_log_d("Error: cJSON item is NULL.\n");
        return NULL;
    }

    char *buf = cJSON_Print(item);
    if (buf == NULL)
    {
        pi_log_d("Error: Failed to convert cJSON to string.\n");
        return NULL;
    }

    const char *output_file = (file == NULL) ? LICENSE_AUTH_TMP_FILE : file;
    int32_t fd = open(output_file, O_RDWR | O_CREAT, 0777);
    if (fd < 0)
	{
        pi_log_d("Error: Failed to open file: %s\n", output_file);
        LICENSE_AUTH_SAFE_FREE(buf);
        return NULL;
    }

    int32_t len = strlen(buf);
    if (write(fd, buf, len) != len)
	{
        pi_log_d("Error: Failed to write to file: %s\n", output_file);
        LICENSE_AUTH_ERROR_CLOSE(fd);
        LICENSE_AUTH_SAFE_FREE(buf);
        return NULL;
    }

    LICENSE_AUTH_SAFE_CLOSE(fd);
    LICENSE_AUTH_SAFE_FREE(buf);
    return (char *)output_file;
}

/**
 * @brief 将 cJSON 非格式化数据写入文件。如果文件路径为 NULL，则使用默认路径 `LICENSE_AUTH_TMP_FILE`。
 *
 * @param item 待写入的 cJSON 数据。
 * @param file 目标文件路径，若为 NULL 则使用默认路径。
 * @return 成功返回文件路径，失败返回 NULL。
 */
char *license_auth_write_unformatted_cjson_to_file(cJSON *item, const char *file)
{
    if (item == NULL)
    {
        pi_log_d("Error: cJSON item is NULL.\n");
        return NULL;
    }

    char *buf = cJSON_PrintUnformatted(item);
    if (buf == NULL)
    {
        pi_log_d("Error: Failed to convert cJSON to unformatted string.\n");
        return NULL;
    }

    const char *output_file = (file == NULL) ? LICENSE_AUTH_TMP_FILE : file;
    int32_t fd = open(output_file, O_RDWR | O_CREAT, 0777);
    if (fd < 0)
    {
        pi_log_d("Error: Failed to open file: %s\n", output_file);
        LICENSE_AUTH_SAFE_FREE(buf);
        return NULL;
    }

    int32_t len = strlen(buf);
    if (write(fd, buf, len) != len)
    {
        pi_log_d("Error: Failed to write to file: %s\n", output_file);
        LICENSE_AUTH_ERROR_CLOSE(fd);
        LICENSE_AUTH_SAFE_FREE(buf);
        return NULL;
    }

    LICENSE_AUTH_SAFE_CLOSE(fd);
    LICENSE_AUTH_SAFE_FREE(buf);
    return (char *)output_file;
}

/**
 * @brief Compares the contents of two files for similarity.
 * @param[in] input_file Path to the first file.
 * @param[in] output_file Path to the second file.
 * @return Returns 0 if files are similar, non-zero otherwise.
 */
int32_t license_auth_compare_files(const char *input_file, const char *output_file)
{
    if (input_file == NULL || output_file == NULL)
    {
        return -1;
    }

    int32_t input_fd = -1, output_fd = -1;
    char *input_buffer = NULL, *output_buffer = NULL;
    int32_t result = -1; // 默认返回错误

    do {
        // 打开输入文件
        input_fd = open(input_file, O_RDONLY, 0777);
        if (input_fd < 0)
        {
            LICENSE_AUTH_ERROR_INT;
        }

        // 打开输出文件
        output_fd = open(output_file, O_RDONLY, 0777);
        if (output_fd < 0)
        {
            LICENSE_AUTH_ERROR_CLOSE(input_fd);
            break;
        }

        // 获取文件长度
        unsigned long input_length = license_auth_get_file_length(input_fd);
        unsigned long output_length = license_auth_get_file_length(output_fd);
        unsigned long compare_length = (input_length * 9) / 10; // 90%

        // 长度检查
        if (compare_length > output_length)
        {
            break;
        }

        // 分配缓冲区
        input_buffer = malloc(compare_length + 1);
        output_buffer = malloc(compare_length + 1);
        if (!input_buffer || !output_buffer)
        {
            break;
        }

        memset(input_buffer, 0, compare_length + 1);
        memset(output_buffer, 0, compare_length + 1);

        // 读取文件内容
        if (read(input_fd, input_buffer, compare_length) != compare_length ||
            read(output_fd, output_buffer, compare_length) != compare_length)
        {
            break;
        }

        // 比较文件内容
        result = memcmp(input_buffer, output_buffer, compare_length);

    } while (0);

    // 释放资源
    LICENSE_AUTH_SAFE_CLOSE(input_fd);
    LICENSE_AUTH_SAFE_CLOSE(output_fd);
    LICENSE_AUTH_SAFE_FREE(input_buffer);
    LICENSE_AUTH_SAFE_FREE(output_buffer);

    return result;
}

/**
 * @brief Copies the content of one file to another.
 * @param[in] source_file Path to the source file.
 * @param[out] target_file Path to the target file.
 * @return On success, returns the target file path. On failure, returns NULL.
 */
char *license_auth_copy_file(const char *source_file, const char *target_file)
{
    int32_t source_fd = -1, target_fd = -1;
    char *buffer = NULL;
    unsigned long file_length = 0;

    // 错误处理宏：释放资源并返回 NULL
    #define CLEANUP_AND_RETURN_NULL() \
        do { \
            LICENSE_AUTH_SAFE_CLOSE(source_fd); \
            LICENSE_AUTH_SAFE_CLOSE(target_fd); \
            LICENSE_AUTH_SAFE_FREE(buffer); \
            return NULL; \
        } while (0)

    // 打开源文件
    source_fd = open(source_file, O_RDONLY, 0777);
    if (source_fd < 0)
    {
        LICENSE_AUTH_ERROR_NULL;
    }

    // 打开目标文件
    target_fd = open(target_file, O_WRONLY | O_CREAT | O_TRUNC, 0777);
    if (target_fd < 0)
    {
        LICENSE_AUTH_ERROR_NULL;
        CLEANUP_AND_RETURN_NULL();
    }

    // 获取源文件长度
    file_length = license_auth_get_file_length(source_fd);
    if (file_length == 0)
    {
        LICENSE_AUTH_ERROR_NULL;
        CLEANUP_AND_RETURN_NULL();
    }

    // 分配缓冲区
    buffer = malloc(file_length + 1);
    if (!buffer)
    {
        LICENSE_AUTH_ERROR_NULL;
        CLEANUP_AND_RETURN_NULL();
    }
    memset(buffer, 0, file_length + 1);

    // 读取源文件内容
    if (read(source_fd, buffer, file_length) != file_length)
    {
        LICENSE_AUTH_ERROR_CLOSE(source_fd);
        CLEANUP_AND_RETURN_NULL();
    }

    // 写入目标文件内容
    if (write(target_fd, buffer, file_length) != file_length)
    {
        LICENSE_AUTH_ERROR_CLOSE(target_fd);
        CLEANUP_AND_RETURN_NULL();
    }

    // 清理资源并返回成功结果
    LICENSE_AUTH_SAFE_FREE(buffer);
    LICENSE_AUTH_SAFE_CLOSE(source_fd);
    LICENSE_AUTH_SAFE_CLOSE(target_fd);

    return (char *)target_file;

    #undef CLEANUP_AND_RETURN_NULL
}


/**
 * @brief 将 Base64 编码的内容解码并写入文件
 * @param[in] input_string_or_file 输入可以是文件路径或 Base64 编码的字符串
 * @param[in] output_file 输出文件路径
 * @return 成功返回输出文件路径，失败返回 NULL
 */
char *license_auth_decode_base64_to_file(char *input_string_or_file, const char *output_file)
{
    if (input_string_or_file == NULL || output_file == NULL)
    {
        pi_log_d("Error: Input string/file or output file is NULL.\n");
        return NULL;
    }

    char *buffer = NULL;
    unsigned long input_length = 0;

    // 读取输入：文件路径或 Base64 字符串
    if (access(input_string_or_file, F_OK) == 0)
    {
        int32_t input_fd = open(input_string_or_file, O_RDONLY, 0777);
        if (input_fd < 0)
        {
            LICENSE_AUTH_ERROR_NULL;
        }

        input_length = license_auth_get_file_length(input_fd);
        buffer = malloc(input_length + 1);
        if (buffer == NULL)
        {
            LICENSE_AUTH_ERROR_CLOSE(input_fd);
            return NULL;
        }

        memset(buffer, 0, input_length + 1);
        if (input_length != read(input_fd, buffer, input_length))
        {
            LICENSE_AUTH_ERROR_CLOSE(input_fd);
            LICENSE_AUTH_SAFE_FREE(buffer);
            return NULL;
        }

        LICENSE_AUTH_SAFE_CLOSE(input_fd);
    }
    else
    {
        input_length = strlen(input_string_or_file);
        buffer = malloc(input_length + 1);
        if (buffer == NULL)
        {
            return NULL;
        }

        strcpy(buffer, input_string_or_file);
    }
    pi_log_d("11output_file %s!\n",output_file);

    // 创建输出文件
    int32_t output_fd = open(output_file, O_RDWR | O_CREAT, 0777);
    if (output_fd < 0)
    {
        LICENSE_AUTH_SAFE_FREE(buffer);
        LICENSE_AUTH_ERROR_NULL;
    }

    // 解码 Base64
    char *decoded_buffer = malloc(input_length + 1);
    if (decoded_buffer == NULL)
    {
        LICENSE_AUTH_SAFE_FREE(buffer);
        LICENSE_AUTH_ERROR_CLOSE(output_fd);
        return NULL;
    }

    memset(decoded_buffer, 0, input_length + 1);
    license_auth_base64_decode(buffer, (unsigned char *const)decoded_buffer);

    // 获取解码后的实际长度
    int32_t decoded_length = license_auth_get_complex_length(decoded_buffer, input_length + 1);

    // 将解码内容写入文件
    if (decoded_length != write(output_fd, decoded_buffer, decoded_length))
    {
        LICENSE_AUTH_ERROR_CLOSE(output_fd);
        LICENSE_AUTH_SAFE_FREE(buffer);
        LICENSE_AUTH_SAFE_FREE(decoded_buffer);
        return NULL;
    }

    // 清理资源
    LICENSE_AUTH_SAFE_FREE(buffer);
    LICENSE_AUTH_SAFE_FREE(decoded_buffer);
    LICENSE_AUTH_SAFE_CLOSE(output_fd);

    return (char *)output_file;
}

/**
 * @brief 将 cJSON 节点的字符串值写入指定文件。
 *
 * @param item cJSON 节点，仅支持 `valuestring` 类型。
 * @param file 目标文件路径。
 * @return 成功返回文件路径，失败返回 NULL。
 */
char *license_auth_write_link_node_to_file(cJSON *item, const char *file)
{
    if (item == NULL || item->valuestring == NULL || file == NULL) {
        pi_log_d("Invalid parameters: item or file is NULL.\n");
        return NULL;
    }
    pi_log_d("open file %s\n",file);

    int32_t fd = open(file, O_RDWR | O_CREAT, 0777);
    if (fd < 0) {
        pi_log_d("Failed to open file: %s\n", file);
        return NULL;
    }
    pi_log_d("fd %d, item->valuestring  %s\n",fd,item->valuestring);

    int32_t length = strlen(item->valuestring);
    if (write(fd, item->valuestring, length) != length) {
        pi_log_d("Failed to write to file: %s\n", file);
        LICENSE_AUTH_ERROR_CLOSE(fd);
        return NULL;
    }

    LICENSE_AUTH_SAFE_CLOSE(fd);
    return (char *)file;
}


/*******************************************************************************
 Description  : Compare_File_PathFile
 Input    Parm: Filename
 Return   Parm: result
 Create Author: yangzikun,2023/9/16
*******************************************************************************/
int32_t license_auth_compare_file_with_nvram(const char *file_path)
{
    int32_t license_fd = -1;
	pi_log_d("in Compare_File_PathFile \n");
    int32_t comparison_result = -1;

    unsigned long license_file_length = 0;

    char *license_buffer = NULL;

    char nvram_value[LICENSE_AUTH_LICENSE_BUF_LENGTH] = {0};
    char processed_license_str[LICENSE_AUTH_LICENSE_BUF_LENGTH] = {0};
    char nvram_license_key[32] = {0};

    pi_log_d("Starting license_auth_compare_file_with_nvram.\n");

    // 打开输入文件
    license_fd = open(file_path, O_RDONLY, 0777);
    if (license_fd < 0)
    {
        LICENSE_AUTH_ERROR_INT;
    }

    // 获取文件长度
    license_file_length = license_auth_get_file_length(license_fd);
    pi_log_d("License file length = %lu\n", license_file_length);

    // 为文件内容分配内存
    license_buffer = (char *)malloc(license_file_length + 1);
    if (license_buffer == NULL)
    {
        pi_log_d("Failed to allocate memory for license buffer.\n");
        LICENSE_AUTH_ERROR_CLOSE(license_fd);
        return -1;
    }

    memset(license_buffer, 0, license_file_length + 1);

    // 读取文件内容
    if (license_file_length != read(license_fd, license_buffer, license_file_length))
    {
        LICENSE_AUTH_ERROR_CLOSE(license_fd);
        pi_log_d("Failed to read file: %s\n", license_buffer);
        LICENSE_AUTH_SAFE_FREE(license_buffer);
        return -1;
    }
    LICENSE_AUTH_SAFE_CLOSE(license_fd);

    LICENSE_COLLECTION_S license_collection = {0};

    if (pi_nvram_get(PRNSDK_ID_LICENSE_AUTHENTICATION, VTYPE_STRUCT, (void *)&license_collection, sizeof(LICENSE_COLLECTION_S)) != 0)
    {
        pi_log_d("Error:pi_nvram_get LICENSE_AUTHENTICATION\n");
        LICENSE_AUTH_SAFE_FREE(license_buffer);
        return -1; // 获取失败
    }

    // 遍历所有可能的 License 数据
    for (int32_t i = 0; i < LICENSE_AUTH_LICENSE_COUNT; i++)
    {
        // 跳过无效的 License 数据
        if (license_collection.licenses[0].license_data[0] == 'T')
        {
            pi_log_d("Skipping invalid license at %s.\n", nvram_license_key);
            continue;
        }
        pi_log_d("nv licenses[%d].license_data %s,Length: %lu\n",i,license_collection.licenses[i].license_data,strlen(license_collection.licenses[i].license_data));


        // 处理 NVRAM 数据
        if (license_auth_process_license_string(license_collection.licenses[i].license_data, processed_license_str, sizeof(processed_license_str)) != 0)
        {
            pi_log_d("Failed to process license string.\n");
            continue;
        }

        pi_log_d("Processed NV License: %s, Length: %lu\n", processed_license_str, strlen(processed_license_str));
        pi_log_d("Local Pub File License: %s, Length: %lu\n", license_buffer, strlen(license_buffer));

        // 计算比较长度（按 95% 的相似度）
        int32_t compare_length = (int32_t)(fmin(strlen(license_buffer), strlen(processed_license_str)) * 0.95);
        pi_log_d("Comparison length: %d\n", compare_length);

        // 比较文件内容和处理后的 NVRAM 数据
        comparison_result = strncmp(processed_license_str, license_buffer, compare_length);
        if (comparison_result == 0)
        {
            LICENSE_AUTH_SAFE_FREE(license_buffer);
            pi_log_d("License verification succeeded.\n");
            return 0;
        }
    }

    LICENSE_AUTH_SAFE_FREE(license_buffer);
    pi_log_d("License verification failed.\n");
    return comparison_result;
}
/**
 *@}
 */
