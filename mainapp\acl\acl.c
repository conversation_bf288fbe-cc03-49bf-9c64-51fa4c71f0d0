/**************************************************************
Copyright (c)  	2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:   acl
file	name: acl.c
author:	 zeng xian chang (013366)
date: 2014-12-03
description:   acl module
****************************************************************/
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdbool.h>
#include "acl/acl.h"
#include "utilities/parsercommon.h"
#include "utilities/msgrouter.h"
#include "msgrouter_main.h"
#include "pol/pol_convert.h"
#include "pol/pol_list.h"
#include "pol/pol_mem.h"
#include "pol/pol_log.h"
#include "pol/pol_string.h"
#include "job_manager.h"
#include "event_manager/event_mgr.h"
#include "preparse.h"
#include "pol/pol_endian.h"


static const uint8_t s_acl_uel[] = "\x1b%-12345X";
static const uint8_t s_acl_enterlang[] = "@PJL ENTER LANGUAGE=ACL\xD\xA";
static const uint16_t s_acl_prefix = 0xAC;
static const uint8_t s_acl_enter_settime[] = "@QUERY\xD\xA@QUERY ENTER SETTIME=ST\xD\xA";
static const uint8_t s_acl_query_end[] = "%-12345X@PJL\xD\xA@PJL EOJ\xD\xA%-12345X\xD\xA";
static const uint8_t s_acl_pl_settime[] = "@PJL COMMENT Setting sleep time\xD\xA@PJL ENTER LANGUAGE=PANTUM\xD\xA";
static const uint8_t s_acl_pl_end[] = "%-12345X@PJL\xD\xA@PJL EOJ\xD\xA";

#if CONFIG_XC4_UPGRADE
static const uint8_t s_acl_licensor[] = "@PJL LICENSOR=SysAdmin\xD\xA";
#endif

#define LEN_UEL (sizeof(s_acl_uel)-1)
#define PROBE_SIZE (sizeof(s_acl_uel) + sizeof(s_acl_enterlang) - 2)
#define CLEAR_BUFFER_SIZE 0x2800 /* 10*1024 */
#define PARSER_CLEAR_TIMEOUT 1

#ifndef ACLPARSER_STACK_SIZE
#define ACLPARSER_STACK_SIZE PI_HUGE_STACK
#endif
#ifndef ACLPARSER_TASK_PRIORITY
#define ACLPARSER_TASK_PRIORITY PI_MEDIUM_PRIORITY
#endif

EVT_MGR_CLI_S *g_acl_cli_ptr;
int g_fw_upgrade_status = -1;

typedef struct
{
	struct list_head acl_list;
    uint32_t ACLCmd;              /*< The acl command this function processes. */
    acl_function_t ACLCmdProcess; /* <cmd process function. */
    void *cmd_data;               /* < the cmd_data pointer. */
} ACLCMDSTR_S;

typedef struct {
    struct list_head acl_list;
    void *Data;      ///< Where the data is located.
} ACL_LIST_S;

struct list_head  g_acl_cmds;     /* < The acl commands that are registered. > */
static PI_THREAD_T s_task_id;  /* < system task ID for this thread */
static PI_THREAD_T s_upgrade_task_id;
static void upgrade_handleMsg(uint32_t msg);
/* The align 8 below is because some of the function we could call use UINT64's. */
char g_acl_parse_string[] = "ACL";
PI_MUTEX_T nv_mtx;

/************************************************************
func: acl_construct_response_buffer
param:
return: ACL_RESPONSE_BASE_STRUCT_S pointer to the initialized structure
description:
	brief A helper function to construct the response buffer and do some init.
	* The response buffer is 16 bytes and consists of at least a status, id, and command.
	* This function allocates the buffer, initalizes the status to success, initializes
	* the id to 0xac, and sets the command according to acl_cmd.  Then it returns
	* the buffer.  Note that if no memory is available this will suspend till it
	* is able to allocate the buffer.
	* param[in] acl_cmd The acl_cmd to put into the response structure.
	* returns ACL_RESPONSE_BASE_STRUCT_S pointer to the initialized structure
author:
************************************************************/
ACL_RESPONSE_BASE_STRUCT_S *acl_construct_response_buffer(uint16_t acl_cmd)
{
    ACL_RESPONSE_BASE_STRUCT_S *buffer;

    buffer = (ACL_RESPONSE_BASE_STRUCT_S *)pi_malloc(sizeof(ACL_RESPONSE_BASE_STRUCT_S));
    if ( NULL == buffer )
    {
        pi_log_e(" malloc memory for ACL_RESPONSE_BASE_STRUCT_S failed \r\n");
        return buffer;
    }

    pi_memset(buffer, 0, sizeof(ACL_RESPONSE_BASE_STRUCT_S));
    buffer->prefix = cpu_to_be16(s_acl_prefix);
    buffer->cmd_status = cpu_to_be16(ACLCMD_STATUS_SUCCESS);
    buffer->cmd_id = cpu_to_be16(acl_cmd);

    return buffer;
}

static ACL_RESPONSE_ALL_SETTING_STRUCT_S *construct_acl_response_allset_buffer(uint16_t acl_cmd)
{
    ACL_RESPONSE_ALL_SETTING_STRUCT_S *buffer;

    buffer = (ACL_RESPONSE_ALL_SETTING_STRUCT_S *)pi_malloc(sizeof(ACL_RESPONSE_ALL_SETTING_STRUCT_S));
    if ( NULL == buffer )
    {
        pi_log_e(" malloc memory for ACL_CMD_BASE_STRUCT_S failed \r\n");
        return buffer;
    }

    pi_memset(buffer, 0, sizeof(ACL_RESPONSE_ALL_SETTING_STRUCT_S));
    buffer->prefix = cpu_to_be16(s_acl_prefix);
    buffer->cmd_status = cpu_to_be16(ACLCMD_STATUS_SUCCESS);
    buffer->cmd_id = cpu_to_be16(acl_cmd);

    return buffer;
}

#if 0
/************************************************************
func: job_cancel_process
param: io data stream from USB
return: int32_t ParseStatus
description:  brief cancel acl job.
author:	 jacky zeng
************************************************************/
static int32_t job_cancel_process(GQIO_S* pgqio)
{
    char buf[LEN_UEL];
    int32_t recv_length;

	/* read  data until UEL is read or data is NULL*/
    while (1)
    {
        pi_memset(buf, 0, LEN_UEL);
        recv_length = parser_common_read(pgqio, (uint8_t *)buf, LEN_UEL, PARSER_USB_TIMEOUT);

        if ((recv_length >= LEN_UEL) &&
            (pi_strncmp((char*)buf, (char *)s_acl_uel, LEN_UEL) == 0))
        {
            // We have read a UEL (possibly followed by other bytes).
            // This signals that it's time to exit from the ACL Parser.
            pi_log_d( "ACL:got a uel flag to exit acl parser\n");
            return PARSER_SUCCESS;
        }
        else if (recv_length <= 0)
        {
            pi_log_w( "ACL:got a uel flag failed\n");
            return PARSER_SUCCESS;
        }
        else if (recv_length < LEN_UEL)
        {
            pi_log_e("ACL:got a uel flag failed to get data = %d bytes\n", recv_length);
            return PARSER_ERROR;
        }
        else
        {
            ;
            //let's go on to read data
        }
    }
}
#endif

static int32_t job_clear_process(GQIO_S* pgqio)
{
    pi_log_d( "ACL QIO data is being cleared, because the ACL function returned error, please wait... \n");

    uint8_t *clear_buffer = NULL;
    int32_t recv_length = 0;
    int32_t clear_data = 0;

    // pqio is valid
    if (!pgqio)
    {
        pi_log_e("pgqio had been fred!\n");
        return PARSER_ERROR;
    }

    // every time read 10K data and release
    clear_buffer = (uint8_t *)pi_malloc(CLEAR_BUFFER_SIZE);
    if (!clear_buffer)
    {
        pi_log_e("Can't malloc 1M memory!\n");
        return PARSER_ERROR;
    }

    while(1)
    {
        // read 10K data
        pi_memset(clear_buffer, 0, CLEAR_BUFFER_SIZE);
        recv_length = parser_common_read(pgqio, (uint8_t *)clear_buffer, CLEAR_BUFFER_SIZE, PARSER_CLEAR_TIMEOUT);
        clear_data += recv_length;

        // can't read any data in 1 second
        if (recv_length <= 0)
        {
            pi_log_d( "All the data %d BYTE has been cleared!\n", clear_data);
            break;
        }
    }

    pi_free(clear_buffer);
    clear_buffer = NULL;

    return PARSER_SUCCESS;
}


static int32_t parser_pantum_command(GQIO_S* pgqio)
{
    bool is_acl = false;
    int32_t i, recv_len;
    int32_t length = pi_strlen((char *)s_acl_pl_settime) + pi_strlen((char *)s_acl_pl_end) + 6;
    uint8_t pjl_string[length];

	/* parser ENTERLANG */
    pi_memset(pjl_string, 0, sizeof(pjl_string));
    recv_len = parser_common_read(pgqio, pjl_string, length, PARSER_USB_TIMEOUT);
    if (recv_len != length)
    {
        (void)parser_common_rewind(pgqio, pjl_string, recv_len);
        pi_log_e("acl pantum enter settime read error!\n");
        return PARSER_ERROR;
    }

    if (!pi_strncmp((char *)pjl_string, (char *)s_acl_pl_settime, pi_strlen((char *)s_acl_pl_settime)))
    {
        is_acl = true;
    }

    if (!is_acl)
    {
        (void)parser_common_rewind(pgqio, pjl_string, recv_len);
        pi_log_e("acl enterlang is error, it is not a acl header data %s!\n", pjl_string);
        return PARSER_ERROR;
    }

    i = pi_strlen((char *)s_acl_pl_settime);

    {
        uint32_t sleep_time = pjl_string[i + 3] * 60; /* 60 minutes turned into seconds */
        //(void)oid_set_uint32(OID_PLATFORM_SLEEP_TIME, 0, sleep_time);
        pi_event_mgr_notify(g_acl_cli_ptr, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY,
	        (void *)sleep_time, sizeof(sleep_time));
    pi_log_i("sleep time is %d\n", sleep_time);
    }

    return PARSER_SUCCESS;
}

static int32_t parser_query_command(GQIO_S* pgqio)
{
    bool is_acl = false;
    int32_t i, recv_len;
    int32_t length = pi_strlen((char *)s_acl_enter_settime) + pi_strlen((char *)s_acl_query_end) + 6 ;
    uint8_t pjl_string[length];

	/* parser ENTERLANG */
    pi_memset(pjl_string, 0, sizeof(pjl_string));
    recv_len = parser_common_read(pgqio, pjl_string, length, PARSER_USB_TIMEOUT);
    if (recv_len != length)
    {
        (void)parser_common_rewind(pgqio, pjl_string, recv_len);
        pi_log_e("acl query enter settime read error!\n");
        return PARSER_ERROR;
    }

    if (!pi_strncmp((char *)pjl_string, (char *)s_acl_enter_settime, pi_strlen((char *)s_acl_enter_settime)))
    {
        is_acl = true;
    }

    if (!is_acl)
    {
        (void)parser_common_rewind(pgqio, pjl_string, recv_len);
        return parser_pantum_command(pgqio);
    }

    i = pi_strlen((char *)s_acl_enter_settime);

    {
        uint32_t sleep_time = pjl_string[i + 3] * 60; /* 60 minutes turned into seconds */
        //(void)oid_set_uint32(OID_PLATFORM_SLEEP_TIME, 0, sleep_time);
        pi_event_mgr_notify(g_acl_cli_ptr, EVT_TYPE_PANEL_SLEEP_TIME_MODIFY,
		    (void *)sleep_time, sizeof(sleep_time));
        pi_log_i( "sleep time is %d\n", sleep_time);
    }

    return PARSER_SUCCESS;
}

/************************************************************
func: parse_acl
param: io data stream from USB
return: int32_t ParseStatus
description: brief parse an acl command with the acl system
    This function is entry for parse an acl command, check data
    is acl cmd or not, if it is, ivoke ProcessACL.
author:	 jacky zeng
************************************************************/
static int32_t parse_acl(GQIO_S* pgqio)
{
    int32_t probe_size = pi_strlen((char *)s_acl_uel);
    bool is_acl = false;
    uint8_t probe_buf[probe_size];
    bool is_uel = false;
    int32_t recv_length;
    ACL_CMD_BASE_STRUCT_S acl_Cmd;
    int32_t enterlang_len = pi_strlen((char *)s_acl_enterlang);
    uint8_t enterlang[enterlang_len];
#if CONFIG_XC4_UPGRADE
    int32_t enterlicensor_len = pi_strlen((char *)s_acl_licensor);
    uint8_t licensor[enterlicensor_len];
	ROUTER_MSG_S send_msg;
#endif
    //ROUTER_MSG_S send_msg;
    int32_t ret = PARSER_SUCCESS;

	/* parser data whether begin with UEL */
    while (!is_uel)
    {
        pi_memset(probe_buf, 0, sizeof(probe_buf));
        recv_length = parser_common_read(pgqio, probe_buf, probe_size, PARSER_USB_TIMEOUT);
        if (recv_length != probe_size)
        {
            pi_log_e("ACL CMD Data length is too short!\n");
            return PARSER_ERROR;
        }
        int32_t i = 0;
        if (probe_buf[i] == 0x1b &&
            probe_buf[i + 1] == '%' &&
            probe_buf[i + 2] == '-' &&
            probe_buf[i + 3] == '1' &&
            probe_buf[i + 4] == '2' &&
            probe_buf[i + 5] == '3' &&
            probe_buf[i + 6] == '4' &&
            probe_buf[i + 7] == '5' &&
            probe_buf[i + 8] == 'X')
        {
            // Found UEL
            is_uel = true;
            pi_log_d("acl cmd head is OK !\n");
            break;
        }
    }

    /* parser ENTERLANG */
    pi_memset(enterlang, 0, sizeof(enterlang));
    recv_length = parser_common_read(pgqio, enterlang, enterlang_len, PARSER_USB_TIMEOUT);
    if (recv_length != enterlang_len)
    {
        (void)parser_common_rewind(pgqio, enterlang, recv_length);
        pi_log_e("acl enterlang read error!\n");
        return PARSER_ERROR;
    }

    if (!pi_strncmp((char *)enterlang, (char *)s_acl_enterlang, (uint32_t)enterlang_len))
    {
        is_acl = true;
        pi_log_d("is_acl is true !\n");
    }

    if (!is_acl)
    {
        (void)parser_common_rewind(pgqio, enterlang, recv_length);
        return parser_query_command(pgqio);
    }
/*if acl cmd is 000f, parser licensor cmd*/
#if CONFIG_XC4_UPGRADE
    pi_memset(licensor, 0, enterlicensor_len);
    recv_length = parser_common_read(pgqio, licensor, enterlicensor_len, PARSER_USB_TIMEOUT);
    if(recv_length != enterlicensor_len)
    {
        (void)parser_common_rewind(pgqio, licensor, enterlicensor_len);
        pi_log_e("ACL Licensor read error!\n");
        return PARSER_ERROR;
    }
    /*parser acl licensor*/
    if( (licensor[0] != 0x00) && (licensor[1] != 0xAC))
    {
        if( pi_strncmp((char *)licensor, (char *)s_acl_licensor, enterlicensor_len) )
        {
            (void)parser_common_rewind(pgqio, licensor, enterlicensor_len);
            pi_log_e("acl LICENSOR is error, it is not a acl licensor header data!\n");
            return PARSER_ERROR;
        }
        else
        {
            pi_log_d("ACL PARSER:acl licensor data lists as  %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X\n",
            licensor[0],licensor[1],licensor[2],licensor[3],licensor[4],licensor[5],licensor[6],licensor[7],licensor[8],licensor[9],licensor[10],
            licensor[11],licensor[12],licensor[13],licensor[14],licensor[15],licensor[16],licensor[17],licensor[18],licensor[19],licensor[20]);
        }
    }
    else
    {
        pi_log_d("it is a ACL cmd without licensor\n");
        (void)parser_common_rewind(pgqio, licensor, enterlicensor_len);
        if((licensor[2] == 0x00) && (licensor[3] == 0x0F))
        {
            send_msg.msgType = MSG_FW_UPGRATE_PARSER_FAIL;
            send_msg.msg1 = 0;
            send_msg.msg2 = 0;
            send_msg.msg3 = NULL;
            send_msg.msgSender = MID_PARSER_ACL;
            if (task_msg_send_by_router( MID_PANEL, &send_msg) != 0)
            {
                pi_log_e("Send PARSER FAIL to PANEL error!\n");
            }
			return PARSER_FUNCRET_ERROR;
        }
    }
#endif
/*parser licensor cmd end*/
    /* let's go our usb acl  parser */
    while (1)
    {

        /* At this point the things that could legally be in the input stream are:
         *   - a 16 byte ACL command (all ACL commands begin with a known 2 byte prefix)
         *   - a UEL that ends the prior ACL command

         * The only way to distinquish these cases is to start reading from the
         * input pipe.  But we must be careful to not insist upon reading more
         * bytes than may be available, and we must consume the UEL that
         * terminates the block of ACL commands before we return to our caller.

         * The PARSEREAD_SOME option to ReadConnect() blocks until at least
         * 1 byte is available.  However we also specify that no more than
         * LEN_ACL_CMD bytes should be returned. */
        pi_memset(&acl_Cmd, 0, sizeof(ACL_CMD_BASE_STRUCT_S));
        recv_length = parser_common_read(pgqio, (uint8_t *)&acl_Cmd, LEN_ACL_CMD, PARSER_USB_TIMEOUT);  //LEN_UEL->LEN_ACL_CMD

        if ((recv_length >= LEN_UEL) &&
            (pi_strncmp((char*)&acl_Cmd, (char *)s_acl_uel, LEN_UEL) == 0 ))
        {
            /* We have read a UEL (possibly followed by other bytes). */
            /* This signals that it's time to exit from the ACL Parser. */
            pi_log_d( "ACL:got a uel flag to exit acl parser\n");
            if( recv_length > LEN_UEL )
                (void)parser_common_rewind(pgqio, (uint8_t *)&acl_Cmd , (recv_length-LEN_UEL));
            return PARSER_SUCCESS;
        }
        else if (recv_length <= 0)
        {
            pi_log_w( "ACL:got a uel flag failed\n");
            return PARSER_SUCCESS;
        }
        else if (recv_length < LEN_UEL)
        {
            pi_log_e("ACL:got a uel flag failed to get data = %d bytes\n", recv_length);
            (void)parser_common_rewind(pgqio, (uint8_t *)&acl_Cmd , recv_length);
            return PARSER_ERROR;
        }
        else if (recv_length < LEN_ACL_CMD)
        {
            /* We also want to exit from the ACL Parser if we receive illegal input. */
            /* In this case we want to rewind everything we have read back into the */
            /* channel so it is available to the next parser. */
            pi_log_e("ACL: Less than 16 bytes received\n");
            (void)parser_common_rewind(pgqio, (uint8_t *)&acl_Cmd , recv_length);
            return PARSER_ERROR;
        }
        else if (be16_to_cpu(acl_Cmd.prefix) != s_acl_prefix)
        {
            /* We also want to exit from the ACL Parser if we receive illegal input. */
            /* In this case we want to rewind everything we have read back into the */
            /* channel so it is available to the next parser. */
            pi_log_e("ACL: ACL_Prefix not found%x %x %x\n", acl_Cmd.prefix, be16_to_cpu(acl_Cmd.prefix), s_acl_prefix);
            (void)parser_common_rewind(pgqio, (uint8_t *)&acl_Cmd , recv_length);
            return PARSER_ERROR;
        }
        else
        {
            //pi_log_d( "ACL: Received CMD=%04X\n", be16_to_cpu(acl_Cmd.cmd_id));
        }
        /* Look for the command in the table and execute it if possible. */
        /* This checks every registered acl command and if it finds one */
        /* calls the function tied to that command. */

        ACLCMDSTR_S *temp_acl;

        /* Try and find the command in the table. */
		struct list_head *pos, *n;
		pi_list_for_each_safe(pos, n, &g_acl_cmds)
        {
        	temp_acl = pi_list_entry(pos, ACLCMDSTR_S, acl_list);
            if (temp_acl->ACLCmd == be16_to_cpu(acl_Cmd.cmd_id))
            {
                /* We found the command.  Process it now. */
                pi_log_d( "ACL PARSER:acl cmd matched 0x%04x\n",be16_to_cpu(acl_Cmd.cmd_id));
                pi_log_d( "ACL PARSER:acl cmd data %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x %02x\n",
                        acl_Cmd.temp[0],
                        acl_Cmd.temp[1],
                        acl_Cmd.temp[2],
                        acl_Cmd.temp[3],
                        acl_Cmd.temp[4],
                        acl_Cmd.temp[5],
                        acl_Cmd.temp[6],
                        acl_Cmd.temp[7],
                        acl_Cmd.temp[8],
                        acl_Cmd.temp[9],
                        acl_Cmd.temp[10],
                        acl_Cmd.temp[11]);
                ret = temp_acl->ACLCmdProcess(pgqio, &acl_Cmd, temp_acl->cmd_data);
                if ((PARSER_SUCCESS != ret) && (REGISTER_SUCCESS != ret))
                {
                    pi_log_e("ACL: Cmd function return error!\n");
                    return PARSER_FUNCRET_ERROR;
                }

                break;  /* done. */
            }
        }
		if (pos == &g_acl_cmds)
		{
             /* We looked through our registered commands and we do not have one
             * for this code.  Return a failure to the host.  We then return
             * a parser error.  This will force the connection manager to
             * read data till it finds something it recognizes.  So if there
             * is data for this command the connection manager will eat it.
             *
             * Send a failure response and set up a failure for this command. */
            acl_direct_response(pgqio, &acl_Cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
            pi_log_e("ACL: Received unhandled CMD=%x\n", be16_to_cpu(acl_Cmd.cmd_id));
            /* continue reading the acl */

		}
    }
}


static void *upgrade_task(void *argv)
{
    ROUTER_MSG_S rev_msg;
    int32_t ret = PARSER_ERROR;
    //GQIO_S* gqio = NULL;

    while (1)
    {
        // wait for something to do
        ret = task_msg_wait_forever_by_router(MID_UPGRADE, &rev_msg);
        if (ret < 0)
        {
            pi_log_e("msg MID_PARSER_ACL wait failed = %d\n", ret );
            break;
        }

        switch (rev_msg.msgType)
        {
            case MSG_FW_UPGRATE_CONFIRM_SEC:
                g_fw_upgrade_status = rev_msg.msg1;
            break;
        };
    }
    pi_log_w( "Ends!\n");
    return NULL;
}

static void *acl_parser_task(void *argv)
{
    ROUTER_MSG_S rev_msg;
    ROUTER_MSG_S snd_msg;
    int32_t ret = PARSER_ERROR;
    GQIO_S* gqio = NULL;
    CURRENT_RESOURCE_DATA_S *my_resource = NULL;
    JOB_RESOURCES_DATA_S *my_sys_job = NULL;
    MODULE_ID_E prev_module = MID_INVALID;
    /*JOB_REQUEST_DATA_S *job_request = NULL;*/

    while (1)
    {
        // wait for something to do
        ret = task_msg_wait_forever_by_router(MID_PARSER_ACL, &rev_msg);
        if (ret < 0)
        {
            pi_log_e("msg MID_PARSER_ACL wait failed = %d\n", ret );
            break;
        }

        switch (rev_msg.msgType)
        {
/*            case MSG_CTRL_QIO_START:*/

                /*gqio = (GQIO_S*)rev_msg.msg3;*/
                /*if (NULL == gqio)*/
                /*{*/
                    /*pi_log_e("A NULL GQIO received!\r\n");*/
                    /*return PARSER_ERROR;*/
                /*}*/
                /*// send job request to system job mgr*/
                /*job_request = (JOB_REQUEST_DATA_S*)pi_malloc(sizeof(JOB_REQUEST_DATA_S));*/
                /*if (NULL == job_request)*/
                /*{*/
                    /*pi_log_e("malloc JOB_REQUEST_DATA_S failed!\n");*/
                    /*return PARSER_ERROR;*/
                /*}*/

                /*pi_memset(job_request, 0, sizeof(JOB_REQUEST_DATA_S));*/
                /*job_request->gqio = gqio;*/
                /*job_request->job_type = JOB_TYPE_PRINT_ACL;*/
                /*job_request->wait_type = JOB_WAIT_FOREVER;*/
                /*job_request->start_module = false;*/
                /*job_request->request_module = MID_PARSER_ACL;*/

                /*snd_msg.msgType = MSG_CTRL_JOB_REQUEST;*/
                /*snd_msg.msg1 = SYS_REQUEST;*/
                /*snd_msg.msg2 = 0;*/
                /*snd_msg.msg3 = (void *)job_request;*/
                /*snd_msg.msgSender = MID_PARSER_ACL;*/
                /*if (task_msg_send_by_router(MID_SYS_JOB_MGR, &snd_msg) != 0)*/
                /*{*/
                    /*pi_log_e("Send job request to SJM error!\n");*/
                /*}*/
                /*break;*/

            case MSG_CTRL_RESOURCE_LINK:
                my_resource =( CURRENT_RESOURCE_DATA_S* )rev_msg.msg3;
                my_sys_job = (JOB_RESOURCES_DATA_S*)(my_resource->job_resource_data);
                prev_module =  my_resource->prev_module;
                gqio = (GQIO_S*)(my_sys_job->gqio);
                break;

            case MSG_CTRL_RESOURCE_START:
				/* the first node begin JOB START*/
                pi_log_e("prev_module = %s \n",get_module_id_string(prev_module));
                if (MID_NULL == prev_module)  //MID_INVALID->MID_NULL
                {
                    /* send to this module to begin JOB START */
                    snd_msg.msgType = MSG_CTRL_JOB_START;
                    snd_msg.msg1 = 0;
                    snd_msg.msg2 = 0;
                    snd_msg.msg3 = gqio;
                    snd_msg.msgSender = MID_PARSER_ACL;
                    if (task_msg_send_by_router( MID_PARSER_ACL, &snd_msg) != 0)
                    {
                        pi_log_e("Send job star to ACL error!\n");
                    }
                }
                else
                {
                    pi_log_e("ACL is not the first module, it's wrong!\r\n");
                }
                break;

             case MSG_CTRL_JOB_START:
                    pi_log_d( "recv msg job detail\n");
                    if (gqio)
                    {
                        ret = parse_acl(gqio);
                        if (ret != PARSER_SUCCESS)
                        {
                            pi_log_e("ERROR:ACL Parser ERROR!\n");

                            if (PARSER_FUNCRET_ERROR == ret)
                            {
                                // clear all the data of the qio
                                (void)job_clear_process(gqio);
                            }
                        }
                    }
                    else
                    {
                        pi_log_e(" MSG_CTRL_JOB_START sysjob is NULL or pgqio is NULL\n");
                    }

                    snd_msg.msgType = MSG_CTRL_RESOURCE_FREE;
                    snd_msg.msg1 = my_resource->job_id;
                    snd_msg.msg2 = my_resource->resource_id;
                    snd_msg.msg3 = NULL;
                    snd_msg.msgSender = MID_PARSER_ACL;
                    if (task_msg_send_by_router( MID_SYS_JOB_MGR, &snd_msg) != 0)
                    {
                        pi_log_e("Send resource free to SJM error!\n");
                    }
                break;

            case MSG_CTRL_QIO_END:
                snd_msg.msgType =rev_msg.msgType;
                snd_msg.msg1 = rev_msg.msg1;
                snd_msg.msg2 = rev_msg.msg2;
                snd_msg.msg3 = rev_msg.msg3;
                snd_msg.msgSender = MID_PARSER_ACL;

                // just transfer the msg to connect mgr
                if (task_msg_send_by_router(MID_CONNECT_MGR, &snd_msg) != 0)
                {
                    pi_log_e("Send qio end to CNM error!\n");
                }
                break;

            case MSG_PORT9120_RESPONSE:
            {
                GQIO_S* pgqio = (GQIO_S *)rev_msg.msg3;
                if (pgqio != NULL)
                {
                    ret = parse_acl(pgqio);
                    if (ret != PARSER_SUCCESS)
                    {
                        pi_log_e("ACL Parser Error from MSG_PORT9120_RESPONSE!\n");
                        if (ret == PARSER_FUNCRET_ERROR)
                        {
                            job_clear_process(pgqio);
                        }
                    }
                    QIO_CLOSE(pgqio);
                }
                break;
            }

            default:
                pi_log_e(" msgType ERROR[%d] \n", rev_msg.msgType );
                break;
        }
    };
    pi_log_w( "Ends!\n");
    return NULL;
}

/************************************************************
func: probe_acl
param: io data stream from USB
return: int32_t ParseStatus
description: brief probe io stream, check it is an acl command or not.
author:	 jacky zeng
************************************************************/
static int32_t probe_acl(unsigned char *pData, uint32_t buff_size, void *handle , PREPARSER_RESULT_P result)
{
    (void)handle;
    int32_t i = 0;
    if (pData[i])
    {
        if (pData[i] == 0x1b &&
            pData[i + 1] == '%' &&
            pData[i + 2] == '-' &&
            pData[i + 3] == '1' &&
            pData[i + 4] == '2' &&
            pData[i + 5] == '3' &&
            pData[i + 6] == '4' &&
            pData[i + 7] == '5' &&
            pData[i + 8] == 'X')
        {
            // Found UEL, now probe next data;
            int32_t j, k, acl_len;

            acl_len = pi_strlen((char *)s_acl_enterlang);
            for (j = i + 9, k = 0; k < acl_len; k++, j++)
            {
                if (pData[j] != s_acl_enterlang[k])
                {
                    break;
                }
            }
            if (k == acl_len)
            {
                result->job_type = JOB_TYPE_PRINT_ACL;
                pi_log_d( "[%d] is ACL JOB\n",__LINE__);
                return PREPARSER_REGISTER_SUCCESS;   //1->PREPARSER_REGISTER_SUCCESS
            }

            acl_len = pi_strlen((char *)s_acl_enter_settime);
            for (j = i + 9, k = 0; k < acl_len; k++, j++)
            {
                if (pData[j] != s_acl_enter_settime[k])
                {
                    break;
                }
            }
            if (k == acl_len)
            {
                result->job_type = JOB_TYPE_PRINT_ACL;
                pi_log_d( "ACL QUERY_ENTERSETTIME\n");
                return PREPARSER_REGISTER_SUCCESS;   //1->PREPARSER_REGISTER_SUCCESS
            }

            acl_len = pi_strlen((char *)s_acl_pl_settime);
            for (j = i + 9, k = 0; k < acl_len; k++, j++)
            {
                if (pData[j] != s_acl_pl_settime[k])
                {
                    break;
                }
            }
            if (k == acl_len)
            {
                result->job_type = JOB_TYPE_PRINT_ACL;
                pi_log_d( "ACL PANTUM language.\n");
                return PREPARSER_REGISTER_SUCCESS;   //1->PREPARSER_REGISTER_SUCCESS
            }
        }
    }
    return PREPARSER_REGISTER_ERROR;   //0->PREPARSER_REGISTER_ERROR
}

/************************************************************
func: acl_register_cmd
param: param ACLCmd The command value.
        param AclProcess The fuction to call when this command is invoked.
        param Reserved
return: int32_t ParseStatus
description: brief Register an acl command with the parser.
author:	 jacky zeng
************************************************************/
int32_t acl_register_cmd(uint32_t acl_cmd, acl_function_t acl_Process, void *cmd_data)
{
    ACLCMDSTR_S *temp_link;
    ACLCMDSTR_S *temp_acl;
    pi_mutex_lock(nv_mtx);

    // see if this cmd already is registered.
	struct list_head *pos, *n;
	pi_list_for_each_safe(pos, n, &g_acl_cmds)
    {
    	temp_acl = pi_list_entry(pos, ACLCMDSTR_S, acl_list);
        if (temp_acl->ACLCmd == acl_cmd)
        {
            pi_log_d( "ACL Cmd already registered\n");
            pi_mutex_unlock(nv_mtx);
            return REGISTER_SUCCESS;   //"ACL Cmd already registered"
        }
    }

    // Add a new acl command to the command list.
    temp_link = (ACLCMDSTR_S*)pi_malloc(sizeof(ACLCMDSTR_S));
    if (temp_link == NULL)
    {
        pi_log_e("memory malloc error\n");
        pi_mutex_unlock(nv_mtx);
        return MEM_MALLOC_ERROR; //"memory malloc error"
    }
    pi_memset(temp_link, 0x00, sizeof(ACLCMDSTR_S));

    temp_link->acl_list.prev = NULL;
	temp_link->acl_list.next = NULL;
    temp_link->ACLCmd = acl_cmd;
    temp_link->ACLCmdProcess = acl_Process;
    temp_link->cmd_data = cmd_data;
	pi_list_add_tail(&(temp_link->acl_list), &g_acl_cmds);
    PARSER_LOG_D("ACL: Added command %04X\n", acl_cmd);
    pi_mutex_unlock(nv_mtx);
    return REGISTER_SUCCESS;   //"ACL Cmd registered successfully"
}

/************************************************************
func: acl_direct_response
param: param[in] PQIO:io stream, send data to this io stream.
    param[in] ACL_CMD_BASE_STRUCT_S *aclCmd: the acl cmd which you processed.
    param[in] uint16_t status: the status which you process the acl cmd.
    param[in] uint8_t *responseData: the data which you want to response.
    param[in] uint32_t *dataLen: the response data length.
return: void
description:   brief after process an acl command, construct acl response cmd,
    and you can response status and data to terminate.
    This function response an acl status to terminate.
author:	 jacky zeng
************************************************************/
void acl_direct_response(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, int16_t status, uint8_t *response_data, uint32_t data_len)
{
    ACL_RESPONSE_BASE_STRUCT_S *buffer;

    buffer = acl_construct_response_buffer(be16_to_cpu(acl_cmd->cmd_id));
    buffer->cmd_status = cpu_to_be16((uint16_t)status);
    buffer->data_len = cpu_to_be16((uint16_t)data_len);

    PARSER_LOG_D("acl_response: response %x status=%d data_len=%d\n", acl_cmd->cmd_id, status, data_len);
    (void)parser_common_write(pgqio, (uint8_t *)buffer, sizeof(ACL_RESPONSE_BASE_STRUCT_S), PARSER_USB_TIMEOUT);
    if (response_data)
    {
        (void)parser_common_write(pgqio, response_data, data_len, PARSER_USB_TIMEOUT);
    }
    pi_free(buffer);
    buffer = NULL;
}

void acl_merge_response(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, int16_t status, uint8_t *response_data, uint32_t data_len)
{
    ACL_RESPONSE_ALL_SETTING_STRUCT_S *buffer = NULL;

    buffer = construct_acl_response_allset_buffer(be16_to_cpu(acl_cmd->cmd_id));
    buffer->cmd_status = cpu_to_be16((uint16_t)status);
    buffer->data_len = cpu_to_be16((uint16_t)data_len);
    if (NULL != response_data)
    {
        pi_memcpy(buffer->getdata, response_data, sizeof(buffer->getdata));
    }
    PARSER_LOG_D("acl_merge_response: response %x status=%d data_len=%d\n", acl_cmd->cmd_id, status, data_len);
    (void)parser_common_write(pgqio, (uint8_t *)buffer, data_len, PARSER_USB_TIMEOUT);
    pi_free(buffer);
    buffer = NULL;
}

/************************************************************
func: acl_response
param: param[in] PQIO:io stream, send data to this io stream.
	param[in] ACL_RESPONSE_BASE_STRUCT_S *Buffer: the response cmd
	param[in] uint8_t *responseData: the data which you want to response.
	param[in] uint32_t *dataLen: the response data length.
return: void
description:  brief send response cmd and data to terminate.
    This function send response cmd and data to terminate.
author:	 jacky zeng
************************************************************/
void acl_response(GQIO_S* pgqio, ACL_RESPONSE_BASE_STRUCT_S *buffer, uint8_t *response_data, uint32_t data_len)
{
    PARSER_LOG_D("acl_response: response %x status=%d data_len=%d\n", buffer->cmd_id, buffer->cmd_status, data_len);
    (void)parser_common_write(pgqio, (uint8_t *)buffer, sizeof(ACL_RESPONSE_BASE_STRUCT_S), PARSER_USB_TIMEOUT);
    if (response_data)
    {
        (void)parser_common_write(pgqio, response_data, data_len, PARSER_USB_TIMEOUT);
    }
}

static void acl_event_callback(const EVT_MSG_S* msg, void* ctx)
{
    //acl_common_event_callback(msg);
}

static void acl_event_process()
{

	g_acl_cli_ptr = pi_event_mgr_create_client(EVT_MODULE_ACL_PARSER, acl_event_callback, NULL, NULL);
	if (g_acl_cli_ptr == NULL) {
		pi_log_e("acl create event mgr client fail\n");
		//return PARSER_ERROR;
	}
}

/************************************************************
func: get_fw_upgrade_status
param: void
return: the status of panel return
description:return the status og upgrade
author:	 zhangjie
************************************************************/
int get_fw_upgrade_status(void)
{
	return g_fw_upgrade_status;
}

/************************************************************
func: set_fw_upgrade_status
param: void
return: int32_t ParseStatus
description:return the status og upgrade
author:	 zhangjie
************************************************************/
void set_fw_upgrade_status(int status)
{
	g_fw_upgrade_status = status;
}

/************************************************************
func: upgrade_handleMsg
param: void
return: int32_t ParseStatus
description:return the status og upgrade
author:	 zhangjie
************************************************************/
static void upgrade_handleMsg(uint32_t msg)
{
    set_fw_upgrade_status(msg);
}

/************************************************************
func: acl_parser_prolog
param: void
return: int32_t ParseStatus
description:brief Initialize the acl parser
	This inits the command structure, Registers the common acl commands.
author:	 jacky zeng
************************************************************/
int32_t acl_parser_prolog(void)
{
    int32_t ret = 0;
    nv_mtx = pi_mutex_create();

    ret = msg_router_register(MID_PARSER_ACL);
    if (ret < 0)
    {
        pi_log_e("msg_router_register(MID_PARSER_ACL failed\n");
        return -1;
    }

    s_task_id = pi_thread_create(
            acl_parser_task,
            ACLPARSER_STACK_SIZE,
            NULL,
            ACLPARSER_TASK_PRIORITY,
            NULL,
            "acl_parser_task");
    if (s_task_id == INVALIDTHREAD)
    {
        pi_log_e("Can't start acl parser task\n");
        msg_router_unregister(MID_PARSER_ACL);
        return PARSER_ERROR;
    }

    //????,????
    ret = msg_router_register(MID_UPGRADE);
    if (ret < 0)
    {
        pi_log_e("msg_router_register(MID_UPGRADE failed\n");
        return -1;
    }

    s_upgrade_task_id = pi_thread_create(
            upgrade_task,
            ACLPARSER_STACK_SIZE,
            NULL,
            ACLPARSER_TASK_PRIORITY,
            NULL,
            "upgrade_task");
    if (s_upgrade_task_id == INVALIDTHREAD)
    {
        pi_log_e("Can't start acl parser task\n");
        msg_router_unregister(MID_UPGRADE);
        return PARSER_ERROR;
    }

    init_Parser_Debug_Mode();
    pi_init_list_head(&g_acl_cmds);   // initialize the command structure
	acl_event_process();

    /*if( pre_parser_register(MID_PARSER_ACL, probe_acl, PROBE_SIZE, ACL_PRIORITY) != PREPARSER_REGISTER_SUCCESS )*/
    if( pre_parser_register1(probe_acl, PROBE_SIZE , ACL_PARSER) != PREPARSER_REGISTER_SUCCESS )
        return -1;
    //(void)resource_register(MID_PARSER_ACL, 1);                                         //暂时注释 20230411      system job

    return ret;
}

int32_t acl_parser_epilog(void)
{
    //if (ACLParserTaskPtr)
    {
        msg_router_unregister(MID_PARSER_ACL);
        //MsgRouterUnregister(MID_PARSER_ACL, ACLParserTaskPtr->s_mailbox);
        //TSdestroyThread(ACLParserTaskPtr.s_task_id);
        //ACLParserTaskPtr->s_task_id = INVALIDTHREAD;
        //TSdestroyMsgQueue(ACLParserTaskPtr.s_mailbox);
        //free(ACLParserTaskPtr);
        //linkedFree(&g_acl_cmds);
        return PARSER_SUCCESS;
    }

    // Delete by oujian because of pclint (unreachable)
    //return PARSER_ERROR;
}

/*
 * End of acl.c
 */

