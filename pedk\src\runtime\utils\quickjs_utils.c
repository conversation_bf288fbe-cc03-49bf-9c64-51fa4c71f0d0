/*
 * @Author: your name
 * @Date: 2023-12-22 09:38:35
 * @LastEditTime: 2024-01-19 17:16:38
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \runtime\src\runtime\utils\quickjs_utils.c
 */
#include "runtime/utils/quickjs_utils.h"

#include <stdlib.h>
#include <string.h>
#include "basic/config.h"
#include "basic/log/log.h"
#include <quickjs-libc.h>

#define RUMTIME_TAG "runtime"

int quickjs_load_js_file(JSContext *ctx, const char *file_path)
{
    if (file_path == NULL)
    {
        LOG_E(RUMTIME_TAG, "Load js error: file_path is NULL");
        return -1;
    }
    FILE *file = NULL;
    file = fopen(file_path, "r");
    if ( NULL == file )
    {
        LOG_E(RUMTIME_TAG, "Failed to open file: %s", file_path);
        return -1;
    }

    char *buf = malloc(JS_FILE_BUF_SIZE);

    if ( NULL == buf )
    {
        LOG_F(RUMTIME_TAG, "Malloc memory failed!");
        fclose(file);
        file = NULL;
        return -1;
    }

    memset(buf, 0x00, JS_FILE_BUF_SIZE);

    size_t bytes_read = fread(buf, 1, JS_FILE_BUF_SIZE, file);
    fclose(file);
    file = NULL;
    //LOG_I(RUMTIME_TAG, "Load JS file: %s", file_path);

    if (bytes_read > 0)
    {
        JSValue ret = JS_Eval(ctx, buf, bytes_read, file_path, JS_EVAL_TYPE_MODULE);
        quickjs_print_exception(ctx);
        JS_FreeValue(ctx, ret);
    }
    else
    {
        LOG_W(RUMTIME_TAG, "JS file is empty");
    }

    free(buf);
    buf = NULL;

    return 0;
}

//js发生异常时，从栈中获取错误消息
void quickjs_print_exception(JSContext *ctx)
{
    // show exception
    JSValue exception = JS_GetException(ctx);

    if (JS_IsError(ctx, exception) && !JS_IsNull(exception)) {
        const char *exception_str = JS_ToCString(ctx, exception);
        JSValue stack = JS_GetPropertyStr(ctx, exception, "stack");
        const char *stack_str = JS_ToCString(ctx, stack);
        
        LOG_E("JavaScript", "\n\n%s\n%s", exception_str, stack_str);

        JS_FreeCString(ctx, stack_str);
        JS_FreeValue(ctx, stack);
        JS_FreeCString(ctx, exception_str);
    }

    JS_FreeValue(ctx, exception);
}

static void quickjs_utils_dump_obj(JSContext *ctx, FILE *f, JSValueConst val)
{
    const char *str;
    str = JS_ToCString(ctx, val);
    if (str) {
        //fprintf(f, "%s\n", str);
        LOG_E("dump_error", "%s", str);
        JS_FreeCString(ctx, str);
    } else {
        fprintf(f, "[exception]\n");
    }
}

static void quickjs_utils_std_dump_error1(JSContext *ctx, JSValueConst exception_val)
{
    JSValue val;
    int is_error;

    is_error = JS_IsError(ctx, exception_val);
    quickjs_utils_dump_obj(ctx, stderr, exception_val);

    if (is_error) {
        val = JS_GetPropertyStr(ctx, exception_val, "stack");
        if (!JS_IsUndefined(val)) {
            quickjs_utils_dump_obj(ctx, stderr, val);
        }
        JS_FreeValue(ctx, val);
    }
}

void quickjs_utils_std_dump_error(JSContext *ctx)
{
    JSValue exception_val;
    
    exception_val = JS_GetException(ctx);
    quickjs_utils_std_dump_error1(ctx, exception_val);
    JS_FreeValue(ctx, exception_val);
}