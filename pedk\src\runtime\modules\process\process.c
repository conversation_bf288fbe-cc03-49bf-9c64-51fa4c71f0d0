/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file process.c
 * @addtogroup process
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief process init 
 */
 
#include "runtime/modules/process/process.h"
#include "runtime/modules/module_manager.h"
#include "runtime/modules/process/on.h"
#include "runtime/utils/uthash_utils.h"
#include "basic/sys/sys.h"
#include "trans/transmission.h"
#include "manager/format.h"

/**
 * @brief js脚本自己退出
 * 
 */
JSValue js_process_exit(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    int exit_code = 1;

    if (argc >= 1) {
        JSValue arg0 = argv[0];

        if (JS_IsNumber(arg0)) {
            JS_ToInt32(ctx, &exit_code, arg0);
        }

        JS_FreeValue(ctx, arg0);
    }

    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    runtime_stop_loop(prt, exit_code);
    return JS_UNDEFINED;
}

/**
 * @brief js脚本中，发送重启打印机命令
 * 
 */
JSValue js_process_reset_printer(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    //发送重启打印机消息
    uint8_t buf[3];
    uint16_t len;
    printf("js_process_reset_printer\n");
    make_reset_printer_buf(buf,&len);
    transport_send(buf,len);

    return JS_UNDEFINED;
}

/**
 * @brief js脚本获取自己app的名字
 *        心跳脚本特殊，在启动心跳脚本前，会将自己的app名，替换成异常app的名字，然后再启动
 * 
 */
JSValue js_get_app_name(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);

    JSValue value = JS_NewString(prt->qjs_ctx, prt->app_name);

    return value;
}

static void process_init()
{
    LOG_I("process","process_init\n");
}

static void process_release()
{
    LOG_I("process","process_release\n");
}

static void process_instance(PeSFRunTime *prt)
{
    JSContext *ctx = prt->qjs_ctx;

    JSValue global = JS_GetGlobalObject(ctx);

    JSValue process = JS_NewObject(ctx);
    JS_SetPropertyStr(ctx, process, "exit", JS_NewCFunction(ctx, js_process_exit, "exit", 1));
	JS_SetPropertyStr(ctx, process, "resetPrinter", JS_NewCFunction(ctx, js_process_reset_printer, "resetPrinter", 0));
    JS_SetPropertyStr(ctx, process, "on_back", JS_NewCFunction(ctx, js_process_on_back, "on_back", 0));
    JS_SetPropertyStr(ctx, process, "on_front", JS_NewCFunction(ctx, js_process_on_front, "on_front", 0));
    JS_SetPropertyStr(ctx, process, "getAppName", JS_NewCFunction(ctx, js_get_app_name, "getAppName", 0));
    
    JS_SetPropertyStr(ctx, global, "process", process);

    // 释放 global 对象的引用
    JS_FreeValue(ctx, global);
}

static void process_generalization(PeSFRunTime *prt)
{

}

static ModuleContext process = {
    .module_name = "process",
    .module_init = process_init,
    .module_release = process_release,
    .module_instance = process_instance,
    .module_generalization = process_generalization
};

MODULE_REGISTER(process);
/**
 * @}
 */
 
