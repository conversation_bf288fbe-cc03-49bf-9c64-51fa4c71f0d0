/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file platform_pedk.c
 * @addtogroup system_manager
 * @{
 * @addtogroup status_pedk
 * <AUTHOR>
 * @date 2024-06-06
 * @brief PEDK API about system status
 *
 */
#include <signal.h>
#include <time.h>
#include <stdlib.h>
#include "pedk_mgr.h"
#include "statusid.h"
#include "cjson/cJSON.h"
#include "pol/pol_mem.h"
#include "pol/pol_log.h"
#include "pol/pol_string.h"
#include "status_manager.h"
#include "status_pedkapi.h"

#define PEDK_UNDEFINE           0                       ///< PEDK API doc undefine ：Invalid ID
#define MAX_STATUS_LEN          256                     ///< system status length
#define MAX_STATUS_NUM          20                      ///< system status number
#define SYSTEM_STATUS_LEN       8

/*
 * StatusData - Class for PEDK status data
 */
typedef struct {
    char    id[64];
    int32_t param1;
    int32_t param2;
    int32_t param3;
    char    data[256];
    int32_t size;
}StatusData;

static int32_t last_status = 0;

//STPEDK_SYS_STATUS_INFO table about status id
static STPEDK_SYS_STATUS_INFO s_system_status_map[] = {

    /* PRINT status BEGIN*/
    {STATUS_I_PRINT_INIT, "PEDK_SID_I_PRINT_INIT", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_READY, "PEDK_SID_I_PRINT_READY", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_SLEEP, "PEDK_SID_I_PRINT_SLEEP", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_WARMING, "PEDK_SID_I_PRINT_WARMING", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PROCESSING, "PEDK_SID_I_PRINT_PROCESSING", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PRINTING, "PEDK_SID_I_PRINT_PRINTING", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_CANCELING, "PEDK_SID_I_PRINT_CANCELING", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PAUSING, "PEDK_SID_I_PRINT_PAUSING", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_ACR_CALIBRATION, "PEDK_SID_I_PRINT_ACR_CALIBRATION", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_WAITING, "PEDK_UNDEFINE","STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_HOLDPRINT_CONFIRM, "PEDK_SID_I_PRINT_HOLDPRINT_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_HOLDPRINT_FINISH, "PEDK_SID_I_PRINT_HOLDPRINT_FINISH", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PINCODEPRINT_FINISH, "PEDK_SID_I_PRINT_PINCODEPRINT_FINISH", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PAPER_CHANGED, "PEDK_SID_I_PRINT_PAPER_CHANGED", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_HOLDPRINT_LIST_FULL, "PEDK_SID_I_PRINT_HOLDPRINT_LIST_FULL", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_STORAGE_JOB_FULL_FAILED, "PEDK_SID_I_PRINT_STORAGE_JOB_FULL_FAILED", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_STORAGE_JOB_STREAM_FAILED, "PEDK_SID_I_PRINT_STORAGE_JOB_STREAM_FAILED", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_IPS_PARSER_TIMEOUT, "PEDK_SID_I_PRINT_IPS_PARSER_TIMEOUT", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_TONER_EMPTY_COLOR, "PEDK_SID_I_PRINT_TONER_EMPTY_COLOR", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_FE_STATUS_ON, "PEDK_SID_I_PRINT_FE_PEDK_SID_ON", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_UNSUPPORTED_DOCUMENT_FORMAT, "PEDK_SID_I_PRINT_UNSUPPORTED_DOCUMENT_FORMAT", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_FLIP_OVER, "PEDK_SID_I_PRINT_OPC_CALIBRATION", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_OPC_CALIBRATION, "PEDK_SID_I_PRINT_TONER_CONSUM_CALIBRATION", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_TONER_CONSUM_CALIBRATION, "PEDK_SID_I_PRINT_HOLDPRINT_STORAGE_FULL", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_HOLDPRINT_STORAGE_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_ABORTING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_TRAY_NEED_CLOSE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_EMMC_NORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_EMMC_FORMATTING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PINCODE_SAVING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_PINCODE_SAVE_FINISH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_SAVING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_TONER_SUPPLYING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_INTERNAL_PAGE_CREATE_PDF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_SAMPLE_NOTIFY_PANEL_STOP_JOB, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_I_PRINT_JOB_LOCKED, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_PRINT ", "eSTATUS_PRI_INFO"},
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_C, "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_M, "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_Y, "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_K, "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_WASTE_TONER_NEAR_FULL, "PEDK_SID_W_PRINT_WASTE_TONER_NEAR_FULL", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FUSER_NEAR_LIFE_END, "PEDK_SID_W_PRINT_FUSER_NEAR_LIFE_END", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_C, "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_M, "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_Y, "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_K, "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_MISSING_STANDARD_TRAY, "PEDK_SID_W_PRINT_TRAY_MISSING_STANDARD_TRAY", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1, "PEDK_SID_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2, "PEDK_SID_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_EMPTY_STANDARD_TRAY, "PEDK_SID_W_PRINT_TRAY_EMPTY_STANDARD_TRAY", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1, "PEDK_SID_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2, "PEDK_SID_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_STANDARD_BIN_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_COLOR_CALIBRATION, "PEDK_SID_W_PRINT_COLOR_CALIBRATION", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_COLOR_REGISTRATION, "PEDK_SID_W_PRINT_COLOR_REGISTRATION", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_C, "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_M, "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_Y, "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_K, "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_REALAY_ROLLER_NEAR_LIFE_END, "PEDK_SID_W_PRINT_REALAY_ROLLER_NEAR_LIFE_END", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_ITU_NEAR_LIFE_END, "PEDK_SID_W_PRINT_ITU_NEAR_LIFE_END", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY2_ERROR, "PEDK_SID_W_PRINT_TRAY2_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY3_ERROR, "PEDK_SID_W_PRINT_TRAY3_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY2_MOTOR_ERROR, "PEDK_SID_W_PRINT_TRAY2_MOTOR_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY3_MOTOR_ERROR, "PEDK_SID_W_PRINT_TRAY3_MOTOR_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_Y, "PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_M, "PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_C, "PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_Y, "PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_M, "PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_C, "PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_Y, "PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_M, "PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_C, "PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_Y, "PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_M, "PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_C, "PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_Y, "PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_M, "PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_C, "PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_INTERNAL_ENV_TEMP_ERROR, "PEDK_SID_W_PRINT_INTERNAL_ENV_TEMP_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_EXTERNAL_ENV_TEMP_ERROR, "PEDK_SID_W_PRINT_EXTERNAL_ENV_TEMP_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FRONT_CTD_SENSOR_DETECT, "PEDK_SID_W_PRINT_FRONT_CTD_SENSOR_DETECT", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_BACK_CTD_SENSOR_DETECT, "PEDK_SID_W_PRINT_BACK_CTD_SENSOR_DETECT", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FRONT_CTD_SENSOR_ADJUST, "PEDK_SID_W_PRINT_FRONT_CTD_SENSOR_ADJUST", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_BACK_CTD_SENSOR_ADJUST, "PEDK_SID_W_PRINT_BACK_CTD_SENSOR_ADJUST", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_EMPTY_C, "PEDK_SID_W_PRINT_TONER_EMPTY_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_EMPTY_M, "PEDK_SID_W_PRINT_TONER_EMPTY_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_EMPTY_Y, "PEDK_SID_W_PRINT_TONER_EMPTY_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_EMPTY_K, "PEDK_SID_W_PRINT_TONER_EMPTY_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LSU_TEMP_SENSOR_ERROR, "PEDK_SID_W_PRINT_LSU_TEMP_SENSOR_ERROR", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0280_03, "PEDK_SID_W_PRINT_FE0280_03", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0280_04, "PEDK_SID_W_PRINT_FE0280_04", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1, "PEDK_SID_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2, "PEDK_SID_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0280_01, "PEDK_SID_W_PRINT_FE0280_01", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0280_02, "PEDK_SID_W_PRINT_FE0280_02", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_OPTION_TRAY1_ADJUSTING, "PEDK_SID_W_PRINT_OPTION_TRAY1_ADJUSTING", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_OPTION_TRAY2_ADJUSTING, "PEDK_SID_W_PRINT_OPTION_TRAY2_ADJUSTING", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_WASTE_TONER_FULL, "PEDK_SID_W_PRINT_WASTE_TONER_FULL", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_END_C, "PEDK_SID_W_PRINT_DRUM_LIFE_END_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_END_M, "PEDK_SID_W_PRINT_DRUM_LIFE_END_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_END_Y, "PEDK_SID_W_PRINT_DRUM_LIFE_END_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_END_K, "PEDK_SID_W_PRINT_DRUM_LIFE_END_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_C, "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_M, "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_Y, "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_K, "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_C, "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_M, "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_Y, "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_K, "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_C, "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_C", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_M, "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_M", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_Y, "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_Y", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_K, "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_K", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LOW_TEMPERATURE, "PEDK_SID_W_PRINT_LOW_TEMPERATURE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_MAIN_HUMITURE_SENSOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SUB_HUMITURE_SENSOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0280_05, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_MAIN_HUM_SENSOR_EXCEPTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0281_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FE0850_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FUSER_LIFE_END, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_1_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_2_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_3_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_4_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_IN_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_1_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_2_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_3_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_4_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_IN_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_EX_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_EX_PAPER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_IN_MOVING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_IN_PAPER_NEAR_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_EX_PAPER_NEAR_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_IN_PAPER_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LCT_EX_PAPER_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_Y_TONER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_M_TONER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_C_TONER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_K_TONER_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_Y_DR_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_M_DR_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_C_DR_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_K_DR_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_Y_DV_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_M_DV_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_C_DV_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_K_DV_NEAR_LIFE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRANSFER_ROLLER_UNIT_LIFE_END, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_FILTER_LIFE_END, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FUSING_UNIT_LIFE_END, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRANSFER_BELT_UNIT_LIFE_END, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SIDE_STITCHING_STAPLE_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_STAPLE_NEAR_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SADDLE_BIND_F_STAPLE_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SADDLE_BIND_B_STAPLE_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_PUNCH_WASTE_NOT_SET, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_PUNCH_TRASH_BOX_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_NEEDLE_SCARP_OVER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_MAIN_ABNORMAL_LOWER_STATE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_MEDIA_SENSOR_ERROR1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_BAROMETRIC_SENSOR_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_MEDIA_SENSOR_ERROR2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_LD_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SKEW_ADJUSTMENT_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_IDC_SENSOR_FRONT_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_C_DR_DV_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_M_DR_DV_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_Y_DR_DV_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_K_DR_DV_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_IDC_SENSOR_BACK_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_COLOR_PATTERN_TEST_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_COLOR_CORRECTION_AMOUNT_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_PH_OPTICAL_SYSTEM_DIRT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_PAPER_WIDTH_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_PAPER_TEMPERATURE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_PH_TEMPERATURE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_ABNORMAL_2ND_TRANSFER_ATVC, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_INFLIGHT_TEMPERATURE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TACKING_FAN_ABNORMALITY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FUSER_SENSOR_TEMP_DETECT_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY1_PAPER_LOW, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY2_PAPER_LOW, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TRAY3_PAPER_LOW, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_STAPLE_EMPTY_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SELECTED_OUTPUT_SOURCE_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_STAPLE_EMPTY_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FLOD_PAPER_TRAY_LIFT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_SADDLE_BIND_STAPLE_MOVE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_FLAT_BINDING_STAPLER_MOVING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_W_PRINT_TONER_SUPPLY_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_PRINT", "eSTATUS_PRI_WARNING"},
    {STATUS_E_PRINT_BACK_COVER_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_RIGHT_COVER_OPEN, "PEDK_SID_E_PRINT_RIGHT_COVER_OPEN", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FRONT_COVER_OPEN, "PEDK_SID_E_PRINT_FRONT_COVER_OPEN", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY2_COVER_OPEN, "PEDK_SID_E_PRINT_TRAY2_COVER_OPEN", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY3_COVER_OPEN, "PEDK_SID_E_PRINT_TRAY3_COVER_OPEN", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_EMPTY_C, "PEDK_SID_E_PRINT_TONER_EMPTY_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_EMPTY_M, "PEDK_SID_E_PRINT_TONER_EMPTY_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_EMPTY_Y, "PEDK_SID_E_PRINT_TONER_EMPTY_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_EMPTY_K, "PEDK_SID_E_PRINT_TONER_EMPTY_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISSING_C, "PEDK_SID_E_PRINT_TONER_MISSING_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISSING_M, "PEDK_SID_E_PRINT_TONER_MISSING_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISSING_Y, "PEDK_SID_E_PRINT_TONER_MISSING_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISSING_K, "PEDK_SID_E_PRINT_TONER_MISSING_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISMATCH_C, "PEDK_SID_E_PRINT_TONER_MISMATCH_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISMATCH_M, "PEDK_SID_E_PRINT_TONER_MISMATCH_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISMATCH_Y, "PEDK_SID_E_PRINT_TONER_MISMATCH_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_MISMATCH_K, "PEDK_SID_E_PRINT_TONER_MISMATCH_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_LIFE_END_C, "PEDK_SID_E_PRINT_DRUM_LIFE_END_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_LIFE_END_M, "PEDK_SID_E_PRINT_DRUM_LIFE_END_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_LIFE_END_Y, "PEDK_SID_E_PRINT_DRUM_LIFE_END_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_LIFE_END_K, "PEDK_SID_E_PRINT_DRUM_LIFE_END_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISSING_C, "PEDK_SID_E_PRINT_DRUM_MISSING_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISSING_M, "PEDK_SID_E_PRINT_DRUM_MISSING_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISSING_Y, "PEDK_SID_E_PRINT_DRUM_MISSING_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISSING_K, "PEDK_SID_E_PRINT_DRUM_MISSING_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISMATCH_C, "PEDK_SID_E_PRINT_DRUM_MISMATCH_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISMATCH_M, "PEDK_SID_E_PRINT_DRUM_MISMATCH_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISMATCH_Y, "PEDK_SID_E_PRINT_DRUM_MISMATCH_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_MISMATCH_K, "PEDK_SID_E_PRINT_DRUM_MISMATCH_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_WASTE_TONER_FULL, "PEDK_SID_E_PRINT_WASTE_TONER_FULL", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_WASTE_TONER_MISSING, "PEDK_SID_E_PRINT_WASTE_TONER_MISSING", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_STANDARD_BIN_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_MISSING_STANDARD_TRAY, "PEDK_SID_E_PRINT_TRAY_MISSING_STANDARD_TRAY", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1, "PEDK_SID_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2, "PEDK_SID_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_EMPTY_STANDARD_TRAY, "PEDK_SID_E_PRINT_TRAY_EMPTY_STANDARD_TRAY", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY, "PEDK_SID_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1, "PEDK_SID_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2, "PEDK_SID_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH, "PEDK_SID_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH, "PEDK_SID_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH, "PEDK_SID_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH, "PEDK_SID_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH, "PEDK_SID_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH, "PEDK_SID_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH, "PEDK_SID_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH, "PEDK_SID_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ENGINE_NOT_READY, "PEDK_SID_E_PRINT_ENGINE_NOT_READY", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ENGINE_COMMUNICATION_FAILED, "PEDK_SID_E_PRINT_ENGINE_COMMUNICATION_FAILED", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_VIDEO_DRIVE, "PEDK_SID_E_PRINT_VIDEO_DRIVE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_VIDEO_BANDING, "PEDK_SID_E_PRINT_VIDEO_BANDING", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FUSER_MISSING, "PEDK_SID_E_PRINT_FUSER_MISSING", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FUSER_LIFE_END, "PEDK_SID_E_PRINT_FUSER_LIFE_END", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_NOT_FEED_STANDARD_TRAY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_NOT_FEED_MULTI_FUNCTION_TRAY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_NOT_FEED_DUPLEX_UINT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_CALIBRATE_SENSOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_OUTPUT_SENSOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_FUSER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_INPUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STRANDED_CALIBRATE_SENSOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STRANDED_OUTPUT_SENSOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STRANDED_FUSER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STRANDED_DUPLEX_INPUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_FUSER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT, "PEDK_SID_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR, "PEDK_SID_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIBRATE_SENSOR, "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_SENSOR", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2, "PEDK_SID_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1, "PEDK_SID_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY, "PEDK_SID_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY, "PEDK_SID_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TIMEPRINT_LIST_FULL, "PEDK_SID_E_PRINT_TIMEPRINT_LIST_FULL", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_PAPER_SIZE_ERROR, "PEDK_SID_E_PRINT_TRAY_PAPER_SIZE_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_FEED_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_C, "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_C", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_M, "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_M", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_Y, "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_Y", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_K, "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_K", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_REALAY_ROLLER_LIFE_END, "PEDK_SID_E_PRINT_REALAY_ROLLER_LIFE_END", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ITU_NEAR_LIFE_END, "PEDK_SID_E_PRINT_ITU_NEAR_LIFE_END", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ILLEGAL_PAGE, "PEDK_SID_E_PRINT_ILLEGAL_PAGE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_CALIB_OPTIONAL_TRAY_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_CALIB_OPTIONAL_TRAY_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_OUTPUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_OUTPUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1, "PEDK_SID_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2, "PEDK_SID_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT, "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIB_DUPLEX, "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_DUPLEX", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_DUPLEX, "PEDK_SID_E_PRINT_JAM_RESIDUAL_OUTPUT_DUPLEX", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT_DUPLEX, "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT_DUPLEX", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_STORAGE_SPACE_NOT_ENOUGH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FUSER_LIFE_TERMINATION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PARSER_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PARSER_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PARSER_DISCONNECT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_VIDEO, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_IMAGE_PROCESS_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_TRAY_IN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PAPER_TYPE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PAPER_SIZE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PRINT_MODE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_STAPLE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_TB_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_TB_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_TB_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_TB_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_DR_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_DR_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_DR_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_DR_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PRINTRUNONEMPTY_WAR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_EMMC_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_EMMC_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_UDISK_PARSE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_NETWORK_ACESS_ALARM, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ENGINE_SYSTEM_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_1_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_2_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_3_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_4_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_LCT_IN_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_LCT_EX_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_1_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_2_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_3_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_4_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_IN_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_EX_UNSET, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_LCT_IN_SET_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_1_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_2_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_3_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_4_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_IN_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_EX_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_1_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_2_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_3_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_4_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_MANUAL_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_IN_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_EX_SIZE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_RECEIVE_1_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_RECEIVE_2_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRAY_RECEIVE_3_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FRONT_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_SIDE_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_LCT_EX_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FNS_TOP_COVER_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_3RD_TRAY_COVER_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_TONER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_TONER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_TONER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_TONER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_W_TB_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_TB_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_TB_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_TB_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_TB_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_DR_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_DR_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_DR_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_DR_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_DR_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_DR_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_DR_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_DR_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_DV_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_DV_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_DV_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_DV_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_DV_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_DV_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_DV_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_DV_UNINSTALL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_LCT_EX_PAPER_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_MANUAL_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_REFEEDER_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_LCT_EX_TRANSPORT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_OUTPUT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_VER_TRANSPORT_SECTION2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FUSING_UNIT_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRANSFER_BELT_UNIT_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TRANSFER_ROLLER_UNIT_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_TONER_FILTER_LIFE_STOP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_2ND_DISCHARGE_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_3RD_TRAY_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_HOR_TRANSPORT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRANSPORT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_EN_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_EX_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FS_SADDLE_EN_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_ZU_LARGE_SIZE_MODE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_ZU_ALL_MODE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_PUNCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_FNS_STAPLE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SADDLE_EN_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_MAIN_PADDLE_HOME_SNR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_EJECT_GRIP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_ETAMPER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_PUNCH_REAR_DETECTION_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_5, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_7, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_8, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_9, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_13, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_CONTROL_JAM_15, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DUPLEX_DISABLE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_MUTEX_INCORRECT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_OFF, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_Y_DV_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_M_DV_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_C_DV_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_K_DV_MISMATCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FEEDTRAY_1_TRAY_SET_E_ST, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FEEDTRAY_2_TRAY_SET_E_ST, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FEEDTRAY_LCT_IN_TRAY_SET_E_ST, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_OPTION_TRAY_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PROCESS_TRAY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PROCESS_TRAY_1ST_BIN_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PROCESS_TRAY_2ND_BIN_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_COLLATE_FILE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_PINCODE_FILE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_INTERNAL_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_DOC_PARSE_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_SDK_ATTRIBUTE_INCORRECT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_STITCHING_STAPLE_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_SADDLE_BIND_F_STAPLE_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_SADDLE_BIND_B_STAPLE_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_STATUS_INTERNAL_PAGE_PDF_FAIL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FEEDTRAY_3_TRAY_SET_E_ST, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_FEEDTRAY_4_TRAY_SET_E_ST, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_JOB_LOCKED, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_TRAY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_TONER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DRUM, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DV, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_RECEIVE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DOOR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_PUNCH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_BIND, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_FOLD, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_SHIFT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_Z_FOLD, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_FINISHER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_PRO_TRAY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_UNATTAIN_JAM_FRONTEND, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_STRANDED_JAM_FRONTEND, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_E_PRINT_RESIDUAL_JAM_FRONTEND, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_PRINT", "eSTATUS_PRI_ERROR"},
    {STATUS_F_PRINT_LSU_FAN_ERROR, "PEDK_SID_F_PRINT_LSU_FAN_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_EXHAUST_FAN_ERROR, "PEDK_SID_F_PRINT_FUSER_EXHAUST_FAN_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LSU_FAN_ERROR_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_MAIN_FAN_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_TEMP_RAISED_SLOW, "PEDK_SID_F_PRINT_FUSER_TEMP_RAISED_SLOW", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_THERMISTOR_ERROR, "PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_OVER_HEAT, "PEDK_SID_F_PRINT_FUSER_OVER_HEAT", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_HW_OVER_HEAT, "PEDK_SID_F_PRINT_FUSER_HW_OVER_HEAT", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_THERMISTOR_ERROR_3, "PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR_3", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_OVER_HEAT_3, "PEDK_SID_F_PRINT_FUSER_OVER_HEAT_3", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_HW_OVER_HEAT_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_1, "PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_1", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_2, "PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_3, "PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_3", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_THERMISTOR_DAMAGE_1, "PEDK_SID_F_PRINT_THERMISTOR_DAMAGE_1", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_THERMISTOR_DAMAGE_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_THERMISTOR_DAMAGE_3, "PEDK_SID_F_PRINT_THERMISTOR_DAMAGE_3", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_MOTOR_ERROR, "PEDK_SID_F_PRINT_FUSER_MOTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_COLOR_DEVELOP_MOTOR_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_BLACKDEVELOP_MOTOR_ERROR, "PEDK_SID_F_PRINT_BLACKDEVELOP_MOTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_MULTI_PRISM_MOTOR_ON_TIMEOUT, "PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_ON_TIMEOUT", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_MULTI_PRISM_MOTOR_OFF_TIMEOUT, "PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_OFF_TIMEOUT", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_MULTI_PRISM_MOTOR_SIGNAL_ERROR, "PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_SIGNAL_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LD0_COCURRENT_K_ERROR, "PEDK_SID_F_PRINT_LD0_COCURRENT_K_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LD0_COCURRENT_Y_ERROR, "PEDK_SID_F_PRINT_LD0_COCURRENT_Y_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_MCU_COMMUNICATION_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRAY_COMMUNICATION_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EEPROM_IIC_COMMUNICATION_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_HUMITURE_IIC_COMMUNICATION_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CTL_VIDEO_OK_NOTIFY_ERROR, "PEDK_SID_F_PRINT_CTL_VIDEO_OK_NOTIFY_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EC_WARMUP_CANNOT_STOP, "PEDK_SID_F_PRINT_EC_WARMUP_CANNOT_STOP", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EC_PAGE_BUFFER_FULL, "PEDK_SID_F_PRINT_EC_PAGE_BUFFER_FULL", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EC_PAGE_CANNOT_START, "PEDK_SID_F_PRINT_EC_PAGE_CANNOT_START", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EC_PAGE_CANNOT_STOP, "PEDK_SID_F_PRINT_EC_PAGE_CANNOT_STOP", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EC_PRINT_CANNOT_STOP, "PEDK_SID_F_PRINT_EC_PRINT_CANNOT_STOP", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_ENGINE_PRARAM_FILE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FACTORY_FILE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CALIBRATION_PARAM, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TEMPERATURE_UNSTABLE_1, "PEDK_SID_F_PRINT_TEMPERATURE_UNSTABLE_1", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_THERMISTOR_ERROR_2, "PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_OVER_HEAT_2, "PEDK_SID_F_PRINT_FUSER_OVER_HEAT_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_THERMISTOR_DAMAGE_2, "PEDK_SID_F_PRINT_FUSER_THERMISTOR_DAMAGE_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_PRESSURE_DECOMPRESS_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_K, "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_M, "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_M", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_Y, "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_Y", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_C, "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_C", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_K, "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_M, "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_M", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_Y, "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_Y", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_C, "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_C", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TONER_SUPPLY_MOTOR_ERROR, "PEDK_SID_F_PRINT_TONER_SUPPLY_MOTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_BK_PHOTORECEPTOR_MOTOR_ERROR, "PEDK_SID_F_PRINT_BK_PHOTORECEPTOR_MOTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_COLOR_PHOTORECEPTRO_MOTOR_ERROR, "PEDK_SID_F_PRINT_COLOR_PHOTORECEPTRO_MOTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_1TB_COMPRESS_MOTOR_ERROR, "PEDK_SID_F_PRINT_1TB_COMPRESS_MOTOR_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_NEW_DRUM_EXCHANGE_ERROR, "PEDK_SID_F_PRINT_NEW_DRUM_EXCHANGE_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_M, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_Y, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_1, "PEDK_SID_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_1", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_MOTOR_ERROR_1, "PEDK_SID_F_PRINT_TRANS_MOTOR_ERROR_1", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_2, "PEDK_SID_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_SEPARATION_MOTOR_ERROR_2, "PEDK_SID_F_PRINT_TRANS_SEPARATION_MOTOR_ERROR_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2, "PEDK_SID_F_PRINT_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2, "PEDK_SID_F_PRINT_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TRANS_SEPARATION_HIGH_VOL_ERROR, "PEDK_SID_F_PRINT_TRANS_SEPARATION_HIGH_VOL_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_REMOVE_LAMP_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_HIGH_VOL_PLATE_INSERT_ERROR, "PEDK_SID_F_PRINT_HIGH_VOL_PLATE_INSERT_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_HIGH_VOL_DETECTION_ERROR, "PEDK_SID_F_PRINT_HIGH_VOL_DETECTION_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LSU_CABLE_NOT_CONNECTED, "PEDK_SID_F_PRINT_LSU_CABLE_NOT_CONNECTED", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_DEVELOP_CO_HIGH_ERROR_K, "PEDK_SID_F_PRINT_DEVELOP_CO_HIGH_ERROR_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_K, "PEDK_SID_F_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_K, "PEDK_SID_F_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_CO_SENSOR_DETECT_ERROR_K, "PEDK_SID_F_PRINT_CO_SENSOR_DETECT_ERROR_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_K, "PEDK_SID_F_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_K", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_K, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_ENV_TEMP_BOTH_ERROR, "PEDK_SID_F_PRINT_ENV_TEMP_BOTH_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LD0_COCURRENT_K_UNSTABLE, "PEDK_SID_F_PRINT_LD0_COCURRENT_K_UNSTABLE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LD0_COCURRENT_Y_UNSTABLE, "PEDK_SID_F_PRINT_LD0_COCURRENT_Y_UNSTABLE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_EC_CALIBRATION_CANNOT_STOP, "PEDK_SID_F_PRINT_EC_CALIBRATION_CANNOT_STOP", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_COMPLETE_COOLING_FAN1_ERROR, "PEDK_SID_F_PRINT_COMPLETE_COOLING_FAN1_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_LSU_FRONT_COOLING_FAN_ERROR, "PEDK_SID_F_PRINT_LSU_FRONT_COOLING_FAN_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FUSER_EXIT_COOLING_FAN_ERROR, "PEDK_SID_F_PRINT_FUSER_EXIT_COOLING_FAN_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_IMAGING_MAIN_MOTOR_K_ERROR, "PEDK_SID_F_PRINT_IMAGING_MAIN_MOTOR_K_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_IMAGING_MAIN_MOTOR_YMC_ERROR, "PEDK_SID_F_PRINT_IMAGING_MAIN_MOTOR_YMC_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0340_01, "PEDK_SID_F_PRINT_FE0340_01", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0340_02, "PEDK_SID_F_PRINT_FE0340_02", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0340_03, "PEDK_SID_F_PRINT_FE0340_03", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0340_04, "PEDK_SID_F_PRINT_FE0340_04", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0154_00, "PEDK_SID_F_PRINT_FE0154_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0164_00, "PEDK_SID_F_PRINT_FE0164_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0170_01, "PEDK_SID_F_PRINT_FE0170_01", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0170_02, "PEDK_SID_F_PRINT_FE0170_02", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0170_03, "PEDK_SID_F_PRINT_FE0170_03", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0172_01, "PEDK_SID_F_PRINT_FE0172_01", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0172_02, "PEDK_SID_F_PRINT_FE0172_02", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0174_00, "PEDK_SID_F_PRINT_FE0174_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0280_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0280_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0820_00, "PEDK_SID_F_PRINT_FE0820_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0821_00, "PEDK_SID_F_PRINT_FE0821_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0822_00, "PEDK_SID_F_PRINT_FE0822_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0840_00, "PEDK_SID_F_PRINT_FE0840_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0171_01, "PEDK_SID_F_PRINT_FE0171_01", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0171_02, "PEDK_SID_F_PRINT_FE0171_02", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0903_11, "PEDK_SID_F_PRINT_FE0903_11", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_MAIN_MOTOR_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0830_00, "PEDK_SID_F_PRINT_FE0830_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0180_00, "PEDK_SID_F_PRINT_FE0180_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0180_01, "PEDK_SID_F_PRINT_FE0180_01", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0181_00, "PEDK_SID_F_PRINT_FE0181_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0182_00, "PEDK_SID_F_PRINT_FE0182_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0183_00, "PEDK_SID_F_PRINT_FE0183_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0184_00, "PEDK_SID_F_PRINT_FE0184_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE_UNKNOWN, "PEDK_SID_F_PRINT_FE_UNKNOWN", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0142_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0142_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0145_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0152_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0171_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0152_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0153_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0570_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0171_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0351_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0351_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0560_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0900_00, "PEDK_SID_F_PRINT_FE0900_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0850_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0175_00, "PEDK_SID_F_PRINT_FE0175_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0822_01, "PEDK_SID_F_PRINT_FE0822_01", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0361_00, "PEDK_SID_F_PRINT_FE0361_00", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0185_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0185_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0901_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0904_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0904_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0904_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_ENGINE_COMMUNICATION_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0141_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0142_04, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0145_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0461_00, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_FE0461_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_31_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_34_25, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_38_22, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_38_25, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_38_26, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_22, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_25, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_26, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_32, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_36, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_37, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_37_38, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_39_22, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_39_25, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_39_26, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_39_2B, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_3B_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_3B_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_3B_7, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_A_3B_9, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_51_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_22_4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_22_53, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_22_54, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_22_55, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_22_56, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_55, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_60, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_61, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_62, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_63, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_58, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_33_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_33_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_33_7, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_23_50, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_23_57, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_51, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_53_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_23_55, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_32_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_32_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_1_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_1_7, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_1_8, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_1_9, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_21_52, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_21_53, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_21_54, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_21_55, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_21_56, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_31_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_31_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_31_04, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_41_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_45_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_3B_8, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_16, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_8, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_E1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_E2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_A1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_A2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_C5, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_13_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_51, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_52, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_53, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_54, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_55, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_56, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_57, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_58, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_59, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_5A, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_5B, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_5C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_61, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_62, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_63, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_25_64, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_5, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_9, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_13, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_15, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_24, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_25, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_27, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_30, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_31, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_32, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_40, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_41, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_42, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_43, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_44, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_45, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_46, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_52, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_56, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_84, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_95, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_96, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_11_97, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_24_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_24_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_24_13, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_24_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2A_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2A_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2A_13, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_2A_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_B_51_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_55_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_39_2A, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_C1_63, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_A1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_00_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_56_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_56_20, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_91, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_A2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_A3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_A4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_A5, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_40_A6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_10_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_10_4, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_10_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_10_81, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_10_82, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_21, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_22, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_23, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_24, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_26_50, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_26_53, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_26_52, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_26_51, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_26_54, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_2A_04, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_D7_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_D7_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_D7_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_D7_04, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_D7_05, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_D7_06, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_56_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_56_6, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_56_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_14_2, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_14_3, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_C1_55, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_C1_5B, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_06, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_07, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_31, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_33, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_35, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_37, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_32, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_34, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_39, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_41, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_51, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_52, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_53, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_60, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_62, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_63, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_71_64, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_06, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_0A, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_0B, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_41, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_42, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_43, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_72_51, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_04, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_05, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_13, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_14, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_15, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_73_17, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_74_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_74_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_75_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_75_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_01, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_02, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_03, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_04, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_05, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_06, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_07, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_0A, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_0B, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_0C, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_0D, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_0E, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_0F, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_22, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_23, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_24, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_31, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_76_33, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    {STATUS_F_PRINT_C_56_5, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_PRINT", "eSTATUS_PRI_FATAL"},
    /* PRINT status END */
    /* SCAN status BEGIN*/
    {STATUS_I_SCAN_INIT,    "PEDK_SID_I_SCAN_INIT", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_IDLE,    "PEDK_SID_I_SCAN_IDLE", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_SLEEP,       "PEDK_SID_I_SCAN_SLEEP", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_PROCESSING,  "PEDK_SID_I_SCAN_PROCESSING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_RUNNING,     "PEDK_SID_I_SCAN_RUNNING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_CANCELING,   "PEDK_SID_I_SCAN_CANCELING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_TOFILE_SENDING, "PEDK_SID_I_SCAN_TOFILE_SENDING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_NEXT_PAGE_WAITING, "PEDK_SID_I_SCAN_NEXT_PAGE_WAITING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_FINISHED,    "PEDK_SID_I_SCAN_FINISHED", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_TO_FILE_UDISK_SAVING, "PEDK_SID_I_SCAN_TO_FILE_UDISK_SAVING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_TO_FILE_SENT, "PEDK_SID_I_SCAN_TO_FILE_SENT", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_PAPER_PRESENT, "PEDK_SID_I_SCAN_ADF_PAPER_PRESENT", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_PAPER_REMOVED, "PEDK_SID_I_SCAN_ADF_PAPER_REMOVED", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_PUT_PAPER_TO_ADF, "PEDK_SID_I_SCAN_PUT_PAPER_TO_ADF", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF, "PEDK_SID_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_PAPER_SIZE_UNSUPPORT_MIXED_SCAN, "PEDK_SID_I_SCAN_PAPER_SIZE_UNSUPPORT_MIXED_SCAN", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_NO_RESOURCE, "PEDK_SID_I_SCAN_NO_RESOURCE", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_PAPER_SIZE_OVER_AREA, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_BOARD_WAIT_INIT, "PEDK_SID_I_SCAN_ADF_BOARD_WAIT_INIT", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_BOARD_INITING, "PEDK_SID_I_SCAN_ADF_BOARD_INITING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_BOARD_IDLE, "PEDK_SID_I_SCAN_ADF_BOARD_IDLE", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_BOARD_WORKING, "PEDK_SID_I_SCAN_ADF_BOARD_WORKING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_BOARD_ERR, "PEDK_SID_I_SCAN_ADF_BOARD_ERR", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_ADF_BOARD_WARNING, "PEDK_SID_I_SCAN_ADF_BOARD_WARNING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_EML_SUCCESS, "PEDK_SID_I_SCAN_OUT_TO_EML_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_FTP_SUCCESS, "PEDK_SID_I_SCAN_OUT_TO_FTP_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_UDISK_SUCCESS, "PEDK_SID_I_SCAN_OUT_TO_UDISK_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_WSD_SUCCESS, "PEDK_SID_I_SCAN_OUT_TO_WSD_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_AIRSCAN_SUCCESS, "PEDK_SID_I_SCAN_OUT_TO_AIRSCAN_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_EML_CANCEL, "PEDK_SID_I_SCAN_OUT_TO_EML_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_FTP_CANCEL, "PEDK_SID_I_SCAN_OUT_TO_FTP_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_UDISK_CANCEL, "PEDK_SID_I_SCAN_OUT_TO_UDISK_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_WSD_CANCEL, "PEDK_SID_I_SCAN_OUT_TO_WSD_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_OUT_TO_AIRSCAN_CANCEL, "PEDK_SID_I_SCAN_OUT_TO_AIRSCAN_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_I_SCAN_LOCKED, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_SCAN ", "eSTATUS_PRI_INFO"},
    {STATUS_E_SCAN_ADF_PAPER_OUT, "PEDK_SID_E_SCAN_ADF_PAPER_OUT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT, "PEDK_SID_E_SCAN_PAPER_MISPICK_ADF_FRONT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK, "PEDK_SID_E_SCAN_PAPER_MISPICK_ADF_BACK", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_COVER_OPEN, "PEDK_SID_E_SCAN_ADF_COVER_OPEN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_PAPER_MISMATCH, "PEDK_SID_E_SCAN_ADF_PAPER_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FB_COVER_OPEN,                "PEDK_SID_E_SCAN_FB_COVER_OPEN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ, "PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN, "PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT, "PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ, "PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN, "PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT, "PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT, "PEDK_SID_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_REMAIN_ADJ, "PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_ADJ", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN, "PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_SCAN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_PAPER_JAM_REMAIN_EXIT, "PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_EXIT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_MEMORY_LOW, "PEDK_SID_E_SCAN_MEMORY_LOW", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_21, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_21", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_22, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_22", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_23, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_23", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_24, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_24", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_25, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_25", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_26, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_26", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_COMMUNICATION_ERR_27, "PEDK_SID_E_SCAN_COMMUNICATION_ERR_27", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_ABORTED, "PEDK_SID_E_SCAN_TO_FILE_ABORTED", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_PASSWORD_WRONG, "PEDK_SID_E_SCAN_TO_FILE_PASSWORD_WRONG", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_FILE_OVERSIZE, "PEDK_SID_E_SCAN_TO_FILE_FILE_OVERSIZE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_SERVER_OVERSIZE, "PEDK_SID_E_SCAN_TO_FILE_SERVER_OVERSIZE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_SEND_FAILED, "PEDK_SID_E_SCAN_TO_FILE_SEND_FAILED", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_VOLUME_LOW, "PEDK_SID_E_SCAN_TO_FILE_VOLUME_LOW", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE, "PEDK_SID_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_UDISK_ABORTED, "PEDK_SID_E_SCAN_TO_FILE_UDISK_ABORTED", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_TO_FILE_PC_NO_RESPONSE, "PEDK_SID_E_SCAN_TO_FILE_PC_NO_RESPONSE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_ESYS, "PEDK_SID_E_SCAN_EML_ESYS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_EUSER, "PEDK_SID_E_SCAN_EML_EUSER", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_ECONN, "PEDK_SID_E_SCAN_EML_ECONN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_ETRAN, "PEDK_SID_E_SCAN_EML_ETRAN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_EPASS, "PEDK_SID_E_SCAN_EML_EPASS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_EFROM, "PEDK_SID_E_SCAN_EML_EFROM", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_ETO, "PEDK_SID_E_SCAN_EML_ETO", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_EATT_ACCESS, "PEDK_SID_E_SCAN_EML_EATT_ACCESS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_EATT_TOO_BIG, "PEDK_SID_E_SCAN_EML_EATT_TOO_BIG", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_ELIMIT, "PEDK_SID_E_SCAN_EML_ELIMIT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_ESERVER, "PEDK_SID_E_SCAN_EML_ESERVER", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_EML_MEM_LOW, "PEDK_SID_E_SCAN_EML_MEM_LOW", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_SERVER_OVERSIZE, "PEDK_SID_E_SCAN_SMB_SERVER_OVERSIZE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_SENDFAIL, "PEDK_SID_E_SCAN_SMB_SENDFAIL", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_DIR, "PEDK_SID_E_SCAN_SMB_DIR", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_HOSTNAME, "PEDK_SID_E_SCAN_SMB_HOSTNAME", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_USER_PASS, "PEDK_SID_E_SCAN_SMB_USER_PASS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_SERVER_DISCONN, "PEDK_SID_E_SCAN_SMB_SERVER_DISCONN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_NET_DISCONN, "PEDK_SID_E_SCAN_SMB_NET_DISCONN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_ESYS, "PEDK_SID_E_SCAN_FTP_ESYS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_ECONN, "PEDK_SID_E_SCAN_FTP_ECONN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_ETRAN, "PEDK_SID_E_SCAN_FTP_ETRAN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_EUSER, "PEDK_SID_E_SCAN_FTP_EUSER", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_EPASS, "PEDK_SID_E_SCAN_FTP_EPASS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_EFILE_ACCESS, "PEDK_SID_E_SCAN_FTP_EFILE_ACCESS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_ESERVPATH, "PEDK_SID_E_SCAN_FTP_ESERVPATH", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_ESERVER, "PEDK_SID_E_SCAN_FTP_ESERVER", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_WSD_QIOERR, "PEDK_SID_E_SCAN_WSD_QIOERR", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_WSD_COMM, "PEDK_SID_E_SCAN_WSD_COMM", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_WSD_LOWMEM, "PEDK_SID_E_SCAN_WSD_LOWMEM", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_WSD_FILE, "PEDK_SID_E_SCAN_WSD_FILE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_AIRSCAN_ESYS, "PEDK_SID_E_SCAN_AIRSCAN_ESYS", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_AIRSCAN_QIOERR, "PEDK_SID_E_SCAN_AIRSCAN_QIOERR", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_AIRSCAN_ECONN, "PEDK_SID_E_SCAN_AIRSCAN_ECONN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_AIRSCAN_ETRAN, "PEDK_SID_E_SCAN_AIRSCAN_ETRAN", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_USB_NO_RESPONSE, "PEDK_SID_E_SCAN_USB_NO_RESPONSE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_EADRR, "PEDK_SID_E_SCAN_FTP_EADRR", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_IPADDR, "PEDK_SID_E_SCAN_SMB_IPADDR", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SMB_EPORT, "PEDK_SID_E_SCAN_SMB_EPORT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_FTP_EPORT, "PEDK_SID_E_SCAN_FTP_EPORT", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_BOARD_COM_HW, "PEDK_SID_E_SCAN_ADF_BOARD_COM_HW", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_BOARD_COM_CRC_SERIAL, "PEDK_SID_E_SCAN_ADF_BOARD_COM_CRC_SERIAL", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_BOARD_COM_CRC_TOTAL, "PEDK_SID_E_SCAN_ADF_BOARD_COM_CRC_TOTAL", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_BOARD_COM_INVALID_SERIAL, "PEDK_SID_E_SCAN_ADF_BOARD_COM_INVALID_SERIAL", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_BOARD_COM_INVALID_TOTAL, "PEDK_SID_E_SCAN_ADF_BOARD_COM_INVALID_TOTAL", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_E_SCAN_ADF_BOTTOM_COVER_OPEN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_F_SCAN_ADF_RELEASE_BAR_ABNORMAL, "PEDK_SID_F_SCAN_ADF_RELEASE_BAR_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_MOTOR_ABNORMAL, "PEDK_SID_F_SCAN_ADF_MOTOR_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_BOARD_HW_ERROR, "PEDK_SID_F_SCAN_ADF_BOARD_HW_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_BOARD_CONNECT_FAILURE, "PEDK_SID_F_SCAN_ADF_BOARD_CONNECT_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_MOTOR_ABNORMAL, "PEDK_SID_F_SCAN_FB_MOTOR_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_HOME_CHECK_ERROR, "PEDK_SID_F_SCAN_HOME_CHECK_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FINE_HOME_FAILURE, "PEDK_SID_F_SCAN_FINE_HOME_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FINE_BLACK_MARK_FAILURE, "PEDK_SID_F_SCAN_FINE_BLACK_MARK_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CIS_ABNORMAL, "PEDK_SID_F_SCAN_CIS_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CIS_DET_FAILURE, "PEDK_SID_F_SCAN_CIS_DET_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_AFE_ABNORMAL, "PEDK_SID_F_SCAN_AFE_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_AFE_CHIP_CHECK_FAILURE, "PEDK_SID_F_SCAN_AFE_CHIP_CHECK_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_AFE_SCAN_DATA_ABNORMAL, "PEDK_SID_F_SCAN_AFE_SCAN_DATA_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CAL_AFE_OFFSET_FAILURE, "PEDK_SID_F_SCAN_CAL_AFE_OFFSET_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CAL_AFE_GAIN_FAILURE, "PEDK_SID_F_SCAN_CAL_AFE_GAIN_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CAL_AFE_EXPOSURE_FAILURE, "PEDK_SID_F_SCAN_CAL_AFE_EXPOSURE_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CAL_SHADING_FAILURE, "PEDK_SID_F_SCAN_CAL_SHADING_FAILURE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CAL_MEM_UNREADY, "PEDK_SID_F_SCAN_CAL_MEM_UNREADY", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0640_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0640_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0641_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0641_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0641_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0642_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0642_11, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0642_12, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FE0643_10, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_CONNECT_ENGINE_TIMEOUT, "PEDK_SID_F_SCAN_CONNECT_ENGINE_TIMEOUT", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_EMMC_ABNORMAL, "PEDK_SID_F_SCAN_EMMC_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_DMA_ABNORMAL, "PEDK_SID_F_SCAN_DMA_ABNORMAL", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_DMA_INTERRUPT_TIMEOUT, "PEDK_SID_F_SCAN_DMA_INTERRUPT_TIMEOUT", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_COOLING_FAN, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_CANTACT_RETRACT_MECH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_BRUSH_MOVEMENT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_ADF_INIT_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_EXPOSURE_ON_FAIL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_EXPOSURE_LAMP_IPL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DRIVE_SYS_HOME_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_SLIDER_OVERRUNNING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_SCAN_SEQ_TROUBLE_1, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_EMMC_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_CCD_GAIN_ADJ_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_ABN_IMAGE_PROCESS_CLK, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_CCD_ABNORMAL_POWER, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DF_EXPOSURE_ON_FAIL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DF_EXPOSURE_LAMP_IPL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DF_HOME_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DF_BOARD_HOME_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DF_CIS_CLA_ADJ_ABNORMAL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_DF_CIS_GAIN_ADJ_ABNORMA, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_CONNECT_ENGINE_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_FATAL_IMAGE_PROCESS_CLK, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    {STATUS_F_SCAN_FB_INIT_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_SCAN", "eSTATUS_PRI_FATAL"},
    /* SCAN status END */
    /* COPY status BEGIN*/
    {STATUS_I_COPY_PROCESSING, "PEDK_SID_I_COPY_PROCESSING", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_CANCELING, "PEDK_SID_I_COPY_CANCELING", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_MANUAL_DUPLEX_CONFIRM, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_ID_CARD_CONFIRM, "PEDK_SID_I_COPY_ID_CARD_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_MEM_LOW, "PEDK_SID_I_COPY_MEM_LOW", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_SAMPLE_CONFIRM, "PEDK_SID_I_COPY_SAMPLE_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_SAMPLE_FINISH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_NEXT_ORIGINAL_CONFIRM, "PEDK_SID_I_COPY_NEXT_ORIGINAL_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_PUT_PAPER_TO_ADF, "PEDK_SID_I_COPY_PUT_PAPER_TO_ADF", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_NO_RESOURCE, "PEDK_SID_I_COPY_NO_RESOURCE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_CONTINUE_CONFIRM, "PEDK_SID_I_COPY_CONTINUE_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_MIX_ORIGINAL_MISMATCH, "PEDK_SID_I_COPY_MIX_ORIGINAL_MISMATCH", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_COMPLETE, "PEDK_SID_I_COPY_COMPLETE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_COLLATE_STORAGE_FULL, "PEDK_SID_E_COPY_SAMPLE_TONER_EMPTY", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_ABORTING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_MANUAL_DUPLEX_UNSUPPORT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_SCAN_AREA_UNSUPPORT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_STATISTIC_ITEM_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_I_COPY_IDLE, "PEDK_SID_I_COPY_IDLE", "STATUS_ID_TYPE_INFO", "MODULE_COPY ", "eSTATUS_PRI_INFO"},
    {STATUS_W_COPY_PARAMETER_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COPY", "eSTATUS_PRI_WARNING"},
    {STATUS_W_COPY_PERMISSION_NOT_ALLOWED, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COPY", "eSTATUS_PRI_WARNING"},
    {STATUS_E_COPY_SAMPLE_TONER_EMPTY, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_COPY", "eSTATUS_PRI_ERROR"},
    {STATUS_E_COPY_IPM_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_COPY", "eSTATUS_PRI_ERROR"},
    /* COPY status END */
    /* PANEL status BEGIN*/
    {STATUS_I_PANEL_UPGRADE_START, "PEDK_SID_I_PANEL_UPGRADE_START", "STATUS_ID_TYPE_INFO", "MODULE_PANEL", "eSTATUS_PRI_INFO"},
    {STATUS_F_PANEL_COMMUNICATION_ERROR, "PEDK_SID_F_PANEL_COMMUNICATION_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_PANEL", "eSTATUS_PRI_FATAL"},
    /* PANEL status END */
    /* USB status BEGIN*/
    {STATUS_I_USB_UDISK_INSERT, "PEDK_SID_I_USB_UDISK_INSERT", "STATUS_ID_TYPE_INFO", "MODULE_USB", "eSTATUS_PRI_INFO"},
    {STATUS_I_USB_UDISK_EXTRACT, "PEDK_SID_I_USB_UDISK_EXTRACT", "STATUS_ID_TYPE_INFO", "MODULE_USB", "eSTATUS_PRI_INFO"},
    {STATUS_E_USB_UDISK_MISTAKE_FORMAT, "PEDK_SID_E_USB_UDISK_MISTAKE_FORMAT", "STATUS_ID_TYPE_ERROR", "MODULE_USB", "eSTATUS_PRI_ERROR"},
    {STATUS_E_USB_UDISK_FAILURE, "PEDK_SID_E_USB_UDISK_FAILURE", "STATUS_ID_TYPE_ERROR", "MODULE_USB", "eSTATUS_PRI_ERROR"},
    {STATUS_F_USB_UDISK_OVERCURRENT, "PEDK_SID_F_USB_UDISK_OVERCURRENT", "STATUS_ID_TYPE_FATAL", "MODULE_USB", "eSTATUS_PRI_FATAL"},
    {STATUS_F_USB_HOST_OVERCURRENT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_FATAL", "MODULE_USB", "eSTATUS_PRI_FATAL"},
    /* USB status END */
    /* NET status BEGIN*/
    {STATUS_I_NET_WIFI_STA_CONNECTING, "PEDK_SID_I_NET_WIFI_STA_CONNECTING", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_PBC, "PEDK_SID_I_NET_WIFI_WPS_PBC", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_PIN, "PEDK_SID_I_NET_WIFI_WPS_PIN", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_CANCEL, "PEDK_SID_I_NET_WIFI_WPS_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_SUCCESS, "PEDK_SID_I_NET_WIFI_WPS_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_CONNECT_SUCCESS, "PEDK_SID_I_NET_WIFI_CONNECT_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WFD_CONNECT_REQUEST, "PEDK_SID_I_NET_WIFI_WFD_CONNECT_REQUEST", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_FAIL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_OVERLAP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WPS_TIMEOUT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_WIFI_WFD_RADAR_FREQ, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_SMTP_TEST_SUCCESS, "PEDK_SID_I_NET_SMTP_TEST_SUCCESS", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_I_NET_AIRPRINT_IDENTIFY_ACTION, "PEDK_SID_I_NET_AIRPRINT_IDENTIFY_ACTION", "STATUS_ID_TYPE_INFO", "MODULE_NET", "eSTATUS_PRI_INFO"},
    {STATUS_E_NET_WIFI_CONNECT_TIMEOUT, "PEDK_SID_E_NET_WIFI_CONNECT_TIMEOUT", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_WIFI_CONNECT_NO_SSID, "PEDK_SID_E_NET_WIFI_CONNECT_NO_SSID", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_WIFI_CONNECT_ERR_PSK, "PEDK_SID_E_NET_WIFI_CONNECT_ERR_PSK", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_WIFI_CONNECT_FAIL, "PEDK_SID_E_NET_WIFI_CONNECT_FAIL", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_WIFI_CONNECT_NO_RECORD, "PEDK_SID_E_NET_WIFI_CONNECT_NO_RECORD", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_WIFI_DISCONNECT, "PEDK_SID_E_NET_WIFI_DISCONNECT", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_WIFI_INIT_ERROR, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_E_NET_SMTP_TEST_FAIL, "PEDK_SID_E_NET_SMTP_TEST_FAIL", "STATUS_ID_TYPE_ERROR", "MODULE_NET", "eSTATUS_PRI_ERROR"},
    {STATUS_F_NET_WIFI_FATAL_ERROR, "PEDK_SID_F_NET_WIFI_FATAL_ERROR", "STATUS_ID_TYPE_FATAL", "MODULE_NET", "eSTATUS_PRI_FATAL"},
    /* NET status END */
    /* FRAMEWORK status BEGIN*/
    {STATUS_I_FRAMEWORK_SUSPEND, "PEDK_SID_I_FRAMEWORK_SUSPEND", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    {STATUS_I_FRAMEWORK_RESUME, "PEDK_SID_I_FRAMEWORK_RESUME", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    {STATUS_I_FRAMEWORK_REQUEST_NEXT_PAGE, "PEDK_SID_I_FRAMEWORK_REQUEST_NEXT_PAGE", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    {STATUS_I_FRAMEWORK_JOB_INFO_UPDATE, "PEDK_SID_I_FRAMEWORK_JOB_INFO_UPDATE", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    {STATUS_I_FRAMEWORK_PRINTING_IN_JOB_START, "PEDK_SID_I_FRAMEWORK_PRINTING_IN_JOB_START", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    {STATUS_I_FRAMEWORK_PRINTING_IN_JOB_FINISH, "PEDK_SID_I_FRAMEWORK_PRINTING_IN_JOB_FINISH", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    {STATUS_I_FRAMEWORK_RESUME_FAILED, "PEDK_SID_I_FRAMEWORK_RESUME_FAILED", "STATUS_ID_TYPE_INFO", "MODULE_PLATFORM", "eSTATUS_PRI_INFO"},
    /* FRAMEWORK status END */
    /* COMMON status BEGIN*/
    {STATUS_I_COMMON_DATA_STORAGE_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COMMON", "eSTATUS_PRI_INFO"},
    {STATUS_E_COMMON_DATA_RECEIVE_TIMEOUT, "PEDK_SID_E_COMMON_DATA_RECEIVE_TIMEOUT", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_E_COMMON_DATA_PASER_FAILED, "PEDK_SID_E_COMMON_DATA_PASER_FAILED", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    /* COMMON status END */
    /* FWUPDATE status BEGIN*/
    {STATUS_I_FWUPDATE_CONFIRM_UPGRADE, "PEDK_SID_I_FWUPDATE_CONFIRM_UPGRADE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_I_FWUPDATE_RESULT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_I_FWUPDATE_WAIT_FOR_PACKAGE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_I_FWUPDATE_DOWNLOADING_PACKAGE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_I_FWUPDATE_PACKAGE_UPGRADING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_I_FWUPDATE_ENGINE_UPGRADING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_E_FWUPDATE_UNACTIVE, "PEDK_SID_E_FWUPDATE_UNACTIVE", "STATUS_ID_TYPE_ERROR", "MODULE_FWUPDATE", "eSTATUS_PRI_ERROR"},
    /* FWUPDATE status END */
    /* POWERMGR status BEGIN*/
    {STATUS_I_POWERMGR_STATUS_UPDATE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_POWERMGR", "eSTATUS_PRI_INFO"},
    {STATUS_I_POWERMGR_SLEEPING, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_POWERMGR", "eSTATUS_PRI_INFO"},
    {STATUS_I_POWERMGR_WAKINGUP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_POWERMGR", "eSTATUS_PRI_INFO"},
    {STATUS_I_POWERMGR_WAKEUP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_POWERMGR", "eSTATUS_PRI_INFO"},
    {STATUS_I_POWERMGR_SLEEP, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_POWERMGR", "eSTATUS_PRI_INFO"},
    /* POWERMGR status END */
    /* STORAGE status BEGIN*/
    {STATUS_E_STORAGE_INNER_MISTAKE_FORMAT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_E_STORAGE_HARDDISK_MISTAKE_FORMAT, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_E_STORAGE_INNER_FAILURE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_E_STORAGE_HARDDISK_FAILURE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_W_STORAGE_INNER_HEALTH, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_W_STORAGE_INNER_CAPACITY_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_W_STORAGE_UDISK_CAPACITY_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_W_STORAGE_HARDDISK_CAPACITY_FULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_W_STORAGE_INNER_CAPACITY_WILLFULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_W_STORAGE_UDISK_CAPACITY_WILLFULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_W_STORAGE_HARDDISK_CAPACITY_WILLFULL, "PEDK_UNDEFINE", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_I_STORAGE_IDLE, "PEDK_UNDEFINE", "STATUS_ID_TYPE_INFO", "MODULE_COMMON", "eSTATUS_PRI_INFO"},
    /* STORAGE status END */
    /*  status BEGIN*/
    {STATUS_INVALID, "PEDK_SID_INVALID", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_I_DATA_DELETING", "STATUS_ID_TYPE_INFO", "MODULE_COMMON", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_NONE", "STATUS_ID_TYPE_INFO", "MODULE_COMMON", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_E_PARSER_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_PARSER_DISCONNECT", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_IMAGE_PROCESS_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_TRAYS_UNAVAILIABLE", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_TRAY1_FEED_FAILED", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_TRAY1_PAPER_SOURCE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_TRAY1_PAPER_SETTING_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_STD_TRAY_FEED_FAIL", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_STD_TRAY_PAPER_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_STD_TRAY_PAPER_SETTING_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_UDISK_PARSE_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_W_DRUM_UNIT_LIFE_WARNING", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_INVALID, "PEDK_SID_W_DRUM_UNIT_AND_TONER_WARNING", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_INVALID, "PEDK_SID_F_SCAN_INTERNAL_ERR_13", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_IPADDR_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_DOMAIN_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_SERVNONW_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_SERVROOT_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_SERVDIS_B_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_CLIDIS_B_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_SERVFULL_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_WRITEFILE_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_CLIDIS_D_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_SERVDIS_D_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_SMB_PASSWORD_ERROR", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_TOFILE_FLASHFULL", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_I_SCAN_SAVE_TO_UDISK", "STATUS_ID_TYPE_INFO", "MODULE_SCAN", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_NONE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_IDLE", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DOWNLOADING", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DOWNLOAD_FAILED", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DC_UPDATING", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DC_SUCCEEDED", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DC_FAILED", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_EC_UPDATING", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_EC_SUCCEDDED", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_EC_FAILED", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_MANUAL_DUPLEX_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_COMMON", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION1_PAPER_SOURCE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION1_PAPER_SETTING_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION1_NOT_INSTALL", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_F_INPUT_OPTION1_ERROR1", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_F_INPUT_OPTION1_ERROR2", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_F_INPUT_OPTION1_ERROR3", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION2_FEED_FAILED", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION2_PAPER_SOURCE_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION2_PAPER_SETTING_MISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION2_NOT_INSTALL", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_F_INPUT_OPTION2_ERROR1", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_INPUT_OPTION2_ERROR2", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_INPUT_OPTION2_ERROR3", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_W_INPUT_TRAY2_FEW", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_INVALID, "PEDK_SID_W_INPUT_OPTION1_FEW", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_INVALID, "PEDK_SID_W_INPUT_OPTION2_FEW", "STATUS_ID_TYPE_WARN", "MODULE_COMMON", "eSTATUS_PRI_WARNING"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_TRAY1_PAPER_PUTMISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_TRAY2_PAPER_PUTMISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION1_PAPER_PUTMISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_INPUT_OPTION2_PAPER_PUTMISMATCH", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_MIDDLE_JAM", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_MIDDLE_JAM_NOT_ELIMINATED", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_OUTPUT_JAM_NOT_ELIMINATED", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_ERROR_PAPER_JAM_DUPLEX_UNIT_STILL", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_ERROR_COVER_OPEN_BACK", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_02", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_03", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_05", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_06", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_07", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_08", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_09", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_10", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_F_PRN_INTERIOR_FAULT_13", "STATUS_ID_TYPE_FATAL", "MODULE_COMMON", "eSTATUS_PRI_FATAL"},
    {STATUS_INVALID, "PEDK_SID_E_ALL_TRAYS_EMPTY", "STATUS_ID_TYPE_ERROR", "MODULE_COMMON", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_I_OUTPUT_TRAY_FULL", "STATUS_ID_TYPE_INFO", "MODULE_COMMON", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_PRINT_DATA_DELETING", "STATUS_ID_TYPE_INFO", "MODULE_PRINT", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_SCAN_DATA_DELETING", "STATUS_ID_TYPE_INFO", "MODULE_SCAN", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_COPY_DATA_DELETING", "STATUS_ID_TYPE_INFO", "MODULE_COPY", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_CONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DC_USERCONFIRM", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_I_FW_UPDATE_DC_CANCEL", "STATUS_ID_TYPE_INFO", "MODULE_FWUPDATE", "eSTATUS_PRI_INFO"},
    {STATUS_INVALID, "PEDK_SID_E_SCAN_ADF_PAPER_JAM", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},
    {STATUS_INVALID, "PEDK_SID_E_SCAN_ADF_PAPER_MISPICK", "STATUS_ID_TYPE_ERROR", "MODULE_SCAN", "eSTATUS_PRI_ERROR"},

};/* STPEDK_SYS_STATUS_INFO s_system_status_map */

/*sub:MSG
  id :pedk status
  data:pedk status type/module/priority /mfp status id
  do：*/
static int64_t get_mapping_from_pedkid(uint32_t sub, char* id, char *data)
{
    STPEDK_SYS_STATUS_INFO *pedk_st_list = s_system_status_map;
    uint32_t size = sizeof(s_system_status_map) / sizeof(s_system_status_map[0]);
    uint32_t i;

    pi_log_d("the first id:pedk_sid=[%s],sizeof mapping(%d)\n", id, size);
    pi_memset(data, 0x00, MAX_STATUS_LEN);
    for ( i = 0; i < size; i++ )
    {
        if( 0 == pi_strncmp(pedk_st_list[i].pedk_id, id, pi_strlen(id)) )
        {
            switch ( sub )
            {
            case MSG_STATUS_SUB_ID:     //mfp status id
                return pedk_st_list[i].sys_id;
                //break;
            case MSG_STATUS_SUB_TYPE:   //pedk status type
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_type);
                break;
            case MSG_STATUS_SUB_MODULE: //pedk status module
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_module);
                break;
            case MSG_STATUS_SUB_PRI:    //pedk status priority
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_pri);
                break;
            default:
                pi_log_e("EINVALID MSG_STATUS_SUB:%d\n", sub);
                break;
            }
            return 0;
        }
    }
    pi_log_d("i   times(%d)\n", i);
    return 0;
}

//sub:MSG
//id :mfpsysid
//data:pedk status id/type/module/priority
//get pedk status from mfp status id
static void get_mapping_from_sysid(uint32_t sub, uint32_t id, char *data)
{
    STPEDK_SYS_STATUS_INFO *pedk_st_list = s_system_status_map;
    uint32_t size = sizeof(s_system_status_map) / sizeof(s_system_status_map[0]); //number of mfp-pedk changed status
    uint32_t i;

    //pi_log_d("the first id:sys_id=[0x%x]\n", id);
    pi_memset(data, 0x00, MAX_STATUS_LEN);
    for ( i = 0; i < size; i++ )
    {
        if( pedk_st_list[i].sys_id == id )
        {
            switch( sub )
            {
            case MSG_STATUS_SUB_ID:     //pedk status id
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_id);
                //pi_log_d("tran pedk_id=[%s|%s]\n", data,pedk_st_list[i].pedk_id);
                break;
            case MSG_STATUS_SUB_TYPE:   //pedk status type
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_type);
                break;
            case MSG_STATUS_SUB_MODULE: //pedk status module
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_module);
                break;
            case MSG_STATUS_SUB_PRI:    //pedk status priority
                pi_snprintf(data, MAX_STATUS_LEN, "%s", pedk_st_list[i].pedk_pri);
                break;
            default:
                pi_log_e("EINVALID MSG_STATUS_SUB:%d\n", sub);
                break;
            }
            return ;
        }
    }
    return;
}

//s_mfp_status:current mfp status
// return : ret-mber of status
static int32_t get_mfpstatuslist(STPEDK_SYS_STATUS_INFO* s_mfp_status)
{
    uint8_t  *addr = NULL;
    size_t   status_count = 0;
    uint32_t len;
    uint32_t i;
    int32_t  ret;

    //get all system status-mfp system status list
    ret = status_manager_get(STATUS_ID_MODULE_ALL , &addr , &len);
    if ( ( !ret ) && (len > 0) )
    {
        //get mfp status ID and write into s_mfp_status
        for( i = 0; i < len; i += 16)
        {
            if( status_count >= MAX_STATUS_NUM )
            {
                pi_log_e("status id Array is full.\n");
                break;
            }
            uint32_t status_id = *(uint32_t*)(addr + i );                     //get status id (4 bytes) + param1 (4 bytes) + param2 (4 bytes) +  param3 (4 bytes)
            pi_log_d("get new status id:0x%08x\n", status_id);
            if ( status_id == 0 )
            {
                break;
            }
            s_mfp_status[status_count].sys_id = status_id;
            get_mapping_from_sysid(MSG_STATUS_SUB_ID,      s_mfp_status[status_count].sys_id, s_mfp_status[status_count].pedk_id);
            get_mapping_from_sysid(MSG_STATUS_SUB_TYPE,    s_mfp_status[status_count].sys_id, s_mfp_status[status_count].pedk_type);
            get_mapping_from_sysid(MSG_STATUS_SUB_MODULE,  s_mfp_status[status_count].sys_id, s_mfp_status[status_count].pedk_module);
            get_mapping_from_sysid(MSG_STATUS_SUB_PRI,     s_mfp_status[status_count].sys_id, s_mfp_status[status_count].pedk_pri);
            status_count++;
        }
        free(addr);
    }
    else
    {
        pi_log_e("get mfp system status failed \n");
        return -1;
    }

    return status_count;
}

static void get_top_pedkstatusId(char *type, char *status_id)
{
    int32_t i;
    int32_t id_num;
    char status_pri[STATUS_MAX_LEN] = {0};
    STPEDK_SYS_STATUS_INFO s_mfp_status[MAX_STATUS_NUM] = {0};

    id_num = get_mfpstatuslist(s_mfp_status);
    pi_log_d("get mfpstatus number(%d)\n", id_num);
    for ( i = 0; i < id_num; i++ )
    {
        if ( 0 == pi_strncmp(s_mfp_status[i].pedk_type, type, pi_strlen(type)) )
        {
            pi_snprintf(status_id, MAX_STATUS_LEN, "%s", s_mfp_status[i].pedk_id);
            pi_snprintf(status_pri, STATUS_MAX_LEN, "%s", s_mfp_status[i].pedk_pri);
            break;
        }
    }
    return ;
}

//get current system status list
static void get_pedkstatuslist(char* type, int *len, char *idlist)
{
    int32_t id_num;
    STPEDK_SYS_STATUS_INFO s_mfp_status[MAX_STATUS_NUM] = {};
    char tmp[STATUS_MAX_LEN] = {0};

    *len = 0;
    id_num = get_mfpstatuslist(s_mfp_status);
    pi_log_d("get mfpstatus number(%d)\n", id_num);
    for ( int i = 0; i < id_num; i++ )
    {
        if (  0 == pi_strncmp(s_mfp_status[i].pedk_type, type, pi_strlen(type))  )
        {
            pi_memset(tmp, 0x00, STATUS_MAX_LEN);
            get_mapping_from_sysid(MSG_STATUS_SUB_ID, s_mfp_status[i].sys_id, tmp);
            strcat(idlist, tmp);
            strcat(idlist, " ");
            (*len)++;
        }
    }
    pi_log_d("get pedk status idlist [%s]\n", idlist);
    return;

}

static void pedk_status_listener_act(uint32_t id, uint32_t param1,  uint32_t param2,  uint32_t param3, SUB_MSG_E sub)
{
    StatusData json_status;
    char       *ret_str = NULL;

    int32_t sysid = id;
    int32_t sysparam1 = param1;
    int32_t sysparam2 = param2;
    int32_t sysparam3 = param3;
    memset(&json_status, 0, sizeof(StatusData));
    get_mapping_from_sysid(MSG_STATUS_SUB_ID, sysid, json_status.id);
    json_status.param1 = sysparam1;
    json_status.param2 = sysparam2;
    json_status.param3 = sysparam3;

    cJSON *child_json = NULL;
    child_json = cJSON_CreateObject();
    //添加键-值的顺序与jobctl_api.js文件中JobInfo类的成员变量顺序保持一致
    cJSON_AddStringToObject(child_json, "id", json_status.id);
    cJSON_AddNumberToObject(child_json, "param1", json_status.param1);
    cJSON_AddNumberToObject(child_json, "param2", json_status.param2);
    cJSON_AddNumberToObject(child_json, "param3", json_status.param3);
    cJSON_AddStringToObject(child_json, "data", json_status.data);
    cJSON_AddNumberToObject(child_json, "size", strlen(json_status.data));

    ret_str = cJSON_PrintUnformatted(child_json);
    if( NULL == ret_str )
    {
        cJSON_Delete(child_json);

        pi_log_e("get json string failed\n");
        return ;
    }
    pi_log_d("json string[(%d) status]: |%s|\n", sub, ret_str);
    pedk_mgr_send_msg_to_runenv(MSG_MODULE_STATUS, sub, 0, (unsigned char*)ret_str, strlen(ret_str));

    free(ret_str);
    cJSON_Delete(child_json);

}

void status_event_callback(char *data, uint32_t len)
{
    int32_t sysid  = *(int32_t*)data;
    int32_t param1 = *(int32_t*)(data + 4);
    int32_t param2 = *(int32_t*)(data + 8);
    int32_t param3 = *(int32_t*)(data + 12);

    if ( sysid  != last_status )
    {
        if ( last_status != 0 )
        {
            pedk_status_listener_act(last_status, 0, 0, 0, MSG_STATUS_SUB_NOTIFY_REMOVE);
        }
        pedk_status_listener_act(sysid, param1, param2, param3, MSG_STATUS_SUB_NOTIFY_PUSH);
    }
    last_status = sysid;
    pi_log_d("status event callback(0x%x)\n", last_status);
}


static void state_pedk_msg_handler(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t* buf, void* ctx)

{
    int32_t len;
    int32_t iret;
    char *ret = NULL;

    pi_log_d("Get MSG sub = %d\n", sub);
    switch(sub)
    {
    case MSG_STATUS_SUB:            /* Get the list of currently raised status IDs of the specified type */
    case MSG_STATUS_SUB_LIST_LEN:   /* Get the length of the list of currently raised status IDs of the specified type */
        ret = (char *)pi_malloc(MAX_STATUS_LEN * MAX_STATUS_NUM);
        pi_memset(ret, 0x00, MAX_STATUS_LEN * MAX_STATUS_NUM);
        get_pedkstatuslist((char*)buf, &len, ret);
        pi_log_d("Get list or list type: {%s} return string [%s]\n", buf, ret);
        iret = pedk_mgr_send_msg_to_runenv(MSG_MODULE_STATUS, sub, len, (unsigned char *)ret, pi_strlen(ret) + 1);
        if( iret != 0 )
        {
            pi_log_e("pedk_get_status error(%d)\n", iret);
        }
        break;
    case MSG_STATUS_SUB_ID:         /* Get the highest priority status ID of the currently raised status IDs of the specified type */

        ret = (char *)pi_malloc(MAX_STATUS_LEN);
        pi_memset(ret, 0x00, MAX_STATUS_LEN);
        get_top_pedkstatusId((char*)buf, ret);
        pi_log_d("Get Top status moudle: {%s} return string [%s]\n", buf, ret);
        iret = pedk_mgr_send_msg_to_runenv(MSG_MODULE_STATUS, sub, 0, (unsigned char *)ret, pi_strlen(ret) + 1);
        if( iret != 0 )
        {
            pi_log_e("pedk_get_status error(%d)\n", iret);
        }
        break;
    case MSG_STATUS_SUB_TYPE:       /* Get the type of a status */
    case MSG_STATUS_SUB_MODULE:     /* Get the module to which a status belongs */
    case MSG_STATUS_SUB_PRI:        /* Get the representation priority of a status */
        ret = (char *)pi_malloc(MAX_STATUS_LEN);
        pi_memset(ret, 0x00, MAX_STATUS_LEN);
        get_mapping_from_pedkid(sub, (char*)buf, ret);
        pi_log_d("Get return string = %s\n", ret);
        iret = pedk_mgr_send_msg_to_runenv(MSG_MODULE_STATUS, sub, 0, (unsigned char *)ret, pi_strlen(ret) + 1);
        if( iret != 0 )
        {
            pi_log_e("pedk_get_status error(%d)\n", iret);
        }
        break;
    default:
        break;
    }

    if ( ret )
    {
        pi_free(ret);
    }
    return;

}


int32_t status_pedkapi_init(void)
{
    int32_t ret = 0;

    ret = pedk_mgr_register_handler(MSG_MODULE_STATUS, state_pedk_msg_handler, NULL);
    return ret;
}

void status_pedkapi_deinit(void)
{
    pedk_mgr_unregister_handler(MSG_MODULE_STATUS);
}

/**
 * @}
 */
