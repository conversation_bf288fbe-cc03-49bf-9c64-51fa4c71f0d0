/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_address_book.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2024-02-24
 * @brief panel dc get and change address book
 */

#ifndef _PANEL_ADDRESS_BOOK_H
#define _PANEL_ADDRESS_BOOK_H

/**
 * @brief panel set address book
 */
    int set_address_book(ADDRBOOK_OP_S* addr_op, uint32_t data_len);

/**
 * @brief panel update email address book
 * @param[in] mail_book 
 * @param[in] mail_book data size 
 */
 int update_email_addr_book(MAIL_ADDRBOOK_S* mail_book, uint32_t data_len);
 
 /**
 * @brief panel update email group book
 * @param[in] mail_group 
 * @param[in] mail_group data size 
 */
 int update_email_group_book(MAIL_GROUPLIST_S* mail_group, uint32_t data_len);
 
 /**
 * @brief panel update ftp book
 * @param[in] ftp_book 
 * @param[in] ftp_book data size 
 */
 int update_ftp_book(FTP_ADDRBOOK_S* ftp_book, uint32_t data_len);
 
 /**
 * @brief panel update ftp book
 * @param[in] ftp_book 
 * @param[in] ftp_book data size 
 */
 int update_smb_book(SMB_ADDRBOOK_S* smb_book, uint32_t data_len);


 /**
 * @brief update all addr book to panel when wakeup
 */
 void update_all_address_book( void );

#endif /* _PANEL_ADDRESS_BOOK_H */

/**
 *@}
 */



