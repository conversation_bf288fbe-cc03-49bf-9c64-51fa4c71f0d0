KBUILD_CFLAGS += -I net/inc -D HAVE_LINUX -D NOT_HAVE_SA_LEN -D USES_NETLINK -D TARGET_OS_LINUX -D HAVE_IPV6

obj-$(CONFIG_NET) += network.o

obj-$(CONFIG_NET) += common/

obj-$(CONFIG_NET) += http/

obj-$(CONFIG_NET) += ipv4/

obj-$(CONFIG_NET) += ipv6/

obj-$(CONFIG_NET) += netjob/

obj-$(CONFIG_NET) += netqio/

obj-$(CONFIG_NET) += pedkapi/ # pedkapi/中通过CONFIG_SDK_PEDK宏判定是否编译该模块源码

obj-$(CONFIG_NET) += pedksrv/ # pedksrv/中通过CONFIG_SDK_PEDK宏判定是否编译该模块源码

obj-$(CONFIG_NET) += prnsdk/  # prnsdk/ 中通过CONFIG_SDK_EWS 宏判定是否编译该模块源码

obj-$(CONFIG_NET) += tlssrv/

obj-$(CONFIG_NET) += websrv/

obj-$(CONFIG_NET_BONJOUR) += bonjour/

obj-$(CONFIG_NET_ESCLSRV) += esclsrv/

obj-$(CONFIG_NET_FTP) += ftp/

obj-$(CONFIG_NET_IPPSRV) += ippsrv/

obj-$(CONFIG_NET_LPD) += lpd/

obj-$(CONFIG_NET_LLMNR) += llmnr/

obj-$(CONFIG_NET_NETBIOS) += netbios/

obj-$(CONFIG_NET_PORT9120) += port9120/

obj-$(CONFIG_NET_RAWPRINT) += rawprint/

obj-$(CONFIG_NET_RAWSCAN) += rawscan/

obj-$(CONFIG_NET_SLP) += slp/

obj-$(CONFIG_NET_SMTP) += smtp/

obj-$(CONFIG_NET_SNMP) += snmp/

obj-$(CONFIG_NET_WIFI) += wifi/

obj-$(CONFIG_NET_WSD) += wsd/

obj-$(CONFIG_NET_WHITELIST) += whitelist/

obj-$(CONFIG_NET_WEBSCAN) += webscan/

