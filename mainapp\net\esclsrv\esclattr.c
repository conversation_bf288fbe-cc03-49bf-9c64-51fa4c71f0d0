#include "nettypes.h"
#include "esclattr.h"
#include "qxml.h"

#define SET_ATTRIBUTE(pxml, function)                           \
{                                                               \
    pv = QXMLnextValue(pxml);                                   \
    if ( pv != NULL )                                           \
    {                                                           \
        QXMLvalueCopy(pxml, value, pv, sizeof(value));          \
        function(pattr, value);                                 \
    }                                                           \
    else                                                        \
    {                                                           \
        QXMLelementCopy(px, element, pe, sizeof(element));      \
        NET_INFO("invalid element(%s)", element);               \
    }                                                           \
}

struct scan_region
{
    uint32_t            units;
    uint32_t            width;
    uint32_t            height;
    uint32_t            x_offset;
    uint32_t            y_offset;
};

struct escl_attribute
{
    struct scan_region  region;
    SCAN_MODE_E         input_source;
    COLOR_MODE_E        color_mode;
    FILE_TYPE_E         file_type;
    uint32_t            x_resolution;
    uint32_t            y_resolution;
    uint32_t            duplex;
};

static inline void set_intent(ESCL_ATTR_S* pattr, const char* value)
{
    if ( strstr(value, "Preview") != NULL )
    {
        pattr->region.units     = 300;
        pattr->region.width     = 2550;
        pattr->region.height    = 3508;
        pattr->region.x_offset  = 0;
        pattr->region.y_offset  = 0;
        pattr->input_source     = SCAN_MODE_AUTO;
        pattr->color_mode       = COLOR_RGB;
        pattr->file_type        = FILE_JPEG;
        pattr->x_resolution     = 300;
        pattr->y_resolution     = 300;
        pattr->duplex           = 0;
    }
}

static inline void set_region_units(ESCL_ATTR_S* pattr, const char* value)
{
    if ( strstr(value, "ThreeHundredthsOfInches") != NULL )
    {
        pattr->region.units = 300;
    }
    else
    {
        pattr->region.units = 1;
    }
    NET_DEBUG("%s - %u", value, pattr->region.units);
}

static inline void set_region_width(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->region.width = (uint32_t)strtoul(value, NULL, 0);
    NET_DEBUG("%s - %u", value, pattr->region.width);
}

static inline void set_region_height(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->region.height = (uint32_t)strtoul(value, NULL, 0);
    NET_DEBUG("%s - %u", value, pattr->region.height);
}

static inline void set_region_x_offset(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->region.x_offset = (uint32_t)strtoul(value, NULL, 0);
    NET_DEBUG("%s - %u", value, pattr->region.x_offset);
}

static inline void set_region_y_offset(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->region.y_offset = (uint32_t)strtoul(value, NULL, 0);
    NET_DEBUG("%s - %u", value, pattr->region.y_offset);
}

static inline void set_document_format(ESCL_ATTR_S* pattr, const char* value)
{
    if ( strstr(value, "application/pdf") == NULL )
    {
        pattr->file_type = FILE_JPEG;
    }
    else
    {
        pattr->file_type = FILE_PDF;
    }
    NET_DEBUG("%s - %d", value, pattr->file_type);
}

static inline void set_input_source(ESCL_ATTR_S* pattr, const char* value)
{
    if ( strstr(value, "Platen") != NULL )
    {
        pattr->input_source = SCAN_MODE_FB;
    }
    else if ( strstr(value, "Feeder") != NULL )
    {
        pattr->input_source = SCAN_MODE_ADF;
    }
    else
    {
        pattr->input_source = SCAN_MODE_AUTO;
    }
    NET_DEBUG("%s - %d", value, pattr->input_source);
}

static inline void set_color_mode(ESCL_ATTR_S* pattr, const char* value)
{
    if ( strstr(value, "RGB") != NULL )
    {
        pattr->color_mode = COLOR_RGB;
    }
    else if ( strstr(value, "Grayscale") != NULL )
    {
        pattr->color_mode = COLOR_GRAY;
    }
    else
    {
        pattr->color_mode = COLOR_MONO;
    }
    NET_DEBUG("%s - %d", value, pattr->color_mode);
}

static inline void set_x_resolution(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->x_resolution = (uint32_t)strtoul(value, NULL, 0);
    NET_DEBUG("%s - %u", value, pattr->x_resolution);
}

static inline void set_y_resolution(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->y_resolution = (uint32_t)strtoul(value, NULL, 0);
    NET_DEBUG("%s - %u", value, pattr->y_resolution);
}

static inline void set_duplex(ESCL_ATTR_S* pattr, const char* value)
{
    pattr->duplex = ( strstr(value, "true") != NULL ? 1 : 0 );
    NET_DEBUG("%s - %u", value, pattr->duplex);
}

void* esclattr_convert_request_data(ESCL_ATTR_S* pattr, int32_t jobid, int32_t module_id)
{
    SCAN_JOB_REQUEST_DATA_S*    scan_req;

    RETURN_VAL_IF(pattr == NULL || pattr->region.units == 0, NET_WARN, NULL);

    scan_req = (SCAN_JOB_REQUEST_DATA_S *)pi_zalloc(sizeof(SCAN_JOB_REQUEST_DATA_S));
    RETURN_VAL_IF(scan_req == NULL, NET_WARN, NULL);

    scan_req->scan_config_param.scan_dst  = SCAN_JOB_TYPE_AIRSCAN;
    scan_req->scan_config_param.scan_mode = pattr->input_source;
    if ( pattr->duplex )
    {
        scan_req->scan_config_param.scan_mode = SCAN_MODE_DADF;
    }

    scan_req->scan_config_param.combination = 0;
    scan_req->scan_config_param.xres = pattr->x_resolution;
    scan_req->scan_config_param.yres = pattr->y_resolution;

    scan_req->area_width  = pattr->region.width * pattr->x_resolution / pattr->region.units;
    scan_req->area_height = pattr->region.height * pattr->y_resolution / pattr->region.units;
    scan_req->margin_top  = pattr->region.y_offset * pattr->y_resolution / pattr->region.units;
    scan_req->margin_left = pattr->region.x_offset * pattr->x_resolution / pattr->region.units;

    scan_req->scan_config_param.image_quality.brightness = 100;
    scan_req->scan_config_param.image_quality.contrast   = 1000;

    scan_req->scan_config_param.scan_send_router[0] = SCAN_TO_AIRSCAN;
    scan_req->scan_config_param.scan_area  = PAPER_SIZE_USER_DEFINE;
    scan_req->scan_config_param.scan_dst   = SCAN_JOB_TYPE_AIRSCAN;
    scan_req->scan_config_param.scan_color = pattr->color_mode;
    scan_req->scan_config_param.file_type  = pattr->file_type;

    scan_req->airscan_moudle_id = module_id;
    scan_req->job_via           = IO_VIA_IPP;
    scan_req->compress_enable   = 1;
    //scan_req->airscan_job_id    = jobid; //TODO: scan_req数据结构变更

    NET_DEBUG("area_width(%u) area_height(%u)", scan_req->area_width, scan_req->area_height);


    return (void *)(scan_req);
}

FILE_TYPE_E esclattr_get_file_type(ESCL_ATTR_S* pattr)
{
    RETURN_VAL_IF(pattr == NULL, NET_WARN, FILE_JPEG);

    return pattr->file_type;
}

SCAN_MODE_E esclattr_get_input_source(ESCL_ATTR_S* pattr)
{
    RETURN_VAL_IF(pattr == NULL, NET_WARN, SCAN_MODE_AUTO);

    return pattr->input_source;
}

ESCL_ATTR_S* esclattr_parse_scan_settings(const char* xml)
{
    ESCL_ATTR_S*    pattr;
    QXML_S*         pxml;
    QXML_S*         px;
    char*           pv;
    char*           pe;
    char*           pel;
    char            element[128];
    char            value[256];

    RETURN_VAL_IF(xml == NULL, NET_WARN, NULL);

    pattr = (ESCL_ATTR_S *)pi_zalloc(sizeof(ESCL_ATTR_S));
    RETURN_VAL_IF(pattr == NULL, NET_WARN, NULL);

    pattr->input_source = SCAN_MODE_AUTO;
    pattr->color_mode   = COLOR_RGB;
    pattr->file_type    = FILE_JPEG;

    pxml = QXMLparserCreate((char*)xml, 1);

    if ( pxml != NULL )
    {
        if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, QXMLnextElement(pxml)), "ScanSettings") == 0 )
        {
            for ( pe = QXMLchildElement(pxml); pe != NULL; pe = QXMLsiblingElement(pxml) )
            {
                if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Intent") == 0 )
                {
                    SET_ATTRIBUTE(pxml, set_intent);
                }
                else if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "ScanRegions") == 0 )
                {
                    px = QXMLparserClone(pxml);
                    if ( QXMLelementCaseCompare(px, QXMLelementName(px, QXMLchildElement(px)), "ScanRegion") == 0 )
                    {
                        for ( pel = QXMLchildElement(px); pel != NULL; pel = QXMLsiblingElement(px) )
                        {
                            if ( QXMLelementCaseCompare(px, QXMLelementName(px, pel), "ContentRegionUnits") == 0 )
                            {
                                SET_ATTRIBUTE(px, set_region_units);
                            }
                            else if ( QXMLelementCaseCompare(px, QXMLelementName(px, pel), "Width") == 0 )
                            {
                                SET_ATTRIBUTE(px, set_region_width);
                            }
                            else if ( QXMLelementCaseCompare(px, QXMLelementName(px, pel), "Height") == 0 )
                            {
                                SET_ATTRIBUTE(px, set_region_height);
                            }
                            else if ( QXMLelementCaseCompare(px, QXMLelementName(px, pel), "XOffset") == 0 )
                            {
                                SET_ATTRIBUTE(px, set_region_x_offset);
                            }
                            else if ( QXMLelementCaseCompare(px, QXMLelementName(px, pel), "YOffset") == 0 )
                            {
                                SET_ATTRIBUTE(px, set_region_y_offset);
                            }
                            else
                            {
                                QXMLelementCopy(px, element, pel, sizeof(element));
                                NET_DEBUG("Ignore Element: %s", element);
                            }
                        }
                    }
                    QXMLparserDelete(px);
                }
                else if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "DocumentFormat") == 0 )
                {
                    SET_ATTRIBUTE(pxml, set_document_format);
                }
                else if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "DocumentFormatExt") == 0)
                {
                    SET_ATTRIBUTE(pxml, set_document_format);
                }
                else if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "InputSource") == 0 )
                {
                    SET_ATTRIBUTE(pxml, set_input_source);
                }
                else if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "ColorMode") == 0 )
                {
                    SET_ATTRIBUTE(pxml, set_color_mode);
                }
                else if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "XResolution") == 0 )
                {
                    SET_ATTRIBUTE(pxml, set_x_resolution);
                }
                else if (! QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "YResolution"))
                {
                    SET_ATTRIBUTE(pxml, set_y_resolution);
                }
                else if (! QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), "Duplex"))
                {
                    SET_ATTRIBUTE(pxml, set_duplex);
                }
                else
                {
                    QXMLelementCopy(pxml, element, pe, sizeof(element));
                    NET_WARN("Ignore Element(%s)", element);
                }
            }
        }
        else
        {
            NET_WARN("not found ScanSettings element");
        }
        QXMLparserDelete(pxml);
    }

    if ( pattr->region.x_offset < 0 || pattr->region.y_offset < 0
      || pattr->region.width <= 0 || pattr->region.height <= 0
      || pattr->x_resolution <= 0 || pattr->y_resolution <= 0 )
    {
        NET_WARN("eSCL ScanSettings [%u | %u | %u | %u | %u | %u] invalid!",
            pattr->region.x_offset, pattr->region.y_offset,
            pattr->region.width, pattr->region.height,
            pattr->x_resolution, pattr->y_resolution);

            pattr->region.units     = 300;
            pattr->region.width     = 2550;
            pattr->region.height    = 3508;
            pattr->region.x_offset  = 0;
            pattr->region.y_offset  = 0;
            pattr->x_resolution     = 300;
            pattr->y_resolution     = 300;
            pattr->duplex           = 0;
    }

    return pattr;
}
