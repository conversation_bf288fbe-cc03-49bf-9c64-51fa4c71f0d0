/**************************************************************
Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:     pedk setting
file name:       pedk_setting.h
date:            2024-10-25
description:     pedk setting API
**************************************************************/

#include "pedk_mgr.h"
#include "pedk_mfp_setting.h"
#include "../data_distribution/panel_pedk_mfp.h"
#include "print_ctl_pedk.h"


FUNC_SWITCH_E g_copy_func_switch = FUNC_ENABLE;
FUNC_SWITCH_E g_copy_id_func_switch = FUNC_ENABLE;
FUNC_SWITCH_E g_copy_bill_func_switch = FUNC_ENABLE;
FUNC_SWITCH_E g_copy_color_switch = FUNC_ENABLE;
int get_copy_func_switch()
{
    return g_copy_func_switch;
}
int get_copy_id_func_switch()
{
    return g_copy_id_func_switch;
}
int get_copy_bill_func_switch()
{
    return g_copy_bill_func_switch;
}
int get_copy_color_switch()
{
    return g_copy_color_switch;
}

void setting_pedk_deinit()
{
    pedk_mgr_unregister_handler(MSG_MODULE_SETTING);
}

int32_t setting_function_switch( SET_FUNC_SWITCH_S* function_switch, int size)
{
    int ret = 0;
    switch( function_switch->funcion )
    {
        case FUNC_T_COPY:
            printf("copy function switch %d===>%d  \n",function_switch->funcion,function_switch->switch_flag);
            g_copy_func_switch = function_switch->switch_flag;
            panel_pedk_function_switch( function_switch, size );
            break;
        case FUNC_T_IDCOPY:
            printf("copy function switch %d===>%d  \n",function_switch->funcion,function_switch->switch_flag);
            g_copy_id_func_switch = function_switch->switch_flag;
            panel_pedk_function_switch( function_switch, size );
            break;
        case FUNC_T_BILL:
            printf("copy function switch %d===>%d  \n",function_switch->funcion,function_switch->switch_flag);
            g_copy_bill_func_switch = function_switch->switch_flag;
            panel_pedk_function_switch( function_switch, size );
            break;
        case FUNC_T_COLOR_COPY:
            printf("copy function switch %d===>%d  \n",function_switch->funcion,function_switch->switch_flag);
            g_copy_color_switch = function_switch->switch_flag;
            panel_pedk_function_switch( function_switch, size );
            break;
        case FUNC_T_USBPORT_PRINT:
        case FUNC_T_PUSH_SCAN:
        case FUNC_T_MENU_SETTING :
        case FUNC_T_MENU_SYSTEM_SETTING :
        case FUNC_T_MENU_PRINT_SETTING :
        case FUNC_T_MENU_TRAY_SETTING :
        case FUNC_T_MENU_NETWORK_SETTING :
        case FUNC_T_MENU_SAMPLE_PRINT_SETTING :
        case FUNC_T_MENU_SHORTCUT_SETTING :
        case FUNC_T_MENU_JOB_LIST_SETTING :
        case FUNC_T_MENU_ADDRESS_BOOK_SETTING :
        case FUNC_T_MENU_CUSTOM_DESKTOP_SETTING :
        case FUNC_T_SECURE_PRINT :
        case FUNC_T_SCAN_TO_USB:
        case FUNC_T_SCAN_TO_PC:
        case FUNC_T_SCAN_TO_FTP:
        case FUNC_T_SCAN_TO_EMAIL:
        case FUNC_T_SCAN_TO_SMB:
            panel_pedk_function_switch( function_switch, size );
            break;
        default:
            break;
    }
    return ret;
}

void setting_pedk_handler( uint32_t sub, int respond, int buf_size, unsigned char* buf, void* ctx )
{
    printf("setting_pedk_handler strat, sub:%u,respond:%d,buf_size:%d\n",sub,respond,buf_size);
    if( NULL == buf)
    {
        printf("buf is null\n");
    }

    switch ( sub )
    {
        case MSG_SETTING_SUB_SETFUNCTIONSWITCH:
            setting_function_switch( (SET_FUNC_SWITCH_S*)buf, buf_size );
            break;

        default:
            print_ctl_pedk_setting_handler( sub, respond,buf_size,buf, ctx);
        break;
    }
}


int32_t setting_pedk_init( void )
{
    int32_t ret = 0;

    ret = pedk_mgr_register_handler(MSG_MODULE_SETTING, setting_pedk_handler, NULL );

    return ret;
}




