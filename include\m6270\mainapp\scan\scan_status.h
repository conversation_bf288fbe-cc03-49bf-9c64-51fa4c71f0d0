/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  scan_status.h
* @addtogroup scan
*
* @{
* @addtogroup  scan
* <AUTHOR>
* @date   2022-5-24
* @version  v1.0
* @brief   transplant LP-2023 protocol for pc driver
**/

#ifndef __SCAN_STATUS_H__
#define __SCAN_STATUS_H__

#include "public_data_proc.h"
#include "system_manager/statusid.h"

#include "scan_var.h"
#include "../scan/scanners/scanner_interface.h"

#define NAME_LENGTH             100     ///< name max length

/**
 * @brief scan module status
 */
typedef enum
{
    SCAN_MODULE_IDLE,                       //idle status
    SCAN_MODULE_INIT,                       //init status
    SCAN_MODULE_RUNNING,                    //running status
    SCAN_MODULE_PAUSE,                      //pause status
    SCAN_MODULE_CANCEL,                     //cancel status
    SCAN_MODULE_ERROR,                      //error status
    SCAN_MODULE_SLEEP,                      //sleep status
} SCAN_MODULE_STATUS_E;

typedef enum
{
    /*job multi status*/
    HOST_STATUS = 0,
    UDISK_STATUS,
    EMAIL_STATUS,
    FTP_STATUS,
    SMB_STATUS,
    WSD_STATUS,

    /*mechanical warning status*/

    /*mechnical error status*/

    /*please add other status before max_status*/
    MAX_STATUS,
}SCAN_MULTI_STATUS_E;

/**
 * @brief scan current page status
 */
typedef enum
{
    SCAN_PAGE_DONE      = 0,    ///< page done
    SCAN_PAGE_CANCEL,           ///< page cancel
} SCAN_CURRENT_PAGE_STATUS_E;

/**
 * @brief scan submoudule status data
 */
typedef struct
{
    int                             module_id;      ///< module id
    uint32_t                        status_param;   ///< status param
    uint32_t                        status_param2;   ///< status param
    uint32_t                        status_param3;   ///< status param
    STATUS_ID_E                     status;         ///< status
} SCAN_SUBMODULE_STATUS_DATA_S;


#define SCAN_SUBMODULE_MAX 20   ///< submodule max

/**
 * @brief scan status information
 */
typedef struct
{
    uint32_t                        scan_page;  ///< scan page
    PAPER_SIZE_E                    scan_area;  ///< scan area
    SCAN_JOB_TYPE_E                 scan_dest;  ///< scan dest (udisl,ftp,email)
    FILE_TYPE_E                 scan_file_type; ///< scan file type
    char                            scan_file_name[NAME_LENGTH];    ///< scan file name
    SCAN_CURRENT_PAGE_STATUS_E      scan_current_page_status;   ///< scan current page status
} SCAN_STATUS_INFO_DATA_S;

/**
 * @brief scan status msg data
 */
typedef struct
{
    SCANNER_FEATURE_DATA_S            scanner_feature;    ///< scanner feature
    SCAN_SUBMODULE_STATUS_DATA_S      submodule_status[SCAN_SUBMODULE_MAX];///< submodule status list
    int                             submodule_count;            ///< submodule count
    STATUS_ID_E                scan_best_status;                ///< scan best status

    int                             adf_paper_status;               ///< 0,no paper;1,have paper
    uint32_t                        fb_paper_width;                 ///< scanner upload
    uint32_t                        fb_paper_height;                ///< scanner upload
    uint32_t                        adf_paper_width;                ///< scanner upload
    uint32_t                        adf_paper_height;               ///< scanner upload
    uint32_t                        page_no;                        ///< page no
    SCAN_MODE_E                     scan_mode;                      ///< scan mode (fb/adf)

    uint32_t                        cur_page_no;                    ///< current page no
} SCAN_STATUS_MGR_DATA_S;

/**
 * @brief scan status update
 */
typedef struct
{
    STATUS_ID_E                     status;     ///< status
    uint32_t                        status_param;   ///< status param
    uint32_t                        status_param2;   ///< status param
    uint32_t                        status_param3;   ///< status param
} SCAN_STATUS_UPDATE_S;

/**
 * @brief register a scan status module
 * @param[in] scan_submodule submodule id
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_module_register( MODULE_ID_E scan_submodule );

/**
 * @brief update scan status
 * @param[in] scan_submodule submodule id
 * @param[in] my_status status
 * @param[in] status_param status param
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_module_update_status( MODULE_ID_E scan_submodule, STATUS_ID_E my_status, uint32_t status_param );

int scan_status_module_update_status_add_param( MODULE_ID_E scan_submodule, STATUS_ID_E my_status, uint32_t status_param,uint32_t status_param2,uint32_t status_param3 );

//some observers are interested in scan status,so we give out a i/f to them
//int ScanStatusObserverAttach(       EVENT_OBSERVER *o );

//scanner pixel depth
//set or get scan width max

/**
 * @brief set or get scan width max
 * @param[in] width set max width
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_set_scan_width( int width );

/**
 * @brief set or get scan width max
 * @return int  \n
 * @retval get max width
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_scan_width( void );

/**
 * @brief set or get scan pixel depth
 * @param[in] depth set scan pixel depth
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_set_pixel_depth( int depth );

/**
 * @brief set or get scan pixel depth
 * @return int  \n
 * @retval get pixel depth
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_pixel_depth( void );

//set or get fb feature

/**
 * @brief set or get fb feature
 * @param[in] res_min resolution min
 * @param[in] res_max resolution max
 * @param[in] height_max height max
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_set_fb_feature( uint16_t res_min,
                                uint16_t res_max,
                                uint16_t height_max );

/**
 * @brief set or get fb feature
 * @return int  \n
 * @retval resolution min
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_fb_min_resolution( void );

/**
 * @brief set or get fb feature
 * @return int  \n
 * @retval resolution max
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_fb_max_resolution( void );

/**
 * @brief set or get fb feature
 * @return int  \n
 * @retval height max
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_fb_max_height( void );

/**
 * @brief set or get adf feature
 * @param[in] res_min resolution min
 * @param[in] res_max resolution max
 * @param[in] height_max height max
 * @param[in] type feature type
 * @param[in] volume volume
 * @param[in] a4_ppm_max a4 suport max ppm
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_set_adf_feature( uint16_t res_min,
                                 uint16_t res_max,
                                 uint16_t height_max,
                                 uint16_t type,
                                 uint16_t volume,
                                 uint16_t a4_ppm_max );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval adf resolution min
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_adf_min_resolution( void );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval adf resolution max
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_adf_max_resolution( void );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval adf height max
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_adf_max_height( void );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval adf type
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_adf_type( void );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval adf volume
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_adf_volume( void );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval max ppm
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_adf_max_ppm( void );

/**
 * @brief set or get adf feature
 * @param[in] have_paper adf have paper flag
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_set_adf_paper_status( int have_paper );

/**
 * @brief set or get adf feature
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/

#ifdef CONFIG_SCAN
int scan_status_get_adf_paper_status( void );
#else
static inline int scan_status_get_adf_paper_status( void ){return 0;};
#endif

/**
 * @brief scan_status_get_status
 * @return STATUS_ID_E  \n
 * @retval status
 * <AUTHOR>
 * @date 2022-5-24
*/
STATUS_ID_E scan_status_get_status( void );

/**
 * @brief scan_status_get_module_status
 * @param[in] scan_submodule scan submodule
 * @return STATUS_ID_E  \n
 * @retval status
 * <AUTHOR>
 * @date 2022-5-24
*/
STATUS_ID_E scan_status_get_module_status( MODULE_ID_E scan_submodule );

/**
 * @brief scan_status_set_page_no
 * @param[in] page_no page no
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_status_set_page_no( uint32_t page_no );

/**
 * @brief scan_status_get_page_no
 * @return uint32_t  \n
 * @retval page_no
 * <AUTHOR>
 * @date 2022-5-24
*/
uint32_t scan_status_get_page_no( void );

/**
 * @brief scan_status_set_paper_size
 * @param[in] fb_width fb width
 * @param[in] fb_height fb height
 * @param[in] adf_width adf width
 * @param[in] adf_height adf height
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_set_paper_size( uint32_t fb_width, uint32_t fb_height, uint32_t adf_width, uint32_t adf_height );

/**
 * @brief scan_status_get_paper_size
 * @param[out] width fb width
 * @param[out] height fb height
 * @param[out] adf_paper_status adf paper status
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_paper_size( uint32_t* width, uint32_t* height, SCAN_MODE_E mode );

/**
 * @brief scan_status_get_paper_size_pixel
 * @param[out] width fb width
 * @param[out] height fb height
 * @param[out] adf_paper_status adf paper status
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_get_paper_size_pixel( uint32_t* width, uint32_t* height, uint32_t adf_paper_status );

/**
 * @brief scan_status_info_update_notify
 * @param[in] scan_status_info scan status information
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
static int scan_status_update_notify( STATUS_ID_E current_status, uint32_t status_param,uint32_t status_param2,uint32_t status_param3 );

/**
 * @brief scan status mgr prolog i/f
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_status_mgr_prolog( void );


/**
 * @brief scan status mgr prolog i/f
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void ScanStatusSubjectConstructInit( void );

#endif

/**
 *@}
 */


