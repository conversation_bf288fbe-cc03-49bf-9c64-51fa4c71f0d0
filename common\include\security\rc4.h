#ifndef RC4_H
#define RC4_H

#include <pol/pol_types.h>

typedef enum
{
	RC4_ENCRYPTO = 0,
	RC4_DECRYPTO
}RC4_TYPE_E;


void rc4_data_process(uint8_t* inBuf, uint64_t inLen, uint8_t* outBuf,char type);

void rc4_data_process_ser(uint8_t* inBuf, uint64_t inLen, char type);

void rc4_data_process_qio_write(uint8_t* inBuf, uint64_t inLen);
void rc4_s_box_init(uint8_t* s_box);
void rc4_acl_data_process(uint8_t* inBuf, uint64_t inLen);


int32_t unicode_to_utf8(char* pInput, char *pOutput);
int32_t utf8_to_unicode(char * pInput, char* pOutPut);

void rc4_data_encry_decry(void *src_value, uint32_t tp, uint32_t src_size,
                          void* dest_value, uint32_t *dest_size, RC4_TYPE_E rc4_tp );

#endif
