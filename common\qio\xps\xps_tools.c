/*
 * @Description:  xps_tools.c
 * @brief: 实现创建xps文档
 * @Author: <PERSON><PERSON>.zhang
 * @Date: 2023-06-20 16:47:01
 * @LastEditors: Zoey.zhang
 * @Email: <EMAIL>
 ****************************************
 * XPS 目录结构
 * $ROOT
 *  │  FixedDocSeq.fdseq
 *  │  [Content_Types].xml
 *  │
 *  ├─Documents
 *  │  └─1
 *  │      │  FixedDoc.fdoc
 *  │      │
 *  │      └─Pages
 *  │          │  1.fpage
 *  │          │  2.fpage
 *  │          │
 *  │          └─_rels
 *  │                  1.fpage.rels
 *  │                  2.fpage.rels
 *  │
 *  ├─Resources
 *  │  └─Images
 *  │          image_0.jpg
 *  │          image_1.jpg
 *  │
 *  └─_rels
 *          .rels
 */

#include <string.h>
#include <time.h>
#include <assert.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <limits.h>
#include <unistd.h>
#include <dirent.h>
#include <sys/stat.h>
#include <sys/types.h>

#include "xps_tools.h"
#include "../ofd/zip.h"

#define TEMP_BUFFER_SIZE    (1*1024*1024)
#define BUFFER_MAX      (1024)

/**
 * @brief content types used in XPS Document
 *
 */
static const char *xps_content_type =  {
    "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n"
    "<Types xmlns=\"http://schemas.openxmlformats.org/package/2006/content-types\">\n"
        "<Default Extension=\"fdoc\" ContentType=\"application/vnd.ms-package.xps-fixeddocument+xml\"/>\n"
        "<Default Extension=\"fdseq\" ContentType=\"application/vnd.ms-package.xps-fixeddocumentsequence+xml\"/>\n"
        "<Default Extension=\"fpage\" ContentType=\"application/vnd.ms-package.xps-fixedpage+xml\"/>\n"
        "<Default Extension=\"jpeg\" ContentType=\"image/jpeg\"/>\n"
        "<Default Extension=\"jpg\" ContentType=\"image/jpeg\"/>\n"
        "<Default Extension=\"rels\" ContentType=\"application/vnd.openxmlformats-package.relationships+xml\"/>\n"
        "<Default Extension=\"xml\" ContentType=\"application/xml\"/>\n"
    "</Types>"
};

/**
 * @brief used to defined the FixedDocumentSequence part.
 * specifies each fixed document in the fixed payload in sequence
 *
 */
static const char *xps_fixed_doc_seq = {
    "<FixedDocumentSequence xmlns=\"http://schemas.microsoft.com/xps/2005/06\">\n"
    "   <DocumentReference Source=\"/Documents/1/FixedDoc.fdoc\"/>\n"
    "</FixedDocumentSequence>\n"
};

/**
 * @brief used to defined the relationships
 * Relationships represent the type of connection between  source part and  target resource.
 *
 */
static const char *xps_root_rels = {
    "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n"
    "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">\n"
    "    <Relationship Id=\"rId1\" Type=\"http://schemas.microsoft.com/xps/2005/06/fixedrepresentation\" Target=\"FixedDocSeq.fdseq\"/>\n"
    "</Relationships>\n"
};

/**
 * @description: getZipFileInfo
 * @brief: 获取zip 文件的时间信息
 * @param {zip_fileinfo} *zipFileInfo
 * @return {*}
 * @author: zoey.zhang
 */
static zip_fileinfo getZipFileInfo()
{
    zip_fileinfo zipFileInfo;
    time_t cur_time;
    struct tm *ptm;
    time(&cur_time);
    ptm = localtime(&cur_time);
    zipFileInfo.tmz_date.tm_sec  = ptm->tm_sec;
    zipFileInfo.tmz_date.tm_min  = ptm->tm_min;
    zipFileInfo.tmz_date.tm_hour = ptm->tm_hour;
    zipFileInfo.tmz_date.tm_mday = ptm->tm_mday;
    zipFileInfo.tmz_date.tm_mon  = ptm->tm_mon;
    zipFileInfo.tmz_date.tm_year = 1900+ptm->tm_year;
    zipFileInfo.dosDate     = 0;
    zipFileInfo.internal_fa = 0;
    zipFileInfo.external_fa = 0;

    return zipFileInfo;
}

/**
 * @description: createNewFileInZip
 * @brief: 在zip文件中新增文件
 * @param {zipFile} zFile：zip文档
 * @param {char*} filename: 新增文件的文件名
 * @param {char*} fileContent：新增文件的文件内容
 * @return {*}
 * @author: zoey.zhang
 */
static int createNewFileInZip(zipFile zFile, char const *filename, char const * fileContent)
{
    zip_fileinfo zipFileInfo;
    int zip_error = 0;

    zipFileInfo = getZipFileInfo();

    zip_error = zipOpenNewFileInZip(zFile, filename, &zipFileInfo,
                NULL, 0, NULL, 0, NULL /* comment */,
	            Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    zip_error = zipWriteInFileInZip(zFile, fileContent, strlen(fileContent));
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    zip_error = zipCloseFileInZip(zFile);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    return 0;
}

/**
 * @description: xpDocumentInit
 * @brief: 初始化xps文档，在xps对应的zip文件中增加必要的文件
 * @param {zipFile} zFile
 * @return {*}
 * @author: zoey.zhang
 */
int xpsDocumentInit(zipFile zFile)
{
    int ret = 0;

    /* 在zip中新增文件 [Content_Types].xml */
    ret = createNewFileInZip(zFile, "[Content_Types].xml", xps_content_type);
    if(ret != 0 )
    {
        return -1;
    }

    /* 新增文件 FixedDocSeq.fdseq */
    ret = createNewFileInZip(zFile, "FixedDocSeq.fdseq", xps_fixed_doc_seq);
    if(ret != 0 )
    {
        return -1;
    }

    /* 新增文件 _rels/.rels */
    ret = createNewFileInZip(zFile, "_rels/.rels", xps_root_rels);
    if(ret != 0 )
    {
        return -1;
    }

    return ret;
}


/**
 * @description: xpsAddImage
 * @brief: 在xps文档中新增image resource, 即在Resources/Images/目录下增加image资源
 * @param {char*} imgPath: image所在路径
 * @param {int} fd_dst: 表示xps文档
 * @return {*}
 * @author: zoey.zhang
 */
int xpsAddResource(zipFile zFile, char *dstPath, const char *srcPath)
{
    int zip_error = 0;
    zip_fileinfo zipFileInfo = getZipFileInfo();
    zip_error = zipOpenNewFileInZip(zFile, dstPath, &zipFileInfo,
                    NULL, 0, NULL, 0, NULL /* comment */,
	                Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if( zip_error != ZIP_OK )
    {
        printf("zipOpenNewFileInZip failed, err = %d\r\n", zip_error);
			return -1;
    }

    /* writer JPG data to zip */
    unsigned char *buffer = NULL;
    FILE *fp_src = NULL;
    buffer = malloc(TEMP_BUFFER_SIZE);
    if( buffer == NULL )
    {
        printf("malloc buffer failed !\n");
        return -1;
    }
    fp_src = fopen(srcPath, "rb");
    if( fp_src == NULL )
    {
        printf("open jpeg file failed \n");
        free(buffer);
        return -1;
    }
    while(feof(fp_src) == 0)
    {
        memset(buffer, 0, TEMP_BUFFER_SIZE);
        size_t size_read = fread(buffer, 1, TEMP_BUFFER_SIZE, fp_src);
        if( size_read > 0 )
        {
            printf("size_read = %d\r\n", size_read);
            zip_error = zipWriteInFileInZip (zFile, buffer, size_read);
            if (zip_error != ZIP_OK)
            {
                printf("zipWriteInFileInZip failed, err = %d\r\n", zip_error);

                free(buffer);
                buffer = NULL;

                fclose(fp_src);
                fp_src = NULL;

                return -1;
            }

        }
        if(ferror(fp_src))
            break;
    }

    free(buffer);
    buffer = NULL;

    fclose(fp_src);
    fp_src = NULL;

    zip_error = zipCloseFileInZip(zFile);
    if( zip_error != ZIP_OK )
    {
        printf("zipCloseFileInZip failed, err = %d\r\n", zip_error);
		return -1;
    }

    return zip_error;
}

/**
 * @description: xpsFixedPagesCreate
 * @brief: 创建xps文档中的页面描述文件 %d.fpage
 * @param {zipFile} zFile : 表示xps文档的zip文件
 * @param {char*} path：页面描述文件%d.page路径
 * @param {int} Width：页面宽度，该值必须为在XPS的坐标系统中定义的值。XPS的默认坐标空间：左下角为原点，单位为1/96inch
 * @param {int} Height：页面高度
 * @param {int} imgID: image resource 的ID， 从0开始
 * @param {int} imgW：页面中图像宽度，在xps坐标系
 * @param {int} imgH：图像高度
 * @return {*}
 * @author: zoey.zhang
 */
int xpsFixedPagesCreate(zipFile zFile, const char* path, int Width, int Height, int imgID,  int imgW, int imgH)
{
    if( path == NULL )
    {
        return -1;
    }
    int zip_error = 0;
    char *beginFixedPage = {
        "<FixedPage xmlns=\"http://schemas.microsoft.com/xps/2005/06\" Width=\"%d\"\n"
        "Height=\"%d\" xml:lang=\"zh-CN\">\n"
    };
    char *endFixedPage = {"</FixedPage>\n"};
    char *pageContent = {
        "<Canvas>\n"
        "   <Path Data=\"M 0,0 L %d,0 L %d,%d L 0,%d Z\"\n"
        "   Clip=\"M 0,0 L %d,0 L %d,%d L 0,%d L 0,0 Z \">\n"
        "       <Path.Fill>\n"
        "           <ImageBrush ImageSource=\"/Resources/Images/image_%d.jpg\"\n"
        "           Viewbox=\"0,0,%d,%d\" TileMode=\"None\" ViewboxUnits=\"Absolute\"\n"
        "           ViewportUnits=\"Absolute\" Viewport=\"0,0,%d,%d\">\n"
        "           </ImageBrush>\n"
        "       </Path.Fill>\n"
        "   </Path>\n"
        "</Canvas>\n"
    };

    zip_fileinfo zFileInfo = getZipFileInfo();
    zip_error = zipOpenNewFileInZip(zFile, path, &zFileInfo,
                    NULL, 0, NULL, 0, NULL /* comment */,
                    Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    char contentXML[BUFFER_MAX] = {0};
    /*
    snprintf(contentXML, BUFFER_MAX, beginFixedPage, Width, Height);
    zip_error = zipWriteInFileInZip(zFile, contentXML, strlen(contentXML));
    if(zip_error != ZIP_OK )
    {
        return -1;
    }
    snprintf(contentXML, BUFFER_MAX, pageContent, Width, Width, Height, Height,
                                                  Width, Width, Height, Height,
                                                  pageCount,
                                                  imgW, imgH, Width, Height);
    zip_error = zipWriteInFileInZip(zFile, contentXML, strlen(contentXML));
    if(zip_error != ZIP_OK )
    {
        return -1;
    }
    zip_error = zipWriteInFileInZip(zFile, endFixedPage, strlen(endFixedPage));
    if(zip_error != ZIP_OK )
    {
        return -1;
    }
    */
    snprintf(contentXML, BUFFER_MAX, beginFixedPage, Width, Height);
    snprintf(contentXML+strlen(contentXML), BUFFER_MAX, pageContent, Width, Width, Height, Height,
                                                  Width, Width, Height, Height,
                                                  imgID,
                                                  imgW, imgH, Width, Height);
    sprintf(contentXML+strlen(contentXML), "%s", endFixedPage);
    zip_error = zipWriteInFileInZip(zFile, contentXML, strlen(contentXML));
    if(zip_error != ZIP_OK )
    {
        return -1;
    }

    zip_error = zipCloseFileInZip(zFile);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    return zip_error;
}

/**
 * @description: xpsPageRelsCreate
 * @brief: 创建与fixedpage对应的relationship文件
 * @param {zipFile} zFile: zip file
 * @param {char const*} path：page relationship file path
 * @param {int} imgID: image resource 的ID， 从0开始
 * @return {*}
 * @author: zoey.zhang
 */
int xpsPageRelsCreate(zipFile zFile, char const* path, int imgID)
{
    if(path == NULL)
    {
        return -1;
    }

    int zip_error = 0;
    zip_fileinfo zFileInfo = getZipFileInfo();

    char *pageRels = {
        "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n"
        "<Relationships xmlns=\"http://schemas.openxmlformats.org/package/2006/relationships\">\n"
        "<Relationship Id=\"rId1\" Type=\"http://schemas.microsoft.com/xps/2005/06/required-resource\" Target=\"../../../Resources/Images/image_%d.jpg\"/>\n"
        "</Relationships>\n"
    };

    zip_error = zipOpenNewFileInZip(zFile, path, &zFileInfo,
                    NULL, 0, NULL, 0, NULL /* comment */,
                    Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    char pageRelsContent[BUFFER_MAX] = { 0 };
    snprintf(pageRelsContent, BUFFER_MAX, pageRels, imgID);
    zip_error = zipWriteInFileInZip(zFile, pageRelsContent, strlen(pageRelsContent));
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    zip_error = zipCloseFileInZip(zFile);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    return zip_error;
}

/**
 * @description: xpsFixedDocumentCreate
 * @brief: 创建xps文档中的 FixedDoc.fdoc 文件
 * @param {zipFile} zFile：zip file
 * @param {char} *path：FixedDoc.fdoc文件路径
 * @param {int} pageNum：xps文档中的页面数量
 * @return {*}
 * @author: zoey.zhang
 */
int xpsFixedDocumentCreate(zipFile zFile, char *path, int pageNum)
{
    if( path == NULL )
    {
        return -1;
    }

    char *fixedDocHeader = {"<FixedDocument xmlns=\"http://schemas.microsoft.com/xps/2005/06\">\n"};
    char *fixedDocTailer = {"</FixedDocument>\n"};
    char *contentFormat = {
        "<PageContent Source=\"Pages/%d.fpage\">\n"
        "</PageContent>\n"
    };

    int zip_error = 0;
    zip_fileinfo zFileInfo = getZipFileInfo();

    zip_error = zipOpenNewFileInZip(zFile, path, &zFileInfo,
                    NULL, 0, NULL, 0, NULL /* comment */,
                    Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    zip_error = zipWriteInFileInZip(zFile, fixedDocHeader, strlen(fixedDocHeader));
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    char content[60] = { 0 };
    int i = 0;
    for( i = 1; i <= pageNum; i++ )
    {
        snprintf(content, 60, contentFormat, i);
        zip_error = zipWriteInFileInZip(zFile, content, strlen(content));
        if( zip_error != ZIP_OK )
        {
            return -1;
        }
    }

    zip_error = zipWriteInFileInZip(zFile, fixedDocTailer, strlen(fixedDocTailer));
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    zip_error = zipCloseFileInZip(zFile);
    if( zip_error != ZIP_OK )
    {
        return -1;
    }

    return zip_error;
}