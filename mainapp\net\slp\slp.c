/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file slp.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Service-Location-Protocol
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "netmisc.h"
#include "netport.h"

#include "openslp/slp.h"
#include "openslp/slp_xid.h"
#include "openslp/slp_message.h"

#define SLP_BUFFER_SIZE       (1024)

#define SLP_IPV4_MCAST_ADDR   "***************"
#define SLP_IPV6_MCAST_ADDR   "FF02::123"
#define SLP_IPV6_MCAST_ADDR2  "FF02::116"


#define SLP_LANG_TAG            "en"

#define SLP_SERVICE_TYPE        "service:printer:raw-tcp://www.pantum.com"

#define SLP_ATTRIBUTE_LIST      "(printer-name=%s)," \
                                "(ieee-1284-device-id=%s)," \
                                "(x-mac-addr=%s)," \
                                "(x-ip-addr=%s)," \
                                "(snmp_ver=%u)," \
                                "(sn=%s)," \
                                "(pidpt=0x%04x)"

#define SLP_MLOCK_UN()          { if (s_slp_ctx != NULL) pi_mutex_unlock(s_slp_ctx->srv_mtx); }
#define SLP_MLOCK_EX()          { if (s_slp_ctx != NULL) pi_mutex_lock(s_slp_ctx->srv_mtx);   }

typedef struct slp_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     srv_tid;
    PI_MUTEX_T      srv_mtx;
    PI_SOCKET_T     sockfd[IPVER_NUM][IFACE_ID_NUM];
    char            ipstr[IPVER_NUM][IFACE_ID_NUM][IPV6_ADDR_LEN];
    char            mac_addr[IFACE_ID_NUM][16];
    char            hostname[HOSTNAME_LEN];
    char            ieee1284[IEEE1284_LEN];
    char            pdt_sn[32];
    uint16_t        pdt_id;
    uint8_t         changed;
}
SLP_CTX_S;

static SLP_CTX_S*   s_slp_ctx = NULL;

/**
 * @brief       Read all the parameters of the SLP protocol.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void slp_read_global_param(void)
{
    char    ieee1284[IEEE1284_LEN] = { 0 };
    int32_t i, j;

    memset(s_slp_ctx->mac_addr, 0, sizeof(s_slp_ctx->mac_addr));

    netdata_get_hostname(DATA_MGR_OF(s_slp_ctx), s_slp_ctx->hostname, sizeof(s_slp_ctx->hostname));
    NET_TRACE("hostname(%s)", s_slp_ctx->hostname);

    netdata_get_ieee1284(DATA_MGR_OF(s_slp_ctx), ieee1284, sizeof(ieee1284));
    for ( i = 0, j = 0 ; i < IEEE1284_LEN && j < IEEE1284_LEN && ieee1284[i] != '\0' ; ++i )
    {
        /* IEEE-1284 string，if have ',' ，transform "\2C" */
        if ( ieee1284[i] == ',' )
        {
            s_slp_ctx->ieee1284[j++] = '\\';
            s_slp_ctx->ieee1284[j++] = '2';
            s_slp_ctx->ieee1284[j++] = 'C';
        }
        else
        {
            s_slp_ctx->ieee1284[j++] = ieee1284[i];
        }
    }
    NET_TRACE("ieee1284(%s)", s_slp_ctx->ieee1284);

    netdata_get_pdt_sn(DATA_MGR_OF(s_slp_ctx), s_slp_ctx->pdt_sn, sizeof(s_slp_ctx->pdt_sn));
    s_slp_ctx->pdt_id = (uint16_t)netdata_get_pdt_id(DATA_MGR_OF(s_slp_ctx));
    NET_TRACE("pid(0x%04X) sn(%s)", s_slp_ctx->pdt_id, s_slp_ctx->pdt_sn);

    return;
}

/**
 * @brief       The function of determine whether you need to update the slp connection status.
 * @param[in]   changed : It needs to be updated.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void slp_update_link_status(uint8_t changed)
{
    RETURN_IF(s_slp_ctx == NULL, NET_WARN);

    SLP_MLOCK_EX();
    s_slp_ctx->changed |= changed;
    SLP_MLOCK_UN();
}

/**
 * @brief       The callback function of update the callback of the slp protocol connection state.
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void slp_update_link_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    slp_update_link_status((uint8_t)(s->subject_status & 0xFF));
}

/**
 * @brief       The callback function of update the return of the SLP protocol port.
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject  object pointer.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2023-9-26
 */
static void slp_update_port_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_SLP )
    {
        slp_update_link_status(LINK_CHANGE_ALL);
    }
}

static uint32_t slp_reply_calc_packet_size(IFACE_ID_E ifid, const char* ipstr)
{
    char        attribute_list[512] = {0};
    uint8_t     snmp_ver;
    uint8_t     mac[6];
    uint32_t    packet_size = 14;           //不管那种SLP的消息固定有14字节的包头
    const char* ifname = IFACE_NAME(ifid);

    //error code
    packet_size += 2;
    //attribute len
    packet_size += 2;
    //attribute auth
    packet_size += 1;
    slp_read_global_param();

    if ( s_slp_ctx->mac_addr[ifid][0] == '\0' )
    {
        net_ifctl_get_mac(ifname, mac, sizeof(mac));
        snprintf(s_slp_ctx->mac_addr[ifid], sizeof(s_slp_ctx->mac_addr[ifid]), "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        NET_TRACE("'%s' mac: '%s'", ifname, s_slp_ctx->mac_addr[ifid]);
    }
    snmp_ver = (uint8_t)netdata_get_snmp_version_flags(DATA_MGR_OF(s_slp_ctx));
    snprintf(attribute_list, sizeof(attribute_list), SLP_ATTRIBUTE_LIST,
             s_slp_ctx->hostname, s_slp_ctx->ieee1284, s_slp_ctx->mac_addr[ifid], ipstr, snmp_ver, s_slp_ctx->pdt_sn, s_slp_ctx->pdt_id);
    packet_size = packet_size + strlen(SLP_LANG_TAG) + strlen(attribute_list);

    return packet_size;

}

static void slp_reply_v2_packet_header(SLPBuffer slp_buf, uint16_t xid, IFACE_ID_E ifid, const char* ipstr)
{
    uint16_t v2_flage = 0;
    uint32_t packet_head_size = 0;

    RETURN_IF(NULL == slp_buf, NET_NONE);
    packet_head_size = slp_reply_calc_packet_size(ifid, ipstr);

    //version
    *slp_buf->curpos = 2;
    slp_buf->curpos++;
    //function id
    *slp_buf->curpos = SLP_FUNCT_ATTRRPLY;
    slp_buf->curpos++;
    //packet length should equal to udp playload size
    PutUINT24(&slp_buf->curpos, packet_head_size);
    //flage
    PutUINT16(&slp_buf->curpos, v2_flage);
    //extern
    PutUINT24(&slp_buf->curpos, 0);
    //xid
    PutUINT16(&slp_buf->curpos, xid);
    //lang tag len
    PutUINT16(&slp_buf->curpos, strlen(SLP_LANG_TAG));
    //lang tag
    memcpy(slp_buf->curpos, SLP_LANG_TAG, strlen(SLP_LANG_TAG));
    slp_buf->curpos += strlen(SLP_LANG_TAG);

    return;
}

static void slp_reply_attribute_packet(SLPBuffer slp_buf, IFACE_ID_E ifid, const char* ipstr)
{
    uint8_t     snmp_ver;
    uint8_t     mac[6];
    char        attribute_list[512] = {0};
    const char* ifname = IFACE_NAME(ifid);

    RETURN_IF(NULL == slp_buf, NET_NONE);

    slp_read_global_param();
    if ( s_slp_ctx->mac_addr[ifid][0] == '\0' )
    {
        net_ifctl_get_mac(ifname, mac, sizeof(mac));
        snprintf(s_slp_ctx->mac_addr[ifid], sizeof(s_slp_ctx->mac_addr[ifid]), "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        NET_TRACE("'%s' mac: '%s'", ifname, s_slp_ctx->mac_addr[ifid]);
    }

    snmp_ver = (uint8_t)netdata_get_snmp_version_flags(DATA_MGR_OF(s_slp_ctx));
    snprintf(attribute_list, sizeof(attribute_list), SLP_ATTRIBUTE_LIST,
             s_slp_ctx->hostname, s_slp_ctx->ieee1284, s_slp_ctx->mac_addr[ifid], ipstr, snmp_ver, s_slp_ctx->pdt_sn, s_slp_ctx->pdt_id);
    //error code
    PutUINT16(&slp_buf->curpos, 0);
    //attribute len
    PutUINT16(&slp_buf->curpos, strlen(attribute_list));
    //attribute
    memcpy(slp_buf->curpos, attribute_list, strlen(attribute_list));
    slp_buf->curpos += strlen(attribute_list);
    //attribute auth
    *slp_buf->curpos = 0;
    slp_buf->curpos++;

    return;
}

static void slp_reply_packet(SLPBuffer slp_buf, uint16_t xid, IFACE_ID_E ifid, const char* ipstr)
{
    RETURN_IF(NULL == slp_buf, NET_NONE);
    slp_reply_v2_packet_header(slp_buf, xid, ifid, ipstr);
    slp_reply_attribute_packet(slp_buf, ifid, ipstr);

    return;
}

/**
 * @brief       The function of processing slp request.
 * @param[in]   buf     : The slp request data received buff.
 * @param[in]   nbuf    : slp requests the length of the data received.
 * @param[in]   from    : The slp request information source structure.
 * @param[in]   ifid    : Network link type.
 * @param[in]   ipver   : IPV4 or IPV6.
 * @return      No return
 * <AUTHOR> Xin
 * @date        2024-10-24
 */
static void slp_process_request(char* buf, size_t nbuf, struct sockaddr_storage* from, IFACE_ID_E ifid, IP_VERSION_E ipver)
{
    void*    src = NULL;
    char     from_addr[IPV6_ADDR_LEN];
    int32_t  send_ret;
    SLPError error_code;
    SLPHeader slp_header;
    SLPBuffer slp_recv_buf = NULL;
    SLPBuffer slp_send_buf = NULL;

    SLPMessage* slp_message = NULL;

    if ( nbuf < sizeof(SLPHeader) || ( NULL == buf ) )
    {
        NET_WARN("SLP packet size error");
        return;
    }

    slp_recv_buf = SLPBufferAlloc(nbuf);
    if ( NULL == slp_recv_buf )
    {
        NET_WARN("SLP alloc cache buf error");
        return;
    }

    memset(&slp_header, 0, sizeof(SLPHeader));

    slp_recv_buf->start = (uint8_t*)buf;
    slp_recv_buf->curpos = slp_recv_buf->start;
    slp_recv_buf->end = slp_recv_buf->start + nbuf;

    error_code = SLPMessageParseHeader(slp_recv_buf, &slp_header);
    if ( SLP_OK != error_code )
    {
        SLPBufferFree(slp_recv_buf);
        NET_WARN("SLP parse message header error %d", error_code);
        return;
    }

    slp_send_buf = SLPBufferAlloc(SLP_BUFFER_SIZE);
    if ( NULL == slp_send_buf )
    {
        NET_WARN("SLP alloc send buf error");
        SLPBufferFree(slp_recv_buf);
        return;
    }

    slp_message = SLPMessageAlloc();
    if ( NULL == slp_message )
    {
        NET_WARN("SLP alloc message buf error");
        SLPBufferFree(slp_recv_buf);
        SLPBufferFree(slp_send_buf);
        return;
    }

    error_code = SLPMessageParseBuffer(from, NULL, slp_recv_buf, slp_message);
    if ( SLP_OK != error_code )
    {
        NET_WARN("SLP parse message buf error");
        SLPBufferFree(slp_recv_buf);
        SLPBufferFree(slp_send_buf);
        SLPMessageFree(slp_message);
        return;
    }

    NET_TRACE("SLP message function id is %d", slp_header.functionid);
    if ( SLP_FUNCT_ATTRRQST == slp_header.functionid )
    {
        if ( 0 != strcmp("service:printer:raw-tcp", slp_message->body.attrrqst.url) )
        {
            NET_TRACE("SLP url error %s", slp_message->body.attrrqst.url);
            SLPBufferFree(slp_recv_buf);
            SLPBufferFree(slp_send_buf);
            SLPMessageFree(slp_message);
            return;
        }
        slp_reply_packet(slp_send_buf, slp_header.xid, ifid, s_slp_ctx->ipstr[ipver][ifid]);
    }
    else
    {
        SLPBufferFree(slp_recv_buf);
        SLPBufferFree(slp_send_buf);
        SLPMessageFree(slp_message);
        return;
    }

    src = (((struct sockaddr *)from)->sa_family == AF_INET ? ((void *)&((struct sockaddr_in *)from)->sin_addr) : ((void *)&((struct sockaddr_in6 *)from)->sin6_addr));
    inet_ntop(((struct sockaddr *)from)->sa_family, src, from_addr, sizeof(from_addr));
    NET_TRACE("SLP send '%s' to '%s' lens %d by %s.%s", s_slp_ctx->ipstr[ipver][ifid], from_addr, slp_send_buf->curpos - slp_send_buf->start, IFACE_NAME(ifid), IPVER_NAME(ipver));

    send_ret = sendto(s_slp_ctx->sockfd[ipver][ifid], slp_send_buf->start, slp_send_buf->curpos - slp_send_buf->start, 0, (struct sockaddr *)from, (socklen_t)sizeof(*from));
    if ( send_ret < 0 )
    {
        NET_WARN("SLP send '%s' to '%s' by %s.%s failed: %d<%s>", s_slp_ctx->ipstr[ipver][ifid], from_addr, IFACE_NAME(ifid), IPVER_NAME(ipver), errno, strerror(errno));
    }

    SLPBufferFree(slp_recv_buf);
    SLPBufferFree(slp_send_buf);
    SLPMessageFree(slp_message);

    return;
}


/**
 * @brief       The function of reload slp protocol socket.
 * @param[in]   readfds : Read socket.
 * @return      Reassigned read socket
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static int32_t slp_reload_socket(fd_set* readfds)
{
    const char*     mcast_v4_addr = SLP_IPV4_MCAST_ADDR;
    const char*     mcast_v6_addr[2] = { SLP_IPV6_MCAST_ADDR, SLP_IPV6_MCAST_ADDR2 };
    const char*     ifname = NULL;
    int32_t         maxfd = 0;
    uint8_t         link_base = LINK_CHANGE_IPV4_BASE;
    uint8_t         changed;
    int32_t         ret = 0;
    int32_t         mcast_v6;

    SLP_MLOCK_EX();
    changed = s_slp_ctx->changed;
    s_slp_ctx->changed = 0;
    SLP_MLOCK_UN();

    FD_ZERO(readfds);
    for ( IP_VERSION_E ipver = IPV4; ipver < IPVER_NUM; ++ipver )
    {
        for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
        {
            ifname = IFACE_NAME(ifid);
            if ( STRING_IS_EMPTY(ifname) )
            {
                continue;
            }

#ifdef CONFIG_NET_WIFI
            if ( ipver == IPV6 && ifid == IFACE_ID_WFD )
            {
                continue;
            }
#endif
            /* 当前链路状态有变更，重载socket */
            if ( (link_base << ifid) & changed )
            {
                NET_DEBUG("'%s.%s' has changed, socket reload", ifname, IPVER_NAME(ipver));
                if ( s_slp_ctx->sockfd[ipver][ifid] != INVALID_SOCKET )
                {
                    if ( ipver == IPV4 )
                    {
                        netsock_leave_multicast_group(s_slp_ctx->sockfd[ipver][ifid], ifid, ipver, mcast_v4_addr);
                    }
                    else
                    {
                        for ( mcast_v6 = 0; mcast_v6 < sizeof(mcast_v6_addr)/sizeof(mcast_v6_addr[0]); mcast_v6++ )
                        {
                            netsock_leave_multicast_group(s_slp_ctx->sockfd[ipver][ifid], ifid, ipver, mcast_v6_addr[mcast_v6]);
                        }
                    }
                    pi_closesock(s_slp_ctx->sockfd[ipver][ifid]);
                    s_slp_ctx->sockfd[ipver][ifid] = INVALID_SOCKET;
                }
                //关闭slp开关时不进行socket的重载
                if ( 0 == netdata_get_slp_switch(DATA_MGR_OF(s_slp_ctx)) )
                {
                    continue;
                }

                if ( ipver == IPV6 )
                {
                    netdata_get_ipv6_link(DATA_MGR_OF(s_slp_ctx), ifid, s_slp_ctx->ipstr[ipver][ifid], sizeof(s_slp_ctx->ipstr[ipver][ifid]));
                }
                else
                {
                    netdata_get_ipv4_addr(DATA_MGR_OF(s_slp_ctx), ifid, s_slp_ctx->ipstr[ipver][ifid], sizeof(s_slp_ctx->ipstr[ipver][ifid]));
                }

                if ( netdata_get_iface_running(DATA_MGR_OF(s_slp_ctx), ifid) == 0 || STRING_IS_EMPTY(s_slp_ctx->ipstr[ipver][ifid]) )
                {
                    NET_DEBUG("'%s.%s' is skipped", ifname, IPVER_NAME(ipver));
                    continue;
                }

                NET_DEBUG("load '%s' address '%s' to SLP multicast group", ifname, s_slp_ctx->ipstr[ipver][ifid]);
                s_slp_ctx->sockfd[ipver][ifid] = netsock_create_multicast(SLP_PORT, ifid, ipver);
                if ( s_slp_ctx->sockfd[ipver][ifid] == INVALID_SOCKET )
                {
                    continue;
                }

                if ( ipver == IPV4 )
                {
                    ret = netsock_join_multicast_group(s_slp_ctx->sockfd[ipver][ifid], ifid, ipver, s_slp_ctx->ipstr[ipver][ifid], mcast_v4_addr);
                }
                else
                {
                    for ( mcast_v6 = 0; mcast_v6 < sizeof(mcast_v6_addr)/sizeof(mcast_v6_addr[0]); mcast_v6++ )
                    {
                        ret = netsock_join_multicast_group(s_slp_ctx->sockfd[ipver][ifid], ifid, ipver, s_slp_ctx->ipstr[ipver][ifid], mcast_v6_addr[mcast_v6]);
                        BREAK_IF(ret != 0, NET_DEBUG);
                    }
                }

                if ( ret != 0 )
                {
                    pi_closesock(s_slp_ctx->sockfd[ipver][ifid]);
                    s_slp_ctx->sockfd[ipver][ifid] = INVALID_SOCKET;
                    continue;
                }
            }

            if ( s_slp_ctx->sockfd[ipver][ifid] != INVALID_SOCKET )
            {
                FD_SET(s_slp_ctx->sockfd[ipver][ifid], readfds);
                if ( maxfd < s_slp_ctx->sockfd[ipver][ifid] )
                {
                    maxfd = s_slp_ctx->sockfd[ipver][ifid];
                }
            }
        }
        link_base = (link_base << IFACE_ID_NUM);
    }

    return maxfd;
}

/**
 * @brief       The llmnr handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-22
 */
static void* slp_thread_handler(void* arg)
{
    IP_VERSION_E            ipver;
    IFACE_ID_E              ifid;
    fd_set                  rfds;
    socklen_t               slen;
    struct sockaddr_storage from;
    struct timeval          tv;
    char                    buf[SLP_BUFFER_SIZE];
    int32_t                 maxfd;
    ssize_t                 rlen;
    int32_t                 ret;

    NET_DEBUG("Starting SLP thread handler");

    while ( 1 )
    {
        if ( (maxfd = slp_reload_socket(&rfds)) <= 0 )
        {
            pi_msleep(2000);
            continue;
        }

        tv.tv_sec  = 2;
        tv.tv_usec = 0;
        ret = select(maxfd + 1, &rfds, NULL, NULL, &tv);
        if ( ret < 0 )
        {
            NET_WARN("select failed: %d<%s>", errno, strerror(errno));
            slp_update_link_status(LINK_CHANGE_ALL);
            continue;
        }
        else if ( ret == 0 )
        {
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ipver++ )
        {
            for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ifid++ )
            {
                if ( s_slp_ctx->sockfd[ipver][ifid] != INVALID_SOCKET && FD_ISSET(s_slp_ctx->sockfd[ipver][ifid], &rfds) )
                {
                    memset(buf, 0, sizeof(buf));
                    slen = (socklen_t)sizeof(from);
                    rlen = recvfrom(s_slp_ctx->sockfd[ipver][ifid], buf, sizeof(buf), 0, (struct sockaddr *)&from, &slen);
                    if ( rlen > 0 )
                    {
                        NET_TRACE("recvfrom %s.%s %d bytes", IFACE_NAME(ifid), IPVER_NAME(ipver), rlen);
                        slp_process_request(buf, rlen, &from, ifid, ipver);
                    }
                    else
                    {
                        NET_WARN("recvfrom %s.%s failed(%d): %d<%s>", IFACE_NAME(ifid), IPVER_NAME(ipver), rlen, errno, strerror(errno));
                        slp_update_link_status(LINK_CHANGE_ALL);
                    }
                }
            }
        }
    }

    return NULL;
}

int32_t slp_prolog(NET_CTX_S *net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_slp_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_slp_ctx = (SLP_CTX_S *)pi_zalloc(sizeof(SLP_CTX_S));
    RETURN_VAL_IF(s_slp_ctx == NULL, NET_WARN, -1);

    do
    {
        NET_DEBUG("LINK_CHANGE_ALL(0x%X)", LINK_CHANGE_ALL);
        memset(s_slp_ctx->sockfd, INVALID_SOCKET, sizeof(s_slp_ctx->sockfd)); /* initial value is INVALID_SOCKET(-1) */
        s_slp_ctx->changed = LINK_CHANGE_IPV4_ALL;
        s_slp_ctx->net_ctx = net_ctx;

        BREAK_IF((s_slp_ctx->srv_mtx = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        s_slp_ctx->srv_tid = pi_thread_create(slp_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "slp_thread_handler");
        BREAK_IF(s_slp_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while( 0 );

    NET_INFO("SLP initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netlink_observer(net_ctx, slp_update_link_callback, NULL);
        netctx_add_netport_observer(net_ctx, slp_update_port_callback, NULL);
    }
    else
    {
        slp_epilog();
    }
    return ret;
}

void slp_epilog(void)
{
    if ( s_slp_ctx != NULL )
    {
        if ( s_slp_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_slp_ctx->srv_tid);
        }
        if ( s_slp_ctx->srv_mtx != INVALIDMTX )
        {
            pi_mutex_destroy(s_slp_ctx->srv_mtx);
        }
        pi_free(s_slp_ctx);
        s_slp_ctx = NULL;
    }
}
/**
 *@}
 */
