/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file network.h
 * @addtogroup net
 * @{
 * @addtogroup network
 * <AUTHOR>
 * @date 2023-5-8
 * @brief Network module prolog/epilog function.
 */
#ifndef __NETWORK_H__
#define __NETWORK_H__

typedef enum
{
    AIRPRINT_IDENTIFY_ACTION_NONE    = 0,
    AIRPRINT_IDENTIFY_ACTION_FLASH   = 1 << 0,
    AIRPRINT_IDENTIFY_ACTION_DISPLAY = 1 << 1,
    AIRPRINT_IDENTIFY_ACTION_SOUND   = 1 << 2,
}
AIRPRINT_IDENTIFY_ACTIONS_E;

typedef enum
{
    NETLINK_STATUS_DISCONNECTED = 0,        ///< WiFi未连接/连接断开
    NETLINK_STATUS_CONNECTED,               ///< WiFi已连接
    NETLINK_STATUS_CONNECTING,              ///< WiFi连接中
}
NETLINK_STATUS_E;

typedef enum
{
    WIFI_CONNECTION_DETAIL_INIT = -2,
    WIFI_CONNECTION_DETAIL_NONE = 0,
    /*
     *@brief when WiFi status is NETLINK_STATUS_DISCONNECTED
     */
    WIFI_DISCONNECTED_DETAIL_TIMEOUT = 1,   ///< WiFi连接超时
    WIFI_DISCONNECTED_DETAIL_NO_SSID,       ///< 未找到SSID
    WIFI_DISCONNECTED_DETAIL_ERR_PSK,       ///< 密码错误
    WIFI_DISCONNECTED_DETAIL_UNKNOWN,       ///< 连接失败，未知错误
    WIFI_DISCONNECTED_DETAIL_UNRECORD,      ///< 没有连接记录（手动重连时）
    WIFI_DISCONNECTED_DETAIL_DISCONNECT,    ///< WiFi状态由已连接直接跳转到未连接（主动断开或外部原因导致自动断开），面板视情况处理：弹窗提示 或 仅更改WiFi图标状态
    WIFI_DISCONNECTED_DETAIL_WPS_CANCEL,    ///< WPS连接取消
    /*
     *@brief when WiFi status is NETLINK_STATUS_CONNECTED
     */
    WIFI_CONNECTED_DETAIL_STA_SUCCESS = 101,///< WiFi连接成功
    WIFI_CONNECTED_DETAIL_WPS_SUCCESS,      ///< WPS连接成功
    WIFI_CONNECTED_DETAIL_CONNECTED,        ///< WiFi状态由未连接直接跳转到已连接（后台重连成功），面板视情况处理：弹窗提示 或 仅更改WiFi图标状态
    /*
     *@brief when WiFi status is NETLINK_STATUS_CONNECTING
     */
    WIFI_CONNECTING_DETAIL_STATION = 201,   ///< WiFi正在连接中
    WIFI_CONNECTING_DETAIL_WPS_PBC,         ///< WPS-PBC连接中
    WIFI_CONNECTING_DETAIL_WPS_PIN          ///< WPS-PIN连接中
}
WIFI_CONNECTION_DETAIL_E;

/*
 *@brief data format used by EVT_TYPE_NET_ETH_SPEED_REQUEST or EVT_TYPE_NET_ETH_SPEED_CHANGED
 */
typedef enum
{
    IFACE_SPEED_AUTO = 0,                   ///< 自适应模式
    IFACE_SPEED_100,                        ///< 强制百兆
    IFACE_SPEED_1000,                       ///< 强制千兆
}
IFACE_SPEED_E;

#if CONFIG_NET
/**
 * @brief Network module prolog(start and initialize).
 * @return result
 * @retval =0: success
 *         <0: fail
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
int32_t network_prolog  (void);
/**
 * @brief Network module epilog.
 * <AUTHOR> Xin
 * @data   2023-5-8
 */
void    network_epilog  (void);
#else
#define network_prolog()    ( { int32_t _rv = 0; printf("Unsupport Network"); _rv; } )   ///< Only output a log when CONFIG_NET is not defined.
#define network_epilog()    ( { printf("Unsupport Network"); } )                         ///< Only output a log when CONFIG_NET is not defined.
#endif /* CONFIG_NET */

#endif /* __NETWORK_H__ */
/**
 *@}
 */
