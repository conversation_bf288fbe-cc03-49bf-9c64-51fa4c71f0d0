#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include "pesf_jobs_scan.h"
#include "PEDK_event.h"
#include "app_manager.h"


#include <quickjs.h>


#define JS_SHARED_LIBRARY

#ifdef JS_SHARED_LIBRARY /* ç”ŸæˆJS CåŠ¨æ€åº“å¤‡é€?*/





static PESF_SCAN_DATA scan_job_data;
static PESF_SCAN_DATA save_data;

static  char papersize[50] = {0};

static   int s_mode =0;

static AppParam s_app_param;

static  char http_path[HTTP_URL_PATH_LEN] = {0};


#define SCAN_BOOK_PARAM_FILE "/emmccache/scanbookparam.dat"

#define countof(x) (sizeof(x) / sizeof((x)[0]))



static int js_scan_writebookparam( void )
{


   FILE *fp      =NULL;

   fp= fopen(SCAN_BOOK_PARAM_FILE,  "w");

   if(!fp)
   {
      printf(" open scan book param file fail........\n");
      return 0;
   }

   fwrite(&scan_job_data.bookparam, 1, sizeof(JSAddressBookParam), fp);

   close(fp);

   return 1;
}


static JSValue js_getScanToHttpPath(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
  

   return JS_NewString(ctx, http_path);

}

static JSValue js_setScanToHttpPath(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
   if(argc != 3 || !JS_IsString(argv[0]))
   {
     return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
   }
                                                                     
   const char *type = JS_ToCString(ctx, argv[0]);
   int is_certified = JS_ToBool(ctx, argv[1]);
   const char *header = JS_ToCString(ctx, argv[2]);
   strcpy(http_path,type); 

   snprintf(scan_job_data.bookparam.http.scanToHttpPath, sizeof(scan_job_data.bookparam.http.scanToHttpPath), type);
   scan_job_data.bookparam.http.scanToHttpPath[HTTP_URL_PATH_LEN-1] = '\0';

   snprintf(scan_job_data.bookparam.http.scanToHttpheaders, sizeof(scan_job_data.bookparam.http.scanToHttpheaders), header);
   scan_job_data.bookparam.http.scanToHttpheaders[HTTP_HEADERS_PATH_LEN-1] = '\0';

   if(is_certified)
   {
     scan_job_data.bookparam.http.is_certified = 1;
   }
   else
   {
     scan_job_data.bookparam.http.is_certified = 0;   
   }

   JS_FreeCString(ctx, type);  
   JS_FreeCString(ctx, header);  
   
   printf("http_path =%s \r\n", http_path);
   printf("Httpheaders =%s \r\n", scan_job_data.bookparam.http.scanToHttpheaders);
   
   return JS_TRUE;
}


static JSValue js_scan_setAutoDuplex(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_BOOL != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    /**
     * 构造函数
     * @since V1.0
     * @param {Boolean} mode 是否开启自动双面
     * <pre>
     * true：开启
     * false：关闭
     * </pre>
     */


    int ret = JS_ToBool(ctx, argv[0]);
    if( -1 == ret )
    {

       return JS_EXCEPTION;
    }

    if(ret)
       scan_job_data.scan_mode = SCAN_MODE_DADF;
    else
       scan_job_data.scan_mode = SCAN_MODE_AUTO;


    return JS_TRUE;
}
static JSValue js_scan_getAutoDuplex(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
    /**
     * 构造函数
     * @since V1.0
     * @param {Boolean} mode 是否开启自动双面
     * <pre>
     * true：开启
     * false：关闭
     * </pre>
     */

    JS_BOOL ret = (scan_job_data.scan_mode == SCAN_MODE_DADF)? 1:0;

    return JS_NewBool(ctx, ret);
}


// ColorTypeÀà

static JSValue js_scan_setColorType(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
    }

    /**
     * 构造函数
     * @since V1.0
     * @param {Number} type 扫描颜色
     * <ul>
     * <li>1:单色</li>
     * <li>2:灰色</li>
     * <li>3:彩色</li>
     * </ul>
     */

    int type;
    int ret = JS_ToInt32(ctx, &type, argv[0]);

    if(ret || (type < SCAN_COLOR_MONO || type > 3))
    {
        return JS_ThrowTypeError(ctx, "Color type range is [1:mono,2:gray,3:color]");
    }

   if(type == 1)
    {
      scan_job_data.scan_color = SCAN_COLOR_GRAY;
    }

    if(type == 2)
    {
      scan_job_data.scan_color = SCAN_COLOR_MONO;
    }

    if(type == 3)
    {
      scan_job_data.scan_color = SCAN_COLOR_RGB;
    }

    printf("setColorType =%d \r\n", scan_job_data.scan_color);

    return JS_TRUE;
}
static JSValue js_scan_getColorType(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    /**
     * 构造函数
     * @since V1.0
     * @param {Number} type 扫描颜色
     * <ul>
     * <li>1:单色</li>
     * <li>2:灰色</li>
     * <li>3:彩色</li>
     * </ul>
     */

    int type = 0;

    if(scan_job_data.scan_color == SCAN_COLOR_GRAY)
     {
       type = 1;
     }

     if(scan_job_data.scan_color == SCAN_COLOR_MONO)
     {
       type = 2;
     }

     if(scan_job_data.scan_color == SCAN_COLOR_RGB)
     {
       type = 3;
     }


    return JS_NewInt32(ctx, type);
}



static JSValue js_scan_setPaperSize(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
   if(argc != 1 || !JS_IsString(argv[0]))
   {
     return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
   }

   const char *type = JS_ToCString(ctx, argv[0]);
   strcpy(papersize,type);

   if(!strcmp(type, PESF_SCAN_AREA_A3))                        scan_job_data.scan_area = SCAN_PAPER_SIZE_A3 ;
   else if(!strcmp(type, PESF_SCAN_AREA_A4))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_A4 ;
   else if(!strcmp(type, PESF_SCAN_AREA_A4L))                  scan_job_data.scan_area = SCAN_PAPER_SIZE_A4L ;
   else if(!strcmp(type, PESF_SCAN_AREA_A5))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_A5 ;
   else if(!strcmp(type, PESF_SCAN_AREA_A5L))                  scan_job_data.scan_area = SCAN_PAPER_SIZE_A5L ;

   else if(!strcmp(type, PESF_SCAN_AREA_B4))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_B4 ;
   else if(!strcmp(type, PESF_SCAN_AREA_B5))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_B5 ;
   else if(!strcmp(type, PESF_SCAN_AREA_B5L))                  scan_job_data.scan_area = SCAN_PAPER_SIZE_B5L ;
   else if(!strcmp(type, PESF_SCAN_AREA_JIS_B6))               scan_job_data.scan_area = SCAN_PAPER_SIZE_B6 ;

   else if(!strcmp(type, PESF_SCAN_AREA_ISO_B5))               scan_job_data.scan_area = SCAN_PAPER_SIZE_ISO_B5 ;
   else if(!strcmp(type, PESF_SCAN_AREA_8K))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_8K ;
   else if(!strcmp(type, PESF_SCAN_AREA_BIG_16K))              scan_job_data.scan_area = SCAN_PAPER_SIZE_BIG_16K ;
   else if(!strcmp(type, PESF_SCAN_AREA_BIG_16KL))             scan_job_data.scan_area = SCAN_PAPER_SIZE_BIG_16KL ;

   else if(!strcmp(type, PESF_SCAN_AREA_16K))                  scan_job_data.scan_area = SCAN_PAPER_SIZE_CUSTOM_16K ;
   else if(!strcmp(type, PESF_SCAN_AREA_LETTER))               scan_job_data.scan_area = SCAN_PAPER_SIZE_LETTER ;
   else if(!strcmp(type, PESF_SCAN_AREA_LETTER_L))             scan_job_data.scan_area = SCAN_PAPER_SIZE_LETTER_L ;
   else if(!strcmp(type, PESF_SCAN_AREA_LEGAL))                scan_job_data.scan_area = SCAN_PAPER_SIZE_LEGAL13 ;

   else if(!strcmp(type, PESF_SCAN_AREA_FOLIO))                scan_job_data.scan_area = SCAN_PAPER_SIZE_FOLIO ;
   else if(!strcmp(type, PESF_SCAN_AREA_EXECUTIVE))            scan_job_data.scan_area = SCAN_PAPER_SIZE_EXECUTIVE ;
   else if(!strcmp(type, PESF_SCAN_AREA_INVOICE))              scan_job_data.scan_area = SCAN_PAPER_SIZE_INVOICE ;
   else if(!strcmp(type, PESF_SCAN_AREA_INVOICE_L))            scan_job_data.scan_area = SCAN_PAPER_SIZE_INVOICE_L ;

   else if(!strcmp(type, PESF_SCAN_AREA_A6))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_A6 ;
   else if(!strcmp(type, PESF_SCAN_AREA_B6))                   scan_job_data.scan_area = SCAN_PAPER_SIZE_B6 ;
   else if(!strcmp(type, PESF_SCAN_AREA_USER_DEFINE))          scan_job_data.scan_area = SCAN_PAPER_SIZE_USER_DEFINE ;
   else if(!strcmp(type, PESF_SCAN_AREA_LEDGER))               scan_job_data.scan_area = SCAN_PAPER_SIZE_LEDGER ;

   else if(!strcmp(type, PESF_SCAN_AREA_A3_WIDE))              scan_job_data.scan_area = SCAN_PAPER_SIZE_A3_WIDE1 ;
   else if(!strcmp(type, PESF_SCAN_AREA_SRA3))                 scan_job_data.scan_area = SCAN_PAPER_SIZE_SRA3 ;
   else if(!strcmp(type, PESF_SCAN_AREA_CARD))                 scan_job_data.scan_area = SCAN_PAPER_SIZE_CARD ;
   else                                                        scan_job_data.scan_area = SCAN_PAPER_SIZE_A4 ;

   JS_FreeCString(ctx, type);

   printf("scan_area =%d \r\n", scan_job_data.scan_area);

    return JS_TRUE;
}

static JSValue js_scan_getPaperSize(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }


   return JS_NewString(ctx, papersize);

}

static JSValue js_scan_setXSize(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{

   if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
     {
       return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
     }


   int area_width = 0;
   if(JS_ToInt32(ctx, &area_width, argv[0]))
    {
       return JS_EXCEPTION;
    }

    scan_job_data.area_width = area_width;

   printf("area_width =%d \r\n", (int)(scan_job_data.area_width));

  return JS_TRUE;

}

static JSValue js_scan_getXSize(JSContext *ctx, JSValueConst this_val,
                                                              int argc, JSValueConst *argv)
{
   if(argc != 0)
   {
    return JS_ThrowTypeError(ctx, "No input parameters are required");
   }

   return JS_NewInt32(ctx, scan_job_data.area_width);
 }


static JSValue js_scan_setYSize(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{

   if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
     {
       return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
     }


   int area_height = 0;
   if(JS_ToInt32(ctx, &area_height, argv[0]))
    {
       return JS_EXCEPTION;
    }

    scan_job_data.area_height = area_height;

   printf("area_height =%d \r\n", (int)(scan_job_data.area_height));

  return JS_TRUE;

}

static JSValue js_scan_getYSize(JSContext *ctx, JSValueConst this_val,
                                                              int argc, JSValueConst *argv)
{
   if(argc != 0)
   {
    return JS_ThrowTypeError(ctx, "No input parameters are required");
   }

   return JS_NewInt32(ctx, scan_job_data.area_height);
 }


static JSValue js_scan_setFileFmtType(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{

    if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }

    /**
     * 构造函数
     * @since V1.0
     * @param {Number} type 文件类型
     *
     * | 文件类型 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | JPEG | 0 | 支持 | 支持 |
     * | PDF | 1 | 支持 | 支持 |
     * | TIFF | 2 | 支持 | 支持 |
     * | OFD | 3 | 支持 | 支持 |
     */

    int file_type = 0;
    if(JS_ToInt32(ctx, &file_type, argv[0]))
     {
        return JS_EXCEPTION;
     }

    if(file_type == 0)
     {
       scan_job_data.file_type = SCAN_FILE_JPEG;
     }

     if(file_type == 1)
     {
       scan_job_data.file_type = SCAN_FILE_PDF;
     }

     if(file_type == 2)
     {
       scan_job_data.file_type =SCAN_FILE_TIFF;
     }

     if(file_type == 3)
     {
       scan_job_data.file_type =SCAN_FILE_OFD;
     }


    printf("setFileFmtType =%d \r\n", scan_job_data.file_type);

    return JS_TRUE;
}

static JSValue js_scan_getFileFmtType(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    /**
     * 构造函数
     * @since V1.0
     * @param {Number} type 文件类型
     *
     * | 文件类型 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | JPEG | 0 | 支持 | 支持 |
     * | PDF | 1 | 支持 | 支持 |
     * | TIFF | 2 | 支持 | 支持 |
     * | OFD | 3 | 支持 | 支持 |
     */


    if(SCAN_FILE_PDF == scan_job_data.file_type)   return JS_NewInt32(ctx, 1);
    else if(SCAN_FILE_JPEG == scan_job_data.file_type)   return JS_NewInt32(ctx, 0);
    else if(SCAN_FILE_TIFF == scan_job_data.file_type)   return JS_NewInt32(ctx, 2);
    else if(SCAN_FILE_OFD == scan_job_data.file_type)   return JS_NewInt32(ctx, 3);
    else
       return JS_EXCEPTION;

}



static JSValue js_scan_getResolution(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    /**
    * 构造函数
    * @since V1.0
    * @param {Number} dpi 分辨率
    *
    * | 分辨率 | 值 | Kanas | 4020 |
    * |:--:|:--:|:--:|:--:|
    * | 75dpi x 75dpi | 0 | 支持 | 支持 |
    * | 150dpi x 150dpi | 1 | 支持 | 支持 |
    * | 200dpi x 200dpi | 2 | 支持 | 不支持 |
    * | 300dpi x 300dpi | 3 | 支持 | 支持 |
    * | 600dpi x 600dpi | 4 | 支持 | 支持 |
    * | 1200dpi x 1200dpi | 5 | 支持 | 不支持 |
    */


    int xres =0;

    if(scan_job_data.xres == DPI_75)
    {
      xres = 0;
    }
    else if(scan_job_data.xres == DPI_150)
    {
      xres = 1;
    }
    else if(scan_job_data.xres == DPI_200)
    {
      xres = 2;
    }
    else if(scan_job_data.xres == DPI_300)
    {
      xres = 3;
    }
    else if(scan_job_data.xres == DPI_600)
    {
        xres = 4;
    }
    else if(scan_job_data.xres == DPI_1200)
    {
        xres = 5;

    }

    printf("getResolution %d\r\n",xres);

    return JS_NewInt32(ctx, xres);
}


static JSValue js_scan_setResolution(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
    }



    int data;
    int ret = JS_ToInt32(ctx, &data, argv[0]);
    if(ret || (data < 0 || data > DPI_1200))
    {
        return JS_ThrowTypeError(ctx, "Resolution range is [1:75dpi,2:150dpi,3:300dpi,4:600dpi]");
    }

    /**
    * 构造函数
    * @since V1.0
    * @param {Number} dpi 分辨率
    *
    * | 分辨率 | 值 | Kanas | 4020 |
    * |:--:|:--:|:--:|:--:|
    * | 75dpi x 75dpi | 0 | 支持 | 支持 |
    * | 150dpi x 150dpi | 1 | 支持 | 支持 |
    * | 200dpi x 200dpi | 2 | 支持 | 不支持 |
    * | 300dpi x 300dpi | 3 | 支持 | 支持 |
    * | 600dpi x 600dpi | 4 | 支持 | 支持 |
    * | 1200dpi x 1200dpi | 5 | 支持 | 不支持 |
    */

    if(data == 0)
    {
        scan_job_data.xres = DPI_75;
        scan_job_data.yres = DPI_75;
    }
    else if(data == 1)
    {
        scan_job_data.xres = DPI_150;
        scan_job_data.yres = DPI_150;
    }
    else if(data == 2)
    {
        scan_job_data.xres = DPI_200;
        scan_job_data.yres = DPI_200;
    }
    else if(data == 3)
    {
        scan_job_data.xres = DPI_300;
        scan_job_data.yres = DPI_300;
    }
    else if(data == 4)
    {
        scan_job_data.xres = DPI_600;
        scan_job_data.yres = DPI_600;
    }
    else if(data == 5)
    {
        scan_job_data.xres = DPI_1200;
        scan_job_data.yres = DPI_1200;
    }


    printf("Resolution=%d \r\n", scan_job_data.xres);

    return JS_TRUE;
}




static JSValue js_pesf_scan_set_parameter(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = -1;


    JSValueConst obj = argv[0];

    JSValueConst job = JS_GetPropertyStr( ctx, obj, "job");
    JSValueConst scan_mode = JS_GetPropertyStr( ctx, obj, "scan_mode");
    JSValueConst scan_color = JS_GetPropertyStr( ctx, obj, "scan_color");
    JSValueConst xres = JS_GetPropertyStr( ctx, obj, "xres");
    JSValueConst yres = JS_GetPropertyStr( ctx, obj, "yres");

    JSValueConst scan_area = JS_GetPropertyStr( ctx, obj, "scan_area");
    JSValueConst file_type = JS_GetPropertyStr( ctx, obj, "file_type");
    JSValueConst area_width = JS_GetPropertyStr( ctx, obj, "area_width");
    JSValueConst area_height = JS_GetPropertyStr( ctx, obj, "area_height");

    JS_ToInt32( ctx, (int32_t*) &scan_job_data.job, job );
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.scan_mode, scan_mode );
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.scan_color, scan_color );
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.xres, xres );

    JS_ToInt32( ctx, (int32_t*) &scan_job_data.yres, yres );
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.scan_area, scan_area);
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.file_type, file_type );
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.area_width, area_width );
    JS_ToInt32( ctx, (int32_t*) &scan_job_data.area_height, area_height );


   printf("job=%d \r\n scan_mode=%d  \r\n scan_color=%d \r\nxres=%d \r\nscan_area=%d \r\n file_type=%d \r\n",scan_job_data.job,scan_job_data.scan_mode,scan_job_data.scan_color, \
                                                                                      scan_job_data.xres,scan_job_data.scan_area,scan_job_data.file_type);

   printf("area_width = %d area_height=%d \r\n", (int)(scan_job_data.area_width),(int)(scan_job_data.area_height));

   // ret = pesf_pkt_send(&pkt, PESF_CMD_SCAN_SET, (unsigned char*)&scan_job_data, sizeof(PESF_SCAN_DATA));
    return JS_NewInt32(ctx, ret);

}



static JSValue js_scan_setJobType(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one string parmeter");
    }


    const char *type = JS_ToCString(ctx, argv[0]);

    if(!strcmp(type, "SCAN_TO_APP") ||!strcmp(type, "SCAN_TO_HTTP") || !strcmp(type, "SCAN_TO_PC") || !strcmp(type, "SCAN_TO_EMAIL") || !strcmp(type, "SCAN_TO_SMB") || !strcmp(type, "SCAN_TO_USB") || !strcmp(type, "SCAN_TO_FTP"))
    {
       // strcpy(file->file_type, type);
        if(!strcmp(type, "SCAN_TO_PC"))  scan_job_data.job = SCAN_JOB_TYPE_HOST_PUSH;
        else if(!strcmp(type, "SCAN_TO_EMAIL"))  scan_job_data.job  = SCAN_JOB_TYPE_EMAIL;
        else if(!strcmp(type, "SCAN_TO_SMB"))  scan_job_data.job  = SCAN_JOB_TYPE_SMB;
        else if(!strcmp(type, "SCAN_TO_USB"))  scan_job_data.job  = SCAN_JOB_TYPE_U_DISK;
        else if(!strcmp(type, "SCAN_TO_FTP"))  scan_job_data.job  = SCAN_JOB_TYPE_FTP;
        else if(!strcmp(type, "SCAN_TO_HTTP"))  scan_job_data.job  = SCAN_JOB_TYPE_HTTP;
		else if(!strcmp(type, "SCAN_TO_APP"))  scan_job_data.job  = SCAN_JOB_TYPE_APP;
        
        JS_FreeCString(ctx, type);
    }
    else
    {
        JS_FreeCString(ctx, type);
        return JS_ThrowTypeError(ctx, "It's not file type [PDF/OFD/TIFF/JPEG]");
    }

    printf("setJobType =%d \r\n", scan_job_data.job );

    return JS_TRUE;

}


static JSValue js_scan_getJobId(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

   int job_id =0;

    return JS_NewInt32(ctx, job_id);
}

static JSValue js_scan_getJobType(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    if(SCAN_JOB_TYPE_HOST_PUSH == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_PC");
    else if(SCAN_JOB_TYPE_EMAIL == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_EMAIL");
    else if(SCAN_JOB_TYPE_SMB == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_SMB");
    else if(SCAN_JOB_TYPE_U_DISK == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_USB");
    else if(SCAN_JOB_TYPE_FTP == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_FTP");
    else if(SCAN_JOB_TYPE_HTTP == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_HTTP");
	else if(SCAN_JOB_TYPE_APP == scan_job_data.job)   return JS_NewString(ctx, "SCAN_TO_APP");
    else 
       return JS_EXCEPTION;

}

static void js_scan_app_getfilename(JSContext *ctx,JSValueConst *argv)
{                                    
   int ret = 0;
   int timer =60*4;
   int i =0;
   int status =0;
   int respond;
   int len =sizeof(AppParam);
   JSValueConst obj = argv[2];

   sleep( 1 );   
      
   for(i = 0 ; i < timer ; i++)
   {
     SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_APP, ret, 0, 0);
     RecvMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_APP, &respond, &s_app_param, &len, 5);

     
     if(s_app_param.result_code == 1)
     {
       status =1;
       JS_SetPropertyStr(ctx, obj, "error_info", JS_NewString(ctx, "Success"));
       JS_SetPropertyStr(ctx, obj, "file_name", JS_NewString(ctx, s_app_param.name));
       JS_SetPropertyStr(ctx, obj, "file_path", JS_NewString(ctx, "/emmcimage/pedk/"));
       JS_SetPropertyStr(ctx, obj, "result_code", JS_NewInt32(ctx, 0));
       break;
     }
     
     if(s_app_param.result_code == 2)
     {
       status =1;
       JS_SetPropertyStr(ctx, obj, "error_info", JS_NewString(ctx, "Operation cancelled"));
       JS_SetPropertyStr(ctx, obj, "file_name", JS_NewString(ctx, ""));
       JS_SetPropertyStr(ctx, obj, "file_path", JS_NewString(ctx, ""));
       JS_SetPropertyStr(ctx, obj, "result_code", JS_NewInt32(ctx, 13));
       break;
     }  
    
     sleep(1);

   }


   if(status == 0)
   {
      JS_SetPropertyStr(ctx, obj, "error_info", JS_NewString(ctx, "Timeout"));
      JS_SetPropertyStr(ctx, obj, "file_name", JS_NewString(ctx, ""));
      JS_SetPropertyStr(ctx, obj, "file_path", JS_NewString(ctx, ""));
      JS_SetPropertyStr(ctx, obj, "result_code", JS_NewInt32(ctx, 18));
   }  

   printf("app over  getfilename %s %d\r\n", s_app_param.name,s_app_param.result_code);
     
}


static JSValue js_pesf_scan_job_start(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 1;
    int wo_num = 0;
    printf("scan_job start\r\n" );

    if(JS_ToInt32(ctx, &wo_num, argv[0]))
    {
      // return JS_EXCEPTION;
    }

    scan_job_data.wo_num = wo_num;

    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_START, ret, sizeof(PESF_SCAN_DATA), (unsigned char *)&scan_job_data);

    if( SCAN_JOB_TYPE_APP == scan_job_data.job )
    {
      js_scan_app_getfilename(ctx,argv);
    }
    printf("scan_job end\r\n" );
    return JS_NewInt32(ctx, ret);
}

static JSValue js_pesf_scan_job_cancel(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;

    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_CANCEL, ret, 0, 0);
    printf("scan_job cancel\r\n" );
    return JS_NewInt32(ctx, ret);
}

static JSValue js_pesf_scan_job_continue(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;

    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_CONTINUE, ret, 0, 0);
    printf("scan_job continue\r\n" );
    return JS_NewInt32(ctx, ret);
}

static JSValue js_pesf_scan_job_finish(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    int ret = 0;

    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_FINISH, ret, 0, 0);
    printf("scan_job finish\r\n" );
    return JS_NewInt32(ctx, ret);
}


static JSValue js_scan_getScanMode(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    /**
    * 构造函数
    * @since V1.0
    * @param {Number} mode 模式
    *
    * | 模式 | 值 | Kanas | 4020 |
    * |:--:|:--:|:--:|:--:|
    * | 自动 | 0 | 支持 | 支持 |
    * | DADF | 1 | 支持 | 支持 |
    * | ADF | 2 | 支持 | 支持 |
    * | FB | 3 | 支持 | 支持 |
    * | MADF | 4 | 支持 | 不支持 |
    * | DADF_UP | 5 | 支持 | 不支持 |
    * | RDADF | 6 | 支持 | 不支持 |
    */

    int mode =0;

    mode = s_mode;

    printf("getScanmode =%d\r\n",mode);

    return JS_NewInt32(ctx, mode );
}


static JSValue js_scan_setScanMode(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
    }

     /**
     * 构造函数
     * @since V1.0
     * @param {Number} mode 模式
     *
     * | 模式 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | 自动 | 0 | 支持 | 支持 |
     * | DADF | 1 | 支持 | 支持 |
     * | ADF | 2 | 支持 | 支持 |
     * | FB | 3 | 支持 | 支持 |
     * | MADF | 4 | 支持 | 不支持 |
     * | DADF_UP | 5 | 支持 | 不支持 |
     * | RDADF | 6 | 支持 | 不支持 |
     */

    int data;
    int ret = JS_ToInt32(ctx, &data, argv[0]);
    if(ret || (data < SCAN_MODE_AUTO || data > SCAN_MODE_RADF))
    {
        return JS_ThrowTypeError(ctx, "setScanSource range is []");
    }

    s_mode = data;

    if(data == 0)
    {
      scan_job_data.scan_mode = SCAN_MODE_AUTO;
    }
    else if(data == 1)
    {
      scan_job_data.scan_mode = SCAN_MODE_DADF;
    }
    else if(data == 2)
    {
      scan_job_data.scan_mode = SCAN_MODE_ADF;
    }
    else if(data == 3)
    {
      scan_job_data.scan_mode = SCAN_MODE_FB;
    }
    else if(data == 4)
    {
        scan_job_data.scan_mode = SCAN_MODE_MADF;
    }
    else if(data == 5)
    {
        scan_job_data.scan_mode = SCAN_MODE_RADF;
    }
    else if(data == 6)
    {
        scan_job_data.scan_mode = SCAN_MODE_RADF;
    }


    printf("setScanMode=%d \r\n", scan_job_data.scan_mode);

    return JS_TRUE;
}


static JSValue js_scan_getScanSource(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    printf("getScanSource \r\n");

    return JS_NewInt32(ctx, scan_job_data.scan_mode);
}


static JSValue js_scan_setScanSource(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
    }



    int data;
    int ret = JS_ToInt32(ctx, &data, argv[0]);
    if(ret || (data < SCAN_MODE_AUTO || data > SCAN_MODE_RADF))
    {
        return JS_ThrowTypeError(ctx, "setScanSource range is []");
    }

    scan_job_data.scan_mode = data;

    printf("setScanSource=%d \r\n", scan_job_data.scan_mode);

    return JS_TRUE;
}



static JSValue js_scan_getScanQuality(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
   /**
     * 构造函数
     * @since V1.0
     * @param {Number} quality 扫描质量
     *
     * | 翻页方式 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | TXT | 0 | 支持 | 不支持 |
     * | MIXED | 1 | 支持 | 不支持 |
     * | PICTURE | 2 | 支持 | 不支持 |
     */
    int data=0;

    if(scan_job_data.quality == QUALITY_TXT)
    {
      data=0;
    }
    else if(scan_job_data.quality == QUALITY_MIXED)
    {
      data=1;
    }
    else if(scan_job_data.quality == QUALITY_PICTURE)
    {
      data=2;
    }

    printf("getScanQuality %d\r\n",data);

    return JS_NewInt32(ctx, data);
}



static JSValue js_scan_setScanQuality(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one int parmeter");
    }

    /**
     * 构造函数
     * @since V1.0
     * @param {Number} quality 扫描质量
     *
     * | 翻页方式 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | TXT | 0 | 支持 | 不支持 |
     * | MIXED | 1 | 支持 | 不支持 |
     * | PICTURE | 2 | 支持 | 不支持 |
     */

    int data;
    int ret = JS_ToInt32(ctx, &data, argv[0]);
    if(ret || (data < 0 || data > 2))
    {
        return JS_ThrowTypeError(ctx, "setScanQuality range is []");
    }

    if(data == 0)
    {
      scan_job_data.quality = QUALITY_TXT;
    }
    else if(data == 1)
    {
      scan_job_data.quality = QUALITY_MIXED;
    }
    else if(data == 2)
    {
      scan_job_data.quality = QUALITY_PICTURE;
    }


    printf("setScanQuality=%d \r\n", scan_job_data.quality);

    return JS_TRUE;
}



static JSValue js_scan_getCombination(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
    /**
     * 设置组合扫描模式
     * @since V1.0
     * @param {Boolean} mode 组合扫描模式开/关
     * @returns {String} 执行结果
     * @see class/pesf/common/errorno.js~ErrorNo.html
     */

    printf("getCombination\r\n");
    JS_BOOL ret = (scan_job_data.combination == 1)? 1:0;
    return JS_NewBool(ctx, ret);
}



static JSValue js_scan_setCombination(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
     if(argc != 1 || JS_TAG_BOOL != JS_VALUE_GET_NORM_TAG(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "Must be only one bool parmeter");
    }

    /**
     * 设置组合扫描模式
     * @since V1.0
     * @param {Boolean} mode 组合扫描模式开/关
     * @returns {String} 执行结果
     * @see class/pesf/common/errorno.js~ErrorNo.html
     */

    int ret = JS_ToBool(ctx, argv[0]);
    if( -1 == ret )
    {

       return JS_EXCEPTION;
    }

    scan_job_data.combination = ret;

    printf("setCombination=%d \r\n", scan_job_data.combination);

    return JS_TRUE;
}

static JSValue js_scan_setScanQuota(JSContext *ctx, JSValueConst this_val,
                                                              int argc, JSValueConst *argv)
{

   int data;
   int ret = JS_ToInt32(ctx, &data, argv[0]);

   scan_job_data.quota = data;
   printf("setQuota %d \r\n" ,data);
   return JS_NewInt32(ctx, ret);


}

static JSValue js_scan_setScansmb(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv)
{

   int ret = 0;
   JSValueConst obj = argv[0];

   JSValueConst index = JS_GetPropertyStr( ctx, obj, "index");
   JSValueConst user_name = JS_GetPropertyStr( ctx, obj, "user_name");
   JSValueConst server_name = JS_GetPropertyStr( ctx, obj, "server_name");
   JSValueConst login_name = JS_GetPropertyStr( ctx, obj, "login_name");
   JSValueConst server_path = JS_GetPropertyStr( ctx, obj, "server_path");
   JSValueConst password = JS_GetPropertyStr( ctx, obj, "password");
   JSValueConst port = JS_GetPropertyStr( ctx, obj, "port");
   JSValueConst is_anonymity = JS_GetPropertyStr( ctx, obj, "is_anonymity");

   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.smb.smb_index, index );
   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.smb.smb_port, port );
   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.smb.is_anonymity, is_anonymity );

   const char *pesf_user_name = JS_ToCString(ctx, user_name);
   snprintf(scan_job_data.bookparam.smb.smb_user_name, sizeof(scan_job_data.bookparam.smb.smb_user_name), pesf_user_name);
   scan_job_data.bookparam.smb.smb_user_name[SMB_USER_NAME_LEN-1] = '\0';

   const char *pesf_server_name = JS_ToCString(ctx, server_name);
   snprintf(scan_job_data.bookparam.smb.smb_server_name, sizeof(scan_job_data.bookparam.smb.smb_server_name), pesf_server_name);
   scan_job_data.bookparam.smb.smb_server_name[SMB_SERVER_NAME_LEN-1] = '\0';

   const char *pesf_login_name = JS_ToCString(ctx, login_name);
   snprintf(scan_job_data.bookparam.smb.smb_login_name, sizeof(scan_job_data.bookparam.smb.smb_login_name), pesf_login_name);
   scan_job_data.bookparam.smb.smb_login_name[SMB_LOGIN_NAME_LEN-1] = '\0';

   const char *pesf_server_path = JS_ToCString(ctx, server_path);
   snprintf(scan_job_data.bookparam.smb.smb_server_path, sizeof(scan_job_data.bookparam.smb.smb_server_path), pesf_server_path);
   scan_job_data.bookparam.smb.smb_server_path[SMB_SERVER_PATH_LEN-1] = '\0';

   const char *pesf_password = JS_ToCString(ctx, password);
   snprintf(scan_job_data.bookparam.smb.smb_password, sizeof(scan_job_data.bookparam.smb.smb_password), pesf_password);
   scan_job_data.bookparam.smb.smb_password[SMB_PASSWORD_LEN-1] = '\0';

   js_scan_writebookparam( );

   printf(" smb_server_name %s \r\n" ,scan_job_data.bookparam.smb.smb_server_name);

   JS_FreeCString(ctx, pesf_user_name);
   JS_FreeCString(ctx, pesf_server_name);
   JS_FreeCString(ctx, pesf_login_name);
   JS_FreeCString(ctx, pesf_server_path);
   JS_FreeCString(ctx, pesf_password);

   return JS_NewInt32(ctx, ret);
}

static JSValue js_scan_setScanftp(JSContext *ctx, JSValueConst this_val,
                                      int argc, JSValueConst *argv)
{

   int ret = 0;
   JSValueConst obj = argv[0];

   JSValueConst index = JS_GetPropertyStr( ctx, obj, "index");
   JSValueConst user_name = JS_GetPropertyStr( ctx, obj, "user_name");
   JSValueConst server_name = JS_GetPropertyStr( ctx, obj, "server_name");
   JSValueConst login_name = JS_GetPropertyStr( ctx, obj, "login_name");
   JSValueConst server_path = JS_GetPropertyStr( ctx, obj, "server_path");
   JSValueConst password = JS_GetPropertyStr( ctx, obj, "password");
   JSValueConst port = JS_GetPropertyStr( ctx, obj, "port");
   JSValueConst is_anonymity = JS_GetPropertyStr( ctx, obj, "is_anonymity");

   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.ftp.ftp_index, index );
   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.ftp.ftp_port, port );
   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.ftp.is_anonymity, is_anonymity );

   const char *pesf_user_name = JS_ToCString(ctx, user_name);
   snprintf(scan_job_data.bookparam.ftp.ftp_user_name, sizeof(scan_job_data.bookparam.ftp.ftp_user_name), pesf_user_name);
   scan_job_data.bookparam.ftp.ftp_user_name[FTP_USER_NAME_LEN-1] = '\0';

   const char *pesf_server_name = JS_ToCString(ctx, server_name);
   snprintf(scan_job_data.bookparam.ftp.ftp_server_name, sizeof(scan_job_data.bookparam.ftp.ftp_server_name), pesf_server_name);
   scan_job_data.bookparam.ftp.ftp_server_name[FTP_SERVER_NAME_LEN-1] = '\0';

   const char *pesf_login_name = JS_ToCString(ctx, login_name);
   snprintf(scan_job_data.bookparam.ftp.ftp_login_name, sizeof(scan_job_data.bookparam.ftp.ftp_login_name), pesf_login_name);
   scan_job_data.bookparam.ftp.ftp_login_name[FTP_LOGIN_NAME_LEN-1] = '\0';

   const char *pesf_server_path = JS_ToCString(ctx, server_path);
   snprintf(scan_job_data.bookparam.ftp.ftp_server_path, sizeof(scan_job_data.bookparam.ftp.ftp_server_path), pesf_server_path);
   scan_job_data.bookparam.ftp.ftp_server_path[FTP_SERVER_PATH_LEN-1] = '\0';

   const char *pesf_password = JS_ToCString(ctx, password);
   snprintf(scan_job_data.bookparam.ftp.ftp_password, sizeof(scan_job_data.bookparam.ftp.ftp_password), pesf_password);
   scan_job_data.bookparam.ftp.ftp_password[FTP_PASSWORD_LEN-1] = '\0';

   js_scan_writebookparam( );

   printf(" ftp_server_name %s \r\n" ,scan_job_data.bookparam.ftp.ftp_server_name);

   JS_FreeCString(ctx, pesf_user_name);
   JS_FreeCString(ctx, pesf_server_name);
   JS_FreeCString(ctx, pesf_login_name);
   JS_FreeCString(ctx, pesf_server_path);
   JS_FreeCString(ctx, pesf_password);

   return JS_NewInt32(ctx, ret);
}

static JSValue js_scan_setScanemail(JSContext *ctx, JSValueConst this_val,
                                      int argc, JSValueConst *argv)
{

   int ret = 0;

   JSValueConst obj = argv[0];

   JSValueConst index = JS_GetPropertyStr( ctx, obj, "index");
   JSValueConst mail_addr = JS_GetPropertyStr( ctx, obj, "mail_addr");

   JS_ToInt32( ctx, (int32_t*) &scan_job_data.bookparam.mail_index, index );
   const char *pesf_mail_name = JS_ToCString(ctx, mail_addr);
   snprintf(scan_job_data.bookparam.mail_addr[scan_job_data.bookparam.mail_index], sizeof(scan_job_data.bookparam.mail_addr[scan_job_data.bookparam.mail_index]), pesf_mail_name);
   scan_job_data.bookparam.mail_addr[scan_job_data.bookparam.mail_index][MAIL_ADDR_LEN-1] = '\0';

   js_scan_writebookparam( );
   printf(" email_index[%d]= %s \r\n" ,scan_job_data.bookparam.mail_index,scan_job_data.bookparam.mail_addr[scan_job_data.bookparam.mail_index]);
   JS_FreeCString(ctx, pesf_mail_name);
   return JS_NewInt32(ctx, ret);
}


JSValue js_scan_removeFtpAddr(JSContext *ctx, JSValueConst this_val,
                              int argc, JSValueConst *argv)
{

   printf(" removeFtpAddr successfully \r\n");
   return JS_TRUE;
}

JSValue js_scan_removeEmailAddr(JSContext *ctx, JSValueConst this_val,
                              int argc, JSValueConst *argv)
{

  printf(" removeEmailAddr successfully \r\n");
  return JS_TRUE;
}

JSValue js_scan_removeSMBAddr(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    printf(" removeSMBAddr successfully \r\n");
    return JS_TRUE;
}


static JSValue js_scan_getImageColor(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{

    /**
     * 构造函数
     * @since V1.0
     * @param {Number} mode 模式
     * @param {Number} balance 平衡度
     *
     * | 模式 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | 锐度 | 0 | 1~5 | 不支持 |
     * | 饱和度 | 1 | 1~5 | 不支持 |
     * | 对比度 | 2 | 1~5 | 不支持 |
     * | 亮度 | 3 | 1~5 | 不支持 |
     * | 色度 | 4 | 1~5 | 不支持 |
     * | 背景消除 | 5 | 1~5 | 不支持 |
     */

    int mode;
    int balance=0;
    if(JS_ToInt32(ctx,&mode, argv[0]))
    {
        printf("JS_ToInt32 failed!\n");
        return JS_EXCEPTION;
    }

    switch(mode)
    {
        case PESF_SCAN_SHARPNESS:
             balance = scan_job_data.sharpness ;

            break;
        case PESF_SCAN_SATURATION:
             balance = scan_job_data.saturation  ;

            break;
        case PESF_SCAN_CONTRAST:
             balance = scan_job_data.contrast  ;

            break;
        case PESF_SCAN_BRIGHTNESS:
             balance =  scan_job_data.brightness   ;

            break;
        case PESF_SCAN_HUE:
            balance = scan_job_data.hue    ;

            break;
        case PESF_SCAN_BACKGROUNDREMOVE:
            balance = scan_job_data.backgroundremove   ;

            break;
        default:
            return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
    }

     printf("getImageColor balance =%d!\n",balance);

    return JS_NewInt32(ctx, balance);
}



static JSValue js_scan_setImageColor(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{

     if(argc != 2 || JS_TAG_INT != JS_VALUE_GET_NORM_TAG(argv[0]))
     {
         return JS_ThrowTypeError(ctx, "Must be  int parmeter");
     }


    /**
     * 构造函数
     * @since V1.0
     * @param {Number} mode 模式
     * @param {Number} balance 平衡度
     *
     * | 模式 | 值 | Kanas | 4020 |
     * |:--:|:--:|:--:|:--:|
     * | 锐度 | 0 | 1~5 | 不支持 |
     * | 饱和度 | 1 | 1~5 | 不支持 |
     * | 对比度 | 2 | 1~5 | 不支持 |
     * | 亮度 | 3 | 1~5 | 不支持 |
     * | 色度 | 4 | 1~5 | 不支持 |
     * | 背景消除 | 5 | 1~5 | 不支持 |
     */

    int mode;
    if(JS_ToInt32(ctx,&mode, argv[0]))
    {
        printf("JS_ToInt32 failed!\n");
        return JS_EXCEPTION;
    }

    int balance;
    if(JS_ToInt32(ctx,&balance, argv[1]))
    {
        printf("JS_ToInt32 failed!\n");
        return JS_EXCEPTION;
    }

    switch(mode)
     {
         case PESF_SCAN_SHARPNESS:
              scan_job_data.sharpness    = balance;
              printf("sharpness=%d \r\n", (int)(scan_job_data.sharpness));

             break;
         case PESF_SCAN_SATURATION:
             scan_job_data.saturation    = balance;
             printf("saturation=%d \r\n", (int)(scan_job_data.saturation));

             break;
         case PESF_SCAN_CONTRAST:
             scan_job_data.contrast    = balance;
             printf("contrast=%d \r\n", (int)(scan_job_data.contrast));

             break;
         case PESF_SCAN_BRIGHTNESS:
             scan_job_data.brightness    = balance;
             printf("brightness=%d \r\n", (int)(scan_job_data.brightness));

             break;
         case PESF_SCAN_HUE:
             scan_job_data.hue    = balance;
             printf("hue=%d \r\n", (int)(scan_job_data.hue));

             break;
         case PESF_SCAN_BACKGROUNDREMOVE:
             scan_job_data.backgroundremove    = balance;
             printf("backgroundremove=%d \r\n", (int)(scan_job_data.backgroundremove));

             break;
         default:
             return JS_ThrowTypeError(ctx, "EINVALIDPARAM");
     }


    return JS_TRUE;
}


static JSValue js_scan_addParameter(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 2 || !JS_IsString(argv[0]) )
    {
        return JS_ThrowTypeError(ctx, "The parmeter are the prop_key(string) ");
    }

    const char *prop_key = JS_ToCString(ctx, argv[0]);

    if(!strcmp(prop_key, "SCAN_PARAM_INPUTPAPERSIZE"))
    {

       save_data.scan_area = scan_job_data.scan_area;
       save_data.area_width = scan_job_data.area_width;
       save_data.area_height = scan_job_data.area_height;

      // printf("area_width = %d area_height=%d scan_area=%d\r\n", scan_job_data.area_width,scan_job_data.area_height,scan_job_data.scan_area);
    }
    else if(!strcmp(prop_key, "SCAN_PARAM_RESOLUTION"))
    {
       save_data.xres = scan_job_data.xres;
       save_data.yres = scan_job_data.yres;

       printf("res->dpi = %d\n",(int)(scan_job_data.xres));
    }
    else if(!strcmp(prop_key, "SCAN_PARAM_SOURCE"))
    {

      printf("setScanSource=%d \r\n", (int)(scan_job_data.scan_mode));

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_COLORTYPE"))
    {
      save_data.scan_color = scan_job_data.scan_color;

      printf("setColorType =%d \r\n", (int)(scan_job_data.scan_color));

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_FILEFMTTYPE"))
    {

      save_data.file_type = scan_job_data.file_type;

      printf("scan_job_data.file_type = %d\n", (int)(scan_job_data.file_type));

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_AUTODUPLEX"))
    {

       save_data.scan_mode = scan_job_data.scan_mode;

       printf("dadf->mode = %d\n", (int)(scan_job_data.scan_mode));
    }
    else if(!strcmp(prop_key, "SCAN_PARAM_QUALITY"))
    {
       save_data.quality = scan_job_data.quality;

       printf("setScanQuality=%d \r\n", (int)(scan_job_data.quality));

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_MODE"))
    {
       save_data.scan_mode = scan_job_data.scan_mode;

       printf("setScanMode=%d \r\n", (int)(scan_job_data.scan_mode));

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_COMBINATION"))
    {

      save_data.combination = scan_job_data.combination;
      printf("setCombination=%d \r\n", (int)(scan_job_data.combination));

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_IMAGE_COLOR"))
    {

       save_data.sharpness = scan_job_data.sharpness;
       save_data.saturation = scan_job_data.saturation;
       save_data.contrast = scan_job_data.contrast;
       save_data.brightness = scan_job_data.brightness;
       save_data.hue = scan_job_data.hue;
       save_data.backgroundremove = scan_job_data.backgroundremove;

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_ADDRESSBOOKPARAM"))
    {

    }
    else if(!strcmp(prop_key, "SCAN_PARAM_USBDIR"))
    {

    }
    else
    {
        JS_FreeCString(ctx, prop_key);
        return JS_TRUE;
    }

    JS_FreeCString(ctx, prop_key);

    return JS_TRUE;
}
JSValue js_scan_removeParameter(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    if(argc != 1 || !JS_IsString(argv[0]))
    {
        return JS_ThrowTypeError(ctx, "The parmeter is the prop_key(string)");
    }

    const char *prop_key = JS_ToCString(ctx, argv[0]);
    //memset(scan_job_data, 0, sizeof(PESF_SCAN_DATA));

    JS_FreeCString(ctx, prop_key);

    return JS_TRUE;
}

static JSValue js_scan_getFrontFPGAVersion(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	  int ret = 0;
	  int respond  =0;
	  int len=32 * sizeof(unsigned char);
	  unsigned char  pVersion[32]={0};

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    printf("get_front_fpgaversion start\r\n" );
    //MSG_SCAN_SUB_GET_FRONT_FPGAVERSION
    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_GET_FRONT_FPGAVERSION, ret, 0, 0);
    ret=RecvMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_SENT_FRONT_FPGAVERSION, &respond, pVersion, &len, 1000);
    printf("get_front_fpgaversion end %d \r\n" ,ret);

    return JS_NewString(ctx, (char*)pVersion);

}

static JSValue js_scan_getBackFPGAVersion(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	  int ret = 0;
	  int respond  =0;
	  int len=32 * sizeof(unsigned char);
	  unsigned char  pVersion[32]={0};

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    printf("get_back_fpgaversion start\r\n" );
    //MSG_SCAN_SUB_GET_BACK_FPGAVERSION
    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_GET_BACK_FPGAVERSION, ret, 0, 0);
    ret=RecvMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_SENT_BACK_FPGAVERSION, &respond, pVersion, &len, 1000);
    printf("get_back_fpgaversion end %d \r\n" ,ret);

    return JS_NewString(ctx, (char*)pVersion);

}


static JSValue js_scan_PageNum(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	  int ret = 0;
	  int respond  =0;
	  int len= sizeof(unsigned int);
	  unsigned int  scan_counter=0;

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    printf("scan_PageNum start\r\n" );
    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_GET_SCAN_PAGE_NUM, ret, 0, 0);
    ret=RecvMsgToMfp(MSG_MODULE_SCAN,MSG_SCAN_SUB_SENT_SCAN_PAGE_NUM, &respond, &scan_counter, &len, 1000);
    printf("scan_PageNum end %d  %d\r\n" ,ret,(int)(scan_counter));

    return JS_NewInt32(ctx, (int32_t)scan_counter);

}


static JSValue js_scan_getScanJobState(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{

     int ret = 0;
     int respond  =0;
 	 /**
	  * 作业状态通知
	  * @since V1.0
	  * @param {String} state 作业状态
	  * <ul>
	  * <li>JBSts_Init</li>
	  * <li>JBSts_PrePare</li>
	  * <li>JBSts_Ready</li>
	  * <li>JBSts_Running</li>
	  * <li>JBSts_Suspend</li>
	  * <li>JBSts_Pause</li>
	  * <li>JBSts_Cancelling</li>
	  * <li>JBSts_Aborting</li>
	  * <li>JBSts_Finish</li>
	  * <li>JBSts_History</li>
	  * </ul>
	  */
     int len= sizeof(unsigned int);
     unsigned int  scan_status=0;
     if(argc != 0)
     {
         return JS_ThrowTypeError(ctx, "No input parameters are required");
     }

     printf("getScanJobState start\r\n" );
     SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_JOB_STATUS, ret, 0, 0);
     ret=RecvMsgToMfp(MSG_MODULE_SCAN,MSG_SCAN_SUB_JOB_STATUS, &respond, &scan_status, &len, 1000);
     printf("getScanJobState end %d  %d\r\n" ,ret,(int)(scan_status));

    if(STATUS_I_SCAN_INIT == scan_status)   return JS_NewString(ctx, "JBSts_Init");
    else if(STATUS_I_SCAN_IDLE == scan_status)   return JS_NewString(ctx, "JBSts_Finish");
    else if(STATUS_I_SCAN_PROCESSING == scan_status)   return JS_NewString(ctx, "JBSts_Ready");
    else if(STATUS_I_SCAN_RUNNING == scan_status)   return JS_NewString(ctx, "JBSts_Running");
    else if(STATUS_I_SCAN_CANCELING == scan_status)   return JS_NewString(ctx, "JBSts_Cancelling");
    else if(STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT  == scan_status)   return JS_NewString(ctx, "JBSts_Aborting");
    else if(STATUS_E_SCAN_MEMORY_LOW == scan_status)   return JS_NewString(ctx, "JBSts_Pause");
    else if(STATUS_I_SCAN_NEXT_PAGE_WAITING == scan_status)   return JS_NewString(ctx, "JBSts_Suspend");


}


static JSValue js_scan_fb_PageNum(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	  int ret = 0;
	  int respond  =0;
	  int len= sizeof(unsigned int);
	  unsigned int  fb_scan_counter=0;

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    printf("fb scan_PageNum start\r\n" );
    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_GET_FB_PAGE_NUM, ret, 0, 0);
    ret=RecvMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_SENT_FB_PAGE_NUM, &respond, &fb_scan_counter, &len, 1000);
    printf("fb  scan_PageNum end %d  %d\r\n" ,ret,(int)(fb_scan_counter));

    return JS_NewInt32(ctx, (int32_t)fb_scan_counter);

}


static JSValue js_scan_adf_PageNum(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
	  int ret = 0;
	  int respond  =0;
	  int len= sizeof(unsigned int);
	  unsigned int  adf_scan_counter=0;

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }

    printf("adf scan_PageNum start\r\n" );
    SendMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_GET_ADF_PAGE_NUM, ret, 0, 0);
    ret=RecvMsgToMfp(MSG_MODULE_SCAN, MSG_SCAN_SUB_SENT_ADF_PAGE_NUM, &respond, &adf_scan_counter, &len, 1000);
    printf("adf scan_PageNum end %d  %d\r\n" ,ret,(int)(adf_scan_counter));

    return JS_NewInt32(ctx, (int32_t)adf_scan_counter);

}

 typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;


static const JSCFunctionList g_js_scan_api_funs[] = {
    {"scan_set_parameter", 4, js_pesf_scan_set_parameter},
    {"start", 2, js_pesf_scan_job_start},
    {"cancel",  0, js_pesf_scan_job_cancel},
    {"job_continue",  0, js_pesf_scan_job_continue},
    {"job_finish",  0, js_pesf_scan_job_finish},

    {"getJobId", 0, js_scan_getJobId},
    {"getJobType", 0, js_scan_getJobType},
    {"setJobType", 1, js_scan_setJobType},



    {"setResolution", 1, js_scan_setResolution},
    {"getResolution", 0, js_scan_getResolution},

    {"setFileFmtType", 1, js_scan_setFileFmtType},
    {"getFileFmtType", 0, js_scan_getFileFmtType},

    {"setColorType", 1, js_scan_setColorType},
    {"getColorType", 0, js_scan_getColorType},

    {"setPaperSize", 1, js_scan_setPaperSize},
    {"getPaperSize", 0, js_scan_getPaperSize},
    {"setXSize", 1, js_scan_setXSize},
    {"getXSize", 0, js_scan_getXSize},
    {"setYSize", 1, js_scan_setYSize},
    {"getYSize", 0, js_scan_getYSize},

    {"setAutoDuplex", 1, js_scan_setAutoDuplex},
    {"getAutoDuplex", 0, js_scan_getAutoDuplex},

    {"addParameter", 2, js_scan_addParameter},
    {"removeParameter", 1, js_scan_removeParameter},

    {"setScanSource", 1, js_scan_setScanSource},
    {"getScanSource", 0, js_scan_getScanSource},

    {"setScanMode", 1, js_scan_setScanMode},
    {"getScanMode", 0, js_scan_getScanMode},

    {"setScanQuality", 1, js_scan_setScanQuality},
    {"getScanQuality", 0, js_scan_getScanQuality},

    {"setCombination", 1, js_scan_setCombination},
    {"getCombination", 0, js_scan_getCombination},

    {"setImageColor", 2, js_scan_setImageColor},
    {"getImageColor", 1, js_scan_getImageColor},

    {"setScanToHttpPath", 3, js_setScanToHttpPath},
    {"getScanToHttpPath", 0, js_getScanToHttpPath}, 

    {"removeFtpAddr", 1, js_scan_removeFtpAddr},
    {"removeEmailAddr", 1, js_scan_removeEmailAddr},
    {"removeSMBAddr", 1, js_scan_removeSMBAddr},

    {"getfrontFPGAVersion", 0, js_scan_getFrontFPGAVersion},
    {"getbackFPGAVersion", 0, js_scan_getBackFPGAVersion},
    {"getScanHostPage", 0, js_scan_PageNum},
    {"getFBScanHostPage", 0, js_scan_fb_PageNum},
    {"getADFScanHostPage", 0, js_scan_adf_PageNum},
    {"getScanJobState", 0, js_scan_getScanJobState},
    {"setScansmb", 1, js_scan_setScansmb},
    {"setScanftp", 1, js_scan_setScanftp},
    {"setScanemail", 1, js_scan_setScanemail},
    {"setScanQuota", 1, js_scan_setScanQuota},

};


JSModuleDef* js_scan_api_init( JSContext* ctx,  JSValueConst this_obj)
{
    JSModuleDef* jsmd = NULL;

    printf( " js_scan_api_init \n" );
    scan_job_data.quota = -1;
    scan_job_data.file_type = SCAN_FILE_JPEG;
    scan_job_data.combination = 0;

    for(int i = 0; i < countof( g_js_scan_api_funs ); i++)
    {
        JS_SetPropertyStr(ctx, this_obj, g_js_scan_api_funs[i].name,JS_NewCFunction(ctx, g_js_scan_api_funs[i].func, g_js_scan_api_funs[i].name, g_js_scan_api_funs[i].length));
    }

    return jsmd;
}


#endif

