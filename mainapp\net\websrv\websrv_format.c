#include <stdio.h>
#include <regex.h>
#include <ctype.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <netinet/in.h>

static int32_t check_websrv_string_format(const char* str, const char* pattern)
{
    regex_t reg;
    int32_t ret;
    regmatch_t pmatch[1];

    regcomp(&reg, pattern, REG_EXTENDED | REG_ICASE);
    ret = regexec(&reg, str, 1, pmatch, 0);
    regfree(&reg);
    return ret;
}

static int32_t check_is_utf8_chinese(const char* str)
{
    unsigned char first_byte = str[0];
    unsigned char second_byte = str[1];
    unsigned char third_byte = str[2];

    unsigned int  unicode = 0;

    if ( 0xE0 != (first_byte & 0xF0) )
    {
        return -1;
    }

    if ( second_byte && third_byte )
    {
        if ( (first_byte >= 0xE4 && first_byte <= 0xE9) && 0x80 == (second_byte & 0xC0) && 0x80 == (third_byte & 0xC0) )
        {
            unicode = (first_byte & 0x0F) << 12 | ((second_byte & 0x3F) << 6) | (third_byte & 0x3F);
            if(unicode >= 0x4E00 && unicode <= 0x9FFF)
            {
                return 3;
            }
        }
    }
    return -1;
}

static int32_t check_is_utf8_russian(const char* str)
{
    unsigned char first_byte = str[0];
    unsigned char second_byte = str[1];
    unsigned char third_byte = str[2];

    if ( 0xD0 == (first_byte & 0xF0) || 0xD1 == (first_byte & 0xF0) )
    {
        if ( second_byte && 0x80 == (second_byte & 0xC0) )
        {
            return 2;
        }
    }
    else if ( 0xE0 == (first_byte & 0xF0) )
    {
        if ( (second_byte && 0x80 == (second_byte & 0xC0))
             && (third_byte && 0x80 == (third_byte & 0xC0)) )
        {
            return 3;
        }
    }

    return -1;
}

int32_t check_ipv4_format(const char* str)
{
    // 校验最大长度为 16
    if ( strlen(str) > 16 )
    {
        return -1;
    }

    int32_t ipv4;
    if ( 1 == inet_pton(AF_INET, str, &ipv4) )
    {
        return ntohl(ipv4);
    }

    return -1;
}

static int32_t check_rule_id_001(const char* str)
{
    int32_t len = 0;
    int32_t ret = 0;

    while(str[len] != '\0')
    {
        if ( 0 == isprint(str[len]) ) // ASCII可打印字符集
        {
            ret = -1;
            break;
        }

        if ( '<' == str[len] || '>' == str[len] || '"' == str[len] || '\\' == str[len] )
        {
            ret = -1;
            break;
        }
        len++;
    }

    return ret;
}

static int32_t check_rule_id_002(const char* str)
{
    int32_t ch = 0;
    int32_t ret = 0;
    int32_t count = 0;

    while(str[ch] != '\0')
    {
        if ( 0 == isprint(str[ch]) ) // ASCII可打印字符集
        {
            ret = -1;
            break;
        }

        if ( '<' == str[ch] || '>' == str[ch] || '"' == str[ch] || '\\' == str[ch] )
        {
            ret = -1;
            break;
        }
        ch++;
    }

    if ( ret == 0 )
    {
        ret = -1;
        for ( ch  = 0; ch < strlen(str); ch++ )
        {
            if ( 0 == isprint(str[ch]) )
            {
                count++;
            }
            else if ( 0 != isalpha(str[ch]) )
            {
                count++;
            }
            else if ( 0 != isdigit(str[ch]) )
            {
                count++;
            }

            if ( count >= 2 )
            {
                ret = 0;
                break;
            }
        }
    }

    return ret;
}

static int32_t check_rule_id_003(const char* str)
{
    int32_t ch;
    int32_t count = 0;
    char* endptr = NULL;

    if ( '-' == str[0] || '-' == str[strlen(str) - 1] )
    {
        return -1;
    }

    strtol(str, &endptr, 10);
    if ( '\0' == *endptr )  //全是数字
    {
        return -1;
    }

    for ( ch  = 0; ch < strlen(str); ch++ )
    {
        if ( 0 != isalpha(str[ch]) )
        {
            count++;
        }
        else if ( 0 != isdigit(str[ch]) )
        {
            count++;
        }
        else if ( '-' == str[ch] )
        {
            count++;
        }

        if ( 3 > count ) // 必须三种类型才可停止检查
        {
            return 0;
        }
    }

    return -1;
}

static int32_t check_rule_id_004(const char* str)
{
    int32_t ipv4;
    ipv4 = check_ipv4_format(str);
    if ( -1 == ipv4 )
    {
        return -1;
    }

    do
    {

        if ( (0xDF000000 < (ipv4 & 0xFF000000)) || (0 == (ipv4 & 0xFF000000)) ) //第一段不大于233且不等于0
        {
            break;
        }

        if ( (0x000000FF == (ipv4 & 0x000000FF)) || (0 == (ipv4 & 0x000000FF)) ) //最后一段不等于255或不等于0
        {
            break;
        }

        if ( 0x7F000001 == ipv4 || 0xFFFFFFFF == ipv4 || 0x00 == ipv4 )
        {
            break;
        }

        return 0;
    }while(0);

    return -1;
}

static int32_t check_rule_id_005(const char* str)
{
    int32_t ipv4;
    ipv4 = check_ipv4_format(str);
    if ( -1 == ipv4 )
    {
        return -1;
    }

    return 0;
}

static int32_t check_rule_id_006(const char* str)
{
    int32_t ipv4;
    ipv4 = check_ipv4_format(str);
    if ( -1 == ipv4 )
    {
        return -1;
    }

    return 0;
}

static int32_t check_rule_id_007(const char* str)
{
    int32_t ch;
    int32_t count =  0;
    for ( ch  = 0; ch < strlen(str); ch++ )
    {
        if ( 0 != isalpha(str[ch]) )
        {
            count++;
        }
        else if ( 0 != isdigit(str[ch]) )
        {
            count++;
        }
        else if ( '-' == str[ch] )
        {
            count++;
        }

        if ( 3 > count ) // 必须三种类型才可停止检查
        {
            return 0;
        }
    }

    return -1;
}

static int32_t check_rule_id_008(const char* str)
{
    int32_t ch;
    int32_t count =  0;
    for ( ch  = 0; ch < strlen(str); ch++ )
    {
        if ( 0 != isalpha(str[ch]) )
        {
            continue;
        }
        else if ( 0 != isdigit(str[ch]) )
        {
            continue;
        }
        else if ( '.' == str[ch] || '-' == str[ch] || '@' == str[ch] || '_' == str[ch] )
        {
            continue;
        }

        return  -1;
    }

    return 0;
}

static int32_t check_rule_id_009(const char* str)
{
    int32_t bytes = 0;
    const uint8_t *p = (const uint8_t *)str;
    while (*p)
    {
        if ( (*p & 0x80) == 0 )
        {
            if ( *p == '<' || *p == '>' || *p == '\\' || *p == '"' )
            {
                return -1;
            }
            p++;
        }
        else
        {
            bytes = 0;
            if ( (*p & 0xE0) == 0xC0 )
            {
                bytes = 2;
            }
            else if ( (*p & 0xF0) == 0xE0 )
            {
                bytes = 3;
            }
            else if ( (*p & 0xF8) == 0xF0 )
            {
                bytes = 4;
            }
            else
            {
                return -1;
            }

            for (size_t i = 1; i < bytes; i++)
            {
                p++;
                if (!*p || (*p & 0xC0) != 0x80)
                {
                    return -1;
                }
            }
            p++;
        }
    }

    return 0;
}


static int32_t check_rule_id_010(const char* str)
{
    if ( NULL != strchr(str, '.') )
    {
        return -1;
    }

    int32_t ret = strtol(str, NULL, 10);
    if ( -90 > ret || 90 < ret )
    {
        return -1;
    }
    return 0;
}

static int32_t check_rule_id_011(const char* str)
{
    if ( NULL != strchr(str, '.') )
    {
        return -1;
    }

    int32_t ret = strtol(str, NULL, 10);
    if ( -180 > ret || 180 < ret )
    {
        return -1;
    }
    return 0;
}

static int32_t check_rule_id_012(const char* str)
{
    return check_rule_id_001(str);
}

static int32_t check_rule_id_013(const char* str)
{
    int32_t ch = 0;
    if ( 2 != strlen(str) )
    {
        return -1;
    }

    if ( (0 == isalpha(str[0]) && 0 == isalpha(str[1]))
         || (0 == isdigit(str[0]) && 0 == isdigit(str[1])) )
    {
        return -1;
    }

    return 0;
}

static int32_t check_rule_id_014(const char* str)
{
    int32_t ch;
    for ( ch = 0; ch < strlen(str); ch++ )
    {
        if ( 0 == isdigit(str[ch]) )
        {
            return -1;
        }
    }

    return 0;
}

static int32_t check_rule_id_015(const char* str)
{
    int32_t ret = strtol(str, NULL, 10);
    if ( 1826 > ret || 99999 < ret )
    {
        return -1;
    }

    return 0;
}

static int32_t check_rule_id_016(const char* str)
{
    int32_t ch = 0;
    int32_t ret = 0;
    while(str[ch] != '\0')
    {
        if ( 0 == isprint(str[ch]) )
        {
            ret = -1;
            break;
        }
        ch++;
    }

    return ret;
}

static int32_t check_rule_id_017(const char* str)
{
    int32_t ret = 0;
    int32_t ch = 0;
    if ( ' ' == str[strlen(str) - 1] )
    {
        return -1;
    }

    while(str[ch] != '\0')
    {
        if ( 0 != isalpha(str[ch]) || 0 != isdigit(str[ch]) || ' ' == str[ch] || '-' == str[ch] )
        {
            ch++;
            continue;
        }

        ret = -1;
        break;
    }

    return ret;
}

static int32_t check_rule_id_018(const char* str)
{
    int32_t ret = strtol(str, NULL, 10);
    if ( 30 > ret || 300 < ret )
    {
        return -1;
    }

    return 0;
}

static int32_t check_rule_id_019(const char* str)
{
    int32_t ch = 0;
    int32_t ret = -1;
    ret = check_rule_id_004(str);
    if ( -1 == ret )
    {
        ret = 0;
        if ( '-' == str[0] || '-' == str[strlen(str) - 1] )
        {
            return -1;
        }

        while(str[ch] != '\0')
        {
            if ( 0 != isalpha(str[ch]) || 0 != isdigit(str[ch]) || '.' == str[ch] || '-' == str[ch] )
            {
                ch++;
                continue;
            }

            ret = -1;
            break;
        }
    }

    return ret;
}

static int32_t check_rule_id_020(const char* str)
{
    int32_t ret = 0;
    int32_t str_len = 0;

    if ( str[0] != '/' )
    {
        ret = -1;
        return ret;
    }


    while(str[str_len] != '\0')
    {
        if ( isascii(str[str_len]) == 0 )
        {
            ret = -1;
            break;
        }

        if ( '/' == str[str_len] && '/' == str[str_len + 1] )
        {
            ret = -1;
            break;
        }

        if ( str[str_len] == '\\' || str[str_len] == ':'  || str[str_len] == '*' || str[str_len] == '?'
              || str[str_len] == '"'  || str[str_len] == '<'  || str[str_len] == '>'  || str[str_len] == '|')
        {
            ret = -1;
            break;
        }
        str_len++;
    }

    return ret;
}

static int32_t check_rule_id_021(const char* str)
{
    int32_t ret = strtol(str, NULL, 10);
    if ( 1 > ret || 65535 < ret )
    {
        return -1;
    }

    return 0;
}

static int32_t check_rule_id_022(const char* str)
{
    int32_t ret = 0;
    int32_t ch=  0;
    while(str[ch] != '\0')
    {
        if ( isascii(str[ch]) == 0 )
        {
            ret = -1;
            break;
        }

        if ( '\'' == str[ch] )
        {
            ret = -1;
            break;
        }
        ch++;
    }

    return ret;
}

static int32_t check_rule_id_023(const char* str)
{
    int32_t ch = 0;
    int32_t ret = 0;
    while ( str[ch] != '\0' )
    {
        if ( 0 != isalpha(str[ch]) || 0 != isdigit(str[ch]) || '-' == str[ch] || '_' == str[ch] )
        {
            ch++;
            continue;
        }

        ret = -1;
        break;
    }

    return ret;
}

static int32_t check_rule_id_024(const char* str)
{
    int32_t ch = 0;
    int32_t ret = 0;
    if ( NULL != strchr(str, '@') )
    {
        return check_rule_id_008(str);
    }

    while ( str[ch] != '\0' )
    {
        if ( 0 == isprint(str[ch]) )
        {
            ret = -1;
            break;
        }

        if ( '<' == str[ch] || '>' == str[ch] || '"' ==  str[ch] || '\\' == str[ch] )
        {
            ret = -1;
            break;
        }
        ch++;
    }

    return ret;
}

static int32_t check_rule_id_025(const char* str)
{
    char* endptr = NULL;
    int32_t ch;

    if ( 64 == strlen(str) )
    {
        strtol(str, &endptr, 16);
        if ( '\0' != *endptr )
        {
            return -1;
        }
    }
    else
    {
        return check_rule_id_016(str);
    }

    return 0;
}

static int32_t check_rule_id_026(const char* str)
{
    int32_t ch;
    for ( ch = 0; ch < strlen(str); ch++ )
    {
        if ( 0 == isdigit(str[ch]) && '-' != str[ch] )
        {
            return -1;
        }
    }

    return 0;
}

static int32_t check_rule_id_027(const char* str)
{
    int32_t ch;
    for ( ch = 0; ch < strlen(str); ch++ )
    {
        if ( 0 == isdigit(str[ch]) && ':' != str[ch] )
        {
            return -1;
        }
    }

    return 0;
}

static int32_t check_rule_id_028(const char* str)
{
    return check_rule_id_004(str);
}

static int32_t check_rule_id_029(const char* str)
{
    int32_t ch;
    char* p, *q;
    char temp_str[512] = {0};
    if ( '-' == str[0] || '-' == str[strlen(str) - 1] )
    {
        return -1;
    }

    for ( ch = 0; ch < strlen(str); ch++ )
    {
       if ( 0 != isalpha(str[ch]) )
       {
           continue;
       }
       else if ( 0 != isdigit(str[ch]) )
       {
           continue;
       }
       else if ( '.' == str[ch] || '-' == str[ch] )
       {
           continue;
       }
       return -1;
    }

    strncpy(temp_str, str, strlen(str));

    p = strtok(temp_str, ".");
    while ( NULL != (q = strtok(NULL, ".")) )
    {
        if ( 1 > (q - p) || 63 < (q - p) )
        {
            return -1;
        }
        p  = q;
    }

    return 0;
}

static int32_t check_rule_id_030(const char* str)
{
    int32_t ch;
    for ( ch = 0; ch < strlen(str); ch++ )
    {
        if ( 0 == isprint(str[ch]) )
        {
            return -1;
        }
        else if ( '<' == str[ch] || '>' == str[ch] || '\'' == str[ch]
                  || '"' == str[ch] || '\\'  == str[ch] || '/' == str[ch] )
        {
            return -1;
        }
    }

    return 0;
}

static int32_t check_rule_id_031(const char* str)
{
    int32_t ch = 0;
    int32_t ret = 0;

    while ( str[ch] != '\0' )
    {
        if ( '<' == str[ch] || '>' == str[ch] || '\'' == str[ch] || '"' == str[ch] || '\\'  == str[ch] || '/' == str[ch] )
        {
            ret = -1;
            break;
        }

        if ( 0 == isprint(str[ch]) )
        {
            ret = -1;
            break;
        }

        ch++;
    }

    return ret;
}

static int32_t check_rule_id_032(const char* str)
{
    return check_rule_id_004(str);
}

static int32_t check_rule_id_033(const char* str)
{
    return check_rule_id_019(str);
}

static int32_t check_rule_id_034(const char* str)
{
    return check_rule_id_012(str);
}

static int32_t check_rule_id_035(const char* str)
{
    return 0;
}

static int32_t check_rule_id_036(const char* str)
{
    int32_t ch;
    for ( ch = 0; ch < strlen(str); ch++ )
    {
        if ( 0 == isdigit(str[ch]) )
        {
            return -1;
        }
    }

    return 0;
}

static int32_t check_rule_id_037(const char* str)
{
    int32_t ch;
    int32_t count=  0;
    for ( ch = 0; ch < strlen(str); ch++ )
    {
        if ( 0 != isdigit(str[ch]) )
        {
            count++;
            continue;
        }
        else if ( 0 != isalpha(str[ch]) )
        {
            count++;
            continue;
        }
        else if ( (unsigned char)str[ch] >= 127 )
        {
            count++;
        }
    }

    if ( 2 < count )
    {
        return 0;
    }

    return -1;
}

static int32_t check_rule_id_038(const char* str)
{
    return 0;
}

static int32_t check_rule_id_039(const char* str)
{
    return 0;
}

static int32_t check_rule_id_040(const char* str)
{
    int32_t ret = strtol(str, NULL, 10);
    if ( 9000 > ret || 9100 < ret )
    {
        return -1;
    }

    return 0;
}

int32_t check_hostname_format(const char *str)
{
    // 校验最大长度为 16
    if ( strlen(str) > 16 )
    {
        return -1;
    }

    // 使用正则表达式校验字符组成
    const char *pattern = "^[A-Za-z0-9-]+$";
    if ( check_websrv_string_format(str, pattern) != 0 )
    {
        return -1;
    }

    // 校验不以连字符开头或结尾
    pattern = "^[-]|[-]$";
    if ( check_websrv_string_format(str, pattern) == 0 )
    {
        return -1;
    }

    // 校验不全为数字
    const char *digit_pattern = "^[0-9]+$";
    if ( check_websrv_string_format(str, digit_pattern) == 0 )
    {
        return -1;
    }

    return 0;  // 满足所有校验条件，返回 0
}

int32_t check_domain_format(const char* str)
{
    // 校验最大长度为 64
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_029(str);
}

int32_t check_9100_format(const char* str)
{
    return check_rule_id_040(str);
}

int32_t check_snmp_community_format(const char* str)
{
    // 校验最大长度为 64
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_017(str);
}

int32_t check_snmp_user_format(const char* str)
{
    // 校验最大长度为 64
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_023(str);
}

int32_t check_snmp_pwsd_format(const char* str)
{
    int32_t flag = 0;
    if ( strlen(str) < 8 || strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_002(str);
}

int32_t check_email_format(const char* str)
{
    // 校验最大长度为 64
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_008(str);
}

int32_t check_email_server_format(const char* str)
{
    // 校验最大长度为 64
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_024(str);
}

int32_t check_port_format(const char* str)
{
    return check_rule_id_021(str);
}

int32_t check_email_sec_mode_format(const char* str)
{
    if ( strcasecmp(str,"0") != 0 && strcasecmp(str,"1") != 0 && strcasecmp(str,"2") != 0 )
    {
        return -1;
    }

    return 0; // 不匹配
}

int32_t check_email_pwsd_format(const char* str)
{
    int32_t flag = 0;
    if ( strlen(str) < 6 || strlen(str) > 20 )
    {
        return -1;
    }

    return check_rule_id_016(str);
}

int32_t check_location_format(const char* str)
{
    if ( strlen(str) > 64 )
    {
        return -1;
    }
    return check_rule_id_009(str);
}

int32_t check_lat_format(const char* str)
{
    if ( strlen(str) > 4 )
    {
        return -1;
    }

    return check_rule_id_010(str);
}
int32_t check_lot_format(const char* str)
{
    if ( strlen(str) > 5 )
    {
        return -1;
    }

    return check_rule_id_011(str);
}

int32_t check_wifi_sec_mode_format(const char* str)
{
    if ( strcasecmp(str,"0") != 0 && strcasecmp(str,"1") != 0 && strcasecmp(str,"2") != 0
        && strcasecmp(str,"3") != 0 && strcasecmp(str,"4") != 0 && strcasecmp(str,"5") != 0 )
    {
        return -1;
    }

    return 0;
}

int32_t check_wifi_ssid_format(const char* str)
{
    if ( strlen(str) > 32 )
    {
        return -1;
    }

    return check_rule_id_016(str);
}

int32_t check_wifi_pwsd_format(const char* str)
{
    size_t length = strlen(str);
    if ( length < 8 || length > 64 )
    {
        return -1;
    }

    return check_rule_id_025(str);
}

int32_t check_wfd_switch_format(const char* str)
{
    if ( strcasecmp(str,"0") != 0 && strcasecmp(str,"1") != 0 && strcasecmp(str,"2") != 0 )
    {
        return -1;
    }

    return 0;
}

int32_t check_wps_request_format(const char* str)
{
    if ( strcasecmp(str,"pbc") != 0 && strcasecmp(str,"0") != 0 && strcasecmp(str,"pin") != 0 && strcasecmp(str,"1") != 0 )
    {
        return -1;
    }

    return 0;
}

int32_t check_wfd_psk(const char* str)
{
    if ( strlen(str) < 8 || strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_016(str);
}

int32_t check_bonjour_sercer_format(const char* str)
{
    int32_t ret = -1;
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    ret = check_rule_id_012(str);
    return ret;
}

int32_t check_property_number_format(const char* str)
{
    if ( strlen(str) > 64 )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_contacts_format(const char* str)
{
    if ( strlen(str) > 32 )
    {
        return -1;
    }

    return check_rule_id_009(str);
}

int32_t check_sleep_time_format(const char* str)
{
    if ( strcasecmp(str,"0") != 0 && strcasecmp(str,"4") != 0 && strcasecmp(str,"6") != 0 && strcasecmp(str,"9") != 0
        && strcasecmp(str,"11") != 0 && strcasecmp(str,"15") != 0 )
    {
        return -1;
    }
    return 0;
}

int32_t check_io_timeout_format(const char* str)
{
    if ( strlen(str) > 3 )
    {
        return -1;
    }

    int num = atoi(str);
    if ( num >= 30 && num <= 300 )
    {
        return 0; // 匹配
    }

    return -1; // 不匹配
}

int32_t check_system_date_format(const char* str)
{
    if ( strlen(str) > 11 )
    {
        return -1;
    }

    return check_rule_id_026(str);
}

int32_t check_system_time_format(const char* str)
{
    if ( strlen(str) > 9 )
    {
        return -1;
    }

    return check_rule_id_027(str);
}

int32_t check_color_pswd_format(const char* str)
{
    if ( strlen(str) > 8 )
    {
        return -1;
    }

    // 正则表达式：只允许数字
    const char* pattern = "^[0-9]+$";
    if ( check_websrv_string_format(str, pattern) == 0 )
    {
        return 0; // 匹配
    }

    return -1; // 不匹配
}

int32_t check_switch_format(const char* str)
{
    if ( strcasecmp(str,"0") != 0 && strcasecmp(str,"1") != 0 )
    {
        return -1;
    }

    return 0;
}

int32_t check_airprint_user_format(const char* str)
{
    if ( 63 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_airprint_pswd_format(const char* str)
{
    if ( 20 < strlen(str) || 6 > strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_mail_usergroup_format(const char* str)
{
    if ( 15 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_ftp_server_name_format(const char* str)
{
    if ( 128 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_ftp_server_addr_format(const char* str)
{
    int32_t ipv4;

    if ( 31 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_019(str);
}

int32_t check_ftp_path_format(const char* str)
{
    if ( 63 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_020(str);
}

int32_t check_ftp_port_format(const char* str)
{
    return check_port_format(str);
}

int32_t check_ftp_username_format(const char* str)
{
    if ( 62 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_ftp_password_format(const char* str)
{
    if ( 30 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_smb_server_name_format(const char* str)
{
    if ( 128 < strlen(str) )
    {
        return -1;
    }

    int32_t len = 0;
    int32_t ret = 0;

    while(str[len] != '\0')
    {
        if ( 0 == isprint(str[len]) ) // ASCII可打印字符集
        {
            ret = check_is_utf8_russian(str + len);
            if ( ret == -1 )
            {
                return -1;
            }
            len += ret;
            continue;
        }

        if ( '<' == str[len] || '>' == str[len] || '"' == str[len] || '\\' == str[len] )
        {
            return -1;
        }
        len++;
    }

    return 0;
}


int32_t check_smb_server_addr_format(const char* str)
{
    uint32_t ipv4;

    if ( 63 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_019(str);
}

int32_t check_smb_path_format(const char* str)
{
    if ( 128 < strlen(str) )
    {
        return -1;
    }

    int32_t ret = -1;
    int32_t ch_len = 0;
    int32_t str_len = 0;
    const char ch[9] = {'\\', ':', '*', '?', '"', '<', '>', '|', '\0'};
    while(str[str_len] != '\0')
    {
        if ( !(str[str_len] >= 0 && str[str_len] <= 127) )
        {
            ret = check_is_utf8_russian(str + str_len);
            if ( ret == -1 )
            {
                return ret;
            }
            str_len += ret;
            continue;
        }

        if ( '/' == str[str_len] && '/' == str[str_len + 1] )
        {
            return -1;
        }

        while ( '\0' != ch[ch_len] )
        {
            if ( ch[ch_len] == str[str_len] )
            {
                return -1;
            }
            ch_len++;
        }
        str_len++;
    }

    return 0;
}


int32_t check_smb_port_format(const char* str)
{
    if ( 5 < strlen(str) )
    {
        return -1;
    }

    return check_port_format(str);
}

int32_t check_smb_username_format(const char* str)
{
    if ( 128 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_smb_password_format(const char* str)
{
    if ( 32 < strlen(str) )
    {
        return -1;
    }

    return check_rule_id_012(str);
}

int32_t check_water_mark_format(const char* str)
{
    int32_t bytes = 0;
    const uint8_t *p = (const uint8_t *)str;
    while (*p)
    {
        if ( (*p & 0x80) == 0 )
        {
#if !CONFIG_NET_SAFETY_MACHINE
            if ( *p == ':' || *p == '\\')
            {
                return -1;
            }
#else
            if (*p == '\\' || *p == ';' || *p == '\'' || *p == '<' || *p == '>' || *p == '"')
            {
                return -1;
            }
#endif
            p++;
        }
        else
        {
            bytes = 0;
            if ( (*p & 0xE0) == 0xC0 )
            {
                bytes = 2;
            }
            else if ( (*p & 0xF0) == 0xE0 )
            {
                bytes = 3;
            }
            else if ( (*p & 0xF8) == 0xF0 )
            {
                bytes = 4;
            }
            else
            {
                return -1;
            }

            for (size_t i = 1; i < bytes; i++)
            {
                p++;
                if (!*p || (*p & 0xC0) != 0x80)
                {
                    return -1;
                }
            }
            p++;
        }
    }

    return 0;

}


