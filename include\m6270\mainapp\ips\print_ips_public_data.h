/**
 * @copyright 2023 Shenzhen Pantum Technology Co.Ltd all rights reserved
 * @file ips_public.h
 * @addtogroup print_image_module
 * @{
 * @addtogroup print_ips_module
 * <AUTHOR>
 * @date 2023-10-23
 * @version v0.1
 * @brief
 */

#ifndef _PRINT_IPS_PUBLIC_DATA_H_
#define _PRINT_IPS_PUBLIC_DATA_H_

/*** Head file area ***************************************************/
#include "utilities/msgrouter.h"
#include "msgrouter_main.h"
/**********************************************************************/

/*** Macro description area *******************************************/
#define IPS_UNKNOWN         (-1)
#define IPS_ERRORCODE_OK    0x00
#define ERRBIT              0x20
#define TYPECHEK            (ERRBIT|1)
#define INVALACCESS         (ERRBIT|2)
#define NOCURPOINT          (ERRBIT|3)
#define UNREG               (ERRBIT|4)
#define INVALIDID           (ERRBIT|5)
#define INVALIDCONTEXT      (ERRBIT|6)
#define INVALFILACCESS      (ERRBIT|7)
#define INVALFONT           (ERRBIT|8)
#define IOERR               (ERRBIT|9)
#define STAKOVFL            (ERRBIT|10)
#define IPSTIMEOUT          (ERRBIT|11)
#define RANGECHEK           (ERRBIT|12)
#define UNDEFRESULT         (ERRBIT|13)
#define STAKUNDFL           (ERRBIT|14)
#define SYNTAXERR           (ERRBIT|15)
#define UNDEFFILNAME        (ERRBIT|16)
#define UNDEF               (ERRBIT|17)
#define LIMCHEK             (ERRBIT|18)
#define UNMATCHMARK         (ERRBIT|19)
#define EXECSTAKOVFL        (ERRBIT|20)
#define VMERR               (ERRBIT|21)
#define INVALEXIT           (ERRBIT|22)
#define INTRPT              (ERRBIT|23)
#define DICTSTAKOVFL        (ERRBIT|24)
#define INVALRESTORE        (ERRBIT|25)
#define DICTFULL            (ERRBIT|26)
#define DICTSTAKUNDFL       (ERRBIT|27)
#define CONFIGURATIONERROR  (ERRBIT|28)
#define UNDEFRESOURCE       (ERRBIT|29)
#define RFA_EXIT            (ERRBIT|30)
#define MEMORYFULL          (ERRBIT|31)

#define IPS_DO_ENABLE               1
#define IPS_TYPE_UNKNOWN            0
#define IPS_TYPE_TXT_DATA           1
#define IPS_TYPE_PJL_PS_JOB         2
#define IPS_TYPE_PJL_XL_JOB         3
#define IPS_TYPE_PJL_PCL_JOB        4
#define IPS_TYPE_PS_JOB             5
#define IPS_TYPE_PDF_JOB            6
#define IPS_TYPE_JPEG_JOB           7
#if IPS_DO_ENABLE
#define IPS_TYPE_DO_JOB             8
#endif
#define IPS_TYPE_DIRECTIMAGE_JOB    9
#define IPS_TYPE_PJL_PDL_JOB        10
#if IN_ISOLATOR
#define IPS_TYPE_SECRET_ENVIRONMENT -1
#endif


/**********************************************************************/


/*** Enumerate description area ***************************************/
/*
    options manager - property manager bridge
*/
typedef enum {
    OM_SUCCESS,
    OM_BAD_OPTION,
    OM_BUFFER_TOO_SMALL,
} OM_ERROR_E;

/*
 * The OM enum is 0 base
 * The postscript option is not included/defined for this port.
 * It is the customer's responsibility to map these enum
 * The last entry must be IPSINF_OMMAX.
 */
typedef enum {
   IPSINF_OMPAPERSIZE     = 0,                                     ///< system
   IPSINF_OMPAPERTYPE,
   IPSINF_OMNCOPIES,
   IPSINF_OMMANUALFEED,                                            ///< intray select
   IPSINF_OMDUPLEX,

   IPSINF_OMORIENT,                                                ///< PCL
   IPSINF_OMFONTPITCH,
   IPSINF_OMFONTSIZE,
   IPSINF_OMFONTNUM,
   IPSINF_OMSYMSET,
   IPSINF_OMVMI,
   IPSINF_OMTOPMARGIN,                                             ///< top margin.     unit: 1/7200 inch
   IPSINF_OMBOTTOMMARGIN,
   IPSINF_OMLEFTMARGIN,                                            ///< left margin.    unit: 1/10 inch
   IPSINF_OMRMARGIN,                                               ///< right margin.   unit: 1/10 inch
   IPSINF_OMXOFFSET,                                               ///< X offset
   IPSINF_OMYOFFSET,                                               ///< Y offset
   IPSINF_OMWIDEA4,                                                ///< wide A4 enable
                                                                   ///< PS - TBD
   IPSINF_DENSITY,                                                 ///< pantum
   PS_ERROR_ENABLE,
   IPSINF_OMBIND,                                                  ///< longedge / shortedge select
   IPSINF_OMRESOLUTION,                                            ///< resolution: 0 - 600 / 1 - 1200
   IPSINF_OMINPUTTRAY,                                             ///< paper source: set enum TRAY_INPUT


   //  IPSINF_OMPDFPRINTPAGES,
                                                                   ///< A string means,which pages of the pdf will be print out.
                                                                   ///< For example: "3,6,11-13,19-" has meaning of pages 3,6,
                                                                   ///< pages in range 11 to 13, and all pages from 19 until the end of the
                                                                   ///< document.

   //   IPSINF_OMPDFNUMCOPIES,                                     ///< Number of copies. PDF ONLY.

    IPSINF_OMMAX                                                   ///< number of options, must be the last entry
} IPSINF_OM_ENTITIES_E;


//#ifdef  SDRAGON
typedef enum {
    IPS_CANCEL_OFF = 0,
    IPS_CANCEL_ON_NORMAL,
    IPS_CANCEL_ON_IPP
} IPS_CANCEL_FLAG_E;

//双面模式
typedef enum
{
    ePRINT_PRINT_MODE_SINGLE = 0,                                      ///< 单面打印(缺省)
    ePRINT_PRINT_MODE_DUPLEX_LONG = 1,                                 ///< 双面长边打印
    ePRINT_PRINT_MODE_DUPLEX_SHORT = 2,                                ///< 双面短边
    ePRINT_PRINT_MODE_MAX,
}IPS_PRINT_PRINT_MODE;

//逐份模式
typedef enum
{
    ePRINT_COLLATE_MODE_OFF = 0,                                      ///< 关(缺省)
    ePRINT_COLLATE_MODE_ON = 1,                                       ///< 开
    ePRINT_COLLATE_MODE_MAX,
}IPS_PRINT_COLLATE_MODE;


//颜色模式
typedef enum
{
    ePRINT_COLOR_MODE_COLOR = 0,                                        ///< 彩色(缺省)
    ePRINT_COLOR_MODE_MONO = 1,                                         ///< 黑白
    ePRINT_COLOR_MODE_MAX,
}IPS_PRINT_COLOR_MODE;


///< 多合一打印
typedef enum
{
    ePRINT_ALL_IN_ONE_PRINT_OFF = 0,                                     ///< 关闭(缺省)
    ePRINT_TWO_IN_ONE_PRINT = 1,                                         ///< 2合1
    ePRINT_FOUR_IN_ONE_PRINT= 2,                                         ///< 4合1
    ePRINT_EIGHT_IN_ONE_PRINT= 3,                                        ///< 8合1
    ePRINT_SIXTEEN_IN_ONE_PRINT= 4,                                      ///< 16合1
    eRPINT_ALL_IN_ON_PRINT_MAX,
}IPS_PRINT_ALL_IN_ONE_PRINT;

///<
typedef enum {
    IPS_PRINT_NUP_ORDER_DOWNLEFT = 0,                                       // 向下再向左
    IPS_PRINT_NUP_ORDER_DOWNRIGHT = 1,                                      // 向下再向右
    IPS_PRINT_NUP_ORDER_LEFTDOWN = 2,                                       // 向左再向下
    IPS_PRINT_NUP_ORDER_RIGHTDOWN = 3,                                      // 向右再向下、顺序
    IPS_PRINT_NUP_ORDER_LEFTUP = 4,                                         // 向左再向上、逆序
    IPS_PRINT_NUP_ORDER_MAX,
}IPS_PRINT_NUP_ORDER;

//文档类型
typedef enum
{
    ePRINT_DOCUMENT_TYPE_DOC = 1,                                        ///< 文档
    ePRINT_DOCUMENT_TYPE_PHOTO = 2,                                      ///< 照片(网页)
    ePRINT_DOCUMENT_TYPE_MIX = 3,                                        ///< 混合 (说明书/文图) 缺省
    ePRINT_DOCUMENT_TYPE_MAX,
}IPS_PRINT_DOCUMENT_TYPE;

//图像旋转方向
typedef enum
{
    ePRINT_IMAGE_ROTATE_DIR_PORTRATI = 0,                               ///< 纵向
    ePRINT_IMAGE_ROTATE_DIR_LANDSCAPE = 1,                              ///< 横向
    ePRINT_IMAGE_ROTATE_DIR_AUTOSELECT = 2,                             ///< 自动选择 缺省
    ePRINT_IMAGE_ROTATE_DIR_MAX,
}IPS_PRINT_IMAGE_ROTATE_DIR;


//打孔模式
typedef enum
{
    ePRINT_PUNCH_MODE_OFF = 0,                                          ///< 关闭 缺省
    ePRINT_PUNCH_MODE_FOUR = 4,                                         ///< 4孔
    ePRINT_PUNCH_MODE_TWO = 8,                                          ///< 2孔
    ePRINT_PUNCH_MODE_MAX,
}IPS_PRINT_PUNCH_MODE;


/**
 * @brief print machine type enum
 */
typedef enum
{
    IPS_TYPE_KANAS = 0,
    IPS_TYPE_BAIKAL,
}IPS_MACHINE_TYPE_E;

typedef enum {
  eJOB_TYPE_OTHER = 0,
  eJOB_TYPE_UDISCK = 1,
  eJOB_TYPE_AIRPRINT = 2,
  eJOB_TYPE_PROOFPRINT = 3,
}IPS_JOB_TYPE_E;

typedef enum
{
    CORRESPONDING_PAPER     = 0,
    CORRESPONDING_LONG_SHOUT_PAPER,
}CORRESPONDING_PAPER_SIZE_E;

typedef enum
{
    LONG_PAPER_SIZE     = 0,
    SHORT_PAPER_SIZE,
    NOT_SET_PAPER_SIZE,
}PAPER_SIZE_TYPE_E;

typedef enum
{
    FINISHER_STAPLE_TOP_OR_DOWN     = 0,
    FINISHER_STAPLE_RIGHT_OR_LEFT,
    FINISHER_STAPLE_ZERO_BIND,
    NOT_SET_FNISHER_STAPLE_TYPE,
}STAPLE_TYPE_E;

typedef enum
{
    FINISHER_PUNCH_TOP_OR_DOWN     = 0,
    FINISHER_PUNCH_RIGHT_OR_LEFT,
    NOT_SET_FNISHER_PUNCH_TYPE,
}PUNCH_TYPE_E;

/*
   brief: the image direct
 */
typedef enum {
    ORIENTATION_PORTRAIT,               /**< Portrait */
    ORIENTATION_LANDSCAPSE,             /**< Landscape */
    ORIENTATION_REVERSE_PORTRATI,       /**< Reverse Portrait */
    ORIENTATION_REVERSER_LANDSCAPSE,    /**< Reverse Landscape */
} FINISHER_IPS_ORIENTATION_E ;

//分隔页
typedef enum
{
    eFINISHER_INSTALL_NO = 0,                                               ///< 装订器未安装
    eFINISHER_INSTALL_YES = 1,                                              ///< 装订器安装
    eFINISHER_INSTALL_MAX,
}IPS_FINISHER_INSTALL_E;

typedef enum
{
    eHARD_UDISK_INSTALL_NO = 0,                                               ///< hard disk uninstall
    eHARD_UDISK_INSTALL_YES = 1,                                              ///< hard disk install
    eHARD_UDISK_INSTALL_MAX,
}IPS_HARD_UDISK_INSTALL_E;

typedef enum {
    eJOB_CANCEL_TYPE_DATA = 0,
    eJOB_CANCEL_TYPE_CTL = 1,
    eJOB_CANCEL_TYPE_INVALID,
    eJOB_CANCEL_TYPE_MAX,
}IPS_CANCEL_TYPE_E;

typedef enum
{
    IPS_CUTOM_TYPE_LEVEL_1 = 0X1,                                   ///< level 1
    IPS_CUTOM_TYPE_LEVEL_2,                                         ///< level 2
    IPS_CUTOM_TYPE_LEVEL_3,                                         ///< level 3
    IPS_CUTOM_TYPE_LEVEL_4,                                         ///< level 2
    IPS_CUTOM_TYPE_LEVEL_5,                                         ///< level 3
    IPS_CUTOM_TYPE_LEVEL_INVALID
}IPS_CUTOM_TYPE_LEVEL_E;

typedef struct
{
    uint32_t                        paper_size_custom1_heigt_min;
    uint32_t                        paper_size_custom1_heigt_max;
    uint32_t                        paper_size_custom1_width_min;
    uint32_t                        paper_size_custom1_width_max;

    uint32_t                        paper_size_custom2_heigt_min;
    uint32_t                        paper_size_custom2_heigt_max;
    uint32_t                        paper_size_custom2_width_min;
    uint32_t                        paper_size_custom2_width_max;

    uint32_t                        paper_size_custom3_heigt_min;
    uint32_t                        paper_size_custom3_heigt_max;
    uint32_t                        paper_size_custom3_width_min;
    uint32_t                        paper_size_custom3_width_max;

    uint32_t                        paper_size_custom4_heigt_min;
    uint32_t                        paper_size_custom4_heigt_max;
    uint32_t                        paper_size_custom4_width_min;
    uint32_t                        paper_size_custom4_width_max;

    uint32_t                        paper_size_custom5_heigt_min;
    uint32_t                        paper_size_custom5_heigt_max;
    uint32_t                        paper_size_custom5_width_min;
    uint32_t                        paper_size_custom5_width_max;

    IPS_CUTOM_TYPE_LEVEL_E          paper_size_custom1_leve;
    IPS_CUTOM_TYPE_LEVEL_E          paper_size_custom2_leve;
    IPS_CUTOM_TYPE_LEVEL_E          paper_size_custom3_leve;
    IPS_CUTOM_TYPE_LEVEL_E          paper_size_custom4_leve;
    IPS_CUTOM_TYPE_LEVEL_E          paper_size_custom5_leve;

    uint32_t                        paper_size_custom_init_num;
}IPS_CUSTOM_TYPE_TABLE_S;

/**********************************************************************/

/*** Function description area ****************************************/

/**********************************************************************/

#endif
/**
 *@ }
*/
