/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file msgrouter.h
 * @addtogroup msgrouter
 * @{
 * <AUTHOR>
 * @date 2023-05-21
 * @version v1.0
 * @brief  public msg header file
 */
#ifndef __MSG_ROUTER_H__
#define __MSG_ROUTER_H__

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_threads.h"

PT_BEGIN_DECLS

typedef struct router_msg
{
    uint32_t    msgType;    ///< action type ( MSG_TYPE_E defined in applicantion )
    uint32_t    msg1;       ///< action sub-type
    uint32_t    msg2;       ///< who want to do this action,user self
    void*       msg3;       ///< action context or data
    uint32_t    msgSender;  ///< msg sender's mid ( MODULE_ID_E defined in applicantion )
}
ROUTER_MSG_S;

#define ARGC_TAB(arg1, arg2, arg3, n, ...)          n
#define ARGS_NUM(...)   ARGC_TAB(__VA_ARGS__, 3, 2, 1)
/**
 * @brief  msgrouter module initialize
 * @param[in] module_num : the total numbers of module
 * @return  init result
 * @retval = 0 : success\n
 *          -1 : fail
 * <AUTHOR> Xin
 * @date   2023-4-18
*/
int32_t msg_router_prolog                   (uint32_t module_num);

/**
 * @brief  msgrouter module deinitialize
 * <AUTHOR> Xin
 * @date   2023-4-18
*/
void    msg_router_epilog                   (void);

/**
 * @brief   Create a mailbox and register into the msg router for this module(module_id).\n
 *          This function can be seen as msg_router_register(uint32_t module_id, int32_t msg_count = 256);
 * @param[in] module_id : the identification number of this module(define in the applicantion).
 * @param[in] msg_count : The number of messages that can be cached in this mailbox, It is an optional parameter with a default value of 256.
 * @return  register result
 * @retval = 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
#define msg_router_register(...)            msg_router_register_custom(ARGS_NUM(__VA_ARGS__), __VA_ARGS__)

/**
 * @brief   Create a mailbox and register into the msg router for this module(module_id).
 * @param[in] n : the number of function args
 * @param[in] module_id : the identification number of this module(define in the applicantion).
 * @param[in] msg_count : The number of messages that can be cached in this mailbox, It is an optional parameter with a default value of 256
 * @return  register result
 * @retval = 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t msg_router_register_custom          (int32_t n, uint32_t module_id, ...);


/**
 * @brief  unregister the mailbox handle by module id
 * @param[in] module_id : the identification number of module
 * <AUTHOR> Xin
 * @date   2023-4-18
*/
void    msg_router_unregister               (uint32_t module_id);

/**
 * @brief   send a msg by router
 * @param[in] module_id : the identification number of module
 * @param[in] rtmsg : The  messages need to send
 * @return send result
 * @retval = 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_send_by_router             (uint32_t module_id, ROUTER_MSG_S* rtmsg);

/**
 * @brief   send a msg by router forever until send successfully
 * @param[in] module_id : the identification number of module
 * @param[in] rtmsg : The  messages need to send
 * @return send result
 * @retval = 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_send_forever_by_router     (uint32_t module_id, ROUTER_MSG_S* rtmsg);

/**
 * @brief wait a msg forever by router
 * @param[in] module_id : the identification number of module
 * @param[out] rtmsg : The  messages to wait recieve
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_wait_forever_by_router     (uint32_t module_id, ROUTER_MSG_S* rtmsg);

/**
 * @brief  recv a msg by router with timeout
 * @param[in] module_id : the identification number of module
 * @param[in] wait_secs : wait seconds number
 * @param[in] wait_usecs : wait useconds number
 * @param[out] rtmsg : The  messages to wait recieve
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_wait_timeout_by_router     (uint32_t module_id, ROUTER_MSG_S* rtmsg, int32_t wait_secs, int32_t wait_usecs);

/**
 * @brief  recv a msg by router with timeout
 * @param[in] module_id : the identification number of module
 * @param[out] rtmsg : The  messages to wait recieve
 * @param[in] tv : timeout to specific
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_timeout_by_router          (uint32_t module_id, ROUTER_MSG_S* rtmsg, TIMEVAL_S* tv);

/**
 * @brief   get moudule msg count of by the module_id
 * @param[in] module_id : the identification number of module
 * @return count
 * @retval >= 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_tryto_get_msg_count        (uint32_t module_id);

/**
 * @brief   get moudule msg pool size of by the module_id
 * @param[in] module_id : the identification number of module
 * @return pool size
 * @retval >= 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_tryto_get_msg_size         (uint32_t module_id);

/**
 * @brief  recv a msg quickly by router
 * @param[in] module_id : the identification number of module
 * @param[out] rtmsg : The  messages to recieve
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_tryto_get_by_router        (uint32_t module_id, ROUTER_MSG_S* rtmsg);

/**
 * @brief   send a msg directly
 * @param[in] mailbox : the mailbox pointer of send to
 * @param[in] rtmsg : The  messages need to send
 * @return send result
 * @retval = 0 : success\n
 *         < 0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_send                       (PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg);

/**
 * @brief  recv a msg directly with timeout
 * @param[in] mailbox : the mailbox pointer of recieve
 * @param[out] rtmsg : The  messages to wait recieve
 * @param[in] tv : timeout to specific
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_timeout                    (PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg, TIMEVAL_S* tv);


/**
 * @brief wait a msg directly forever
 * @param[in] mailbox : the mailbox pointer of recieve
 * @param[out] rtmsg : The  messages to wait recieve
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_wait_forever               (PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg);

/**
 * @brief  recv a msg quickly directly
 * @param[in] module_id : the identification number of module
 * @param[out] rtmsg : The  messages to recieve
 * @return recv result
 * @retval = 0 : success\n
 *         !0 : fail
 * <AUTHOR> Xin
 * @date   2023-5-24
 */
int32_t task_msg_tryto_get                  (PI_MAILBOX_T mailbox, ROUTER_MSG_S* rtmsg);

PT_END_DECLS

#endif /* __MSG_ROUTER_H__ */
/**
 *@}
 */
