/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pedk_addressbook.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2024-6-6
 * @brief Addressbook function for pedk
 */
#include <stdlib.h>
#include "nettypes.h"
#include "pedk_addressbook.h"
#include <sys/file.h>



#define MAIL_ADDRESSBOOK    "/tmp/addressbook/mail.dat"
#define GROUP_ADDRESSBOOK   "/tmp/addressbook/group.dat"
#define FTP_ADDRESSBOOK     "/tmp/addressbook/ftp.dat"
#define SMB_ADDRESSBOOK     "/tmp/addressbook/smb.dat"

#define int32_t_MAX 2147483647

typedef enum
{
    ADDRESSBOOK_TYPE_MAIL   = 0,
    ADDRESSBOOK_TYPE_GROUP  = 1,
    ADDRESSBOOK_TYPE_FTP    = 2,
    ADDRESSBOOK_TYPE_SMB    = 3,
}
ADDRESSBOOK_TYPE_E;

static int32_t save_addressbook(char* file_name, ADDRESSBOOK_TYPE_E save_type, void* addressbook)
{
    int32_t     ret = -1;
    int32_t     fd = -1;
    FILE*       file = NULL;
    ADDRESSBOOK_MAIL* mail_book = NULL;
    ADDRESSBOOK_GROUP* group_book = NULL;
    ADDRESSBOOK_FTP* ftp_book = NULL;
    ADDRESSBOOK_SMB* smb_book = NULL;

    do
    {
        BREAK_IF((fd = pi_open(file_name, O_RDWR)) == -1, NET_WARN);
        BREAK_IF(flock(fd, LOCK_EX) == -1, NET_WARN);
        BREAK_IF((file = pi_fopen(file_name, "wb")) == NULL, NET_WARN);

        switch(save_type)
        {
            case ADDRESSBOOK_TYPE_MAIL:
                mail_book = (ADDRESSBOOK_MAIL*)addressbook;
                BREAK_IF(pi_fwrite(mail_book, 1, sizeof(ADDRESSBOOK_MAIL), file) != sizeof(ADDRESSBOOK_MAIL), NET_WARN);
                break;

            case ADDRESSBOOK_TYPE_GROUP:
                group_book = (ADDRESSBOOK_GROUP*)addressbook;
                BREAK_IF(pi_fwrite(group_book, 1, sizeof(ADDRESSBOOK_GROUP), file) != sizeof(ADDRESSBOOK_GROUP), NET_WARN);
                break;

            case ADDRESSBOOK_TYPE_FTP:
                ftp_book = (ADDRESSBOOK_FTP*)addressbook;
                BREAK_IF(pi_fwrite(ftp_book, 1, sizeof(ADDRESSBOOK_FTP), file) != sizeof(ADDRESSBOOK_FTP), NET_WARN);
                break;

            case ADDRESSBOOK_TYPE_SMB:
                smb_book = (ADDRESSBOOK_SMB*)addressbook;
                BREAK_IF(pi_fwrite(smb_book, 1, sizeof(ADDRESSBOOK_SMB), file) != sizeof(ADDRESSBOOK_SMB), NET_WARN);
                break;

            default:
                ret = -1;
                break;
        }
        ret = 0;
    }
    while(0);
    //此操作关闭FILE*流的同时也关闭了fd和释放了fd上的锁
    if ( file != NULL )
    {
        pi_fclose(file);
    }
    if ( fd != -1)
    {
        flock(fd, LOCK_UN);
        pi_close(fd);
    }

    return ret;
}

static int32_t load_addressbook(char* file_name, ADDRESSBOOK_TYPE_E save_type, void* addressbook)
{
    FILE*       file = NULL;
    int32_t     fd = -1;
    int32_t     ret = -1;
    int32_t     nread = 0;
    ADDRESSBOOK_MAIL* mail_book = NULL;
    ADDRESSBOOK_GROUP* group_book = NULL;
    ADDRESSBOOK_FTP* ftp_book = NULL;
    ADDRESSBOOK_SMB* smb_book = NULL;

    do
    {
        if ( access("/tmp/addressbook", F_OK) != 0 )
        {
           BREAK_IF(mkdir("/tmp/addressbook", 0755) != 0, NET_WARN);
        }
        BREAK_IF((fd = pi_open(file_name, O_RDWR|O_CREAT)) == -1, NET_WARN);
        BREAK_IF(flock(fd, LOCK_EX) == -1, NET_WARN);
        BREAK_IF((file = pi_fopen(file_name, "rb")) == NULL, NET_WARN);

        switch(save_type)
        {
            case ADDRESSBOOK_TYPE_MAIL:
                mail_book = (ADDRESSBOOK_MAIL*)addressbook;
                pi_memset(mail_book, 0, sizeof(ADDRESSBOOK_MAIL));
                BREAK_IF(((nread = pi_fread(mail_book, 1, sizeof(ADDRESSBOOK_MAIL), file)) != sizeof(ADDRESSBOOK_MAIL) && nread > 0), NET_WARN);
                break;

            case ADDRESSBOOK_TYPE_GROUP:
                group_book = (ADDRESSBOOK_GROUP*)addressbook;
                pi_memset(group_book, 0, sizeof(ADDRESSBOOK_GROUP));
                BREAK_IF(((nread = pi_fread(group_book, 1, sizeof(ADDRESSBOOK_GROUP), file)) != sizeof(ADDRESSBOOK_GROUP) && nread > 0), NET_WARN);
                break;

            case ADDRESSBOOK_TYPE_FTP:
                ftp_book = (ADDRESSBOOK_FTP*)addressbook;
                pi_memset(ftp_book, 0, sizeof(ADDRESSBOOK_FTP));
                BREAK_IF(((nread = pi_fread(ftp_book, 1, sizeof(ADDRESSBOOK_FTP), file)) != sizeof(ADDRESSBOOK_FTP) && nread > 0), NET_WARN);
                break;

            case ADDRESSBOOK_TYPE_SMB:
                smb_book = (ADDRESSBOOK_SMB*)addressbook;
                pi_memset(smb_book, 0, sizeof(ADDRESSBOOK_SMB));
                BREAK_IF(((nread = pi_fread(smb_book, 1, sizeof(ADDRESSBOOK_SMB), file)) != sizeof(ADDRESSBOOK_SMB) && nread > 0), NET_WARN);
                break;

            default:
                ret = -1;
                break;
        }
        ret = 0;
    }
    while(0);

    //此操作关闭FILE*流的同时也关闭了fd和释放了fd上的锁
    if ( file != NULL )
    {
        pi_fclose(file);
    }
    if ( fd != -1)
    {
        flock(fd, LOCK_UN);
        pi_close(fd);
    }

    return ret;
}

//实现降序排序
static int32_t compareints(const void* a, const void* b)
{
    int32_t* ia = (int32_t *)a;
    int32_t* ib = (int32_t *)b;

    return *ib - *ia;
}


int32_t add_email_addr(PEDK_MAIL_PARM* mail_info)
{
    ADDRESSBOOK_MAIL mail_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(mail_book.total >= PEDK_MAIL_ADDR_NUM_MAX, NET_WARN, -1);

    pi_snprintf(mail_book.mail_parm[mail_book.total].pedk_mail_name, PEDK_MAIL_NAME_LEN, "%s", mail_info->pedk_mail_name);
    pi_snprintf(mail_book.mail_parm[mail_book.total].pedk_mail_addr, PEDK_MAIL_ADDR_LEN, "%s", mail_info->pedk_mail_addr);
    pi_memset(mail_book.mail_parm[mail_book.total].pedk_group_num, 0, sizeof(mail_book.mail_parm[mail_book.total].pedk_group_num));
    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        mail_book.mail_parm[mail_book.total].pedk_group_num[i] = mail_info->pedk_group_num[i];
    }
    mail_book.mail_parm[mail_book.total].pedk_mail_index = (++mail_book.id == int32_t_MAX) ? 0 : mail_book.id;
    qsort(mail_book.mail_parm[mail_book.total].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);

    mail_book.total++;
    if ( mail_book.total >= PEDK_MAIL_ADDR_NUM_MAX )
    {
        mail_book.total = PEDK_MAIL_ADDR_NUM_MAX;
    }

    RETURN_VAL_IF(save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    NET_DEBUG("save email addressbook success %d ", mail_book.id);

    return mail_book.id;
}

int32_t get_email_addr(int32_t mail_index, PEDK_MAIL_PARM* mail_info, size_t* mail_size)
{
    ADDRESSBOOK_MAIL mail_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(mail_book.total == 0, NET_WARN, -1);

    for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        if ( mail_book.mail_parm[i].pedk_mail_index == mail_index )
        {
            pi_memcpy(mail_info, &mail_book.mail_parm[i], sizeof(PEDK_MAIL_PARM));
            *mail_size = sizeof(PEDK_MAIL_PARM);
            return 0;
        }
    }

    return -1;
}

int32_t get_email_addr_num(void)
{
    ADDRESSBOOK_MAIL mail_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);

    return mail_book.total;
}

int32_t is_email_addr_full(void)
{
    RETURN_VAL_IF(get_email_addr_num() == PEDK_MAIL_ADDR_NUM_MAX, NET_DEBUG, 1);

    return 0;
}

int32_t modify_email_addr(PEDK_MAIL_PARM* mail_info)
{
    ADDRESSBOOK_MAIL    mail_book;
    ADDRESSBOOK_GROUP   group_book;
    int32_t             mail_index = 0;
    int32_t             i,j,k;
    int32_t             ret = 0;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);

    for ( int32_t n = 0; n < PEDK_MAIL_ADDR_NUM_MAX; n++ )
    {
        if ( mail_book.mail_parm[n].pedk_mail_index == mail_info->pedk_mail_index )
        {
            mail_index = n;
            break;
        }
    }

    mail_book.mail_parm[mail_index].pedk_mail_index = mail_info->pedk_mail_index;
    pi_snprintf(mail_book.mail_parm[mail_index].pedk_mail_name, PEDK_MAIL_NAME_LEN, "%s", mail_info->pedk_mail_name);
    pi_snprintf(mail_book.mail_parm[mail_index].pedk_mail_addr, PEDK_MAIL_ADDR_LEN, "%s", mail_info->pedk_mail_addr);

    for ( int32_t m = 0; m < PEDK_MAIL_GROUP_MAX; m++ )
    {
        mail_book.mail_parm[mail_index].pedk_group_num[m] = mail_info->pedk_group_num[m];
    }
    qsort(mail_book.mail_parm[mail_index].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);

    for ( i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        qsort(group_book.group_parm[i].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);
        for ( j = 0; j < PEDK_MAIL_ADDR_NUM_MAX; j++ )
        {
            BREAK_IF(group_book.group_parm[i].pedk_mail_index[j] == mail_info->pedk_mail_index, NET_DEBUG);
        }
        for ( k = 0; k < PEDK_MAIL_GROUP_MAX; k++ )
        {
            BREAK_IF(mail_book.mail_parm[mail_index].pedk_group_num[k] == group_book.group_parm[i].pedk_group_index, NET_DEBUG);
        }
        if ( j < PEDK_MAIL_ADDR_NUM_MAX && k == PEDK_MAIL_GROUP_MAX )
        {
            group_book.group_parm[i].pedk_mail_index[j] = 0;
            qsort(group_book.group_parm[i].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);
        }
        else if ( j == PEDK_MAIL_ADDR_NUM_MAX && k < PEDK_MAIL_GROUP_MAX )
        {
            for ( int32_t tmp = 0; tmp < PEDK_MAIL_GROUP_MAX; tmp++ )
            {
                if ( group_book.group_parm[i].pedk_mail_index[tmp] == 0 )
                {
                    group_book.group_parm[i].pedk_mail_index[tmp] = mail_info->pedk_mail_index;
                }
            }
            qsort(group_book.group_parm[i].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);
        }
    }

    ret |= save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book);
    ret |= save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book);

    return (ret ? -1 : 0);
}

int32_t remove_email_addr(int32_t mail_index)
{
    int32_t             i,j,k;
    int32_t             ret = 0;
    PEDK_MAIL_PARM      tmp_info;
    ADDRESSBOOK_MAIL    mail_book;
    ADDRESSBOOK_GROUP   group_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(mail_index == 0, NET_WARN, -1);

    for ( i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        if ( mail_book.mail_parm[i].pedk_mail_index == mail_index )
        {
            tmp_info = mail_book.mail_parm[i];
            for ( j = i; j < PEDK_MAIL_ADDR_NUM_MAX - 1; j++ )
            {
                mail_book.mail_parm[j] = mail_book.mail_parm[j + 1]; // 向前移动
            }
            pi_memset(&mail_book.mail_parm[PEDK_MAIL_ADDR_NUM_MAX - 1], 0, sizeof(PEDK_MAIL_PARM));
            mail_book.total--;
            if ( mail_book.total <= 0 )
            {
                mail_book.total = 0;
            }
            break;
        }
    }

    for ( i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {

        for ( j = 0; j < PEDK_MAIL_ADDR_NUM_MAX; j++ )
        {
            if ( group_book.group_parm[i].pedk_mail_index[j] == tmp_info.pedk_mail_index )
            {
                for ( k = j; k < PEDK_MAIL_ADDR_NUM_MAX - 1; k++ )
                {
                    group_book.group_parm[i].pedk_mail_index[k] = group_book.group_parm[i].pedk_mail_index[k + 1]; // 向前移动
                }
                group_book.group_parm[i].pedk_mail_index[PEDK_MAIL_ADDR_NUM_MAX - 1] = 0;
                qsort(group_book.group_parm[i].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);
                break;
            }
        }
    }

    ret |= save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book);
    ret |= save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book);

    return (ret ? -1 : 0);

}

int32_t get_email_addr_list(PEDK_MAIL_PARM* mail_info_list, size_t arry_size, size_t* mail_size)
{
    ADDRESSBOOK_MAIL mail_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(mail_book.total == 0, NET_WARN, -1);
    for ( int32_t i = 0; i < arry_size; i++ )
    {
        mail_info_list[i] = mail_book.mail_parm[i];
    }
    *mail_size = sizeof(PEDK_MAIL_PARM) * arry_size;

    return 0;
}



//Group
int32_t creat_email_group(PEDK_MAIL_GROUP_MGR* group_info)
{
    ADDRESSBOOK_GROUP group_book;

    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(group_book.total >= PEDK_MAIL_GROUP_MAX, NET_WARN, -1);

    pi_snprintf(group_book.group_parm[group_book.total].pedk_group_name, PEDK_MAIL_GROUP_NAME_LEN, "%s", group_info->pedk_group_name);
    pi_memset(group_book.group_parm[group_book.total].pedk_mail_index, 0, sizeof(group_book.group_parm[group_book.total].pedk_mail_index));
    for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        group_book.group_parm[group_book.total].pedk_mail_index[i] = group_info->pedk_mail_index[i];
    }

    group_book.group_parm[group_book.total].pedk_group_index = (++group_book.id == int32_t_MAX) ? 0 : group_book.id;
    group_book.total++;

    RETURN_VAL_IF(save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);

    return group_book.id;
}

int32_t get_email_group(int32_t group_index, PEDK_MAIL_GROUP_MGR* group_info, size_t* group_size)
{
    ADDRESSBOOK_GROUP group_book;

    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(group_book.total == 0, NET_WARN, -1);

    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        if ( group_book.group_parm[i].pedk_group_index == group_index )
        {
            pi_memcpy(group_info, &group_book.group_parm[i], sizeof(PEDK_MAIL_GROUP_MGR));
            *group_size = sizeof(PEDK_MAIL_GROUP_MGR);

            return 0;
        }
    }

    return -1;
}

int32_t get_email_group_num(void)
{
    ADDRESSBOOK_GROUP group_book;

    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    return group_book.total;
}

int32_t is_email_group_full(void)
{
    RETURN_VAL_IF(get_email_group_num() == PEDK_MAIL_GROUP_MAX, NET_DEBUG, 1);

    return 0;
}

int32_t add_email_to_group(int32_t group_id, int32_t mail_id)
{
    int32_t             mail_index;
    int32_t             group_index;
    int32_t             ret = 0;
    ADDRESSBOOK_MAIL    mail_book;
    ADDRESSBOOK_GROUP   group_book;


    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, 0, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(group_id == 0 || mail_id == 0, NET_WARN, -1);

    for ( int32_t n = 0; n < PEDK_MAIL_ADDR_NUM_MAX; n++ )
    {
        if ( mail_book.mail_parm[n].pedk_mail_index == mail_id )
        {
            mail_index = n;
            break;
        }
    }

    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        RETURN_VAL_IF(mail_book.mail_parm[mail_index].pedk_group_num[i] == group_id, NET_WARN, -1);
    }

    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        if ( mail_book.mail_parm[mail_index].pedk_group_num[i] == 0 )
        {
            mail_book.mail_parm[mail_index].pedk_group_num[i] = group_id;
            break;
        }
    }
    qsort(mail_book.mail_parm[mail_index].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);

    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    for ( int32_t n = 0; n < PEDK_MAIL_GROUP_MAX; n++ )
    {
        if ( group_book.group_parm[n].pedk_group_index == group_id )
        {
            group_index = n;
            break;
        }
    }

    for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        if ( group_book.group_parm[group_index].pedk_mail_index[i] == 0 )
        {
            group_book.group_parm[group_index].pedk_mail_index[i] = mail_id;
            break;
        }
    }
    qsort(group_book.group_parm[group_index].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);

    ret |= save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book);
    ret |= save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book);

    return (ret ? -1 : 0);
}

int32_t remove_email_from_group(int32_t group_id, int32_t mail_id)
{
    int32_t             mail_index;
    int32_t             group_index;
    int32_t             ret = 0;
    ADDRESSBOOK_MAIL    mail_book;
    ADDRESSBOOK_GROUP   group_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, 0, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(group_id == 0 || mail_id == 0, NET_WARN, -1);
    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);

    for ( int32_t n = 0; n < PEDK_MAIL_GROUP_MAX; n++ )
    {
        if ( group_book.group_parm[n].pedk_group_index == group_id )
        {
            group_index = n;
            break;
        }
    }

    for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        if ( group_book.group_parm[group_index].pedk_mail_index[i] == mail_id )
        {
            group_book.group_parm[group_index].pedk_mail_index[i] = 0;
            break;
        }
    }
    qsort(group_book.group_parm[group_index].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);

    for ( int32_t n = 0; n < PEDK_MAIL_ADDR_NUM_MAX; n++ )
    {
        if ( mail_book.mail_parm[n].pedk_mail_index == mail_id )
        {
            mail_index = n;
            break;
        }
    }

    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        if ( mail_book.mail_parm[mail_index].pedk_group_num[i] == group_id )
        {
            mail_book.mail_parm[mail_index].pedk_group_num[i] = 0 ;
            break;
        }
    }
    qsort(mail_book.mail_parm[mail_index].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);

    ret |= save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book);
    ret |= save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book);


    return (ret ? -1 : 0);
}

int32_t get_email_group_list(PEDK_MAIL_GROUP_MGR* group_info_list, size_t arry_size, size_t* group_size)
{
    ADDRESSBOOK_GROUP group_book;

    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(group_book.total == 0, NET_WARN, -1);
    for ( int32_t i = 0; i < arry_size; i++ )
    {
        group_info_list[i] = group_book.group_parm[i];
    }
    *group_size = sizeof(PEDK_MAIL_GROUP_MGR) * arry_size;
    return 0;
}

int32_t modify_email_group(int32_t group_id,PEDK_MAIL_GROUP_MGR* group_info)
{
    int32_t                 i,j,k;
    int32_t                 group_index = 0;
    int32_t                 ret = 0;
    ADDRESSBOOK_MAIL        mail_book;
    ADDRESSBOOK_GROUP       group_book;

    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);

    for ( int32_t n = 0; n < PEDK_MAIL_GROUP_MAX; n++ )
    {
        if ( group_book.group_parm[n].pedk_group_index == group_id )
        {
            group_index = n;
            break;
        }
    }

    group_book.group_parm[group_index].pedk_group_index = group_id;
    pi_snprintf(group_book.group_parm[group_index].pedk_group_name, PEDK_MAIL_GROUP_NAME_LEN, "%s", group_info->pedk_group_name);

    for ( i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        group_book.group_parm[group_index].pedk_mail_index[i] = group_info->pedk_mail_index[i];
    }
    qsort(group_book.group_parm[group_index].pedk_mail_index, PEDK_MAIL_ADDR_NUM_MAX, sizeof(int32_t), compareints);

    for ( i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        qsort(mail_book.mail_parm[i].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);
        for ( j = 0; j < PEDK_MAIL_GROUP_MAX; j++ )
        {
            BREAK_IF(mail_book.mail_parm[i].pedk_group_num[j] == group_info->pedk_group_index, NET_DEBUG);
        }
        for ( k = 0; k < PEDK_MAIL_ADDR_NUM_MAX; k++ )
        {
            BREAK_IF(group_book.group_parm[group_index].pedk_mail_index[k] == mail_book.mail_parm[i].pedk_mail_index, NET_DEBUG);
        }
        if ( k < PEDK_MAIL_ADDR_NUM_MAX && j == PEDK_MAIL_GROUP_MAX )
        {
            for ( int32_t tmp = 0; tmp < PEDK_MAIL_GROUP_MAX; tmp++ )
            {
                if ( mail_book.mail_parm[i].pedk_group_num[tmp] == 0 )
                {
                    mail_book.mail_parm[i].pedk_group_num[tmp] = group_info->pedk_group_index;
                }
            }
            qsort(mail_book.mail_parm[i].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);
        }
        else if ( k == PEDK_MAIL_ADDR_NUM_MAX && j < PEDK_MAIL_GROUP_MAX )
        {
            mail_book.mail_parm[i].pedk_group_num[j] = 0;
            qsort(mail_book.mail_parm[i].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);
        }
    }

    ret |= save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book);
    ret |= save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book);

    return (ret ? -1 : 0);
}

int32_t remove_email_group(int32_t group_index)
{
    int32_t             i,j,k;
    int32_t             ret = 0;
    ADDRESSBOOK_MAIL    mail_book;
    ADDRESSBOOK_GROUP   group_book;
    PEDK_MAIL_GROUP_MGR tmp_info;


    RETURN_VAL_IF(load_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(load_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(group_index == 0, NET_WARN, -1);

    for ( i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        if ( group_book.group_parm[i].pedk_group_index == group_index )
        {
            tmp_info = group_book.group_parm[i];
            for ( j = i; j < PEDK_MAIL_GROUP_MAX - 1; j++ )
            {
                group_book.group_parm[j] = group_book.group_parm[j + 1]; // 向前移动
            }
            pi_memset(&group_book.group_parm[PEDK_MAIL_GROUP_MAX - 1], 0, sizeof(PEDK_MAIL_GROUP_MGR));
            group_book.total--;
            if ( group_book.total <= 0 )
            {
                group_book.total = 0;
            }
            break;
        }
    }

    for ( i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        qsort(mail_book.mail_parm[i].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);
        for ( j = 0; j < PEDK_MAIL_GROUP_MAX; j++ )
        {
            if ( mail_book.mail_parm[i].pedk_group_num[j] == tmp_info.pedk_group_index )
            {
                for ( k = j; k < PEDK_MAIL_GROUP_MAX - 1; k++ )
                {
                    mail_book.mail_parm[i].pedk_group_num[k] = mail_book.mail_parm[i].pedk_group_num[k + 1]; // 向前移动
                }
                mail_book.mail_parm[i].pedk_group_num[PEDK_MAIL_GROUP_MAX - 1] = 0;
                qsort(mail_book.mail_parm[i].pedk_group_num, PEDK_MAIL_GROUP_MAX, sizeof(int32_t), compareints);
            }
        }
    }

    ret |= save_addressbook(MAIL_ADDRESSBOOK, ADDRESSBOOK_TYPE_MAIL, &mail_book);
    ret |= save_addressbook(GROUP_ADDRESSBOOK, ADDRESSBOOK_TYPE_GROUP, &group_book);

    return (ret ? -1 : 0);
}

//FTP
int32_t add_ftp_addr(PEDK_FTP_PARM* ftp_info)
{
    ADDRESSBOOK_FTP ftp_book;

    RETURN_VAL_IF(load_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(ftp_book.total >= PEDK_FTP_PARM_MAX, NET_WARN, -1);

    NET_DEBUG("add_ftp_addr ftp_book.total %d", ftp_book.total);

    pi_snprintf(ftp_book.ftp_parm[ftp_book.total].pedk_ftp_name, PEDK_FTP_USER_NAME_LEN, "%s", ftp_info->pedk_ftp_name);
    pi_snprintf(ftp_book.ftp_parm[ftp_book.total].pedk_ftp_addr, PEDK_FTP_SERVER_ADDR_LEN, "%s", ftp_info->pedk_ftp_addr);
    pi_snprintf(ftp_book.ftp_parm[ftp_book.total].pedk_ftp_subdirectory, PEDK_FTP_SERVER_PATH_LEN, "%s", ftp_info->pedk_ftp_subdirectory);
    ftp_book.ftp_parm[ftp_book.total].pedk_ftp_port = ftp_info->pedk_ftp_port;
    ftp_book.ftp_parm[ftp_book.total].pedk_ftp_anonymous = ftp_info->pedk_ftp_anonymous;
    pi_snprintf(ftp_book.ftp_parm[ftp_book.total].pedk_ftp_login_name, PEDK_FTP_LOGIN_NAME_LEN, "%s", ftp_info->pedk_ftp_login_name);
    pi_snprintf(ftp_book.ftp_parm[ftp_book.total].pedk_ftp_login_pwd, PEDK_FTP_PASSWORD_LEN, "%s", ftp_info->pedk_ftp_login_pwd);

    ftp_book.ftp_parm[ftp_book.total].pedk_ftp_index = (++ftp_book.id == int32_t_MAX) ? 0 : ftp_book.id;
    ftp_book.total++;
    if ( ftp_book.total >= PEDK_FTP_PARM_MAX )
    {
        ftp_book.total = PEDK_FTP_PARM_MAX;
    }

    RETURN_VAL_IF(save_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);

    return ftp_book.id;
}

int32_t get_ftp_addr(int32_t ftp_index, PEDK_FTP_PARM* ftp_info, size_t* ftp_size)
{
    ADDRESSBOOK_FTP ftp_book;

    pi_memset(ftp_info, 0, sizeof(PEDK_FTP_PARM));
    RETURN_VAL_IF(load_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(ftp_book.total == 0, NET_WARN, -1);

    NET_DEBUG("get_ftp_addr ftp_book.total %d", ftp_book.total);
    for ( int32_t i = 0; i < PEDK_FTP_PARM_MAX; i++ )
    {
        if ( ftp_book.ftp_parm[i].pedk_ftp_index == ftp_index )
        {
            NET_DEBUG("get_ftp_addr i = %d %d %d", i, ftp_index, ftp_book.ftp_parm[i].pedk_ftp_index);
            pi_memcpy(ftp_info, &ftp_book.ftp_parm[i], sizeof(PEDK_FTP_PARM));
            *ftp_size = sizeof(PEDK_FTP_PARM);
            return 0;
        }
    }

    return -1;
}

int32_t get_ftp_addr_list(PEDK_FTP_PARM* ftp_info_list, size_t arry_size, size_t* ftp_size)
{
    ADDRESSBOOK_FTP ftp_book;

    RETURN_VAL_IF(load_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(ftp_book.total == 0, NET_WARN, -1);
    for ( int32_t i = 0; i < arry_size; i++ )
    {
        ftp_info_list[i] = ftp_book.ftp_parm[i];
    }
    *ftp_size = sizeof(PEDK_FTP_PARM) * arry_size;

    return 0;
}

int32_t get_ftp_addr_num(void)
{
    ADDRESSBOOK_FTP ftp_book;

    RETURN_VAL_IF(load_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);

    return ftp_book.total;
}

int32_t is_ftp_addr_full(void)
{
    RETURN_VAL_IF(get_ftp_addr_num() == PEDK_FTP_PARM_MAX, NET_DEBUG, 1);

    return 0;
}

int32_t remove_ftp_addr(int32_t ftp_index)
{
    int32_t         i,j;
    int32_t         ret = -1;
    ADDRESSBOOK_FTP ftp_book;

    RETURN_VAL_IF(load_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(ftp_index == 0, NET_WARN, -1);

    for ( i = 0; i < PEDK_FTP_PARM_MAX; i++ )
    {
        if ( ftp_book.ftp_parm[i].pedk_ftp_index == ftp_index )
        {
            for ( j = i; j < PEDK_FTP_PARM_MAX - 1; j++ )
            {
                ftp_book.ftp_parm[j] = ftp_book.ftp_parm[j + 1]; // 向前移动
            }
            ftp_book.total--;
            if ( ftp_book.total <= 0 )
            {
                ftp_book.total = 0;
            }
            pi_memset(&ftp_book.ftp_parm[PEDK_FTP_PARM_MAX - 1] ,0, sizeof(PEDK_FTP_PARM));
            break;
        }
    }

    ret = save_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book);

    return ret;
}

int32_t modify_ftp_addr(PEDK_FTP_PARM* ftp_info)
{
    ADDRESSBOOK_FTP ftp_book;
    int32_t         ftp_index;
    int32_t         ret = -1;

    RETURN_VAL_IF(load_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book) < 0, NET_WARN, -1);

    for ( int32_t i = 0; i < PEDK_FTP_PARM_MAX; i++ )
    {
        NET_DEBUG("modify_ftp_addr i = %d %d %d", i,ftp_book.ftp_parm[i].pedk_ftp_index,ftp_info->pedk_ftp_index);
        if ( ftp_book.ftp_parm[i].pedk_ftp_index == ftp_info->pedk_ftp_index )
        {
            ftp_index = i;
            NET_DEBUG("modify_ftp_addr i = %d", ftp_index);
            break;
        }
    }

    ftp_book.ftp_parm[ftp_index].pedk_ftp_index = ftp_info->pedk_ftp_index;
    pi_snprintf(ftp_book.ftp_parm[ftp_index].pedk_ftp_name, PEDK_FTP_USER_NAME_LEN, "%s", ftp_info->pedk_ftp_name);
    pi_snprintf(ftp_book.ftp_parm[ftp_index].pedk_ftp_addr, PEDK_FTP_SERVER_ADDR_LEN, "%s", ftp_info->pedk_ftp_addr);
    pi_snprintf(ftp_book.ftp_parm[ftp_index].pedk_ftp_subdirectory, PEDK_FTP_SERVER_PATH_LEN, "%s", ftp_info->pedk_ftp_subdirectory);
    ftp_book.ftp_parm[ftp_index].pedk_ftp_port = ftp_info->pedk_ftp_port;
    ftp_book.ftp_parm[ftp_index].pedk_ftp_anonymous = ftp_info->pedk_ftp_anonymous;
    pi_snprintf(ftp_book.ftp_parm[ftp_index].pedk_ftp_login_name, PEDK_FTP_LOGIN_NAME_LEN, "%s", ftp_info->pedk_ftp_login_name);
    pi_snprintf(ftp_book.ftp_parm[ftp_index].pedk_ftp_login_pwd, PEDK_FTP_PASSWORD_LEN, "%s", ftp_info->pedk_ftp_login_pwd);

    ret = save_addressbook(FTP_ADDRESSBOOK, ADDRESSBOOK_TYPE_FTP, &ftp_book);

    return ret;
}

//SMB
int32_t add_smb_addr(PEDK_SMB_PARM* smb_info)
{
    ADDRESSBOOK_SMB smb_book;

    RETURN_VAL_IF(load_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(smb_book.total >= PEDK_SMB_PARM_MAX, NET_WARN, -1);

    pi_snprintf(smb_book.smb_parm[smb_book.total].pedk_smb_name, PEDK_SMB_USER_NAME_LEN, "%s", smb_info->pedk_smb_name);
    pi_snprintf(smb_book.smb_parm[smb_book.total].pedk_smb_addr, PEDK_SMB_SERVER_ADDR_LEN, "%s", smb_info->pedk_smb_addr);
    pi_snprintf(smb_book.smb_parm[smb_book.total].pedk_smb_subdirectory, PEDK_SMB_SERVER_PATH_LEN, "%s", smb_info->pedk_smb_subdirectory);
    smb_book.smb_parm[smb_book.total].pedk_smb_port = smb_info->pedk_smb_port;
    smb_book.smb_parm[smb_book.total].pedk_smb_anonymous = smb_info->pedk_smb_anonymous;
    pi_snprintf(smb_book.smb_parm[smb_book.total].pedk_smb_login_name, PEDK_SMB_LOGIN_NAME_LEN, "%s", smb_info->pedk_smb_login_name);
    pi_snprintf(smb_book.smb_parm[smb_book.total].pedk_smb_login_pwd, PEDK_SMB_PASSWORD_LEN, "%s", smb_info->pedk_smb_login_pwd);

    smb_book.smb_parm[smb_book.total].pedk_smb_index = (++smb_book.id == int32_t_MAX) ? 0 : smb_book.id;
    smb_book.total++;
    if ( smb_book.total >= PEDK_SMB_PARM_MAX )
    {
        smb_book.total = PEDK_SMB_PARM_MAX;
    }

    RETURN_VAL_IF(save_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);

    return smb_book.id;
}

int32_t get_smb_addr(int32_t smb_index, PEDK_SMB_PARM* smb_info, size_t* smb_size)
{
    ADDRESSBOOK_SMB smb_book;

    RETURN_VAL_IF(load_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(smb_book.total == 0, NET_WARN, -1);
    for ( int32_t i = 0; i < PEDK_SMB_PARM_MAX; i++ )
    {
        if ( smb_book.smb_parm[i].pedk_smb_index == smb_index )
        {
            pi_memcpy(smb_info, &smb_book.smb_parm[i], sizeof(PEDK_SMB_PARM));
            *smb_size = sizeof(PEDK_SMB_PARM);
            return 0;
        }
    }

    return -1;
}

int32_t get_smb_addr_list(PEDK_SMB_PARM* smb_info_list, size_t arry_size, size_t* smb_size)
{
    ADDRESSBOOK_SMB smb_book;

    RETURN_VAL_IF(load_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(smb_book.total == 0, NET_WARN, -1);
    for ( int32_t i = 0; i < arry_size; i++ )
    {
        smb_info_list[i] = smb_book.smb_parm[i];
    }
    *smb_size = sizeof(PEDK_SMB_PARM) * arry_size;

    return 0;
}

int32_t get_smb_addr_num(void)
{
    ADDRESSBOOK_SMB smb_book;

    RETURN_VAL_IF(load_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);

    return smb_book.total;
}

int32_t is_smb_addr_full(void)
{
    RETURN_VAL_IF(get_smb_addr_num() == PEDK_SMB_PARM_MAX, NET_DEBUG, 1);
    return 0;
}

int32_t remove_smb_addr(int32_t smb_index)
{
    int32_t         i,j;
    int32_t         ret = -1;
    ADDRESSBOOK_SMB smb_book;

    NET_DEBUG("remove_smb_addr smb_index %d。", smb_index);
    RETURN_VAL_IF(load_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);
    RETURN_VAL_IF(smb_index == 0, NET_WARN, -1);

    for ( i = 0; i < PEDK_SMB_PARM_MAX; i++ )
    {
        if ( smb_book.smb_parm[i].pedk_smb_index == smb_index )
        {
            for ( j = i; j < PEDK_SMB_PARM_MAX - 1; j++ )
            {
                smb_book.smb_parm[j] = smb_book.smb_parm[j + 1]; // 向前移动
            }
            smb_book.total--;
            if ( smb_book.total <= 0 )
            {
                smb_book.total = 0;
            }
            pi_memset(&smb_book.smb_parm[PEDK_SMB_PARM_MAX - 1] ,0, sizeof(PEDK_SMB_PARM));
            break;
        }
    }

    ret = save_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book);

    return ret;
}

int32_t modify_smb_addr(PEDK_SMB_PARM* smb_info)
{
    ADDRESSBOOK_SMB smb_book;
    int32_t         smb_index = 0;
    int32_t         ret = -1;

    RETURN_VAL_IF(load_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book) < 0, NET_WARN, -1);
    for ( int32_t i = 0; i < PEDK_SMB_PARM_MAX; i++ )
    {
        if ( smb_book.smb_parm[i].pedk_smb_index == smb_info->pedk_smb_index )
        {
            smb_index = i;
            break;
        }
    }

    smb_book.smb_parm[smb_index].pedk_smb_index = smb_info->pedk_smb_index;
    pi_snprintf(smb_book.smb_parm[smb_index].pedk_smb_name, PEDK_SMB_USER_NAME_LEN, "%s", smb_info->pedk_smb_name);
    pi_snprintf(smb_book.smb_parm[smb_index].pedk_smb_addr, PEDK_SMB_SERVER_ADDR_LEN, "%s", smb_info->pedk_smb_addr);
    pi_snprintf(smb_book.smb_parm[smb_index].pedk_smb_subdirectory, PEDK_SMB_SERVER_PATH_LEN, "%s", smb_info->pedk_smb_subdirectory);
    smb_book.smb_parm[smb_index].pedk_smb_port = smb_info->pedk_smb_port;
    smb_book.smb_parm[smb_index].pedk_smb_anonymous = smb_info->pedk_smb_anonymous;
    pi_snprintf(smb_book.smb_parm[smb_index].pedk_smb_login_name, PEDK_SMB_LOGIN_NAME_LEN, "%s", smb_info->pedk_smb_login_name);
    pi_snprintf(smb_book.smb_parm[smb_index].pedk_smb_login_pwd, PEDK_SMB_PASSWORD_LEN, "%s", smb_info->pedk_smb_login_pwd);

    ret = save_addressbook(SMB_ADDRESSBOOK, ADDRESSBOOK_TYPE_SMB, &smb_book);

    return ret;
}

/**
 *@}
 */
