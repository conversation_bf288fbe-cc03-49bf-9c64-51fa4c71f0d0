/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file runtime.h
 * @addtogroup runtime
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief runtime init
 */
#ifndef __RUNTIME_H__
#define __RUNTIME_H__

#include <quickjs.h>
#include <quickjs-libc.h>
#include <uv.h>
#include "basic/hash/uthash.h"
#include "basic/config.h"
#include "runtime/utils/property_utils.h"

typedef struct InstanceDataList {
    char instance_name[APP_NAME_LEN_MAX];   ///<instance module name
    void* instance_data;                    ///<instance module data
    UT_hash_handle hh;                      ///<hash handle
} InstanceDataList;

typedef struct APPPath {
    char* work_space;           ///<app work path
    char* app_js;               ///<app.js path
    char* app_json;             ///<app json path
    char* proj_config_json;     ///<project config json path
    char* data;                 ///<data
    char* js;                   ///<js
    char* log;                  ///<log
} APPPath;

typedef struct WhileList {
    char    name[64];       ///<whilelist name
    int32_t value;          ///<whilelist value
    UT_hash_handle hh;      ///<hash handle
} WhileList;

typedef struct ProjectConfig{
    int64_t valid_time;     ///<valid time
    WhileList *whitelist;   ///<whitelist 
} ProjectConfig;

/* 线程间通信的内部消息类型 */
typedef enum {
    E_INNER_END,        ///<inner js end
    E_INNER_APP_MSG,    ///<inner app msg
    E_INNER_HEARTBEAT   ///<inner heartbeat 
} E_INNER_CMD;

/* 线程间通信的内部消息结构 */
typedef struct INNER_MSG {
    E_INNER_CMD type;       ///<inner msg type
    uint16_t data_length;   ///<inner msg data len
    uint8_t  *data;         ///<inner msg data
    struct INNER_MSG *next; ///<inner msg pointer
} INNER_MSG;

/* 内部消息队列 */
typedef struct MsgQueue{
   uint32_t msg_cont;       ///<inner msg queue counts
   INNER_MSG *msg_head;     ///<inner msg queue head
   INNER_MSG *msg_tail;     ///<inner msg queue tail
}MsgQueue;

// 运行时资源，各个APP的所有资源全部挂载到这个结构体中
typedef struct PeSFRunTime {
    /* quickjs相关资源 */
    JSRuntime* qjs_rt;          ///< quick js的JS运行时
    JSContext* qjs_ctx;         ///< quick js的上下文

    /* libuv相关资源 */
    uv_loop_t* uv_loop;         ///< 循环管理句柄
    uv_async_t* uv_async;       ///< 消息队列的同步信号句柄
    uv_idle_t* uv_idle;         ///< 空闲状态句柄
    uv_prepare_t* uv_prepare;   ///< 预处理句柄
    uv_check_t* uv_check;       ///< 检查句柄
    uv_mutex_t* uv_mutex;       ///< 锁
    MsgQueue    msgq;           ///< msg queue

    char* app_name;             ///< 应用程序名
    APPPath app_path;           ///< app path
    uint64_t thread_handle;     ///< 运行时的线程句柄
    int exit_code;              ///< 退出标记
    DynamicProperty dynamic_property; ///< APP属性
    ProjectConfig proj_config;  ///< project config
#define BEAT 1
#define NOT_BEAT 0
    int heartbeat_flag;

    /* 功能函数 */
    void* (*start_func)(void*); ///< 线程启动函数
    /* 各模块的实例化数据 */
    InstanceDataList* instance_list; ///< 各个子模块实例化数据注册列表。该列表不可直接使用，提供以下三个工具函数
    int32_t (*add_instance_data)(struct PeSFRunTime* prt, char* instance_data_name, void* data); ///< 添加实例化数据，形成映射[instance_data_name：data]
    void* (*get_instance_data)(struct PeSFRunTime* prt, char* instance_data_name); ///< 查找实例化数据。
    int32_t (*del_instance_data)(struct PeSFRunTime* prt, char* instance_data_name); ///< 删除实例化数据。
} PeSFRunTime;


/**
 * @brief   create runtime
 * @param[in] *data :app name 
 * @param[in] len   :app len
 * @return  Construct result
 * @retval  *PeSFRunTime :return a heap space runtime handle
 * <AUTHOR> @date    2024-06-11
 */
PeSFRunTime* runtime_create(uint8_t* data, uint16_t len);

/**
 * @brief   destroy runtime
 * @param[in] *prt :a heap space runtime handle
 * @return  Construct result
 * @retval  =0 :sucess
 * @retval  <0 :error
 * <AUTHOR> @date    2024-06-11
 */
int runtime_destroy(PeSFRunTime* prt);

/**
 * @brief   destroy runtime simple, only in the creation of runtime failure
 * @param[in] *prt :a heap space runtime handle
 * @return  Construct result
 * @retval  =0 :sucess
 * @retval  <0 :error
 * <AUTHOR> @date    2024-06-11
 */
int runtime_destroy_simple(PeSFRunTime* prt);

/**
 * @brief   stop loop of runtime 
 * @param[in] *prt :a heap space runtime handle
 * @param[in] code :exit code
 * @return  Construct result
 * @retval  =0 :sucess
 * @retval  <0 :error
 * <AUTHOR> @date    2024-06-11
 */
int runtime_stop_loop(PeSFRunTime* prt, int code);

/**
 * @brief   processing of internal message funtions in each child thread execution
 * @param[in] *prt :a heap space runtime handle
 * @param[in] *inner_msg :innner msg
 * <AUTHOR> @date    2024-06-11
 */
/**
 * @brief 处理内部消息函数，在各子线程执行
 * 
 * @param inner_msg 
 */
void app_msg_exec(PeSFRunTime* prt ,INNER_MSG* inner_msg);

/* 宏定义 */
//JSRuntime -> PeSFRunTime
#define GET_PESF_RUNTIME(ctx) (PeSFRunTime*)JS_GetContextOpaque(ctx) ///< 在函数内部，通过JSRuntime类型，获取到其上层的PeSFRunTime类型

/**
 * @brief   run runtime 
 * @param[in] *prt :a heap space runtime handle
 * @return  Construct result
 * @retval  =0 :sucess
 * @retval  <0 :error
 * <AUTHOR> @date    2024-06-11
 */
int32_t runtime_run(PeSFRunTime* prt);

/**
 * @brief   run init
 * @param[in] *prt :a heap space runtime handle
 * <AUTHOR> @date    2024-06-11
 */
void runtime_init(PeSFRunTime* prt);
#endif /* __RUNTIME_H__ */
/**
 * @}
 */
 
