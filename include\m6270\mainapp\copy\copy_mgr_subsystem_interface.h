/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       copy_mgr_subsytem_interface.h
 * @addtogroup copy system
 * @{
 * @addtogroup copy mgr subsystem
 * <AUTHOR>
 * @date       2023-06-9
 * @version    v1.0
 * @details    interface for the copy mgr subsystem
 */
#ifndef __COPY_JOB_SUBSYSTEM_INTERFACE_H__
#define __COPY_JOB_SUBSYSTEM_INTERFACE_H__

/**
 * @brief  status module
 *                
 */
 
 /**
 * @brief register the module to system
 * @param[in] the module name
 * @param[in] the module level
 * @return -1 : Error  0 : Success
 * @autor liushaoxi
 * @date 2023-06-10
 */
int copy_mgr_register_status_mod(char *module_name,int level);

/**
* @brief add the status to module
* @param[in] the module's instance
* @param[in] the name for status
* @param[in] the level for status
* @param[in] the value for status
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-06-9
*/
int copy_mgr_add_status(char *module_name,char *status_name,char level,int status_value);

/**
* @brief update the status name for the module
* @param[in] the module name
* @param[in] the status name
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-06-10
*/
int copy_mgr_update_status_with_name(char *module_name,char *status_name);

/**
* @brief update the status value for the module
* @param[in] the module name
* @param[in] the status value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-06-10
*/
int copy_mgr_update_status_with_value(char *module_name,int status_value);

/**
* @brief get the version of this interface
* @return the version
* @autor liushaoxi
* @date 2023-06-9
*/
char *copy_mgr_get_intf_version(void);

/**
* @brief initialize the copy manager subsystem
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-06-9
*/
int copy_mgr_subsystem_init(void);


#endif
/**
 *@}
 */
