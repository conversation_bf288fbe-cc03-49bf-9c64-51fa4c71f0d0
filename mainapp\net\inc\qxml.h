//*****************************************************************************
//  Copyright (C) 2012 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/incl/qxml.h#6 $
//  $Change: 198955 $ $Date: 2012/06/14 $
//
/// @file
/// Public Interface for XML parsing and utilities subsystem
///
/// @ingroup Network
///
/// <AUTHOR> <<EMAIL>>
//
//*****************************************************************************
#ifndef QXML_H
#define QXML_H 1

#define XML_STREAM_EOF          -1
#define XML_BAD_ELEMENT         -2
#define XML_BAD_VERSION_SPEC    -3
#define XML_NO_ATTRIB_VALUE     -4
#define XML_NO_ATTRIB_QUOTE     -5
#define XML_BAD_PARAMETER       -6
#define XML_OK                  0

// ----------------------------------------------------------------------

#include <stddef.h>
#include <stdint.h>
typedef enum
{
    xmlStart,
    xmlVersion,
    xmlElement,
    xmlValue,
    xmlAttributeName, xmlAttributeValue,
    xmlComment, xmlClearData,
    xmlEnd
}
QXMLtokenType;

// ----------------------------------------------------------------------

typedef enum
{
    psBase, psNonWhite, psVersion,
    psElement1, psElement, psSingularElement,
    psComment1, psComment2, psComment, psCommentNL,
    psAttribLeft, psAttribRight, psAttribValue, psAttribUnquotedValue
}
QXMLparseState;

typedef struct
{
    char*           m_src;
    char*           m_cur;
    char*           m_pe;

    int             m_line;
    int             m_column;

    QXMLparseState  m_state;
    int             m_level;
    char*           m_token;
    int             m_toklen;

    int             m_expandum;
}
QXML_S, *PQXML;

//***********************************************************************
int             QXMLvalueEscape         (char* pdst, char* pval, int ndst);
PQXML           QXMLparserCreate        (char* src, int expandEnties);
PQXML           QXMLparserClone         (PQXML pxml);
PQXML           QXMLparserSyncTo        (PQXML pxmlBase, PQXML pxmlVar);
void            QXMLparserDelete        (PQXML pxml);
char*           QXMLnextElement         (PQXML pxml);
char*           QXMLclosingElement      (PQXML pxml);
char*           QXMLsiblingElement      (PQXML pxml);
char*           QXMLchildElement        (PQXML pxml);
char*           QXMLfirstElement        (PQXML pxml);
char*           QXMLnextAttribute       (PQXML pxml, char* ptag, int ntag);
char*           QXMLnextValue           (PQXML pxml);
int             QXMLelementCopy         (PQXML pxml, char* pdst, char* pval, size_t ndst);
int             QXMLvalueCopy           (PQXML pxml, char* pdst, char* pval, size_t ndst);
int             QXMLattributeCopy       (PQXML pxml, char* pdst, char* pval, size_t ndst);
char*           QXMLelementName         (PQXML pxml, char* pelement);
int             QXMLelementCompare      (PQXML pxml, char* pelement, char* pname);
int             QXMLelementCaseCompare  (PQXML pxml, char* pelement, char* pname);
int             QXMLvalueCompare        (PQXML pxml, char* pvalue, char* pname);
int             QXMLattributeCompare    (PQXML pxml, char* pvalue, char* pname);
int32_t         QXMLsoapGetVal          (PQXML pxml, const char* name, char* val, size_t val_size);
int32_t         QXMLsoapFind            (PQXML pxml, const char* name);

#endif

