/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  scan_var.h
* @addtogroup scan
*
* @{
* @addtogroup  scan
* <AUTHOR>
* @date   2022-5-24
* @version  v1.0
* @brief   transplant LP-2023 protocol for pc driver
**/

#ifndef __SCAN_VAR_H__
#define __SCAN_VAR_H__

#include <stdint.h>
#include <stdbool.h>
#include "pol/pol_threads.h"

#ifdef __cplusplus
extern "C" {
#endif

#define FTP_USER_NAME_LEN		129
#define FTP_SERVER_NAME_LEN		32
#define FTP_SERVER_PATH_LEN		64
#define FTP_LOGIN_NAME_LEN		64
#define FTP_PASSWORD_LEN		32
#define FTP_PARM_MAX			60


#define MAIL_LINKMAN_NAME_LEN   16  ///< mail linkman name len
#define MAIL_ADDR_LEN           64  ///< mail addr len
#define MAIL_ADDR_NUM_MAX       60  ///< mail adde num max
#define MAIL_GROUP_NAME_LEN     16  ///< mail group name len
#define MAIL_GROUP_MAX          10  ///< mail group max

#define HTTP_URL_PATH_LEN	   512
#define HTTP_HEADERS_PATH_LEN	   1024



#define SCAN_MGR_BAND_HEIGHT_BASE         32    ///< scan mgr band height base
#define SCAN_MGR_RESOLUTION_BASE          1200  ///< scan mgr resolution base

/**
 * @brief scan send router
*/
typedef struct
{
    uint8_t doc_size_sensor1;       ///< doc size sensor
    uint8_t doc_size_sensor2;       ///< doc size sensor
    uint8_t home_position_sensor;   ///< home position sensor
    uint8_t df_shading_home_positon_sensor; ///< df shading home position sensor
    uint8_t doc_cover_0degree;      ///< doc cover 0 degree
    uint8_t doc_cover_20degree;     ///< doc cover 20 degree
} SCANNER_SENSOR_STATUS_S;

/**
 * @brief scanner scanner status
*/
typedef struct
{
    uint32_t    main_state;                 ///< main status
    uint32_t    error_code;                 ///< error code
    uint32_t    adf_paper_state;            ///< have paper or no paper
    uint32_t    adf_cover_state;            ///< adf cover open or not
    uint32_t    adf_bottom_cover_state;     ///< adf bottom cover open or not
    uint32_t    fb_cover_state;             ///< fb cover open or not
    uint32_t    cis_cover_state;            ///< cis cover open or not
    uint32_t    adf_feed_jam_state;         ///< adf paper in path or not  JAM_CODE_T
    uint32_t    adf_fatal_state;            ///< fun state ADF_ENGINE_FATAL_T
    uint32_t    fb_paper_width;             ///< update to application,when paper insert;
    uint32_t    fb_paper_height;            ///< updata to application,when paper insert;
    uint32_t    adf_paper_width;            ///< update to application,when paper insert;
    uint32_t    adf_paper_height;           ///< updata to application,when paper insert;
    uint32_t    fb_fatal_state;             ///< fb fatal  FB_ENGINE_FATAL_T

} SCANNER_STATUS_S;

/**
 * @brief doc input mode
*/
typedef enum
{
    DOC_INPUT_MODE_FB,      ///< fb mode
    DOC_INPUT_MODE_DF,      ///< df mode
    DOC_INPUT_MODE_AUTO,    ///< auto mode
} DOC_INPUT_MODE_E;

/**
 * @brief scan param
*/
typedef struct
{
    int doc_input_mode;                         ///< OC_INPUT_MODE_T
    uint32_t        resolution_v;               ///< y resolution
    uint32_t        resolution_h;               ///< x resolution
    char            is_mix_doc;                 ///< is mix doc
    char            simplex_duplex;             ///< simplex duplex
    long int        doc_width;                  ///< doc width
    long int        doc_height;                 ///< doc height
    uint32_t        is_mix_size;                ///< mix scan flag
} SCAN_ENGINE_MGR_PARAM_S;

/**
 * @brief scan config
*/
typedef struct
{
    uint16_t    Resolution;             ///< resolution
    uint16_t    ScanColor;              ///< scan color
    uint16_t    FileFormat;             ///< file format
    uint16_t    ScanArea;               ///< scan area
    uint16_t    ScanShadingCorrection;   ///< scan shading correction

    uint16_t     Scantype;               ///< scan type
    uint16_t     ScanNup;               ///< scan nup
    uint16_t    ScanTo;                 ///< scan dest
    uint16_t    ScanAutoDouble;         ///< scan auto double flag

    uint32_t    ScanEmptyMarginTopFb;       ///< scan fb top margin
    uint32_t    ScanEmptyMarginLeftFb;      ///< scan fb left margin
    uint32_t    ScanEmptyMarginTopAdf;      ///< scan adf top margin
    uint32_t    ScanEmptyMarginLeftAdf;     ///< scan adf top margin
    uint32_t    ScanEmptyMarginDuplexTop;   ///< scan dupdlex adf top margin
    uint32_t    ScanEmptyMarginDuplexLeft;  ///< scan dupdlex adf top margin
} SCAN_CONFIG_S;

/**
 * @brief scan state
*/
typedef enum
{
    SCAN_STATE_INIT = 0,    ///< init status
    SCAN_STATE_IDLE,        ///< idle status
    SCAN_STATE_SLEEP,       ///< sleep status
    SCAN_STATE_SCAN,        ///< scan status
    SCAN_STATE_ERROR,       ///< error status
    SCAN_STATE_CANCEL,      ///< cancel status
    SCAN_STATE_PAUSE        ///< pause status
} SCANNER_STATE_E;

/**
 * @brief scan job state
*/
typedef enum
{
    SCAN_JOB_NONE = 0,      ///< job none
    SCAN_JOB_REQUEST,       ///< job request
    SCAN_JOB_WAIT_BANDS,    ///< wait band
    SCAN_JOB_START,         ///< job start
    SCAN_JOB_PAUSE,         ///< job pause
    SCAN_JOB_END,           ///< job end
    SCAN_JOB_ERROR,         ///< job error
    SCAN_JOB_CANCEL,        ///< job cancel
} SCAN_JOB_STATE_E;

/**
 * @brief adf paper state
*/
typedef enum
{

    ADF_NO_PAPER = 0,   ///<host scan driver define
    ADF_HAVE_PAPER,     ///< adf have paper flag
} ADF_PAPER_STATE_E;

/**
 * @brief scan error type
*/
typedef enum
{
    SCAN_ERROR_TYPE_NONE                    = 0x00,     ///< none error

    //tray error
    SCAN_ERROR_TYPE_PAPER_EMPTY             = 0x11,     ///< tray error paper empty
    SCAN_ERROR_TYPE_PAPER_MISMATCH          = 0x12,     ///< tray error paper mismatch
    SCAN_ERROR_TYPE_FEED_FAILED             = 0x13,     ///< tray error feed failed

    //application error
    SCAN_ERROR_TYPE_MEM_SHORTAGE            = 0x21,     ///< application error mem shortage

    //big error
    SCAN_ERROR_TYPE_PAPER_JAM               = 0x31,     ///< big error paper jam
    SCAN_ERROR_TYPE_COVER_OPEN              = 0x32,     ///< big error cover open
    SCAN_ERROR_TYPE_CIS_COVER_OPEN          = 0x33,     ///< big error cis cover open
    SCAN_ERROR_TYPE_DATA_RECEIVE_TIMEOUT    = 0x34,     ///< data receive timeout
    SCAN_ERROR_TYPE_ADF_BOTTOM_COVER_OPEN   = 0x35,     ///< adf bottom cover open

    //components error
    SCAN_ERROR_TYPE_MOTOR_ABNORMAL          = 0x41,     ///< motor abnormal
    SCAN_ERROR_TYPE_CIS_ABNORMAL            = 0x42,     ///< cis abnormal
    SCAN_ERROR_TYPE_AFE_ABNORMAL            = 0x43,     ///< afe abnormal
    SCAN_ERROR_TYPE_DMA_ABNORMAL            = 0x44,     ///< dam abnormal

    //other fatal error
    SCAN_ERROR_TYPE_COMM_FAILED             = 0x61,     ///< other fatal error
} SCAN_ERROR_TYPE_E;

/**
 * @brief fb engine fatal
*/
typedef enum
{
    FB_FATAL_THE_EXPOSURE_TURNING_ON_THE_LAMP_IS_DEFECTIVE                      =   0x0004, ///<
    FB_FATAL_EXPOSURE_LAMP_IRREGULAR_POINT_LIGHT                                =   0x0044,
    FB_FATAL_DRIVE_SYSTEM_HOME_SENSOR_ABNORMAL                                  =   0x0146,
    FB_FATAL_SLIDER_OVERRUNNING                                                 =   0x0186,
    FB_FATAL_SCANNER_SEQUENCE_TROUBLE_1                                         =   0x0054,
    FB_FATAL_EMMC_ABNORMAL                                                      =   0x0020,
    FB_FATAL_CCD_CLAMP_GAIN_ADJUSTMENT_ABNORMAL                                 =   0x0073,
    FB_FATAL_SCANNER_IMAGE_PROCESSING_ASIC_CLOCK_INPUT_DEFECT_TROUBLE_FRONT     =   0x00B3,
    FB_FATAL_ABNORMAL_OF_POWER_SUPPLY_VOLTAGE_OF_CCD                            =   0x00F3,
    FB_FATAL_DF_EXPOSURE_TURNING_ON_THE_LAMP_IS_DEFECTIVE                       =   0x0078,
    FB_FATAL_DF_EXPOSURE_LAMP_IRREGULAR_POINT_LIGHT                             =   0x00B8,
    FB_FATAL_DF_SHADING_APPARATUS_HOME_SENSOR_ABNORMAL                          =   0x00F8,
    FB_FATAL_DF_SHADING_APPARATUS_BOARD_HOME_SENSOR_ABNORMAL                    =   0x0138,
    FB_FATAL_DF_CIS_CLAMP_ADJUSTMENT_ABNORMAL                                   =   0x0178,
    FB_FATAL_DF_CIS_GAIN_ADJUSTMENT_ABNORMA                                     =   0x01B8,
    FB_FATAL_FATAL_SCANNER_IMAGE_PROCESS_ASIC_CLOCK_INPUT_DEFECT_TROUBLE_FRONT  =   0x01F8,
    FB_FATAL_INIT_TIMEOUT                                                       =   0xFFFE,
    FB_FATAL_CONNECT_ENGINE_TIMEOUT                                             =   0xFFFF,
} FB_ENGINE_FATAL_E;

/**
 * @brief adf engine fatal
*/
typedef enum
{
    ADF_FATAL_COOLING_FAN                                           =   0x0001,
    ADF_FATAL_CANTACT_RETRACT_MECHANISM_IN_FRONT_OF_SCAN_SLIT       =   0x0002,
    ADF_FATAL_BRUSH_MOVEMENT                                        =   0x0200,
    ADF_FATAL_INIT_TIMEOUT                                          =   0xFFFF,
} ADF_ENGINE_FATAL_E;

typedef struct
{
    char        server_name[128+1];    ///< FTP服务器名称
    char        server_addr[32];    ///< FTP服务器地址
    char        server_path[64];    ///< FTP服务器子目录
    uint16_t    server_port;        ///< FTP服务器端口
    uint16_t    anonymity;          ///< 是否匿名登陆
    char        login_name[64];     ///< 登陆用户名
    char        login_pswd[32];     ///< 登陆密码
    int32_t     record_id;          ///< FTP唯一标识
} FTP_PARAM_S, *FTP_PARAM_P;


//SMB
#define SMB_USER_NAME_LEN		128+1  //Windy modify form 33 to 256 in 20200708
#define SMB_SERVER_NAME_LEN		32+1
#define SMB_SERVER_PATH_LEN		129
#define SMB_LOGIN_NAME_LEN		129
#define SMB_PASSWORD_LEN		32+1
#define SMB_PARM_MAX			60


typedef struct
{
	char     smb_user_name[SMB_USER_NAME_LEN];								//用户别名scan
	char     smb_server_name[SMB_SERVER_NAME_LEN];							//smb服务器名
	char     smb_login_name[SMB_LOGIN_NAME_LEN];							//登录名
	char     smb_server_path[SMB_SERVER_PATH_LEN];							//smb服务器路径
	char     smb_password[SMB_PASSWORD_LEN];								//密码
	uint16_t smb_port;														//端口号
	uint16_t is_anonymity;													//是否匿名
}SMB_PARM_S, *PSMB_PARM;


typedef struct
{
    char scanToHttpPath[HTTP_URL_PATH_LEN];
    char scanToHttpheaders[HTTP_HEADERS_PATH_LEN];
    int  is_certified;
}HTTP_PARM_S, *PHTTP_PARM;

/**
 * @brief ftp param
*/
typedef struct
{
    char     ftp_user_name[FTP_USER_NAME_LEN];          ///< ftp user name
    char     ftp_server_name[FTP_SERVER_NAME_LEN];      ///< ftp server name
    char     ftp_login_name[FTP_LOGIN_NAME_LEN];        ///< ftp login name
    char     ftp_server_path[FTP_SERVER_PATH_LEN];      ///< ftp server path
    char     ftp_password[FTP_PASSWORD_LEN];            ///< ftp password
    uint16_t ftp_port;                                  ///< ftp port
    uint16_t is_anonymity;                              ///< is anonymity
    uint16_t ftp_index;                                 ///< ftp index
    uint16_t ftp_prev_index;                            ///< ftp prev index
    uint16_t ftp_next_index;                            ///< ftp next index
} FTP_PARM_S, *FTP_PARM_P;

/**
 * @brief gamma array
*/
typedef struct
{
    uint16_t  GAMMARGB[3][257]; ///< gamma array

} CORRECTIONGAMMA_S, *CORRECTIONGAMMA_P;

/**
 * @brief ftp search result
*/
typedef struct
{
    uint32_t search_count;                                                  ///< search count
    uint8_t  index[FTP_PARM_MAX];                                           ///< index
} FTP_PARM_SEARCH_RESULT_S;

/**
 * @brief mail param
*/
typedef struct
{
    char     mail_linkman_name[MAIL_LINKMAN_NAME_LEN];                      ///< mail link name
    char     mail_addr[MAIL_ADDR_LEN];                                      ///< mail addr
    uint8_t  mail_group_no[MAIL_GROUP_MAX];                                 ///< mail group no
    uint16_t mail_index;                                                    ///< mail index

    uint16_t mail_prev_index;                                               //ָ/< mail prev index
    uint16_t mail_next_index;                                               ///< mail next index
} MAIL_PARM_S, *MAIL_PARM_P;

/**
 * @brief group param
*/
typedef struct
{
    char     group_name[MAIL_GROUP_NAME_LEN];                               ///< group name
    uint8_t  mail_index[MAIL_ADDR_NUM_MAX];                                 ///< mail index
    uint16_t group_no;                                                      ///< gourp no
    uint16_t group_num;                                                     ///< gourp number

    uint16_t group_prev_index;                                              //ָ/< gourp prev index
    uint16_t group_next_index;                                              ///< gourp next index
} MAIL_GROUP_MGR_S;

/**
 * @brief mail search result
*/
typedef struct
{
    uint32_t search_count;                                                  ///< search count
    uint8_t  index[MAIL_ADDR_NUM_MAX];                                      ///< index
} MAIL_SEARCH_RESULT_S;

/**
 * @brief get_offic_flag
 * @return uint32_t  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
uint32_t scan_vars_get_offic_flag( void );

/**
 * @brief get_offic_scan
 * @return uint32_t  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
uint32_t scan_vars_get_offic_scan( void );

/**
 * @brief scan_vars_set_resolution
 * @param[in] Value resolution
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_vars_set_resolution( uint32_t Value );

/**
 * @brief scan_vars_get_resolution
 * @param[out] Value resolution
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_vars_get_resolution( uint32_t* Value );

/**
 * @brief scan_vars_set_shading_correction
 * @param[in] Value shading_correction
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_vars_set_shading_correction( uint32_t Value );

/**
 * @brief scan_vars_set_shading_correction
 * @param[out] Value shading_correction
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_vars_get_shading_correction( uint32_t* Value );

/**
 * @brief scan_vars_set_colour
 * @param[in] Value shading_colour
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_vars_set_colour( uint32_t Value );

/**
 * @brief scan_vars_get_colour
 * @param[out] Value shading_colour
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_vars_get_colour( uint32_t* Value );


/**
 * @brief scan_vars_set_file_format
 * @param[in] Value file_format
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_vars_set_file_format( uint32_t Value );

/**
 * @brief scan_vars_get_file_format
 * @param[out] Value file_format
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_vars_get_file_format( uint32_t* Value );

/**
 * @brief scan_vars_set_area
 * @param[in] Value area
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_vars_set_area( uint32_t Value );

/**
 * @brief scan_vars_get_area
 * @param[out] Value area
 * @return int  \n
 * @retval success:0, failed:-1
 * <AUTHOR>
 * @date 2022-5-24
*/
int scan_vars_get_area( uint32_t* Value );

#if 0
void InitFtpParm( void );
int32_t SetFtpParm( FTP_PARM_S* p_ftp_parm );
FTP_PARM_S* GetFtpHeader( void );
FTP_PARM_S* GetFtpParmByIndex( uint16_t index );
int32_t SearchFtpParmByKeyword( FTP_PARM_SEARCH_RESULT* p_search_result, int8_t* p_keyword );
void DelFtpParm( uint16_t ftp_index );
int32_t NewFtpParm( FTP_PARM_S** p_ftp_parm );

void InitMailParm( void );
uint32_t NewMailParm( MAIL_PARM_S** p_mail_parm );
MAIL_PARM_S* GetMailParmHeader( void );
MAIL_PARM_S* GetMailParmByIndex( uint16_t index );
int32_t SearchMailParmByKeyword( MAIL_SEARCH_RESULT* p_search_result, const char* p_keyword );
void DelMailParm( uint16_t index );
int32_t SetMailParm( MAIL_PARM_S* p_mail_parm );

int32_t AddMailGroup( MAIL_PARM_S* p_mail_parm, uint16_t group_index );
int32_t NewMailGroup( MAIL_GROUP_MGR_S** p_mail_group_mgr );
int32_t SetMailGroup( MAIL_GROUP_MGR_S* p_mail_group );
MAIL_GROUP_MGR_S* GetMailGroupHeader( void );
MAIL_GROUP_MGR_S* GetMailGroupByIndex( uint16_t index );
int32_t SearchMailGroupByKeyword( MAIL_SEARCH_RESULT* p_search_result, char* p_keyword );
void DelMailGroup( uint16_t group_index );
#endif

/**
 * @brief scan_var_prolog
 * @return void  \n
 * @retval void
 * <AUTHOR>
 * @date 2022-5-24
*/
void scan_var_prolog( void );

#ifdef __cplusplus
}
#endif
#endif

/**
 *@}
 */

