#include <stdio.h>
#include <unistd.h>
#include "pol_types.h"
#include "pol_mem_trace.h"
#include "pol_mem.h"
//#include "pol_io.h"

#include <assert.h>
#include <errno.h>
#include "case.h"
/*
   return 0: search the addr
   return 1: not find the addr 
   */
uint8_t freelist_addr_check(void *addr,char * func_str)
{
    char linebuf[256];
    FILE *fp;
    char path[128];
    char addr_string[32];
    sprintf(path, "%s.flog",func_str);
    remove(path);
    memtrace_heap_dump_freelist_to_file(path);
    sprintf(addr_string,"%p",addr);
    fp  = fopen(path, "r");
    if(fp == NULL){
        perror("fopen");		
        return 1;
    }
    while(fgets(linebuf,255,fp)){
        if( strstr(linebuf,addr_string)){			
            fclose(fp);
            return 0;
        }
    }
    fclose(fp);
    return 1;


}

/*
   return 0: search the addr
   return 1: not find the addr 
   */
uint8_t alloctree_addr_check(void *addr,uint32_t len,char * func_str)
{
    char linebuf[256];
    FILE *fp;
    char path[128];
    char start_string[32];
    char end_string[32];

    sprintf(path, "%s.log",func_str);
    memtrace_heap_dump_node_to_file(path);
    sprintf(start_string,"%p",addr);
    sprintf(end_string,"%p",addr+len);
    fp  = fopen(path, "r");
    if(fp == NULL){		
        perror("fopen");
        return 1;
    }

    //			printf("start_string:%s\n",start_string);
    //			printf("end_string:%s\n",end_string);
    while(fgets(linebuf,255,fp)){
        //		printf("linebuf:%s\n",linebuf);
        if( strstr(linebuf,start_string)&&strstr(linebuf,end_string)){			
            fclose(fp);
            return 0;
        }
    }
    fclose(fp);
    return 1;

}


TEST_RESULT_E  munmap_ok_test_trace(   )
{

    void* addr;
    uint32_t len = 12*4096*1024;
    addr = pi_mmap(NULL,len,  PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    pi_munmap(addr,len);
    if(!freelist_addr_check(addr,__func__)){		
        case_pass();
    }
    else{
        case_fail();
    }
}

TEST_RESULT_E  munmap_part_ok_test_trace(   )
{

    void *addr ;
    uint32_t len = 12*4096*1024;
    addr = pi_mmap(NULL,len,  PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    pi_munmap(addr,len/2);
    if(!freelist_addr_check(addr,__func__)&&!alloctree_addr_check(addr+len/2,len/2, __func__)){
        case_pass();
    }
    else{
        case_fail();
    }
}

TEST_RESULT_E  munmap_tail_part_ok_test_trace(   )
{

    void *addr ;
    uint32_t len = 12*4096*1024;
    addr = pi_mmap(NULL,len,  PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    pi_munmap(addr+len/2,len/2);
    if(!freelist_addr_check(addr+len/2,__func__)&&!alloctree_addr_check(addr,len/2, __func__)){
        case_pass();
    }
    else{
        case_fail();
    }


}



TEST_RESULT_E  munmap_split_ok_test_trace( )
{

    void *addr ;
    uint32_t len = 12*4096*1024;
    addr = pi_mmap(NULL,len,  PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    pi_munmap(addr+len/3,len/3);

    if(!freelist_addr_check(addr+len/3,__func__)&&!alloctree_addr_check(addr,len/3, __func__)
            &&!alloctree_addr_check(addr+((len*2)/3),len/3, __func__)){
        case_pass();
    }
    else{
        case_fail();
    }

}




TEST_RESULT_E  free_ok_test_trace(   )
{

    void* addr;
    void* addr_tmp ;
    addr_tmp = addr = pi_malloc(32);
    pi_free(addr);
    if(!freelist_addr_check(addr_tmp,__func__)){		
        case_pass();
    }
    else{
        case_fail();
    }

}



TEST_RESULT_E  mmap_ok_test_trace(   )
{

    void *addr ;
    uint32_t len = 12*4096*1024; 
    addr = pi_mmap(NULL, len,PROT_READ|PROT_WRITE,MAP_ANONYMOUS|MAP_SHARED,-1,0);
    if(!alloctree_addr_check(addr,len,__func__)){		
        case_pass();
    }
    else{
        case_fail();
    }
}




TEST_RESULT_E  malloc_ok_test_trace(   )
{

    void *addr ;
    addr = pi_malloc(32);
    if(!alloctree_addr_check(addr,32,__func__)){		
        case_pass();
    }
    else{
        case_fail();
    }
}

TEST_RESULT_E  zalloc_ok_test_trace(   )
{

    void *addr ;
    addr = pi_zalloc(32);
    if(!alloctree_addr_check(addr,32,__func__)){		
        case_pass();
    }
    else{
        case_fail();
    }


}

TEST_RESULT_E  realloc_ok_test_trace(   )
{

    char path[128];
    FILE *fp;
    char linebuf[256];
    void *addr ;
    char addr_string[32];
    addr = pi_malloc(32);
    addr= pi_realloc(addr, 64);

    if(!alloctree_addr_check(addr,64,__func__)){				
        case_pass();
    }else{	
        case_fail();
    }
}

case0_func case0_sets[]={
    realloc_ok_test_trace,  
    malloc_ok_test_trace,
    zalloc_ok_test_trace,
    free_ok_test_trace,
    mmap_ok_test_trace,
    munmap_ok_test_trace,
    munmap_part_ok_test_trace,
    munmap_tail_part_ok_test_trace,
    munmap_split_ok_test_trace,
};


uint8_t get_case_sets_num()
{    
    return  sizeof(case0_sets)/sizeof(case0_func);
}
