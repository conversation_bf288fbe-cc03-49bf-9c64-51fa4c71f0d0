/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ptm_base64.h
 * @addtogroup ptm_base64
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal base64 encode and decode interface set
 */
#ifndef __PTM_BASE64_H__
#define __PTM_BASE64_H__

#include <pol/pol_types.h>
#include <pol/pol_define.h>

PT_BEGIN_DECLS

/**
 * @brief  encode the string with base64
 * @param[in] input:        source string pointer
 * @param[in] input_strlen: source string length
 * @param[in] output:       point to encode output buffer
 * @param[in] output_buflen:encode output buffer size
 * @return encoded output string length
 * @retval -1:if fail \n
 *         >0:if sucess, return encoded output string length
 * <AUTHOR>
 * @data   2023-05-08
*/

int32_t base64_encode(const char *input, size_t input_strlen, char *output, size_t output_buflen);

/**
 * @brief  decode the base64 encoded string
 * @param[in] input:        base64 encoded string pointer
 * @param[in] input_strlen: encoded string length
 * @param[in] output:       point to decode output buffer
 * @param[in] output_buflen:decode output buffer size
 * @return decode output string length
 * @retval -1:if decode fail \n
 *         >0:if sucess, return decoded output string length
 * <AUTHOR>
 * @data   2023-05-08
*/

int32_t base64_decode(const char *input, size_t input_strlen, char *output, size_t output_buflen);

#if 0
/**
 * @brief  encode the string with base64
 * @param[in] input  : source string pointer
 * @param[in] input_length   :source string length
 * @param[out] output_length: point to encoding string length
 * @return encoded string pointer
 * @retval NULL:if encode fail \n
 *         !NULL:encoded string pointer
 * <AUTHOR>
 * @date   2023-05-06
*/
char* base64_encode(const char *input,size_t input_length,size_t *output_length);

/**
 * @brief  decode the base64 encoding string
 * @param[in] input  : base64 encoding string pointer
 * @param[in] input_length   :encoding string length
 * @param[out] output_length: point to decoding string length
 * @return decoding string pointer
 * @retval NULL:if decode fail \n
 *         !NULL:decoding string pointer
 * <AUTHOR>
 * @date   2023-05-06
*/
char* base64_decode(const char *input, size_t input_length, size_t *output_length);
#endif

/**
 * @brief  encode the file with base64
 * @param[in] inputFile  :  source file pointer
 * @param[out] outputFile: point to encoding file
 * @return encode result
 * @retval 0:if encode success\n
 *         -1:if encode fail
 * <AUTHOR>
 * @date   2023-05-06
*/

int32_t base64_encode_file(FILE* inputFile, FILE* outputFile);

/**
 * @brief  decode the base64 encoding file
 * @param[in] inputFile  :  base64 encoding  file pointer
 * @param[out] outputFile:  decoding file pointer
 * @return decode result
 * @retval 0:if decode success \n
 *         -1:if decode fail
 * <AUTHOR>
 * @date   2023-05-06
*/
int32_t base64_decode_file(FILE* inputFile, FILE* outputFile);

PT_END_DECLS
#endif
/**
 *@}
 */
