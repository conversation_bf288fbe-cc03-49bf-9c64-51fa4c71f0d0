/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netmodules.h
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network submodules interface declaration
 */
#ifndef __NETMODULES_H__
#define __NETMODULES_H__

#include "netctx.h"

#if CONFIG_SDK_PEDK
/**
 * @brief       Construct PEDK service.
 * @param[in]   obj     : The object pointer.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR>
 * @date        2024-02-02
 */
int32_t pedksrv_construct   (void* obj);
/**
 * @brief       Destruct PEDK service.
 * @param[in]   obj     : The object pointer.
 * <AUTHOR>
 * @date        2024-02-02
 */
void    pedksrv_destruct    (void* obj);
/**
 * @brief       Prolog of the PEDK service handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR>
 * @date        2024-2-23
 */
int32_t pedksrv_prolog      (NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the PEDK service handling thread.
 * <AUTHOR> Xin
 * @date        2024-2-23
 */
void    pedksrv_epilog      (void);
#else
#define pedksrv_construct(a)    ( { int32_t _rv = -1; NET_DEBUG("Unsupport PEDK service"); _rv; } ) ///< Only output a log when CONFIG_SDK_PEDK is not defined.
#define pedksrv_destruct(a)     ( { NET_DEBUG("Unsupport PEDK service"); } )                        ///< Only output a log when CONFIG_SDK_PEDK is not defined.
#define pedksrv_prolog(a)       ( { int32_t _rv = 0;  NET_DEBUG("Unsupport PEDK service"); _rv; } ) ///< Only output a log when CONFIG_SDK_PEDK is not defined.
#define pedksrv_epilog()        ( { NET_DEBUG("Unsupport PEDK service"); } )                        ///< Only output a log when CONFIG_SDK_PEDK is not defined.
#endif

#if CONFIG_SDK_EWS
/**
 * @brief       Construct PrintSystem SDK.
 * @param[in]   obj     : The object pointer.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
int32_t prnsdk_construct    (void* obj);
/**
 * @brief       Destruct PrintSystem SDK.
 * @param[in]   obj     : The object pointer.
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
void    prnsdk_destruct     (void* obj);
/**
 * @brief       Prolog of the PRNSDK(PrintSystem SDK) service handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2024-2-23
 */
int32_t prnsdk_prolog       (NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the PRNSDK(PrintSystem SDK) service handling thread.
 * <AUTHOR> Xin
 * @date        2024-2-23
 */
void    prnsdk_epilog       (void);
#else
#define prnsdk_construct(a)     ( { int32_t _rv = -1; NET_DEBUG("Unsupport PrintSystem SDK"); _rv; } ) ///< Only output a log when CONFIG_SDK_EWS is not defined.
#define prnsdk_destruct(a)      ( { NET_DEBUG("Unsupport PrintSystem SDK"); } )                        ///< Only output a log when CONFIG_SDK_EWS is not defined.
#define prnsdk_prolog(a)        ( { int32_t _rv = 0;  NET_DEBUG("Unsupport PrintSystem SDK"); _rv; } ) ///< Only output a log when CONFIG_SDK_EWS is not defined.
#define prnsdk_epilog()         ( { NET_DEBUG("Unsupport PrintSystem SDK"); } )                        ///< Only output a log when CONFIG_SDK_EWS is not defined.
#endif

#if CONFIG_NET_ESCLSRV
/**
 * @brief       Construct the eSCL service.
 * @param[in]   obj     : The object pointer.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
int32_t esclsrv_construct   (void* obj);
/**
 * @brief       Destruct the eSCL service.
 * @param[in]   obj     : The object pointer.
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
void    esclsrv_destruct    (void* obj);
/**
 * @brief       Prolog of the eSCL service handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t esclsrv_prolog      (NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the eSCL service handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    esclsrv_epilog      (void);
#else
#define esclsrv_construct(a)    ( { int32_t _rv = -1; NET_DEBUG("Unsupport eSCL service"); _rv; } ) ///< Only output a log when CONFIG_NET_ESCLSRV is not defined.
#define esclsrv_destruct(a)     ( { NET_DEBUG("Unsupport eSCL service"); } )                        ///< Only output a log when CONFIG_NET_ESCLSRV is not defined.
#define esclsrv_prolog(a)       ( { int32_t _rv = 0;  NET_DEBUG("Unsupport eSCL service"); _rv; } ) ///< Only output a log when CONFIG_NET_ESCLSRV is not defined.
#define esclsrv_epilog()        ( { NET_DEBUG("Unsupport eSCL service"); } )                        ///< Only output a log when CONFIG_NET_ESCLSRV is not defined.
#endif /* CONFIG_NET_ESCLSRV */

#if CONFIG_NET_IPPSRV
/**
 * @brief       Construct the IPP(Internet-Printing-Protocol) service.
 * @param[in]   obj     : The object pointer.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
int32_t ippsrv_construct    (void* obj);
/**
 * @brief       Destruct the IPP(Internet-Printing-Protocol) service.
 * @param[in]   obj     : The object pointer.
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
void    ippsrv_destruct     (void* obj);
/**
 * @brief       Prolog of the IPP(Internet-Printing-Protocol) service handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t ippsrv_prolog       (NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the IPP(Internet-Printing-Protocol) service handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    ippsrv_epilog       (void);
#else
#define ippsrv_construct(a)     ( { int32_t _rv = -1; NET_DEBUG("Unsupport IPP service"); _rv; } ) ///< Only output a log when CONFIG_NET_IPPSRV is not defined.
#define ippsrv_destruct(a)      ( { NET_DEBUG("Unsupport IPP service"); } )                        ///< Only output a log when CONFIG_NET_IPPSRV is not defined.
#define ippsrv_prolog(a)        ( { int32_t _rv = 0;  NET_DEBUG("Unsupport IPP service"); _rv; } ) ///< Only output a log when CONFIG_NET_IPPSRV is not defined.
#define ippsrv_epilog()         ( { NET_DEBUG("Unsupport IPP service"); } )                        ///< Only output a log when CONFIG_NET_IPPSRV is not defined.
#endif /* CONFIG_NET_IPPSRV */

/**
 * @brief       Construct the WEB service.
 * @param[in]   obj     : The object pointer.
 * @return      reply value
 * @retval      == 0    : success\n
 *              <  0    : fail
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
int32_t websrv_construct    (void* obj);
/**
 * @brief       Destruct the WEB service.
 * @param[in]   obj     : The object pointer.
 * <AUTHOR> Xin
 * @date        2024-02-02
 */
void    websrv_destruct     (void* obj);
/**
 * @brief       Prolog of the WEB service handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t websrv_prolog       (NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the WEB service handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    websrv_epilog       (void);

/**
 * @brief       Prolog of the TLS service handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t tlssrv_prolog       (NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the TLS service handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    tlssrv_epilog       (void);

#if CONFIG_NET_RAWPRINT
/**
 * @brief       Prolog of the RAWPrint(port default is 9100) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t rawprint_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the RAWPrint(port default is 9100) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    rawprint_epilog(void);
#else
#define rawprint_prolog(a)  ( { int32_t _rv = 0; NET_DEBUG("Unsupport RAW Print"); _rv; } ) ///< Only output a log when CONFIG_NET_RAWPRINT is not defined.
#define rawprint_epilog()   ( { NET_DEBUG("Unsupport RAW Print"); } )                       ///< Only output a log when CONFIG_NET_RAWPRINT is not defined.
#endif /* CONFIG_NET_RAWPRINT */

#if CONFIG_NET_RAWSCAN
/**
 * @brief       Prolog of the RAWScan(port default is 9200) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t rawscan_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the RAWScan(port default is 9200) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    rawscan_epilog(void);
#else
#define rawscan_prolog(a)   ( { int32_t _rv = 0; NET_DEBUG("Unsupport RAW Scan"); _rv; } )  ///< Only output a log when CONFIG_NET_RAWSCAN is not defined.
#define rawscan_epilog()    ( { NET_DEBUG("Unsupport RAW Scan"); } )                        ///< Only output a log when CONFIG_NET_RAWSCAN is not defined.
#endif /* CONFIG_NET_RAWSCAN */

#if CONFIG_NET_LPD
/**
 * @brief       Prolog of the LPD(Line-Printer-Daemon) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t lpd_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the LPD(Line-Printer-Daemon) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    lpd_epilog(void);
#else
#define lpd_prolog(a)       ( { int32_t _rv = 0; NET_DEBUG("Unsupport LPD"); _rv; } )       ///< Only output a log when CONFIG_NET_LPD is not defined.
#define lpd_epilog()        ( { NET_DEBUG("Unsupport LPD"); } )                             ///< Only output a log when CONFIG_NET_LPD is not defined.
#endif /* CONFIG_NET_LPD */

#if CONFIG_NET_WSD
/**
 * @brief       Prolog of the WSD handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t wsd_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the WSD handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    wsd_epilog(void);
#else
#define wsd_prolog(a)       ( { int32_t _rv = 0; NET_DEBUG("Unsupport WSD"); _rv; } )       ///< Only output a log when CONFIG_NET_WSD is not defined.
#define wsd_epilog()        ( { NET_DEBUG("Unsupport WSD"); } )                             ///< Only output a log when CONFIG_NET_WSD is not defined.
#endif /* CONFIG_NET_WSD */

#if CONFIG_NET_SLP
/**
 * @brief       Prolog of the SLP(Service-Location-Protocol) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t slp_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the SLP(Service-Location-Protocol) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    slp_epilog(void);
#else
#define slp_prolog(a)       ( { int32_t _rv = 0; NET_DEBUG("Unsupport SLP"); _rv; } )       ///< Only output a log when CONFIG_NET_SLP is not defined.
#define slp_epilog()        ( { NET_DEBUG("Unsupport SLP"); } )                             ///< Only output a log when CONFIG_NET_SLP is not defined.
#endif /* CONFIG_NET_SLP */

#if CONFIG_NET_BONJOUR
/**
 * @brief       Prolog of the Bonjour(mDNS) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t bonjour_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the Bonjour(mDNS) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    bonjour_epilog(void);
#else
#define bonjour_prolog(a)   ( { int32_t _rv = 0; NET_DEBUG("Unsupport Bonjour"); _rv; } )   ///< Only output a log when CONFIG_NET_BONJOUR is not defined.
#define bonjour_epilog()    ( { NET_DEBUG("Unsupport Bonjour"); } )                         ///< Only output a log when CONFIG_NET_BONJOUR is not defined.
#endif /* CONFIG_NET_BONJOUR */

#if CONFIG_NET_NETBIOS
/**
 * @brief       Prolog of the NetBIOS handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t netbios_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the NetBIOS handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    netbios_epilog(void);
#else
#define netbios_prolog(a)   ( { int32_t _rv = 0; NET_DEBUG("Unsupport NetBIOS"); _rv; } )   ///< Only output a log when CONFIG_NET_NETBIOS is not defined.
#define netbios_epilog()    ( { NET_DEBUG("Unsupport NetBIOS"); } )                         ///< Only output a log when CONFIG_NET_NETBIOS is not defined.
#endif /* CONFIG_NET_NETBIOS */

#if CONFIG_NET_LLMNR
/**
 * @brief       Prolog of the LLMNR(Link-Local-Multicast-Name-Resolution) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t llmnr_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the LLMNR(Link-Local-Multicast-Name-Resolution) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    llmnr_epilog(void);
#else
#define llmnr_prolog(a)     ( { int32_t _rv = 0; NET_DEBUG("Unsupport LLMNR"); _rv; } )     ///< Only output a log when CONFIG_NET_LLMNR is not defined.
#define llmnr_epilog()      ( { NET_DEBUG("Unsupport LLMNR"); } )                           ///< Only output a log when CONFIG_NET_LLMNR is not defined.
#endif /* CONFIG_NET_LLMNR */

#if CONFIG_NET_SNMP
/**
 * @brief       Prolog of the SNMP(Simple-Network-Management-Protocol) handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t snmp_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the SNMP(Simple-Network-Management-Protocol) handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    snmp_epilog(void);
#else
#define snmp_prolog(a)      ( { int32_t _rv = 0; NET_DEBUG("Unsupport SNMP"); _rv; } )      ///< Only output a log when CONFIG_NET_SNMP is not defined.
#define snmp_epilog()       ( { NET_DEBUG("Unsupport SNMP"); } )                            ///< Only output a log when CONFIG_NET_SNMP is not defined.
#endif /* CONFIG_NET_SNMP */

#if CONFIG_NET_SMTP
/**
 * @brief       Prolog of the SMTP(Simple-Mail-Transfer-Protocol) client handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t smtp_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the SMTP(Simple-Mail-Transfer-Protocol) client handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    smtp_epilog(void);
#else
#define smtp_prolog(a)      ( { int32_t _rv = 0; NET_DEBUG("Unsupport SMTP"); _rv; } )      ///< Only output a log when CONFIG_NET_SMTP is not defined.
#define smtp_epilog()       ( { NET_DEBUG("Unsupport SMTP"); } )                            ///< Only output a log when CONFIG_NET_SMTP is not defined.
#endif /* CONFIG_NET_SMTP */

#if CONFIG_NET_FTP
/**
 * @brief       Prolog of the FTP(File-Transfer-Protocol) client handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t ftp_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the FTP(File-Transfer-Protocol) client handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    ftp_epilog(void);
#else
#define ftp_prolog(a)       ( { int32_t _rv = 0; NET_DEBUG("Unsupport FTP"); _rv; } )       ///< Only output a log when CONFIG_NET_FTP is not defined.
#define ftp_epilog()        ( { NET_DEBUG("Unsupport FTP"); } )                             ///< Only output a log when CONFIG_NET_FTP is not defined.
#endif /* CONFIG_NET_FTP */

#if CONFIG_NET_PORT9120
/**
 * @brief       Prolog of the PORT9120 handling thread.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Prolog result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t port9120_prolog(NET_CTX_S* net_ctx);
/**
 * @brief       Epilog of the PORT9120 handling thread.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    port9120_epilog(void);
#else
#define port9120_prolog(a)  ( { int32_t _rv = 0; NET_DEBUG("Unsupport PORT9120"); _rv; } )  ///< Only output a log when CONFIG_NET_PORT9120 is not defined.
#define port9120_epilog()   ( { NET_DEBUG("Unsupport PORT9120"); } )                        ///< Only output a log when CONFIG_NET_PORT9120 is not defined.
#endif /* CONFIG_NET_PORT9120 */

/**
 * @brief       Register ACL command abort network.
 * @param[in]   net_ctx : The network context pointer.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    netacl_init (NET_CTX_S* net_ctx);

/**
 * @brief       Register ACL-OID command abort network.
 * @param[in]   net_ctx : The network context pointer.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    netoid_init (NET_CTX_S* net_ctx);

/**
 * @brief       Register CMD command abort network.
 * @param[in]   net_ctx : The network context pointer.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void    netcmd_init (NET_CTX_S* net_ctx);

#endif /* __NETMODULES_H__ */
/**
 *@}
 */
