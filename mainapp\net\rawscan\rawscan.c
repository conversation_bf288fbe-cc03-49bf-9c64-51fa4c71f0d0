/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file rawscan.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief RAWScan base on Pantum PJL
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"

#define RAW_LOCAL_PORT  ( (uint16_t)(netdata_get_rawscan_switch(DATA_MGR_OF(s_raws_ctx)) ? netdata_get_rawscan_port(DATA_MGR_OF(s_raws_ctx)) : 0) )

typedef struct rawscan_task
{
    NET_CONN_S*         rawconn;    ///< The NET_CONN_S object pointer of RAWScan connection.
    QIO_S*              pqiotcp;    ///< The QIO_S object pointer of TCP stream.
    uint8_t             working;    ///< The RAWScan task working flag.
}
RAW_TASK_S;

typedef struct rawscan_ctx
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         srv_tid;
    int32_t             pfd[2];
}
RAWSCAN_CTX_S;

static RAWSCAN_CTX_S*   s_raws_ctx = NULL;

/**
 * @brief       The callback function of QIO_CLOSE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current RAWScan task.
 * @return      Close result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawscan_task_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);

    RETURN_VAL_IF(ptask == NULL || ptask->rawconn == NULL, NET_WARN, -1);

    NET_DEBUG("QIO_CLOSE for client(%s : %u)", ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
    ptask->working = 0;

    return 0;
}

/**
 * @brief       The callback function of QIO_READABLE(pqio) or QIO_WRITEABLE(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current RAWScan task.
 * @return      Poll result
 * @retval      > 0     : this QIO_S object can be read or written\n
 *              ==0     : timeout\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawscan_task_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);
    int32_t rv = -1;

    RETURN_VAL_IF(ptask == NULL || ptask->rawconn == NULL || ptask->pqiotcp == NULL, NET_WARN, -1);

    if ( netdata_get_iface_running(DATA_MGR_OF(s_raws_ctx), ptask->rawconn->ifid) )
    {
        if ( what & QIO_POLL_READ )
        {
            rv = QIO_READABLE(ptask->pqiotcp, tos, tous);
            if ( rv > 0 )
            {
                rv = netsock_peek_readable_number(ptask->rawconn->sockfd);
                if ( rv <= 0 )
                {
                    NET_DEBUG("connection has been closed from client(%s : %u)", ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
                    rv = -1;
                }
            }
        }
        else if ( what & QIO_POLL_WRITE )
        {
            rv = QIO_WRITEABLE(ptask->pqiotcp, tos, tous);
        }
        else
        {
            NET_WARN("unsupported operation(%d) from client(%s : %u)", what, ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
        }
    }
    else
    {
        NET_DEBUG("network link(%s) is disconnected from client(%s : %u)", IFACE_NAME(ptask->rawconn->ifid), ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
    }

    if ( rv < 0 )
    {
        NET_DEBUG("expect(%d, %d, %d) result(%d) from client(%s : %u)", what, tos, tous, rv, ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
    }
    return rv;
}

/**
 * @brief       The callback function of QIO_READ(pqio), read data from pqiotcp.
 * @param[in]   pqio    : The QIO_S object pointer for current RAWScan task.
 * @return      Read result
 * @retval      >=0     : read the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawscan_task_read(QIO_S* pqio, void* buf, size_t count)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);
    int32_t rv;

    RETURN_VAL_IF(ptask == NULL || ptask->pqiotcp == NULL, NET_WARN, -1);

    rv = QIO_READ(ptask->pqiotcp, buf, count);
    if ( rv <= 0 )
    {
        NET_DEBUG("expect(%u) result(%d) from client(%s : %u)", count, rv, ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
    }
    return rv;
}

/**
 * @brief       The callback function of QIO_WRITE(pqio), write data to TCP stream.
 * @param[in]   pqio    : The QIO_S object pointer for current RAWScan task.
 * @return      Write result
 * @retval      >=0     : write the length of the data from this QIO_S object\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawscan_task_write(QIO_S* pqio, void* buf, size_t count)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);
    int32_t rv;

    RETURN_VAL_IF(ptask == NULL || ptask->pqiotcp == NULL, NET_WARN, -1);

    rv = QIO_WRITE(ptask->pqiotcp, buf, count);
    if ( rv <= 0 )
    {
        NET_DEBUG("expect(%u) result(%d) from client(%s : %u)", count, rv, ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
    }
    return rv;
}

/**
 * @brief       The callback function of QIO_SEEK(pqio).
 * @param[in]   pqio    : The QIO_S object pointer for current RAWScan task.
 * @return      Seek result
 * @retval      =+0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t rawscan_task_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    DECL_PRIV(pqio, RAW_TASK_S, ptask);
    int32_t rv;

    RETURN_VAL_IF(ptask == NULL || ptask->pqiotcp == NULL, NET_WARN, -1);

    rv = QIO_SEEK(ptask->pqiotcp, offset, whence);
    if ( rv <= 0 )
    {
        NET_DEBUG("expect(%d, %d) result(%d) from client(%s : %u)", offset, whence, rv, ptask->rawconn->remote_addr, ptask->rawconn->remote_port);
    }
    return rv;
}

/**
 * @brief       The threads pool handling function for once connect to the RAWScan port.
 * @param[in]   arg     : context(NET_CONN_S object pointer).
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void rawscan_connection(void *arg)
{
    NET_CONN_S*     pnc = (NET_CONN_S *)arg;
    JOB_REQUEST_S*  pjobreq = NULL;
    RAW_TASK_S*     ptask = NULL;
    QIO_S*          pqio = NULL;
    ROUTER_MSG_S    sendmsg;

    do
    {
        BREAK_IF(netsock_peek_connection(pnc, 30) <= 0, NET_TRACE);

        /* 获取本地IP地址，并查到对应的网卡接口 */
        if ( pnc->ipver == IPV6 )
        {
            pnc->ifid = netdata_get_ifid_by_ipv6_addr(DATA_MGR_OF(s_raws_ctx), pnc->local_addr);
        }
        else
        {
            pnc->ifid = netdata_get_ifid_by_ipv4_addr(DATA_MGR_OF(s_raws_ctx), pnc->local_addr);
        }
        NET_DEBUG("connection using interface[%d](%s) from client(%s : %u)", pnc->ifid, IFACE_NAME(pnc->ifid), pnc->remote_addr, pnc->remote_port);
        BREAK_IF(pnc->ifid < IFACE_ID_ETH || pnc->ifid >= IFACE_ID_NUM, NET_WARN);

        ptask = (RAW_TASK_S *)pi_zalloc(sizeof(RAW_TASK_S));
        BREAK_IF(ptask == NULL, NET_WARN);

        ptask->pqiotcp = qio_tcp_create(pnc->sockfd, NETQIO_SOCK_NOCLOSE); /* QIO_CLOSE时只释放自身资源，socket句柄随NET_CONN_S关闭 */
        BREAK_IF(ptask->pqiotcp == NULL, NET_WARN);

        ptask->rawconn = pnc;
        ptask->working = 1;

        pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
        BREAK_IF(pqio == NULL, NET_WARN)

        pqio->close = rawscan_task_close;
        pqio->poll  = rawscan_task_poll;
        pqio->read  = rawscan_task_read;
        pqio->write = rawscan_task_write;
        pqio->seek  = rawscan_task_seek;
        pqio->priv  = (void *)ptask;

        pjobreq = (JOB_REQUEST_S *)pi_zalloc(sizeof(JOB_REQUEST_S));
        BREAK_IF(pjobreq == NULL, NET_WARN);

        pjobreq->io_class   = IO_CLASS_SCAN;
        pjobreq->io_via     = ((pnc->ifid == IFACE_ID_ETH) ? IO_VIA_NET : IO_VIA_WIFI);
        pjobreq->pqio       = pqio;

        NET_DEBUG("send task to MID_SYS_JOB_MGR from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
        sendmsg.msgType     = MSG_CTRL_JOB_REQUEST;
        sendmsg.msg1        = 0;
        sendmsg.msg2        = 0;
        sendmsg.msg3        = (void *)pjobreq;
        sendmsg.msgSender   = MID_PORT_NET;
        task_msg_send_by_router(MID_SYS_JOB_MGR, &sendmsg);

        while ( ptask->working )
        {
            if ( pi_time(NULL) % 10 == 0 )
            {
                NET_DEBUG("waiting RAWScan job end from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
            }
            pi_msleep(1000);
        }
        NET_DEBUG("RAWScan job end from client(%s : %u)", pnc->remote_addr, pnc->remote_port);
    }
    while ( 0 );

    if ( pqio != NULL )
    {
        pqio->priv = NULL;
        pi_free(pqio);
    }
    if ( ptask != NULL )
    {
        if ( ptask->pqiotcp != NULL )
        {
            QIO_CLOSE(ptask->pqiotcp);
            ptask->pqiotcp = NULL;
        }
        pi_free(ptask);
    }
    netsock_close_connection(pnc);
}

/**
 * @brief       RAWScan handling thread.
 * @param[in]   arg     : context(NULL).
 * @return      Thread handling result
 * @retval      NULL    : success.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void* rawscan_thread_handler(void* arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    int32_t         pipefd = s_raws_ctx->pfd[0];
    int32_t         update = 0;
    int32_t         status;
    int32_t         max_fd;

    while ( 1 )
    {
        FD_ZERO(&rfds);
        FD_SET(pipefd, &rfds);
        max_fd = pipefd;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, RAW_LOCAL_PORT, 1);
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        if ( FD_ISSET(pipefd, &rfds) )
        {
            read(pipefd, &status, sizeof(status));
            NET_DEBUG("recv(0x%X), reload service socket", status);
            update = 1;
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    NET_DEBUG("new connection %d from %s : %u to %u", pnc->sockfd, pnc->remote_addr, pnc->remote_port, pnc->local_port);
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_raws_ctx), rawscan_connection, pnc) < 0 )
                    {
                        NET_WARN("add rawscan_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, reload current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}

/**
 * @brief       The callback function when the subject notify.
 * @param[in]   o       : observer object pointer.
 * @param[in]   s       : subject object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static void rawscan_update_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_raws_ctx == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_RAWSCAN )
    {
        write(s_raws_ctx->pfd[1], &(s->subject_status), sizeof(s->subject_status));
    }
}

int32_t rawscan_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_raws_ctx != NULL, NET_WARN, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_raws_ctx = (RAWSCAN_CTX_S *)pi_zalloc(sizeof(RAWSCAN_CTX_S));
    RETURN_VAL_IF(s_raws_ctx == NULL, NET_WARN, -1);

    do
    {
        s_raws_ctx->net_ctx = net_ctx;
        pipe(s_raws_ctx->pfd);

        s_raws_ctx->srv_tid = pi_thread_create(rawscan_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "rawscan_thread_handler");
        BREAK_IF(s_raws_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("RAWScan initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netport_observer(net_ctx, rawscan_update_callback, NULL);
    }
    else
    {
        rawscan_epilog();
    }
    return ret;
}

void rawscan_epilog(void)
{
    if ( s_raws_ctx != NULL )
    {
        if ( s_raws_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_raws_ctx->srv_tid);
        }
        pi_close(s_raws_ctx->pfd[0]);
        pi_close(s_raws_ctx->pfd[1]);
        pi_free(s_raws_ctx);
        s_raws_ctx = NULL;
    }
}
/**
 *@}
 */
