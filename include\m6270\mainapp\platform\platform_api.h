/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file platform_api.h
 * @addtogroup platform
 * @{
 * @addtogroup platform_api
 * <AUTHOR>
 * @date 2023-07-17
 * @brief provide some common printer proterty information and interface functions\n
 *        for getting properties
 */

#ifndef PLATFORMAPI_H
#define PLATFORMAPI_H

#include <stdint.h>
#include "location_api.h"

#define IN_SECURITY 0
#define IN_XC 0
#define IN_JFJ 0
#define IN_PORT_CTRL 0
#define IN_FAX 0
#define IN_NETWORK 1

#ifdef __cplusplus
extern "C" {
#endif

/*7265 V1R2*/
#define  MACHINE_TYPE_CM805ADN   0xDD954107                   ///< machine type id cm805adn
#define  MACHINE_TYPE_BM805ADN   0x1DD54107                   ///< machine type id bm805adn
#define  MACHINE_TYPE_CM605ADN   0x9D52D07                    ///< machine type id cm605adn
#define  MACHINE_TYPE_BM605ADN   0x49952D87                   ///< machine type id bm605adn

#define  PID_TYPE_CM805ADN       0xA31F                       ///< cm805adn pid
#define  PID_TYPE_BM805ADN       0xA320                       ///< bm805adn pid
#define  PID_TYPE_CM605ADN       0xA321                       ///< cm605adn pid
#define  PID_TYPE_BM605ADN       0xA322                       ///< bm605adn pid
/*7265 V1R2*/

/*Kanas V2R12*/
#define  MACHINE_TYPE_CM9708DN   0x5DD54147                   ///< machine type id CM9708DN
#define  MACHINE_TYPE_CM9108DN   0x89952DC7                   ///< machine type id CM9108DN
#define  MACHINE_TYPE_M9708DN    0x9DD54187                   ///< machine type id M9708DN
#define  MACHINE_TYPE_M9108DN    0xC9952D07                   ///< machine type id M9108DN
#define  MACHINE_TYPE_CM9108DW   0x89952DC7                   ///< machine type id CM9108DW
#define  MACHINE_TYPE_M9108DW    0xC9952D07                   ///< machine type id M9108DW

#define  PID_TYPE_CM9708DN       0xA31C                       ///< pid CM9708DN
#define  PID_TYPE_CM9108DN       0xA31B                       ///< pid CM9108DN
#define  PID_TYPE_M9708DN        0xA31E                       ///< pid M9708DN
#define  PID_TYPE_M9108DN        0xA31D                       ///< pid M9108DN
#define  PID_TYPE_CM9108DW       0xA32F                       ///< pid CM9108DW
#define  PID_TYPE_M9108DW        0xA330                       ///< pid M9108DW
/*Kanas V2R12*/

/*7265 V1R3 XC*/
#define  MACHINE_TYPE_CM9705DN_PLUS   0xDD554147              ///< machine type id CM9705DN Plus
#define  MACHINE_TYPE_CM9105DN_PLUS   0xC9552D47              ///< machine type id CM9105DN Plus
#define  MACHINE_TYPE_M9705DN_PLUS    0x1D554187              ///< machine type id M9705DN PlusS
#define  MACHINE_TYPE_M9105DN_PLUS    0x9952D47               ///< machine type id M9105DN PlusS

#define  PID_TYPE_CM9705DN_PLUS       0xA332                  ///< pid CM9705DN PlusS
#define  PID_TYPE_CM9105DN_PLUS       0xA331                  ///< pid CM9105DN Plus
#define  PID_TYPE_M9705DN_PLUS        0xA334                  ///< pid M9705DN Plus
#define  PID_TYPE_M9105DN_PLUS        0xA333                  ///< pid M9105DN Plus
/*7265 V1R3 XC*/

/*7265 ELS*/
#define  MACHINE_TYPE_CM600ADN   0x49D52D47                    ///< machine type id CM600ADN
#define  MACHINE_TYPE_CM800ADN   0x5D954187                   ///< machine type id CM800ADN
#define  MACHINE_TYPE_BM600ADN   0x89D52D87                   ///< machine type id BM600ADN
#define  MACHINE_TYPE_BM800ADN   0x5D5541C7                   ///< machine type id BM800ADN

#define  PID_TYPE_CM600ADN       0xA335                       ///< pid CM600ADN
#define  PID_TYPE_CM800ADN       0xA336                       ///< pid CM800ADN
#define  PID_TYPE_BM600ADN       0xA337                       ///< pid BM600ADN
#define  PID_TYPE_BM800ADN       0xA338                       ///< pid BM800ADN
/*7265 ELS*/

#ifndef OPTION
#define OPTION 2                                              ///< enable or disable
#endif
#ifndef ENABLE
#define ENABLE 1                                              ///< enable
#endif
#ifndef DISABLE
#define DISABLE 0                                             ///< disable
#endif

#define NVLEN_VERSION           16                            ///< short version length
#define NVLEN_LVERSION          32                            ///< long version length
#define NVLEN_1284_STRING       1024                          ///< 1284 string length
#define NVLEN_PROD_SER_NUM      16                            ///< product serial number length
#define NVLEN_USB_MFGSTR        32                            ///< usb mfg string length
#define NVLEN_PRINTER_NAMESTR   32                            ///< printer name string length
#define NVLEN_CID_STR           64                            ///< cid string length
#define NVLEN_PRINTER_LOCATION  (63*3 + 1)                    ///< printer location length
#define NVLEN_PRODUCT_INFO      32                            ///< product information length
#define NVLEN_CONTACT_INFO      (31*3 + 1)                    ///< contact information length
#define NVLEN_UUID_LEN          128                           ///< uuid name length
#define NVLEN_UNIQUE_NAME       NVLEN_PRINTER_NAMESTR + 32    ///< unique name length
#define NVLEN_SYSTEM_TIME       32                            ///< system time length
#define MFG_NAME_LEN            16                            ///< manufacturer name length
#define SERIES_NAME_LEN         32                            ///< serial name length
#define PDT_NAME_LEN            32                            ///< product name length
#define SET_SYSTEM_TIME_LEN     21                            ///< set system time length
#define PANEL_BOARD_SERIAL_NUMBER_LEN 10                      ///< panel serial number length

/*
 *@brief The IO Timeout value is in seconds
 */
#define DEFAULT_IO_TIMEOUT      120                           ///< default io timeout
#define MIN_IO_TIMEOUT          20                            ///< min io timeout
#define MAX_IO_TIMEOUT          600                           ///< max io timeout

/*
 *@brief These values will almost certainly need to change for each platform
 */
#define DEFAULT_LOCATION        ""                            ///< default location
#define DEFAULT_PRODUCT_DATE    "2016.1.1"                    ///< default product date
#define DEFAULT_BATCH_NUMBER    "40000000"                    ///< default datch number
#define DEFAULT_CONTACT_INFO    ""                            ///< default contact info
#define DEFAULT_PROPERTY_NUMBER ""                            ///< default property number
#define DEFAULT_TIMEZONE        UTC_E_08_00                   ///< default time zone
#define DEFAULT_MFG_STRING      "Pantum"                      ///< default manufacturer string
#define DEFAULT_SER_NUM_STRING  "AA2A000000"                  ///< default serial number string
#define DEFAULT_VENDOR_ID       0x232B                        ///< default vendor id(vid)
#define DEFAULT_AUTO_SHUTDOWN_TIME    4                       ///< default auto shutdown time

#define PANEL_TEST_MODE_LED            0x0001                 ///< led panel
#define PANEL_TEST_MODE_DISPLAY        0x0002                 ///< display panel
#define PANEL_TEST_MODE_KEY            0x0004                 ///< key panel
#define PANEL_TEST_MODE_TOUCH_CLICK    0x0008                 ///< touch click panel
#define PANEL_TEST_MODE_TOUCH_SWIPE    0x0008                 ///< touch swipe panel

/*
 *@brief scan type
*/
typedef enum
{
    SCAN_TYPE_NONE  = 0,                                      ///< none-normal
    SCAN_TYPE_FB    = 1,                                      ///< FB
    SCAN_TYPE_ADF   = 2,                                      ///< ADF
    SCAN_TYPE_DADF  = 3,                                      ///< DADF
    SCAN_TYPE_RADF  = 4,                                      ///< RADF
}SCAN_TYPE_E;

/*
 *@brief screen type
*/
typedef enum
{
    SCREEN_TYPE_LED  = 0,                                     ///< led
    SCREEN_TYPE_LCD  = 1,                                     ///< lcd
    SCREEN_TYPE_TOUCH  = 2,                                   ///< touch
}SCREEN_TYPE_E;

/*
 *@brief print languae type
*/
typedef enum
{
    PRINT_LANGUAGE_GDI = 0,                                   ///< GDI
    PRINT_LANGUAGE_IPS = 1,                                   ///< IPS
    PRINT_LANGUAGE_NULL = 0xFF
}PRINT_LANGUAGE_E;

/*
 *@brief MONO or COLOR print type
*/
typedef enum
{
    MONO_PRINT = 0,
    COLOR_PRINT = 1
}PRINT_COLOR_E;

/*
 *@brief time zone
*/
typedef enum
{
    UTC_W_12_00 = 0,
    UTC_W_11_00,
    UTC_W_10_00,
    UTC_W_09_30,
    UTC_W_09_00,
    UTC_W_08_00,
    UTC_W_07_00,
    UTC_W_06_00,
    UTC_W_05_00,
    UTC_W_04_30,
    UTC_W_04_00,
    UTC_W_03_30,
    UTC_W_03_00,
    UTC_W_02_00,
    UTC_W_01_00,
    UTC_M_00_00,
    UTC_E_01_00,
    UTC_E_02_00,
    UTC_E_03_00,
    UTC_E_03_30,
    UTC_E_04_00,
    UTC_E_04_30,
    UTC_E_05_00,
    UTC_E_05_30,
    UTC_E_05_45,
    UTC_E_06_00,
    UTC_E_06_30,
    UTC_E_07_00,
    UTC_E_08_00,
    UTC_E_09_00,
    UTC_E_09_30,
    UTC_E_10_00,
    UTC_E_10_30,
    UTC_E_11_00,
    UTC_E_11_30,
    UTC_E_12_00,
    UTC_E_12_45,
    UTC_E_13_00,
    UTC_E_14_00
}E_TIME_ZONE_UTC_E;

/*
 *@brief auto shutdown time type
*/
typedef enum
{
    AUTO_SHUTDOWN_TIME_NEVER   = 0xFF,
    AUTO_SHUTDOWN_TIME_NOW     = 0,
    AUTO_SHUTDOWN_TIME_2HOURS  = 2,
    AUTO_SHUTDOWN_TIME_4HOURS  = 4,
    AUTO_SHUTDOWN_TIME_8HOURS  = 8,
    AUTO_SHUTDOWN_TIME_24HOURS = 24,
}AUTO_SHUTDOWN_TIME_E;

/*
 *@brief some machine configs which are recorded in the machine_config.ini
*/
typedef struct {
    uint32_t machine_tag;                           ///< machine type id
    char     mfg_name[MFG_NAME_LEN];                ///< manufacturer name
    char     series_name[SERIES_NAME_LEN];          ///< series name
    char     pdt_name[PDT_NAME_LEN];                ///< product name
    uint16_t pid;                                   ///< pid
    uint8_t  speed;                                 ///< speed
    uint8_t  color;                                 ///< color
    uint8_t  duplex_enable;                         ///< duplex enable
    uint8_t  wired_enable;                          ///< wired enable
    uint8_t  wireless_enable;                       ///< wireless enable
    uint8_t  print_language;                        ///< print language
    uint8_t  screen_type;                           ///< screen type
    uint8_t  expand_tray_number;                    ///< expand tray number
    uint8_t  usbhttp_enable;                        ///< usbhttp enable
    uint8_t  print_enable;                          ///< print enable
    uint8_t  scan_enable;                           ///< scan enable
    uint8_t  copy_enable;                           ///< copy enable
    uint8_t  fax_enable;                            ///< fax enable
    uint8_t  scan_type;                             ///< scan enable
    uint8_t  print_rk_mode;                         ///< print red and black mode
    uint8_t  machine_country_code;                  ///< machine ini country_code
} MACHINE_CONFIG_S;

/*
 *@brief some machine configs which are recorded in config tables
*/
typedef struct {
    char     firmware_version[NVLEN_VERSION];       ///< firmware version
    char     boot_version[NVLEN_VERSION];           ///< boot version
    char     kernel_version[NVLEN_VERSION];         ///< kernel version
    char     rootfs_version[NVLEN_VERSION];         ///< rootfs version
    char     engine_version[NVLEN_LVERSION];         ///< engine version

    uint32_t auto_shutdown_enable;                  ///< automatic shutdown enable
    uint32_t auto_shutdown_time;                    ///< automatic shutdown time
    uint32_t auto_shutdown_condition;               ///< automatic shutdown condition
    uint32_t power_on_count;                        ///< power on count
    uint32_t system_volume;                         ///< system volume

    uint32_t power_sleep_time;                      ///< power sleep time
    uint16_t firmware_region_code;                  ///< firmware region code
    uint16_t firmware_country_code;                 ///< firmware country code
    uint16_t system_language_code;                  ///< system languge code

    uint32_t quiet;                                 ///< quiet mode

    uint8_t  restore_flag;                          ///< factory reset flag
    uint8_t  timezone;                              ///< time zone
    uint32_t AirPrintEn;                            ///< air print enable
    uint32_t AcrFactory;                            ///< ACR factory
    uint32_t calibration;                           ///< calibration

    char     product_serial_number[NVLEN_PROD_SER_NUM];     ///< product serial number
    char     printer_location[NVLEN_PRINTER_LOCATION];      ///< printer location
    char     product_date[NVLEN_PRODUCT_INFO];              ///< product date
    char     batch_number[NVLEN_PRODUCT_INFO];              ///< batch number
    char     contact_info[NVLEN_CONTACT_INFO];              ///< contact information
    char     property_number[NVLEN_CONTACT_INFO];           ///< property number
    char     panel_fw_version[NVLEN_VERSION];               ///< panel firmware version
    char     system_time[SET_SYSTEM_TIME_LEN];              ///< system time
#if	IN_PORT_CTRL
    uint32_t storage_clear;                                 ///< storage clear
    uint32_t port_limit_usb;                                ///< port limit usb
    uint32_t delay_cancel_time_count;                       ///< delay cancel time count
#endif
    uint32_t vid;
    char     product_1284_string[NVLEN_1284_STRING];        ///< product 1284 string
    char     product_fax_1284_string[NVLEN_1284_STRING];    ///< product fax 1284 string
    char     printer_cid_string[NVLEN_CID_STR];             ///< printer cid
    uint32_t io_timeout;                                    ///< io timeout
    char     unique_name[NVLEN_UNIQUE_NAME];                ///< unique name
    char     calibration_qrcode[NVLEN_CONTACT_INFO];        ///< calibration info
    uint16_t system_energy_code;                            ///< system energy code
    uint32_t TR2ModeSet;                                    ///< tr2 mode set
    uint32_t udisk_enable;                                  ///< udisk enable
    uint32_t fw_upgrade_way;                                ///< firmware upgrade way
    uint32_t exit_tray_count;                               ///< count of the current exit tray
} CONFIG_TABLE_VARS_S;

int32_t pi_platform_set_system_reboot(uint32_t data);


/**
 * @brief platform restore factory default
 * @param[in] index restore factory default index
 * @return PLATFPORM_OK or PLATFORM_ERR
 * @retval PLATFPORM_OK restore factory default value successfully
 * @retval PLATFPORM_ERR restore factory default value failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platvars_restore_factory_default(uint32_t index);
/**
 * @brief platform set serial number
 * @param[in] data serial number
 * @param[in] len data length
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK set successfully
 * @retval PLATFPORM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_serial_number(char *data, int32_t len);

/**
 * @brief platform get product serial number
 * @param[in] data product serial number
 * @param[in] len data length
 * @return PLATFPORM_OK
 * @retval PLATFPORM_OK get
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_serial_number(char *data, int32_t len);

/**
 * @brief platform set firmware country code
 * @param[in] data firmware country code
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK set successfully
 * @retval PLATFPORM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_countrycode(uint16_t data);

/**
 * @brief platform get firmware country code
 * @param[in] data firmware country code
 * @return PLATFPORM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17

 */
int32_t pi_platform_get_countrycode(uint16_t *data);

/**
 * @brief platform set machine type
 * @param[in] machine_type machine type
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK set successfully
 * @retval PLATFPORM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_machine_type(uint32_t machine_type);

/**
 * @brief platform get machine type
 * @param[in] machine_type machine type
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK get successfully
 * @retval PLATFPORM_ERR get failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_machine_type(uint32_t *machine_type);
//

/**
 * @brief platform set auto shutdown enable
 * @param[in] data auto shutdown enable
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK set successfully
 * @retval PLATFPORM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_auto_shutdown_enable(uint32_t data);

/**
 * @brief platform get auto shutdown enable
 * @param[in] data  auto shutdown enable
 * @retval PLATFORM_OK get auto shutdown enable
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_auto_shutdown_enable(uint32_t *data);

/**
 * @brief platform set auto shutdown time
 * @param[in] data auto shutdown time
 * @return PLATFPROM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_auto_shutdown_time(uint32_t data);

/**
 * @brief platform get auto shutdown time
 * @param[in] data  auto shutdown time
 * @retval PLATFRORM_OK get auto shutdown time
 * @return PLATFPORM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_auto_shutdown_time(uint32_t *data);

/**
 * @brief platform set auto shutdown condition
 * @param[in] data auto shutdown condition
 * @return PLATFPROM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_auto_shutdown_condition(uint32_t data);

/**
 * @brief platform get auto shutdown condition
 * @param[in] data  auto shutdown condition
 * @retval PLATFROM_OK get auto shutdown condition
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_auto_shutdown_condition(uint32_t *data);

/**
 * @brief platform get quiet mode
 * @param[in] data quiet mode
 * @retval PLATFRORM_OK get quiet mode enable
 * @return PLATFPORM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_quiet(uint32_t *data);

/**
 * @brief platform set quiet mode
 * @param[in] data auto quiet mode
 * @return PLATFPROM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_quiet(uint32_t data);

/**
 * @brief platform set calibration progress
 * @param[in] data auto calibration information
 * @return PLATFPROM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platvars_set_calibration_progress(uint32_t flag);

/**
 * @brief platform get firmware version
 * @param[in] data firmware version
 * @param[in] len data length
 * @return PLATFPORM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_firmware_version(char *data, int32_t len);

/**
 * @brief platform get engine firmware version
 * @param[in] data engine firmware version
 * @param[in] len data length
 * @return PLATFPoRM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_engine_firmware_version(char *data, int32_t len);

/**
 * @brief platform get panel firmware version
 * @param[in] data  panel firmware version
 * @param[in] len data length
 * @return PLATFPRM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_panel_fw_version(char *data, int32_t len);

/**
 * @brief platform get printer name string
 * @param[in] data  product name
 * @param[in] len data length
 * @return PLATFPRM_OK
 * @retval PLATFPORM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_print_name_string(char *data, int32_t len);

/**
 * @brief platform set system time
 * @param[in] index
 * @param[in] data  system time
 * @param[in] len data length
 * @return PLATFPROM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
 int32_t pi_platform_set_system_time(uint32_t index, char *data, int32_t len);

/**
 * @brief platform get system time
 * @param[in] index 1:get the new data, other: do nothing
 * @param[in] data  system time
 * @param[in] len data length
 * @return PLATFPRM_OK
 * @retval PLATFPROM_OK set successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
 int32_t pi_platform_get_system_time(uint32_t index, char *data, int32_t len);

/**
 * @brief platform get product date
 * @param[in] data  product date
 * @param[in] len data length
 * @return PLATFPRM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_product_date(char *data, int32_t len);

/**
 * @brief platform get product date
 * @param[in] data  product date
 * @param[in] len data length
 * @return PLATFPRM_OK
 * @retval PLATFPROM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_product_date(char *data, int32_t len);

/**
 * @brief platform get kernel version
 * @param[in] data kernel version
 * @param[in] len data length
 * @return PLATFPRM_OK
 * @retval PLATFPROM_OK get successfully
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_kernel_version(char *data, int32_t len);

/**
 * @brief platform set language code
 * @param[in] data language code
 * @return PLATFORM_OK or PLATFPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_language_code(uint16_t data);

/**
 * @brief platform get language code
 * @param[in] data system language code
 * @retval PLATFORM_ERR get null
 * @retval PLATFORM_OK get language code
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_language_code(uint16_t *data);

/**
 * @brief platform set engine_mgr_uart enable
 * @param[in] data enable-1 or not-0
 * @return PLATFORM_OK
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_set_uart_enable(uint32_t data);

/**
 * @brief platform get the way of firmware upgrade
 * @param[in] data  the way of firmware upgrad
 * @retval PLATFORM_OK get the way of firmware upgrade
 * @autor liangshiqin
 * @date 2023-07-17
 */
int32_t pi_platform_get_fw_upgrade_way(uint32_t *data);

/**
 * @brief platform get the printer of location info(webpage set)
 * @param[out] data  location info string
 * @retval PLATFORM_OK get the location info string
 * @autor liangshiqin
 * @date 2024-08-15
 */
int32_t pi_platform_get_location(char *data, int32_t len);

/**
 * @brief platform get the versions of FPGA1 and FPGA2
 * @param[out] fpga_f  the version of FPGA1
 * @param[out] fpga_b  the version of FPGA2
 * @retval PLATFORM_OK get the versions
 * @autor liangshiqin
 * @date 2024-08-15
 */
int32_t pi_platform_get_fpga_ver(char *fpga_f, char *fpga_b);

/**
 * @brief platform set panel board ssn
 * @param[in] data  panel board ssn
 * @param[in] len data length
 * @return PLATFPRM_OK or PLATPROM_ERR
 * @retval PLATFPROM_OK set successfully
 * @retval PLATFPROM_ERR set failed
 * @autor liangshiqin
 * @date 2025-05-09
 */
int32_t pi_platform_set_panel_board_ssn(char *data, int32_t len);

/**
 * @brief platform get the panel board ssn
 * @param[out] data  panel board ssn string
 * @retval PLATFORM_OK get the panel board ssn string
 * @autor liangshiqin
 * @date 2025-05-09
 */
int32_t pi_platform_get_panel_board_ssn(char *data, int32_t len);

/**
 * @brief platform get the current machine configs
 * @param[out] config  MACHINE_CONFIG_S type data
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK get successfully
 * @retval PLATFPORM_ERR get faileds
 * @autor liangshiqin
 * @date 2024-06-03
 */
int32_t pi_platform_get_config(MACHINE_CONFIG_S *config);

/**
 * @brief platform get the count of the current exit tray
 * @param[out] data  Count of the current exit tray
 * @return PLATFPORM_OK or PLATPORM_ERR
 * @retval PLATFPORM_OK get successfully
 * @retval PLATFPORM_ERR get faileds
 * @autor liangshiqin
 * @date 2024-06-20
 */
int32_t pi_platform_get_exit_tray_count(uint32_t *data);


/**
 * @brief Called by system initialization for platform attribute initialization.\n
 *       Register the data to nvram, and read data save by power off from nvram.
 * @autor liangshiqin
 * @date 2023-07-17
 */
void platform_prolog(void);

#ifdef __cplusplus
}
#endif

#endif

/**
 * @}
 */

