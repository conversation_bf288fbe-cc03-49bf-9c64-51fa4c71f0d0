/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file on.c
 * @addtogroup process
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief on init
 */

#include "runtime/modules/process/on.h"

JSValue js_on_create(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{

}

JSValue js_on_start(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{

}

JSValue js_on_resume(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{

}

JSValue js_on_pause(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{

}

JSValue js_on_stop(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{

}

JSValue js_on_destroy(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    
}

JSValue js_process_on_back(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    //LOG_D("on_back","js_process_on_back");
    return JS_UNDEFINED;
}

JSValue js_process_on_front(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    //LOG_D("on_front","js_process_on_front");
    return JS_UNDEFINED;
}

/**
 * @}
 */


