/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_dc_cmd_process.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-06-09
 * @brief process cmd receive from panel
 */

#ifndef _PANEL_DC_CMD_PROCESS_H
#define _PANEL_DC_CMD_PROCESS_H


/**
 * @brief panel reuqest to restore factory default
 * @author: madechang
 */
uint32_t panel_request_restore_factory( void );

/**
 * @brief panel reuqest to scan next page
 * @author: madechang
 */
uint32_t panel_request_scan_next_page(uint32_t job_id );

/**
 * @brief panel reuqest to end scan
 * @author: madechang
 */
uint32_t panel_request_scan_done( uint32_t job_id  );


/**
 * @brief panel reuqest cancel job
 * @param[in] job_id job id from panel
 * @author: madechang
 */
void panel_job_cancel(uint32_t job_id);

/**
 * @brief panel reuqest remove job
 * @param[in] job_id job id from panel
 * @author: longxuan
 */
void panel_job_remove(uint32_t job_id);

/**
 * @brief panel reuqest priority print job
 * @param[in] job_id job id from panel
 * @author: longxuan
 */
void panel_job_priority_print(uint32_t job_id);

/**
 * @brief panel reuqest suspend print job
 * @author: longxuan
 */
void panel_job_suspend_print(void);

/**
 * @brief panel reuqest resume print job
 * @author: longxuan
 */
void panel_job_resume_print(void);

/**
 * @brief panel reuqest copy job
 * @param[in] data struct COPY_JOB_REQUEST_DATA_S data from panel
 * @param[in] data_len data length
 * @param[out] message job param from panel
 * @author: madechang
 */
void copy_job_start_process( void *data ,int data_len );

/**
 * @brief recv information cmd from panel
 * @param[in] short cmd setting cmd
 * @param[in] data setting data from panel
 * @param[in] data_len data length
 * @author: madechang
 */
void information_cmd_process(unsigned short cmd ,void *data ,int data_len);

/**
 * @brief process job cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void job_cmd_process(unsigned short cmd ,void *data ,int data_len);

/**
 * @brief process operate cmd which from dc to panel
 * @param[in] unsigned short cmd :cmd id
 * @param[in] void *data : data from panel or dc
 * @param[in] int data_len : data length
 * @author: madechang
 */
void operate_cmd_process(unsigned short cmd ,void *data ,int data_len);


/**
 * @brief recv setting cmd from panel to set sys setting
 * @param[in] short cmd setting cmd
 * @param[in] data setting data from panel
 * @param[in] data_len data length
 * @author: madechang
 */
void setting_cmd_process(unsigned short cmd ,void *data ,int data_len);

/**
 * @brief recv setting cmd from panel to set sys setting
 * @param[in] short cmd setting cmd
 * @param[in] data setting data from panel
 * @param[in] data_len data length
 * @author: longxuan
 */
void resource_cmd_process( unsigned short cmd ,void *data ,int data_len );

/**
 * @brief panel reuqest scan job
 * @param[in] data  struct SCAN_JOB_REQUEST_DATA_S data from panel
 * @param[in] data_len data length
 * @param[out] message job param from panel
 * @author: madechang
 */
void scan_job_start_process( void *data ,int data_len );

/**
 * @brief get panel firmware version from panel
 * @param[in] data  panel version string
 * @param[in] data_len data length
 * @author: madechang
 */
void get_panel_version( void *data ,int data_len );

/**
 * @brief get panel firmware version from panel
 * @param[in] data  print info page mode
 * @param[in] data_len data length
 * @author: madechang
 */
void print_info_page_job( void *data ,int data_len );

/**
 * @brief recv other cmd from panel to set sys setting
 * @param[in] short cmd setting cmd
 * @param[in] data setting data from panel
 * @param[in] data_len data length
 * @author: madechang
 */
void other_cmd_process(unsigned short cmd ,void *data ,int data_len);

/**
 * @brief panel reuqest pincode job
 * @param[in] data struct COPY_JOB_REQUEST_DATA_S data from panel
 * @param[in] data_len data length
 * @author: longxuan
 */
void pincode_print_job_start_process( void *data ,int data_len );

/**
 * @brief panel reuqest delay job
 * @param[in] data struct COPY_JOB_REQUEST_DATA_S data from panel
 * @param[in] data_len data length
 * @author: longxuan
 */
void delay_print_job_start_process( void *data ,int data_len );

/**
 * @brief panel reuqest sample print job
 * @param[in] data struct COPY_JOB_REQUEST_DATA_S data from panel
 * @param[in] data_len data length
 * @author: longxuan
 */
void sample_print_job_start_process( void *data ,int data_len );

#endif /* _PANEL_DC_CMD_PROCESS_H */

/**
 *@}
 */

