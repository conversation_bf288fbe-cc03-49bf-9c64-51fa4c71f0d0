.SUFFIXES: .c .o
.SUFFIXES: .c .i

ifeq ($(CROSS),1)
TOOLCHAIN_DIR=/tools/linaro/gcc-linaro-4.9.4-2017.01-i686_arm-linux-gnueabihf
SYSROOT=$(TOOLCHAIN_DIR)/arm-linux-gnueabihf/libc
CC=$(TOOLCHAIN_DIR)/bin/arm-linux-gnueabihf-gcc
AR=$(TOOLCHAIN_DIR)/bin/arm-linux-gnueabihf-ar
STRIP=$(TOOLCHAIN_DIR)/bin/arm-linux-gnueabihf-strip
CFLAGS=-std=gnu99 --sysroot=$(SYSROOT) 
libctest=ctest_arm
else
CC=gcc
#CFLAGS=-g
endif

SRC+= ./mem_testcase.c
SRC+= ../pol_mem.c
SRC+= ../pol_io.c
SRC+= ../pol_unistd.c
SRC+= ../pol_time.c
SRC+= ../pol_mem_trace.c
SRC+= ../../rbtree/rbtree.c

INC=-I ../../../include
INC+=-I ../incl/
INC+=-I ../../../include/pol		
INC+=-I ../../../include/linux
INC+=-include ../../../include/generated/autoconf.h

CC_FLAG=-Wall  -rdynamic -g -fvisibility=default -D_GNU_SOURCE
LIB=-lpthread

TARGET=mem_test single_thread_case multi_thread_case multi_process_case 

OBJ = $(patsubst %.c,%.o,$(SRC))
PRE_OBJ = $(patsubst %.c,%.i,$(SRC))
 

all: $(TARGET)

mem_test:$(OBJ) mem_test.o
	$(CC) $(CC_FLAG)  -o $@ mem_test.o $(OBJ)  $(LIB)
	
single_thread_case:$(OBJ) single_thread_case.o
	$(CC) $(CC_FLAG)  -o $@ single_thread_case.o  $(OBJ)  $(LIB)
multi_thread_case:$(OBJ) multi_thread_case.o
	$(CC) $(CC_FLAG)  -o $@ multi_thread_case.o  $(OBJ)  $(LIB)
multi_process_case:$(OBJ) multi_process_case.o
	$(CC) $(CC_FLAG)  -o $@ multi_process_case.o  $(OBJ)  $(LIB)
.c.o:
	$(CC) $(CC_FLAG) $(INC)  -o $@ -c $<	

.PHONY:pre
pre:$(PRE_OBJ)
	$(CC) $(CC_FLAG)   -o  $@ $(PRE_OBJ)  $(LIB)
#%.i:%.c
.c.i:
	$(CC) $(CC_FLAG) $(INC)   -o $@ -E $<	

.PHONY:clean
clean:
	@echo "clean project $(TARGET)" 
	@rm -f $(TARGET) $(OBJ) *_in *.log *.o *.flog 
