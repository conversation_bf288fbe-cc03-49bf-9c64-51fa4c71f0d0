#include "runtime/modules/std/object_storage.h"
#include <stdio.h>
#include <string.h>

/**
 * @brief 
 * 
 * @param ctx 
 * @param this_val 
 * @param argc 
 * @param argv 
 * @return JSValue 
 */
JSValue js_object_save(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    // 参数检查
    if (argc != 2) {
        // 调用参数错误
        return JS_ThrowTypeError(ctx, "Number of parameters ERROR!");
    }

    if (!JS_IsString(argv[0])) {
        // 参数1类型不是字符串类型
        return JS_ThrowTypeError(ctx, "Parameter 1 is not of String type");
    }

    if (!JS_IsObject(argv[1])) {
        // 参数2类型不是对象类型
        return JS_ThrowTypeError(ctx, "Parameter 2 is not of Object type");
    }

    // 数据类型调整。参数1：JSValue -> 字符串；参数2：object -> JSON格式字符串；
    JSValue object;
    const char* file_name = JS_ToCString(ctx, argv[0]);
    object = JS_JSONStringify(ctx, argv[1], JS_UNDEFINED, JS_UNDEFINED);
    const char* object_str = JS_ToCString(ctx, object);

    // 获取工作路径
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    if (prt->app_path.work_space) {
        char temp_file[WORK_PATH_LENGTH];

        // 拼，完整路径
        memset(temp_file, 0x00, WORK_PATH_LENGTH);
        strcpy(temp_file, prt->app_path.work_space);
        strcat(temp_file, file_name);

        // 创建对象存储文件，用参数一命名。
        FILE* pf = NULL;
        pf = fopen(temp_file, "w");
        fprintf(pf, "%s", object_str); // 将object以JSON字符串方式写入
        fclose(pf);
    }

    // 释放内存，避免内存泄露
    JS_FreeCString(ctx, file_name);
    JS_FreeCString(ctx, object_str);
}

JSValue js_object_load(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    JSValue ret = { 0 };

    // 参数检查
    if (argc != 1) {
        // 调用参数错误
        return JS_ThrowTypeError(ctx, "Number of parameters ERROR!");
    }

    if (!JS_IsString(argv[0])) {
        // 参数1类型不是字符串类型
        return JS_ThrowTypeError(ctx, "Parameter 1 is not of String type");
    }
    const char* file_name = JS_ToCString(ctx, argv[0]);

    // 获取工作路径rt->workspace
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    if (prt->app_path.work_space) {
        char temp_file[WORK_PATH_LENGTH];

        // 拼，完整路径
        memset(temp_file, 0x00, WORK_PATH_LENGTH);
        strcpy(temp_file, prt->app_path.work_space);
        strcat(temp_file, file_name);

        // 获取文件内数据总长度
        size_t file_size = get_file_size(temp_file);
        // 准备数据接收缓存
        char* tempbuf = NULL;
        tempbuf = (char*)malloc(file_size + 1);
        if (NULL == tempbuf) {
            return ret;
        }

        // 读取文件全部数据
        memset(tempbuf, 0x00, file_size + 1);
        read_file(temp_file, tempbuf, file_size + 1, &file_size);
        tempbuf[file_size] = '\0'; // 最后一个字节补'\0'

        //*字符串转换成JSValue值，使用可以扩展的JSON格式：JS_PARSE_JSON_EXT
        ret = JS_ParseJSON2(ctx, tempbuf, file_size - 1, temp_file, JS_PARSE_JSON_EXT);

        free(tempbuf); // 释放缓存
    }

    return ret;
}
