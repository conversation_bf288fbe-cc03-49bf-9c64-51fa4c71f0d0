/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_file.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief QIO file delegator
 */
#include "qiox.h"

#if 0
static void parse_stream_name(const char* stream, char* path, char* user, char* pass, size_t size)
{
    char*   pp;
    int32_t i;

    snprintf(path, size, "%s", stream);
    user[0] = pass[0] = '\0';

    pp = path;
    while ( *pp && *pp != '!' )
    {
        pp++;
    }

    if ( *pp == '!' )
    {
        *pp++ = '\0';
        i = 0;
        while ( *pp && *pp != '!' && i < size - 2 )
        {
            user[i++] = *pp++;
        }
        user[i] = '\0';
    }

    if ( *pp == '!' )
    {
        pp++;
        i = 0;
        while ( *pp && *pp != '!' && i < size - 2 )
        {
            pass[i++] = *pp++;
        }
        pass[i] = '\0';
    }
    QIO_DEBUG("path(%s) user(%s) pass(%s)", path, user, pass);
}
#endif

QIO_S* qio_file_create(const char* stream, int32_t flags)
{
    QIO_S*  pqio = NULL;

    QIO_DEBUG("stream(%s) flags(%x)", stream, flags);
    if ( (flags & (O_WRONLY | O_CREAT)) == 0 )
    {
        pqio = qio_mem_create_from_name(stream, flags);
    }
    RETURN_VAL_IF(pqio != NULL, QIO_DEBUG, pqio);

#if 0
    int32_t rcode = 0;
    char    path[MAX_PATH];
    char    user[MAX_PATH];
    char    pass[MAX_PATH];

    if ( strncmp(stream, "ftp://", 6) == 0 )
    {
        parse_stream_name(stream + 6, path, user, pass, MAX_PATH);
        pqio = qio_ftpfile_create(path, user, pass);
    }
    RETURN_VAL_IF(pqio != NULL, QIO_DEBUG, pqio);

    if ( strncmp(stream, "http://", 7) == 0 )
    {
        parse_stream_name(stream, path, user, pass, MAX_PATH);
        pqio = qio_httpfile_create(path, 0x20000, 0x10000000, flags);
        if ( pqio != NULL )
        {
            if ( qio_httpfile_transfer(pqio, user, pass, &rcode) != 0 )
            {
                QIO_WARN("httpfile_transfer rcode(%d)", rcode);
                QIO_CLOSE(pqio);
                return NULL;
            }
        }
    }
    RETURN_VAL_IF(pqio != NULL, QIO_DEBUG, pqio);
#endif

    return qio_storage_file_create(stream, flags);
}
/**
 *@}
 */
