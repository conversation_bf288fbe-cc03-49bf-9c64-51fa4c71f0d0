# XHR.C 文件完整功能分析

## 1. 概述

本文档详细分析了PEDK系统中xhr.c文件的完整实现逻辑，包括XMLHttpRequest类的所有功能、与JavaScript的接口、libcurl集成、以及与http.js的调用关系。xhr.c是PEDK运行时中负责HTTP网络通信的核心模块。

## 2. xhr.c 文件架构

### 2.1 模块定位
xhr.c是PEDK运行时的标准模块，位于`pedk/src/runtime/modules/std/`目录下，负责为JavaScript提供XMLHttpRequest API的完整实现。

### 2.2 核心组件架构
```
┌─────────────────────────────────────────────────────────────┐
│                    xhr.c 核心架构                            │
├─────────────────────────────────────────────────────────────┤
│  JavaScript API Layer                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ Constructor │ │ Properties  │ │   Methods   │          │
│  │ new XHR()   │ │ readyState  │ │   open()    │          │
│  │             │ │ response    │ │   send()    │          │
│  │             │ │ status      │ │   abort()   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  QuickJS Bridge Layer                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ JS_CFUNC    │ │ JS_CGETSET  │ │ Event Emit  │          │
│  │ 函数绑定     │ │ 属性绑定     │ │ 事件触发     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Core Logic Layer                                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ STPeSFXhr   │ │ State Mgmt  │ │ Buffer Mgmt │          │
│  │ 核心结构体   │ │ 状态管理     │ │ 缓冲区管理   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
├─────────────────────────────────────────────────────────────┤
│  Network Layer                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │ libcurl     │ │ libuv Loop  │ │ Async I/O   │          │
│  │ HTTP客户端   │ │ 事件循环     │ │ 异步处理     │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 3. 核心数据结构分析

### 3.1 STPeSFXhr 主结构体
```c
typedef struct {
    JSContext *ctx;                         // JavaScript上下文
    JSValue events[XHR_EVENT_MAX];          // 事件回调函数数组
    STPeSFCurlPrivate curl_private;         // libcurl私有数据

    struct curl_slist *slist;               // HTTP头部链表
    bool sent;                              // 是否已发送请求
    bool async;                             // 是否异步模式
    unsigned long timeout;                  // 超时时间(毫秒)
    short response_type;                    // 响应类型
    unsigned short ready_state;             // 就绪状态

    struct {
        char *raw;                          // 原始状态字符串
        JSValue status;                     // HTTP状态码
        JSValue status_text;                // HTTP状态文本
    } status;

    struct {
        JSValue url;                        // 响应URL
        JSValue headers;                    // 响应头部
        JSValue response;                   // 响应数据
        JSValue response_text;              // 响应文本
        DynBuf hbuf;                        // 头部动态缓冲区
        DynBuf bbuf;                        // 数据动态缓冲区
    } result;

    // PEDK扩展属性 (用于文件下载)
    JSValue user_path;                      // 用户指定保存路径
    JSValue overwrite;                      // 是否覆盖现有文件
} STPeSFXhr;
```

### 3.2 状态枚举定义
```c
// XHR就绪状态
enum {
    XHR_RSTATE_UNSENT = 0,              // 未发送
    XHR_RSTATE_OPENED,                  // 已打开
    XHR_RSTATE_HEADERS_RECEIVED,        // 已接收头部
    XHR_RSTATE_LOADING,                 // 正在加载
    XHR_RSTATE_DONE,                    // 完成
};

// 响应类型
enum {
    XHR_RTYPE_DEFAULT = 0,              // 默认类型
    XHR_RTYPE_TEXT,                     // 文本类型
    XHR_RTYPE_ARRAY_BUFFER,             // 数组缓冲区
    XHR_RTYPE_JSON,                     // JSON类型
};
```

### 3.3 事件类型定义
xhr.c支持完整的XHR事件模型，包括：
- `XHR_EVENT_READY_STATE_CHANGED`: 状态变更事件
- `XHR_EVENT_LOAD_START`: 开始加载事件
- `XHR_EVENT_LOAD`: 加载完成事件
- `XHR_EVENT_LOAD_END`: 加载结束事件
- `XHR_EVENT_PROGRESS`: 进度事件
- `XHR_EVENT_ERROR`: 错误事件
- `XHR_EVENT_ABORT`: 中止事件
- `XHR_EVENT_TIMEOUT`: 超时事件

## 4. JavaScript API 完整实现

### 4.1 XMLHttpRequest 构造函数
```c
static JSValue net_xhr_constructor(JSContext *ctx, JSValueConst new_target, int argc, JSValueConst *argv)
{
    // 1. 创建JavaScript对象
    JSValue obj = JS_NewObjectClass(ctx, net_xhr_class_id);

    // 2. 分配C结构体内存
    STPeSFXhr* x = calloc(1, sizeof(STPeSFXhr));

    // 3. 初始化基本属性
    x->ctx = ctx;
    x->ready_state = XHR_RSTATE_UNSENT;
    x->async = true;                        // 默认异步模式
    x->sent = false;

    // 4. 初始化动态缓冲区
    dbuf_init(&x->result.hbuf);             // 头部缓冲区
    dbuf_init(&x->result.bbuf);             // 数据缓冲区

    // 5. 初始化事件数组
    for (int i = 0; i < XHR_EVENT_MAX; i++) {
        x->events[i] = JS_UNDEFINED;
    }

    // 6. 初始化libcurl和libuv
    x->curl_private.uv_loop = qrt->uv_loop;
    x->curl_private.arg = x;
    x->curl_private.done_cb = curlm__done_cb;

    // 7. 创建curl句柄
    x->curl_private.curlm_h = pesfGetCurlm(&x->curl_private);
    x->curl_private.curl_h = pesfCurlEasyInit(NULL);

    // 8. 配置curl基本选项
    curl_easy_setopt(x->curl_private.curl_h, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(x->curl_private.curl_h, CURLOPT_WRITEFUNCTION, curl__data_cb);
    curl_easy_setopt(x->curl_private.curl_h, CURLOPT_HEADERFUNCTION, curl__header_cb);

    // 9. 绑定C对象到JavaScript对象
    JS_SetOpaque(obj, x);
    return obj;
}
```

### 4.2 核心属性实现

#### 4.2.1 只读属性
```c
// readyState - 当前状态
static JSValue net_xhr_readystate_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    return JS_NewInt32(ctx, x->ready_state);
}

// status - HTTP状态码
static JSValue net_xhr_status_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    return JS_DupValue(ctx, x->status.status);
}

// statusText - HTTP状态文本
static JSValue net_xhr_statustext_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    return JS_DupValue(ctx, x->status.status_text);
}

// response - 响应数据 (根据responseType返回不同格式)
static JSValue net_xhr_response_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    DynBuf *bbuf = &x->result.bbuf;

    switch (x->response_type) {
        case XHR_RTYPE_ARRAY_BUFFER:
            return JS_NewArrayBuffer(ctx, bbuf->buf, bbuf->size, NULL, NULL, FALSE);
        case XHR_RTYPE_JSON:
            return JS_ParseJSON(ctx, (const char*)bbuf->buf, bbuf->size, "<xhr>");
        case XHR_RTYPE_TEXT:
        default:
            return JS_NewStringLen(ctx, (const char*)bbuf->buf, bbuf->size);
    }
}
```

#### 4.2.2 可读写属性
```c
// timeout - 超时时间
static JSValue net_xhr_timeout_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    return JS_NewInt64(ctx, x->timeout);
}

static JSValue net_xhr_timeout_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    int32_t timeout;
    JS_ToInt32(ctx, &timeout, value);
    x->timeout = timeout;

    // 如果还未发送，设置curl超时
    if (!x->sent)
        curl_easy_setopt(x->curl_private.curl_h, CURLOPT_TIMEOUT_MS, timeout);

    return JS_UNDEFINED;
}

// responseType - 响应类型
static JSValue net_xhr_responsetype_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    const char *response_type = JS_ToCString(ctx, value);

    if (strcmp(response_type, "text") == 0)
        x->response_type = XHR_RTYPE_TEXT;
    else if (strcmp(response_type, "arraybuffer") == 0)
        x->response_type = XHR_RTYPE_ARRAY_BUFFER;
    else if (strcmp(response_type, "json") == 0)
        x->response_type = XHR_RTYPE_JSON;
    else
        x->response_type = XHR_RTYPE_DEFAULT;

    JS_FreeCString(ctx, response_type);
    return JS_UNDEFINED;
}
```

### 4.3 PEDK扩展属性
```c
// userSavePath - 文件保存路径 (PEDK扩展)
static JSValue net_wget_save_path_get(JSContext *ctx, JSValueConst this_val) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    return JS_DupValue(ctx, x->user_path);
}

static JSValue net_wget_save_path_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    JS_FreeValue(ctx, x->user_path);
    x->user_path = JS_DupValue(ctx, value);
    return JS_UNDEFINED;
}

// overwrite - 是否覆盖文件 (PEDK扩展)
static JSValue net_wget_overwrite_set(JSContext *ctx, JSValueConst this_val, JSValueConst value) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    x->overwrite = JS_IsBool(value) ? value : JS_TRUE;
    return JS_UNDEFINED;
}
```

## 5. 核心方法实现

### 5.1 open() 方法
```c
static JSValue net_xhr_open(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    // 只有在UNSENT状态才能调用open
    if (x->ready_state < XHR_RSTATE_OPENED) {
        JSValue method = argv[0];           // HTTP方法
        JSValue url = argv[1];              // 请求URL
        JSValue async = argv[2];            // 是否异步(可选)

        const char *method_str = JS_ToCString(ctx, method);
        const char *url_str = JS_ToCString(ctx, url);

        // 设置异步模式
        if (argc >= 3)
            x->async = JS_ToBool(ctx, async);

        // 配置HTTP方法
        if (strncasecmp("HEAD", method_str, 4) == 0)
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_NOBODY, 1L);
        else
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_CUSTOMREQUEST, method_str);

        // 设置请求URL
        curl_easy_setopt(x->curl_private.curl_h, CURLOPT_URL, url_str);

        JS_FreeCString(ctx, method_str);
        JS_FreeCString(ctx, url_str);

        // 更新状态并触发事件
        x->ready_state = XHR_RSTATE_OPENED;
        maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);
    }

    return JS_UNDEFINED;
}
```

### 5.2 send() 方法
```c
static JSValue net_xhr_send(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    if (!x->sent) {
        // 处理请求体数据
        if (argc > 0) {
            JSValue arg = argv[0];
            if (JS_IsString(arg)) {
                size_t size;
                const char *body = JS_ToCStringLen(ctx, &size, arg);
                if (body) {
                    // 设置POST数据
                    curl_easy_setopt(x->curl_private.curl_h, CURLOPT_POSTFIELDSIZE, (long) size);
                    curl_easy_setopt(x->curl_private.curl_h, CURLOPT_COPYPOSTFIELDS, body);
                    JS_FreeCString(ctx, body);
                }
            }
            // TODO: 支持FormData、ArrayBuffer等其他数据类型
        }

        // 设置HTTP头部
        if (x->slist)
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_HTTPHEADER, x->slist);

        // 根据异步模式选择执行方式
        if (x->async) {
            // 异步模式：添加到multi句柄
            curl_multi_add_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
        } else {
            // 同步模式：直接执行
            CURLcode result = curl_easy_perform(x->curl_private.curl_h);
            curl__done_cb(result, x);
        }

        x->sent = true;
    }

    return JS_UNDEFINED;
}
```

### 5.3 setRequestHeader() 方法
```c
static JSValue net_xhr_setrequestheader(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    if (!JS_IsString(argv[0]))
        return JS_UNDEFINED;

    const char *h_name = JS_ToCString(ctx, argv[0]);
    const char *h_value = NULL;

    if (!JS_IsUndefined(argv[1]))
        h_value = JS_ToCString(ctx, argv[1]);

    // 构建HTTP头部字符串
    char buf[CURL_MAX_HTTP_HEADER];
    if (h_value)
        snprintf(buf, sizeof(buf), "%s: %s", h_name, h_value);
    else
        snprintf(buf, sizeof(buf), "%s;", h_name);

    // 添加到curl头部链表
    struct curl_slist *list = curl_slist_append(x->slist, buf);
    if (list)
        x->slist = list;

    JS_FreeCString(ctx, h_name);
    if (h_value)
        JS_FreeCString(ctx, h_value);

    return JS_UNDEFINED;
}
```

### 5.4 abort() 方法
```c
static JSValue net_xhr_abort(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    if (x->curl_private.curl_h) {
        // 从multi句柄中移除
        curl_multi_remove_handle(x->curl_private.curlm_h, x->curl_private.curl_h);

        // 清理curl句柄
        curl_easy_cleanup(x->curl_private.curl_h);
        x->curl_private.curl_h = NULL;
        x->curl_private.curlm_h = NULL;

        // 重置状态
        x->ready_state = XHR_RSTATE_UNSENT;
        JS_FreeValue(ctx, x->status.status);
        x->status.status = JS_NewInt32(x->ctx, 0);
        JS_FreeValue(ctx, x->status.status_text);
        x->status.status_text = JS_NewString(ctx, "");

        // 触发abort事件
        maybe_emit_event(x, XHR_EVENT_ABORT, JS_UNDEFINED);
    }

    return JS_UNDEFINED;
}
```

### 5.5 getResponseHeader() 方法
```c
static JSValue net_xhr_getresponseheader(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    if (x->ready_state < XHR_RSTATE_HEADERS_RECEIVED)
        return JS_NULL;

    const char *header_name = JS_ToCString(ctx, argv[0]);
    DynBuf r;
    dbuf_init(&r);

    // 在头部缓冲区中搜索指定头部
    // 实现头部名称的大小写不敏感匹配
    // 返回匹配的头部值

    JS_FreeCString(ctx, header_name);

    JSValue ret;
    if (r.size == 0)
        ret = JS_NULL;
    else
        ret = JS_NewStringLen(ctx, (const char *) r.buf, r.size - 2);

    dbuf_free(&r);
    return ret;
}
```

## 6. 事件处理机制

### 6.1 事件绑定
xhr.c支持标准的XHR事件模型，通过属性setter绑定事件处理函数：

```c
// 事件属性的getter/setter
static JSValue net_xhr_event_get(JSContext *ctx, JSValueConst this_val, int magic) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);
    return JS_DupValue(ctx, x->events[magic]);
}

static JSValue net_xhr_event_set(JSContext *ctx, JSValueConst this_val, JSValueConst value, int magic) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    // 只接受函数、undefined或null
    if (JS_IsFunction(ctx, value) || JS_IsUndefined(value) || JS_IsNull(value)) {
        JS_FreeValue(ctx, x->events[magic]);
        x->events[magic] = JS_DupValue(ctx, value);
    }

    return JS_UNDEFINED;
}

// 事件属性定义 (使用magic number区分不同事件)
JS_CGETSET_MAGIC_DEF("onloadstart", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_LOAD_START),
JS_CGETSET_MAGIC_DEF("onload", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_LOAD),
JS_CGETSET_MAGIC_DEF("onloadend", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_LOAD_END),
JS_CGETSET_MAGIC_DEF("onprogress", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_PROGRESS),
JS_CGETSET_MAGIC_DEF("onerror", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_ERROR),
JS_CGETSET_MAGIC_DEF("onabort", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_ABORT),
JS_CGETSET_MAGIC_DEF("ontimeout", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_TIMEOUT),
JS_CGETSET_MAGIC_DEF("onreadystatechange", net_xhr_event_get, net_xhr_event_set, XHR_EVENT_READY_STATE_CHANGED),
```

### 6.2 事件触发机制
```c
static void maybe_emit_event(STPeSFXhr* x, int event_type, JSValue event_data) {
    if (!JS_IsFunction(x->ctx, x->events[event_type]))
        return;

    JSValue ret = JS_Call(x->ctx, x->events[event_type], JS_UNDEFINED,
                         JS_IsUndefined(event_data) ? 0 : 1, &event_data);

    if (JS_IsException(ret)) {
        // 处理事件处理函数中的异常
        js_std_dump_error(x->ctx);
    }

    JS_FreeValue(x->ctx, ret);
    if (!JS_IsUndefined(event_data))
        JS_FreeValue(x->ctx, event_data);
}
```

## 7. libcurl集成层

### 7.1 curl回调函数

#### 7.1.1 数据接收回调
```c
static size_t curl__data_cb(char *ptr, size_t size, size_t nmemb, void *userdata)
{
    STPeSFXhr* x = userdata;
    size_t realsize = size * nmemb;

    // 状态转换：HEADERS_RECEIVED → LOADING
    if (x->ready_state == XHR_RSTATE_HEADERS_RECEIVED) {
        x->ready_state = XHR_RSTATE_LOADING;
        maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);
    }

    // 将数据追加到响应缓冲区
    if (dbuf_put(&x->result.bbuf, (const uint8_t *) ptr, realsize))
        return -1;

    return realsize;
}
```

#### 7.1.2 头部接收回调
```c
static size_t curl__header_cb(char *ptr, size_t size, size_t nmemb, void *userdata)
{
    STPeSFXhr* x = userdata;
    size_t realsize = size * nmemb;

    // 检查是否为HTTP状态行
    if (strncmp(ptr, "HTTP/", 5) == 0) {
        // 解析状态码和状态文本
        char *status_line = strndup(ptr, realsize);
        // 提取状态码和状态文本...

        x->ready_state = XHR_RSTATE_HEADERS_RECEIVED;
        maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);
    }

    // 将头部数据追加到头部缓冲区
    if (dbuf_put(&x->result.hbuf, (const uint8_t *) ptr, realsize))
        return -1;

    return realsize;
}
```

#### 7.1.3 进度回调 (当前未启用)
```c
static int curl__progress_cb(void *clientp, curl_off_t dltotal, curl_off_t dlnow,
                            curl_off_t ultotal, curl_off_t ulnow)
{
    STPeSFXhr* x = clientp;

    // 下载进度事件
    if (x->ready_state == XHR_RSTATE_LOADING && dltotal > 0) {
        JSContext *ctx = x->ctx;
        JSValue event = JS_NewObjectProto(ctx, JS_NULL);
        JS_DefinePropertyValueStr(ctx, event, "lengthComputable", JS_NewBool(ctx, TRUE), JS_PROP_C_W_E);
        JS_DefinePropertyValueStr(ctx, event, "loaded", JS_NewInt64(ctx, dlnow), JS_PROP_C_W_E);
        JS_DefinePropertyValueStr(ctx, event, "total", JS_NewInt64(ctx, dltotal), JS_PROP_C_W_E);
        maybe_emit_event(x, XHR_EVENT_PROGRESS, event);
    }

    // 上传进度事件 (upload.onprogress)
    // TODO: 实现upload对象和相关事件

    return 0;
}
```

#### 7.1.4 请求完成回调
```c
static void curl__done_cb(CURLcode result, STPeSFXhr* x) {
    // 更新最终状态
    x->ready_state = XHR_RSTATE_DONE;
    maybe_emit_event(x, XHR_EVENT_READY_STATE_CHANGED, JS_UNDEFINED);

    // 触发结束事件
    maybe_emit_event(x, XHR_EVENT_LOAD_END, JS_UNDEFINED);

    // 根据结果触发成功或错误事件
    if (result != CURLE_OPERATION_TIMEDOUT) {
        if (result != CURLE_OK)
            maybe_emit_event(x, XHR_EVENT_ERROR, JS_UNDEFINED);
        else
            maybe_emit_event(x, XHR_EVENT_LOAD, JS_UNDEFINED);
    }
}

// multi句柄完成回调
static void curlm__done_cb(CURLMsg *message, void *arg) {
    STPeSFXhr* x = arg;
    curl__done_cb(message->data.result, x);

    // 清理curl句柄
    x->curl_private.curl_h = NULL;
}
```

### 7.2 libuv集成
xhr.c通过curl_utils.c与libuv事件循环集成，实现真正的异步I/O：

```c
// 初始化时绑定到运行时的事件循环
x->curl_private.uv_loop = qrt->uv_loop;

// 创建uv定时器用于curl超时处理
uv_timer_init(qrt->uv_loop, &(x->curl_private.uv_timer));

// curl multi句柄与uv事件循环集成
x->curl_private.curlm_h = pesfGetCurlm(&x->curl_private);
```

## 8. 与http.js的调用关系

### 8.1 http.js中的XHR使用
http.js是PEDK的高级HTTP API封装，内部大量使用XMLHttpRequest：

#### 8.1.1 fetchData() 函数实现
```javascript
// pedk/js/modules/pesf/http.js
function fetchData(request, callback) {
    if (!(request instanceof Request)) {
        throw new TypeError("invalid parameter");
    }
    if (typeof callback !== 'function') {
        throw TypeError("not a function");
    }

    //console.log('JS:DEBUG: fetchData start ' + request.url);
    var xhr = new XMLHttpRequest();
    if (request.getTimeout() > 0)
        xhr.timeout = request.getTimeout();

    xhr.onload = function() {
        let headers = parseHeaders(xhr.getAllResponseHeaders() || '');
        let body = 'response' in xhr ? xhr.response : xhr.responseText;
        var statusText = String(xhr.statusText);
        var respObj = new Response(xhr.status, headers, body);
        setTimeout(function() {
            callback(statusText, respObj);
            xhr = null;
        }, 1);
    };

    xhr.onerror = function() {
        setTimeout(function() {
            callback("Connection Refused", new Response(-2));
        }, 0);
    };

    xhr.ontimeout = function() {
        setTimeout(function() {
            callback("No Internet/Timeout", new Response(-1));
        }, 0);
    };

    xhr.open(request.method, request.url, true);

    request.headers.forEach(function(value, name) {
        xhr.setRequestHeader(name, value);
    });

    // 根据Content-Type设置响应类型
    if ('responseType' in xhr) {
        if (support.arrayBuffer &&
            request.headers.get('Content-Type') &&
            request.headers.get('Content-Type').indexOf('application/octet-stream') !== -1) {
            xhr.responseType = 'arraybuffer';
        }
        else if (request.headers.get('Content-Type') &&
                 request.headers.get('Content-Type').indexOf('application/json') !== -1) {
            xhr.responseType = 'json';
        }
    }

    xhr.send(typeof request.body === 'undefined' ? null : request.body);
}
```

#### 8.1.2 wget() 函数实现
```javascript
function wget(url, save_path, callback, overwrite) {
    var xhr = new XMLHttpRequest();
    xhr.timeout = 5*60*1000;                // 5分钟超时
    xhr.userSavePath = save_path;           // PEDK扩展属性
    xhr.overwrite = overwrite;              // PEDK扩展属性

    xhr.onload = function() {
        var statusText = xhr.response ? String(xhr.statusText) : "Download failed";
        setTimeout(function() {
            callback(statusText, save_path);
            xhr = null;
        }, 1);
    };

    xhr.onerror = function() {
        setTimeout(function() {
            callback("Connection Refused", save_path);
        }, 0);
    };

    xhr.ontimeout = function() {
        setTimeout(function() {
            callback("No Internet/Timeout", save_path);
        }, 0);
    };

    xhr.open("GET", url, true);
    xhr.send(null);
}
```

#### 8.1.3 http.js中的核心类详细解释

##### ******* Headers 类
```javascript
function Headers(headers, value) {
    this.map = {};

    if (headers instanceof Headers) {
        headers.forEach(function(value, name) {
            this.append(name, value);
        }, this);
    } else if (Array.isArray(headers)) {
        headers.forEach(function(header) {
            this.append(header[0], header[1]);
        }, this);
    } else if (headers) {
        if (typeof headers === 'string' && value) {
            this.append(headers, value);
        } else {
            Object.getOwnPropertyNames(headers).forEach(function (name) {
                this.append(name, headers[name]);
            }, this);
        }
    }
}

// 核心方法
Headers.prototype.append = function(name, value) {
    name = normalizeName(name);
    value = normalizeValue(value);
    var oldValue = this.map[name];
    this.map[name] = oldValue ? oldValue + ', ' + value : value;
};

Headers.prototype.get = function(name) {
    name = normalizeName(name);
    return this.has(name) ? this.map[name] : null
};

Headers.prototype.set = function(name, value) {
    this.map[normalizeName(name)] = normalizeValue(value);
};
```

##### ******* Request 类
```javascript
function Request(url, method, headers, body) {
    this.timeout = 0;
    this.url = String(url);
    this.method = normalizeMethod(method || this.method || 'GET');

    // 自动调整方法：GET/HEAD有body时改为POST
    if ((this.method === 'GET' || this.method === 'HEAD') && body) {
        this.method = 'POST';
    }

    if (headers || !this.headers) {
        this.headers = new Headers(headers);
    }

    if (body || !this.body) {
        if (body instanceof RequestBody) {
            if ('_bodyJsonText' in body) {
                this.body = body._bodyJsonText;
                if (!this.headers.get('content-type')) {
                    this.headers.set('Content-Type', 'application/json');
                }
            } else {
                this.body = body._bodyText;
            }
        } else {
            this.body = body;
        }
    }
}

// 超时设置方法
Request.prototype.setTimeout = function(ms) {
    if (typeof ms !== 'number') {
        throw new TypeError("invalid parameter");
    }
    if (ms < 0) ms = 0;
    this.timeout = ms;
}
```

##### ******* HTTPS配置相关API
```javascript
// HTTPS配置参数类
class HttpsConfigParam {
    constructor() {
        this.verify_certificate = 1; // 默认：1 (验证证书)
        this.verify_host_mode = 2;   // 默认：2 (严格验证)
        this.client_cert_path = ""; // 默认：空
        this.client_key_path = "";  // 默认：空
        this.key_password = "";     // 默认：空
    }
}

// 设置HTTPS配置参数
function setHttpsConfigParams(https_config_params) {
    // 调用 C 实现绑定的 native 函数
    const result = set_https_config_params(JSON.stringify(https_config_params));
    return result;
}

// 获取HTTPS配置参数
function getHttpsConfigParams() {
    const result = get_https_config_params();
    try {
        return JSON.parse(result); // 尝试将结果解析为 JSON 格式
    } catch (e) {
        return result; // 如果解析失败，返回原始字符串
    }
}
```

### 8.2 调用流程分析

#### 8.2.1 完整调用链路图
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           PEDK HTTP 完整调用链路                            │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │  JavaScript应用  │    │    http.js      │    │   xhr.c (C层)   │         │
│  │                 │    │                 │    │                 │         │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│  │ │ fetch()     │ │───▶│ │ new XHR()   │ │───▶│ │ constructor │ │         │
│  │ │ 调用        │ │    │ │ 创建对象    │ │    │ │ 初始化      │ │         │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │         │
│  │                 │    │                 │    │                 │         │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│  │ │ Promise     │ │◀───│ │ 事件处理    │ │◀───│ │ 事件触发    │ │         │
│  │ │ resolve     │ │    │ │ onload等    │ │    │ │ 回调        │ │         │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│           ▲                       ▲                       │                 │
│           │                       │                       ▼                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐         │
│  │   应用层响应     │    │   JavaScript     │    │  curl_utils.c   │         │
│  │                 │    │   事件系统       │    │                 │         │
│  │ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │         │
│  │ │ Response    │ │    │ │ Event Loop  │ │    │ │ libuv集成   │ │         │
│  │ │ 对象        │ │    │ │ 事件循环    │ │    │ │ 异步I/O     │ │         │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │         │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘         │
│                                                          │                 │
│                                                          ▼                 │
│  ┌─────────────────────────────────────────────────────────────────────┐   │
│  │                        网络传输层                                   │   │
│  │                                                                     │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │   │
│  │  │ TCP Socket  │  │ HTTP/HTTPS  │  │ TLS/SSL     │  │ 外部服务器 │   │   │
│  │  │ 连接管理    │  │ 协议处理    │  │ 安全传输    │  │ API端点   │   │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │   │
│  └─────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 8.2.2 时序图：HTTP请求生命周期
```
JavaScript应用    http.js      xhr.c       curl_utils.c    网络层      外部服务器
     │              │           │              │            │             │
     │─ fetch() ────▶│           │              │            │             │
     │              │─ new XHR()─▶│              │            │             │
     │              │           │─ 初始化 ─────▶│            │             │
     │              │           │              │─ 创建句柄 ─▶│             │
     │              │─ open() ──▶│              │            │             │
     │              │           │─ 配置curl ───▶│            │             │
     │              │─ send() ──▶│              │            │             │
     │              │           │─ 启动请求 ───▶│            │             │
     │              │           │              │─ 建立连接 ─▶│─ TCP连接 ──▶│
     │              │           │              │            │◀─ 连接确认 ─│
     │              │           │              │─ 发送请求 ─▶│─ HTTP请求 ─▶│
     │              │           │              │            │◀─ HTTP响应 ─│
     │              │           │◀─ 数据回调 ───│◀─ 接收数据 ─│             │
     │              │◀─ onload ─│              │            │             │
     │◀─ resolve() ─│           │              │            │             │
     │              │           │─ 清理资源 ───▶│            │             │
     │              │           │              │─ 关闭连接 ─▶│             │
```

#### 8.2.3 数据流向图
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           HTTP 数据流向分析                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  请求方向 (JavaScript → 网络)                                               │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │ JavaScript  │    │   xhr.c     │    │curl_utils.c │    │   网络层    │   │
│  │   数据      │    │   处理      │    │   传输      │    │   发送      │   │
│  │             │    │             │    │             │    │             │   │
│  │ ┌─────────┐ │───▶│ ┌─────────┐ │───▶│ ┌─────────┐ │───▶│ ┌─────────┐ │   │
│  │ │ 请求体  │ │    │ │ 编码转换│ │    │ │ 缓冲管理│ │    │ │ TCP发送 │ │   │
│  │ │ 头部    │ │    │ │ 格式化  │ │    │ │ 流控制  │ │    │ │ 分片    │ │   │
│  │ │ URL参数 │ │    │ │ 验证    │ │    │ │ 重试    │ │    │ │ 校验    │ │   │
│  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│                                                                             │
│  响应方向 (网络 → JavaScript)                                               │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │   网络层    │    │curl_utils.c │    │   xhr.c     │    │ JavaScript  │   │
│  │   接收      │    │   处理      │    │   解析      │    │   回调      │   │
│  │             │    │             │    │             │    │             │   │
│  │ ┌─────────┐ │───▶│ ┌─────────┐ │───▶│ ┌─────────┐ │───▶│ ┌─────────┐ │   │
│  │ │ TCP接收 │ │    │ │ 数据聚合│ │    │ │ 状态更新│ │    │ │ 事件触发│ │   │
│  │ │ 重组    │ │    │ │ 错误处理│ │    │ │ 类型转换│ │    │ │ Promise │ │   │
│  │ │ 校验    │ │    │ │ 进度跟踪│ │    │ │ 内存管理│ │    │ │ 回调    │ │   │
│  │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │    │ └─────────┘ │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 9. 完整API列表

### 9.1 XMLHttpRequest 构造函数
```javascript
var xhr = new XMLHttpRequest();
```

### 9.2 属性 (Properties)

#### 9.2.1 只读属性
```javascript
xhr.readyState          // 0-4: 请求状态
xhr.response            // 响应数据 (根据responseType格式化)
xhr.responseText        // 响应文本
xhr.responseURL         // 响应URL
xhr.status              // HTTP状态码
xhr.statusText          // HTTP状态文本
xhr.upload              // Upload对象 (TODO: 未实现)
```

#### 9.2.2 可读写属性
```javascript
xhr.timeout             // 超时时间(毫秒)
xhr.responseType        // 响应类型: "", "text", "json", "arraybuffer"
xhr.withCredentials     // 是否发送凭据 (TODO: 未实现)

// PEDK扩展属性
xhr.userSavePath        // 文件保存路径
xhr.overwrite           // 是否覆盖现有文件
```

#### 9.2.3 事件属性
```javascript
xhr.onreadystatechange  // 状态变更事件
xhr.onloadstart         // 开始加载事件
xhr.onload              // 加载完成事件
xhr.onloadend           // 加载结束事件
xhr.onprogress          // 进度事件
xhr.onerror             // 错误事件
xhr.onabort             // 中止事件
xhr.ontimeout           // 超时事件
```

### 9.3 方法 (Methods)
```javascript
xhr.open(method, url [, async [, user [, password]]])    // 初始化请求
xhr.send([body])                                         // 发送请求
xhr.setRequestHeader(name, value)                        // 设置请求头
xhr.getResponseHeader(name)                              // 获取响应头
xhr.getAllResponseHeaders()                              // 获取所有响应头
xhr.abort()                                              // 中止请求
xhr.overrideMimeType(mimeType)                          // 覆盖MIME类型
```

### 9.4 常量 (Constants)
```javascript
XMLHttpRequest.UNSENT = 0           // 未发送
XMLHttpRequest.OPENED = 1           // 已打开
XMLHttpRequest.HEADERS_RECEIVED = 2 // 已接收头部
XMLHttpRequest.LOADING = 3          // 正在加载
XMLHttpRequest.DONE = 4             // 完成
```

## 10. curl_utils.c 深度分析

### 10.1 架构设计
curl_utils.c是PEDK中libcurl与libuv事件循环集成的核心模块，实现了真正的异步HTTP通信：

```
┌─────────────────────────────────────────────────────────────┐
│                    curl_utils.c 架构                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ xhr.c       │  │ timer.c     │  │ 其他模块     │         │
│  │ (HTTP请求)  │  │ (定时器)    │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│         │               │               │                  │
│         └───────────────┼───────────────┘                  │
│                         │                                  │
│  ┌─────────────────────────────────────────────────────────┤
│  │              STPeSFCurlPrivate                          │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │ CURL* curl_h│  │CURLM* curlm_h│  │uv_timer_t   │     │
│  │  │ (单个请求)  │  │ (多路复用)   │  │ (超时处理)   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  └─────────────────────────────────────────────────────────┤
│                         │                                  │
│  ┌─────────────────────────────────────────────────────────┤
│  │              libuv 事件循环集成                          │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  │ uv_poll_t   │  │ Socket监听   │  │ 定时器管理   │     │
│  │  │ (Socket轮询)│  │ (读写事件)   │  │ (超时控制)   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │
│  └─────────────────────────────────────────────────────────┤
│                         │                                  │
│  ┌─────────────────────────────────────────────────────────┤
│  │                  网络层                                 │
│  │     TCP Socket ←→ HTTP/HTTPS ←→ 外部服务器              │
│  └─────────────────────────────────────────────────────────┤
└─────────────────────────────────────────────────────────────┘
```

### 10.2 核心数据结构
```c
typedef struct {
    uint32_t magic;                     // 魔数标识
    void *arg;                          // 用户数据指针 (指向STPeSFXhr)
    pesfCurlDoneCallback done_cb;       // 完成回调函数

    CURL*  curl_h;                      // libcurl easy句柄
    CURLM* curlm_h;                     // libcurl multi句柄
    uv_timer_t uv_timer;                // libuv定时器
    struct uv_loop_s* uv_loop;          // libuv事件循环
} STPeSFCurlPrivate;

typedef struct {
    uv_poll_t poll;                     // libuv轮询句柄
    curl_socket_t sockfd;               // socket文件描述符
    STPeSFCurlPrivate* private;         // 私有数据指针
} STPeSFUVPollContext;
```

### 10.3 Socket事件处理机制

#### 10.3.1 Socket回调注册
```c
static int curlHandleSocket(CURL *easy, curl_socket_t s, int action, void* userp, void* socketp) {
    STPeSFCurlPrivate* private = userp;

    switch (action) {
        case CURL_POLL_IN:      // 可读事件
        case CURL_POLL_OUT:     // 可写事件
        case CURL_POLL_INOUT:   // 读写事件
        {
            // 创建或复用uv_poll上下文
            STPeSFUVPollContext* poll_ctx = socketp ? socketp : createUvPollCtx(s, private);
            curl_multi_assign(private->curlm_h, s, (void *) poll_ctx);

            // 设置监听事件类型
            int events = 0;
            if (action != CURL_POLL_IN)  events |= UV_WRITABLE;
            if (action != CURL_POLL_OUT) events |= UV_READABLE;

            // 启动uv轮询
            uv_poll_start(&poll_ctx->poll, events, uvPollCallback);
            break;
        }
        case CURL_POLL_REMOVE:  // 移除socket监听
        {
            if (socketp) {
                STPeSFUVPollContext* poll_ctx = socketp;
                uv_poll_stop(&poll_ctx->poll);
                curl_multi_assign(private->curlm_h, s, NULL);
                uv_close((uv_handle_t* ) &poll_ctx->poll, uvPollCloseCallback);
            }
            break;
        }
    }
    return 0;
}
```

#### 10.3.2 Socket事件处理
```c
static void uvPollCallback(uv_poll_t* handle, int status, int events) {
    STPeSFUVPollContext* poll_ctx = handle->data;
    STPeSFCurlPrivate* private = poll_ctx->private;

    // 将libuv事件转换为curl事件标志
    int flags = 0;
    if (events & UV_READABLE)  flags |= CURL_CSELECT_IN;
    if (events & UV_WRITABLE)  flags |= CURL_CSELECT_OUT;

    // 通知curl处理socket事件
    int running_handles;
    curl_multi_socket_action(private->curlm_h, poll_ctx->sockfd, flags, &running_handles);

    // 检查是否有请求完成
    checkCurlmultiInfo(private);
}
```

### 10.4 定时器管理

#### 10.4.1 定时器回调设置
```c
static int curlUvStartStop(CURLM* multi, long timeout_ms, void* userp) {
    STPeSFCurlPrivate* p = userp;

    if (timeout_ms < 0) {
        // 停止定时器
        uv_timer_stop(&(p->uv_timer));
    } else {
        // 启动定时器 (最小1ms)
        if (timeout_ms == 0) timeout_ms = 1;
        uv_timer_start(&(p->uv_timer), uvTimerCallback, timeout_ms, 0);
    }
    return 0;
}
```

#### 10.4.2 定时器超时处理
```c
static void uvTimerCallback(uv_timer_t* handle) {
    STPeSFCurlPrivate* private = handle->data;

    // 处理curl超时事件
    int running_handles;
    curl_multi_socket_action(private->curlm_h, CURL_SOCKET_TIMEOUT, 0, &running_handles);

    // 检查完成的请求
    checkCurlmultiInfo(private);
}
```

### 10.5 请求完成处理
```c
static void checkCurlmultiInfo(STPeSFCurlPrivate* private) {
    CURLMsg* message;
    int pending = -1;

    // 检查multi句柄中的消息队列
    while ((message = curl_multi_info_read(private->curlm_h, &pending))) {
        if (message->msg == CURLMSG_DONE) {
            // 请求完成，调用用户回调
            if (private->done_cb) {
                private->done_cb(message, private->arg);
            }

            // 从multi句柄中移除完成的请求
            curl_multi_remove_handle(private->curlm_h, message->easy_handle);
        }
    }
}
```

### 10.6 初始化和配置

#### 10.6.1 全局初始化
```c
static pthread_once_t curl__init_once = PTHREAD_ONCE_INIT;

static void pesfCurlGlobalInitOnce(void) {
    curl_global_init(CURL_GLOBAL_ALL);
}

static void pesfCurlGlobalInit(void) {
    pthread_once(&curl__init_once, pesfCurlGlobalInitOnce);
}
```

#### 10.6.2 Easy句柄配置
```c
CURL* pesfCurlEasyInit(CURL* curl_h) {
    pesfCurlGlobalInit();

    if (curl_h == NULL)
        curl_h = curl_easy_init();

    // 基本配置
    curl_easy_setopt(curl_h, CURLOPT_USERAGENT, "pesf/1.0/"__DATE__);
    curl_easy_setopt(curl_h, CURLOPT_FOLLOWLOCATION, 1L);

    // 协议限制 (只允许HTTP/HTTPS)
    curl_easy_setopt(curl_h, CURLOPT_PROTOCOLS_STR, "http,https");
    curl_easy_setopt(curl_h, CURLOPT_REDIR_PROTOCOLS_STR, "http,https");

    // TLS配置
    curl_easy_setopt(curl_h, CURLOPT_SSLVERSION, CURL_SSLVERSION_TLSv1_1);
    curl_easy_setopt(curl_h, CURLOPT_SSL_VERIFYPEER, 0L);  // 不验证证书

    return curl_h;
}
```

#### 10.6.3 Multi句柄配置
```c
CURLM* pesfGetCurlm(STPeSFCurlPrivate* private) {
    pesfCurlGlobalInit();

    CURLM* curlm_h = curl_multi_init();

    // 设置socket回调
    curl_multi_setopt(curlm_h, CURLMOPT_SOCKETFUNCTION, curlHandleSocket);
    curl_multi_setopt(curlm_h, CURLMOPT_SOCKETDATA, private);

    // 设置定时器回调
    curl_multi_setopt(curlm_h, CURLMOPT_TIMERFUNCTION, curlUvStartStop);
    curl_multi_setopt(curlm_h, CURLMOPT_TIMERDATA, private);

    return curlm_h;
}
```

### 10.7 与xhr.c的协作流程
```
xhr.c 发起请求
    ↓
1. 创建STPeSFCurlPrivate结构
    ↓
2. 初始化curl easy/multi句柄
    ↓
3. 绑定到libuv事件循环
    ↓
4. 调用curl_multi_add_handle()
    ↓
5. curl调用curlHandleSocket()注册socket事件
    ↓
6. libuv监听socket读写事件
    ↓
7. 事件触发时调用uvPollCallback()
    ↓
8. curl_multi_socket_action()处理数据
    ↓
9. 数据通过回调函数传递给xhr.c
    ↓
10. 请求完成时调用curlm__done_cb()
    ↓
11. xhr.c触发JavaScript事件
```

## 11. MFP端HTTP服务器实现 (pedksrv.c)

### 11.1 服务器架构
MFP端运行一个完整的HTTP服务器，专门处理来自PEDK应用的HTTP请求：

```
┌─────────────────────────────────────────────────────────────┐
│                    MFP HTTP服务器架构                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  HTTP请求路由                            │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │ users/login │  │ apps/upload │  │ apps/list   │     │ │
│  │  │ (用户登录)  │  │ (应用上传)  │  │ (应用列表)  │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                         │                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  请求处理层                              │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │ 头部解析    │  │ 请求体处理  │  │ 响应生成    │     │ │
│  │  │ (权限验证)  │  │ (文件上传)  │  │ (JSON返回)  │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                         │                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                PEDK通信层                                │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │ │
│  │  │ 消息封装    │  │ 队列发送    │  │ 响应处理    │     │ │
│  │  │ (JSON格式)  │  │ (异步)      │  │ (回调)      │     │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘     │ │
│  └─────────────────────────────────────────────────────────┘ │
│                         │                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  网络传输层                              │ │
│  │     TCP Socket ←→ HTTP协议 ←→ PEDK Runtime              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 11.2 URL路由表
pedksrv.c使用静态路由表管理所有API端点：

```c
static URL_TABLE_S s_request_table[] = {
    // 用户管理
    { "users/login",       SET_BITS(HTTP_GET, HTTP_POST),     0,         pedk_users_login},
    { "users/logout",      SET_BIT(HTTP_GET),                 ePRIVILEGE, pedk_users_logout},

    // 应用管理
    { "apps/info",         SET_BIT(HTTP_GET),                 ePRIVILEGE, pedk_apps_info},
    { "apps/upload",       SET_BIT(HTTP_POST),                ePRIVILEGE, pedk_apps_upload},
    { "apps/list",         SET_BIT(HTTP_GET),                 0,         pedk_apps_list},
    { "apps/delete",       SET_BIT(HTTP_DELETE),              ePRIVILEGE, pedk_apps_delete},
    { "apps/deleteMany",   SET_BIT(HTTP_DELETE),              ePRIVILEGE, pedk_apps_delete_many},

    // 系统管理
    { "system/info",       SET_BIT(HTTP_GET),                 0,         pedk_system_info},
    { "system/reboot",     SET_BIT(HTTP_POST),                ePRIVILEGE, pedk_system_reboot},

    // 文件管理
    { "files/upload",      SET_BIT(HTTP_POST),                ePRIVILEGE, pedk_files_upload},
    { "files/download",    SET_BIT(HTTP_GET),                 ePRIVILEGE, pedk_files_download},
};

typedef struct url_table {
    const char*  request_url;           // URL路径
    uint64_t     request_method_mask;   // 支持的HTTP方法
    uint32_t     request_flags;         // 权限标志
    HANDLE_FUNC  request_handle;        // 处理函数
} URL_TABLE_S;
```

### 11.3 请求处理流程

#### 11.3.1 URL匹配和权限验证
```c
static URL_TABLE_S* url_match(const char* url, enum http_method method, const char* token) {
    // 使用二分查找匹配URL
    URL_TABLE_S* entry = bsearch(url + LEN(URL_PREFIX), s_request_table,
                                ARRAY_SIZE(s_request_table), sizeof(s_request_table[0]), url_compar);

    if (entry == NULL) return NULL;

    // 验证HTTP方法
    if (!(SET_BIT(method) & entry->request_method_mask)) return NULL;

    // 验证权限 (需要token的API)
    if ((entry->request_flags & ePRIVILEGE) && check_token(token)) return NULL;

    return entry;
}
```

#### 11.3.2 请求头部处理
```c
static int32_t pedksrv_process_headers(HTTP_TASK_S* ptask, enum http_method method,
                                      const char* url, uint64_t content_length) {
    PRIV_INFO_S* priv;
    const char* bearer_token;
    URL_TABLE_S* table_entry;

    // 获取Authorization头部
    bearer_token = http_task_search_header_field(ptask, HEADER_AUTHORIZATION);

    // 匹配URL和验证权限
    table_entry = url_match(url, method, bearer_token);
    if (table_entry == NULL) return -1;

    // 分配私有数据结构
    size_t body_len = (content_length != ULLONG_MAX ? content_length : 0);
    size_t body_buf_len = (body_len > 0 ? body_len + 1 : 0);

    // 大文件处理策略
    int need_to_create_file = 0;
    if (body_len > MAXIMUM_FILE_LIMIT) {
        return -1;  // 文件过大
    } else if (body_len >= MAXIMUM_MEMORY_LIMIT) {
        need_to_create_file = 1;  // 使用临时文件
    }

    // 分配内存或创建临时文件
    if (need_to_create_file) {
        // 创建临时文件存储大请求体
        snprintf(priv->body_fname, sizeof(priv->body_fname), "/tmp/pedk_upload_%d", getpid());
        priv->body_fp = fopen(priv->body_fname, "w+b");
    } else {
        // 分配内存缓冲区
        priv = pi_malloc(sizeof(PRIV_INFO_S) + body_buf_len);
        priv->body_buflen = body_buf_len;
    }

    priv->table_entry = table_entry;
    priv->body_ctlen = body_len;
    ptask->priv_subclass[5] = priv;

    return 0;
}
```

#### 11.3.3 请求体数据接收
```c
static int32_t pedksrv_process_reqbody(HTTP_TASK_S* ptask, const uint8_t* data, size_t len) {
    PRIV_INFO_S* priv = ptask->priv_subclass[5];

    if (priv->body_fp) {
        // 写入临时文件
        if (fwrite(data, 1, len, priv->body_fp) != len) {
            return -1;
        }
    } else {
        // 写入内存缓冲区
        if (priv->body_received + len > priv->body_buflen - 1) {
            return -1;  // 缓冲区溢出
        }
        memcpy(priv->body_buf + priv->body_received, data, len);
    }

    priv->body_received += len;
    return 0;
}
```

### 11.4 与PEDK通信机制

#### 11.4.1 消息封装和发送
```c
static int32_t send_http_request_to_pedk(PRIV_INFO_S* priv, HTTP_TASK_S* ptask) {
    cJSON* json_root = cJSON_CreateObject();
    cJSON* json_hdrs = cJSON_CreateObject();
    char* json_str;
    char* hdrs_str;

    // 封装HTTP头部
    // 遍历所有请求头部，添加到JSON对象

    hdrs_str = cJSON_PrintUnformatted(json_hdrs);

    // 创建完整的请求消息
    cJSON_AddStringToObject(json_root, "url", priv->url);
    cJSON_AddStringToObject(json_root, "method", "Post");
    cJSON_AddStringToObject(json_root, "header", hdrs_str);
    cJSON_AddStringToObject(json_root, "body", (char *)priv->body_buf);

    json_str = cJSON_PrintUnformatted(json_root);

    // 发送到PEDK运行时
    pedk_mgr_send_msg_to_runenv(MSG_MODULE_HTTP, MSG_HTTP_SUB, 0,
                               (uint8_t *)json_str, strlen(json_str));

    // 清理资源
    cJSON_Delete(json_root);
    cJSON_Delete(json_hdrs);
    free(hdrs_str);
    free(json_str);

    return 0;
}
```

#### 11.4.2 响应处理回调
```c
static int32_t http_pedkapi_handler(MAIN_MSG_E main, SUB_MSG_E sub, int32_t respond,
                                   uint8_t* buf, int32_t bufsize, void* ctx) {
    NET_CTX_S* net_ctx = (NET_CTX_S*)ctx;

    // 解析PEDK返回的响应数据
    cJSON* json_response = cJSON_Parse((char*)buf);
    if (json_response == NULL) return -1;

    // 提取响应信息
    cJSON* status_code = cJSON_GetObjectItem(json_response, "status");
    cJSON* response_body = cJSON_GetObjectItem(json_response, "body");
    cJSON* response_headers = cJSON_GetObjectItem(json_response, "headers");

    // 构造HTTP响应
    // 这里需要找到对应的HTTP_TASK_S并发送响应
    // (实际实现中可能需要维护请求-响应映射表)

    cJSON_Delete(json_response);
    return 0;
}
```

### 11.5 具体API实现示例

#### 11.5.1 用户登录API
```c
static int pedk_users_login(HTTP_TASK_S* ptask, const char** reply_body,
                           const char** reply_code, const char** reply_type) {
    PRIV_INFO_S* priv = ptask->priv_subclass[5];
    cJSON* json_request;
    cJSON* username, *password;

    // 解析请求体
    json_request = cJSON_Parse((char*)priv->body_buf);
    if (json_request == NULL) {
        *reply_code = "400 Bad Request";
        *reply_body = "{\"error\":\"Invalid JSON\"}";
        return strlen(*reply_body);
    }

    username = cJSON_GetObjectItem(json_request, "username");
    password = cJSON_GetObjectItem(json_request, "password");

    // 验证用户凭据
    if (validate_user_credentials(username->valuestring, password->valuestring)) {
        // 生成访问令牌
        char* token = generate_access_token(username->valuestring);

        // 构造成功响应
        cJSON* response = cJSON_CreateObject();
        cJSON_AddStringToObject(response, "status", "success");
        cJSON_AddStringToObject(response, "token", token);

        *reply_body = cJSON_PrintUnformatted(response);
        *reply_code = "200 OK";

        cJSON_Delete(response);
        free(token);
    } else {
        *reply_code = "401 Unauthorized";
        *reply_body = "{\"error\":\"Invalid credentials\"}";
    }

    cJSON_Delete(json_request);
    return strlen(*reply_body);
}
```

#### 11.5.2 应用上传API
```c
static int pedk_apps_upload(HTTP_TASK_S* ptask, const char** reply_body,
                           const char** reply_code, const char** reply_type) {
    PRIV_INFO_S* priv = ptask->priv_subclass[5];

    // 处理multipart/form-data上传
    if (priv->body_fp) {
        // 大文件：从临时文件读取
        fseek(priv->body_fp, 0, SEEK_SET);
        // 解析multipart数据...
    } else {
        // 小文件：从内存缓冲区读取
        // 解析multipart数据...
    }

    // 验证应用包格式
    if (!validate_app_package(app_data, app_size)) {
        *reply_code = "400 Bad Request";
        *reply_body = "{\"error\":\"Invalid app package\"}";
        return strlen(*reply_body);
    }

    // 安装应用
    char app_id[64];
    if (install_app_package(app_data, app_size, app_id) == 0) {
        // 构造成功响应
        cJSON* response = cJSON_CreateObject();
        cJSON_AddStringToObject(response, "status", "success");
        cJSON_AddStringToObject(response, "app_id", app_id);

        *reply_body = cJSON_PrintUnformatted(response);
        *reply_code = "200 OK";

        cJSON_Delete(response);
    } else {
        *reply_code = "500 Internal Server Error";
        *reply_body = "{\"error\":\"Installation failed\"}";
    }

    return strlen(*reply_body);
}
```

### 11.6 服务器生命周期管理

#### 11.6.1 服务器初始化
```c
int32_t pedksrv_prolog(NET_CTX_S* net_ctx) {
    // 注册PEDK消息处理器
    int32_t ret = pedk_mgr_register_handler(MSG_MODULE_HTTP, http_pedkapi_handler, (void *)net_ctx);
    if (ret != 0) return ret;

    // 分配服务器上下文
    s_pedksrv_ctx = (PEDKSRV_CTX_S *)pi_zalloc(sizeof(PEDKSRV_CTX_S));
    if (s_pedksrv_ctx == NULL) return -1;

    // 初始化URL路由表 (已排序，支持二分查找)
    qsort(s_request_table, ARRAY_SIZE(s_request_table),
          sizeof(s_request_table[0]), url_table_comparator);

    return 0;
}
```

#### 11.6.2 HTTP任务构造
```c
int32_t pedksrv_construct(void* obj) {
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    // 设置回调函数
    ptask->headers_complete_callback = pedksrv_process_headers;
    ptask->reqbody_received_callback = pedksrv_process_reqbody;
    ptask->request_complete_callback = pedksrv_process_request;

    return 0;
}
```

#### 11.6.3 资源清理
```c
void pedksrv_destruct(void* obj) {
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;
    PRIV_INFO_S* priv;

    if (ptask != NULL && ptask->priv_subclass[5] != NULL) {
        priv = (PRIV_INFO_S *)ptask->priv_subclass[5];

        // 清理临时文件
        if (priv->body_fp) {
            fclose(priv->body_fp);
            unlink(priv->body_fname);
        }

        // 释放内存
        pi_free(ptask->priv_subclass[5]);
        ptask->priv_subclass[5] = NULL;
    }
}
```

### 5.1 HTTP服务器架构
MFP端运行一个HTTP服务器来接收来自PEDK的请求，主要处理文件上传等操作。

### 5.2 文件上传处理

#### 5.2.1 multipart/form-data解析
```c
// 处理PEDK应用上传
HANDLE_DEFINITION(pedk_apps_upload)
{
    char* form_boundary;
    char boundary_delimiter[72];
    int boundary_delimiter_len;
    const char* content_type;
    char app_path[128] = {0};
    char file_name[64];
    char auto_boot[64];
    char env[128];
    
    // 解析multipart边界
    content_type = http_task_search_header_field(ptask, HEADER_CONTENT_TYPE);
    form_boundary = strstr(content_type, "boundary=");
    boundary_delimiter_len = snprintf(boundary_delimiter, sizeof(boundary_delimiter), 
                                     "\r\n--%s", form_boundary + 9);
    
    // 提取表单数据
    ret = multipart_form_get_data(priv, boundary_delimiter, boundary_delimiter_len, 
                                 "auto-boot", auto_boot, sizeof(auto_boot));
    ret = multipart_form_get_data(priv, boundary_delimiter, boundary_delimiter_len, 
                                 "env", env, sizeof(env));
    
    // 写入文件
    ret = multipart_form_write_file(priv, boundary_delimiter, boundary_delimiter_len, 
                                   "file", file_name, sizeof(file_name), 
                                   app_path, sizeof(app_path));
    
    // 安装应用
    ret = pedk_download_app_install(app_path, app_whitelist, sizeof(app_whitelist), auto_boot);
}
```

#### 5.2.2 文件写入处理
```c
static int multipart_form_write_file(PRIV_INFO_S* priv, const char* delimiter, 
                                    int delimiter_len, const char* name, 
                                    OUT char* fname, size_t fname_size, 
                                    OUT char* app_path, size_t app_path_size)
{
    // 解析文件名
    // 创建目标文件路径 /tmp/filename
    snprintf(app_path, app_path_size, "/tmp/%s", fname);
    
    // 检查文件类型 (必须是.tar文件)
    if (strstr(app_path, ".tar") == NULL) {
        NET_WARN("file is not *.tar!");
        return -1;
    }
    
    // 打开文件进行写入
    if ((fp = fopen(app_path, "wb")) == NULL) {
        NET_WARN("open error!");
        return -1;
    }
    
    // 写入文件数据
    // 处理大文件的分块写入
    // 验证数据完整性
}
```

## 6. 完整通信流程

### 6.1 Upload请求流程
```
1. JavaScript调用: xhr.send(formData)
   ↓
2. PEDK Runtime: net_xhr_send()
   ↓
3. libcurl: 发起HTTP POST请求
   ↓
4. MFP HTTP服务器: 接收请求
   ↓
5. pedksrv.c: 解析multipart数据
   ↓
6. 文件系统: 保存上传文件
   ↓
7. 业务处理: 应用安装/处理
   ↓
8. HTTP响应: 返回处理结果
   ↓
9. libcurl: 接收响应
   ↓
10. PEDK Runtime: 触发JavaScript事件
    ↓
11. JavaScript: onload/onerror回调
```

### 6.2 数据流转
```
JavaScript FormData → libcurl POST → HTTP multipart → 文件系统 → 业务逻辑
```

## 7. 关键技术点

### 7.1 异步处理
- 使用libuv事件循环实现非阻塞I/O
- libcurl multi接口支持并发请求
- JavaScript事件驱动模型

### 7.2 内存管理
- 动态缓冲区(DynBuf)管理响应数据
- 大文件上传时使用临时文件避免内存溢出
- QuickJS垃圾回收机制

### 7.3 错误处理
- 多层次错误检查和报告
- 超时机制防止请求挂起
- 资源清理确保系统稳定

### 7.4 安全机制
- 文件类型验证(.tar文件)
- 路径安全检查
- 数字签名验证

## 11. 内存管理和资源清理

### 11.1 对象生命周期管理
```c
// 垃圾回收标记函数
static void net_xhr_mark(JSRuntime *rt, JSValueConst val, JS_MarkFunc *mark_func) {
    STPeSFXhr* x = JS_GetOpaque(val, net_xhr_class_id);
    if (x) {
        // 标记所有JavaScript值防止被回收
        for (int i = 0; i < XHR_EVENT_MAX; i++)
            JS_MarkValue(rt, x->events[i], mark_func);
        JS_MarkValue(rt, x->status.status, mark_func);
        JS_MarkValue(rt, x->status.status_text, mark_func);
        JS_MarkValue(rt, x->result.url, mark_func);
        JS_MarkValue(rt, x->result.headers, mark_func);
        JS_MarkValue(rt, x->result.response, mark_func);
        JS_MarkValue(rt, x->result.response_text, mark_func);
        JS_MarkValue(rt, x->user_path, mark_func);
        JS_MarkValue(rt, x->overwrite, mark_func);
    }
}

// 对象析构函数
static void net_xhr_finalizer(JSRuntime *rt, JSValue val) {
    STPeSFXhr* x = JS_GetOpaque(val, net_xhr_class_id);
    if (x) {
        // 清理curl资源
        if (x->curl_private.curl_h) {
            if (x->curl_private.curlm_h)
                curl_multi_remove_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
            curl_easy_cleanup(x->curl_private.curl_h);
        }

        // 清理HTTP头部链表
        if (x->slist)
            curl_slist_free_all(x->slist);

        // 清理动态缓冲区
        dbuf_free(&x->result.hbuf);
        dbuf_free(&x->result.bbuf);

        // 释放JavaScript值
        for (int i = 0; i < XHR_EVENT_MAX; i++)
            JS_FreeValue(rt, x->events[i]);
        JS_FreeValue(rt, x->status.status);
        JS_FreeValue(rt, x->status.status_text);
        JS_FreeValue(rt, x->result.url);
        JS_FreeValue(rt, x->result.headers);
        JS_FreeValue(rt, x->result.response);
        JS_FreeValue(rt, x->result.response_text);
        JS_FreeValue(rt, x->user_path);
        JS_FreeValue(rt, x->overwrite);

        // 释放状态字符串
        if (x->status.raw)
            free(x->status.raw);

        // 释放结构体内存
        free(x);
    }
}
```

### 11.2 动态缓冲区管理
xhr.c使用QuickJS的DynBuf来管理可变长度的数据：
- `x->result.hbuf`: 存储HTTP响应头部
- `x->result.bbuf`: 存储HTTP响应体数据
- 自动扩容，避免缓冲区溢出
- 在对象销毁时自动释放

## 12. 错误处理机制

### 12.1 多层错误检查
```c
// 参数验证
static STPeSFXhr* net_xhr_get(JSContext *ctx, JSValueConst obj) {
    return JS_GetOpaque2(ctx, obj, net_xhr_class_id);
}

// 状态检查
if (x->ready_state < XHR_RSTATE_OPENED) {
    // 只有在正确状态才能执行操作
}

// curl错误处理
CURLcode result = curl_easy_perform(x->curl_private.curl_h);
if (result != CURLE_OK) {
    maybe_emit_event(x, XHR_EVENT_ERROR, JS_UNDEFINED);
}
```

### 12.2 异常安全
- 所有JavaScript字符串都通过`JS_ToCString()`获取，使用后立即`JS_FreeCString()`释放
- 所有JavaScript值都通过`JS_DupValue()`复制，避免悬空引用
- curl操作失败时正确清理资源和重置状态

## 13. 技术特点总结

### 13.1 架构优势
1. **标准兼容**: 完整实现XMLHttpRequest Level 1标准
2. **异步非阻塞**: 基于libuv事件循环，不阻塞JavaScript执行
3. **内存高效**: 使用动态缓冲区，按需分配内存
4. **错误健壮**: 多层错误检查和异常安全设计
5. **扩展性强**: 支持PEDK特有的文件下载功能

### 13.2 性能特点
1. **零拷贝**: 响应数据直接从curl缓冲区传递给JavaScript
2. **事件驱动**: 避免轮询，减少CPU占用
3. **并发支持**: curl multi接口支持多个并发请求
4. **内存复用**: 动态缓冲区可重复使用

### 13.3 当前限制
1. **Upload对象**: 尚未实现，返回`JS_UNDEFINED`
2. **withCredentials**: 未实现跨域凭据支持
3. **FormData支持**: send()方法目前只支持字符串数据
4. **进度事件**: 下载进度事件被注释掉
5. **同步模式**: 虽然支持但不推荐使用

### 13.4 与标准的差异
1. **扩展属性**:
   - `userSavePath`: 文件保存路径
   - `overwrite`: 是否覆盖文件
2. **文件下载**: 支持直接保存到文件系统
3. **超时单位**: 使用毫秒而非秒

## 14. 使用示例

### 14.1 基本HTTP请求
```javascript
var xhr = new XMLHttpRequest();
xhr.open('GET', 'https://api.example.com/data', true);
xhr.responseType = 'json';

xhr.onload = function() {
    if (xhr.status === 200) {
        console.log('Data:', xhr.response);
    }
};

xhr.onerror = function() {
    console.error('Request failed');
};

xhr.send();
```

### 14.2 文件上传
```javascript
var xhr = new XMLHttpRequest();
xhr.open('POST', 'http://mfp-server/upload', true);
xhr.setRequestHeader('Content-Type', 'application/octet-stream');

xhr.onload = function() {
    console.log('Upload completed:', xhr.statusText);
};

xhr.send(fileData);
```

### 14.3 文件下载 (PEDK扩展)
```javascript
var xhr = new XMLHttpRequest();
xhr.userSavePath = '/storage/downloads/file.pdf';
xhr.overwrite = true;
xhr.timeout = 300000; // 5分钟

xhr.onload = function() {
    console.log('File saved to:', xhr.userSavePath);
};

xhr.open('GET', 'https://example.com/file.pdf', true);
xhr.send();
```

## 12. 完整通信流程技术分析

### 12.1 端到端通信架构

#### 12.1.1 系统整体架构图
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        PEDK 端到端通信架构                                  │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  PEDK Runtime (JavaScript端)              MFP固件端 (HTTP服务器)            │
│  ┌─────────────────────────────┐         ┌─────────────────────────────┐     │
│  │  ┌─────────────────────────┐ │         │  ┌─────────────────────────┐ │     │
│  │  │     应用层 (http.js)    │ │         │  │     路由层 (pedksrv.c)  │ │     │
│  │  │  ┌─────────────────────┐│ │         │  │  ┌─────────────────────┐│ │     │
│  │  │  │ fetch() / wget()    ││ │◀────────▶│  │  │ URL路由表           ││ │     │
│  │  │  │ Promise封装         ││ │  HTTP   │  │  │ API端点管理         ││ │     │
│  │  │  └─────────────────────┘│ │  请求   │  │  └─────────────────────┘│ │     │
│  │  └─────────────────────────┘ │         │  └─────────────────────────┘ │     │
│  │              │                │         │              │                │     │
│  │  ┌─────────────────────────┐ │         │  ┌─────────────────────────┐ │     │
│  │  │     API层 (xhr.c)       │ │         │  │   处理层 (HTTP处理器)   │ │     │
│  │  │  ┌─────────────────────┐│ │         │  │  ┌─────────────────────┐│ │     │
│  │  │  │ XMLHttpRequest      ││ │         │  │  │ 头部解析/权限验证   ││ │     │
│  │  │  │ 标准Web API         ││ │         │  │  │ 请求体处理/文件上传 ││ │     │
│  │  │  │ 事件处理机制        ││ │         │  │  │ 响应生成/错误处理   ││ │     │
│  │  │  └─────────────────────┘│ │         │  │  └─────────────────────┘│ │     │
│  │  └─────────────────────────┘ │         │  └─────────────────────────┘ │     │
│  │              │                │         │              │                │     │
│  │  ┌─────────────────────────┐ │         │  ┌─────────────────────────┐ │     │
│  │  │   传输层 (curl_utils.c) │ │         │  │   通信层 (pedk_mgr)     │ │     │
│  │  │  ┌─────────────────────┐│ │         │  │  ┌─────────────────────┐│ │     │
│  │  │  │ libcurl集成         ││ │         │  │  │ 消息封装/队列发送   ││ │     │
│  │  │  │ libuv事件循环       ││ │         │  │  │ JSON格式化          ││ │     │
│  │  │  │ 异步I/O管理         ││ │         │  │  │ 协议处理            ││ │     │
│  │  │  └─────────────────────┘│ │         │  │  └─────────────────────┘│ │     │
│  │  └─────────────────────────┘ │         │  └─────────────────────────┘ │     │
│  └─────────────────────────────┘         └─────────────────────────────┘     │
│              │                                         │                     │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                        网络传输层                                   │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ TCP/IP协议  │  │ HTTP/HTTPS  │  │ TLS/SSL加密 │  │ 负载均衡  │   │     │
│  │  │ 连接管理    │  │ 协议处理    │  │ 安全传输    │  │ 故障转移  │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 12.1.2 消息流转架构图
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          消息流转架构                                       │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                        请求流 (Request Flow)                        │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                                                             │
│  JavaScript应用                                                             │
│       │                                                                     │
│       ▼ 1. API调用                                                          │
│  ┌─────────────┐                                                            │
│  │  http.js    │ 2. 创建XHR对象                                             │
│  │  fetch()    │────────────────────┐                                       │
│  └─────────────┘                    │                                       │
│                                     ▼                                       │
│                              ┌─────────────┐                                │
│                              │   xhr.c     │ 3. 配置请求                    │
│                              │ open/send() │────────────────────┐           │
│                              └─────────────┘                    │           │
│                                                                 ▼           │
│                                                          ┌─────────────┐    │
│                                                          │curl_utils.c │    │
│                                                          │ 网络传输    │    │
│                                                          └─────────────┘    │
│                                                                 │           │
│                                                                 ▼           │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                      网络层 (Network Layer)                         │     │
│  │                                                                     │     │
│  │  HTTP请求 ──▶ 路由器 ──▶ 防火墙 ──▶ 负载均衡 ──▶ MFP服务器          │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                                                 │           │
│                                                                 ▼           │
│                                                          ┌─────────────┐    │
│                                                          │ pedksrv.c   │    │
│                                                          │ URL路由     │    │
│                                                          └─────────────┘    │
│                                                                 │           │
│                                                                 ▼           │
│                                                          ┌─────────────┐    │
│                                                          │ 业务处理器   │    │
│                                                          │ API逻辑     │    │
│                                                          └─────────────┘    │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                        响应流 (Response Flow)                       │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                                                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐   │
│  │ 业务处理器   │───▶│ pedksrv.c   │───▶│ 网络层      │───▶│curl_utils.c │   │
│  │ 生成响应    │    │ HTTP响应    │    │ 数据传输    │    │ 接收处理    │   │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘   │
│                                                                 │           │
│                                                                 ▼           │
│                              ┌─────────────┐    ┌─────────────┐             │
│                              │   xhr.c     │◀───│  事件触发   │             │
│                              │ 状态更新    │    │  回调处理   │             │
│                              └─────────────┘    └─────────────┘             │
│                                     │                                       │
│                                     ▼                                       │
│  ┌─────────────┐                                                            │
│  │  http.js    │◀───────────────────┘                                       │
│  │ Promise解析 │ 4. 事件回调                                                │
│  └─────────────┘                                                            │
│       │                                                                     │
│       ▼ 5. 返回结果                                                         │
│  JavaScript应用                                                             │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### 12.2 关键技术集成点

#### 12.2.1 技术栈集成架构图
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        PEDK HTTP 技术栈集成架构                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                    JavaScript运行时层                               │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ QuickJS     │  │ 事件系统    │  │ 内存管理    │  │ 异常处理  │   │     │
│  │  │ 引擎        │  │ Event Loop  │  │ GC集成      │  │ 错误转换  │   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ 字节码  │ │  │ │ 事件队列│ │  │ │ 对象池  │ │  │ │ 栈跟踪│ │   │     │
│  │  │ │ 执行    │ │  │ │ 调度器  │ │  │ │ 引用计数│ │  │ │ 映射  │ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                    │                                         │
│                                    ▼ 桥接层                                  │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                      JavaScript-C 桥接层                            │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ 属性绑定    │  │ 方法调用    │  │ 事件触发    │  │ 类型转换  │   │     │
│  │  │ Magic Number│  │ 参数传递    │  │ 回调管理    │  │ 数据映射  │   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ Getter  │ │  │ │ 参数校验│ │  │ │ 事件队列│ │  │ │ 编码  │ │   │     │
│  │  │ │ Setter  │ │  │ │ 返回值  │ │  │ │ 异步触发│ │  │ │ 解码  │ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                    │                                         │
│                                    ▼ C实现层                                 │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                        C 实现层 (xhr.c)                             │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ 对象管理    │  │ 状态机      │  │ 缓冲区管理  │  │ 错误处理  │   │     │
│  │  │ 生命周期    │  │ Ready State │  │ 动态扩容    │  │ 异常捕获  │   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ 构造    │ │  │ │ 状态转换│ │  │ │ 内存池  │ │  │ │ 错误码│ │   │     │
│  │  │ │ 析构    │ │  │ │ 事件触发│ │  │ │ 零拷贝  │ │  │ │ 堆栈  │ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                    │                                         │
│                                    ▼ 网络层                                  │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                      网络传输层 (curl_utils.c)                      │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ libcurl     │  │ libuv       │  │ 协议处理    │  │ 安全传输  │   │     │
│  │  │ HTTP客户端  │  │ 事件循环    │  │ HTTP/HTTPS  │  │ TLS/SSL   │   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ Multi   │ │  │ │ Poll    │ │  │ │ 头部解析│ │  │ │ 证书  │ │   │     │
│  │  │ │ 接口    │ │  │ │ Timer   │ │  │ │ 状态码  │ │  │ │ 验证  │ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 12.2.2 异步I/O集成架构图
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                        异步I/O集成架构                                      │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                      libuv 事件循环核心                             │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ 事件循环    │  │ 句柄管理    │  │ 请求队列    │  │ 线程池    │   │     │
│  │  │ Event Loop  │  │ Handle Mgmt │  │ Request Q   │  │ Thread Pool│   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ 轮询    │ │  │ │ 活跃句柄│ │  │ │ 待处理  │ │  │ │ 工作线│ │   │     │
│  │  │ │ 调度    │ │  │ │ 引用计数│ │  │ │ 完成队列│ │  │ │ 程管理│ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                    │                                         │
│                                    ▼ 集成接口                                │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                      curl-libuv 集成层                              │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ Socket监听  │  │ 定时器管理  │  │ 回调处理    │  │ 错误处理  │   │     │
│  │  │ uv_poll_t   │  │ uv_timer_t  │  │ Callback    │  │ Error Hdl │   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ 读事件  │ │  │ │ 超时检测│ │  │ │ 数据回调│ │  │ │ 重试  │ │   │     │
│  │  │ │ 写事件  │ │  │ │ 周期任务│ │  │ │ 进度回调│ │  │ │ 恢复  │ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                    │                                         │
│                                    ▼ 网络接口                                │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                      libcurl Multi 接口                             │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ 多请求管理  │  │ 连接复用    │  │ 管道化      │  │ 负载均衡  │   │     │
│  │  │ Multi Handle│  │ Keep-Alive  │  │ Pipelining  │  │ Load Bal  │   │     │
│  │  │             │  │             │  │             │  │           │   │     │
│  │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌─────────┐ │  │ ┌───────┐ │   │     │
│  │  │ │ 并发控制│ │  │ │ 连接池  │ │  │ │ 请求队列│ │  │ │ 故障  │ │   │     │
│  │  │ │ 资源限制│ │  │ │ DNS缓存 │ │  │ │ 响应聚合│ │  │ │ 转移  │ │   │     │
│  │  │ └─────────┘ │  │ └─────────┘ │  │ └─────────┘ │  │ └───────┘ │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
│                                    │                                         │
│                                    ▼ 传输层                                  │
│  ┌─────────────────────────────────────────────────────────────────────┐     │
│  │                        网络传输层                                   │     │
│  │                                                                     │     │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐   │     │
│  │  │ TCP/IP      │  │ HTTP协议    │  │ TLS/SSL     │  │ 压缩编码  │   │     │
│  │  │ 连接管理    │  │ 状态处理    │  │ 安全传输    │  │ 数据处理  │   │     │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘   │     │
│  └─────────────────────────────────────────────────────────────────────┘     │
└─────────────────────────────────────────────────────────────────────────────┘
```

#### 12.2.3 JavaScript-C桥接层详细架构
- **QuickJS引擎**: 提供JavaScript运行时环境
- **属性绑定**: 通过magic number实现事件属性的getter/setter
- **内存管理**: 垃圾回收标记和对象生命周期管理
- **异常处理**: JavaScript异常与C错误码的双向转换

#### 12.2.4 异步I/O集成层技术要点
- **libuv事件循环**: 与PEDK运行时共享同一事件循环
- **curl multi接口**: 支持多个并发HTTP请求
- **Socket轮询**: uv_poll_t监听socket读写事件
- **定时器管理**: uv_timer_t处理请求超时

#### 12.2.5 网络协议层实现
- **HTTP/HTTPS支持**: 完整的HTTP/1.1协议实现
- **TLS安全**: 支持TLS v1.1+，可配置证书验证
- **协议限制**: 仅允许HTTP/HTTPS，防止协议攻击
- **重定向处理**: 自动跟随HTTP重定向

#### 12.2.6 数据处理层优化
- **动态缓冲区**: 自动扩容的响应数据缓冲
- **流式处理**: 支持大文件的流式上传/下载
- **编码转换**: 自动处理不同字符编码
- **MIME类型**: 支持多种响应数据格式

### 12.3 性能优化技术

#### 12.3.1 内存优化
```c
// 零拷贝数据传递
static size_t curl__data_cb(char *ptr, size_t size, size_t nmemb, void *userdata) {
    STPeSFXhr* x = userdata;
    size_t realsize = size * nmemb;

    // 直接将curl缓冲区数据追加到动态缓冲区，避免额外拷贝
    if (dbuf_put(&x->result.bbuf, (const uint8_t *) ptr, realsize))
        return -1;

    return realsize;
}

// 内存池复用
typedef struct {
    DynBuf hbuf;    // 头部缓冲区可重复使用
    DynBuf bbuf;    // 数据缓冲区可重复使用
} xhr_buffers;
```

#### 12.3.2 并发优化
```c
// 多请求并发处理
CURLM* curlm_h = curl_multi_init();
curl_multi_add_handle(curlm_h, xhr1->curl_h);
curl_multi_add_handle(curlm_h, xhr2->curl_h);
curl_multi_add_handle(curlm_h, xhr3->curl_h);

// 单一事件循环处理所有请求
uv_run(uv_loop, UV_RUN_DEFAULT);
```

#### 12.3.3 缓存优化
```c
// 连接复用
curl_easy_setopt(curl_h, CURLOPT_TCP_KEEPALIVE, 1L);
curl_easy_setopt(curl_h, CURLOPT_TCP_KEEPIDLE, 120L);
curl_easy_setopt(curl_h, CURLOPT_TCP_KEEPINTVL, 60L);

// DNS缓存
curl_easy_setopt(curl_h, CURLOPT_DNS_CACHE_TIMEOUT, 300L);
```

### 12.4 安全机制

#### 12.4.1 输入验证
```c
// URL验证
static bool validate_url(const char* url) {
    if (!url || strlen(url) == 0) return false;
    if (strncmp(url, "http://", 7) != 0 && strncmp(url, "https://", 8) != 0) return false;
    if (strlen(url) > MAX_URL_LENGTH) return false;
    return true;
}

// 头部验证
static bool validate_header(const char* name, const char* value) {
    if (!name || !value) return false;
    if (strchr(name, '\r') || strchr(name, '\n')) return false;
    if (strchr(value, '\r') || strchr(value, '\n')) return false;
    return true;
}
```

#### 12.4.2 资源限制
```c
// 请求大小限制
#define MAX_REQUEST_SIZE    (10 * 1024 * 1024)  // 10MB
#define MAX_RESPONSE_SIZE   (50 * 1024 * 1024)  // 50MB
#define MAX_HEADER_SIZE     (8 * 1024)          // 8KB

// 超时控制
curl_easy_setopt(curl_h, CURLOPT_TIMEOUT_MS, 300000);      // 5分钟总超时
curl_easy_setopt(curl_h, CURLOPT_CONNECTTIMEOUT_MS, 30000); // 30秒连接超时
```

#### 12.4.3 权限控制
```c
// MFP端权限验证
static bool check_token(const char* token) {
    if (!token || strncmp(token, "Bearer ", 7) != 0) return false;

    const char* jwt_token = token + 7;
    return validate_jwt_token(jwt_token);
}

// API访问控制
typedef struct {
    const char* url;
    uint32_t required_permissions;
    bool (*permission_check)(const char* token);
} api_permission_t;
```

### 12.5 错误处理和恢复

#### 12.5.1 多层错误检查
```c
// 网络层错误
if (curl_result != CURLE_OK) {
    const char* error_msg = curl_easy_strerror(curl_result);
    maybe_emit_event(x, XHR_EVENT_ERROR, create_error_event(error_msg));
}

// HTTP层错误
long response_code;
curl_easy_getinfo(curl_h, CURLINFO_RESPONSE_CODE, &response_code);
if (response_code >= 400) {
    maybe_emit_event(x, XHR_EVENT_ERROR, create_http_error_event(response_code));
}

// 应用层错误
if (x->response_type == XHR_RTYPE_JSON) {
    JSValue parsed = JS_ParseJSON(ctx, response_text, response_length, "<xhr>");
    if (JS_IsException(parsed)) {
        maybe_emit_event(x, XHR_EVENT_ERROR, create_parse_error_event());
    }
}
```

#### 12.5.2 自动重试机制
```c
typedef struct {
    int max_retries;
    int current_retry;
    int retry_delay_ms;
    bool (*should_retry)(CURLcode result, long response_code);
} retry_config_t;

static bool should_retry_request(CURLcode result, long response_code) {
    // 网络错误重试
    if (result == CURLE_COULDNT_CONNECT || result == CURLE_OPERATION_TIMEDOUT)
        return true;

    // HTTP 5xx错误重试
    if (response_code >= 500 && response_code < 600)
        return true;

    return false;
}
```

### 12.6 调试和监控

#### 12.6.1 详细日志记录
```c
#define XHR_LOG_DEBUG(fmt, ...) \
    printf("[XHR][%s:%d] " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__)

// 请求生命周期日志
XHR_LOG_DEBUG("Request started: %s %s", method, url);
XHR_LOG_DEBUG("Headers sent: %d bytes", header_size);
XHR_LOG_DEBUG("Body sent: %d bytes", body_size);
XHR_LOG_DEBUG("Response received: %ld status, %d bytes", response_code, response_size);
XHR_LOG_DEBUG("Request completed in %d ms", elapsed_time);
```

#### 12.6.2 性能指标收集
```c
typedef struct {
    uint64_t total_requests;
    uint64_t successful_requests;
    uint64_t failed_requests;
    uint64_t total_bytes_sent;
    uint64_t total_bytes_received;
    uint32_t average_response_time_ms;
} xhr_statistics_t;

static xhr_statistics_t g_xhr_stats = {0};

static void update_statistics(STPeSFXhr* x, CURLcode result, long response_code) {
    g_xhr_stats.total_requests++;

    if (result == CURLE_OK && response_code < 400) {
        g_xhr_stats.successful_requests++;
    } else {
        g_xhr_stats.failed_requests++;
    }

    // 更新传输字节数和响应时间...
}
```

## 13. 总结与展望

### 13.1 技术成就
xhr.c作为PEDK系统的核心网络模块，成功实现了：

1. **标准兼容性**: 完整实现XMLHttpRequest Level 1标准，确保Web应用的兼容性
2. **高性能异步I/O**: 通过libcurl和libuv的深度集成，实现了真正的非阻塞网络通信
3. **内存高效**: 采用零拷贝、动态缓冲区和对象池等技术，最小化内存占用
4. **安全可靠**: 多层错误检查、输入验证和资源限制，确保系统稳定性
5. **扩展性强**: 支持PEDK特有的文件下载功能，满足嵌入式设备需求

### 13.2 架构优势
- **分层设计**: 清晰的架构分层，便于维护和扩展
- **事件驱动**: 基于事件的异步编程模型，提高并发性能
- **模块化**: 良好的模块边界，降低耦合度
- **可测试性**: 清晰的接口定义，便于单元测试

### 13.3 待完善功能
1. **Upload对象**: 实现完整的上传进度监控和事件处理
2. **FormData支持**: 扩展send()方法支持更多数据类型
3. **WebSocket支持**: 添加实时双向通信能力
4. **HTTP/2支持**: 利用libcurl的HTTP/2功能提升性能
5. **缓存机制**: 实现HTTP缓存以减少网络请求

### 13.4 网络硬件依赖与第三方库集成分析

#### 13.4.1 硬件网卡完全依赖
PEDK的curl网络功能**完全依赖于硬件网卡**，JavaScript运行在RE（Runtime Environment）环境中，但网络通信必须通过物理硬件实现：

##### **有线网络硬件依赖**
```c
// mainapp/net/network.c - 有线网络初始化
static int32_t network_init_wired(NET_CTX_S* net_ctx) {
    char hostname[HOSTNAME_LEN];
    uint8_t mac[6];

    // 必须获取物理网卡MAC地址
    RETURN_VAL_IF(net_ifctl_get_mac(IFACE_ETH, mac, sizeof(mac)) != 0, NET_WARN, -1);

    // 启用物理以太网接口 eth0
    if (netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_ETH)) {
        RETURN_VAL_IF(net_ifctl_switch_safe(IFACE_ETH, IFACE_SWITCH_ON) < 0, NET_WARN, -1);
    }

    return 0;
}
```

##### **WiFi硬件驱动依赖**
```c
// mainapp/hal/hal_load.c - WiFi驱动加载
int32_t hal_prolog(void) {
    int ret = 0;
    void* wifi_handle = NULL;

    ret = pi_hal_init();

    /* 必须加载WiFi硬件驱动 */
    ret = pi_hal_wifi_request(&wifi_handle, HAL_REQUEST_FLAG_NONBLOCK);
    if (ret < 0) {
        return ret;  // WiFi硬件不可用
    }

    pi_hal_wifi_load(wifi_handle);  // 加载WiFi芯片驱动
    pi_hal_wifi_free(&wifi_handle);

    return ret;
}
```

#### 13.4.2 第三方库编译集成
```makefile
# pedk/makefile - PEDK端编译配置
lib_path := $(STAGING_DIR)/usr/lib/quickjs/ \
            $(STAGING_DIR)/usr/lib/libuv \
            $(STAGING_DIR)/usr/lib/

# 所有第三方库都是编译时静态链接
lib_file := -lm \
            -ldl \
            -lquickjs \     # JavaScript引擎
            -luv_a \        # 异步I/O事件循环
            -lcurl \        # HTTP/HTTPS客户端
            -lpedk \        # PEDK核心库
            -lpthread \     # POSIX线程
            -lcjson         # JSON解析库
```

#### 13.4.3 运行时环境架构
```
┌─────────────────────────────────────────────────────────────┐
│                    PEDK 2.0 架构                            │
├─────────────────────────────────────────────────────────────┤
│  RE进程 (Runtime Environment)                               │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ JavaScript应用 (运行在QuickJS引擎中)                    ││
│  │ ├─ http.js (fetchData, wget等高级API)                  ││
│  │ ├─ xhr.c (XMLHttpRequest C实现)                        ││
│  │ ├─ curl_utils.c (libcurl + libuv集成)                  ││
│  │ └─ 网络请求 → 通过IPC发送到MFP进程                      ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  MFP进程 (Multi-Function Printer)                          │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ 网络管理模块                                            ││
│  │ ├─ network.c (网络初始化和管理)                         ││
│  │ ├─ netifctl.c (网络接口控制)                            ││
│  │ ├─ wifi_main.c (WiFi驱动管理)                           ││
│  │ └─ 直接操作硬件网卡                                     ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  硬件抽象层 (HAL)                                           │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ ├─ 以太网卡驱动 (eth0)                                  ││
│  │ ├─ WiFi芯片驱动 (wlan0, p2p0)                           ││
│  │ └─ Linux内核网络协议栈 (TCP/IP, IPv6)                   ││
│  └─────────────────────────────────────────────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  物理硬件                                                   │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ ├─ 以太网控制器芯片                                     ││
│  │ ├─ WiFi无线网卡芯片                                     ││
│  │ └─ 网络变压器、天线等物理组件                           ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 13.4.4 **重要澄清：网络请求实际处理位置**

经过详细代码分析，需要澄清一个关键技术细节：

#### **XMLHttpRequest网络请求直接在RE进程中处理**

```c
// pedk/src/runtime/modules/std/xhr.c - send()方法实现
static JSValue net_xhr_send(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv) {
    STPeSFXhr* x = net_xhr_get(ctx, this_val);

    if (!x->sent) {
        // 设置请求体数据
        if (JS_IsString(argv[0])) {
            const char *body = JS_ToCStringLen(ctx, &size, argv[0]);
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_POSTFIELDSIZE, (long) size);
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_COPYPOSTFIELDS, body);
        }

        // 设置HTTP头部
        if (x->slist)
            curl_easy_setopt(x->curl_private.curl_h, CURLOPT_HTTPHEADER, x->slist);

        // 关键：网络请求直接在RE进程中发送
        if (x->async) {
            // 异步模式：添加到RE进程的curl multi句柄
            curl_multi_add_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
        } else {
            // 同步模式：直接在RE进程中执行网络请求
            CURLcode result = curl_easy_perform(x->curl_private.curl_h);
            curl__done_cb(result, x);
        }

        x->sent = true;
    }
    return JS_UNDEFINED;
}
```

#### **网络请求处理架构对比**

##### **XMLHttpRequest (xhr.c) - 直接网络处理**
```
JavaScript App (RE进程)
    ↓ xhr.send()
libcurl + libuv (RE进程) ← 直接在RE进程中处理
    ↓ socket系统调用
Linux内核网络协议栈
    ↓ 网络驱动
物理网卡硬件
    ↓ 网络传输
远程服务器
```

##### **Bridge消息 (bridge.c) - IPC转发处理**
```
JavaScript App (RE进程)
    ↓ bridge.send_msg()
js_send_msg() (RE进程)
    ↓ a2p_msg_send()
IPC通信 → MFP进程
    ↓ pedk_mgr处理
MFP进程中的业务逻辑
```

#### **两种不同的通信机制**

1. **XMLHttpRequest网络请求**：
   - **处理位置**：直接在RE进程中
   - **使用库**：libcurl + libuv
   - **网络访问**：直接访问外部网络
   - **典型用途**：HTTP API调用、文件下载、Web服务通信

2. **Bridge消息通信**：
   - **处理位置**：通过IPC转发到MFP进程
   - **使用协议**：自定义IPC协议
   - **通信范围**：RE进程与MFP进程之间
   - **典型用途**：打印控制、设备状态查询、系统配置

#### **代码证据对比**

**xhr.c中的直接网络处理**：
```c
// 构造函数中初始化curl句柄 - 在RE进程中
x->curl_private.curlm_h = pesfGetCurlm(&x->curl_private);
x->curl_private.curl_h = pesfCurlEasyInit(NULL);
x->curl_private.uv_loop = qrt->uv_loop;  // 绑定到RE进程的事件循环

// send()方法中直接发送网络请求 - 在RE进程中
curl_multi_add_handle(x->curl_private.curlm_h, x->curl_private.curl_h);
```

**bridge.c中的IPC转发**：
```c
// js_send_msg()函数 - 转发到MFP进程
JSValue js_send_msg(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv) {
    const char* str = JS_ToCStringLen(ctx, &str_len, argv[0]);

    // 关键：通过IPC发送到MFP进程，而不是直接网络请求
    a2p_msg_send(prt->dynamic_property.rtid, (uint16_t)str_len, (uint8_t*)str);

    return JS_UNDEFINED;
}
```

#### **13.4.5 深度解析：RE进程为什么可以直接调用curl接口？**

这是一个涉及操作系统权限、进程架构和库链接的核心问题：

##### **1. 进程权限和网络访问权限**

在Linux系统中，**任何用户态进程都可以创建网络socket**，除非被特殊限制：

```c
// 任何进程都可以调用这些系统调用
int sockfd = socket(AF_INET, SOCK_STREAM, 0);  // 创建TCP socket
connect(sockfd, &server_addr, sizeof(server_addr));  // 连接远程服务器
send(sockfd, data, len, 0);  // 发送数据
recv(sockfd, buffer, size, 0);  // 接收数据
```

**关键点**：
- **网络访问不需要特殊权限**（除了绑定特权端口<1024需要root权限）
- **RE进程作为普通用户进程**，具有标准的网络访问权限
- **MFP进程和RE进程在权限上是平等的**，都可以访问网络

##### **2. 第三方库的静态链接机制**

```makefile
# pedk/makefile - RE进程的编译配置
lib_file := -lm \
            -ldl \
            -lquickjs \     # JavaScript引擎
            -luv_a \        # libuv异步I/O库
            -lcurl \        # libcurl HTTP客户端库
            -lssl \         # OpenSSL加密库
            -lcrypto \      # OpenSSL密码学库
            -lpedk \        # PEDK核心库
            -lpthread \     # POSIX线程库
            -lcjson         # JSON解析库

# 所有库都静态链接到RE进程的可执行文件中
```

**静态链接的含义**：
- **编译时集成**：libcurl、OpenSSL等库的代码直接编译进RE进程的可执行文件
- **运行时无依赖**：RE进程启动后，所有网络功能代码已经在进程内存中
- **独立网络能力**：RE进程拥有完整的HTTP/HTTPS客户端功能

##### **3. 网络协议栈访问机制**

```
┌─────────────────────────────────────────────────────────────┐
│                    RE进程内存空间                            │
├─────────────────────────────────────────────────────────────┤
│  JavaScript引擎 (QuickJS)                                   │
│  ├─ xhr.c (XMLHttpRequest实现)                              │
│  ├─ libcurl (静态链接的HTTP客户端库)                        │
│  ├─ OpenSSL (静态链接的加密库)                              │
│  └─ libuv (静态链接的异步I/O库)                             │
├─────────────────────────────────────────────────────────────┤
│  系统调用接口 (socket, connect, send, recv等)               │
├─────────────────────────────────────────────────────────────┤
│  Linux内核网络协议栈 (TCP/IP, IPv6)                        │
├─────────────────────────────────────────────────────────────┤
│  网络设备驱动 (eth0, wlan0)                                 │
├─────────────────────────────────────────────────────────────┤
│  物理网卡硬件                                               │
└─────────────────────────────────────────────────────────────┘
```

##### **4. 具体的调用链路分析**

```c
// JavaScript调用
xhr.send("Hello World");
    ↓
// xhr.c中的C函数
static JSValue net_xhr_send(...) {
    curl_easy_perform(x->curl_private.curl_h);  // libcurl函数调用
}
    ↓
// libcurl内部实现 (静态链接在RE进程中)
CURLcode curl_easy_perform(CURL *curl) {
    // 创建socket
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);

    // 连接服务器
    connect(sockfd, &server_addr, sizeof(server_addr));

    // 发送HTTP请求
    send(sockfd, http_request, request_len, 0);

    // 接收HTTP响应
    recv(sockfd, response_buffer, buffer_size, 0);
}
    ↓
// Linux内核系统调用
sys_socket() → sys_connect() → sys_send() → sys_recv()
    ↓
// 内核网络协议栈处理
TCP/IP协议处理 → 网络设备驱动 → 物理网卡硬件
```

##### **5. 为什么不需要通过MFP进程？**

**传统误解**：认为只有MFP进程才能访问硬件，包括网卡

**实际情况**：
- **网卡访问通过内核**：任何进程都通过内核的网络协议栈访问网卡
- **不是直接硬件访问**：进程不直接操作网卡寄存器，而是通过socket系统调用
- **内核统一管理**：Linux内核负责网络资源的分配和管理
- **多进程并发**：多个进程可以同时进行网络通信

##### **6. MFP进程的网络功能对比**

```c
// mainapp/net/prnsdk/prnsdk.c - MFP进程中也有curl调用
static int32_t prnsdk_send_to_cloud(const char* url, const char* json_data) {
    CURL *curl = curl_easy_init();  // MFP进程中也可以直接调用curl

    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_data);

    CURLcode rc = curl_easy_perform(curl);  // 直接网络请求

    curl_easy_cleanup(curl);
    return (rc == CURLE_OK) ? 0 : -1;
}
```

**结论**：
- **MFP进程和RE进程都具有网络访问能力**
- **两个进程都静态链接了libcurl**
- **网络访问是标准的用户态权限**，不需要特殊硬件访问权限

**关键结论修正**:
1. **RE进程通过静态链接的libcurl直接访问网络**，这是标准的用户态网络编程
2. **网络访问不需要特殊硬件权限**，任何进程都可以通过socket系统调用访问网络
3. **libcurl、OpenSSL等库编译时静态链接到RE进程**，提供完整的HTTP/HTTPS功能
4. **内核网络协议栈统一管理**，多个进程可以并发进行网络通信
5. **MFP进程和RE进程都具有独立的网络访问能力**

### 13.5 技术价值
xhr.c的实现展现了在嵌入式环境中构建现代Web技术栈的可行性，为物联网设备的Web化提供了重要参考。其设计理念和技术方案对于类似的嵌入式Web运行时项目具有重要的借鉴价值。

该模块的成功实现证明了PEDK系统的技术路线正确性：在保持Web标准兼容的前提下，通过精心的架构设计和性能优化，完全可以在资源受限的嵌入式设备上运行复杂的Web应用。
