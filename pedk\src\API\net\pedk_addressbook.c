#include <string.h>
#include <stdio.h>
#include "pedk_addressbook.h"
#include "PEDK_event.h"
#include <quickjs.h>
#include "app_manager.h"


//email_addressbook
JSValue js_add_email_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_MAIL_PARM  set_mail_info;
    int32_t         respond = -1;
    int32_t         receive_data[512];
    int32_t         receive_cnt = sizeof(receive_data);
    int32_t         add_index = -1;
    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }
    JSValueConst obj = argv[0];

    JSValueConst pedk_mail_name = JS_GetPropertyStr( ctx, obj, "mail_name");
    JSValueConst pedk_mail_addr = JS_GetPropertyStr( ctx, obj, "mail_addr");
    JSValueConst pedk_group_num = JS_GetPropertyStr( ctx, obj, "group_num");

    const char *c_pedk_mail_name = JS_ToCString(ctx, pedk_mail_name);
    const char *c_pedk_mail_addr = JS_ToCString(ctx, pedk_mail_addr);
    strncpy(set_mail_info.pedk_mail_name, c_pedk_mail_name, strlen(c_pedk_mail_name));
    set_mail_info.pedk_mail_name[strlen(c_pedk_mail_name)] = '\0';
    strncpy(set_mail_info.pedk_mail_addr, c_pedk_mail_addr, strlen(c_pedk_mail_addr));
    set_mail_info.pedk_mail_addr[strlen(c_pedk_mail_addr)] = '\0';

    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        JS_ToInt32(ctx, &set_mail_info.pedk_group_num[i], JS_GetPropertyUint32(ctx, pedk_group_num, i));
    }
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_EMAIL_ADDR, 0, sizeof(PEDK_MAIL_PARM), &set_mail_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_EMAIL_ADDR, &respond, receive_data, &receive_cnt, 3);
    add_index = receive_data[0];

    JS_FreeCString(ctx, c_pedk_mail_name);
    JS_FreeCString(ctx, c_pedk_mail_addr);
    JS_FreeValue(ctx, pedk_mail_name);
    JS_FreeValue(ctx, pedk_mail_addr);
    JS_FreeValue(ctx, pedk_group_num);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewInt32(ctx, add_index);
    }


}

JSValue js_get_email_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t             index = 0;
    int32_t             respond = -1;
    unsigned char       receive_data[1024];
    int32_t             receive_cnt = sizeof(receive_data);
    PEDK_MAIL_PARM*     receive_p;

    JSValue obj = JS_NewObject(ctx);
    JSValue array = JS_NewArray(ctx);

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR, &respond, receive_data, &receive_cnt, 3);
    receive_p = (PEDK_MAIL_PARM*)receive_data;

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, receive_p->pedk_mail_index));
        JS_SetPropertyStr(ctx, obj, "mail_name", JS_NewString(ctx, receive_p->pedk_mail_name));
        JS_SetPropertyStr(ctx, obj, "mail_addr", JS_NewString(ctx, receive_p->pedk_mail_addr));

        for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
        {
            if ( receive_p->pedk_group_num[i] != 0 )
            {
                JS_SetPropertyUint32(ctx, array, i, JS_NewInt32(ctx, receive_p->pedk_group_num[i]));
            }
        }
        JS_SetPropertyStr(ctx, obj, "group_num", array);

        return obj;
    }
}

JSValue js_get_email_addr_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t         respond = -1;
    unsigned char   receive_data[1024 * 10];
    int32_t         receive_cnt = sizeof(receive_data);
    int32_t         n = 0;


    JSValue array_list = JS_NewArray(ctx);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_LIST, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_LIST, &respond, receive_data, &receive_cnt, 3);

    if ( respond > 0 )
    {
        PEDK_MAIL_PARM (*list_p)[respond];
        list_p = (PEDK_MAIL_PARM(*)[respond])receive_data;
        for ( int32_t i = 0; i < respond; i++ )
        {
            n = 0;
            JSValue obj = JS_NewObject(ctx);
            JSValue array = JS_NewArray(ctx);

            JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, (*list_p)[i].pedk_mail_index));
            JS_SetPropertyStr(ctx, obj, "mail_name", JS_NewString(ctx, (*list_p)[i].pedk_mail_name));
            JS_SetPropertyStr(ctx, obj, "mail_addr", JS_NewString(ctx, (*list_p)[i].pedk_mail_addr));

            for ( int32_t j = 0; j < PEDK_MAIL_GROUP_MAX; j++ )
            {
                if ( (*list_p)[i].pedk_group_num[j] != 0 )
                {
                    JS_SetPropertyUint32(ctx, array, n, JS_NewInt32(ctx, (*list_p)[i].pedk_group_num[j]));
                    n++;
                }
            }
            JS_SetPropertyStr(ctx, obj, "group_num", array);

            JS_SetPropertyUint32(ctx, array_list, i, obj);
        }
        return array_list;
    }
    else
    {
        return array_list;
    }
}

JSValue js_get_email_addr_num(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t email_num;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_NUM, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_ADDR_NUM, &respond, receive_data, &receive_cnt, 3);
    email_num = receive_data[0];

    return JS_NewInt32(ctx, email_num);

}

JSValue js_is_email_addr_full(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t email_full = 0;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_EMAIL_ADDR_FULL, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_EMAIL_ADDR_FULL, &respond, receive_data, &receive_cnt, 3);
    email_full = receive_data[0];
    if ( email_full )
    {
        return JS_NewBool(ctx, 1);
    }
    else
    {
        return JS_NewBool(ctx, 0);
    }
}

JSValue js_modify_email_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_MAIL_PARM  set_mail_info;
    int32_t         respond = -1;
    int32_t         modify_index;

    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];

    JSValueConst pedk_mail_index = JS_GetPropertyStr( ctx, obj, "index");
    JSValueConst pedk_mail_name = JS_GetPropertyStr( ctx, obj, "mail_name");
    JSValueConst pedk_mail_addr = JS_GetPropertyStr( ctx, obj, "mail_addr");
    JSValueConst pedk_group_num = JS_GetPropertyStr( ctx, obj, "group_num");

    JS_ToInt32(ctx,&modify_index,pedk_mail_index);
    const char *c_pedk_mail_name = JS_ToCString(ctx, pedk_mail_name);
    const char *c_pedk_mail_addr = JS_ToCString(ctx, pedk_mail_addr);

    set_mail_info.pedk_mail_index = modify_index;
    strncpy(set_mail_info.pedk_mail_name, c_pedk_mail_name, strlen(c_pedk_mail_name));
    set_mail_info.pedk_mail_name[strlen(c_pedk_mail_name)] = '\0';
    strncpy(set_mail_info.pedk_mail_addr, c_pedk_mail_addr, strlen(c_pedk_mail_addr));
    set_mail_info.pedk_mail_addr[strlen(c_pedk_mail_addr)] = '\0';

    memset(set_mail_info.pedk_group_num, 0, sizeof(set_mail_info.pedk_group_num));

    for ( int32_t i = 0; i < PEDK_MAIL_GROUP_MAX; i++ )
    {
        JS_ToInt32(ctx, &set_mail_info.pedk_group_num[i], JS_GetPropertyUint32(ctx, pedk_group_num, i));
    }
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_EMAIL_ADDR, 0, sizeof(PEDK_MAIL_PARM), &set_mail_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_EMAIL_ADDR, &respond, NULL, NULL, 3);


    JS_FreeCString(ctx, c_pedk_mail_name);
    JS_FreeCString(ctx, c_pedk_mail_addr);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}

JSValue js_remove_email_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t index = 0;
    int32_t respond = -1;

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_ADDR, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_ADDR, &respond, NULL, NULL, 3);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}


//group_addressbook
JSValue js_add_email_to_group(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t index[2];
    int32_t respond = -1;

    JS_ToInt32(ctx, &index[0], argv[0]);
    JS_ToInt32(ctx, &index[1], argv[1]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_EMAIL_TO_GROUP, 0, sizeof(index), index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_EMAIL_TO_GROUP, &respond, NULL, NULL, 3);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}

JSValue js_creat_email_group(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_MAIL_GROUP_MGR set_group_info;
    int32_t             respond = -1;
    int32_t             receive_data[512];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t             add_index = -1;

    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];

    JSValueConst pedk_group_name = JS_GetPropertyStr( ctx, obj, "group_name");
    JSValueConst pedk_mail_index = JS_GetPropertyStr( ctx, obj, "array");

    const char *c_pedk_group_name = JS_ToCString(ctx, pedk_group_name);
    strncpy(set_group_info.pedk_group_name, c_pedk_group_name, strlen(c_pedk_group_name));
    set_group_info.pedk_group_name[strlen(c_pedk_group_name)] = '\0';

    for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        JS_ToInt32(ctx, &set_group_info.pedk_mail_index[i], JS_GetPropertyUint32(ctx, pedk_mail_index, i));
    }
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_CREAT_EMAIL_GROUP, 0, sizeof(PEDK_MAIL_GROUP_MGR), &set_group_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_CREAT_EMAIL_GROUP, &respond, receive_data, &receive_cnt, 3);
    add_index = receive_data[0];

    JS_FreeCString(ctx, c_pedk_group_name);
    JS_FreeValue(ctx, pedk_group_name);
    JS_FreeValue(ctx, pedk_mail_index);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewInt32(ctx, add_index);
    }
}

JSValue js_get_email_group(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t                 index = 0;
    int32_t                 respond = -1;
    unsigned char           receive_data[1024];
    int32_t                 receive_cnt = sizeof(receive_data);
    PEDK_MAIL_GROUP_MGR*    receive_p;

    JSValue obj = JS_NewObject(ctx);
    JSValue array = JS_NewArray(ctx);

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP, &respond, receive_data, &receive_cnt, 3);
    receive_p = (PEDK_MAIL_GROUP_MGR*)receive_data;

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, receive_p->pedk_group_index));
        JS_SetPropertyStr(ctx, obj, "group_name", JS_NewString(ctx, receive_p->pedk_group_name));

        for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
        {
            if ( receive_p->pedk_mail_index[i] != 0 )
            {
                JS_SetPropertyUint32(ctx, array, i, JS_NewInt32(ctx, receive_p->pedk_mail_index[i]));
            }
        }
        JS_SetPropertyStr(ctx, obj, "array", array);

        return obj;
    }
}

JSValue js_get_email_group_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t         respond = -1;
    unsigned char   receive_data[1024 * 10];
    int32_t         receive_cnt = sizeof(receive_data);
    int32_t         n = 0;


    JSValue array_list = JS_NewArray(ctx);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_LIST, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_LIST, &respond, receive_data, &receive_cnt, 3);

    if ( respond > 0 )
    {
        PEDK_MAIL_GROUP_MGR (*list_p)[respond];
        list_p = (PEDK_MAIL_GROUP_MGR(*)[respond])receive_data;
        for ( int32_t i = 0; i < respond; i++ )
        {
            n = 0;
            JSValue obj = JS_NewObject(ctx);
            JSValue array = JS_NewArray(ctx);

            JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, (*list_p)[i].pedk_group_index));
            JS_SetPropertyStr(ctx, obj, "group_name", JS_NewString(ctx, (*list_p)[i].pedk_group_name));

            for ( int32_t j = 0; j < PEDK_MAIL_ADDR_NUM_MAX; j++ )
            {
                if( (*list_p)[i].pedk_mail_index[j] != 0 )
                {
                    JS_SetPropertyUint32(ctx, array, n, JS_NewInt32(ctx, (*list_p)[i].pedk_mail_index[j]));
                    n++;
                }
            }
            JS_SetPropertyStr(ctx, obj, "array", array);

            JS_SetPropertyUint32(ctx, array_list, i, obj);
        }
        return array_list;
    }
    else
    {
        return array_list;
    }
}

JSValue js_get_email_group_num(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t group_num;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_NUM, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_EMAIL_GROUP_NUM, &respond, receive_data, &receive_cnt, 3);
    group_num = receive_data[0];

    return JS_NewInt32(ctx, group_num);
}

JSValue js_is_email_group_full(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t group_full = 0;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_EMAIL_GROUP_FULL, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_EMAIL_GROUP_FULL, &respond, receive_data, &receive_cnt, 3);
    group_full = receive_data[0];
    if ( group_full )
    {
        return JS_NewBool(ctx, 1);
    }
    else
    {
        return JS_NewBool(ctx, 0);
    }
}

JSValue js_modify_email_group(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_MAIL_GROUP_MGR set_group_info;
    int32_t             respond = -1;
    int32_t             modify_index;

    if ( !JS_IsObject(argv[1]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[1];

    JSValueConst pedk_group_index = JS_GetPropertyStr( ctx, obj, "index");
    JSValueConst pedk_group_name = JS_GetPropertyStr( ctx, obj, "group_name");
    JSValueConst pedk_mail_index = JS_GetPropertyStr( ctx, obj, "array");

    JS_ToInt32(ctx,&modify_index,pedk_group_index);

    set_group_info.pedk_group_index = modify_index;
    const char *c_pedk_group_name = JS_ToCString(ctx, pedk_group_name);
    strncpy(set_group_info.pedk_group_name, c_pedk_group_name, strlen(c_pedk_group_name));
    set_group_info.pedk_group_name[strlen(c_pedk_group_name)] = '\0';


    for ( int32_t i = 0; i < PEDK_MAIL_ADDR_NUM_MAX; i++ )
    {
        JS_ToInt32(ctx, &set_group_info.pedk_mail_index[i], JS_GetPropertyUint32(ctx, pedk_mail_index, i));
    }
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_EMAIL_GROUP, 0, sizeof(PEDK_MAIL_GROUP_MGR), &set_group_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_EMAIL_GROUP, &respond, NULL, NULL, 3);

    JS_FreeCString(ctx, c_pedk_group_name);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}

JSValue js_remove_email_from_group(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t index[2];
    int32_t respond = -1;

    JS_ToInt32(ctx, &index[0], argv[0]);
    JS_ToInt32(ctx, &index[1], argv[1]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_FROM_GROUP, 0, sizeof(index), index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_FROM_GROUP, &respond, NULL, NULL, 3);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}

JSValue js_remove_email_group(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    uint16_t index = 0;
    int32_t respond = -1;

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_GROUP, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_EMAIL_GROUP, &respond, NULL, NULL, 3);
    if ( respond < 0 )
    {
        return JS_NewInt32(ctx, 0);
    }
    else
    {
        return JS_NewInt32(ctx, 1);
    }
}



//ftp_addressbook
JSValue js_add_ftp_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_FTP_PARM   set_ftp_info;
    int32_t         respond = -1;
    int32_t         receive_data[512];
    int32_t         receive_cnt = sizeof(receive_data);
    int32_t         add_index = -1;
    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }
    JSValueConst obj = argv[0];

    JSValueConst pedk_ftp_name = JS_GetPropertyStr( ctx, obj, "ftp_name");
    JSValueConst pedk_ftp_addr = JS_GetPropertyStr( ctx, obj, "ftp_addr");
    JSValueConst pedk_ftp_subdirectory = JS_GetPropertyStr( ctx, obj, "ftp_subdirectory");
    JSValueConst pedk_ftp_port = JS_GetPropertyStr( ctx, obj, "ftp_port");
    JSValueConst pedk_ftp_anonymous = JS_GetPropertyStr( ctx, obj, "ftp_anonymous");
    JSValueConst pedk_ftp_login_name = JS_GetPropertyStr( ctx, obj, "ftp_login_name");
    JSValueConst pedk_ftp_login_pwd = JS_GetPropertyStr( ctx, obj, "ftp_login_pwd");

    const char *c_pedk_ftp_name = JS_ToCString(ctx, pedk_ftp_name);
    const char *c_pedk_ftp_addr = JS_ToCString(ctx, pedk_ftp_addr);
    const char *c_pedk_ftp_subdirectory = JS_ToCString(ctx, pedk_ftp_subdirectory);
    const char *c_pedk_ftp_login_name = JS_ToCString(ctx, pedk_ftp_login_name);
    const char *c_pedk_ftp_login_pwd = JS_ToCString(ctx, pedk_ftp_login_pwd);

    strncpy(set_ftp_info.pedk_ftp_name, c_pedk_ftp_name, strlen(c_pedk_ftp_name));
    set_ftp_info.pedk_ftp_name[strlen(c_pedk_ftp_name)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_addr, c_pedk_ftp_addr, strlen(c_pedk_ftp_addr));
    set_ftp_info.pedk_ftp_addr[strlen(c_pedk_ftp_addr)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_subdirectory, c_pedk_ftp_subdirectory, strlen(c_pedk_ftp_subdirectory));
    set_ftp_info.pedk_ftp_subdirectory[strlen(c_pedk_ftp_subdirectory)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_login_name, c_pedk_ftp_login_name, strlen(c_pedk_ftp_login_name));
    set_ftp_info.pedk_ftp_login_name[strlen(c_pedk_ftp_login_name)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_login_pwd, c_pedk_ftp_login_pwd, strlen(c_pedk_ftp_login_pwd));
    set_ftp_info.pedk_ftp_login_pwd[strlen(c_pedk_ftp_login_pwd)] = '\0';

    JS_ToInt32(ctx, &set_ftp_info.pedk_ftp_port, pedk_ftp_port);
    set_ftp_info.pedk_ftp_anonymous = JS_ToBool(ctx, pedk_ftp_anonymous);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_FTP_ADDR, 0, sizeof(PEDK_FTP_PARM), &set_ftp_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_FTP_ADDR, &respond, receive_data, &receive_cnt, 3);
    add_index = receive_data[0];

    JS_FreeCString(ctx, c_pedk_ftp_name);
    JS_FreeCString(ctx, c_pedk_ftp_addr);
    JS_FreeCString(ctx, c_pedk_ftp_subdirectory);
    JS_FreeCString(ctx, c_pedk_ftp_login_name);
    JS_FreeCString(ctx, c_pedk_ftp_login_pwd);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewInt32(ctx, add_index);
    }
}

JSValue js_get_ftp_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t         index = 0;
    int32_t         respond = -1;
    unsigned char   receive_data[1024];
    int32_t         receive_cnt = sizeof(receive_data);
    PEDK_FTP_PARM*  receive_p;

    JSValue obj = JS_NewObject(ctx);
    JSValue array = JS_NewArray(ctx);

    JS_ToInt32(ctx, &index, argv[0]);
    memset(receive_data,0,sizeof(receive_data));
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR, &respond, receive_data, &receive_cnt, 3);


    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        receive_p = (PEDK_FTP_PARM*)receive_data;
        
        JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, receive_p->pedk_ftp_index));
        JS_SetPropertyStr(ctx, obj, "ftp_name", JS_NewString(ctx, receive_p->pedk_ftp_name));
        JS_SetPropertyStr(ctx, obj, "ftp_addr", JS_NewString(ctx, receive_p->pedk_ftp_addr));
        JS_SetPropertyStr(ctx, obj, "ftp_subdirectory", JS_NewString(ctx, receive_p->pedk_ftp_subdirectory));
        JS_SetPropertyStr(ctx, obj, "ftp_port", JS_NewInt32(ctx, receive_p->pedk_ftp_port));
        JS_SetPropertyStr(ctx, obj, "ftp_anonymous", JS_NewBool(ctx, receive_p->pedk_ftp_anonymous));
        JS_SetPropertyStr(ctx, obj, "ftp_login_name", JS_NewString(ctx, receive_p->pedk_ftp_login_name));
        JS_SetPropertyStr(ctx, obj, "ftp_login_pwd", JS_NewString(ctx, receive_p->pedk_ftp_login_pwd));

        return obj;
    }
}

JSValue js_get_ftp_addr_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t     respond = -1;
    char        receive_data[1024 * 30];
    int32_t     receive_cnt = sizeof(receive_data);


    JSValue array_list = JS_NewArray(ctx);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_LIST, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_LIST, &respond, receive_data, &receive_cnt, 3);


    if ( respond > 0 )
    {
        PEDK_FTP_PARM (*list_p)[respond];
        list_p = (PEDK_FTP_PARM(*)[respond])receive_data;
        for ( int32_t i = 0; i < respond; i++ )
        {
            JSValue obj = JS_NewObject(ctx);
            JSValue array = JS_NewArray(ctx);

            JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, (*list_p)[i].pedk_ftp_index));
            JS_SetPropertyStr(ctx, obj, "ftp_name", JS_NewString(ctx, (*list_p)[i].pedk_ftp_name));
            JS_SetPropertyStr(ctx, obj, "ftp_addr", JS_NewString(ctx, (*list_p)[i].pedk_ftp_addr));
            JS_SetPropertyStr(ctx, obj, "ftp_subdirectory", JS_NewString(ctx, (*list_p)[i].pedk_ftp_subdirectory));
            JS_SetPropertyStr(ctx, obj, "ftp_port", JS_NewInt32(ctx, (*list_p)[i].pedk_ftp_port));
            JS_SetPropertyStr(ctx, obj, "ftp_anonymous", JS_NewBool(ctx, (*list_p)[i].pedk_ftp_anonymous));
            JS_SetPropertyStr(ctx, obj, "ftp_login_name", JS_NewString(ctx, (*list_p)[i].pedk_ftp_login_name));
            JS_SetPropertyStr(ctx, obj, "ftp_login_pwd", JS_NewString(ctx, (*list_p)[i].pedk_ftp_login_pwd));

            JS_SetPropertyUint32(ctx, array_list, i, obj);
        }
        return array_list;
    }
    else
    {
        return array_list;
    }
}

JSValue js_get_ftp_addr_num(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t ftp_num;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_NUM, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_FTP_ADDR_NUM, &respond, receive_data, &receive_cnt, 3);
    ftp_num = receive_data[0];

    return JS_NewInt32(ctx, ftp_num);
}

JSValue js_is_ftp_addr_full(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t ftp_full = 0;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_FTP_ADDR_FULL, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_FTP_ADDR_FULL, &respond, receive_data, &receive_cnt, 3);
    ftp_full = receive_data[0];
    if ( ftp_full )
    {
        return JS_NewBool(ctx, 1);
    }
    else
    {
        return JS_NewBool(ctx, 0);
    }
}

JSValue js_modify_ftp_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_FTP_PARM   set_ftp_info;
    int32_t         respond = -1;
    int32_t         modify_index;

    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];

    JSValueConst pedk_ftp_index = JS_GetPropertyStr( ctx, obj, "index");
    JSValueConst pedk_ftp_name = JS_GetPropertyStr( ctx, obj, "ftp_name");
    JSValueConst pedk_ftp_addr = JS_GetPropertyStr( ctx, obj, "ftp_addr");
    JSValueConst pedk_ftp_subdirectory = JS_GetPropertyStr( ctx, obj, "ftp_subdirectory");
    JSValueConst pedk_ftp_port = JS_GetPropertyStr( ctx, obj, "ftp_port");
    JSValueConst pedk_ftp_anonymous = JS_GetPropertyStr( ctx, obj, "ftp_anonymous");
    JSValueConst pedk_ftp_login_name = JS_GetPropertyStr( ctx, obj, "ftp_login_name");
    JSValueConst pedk_ftp_login_pwd = JS_GetPropertyStr( ctx, obj, "ftp_login_pwd");
    JS_ToInt32(ctx,&modify_index,pedk_ftp_index);

    set_ftp_info.pedk_ftp_index = modify_index;
    const char *c_pedk_ftp_name = JS_ToCString(ctx, pedk_ftp_name);
    const char *c_pedk_ftp_addr = JS_ToCString(ctx, pedk_ftp_addr);
    const char *c_pedk_ftp_subdirectory = JS_ToCString(ctx, pedk_ftp_subdirectory);
    const char *c_pedk_ftp_login_name = JS_ToCString(ctx, pedk_ftp_login_name);
    const char *c_pedk_ftp_login_pwd = JS_ToCString(ctx, pedk_ftp_login_pwd);
    strncpy(set_ftp_info.pedk_ftp_name, c_pedk_ftp_name, strlen(c_pedk_ftp_name));
    set_ftp_info.pedk_ftp_name[strlen(c_pedk_ftp_name)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_addr, c_pedk_ftp_addr, strlen(c_pedk_ftp_addr));
    set_ftp_info.pedk_ftp_addr[strlen(c_pedk_ftp_addr)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_subdirectory, c_pedk_ftp_subdirectory, strlen(c_pedk_ftp_subdirectory));
    set_ftp_info.pedk_ftp_subdirectory[strlen(c_pedk_ftp_subdirectory)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_login_name, c_pedk_ftp_login_name, strlen(c_pedk_ftp_login_name));
    set_ftp_info.pedk_ftp_login_name[strlen(c_pedk_ftp_login_name)] = '\0';

    strncpy(set_ftp_info.pedk_ftp_login_pwd, c_pedk_ftp_login_pwd, strlen(c_pedk_ftp_login_pwd));
    set_ftp_info.pedk_ftp_login_pwd[strlen(c_pedk_ftp_login_pwd)] = '\0';

    JS_ToInt32(ctx, &set_ftp_info.pedk_ftp_port, pedk_ftp_port);
    set_ftp_info.pedk_ftp_anonymous = JS_ToBool(ctx, pedk_ftp_anonymous);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_FTP_ADDR, 0, sizeof(PEDK_FTP_PARM), &set_ftp_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_FTP_ADDR, &respond, NULL, NULL, 3);


    JS_FreeCString(ctx, c_pedk_ftp_name);
    JS_FreeCString(ctx, c_pedk_ftp_addr);
    JS_FreeCString(ctx, c_pedk_ftp_subdirectory);
    JS_FreeCString(ctx, c_pedk_ftp_login_name);
    JS_FreeCString(ctx, c_pedk_ftp_login_pwd);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}

JSValue js_remove_ftp_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    uint16_t    index = 0;
    int32_t     respond = -1;

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_FTP_ADDR, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_FTP_ADDR, &respond, NULL, NULL, 3);
    if ( respond < 0 )
    {
        return JS_NewInt32(ctx, 0);
    }
    else
    {
        return JS_NewInt32(ctx, 1);
    }
}

//smb_addressbook
JSValue js_add_smb_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_SMB_PARM   set_smb_info;
    int32_t         respond = -1;
    int32_t         receive_data[512];
    int32_t         receive_cnt = sizeof(receive_data);
    int32_t         add_index = -1;

    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];

    JSValueConst pedk_smb_name = JS_GetPropertyStr( ctx, obj, "smb_name");
    JSValueConst pedk_smb_addr = JS_GetPropertyStr( ctx, obj, "smb_addr");
    JSValueConst pedk_smb_subdirectory = JS_GetPropertyStr( ctx, obj, "smb_subdirectory");
    JSValueConst pedk_smb_port = JS_GetPropertyStr( ctx, obj, "smb_port");
    JSValueConst pedk_smb_anonymous = JS_GetPropertyStr( ctx, obj, "smb_anonymous");
    JSValueConst pedk_smb_login_name = JS_GetPropertyStr( ctx, obj, "smb_login_name");
    JSValueConst pedk_smb_login_pwd = JS_GetPropertyStr( ctx, obj, "smb_login_pwd");

    const char *c_pedk_smb_name = JS_ToCString(ctx, pedk_smb_name);
    const char *c_pedk_smb_addr = JS_ToCString(ctx, pedk_smb_addr);
    const char *c_pedk_smb_subdirectory = JS_ToCString(ctx, pedk_smb_subdirectory);
    const char *c_pedk_smb_login_name = JS_ToCString(ctx, pedk_smb_login_name);
    const char *c_pedk_smb_login_pwd = JS_ToCString(ctx, pedk_smb_login_pwd);

    strncpy(set_smb_info.pedk_smb_name, c_pedk_smb_name, strlen(c_pedk_smb_name));
    set_smb_info.pedk_smb_name[strlen(c_pedk_smb_name)] = '\0';

    strncpy(set_smb_info.pedk_smb_addr, c_pedk_smb_addr, strlen(c_pedk_smb_addr));
    set_smb_info.pedk_smb_addr[strlen(c_pedk_smb_addr)] = '\0';

    strncpy(set_smb_info.pedk_smb_subdirectory, c_pedk_smb_subdirectory, strlen(c_pedk_smb_subdirectory));
    set_smb_info.pedk_smb_subdirectory[strlen(c_pedk_smb_subdirectory)] = '\0';

    strncpy(set_smb_info.pedk_smb_login_name, c_pedk_smb_login_name, strlen(c_pedk_smb_login_name));
    set_smb_info.pedk_smb_login_name[strlen(c_pedk_smb_login_name)] = '\0';

    strncpy(set_smb_info.pedk_smb_login_pwd, c_pedk_smb_login_pwd, strlen(c_pedk_smb_login_pwd));
    set_smb_info.pedk_smb_login_pwd[strlen(c_pedk_smb_login_pwd)] = '\0';

    JS_ToInt32(ctx, &set_smb_info.pedk_smb_port, pedk_smb_port);
    set_smb_info.pedk_smb_anonymous = JS_ToBool(ctx, pedk_smb_anonymous);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_SMB_ADDR, 0, sizeof(PEDK_SMB_PARM), &set_smb_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_ADD_SMB_ADDR, &respond, receive_data, &receive_cnt, 3);
    add_index = receive_data[0];

    JS_FreeCString(ctx, c_pedk_smb_name);
    JS_FreeCString(ctx, c_pedk_smb_addr);
    JS_FreeCString(ctx, c_pedk_smb_subdirectory);
    JS_FreeCString(ctx, c_pedk_smb_login_name);
    JS_FreeCString(ctx, c_pedk_smb_login_pwd);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewInt32(ctx, add_index);
    }
}

JSValue js_get_smb_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t         index = 0;
    int32_t         respond = -1;
    unsigned char   receive_data[1024];
    int32_t         receive_cnt = sizeof(receive_data);
    PEDK_SMB_PARM*  receive_p;

    JSValue obj = JS_NewObject(ctx);
    JSValue array = JS_NewArray(ctx);

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR, &respond, receive_data, &receive_cnt, 3);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        receive_p = (PEDK_SMB_PARM*)receive_data;
        JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, receive_p->pedk_smb_index));
        JS_SetPropertyStr(ctx, obj, "smb_name", JS_NewString(ctx, receive_p->pedk_smb_name));
        JS_SetPropertyStr(ctx, obj, "smb_addr", JS_NewString(ctx, receive_p->pedk_smb_addr));
        JS_SetPropertyStr(ctx, obj, "smb_subdirectory", JS_NewString(ctx, receive_p->pedk_smb_subdirectory));
        JS_SetPropertyStr(ctx, obj, "smb_port", JS_NewInt32(ctx, receive_p->pedk_smb_port));
        JS_SetPropertyStr(ctx, obj, "smb_anonymous", JS_NewBool(ctx, receive_p->pedk_smb_anonymous));
        JS_SetPropertyStr(ctx, obj, "smb_login_name", JS_NewString(ctx, receive_p->pedk_smb_login_name));
        JS_SetPropertyStr(ctx, obj, "smb_login_pwd", JS_NewString(ctx, receive_p->pedk_smb_login_pwd));
        return obj;
    }
}

JSValue js_get_smb_addr_list(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t     respond = -1;
    char        receive_data[1024 * 30];
    int32_t     receive_cnt = sizeof(receive_data);


    JSValue array_list = JS_NewArray(ctx);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_LIST, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_LIST, &respond, receive_data, &receive_cnt, 3);

    if ( respond > 0 )
    {
        printf("js_get_smb_addr_list respond %d \n", respond);
        PEDK_SMB_PARM (*list_p)[respond];
        list_p = (PEDK_SMB_PARM(*)[respond])receive_data;
        for ( int32_t i = 0; i < respond; i++ )
        {
            JSValue obj = JS_NewObject(ctx);
            JSValue array = JS_NewArray(ctx);

            JS_SetPropertyStr(ctx, obj, "index", JS_NewInt32(ctx, (*list_p)[i].pedk_smb_index));
            JS_SetPropertyStr(ctx, obj, "smb_name", JS_NewString(ctx, (*list_p)[i].pedk_smb_name));
            JS_SetPropertyStr(ctx, obj, "smb_addr", JS_NewString(ctx, (*list_p)[i].pedk_smb_addr));
            JS_SetPropertyStr(ctx, obj, "smb_subdirectory", JS_NewString(ctx, (*list_p)[i].pedk_smb_subdirectory));
            JS_SetPropertyStr(ctx, obj, "smb_port", JS_NewInt32(ctx, (*list_p)[i].pedk_smb_port));
            JS_SetPropertyStr(ctx, obj, "smb_anonymous", JS_NewBool(ctx, (*list_p)[i].pedk_smb_anonymous));
            JS_SetPropertyStr(ctx, obj, "smb_login_name", JS_NewString(ctx, (*list_p)[i].pedk_smb_login_name));
            JS_SetPropertyStr(ctx, obj, "smb_login_pwd", JS_NewString(ctx, (*list_p)[i].pedk_smb_login_pwd));

            JS_SetPropertyUint32(ctx, array_list, i, obj);
        }
        return array_list;
    }
    else
    {
        return array_list;
    }
}

JSValue js_get_smb_addr_num(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t smb_num;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_NUM, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GET_SMB_ADDR_NUM, &respond, receive_data, &receive_cnt, 3);
    smb_num = receive_data[0];

    return JS_NewInt32(ctx, smb_num);
}

JSValue js_is_smb_addr_full(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    int32_t respond = -1;
    int32_t receive_data[512];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t smb_full = 0;
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_SMB_ADDR_FULL, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_IS_SMB_ADDR_FULL, &respond, receive_data, &receive_cnt, 3);
    smb_full = receive_data[0];
    if ( smb_full )
    {
        return JS_NewBool(ctx, 1);
    }
    else
    {
        return JS_NewBool(ctx, 0);
    }
}

JSValue js_modify_smb_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    PEDK_SMB_PARM   set_smb_info;
    int32_t         respond = -1;
    int32_t         modify_index;

    if ( !JS_IsObject(argv[0]) )
    {
       return JS_NewString(ctx, "The parmeter are the prop_key(Object) ");
    }

    JSValueConst obj = argv[0];

    JSValueConst pedk_smb_index = JS_GetPropertyStr( ctx, obj, "index");
    JSValueConst pedk_smb_name = JS_GetPropertyStr( ctx, obj, "smb_name");
    JSValueConst pedk_smb_addr = JS_GetPropertyStr( ctx, obj, "smb_addr");
    JSValueConst pedk_smb_subdirectory = JS_GetPropertyStr( ctx, obj, "smb_subdirectory");
    JSValueConst pedk_smb_port = JS_GetPropertyStr( ctx, obj, "smb_port");
    JSValueConst pedk_smb_anonymous = JS_GetPropertyStr( ctx, obj, "smb_anonymous");
    JSValueConst pedk_smb_login_name = JS_GetPropertyStr( ctx, obj, "smb_login_name");
    JSValueConst pedk_smb_login_pwd = JS_GetPropertyStr( ctx, obj, "smb_login_pwd");
    JS_ToInt32(ctx,&modify_index,pedk_smb_index);

    set_smb_info.pedk_smb_index = modify_index;
    const char *c_pedk_smb_name = JS_ToCString(ctx, pedk_smb_name);
    const char *c_pedk_smb_addr = JS_ToCString(ctx, pedk_smb_addr);
    const char *c_pedk_smb_subdirectory = JS_ToCString(ctx, pedk_smb_subdirectory);
    const char *c_pedk_smb_login_name = JS_ToCString(ctx, pedk_smb_login_name);
    const char *c_pedk_smb_login_pwd = JS_ToCString(ctx, pedk_smb_login_pwd);
    strncpy(set_smb_info.pedk_smb_name, c_pedk_smb_name, strlen(c_pedk_smb_name));
    set_smb_info.pedk_smb_name[strlen(c_pedk_smb_name)] = '\0';

    strncpy(set_smb_info.pedk_smb_addr, c_pedk_smb_addr, strlen(c_pedk_smb_addr));
    set_smb_info.pedk_smb_addr[strlen(c_pedk_smb_addr)] = '\0';

    strncpy(set_smb_info.pedk_smb_subdirectory, c_pedk_smb_subdirectory, strlen(c_pedk_smb_subdirectory));
    set_smb_info.pedk_smb_subdirectory[strlen(c_pedk_smb_subdirectory)] = '\0';

    strncpy(set_smb_info.pedk_smb_login_name, c_pedk_smb_login_name, strlen(c_pedk_smb_login_name));
    set_smb_info.pedk_smb_login_name[strlen(c_pedk_smb_login_name)] = '\0';

    strncpy(set_smb_info.pedk_smb_login_pwd, c_pedk_smb_login_pwd, strlen(c_pedk_smb_login_pwd));
    set_smb_info.pedk_smb_login_pwd[strlen(c_pedk_smb_login_pwd)] = '\0';

    JS_ToInt32(ctx, &set_smb_info.pedk_smb_port, pedk_smb_port);
    set_smb_info.pedk_smb_anonymous = JS_ToBool(ctx, pedk_smb_anonymous);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_SMB_ADDR, 0, sizeof(PEDK_SMB_PARM), &set_smb_info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_MODIFY_SMB_ADDR, &respond, NULL, NULL, 3);

    JS_FreeCString(ctx, c_pedk_smb_name);
    JS_FreeCString(ctx, c_pedk_smb_addr);
    JS_FreeCString(ctx, c_pedk_smb_subdirectory);
    JS_FreeCString(ctx, c_pedk_smb_login_name);
    JS_FreeCString(ctx, c_pedk_smb_login_pwd);
    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
}

JSValue js_remove_smb_addr(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv)
{
    uint16_t    index = 0;
    int32_t     respond = -1;

    JS_ToInt32(ctx, &index, argv[0]);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_SMB_ADDR, 0, sizeof(index), &index);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_REMOVE_SMB_ADDR, &respond, NULL, NULL, 3);
    if ( respond < 0 )
    {
        return JS_NewInt32(ctx, 0);
    }
    else
    {
        return JS_NewInt32(ctx, 1);
    }
}



