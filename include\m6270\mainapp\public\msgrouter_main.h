/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file msgrouter_main.h
 * @addtogroup public
 * @{
 * @addtogroup msgrouter_main
 * <AUTHOR>
 * @date 2023-05-21
 * @version v1.0
 * @brief  public msg and module header file
 */
#ifndef __MSGROUTER_MAIN_H__
#define __MSGROUTER_MAIN_H__

#include "pol/pol_define.h"

PT_BEGIN_DECLS

/**
 * @brief  modules id definition
 */
typedef enum{
    MID_NULL=  0,

/**
 * @brief  prohibit new additions before this line, engine module need fixed ID
 */
    MID_TONER_CORE=1, ///<2020-06-15 wangbo
    MID_SSCDC2K=2, ///<2022-03-30 wangbo
    MID_SSCINFOINCHIP=3, ///<2022-04-09 wangbo
    MID_SSCFORWARDTHREAD=4, ///<2022-04-09 wangbo
    MID_SSCCHECKTHREAD=5, ///<2022-06-09 wangbo

/**
 * @brief  prohibit direct assignment after this line
 */
    MID_PORT_USB,
    MID_PORT_NET,
    MID_PORT_WIFI,
    MID_PORT_FILE,
    MID_PORT_RAM,
    MID_PORT_EMAIL,
    MID_PORT_FAX_EMAIL,
    MID_PORT_FTP,
    MID_PORT_IPP,
    MID_PORT_WEBSCAN,

    MID_CONNECT_MGR,

    MID_SYS_JOB_MGR,
    MID_SYS_STATUS_MGR,

/**
 * @brief print modules
 */
    MID_PRINT_JOB_MGR,
    MID_PRINT_JOB_DATA_MGR,
    MID_PRINT_JOB_CTRL_MGR,
    MID_PRINT_MGR,
    MID_PRINT_CONSUMABLE,
    MID_PRINT_VIDEO_MGR,
    MID_PRINT_VIDEO_DRIVER,
    MID_PRINT_VIDEO_READY,
    MID_PRINT_VIDEO_REAP,
    MID_PRINT_STATISTICS,
    MID_PRINT_STATUS_MGR,
    MID_PRINT_TRAY_MGR,
    MID_PRINT_ENGINE_CONFIG,
    MID_PRINT_ENGINE_CAL,

    MID_PRINT_FRAMEWORK,
    MID_PRINT_CTL,
    MID_PRINT_DATA,
    MID_PRINT_IF,
    MID_PRINT_PARSER_IF,
    MID_PRINT_PRE,
    MID_PRINT_RELAY_MGR,
    MID_PRINT_RELAY_RECEIVE,
    MID_PRINT_RELAY_WRITE,
    MID_PRINT_RELAY_READ,
    MID_PRINT_PROCESS,
    MID_PRINT_DEBUG,
    MID_PRINT_FINISHER,
    MID_PRINT_STATUS,
    MID_PRINT_INFO,
    MID_PRINT_APP_STATUS,
    MID_PRINT_STORAGE,
    MID_PRINT_DATA_IMAGE_SAVE,
    MID_PRINT_DATA_IMAGE_SEND,
    MID_PRINT_DATA_IMAGE_CANCEL,
    MID_PRINT_IF_IMAGE,

/**
 * @brief engine modules
 */
    MID_PRINT_ENGINE_MGR,
    MID_PRINT_ENGINE_PROCESS_MGR,

/**
 * @brief parser modules
 */
    MID_PARSER_PJL,
    MID_PARSER_CGDI,
    MID_PARSER_MGDI,
    MID_PARSER_IPS,
    MID_PARSER_ACL,
    MID_PARSER_PWG,
    MID_PARSER_URF,
    MID_PARSER_FPL,  ///< fingerprint print langguage
    MID_PARSER_IMAGE,
    MID_PARSER_JPEG,
    MID_PARSER_CGDI_ACR,
    MID_PARSER_PINCODE,
    MID_PARSER_IF_PWG ,
    MID_PARSER_IF_URF,
    MID_PARSER_SAMPLE,
    MID_PARSER_STATUS,

/**
 * @brief scan modules
 */
    MID_SCAN_JOB_MGR,
    MID_SCAN_MGR,
    MID_SCAN_TEST_MGR,
    MID_SCAN_TEST_ENGINE,
    MID_SCAN_PARSER,
    MID_SCAN_PARSER_NEW,
    MID_SCAN_OUT,
    MID_SCAN_OUT_HOST,
    MID_SCAN_OUT_UDISK,
    MID_SCAN_OUT_FTP,
    MID_SCAN_OUT_SMB,
    MID_SCAN_OUT_EMAIL,
    MID_SCAN_OUT_HTTP,
	MID_SCAN_OUT_APP,
    MID_SCAN_OUT_AIRSCAN,
    MID_SCAN_OUT_WSDSCAN,
    MID_SCAN_OUT_FAX,
    MID_SCAN_ADF_TRAY,
    MID_SCAN_STATISTICS,
    MID_SCAN_STATUS_MGR,
    MID_SCAN_ENGINE_TEST,
    MID_SCAN_ENGINE_ACK,
    MID_SCAN_ENGINE_MGR,
    MID_SCAN_INTERFACE_TEST,
    MID_SCAN_INTERFACE_ACK,
    MID_SCAN_INTERFACE_MGR,
    MID_SCAN_DEBUG_MGR,
    MID_SCAN_PCIE_RCV_DEBUG,
    MID_SCAN_PCIE_NTF_DEBUG,
/**
 * @brief copy modules
 */
    MID_COPY_JOB_MGR,
    MID_COPY_MGR,
    MID_COPY_STATISTICS,
    MID_COPY_STATUS_MGR,
    MID_COPY_DEBUG,

    MID_IMAGE_SAVE,
    MID_IMAGE_SEND,
    MID_IMAGE_CANCEL,

/**
 * @brief fax modules
 */
    MID_FAX_JOB_MGR,
    MID_FAX_MGR,
    MID_FAX_STATISTICS,
    MID_FAX_PRN_MGR,
    MID_FAX_PRN,
    MID_FAX_SCN_MGR,
    MID_FAX_SCN_DATA_MGR,
    MID_FAX_STATUS_MGR,
    MID_FAX_PARSER_MGR,
    MID_FAX_REPORT,
    MID_AIRFAX_MGR,

/**
 * @brief image process modules
 */
    MID_IMAGE_PROCESS,
    MID_PIP_PROCESS,
    MID_CORRECT_PROCESS,
    MID_PIP_FRONT_PAGE_MGR,
    MID_PIP_BACK_PAGE_MGR,
    MID_ROTATE_PROCESS,
    MID_JPEG_PROCESS,
    MID_JPEG_PROCESS_INSIDE,

    MID_EMMC_CACHE,

    MID_LOW_POWER,
    MID_PLATFORM,
    MID_INTERNAL_PAGE,
    MID_INTERNAL_PAGE_STORE,
    MID_NETWORK,
    MID_NET_GEN_REPORT,

/**
 * @brief ui module
 */
    MID_PANEL,

    MID_FINGER,
    MID_FINGER_STATUS_MGR,

    MID_CONSUMABLE,
    MID_CARTRIDGE,
    MID_TONER,
    MID_AIRSCAN,
    MID_WSDSCAN,
    MID_MANAGESERVER,
    MID_CARDSERVER,

    MID_GCP,

/**
 * @brief pincode module
 */
    MID_PINCODE_PARSER,
    MID_PINCODE_PRN,


    MID_UDISK_PRN, ///< udiskprint module

/**
 * @brief JBIG
 */
    MID_JBIG_WORK_THREAD,
    MID_JBIG_INPUT_MGR,
    MID_JBIG_OUTPUT_MGR,

    MID_UPGRADE,
    MID_ACR_MGR,

/**
 * @brief power manager module
 */
    MID_POWER_MANAGER,
    MID_PARSER_DELAY,

/**
 * @brief PRINT SDK module
 */
    MID_PRNSDK_NET,
    MID_PRNSDK_STATE_MGR,
    MID_PRNSDK_AUTH_LICENSE,

/**
 * @brief PEDK module
 */
    MID_PEDK,

/**
 * @brief MUST add MID above this line
 */
    MID_INVALID ,
}MODULE_ID_E;

#define MID_NUMBERS     MID_INVALID

typedef enum {
    MSG_NULL = 0,
/**
 * @brief  prohibit new additions before this line, engine module need fixed ID
 */
    MSG_CHECK_CMYK = 1,
    MSG_SYNC_CMYK = 2,
    MSG_SYNC_TO_C = 3,
    MSG_SYNC_TO_M = 4,
    MSG_SYNC_TO_Y = 5,
    MSG_SYNC_TO_K = 6,
    MSG_SSCDC2K_CHECK = 7,
    MSG_SSCDC2K_SET = 8,
    MSG_SSCDC2K_GET = 9,
    MSG_SSCDC2K_S_SET = 10,
    MSG_SSCDC2K_S_GET = 11,
    MSG_SSCDC2K_DIRECT_TO_CHIP = 12,
    MSG_SSCDC2K_CRYPTO2K = 13,
    MSG_SSCDC2K_CRYPTO2K2 = 14,
    MSG_SSCDC2K_CRYPTO2K3 = 15,
    MSG_SSCDC2K_ZB_R = 16,
    MSG_SSCDC2K_ZB_W = 17,
    MSG_SSCDC2K_ZB_E = 18,
    MSG_SSCDC2K_VCC_ON = 19,
    MSG_SSCDC2K_VCC_OFF = 20,
    MSG_SSCDC2K_IIC_SEND = 21,
    MSG_SSCDC2K_IIC_RECE = 22,
    MSG_SSCDC2K_GET_AGAIN = 23,
    MSG_SSCINFOINCHIP_CHECK = 24,
    MSG_SSCINFOINCHIP_SET = 25,
    MSG_SSCINFOINCHIP_C2 = 26,
    MSG_SSCINFOINCHIP_ZbRead = 27,
    MSG_SSCINFOINCHIP_ZbWrite = 28,
    MSG_SSCINFOINCHIP_ZbErase = 29,
    MSG_SSC_GI = 30,
    MSG_SSC_SS = 31,
    MSG_SSC_ZB_R = 32,
    MSG_SSC_ZB_W = 33,
    MSG_SSC_ZB_E = 34,
    MSG_SSC_C2 = 35,
    MSG_SSC_C22 = 36,
    MSG_SSC_C23 = 37,
    MSG_SSC_SN0 = 38,
    MSG_SSC_SN1 = 39,
    MSG_SSC_SN2 = 40,
    MSG_SSC_SN3 = 41,
    MSG_SSC_SN4 = 42,
    MSG_SSC_SN5 = 43,
    MSG_SSC_SN6 = 44,
    MSG_SSC_SN7 = 45,
    MSG_SSC_GI_A = 46,
    MSG_SSC_MER = 47,
    MSG_SSC_USE64K = 48,
    MSG_SSC_C20220817 = 49,
    MSG_SSC_SS1 = 50,
    MSG_SSC_PKI_READ = 51,
    MSG_SSC_PKI_VERIFY = 52,
    MSG_SSC_PKI_CANREAD = 53,
    MSG_SSC_PKI_CANVERIFY = 54,
    MSG_SSC_UP_ERASE = 55,
    MSG_SSC_UP_WRITE = 56,
    MSG_SSC_UP_READ = 57,
    MSG_SSC_UP_VERIFY = 58,
    MSG_SSC_CHECK = 59,
    MSG_SSCDC2K_Z_CRYPTO2K = 60,
    MSG_SSC_Z_C2 = 61,
    MSG_SSC_Z_C22 = 62,
    MSG_SSC_Z_C23 = 63,
    MSG_SSCDC2K_ZZB_R = 64,
    MSG_SSCDC2K_ZZB_W = 65,
    MSG_SSCDC2K_ZZB_E = 66,
    MSG_SSC_ZZB_R = 67,
    MSG_SSC_ZZB_W = 68,
    MSG_SSC_ZZB_E = 69,
    MSG_SSC_ECC = 70,
    MSG_SSC_ZSS = 71,
    MSG_SSCDC2K_ZSET = 72,
    MSG_SSCDC2K_ZGET = 73,
    MSG_SSCDC2K_ZSN = 74,
    MSG_SSC_ZGI = 75,
    MSG_SSC_ZS0 = 76,
    MSG_SSC_ZS1 = 77,
    MSG_SSC_ZS2 = 78,
    MSG_SSC_ZGS = 79,

/**
 * @brief  prohibit direct assignment after this line
 */
    MSG_DATA_JOB_START,
    MSG_DATA_JOB_PAUSE,
    MSG_DATA_JOB_RESTART,
    MSG_DATA_JOB_END,
    MSG_DATA_JOB_SAVE_DONE,
    MSG_DATA_PAGE_START,
    MSG_DATA_PAGE_END,
    MSG_DATA_PLANE_START,
    MSG_DATA_PLANE_DATA,
    MSG_DATA_PLANE_END,
    MSG_DATA_JOB_ABORT,
    MSG_DATA_MEMORY_LOW,
    MSG_DATA_PREPRINT,
    MSG_DATA_IMAGE_SAVE,
    MSG_DATA_IMAGE_SEND,
    MSG_DATA_IMAGE_CANCEL,
    MSG_DATA_IMAGE_REMOVE,
    MSG_DATA_PINCODE_CONTROL,
    MSG_DATA_DELAY_CONTROL,
    MSG_DATA_FINISHER_REMOVE,

    MSG_CTRL_JOB_CONTINUE,
    MSG_CTRL_JOB_SUSPEND,
    MSG_CTRL_JOB_SUSPEND_ACK,
    MSG_CTRL_JOB_RESUME,
    MSG_CTRL_JOB_RESUME_ACK,
    MSG_CTRL_JOB_INSERT,
    MSG_CTRL_INTERRUPT_MODE,
    MSG_CTRL_INTERRUPT_MODE_ACK,
    MSG_CTRL_INTERRUPT_RELIEVE,
    MSG_CTRL_INTERRUPT_RELIEVE_ACK,
    MSG_CTRL_JOB_ROLLBACK,
    MSG_CTRL_JOB_RECOVERY,
    MSG_CTRL_JOB_REMOVE,
    MSG_CTRL_FORCE_PRINT,
    MSG_CTRL_ENGINE_FLUSH,
    MSG_CTRL_ENGINE_ROLLBACK,

    MSG_CTRL_QIO_START,
    MSG_CTRL_QIO_END,
    MSG_CTRL_QIO_UPGRADE,
    MSG_CTRL_QIO_INVALID,
    MSG_CTRL_JOB_REQUEST,
    MSG_CTRL_JOB_RUN,
    MSG_CTRL_JOB_START,
    MSG_CTRL_JOB_DONE,
    MSG_CTRL_JOB_CANCEL,
    MSG_CTRL_JOB_CANCEL_ACK,
    MSG_CTRL_JOB_AUTO_CANCEL,
    MSG_CTRL_JOB_PAUSE,
    MSG_CTRL_JOB_PAUSE_ACK ,
    MSG_CTRL_JOB_RESTART,
    MSG_CTRL_JOB_RESTART_ACK,
    MSG_CTRL_JOB_WAIT,
    MSG_CTRL_JOB_GOON,
    MSG_CTRL_JOB_ABORT,
    MSG_CTRL_JOB_JOBOWNER_UPDATE,
    MSG_CTRL_JOBID_GENERATE,
    MSG_CTRL_PAGE_NEXT,
    MSG_CTRL_GET_CORRECT_PASSWORD,
    MSG_CTRL_SECURE_JOB_CANCEL,
    MSG_CTRL_AUDIT_JOB_CANCEL,
    MSG_CTRL_SYSTEM_UNACTIVED,

    MSG_CTRL_RESOURCE_LINK,
    MSG_CTRL_RESOURCE_START,
    MSG_CTRL_RESOURCE_IO_FREE,
    MSG_CTRL_RESOURCE_FREE,
    MSG_CTRL_CLEAR_ERRORS,
    MSG_CTRL_USER_INPUT,
    MSG_CTRL_JOB_RERUN,
    MSG_CTRL_CHANGE_JOB_ATTR,

    MSG_PRINT_DATA_FORMAT,
    MSG_PRINT_DATA_STORAGE,
    MSG_PRINT_RELAY_INIT,
    MSG_PRINT_RELAY_JOB_REQ,
    MSG_PRINT_RELAY_PAGE_REQ,
    MSG_PRINT_SHEET_START,
    MSG_PRINT_SHEET_DONE,
    MSG_PRINT_SHEET_SAVE_DONE,
    MSG_PRINT_SHEET_CANCEL,
    MSG_PRINT_SHEET_RESTART,
    MSG_PRINT_PAGE_START,
    MSG_PRINT_PAGE_DONE,
    MSG_PRINT_JOB_DONE,
    MSG_PRINT_VIDEO_START,
    MSG_PRINT_VIDEO_DONE,
    MSG_PRINT_VIDEO_READY,
    MSG_PRINT_VIDEO_DOT_SET,
    MSG_PRINT_VIDEO_CANCEL,
    MSG_PRINT_VIDEO_PAGE_TYPE,
    MSG_PRINT_VIDEO_CLEAR_TYPE,
    MSG_PRINT_VIDEO_CHANGE_TYPE,
    MSG_PRINT_VIDEO_CALIBRATE,
    MSG_PRINT_VIDEO_CALIBRATE_UPDATE,
    MSG_PRINT_VIDEO_STATUS_UPDATE,
    MSG_PRINT_VIDEO_PRINT_INTERIOR_PAGE,
    MSG_PRINT_ENGINE_THRESHOLD,
    MSG_PRINT_STATUS_UPDATE,
    MSG_PRINT_STATUS_PRINTAPP_UPDATE,
    MSG_PRINT_STATUS_IMAGE_UPDATE,
    MSG_PRINT_STATUS_INNER_REQUEST,
    MSG_PRINT_STATUS_OUTSIDE_REQUEST,
    MSG_PRINT_STATUS_INSERT_REQUEST,
    MSG_PRINT_STATUS_SUBMOD_REGIST,
    MSG_PRINT_STATUS_CONFIG_UPDATE,
    MSG_PRINT_TONER_STATUS,
    MSG_PRINT_QUIET_PRINT,
    MSG_PRINT_TRAY_STATUS_UPDATE,
    MSG_PRINT_TRAY_WARNING_IDLE,
    MSG_PRINT_IGNORE_TONER_ERROR,
    MSG_PRINT_PREPRINT_START,
    MSG_PRINT_PRINTCHECK_DONE,
    MSG_PRINT_ENGINE_CALIBRATION,
    MSG_PRINT_ENGINE_CALIBRATION_PREPARE,
    MSG_PRINT_ENGINE_CALIBRATION_DONE,
    MSG_PRINT_ENGINE_CALIBRATION_PARAM_REC_DONE,
    MSG_PRINT_ENGINE_OPC_CALIBRATION_PREPARE,
    MSG_PRINT_ENGINE_CAL_TIMEOUT,
    MSG_PRINT_ENGINE_CONFIG_SET,
    MSG_PRINT_ENGINE_CONFIG_GET,
    MSG_PRINT_ENGINE_SERIAL_SET,
    MSG_PRINT_ENGINE_SERIAL_SYNC,
    MSG_PRINT_ENGINE_DIAGNOSTICMODE_CHANGED,
    MSG_PRINT_ENGINE_JOBDELAYCANCEL,
    MSG_PRINT_ENGINE_RESET,
    MSG_PRINT_ENGINE_TONER_UPADTE,
    MSG_PRINT_ENGINE_RUNONEMPTY_CHECK,
    MSG_PRINT_ENGINE_RUNONEMPTY_COPYCHECK,
    MSG_PRINT_ENGINE_PROCESS,
    MSG_PRINT_ENGINE_PROCESS_HANDSHAKE,
    MSG_PRINT_ENGINE_STATUS_CHANGED,
    MSG_PRINT_ENGINE_PRINT_RESTART,
    MSG_PRINT_REPAIR_MODE_NOTIFY,
    MSG_PRINT_INTERNAL_IMAGE,
    MSG_PRINT_QUICKTEST_IMAGE,
    MSG_PRINT_PARSER_IMAGE,
    MSG_PRINT_SAVE_JOB_START,
    MSG_PRINT_SAVE_PAGE_START,
    MSG_PRINT_SAVE_PLANE_DATA,
    MSG_PRINT_SAVE_PAGE_END,
    MSG_PRINT_SAVE_JOB_END,
    MSG_PRINT_SAVE_SEND_DATA,
    MSG_PRINT_JOB_CANCEL_REQ,
    MSG_PRINT_JOB_CANCEL_DONE,
    MSG_PRINT_ENGINE_ERROR,
    MSG_PRINT_SHEET_RESEND,
    MSG_PRINT_SHEET_FLUSH,
    MSG_PRINT_FORCE_RESEND,
    MSG_PRINT_ENGINE_IDLE,
    MSG_PRINT_INIT_FINISH,
    MSG_PRINT_SAMPLE_INIT,
    MSG_PRINT_SAMPLE_PATH,
    MSG_PRINT_NEW_SHEET_SEND,

    MSG_PRINT_ENGINE_SLEEP,
    MSG_PRINT_ENGINE_WAKEUP,
    MSG_PRINT_ENGINE_CANCEL,
    MSG_PRINT_ENGINE_PAUSE,
    MSG_PRINT_ENGINE_FLUSH,
    MSG_PRINT_ENGINE_ROLLBACK,
    MSG_PRINT_ENGINE_STOP_SHEET_SEND,
    MSG_PRINT_ENGINE_ENTER_MAIN_MAINTENANCE,
    MSG_PRINT_ENGINE_EXIT_MAIN_MAINTENANCE,
    MSG_PRINT_ENGINE_MAINTENANCE_GET,
    MSG_PRINT_ENGINE_MAINTENANCE_SET,
    MSG_PRINT_ENGINE_MAINTENANCE_CHECK,
    MSG_PRINT_ENGINE_MAINTENANCE_TEST,
    MSG_PRINT_ENGINE_PRINT_TEST_START,
    MSG_PRINT_ENGINE_MAINTENANCE_REPLY,
    MSG_PRINT_ENGINE_CONSUMPTION_UPDATE,
    MSG_PRINT_ENGINE_DATA_UPLOAD,
    MSG_PRINT_ENGINE_PRINT_PERMISSION,
    MSG_PRINT_IMAGE_OPERATE_FAIL,
    MSG_PRINT_SHEET_SEND_DONE,
    MSG_PRINT_RELAY_FINISHER_JOB_REQ,
    MSG_PRINT_ENGINE_ADVANCE_READY,
    MSG_PRINT_TONER_COVERAGE_CLEAR,
    MSG_PRINT_ENGINE_OPC_CLEAR,

/**
 * @brief ips module message
 */
    MSG_IMAGE_IPS_CALIBRATION_DATA,
    MSG_IAMGE_IPS_TONER_EMPTY_TURN_MONO,

    MSG_SCAN_JOB_REQUEST,
    MSG_SCAN_STATUS_UPDATE,
    MSG_SCAN_SENSOR_UPDATE,
    MSG_SCAN_FEATURE_UPDATE,
    MSG_SCAN_JOB_RESTART,
    MSG_SCAN_JOB_ERROR,
    MSG_SCAN_JOB_CANCEL,
    MSG_SCAN_JOB_ABORT,
    MSG_SCAN_MEMORY_LOW,
    MSG_SCAN_JOB_DONE,
    MSG_SCAN_JOB_PARSER,
    MSG_SCAN_JOB_PAUSE,
    MSG_SCAN_NEXT_PAGE,
    MSG_SCAN_SCANNER_READY,
    MSG_SCAN_WAIT_HOST_QIOEND,
    MSG_SCAN_WAIT_HOST_RESPONSE,
    MSG_SCAN_WAIT_PANEL_CONFIRM,
    MSG_SCAN_SCANNER_ADJUST,
    MSG_SCAN_CALADJUST_SET,
    MSG_SCAN_CALADJUST_RESPOND,
    MSG_SCAN_RESOURCE_LINK,
    MSG_SCAN_RESOURCE_FREE,
    MSG_SCAN_DEBUG_INTERFACE,
    MSG_SCAN_LAST_PAGE,
    MSG_SCAN_CONTINUOUS_SCAN,
    MSG_PCIE_MONITOR_ERROR,
    MSG_PCIE_MSG_RECEIVE,
    MSG_SCAN_ENGINE_JOB_END,
    MSG_SCAN_ENGINE_UPDATE_STATUS,
    MSG_PRINT_ENGINE_GA_DATA,
    MSG_SCAN_BACKUP,                            ///< 扫描留底

/**
 * @brief copy module message
 */
    MSG_COPY_STATUS_UPDATE,
    MSG_COPY_WAIT_PANEL_CONFIRM,
    MSG_COPY_BACKUP_START,
    MSG_COPY_BACKUP_TRANS,
    MSG_COPY_BACKUP_END,

/**
 * @brief fax module message
 */
    MSG_FAX_JOB_REQUEST,
    MSG_FAX_PRINT_CHECK,
    MSG_FAX_PRINT_AVALIABLE,
    MSG_FAX_PRINT_UNAVALIABLE,
    MSG_FAX_HOOK_EVENT,
    MSG_FAX_DIAL_EVENT,
    MSG_FAX_REDIAL_CANCEL_EVENT,
    MSG_FAX_CNG_DETECT,
    MSG_FAX_STATUS_UPDATE,
    MSG_FAX_STATUS_TOMAIL_UPDATE,
    MSG_FAX_REPORT_PRINT,
    MSG_FAX_AT_COMMAND,
    MSG_FAX_REJECT_CALL,
    MSG_FAX_SCAN_CANCEL,
    MSG_FAX_UPDATE_DONE,

/**
 * @brief ips module message
 */
    MSG_IPS_GQIO_ERROR,
    MSG_IPS_GQIO_TIMEOUT,
    MSG_IPS_EOJ,
    MSG_IPS_CANCEL_DONE,

/**
 * @brief image process message
 */
    MSG_IMAGE_PROCESS_START,
    MSG_IMAGE_PROCESS_DONE,

/**
 * @brief low power module message
 */
    MSG_LOWPOWER_ON,
    MSG_LOWPOWER_OFF,
    MSG_LOWPOWER_CPU_FREQUENT_LOW,
    MSG_LOWPOWER_CPU_FREQUENT_NORMAL,
    MSG_POWER_MANAGER_MACHINE_SLEEP,
    MSG_POWER_MANAGER_MACHINE_WAKEUP,
    MSG_POWER_MANAGER_CPU_SLEEP,
    MSG_POWER_MANAGER_CPU_WAKEUP,
    MSG_POWER_MANAGER_ENGINES_SLEEP,

/**
 * @brief internal page module message
 */
    MSG_INTERNAL_PAGE_INFO,
    MSG_INTERNAL_PAGE_FONT,
    MSG_INTERNAL_PAGE_END,
    MSG_INTERNAL_PAGE,
    MSG_INTERNAL_PAGE_PDF,
    MSG_INTERNAL_PAGE_PDF_STORE_RESULT,
    MSG_INTERNAL_PAGE_PDF_STORE_RESULTS_ACK,

/**
 * @brief EmWin module message
 */
    MSG_EW_KEY_INPUT,
    MSG_EW_TOUCH_INPUT,
    MSG_EW_OS_STATE_CHANGE,
    MSG_EW_OS_REDRAW,
    MSG_AIRPRINT_IDENTIFY,
    MSG_EW_AUTO_TRAY_PAPER_PLACE,
    MSG_EW_AUTO_TRAY_PAPER_REMOVE,
    MSG_EW_MANUAL_TRAY_PAPER_PLACE,
    MSG_EW_MANUAL_TRAY_PAPER_REMOVE,
    MSG_EW_USB_INSERT,                               ///<U盘已介入
    MSG_EW_USB_EXTRACT,                               ///<U盘已移除
    MSG_EW_USB_UNMOUNT,                               ///<U盘格式错误
    MSG_EW_USB_OVERLOAD,                            ///<U盘过载
    MSG_EW_ADF_HAVE_PAPER,                             ///<ADF已装入文档
    MSG_EW_ADF_NO_PAPER,                             ///<ADF文档已移除
    MSG_EW_WIFI_DIRECT_TIMEOUT,                       ///<WIFI直连手动确认超时

    MSG_EW_FCT_STATUS,
    MSG_EW_UI_INIT_DONE,                           ///<通知传真模块UI初始化完成
    MSG_EW_FPJOB_CONTINUE,                         ///<指纹打印，用于通知用户打印机中仍有该用户作业

    MSG_EW_SCAN_AUDIT_JOB_ENABLE,
    MSG_EW_COPY_AUDIT_JOB_ENABLE,
    MSG_EW_ILLEGAL_DATA,
    MSG_EW_TURN_OFF_NET,
    MSG_EW_SECRET_WARNING,
    MSG_UPDATE_SOLE_FAIL,                         ///< 唯一序列号写入失败
    MSG_EW_ENTER_CARD_NUMBER,
    MSG_EW_CARD_ENTER_SUCCESS,
    MSG_CARD_NUMBER_IDENT,

/**
 * @brief fw upgrade module message
 */
    MSG_FW_UPGRATE_CONFIRM,
    MSG_FW_UPGRATE_PARSER_FAIL,
    MSG_FW_UPGRATE_CONFIRM_SEC,

    MSG_SUBBOARD_CHECK,
    MSG_SIGNATURE_VERIFY,
    MSG_MUTUAL_AUTHENTICATION,
    MSG_JOB_INPUT_PASSWORD,

/**
 * @brief system status mgr message
 */
    MSG_SM_UPDATE_STATUS,
    MSG_SM_UPDATE_PARAM,
    MSG_SM_CONFIRM_ALERT,
    MSG_SM_STATUS_RESEND,

/**
 * @brief pincode status message
 */
    MSG_PINCODE_DATA_START,
    MSG_PINCODE_DATA_COMPLETE,
    MSG_PINCODE_DATA_ERROR,

    MSG_FINGER_UNDISCOVERED_DEVICE,                 ///<未接入指纹模块设备
    MSG_FINGER_INSERT,                              ///<指纹模块已接入
    MSG_FINGER_REMOVE,                              ///<指纹模块已移除
    MSG_FINGER_ENROLL,                              ///<录入指纹
    MSG_FINGER_ENROLL_FAIL,                         ///<录入指纹失败
    MSG_FINGER_ENROLL_SUCCESS,                      ///<录入指纹成功
    MSG_FINGER_IDENT_FAIL,                          ///<认证指纹失败
    MSG_FINGER_IDENT_SUCCESS,                       ///<认证指纹成功
    MSG_FINGER_IDENT,                               ///<认证指纹
    MSG_FINGER_DELETE,                              ///<删除指纹
    MSG_FINGER_DELETE_FAIL,                         ///<删除指纹失败
    MSG_FINGER_DELETE_SUCCESS,                      ///<删除指纹成功
    MSG_FINGER_UPDATE_HOST_MARK,                    ///<更新主机标识
    MSG_FINGER_JOB_CANCEL,                          ///<作业取消
    MSG_FINGER_ENROLL_CANCEL,                       ///<指纹录入前取消
    MSG_FINGER_USER_DATA_RECOVER,                   ///<指纹用户数据恢复
    MSG_FINGER_MOD_MISMATCH,                        ///<指纹模块不匹配
    MSG_FINGER_MOD_CHANGE,                          ///<指纹模块型号已变更

    MSG_SYS_FLASH_ERROR,


    MSG_EW_UP_PAPER_WHITE_CORECTION_OK, ///< 反馈给UI，工装白条校准成功消息 ; leiyanhu 20171107
    MSG_EW_UP_GLASS_WHITE_CORECTION_OK, ///< 反馈给UI，玻璃板下的白条校准成功消息 leiyanhu  20171107

    MSG_PRINT_SEND_ACK,
    MSG_PRINT_RECV_ACK,
    MSG_PRINT_RECV_NAK,
    MSG_PRINT_RECV_REPORT,

    MSG_SERVER_COPY_PAGE_INFO,
    MSG_ENTER_CARD_NUMBER,

/**
 * @brief JBIG
 */
    MSG_JBIG_OUTPUT_DISTRIBUTE,                      ///< jbig output thread distrbute
    MSG_JBIG_VIDEO_DRIVER_DECODE,                    ///< data from video driver to jbig for decode
    MSG_JBIG_VIDEO_DRIVER_IOCTL,                     ///< message from video driver to jbig for ioctl

    MSG_ACR_MGR_START, 								///< 开始色彩校正
    MSG_ACR_MGR_STARTUP, 							///< 正式启动
    MSG_ACR_MGR_DONE, 						        ///<  ACR结束
    MSG_DATA_SEND_CANCEL, 								///< data cancel
    MSG_CTRL_JOB_ATTRIBUTE_INCORRECT,                 ///<非法数据

    MSG_PORT9120_RESPONSE,                           ///< 9120端口响应
    MSG_PKI_RESPONSE,

    MSG_PRINT_SLEEP_CONFIG,
	MSG_PRINT_WAKEUP_CONFIG,
	MSG_SCAN_SLEEP_CONFIG,
	MSG_SCAN_WAKEUP_CONFIG,

	MSG_SCAN_FILE_STATUS_UPDATE,

    MSG_CTRL_BOOK_SCAN_NEXT_PAGE,

/**
 * @brief PRINT SDK MSG
 */
    MSG_PRNSDK_NET_UI_RS,
    MSG_PRNSDK_NET_MANAGE_RS,
    MSG_PRNSDK_JOB_INFO,
    MSG_PRNSDK_PANEL_INFO,                          ///< PRNSDK panel license result info
    MSG_PRNSDK_ENABLE_CLOSE,                        ///< PRNSDK enable switch close
    MSG_PRNSDK_LOGOUT_PANEL,                        ///< PRNSDK logout display panel
    MSG_PRNSDK_LOGIN_PANEL,                         ///< PRNSDK login to panel
    MSG_PRNSDK_ACCESS_PANEL,                        ///< PRNSDK access to panel
    MSG_PRNSDK_AUTHORITY_PANEL,                     ///< PRNSDK authority to panel

/**
 * @brief PEDK MSG
 */
    MSG_PEDK_APP_INSTALL,                           ///< PEDK APP install and notify to panel

/**
 * @brief MUST add MSG above this line
 */
    MSG_LAST_MSG ,
}MSG_TYPE_E;

#define MSG_NUMBERS      MSG_LAST_MSG

/**
 * @brief message action to notice module
 */
typedef enum
{
    MSG_ACTION_NONE = 0,

/**
 * @brief system action for different modules or system mgr modules
 */
    SYS_REQUEST,
    SYS_START,
    SYS_ACK,
    SYS_NAK,
    SYS_REQUEST_FAIL,

/**
 * @brief local action among submodules of a module
 */
    LOCAL_REQUEST,
    LOCAL_START,
    LOCAL_ACK,
    LOCAL_NAK,

/**
 * @brief MUST add your action above this line!
 */
    DOACTION_NUM_ENUMS
}
MSG_ACTION_E;

/**
 * @brief     get  name string of module_id ,which is according to  the MODULE_ID_E define
 * @param[in] module_id: specfic module id
 * @return    name string of the module id
 * @retval    name string of the module id
 * <AUTHOR>
 * @date    2023-5-26
*/

__attribute__((unused))  const char* get_module_id_string(MODULE_ID_E module_id);


/**
 * @brief     get  name string of msg_type,which is according to the MSG_TYPE_E define
 * @param[in] module_id: specfic msg_type
 * @return    name string of the msg_type
 * @retval    name string of the msg_type
 * <AUTHOR>
 * @date      2023-5-26
*/
__attribute__((unused))  const char* get_msg_type_string(MSG_TYPE_E msg_type);
PT_END_DECLS

#endif /* __MSGROUTER_MAIN_H__ */
/**
 *@}
 */
