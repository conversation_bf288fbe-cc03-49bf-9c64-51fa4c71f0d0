/** * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_dc_communication_cmd
 * @addtogroup proxy
 * @{
 * <AUTHOR>
 * @date 2023-05-30
 * @brief inter-board communication cmmand
*/
#ifndef _PANEL_DC_COMMUNICATION_CMD_H
#define _PANEL_DC_COMMUNICATION_CMD_H

#define    INFO_CMD_INIT                        0           ///< info commands initial value
#define    SETTING_PROPERTY_PRINTER_CMD_INIT    0           ///< printer property commands initial value
#define    SETTING_PROPERTY_JOB_CMD_INIT        8192        ///< job property commands initial value
#define    SETTING_PROPERTY_MAINTENANCE_CMD_INIT        16384       ///< job property commands initial value
#define    JOB_PRINT_CMD_INIT                   0           ///< job property commands initial value
#define    JOB_COPY_CMD_INIT                    8192        ///< job property commands initial value
#define    JOB_SCAN_CMD_INIT                    16384       ///< job property commands initial value
#define    JOB_FAX_CMD_INIT                     24576       ///< job property commands initial value
#define    OPERATE_CMD_INIT                     0           ///< job property commands initial value
#define    STATUS_CMD_INIT                      0           ///< job property commands initial value
#define    RESOURCE_PICTURE_CMD_INIT            41          ///< job property commands initial value
#define    RESOURCE_DATABASE_CMD_INIT           0           ///< job property commands initial value
#define    UPGRADE_CMD_INIT                     0           ///< job property commands initial value
#define    OTHER_CMD_INIT                       0           ///< job property commands initial value

/**
 * @brief
  */
typedef enum {
    INFO_CMD_GET_FW_VERSION = INFO_CMD_INIT,    ///< get firmware version
    INFO_CMD_GET_PANEL_VERSION,    ///< get panel firmware version
    INFO_CMD_GET_PRINTER_ENG_VERSION,    ///< get printer engine version
    INFO_CMD_GET_SCANNER_ENG_VERSION,    ///< get scanner engine version
    INFO_CMD_GET_FPGA1_VERSION,    ///< get fpga1 version
    INFO_CMD_GET_FPGA2_VERSION,    ///< get fpga2 version
    INFO_CMD_GET_DEVICE_NAME,    ///< get device name
    INFO_CMD_GET_PRINT_ENG_INFO,    ///< get print engine config,include version,toner id
} INFO_CMD_E;

/**
 * @brief
  */
typedef enum {
    SETTING_CMD_GET_SYS_TIME = SETTING_PROPERTY_PRINTER_CMD_INIT,    ///< get system time
    SETTING_CMD_SET_SYS_TIME,    ///< set system time
    SETTING_CMD_GET_SLEEP_TIME,    ///< get sleep time
    SETTING_CMD_SET_SLEEP_TIME,    ///< set sleep time
    SETTING_CMD_GET_TIME_FORMAT,    ///< get time format
    SETTING_CMD_SET_TIME_FORMAT,    ///< set time format
    SETTING_CMD_GET_DATE_FORMAT,    ///< get date format
    SETTING_CMD_SET_DATE_FORMAT,    ///< set date format
    SETTING_CMD_GET_BRIGHTNESS,    ///< get screen brightness
    SETTING_CMD_SET_BRIGHTNESS,    ///< set screen brightness
    SETTING_CMD_GET_BEEP_VOLUE,    ///< get volume
    SETTING_CMD_SET_BEEP_VOLUE,    ///< set volume
    SETTING_CMD_GET_MUTE_MODE,    ///< get mute mode
    SETTING_CMD_SET_MUTE_MODE,    ///< set mute mode
    SETTING_CMD_GET_SYS_LANGUAGE,    ///< get system language
    SETTING_CMD_SET_SYS_LANGUAGE,    ///< set system language
    SETTING_CMD_GET_USB_CONTROL,    ///< get usb port control
    SETTING_CMD_SET_USB_CONTROL,    ///< set usb port control
    SETTING_CMD_GET_AUTO_SHUTDOWN_EN,    ///< get auto turn off enable
    SETTING_CMD_SET_AUTO_SHUTDOWN_EN,    ///< set auto turn off enable
    SETTING_CMD_GET_AUTO_SHUTDOWN_TIME,    ///< get auto turn off time
    SETTING_CMD_SET_AUTO_SHUTDOWN_TIME,    ///< set auto turn off time
    SETTING_CMD_GET_AUTO_SHUTDOWN_CONDITION,    ///< get auto turn off condition
    SETTING_CMD_SET_AUTO_SHUTDOWN_CONDITION,    ///< set auto turn off condition
    SETTING_CMD_SET_NETWORK_IPV4_CONFIG,    ///< set ipv4 config
    SETTING_CMD_SET_NETWORK_HOST_NAME,    ///< set host name
    SETTING_CMD_SET_NETWORK_IPV6_SWITCH,    ///< set ipv6 switch
    SETTING_CMD_UPDATE_NETWORK_IPV4_ADDR,    ///< update ipv4 addr
    SETTING_CMD_UPDATE_NETWORK_IPV4_MASK,    ///< upfate ipv4 mask
    SETTING_CMD_UPDATE_NETWORK_IPV4_GATEWAY,    ///< update ipv4 gateway
    SETTING_CMD_GET_SLEEP_MODE,    ///< get system sleep mode
    SETTING_CMD_SET_SLEEP_MODE,    ///< set system sleep mode
    SETTING_CMD_SET_NET_CONTROL,    ///< set net eth control
    SETTING_CMD_UPDATE_NETWORK_IPV4_DHCP_SWITCH,    ///< update ipv4 dhcp
    SETTING_CMD_UPDATE_NETWORK_ETH_SWITCH,    ///< update network eth switch statu
    SETTING_CMD_GET_WATER_MARK,    ///< get water mark from webpage
    SETTING_CMD_GET_PRINT_TRAY,    ///< get print tray info
    SETTING_CMD_GET_PRINT_CONSUM,    ///< get print consum remain info
    SETTING_CMD_GET_SCAN_AREA_SIZE,    ///< get scan area size
    SETTING_CMD_GET_PRINT_STATISTIC_INFO,    ///< get print statistic info
    SETTING_CMD_DATA_SYNC,    ///< data sync  !!!proxy will send this cmd to distributor when wakeup form deepsleep
    SETTING_CMD_GET_PRINT_TRAY_INSTALL,    ///< tray install info
    SETTING_CMD_GET_PRINT_CONSUM_PARAM,    ///< get print consum id info
    SETTING_CMD_SET_AMBIENT_LIGHT,    ///< panel set ambient light turn on/off
    SETTING_CMD_SET_JOB_ERROR_PROCESS_MODE,    ///< job error process mode
    SETTING_CMD_SET_JOB_ERROR_DELETE_TIME,    ///< delete job time when job error
    SETTING_CMD_GET_MACHINE_COLOR_MODE,    ///< notify panel the machine color mode
    SETTING_CMD_UPDATE_WIFI_SWITCH_CHANGED,    ///< DC update wifi Switch status
    SETTING_CMD_UPDATE_WIFI_CONNECTION_CHANGED,    ///< DC update wifi connect status
    SETTING_CMD_UPDATE_WIFI_SCAN_RESULT_CHANGED,    ///< DC update wifi scan status
    SETTING_CMD_UPDATE_WIFI_WPS_PIN_CHANGED,    ///< DC update wifi wps status
    SETTING_CMD_SET_WIFI_SWITCH,    ///< panel request to set wifi switch
    SETTING_CMD_SET_WIFI_SCAN_SSID,    ///< panel request to scan wifi
    SETTING_CMD_SET_WIFI_CONNECT,    ///< panel request to connect wifi
    SETTING_CMD_SET_WIFI_WPS,    ///< panel request wps
    SETTING_CMD_SET_WIFI_IPV4_CONFIG,    ///< panel request to set wifi ipv4
    SETTING_CMD_SET_WIFI_DNSV4_CONFIG,    ///< panel request to set wifi dns
    SETTING_CMD_SET_WIFI_IPV6_CONFIG,    ///< panel request to set wifi ipv6
    SETTING_CMD_SET_WFD_SWITCH,    ///< panel request to set wfd switch
    SETTING_CMD_SET_NET_WFD_RESPONSE,    ///< panel request to set net wfd response
    SETTING_CMD_UPDATE_WIFI_MAC_CHANGED,    ///< update wifi mac changed
    SETTING_CMD_UPDATE_WIFI_IPV4_DHCP_SWITCH_CHANGED,    ///< update_wifi_ipv4 dhcp switch changed
    SETTING_CMD_UPDATE_WIFI_IPV4_ADDRESS_CHANGED,    ///< update_wifi ipv4 address changed
    SETTING_CMD_UPDATE_WIFI_IPV4_MASK_CHANGED,    ///< update_wifi ipv4 mask changed
    SETTING_CMD_UPDATE_WIFI_IPV4_GATEWAY_CHANGED,    ///< update_wifi ipv4 gateway changed
    SETTING_CMD_UPDATE_WIFI_IPV4_AUTODNS_SWITCH_CHANGED,    ///< update_wifi_ipv4 autodns switch changed
    SETTING_CMD_UPDATE_WIFI_IPV4_PRIMARY_DNS_CHANGED,    ///< update_wifi_ipv4 primary dns changed
    SETTING_CMD_UPDATE_WIFI_IPV4_SECONDARY_DNS_CHANGED,    ///< update_wifi_ipv4 secondary dns changed
    SETTING_CMD_UPDATE_WIFI_IPV6_SWITCH_CHANGED,    ///< update_wifi ipv6 switch changed
    SETTING_CMD_UPDATE_WIFI_IPV6_DHCP_SWITCH_CHANGED,    ///< update_wifi_ipv6 dhcp switch changed
    SETTING_CMD_UPDATE_WIFI_IPV6_LINK_ADDRESS_CHANGED,    ///< update_wifi_ipv6 link address changed
    SETTING_CMD_UPDATE_WIFI_IPV6_STLS_ADDRESS_CHANGED,    ///< update_wifi_ipv6 stls address changed
    SETTING_CMD_UPDATE_WIFI_IPV6_DHCP_ADDRESS_CHANGED,    ///< update_wifi_ipv6 dhcp address changed
    SETTING_CMD_UPDATE_WIFI_IPV6_PRIMARY_DNS_CHANGED,    ///< update_wifi_ipv6 primary dns changed
    SETTING_CMD_UPDATE_WIFI_IPV6_SECONDARY_DNS_CHANGED,    ///< update_wifi_ipv6 secondary dns changed
    SETTING_CMD_UPDATE_WFD_SWITCH_CHANGED,    ///< update wifi switch changed
    SETTING_CMD_UPDATE_WFD_MAC_CHANGED,    ///< update wfd mac changed
    SETTING_CMD_UPDATE_WFD_SSID_PREFIX_CHANGED,    ///< update_wfd ssid prefix changed
    SETTING_CMD_UPDATE_WFD_SSID_SUFFIX_CHANGED,    ///< update_wfd ssid suffix changed
    SETTING_CMD_UPDATE_WFD_PASSWORD_CHANGED,    ///< update wfd password changed
    SETTING_CMD_UPDATE_WFD_IPV4_ADDRESS_CHANGED,    ///< update_wfd ipv4 address changed
    SETTING_CMD_UPDATE_WFD_IPV4_MASK_CHANGED,    ///< update_wfd ipv4 mask changed
    SETTING_CMD_UPDATE_WFD_IPV4_GATEWAY_CHANGED,    ///< update_wfd ipv4 gateway changed
    SETTING_CMD_UPDATE_WFD_REQUEST_CHANGED,    ///< update wfd request changed
    SETTING_CMD_UPDATE_WFD_ATTACHED_DEVICES_COUNT_CHANGED,    ///< update_wfd_attached devices count changed
    SETTING_CMD_SET_LOCK_SCREEN_PASSWORD,    ///< set lock screen password
    SETTING_CMD_UPDATE_WIFI_SUPPORT,    ///< update wifi support
    SETTING_CMD_SET_HARD_DISK,    ///< control hard disk on/off
    SETTING_CMD_SET_HARD_DISK_ENCRYPT,    ///< hard disk encrypt on/off
    SETTING_CMD_SET_ERROR_STATUS_VOLUME,    ///< set error status volume
    SETTING_CMD_SET_JOB_END_VOLUME,    ///< set job end volume
    SETTING_CMD_UPDATE_NET_ETH_SPEED,    ///< update net eth speed
    SETTING_CMD_SET_COLOR_COPY_ENABLE,    ///< update color copy enable
    SETTING_CMD_SET_COLOR_COPY_PASSWORD,    ///< update color copy password
    SETTING_CMD_SET_SYSTEM_SET_TIMEOUT,    ///< panel set timeout time
    SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_COPY,    ///< panel set control copy
    SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_SCAN,    ///< panel set control scan
    SETTING_CMD_SET_SAFETYMG_JOB_CONTROL_PRINT,    ///< panel set control prin
    SETTING_CMD_SET_LOCK_SCREEN_TIME,    ///< panel set lock screen time
    SETTING_CMD_SET_LOCK_SCREEN_SWITCH,    ///< panel set lock screen switch
    SETTING_CMD_SET_COPY_PARAM_RESET,    ///< panel set copy param reset switch
    SETTING_CMD_UPDATE_USBDEVICE_CONNECT,    ///< update usbdevice connect status
    SETTING_CMD_UPDATE_ETH_CONNECT_CHANGE,    ///< update eth connect status
    SETTING_CMD_SET_SMTP_CONFIG,    ///< panel request set smtp config
    SETTING_CMD_SET_SMTP_SENDER_ADDRESS,    ///< network notify smtp sender address
    SETTING_CMD_SET_SMTP_SERVER_ADDRESS,    ///< network notify smtp server address
    SETTING_CMD_SET_SMTP_SERVER_PORT,    ///< network notify smtp server port
    SETTING_CMD_SET_SMTP_SERVER_AUTH,    ///< network notify smtp server auth
    SETTING_CMD_SET_SMTP_SEC_MODE,    ///< network notify smtp sec mode
    SETTING_CMD_SET_SMTP_USERNAME,    ///< network notify smtp user name
    SETTING_CMD_SET_SMTP_PASSWORD,    ///< network notify smtp password
    SETTING_CMD_SET_WHITELIST_SWITCH,    ///< panel set network whitelist
    SETTING_CMD_UPDATE_FB_ADF_COVER_STATE,    ///< update fb/adf cover state
    SETTING_CMD_SET_COPY_RESET_RANGE_SWITCH,    ///< copy reset range switch
    SETTING_CMD_SET_NET_IPP_IDENTIFY_ACTION,    ///< net update ipp identify action
    SETTING_CMD_SET_JOB_ADVANCE_IMAGE,    ///< set job advance image
    SETTING_CMD_SET_WIFI_WPS_REQUEST,    ///< wifi wps request
    SETTING_CMD_SET_ADF_GET_PAPER_VOLUME,    ///< adf paper in volume
    SETTING_CMD_GET_SCAN_STATISTIC_INFO,    ///< get scan statistic info
    SETTING_CMD_GET_UDISK_REMAIN,    ///< get udisk remian
    SETTING_CMD_GET_PRINT_CONSUMPTION_STATUS,    ///< get print consumption status
    SETTING_CMD_GET_NET_INITIALIZE_DONE,    ///< get net initialize done
} SETTING_PRINTER_CMD_E;

/**
 * @brief
  */
typedef enum {
    SETTING_CMD_PRINT_SET_PRINT_COLOR = SETTING_PROPERTY_JOB_CMD_INIT,    ///<
    SETTING_CMD_PRINT_SET_DEFAULT_TRAY,    ///<
    SETTING_CMD_PRINT_SET_TRAY1_SIZE,    ///<
    SETTING_CMD_PRINT_SET_TRAY1_TYPE,    ///<
    SETTING_CMD_PRINT_SET_TRAY2_SIZE,    ///<
    SETTING_CMD_PRINT_SET_TRAY2_TYPE,    ///<
    SETTING_CMD_PRINT_SET_TRAY3_SIZE,    ///<
    SETTING_CMD_PRINT_SET_TRAY3_TYPE,    ///<
    SETTING_CMD_PRINT_SET_TRAY4_SIZE,    ///<
    SETTING_CMD_PRINT_SET_TRAY4_TYPE,    ///<
    SETTING_CMD_PRINT_SET_TRAY_MULTI_SIZE,    ///<
    SETTING_CMD_PRINT_SET_TRAY_MULTI_TYPE,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM_SIZE_WIDTH,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM_SIZE_HEIGHT,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM1_SIZE_WIDTH,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM1_SIZE_HEIGHT,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM2_SIZE_WIDTH,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM2_SIZE_HEIGHT,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM3_SIZE_WIDTH,    ///<
    SETTING_CMD_PRINT_SET_CUSTOM3_SIZE_HEIGHT,    ///<
    SETTING_CMD_PRINT_SET_COLOR_BALANCE_C,    ///<
    SETTING_CMD_PRINT_SET_COLOR_BALANCE_M,    ///<
    SETTING_CMD_PRINT_SET_COLOR_BALANCE_Y,    ///<
    SETTING_CMD_PRINT_SET_COLOR_BALANCE_K,    ///<
    SETTING_CMD_PRINT_SET_IMAGE_CONTRAST,    ///<
    SETTING_CMD_PRINT_SET_IMAGE_SATURATION,    ///<
    SETTING_CMD_PRINT_SET_IMAGE_BRIGTHNESS,    ///<
    SETTING_CMD_PRINT_SET_TONER_DENISTY,    ///<
    SETTING_CMD_PRINT_SET_SAVE_TONER_MODE,    ///<
    SETTING_CMD_PRINT_SET_SKIP_BLANK_PAGE,    ///<
    SETTING_CMD_PRINT_SET_SEPARATOR_PAGE,    ///<
    SETTING_CMD_PRINT_SET_ERROR_JOB_PROCESS,    ///<
    SETTING_CMD_PRINT_SET_ERROR_JOB_DELAY_DEL_TIME,    ///<
    SETTING_CMD_PRINT_SET_BINDING_MODE,    ///<
    SETTING_CMD_PRINT_SET_SHIFT_MODE,    ///<
    SETTING_CMD_PRINT_SET_PUNCH_MODE,    ///<
    SETTING_CMD_PRINT_SET_FOLD_MODE,    ///<
    SETTING_CMD_PRINT_SET_TRAY_LCT_IN_SIZE,    ///<
    SETTING_CMD_PRINT_SET_TRAY_LCT_IN_TYPE,    ///<
    SETTING_CMD_PRINT_SET_IMAGE_ORIGINAL_TYPE,    ///< original type
    SETTING_CMD_SCAN_SET_ADDRESS_BOOK,    ///< set address book to DC
    SETTING_CMD_SCAN_UPDATE_EMAIL_ADDRESS_BOOK,    ///< DC update email address book
    SETTING_CMD_SCAN_UPDATE_EMAIL_GROUP_BOOK,    ///< DC update email group book
    SETTING_CMD_SCAN_UPDATE_FTP_ADDRESS_BOOK,    ///< DC update ftp address book
    SETTING_CMD_SCAN_UPDATE_SMB_ADDRESS_BOOK,    ///< DC update smb address book
    SETTING_CMD_UDISK_MOUNT_PATH,    ///< DC update udisk mount payh
    SETTING_CMD_UPDATE_UDISK_FILE,    ///< DC update udisk file
    SETTING_CMD_UPDATE_SHORTCUT_COPY,    ///< update shorcut copy data
    SETTING_CMD_OVERLAY_COPY_DATA,    ///< overlay copy data
    SETTING_CMD_PRINT_SET_TRAY_LCT_OUT_SIZE,    ///< lct out tray size
    SETTING_CMD_PRINT_SET_TRAY_LCT_OUT_TYPE,    ///< lct out tray type
} SETTING_JOB_CMD_E;

/**
 * @brief
  */
typedef enum {
    SETTING_CMD_MAINTENANCE_ENTER = SETTING_PROPERTY_MAINTENANCE_CMD_INIT,    ///< maintenance enter
    SETTING_CMD_MAINTENANCE_EXIT,    ///< maintenance exit
    SETTING_CMD_MAINTENANCE_SET_FUSER_TMP,    ///< maintenance set fuser tmp
    SETTING_CMD_MAINTENANCE_SET_FUSER_SPEED,    ///< maintenance set fuser speed
    SETTING_CMD_MAINTENANCE_SET_PRINT_AREA,    ///< maintenance set print area
    SETTING_CMD_MAINTENANCE_SET_PAPER_LOOP,    ///< maintenance set paper loop
    SETTING_CMD_MAINTENANCE_SET_COLOR_POS,    ///< maintenance set color pos
    SETTING_CMD_MAINTENANCE_SET_FAN_DELAY,    ///< maintenance set fan delay
    SETTING_CMD_MAINTENANCE_SET_FINISHER_ADJUSTMENT,    ///< maintenance set finisher adjustment
    SETTING_CMD_MAINTENANCE_SET_MAX_DENSITY,    ///< maintenance set max density
    SETTING_CMD_MAINTENANCE_SET_BACKGROUND_VOLTAGE,    ///< maintenance set background voltage
    SETTING_CMD_MAINTENANCE_SET_MONO_DENSITY,    ///< maintenance set mono density
    SETTING_CMD_MAINTENANCE_SET_NEUTRALIZING_VOLTAGE,    ///< maintenance set neutralizing voltage
    SETTING_CMD_MAINTENANCE_SET_TRANSFER_OUTPUT,    ///< maintenance set transfer output
    SETTING_CMD_MAINTENANCE_SET_CHARGE_AC_OUTPUT,    ///< maintenance set charge ac output
    SETTING_CMD_MAINTENANCE_SET_THICK_PAPER_DENSITY,    ///< maintenance set thick paper density
    SETTING_CMD_MAINTENANCE_SET_DV_AC_CHOICE,    ///< maintenance set dv ac choice
    SETTING_CMD_MAINTENANCE_SET_DV_AC_FREQ_CHOICE,    ///< maintenance set dv ac freq choice
    SETTING_CMD_MAINTENANCE_EXEC_TONER_SUPPLY_CHOICE,    ///< maintenance exec toner supply choice
    SETTING_CMD_MAINTENANCE_SET_TROUBLE_ISOLATION,    ///< maintenance set trouble isolation
    SETTING_CMD_MAINTENANCE_SET_WARMUP_MODE,    ///< maintenance set warmup mode
    SETTING_CMD_MAINTENANCE_SET_LED_STATE,    ///< maintenance set led state
    SETTING_CMD_MAINTENANCE_GET_SENSOR_VALUE,    ///< maintenance get sensor value
    SETTING_CMD_MAINTENANCE_GET_TABLE_VALUE,    ///< maintenance get table value
    SETTING_CMD_MAINTENANCE_GET_VOLTAGE_VALUE,    ///< maintenance get voltage value
    SETTING_CMD_MAINTENANCE_GET_ENVIRONMENT_VALUE,    ///< maintenance get environment value
    SETTING_CMD_MAINTENANCE_GET_CALIBRATION_VALUE,    ///< maintenance get calibration value
    SETTING_CMD_MAINTENANCE_GET_FUSER_TMP,    ///< maintenance get fuser tmp
    SETTING_CMD_MAINTENANCE_GET_FUSER_SPEED,    ///< maintenance get fuser speed
    SETTING_CMD_MAINTENANCE_GET_PRINT_AREA,    ///< maintenance get print area
    SETTING_CMD_MAINTENANCE_GET_PAPER_LOOP,    ///< maintenance get paper loop
    SETTING_CMD_MAINTENANCE_GET_COLOR_POS,    ///< maintenance get color pos
    SETTING_CMD_MAINTENANCE_GET_SKEW_VALUE,    ///< maintenance get skew value
    SETTING_CMD_MAINTENANCE_GET_FAN_DELAY,    ///< maintenance get fan delay
    SETTING_CMD_MAINTENANCE_GET_FINISHER_ADJUSTMENT,    ///< maintenance get finisher adjustment
    SETTING_CMD_MAINTENANCE_GET_MAX_DENSITY,    ///< maintenance get max density
    SETTING_CMD_MAINTENANCE_GET_BACKGROUND_VOLTAGE,    ///< maintenance get background voltage
    SETTING_CMD_MAINTENANCE_GET_MONO_DENSITY,    ///< maintenance get mono density
    SETTING_CMD_MAINTENANCE_GET_NEUTRALIZING_VOLTAGE,    ///< maintenance get neutralizing voltage
    SETTING_CMD_MAINTENANCE_GET_TRANSFER_OUTPUT,    ///< maintenance get transfer output
    SETTING_CMD_MAINTENANCE_GET_CHARGE_AC_OUTPUT,    ///< maintenance get charge ac output
    SETTING_CMD_MAINTENANCE_GET_THICK_PAPER_DENSITY,    ///< maintenance get thick paper density
    SETTING_CMD_MAINTENANCE_GET_DV_AC_CHOICE,    ///< maintenance get dv ac choice
    SETTING_CMD_MAINTENANCE_GET_DV_AC_FREQ_CHOICE,    ///< maintenance get dv ac freq choice
    SETTING_CMD_MAINTENANCE_GET_TROUBLE_ISOLATION,    ///< maintenance get trouble isolation
    SETTING_CMD_MAINTENANCE_GET_WARMUP_MODE,    ///< maintenance get warmup mode
    SETTING_CMD_MAINTENANCE_GET_LED_STATE,    ///< maintenance get led state
    SETTING_CMD_MAINTENANCE_EXEC_SKEW_ADJUST,    ///< maintenance exec skew adjust
    SETTING_CMD_MAINTENANCE_EXEC_MANUAL_TRAY_ADJUST,    ///< maintenance exec manual tray adjust
    SETTING_CMD_MAINTENANCE_EXEC_ENG_RESET,    ///< maintenance exec eng reset
    SETTING_CMD_MAINTENANCE_EXEC_IMAGE_STABILIZE,    ///< maintenance exec image stabilize
    SETTING_CMD_MAINTENANCE_EXEC_FINISHER_SENSOR_CHECK,    ///< maintenance exec finisher sensor check
    SETTING_CMD_MAINTENANCE_EXEC_HOPPER_TONER_FILLING,    ///< maintenance exec hopper toner filling
    SETTING_CMD_MAINTENANCE_EXEC_SENSOR_CHECK,    ///< maintenance exec sensor check
    SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SENSOR,    ///< maintenance scan fb adf sensor
    SETTING_CMD_MAINTENANCE_SCAN_FB_ADF_SCANING_TEST,    ///< maintenance scan fb adf scaning test
    SETTING_CMD_MAINTENANCE_SCAN_ADF_ENGINE_PARAM_SET,    ///< maintenance scan adf engine param set
    SETTING_CMD_MAINTENANCE_SCAN_FB_ENGINE_PARAM_SET,    ///< maintenance scan fb engine param set
    SETTING_CMD_MAINTENANCE_SCAN_ENGINE_ERROR_CLEAR,    ///< maintenance scan engine error clear
    SETTING_CMD_MAINTENANCE_EVENT_LOG,    ///< maintenance event log
    SETTING_CMD_MAINTENANCE_EXEC_NEW_UNIT_CONFIRM,    ///< printf NEW_UNIT_CONFIRM
    SETTING_CMD_MAINTENANCE_GET_LD_ADJUST,    ///< maintenance get ld adjust
    SETTING_CMD_MAINTENANCE_GET_PAPER_FEED_DIRECTION_ADJUST,    ///< maintenance get paper feed direction adjust
    SETTING_CMD_MAINTENANCE_GET_PAPER_SEPARATION_ADJUST,    ///< maintenance get paper separation adjust
    SETTING_CMD_MAINTENANCE_SET_LD_ADJUST,    ///< maintenance set set ld adjust
    SETTING_CMD_MAINTENANCE_SET_PAPER_FEED_DIRECTION_ADJUST,    ///< maintenance set_paper feed direction adjust
    SETTING_CMD_MAINTENANCE_SET_PAPER_SEPARATION_ADJUST,    ///< maintenance set paper separation adjust
    SETTING_CMD_MAINTENANCE_EXEC_MOTOR_TEST,    ///< maintenance exec exec motor test
    SETTING_CMD_MAINTENANCE_EXEC_SIMPLEX_FEED,    ///< maintenance exec  simplex feed
    SETTING_CMD_MAINTENANCE_GET_LOG_TO_WEBPAGE_CHOICE,    ///< webpage log
    SETTING_CMD_MAINTENANCE_GET_HARD_DISK_R_W_RET,    ///< maintenance exec hard disk read write ret
    SETTING_CMD_MAINTENANCE_GET_LOG_SAVE_SWITCH,    ///< get log save switch
} SETTING_MAINTENCE_CMD_E;

/**
 * @brief
  */
typedef enum {
    JOB_CMD_PRINT_START = JOB_PRINT_CMD_INIT,    ///< start print job
    JOB_CMD_PRINT_CANCEL,    ///< cancel print job
    JOB_CMD_PRINT_PUASE,    ///< pause print job
    JOB_CMD_PRINT_CONTINUE,    ///< continue print job
    JOB_CMD_PRINT_INFO_PAGE,    ///< print info page
    JOB_CMD_UDISK_PRINT_START,    ///< udisk print
    JOB_CMD_PINCODE_PRINT_START,    ///< pincode print
    JOB_CMD_DELAY_PRINT_START,    ///< delay print start
    JOB_CMD_SAMPLE_PRINT_START,    ///< sample print start
} PRINT_JOB_CMD_E;

/**
 * @brief
  */
typedef enum {
    JOB_CMD_COPY_START = JOB_COPY_CMD_INIT,    ///< start copy job
    JOB_CMD_COPY_CANCEL,    ///< cancel copy job
    JOB_CMD_COPY_PUASE,    ///< pause copy job
    JOB_CMD_COPY_CONTINUE,    ///< continue copy job
    JOB_CMD_SAMPLE_COPY_CONTINUE,    ///< sample copy continue
} COPY_JOB_CMD_E;

/**
 * @brief
  */
typedef enum {
    JOB_CMD_SCAN_START = JOB_SCAN_CMD_INIT,    ///< start scan job
    JOB_CMD_SCAN_CANCEL,    ///< cancel scan job
    JOB_CMD_SCAN_PUASE,    ///< pause scan job
    JOB_CMD_SCAN_CONTINUE,    ///< continue scan job
} SCAN_JOB_CMD_E;

/**
 * @brief
  */
typedef enum {
    JOB_CMD_FAX_START = JOB_FAX_CMD_INIT,    ///< start fax job
    JOB_CMD_FAX__CANCEL,    ///< cancel fax job
    JOB_CMD_FAX__PUASE,    ///< pause fax job
    JOB_CMD_FAX__CONTINUE,    ///< continue fax job
} FAX_JOB_CMD_E;

/**
 * @brief
  */
typedef enum {
    OPERATE_CMD_CANCEL_JOB_REQUEST = OPERATE_CMD_INIT,    ///< panel request to cancel job
    OPERATE_CMD_SCAN_NEXT_REQUEST,    ///< panel request to scan next page
    OPERATE_CMD_SCAN_NEXT_DONE_REQUEST,    ///< panel request to end scan
    OPERATE_CMD_SLEEP_FIRST_REQUEST,    ///< panel request to first sleep
    OPERATE_CMD_SLEEP_SECOND_REQUEST,    ///< panel request to second sleep
    OPERATE_CMD_WAKEUP_REQUEST,    ///< panel request to wake
    OPERATE_CMD_POWER_OFF_REQUEST,    ///< panel key request to confirm power off
    OPERATE_CMD_DC_INIT_DONE_NOTIFY,    ///< dc request to sync data
    OPERATE_CMD_REQUEST_RESTORE_FACTORY_DEFAULT,    ///< panel request to resore factory default
    OPERATE_CMD_PANEL_KEY_SHORT_PRESS,    ///< panel key short press
    OPERATE_CMD_CANCEL_POWER_OFF,    ///< panel request to cancel power off
    OPERATE_CMD_CLEAR_PRINT_ERRORS,    ///< clear error and continue print
    OPERATE_CMD_UPGRADE_START_PROC,    ///< panel request to start upgrade proc
    OPERATE_CMD_REMOVE_JOB,    ///< panel request to remove job
    OPERATE_CMD_PINCODE_GET_PRINT_JOB,    ///< panel request to print pincode job
    OPERATE_CMD_REQUEST_MONO_PRINT,    ///< panel request to mono print when color toner empty
    OPERATE_CMD_STORAGE_UNINSTALL_REQUEST,    ///< panel request to uninstall mount point
    OPERATE_CMD_STORAGE_REPAIRE_REQUEST,    ///< panel request to repaire mount point
    OPERATE_CMD_STORAGE_FORMAT_REQUEST,    ///< panel request to format mount point
    OPERATE_CMD_STORAGE_CLEAR_ERROR_REQUEST,    ///< panel request to clear error
    OPERATE_CMD_FORCE_PRINT,    ///< panel request to force print
    OPERATE_CMD_CHANGE_JOB_ATTR,    ///< panel request to change job attr
    OPERATE_CMD_GET_UDISK_FILE_REQUEST,    ///< panel request to get udisk file info
    OPERATE_CMD_SATASSD1_CLEAR_ALL_DATA,    ///< panel request to clear all data
    OPERATE_CMD_GET_STORAGE_REMAIN,    ///< get storage(ssd) remain
    OPERATE_CMD_SYSTEM_REBOOT_REQUEST,    ///< panel request to reboot system
    OPERATE_CMD_STORAGEMGR_HOTPLUGDEV_STATE_CHANGED,    ///< disk install state
    OPERATE_CMD_STORAGEMGR_USBDISK_DISABLE,    ///< udisk disable
    OPERATE_CMD_PRIORITY_PRINT,    ///< priority print
    OPERATE_CMD_SUSPEND_PRINT,    ///< suspend print
    OPERATE_CMD_RESUME_PRINT,    ///< resume print
    OPERATE_CMD_JOB_LIST_DATA_SYNC,    ///< job list data sync
    OPERATE_CMD_OFFLINE_UPGRADE_MODE,    ///< panel request to enter offline upgrade mode
    OPERATE_CMD_JOB_ABORT,    ///< job abort
    OPERATE_CMD_STORAGEMGR_SATASSD1_COMPAT_CHECK,    ///< panel request to check ssd compat
    OPERATE_CMD_STORAGEMGR_SATASSD1_HEALTH_CHECK,    ///< panel request to check ssd health
    OPERATE_CMD_STORAGEMGR_SATASSD1_FORMAT,    ///< panel request to format ssd
    OPERATE_CMD_FINISHER_REMOVE,    ///< panel request remove finisher
    OPERATE_CMD_STORAGEMGR_SATASSD1_FIXSTATE,    ///< panel sata ssd fix state
    OPERATE_CMD_STORAGEMGR_SATASSD1_ACTIVATE,    ///< panel sata ssd activate
    OPERATE_CMD_PRINT_DATA_CLEAR,    ///< print data clear
    OPERATE_CMD_ADF_ENGINE_PARAM_SHOW,    ///< adf engine param show
    OPERATE_CMD_UPGRADE_OPERATE,       ///< upgrade operate
    OPERATE_CMD_MACHINE_TYPE_CHANGE,
} OPERATE_CMD_E;

/**
 * @brief
  */
typedef enum {
    STATUS_CMD_STATUS_UPDATE = STATUS_CMD_INIT,    ///< update system status to panel
    STATUS_CMD_JOB_STATUS_UPDATE,    ///< update job status to panel
    STATUS_CMD_JOB_START_RETURN,    ///< return job id and code when start ajob
    STATUS_CMD_GET_PRINT_MULTI_TRAY_PAPER_IN,    ///< multi tray paper in
    STATUS_CMD_UPGRATE_FW_PARSER_FAIL,    ///< upgrate framwork parsing fail
    STATUS_CMD_ENGINES_SLEEP,    ///< notify panel that engine is sleep
    STATUS_CMD_PRINT_ENGINE_ADVANCE_READY,    ///< notify panel that engine advance ready
    STATUS_CMD_SERIAL_NUM_WRITE_FAILED,
} STATUS_CMD_E;

/**
 * @brief
  */
typedef enum {
    RESOURCE_CMD_FILE_ACCESS = RESOURCE_DATABASE_CMD_INIT,    ///< file access
    RESOURCE_CMD_FILE_OPEN,    ///< file open
    RESOURCE_CMD_FILE_READ,    ///< file read
    RESOURCE_CMD_FILE_WRITE,    ///< file write
    RESOURCE_CMD_FILE_SEEK,    ///< file seek
    RESOURCE_CMD_FILE_CLOSE,    ///< file close
} RES_DATABASE_CMD_E;

/**
 * @brief
  */

/**
 * @brief
  */
typedef enum {
    eUPGRADE_CMD_PACKAGE = UPGRADE_CMD_INIT,    ///< upgrade package to panel
    eUPGRADE_CMD_PROGRESS,    ///< updating progress to panel
    eUPGRADE_CMD_STATUS,    ///< the status before upgrade to panel
    eUPGRADE_CMD_OPERATE,    ///< the operate before upgrade to panel
} UPGRADE_CMD_E;

/**
 * @brief
  */
typedef enum {
    OTHER_CMD_PEDK_START = OTHER_CMD_INIT,    ///<
    OTHER_CMD_PEDK_DRAW,    ///<
    OTHER_CMD_PEDK_CTRL_BRIGHTNESS,    ///<
    OTHER_CMD_PEDK_CTRL_BUZZER,    ///<
    OTHER_CMD_PEDK_CTRL_LED,    ///<
    OTHER_CMD_PEDK_KEY,    ///<
    OTHER_CMD_PEDK_TOUCH,    ///<
    OTHER_CMD_PEDK_APP_INSTALL,    ///<
    OTHER_CMD_PEDK_DRAW_EXIT,    ///<
    OTHER_CMD_PEDK_FUNCTION_SWITCH,    ///<
    OTHER_CMD_PEDK_SET_BACK_TIMEOUT_TIME,    ///<
    OTHER_CMD_PEDK_GET_BACK_TIMEOUT_TIME,    ///<
    OTHER_CMD_PEDK_DELETE_BACK_TIMEOUT,    ///<
    OTHER_CMD_PEDK_CHANGE_TO_NATIVE_WINDOW,    ///<
} OTHER_CMD_E;
#endif /* _PANEL_DC_COMMUNICATION_CMD_H */
/**
 *@}
 */
