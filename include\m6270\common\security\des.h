#ifndef __DES_H__
#define __DES_H__
/*
 * DES-CBC buffer encryption API
 */
unsigned int des_cbc_encode_crypt(unsigned char *pout,
							 unsigned char *pdata,
							 unsigned int nlen,
							 unsigned char *pkey,
							 unsigned char *piv);

/*
 * DES-CBC buffer decryption API
 */
unsigned int des_cbc_decode_crypt(unsigned char *pout,
							 unsigned char *pdata,
							 unsigned int nlen,
							 unsigned char *pkey,
							 unsigned char *piv);
#endif