/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_config.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-08-12
 * @brief the common function and set config for panel_dc
 */

#ifndef _PANEL_CONFIG_H
#define _PANEL_CONFIG_H

#include <pol/pol_types.h>
#include "event_manager/event_mgr_typedef.h"
#include "event_manager/event_msg_typedef.h"
#include "panel_public.h"
#include "print.h"
#include "scan_event.h"


#define         VERSION_STR_LEN       16
#define PDT_NAME_LEN            32                            ///< product name length
#define VERSION_LENGTH 32

/**
 * @brief system time struct from panel
 */
typedef struct
{
    uint16_t year;          ///< sys time year
    uint8_t  month;         ///< sys time month
    uint8_t  day;           ///< sys time day
    uint8_t  hour;          ///< sys time hour
    uint8_t  minute;        ///< sys time minute
    uint8_t  second;        ///< sys time second
    uint8_t  time_fmt;      ///< time 12/24
}UI_DATE_TIME_S;

/**
 * @brief scan paper size
*/
typedef struct
{
    uint32_t     adf_size;    ///< adf paper size
    uint32_t     fb_size;     ///< fb paper size
} SCAN_PAPER_SIZE_S;

/**
 * @brief get firmware version
 * <AUTHOR>
 * @date 2023-08-14
 */
typedef struct
{
    char engine_version[VERSION_LENGTH];
    char front_fpga_version[VERSION_LENGTH];
    char back_fpga_version[VERSION_LENGTH];
    char front_6220_firmware_version[VERSION_LENGTH];
    char back_6220_firmware_version[VERSION_LENGTH];
}SCAN_FIRMWARE_VERSION_S;

/**
 * @brief This is the print statistics page data structure of receive
 */
typedef struct
{
    uint32_t    statical_type           ;    ///< 1:print 2:copy
    uint32_t    total_print_sum         ;    ///< total print sum
    uint32_t    color_print_sum         ;    ///< color print sum
    uint32_t    mono_print_sum          ;    ///< mono print sum
    uint32_t    a3_print_sum            ;    ///< a3 print sum
    uint32_t    a4_print_sum            ;    ///< a4 print sum
    uint32_t    a5_print_sum            ;    ///< a5 print sum
}PANEL_PRINT_STATISTIC_PAGE_INFGO;


/**
 * @brief save some status and flag
 */
typedef struct
{
    uint32_t system_sleep_status;
    uint32_t last_sleep_status;
    uint32_t print_tray_multi_remain;
}PANEL_STATUS_TABLE_S;

/**
 * @brief fb/adf cover status
*/
typedef struct
{
    uint32_t    fb_cover_status;    ///< 0:close, 1:open
    uint32_t    adf_cover_status;   ///< 0:close, 1:open
}SCAN_FB_ADF_COVER_STATUS_S;

/**
 * @brief local data in dc, use for data sync when deep wakeup
 */
typedef struct {
    uint32_t lcd_backlight_set;                         ///< lcd backlight
    uint32_t quiet;                                     ///< quiet mode
    uint32_t system_volume;                             ///< sys volume
    uint32_t sleep_time;                                ///< sleep time
    uint32_t sleep_mode;                                ///< sleep mode
    uint32_t system_language_code;                      ///< language code
    uint32_t date_format;                               ///< date format
    uint32_t time_format;                               ///< time format
    uint32_t usb_control;                               ///< usb control
    uint32_t net_control;                               ///< net control
    uint32_t net_connect_status;                       ///< if in sleep
    uint32_t usb_connect_status;
    char     firmware_version[VERSION_STR_LEN];         ///< firmware version
    char     ptd_name[PDT_NAME_LEN];
    char     host_name[PDT_NAME_LEN];
    NET_IPV4_CONF_S ipv4_config;
    NET_IPV6_CONF_S ipv6_config;
    PRINT_CONFIG_ENGINE_INFO_S print_machine_config;
    PRINT_CONFIG_CONSUMPTION_INFO_S print_consumption;
    PRINT_CONFIG_TRAY_INFO_S print_tray_config;
    PANEL_PRINT_STATISTIC_PAGE_INFGO print_statistic_info;
    PANEL_PRINT_STATISTIC_PAGE_INFGO copy_statistic_info;
    PRINT_CONFIG_INSTALL_INFO_S print_install_config;
    SCAN_PAPER_SIZE_S adf_fb_size;
    SCAN_FIRMWARE_VERSION_S scan_firmware_version;
    PANEL_PRINT_DEFAULT_CONFIG_S panel_print_default_config;
    PANEL_PRINT_IMAGE_CONFIG_S panel_print_image_config;
    PRINT_CONFIG_CONSUMPTION_PARAM_S print_consumption_param;

    uint32_t ambient_light;
    uint32_t job_error_process_mode;
    uint32_t job_error_delete_time;
    uint32_t config_color_print;                    ///< 0:black; 1:color
    char    lock_screen_password[16];
    uint32_t error_status_volume;
    uint32_t job_end_volume;

    uint32_t color_copy_enable;
    char color_copy_pwd[16];

    PANLE_SHORTCUT_COPY_PARAM_S panel_shorcut_copy_param_s;

    uint32_t hard_disk_insert_status;

    uint32_t lock_screen_time;
    uint32_t timeout_time;
    char udisk_mount_path[128];

    uint32_t lock_screen_switch;
    uint32_t copy_param_reset;

    uint32_t udisk_insert_status;

    PRINT_CONFIG_CUSTOM_SIZE_S print_multi_custom_size;

    uint32_t whiteList_switch;
    SCAN_FB_ADF_COVER_STATUS_S scan_fb_adf_cover_state;
    NET_SMTP_CONF_S smtp_config;

    uint32_t scan_job_control;
    uint32_t print_job_control;
    uint32_t copy_job_control;

    uint32_t copy_range_reset;
    PANEL_JOB_ADVANCE_IMAGE_S job_advance_image;
    uint32_t hard_disk_control;
    uint32_t hard_disk_encrypt;
    uint32_t adf_paper_in_volume;
    SCAN_PAPER_SIZE_NUM_S scan_paper_count;
    PRINT_CONFIG_CONSUMPTION_STATUS_S print_consumption_status;

    uint32_t net_init_done;
    uint32_t net_eth_speed;
    uint32_t log_save_switch;
} PANEL_CONFIG_TABLE_S;





/**
 * @brief get panel data from nvram and sned to panel
 * @author: madechang
 */
void dc_get_nvram_to_panel_u32( void );

/**
 * @brief set panel data from panel and sned to nvram
 * @author: madechang
 */
 int32_t panel_set_setting_to_nvram_u32( uint32_t* data, uint32_t data_len , uint16_t panel_dc_cmd);

/**
 * @brief set panel data from panel and sned to nvram
 * @author: madechang
 */
 int32_t panel_set_setting_to_nvram_str( char* data, uint32_t data_len , uint16_t panel_dc_cmd);

 /**
  * @brief set panel data from panel and sned to nvram
  * @author: madechang
  */
 int32_t panel_set_setting_to_nvram_struct( char* data, uint32_t data_len , uint16_t panel_dc_cmd);

/**
 * @brief set panel data from panel and sned to nvram
 * @author: madechang
 */
void dc_get_nvram_to_panel_str( void );


/**
 * @brief send uint8 data to panel
 * @param[in] head_type cmd type
 * @param[in] head_id cmd id
 * @param[in] head_msg_tpye cmd messege
 * @param[in] data data from panel or dc
 * @param[in] data_len data length
 * @return send result; 1 for sueccess; 0 for error
 * @author: madechang
 */
uint32_t panel_send_data_u8(uint8_t head_type, uint16_t head_id, uint8_t head_msg_tpye, void* data, uint32_t data_len);

/**
 * @brief dc send uint8 data to panel
 * @param[in] uint8_t head_type:cmd type
 * @param[in] uint16_t head_id:cmd id
 * @param[in] uint8_t head_msg_tpye:cmd messege
 * @author: madechang
 */
uint32_t panel_send_cmd( uint8_t head_type, uint16_t head_id, uint8_t head_msg_tpye );

/**
 * @brief get panel config
 * @return panel config
 * @author: madechang
 */
PANEL_CONFIG_TABLE_S* get_panel_config_table( void );

/**
 * @brief get panel status flag
 * @return print status
 * @author: madechang
 */
PANEL_STATUS_TABLE_S* get_panel_status_table( void );

/**
 * @brief get panel default print config
 * @return print config
 * @author: madechang
 */
PANEL_PRINT_DEFAULT_CONFIG_S* get_panel_print_config( void );

/**
 * @brief get print image config
 * @return image config
 * @author: madechang
 */
PANEL_PRINT_IMAGE_CONFIG_S* get_print_image_config();

void panel_default_param( void );
NET_IPV6_CONF_S* set_ipv6_config( uint32_t* ipv6_enable );

/**
 * @brief set usb control switch from panel
 * @param[in] usb control from panel
 * @param[in] data_len data lenght
 * @author: madechang
 */
void panel_set_usb_control( uint32_t* data, uint32_t data_len );

/**
 * @brief send all data to panel when wakeup from deep sleep
 * @author: madechang
 */
void panel_data_sync(void);

/**
 * @brief notify security modle that panel operate
 * @author: madechang
 */
void notify_security_operate( uint32_t operate_type, void* operate_value, uint32_t value_len );

void panel_set_water_mark( CUSTOM_WATER_MARK_S* water_mark );
void send_watermark_to_panel();


#endif /* _PANEL_CONFIG_H */

/**
 *@}
 */


