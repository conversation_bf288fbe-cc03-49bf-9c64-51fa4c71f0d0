/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_socket.h
 * @addtogroup socket
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal socket interface set
 */
#ifndef POL_SOCKET_H
#define POL_SOCKET_H

#if defined(Linux)

#include <sys/types.h>
#include <sys/socket.h>

typedef int PI_SOCKET_T;

#define INVALID_SOCKET  ((PI_SOCKET_T)-1)

#define pi_socket       socket
#define pi_closesock    close
#define pi_socketpair   socketpair

#define pi_setsockopt   setsockopt
#define pi_getsockopt   getsockopt

#define pi_bind         bind
#define pi_listen       listen
#define pi_accept       accept
#define pi_connect      connect

#define pi_recv         recv
#define pi_recvfrom     recvfrom
#define pi_recvmsg      recvmsg

#define pi_send         send
#define pi_sendto       sendto
#define pi_sendmsg      sendmsg

#endif /* defined(Linux) */

#endif /* POL_SOCKET_H */

/**
 *@}
 */
