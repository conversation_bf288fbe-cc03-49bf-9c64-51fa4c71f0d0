/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netacl.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-9-18
 * @brief network register ACL command
 */
#include "nettypes.h"
#include "netmisc.h"
#include "netctx.h"
#include "acl/acl.h"

#define TMPBUF_APPEND(fmt, ...)                                                         \
{                                                                                       \
    offset += snprintf(tmpbuf + offset, sizeof(tmpbuf) - offset, fmt, ##__VA_ARGS__);   \
    BREAK_IF(sizeof(tmpbuf) < offset, NET_WARN);                                        \
}

#define ACLCMD_GET_ETH_MAC_ADDR 0xFF07  ///< 获取有线网络MAC地址
#define ACLCMD_GET_NET_INFO     0xFF29  ///< 获取网络信息(IPv4 address/MAC address/raw port/IPv4 useDHCP/hostname)
#define ACLCMD_GET_NET_PROT	    0xFF30  ///< 获取网络协议信息
#define ACLCMD_GET_SNMP_INFO    0xFFA1  ///< 获取SNMP版本信息

#if CONFIG_NET_WIFI
#include "wifi.h"

#define ACLCMD_GET_STA_IPINFO   0xFF0F  ///< 获取WiFi-Station IP配置(IPv4 address/IPv4 mask/IPv6 address)
#define ACLCMD_SET_STA_CONFIG   0xFF80  ///< 配置WiFi-Station(ssid + sec_mode + psk)
#define ACLCMD_SET_WFD_CONFIG   0xFF90  ///< 配置WiFi-Direct(关闭/自动/手动)

#pragma pack(1)
typedef struct acl_sta_ipinfo
{
    ACL_RESPONSE_BASE_CLASS_MEMBERS;    ///< define the base items
    uint8_t ipv4_len;
    uint8_t ipv6_len;
    uint8_t temp[8];                    ///< has to be expanded to 16 bytes total, this does that
}
ACL_STA_IPINFO_S;

typedef struct acl_sta_config
{
    char    ssid[33];                   ///< SSID
    uint8_t sec_mode;                   ///< security mode
    char    psk[65];                    ///< WPA password
    uint8_t wep_mode;                   ///< WEP encryption mode: AUTH_OPEN=0, AUTH_SHARED_KEY=1, AUTH_AUTO=2
    char    key1[27];                   ///< WEP password part 1
    char    key2[27];                   ///< WEP password part 2
    char    key3[27];                   ///< WEP password part 3
    char    key4[27];                   ///< WEP password part 4
}
ACL_STA_CFG_S;

typedef struct acl_wfd_config
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint8_t  enable_flags;
    uint8_t  temp[11];
}
ACL_WFD_CFG_S;
#pragma pack()

#endif /* CONFIG_NET_WIFI */

/**
 * @brief       Parse ACLCMD_GET_ETH_MAC_ADDR to get MAC address of wired network.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_get_eth_mac_addr(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
    int32_t result;
    uint8_t mac[6];

    if ( net_ifctl_get_mac(IFACE_ETH, mac, sizeof(mac)) == 0 )
    {
        NET_DEBUG("response MAC(%02X:%02X:%02X:%02X:%02X:%02X)", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_SUCCESS, mac, sizeof(mac));
        result = PARSER_SUCCESS;
    }
    else
    {
        NET_WARN("get eth0 mac failed: %d<%s>", errno, strerror(errno));
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        result = PARSER_ERROR;
    }

    return result;
}

/**
 * @brief       Parse ACLCMD_GET_NET_INFO to get network information.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_get_net_info(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
    ACL_PARAMETER_OPERATE_S* operat = (ACL_PARAMETER_OPERATE_S *)cmd;
    NET_CTX_S*               netctx = (NET_CTX_S *)ctx;
    int32_t                  result = PARSER_ERROR;
    int32_t                  offset = 0;
    uint8_t                  rspbuf[71]; ///< 16 + 32 + 5 + 2 + 16
    char                     tmpbuf[71]; ///< 16 + 32 + 5 + 2 + 16
    char                     tmpstr[32];

    do
    {
        BREAK_IF(netctx == NULL || operat == NULL, NET_WARN);

        netdata_get_ipv4_addr(netctx->data_mgr, IFACE_ID_ETH, tmpstr, sizeof(tmpstr));
        if ( STRING_IS_EMPTY(tmpstr) )
        {
            TMPBUF_APPEND("%16s", "0.0.0.0");
        }
        else
        {
            TMPBUF_APPEND("%16s", tmpstr);
        }

        netdata_get_mac_addr(netctx->data_mgr, IFACE_ID_ETH, tmpstr, sizeof(tmpstr));
        if ( STRING_IS_EMPTY(tmpstr) )
        {
            NET_WARN("wired MAC address invalid!");
        }
        TMPBUF_APPEND("%32s", tmpstr);

        TMPBUF_APPEND("%5d", ((int32_t)netdata_get_rawprint_port(netctx->data_mgr)));
        TMPBUF_APPEND("%2d", ((int32_t)!!(netdata_get_ipv4_usedhcp(netctx->data_mgr, IFACE_ID_ETH))));

        netdata_get_hostname(netctx->data_mgr, tmpstr, sizeof(tmpstr));
        if ( STRING_IS_EMPTY(tmpstr) )
        {
            NET_WARN("hostname invalid!");
        }
        TMPBUF_APPEND("%.16s", tmpstr);

        encrypt_data_rc4((uint8_t *)tmpbuf, rspbuf, sizeof(rspbuf));
        result = PARSER_SUCCESS;
    }
    while ( 0 );

    if ( result == PARSER_SUCCESS )
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_SUCCESS, rspbuf, sizeof(rspbuf));
    }
    else
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
    }
    return result;
}

/**
 * @brief       Parse ACLCMD_GET_NET_PROT to get these protocol of wired network supported.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_get_net_protocol(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
    ACL_PARAMETER_OPERATE_S* operat = (ACL_PARAMETER_OPERATE_S *)cmd;
    NET_CTX_S*               netctx = (NET_CTX_S *)ctx;
    int32_t                  result = PARSER_ERROR;
    int32_t                  offset = 0;
    uint8_t                  rspbuf[256];
    char                     tmpbuf[256];

    memset(rspbuf, 0, sizeof(rspbuf));
    memset(tmpbuf, 0, sizeof(tmpbuf));
    do
    {
        BREAK_IF(netctx == NULL || operat == NULL, NET_WARN);

        TMPBUF_APPEND("SNMP,LLMNR,NetBIOS,SSL/TLS,HTTPS,MDNS");

        if ( netdata_get_slp_switch(netctx->data_mgr) )
        {
            TMPBUF_APPEND(",SLP");
        }
#if !CONFIG_NET_SAFETY_MACHINE
        if ( netdata_get_bonjour_switch(netctx->data_mgr) )
        {
            TMPBUF_APPEND(",Bonjour");
        }
#endif
        if ( netdata_get_rawprint_switch(netctx->data_mgr) )
        {
            TMPBUF_APPEND(",RAW");
        }

        if ( netdata_get_ipv4_usedhcp(netctx->data_mgr, IFACE_ID_ETH) )
        {
            TMPBUF_APPEND(",DHCP");
        }

        if ( netdata_get_ipv4_autodns(netctx->data_mgr, IFACE_ID_ETH) )
        {
            TMPBUF_APPEND(",DNS");
        }

        NET_DEBUG("tmpbuf(%s) (%d)", tmpbuf, strlen(tmpbuf));
        encrypt_data_rc4((uint8_t *)tmpbuf, rspbuf, strlen(tmpbuf));
        result = PARSER_SUCCESS;
    }
    while ( 0 );

    if ( result == PARSER_SUCCESS )
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_SUCCESS, rspbuf, strlen((char *)rspbuf));
    }
    else
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
    }
    return result;
}

/**
 * @brief       Parse ACLCMD_GET_SNMP_INFO to get information of the snmp protocol.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_get_snmp_info(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
#define SNMP_UNIT_LEN   32
    NET_CTX_S*  netctx = (NET_CTX_S *)ctx;
    int32_t     result = PARSER_ERROR;
    uint8_t     rspbuf[SNMP_UNIT_LEN*8];
    char        tmpbuf[SNMP_UNIT_LEN*8];
    char*       pstr;

    if ( netctx != NULL && cmd != NULL )
    {
        memset(rspbuf, 0, sizeof(rspbuf));
        memset(tmpbuf, 0, sizeof(tmpbuf));

        tmpbuf[0] = (uint8_t)netdata_get_snmp_version_flags(netctx->data_mgr);
        pstr = &tmpbuf[1];

        /* snmp v1团体名 */
        netdata_get_snmp_v1_community(netctx->data_mgr, pstr, SNMP_UNIT_LEN);
        pstr += SNMP_UNIT_LEN;

        /* snmp v2团体名 */
        netdata_get_snmp_v2_community(netctx->data_mgr, pstr, SNMP_UNIT_LEN);
        pstr += SNMP_UNIT_LEN;

        /* snmp v3团体名 */
        netdata_get_snmp_v3_community(netctx->data_mgr, pstr, SNMP_UNIT_LEN);
        pstr += SNMP_UNIT_LEN;

        /* snmpv3用户名 */
        netdata_get_snmp_v3_user_name(netctx->data_mgr, pstr, SNMP_UNIT_LEN);
        pstr += SNMP_UNIT_LEN;

        /* snmpv3授权密码 */
        netdata_get_snmp_v3_auth_pswd(netctx->data_mgr, pstr, SNMP_UNIT_LEN);
        pstr += SNMP_UNIT_LEN;

        /* snmpv3私钥密钥 */
        netdata_get_snmp_v3_priv_pswd(netctx->data_mgr, pstr, SNMP_UNIT_LEN);
        pstr += SNMP_UNIT_LEN;

        encrypt_data_rc4((uint8_t *)tmpbuf, rspbuf, sizeof(rspbuf));
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_SUCCESS, rspbuf, sizeof(rspbuf));
        result = PARSER_SUCCESS;
    }
    else
    {
        NET_DEBUG("cmd invalid, response(0x%04X)", ACLCMD_STATUS_FAILURE);
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        result = PARSER_ERROR;
    }

    return result;
}

#if CONFIG_NET_WIFI
/**
 * @brief       Parse ACLCMD_GET_STA_IPINFO to get IP address of WiFi-Station.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_get_sta_ipinfo(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
#define IP_INFO_LEN     (IPV4_ADDR_LEN * 3 + IPV6_ADDR_LEN * 3)
    ACL_STA_IPINFO_S*   netcmd = NULL;
    NET_CTX_S*          netctx = (NET_CTX_S *)ctx;
    int32_t             result = PARSER_ERROR;
    uint8_t             rspbuf[IP_INFO_LEN];
    char*               pstr;

    do
    {
        BREAK_IF(netctx == NULL || cmd == NULL, NET_WARN);

        memset(rspbuf, 0, sizeof(rspbuf));
        pstr = (char *)rspbuf;

        netdata_get_ipv4_addr(netctx->data_mgr, IFACE_ID_STA, pstr, IPV4_ADDR_LEN);
        NET_DEBUG("IPv4(%s)", pstr);
        pstr += IPV4_ADDR_LEN;

        netdata_get_ipv4_mask(netctx->data_mgr, IFACE_ID_STA, pstr, IPV4_ADDR_LEN);
        NET_DEBUG("mask(%s)", pstr);
        pstr += IPV4_ADDR_LEN;

        netdata_get_ipv4_gtwy(netctx->data_mgr, IFACE_ID_STA, pstr, IPV4_ADDR_LEN);
        NET_DEBUG("gateway(%s)", pstr);
        pstr += IPV4_ADDR_LEN;

        netdata_get_ipv6_link(netctx->data_mgr, IFACE_ID_STA, pstr, IPV6_ADDR_LEN);
        NET_DEBUG("IPv6 link(%s)", pstr);
        pstr += IPV6_ADDR_LEN;

        netdata_get_ipv6_stls(netctx->data_mgr, IFACE_ID_STA, pstr, IPV6_ADDR_LEN);
        NET_DEBUG("ipv6 stls(%s)", pstr);
        pstr += IPV6_ADDR_LEN;

        netdata_get_ipv6_site(netctx->data_mgr, IFACE_ID_STA, pstr, IPV6_ADDR_LEN);
        NET_DEBUG("IPv6 site(%s)", pstr);
        pstr += IPV6_ADDR_LEN;

        netcmd = (ACL_STA_IPINFO_S *)acl_construct_response_buffer(be16_to_cpu(cmd->cmd_id));
        BREAK_IF(netcmd == NULL, NET_WARN);

        netcmd->cmd_status = 1;
        netcmd->ipv4_len   = IPV4_ADDR_LEN * 3;
        netcmd->ipv6_len   = IPV6_ADDR_LEN * 3;
        acl_response(pgqio, (ACL_RESPONSE_BASE_STRUCT_S *)netcmd, rspbuf, sizeof(rspbuf));
        result = PARSER_SUCCESS;
        pi_free(netcmd);
    }
    while ( 0 );

    if ( result != PARSER_SUCCESS )
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
    }
    return result;
}

/**
 * @brief       Parse ACLCMD_SET_STA_CONFIG to set configuration of WiFi-Station.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      Parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_set_sta_config(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
    ACL_STA_CFG_S    stacfg;
    WIFI_CONN_CONF_S conn_conf = { .sec_mode = SEC_AUTO };
    NET_IPV4_CONF_S  ipconf = {.dhcp_enabled = 1, .changed = 0};
    NET_CTX_S*       netctx = (NET_CTX_S *)ctx;
    int32_t          result = PARSER_ERROR;
    int32_t          rcvlen;

    do
    {
        BREAK_IF(netctx == NULL, NET_WARN);

        memset(&stacfg, 0, sizeof(stacfg));
        rcvlen = parser_common_read(pgqio, (uint8_t *)&stacfg, sizeof(stacfg), PARSER_NET_TIMEOUT);
        BREAK_IF(rcvlen <= 0, NET_WARN);

        if ( rcvlen < sizeof(stacfg) )
        {
            NET_WARN("read not enough data, need %u bytes, but only have %d bytes!", sizeof(stacfg), rcvlen);
            break;
        }
        netctx_update_ipv4_config(netctx, IFACE_ID_STA, &ipconf); /* 启动DHCP */

        NET_DEBUG("wifi sec mode(%u)", stacfg.sec_mode);
        NET_DEBUG("wifi sta ssid(%s)", stacfg.ssid);
        NET_DEBUG("wifi wpa psk (%s)", stacfg.psk);

        snprintf(conn_conf.ssid, sizeof(conn_conf.ssid), "%s", stacfg.ssid);
        snprintf(conn_conf.psk,  sizeof(conn_conf.psk),  "%s", stacfg.psk);
        netctx_connect_wifi_station(netctx, &conn_conf, 2);
        if ( netdata_get_sta_status(netctx->data_mgr) == NETLINK_STATUS_CONNECTED )
        {
            result = ACLCMD_STATUS_SUCCESS;
        }
    }
    while ( 0 );

    NET_DEBUG("acl_direct_response status(%d)", result);
    if ( result == PARSER_SUCCESS )
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);
    }
    else
    {
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
    }
    return result;
}

/**
 * @brief       Parse ACLCMD_SET_WFD_CONFIG to set configuration of WiFi-Direct.
 * @param[in]   pqio            : The qio_general object pointer.
 * @param[in]   cmd             : The ACL_CMD_BASE_STRUCT_S pointer.
 * @param[in]   ctx             : The context of callback.
 * @return      parse result(ParserStatus_E)
 * @retval      PARSER_SUCCESS  : success\n
 *              PARSER_ERROR    : fail
 * <AUTHOR> Xin
 * @date        2023-9-18
 */
static int32_t netacl_set_wfd_config(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S* cmd, void* ctx)
{
    ACL_WFD_CFG_S*  wfdcfg = (ACL_WFD_CFG_S *)cmd;
    NET_CTX_S*      netctx = (NET_CTX_S *)ctx;
    int32_t         result;

    if ( netctx != NULL && wfdcfg != NULL )
    {
        netctx_update_iface_switch(netctx, IFACE_ID_WFD, (uint32_t)wfdcfg->enable_flags);
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);
        result = PARSER_SUCCESS;
    }
    else
    {
        NET_DEBUG("cmd invalid, response(%d)", ACLCMD_STATUS_FAILURE);
        acl_direct_response(pgqio, cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        result = PARSER_ERROR;
    }

    return result;
}
#endif /* CONFIG_NET_WIFI */

void netacl_init(NET_CTX_S* net_ctx)
{
    RETURN_IF(net_ctx == NULL, NET_WARN);

    if ( acl_register_cmd(ACLCMD_GET_ETH_MAC_ADDR, netacl_get_eth_mac_addr, net_ctx) != REGISTER_SUCCESS )
    {
        NET_WARN("register ACLCMD_GET_ETH_MAC_ADDR(0x%X) failed", ACLCMD_GET_ETH_MAC_ADDR);
    }

    if ( acl_register_cmd(ACLCMD_GET_NET_INFO, netacl_get_net_info, net_ctx) != REGISTER_SUCCESS )
    {
        NET_WARN("register ACLCMD_GET_NET_INFO(0x%X) failed", ACLCMD_GET_NET_INFO);
    }

    if ( acl_register_cmd(ACLCMD_GET_NET_PROT, netacl_get_net_protocol, net_ctx) != REGISTER_SUCCESS )
    {
        NET_WARN("register ACLCMD_GET_NET_PROT(0x%X) failed", ACLCMD_GET_NET_PROT);
    }

    if ( acl_register_cmd(ACLCMD_GET_SNMP_INFO, netacl_get_snmp_info, net_ctx) != REGISTER_SUCCESS )
    {
        NET_WARN("register aclcmd_get_snmp_info(0x%X) failed", ACLCMD_GET_SNMP_INFO);
    }

#if CONFIG_NET_WIFI
    if ( netdata_get_support_wifi(net_ctx->data_mgr) )
    {
        if ( acl_register_cmd(ACLCMD_GET_STA_IPINFO,  netacl_get_sta_ipinfo, net_ctx) != REGISTER_SUCCESS )
        {
            NET_WARN("register ACLCMD_GET_STA_IPINFO(0x%X) failed", ACLCMD_GET_STA_IPINFO);
        }

        if ( acl_register_cmd(ACLCMD_SET_STA_CONFIG, netacl_set_sta_config, net_ctx) != REGISTER_SUCCESS )
        {
            NET_WARN("register ACLCMD_SET_STA_CONFIG(0x%X) failed", ACLCMD_SET_STA_CONFIG);
        }

        if ( acl_register_cmd(ACLCMD_SET_WFD_CONFIG, netacl_set_wfd_config, net_ctx) != REGISTER_SUCCESS )
        {
            NET_WARN("register ACLCMD_SET_WFD_CONFIG(0x%X) failed", ACLCMD_SET_WFD_CONFIG);
        }
    }
    else
    {
        NET_DEBUG("Wireless is not supported");
    }
#endif /* CONFIG_NET_WIFI */
}
/**
 *@}
 */
