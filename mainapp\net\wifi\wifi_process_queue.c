/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi_process_queue.c
 * @addtogroup net
 * @{
 * @addtogroup wifi_process_queue
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WiFi process queue API
 */
#include "nettypes.h"
#include "netctx.h"

struct process_task
{
    struct list_head    list;
    int32_t             (*callback)(NET_CTX_S *, void *);
    void*               arg;
};

static struct list_head s_process_list = PI_LIST_HEAD_INIT(s_process_list);
static PI_SEMAPHORE_T   s_process_sem  = INVALIDSEM;
static PI_MUTEX_T       s_process_mtx  = INVALIDMTX;
static uint8_t          s_process_queue_inited = 0;

static struct process_task* process_task_create(int32_t (*callback)(NET_CTX_S *, void *), void* arg)
{
    struct process_task* node;

    node = (struct process_task *)pi_malloc(sizeof(struct process_task));
    if ( node != NULL )
    {
        node->callback = callback;
        node->arg = arg;
    }

    return node;
}

static void process_task_destroy(struct process_task* node)
{
    pi_list_del_entry(&(node->list));
    pi_free(node);
}

int32_t wifi_process_queue_init(void)
{
    RETURN_VAL_IF((s_process_mtx = pi_mutex_create()) == INVALIDMTX, NET_WARN, -1);
    RETURN_VAL_IF((s_process_sem = pi_sem_create(0))  == INVALIDSEM, NET_WARN, -1);

    pi_init_list_head(&s_process_list);
    s_process_queue_inited = 1;

    return 0;
}

void wifi_process_queue_deinit(void)
{
    s_process_queue_inited = 0;

    pi_sem_post(s_process_sem); /* activate the main looper */
}

int32_t wifi_process_queue_push(int32_t (*callback)(NET_CTX_S *, void *), void* arg)
{
    struct process_task* node;

    RETURN_VAL_IF(s_process_queue_inited == 0, NET_WARN, -1);

    node = process_task_create(callback, arg);
    RETURN_VAL_IF(node == NULL, NET_WARN, -1);

    pi_mutex_lock(s_process_mtx);
    pi_list_add_tail(&(node->list), &s_process_list);
    pi_mutex_unlock(s_process_mtx);

    pi_sem_post(s_process_sem); /* activate the main looper */

    return 0;
}

int32_t wifi_process_queue_run(NET_CTX_S* net_ctx)
{
    struct process_task*    node;
    struct list_head*       pos;
    struct list_head*       n;

    while ( 1 )
    {
        pi_sem_wait(s_process_sem);

        BREAK_IF(s_process_queue_inited == 0, NET_WARN);

        pi_mutex_lock(s_process_mtx);
        node = pi_list_first_entry_or_null(&s_process_list, struct process_task, list);
        pi_mutex_unlock(s_process_mtx);

        if ( node == NULL )
        {
            NET_WARN("process queue is empty and should not be awakened!!!");
            continue;
        }

        if ( node->callback != NULL )
        {
            node->callback(net_ctx, node->arg);
        }
        else
        {
            NET_WARN("callback is NULL");
        }

        pi_mutex_lock(s_process_mtx);
        process_task_destroy(node);
        pi_mutex_unlock(s_process_mtx);
    }

    pi_mutex_lock(s_process_mtx);
    pi_list_for_each_safe(pos, n, &s_process_list)
    {
        node = pi_list_entry(pos, struct process_task, list);
        if ( node != NULL )
        {
            process_task_destroy(node);
        }
    }
    pi_mutex_unlock(s_process_mtx);
    if ( s_process_mtx != INVALIDMTX )
    {
        pi_mutex_destroy(s_process_mtx);
    }
    if ( s_process_sem != INVALIDSEM )
    {
        pi_sem_destroy(s_process_sem);
    }
    NET_WARN("process queue end");

    return 0;
}
/**
 *@}
 */
