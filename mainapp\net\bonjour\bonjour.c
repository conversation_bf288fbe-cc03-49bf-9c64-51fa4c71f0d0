/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file bonjour.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief bonjour base on mDNS
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netmisc.h"
#include "mDNSEmbeddedAPI.h"
#include "mDNSPosix.h"
#if CONFIG_NET_WHITELIST
#include "whitelist.h"
#endif

#define BONJOUR_MLOCK_UN()      { if (s_bnj_ctx != NULL) pi_mutex_unlock(s_bnj_ctx->srv_mtx); }
#define BONJOUR_MLOCK_EX()      { if (s_bnj_ctx != NULL) pi_mutex_lock(s_bnj_ctx->srv_mtx);   }

#define MDNS_SERV_NAME_HTTP     "_http._tcp"
#define MDNS_SERV_NAME_IPP      "_ipp._tcp"
#define MDNS_SERV_NAME_IPPS     "_ipps._tcp"
#define MDNS_SERV_NAME_PDL      "_pdl-datastream._tcp"
#define MDNS_SERV_NAME_USCAN    "_uscan._tcp"
#define MDNS_SERV_NAME_USCANS   "_uscans._tcp"
#define MDNS_SERV_NAME_SCAN     "_scanner._tcp"
#define MDNS_SERV_DOMAIN        "local."
#define MDNS_SERV_TXT_LEN       256

enum
{
    COMM_TXT_IDX_TXTVERS = 0,
    COMM_TXT_IDX_QTOTAL,
    COMM_TXT_IDX_ADMINURL,
    COMM_TXT_IDX_KIND,
    COMM_TXT_IDX_NOTE,
    COMM_TXT_IDX_PDL,
    COMM_TXT_IDX_PRIORITY,
    COMM_TXT_IDX_PRODUCT,
    COMM_TXT_IDX_RP,
    COMM_TXT_IDX_TY,
    COMM_TXT_IDX_COLOR,
    COMM_TXT_IDX_DUPLEX,
    COMM_TXT_IDX_FAX,
    COMM_TXT_IDX_PAPERMAX,
    COMM_TXT_IDX_SCAN,
    COMM_TXT_IDX_TLS,
#if CONFIG_URF
    COMM_TXT_IDX_URF,
#endif
    COMM_TXT_IDX_UUID,
#if CONFIG_PWG
    COMM_TXT_IDX_MOPRIA_CERT,
#endif
    COMM_TXT_IDX_PRINT_WFDS,
    COMM_TXT_IDX_TRANSPARENT,
    COMM_TXT_IDX_USB_MDL,
    COMM_TXT_IDX_USB_MFG,
    COMM_TXT_IDX_SNMP_VER,
    COMM_TXT_IDX_SN,
    COMM_TXT_IDX_PID_PT,
#if CONFIG_NET_PCLOUD
    COMM_TXT_IDX_PID_WX,
    COMM_TXT_IDX_DEVICE_CAP,
    COMM_TXT_IDX_APPID_PATH,
#endif

    COMM_TXT_NUM
};

#if CONFIG_NET_ESCLSRV
enum
{
    ESCL_TXT_IDX_TXTVERS,       ///< version of txt record, must be 1
    ESCL_TXT_IDX_VERS,          ///< vers: eSCL version
    ESCL_TXT_IDX_CERT,          ///< mopria certified scan
    ESCL_TXT_IDX_RS,            ///< resource scanner
    ESCL_TXT_IDX_TY,
    ESCL_TXT_IDX_ADMINURL,
    ESCL_TXT_IDX_REPRESENT,     ///< URL of the PNG or ICO file containing the graphical representation of the scanner
    ESCL_TXT_IDX_NOTE,          ///< location
    ESCL_TXT_IDX_PDL,
    ESCL_TXT_IDX_USB_MFG,
    ESCL_TXT_IDX_UUID,
    ESCL_TXT_IDX_CS,            ///< the color space defines the color capabilities of the scanner
    ESCL_TXT_IDX_IS,            ///< the input source defines the list of scan input options
    ESCL_TXT_IDX_DUPLEX,

    ESCL_TXT_NUM
};
#endif

#if CONFIG_SCAN
enum
{
    SCAN_TXT_IDX_TXTVERS,
    SCAN_TXT_IDX_TY,
    SCAN_TXT_IDX_ADMINURL,
    SCAN_TXT_IDX_NOTE,
    SCAN_TXT_IDX_MFG,
    SCAN_TXT_IDX_MDL,
    SCAN_TXT_IDX_UUID,
    SCAN_TXT_IDX_BUTTON,
    SCAN_TXT_IDX_FLATBED,
    SCAN_TXT_IDX_FEEDER,

    SCAN_TXT_NUM
};
#endif

enum
{
    BONJOUR_RELOAD_INTERFACES   = 0x01 << 0,
    BONJOUR_RELOAD_SERVICES     = 0x01 << 1,
};

typedef struct bonjour_rdata
{
    mDNSu8              txt[1024];
    mDNSu16             len;
}
BONJOUR_RDATA_S;

typedef struct bonjour_service
{
    ServiceRecordSet    core_serv;
    BONJOUR_RDATA_S     txt_rdata;
    struct list_head    list;
}
BONJOUR_SERVICE_S;

typedef struct bonjour_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         srv_tid;
    PI_MUTEX_T          srv_mtx;
    char                hostname[HOSTNAME_LEN];
    char                comm_txt[COMM_TXT_NUM][MDNS_SERV_TXT_LEN];
#if CONFIG_NET_ESCLSRV
    char                escl_txt[ESCL_TXT_NUM][MDNS_SERV_TXT_LEN];
#endif
#if CONFIG_SCAN
    char                scan_txt[SCAN_TXT_NUM][MDNS_SERV_TXT_LEN];
#endif
    struct list_head    serv_head;
    uint8_t             reload;
}
BONJOUR_CTX_S;

static BONJOUR_CTX_S*   s_bnj_ctx = NULL;

const char              ProgramName[] = "bonjour";
mDNS                    mDNSStorage;
mDNS_PlatformSupport    platform_storage;

/**
 * @brief       Initialize the response txt array for the bonjour service
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_init_txt_array(void)
{
    char        service_name[128];
    char        hostname[HOSTNAME_LEN];
    char        location[256];
    char        mfg_name[16];
    char        pdt_name[32];
    char        pdt_sn[32];
    char        uuid[64];
    char        finishings_str[512];
    char        finishings_val[512];
    int32_t     finishings_arr[128];
    int32_t     ret;
    const char* pdl_info = "application/octet-stream"
#if CONFIG_JPEG
        ",image/jpeg"
#endif
#if CONFIG_URF
        ",image/urf"
#endif
#if CONFIG_PWG
        ",image/pwg-raster"
#endif
#if CONFIG_PDF
        ",application/pdf"
#endif
        ;

    netdata_get_bonjour_server(DATA_MGR_OF(s_bnj_ctx), service_name, sizeof(service_name));
    netdata_get_hostname(DATA_MGR_OF(s_bnj_ctx), hostname, sizeof(hostname));
    netdata_get_location(DATA_MGR_OF(s_bnj_ctx), location, sizeof(location));
    netdata_get_mfg_name(DATA_MGR_OF(s_bnj_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(DATA_MGR_OF(s_bnj_ctx), pdt_name, sizeof(pdt_name));
    netdata_get_pdt_sn  (DATA_MGR_OF(s_bnj_ctx), pdt_sn,   sizeof(pdt_sn));
    netdata_get_uuid    (DATA_MGR_OF(s_bnj_ctx), uuid,     sizeof(uuid));
    netdata_get_ipp_finisher_info(DATA_MGR_OF(s_bnj_ctx), finishings_arr, sizeof(finishings_arr));

    snprintf(s_bnj_ctx->hostname, sizeof(s_bnj_ctx->hostname), "%s", hostname);

    memset(finishings_val, 0, sizeof(finishings_val));
    ret = array_to_string(finishings_arr, sizeof(finishings_arr)/sizeof(int), finishings_str, sizeof(finishings_str));
    if ( ret > 0 )
    {
        snprintf(finishings_val, sizeof(finishings_val), ",FN%s", finishings_str);
    }
    NET_DEBUG("finishings_str(%s)", finishings_val);
    NET_DEBUG("service(%s) uuid(%s) pdl_info(%s)", service_name, uuid, pdl_info);

    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_TXTVERS],     MDNS_SERV_TXT_LEN, "txtvers=1"                                                                              );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_QTOTAL],      MDNS_SERV_TXT_LEN, "qtotal=1"                                                                               );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_ADMINURL],    MDNS_SERV_TXT_LEN, "adminurl=http://%s.local:%d/index.html#airprint-configure",          hostname, HTTP_PORT);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_KIND],        MDNS_SERV_TXT_LEN, "kind=document"                                                                 );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_NOTE],        MDNS_SERV_TXT_LEN, "note=%s",                                                                       location);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PDL],         MDNS_SERV_TXT_LEN, "pdl=%s",                                                                        pdl_info);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PRIORITY],    MDNS_SERV_TXT_LEN, "priority=25"                                                                            );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PRODUCT],     MDNS_SERV_TXT_LEN, "product=%s %s Series",                                                mfg_name, pdt_name);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_RP],          MDNS_SERV_TXT_LEN, "rp=ipp/print"                                                                           );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_TY],          MDNS_SERV_TXT_LEN, "ty=%s %s Series",                                                     mfg_name, pdt_name);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_COLOR],       MDNS_SERV_TXT_LEN, "Color=%s",               (netdata_get_support_color(DATA_MGR_OF(s_bnj_ctx)) ? "T" : "F"));
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_DUPLEX],      MDNS_SERV_TXT_LEN, "Duplex=%s",             (netdata_get_support_duplex(DATA_MGR_OF(s_bnj_ctx)) ? "T" : "F"));
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_FAX],         MDNS_SERV_TXT_LEN, "Fax=%s",                                                        (CONFIG_FAX ? "T" : "F"));
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PAPERMAX],    MDNS_SERV_TXT_LEN, "PaperMax=tabloid-A3"                                                                    );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_SCAN],        MDNS_SERV_TXT_LEN, "Scan=%s",                                                      (CONFIG_SCAN ? "T" : "F"));
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_TLS],         MDNS_SERV_TXT_LEN, "TLS=1.2"                                                                                );
#if CONFIG_URF
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_URF],         MDNS_SERV_TXT_LEN, "URF=V1.5,W8,IS1,CP99,PQ4,OB10,RS600,DM1%s%s",(netdata_get_support_color(DATA_MGR_OF(s_bnj_ctx)) ? ",SRGB24" : ""), finishings_str);
#endif
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_UUID],        MDNS_SERV_TXT_LEN, "UUID=%s",                                                                           uuid);
#if CONFIG_PWG
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_MOPRIA_CERT], MDNS_SERV_TXT_LEN, "mopria-certified=2.2"                                                                   );
#endif
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PRINT_WFDS],  MDNS_SERV_TXT_LEN, "print_wfds=T"                                                                           );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_TRANSPARENT], MDNS_SERV_TXT_LEN, "Transparent=T"                                                                          );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_USB_MDL],     MDNS_SERV_TXT_LEN, "usb_MDL=%s Series",                                                             pdt_name);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_USB_MFG],     MDNS_SERV_TXT_LEN, "usb_MFG=%s",                                                                    mfg_name);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_SNMP_VER],    MDNS_SERV_TXT_LEN, "snmp_ver=%u",            (uint8_t)netdata_get_snmp_version_flags(DATA_MGR_OF(s_bnj_ctx)));
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_SN],          MDNS_SERV_TXT_LEN, "sn=%s",                                                                           pdt_sn);
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PID_PT],      MDNS_SERV_TXT_LEN, "pidpt=0x%04x",                                netdata_get_pdt_id(DATA_MGR_OF(s_bnj_ctx)));
#if CONFIG_NET_PCLOUD
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_PID_WX],      MDNS_SERV_TXT_LEN, "pidwx=0x15B4"                                                                           );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_DEVICE_CAP],  MDNS_SERV_TXT_LEN, "deviceCap=0x0011"                                                                       );
    snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_APPID_PATH],  MDNS_SERV_TXT_LEN, "appid_path=gh_7e05f03f79e7/PREVIEW"                                                     );
#endif

#if CONFIG_NET_ESCLSRV
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_TXTVERS],     MDNS_SERV_TXT_LEN, "txtvers=1"                                                                              );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_VERS],        MDNS_SERV_TXT_LEN, "vers=2.63"                                                                              );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_CERT],        MDNS_SERV_TXT_LEN, "mopria-certified-scan=1.5"                                                              );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_RS],          MDNS_SERV_TXT_LEN, "rs=eSCL"                                                                                );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_TY],          MDNS_SERV_TXT_LEN, "ty=%s %s Series",                                                     mfg_name, pdt_name);
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_ADMINURL],    MDNS_SERV_TXT_LEN, "adminurl=http://%s.local:%d/index.html",                         service_name, HTTP_PORT);
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_REPRESENT],   MDNS_SERV_TXT_LEN, "representation=http://%s.local:%d/img/%s128.png",      service_name, HTTP_PORT, pdt_name);
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_NOTE],        MDNS_SERV_TXT_LEN, "note=%s",                                                                       location);
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_PDL],         MDNS_SERV_TXT_LEN, "pdl=application/pdf,image/jpeg"                                                         );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_CS],          MDNS_SERV_TXT_LEN, "cs=color,grayscale"                                                                     );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_IS],          MDNS_SERV_TXT_LEN, "is=%s",                                      (CONFIG_SCAN_ADF ? "platen,adf" : "platen"));
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_DUPLEX],      MDNS_SERV_TXT_LEN, "duplex=T"                                                                               );
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_USB_MFG],     MDNS_SERV_TXT_LEN, "usb_MFG=%s",                                                                    mfg_name);
    snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_UUID],        MDNS_SERV_TXT_LEN, "uuid=%s",                                                                           uuid);
#endif

#if CONFIG_SCAN
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_TXTVERS],     MDNS_SERV_TXT_LEN, "txtvers=1"                                                                              );
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_TY],          MDNS_SERV_TXT_LEN, "ty=%s %s Series",                                                     mfg_name, pdt_name);
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_ADMINURL],    MDNS_SERV_TXT_LEN, "adminurl=http://%s.local:%d/index.html",                         service_name, HTTP_PORT);
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_NOTE],        MDNS_SERV_TXT_LEN, "note=%s",                                                                       location);
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_BUTTON],      MDNS_SERV_TXT_LEN, "button=T"                                                                               );
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_FLATBED],     MDNS_SERV_TXT_LEN, "flatbed=T"                                                                              );
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_FEEDER],      MDNS_SERV_TXT_LEN, "feeder=T"                                                                               );
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_MFG],         MDNS_SERV_TXT_LEN, "mfg=%s",                                                                        mfg_name);
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_MDL],         MDNS_SERV_TXT_LEN, "mdl=%s Series",                                                                 pdt_name);
    snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_UUID],        MDNS_SERV_TXT_LEN, "UUID=%s",                                                                           uuid);
#endif
}

/**
 * @brief       Packet the record txt array to bonjour service response information.
 * @param[in]   txt_array   : The response txt array.
 * @param[in]   array_count : The count of array member.
 * @param[out]  buf         : packet response to this buffer.
 * @param[out]  buf_len     : buffer length.
 * @param[in]   buf_size    : buffer size.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_packing_txt_rdata(char (*txt_array)[MDNS_SERV_TXT_LEN], size_t array_count, mDNSu8 buf[], mDNSu16* buf_len, size_t buf_size)
{
    mDNSu16 txt_len = 0;
    size_t  tmp_len;
    size_t  i;

    memset(buf, 0, buf_size);
    for ( i = 0; i < array_count; ++i )
    {
        tmp_len = strlen(txt_array[i]);
        if ( txt_len + tmp_len + 1 > buf_size )
        {
            NET_WARN("Warning: buffer not enough, array[%u][%u](%s) buf_size(%u) need(%u)", i, tmp_len, txt_array[i], buf_size, txt_len + tmp_len + 1);
            break;
        }
        buf[txt_len++] = (mDNSu8)tmp_len;
        memcpy(buf + txt_len, txt_array[i], tmp_len);
        txt_len += (mDNSu16)tmp_len;
    }
    *buf_len = txt_len;
}

/**
 * @brief       Bonjour_registration_callback.
 * @param[in]   m           : mDNS object pointer.
 * @param[in]   registration: registration.
 * @param[in]   status      : status.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_registration_callback(mDNS* const m, ServiceRecordSet* const registration, mStatus status)
{
    BONJOUR_SERVICE_S*  this_serv;

    RETURN_IF(registration == NULL, NET_WARN);

    switch ( status )
    {
    case mStatus_NoError:
        NET_TRACE("service(%s) mStatus_NoError", registration->RR_SRV.resrec.name->c);
        break;

    case mStatus_NameConflict:
        NET_DEBUG("service(%s) mStatus_NameConflict", registration->RR_SRV.resrec.name->c);
        status = mDNS_RenameAndReregisterService(m, registration, mDNSNULL);
        break;

    case mStatus_MemFree:
        NET_TRACE("service(%s) mStatus_MemFree", registration->RR_SRV.resrec.name->c);
        this_serv = container_of(registration, BONJOUR_SERVICE_S, core_serv);
        NET_TRACE("registration(%p) this_serv(%p)", registration, this_serv);
#if CONFIG_URF
        if ( registration->SubTypes )
        {
            free(registration->SubTypes);
        }
#endif
        free(this_serv);
        break;

    default:
        NET_TRACE("service(%s) mStatus(%d)", registration->RR_SRV.resrec.name->c, status);
        break;
    }
}

/**
 * @brief       Register ont bonjour service.
 * @param[in]   serv_name   : The register service name.
 * @param[in]   serv_type   : The register service type.
 * @param[in]   serv_domain : The register service domain.
 * @param[in]   port        : The register service port.
 * @param[in]   txt_array   : The response txt array.
 * @param[in]   array_count : The count of array member.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_register_one_service(const char* serv_name, const char* serv_type, const char* serv_domain, mDNSu16 port, char (*txt_array)[MDNS_SERV_TXT_LEN], size_t array_count)
{
    BONJOUR_SERVICE_S*  this_serv = NULL;
    AuthRecord*         sub_types = NULL;
    mStatus             status = mStatus_NoMemoryErr;
    domainname          type, domain;
    domainlabel         name;

    do
    {
        this_serv = (BONJOUR_SERVICE_S *)pi_zalloc(sizeof(BONJOUR_SERVICE_S));
        if ( this_serv == NULL )
        {
            NET_WARN("alloc BONJOUR_SERVICE_S for (%s) failed: %d<%s>", serv_type, errno, strerror(errno));
            break;
        }

        bonjour_packing_txt_rdata(txt_array, array_count, this_serv->txt_rdata.txt, &this_serv->txt_rdata.len, sizeof(this_serv->txt_rdata.txt));
        NET_DEBUG("n(%s) t(%s) d(%s) len(%u)", serv_name, serv_type, serv_domain, this_serv->txt_rdata.len);

#if CONFIG_AIRPRINT
        sub_types = (AuthRecord *)pi_zalloc(sizeof(AuthRecord));
        if ( sub_types == NULL )
        {
            NET_WARN("alloc AuthRecord for (%s) failed: %d<%s>", serv_type, errno, strerror(errno));
            break;
        }

        mDNS_SetupResourceRecord(sub_types, mDNSNULL, mDNSInterface_Any, kDNSType_PTR, 2*3600, kDNSRecordTypeShared, AuthRecordAny, NULL, NULL);
        if ( netdata_get_ipp_switch(DATA_MGR_OF(s_bnj_ctx)) == 1 )
        {
            if ( strcmp(serv_type, "_ipp._tcp") == 0 )
            {
                MakeDomainNameFromDNSNameString((domainname *)sub_types->resrec.name, "_universal._sub._ipp._tcp");
            }
            else
            {
                MakeDomainNameFromDNSNameString((domainname *)sub_types->resrec.name, "_universal._sub._ipps._tcp");
            }
        }
#endif

        memset(&name, 0, sizeof(name));
        MakeDomainLabelFromLiteralString(&name, serv_name);
        memset(&type, 0, sizeof(type));
        MakeDomainNameFromDNSNameString(&type, serv_type);
        memset(&domain, 0, sizeof(domain));
        MakeDomainNameFromDNSNameString(&domain, serv_domain);

        status = mDNS_RegisterService(
                &mDNSStorage, &this_serv->core_serv,
                &name, &type, &domain, NULL, mDNSOpaque16fromIntVal(port),
                this_serv->txt_rdata.txt, this_serv->txt_rdata.len,
#if CONFIG_AIRPRINT
                sub_types, 1,
#else
                NULL, 0,
#endif
                mDNSInterface_Any, bonjour_registration_callback,
                this_serv, 0);
    }
    while ( 0 );

    if ( status == mStatus_NoError )
    {
        pi_list_add_head(&this_serv->list, &s_bnj_ctx->serv_head);
        NET_TRACE("register service(%s) successfully", serv_type);
    }
    else
    {
        NET_WARN("register service(%s) failed(%d)\n", serv_type, status);
        if ( sub_types != NULL )
        {
            pi_free(sub_types);
        }
        if ( this_serv != NULL )
        {
            pi_free(this_serv);
        }
    }
}

/**
 * @brief       Register all bonjour services.
 * @param[in]   serv_name   : The register service name.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_register_services(const char* serv_name)
{
    uint32_t debug = netdata_get_mdns_debug(DATA_MGR_OF(s_bnj_ctx));
    uint16_t port;

    bonjour_register_one_service(serv_name, MDNS_SERV_NAME_HTTP,        MDNS_SERV_DOMAIN, HTTP_PORT, s_bnj_ctx->comm_txt, COMM_TXT_NUM);
#if CONFIG_NET_IPPSRV
    bonjour_register_one_service(serv_name, MDNS_SERV_NAME_IPP,         MDNS_SERV_DOMAIN, IPP_PORT,  s_bnj_ctx->comm_txt, COMM_TXT_NUM);
    if ( !debug ) /* 开启debug模式时，不注册TLS端口，便于分析抓包数据 */
    {
        bonjour_register_one_service(serv_name, MDNS_SERV_NAME_IPPS,    MDNS_SERV_DOMAIN, TLS_PORT,  s_bnj_ctx->comm_txt, COMM_TXT_NUM);
    }
#else
    port = netdata_get_rawprint_port(DATA_MGR_OF(s_bnj_ctx));
    if ( port > 0 )
    {
        bonjour_register_one_service(serv_name, MDNS_SERV_NAME_PDL,     MDNS_SERV_DOMAIN, port,      s_bnj_ctx->comm_txt, COMM_TXT_NUM);
    }
#endif

#if CONFIG_NET_ESCLSRV
    bonjour_register_one_service(serv_name, MDNS_SERV_NAME_USCAN,       MDNS_SERV_DOMAIN, IPP_PORT,  s_bnj_ctx->escl_txt, ESCL_TXT_NUM);
    if ( !debug ) /* 开启debug模式时，不注册TLS端口，便于分析抓包数据 */
    {
        bonjour_register_one_service(serv_name, MDNS_SERV_NAME_USCANS,  MDNS_SERV_DOMAIN, TLS_PORT,  s_bnj_ctx->escl_txt, ESCL_TXT_NUM);
    }
#endif

#if CONFIG_SCAN
    port = netdata_get_rawscan_port(DATA_MGR_OF(s_bnj_ctx));
    if ( port > 0 )
    {
        bonjour_register_one_service(serv_name, MDNS_SERV_NAME_SCAN,    MDNS_SERV_DOMAIN, port,      s_bnj_ctx->scan_txt, SCAN_TXT_NUM);
    }
#endif
}

/**
 * @brief       Deregister all bonjour services.
 * @param[in]   serv_name   : The register service name.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_deregister_services(void)
{
    BONJOUR_SERVICE_S*  this_serv;
    struct list_head*   pos;
    struct list_head*   n;

    pi_list_for_each_safe(pos, n, &s_bnj_ctx->serv_head)
    {
        this_serv = pi_list_entry(pos, BONJOUR_SERVICE_S, list);
        pi_list_del_entry(&this_serv->list);
        mDNS_DeregisterService(&mDNSStorage, &this_serv->core_serv);
    }
}

/**
 * @brief       Reload all bonjour services.
 * @param[in]   enabled     : bonjour enabled/disabled switch.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_reload_all_services(uint32_t enabled, uint32_t first_flag)
{
    char                    hostname[HOSTNAME_LEN];
    char                    location[256];
    char                    bnj_serv[128];
    char                    pdt_name[64];
    char                    finishings_str[512];
    char                    finishings_val[512];
    int32_t                 finishings_arr[128];
    int                     ret;
    mStatus                 status = mStatus_NoError;

    bonjour_deregister_services();
    mDNS_Close(&mDNSStorage);

    if ( first_flag == 0 )
    {
        NET_DEBUG("reload sleep ");
        pi_sleep(2);
    }
    if ( enabled )
    {
        netdata_get_bonjour_server(DATA_MGR_OF(s_bnj_ctx), bnj_serv, sizeof(bnj_serv));
        netdata_get_location(DATA_MGR_OF(s_bnj_ctx), location, sizeof(location));
        netdata_get_hostname(DATA_MGR_OF(s_bnj_ctx), hostname, sizeof(hostname));
        netdata_get_pdt_name(DATA_MGR_OF(s_bnj_ctx), pdt_name, sizeof(pdt_name));
        netdata_get_ipp_finisher_info(DATA_MGR_OF(s_bnj_ctx), finishings_arr, sizeof(finishings_arr));
        memset(finishings_val, 0, sizeof(finishings_val));
        ret = array_to_string(finishings_arr, sizeof(finishings_arr)/sizeof(int), finishings_str, sizeof(finishings_str));
        if ( ret > 0 )
        {
            snprintf(finishings_val, sizeof(finishings_val), ",FN%s", finishings_str);
        }
        NET_DEBUG("bonjour finishings_val(%s)", finishings_val);

        snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_SNMP_VER],  MDNS_SERV_TXT_LEN, "snmp_ver=%u", (uint8_t)netdata_get_snmp_version_flags(DATA_MGR_OF(s_bnj_ctx)));
        snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_ADMINURL],  MDNS_SERV_TXT_LEN, "adminurl=http://%s.local:%d/index.html#airprint-configure", hostname, HTTP_PORT);
        snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_NOTE],      MDNS_SERV_TXT_LEN, "note=%s",                                         location);
#if CONFIG_URF
        snprintf(s_bnj_ctx->comm_txt[COMM_TXT_IDX_URF],       MDNS_SERV_TXT_LEN, "URF=V1.5,W8,IS1,CP99,PQ4,OB10,RS600,DM1%s%s",(netdata_get_support_color(DATA_MGR_OF(s_bnj_ctx)) ? ",SRGB24" : ""), finishings_val);
#endif
#if CONFIG_NET_ESCLSRV
        snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_REPRESENT], MDNS_SERV_TXT_LEN, "representation=http://%s.local:%d/img/%s128.png", bnj_serv, HTTP_PORT, pdt_name);
        snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_ADMINURL],  MDNS_SERV_TXT_LEN, "adminurl=http://%s.local:%d/index.html",          bnj_serv, HTTP_PORT);
        snprintf(s_bnj_ctx->escl_txt[ESCL_TXT_IDX_NOTE],      MDNS_SERV_TXT_LEN, "note=%s",                                         location);
#endif

#if CONFIG_SCAN
        snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_ADMINURL],  MDNS_SERV_TXT_LEN, "adminurl=http://%s.local:%d/index.html",          bnj_serv, HTTP_PORT);
        snprintf(s_bnj_ctx->scan_txt[SCAN_TXT_IDX_NOTE],      MDNS_SERV_TXT_LEN, "note=%s",                                         location);
#endif
        if(STRING_NO_EMPTY(hostname) && pi_strcmp(hostname, s_bnj_ctx->hostname) != 0)
        {
            status = mDNS_Init(&mDNSStorage, &platform_storage, NULL, 0, 1, NULL, NULL, &whitelist_check_ipv4_is_exist);
            mDNSPlatformPosixRefreshInterfaceList(&mDNSStorage);
            NET_DEBUG("mDNS_Init OK(%d)hostname(%s)s_bnj_ctx->hostname(%s)", status, hostname, s_bnj_ctx->hostname);
        }
        else
        {
            NET_WARN("mDNS_Init failed(%d)", status);
        }

        if ( status != mStatus_NoError )
        {
            NET_WARN("mDNS_Init failed(%d)", status);
        }
        else
        {
            snprintf(s_bnj_ctx->hostname, sizeof(s_bnj_ctx->hostname), "%s", hostname);
            bonjour_register_services(bnj_serv);
            NET_DEBUG("bonjour reload done.");
        }
        pi_sleep(1);
    }
    else
    {
        NET_DEBUG("bonjour disabled.");
        pi_sleep(3);
    }
}

/**
 * @brief       Update the reload flags.
 * @param[in]   reload      : BONJOUR_RELOAD_SERVICES or BONJOUR_RELOAD_INTERFACES.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_update_reload_flags(uint8_t reload)
{
    RETURN_IF(s_bnj_ctx == NULL, NET_WARN);

    BONJOUR_MLOCK_EX();
    NET_DEBUG("reload(%x | %x)", s_bnj_ctx->reload, reload);
    s_bnj_ctx->reload |= reload;
    BONJOUR_MLOCK_UN();
}

/**
 * @brief       The bonjour handling thread.
 * @param[in]   arg         : context(NULL).
 * @return      thread handling result
 * @retval      NULL        : success.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void* bonjour_thread_handler(void* arg)
{
    mStatus                 status;
    fd_set                  rfds;
    struct timeval          timeout;
    uint32_t                last_enabled = 0;
    uint32_t                enabled;
    uint8_t                 reload;
    int32_t                 maxfd;
    int32_t                 ret;
    uint32_t                first_flag = 1;

#if CONFIG_NET_WHITELIST
    status = mDNS_Init(&mDNSStorage, &platform_storage, NULL, 0, 1, NULL, NULL, &whitelist_check_ipv4_is_exist);
#else
    status = mDNS_Init(&mDNSStorage, &platform_storage, NULL, 0, 1, NULL, NULL, NULL);
#endif
    if ( status == mStatus_NoError )
    {
        while ( 1 )
        {
            BONJOUR_MLOCK_EX();
            reload = s_bnj_ctx->reload;
            s_bnj_ctx->reload = 0;
            BONJOUR_MLOCK_UN();

            enabled = netdata_get_bonjour_switch(DATA_MGR_OF(s_bnj_ctx));
            if ( reload )
            {
                mDNSPlatformPosixRefreshInterfaceList(&mDNSStorage);
                /* 重载标记激活 && 上一个和当前的enabled状态不同时为0 */
                if ( (reload & BONJOUR_RELOAD_SERVICES) && (last_enabled != 0 || enabled != 0) )
                {
                    bonjour_reload_all_services(enabled, first_flag);
                    first_flag = 0;
                }
            }
            last_enabled = enabled;

            if ( enabled )
            {
                timeout.tv_sec  = 2;
                timeout.tv_usec = 0;
                maxfd = 0;
                FD_ZERO(&rfds);
                mDNSPosixGetFDSet(&mDNSStorage, &maxfd, &rfds, &timeout);

                ret = select(maxfd, &rfds, NULL, NULL, &timeout);
                if ( ret < 0 )
                {
                    NET_WARN("select failed: %d<%s>, reload bonjour services", errno, strerror(errno));
                    bonjour_update_reload_flags(BONJOUR_RELOAD_INTERFACES);
                }
                else if ( ret > 0 )
                {
					NET_TRACE("ret > 0 (%d)", ret);
                    mDNSPosixProcessFDSet(&mDNSStorage, &rfds);
                }
				else if ( ret == 0 )
                {
                    NET_TRACE("ret == 0");
                }
            }
            else
            {
                pi_sleep(1); /* 避免bonjour禁用时线程空转 */
            }
        }

        bonjour_deregister_services();
        mDNS_Close(&mDNSStorage);
    }
    else
    {
        NET_WARN("mDNS_Init failed(%d), bonjour thread destroy!", status);
    }

    pi_thread_destroy_self();
    NET_WARN("exit!");
    return NULL;
}

/**
 * @brief       The callback function when the netlink subject notify.
 * @param[in]   o           : observer object pointer.
 * @param[in]   s           : subject  object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_update_link_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    bonjour_update_reload_flags(BONJOUR_RELOAD_INTERFACES);
}

/**
 * @brief       The callback function when the netport subject notify.
 * @param[in]   o           : observer object pointer.
 * @param[in]   s           : subject  object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-20
 */
static void bonjour_update_port_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_BONJOUR )
    {
        bonjour_update_reload_flags(BONJOUR_RELOAD_SERVICES);
    }
}

int32_t bonjour_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_bnj_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_bnj_ctx = (BONJOUR_CTX_S *)pi_zalloc(sizeof(BONJOUR_CTX_S));
    RETURN_VAL_IF(s_bnj_ctx == NULL, NET_WARN, -1);

    do
    {
        pi_init_list_head(&s_bnj_ctx->serv_head);
        s_bnj_ctx->reload  = BONJOUR_RELOAD_SERVICES;
        s_bnj_ctx->net_ctx = net_ctx;
        bonjour_init_txt_array();

        BREAK_IF((s_bnj_ctx->srv_mtx = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        s_bnj_ctx->srv_tid = pi_thread_create(bonjour_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "bonjour_thread_handler");
        BREAK_IF(s_bnj_ctx->srv_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("Bonjour initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netlink_observer(net_ctx, bonjour_update_link_callback, NULL);
        netctx_add_netport_observer(net_ctx, bonjour_update_port_callback, NULL);
    }
    else
    {
        bonjour_epilog();
    }
    return ret;
}

void bonjour_epilog(void)
{
    if ( s_bnj_ctx != NULL )
    {
        if ( s_bnj_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_bnj_ctx->srv_tid);
        }
        if ( s_bnj_ctx->srv_mtx != INVALIDMTX )
        {
            pi_mutex_destroy(s_bnj_ctx->srv_mtx);
        }
        pi_free(s_bnj_ctx);
        s_bnj_ctx = NULL;
    }
}
/**
 *@}
 */
