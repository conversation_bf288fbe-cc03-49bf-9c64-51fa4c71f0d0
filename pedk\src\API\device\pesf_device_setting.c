#include "pesf_device_setting.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define countof(x) (sizeof(x) / sizeof((x)[0]) )
#define Log(format, ...) printf("[setting] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

typedef struct
{
    int funcion;
    int switch_flag;
}SET_FUNC_SWITCH_S;

// 证书安装结构体
typedef struct {
    char url[256];
    char headers[2048];
    char file_name_prefix[256];
    char custom_field[512];
} ScanHttpParam;

JSValue js_setting_setFunctionSwitch(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = -1;
    SET_FUNC_SWITCH_S set_func_switch = {0};

    if(!JS_IsNumber(argv[0]) || !JS_IsNumber(argv[1]))
    {
        Log( "js_setting_setFunctionSwitch param ERROR!!!\n" );
       return -1;
    }

    JS_ToInt32(ctx, &set_func_switch.funcion, argv[0]);
    JS_ToInt32(ctx, &set_func_switch.switch_flag, argv[1]);

    ret = SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_SETFUNCTIONSWITCH, 0, sizeof(SET_FUNC_SWITCH_S), &set_func_switch);
    if( ret = 0 )
    {
        Log( "js_setting_setFunctionSwitch funcion:%d,switch_flag:%d\n", set_func_switch.funcion, set_func_switch.switch_flag );
    }
    else
    {
        Log( "js_setting_setFunctionSwitch ERROR!!!\n" );
    }


    return JS_NewInt32(ctx, ret);

}

#if 0
JSValue js_setting_getTrayPaperSize(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
    char * func_str;
    char PaperSize[32] = {0};
    int len = 0;

    func_str = JS_ToCString(ctx, argv[0]);
    if(NULL == func_str)
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,func_str = %s\n",__LINE__,__func__,func_str);

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_SIZE, 0,strlen(func_str)+1,func_str);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }

    len = sizeof(PaperSize);
    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_SIZE, &value,PaperSize,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,PaperSize = %s\n",__LINE__,__func__,PaperSize);

    JS_FreeCString(ctx, func_str);

    return JS_NewString(ctx, PaperSize);

}

JSValue js_setting_getTrayPaperType(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
    char * func_str;
    char PaperType[32] = {0};
    int len = 0;

    func_str = JS_ToCString(ctx, argv[0]);
    if(NULL == func_str)
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,func_str = %s\n",__LINE__,__func__,func_str);

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_TYPE, 0,strlen(func_str)+1,func_str);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }

    len = sizeof(PaperType);
    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_TYPE, &value,PaperType,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,PaperType = %s\n",__LINE__,__func__,PaperType);
    JS_FreeCString(ctx, func_str);

    return JS_NewString(ctx, PaperType);
}
#endif

JSValue js_setting_getPowerBoxModel(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char * func_str;
    char model[32] = {0};

    func_str = JS_ToCString(ctx, argv[0]);
    if(NULL == func_str)
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    printf("func_str = %s \n",func_str);
    printf("[line:%d] %s,%s\n",__LINE__,__func__,func_str);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_POWER_BOX_MODEL, 0,strlen(func_str)+1,func_str);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    len = sizeof(model);
    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_POWER_BOX_MODEL, &respone,model,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,model = %s\n",__LINE__,__func__,model);

    JS_FreeCString(ctx, func_str);

    return JS_NewString(ctx, model);
}

JSValue js_set_scan_http_params(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    if (argc < 1)
    {
         printf("Error: No arguments passed.\n");
         return JS_NewString(ctx, "EINVALIDPARAM");
    }

    const char *param_str = JS_ToCString(ctx, argv[0]);
    if (!param_str)
    {
     printf("Error: Failed to convert argument to string.\n");
     return JS_NewString(ctx, "EINVALIDPARAM");
    }

    printf("Received JSON string: %s\n", param_str);

    // 解析 JSON
    cJSON *json = cJSON_Parse(param_str);
    JS_FreeCString(ctx, param_str);

    if (!json)
    {
     printf("Error: Failed to parse JSON string.\n");
     return JS_NewString(ctx, "EXIT_FAILURE");
    }

    ScanHttpParam scan_http_param;
    memset(&scan_http_param, 0, sizeof(scan_http_param));

    // 获取 URL
    cJSON *url = cJSON_GetObjectItem(json, "url");
    if (cJSON_IsString(url) && url->valuestring && strlen(url->valuestring) < sizeof(scan_http_param.url) - 1)
    {
        strncpy(scan_http_param.url, url->valuestring, sizeof(scan_http_param.url) - 1);
        printf("Parsed URL: %s\n", scan_http_param.url);
    }
    else
    {
        printf("Error: Missing or invalid 'url' in JSON.\n");
        cJSON_Delete(json);
        return JS_NewString(ctx, "EINVALIDPARAM");
    }

    // 获取 headers
    cJSON *headers = cJSON_GetObjectItem(json, "headers");
    if (headers && headers->type == cJSON_Object)  // Headers 对象
    {
        // 将 Headers 对象转换为字符串
        char *headers_str = cJSON_PrintUnformatted(headers);
        if (headers_str && strlen(headers_str) < sizeof(scan_http_param.headers) - 1)
        {
            strncpy(scan_http_param.headers, headers_str, sizeof(scan_http_param.headers) - 1);
            printf("Parsed Headers: %s\n", scan_http_param.headers);
            free(headers_str);
        }
        else
        {
            printf("Error: headers too long or conversion failed\n");
            scan_http_param.headers[0] = '\0';
            if (headers_str) free(headers_str);
        }
    }
    else
    {
        printf("Error: Missing or invalid 'headers' in JSON\n");
        scan_http_param.headers[0] = '\0';
    }

    // 获取 file_name_prefix
    cJSON *file_name_prefix = cJSON_GetObjectItem(json, "file_name_prefix");
    if (cJSON_IsString(file_name_prefix) && file_name_prefix->valuestring && strlen(file_name_prefix->valuestring) < sizeof(scan_http_param.file_name_prefix) - 1)
    {
        strncpy(scan_http_param.file_name_prefix, file_name_prefix->valuestring, sizeof(scan_http_param.file_name_prefix) - 1);
        printf("Parsed file_name_prefix: %s\n", scan_http_param.file_name_prefix);
    }
    else
    {
        printf("Error: Missing or invalid 'file_name_prefix' in JSON.\n");
        cJSON_Delete(json);
        scan_http_param.file_name_prefix[0] = '\0';
    }

    // 获取 custom_field
    cJSON *custom_field = cJSON_GetObjectItem(json, "custom_field");
    if (custom_field)
    {
        // 将 custom_field 对象转换为字符串
        char *custom_field_str = cJSON_PrintUnformatted(custom_field);
        if (custom_field_str && strlen(custom_field_str) < sizeof(scan_http_param.custom_field) - 1)
        {
            strncpy(scan_http_param.custom_field, custom_field_str, sizeof(scan_http_param.custom_field) - 1);
            printf("Parsed custom_field: %s\n", scan_http_param.custom_field);
            free(custom_field_str);  // 释放临时字符串
        }
        else
        {
            printf("Error: custom_field too long or conversion failed\n");
            scan_http_param.custom_field[0] = '\0';
            if (custom_field_str) free(custom_field_str);
        }
    }
    else
    {
        printf("Error: Missing or invalid 'custom_field' in JSONs.\n");
        scan_http_param.custom_field[0] = '\0';
    }

    cJSON_Delete(json);

    // 发送数据到 MFP
    int respond = -1;
    printf("Sending data to MFP...\n");
    SendMsgToMfp(MSG_MODULE_NET, MSG_SETTING_SUB_SET_SCAN_HTTP_PARAMS, 0, sizeof(scan_http_param), &scan_http_param);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_SETTING_SUB_SET_SCAN_HTTP_PARAMS, &respond, NULL, NULL, 10);

    if (respond == 0)
    {
        printf("Data sent successfully.\n");
        return JS_NewString(ctx, "EXIT_SUCCESS");
    }
    else
    {
        printf("Error: Failed to send data to MFP. Response: %d\n", respond);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
}

JSValue js_get_scan_http_params(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    printf("js_get_scan_http_params\n");

    // 初始化结构体
    ScanHttpParam scan_http_param;
    memset(&scan_http_param, 0, sizeof(scan_http_param));

    int32_t respond = -1;
    int32_t receive_cnt = sizeof(ScanHttpParam);

    // 发送和接收消息
    printf("Sending message to MFP...\n");
    SendMsgToMfp(MSG_MODULE_NET, MSG_SETTING_SUB_GET_SCAN_HTTP_PARAMS, 0, 0, NULL);

    printf("Receiving message from MFP...\n");
    int ret = RecvMsgToMfp(MSG_MODULE_NET, MSG_SETTING_SUB_GET_SCAN_HTTP_PARAMS, &respond, &scan_http_param, &receive_cnt, 10);

    // 检查返回值
    if (ret < 0) {
        printf("RecvMsgToMfp failed with return code: %d\n", ret);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    if (respond < 0) {
        printf("MFP responded with error code: %d\n", respond);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    // 打印接收到的结构体内容，方便调试
    printf("Received scan_http_param:\n");
    printf("URL: %s\n", scan_http_param.url);
    printf("Headers: %s\n", scan_http_param.headers);
    printf("files_name_prefix: %s\n", scan_http_param.file_name_prefix);
    printf("custom_field: %s\n", scan_http_param.custom_field);

    // 创建一个 JavaScript 对象并设置属性
    JSValue obj = JS_NewObject(ctx);
    if (!JS_IsObject(obj)) {
        printf("Failed to create JS object.\n");
        return JS_NewString(ctx, "EXIT_FAILURE");
    }

    // 设置基本字段
    JS_SetPropertyStr(ctx, obj, "url", JS_NewString(ctx, scan_http_param.url));
    JS_SetPropertyStr(ctx, obj, "headers", JS_NewString(ctx, scan_http_param.headers));
    JS_SetPropertyStr(ctx, obj, "file_name_prefix", JS_NewString(ctx, scan_http_param.file_name_prefix));

    // 解析 custom_field 字符串为对象
    if (scan_http_param.custom_field[0] != '\0') {
        JSValue custom_field;
        // 直接使用 JS_ParseJSON 解析存储的 JSON 字符串
        custom_field = JS_ParseJSON(ctx, scan_http_param.custom_field, strlen(scan_http_param.custom_field), "<custom_field>");
        if (JS_IsException(custom_field)) {
            custom_field = JS_NULL;
        }
        JS_SetPropertyStr(ctx, obj, "custom_field", custom_field);
    } else {
        JS_SetPropertyStr(ctx, obj, "custom_field", JS_NewObject(ctx));  // 空对象
    }

    return obj;

}


typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_setting_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    /* setting */
    {"js_setting_setFunctionSwitch", 2, js_setting_setFunctionSwitch},
    {"getPowderBoxModel",            1, js_setting_getPowerBoxModel},
    {"set_scan_http_params",         1, js_set_scan_http_params},
    {"get_scan_http_params",         0, js_get_scan_http_params},

};

const JSCFunctionList* getSettingJSCFunctionList(int *length)
{
    *length = countof(pesf_setting_funcs);
    return pesf_setting_funcs;
}

int js_setting_init(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;

   Log("*********start setting module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = getSettingJSCFunctionList(&count);
   Log("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   Log("*********start setting init end**********\n");
   return 0;
}

