/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_shm.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2025-01-02
 * @brief QIO object wrapper for POSIX share memory, only one producer write data and
 *        one consumer read data, each on different process, implemented in a ring bufffer way
 */
#include "qiox.h"
#include <semaphore.h>

#define ROUND_UP(_val, _alignment)     ( ( (_val) % (_alignment) == 0 ) ? (_val) : ( (_alignment) * ((_val) / (_alignment) + 1) ) )
typedef enum
{
    QIOSHM_INIT_SIDE = 1 << 0,
    //  QIOSHM_XXX = 1 << number,
}
QIOSHM_FLAG_E;

#define QIOSHM_MASK 0xffffffff

#define SHM_ERROR -1
#define SHM_EOF   0

typedef struct shmbuf
{
     sem_t sem_read;         // for read synchronization
     sem_t sem_write;        // for write synchronization
     sem_t sem_mutex;        // for mutex, when called by sem_wait, must be called by sem_post in the same process
     uint32_t capacity;      // capacity for total available data size
     uint32_t size;          // size for current effective data in buf, protected by sem_mutex
     uint32_t in, out;       // offset relative to data pointer, protected by sem_mutex
     uint32_t flg_fin : 1;   // finish flag
} SHMBUF_S;

typedef struct priv_info
{
    SHMBUF_S* shmp;
    void*     shmp_data;
    size_t    virtsz;
    char      name[NAME_MAX];
}
PRIV_INFO_S;

static inline unsigned int shm_unused(SHMBUF_S* shmp)
{
    return shmp->capacity- shmp->size;
}

static int32_t qio_shm_close(QIO_S* pqio)
{
    RETURN_VAL_IF(pqio == NULL, QIO_DEBUG, SHM_ERROR);
    PRIV_INFO_S* priv = pqio->priv;

    SHMBUF_S* shmp = priv->shmp;
    if (!shmp->flg_fin)
    {
        shmp->flg_fin = 1;
        sem_post(&shmp->sem_read);
        sem_post(&shmp->sem_write);
        sem_post(&shmp->sem_mutex);
        QIO_DEBUG("qio(%s) have been close, post semaphore", priv->name);
    }
    else
    {
        sem_destroy(&shmp->sem_read);
        sem_destroy(&shmp->sem_write);
        sem_destroy(&shmp->sem_mutex);
    }
    munmap(shmp, priv->virtsz);
    shm_unlink(priv->name);
    pi_free(pqio);

    return 0;
}

/**
 * @brief       The callback function of QIO_READABLE(pqio) or QIO_WRITEABLE(pqio)
 *              like kernel buffer, first check if size available, if not available,
 *              call sem_wait to sleep
 * @param[in]   pqio    : The QIO_S object pointer for inter-process share memory
 * @return      Poll result
 * @retval      > 0     : this QIO_S object can be read or written\n
 *              ==0     : poll timeout\n
 *              < 0     : invalid argument or qio have benn closed
 */
static int32_t qio_shm_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    RETURN_VAL_IF(pqio == NULL, QIO_DEBUG, SHM_ERROR);
    PRIV_INFO_S* priv = pqio->priv;

    SHMBUF_S* shmp = priv->shmp;
    sem_t*    sem;
    int       ret;
    struct timespec ts;

    RETURN_VAL_IF(pqio == NULL, QIO_DEBUG, SHM_ERROR);
    if (what & QIO_POLL_READ)
    {
        if (shmp->size)
        {
            return 1;
        }
        sem = &shmp->sem_read;
    }
    else if (what & QIO_POLL_WRITE)
    {
        if ( shm_unused(shmp) )
        {
            return 1;
        }
        sem = &shmp->sem_write;
    }
    else
    {
        return SHM_ERROR;
    }

    RETURN_VAL_IF(shmp->flg_fin == 1, QIO_WARN, SHM_ERROR);
    if ( tos < 0 || (tos == 0 && tous <= 0) )
    {
        while ( (ret = sem_wait(sem)) == -1 && errno == EINTR )
        {
            continue;       /* Restart if interrupted by handler */
        }
    }
    else
    {
        if (clock_gettime(CLOCK_REALTIME, &ts) == -1)
        {
            QIO_WARN("clock_gettime failed: %s", strerror(errno));
            return SHM_ERROR;
        }
        ts.tv_sec  += tos;
        ts.tv_nsec += tous * 1000;
        while ( (ret = sem_timedwait(sem, &ts)) == -1 && errno == EINTR )
        {
            continue;       /* Restart if interrupted by handler */
        }
    }
    RETURN_VAL_IF(shmp->flg_fin == 1, QIO_WARN, SHM_ERROR);
    //sem_post(sem);

    if (ret == -1)
    {
        if (errno == ETIMEDOUT)
        {
            return 0;
        }
        else
        {
            QIO_WARN("sem wait failed: %s", strerror(errno));
        }
    }
    return ret == 0 ? 1 : SHM_ERROR;
}

/**
 * @brief       The callback function of QIO_READ(pqio)
 * @param[in]   pqio    : The QIO_S object pointer for inter-process share memory
 * @return      Read result
 * @retval      > 0     : recv the length of the data from this QIO_S object\n
 *              = 0     : end of file
 *              < 0     : fail
 */
static int32_t qio_shm_peek(QIO_S* pqio, void* buffer, size_t nbuf)
{
    RETURN_VAL_IF(pqio == NULL, QIO_DEBUG, SHM_ERROR);
    PRIV_INFO_S* priv = pqio->priv;

    SHMBUF_S* shmp = priv->shmp;

    uint32_t shm_size = shmp->size;

    if (shm_size == 0)
    {
        if (shmp->flg_fin == 1)
        {
            QIO_DEBUG("qio had benn closed, return 0");
            return SHM_EOF;
        }
        else
        {
            QIO_DEBUG("size is 0, no more data to read");
            return SHM_ERROR;
        }
    }

    if (nbuf > shm_size)
    {
        nbuf = shm_size;
    }
    memcpy(buffer, priv->shmp_data + shmp->out, nbuf);
    return nbuf;
}

/**
 * @brief       The callback function of QIO_READ(pqio)
 * @param[in]   pqio    : The QIO_S object pointer for inter-process share memory
 * @return      Read result
 * @retval      > 0     : the length of the data that had been written to
 *              = 0     : end of file
 *              < 0     : fail
 */
static int32_t qio_shm_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    RETURN_VAL_IF(pqio == NULL || nbuf == 0, QIO_DEBUG, SHM_ERROR);
    PRIV_INFO_S* priv = pqio->priv;

    SHMBUF_S* shmp = priv->shmp;
    uint32_t unused_size;
    int need_to_post;
    int sval;

    RETURN_VAL_IF(shmp->flg_fin == 1, QIO_WARN, SHM_EOF);

    unused_size = shm_unused(shmp);
    RETURN_VAL_IF(unused_size == 0, QIO_WARN, SHM_ERROR);

    if ( nbuf > unused_size )
    {
        nbuf = unused_size;
    }

    memcpy(priv->shmp_data + shmp->in, buffer, nbuf);

    sem_wait(&shmp->sem_mutex);
    need_to_post = (shmp->size == 0);

    shmp->in += nbuf;
    if (shmp->in >= shmp->capacity)
    {
        shmp->in -= shmp->capacity;
    }
    shmp->size += nbuf;
    if (need_to_post)
    {
        sem_post(&shmp->sem_read);
    }

    sem_post(&shmp->sem_mutex);

    return nbuf;
}

/**
 * @brief       The callback function of QIO_SEEK(pqio), unsupported.
 * @param[in]   pqio    : The QIO_S object pointer for inter-process share memory
 * @return      Seek result
 * @retval      ==0     : success\n
 *              < 0     : fail
 */
static int32_t qio_shm_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    int ret = SHM_ERROR;
    int need_to_post;
    int sval;

    RETURN_VAL_IF(pqio == NULL, QIO_DEBUG, SHM_ERROR);
    RETURN_VAL_IF(offset <= 0, QIO_DEBUG, 0);

    PRIV_INFO_S* priv = pqio->priv;

    SHMBUF_S* shmp = priv->shmp;

    sem_wait(&shmp->sem_mutex);
    do
    {
        BREAK_IF(offset > shmp->size, QIO_WARN);

        need_to_post = (shmp->size == shmp->capacity);
        shmp->out = shmp->out + offset;

        if (shmp->out >= shmp->capacity)
        {
            shmp->out -= shmp->capacity;
        }
        shmp->size -= offset;
        ret = 0;
        if (need_to_post)
        {
            sem_post(&shmp->sem_write);
        }
    }
    while (0);
    sem_post(&shmp->sem_mutex);

    return ret;
}

//QIO_S* qio_shm_create_custom(const char* name, size_t length, QIOSHM_FLAG_E flag)
QIO_S* qio_shm_create(const char* name, size_t length, int oflag)
{
    int          fd;
    void*        p;
    SHMBUF_S*    shmp;
    QIO_S*       pqio = NULL;
    int          shm_create = 0;
    size_t       pagesz;
    size_t       virtsz;
    PRIV_INFO_S* priv;

    do
    {
        if (oflag & O_CREAT)
        {
            shm_create = 1;
            oflag |= O_EXCL;
        }
        oflag |= O_RDWR;

        fd = shm_open(name, oflag, 0600);
        if (fd < 0)
        {
            QIO_WARN( "create share memory failed: %s", strerror(errno) );
            break;
        }

        pagesz = getpagesize();
        length = ROUND_UP (length, pagesz);
        if (shm_create)
        {
            if ( ftruncate(fd, length + pagesz) < 0 )
            {
                QIO_WARN( "set share memory capacity failed: %s", strerror(errno) );
                break;
            }
        }

        virtsz = pagesz + 2 * length;
        p = mmap(NULL, virtsz,  PROT_NONE, MAP_SHARED | MAP_ANONYMOUS, -1, 0);
        BREAK_IF (p == MAP_FAILED, QIO_WARN);
        shmp = p = mmap(p, length + pagesz,  PROT_READ | PROT_WRITE, MAP_SHARED | MAP_FIXED, fd, 0);
        RETURN_VAL_IF(shmp->flg_fin == 1, QIO_WARN, SHM_EOF);
        p = mmap(p+length+pagesz, length,  PROT_READ | PROT_WRITE, MAP_SHARED | MAP_FIXED, fd, pagesz);
        BREAK_IF (p == MAP_FAILED, QIO_WARN);

        if (shm_create)
        {
            BREAK_IF ( sem_init( &shmp -> sem_read,  1, 0 ) < 0, QIO_WARN );  // at first can't be read
            BREAK_IF ( sem_init( &shmp -> sem_write, 1, 0 ) < 0, QIO_WARN );  // at first can be written
            BREAK_IF ( sem_init( &shmp -> sem_mutex, 1, 1 ) < 0, QIO_WARN );  // at first can be locked


            shmp->capacity = length;
            shmp->size = shmp->in = shmp->out = 0;
            shmp->flg_fin = 0;
        }

        pqio = (QIO_S *)pi_zalloc( sizeof(QIO_S) + sizeof(PRIV_INFO_S) );
        BREAK_IF( pqio == NULL, QIO_WARN );

        QIO_DEBUG("create shmbuf (%s) QIO ok, capacity:%u", name, shmp->capacity);
        priv        = (PRIV_INFO_S*)(pqio + 1);
        priv->virtsz= virtsz;
        priv->shmp  = shmp;
        priv->shmp_data = (void*)shmp + pagesz;
        snprintf(priv->name, sizeof(priv->name), "%s", name);
        pqio->close = qio_shm_close;
        pqio->poll  = qio_shm_poll;
        pqio->read  = qio_shm_peek;
        pqio->write = qio_shm_write;
        pqio->seek  = qio_shm_seek;
        pqio->priv  = priv;
    }
    while (0);

    if (pqio == NULL)
    {
        if (fd >= 0)
        {
            shm_unlink(name);
        }
        if (shmp != MAP_FAILED)
        {
            munmap(shmp, virtsz);
        }
    }

    close(fd);
    return pqio;
}
/**
 *@}
 */
