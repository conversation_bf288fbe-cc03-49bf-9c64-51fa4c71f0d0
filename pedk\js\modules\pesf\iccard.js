var lp_Module_Main = globalThis.pedk.Module_Main
var lp_Module_Sub = globalThis.pedk.Module_Sub
var ic_listeners = []
var ERROR_NO = globalThis.pedk.common.ERROR_NO

class CardListener{

    constructor()
    {
        let instance = new globalThis.pedk.ObserverManager();
        instance.addListeners(this, this.SetState, lp_Module_Main.MSG_MOUDLE_USB_ICCARD, lp_Module_Sub.MSG_GET_ICCAED_INFO);
    }
    
    notify(state)
    {
    }
    
    SetState(obj,respond,data)
    {
        //console.log('iccard state:', data)
        obj.updateJobState(data)
        
    }
    
    updateJobState(newJobState)
    {
        console.log('updateJobState start :',newJobState)

        for(const listener of ic_listeners)
        {
            listener.notify(newJobState)
        }
    }
}

export function addCreditCardListener(listener)
{
    if(typeof listener !== 'object')
    {
        return ERROR_NO.EINVALIDPARAM
    }

    ic_listeners.push(listener)
    return ERROR_NO.EXIT_SUCCESS
}

export function removeCreditCardListener(listener)
{
    if(typeof listener !== 'object')
    {
        return ERROR_NO.EINVALIDPARAM
    }

    ic_listeners.pop(listener)
    return ERROR_NO.EXIT_SUCCESS
}

globalThis.pedk.usbh.iccard.CardListener = CardListener
globalThis.pedk.usbh.iccard.addCreditCardListener = addCreditCardListener
globalThis.pedk.usbh.iccard.removeCreditCardListener = removeCreditCardListener

