#include "runtime/modules/std/timer.h"

#include <stdlib.h>
#include <uv.h>

#include "runtime/runtime.h"

typedef struct timer_data_t {
    JSContext* ctx;
    JSValue func;
} timer_data_t;

static void timerCloseCallback(uv_handle_t* handle)
{
    timer_data_t* data = (timer_data_t*)handle->data;
    JSContext* ctx = data->ctx;
    if(NULL != ctx){
        JS_FreeValue(ctx, data->func);
    }
    if(NULL != data){
        free(data);
    }
    handle->data = NULL;
}

static void timer_walk_cb(uv_handle_t* handle, void* arg)
{
    LOG_D("timer","timer_walk_cb");
    if (handle->type == UV_TIMER && handle->data) {
        timerCloseCallback(handle);
    }
}

void timer_close(JSContext* ctx)
{
    LOG_D("timer","timer_close");
    //如果模块结束时，还有存在没被调用的定时器
    //则把全部定时器删除、定时器占用的资源释放
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx); 
    // 1.先遍历loop中的全部句柄,然后触发回调函数
    uv_walk(prt->uv_loop, &timer_walk_cb, ctx);
}

static void setTimeoutCallback(uv_timer_t* handle)
{
    LOG_D("timer","setTimeoutCallback");
    timer_data_t* timer_data = (timer_data_t*)handle->data;
    PeSFRunTime* prt = GET_PESF_RUNTIME(timer_data->ctx);

    JS_Call(timer_data->ctx, timer_data->func, JS_UNDEFINED, 0, NULL);

    uv_timer_stop(handle);
    // 关闭和释放定时器句柄
    uv_close((uv_handle_t*)handle, &timerCloseCallback);
}

static void setIntervalCallback(uv_timer_t* handle)
{
    LOG_D("timer","setIntervalCallback");
    timer_data_t* timer_data = (timer_data_t*)handle->data;
    PeSFRunTime* prt = GET_PESF_RUNTIME(timer_data->ctx);

    JS_Call(timer_data->ctx, timer_data->func, JS_UNDEFINED, 0, NULL);
}

JSValue js_setTimeout(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    LOG_D("timer","js_setTimeout");
    JSValue ret;
    if (argc < 2 || !JS_IsFunction(ctx, argv[0]) || !JS_IsNumber(argv[1])) {
        return JS_ThrowTypeError(ctx, "Invalid arguments");
    }
    // 获取延迟时间和回调函数
    int64_t delay = 0;
    JSValue func = JS_DupValue(ctx, argv[0]);
    timer_data_t* tmp_timer_data = (timer_data_t*)malloc(sizeof(timer_data_t));
    timer_data_t* timer_data = tmp_timer_data;
    JS_ToInt64(ctx, &delay, argv[1]);

    // 3.创建libuv的定时器具备，并初始化，然后启动定时器
    uv_timer_t* timer = (uv_timer_t*)malloc(sizeof(uv_timer_t));
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    ret = JS_NewInt64(ctx, (int64_t)timer);

    timer_data->ctx = ctx;
    timer_data->func = func;

    timer->data = (void*)timer_data;
    uv_timer_init(prt->uv_loop, timer);

    uv_timer_start(timer, setTimeoutCallback, delay, 0);

    return ret;
}

JSValue js_clearTimeout(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    LOG_D("timer","js_clearTimeout");
    if (argc < 1 || !JS_IsNumber(argv[0])) {
        return JS_ThrowTypeError(ctx, "Invalid arguments");
        return JS_ThrowTypeError(ctx, "Invalid arguments");
    }

    int64_t timer_id = 0;
    JS_ToInt64(ctx, &timer_id, argv[0]);
    uv_timer_t* timer = (uv_timer_t*)timer_id;

    uv_timer_stop(timer);
    uv_close((uv_handle_t*)timer, &timerCloseCallback);

    return JS_UNDEFINED;
}

JSValue js_setInterval(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    LOG_D("timer","js_setInterval");
    if (argc < 2) {
        return JS_ThrowTypeError(ctx, "Invalid arguments");
    }

    // 获取延迟时间和回调函数
    int64_t delay;
    if (!JS_IsFunction(ctx, argv[0]) || JS_ToInt64(ctx, &delay, argv[1])) {
        return JS_ThrowTypeError(ctx, "Invalid arguments");
    }

    timer_data_t* data = malloc(sizeof(timer_data_t));
    JSValue func = JS_DupValue(ctx, argv[0]);
    data->ctx = ctx;
    data->func = func;

    // 创建 libuv 定时器句柄，并设置回调函数和数据
    PeSFRunTime* prt = GET_PESF_RUNTIME(ctx);
    uv_timer_t* timer = (uv_timer_t*)malloc(sizeof(uv_timer_t));
    uv_timer_init(prt->uv_loop, timer);
    timer->data = data;

    // 启动定时器
    uv_timer_start(timer, setIntervalCallback, delay, delay);
    JSValue ret = JS_NewInt64(ctx, (int64_t)timer);
    return ret;
}

JSValue js_clearInterval(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv)
{
    LOG_D("timer","js_clearInterval");
    // 1.参数检查
    if (argc < 1 || !JS_IsNumber(argv[0])) {
        return JS_ThrowTypeError(ctx, "Invalid arguments");
    }

    // 2.获取定时器id
    int64_t timer_id = 0;
    JS_ToInt64(ctx, &timer_id, argv[0]);
    uv_timer_t* timer = (uv_timer_t*)timer_id;

    uv_timer_stop(timer);
    uv_close((uv_handle_t*)timer, &timerCloseCallback);

    return JS_UNDEFINED;
}
