#ifndef _PEDK_TOTAL_PAGES_
#define _PEDK_TOTAL_PAGES_

#include <quickjs.h>

JSValue js_getTotalColorPrintPage(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv);

JSValue js_getTotalMonoPrintPage(JSContext *ctx, JSValueConst this_val,
                                  int argc, JSValueConst *argv);


JSValue js_getTotalPagesByMediaSize(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv);

JSValue js_getTotalPrintedSheets(JSContext *ctx, JSValueConst this_val,
                                   int argc, JSValueConst *argv);


#endif /* _PEDK_TOTAL_PAGES_ */
