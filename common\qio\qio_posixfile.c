/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_posixfile.c
 * @addtogroup qio
 * @{
 * <AUTHOR> <PERSON>
 * @date 2023-04-04
 * @brief QIO object wrapper for file I/O
 */
#include "qiox.h"

typedef struct priv_info
{
    char    name[256];
    int32_t fd;
}
PRIV_INFO_S;

static int32_t qio_posixfile_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, priv);
    struct timeval  tv = { .tv_sec = (__time_t)tos, .tv_usec = (__suseconds_t)tous };
    fd_set          fds;
    fd_set*         rfds = ( what & QIO_POLL_READ  ) ? &fds : NULL;
    fd_set*         wfds = ( what & QIO_POLL_WRITE ) ? &fds : NULL;
    fd_set*         efds = ( what & QIO_POLL_EVENT ) ? &fds : NULL;

    RETURN_VAL_IF(priv == NULL || priv->fd < 0, QIO_WARN, QIOEOF);

    FD_ZERO(&fds);
    FD_SET(priv->fd, &fds);

    return select(priv->fd + 1, rfds, wfds, efds, (tos < 0 || (tos == 0 && tous < 0)) ? NULL : &tv);
}

static int32_t qio_posixfile_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->fd < 0, QIO_WARN, QIOEOF);

    return read(priv->fd, buffer, nbuf);
}

static int32_t qio_posixfile_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->fd < 0, QIO_WARN, QIOEOF);

    return write(priv->fd, buffer, nbuf);
}

static int32_t qio_posixfile_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL || priv->fd < 0, QIO_WARN, QIOEOF);

    return lseek(priv->fd, offset, whence);
}

static int32_t qio_posixfile_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( priv )
    {
        QIO_DEBUG("close POSIXFILE(%s,%d) QIO<%p>", priv->name, priv->fd, pqio);
        if ( priv->fd >= 0 )
        {
            fsync(priv->fd);
            posix_fadvise(priv->fd, 0, 0, POSIX_FADV_DONTNEED | POSIX_FADV_NOREUSE);
            pi_close(priv->fd);
        }
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_posixfile_create(const char* stream, int32_t flags)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;

    RETURN_VAL_IF(stream == NULL, QIO_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    priv->fd = pi_open(stream, flags | O_BINARY, 0666);
    if ( priv->fd < 0 )
    {
        QIO_WARN("open (%s) failed: %d<%s>", stream, errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }
    snprintf(priv->name, sizeof(priv->name), "%s", stream);

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        QIO_WARN("alloc POSIXFILE(%s) QIO failed: %d<%s>", stream, errno, strerror(errno));
        pi_close(priv->fd);
        pi_free(priv);
        return NULL;
    }

    QIO_DEBUG("create POSIXFILE(%s) QIO<%p>", stream, pqio);
    pqio->close = qio_posixfile_close;
    pqio->poll  = qio_posixfile_poll;
    pqio->read  = qio_posixfile_read;
    pqio->write = qio_posixfile_write;
    pqio->seek  = qio_posixfile_seek;
    pqio->priv  = priv;

    return pqio;
}
/**
 *@}
 */
