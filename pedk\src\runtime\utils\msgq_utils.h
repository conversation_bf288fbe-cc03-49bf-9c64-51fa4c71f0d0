/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file msgq_utils.h
 * @addtogroup utils
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief msgq_utils init
 */
#ifndef _ZMQ_UTILS_H_
#define _ZMQ_UTILS_H_

#include "basic/config.h"
#include "runtime.h"

/**
 * @brief   send data to queue
 * @param[in] *prt :pesf runtime run context
 * @param[in] *inner_msg :inner msg modules 
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t send_data_to_queue(PeSFRunTime *prt, INNER_MSG *inner_msg);

/**
 * @brief   receive data from queue
 * @param[in] *handle :file handle address
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
void receive_data_from_queue(uv_async_t *handle);

/**
 * @brief   delete message queue
 * @param[in] *queue :queue address
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t delete_message_queue(void *queue);


#endif // _ZMQ_UTILS_H_

/**
 * @}
 */

