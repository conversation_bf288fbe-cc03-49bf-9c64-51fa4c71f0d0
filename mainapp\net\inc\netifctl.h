/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netifctl.h
 * @addtogroup net
 * @{
 * @addtogroup netifctl
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network Interface Control API, eg. "eth0", "mlan0"...
 */
#ifndef __NETIFCTL_H__
#define __NETIFCTL_H__

#define IFACE_ANY       ""
#define IFACE_ETH       "eth0"
#if CONFIG_NET_WIFI
#define IFACE_STA       "wlan0"
#define IFACE_WFD       "wlan1"
#endif

typedef enum
{
    IFACE_ID_ANY = -1,
    IFACE_ID_ETH = 0,
#if CONFIG_NET_WIFI
    IFACE_ID_STA,
    IFACE_ID_WFD,
#endif
    IFACE_ID_NUM,
}
IFACE_ID_E;

typedef enum
{
    IFACE_SWITCH_OFF = 0,
    IFACE_SWITCH_ON,
}
IFACE_SWITCH_E;

/**
 * @brief       Check if the network interface is running. This function can be seen as\n
 *              uint32_t net_ifctl_is_running(const char* ifname);
 * @param[in]   ifname  : The network interface name.
 * @return      Check result
 * @retval      == 1    : yes\n
 *              == 0    : no
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
uint32_t    ifctl_is_running        (const char* ifname, const char* caller);
#define net_ifctl_is_running(a)     ifctl_is_running(a, __func__)

/**
 * @brief       Check if the network interface is active. This function can be seen as\n
 *              uint32_t net_ifctl_is_active(const char* ifname);
 * @param[in]   ifname  : The network interface name.
 * @return      Check result
 * @retval      == 1    : yes\n
 *              == 0    : no
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
uint32_t    ifctl_is_active         (const char* ifname, const char* caller);
#define net_ifctl_is_active(a)      ifctl_is_active(a, __func__)

/**
 * @brief       Check if the network interface is valid. This function can be seen as\n
 *              uint32_t net_ifctl_is_valid(const char* ifname);
 * @param[in]   ifname  : The network interface name.
 * @return      Check result
 * @retval      == 1    : yes\n
 *              == 0    : no
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
uint32_t    ifctl_is_valid         (const char* ifname, const char* caller);
#define net_ifctl_is_valid(a)      ifctl_is_valid(a, __func__)

/**
 * @brief       Switch network interface activation/unactivated. This function can be seen as\n
 *              uint32_t net_ifctl_switch(const char* ifname, IFACE_SWITCH_E on);
 * @param[in]   ifname  : The network interface name.
 * @param[in]   on      : value in IFACE_SWITCH_E.
 * @return      setting result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t     ifctl_switch            (const char* ifname, IFACE_SWITCH_E on, const char* caller);
#define net_ifctl_switch(a, b)      ifctl_switch(a, b, __func__)

/**
 * @brief       Switch network interface activation/unactivated. This function can be seen as\n
 *              uint32_t net_ifctl_switch_safe(const char* ifname, IFACE_SWITCH_E on);
 * @param[in]   ifname  : The network interface name.
 * @param[in]   on      : value in IFACE_SWITCH_E.
 * @return      setting result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t     ifctl_switch_safe       (const char* ifname, IFACE_SWITCH_E on, const char* caller);
#define net_ifctl_switch_safe(a, b) ifctl_switch_safe(a, b, __func__)

/**
 * @brief       Set the speed of this network interface. This function can be seen as\n
 *              uint32_t net_ifctl_set_speed(const char* ifname, const char* mac);
 * @param[in]   ifname  : The network interface name.
 * @param[in]   speed   : The speed mode.
 * @return      setting result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t     ifctl_set_speed         (const char* ifname, IFACE_SPEED_E mode, const char* caller);
#define net_ifctl_set_speed(a, b)   ifctl_set_speed(a, b, __func__)

/**
 * @brief       Set the MAC address of this network interface. This function can be seen as\n
 *              uint32_t net_ifctl_set_mac(const char* ifname, const uint8_t* mac, size_t len);
 * @param[in]   ifname  : The network interface name.
 * @param[in]   mac     : mac address buffer.
 * @param[in]   len     : buffer length, must to be greater than 6.
 * @return      setting result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t     ifctl_set_mac           (const char* ifname, const uint8_t* mac, size_t len, const char* caller);
#define net_ifctl_set_mac(a, b, c)  ifctl_set_mac(a, b, c, __func__)

/**
 * @brief       Set the MAC address of this network interface. This function can be seen as\n
 *              uint32_t net_ifctl_get_mac(const char* ifname, uint8_t* mac, size_t len);
 * @param[in]   ifname  : The network interface name.
 * @param[in]   mac     : mac address buffer.
 * @param[in]   len     : buffer length, must to be greater than 6.
 * @return      getting result
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t     ifctl_get_mac           (const char* ifname, uint8_t* mac, size_t len, const char* caller);
#define net_ifctl_get_mac(a, b, c)  ifctl_get_mac(a, b, c, __func__)

/**
 * @brief       Get the network interface index by default gateway address. This function can be seen as\n
 *              uint32_t net_ifctl_get_id_by_gtwy(uint32_t gateway);
 * @param[in]   gateway : The gateway address.
 * @return      The network interface index.
 * @retval      >= 0    : value in the IFACE_ID_E\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
IFACE_ID_E  ifctl_get_id_by_gtwy    (uint32_t gateway, const char* caller);
#define net_ifctl_get_id_by_gtwy(a) ifctl_get_id_by_gtwy(a, __func__)

/**
 * @brief       Get the network interface index by name. This function can be seen as\n
 *              IFACE_ENUM(name);
 * @param[in]   name    : The network interface_name.
 * @return      The network interface index.
 * @retval      >= 0    : value in the IFACE_ID_E\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
IFACE_ID_E  ifctl_get_id_by_name    (const char* ifname, const char* caller);
#define IFACE_ENUM(name)            ifctl_get_id_by_name(name, __func__)

/**
 * @brief       Get the network interface name by index. This function can be seen as\n
 *              IFACE_NAME(name);
 * @param[in]   name    : The network interface index.
 * @return      The network interface index.
 * @retval      != ""   : The network interface name\n
 *              == ""   : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
const char* ifctl_get_name_by_id    (IFACE_ID_E ifid, const char* caller);
#define IFACE_NAME(ifid)            ifctl_get_name_by_id(ifid, __func__)

#endif /* __NETIFCTL_H__ */
/**
 *@}
 */
