/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file sys.h
 * @addtogroup basic
 * @{
 * @addtogroup basic
 * @autor 
 * @date 2024-06-11
 * @brief common sys config 
 */
 
#ifndef _LINUX_SYS_H_
#define _LINUX_SYS_H_

#include "basic/config.h"
#include "runtime/runtime.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>


//#define NET_SOCKET
#define THREAD_OK     0     ///<thread return sucess
#define THREAD_EROOR -1     ///<thread return error

/**
 * @brief   pesf create thread
 * @param[in] *arga  :pesf struct data
 * @return  Construct result
 * @retval   =0 : error
 * @retval  !=0 : sucess
 * <AUTHOR> @date    2024-06-11
 */
int32_t pesf_new_thread(PeSFRunTime *arga);

/**
 * @brief   pesf thread exit
 * @param[in] *prt  :pesf struct data
 * @return  Construct result
 * @retval   =0 : sucess
 * @retval  !=0 : error
 * <AUTHOR> @date    2024-06-11
 */
int32_t join_thread_exit(PeSFRunTime *prt);

/**
 * @brief    thread exit
 * @param[in] *prt  :pesf struct data
 * @return  Construct result
 * @retval   =0 : sucess
 * @retval  !=0 : error
 * <AUTHOR> @date    2024-06-11
 */
int32_t thread_exit(PeSFRunTime *prt);

/**
 * @brief    get sys time 
 * @return  Construct result
 * @retval   =0 : error
 * @retval  !=0 : sucess
 * <AUTHOR> @date    2024-06-11
 */

int64_t get_sys_time(void);

#ifdef TRANS_MONITOR
/**
 * @brief   Terminal input
 * @param[in] *buffer  :input buffer 
 * @param[in] *length  :input len
 * @param[in] *ms  :wait timeout 
 * @return  Construct result
 * @retval   =0 : sucess
 * @retval  !=0 : error
 * <AUTHOR> @date    2024-06-11
 */
int32_t monitor_in(char *buffer, uint16_t *length, uint32_t ms);

/**
 * @brief   sys time to str
 * @param[in] tm  :time
 * @return  Construct result
 * @retval  char* : char str return
 * <AUTHOR> @date    2024-06-11
 */

char* sys_time_to_str(int64_t tm);

#endif

#endif /* _LINUX_SYS_H_ */

/**
 * @}
 */

