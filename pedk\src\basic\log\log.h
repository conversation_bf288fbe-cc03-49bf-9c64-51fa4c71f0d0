/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file log.h
 * @addtogroup basic
 * @{
 * @addtogroup basic
 * @autor 
 * @date 2024-06-11
 * @brief log config
 */

#ifndef __COMMON__LOG_H__
#define __COMMON__LOG_H__

#include <stdio.h>
#include <time.h>

#define LOG_INPUT_TO_FILE ///<log input to file

#define LOG_TIME_FORMAT "%Y-%m-%d %H:%M:%S" ///<time format

#define LOG_LEVEL_VERBOSE 0 ///<lever 0
#define LOG_LEVEL_DEBUG 1   ///<lever 1
#define LOG_LEVEL_INFO 2    ///<lever 2
#define LOG_LEVEL_WARNING 3 ///<lever 3
#define LOG_LEVEL_ERROR 4   ///<lever 4
#define LOG_LEVEL_FATAL 5   ///<lever 5
#define LOG_LEVEL_JS 6      ///<lever 6

#define LOG_LEVEL_VERBOSE_LABEL "VERBOSE"   ///<lever VERBOSE
#define LOG_LEVEL_DEBUG_LABEL "DEBUG"       ///<lever DEBUG
#define LOG_LEVEL_INFO_LABEL "INFO"         ///<lever INFO
#define LOG_LEVEL_WARNING_LABEL "WARNING"   ///<lever WARNING
#define LOG_LEVEL_ERROR_LABEL "ERROR"       ///<lever ERROR
#define LOG_LEVEL_FATAL_LABEL "FATAL"       ///<lever FATAL
#define LOG_LEVEL_JS_LABEL "JS"             ///<lever JS

#ifdef LOG_INPUT_TO_FILE

static FILE* log_file = NULL;
static int log_num = 1;
#define LOG_MAX_SIZE 2 * 1024 * 1024    ///<log size
#define LOG_FILE_MAX_NUM 5              ///<file num
#define LOG_FILE_PATH "/tmp/Pedk%d.log" ///<Pedk1.log

/**
* @brief LOG input
*/
#define LOG(level, tag, format, ...)                                                               \
    do {                                                                                           \
        time_t t = time(NULL);                                                                     \
        struct tm* tm_info = localtime(&t);                                                        \
        char time_str[20];                                                                         \
        strftime(time_str, sizeof(time_str), LOG_TIME_FORMAT, tm_info);                            \
        const char* level_str;                                                                     \
        switch (level) {                                                                           \
        case LOG_LEVEL_VERBOSE:                                                                    \
            level_str = LOG_LEVEL_VERBOSE_LABEL;                                                   \
            break;                                                                                 \
        case LOG_LEVEL_DEBUG:                                                                      \
            level_str = LOG_LEVEL_DEBUG_LABEL;                                                     \
            break;                                                                                 \
        case LOG_LEVEL_INFO:                                                                       \
            level_str = LOG_LEVEL_INFO_LABEL;                                                      \
            break;                                                                                 \
        case LOG_LEVEL_WARNING:                                                                    \
            level_str = LOG_LEVEL_WARNING_LABEL;                                                   \
            break;                                                                                 \
        case LOG_LEVEL_ERROR:                                                                      \
            level_str = LOG_LEVEL_ERROR_LABEL;                                                     \
            break;                                                                                 \
        case LOG_LEVEL_FATAL:                                                                      \
            level_str = LOG_LEVEL_FATAL_LABEL;                                                     \
            break;                                                                                 \
        case LOG_LEVEL_JS:                                                                         \
            level_str = LOG_LEVEL_JS_LABEL;                                                        \
            break;                                                                                 \
        default:                                                                                   \
            level_str = "UNKNOWN";                                                                 \
        }                                                                                          \
        if (log_file == NULL) {                                                                    \
            char buf[256];                                                                         \
            sprintf(buf, LOG_FILE_PATH, log_num);                                                  \
            log_file = fopen(buf, "a+");                                                           \
        }                                                                                          \
        if(LOG_LEVEL_JS == level){                                                                 \
            fprintf(log_file, "[%s] [%s] [%s] " , time_str, level_str, tag);                       \
            fflush(log_file);                                                                      \
        }else{                                                                                     \
            fprintf(log_file, "[%s] [%s] [%s] " format "\n", time_str, level_str, tag, ##__VA_ARGS__); \
            fflush(log_file);                                                                       \
        }                                                                                          \
        if (ftell(log_file) > LOG_MAX_SIZE) {                                                      \
            fclose(log_file);                                                                      \
            log_file = NULL;                                                                       \
            log_num++;                                                                             \
            if (log_num > LOG_FILE_MAX_NUM) {                                                      \
                log_num = 1;                                                                       \
            }                                                                                      \
            char buf[256];                                                                         \
            sprintf(buf, LOG_FILE_PATH, log_num);                                                  \
            log_file = fopen(buf, "w+");                                                           \
        }                                                                                          \
    } while (0);
#else

/**
* @brief LOG input
*/
#define LOG(level, tag, format, ...)                                                    \
    do {                                                                                \
        time_t t = time(NULL);                                                          \
        struct tm* tm_info = localtime(&t);                                             \
        char time_str[20];                                                              \
        strftime(time_str, sizeof(time_str), LOG_TIME_FORMAT, tm_info);                 \
        const char* level_str;                                                          \
        switch (level) {                                                                \
        case LOG_LEVEL_VERBOSE:                                                         \
            level_str = LOG_LEVEL_VERBOSE_LABEL;                                        \
            break;                                                                      \
        case LOG_LEVEL_DEBUG:                                                           \
            level_str = LOG_LEVEL_DEBUG_LABEL;                                          \
            break;                                                                      \
        case LOG_LEVEL_INFO:                                                            \
            level_str = LOG_LEVEL_INFO_LABEL;                                           \
            break;                                                                      \
        case LOG_LEVEL_WARNING:                                                         \
            level_str = LOG_LEVEL_WARNING_LABEL;                                        \
            break;                                                                      \
        case LOG_LEVEL_ERROR:                                                           \
            level_str = LOG_LEVEL_ERROR_LABEL;                                          \
            break;                                                                      \
        case LOG_LEVEL_FATAL:                                                           \
            level_str = LOG_LEVEL_FATAL_LABEL;                                          \
            break;                                                                      \
        case LOG_LEVEL_JS:                                                              \
            level_str = LOG_LEVEL_JS_LABEL;                                             \
            break;                                                                      \
        default:                                                                        \
            level_str = "UNKNOWN";                                                      \
        }                                                                               \
        if(LOG_LEVEL_JS == level){                                                      \
            printf("[%s] [%s] [%s] ", time_str, level_str, tag);                        \
        }else{                                                                          \
            printf("[%s] [%s] [%s] " format "\n", time_str, level_str, tag, ##__VA_ARGS__); \
        }                                                                                \
    } while (0);
#endif

#define LOG_V(tag, format, ...) LOG(LOG_LEVEL_VERBOSE, tag, format, ##__VA_ARGS__)  ///<level VERBOSE log input format
#define LOG_D(tag, format, ...) LOG(LOG_LEVEL_DEBUG, tag, format, ##__VA_ARGS__)    ///<level DEBUG input format
#define LOG_I(tag, format, ...) LOG(LOG_LEVEL_INFO, tag, format, ##__VA_ARGS__)     ///<level INFO input format
#define LOG_W(tag, format, ...) LOG(LOG_LEVEL_WARNING, tag, format, ##__VA_ARGS__)  ///<level WARNING input format
#define LOG_E(tag, format, ...) LOG(LOG_LEVEL_ERROR, tag, format, ##__VA_ARGS__)    ///<level ERROR input format
#define LOG_F(tag, format, ...) LOG(LOG_LEVEL_FATAL, tag, format, ##__VA_ARGS__)    ///<level FATAL input format
#define LOG_JS(tag, format, ...) LOG(LOG_LEVEL_JS, tag, format, ##__VA_ARGS__)      ///<level JS input format

#ifdef LOG_INPUT_TO_FILE
#define SHOW_MSG(format, ...) if (log_file == NULL) {                                              \
            char buf[256];                                                                         \
            sprintf(buf, LOG_FILE_PATH, log_num);                                                  \
            log_file = fopen(buf, "a+");                                                           \
        }                                                                                          \
        fprintf(log_file,  format ,  ##__VA_ARGS__);                                               \
        fflush(log_file);                                                                          \
        if (ftell(log_file) > LOG_MAX_SIZE) {                                                      \
            fclose(log_file);                                                                      \
            log_file = NULL;                                                                       \
            log_num++;                                                                             \
            if (log_num > LOG_FILE_MAX_NUM) {                                                      \
                log_num = 1;                                                                       \
            }                                                                                      \
            char buf[256];                                                                         \
            sprintf(buf, LOG_FILE_PATH, log_num);                                                  \
            log_file = fopen(buf, "w+");                                                           \
        }   ///<SHOW_MSG input format
#else
#define SHOW_MSG(format, ...) printf(format, ##__VA_ARGS__);    ///<SHOW_MSG input format
#endif
#endif // __COMMON__LOG_H__