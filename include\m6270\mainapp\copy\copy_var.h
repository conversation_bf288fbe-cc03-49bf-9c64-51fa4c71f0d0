/**
* @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file copy_var.h
* @addtogroup copy
* @{
* @addtogroup  copy
* <AUTHOR>
* @date  2023-6-2
* @version  v1.0
* @brief  copy job common parameters
**/

#ifndef __COPY_CTRL_H__
#define __COPY_CTRL_H__


#define COPY_DEBUG_ON                   5
#define COPY_DEBUG_OFF                  10
#define COPY_DEBUG_WARNING              3
#define COPY_SCALE_LIMIT_MIN     25
#define COPY_SCALE_LIMIT_MAX     400

typedef enum
{
    COPY_SCALE_AUTO = 0,        // AUTO
	COPY_SCALE_USER_DEFINE, 	// 25%~400%
}COPY_SCALE_TYPE_E;


#endif

/**
* @}
**/

