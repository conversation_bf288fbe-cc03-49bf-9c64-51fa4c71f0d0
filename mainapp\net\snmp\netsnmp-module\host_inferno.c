/*
 * @host_inferno host_inferno.c
 */

#include <net-snmp/net-snmp-config.h>
#include <net-snmp/net-snmp-includes.h>
#include <net-snmp/agent/net-snmp-agent-includes.h>
#include "host_inferno.h"
#include "snmpipc.h"

void init_host_inferno_string(void);
int
hrDeviceStatus_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPrinterStatus_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrDeviceIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrDeviceType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrDeviceDescr_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrDeviceID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPrinterDetectedErrorState_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrMemorySize_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrDiskStorageAccess_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrDiskStorageMedia_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrDiskStorageRemoveble_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrDiskStorageCapacity_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
    int
hrSystemProcesses_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrSystemMaxProcesses_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrStorageIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrStorageType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrStorageDescr_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrStorageAllocationUnits_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

 int
hrStorageSize_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);
int
hrStorageUsed_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrStorageAllocationFailures_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrDeviceErrors_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrProcessorFrwID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrProcessorLoad_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrNetworkIfIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPartitionIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPartitionLabel_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPartitionID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPartitionSize_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrPartitionFSIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSMountPoint_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSRemoteMountPoint_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSAccess_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSBootable_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSStorageIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSLastFullBackupDate_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrFSLastPartialBackupDate_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWOSIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunName_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunPath_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunParameters_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunStatus_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunPerfCPU_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWRunPerfMem_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWInstalledIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWInstalledName_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWInstalledID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWInstalledType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

int
hrSWInstalledDate_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests);

static int snmp_lock = 0;
static SNMP_MAP_S* snmp_map = NULL;
static HRES_MIB_S* hr_map = NULL;

static oid hrPrinterStatus_oid[]                = {1,3,6,1,2,1,25,3,5,1,1,1};
static oid hrPrinterDetectedErrorState_oid[]    = {1,3,6,1,2,1,25,3,5,1,2,1};
static oid hrDeviceIndex_oid[]                  = {1,3,6,1,2,1,25,3,2,1,1,1};
static oid hrDeviceType_oid[]                   = {1,3,6,1,2,1,25,3,2,1,2,1};
static oid hrDeviceDescr_oid[]                  = {1,3,6,1,2,1,25,3,2,1,3,1};
static oid hrDeviceID_oid[]                     = {1,3,6,1,2,1,25,3,2,1,4,1};
static oid hrDeviceStatus_oid[]                 = {1,3,6,1,2,1,25,3,2,1,5,1};
static oid hrDeviceErrors_oid[]                 = {1,3,6,1,2,1,25,3,2,1,6,1};
static oid hrMemorySize_oid[]                   = {1,3,6,1,2,1,25,2,2};
static oid hrDiskStorageAccess_oid[]            = {1,3,6,1,2,1,25,3,6,1,1};
static oid hrDiskStorageMedia_oid[]             = {1,3,6,1,2,1,25,3,6,1,2};
static oid hrDiskStorageRemoveble_oid[]         = {1,3,6,1,2,1,25,3,6,1,3};
static oid hrDiskStorageCapacity_oid[]          = {1,3,6,1,2,1,25,3,6,1,4};
static oid hrSystemProcesses_oid[]              = {1,3,6,1,2,1,25,1,6,1};
static oid hrSystemMaxProcesses_oid[]           = {1,3,6,1,2,1,25,1,7,1};
static oid hrStorageIndex_oid[]                 = {1,3,6,1,2,1,25,2,3,1,1,1};
static oid hrStorageType_oid[]                  = {1,3,6,1,2,1,25,2,3,1,2,1};
static oid hrStorageDescr_oid[]                 = {1,3,6,1,2,1,25,2,3,1,3,1};
static oid hrStorageAllocationUnits_oid[]       = {1,3,6,1,2,1,25,2,3,1,4,1};
static oid hrStorageSize_oid[]                  = {1,3,6,1,2,1,25,2,3,1,5,1};
static oid hrStorageUsed_oid[]                  = {1,3,6,1,2,1,25,2,3,1,6,1};
static oid hrStorageAllocationFailures_oid[]    = {1,3,6,1,2,1,25,2,3,1,7,1};
static oid hrProcessorFrwID_oid[]               = {1,3,6,1,2,1,25,3,3,1,1};
static oid hrProcessorLoad_oid[]                = {1,3,6,1,2,1,25,3,3,1,2};
static oid hrNetworkIfIndex_oid[]               = {1,3,6,1,2,1,25,3,4,1,1};
static oid hrPartitionIndex_oid[]               = {1,3,6,1,2,1,25,3,7,1,1};
static oid hrPartitionLabel_oid[]               = {1,3,6,1,2,1,25,3,7,1,2};
static oid hrPartitionID_oid[]                  = {1,3,6,1,2,1,25,3,7,1,3};
static oid hrPartitionSize_oid[]                = {1,3,6,1,2,1,25,3,7,1,5};
static oid hrPartitionFSIndex_oid[]             = {1,3,6,1,2,1,25,3,7,1,6};
static oid hrFSIndex_oid[]                      = {1,3,6,1,2,1,25,3,8,1,1};
static oid hrFSMountPoint_oid[]                 = {1,3,6,1,2,1,25,3,8,1,2};
static oid hrFSRemoteMountPoint_oid[]           = {1,3,6,1,2,1,25,3,8,1,3};
static oid hrFSType_oid[]                       = {1,3,6,1,2,1,25,3,8,1,4};
static oid hrFSAccess_oid[]                     = {1,3,6,1,2,1,25,3,8,1,5};
static oid hrFSBootable_oid[]                   = {1,3,6,1,2,1,25,3,8,1,6};
static oid hrFSStorageIndex_oid[]               = {1,3,6,1,2,1,25,3,8,1,7};
static oid hrFSLastFullBackupDate_oid[]         = {1,3,6,1,2,1,25,3,8,1,8};
static oid hrFSLastPartialBackupDate_oid[]      = {1,3,6,1,2,1,25,3,8,1,9};
static oid hrSWOSIndex_oid[]                    = {1,3,6,1,2,1,25,4,1};
static oid hrSWRunIndex_oid[]                   = {1,3,6,1,2,1,25,4,2,1,1};
static oid hrSWRunName_oid[]                    = {1,3,6,1,2,1,25,4,2,1,2};
static oid hrSWRunID_oid[]                      = {1,3,6,1,2,1,25,4,2,1,3};
static oid hrSWRunPath_oid[]                    = {1,3,6,1,2,1,25,4,2,1,4};
static oid hrSWRunParameters_oid[]              = {1,3,6,1,2,1,25,4,2,1,5};
static oid hrSWRunType_oid[]                    = {1,3,6,1,2,1,25,4,2,1,6};
static oid hrSWRunStatus_oid[]                  = {1,3,6,1,2,1,25,4,2,1,7};
static oid hrSWRunPerfCPU_oid[]                 = {1,3,6,1,2,1,25,5,1,1,1};
static oid hrSWRunPerfMem_oid[]                 = {1,3,6,1,2,1,25,5,1,1,2};
static oid hrSWInstalledIndex_oid[]             = {1,3,6,1,2,1,25,6,3,1,1};
static oid hrSWInstalledName_oid[]              = {1,3,6,1,2,1,25,6,3,1,2};
static oid hrSWInstalledID_oid[]                = {1,3,6,1,2,1,25,6,3,1,3};
static oid hrSWInstalledType_oid[]              = {1,3,6,1,2,1,25,6,3,1,4};
static oid hrSWInstalledDate_oid[]              = {1,3,6,1,2,1,25,6,3,1,5};

void init_host_inferno(void)
{
    DEBUGMSGTL(("host_inferno", "Initializing\n"));

    snmp_map = (SNMP_MAP_S *)ipc_shm(&snmp_lock, SNMP_MAP_NAME, sizeof(SNMP_MAP_S));

    if (snmp_map) {
        netsnmp_watcher_info watcher_info;
        netsnmp_handler_registration *reginfo;
        int watcher_flags = WATCHER_MAX_SIZE;

        hr_map = &snmp_map->hres;
        reginfo = netsnmp_create_handler_registration("hrPrinterStatus", hrPrinterStatus_handler,
                hrPrinterStatus_oid, OID_LENGTH(hrPrinterStatus_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrPrinterDetectedErrorState", hrPrinterDetectedErrorState_handler,
                hrPrinterDetectedErrorState_oid, OID_LENGTH(hrPrinterDetectedErrorState_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDeviceIndex", hrDeviceIndex_handler,
                hrDeviceIndex_oid, OID_LENGTH(hrDeviceIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDeviceType", hrDeviceType_handler,
                hrDeviceType_oid, OID_LENGTH(hrDeviceType_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDeviceDescr", hrDeviceDescr_handler,
                hrDeviceDescr_oid, OID_LENGTH(hrDeviceDescr_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDeviceID", hrDeviceID_handler,
                hrDeviceID_oid, OID_LENGTH(hrDeviceID_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDeviceStatus", hrDeviceStatus_handler,
                hrDeviceStatus_oid, OID_LENGTH(hrDeviceStatus_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrMemorySize", hrMemorySize_handler,
                hrMemorySize_oid, OID_LENGTH(hrMemorySize_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDiskStorageAccess", hrDiskStorageAccess_handler,
                hrDiskStorageAccess_oid, OID_LENGTH(hrDiskStorageAccess_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDiskStorageMedia", hrDiskStorageMedia_handler,
                hrDiskStorageMedia_oid, OID_LENGTH(hrDiskStorageMedia_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDiskStorageRemoveble", hrDiskStorageRemoveble_handler,
                hrDiskStorageRemoveble_oid, OID_LENGTH(hrDiskStorageRemoveble_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDiskStorageCapacity", hrDiskStorageCapacity_handler,
                hrDiskStorageCapacity_oid, OID_LENGTH(hrDiskStorageCapacity_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSystemProcesses", hrSystemProcesses_handler,
                hrSystemProcesses_oid, OID_LENGTH(hrSystemProcesses_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSystemMaxProcesses", hrSystemMaxProcesses_handler,
                hrSystemMaxProcesses_oid, OID_LENGTH(hrSystemMaxProcesses_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageIndex", hrStorageIndex_handler,
                hrStorageIndex_oid, OID_LENGTH(hrStorageIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageType", hrStorageType_handler,
                hrStorageType_oid, OID_LENGTH(hrStorageType_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageDescr", hrStorageDescr_handler,
                hrStorageDescr_oid, OID_LENGTH(hrStorageDescr_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageAllocationUnits", hrStorageAllocationUnits_handler,
                hrStorageAllocationUnits_oid, OID_LENGTH(hrStorageAllocationUnits_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageSize", hrStorageSize_handler,
                hrStorageSize_oid, OID_LENGTH(hrStorageSize_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageUsed", hrStorageUsed_handler,
                hrStorageUsed_oid, OID_LENGTH(hrStorageUsed_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrStorageAllocationFailures", hrStorageAllocationFailures_handler,
                hrStorageAllocationFailures_oid, OID_LENGTH(hrStorageAllocationFailures_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrDeviceErrors", hrDeviceErrors_handler,
                hrDeviceErrors_oid, OID_LENGTH(hrDeviceErrors_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrProcessorFrwID", hrProcessorFrwID_handler,
                hrProcessorFrwID_oid, OID_LENGTH(hrProcessorFrwID_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrProcessorLoad", hrProcessorLoad_handler,
                hrProcessorLoad_oid, OID_LENGTH(hrProcessorLoad_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrNetworkIfIndex", hrNetworkIfIndex_handler,
                hrNetworkIfIndex_oid, OID_LENGTH(hrNetworkIfIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrPartitionIndex", hrPartitionIndex_handler,
                hrPartitionIndex_oid, OID_LENGTH(hrPartitionIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrPartitionLabel", hrPartitionLabel_handler,
                hrPartitionLabel_oid, OID_LENGTH(hrPartitionLabel_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrPartitionID", hrPartitionID_handler,
                hrPartitionID_oid, OID_LENGTH(hrPartitionID_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrPartitionSize", hrPartitionSize_handler,
                hrPartitionSize_oid, OID_LENGTH(hrPartitionSize_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrPartitionFSIndex", hrPartitionFSIndex_handler,
                hrPartitionFSIndex_oid, OID_LENGTH(hrPartitionFSIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSIndex", hrFSIndex_handler,
                hrFSIndex_oid, OID_LENGTH(hrFSIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSMountPoint", hrFSMountPoint_handler,
                hrFSMountPoint_oid, OID_LENGTH(hrFSMountPoint_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSRemoteMountPoint", hrFSRemoteMountPoint_handler,
                hrFSRemoteMountPoint_oid, OID_LENGTH(hrFSRemoteMountPoint_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSType", hrFSType_handler,
                hrFSType_oid, OID_LENGTH(hrFSType_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSAccess", hrFSAccess_handler,
                hrFSAccess_oid, OID_LENGTH(hrFSAccess_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSBootable", hrFSBootable_handler,
                hrFSBootable_oid, OID_LENGTH(hrFSBootable_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSStorageIndex", hrFSStorageIndex_handler,
                hrFSStorageIndex_oid, OID_LENGTH(hrFSStorageIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSLastFullBackupDate", hrFSLastFullBackupDate_handler,
                hrFSLastFullBackupDate_oid, OID_LENGTH(hrFSLastFullBackupDate_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrFSLastPartialBackupDate", hrFSLastPartialBackupDate_handler,
                hrFSLastPartialBackupDate_oid, OID_LENGTH(hrFSLastPartialBackupDate_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWOSIndex", hrSWOSIndex_handler,
                hrSWOSIndex_oid, OID_LENGTH(hrSWOSIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunIndex", hrSWRunIndex_handler,
                hrSWRunIndex_oid, OID_LENGTH(hrSWRunIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunName", hrSWRunName_handler,
                hrSWRunName_oid, OID_LENGTH(hrSWRunName_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunID", hrSWRunID_handler,
                hrSWRunID_oid, OID_LENGTH(hrSWRunID_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunPath", hrSWRunPath_handler,
                hrSWRunPath_oid, OID_LENGTH(hrSWRunPath_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunParameters", hrSWRunParameters_handler,
                hrSWRunParameters_oid, OID_LENGTH(hrSWRunParameters_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunType", hrSWRunType_handler,
                hrSWRunType_oid, OID_LENGTH(hrSWRunType_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunStatus", hrSWRunStatus_handler,
                hrSWRunStatus_oid, OID_LENGTH(hrSWRunStatus_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunPerfCPU", hrSWRunPerfCPU_handler,
                hrSWRunPerfCPU_oid, OID_LENGTH(hrSWRunPerfCPU_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWRunPerfMem", hrSWRunPerfMem_handler,
                hrSWRunPerfMem_oid, OID_LENGTH(hrSWRunPerfMem_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWInstalledIndex", hrSWInstalledIndex_handler,
                hrSWInstalledIndex_oid, OID_LENGTH(hrSWInstalledIndex_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWInstalledName", hrSWInstalledName_handler,
                hrSWInstalledName_oid, OID_LENGTH(hrSWInstalledName_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWInstalledID", hrSWInstalledID_handler,
                hrSWInstalledID_oid, OID_LENGTH(hrSWInstalledID_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWInstalledType", hrSWInstalledType_handler,
                hrSWInstalledType_oid, OID_LENGTH(hrSWInstalledType_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

        reginfo = netsnmp_create_handler_registration("hrSWInstalledDate", hrSWInstalledDate_handler,
                hrSWInstalledDate_oid, OID_LENGTH(hrSWInstalledDate_oid),
                HANDLER_CAN_RONLY);
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER, watcher_flags,sizeof(int), NULL);
        netsnmp_register_watched_instance(reginfo, &watcher_info);

    } else {
        fprintf(stderr, "Failed to create shared memory so that SN3012DWMIB module will not response\n");
    }
}

    int
hrSystemProcesses_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSystemProcesses_oid))
                {
                    length = OID_LENGTH(hrSystemProcesses_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSystemProcesses_oid, OID_LENGTH(hrSystemProcesses_oid))
                        == 0) {
                    int hrSystemProcesses_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSystemProcesses\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSystemProcesses_val = hr_map->_hrSystemProcesses;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSystemProcesses_val, sizeof(hrSystemProcesses_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}


    int
hrMemorySize_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrMemorySize_oid))
                {
                    length = OID_LENGTH(hrMemorySize_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrMemorySize_oid, OID_LENGTH(hrMemorySize_oid))
                        == 0) {
                    int hrMemorySize_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrMemorySize\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrMemorySize_val = hr_map->_hrMemorySize;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrMemorySize_val, sizeof(hrMemorySize_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrPrinterStatus_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPrinterStatus_oid))
                {
                    length = OID_LENGTH(hrPrinterStatus_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPrinterStatus_oid, OID_LENGTH(hrPrinterStatus_oid))
                        == 0) {
                    int hrPrinterStatus_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrPrinterStatus\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrPrinterStatus_val = hr_map->_hrPrinterStatus;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrPrinterStatus_val, sizeof(hrPrinterStatus_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}
    int
hrPrinterDetectedErrorState_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPrinterDetectedErrorState_oid))
                {
                    length = OID_LENGTH(hrPrinterDetectedErrorState_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPrinterDetectedErrorState_oid, OID_LENGTH(hrPrinterDetectedErrorState_oid))
                        == 0) {
                    char hrPrinterDetectedErrorState_val[2];

                    DEBUGMSGTL(("host_inferno", "oid hrPrinterDetectedErrorState\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrPrinterDetectedErrorState_val, hr_map->_hrPrinterDetectedErrorState, sizeof(hrPrinterDetectedErrorState_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrPrinterDetectedErrorState_val, sizeof(hrPrinterDetectedErrorState_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDeviceIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDeviceIndex_oid))
                {
                    length = OID_LENGTH(hrDeviceIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDeviceIndex_oid, OID_LENGTH(hrDeviceIndex_oid))
                        == 0) {
                    int hrDeviceIndex_val;

                    DEBUGMSGTL(("host_inferno", "oid hrDeviceIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDeviceIndex_val = hr_map->_hrDeviceIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDeviceIndex_val, sizeof(hrDeviceIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDeviceType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDeviceType_oid))
                {
                    length = OID_LENGTH(hrDeviceType_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDeviceType_oid, OID_LENGTH(hrDeviceType_oid))
                        == 0) {
                    char hrDeviceType_val[256];

                    DEBUGMSGTL(("host_inferno", "oid hrDeviceType\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrDeviceType_val, hr_map->_hrDeviceType, sizeof(hrDeviceType_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrDeviceType_val, strlen(hrDeviceType_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDeviceDescr_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDeviceDescr_oid))
                {
                    length = OID_LENGTH(hrDeviceDescr_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDeviceDescr_oid, OID_LENGTH(hrDeviceDescr_oid))
                        == 0) {
                    char hrDeviceDescr_val[64];

                    DEBUGMSGTL(("host_inferno", "oid hrDeviceDescr\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrDeviceDescr_val, hr_map->_hrDeviceDescr, sizeof(hrDeviceDescr_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrDeviceDescr_val, strlen(hrDeviceDescr_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDeviceID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDeviceID_oid))
                {
                    length = OID_LENGTH(hrDeviceID_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDeviceID_oid, OID_LENGTH(hrDeviceID_oid))
                        == 0) {
                    char hrDeviceID_val[256];

                    DEBUGMSGTL(("host_inferno", "oid hrDeviceID\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrDeviceID_val, hr_map->_hrDeviceID, sizeof(hrDeviceID_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrDeviceID_val, strlen(hrDeviceID_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDeviceStatus_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDeviceStatus_oid))
                {
                    length = OID_LENGTH(hrDeviceStatus_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDeviceStatus_oid, OID_LENGTH(hrDeviceStatus_oid))
                        == 0) {
                    int hrDeviceStatus_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrDeviceStatus\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDeviceStatus_val = hr_map->_hrDeviceStatus;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDeviceStatus_val, sizeof(hrDeviceStatus_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDiskStorageAccess_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDiskStorageAccess_oid))
                {
                    length = OID_LENGTH(hrDiskStorageAccess_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDiskStorageAccess_oid, OID_LENGTH(hrDiskStorageAccess_oid))
                        == 0) {
                    int hrDiskStorageAccess_val;

                    DEBUGMSGTL(("host_inferno", "oid hrDiskStorageAccess\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDiskStorageAccess_val = hr_map->_hrDiskStorageAccess;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDiskStorageAccess_val, sizeof(hrDiskStorageAccess_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDiskStorageMedia_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDiskStorageMedia_oid))
                {
                    length = OID_LENGTH(hrDiskStorageMedia_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDiskStorageMedia_oid, OID_LENGTH(hrDiskStorageMedia_oid))
                        == 0) {
                    int hrDiskStorageMedia_val;

                    DEBUGMSGTL(("host_inferno", "oid hrDiskStorageMedia\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDiskStorageMedia_val = hr_map->_hrDiskStorageMedia;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDiskStorageMedia_val, sizeof(hrDiskStorageMedia_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}
    int
hrDiskStorageRemoveble_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDiskStorageRemoveble_oid))
                {
                    length = OID_LENGTH(hrDiskStorageRemoveble_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDiskStorageRemoveble_oid, OID_LENGTH(hrDiskStorageRemoveble_oid))
                        == 0) {
                    int hrDiskStorageRemoveble_val;

                    DEBUGMSGTL(("host_inferno", "oid hrDiskStorageRemoveble\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDiskStorageRemoveble_val = hr_map->_hrDiskStorageRemoveble;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDiskStorageRemoveble_val, sizeof(hrDiskStorageRemoveble_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}


    int
hrDiskStorageCapacity_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDiskStorageCapacity_oid))
                {
                    length = OID_LENGTH(hrDiskStorageCapacity_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDiskStorageCapacity_oid, OID_LENGTH(hrDiskStorageCapacity_oid))
                        == 0) {
                    int hrDiskStorageCapacity_val;

                    DEBUGMSGTL(("host_inferno", "oid hrDiskStorageCapacity\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDiskStorageCapacity_val = hr_map->_hrDiskStorageCapacity;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDiskStorageCapacity_val, sizeof(hrDiskStorageCapacity_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSystemMaxProcesses_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSystemMaxProcesses_oid))
                {
                    length = OID_LENGTH(hrSystemMaxProcesses_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSystemMaxProcesses_oid, OID_LENGTH(hrSystemMaxProcesses_oid))
                        == 0) {
                    int hrSystemMaxProcesses_val;

                    DEBUGMSGTL(("host_inferno", "oid hrSystemMaxProcesses\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSystemMaxProcesses_val = hr_map->_hrSystemMaxProcesses;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSystemMaxProcesses_val, sizeof(hrSystemMaxProcesses_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageIndex_oid))
                {
                    length = OID_LENGTH(hrStorageIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageIndex_oid, OID_LENGTH(hrStorageIndex_oid))
                        == 0) {
                    int hrStorageIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrStorageIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrStorageIndex_val = hr_map->_hrStorageIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrStorageIndex_val, sizeof(hrStorageIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageType_oid))
                {
                    length = OID_LENGTH(hrStorageType_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageType_oid, OID_LENGTH(hrStorageType_oid))
                        == 0) {
                    int hrStorageType_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrStorageType\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrStorageType_val = hr_map->_hrStorageType;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrStorageType_val, sizeof(hrStorageType_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageDescr_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageDescr_oid))
                {
                    length = OID_LENGTH(hrStorageDescr_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageDescr_oid, OID_LENGTH(hrStorageDescr_oid))
                        == 0) {
                    char hrStorageDescr_val[256];

                    DEBUGMSGTL(("host_inferno", "oid hrStorageDescr\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrStorageDescr_val, hr_map->_hrStorageDescr, sizeof(hrStorageDescr_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrStorageDescr_val, strlen(hrStorageDescr_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageAllocationUnits_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageAllocationUnits_oid))
                {
                    length = OID_LENGTH(hrStorageAllocationUnits_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageAllocationUnits_oid, OID_LENGTH(hrStorageAllocationUnits_oid))
                        == 0) {
                    int hrStorageAllocationUnits_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrStorageAllocationUnits\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrStorageAllocationUnits_val = hr_map->_hrStorageAllocationUnits;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrStorageAllocationUnits_val, sizeof(hrStorageAllocationUnits_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageSize_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageSize_oid))
                {
                    length = OID_LENGTH(hrStorageSize_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageSize_oid, OID_LENGTH(hrStorageSize_oid))
                        == 0) {
                    int hrStorageSize_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrStorageSize\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrStorageSize_val = hr_map->_hrStorageSize;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrStorageSize_val, sizeof(hrStorageSize_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageUsed_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageUsed_oid))
                {
                    length = OID_LENGTH(hrStorageUsed_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageUsed_oid, OID_LENGTH(hrStorageUsed_oid))
                        == 0) {
                    int hrStorageUsed_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrStorageUsed\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrStorageUsed_val = hr_map->_hrStorageUsed;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrStorageUsed_val, sizeof(hrStorageUsed_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrStorageAllocationFailures_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrStorageAllocationFailures_oid))
                {
                    length = OID_LENGTH(hrStorageAllocationFailures_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrStorageAllocationFailures_oid, OID_LENGTH(hrStorageAllocationFailures_oid))
                        == 0) {
                    int hrStorageAllocationFailures_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrStorageAllocationFailures\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrStorageAllocationFailures_val = hr_map->_hrStorageAllocationFailures;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrStorageAllocationFailures_val, sizeof(hrStorageAllocationFailures_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrDeviceErrors_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrDeviceErrors_oid))
                {
                    length = OID_LENGTH(hrDeviceErrors_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrDeviceErrors_oid, OID_LENGTH(hrDeviceErrors_oid))
                        == 0) {
                    int hrDeviceErrors_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrDeviceErrors\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrDeviceErrors_val = hr_map->_hrDeviceErrors;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrDeviceErrors_val, sizeof(hrDeviceErrors_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrProcessorFrwID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrProcessorFrwID_oid))
                {
                    length = OID_LENGTH(hrProcessorFrwID_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrProcessorFrwID_oid, OID_LENGTH(hrProcessorFrwID_oid))
                        == 0) {
                    int hrProcessorFrwID_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrProcessorFrwID\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrProcessorFrwID_val = hr_map->_hrProcessorFrwID;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrProcessorFrwID_val, sizeof(hrProcessorFrwID_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrProcessorLoad_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrProcessorLoad_oid))
                {
                    length = OID_LENGTH(hrProcessorLoad_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrProcessorLoad_oid, OID_LENGTH(hrProcessorLoad_oid))
                        == 0) {
                    int hrProcessorLoad_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrProcessorLoad\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrProcessorLoad_val = hr_map->_hrProcessorLoad;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrProcessorLoad_val, sizeof(hrProcessorLoad_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrNetworkIfIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrNetworkIfIndex_oid))
                {
                    length = OID_LENGTH(hrNetworkIfIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrNetworkIfIndex_oid, OID_LENGTH(hrNetworkIfIndex_oid))
                        == 0) {
                    int hrNetworkIfIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrNetworkIfIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrNetworkIfIndex_val = hr_map->_hrNetworkIfIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrNetworkIfIndex_val, sizeof(hrNetworkIfIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrPartitionIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPartitionIndex_oid))
                {
                    length = OID_LENGTH(hrPartitionIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPartitionIndex_oid, OID_LENGTH(hrPartitionIndex_oid))
                        == 0) {
                    int hrPartitionIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrPartitionIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrPartitionIndex_val = hr_map->_hrPartitionIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrPartitionIndex_val, sizeof(hrPartitionIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrPartitionLabel_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPartitionLabel_oid))
                {
                    length = OID_LENGTH(hrPartitionLabel_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPartitionLabel_oid, OID_LENGTH(hrPartitionLabel_oid))
                        == 0) {
                    char hrPartitionLabel_val[128];

                    DEBUGMSGTL(("host_inferno", "oid hrPartitionLabel\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrPartitionLabel_val, hr_map->_hrPartitionLabel, sizeof(hrPartitionLabel_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrPartitionLabel_val, strlen(hrPartitionLabel_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrPartitionID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPartitionID_oid))
                {
                    length = OID_LENGTH(hrPartitionID_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPartitionID_oid, OID_LENGTH(hrPartitionID_oid))
                        == 0) {
                    char hrPartitionID_val[128];

                    DEBUGMSGTL(("host_inferno", "oid hrPartitionID\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrPartitionID_val, hr_map->_hrPartitionID, sizeof(hrPartitionID_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrPartitionID_val, strlen(hrPartitionID_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrPartitionSize_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPartitionSize_oid))
                {
                    length = OID_LENGTH(hrPartitionSize_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPartitionSize_oid, OID_LENGTH(hrPartitionSize_oid))
                        == 0) {
                    int hrPartitionSize_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrPartitionSize\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrPartitionSize_val = hr_map->_hrPartitionSize;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrPartitionSize_val, sizeof(hrPartitionSize_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

    int
hrPartitionFSIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrPartitionFSIndex_oid))
                {
                    length = OID_LENGTH(hrPartitionFSIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrPartitionFSIndex_oid, OID_LENGTH(hrPartitionFSIndex_oid))
                        == 0) {
                    int hrPartitionFSIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrPartitionFSIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrPartitionFSIndex_val = hr_map->_hrPartitionFSIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrPartitionFSIndex_val, sizeof(hrPartitionFSIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSIndex_oid))
                {
                    length = OID_LENGTH(hrFSIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSIndex_oid, OID_LENGTH(hrFSIndex_oid))
                        == 0) {
                    int hrFSIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrFSIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrFSIndex_val = hr_map->_hrFSIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrFSIndex_val, sizeof(hrFSIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSMountPoint_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSMountPoint_oid))
                {
                    length = OID_LENGTH(hrFSMountPoint_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSMountPoint_oid, OID_LENGTH(hrFSMountPoint_oid))
                        == 0) {
                    char hrFSMountPoint_val[128];

                    DEBUGMSGTL(("host_inferno", "oid hrFSMountPoint\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrFSMountPoint_val, hr_map->_hrFSMountPoint, sizeof(hrFSMountPoint_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrFSMountPoint_val, strlen(hrFSMountPoint_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSRemoteMountPoint_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSRemoteMountPoint_oid))
                {
                    length = OID_LENGTH(hrFSRemoteMountPoint_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSRemoteMountPoint_oid, OID_LENGTH(hrFSRemoteMountPoint_oid))
                        == 0) {
                    char hrFSRemoteMountPoint_val[128];

                    DEBUGMSGTL(("host_inferno", "oid hrFSRemoteMountPoint\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrFSRemoteMountPoint_val, hr_map->_hrFSRemoteMountPoint, sizeof(hrFSRemoteMountPoint_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrFSRemoteMountPoint_val, strlen(hrFSRemoteMountPoint_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSType_oid))
                {
                    length = OID_LENGTH(hrFSType_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSType_oid, OID_LENGTH(hrFSType_oid))
                        == 0) {
                    int hrFSType_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrFSType\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrFSType_val = hr_map->_hrFSType;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrFSType_val, sizeof(hrFSType_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSAccess_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSAccess_oid))
                {
                    length = OID_LENGTH(hrFSAccess_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSAccess_oid, OID_LENGTH(hrFSAccess_oid))
                        == 0) {
                    int hrFSAccess_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrFSAccess\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrFSAccess_val = hr_map->_hrFSAccess;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrFSAccess_val, sizeof(hrFSAccess_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSBootable_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSBootable_oid))
                {
                    length = OID_LENGTH(hrFSBootable_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSBootable_oid, OID_LENGTH(hrFSBootable_oid))
                        == 0) {
                    int hrFSBootable_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrFSBootable\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrFSBootable_val = hr_map->_hrFSBootable;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrFSBootable_val, sizeof(hrFSBootable_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSStorageIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSStorageIndex_oid))
                {
                    length = OID_LENGTH(hrFSStorageIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSStorageIndex_oid, OID_LENGTH(hrFSStorageIndex_oid))
                        == 0) {
                    int hrFSStorageIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrFSStorageIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrFSStorageIndex_val = hr_map->_hrFSStorageIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrFSStorageIndex_val, sizeof(hrFSStorageIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSLastFullBackupDate_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSLastFullBackupDate_oid))
                {
                    length = OID_LENGTH(hrFSLastFullBackupDate_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSLastFullBackupDate_oid, OID_LENGTH(hrFSLastFullBackupDate_oid))
                        == 0) {
                    char hrFSLastFullBackupDate_val[64];

                    DEBUGMSGTL(("host_inferno", "oid hrFSLastFullBackupDate\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrFSLastFullBackupDate_val, hr_map->_hrFSLastFullBackupDate, sizeof(hrFSLastFullBackupDate_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrFSLastFullBackupDate_val, strlen(hrFSLastFullBackupDate_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrFSLastPartialBackupDate_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrFSLastPartialBackupDate_oid))
                {
                    length = OID_LENGTH(hrFSLastPartialBackupDate_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrFSLastPartialBackupDate_oid, OID_LENGTH(hrFSLastPartialBackupDate_oid))
                        == 0) {
                    char hrFSLastPartialBackupDate_val[64];

                    DEBUGMSGTL(("host_inferno", "oid hrFSLastPartialBackupDate\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrFSLastPartialBackupDate_val, hr_map->_hrFSLastPartialBackupDate, sizeof(hrFSLastPartialBackupDate_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrFSLastPartialBackupDate_val, strlen(hrFSLastPartialBackupDate_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWOSIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWOSIndex_oid))
                {
                    length = OID_LENGTH(hrSWOSIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWOSIndex_oid, OID_LENGTH(hrSWOSIndex_oid))
                        == 0) {
                    int hrSWOSIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWOSIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWOSIndex_val = hr_map->_hrSWOSIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWOSIndex_val, sizeof(hrSWOSIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunIndex_oid))
                {
                    length = OID_LENGTH(hrSWRunIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunIndex_oid, OID_LENGTH(hrSWRunIndex_oid))
                        == 0) {
                    int hrSWRunIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWRunIndex_val = hr_map->_hrSWRunIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWRunIndex_val, sizeof(hrSWRunIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunName_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunName_oid))
                {
                    length = OID_LENGTH(hrSWRunName_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunName_oid, OID_LENGTH(hrSWRunName_oid))
                        == 0) {
                    char hrSWRunName_val[64];

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunName\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrSWRunName_val, hr_map->_hrSWRunName, sizeof(hrSWRunName_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrSWRunName_val, strlen(hrSWRunName_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunID_oid))
                {
                    length = OID_LENGTH(hrSWRunID_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunID_oid, OID_LENGTH(hrSWRunID_oid))
                        == 0) {
                    char hrSWRunID_val[64];

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunID\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrSWRunID_val, hr_map->_hrSWRunID, sizeof(hrSWRunID_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrSWRunID_val, strlen(hrSWRunID_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunPath_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunPath_oid))
                {
                    length = OID_LENGTH(hrSWRunPath_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunPath_oid, OID_LENGTH(hrSWRunPath_oid))
                        == 0) {
                    char hrSWRunPath_val[128];

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunPath\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrSWRunPath_val, hr_map->_hrSWRunPath, sizeof(hrSWRunPath_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrSWRunPath_val, strlen(hrSWRunPath_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunParameters_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunParameters_oid))
                {
                    length = OID_LENGTH(hrSWRunParameters_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunParameters_oid, OID_LENGTH(hrSWRunParameters_oid))
                        == 0) {
                    char hrSWRunParameters_val[128];

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunParameters\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrSWRunParameters_val, hr_map->_hrSWRunParameters, sizeof(hrSWRunParameters_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrSWRunParameters_val, strlen(hrSWRunParameters_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}


int
hrSWRunType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunType_oid))
                {
                    length = OID_LENGTH(hrSWRunType_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunType_oid, OID_LENGTH(hrSWRunType_oid))
                        == 0) {
                    int hrSWRunType_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunType\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWRunType_val = hr_map->_hrSWRunType;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWRunType_val, sizeof(hrSWRunType_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunStatus_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunStatus_oid))
                {
                    length = OID_LENGTH(hrSWRunStatus_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunStatus_oid, OID_LENGTH(hrSWRunStatus_oid))
                        == 0) {
                    int hrSWRunStatus_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunStatus\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWRunStatus_val = hr_map->_hrSWRunStatus;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWRunStatus_val, sizeof(hrSWRunStatus_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunPerfCPU_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunPerfCPU_oid))
                {
                    length = OID_LENGTH(hrSWRunPerfCPU_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunPerfCPU_oid, OID_LENGTH(hrSWRunPerfCPU_oid))
                        == 0) {
                    int hrSWRunPerfCPU_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunPerfCPU\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWRunPerfCPU_val = hr_map->_hrSWRunPerfCPU;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWRunPerfCPU_val, sizeof(hrSWRunPerfCPU_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWRunPerfMem_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWRunPerfMem_oid))
                {
                    length = OID_LENGTH(hrSWRunPerfMem_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWRunPerfMem_oid, OID_LENGTH(hrSWRunPerfMem_oid))
                        == 0) {
                    int hrSWRunPerfMem_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWRunPerfMem\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWRunPerfMem_val = hr_map->_hrSWRunPerfMem;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWRunPerfMem_val, sizeof(hrSWRunPerfMem_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWInstalledIndex_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWInstalledIndex_oid))
                {
                    length = OID_LENGTH(hrSWInstalledIndex_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWInstalledIndex_oid, OID_LENGTH(hrSWInstalledIndex_oid))
                        == 0) {
                    int hrSWInstalledIndex_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWInstalledIndex\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWInstalledIndex_val = hr_map->_hrSWInstalledIndex;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWInstalledIndex_val, sizeof(hrSWInstalledIndex_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWInstalledName_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWInstalledName_oid))
                {
                    length = OID_LENGTH(hrSWInstalledName_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWInstalledName_oid, OID_LENGTH(hrSWInstalledName_oid))
                        == 0) {
                    char hrSWInstalledName_val[64];

                    DEBUGMSGTL(("host_inferno", "oid hrSWInstalledName\n"));
                    flock(snmp_lock, LOCK_EX);
                    memcpy(hrSWInstalledName_val, hr_map->_hrSWInstalledName, sizeof(hrSWInstalledName_val));
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_OCTET_STR, (u_char *) hrSWInstalledName_val, strlen(hrSWInstalledName_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWInstalledID_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWInstalledID_oid))
                {
                    length = OID_LENGTH(hrSWInstalledID_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWInstalledID_oid, OID_LENGTH(hrSWInstalledID_oid))
                        == 0) {
                    int hrSWInstalledID_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWInstalledID\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWInstalledID_val = hr_map->_hrSWInstalledID;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWInstalledID_val, sizeof(hrSWInstalledID_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWInstalledType_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWInstalledType_oid))
                {
                    length = OID_LENGTH(hrSWInstalledType_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWInstalledType_oid, OID_LENGTH(hrSWInstalledType_oid))
                        == 0) {
                    int hrSWInstalledType_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWInstalledType\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWInstalledType_val = hr_map->_hrSWInstalledType;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWInstalledType_val, sizeof(hrSWInstalledType_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}

int
hrSWInstalledDate_handler(netsnmp_mib_handler *handler,
        netsnmp_handler_registration *reginfo,
        netsnmp_agent_request_info *reqinfo,
        netsnmp_request_info *requests)
{
    int length;
    DEBUGMSGTL(("host_inferno", "in %s\n", __func__));

    while (requests) {
        netsnmp_variable_list *var = requests->requestvb;
        switch (reqinfo->mode) {
            case MODE_GET:
                length = var->name_length;
                if (length > OID_LENGTH(hrSWInstalledDate_oid))
                {
                    length = OID_LENGTH(hrSWInstalledDate_oid);
                }
                if (netsnmp_oid_equals(var->name, length, hrSWInstalledDate_oid, OID_LENGTH(hrSWInstalledDate_oid))
                        == 0) {
                    int hrSWInstalledDate_val = 0;

                    DEBUGMSGTL(("host_inferno", "oid hrSWInstalledDate\n"));
                    flock(snmp_lock, LOCK_EX);
                    hrSWInstalledDate_val = hr_map->_hrSWInstalledDate;
                    flock(snmp_lock, LOCK_UN);

                    snmp_set_var_typed_value(var, ASN_INTEGER, (u_char *) &hrSWInstalledDate_val, sizeof(hrSWInstalledDate_val));
                    return SNMP_ERR_NOERROR;
                }
                break;

            case MODE_GETNEXT:
                break;

            default:
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);
                break;
        }

        requests = requests->next;
    }
    return SNMP_ERR_NOERROR;
}
