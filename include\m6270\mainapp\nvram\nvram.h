
/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file nvram.h
 * @addtogroup nvram
 * @{
 * @brief Data storage frame layer
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */


#ifndef __NVRAM_H 
#define __NVRAM_H

#include "pol/pol_types.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

typedef enum
{
    VTYPE_INT = 0,
    VTYPE_UINT,
    VTYPE_STRING,
    VTYPE_STRUCT
}VARIABLE_TYPE_E;


typedef struct 
{
	uint32_t id; 
	VARIABLE_TYPE_E tp;
	void *value;
	uint32_t value_size; 
	uint32_t is_static; 
} NVRAM_TABLE_S, *NVRAM_TABLE_P;

typedef enum
{
	S_NONE = 0,
	S_UPDATE,
	S_FILE_BUSY,
	S_FILE_CORRUPT,
	S_FACTORY_RESET
}NVRAM_STATUS_E;

typedef enum
{
	N_SUCCESS = 0,
	N_ERROR = -1,
	N_BUSY = -2 , 
	N_CORRUPT = -3
}NVRAM_RETCODE_E;

typedef enum
{
	A_RECOVER = 0,
	A_INIT,
	A_DESTROY,
	A_PARTITION_MOUNT,
	A_PARTITION_UNMOUNT,
    A_CHECK,
    A_REPAIR
}NVRAM_ASSIST_E;


/**
 * @brief nvram module init 
 *
 * @param tables_point[in] Entry of nvram table
 * @param tables_number[in] The number of elements in the nvram table
 *
 * @return 0 on success , -1 on error 
 */
int32_t pi_nvram_init(void *tables_point , uint32_t tables_number);


/**
 * @brief nvram module destroy
 *
 * @return 0 on success , -1 on error 
 */
int32_t pi_nvram_destroy(void);



/**
 * @brief nvram module modify the specified id property value 
 *
 * @param[in] id identity
 * @param[in] tp the element type , see [VARIABLE_TYPE_E] enum
 * @param[in] value a pointer to the first address of the element
 * @param[in] value_size value the number of bytes
 * @param[in] is_block Whether it's blocking mode , 0 is non-blocking(Asynchronous) mode;1 is blocking(Sync) mode
 * @param[in] notify_callback Asynchronous notification callback function.Only valid in non-blocking(asynchronous) mode.\n
 *                            When it's not NULL,the function will be called by a background thread. \n
 *                            The result of data writing will be passwd into the callback function through parameters( \n
 *                            the first parameter will be the id value,and the second paramter will be the result value); \n
 *                            when it't NULL,the caller will not be notified of the result of data writing
 *
 * @return  1.In non-blocking(asynchronous) mode,inserting the linked list successfully returns 0,  \n
 *              otherwise it returns -1
 *          2.(synchronous) mode operation successfully returns 0,and fails to return negative \n
 *              (-1 generic error code , -2 only supports one writer at a time , -3 database corruption) 
 *
 */
int32_t pi_nvram_set( 
		uint32_t id , 
		VARIABLE_TYPE_E tp , 
		void *value , 
		uint32_t value_size , 
		int32_t is_block ,
		void (*notify_callback)(uint32_t id,int32_t result)
);



/**
 * @brief nvram module gets the property value of the specified id 
 *
 * @param id[in] identity
 * @param tp[in] the element type , see [VARIABLE_TYPE_E] enum
 * @param value[in] a pointer to the first address of the element
 * @param value_size[in] value the number of bytes
 *
 * @return 0 on success , negative number on error 
 * @retval -1 generic error code 
 * @retval -2 only supports one write at a time
 * @retval -3 database corruption 
 *
 */
int32_t pi_nvram_get(uint32_t id , VARIABLE_TYPE_E tp , void *value , uint32_t value_size);



/**
 * @brief nvram module recover factory , some property values  
 *        revert to the default
 *
 * @return 0 on success , negative number on error 
 * @retval -1 generic error code 
 * @retval -2 only supports one write at a time
 * @retval -3 database corruption 
 */
int32_t pi_nvram_recover_factory(void);

PT_END_DECLS

#endif //__NVRAM_H

/**
 * @}
 */
