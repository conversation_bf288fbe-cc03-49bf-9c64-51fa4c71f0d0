/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netifctl.c
 * @addtogroup net
 * @{
 * @addtogroup netifctl
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network Interface Control API, eg. "eth0", "mlan0"...
 */
#include "nettypes.h"
#include "netsock.h"

static const char* s_iface_list[IFACE_ID_NUM] = {
    IFACE_ETH,
#if CONFIG_NET_WIFI
    IFACE_STA,
    IFACE_WFD,
#endif
};

/**
 * @brief       Check if the flags of the network interface is activated.
 * @param[in]   ifname  : The network interface name.
 * @return      Check result
 * @retval      == -1   : error\n
 *              == 0    : no\n
 *              == 1    : yes
 * <AUTHOR>
 * @date        2023-9-16
 */
static inline int32_t ifctl_check_status(const char* ifname, uint32_t flag, const char* caller)
{
    PI_SOCKET_T     sockfd;
    struct ifreq    ifr;
    int32_t         ret;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname), NET_INFO, caller, -1);
    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);

    RETURN_VAL_SHOW_CALLER_IF((sockfd = netsock_create_custom(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET, NET_WARN, caller, -1);
    if ( ioctl(sockfd, SIOCGIFFLAGS, &ifr) == 0 )
    {
        ret = (int32_t)(!!(ifr.ifr_flags & flag));
    }
    else
    {
        ret = -1;
    }
    pi_closesock(sockfd);

    return ret;
}

uint32_t ifctl_is_running(const char* ifname, const char* caller)
{
    return ( (ifctl_check_status(ifname, IFF_RUNNING, caller) <= 0) ? 0 : 1 );
}

uint32_t ifctl_is_active(const char* ifname, const char* caller)
{
    return ( (ifctl_check_status(ifname, IFF_UP, caller) <= 0) ? 0 : 1 );
}

uint32_t ifctl_is_valid(const char* ifname, const char* caller)
{
    return ( (ifctl_check_status(ifname, 0, caller) < 0) ? 0 : 1 );
}

int32_t ifctl_switch(const char* ifname, IFACE_SWITCH_E on, const char* caller)
{
    PI_SOCKET_T     sockfd;
    struct ifreq    ifr;
    int32_t         ret;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname), NET_WARN, caller, -1);
    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);

    RETURN_VAL_SHOW_CALLER_IF((sockfd = netsock_create_custom(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET, NET_WARN, caller, -1);
    do
    {
        ret = ioctl(sockfd, SIOCGIFFLAGS, &ifr);
        if ( ret != 0 )
        {
            NET_WARN("[%s] ioctl %s SIOCGIFFLAGS failed: %d<%s>", caller, ifname, errno, strerror(errno));
            break;
        }

        if ( on == IFACE_SWITCH_OFF )
        {
            if ( strcmp(ifname, IFACE_ETH) == 0 )
            {
                ifr.ifr_flags &= (~(IFF_UP | IFF_PROMISC));
            }
            else
            {
                ifr.ifr_flags &= (~IFF_UP);
            }
        }
        else
        {
            if ( strcmp(ifname, IFACE_ETH) == 0 )
            {
                ifr.ifr_flags |= (IFF_UP | IFF_PROMISC);
            }
            else
            {
                ifr.ifr_flags |= IFF_UP;
            }
        }

        ret = ioctl(sockfd, SIOCSIFFLAGS, &ifr);
        if ( ret != 0 )
        {
            NET_WARN("[%s] ioctl %s SIOCSIFFLAGS failed: %d<%s>", caller, ifname, errno, strerror(errno));
            break;
        }
    }
    while ( 0 );
    pi_closesock(sockfd);

    return ret;
}

int32_t ifctl_switch_safe(const char* ifname, IFACE_SWITCH_E on, const char* caller)
{
    const char* operat = (on ? "on" : "off");
    int32_t     expect = (on ? 1 : 0);
    int32_t     retries = 0;
    int32_t     ret;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname), NET_NONE, caller, 0);

    while ( (ifctl_is_active(ifname, caller) != expect) && (++retries < 10) )
    {
        NET_DEBUG("[%s] turn %s %s, %d times.", caller, operat, ifname, retries);
        ifctl_switch(ifname, on, caller);
        pi_msleep(100);
    }
    RETURN_VAL_IF(retries == 0, NET_NONE, 0);

    if ( retries < 10 )
    {
        NET_INFO("[%s] turn %s %s success", caller, operat, ifname);
        ret = 0;
    }
    else
    {
        NET_WARN("[%s] turn %s %s failed", caller, operat, ifname);
        ret = -1;
    }
    return ret;
}

int32_t ifctl_set_speed(const char* ifname, IFACE_SPEED_E mode, const char* caller)
{
    PI_SOCKET_T         sockfd;
    struct ethtool_cmd  edata;
    struct ifreq        ifr;
    int32_t             ret;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname) || strcmp(ifname, IFACE_ETH) != 0, NET_WARN, caller, -1); /* only support to set eth0 speed mode. */

    sockfd = netsock_create_custom(AF_INET, SOCK_DGRAM, 0);
    RETURN_VAL_SHOW_CALLER_IF(sockfd == INVALID_SOCKET, NET_WARN, caller, -1);

    memset(&edata, 0, sizeof(edata));
    edata.cmd = ETHTOOL_GSET;

    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
    ifr.ifr_data = (void *)&edata;

    NET_DEBUG("[%s] set %s speed mode(%d)", caller, ifname, mode);
    do
    {
        /* Get current settings */
        ret = ioctl(sockfd, SIOCETHTOOL, &ifr);
        if ( ret != 0 )
        {
            NET_WARN("[%s] ioctl %s SIOCETHTOOL(%d) failed: %d<%s>", caller, ifname, mode, errno, strerror(errno));
            break;
        }

        switch ( mode )
        {
        case IFACE_SPEED_1000: edata.speed = SPEED_1000; edata.autoneg = AUTONEG_DISABLE; break;
        case IFACE_SPEED_100:  edata.speed = SPEED_100;  edata.autoneg = AUTONEG_DISABLE; break;
        case IFACE_SPEED_AUTO:                           edata.autoneg = AUTONEG_ENABLE;  break;
        default: NET_WARN("[%s] set %s invaled speed mode(%d)", caller, ifname, mode);    break;
        }

        if ( edata.duplex != 1 )
        {
            NET_DEBUG("[%s] set %s duplex %u to 1", caller, ifname, edata.duplex);
            edata.duplex = 1;
        }
        edata.cmd = ETHTOOL_SSET;

        /* Set the new settings */
        ret = ioctl(sockfd, SIOCETHTOOL, &ifr);
        if ( ret != 0 )
        {
            NET_WARN("[%s] ioctl %s SIOCETHTOOL(%d) failed: %d<%s>", caller, ifname, mode, errno, strerror(errno));
            break;
        }
    }
    while ( 0 );

    pi_closesock(sockfd);
    return ret;
}

int32_t ifctl_set_mac(const char* ifname, const uint8_t* mac, size_t len, const char* caller)
{
    PI_SOCKET_T     sockfd;
    struct ifreq    ifr;
    int16_t         up_flag;
    int32_t         ret;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname) || strcmp(ifname, IFACE_ETH) != 0, NET_WARN, caller, -1); /* only support to set eth0 MAC address. */
    RETURN_VAL_SHOW_CALLER_IF(mac == NULL || len < 6, NET_WARN, caller, -1);

    RETURN_VAL_SHOW_CALLER_IF((sockfd = netsock_create_custom(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET, NET_WARN, caller, -1);

    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
    do
    {
        /* get the original interface flags. */
        ret = ioctl(sockfd, SIOCGIFFLAGS, &ifr);
        if ( ret != 0 )
        {
            NET_WARN("[%s] ioctl %s SIOCGIFFLAGS failed: %d<%s>", caller, ifname, errno, strerror(errno));
            break;
        }

        NET_DEBUG("[%s] current ifr_flags(0x%04X)", caller, ifr.ifr_flags);
        up_flag = (ifr.ifr_flags & IFF_UP);

        if ( up_flag )
        {
            /* turn off this interface before setting the MAC address. */
            ifr.ifr_flags &= (~IFF_UP);
            ret = ioctl(sockfd, SIOCSIFFLAGS, &ifr);
            if ( ret != 0 )
            {
                NET_WARN("[%s] ioctl %s SIOCSIFFLAGS failed: %d<%s>", caller, ifname, errno, strerror(errno));
                break;
            }
        }

        /* setting the MAC address. */
        ifr.ifr_addr.sa_family = ARPHRD_ETHER;
        memcpy(ifr.ifr_hwaddr.sa_data, mac, len);
        if ( ioctl(sockfd, SIOCSIFHWADDR, &ifr) )
        {
            NET_WARN("[%s] ioctl %s SIOCSIFHWADDR failed: %d<%s>", caller, ifname, errno, strerror(errno));
            /* no break, still need to turn on this interface. */
        }

        if ( up_flag )
        {
            /* turn on this interface after setting the MAC address. */
            ifr.ifr_flags |= IFF_UP;
            ret = ioctl(sockfd, SIOCSIFFLAGS, &ifr);
            if ( ret != 0 )
            {
                NET_WARN("[%s] ioctl %s SIOCSIFFLAGS failed: %d<%s>", caller, ifname, errno, strerror(errno));
                break;
            }
        }
    }
    while (0);
    pi_closesock(sockfd);

    return ret;
}

int32_t ifctl_get_mac(const char* ifname, uint8_t* mac, size_t len, const char* caller)
{
    PI_SOCKET_T     sockfd;
    struct ifreq    ifr;
    int32_t         ret;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname), NET_WARN, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(mac == NULL || len < 6, NET_WARN, caller, -1);

    RETURN_VAL_SHOW_CALLER_IF((sockfd = netsock_create_custom(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET, NET_WARN, caller, -1);

    memset(&ifr, 0, sizeof(ifr));
    snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "%s", ifname);
    ret = ioctl(sockfd, SIOCGIFHWADDR, &ifr);
    if ( ret != 0 )
    {
        NET_WARN("[%s] ioctl %s SIOCGIFHWADDR failed: %d<%s>", caller, ifname, errno, strerror(errno));
    }
    else
    {
        memcpy(mac, ifr.ifr_hwaddr.sa_data, 6);
    }
    pi_closesock(sockfd);

    return ret;
}

IFACE_ID_E ifctl_get_id_by_gtwy(uint32_t gateway, const char* caller)
{
    FILE*       stream;
    char        line[256];
    char        name[IF_NAMESIZE];
    uint32_t    dst;
    uint32_t    gtw;
    int32_t     flag;
    int32_t     ret;

    RETURN_VAL_SHOW_CALLER_IF((stream = fopen("/proc/net/route", "r")) == NULL, NET_WARN, caller, IFACE_ID_ANY);

    fgets(line, sizeof(line), stream); /* 跳过标签行 */
    memset(line, 0, sizeof(line));
    memset(name, 0, sizeof(name));

    while ( fgets(line, sizeof(line), stream) )
    {
        ret = sscanf(line, "%15s%*[\t]%x%*[\t]%x%*[\t]%d", name, &dst, &gtw, &flag);
        if ( ret == 4 && gtw == gateway && (flag & RTF_UP) && (flag & RTF_GATEWAY) )
        {
            break;
        }
        memset(line, 0, sizeof(line));
        memset(name, 0, sizeof(name));
    }
    fclose(stream);

    return ifctl_get_id_by_name(name, caller);
}

IFACE_ID_E ifctl_get_id_by_name(const char* ifname, const char* caller)
{
    IFACE_ID_E  ifid;

    RETURN_VAL_SHOW_CALLER_IF(STRING_IS_EMPTY(ifname), NET_DEBUG, caller, IFACE_ID_ANY);

    for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        if ( strcmp(ifname, s_iface_list[ifid]) == 0 )
        {
            return ifid;
        }
    }

    return IFACE_ID_ANY;
}

const char* ifctl_get_name_by_id(IFACE_ID_E ifid, const char* caller)
{
    return ( (ifid >= IFACE_ID_ETH && ifid < IFACE_ID_NUM) ? s_iface_list[ifid] : IFACE_ANY );
}
/**
 *@}
 */
