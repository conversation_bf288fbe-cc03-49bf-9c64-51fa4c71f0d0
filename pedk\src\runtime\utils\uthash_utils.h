/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file uthas_utils.h
 * @addtogroup utils
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief uthash utils
 */
#ifndef _UTHASH_UTILS_H_
#define _UTHASH_UTILS_H_

#include "basic/hash/uthash.h"
#include "basic/config.h"
#include "runtime/runtime.h"

/**
 * @brief   hash add
 * @param[in] *rt :pesf runtime run context
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
uint32_t hash_add(PeSFRunTime* rt);

/**
 * @brief   js object save
 * @param[in] rtid :app rtid
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
PeSFRunTime* hash_find(uint32_t rtid);

/**
 * @brief   hash del
 * @param[in] rtid :app rtid
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */

int32_t hash_del(uint32_t rtid);

/**
 * @brief   hash clear
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */

int32_t hash_clear();

/**
 * @brief   hash iter
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
PeSFRunTime* hash_iter();

#define PESF_ITER(prt) for (prt = hash_iter(); prt; prt = hash_iter())
#endif // _UTHASH_UTILS_H_

/**
 * @}
 */

