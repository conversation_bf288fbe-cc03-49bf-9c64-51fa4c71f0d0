/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file data_distribution.h
 * @addtogroup panel_dc
 * @{
 * @brief inter-board communication,distributing the data of the peer
 *         to the corresponding module 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef _DATA_DISTRIBUTION_H
#define _DATA_DISTRIBUTION_H

/**
 * @brief panel_dc command type define
 */
enum CMD_TYPE
{
    INFORMATION = 0,
    SETTING = 1,
    JOB = 2 , 
    OPERATE = 3,
    STATUS = 4 , 
    RESOURCE = 5 , 
    UPGRADE = 6 , 
    OTHER = 7
};
    
/**
 * @brief panel_dc command msg type define
 */
enum MSG_TYPE
{
    REQUEST = 0 , 
    SUCCESSFUL_RESPONSE = 1 ,
    NOTIFICATION_RESPONSE = 2 ,
    FAIL_RESPONSE = 3
};

/**
 * @brief the data generated by the parse callback is distributed
 *         to the appropriate module 
 *
 * @param data[in] callbacks to incoming data
 * @param len[in] the length of the data
 */
void data_distribution(void *data , unsigned int len);

#endif //_DATA_DISTRIBUTION_H

/**                                                                                                                                                                  
 * @}
 */
