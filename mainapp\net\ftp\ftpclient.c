/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ftpclient.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief File-Transfer-Protocol client
 */

#undef _FILE_OFFSET_BITS
#define _FILE_OFFSET_BITS 64

#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "netmisc.h"

#define FTP_CTRL_PORT   21
#define FTP_DATA_PORT   20
#define FTP_TIMEOUT     5

typedef enum
{
    FTP_INVALID = -1,       ///< 无效状态
    FTP_SUCCESS = 0,
    FTP_ESYS,				///< 系统故障（分不出内存等）
    FTP_ECONN,				///< 连接失败
    FTP_ETRAN,				///< 传输失败
    FTP_EUSER,				///< 用户名错误
    FTP_EPASS,				///< 密码错误
    FTP_EFILE_ACCESS,		///< 文件访问失败
    FTP_ESERVPATH,			///< 服务器路径错误
    FTP_ESERVER,			///< 服务器响应了其它错误信息
}
FTP_STATUS_E;

typedef struct ftp_client
{
    char        server_addr[16];    ///< remote ftp server IPv4 address
    char        server_path[32];    ///< remote ftp server file path
    char        login_name[64];     ///< login username
    char        login_pswd[32];     ///< login password
    char        file_name[256];     ///< file name
    uint16_t    ctrl_port;          ///< port of control server
    uint16_t    data_port;          ///< port of data server
    QIO_S*      ctrl_qio;           ///< socket of control server
    QIO_S*      data_qio;           ///< socket of data server
}
FTP_CLI_S;

typedef struct ftp_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     cli_tid;
    FTP_CLI_S*      cli_obj;
}
FTP_CTX_S;

static FTP_CTX_S* s_ftp_ctx = NULL;

static FTP_CLI_S* ftp_client_create(FTP_ADDRINFO_S* param)
{
    FTP_CLI_S* pcli;

    RETURN_VAL_IF(param == NULL, NET_WARN, NULL);

    pcli = (FTP_CLI_S *)pi_zalloc(sizeof(FTP_CLI_S));
    RETURN_VAL_IF(pcli == NULL, NET_WARN, NULL);

    snprintf(pcli->server_addr, sizeof(pcli->server_addr), "%s", param->server_addr);
    snprintf(pcli->server_path, sizeof(pcli->server_path), "%s", param->server_path);
    snprintf(pcli->login_name,  sizeof(pcli->login_name),  "%s", param->login_name);
    snprintf(pcli->login_pswd,  sizeof(pcli->login_pswd),  "%s", param->login_pswd);
    pcli->ctrl_port = param->server_port;
    pcli->data_port = FTP_DATA_PORT;

    if ( pcli->server_path[0] != '\0' && pcli->server_path[strlen(pcli->server_path) - 1] == _PTC_ )
    {
        pcli->server_path[strlen(pcli->server_path) - 1] = '\0';
    }

    return pcli;
}

static void ftp_client_destroy(FTP_CLI_S* pcli)
{
    if ( pcli != NULL )
    {
        if ( pcli->ctrl_qio != NULL )
        {
            QIO_CLOSE(pcli->ctrl_qio);
        }
        if ( pcli->data_qio != NULL )
        {
            QIO_CLOSE(pcli->data_qio);
        }
        pi_free(pcli);
    }
}

static int32_t ftp_client_recv(FTP_CLI_S* pcli, char* buf, size_t nbuf)
{
    int32_t ret;
    int32_t read_cnt = 0;
    int32_t read_timeout = 1000;//1000毫秒
    time_t time_out = time(NULL) + 30;

    memset(buf, 0, nbuf);
    do
    {
        ret = QIO_READABLE(pcli->ctrl_qio, FTP_TIMEOUT, 0);
        if ( ret > 0 )
        {
            read_cnt = QIO_READ(pcli->ctrl_qio, buf, nbuf);
            if ( read_cnt < 0 )
            {
                return 0;
            }
            else if( read_cnt == 0 )
            {
                RETURN_VAL_IF(read_timeout <= 0, NET_WARN, 0);
                pi_msleep(10);
                read_timeout -= 10;
            }
            NET_DEBUG("recv(%s) ret(%d) read_cnt(%d)", buf, ret, read_cnt);
            time_out = time(NULL) + 30;
        }
        else
        {
            if ( ret < 0 )
            {
                return 0;
            }
            else
            {
                RETURN_VAL_IF(time(NULL) > time_out, NET_WARN, 0);
            }
        }
        NET_DEBUG("read_cnt(%d)", read_cnt);
    }
    while( read_cnt <= 0 );

    return read_cnt;
}

static FTP_STATUS_E ftp_client_check_response(FTP_CLI_S* pcli, int32_t* expected, int32_t* rcode)
{
    char    buf[1024];

    RETURN_VAL_IF(ftp_client_recv(pcli, buf, sizeof(buf)) <= 0, NET_WARN, FTP_ECONN);

    RETURN_VAL_IF(expected == NULL || rcode == NULL, NET_TRACE, FTP_SUCCESS);

    *rcode = atoi(buf);
    while ( *expected != 0 )
    {
        if ( *expected == *rcode || *expected == -1 )
        {
            return FTP_SUCCESS;
        }
        expected++;
    }

    NET_DEBUG("rcode(%d) not expected", *rcode);
    return FTP_ESERVER;
}

static FTP_STATUS_E ftp_client_send_command(FTP_CLI_S* pcli, const char* command, const char* param)
{
    char    buf[1024];

    if ( STRING_NO_EMPTY(param) )
    {
        snprintf(buf, sizeof(buf), "%s %s\r\n", command, param);
    }
    else
    {
        snprintf(buf, sizeof(buf), "%s\r\n", command);
    }
    NET_DEBUG("send(%s)", buf);

    RETURN_VAL_IF(QIO_WRITEABLE(pcli->ctrl_qio, FTP_TIMEOUT, 0) <= 0, NET_WARN, FTP_ECONN);
    RETURN_VAL_IF(QIO_WRITE(pcli->ctrl_qio, buf, strlen(buf))   <= 0, NET_WARN, FTP_ECONN);

    return FTP_SUCCESS;
}

static FTP_STATUS_E ftp_client_login(FTP_CLI_S* pcli)
{
    FTP_STATUS_E    status;
    PI_SOCKET_T     sockfd;
    int32_t         pass_exp[] = { 230, 202, 332, 500, 530, 0 };
    int32_t         user_exp[] = { 331, 230, 332, 530, 0 };
    int32_t         acct_exp[] = { 230, 202, 0 };
    int32_t         conn_exp[] = { 220, 0 };
    int32_t         rcode;
    int32_t         try_times = 0;

#if CONFIG_NET_WIFI
    do
    {
        sockfd = netsock_create_by_url(pcli->server_addr, pcli->ctrl_port, FTP_TIMEOUT, 0);
        if ( INVALID_SOCKET == sockfd )
        {
            try_times = change_card_priority(s_ftp_ctx->net_ctx, try_times);
        }
    }
    while ( INVALID_SOCKET == sockfd && try_times != 2 );
#else
    sockfd = netsock_create_by_url(pcli->server_addr, pcli->ctrl_port, FTP_TIMEOUT, 0);
#endif
    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, FTP_ECONN);

    pcli->ctrl_qio = qio_tcp_create(sockfd);
    if ( pcli->ctrl_qio == NULL )
    {
        NET_WARN("create control QIO failed");
        pi_closesock(sockfd);
        return FTP_ECONN;
    }
    NET_DEBUG("connected to %s:%u", pcli->server_addr, pcli->ctrl_port);

    status = ftp_client_check_response(pcli, conn_exp, &rcode);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_send_command(pcli, "USER", pcli->login_name);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_check_response(pcli, user_exp, &rcode);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    if ( rcode == 331 || rcode == 332 )
    {
        NET_DEBUG("rcode(%d) username OK, need password", rcode);

        status = ftp_client_send_command(pcli, "PASS", pcli->login_pswd);
        RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

        status = ftp_client_check_response(pcli, pass_exp, &rcode);
        RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

        if ( rcode == 332 )
        {
            status = ftp_client_send_command(pcli, "ACCT", "noaccount");
            RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

            status = ftp_client_check_response(pcli, acct_exp, &rcode);
            RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);
        }
        NET_DEBUG("rcode(%d) password OK", rcode);
    }
    RETURN_VAL_IF(rcode >= 500, NET_WARN, (530 == rcode) ? FTP_EPASS : FTP_ESERVER);

    NET_DEBUG("user(%s) pass(%s) login status(%d)", pcli->login_name, pcli->login_pswd, status);
    return status;
}

static FTP_STATUS_E ftp_client_setup_passively(FTP_CLI_S* pcli)
{
    FTP_STATUS_E    status;
    PI_SOCKET_T     sockfd;
    int32_t         type_exp1[] = { 200, 226, 331, 0 };
    int32_t         type_exp0[] = { 200, 331, 0 };
    char            ipstr[IPV4_ADDR_LEN];
    char            buf[1024];
    char*           ptr;
    int32_t         rcode;
    uint16_t        port;
    uint8_t         p1, p2;
    uint8_t         ip[4];
    int32_t         try_times = 0;

    status = ftp_client_send_command(pcli, "TYPE", "I");
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_check_response(pcli, type_exp0, &rcode);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_send_command(pcli, "PASV", NULL);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    RETURN_VAL_IF(ftp_client_recv(pcli, buf, sizeof(buf)) <= 0, NET_WARN, FTP_ECONN);
    RETURN_VAL_IF(strstr(buf, "227") == NULL, NET_WARN, FTP_ECONN);

    for ( ptr = buf + 3; *ptr; ptr++ )
    {
        if ( *ptr >= '0' && *ptr <= '9' )
        {
            break;
        }
    }
    RETURN_VAL_IF(*ptr == '\0', NET_WARN, FTP_ESERVER);
    NET_DEBUG("ptr(%s)", ptr);

    for ( int32_t i = 0; i < 4; ++i )
    {
        ip[i] = (uint8_t)strtol(ptr, &ptr, 10);
        RETURN_VAL_IF(ptr == NULL, NET_WARN, FTP_ESERVER);
        ptr++;
    }
    snprintf(ipstr, sizeof(ipstr), "%d.%d.%d.%d", ip[0], ip[1], ip[2], ip[3]);

    p1 = (uint8_t)strtol(ptr, &ptr, 10);
    RETURN_VAL_IF(ptr == NULL, NET_WARN, FTP_ESERVER);
    ptr++;

    p2 = (uint8_t)strtol(ptr, &ptr, 10);
    RETURN_VAL_IF(ptr == NULL, NET_WARN, FTP_ESERVER);

    port = ((uint16_t)p1 << 8) | p2;
    if ( port != pcli->data_port )
    {
        NET_DEBUG("data port change %u - %u\n", pcli->data_port, port);
        pcli->data_port = port;
    }

#if CONFIG_NET_WIFI
    do
    {
        sockfd = netsock_create_by_url(ipstr, pcli->data_port, FTP_TIMEOUT, 0);
        if ( INVALID_SOCKET == sockfd )
        {
            try_times = change_card_priority(s_ftp_ctx->net_ctx, try_times);
        }
    }
    while ( INVALID_SOCKET == sockfd && try_times != 2 );
#else
    sockfd = netsock_create_by_url(ipstr, pcli->data_port, FTP_TIMEOUT, 0);
#endif
    RETURN_VAL_IF(sockfd == INVALID_SOCKET, NET_WARN, FTP_ECONN);

    pcli->data_qio = qio_tcp_create(sockfd);
    if ( pcli->data_qio == NULL)
    {
        NET_WARN("connect to data server(%s : %u) PASV failed", ipstr, pcli->data_port);
        pi_closesock(sockfd);
        return FTP_ECONN;
    }

    status = ftp_client_send_command(pcli, "TYPE", "I");
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_check_response(pcli, type_exp1, &rcode);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    return status;
}

static FTP_STATUS_E ftp_client_logout(FTP_CLI_S* pcli)
{
    FTP_STATUS_E    status;
    int32_t         quit_exp[] = { 221, 0 };
    int32_t         rcode;

    status = ftp_client_send_command(pcli, "QUIT", NULL);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_check_response(pcli, quit_exp, &rcode);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    return status;
}

static FTP_STATUS_E ftp_client_start_scan(FTP_ADDRINFO_S* param)
{
    FTP_CLI_S*      pcli = s_ftp_ctx->cli_obj;
    FTP_STATUS_E    status;

    RETURN_VAL_IF(param == NULL || param->server_name[0] == '\0', NET_WARN, FTP_INVALID);
    RETURN_VAL_IF(pcli != NULL, NET_WARN, FTP_ESYS);

    pcli = ftp_client_create(param);
    RETURN_VAL_IF(pcli == NULL, NET_WARN, FTP_ESYS);

    NET_DEBUG("server(%s) path(%s) port(%u) user(%s) pass(%s)", pcli->server_addr, pcli->server_path, pcli->ctrl_port, pcli->login_name, pcli->login_pswd);
    status = ftp_client_login(pcli);
    if ( status != FTP_SUCCESS )
    {
        NET_WARN("login failed(%d)", status);
        ftp_client_destroy(pcli);
        return status;
    }
    s_ftp_ctx->cli_obj = pcli;

    return FTP_SUCCESS;
}

static FTP_STATUS_E ftp_client_put_ready(char* file)
{
    FTP_CLI_S*      pcli = s_ftp_ctx->cli_obj;
    FTP_STATUS_E    status;
    int32_t         cwd_exp[]  = { 250, 200, 0 };
    int32_t         stor_exp[] = { 150, 0 };
    int32_t         rcode;
    char            remote_path[FILE_PATH_MAX];
    char            sub_dir[FILE_PATH_MAX]; /* 子目录名 */
    char*           p1;                     /* 子目录首指针 */
    char*           p2;                     /* 子目录尾指针 */

    RETURN_VAL_IF(pcli == NULL || pcli->ctrl_qio == NULL || STRING_IS_EMPTY(file), NET_WARN, FTP_ESYS);

    status = ftp_client_setup_passively(pcli);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    snprintf(pcli->file_name, sizeof(pcli->file_name), "%s", basename(file));

    status = ftp_client_send_command(pcli, "CWD", "/");
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_check_response(pcli, NULL, NULL);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    p2 = pcli->server_path;
    do
    {
        for ( p1 = p2; *p1 == _PTC_; ++p1 )
        {
            /* p1找到下一级目录的起始位置 */
        }
        BREAK_IF(*p1 == '\0', NET_NONE);

        p2 = strchr(p1, _PTC_);
        if ( p2 != NULL )
        {
            snprintf(sub_dir, (size_t)(p2 - p1 + 1), "%s", p1);
        }
        else
        {
            snprintf(sub_dir, sizeof(sub_dir), "%s", p1);
        }

        ftp_client_send_command(pcli, "MKD", sub_dir);
        ftp_client_check_response(pcli, NULL, NULL);

        status = ftp_client_send_command(pcli, "CWD", sub_dir);
        RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

        status = ftp_client_check_response(pcli, cwd_exp, &rcode);
        RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);
    }
    while ( p2 != NULL );

    snprintf(remote_path, sizeof(remote_path), "%s/%s", pcli->server_path, pcli->file_name);
    NET_DEBUG("upload '%s' -> '%s'", pcli->file_name, remote_path);

    status = ftp_client_send_command(pcli, "STOR", remote_path);
    RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);

    status = ftp_client_check_response(pcli, stor_exp, &rcode);
    NET_DEBUG("put_ready %d", status);
    if ( status == FTP_ESERVER && rcode < 400 )
    {
        NET_DEBUG("status FTP_ESERVER rcode(%d)", rcode);
        status = FTP_SUCCESS;
    }

    return status;
}

static FTP_STATUS_E ftp_client_put_file(char * file, int isclose)
{
    FTP_CLI_S*      pcli = s_ftp_ctx->cli_obj;
    FTP_STATUS_E    status = FTP_SUCCESS;
    FILE*           stream = NULL;
    char            buf[5120];
    int32_t         close_exp[] = { 226, 0 };
    int32_t         rcode;
    size_t          rlen;
    int32_t         slen;
    int32_t         ret;
    int32_t         write_timeout = 3000;
    time_t          timeout = time(NULL) + 30;

    RETURN_VAL_IF(pcli == NULL || pcli->data_qio == NULL || STRING_IS_EMPTY(file), NET_WARN, FTP_ESYS);

    RETURN_VAL_IF((stream = pi_fopen(file, "r")) == NULL, NET_WARN, FTP_ETRAN);

    do
    {
        rlen = fread(buf, 1, sizeof(buf), stream);

        ret     = 0;
        slen    = 0;
        while ( rlen > 0 )
        {
            ret = QIO_WRITEABLE(pcli->data_qio, FTP_TIMEOUT, 0);
            if ( ret > 0 )
            {
                ret = QIO_WRITE(pcli->data_qio, buf + slen, rlen);
                if ( ret < 0 )
                {
                    NET_WARN("Can't write FTP data ");
                    status = FTP_ETRAN;
                    break;
                }
                else if ( ret > 0 )
                {
                    slen += ret;
                    rlen -= ret;
                    write_timeout = 3000;
                }
                else
                {
                    write_timeout -= 30;
                    if ( write_timeout <= 0 )
                    {
                        NET_WARN("FTP data write timeout");
                        status = FTP_ETRAN;
                        break;
                    }
                    pi_msleep(30);
                }
                timeout = time(NULL) + 30;
            }
            else if ( ret < 0 )
            {
                NET_WARN("FTP waiting write error ");
                status = FTP_ETRAN;
                break;
            }
            else
            {
                if ( time(NULL) > timeout )
                {
                    NET_WARN("FTP waiting write timeout");
                    status = FTP_ETRAN;
                    break;
                }
            }
        }
        pi_msleep( 2 );
    }
    while ( ret > 0 && slen > 0 );

    pi_fclose(stream);

    if ( isclose == 1 && status == FTP_SUCCESS )
    {
        QIO_CLOSE(pcli->data_qio);
        pcli->data_qio = NULL;

        status = ftp_client_check_response(pcli, close_exp, &rcode);
        RETURN_VAL_IF(status != FTP_SUCCESS, NET_WARN, status);
    }

    return status;
}

static void ftp_client_release_job(FTP_STATUS_E status, char* file, uint32_t type)
{
    FTP_CLI_S*  pcli = s_ftp_ctx->cli_obj;
    char        rnfr_path[FILE_PATH_MAX];
    char        rnto_path[FILE_PATH_MAX];
    char        file_path[FILE_PATH_MAX];

    RETURN_IF(pcli == NULL, NET_WARN);

    if ( status == FTP_SUCCESS )
    {
        if ( file != NULL )
        {
            snprintf(rnfr_path, sizeof(rnfr_path), "%s/%s", pcli->server_path, pcli->file_name);
            snprintf(rnto_path, sizeof(rnto_path), "%s/%s", pcli->server_path, basename(file));

            NET_DEBUG("Rename file from(%s) to(%s)", rnfr_path, rnto_path);

            ftp_client_send_command(pcli, "RNFR", rnfr_path);
            ftp_client_check_response(pcli, NULL, NULL);
            ftp_client_send_command(pcli, "RNTO", rnto_path);
            ftp_client_check_response(pcli, NULL, NULL);
        }
        ftp_client_logout(pcli);
    }
    else if ( (type == FILE_PDF || type == FILE_OFD || type == FILE_XPS) && pcli->file_name[0] != '\0' )
    {
        if ( pcli->data_qio != NULL )
        {
            QIO_CLOSE(pcli->data_qio);
            pcli->data_qio = NULL;
        }

        snprintf(file_path, sizeof(file_path), "%s/", pcli->server_path);
        ftp_client_send_command(pcli, "CWD", file_path);
        ftp_client_check_response(pcli, NULL, NULL);

        NET_DEBUG("Delete the file %s %s", file_path, pcli->file_name);
        ftp_client_send_command(pcli, "DELE", pcli->file_name);
        ftp_client_check_response(pcli, NULL, NULL);
    }

    ftp_client_destroy(s_ftp_ctx->cli_obj);
    s_ftp_ctx->cli_obj = NULL;
}

static void* ftp_client_thread(void* arg)
{
    ROUTER_MSG_S    message;
    FTP_STATUS_E    status = FTP_SUCCESS;
    int32_t         scanjobend = 0;
    int32_t         pagecount = 0;
    char*           file = NULL;
    int32_t         packupend = 0;
    int32_t         backup_from = 0;

    while ( 1 )
    {
        int32_t backup_flag = 0;
        message.msgType = MSG_NULL;
        if ( task_msg_wait_forever_by_router(MID_PORT_FTP, &message) < 0 )
        {
            NET_WARN("wait MID_PORT_FTP failed");
            break;
        }
        file = NULL;

        switch( message.msgType )
        {
        case MSG_CTRL_JOB_START:
            {
                backup_from = message.msgSender;
                NET_DEBUG("MSG_CTRL_JOB_START type(%u)", message.msg2);
                scanjobend = 0;
                pagecount = 0;
                status = ftp_client_start_scan((FTP_ADDRINFO_S *)message.msg3);
                break;
            }
        case MSG_DATA_PAGE_END:
            {
                backup_from = message.msgSender;
                file = (char *)message.msg3;
                NET_DEBUG("MSG_DATA_PAGE_END type(%u) file(%s) status(%d) count(%d)", message.msg2, file, status, pagecount);
                if ( file == NULL )
                {
                    status = FTP_ETRAN;
                    break;
                }

                if ( status != FTP_SUCCESS )
                {
                    break;
                }

                if ( (FILE_PDF != message.msg2 && FILE_OFD != message.msg2 && FILE_XPS != message.msg2) || pagecount == 0 )
                {
                    status = ftp_client_put_ready(file);
                    if ( status != FTP_SUCCESS )
                    {
                        break;
                    }
                }
                status = ftp_client_put_file(file, (FILE_PDF != message.msg2 && FILE_OFD != message.msg2 && FILE_XPS != message.msg2) ? 1 : 0);
                pagecount++;
                break;
            }
        case MSG_DATA_JOB_END:
            {
                backup_from = message.msgSender;
                file = (char *)message.msg3;
                NET_DEBUG("MSG_DATA_JOB_END type(%u) file(%s) status(%d) count(%d)", message.msg2, file, status, pagecount);
                if ( (FILE_PDF == message.msg2 || FILE_OFD == message.msg2 || FILE_XPS == message.msg2) && file != NULL && status == FTP_SUCCESS )
                {
                    if ( pagecount == 0 )
                    {
                        status = ftp_client_put_ready(file);
                        NET_DEBUG("MSG_DATA_JOB_END status(%d) ", status);
                        if ( status != FTP_SUCCESS )
                        {
                            ftp_client_release_job(status, file, message.msg2);
                            break;
                        }
                    }
                    status = ftp_client_put_file(file, 1);
                    ftp_client_release_job(status, file, message.msg2);
                    scanjobend = (status != FTP_SUCCESS) ? 0 : -1;
                }
                else
                {
                    ftp_client_release_job(status, file, message.msg2);
                    scanjobend = -1;
                }
                break;
            }
        case MSG_CTRL_JOB_CANCEL:
            {
                backup_from = message.msgSender;
                NET_DEBUG("MSG_CTRL_JOB_CANCEL type(%u) status(%d) count(%d)", message.msg2, status, pagecount);
                ftp_client_release_job(FTP_ESYS, NULL, message.msg2);
                scanjobend = (status != FTP_SUCCESS) ? 0 : -2;
                break;
            }
        case MSG_SCAN_BACKUP:
            {
                char tmp_file[FILE_PATH_MAX];
                //login
                NET_DEBUG("backup start !!!");
                status = FTP_SUCCESS;
                packupend = 0;
                backup_from = message.msgSender;
                backup_flag = 1;
                
                status = ftp_client_start_scan((FTP_ADDRINFO_S *)message.msg3);
                if ( status != FTP_SUCCESS )
                {
                    NET_DEBUG("backup login fail");
                    break;
                }
                
                //遍历文件
                DIR *dir = NULL;
                struct dirent *entry;
                if ( backup_from == MID_SCAN_OUT )
                {
                    dir = opendir("/emmccache/image/scan");
                }
                else if ( backup_from == MID_COPY_JOB_MGR )
                {
                    dir = opendir("/emmccache/sdk_copy_image");
                }
                if ( dir == NULL )
                {
                    NET_DEBUG("opendir() error");
                    status = FTP_ETRAN;
                }
                while ( (entry = readdir(dir)) != NULL )
                {
                    
                    NET_DEBUG("backup file name %s",entry->d_name);
                    
                    if ( strstr(entry->d_name,"jpg") != NULL || strstr(entry->d_name,"tif") != NULL || strstr(entry->d_name,"pdf") != NULL )
                    {
                        NET_DEBUG("net backup dir %s",entry->d_name);
                        //init_file
                        NET_DEBUG("backup file init");
                        if ( backup_from == MID_SCAN_OUT )
                        {
                            snprintf(tmp_file, FILE_PATH_MAX - 1, "/emmccache/image/scan/%s", entry->d_name);
                        }
                        else if ( backup_from == MID_COPY_JOB_MGR )
                        {
                            snprintf(tmp_file, FILE_PATH_MAX - 1, "/emmccache/sdk_copy_image/%s", entry->d_name);
                        }
                        file = (char *)tmp_file;
                        NET_DEBUG("(file): %s !", file);
                
                        if ( status != FTP_SUCCESS || NULL == file )
                        {
                            status = (NULL == file) ? FTP_ETRAN : status;
                            break;
                        }
                
                        status = ftp_client_put_ready(file);
                        if ( status != FTP_SUCCESS )
                        {
                            NET_DEBUG("backup init file fail status = %d !!!", status);
                            break;
                        }
                
                        //put_file
                        NET_DEBUG("backup put");
                        NET_DEBUG("backup put file: %s !", file);
                        status = ftp_client_put_file(file, 1);
                    }
                }
                if ( file == NULL )
                {
                    status = FTP_EFILE_ACCESS;
                }
                
                packupend = (status != FTP_SUCCESS) ? 0 : -1;
                ftp_client_release_job(status, file, message.msg2);
                
                NET_DEBUG("status: %d packupend: %d !", status, packupend);
                NET_DEBUG("file: %s !", file);
                NET_DEBUG("message.msg2: %d ", (int32_t)message.msg2);
                break;
            }
        default:
            {
                NET_WARN("msgtype(%u) invalid", message.msgType);
                break;
            }
        }

#if 1
        if ( file != NULL && backup_from == MID_SCAN_OUT && backup_flag == 0 )
        {
            NET_DEBUG("MSG_SCAN_FILE_STATUS_UPDATE type(%u) file(%s) pagecount(%d) status(%d)", message.msg2, file, pagecount, status);
            message.msgType   = MSG_SCAN_FILE_STATUS_UPDATE;
            message.msg1      = LOCAL_REQUEST;
            message.msg3      = file;
            message.msgSender = MID_PORT_FTP;
            task_msg_send_by_router(MID_SCAN_OUT, &message);
        }
#else
        if ( file != NULL )
        {
            pi_free(file);
            remove(file);
            file = NULL;
        }
#endif

        if ( (scanjobend == 0 && status != FTP_SUCCESS) || (scanjobend < 0 && status == FTP_SUCCESS) 
            || (-1 == packupend && status == FTP_SUCCESS) || (0 == packupend && status != FTP_SUCCESS) )
        {
            if ( backup_from == MID_SCAN_OUT || backup_from == MID_SCAN_OUT_FTP )
            {
                NET_WARN("ftp scan error pagecount(%d) status(%d) endstatus(%d)", pagecount, status, scanjobend);
                message.msgType   = MSG_SCAN_STATUS_UPDATE;
                message.msg1      = LOCAL_REQUEST;
                message.msg2      = status;
                message.msg3      = NULL;
                message.msgSender = MID_PORT_FTP;
                task_msg_send_by_router(MID_SCAN_OUT, &message);
            }
            /*
            else if ( backup_from == MID_COPY_JOB_MGR )
            {
                NET_WARN("ftp scan error pagecount(%d) status(%d) endstatus(%d)", pagecount, status, scanjobend);
                message.msgType   = MSG_COPY_SAVE_PDF_STATUS;
                message.msg1      = LOCAL_REQUEST;
                message.msg2      = status;
                message.msg3      = 0;
                message.msgSender = MID_PORT_FTP;
                task_msg_send_by_router(MID_COPY_JOB_MGR, &message);
            }
            */
            scanjobend = 1;
            packupend = 1;
            backup_from = 0;
            backup_flag = 0;
        }
    }
    NET_WARN("FTP Scan Thread Exit!!!");

    return NULL;
}

int32_t ftp_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_ftp_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_ftp_ctx = (FTP_CTX_S *)pi_zalloc(sizeof(FTP_CTX_S));
    RETURN_VAL_IF(s_ftp_ctx == NULL, NET_WARN, -1);

    do
    {
        s_ftp_ctx->net_ctx = net_ctx;

        BREAK_IF(msg_router_register(MID_PORT_FTP) < 0, NET_WARN);

        s_ftp_ctx->cli_tid = pi_thread_create(ftp_client_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "ftp_client_thread");
        BREAK_IF(s_ftp_ctx->cli_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("FTP client initialize result(%d)", ret);
    if ( ret != 0 )
    {
        ftp_epilog();
    }
    return ret;
}

void ftp_epilog(void)
{
    if ( s_ftp_ctx != NULL )
    {
        if ( s_ftp_ctx->cli_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_ftp_ctx->cli_tid);
        }
        msg_router_unregister(MID_PORT_FTP);
        pi_free(s_ftp_ctx);
        s_ftp_ctx = NULL;
    }
}
/**
 *@}
 */
