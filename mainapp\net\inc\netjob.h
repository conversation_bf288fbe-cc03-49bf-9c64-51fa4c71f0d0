/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netjob.h
 * @addtogroup net
 * @{
 * @addtogroup netjob
 * <AUTHOR>
 * @date 2023-4-20
 * @brief netjob member manager API
 */
#ifndef __NETJOB_H__
#define __NETJOB_H__

#define NETJOB_STATE_MAP(out)    \
{                                \
    out(NETJOB_STATE_UNKNOWN)    \
    out(NETJOB_STATE_NEW)        \
    out(NETJOB_STATE_INIT)       \
    out(NETJOB_STATE_PAUSED)     \
    out(NETJOB_STATE_PROCESSING) \
    out(NETJOB_STATE_RUNNING)    \
    out(NETJOB_STATE_CANCELED)   \
    out(NETJOB_STATE_ABORTED)    \
    out(NETJOB_STATE_DONE)       \
    out(NETJOB_STATE_MAX)        \
}

#define NETJOB_TYPE_MAP(out)     \
{                                \
    out(NETJOB_TYPE_UNKNOWN)     \
    out(NETJOB_TYPE_COPY)        \
    out(NETJOB_TYPE_SCAN)        \
    out(NETJOB_TYPE_PRINT)       \
    out(NETJOB_TYPE_MAX)         \
}

#define ENUM_OUT(n) n,
typedef enum    NETJOB_STATE_MAP(ENUM_OUT)  NETJOB_STATE_E;
typedef enum    NETJOB_TYPE_MAP(ENUM_OUT)   NETJOB_TYPE_E;
#undef  ENUM_OUT

PT_BEGIN_DECLS

/**
 * @brief       Output the state by string.
 * @param[in]   state   : The state in NETJOB_STATE_E.
 * @return      The state string.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
const char*     netjob_state_string         (NETJOB_STATE_E state);

/**
 * @brief       Output the type by string.
 * @param[in]   type    : The type in NETJOB_TYPE_E.
 * @return      The type string.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
const char*     netjob_type_string          (NETJOB_TYPE_E type);

/**
 * @brief       Get the state by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The state in NETJOB_STATE_E.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
NETJOB_STATE_E  netjob_get_state            (int32_t jobid);

/**
 * @brief       Get the type by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The type in NETJOB_STATE_E.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
NETJOB_TYPE_E   netjob_get_type             (int32_t jobid);

/**
 * @brief       Get the QIO_S pointer by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The QIO_S pointer.
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
QIO_S*          netjob_get_pqio             (int32_t jobid);

/**
 * @brief       Get the pages total by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The count of the pages total.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
uint32_t        netjob_get_pages_total      (int32_t jobid);

/**
 * @brief       Get the pages done by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The count of the pages done.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
uint32_t        netjob_get_pages_done       (int32_t jobid);

/**
 * @brief       Get the finish mark by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The finish mark.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
uint8_t         netjob_get_finish_flag      (int32_t jobid);

/**
 * @brief       Get the cancel mark by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The cancel mark.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
uint8_t         netjob_get_cancel_flag      (int32_t jobid);

/**
 * @brief       Get the userdata_type and the userdata pointer by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @param[out]  type    : The type this netjob member.
 * @param[out]  data    : The userdata pointer address.
 * @return      Get result.
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_get_userdata         (int32_t jobid, uint32_t* type, void** data);

/**
 * @brief       Get the jobid of the next netjob member by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The next jobid.
 * @retval      > 0     : success\n
 *              ==0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_get_next_jobid       (int32_t jobid);

/**
 * @brief       Mark the finish mark  by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The result.
 * @retval      > 0     : success\n
 *              ==0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_mark_finish          (int32_t jobid);

/**
 * @brief       Mark the cancel mark  by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @return      The result.
 * @retval      > 0     : success\n
 *              ==0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_mark_cancel          (int32_t jobid);

/**
 * @brief       Set the userdata_type, the userdata pointer and the destroy function by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @param[in]   type    : The type this netjob member.
 * @param[in]   data    : The userdata pointer address.
 * @param[in]   destroy : The destroy function.
 * @return      The result.
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_set_userdata         (int32_t jobid, uint32_t type, void* data, void (*destroy)(void *, uint32_t));

/**
 * @brief       Set the netjob state by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @param[in]   state   : The new state of this netjob member.
 * @return      The result.
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_set_state            (int32_t jobid, NETJOB_STATE_E state);

/**
 * @brief       Set the netjob page done by the jobid of this netjob member.
 * @param[in]   jobid   : The jobid of this netjob member.
 * @param[in]   state   : The new state of this netjob member.
 * @return      The result.
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> huanbin
 * @date        2024-4-22
 */
int32_t         netjob_set_page_done(int32_t jobid, int32_t page_count);

/**
 * @brief       Alloc a netjob and set the job type and QIO_S pointer.
 * @param[in]   type    : the type in NETJOB_TYPE_E.
 * @param[in]   pqio    : The QIO_S pointer.
 * @return      The jobid of new alloc.
 * @retval      > 0     : success\n
 *              ==0     : fail
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
int32_t         netjob_alloc                (NETJOB_TYPE_E type, QIO_S* pqio);

/**
 * @brief       Free the netjob by the jobid of this netjob member.
 * @param[in]   jobid   : the jobid of this netjob member.
 * @param[in]   reserved: The number of jobs reserved in the alloc list.
 * <AUTHOR> Xin
 * @date        2023-4-20
 */
void            netjob_free                 (int32_t jobid, int32_t reserved);

int32_t         netjob_start_task           (int32_t jobid, int32_t (*handler)(void *), void* cookie);

int32_t         netjob_has_free_task        (void);

/**
 * @brief       Construct the netjob list.
 * @return      The result.
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
int32_t         netjob_init                 (void);

/**
 * @brief       Destruct the netjob list.
 * <AUTHOR> Xin
 * @date        2023-5-10
 */
void            netjob_deinit               (void);

PT_END_DECLS

#endif /* __NETJOB_H__ */
/**
 *@}
 */
