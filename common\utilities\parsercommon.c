#include <string.h>
#include <unistd.h>
#include <stdbool.h>
#include "pol/pol_log.h"
#include "utilities/parsercommon.h"
#include "cmd.h"

/**
 * \brief init parser mode, debug mode or release mode.
 * \author:jacky zeng
 */
static int debug = false;
static int32_t s_parser_log_level = PARSER_DEFALUT_LOG_LEVEL;

static int32_t parser_log_ctrl( int argc, char *argv[] )
{
    if (argc<1)
    {
        pi_log_e("Incorrect parameter number\n");
        return -1;
    }

    s_parser_log_level = atoi(argv[0]);
    pi_log_d("Set Parser log level is [%d]\n", s_parser_log_level);
	return 0;
}

void init_Parser_Debug_Mode()
{
    if (access("/tmp/debug", R_OK) == 0)
        debug = true;
    cmd_register( "parser","log",parser_log_ctrl,"");
}

/**
 * \brief debug mode, get the mode is debug mode or not.
 * \author:jacky zeng
 */

int debug_Mode()
{
    return debug;
}

int get_parser_log_level()
{
    return s_parser_log_level;
}

#if ENCRYPTION_PRINT_ENABLE
int32_t parser_GDI_read(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs)
{
    int32_t rc;
    int32_t totalSecs = tosecs;

    rc = GQIO_READ(pgqio, buffer, count, 0, totalSecs);
    return rc;
}

int32_t parser_common_read(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs)
{
	int32_t rc;
	rc = GQIO_READ(pgqio, buffer, count, 0, tosecs);
	return rc;
}

#else
/**
 * \brief read IO stream for the parsers.
 * This function read io stream for the parsers
 * \param[in] PQIO:io data stream
 * \param[in] Uint8 *buffer: data buffer
 * \param[in] int count:data buffer size
 * \param[in] int tosecs: seconds to wait for results
 * \return int tc: read size
 * \author:jacky zeng
 */
int32_t parser_GDI_read(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs)
{
    int32_t ec, rc;
    int32_t totalSecs = tosecs;

    while (1)
    {
        ec = GQIO_READABLE(pgqio, 1, 0);
        if (ec < 0)
        {
            pi_log_e("QIO is not readable.\n");
            return (-1);
        }
        else if (ec == 0)
        {
            totalSecs--;
            if (totalSecs <= 0)
            {
                return 0;
            }
            if (0 == totalSecs%3)
            {
                pi_log_d( "waiting for data.\n");
            }
        }
        else
        {
            rc = GQIO_READ(pgqio, buffer, count);
            if (rc > 0)
            {
                return rc;
            }
            else
            {
                pi_log_e("Read failed %d\n", rc);
                return (-1);
            }
        }
    }
}


int32_t parser_common_read(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs)
{
    int32_t ec, rc, tc;

    tc = 0;
    rc = 0;
    int32_t totalSecs = tosecs;
    while (count > 0)
    {
        ec = GQIO_READABLE(pgqio, 1, 0);
        if (ec < 0)
        {
            if(-2 == ec)
            {
                /*dummy keep connection, can try to poll again. only qiousb dummy will return -2*/
                pi_log_d( "QIO dummy connect status...\n");
                totalSecs = tosecs;

                /*dummy continue to try to pool aagin*/
                continue;
            }
            else
            {
                if (totalSecs && (0 == tc))
                {
                    pi_log_e("QIO is not readable! <%d>\n", ec);
                    tc = -1;
                }
                break;
            }

            //break;
        }
        else if (ec == 0)
        {
            totalSecs--;
            if (totalSecs <= 0)
                break;
        }
        else
        {
            rc = GQIO_READ(pgqio, buffer + tc, count);
            if (rc > 0)
            {
                count -= rc;
                tc += rc;
            }
            else if (rc < 0)
            {
                pi_log_e("Read failed %d\n", rc);
                if (totalSecs)
                {
                    tc = -1;
                }
                break;
            }
            else /* rc == 0 after select 1 means no more connection */
            {
                pi_log_d( "Connection Closed\n");
                return tc;
            }
        }
    }
    return tc;
}

#endif
/**
 * \brief write data to IO stream.
 * This function write data to io stream for the parsers
 * \param[in] GQIO_P:io stream
 * \param[in] Uint8 *buffer: data buffer
 * \param[in] int count:data buffer size
 * \param[in] int tosecs: seconds to wait for results
 * \return int tc: write size
 * \author:jacky zeng
 */
int32_t parser_common_write(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs)
{
    int32_t ec, rc, tc;

    tc = 0;
    rc = 0;
    while (count > 0)
    {
        ec = GQIO_WRITEABLE(pgqio, tosecs, 0);
        if (ec < 0)
        {
            if (tosecs)
            {
                tc = -1;
            }
            break;
        }
        else if (ec == 0)
        {
            break;
        }
        else
        {
            rc = GQIO_WRITE(pgqio, buffer + tc, count);
            if (rc > 0)
            {
                count -= rc;
                tc += rc;
            }
            else if (rc < 0)
            {
                pi_log_e("Write failed %d\n", rc);
                if (tosecs)
                {
                    tc = -1;
                }
                break;
            }
            else /* rc == 0 after select 1 means no more connection */
            {
                pi_log_d( "Connection Closed\n");
                return tc;
            }
        }
    }
    return tc;
}

int32_t parser_common_rewind(GQIO_S* pgqio, uint8_t* buffer, int32_t count)
{
    return GQIO_REWIND(pgqio, buffer, count);
}

/**
 * \brief uint8 convert to uint16 with big-endian format
 * \param[in] uint8 front
 * \param[in] uint8 behind
 * \return uint16
 * \author:jacky zeng
 */
uint16_t u8_to_u16_be(uint8_t front, uint8_t behind)
{
    return (((uint16_t)behind <<8) | (front & 0xff));
}

/**
 * \brief uint16 with big-endian format convert to uint8 buffer[2]
 * \param[in] uint16 src
 * \param[in] uint8 *buffer
 * \author:jacky zeng
 */
void be_u16_to_u8_buffer(uint16_t src, uint8_t *buffer)
{
   uint16_t data = src;
   *(buffer + 1) = (uint8_t)(data >> 8);
   *(buffer) = (uint8_t)(data);
}

/**
 * \brief uint8 convert to uint32 with big-endian format
 * \param[in] uint8.
 * \param[in] uint8.
 * \param[in] uint8.
 * \param[in] uint8.
 * \return uint32
 * \author:jacky zeng
 */
uint32_t u8_to_u32_be(uint8_t first, uint8_t second, uint8_t third, uint8_t fourth)
{
    return ((uint32_t)first | ((uint32_t)second << 8) | ((uint32_t)third << 16) | ((uint32_t)fourth << 24));
}
