# *DOCUMENTATION*
# To see a list of typical targets execute "make help"
# More info can be located in ./README
# Comments in this file are targeted only to the developer, do not
# expect to learn how to build the kernel reading this file.

# Do not:
# o  use make's built-in rules and variables
#    (this increases performance and avoids hard-to-debug behaviour);
# o  print "Entering directory ...";
MAKEFLAGS += -rR --no-print-directory


# To put more focus on warnings, be less verbose as default
# Use 'make V=1' to see the full commands

ifeq ("$(origin V)", "command line")
  KBUILD_VERBOSE = $(V)
endif
ifndef KBUILD_VERBOSE
  KBUILD_VERBOSE = 0
endif

# kbuild supports saving output files in a separate directory.
# To locate output files in a separate directory two syntaxes are supported.
# In both cases the working directory must be the root of the kernel src.
# 1) O=
# Use "make O=dir/to/store/output/files/"
#
# 2) Set KBUILD_OUTPUT
# Set the environment variable KBUILD_OUTPUT to point to the directory
# where the output files shall be placed.
# export KBUILD_OUTPUT=dir/to/store/output/files/
# make
#
# The O= assignment takes precedence over the KBUILD_OUTPUT environment
# variable.

# Our default target
PHONY := _all
_all:

# KBUILD_SRC is set on invocation of make in OBJ directory
# KBUILD_SRC is not intended to be used by the regular user (for now)
ifeq ($(KBUILD_SRC),)

# OK, Make called in directory where kernel src resides
# Do we want to locate output files in a separate directory?
ifeq ("$(origin O)", "command line")
  KBUILD_OUTPUT := $(O)
endif

ifeq ("$(origin W)", "command line")
  export KBUILD_ENABLE_EXTRA_GCC_CHECKS := $(W)
endif

# Cancel implicit rules on top Makefile
$(CURDIR)/Makefile Makefile: ;

# ### test ###############################################
# 代码文件路径
#code_path := print/print_app/print_if/print_if.o
#             #print/print_app/print_pre/print_pre.o
#sub_makefile_path = $(code_path)
#-include $(sub_makefile_path)
######################################################

ifneq ($(KBUILD_OUTPUT),)
# Invoke a second make in the output directory, passing relevant variables
# check that the output directory actually exists
saved-output := $(KBUILD_OUTPUT)
KBUILD_OUTPUT := $(shell cd $(KBUILD_OUTPUT) && /bin/pwd)
$(if $(KBUILD_OUTPUT),, \
     $(error output directory "$(saved-output)" does not exist))

PHONY += $(MAKECMDGOALS) sub-make

$(filter-out _all sub-make $(CURDIR)/Makefile, $(MAKECMDGOALS)) _all: sub-make
	$(Q)@:

sub-make: FORCE
	$(if $(KBUILD_VERBOSE:1=),@)$(MAKE) -C $(KBUILD_OUTPUT) \
	KBUILD_SRC=$(CURDIR) \
	-f $(CURDIR)/Makefile \
	$(filter-out _all sub-make,$(MAKECMDGOALS))

# Leave processing to above invocation of make
skip-makefile := 1
endif # ifneq ($(KBUILD_OUTPUT),)
endif # ifeq ($(KBUILD_SRC),)

# We process the rest of the Makefile if this is the final invocation of make
ifeq ($(skip-makefile),)

# If building an external module we do not care about the all: rule
# but instead _all depend on modules
PHONY += all
_all: all

srctree		:= $(if $(KBUILD_SRC),$(KBUILD_SRC),$(CURDIR))
objtree		:= $(CURDIR)
src		:= $(srctree)
obj		:= $(objtree)

VPATH		:= $(srctree)

export srctree objtree VPATH

CROSS_COMPILE	?= $(CONFIG_CROSS_COMPILE:"%"=%)

KCONFIG_CONFIG	?= .config
export KCONFIG_CONFIG

# SHELL used by kbuild
CONFIG_SHELL := $(shell if [ -x "$$BASH" ]; then echo $$BASH; \
	  else if [ -x /bin/bash ]; then echo /bin/bash; \
	  else echo sh; fi ; fi)

HOSTCC       = gcc
HOSTCXX      = g++
HOSTCFLAGS   = -Wall -Wmissing-prototypes -Wstrict-prototypes -O2 -fomit-frame-pointer
HOSTCXXFLAGS = -O2

# Beautify output
# ---------------------------------------------------------------------------
#
# Normally, we echo the whole command before executing it. By making
# that echo $($(quiet)$(cmd)), we now have the possibility to set
# $(quiet) to choose other forms of output instead, e.g.
#
#         quiet_cmd_cc_o_c = Compiling $(RELDIR)/$@
#         cmd_cc_o_c       = $(CC) $(c_flags) -c -o $@ $<
#
# If $(quiet) is empty, the whole command will be printed.
# If it is set to "quiet_", only the short version will be printed.
# If it is set to "silent_", nothing will be printed at all, since
# the variable $(silent_cmd_cc_o_c) doesn't exist.
#
# A simple variant is to prefix commands with $(Q) - that's useful
# for commands that shall be hidden in non-verbose mode.
#
#	$(Q)ln $@ :<
#
# If KBUILD_VERBOSE equals 0 then the above command will be hidden.
# If KBUILD_VERBOSE equals 1 then the above command is displayed.

ifeq ($(KBUILD_VERBOSE),1)
  quiet =
  Q =
else
  quiet=quiet_
  Q = @
endif

# If the user is running make -s (silent mode), suppress echoing of
# commands

ifneq ($(findstring s,$(MAKEFLAGS)),)
  quiet=silent_
endif

export quiet Q KBUILD_VERBOSE


# Look for make include files relative to root of kernel src
MAKEFLAGS += --include-dir=$(srctree)

# We need some generic definitions (do not try to remake the file).
$(srctree)/scripts/Kbuild.include: ;
include $(srctree)/scripts/Kbuild.include

# Make variables (CC, etc...)

AS		= $(CROSS_COMPILE)as
LD		= $(CROSS_COMPILE)ld
CC		= $(CROSS_COMPILE)gcc
CPP		= $(CC) -E
# c++ compiler append
CXX     = $(CROSS_COMPILE)g++
AR		= $(CROSS_COMPILE)ar
NM		= $(CROSS_COMPILE)nm
STRIP		= $(CROSS_COMPILE)strip
OBJCOPY		= $(CROSS_COMPILE)objcopy
OBJDUMP		= $(CROSS_COMPILE)objdump
AWK		= awk
INSTALLKERNEL  := installkernel
PERL		= perl

CHECKFLAGS     := -D__linux__ -Dlinux -D__STDC__ -Dunix -D__unix__ \
		  -Wbitwise -Wno-return-void $(CF)
CFLAGS_KERNEL	=
AFLAGS_KERNEL	=
# c++ compiler modify
MFP_LDFLAGS     :=$(MAINAPP_LDFLAGS)

# Use LINUXINCLUDE when you must reference the include/ directory.
# Needed to be compatible with the O= option
LINUXINCLUDE    := -Iinclude \
                   $(if $(KBUILD_SRC), -I$(srctree)/include) \
                   -include include/generated/autoconf.h \
                   -Iinclude/linux \
				   -Iinclude/ipm \
                   -Iinclude/print \
				   -Iinclude/scan \
                   -Iinclude/copy \
                   -Iinclude/net \
				   -Iinclude/peripheral \
				   -Iinclude/platform \
				   -Iinclude/nvram \
				   -Iinclude/nvtables \
				   -Iinclude/proxy_transfer \
				   -Iinclude/data_distribution \
				   -Iinclude/system_manager \
				   -Iinclude/usb \
				   -Iinclude/public \
				   -Iinclude/printer_status \
				   -Iinclude/acl \
				   -Iinclude/security \
				   -Iinclude/httc \
				   -Iinclude/storage_manager \
				   -Iinclude/timestamp \
				   -Iinclude/event_log \
				   -Iinclude/hal \
				   -Iprint/print_app/print_image/print_image_udisk \
				   -Iprint/print_core/video_if/video_app/common \
				   -Iprint/print_core/video_if/video_kernel_if \
				   -Iinclude/pedk_manager/app_manager \
				   -Iinclude/pedk_manager \
				   -Iinclude/pedk_setting \
				   -Iinclude/pedk_manager/download \
				   -Iinclude/data_distribution/panel_pedk \
				   -Iinclude/printer_resource	\
				   -Iinclude/print_parser	\
				   -Iinclude/prnsdk_manager \
				   -Iinclude/parser	\
				   -Iplog \


LINUXINCLUDE	+=

KBUILD_CPPFLAGS :=

KBUILD_CFLAGS   := -g -ggdb \
                   -Wall -Werror -std=gnu99 -Wno-undef -Wno-trigraphs \
                   -Wno-unused-variable -Wno-unused-function \
                   -fno-strict-aliasing -fno-common \
                   -Werror-implicit-function-declaration \
                   -fno-delete-null-pointer-checks \
                   -rdynamic -funwind-tables -ffunction-sections \
                   -D'SVN_REV="$(SVN_REV)"' \
                   -D_GNU_SOURCE \
                   -D Linux

KBUILD_AFLAGS_KERNEL :=
KBUILD_CFLAGS_KERNEL :=
KBUILD_AFLAGS   := -D__ASSEMBLY__

# Read KERNELRELEASE from include/config/kernel.release (if it exists)
KERNELRELEASE = $(shell cat include/config/kernel.release 2> /dev/null)
KERNELVERSION = $(VERSION)$(if $(PATCHLEVEL),.$(PATCHLEVEL)$(if $(SUBLEVEL),.$(SUBLEVEL)))$(EXTRAVERSION)

# c++ compiler modify
export ARCH SRCARCH CONFIG_SHELL HOSTCC HOSTCFLAGS CROSS_COMPILE AS LD CC CXX
export CPP AR NM STRIP OBJCOPY OBJDUMP
export MAKE AWK GENKSYMS INSTALLKERNEL PERL UTS_MACHINE
export HOSTCXX HOSTCXXFLAGS

export KBUILD_CPPFLAGS NOSTDINC_FLAGS LINUXINCLUDE OBJCOPYFLAGS LDFLAGS
export KBUILD_CFLAGS CFLAGS_KERNEL
export KBUILD_AFLAGS AFLAGS_KERNEL
export KBUILD_AFLAGS_KERNEL KBUILD_CFLAGS_KERNEL
export KBUILD_ARFLAGS

# Files to ignore in find ... statements

RCS_FIND_IGNORE := \( -name SCCS -o -name BitKeeper -o -name .svn -o -name CVS -o -name .pc -o -name .hg -o -name .git \) -prune -o

# ===========================================================================
# Rules shared between *config targets and build targets

# Basic helpers built in scripts/
PHONY += scripts_basic
scripts_basic:
	$(Q)$(MAKE) $(build)=scripts/basic

# To avoid any implicit rule to kick in, define an empty command.
scripts/basic/%: scripts_basic ;

PHONY += outputmakefile
# outputmakefile generates a Makefile in the output directory, if using a
# separate output directory. This allows convenient use of make in the
# output directory.
outputmakefile:
ifneq ($(KBUILD_SRC),)
	$(Q)ln -fsn $(srctree) source
	$(Q)$(CONFIG_SHELL) $(srctree)/scripts/mkmakefile \
	    $(srctree) $(objtree) $(VERSION) $(PATCHLEVEL)
endif


# To make sure we do not include .config for any of the *config targets
# catch them early, and hand them over to scripts/kconfig/Makefile
# It is allowed to specify more targets when calling make, including
# mixing *config targets and build targets.
# For example 'make oldconfig all'.
# Detect when mixed targets is specified, and make a second invocation
# of make so .config is not included in this case either (for *config).

no-dot-config-targets := clean mrproper distclean \
			 cscope gtags TAGS tags help %docs check% coccicheck \
			 include/linux/version.h headers_% \
			 kernelversion %src-pkg

config-targets := 0
mixed-targets  := 0
dot-config     := 1

ifneq ($(filter $(no-dot-config-targets), $(MAKECMDGOALS)),)
	ifeq ($(filter-out $(no-dot-config-targets), $(MAKECMDGOALS)),)
		dot-config := 0
	endif
endif

ifneq ($(filter config %config,$(MAKECMDGOALS)),)
        config-targets := 1
        ifneq ($(filter-out config %config,$(MAKECMDGOALS)),)
                mixed-targets := 1
        endif
endif

ifeq ($(mixed-targets),1)
# ===========================================================================
# We're called with mixed targets (*config and build targets).
# Handle them one by one.

%:: FORCE
	$(Q)$(MAKE) -C $(srctree) KBUILD_SRC= $@

else
ifeq ($(config-targets),1)
# ===========================================================================
# *config targets only - make sure prerequisites are updated, and descend
# in scripts/kconfig to make the *config target

# Read arch specific Makefile to set KBUILD_DEFCONFIG as needed.
# KBUILD_DEFCONFIG may point out an alternative default configuration
# used for 'make defconfig'
#include $(srctree)/arch/$(SRCARCH)/Makefile
export KBUILD_DEFCONFIG KBUILD_KCONFIG

config: scripts_basic outputmakefile FORCE
	$(Q)mkdir -p include/linux include/config
	$(Q)$(MAKE) $(build)=scripts/kconfig $@

%config: scripts_basic outputmakefile FORCE
	$(Q)mkdir -p include/linux include/config
	$(Q)$(MAKE) $(build)=scripts/kconfig $@

else


ifeq ($(dot-config),1)
# Read in config
-include include/config/auto.conf


# Read in dependencies to all Kconfig* files, make sure to run
# oldconfig if changes are detected.
-include include/config/auto.conf.cmd

# To avoid any implicit rule to kick in, define an empty command
$(KCONFIG_CONFIG) include/config/auto.conf.cmd: ;

# If .config is newer than include/config/auto.conf, someone tinkered
# with it and forgot to run make oldconfig.
# if auto.conf.cmd is missing then we are probably in a cleaned tree so
# we execute the config step to be sure to catch updated Kconfig files
include/config/%.conf: $(KCONFIG_CONFIG) include/config/auto.conf.cmd
	$(Q)$(MAKE) -f $(srctree)/Makefile silentoldconfig


else
# Dummy target needed, because used as prerequisite
include/config/auto.conf: ;
endif # $(dot-config)

prebuild:
	echo prebuild

# The all: target is the default when no target is given on the
# command line.
# This allow a user to issue only 'make' to build a kernel including modules
# Defaults to vmlinux, but the arch makefile usually adds further targets
all: prebuild mfp.afx
	echo all: $@

TIMESTAMP := build_datetime

define timestamp
        @ echo "#define _QUOTED(v) #v" > $(TIMESTAMP).c
        @ echo "#define QUOTED(v) _QUOTED(v)" >> $(TIMESTAMP).c
        @ echo "const char *buildtarget =" >> $(TIMESTAMP).c
        @ echo "QUOTED(" $(TARGET_ARCH) ");" >> $(TIMESTAMP).c
        @ echo "const char *buildtargos =" >> $(TIMESTAMP).c
        @ echo "QUOTED(" $(TARGET_OS) ");" >> $(TIMESTAMP).c
        @ echo "const char *buildboard =" >> $(TIMESTAMP).c
        @ echo "QUOTED(" $(BOARD) ");" >> $(TIMESTAMP).c
        @ echo "const char *builddate = __DATE__;" >> $(TIMESTAMP).c
        @ echo "const char *buildtime = __TIME__;" >> $(TIMESTAMP).c
        @ echo "const char *builddir = " >> $(TIMESTAMP).c
        @ echo "QUOTED(" $(CURDIR) ");" >> $(TIMESTAMP).c
        @ echo "const char *buildhost = " >> $(TIMESTAMP).c
        @ echo "QUOTED(" $(shell hostname) ");" >> $(TIMESTAMP).c
        @ echo "const char *buildhostos = " >> $(TIMESTAMP).c
        @ echo "QUOTED(" $(HOST_ARCH) ");" >> $(TIMESTAMP).c
        @ $(changeset)
        @ "$(CC)" $(CFLAGS) $(TIMESTAMP).c -c -o $(TIMESTAMP).o
        @ rm $(TIMESTAMP).c
endef

$(TIMESTAMP).o:
	$(timestamp)

# print scan os/osLinux bios folder, and main makefile will scan sub-makefile from these folder
objs-y  := main \
		   ipm \
           print \
           copy \
		   scan \
           net \
           parser \
		   peripheral \
		   nvram \
		   nvtables \
           platform \
		   proxy_transfer \
		   data_distribution \
		   system_manager \
		   usb \
		   acl \
		   public \
		   power_manager \
		   power_manager_debug \
		   security \
		   printer_info \
		   httc \
		   upgrade \
		   storage_manager	\
		   timestamp \
		   event_log/src \
		   hal \
		   pedk_manager \
		   pedk_setting \
		   printer_resource \
		   prnsdk_manager \
           log \


libs-y  :=

#libs-a-y	:= ipm/sbe/prebuilt/Linux/6300/seg_B0.a

mfp.afx-dirs	:= $(objs-y) $(libs-y)
#mfp.afx-objs	:= $(foreach dir,$(objs-y),$(dir)/$(dir)-builtin.o)
mfp.afx-objs	:= $(patsubst %,%/built-in.o, $(objs-y))
#mfp.afx-libs	:= $(foreach dir,$(libs-y),$(dir)/$(dir).a)
mfp.afx-libs	:= $(patsubst %,%/lib.a, $(libs-y))
#mfp.afx-libs	+= print/pdls/ipsifio/ print/pdls/ipsinf/
mfp.afx-all	:= $(mfp.afx-objs) $(mfp.afx-libs)

#quiet_cmd_mfp.a = AR	$@
#      cmd_mfp.a = $(AR) rcs $@ $(mfp.afx-libs) $(mfp.afx-objs) $(TIMESTAMP).o
#
#mfp.a: $(mfp.afx-all) $(TIMESTAMP).o
#	$(call if_changed,mfp.a)

# Do modpost on a prelinked vmlinux. The finally linked vmlinux has
# relevant sections renamed as per the linker script.
#quiet_cmd_mfp.afx = LD      $@
#      cmd_mfp.afx = $(CC) $(LDFLAGS) -o $@                          \
#      -Wl,--start-group $< -Wl,--end-group

#mfp.afx: mfp.a
#	echo $(mfp.afx-objs)
#	echo $(mfp.afx-libs)
#	echo $(SVN_REV)
#	$(call if_changed,mfp.afx)

ifeq ($(CONFIG_NET_BONJOUR),y)
MFP_LDFLAGS += -lmdnsresponder
endif

ifeq ($(CONFIG_NET_WIFI),y)
MFP_LDFLAGS += -lwpa_client
endif

ifeq ($(CONFIG_NET_WSD),y)
MFP_LDFLAGS += -lcurl
endif

ifeq ($(CONFIG_SDK_EWS),y)
MFP_LDFLAGS += -lcurl
endif

ifeq ($(CONFIG_HTTC),y)
$(info XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX)
MFP_LDFLAGS += -lhttc_version
MFP_LDFLAGS += -ltpcm_measure
MFP_LDFLAGS += -ltcm
endif

CONFIG_IMAGES ?= n
ifeq ($(CONFIG_IMAGES), y)
MFP_LDFLAGS += -limages
endif
# Do modpost on a prelinked vmlinux. The finally linked vmlinux has
# relevant sections renamed as per the linker script.
quiet_cmd_mfp.afx = LD      $@
      cmd_mfp.afx = $(CC) $(LDFLAGS) -Wl,-Map,mfp.map -o $@                          \
      -Wl,--start-group $(mfp.afx-libs) $(mfp.afx-objs) $(TIMESTAMP).o $(MFP_LDFLAGS) $(libs-a-y) -Wl,--end-group \
	  -L print/print_parser/parser_ips \
	  -L httc/httc_lib/


mfp.afx: $(mfp.afx-all) $(TIMESTAMP).o
	echo afx-objs: $(mfp.afx-objs)
	echo afx-libs: $(mfp.afx-libs)
	echo obj: $(obj)
	echo SCANNER_TYPE=$(SCANNER_TYPE)
	$(call if_changed,mfp.afx)

# The actual objects are generated when descending,
# make sure no implicit rule kicks in
$(sort $(mfp.afx-all)): $(mfp.afx-dirs) ;

# Handle descending into subdirectories listed in $(vmlinux-dirs)
# Preset locale variables to speed up the build process. Limit locale
# tweaks to this spot to avoid wrong language settings when running
# make menuconfig etc.
# Error messages still appears in the original language

#PHONY += $(vmlinux-dirs)
#$(vmlinux-dirs): prepare scripts

PHONY += $(mfp.afx-dirs)
$(mfp.afx-dirs): scripts_basic
	$(Q)$(MAKE) $(build)=$@


###
# Cleaning is done on three levels.
# make clean     Delete most generated files
#                Leave enough to build external modules
# make mrproper  Delete the current configuration, and all generated files
# make distclean Remove editor backup files, patch leftover files and the like

# Directories & files removed with 'make clean'
CLEAN_DIRS  +=
CLEAN_FILES +=	mfp.afx

# Directories & files removed with 'make mrproper'
MRPROPER_DIRS  += include/config include/generated
MRPROPER_FILES += .config .config.old tags TAGS cscope* GPATH GTAGS GRTAGS GSYMS

# clean - Delete most, but leave enough to build external modules
#
clean: rm-dirs  := $(CLEAN_DIRS)
clean: rm-files := $(CLEAN_FILES)
clean-dirs      := $(addprefix _clean_, $(mfp.afx-dirs))

PHONY += $(clean-dirs) clean archclean
$(clean-dirs):
	$(Q)$(MAKE) $(clean)=$(patsubst _clean_%,%,$@)

clean: $(clean-dirs)
	$(call cmd,rmdirs)
	$(call cmd,rmfiles)
	@find . $(RCS_FIND_IGNORE) \
		\( -name '*.[oas]' -o -name '.*.cmd' \
		-o -name '.*.d' -o -name '.*.tmp' -o -name '*.mod.c' \
		-o -name modules.builtin -o -name '.tmp_*.o.*' \
		-o -name '*.gcno' \) -type f -print | xargs rm -f

# mrproper - Delete all generated files, including .config
#
mrproper: rm-dirs  := $(wildcard $(MRPROPER_DIRS))
mrproper: rm-files := $(wildcard $(MRPROPER_FILES))
mrproper-dirs      := $(addprefix _mrproper_, scripts)

PHONY += $(mrproper-dirs) mrproper
$(mrproper-dirs):
	$(Q)$(MAKE) $(clean)=$(patsubst _mrproper_%,%,$@)

mrproper: clean $(mrproper-dirs)
	$(call cmd,rmdirs)
	$(call cmd,rmfiles)

# distclean
#
PHONY += distclean
distclean: mrproper
	@find $(srctree) $(RCS_FIND_IGNORE) \
		\( -name '*.orig' -o -name '*.rej' -o -name '*~' \
		-o -name '*.bak' -o -name '#*#' -o -name '.*.orig' \
		-o -name '.*.rej' -o -size 0 \
		-o -name '*%' -o -name '.*.cmd' -o -name 'core' \) \
		-type f -print | xargs rm -f


# FIXME Should go into a make.lib or something
# ===========================================================================

quiet_cmd_rmdirs = $(if $(wildcard $(rm-dirs)),CLEAN   $(wildcard $(rm-dirs)))
      cmd_rmdirs = rm -rf $(rm-dirs)

quiet_cmd_rmfiles = $(if $(wildcard $(rm-files)),CLEAN   $(wildcard $(rm-files)))
      cmd_rmfiles = rm -f $(rm-files)

# Shorthand for $(Q)$(MAKE) -f scripts/Makefile.clean obj=dir
# Usage:
# $(Q)$(MAKE) $(clean)=dir
clean := -f $(if $(KBUILD_SRC),$(srctree)/)scripts/Makefile.clean obj



help:
	@echo  'Cleaning targets:'
	@echo  '  clean		  - Remove most generated files but keep the config and'
	@echo  '                    enough build support to build external modules'
	@echo  '  mrproper	  - Remove all generated files + config + various backup files'
	@echo  '  distclean	  - mrproper + remove editor backup and patch files'
	@echo  ''
	@echo  'Configuration targets:'
	@$(MAKE) -f $(srctree)/scripts/kconfig/Makefile help
	@echo  ''
	@echo  'Other generic targets:'
	@echo  '  all		  - Build all targets marked with [*]'
	@echo  '* mfp.afx	  	  - Build the application'
	@echo  '  dir/            - Build all files in dir and below'
	@echo  '  dir/file.[oisS] - Build specified target only'
	@echo  '  dir/file.lst    - Build specified mixed source/assembly target only'
	@echo  '                    (requires a recent binutils and recent build (System.map))'
	@echo  '  tags/TAGS	  - Generate tags file for editors'
	@echo  '  cscope	  - Generate cscope index'
	@echo  '  gtags           - Generate GNU GLOBAL index'
	@echo  '  kernelrelease	  - Output the release version string'
	@echo  '  kernelversion	  - Output the version stored in Makefile'
	 echo  ''
	@echo  'Static analysers'
	@echo  '  checkstack      - Generate a list of stack hogs'
	@echo  '  namespacecheck  - Name space analysis on compiled kernel'
	@echo  '  versioncheck    - Sanity check on version.h usage'
	@echo  '  includecheck    - Check for duplicate included header files'
	@echo  '  export_report   - List the usages of all exported symbols'
	@echo  '  headers_check   - Sanity check on exported headers'
#	@$(MAKE) -f $(srctree)/scripts/Makefile.help checker-help
	@echo  ''
#	@echo  'Kernel packaging:'
#	@$(MAKE) $(build)=$(package-dir) help
	@echo  ''
#	@echo  'Documentation targets:'
#	@$(MAKE) -f $(srctree)/Documentation/DocBook/Makefile dochelp
	@echo  ''
	@echo  '  make V=0|1 [targets] 0 => quiet build (default), 1 => verbose build'
	@echo  '  make V=2   [targets] 2 => give reason for rebuild of target'
	@echo  '  make O=dir [targets] Locate all output files in "dir", including .config'
	@echo  '  make W=n   [targets] Enable extra gcc checks, n=1,2,3 where'
	@echo  '		1: warnings which may be relevant and do not occur too often'
	@echo  '		2: warnings which occur quite often but may still be relevant'
	@echo  '		3: more obscure warnings, can most likely be ignored'
	@echo  '		Multiple levels can be combined with W=12 or W=123'
	@echo  '  make RECORDMCOUNT_WARN=1 [targets] Warn about ignored mcount sections'
	@echo  ''
	@echo  'Execute "make" or "make all" to build all targets marked with [*] '
	@echo  'For further info see the ./README file'


endif #ifeq ($(config-targets),1)
endif #ifeq ($(mixed-targets),1)

endif	# skip-makefile

PHONY += FORCE
FORCE:

# Declare the contents of the .PHONY variable as phony.  We keep that
# information in a variable so we can use it in if_changed and friends.
.PHONY: $(PHONY)
