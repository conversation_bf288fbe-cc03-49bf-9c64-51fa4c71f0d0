/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_serial_num.c
 * @addtogroup panel_dc
 * @{
 * @brief panel dc serial num operate interface file
 * <AUTHOR>
 * @date 2025-05-12
 */

#include <string.h>
#include <stdio.h>
#include <fcntl.h>
#include "panel_file.h"
#include "pol/pol_log.h"

#define SERIAL_NUM_PATH     "/mnt/serial_num"

int32_t panel_get_ui_board_serial_num( char* buf, uint32_t buf_size )
{
    int32_t err_num = 0;
    int32_t fd = 0;

    fd = panel_file_open( SERIAL_NUM_PATH, O_RDONLY, 0, &err_num );
    if( -1 == fd )
    {
        pi_log_e( "file [%s] open failed, err_num = %d\n", SERIAL_NUM_PATH, err_num );
        return -1;
    }

    panel_file_read( fd, (void*)buf, buf_size, &err_num );

    panel_file_close( fd, &err_num );

    return 0;
}

int32_t panel_set_ui_board_serial_num( const char* buf, uint32_t buf_size )
{
    int32_t err_num = 0;
    int32_t fd = 0;

    fd = panel_file_open( SERIAL_NUM_PATH, O_WRONLY | O_CREAT | O_TRUNC, 0666, &err_num );
    if( -1 == fd )
    {
        pi_log_e( "file [%s] open failed, err_num = %d\n", SERIAL_NUM_PATH, err_num );
        return -1;
    }

    panel_file_write( fd, (const void*)buf, buf_size, &err_num );

    panel_file_close( fd, &err_num );

    return 0;
}
 /**
  * @}
  */



