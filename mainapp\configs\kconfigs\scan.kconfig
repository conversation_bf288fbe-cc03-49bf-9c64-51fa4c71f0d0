# 扫描引擎类型
config SCAN_ENGINE_TYPE
    string "scan engine type"
    depends on SCAN
    default "baikal"
    help
        "scan engine type: baikal or kanas or ..." 

# 拉扫支持
config SCAN_PULL
    bool "scan pull"
    depends on SCAN && USBDEVICE
    default y
    help
        "scan pull support"

# 推扫支持
config SCAN_PUSH
    bool "scan push"
    depends on SCAN && USBDEVICE
    default y
    help
        "scan push support"

# 扫描到FTP支持
config SCAN_FTP
    bool "scan ftp"
    depends on SCAN && NET_FTP
    default y
    help
        "scan ftp support"

# 扫描到SMB支持
config SCAN_SMB
    bool "scan smb"
    depends on SCAN && NET
    default y
    help
        "scan smb support"

# 扫描到Email支持
config SCAN_EMAIL
    bool "scan email"
    depends on SCAN && NET_SMTP
    default y
    help
        "scan email support"

# 扫描到U盘支持
config SCAN_UDISK
    bool "scan udisk"
    depends on SCAN && USBDEVICE
    default y
    help
        "scan udisk support"

# 扫描硬盘缓冲支持
config SCAN_DISK_BUFFER
    bool "scan disk buffer"
    depends on SCAN
    help
        "scan disk buffer support"

# 分组扫描支持
config SCAN_GROUP
    bool "scan group"
    depends on SCAN
    default y
    help
        "scan group support"

# 扫描留底支持
config SCAN_BACKUP
    bool "scan backup"
    depends on SCAN
    help
        "scan backup support"

# id 扫描支持
config SCAN_ID
    bool "scan id"
    depends on SCAN
    default y
    help
        "scan id support"

# 混合扫描支持
config SCAN_MIX
    bool "scan mix"
    depends on SCAN
    default y
    help
        "scan mix support"

# 原稿方向支持
config SCAN_FILE_DIR
    bool "scan file dir"
    depends on SCAN
    default y
    help
        "scan file dir support"

# 扫描边缘清除支持
config SCAN_EDGE_CLEAN
    bool "scan edge clean"
    depends on SCAN
    default y
    help
        "scan edge clean support"

# 分离扫描支持
config SCAN_SEPARATE
    bool "scan separate"
    depends on SCAN
    default y
    help
        "scan separate support"

# 扫描时打印原稿支持
config PRINT_ON_SCAN
    bool "print on scan"
    depends on SCAN
    help
        "print on scan support"

# 复印时保存扫描原稿画像支持
config SCAN_ON_COPY
    bool "scan on copy"
    depends on SCAN
    default y
    help
        "scan on copy support"

# 扫描翻页方式支持
config SCAN_PAGE_TURNING_MODE
    bool "scan page turning mode"
    depends on SCAN
    default y
    help
        "scan page turning mode support"

# 非压缩式扫描支持
config SCAN_UNCOMPRESSED_MODE
    bool "scan uncompressed mode"
    depends on SCAN
    default y
    help
        "scan uncompressed mode support"

# 预扫描支持
config SCAN_PREVIEW
    bool "scan preview"
    depends on SCAN
    help
        "scan preview support"

# 书本扫描支持
config SCAN_BOOK
    bool "scan book"
    depends on SCAN
    help
        "scan book support"

# 设置扫描文件名支持
config SCAN_FILE_NAME
    bool "scan file name"
    depends on SCAN
    help
        "scan file name support"

# 设置扫描文档添加日期支持
config SCAN_TIME
    bool "scan time"
    depends on SCAN
    help
        "scan time support"

# 设置扫描文档添加页码支持
config SCAN_PAGINATION
    bool "scan pagination"
    depends on SCAN
    help
        "scan pagination support"

# 设置扫描文档添加印记支持
config SCAN_WATERMARK
    bool "scan watermark"
    depends on SCAN
    help
        "scan watermark support"

# 设置扫描文档添加页眉页脚支持
config SCAN_PAGE_HEADER_FOOTER
    bool "scan page header footer"
    depends on SCAN
    help
        "scan page header footer support"

# 扫描URL目的地支持
config SCAN_URL
    bool "scan url"
    depends on SCAN
    help
        "scan url support"
