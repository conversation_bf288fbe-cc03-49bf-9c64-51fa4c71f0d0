/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file std.h
 * @addtogroup std
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief std init 
 */
#ifndef __MODULES__STD__STD_H__
#define __MODULES__STD__STD_H__

#include <quickjs.h>

/**
 * @brief   add std
 * @param[in] *ctx :a heap space runtime handle
 * <AUTHOR> @date    2024-06-11
 */
void add_std(JSContext *ctx);

/**
 * @brief   destory std
 * @param[in] *ctx :a heap space runtime handle
 * <AUTHOR> @date    2024-06-11
 */
void destory_std(JSContext *ctx);

#endif // __MODULES__STD__STD_H__
/**
 * @}
 */
