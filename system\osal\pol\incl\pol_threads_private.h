/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_threads_private.h
 * @addtogroup pol
 * @{
 * @addtogroup threads
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal threads interface set
 */
#ifndef POL_THREADS_PRIVATE_H
#define POL_THREADS_PRIVATE_H

/**
 * @brief Define the number of TASKS/THREADS to support.
 */
#define PI_NUM_THREADS      160

/**
 * @brief Define the number of Mutexes to support.
 */
#define PI_NUM_MTXS         1024

/**
 * @brief Define the number of Semaphores to support.
 */
#define PI_NUM_SEMS         1024

/**
 * @brief Define the number of Queues to support.
 */
#define PI_NUM_QUEUES       1024

/**
 * @brief  threads module initialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_thread_prolog    (void);

/**
 * @brief  mutex module  initialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_mutex_prolog     (void);

/**
 * @brief  Semaphores module initialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_sem_prolog       (void);

/**
 * @brief  message module initialize
 * @return initialize result
 * @retval 0: if initialize success
 * @retval -1: if initialize fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pi_msgq_prolog      (void);

/**
 * @brief  threads module initialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
void    pi_thread_epilog    (void);

/**
 * @brief  mutex module deinitialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
void    pi_mutex_epilog     (void);

/**
 * @brief  Semaphores module initialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
void    pi_sem_epilog       (void);

/**
 * @brief  message module initialize
 * @return initialize result
 * @retval 0: if initialize success
 * <AUTHOR>
 * @data   2023-4-18
*/
void    pi_msgq_epilog      (void);

/**
 * @brief  create the thread
 * @param[in] entry: specific the threads executing routine
 * @param[in] stacksize: specific the threads stacksize
 * @param[in] stack: reserved
 * @param[in] priority: reserved
 * @param[in] arg: is passed as the threads routine sole arguments of entry()
 * @param[in] name: specific the threads name
 * @return the thread object pointer.
 * @retval != NULL: success
 * @retval == NULL: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
void*   pol_thread_create   (void* (*entry)(void *), int32_t size, void* stack, int32_t priority, void* arg, const char* name);

/**
 * @brief  destroy the thread
 * @param[in] os_thread: specific the thread object pointer to destroy
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_thread_destroy  (void* os_thread);

/**
 * @brief  destroy the current thread
 * @param[in] os_thread: specific the thread object pointer to detach
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_thread_detach   (void* os_thread);

/**
 * @brief  if the  thread is current thread
 * @param[in] os_thread: specific the thread object pointer
 * @return  result
 * @retval 1: is the self
 * @retval 0: not self
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_thread_is_self  (void* os_thread);

/**
 * @brief  create mutex
 * @return the mutex object pointer
 * @retval != NULL: success
 * @retval == NULL: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
void*   pol_mutex_create    (void);

/**
 * @brief  destroy the mutex
 * @param[in] os_mtx: pointer to mutex that is to be destroyed
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_mutex_destroy   (void* os_mtx);

/**
 * @brief   lock the mutex
 * @param[in] os_mtx: pointer to mutex that is to be lock
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_mutex_lock      (void* os_mtx);

/**
 * @brief   lock the mutex with timeout
 * @param[in] os_mtx: pointer to mutex that is to be lock
 * @param[in] secs:   waiting seconds
 * @param[in] usecs:  waiting microseconds
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_mutex_timedlock (void* os_mtx, int32_t secs, int32_t usecs);

/**
 * @brief   unlock the mutex
 * @param[in] os_mtx: pointer to mutex that is to be unlock
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_mutex_unlock    (void* os_mtx);

/**
 * @brief   create the Semaphores
 * @param[in] count:    specific the Semaphores number to create
 * @return the semaphore object pointer
 * @retval != NULL: success
 * @retval == NULL: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
void*   pol_sem_create      (int32_t count);

/**
 * @brief   destroy the Semaphore
 * @param[in] os_sem:   specific the semaphore to destroy
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_sem_destroy     (void* os_sem);

/**
 * @brief   lock the Semaphore
 * @param[in] os_sem:   specific the Semaphores to wait
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_sem_wait        (void* os_sem);

/**
 * @brief   lock the Semaphore with timeout
 * @param[in] os_sem:   specific the Semaphores to lock
 * @param[in] secs:     waiting seconds
 * @param[in] usecs:    wait microseconds
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_sem_timedwait   (void* os_sem, int32_t secs, int32_t usecs);

/**
 * @brief   unlock the Semaphore
 * @param[in] os_sem:   specific the Semaphores to unlock
 * @return operate result
 * @retval == 0: success
 * @retval != 0: fail
 * <AUTHOR>
 * @data   2023-4-18
*/
int32_t pol_sem_post        (void* os_sem);

#endif /* POL_THREADS_PRIVATE_H */
/**
 *@}
 */
