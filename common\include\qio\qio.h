/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio.h
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-25
 * @brief QIO public header file
 */
#ifndef __QIO_H__
#define __QIO_H__

#include "utilities/event_observer.h"
#include "pol/pol_socket.h"

#define QIO_POLL_READ           ( 1 )
#define QIO_POLL_WRITE          ( 2 )
#define QIO_POLL_EVENT          ( 4 )

#define QIOERR                  ( -101 )
#define QIOEOF                  ( -1 )

#define QIO_READABLE(q, t, u)   ( ((q) && (q)->poll)  ? ((q)->poll(q, QIO_POLL_READ, t, u))  : QIOERR )
#define QIO_WRITEABLE(q, t, u)  ( ((q) && (q)->poll)  ? ((q)->poll(q, QIO_POLL_WRITE, t, u)) : QIOERR )
#define QIO_READ(q, b, n)       ( ((q) && (q)->read)  ? ((q)->read(q, b, n))                 : QIOERR )
#define QIO_WRITE(q, b, n)      ( ((q) && (q)->write) ? ((q)->write(q, b, n))                : QIOERR )
#define QIO_SEEK(q, o, w)       ( ((q) && (q)->seek)  ? (q)->seek(q, o, w)                   : QIOERR )
#define QIO_CLOSE(q)            ( ((q) && (q)->close) ? ((q)->close(q))                      : QIOERR )

typedef struct qio_observer_obj
{
    EVENT_OBSERVER_S*   job_start_observer;
    EVENT_OBSERVER_S*   job_done_observer;
    EVENT_OBSERVER_S*   job_error_observer;
    EVENT_OBSERVER_S*   job_cancel_observer;
    EVENT_OBSERVER_S*   page_receive_observer;
    EVENT_OBSERVER_S*   page_done_observer;
    EVENT_OBSERVER_S*   page_count_observer;

	EVENT_OBSERVER_S*   airscan_close_qio_observer;
}
QIO_OBSERVER_S;

typedef struct qio_obj
{
    QIO_OBSERVER_S      observers;
    int32_t             (*close)    (struct qio_obj *);
    int32_t             (*poll)     (struct qio_obj *, int32_t, int32_t, int32_t);
    int32_t             (*read)     (struct qio_obj *, void *,  size_t);
    int32_t             (*write)    (struct qio_obj *, void *,  size_t);
    int32_t             (*seek)     (struct qio_obj *, ssize_t, int32_t);
    void*               priv;
}
QIO_S, *QIO_P;

typedef struct qio_data_offset_obj
{
    QIO_S*              m_qio;
    char*               m_data;
    int32_t             m_offset;
}
QIO_DATA_OFFSET_S, *QIO_DATA_OFFSET_P;

typedef void*   (*QIO_CLOSE_CALLBACK)  (void *);

/**
 * @brief       Create a file based QIO stream.  This is an upper level function which \n
 *              calls more specific creation functions depending on the system and \n
 *              the stream name.
 * @param[in]   stream  : the file stream name.
 * @param[in]   flags   : creation flags, refer the open function(man 2 open), eg. O_WRONLY, O_CREAT.
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_file_create             (const char* stream, int32_t flags);

/**
 * @brief       Create a memory-backed QIO.  This is a chunk of memory that can be accessed as \n
 *              a QIO object to provide read/write/seek access as if the object was a file.\n
 *              A QIOMEM object can grow (to n_max) as data is written.  Other QIO types can \n
 *              use a QIOMEM underneath for data buffering.\n
 *              If there is no initial data, a data area of size n_room is allocated. If the \n
 *              n_max parameter is NULL, the memory area is limited to the initial size.
 * @param[in]   data    : data area for file data, may be NULL.
 * @param[in]   n_room  : how large the data area is.
 * @param[in]   n_max   : how large the memory region is allowed to grow. may be 0.
 * @param[in]   n_valid : how many bytes in room are valid.
 * @param[in]   flags   : creation flags, refer the open function(man 2 open), eg. O_WRONLY, O_CREAT.
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_mem_create              (uint8_t* data, uint32_t n_room, uint32_t n_max, uint32_t n_valid, int32_t flags);

/**
 * @brief       Create a memory backed QIO on top of a buffer in the memory-backed file list.
 * @param[in]   name    : name of previously added file.
 * @param[in]   flags   : creation flags, refer the open function(man 2 open), eg. O_WRONLY, O_CREAT.
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_mem_create_from_name    (const char* name, int32_t flags);

/**
 * @brief       Set the contents of a file in the memory-backed file list.
 * @param[in]   name    : name of file to add.
 * @param[in]   bytes   : data area for file data.
 * @param[in]   n_room  : how large the data area is.
 * @param[in]   n_valid : how many bytes in room are valid.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_mem_set_file_contents   (const char* name, uint8_t* bytes, uint32_t n_room, uint32_t n_valid);

/**
 * @brief       Add a file to a the memory-backed file sytem.
 * @param[in]   name    : name of file to add.
 * @param[in]   bytes   : data area for file data.
 * @param[in]   n_room  : how large the data area is.
 * @param[in]   n_valid : how many bytes in room are valid.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_mem_add_file            (const char* name, uint8_t* bytes, uint32_t n_room, uint32_t n_valid);

/**
 * @brief       Remove a file to a the memory-backed file sytem.
 * @param[in]   name    : name of file to remove.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_mem_remove_file         (const char* name);

/**
 * @brief       Create a OFD creation stream, and builds a OFD wrapper around image \n
 *              data written to it. Use qio_ofd_begin_image to setup image dimensions, \n
 *              then write normally.
 * @param[in]   stream  : the file stream name.
 * @param[in]   flags   : creation flags, refer the open function(man 2 open), eg. O_WRONLY, O_CREAT.
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_ofd_create              (const char* stream, int32_t flags);

/**
 * @brief       Begin an image in a OFD stream creation filter. This sets the dimensions \n
 *              and format of the image being added to a OFD filter. See qio_ofd_create.
 * @param[in]   pqio    : QIO to write TIFF output to.
 * @param[in]   w       : image width.
 * @param[in]   h       : image height.
 * @param[in]   d       : image depth.
 * @param[in]   s       : image stride.
 * @param[in]   f       : image format.
 * @param[in]   xres    : x res of image, can be 0.
 * @param[in]   yres    : y res of image, can be 0.
 * @param[in]   clipx   : limit pdf output to this max width, can be 0.
 * @param[in]   clipy   : limit pdf output to this max height, can be 0.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_ofd_begin_image         (QIO_S* pqio, int32_t w, int32_t h, int32_t d, int32_t s, int32_t f, int32_t xres, int32_t yres, int32_t clipx, int32_t clipy);

/**
 * @brief       add image to QIOOFD stream.
 * @param[in]   pqio    : QIO to write OFD output to.
 * @param[in]   path    : filepath.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_ofd_add_image           (QIO_S* pqio, const char* path);

/**
 * @brief       Create a PDF creation stream. This QIO is a write-only stream filter \n
 *              which takes a QIO destination, and builds a PDF wrapper around image \n
 *              data written to it. Use qio_pdf_begin_image to setup image dimensions, \n
 *              then write normally.
 * @param[in]   pqio_dst: qio to write TIFF data to.
 * @param[in]   take_owner_ship: non-0 means QIOPDF now owns input QIO(to delete on close).
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_pdf_create              (QIO_S* pqio_dst, int32_t take_owner_ship);

/**
 * @brief       Begin an image in a PDF stream creation filter. This sets the dimensions \n
 *              and format of the image being added to a PDF filter. See qio_pdf_create.
 * @param[in]   pqio    : QIO to write TIFF output to.
 * @param[in]   w       : image width.
 * @param[in]   h       : image height.
 * @param[in]   d       : image depth.
 * @param[in]   s       : image stride.
 * @param[in]   f       : image format.
 * @param[in]   xres    : x res of image, can be 0.
 * @param[in]   yres    : y res of image, can be 0.
 * @param[in]   clipx   : limit pdf output to this max width, can be 0.
 * @param[in]   clipy   : limit pdf output to this max height, can be 0.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_pdf_begin_image         (QIO_S* pqio, const char* filter, int32_t w, int32_t h, int32_t d, int32_t s, int32_t f, int32_t xres, int32_t yres, int32_t clipx, int32_t clipy);

/**
 * @brief       recreate a QIOPDF object base a pdf filepath.
 * @param[in]   pqio    : QIO to write PDF output to.
 * @param[in]   path    : the PDF filepath.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_pdf_recreate_file       (QIO_S* pqio, char* path);

/**
 * @brief       Create a TIFF creation stream. This QIO is a write-only stream filter \n
 *              which takes a QIO destination, and builds a TIFF wrapper around image \n
 *              data written to it. Use qio_tiff_begin_image to setup image dimensions, \n
 *              then write normally.
 * @param[in]   pqio_dst: qio to write TIFF data to.
 * @param[in]   take_owner_ship: non-0 means QIOPDF now owns input QIO(to delete on close).
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_tiff_create             (QIO_S* pqio_dst, int32_t take_owner_ship);

/**
 * @brief       begin to generate TIFF image data.
 * @brief       Begin an image in a TIFF stream creation filter. This sets the dimensions \n
 *              and format of the image being added to a TIFF filter. See qio_tiff_create.
 * @param[in]   pqio    : QIO to write TIFF output to.
 * @param[in]   w       : image width.
 * @param[in]   h       : image height.
 * @param[in]   d       : image depth.
 * @param[in]   s       : image stride.
 * @param[in]   f       : image format.
 * @param[in]   xres    : x res of image, can be 0.
 * @param[in]   yres    : y res of image, can be 0.
 * @param[in]   clipx   : limit pdf output to this max width, can be 0.
 * @param[in]   clipy   : limit pdf output to this max height, can be 0.
 * @return      non-0 on error
 * @retval      == 0    : success\n
 *              != 0    : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
int32_t qio_tiff_begin_image        (QIO_S* pqio, int32_t w, int32_t h, int32_t d, int32_t s, int32_t f, int32_t xres, int32_t yres, int32_t clipx, int32_t clipy);

/**
 * @brief       Create a QIO stream on a serial port.
 * @param[in]   port    : which serial port.
 * @param[in]   baud    : baud rate to use.
 * @param[in]   bits    : bits.
 * @param[in]   stops   : stop bits.
 * @param[in]   parity  : parity 0-none, 1-odd, 2-even.
 * @param[in]   flow    : flow control (h/w).
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_tty_create              (int32_t port, int32_t baud, int32_t bits, int32_t stops, int32_t parity, int32_t flow);

/**
 * @brief       Create a QIO stream based on a connection on the USB device port.
 * @param[in]   dev_fd  : the USB device handle.
 * @param[in]   callback: the callback function when closing this USB device.
 * @param[in]   context : the parameter of this callback function.
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @data        2023-6-19
 */
QIO_S*  qio_usbd_create             (int32_t dev_fd, QIO_CLOSE_CALLBACK callback, void* context);

QIO_P QIOXPScreate(const char* filePath, int flags);
int QIOXPSbeginImage( QIO_P pqio,
                      int w, int h, int d, int s, int f,
                      int xres, int yres,
                      int clipx, int clipy);
int QIOXPSAddImage(const char* imgPath, int fd_dst);


/**
 * @brief       get context parameter from the USB device QIO pointer.
 * @param[in]   pqio    : The qio pointer.
 * @return      The context pointer.
 * <AUTHOR> Xin
 * @data        2023-7-13
 */
void*   qio_usbd_get_context        (QIO_S* pqio);

/**
 * @brief       Create a QIO stream based on POSIX share memory, for which only one producer
 *              write data to and only one consumer read data from, each on different process
 * @param[in]   name    : name specifies the shared memory object to be created or opened.
 *                        For portable use, a shared memory object should be identified by a name
 *                        of the form /somename; that is, a null-terminated string of  up  to  NAME_MAX
 *                        (i.e. 255) characters consisting of an initial slash, followed by
 *                        one or more characters, none of which are slashes.
 * @param[in]   length  : the length share memory will occupy, will be aligned up to page size.
 * @param[in]   oflag   : same as parameter oflag of shm_open
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> JiaMing
 * @data        2025-1-6
 */
QIO_S*  qio_shm_create              (const char* name, size_t length, int oflag);

#endif /* __QIO_H__ */
/**
 *@}
 */
