/**************************************************************
Copyright (c) 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:    prnsdk
file name:      prnsdk_status.h
author:         <PERSON>(<EMAIL>)
date:           2024-05-24
description:    system status map to prnsdk status
**************************************************************/
#include "prnsdk_status.h"
#include "nettypes.h"

#define SYSSTS_TO_PRNSTS_TABE(out)\
out(STATUS_E_PRINT_FRONT_DOOR_OPEN                                           ,1);\
out(STATUS_E_PRINT_SIDE_DOOR_OPEN                                            ,2);\
out(STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN                             ,3);\
out(STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN                                       ,4);\
out(STATUS_E_PRINT_FNS_TOP_COVER_OPEN                                        ,5);\
out(STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN                                    ,6);\
out(STATUS_E_PRINT_LCT_IN_OPEN                                               ,7);\
out(STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION                             ,8);\
out(STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION                             ,9);\
out(STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION                             ,10);\
out(STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION                             ,11);\
out(STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION                             ,12);\
out(STATUS_E_PRINT_JAM_TRAY_MANUAL_FEED_SECTION                              ,13);\
out(STATUS_E_PRINT_JAM_REFEEDER_SECTION                                      ,14);\
out(STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1                           ,15);\
out(STATUS_E_PRINT_JAM_LCT_EX_TRANSPORT_SECTION                              ,16);\
out(STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1                                ,17);\
out(STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1                             ,18);\
out(STATUS_E_PRINT_JAM_OUTPUT_SECTION                                        ,19);\
out(STATUS_E_PRINT_JAM_VER_TRANSPORT_SECTION2                                ,20);\
out(STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2                                 ,21);\
out(STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION                                    ,22);\
out(STATUS_E_PRINT_JAM_TRAY_1_SECTION                                        ,23);\
out(STATUS_E_PRINT_JAM_TRAY_2_SECTION                                        ,24);\
out(STATUS_E_PRINT_JAM_TRAY_3_SECTION                                        ,25);\
out(STATUS_E_PRINT_JAM_TRAY_4_SECTION                                        ,26);\
out(STATUS_E_PRINT_JAM_LCT_IN_SECTION                                        ,27);\
out(STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION                                   ,28);\
out(STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION                                   ,29);\
out(STATUS_E_PRINT_JAM_LCT_EX_SECTION                                        ,30);\
out(STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2                             ,31);\
out(STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_OFF                              ,32);\
out(STATUS_E_PRINT_JAM_SADDLE_STITCHER_EX_NOT_OFF                            ,33);\
out(STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON                                   ,34);\
out(STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF                            ,35);\
out(STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON                             ,36);\
out(STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF                            ,37);\
out(STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON                                  ,38);\
out(STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF                                 ,39);\
out(STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON                                  ,40);\
out(STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF                                 ,41);\
out(STATUS_E_PRINT_JAM_SADDLE_STITCHER_EN_NOT_ON                             ,42);\
out(STATUS_E_PRINT_JAM_SADDLE_EN_NOT_OFF                                     ,43);\
out(STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_NOT_ON                              ,44);\
out(STATUS_E_PRINT_JAM_PUNCH                                                 ,45);\
out(STATUS_E_PRINT_JAM_FNS_STAPLE                                            ,46);\
out(STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE				                 ,47);\
out(STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON                                    ,48);\
out(STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF                                   ,49);\
out(STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_SECTION                               ,50);\
out(STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION                             ,51);\
out(STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1                                    ,52);\
out(STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_SECTION                               ,53);\
out(STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION                                  ,54);\
out(STATUS_E_PRINT_JAM_FNS_HOR_TRANSPORT_SECTION                             ,55);\
out(STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION                               ,56);\
out(STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION                               ,57);\
out(STATUS_E_PRINT_CONTROL_JAM_1                                             ,58);\
out(STATUS_E_PRINT_CONTROL_JAM_2                                             ,59);\
out(STATUS_E_PRINT_CONTROL_JAM_3                                             ,60);\
out(STATUS_E_PRINT_CONTROL_JAM_4                                             ,61);\
out(STATUS_E_PRINT_CONTROL_JAM_5                                             ,62);\
out(STATUS_E_PRINT_CONTROL_JAM_6                                             ,63);\
out(STATUS_E_PRINT_CONTROL_JAM_7                                             ,64);\
out(STATUS_E_PRINT_CONTROL_JAM_8                                             ,65);\
out(STATUS_E_PRINT_CONTROL_JAM_9                                             ,66);\
out(STATUS_E_PRINT_Y_DR_UNINSTALL                                            ,67);\
out(STATUS_E_PRINT_C_DR_UNINSTALL                                            ,68);\
out(STATUS_E_PRINT_K_DR_UNINSTALL                                            ,69);\
out(STATUS_E_PRINT_Y_DV_UNINSTALL                                            ,70);\
out(STATUS_E_PRINT_C_DV_UNINSTALL                                            ,71);\
out(STATUS_E_PRINT_K_DV_UNINSTALL                                            ,72);\
out(STATUS_E_PRINT_W_TB_UNINSTALL                                            ,73);\
out(STATUS_E_PRINT_M_TB_UNINSTALL                                            ,74);\
out(STATUS_E_PRINT_Y_TONER_EMPTY                                             ,75);\
out(STATUS_E_PRINT_M_TONER_EMPTY                                             ,76);\
out(STATUS_E_PRINT_C_TONER_EMPTY                                             ,77);\
out(STATUS_E_PRINT_K_TONER_EMPTY                                             ,78);\
out(STATUS_E_PRINT_Y_DR_LIFE_STOP                                            ,79);\
out(STATUS_E_PRINT_M_DR_LIFE_STOP                                            ,80);\
out(STATUS_E_PRINT_C_DR_LIFE_STOP                                            ,81);\
out(STATUS_E_PRINT_K_DR_LIFE_STOP                                            ,82);\
out(STATUS_E_PRINT_FUSING_UNIT_LIFE_STOP                                     ,83);\
out(STATUS_E_PRINT_TRANSFER_BELT_UNIT_LIFE_STOP                              ,84);\
out(STATUS_E_PRINT_TRANSFER_ROLLER_UNIT_LIFE_STOP                            ,85);\
out(STATUS_E_PRINT_TONER_FILTER_LIFE_STOP                                    ,86);\
out(STATUS_E_PRINT_Y_DV_LIFE_STOP                                            ,87);\
out(STATUS_E_PRINT_M_DV_LIFE_STOP                                            ,88);\
out(STATUS_E_PRINT_C_DV_LIFE_STOP                                            ,89);\
out(STATUS_E_PRINT_K_DV_LIFE_STOP                                            ,90);\
out(STATUS_E_PRINT_TRAY_1_PAPER_EMPTY                                        ,91);\
out(STATUS_E_PRINT_TRAY_2_PAPER_EMPTY                                        ,92);\
out(STATUS_E_PRINT_TRAY_3_PAPER_EMPTY                                        ,93);\
out(STATUS_E_PRINT_TRAY_4_PAPER_EMPTY                                        ,94);\
out(STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY                                   ,95);\
out(STATUS_F_PRINT_A_31_2                                                    ,96);\
out(STATUS_F_PRINT_A_34_25                                                   ,97);\
out(STATUS_F_PRINT_A_38_22                                                   ,98);\
out(STATUS_F_PRINT_A_38_25                                                   ,99);\
out(STATUS_F_PRINT_A_37_22                                                   ,100);\
out(STATUS_F_PRINT_A_37_25                                                   ,101);\
out(STATUS_F_PRINT_A_37_26                                                   ,102);\
out(STATUS_F_PRINT_A_37_32                                                   ,103);\
out(STATUS_F_PRINT_A_37_36                                                   ,104);\
out(STATUS_F_PRINT_A_37_37                                                   ,105);\
out(STATUS_F_PRINT_A_37_38                                                   ,106);\
out(STATUS_F_PRINT_A_39_22                                                   ,107);\
out(STATUS_F_PRINT_A_39_25                                                   ,108);\
out(STATUS_F_PRINT_A_39_26                                                   ,109);\
out(STATUS_F_PRINT_A_39_2B                                                   ,110);\
out(STATUS_F_PRINT_A_3B_2                                                    ,111);\
out(STATUS_F_PRINT_A_3B_3                                                    ,112);\
out(STATUS_F_PRINT_A_3B_7                                                    ,113);\
out(STATUS_F_PRINT_A_3B_9                                                    ,114);\
out(STATUS_F_PRINT_B_51_3                                                    ,115);\
out(STATUS_F_PRINT_B_22_53                                                   ,116);\
out(STATUS_F_PRINT_B_22_54                                                   ,117);\
out(STATUS_F_PRINT_B_22_55                                                   ,118);\
out(STATUS_F_PRINT_B_22_56                                                   ,119);\
out(STATUS_F_PRINT_B_53_55                                                   ,120);\
out(STATUS_F_PRINT_B_53_60                                                   ,121);\
out(STATUS_F_PRINT_B_53_61                                                   ,122);\
out(STATUS_F_PRINT_B_53_4                                                    ,123);\
out(STATUS_F_PRINT_B_33_2                                                    ,124);\
out(STATUS_F_PRINT_B_23_50                                                   ,125);\
out(STATUS_F_PRINT_B_53_51                                                   ,126);\
out(STATUS_F_PRINT_B_53_6                                                    ,127);\
out(STATUS_F_PRINT_B_23_55                                                   ,128);\
out(STATUS_F_PRINT_B_32_1                                                    ,129);\
out(STATUS_F_PRINT_B_32_2                                                    ,130);\
out(STATUS_F_PRINT_B_1_6                                                     ,131);\
out(STATUS_F_PRINT_B_1_7                                                     ,132);\
out(STATUS_F_PRINT_B_1_8                                                     ,133);\
out(STATUS_F_PRINT_B_1_9                                                     ,134);\
out(STATUS_F_PRINT_B_21_52                                                   ,135);\
out(STATUS_F_PRINT_B_21_53                                                   ,136);\
out(STATUS_F_PRINT_B_21_54                                                   ,137);\
out(STATUS_F_PRINT_B_21_55                                                   ,138);\
out(STATUS_F_PRINT_B_21_56                                                   ,139);\
out(STATUS_F_PRINT_B_31_1                                                    ,140);\
out(STATUS_F_PRINT_B_31_03                                                   ,141);\
out(STATUS_F_PRINT_B_41_1                                                    ,142);\
out(STATUS_F_PRINT_B_45_1                                                    ,143);\
out(STATUS_F_PRINT_B_3B_8                                                    ,144);\
out(STATUS_F_PRINT_B_2_6                                                     ,145);\
out(STATUS_F_PRINT_B_2_4                                                     ,146);\
out(STATUS_F_PRINT_B_2_2                                                     ,147);\
out(STATUS_F_PRINT_B_2_16                                                    ,148);\
out(STATUS_F_PRINT_B_2_8                                                     ,149);\
out(STATUS_F_PRINT_B_2_11                                                    ,150);\
out(STATUS_F_PRINT_B_2_10                                                    ,151);\
out(STATUS_F_PRINT_B_2_14                                                    ,152);\
out(STATUS_F_PRINT_B_11_E1                                                   ,153);\
out(STATUS_F_PRINT_B_11_A1                                                   ,154);\
out(STATUS_F_PRINT_B_11_A2                                                   ,155);\
out(STATUS_F_PRINT_B_13_1                                                    ,156);\
out(STATUS_F_PRINT_B_25_51                                                   ,157);\
out(STATUS_F_PRINT_B_25_52                                                   ,158);\
out(STATUS_F_PRINT_B_25_53                                                   ,159);\
out(STATUS_F_PRINT_B_25_54                                                   ,160);\
out(STATUS_F_PRINT_B_25_55                                                   ,161);\
out(STATUS_F_PRINT_B_25_56                                                   ,162);\
out(STATUS_F_PRINT_B_25_57                                                   ,163);\
out(STATUS_F_PRINT_B_25_58                                                   ,164);\
out(STATUS_F_PRINT_B_25_59                                                   ,165);\
out(STATUS_F_PRINT_B_25_5A                                                   ,166);\
out(STATUS_F_PRINT_B_25_5B                                                   ,167);\
out(STATUS_F_PRINT_B_25_5C                                                   ,168);\
out(STATUS_F_PRINT_B_25_61                                                   ,169);\
out(STATUS_F_PRINT_B_25_62                                                   ,170);\
out(STATUS_F_PRINT_B_25_63                                                   ,171);\
out(STATUS_F_PRINT_B_25_64                                                   ,172);\
out(STATUS_F_PRINT_B_11_2                                                    ,173);\
out(STATUS_F_PRINT_B_11_3                                                    ,174);\
out(STATUS_F_PRINT_B_11_5                                                    ,175);\
out(STATUS_F_PRINT_B_11_6                                                    ,176);\
out(STATUS_F_PRINT_B_11_9                                                    ,177);\
out(STATUS_F_PRINT_B_11_12                                                   ,178);\
out(STATUS_F_PRINT_B_11_13                                                   ,179);\
out(STATUS_F_PRINT_B_11_14                                                   ,180);\
out(STATUS_F_PRINT_B_11_15                                                   ,181);\
out(STATUS_F_PRINT_B_11_32                                                   ,182);\
out(STATUS_F_PRINT_B_11_40                                                   ,183);\
out(STATUS_F_PRINT_B_11_41                                                   ,184);\
out(STATUS_F_PRINT_B_11_44                                                   ,185);\
out(STATUS_F_PRINT_B_11_45                                                   ,186);\
out(STATUS_F_PRINT_B_11_56                                                   ,187);\
out(STATUS_F_PRINT_B_11_84                                                   ,188);\
out(STATUS_F_PRINT_B_11_95                                                   ,189);\
out(STATUS_F_PRINT_B_11_96                                                   ,190);\
out(STATUS_F_PRINT_B_11_97                                                   ,191);\
out(STATUS_F_PRINT_B_24_11                                                   ,192);\
out(STATUS_F_PRINT_B_24_12                                                   ,193);\
out(STATUS_F_PRINT_B_24_13                                                   ,194);\
out(STATUS_F_PRINT_B_24_14                                                   ,195);\
out(STATUS_F_PRINT_B_2A_11                                                   ,196);\
out(STATUS_F_PRINT_B_2A_12                                                   ,197);\
out(STATUS_F_PRINT_B_2A_13                                                   ,198);\
out(STATUS_F_PRINT_B_2A_14                                                   ,199);\
out(STATUS_F_PRINT_B_51_2                                                    ,200);\
out(STATUS_F_PRINT_C_39_2A                                                   ,201);\
out(STATUS_F_PRINT_C_C1_63                                                   ,202);\
out(STATUS_F_PRINT_C_40_A1                                                   ,203);\
out(STATUS_F_PRINT_C_40_A2                                                   ,204);\
out(STATUS_F_PRINT_C_40_A3                                                   ,205);\
out(STATUS_F_PRINT_C_40_A4                                                   ,206);\
out(STATUS_F_PRINT_C_10_4                                                    ,207);\
out(STATUS_F_PRINT_C_10_81                                                   ,208);\
out(STATUS_F_PRINT_C_2A_21                                                   ,209);\
out(STATUS_F_PRINT_C_2A_22                                                   ,210);\
out(STATUS_F_PRINT_C_2A_23                                                   ,211);\
out(STATUS_F_PRINT_C_2A_24                                                   ,212);\
out(STATUS_F_PRINT_C_26_50                                                   ,213);\
out(STATUS_F_PRINT_C_26_51                                                   ,214);\
out(STATUS_F_PRINT_C_26_52                                                   ,215);\
out(STATUS_F_PRINT_C_26_53                                                   ,216);\
out(STATUS_F_PRINT_C_26_54                                                   ,217);\
out(STATUS_F_PRINT_C_2A_01                                                   ,218);\
out(STATUS_F_PRINT_C_2A_02                                                   ,219);\
out(STATUS_F_PRINT_C_2A_03                                                   ,220);\
out(STATUS_F_PRINT_C_2A_04                                                   ,221);\
out(STATUS_F_PRINT_C_D7_01                                                   ,222);\
out(STATUS_F_PRINT_C_D7_02                                                   ,223);\
out(STATUS_F_PRINT_C_D7_03                                                   ,224);\
out(STATUS_F_PRINT_C_D7_04                                                   ,225);\
out(STATUS_F_PRINT_C_D7_05                                                   ,226);\
out(STATUS_F_PRINT_C_D7_06                                                   ,227);\
out(STATUS_F_PRINT_C_56_1                                                    ,228);\
out(STATUS_F_PRINT_C_56_6                                                    ,229);\
out(STATUS_F_PRINT_C_56_10                                                   ,230);\
out(STATUS_F_PRINT_C_14_2                                                    ,231);\
out(STATUS_F_PRINT_C_C1_55                                                   ,232);\
out(STATUS_F_PRINT_C_C1_5B                                                   ,233);\
out(STATUS_E_PRINT_WASTE_TONER_FULL                                          ,234);\
out(STATUS_E_PRINT_Y_TB_MISMATCH                                             ,235);\
out(STATUS_E_PRINT_M_TB_MISMATCH                                             ,236);\
out(STATUS_E_PRINT_C_TB_MISMATCH                                             ,237);\
out(STATUS_E_PRINT_K_TB_MISMATCH                                             ,238);\
out(STATUS_E_PRINT_Y_DR_MISMATCH                                             ,239);\
out(STATUS_E_PRINT_M_DR_MISMATCH                                             ,240);\
out(STATUS_E_PRINT_C_DR_MISMATCH                                             ,241);\
out(STATUS_E_PRINT_K_DR_MISMATCH                                             ,242);\
out(STATUS_E_PRINT_LCT_EX_OPEN                                               ,243);\
out(STATUS_E_PRINT_LCT_EX_PAPER_EMPTY                                        ,244);\
out(STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION                              ,245);\
out(STATUS_E_PRINT_EMMC_ERROR                                                ,246);\
out(STATUS_E_PRINT_ENGINE_COMMUNICATION_FAILED                               ,247);\
out(STATUS_E_PRINT_VIDEO                                                     ,248);\
/*TODO
out(SYS_STATUS_FB_INTERNAL_ERROR_1                                           ,249);\
out(SYS_STATUS_FB_INTERNAL_ERROR_2                                           ,250);\
out(SYS_STATUS_FB_INTERNAL_ERROR_3                                           ,251);\
out(SYS_STATUS_FB_INTERNAL_ERROR_4                                           ,252);\
out(SYS_STATUS_FB_INTERNAL_ERROR_5                                           ,253);\
out(SYS_STATUS_FB_INTERNAL_ERROR_6                                           ,254);\
out(SYS_STATUS_FB_INTERNAL_ERROR_7                                           ,255);\
out(SYS_STATUS_FB_INTERNAL_ERROR_8                                           ,256);\
out(SYS_STATUS_FB_INTERNAL_ERROR_9                                           ,257);\
out(SYS_STATUS_FB_INTERNAL_ERROR_10                                          ,258);\
out(SYS_STATUS_FB_INTERNAL_ERROR_11                                          ,259);\
out(SYS_STATUS_FB_INTERNAL_ERROR_12                                          ,260);\
out(SYS_STATUS_FB_INTERNAL_ERROR_13                                          ,261);\
out(SYS_STATUS_FB_INTERNAL_ERROR_14                                          ,262);\
out(SYS_STATUS_FB_INTERNAL_ERROR_15                                          ,263);\
out(SYS_STATUS_FB_INTERNAL_ERROR_16                                          ,264);\
out(SYS_STATUS_FB_INTERNAL_ERROR_17                                          ,265);\
out(SYS_STATUS_FB_INTERNAL_ERROR_18                                          ,266);\
out(SYS_STATUS_ADF_INTERNAL_ERROR_1                                          ,267);\
out(SYS_STATUS_ADF_INTERNAL_ERROR_2                                          ,268);\
out(SYS_STATUS_ADF_INTERNAL_ERROR_3                                          ,269);\
out(SYS_STATUS_ADF_INTERNAL_ERROR_4                                          ,270);\
out(SYS_STATUS_CIS_COVER_ERR                                                 ,271);\
*/                                                                                 \
out(STATUS_I_COPY_CANCELING                                                  ,272);\
out(STATUS_I_PRINT_CANCELING                                                 ,272);\
out(STATUS_I_SCAN_CANCELING                                                  ,272);\
out(STATUS_E_PRINT_Y_TB_UNINSTALL                                            ,273);\
out(STATUS_E_PRINT_C_TB_UNINSTALL                                            ,274);\
out(STATUS_E_PRINT_K_TB_UNINSTALL                                            ,275);\

#define CASE_FUNCTION(a,b)    case a: NET_DEBUG("a %X  ->  b %d\n",a, b); prnsts = b;break

uint32_t syssts_map_prnsts(int syssts_value)
{
    uint32_t prnsts = 0;

    switch ( syssts_value )
    {
        SYSSTS_TO_PRNSTS_TABE(CASE_FUNCTION)
        default: break;
    }

    return prnsts;
}
