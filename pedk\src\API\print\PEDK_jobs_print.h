#ifndef _PEDK_JOBS_PRINT_
#define _PEDK_JOBS_PRINT_

#include <quickjs.h>

/**
 * @brief duplex mode type
 *
 */
typedef enum
{

   ePRINT_PEDK_DUPLEX_MODE_OFF = 0,                                                     //0：关闭
   ePRINT_PEDK_DUPLEX_MODE_AUTO,                                                        //1：自动双面
   ePRINT_PEDK_DUPLEX_MODE_MANUALE,                                                     //2：手动双面
   ePRINT_PEDK_DUPLEX_MODE_MAX,
   ePRINT_PEDK_DUPLEX_MODE_INVALID = 0xFF,
}EDUPLEX_PRINT_MODE;

/**
 * @brief image orientation
 *
 */
typedef enum
{
    eIMAGE_ORIENTATION_VERTIAL = 1,                                                     ///< vertial
    eIMAGE_ORIENTATION_HORIZONTAL,                                                      ///< internal page print
    eIMAGE_ORIENTATION_MAX,                                                             ///< max num
    eIMAGE_ORIENTATION_INVALID = 0xFF,                                                  ///< invalid
}EIMAGE_ORIENTATION_MODE;

/**
 * @brief tray in
 *
 */
typedef enum
{
    ePEDK_TRAY_AUTO  = 0,                                                               ///< TRAY_AUTO
    ePEDK_TRAY_MULTI,                                                                   ///< TRAY_MULTI
    ePEDK_TRAY_STANDARD,                                                                ///< STANDARD
    ePEDK_TRAY_OPENTION1,                                                               ///< TRAY_OPENTION1
    ePEDK_TRAY_OPENTION2,                                                               ///< TRAY_OPENTION2
    ePEDK_TRAY_OPENTION3,                                                               ///< TRAY_OPENTION3
    ePEDK_TRAY_OPENTION4,                                                               ///< TRAY_OPENTION4
    ePEDK_TRAY_OPENTION5,                                                               ///< TRAY_OPENTION5
    ePEDK_TRAY_MAX,                                                                     ///< MAX
    ePEDK_TRAY_INVALID = 0xFF,                                                          ///< invalid
}EPEDK_TRAY_IN;


/**
 * @brief paper save mode
 *
 */
typedef enum
{
    ePEDK_PAPER_SAVE_MODE_OFF = 0,                                                      ///< 关闭
    ePEDK_PAPER_SAVE_MODE_2IN1,                                                         ///< 2合1
    ePEDK_PAPER_SAVE_MODE_4IN1,                                                         ///< 4合1
    ePEDK_PAPER_SAVE_MODE_8IN1,                                                         ///< 8合1
    ePEDK_PAPER_SAVE_MODE_9IN1,                                                         ///< 9合1
    ePEDK_PAPER_SAVE_MODE_16IN1,                                                        ///< 16合1
    ePEDK_PAPER_SAVE_MODE_MAX,                                                          ///< MAX
    ePEDK_PAPER_SAVE_MODE_INVALID,                                                      ///< invalid
}EPEDK_PAPER_SAVE_MODE;


typedef enum
{
    ePRINT_PEDK_INTERNAL_PAGE_DEMOE = 1,                                                ///< 1：Demo页
    ePRINT_PEDK_INTERNAL_PAGE_MENU_STRUCT = 2,                                          ///< 2：菜单结构
    ePRINT_PEDK_INTERNAL_PAGE_PRINTER_INFO = 3,                                         ///< 3：打印机信息页
    ePRINT_PEDK_INTERNAL_PAGE_NET_CONFIG = 4,                                           ///< 4：网络配置页
    ePRINT_PEDK_INTERNAL_PAGE_WIFI_SSID_LIST = 5,                                       ///< 5：WIFI热点页
    ePRINT_PEDK_INTERNAL_PAGE_WIFI_GUIDE = 6,                                           ///< 6：WIFI向导页
    ePRINT_PEDK_INTERNAL_PAGE_EMAIL_ADDRESS_LIST = 7,                                   ///< 7：邮件地址列表
    ePRINT_PEDK_INTERNAL_PAGE_EMAIL_GROUP_LIST = 8,                                     ///< 8：邮件群组列表
    ePRINT_PEDK_INTERNAL_PAGE_FTP_LIST = 9,                                             ///< 9：FTP列表
    ePRINT_PEDK_INTERNAL_PAGE_MAX = 10,                                                 ///< 10：MAX
}EPRTNT_PEDK_INTERNAL_PAGE_TYPE;

/*
    声明 QuickJS C 函数初始化
*/

int js_print_init(JSContext *ctx, JSValueConst global);

#endif /* _PEDK_JOBS_PRINT_ */
