/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netdata.c
 * @addtogroup net
 * @{
 * @addtogroup netdata
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Network data manager API
 */
#include "moduleid.h"
#include "nvram.h"
#include "nettypes.h"
#include "netdata.h"

#define NVRAM_GET       pi_nvram_get
#define NVRAM_SET       pi_nvram_set

typedef struct bytes_buffer
{
    size_t  length;
    void*   buffer;
}
BYTES_BUF_S;

struct network_data_manager
{
    PI_MUTEX_T  mutex;
#define out1(_member, _nvid, _i)                 uint32_t    _member[_i];
#define out2(_member, _nvid)                     uint32_t    _member;
#define out3(_member, _nvid, _i, _s)             char        _member[_i][_s];
#define out4(_member, _nvid, _s)                 char        _member[_s];
#define out5(_member, _nvid, _s)                 BYTES_BUF_S _member;
    NET_DATA_MEMBER_TABLE(out1, out2, out3, out4, out5)
#undef  out1
#undef  out2
#undef  out3
#undef  out4
#undef  out5
};

#define NET_DATA_GET_UINT32_ARRAY(member)                                                               \
    uint32_t netdata_get_ ##member (DATA_MGR_S* thiz, uint32_t index)                                   \
    {                                                                                                   \
        uint32_t val;                                                                                   \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || index >= ARRAY_SIZE(thiz->member), NET_WARN, 0);                  \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        val = thiz->member[index];                                                                      \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return val;                                                                                     \
    }

#define NET_DATA_GET_UINT32_VALUE(member)                                                               \
    uint32_t netdata_get_ ##member (DATA_MGR_S* thiz)                                                   \
    {                                                                                                   \
        uint32_t val;                                                                                   \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL, NET_WARN, 0);                                                       \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        val = thiz->member;                                                                             \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return val;                                                                                     \
    }

#define NET_DATA_GET_STRING_ARRAY(member)                                                               \
    int32_t netdata_get_ ##member (DATA_MGR_S* thiz, uint32_t index, char* str, size_t size)            \
    {                                                                                                   \
        int32_t len;                                                                                    \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || str == NULL || index >= ARRAY_SIZE(thiz->member), NET_WARN, -1);  \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        len = snprintf(str, size, "%s", thiz->member[index]);                                           \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return len;                                                                                     \
    }

#define NET_DATA_GET_STRING_VALUE(member)                                                               \
    int32_t netdata_get_ ##member (DATA_MGR_S* thiz, char* str, size_t size)                            \
    {                                                                                                   \
        int32_t len;                                                                                    \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || str == NULL, NET_WARN, -1);                                       \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        len = snprintf(str, size, "%s", thiz->member);                                                  \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return len;                                                                                     \
    }

#define NET_DATA_GET_BUFFER_BYTES(member)                                                               \
    int32_t netdata_get_ ##member (DATA_MGR_S* thiz, void* buf, size_t size)                            \
    {                                                                                                   \
        int32_t ret = 0;                                                                                \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || buf == NULL || size == 0, NET_WARN, -1);                          \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        if ( thiz->member.buffer == NULL || thiz->member.length == 0 )                                  \
        {                                                                                               \
            memset(buf, 0, size);                                                                       \
        }                                                                                               \
        else if ( thiz->member.length <= size )                                                         \
        {                                                                                               \
            memcpy(buf, thiz->member.buffer, thiz->member.length);                                      \
        }                                                                                               \
        else                                                                                            \
        {                                                                                               \
            NET_WARN("bufsize(%u), need size(%u)", size, thiz->member.length);                          \
            ret = -1;                                                                                   \
        }                                                                                               \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return ret;                                                                                     \
    }

#define out1(_member, _nvid, _i)        NET_DATA_GET_UINT32_ARRAY(_member)
#define out2(_member, _nvid)            NET_DATA_GET_UINT32_VALUE(_member)
#define out3(_member, _nvid, _i, _s)    NET_DATA_GET_STRING_ARRAY(_member)
#define out4(_member, _nvid, _s)        NET_DATA_GET_STRING_VALUE(_member)
#define out5(_member, _nvid, _s)        NET_DATA_GET_BUFFER_BYTES(_member)
NET_DATA_MEMBER_TABLE(out1, out2, out3, out4, out5)
#undef  out1
#undef  out2
#undef  out3
#undef  out4
#undef  out5

#define NET_DATA_SET_UINT32_ARRAY(member, nvid)                                                         \
    int32_t netdata_set_ ##member (DATA_MGR_S* thiz, uint32_t index, uint32_t val)                      \
    {                                                                                                   \
        int32_t ret = -1;                                                                               \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || index >= ARRAY_SIZE(thiz->member), NET_WARN, -1);                 \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        NET_TRACE(#member"[%u]: %u to %u", index, thiz->member[index], val);                            \
        if ( thiz->member[index] != val )                                                               \
        {                                                                                               \
            thiz->member[index] = val;                                                                  \
            if ( nvid > 0 )                                                                             \
            {                                                                                           \
                NVRAM_SET(nvid, VTYPE_STRUCT, (void *)(thiz->member), sizeof(thiz->member), 0, NULL);   \
            }                                                                                           \
            ret = 0;                                                                                    \
        }                                                                                               \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return ret;                                                                                     \
    }

#define NET_DATA_SET_UINT32_VALUE(member, nvid)                                                         \
    int32_t netdata_set_ ##member (DATA_MGR_S* thiz, uint32_t val)                                      \
    {                                                                                                   \
        int32_t ret = -1;                                                                               \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL, NET_WARN, -1);                                                      \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        NET_TRACE(#member": %u to %u", thiz->member, val);                                              \
        if ( thiz->member != val )                                                                      \
        {                                                                                               \
            thiz->member = val;                                                                         \
            if ( nvid > 0 )                                                                             \
            {                                                                                           \
                NVRAM_SET(nvid, VTYPE_UINT, (void *)val, 0, 0, NULL);                                   \
            }                                                                                           \
            ret = 0;                                                                                    \
        }                                                                                               \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return ret;                                                                                     \
    }

#define NET_DATA_SET_STRING_ARRAY(member, nvid)                                                         \
    int32_t netdata_set_ ##member (DATA_MGR_S* thiz, uint32_t index, const char* str)                   \
    {                                                                                                   \
        int32_t ret = -1;                                                                               \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || str == NULL || index >= ARRAY_SIZE(thiz->member), NET_WARN, -1);  \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        NET_TRACE(#member"[%u]: \"%s\" to \"%s\"", index, thiz->member[index], str);                    \
        if ( strcmp(thiz->member[index], str) != 0 )                                                    \
        {                                                                                               \
            snprintf(thiz->member[index], sizeof(thiz->member[index]), "%s", str);                      \
            if ( nvid > 0 )                                                                             \
            {                                                                                           \
                NVRAM_SET(nvid, VTYPE_STRUCT, (void *)(thiz->member), sizeof(thiz->member), 0, NULL);   \
            }                                                                                           \
            ret = 0;                                                                                    \
        }                                                                                               \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return ret;                                                                                     \
    }

#define NET_DATA_SET_STRING_VALUE(member, nvid)                                                         \
    int32_t netdata_set_ ##member (DATA_MGR_S* thiz, const char* str)                                   \
    {                                                                                                   \
        int32_t ret = -1;                                                                               \
                                                                                                        \
        RETURN_VAL_IF(thiz == NULL || str == NULL, NET_WARN, -1);                                       \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        NET_TRACE(#member": \"%s\" to \"%s\"", thiz->member, str);                                      \
        if ( strcmp(thiz->member, str) != 0 )                                                           \
        {                                                                                               \
            snprintf(thiz->member, sizeof(thiz->member), "%s", str);                                    \
            if ( nvid > 0 )                                                                             \
            {                                                                                           \
                NVRAM_SET(nvid, VTYPE_STRING, (void *)str, strlen(str), 0, NULL);                       \
            }                                                                                           \
            ret = 0;                                                                                    \
        }                                                                                               \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return ret;                                                                                     \
    }

#define NET_DATA_SET_BUFFER_BYTES(member, nvid)                                                         \
    int32_t netdata_set_ ##member (DATA_MGR_S* thiz, void* buf, size_t size)                            \
    {                                                                                                   \
        RETURN_VAL_IF(thiz == NULL || buf == NULL || size == 0, NET_WARN, -1);                          \
                                                                                                        \
        pi_mutex_lock(thiz->mutex);                                                                     \
        if ( thiz->member.buffer != NULL && thiz->member.length != size )                               \
        {                                                                                               \
            pi_free(thiz->member.buffer);                                                               \
            thiz->member.buffer = NULL;                                                                 \
        }                                                                                               \
        if ( thiz->member.buffer == NULL )                                                              \
        {                                                                                               \
            thiz->member.buffer = pi_malloc(size);                                                      \
            thiz->member.length = size;                                                                 \
        }                                                                                               \
        memcpy(thiz->member.buffer, buf, size);                                                         \
        if ( nvid > 0 )                                                                                 \
        {                                                                                               \
            NVRAM_SET(nvid, VTYPE_STRUCT, buf, size, 0, NULL);                                          \
        }                                                                                               \
        pi_mutex_unlock(thiz->mutex);                                                                   \
                                                                                                        \
        return 0;                                                                                       \
    }

#define out1(_member, _nvid, _i)        NET_DATA_SET_UINT32_ARRAY(_member, _nvid)
#define out2(_member, _nvid)            NET_DATA_SET_UINT32_VALUE(_member, _nvid)
#define out3(_member, _nvid, _i, _s)    NET_DATA_SET_STRING_ARRAY(_member, _nvid)
#define out4(_member, _nvid, _s)        NET_DATA_SET_STRING_VALUE(_member, _nvid)
#define out5(_member, _nvid, _s)        NET_DATA_SET_BUFFER_BYTES(_member, _nvid)
NET_DATA_MEMBER_TABLE(out1, out2, out3, out4, out5)
#undef  out1
#undef  out2
#undef  out3
#undef  out4
#undef  out5

#define NET_DATA_INIT_UINT32_ARRAY(member, nvid)                                                        \
{                                                                                                       \
    NVRAM_GET(nvid, VTYPE_STRUCT, thiz->member, sizeof(thiz->member));                                  \
    for ( size_t i = 0; i < ARRAY_SIZE(thiz->member); ++i )                                             \
    {                                                                                                   \
        NET_DEBUG(#member"[%u](%u)", i, thiz->member[i]);                                               \
    }                                                                                                   \
}

#define NET_DATA_INIT_UINT32_VALUE(member, nvid)                                                        \
{                                                                                                       \
    NVRAM_GET(nvid, VTYPE_UINT, &(thiz->member), sizeof(thiz->member));                                 \
    NET_DEBUG(#member"(%u)", thiz->member);                                                             \
}

#define NET_DATA_INIT_STRING_ARRAY(member, nvid)                                                        \
{                                                                                                       \
    NVRAM_GET(nvid, VTYPE_STRUCT, thiz->member, sizeof(thiz->member));                                  \
    for ( size_t i = 0; i < ARRAY_SIZE(thiz->member); ++i )                                             \
    {                                                                                                   \
        if ( strlen(thiz->member[i]) < 1024 )                                                           \
        {                                                                                               \
            NET_DEBUG(#member"[%u](%s)", i, thiz->member[i]);                                           \
        }                                                                                               \
    }                                                                                                   \
}

#define NET_DATA_INIT_STRING_VALUE(member, nvid)                                                        \
{                                                                                                       \
    NVRAM_GET(nvid, VTYPE_STRING, thiz->member, sizeof(thiz->member));                                  \
    if ( strlen(thiz->member) < 1024 )                                                                  \
    {                                                                                                   \
        NET_DEBUG(#member"(%s)", thiz->member);                                                         \
    }                                                                                                   \
}

#define NET_DATA_INIT_BUFFER_BYTES(member, nvid, size)                                                  \
{                                                                                                       \
    if ( size > 0 )                                                                                     \
    {                                                                                                   \
        thiz->member.buffer = pi_zalloc(size);                                                          \
        if ( thiz->member.buffer != NULL )                                                              \
        {                                                                                               \
            NVRAM_GET(nvid, VTYPE_STRUCT, thiz->member.buffer, size);                                   \
            thiz->member.length = size;                                                                 \
        }                                                                                               \
        else                                                                                            \
        {                                                                                               \
            NET_WARN("alloc "#member" failed: %d<%s>", errno, strerror(errno));                         \
        }                                                                                               \
    }                                                                                                   \
    else                                                                                                \
    {                                                                                                   \
        NET_WARN("invalid buffer size(%d) for "#member, (int32_t)size);                                 \
    }                                                                                                   \
}

/**
 * @brief       The initialization data is set up after the factory is restored.
 * @param[in]   thiz    : The DATA_MGR_S object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-25
 */
static void netdata_init_factory_status(DATA_MGR_S* thiz)
{
    if ( thiz->factory_status == 0 )
    {
        thiz->iface_switch[IFACE_ID_ETH] = 1;
        thiz->ipv6_switch[IFACE_ID_ETH]  = 1;
        thiz->ipv6_usedhcp[IFACE_ID_ETH] = 1;
        thiz->ipv4_usedhcp[IFACE_ID_ETH] = 1;
        thiz->ipv4_autodns[IFACE_ID_ETH] = 1;
#if CONFIG_NET_WIFI
        thiz->iface_switch[IFACE_ID_STA] = 0;
        thiz->ipv6_switch[IFACE_ID_STA]  = 1;
        thiz->ipv6_usedhcp[IFACE_ID_STA] = 1;
        thiz->ipv4_usedhcp[IFACE_ID_STA] = 1;
        thiz->ipv4_autodns[IFACE_ID_STA] = 1;
        thiz->iface_switch[IFACE_ID_WFD] = 0;
        thiz->ipv6_switch[IFACE_ID_WFD]  = 0;
        thiz->ipv6_usedhcp[IFACE_ID_WFD] = 0;
        thiz->ipv4_usedhcp[IFACE_ID_WFD] = 1;
        thiz->ipv4_autodns[IFACE_ID_WFD] = 1;
#endif /* CONFIG_NET_WIFI */
        NVRAM_SET(NETWORK_ID_IFACE_SWITCH, VTYPE_STRUCT, (void *)(thiz->iface_switch), sizeof(thiz->iface_switch), 0, NULL);
        NVRAM_SET(NETWORK_ID_IPV6_SWITCH,  VTYPE_STRUCT, (void *)(thiz->ipv6_switch),  sizeof(thiz->ipv6_switch),  0, NULL);
        NVRAM_SET(NETWORK_ID_IPV6_USEDHCP, VTYPE_STRUCT, (void *)(thiz->ipv6_usedhcp), sizeof(thiz->ipv6_usedhcp), 0, NULL);
        NVRAM_SET(NETWORK_ID_IPV4_USEDHCP, VTYPE_STRUCT, (void *)(thiz->ipv4_usedhcp), sizeof(thiz->ipv4_usedhcp), 0, NULL);
        NVRAM_SET(NETWORK_ID_IPV4_AUTODNS, VTYPE_STRUCT, (void *)(thiz->ipv4_autodns), sizeof(thiz->ipv4_autodns), 0, NULL);

        NVRAM_SET(NETWORK_ID_FACTORY_STATUS, VTYPE_UINT, (void *)1,                    0,                          0, NULL);
        NET_DEBUG("The factory parameters of network module are initialized!");
    }
}

IFACE_ID_E netdata_get_ifid_by_ipv4_addr(DATA_MGR_S* thiz, const char* ipstr)
{
    IFACE_ID_E ifid;

    RETURN_VAL_IF(thiz == NULL || STRING_IS_EMPTY(ipstr), NET_WARN, IFACE_ID_ANY);

    pi_mutex_lock(thiz->mutex);
    for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        BREAK_IF(strcmp(ipstr, thiz->ipv4_addr[ifid]) == 0, NET_NONE);
    }
    pi_mutex_unlock(thiz->mutex);

    return ( ifid < IFACE_ID_NUM ? ifid : IFACE_ID_ANY );
}

IFACE_ID_E netdata_get_ifid_by_ipv6_addr(DATA_MGR_S* thiz, const char* ipstr)
{
    IFACE_ID_E ifid;

    RETURN_VAL_IF(thiz == NULL || STRING_IS_EMPTY(ipstr), NET_WARN, IFACE_ID_ANY);

    pi_mutex_lock(thiz->mutex);
    for ( ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        BREAK_IF(strcasecmp(ipstr, thiz->ipv6_link[ifid]) == 0, NET_NONE);
        BREAK_IF(strcasecmp(ipstr, thiz->ipv6_stls[ifid]) == 0, NET_NONE);
        BREAK_IF(strcasecmp(ipstr, thiz->ipv6_site[ifid]) == 0, NET_NONE);
    }
    pi_mutex_unlock(thiz->mutex);

    return ( ifid < IFACE_ID_NUM ? ifid : IFACE_ID_ANY );
}

uint32_t netdata_get_netlink_flags(DATA_MGR_S* thiz)
{
    uint32_t flags = 0;

    RETURN_VAL_IF(thiz == NULL, NET_WARN, 0);

    pi_mutex_lock(thiz->mutex);
    for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        flags |= ((!!thiz->iface_running[ifid]) << ifid);
    }
    pi_mutex_unlock(thiz->mutex);

    return flags;
}

void netdata_manager_destroy(DATA_MGR_S* thiz)
{
    if ( thiz )
    {
        pi_mutex_lock(thiz->mutex);
#define out1(_member, _nvid, _i)        {}
#define out2(_member, _nvid)            {}
#define out3(_member, _nvid, _i, _s)    {}
#define out4(_member, _nvid, _s)        {}
#define out5(_member, _nvid, _s)        { pi_free(thiz->_member.buffer); }
        NET_DATA_MEMBER_TABLE(out1, out2, out3, out4, out5);
#undef  out1
#undef  out2
#undef  out3
#undef  out4
#undef  out5
        pi_mutex_unlock(thiz->mutex);

        pi_mutex_destroy(thiz->mutex);
        pi_free(thiz);
    }
}

DATA_MGR_S* netdata_manager_create(void)
{
    DATA_MGR_S* thiz;

    thiz = (DATA_MGR_S *)pi_zalloc(sizeof(DATA_MGR_S));
    RETURN_VAL_IF(thiz == NULL, NET_WARN, NULL);

    thiz->mutex = pi_mutex_create();
    if ( thiz->mutex == NULL )
    {
        NET_WARN("create data_mgr mutex failed: %d<%s>", errno, strerror(errno));
        pi_free(thiz);
        return NULL;
    }

#define out1(_member, _nvid, _i)        if ( _nvid > 0 ) NET_DATA_INIT_UINT32_ARRAY(_member, _nvid)
#define out2(_member, _nvid)            if ( _nvid > 0 ) NET_DATA_INIT_UINT32_VALUE(_member, _nvid)
#define out3(_member, _nvid, _i, _s)    if ( _nvid > 0 ) NET_DATA_INIT_STRING_ARRAY(_member, _nvid)
#define out4(_member, _nvid, _s)        if ( _nvid > 0 ) NET_DATA_INIT_STRING_VALUE(_member, _nvid)
#define out5(_member, _nvid, _s)        if ( _nvid > 0 ) NET_DATA_INIT_BUFFER_BYTES(_member, _nvid, _s)
    NET_DATA_MEMBER_TABLE(out1, out2, out3, out4, out5);
#undef  out1
#undef  out2
#undef  out3
#undef  out4
#undef  out5

    netdata_init_factory_status(thiz);

    return thiz;
}
/**
 *@}
 */
