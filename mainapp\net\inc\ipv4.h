/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ipv4.h
 * @addtogroup net
 * @{
 * @addtogroup ipv4
 * <AUTHOR>
 * @date 2023-9-16
 * @brief Network IPv4 address manager API header file.
 */
#ifndef __IPV4_H__
#define __IPV4_H__

/**
 * @brief       Start udhcpc or dhcpd(WiFi-Direct) progress to generate automatic IPv4 addresses for this network interface.
 * @param[in]   ifid    : The network interface index.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            net_ipv4_start_dhcp     (IFACE_ID_E ifid);

/**
 * @brief       Stop udhcpc or dhcpd(WiFi-Direct) progress for this network interface(ifid).
 * @param[in]   ifid    : The network interface index.
 * <AUTHOR> <PERSON>n
 * @date        2023-9-16
 */
void            net_ipv4_stop_dhcp      (IFACE_ID_E ifid);

/**
 * @brief       Check this IPv4 address if conflict.
 * @param[in]   ifid    : The network interface index.
 * @param[in]   ipaddr  : The IP address string, eg. "xxx.xxx.xxx.xxx".
 * @return      Check result
 * @retval      ==0     : No conflict\n
 *              !=0     : Conflict
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv4_check_conflict (IFACE_ID_E ifid, const char* probe_addr);

/**
 * @brief       Set IP, subnet mask and default gateway address for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @param[in]   ipaddr  : The IP address string, eg. "xxx.xxx.xxx.xxx".
 * @param[in]   mask    : The subnet mask string.
 * @param[in]   gateway : The default gateway string.
 * @return      setting result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv4_set_addr       (IFACE_ID_E ifid, const char* ipaddr, const char* mask, const char* gateway);

/**
 * @brief       Start udhcpc progress to generate only autodns addresses and save to resolv file for this network interface.
 * @param[in]   ifid    : The network interface index.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            net_ipv4_update_autodns (IFACE_ID_E ifid);

/**
 * @brief       Set the manual DNS address to the resolv file for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @param[in]   dns     : The DNS address.
 * @return      setting result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv4_set_dns        (IFACE_ID_E ifid, char (*dns)[IPV4_ADDR_LEN]);

/**
 * @brief       Get current DNS address from the resolv file for this network interface.
 * @param[in]   ifid    : The network interface index.
 * @param[out]  dns     : The DNS address.
 * @return      Setting result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_ipv4_get_dns        (IFACE_ID_E ifid, char (*dns)[IPV4_ADDR_LEN]);

#endif /* __IPV4_H__ */
/**
 *@}
 */
