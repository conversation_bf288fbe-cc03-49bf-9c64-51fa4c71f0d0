/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file public_data_proc.c
 * @addtogroup public
 * @{
 * @addtogroup public_data_proc
 * <AUTHOR>
 * @date 2023-07-17
 * @brief Records public data
 */

#include "public_data_proc.h"
#include "pol/pol_log.h"

const PAPER_SIZE_DETAIL_S c_paper_size_detail[] =
{
    {PAPER_SIZE_A4               ,   210 ,    297}, //0x00,     210.0 × 297.0
    {PAPER_SIZE_A5               ,   148 ,    210}, //0x01,     148.5 × 210.0
    {PAPER_SIZE_A5L              ,   210 ,    148}, //0x02,     210.0 × 148.0
    {PAPER_SIZE_B5               ,   176 ,    250}, //0x03,     105.0 × 148.0
    {PAPER_SIZE_FULL_PLATEN      ,     0 ,      0}, //0x04,
    {PAPER_SIZE_LETTER           ,   216 ,    279}, //0x05,     215.9 × 279.4
    {PAPER_SIZE_CARD             ,     0 ,      0}, //0x06,
    {PAPER_SIZE_FOLIO            ,   216 ,    330}, //0x07,     216.0 × 330.0
    {PAPER_SIZE_ISO_B5           ,   176 ,    250}, //0x08,     176.0 × 250.0
    {PAPER_SIZE_A6               ,   105 ,    148}, //0x09,     105.0 × 148.0
    {PAPER_SIZE_USER_DEFINE      ,     0 ,      0}, //0x0A,     75~218× 148~356,long paper above 356-1200
    {PAPER_SIZE_LEGAL13          ,   216 ,    330}, //0x0B,     215.9 × 330.0
    {PAPER_SIZE_LEGAL14          ,   216 ,    356}, //0x0C,     215.9 × 355.6
    {PAPER_SIZE_JIS_B5           ,   182 ,    257}, //0x0D,     182.0 × 257.0
    {PAPER_SIZE_ENV_MONARCH      ,   98  ,    191}, //0x0E      098.4 × 190.5
    {PAPER_SIZE_ENV_DL           ,   110 ,    220}, //0x0F,     110.0 × 220.0
    {PAPER_SIZE_ENV_C5           ,   162 ,    229}, //0x10,     162.0 × 229.0
    {PAPER_SIZE_ENV_10           ,   105 ,    241}, //0x11,     104.8 × 241.3
    {PAPER_SIZE_YOUKEI_SIZE4     ,   105 ,    234}, //0x12,     105.0 × 234.0
    {PAPER_SIZE_JAPANESE_POSTCARD,   100 ,    148}, //0x13,     100.0 × 148.0
    {PAPER_SIZE_CHOUKEI_SIZE3    ,   120 ,    235}, //0x14,     120.0 × 235.0
    {PAPER_SIZE_CUSTOM_16K       ,   185 ,    260}, //0x15,     185.0 × 260.0
    {PAPER_SIZE_CUSTOM_BIG_16K   ,   195 ,    270}, //0x16,     195.0 × 270.0
    {PAPER_SIZE_CUSTOM_32K       ,   130 ,    185}, //0x17,     130.0 × 185.0
    {PAPER_SIZE_CUSTOM_BIG_32K   ,   135 ,    195}, //0x18,     135.0 × 195.0
    {PAPER_SIZE_EXECUTIVE        ,   184 ,    267}, //0x19,     184.0 × 267.0
    {PAPER_SIZE_OFICIO           ,   216 ,    343}, //0x1A,     216.0 × 343.0
    {PAPER_SIZE_STATEMENT        ,   140 ,    216}, //0x1B,     140.0 × 216.0
    {PAPER_SIZE_ENV_C6           ,   114 ,    162}, //0x1C,     114.3 × 162.0
    {PAPER_SIZE_ZL               ,   120 ,    230}, //0x1D,     120.0 × 230.0
    {PAPER_SIZE_B6               ,   125 ,    176}, //0x1E,     125.0 × 176.0
    {PAPER_SIZE_ENV_B6           ,   125 ,    176}, //0x1F,     125.0 × 176.0
    {PAPER_SIZE_POSTCARD         ,   148 ,    200}, //0x20,     148.0 × 200.0
    {PAPER_SIZE_YOUGATA2         ,   114 ,    162}, //0x21,     114.0 × 162.0
    {PAPER_SIZE_NAGAGATA3        ,   120 ,    235}, //0x22,     120.0 × 235.0
    {PAPER_SIZE_YOUNAGA3         ,   120 ,    235}, //0x23,     120.0 × 235.0
    {PAPER_SIZE_YOUGATA4         ,   105 ,    235}, //0x24,     105.0 × 235.0
    {PAPER_SIZE_LONG             ,   210 ,   1200}, //0x25,     210.0 × 1200.0
    {PAPER_SIZE_A3               ,   297 ,    420}, //0x26,     297.0 × 420.0
    {PAPER_SIZE_A4L              ,   297 ,    210}, //0x27,     297.0 × 210.0
    {PAPER_SIZE_JIS_B6           ,   128 ,    182}, //0x28,     128.0 × 182.0
    {PAPER_SIZE_JIS_B4           ,   257 ,    364}, //0x29,     257.0 × 364.0
    {PAPER_SIZE_4X6_INCH         ,   102 ,    152}, //0x2A,     101.6 × 152.4 / 4" × 6"
    {PAPER_SIZE_INVOICE          ,   140 ,    216}, //0x2B,     215.9 × 139.7 / 5.5" × 8.5"
    {PAPER_SIZE_QUARTO           ,   254 ,    203}, //0x2C,     254.0 × 203.2 / 10" × 8"
    {PAPER_SIZE_G_LETTER         ,   266 ,    203}, //0x2D,     266.0 × 203.2 /10.5" × 8"
    {PAPER_SIZE_11X14_INCH       ,   297 ,    356}, //0x2E,     279.4 × 355.6 / 11" × 14"
    {PAPER_SIZE_LEDGER           ,   279 ,    432}, //0x2F,     279.4 × 431.8 /11" × 17"
    {PAPER_SIZE_8K               ,   270 ,    390}, //0x30,     270.0 × 390.0
    {PAPER_SIZE_SRA3             ,   320 ,    450}, //0x31,     320.0 × 450.0
    {PAPER_SIZE_FOOLSCAP1        ,   203 ,    330}, //0x32,     203.0 × 330.2 / 8"×13"
    {PAPER_SIZE_FOOLSCAP2        ,   210 ,    330}, //0x33,     209.6 × 330.2 / 8.25"×13"
    {PAPER_SIZE_FOOLSCAP3        ,   216 ,    330}, //0x34,     215.9 × 330.2 / 8.5"×13"
    {PAPER_SIZE_FOOLSCAP4        ,   220 ,    330}, //0x35,     220.0 × 330.0 / 8.65"×13"
    {PAPER_SIZE_FOOLSCAP5        ,   206 ,    337}, //0x36,     206.4 × 336.6 / 8.125"×13.25"
    {PAPER_SIZE_A3_WIDE1         ,   305 ,    457}, //0x37,     304.8 × 457.2 / 12"×18"
    {PAPER_SIZE_A3_WIDE2         ,   311 ,    457}, //0x38,     311.1 × 457.2 / 12.25"×18"
    {PAPER_SIZE_CUSTOM_BIG_16KL  ,   270,     195}, //0x39,     270.0 × 195.0 / 12.25"×18"
    {PAPER_SIZE_JIS_B5L          ,   257,     182}, //0x3A,     257.0 × 182.0
    {PAPER_SIZE_INVOICE_L        ,   216,     140}, //0x3B,     215.9 × 139.7 / 8.5" × 5.5"
    {PAPER_SIZE_EXECUTIVE_L      ,   267,     184}, //0x3C,     266.7 × 184.2 / 10.5"×7.25"
    {PAPER_SIZE_QUARTO_L         ,   254,     203}, //0x3D,     254.0 × 203.2 / 10"×8"
    {PAPER_SIZE_G_LETTER_L       ,   267,     203}, //0x3E,     266.7 × 203.2 / 10.5"×8"
    {PAPER_SIZE_LETTER_L         ,   279,     216}, //0x3F,     279.4 × 215.9 / 11" × 8.5"
    {PAPER_SIZE_ISO_B5L          ,   250,     176}, //0x40,     250.0 × 176.0

    {PAPER_SIZE_USER_DEFINE1     ,     0 ,      0}, //0x41,
    {PAPER_SIZE_USER_DEFINE2     ,     0 ,      0}, //0x42,
    {PAPER_SIZE_USER_DEFINE3     ,     0 ,      0}, //0x43,
    {PAPER_SIZE_USER_DEFINE4     ,     0 ,      0}, //0x44,
    {PAPER_SIZE_USER_DEFINE5     ,     0 ,      0}, //0x45,
    {PAPER_SIZE_B4               ,   257 ,    364}, //0x46,     257.0 × 364.0
    {PAPER_SIZE_A6CARD           ,   105 ,    148}, //0x47,     105.0 × 148.0

    {PAPER_SIZE_GENERAL          ,     0 ,      0}, //0x48,
    {PAPER_SIZE_MIXED            ,     0 ,      0}, //0x49,
    {PAPER_SIZE_STATEMENT_L      ,   216 ,    140}, //0x4A,     216.0 × 140.0

    {PAPER_SIZE_B5L              ,   250,     176}, //0x4B,     250.0 × 176.0
    {PAPER_SIZE_BIG_16K          ,   195 ,    270}, //0x4C,     295.0 × 270.0
    {PAPER_SIZE_BIG_16KL         ,   270,     195}, //0x4D,     270.0 × 195.0

    {PAPER_SIZE_AUTO             ,     0 ,      0}, //0x4E,
    {PAPER_SIZE_UNKOWN           ,     0 ,      0}, //0x4F,
    {PAPER_SIZE_FULL_TABLE       ,     0 ,      0}, //0x50,

};


int get_paper_h(PAPER_SIZE_E paper_size, PAPER_UNIT_E unit, int y_dpi)
{
    int      i            = 0;
    int      array_num    = 0;
    uint16_t paper_height = 0;

    array_num = sizeof( c_paper_size_detail ) / sizeof( c_paper_size_detail[0] );
    for(i=0;i<array_num;i++)
    {
        if(c_paper_size_detail[i].paper_size == paper_size)
        {
            if(UNIT_INCH == unit)
            {
                paper_height = c_paper_size_detail[i].height/25.4;

            }
            else if(UNIT_PIXEL == unit)
            {
                paper_height = c_paper_size_detail[i].height*y_dpi/25.4;
            }
            else if(UNIT_MM == unit)
            {
                paper_height = c_paper_size_detail[i].height;
            }
            else
            {
                pi_log_e( "uint err\n");
                return -1;
            }
            break;
        }
    }

    if(i >= array_num)
    {
        pi_log_e("pape size err\n");
        return -1;
    }

    return paper_height;
}


int get_paper_w(PAPER_SIZE_E paper_size, PAPER_UNIT_E unit, int x_dpi)
{
    int      i            = 0;
    int      array_num    = 0;
    uint16_t paper_width  = 0;

    array_num = sizeof( c_paper_size_detail ) / sizeof( c_paper_size_detail[0] );
    for(i=0;i<array_num;i++)
    {
        if(c_paper_size_detail[i].paper_size == paper_size)
        {
            if(UNIT_INCH == unit)
            {
                paper_width = c_paper_size_detail[i].width/25.4;

            }
            else if(UNIT_PIXEL == unit)
            {
                paper_width = c_paper_size_detail[i].width*x_dpi/25.4;
            }
            else if(UNIT_MM == unit)
            {
                paper_width = c_paper_size_detail[i].width;
            }
            else
            {
                pi_log_e( "uint err\n");
                return -1;
            }
            break;
        }
    }

    if(i >= array_num)
    {
        pi_log_e("pape size err\n");
        return -1;
    }

    return paper_width;
}




int get_paper_w_h(PAPER_SIZE_E paper_size, PAPER_UNIT_E unit, int x_dpi, int y_dpi, int *paper_width, int *paper_height)
{
    int i = 0;
    int array_num    = 0;

    if(NULL == paper_width || NULL == paper_height)
    {
        pi_log_d(  "point is null\n");
        return -1;
    }

    array_num = sizeof( c_paper_size_detail ) / sizeof( c_paper_size_detail[0] );
    for(i=0;i<array_num;i++)
    {
        if(c_paper_size_detail[i].paper_size == paper_size)
        {
            if(UNIT_INCH == unit)
            {
                *paper_width  = c_paper_size_detail[i].width/25.4;
                *paper_height = c_paper_size_detail[i].height/25.4;

            }
            else if(UNIT_PIXEL == unit)
            {
                *paper_width  = c_paper_size_detail[i].width*x_dpi/25.4;
                *paper_height = c_paper_size_detail[i].height*y_dpi/25.4;
            }
            else if(UNIT_MM == unit)
            {
                *paper_width  = c_paper_size_detail[i].width;
                *paper_height = c_paper_size_detail[i].height;
            }
            else
            {
                pi_log_e( "uint err\n");
                return -1;
            }
            break;
        }
    }

    if(i >= array_num)
    {
        pi_log_e("pape size err\n");
        return -1;
    }

    return 0;
}

int get_paper_size(int w, int h)
{
    int i = 0;
    int array_size = sizeof(c_paper_size_detail)/sizeof(c_paper_size_detail[0]);
    int tolerance = 0;
    for(tolerance=0;tolerance<20;tolerance++)
    {
        for(i=0;i<array_size;i++)
        {
            if((abs(w-c_paper_size_detail[i].width)<=tolerance) &&(abs(h-c_paper_size_detail[i].height)<=tolerance))
            {
                pi_log_i("paper size %d  width = %d height = %d tolerance=%d\n",c_paper_size_detail[i].paper_size,w,h,tolerance);
                return c_paper_size_detail[i].paper_size;
            }
        }
    }


    if(i >= sizeof(c_paper_size_detail)/sizeof(PAPER_SIZE_DETAIL_S))
    {
        return PAPER_SIZE_INVALID;
    }

    return c_paper_size_detail[i].paper_size;
}

char *get_page_type_string(int page_type)
{
    static char page_type_str[256]={0};

    memset(page_type_str,0,sizeof(page_type_str));

    strcat(page_type_str,"#p#[");
    if(FMT_PAGE_IS_FRONT(page_type))
    {
        strcat(page_type_str,"Front+");
    }
    if(FMT_PAGE_IS_BACK(page_type))
    {
        strcat(page_type_str,"Back+");
    }
    if(FMT_PAGE_IS_FIRST(page_type))
    {
        strcat(page_type_str,"First_page+");
    }
    if(FMT_PAGE_IS_LAST(page_type))
    {
        strcat(page_type_str,"Last_page+");
    }
    if(FMT_PAGE_IS_BLANK(page_type))
    {
        strcat(page_type_str,"Blank+");
    }
    if(FMT_PAGE_IS_INSTER(page_type))
    {
        strcat(page_type_str,"Insert+");
    }
    if(FMT_PAGE_IS_SPEARATE(page_type))
    {
        strcat(page_type_str,"Spearate+");
    }

    if(FMT_PAGE_IS_FRONT_COVER_OUTSIDE(page_type))
    {
        strcat(page_type_str,"Cover_outside+");
    }
    if(FMT_PAGE_IS_FRONT_COVER_INSIDE(page_type))
    {
        strcat(page_type_str,"Cover_inside+");
    }
    if(FMT_PAGE_IS_BACK_COVER_INSIDE(page_type))
    {
        strcat(page_type_str,"BackCover_inside+");
    }
    if(FMT_PAGE_IS_BACK_COVER_OUTSIDE(page_type))
    {
        strcat(page_type_str,"BackCover_outside+");
    }


    strcat(page_type_str,"]");

    return page_type_str;
}

JOB_DATA_S* job_create(void)
{
    JOB_DATA_S* job;

    job = (JOB_DATA_S *)pi_zalloc(sizeof(JOB_DATA_S));
    RETURN_VAL_IF(job == NULL, pi_log_e, NULL);

    job->mtx = pi_mutex_create();

    return job;
}

int32_t job_destroy(JOB_DATA_S* job)
{
    RETURN_VAL_IF(job == NULL, pi_log_e, -1);

    if ( job->mtx != INVALIDMTX )
    {
        pi_mutex_destroy(job->mtx);
        job->mtx = INVALIDMTX;
    }
    pi_free(job);

    return 0;
}

PAGE_DATA_S *page_create( void )
{
    signed int      ret     = 0;
    PAGE_DATA_S*    page    = NULL;

    page = (PAGE_DATA_S *)pi_malloc( sizeof(PAGE_DATA_S) );
    if( !page )
    {
        pi_log_e("Page data create failed\n");
        return NULL;
    }
    pi_memset( page, 0, sizeof( PAGE_DATA_S ) );

    PUBLIC_COOKIE_SET(page);

    //default page config
    page->paper_size = PAPER_SIZE_A4;
    page->paper_type = PAPER_TYPE_ORDINARY;
    page->density    = PRINT_DENSITY_NORMAL;

    page->copies = 1;
    page->current_job_data = NULL;
    page->oem_data  = NULL;

    ret = pi_image_open( &(page->image) );
    if (0 != ret)
    {
        pi_free( page );
        page = NULL;
    }

    return page;
}

signed int page_destroy( PAGE_DATA_S *page )
{
    if( !page )
    {
        pi_log_e("Page data is NULL\n");
        return -1;
    }
    if( ! PUBLIC_COOKIE_VALID(page ) )
    {
        pi_log_e("Page data cookie invalid\n");
    }

    // free the band pointer first
    pi_image_close( &(page->image) );

    if( page->oem_data)
    {
        pi_free(page->oem_data);
    }

    pi_free( page );

    return 0;
}

signed int page_copy_dimension( PAGE_DATA_S *dst, PAGE_DATA_S *src )
{
    if( !dst || !src )
    {
        pi_log_e("Page data is NULL\n");
        return -1;
    }

    dst->print_mode     = src->print_mode;
    dst->paper_size     = src->paper_size;
    dst->paper_type     = src->paper_type;
    dst->page_type        = src->page_type;
    dst->paper_width    = src->paper_width;
    dst->paper_height   = src->paper_height;
    dst->density        = src->density;
    dst->tray_in        = src->tray_in;
    dst->tray_in_original   = src->tray_in_original;
    dst->page_source        = src->page_source;
    dst->insert_page        = src->insert_page;
    dst->copies        = src->copies;
    dst->oem_data        = src->oem_data;
    dst->left_margin = src->left_margin;
    dst->top_margin  = src->top_margin;
    dst->current_job_data     = src->current_job_data;
    dst->page_number     = src->page_number;
    //dst->renderg2     = src->renderg2;
    dst->fine_field        = src->fine_field;
    dst->paper_mismatch      = src->paper_mismatch;
    dst->page_cmpbytestot     = src->page_cmpbytestot;
    dst->page_id      = src->page_id;
    dst->rotation     = src->rotation;

    dst->staple.punch_mode    = src->staple.punch_mode;
    dst->staple.staple_mode    = src->staple.staple_mode;
    dst->staple.fold_mode    = src->staple.fold_mode;
    dst->staple.tray_receive    = src->staple.tray_receive;
    dst->shift_mode    = src->shift_mode;
    dst->staple.fold_pages    = src->staple.fold_pages;
    dst->staple.paper_max_push_put = src->staple.paper_max_push_put;

    return 0;
}

JOB_VIA_E iovia_to_jobvia(IO_VIA_E via)
{
    JOB_VIA_E job_via;
    switch(via)
    {
        case IO_VIA_UNKNOWN:
            job_via = JOB_VIA_UNKNOWN;
            break;
        case IO_VIA_USB:
            job_via = JOB_VIA_USB;
            break;
        case IO_VIA_NET:
            job_via = JOB_VIA_ETHERNET;
            break;
        case IO_VIA_IPP:
            job_via = JOB_VIA_IPP;
            break;
        case IO_VIA_GCLOUD:
            job_via = JOB_VIA_GCP;
            break;
        case IO_VIA_WIFI:
            job_via = JOB_VIA_WIFI;
            break;
        case IO_VIA_FILE:
            job_via = JOB_VIA_FILE;
            break;
        case IO_VIA_INTERNAL:
            job_via = JOB_VIA_INTERNAL;
            break;
        case IO_VIA_FAX:
            job_via = JOB_VIA_FAX;
            break;
        case IO_VIA_AIRSCN:
            job_via = JOB_VIA_AIRSCAN;
            break;
        case IO_VIA_UDISK:
            job_via = JOB_VIA_UDISK;
            break;
        case IO_VIA_MAX:
            job_via = JOB_VIA_MAX;
            break;
        //IO_VIA_ACR IO_VIA_PKI
        default:
            job_via = JOB_VIA_OTHER;
            break;
    }
    return job_via;
}

/**
 * @}
 */

