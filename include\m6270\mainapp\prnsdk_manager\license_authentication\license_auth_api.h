/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd
 * @file license_auth_api.h
 * @brief Header file for license authentication functions.
 * @details This file declares functions for handling license authentication, including SDK initialization, authentication checks, and random number generation.
 * @addtogroup license_authentication
 * @{
 * <AUTHOR>
 * @date 2024-12-11
 */

#ifndef _LICENSE_AUTH_API_H_
#define _LICENSE_AUTH_API_H_

#include "cjson/cJSON.h"
#include <signal.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Retrieves the version of the license SDK.
 * @return The SDK version as an integer.
 */
int license_auth_get_license_version(void);

/**
 * @brief Checks whether the SDK function is enabled.
 * @return Returns 1 if enabled, otherwise 0.
 */
int license_auth_is_sdk_activated(void);

/**
 * @brief Initializes the SDK license.
 * @details This function starts two threads when opening the initialization.初始化调用
 * @return Returns 1 if the SDK is initialized successfully, otherwise 0.
 */
int license_auth_initialize_sdk_license(void);

/**
 * @brief Generates a random hexadecimal string of the specified length.
 * @param[in] buffer_size Target length of the hexadecimal string.
 * @param[in] length random of the hexadecimal string.
 * @param[out] buffer User-provided buffer to store the generated hexadecimal string.
 * @return Returns 0 on success, -1 on failure.
 */
int32_t license_auth_generate_random_number(char *buffer, size_t buffer_size, int32_t length);

/**
 * @brief Verifies an authentication request from a third party.
 * @param[in] input_buffer The authentication request from a third party
 * @return 0 on success, -1 on error.
 */
int license_auth_access_authentication(const char *input_buffer);

/**
 * @brief Prolog function for license authentication.
 * @details Registers commands and initializes configurations related to license authentication.
 * @return 0 on success, or an error code on failure.
 */
int license_auth_prolog(void);

#ifdef __cplusplus
}
#endif

#endif /* _LICENSE_AUTH_API_H_ */
/**
 *@}
 */
