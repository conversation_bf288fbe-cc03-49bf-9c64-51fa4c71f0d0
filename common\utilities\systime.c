#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_log.h"
#include "hal_common.h"
#include "hal_rtc.h"

__attribute__((always_inline))
static inline void cleanup_handle(void** phandle)
{
    if ( phandle && *phandle )
    {
        pi_hal_rtc_free(phandle);
        *phandle = NULL;
    }
}

int32_t systime_get(struct tm* tmval)
{
    time_t now = time(NULL);

    RETURN_VAL_IF(tmval == NULL, pi_log_e, -1);

    gmtime_r(&now, tmval);

    return 0;
}

int32_t systime_set(struct tm* tmval)
{
    void*   handle __attribute__ ((__cleanup__(cleanup_handle))) = NULL;

    RETURN_VAL_IF(tmval == NULL, pi_log_e, -1);

    /* 更新RTC时间 */
    RETURN_VAL_IF(pi_hal_rtc_request(&handle, HAL_REQUEST_FLAG_BLOCK) < 0 || handle == NULL, pi_log_e, -1);
    RETURN_VAL_IF(pi_hal_rtc_time_write(handle, *tmval) < 0, pi_log_e, -1);

    /* 更新系统时间 */
    time_t rawtime;
    RETURN_VAL_IF(pi_hal_rtc_time_read(handle, &rawtime) < 0, pi_log_e, -1);
    stime(&rawtime);

    return 0;
}

int32_t systime_init(void)
{
    void*   handle __attribute__ ((__cleanup__(cleanup_handle))) = NULL;
    time_t  now;

    RETURN_VAL_IF(pi_hal_rtc_request(&handle, HAL_REQUEST_FLAG_BLOCK) < 0 || handle == NULL, pi_log_e, -1);
    RETURN_VAL_IF(pi_hal_rtc_time_read(handle, &now) < 0, pi_log_e, -1);
    stime(&now);

    return 0;
}
