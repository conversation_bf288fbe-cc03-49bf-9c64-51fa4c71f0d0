#include "nettypes.h"
#include "netsock.h"
#include "wsdef.h"
#include "wsd.h"
#include <sys/eventfd.h>

#define __DECLARE_SECTION_FUNC(_section, _func_type)        \
        extern _func_type __start_##_section;  \
        extern _func_type __stop_##_section;   \
        static __attribute__((weakref(STR(__start_##_section)))) _func_type __start_##_section##_wref;     \
        static __attribute__((weakref(STR(__stop_##_section)))) _func_type __stop_##_section##_wref;


#define _DECLARE_SECTION_FUNC(_section, _func_type)      __DECLARE_SECTION_FUNC(_section, _func_type)
#define DECLARE_SECTION_FUNC(_section, _func_type)      _DECLARE_SECTION_FUNC(WSD_SECTION_NAME(_section), _func_type)

#define __FOREACH_FUNCTION_CALL(_section, ...) \
    do {                            \
        if(&__start_##_section##_wref == 0) break;                                                \
        for (__auto_type _pfunc = &__start_##_section##_wref; _pfunc < &__stop_##_section##_wref; _pfunc++)  {  \
            (*_pfunc)(__VA_ARGS__);                           \
        }                                                     \
    } while(0)

#define _FOREACH_FUNCTION_CALL(_section_name, ...) __FOREACH_FUNCTION_CALL(_section_name, ##__VA_ARGS__)
#define FOREACH_FUNCTION_CALL(_section_name, ...) _FOREACH_FUNCTION_CALL(WSD_SECTION_NAME(_section_name), ##__VA_ARGS__)

#define WSD_UUID_FORMAT                 "%08x-%04x-%04x-%02x%02x-%02x%02x%02x%02x%02x%02x"

DECLARE_SECTION_FUNC(init, INIT_FUNC);
DECLARE_SECTION_FUNC(deinit, DEINIT_FUNC);

static WSD_CTX_S* s_wsd_ctx = NULL;

const char* wsd_generate_uuid(char* buf, size_t nbuf)
{
    struct timeval tv;
    uint64_t now;
    uint16_t clock;
    uint8_t* mac;

    RETURN_VAL_IF(s_wsd_ctx == NULL, NET_WARN, "");
    mac = s_wsd_ctx->mac;

    gettimeofday(&tv, NULL);
    now = ((uint64_t)tv.tv_sec * 10000000) + ((uint64_t)tv.tv_usec * 10) + ((uint64_t)(0x01B21DD213814000));
    clock = (uint16_t)random();
    snprintf(buf, nbuf, WSD_UUID_FORMAT, (uint32_t)now, (uint16_t)(now >> 32), (uint16_t)((now >> 48) & 0x0fff), clock & 0xff, (clock & 0x3f00) >> 8,
            mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    return buf;
}

QXML_S* wsd_qxml_parser(char* str)
{
    char *psoap;
    psoap = strstr(str, "<?xml");
    if (psoap == NULL)
    {
        NET_WARN("Not a soap body");
        return NULL;
    }

    return QXMLparserCreate(psoap, 0);
}

void wsd_tcp_loop(int notify_efd, THREADS_POOL_S* trd_pool, WSD_GET_PORT_FUNC get_port_func, THREAD_TASK_FUNC trd_handler)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };
    NET_CONN_S*     pnc = NULL;
    fd_set          rfds;
    uint64_t        efd_val;
    int32_t         update = 1;
    int32_t         max_fd;
    uint16_t        port;

    port = get_port_func();
    while ( 1 )
    {
        FD_ZERO(&rfds);
        FD_SET(notify_efd, &rfds);
        max_fd = notify_efd;
        if (update)
        {
            port = get_port_func();
        }
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, port, 1);
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        for ( IP_VERSION_E ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    NET_DEBUG("new connection %d from %s : %u to %u", pnc->sockfd, pnc->remote_addr, pnc->remote_port, pnc->local_port);
                    if ( threads_pool_add_task(trd_pool, trd_handler, pnc) < 0 )
                    {
                        NET_WARN("add wsd_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }

        if ( FD_ISSET(notify_efd, &rfds) )
        {
            read(notify_efd, &efd_val, sizeof(efd_val));
            NET_DEBUG("recv notify from %d , reload service socket", notify_efd);
            update = 1;
        }
    }
}

static void wsd_update_port_cb(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    uint64_t changed = LINK_CHANGE_ALL;
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_wsd_ctx== NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_WSD )
    {
        for( int i = 0; i < s_wsd_ctx->efd_cnt; ++i)
        {
            write(s_wsd_ctx->efd_arr[i], &changed, sizeof(changed));
        }
    }
}

int wsd_block_sigpipe(sigset_t *out_oldset)
{
    sigset_t sig_mask;
    sigemptyset(&sig_mask);
    sigaddset(&sig_mask, SIGPIPE);
    return pthread_sigmask(SIG_BLOCK, &sig_mask, out_oldset);
}

void wsd_epilog(void)
{
    FOREACH_FUNCTION_CALL(deinit);
    if ( s_wsd_ctx != NULL )
    {
        pi_free(s_wsd_ctx);
        s_wsd_ctx = NULL;
    }
}

/*    client                            Device         Hosted Service
 *
 *      ---------multicsast probe--------->
 *
 *      <------------probe Match-----------                                 WS-Discovery
 *
 *      ---------Get Metadata------------->
 *
 *      <------------Metadata--------------                              WS-Metadata Transfer
 *
 *      ----------------------Get Metadata------------------>
 *
 *      <-----------------------Metadata---------------------
 *
 *      --------------------Start Print Job----------------->
 *
 *      -------------------Print Job Started---------------->
 *
 *      ----------------------Subscirbe--------------------->
 *                                                                           WS-Eventing
 *      -------------------Subscribe Response--------------->
 *
 *      -------------------Print Document------------------->
 *
 *      <----------------------Printing----------------------
 *
 *      <---------------------Job Status---------------------
 *
 * */


int32_t wsd_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;
    RETURN_VAL_IF(s_wsd_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);
    RETURN_VAL_IF(msg_router_register(MID_WSDSCAN) < 0, NET_WARN, -1);

    s_wsd_ctx = (WSD_CTX_S *)pi_zalloc(sizeof(WSD_CTX_S));
    RETURN_VAL_IF(s_wsd_ctx == NULL, NET_WARN, -1);
    s_wsd_ctx->net_ctx = net_ctx;

    do
    {
        net_ifctl_get_mac(IFACE_ETH, s_wsd_ctx->mac, sizeof(s_wsd_ctx->mac));
        snprintf(s_wsd_ctx->default_uuid, sizeof(s_wsd_ctx->default_uuid), WSD_UUID_DEFAULT,
                 s_wsd_ctx->mac[0], s_wsd_ctx->mac[1], s_wsd_ctx->mac[2], s_wsd_ctx->mac[3], s_wsd_ctx->mac[4], s_wsd_ctx->mac[5]);
        FOREACH_FUNCTION_CALL(init, s_wsd_ctx);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("WSD initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netport_observer(net_ctx, wsd_update_port_cb, NULL);
    }
    else
    {
        wsd_epilog();
    }
    return ret;
}

