/**
* @copyright   2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file  scanner_ctrl.h
* @addtogroup scan
*
* @{
* @addtogroup  scan
* <AUTHOR>
* @date   2022-5-24
* @version  v1.0
* @brief   transplant LP-2023 protocol for pc driver
**/


#ifndef __SCAN_CTRL_H__
#define __SCAN_CTRL_H__


/**
 * @brief scan job state
 */
typedef enum
{
    SCAN_JOB_NONE = 0,      ///< job node
    SCAN_JOB_REQUEST,       ///< job request
    SCAN_JOB_WAIT_BANDS,    ///< wait bands
    SCAN_JOB_START,         ///< job start
    SCAN_JOB_PAUSE,         ///< job pause
    SCAN_JOB_END,           ///< job end
    SCAN_JOB_ERROR,         ///< job error
    SCAN_JOB_CANCEL,        ///< job cancel
} SCAN_JOB_STATE_E;

/**
 * @brief doc type
 */
typedef enum
{
    SCAN_DOC_AUTO = 0,      ///< doc auto
    SCAN_DOC_TXT,           ///< doc txt
    SCAN_DOC_MIXED,         ///< doc mixed
    SCAN_DOC_PICTURE,       ///< doc picture
} SCAN_DOC_TYPE_E;

/**
 * @brief scan feature
 */
typedef struct
{
    //general feature
    uint16_t            scan_width_max; ///< general feature scan width
    uint16_t            bit_per_pixel;  ///< general feature bpp

    //fb config
    uint16_t            have_fb;        ///< have fb
    uint16_t            fb_res_min;     ///< fb res min
    uint16_t            fb_res_max;     ///< fb res max
    uint16_t            fb_height_max;  ///< fb height max

    //adf config
    uint16_t            have_adf;       ///< have adf
    uint16_t            adf_res_min;    ///< adf res min
    uint16_t            adf_res_max;    ///< adf res max
    uint16_t            adf_height_max; ///< adf height max

    uint16_t            adf_type;       ///< single adf or dual adf
    uint16_t            adf_volume;     ///< adf volume
    uint16_t            adf_a4_ppm_max; ///< adf a4 ppm
} SCANNER_FEATURE_DATA_S;

/**
 * @brief scan ctrl config
 */
typedef struct
{
    SCAN_MODE_E     scan_mode;          ///< scan mode
    COLOR_MODE_E    color_mode;         ///< color mode
    RESOLUTION_E    resx;               ///< x res
    RESOLUTION_E    resy;               ///< y res
    uint16_t        width;              ///< width
    uint16_t        height;             ///< height
    uint16_t        margin_top;         ///< margin top
    uint16_t        margin_left;        ///< margin left
    uint16_t        band_height;        ///< margin height
} SCAN_CTRL_CONFIG_DATA__S;

//#define SCANNER_BAND_BUFFER_MAX         20

/**
 * @brief scan ctrl band data
 */
typedef struct tag_scan_ctrl_band_data
{
    uint32_t        band_index;         ///< band index
    uint32_t        band_size;          ///< band size
    uint32_t        physical_address;   ///< physical address
    uint16_t        page_type;          ///< page type

    struct tag_scan_ctrl_band_data
        * next;             ///< point to next band data
} SCAN_CTRL_BAND_DATA_S;

/**
 * @brief scan ctrl cb type
 */
typedef enum
{
    /*
    param1:main state
    param2:error status
    param3:0
    param4:0
    param5:NULL
    */
    SCAN_CB_STATUS_UPDATE  = 0, ///< status update

    /*
    param1:load sensor
    param2:feed sensor
    param3:0
    param4:0
    param5:NULL
    */
    SCAN_CB_SENSOR_UPDATE,      ///< sensor update

    /*
    param1:0
    param2:0
    param3:0
    param4:0
    param5:feature data pointer
    */
    SCAN_CB_FEATURE_UPDATE,     ///< feature update

    /*
    param1:page index
    param2:page type
    param3:0
    param4:0
    param5:NULL
    */
    SCAN_CB_PAGE_START,         ///< page start

    /*
    param1:band index
    param2:band size
    param3:band physical address
    param4:page index+type
    param5:NULL
    */
    SCAN_CB_BAND_DONE,      ///< band done

    /*
    param1:page index
    param2:page type
    param3:0
    param4:0
    param5:NULL
    */
    SCAN_CB_PAGE_END,       ///< page end

    /*
    param1:0
    param2:0
    param3:0
    param4:0
    param5:NULL
    */
    SCAN_CB_JOB_END,        ///< job end

    /*
    param1:0
    param2:0
    param3:0
    param4:0
    param5:NULL
    */
    SCAN_CB_JOB_CANCEL_ACK, ///< cancel ack
} SCAN_CTRL_CB_TYPE_S;

/**
 * @brief scan ctrl callback
 */
typedef int ( *scan_ctrl_callback )( SCAN_CTRL_CB_TYPE_S cb_type, int param1, int param2, int param3, int param4, int param5, int param6, void* param7 );

/**
 * @brief a common api to start scanner for a job
 * @param[in] scan_mode scan mode
 * @param[in] color_mode color mode
 * @param[in] resx x res
 * @param[in] page_height page height
 * @param[in] band_height band height
 * @param[in] margin_top top margin
 * @param[in] margin_left left margin
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_ctrl_job_request( SCAN_MODE_E       scan_mode,
                           COLOR_MODE_E    color_mode,
                           RESOLUTION_E    resx,
                           RESOLUTION_E    resy,
                           uint16_t        page_width,
                           uint16_t        page_height,
                           uint16_t        band_height,
                           uint16_t        margin_top,
                           uint16_t        margin_left );

/**
 * @brief a common api to submit bands to scanner
 * @param[in] band_index band index
 * @param[in] band_size band size
 * @param[in] physical_address physical address
 * @param[in] page_type page type
 * @param[in] emergency_enable 0/1
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_ctrl_band_submit( uint32_t        band_index,
                           uint32_t        band_size,
                           uint32_t        physical_address,
                           uint16_t        page_type,
                           int             emergency_enable );

/**
 * @brief a common api to start job
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_ctrl_job_start( void );

/**
 * @brief a common api to pause job
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_ctrl_job_pause( void );

/**
 * @brief a common api to restart job
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_ctrl_job_restart( void );

/**
 * @brief a common api to cancel job
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_ctrl_job_cancel( void );

/**
 * @brief a common api to prolog scanner ctrl
 * @param[in] scan_ctrl_cb callback
 * @return int \n
 * @retval success:1, failed:0
 * <AUTHOR>
 * @date 2021-10-29
 */
int scan_control_prolog( scan_ctrl_callback scan_ctrl_cb );

#endif

/**
 *@}
 */


