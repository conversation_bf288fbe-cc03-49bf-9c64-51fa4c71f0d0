/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file parsercommon.h
 * @addtogroup utilities
 * @{
 * <AUTHOR> @addtogroup parsercommon
 * @date 2023-4-25
 * @brief parsercommon public header file
 */
#ifndef PARSERCOMMON_H
#define PARSERCOMMON_H

#ifdef __cplusplus
extern "C" {
#endif

//#include "arch.h"
#include "pol/pol_threads.h"
#include "qio/qio_general.h"

//global debug switch,you can use it to control parser debug log
#define  PARSER_DEBUG_ON                5   //black;
#define  PARSER_DEBUG_WARNING           1   //green;
#define  PARSER_DEBUG_FOREVER           (-1)

#define PARSER_DEFALUT_LOG_LEVEL        5
extern int get_parser_log_level();
//if get_parser_log_level() == 0, it means turn off log(include error level)
//LOG_C means critical log need output fordefault
#define PARSER_LOG_E(fmt, ...)          { do { if (get_parser_log_level() >= 1) pi_log_e(fmt, ## __VA_ARGS__); } while(0); }    
#define PARSER_LOG_C(fmt, ...)          { do { if (get_parser_log_level() >= 2) pi_log_w(fmt, ## __VA_ARGS__); } while(0); }
#define PARSER_LOG_W(fmt, ...)          { do { if (get_parser_log_level() >= 3) pi_log_w(fmt, ## __VA_ARGS__); } while(0); }
#define PARSER_LOG_I(fmt, ...)          { do { if (get_parser_log_level() >= 4) pi_log_i(fmt, ## __VA_ARGS__); } while(0); }
#define PARSER_LOG_D(fmt, ...)          { do { if (get_parser_log_level() >= 5) pi_log_d(fmt, ## __VA_ARGS__); } while(0); }

#define PARSER_NET_TIMEOUT  10
#define PARSER_WIFI_TIMEOUT 30
#define PARSER_USB_TIMEOUT  2

typedef enum {
    PARSER_PC_CANCEL = -19,
    PARSER_NOT_SUPPORT = -18,
    PARSER_USER_CANCEL = -17,
    PARSER_FUNCRET_ERROR = -16,
	PARSER_NOT_ENOUGH_DATA_ERROR = -15,
    PARSER_JPEG_FORMAT_ERROR = -14,
    PARSER_URF_FORMAT_ERROR = -13,
    PARSER_PWG_FORMAT_ERROR = -12,
    PARSER_PL_OBJECT_ERROR = -11,
    PARSER_PL_FORMAT_ERROR = -10,
    PARSER_PJL_ERROR = -9,
    PARSER_PJL_FORMAT_ERROR = -8,
    ADD_ACTIVE_ERROR = -7,
    NO_FREE_QUEUE = -6,
    PARSER_NODATA_ERROR = -5,
    PARSER_STALLDATA_ERROR = -4,
    CHECKSUM_ERROR = -3,
    MEM_MALLOC_ERROR = -2,
    REGISTER_ERROR = -1,
    PARSER_ERROR = 0,
    PARSER_SUCCESS = 1,
    REGISTER_SUCCESS = 2,
    ADD_ACTIVE_SUCCESS = 3,
    PARSER_PJL_CONTINUE = 4,
    PARSER_PL_CONTINUE = 5,
    PARSER_PL_CANCELED = 6,
    PARSER_PL_TO_PJL = 7,
    PARSER_JOB_CANCEL = 8,
    PARSER_PWG_CONTINUE = 9,
    PARSER_URF_CONTINUE = 10,
    PARSER_JPEG_CONTINUE = 11,
} ParserStatus_E;
/**
 * @brief init debug mode, set debug param.
 * @author:jacky zeng
 */
void init_Parser_Debug_Mode();

/**
 * @brief debug mode, get the mode is debug mode or not.
 * @author:jacky zeng
 */
int debug_Mode();

 /**
 * @brief Read IO stream for the parsers.This function read io stream for the parsers
 * @param[in] GQIO_S* pgqio  : io data stream
 * @param[in] uint8_t* buffer  : data buffer
 * @param[in] int32_t count  : data buffer size
 * @param[in] int32_t tosecs  : seconds to wait for results
 * @param[out] Output : NULL
 * @return int tc: write size
 * <AUTHOR> zeng
 * @data   2021-11-20
*/
int32_t parser_common_read(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs);
int32_t parser_GDI_read(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs);

 /**
 * @brief  Write data to IO stream.This function write data to io stream for the parsers
 * @param[in] GQIO_S* pgqio  : io stream
 * @param[in] uint8_t* buffer  : data buffer
 * @param[in] int32_t count  : data buffer size
 * @param[in] int32_t tosecs  : seconds to wait for results
 * @param[out] Output : NULL
 * @return int tc: write size
 * <AUTHOR> zeng
 * @data   2021-11-20
*/
int32_t parser_common_write(GQIO_S* pgqio, uint8_t* buffer, int32_t count, int32_t tosecs);
int32_t parser_common_rewind(GQIO_S* pgqio, uint8_t* buffer, int32_t count);

 /**
 * @brief  uint8_t convert to uint16 with big-endian format.
 * @param[in] uint8_t  : uint8_t front
 * @return uint16
 * <AUTHOR> zeng
 * @data   2021-11-20
*/
uint16_t u8_to_u16_be(uint8_t, uint8_t);

 /**
 * @brief  uint16 with big-endian format convert to uint8_t buffer[2].
 * @param[in] uint16 src  : uint16 src
 * @param[out] uint8_t *buffer : uint8_t *buffer
 * @return void
 * <AUTHOR> zeng
 * @data   2021-11-20
*/
void be_u16_to_u8_buffer(uint16_t src, uint8_t *buffer);

/**
 * @brief  uint8_t convert to uint32 with big-endian format
 * @param[in] uint8_t first  : uint8_t first
 * @param[in] uint8_t second  : uint8_t second
 * @param[in] uint8_t third  : uint8_t third
 * @param[in] uint8_t fourth  : uint8_t fourth
 * @param[out] Output : NULL
 * @return uint32_t value
 * <AUTHOR> zeng
 * @data   2021-11-20
*/
uint32_t u8_to_u32_be(uint8_t first, uint8_t second, uint8_t third, uint8_t fourth);


#ifdef __cplusplus
}
#endif
#endif
/**
 *@}
 */
