/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file platform_aclregister.c
 * @addtogroup platform
 * @{
 * @addtogroup platform_aclregister
 * <AUTHOR>
 * @date 2022-02-16
 * @brief acl module  settings local acl parser will do some machine settings.\n
 *        for example: get mac address burn flash, and so on.
*/

#include <sys/types.h>
#include <sys/stat.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/reboot.h>
#include <linux/reboot.h>
#include "pol/pol_threads.h"
#include "pol/pol_mem.h"
#include "pol/pol_endian.h"
#include "pol/pol_io.h"
#include "pol/pol_string.h"
#include "pol/pol_log.h"
#include "utilities/msgrouter.h"
#include "msgrouter_main.h"
#include "usb/usbdservice.h"
#include "event_manager/event_mgr.h"
#include "qio/qio_general.h"
#include "platform_aclregister.h"
#include "platform_api.h"
#include "acl/acl_attribute.h"
#include "acl/acl.h"
#include "public_data_proc.h"
#include "md5c.h"
#include "event_manager/event_mgr_typedef.h"
#include "system_manager/statusid.h"

#define DIRECT_LEN 16
#define SERIAL_NUMBER_FIXED_LEN 13              ///< 设备序列号可修改的长度为13

static const char UEL[] = "\x1b%-12345X";
static const char HEAD2[] = "@PJL ENTER LANGUAGE=ACL\r\n";
static const char ACL_LICENSOR[]  = "@PJL LICENSOR=SysAdmin\xD\xA";

/* ACLcmd -启老化测试相关 */
static void acl_fct_status_notify(uint8_t fct_mode)
{
    ROUTER_MSG_S msg;

    msg.msgType   = MSG_EW_FCT_STATUS;
    msg.msg1      = fct_mode;
    msg.msg2      = 0;
    msg.msg3      = NULL;
    msg.msgSender = MID_PARSER_ACL;

    if ( 0 != task_msg_send_by_router(MID_PANEL, &msg) )
    {
        pi_log_e("Send msg ERROR!\n");
    }
}

static int32_t acl_set_serial_number(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    /* serial_number fixed len is 13 */
    int32_t data_length = SERIAL_NUMBER_FIXED_LEN;
    int32_t recv_length;
    SERIAL_NUMBER_DATDA_STRUCT_S serialData;
    pi_memset(&serialData, 0, (size_t)data_length);

    /* pi_read  serial_number data */
    recv_length = parser_common_read(pgqio, (uint8_t *)&serialData, data_length, PARSER_USB_TIMEOUT);
    if ( recv_length <= 0 )
    {
        pi_log_e("serial number data pi_read error!\n");
        return PARSER_ERROR;
    }

    if ( recv_length < data_length )
    {
        pi_log_e("Not enough data to pi_read, need %d data, but only have %d data!\n", data_length, recv_length);
        return PARSER_ERROR;
    }
    pi_log_d( "serial number:%s\n", (uint8_t *)&serialData);

    /* set serial_number to platform */
    pi_platform_set_serial_number((char *)&serialData, data_length);

    return PARSER_SUCCESS;
}

static int32_t acl_get_serial_number(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    /* serial_number fixed len is SERIAL_NUMBER_FIXED_LEN(13) */
    char buffer[SERIAL_NUMBER_FIXED_LEN];
    pi_memset(buffer, 0, SERIAL_NUMBER_FIXED_LEN);
    pi_platform_get_serial_number(buffer, SERIAL_NUMBER_FIXED_LEN);
    pi_log_d( "serial number:%s\n", buffer);
    acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, (uint8_t *)buffer, SERIAL_NUMBER_FIXED_LEN);

    return PARSER_SUCCESS;
}

/* ACLcmd-设置生产日期 */
static int32_t acl_set_production_date(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
	return PARSER_SUCCESS;
}
static int32_t acl_get_production_date(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
	return PARSER_SUCCESS;
}

static int32_t acl_set_production_batch(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
	return PARSER_SUCCESS;
}

static int32_t acl_get_production_batch(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
	return PARSER_SUCCESS;
}

static int32_t acl_set_restore_factory(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
	return PARSER_SUCCESS;
}

static int32_t acl_set_engine_type(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    return PARSER_SUCCESS;
}

static int32_t acl_rewind_really_data(GQIO_S* pqio, int32_t readDataLen, int32_t tosecs)
{
    int32_t needToReadDataLen;
    int32_t alignDataLen;
    char * buf;
    //0x32 = pi_strlen(UEL)+pi_strlen(HEAD2)+sizeof(ACL_CMD_BASE_STRUCT_S)

    readDataLen += 0x32;
    alignDataLen = 0x10 - ((readDataLen + pi_strlen(UEL)) & 0x0F);
    if ( 0 == (alignDataLen % 0x10) )
    {
        alignDataLen = 0;
    }
    needToReadDataLen = pi_strlen(UEL) + alignDataLen;
    buf = (char*)pi_malloc(needToReadDataLen);
    if ( NULL == buf )
    {
        return -1;//buff alloc fail
    }
    if ( needToReadDataLen != parser_common_read(pqio, (uint8_t *)buf, needToReadDataLen, tosecs) )
    {
        pi_free(buf);
        return -2;
    }
    parser_common_rewind(pqio, (uint8_t *)buf, pi_strlen(UEL));
    pi_free(buf);

    return 0;
}

static int32_t acl_get_engine_type(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    return PARSER_SUCCESS;
}

static int32_t acl_write_file(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    WRITE_FILE_CMD_S *cmd = (WRITE_FILE_CMD_S *)acl_cmd;
    int32_t file_path_len = be16_to_cpu(cmd->path_len);
    int32_t file_data_len = be32_to_cpu(cmd->data_len);
    int32_t recv_length;
    pi_log_d( "file path len:%d\n", file_path_len);

    if ( 0 >= file_path_len )
    {
        pi_log_e("pi_write file path len %d error!\n", file_path_len);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    char *file_path = (char *)pi_malloc((size_t)(file_path_len + 1));
    if ( NULL == file_path )
    {
        pi_log_e("malloc buffers error!\n");
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    pi_memset(file_path, 0, file_path_len + 1);
    recv_length = parser_common_read(pgqio, (uint8_t *)file_path, file_path_len, PARSER_USB_TIMEOUT);
    file_path[file_path_len] = '\0';
    pi_log_d( "file path %s\n", file_path);

    if ( 0 >= recv_length )
    {
        pi_log_e("receive data error!\n");
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    if ( recv_length < file_path_len )
    {
        pi_log_e("receive data length %d, need length %d\n", recv_length, file_path_len);
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    pi_log_d("file date len = %d \n",file_data_len);
    char *file_data = (char *)pi_malloc((size_t)file_data_len);
    if ( NULL == file_data )
    {
        pi_log_e("pi_malloc buffers error!\n");
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    pi_memset(file_data, 0, sizeof(file_data_len));
    recv_length = parser_common_read(pgqio, (uint8_t *)file_data, file_data_len, PARSER_USB_TIMEOUT);
    if ( 0 >= recv_length )
    {
        pi_log_e("receive data error!\n");
        pi_free(file_path);
        pi_free(file_data);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    int32_t fd = pi_open(file_path, O_WRONLY | O_CREAT | O_TRUNC, S_IRWXU | S_IRGRP | S_IROTH); // -rwxr--r-- 权限
    if ( fd < 0 )
    {
        pi_log_e("pi_open file %s error.\n", file_path);
        pi_free(file_path);
        pi_free(file_data);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    int32_t write_size = pi_write(fd, file_data, (size_t)file_data_len);
    if ( write_size != file_data_len )
    {
        pi_log_e("pi_write file %s error. write_size = %d, file_data_len = %s\n", file_path, write_size, file_data_len);
        pi_free(file_path);
        pi_free(file_data);
        pi_close(fd);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    else
    {
        pi_free(file_path);
        pi_free(file_data);
        pi_close(fd);
        acl_direct_response(pgqio, (ACL_CMD_BASE_STRUCT_S *)cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);

        pi_log_d( "AclWriteFile success\n");

        return PARSER_SUCCESS;
    }
}

static int32_t acl_read_file(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    int32_t status = 1;
    READ_FILE_CMD_S *cmd = (READ_FILE_CMD_S *)acl_cmd;
    int32_t file_path_len = be16_to_cpu(cmd->len);
    int32_t recv_length;
    pi_log_d( "file path len:%d\n", file_path_len);

    if ( 0 >= file_path_len )
    {
        pi_log_e("pi_read file path len %d error!\n", file_path_len);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    char *file_path = (char *)pi_malloc((size_t)(file_path_len + 1));
    if ( NULL == file_path )
    {
        pi_log_e("malloc buffers error!\n");
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    pi_memset(file_path, 0, file_path_len + 1);
    recv_length = parser_common_read(pgqio, (uint8_t *)file_path, file_path_len, PARSER_USB_TIMEOUT);
    file_path[file_path_len] = '\0';
    pi_log_d( "file path %s\n", file_path);

    if ( 0 >= recv_length )
    {
        pi_log_e("receive data error!\n");
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    if ( recv_length < file_path_len )
    {
        pi_log_e("receive data length %d, need length %d\n", recv_length, file_path_len);
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    if ( access(file_path, R_OK) )
    {
        pi_log_e("file %s can not pi_read or file is not exist\n", file_path);
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    struct stat statbuf;
    if ( 0 != stat(file_path, &statbuf) )
    {
        pi_log_e("%s file can not stat.\n", file_path);
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    if ( 0 >= statbuf.st_size )
    {
        pi_log_e("Read %s file size is zero.\n", file_path);
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }

    pi_log_d( "file size = %ld bytes\n", (unsigned long)statbuf.st_size);

    char *read_data = (char *)pi_malloc((size_t)(statbuf.st_size));
    if ( NULL == read_data )
    {
        pi_log_e("pi_malloc pi_read data buffers error.\n");
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    pi_memset(read_data, 0x00, statbuf.st_size);

    int32_t fd = pi_open(file_path, O_RDONLY);
    if ( fd < 0 )
    {
        pi_log_e("pi_open file %s error.\n", file_path);
        pi_free(file_path);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        pi_free(read_data);
        return PARSER_ERROR;
    }

    int32_t read_size = pi_read(fd, read_data, (size_t)statbuf.st_size);
    pi_log_d( "pi_read size = %d\n", read_size);
    if ( read_size < statbuf.st_size )
    {
        pi_log_e("file size is %d, but only pi_read file size %d.\n", statbuf.st_size, read_size);
        status = 0;
    }
    pi_close(fd);

    ACL_RESPONSE_READ_FILE_STRUCT_S *read_file_cmd = (ACL_RESPONSE_READ_FILE_STRUCT_S *)(acl_construct_response_buffer(be16_to_cpu(acl_cmd->cmd_id)));
    read_file_cmd->cmd_status = cpu_to_be16((uint16_t)status);
    read_file_cmd->len = cpu_to_be32((uint32_t)read_size);
    acl_response(pgqio, (ACL_RESPONSE_BASE_STRUCT_S *)read_file_cmd, (uint8_t *)read_data, (uint32_t)read_size);

    pi_free(read_file_cmd);
    pi_free(read_data);
    pi_free(file_path);
    pi_log_d( "AclReadFile success\n");

    return PARSER_SUCCESS;
}

static int32_t acl_set_aging_test(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    AGING_TEST_S *aging_test = NULL;

    if ( NULL == acl_cmd )
    {
        pi_log_e("receive acl_cmd error!\n");
        return PARSER_ERROR;
    }

    aging_test = (AGING_TEST_S *)acl_cmd;
    pi_log_d("set printer aging mode:%d\n",aging_test->mode);
    acl_fct_status_notify(aging_test->mode);
    acl_direct_response(pgqio, (ACL_CMD_BASE_STRUCT_S *)acl_cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);

    return PARSER_SUCCESS;
}


static int32_t acl_port_test(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    PORT_TEST_CMD_S *cmd = (PORT_TEST_CMD_S *)acl_cmd;
    if ( cmd == NULL )
    {
        return PARSER_ERROR;
    }
    uint32_t data_len = be32_to_cpu(cmd->len);
    int32_t ret = 0;

    if ( data_len == 0 )
    {
        return PARSER_ERROR;
    }

    pi_log_d( "data len %d\n", data_len);
    char *buffer = (char *)pi_malloc(data_len);
    if ( !buffer )
    {
        pi_log_e("malloc buffers error!\n");
        return PARSER_ERROR;
    }
    pi_memset(buffer, 0, data_len);
    ret = parser_common_read(pgqio, (uint8_t *)buffer, data_len, PARSER_USB_TIMEOUT);
    if ( ret <= 0 )
    {
        pi_log_e("receive data error!\n");
        pi_free(buffer);
        return PARSER_ERROR;
    }
#if CONFIG_USBDEVICE
    pi_log_d( "Push test data head to usb\n");
    if ( LEN_ACL_CMD != pi_usbd_server_push_raw_data(USB_PRT_INTERFACE, (char *)cmd, LEN_ACL_CMD) )
    {
        pi_log_e("Push test data to usb error\n");
        pi_free(buffer);
        return PARSER_ERROR;
    }

    pi_log_d( "Push test data body to usb\n");
    if ( data_len != pi_usbd_server_push_raw_data(USB_PRT_INTERFACE, buffer, data_len) )
    {
        pi_log_e("Push test data to usb error\n");
        pi_free(buffer);
        return PARSER_ERROR;
    }
#else
    pi_log_d( "UNSUPPORT USBDEVICE\n");
#endif
    pi_free(buffer);

    return PARSER_SUCCESS;
}

static int32_t acl_set_printer_reboot(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    //baikal unsupport soft-reboot

    return PARSER_SUCCESS;
}

static int32_t acl_set_system_config(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    if (!pgqio || !acl_cmd)
    {
        return -1;
    }

    int32_t i       = 0;
    int32_t rtn     = PARSER_SUCCESS;
    int32_t recvlen = 0;
    int16_t index   = 0;
    char set_data[14];

    pi_memset(&set_data, 0, sizeof(set_data));
    index = (acl_cmd->temp[0] << 8) + acl_cmd->temp[1]; //index: language config/ date config/ sleep time config/and so on
    pi_log_e("Set System Config index=%x\n", index);

    // read data
    recvlen = parser_common_read(pgqio, (uint8_t *)&set_data, sizeof(set_data), PARSER_USB_TIMEOUT);
    if ( recvlen <= 0 )
    {
        pi_log_e("Set System Config error! %d\n", recvlen);
        rtn = PARSER_ERROR;
    }
    else
    {
        switch (index)
        {
        case 0x01: //language config
            break;
        case 0x02: //date config
            break;
        case 0x03: //sleep time config
            break;
        case 0x04: // save toner config
            break;
        case 0x05: //silence print config
            break;
        case 0x06: //recover defaul config
            pi_log_d( "recover default %s\n", (1 == set_data[13]) ? "ON" : "OFF");
            if ( 1 == set_data[13] )
            {
                pi_platvars_restore_factory_default((uint32_t)1);
            }
            break;
        case 0xFFFFFFFF: //set all configs
            pi_platform_set_quiet((uint32_t)set_data[12]);
            break;
        default:
            rtn = PARSER_STALLDATA_ERROR;
            pi_log_e("Set System Config invalid index=%d\n", index);
            break;
        }
    }
    //acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);

    return rtn;
}


static int32_t acl_get_system_config(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    if ( !pgqio || !acl_cmd )
    {
        return -1;
    }
    int32_t i           = 0;
    int32_t rtn         = PARSER_SUCCESS;
    int16_t index       = 0;
    uint32_t tonerSave  = 0;
    uint32_t quiet      = 0;
    char get_data[13];

    pi_memset(&get_data, 0, sizeof(get_data));
    index = (acl_cmd->temp[0] << 8) + acl_cmd->temp[1];//index: language config/ date config/ sleep time config/and so on
    pi_log_e("Get System Config index=%x\n", index);

    if (NULL != acl_cmd)
    {
        switch ( index )
        {
        case 0x01:
            break;
        case 0x02:
            break;
        case 0x03:
            break;
        case 0x04:
            break;
        case 0x05:
            pi_platform_get_quiet(&quiet);
            get_data[12] = quiet & 0xff;
            pi_log_d( "Get sound off mode %s\n", (1 == get_data[12]) ? "ON" : "OFF");
            break;
        case 0xFFFFFFFF:// get all configs
            break;
        default:
            rtn = PARSER_STALLDATA_ERROR;
            pi_log_e("Get System Config invalid index=%d\n", index);
            break;
        }
        acl_merge_response(pgqio, acl_cmd, (int16_t)(ACLCMD_STATUS_SUCCESS), (uint8_t *)get_data, (DIRECT_LEN + sizeof(get_data)));
    }
    return rtn;
}

static int32_t check_time_value(ACL_CMD_BASE_STRUCT_S *acl_cmd)
{
    int32_t year    = (acl_cmd->temp[0] * 16 * 16) + (int32_t)acl_cmd->temp[1];
    int32_t month   = acl_cmd->temp[2];
    int32_t day     = acl_cmd->temp[3];
    int32_t hour    = acl_cmd->temp[4];
    int32_t minute  = acl_cmd->temp[5];
    int32_t second  = acl_cmd->temp[6];

    pi_log_d("systime value get: year:%d month:%d day:%d hour:%d minute:%d second:%d\n",
        year, month, day, hour, minute, second);
    if ( (year > 0xffff) || (month > 0x0c) || (day > 0x1f) ||
        (hour > 0x17) || (minute > 0x3b) || (second > 0x3b) )
    {
        return PARSER_ERROR;
    }
    return PARSER_SUCCESS;
}


static int32_t acl_set_system_time(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    char cmd[32] = "date -s ";
    char time[21] = {0};

    int32_t ret = check_time_value(acl_cmd);
    if ( ret != PARSER_SUCCESS )
    {
        pi_log_e("systime value check error!\n");
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    int32_t tmp = (acl_cmd->temp[0] * 16* 16) + (int32_t)acl_cmd->temp[1];
    pi_snprintf(time, 21, "%d-%d-%d %d:%d:%d", tmp,  acl_cmd->temp[2], acl_cmd->temp[3],
        acl_cmd->temp[4],acl_cmd->temp[5],acl_cmd->temp[6]);

    pi_strncat(cmd, "\"", pi_strlen("\""));
    pi_strncat(cmd, time, pi_strlen(time));
    pi_strncat(cmd, "\"", pi_strlen("\""));

    (void)pi_runcmd(NULL, 0, 0, cmd);
    (void)pi_runcmd(NULL, 0, 0, "hwclock -w");

    pi_log_d("ACLCMD set systime %s ok!\n", time);
    acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);

    return PARSER_SUCCESS;
}

static int32_t acl_set_board_ssn(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    int32_t     ret = PARSER_SUCCESS;
    int16_t     data_type;                  // 子板卡类型
    uint16_t    data_length;                // 子板卡序列号长度
    int32_t     recv_length;
    char        serial_data[32] = {0};

    data_length = (acl_cmd->temp[0] << 8) + acl_cmd->temp[1];
    data_type   =  acl_cmd->temp[2];        // 0x00：面板
    pi_log_d("Set BOARD SSN data_type = %x length(%d)\n", data_type, data_length);

    recv_length = parser_common_read(pgqio, (uint8_t *)&serial_data, data_length, PARSER_USB_TIMEOUT);
    if ( recv_length <= 0 )
    {
        pi_log_e("BOARD SSN data pi_read error!\n");
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    if ( recv_length < data_length )
    {
        pi_log_e("Not enough data to pi_read, need %d data, but only have %d data!\n", data_length, recv_length);
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        return PARSER_ERROR;
    }
    pi_log_d( "BOARD SSN:{%s}\n", (uint8_t *)&serial_data);

    switch (data_type)
    {
    case 0x00:                 // 0x00：面板
        ret = pi_platform_set_panel_board_ssn((char *)&serial_data, PANEL_BOARD_SERIAL_NUMBER_LEN);
        if ( -1 == ret )
        {
            pi_log_e("Set BOARD SSN failed(platform)\n");
            ret = PARSER_ERROR;
        }
        break;
    default:
        pi_log_e("Set BOARD SSN invalid data_type=%d\n", data_type);
        ret = PARSER_ERROR;
        break;
    }
     if ( -1 == ret )
    {
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
        ret = PARSER_ERROR;
    }
    else
    {
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, NULL, 0);
        ret = PARSER_SUCCESS;
    }

    return ret;
}

static int32_t acl_get_board_ssn(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data)
{
    int32_t     ret = PARSER_SUCCESS;
    int16_t     data_length = 0;          // 回复数据长度
    int16_t     data_type   = 0;          // 子板卡类型
    char        set_data[32] = {0};

    if ( NULL == acl_cmd )
    {
        ret = PARSER_ERROR;
    }
    else
    {
        data_type = acl_cmd->temp[0];        // 0x00：面板
        pi_log_d("Get BOARD SSN data_type = %x \n", data_type);
        switch ( data_type )
        {
        case 0x00:
            data_length = PANEL_BOARD_SERIAL_NUMBER_LEN;
            pi_platform_get_panel_board_ssn(set_data, data_length);
            pi_log_d( "BOARD SSN:{%s}\n", (uint8_t *)&set_data);
            break;
        default:
            ret = PARSER_ERROR;
            pi_log_e("Get BOARD SSN invalid data_type=%d\n", data_type);
            break;
        }
    }
    if ( PARSER_ERROR == ret )
    {
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_FAILURE, NULL, 0);
    }
    else
    {
        acl_direct_response(pgqio, acl_cmd, ACLCMD_STATUS_SUCCESS, (uint8_t *)set_data, data_length);
    }

    return ret;
}

static int32_t platformoid_set_product_model(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_machine_type(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_product_model(uint32_t *val)
{
    int32_t ret;

    ret = pi_platform_get_machine_type(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_country_code(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_countrycode((uint16_t)val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_country_code(uint32_t *val)
{
    int32_t ret;

    ret = pi_platform_get_countrycode((uint16_t *)val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_auto_shutdown_enable(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_auto_shutdown_enable(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_auto_shutdown_enable(uint32_t *val)
{
    int32_t ret;

    ret = pi_platform_get_auto_shutdown_enable(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_auto_shutdown_time(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_auto_shutdown_time(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_auto_shutdown_time(uint32_t *val)
{
    int32_t ret;

    ret = pi_platform_get_auto_shutdown_time(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_auto_shutdown_condition(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_auto_shutdown_condition(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_auto_shutdown_condition(uint32_t *val)
{
    int32_t ret;

    ret = pi_platform_get_auto_shutdown_condition(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_restore_factory_default(uint32_t val)
{
    int32_t ret;

    ret = pi_platvars_restore_factory_default(val);;
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_language_code(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_language_code((uint16_t)val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_language_code(uint32_t *val)
{
    int32_t ret;

    ret = pi_platform_get_language_code((uint16_t *)val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_uart_enable(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_uart_enable(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_system_reboot(uint32_t val)
{
    int32_t ret;

    ret = pi_platform_set_system_reboot(val);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_fax_page_count(uint32_t val)
{
    /* baikal unsupport FAX */
    /* clear FAX total page count */
    return PARSER_SUCCESS;
}

static int32_t platformoid_get_fax_page_count(uint32_t *val)
{
   /* baikal unsupport FAX */
    *val = 0;
    return PARSER_SUCCESS;
}

static int32_t platformoid_set_netlog_enable(uint32_t val)
{
    int32_t ret;
    pi_log_set_level(val);

    //系统api
    //出错处理返回PARSER_ERROR
    return PARSER_SUCCESS;
}

static int32_t platformoid_set_serial_number(char* val, uint32_t len)
{
    int32_t ret;

    ret = pi_platform_set_serial_number(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_serial_number(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_serial_number(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_fw_version(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_firmware_version(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_eng_fw_version(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_engine_firmware_version(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_panel_fw_versions(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_panel_fw_version(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_print_name_string(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_print_name_string(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_product_date(char* val, uint32_t len)
{
    int32_t ret;

    ret = pi_platform_set_product_date(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_product_date(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_product_date(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_kernel_version(char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_kernel_version(val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_set_system_time(uint32_t index, char* val, uint32_t len)
{
    int32_t ret;

    ret = pi_platform_set_system_time(index, val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

static int32_t platformoid_get_system_time(uint32_t index, char* val, uint32_t len)
{
   int32_t ret;

    ret = pi_platform_get_system_time(index, val, (uint32_t)len);
    if ( 0 != ret )
    {
        return PARSER_ERROR;
    }

    return PARSER_SUCCESS;
}

void platform_register_acl_cmds( void )
{
    acl_register_cmd(ACLCMD_SET_SERIAL_NUMBER,   acl_set_serial_number,     (void *)0);
    acl_register_cmd(ACLCMD_GET_SERIAL_NUMBER,   acl_get_serial_number,     (void *)0);
    acl_register_cmd(ACLCMD_SET_PRODUCT_DATE,    acl_set_production_date,   (void *)0);
    acl_register_cmd(ACLCMD_GET_PRODUCT_DATE,    acl_get_production_date,   (void *)0);
    acl_register_cmd(ACLCMD_SET_PRODUCT_BATCH,   acl_set_production_batch,  (void *)0);
    acl_register_cmd(ACLCMD_GET_PRODUCT_BATCH,   acl_get_production_batch,  (void *)0);
    acl_register_cmd(ACLCMD_SET_ENGINE_TYPE,     acl_set_engine_type,       (void *)0);
    acl_register_cmd(ACLCMD_GET_ENGINE_TYPE,     acl_get_engine_type,       (void *)0);
    acl_register_cmd(ACLCMD_WRITE_FILE,          acl_write_file,            (void *)0);
    acl_register_cmd(ACLCMD_READ_FILE,           acl_read_file,             (void *)0);
    acl_register_cmd(ACLCMD_SET_PREBURNING_TEST, acl_set_aging_test,        (void *)0);
    acl_register_cmd(ACLCMD_PORT_TEST,           acl_port_test,             (void *)0);
    acl_register_cmd(ACLCMD_SET_RESTORE_FACTORY, acl_set_restore_factory,   (void *)0);
    //acl_register_cmd(ACLCMD_SET_PRINTER_REBOOT,  acl_set_printer_reboot,    (void *)0);(LP2023指令,与获取打印机系统网络信息（0xFF29）冲突)
    acl_register_cmd(ACLCMD_SET_SYSTEM_CONFIG,   acl_set_system_config,     (void *)0);
    acl_register_cmd(ACLCMD_GET_SYSTEM_CONFIG,   acl_get_system_config,     (void *)0);
    acl_register_cmd(ACLCMD_SET_SYS_TIME,        acl_set_system_time,       (void *)0);
    acl_register_cmd(ACLCMD_SET_SUB_BOARD_SSN,   acl_set_board_ssn,         (void *)0);
    acl_register_cmd(ACLCMD_GET_SUB_BOARD_SSN,   acl_get_board_ssn,         (void *)0);

    acl_attribute_init();
    acl_register_cmd(ACLCMD_SET_PRINTER_ATTRIBUTES,  acl_set_printer_attributes, (void *)0);
    acl_register_cmd(ACLCMD_GET_PRINTER_ATTRIBUTES,  acl_get_printer_attributes, (void *)0);


}

void platform_register_oid_attr(void)
{


    pi_log_d( "register attr the platform attr key  in the register list!\n");

    acl_attribute_uint32_value_register("OID_PLATFORM_PRODUCT_MODEL",             platformoid_set_product_model,                     platformoid_get_product_model );
    acl_attribute_uint32_value_register("OID_PLATFORM_COUNTRY_CODE",              platformoid_set_country_code,                      platformoid_get_country_code );
    acl_attribute_uint32_value_register("OID_PLATFORM_AUTO_SHUTDOWN_ENABLED",     platformoid_set_auto_shutdown_enable,              platformoid_get_auto_shutdown_enable );
    acl_attribute_uint32_value_register("OID_PLATFORM_AUTO_SHUTDOWN_TIME",        platformoid_set_auto_shutdown_time,                platformoid_get_auto_shutdown_time );
    acl_attribute_uint32_value_register("OID_PLATFORM_AUTO_SHUTDOWN_CONDITION",   platformoid_set_auto_shutdown_condition,           platformoid_get_auto_shutdown_condition );
    acl_attribute_uint32_value_register("OID_PLATFORM_RESTORE_DEFAULT",           platformoid_restore_factory_default,               NULL );
    acl_attribute_uint32_value_register("OID_PLATFORM_LANGUAGE_CODE",             platformoid_set_language_code,                     platformoid_get_language_code );
    acl_attribute_uint32_value_register("OID_PLATFORM_UART_ENABLE_FLAG",          platformoid_set_uart_enable,                       NULL );
    acl_attribute_uint32_value_register("OID_PLATFORM_SYSTEM_REBOOT",             platformoid_set_system_reboot,                     NULL );
    acl_attribute_uint32_value_register("OID_FAX_TOTAL_PAGE_COUNT",               platformoid_set_fax_page_count,                    platformoid_get_fax_page_count );
    acl_attribute_uint32_value_register("OID_PLATFORM_NETLOG_ENABLED",            platformoid_set_netlog_enable,                     NULL );

    acl_attribute_string_value_register("OID_PLATFORM_PRODUCT_SERIAL_NUM",        platformoid_set_serial_number,                     platformoid_get_serial_number );
    acl_attribute_string_value_register("OID_PLATFORM_FW_VERSION",                NULL,                                              platformoid_get_fw_version );
    acl_attribute_string_value_register("OID_PLATFORM_ENG_FW_VERSION",            NULL,                                              platformoid_get_eng_fw_version );
    acl_attribute_string_value_register("OID_PLATFORM_PANEL_FW_VERSION",          NULL,                                              platformoid_get_panel_fw_versions );
    acl_attribute_string_value_register("OID_PLATFORM_PRINT_NAME_STRING",         NULL,                                              platformoid_get_print_name_string );
    acl_attribute_string_value_register("OID_PLATFORM_PRODUCT_DATE",              platformoid_set_product_date,                      platformoid_get_product_date );
    acl_attribute_string_value_register("OID_PLATFORM_KERNEL_VERSION",            NULL,                                              platformoid_get_kernel_version );
    acl_attribute_string_index_register("OID_PLATFORM_SYSTEM_TIME",               platformoid_set_system_time,                       platformoid_get_system_time );
}
/**
 *@}
 */

