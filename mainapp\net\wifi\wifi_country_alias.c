#include <stdint.h>
#include "pol/pol_define.h"

const char* s_wifi_country_alias[] =
{
    "AR",   ///< 1   阿根廷
    "AU",   ///< 2   澳大利亚
    "AT",   ///< 3   奥地利
    "BY",   ///< 4   白俄罗斯
    "BE",   ///< 5   比利时
    "BO",   ///< 6   玻利维亚
    "BR",   ///< 7   巴西
    "BN",   ///< 8   文莱
    "BG",   ///< 9   保加利亚
    "KH",   ///< 10  柬埔寨
    "CA",   ///< 11  加拿大
    "TD",   ///< 12  乍得
    "CL",   ///< 13  智利
    "CN",   ///< 14  中国
    "CO",   ///< 15  哥伦比亚
    "KM",   ///< 16  哥斯达黎加
    "HR",   ///< 17  克罗地亚
    "CO",   ///< 18  古巴，Wi-Fi芯片驱动不支持，映射到同信道的哥伦比亚
    "CY",   ///< 19  塞浦路斯
    "CZ",   ///< 20  捷克
    "DK",   ///< 21  丹麦
    "DO",   ///< 22  多米尼加
    "EC",   ///< 23  厄瓜多尔
    "EG",   ///< 24  埃及
    "GB",   ///< 25  英格兰
    "EE",   ///< 26  爱沙尼亚
    "FJ",   ///< 27  斐济
    "FI",   ///< 28  芬兰
    "FR",   ///< 29  法国
    "DE",   ///< 30  德国
    "GR",   ///< 31  希腊
    "GT",   ///< 32  危地马拉
    "HN",   ///< 33  洪都拉斯
    "HK",   ///< 34  香港
    "HU",   ///< 35  匈牙利
    "IS",   ///< 36  冰岛
    "IN",   ///< 37  印度
    "ID",   ///< 38  印尼
    "IR",   ///< 39  伊朗
    "IQ",   ///< 40  伊拉克
    "IE",   ///< 41  爱尔兰
    "IL",   ///< 42  以色列
    "IT",   ///< 43  意大利
    "JP",   ///< 44  日本
    "JO",   ///< 45  约旦
    "KZ",   ///< 46  哈萨克斯坦
    "KR",   ///< 47  韩国
    "KW",   ///< 48  科威特
    "LV",   ///< 49  拉脱维亚
    "LB",   ///< 50  黎巴嫩
    "LI",   ///< 51  列支敦斯登
    "LT",   ///< 52  立陶宛
    "LU",   ///< 53  卢森堡
    "MK",   ///< 54  马其顿
    "MY",   ///< 55  马来西亚
    "MT",   ///< 56  马耳他
    "MX",   ///< 57  墨西哥
    "MC",   ///< 58  摩纳哥
    "MN",   ///< 59  蒙古
    "MA",   ///< 60  摩洛哥
    "NL",   ///< 61  荷兰
    "NZ",   ///< 62  新西兰
    "GB",   ///< 63  北非，Wi-Fi芯片驱动不支持,映射到同信道的英国
    "NO",   ///< 64  挪威
    "OM",   ///< 65  阿曼
    "PK",   ///< 66  巴基斯坦
    "PA",   ///< 67  巴拿马
    "PY",   ///< 68  巴拉圭
    "PE",   ///< 69  秘鲁
    "PH",   ///< 70  菲律宾
    "PL",   ///< 71  波兰
    "PT",   ///< 72  葡萄牙
    "RO",   ///< 73  罗马尼亚
    "RU",   ///< 74  俄罗斯
    "WS",   ///< 75  萨摩亚
    "SA",   ///< 76  沙特阿拉伯
    "SG",   ///< 77  新加坡
    "SK",   ///< 78  斯洛伐克
    "SI",   ///< 79  斯洛文尼亚
    "ZA",   ///< 80  南非
    "KR",   ///< 81  南韩
    "ES",   ///< 82  西班牙
    "LK",   ///< 83  斯里兰卡
    "",     ///< 84  苏丹，驱动不支持,目前认证科没有给出该国家的信道信息，所以配置为""，后续终止WiFi组件初始化
    "SE",   ///< 85  瑞典
    "CH",   ///< 86  瑞士
    "GB",   ///< 87  叙利亚，Wi-Fi芯片驱动不支持，映射到同信道的英国
    "TW",   ///< 88  台湾
    "TH",   ///< 89  泰国
    "TN",   ///< 90  突尼斯
    "TR",   ///< 91  土耳其
    "AE",   ///< 92  阿联酋
    "GB",   ///< 93  英国
    "UA",   ///< 94  乌克兰
    "US",   ///< 95  美国
    "UY",   ///< 96  乌拉圭
    "VE",   ///< 97  委内瑞拉
    "VN",   ///< 98  越南
    "YE",   ///< 99  也门
    "GE",   ///< 100 格鲁吉亚
    "AZ",   ///< 101 阿塞拜疆
    "US",   ///< 102 拉丁美洲，Wi-Fi芯片驱动不支持，映射到同信道的美国
    "CG",   ///< 103 刚果
    "JM",   ///< 104 牙买加
    "RS",   ///< 105 塞尔维亚
    "MV",   ///< 106 马尔代夫
    "DZ",   ///< 107 阿尔及利亚
    "BD",   ///< 108 孟加拉国
    "BA",   ///< 109 波黑
    "ME",   ///< 110 黑山
    "RS"    ///< 111 科索沃
};

uint32_t s_wifi_country_alias_count = ARRAY_SIZE(s_wifi_country_alias);
