# 打印引擎类型
config PRINT_ENGINE_TYPE
    string "print engine type"
    depends on PRINT
    default "baikal"
    help
        "print engine type: baikal or kanas or ..." 

# U盘打印支持
config PRINT_UDISK
    bool "udisk job"
    depends on PRINT && IPS
    help
        "udisk print support"

# 密码打印支持
config PRINT_PINCODE
    bool "pincode job"
    depends on PRINT && IPS
    help
        "pincode job support"

# 样本打印支持
config PRINT_SWATCH
    bool "swatch job"
    depends on PRINT && IPS
    help
        "swatch job support"

# 零边距打印支持
config PRINT_ZERO_MARGIN
    bool "zero margin job"
    depends on PRINT
    help
        "zero margin job support"

# 延时打印支持
config PRINT_DELAY
    bool "delay job"
    depends on PRINT
    help
        "delay job support"

# 装订打印支持
config PRINT_FINISHER
    bool "finisher job"
    depends on PRINT
    help
        "finisher job support"

# 数据清除
config DATA_CLEAN
    int "data clean"
    default 0
    help
        "data clean"

# 打印数据异或加密
config PRINT_JOBDATA_XOR
    bool "print jobdata xor"
    help
        "jobdata xor support"