#ifndef _PEDK_NET_SETTING_
#define _PEDK_NET_SETTING_

#include <quickjs.h>

#define SMB_ADDR_LEN 256
#define SMB_SERVER_PATH_LEN 256
#define SMB_LOGIN_NAME_LEN  128
#define SMB_LOGIN_PWD_LEN   41

#include <string.h>
#include <stdio.h>
#include "PEDK_net_setting.h"
#include "PEDK_event.h"

#include <quickjs.h>

JSValue js_get_smtp_email_encrypt_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smtp_login_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smtp_login_pwd(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smtp_port(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_smtp_server_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_smtp_email_encrypt_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_smtp_login_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_smtp_login_pwd(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_smtp_port(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_smtp_server_name(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_connect_info(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_mac_addr_info(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_net_802_protocol_switch(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_net_ipv4_gateway(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_net_ipv4_hostname(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_get_wired_net_ipv4_mask(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_wired_net_802_protocol_switch(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_smtp_port(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_wired_net_ipv4_addr(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_wired_net_ipv4_hostname(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_wired_net_ipv4_mode(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_set_wired_net_ipv6_switch(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);







#endif /* _PEDK_NET_SMB_ */
