/**************************************************************
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd
 * @module_name Authority Proc
 * @file_name authority_adapt.c
 * <AUTHOR> - <PERSON> (<EMAIL>)
 * @date 2024-12-12
 * @brief Authority adapt API implementation.
 **************************************************************/
#include "authority_adapt/authority_adapt.h"

/**
 * @enum AUTHORITY_PARSER_TYPE_E
 * @brief Enumerations for authority parser types.
 */
typedef enum {
    AUTHORITY_PARSER_USRNAME,      /**< User name parser */
    AUTHORITY_PARSER_USBENABLE,    /**< USB enable parser */
    AUTHORITY_PARSER_PRINT,        /**< Print capabilities parser */
    AUTHORITY_PARSER_SCAN,         /**< Scan capabilities parser */
    AUTHORITY_PARSER_COPY,         /**< Copy capabilities parser */
    AUTHORITY_PARSER_MAX,          /**< Maximum parser type */
} AUTHORITY_PARSER_TYPE_E;

/** @brief Maximum JSON key length for authority adapt module. */
#define AUTHORITY_ADAPT_JSON_KEY_LEN 22

static uint32_t m_usb_enable = 0;                               /**< USB enable status. */
static uint32_t m_is_get_remotejobs_data = 0;                   /**< Remote job data status. */
static char m_usr_name[USRNAME_STRING_SIZE] = {0};              /**< User name storage. */
static void *authority_adapt_intfs[AUTHORITY_MODULE_MAX] = {0}; /**< Interface callbacks for authority modules. */

static AUTHORITY_ADAPT_INTF_S *authority_adapt_intf_ptr[AUTHORITY_MODULE_MAX] = {0}; /**< Interface pointers for modules. */

pthread_spinlock_t spin_auth = 0;                                                    /**< Spinlock for user name operations. */
pthread_spinlock_t spin_usb_enable = 0;                                              /**< Spinlock for USB enable operations. */
pthread_spinlock_t spin_remotejobs_data = 0;                                         /**< Spinlock for remote jobs data operations. */

#define SPIN_LOCK(spin)     pthread_spin_lock(spin)
#define SPIN_UNLOCK(spin)   pthread_spin_unlock(spin)

char *m_auth_remotejobs_data =NULL;


/**
 * @brief Process authority data for a specific module type.
 * @param[in] module_type The type of the module.
 * @param[in] json_data The JSON data to process.
 * @return 0 on success, non-zero on failure.
 */
static int32_t authority_adapt_proc(AUTHORITY_MODULE_TYPE_E module_type, void *json_data)
{
    if(NULL == authority_adapt_intf_ptr[module_type] || NULL == authority_adapt_intf_ptr[module_type]->authority_proc)
    {
        pi_log_d("auth module_type_%d adapt NULL.return.\n",module_type);
        return RET_ERROR;
    }
    pi_log_d("module_type  %d.\n",module_type);
    return authority_adapt_intf_ptr[module_type]->authority_proc(module_type,json_data);
}
/**
 * @brief Push scan configuration for the scan module.
 * @param[in] cfg_type The type of the configuration.
 * @param[in] json_data The JSON data for configuration.
 * @return 0 on success, non-zero on failure.
 */
static int32_t authority_adapt_cfg_scan_push(CFG_SCAN_PUSH_TYPE_E cfg_type, void *json_data)
{
    if(NULL == authority_adapt_intf_ptr[AUTHORITY_MODULE_SCAN] || NULL == authority_adapt_intf_ptr[AUTHORITY_MODULE_SCAN]->cfg_scan_push_broom)
    {
       pi_log_d("cfg scan push NULL.return.\n");
       return RET_ERROR;
    }
    return authority_adapt_intf_ptr[AUTHORITY_MODULE_SCAN]->cfg_scan_push_broom(cfg_type,json_data);
}

/**
 * @brief Get the interface callback for a specific module type.
 * @param[in] module_type The type of the module.
 * @return The registered interface callback or NULL if not registered.
 */
static void *authority_adapt_get_intf (AUTHORITY_MODULE_TYPE_E module_type)
{
    if(module_type >= AUTHORITY_MODULE_MAX)
    {
        return NULL;
    }
    return authority_adapt_intfs[module_type];
}

/**
 * @brief Register an interface callback for a specific module type.
 * @param[in] module_type The type of the module.
 * @param[in] intf_cb The interface callback to register.
 * @return RET_OK on success, RET_ERROR on failure.
 */
int32_t authority_adapt_reg_intf(AUTHORITY_MODULE_TYPE_E module_type, void *intf_cb)
{
    authority_adapt_intfs[module_type] = intf_cb;

    authority_adapt_intf_ptr[module_type] = authority_adapt_get_intf(module_type);
    pi_log_d("authority reg_intf.\n");
    if(!spin_auth && !spin_usb_enable && !spin_remotejobs_data)
    {
        if ( pthread_spin_init(&spin_auth, 0) ||
             pthread_spin_init(&spin_usb_enable, 0) ||
             pthread_spin_init(&spin_remotejobs_data, 0))
        {
            pi_log_d("pthread_spin_init failed\n");
            return RET_ERROR;
        }
    }

    return RET_OK;
}

/**
 * @brief Retrieve the stored user name.
 * @param[out] usr_name Buffer to store the retrieved user name.
 * @param[in] usr_name_size Size of the provided buffer.
 * @return 0 on success, -1 on failure.
 */
int authority_adapt_get_usrname(char *usr_name, size_t usr_name_size)
{
    if (usr_name == NULL || usr_name_size == 0)
    {
        pi_log_d("Invalid buffer or size\n");
        return RET_ERROR;
    }

    SPIN_LOCK(&spin_auth);

    // 按最小长度进行安全拷贝，不超过 m_usr_name 的 64 字节
    size_t copy_len = (usr_name_size < USRNAME_STRING_SIZE) ? usr_name_size - 1 : USRNAME_STRING_SIZE - 1;
    strncpy(usr_name, m_usr_name, copy_len);
    usr_name[copy_len] = '\0'; // 确保以 '\0' 结尾
    pi_log_d("authority usrname:%s\n",usr_name);

    SPIN_UNLOCK(&spin_auth);

    return RET_OK; // 成功
}

/**
 * @brief Reset the stored user name to an empty string.
 */
void authority_adapt_reset_usrname (void)
{
    SPIN_LOCK(&spin_auth);
    memset(m_usr_name, 0, USRNAME_STRING_SIZE);
    pi_log_d("reset_usrname\n");
    SPIN_UNLOCK(&spin_auth);
}

/**
 * @brief Retrieve the current USB enable status.
 * @return The current USB enable status.
 */
uint32_t authority_adapt_get_usbenable (void)
{
    SPIN_LOCK(&spin_usb_enable);
    uint32_t usb_enable = m_usb_enable;
    pi_log_d("usb_enable:%u\n",usb_enable);
    SPIN_UNLOCK(&spin_usb_enable);
    return usb_enable;
}

/**
 * @brief Retrieve the stored remote jobs data.
 * @return Pointer to the remote jobs data string.
 */
char *authority_adapt_get_remotejobs_data (void)
{
    SPIN_LOCK(&spin_remotejobs_data);
    char *data = m_auth_remotejobs_data;
    SPIN_UNLOCK(&spin_remotejobs_data);

    return data;
}

/**
 * @brief Process remote jobs data and update internal storage.
 * @param[in] pData The raw JSON data of remote jobs.
 * @return RET_OK on success, RET_ERROR on failure.
 */
static int32_t authority_adapt_remote_data_proc (char *pData)
{
    cJSON *json_item, *user_name;

    json_item = cJSON_Parse(pData);
    if(json_item == NULL) {
		pi_log_d("cJSON_GetErrorPtr: %s\n", cJSON_GetErrorPtr());
        return RET_ERROR;
    }
    size_t data_size = strlen(pData);
    pi_log_d("parser data_size :%d\n",data_size);

    //free掉原有的内存空间，重新malloc，目的是malloc的空间和下发权限的大小一致，避免数据丢失
    SPIN_LOCK(&spin_remotejobs_data);
    if(m_auth_remotejobs_data != NULL)
    {
        pi_log_d("free before m_auth_remotejobs_data\n");
        free(m_auth_remotejobs_data);
        m_auth_remotejobs_data = NULL;
    }

    m_auth_remotejobs_data = (char *)malloc(data_size + 1);
    if(m_auth_remotejobs_data == NULL)
    {
        pi_log_d("malloc size %d failed,\n",data_size + 1);
        cJSON_Delete(json_item);
        json_item = NULL;
        SPIN_UNLOCK(&spin_remotejobs_data);

        return RET_ERROR;
    }
    pi_log_d("malloc m_auth_remotejobs_data\n");

    memset(m_auth_remotejobs_data,0,data_size);
    strncpy(m_auth_remotejobs_data,pData,data_size);
    pi_log_d("cp data:m_auth_remotejobs_data %s\n",m_auth_remotejobs_data);
    SPIN_UNLOCK(&spin_remotejobs_data);

    cJSON_Delete(json_item);
    json_item = NULL;

    return RET_OK;
}

/**
 * @brief Process local data and update internal authority information.
 * @param[in] pData The raw JSON data of local authority.
 * @return RET_OK on success, RET_ERROR on failure.
 */
int32_t authority_adapt_local_data_proc (char *pData)
{
    //RouterMsg   sendMsg;

    cJSON *json_item, *getitem_result;
    int ret = 0, ret_val = 0;
    const char parser_key[][AUTHORITY_ADAPT_JSON_KEY_LEN] = {
        "UserName",
        "UsbEnable",
        "PrintCapabilities",
        "ScanCapabilities",
        "CopyCapabilities",
    };

    json_item = cJSON_Parse(pData);

    if(json_item == NULL) {
        pi_log_d("cJSON_GetErrorPtr: %s\n", cJSON_GetErrorPtr());
        return RET_ERROR;
    }
    for(AUTHORITY_PARSER_TYPE_E parser_num = AUTHORITY_PARSER_USRNAME;parser_num < sizeof(parser_key) / sizeof(parser_key[0]); parser_num++)
    {
        getitem_result = cJSON_GetObjectItem(json_item, parser_key[parser_num]);
        if(getitem_result && getitem_result->valuestring) {
            size_t copy_len = strnlen(getitem_result->valuestring, USRNAME_STRING_SIZE - 1);
            switch (parser_num)
            {
                pi_log_d("parser_key[%d] :%s\n",parser_num,parser_key[parser_num]);
                case AUTHORITY_PARSER_USRNAME:
                    SPIN_LOCK(&spin_auth);
                    memset(m_usr_name, 0, sizeof(m_usr_name));
                    memcpy(m_usr_name, getitem_result->valuestring, copy_len);
                    pi_log_d("copy_len %zu, authority usrname:%s\n",copy_len, m_usr_name);
                    SPIN_UNLOCK(&spin_auth);

                    break;
                case AUTHORITY_PARSER_USBENABLE:
                    SPIN_LOCK(&spin_usb_enable);
                    m_usb_enable = (getitem_result->valueint == 1) ? getitem_result->valueint : 0;
                    pi_log_d("m_usb_enable:%u\n",m_usb_enable);
                    SPIN_UNLOCK(&spin_usb_enable);

                    break;
                case AUTHORITY_PARSER_PRINT:
                case AUTHORITY_PARSER_SCAN:
                case AUTHORITY_PARSER_COPY:
                    ret = authority_adapt_proc(parser_num - 2,getitem_result);
                    if(ret != 0) {
                        pi_log_d("%s auth proc fail.\n",parser_key[parser_num]);
                        ret_val |= ret;
                    }
                    break;

                default:
                    printf("invalid case value,break;\n");
                    break;
            }
        }
        else
        {
            pi_log_d("get %s failed\n",parser_key[parser_num]);
            ret_val |= RET_ERROR;
        }
    }

#if 0 //TODO msg发送
    sendMsg.msgType = MSG_SDK_NET_UI_RS;
    sendMsg.msg1 = 6;
    sendMsg.msg2 = 0;//(0成功，1失败)
    sendMsg.msg3 = NULL;
    sendMsg.msgSender = MID_PANEL;
    int status = TaskMsgSendByRouter(MID_SDK_NET, &sendMsg );
    if ( 0 > status)
    {
        pi_log_d(" send MSG_SDK_NET_UI_RS Fail 0  %d\r\n", status);
        ret_val |= -1;
    }

    //sendMsg 通知面板权限下发处理结果
    sendMsg.msgType = MSG_SDK_AUTHORITY_PANEL;
    sendMsg.msg1 = ret;//(0成功，-1失败)
    sendMsg.msg2 = 0;
    sendMsg.msg3 = NULL;
    sendMsg.msgSender = MID_AUTHENTICATION_LICENSE;
    status = TaskMsgSendByRouter(MID_PANEL, &sendMsg );
    if ( 0 > status)
    {
        pi_log_d(" send MSG_SDK_AUTHORITY_PANEL Fail 0  %d\r\n", status);
        ret_val |= -1;
    }
#endif
    return ret_val;
}

/**
 * @brief Receive and process authority data.
 * @param[in] pData The raw JSON authority data.
 * @return RET_OK on success, RET_ERROR on failure.
 */
int32_t authority_adapt_recv_data_proc (char *pData)
{
    cJSON *json_item, *usr_name;
    int ret_val = 0;

    json_item = cJSON_Parse(pData);
    if(json_item == NULL) {
        pi_log_d("cJSON_GetErrorPtr: %s\n", cJSON_GetErrorPtr());
        return RET_ERROR;
    }

    usr_name = cJSON_GetObjectItem(json_item, "UserName");
    if(!usr_name)
    {
        pi_log_d("get cjsonobj UserName failed\n");
        cJSON_Delete(json_item);
        json_item = NULL;
        return RET_ERROR;
    }

    pi_log_d("usr_name is %s\n",usr_name->valuestring);
    if(!strncmp(usr_name->valuestring,m_usr_name,strlen(usr_name->valuestring)))
    {
        pi_log_d("local authority data\n");
        ret_val = authority_adapt_local_data_proc(pData);
    }
    else
    {
        pi_log_d("remotejobs authority data\n");
        ret_val= authority_adapt_remote_data_proc(pData);
    }

    cJSON_Delete(json_item);
    json_item = NULL;

    return ret_val;
}
