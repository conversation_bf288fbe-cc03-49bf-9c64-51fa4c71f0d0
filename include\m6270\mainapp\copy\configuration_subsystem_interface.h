/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       configuration_subsytem_interface.h
 * @addtogroup copy system
 * @{
 * @addtogroup configuration subsystem
 * <AUTHOR>
 * @date       2023-05-31
 * @version    v1.0
 * @details    interface for the configuration subsystem
 */
#ifndef __CONFIGURATION_SUBSYSTEM_INTERFACE_H__
#define __CONFIGURATION_SUBSYSTEM_INTERFACE_H__
#include "public_data_proc.h"


/**
 * @brief ipunit module
 *
 */
typedef int (*CNFG_IPU_ACT_FUNC)();
typedef void*    IPU_HANDLER_P;

 /**
 * @brief the attribute instance
 *
 */
typedef struct
{
    char                                     *name;                ///< attr name
    char                                     *default_value;     ///<
    CNFG_IPU_ACT_FUNC                          action;               ///<
    char                                    *description;       ///< description for this attribute
}CNFG_IPU_ATTR_S;

/**
 * @brief order number
 *
 */
typedef enum
{
    CNFG_IPU_ORDER_1 = 0,
    CNFG_IPU_ORDER_2,
    CNFG_IPU_ORDER_3,
    CNFG_IPU_ORDER_4,
    CNFG_IPU_ORDER_5,
    CNFG_IPU_ORDER_6,
    CNFG_IPU_ORDER_7,
    CNFG_IPU_ORDER_8,
    CNFG_IPU_ORDER_9,
    CNFG_IPU_ORDER_10,
    CNFG_IPU_ORDER_11,
    CNFG_IPU_ORDER_12,
    CNFG_IPU_ORDER_13,
    CNFG_IPU_ORDER_14,
    CNFG_IPU_ORDER_15,
    CNFG_IPU_ORDER_16,
    CNFG_IPU_ORDER_17,
    CNFG_IPU_ORDER_18,
    CNFG_IPU_ORDER_19,
    CNFG_IPU_ORDER_20,
    CNFG_IPU_ORDER_21,
    CNFG_IPU_ORDER_22,
    CNFG_IPU_ORDER_23,
    CNFG_IPU_ORDER_24,
    CNFG_IPU_ORDER_25,

    CNFG_IPU_ORDER_MAX = 0xffffffff,
}CNFG_ORDER_TYPE_E;

/**
 * @brief pattern
 *
 */
typedef enum
{
    CNFG_PATTERN_BEFORE = 1,
    CNFG_PATTERN_AFTER  = 2,

    CNFG_PATTERN_MAX = 0xffffffff,
}CNFG_PATTERN_TYPE_E;

/**
* @brief register the new ipunit
* @param[in] order for the ipunit
* @param[in] name for ipunit
* @param[in] a set of attribute
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_register_ipu(int order,char *name,CNFG_IPU_ATTR_S *attribute_list);

/**
* @brief get the ipunit instance
* @param[in] name for the ipunit
* @return the ipunit instance
* @autor liushaoxi
* @date 2023-05-31
*/
IPU_HANDLER_P config_get_ipu(char *name);

/**
* @brief set the value of the attribute
* @param[in] the attribute handler
* @param[in] group for the value instance
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_set_attr_for_ipu(IPU_HANDLER_P handler,int group,char *attr_name,char *value);


/**
* @brief make description for the pipeline
* @param[in] group for the value instance
* @param[in] buffer for description
* @param[in] buffer length
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_make_desc(int group,char *description_buffer,int buffer_length);

/**
* @brief reset all ipunits instance
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_reset_all_ipu(void);

/**
* @brief adjust the position for the ipunit
* @param[in]  name for the current_ipu
* @param[in]  name for the dest_ipu
* @param[in] pattern   1 for before  2 for after
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_adjust_ipu_position(char *current_ipu,char *dest_ipu,CNFG_PATTERN_TYPE_E pattern);


/**
 * @brief config module
 *
 */
typedef int (*CNFG_CONFIG_ACT_FUNC)(void *);
typedef int (*CNFG_CONFIG_ATTR_ACT_FUNC)(void *);
typedef void*    CNFG_CONFIG_HANDLER_P;
typedef void*    CNFG_CONFIG_ATTR_HANDLER_P;

#define config_add_attr_for_item_no_act(item_name,attr_name,attr_type,attr_desc) \
        config_add_attr_for_item_no_def(item_name,attr_name,attr_type,attr_desc,NULL);

#define config_add_attr_for_item_with_act(item_name,attr_name,attr_type,attr_desc,action) \
        config_add_attr_for_item_no_def(item_name,attr_name,attr_type,attr_desc,action);

#define config_add_attr_for_item_no_def(item_name,attr_name,attr_type,attr_desc,attr_action) \
        config_add_attr_for_item(item_name,attr_name,attr_type,attr_desc,attr_action,NULL);

#define config_add_attr_for_item_with_act_def(item_name,attr_name,attr_type,attr_desc,attr_action,default_value) \
                config_add_attr_for_item(item_name,attr_name,attr_type,attr_desc,attr_action,default_value);


/**
 * @brief the attribute type
 *
 */
typedef enum
{
    CONFIGURATION_ATTRIBUTE_TYPE_UNDEFINE,
    CONFIGURATION_ATTRIBUTE_TYPE_ENUM,
    CONFIGURATION_ATTRIBUTE_TYPE_INT,
    CONFIGURATION_ATTRIBUTE_TYPE_CHAR,
    CONFIGURATION_ATTRIBUTE_TYPE_LONG,
    CONFIGURATION_ATTRIBUTE_TYPE_FLOAT,
    CONFIGURATION_ATTRIBUTE_TYPE_DOUBLE,
    CONFIGURATION_ATTRIBUTE_TYPE_STRING,
    CONFIGURATION_ATTRIBUTE_TYPE_POINTER,
    CONFIGURATION_ATTRIBUTE_TYPE_MAX,
}CNFG_CONFIG_ATTR_TYPE_E;


/**
 * @brief mode for action
 *
 */
typedef enum
{
    CNFG_MODE_INCHING,
    CNFG_MODE_LINKAGE,

    CNFG_MODE_MAX = 0xffffffff,
}CNFG_ACTION_MODE;

#define config_register_item(name,action) \
                        config_register_item_extension(name,action,CNFG_MODE_INCHING);

#define config_register_item_with_linkage(name,action) \
                        config_register_item_extension(name,action,CNFG_MODE_LINKAGE);

#ifdef CONFIG_COPY
/**
* @brief register the new config item
* @param[in] name for the config item
* @param[in] action for the config item
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_register_item_extension(char *name,CNFG_CONFIG_ACT_FUNC action,CNFG_ACTION_MODE mode);

/**
* @brief add the attribute to the config item
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] type for the attribute
* @param[in] description for the attribute
* @param[in] action for the attribute
* @param[in] default value for the attribute
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_add_attr_for_item(char *item_name,char *attr_name,CNFG_CONFIG_ATTR_TYPE_E attr_type,char *attr_desc,CNFG_CONFIG_ATTR_ACT_FUNC attr_action,void *default_value);

/**
* @brief set the value for the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the char value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_set_attr_c_value_for_item(char *item_name,char *attr_name,char value);

/**
* @brief set the value for the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the int value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_set_attr_i_value_for_item(char *item_name,char *attr_name,int value);

/**
* @brief set the value for the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_set_attr_s_value_for_item(char *item_name,char *attr_name,char *value);

/**
* @brief set the value for the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_set_attr_p_value_for_item(char *item_name,char *attr_name,void *value_pointer);

/**
* @brief get the value of the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_get_attr_c_value_for_item(char *item_name,char *attr_name,char *value);

/**
* @brief get the value of the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_get_attr_i_value_for_item(char *item_name,char *attr_name,int *value);

/**
* @brief get the value of the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_get_attr_s_value_for_item(char *item_name,char *attr_name,char *value);

/**
* @brief get the value of the attribute
* @param[in] name for the config item
* @param[in] name for the attribute
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_get_attr_p_value_for_item(char *item_name,char *attr_name,void **value_pointer);

/**
* @brief get the attribute value of the environment
* @param[in] the attribute environment
* @param[in] the pointer of value
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_get_attr_env(void *environment,void *value);

/**
* @brief register the dependency for the node
* @param[in] name for node
* @param[in] name for dependency
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_register_depend(char *my_name,char *dependency_name);

/**
* @brief reset all configs
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_reset_all_config(void);


/**
* @brief get version of interface
* @return the pointer of version
* @autor liushaoxi
* @date 2023-05-31
*/
char *config_get_intf_version(void);

/**
* @brief initialize the configuration subsystem
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-31
*/
int config_subsystem_init(void);


#if 1 //for tmp use
/**
* @brief get the width of paper
* @param[in] type of paper_size
* @return the width of paper
* @autor liushaoxi
* @date 2023-05-31
*/
int copy_get_paper_width(PAPER_SIZE_E paper_size);

/**
* @brief get the height of paper
* @param[in] type of paper_size
* @return the height of paper
* @autor liushaoxi
* @date 2023-05-31
*/
int copy_get_paper_height(PAPER_SIZE_E paper_size);

/**
* @brief translate int to string
* @param[in] the value of int
* @return the value of string
* @autor liushaoxi
* @date 2023-05-31
*/
char *copy_int_to_str(int int_value ,char *str);
#endif

#else
static inline int config_register_item_extension(char *name,CNFG_CONFIG_ACT_FUNC action,CNFG_ACTION_MODE mode){return 0;};

static inline int config_add_attr_for_item(char *item_name,char *attr_name,CNFG_CONFIG_ATTR_TYPE_E attr_type,char *attr_desc,CNFG_CONFIG_ATTR_ACT_FUNC attr_action,void *default_value){return 0;};

static inline int config_set_attr_c_value_for_item(char *item_name,char *attr_name,char value){return 0;};

static inline int config_set_attr_i_value_for_item(char *item_name,char *attr_name,int value){return 0;};

static inline int config_set_attr_s_value_for_item(char *item_name,char *attr_name,char *value){return 0;};

static inline int config_set_attr_p_value_for_item(char *item_name,char *attr_name,void *value_pointer){return 0;};

static inline int config_get_attr_c_value_for_item(char *item_name,char *attr_name,char *value){return 0;};

static inline int config_get_attr_i_value_for_item(char *item_name,char *attr_name,int *value){return 0;};

static inline int config_get_attr_s_value_for_item(char *item_name,char *attr_name,char *value){return 0;};

static inline int config_get_attr_p_value_for_item(char *item_name,char *attr_name,void **value_pointer){return 0;};

static inline int config_get_attr_env(void *environment,void *value){return 0;};

static inline int config_register_depend(char *my_name,char *dependency_name){return 0;};

static inline int config_reset_all_config(){return 0;};

static inline char *config_get_intf_version(void){return NULL;};

static inline int config_subsystem_init(void){return 0;};

#endif

#endif
/**
 *@}
 */
