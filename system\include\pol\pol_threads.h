/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_threads.h
 * @addtogroup threads
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal threads and interaction set
 */

#ifndef POL_THREADS_H
#define POL_THREADS_H

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_time.h"

PT_BEGIN_DECLS

// Mutex handle
typedef void* PI_MUTEX_T;

// Semaphore handle
typedef void* PI_SEMAPHORE_T;

// Mailbox handle
typedef void* PI_MAILBOX_T;

// Thread handle
typedef void* PI_THREAD_T;

// These defines are to to enable code to be compiled for other systems
// that might not use ptr/handles for primitives to port easier
#define INVALIDMTX      (PI_MUTEX_T)NULL
#define INVALIDSEM      (PI_SEMAPHORE_T)NULL
#define INVALIDTHREAD   (PI_THREAD_T)NULL
#define INVALIDMSGQ     (PI_MAILBOX_T)NULL

// MSG_S - holds a mail message
typedef struct msg
{
    uint32_t    msg1;           // First parameter, usually message code
    uint32_t    msg2;           // Second parameter
    uint32_t    msg3;           // Third parameter
    union
    {
        uint64_t    ullval;     // Fourth parameter, can by up to 64 bits
        void*       ptrval;     // or, a pointer parameter if needed
    }
    msg4;
}
MSG_S;

typedef struct _TimeVal
{
    int32_t secs;
    int32_t usecs;
}
TIMEVAL_S;

// Inferno provides 32 priorities, with 31 being the highest
// and 0 being the lowest.  These are translated to whatever
// the underlying OS uses in port specific files.  Note that
// some implementations (like Linux) will not even look at
// the priority passed in, so don't assume that your priorities
// will be exactly honored or depend on them.
#define PI_LOWEST_PRIORITY      0
#define PI_LOW_PRIORITY         7
#define PI_LEVEL_PRIORITY       15
#define PI_MEDIUM_PRIORITY      15
#define PI_HIGHEST_PRIORITY     31

/* Task Stack sizes */
#define PI_STACK_MULT           1

#define PI_HUGE_STACK           (32768 * PI_STACK_MULT)
#define PI_LARGE_STACK          (16384 * PI_STACK_MULT)
#define PI_NORMAL_STACK         (8192 * PI_STACK_MULT)
#define PI_SMALL_STACK          (4096 * PI_STACK_MULT)


/* Define the number of Queue Elements to support. */
#define PI_NUM_MORE_QELEMENTS   (2560)
#define PI_NUM_QELEMENTS        (256)

const char*     pi_runcmd                   (char* buf, size_t nbuf, int32_t background, const char* format, ...);

// On-time initialization of thread system.
// @returns non-0 for errors
int32_t	        pi_threads_prolog           (void);

// On-time uninitialization of thread system.
// @returns non-0 for errors
int32_t         pi_threads_epilog           (void);

// Create a thread of exectution.
// @returns the id of the created task or INVALIDTHREAD on error
PI_THREAD_T     pi_thread_create            (void *(*entry)(void *), uint32_t stacksize, void* stack, uint32_t priority, void* arg, const char* name);

// Stop and destroy a running thread.
// @returns non-0 on error (no such task)
int32_t         pi_thread_destroy           (PI_THREAD_T thread);

// The child thread destroy itself before the end.
int32_t         pi_thread_destroy_self      (void);

PI_THREAD_T     pi_thread_self              (void);

/**
 * @brief Creates a mutex object for synchronization.
 * @return Returns Pointer to a `pi_mutex_t` object representing the mutex.
 */
PI_MUTEX_T      pi_mutex_create_ent         (const char* caller);
#define         pi_mutex_create()           pi_mutex_create_ent(__func__)

/**
 * @brief Destroys a previously created mutex object.
 * @param[in] mtx Pointer to the `pi_mutex_t` object representing the mutex.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_mutex_destroy_ent        (PI_MUTEX_T mtx, const char* caller);
#define         pi_mutex_destroy(m)         pi_mutex_destroy_ent(m, __func__)

/**
 * @brief Locks a mutex, blocking if it is already locked by another thread.
 * @param[in] mtx Pointer to the `pi_mutex_t` object representing the mutex.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_mutex_lock_ent           (PI_MUTEX_T mtx, const char* caller);
#define         pi_mutex_lock(m)            pi_mutex_lock_ent(m, __func__)

/**
 * @brief Locks a mutex within a specified timeout period.
 * @param[in] mtx Pointer to the `pi_mutex_t` object representing the mutex.
 * @param[in] secs secs Number of milliseconds to locked.
 * @param[in] usecs usecs Number of microseconds to locked.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_mutex_timedlock_ent      (PI_MUTEX_T mtx, int32_t secs, int32_t usecs, const char* caller);
#define         pi_mutex_timedlock(m, s, u) pi_mutex_timedlock_ent(m, s, u, __func__)

/**
 * @brief Unlocks a previously locked mutex.
 * @param[in] mtx Pointer to the `pi_mutex_t` object representing the mutex.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_mutex_unlock_ent         (PI_MUTEX_T mtx, const char* caller);
#define         pi_mutex_unlock(m)          pi_mutex_unlock_ent(m, __func__)

/**
 * @brief Creates a semaphore object for synchronization.
 * @param[in] count Initial value of the semaphore.
 * @return Returns Pointer to a `pi_sem_t` object representing the semaphore.
 */
PI_SEMAPHORE_T  pi_sem_create_ent           (uint32_t count, const char* caller);
#define         pi_sem_create(n)            pi_sem_create_ent(n, __func__)

/**
 * @brief Destroys a previously created semaphore object.
 * @param[in] sem Pointer to the `pi_sem_t` object representing the semaphore.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_sem_destroy_ent          (PI_SEMAPHORE_T sem, const char* caller);
#define         pi_sem_destroy(m)           pi_sem_destroy_ent(m, __func__)

/**
 * @brief Waits until a semaphore is available and then decrements its value.
 * @param[in] sem Pointer to the `pi_sem_t` object representing the semaphore.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_sem_wait_ent             (PI_SEMAPHORE_T sem, const char* caller);
#define         pi_sem_wait(m)              pi_sem_wait_ent(m, __func__)

/**
 * @brief Waits until a semaphore is available within a specified timeout period.
 * @param[in] sem Pointer to the `pi_sem_t` object representing the semaphore.
 * @param[in] secs secs Number of milliseconds to wait.
 * @param[in] usecs usecs Number of microseconds to wait.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_sem_timedwait_ent        (PI_SEMAPHORE_T sem, int32_t secs, int32_t usecs, const char* caller);
#define         pi_sem_timedwait(m, s, u)   pi_sem_timedwait_ent(m, s, u, __func__)

/**
 * @brief Increments the value of a semaphore, releasing a waiting thread if necessary.
 * @param[in] sem Pointer to the `pi_sem_t` object representing the semaphore.
 * @return Returns 0 on success, or an error code on failure.
 */
int32_t         pi_sem_post_ent             (PI_SEMAPHORE_T sem, const char* caller);
#define         pi_sem_post(m)              pi_sem_post_ent(m, __func__)

/**
 * @brief Mailbox (message queue) functions.  These are used to send
 * messages (commands, information, wakeup events, etc.) from
 * one thread (task) to another.

 * Inferno does not (and should not) have an "event" primative
 * and message queues are used to signal events as well as send
 * information messages.  To send an event to multiple recipients
 * a message could be sent to each recipient's message box.
 * Create a message queue

 * @param[in] msg_cnt number of msg created,custom the max message count of this message queue.
 * @return returns the queue object handle, or NULL on failure (alloction)
 */
PI_MAILBOX_T    pi_msgq_create_ent          (int32_t msg_cnt, const char* caller);
#define         pi_msgq_create_custom(n)    pi_msgq_create_ent(n, __func__)
#define         pi_create_more_msg_queue()  pi_msgq_create_ent(10 * PI_NUM_QELEMENTS, __func__)
#define         pi_msgq_create()            pi_msgq_create_ent(PI_NUM_QELEMENTS, __func__)

/**
 * @brief Destroys a previously created message queue object.
 * @param[in] msgq Pointer to the `PI_MAILBOX_T` object representing.
 * @return Returns non-0 on success, or an error code on failure.
 */
int32_t         pi_msgq_destroy_ent         (PI_MAILBOX_T msgq, const char* caller);
#define         pi_msgq_destroy(q)          pi_msgq_destroy_ent(q, __func__)

/**
 * @brief Wait for a message to appear in a message queue.  Timeout after
 * waiting secs seconds and usecs microseconds.  If no timeout is
 * specified, the function return immediately
 * Use -1 for secs/usecs to wait forever however the concept of forever
 * is not friendly to users who usually have better things to do.
 * If you have a thread that does nothing but respond to mail messages
 * then put a long timeout in, and do nothing in the timeout return case
 * @param[in] msgq struct of the `PI_MAILBOX_T` object representing.
 * @param[in] msg Pointer to the `MSG_S` object representing.
 * @param[in] secs secs Number of milliseconds to recv.
 * @param[in] usecs usecs Number of microseconds to recv.
 * @retval < 0 on error (no such queue, queue destroyed, etc.)
 * @retval > 0 on timeout
 * @retval 0 there was a message
 */
int32_t         pi_msg_recv_ent             (PI_MAILBOX_T msgq, MSG_S* msg, int32_t secs, int32_t usecs, const char* caller);
#define         pi_msg_recv(q, m, s, u)     pi_msg_recv_ent(q, m, s, u, __func__)

/**
 * @brief Send a message to a message queue
 * @param[in] msgq struct of the `PI_MAILBOX_T` object representing.
 * @param[in] msg Pointer to the `MSG_S` object representing.
 * @return Returns 0 on success, or an error code on failure.
 * @retval -1 on error (no such queue)
 * @retval -2 on overflow
 * @retval 0 success
 */
int32_t         pi_msg_send_ent             (PI_MAILBOX_T msgq, MSG_S* msg, const char* caller);
#define         pi_msg_send(q, m)           pi_msg_send_ent(q, m, __func__)

/**
 * @brief Gets the number of messages currently in a message queue.
 * @param[in] msgq msgq struct of the `PI_MAILBOX_T` object representing.
 * @return Returns the count of messages in the message queue or < 0 on error (no such queue)
 */
int32_t         pi_msg_count_get_ent        (PI_MAILBOX_T msgq, const char* caller);
#define         pi_msg_count_get(q)         pi_msg_count_get_ent(q, __func__)

/**
 * @brief Gets the size of the largest message that can be sent to a message queue.
 * @param[in] msgq msgq struct of the `PI_MAILBOX_T` object representing.
 * @return Returns the max size of the message queue or < 0 on error (no such queue)
 */
int32_t         pi_msgq_size_get_ent        (PI_MAILBOX_T msgq, const char* caller);
#define         pi_msgq_size_get(q)         pi_msgq_size_get_ent(q, __func__)

PT_END_DECLS

#endif /* POL_THREADS_H */

/**
 *@}
 */
