#include "PEDK_net_ssl.h"
#include "PEDK_event.h"

#include <quickjs.h>

typedef JSValue (*PJS_CONSTRUCTOR)(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);
typedef struct
{
    JSClassID *class_id;
    JSClassDef *pclass;
    const JSCFunctionListEntry *pesf_funs;
    int fun_len;
    PJS_CONSTRUCTOR ctor;
    int ctor_parm_num;
    const char* name;
}JSCreatClassParam;

// 证书安装结构体
typedef struct {
    char cert_content[4096];
    char key_content[4096];
    char key_password[256];
    int export_flag;
} CertInstallInfo;

// 定义证书和私钥文件的保存路径
#define CERT_FILE_PATH "/pesf_data/custom_cert.pem"
#define KEY_FILE_PATH  "/pesf_data/custom_key.pem"

static const JSCFunctionListEntry pesf_CertInfo_funcs[] = {};

static JSClassID js_ssllibrary_class_id;
static JSClassID js_sslconnect_class_id;
static JSClassID js_CertInfo_class_id;

#define countof(x) (sizeof(x) / sizeof((x)[0]))

static JSValue JS_ExtendClass(JSContext *ctx, JSValueConst this_val, void *obj_data, JSClassID classid)
{
    JSValue obj = JS_UNDEFINED;
    JSValue proto;

    /* using this_val to get the prototype is necessary when the class is extended. */
    proto = JS_GetPropertyStr(ctx, this_val, "prototype");
    if (JS_IsException(proto))
    {
        js_free(ctx, obj_data);
        JS_FreeValue(ctx, obj);
        return JS_UNDEFINED;
    }

    obj = JS_NewObjectProtoClass(ctx, proto, classid);
    JS_FreeValue(ctx, proto);
    if (JS_IsException(obj))
    {
        js_free(ctx, obj_data);
        JS_FreeValue(ctx, obj);
        return JS_UNDEFINED;
    }

    JS_SetOpaque(obj, obj_data);
    return obj;
}

static const JSCFunctionListEntry pesf_ssllibrary_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("generateCertificate", 1, js_generateCertificate),
    JS_CFUNC_DEF("loadSslCertificate", 3, js_loadSslCertificate),
};

static const JSCFunctionListEntry pesf_sslconnect_funcs[] = {
    /* JS_CFUNC_DEF(函数入口名称，入参个数，QuickJS C 函数) */
    JS_CFUNC_DEF("createSslConnection", 5, js_createSslConnection),
    JS_CFUNC_DEF("sslSend", 1, js_SslSend),
    JS_CFUNC_DEF("sslRecv", 1, sslonrecv),
    JS_CFUNC_DEF("sslClose", 0, js_SslClose),
};


static void js_ssllibrary_finalizer(JSRuntime *rt, JSValue val)
{
    CERTINFO *info = JS_GetOpaque(val, js_ssllibrary_class_id);

    js_free_rt(rt, info);
}

static JSClassDef js_ssllibrary_class =
{
    "SSLLibrary",
     .finalizer = js_ssllibrary_finalizer,
};

static void js_sslconnect_finalizer(JSRuntime *rt, JSValue val)
{
    CERTINFO *info = JS_GetOpaque(val, js_sslconnect_class_id);

    js_free_rt(rt, info);
}

static JSClassDef js_sslconnect_class =
{
    "SslConnect",
     .finalizer = js_sslconnect_finalizer,
};

static void js_CertInfo_finalizer(JSRuntime *rt, JSValue val)
{
    CERTINFO *info = JS_GetOpaque(val, js_CertInfo_class_id);

    js_free_rt(rt, info);
}

static JSClassDef js_CertInfo_class =
{
    "CertInfo",
    .finalizer = js_CertInfo_finalizer,
};


/* 自定义原生C函数 */
static int send_generateCertificate(int type, int length, int day, const char *psd, const char *c_path, const char *k_path, const char *pub_kpath)
{
    printf("[send_generateCertificate]\n");
    //int ret = 0;
    //int recvsize = 0;
    //int ret_v = 0;

    CERTINFO info;

    info.type = type;
    info.key_length = length;
    info.days = day;
    strcpy(info.password, psd);
    strcpy(info.cert_path, c_path);
    strcpy(info.key_path, k_path);
    strcpy(info.pub_key_path, pub_kpath);
    printf("[type = %d; length = %d;day = %d; psd = %s; c_path = %s; k_path = %s; pub_kpath = %s]\n", info.type, info.key_length, info.days, info.password, info.cert_path, info.key_path, info.pub_key_path);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GEN_CERT,0, sizeof(CERTINFO)+1, (char *)&info);
    //ret_v = RecvMsgToMfp(MSG_MOUDLE_NET_SSL,MSG_SSL_GENER,&ret,NULL,&recvsize,20);
    //printf("[retval]ret = %d ; ret_v = %d\n",ret,ret_v);

    return 0;
}

static int send_loadSslCertificate(const char *key, const char *cert, const char *pass)
{
    int ret = 0;
    //int recvsize = 0;
    //int ret_v = 0;

    LOADINFO param;
    strcpy(param.keypath, key);
    strcpy(param.certpath, cert);
    strcpy(param.password, pass);
    printf("[type = %s; length = %s; psd = %s]\n",param.keypath, param.certpath, param.password);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_LOAD_CERT, 0, sizeof(LOADINFO)+1, (char *)&param);
    //ret_v = RecvMsgToMfp(MSG_MOUDLE_NET_SSL,MSG_SSL_GENER,&ret,NULL,&recvsize);
    //printf("[retval]ret = %d ; ret_v = %d\n",ret,ret_v);

    return ret;
}

static void send_Createssl(const char *hostname, int port, const char *kpath, const char *cpath, const char *psd)
{
    SSLCONINFO para;
    strcpy(para.hostname, hostname);
    strcpy(para.keypath, kpath);
    strcpy(para.certpath, cpath);
    strcpy(para.password, psd);
    para.port = port;

    printf("[hname = %s; port = %d, kpath = %s; cpath = %s; psd = %s;]\n",para.hostname, para.port, para.keypath, para.certpath, para.password);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_CREATE_CONN, 0, sizeof(SSLCONINFO)+1, (char *)&para);
}


/*
    定义 QuickJS C 函数
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/

static JSValue js_SSLLibrary_ctor(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    CERTINFO *info;

    info = js_malloc(ctx, sizeof(*info));
    if(!info)
    {
        return JS_EXCEPTION;
    }

    memset(info, 0, sizeof(*info));

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
    return JS_ExtendClass(ctx, this_val, info, js_ssllibrary_class_id);
}

int ret = 0;

static JSValue js_SslConnect_ctor(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    sslflag *ssl_flag;

    ssl_flag = js_malloc(ctx, sizeof(*ssl_flag));
    if(!ssl_flag)
    {
        return JS_EXCEPTION;
    }

    memset(ssl_flag, 0, sizeof(*ssl_flag));
    ssl_flag->num = ret;
    printf("p = %p ;ssl_num = %d\n",ssl_flag,ssl_flag->num);
    ret++;
    printf("ret = %d\n",ret);

    if(argc != 0)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
    return JS_ExtendClass(ctx, this_val, ssl_flag, js_sslconnect_class_id);
}

static JSValue js_CertInfo_ctor(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    CERTINFO *info;

    info = js_malloc(ctx, sizeof(*info));
    if(!info)
    {
        return JS_EXCEPTION;
    }

    memset(info, 0, sizeof(*info));
    if(argc != 7)
    {
        return JS_ThrowTypeError(ctx, "No input parameters are required");
    }
    if(!JS_IsNumber(argv[0]) || !JS_IsNumber(argv[1]) || !JS_IsNumber(argv[2]))
    {
        return JS_ThrowTypeError(ctx, " int parmeter error");
    }
    if(JS_ToInt32(ctx, &info->type, argv[0]))
    {
        return JS_ThrowTypeError(ctx, "JS_ToInt32 failed");
    }
    if(JS_ToInt32(ctx, &info->key_length, argv[1]))
    {
        return JS_ThrowTypeError(ctx, "JS_ToInt32 failed");
    }
    if(JS_ToInt32(ctx, &info->days, argv[2]))
    {
        return JS_ThrowTypeError(ctx, "JS_ToInt32 failed");
    }
    if (!JS_IsString(argv[3]))
    {
        return JS_ThrowTypeError(ctx, "string parmeter4 error");
    }
    if (!JS_IsString(argv[4]))
    {
        return JS_ThrowTypeError(ctx, "string parmeter5 error");
    }
    if (!JS_IsString(argv[5]))
    {
        return JS_ThrowTypeError(ctx, "string parmeter6 error");
    }
    if (!JS_IsString(argv[6]))
    {
        return JS_ThrowTypeError(ctx, "string parmeter7 error");
    }
    const char *psd= JS_ToCString(ctx, argv[3]);
    const char *pub_kpath= JS_ToCString(ctx, argv[4]);
    const char *k_path= JS_ToCString(ctx, argv[5]);
    const char *c_path= JS_ToCString(ctx, argv[6]);
    strcpy(info->password,psd);
    strcpy(info->cert_path,c_path);
    strcpy(info->key_path,k_path);
    strcpy(info->pub_key_path,pub_kpath);

    JS_FreeCString(ctx, psd);
    JS_FreeCString(ctx, pub_kpath);
    JS_FreeCString(ctx, k_path);
    JS_FreeCString(ctx, c_path);
    return JS_ExtendClass(ctx, this_val, info, js_CertInfo_class_id);
}


//生成证书
JSValue js_generateCertificate(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    int ret = 0;
    int recvsize = 0;
    int ret_v = 0;
    unsigned char *ret_s= NULL;
    ret_s = (unsigned char *)malloc(RET_MAX+1);
    int bufsize = RET_MAX;
    int res = 0;

    JSValue certinfo = argv[0];
    CERTINFO *info = JS_GetOpaque(certinfo, js_CertInfo_class_id);
    if(!info)
    {
        printf("[SSL error]JS_GetOpaque2 failed\n");
        free(ret_s);
        return JS_EXCEPTION;
    }

    printf("argc = %d\n",argc);
    printf("cert_type: %d; psd: %s\n",info->type,info->password);

    send_generateCertificate(info->type,info->key_length,info->days,info->password,info->cert_path,info->key_path,info->pub_key_path);

    ret_v = RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_GEN_CERT, &ret, ret_s, &bufsize, 40);
    printf("[retval]ret_v = %d\n",ret_v);
    printf("[js_genreate]ret:%s| len[%d]| size[%d]\n",ret_s,strlen(ret_s),sizeof(ret_s));

    return JS_NewString(ctx, ret_s);

}

JSValue js_loadSslCertificate(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    int ret = 0;
    int recvsize = 0;
    int ret_v = 0;
    unsigned char *ret_s= NULL;
    ret_s = (unsigned char *)malloc(RET_MAX+1);
    int bufsize = RET_MAX;
    int res = 0;

    if (!JS_IsString(argv[0]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "string parmeter[key] error");
    }
    if (!JS_IsString(argv[1]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "string parmeter[cert] error");
    }
    if (!JS_IsString(argv[2]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "string parmeter[pass] error");
    }
    const char *key = JS_ToCString(ctx, argv[0]);
    const char *cert = JS_ToCString(ctx, argv[1]);
    const char *pass = JS_ToCString(ctx, argv[2]);
    send_loadSslCertificate(key, cert, pass);

    JS_FreeCString(ctx, key);
    JS_FreeCString(ctx, cert);
    JS_FreeCString(ctx, pass);

    ret_v = RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_LOAD_CERT, &ret, ret_s, &bufsize, 5);
    printf("[retval]ret_v = %d\n",ret_v);
    printf("[js_load]ret:%s\n",ret_s);

    return JS_NewString(ctx, ret_s);
}

JSValue js_createSslConnection(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    int ret = 0;
    int ret_v = 0;
    int port;
    unsigned char *ret_s= NULL;
    ret_s = (unsigned char *)malloc(RET_MAX+1);
    int bufsize = RET_MAX;

    sslflag *get_object = JS_GetOpaque(this_val,js_sslconnect_class_id);
    printf("flag = %d\n",get_object->num);
    //printf("ret = %d\n",ret);

    if (!JS_IsString(argv[0]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "parmeter[hostname] error");
    }
    if(!JS_IsNumber(argv[1]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, " int parmeter error");
    }
    if(JS_ToInt32(ctx, &port, argv[1]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "JS_ToInt32 failed");
    }
    if (!JS_IsString(argv[2]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "parmeter[kpath] error");
    }
    if (!JS_IsString(argv[3]))
    {
       free(ret_s);
        return JS_ThrowTypeError(ctx, "parmeter[cpath] error");
    }
    if (!JS_IsString(argv[4]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "parmeter[psd] error");
    }

    const char *hname = JS_ToCString(ctx, argv[0]);
    const char *kpath = JS_ToCString(ctx, argv[2]);
    const char *cpath = JS_ToCString(ctx, argv[3]);
    const char *psd = JS_ToCString(ctx, argv[4]);
    printf("hname :%s|port :%d|kapth :%s|cpath :%s|psd :%s\n", hname, port, kpath, cpath, psd);
    //send to mfp create ssl connection
    send_Createssl(hname, port, kpath, cpath, psd);

    //SetOpaque port
    sslflag *sslconnect_falg;
    sslconnect_falg = js_malloc(ctx, sizeof(*sslconnect_falg));
    memset(sslconnect_falg, 0, sizeof(*sslconnect_falg));
    sslconnect_falg->sslcon_port = port;
    sslconnect_falg->num = get_object->num;
    strcpy(sslconnect_falg->hostname,hname);
    JS_SetOpaque(this_val,sslconnect_falg);
    printf("***[end JS_SetOpaque]port = %d, hostname = %s, num = %d***\n",sslconnect_falg->sslcon_port,sslconnect_falg->hostname,sslconnect_falg->num);

    JS_FreeCString(ctx, hname);
    JS_FreeCString(ctx, kpath);
    JS_FreeCString(ctx, cpath);
    JS_FreeCString(ctx, psd);

    ret_v = RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_CREATE_CONN, &ret, ret_s, &bufsize, 5);
    printf("[retval]ret_v = %d\n",ret_v);
    printf("[js_create]ret:%s\n",ret_s);

    return JS_NewString(ctx, ret_s);
}
JSValue js_SslSend(JSContext *ctx, JSValueConst this_val,
                                int argc, JSValueConst *argv)
{
    int ret = 0;
    int ret_v = 0;
    unsigned char *ret_s= NULL;
    ret_s = (unsigned char *)malloc(RET_MAX+1);
    int bufsize = RET_MAX;

    sslflag *get_object = JS_GetOpaque(this_val,js_sslconnect_class_id);
    printf("flag = %d; port = %d; hostname = %s\n",get_object->num,get_object->sslcon_port,get_object->hostname);
    if (!JS_IsString(argv[0]))
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "Data error");
    }
    const char *send_data = JS_ToCString(ctx, argv[0]);
    sslsendinfo sendinfo;
    sendinfo.sslcon_port = get_object->sslcon_port;
    strcpy(sendinfo.ssl_data,send_data);
    strcpy(sendinfo.sslcon_hostname,get_object->hostname);
    printf("[send data:%s]\n",sendinfo.ssl_data);

    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_SEND, 0, sizeof(sslsendinfo)+1, (char *)&sendinfo);

    ret_v = RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_SEND, &ret, ret_s, &bufsize, 5);
    printf("[retval]ret_v = %d\n",ret_v);
    printf("[js_send]ret:%s\n",ret_s);

    return JS_NewString(ctx, ret_s);
}

JSValue js_SslRecv(JSContext *ctx, int argc, JSValueConst *argv)
{
    JSValue func = argv[0];
    JSValue args_valu[] = {func};
    unsigned char *val= NULL;
    val = (unsigned char *)malloc(MAXBUF+1);
    int bufsize = MAXBUF;
    int res = 0;
    int ret = 0;

    ret = RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_RECV, &res, val, &bufsize, 5);
    printf("##[sslrecv_callback]## recv_data = %s; ret = %d\n",val,ret);
    if(strlen(val) != 0 && ret == 0)
    {
        printf("##[sslrecv_callback]## Continue recv data\n");
        JSValue args = JS_NewString(ctx,val);
        JSValue call_ret = JS_Call(ctx, func, JS_UNDEFINED, 1, (JSValueConst *) &args);
        JS_EnqueueJob(ctx, js_SslRecv, 1, args_valu);
    }
    else
    {
        printf("##[sslrecv_callback]## No data\n");
    }
    free(val);
    printf("##[sslrecv_callback]## End recv data \n");

    return JS_EXCEPTION;
}
JSValue sslonrecv(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = 0;
    //int ret_v = 0;
    //int bufsize = 0;

    sslflag *get_object = JS_GetOpaque(this_val,js_sslconnect_class_id);
    printf("flag = %d; port = %d; hostname = %s\n",get_object->num,get_object->sslcon_port,get_object->hostname);

    sslsendinfo sslrecvinfo;
    sslrecvinfo.sslcon_port = get_object->sslcon_port;
    strcpy(sslrecvinfo.sslcon_hostname,get_object->hostname);
    printf("sslrecv_hname = %s\n",sslrecvinfo.sslcon_hostname);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_RECV, 0, sizeof(sslsendinfo)+1, (char *)&sslrecvinfo);

    JSValue sslrecv = argv[0];
    JSValue args_val[] = {sslrecv};
    js_SslRecv(ctx, 1, args_val);

    printf("[sslonrecv]end js_SslRecv\n");
    /*
    ret_v = RecvMsgToMfp(MSG_MODULE_NET, MSG_SSL_RECV, &ret, NULL, &bufsize, 5);
    printf("[retval]ret_v = %d\n",ret_v);
    printf("[js_recv]ret:%d\n",ret);
    if(ret == 1)
    {
        return JS_NewString(ctx, "OK");
    }
    else
    {
        return JS_NewString(ctx, "Error");
    }
    */
    return JS_NewString(ctx, "EXIT_SUCCESS");
}

JSValue js_SslClose(JSContext *ctx, JSValueConst this_val,
                            int argc, JSValueConst *argv)
{
    int ret = 0;
    int ret_v = 0;
    unsigned char *ret_s= NULL;
    ret_s = (unsigned char *)malloc(RET_MAX+1);
    int bufsize = RET_MAX;
    if(argc != 0)
    {
        free(ret_s);
        return JS_ThrowTypeError(ctx, "Wrong number of input parameters");
    }

    printf("***close ssl connect***\n");
    sslflag *get_object = JS_GetOpaque(this_val,js_sslconnect_class_id);
    printf("flag = %d; port = %d; hostname = %s\n",get_object->num,get_object->sslcon_port,get_object->hostname);

    sslsendinfo sslcloseinfo;
    sslcloseinfo.sslcon_port = get_object->sslcon_port;
    strcpy(sslcloseinfo.sslcon_hostname,get_object->hostname);
    printf("sslclose_hname = %s\n",sslcloseinfo.sslcon_hostname);
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_CLOSE, 0, sizeof(sslsendinfo)+1, (char *)&sslcloseinfo);

    ret_v = RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_CLOSE, &ret, ret_s, &bufsize, 5);
    printf("[retval]ret_v = %d\n",ret_v);
    printf("[js_close]ret:%s\n",ret_s);

    return JS_NewString(ctx, ret_s);
}

// 证书安装函数
JSValue js_install_cert(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv)
{
    const char *cert_content, *key_content, *key_password;
    int export_flag;
    printf("js_install_cert\n");

    cert_content = JS_ToCString(ctx, argv[0]);
    key_content = JS_ToCString(ctx, argv[1]);
    key_password = JS_ToCString(ctx, argv[3]);;

    // 参数解析
    if (!cert_content)
    {
        return JS_NewString(ctx, "Invalid certificate");
    }
    export_flag = JS_ToBool(ctx, argv[2]);
    if(export_flag)
    {
        if(!key_content || !key_password)
        {
            return JS_NewString(ctx, "Invalid private key");

        }
    }

    // 打包参数到结构体
    CertInstallInfo info;
    memset(&info, 0, sizeof(info));
    strncpy(info.cert_content, cert_content, sizeof(info.cert_content) - 1);
    strncpy(info.key_content, key_content, sizeof(info.key_content) - 1);
    strncpy(info.key_password, key_password, sizeof(info.key_password) - 1);
    info.export_flag = export_flag;

    unsigned char receive_data[32];
    int32_t receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    printf("cert_content %s,key_content %s, key_password %s,export_flag %d \n",info.cert_content,info.key_content,info.key_password,info.export_flag);
    // 调用打印机接口
    SendMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_INSTALL_CERT, 0, sizeof(info), &info);
    RecvMsgToMfp(MSG_MODULE_NET, MSG_NET_SUB_SSL_INSTALL_CERT, &respond, receive_data, &receive_cnt, 10);

    // 根据响应结果处理
    if (respond == 0)
    {
        // 成功
        char result[512];
        snprintf(result, sizeof(result),
                 "{\"cert_path\":\"%s\",\"private_key_path\":\"%s\"}",
                 CERT_FILE_PATH, KEY_FILE_PATH);
        printf("Success result: %s\n", result);
        return JS_NewString(ctx, result);
    }
    else if (respond == -1)
    {
        // 证书验证失败
        return JS_NewString(ctx, "Invalid certificate");
    }
    else if (respond == -2)
    {
        // 密钥无效
        return JS_NewString(ctx, "Invalid private key");
    }
    else
    {
        // 未知错误
        return JS_NewString(ctx, "Unknown error during certificate installation");
    }

}

static JSCreatClassParam pesf_ssl_class_list[] = {
    {&js_CertInfo_class_id, &js_CertInfo_class, pesf_CertInfo_funcs, countof(pesf_CertInfo_funcs),  js_CertInfo_ctor, 7, "CertInfo"},
    {&js_ssllibrary_class_id, &js_ssllibrary_class, pesf_ssllibrary_funcs, countof(pesf_ssllibrary_funcs),  js_SSLLibrary_ctor, 0, "SSLLibrary"},
    {&js_sslconnect_class_id, &js_sslconnect_class, pesf_sslconnect_funcs, countof(pesf_sslconnect_funcs),  js_SslConnect_ctor, 0, "SslConnect"},
};

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_setting_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    {"install_cert",  5,  js_install_cert},

};

const JSCFunctionList* net_ssl_JSCFunctionList(int *length)
{
    *length = countof(pesf_setting_funcs);
    return pesf_setting_funcs;
}

int js_ssl_cert_init(JSContext *ctx, JSValueConst global)
{
   printf("*********start ssl_cert module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = net_ssl_JSCFunctionList(&count);
   printf("ssl count:%d\n",count);
   for(int i = 0; i < count; i++)
   {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   printf("ssl end:\n");

   printf("*********start ssl_cert init end**********\n");
   return 0;
}

int js_ssl_init(JSContext *ctx, JSValueConst global)
{
    JSValue ssl_proto, ssl_constructor;
    int i = 0;

    for(i = 0; i < sizeof(pesf_ssl_class_list)/sizeof(pesf_ssl_class_list[0]); i++)
    {
        /* create the ssl class */
        JS_NewClassID(pesf_ssl_class_list[i].class_id);
        JS_NewClass(JS_GetRuntime(ctx), *pesf_ssl_class_list[i].class_id, pesf_ssl_class_list[i].pclass);

        ssl_proto = JS_NewObject(ctx);
        JS_SetPropertyFunctionList(ctx, ssl_proto, pesf_ssl_class_list[i].pesf_funs, pesf_ssl_class_list[i].fun_len);
        JS_SetClassProto(ctx, *pesf_ssl_class_list[i].class_id, ssl_proto);

        ssl_constructor = JS_NewCFunction2(ctx, pesf_ssl_class_list[i].ctor, pesf_ssl_class_list[i].name, pesf_ssl_class_list[i].ctor_parm_num, JS_CFUNC_constructor, 0);
        printf("JS_NewCFunction2 name %s\n",pesf_ssl_class_list[i].name);
        /* set proto.constructor and ctor.prototype */
        JS_SetConstructor(ctx, ssl_constructor, ssl_proto);

        JS_DefinePropertyValueStr(ctx, global, pesf_ssl_class_list[i].name, ssl_constructor, JS_PROP_C_W_E);
    }

    return 0;
}

