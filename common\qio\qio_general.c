/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_general.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-04
 * @brief general qio object wrapper
 */
#include "qiox.h"

#define GQIO_UNLOCK()   { do { if (pgqio->rewind_lock != INVALIDMTX) pi_mutex_unlock(pgqio->rewind_lock); } while(0); }
#define GQIO_LOCK()     { do { if (pgqio->rewind_lock != INVALIDMTX) pi_mutex_lock(pgqio->rewind_lock);   } while(0); }

static int32_t gqio_poll(GQIO_S* pgqio, int32_t what, int32_t tos, int32_t tous)
{
    int32_t result;

    RETURN_VAL_IF(pgqio == NULL || pgqio->pqio == NULL, QIO_WARN, QIOEOF);

    GQIO_LOCK();
    if ( (what & QIO_POLL_READ) && (pgqio->rewind_obj.buffer) )
    {
        result = 1;
    }
    else
    {
        if ( pgqio->pqio->poll )
        {
            result = pgqio->pqio->poll(pgqio->pqio, what, tos, tous);
        }
        else
        {
            QIO_WARN("pqio<%p> has beed free and poll is NULL!\n", pgqio->pqio);
            result = -1;
        }
    }
    GQIO_UNLOCK();

    return result;
}

static int32_t gqio_read(GQIO_S* pgqio, void* buffer, size_t nbuf)
{
    size_t  remaining;
    int32_t result;
    int32_t rlen;

    RETURN_VAL_IF(pgqio == NULL || pgqio->pqio == NULL, QIO_WARN, QIOEOF);
    RETURN_VAL_IF(buffer == NULL || nbuf == 0, QIO_WARN, QIOEOF);

    GQIO_LOCK();
    if ( pgqio->rewind_obj.buffer )
    {
        remaining = pgqio->rewind_obj.size - pgqio->rewind_obj.offset;
        if ( nbuf < remaining )
        {
            memcpy(buffer, pgqio->rewind_obj.buffer + pgqio->rewind_obj.offset, nbuf);
            pgqio->rewind_obj.offset += nbuf;
            result = nbuf;
        }
        else
        {
            memcpy(buffer, pgqio->rewind_obj.buffer + pgqio->rewind_obj.offset, remaining);
            pi_free(pgqio->rewind_obj.buffer);
            memset(&(pgqio->rewind_obj), 0, sizeof(QREWIND_S));
            result = remaining;

            if ( nbuf > remaining )
            {
                rlen = QIO_READ(pgqio->pqio, buffer + remaining, nbuf - remaining);
                if ( rlen > 0 )
                {
                    result += rlen;
                }
                else
                {
                    QIO_WARN("read(%p->%p, %u) rlen(%d) err(%d<%s>)", pgqio, pgqio->pqio, nbuf - remaining, rlen, errno, strerror(errno));
                }
            }
        }
    }
    else
    {
        result = QIO_READ(pgqio->pqio, buffer, nbuf);
    }
    GQIO_UNLOCK();

    return result;
}

static int32_t gqio_write(GQIO_S* pgqio, void* buffer, size_t nbuf)
{
    int32_t result;

    RETURN_VAL_IF(pgqio == NULL || pgqio->pqio == NULL, QIO_WARN, QIOEOF);
    RETURN_VAL_IF(buffer == NULL || nbuf == 0, QIO_WARN, QIOEOF);

    GQIO_LOCK();
    result = QIO_WRITE(pgqio->pqio, buffer, nbuf);
    GQIO_UNLOCK();

    return result;
}

static int32_t gqio_rewind(GQIO_S* pgqio, void* buffer, size_t nbuf)
{
    int32_t result;

    RETURN_VAL_IF(pgqio == NULL || pgqio->pqio == NULL, QIO_WARN, QIOEOF);

    GQIO_LOCK();
    if ( pgqio->rewind_obj.buffer )
    {
        if ( nbuf > pgqio->rewind_obj.offset )
        {
            QIO_WARN("rewind %u bytes more than buffer offset %u", nbuf, pgqio->rewind_obj.offset);
            result = QIOEOF;
        }
        else
        {
            pgqio->rewind_obj.offset -= nbuf;
            if ( memcmp(pgqio->rewind_obj.buffer + pgqio->rewind_obj.offset, buffer, nbuf) != 0 )
            {
                QIO_INFO("rewind buffer exist, but current rewind data is not match original data in buffer!");
                memcpy(pgqio->rewind_obj.buffer + pgqio->rewind_obj.offset, buffer, nbuf);
            }
            result = (int32_t)nbuf;
        }
    }
    else
    {
        pgqio->rewind_obj.buffer = (uint8_t *)pi_zalloc(nbuf);
        if ( pgqio->rewind_obj.buffer == NULL )
        {
            QIO_WARN("alloc rewind buffer failed: %d<%s>", errno, strerror(errno));
            result = QIOEOF;
        }
        else
        {
            memcpy(pgqio->rewind_obj.buffer, buffer, nbuf);
            pgqio->rewind_obj.size   = nbuf;
            pgqio->rewind_obj.offset = 0;
            result = (int32_t)nbuf;
        }
    }
    GQIO_UNLOCK();

    return result;
}

static int32_t gqio_seek(GQIO_S* pgqio, ssize_t offset, int32_t from_where)
{
    size_t  remaining;
    int32_t cur_offset;
    int32_t result;

    RETURN_VAL_IF(pgqio == NULL || pgqio->pqio == NULL, QIO_WARN, QIOEOF);

    GQIO_LOCK();
    if ( pgqio->rewind_obj.buffer )
    {
        if ( from_where == SEEK_SET || from_where == SEEK_END )
        {
            result = QIO_SEEK(pgqio->pqio, offset, from_where);
        }
        else
        {
            cur_offset = QIO_SEEK(pgqio->pqio, 0, SEEK_CUR);
            if ( cur_offset < 0 )
            {
                result = QIO_SEEK(pgqio->pqio, offset, from_where);
            }
            else
            {
                remaining = pgqio->rewind_obj.size - pgqio->rewind_obj.offset;
                if ( offset > remaining )
                {
                    result = QIO_SEEK(pgqio->pqio, offset, from_where);
                }
                else if ( offset == remaining )
                {
                    pi_free(pgqio->rewind_obj.buffer);
                    memset(&(pgqio->rewind_obj), 0, sizeof(QREWIND_S));
                    result = cur_offset;
                }
                else
                {
                    pgqio->rewind_obj.offset += offset;
                    result = cur_offset - (pgqio->rewind_obj.size - pgqio->rewind_obj.offset);
                }
            }
        }
    }
    else
    {
        result = QIO_SEEK(pgqio->pqio, offset, from_where);
    }
    GQIO_UNLOCK();

    return result;
}

static int32_t gqio_close(GQIO_S* pgqio)
{
    RETURN_VAL_IF(pgqio == NULL || pgqio->pqio == NULL, QIO_WARN, QIOEOF);

    GQIO_LOCK();
    QIO_DEBUG("close GQIO<%p->%p>\n", pgqio, pgqio->pqio);
    QIO_CLOSE(pgqio->pqio);
    if ( pgqio->rewind_obj.buffer )
    {
        pi_free(pgqio->rewind_obj.buffer);
    }
    memset(&(pgqio->rewind_obj), 0, sizeof(QREWIND_S));
    GQIO_UNLOCK();

    if ( pgqio->rewind_lock != INVALIDMTX )
    {
        pi_mutex_destroy(pgqio->rewind_lock);
    }
    memset(pgqio, 0, sizeof(GQIO_S));
    pi_free(pgqio);

    return 0;
}

GQIO_S* gqio_create(QIO_S* pqio, IO_CLASS_E io_class, IO_VIA_E io_via)
{
    GQIO_S* pgqio;

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, NULL);

    pgqio = (GQIO_S *)pi_zalloc(sizeof(GQIO_S));
    RETURN_VAL_IF(pgqio == NULL, QIO_WARN, NULL);

    pgqio->rewind_lock  = pi_mutex_create();
    if ( pgqio->rewind_lock == INVALIDSEM )
    {
        QIO_WARN("create GQIO mutex failed: %d<%s>", errno, strerror(errno));
        pi_free(pgqio);
        return NULL;
    }

    QIO_DEBUG("create GQIO(%p) by QIO<%p>", pgqio, pqio);
    pgqio->close    = gqio_close;
    pgqio->poll     = gqio_poll;
    pgqio->read     = gqio_read;
    pgqio->write    = gqio_write;
    pgqio->rewind   = gqio_rewind;
    pgqio->seek     = gqio_seek;
    pgqio->io_class = io_class;
    pgqio->io_via   = io_via;
    pgqio->pqio     = pqio;

    return pgqio;
}
/**
 *@}
 */
