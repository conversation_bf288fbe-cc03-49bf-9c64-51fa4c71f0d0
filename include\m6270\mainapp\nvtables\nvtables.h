/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file nvtables.h
 * @addtogroup nvram
 * @{
 * @addtogroup nvtable
 * @brief Get nvram static array table  
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */


#ifndef _NVTABLES_H
#define _NVTABLES_H


/**
 * @brief Get the entry of nvram static array table
 *
 * @return table address 
 * <AUTHOR>
 */
void* get_nvtables_point(void);

/**
 * @brief Get the number of elements in the nvram static array 
 *
 * @return non-negative number 
 */
unsigned int get_nvtables_number(void);

#endif //_NVTABLES_H
/**
 * @}
 */
