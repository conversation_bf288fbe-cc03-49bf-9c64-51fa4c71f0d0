/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_pdf.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-17
 * @brief QIO object that creates a PDF file from an image stream
 */
#include "qiox.h"

#define PDF_MAX_OBJ_SIZE    16*2048                ///< size of formatting buffer for header/trailer
#define PDF_MAX_PAGES       2048                ///< max pages (images) we account for
#define PDF_MAX_OBJECTS     (PDF_MAX_PAGES * 4) ///< each page has four objects (1 header and 3 in trailer)
#define PDF_MAX_FILTER      128                 ///< max filter spec
#define PDF_DOT_PER_INCH    72                  ///< pdf unit of measure dpi
#define PDF_OBJ_HEADER      0
#define PDF_OBJ_CATALOG     1
#define PDF_OBJ_PAGES       2
#define PDF_OBJ_BASE        3

#define FMT_COLOR(f)        ((f) & FMT_COLORMASK)

typedef struct private_info_pdf
{
    QIO_S*      pqio;                           ///< Actual output stream qio
    int32_t     ownqio;                         ///< this qio owns dest qio (to close it at close)
    int32_t     isftp;                          ///< flag ftp
    int32_t     imgcount;                       ///< total images read in
    int32_t     nobj;                           ///< number of objects
    int64_t     offset;                         ///< bytes written to output stream
    int64_t     imgstart;                       ///< offset where current image starts
    int32_t     w, h, d, s, f;                  ///< current image dimensions and format
    int32_t     xres, yres;                     ///< the resolution of the image, in DPI, where applicable
    int32_t     clipX, clipY;                   ///< max dimensions of output w/h
    int64_t     objoff[PDF_MAX_OBJECTS];        ///< in-page object offsets
    int32_t     pageobj[PDF_MAX_PAGES];         ///< page object offsets
    char        filter[PDF_MAX_FILTER];         ///< filter (compression type) of source
    int32_t     tos;                            ///< timeout, seconds, for writing qio
    int32_t     tous;                           ///< timeout, microseconds
    uint8_t*    pdfobj;                         ///< format pdf objects into
    int32_t     pdf_image_length;
    int64_t     pdf_image_length_offset_addr;
    char        pdf_pagekids_buffer[32*PDF_MAX_PAGES];
}
PRIV_INFO_S;

// pdf envelope
//
static const char *s_pdf_header =
"%PDF-1.4\r\n"
"%\342\343\317\323\r\n";

static const char *s_pdf_01_catalog =
"1 0 obj\n"
"  << /Type /Catalog\n"
"   /Pages 2 0 R\n"
"  >>\n"
"endobj\n";

static const char *s_pdf_02_pages =
"2 0 obj\n"
"  << /Type /Pages\n"
"     /Kids [%s]\n"         /* put in where /Page objects in different pages */
"     /Count %d\n"          /* how many pages in this pdf */
// (ex. /Kids [ 4 0 R 8 0 R] /Count 2 for two pages)
"  >>\n"
"endobj\n"
"\n";

static const char *s_pdf_page =
"%d 0 obj\n"  // npage
"  << /Type /Page\n"
"    /Parent 2 0 R\n"
"    /MediaBox [ 0 0 %5.2f %5.2f ]\n"   /* put in image w/h transformed to 1/72 dots per inch */
"    /Contents %d 0 R\n"                /* npage + 1 */
"    /Resources << /ProcSet %d 0 R\n"   /* npage + 2 */
"                  /XObject << /Im1 %d 0 R >>\n"  /* npage + 3 */
"               >>\n"
"  >>\n"
"endobj\n"
"\n";

static const char *s_pdf_contents =
"%d 0 obj\n"
"  << /Length %d >>\n"
"stream\r\n"
"%s"
"endstream\r\n"
"endobj\r\n";

static const char *s_pdf_stream =
"q %5.2f 0 0 %5.2f 0 0 cm /Im1 Do Q\n"; /* put in image dimensions */

static const char *s_pdf_procset =
/*
   "    [/PDF /Text]\n"
   "  << [/PDF /ImageC]\n"  // Color
   "  << [/PDF /ImageB]\n"  // Mono
   */
"%d 0 obj\r\n"  // 5 0
"[/PDF/%s]"
"\r\n"
"endobj\r\n"
;

static const char *s_pdf_image_header =
"%d 0 obj\n"
"  << /Type /XObject\n"
"  /Subtype /Image\n"
"%s"                                  /* filter         */
"  /Width %d\n"                       /* width          */
"  /Height %d\n"                      /* height         */
"  /ColorSpace /%s\n"                 /* color space    */
"  /BitsPerComponent %d\n"            /* depth          */
"  /Length %d 0 R\n\n\n\n\n\n\n\n\n\n"    /* bytes in image */
"  >>\n"
"stream\n";

static const char *s_pdf_image_trailer =
"\nendstream\n"
"endobj\n"
"\n";

static const char *s_pdf_xref_header =
"xref\n"
"0 %d\n" /* put in number of objects */
"0000000000 65535 f\r\n";

static const char *s_pdf_xref_entry =
"%010lld %05d n\r\n";  /* byte offset and generation (note \r is important!) */

static const char *s_pdf_trailer =
"\ntrailer\n"
"  << /Size %d\n"    // how many pdf object in all pages
"     /Root %d 0 R\n"
"  >>\n"
"startxref\n"
"%d\n"      /* offet from 0 to xref tag in bytes */
"%%%%EOF";   /*%%EOF */

static const char* image_pdf_colorspace(uint32_t format)
{
    const char* rs;

    switch ( FMT_COLOR(format) )
    {
    case cfRGB:     rs = "DeviceRGB";   break;
    case cfCMYK:    rs = "DeviceCMYK";  break;
    case cfYCbCr:   rs = "DeviceRGB";   break;
    case cfMono:    rs = "DeviceGray";  break;
    default:        rs = "ERROR";       break;
    }

    return rs;
}

static const char *image_pdf_color(uint32_t format)
{
    return ( (FMT_COLOR(format) == cfMono) ? "ImageB" : "ImageC" );
}

static int32_t write_image_bytes(PRIV_INFO_S* priv, uint8_t* bytes, int32_t bytelen)
{
    size_t  chunk;
    int32_t wtotal;
    int32_t wlen;

    RETURN_VAL_IF(bytelen <= 0, QIO_WARN, 0);

    for ( wtotal = 0; wtotal < bytelen; wtotal += wlen )
    {
        RETURN_VAL_IF(QIO_WRITEABLE(priv->pqio, priv->tos, priv->tous) <= 0, QIO_WARN, -1);

        chunk = ( ((bytelen - wtotal) < MAX_PDF_CHUNK_SIZE) ? (bytelen - wtotal) : MAX_PDF_CHUNK_SIZE );
        priv->pdf_image_length += (int32_t)chunk;
        wlen = QIO_WRITE(priv->pqio, bytes + wtotal, chunk);
        RETURN_VAL_IF(wlen < 0, QIO_WARN, -2);
    }
    priv->offset += wtotal;

    return wtotal;
}

static int32_t write_image_header(PRIV_INFO_S* priv)
{
    char    filter[PDF_MAX_FILTER + 32];
    char*   start_ptr = NULL;
    int64_t file_cur_seek = 0;
    int32_t len;
    int32_t wc;

    QIO_DEBUG("write_image_header");
    priv->pdf_image_length = 0;
    RETURN_VAL_IF(priv->nobj >= PDF_MAX_OBJECTS - 1, QIO_WARN, -1);
    RETURN_VAL_IF(priv->imgcount >= PDF_MAX_PAGES, QIO_WARN, -2);

    if ( priv->clipX <= 0 )
    {
        priv->clipX = priv->w;
    }
    if ( priv->clipY <= 0 )
    {
        priv->clipY = priv->h;
    }
    if ( priv->xres <= 0 )
    {
        priv->xres = 600;
    }
    if ( priv->yres <= 0 )
    {
        priv->yres = 600;
    }

    QIO_DEBUG("pdf parm = clipX:%d, clipY:%d, w:%d, h:%d, d:%d, s:%d, f:0x%08x, x:%d, y:%d",
            priv->clipX, priv->clipY, priv->w, priv->h, priv->d, priv->s, priv->f, priv->xres, priv->yres);

    if ( priv->filter[0] )
    {
        snprintf(filter, sizeof(filter), "  /Filter /%s\n", priv->filter);
    }
    else
    {
        filter[0] = '\0';
    }

    // Image Object in Page Object
    priv->objoff[priv->nobj] = priv->offset;
    len = snprintf((char *)priv->pdfobj,
                   PDF_MAX_OBJ_SIZE,
                   s_pdf_image_header,
                   priv->nobj,
                   filter,
                   priv->clipX,
                   priv->clipY,
                   image_pdf_colorspace(priv->f),
                   (priv->d/(FMT_COLOR(priv->f)==cfRGB?3:1)),
                   (priv->filter[0] ? 0 : priv->clipY * priv->s));
    // add by lilei
    file_cur_seek = QIO_SEEK(priv->pqio, 0, SEEK_END);
    QIO_DEBUG("file_cur_seek = %d", file_cur_seek);

    start_ptr = strstr((char *)priv->pdfobj, "Length");
    RETURN_VAL_IF(start_ptr == NULL, QIO_WARN, -1);

    start_ptr += 8;  // "Length 0"
    RETURN_VAL_IF((size_t)start_ptr <= (size_t)priv->pdfobj, QIO_WARN, -1);

    priv->pdf_image_length_offset_addr = file_cur_seek + (int32_t)((size_t)start_ptr - (size_t)priv->pdfobj);
    priv->pdf_image_length_offset_addr -= 1;
    QIO_DEBUG("offset_addr = %lld", priv->pdf_image_length_offset_addr);

    wc = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    priv->pdf_image_length = 0;
    priv->nobj++;

    return 0;
}

static int32_t write_image_trailer(PRIV_INFO_S* priv)
{
    char    len_str[11];
    char    pdf_stream[128];
    int32_t pdf_stream_len;
    int32_t len;
    int32_t wc;

    QIO_DEBUG("write_image_trailer = %d", priv->pdf_image_length );
    RETURN_VAL_IF(priv->nobj >= PDF_MAX_OBJECTS - 1, QIO_WARN, -1);
    RETURN_VAL_IF(priv->imgcount >= PDF_MAX_PAGES, QIO_WARN, -2);

    if ( priv->isftp == 0 )
    {
        RETURN_VAL_IF(priv->pdf_image_length > 0x25000000, QIO_WARN, -1);
        memset(len_str, 0, sizeof(len_str));
        len = snprintf(len_str, sizeof(len_str), "%d", priv->pdf_image_length);
        QIO_DEBUG("pdf_image_length(%s)", len_str);
        QIO_DEBUG("pdf_image_length_offset_addr(%lld)", priv->pdf_image_length_offset_addr);
        QIO_SEEK(priv->pqio, priv->pdf_image_length_offset_addr, SEEK_SET);
        wc = QIO_WRITE(priv->pqio, len_str, len);
        RETURN_VAL_IF(wc != len, QIO_WARN, -1);
        QIO_SEEK(priv->pqio, 0, SEEK_END);
    }

    // Image trailer
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, "%s", s_pdf_image_trailer);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    // Page Object
    priv->objoff[priv->nobj] = priv->offset;
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_page, priv->nobj,
            (priv->clipX * PDF_DOT_PER_INCH) / (float)priv->xres,
            (priv->clipY * PDF_DOT_PER_INCH) / (float)priv->yres,
            priv->nobj + 1,  // Contents
            priv->nobj + 2,  // Resources(Procset)
            priv->nobj - 1   // Image(XObject)
            );
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    priv->pageobj[priv->imgcount - 1] = priv->nobj;
    priv->nobj++;

    // Contents Object in Page Object
    pdf_stream_len = snprintf(pdf_stream, sizeof(pdf_stream), s_pdf_stream,
            (priv->clipX * PDF_DOT_PER_INCH) / (float)priv->xres,
            (priv->clipY * PDF_DOT_PER_INCH) / (float)priv->yres);
    priv->objoff[priv->nobj] = priv->offset;
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_contents, priv->nobj, pdf_stream_len, pdf_stream);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);
    priv->nobj++;

    // Resources(Procset) Object in Page Object
    priv->objoff[priv->nobj] = priv->offset;
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_procset, priv->nobj, image_pdf_color(priv->f));
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);
    priv->nobj++;

    return 0;
}

static int32_t write_pdf_header(PRIV_INFO_S* priv)
{
    int32_t len;
    int32_t wc;

    priv->objoff[PDF_OBJ_HEADER] = priv->offset;
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, "%s", s_pdf_header);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);
    priv->imgstart = priv->offset;

    return 0;
}

static int32_t write_pdf_trailer(PRIV_INFO_S* priv)
{
    char*   pagekids;
    char    curkid[32];
    int32_t len;
    int32_t wc;
    int32_t i;

    memset(priv->pdf_pagekids_buffer, 0, sizeof(priv->pdf_pagekids_buffer));
    pagekids = &priv->pdf_pagekids_buffer[ 0 ];

    // 1. Catalog Object.
    priv->objoff[PDF_OBJ_CATALOG] = priv->offset;
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, "%s", s_pdf_01_catalog);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    // 2. Pages Object: describe how many pages in this document
    priv->objoff[PDF_OBJ_PAGES] = priv->offset;

    // Prepare kids object string
    // pagekids[0] = 0;
    for ( i = 0; i < priv->imgcount; ++i )
    {
        snprintf(curkid, sizeof(curkid), "%d 0 R ", priv->pageobj[i]);
        strcat(pagekids, curkid);
    }

    if ( strlen(pagekids) > 1 && pagekids[strlen(pagekids) - 1] == ' ' )
    {
        pagekids[strlen(pagekids) - 1] = 0;
    }

    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_02_pages, pagekids, priv->imgcount);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    // xref: 0 (header)
    priv->objoff[priv->nobj] = priv->offset;
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_xref_header, priv->nobj);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    // xref: 1 ~ priv->nobj
    for ( i = 1; i < priv->nobj; ++i )
    {
        len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_xref_entry, priv->objoff[i], 0);
        wc  = write_image_bytes(priv, priv->pdfobj, len);
        RETURN_VAL_IF(wc != len, QIO_WARN, -1);
    }

    // xref: trailer
    len = snprintf((char *)priv->pdfobj, PDF_MAX_OBJ_SIZE, s_pdf_trailer, priv->nobj, 1, priv->objoff[priv->nobj]);
    wc  = write_image_bytes(priv, priv->pdfobj, len);
    RETURN_VAL_IF(wc != len, QIO_WARN, -1);

    return 0;
}

static int32_t qio_pdf_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(what != QIO_POLL_WRITE, QIO_WARN, QIOEOF);
    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    return ( priv->pqio ? QIO_WRITEABLE(priv->pqio, tos, tous) : 1 );
}

static int32_t qio_pdf_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    return QIOEOF;
}

static int32_t qio_pdf_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);
    int32_t rc;

    RETURN_VAL_IF(buffer == NULL || nbuf == 0, QIO_WARN, 0);
    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    if ( priv->nobj >= PDF_MAX_OBJECTS - 1 )
    {
        if ( priv->imgstart == priv->offset )
        {
            // can't fit more objects into xref, so fail
            // and set imgstart to bogus value to avoid other debug msgs
            QIO_WARN("No room for more PDF objects");
            priv->imgstart = -1;
        }
        return -1;
    }

    if ( priv->imgstart > 0 )
    {
        if ( priv->imgstart == priv->offset )
        {
            priv->imgcount++;
            QIO_DEBUG("Begin image %d at %d", priv->imgcount, priv->imgstart);

            // this is the first byte(s) for the image, write header out
            RETURN_VAL_IF(write_image_header(priv) != 0, QIO_WARN, -1);
        }
        rc = write_image_bytes(priv, buffer, nbuf);
    }
    else
    {
        QIO_WARN("attempt to write image bytes before pi_qio_pdf_begin_image called");
        rc = -1;
    }

    return rc;
}

static int32_t qio_pdf_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

static int32_t qio_pdf_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( priv )
    {
        QIO_DEBUG("close PDF<%p> QIO<%p>", priv->pqio, pqio);
        if ( priv->offset > priv->imgstart )
        {
            write_image_trailer(priv); // if any image bytes written, clean up
        }
        write_pdf_trailer(priv);

        if ( priv->pqio && priv->ownqio)
        {
            QIO_CLOSE(priv->pqio);
        }

        if ( priv->pdfobj )
        {
            pi_free(priv->pdfobj);
        }

        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_pdf_create(QIO_S* pqio_dst, int32_t take_owner_ship)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;

    RETURN_VAL_IF(pqio_dst == NULL, QIO_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    priv->pdfobj = (uint8_t *)pi_zalloc(PDF_MAX_OBJ_SIZE);
    if ( priv->pdfobj == NULL )
    {
        QIO_WARN("alloc pdfobj failed: %d<%s>", errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }

    priv->pqio      = pqio_dst;
    priv->ownqio    = take_owner_ship;
    priv->isftp     = 0;
    priv->imgcount  = 0;
    priv->nobj      = PDF_OBJ_BASE;
    priv->offset    = 0;
    priv->imgstart  = 0;
    priv->w         = 0;
    priv->h         = 0;
    priv->d         = 0;
    priv->s         = 0;
    priv->f         = 0;
    priv->xres      = 600;
    priv->yres      = 600;
    priv->clipX     = 0;
    priv->clipY     = 0;
    priv->tos       = 15;
    priv->tous      = 0;

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        QIO_WARN("alloc PDF<%p> QIO failed: %d<%s>", priv->pqio, errno, strerror(errno));
        pi_free(priv->pdfobj);
        pi_free(priv);
        return NULL;
    }

    pqio->close     = qio_pdf_close;
    pqio->poll      = qio_pdf_poll;
    pqio->read      = qio_pdf_read;
    pqio->write     = qio_pdf_write;
    pqio->seek      = qio_pdf_seek;
    pqio->priv      = (void*)priv;

    priv->pdf_image_length = 0;
    priv->pdf_image_length_offset_addr = 0;
    write_pdf_header(priv);

    return pqio;
}

int32_t qio_pdf_begin_image(QIO_S* pqio, const char* filter, int32_t w, int32_t h, int32_t d, int32_t s, int32_t f, int32_t xres, int32_t yres, int32_t clipx, int32_t clipy)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, -1);
    // if any image bytes written, clean up
    if ( priv->imgcount > 0 )
    {
        RETURN_VAL_IF(priv->imgcount >= PDF_MAX_PAGES, QIO_WARN, -1);

        if ( priv->offset > priv->imgstart && priv->imgstart > 0 )
        {
            write_image_trailer(priv);
        }
    }

    // save the image dimensions.  we'll write out the header later
    // only if image bytes come in
    priv->imgstart  = priv->offset;
    priv->w         = w;
    priv->h         = h;
    priv->d         = d;
    priv->s         = s;
    priv->f         = f;
    priv->xres      = xres;
    priv->yres      = yres;
    priv->clipX     = clipx;
    priv->clipY     = clipy;

    if ( STRING_NO_EMPTY(filter) )
    {
        snprintf(priv->filter, sizeof(priv->filter), "%s", filter);
    }
    else
    {
        priv->filter[0] = '\0';
    }

    return 0;
}

int32_t qio_pdf_recreate_file(QIO_S* pqio, char* path)
{
    DECL_PRIV(pqio, priv);
    QIO_S*  pqio_file;
    char    len_str[11];
    int32_t wlen;
    int32_t len;

    RETURN_VAL_IF(priv == NULL, QIO_WARN, -1);
    RETURN_VAL_IF(path == NULL, QIO_DEBUG, 0);

    RETURN_VAL_IF(priv->pdf_image_length > 0x25000000, QIO_WARN, -1);

    memset(len_str, 0, sizeof(len_str));
    len = snprintf(len_str, sizeof(len_str), "%d", priv->pdf_image_length);
    QIO_WARN("pdf_image_length(%s)", len_str);
    QIO_WARN("pdf_image_length_offset_addr(%lld)", priv->pdf_image_length_offset_addr);
    QIO_SEEK(priv->pqio, priv->pdf_image_length_offset_addr, SEEK_SET);
    wlen = QIO_WRITE(priv->pqio, len_str, len);
    RETURN_VAL_IF(wlen != len, QIO_WARN, -1);

    QIO_SEEK(priv->pqio, 0, SEEK_END);
    priv->isftp = 1;                                                            //flag ftp
    QIO_CLOSE(priv->pqio);
    priv->pqio = NULL;

    pqio_file = qio_file_create(path, O_CREAT | O_EXCL | O_RDWR | O_SYNC);
    RETURN_VAL_IF(pqio_file == NULL, QIO_WARN, -1);

    QIO_DEBUG("filepath(%s) QIO<%p>", path, pqio_file);
    priv->pqio = pqio_file;

    return 0;
}
/**
 *@}
 */
