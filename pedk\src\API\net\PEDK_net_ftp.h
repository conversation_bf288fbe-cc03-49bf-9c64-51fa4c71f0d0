#ifndef _PEDK_NET_FTP_
#define _PEDK_NET_FTP_

#include <quickjs.h>

#define FTP_ADDR_LEN 256
#define FTP_SERVER_PATH_LEN 256
#define FTP_LOGIN_NAME_LEN  128
#define FTP_LOGIN_PWD_LEN   32

#include <quickjs.h>


typedef struct
{
    char     ftp_addr[FTP_ADDR_LEN];               //服务器IP
    char     ftp_server_path[FTP_SERVER_PATH_LEN];        //ftp服务器路径
    uint16_t ftp_port;                                         //端口号
    uint16_t ftp_anonymity;                                    //是否匿名
    char     ftp_login_name[FTP_LOGIN_NAME_LEN];          //登录名
    char     ftp_login_pwd[FTP_LOGIN_PWD_LEN];             //密码

}
PEDK_FTP_PARAMETER_SET;

JSValue js_ftp_connect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);

JSValue js_ftp_disconnect(JSContext *ctx, JSValueConst this_val, int argc, JSValueConst *argv);




#endif /* _PEDK_NET_FTP_ */
