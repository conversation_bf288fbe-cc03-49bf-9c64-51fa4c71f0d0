/*******************************************************************************
 Copyright (c) 2009-2014  Pantum Technology. All Rights Reserved

                        Pantum Technology Confidential
******************************************************************************
Author: Wangfei
Date: 2024-03-07
Description:
******************************************************************************/
#ifndef __PARSER_PUBLIC_H__
#define __PARSER_PUBLIC_H__

#include "public_data_proc.h"

typedef enum {
    PARSER_COLOR_MODE_MONOCHROME,
    PARSER_COLOR_MODE_COLOR,
    PARSER_COLOR_MODE_AUTO,
}PARSER_COLOR_MODE_E;

typedef enum {
    PARSER_QUALITY_AUTO,
    PARSER_QUALITY_DRAFT,
    PARSER_QUALITY_NORMAL,
    PARSER_QUALITY_HIGH
} PARSER_QUALITY_E;

typedef enum
{
    PARSER_IMAGE_DIRECT_AUTO = 1,             //自动
    PARSER_IMAGE_DIRECT_LANDSCAPE,            //横向
    PARSER_IMAGE_DIRECT_PORTRAIT,             //纵向,默认
}PARSER_IMAGE_DIRECT_E;

typedef enum {
    PARSER_SCALE_MODE_AUTO = 0,
    PARSER_SCALE_MODE_FIT = 1 ,
    PARSER_SCALE_MODE_AUTOFIT = 2,
    PARSER_SCALE_MODE_FILL = 3,
    PARSER_SCALE_MODE_CENTER = 4,
} PARSER_SCALE_MODE_E;

typedef enum {
    PARSER_IPP_FIDELITY_UNSPECIFIED =0,
    PARSER_IPP_FIDELITY_FORCE =1,
    PARSER_IPP_FIDELITY_AUTO,
}PARSER_IPP_FIDELITY_E;


typedef struct
{
    uint32_t                print_mode;         ///双面模式 0-单面打印(默认)，1-双面短边打印 2-双面长边打印
    uint32_t                collate_mode;       ///逐份打印 0-关闭       1-开启
    uint32_t                copies;             ///份数 0-9999
    uint32_t                media_source;       ///介质来源
    PAPER_SIZE_E            paper_size;         ///纸张尺寸
    PAPER_TYPE_E            paper_type;         ///纸张介质
    uint32_t                resolution_mode;    ///分辨率模式 0-300DPI 1-600DPI
    PARSER_QUALITY_E        quality;            ///质量
    PARSER_COLOR_MODE_E     color;              ///颜色
    PARSER_IMAGE_DIRECT_E   orientation;        ///方向
    uint32_t                fold_mode;          ///装订-折叠模式
    uint32_t                punch_mode;         ///装订-打孔模式
    uint32_t                staple_mode;        ///装订-钉钉模式
    PARSER_SCALE_MODE_E     scale_mode;         ///缩放模式
    PARSER_IPP_FIDELITY_E   fidelity_mode;      ///保真度模式
    char                    page_range[65];     /// rang page print
    char                    job_name[128];      /// job name
    void *                  context;
}PARSER_DATA_S,*PARSER_DATA_P;


#endif


