#ifndef EXAMPLES_SEINE_H
#define EXAMPLES_SEINE_H

#ifdef __cplusplus
extern "C" {
#endif

void init_seine(void);

#define SEINE_HANDLER_INT(oid_name, array...) static oid oid_name ## _oid[] = {array};  \
    static int oid_name ## _handler(netsnmp_mib_handler *handler,                       \
        netsnmp_handler_registration *reginfo,                                          \
        netsnmp_agent_request_info *reqinfo,                                            \
        netsnmp_request_info *requests)                                                 \
    {                                                                                   \
        int length;                                                                     \
        DEBUGMSGTL(("seine", "in %s\n", __func__));                                     \
                                                                                        \
        while (requests) {                                                              \
            netsnmp_variable_list *var = requests->requestvb;                           \
            switch (reqinfo->mode) {                                                    \
                case MODE_GET:                                                          \
                length = var->name_length;                                              \
                if (length > OID_LENGTH(oid_name ## _oid))                              \
                {                                                                       \
                    length = OID_LENGTH(oid_name ## _oid);                              \
                }                                                                       \
                if (netsnmp_oid_equals(var->name, length, oid_name ## _oid,             \
                            OID_LENGTH(oid_name ## _oid))                               \
                        == 0) {                                                         \
                    int oid_name ## _val = 0;                                           \
                                                                                        \
                    DEBUGMSGTL(("seine", "oid " #oid_name "\n"));                       \
                    flock(seine_lock, LOCK_EX);                                         \
                    oid_name ## _val = seine_map->_ ## oid_name;                        \
                    flock(seine_lock, LOCK_UN);                                         \
                                                                                        \
                    snmp_set_var_typed_value(var, ASN_INTEGER,                          \
                            (u_char *) &oid_name ## _val, sizeof(oid_name ## _val));    \
                    return SNMP_ERR_NOERROR;                                            \
                }                                                                       \
                break;                                                                  \
                                                                                        \
                case MODE_GETNEXT:                                                      \
                break;                                                                  \
                                                                                        \
                default:                                                                \
                netsnmp_set_request_error(reqinfo, requests, SNMP_ERR_GENERR);          \
                break;                                                                  \
            }                                                                           \
                                                                                        \
            requests = requests->next;                                                  \
        }                                                                               \
        return SNMP_ERR_NOERROR;                                                        \
    }                                                                                   \
    static void register_ ## oid_name (void)                                            \
    {                                                                                   \
        netsnmp_handler_registration *reginfo;                                          \
        static netsnmp_watcher_info watcher_info;                                       \
                                                                                        \
        reginfo = netsnmp_create_handler_registration(#oid_name, oid_name ## _handler,  \
                oid_name ## _oid, OID_LENGTH(oid_name ## _oid),                         \
                HANDLER_CAN_RONLY);                                                     \
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_INTEGER,         \
                WATCHER_MAX_SIZE,sizeof(int), NULL);                                    \
        netsnmp_register_watched_instance(reginfo, &watcher_info);                      \
    }

#define SEINE_HANDLER_STR(oid_name, array...) static oid oid_name ## _oid[] = {array};  \
        int oid_name ## _handler(netsnmp_mib_handler *handler,                          \
        netsnmp_handler_registration *reginfo,                                          \
        netsnmp_agent_request_info *reqinfo,                                            \
        netsnmp_request_info *requests)                                                 \
    {                                                                                   \
        int length;                                                                     \
        DEBUGMSGTL(("seine", "in %s\n", __func__));                                     \
                                                                                        \
        while (requests) {                                                              \
            netsnmp_variable_list *var = requests->requestvb;                           \
            switch (reqinfo->mode) {                                                    \
                case MODE_GET:                                                          \
                length = var->name_length;                                              \
                if (length > OID_LENGTH(oid_name ## _oid))                              \
                {                                                                       \
                    length = OID_LENGTH(oid_name ## _oid);                              \
                }                                                                       \
                if (netsnmp_oid_equals(var->name, length,                               \
                            oid_name ## _oid, OID_LENGTH(oid_name ## _oid))             \
                        == 0) {                                                         \
                    u_char oid_name ## _val[1024] = {0};                                 \
                                                                                        \
                    DEBUGMSGTL(("seine", "oid " #oid_name "\n"));                       \
                    flock(seine_lock, LOCK_EX);                                         \
                    memcpy(&oid_name ## _val, &seine_map->_ ## oid_name,                \
                            sizeof(seine_map->_ ## oid_name));                          \
                    flock(seine_lock, LOCK_UN);                                         \
                                                                                        \
                    snmp_set_var_typed_value(var, ASN_OCTET_STR, oid_name ## _val,      \
                        strlen(oid_name ## _val));                                      \
                    return SNMP_ERR_NOERROR;                                            \
                }                                                                       \
                break;                                                                  \
                case MODE_GETNEXT:                                                      \
                break;                                                                  \
                default:                                                                \
                netsnmp_set_request_error(reqinfo, requests,                            \
                    SNMP_ERR_GENERR);                                                   \
                break;                                                                  \
            }                                                                           \
                                                                                        \
            requests = requests->next;                                                  \
        }                                                                               \
        return SNMP_ERR_NOERROR;                                                        \
    }                                                                                   \
    static void register_ ## oid_name(void)                                             \
    {                                                                                   \
        netsnmp_handler_registration *reginfo;                                          \
        static  netsnmp_watcher_info watcher_info;                                      \
                                                                                        \
        reginfo = netsnmp_create_handler_registration(#oid_name, oid_name ## _handler,  \
                oid_name ## _oid, OID_LENGTH(oid_name ## _oid),                         \
                HANDLER_CAN_RONLY);                                                     \
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR,       \
                WATCHER_MAX_SIZE,sizeof(int), NULL);                                    \
        netsnmp_register_watched_instance(reginfo, &watcher_info);                      \
    }

#define SEINE_HANDLER_STRUCT(type, oid_name, array...) static oid oid_name ## _oid[] = {array};  \
        int oid_name ## _handler(netsnmp_mib_handler *handler,                          \
        netsnmp_handler_registration *reginfo,                                          \
        netsnmp_agent_request_info *reqinfo,                                            \
        netsnmp_request_info *requests)                                                 \
    {                                                                                   \
        int length;                                                                     \
        DEBUGMSGTL(("seine", "in %s\n", __func__));                                     \
                                                                                        \
        while (requests) {                                                              \
            netsnmp_variable_list *var = requests->requestvb;                           \
            switch (reqinfo->mode) {                                                    \
                case MODE_GET:                                                          \
                length = var->name_length;                                              \
                if (length > OID_LENGTH(oid_name ## _oid))                              \
                {                                                                       \
                    length = OID_LENGTH(oid_name ## _oid);                              \
                }                                                                       \
                if (netsnmp_oid_equals(var->name, length,                               \
                            oid_name ## _oid, OID_LENGTH(oid_name ## _oid))             \
                        == 0) {                                                         \
                    type oid_name ## _val;                                              \
                                                                                        \
                    DEBUGMSGTL(("seine", "oid " #oid_name "\n"));                       \
                    flock(seine_lock, LOCK_EX);                                         \
                    memcpy(&oid_name ## _val, &seine_map->_ ## oid_name,                \
                            sizeof(oid_name ## _val));                                  \
                    flock(seine_lock, LOCK_UN);                                         \
                                                                                        \
                    snmp_set_var_typed_value(var, ASN_OCTET_STR,                        \
                        (u_char *) &oid_name ## _val, sizeof(seine_map->_ ## oid_name));\
                    return SNMP_ERR_NOERROR;                                            \
                }                                                                       \
                break;                                                                  \
                case MODE_GETNEXT:                                                      \
                break;                                                                  \
                default:                                                                \
                netsnmp_set_request_error(reqinfo, requests,                            \
                    SNMP_ERR_GENERR);                                                   \
                break;                                                                  \
            }                                                                           \
                                                                                        \
            requests = requests->next;                                                  \
        }                                                                               \
        return SNMP_ERR_NOERROR;                                                        \
    }                                                                                   \
    static void register_ ## oid_name(void)                                             \
    {                                                                                   \
        netsnmp_handler_registration *reginfo;                                          \
        static  netsnmp_watcher_info watcher_info;                                      \
                                                                                        \
        reginfo = netsnmp_create_handler_registration(#oid_name, oid_name ## _handler,  \
                oid_name ## _oid, OID_LENGTH(oid_name ## _oid),                         \
                HANDLER_CAN_RONLY);                                                     \
        netsnmp_init_watcher_info6(&watcher_info, " ", strlen(""), ASN_OCTET_STR,       \
                WATCHER_MAX_SIZE,sizeof(int), NULL);                                    \
        netsnmp_register_watched_instance(reginfo, &watcher_info);                      \
    }

#ifdef __cplusplus
}
#endif

#endif /* EXAMPLES_SEINE_H */
