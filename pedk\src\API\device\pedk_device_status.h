#ifndef _PEDK_DEVICE_STATUS_
#define _PEDK_DEVICE_STATUS_

#include <quickjs.h>

/*
    声明 QuickJS C 函数由于初始化回调
*/
JSValue js_getStatusIdListLength(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);
JSValue js_getStatusIdList(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);
JSValue js_getTopStatusId(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);
JSValue js_getStatusType(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);
JSValue js_getStatusModule(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);
JSValue js_getStatusPriority(JSContext *ctx, JSValueConst this_val, int32_t argc, JSValueConst *argv);
int32_t js_status_init(JSContext *ctx, JSValueConst global);

#endif /* _PEDK_DEVICE_STATUS_ */
