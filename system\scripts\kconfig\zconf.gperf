%language=ANSI-C
%define hash-function-name kconf_id_hash
%define lookup-function-name kconf_id_lookup
%define string-pool-name kconf_id_strings
%compare-strncmp
%enum
%pic
%struct-type

struct kconf_id;

static struct kconf_id *kconf_id_lookup(register const char *str, register unsigned int len);

%%
mainmenu,	T_MAINMENU,	TF_COMMAND
menu,		T_MENU,		TF_COMMAND
endmenu,	T_ENDMENU,	TF_COMMAND
source,		T_SOURCE,	TF_COMMAND
choice,		T_CHOICE,	TF_COMMAND
endchoice,	T_ENDCHOICE,	TF_COMMAND
comment,	T_COMMENT,	TF_COMMAND
config,		T_CONFIG,	TF_COMMAND
menuconfig,	T_MENUCONFIG,	TF_COMMAND
help,		T_HELP,		TF_COMMAND
if,		T_IF,		TF_COMMAND|TF_PARAM
endif,		T_<PERSON>ND<PERSON>,	TF_COMMAND
depends,	T_DEPENDS,	TF_COMMAND
optional,	T_OPTION<PERSON>,	TF_COMMAND
default,	T_DEFAULT,	TF_COMMAND, S_UNKNOWN
prompt,		T_PROMPT,	TF_COMMAND
tristate,	T_TYPE,		TF_COMMAND, S_TRISTATE
def_tristate,	T_DEFAULT,	TF_COMMAND, S_TRISTATE
bool,		T_TYPE,		TF_COMMAND, S_BOOLEAN
boolean,	T_TYPE,		TF_COMMAND, S_BOOLEAN
def_bool,	T_DEFAULT,	TF_COMMAND, S_BOOLEAN
int,		T_TYPE,		TF_COMMAND, S_INT
hex,		T_TYPE,		TF_COMMAND, S_HEX
string,		T_TYPE,		TF_COMMAND, S_STRING
select,		T_SELECT,	TF_COMMAND
range,		T_RANGE,	TF_COMMAND
visible,	T_VISIBLE,	TF_COMMAND
option,		T_OPTION,	TF_COMMAND
on,		T_ON,		TF_PARAM
modules,	T_OPT_MODULES,	TF_OPTION
defconfig_list,	T_OPT_DEFCONFIG_LIST,TF_OPTION
env,		T_OPT_ENV,	TF_OPTION
%%
