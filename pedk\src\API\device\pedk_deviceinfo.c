#include <string.h>
#include <stdio.h>
#include "pedk_deviceinfo.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define NVLEN_VERSION           16
#define NVLEN_LVERSION          32
#define countof(x)              (sizeof(x) / sizeof((x)[0]))

JSValue js_get_dcfw_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[NVLEN_VERSION];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_DCFWVERSION, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_DCFWVERSION, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :DC FW version[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_get_ecfw_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[NVLEN_LVERSION];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_ECFWVERSION, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_ECFWVERSION, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :EC FW version[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_get_fpga1_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[NVLEN_LVERSION];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA1VERSION, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA1VERSION, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :FPGA1 version[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_get_fpga2_version(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    unsigned char       receive_data[NVLEN_LVERSION];
    int32_t             receive_cnt = sizeof(receive_data);
    int32_t respond = -1;

    SendMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA2VERSION, 0, 0, NULL);
    RecvMsgToMfp(MSG_MODULE_PLATFORM, MSG_PLATFORM_SUB_DEVICEINFO_GET_FPGA2VERSION, &respond, receive_data, &receive_cnt, 3);

    printf("[pedk-productinfo] recv from mfp datat :DC FPGA2 version[%s]\n", receive_data);

    if ( respond < 0 )
    {
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    else
    {
        return JS_NewString(ctx, receive_data);
    }
}

JSValue js_setting_getCTonerRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_TONER_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_TONER_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}

JSValue js_setting_getMTonerRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_TONER_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_TONER_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}

JSValue js_setting_getYTonerRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_TONER_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_TONER_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}

JSValue js_setting_getKTonerRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_TONER_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_TONER_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}

JSValue js_setting_getCDrumModuleModel(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char model[32] = {0};

    len = sizeof(model);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_DRUM_MODEL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_DRUM_MODEL, &respone,model,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,model = %s\n",__LINE__,__func__,model);

    return JS_NewString(ctx, model);
}


JSValue js_setting_getMDrumModuleModel(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char model[32] = {0};

    len = sizeof(model);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_DRUM_MODEL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_DRUM_MODEL, &respone,model,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,model = %s\n",__LINE__,__func__,model);

    return JS_NewString(ctx, model);
}

JSValue js_setting_getYDrumModuleModel(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char model[32] = {0};

    len = sizeof(model);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_DRUM_MODEL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_DRUM_MODEL, &respone,model,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,model = %s\n",__LINE__,__func__,model);

    return JS_NewString(ctx, model);
}


JSValue js_setting_getKDrumModuleModel(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char model[32] = {0};

    len = sizeof(model);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_DRUM_MODEL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_DRUM_MODEL, &respone,model,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,model = %s\n",__LINE__,__func__,model);

    return JS_NewString(ctx, model);
}


JSValue js_setting_getCDrumModuleRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_DRUM_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_C_DRUM_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}


JSValue js_setting_getMDrumModuleRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_DRUM_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_M_DRUM_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}


JSValue js_setting_getYDrumModuleRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_DRUM_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_Y_DRUM_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}


JSValue js_setting_getKDrumModuleRemainVal(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char remain_val[10] = {0};

    len = sizeof(remain_val);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_DRUM_REMAIN_VAL, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_K_DRUM_REMAIN_VAL, &respone,remain_val,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,remain_val = %s\n",__LINE__,__func__,remain_val);

    return JS_NewString(ctx, remain_val);
}


JSValue js_setting_getDeviceAverageCoverageRate(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int len = 0;
    int respone = -1;
    char coverage_rate[10] = {0};

    len = sizeof(coverage_rate);
    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_AVERAGE_COVERAGE_RATE, 0, 0, NULL);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s\n",__LINE__,__func__);

    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_AVERAGE_COVERAGE_RATE, &respone,coverage_rate,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,coverage_rate = %s\n",__LINE__,__func__,coverage_rate);

    return JS_NewString(ctx, coverage_rate);
}


typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pedk_deviceinfo_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    {"get_dcfw_version",            0,  js_get_dcfw_version},
    {"get_Ecfw_version",            0,  js_get_ecfw_version},
    {"get_fpga1_version",           0,  js_get_fpga1_version},
    {"get_fpga2_version",           0,  js_get_fpga2_version},
    {"get_CToner_RemainVal",        0,  js_setting_getCTonerRemainVal},
    {"get_MToner_RemainVal",        0,  js_setting_getMTonerRemainVal},
    {"get_YToner_RemainVal",        0,  js_setting_getYTonerRemainVal},
    {"get_KToner_RemainVal",        0,  js_setting_getKTonerRemainVal},
    {"get_CDrumModule_Model",       0,  js_setting_getCDrumModuleModel},
    {"get_MDrumModule_Model",       0,  js_setting_getMDrumModuleModel},
    {"get_YDrumModule_Model",       0,  js_setting_getYDrumModuleModel},
    {"get_KDrumModule_Model",       0,  js_setting_getKDrumModuleModel},
    {"get_CDrumModule_RemainVal",   0,  js_setting_getCDrumModuleRemainVal},
    {"get_MDrumModule_RemainVal",   0,  js_setting_getMDrumModuleRemainVal},
    {"get_YDrumModule_RemainVal",   0,  js_setting_getYDrumModuleRemainVal},
    {"get_KDrumModule_RemainVal",   0,  js_setting_getKDrumModuleRemainVal},
    {"get_Device_AverageCoverageRate",0,  js_setting_getDeviceAverageCoverageRate},
};

const JSCFunctionList* device_deviceinfo_JSCFunctionList(int *length)
{
    *length = countof(pedk_deviceinfo_funcs);
    return pedk_deviceinfo_funcs;
}

int js_device_deviceinfo_init(JSContext *ctx, JSValueConst global)
{
   printf("*********start device setting device info module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pedk_funcs = device_deviceinfo_JSCFunctionList(&count);
   printf("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pedk_funcs[i].name,
                           JS_NewCFunction(ctx, pedk_funcs[i].func, pedk_funcs[i].name, pedk_funcs[i].length));

   }
   printf("*********start device setting device info init end**********\n");
   return 0;
}

