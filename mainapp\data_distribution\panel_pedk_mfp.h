
#ifndef __PANEL_PEDK_MFP_H__
#define __PANEL_PEDK_MFP_H__


#include <string.h>
#include <stdlib.h>
#include "cmd.h"
#include "pol/pol_threads.h"
#include "msgrouter_main.h"
#include "utilities/msgrouter.h"
#include "pol/pol_log.h"
#include "event_manager/event_msg_typedef.h"
#include "pol/pol_string.h"
#include "public_data_proc.h"
#include "cmd.h"
//#include "pesf_mfp_server.h"

#define PEDK_APP_INFO_LEN       256

typedef struct
{
    char app_name[PEDK_APP_INFO_LEN];
    char app_id[PEDK_APP_INFO_LEN];
    char app_icon[PEDK_APP_INFO_LEN];
}PEDK_APP_INFO_S;

typedef enum
{    
    PEDK_APP_REMOVE = 0,
    PEDK_APP_INSTALL = 1,
}PEDK_APP_INSTALL_MODE_E;

typedef enum
{
    PEDK_APP_OP_FAILURE = -1,    // 安装/卸载失败
    PEDK_APP_OP_SUCCESS = 0,     // 安装/卸载成功
} PEDK_APP_OPERATION_RESULT;

typedef struct
{
    PEDK_APP_INSTALL_MODE_E mode;
    PEDK_APP_OPERATION_RESULT result; 
    char app_name[PEDK_APP_INFO_LEN];
}PEDK_APP_INSTALL_INFO_S;

void panel_pedk_deinit();

int32_t panel_pedk_init();

/**
 * @brief process pedk app install/uninstall msg
 * @param[in] is_install 0-install, 1-uninstall
 * @param[in] result 0-success, 1-fail
 * @author: madechang
 */
void panel_recv_app_install_msg( uint32_t is_install, uint32_t* data, uint32_t data_len );

void panel_update_pedk_install_info();

void panel_pedk_function_switch(void* function_switch, int size);

/**
 * @brief pedk 2.0 app install process
 * @param[in] install_mode 0-install, 1-uninstall
 * @param[in] result 0-success, -1-fail
 * @param[in] app_name app name
 * @author: madechang
 */
void pedk_install_proc( PEDK_APP_INSTALL_MODE_E install_mode, PEDK_APP_OPERATION_RESULT result, char* app_name );


    #endif

