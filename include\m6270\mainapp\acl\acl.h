/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file acl.h
 * @addtogroup acl
 * @{
 * <AUTHOR> xian chang
 * @date 2023-4-25
 * @brief ACL External prototypes and command defines\n
    ACL is a simple control language that provides access to many internal fw \n
    functions.  ACL consists of a command block of 16 bytes followed by optional \n
    data.  The response consists of a response block of 16 bytes followed by \n
    optional data.  All acl commands will follow this response format.  Within \n
    the command and response blocks are a 2 byte header of 0x00ac, and a command id.
 */

#ifndef ACL_API_H
#define ACL_API_H

//#include "arch.h"
#include "qio/qio_general.h"
//#include "type.h"

#ifdef __cplusplus
extern "C" {
#endif

#define START                           1               ///< yes or no of downloading firmware(yes)
#define CANCEL                          2               ///< yes or no of downloading firmware(no)
#define START_UPDATE_BASE               3               ///< yes or no of updating base value(yes)
#define CANCEL_UPDATE_BASE              4               ///< yes or no of updating base value(no)

/* acl cmd results */
#define ACLCMD_STATUS_FAILURE   0x0000
#define ACLCMD_STATUS_SUCCESS   0x0001

//add for security
#define ACL_OPERATOR_TYPE_GET_PARA		0x0010
#define ACL_OPERATOR_TYPE_SET_PARA		0x00FF

#define LEN_ACL_CMD 16 /* < The length of the command and response blocks */
#define ACL_CMD_BASE_CLASS_MEMBERS \
	uint16_t prefix; /* The acl prefix 0xAC */ \
    uint16_t cmd_id  /* This is the acl cmd to execute . */


#define ACL_RESPONSE_BASE_CLASS_MEMBERS \
	uint16_t prefix;   /* The acl prefix --- 0xac */ \
	uint16_t cmd_id;   /* This is the acl cmd we are responding to */ \
	int16_t cmd_status /* This is the status of the command. */

/**
 * @brief Base class defines for acl cmds
 * This is the cmd block that is received from the host to initiate an acl cmd.
 * This struct is the base class, each acl cmd can use the last 12 bytes in
 * any way they choose.  This usage is defined in each module that implements
 * that acl cmd.
 * The acl commands are always 16 bytes long.  When this is included 4 bytes are
 * used.  12 more bytes must exist at the end of the structure.
 */
#pragma pack(1)
typedef struct
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint8_t temp[12];                      ///< has to be expanded to 16 bytes total, this does that
} ACL_CMD_BASE_STRUCT_S;


/**
 * @brief Base class defines for acl responses
 * The acl commands are always 16 bytes long.  When this is included 8 bytes
 * are used.  This must be extended by 8 bytes to the full length.
 */
typedef struct
{
    ACL_RESPONSE_BASE_CLASS_MEMBERS;        ///< define the base items
    uint16_t data_len;                      ///< this is the length of the response data
    uint8_t temp[8];                        ///< has to be expanded to 16 bytes total, this does that
} ACL_RESPONSE_BASE_STRUCT_S;

typedef struct
{
    ACL_RESPONSE_BASE_CLASS_MEMBERS; /* < define the base items */
    uint16_t data_len;               /* < this is the length of the response data */
    uint8_t temp[8];                 /* < has to be expanded to 16 bytes total, this does that */
    uint8_t getdata[53];
} ACL_RESPONSE_ALL_SETTING_STRUCT_S;

typedef struct                       // 16 bytes
{
    ACL_CMD_BASE_CLASS_MEMBERS;
    uint16_t data_length;             //Data length
    uint8_t encodeflag;
    uint8_t tempdata[9];               //unused data
} ACL_GET_PRINTER_ATTRIBUTE_REQUEST_CMD_S;

//add for security
typedef struct
{
	ACL_CMD_BASE_CLASS_MEMBERS;
	uint16_t  operator_type;
    uint8_t  temp[10];
}ACL_PARAMETER_OPERATE_S;
#pragma pack()

#pragma pack(1)

typedef struct
{
    ACL_RESPONSE_BASE_CLASS_MEMBERS;        ///< define the base items
    uint32_t data_len;                        ///< this is the length of the response data
    uint8_t temp[6];                          ///< has to be expanded to 16 bytes total, this does that
} ACL_RESPONSE_LOG_STRUCT_S;
//add for security end

#pragma pack()
/**
 * @brief Define the form of an acl command function calling sequence.
 * When an acl command is registered you pass in the command value, and the function
 * that is used to execute the acl command.  This typedef defines the calling
 * sequence for that function.
 * @param[in] pgqio, The io stream on which to respond
 * @param[in] acl_cmd Pointer to the received acl command block
 * @param[in] cmd_data This is a pointer to the data that was passed into the acl parser
 *              during registration.
 * @return uint32
 * retval 0 for success
 * retval != 0 for failure.
 */
typedef int32_t (*acl_function_t)(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, void *cmd_data);

/**
 * @brief Register an acl command with the acl system.
 * This function takes and makes a one-to-one mapping between the acl_cmd_value
 * and the acl_callback function.  When an acl command is received of the acl_cmd_value
 * the acl_callback is called to execute the function.  If an acl_cmd_value
 * already exists this function will return.
 * @param[in] acl_cmd_value The acl command function.  Fits into categories listed above
 * @param[in] acl_callback The function to be called when the acl command is received.
 *                          This does the cmd.
 * @param[in] cmd_data Whatever is here will be passed to the acl function when initiated.
 * @return uint32
 * retval 0 for success
 * retval != 0 for failure.
 */
int32_t acl_register_cmd(uint32_t acl_cmd_value, acl_function_t acl_callback , void *cmd_data);
/**
 * @brief acl parser prolog function.
 */
int32_t acl_parser_prolog( void );
/**
 * @brief acl parser epilog function.
 */
int32_t acl_parser_epilog( void );

/**
 * @brief set upgrade status from panel to dc.
 */
void set_fw_upgrade_status(int status);

/**
 * @brief get upgrade status from panel.
 */
int get_fw_upgrade_status(void);

/* FUNCTION NAME: acl_direct_response*/
/**
 * @brief after process an acl command, construct acl response cmd, and you can response status and data to terminate.
 * This function response an acl status to terminate  .
 * @param[in] PQIO:io stream, send data to this io stream.
 * @param[in] ACL_CMD_BASE_STRUCT_S *aclCmd: the acl cmd which you processed.
 * @param[in] uint16_t status: the status which you process the acl cmd.
 * @param[in] uint8_t *responseData: the data which you want to response.
 * @param[in] uint32_t dataLen: the response data length.
 * @author:jacky zeng
 **/
void acl_direct_response(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, int16_t status, uint8_t *response_data, uint32_t data_len);

/* FUNCTION NAME: acl_response*/

/**
 * @brief send response cmd and data to terminate.
 * This function send response cmd and data to terminate  .
 * @param[in] PQIO:io stream, send data to this io stream.
 * @param[in] ACL_RESPONSE_BASE_STRUCT_S *Buffer: the response cmd
 * @param[in] uint8_t *responseData: the data which you want to response.
 * @param[in] uint32_t dataLen: the response data length.
 * @author:jacky zeng
 **/
void acl_response(GQIO_S* pgqio, ACL_RESPONSE_BASE_STRUCT_S *buffer, uint8_t *response_data, uint32_t data_len);

/**
 * brief A helper function to construct the response buffer and do some init.
 * The response buffer is 16 bytes and consists of at least a status, id, and command.
 * This function allocates the buffer, initalizes the status to success, initializes
 * the id to 0xac, and sets the command according to acl_cmd.  Then it returns
 * the buffer.  Note that if no memory is available this will suspend till it
 * is able to allocate the buffer.
 * param[in] acl_cmd The acl_cmd to put into the response structure.
 * returns ACL_RESPONSE_BASE_STRUCT_S pointer to the initialized structure
 */

/**
 * @brief  after process an acl command, construct acl response cmd, and you can response status and data to terminate.
 * @param[in] GQIO_S* pgqio : io stream, send data to this io stream.
 * @param[in] ACL_CMD_BASE_STRUCT_S *acl_cmd : the acl cmd which you processed.
 * @param[in] int16_t status : the status which you process the acl cmd.
 * @param[in] uint8_t *response_data : the data which you want to response.
 * @param[in] uint32_t data_len : the response data length.
 * @param[out] Output : NULL
 * @return uint32_t value
 * <AUTHOR>
*/
void acl_merge_response(GQIO_S* pgqio, ACL_CMD_BASE_STRUCT_S *acl_cmd, int16_t status, uint8_t *response_data, uint32_t data_len);


ACL_RESPONSE_BASE_STRUCT_S *acl_construct_response_buffer(uint16_t acl_cmd);

/**
 * @brief  return the status of the upgrade status
 * @return int value
 * <AUTHOR>
*/
int get_fw_upgrade_status(void);


#ifdef __cplusplus
}
#endif

#endif
/**
 *@}
 */
