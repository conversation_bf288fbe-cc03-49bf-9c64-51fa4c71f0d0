/**
* @copyright 2025 Zhuhai Pantum Technology Co.Ltd all rights reserved
* @file moduleid.h
* @addtogroup nvram
* @{
* @addtogroup nvtable
* @brief Each module customizes the id value of the enumeration 
*          constant,and it is forbidden to modify the file directly
* <AUTHOR> Tool(Automatic generated)
* @version 1.0
* @date 2025-05-19
*/

#ifndef __MODULEID_H
#define __MODULEID_H

typedef enum
{
	COPY_ID_WHITE_MASK_LEFT = 40000 ,
	COPY_ID_WHITE_MASK_RIGHT = 40001 ,
	COPY_ID_WHITE_MASK_TOP = 40002 ,
	COPY_ID_WHITE_MASK_BOTTOM = 40003 ,
	COPY_ID_WHITE_MASK_MIDDLE = 40004 ,
	COPY_ID_MARGIN_LEFT = 40005 ,
	COPY_ID_MARGIN_TOP = 40006 ,
	COPY_ID_TOTAL_PAGES_COUNT = 40007 ,
	COPY_ID_A4_PAGES_COUNT = 40008 ,
	COPY_ID_A5_PAGES_COUNT = 40009 ,
	COPY_ID_A6_PAGES_COUNT = 40010 ,
	COPY_ID_B4_PAGES_COUNT = 40011 ,
	COPY_ID_B5_PAGES_COUNT = 40012 ,
	COPY_ID_B6_PAGES_COUNT = 40013 ,
	COPY_ID_LETTER_PAGES_COUNT = 40014 ,
	COPY_ID_LEGAL_PAGES_COUNT = 40015 ,
	COPY_ID_OFICIO_PAGES_COUNT = 40016 ,
	COPY_ID_8K_PAGES_COUNT = 40017 ,
	COPY_ID_16K_PAGES_COUNT = 40018 ,
	COPY_ID_32K_PAGES_COUNT = 40019 ,
	COPY_ID_OTHERS_PAGES_COUNT = 40020 ,
	COPY_ID_REDUCE_ENLARGE = 40021 ,
	COPY_ID_REDUCE_ENLARGE_PERCENT = 40022 ,
	COPY_ID_QUALITY = 40023 ,
	COPY_ID_LUMINOSITY = 40024 ,
	COPY_ID_DEFAULT_MEDIA_SIZE = 40025 ,
	COPY_ID_DEFAULT_MEDIA_TYPE = 40026 ,
	COPY_ID_IDCOPY_LUMINOSITY = 40027 ,
	COPY_ID_IDCOPY_DEFAULT_MEDIA_SIZE = 40028 ,
	COPY_ID_BILL_LUMINOSITY = 40029 ,
	COPY_ID_BILL_DEFAULT_MEDIA_SIZE = 40030 ,
	COPY_ID_DEFAULT_COLOR_MODE = 40031 ,
	COPY_ID_IDCOPY_COLOR_MODE = 40032 ,
	COPY_ID_BILL_COLOR_MODE = 40033 ,
	COPY_ID_BRIGHTNESS_ADJUST_VAL = 40034 ,
	COPY_ID_CONTRAST_ADJUST_VAL = 40035 ,
	COPY_ID_SATURAB_ADJUST_VAL = 40036 ,
	COPY_ID_RED_ADJUST_VAL = 40037 ,
	COPY_ID_GREEN_ADJUST_VAL = 40038 ,
	COPY_ID_BLUE_ADJUST_VAL = 40039 ,
	COPY_ID_COLOR_SCHEME_ADJUST_VAL = 40040 ,
	COPY_ID_COPY_CONCENTRATION_ADJUST_VAL = 40041 ,
	COPY_ID_ACUTE_ADJUST_VAL = 40042 ,
	COPY_ID_COLOR_BALANCE_YELLOW_VAL = 40043 ,
	COPY_ID_COLOR_BALANCE_RED_VAL = 40044 ,
	COPY_ID_COLOR_BALANCE_CYAN_VAL = 40045 ,
	COPY_ID_COLOR_BALANCE_BLACK_VAL = 40046 ,
	COPY_ID_MAGIN_REMOVE_ONOFF = 40047 ,
	COPY_ID_PAGE_MAGIN_ONOFF = 40048 ,
	COPY_ID_NORMAL_COPY_SHADE_ONOFF = 40049 ,
	COPY_ID_ID_COPY_SHADE_ONOFF = 40050 ,
	COPY_ID_BIIL_COPY_SHADE_ONOFF = 40051 ,
	COPY_ID_JOB_CONTROL_ENABLE = 40052 
}COPY_ID_E;

typedef enum
{
	IMAGE_ID_ENGINE_SCREEN_CURVE = 70000 ,
	IMAGE_ID_ENGINE_ED_CURVE = 70001 
}IMAGE_ID_E;

typedef enum
{
	NETWORK_ID_FACTORY_STATUS = 20000 ,
	NETWORK_ID_IFACE_SWITCH = 20001 ,
	NETWORK_ID_IPV6_SWITCH = 20002 ,
	NETWORK_ID_IPV6_USEDHCP = 20003 ,
	NETWORK_ID_IPV4_USEDHCP = 20004 ,
	NETWORK_ID_IPV4_AUTODNS = 20005 ,
	NETWORK_ID_IPV4_ADDR = 20006 ,
	NETWORK_ID_IPV4_MASK = 20007 ,
	NETWORK_ID_IPV4_GTWY = 20008 ,
	NETWORK_ID_IPV4_DNS0 = 20009 ,
	NETWORK_ID_IPV4_DNS1 = 20010 ,
	NETWORK_ID_STA_SEC_MODE = 20011 ,
	NETWORK_ID_STA_SSID = 20012 ,
	NETWORK_ID_STA_PSK = 20013 ,
	NETWORK_ID_WFD_SSID_PREFIX = 20014 ,
	NETWORK_ID_WFD_SSID_SUFFIX = 20015 ,
	NETWORK_ID_WFD_PSK = 20016 ,
	NETWORK_ID_HOSTNAME = 20017 ,
	NETWORK_ID_DOMAIN = 20018 ,
	NETWORK_ID_UUID = 20019 ,
	NETWORK_ID_WEB_USERNAME = 20020 ,
	NETWORK_ID_WEB_PASSWORD = 20021 ,
	NETWORK_ID_WEB_SEC_PSWD = 20022 ,
	NETWORK_ID_WEB_AUD_PSWD = 20023 ,
	NETWORK_ID_WEB_SYS_PSWD = 20024 ,
	NETWORK_ID_RAWPRINT_SWITCH = 20025 ,
	NETWORK_ID_RAWPRINT_PORT = 20026 ,
	NETWORK_ID_RAWSCAN_SWITCH = 20027 ,
	NETWORK_ID_RAWSCAN_PORT = 20028 ,
	NETWORK_ID_LPD_SWITCH = 20029 ,
	NETWORK_ID_WSD_SWITCH = 20030 ,
	NETWORK_ID_SLP_SWITCH = 20031 ,
	NETWORK_ID_BONJOUR_SWITCH = 20032 ,
	NETWORK_ID_BONJOUR_SERVER = 20033 ,
	NETWORK_ID_IPP_SWITCH = 20034 ,
	NETWORK_ID_SNMP_SWITCH = 20035 ,
	NETWORK_ID_SNMP_VERSION_FLAGS = 20036 ,
	NETWORK_ID_SNMP_V1_COMMUNITY = 20037 ,
	NETWORK_ID_SNMP_V2_COMMUNITY = 20038 ,
	NETWORK_ID_SNMP_V3_COMMUNITY = 20039 ,
	NETWORK_ID_SNMP_V3_USER_NAME = 20040 ,
	NETWORK_ID_SNMP_V3_AUTH_PSWD = 20041 ,
	NETWORK_ID_SNMP_V3_PRIV_PSWD = 20042 ,
	NETWORK_ID_SMB_NTLMV1_SWITCH = 20043 ,
	NETWORK_ID_SMB_NTLM_AUTO = 20044 ,
	NETWORK_ID_SMTP_SENDER_ADDR = 20045 ,
	NETWORK_ID_SMTP_SERVER_ADDR = 20046 ,
	NETWORK_ID_SMTP_SERVER_PORT = 20047 ,
	NETWORK_ID_SMTP_SERVER_AUTH = 20048 ,
	NETWORK_ID_SMTP_SEC_MODE = 20049 ,
	NETWORK_ID_SMTP_USERNAME = 20050 ,
	NETWORK_ID_SMTP_PASSWORD = 20051 ,
	NETWORK_ID_ALARM_CLIENT_ADDR1 = 20052 ,
	NETWORK_ID_ALARM_CLIENT_ADDR2 = 20053 ,
	NETWORK_ID_ALARM_CLIENT_ADDR3 = 20054 ,
	NETWORK_ID_ALARM_CLIENT_ADDR4 = 20055 ,
	NETWORK_ID_ALARM_STATUS_FLAGS = 20056 ,
	NETWORK_ID_MAIL_GROUPLIST = 20057 ,
	NETWORK_ID_MAIL_ADDRBOOK = 20058 ,
	NETWORK_ID_FTP_ADDRBOOK = 20059 ,
	NETWORK_ID_SMB_ADDRBOOK = 20060 ,
	NETWORK_ID_AIRPRINT_USERLIST = 20061 ,
	NETWORK_ID_PRINTER_LONGITUDE = 20062 ,
	NETWORK_ID_PRINTER_LATITUDE = 20063 ,
	NETWORK_ID_TLS_PRIV_PSWD = 20064 ,
	NETWORK_ID_TLS_PRIV_CERT = 20065 ,
	NETWORK_ID_TLS_CA_PKEY = 20066 ,
	NETWORK_ID_TLS_CA_CERT = 20067 ,
	NETWORK_ID_WATER_MARK1 = 20068 ,
	NETWORK_ID_WATER_MARK2 = 20069 ,
	NETWORK_ID_WATER_MARK3 = 20070 ,
	NETWORK_ID_WATER_MARK4 = 20071 ,
	NETWORK_ID_WATER_MARK5 = 20072 ,
	NETWORK_ID_WATER_MARK6 = 20073 ,
	NETWORK_ID_COPY_AUDIT_SWITCH = 20074 ,
	NETWORK_ID_COPY_AUDIT_SERVER = 20075 ,
	NETWORK_ID_SEC_ALERT_SWITCH = 20076 ,
	NETWORK_ID_SEC_ALERT_UNIT = 20077 ,
	NETWORK_ID_SEC_ALERT_DEPARTMENT = 20078 ,
	NETWORK_ID_SEC_ALERT_OWNER = 20079 ,
	NETWORK_ID_SEC_ALERT_SERVER1 = 20080 ,
	NETWORK_ID_SEC_ALERT_SERVER2 = 20081 ,
	NETWORK_ID_WIRED_SPEED = 20082 ,
	NETWORK_ID_WHITELIST_SWITCH = 20083 ,
	NETWORK_ID_WHITELIST_ADDRBOOK = 20084 
}NETWORK_ID_E;

typedef enum
{
	PLATFORM_ID_PLATFORM_PRODUCT_MODEL = 10000 ,
	PLATFORM_ID_AUTO_SHUTDOWN_ENABLED = 10001 ,
	PLATFORM_ID_AUTO_SHUTDOWN_TIME = 10002 ,
	PLATFORM_ID_AUTO_SHUTDOWN_CONDITION = 10003 ,
	PLATFORM_ID_POWER_ON_COUNT = 10004 ,
	PLATFORM_ID_LCD_BACKLIGHT_SET = 10005 ,
	PLATFORM_ID_VOLUME_SET = 10006 ,
	PLATFORM_ID_SLEEP_TIME = 10007 ,
	PLATFORM_ID_COUNTRY_CODE = 10008 ,
	PLATFORM_ID_LANGUAGE_CODE = 10009 ,
	PLATFORM_ID_REGION_CODE = 10010 ,
	PLATFORM_ID_QUIET = 10011 ,
	PLATFORM_ID_RESTORE_FLAG = 10012 ,
	PLATFORM_ID_TIMEZONE = 10013 ,
	PLATFORM_ID_DATE_FORMAT = 10014 ,
	PLATFORM_ID_TIME_FORMAT = 10015 ,
	PLATFORM_ID_AIRPRINT_EN_ON_USB = 10016 ,
	PLATFORM_ID_PORTLIMIT_USB = 10017 ,
	PLATFORM_ID_PRODUCT_SERIAL_NUM = 10018 ,
	PLATFORM_ID_LOCATION = 10019 ,
	PLATFORM_ID_PRODUCT_DATE = 10020 ,
	PLATFORM_ID_BATCH_NUMBER = 10021 ,
	PLATFORM_ID_CONTACT_INFO = 10022 ,
	PLATFORM_ID_PROPERTY_NUMBER = 10023 ,
	PLATFORM_ID_STORAGE_CLEAR = 10024 ,
	PLATFORM_ID_DELAYCANCEL_TIMECOUNT = 10025 ,
	PLATFORM_ID_IO_TIMEOUT = 10026 ,
	PLATFORM_ID_CALIBRATION_QRCODE = 10027 ,
	PLATFORM_ID_SYSTEM_ENERGY_CODE = 10028 ,
	PLATFORM_ID_ACRFACTORY = 10029 ,
	PLATFORM_ID_CALIBRATION = 10030 ,
	PLATFORM_ID_TR2MODESET = 10031 ,
	PLATFORM_ID_FIRST_SLEEP_TIME = 10032 ,
	PLATFORM_ID_SECOND_SLEEP_TIME = 10033 ,
	PLATFORM_ID_FIRMWARE_VERSION = 10034 ,
	PLATFORM_ID_ENG_FW_VERSION = 10035 ,
	PLATFORM_ID_PANEL_FW_VERSION = 10036 ,
	PLATFORM_ID_PRINT_NAME_STRING = 10037 ,
	PLATFORM_ID_SYSTEM_TIME1 = 10038 ,
	PLATFORM_ID_PLATFORM_CONSUMABLE_ALGO_VERSION = 10039 ,
	PLATFORM_ID_PLATFORM_FW_Flag = 10040 ,
	PLATFORM_ID_PLATFORM_CONSUMABLE_SN_BLACKLIST_VERSION = 10041 ,
	PLATFORM_ID_PLATFORM_FW_UPGRADE_WAY = 10042 ,
	PLATFORM_ID_SYSTEM_TIME = 10043 ,
	PLATFORM_ID_UDISK_ENABLE = 10044 ,
	PLATFORM_ID_SOLE_SERIAL_NUMBER = 10045 ,
	PLATFORM_ID_SOLE_SERIAL_LOCK_FLAG = 10046 ,
	PLATFORM_ID_ENG_BOARD_SIGN_VALUE = 10047 ,
	PLATFORM_ID_SEC_ENG_MACHINE_INFO = 10048 ,
	PLATFORM_ID_SCAN_ENG_FW_VERSION = 10049 ,
	PLATFORM_ID_FPGA_FRONT_VERSION = 10050 ,
	PLATFORM_ID_FPGA_BACK_VERSION = 10051 ,
	PLATFORM_ID_M6220_FRONT_VERSION = 10052 ,
	PLATFORM_ID_M6220_BACK_VERSION = 10053 ,
	PLATFORM_ID_SATASSD1_ENABLE = 10054 ,
	PLATFORM_ID_SATASSD1_ENCRYPT_ENABLE = 10055 ,
	PLATFORM_ID_USBDISK_DISABLE = 10056 ,
	PLATFORM_ID_SATASSD1_COMPAT_ENABLE = 10057 ,
	PLATFORM_ID_SATASSD1_SERIAL_NUMBER = 10058 ,
	PLATFORM_ID_SSD_SUPPORT_MODEL = 10059 ,
	PLATFORM_ID_LOG_LEVEL = 10060 ,
	PLATFORM_ID_LOG_MODE = 10061 
}PLATFORM_ID_E;

typedef enum
{
	PRINT_ID_INPUT_TRAY_DEFAULT_MEDIA_SIZE = 50000 ,
	PRINT_ID_INPUT_TRAY_MEDIA_PROMPT = 50001 ,
	PRINT_ID_INPUT_TRAY_DEFAULT_MEDIA_TYPE = 50002 ,
	PRINT_ID_INPUT_TRAY_STATUS = 50003 ,
	PRINT_ID_INPUT_TRAY_NUMBER = 50004 ,
	PRINT_ID_INPUT_TRAY_DEFAULT_COPYTO = 50005 ,
	PRINT_ID_OVERTURN_UNIT_TYPE = 50006 ,
	PRINT_ID_SIMPLEX_PAGES = 50007 ,
	PRINT_ID_DUPLEX_PAGES = 50008 ,
	PRINT_ID_TOTAL_JOB_COUNT = 50009 ,
	PRINT_ID_TOTAL_PAGE_COUNT = 50010 ,
	PRINT_ID_A5_PAGE_COUNT = 50011 ,
	PRINT_ID_A4_LETTER_PAGE_COUNT = 50012 ,
	PRINT_ID_LEGAL_FOLIO_PAGE_COUNT = 50013 ,
	PRINT_ID_B5_EXECUTIVE_PAGE_COUNT = 50014 ,
	PRINT_ID_B6_PAGE_COUNT = 50015 ,
	PRINT_ID_OTHER_PAGE_COUNT = 50016 ,
	PRINT_ID_PAGES_MONO_COUNT = 50017 ,
	PRINT_ID_PAGES_COLOR_COUNT = 50018 ,
	PRINT_ID_USE_PRINTER_CONFIG = 50019 ,
	PRINT_ID_INPUT_MULTIFUNCTION_TRAY_DEFAULT_MEDIA_SIZE = 50020 ,
	PRINT_ID_INPUT_MULTIFUNCTION_TRAY_DEFAULT_MEDIA_TYPE = 50021 ,
	PRINT_ID_INPUT_TRAY1_STATUS = 50022 ,
	PRINT_ID_INPUT_STANDARD_TRAY_DEFAULT_MEDIA_SIZE = 50023 ,
	PRINT_ID_INPUT_EXTENDED_TRAY1_DEFAULT_MEDIA_SIZE = 50024 ,
	PRINT_ID_INPUT_EXTENDED_TRAY2_DEFAULT_MEDIA_SIZE = 50025 ,
	PRINT_ID_INPUT_EXTENDED_TRAY3_DEFAULT_MEDIA_SIZE = 50026 ,
	PRINT_ID_INPUT_EXTENDED_TRAY4_DEFAULT_MEDIA_SIZE = 50027 ,
	PRINT_ID_INPUT_EXTENDED_TRAY5_DEFAULT_MEDIA_SIZE = 50028 ,
	PRINT_ID_INPUT_EXTENDED_TRAY6_DEFAULT_MEDIA_SIZE = 50029 ,
	PRINT_ID_INPUT_STANDARD_TRAY_DEFAULT_MEDIA_TYPE = 50030 ,
	PRINT_ID_INPUT_EXTENDED_TRAY1_DEFAULT_MEDIA_TYPE = 50031 ,
	PRINT_ID_INPUT_EXTENDED_TRAY2_DEFAULT_MEDIA_TYPE = 50032 ,
	PRINT_ID_INPUT_EXTENDED_TRAY3_DEFAULT_MEDIA_TYPE = 50033 ,
	PRINT_ID_INPUT_EXTENDED_TRAY4_DEFAULT_MEDIA_TYPE = 50034 ,
	PRINT_ID_INPUT_EXTENDED_TRAY5_DEFAULT_MEDIA_TYPE = 50035 ,
	PRINT_ID_INPUT_EXTENDED_TRAY6_DEFAULT_MEDIA_TYPE = 50036 ,
	PRINT_ID_MULTIFUNCTION_INPUT_TRAY_MEDIA_PROMPT = 50037 ,
	PRINT_ID_STANDARD_INPUT_TRAY_MEDIA_PROMPT = 50038 ,
	PRINT_ID_EXTENDED_INPUT_TRAY1_MEDIA_PROMPT = 50039 ,
	PRINT_ID_EXTENDED_INPUT_TRAY2_MEDIA_PROMPT = 50040 ,
	PRINT_ID_EXTENDED_INPUT_TRAY3_MEDIA_PROMPT = 50041 ,
	PRINT_ID_EXTENDED_INPUT_TRAY4_MEDIA_PROMPT = 50042 ,
	PRINT_ID_EXTENDED_INPUT_TRAY5_MEDIA_PROMPT = 50043 ,
	PRINT_ID_EXTENDED_INPUT_TRAY6_MEDIA_PROMPT = 50044 ,
	PRINT_ID_INTERNAL_PAGE = 50045 ,
	PRINT_ID_FINGERPRINT_ENABLE = 50046 ,
	PRINT_ID_FINGERPRINT_MODULE = 50047 ,
	PRINT_ID_FINGERPRINT_MODULE_TYPE = 50048 ,
	PRINT_ID_JOB_TOTAL_PAGE = 50049 ,
	PRINT_ID_CANCEL_TOTAL_PAGE_COUNT = 50050 ,
	PRINT_ID_JOB_TOTAL_COPIES = 50051 ,
	PRINT_ID_JOB_ID = 50052 ,
	PRINT_ID_USEPRINTERCOF_ENABLE = 50053 ,
	PRINT_ID_INPUT_TRAY2_STATUS = 50054 ,
	PRINT_ID_INPUT_TRAY3_STATUS = 50055 ,
	PRINT_ID_INPUT_TRAY4_STATUS = 50056 ,
	PRINT_ID_PAGES_BLACK_COUNT = 50057 ,
	PRINT_ID_INPUT_QUALITY_INFO = 50058 ,
	PRINT_ID_JOB_COUNT_INFO = 50059 ,
	PRINT_ID_PAGE_COUNT_INFO = 50060 ,
	PRINT_ID_PRINT_VARS_INFO = 50061 ,
	PRINT_ID_PRINT_STATIC_INFO = 50062 ,
	PRINT_ID_PRINT_ENG_ERR_INFO = 50063 ,
	PRINT_ID_PRINT_USER_RESOLUTION = 50064 ,
	PRINT_ID_PRINT_USER_INPUT_TRAY = 50065 ,
	PRINT_ID_INPUT_MARGIN_INFO = 50066 ,
	PRINT_ID_COPY_PAGE_COUNT_INFO = 50067 ,
	PRINT_ID_AVERAGE_TONER_COVERAGE_C = 50068 ,
	PRINT_ID_AVERAGE_TONER_COVERAGE_M = 50069 ,
	PRINT_ID_AVERAGE_TONER_COVERAGE_Y = 50070 ,
	PRINT_ID_AVERAGE_TONER_COVERAGE_K = 50071 ,
	PRINT_ID_AVERAGE_TONER_COVERAGE_ALL = 50072 ,
	PRINT_ID_DIAG_SET_LED_STATE = 50073 ,
	PRINT_ID_DIAG_SET_TROUBLE_ISOLATION = 50074 ,
	PRINT_ID_DIAG_SET_TRI_FOLD = 50075 ,
	PRINT_ID_DIAG_SET_Z_FOLD = 50076 ,
	PRINT_ID_ENGINE_CAL_DATA = 50077 ,
	PRINT_ID_ENGINE_CAL_TYPE = 50078 ,
	PRINT_ID_TRAY1_SET_SIZE = 50079 ,
	PRINT_ID_TRAY2_SET_SIZE = 50080 ,
	PRINT_ID_TRAY3_SET_SIZE = 50081 ,
	PRINT_ID_TRAY4_SET_SIZE = 50082 ,
	PRINT_ID_MULTI_TRAY_SET_SIZE = 50083 ,
	PRINT_ID_LCTIN_TRAY_SET_SIZE = 50084 ,
	PRINT_ID_LCTOUT_TRAY_SET_SIZE = 50085 ,
	PRINT_ID_TRAY1_SET_TYPE = 50086 ,
	PRINT_ID_TRAY2_SET_TYPE = 50087 ,
	PRINT_ID_TRAY3_SET_TYPE = 50088 ,
	PRINT_ID_TRAY4_SET_TYPE = 50089 ,
	PRINT_ID_MULTI_TRAY_SET_TYPE = 50090 ,
	PRINT_ID_LCTIN_TRAY_SET_TYPE = 50091 ,
	PRINT_ID_LCTOUT_TRAY_SET_TYPE = 50092 ,
	PRINT_ID_PRINT_JOB_CONTROL = 50093 ,
	PRINT_ID_CUSTOM_PAPERSIZE = 50094 ,
	PRINT_ID_TONER_DRUM_ID = 50095 ,
	PRINT_ID_TONER_SERIAL_K = 50096 ,
	PRINT_ID_TONER_SERIAL_C = 50097 ,
	PRINT_ID_TONER_SERIAL_M = 50098 ,
	PRINT_ID_TONER_SERIAL_Y = 50099 ,
	PRINT_ID_PAPER_JAM_COUNT = 50100 ,
	PRINT_ID_CANCEL_PAGE_COUNT = 50101 
}PRINT_ID_E;

typedef enum
{
	PRNSDK_ID_PRNSDK_ENABLE = 80000 ,
	PRNSDK_ID_LICENSE_AUTHENTICATION = 80001 
}PRNSDK_ID_E;

typedef enum
{
	SCAN_ID_RESOLUTION = 30000 ,
	SCAN_ID_SHADING_CORRECTION = 30001 ,
	SCAN_ID_COLOR = 30002 ,
	SCAN_ID_FILE_FORMAT = 30003 ,
	SCAN_ID_AREA_SIZE = 30004 ,
	SCAN_ID_GAMMA = 30005 ,
	SCAN_ID_BRIGHTNESS = 30006 ,
	SCAN_ID_CONTRAST = 30007 ,
	SCAN_ID_X_NUMERATOR = 30008 ,
	SCAN_ID_X_DENOMINATOR = 30009 ,
	SCAN_ID_Y_NUMERATOR = 30010 ,
	SCAN_ID_Y_DENOMINATOR = 30011 ,
	SCAN_ID_SHARP = 30012 ,
	SCAN_ID_DEPRECATED = 30013 ,
	SCAN_ID_BITS_PER_PIXEL = 30014 ,
	SCAN_ID_FLAGS = 30015 ,
	SCAN_ID_DATA_TYPE = 30016 ,
	SCAN_ID_WINDOW_TOP = 30017 ,
	SCAN_ID_WINDOW_LEFT = 30018 ,
	SCAN_ID_WINDOW_BOTTOM = 30019 ,
	SCAN_ID_WINDOW_RIGHT = 30020 ,
	SCAN_ID_SCANNABLE_AREA_TOP = 30021 ,
	SCAN_ID_SCANNABLE_AREA_LEFT = 30022 ,
	SCAN_ID_SCANNABLE_AREA_BOTTOM = 30023 ,
	SCAN_ID_SCANNABLE_AREA_RIGHT = 30024 ,
	SCAN_ID_SCAN_TYPE = 30025 ,
	SCAN_ID_ADF_SUPPORT = 30026 ,
	SCAN_ID_FLATBED_SUPPORT = 30027 ,
	SCAN_ID_DUPLEX_SUPPORT = 30028 ,
	SCAN_ID_SCAN_NUM_SCANS = 30029 ,
	SCAN_ID_TARGET_HOST_NAME_0 = 30030 ,
	SCAN_ID_TARGET_HOST_SCAN_PENDING_0 = 30031 ,
	SCAN_ID_TOTAL_PAGE_COUNT = 30032 ,
	SCAN_ID_COPY_TOTAL_FBSCAN_PAGE_COUNT = 30033 ,
	SCAN_ID_SCAN_TOTAL_FBSCAN_PAGE_COUNT = 30034 ,
	SCAN_ID_COPY_TOTAL_ADFSCAN_PAGE_COUNT = 30035 ,
	SCAN_ID_SCAN_TOTAL_ADFSCAN_PAGE_COUNT = 30036 ,
	SCAN_ID_SCAN_TOTAL_JOB_COUNT = 30037 ,
	SCAN_ID_FTP_ADDR_OP = 30038 ,
	SCAN_ID_MAIL_ADDR_OP = 30039 ,
	SCAN_ID_MAIL_GROUP_OP = 30040 ,
	SCAN_ID_FTP_ADDR_ARRAY = 30041 ,
	SCAN_ID_MAIL_ADDR_ARRAY = 30042 ,
	SCAN_ID_MAIL_GROUP_ARRAAY = 30043 ,
	SCAN_ID_GAMMA_CORRECTION_HAS_FB = 30044 ,
	SCAN_ID_GAMMA_CORRECTION_HAS_ADF_FRONT = 30045 ,
	SCAN_ID_GAMMA_CORRECTION_HAS_ADF_BACK = 30046 ,
	SCAN_ID_UNIFORM_CORRECTION_600 = 30047 ,
	SCAN_ID_UNIFORM_CORRECTION_300 = 30048 ,
	SCAN_ID_EMPTY_MARGIN_TOP_ADF = 30049 ,
	SCAN_ID_EMPTY_MARGIN_TOP_FB = 30050 ,
	SCAN_ID_EMPTY_MARGIN_LEFT_ADF = 30051 ,
	SCAN_ID_EMPTY_MARGIN_LEFT_FB = 30052 ,
	SCAN_ID_STATUS_NOTIFY = 30053 ,
	SCAN_ID_ADF_PAPER_NOTIFY = 30054 ,
	SCAN_ID_ADF_COVER_NOTIFY = 30055 ,
	SCAN_ID_PAPER_SENSOR_DETECTION = 30056 ,
	SCAN_ID_HOME_SENSOR_DETECTION = 30057 ,
	SCAN_ID_FB_FUNCTION_SWITCH = 30058 ,
	SCAN_ID_ADF_FUNCTION_SWITCH = 30059 ,
	SCAN_ID_FB_SCANNER_SENOR = 30060 ,
	SCAN_ID_ADF_TRANSPORT_TEST = 30061 ,
	SCAN_ID_ADF_VERTICAL_AMPLIFICATION = 30062 ,
	SCAN_ID_FB_VERTICAL_AMPLIFICATION = 30063 ,
	SCAN_ID_COPY_TOTAL_FBSCAN_PAGE_NUM = 30064 ,
	SCAN_ID_SCAN_TOTAL_FBSCAN_PAGE_NUM = 30065 ,
	SCAN_ID_COPY_TOTAL_ADFSCAN_PAGE_NUM = 30066 ,
	SCAN_ID_SCAN_TOTAL_ADFSCAN_PAGE_NUM = 30067 ,
	SCAN_ID_DOUBLE_PAGE_COUNT = 30068 ,
	SCAN_ID_SCAN_SOURCE_PAGE_NUM = 30069 ,
	SCAN_ID_SCAN_PAPER_SIZE_MONO_NUM = 30070 ,
	SCAN_ID_SCAN_PAPER_SIZE_COLOR_NUM = 30071 ,
	SCAN_ID_SCAN_PAPER_SIZE_COUNT = 30072 ,
	SCAN_ID_FB_ENGINE_PARAM_SET = 30073 ,
	SCAN_ID_ADF_ENGINE_PARAM_SET = 30074 ,
	SCAN_ID_JOB_CONTROL_ENABLE = 30075 
}SCAN_ID_E;

typedef enum
{
	UI_ID_PANEL_SLEEP_TIME = 60000 ,
	UI_ID_PANEL_LCD_BACKLIGHT = 60001 ,
	UI_ID_PANEL_QUIET_MODE = 60002 ,
	UI_ID_PANEL_SYS_VOLUE = 60003 ,
	UI_ID_PANEL_SLEEP_MODE = 60004 ,
	UI_ID_PANEL_DATE_FORMAT = 60005 ,
	UI_ID_PANEL_TIME_FORMAT = 60006 ,
	UI_ID_PANEL_USB_CONTROL = 60007 ,
	UI_ID_PANEL_NET_CONTROL = 60008 ,
	UI_ID_PANEL_PRINT_COLOR = 60009 ,
	UI_ID_PANEL_PRINT_DEFAULT_TRAY = 60010 ,
	UI_ID_PANEL_PRINT_TRAY1_SIZE = 60011 ,
	UI_ID_PANEL_PRINT_TRAY1_TYPE = 60012 ,
	UI_ID_PANEL_PRINT_TRAY2_SIZE = 60013 ,
	UI_ID_PANEL_PRINT_TRAY2_TYPE = 60014 ,
	UI_ID_PANEL_PRINT_TRAY3_SIZE = 60015 ,
	UI_ID_PANEL_PRINT_TRAY3_TYPE = 60016 ,
	UI_ID_PANEL_PRINT_TRAY4_SIZE = 60017 ,
	UI_ID_PANEL_PRINT_TRAY4_TYPE = 60018 ,
	UI_ID_PANEL_PRINT_TRAY_MULTI_SIZE = 60019 ,
	UI_ID_PANEL_PRINT_TRAY_MULTI_TYPE = 60020 ,
	UI_ID_PANEL_PRINT_CUSTOM_SIZE_LENGHT = 60021 ,
	UI_ID_PANEL_PRINT_CUSTOM_SIZE_HEIGHT = 60022 ,
	UI_ID_PANEL_PRINT_CUSTOM1_SIZE_LENGHT = 60023 ,
	UI_ID_PANEL_PRINT_CUSTOM1_SIZE_HEIGHT = 60024 ,
	UI_ID_PANEL_PRINT_CUSTOM2_SIZE_LENGHT = 60025 ,
	UI_ID_PANEL_PRINT_CUSTOM2_SIZE_HEIGHT = 60026 ,
	UI_ID_PANEL_PRINT_CUSTOM3_SIZE_LENGHT = 60027 ,
	UI_ID_PANEL_PRINT_CUSTOM3_SIZE_HEIGHT = 60028 ,
	UI_ID_PANEL_PRINT_COLOR_BALANCE_C = 60029 ,
	UI_ID_PANEL_PRINT_COLOR_BALANCE_M = 60030 ,
	UI_ID_PANEL_PRINT_COLOR_BALANCE_Y = 60031 ,
	UI_ID_PANEL_PRINT_COLOR_BALANCE_K = 60032 ,
	UI_ID_PANEL_PRINT_IMAGE_SATURATION = 60033 ,
	UI_ID_PANEL_PRINT_IMAGE_BRIGTHNESS = 60034 ,
	UI_ID_PANEL_PRINT_SAVE_TONER_MODE = 60035 ,
	UI_ID_PANEL_PRINT_SKIP_BLANK_PAGE = 60036 ,
	UI_ID_PANEL_PRINT_SEPARATOR_PAGE = 60037 ,
	UI_ID_PANEL_PRINT_IMAGE_CONTRAST = 60038 ,
	UI_ID_PANEL_PRINT_TONER_DENISTY = 60039 ,
	UI_ID_PANEL_AMBIENT_LIGHT = 60040 ,
	UI_ID_PANEL_JOB_ERROR_PROCESS_MODE = 60041 ,
	UI_ID_PANEL_JOB_ERROR_DELETE_TIME = 60042 ,
	UI_ID_PANEL_PRINT_TRAY_LCT_IN_SIZE = 60043 ,
	UI_ID_PANEL_PRINT_TRAY_LCT_IN_TYPE = 60044 ,
	UI_ID_PANEL_PRINT_IMAGE_ORIGINAL_TYPE = 60045 ,
	UI_ID_PANEL_LOCK_SCREEN_PASSWORD = 60046 ,
	UI_ID_PANEL_ERROR_STATUS_VOLUE = 60047 ,
	UI_ID_PANEL_JOB_END_VOLUE = 60048 ,
	UI_ID_PANEL_COLOR_COPY_ENABLE = 60049 ,
	UI_ID_PANEL_COLOR_COPY_PASSWORD = 60050 ,
	UI_ID_PANEL_SHORTCUT_COPY = 60051 ,
	UI_ID_PANEL_LOCK_SCREEN_TIME = 60052 ,
	UI_ID_PANEL_SCREENSAVER_TIMEOUT = 60053 ,
	UI_ID_PANEL_OVERLAY_COPY_DATA = 60054 ,
	UI_ID_PANEL_LOCK_SCREEN_SWITCH = 60055 ,
	UI_ID_PANEL_COPY_PARAM_RESET = 60056 ,
	UI_ID_PANEL_COPY_RANGE_RESET = 60057 ,
	UI_ID_PANEL_JOB_ADVANCE_IMAGE = 60058 ,
	UI_ID_PANEL_ADF_PAPER_IN_VOLUME = 60059 ,
	UI_ID_PANEL_LOG_SAVE_SWITCH = 60060 ,
	UI_ID_PANEL_ADF_SKEW_VALUE = 60061 
}UI_ID_E;

#endif //__MODULEID_H
/**
 * @}
 */
