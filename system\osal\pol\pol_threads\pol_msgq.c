/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_msgq.c
 * @addtogroup threads
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief Pantum Mail/Message Queue API functions
 */
#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"

#include "pol_threads_private.h"
#include "pol_inner.h"

#define get_msgq(m) (MSGQ_S *)(((MSGQ_S *)m >= &s_msgq_pool[0] && (MSGQ_S *)m <= &s_msgq_pool[PI_NUM_QUEUES - 1]) ? m : NULL)

typedef struct msg_queue
{
    PI_SEMAPHORE_T	sem;
    PI_MUTEX_T      mtx;
    MSG_S*          msg_queue;
    int32_t		    msg_count;
    int32_t		    msg_size;
    int32_t		    msg_head;
    int32_t		    msg_tail;
}
MSGQ_S;

static MSGQ_S	    s_msgq_pool[PI_NUM_QUEUES];
static void*        s_msgq_mutex = NULL;

PI_MAILBOX_T pi_msgq_create_ent(int32_t msg_cnt, const char* caller)
{
    MSGQ_S* pmq = NULL;

    RETURN_VAL_SHOW_CALLER_IF(s_msgq_mutex == NULL, pi_log_e, caller, INVALIDMSGQ);
    RETURN_VAL_SHOW_CALLER_IF(msg_cnt <= 0, pi_log_e, caller, INVALIDMSGQ);

    pol_mutex_lock(s_msgq_mutex);
    do
    {
        for ( int32_t i = 0; i < ARRAY_SIZE(s_msgq_pool); ++i )
        {
            if ( s_msgq_pool[i].sem == INVALIDSEM )
            {
                pmq = &s_msgq_pool[i];
                break;
            }
        }
        BREAK_IF(pmq == NULL, pi_log_e);

        pmq->mtx = pi_mutex_create();
        BREAK_IF(pmq->mtx == INVALIDMTX, pi_log_e);

        pmq->sem = pi_sem_create(0);
        BREAK_IF(pmq->sem == INVALIDSEM, pi_log_e);

        pmq->msg_queue = (MSG_S *)pi_zalloc(sizeof(MSG_S) * msg_cnt);
        BREAK_IF(pmq->msg_queue == NULL, pi_log_e);

        pmq->msg_count = 0;
        pmq->msg_size  = msg_cnt;
        pmq->msg_head  = 0;
        pmq->msg_tail  = 0;
    }
    while ( 0 );
    pol_mutex_unlock(s_msgq_mutex);

    if ( pmq == NULL )
    {
        pi_log_e("[CALL:%s] msgq pool full!!!\n", caller);
    }
    else if ( pmq->msg_queue == NULL )
    {
        pi_log_e("[CALL:%s] create failed!!!\n", caller);
        if ( pmq->mtx != INVALIDMTX )
        {
            pi_mutex_destroy(pmq->mtx);
            pmq->mtx = INVALIDMTX;
        }
        if ( pmq->sem != INVALIDSEM )
        {
            pi_sem_destroy(pmq->sem);
            pmq->sem = INVALIDSEM;
        }
        pmq = NULL;
    }
    return (PI_MAILBOX_T)pmq;
}

int32_t pi_msgq_destroy_ent(PI_MAILBOX_T msgq, const char* caller)
{
    MSGQ_S* pmq = get_msgq(msgq);

    RETURN_VAL_SHOW_CALLER_IF(s_msgq_mutex == NULL, pi_log_e, caller, -1);

    pol_mutex_lock(s_msgq_mutex);
    if ( pmq == NULL )
    {
        pi_log_e("[CALL:%s] no such msg queue(%p)!!!\n", caller, msgq);
    }
    else
    {
        if ( pmq->msg_queue != NULL )
        {
            pi_free(pmq->msg_queue);
        }
        if ( pmq->mtx != INVALIDMTX )
        {
            pi_mutex_destroy(pmq->mtx);
        }
        if ( pmq->sem != INVALIDSEM )
        {
            pi_sem_destroy(pmq->sem);
        }
        memset(pmq, 0, sizeof(MSGQ_S));
    }
    pol_mutex_unlock(s_msgq_mutex);

    return 0;
}

int32_t pi_msg_recv_ent(PI_MAILBOX_T msgq, MSG_S* msg, int32_t secs, int32_t usecs, const char* caller)
{
    MSGQ_S* pmq = get_msgq(msgq);
    MSG_S*  pm;
    int32_t ret;

    RETURN_VAL_SHOW_CALLER_IF(pmq == NULL || pmq->msg_queue == NULL || pmq->mtx == NULL || pmq->sem == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_msgq_mutex == NULL, pi_log_e, caller, -1);

    RETURN_VAL_SHOW_CALLER_IF((ret = pi_sem_timedwait(pmq->sem, secs, usecs)) < 0, pi_log_e, caller, ret);
    pi_mutex_lock(pmq->mtx);
    if ( ret > 0 )
    {
        if ( pmq->msg_count == 0 && secs == 0 && usecs == 0 )
        {
            ret = 3;
        }
    }
    else if ( pmq->msg_count > 0 )
    {
        if ( msg != NULL )
        {
            pm = &(pmq->msg_queue[pmq->msg_tail]);
            msg->msg1 = pm->msg1;
            msg->msg2 = pm->msg2;
            msg->msg3 = pm->msg3;
            msg->msg4.ullval = pm->msg4.ullval;
        }
        pmq->msg_count--;
        pmq->msg_tail++;
        if ( pmq->msg_tail >= pmq->msg_size )
        {
            pmq->msg_tail = 0;
        }
        ret = 0;
    }
    else
    {
        pi_log_e("[CALL:%s]sem_timedwait(%d, %d) = %d, but count(%d)\n", caller, secs, usecs, ret, pmq->msg_count);
        ret = 1;
    }
    pi_mutex_unlock(pmq->mtx);

    return ret;
}

int32_t pi_msg_send_ent(PI_MAILBOX_T msgq, MSG_S* msg, const char* caller)
{
    MSGQ_S* pmq = get_msgq(msgq);
    MSG_S*  pm;
    int32_t ret;

    RETURN_VAL_SHOW_CALLER_IF(pmq == NULL || pmq->msg_queue == NULL || pmq->mtx == NULL || pmq->sem == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_msgq_mutex == NULL, pi_log_e, caller, -1);

    pi_mutex_lock(pmq->mtx);
    if ( pmq->msg_count < pmq->msg_size )
    {
        pm = &(pmq->msg_queue[pmq->msg_head]);
        pm->msg1 = msg->msg1;
        pm->msg1 = msg->msg1;
        pm->msg2 = msg->msg2;
        pm->msg3 = msg->msg3;
        pm->msg4.ullval = msg->msg4.ullval;
        pmq->msg_count++;
        pmq->msg_head++;
        if ( pmq->msg_head >= pmq->msg_size )
        {
            pmq->msg_head = 0;
        }
        ret = 0;
        pi_sem_post(pmq->sem);
    }
    else
    {
        pi_log_e("[CALL:%s] msg queue full, current count(%d) size(%d)\n", caller, pmq->msg_count, pmq->msg_size);
        ret = -2;
    }
    pi_mutex_unlock(pmq->mtx);
    return ret;
}

int32_t pi_msg_count_get_ent(PI_MAILBOX_T msgq, const char* caller)
{
    MSGQ_S* pmq = get_msgq(msgq);
    int32_t ret;

    RETURN_VAL_SHOW_CALLER_IF(pmq == NULL || pmq->mtx == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_msgq_mutex == NULL, pi_log_e, caller, -1);

    pi_mutex_lock(pmq->mtx);
    ret = pmq->msg_count;
    pi_mutex_unlock(pmq->mtx);

    return ret;
}

int32_t pi_msgq_size_get_ent(PI_MAILBOX_T msgq, const char* caller)
{
    MSGQ_S* pmq = get_msgq(msgq);
    int32_t ret;

    RETURN_VAL_SHOW_CALLER_IF(pmq == NULL || pmq->mtx == NULL, pi_log_e, caller, -1);
    RETURN_VAL_SHOW_CALLER_IF(s_msgq_mutex == NULL, pi_log_e, caller, -1);

    pi_mutex_lock(pmq->mtx);
    ret = pmq->msg_size;
    pi_mutex_unlock(pmq->mtx);

    return ret;
}

int32_t pi_msgq_prolog(void)
{
    RETURN_VAL_IF(s_msgq_mutex != NULL, pi_log_e, 0); /* 以互斥锁作是否初始化的标识，避免同一进程中多次调用重复初始化 */
    RETURN_VAL_IF((s_msgq_mutex = pol_mutex_create()) == NULL, pi_log_e, -1);

    pol_mutex_lock(s_msgq_mutex);
    memset(&s_msgq_pool, 0, sizeof(s_msgq_pool));
    pol_mutex_unlock(s_msgq_mutex);

    return 0;
}

void pi_msgq_epilog(void)
{
    RETURN_IF(s_msgq_mutex == NULL, pi_log_e);

    pol_mutex_lock(s_msgq_mutex);
    for ( int32_t i = 0; i < ARRAY_SIZE(s_msgq_pool); ++i )
    {
        if ( s_msgq_pool[i].msg_queue != NULL )
        {
            pi_free(s_msgq_pool[i].msg_queue);
        }
        if ( s_msgq_pool[i].mtx != INVALIDMTX )
        {
            pi_mutex_destroy(s_msgq_pool[i].mtx);
        }
        if ( s_msgq_pool[i].sem != INVALIDSEM )
        {
            pi_sem_destroy(s_msgq_pool[i].sem);
        }
    }
    memset(&s_msgq_pool, 0, sizeof(s_msgq_pool));
    pol_mutex_unlock(s_msgq_mutex);

    pol_mutex_destroy(s_msgq_mutex);
    s_msgq_mutex = NULL;
}
/**
 *@}
 */
