/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file preparse.c
 * @addtogroup system_manager
 * @{
 * @brief The type used to pre-parse out jobs 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#include "preparse.h"

#include "channels.h"
#include "pol/pol_threads.h"
#include "pol/pol_types.h"
#include "pol/pol_mem.h"
#include "pol/pol_io.h"
#include "msgrouter_main.h"
#include "ulog.h"

#define PRE_PARSER_LOG "PRE_PARSER"

typedef enum
{
    S_PANEL = 0,
    S_OTHER = 1
}SOURCE_MID_E;

typedef struct
{
    void *callback;
    uint32_t priority;
    uint32_t buff_size;
}PRE_PARSE_ELEMENT_S , *PRE_PARSE_ELEMENT_P;

typedef struct
{
    PI_MUTEX_T _mtx;
    struct channels _set[2];
}PRE_PARSER_DATA_S,PRE_PARSER_DATA_P;

static PRE_PARSER_DATA_S s_preparse_data;

static void free_element(void *data)
{
    PRE_PARSE_ELEMENT_P node = (PRE_PARSE_ELEMENT_P)data;
    if (node)
    {
        free(node);
    }
}

static struct ulog s_log = {
    .name = PRE_PARSER_LOG,
    .file = NULL,
    .fd = -1 ,
    .limitsize = 0,
    .level = DEBUG,
    .o_time = 1, 
    .o_file = 0, 
    .o_func = 1, 
    .o_line = 1, 
    .o_module = 1,
    .o_level = 1,
    .o_pid = 0,  
    .o_thread = 0,
    .o_color = 0,
    .next = NULL 
};

int32_t pre_parser_prolog(void)
{
    ulog_register(&s_log);
    s_preparse_data._mtx = pi_mutex_create();
    if (s_preparse_data._mtx == INVALIDMTX)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "create mutex failed\n");
    }

    for (int32_t i = 0; 
            i < sizeof(s_preparse_data._set)/sizeof(s_preparse_data._set[0]);
            ++i)
    {
        channels_init(&s_preparse_data._set[i] , NULL , free_element);
    }

    return 0;
}

void pre_parser_epilog(void)
{
    pi_mutex_lock(s_preparse_data._mtx);
    for (int32_t i = 0; 
            i < sizeof(s_preparse_data._set)/sizeof(s_preparse_data._set[0]);
            ++i)
    {
        channels_destroy(&s_preparse_data._set[i]);
    }
    pi_mutex_unlock(s_preparse_data._mtx);

    pi_mutex_destroy(s_preparse_data._mtx);
}

PREPARSER_REGISTER_E pre_parser_register1( PPRE_PARSER1 function, uint32_t min_buff_size, PARSER_FLAGS_E flags)
{
    PRE_PARSE_ELEMENT_P node = (PRE_PARSE_ELEMENT_P)pi_malloc(sizeof(PRE_PARSE_ELEMENT_S));
    if (!node)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "malloc failed\n");
        return PREPARSER_REGISTER_ERROR;
    }

    node->callback = function;
    node->buff_size = min_buff_size;
    node->priority = flags;

    pi_mutex_lock(s_preparse_data._mtx);
    if (channels_order_insert(&s_preparse_data._set[S_OTHER] , node->priority , (void*)node , 1) == -1)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "insert channels failed\n");
        pi_free(node);
        pi_mutex_unlock(s_preparse_data._mtx);
        return PREPARSER_REGISTER_ERROR;
    }
    pi_mutex_unlock(s_preparse_data._mtx);

    return PREPARSER_REGISTER_SUCCESS;
}

PREPARSER_REGISTER_E pre_parser_register2( PPRE_PARSER2 function, JOB_CONFIG_OBJ_E job_obj)
{
    PRE_PARSE_ELEMENT_P node = (PRE_PARSE_ELEMENT_P)pi_malloc(sizeof(PRE_PARSE_ELEMENT_S));
    if (!node)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "malloc failed\n");
        return PREPARSER_REGISTER_ERROR;
    }

    node->callback = function;
    node->buff_size = 0;
    node->priority = 0;

    pi_mutex_lock(s_preparse_data._mtx);
    if (channels_tail_insert(&s_preparse_data._set[S_PANEL] , job_obj , (void*)node , 0) == -1)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "insert channels failed\n");
        pi_free(node);
        pi_mutex_unlock(s_preparse_data._mtx);
        return PREPARSER_REGISTER_ERROR;
    }
    pi_mutex_unlock(s_preparse_data._mtx);

    return PREPARSER_REGISTER_SUCCESS;
}

static int32_t _get_qio_job_tyep(void *handle , 
        PREPARSER_RESULT_P job_result)
{
    struct fragment *cur = s_preparse_data._set[S_OTHER].head , *next = NULL;
    GQIO_S *gqio = (GQIO_S*)handle;
    uint8_t buf[4096];
    uint32_t offset = 0 , remain = 0 , default_len = sizeof(buf) / sizeof(buf[0]);
    int32_t readnum = 0 , i = 0;
    PRE_PARSE_ELEMENT_P ptr = NULL;
    PPRE_PARSER1 callback = NULL;

    if (gqio->io_class == IO_CLASS_SCAN)
    {
        ULOG_INFO(PRE_PARSER_LOG , "scan preparser..\n");
        ptr = (PRE_PARSE_ELEMENT_P)channels_find(
                &s_preparse_data._set[S_OTHER] , SCAN_PARSER);
        if (!ptr)
        {
            ULOG_ERROR(PRE_PARSER_LOG , "Scan preparse not registered!\n");
            goto END;
        }

        callback = (PPRE_PARSER1)ptr->callback;
        if (callback)
        {
            if (callback((unsigned char*)buf , 
                offset , 
                handle , 
                job_result) == PREPARSER_REGISTER_SUCCESS)
                return 0;
            else
                goto END;
        }
        else
        {
            ULOG_ERROR(PRE_PARSER_LOG , "Scan preparse callback is NULL!\n");
            goto END;
        }
    }

    while(cur)
    {
        ptr = (PRE_PARSE_ELEMENT_P)channels_iterator(cur , &next);
        if (ptr)
        {
            callback = (PPRE_PARSER1)ptr->callback;
            if (!callback || ptr->priority == SCAN_PARSER)
            {
                cur = next;
                continue;
            }
            else
            {
                for (; ptr->buff_size > offset && i < 100;++i)
                {
                    if (ptr->buff_size > default_len)
                    {
                        remain = default_len - offset;
                    }
                    else
                    {
                        remain = ptr->buff_size - offset;
                    }
                    if (remain > 0)
                    {
                        readnum = GQIO_READ(gqio , buf + offset , remain);
                        if (readnum < 0)
                        {
                            ULOG_ERROR(PRE_PARSER_LOG , "GQIO read failed , code:%d\n" , readnum);
                            usleep(100000);
                        }
                        else if (readnum == 0)
                        {
                            if (gqio->io_via == IO_VIA_INTERNAL || gqio->io_via == IO_VIA_FILE || gqio->io_via == IO_VIA_UDISK || gqio->io_via == IO_VIA_INTERNAL_PDF)
                            {
                                ULOG_WARN(PRE_PARSER_LOG , "No data available..\n");
                                break;
                            }
                            usleep(100000);
                        }
                        else
                        {
                            offset += readnum;
                        }
                    }
                    else
                    {
                        break;
                    }
                }
                ULOG_INFO(PRE_PARSER_LOG , "priority:%d , read bytes:%u , expect bytes:%u\n", 
                        ptr->priority , offset , ptr->buff_size); 
                if (offset && 
                        callback((unsigned char*)buf , offset , gqio , job_result) == PREPARSER_REGISTER_SUCCESS)
                {
                    GQIO_REWIND(gqio , buf , offset);
                    return 0;
                }
            }
        }

        cur = next;
    }

END:
    ULOG_WARN(PRE_PARSER_LOG , "drop data..\n");
    while(GQIO_READABLE(gqio , 1 , 0) > 0)
    {
        if (GQIO_READ(gqio , buf , default_len) <= 0)
        {
            break;
        }
    }

    return -1;
}

int32_t pre_parser_get_job_type(void *handle , 
        uint32_t source_mid , 
        uint32_t job_obj , 
        PREPARSER_RESULT_P parser_result)
{
    if (!handle ||!parser_result)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "input args invalid\n");
        return -1;
    }

    if (job_obj)
    {
        PRE_PARSE_ELEMENT_P  ptr = (PRE_PARSE_ELEMENT_P)channels_find(
                &s_preparse_data._set[S_PANEL] , (int)job_obj);

        if (ptr)
        {
            PPRE_PARSER2 callback = (PPRE_PARSER2)(ptr->callback);

            if (callback && callback(handle , parser_result) == PREPARSER_REGISTER_SUCCESS)
            {
                /*ULOG_INFO(PRE_PARSER_LOG , "preparse success , job_type:%d , priority:%d\n" , */
                        /*parser_result->job_type , parser_result->priority);*/
                return 0;
            }
            else
            {
                ULOG_ERROR(PRE_PARSER_LOG , "preparse failed , job_obj:%d\n" , job_obj);
            }
        } 
        else
        {
            ULOG_ERROR(PRE_PARSER_LOG , "The callback function of the job_obj:%d cannot be found\n" , job_obj);
        }
    }
    else
    {
        return _get_qio_job_tyep(handle , parser_result);
    }

    return -1;
}
 
int32_t pre_parser_get_qio_job_type(void *handle , 
        PREPARSER_RESULT_P parser_result)
{
    if (!handle || !parser_result)
    {
        ULOG_ERROR(PRE_PARSER_LOG , "input args invalid\n");
        return -1;
    }

    return _get_qio_job_tyep(handle , parser_result);
}

/**
 * @}
 */
