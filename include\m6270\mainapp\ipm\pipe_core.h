/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       pipe_core.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-10-29
 * @version    v1.0
 * @details    this is pipe layout for the image process moudle
 */

#ifndef PIPE_CORE_H
#define PIPE_CORE_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "step_core.h"
#include "rule_parsing.h"

#define MAX_PIPE_NAME    64  ///<pipe config

/**
 * @brief tag_ip_pipe
 */
typedef struct tag_ip_pipe  PIPE_S,*PIPE_P;
struct tag_ip_step;

/**
 * @brief wake current pipe
 */
typedef int32_t (*WAKEUP_PIPE) (struct tag_ip_step *pstep);

/**
 * @brief pipe Object
 */
  struct tag_ip_pipe
{
    char                pipe_name[MAX_PIPE_NAME];
    RULE_PARSING_S      *pipe_rule;     ///< rule for pipe
    struct tag_ip_step  *steps;          ///< list of steps this pipe has
    WAKEUP_PIPE          wakeup_pipe;    ///< wakeup_pipe callback
    MEMOBJ_P             hImem;          ///< image memory allocator
    struct tag_ip_step   *current_step;  ///< current step Object

    int                 busy;          ///< state of pipe in run
    int                 eoi_flagged;    ///< an EOI reported by the pipe printed
    int                 eoj_flagged;    ///< an EOJ reported by the pipe printed
    int                 next_page;      ///< next page

    int                 pipeline_cancel;
    void                *context;        ///< note : point to object of the up layout
    struct tag_ip_pipe  *next;           ///< for listing
};

/**
 * @brief pipeline  msg
 */
typedef struct tag_pipeline_msg
{
    uint32_t    action_type;    ///< action type
    uint32_t    msg2;           ///< msg2
    uint32_t    msg3;           ///< msg3
    union
    {
        uint64_t  ullval;     ///< Fourth parameter, can by up to 64 bits
        void   *ptrval;     ///< or, a pointer parameter if needed
    }msg4;
}PIPE_LINE_MSG_S, *PIPE_LINE_MSG_P;

/**
 * @brief get rule object
 * @param[in] description  step rule description
 * @param[in] context point to object of the up layout
 * @return PIPE_P \n
 *          … …
 * @retval current pipe object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
PIPE_P pipe_core_initialize_pipe(char* description,void *context);

/**
 * @brief pipe core building pipe
 * @param[in] PIPE_P current pipe object
 * @return int32_t \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipe_core_building_pipe(PIPE_P pipe);

/**
 * @brief pipe core open pipe
 * @param[in] PIPE_P current pipe object
 * @return int32_t \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipe_core_open_pipe(PIPE_P pipe);

/**
 * @brief pipe core running pipe
 * @param[in] PIPE_P current pipe object
 * @param[in] PIPE_LINE_MSG_P pipe msg
 * @return int32_t \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipe_core_running_pipe_one_cycle(PIPE_P pipe,PIPE_LINE_MSG_P msg);

/**
 * @brief close pipe
 * @param[in] PIPE_P current pipe object
 * @return int32_t \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void pipe_core_close_pipe(PIPE_P pipe);

/**
 * @brief init pipe core
 * @return int32_t \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipe_core_prolog();

/**
 * @brief destory pipe core
 * @return int32_t \n
 *          … …
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pipe_core_epilog();


#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* PIPE_CORE_H */

/**
 *@}
 */


