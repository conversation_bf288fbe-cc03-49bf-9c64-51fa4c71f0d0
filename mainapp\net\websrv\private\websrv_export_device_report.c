#include "nettypes.h"
#include "netctx.h"
#include "netsts.h"
#include "websrv_private.h"

/**
 * @brief       EWS export device report file.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR>
 * @date        2023-12-18
 */
int32_t websrv_export_device_report(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    NET_CTX_S*   net_ctx = s_websrv_ctx->net_ctx;
    ROUTER_MSG_S send_msg;
    ROUTER_MSG_S recv_msg;
    TIMEVAL_S    timeout_val;
    struct stat  file_stat;
    char         file_path[256];
    char         file_url[256];
    int32_t      ret = 1;
    int32_t      rlen = -1;
    uint8_t      wait_time = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    NETEVT_NOTIFY_I(EVT_TYPE_POWERMGR_CONFIG, PM_EVENT_DEEP_WAKEUP); /*如果打印机睡眠 唤醒打印机*/
    snprintf(file_url, sizeof(file_url), "webpage/device_report.pdf");
    snprintf(file_path, sizeof(file_path), "/tmp/%s", file_url);
    NET_TRACE("device report file path(%s) from client(%u->%u)", file_path, ptask->r_port, ptask->l_port);

    timeout_val.secs  = 1;
    timeout_val.usecs = 0;
    task_msg_timeout_by_router(MID_NET_GEN_REPORT, &recv_msg, &timeout_val);

    send_msg.msgType   = MSG_INTERNAL_PAGE_PDF;
    send_msg.msg1      = ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO; /*1: generate report */
    send_msg.msg2      = 0;
    send_msg.msg3      = (void *)(file_path);
    send_msg.msgSender = MID_NET_GEN_REPORT;

    RETURN_VAL_IF(task_msg_send_by_router(MID_PRINT_PARSER_IF, &send_msg) < 0, NET_WARN, -1);

    timeout_val.secs  = REPORT_TIME_OUT;
    timeout_val.usecs = 0;
    ret = task_msg_timeout_by_router(MID_NET_GEN_REPORT, &recv_msg, &timeout_val);
    if ( recv_msg.msgType == MSG_INTERNAL_PAGE_PDF_STORE_RESULT && recv_msg.msg1 == ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO && recv_msg.msg2 == 0 && ret == 0 )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'%s'}", file_url);
    }
    else
    {
        NET_WARN("ret(%d) recv_msg.msg2(%u)", ret, recv_msg.msg2);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'ERROR'}");
    }

    /* send generate device result to print_module*/
    send_msg.msgType   = MSG_INTERNAL_PAGE_PDF_STORE_RESULTS_ACK;
    send_msg.msg1      = ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO; /*1: generate report */
    send_msg.msg2      = (ret == 1) ? 0 : 1;
    send_msg.msg3      = (void *)NULL;
    send_msg.msgSender = MID_NET_GEN_REPORT;
    task_msg_send_by_router(MID_PRINT_PARSER_IF, &send_msg);

    return rlen;
}
