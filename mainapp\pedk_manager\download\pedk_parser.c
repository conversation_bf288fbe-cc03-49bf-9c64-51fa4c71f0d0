/**************************************************************
Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
module name:     pedk parser
file name:       pedk_parser.h
author:          <PERSON> (<EMAIL>)
date:            2023-12-21
description:     pedk parser API
**************************************************************/

#include <pthread.h>
#include "pedk_parser.h"

#define APP_JSON_KEY_DEBUG 0

// 函数原型
PEDK_APP_CONFIG_S *parse_json_file(const char *filename);
void free_app_config(PEDK_APP_CONFIG_S *app_config);
void parse_printer_info(cJSON *printer_info, PEDK_APP_CONFIG_S *app_config);

// 解析打印机信息
void parse_printer_info(cJSON *printer_info, PEDK_APP_CONFIG_S *app_config)
{
#if APP_JSON_KEY_DEBUG

    cJSON *model_array = cJSON_GetObjectItem(printer_info, "model");
    cJSON *type_array = cJSON_GetObjectItem(printer_info, "type");
    cJSON *screen_type_array = cJSON_GetObjectItem(printer_info, "srceen_type");

    int model_count = cJSON_GetArraySize(model_array);
    int type_count = cJSON_GetArraySize(type_array);
    int screen_type_count = cJSON_GetArraySize(screen_type_array);

    app_config->printer_info.model = (char **)malloc((model_count + 1) * sizeof(char *));
    app_config->printer_info.type = (char **)malloc((type_count + 1) * sizeof(char *));
    app_config->printer_info.srceen_type = (char **)malloc((screen_type_count + 1) * sizeof(char *));

    // 解析并存储 model
    for (int i = 0; i < model_count; i++) {
        cJSON *model_item = cJSON_GetArrayItem(model_array, i);
        if (model_item->valuestring) {
            app_config->printer_info.model[i] = strdup(model_item->valuestring);
        }
    }
    app_config->printer_info.model[model_count] = NULL;

    // 解析并存储 type
    for (int i = 0; i < type_count; i++) {
        cJSON *type_item = cJSON_GetArrayItem(type_array, i);
        if (type_item->valuestring) {
            app_config->printer_info.type[i] = strdup(type_item->valuestring);
        }
    }
    app_config->printer_info.type[type_count] = NULL;

    // 解析并存储 srceen_type
    for (int i = 0; i < screen_type_count; i++) {
        cJSON *screen_type_item = cJSON_GetArrayItem(screen_type_array, i);
        if (screen_type_item->valuestring) {
            app_config->printer_info.srceen_type[i] = strdup(screen_type_item->valuestring);
        }
    }
    app_config->printer_info.srceen_type[screen_type_count] = NULL;
#endif
}


cJSON *parse_read_file(const char *filename)
{
    printf("filename : %s\n",filename);
    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Error opening file.\n");
        return NULL;
    }

    fseek(file, 0, SEEK_END);
    long fileSize = ftell(file);
    fseek(file, 0, SEEK_SET);

    char *buffer = (char *)malloc(fileSize + 1);
    fread(buffer, 1, fileSize, file);
    fclose(file);

    buffer[fileSize] = '\0';

    cJSON *root = cJSON_Parse(buffer);
    free(buffer);

    return root;
}
// 解析 JSON 文件
PEDK_APP_CONFIG_S *parse_json_file(const char *filename)
{
    cJSON *root = parse_read_file(filename);

    if (root == NULL) {
        fprintf(stderr, "Error parsing JSON.\n");
        return NULL;
    }

    PEDK_APP_CONFIG_S *app_config = (PEDK_APP_CONFIG_S *)malloc(sizeof(PEDK_APP_CONFIG_S));
    memset(app_config, 0, sizeof(PEDK_APP_CONFIG_S)); // 初始化结构体

    cJSON *name = cJSON_GetObjectItem(root, "name");
    cJSON *bundleName = cJSON_GetObjectItem(root, "bundleName");
    cJSON *version = cJSON_GetObjectItem(root, "version");
    cJSON *apiVer = cJSON_GetObjectItem(root, "api_ver");
    cJSON *uiVer = cJSON_GetObjectItem(root, "ui_ver");
    cJSON *_main = cJSON_GetObjectItem(root, "main");
    cJSON *icon = cJSON_GetObjectItem(root, "icon");
    cJSON *license = cJSON_GetObjectItem(root, "license");
    cJSON *description = cJSON_GetObjectItem(root, "description");
    cJSON *author = cJSON_GetObjectItem(root, "author");
    cJSON *rootApp = cJSON_GetObjectItem(root, "rootAPP");
    //cJSON *printer_info = cJSON_GetObjectItem(root, "printer_info");
    //cJSON *whitelistArray = cJSON_GetObjectItem(root, "whitelist");

    // 分配内存并复制字符串
    app_config->name = (name != NULL && name->type == cJSON_String) ? strdup(name->valuestring) : NULL;
    app_config->bundleName = (bundleName != NULL && bundleName->type == cJSON_String) ? strdup(bundleName->valuestring) : NULL;
    app_config->version = (version != NULL && version->type == cJSON_String) ? strdup(version->valuestring) : NULL;
    app_config->api_ver = (apiVer != NULL && apiVer->type == cJSON_String) ? strdup(apiVer->valuestring) : NULL;
    app_config->ui_ver = (uiVer != NULL && uiVer->type == cJSON_String) ? strdup(uiVer->valuestring) : NULL;
    app_config->main = (_main != NULL && _main->type == cJSON_String) ? strdup(_main->valuestring) : NULL;
    app_config->icon = (icon != NULL && icon->type == cJSON_String) ? strdup(icon->valuestring) : NULL;
    app_config->license = (license != NULL && license->type == cJSON_String) ? strdup(license->valuestring) : NULL;
    app_config->description = (description != NULL && description->type == cJSON_String) ? strdup(description->valuestring) : NULL;
    app_config->author = (author != NULL && author->type == cJSON_String) ? strdup(author->valuestring) : NULL;
    app_config->rootApp = (rootApp != NULL && rootApp->type == cJSON_True) ? 1 : 0;

#if APP_JSON_KEY_DEBUG
    // 解析并存储 whitelist
    if(whitelistArray != NULL)
    {
        int white_list_count = cJSON_GetArraySize(whitelistArray);
        app_config->whitelist = (char **)malloc((white_list_count + 1) * sizeof(char *));
        for (int i = 0; i < white_list_count; i++) {
            cJSON *whitelistItem = cJSON_GetArrayItem(whitelistArray, i);
            if (whitelistItem->valuestring) {
                app_config->whitelist[i] = strdup(whitelistItem->valuestring);
            }
        }
        app_config->whitelist[white_list_count] = NULL;
    }
    else
    {
        Log(5, "whitelistArray is NULL\n");
    }

    // 解析打印机信息
    if (printer_info) {
        parse_printer_info(printer_info, app_config);
    } else {
        printf("Error parsing printer info.\n");
    }
#endif
    cJSON_Delete(root);

    return app_config;
}

// 释放内存
void free_app_config(PEDK_APP_CONFIG_S *app_config)
{
    if (app_config == NULL) {
        return;
    }

   // 释放字符串
    if (app_config->name != NULL) {
        free(app_config->name);
    }
    if (app_config->bundleName != NULL) {
        free(app_config->bundleName);
    }
    if (app_config->version != NULL) {
        free(app_config->version);
    }
    if (app_config->api_ver != NULL) {
        free(app_config->api_ver);
    }
    if (app_config->ui_ver != NULL) {
        free(app_config->ui_ver);
    }
    if (app_config->main != NULL) {
        free(app_config->main);
    }
    if (app_config->icon != NULL) {
        free(app_config->icon);
    }
    if (app_config->license != NULL) {
        free(app_config->license);
    }
    if (app_config->description != NULL) {
        free(app_config->description);
    }
    if (app_config->author != NULL) {
        free(app_config->author);
    }

#if APP_JSON_KEY_DEBUG
    // 释放打印机信息
    if (app_config->printer_info.model != NULL) {
        for (int i = 0; app_config->printer_info.model[i] != NULL; i++) {
            free(app_config->printer_info.model[i]);
        }
        free(app_config->printer_info.model);
    }

    if (app_config->printer_info.type != NULL) {
        for (int i = 0; app_config->printer_info.type[i] != NULL; i++) {
            free(app_config->printer_info.type[i]);
        }
        free(app_config->printer_info.type);
    }

    if (app_config->printer_info.srceen_type != NULL) {
        for (int i = 0; app_config->printer_info.srceen_type[i] != NULL; i++) {
            free(app_config->printer_info.srceen_type[i]);
        }
        free(app_config->printer_info.srceen_type);
    }

    // 释放白名单
    if (app_config->whitelist != NULL) {
        for (int i = 0; app_config->whitelist[i] != NULL; i++) {
            free(app_config->whitelist[i]);
        }
        free(app_config->whitelist);
    }
#endif
    // 释放结构体
    free(app_config);
}