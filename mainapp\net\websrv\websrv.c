/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file websrv.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief WEB Server for port 80
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netport.h"
#include "netsock.h"
#include "netsts.h"
#include "netmisc.h"
#include "nettls.h"
#include "ipv4.h"
#include "private/websrv_private.h"
#include <stdint.h>

#if CONFIG_NET_PLOG
#include "plog/plog.h"
#include "zip/zip.h"
#endif

#if CONFIG_NET_SAFETY_MACHINE
#include "event_log/event_log.h"
#endif

#ifndef CONFIG_NET_EWS_HOME_DIR
#define CONFIG_NET_EWS_HOME_DIR             "/root/pantumweb"
#endif

#define WEBSRV_TEMP_DIR                     "/tmp/webpage"

#define WEBSRV_TEMP_CLI_PREFIX              WEBSRV_TEMP_DIR "/client_"

#define MAX_UPLOAD_FILE_SIZE                (50*1024)
#define P12_CERTFILE                        "/tmp/pksc12cert.pfx"
#define P12_KEYFILE                         "/tmp/pksc12key.pem"
#define TLS_CERTFILE                        "/tmp/certtls.pem"

#define REPORT_TIME_OUT                     60

#define BREAK_VAL_IF(_cond, _log, block) 						if (_cond) { _log("[LINE:%d] condition ("#_cond"), break. \n", __LINE__); block; break;  }

#define _autofree_cjson_ __attribute__ ((__cleanup__(cjson_cleanup)))

#define __DECLARE_SECTION_FUNC(_section, _func_type)        \
        extern _func_type __start_##_section;  \
        extern _func_type __stop_##_section;   \
        static __attribute__((weakref(STR(__start_##_section)))) INIT_FUNC __start_##_section##_wref;     \
        static __attribute__((weakref(STR(__stop_##_section)))) INIT_FUNC __stop_##_section##_wref;


#define _DECLARE_SECTION_FUNC(_section, _func_type)      __DECLARE_SECTION_FUNC(_section, _func_type)
#define DECLARE_SECTION_FUNC(_section, _func_type)      _DECLARE_SECTION_FUNC(EWS_SECTION_NAME(_section), _func_type)

#define __FOREACH_FUNCTION_CALL(_section, ...) \
    do {                            \
        if(&__start_##_section##_wref == 0) break;                                                \
        for (__auto_type _pfunc = &__start_##_section##_wref; _pfunc < &__stop_##_section##_wref; _pfunc++)  {  \
            (*_pfunc)(__VA_ARGS__);                           \
        }                                                     \
    } while(0)

#define _FOREACH_FUNCTION_CALL(_section_name, ...) __FOREACH_FUNCTION_CALL(_section_name, ##__VA_ARGS__)
#define FOREACH_FUNCTION_CALL(_section_name, ...) _FOREACH_FUNCTION_CALL(EWS_SECTION_NAME(_section_name), ##__VA_ARGS__)

#define CPP_CHECK

#ifdef CPP_CHECK
    // only for cppcheck, virtually just p1 - p2
    #define POINTER_SUBSTRACT(p1, p2)    ( ( (uintptr_t)p1 - (uintptr_t)p2 ) / sizeof(*p1) )
#else
    #define POINTER_SUBSTRACT(p1, p2)    ( p1 - p2 )
#endif

#define __QSORT_SECTION(_section, _type, _comp)      \
    ( {                                      \
        extern _type __start_##_section;  \
        extern _type __stop_##_section;   \
        qsort(&__start_##_section, POINTER_SUBSTRACT(&__stop_##_section, &__start_##_section), sizeof(_type), _comp);    \
    } )

#define _QSORT_SECTION(_section_name, _type, _comp)         __QSORT_SECTION(_section_name, _type, _comp)
#define QSORT_SECTION(_section_name, _type, _comp)         _QSORT_SECTION(EWS_SECTION_NAME(_section_name), _type, _comp)

#define __BSEARCH_SECTION(_section, _key, _type, _comp)      \
    ( { \
        extern _type __start_##_section; extern _type __stop_##_section;   \
        bsearch(_key, &__start_##_section, POINTER_SUBSTRACT(&__stop_##_section, &__start_##_section), sizeof(_type), _comp);  \
    } )

#define _BSEARCH_SECTION(_section_name, _key, _type, _comp)      __BSEARCH_SECTION(_section_name, _key, _type, _comp)
#define BSEARCH_SECTION(_section_name, _key, _type, _comp)       _BSEARCH_SECTION(EWS_SECTION_NAME(_section_name), _key, _type, _comp)

#define CLEAR_USER_DATA()   \
    if (priv->user_data && priv->user_data_destroy_fn) {   \
        priv->user_data_destroy_fn(priv->user_data);   \
        priv->user_data = NULL;                        \
        priv->user_data_destroy_fn = NULL;             \
    }

#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */

#define WEB_ADMIN_TABLE(x)                  \
    x(ADMIN_ID_SEC, WEB_SEC_ADMIN_NAME, 0), \
    x(ADMIN_ID_AUD, WEB_AUD_ADMIN_NAME, 1), \
    x(ADMIN_ID_SYS, WEB_SYS_ADMIN_NAME, 2), \

#define enum_out(e, n, i)   e = i
typedef enum { WEB_ADMIN_TABLE(enum_out) ADMIN_ID_NUM } WEB_ADMIN_ID_E;
#undef  enum_out

#define name_out(e, n, i)   n
static const char*      s_web_admin_name[ADMIN_ID_NUM]  = { WEB_ADMIN_TABLE(name_out) };
#undef  name_out

#define url_out(e, n, i)    "index_" n ".html"
static const char*      s_web_admin_url[ADMIN_ID_NUM]   = { WEB_ADMIN_TABLE(url_out) };
#undef  url_out

#endif /* CONFIG_NET_SAFETY_MACHINE */

DECLARE_SECTION_FUNC(init, INIT_FUNC);

typedef struct websrv_context
{
    NET_CTX_S*          net_ctx;
    PI_THREAD_T         srv_tid;
    char                owner[64];
    void*               fb_handle;
    void*               tp_handle;
#if CONFIG_NET_PLOG
    PLOG_TOPIC_MAP_S*   topic_map;
    EXPORT_PLOG_FLAG_E  export_flag;
    char                export_path[256];
#endif
	uint8_t*			screenbuf;
	int32_t				screen_size;
	HAL_FB_DEVINFO_S 	fb_info;
	PI_MUTEX_T  		mutex;
}
WEBSRV_CTX_S;

static WEBSRV_CTX_S*    s_websrv_ctx = NULL;

__attribute__((always_inline))
static inline void cjson_cleanup(cJSON** ppcjson)
{
    if (*ppcjson)
    {
        cJSON_Delete(*ppcjson);
        *ppcjson = NULL;
    }
}

static int32_t websrv_is_authorized_ok(PRIV_INFO_S* priv, uint8_t access_flags, uint8_t author_need)
{
    if ( (priv->authorized == 0) && (author_need != 0) )
    {
        /* 当前连接未完成登陆校验 && 当前请求需要先通过登陆授权方可访问 */
        return 0;
    }
#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
    else if ( (priv->authorized != 0) && ((access_flags & (1 << priv->admin_id)) == 0) )
    {
        /* 当前GET请求的准入权限与已登录的用户类别不匹配 */
        return 0;
    }
#endif
    return 1;
}

static int websrv_url_tbl_compar(const void *x, const void *y)
{
    const WEBSRV_URL_TABLE_S* a1 = x;
    const WEBSRV_URL_TABLE_S* a2 = y;
    int ret;

    ret = a1->request_method - a2->request_method;
    RETURN_VAL_IF(ret != 0 , NET_NONE, ret);

    return strcmp(a1->request_url, a2->request_url);
}

/**
 * @brief       Get content type.
 * @param[in]   url     : The WEBOM_CTX_S object pointer.
 * @param[out]  rtype   : The oms tring.
 * @return      reply value
 * @retval      =0      : type is not shtml\n
 *              =1      : type is shtml\n
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t get_content_type(const char* url, const char** rtype)
{
    const char* suffix = strrchr(url, '.');
    int32_t     do_ssi = 0;

    if ( STRING_NO_EMPTY(suffix) )
    {
        if( strcasecmp(suffix, ".shtml") == 0 || strcasecmp(suffix, ".shtm") == 0 )
        {
            *rtype = "text/html; charset=\"utf-8\"";
            do_ssi = 1;
        }
        else if( strcasecmp(suffix, ".html") == 0 || strcasecmp(suffix, ".htm") == 0 )
        {
            *rtype = "text/html; charset=\"utf-8\"";
        }
        else if( strcasecmp(suffix, ".png") == 0 )
        {
            *rtype = "image/png";
        }
        else if( strcasecmp(suffix, ".gif") == 0 )
        {
            *rtype = "image/gif";
        }
        else if( strcasecmp(suffix, ".jpeg") == 0 )
        {
            *rtype = "image/jpeg";
        }
        else if( strcasecmp(suffix, ".jpg") == 0 )
        {
            *rtype = "image/jpeg";
        }
        else if( strcasecmp(suffix, ".tiff") == 0 )
        {
            *rtype = "image/tiff";
        }
        else if( strcasecmp(suffix, ".tif") == 0 )
        {
            *rtype = "image/tiff";
        }
        else if( strcasecmp(suffix, ".svg") == 0 )
        {
            *rtype = "image/svg+xml";
        }
        else if( strcasecmp(suffix, ".js") == 0 )
        {
            *rtype = "application/x-javascript";
        }
        else if( url + 3 < suffix && strcasecmp(suffix - 3, ".js.gz") == 0 )
        {
            *rtype = "gzip-application/x-javascript";
        }
        else if( strcasecmp(suffix, ".css") == 0 )
        {
            *rtype = "text/css; charset=\"utf-8\"";
        }
        else if( strcasecmp(suffix, ".swf") == 0 )
        {
            *rtype = "application/x-swf-flash";
        }
        else if( strcasecmp(suffix, ".pem") == 0 )
        {
            *rtype = "application/binary";
        }
        else if( strcasecmp(suffix, ".txt") == 0 )
        {
            *rtype = "application/force-download";
        }
        else
        {
            *rtype = "application/octet-stream";
        }
    }
    else
    {
        *rtype = "application/octet-stream";
    }

    return do_ssi;
}

/**
 * @brief       EWS process ssicmd.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   group   : The group of this ssicmd.
 * @param[in]   ssicmd  : ssicmd.
 * @param[out]  buf     : buffer.
 * @param[in]   buf_size: buf_size.
 * @return      reply length
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
static int32_t websrv_process_ssicmd(HTTP_TASK_S* ptask, WEBOM_GROUP_S* group, const char* ssicmd, char* buf, size_t buf_size)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char    cmd[32];
    char    att[32];
    char    str[64];
    char*   ptr = NULL;
    int32_t idx = 0;
    int32_t len = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, 0);

    memset(cmd, 0, sizeof(cmd));
    memset(att, 0, sizeof(att));
    memset(str, 0, sizeof(str));
    if ( sscanf(ssicmd, "%31[^ ] %31[^=]=%63[^\n]", cmd, att, str) == 3 )
    {
        if ( strcmp(cmd, "include") == 0 && (strcmp(att, "oid") == 0 || strcmp(att, "wifi") == 0) )
        {
            ptr = strrchr(str, '.');
            if ( ptr != NULL )
            {
                *ptr++ = '\0';
                NET_TRACE("str(%s) ptr(%s) from client(%u->%u)", str, ptr, ptask->r_port, ptask->l_port);
                idx = atoi(ptr);
            }
            len = websrv_omstr_get_val(&(priv->webomctx), group, str, idx, buf, buf_size);
        }
        else
        {
            NET_DEBUG("invalid cmd(%s) att(%s) str(%s) from client(%u->%u)", cmd, att, str, ptask->r_port, ptask->l_port);
        }
    }
    else
    {
        NET_WARN("invalid ssicmd(%s) from client(%u->%u)", ssicmd, ptask->r_port, ptask->l_port);
    }

    return len;
}

/**
 * @brief       EWS process shtml.
 * @param[in]   ptask         : The HTTP_TASK_S object pointer.
 * @param[in]   parms         : The group of this ssicmd.
 * @param[in]   src_filepath  : ssicmd.
 * @param[out]  dst_filepath  : buffer.
 * @return      reply length
 * @retval      = 0     : fail\n
 *              > 0     : success
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_process_shtml(HTTP_TASK_S* ptask, const char* parms, const char* src_filepath, const char* dst_filepath)
{
#define WEBSRV_SSITAG_BEGIN     "<!--#"
#define WEBSRV_SSITAG_END       "-->"
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    WEBOM_GROUP_S*  webom_group = NULL;
    FILE*           src_stream = NULL;
    FILE*           dst_stream = NULL;
    char*           pstr = NULL;
    char            prefix[128];
    char            suffix[128];
    char            ssicmd[128];
    char            module[64];
    char            line[256];
    int32_t         parsing = 0;
    int32_t         rlen = 0;
    int32_t         nbuf = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, 0);

    do
    {
        dst_stream = pi_fopen(dst_filepath, "w+");
        BREAK_IF(dst_stream == NULL, NET_WARN);

        src_stream = pi_fopen(src_filepath, "r");
        BREAK_IF(src_stream == NULL, NET_WARN);

        /* 读取首行，通过匹配SSITAG过滤文件头的字节顺序标记 */
        BREAK_IF(fgets(line, sizeof(line), src_stream) == NULL, NET_INFO);
        pstr = strstr(line, WEBSRV_SSITAG_BEGIN);
        if ( pstr != NULL )
        {
            snprintf(line, sizeof(line), "%s", pstr);
        }

        do
        {
            if ( parsing == 0 )
            {
                memset(module, 0, sizeof(module));
                if ( sscanf(line, WEBSRV_SSITAG_BEGIN "%63[^-]" WEBSRV_SSITAG_END, module) == 1 )
                {
                    if ( STRING_IS_EMPTY(parms) || strcasecmp(module, parms) == 0 || strcasecmp(module, "EXTERN") == 0 )
                    {
                        webom_group = websrv_omstr_get_group(module);
                        if ( webom_group == NULL )
                        {
                            if ( STRING_IS_EMPTY(parms) )
                            {
                                NET_TRACE("global load(%s), ignored module(%s)", src_filepath, module);
                            }
                            else
                            {
                                NET_WARN("load(%s?%s), no search group", src_filepath, parms);
                            }
                        }
                        NET_TRACE("parse module(%s) start", module);
                        parsing = 1;
                    }
                }
            }
            else
            {
                if ( sscanf(line, WEBSRV_SSITAG_BEGIN "%63[^-]" WEBSRV_SSITAG_END, module) == 1 )
                {
                    NET_TRACE("parse module(%s) end", module);
                    parsing = 0;
                    continue;
                }

                if ( sscanf(line, "%127[^<]" WEBSRV_SSITAG_BEGIN "%127[^-]" WEBSRV_SSITAG_END "%127[^\n]", prefix, ssicmd, suffix) == 3 )
                {
                    nbuf = websrv_process_ssicmd(ptask, webom_group, ssicmd, priv->iobuf, sizeof(priv->iobuf));
                    if ( nbuf >= 0 && nbuf < sizeof(priv->iobuf))
                    {
                        priv->iobuf[nbuf] = '\0';
                    }
                    else
                    {
                        NET_WARN("process ssicmd(%s) failed", ssicmd);
                        priv->iobuf[0] = '\0';
                    }
                    rlen += fprintf(dst_stream, "%s%s%s\n", prefix, priv->iobuf, suffix);
                }
                else
                {
                    rlen += fprintf(dst_stream, "%s", line);
                }
            }
        }
        while ( fgets(line, sizeof(line), src_stream) );
    }
    while ( 0 );

    if ( src_stream != NULL )
    {
        pi_fclose(src_stream);
    }
    if ( dst_stream != NULL )
    {
        pi_fclose(dst_stream);
    }
    return rlen;
}

/**
 * @brief       EWS process omstr.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   setcmd  : The group of this setcmd.
 * @return      reply value
 * @retval      >=0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_process_omstr(HTTP_TASK_S* ptask, const char* setcmd, const char** rcode)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char    val[1024];
    char    str[64];
    char*   ptr = NULL;
    int32_t idx = 0;
    int32_t ret = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    memset(str, 0, sizeof(str));
    memset(val, 0, sizeof(val));
    sscanf(setcmd, "%63[^=]=%1023s", str, val);
    if ( str[0] != '\0' )
    {
        ptr = strrchr(str, '.');
        if ( ptr != NULL )
        {
            *ptr++ = '\0';
            NET_DEBUG("str(%s) ptr(%s) from client(%u->%u)", str, ptr, ptask->r_port, ptask->l_port);
            idx = atoi(ptr);
        }
        ret = websrv_omstr_set_val(&(priv->webomctx), str, idx, val);
        if ( -1 == ret )
        {
            *rcode = http_status_string(400);
        }
    }
    else
    {
        NET_WARN("invalid setcmd(%s) from client(%u->%u)", setcmd, ptask->r_port, ptask->l_port);
    }

    return ret;
}

/**
 * @brief       EWS verify author.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   author   :The group of this setcmd.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_verify_author(HTTP_TASK_S* ptask, const char* author)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char*   auth_pswd = NULL;
    char    auth_user[96];
    char    password[64];
    int32_t ret = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    do
    {
        memset(auth_user, 0, sizeof(auth_user));
        base64_decode(author, strlen(author), auth_user, sizeof(auth_user));

        auth_pswd = strchr(auth_user, ':');
        BREAK_IF(auth_pswd == NULL, NET_WARN);
        *auth_pswd++ = '\0';

        memset(password, 0, sizeof(password));
#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
        for ( uint8_t admin_id = 0; admin_id < ADMIN_ID_NUM; ++admin_id )
        {
            if ( strcmp(auth_user, s_web_admin_name[admin_id]) == 0 )
            {
                switch ( admin_id )
                {
                    case ADMIN_ID_SEC: netdata_get_web_sec_pswd(DATA_MGR_OF(s_websrv_ctx), password, sizeof(password)); break;
                    case ADMIN_ID_AUD: netdata_get_web_aud_pswd(DATA_MGR_OF(s_websrv_ctx), password, sizeof(password)); break;
                    case ADMIN_ID_SYS: netdata_get_web_sys_pswd(DATA_MGR_OF(s_websrv_ctx), password, sizeof(password)); break;
                    default: break;
                }

                if ( priv->admin_id != admin_id )
                {
                    NET_INFO("user category update(%u->%u)", priv->admin_id, admin_id);
                    priv->admin_id = admin_id;
                }
                break;
            }
        }
#else
        char username[64];
        netdata_get_web_user(DATA_MGR_OF(s_websrv_ctx), username, sizeof(username));
        if ( strcmp(auth_user, username) == 0 )
        {
            netdata_get_web_pswd(DATA_MGR_OF(s_websrv_ctx), password, sizeof(password));
        }
#endif
        if ( STRING_IS_EMPTY(password) )
        {
            NET_WARN("username(%s) no exist from client(%u->%u)", auth_user, ptask->r_port, ptask->l_port);
            break;
        }

        if ( strcmp(auth_pswd, password) != 0 )
        {
            NET_WARN("password(%s-%s) mismatch from client(%u->%u)", auth_pswd, password, ptask->r_port, ptask->l_port);
            break;
        }
        ret = 0;
    }
    while ( 0 );

    if ( ret < 0 )
    {
        NET_INFO("author(%s) mismatch from client(%u->%u)", author, ptask->r_port, ptask->l_port);
    }
    return ret;
}

/**
 * @brief       EWS verify author.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   author   :The group of this setcmd.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> KaiYong
 * @date        2024-10-25
 */
static int32_t websrv_verify_airprint_author(HTTP_TASK_S* ptask, const char* author)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char*   auth_pswd = NULL;
    char    auth_user[96];
    char    password[64];
    int32_t ret = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    do
    {
        memset(auth_user, 0, sizeof(auth_user));
        base64_decode(author, strlen(author), auth_user, sizeof(auth_user));

        auth_pswd = strchr(auth_user, ':');
        BREAK_IF(auth_pswd == NULL, NET_WARN);
        *auth_pswd++ = '\0';

        char username[64];
        AIRPRINT_USERLIST_S   userlist;
        memset(&userlist, 0x00, sizeof(userlist));
        netdata_get_airprint_userlist(DATA_MGR_OF(s_websrv_ctx), &userlist, sizeof(userlist));
        for ( uint8_t i = 0; i < MAX_AIRPRINT_USERINFO; ++i )
        {
            memset(username, 0, sizeof(username));
            memset(password, 0, sizeof(password));
            ret = sscanf(userlist.userinfo[i], "{\"user\":\"%63[^\"]\",\"pswd\":\"%63[^\"]\"}", username, password);

            if ( ret == 2 && strcmp(auth_user, username) == 0 )
            {
                NET_DEBUG("username(%s), password(%s)", username, password);
                netdata_set_web_login_sit(DATA_MGR_OF(s_websrv_ctx), i);
                break;
            }
        }

        if ( STRING_IS_EMPTY(password) )
        {
            NET_WARN("username(%s) no exist from client(%u->%u)", auth_user, ptask->r_port, ptask->l_port);
            break;
        }

        if ( strcmp(auth_pswd, password) != 0 )
        {
            NET_WARN("password(%s-%s) mismatch from client(%u->%u)", auth_pswd, password, ptask->r_port, ptask->l_port);
            break;
        }
        ret = 0;
    }
    while ( 0 );

    if ( ret < 0 )
    {
        NET_INFO("author(%s) mismatch from client(%u->%u)", author, ptask->r_port, ptask->l_port);
    }
    return ret;
}

/**
 * @brief       EWS check cookie.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_check_cookie(HTTP_TASK_S* ptask)
{
#define WEBSRV_COOKIE_DECRYPT_KEY   "\xf1\xe0\xd3\xc2\xb5\xa4\x97\x86"
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    const char* cookie;
    const char* ptr;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    cookie = http_task_search_header_field(ptask, "Cookie");
    if ( cookie != NULL )
    {
#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
        ptr = strstr(cookie, "info=");
        if ( ptr != NULL )
        {
            sscanf(ptr, "info=%127[^;]", priv->cookie_info);
            if ( priv->cookie_info[0] != '\0' )
            {
                uint8_t ciphertext[64];
                uint8_t decryptext[32];
                size_t  num;

                num = convert_string_to_hexbytes(priv->cookie_info, ciphertext, sizeof(ciphertext));
                memset(decryptext, 0, sizeof(decryptext));
                des_cbc_decode_crypt(decryptext, ciphertext, num, (uint8_t *)WEBSRV_COOKIE_DECRYPT_KEY, NULL);
                for ( uint8_t i = 0; i < ADMIN_ID_NUM; ++i )
                {
                    if ( strstr((const char *)(decryptext + 8), s_web_admin_name[i]) != NULL )
                    {
                        NET_TRACE("match user category(%s) from client(%u->%u)", s_web_admin_name[i], ptask->r_port, ptask->l_port);
                        priv->admin_id = i;
                        netdata_set_web_admin_id(DATA_MGR_OF(s_websrv_ctx), priv->admin_id);
                        break;
                    }
                }
            }
        }
#endif /* CONFIG_NET_SAFETY_MACHINE */

        ptr = strstr(cookie, "author=");
        if ( ptr != NULL )
        {
            sscanf(ptr, "author=%127[^;]", priv->author_info);
        }
    }

    return 0;
}

/**
 * @brief       EWS check author.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_check_author(HTTP_TASK_S* ptask)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    const char* author = NULL;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( STRING_IS_EMPTY(priv->author_info) )
    {
        author = http_task_search_header_field(ptask, "Author");
        if ( STRING_NO_EMPTY(author) )
        {
            snprintf(priv->author_info, sizeof(priv->author_info), "%s", author);
        }
    }

    NET_TRACE("author_info(%s) from client(%u->%u)", priv->author_info, ptask->r_port, ptask->l_port);
    if ( STRING_NO_EMPTY(priv->author_info) && websrv_verify_author(ptask, priv->author_info) == 0 ) /* 基于cookie和author字段解析到授权登陆信息，校验授权信息 */
    {
        priv->authorized = 1;
    }
    else
    {
        priv->authorized = 0;
    }
    NET_TRACE("authorized(%u) from client(%u->%u)", priv->authorized, ptask->r_port, ptask->l_port);

    return 0;
}

/**
 * @brief       EWS check host.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Jun
 * @date        2024-5-11
 */
static int32_t websrv_check_host(HTTP_TASK_S* ptask)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    IFACE_ID_E  ifid = IFACE_ID_ANY;
    const char* pstr = NULL;
    char        localname[LOCALNAME_LEN];
    char        ipv6_addr[IPV6_ADDR_LEN];
    char        host_name[HOSTNAME_LEN];
    char        host_val[LOCALNAME_LEN];
    int32_t     ret = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    do
    {
        BREAK_IF(ptask->r_port == ptask->l_port && ptask->l_port == 0, NET_NONE); /* 允许IPP-over-USB请求 */
        pstr = http_task_search_header_field(ptask, "Host");
        parse_uri_host(pstr, NULL, NULL, host_val, sizeof(host_val)); /* 处理Host字段中包含端口号的情况，拆分出主机名部分 */

        netdata_get_hostname(DATA_MGR_OF(s_websrv_ctx), host_name, sizeof(host_name));
        snprintf(localname, sizeof(localname), "%s.local.", host_name);

        if ( strlen(host_val) + 1 == strlen(localname) )
        {
            BREAK_IF(strncasecmp(localname, host_val, strlen(host_val)) == 0, NET_NONE); /* 允许请求主机名是 主机名.local */
        }
        BREAK_IF(strcasecmp(localname, host_val) == 0, NET_NONE);
        BREAK_IF(strcasecmp(host_name, host_val) == 0, NET_NONE);

        ifid = netdata_get_ifid_by_ipv4_addr(DATA_MGR_OF(s_websrv_ctx), host_val); /* Host值为IPv4地址时，校验是否本机IP */
        BREAK_IF(STRING_NO_EMPTY(IFACE_NAME(ifid)), NET_NONE);

        if ( host_val[0] == '[' && host_val[strlen(host_val) - 1] == ']' ) /* "[xxxx::xxxx]" IPv6地址去方括号 */
        {
            memset(ipv6_addr, 0, sizeof(ipv6_addr));
            sscanf(host_val + 1, "%63[^]]", ipv6_addr);
            ifid = netdata_get_ifid_by_ipv6_addr(DATA_MGR_OF(s_websrv_ctx), ipv6_addr);
            BREAK_IF(STRING_NO_EMPTY(IFACE_NAME(ifid)), NET_NONE);
        }
        ret = -1; /* Host字段与当前主机名或IP地址均不匹配 */
    }
    while ( 0 );

    if ( ret != 0 )
    {
        NET_WARN("request host(%s) is invalid from client(%u->%u)", pstr, ptask->r_port, ptask->l_port);
    }
    return ret;
}

/**
 * @brief       EWS process reqbody.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   data   :  The body data .
 * @param[in]   ndata   : The  length of body data.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_process_reqbody(HTTP_TASK_S* ptask, const char* data, size_t ndata)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    if ( priv->received + ndata > sizeof(priv->iobuf) )
    {
        NET_WARN("request body overlength(%u + %u) from client(%u->%u)\n", priv->received, ndata, ptask->r_port, ptask->l_port);
        return -1;
    }

    memcpy(priv->iobuf + priv->received, data, ndata);
    priv->received += ndata;

    return 0;
}

/**
 * @brief       EWS set properties.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_set_properties(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    NET_CTX_S*  net_ctx = s_websrv_ctx->net_ctx;
    char*       psrc0 = NULL;
    char*       psrc1 = NULL;
    char        timebuf[128];
    int32_t     result = 0;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    for ( psrc0 = psrc1 = priv->iobuf; STRING_NO_EMPTY(psrc1); psrc0 = psrc1 )
    {
        psrc1 = strchr(psrc0, '&');
        if ( psrc1 != NULL )
        {
            *psrc1++ = '\0';
        }
        result = websrv_process_omstr(ptask, psrc0, rcode);
        if ( result != 0 )
        {
            *rcode = http_status_string(400);
            NET_DEBUG("process omstr(%s) result(%d)", psrc0, result);
            break;
        }
    }

    if ( result == 0 )
    {
        do
        {
            for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
            {
                if ( priv->webomctx.ipv4_conf[ifid] != NULL )
                {
                    netctx_update_ipv4_config(net_ctx, ifid, priv->webomctx.ipv4_conf[ifid]);
                }
                if ( priv->webomctx.dnsv4_conf[ifid] != NULL )
                {
                    netctx_update_dnsv4_config(net_ctx, ifid, priv->webomctx.dnsv4_conf[ifid]);
                }
                if ( priv->webomctx.ipv6_conf[ifid] != NULL )
                {
                    netctx_update_ipv6_config(net_ctx, ifid, priv->webomctx.ipv6_conf[ifid]);
                }
            }

            if ( priv->webomctx.wifi_conf != NULL )
            {
                netctx_connect_wifi_station(net_ctx, priv->webomctx.wifi_conf, 0);
            }

            if ( priv->webomctx.time_conf != NULL )
            {
                priv->webomctx.modfiy_data = 0;
                strftime(timebuf, sizeof(timebuf), "%Y-%m-%d %H:%M:%S", priv->webomctx.time_conf);
                NET_DEBUG("update system time(%s) tm_gmtoff(%ld) tm_zone(%s)", timebuf, priv->webomctx.time_conf->tm_gmtoff, priv->webomctx.time_conf->tm_zone);
                BREAK_IF(systime_set(priv->webomctx.time_conf) < 0, NET_WARN);
                NETEVT_NOTIFY_S(EVT_TYPE_PLATFORM_SYSTEM_TIME_SYNC, NULL);
            }
        }
        while ( 0 );
    }

    for ( IFACE_ID_E ifid = IFACE_ID_ETH; ifid < IFACE_ID_NUM; ++ifid )
    {
        DESTRUCT_CONFIG(priv->webomctx.ipv4_conf[ifid]);
        DESTRUCT_CONFIG(priv->webomctx.dnsv4_conf[ifid]);
        DESTRUCT_CONFIG(priv->webomctx.ipv6_conf[ifid]);
    }
    DESTRUCT_CONFIG(priv->webomctx.wifi_conf);
    DESTRUCT_CONFIG(priv->webomctx.time_conf);

    switch ( result )
    {
    case WEBSRV_OK:
        {
            rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Setting Properties','Result':'0'}");
            break;
        }
    case WEBSRV_WPS_CONNECTION_OK:
        {
            cJSON*  json_root = cJSON_CreateObject();
            char*   json_str = NULL;
            char    temp_str[128];
            char    sta_ssid[128];

            RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, -1);

            netdata_get_sta_ssid(DATA_MGR_OF(s_websrv_ctx), sta_ssid, sizeof(sta_ssid));
            cJSON_AddExtendToObject(json_root, "Ssid"  , "%s", sta_ssid);
            cJSON_AddExtendToObject(json_root, "Result", "%d", result);
            json_str = cJSON_PrintUnformatted(json_root);
            if ( json_str != NULL )
            {
                rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", json_str);
                NET_DEBUG("reply(%s)", priv->iobuf);
                pi_free(json_str);
            }
            else
            {
                NET_WARN("cJSON_PrintUnformatted failed!");
            }
            cJSON_Delete(json_root);
            break;
        }
    default:
        {
            rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Set property error','Result':'%d'}", result);
            break;
        }
    }
    return rlen;
}

/**
 * @brief       EWS show tlscer.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_show_tlscert(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    TLS_CERT_INFO_S cert_info;
    cJSON*          json_root = NULL;
    char*           json_str = NULL;
    char            temp_str[64];
    int32_t         use_main = 1;
    int32_t         rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    if ( nettls_get_cert_info(&cert_info, &use_main) == 0 )
    {
        cJSON_AddStringToObject(json_root , "Operation" , "show");
        cJSON_AddStringToObject(json_root , "Firmware"  , use_main ? "1" : "0");
        cJSON_AddExtendToObject(json_root , "Version"   , "%ld", cert_info.ver);
        cJSON_AddStringToObject(json_root , "SignAlg"   , cert_info.sign_alg);
        cJSON_AddStringToObject(json_root , "User"      , cert_info.user);
        cJSON_AddStringToObject(json_root , "Award"     , cert_info.ca);
        cJSON_AddExtendToObject(json_root , "Data"      , "%s-%s", cert_info.date_start, cert_info.date_end);
        cJSON_AddExtendToObject(json_root , "Result"    , "%d", WEBSRV_CERTIFICATE_OK);
    }
    else
    {
        cJSON_AddStringToObject(json_root , "Operation" , "show");
        cJSON_AddStringToObject(json_root , "Firmware"  , "1");
        cJSON_AddExtendToObject(json_root , "Result"    , "%d", WEBSRV_CERTIFICATE_FAIL);
    }

    json_str = cJSON_PrintUnformatted(json_root);
    if ( json_str != NULL )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", json_str);
        NET_DEBUG("reply(%s)", priv->iobuf);
        pi_free(json_str);
    }
    else
    {
        NET_WARN("cJSON_PrintUnformatted failed!");
    }
    cJSON_Delete(json_root);

    return rlen;
}

/**
 * @brief       EWS make tlscert.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_make_tlscert(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    TLS_CERT_SELF_S cert;
    char*           certbuf = NULL;
    int32_t         result = -1;
    int32_t         parsed = 0;
    int32_t         rlen = -1;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    certbuf = (char *)pi_zalloc(priv->received);
    RETURN_VAL_IF(certbuf == NULL, NET_WARN, -1);

    do
    {
        parsed = base64_decode(priv->iobuf, priv->received, certbuf, priv->received);
        BREAK_IF(parsed < 0, NET_WARN);

        /*
         * 拆分下面的字符串和取得证书字段值
         * 2&omCert0=Pantum-3A3DFC&omCert1=pantum&omCert2=firmware&omCert3=dalian&omCert4=liaoningsheng&omCert5=CN&omCert6=20220525113630&omCert7=20491010113630
         */
        NET_DEBUG("decode certbuf(%s)", certbuf);
        memset(&cert, 0, sizeof(cert));
        parsed = sscanf(certbuf, "%d&omCert0=%63[^&]&omCert1=%63[^&]&omCert2=%63[^&]&omCert3=%63[^&]&omCert4=%63[^&]&omCert5=%3[^&]&omCert6=%15[^&]&omCert7=%15s",
                &cert.flag, cert.name, cert.org, cert.unit, cert.city, cert.state, cert.country, cert.start, cert.end);
        BREAK_IF(parsed != 9, NET_WARN);

        NET_DEBUG("cert.name    : %s", cert.name);
        NET_DEBUG("cert.org     : %s", cert.org);
        NET_DEBUG("cert.unit    : %s", cert.unit);
        NET_DEBUG("cert.city    : %s", cert.city);
        NET_DEBUG("cert.state   : %s", cert.state);
        NET_DEBUG("cert.country : %s", cert.country);
        NET_DEBUG("cert.start   : %s", cert.start);
        NET_DEBUG("cert.end     : %s", cert.end);
        NET_DEBUG("cert.flag    : %d", cert.flag);
        // 依次检查自生成证书参数合法性，可能存在RCE漏洞利用
        if ( !check_string_format(cert.name, REGULAR_EXPRESSIONS_POLICY_02) ||
             !check_string_format(cert.org, REGULAR_EXPRESSIONS_POLICY_02)  ||
             !check_string_format(cert.unit, REGULAR_EXPRESSIONS_POLICY_02) ||
             !check_string_format(cert.city, REGULAR_EXPRESSIONS_POLICY_03) ||
             !check_string_format(cert.state, REGULAR_EXPRESSIONS_POLICY_03)||
             !check_string_format(cert.country, REGULAR_EXPRESSIONS_POLICY_03) )
        {
            return -1;
        }
        result = nettls_make_cert(&cert, WEBSRV_TEMP_DIR "/tlscert.pem");
    }
    while ( 0 );

    if ( result == 0 )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'webpage/tlscert.pem'}");
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'ERROR'}");
    }
    pi_free(certbuf);
    return rlen;
}

/**
 * @brief       EWS install tlscert.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_install_tlscert(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char    buff[MAX_UPLOAD_FILE_SIZE];
    char*   sslcertkey = NULL;
    char*   filename = NULL;
    char*   boudary = NULL;
    char*   ptr = NULL;
    int32_t result = WEBSRV_FILE_UPLOAD_FAIL;
    size_t  nboudary = 0;
    size_t  filesize = 0;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    do
    {
        boudary = strstr(priv->iobuf, "--");
        ptr = strstr(priv->iobuf, "\r\n");
        BREAK_IF(boudary == NULL || ptr == NULL, NET_WARN);

        *ptr = '\0';
        ptr += 2;
        nboudary = strlen(boudary);

        sslcertkey = strstr(ptr, "name=\"sslcertkey\"\r\n\r\n");
        if ( sslcertkey != NULL )
        {
            sslcertkey += strlen("name=\"sslcertkey\"\r\n\r\n");
            ptr = strstr(sslcertkey, "\r\n");
            if ( ptr != NULL )
            {
                *ptr = '\0';
                ptr += 2;
                ptr = strstr(ptr, boudary);
            }
        }

        BREAK_IF(sslcertkey == NULL || ptr == NULL, NET_WARN);

        NET_DEBUG("Certificate key(%s)", sslcertkey);
        ptr += nboudary;
        ptr = strstr(ptr, "filename=");
        if ( ptr != NULL )
        {
            ptr += strlen("filename=");
        }

        while ( ptr && *ptr != '\0' )
        {
            if ( filename == NULL && *ptr == '\"' )
            {
                filename = ++ptr;
            }
            else if ( *ptr == '\"' )
            {
                break;
            }
            ptr++;
        }

        if ( ptr && filename && '\"' == *ptr )
        {
            *ptr++ = '\0';
        }

        BREAK_IF(filename == NULL, NET_WARN);
        NET_DEBUG("filename(%s)", filename);

        ptr = strstr(ptr, "\r\n\r\n");
        if ( ptr != NULL )
        {
            ptr += strlen("\r\n\r\n");
        }

        BREAK_IF(ptr == NULL, NET_WARN);

        filesize = priv->iobuf + priv->received - ptr;
        NET_DEBUG("Certificate file size(%d)", filesize);
        if ( MAX_UPLOAD_FILE_SIZE <= filesize )
        {
            NET_WARN("Check the certificate file too big!");
            result = WEBSRV_FILE_UPLOAD_TOOBIG;
            break;
        }

        memcpy(buff, ptr, filesize);
        filesize -= nboudary + 6;
        NET_DEBUG("the end data(%s)", buff + filesize);
        if ( memcmp(boudary, buff + filesize + 2, nboudary) == 0 )
        {
            struct stat s;

            FILE* stream = pi_fopen(P12_CERTFILE, "w+");
            BREAK_IF(stream == NULL, NET_WARN);

            size_t wlen = fwrite(buff, 1, filesize, stream);
            pi_fclose(stream);
            if ( wlen != filesize )
            {
                NET_WARN("write to %s failed: %d<%s>", P12_CERTFILE, errno, strerror(errno));
                break;
            }

            BREAK_IF(check_string_format(sslcertkey, REGULAR_EXPRESSIONS_POLICY_02) == 0, NET_WARN);
            BREAK_IF(nettls_reset_cert_conf() < 0, NET_WARN);

            remove(TLS_CERTFILE);
            pi_runcmd(buff, sizeof(buff), 0, "openssl pkcs12 -in %s -out %s -passin pass:'%s' -passout pass:", P12_CERTFILE, TLS_CERTFILE, sslcertkey);
            printf("runcmd result: %s\n", buff);
            BREAK_IF(stat(TLS_CERTFILE, &s) != 0 || s.st_size <= 0, NET_WARN);

            remove(P12_KEYFILE);
            pi_runcmd(buff, sizeof(buff), 0, "openssl pkcs12 -in %s -nocerts -nodes -out %s -passin pass:'%s'", P12_CERTFILE, P12_KEYFILE, sslcertkey);
            printf("runcmd result: %s\n", buff);
            BREAK_IF(stat(P12_KEYFILE, &s) != 0 || s.st_size <= 0, NET_WARN);

            copy_file(TLS_CERTFILE, P12_KEYFILE, "a+");
            if ( (stat(TLS_CERTFILE, &s) == 0) && (s.st_size > 0) && (s.st_size <= MAX_UPLOAD_FILE_SIZE) )
            {
                int32_t ret = nettls_install_cert(TLS_CERTFILE, sslcertkey);
                if ( ret != 0 )
                {
                    NET_WARN("install cert(%s) failed(%d): %d<%s>", TLS_CERTFILE, ret, errno, strerror(errno));
                }
                else
                {
                    result = WEBSRV_FILE_UPLOAD_OK;
                }
            }
        }
    }
    while ( 0 );

    return snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'docertificate','Result':'%d'}", result);
}

/**
 * @brief       EWS delete tlscert.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_delete_tlscert(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    nettls_delete_cert();

    return snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'delete','Result':'%d'}", WEBSRV_CERTIFICATE_OK);
}

/**
 * @brief       EWS verify maintenmance pwd.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_verify_maintenmance_pwd(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
#define MAINTENMANCE_PASSWORD   "**36"
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char    base64data[32];
    int32_t result;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    base64_encode(MAINTENMANCE_PASSWORD, strlen(MAINTENMANCE_PASSWORD), base64data, sizeof(base64data));
    if ( strcmp(priv->iobuf, base64data) == 0 )
    {
        result = -28;
    }
    else
    {
        result = -29;
    }

    return snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Set property error','Result':'%d'}", result);
}

/**
 * @brief       EWS verify maintenmance log.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_report_maintenmance_log(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);

    RETURN_VAL_IF(priv == NULL || copy_file(WEBSRV_TEMP_DIR "/records.log", "/settings/pol_log/pol_log_0.log", "w+") != 0, NET_WARN, -1);

    return snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'webpage/records.log','Result':'-27'}");
}

#if CONFIG_NET_SAFETY_MACHINE
static void websrv_export_xc_log_line(const char* file, int32_t startline, char* data, int32_t data_size)
{
    int32_t currentline = 0;
    int32_t totallens = 0;
    int32_t endline = startline + 19;

    char buf[2048];

    FILE* logfp = fopen(file, "rb+");
    if ( logfp == NULL )
    {
        return;
    }

    while ( fgets(buf, sizeof(buf), logfp) != NULL )
    {
        currentline++;
        if ( currentline > endline )
        {
            break;
        }

        if ( currentline >= startline && currentline <= endline )
        {
            totallens += snprintf(data + totallens, (data_size - totallens), "%s", buf);
            data[totallens] = '\n';
            data[totallens + 1] = '\0';
        }
    }
    fclose(logfp);

    return;
}

static int32_t websrv_export_xc_log(const char* file, int32_t file_size, int32_t startline, int32_t totalline, const char* result)
{
    char*  data = NULL;
    char   buffer[16] = {0};
    cJSON* json_root = NULL;
    int32_t malloc_size = 0;
    malloc_size = file_size + 64;

    data = (char*)pi_zalloc(malloc_size);
    if ( data == NULL )
    {
        NET_ERROR("not enough space");
        return -1;
    }
    else
    {
        json_root = cJSON_CreateObject();
        if ( startline > totalline  || startline == 0)
        {
            startline = 1;
        }
        websrv_export_xc_log_line(file, startline, data, malloc_size);
        cJSON_AddStringToObject(json_root, "Log", data);
        pi_free(data);
        data = NULL;
        snprintf(buffer, sizeof(buffer), "%d", startline + 19);
        cJSON_AddStringToObject(json_root, "Current", buffer);
        snprintf(buffer, sizeof(buffer), "%d", totalline);
        cJSON_AddStringToObject(json_root, "Total", buffer);
        cJSON_AddStringToObject(json_root, "Result", result);
        data = cJSON_PrintUnformatted(json_root);
        file_size = strlen(data);
        save_file(file, data, file_size);
        pi_free(data);
        cJSON_free(json_root);
    }
    return file_size;
}
#endif

/**
 * @brief       EWS export taskrecord.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_export_taskrecord(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "taskrecord");
    NET_DEBUG("export taskrecord(%s)", priv->reply_file);
    export_auditlog_to_webpage(priv->reply_file);
    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
#if CONFIG_NET_SAFETY_MACHINE
        int32_t startline = 0;
        const char* omStartline = strstr(priv->iobuf, "startline=");
        if( omStartline != NULL)
        {
            omStartline = omStartline + strlen("startline=");
            startline = strtol(omStartline, NULL, 10);
            printf("startline is %d", startline);
        }
        rlen = websrv_export_xc_log(priv->reply_file, rlen, startline, get_auditlog_number() - 1, "-26");
#endif
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "no data");
        memset(priv->reply_file, 0, sizeof(priv->reply_file));
    }
    *rtype = "application/octet-stream";

    return rlen;
}

/**
 * @brief       EWS export boardrecord.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_export_boardrecord(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "boardrecord");
    export_subboard_verify_event_log_to_webpage(priv->reply_file);
    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
#if CONFIG_NET_SAFETY_MACHINE
        int32_t startline = 0;
        const char* omStartline = strstr(priv->iobuf, "startline=");
        if( omStartline != NULL)
        {
            omStartline = omStartline + strlen("startline=");
            startline = strtol(omStartline, NULL, 10);
            printf("startline is %d", startline);
        }
        rlen = websrv_export_xc_log(priv->reply_file, rlen, startline, get_subboard_verify_event_log_number(), "-28");
#endif
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "no data");
        memset(priv->reply_file, 0, sizeof(priv->reply_file));
    }
    *rtype = "application/octet-stream";

    return rlen;
}

/**
 * @brief       EWS export operatelog.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_export_operatelog(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t     rlen = -1;
    char*  data = NULL;
    char   buffer[16] = {0};
    cJSON* json_root = NULL;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "operatelog");
    export_operatelog_to_webpage(priv->reply_file);
    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
#if CONFIG_NET_SAFETY_MACHINE
        int32_t startline = 0;
        const char* omStartline = strstr(priv->iobuf, "startline=");
        if( omStartline != NULL)
        {
            omStartline = omStartline + strlen("startline=");
            startline = strtol(omStartline, NULL, 10);
            printf("startline is %d", startline);
        }
        rlen = websrv_export_xc_log(priv->reply_file, rlen, startline, get_operatelog_number() - 1, "-31");
#endif
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "no data");
        memset(priv->reply_file, 0, sizeof(priv->reply_file));
    }
    *rtype = "application/octet-stream";

    return rlen;
}

/**
 * @brief       EWS export firmware upgrade log.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2023-12-18
 */
static int32_t websrv_export_upgradelog(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "upgradelog");
    export_fw_upgrade_log_to_webpage(priv->reply_file);
    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "no data");
        memset(priv->reply_file, 0, sizeof(priv->reply_file));
    }
    *rtype = "application/octet-stream";

    return rlen;
}

/**
 * @brief       EWS export device report file.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Kaiyong
 * @date        2023-12-18
 */
int32_t websrv_export_device_report(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    NET_CTX_S*   net_ctx = s_websrv_ctx->net_ctx;
    ROUTER_MSG_S send_msg;
    ROUTER_MSG_S recv_msg;
    TIMEVAL_S    timeout_val;
    struct stat  file_stat;
    char         file_path[256];
    char         file_url[256];
    int32_t      ret = 1;
    int32_t      rlen = -1;
    uint8_t      wait_time = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    NETEVT_NOTIFY_I(EVT_TYPE_POWERMGR_CONFIG, PM_EVENT_DEEP_WAKEUP); /*如果打印机睡眠 唤醒打印机*/
    snprintf(file_url, sizeof(file_url), "webpage/device_report.pdf");
    snprintf(file_path, sizeof(file_path), "/tmp/%s", file_url);
    NET_TRACE("device report file path(%s) from client(%u->%u)", file_path, ptask->r_port, ptask->l_port);

    timeout_val.secs  = 1;
    timeout_val.usecs = 0;
    task_msg_timeout_by_router(MID_NET_GEN_REPORT, &recv_msg, &timeout_val);

    send_msg.msgType   = MSG_INTERNAL_PAGE_PDF;
    send_msg.msg1      = ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO; /*1: generate report */
    send_msg.msg2      = 0;
    send_msg.msg3      = (void *)(file_path);
    send_msg.msgSender = MID_NET_GEN_REPORT;

    RETURN_VAL_IF(task_msg_send_by_router(MID_PRINT_PARSER_IF, &send_msg) < 0, NET_WARN, -1);

    timeout_val.secs  = REPORT_TIME_OUT;
    timeout_val.usecs = 0;
    ret = task_msg_timeout_by_router(MID_NET_GEN_REPORT, &recv_msg, &timeout_val);
    if ( recv_msg.msgType == MSG_INTERNAL_PAGE_PDF_STORE_RESULT && recv_msg.msg1 == ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO && recv_msg.msg2 == 0 && ret == 0 )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'%s'}", file_url);
    }
    else
    {
        NET_WARN("ret(%d) recv_msg.msg2(%u)", ret, recv_msg.msg2);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'url':'ERROR'}");
    }

    /* send generate device result to print_module*/
    send_msg.msgType   = MSG_INTERNAL_PAGE_PDF_STORE_RESULTS_ACK;
    send_msg.msg1      = ePRINT_PARSER_INTERNAL_PAGE_STORE_PRINTER_INFO; /*1: generate report */
    send_msg.msg2      = (ret == 1) ? 0 : 1;
    send_msg.msg3      = (void *)NULL;
    send_msg.msgSender = MID_NET_GEN_REPORT;
    task_msg_send_by_router(MID_PRINT_PARSER_IF, &send_msg);

    return rlen;
}

/**
 * @brief       EWS remote device screen capture.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Kaiyong
 * @date        2024-6-18
 */
static int32_t websrv_capture_remote_screen(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    NETSTS_PACKET_S  packet;
    struct stat      file_stat;
    FILE*            f_rsc = NULL;
    char             hdrbuf[32];
    uint32_t*        rgb565;
    int32_t          screen_size;
    int32_t          size;
    int32_t          rlen;
    uint32_t         rgb;
    uint8_t          buf[6];
	int32_t 		 ret;
	unsigned char*	 screen_addr;

    RETURN_VAL_IF(priv == NULL || s_websrv_ctx->fb_handle == NULL, NET_WARN, -1);
	do
	{
		ret = 0;
		pi_mutex_lock(s_websrv_ctx->mutex);
	    pi_hal_fb_virt(s_websrv_ctx->fb_handle, &screen_addr);

	    BREAK_VAL_IF(screen_addr == NULL || netsts_take_packet(&packet) < 0 ||
	                 s_websrv_ctx->fb_info.color_type != HAL_FB_COLOR_RGB565 ||
	                 s_websrv_ctx->screenbuf == NULL, NET_WARN, ret = -1);  //现仅支持RGB565 数据

	    screen_size = s_websrv_ctx->screen_size / 4;
	    snprintf(hdrbuf, sizeof(hdrbuf), "%u", netsts_check_status_level(&packet, STATUS_ID_TYPE_INFO));
	    http_task_append_resp_headers(ptask, "SysStatus4Screen", hdrbuf);

		pi_memset(s_websrv_ctx->screenbuf, 0x00, s_websrv_ctx->screen_size);
		pi_memcpy(s_websrv_ctx->screenbuf, screen_addr, s_websrv_ctx->screen_size);
		rgb565 = (uint32_t*)s_websrv_ctx->screenbuf;

	    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "screen_capture");
		remove(priv->reply_file);  // 先删除可能存在的旧文件
	    f_rsc = pi_fopen(priv->reply_file, "w");

	    BREAK_VAL_IF(f_rsc == NULL, NET_WARN, ret = -1);

		fflush(f_rsc);				// 确保文件流处于干净状态
		rewind(f_rsc);				// 重置文件指针

	    for ( size = screen_size; size > 0; --size )
	    {
	        rgb = *rgb565;

	        buf[0] = ( rgb  <<  3);
	        buf[1] = ( rgb  >>  3) & 0xFC;
	        buf[2] = ( rgb  >>  8) & 0xF8;

	        buf[3] = ( rgb  >>  13) & 0xF8;
	        buf[4] = ( rgb  >>  19) & 0xFC;
	        buf[5] = ( rgb  >>  24) & 0xF8;

	        pi_fwrite((const void*)buf, sizeof(buf), 1, f_rsc);

	        rgb565++;
	    }
	    pi_fclose(f_rsc);
	}
	while ( 0 );
	pi_mutex_unlock(s_websrv_ctx->mutex);
	RETURN_VAL_IF( ret < 0, NET_WARN, -1);

    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
    }
    else
    {
        NET_WARN("invalid file(%s)", priv->reply_file);
        rlen = -1;
    }
    *rtype = "image/bmp";

    return rlen;
}

/**
 * @brief       EWS remote device touch screen.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Kaiyong
 * @date        2024-06-18
 */
static int32_t websrv_touch_remote_screen(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    NET_CTX_S*          net_ctx = s_websrv_ctx->net_ctx;
    cJSON*              mouse_info _autofree_cjson_ = NULL;
    cJSON*              mouse_item;
    HAL_TOUCH_EVENT_S   touch_ev;
    NETSTS_PACKET_S     packet;
    int32_t             top;
    int32_t             left;
    int32_t             key;
    int32_t             ret;

    RETURN_VAL_IF(priv == NULL || s_websrv_ctx->tp_handle == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);
    NET_DEBUG("iobuf(%s)", priv->iobuf);

    //data type {\"type\":\"activeLCD\",\"top\":0,\"left\":0,\"key\":330}
    RETURN_VAL_IF((mouse_info = cJSON_Parse(priv->iobuf)) == NULL, NET_WARN, -1);

    mouse_item = cJSON_GetObjectItemCaseSensitive(mouse_info, "top");
    RETURN_VAL_IF(mouse_item == NULL && !cJSON_IsNumber(mouse_item), NET_WARN, -1);
    top = mouse_item->valueint;

    mouse_item = cJSON_GetObjectItemCaseSensitive(mouse_info, "left");
    RETURN_VAL_IF(mouse_item == NULL && !cJSON_IsNumber(mouse_item), NET_WARN, -1);
    left = mouse_item->valueint;

    mouse_item = cJSON_GetObjectItemCaseSensitive(mouse_info, "key");
    RETURN_VAL_IF(mouse_item == NULL && !cJSON_IsNumber(mouse_item), NET_WARN, -1);
    key = mouse_item->valueint;

    NET_DEBUG("left(%d) top(%d) key(0x%x)", left, top, key);
    touch_ev.touch_action = HAL_TOUCH_ACTION_RELEASE;
    touch_ev.touch_type = HAL_TOUCH_COR;
    switch ( key )
    {
    case 0x80:
        touch_ev.touch_action = HAL_TOUCH_ACTION_PRESS;
        touch_ev.x = left;
        touch_ev.y = top;
        ret = pi_hal_touch_write(s_websrv_ctx->tp_handle, touch_ev);
        NET_DEBUG("HAL_TOUCH_ACTION_PRESS result(%d)", ret);
        break;
    case 0x81:
        touch_ev.touch_action = HAL_TOUCH_ACTION_RELEASE;
        touch_ev.x = left;
        touch_ev.y = top;
        ret = pi_hal_touch_write(s_websrv_ctx->tp_handle, touch_ev);
        NET_DEBUG("HAL_TOUCH_ACTION_RELEASE result(%d)", ret);
        break;
    case 0x82:
        touch_ev.touch_action = HAL_TOUCH_ACTION_MOVE;
        touch_ev.x = left;
        touch_ev.y = top;
        ret = pi_hal_touch_write(s_websrv_ctx->tp_handle, touch_ev);
        NET_DEBUG("HAL_TOUCH_ACTION_MOVE result(%d)", ret);
        break;
    case 0x1e:
    case 0x30:
    case 0x2e:
        /* 对应三个按钮：返回、主页、帮助 */
        RETURN_IF(netsts_take_packet(&packet) < 0, NET_WARN);
        /* 鼠标按下或松开时如果触摸屏是熄灭（系统休眠）状态不触发点击改为唤醒屏幕 */
        if ( netsts_check_status_level(&packet, STATUS_ID_TYPE_INFO) == STATUS_I_PRINT_SLEEP )
        {
            NETEVT_NOTIFY_I(EVT_TYPE_POWERMGR_CONFIG, PM_EVENT_DEEP_WAKEUP);
        }
    case 0x14a:
        touch_ev.touch_action = HAL_TOUCH_ACTION_RELEASE;
        NET_DEBUG("action(%d)", touch_ev.touch_action); ///< TODO: touch_ev.touch_action = ???
        touch_ev.x = left;
        touch_ev.y = top;
        ret = pi_hal_touch_write(s_websrv_ctx->tp_handle, touch_ev);
        NET_DEBUG("result(%d)", ret);
        break;
    default:
        break;
    }

    return 0;
}

/**
 * @brief       EWS export boardrecord.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_export_htsrecord(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "htsrecord");
    httc_export_logfile(HTTC_SUCCESS_LOG, priv->reply_file);
    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
#if CONFIG_NET_SAFETY_MACHINE
        int32_t startline = 0;
        const char* omStartline = strstr(priv->iobuf, "startline=");
        if( omStartline != NULL)
        {
            omStartline = omStartline + strlen("startline=");
            startline = strtol(omStartline, NULL, 10);
            printf("startline is %d", startline);
        }
        rlen = websrv_export_xc_log(priv->reply_file, rlen, startline, HttcGetLogNumber(HTTC_SUCCESS_LOG) - 1, "-32");
#endif
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "no data");
        memset(priv->reply_file, 0, sizeof(priv->reply_file));
    }
    *rtype = "application/octet-stream";

    return rlen;
}

/**
 * @brief       EWS export htfrecord.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_export_htfrecord(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "htfrecord");
    httc_export_logfile(HTTC_FAIL_LOG, priv->reply_file);
    if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        rlen = (int32_t)file_stat.st_size;
#if CONFIG_NET_SAFETY_MACHINE
        int32_t startline = 0;
        const char* omStartline = strstr(priv->iobuf, "startline=");
        if( omStartline != NULL)
        {
            omStartline = omStartline + strlen("startline=");
            startline = strtol(omStartline, NULL, 10);
            printf("startline is %d", startline);
        }
        rlen = websrv_export_xc_log(priv->reply_file, rlen, startline, HttcGetLogNumber(HTTC_FAIL_LOG) - 1, "-33");
#endif
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "no data");
        memset(priv->reply_file, 0, sizeof(priv->reply_file));
    }
    *rtype = "application/octet-stream";

    return rlen;
}

/**
 * @brief       EWS process "/WifiControlbyAPP".
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2024-01-29
 */
static int32_t websrv_control_wifi(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    WIFI_CONN_CONF_S wifi_conf = { .sec_mode = SEC_AUTO };
    NET_CTX_S*       net_ctx = s_websrv_ctx->net_ctx;
    cJSON*           json_root = NULL;
    cJSON*           json_item = NULL;
    cJSON*           json_temp = NULL;
    char             link_action[32] = {0};
    char             password[89] = {0};
    int32_t          rlen;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    NET_DEBUG("iobuf(%s)", priv->iobuf);

    do
    {
        json_root = cJSON_Parse(priv->iobuf);
        BREAK_IF(json_root == NULL, NET_WARN);

        json_item = cJSON_GetObjectItem(json_root, "request");
        BREAK_IF(json_item == NULL, NET_WARN);

        CJSON_GET_VALUESTRING(json_item, "owner",    s_websrv_ctx->owner);
        CJSON_GET_VALUESTRING(json_item, "action",   link_action);
        CJSON_GET_VALUESTRING(json_item, "ssid",     wifi_conf.ssid);
        CJSON_GET_VALUESTRING(json_item, "password", password);

        if ( STRING_NO_EMPTY(password) )
        {
            base64_decode(password, strlen(password), wifi_conf.psk, sizeof(wifi_conf.psk));
            wifi_conf.sec_mode = SEC_AUTO;
        }
        else
        {
            wifi_conf.sec_mode = SEC_OPEN;
        }

        if ( strcasecmp(link_action, "link") == 0 )
        {
            netctx_connect_wifi_station(net_ctx, &wifi_conf, 0);
        }
        else
        {
            netctx_connect_wifi_station(net_ctx, NULL, 0);
        }
    }
    while ( 0 );

    rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'WifiControlbyAPP','Result':'%d'}", WEBSRV_SET_WIFI_OK);
    if ( json_root != NULL )
    {
        cJSON_Delete(json_root);
    }
    return rlen;
}

/**
 * @brief       EWS modify userinfo.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_modify_userinfo(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char    author_info[256];
    char    modify_info[256];
    char    userbase64[89];
    char    passbase64[89];
    char    username[64];
    char    password[64];
    int32_t nparsed = 0;
    int32_t rlen = -1;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    memset(author_info, 0, sizeof(author_info));
    memset(modify_info, 0, sizeof(modify_info));
    nparsed = sscanf(priv->iobuf, "%255[^?]?%255s", author_info, modify_info);
    if ( nparsed == 2 && websrv_verify_author(ptask, author_info) == 0 )
    {
        memset(userbase64, 0, sizeof(userbase64));
        memset(passbase64, 0, sizeof(passbase64));
        memset(username,   0, sizeof(username));
        memset(password,   0, sizeof(password));
#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
        switch ( priv->admin_id )
        {
        case ADMIN_ID_SEC:
            nparsed = sscanf(modify_info, "omSecAdminUser=%*[^&]&omSecAdminPass=%88[^&]", passbase64);
            if ( nparsed == 1 && STRING_NO_EMPTY(passbase64) )
            {
                rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Setting Properties','Result':'0'}");
                base64_decode(passbase64, strlen(passbase64), password, sizeof(password));
                netdata_set_web_sec_pswd(DATA_MGR_OF(s_websrv_ctx), password);
            }
            break;
        case ADMIN_ID_AUD:
            nparsed = sscanf(modify_info, "omAuditAdminUser=%*[^&]&omAuditAdminPass=%88[^&]", passbase64);
            if ( nparsed == 1 && STRING_NO_EMPTY(passbase64) )
            {
                rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Setting Properties','Result':'0'}");
                base64_decode(passbase64, strlen(passbase64), password, sizeof(password));
                netdata_set_web_aud_pswd(DATA_MGR_OF(s_websrv_ctx), password);
            }
            break;
        case ADMIN_ID_SYS:
            nparsed = sscanf(modify_info, "omAdminUser=%*[^&]&omAdminPass=%88[^&]", passbase64);
            if ( nparsed == 1 && STRING_NO_EMPTY(passbase64) )
            {
                rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Setting Properties','Result':'0'}");
                base64_decode(passbase64, strlen(passbase64), password, sizeof(password));
                netdata_set_web_sys_pswd(DATA_MGR_OF(s_websrv_ctx), password);
            }
            break;
        default:
            break;
        }
#else /* !CONFIG_NET_SAFETY_MACHINE */
        nparsed = sscanf(modify_info, "omAdminUser=%88[^&]&omAdminPass=%88s", userbase64, passbase64);
        if ( nparsed == 2 && STRING_NO_EMPTY(userbase64) && STRING_NO_EMPTY(passbase64) )
        {
            rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Setting Properties','Result':'0'}");
            base64_decode(userbase64, strlen(userbase64), username, sizeof(username));
            base64_decode(passbase64, strlen(passbase64), password, sizeof(password));
            netdata_set_web_user(DATA_MGR_OF(s_websrv_ctx), username);
            netdata_set_web_pswd(DATA_MGR_OF(s_websrv_ctx), password);
        }
#endif /* CONFIG_NET_SAFETY_MACHINE */
        if ( rlen < 0 )
        {
            NET_WARN("modify_info(%s) invalid from client(%u->%u)", modify_info, ptask->r_port, ptask->l_port);
        }
    }
    else
    {
        NET_WARN("author(%s) verify failure from client(%u->%u)", author_info, ptask->r_port, ptask->l_port);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'No Authorized','Result':'401'}");
    }

    return rlen;
}

static int32_t websrv_reset_check_secure_pwd(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
#define WEBSRV_SECURE_OMSTR  "omSecurePassword="
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char*       ptr = NULL;
    char        webpage_secure_password[32] = {0};
    char        panel_secure_password[32] = {0};
    int32_t     rlen;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    ptr = strstr(priv->iobuf, WEBSRV_SECURE_OMSTR);
    RETURN_VAL_IF(ptr == NULL, NET_WARN, -1);

    *ptr = '\0';
    ptr += (sizeof(WEBSRV_SECURE_OMSTR) - 1);

    netdata_get_secure_password(DATA_MGR_OF(s_websrv_ctx), panel_secure_password, sizeof(panel_secure_password));
    base64_decode(ptr, strlen(ptr), webpage_secure_password, sizeof(webpage_secure_password));
    if ( strcmp(webpage_secure_password, panel_secure_password) == 0 )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Resetall success','Result':'0'}");
    }
    else
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Resetall fail','Result':'-1'}");
    }

    return rlen;
}


/**
 * @brief       EWS reset_factory.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_reset_factory(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
#define WEBSRV_RESET_OMSTR  "?omNetworkReset="
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    NET_CTX_S*  net_ctx = s_websrv_ctx->net_ctx;
    char*       ptr = NULL;
    char        str[4];
    int32_t     rlen;
    uint32_t    val;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    ptr = strstr(priv->iobuf, WEBSRV_RESET_OMSTR);
    RETURN_VAL_IF(ptr == NULL, NET_WARN, -1);

    *ptr = '\0';
    ptr += (sizeof(WEBSRV_RESET_OMSTR) - 1);

    if( job_manager_set_silent_mode() == -1 )
    {
        NET_WARN("Failure, Have current job.");
        return snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Have Current Job','Result':'-43'}");
    }

    memset(str, 0, sizeof(str));
    base64_decode(ptr, strlen(ptr), str, sizeof(str));
    val = (uint32_t)atoi(str);
    if ( val )
    {
        nettls_delete_cert();
    }
    NETEVT_NOTIFY_I(EVT_TYPE_PLATFORM_RESTORE_FACTORY_DEFAULT_REQUEST, val);
    rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Resetall success','Result':'0'}");

    return rlen;
}

/**
 * @brief       EWS verify login.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_verify_login(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char    author[256];
    int32_t rlen;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf), NET_WARN, -1);

    snprintf(author, sizeof(author), "%s", priv->iobuf);
    if ( websrv_verify_author(ptask, author) == 0 )
    {
        NET_DEBUG("set admin user flage ...");
        netdata_set_web_login_sit(DATA_MGR_OF(s_websrv_ctx), MAX_AIRPRINT_USERINFO);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Login success','Result':'0','SessionID':'%s'}", author);
    }
    else if ( websrv_verify_airprint_author(ptask, author) == 0 )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'Login success','Result':'0','SessionID':'%s'}", author);
    }
    else
    {
        NET_WARN("author(%s) verify failure from client(%u->%u)", author, ptask->r_port, ptask->l_port);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'No Authorized','Result':'401'}");
    }

    return rlen;
}

#if CONFIG_NET_PLOG
void _websrv_zip_log_create(zipFile file, const char* filename)
{
    zip_fileinfo    zip_finfo;
    struct tm*      ptm;
    time_t          ctm;
    int32_t         ret;

    time(&ctm);
    ptm = localtime(&ctm);

    zip_finfo.tmz_date.tm_sec  = ptm->tm_sec;
    zip_finfo.tmz_date.tm_min  = ptm->tm_min;
    zip_finfo.tmz_date.tm_hour = ptm->tm_hour;
    zip_finfo.tmz_date.tm_mday = ptm->tm_mday;
    zip_finfo.tmz_date.tm_mon  = ptm->tm_mon;
    zip_finfo.tmz_date.tm_year = 1900 + ptm->tm_year;
    zip_finfo.dosDate          = 0;
    zip_finfo.internal_fa      = 0;
    zip_finfo.external_fa      = 0;

    ret = zipOpenNewFileInZip(file, filename, &zip_finfo, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_DEFAULT_COMPRESSION);
    if ( ret < 0 )
    {
        NET_ERROR("create %s zip error", filename);
    }


}

void websrv_zip_log_create(char* ziplog_path, int32_t path_size)
{
    int zip_ret = ZIP_OK;
    int32_t     rlen = -1;
    uint32_t    topic_count;
    char        log_path[256];
    zipFile     zf;

    FILE*       log_file;

    if ( s_websrv_ctx->export_flag == EXPORT_PLOG_FINISH )
    {
        if( access(WEBSRV_TEMP_DIR"log.zip", F_OK) == 0 )
        {
            remove(WEBSRV_TEMP_DIR"log.zip");
        }
        NET_DEBUG("export log finish. There are %d files in %s", s_websrv_ctx->topic_map->topic_count, s_websrv_ctx->export_path);
        snprintf(ziplog_path, path_size, "%s/%s", WEBSRV_TEMP_DIR, "log.zip");
        ziplog_path[strlen(ziplog_path)] = '\0';
        zf = zipOpen(ziplog_path, 0);
        if ( zf == NULL )
        {
            NET_ERROR("create %s error", ziplog_path);
            return;
        }
        for ( topic_count = 0; topic_count < s_websrv_ctx->topic_map->topic_count; topic_count++ )
        {
            rlen = snprintf(log_path, sizeof(log_path) - 1, "%s/%s", s_websrv_ctx->export_path, s_websrv_ctx->topic_map->plog_topic[topic_count].name);
            log_path[rlen] = '\0';
            log_file = fopen(log_path, "rb+");
            if ( log_file == NULL )
            {
                NET_ERROR("open %s error", log_path);
                continue;
            }
            _websrv_zip_log_create(zf, s_websrv_ctx->topic_map->plog_topic[topic_count].name);
            memset(log_path, 0, sizeof(log_path));
            while ( 1 )
            {
                rlen = fread(log_path, 1, sizeof(log_path), log_file);
                if ( rlen > 0 )
                {
                    zip_ret = zipWriteInFileInZip(zf, log_path, rlen);
                    if(zip_ret != ZIP_OK)
                    {
                        NET_ERROR("write file data in zip error");
                        break;
                    }
                }
                else
                {
                    break;
                }
            }
            zip_ret = zipCloseFileInZip(zf);
            if(zip_ret != ZIP_OK)
            {
                NET_ERROR("close %s file in zip error", log_path);
            }
            fclose(log_file);
        }
        zip_ret = zipClose(zf, "");
        if(zip_ret != ZIP_OK)
        {
            NET_ERROR("close zip file error");
        }
        else
        {
            log_file = fopen(ziplog_path, "ab+");
            if ( log_file != NULL )
            {
                fflush(log_file);
                fclose(log_file);
            }
        }
    }
}

int websrv_export_log_callback(EXPORT_PLOG_FLAG_E flag, const char* export_path)
{
    s_websrv_ctx->export_flag = flag;
    strncpy(s_websrv_ctx->export_path, export_path, sizeof(s_websrv_ctx->export_path));
    s_websrv_ctx->export_path[strlen(s_websrv_ctx->export_path)] = '\0';
    return 0;
}
#endif

/**
 * @brief       EWS verify maintenmance log.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Kaiyong
 * @date        2024-06-20
 */
static int32_t websrv_export_log(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    #define EXPORTLOG_PASSWORD   "**36"
#if CONFIG_NET_PLOG
    #define EXPORTLOG_DIR       "webpage"    //这里有软连接/web/pantumweb/webpage -> /tmp/webpage
    #define EXPORTLOG_FILE1     EXPORTLOG_DIR"/log.zip"
#else
    #define EXPORTLOG_DIR       "log"
    #define EXPORTLOG_FILE1     EXPORTLOG_DIR"/pol_log_0.log"
    #define EXPORTLOG_FILE2     EXPORTLOG_DIR"/pol_log_1.log"
    #define EXPORTLOG_FILE3     EXPORTLOG_DIR"/pol_crash_log.log"
#endif

    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    char        base64data[32];
    char*       json_str;
    int32_t     rlen = -1;

#if CONFIG_NET_PLOG
    char ziplog_path[512];
    int32_t    timeout_count = 0;
#endif

    cJSON *logfile_path _autofree_cjson_ = NULL;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(priv->iobuf) || (logfile_path = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    NET_DEBUG("iobuf[%s]", priv->iobuf);
    base64_encode(EXPORTLOG_PASSWORD, strlen(EXPORTLOG_PASSWORD), base64data, sizeof(base64data));
    if ( strcmp(priv->iobuf, base64data) != 0 )
    {
        cJSON_AddNumberToObject(logfile_path, "Result",    WEBSRV_EXPORT_LOG_FAIL);
    }
    else
    {
        cJSON_AddNumberToObject(logfile_path, "Result",    WEBSRV_EXPORT_LOG_OK);
#if CONFIG_NET_PLOG
        pi_plog_get_topic_map((void**)(&s_websrv_ctx->topic_map));
        s_websrv_ctx->export_flag = EXPORT_PLOG_INVALID;
        pi_webpage_export_plog(ALL_TOPIC, websrv_export_log_callback);
        while ( s_websrv_ctx->export_flag != EXPORT_PLOG_FINISH && timeout_count <= 15)
        {
            pi_msleep(1 * 1000);
            timeout_count++;
            NET_DEBUG("wait export log finish, %d", s_websrv_ctx->export_flag);
        }
        websrv_zip_log_create(ziplog_path, sizeof(ziplog_path) - 1);
        pi_export_plog_finish();
        s_websrv_ctx->export_flag = EXPORT_PLOG_INVALID;
        cJSON_AddStringToObject(logfile_path, "LOG_FILE1", EXPORTLOG_FILE1);
#else
        cJSON_AddStringToObject(logfile_path, "LOG_FILE1", EXPORTLOG_FILE1);
        cJSON_AddStringToObject(logfile_path, "LOG_FILE2", EXPORTLOG_FILE2);
        cJSON_AddStringToObject(logfile_path, "LOG_FILE3", EXPORTLOG_FILE3);
#endif
    }
    RETURN_VAL_IF( (json_str = cJSON_PrintUnformatted(logfile_path)) == NULL, NET_WARN, -1 );
    rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", json_str);
    NET_DEBUG("reply(%s)", priv->iobuf);
    *rtype = "application/json; charset=\"utf-8\"";
    pi_free(json_str);

    return rlen;
}

#if CONFIG_NET_SAFETY_MACHINE
static int32_t websrv_export_eventlog(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    int32_t rlen = 0;
    char*   buf = NULL;
    cJSON * logfile_path = NULL;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX"%u_%08x_%s", ptask->r_port, (uint32_t)ptask, "eventlog");
    logfile_path = cJSON_CreateObject();
    if ( event_log_export_log_summary(priv->reply_file) == 1 )
    {
        if ( access(priv->reply_file, F_OK) == 0 )
        {
            if ( stat(priv->reply_file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
            {
                buf = pi_zalloc(file_stat.st_size);
                if ( buf == NULL )
                {
                    rlen = -1;
                }
                else
                {
                    read_file(priv->reply_file, buf, file_stat.st_size);
                    cJSON_AddNumberToObject(logfile_path, "Result", -31);
                    cJSON_AddStringToObject(logfile_path, "File", buf);
                    pi_free(buf);
                    buf = cJSON_PrintUnformatted(logfile_path);
                    rlen = strlen(buf);
                    save_file(priv->reply_file, buf, rlen);
                    cJSON_free(buf);
                }
            }
            else
            {
                rlen = -1;
            }
        }
        else
        {
            rlen = -1;
        }
    }
    else
    {
        rlen = -1;
    }

    if ( rlen == -1 )
    {
        pi_memset(priv->reply_file, 0, sizeof(priv->reply_file));
        cJSON_AddNumberToObject(logfile_path, "Result", -1);
        buf = cJSON_PrintUnformatted(logfile_path);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", buf);
        cJSON_free(buf);
    }

    *rtype = "application/json; charset=\"utf-8\"";
    cJSON_Delete(logfile_path);

    return rlen;
}
#endif


/**
 * @brief       EWS process "/WifiConnectionStatus".
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : http rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Xin
 * @date        2024-01-29
 */
static int32_t websrv_get_wifi_status(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    cJSON*      json_root = NULL;
    cJSON*      json_item = NULL;
    char*       json_str = NULL;
    char        bonj_serv[128] = {0};
    char        wifi_ssid[128] = {0};
    char        temp_str[64] = {0};
    uint32_t    status = 0;
    uint32_t    detail = 0;
    int32_t     rlen = -1;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    cJSON_AddNumberToObject(json_root, "code",  2002);
    cJSON_AddStringToObject(json_root, "owner", s_websrv_ctx->owner);

    status = netdata_get_sta_status(DATA_MGR_OF(s_websrv_ctx));
    NET_DEBUG("connection status(%u)", status);
    if ( status == NETLINK_STATUS_CONNECTING )
    {
        netdata_get_sta_ssid(DATA_MGR_OF(s_websrv_ctx), wifi_ssid, sizeof(wifi_ssid));
        cJSON_AddStringToObject(json_root, "status", "connecting");
        cJSON_AddStringToObject(json_root, "reason", "");
    }
    else if ( status == NETLINK_STATUS_CONNECTED )
    {
        netdata_get_sta_ssid(DATA_MGR_OF(s_websrv_ctx), wifi_ssid, sizeof(wifi_ssid));
        cJSON_AddStringToObject(json_root, "status", "connected");
        cJSON_AddStringToObject(json_root, "reason", "");
    }
    else
    {
        cJSON_AddStringToObject(json_root, "status", "disconnected");
        detail = netdata_get_sta_detail(DATA_MGR_OF(s_websrv_ctx));
        NET_DEBUG("connection detail(%u)", detail);
        switch ( detail )
        {
        case WIFI_DISCONNECTED_DETAIL_TIMEOUT:
            cJSON_AddExtendToObject(json_root, "reason", "%s", "connect timeout");
            break;
        case WIFI_DISCONNECTED_DETAIL_NO_SSID:
        case WIFI_DISCONNECTED_DETAIL_ERR_PSK:
            cJSON_AddExtendToObject(json_root, "reason", "%s", "auth failed");
            break;
        default:
            cJSON_AddExtendToObject(json_root, "reason", "%s", "none");
            break;
        }
    }

    if ( netdata_get_bonjour_switch(DATA_MGR_OF(s_websrv_ctx)) )
    {
        netdata_get_bonjour_server(DATA_MGR_OF(s_websrv_ctx), bonj_serv, sizeof(bonj_serv));
    }
    cJSON_AddStringToObject(json_root, "bonjour_name", bonj_serv);

    json_item = cJSON_CreateObject();
    if ( json_item != NULL )
    {
        cJSON_AddStringToObject(json_item, "ssid",      wifi_ssid);
        cJSON_AddItemToObject  (json_root, "parameter", json_item);
    }

    json_str = cJSON_PrintUnformatted(json_root);
    if ( json_str != NULL)
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", json_str);
        *rtype = "application/json; charset=\"utf-8\"";
        NET_DEBUG("reply(%s)", priv->iobuf);
        pi_free(json_str);
    }
    cJSON_Delete(json_root);

    return rlen;
}

/**
 * @brief       EWS get login nfo.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   rcode   : http rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_get_login_info(HTTP_TASK_S* ptask, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    cJSON*  json_root = NULL;
    char*   json_str = NULL;
    char    temp_str[16];
    char    mfg_name[32];
    char    pdt_name[32];
    char    username[64];
    int32_t rlen = -1;
    uint32_t login_usr = 0;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

    RETURN_VAL_IF((json_root = cJSON_CreateObject()) == NULL, NET_WARN, -1);

    netdata_get_mfg_name(DATA_MGR_OF(s_websrv_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(DATA_MGR_OF(s_websrv_ctx), pdt_name, sizeof(pdt_name));

    login_usr = netdata_get_web_login_sit(DATA_MGR_OF(s_websrv_ctx));
    if(login_usr >= MAX_AIRPRINT_USERINFO )
        netdata_get_web_user(DATA_MGR_OF(s_websrv_ctx), username, sizeof(username));
    else
    {
        AIRPRINT_USERLIST_S   userlist;
        netdata_get_airprint_userlist(DATA_MGR_OF(s_websrv_ctx), &userlist, sizeof(userlist));
        int ret = sscanf(userlist.userinfo[login_usr], "{\"user\":\"%63[^\"]\",}", username);
        RETURN_VAL_IF(ret == 0, NET_WARN, -1);
    }

    cJSON_AddStringToObject(json_root, "ManufacturerName", mfg_name);
    cJSON_AddStringToObject(json_root, "ProductName",      pdt_name);
    cJSON_AddStringToObject(json_root, "UserName",         username);
    cJSON_AddExtendToObject(json_root, "SupportWired",     "%u", netdata_get_support_wired(DATA_MGR_OF(s_websrv_ctx)));
    cJSON_AddExtendToObject(json_root, "SupportWireless",  "%u", netdata_get_support_wifi(DATA_MGR_OF(s_websrv_ctx)));

    json_str = cJSON_PrintUnformatted(json_root);
    if ( json_str != NULL )
    {
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "%s", json_str);
        *rtype = "application/json; charset=\"utf-8\"";
        NET_DEBUG("reply(%s)", priv->iobuf);
        pi_free(json_str);
    }
    else
    {
        NET_WARN("json print failed!!!");
    }
    cJSON_Delete(json_root);

    return rlen;
}

static WEBSRV_URL_TABLE_S   s_websrv_table[] __attr_section(url_table) =
{
    { "/WifiConnectionStatus"      , HTTP_GET  , websrv_get_wifi_status         , NULL                        , 0x7 , 0 }, ///< 获取WiFi连接状态
    { "/logininfo"                 , HTTP_GET  , websrv_get_login_info          , NULL                        , 0x7 , 0 }, ///< 获取登陆信息
    { "/SetProperties.cgi"         , HTTP_POST , websrv_set_properties          , NULL                        , 0x7 , 1 }, ///< 属性设置
    { "/showssltls"                , HTTP_POST , websrv_show_tlscert            , NULL                        , 0x4 , 1 }, ///< SSL/TLS  - 获取证书信息
    { "/tlscertmake"               , HTTP_POST , websrv_make_tlscert            , NULL                        , 0x4 , 1 }, ///< SSL/TLS  - 生成/安装证书
    { "/docertificate"             , HTTP_POST , websrv_install_tlscert         , NULL                        , 0x4 , 1 }, ///< SSL/TLS  - 证书安装
    { "/deletessltls"              , HTTP_POST , websrv_delete_tlscert          , NULL                        , 0x4 , 1 }, ///< SSL/TLS  - 证书卸载
    { "/maintenmance_passwd_check" , HTTP_POST , websrv_verify_maintenmance_pwd , NULL                        , 0x4 , 1 }, ///< 维修模式 - 密码校验
    { "/maintenmance_mode_log"     , HTTP_POST , websrv_report_maintenmance_log , NULL                        , 0x4 , 1 }, ///< 维修模式 - 日志导出
    { "/taskrecord"                , HTTP_POST , websrv_export_taskrecord       , NULL                        , 0x2 , 1 }, ///< 安全日志 - 任务记录
    { "/boardrecord"               , HTTP_POST , websrv_export_boardrecord      , NULL                        , 0x2 , 1 }, ///< 安全日志 - 硬件变更记录
    { "/operatelog"                , HTTP_POST , websrv_export_operatelog       , NULL                        , 0x1 , 1 }, ///< 安全日志 - 操作记录
    { "/upgradelog"                , HTTP_POST , websrv_export_upgradelog       , NULL                        , 0x1 , 1 }, ///< 安全日志 - 固件升级日志
    { "/htsrecord"                 , HTTP_POST , websrv_export_htsrecord        , NULL                        , 0x2 , 1 }, ///< 可信日志 - 成功日志
    { "/htfrecord"                 , HTTP_POST , websrv_export_htfrecord        , NULL                        , 0x2 , 1 }, ///< 可信日志 - 成功日志

    { "/WifiControlbyAPP"          , HTTP_POST , websrv_control_wifi            , NULL                        , 0x7 , 0 }, ///< WiFi配网
    { "/changepassword"            , HTTP_POST , websrv_modify_userinfo         , NULL                        , 0x7 , 1 }, ///< 用户管理 - 修改用户密码
    { "/checkSecurePassword"       , HTTP_POST , websrv_reset_check_secure_pwd  , NULL                        , 0x7 , 1 }, ///< 用户管理 - 修改用户密码
    { "/resetall"                  , HTTP_POST , websrv_reset_factory           , NULL                        , 0x4 , 1 }, ///< 用户管理 - 恢复出厂设置
    { "/login"                     , HTTP_POST , websrv_verify_login            , NULL                        , 0x7 , 0 }, ///< 登陆界面 - 登陆请求
    { "/DeviceReport"              , HTTP_POST , websrv_export_device_report    , NULL                        , 0x7 , 1 }, ///< 机器设置 - 设备报告
    { "/ScreenCapture"             , HTTP_POST , websrv_capture_remote_screen   , NULL                        , 0x7 , 1 }, ///< 机器设置 - 远程控制
    { "/TouchScreen"               , HTTP_POST , websrv_touch_remote_screen     , NULL                        , 0x7 , 1 }, ///< 获取鼠标触控事件
    { "/export_log"                , HTTP_POST , websrv_export_log              , NULL                        , 0x7 , 1 }, ///< 机器设置 - 日志导出
#if CONFIG_NET_SAFETY_MACHINE
    { "/eventlog"                  , HTTP_POST , websrv_export_eventlog         , NULL                        , 0x4 , 1 }, ///< 日志导出 - 事件记录
#endif
};

/**
 * @brief       EWS post request.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   url     : http request url .
 * @param[in]   parms   : parms .
 * @param[in]   rcode   : http rcode .
 * @param[in]   rtype   : rtype.
 * @return      reply value
 * @retval      >= 0    : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_process_get_request(HTTP_TASK_S* ptask, const char* url, const char* parms, const char** rcode, const char** rtype)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    struct stat file_stat;
    char        file[FILE_PATH_MAX];
    int32_t     rlen;
    size_t      i;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);

#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
    if ( url[0] == '/' && strcmp(url + 1, priv->cookie_info) == 0 && priv->admin_id < ADMIN_ID_NUM ) /* 引导安全机主页 */
    {
        return snprintf(priv->iobuf, sizeof(priv->iobuf), "<html><head><meta http-equiv=\"refresh\" content=\"0; url=%s\" /></head></html>\r\n", s_web_admin_url[priv->admin_id]);
    }
#else
    if ( strcmp(url, "/") == 0 ) /* 引导通用机主页 */
    {
        return snprintf(priv->iobuf, sizeof(priv->iobuf), "<html><head><meta http-equiv=\"refresh\" content=\"0; url=index.html\" /></head></html>\r\n");
    }
#endif

    snprintf(file, sizeof(file), CONFIG_NET_EWS_HOME_DIR "%s", url);
    if ( strstr(url, "../") == NULL && stat(file, &file_stat) == 0 && S_ISREG(file_stat.st_mode) )
    {
        if ( get_content_type(url, rtype) )
        {
            if ( 0 ) //( priv->authorized == 0 ) /* 未完成登陆校验之前，禁止获取shtml中的OM属性 */
            {
                NET_WARN("get url(%s) parms(%s) unauthorized from client(%u->%u)", url, parms, ptask->r_port, ptask->l_port);
                rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'No Authorized','Result':'401'}");
                *rcode = "404 WHAT";
            }
            else
            {
                snprintf(priv->reply_file, sizeof(priv->reply_file), WEBSRV_TEMP_CLI_PREFIX "%u_%08x_%s", ptask->r_port, (uint32_t)ptask, basename(file));
                rlen = websrv_process_shtml(ptask, parms, file, priv->reply_file);
            }
        }
        else
        {
            snprintf(priv->reply_file, sizeof(priv->reply_file), "%s", file);
            rlen = (int32_t)file_stat.st_size;
        }
    }
    else
    {
        NET_WARN("no such url(%s) parms(%s) from client(%u->%u)", url, parms, ptask->r_port, ptask->l_port);
        rlen = snprintf(priv->iobuf, sizeof(priv->iobuf), "<html><body>No such URL here</body></html>");
        *rcode = "404 WHAT";
    }
    return rlen;
}

static WEBSRV_URL_TABLE_S* websrv_url_match(const char* url, enum http_method method)
{
    struct websrv_url_table item = { .request_url=url, .request_method=method };
    return BSEARCH_SECTION(url_table, &item, WEBSRV_URL_TABLE_S, websrv_url_tbl_compar);
}

/**
 * @brief       EWS process headers.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   method  : http method.
 * @param[in]   url     : url.
 * @param[in]   content_length : The HTTP content_length.
 * @return      reply value
 * @retval      = 0     : process success\n
 *              < 0     : process fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_process_headers(HTTP_TASK_S* ptask, enum http_method method, const char* url, uint64_t content_length)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    WEBSRV_URL_TABLE_S* url_tbl = NULL;

    RETURN_VAL_IF(priv == NULL, NET_WARN, -1);
    if ( ptask->l_port == 631 )
    {
        if ( strncmp(url, "/eSCL", 5) == 0 )
        {
            esclsrv_construct(ptask);
        }
        else if ( (strncmp(url, "/ipp", 4 ) == 0 || (method == HTTP_POST && strcmp(url, "/") == 0)) )
        {
            ippsrv_construct(ptask);
        }
        if ( ptask->headers_complete_callback != websrv_process_headers ) /* 确认callback被重载，避免无效嵌套 */
        {
            return ptask->headers_complete_callback(ptask, method, url, content_length);
        }
    }

    memset(priv->cookie_info, 0, sizeof(priv->cookie_info));
    memset(priv->author_info, 0, sizeof(priv->author_info));
    memset(priv->reply_hdrs , 0, sizeof(priv->reply_hdrs));
    memset(priv->reply_file , 0, sizeof(priv->reply_file));
#if CONFIG_NET_SAFETY_MACHINE /* 安全机三员管理相关 */
    priv->admin_id   = ADMIN_ID_NUM;
#endif
    priv->authorized = 0;
    priv->received   = 0;
    priv->flg_authorized_ok = 0;
    CLEAR_USER_DATA();

    memset(&(priv->webomctx), 0, sizeof(priv->webomctx));
    priv->webomctx.net_ctx  = s_websrv_ctx->net_ctx;

    RETURN_VAL_IF(websrv_check_cookie(ptask) < 0, NET_WARN, -1);
    RETURN_VAL_IF(websrv_check_author(ptask) < 0, NET_WARN, -1);
    RETURN_VAL_IF(websrv_check_host(ptask) < 0, NET_WARN, -1);

    priv->tbl_entry = websrv_url_match(url, method);
    RETURN_VAL_IF(priv->tbl_entry == NULL && method == HTTP_POST, NET_WARN, -1);

    if ( method == HTTP_POST && content_length != (uint64_t)-1 )
    {
        priv->reqtotal = (size_t)content_length;
    }

    if (priv->tbl_entry)
    {
        url_tbl = priv->tbl_entry;
        priv->flg_authorized_ok = websrv_is_authorized_ok(priv, url_tbl->access_flags, url_tbl->author_need);
        if (!priv->flg_authorized_ok)
        {
            // bypass ptask->reqbody_received_callback, call ptask->request_complete_callback directly
            return 1;
        }

        if (url_tbl->extra_header_cb)
        {
            return url_tbl->extra_header_cb(ptask, method, url, content_length);
        }
    }
    return 0;
}

/**
 * @brief       EWS  process  request.
 * @param[in]   ptask   : The HTTP_TASK_S object pointer.
 * @param[in]   method  : method .
 * @param[in]   url     : http request url .
 * @param[in]   parms   : parms .
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
static int32_t websrv_process_request(HTTP_TASK_S* ptask, enum http_method method, const char* url, const char* parms)
{
    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    FILE*       reply_stream = NULL;
    const char* reply_type = MIME_TYPE_HTML;
    const char* reply_code = http_status_string(200);
    int32_t     reply_len = -1;
    size_t      total;
    size_t      nread;

    RETURN_VAL_IF(priv == NULL || STRING_IS_EMPTY(url), NET_WARN, -1);

    priv->iobuf[priv->received] = '\0';
    NET_TRACE("method(%s) url(%s) parms(%s) body[%u](%s) from client(%u->%u)", http_method_str(method), url, parms, priv->received, priv->iobuf, ptask->r_port, ptask->l_port);

    if (priv->tbl_entry)
    {
        if (priv->flg_authorized_ok)
        {
            reply_len = priv->tbl_entry->request_handle(ptask, &reply_code, &reply_type);
        }
        else
        {
            reply_code = "401 Unauthorized";
            reply_len = snprintf(priv->iobuf, sizeof(priv->iobuf), "{'Operation':'No Authorized','Result':'401'}");
        }
    }
    else if (method == HTTP_GET)
    {
        reply_len = websrv_process_get_request(ptask, url, parms, &reply_code, &reply_type);
    }

    RETURN_VAL_IF(reply_len < 0, NET_WARN, -1);

    http_task_reply_resp_headers(ptask, reply_code, reply_type, reply_len);
    if ( reply_len > 0 )
    {
        if ( STRING_NO_EMPTY(priv->reply_file) )
        {
            NET_TRACE("url(%s) file(%s) filesize(%d) from client(%u->%u)", url, priv->reply_file, reply_len, ptask->r_port, ptask->l_port);
            reply_stream = pi_fopen(priv->reply_file, "r");
            RETURN_VAL_IF(reply_stream == NULL, NET_WARN, -1);

            for ( total = 0; total < reply_len; total += nread )
            {
                nread = fread(priv->iobuf, 1, sizeof(priv->iobuf), reply_stream);
                if ( nread == 0 )
                {
                    NET_WARN("url(%s) file(%s) reply_len(%d) rtotal(%u) from client(%u->%u)", url, priv->reply_file, reply_len, total, ptask->r_port, ptask->l_port);
                    break;
                }
                http_task_send(ptask, priv->iobuf, nread);
            }
            pi_fclose(reply_stream);
            if ( strncmp(priv->reply_file, WEBSRV_TEMP_CLI_PREFIX, sizeof(WEBSRV_TEMP_CLI_PREFIX) - 1) == 0 )
            {
                unlink(priv->reply_file); ///< 删除返回客户端数据的临时文件
            }
            memset(priv->reply_file, 0, sizeof(priv->reply_file));
        }
        else
        {
            http_task_send(ptask, priv->iobuf, reply_len);
        }
    }

    return 0;
}

/**
 * @brief       websrv construct.
 * @param[in]   obj     : The object pointer.
 * @return      reply value
 * @retval      = 0     : success\n
 *              < 0     : fail
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
int32_t websrv_construct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    RETURN_VAL_IF(ptask == NULL, NET_WARN, -1);

    if ( ptask->priv_subclass[1] == NULL )
    {
        ptask->priv_subclass[1] = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
        RETURN_VAL_IF(ptask->priv_subclass[1] == NULL, NET_WARN, -1);
    }
    ptask->headers_complete_callback = websrv_process_headers;
    ptask->reqbody_received_callback = websrv_process_reqbody;
    ptask->request_complete_callback = websrv_process_request;

    return 0;
}

/**
 * @brief       websrv destruct.
 * @param[in]   obj   : The object pointer.
 * <AUTHOR> Huanbin
 * @date        2023-10-18
 */
void websrv_destruct(void* obj)
{
    HTTP_TASK_S* ptask = (HTTP_TASK_S *)obj;

    DECL_PRIV1(ptask, PRIV_INFO_S, priv);
    if ( priv )
    {
        CLEAR_USER_DATA();
        pi_free(priv);
        ptask->priv_subclass[1] = NULL;
    }
}

#if !CONFIG_NET_SAFETY_MACHINE
static void websrv_connection(void* arg)
{
    NET_CONN_S*     pnc = (NET_CONN_S *)arg;
    HTTP_TASK_S*    ptask;
    QIO_S*          pqtcp;

    pqtcp = qio_tcp_create(pnc->sockfd, NETQIO_SOCK_NOCLOSE);
    if ( pqtcp != NULL )
    {
        ptask = http_task_create(pqtcp, pnc->remote_port, pnc->local_port);
        if ( websrv_construct(ptask) == 0 )
        {
            http_task_process(ptask);
        }
        websrv_destruct(ptask);
        http_task_destroy(ptask);
        QIO_CLOSE(pqtcp);
    }
    netsock_close_connection(pnc);
}

static void* websrv_thread_handler(void* arg)
{
    PI_SOCKET_T     service_sockfd[IPVER_NUM] = { INVALID_SOCKET, INVALID_SOCKET };    /* PORT: 80 */
    IP_VERSION_E    ipver;
    NET_CONN_S*     pnc;
    fd_set          rfds;
    int32_t         update = 0;
    int32_t         max_fd;

    while ( 1 )
    {
        FD_ZERO(&rfds);
        max_fd = 0;
        netsock_reload_tcpserver(service_sockfd, &rfds, &max_fd, update, HTTP_PORT, 1);
        update = 0;

        if ( select(max_fd + 1, &rfds, NULL, NULL, NULL) <= 0 )
        {
            NET_WARN("select failed: %d<%s>, reload service socket", errno, strerror(errno));
            update = 1;
            continue;
        }

        for ( ipver = IPV4; ipver < IPVER_NUM; ++ipver )
        {
            if ( FD_ISSET(service_sockfd[ipver], &rfds) )
            {
                pnc = netsock_accept_connection(service_sockfd[ipver], ipver);
                if ( pnc != NULL )
                {
                    if ( threads_pool_add_task(THREADS_POOL_OF(s_websrv_ctx), websrv_connection, pnc) < 0 )
                    {
                        NET_WARN("add web_connection %s to threads pool failed", IPVER_NAME(ipver));
                        netsock_close_connection(pnc);
                    }
                }
                else
                {
                    NET_WARN("new connection by %s failed, update current socket", IPVER_NAME(ipver));
                    pi_closesock(service_sockfd[ipver]);
                    service_sockfd[ipver] = INVALID_SOCKET;
                    continue;
                }
            }
        }
    }

    return NULL;
}
#endif /* !CONFIG_NET_SAFETY_MACHINE */

int32_t websrv_prolog(NET_CTX_S* net_ctx)
{
    int32_t 		 ret = -1;
	int32_t          screen_size;

    RETURN_VAL_IF(s_websrv_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_websrv_ctx = (WEBSRV_CTX_S *)pi_zalloc(sizeof(WEBSRV_CTX_S));
    RETURN_VAL_IF(s_websrv_ctx == NULL, NET_WARN, -1);

    websrv_omstr_page_support_init(net_ctx);
    do
    {
    	s_websrv_ctx->mutex = pi_mutex_create();
        s_websrv_ctx->net_ctx = net_ctx;
        mkdir(WEBSRV_TEMP_DIR, 0664);   ///< 创建软链接"/web/pantumweb/webpage"指向的目标目录"/tmp/webpage"

        pi_hal_touch_request(HAL_TOUCH_PRIMARY, &s_websrv_ctx->tp_handle, HAL_REQUEST_FLAG_NONBLOCK);
        pi_hal_fb_request(HAL_FB_PRIMARY, &s_websrv_ctx->fb_handle, HAL_REQUEST_FLAG_NONBLOCK);

		//开启双缓冲
		ret = pi_hal_fb_ctrl(s_websrv_ctx->fb_handle, HAL_FB_DOUBLE_BUFFER_ON);
		if( ret < 0 )
			NET_WARN("CTRL HAL_FB_DOUBLE_BUFFER_ON ERROR! " );

		//分配空间
		ret = pi_hal_fb_getinfo(s_websrv_ctx->fb_handle, &s_websrv_ctx->fb_info);
	    s_websrv_ctx->screen_size = s_websrv_ctx->fb_info.xres * s_websrv_ctx->fb_info.yres * s_websrv_ctx->fb_info.bits_per_pixel / 8;
		if( ret == 0 )
			s_websrv_ctx->screenbuf = (uint8_t*)malloc(s_websrv_ctx->screen_size);
		else
			s_websrv_ctx->screenbuf = NULL;

#if !CONFIG_NET_SAFETY_MACHINE
        s_websrv_ctx->srv_tid = pi_thread_create(websrv_thread_handler, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "websrv_thread_handler");
        BREAK_IF(s_websrv_ctx->srv_tid == INVALIDTHREAD, NET_WARN);
#endif
        QSORT_SECTION(url_table, WEBSRV_URL_TABLE_S, websrv_url_tbl_compar);
        FOREACH_FUNCTION_CALL(init, net_ctx);
        ret = 0;
        BREAK_IF(msg_router_register(MID_NET_GEN_REPORT) < 0, NET_WARN);
    }
    while ( 0 );

    NET_INFO("websrv initialize result(%d)", ret);
    if ( ret != 0 )
    {
        websrv_epilog();
    }
    return ret;
}

void websrv_epilog(void)
{
    if ( s_websrv_ctx != NULL )
    {
        if ( s_websrv_ctx->srv_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_websrv_ctx->srv_tid);
        }
        if ( s_websrv_ctx->tp_handle != NULL )
        {
            pi_hal_touch_free(&s_websrv_ctx->tp_handle);
        }
        if ( s_websrv_ctx->fb_handle != NULL )
        {
            pi_hal_fb_free(&s_websrv_ctx->fb_handle);
        }
        if( s_websrv_ctx->screenbuf != NULL )
        {
            pi_free( s_websrv_ctx->screenbuf );
            s_websrv_ctx->screenbuf = NULL;
        }
        pi_free(s_websrv_ctx);
        s_websrv_ctx = NULL;
        msg_router_unregister(MID_NET_GEN_REPORT);
    }
}
/**
 *@}
 */
