/* ************************************ (c) Roon ** Start File ******************************************* */
/*************************************************************************************
 * File Name   	: httc_audit.c
 * Description 	: audit log for HTTC modules
 * Data	    	: 2019 - 10 - 14
 * By	        : RoonZhang
 * Email       	: <EMAIL>
 * Platform    	: linux-4.2.8 with sdk_marvell_88pa62x0
 * Explain     	: None
 * Modify	    : None
 ************************************************************************************* */
/* head files include --------------------------------------------------------------- */
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <netdb.h>
#include <sys/mman.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <net/ethernet.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <errno.h>
#include "pol/pol_threads.h"
//#include "oid_api.h"
#include "httc/httc_audit.h"
#include <string.h>
#include <stdio.h>
#include "public_data_proc.h"
#include "public/msgrouter_main.h"
#include "utilities/msgrouter.h"
#include "qio/qio_general.h"
#include "job_manager.h"
#include "pol/pol_log.h"
#include "cjson/cJSON.h"
//#include "httc_version.h" 

/* global macro ----------------------------------------------------------------------- */
#define HTTC_SUCESS_LOG_MAX_NUMBER		4000  // 最大成功日志条
#define HTTC_FAIL_LOG_MAX_NUMBER        1000  // 最大失败日志条
#define HTTC_LOG_LINE_WIDTH				1024  // 每条日志的最大宽 字节)

/* global values ---------------------------------------------------------------------- */
static PI_THREAD_T  g_httc_socket_taskID  = INVALIDTHREAD;

#define RN_RUN_CMD(...)	    pi_runcmd(NULL, 0, 0, __VA_ARGS__)
#define RN_RUN_CMD_BG(...)	pi_runcmd(NULL, 0, 1, __VA_ARGS__)

/* functions statement ---------------------------------------------------------------- */
//重设可信日志
static int RebuildHTTCLog(char *src_path,char *dst_path,int max_num)
{
    FILE  *fp_src = NULL;
    FILE  *fp_dst = NULL;
    int    i = 0;

    fp_src = fopen(src_path, "r");
    fp_dst = fopen(dst_path, "wb+");

    if (NULL == fp_dst)
    {
        pi_log_e("fopen %s error.\n", dst_path);
        if (fp_src) fclose(fp_src);
        return (-5);
    }

    //跳过源文件的第一行，将第二行数据作为第一行写入临时文件中
    for (i = 1; i <= max_num; i++)
    {
        unsigned int src_index;
        struct log_msg_st     log;
        memset((void *)&log, 0x0, sizeof(typeof(log)));

        // 读取原日志文件，格式化输出到log中，每次输出一       
        src_index = i * sizeof(typeof(log));
        fseek(fp_src, src_index, SEEK_SET);
        fread((void *)&log, sizeof(typeof(log)), 1, fp_src);

        // 写入文本文件
        fwrite(&log, sizeof(typeof(log)), 1, fp_dst);
    }
    fclose(fp_src);
    fp_src = NULL;
    fclose(fp_dst);
    fp_dst = NULL;

    //执行文件替换操作
    pi_runcmd(NULL, 0, 0, "rm %s", src_path);
    pi_runcmd(NULL, 0, 0, "mv %s %s", dst_path, src_path);

    return 0;
}

/************************************************************************
 * Function Name : int HttcGetLogIndex(HTTC_LOG_TYPE log_type)
 * Description   : get httc log index
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcGetLogIndex(HTTC_LOG_TYPE log_type)
{
    FILE  *fp = NULL;
    unsigned int audit_log_index;
    int ret = 0;
    char *path = NULL;
    int log_num_max;

    if(HTTC_SUCCESS_LOG == log_type)
    {
        path = HTTC_SUCESS_LOG_FILE_PATH;
        log_num_max = HTTC_SUCESS_LOG_MAX_NUMBER;
    }
    else
    {
        path = HTTC_FAIL_LOG_FILE_PATH;
        log_num_max = HTTC_FAIL_LOG_MAX_NUMBER;
    }

    if (0 != access(path, F_OK))
    {
        audit_log_index = 0;
        ret = audit_log_index;
        fp = fopen(path, "wb+");
        if (NULL == fp)
        {
            pi_log_e("failed to create file %s\n", path);
            return (-1);
        }
        fseek(fp, 0, SEEK_SET);
        fwrite(&audit_log_index, sizeof(uint32_t), 1, fp);

        fclose(fp);
        fp = NULL;
    }
    else
    {
        fp = fopen(path, "rb+");
        if (NULL == fp)
        {
            pi_log_e("fopen %s error.\n", path);
            return (-1);
        }
        fseek(fp, 0, SEEK_SET);
        fread(&audit_log_index, sizeof(uint32_t), 1, fp);

        audit_log_index++;
        if(audit_log_index > log_num_max)
        {
            if(HTTC_SUCCESS_LOG == log_type)
            {
                RebuildHTTCLog(HTTC_SUCESS_LOG_FILE_PATH,HTTC_TEMP_LOG_FILE_PATH,HTTC_SUCESS_LOG_MAX_NUMBER);
            }
            else
            {
                RebuildHTTCLog(HTTC_FAIL_LOG_FILE_PATH,HTTC_TEMP_LOG_FILE_PATH,HTTC_FAIL_LOG_MAX_NUMBER);
            }
            audit_log_index = log_num_max;
        }
        ret = audit_log_index;

        fseek(fp, 0, SEEK_SET);
        fwrite(&audit_log_index, sizeof(uint32_t), 1, fp);

        fclose(fp);
        fp = NULL;
    }
    return ret;
}

/************************************************************************
 * Function Name : int HttcSaveLogToFlash
 * Description   : write log
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  struct log_msg_st *pLog  	log message
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcSaveLogToFlash(HTTC_LOG_TYPE log_type, struct log_msg_st *pLog)
{
    FILE  *fp = NULL;
    uint32_t log_len = sizeof(struct log_msg_st);
    uint32_t file_offset;
    int log_index;
    char *path = NULL;

    if(HTTC_SUCCESS_LOG == log_type)
        path = HTTC_SUCESS_LOG_FILE_PATH;
    else
        path = HTTC_FAIL_LOG_FILE_PATH;


    if (!pLog)
    {
        pi_log_e("pLog is NULL!!\n");
        return (-1);
    }

    log_index = HttcGetLogIndex(log_type);
    if(log_index<0)
    {
        pi_log_e("httc log_index error.\n");
        return (-1);
    }

    // 使用rb+模式打开文件，可以往中间任意位置覆盖插入数据
    fp = fopen(path, "rb+");
    if (NULL == fp)
    {
        pi_log_e("fopen %s error.\n", path);
        return (-1);
    }


    // 在文件中覆盖插入
    file_offset = (log_index  * log_len) + sizeof(uint32_t);
    fseek(fp, file_offset, SEEK_SET);


    // 写入日志
    fwrite((const void*)pLog, sizeof(struct log_msg_st), 1, fp);

    fclose(fp);
    fp = NULL;
    return 0;
}


/************************************************************************
 * Function Name : HttcParseCJsonBootMeasure
 * Description   : 解析启动度量日志
 * Parameters    : char* pBody
 *				  
 * Return        : void
 * Data	      	: 2020 - 10 - 28
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcParseCJsonBootMeasure(char* pBody)
{
    cJSON *json,*arrayItem,*item,*object;
    int arraySize,i;
    struct log_msg_st lms;

    json = cJSON_Parse(pBody);
    if(!json)
    {
        pi_log_e("boot measure pBody error\n");
        return -1;
    }
    arrayItem = cJSON_GetObjectItem(json, "bmeasure_log");
    if(!arrayItem)
    {
        pi_log_e("boot measure array error\n");
        return -1;
    }

    arraySize = cJSON_GetArraySize(arrayItem);
    //printf("arraySize = %d\n",arraySize);

    for(i = 0; i < arraySize; i++)
    {
        object = cJSON_GetArrayItem(arrayItem, i);

        memset(&lms, 0, sizeof(struct log_msg_st));

        //time
        item = cJSON_GetObjectItem(object, "time");
        if(NULL != item)
        {
            memcpy(lms.time, item->valuestring, strlen(item->valuestring)<TIME_LEN?strlen(item->valuestring):TIME_LEN);
            //printf("\033[35m" "time: %s-%d\n" "\033[0m", lms.time, strlen(item->valuestring));
        }

        //subject
        item = cJSON_GetObjectItem(object, "processname");
        if(NULL != item)
        {
            memcpy(lms.subject, item->valuestring, strlen(item->valuestring));
            //printf("\033[35m" "subject: %s-%d\n" "\033[0m", lms.subject, strlen(item->valuestring));
        }

        //object
        item = cJSON_GetObjectItem(object, "object");
        if(NULL != item)
        {
            memcpy(lms.object, item->valuestring, strlen(item->valuestring));
            //printf("\033[35m" "object: %s-%d\n" "\033[0m", lms.object, strlen(item->valuestring));
        }

        //local_user
        item = cJSON_GetObjectItem(object, "local_user");
        if(NULL != item)
        {
            sprintf(item->valuestring, "%d", lms.userid);
            //printf("\033[35m" "userid: %d\n" "\033[0m", lms.userid);
        }

        //pid
        item = cJSON_GetObjectItem(object, "pid");
        if(NULL != item)
        {
            sscanf(item->valuestring, "%d", &(lms.pid));
            //printf("\033[35m" "pid: %d\n" "\033[0m", lms.pid);
        }

        //type
        item = cJSON_GetObjectItem(object, "type");
        if(NULL != item)
        {
            sscanf(item->valuestring,"%d", (int*)&(lms.type));
            //printf("\033[35m" "type: %d\n" "\033[0m",lms.type);
        }

        //hash
        item = cJSON_GetObjectItem(object, "pro_hash");
        if(NULL != item)
        {
            memcpy(lms.hash, item->valuestring, strlen(item->valuestring)<HASH_LEN?strlen(item->valuestring):HASH_LEN);
            //printf("\033[35m" "pro_hash: %s-%d\n" "\033[0m", lms.hash, strlen(item->valuestring));
        }

        //result
        item = cJSON_GetObjectItem(object, "result");
        if(NULL != item)
        {
            lms.result = item->valueint;
            //printf("\033[35m" "result: %d\n" "\033[0m", lms.result);
        }

        //save httc log to flash
        if(0 != lms.result)
        {
            HttcSaveLogToFlash(HTTC_FAIL_LOG, &lms);
        }
        else if(0 == lms.result)
        {
            HttcSaveLogToFlash(HTTC_SUCCESS_LOG, &lms);
        }
    }

    cJSON_Delete(json);

    return 0;
}


/************************************************************************
 * Function Name : HttcParseCJsonReportLinux
 * Description   : 解析白名单、文件访问控制、动态度量日志
 * Parameters    : char* pBody
 *				  
 * Return        : void
 * Data	      	: 2020 - 10 - 28
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcParseCJsonReportLinux(char* pBody)
{
    cJSON *json, *arrayItem, *item, *object;
    int arraySize, i, k;
    struct log_msg_st lms;
    const char* ObjectItem[3] = {"whitelist_log", "fileaccess_log", "dmeasure_log"};

    json = cJSON_Parse(pBody);
    if(!json)
    {
        pi_log_e("report linux pBody error\n");
        return -1;
    }
    //循环解析三种日志
    for(k = 0; k < (sizeof(ObjectItem)/sizeof(char *)); k++)
    {
        arrayItem = cJSON_GetObjectItem(json, ObjectItem[k]);
        if(!arrayItem)
        {
            pi_log_e("report linux array error\n");
            return -1;
        }
        arraySize = cJSON_GetArraySize(arrayItem);

        //printf("arraySize = %d\n",arraySize);

        for(i = 0;i<arraySize;i++)
        {
            object = cJSON_GetArrayItem(arrayItem, i);

            memset(&lms, 0, sizeof(struct log_msg_st));

            //time
            item = cJSON_GetObjectItem(object, "time");
            if(NULL != item)
            {
                memcpy(lms.time, item->valuestring, strlen(item->valuestring)<TIME_LEN?strlen(item->valuestring):TIME_LEN);
                //printf("\033[35m" "time: %s-%d\n" "\033[0m", lms.time, strlen(item->valuestring));
            }

            //local_user
            item = cJSON_GetObjectItem(object, "local_user");
            if(NULL != item)
            {
                sprintf(item->valuestring, "%d", lms.userid);
                //printf("\033[35m" "local_user: %d\n" "\033[0m", lms.userid);
            }

            //pid
            item = cJSON_GetObjectItem(object, "pid");
            if(NULL != item)
            {
                sscanf(item->valuestring, "%d", &(lms.pid));
                //printf("\033[35m" "pid: %d\n" "\033[0m", lms.pid);
            }

            //subject
            item = cJSON_GetObjectItem(object, "processname");
            if(NULL != item)
            {
                memcpy(lms.subject, item->valuestring, strlen(item->valuestring));
                //printf("\033[35m" "subject: %s-%d\n" "\033[0m", lms.subject, strlen(item->valuestring));
            }

            //hash
            item = cJSON_GetObjectItem(object, "pro_hash");
            if(NULL != item)
            {
                memcpy(lms.hash, item->valuestring, strlen(item->valuestring)<HASH_LEN?strlen(item->valuestring):HASH_LEN);
                //printf("\033[35m" "pro_hash: %s-%d\n" "\033[0m", lms.hash, strlen(item->valuestring));
            }

            //object
            item = cJSON_GetObjectItem(object, "object");
            if(NULL != item)
            {
                memcpy(lms.object, item->valuestring, strlen(item->valuestring));
                //printf("\033[35m" "object: %s-%d\n" "\033[0m", lms.object, strlen(item->valuestring));
            }

            //type
            item = cJSON_GetObjectItem(object, "type");
            if(NULL != item)
            {
                sscanf(item->valuestring,"%hu", (unsigned short int*)&(lms.type));
                //printf("\033[35m" "type: %d\n" "\033[0m",lms.type);
            }

            //operate
            item = cJSON_GetObjectItem(object, "operate");
            if(NULL != item)
            {
                memcpy(lms.operate,item->valuestring,strlen(item->valuestring));
                //printf("\033[35m" "operate: %s\n" "\033[0m",lms.operate);
            }
            //result
            item = cJSON_GetObjectItem(object, "result");
            if(NULL != item)
            {
                sscanf(item->valuestring, "%hu", (unsigned short int*)&(lms.result));
                //printf("\033[35m" "result: %d\n" "\033[0m", lms.result);
            }

            //save httc log to flash
            if(RESULT_FAIL == lms.result)
            {
                HttcSaveLogToFlash(HTTC_FAIL_LOG, &lms);
            }
            else if(RESULT_SUCCESS == lms.result)
            {
                HttcSaveLogToFlash(HTTC_SUCCESS_LOG, &lms);
            }
        }
    }	

    cJSON_Delete(json);
    return 0;
}

/************************************************************************
 * Function Name : HttcParseLog
 * Description   : 解析可信日志
 * Parameters    : char* szBuf
 *				  
 * Return        : void
 * Data	      	: 2020 - 10 - 28
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcParseLog(char* szBuf)
{
    char* pInterfaceName = strstr(szBuf, "/platform/");
    if (!pInterfaceName)
    {
        pi_log_e("packet error!\n");
        return -1;
    }
    pInterfaceName += strlen("/platform/");

    char* packet = strstr(pInterfaceName, " ");
    if (!packet)
    {
        pi_log_e("packet error!\n");
        return -1;
    }
    *packet = '\0';
    packet++;

    char* pBody = strstr(packet, "\r\n\r\n");
    if (!pBody)
    {
        pi_log_e("pBody error!\n");
        return -1;
    }
    pBody += strlen("\r\n\r\n");

    if (strcmp(pInterfaceName, "reportLinuxLog") == 0)
    {
        //白名单、文件访问控制、动态度量日志
        return HttcParseCJsonReportLinux(pBody);
    }
    else if (strcmp(pInterfaceName, "reportBootMeasureLog") == 0)
    {
        //启动度量
        return HttcParseCJsonBootMeasure(pBody);
    }
    else if (strcmp(pInterfaceName, "linux_node_trusted_report_upload") == 0)
    {
        //可信报告
    }
    else if (strcmp(pInterfaceName, "linux_node_tpcmStatus_tcmStatus_upload") == 0)
    {
        //可信根状态
    }	

    return 0;
}


/*
/platform/reportBootMeasureLog
/platform/reportBasesLog
/platform/reportLinuxLog
/platform/linux_node_trusted_report_upload
/platform/linux_node_trusted_bootmeasure
/platform/linux_node_trusted_pubKey
/platform/linux_node_trusted_report_upload
/platform/linux_node_tpcmStatus_tcmStatus_upload
/platform/confirm_Mac_Policy
/platform/confirm_DMeasure_Policy
/platform/confirm_Audit_Policy
/platform/confirm_TpcmSwitch_Policy
/platform/confirm_WhiteList_Policy
/platform/conform_linuxNetControl_policy
/platform/modAction/uploadModuleInstallInfo
/platform/upload_tpcm_primary_key
可信服务post字段中可能使用的路径
*/
int ClienMessageCompare(char *recv_buf)
{
	char path1[64] = {"POST /platform/report\0"};
	char path2[64] = {"POST /platform/linux_node_t\0"};
	char path3[64] = {"POST /platform/confirm_\0"};
	char path4[64] = {"POST /platform/conform_\0"};
	char path5[64] = {"POST /platform/modAction\0"};
	char path6[64] = {"POST /platform/upload_tpcm_primary_key\0"};
	//printf("len1=%d len1=%d len1=%d len1=%d len1=%d len1=%d\n", strlen(path1), strlen(path2), strlen(path3), strlen(path4), strlen(path5), strlen(path6));

	if(  memcmp(path1,recv_buf,strlen(path1)) && memcmp(path2,recv_buf,strlen(path2)) &&
		 memcmp(path3,recv_buf,strlen(path3)) && memcmp(path4,recv_buf,strlen(path4)) &&
		 memcmp(path5,recv_buf,strlen(path5)) && memcmp(path6,recv_buf,strlen(path6))
	  )
	{
		//printf("ClienMessageCompare XXXXXXXXXXXXXXXXXXXXXXXXXXXXX!\n");
		return -1;
	}
	else
	{
		//printf("ClienMessageCompare is ok!\n");
		return 0;
	}
}

/************************************************************************
 * Function Name : int HttcSocket_ServerThread(void* param)
 * Description   : httc log recv thread
 * Parameters    : void* param
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static void* HttcSocket_ServerThread(void* param)
{
    char buf[1024*512] = {0};
    int r = 0;
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    char res[256] = {0};
    if (sock < 0)
    {
        printf("socket error\n");
    }

    int opt = 1;
    setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, (const void *)&opt, sizeof(opt));

    struct sockaddr_in server_socket;
    bzero(&server_socket, sizeof(server_socket));
    server_socket.sin_family = AF_INET;
    server_socket.sin_addr.s_addr = htonl(INADDR_ANY);
    server_socket.sin_port = htons(8080);

    if (bind(sock, (struct sockaddr*) &server_socket, sizeof(struct sockaddr_in)) < 0)
    {
        printf("bind() error\n");
        close(sock);
        return (void*)-1;
    }
    if (listen(sock, _BACKLOG_) < 0)
    {
        printf("listen() error\n");
        close(sock);
        return (void*)-1;
    }

    printf("----------- new tsb --------\n");
    sprintf(res, "%s%s%s%s%s","HTTP/1.1 200 OK\r\n",
            "X-Frame-Options: SAMEORIGIN\r\n",
            "X-XSS-Protection: 1; mode=block\r\n",
            "X-Content-Type-Options: nosniff\r\n",
            "Strict-Transport-Security: max-age=16070400\r\n\r\n"
           );

    HttcServerStart();

    while (1)
    {
        socklen_t len = sizeof(struct sockaddr_in);
        struct sockaddr_in addr_cli;

        int client_sock = accept(sock, (struct sockaddr*)&addr_cli, &len);
        if (client_sock < 0)
        {
            printf("accept() error\n");
            return (void*)-1;
        }

        memset(buf, 0, sizeof(buf));
        r = recv(client_sock, buf, sizeof(buf), 0);
        if(r <= 0)
        {
            printf("recv error %d", r);
            close(client_sock);
            continue;
        }
        //printf("read  from client : %s\n", buf);

        if(0 == ClienMessageCompare(buf))
		{
			HttcParseLog(buf);
			send(client_sock, res, strlen(res), 0);
		}
        close(client_sock);
    }

    close(sock);
    pi_msleep(100);
    return NULL;
}

/************************************************************************
 * Function Name : int Httc_Socket_prolog(void)
 * Description   : httc_server_socket
 * Parameters    : void
 *				  
 * Return        : void
 * Data	      	 : 2023 - 01 - 14
 * By	         : ZhangJie
 * Explain       : None
 *************************************************************************/
int Httc_Socket_prolog(void)
{
    char tpcm_ver[32] = {0};

    // create a thread to listen httc_socket
    g_httc_socket_taskID = pi_thread_create( HttcSocket_ServerThread,
            1,
            NULL,
            0,
            NULL,
            "httc_server_socket" );
    if (INVALIDTHREAD == g_httc_socket_taskID)
    {
        pi_log_e("httc_server_socket - can't start thread\n");
        return (-1);
    }

    HttcGetVersion(tpcm_ver);

    return 0;
}

/************************************************************************
 * Function Name : int HttcGetLogNumber(HTTC_LOG_TYPE log_type)
 * Description   : get httc log numbers
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
int HttcGetLogNumber(HTTC_LOG_TYPE log_type)
{
    FILE  *fp = NULL;
    uint32_t file_size = 0;
    uint32_t log_len = sizeof(struct log_msg_st);

    char *path = NULL;

    if(HTTC_SUCCESS_LOG == log_type)
        path = HTTC_SUCESS_LOG_FILE_PATH;
    else
        path = HTTC_FAIL_LOG_FILE_PATH;

    fp = fopen(path, "r");
    if (NULL == fp)
    {
        pi_log_e("fopen %s error.\n", path);
        return 0;
    }

    fseek(fp, 0, SEEK_END);
    file_size = ftell(fp);
    file_size = file_size - 4;
    fclose(fp);
    fp = NULL;

    return (file_size/log_len);
}

/************************************************************************
 * Function Name : int HttcExportLog
 * Description   : export httc audit log
 * Parameters    : HTTC_LOG_TYPE  log_type (1.HTTC_SUCCESS_LOG   2.HTTC_FAIL_LOG)
 *				   file path
 * Return        : void
 * Data	      	: 2013 - 09 - 26
 * By	        : ZhangJie
 * Explain       : None
 *************************************************************************/
int httc_export_logfile(HTTC_LOG_TYPE log_type, const char *file_path)
{
    FILE  *fp_src = NULL;
    FILE  *fp_dst = NULL;
    int    i,j;
    char   log_buff[HTTC_LOG_LINE_WIDTH];

    uint32_t src_index;
    struct log_msg_st log;
    char   str_buff[64];
    //uint32_t log_buff_index;

    //int log_num_max;
    char *src_path = NULL;
    char *des_path = NULL;
    const char* LogOperate[9] = {"W","R","A","D","M","O","C","B","W|A"};
    const char* RLogOperate[9] = {"write","read","add","delete","measure","mount","create","rename","writ add"};
    //const char* LogType[4] = {"whitelist_log^^^","fileaccess_log^^^", "no type^^^","dmeasure_log^^^"};

    if(HTTC_SUCCESS_LOG == log_type)
    {
        src_path = HTTC_SUCESS_LOG_FILE_PATH;
    }
    else
    {
        src_path = HTTC_FAIL_LOG_FILE_PATH;
    }


    fp_src = fopen(src_path, "r");
    fp_dst = fopen(file_path, "w");
    if (NULL == fp_dst)
    {
        pi_log_e("fopen %s pi_log_e.\n", des_path);
        if (fp_src) fclose(fp_src);
        return (-5);
    }

    if (NULL == fp_src)
    {
        pi_log_e("fopen %s pi_log_e.\n", src_path);

        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        snprintf((char *)log_buff, HTTC_LOG_LINE_WIDTH, 
                "      no data^^^\n");
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
        fclose(fp_dst);
        fp_dst = NULL;
        return -1;
    }

    for (i = 0; i <= HttcGetLogNumber(log_type) - 1; i++)
    {

        // 读取日志文件
        src_index = i *sizeof(struct log_msg_st) + sizeof(uint32_t);
        fseek(fp_src, src_index, SEEK_SET);
        fread((void *)&log, sizeof(struct log_msg_st), 1, fp_src);

        //log_buff_len   = HTTC_LOG_LINE_WIDTH;
        //log_buff_index = 0;
        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        // index
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^", i+1);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // time
        {	
            strncat(log_buff,(char *)&log.time,strlen((char *)log.time));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // type and operate
        {
            switch (log.type) 
            {
                case TYPE_WHITELIST:
                    strncat(log_buff,"WHITELIST^^^",strlen("WHITELIST^^^"));
                    strncat(log_buff,"EXEC^^^",strlen("EXEC^^^"));
                    break;

                case TYPE_FILE_ACCESS:
                    strncat(log_buff,"FILE_ACCESS^^^",strlen("FILE_ACCESS^^^"));
                    for(j = 0;j<9;j++)
                    {
                        if(0 == strncmp(LogOperate[j],(char *)&log.operate,strlen((char *)&log.operate)))
                        {
                            strncat(log_buff,RLogOperate[j],strlen(RLogOperate[j]));
                            strncat(log_buff,"^^^",strlen("^^^"));
                            break;
                        }
                    }
                    break;

                case TYPE_DMEASURE:
                    strncat(log_buff,"DMEASURE^^^",strlen("DMEASURE^^^"));
                    strncat(log_buff,"PERIODICITY_DMEASURE^^^",strlen("PERIODICITY_DMEASURE^^^"));
                    break;

                case TYPE_BMEASURE:
                    strncat(log_buff,"BMEASURE^^^",strlen("BMEASURE^^^"));
                    strncat(log_buff,"BOOT_MEASURE^^^",strlen("BOOT_MEASURE^^^"));
                    break;
            }
        }
        // userid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4u^^^",log.userid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // pid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4u^^^",log.pid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // hash
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));

            memcpy(str_buff, log.hash, HASH_LEN);

            strncat(log_buff,str_buff,strlen(str_buff));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // subject
        {	
            strncat(log_buff,(char *)&log.subject,strlen((char *)log.subject));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // object
        {	
            strncat(log_buff,(char *)&log.object,strlen((char *)log.object));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // result
        {
            if (log.type == TYPE_BMEASURE) 
            {
                if(0 == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
            else
            {
                if(RESULT_SUCCESS == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
        }
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }

        //Log(5, "%s", log_buff);
        // 写入文本文件
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
    }

    fclose(fp_src);
    fp_src = NULL;
    fclose(fp_dst);
    fp_dst = NULL;
    return 0;    
}


/************************************************************************
 * Function Name : int HttcExportLog
 * Description   : export httc audit log
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  unsigned int  export_start  	index of start secure log
 *				  unsigned int  export_end		index of end secure log
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
    int HttcExportLog
(
 HTTC_LOG_TYPE log_type,
 unsigned int  export_start,
 unsigned int  export_end
 )
{
    FILE  *fp_src = NULL;
    FILE  *fp_dst = NULL;
    int    i,j;
    char   log_buff[HTTC_LOG_LINE_WIDTH];

    uint32_t src_index;
    struct log_msg_st log;
    char   str_buff[64];
    /*uint32_t log_buff_len, log_buff_index;*/

    int log_num_max;
    char *src_path = NULL;
    char *des_path = NULL;
    const char* LogOperate[9] = {"W","R","A","D","M","O","C","B","W|A"};
    const char* RLogOperate[9] = {"write","read","add","delete","measure","mount","create","rename","writ add"};
    /*const char* LogType[4] = {"whitelist_log^^^","fileaccess_log^^^", "no type^^^","dmeasure_log^^^"};*/

    if(HTTC_SUCCESS_LOG == log_type)
    {
        src_path = HTTC_SUCESS_LOG_FILE_PATH;
        des_path = HTTC_WEB_SUCESS_LOG_PATH;
        log_num_max = HTTC_SUCESS_LOG_MAX_NUMBER;
    }
    else
    {
        src_path = HTTC_FAIL_LOG_FILE_PATH;
        des_path = HTTC_WEB_FAIL_LOG_PATH;
        log_num_max = HTTC_FAIL_LOG_MAX_NUMBER;
    }


    if ((log_num_max <= export_end)
            ||(log_num_max <= export_start)
            ||(export_start > export_end))
    {
        pi_log_e("invalid export_end or export_start\n");
        return (-2);
    }

    if(export_end >= HttcGetLogNumber(log_type))
    {
        export_end = HttcGetLogNumber(log_type) - 1;
    }

    fp_src = fopen(src_path, "r");
    fp_dst = fopen(des_path, "w");
    if (NULL == fp_dst)
    {
        pi_log_e("fopen %s error.\n", des_path);
        if (fp_src) fclose(fp_src);
        return (-5);
    }

    if (NULL == fp_src)
    {
        pi_log_e("fopen %s error.\n", src_path);

        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        snprintf((char *)log_buff, HTTC_LOG_LINE_WIDTH, 
                "      no data^^^\n");
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
        fclose(fp_dst);
        fp_dst = NULL;
        return -1;
    }

    for (i = export_start; i <= export_end; i++)
    {

        // 读取日志文件
        src_index = i *sizeof(struct log_msg_st) + sizeof(uint32_t);
        fseek(fp_src, src_index, SEEK_SET);
        fread((void *)&log, sizeof(struct log_msg_st), 1, fp_src);

        // ת��Ϊ�ı�
        /*log_buff_len   = HTTC_LOG_LINE_WIDTH;*/
        /*log_buff_index = 0;*/
        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        // index
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^", i+1);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // time
        {	
            strncat(log_buff,(char *)&log.time,strlen((char *)log.time));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // type and operate
        {
            switch (log.type) 
            {
                case TYPE_WHITELIST:
                    strncat(log_buff,"WHITELIST^^^",strlen("WHITELIST^^^"));
                    strncat(log_buff,"EXEC^^^",strlen("EXEC^^^"));
                    break;

                case TYPE_FILE_ACCESS:
                    strncat(log_buff,"FILE_ACCESS^^^",strlen("FILE_ACCESS^^^"));
                    for(j = 0;j<9;j++)
                    {
                        if(0 == strncmp(LogOperate[j],(char *)&log.operate,strlen((char *)&log.operate)))
                        {
                            strncat(log_buff,RLogOperate[j],strlen(RLogOperate[j]));
                            strncat(log_buff,"^^^",strlen("^^^"));
                            break;
                        }
                    }
                    break;

                case TYPE_DMEASURE:
                    strncat(log_buff,"DMEASURE^^^",strlen("DMEASURE^^^"));
                    strncat(log_buff,"PERIODICITY_DMEASURE^^^",strlen("PERIODICITY_DMEASURE^^^"));
                    break;

                case TYPE_BMEASURE:
                    strncat(log_buff,"BMEASURE^^^",strlen("BMEASURE^^^"));
                    strncat(log_buff,"BOOT_MEASURE^^^",strlen("BOOT_MEASURE^^^"));
                    break;
            }
        }
        // userid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^",log.userid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // pid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^",log.pid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // hash
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));

            memcpy(str_buff, log.hash, HASH_LEN);

            strncat(log_buff,str_buff,strlen(str_buff));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // subject
        {	
            strncat(log_buff,(char *)&log.subject,strlen((char *)log.subject));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // object
        {	
            strncat(log_buff,(char *)&log.object,strlen((char *)log.object));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // result
        {
            if (log.type == TYPE_BMEASURE) 
            {
                if(0 == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
            else
            {
                if(RESULT_SUCCESS == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
        }
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }

        //Log(5, "%s", log_buff);
        // 写入文本文件
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
    }

    fclose(fp_src);
    fp_src = NULL;
    fclose(fp_dst);
    fp_dst = NULL;
    return 0;    
}

/************************************************************************
 * Function Name : int HttcGetVersion
 * Description   : export httc audit log
 * Parameters    : char * _tpcm_ver tpcm version point
 *				  
 * Return        : void
 * Data	      	: 2021 - 04 - 01
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
int HttcGetVersion(char * _tpcm_ver)
{
    char tpcm[32] = {0};
    char tsb[32] = {0};

    if (_tpcm_ver == NULL) {
        pi_log_e("param error\n");
        return -1;
    }

    HTTC_GetVersion(tpcm, 32, tsb, 32);

    memcpy(_tpcm_ver, tpcm, 32);

    pi_log_i("TPCM(%s) TSB(%s)\n", tpcm, tsb);

    return 0;
}

/***
 * Description: check httc service start result 
 * 
 * 
 * *
 * */




/*static HTTC_START_STATUS httc_start_status=ENUM_HTTC_NOT_START;*/


#define DMEASURE_SUCCESS_STR    "start Dmeasure service success!!!"
#define FAC_SUCCESS_STR         "start Fac service success!!!"
#define SMEASURE_SUCCESS_STR    "start Smeasure service success!!!"
#define HTTC_CLEARFLAG_FILE     "/usr/local/httcsec/httc_clearflag"
#define HTTC_RECORD_FILE        "/settings/httc_record"
#define HTTC_STATUE_FILE        "/tmp/httc_statue"

//-1 继续查询可信状态（可信启动未完成）
//0 可信启动成功
//1 可信启动失败
int get_httc_status()
{	
    int fd = 0;
    static int statue = -1;
    if(statue == 0)
        return 0;
	if(!access(HTTC_STATUE_FILE, F_OK))
	{
		fd = open(HTTC_STATUE_FILE, O_RDONLY);
		if(fd < 0)
		{
			printf("open /tmp/httc_statue error!\n");
            close(fd);
			return -1;
		}
		int ret = read(fd, &statue, sizeof(statue));
		if(ret <= 0)
		{
			printf("read statue error!\n");
            close(fd);
			return -1;
		}
		if(statue != 0)
		{
		    printf("get_httc_status : %d\n", statue);
		}	
        close(fd);
		return statue;
	}
	else
	{
		return -1;
	}
}

static int read_httc_record_file(int* run_cnt,int *fail_cnt)
{
    char linestring[32]={};
    FILE *fp = fopen(HTTC_RECORD_FILE,"r");
    int ret;
    char *fret = NULL;
    if(!fp)
    {
        pi_log_e("%s open fail :%s\n",HTTC_RECORD_FILE,strerror(errno));        
        return -1;
    }
    fret = fgets(linestring, sizeof(linestring),fp);
    if(fret==NULL&&!feof(fp)){
        pi_log_e("fgets %s 1 error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return  -1;
    }  
    ret = sscanf(linestring,"run_cnt=%d",run_cnt); 
    if(ret == 0 ||ret == EOF)
    {
        fclose(fp);    
        return -1;
    }

    printf("read success cnt:%d\n",*run_cnt);
    fret = fgets(linestring, sizeof(linestring),fp);
    if(fret==NULL&&!feof(fp)){
        pi_log_e("fgets %s 2line error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return  -1;
    }  
    ret = sscanf(linestring,"fail_cnt=%d",fail_cnt); 
    if(ret == 0 ||ret == EOF)
    {
        fclose(fp);    
        return -1;
    }

    printf("read fail cnt:%d\n",*fail_cnt);
    fclose(fp);
    return 0;
} 

static int write_httc_record_file(int run_cnt,int fail_cnt)
{
    FILE *fp = fopen(HTTC_RECORD_FILE,"w+");
    char linestring[32] = {0};
    if(!fp)

    {
        pi_log_e("%s open fail :%s\n",HTTC_RECORD_FILE,strerror(errno));        
        return -1;
    }
    sprintf(linestring,"run_cnt=%d\n",run_cnt);
    if(fputs(linestring,fp)==EOF){
        pi_log_e("fwrite %s error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return -1;
    }        
    sprintf(linestring,"fail_cnt=%d\n",fail_cnt);
    if(fputs(linestring,fp)==EOF){
        pi_log_e("fwrite %s error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return -1;
    }        
    fclose(fp);
    return 0;
}

#if 0
static void record_httc_fail_cnt()
{
    int run_cnt = 0;
    int fail_cnt = 0;

    if(!read_httc_record_file(&run_cnt,&fail_cnt))
        write_httc_record_file(run_cnt,fail_cnt+1);
    else
        write_httc_record_file(1,1);

}
#endif


static void record_httc_run_cnt()
{
    int run_cnt = 0;
    int fail_cnt = 0;

    if(!read_httc_record_file(&run_cnt,&fail_cnt))
        write_httc_record_file(run_cnt+1,fail_cnt);
    else
        write_httc_record_file(1,0);

}



/************************************************************************
 * Function Name : void HttcServerStart(void)
 * Description   : HttcServerStart
 * Parameters    : void
 *				  
 * Return        : void
 * Data	      	: 2021 - 04 - 02
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
#define HTTC_CONFIG_PATH   /settings/config_httc
void HttcServerStart(void)
{
#if CONFIG_HTTC 
    system("echo CONFIG_HTTC=1 > /settings/config_httc");
    record_httc_run_cnt();
    pi_runcmd(NULL, 0, 1, "/root/httc_start.sh");
#else
    remove(HTTC_CONFIG_PATH);
#endif	
	
}

/************************************************************************
 * Function Name : void HttcServerStop(void)
 * Description   : HttcServerStop
 * Parameters    : void
 *				  
 * Return        : void
 * Data	      	: 2021 - 05 - 10
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
void HttcServerStop(void)
{
    RN_RUN_CMD_BG("/usr/local/httcsec/Base/srv stop");
    printf("RN_RUN_CMD_BG(/usr/local/httcsec/Base/srv stop)\n");
}


/* ************************************* (c) Roon ** End File ******************************************* */
