/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file bridge.h
 * @addtogroup bridge
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief bridge init
 */
#ifndef __MODULES__BRIDGE__BRIDGE_H__
#define __MODULES__BRIDGE__BRIDGE_H__

#include <quickjs.h>
#include "runtime.h"
#include "basic/config.h"

/**
 * @brief   send the data to js through the bridge module
 * @param[in] *prt    :a heap space runtime handle
 * @param[in] data_length  :data len
 * @param[in] *data  :data
 * <AUTHOR> @date    2024-06-11
 */
void send_to_bridge(PeSFRunTime *prt, uint16_t data_length, const char *data);
#endif /* __MODULES__BRIDGE__BRIDGE_H__  */
/**
 * @}
 */
 
