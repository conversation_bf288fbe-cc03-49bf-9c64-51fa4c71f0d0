//*****************************************************************************
//  Copyright (C) 2014 Cambridge Silicon Radio Ltd.
//  $Header: //depot/imgeng/sw/inferno/appsrc/net/ipp/ippattribs.h#16 $
//  $Change: 243377 $ $Date: 2014/11/05 $
//
/// @file
/// IPP (Internet Printing Protocol) server, attributes handling
///
/// @ingroup Network
//
//*****************************************************************************
#ifndef IPPATTRIBS_H
#define IPPATTRIBS_H 1

#include "nettypes.h"
#include "network.h"


typedef int (*IPPATTRCB)(void *cookie, const char *name, int type, void *value);

int IPPATTRgetIndexedString                     (const char *str, int index, char *psubstr, int nsubstr);

int IPPATTRgetRequestedPrinterAttributes        (PIPPCTX ppp, IPPATTRCB callback, void *cookie);
int IPPATTRrequestPrinterAttribute              (PIPPCTX ppp, const char *name);
int IPPATTRrequestAllPrinterAttributes          (PIPPCTX ppp, int onoff);
int IPPATTRgetPrinterAttribute                  (uint32_t service, const char *name, int *type, void **value, int index, int *count);
int IPPATTRsetPrinterAttribute                  (uint32_t service, const char *name, void *value, int index);

int IPPATTRgetSetOperationAttributes            (PIPPCTX ppp, IPPATTRCB callback, void *cookie);
int IPPATTRsetJobAttributesFromSetOperationAttributes
                                                (PIPPCTX ppp, void *pSet);
int IPPATTRgetOperationAttribute                (PIPPCTX ppp, const char *name, int *type, void **value, int index, int *count);
int IPPATTRsetOperationAttribute                (PIPPCTX ppp, const char *name, void *value, int index);
int IPPATTRcheckOperationAttribute              (PIPPCTX ppp, const char *name, int pedantic, int group, int type);
int IPPATTRrequestOperationAttribute            (PIPPCTX ppp, const char *name);
int IPPATTRrequestAllOperationAttributes        (PIPPCTX ppp, int onoff);
int IPPATTRclearOperationAttributes             (PIPPCTX ppp);

int IPPATTRgetRequestedJobAttributes            (void *pSet, IPPATTRCB callback, void *cookie);
int IPPATTRgetJobAttributeIsSet                 (void *pSet, const char *name, int *isSet);
int IPPATTRgetJobAttribute                      (void *pSet, const char *name, int *type, void **value, int index, int *count);
int IPPATTRsetJobAttribute                      (void *pSet, const char *name, void *value, int index);
int IPPATTRrequestJobAttribute                  (void *pSet, const char *name);
int IPPATTRrequestAllJobAttributes              (void *pSet, int onoff);
int IPPATTRsetAllJobAttributes                  (void *pSet, int onoff);
int IPPATTRclearJobAttributes                   (void *pSet);
int IPPATTRcreateJobAttributeSet                (void **pAttribs);
int IPPATTRdestroyJobAttributeSet               (void *pAttribs);

int IPPATTRaddUnsupportedAttribute              (PIPPCTX ppp, const char *name);
int IPPATTRgetUnsupportedAttributes             (PIPPCTX ppp, IPPATTRCB callback, void *cookie);
int IPPATTRclearUnsupportedAttributes           (PIPPCTX ppp, int stomp);

int IPPATTRbeginCollection                      (PIPPCTX ppp, int *level, const char *name, int ind);
int IPPATTRaddCollectionMember                  (PIPPCTX ppp, int level, const char *name, int type, void *value, int index);
int IPPATTRendCollection                        (PIPPCTX ppp, int *level, const char *name, int index);

IPP_ATTR_S* IPPATTRmediaStringToProperty        (const char* medianame, PARSER_DATA_S* parser_data);
int32_t     IPPATTRmediaPropertyToParam         (IPP_ATTR_S* pcol, PARSER_DATA_S* parser_data, int32_t mediacolIsSet);

int IPPATTRupdatePrinterAttributes              (void);

int32_t ippattr_set_finishings_col(int32_t finishing_num, int32_t index);
int32_t ippattr_get_finishings_col_num(IPP_ATTR_S* P_finishings_col);

void    IPPATTRfinishStringToProperty(int32_t finishings_num, int32_t job_orientation, PARSER_DATA_S* parser_data);

int32_t ippattr_set_printer_attribute_internal  (uint32_t services, const char* name, void* value, int index);

int32_t ippattr_prolog                          (NET_CTX_S* net_ctx);

void    ippattr_epilog                          (void);

#endif

