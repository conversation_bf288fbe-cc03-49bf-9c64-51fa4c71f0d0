#include "nettypes.h"
#include "netmisc.h"
#include "qxml.h"
//#include "soap.h"

#define QXML_STATIC static

static struct tag_entities
{
    char* ename;
    int   eval;
}
g_entitytab[] =
{
    { "quot",   34 },
    { "amp",    38 },
    { "apos",   39 },
    { "lt",     60 },
    { "gt",     62 },
    { "OElig",  338 },
    { "oelig",  339 },
    { "Scaron", 352 },
    { "scaron", 353 },
    { "Yuml",   376 },
    { "circ",   710 },
    { "tilde",  732 },
    { "ensp",   8194 },
    { "emsp",   8195 },
    { "thinsp", 8201 },
    { "zwnj",   8204 },
    { "zwj",    8205 },
    { "lrm",    8206 },
    { "rlm",    8207 },
    { "ndash",  8211 },
    { "mdash",  8212 },
    { "lsquo",  8216 },
    { "rsquo",  8217 },
    { "sbquo",  8218 },
    { "ldquo",  8220 },
    { "rdquo",  8221 },
    { "bdquo",  8222 },
    { "dagger", 8224 },
    { "Dagger", 8225 },
    { "permil", 8240 },
    { "lsaquo", 8249 },
    { "rsaquo", 8250 },
    { "euro",   8364 }
};

//***********************************************************************
QXML_STATIC char QXMLentityToken(PQXML pXML, char* pent)
{
    int j;
    char nc;

    if (! pXML->m_expandum)
        return 0;

    for (j = 0, nc = 0; j < (sizeof(g_entitytab)/sizeof(struct tag_entities)); j++)
    {
        if (! strcmp(pent, g_entitytab[j].ename))
        {
            // [TODO - utf-8 encode eval]
            nc = (char)g_entitytab[j].eval;
            break;
        }
    }
    return nc;
}

//***********************************************************************
QXML_STATIC int QXMLparseToken(PQXML pXML, char** ptok, int* ptoklen, QXMLtokenType* ptype)
{
    int ec = 0;
    char    nc;

    if (! pXML || ! ptok || ! ptoklen || ! ptype)
    {
        return XML_BAD_PARAMETER;
    }
    pXML->m_token  = pXML->m_cur;
    pXML->m_toklen = 0;

    *ptok    = pXML->m_token;
    *ptoklen = pXML->m_toklen;

    while (ec == 0)
    {
        nc = *pXML->m_cur;
        if (! nc)
            return XML_STREAM_EOF;

        pXML->m_column++;

        if (nc == '\r')
        {
            pXML->m_cur++;
            if (pXML->m_toklen == 0)
            {
                pXML->m_token++;
                *ptok = pXML->m_token;
            }
            continue;
        }
        else if (nc == '\n')
        {
            pXML->m_cur++;
            pXML->m_line++;
            if (pXML->m_toklen == 0)
            {
                pXML->m_token++;
                *ptok = pXML->m_token;
            }
            pXML->m_column = 1;
            if (pXML->m_state == psComment)
                pXML->m_state = psCommentNL;
            continue;
        }
        else if (nc == '&')
        {
            char eb[8], *pxx;
            int  l, pxc;

            pxx = pXML->m_cur;
            pxc = pXML->m_column;
            pXML->m_cur++;
            for (l = 0; l < 7; l++)
            {
                pXML->m_column++;
                nc = *pXML->m_cur++;
                if (! nc)
                    return XML_STREAM_EOF;
                if (nc == ';')
                    break;
                eb[l] = nc;
            }
            eb[l] = '\0';
            nc = QXMLentityToken(pXML, eb);
            if (nc == '<' && pXML->m_state == psNonWhite)
            {
                pXML->m_cur -= 4;
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlValue;
                return 0;
            }
            else if (nc == 0)
            {
                // not a valid entity, unwind and treat the ampersand raw
                nc = '&';
                pXML->m_cur = pxx;
                pXML->m_column = pxc;
            }
            else
            {
                // was valid, so pretend the semicolon at the end is the char
                pXML->m_cur--;
            }
        }
        switch (pXML->m_state)
        {
        case psBase:
            switch (nc)
            {
            case '<':
                pXML->m_state = psElement1;
                pXML->m_cur++;
                if (pXML->m_toklen > 0)
                {
                    *ptoklen = pXML->m_toklen;
                    *ptype   = xmlValue;
                    return 0;
                }
                break;

            case ' ': case '\t':
                pXML->m_cur++;
                pXML->m_token = pXML->m_cur;
                *ptok = pXML->m_token;
                break;

            case '>':
                pXML->m_cur++;
                pXML->m_token = pXML->m_cur;
                *ptok = pXML->m_token;
                break;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psNonWhite;
                break;
            }
            break;

        case psNonWhite:
            switch (nc)
            {
            case '<':
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlValue;
                return 0;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;

        case psElement1:
            switch (nc)
            {
            case '!':
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psComment1;
                break;

            case '?':
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psVersion;
                break;

            case '>':
                pXML->m_cur++;
                pXML->m_toklen++;
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psElement;
                break;
            }
            break;

        case psElement:
            switch (nc)
            {
            case '>':
                pXML->m_cur++;
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            case '/':
                pXML->m_state = psSingularElement;
                pXML->m_cur++;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            case ' ': case '\t':
                pXML->m_state = psAttribLeft;
                pXML->m_cur++;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;

        case psSingularElement:
            switch (nc)
            {
            case '>':
                pXML->m_cur++;
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            default:
                NET_WARN("XML - bad element");
                ec = XML_BAD_ELEMENT;
                break;
            }
            break;

        case psComment1:
            switch (nc)
            {
            case '-':
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psComment2;
                break;

            case '>':
                pXML->m_cur++;
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            case '[':
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psComment;
                break;

            default:
#if 0 // this lets things like <!DOC *not* be comments
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psElement;
#else
                pXML->m_state = psComment;
#endif
                break;
            }
            break;

        case psComment2:
            switch (nc)
            {
            case '-':
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psComment;
                break;

            case '>':
                pXML->m_cur++;
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlElement;
                return 0;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                pXML->m_state = psElement;
                break;
            }
            break;

        case psComment:
        case psCommentNL:
            switch (nc)
            {
            case '>':
                pXML->m_cur++;
                if (pXML->m_toklen >= 3)
                {
                    if (pXML->m_cur[-2] == '-')
                    {
                        if (pXML->m_cur[-3] == '-')
                        {
                            pXML->m_state = psBase;
                            *ptoklen = pXML->m_toklen;
                            *ptype   = xmlComment;
                            return 0;
                        }
                    }
                    else if (pXML->m_cur[-2] == ']')
                    {
                        if (pXML->m_cur[-3] == ']')
                        {
                            pXML->m_state = psBase;
                            *ptoklen = pXML->m_toklen;
                            *ptype   = xmlClearData;
                            return 0;
                        }
                    }
                }
                // default, no ">" should be raw inside a comment
                pXML->m_state = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlComment;
                return 0;

            case ' ': case '\t':
                if (pXML->m_state != psCommentNL)
                {
                    pXML->m_toklen++;
                }
                pXML->m_cur++;
                break;

            default:
                if (pXML->m_state == psCommentNL)
                {
                    pXML->m_state = psComment;
                }
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;

        case psVersion:
            switch (nc)
            {
            case '>':
                pXML->m_cur++;
                pXML->m_toklen++;
                if (pXML->m_toklen >= 1)
                {
                    if (pXML->m_token[pXML->m_toklen - 1] == '?')
                    {
                        pXML->m_state = psBase;
                        *ptoklen = pXML->m_toklen;
                        *ptype   = xmlVersion;
                        return 0;
                    }
                    else
                    {
                        NET_WARN("XML - bad version");
                        ec = XML_BAD_VERSION_SPEC;
                    }
                }
                else
                {
                    NET_WARN("XML - bad version");
                    ec = XML_BAD_VERSION_SPEC;
                }
                break;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;

        case psAttribLeft:
            switch (nc)
            {
            case '=':
                pXML->m_state  = psAttribRight;
                pXML->m_cur++;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlAttributeName;
                return 0;

            case '/':
                pXML->m_cur++;
                pXML->m_state = psSingularElement;
                break;

            case ' ': case '\t':
                pXML->m_cur++;
                if (pXML->m_token[0] == ' ' || pXML->m_token[0] == '\t')
                {
                    pXML->m_token = pXML->m_cur;
                    *ptok = pXML->m_token;
                }
                else
                {
                    *ptoklen = pXML->m_toklen;
                    *ptype   = xmlAttributeName;
                    return 0;
                }
                break;

            case '>':
                pXML->m_cur++;
                pXML->m_state = psBase;
                if (pXML->m_toklen > 0)
                {
                    *ptoklen = pXML->m_toklen;
                    *ptype   = xmlAttributeName;
                    return 0;
                }
                else
                {
                    pXML->m_token = pXML->m_cur;
                    *ptok = pXML->m_token;
                    pXML->m_toklen++;
                }
                break;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;

        case psAttribRight:
            switch (nc)
            {
            case '\"':
                pXML->m_state = psAttribValue;
                pXML->m_token = pXML->m_cur;
                pXML->m_toklen++;
                pXML->m_cur++;
                break;

            case ' ': case '\t':
                pXML->m_cur++;
                break;

            case '>':
                pXML->m_cur++;
                NET_WARN("XML - malformed attribute");
                ec = XML_NO_ATTRIB_VALUE;
                break;

            default:
                pXML->m_cur++;
                pXML->m_state = psAttribUnquotedValue;
                break;
            }
            break;

        case psAttribValue:
            switch (nc)
            {
            case '\"':
                pXML->m_cur++;
                pXML->m_state  = psAttribLeft;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlAttributeValue;
                return 0;

            case '>':
                pXML->m_cur++;
                NET_WARN("XML - malformed attribute");
                ec = XML_NO_ATTRIB_QUOTE;
                break;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;

        case psAttribUnquotedValue:
            switch (nc)
            {
            case ' ': case '\t':
                pXML->m_cur++;
                pXML->m_state  = psAttribLeft;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlAttributeValue;
                return 0;

            case '>':
                pXML->m_cur++;
                pXML->m_state  = psBase;
                *ptoklen = pXML->m_toklen;
                *ptype   = xmlAttributeValue;
                return 0;

            default:
                pXML->m_toklen++;
                pXML->m_cur++;
                break;
            }
            break;
        }
    }
    return ec;
}

//***********************************************************************
char* QXMLnextValue(PQXML pxml)
{
    int ec;
    char*   ptok;
    int     tokl;
    char*   pstrt;
    QXMLtokenType type;
    QXMLparseState startstate;

    for (pstrt = pxml->m_cur, startstate = pxml->m_state, ec = 0; ec == 0;)
    {
        ec = QXMLparseToken(pxml, &ptok, &tokl, &type);

        if (ec == 0)
        {
            if (type == xmlValue)
            {
                return ptok;
            }
            else if (type != xmlComment && type != xmlAttributeName && type != xmlAttributeValue)
            {
                break;
            }
        }
    }
    pxml->m_cur = pstrt;
    pxml->m_state = startstate;
    return NULL;
}

//***********************************************************************
char* QXMLnextAttribute(PQXML pxml, char* ptag, int ntag)
{
    int ec;
    char*   ptok;
    int     tokl;
    char*   pstrt;
    QXMLtokenType type;
    QXMLparseState startstate;

    for (pstrt = pxml->m_cur, startstate = pxml->m_state, ec = 0; ec == 0;)
    {
        ec = QXMLparseToken(pxml, &ptok, &tokl, &type);

        if (ec == 0)
        {
            if (type == xmlAttributeName)
            {
                QXMLattributeCopy(pxml, ptag, ptok, ntag);

                if (pxml->m_state == psAttribLeft || pxml->m_state == psAttribRight)
                {
                    for (pstrt = pxml->m_cur, startstate = pxml->m_state, ec = 0; ec == 0;)
                    {
                        ec = QXMLparseToken(pxml, &ptok, &tokl, &type);

                        if (type != xmlComment && type != xmlAttributeName)
                            break;
                    }
                    if (type != xmlAttributeValue)
                    {
                        pxml->m_cur = pstrt;
                        pxml->m_state = startstate;
                        return NULL;
                    }
                }
                return ptok;
            }
            else if (type != xmlComment)
            {
                break;
            }
        }
    }
    pxml->m_cur = pstrt;
    pxml->m_state = startstate;
    return NULL;
}


//***********************************************************************
char* QXMLnextElement(PQXML pxml)
{
    int ec;
    char*   ptok;
    int     tokl;
    char*   pstrt;
    QXMLtokenType type;
    QXMLparseState startstate;

    for (pstrt = pxml->m_cur, startstate = pxml->m_state, ec = 0; ec == 0;)
    {
        ec = QXMLparseToken(pxml, &ptok, &tokl, &type);

        if (ec == 0 && type == xmlElement)
        {
            if (
                    (ptok[0] == '/')
                ||  (ptok[0] == '<' && ptok[1] == '/')
                ||  (ptok[0] == '&' && ptok[1] == 'l' && ptok[2] == 't' && ptok[3] == ';' && ptok[4] == '/')
            )
            {
                if (pxml->m_level > 0)
                    pxml->m_level--;
            }
            else
            {
                pxml->m_level++;
                return ptok;
            }
        }
    }
    pxml->m_cur = pstrt;
    pxml->m_state = startstate;
    return NULL;
}

//***********************************************************************
char* QXMLclosingElement(PQXML pxml)
{
    int ec;
    char*   ptok;
    int     tokl;
    char*   pstrt;
    QXMLtokenType type;

    for (pstrt = pxml->m_cur, ec = 0; ec == 0;)
    {
        ec = QXMLparseToken(pxml, &ptok, &tokl, &type);

        if (ec == 0 && type == xmlElement)
        {
            if (
                    (ptok[0] == '/')
                ||  (ptok[0] == '<' && ptok[1] == '/')
                ||  (ptok[0] == '&' && ptok[1] == 'l' && ptok[2] == 't' && ptok[3] == ';' && ptok[4] == '/')
            )
            {
                if (pxml->m_level > 0)
                    pxml->m_level--;
                return ptok;
            }
            else
            {
                NET_WARN("XML - expected closing element");
                pxml->m_level++;
                break;
            }
        }
    }
    pxml->m_cur = pstrt;
    return NULL;
}

//***********************************************************************
char* QXMLsiblingElement(PQXML pxml)
{
    char*   ptok;
    int     level;

    level = pxml->m_level;
    do
    {
        ptok = QXMLnextElement(pxml);
        if (! ptok) return NULL;
        if (pxml->m_level == level)
            return ptok;
    }
    while (ptok);
    return NULL;
}

//***********************************************************************
char* QXMLchildElement(PQXML pxml)
{
    char*   ptok;
    int     level;

    level = pxml->m_level;
    do
    {
        ptok = QXMLnextElement(pxml);
        if (! ptok) return NULL;
        if (pxml->m_level > level)
            return ptok;
        if (pxml->m_level <= level)
            return NULL;
    }
    while (ptok);
    return NULL;
}

//***********************************************************************
char* QXMLfirstElement(PQXML pxml)
{
    if (pxml)
    {
        pxml->m_cur     = pxml->m_src;
        pxml->m_state   = psBase;
        pxml->m_level   = 0;
        pxml->m_toklen  = 0;
        pxml->m_line    = 1;
        pxml->m_column  = 1;

        return QXMLnextElement(pxml);
    }
    return NULL;
}

//***********************************************************************
char* QXMLelementName(PQXML pxml, char* pelement)
{
    char *pe, a;
    if (! pxml || ! pelement)
    {
        return NULL;
    }
    if (pxml->m_expandum)
    {
        if (*pelement == '&')
            if (pelement[1] == 'l')
                if (pelement[2] == 't')
                    if (pelement[3] == ';')
                        pelement+= 4;
    }
    if (*pelement == '<')
        pelement++;
    if (*pelement == '/')
        pelement++;

    pe = pelement;
    while (*pe)
    {
        a = *pe++;

        if (a == '&' && pxml->m_expandum)
        {
            char eb[8], xb[8];
            int  l;

            eb[0] = a;
            for (l = 1; a != ';' && l < 7; l++)
            {
                a= *pelement++;
                eb[l] = a;
            }
            eb[l] = '\0';
            if (a != ';')
            {
                a = 0;
            }
            else
            {
                QXMLvalueCopy(pxml, xb, eb, sizeof(xb));
                a = xb[0];
            }
        }
        if (a == '>') a = 0;
        if (a == '/') a = 0;
        if (a == ' ') a = 0;

        if (! a)
        {
            return pelement;
        }
        if (a == ':')
        {
            return pe;
        }
    }
    return pelement;
}

//***********************************************************************
int QXMLelementCompare(PQXML pxml, char* pelement, char* pname)
{
    char a, b;

    if (! pelement || ! pname) return -1;

    if (pxml->m_expandum)
    {
        if (*pelement == '&')
            if (pelement[1] == 'l')
                if (pelement[2] == 't')
                    if (pelement[3] == ';')
                        pelement+= 4;
    }
    if (*pelement == '<')
        pelement++;
    if (*pelement == '/')
        pelement++;

    do
    {
        a = *pelement++;
        b = *pname++;

        if (a == '&' && pxml->m_expandum)
        {
            char eb[8], xb[8];
            int  l;

            eb[0] = a;
            for (l = 1; a != ';' && l < 7; l++)
            {
                a= *pelement++;
                eb[l] = a;
            }
            eb[l] = '\0';
            if (a != ';')
            {
                a = 0;
            }
            else
            {
                QXMLvalueCopy(pxml, xb, eb, sizeof(xb));
                a = xb[0];
            }
        }
        if (a == '>') a = 0;
        if (a == '/') a = 0;
        if (a == ' ') a = 0;

        if (a < b) return -1;
        if (a > b) return 1;
    }
    while (a && b);

    return 0;
}

//***********************************************************************
int QXMLelementCaseCompare(PQXML pxml, char* pelement, char* pname)
{
    char a, b;

    if (! pelement || ! pname) return -1;

    if (pxml->m_expandum)
    {
        if (*pelement == '&')
            if (pelement[1] == 'l')
                if (pelement[2] == 't')
                    if (pelement[3] == ';')
                        pelement+= 4;
    }
    if (*pelement == '<')
        pelement++;
    if (*pelement == '/')
        pelement++;

    do
    {
        a = *pelement++;
        b = *pname++;

        if (a == '&' && pxml->m_expandum)
        {
            char eb[8], xb[8];
            int  l;

            eb[0] = a;
            for (l = 1; a != ';' && l < 7; l++)
            {
                a= *pelement++;
                eb[l] = a;
            }
            eb[l] = '\0';
            if (a != ';')
            {
                a = 0;
            }
            else
            {
                QXMLvalueCopy(pxml, xb, eb, sizeof(xb));
                a = xb[0];
            }
        }
        if (a == '>') a = 0;
        else if (a == '/') a = 0;
        else if (a == ' ') a = 0;
        else if (a >= 'A' && a <= 'Z')
            a = a - 'A' + 'a';

        if (b >= 'A' && b <= 'Z')
            b = b - 'A' + 'a';

        if (a < b) return -1;
        if (a > b) return 1;
    }
    while (a && b);

    return 0;
}

//***********************************************************************
int QXMLvalueCompare(PQXML pxml, char* pvalue, char* pname)
{
    char a, b;

    if (! pvalue || ! pname) return -1;

    do
    {
        a = *pvalue++;
        b = *pname++;

        if (a == '&' && pxml->m_expandum)
        {
            char eb[8], xb[8];
            int  l;

            eb[0] = a;
            for (l = 1; a != ';' && l < 7; l++)
            {
                a= *pvalue++;
                eb[l] = a;
            }
            eb[l] = '\0';
            if (a != ';')
            {
                a = 0;
            }
            else
            {
                QXMLvalueCopy(pxml, xb, eb, sizeof(xb));
                a = xb[0];
            }
        }
        if (a == '>') a = 0;
        if (a == '<') a = 0;

        if (a < b) return -1;
        if (a > b) return 1;
    }
    while (a && b);

    return 0;
}

//***********************************************************************
int QXMLattributeCompare(PQXML pxml, char* pvalue, char* pname)
{
    char a, b;

    if (! pvalue || ! pname) return -1;

    do
    {
        a = *pvalue++;
        b = *pname++;

        if (a == '=') a = 0;

        if (a == '&' && pxml->m_expandum)
        {
            char eb[8], xb[8];
            int  l;

            eb[0] = a;
            for (l = 1; a != ';' && l < 7; l++)
            {
                a= *pvalue++;
                eb[l] = a;
            }
            eb[l] = '\0';
            if (a != ';')
            {
                a = 0;
            }
            else
            {
                QXMLvalueCopy(pxml, xb, eb, sizeof(xb));
                a = xb[0];
            }
        }
        if (a < b) return -1;
        if (a > b) return 1;
    }
    while (a && b);

    return 0;
}

//***********************************************************************
int QXMLvalueCopy(PQXML pxml, char* pdst, char* pval, size_t ndst)
{
    char        eb[8];
    char*       pdstb;
    int         i, j, s, x;
    char        nc;

    if (! pdst || ! ndst || ! pval) return 0;

    i = 0, s = x = 0, pdstb = pdst;
    while (i < ndst)
    {
        nc = *pval++;
        if (! nc) break;

        if (s > 0)
        {
            if (nc == ';')
            {
                eb[s-1] = '\0';

                for (j = 0; j < (sizeof(g_entitytab)/sizeof(struct tag_entities)); j++)
                {
                    if (! strcmp(eb, g_entitytab[j].ename))
                    {
                        // [TODO - utf-8 encode eval]
                        nc = (char)g_entitytab[j].eval;
                        if (pxml && !pxml->m_expandum)
                        {
                            // incase it is a '<' and don't want any interpretation
                            *pdst++ = nc;
                            nc = 0;
                        }
                        break;
                    }
                }
                s = 0;
            }
            else if (s < sizeof(eb)-1)
            {
                eb[s-1] = nc;
                s++;
            }
            else
            {
#if 0
                NET_WARN("XML - parse xml entity overflow");
#else
                *pdst++ = '&';
                i++;
                for (j = 0; i < ndst && j < s-1; j++, i++)
                {
                    *pdst++ = eb[j];
                }
                *pdst++ = nc;
                pdst--;
                i++;
#endif
                s = 0;
            }
        }
        else if (x > 0)
        {
            eb[x-1] = nc;
            if (x == 2)
            {
                eb[2] = '\0';
                nc = (char)strtoul(eb, NULL, 16);
                i++;
                x = 0;
            }
            else
            {
                x++;
            }
        }
        if (! x && ! s)
        {
            if (nc == '&')
            {
                s = 1;
            }
            else if (nc == '<' || nc == '\r' || nc == '\n')
            {
                break;
            }
            else if (nc == '%')
            {
                x = 1;
            }
            else if (nc)
            {
                *pdst++ = nc;
            }
        }
        i++;
    }
    *pdst = '\0';
    return pdst - pdstb;
}

//***********************************************************************
int QXMLvalueEscape(char* pdst, char* pval, int ndst)
{
    char* estr;
    int   i, j;
    char  nc;

    if (! pdst || ! ndst || ! pval) return 0;

    for (i = 0; i < (ndst - 1); )
    {
        nc = *pval++;
        if (! nc) break;

        switch (nc)
        {
        case '<':
            estr = "lt";
            break;
        case '>':
            estr = "gt";
            break;
        case '&':
            estr = "amp";
            break;
        case '\"':
            estr = "quot";
            break;
        case '\'':
            estr = "apos";
            break;
        default:
            pdst[i] = nc;
            estr = NULL;
            break;
        }
        if (estr)
        {
            j = snprintf(
                            pdst + i,
                            ndst - i,
                            "&%s;",
                            estr
                        );
            i += j;
        }
        else
        {
            i++;
        }
    }
    pdst[i] = '\0';
    return i;
}

//***********************************************************************
int QXMLattributeCopy(PQXML pxml, char* pdst, char* pval, size_t ndst)
{
    char        eb[8];
    char*       pdstb;
    int         i, j, s, x;
    char        nc;

    if (! pdst || ! ndst || ! pval) return 0;

    while (*pval == ' ' || *pval == '\t')
        pval++;

    i = 0, s = x = 0, pdstb = pdst;
    while (i < ndst)
    {
        nc = *pval++;
        if (! nc)
        {
            // end of input
            //
            if (s > 0)
            {
                // in an entity, dump it cause it isn't one
                //
                *pdst++ = '&';
                i++;
                for (j = 0; i < ndst && j < s-1; j++, i++)
                {
                    *pdst++ = eb[j];
                }
                s = 0;
            }
            break;
        }
        if (s > 0)
        {
            int islegalent = (nc == ';' || (nc >= 'a' && nc <= 'z') || (nc >= 'A' && nc <= 'Z'));

            if (! islegalent)
            {
                *pdst++ = '&';
                i++;
                for (j = 0; i < ndst && j < s-1; j++, i++)
                {
                    *pdst++ = eb[j];
                }
                s = 0;
            }
            else if (nc == ';')
            {
                eb[s-1] = '\0';

                for (j = 0; j < (sizeof(g_entitytab)/sizeof(struct tag_entities)); j++)
                {
                    if (! strcmp(eb, g_entitytab[j].ename))
                    {
                        // [TODO - utf-8 encode eval?]
                        nc = (char)g_entitytab[j].eval;
                        if (pxml && ! pxml->m_expandum)
                        {
                            // incase it is a '<' and don't want any interpretation
                            *pdst++ = nc;
                            nc = 0;
                        }
                        break;
                    }
                }
                if (j >= (sizeof(g_entitytab)/sizeof(struct tag_entities)))
                {
                    // getting here means entity not found, so dump eb into output
                    //
                    *pdst++ = '&';
                    i++;
                    for (j = 0; i < ndst && j < s-1; j++, i++)
                    {
                        *pdst++ = eb[j];
                    }
                }
                s = 0;
            }
            else if (s < sizeof(eb)-1)
            {
                eb[s-1] = nc;
                s++;
            }
            else
            {
#if 0
                NET_WARN("XML - parse xml entity overflow");
#else
                *pdst++ = '&';
                i++;
                for (j = 0; i < ndst && j < s-1; j++, i++)
                {
                    *pdst++ = eb[j];
                }
                *pdst++ = nc;
                i++;
#endif
                s = 0;
            }
        }
        else if (x > 0)
        {
            eb[x-1] = nc;
            if (x == 2)
            {
                eb[2] = '\0';
                nc = (char)strtoul(eb, NULL, 16);
                i++;
                x = 0;
            }
            else
            {
                x++;
            }
        }
        if (!s && !x)
        {
            if (nc == '&')
            {
                s = 1;
            }
            else if (nc == '<')
            {
                break;
            }
            else if (nc == '%')
            {
                x = 1;
            }
            else if (nc == '\"')
            {
                if (pdstb != pdst)
                    break;
            }
            else if (nc)
            {
                *pdst++ = nc;
            }
        }
        i++;
    }
    *pdst = '\0';
    return pdst - pdstb;
}

//***********************************************************************
int QXMLelementCopy(PQXML pxml, char* pdst, char* pval, size_t ndst)
{
    char*       pdstb;
    int         i;
    char        nc;

    if (! pdst || ! ndst || ! pval) return 0;

    for (i = 0, pdstb = pdst; i < ndst - 1; i++)
    {
        nc = *pval++;
        if (! nc) break;

        if (pxml->m_expandum)
        {
            if (nc == '&' && pval[1] == 'l' && pval[2] == 't' && pval[3] == ';')
            {
                pval+= 4;
                nc = '<';
            }
        }
        if (! nc || nc == '>' || nc == ' ')
            break;
        *pdst++ = nc;
    }
    *pdst++ = '>';
    *pdst = '\0';
    return pdst - pdstb;
}

//***********************************************************************
PQXML QXMLparserSyncTo(PQXML pxmlBase, PQXML pxmlVar)
{
    if (pxmlVar && pxmlBase)
    {
        pxmlVar->m_src = pxmlBase->m_src;
        pxmlVar->m_cur = pxmlBase->m_cur;

        pxmlVar->m_state   = pxmlBase->m_state;
        pxmlVar->m_level   = pxmlBase->m_level;
        pxmlVar->m_toklen  = pxmlBase->m_toklen;
        pxmlVar->m_line    = pxmlBase->m_line;
        pxmlVar->m_column  = pxmlBase->m_column;

        pxmlVar->m_expandum= pxmlBase->m_expandum;
    }
    return pxmlVar;
}

//***********************************************************************
PQXML QXMLparserClone(PQXML pxmlBase)
{
    PQXML pxml;

    pxml = (PQXML)pi_zalloc(sizeof(QXML_S));
    if (pxml)
    {
        memset(pxml, 0x00, sizeof(QXML_S));
        pxml->m_src = pxmlBase->m_src;
        pxml->m_cur = pxmlBase->m_cur;

        pxml->m_state   = pxmlBase->m_state;
        pxml->m_level   = pxmlBase->m_level;
        pxml->m_toklen  = pxmlBase->m_toklen;
        pxml->m_line    = pxmlBase->m_line;
        pxml->m_column  = pxmlBase->m_column;

        pxml->m_expandum= pxmlBase->m_expandum;
    }
    return pxml;
}

//***********************************************************************
PQXML QXMLparserCreate(char* src, int expandEntities)
{
    PQXML pxml;

    pxml = (PQXML)pi_zalloc(sizeof(QXML_S));
    if (pxml)
    {
        memset(pxml, 0x00, sizeof(QXML_S));
        pxml->m_src = src;
        pxml->m_cur = src;

        pxml->m_state   = psBase;
        pxml->m_level   = 0;
        pxml->m_toklen  = 0;
        pxml->m_line    = 1;
        pxml->m_column  = 1;

        pxml->m_expandum= expandEntities;
    }
    return pxml;
}

//***********************************************************************
void QXMLparserDelete(PQXML pxml)
{
    pi_free(pxml);
}

//int32_t SOAPgetVarValueFromXML(QXML_S* pxml, const char* name, char* val, size_t val_size)
int32_t QXMLsoapGetVal(PQXML pxml, const char* name, char* val, size_t val_size)
{
    char* pe;

    RETURN_VAL_IF(pxml == NULL || name == NULL || val == NULL || val_size == 0, NET_WARN, -1);

    RETURN_VAL_IF(QXMLsoapFind(pxml, name) != 0, NET_WARN, -2);

    pe = QXMLnextValue(pxml);
    if ( pe == NULL )
    {
        NET_WARN("No value for element(%s)", name);
        return -3;
    }

    QXMLvalueCopy(pxml, val, pe, val_size);
    return 0;
}

//int32_t SOAPfindVarInXML(QXML_S* pxml, const char* name)
int32_t QXMLsoapFind(PQXML pxml, const char* name)
{
    const char* pv = name;
    char*       pe = NULL;
    char        varbuf[256];
    int32_t     recurse = 0;
    int32_t     level;
    int32_t     i;

    RETURN_VAL_IF(pxml == NULL || STRING_IS_EMPTY(pv), NET_WARN, -1);

    do
    {
        for ( i = 0; i < (sizeof(varbuf) - 1); ++i )
        {
            if ( *pv == '.' )
            {
                varbuf[i] = '\0';
                recurse = 1;
                pv++;
                break;
            }
            else if( *pv == '\0')
            {
                varbuf[i] = '\0';
                recurse = 0;
                break;
            }
            else
            {
                varbuf[i] = *pv++;
            }
        }
        level = pxml->m_level;
        varbuf[i] = '\0';

        if ( recurse )
        {
            pe = QXMLchildElement(pxml);
        }
        else
        {
            pe = QXMLnextElement(pxml);
        }

        do
        {
            if ( QXMLelementCaseCompare(pxml, QXMLelementName(pxml, pe), varbuf) == 0 )
            {
                NET_DEBUG("found subelement(%s) recurse(%d)", varbuf, recurse);
                RETURN_VAL_IF(recurse == 0, NET_NONE, 0);
                break;
            }
            pe = QXMLnextElement(pxml);
        }
        while ( pe && (recurse == 0 || level < pxml->m_level) );
    }
    while ( recurse && *pv );

    return -3;
}

