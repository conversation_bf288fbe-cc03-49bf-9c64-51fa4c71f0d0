/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file smtpclient.c
 * @addtogroup net
 * @{
 * @addtogroup netmodules
 * <AUTHOR>
 * @date 2023-4-20
 * @brief Simple-Mail-transfer-Protocol client
 */
#include "nettypes.h"
#include "netmodules.h"
#include "netsock.h"
#include "netsts.h"
#include "netmisc.h"

#if CONFIG_SCAN_EMAIL
#define SMTP_SCAN_MLOCK_UN()    { if (s_smtp_ctx && s_smtp_ctx->scan_mtx != INVALIDMTX) pi_mutex_unlock(s_smtp_ctx->scan_mtx); }
#define SMTP_SCAN_MLOCK_EX()    { if (s_smtp_ctx && s_smtp_ctx->scan_mtx != INVALIDMTX) pi_mutex_lock(s_smtp_ctx->scan_mtx);   }
#endif

#define WARN_EMAIL_BODY_FORMAT \
"You may receive this message when printer error has occurred.\r\n"\
"This information may help:\r\n"\
"\r\n"\
"Host Name: %s\r\n"\
"Host Location: %s\r\n"\
"Administrator Name: %s\r\n"\
"Serial Number: %s\r\n"\
"%s"\
"\r\n"\
"System ERROR:  %s.\r\n"\
"\r\n"\
"If you do not want to receive this kind of e-mail, please visit http://%s and disable the E-mail Notification.\r\n"\
"Thanks for using.\r\n"\
"*********************************\r\n"\
"%s %s Printer\r\n"\
"*********************************\r\n"

#define EMAIL_HAS_ATTACHMENT \
"MIME-Version: 1.0\r\n"\
"Content-Type: multipart/mixed; boundary=\"boundary\"\r\n"\
"X-Mailer: Pantum\r\n"\
"\r\n"\
"This is a multi-part message in MIME format.\r\n"\
"\r\n"\
"--boundary\r\n"

#define EMAIL_HEADER_FORMAT \
"Content-Type: text/%s; Charset=UTF-8\r\n"\
"Content-Disposition: inline; \r\n\r\n"

#define EMAIL_ATTACHMENT_FORMAT \
"--boundary\r\n"\
"Content-Type: %s; name=\"%s\"\r\n"\
"Content-ID: <attachement-%u>\r\n"\
"Content-Transfer-Encoding: base64\r\n"\
"Content-Disposition: attachment; filename=\"%s\"\r\n\r\n"


#define SMTP_MAX_SINGLE_FILE        (22*1024*1024) /* 22MB attachment limit */
#define SMTP_LIMIT_READ             (3*512*1024)   /* must to SMTP_LIMIT_READ%3 == 0*/
#define SMTP_LIMIT_ATTACHMENT       (27*1024*1024) /* encode base64 buffer*/
#define SMTP_LIMIT_WRITE            (64*1000)      /* must to SMTP_LIMIT_WRITE%4 == 0*/

#define SMTP_ADDR_LEN               (64)

typedef enum
{
    EML_SEC_NONE,
    EML_SEC_SSL,
    EML_SEC_STARTTLS
}
SMTP_SEC_E;

typedef enum
{
	EML_INVALID = -1,       //无效状态
	EML_SUCCESS = 0,        //成功
	EML_ESYS,				//系统故障（分不出内存等）
	EML_EUSER,				//用户名错误
	EML_ECONN,				//连接失败
	EML_ETRAN,				//传输失败
	EML_EPASS,				//密码错误
	EML_EFROM,              //发件人错误
	EML_ETO,                //收件人错误
	EML_EATT_ACCESS,		//附件不可访问（文件不存在等）
	EML_EATT_TOO_BIG,		//附件过大
	EML_ELIMIT,				//邮件大小超过了服务器限制
	EML_ESERVER,			//服务器响应了其它错误信息
	EML_MEM_LOW             //内存不足
}
EML_STATUS_E;

typedef struct smtp_client
{
    char                m_server_addr[SMTP_ADDR_LEN];               ///< host name of SMTP server
    uint16_t            m_server_port;                              ///< port connected to
    uint8_t             m_server_auth;                              ///< switch on authentication by server.
    uint8_t             m_sec_mode;                                 ///< security mode
    char                m_username[64];                             ///< sending user
    char                m_password[24];                             ///< sending user's password (encrytped)
    char                m_sender_addr[SMTP_ADDR_LEN];               ///< mail from (sender address)
    char                m_rcpt_to[60][SMTP_ADDR_LEN];               ///< mail recipient (email address)
    uint32_t            m_rcpt_num;                                 ///< mail recipient count
    uint32_t            m_is_test;                                  ///< send a self-test email
    PI_SOCKET_T         m_sockfd;                                   ///< socket handle of TCP connection
    QIO_S*              m_qio;                                      ///< qio wrapper for TCP or SSL layer
    int32_t             m_tos;                                      ///< io timeout seconds
}
SMTP_CLI_S;

typedef struct file_info
{
    char                fpath[256];     //文件路径
    char                ftype[128];     //文件类型
    int32_t             fsize;          //文件大小
    int32_t             bsize;          //编码文件大小
}
FILE_INFO_S;

typedef struct smtp_attach
{
    FILE_INFO_S         file;
    struct smtp_attach* next;
}
SMTP_ATTACH_S;

#if CONFIG_SCAN_EMAIL
typedef enum
{
    SCNST_IDLE,
    SCNST_READ,
    SCNST_SEND,
    SCNST_ERR
}
SCAN_STATUS_E;

typedef struct smtp_scan
{
    SMTP_ATTACH_S*  head;        //文件头结点
    SMTP_ATTACH_S*  end;         //文件尾结点
    int32_t         limit;       //服务器大小限制
    int32_t         atchnum;     //文件个数
    int32_t         fbuflen;     //缓冲区使用大小
    char*           fbuffer;     //读文件缓冲
    SMTP_CLI_S*     pcli;
    SCAN_STATUS_E   scanstat;
}
SMTP_SCAN_S;
#endif /* CONFIG_SCAN_EMAIL */

typedef struct smtp_context
{
    NET_CTX_S*      net_ctx;
    PI_THREAD_T     alarm_tid;
    PI_SEMAPHORE_T  alarm_sem;
#if CONFIG_SCAN_EMAIL
    SMTP_SCAN_S     scan;
    PI_THREAD_T     scan_tid;
    PI_MUTEX_T      scan_mtx;
#endif
}
SMTP_CTX_S;

static SMTP_CTX_S*  s_smtp_ctx = NULL;

static void smtp_testing_string(int32_t code)
{
    char msg[128];

    snprintf(msg, sizeof(msg), "{\"testing\":true,\"result\":0x%02x}", code);
    netdata_set_smtp_test_result(DATA_MGR_OF(s_smtp_ctx), msg);
}

static SMTP_CLI_S* smtp_client_create(const void* rcpt_to, uint32_t rcpt_num, uint32_t is_test)
{
    SMTP_CLI_S* pcli = NULL;

    pcli = (SMTP_CLI_S *)pi_zalloc(sizeof(SMTP_CLI_S));
    RETURN_VAL_IF(pcli == NULL, NET_WARN, NULL);

    netdata_get_smtp_sender_addr(DATA_MGR_OF(s_smtp_ctx), pcli->m_sender_addr, sizeof(pcli->m_sender_addr));
    netdata_get_smtp_server_addr(DATA_MGR_OF(s_smtp_ctx), pcli->m_server_addr, sizeof(pcli->m_server_addr));
    pcli->m_server_port = (uint16_t)netdata_get_smtp_server_port(DATA_MGR_OF(s_smtp_ctx));
    pcli->m_server_auth = (uint8_t)netdata_get_smtp_server_auth(DATA_MGR_OF(s_smtp_ctx));
    pcli->m_sec_mode    = (uint8_t)netdata_get_smtp_sec_mode(DATA_MGR_OF(s_smtp_ctx));
    netdata_get_smtp_username(DATA_MGR_OF(s_smtp_ctx), pcli->m_username, sizeof(pcli->m_username));
    netdata_get_smtp_password(DATA_MGR_OF(s_smtp_ctx), pcli->m_password, sizeof(pcli->m_password));

    memcpy((void *)pcli->m_rcpt_to, rcpt_to, sizeof(pcli->m_rcpt_to[0]) * rcpt_num);
    pcli->m_rcpt_num    = rcpt_num;
    pcli->m_is_test     = is_test;
    pcli->m_sockfd      = INVALID_SOCKET;
    pcli->m_tos         = 5;

    NET_DEBUG("%s|%u|%u|%u|%s|%s|%s\n", pcli->m_server_addr, pcli->m_server_port, pcli->m_sec_mode, pcli->m_server_auth, pcli->m_sender_addr, pcli->m_username, pcli->m_password);

    return pcli;
}

static void smtp_client_destroy(SMTP_CLI_S* pcli)
{
    if ( pcli != NULL )
    {
        if ( pcli->m_qio != NULL )
        {
            QIO_CLOSE(pcli->m_qio);
            pcli->m_qio = NULL;
        }
        if ( pcli->m_sockfd != INVALID_SOCKET )
        {
            pi_closesock(pcli->m_sockfd);
            pcli->m_sockfd = INVALID_SOCKET;
        }
        pi_free(pcli);
    }
}

static int32_t smtp_client_recv(SMTP_CLI_S* pcli, char* buf, size_t nbuf)
{
    int32_t ret;
    int32_t read_cnt = 0;
    int32_t read_timeout = 1000;//1000毫秒
    time_t time_out = time(NULL) + 30;

    memset(buf, 0, nbuf);
    do
    {
        ret = QIO_READABLE(pcli->m_qio, pcli->m_tos, 0);
        if ( ret > 0 )
        {
            read_cnt = QIO_READ(pcli->m_qio, (void *)buf, nbuf);
            NET_DEBUG("SMTP recv(%s) ret(%d)", buf, ret);
            if ( read_cnt < 0 )
            {
                return 0;
            }
            else if( read_cnt == 0 )
            {
                RETURN_VAL_IF(read_timeout <= 0, NET_WARN, 0);
                pi_msleep(10);
                read_timeout -= 10;
            }
            NET_DEBUG("recv(%s) ret(%d) read_cnt(%d)", buf, ret, read_cnt);
            time_out = time(NULL) + 30;
        }
        else
        {
            if ( ret < 0 )
            {
                return 0;
            }
            else
            {
                RETURN_VAL_IF(time(NULL) > time_out, NET_WARN, 0);
            }
        }
        NET_DEBUG("read_cnt(%d)", read_cnt);
    }
    while( read_cnt <= 0 );

    return read_cnt;
}

static int32_t smtp_client_send(SMTP_CLI_S* pcli, const char* buf, size_t nbuf)
{
    int32_t ret;

    ret = QIO_WRITEABLE(pcli->m_qio, pcli->m_tos, 0);
    RETURN_VAL_IF(ret <= 0, NET_WARN, ret);

    ret = QIO_WRITE(pcli->m_qio, (void *)buf, nbuf);
    NET_DEBUG("SMTP send(%s) ret(%d)", buf, ret);

    return ret;
}

static int32_t smtp_client_send_check(SMTP_CLI_S* pcli, const char* cmd, const char* check_reply)
{
    char buf[256];

    RETURN_VAL_IF(smtp_client_send(pcli, cmd, strlen(cmd)) <= 0, NET_WARN, -1);

    RETURN_VAL_IF(smtp_client_recv(pcli, buf, sizeof(buf)) <= 0, NET_WARN, -1);

    RETURN_VAL_IF(strncmp(buf, check_reply, strlen(check_reply)) != 0, NET_WARN, -1);

    return 0;
}

static EML_STATUS_E smtp_send_attachment(SMTP_CLI_S* pcli, char* attachments, uint32_t attach_num)
{
    FILE_INFO_S*    pfile;
    char*           filename;
    char*           ptr;
    uint32_t        i;
    int32_t         nwrite;
    int32_t         len;
    char            buf[510];
    char            tmp[2];

    for ( i = 0, ptr = attachments; i < attach_num; ++i )
    {
        pfile = (FILE_INFO_S *)ptr;
        filename = strrchr(pfile->fpath, '/');
        if ( filename == NULL )
        {
            filename = strrchr(pfile->fpath, '\\');
        }

        if ( filename == NULL )
        {
            filename = pfile->fpath;
        }
        else
        {
            filename++;
        }

        snprintf(buf, sizeof(buf), EMAIL_ATTACHMENT_FORMAT, pfile->ftype, filename, i, filename);
        RETURN_VAL_IF(smtp_client_send(pcli, buf, strlen(buf)) <= 0, NET_WARN, EML_ESERVER);
        NET_DEBUG("attachment file: '%s', size: %d, bsize: %d", pfile->fpath, pfile->fsize, pfile->bsize);
        ptr += sizeof(FILE_INFO_S);

        for ( nwrite = 0, len = 0; nwrite < pfile->bsize; nwrite += len )
        {
            memset(tmp, 0, sizeof(tmp));
            if ( pfile->bsize - nwrite > SMTP_LIMIT_WRITE )
            {
                len = SMTP_LIMIT_WRITE;
                memcpy(tmp, ptr + nwrite + len, 2);
                memcpy(ptr + nwrite + len, "\r\n", 2);
                RETURN_VAL_IF(smtp_client_send(pcli, ptr + nwrite, len + 2) <= 0, NET_WARN, EML_ESERVER);
                memcpy(ptr + nwrite + len, tmp, 2);
            }
            else
            {
                len = pfile->bsize - nwrite;
                RETURN_VAL_IF(smtp_client_send(pcli, ptr + nwrite, len) <= 0, NET_WARN, EML_ESERVER);
            }
            NET_DEBUG("nwrite(%d) len(%d) tmp[%02x, %02x]", nwrite, len, tmp[0], tmp[1]);
        }
        ptr += pfile->bsize;
    }
    RETURN_VAL_IF(smtp_client_send(pcli, "--boundary--\r\n", strlen("--boundary--\r\n")) <= 0, NET_WARN, EML_ECONN);

    return EML_SUCCESS;
}

static EML_STATUS_E smtp_start_session(SMTP_CLI_S* pcli)
{
    EML_STATUS_E    res = EML_ECONN;
    char            buf[256];
    int32_t         try_times = 0;

    do
    {
#if CONFIG_NET_WIFI
        /* create socket for TCP connection */
        do
        {
            pcli->m_sockfd = netsock_create_by_url(pcli->m_server_addr, pcli->m_server_port, pcli->m_tos, 0);
            if ( INVALID_SOCKET == pcli->m_sockfd )
            {
                try_times = change_card_priority(s_smtp_ctx->net_ctx, try_times);
            }
        }
        while ( INVALID_SOCKET == pcli->m_sockfd && try_times != 2 );

        if ( try_times == 2 )
        {
            BREAK_IF(pcli->m_sockfd == INVALID_SOCKET, NET_WARN);
        }
#else
        pcli->m_sockfd = netsock_create_by_url(pcli->m_server_addr, pcli->m_server_port, pcli->m_tos, 0);
        BREAK_IF(pcli->m_sockfd == INVALID_SOCKET, NET_WARN);
#endif

        if ( pcli->m_sec_mode == EML_SEC_SSL )
        {
            pcli->m_qio = qio_ssl_create(pcli->m_sockfd, NETQIO_SSL_CLIENT, NETQIO_SOCK_NOCLOSE);
            BREAK_IF(pcli->m_qio == NULL, NET_WARN);
        }
        else
        {
            pcli->m_qio = qio_tcp_create(pcli->m_sockfd, NETQIO_SOCK_NOCLOSE);
            BREAK_IF(pcli->m_qio == NULL, NET_WARN);
        }

        /* welcome from server responsed */
        BREAK_IF(smtp_client_recv(pcli, buf, sizeof(buf)) <= 0 || strncmp(buf, "220", 3) != 0, NET_WARN);

        /* send hello */
        BREAK_IF(smtp_client_send_check(pcli, "EHLO Pantum\r\n", "250") < 0, NET_WARN);

        /* ask whether the server supports SSL */
        if ( pcli->m_sec_mode == EML_SEC_STARTTLS )
        {
            BREAK_IF(smtp_client_send_check(pcli, "STARTTLS\r\n", "220") < 0, NET_WARN);

            QIO_CLOSE(pcli->m_qio);
            pcli->m_qio = qio_ssl_create(pcli->m_sockfd, NETQIO_SSL_CLIENT, NETQIO_SOCK_NOCLOSE);
            BREAK_IF(pcli->m_qio == NULL, NET_WARN);
        }

        res = EML_SUCCESS;
    }
    while ( 0 );

    if ( pcli->m_is_test )
    {
        smtp_testing_string((res == EML_SUCCESS) ? 0x11 : 0x10);
    }
    return res;
}

static EML_STATUS_E smtp_auth_login(SMTP_CLI_S* pcli)
{
    EML_STATUS_E    res = EML_SUCCESS;
    char            base64str[256];
    char            buf[256];

    do
    {
        if ( pcli->m_sec_mode == EML_SEC_STARTTLS )
        {
            if( smtp_client_send_check(pcli, "EHLO Pantum\r\n", "250") < 0 )
            {
                res = EML_ECONN;
                break;
            }
        }
        if ( smtp_client_send_check(pcli, "AUTH LOGIN\r\n", "334") < 0 )
        {
            res = EML_ECONN;
            break;
        }

        memset(base64str, 0, sizeof(base64str));
        base64_encode(pcli->m_username, strlen(pcli->m_username), base64str, sizeof(base64str));
        snprintf(buf, sizeof(buf), "%s\r\n", base64str);
        if ( smtp_client_send_check(pcli, buf, "334") < 0 )
        {
            res = EML_EUSER;
            break;
        }

        memset(base64str, 0, sizeof(base64str));
        base64_encode(pcli->m_password, strlen(pcli->m_password), base64str, sizeof(base64str));
        snprintf(buf, sizeof(buf), "%s\r\n", base64str);
        if ( smtp_client_send_check(pcli, buf, "235") < 0 )
        {
            res = EML_EPASS;
            break;
        }
    }
    while ( 0 );

    if ( pcli->m_is_test )
    {
        smtp_testing_string((res == EML_SUCCESS) ? 0x21 : 0x20);
    }
    return res;
}

static EML_STATUS_E smtp_send_envelope(SMTP_CLI_S* pcli)
{
    EML_STATUS_E    res = EML_ETO;
    char            buf[256];
    uint32_t        i;

    do
    {
        snprintf(buf, sizeof(buf), "MAIL From:<%s>\r\n", pcli->m_sender_addr);
        if ( smtp_client_send_check(pcli, buf, "250") < 0 )
        {
            res = EML_EFROM;
            break;
        }

        for ( i = 0; i < pcli->m_rcpt_num; ++i )
        {
            NET_DEBUG("recipient[%u]: '%s'", i, pcli->m_rcpt_to[i]);
            snprintf(buf, sizeof(buf), "RCPT To:<%s>\r\n", pcli->m_rcpt_to[i]);
            if ( smtp_client_send_check(pcli, buf, "250") < 0 )
            {
                NET_DEBUG("RCPT To:<%s> failed", pcli->m_rcpt_to[i]);
                continue;
            }
            res = EML_SUCCESS;
        }
    }
    while ( 0 );

    if ( pcli->m_is_test )
    {
        smtp_testing_string((res == EML_SUCCESS) ? 0x31 : 0x30);
    }
    return res;
}

static EML_STATUS_E smtp_send_body(SMTP_CLI_S* pcli, const char* subject, const char* body, char* attachments, int32_t attach_num)
{
    EML_STATUS_E    res = EML_ECONN;
    char            buf[4096];
    char*           ptr = buf;
    uint32_t        i;

    do
    {
        if ( smtp_client_send_check(pcli, "DATA\r\n", "354") < 0 )
        {
            break;
        }

        ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "From: %s\r\n", pcli->m_sender_addr);
        ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "To: %s", pcli->m_rcpt_to[0]);
        for ( i = 1; i < pcli->m_rcpt_num; ++i )
        {
            ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "; %s", pcli->m_rcpt_to[i]);
        }
        ptr += snprintf(ptr, sizeof(buf) + buf - ptr, "\r\nSubject: %s\r\nMIME-Version: 1.0\r\n", subject);

        if ( attach_num > 0 )
        {
            ptr += snprintf(ptr, sizeof(buf) + buf - ptr, EMAIL_HAS_ATTACHMENT);
        }

        ptr += snprintf(ptr, sizeof(buf) + buf - ptr, EMAIL_HEADER_FORMAT, (strstr(body, "<html") != NULL ? "html" : "plain"));
        if ( smtp_client_send(pcli, buf, strlen(buf)) < 0 )
        {
            break;
        }

        if ( smtp_client_send(pcli, body, strlen(body)) < 0 )
        {
            break;
        }

        if ( attach_num > 0 && attachments )
        {
            NET_DEBUG("%d attachment file", attach_num);
            res = smtp_send_attachment(pcli, attachments, attach_num);
            if ( res != EML_SUCCESS )
            {
                break;
            }
        }

        if ( smtp_client_send(pcli, "\r\n.\r\n", 5) < 0 )
        {
            res = EML_ECONN;
            break;
        }

        if ( smtp_client_recv(pcli, buf, sizeof(buf)) < 0 )
        {
            NET_DEBUG("send data end, receive result failed");
            res = EML_ECONN;
            break;
        }

        NET_DEBUG("send data end, return result(%s)", buf);
        if ( strncmp(buf, "250", 3) == 0 )
        {
            res = EML_SUCCESS;
        }
        else if ( strncmp(buf, "552", 3) == 0 )
        {
            res = EML_ELIMIT;
        }
        else
        {
            res = EML_SUCCESS;
        }
    }
    while ( 0 );

    if ( pcli->m_is_test )
    {
        smtp_testing_string((res == EML_SUCCESS) ? 0x41 : 0x40);
    }
    return res;
}

static EML_STATUS_E smtp_end_session(SMTP_CLI_S* pcli)
{
    EML_STATUS_E res = EML_SUCCESS;

    if ( pcli->m_sec_mode == EML_SEC_NONE )
    {
        smtp_client_send_check(pcli, "QUIT\r\n", "221");
    }

    if ( pcli->m_is_test )
    {
        smtp_testing_string((res == EML_SUCCESS) ? 0x51 : 0x50);
    }
    return res;
}

static int32_t smtp_send_email(SMTP_CLI_S* pcli, const char* subject, const char* body, char* attachments, uint32_t attach_num)
{
    EML_STATUS_E res = EML_SUCCESS;

    RETURN_VAL_IF(pcli == NULL, NET_WARN, EML_ELIMIT);

    res = smtp_start_session(pcli);
    RETURN_VAL_IF(res != EML_SUCCESS, NET_WARN, res);

    if ( pcli->m_server_auth )
    {
        res = smtp_auth_login(pcli);
        RETURN_VAL_IF(res != EML_SUCCESS, NET_WARN, res);
    }

    res = smtp_send_envelope(pcli);
    RETURN_VAL_IF(res != EML_SUCCESS, NET_WARN, res);

    res = smtp_send_body(pcli, subject, body, attachments, attach_num);
    RETURN_VAL_IF(res != EML_SUCCESS, NET_WARN, res);

    res = smtp_end_session(pcli);
    RETURN_VAL_IF(res != EML_SUCCESS, NET_WARN, res);

    return EML_SUCCESS;
}

#if CONFIG_SCAN_EMAIL

#define FILE_STATUS_UPDATE(file) {                                                                      \
    NET_DEBUG("MSG_SCAN_FILE_STATUS_UPDATE type(%u) file(%s) status(%d)", message.msg2, file, status);  \
    message.msgType   = MSG_SCAN_FILE_STATUS_UPDATE;                                                    \
    message.msg1      = LOCAL_REQUEST;                                                                  \
    message.msg3      = strdup(file);                                                                   \
    message.msgSender = MID_PORT_EMAIL;                                                                 \
    task_msg_send_by_router(MID_SCAN_OUT, &message);                                                    \
}

static int32_t scan_buffer_overflow(int32_t size)
{
    int32_t need_bufsize = s_smtp_ctx->scan.fbuflen + size + sizeof(FILE_INFO_S);

    NET_DEBUG("need buffer size: %d, limit: %d\n", need_bufsize, s_smtp_ctx->scan.limit);

    return (need_bufsize > s_smtp_ctx->scan.limit);
}

static int32_t smtp_scan_load_attachment(FILE_INFO_S* pfile)
{
    QIO_S*  pqio;
    char*   ptr;
    char*   readbuf;
    int32_t readlen;
    int32_t size;
    int32_t len;
    int32_t n;
    int32_t ret;

    RETURN_VAL_IF(s_smtp_ctx->scan.fbuffer == NULL, NET_DEBUG, EML_SUCCESS);

    pqio = qio_file_create(pfile->fpath, O_RDONLY);
    RETURN_VAL_IF(pqio == NULL, NET_WARN, EML_EATT_ACCESS);

    readbuf = (char *)pi_zalloc(SMTP_LIMIT_READ);
    if ( readbuf == NULL )
    {
        NET_WARN("alloc readbuf failed: %d<%s>", errno, strerror(errno));
        QIO_CLOSE(pqio);
        return EML_MEM_LOW;
    }

    NET_DEBUG("read smtp scan file: %s %d-%d-%d", pfile->fpath, pfile->fsize, pfile->bsize, s_smtp_ctx->scan.fbuflen);
    ptr  = s_smtp_ctx->scan.fbuffer + s_smtp_ctx->scan.fbuflen + sizeof(FILE_INFO_S);
    len  = pfile->fsize;
    size = pfile->bsize;
    pfile->bsize = 0;

    do
    {
        readlen = (len < SMTP_LIMIT_READ ? len : SMTP_LIMIT_READ);
        n = QIO_READ(pqio, readbuf, readlen);
        if ( n != readlen )
        {
            NET_DEBUG("read %d bytes failed: %d<%s>", readlen, errno, strerror(errno));
            break;
        }
        len -= n;
        n = base64_encode(readbuf, readlen, ptr, size);
        size -= n;
        ptr += n;
        pfile->bsize += n;
        NET_DEBUG("Read %d bytes, encode[%d], len[%d], size[%d], blen[%d]", readlen, n, len, size, pfile->bsize);
    }
    while ( len > 0 && size > 0 );

    if ( 0 == len && size > 2 )
    {
        snprintf(ptr, size, "\r\n");
        pfile->bsize += 2;
        s_smtp_ctx->scan.atchnum++;
        memcpy(s_smtp_ctx->scan.fbuffer + s_smtp_ctx->scan.fbuflen, pfile, sizeof(FILE_INFO_S));
        s_smtp_ctx->scan.fbuflen += sizeof(FILE_INFO_S) + pfile->bsize;
        ret = EML_SUCCESS;
    }
    else
    {
        ret = EML_EATT_ACCESS;
    }

    pi_free(readbuf);
    QIO_CLOSE(pqio);

    return ret;
}

static char *GetLimitBufferNum(int32_t *num, int32_t *limit)
{
    int32_t i = 0, size = 0;
    int32_t sendnum = *num;
    FILE_INFO_S* pfile = NULL;
    char *ptr = NULL;
    char *savebuf = NULL;

    savebuf = s_smtp_ctx->scan.fbuffer;
    for (i = 0; i < sendnum; i++)
    {
        pfile = (FILE_INFO_S *)savebuf;
        savebuf += sizeof(FILE_INFO_S);
        savebuf += pfile->bsize;
    }
    ptr = savebuf;
    for (i = 0; i < s_smtp_ctx->scan.atchnum - sendnum; i++)
    {
        pfile = (FILE_INFO_S *)ptr;
        size += pfile->bsize;
        if (size > s_smtp_ctx->scan.limit)
        {
            break;
        }
        ptr += sizeof(FILE_INFO_S);
        ptr += pfile->bsize;
    }
    *num = i;
    if (limit)
    {
        *limit = size;
    }

    return savebuf;
}

static EML_STATUS_E smtp_scan_send(void)
{
    int32_t stat = EML_SUCCESS;
    int32_t num = 0, sendnum = 0;
    int32_t blimit = 1, limit = 0;
    char email_subj[128];
    char email_body[128];
    char pdt_name[32];
    char* buf;

    GetLimitBufferNum(&num, &limit);
    num = s_smtp_ctx->scan.atchnum;
    buf = s_smtp_ctx->scan.fbuffer;

    netdata_get_pdt_name(DATA_MGR_OF(s_smtp_ctx), pdt_name, sizeof(pdt_name));
    snprintf(email_subj, sizeof(email_subj), "[%s] Scan Results", pdt_name);
    snprintf(email_body, sizeof(email_body), "[%s] Scan resulted as attachments\r\n", pdt_name);

    do
    {
        stat = smtp_send_email(s_smtp_ctx->scan.pcli, email_subj, email_body, buf, num);
        if (stat == EML_ELIMIT && num > 1)
        {
            num = sendnum;
            blimit++;
            s_smtp_ctx->scan.limit = limit / blimit;
            buf = GetLimitBufferNum(&num, NULL);
            NET_DEBUG("Change SMTP scan send limit %d %d %d %d\n", s_smtp_ctx->scan.limit, blimit, sendnum, num);
        }
        else if (stat == EML_SUCCESS)
        {
            sendnum += num;
            NET_DEBUG("Send scan file success, number %d-%d\n", sendnum, s_smtp_ctx->scan.atchnum);
            if (sendnum < s_smtp_ctx->scan.atchnum)
            {
                num = sendnum;
                buf = GetLimitBufferNum(&num, NULL);
            }
        }
        else
        {
            NET_DEBUG("ERROR status %d\n", stat);
            break;
        }

        if (num <= 0)
        {
            NET_DEBUG("Single file is over limit\n");
            stat = EML_ELIMIT;
            break;
        }
    }
    while (sendnum < s_smtp_ctx->scan.atchnum);

    return stat;
}

static int32_t smtp_scan_job_start(void* rcpt_to, uint32_t rcpt_num)
{
    EML_STATUS_E    res = EML_SUCCESS;
    SMTP_CLI_S*     pcli;

    RETURN_VAL_IF(rcpt_to == NULL || rcpt_num == 0, NET_WARN, EML_ESYS);

    pcli = smtp_client_create(rcpt_to, rcpt_num, 0);
    RETURN_VAL_IF(pcli == NULL, NET_WARN, EML_MEM_LOW);

    do
    {
        res = smtp_start_session(pcli);
        if ( res == EML_SUCCESS )
        {
            s_smtp_ctx->scan.head     = NULL;
            s_smtp_ctx->scan.end      = NULL;
            s_smtp_ctx->scan.limit    = SMTP_LIMIT_ATTACHMENT;
            s_smtp_ctx->scan.scanstat = SCNST_IDLE;
            s_smtp_ctx->scan.pcli     = pcli;
            s_smtp_ctx->scan.atchnum  = 0;
            s_smtp_ctx->scan.fbuflen  = 0;
            s_smtp_ctx->scan.fbuffer  = (char *)pi_zalloc(SMTP_LIMIT_ATTACHMENT);
            if ( s_smtp_ctx->scan.fbuffer == NULL )
            {
                NET_WARN("alloc read file buffer failed: %d<%s>", errno, strerror(errno));
                res = EML_MEM_LOW;
            }
            SMTP_SCAN_MLOCK_UN();
        }
        else
        {
            NET_WARN("connect to smtp server(%s : %u) failed: %d<%s>", pcli->m_server_addr, pcli->m_server_port, errno, strerror(errno));
            res = EML_ECONN;
            break;
        }

        if ( pcli->m_server_auth )
        {
            res = smtp_auth_login(pcli);
            BREAK_IF( res != EML_SUCCESS, NET_WARN);
        }
    }
    while(0);
    smtp_end_session(pcli);
    if ( res != EML_SUCCESS )
    {
        smtp_client_destroy(pcli);
        s_smtp_ctx->scan.pcli = NULL;
    }
    return res;
}

static void* smtp_scan_thread(void* arg)
{
    ROUTER_MSG_S    message;
    int32_t         status = EML_SUCCESS;
    int32_t         scnjobend = 0;

    while ( 1 )
    {
        message.msgType = MSG_NULL;
        if ( task_msg_wait_forever_by_router(MID_PORT_EMAIL, &message) < 0 )
        {
            NET_WARN("wait MID_PORT_EMAIL failed");
            break;
        }

        switch ( message.msgType )
        {
        case MSG_CTRL_JOB_START:
            {
                NET_DEBUG("MSG_CTRL_JOB_START %d", message.msg2);
                scnjobend = 0;
                status = smtp_scan_job_start(message.msg3, message.msg2);
                break;
            }
        case MSG_DATA_PAGE_END:
            {
                SMTP_ATTACH_S*  node = NULL;
                FILE_INFO_S     file;
                char*           ptr = (char *)message.msg3;
                struct stat     st;

                NET_DEBUG("MSG_DATA_PAGE_END %p %s", ptr, ptr);
                snprintf(file.fpath, sizeof(file.fpath), "%s", ptr);
                pi_free(ptr);

                if ( s_smtp_ctx->scan.scanstat == SCNST_ERR )
                {
                    NET_WARN("SMTP scan has error, ignore");
                    FILE_STATUS_UPDATE(file.fpath);
                    break;
                }

                snprintf(file.ftype, sizeof(file.ftype), "%s", "application/octet-stream");
                if ( stat(file.fpath, &st) < 0 )
                {
                    NET_DEBUG("stat file '%s' failed: %d<%s>", file.fpath, errno, strerror(errno));
                    status = EML_EATT_ACCESS;
                    FILE_STATUS_UPDATE(file.fpath);
                    break;
                }

                NET_DEBUG("attachment file '%s' size [%ld-%d]", file.fpath, st.st_size, SMTP_MAX_SINGLE_FILE);
                if ( st.st_size >= SMTP_MAX_SINGLE_FILE )
                {
                    NET_DEBUG("attachment file too large %ld", st.st_size);
                    status = EML_EATT_TOO_BIG;
                    FILE_STATUS_UPDATE(file.fpath);
                    break;
                }

                file.fsize = (int32_t)st.st_size;
                file.bsize = (file.fsize + 2) / 3 * 4 + 8; //多留8个字节长度

                SMTP_SCAN_MLOCK_EX();
                if ( s_smtp_ctx->scan.scanstat == SCNST_IDLE && !scan_buffer_overflow(file.bsize) )
                {
                    status = smtp_scan_load_attachment(&file);
                    FILE_STATUS_UPDATE(file.fpath);
                }
                else
                {
                    node = (SMTP_ATTACH_S *)pi_zalloc(sizeof(SMTP_ATTACH_S));
                    if ( node )
                    {
                        NET_DEBUG("add file '%s' to list", file.fpath);
                        memcpy(&node->file, &file, sizeof(FILE_INFO_S));
                        node->next = NULL;
                        if ( NULL == s_smtp_ctx->scan.head )
                        {
                            s_smtp_ctx->scan.head = node;
                            s_smtp_ctx->scan.end = node;
                        }
                        else
                        {
                            s_smtp_ctx->scan.end->next = node;
                            s_smtp_ctx->scan.end = node;
                        }

                        if ( s_smtp_ctx->scan.scanstat == SCNST_IDLE )
                        {
                            NET_DEBUG("SMTP Scan buffer need to send !!!");
                            s_smtp_ctx->scan.scanstat = SCNST_SEND;
                            {
                                EML_STATUS_E stat;
                                ROUTER_MSG_S msg;

                                stat = smtp_scan_send();

                                s_smtp_ctx->scan.fbuflen = 0;
                                s_smtp_ctx->scan.atchnum = 0;
                                s_smtp_ctx->scan.scanstat = SCNST_READ;
                                if ( stat != EML_SUCCESS )
                                {
                                    s_smtp_ctx->scan.scanstat = SCNST_ERR;

                                    msg.msgType   = MSG_SCAN_STATUS_UPDATE;
                                    msg.msg1      = LOCAL_REQUEST;
                                    msg.msg2      = stat;
                                    msg.msg3      = NULL;
                                    msg.msgSender = MID_PORT_EMAIL;
                                    task_msg_send_by_router(MID_SCAN_OUT, &msg);
                                }
                            }
                        }
                    }
                    else
                    {
                        NET_DEBUG("Can't malloc SMTP_ATTACH_S\n");
                        status = EML_MEM_LOW;
                        FILE_STATUS_UPDATE(file.fpath);
                    }
                }
                SMTP_SCAN_MLOCK_UN();
                break;
            }
        case MSG_DATA_JOB_END:
            {
                NET_DEBUG("MSG_DATA_JOB_END !!!");
                scnjobend = 1;
                SMTP_SCAN_MLOCK_EX();
                if ( SCNST_IDLE == s_smtp_ctx->scan.scanstat )
                {
                    NET_DEBUG("SMTP Scan job end requset send");
                    s_smtp_ctx->scan.scanstat = SCNST_SEND;
                    {
                        EML_STATUS_E stat;
                        ROUTER_MSG_S msg;

                        stat = smtp_scan_send();
                        s_smtp_ctx->scan.fbuflen = 0;
                        s_smtp_ctx->scan.atchnum = 0;
                        s_smtp_ctx->scan.scanstat = SCNST_READ;
                        if ( stat != EML_SUCCESS )
                        {
                            s_smtp_ctx->scan.scanstat = SCNST_ERR;
                            msg.msgType   = MSG_SCAN_STATUS_UPDATE;
                            msg.msg1      = LOCAL_REQUEST;
                            msg.msg2      = stat;
                            msg.msg3      = NULL;
                            msg.msgSender = MID_PORT_EMAIL;
                            task_msg_send_by_router(MID_SCAN_OUT, &msg);
                        }
                    }
                }
                SMTP_SCAN_MLOCK_UN();
                break;
            }
        case MSG_CTRL_JOB_CANCEL:
            {
                NET_DEBUG("MSG_CTRL_JOB_CANCEL !!!");
                scnjobend = 2;
                break;
            }
        default:
            {
                NET_DEBUG("Unknown msg = %d", message.msgType);
                break;
            }
        }

        if ( EML_SUCCESS == status && 2 != scnjobend && SCNST_READ == s_smtp_ctx->scan.scanstat )
        {
            SMTP_ATTACH_S* node = NULL;

            NET_DEBUG("SMTP Scan check request send, scnjobend %d!!!", scnjobend);
            SMTP_SCAN_MLOCK_EX();
            node = s_smtp_ctx->scan.head;
            if ( NULL == node && 1 == scnjobend )
            {
                NET_DEBUG("SMTP scan job end !!!");
                s_smtp_ctx->scan.end = NULL;
                s_smtp_ctx->scan.scanstat = SCNST_IDLE;
            }
            while ( node && EML_SUCCESS == status )
            {
                if ( scan_buffer_overflow(node->file.bsize) )
                {
                    NET_DEBUG("Ckeck read file is over limit %d", s_smtp_ctx->scan.limit);
                    if ( s_smtp_ctx->scan.atchnum <= 0 )
                    {
                        status = EML_ELIMIT;
                        s_smtp_ctx->scan.scanstat = SCNST_IDLE;
                    }
                    else
                    {
                        s_smtp_ctx->scan.scanstat = SCNST_SEND;
                    }
                    break;
                }
                status = smtp_scan_load_attachment(&node->file);
                if ( EML_SUCCESS == status )
                {
                    FILE_STATUS_UPDATE(node->file.fpath);
                    s_smtp_ctx->scan.head = node->next;
                    pi_free(node);
                    node = s_smtp_ctx->scan.head;
                    if ( NULL == node )
                    {
                        s_smtp_ctx->scan.end = NULL;
                        s_smtp_ctx->scan.scanstat = (1 == scnjobend) ? SCNST_SEND : SCNST_IDLE;
                    }
                }
            }
            SMTP_SCAN_MLOCK_UN();
        }

        if ( status != EML_SUCCESS || 2 == scnjobend || (1 == scnjobend && SCNST_IDLE == s_smtp_ctx->scan.scanstat) )
        {
            ROUTER_MSG_S msg;

            NET_DEBUG("SMTP scan send status %d !!!\n", status);
            msg.msgType   = MSG_SCAN_STATUS_UPDATE;
            msg.msg1      = LOCAL_REQUEST;
            msg.msg2      = status;
            msg.msg3      = NULL;
            msg.msgSender = MID_PORT_EMAIL;
            task_msg_send_by_router(MID_SCAN_OUT, &msg);
            if ( status != EML_SUCCESS )
            {
                s_smtp_ctx->scan.scanstat = SCNST_ERR;
            }
            status = EML_SUCCESS;
        }

        if ( SCNST_ERR == s_smtp_ctx->scan.scanstat || (2 == scnjobend && SCNST_SEND != s_smtp_ctx->scan.scanstat)
                || (1 == scnjobend && SCNST_IDLE == s_smtp_ctx->scan.scanstat) )
        {
            SMTP_ATTACH_S* node = NULL;

            // NetserverUpdateScanning(0); //fht 2022-04-18
            NET_DEBUG("SMTP scan release source, scnjobend %d, scanstat %d!!!\n", scnjobend, s_smtp_ctx->scan.scanstat);
            SMTP_SCAN_MLOCK_EX();
            if ( s_smtp_ctx->scan.fbuffer )
            {
                pi_free(s_smtp_ctx->scan.fbuffer);
                s_smtp_ctx->scan.fbuffer = NULL;
            }
            if ( s_smtp_ctx->scan.pcli )
            {
                smtp_client_destroy(s_smtp_ctx->scan.pcli);
                s_smtp_ctx->scan.pcli = NULL;
            }
            s_smtp_ctx->scan.fbuflen = 0;
            while ( s_smtp_ctx->scan.head )
            {
                node = s_smtp_ctx->scan.head;
                FILE_STATUS_UPDATE(node->file.fpath);
                s_smtp_ctx->scan.head = node->next;
                pi_free(node);
                node = NULL;
            }
            s_smtp_ctx->scan.end = NULL;
            s_smtp_ctx->scan.scanstat = (scnjobend > 0) ? SCNST_IDLE : s_smtp_ctx->scan.scanstat;
            SMTP_SCAN_MLOCK_UN();
            scnjobend = 0;
        }
    }
    NET_DEBUG("Exit SMTP Scan Thread !!!\n");

    return NULL;
}
#endif /* CONFIG_SCAN_EMAIL */

static void smtp_send_alarm_email(const char* info)
{
    SMTP_CLI_S* pcli = NULL;
    uint32_t    rcpt_num = 0;
    char        rcpt_to[4][SMTP_ADDR_LEN];
    char        email_body[1024];
    char        email_subj[256];
    char        hostname[HOSTNAME_LEN];
    char        ip_addr[IPV4_ADDR_LEN];
    char        addr_info[256];
    char        location[256];
    char        contacts[128];
    char        mfg_name[16];
    char        pdt_name[32];
    char        pdt_sn[32];
    char*       ptr;

    RETURN_IF(info == NULL, NET_WARN);

    netdata_get_alarm_client_addr1(DATA_MGR_OF(s_smtp_ctx), rcpt_to[rcpt_num], sizeof(rcpt_to[rcpt_num]));
    if ( STRING_NO_EMPTY(rcpt_to[rcpt_num]) )
    {
        rcpt_num++;
    }
    netdata_get_alarm_client_addr2(DATA_MGR_OF(s_smtp_ctx), rcpt_to[rcpt_num], sizeof(rcpt_to[rcpt_num]));
    if ( STRING_NO_EMPTY(rcpt_to[rcpt_num]) )
    {
        rcpt_num++;
    }
    netdata_get_alarm_client_addr3(DATA_MGR_OF(s_smtp_ctx), rcpt_to[rcpt_num], sizeof(rcpt_to[rcpt_num]));
    if ( STRING_NO_EMPTY(rcpt_to[rcpt_num]) )
    {
        rcpt_num++;
    }
    netdata_get_alarm_client_addr4(DATA_MGR_OF(s_smtp_ctx), rcpt_to[rcpt_num], sizeof(rcpt_to[rcpt_num]));
    if ( STRING_NO_EMPTY(rcpt_to[rcpt_num]) )
    {
        rcpt_num++;
    }
    RETURN_IF(rcpt_num <= 0, NET_INFO);

    netdata_get_hostname(DATA_MGR_OF(s_smtp_ctx), hostname, sizeof(hostname));
    netdata_get_location(DATA_MGR_OF(s_smtp_ctx), location, sizeof(location));
    netdata_get_contacts(DATA_MGR_OF(s_smtp_ctx), contacts, sizeof(contacts));
    netdata_get_mfg_name(DATA_MGR_OF(s_smtp_ctx), mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(DATA_MGR_OF(s_smtp_ctx), pdt_name, sizeof(pdt_name));
    netdata_get_pdt_sn(DATA_MGR_OF(s_smtp_ctx), pdt_sn, sizeof(pdt_sn));

    ptr = addr_info;
    if ( net_ifctl_is_running(IFACE_ETH) )
    {
        netdata_get_ipv4_addr(DATA_MGR_OF(s_smtp_ctx), IFACE_ID_ETH, ip_addr, sizeof(ip_addr));
        ptr += snprintf(ptr, sizeof(addr_info) + addr_info - ptr, "Wired IP Address: %s\r\n", ip_addr);
    }
#if CONFIG_NET_WIFI
    if ( net_ifctl_is_running(IFACE_STA) )
    {
        netdata_get_ipv4_addr(DATA_MGR_OF(s_smtp_ctx), IFACE_ID_STA, ip_addr, sizeof(ip_addr));
        ptr += snprintf(ptr, sizeof(addr_info) + addr_info - ptr, "Wireless IP Address: %s\r\n", ip_addr);
    }
    if ( net_ifctl_is_running(IFACE_WFD) )
    {
        char ssid_prefix[32];
        char ssid_suffix[32];
        netdata_get_wfd_ssid_prefix(DATA_MGR_OF(s_smtp_ctx), ssid_prefix, sizeof(ssid_prefix));
        netdata_get_wfd_ssid_suffix(DATA_MGR_OF(s_smtp_ctx), ssid_suffix, sizeof(ssid_suffix));
        ptr += snprintf(ptr, sizeof(addr_info) + addr_info - ptr, "Service Set Identifier: %s%s\r\n", ssid_prefix, ssid_suffix);
    }
#endif

    snprintf(email_body, sizeof(email_body), WARN_EMAIL_BODY_FORMAT, hostname, location, contacts, pdt_sn, addr_info, info, hostname, mfg_name, pdt_name);
    snprintf(email_subj, sizeof(email_subj), "[%s] %s", pdt_name, info);
    NET_DEBUG("send email(%s)", email_subj);

    pcli = smtp_client_create((void *)rcpt_to, rcpt_num, 0);
    RETURN_IF(pcli == NULL, NET_WARN);

    smtp_send_email(pcli, email_subj, email_body, NULL, 0);
    smtp_client_destroy(pcli);
}

static void* smtp_alarm_thread(void* arg)
{
    NETSTS_PACKET_S packet;
    uint32_t        toner_status[MARKER_NUM];
    uint8_t         pe_reset = 1;
    uint8_t         pj_reset = 1;
    uint8_t         te_reset = 1;
    uint8_t         tl_reset = 1;
    uint8_t         wt_reset = 1;
    int32_t         i;

    while ( 1 )
    {
        pi_sem_wait(s_smtp_ctx->alarm_sem);

        if ( netsts_take_packet(&packet) == 0 )
        {
            if ( netsts_check_paper_empty(&packet) )
            {
                if ( pe_reset && netdata_get_alarm_paper_empty(DATA_MGR_OF(s_smtp_ctx)) )
                {
                    smtp_send_alarm_email("Paper Empty");
                    pe_reset = 0;
                }
            }
            else
            {
                pe_reset = 1;
            }

            if ( netsts_check_paper_jam(&packet) )
            {
                if ( pj_reset && netdata_get_alarm_paper_jam(DATA_MGR_OF(s_smtp_ctx)) )
                {
                    smtp_send_alarm_email("Paper Jam");
                    pj_reset = 0;
                }
            }
            else
            {
                pj_reset = 1;
            }
        }

        toner_status[MARKER_ID_K] = netdata_get_tb_status(DATA_MGR_OF(s_smtp_ctx), MARKER_ID_K);
        toner_status[MARKER_ID_Y] = netdata_get_tb_status(DATA_MGR_OF(s_smtp_ctx), MARKER_ID_Y);
        toner_status[MARKER_ID_M] = netdata_get_tb_status(DATA_MGR_OF(s_smtp_ctx), MARKER_ID_M);
        toner_status[MARKER_ID_C] = netdata_get_tb_status(DATA_MGR_OF(s_smtp_ctx), MARKER_ID_C);
        NET_DEBUG("toner status C(%u) M(%u) Y(%u) K(%u)", toner_status[0], toner_status[1], toner_status[2], toner_status[3]);
        /* 检查粉盒寿命尽状态(这里需涵盖未安装和不匹配等异常) */
        for ( i = MARKER_ID_K; i < MARKER_NUM; ++i )
        {
            if ( toner_status[i] != V_ID_TONER_NORMAL && toner_status[i] != V_ID_TONER_LOW )
            {
                break;
            }
        }

        if ( i < MARKER_NUM )
        {
            if ( te_reset && netdata_get_alarm_toner_empty(DATA_MGR_OF(s_smtp_ctx)) )
            {
                smtp_send_alarm_email("Cartridge End");
                te_reset = 0;
            }
        }
        else
        {
            te_reset = 1;
        }

        /* 检查粉盒粉量低状态 */
        for ( i = MARKER_ID_K; i < MARKER_NUM; ++i )
        {
            if ( toner_status[i] == V_ID_TONER_LOW )
            {
                break;
            }
        }

        if ( i < MARKER_NUM )
        {
            if ( tl_reset && netdata_get_alarm_toner_low(DATA_MGR_OF(s_smtp_ctx)) )
            {
                smtp_send_alarm_email("Toner Lower");
                tl_reset = 0;
            }
        }
        else
        {
            tl_reset = 1;
        }

        /* 检查废粉瓶异常状态 */
        if ( netdata_get_wtb_status(DATA_MGR_OF(s_smtp_ctx)) )
        {
            if ( wt_reset && netdata_get_alarm_waste_toner(DATA_MGR_OF(s_smtp_ctx)) )
            {
                smtp_send_alarm_email("WasteToner (near) Full");
                wt_reset = 0;
            }
        }
        else
        {
            wt_reset = 1;
        }
    }

    return NULL;
}

static void smtp_send_test_email(void* arg)
{
    SMTP_CLI_S* pcli = NULL;
    uint32_t    rcpt_num = 1;
    char        rcpt_to[1][SMTP_ADDR_LEN];
    char        email_body[1024];
    char        email_subj[256];

    /* smtp自测试邮件，所以收件人即当前发件人地址 */
    netdata_get_smtp_sender_addr(DATA_MGR_OF(s_smtp_ctx), rcpt_to[0], sizeof(rcpt_to[0]));
    snprintf(email_body, sizeof(email_body), "just the test email!");
    snprintf(email_subj, sizeof(email_subj), "test email");
    netdata_set_smtp_test_result(DATA_MGR_OF(s_smtp_ctx), "0x00");

    pcli = smtp_client_create((void *)rcpt_to, rcpt_num, 1);
    RETURN_IF(pcli == NULL, NET_WARN);

    smtp_send_email(pcli, email_subj, email_body, NULL, 0);
    smtp_client_destroy(pcli);
}

static void smtp_update_netport_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_smtp_ctx == NULL, NET_WARN);

    if ( s->subject_status & PORT_UPDATE_SMTP )
    {
        threads_pool_add_task(THREADS_POOL_OF(s_smtp_ctx), smtp_send_test_email, NULL);
    }
}

static void smtp_update_sysstat_callback(EVENT_OBSERVER_S* o, EVENT_SUBJECT_S* s)
{
    RETURN_IF(o == NULL || s == NULL, NET_WARN);
    RETURN_IF(s_smtp_ctx == NULL, NET_WARN);

    if ( netdata_get_alarm_status_flags(DATA_MGR_OF(s_smtp_ctx)) )
    {
        pi_sem_post(s_smtp_ctx->alarm_sem);
    }
}

int32_t smtp_prolog(NET_CTX_S* net_ctx)
{
    int32_t ret = -1;

    RETURN_VAL_IF(s_smtp_ctx != NULL, NET_DEBUG, 0);
    RETURN_VAL_IF(net_ctx == NULL, NET_WARN, -1);

    s_smtp_ctx = (SMTP_CTX_S *)pi_zalloc(sizeof(SMTP_CTX_S));
    RETURN_VAL_IF(s_smtp_ctx == NULL, NET_WARN, -1);

    do
    {
        s_smtp_ctx->net_ctx = net_ctx;

#if CONFIG_SCAN_EMAIL
        BREAK_IF((s_smtp_ctx->scan_mtx = pi_mutex_create()) == INVALIDMTX, NET_WARN);

        BREAK_IF(msg_router_register(MID_PORT_EMAIL) < 0, NET_WARN);

        s_smtp_ctx->scan_tid = pi_thread_create(smtp_scan_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "smtp_scan_thread");
        BREAK_IF(s_smtp_ctx->scan_tid == INVALIDTHREAD, NET_WARN);
#endif
        BREAK_IF((s_smtp_ctx->alarm_sem = pi_sem_create(1)) == INVALIDSEM, NET_WARN);

        s_smtp_ctx->alarm_tid = pi_thread_create(smtp_alarm_thread, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, NULL, "smtp_alarm_thread");
        BREAK_IF(s_smtp_ctx->alarm_tid == INVALIDTHREAD, NET_WARN);

        ret = 0;
    }
    while ( 0 );

    NET_INFO("SMTP client initialize result(%d)", ret);
    if ( ret == 0 )
    {
        netctx_add_netport_observer(net_ctx, smtp_update_netport_callback, NULL);
        netctx_add_sysstat_observer(net_ctx, smtp_update_sysstat_callback, NULL);
    }
    else
    {
        smtp_epilog();
    }
    return ret;
}

void smtp_epilog(void)
{
    if ( s_smtp_ctx != NULL )
    {
        if ( s_smtp_ctx->alarm_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_smtp_ctx->alarm_tid);
        }
        if ( s_smtp_ctx->alarm_sem != INVALIDSEM )
        {
            pi_sem_destroy(s_smtp_ctx->alarm_sem);
        }
#if CONFIG_SCAN_EMAIL
        if ( s_smtp_ctx->scan_tid != INVALIDTHREAD )
        {
            pi_thread_destroy(s_smtp_ctx->scan_tid);
        }
        if ( s_smtp_ctx->scan_mtx != INVALIDMTX )
        {
            pi_mutex_destroy(s_smtp_ctx->scan_mtx);
        }
        msg_router_unregister(MID_PORT_EMAIL);
#endif
        pi_free(s_smtp_ctx);
        s_smtp_ctx = NULL;
    }
}
/**
 *@}
 */
