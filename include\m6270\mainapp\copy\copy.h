/**
* @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reverved
* @file copy.h
* @addtogroup copy system
*
* @{
* @addtogroup copy
* <AUTHOR>
* @date  2023-07-03
* @version  V1.1
* @brief  copy public define
**/

#ifndef __COPY_H
#define __COPY_H

void copy_func();

/**
 * @brief definition of the angle array member\n
 *
 */
typedef enum
{
    ANGLE_COVER_OUT_SIDE        = 0,
    ANGLE_COVER_IN_SIDE         = 1,
    AN<PERSON><PERSON>_BACK_COVER_OUT_SIDE   = 2,
    ANG<PERSON>_BACK_COVER_IN_SIDE    = 3,
    ANGLE_START_OF_MAIN_TEXT    = 4,
    ANGLE_COVER_MAX           = 0XFFFFFFFF
}ANGLE_ARRAY_MEMBER_E;


/**
 * @brief configuration switch \n
 *
 */

#define ATR_GENERAL_SWITCH                        "switch"


/**
 * @brief configuration copies\n
 *
 */
#define CONFIG_COPIES                             "copies"
#define ATR_COPIES                                "copies"

/**
 * @brief configuration copy type\n
 *
 */
#define CONFIG_COPY_TYPE                          "copy_type"
#define ATR_COPY_TYPE                             "copy_type"

/**
 * @brief configuration scan color\n
 *
 */
#define CONFIG_SCAN_COLOR                         "scan_color"
#define ATR_SCAN_COLOR                            "scan_color"

/**
 * @brief configuration print size\n
 *
 */
#define CONFIG_PRINT_SIZE                         "print_size"
#define ATR_PRINT_SIZE                            "print_size"
#define ATR_PRINT_WIDTH                           "print_width"
#define ATR_PRINT_HEIGHT                          "print_height"


/**
 * @brief configuration print mode\n
 *
 */
#define CONFIG_PRINT_MODE                         "print_mode"
#define ATR_PRINT_MODE                            "print_mode"

/**
 * @brief configuration print tray in\n
 *
 */
#define CONFIG_TRAY_IN                            "tray_in"
#define ATR_TRAY_IN                               "tray_in"

/**
 * @brief configuration tray receive\n
 *
 */
#define CONFIG_TRAY_RECEIVE                       "tray_receive"
#define ATR_TRAY_RECEIVE                          "tray_receive"

/**
 * @brief configuration collate\n
 *
 */
#define CONFIG_COLLATE                            "collate"
#define ATR_COLLATE                               "collate"

/**
 * @brief configuration paper type\n
 *
 */
#define CONFIG_PAPER_TYPE                         "paper_type"
#define ATR_PAPER_TYPE                            "paper_type"

/**
 * @brief configuration quality\n
 *
 */
#define CONFIG_QUALITY                            "quality"
#define ATR_QUALITY                               "quality"

/**
 * @brief configuration staple mode\n
 *
 */
#define CONFIG_STAPLE_MODE                        "staple_mode"
#define ATR_STAPLE_MODE                           "staple_mode"
#define ATR_STAPLE_NUM                            "staple_num"
#define ATR_STAPLE_ANGLE                          "staple_angle"

/**
 * @brief configuration punch mode\n
 *
 */
#define CONFIG_PUNCH_MODE                         "punch_mode"
#define ATR_PUNCH_MODE                            "punch_mode"
#define ATR_PUNCH_NUM                             "punch_num"

/**
 * @brief configuration fold mode \n
 *
 */
#define CONFIG_FOLD_MODE                          "fold_mode"
#define ATR_FOLD_MODE                             "fold_mode"

/**
 * @brief configuration shift mode \n
 *
 */
#define CONFIG_SHIFT_MODE                         "shift_mode"
#define ATR_SHIFT_MODE                            "shift_mode"

/**
 * @brief configuration fold number \n
 *
 */
#define CONFIG_FOLD_NUMBER                         "fold_number"
#define ATR_FOLD_NUMBER                            "fold_number"


/**
 * @brief configuration colour inversion \n
 *
 */
#define CONFIG_COLOR_INVERSION                   "color_inversion"
#define ATR_COLOR_INVERSION                      "color_inversion"

/**
 * @brief configuration jpeg encode \n
 *
 */
#define CONFIG_JPEG_ENCODE                        "jpeg_encode"

/**
 * @brief configuration jbig encode \n
 *
 */
#define CONFIG_JBIG_ENCODE                        "jbig_encode"

/**
 * @brief configuration image orientation \n
 *
 */
#define CONFIG_IMAGE_ORIENTATION                  "image_orientation"
#define ATR_IMAGE_ORIENTATION                     "image_orientation"

/**
 * @brief configuration mirror \n
 *
 */
#define CONFIG_MIRROR                              "mirror"

/**
 * @brief configuration original flip mode \n
 *
 */
#define CONFIG_ORIGINAL_FLIP                      "original_flip"
#define ATR_ORIGINAL_FLIP_MODE                    "original_flip_mode"


/**
 * @brief configuration copies flip mode \n
 *
 */
#define CONFIG_COPIES_FLIP                         "copies_flip"
#define ATR_COPIES_FLIP_MODE                       "copies_flip_mode"

/**
 * @brief configuration scan combination \n
 *
 */
#define CONFIG_SCAN_COMBINATION                    "scan_combination"
#define ATR_COMBINATION                            "combination"

/**
 * @brief configuration bill \n
 *
 */
#define CONFIG_BILL                                "bill"

/**
 * @brief configuration auto colour check \n
 *
 */
#define CONFIG_AUTO_COLOUR_CHECK                   "auto_colour_check"

/**
 * @brief configuration auto id correct \n
 *
 */
#define CONFIG_AUTO_ID_CORRECT                     "auto_id_correct"

/**
 * @brief configuration backgroundmove \n
 *
 */
#define CONFIG_BACK_GROUND_MOVE                    "backgroundmove"
#define ATR_BACK_GROUND_MOVE_LEVEL                 "backgroundmove_level"


/**
 * @brief configuration booklet \n
 *
 */
#define CONFIG_BOOKLET                             "booklet"

/**
 * @brief configuration bpp \n
 *
 */
#define CONFIG_BPP                                 "bpp"
#define ATR_SCAN_BPP                               "scan_bpp"
#define ATR_PRINT_BPP                              "print_bpp"

/**
 * @brief configuration clone \n
 *
 */
#define CONFIG_CLONE                               "clone"
#define ATR_CLONE_TYPE                             "clone_type"
#define ATR_CLONE_GAP                              "clone_gap"

/**
 * @brief configuration color adjust \n
 *
 */
#define CONFIG_COLOR_ADJUST                        "color_adjust"
#define ATR_ADJUST_SHARPNESS                       "sharpness"
#define ATR_ADJUST_BRIGHTNESS                      "brightness"
#define ATR_ADJUST_CONTRAST                        "contrast"
#define ATR_ADJUST_HUE                             "hue"
#define ATR_ADJUST_BG                              "backgroundremove"
#define ATR_ADJUST_SATURATION                      "saturation"
#define ATR_ADJUST_BALANCE_C                       "colorbalance_c"
#define ATR_ADJUST_BALANCE_M                       "colorbalance_m"
#define ATR_ADJUST_BALANCE_Y                       "colorbalance_y"
#define ATR_ADJUST_BALANCE_K                       "colorbalance_k"


/**
 * @brief configuration copy scan meantime \n
 *
 */
#define CONFIG_MEANTIME                            "copy_scan_meantime"
#define ATR_FILE_TYPE                              "scan_file_type"
#define ATR_SEND_ROUTER                            "scan_send_router"

/**
 * @brief configuration cover \n
 *
 */
#define CONFIG_COVER                               "cover"
#define ATR_FRONT_COVER                            "front_cover"
#define ATR_BACK_COVER                             "back_cover"
#define ATR_COVER_TRAY_IN                          "cover_tray_in"
#define ATR_COVER_PAPER_TYPE                       "cover_paper_type"
#define ATR_COVER_PAPER_SIZE                       "cover_paper_size"
#define ATR_BACK_COVER_TRAY_IN                     "back_cover_tray_in"
#define ATR_BACK_COVER_PAPER_TYPE                  "back_cover_paper_type"
#define ATR_BACK_COVER_PAPER_SIZE                  "back_cover_paper_size"


/**
 * @brief configuration edge clean \n
 *
 */
#define CONFIG_EDGE_CLEAN                          "edge_clean"
#define ATR_6220_CLEAN_SWITCH                      "6220_clean_switch"
#define ATR_CLEAN_B                                "edge_clean_B"
#define ATR_CLEAN_T                                "edge_clean_T"
#define ATR_CLEAN_L                                "edge_clean_L"
#define ATR_CLEAN_R                                "edge_clean_R"
#define ATR_FRONT_CLEAN_INFO                       "front_clean_info"
#define ATR_BACK_CLEAN_INFO                        "back_clean_info"

/**
 * @brief configuration idcard \n
 *
 */
#define CONFIG_ID_CARD                             "idcard"
#define ATR_IDCARD_TYPE                            "idcard_type"

/**
 * @brief configuration insert page \n
 *
 */
#define CONFIG_SEPARATOR_PAGE                      "separator_page"
#define ATR_SEPARATOR_PAGE                         "separator_page"
#define ATR_SEPARATOR_TRAY_IN                      "separator_tray_in"
#define ATR_SEPARATOR_PAPER_SIZE                   "separator_paper_size"
#define ATR_SEPARATOR_PAPER_TYPE                   "separator_paper_type"

/**
 * @brief configuration duplex print insert page \n
 *
 */
#define CONFIG_DUPLEX_PRINT_INSERT_PAGE            "duplex_print_insert_page"
#define ATR_DUPLEX_PRINT_INSERT_PAGE_SWITCH        "duplex_print_insert_page_switch"

/**
 * @brief configuration mix duplex print insert page \n
 *
 */
#define CONFIG_MIX_DUPLEX_PRINT_INSERT_PAGE            "mix_duplex_print_insert_page"
#define ATR_MIX_DUPLEX_PRINT_INSERT_PAGE_SWITCH        "mix_duplex_print_insert_page_switch"

/**
 */
#define CONFIG_CHAPTER            "chapter"
#define ATR_CHAPTER_SWITCH        "chapter_switch"
#define ATR_CHAPTER_PAGE          "chapter_page"
#define ATR_CHAPTER_TRAY_IN       "chapter_tray_in"
#define ATR_CHAPTER_PAPER_TYPE    "chapter_paper_type"
#define ATR_CHAPTER_PAPER_SIZE    "chapter_paper_size"


/**
 * @brief configuration ohp_insert \n
 *
 */
#define CONFIG_OHP_INSERT            "ohp_insert"
#define ATR_OHP_INSERT_SWITCH        "ohp_insert_switch"
#define ATR_OHP_INSERT_TRAY_IN       "ohp_insert_tray_in"
#define ATR_OHP_INSERT_PAPER_TYPE    "ohp_insert_paper_type"
#define ATR_OHP_INSERT_PAPER_SIZE    "ohp_insert_paper_size"

/**
 * @brief configuration chapter \n
 *
 */
#define CONFIG_INSERT_PAGE            "insert_page"
#define ATR_INSERT_PAGE_SWITCH        "insert_page_switch"
#define ATR_INSERT_PAGE_USE           "insert_page_use"
#define ATR_INSERT_PAGE               "insert_page"
#define ATR_INSERT_PAGE_TRAY_IN       "insert_page_tray_in"
#define ATR_INSERT_PAGE_PAPER_TYPE    "insert_page_paper_type"
#define ATR_INSERT_PAGE_PAPER_SIZE    "insert_page_paper_size"
/**
 * @brief configuration margin adjust \n
 *
 */
#define CONFIG_MARGIN_ADJUST                       "margin_adjust"
#define ATR_HORIZONTAL_MARGIN                      "horizontal_margin"
#define ATR_VERTICAL_MARGIN                        "vertical_margin"
#define ATR_SMALL_IMAGE_ENABLE                     "small_image_enable"

/**
 * @brief configuration pip 6220 \n
 *
 */
#define CONFIG_PIP_6220                            "pip_6220"
#define ATR_PIP_6220_MODE                          "pip_6220_mode"
#define ATR_PIP_6220_SCALE_PERCENT                 "scale_percent"
#define ATR_PIP_6220_OUT_BPP                       "pip_out_bpp"


/**
 * @brief configuration pip 6270 \n
 *
 */
#define CONFIG_PIP_6270                            "pip_6270"
#define ATR_PIP_6270_MODE                          "pip_6270_mode"
#define ATR_PIP_SCALE                              "pip_scale_percent"
#define ATR_PIP_BRIGHTNESS                         "pip_brightness"
#define ATR_OUT_BAND_H                             "out_band_high"
#define ATR_OUT_BPP                                "out_bpp"
#define ATR_OUT_XRES                               "out_xres"

/**
 * @brief configuration poster \n
 *
 */
#define CONFIG_POSTER                              "poster"
#define ATR_POSTER_SIZE                            "poster_size"

/**
 * @brief configuration removel color \n
 *
 */
#define CONFIG_REMOVE_COLOR                        "removel_color"
#define ATR_REMOVE_PLANE                           "remove_color_plane"

/**
 * @brief configuration resolution \n
 *
 */
#define CONFIG_RESOLUTION                          "resolution"
#define ATR_HORIZONTAL_DIRECTION                   "horizontal_direction"
#define ATR_VERTICAL_DIRECTION                     "vertical_direction"

/**
 * @brief configuration copy rotate angle \n
 *
 */
#define CONFIG_ROTATE_ANGLE                        "copy_rotate_angle"
#define ATR_ROTATE_ANGLE                           "rotate_angle"

/**
 * @brief configuration copy image orientation \n
 *
 */
#define CONFIG_COPIED_IMAGE_ORIENTATION            "copied_image_orientation"
#define ATR_FRONT_ORIENTATION                      "image_orientation"
#define ATR_BACK_ORIENTATION                       "image_orientation_back"

/**
 * @brief configuration copy backend rotate angle \n
 *
 */
#define CONFIG_ROTATE_ANGLE_BACKEND                        "backend_copy_rotate_angle"
#define ATR_ROTATE_ANGLE_FRONT_PAGE                         "rotate_angle_front_page"
#define ATR_ROTATE_ANGLE_BACK_PAGE                         "rotate_angle_back_page"

/**
 * @brief configuration copy scale \n
 *
 */
#define CONFIG_COPY_SCALE                          "scale"
#define ATR_SCALE_TYPE                             "scale_type"
#define ATR_SCALE_CUSTOM                           "user_define_scale_percent"

/**
 * @brief configuration pip 6270 \n
 *
 */
#define CONFIG_SCALE_PERCENT                       "scale_percent"
#define ATR_SCALE_PERCENT                          "scale_percent"
#define ATR_SCALE_X                                "scale_percent_x"
#define ATR_SCALE_Y                                "scale_percent_y"


/**
 * @brief configuration scan area \n
 *
 */
#define CONFIG_SCAN_AREA                           "scan_area"
#define ATR_AREA_W                                 "area_width"
#define ATR_AREA_H                                 "area_height"
#define ATR_AREA_SIZE                              "scan_page_size"

/**
 * @brief configuration scan job request \n
 *
 */
#define CONFIG_SCAN_REQUEST                        "scan_job_request"

/**
 * @brief configuration scan margin \n
 *
 */
#define CONFIG_SCAN_MARGIN                         "scan_margin"
#define ATR_MARGIN_TOP                             "scan_margin_top"
#define ATR_MARGIN_LEFT                            "scan_margin_left"

/**
 * @brief configuration scan source \n
 *
 */
#define CONFIG_SCAN_SOURCE                         "scan_source"
#define ATR_SOURCE_INT                             "scan_source_int"
#define ATR_SOURCE_STR                             "scan_source_str"
#define ATR_IS_DOUBLE                              "scan_is_double"


/**
 * @brief configuration sort \n
 *
 */
#define CONFIG_SORT                                "sort"
#define CONFIG_COVER_SORT                          "cover_sort"
#define CONFIG_COVER_EMPTY                         "cover_empty"
#define ATR_SORT_MODE                              "sort_mode"
#define ATR_SORT_SOURCE                            "scan_source"


/**
 * @brief configuration watermark \n
 *
 */
#define CONFIG_WATER_MARK                          "watermark"
#define ATR_MARK_TEXT                              "wm_text"
#define ATR_WATER_FRONT_ROTATE                     "front_rotate"
#define ATR_WATER_BACK_ROTATE                      "back_rotate"
#define ATR_WATER_MODE_2BIT                        "mode_2bit"


/**
 * @brief configuration for the type of normal copy \n
 *
 */
#define CONFIG_NORMAL                              "normal_copy"


/**
 * @brief configuration copy nup \n
 *
 */
#define CONFIG_NUP                            "copy_nup"
#define ATR_NUP_GAP                                "nup_gap"
#define ATR_NUP_MODE                               "nup_copy_mode"
#define ATR_COMBINATION_MODE                       "combination_mode"

/**
 * @brief configuration copy page_header_page_footer \n
 *
 */
#define CONFIG_PAGE_HEADER_PAGE_FOOTER             "page_header_page_footer"
#define ATR_PAGE_HEADER_ENABLE                     "page_header_enable"
#define ATR_PAGE_HEADER_POSITION                   "page_header_position"
#define ATR_PAGE_HEADER_PAGINATION                 "page_header_pagination"
#define ATR_PAGE_HEADER_TEXT_TYPE                  "page_header_text_type"
#define ATR_PAGE_HEADER_TEXT                       "page_header_text"
#define ATR_PAGE_FOOTER_ENABLE                     "page_footer_enable"
#define ATR_PAGE_FOOTER_POSITION                   "page_footer_position"
#define ATR_PAGE_FOOTER_PAGINATION                 "page_footer_pagination"
#define ATR_PAGE_FOOTER_TEXT_TYPE                  "page_footer_text_type"
#define ATR_PAGE_FOOTER_TEXT                       "page_footer_text"

/**
 * @brief configuration print mode\n
 *
 */
#define CONFIG_EDGE_TO_EDGE_MODE                   "edge_to_edge_mode"
#define ATR_EDGE_TO_EDGE_MODE                      "edge_to_edge_mode"

/**
 * @brief configuration for the speed for scan \n
 *
 */
#define CONFIG_SCAN_SPEED                           "scan_speed"
#define ATR_SCAN_SPEED                              "scan_speed"

/**
 * @brief configuration for the stapler \n
 *
 */
#define CONFIG_STAPLER                              "stapler"
#define ATR_STAPLER                                 "stapler"

/**
 * @brief configuration for mix mode \n
 *
 */
#define CONFIG_MIX                                  "mix_mode"
#define ATR_MIX                                     "mix_mode"

/**
 * @brief configuration for red_black mode \n
 *
 */
#define CONFIG_RK                                  "rk_mode"
#define ATR_RK                                     "rk_mode"

/**
 * @brief configuration for save_toner mode \n
 *
 */
#define CONFIG_SAVE_TONER                                  "save_toner_mode"
#define ATR_SAVE_TONER                                     "save_toner_mode"

/**
 * @brief configuration for page_split \n
 *
 */
#define CONFIG_PAGE_SPLIT                                  "page_split"
#define ATR_PAGE_SPLIT                                     "page_split"



#define CONFIG_BACKUP_MODE                          "backup_mode"
#define ATR_BACKUP_SEND_MODE                        "backup_send_mode"
#define ATR_BACKUP_IS_COMBINE                       "backup_is_combine"
#define ATR_BACKUP_MODE_EMAIL_NUM                   "backup_email_num"
#define ATR_BACKUP_MODE_EMAIL                       "backup_email_addr"
#define ATR_BACKUP_MODE_FTP                         "backup_ftp_info"
#define ATR_BACKUP_MODE_SMB                         "backup_SMB_info"
#define ATR_BACKUP_MODE_HTTP                        "backup_http_info"


/**
 * @brief configuration for image_enhancement mode \n
 *
 */
#define CONFIG_IMAGE_ENHANCEMENT                          "image_enhancement"

#define CONFIG_CROSS_OFFSET                         "cross_offset"
#define ATR_SAVE_IMAGE                              "save_image"

#define CONFIG_OFFSET_TRAY_PAPER                    "cross_tray_paper"
#define ATR_OFFSET_TRAY_PAPER                       "offset_tray_paper"
/**
 * @brief configuration for overlay image \n
 *
 */
#define CONFIG_OVERLAY_IMAGE                        "overlay_image"
#define IPU_OVERLAY_IMAGE_CREATE                    "overlay_image_create"
#define IPU_OVERLAY_IMAGE_COMPOUND                  "overlay_image_compound"
#define ATR_OLY_MODE                                "oly_mode"
#define ATR_OLY_IMG_REGISTER_NAME                   "oly_img_register_name"
#define ATR_OLY_IMG_NAME_FRONT                      "oly_img_name_front"
#define ATR_OLY_IMG_NAME_BACK                       "oly_img_name_back"
#define ATR_OLY_OFFSET_X                            "oly_offset_x"
#define ATR_OLY_OFFSET_Y                            "oly_offset_y"
#define ATR_OLY_STYLE                               "oly_style"
#define ATR_OLY_COLOR                               "oly_color"
#define ATR_OLY_COUNT                               "oly_count"
#define ATR_OLY_DENSITY                             "oly_density"
#define ATR_OLY_DESTROY_FLAG                        "oly_destroy_flag"

/**
 * @brief configuration for insert_image \n
 *
 */
#define CONFIG_INSERT_IMAGE                        "insert_image"
#define ATR_INSERT_IMAGE_ARRAY                     "insert_image_array"
#define ATR_INSERT_IMAGE_VAILD_COUNT               "insert_image_vaild_count"

/**
 * @brief configuration for insert_image \n
 *
 */
#define CONFIG_STAMP                               "stamp"
#define ATR_STAMP_TEXT                             "stamp_text"
#define ATR_STAMP_VALID_LINE                       "stamp_valid_line"
#define ATR_STAMP_ROTATE                           "stamp_rotate"
#define ATR_STAMP_COLOR                            "stamp_color"
#define ATR_STAMP_FONT_SIZE                        "stamp_font_size"
#define ATR_STAMP_ATOMIZE                          "stamp_atomize"
#define ATR_STAMP_ALIGN_STYLE                      "stamp_align_style"


#define ATR_STAMP_FRONT_ROTATE                     "front_rotate"
#define ATR_STAMP_BACK_ROTATE                      "back_rotate"

#define CONFIG_BOOK_COPY                            "book_copy"
#define ATR_BOOK_COPY_TYPE                          "book_copy_type"
#define ATR_NON_IMAGE_CLEAN                         "non_image_clean"
#define ATR_CENTER_CLEAN                            "center_clean"
#define ATR_IMAGE_IN_CENTER                         "image_in_center"

/**
 * @brief configuration sample mode \n
 *
 */
#define CONFIG_SAMPLE_MODE                          "sample_mode"
#define ATR_SAMPLE_MODE                             "sample_mode"

/**
 * @brief configuration single to double \n
 *
 */
#define CONFIG_S_TO_D                               "single_to_double"

/**
 * @brief configuration separate_scan_mode \n
 *
 */
#define CONFIG_SEPARATE_SCAN                       "separate_scan"
#define ATR_SEPARATE_SCAN_MODE                     "separate_scan_mode"

#endif

/**
* @}
**/

