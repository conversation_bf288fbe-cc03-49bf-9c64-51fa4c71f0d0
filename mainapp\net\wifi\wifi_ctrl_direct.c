/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file wifi_ctrl_direct.c
 * @addtogroup net
 * @{
 * @addtogroup wifi_ctrl_direct
 * <AUTHOR> Xin
 * @date 2023-4-20
 * @brief WiFi-Direct control API
 */
#include "nettypes.h"
#include "netmisc.h"
#include "netctx.h"
#include "wifi_country_alias.h"
#include "wifi_process_queue.h"
#include "wifi_ctrl.h"

/* WFD配置文件 */
#define WPA_WFD_CFG_FORMAT  "p2p_go_intent=15\n"        \
                            "p2p_ssid_postfix=%s\n"     \
                            "p2p_passphrase_len=%d\n"   \
                            "p2p_no_group_iface=1\n"    \
                            "max_num_sta=8\n"           \
                            "\n"                        \
                            "network={\n"               \
                            "	ssid=\"%s\"\n"          \
                            "	bssid=%s\n"             \
                            "	psk=\"%s\"\n"           \
                            "	proto=RSN\n"            \
                            "	key_mgmt=WPA-PSK\n"     \
                            "	pairwise=CCMP\n"        \
                            "	auth_alg=OPEN\n"        \
                            "	mode=3\n"               \
                            "	disabled=2\n"           \
                            "}\n"

static PI_THREAD_T          s_wfd_wpa_tid    = INVALIDTHREAD;
static struct wpa_ctrl*     s_wfd_wpa_recv   = NULL;
static struct wpa_ctrl*     s_wfd_wpa_send   = NULL;
static time_t               s_dev_lost_timer = 0;
static time_t               s_p2p_req_timer  = 0;
static char                 s_p2p_mac[18];

static void* wfd_wpa_monitor(void* arg);

static int32_t wfd_wpa_reconfigure(NET_CTX_S* net_ctx)
{
    FILE*           stream = NULL;
    char            mfg_name[16];
    char            pdt_name[32];
    char            pdt_sn[32];
    char            ssid_prefix[32];
    char            ssid_suffix[32];
    char            wfd_ssid[64];
    char            wfd_psk[64];
    char            wfd_mac[18];
    char            res[16];

    netdata_get_mac_addr(net_ctx->data_mgr, IFACE_ID_WFD, wfd_mac, sizeof(wfd_mac));
    netdata_get_mfg_name(net_ctx->data_mgr, mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(net_ctx->data_mgr, pdt_name, sizeof(pdt_name));
    netdata_get_pdt_sn(net_ctx->data_mgr, pdt_sn, sizeof(pdt_sn));

    netdata_get_wfd_ssid_prefix(net_ctx->data_mgr, ssid_prefix, sizeof(ssid_prefix));
    netdata_get_wfd_ssid_suffix(net_ctx->data_mgr, ssid_suffix, sizeof(ssid_suffix));
    snprintf(wfd_ssid, sizeof(wfd_ssid), "%s%s", ssid_prefix, ssid_suffix);
    netdata_get_wfd_psk(net_ctx->data_mgr, wfd_psk, sizeof(wfd_psk));

    /* 为wpa_supplicant生成配置文件 */
    RETURN_VAL_IF((stream = pi_fopen(WPA_WFD_CFG_FILE, "w")) == NULL, NET_WARN, -1);
    fprintf(stream, WPA_DEF_CFG_FORMAT, wfd_ssid, mfg_name, pdt_name, pdt_name, pdt_sn, wifi_country_alias(netdata_get_country(net_ctx->data_mgr)));
    fprintf(stream, WPA_WFD_CFG_FORMAT, wfd_ssid, strlen(wfd_psk), wfd_ssid, wfd_mac, wfd_psk);
    pi_fclose(stream);

    NET_DEBUG("starting wpa_supplicant on "IFACE_WFD);
    pi_runcmd(NULL, 0, 0, "wpa_supplicant -B -i "IFACE_WFD" -c "WPA_WFD_CFG_FILE" -P "WPA_WFD_PID_FILE" -f "WPA_WFD_LOG_FILE);
    RETURN_VAL_IF(waiting_program_start(WPA_WFD_PID_FILE, 20, 100) < 0, NET_WARN, -1);

    s_wfd_wpa_send = wpa_ctrl_open(WPA_CTRL_DIR"/"IFACE_WFD);
    RETURN_VAL_IF(s_wfd_wpa_send == NULL, NET_WARN, -1);

    wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), "RECONFIGURE");
    RETURN_VAL_IF(strncasecmp(res, "OK", 2) != 0, NET_WARN, -1);
    pi_msleep(200);

    return 0;
}

static void wfd_wpa_release(void)
{
    char    res[16];
    int32_t i;

    /* 销毁monitor线程 */
    if ( s_wfd_wpa_tid != INVALIDTHREAD )
    {
        pi_thread_destroy(s_wfd_wpa_tid);
        s_wfd_wpa_tid = NULL;
    }

    /* 销毁wpa_client recv对象 */
    if ( s_wfd_wpa_recv != NULL )
    {
        wpa_ctrl_detach(s_wfd_wpa_recv);
        wpa_ctrl_close(s_wfd_wpa_recv);
        s_wfd_wpa_recv = NULL;
    }

    if ( s_wfd_wpa_send != NULL )
    {
        /* 终止wpa_supplicant */
        wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), "TERMINATE");
        for ( i = 0; i < MAX_RETRY_TIMES && check_program_exist(WPA_WFD_PID_FILE) > 0; ++i )
        {
            NET_DEBUG("stoping wpa_supplicant on %s", IFACE_WFD);
            pi_msleep(30);
        }

        wpa_ctrl_close(s_wfd_wpa_send);
        s_wfd_wpa_send = NULL;
    }
}

static uint32_t wfd_select_frequency(NET_CTX_S* net_ctx)
{
    WIFI_SCAN_RESULT_S  result = { .ap_count = 0 };
    WIFI_AP_INFO_S*     apinfo = NULL;
    uint32_t            ap_num[11]; /* 信道1~11的负载权重，当有一个AP使用该信道时，权重+10，相邻信道权重+1 */
    uint32_t            frequency = 0;
    uint32_t            channel = 0;

    /* 如果STA是在线状态且频率获取大于0的话，WFD使用STA的频率 */
    if ( netdata_get_sta_status(net_ctx->data_mgr) == NETLINK_STATUS_CONNECTED )
    {
        frequency = netdata_get_sta_freq(net_ctx->data_mgr);
        NET_DEBUG("using STA frequency(%u)", frequency);
        return frequency;
    }

    netdata_get_ap_list(net_ctx->data_mgr, &result, sizeof(result));
    RETURN_VAL_IF(result.ap_count == 0, NET_INFO, 0);
    RETURN_VAL_IF(result.ap_count > 60, NET_WARN, 0);

    memset(ap_num, 0, sizeof(ap_num));
    /* 解析ap_info中的信道信息，并根据信道信息计算权重 */
    for ( uint32_t i = 0; i < result.ap_count; ++i )
    {
        apinfo = &(result.ap_info[i]);
        if ( apinfo->channel > 1 && apinfo->channel < 11 )
        {
            ap_num[apinfo->channel - 1] += 10;
            ap_num[apinfo->channel - 2]++;
            ap_num[apinfo->channel]++;
        }
        else if ( apinfo->channel == 11 )
        {
            ap_num[10] += 10;
            ap_num[9]++;
        }
        else if ( apinfo->channel == 1 )
        {
            ap_num[0] += 10;
            ap_num[1]++;
        }
    }

    /* 选择权重最小的信道 */
    for ( size_t i = 1; i < ARRAY_SIZE(ap_num); ++i )
    {
        if ( ap_num[i] < ap_num[channel] )
        {
            channel = (uint32_t)i;
        }
    }

    /* 真正的信道是数组下标+1 */
    frequency = (2407 + (channel + 1) * 5);
    NET_DEBUG("select channel(%u) frequency(%u)", channel + 1, frequency);
    return frequency;
}

static int32_t has_p2p_req(const char* s, char* mac)
{
    char*   pstr;
    int32_t ret;

    if ( (pstr = strstr(s, "P2P-PROV-DISC-PBC-REQ")) != NULL )
    {
        ret = sscanf(pstr, "P2P-PROV-DISC-PBC-REQ %17[^ ^\n]", mac);
        NET_DEBUG("Device(%s) requests to connect Wi-Fi Direct", mac);
    }
    else if ( (pstr = strstr(s, "P2P-GO-NEG-REQUEST")) != NULL )
    {
        ret = sscanf(pstr, "P2P-GO-NEG-REQUEST %17[^ ^\n]", mac);
        NET_DEBUG("Device(%s) requests to negotiate Wi-Fi Direct role", mac);
    }
    else if ( (pstr = strstr(s, "P2P-INVITATION-RECEIVED")) != NULL )
    {
        ret = sscanf(pstr, "P2P-INVITATION-RECEIVED %17[^ ^\n]", mac);
        NET_DEBUG("Device(%s) invites to join Wi-Fi Direct", mac);
    }
    else
    {
        ret = 0;
    }

    return ret;
}

static int32_t process_wfd_p2p_group_remove(NET_CTX_S* net_ctx, void* arg)
{
    char    res[16];

    RETURN_VAL_IF(netdata_get_iface_running(net_ctx->data_mgr, IFACE_ID_WFD) == 0, NET_DEBUG, 0);
    RETURN_VAL_IF(netdata_get_wfd_att_num(net_ctx->data_mgr) > 0, NET_DEBUG, 0);

    RETURN_VAL_IF(s_wfd_wpa_send == NULL, NET_WARN, -1);
    NET_INFO("WiFi-P2P remove group temporary");

    wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), "P2P_GROUP_REMOVE "IFACE_WFD);
    RETURN_VAL_IF(strncasecmp(res, "OK", 2) != 0, NET_WARN, -1);
    pi_msleep(500);

    wfd_wpa_release();
    return 0;
}

static int32_t process_wfd_p2p_group_add(NET_CTX_S* net_ctx, void* arg)
{
    char     res[16] = {0};

    RETURN_VAL_IF(netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_WFD)  == 0, NET_DEBUG, 0);
    wfd_wpa_reconfigure(net_ctx);
    wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), "P2P_GROUP_ADD persistent=0");
    if ( strncasecmp(res, "OK", 2) != 0 )
    {
        wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), "P2P_GROUP_ADD persistent=0");
        RETURN_VAL_IF(strncasecmp(res, "OK", 2) != 0, NET_WARN, -1);
    }

    return 0;
}

static int32_t process_wfd_switch(NET_CTX_S* net_ctx, void* arg)
{
    IFACE_SWITCH_E  active = (arg ? IFACE_SWITCH_ON : IFACE_SWITCH_OFF);
    uint32_t        mode = (uint32_t)arg;
    #if 0
    FILE*           stream = NULL;
    char            mfg_name[16];
    char            pdt_name[32];
    char            pdt_sn[32];
    char            ssid_prefix[32];
    char            ssid_suffix[32];
    char            wfd_ssid[64];
    char            wfd_psk[64];
    char            wfd_mac[18];
    char            res[16];
    #endif

    wfd_wpa_release();

    net_ifctl_switch_safe(IFACE_WFD, active);

    if ( netdata_set_iface_switch(net_ctx->data_mgr, IFACE_ID_WFD, mode) == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_SWITCH_CHANGED, mode);
    }

    if ( mode == 0 )
    {
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED, 0);
        netdata_set_wfd_att_num(net_ctx->data_mgr, 0);
        return 0;
    }

    #if 0
    netdata_get_mac_addr(net_ctx->data_mgr, IFACE_ID_WFD, wfd_mac, sizeof(wfd_mac));
    netdata_get_mfg_name(net_ctx->data_mgr, mfg_name, sizeof(mfg_name));
    netdata_get_pdt_name(net_ctx->data_mgr, pdt_name, sizeof(pdt_name));
    netdata_get_pdt_sn(net_ctx->data_mgr, pdt_sn, sizeof(pdt_sn));

    netdata_get_wfd_ssid_prefix(net_ctx->data_mgr, ssid_prefix, sizeof(ssid_prefix));
    netdata_get_wfd_ssid_suffix(net_ctx->data_mgr, ssid_suffix, sizeof(ssid_suffix));
    snprintf(wfd_ssid, sizeof(wfd_ssid), "%s%s", ssid_prefix, ssid_suffix);
    netdata_get_wfd_psk(net_ctx->data_mgr, wfd_psk, sizeof(wfd_psk));

    /* 为wpa_supplicant生成配置文件 */
    RETURN_VAL_IF((stream = pi_fopen(WPA_WFD_CFG_FILE, "w")) == NULL, NET_WARN, -1);
    fprintf(stream, WPA_DEF_CFG_FORMAT, wfd_ssid, mfg_name, pdt_name, pdt_name, pdt_sn, wifi_country_alias(netdata_get_country(net_ctx->data_mgr)));
    fprintf(stream, WPA_WFD_CFG_FORMAT, wfd_ssid, strlen(wfd_psk), wfd_ssid, wfd_mac, wfd_psk);
    pi_fclose(stream);

    NET_DEBUG("starting wpa_supplicant on "IFACE_WFD);
    pi_runcmd(NULL, 0, 0, "wpa_supplicant -B -i "IFACE_WFD" -c "WPA_WFD_CFG_FILE" -P "WPA_WFD_PID_FILE" -f "WPA_WFD_LOG_FILE);
    RETURN_VAL_IF(waiting_program_start(WPA_WFD_PID_FILE, 20, 100) < 0, NET_WARN, -1);

    s_wfd_wpa_send = wpa_ctrl_open(WPA_CTRL_DIR"/"IFACE_WFD);
    RETURN_VAL_IF(s_wfd_wpa_send == NULL, NET_WARN, -1);

    wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), "RECONFIGURE");
    RETURN_VAL_IF(strncasecmp(res, "OK", 2) != 0, NET_WARN, -1);
    pi_msleep(200);
    #endif

    process_wfd_p2p_group_add(net_ctx, NULL);
    pi_msleep(200);

    return 0;
}

static int32_t process_wfd_running(NET_CTX_S* net_ctx, void* arg)
{
    RETURN_VAL_IF(s_wfd_wpa_tid != INVALIDTHREAD, NET_NONE, 0);

    if ( s_wfd_wpa_recv != NULL )
    {
        NET_WARN("s_wfd_wpa_recv should be reload");
        wpa_ctrl_detach(s_wfd_wpa_recv);
        wpa_ctrl_close(s_wfd_wpa_recv);
    }

    s_wfd_wpa_recv = wpa_ctrl_open(WPA_CTRL_DIR"/"IFACE_WFD);
    RETURN_VAL_IF(s_wfd_wpa_recv == NULL, NET_WARN, -1);

    NET_DEBUG("Register as an event monitor for the "IFACE_WFD" control interface");
    RETURN_VAL_IF(wpa_ctrl_attach(s_wfd_wpa_recv) != 0, NET_WARN, -1);

    s_wfd_wpa_tid = pi_thread_create(wfd_wpa_monitor, NETSERVICE_STACK_SIZE, NULL, NETSERVICE_TASK_PRIORITY, net_ctx, "wfd_wpa_monitor");
    RETURN_VAL_IF(s_wfd_wpa_tid == INVALIDTHREAD, NET_WARN, -1);

    return 0;
}

static int32_t process_wfd_p2p_response(NET_CTX_S* net_ctx, void* arg)
{
    int32_t accept = (int32_t)arg;
    char    res[64];
    char    cmd[64];

    NET_DEBUG("response %d to %s", accept, s_p2p_mac);
    if ( s_p2p_mac[0] )
    {
        if ( accept )
        {
            snprintf(cmd, sizeof(cmd), "WPS_PBC p2p_dev_addr=%s", s_p2p_mac);
        }
        else
        {
            snprintf(cmd, sizeof(cmd), "P2P_REJECT %s", s_p2p_mac);
        }
        memset(s_p2p_mac, 0, sizeof(s_p2p_mac));
    }
    else
    {
        if ( accept )
        {
            snprintf(cmd, sizeof(cmd), "WPS_PBC");
        }
        else
        {
            snprintf(cmd, sizeof(cmd), "P2P_REJECT");
        }
    }
    wpa_ctrl_cmd(s_wfd_wpa_send, res, sizeof(res), cmd);
    s_dev_lost_timer = 0;
    s_p2p_req_timer = 0;

    return 0;
}

static int32_t process_wfd_p2p_request(NET_CTX_S* net_ctx, void* arg)
{
    char*    dev_mac = (char *)arg;
    uint32_t wfd_mode;

    if ( dev_mac == NULL )
    {
        NET_DEBUG("dev_mac is NULL");
        memset(s_p2p_mac, 0, sizeof(s_p2p_mac));
        s_dev_lost_timer = 0;
        s_p2p_req_timer = 0;
        return 0;
    }

    NET_DEBUG("dev_mac(%s)", dev_mac);
    wfd_mode = netdata_get_iface_switch(net_ctx->data_mgr, IFACE_ID_WFD);
    NET_DEBUG("mode(%u)", wfd_mode);
    if ( wfd_mode == 1 ) /* 自动 */
    {
        snprintf(s_p2p_mac, sizeof(s_p2p_mac), "%s", dev_mac);
        process_wfd_p2p_response(net_ctx, (void *)1);
    }
    else if ( wfd_mode == 2 ) /* 手动 */
    {
        if ( s_p2p_req_timer == 0 )
        {
            snprintf(s_p2p_mac, sizeof(s_p2p_mac), "%s", dev_mac);
            NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_REQUEST_CHANGED, 1);
            s_p2p_req_timer = pi_time(NULL);
        }
        else if ( s_dev_lost_timer > 0 && strcmp(dev_mac, s_p2p_mac) == 0 )
        {
            s_dev_lost_timer = 0;
        }
        else
        {
            NET_INFO("device(%s) connecting, ignore new request from device(%s)", s_p2p_mac, dev_mac);
        }
    }
    else
    {
        NET_WARN("invalid wfd mode(%u)", wfd_mode);
    }
    pi_free(dev_mac);

    return 0;
}

static int32_t process_wfd_event_connected(NET_CTX_S* net_ctx, void* arg)
{
    memset(s_p2p_mac, 0, sizeof(s_p2p_mac));
    process_wfd_p2p_response(net_ctx, (void *)1);
    return 0;
}

static int32_t process_wfd_device_lost(NET_CTX_S* net_ctx, void* arg)
{
    RETURN_VAL_IF(s_p2p_req_timer <= 0 || s_dev_lost_timer != 0, NET_WARN, -1);

    s_dev_lost_timer = pi_time(NULL); /* 收到设备丢失后不立刻结束应答等待，启动confirm计时器 */
    NET_DEBUG("s_dev_lost_timer(%d)", (int32_t)s_dev_lost_timer);

    return 0;
}

static int32_t process_wfd_idle_check(NET_CTX_S* net_ctx, void* arg)
{
    if ( pi_time(NULL) - s_p2p_req_timer > 130 ) /* 手动模式下，等待直连确认超时，通知面板结束直连请求确认界面 */
    {
        NET_DEBUG("p2p request timeout, response reject");
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_REQUEST_CHANGED, 0);
        process_wfd_p2p_response(net_ctx, (void *)0);
    }
    else if ( s_dev_lost_timer > 0 && pi_time(NULL) - s_dev_lost_timer > 5 )/* 设备丢失5S仍未收到重连请求，通知面板结束直连请求确认界面 */
    {
        NET_DEBUG("p2p device lost");
        NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_REQUEST_CHANGED, 0);
        process_wfd_p2p_request(net_ctx, NULL);
    }

    return 0;
}

static void* wfd_wpa_monitor(void* arg)
{
    NET_CTX_S*  net_ctx = (NET_CTX_S *)arg;
    uint32_t    attached_count = 0;
    time_t      reactive_timer = 0;
    char        dev_mac[18];
    char        buf[512];
    size_t      len;

    while ( 1 )
    {
        if ( wpa_ctrl_pending(s_wfd_wpa_recv) > 0 )
        {
            len = sizeof(buf) - 1;
            wpa_ctrl_recv(s_wfd_wpa_recv, buf, &len);
            buf[len] = '\0';

            /* 过滤不关注的事件，减少LOG刷屏 */
            if ( strstr(buf, "CTRL-EVENT-BSS-ADDED") == NULL && strstr(buf, "CTRL-EVENT-BSS-REMOVED") == NULL
                    && strstr(buf, "RX-PROBE-REQUEST") == NULL && strstr(buf, "WPS-ENROLLEE-SEEN") == NULL )
            {
                NET_DEBUG("len(%u) buf(%s)", len, buf);
            }

            memset(dev_mac, 0, sizeof(dev_mac));
            if ( has_p2p_req(buf, dev_mac) ) /* 判断是否直连请求 */
            {
                PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_p2p_request, (void *)strdup(dev_mac));
            }
            else if ( strstr(buf, "CTRL-EVENT-CONNECTED") ) /* WPS快速连接 */
            {
                PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_event_connected, NULL);
            }
            else if ( strstr(buf, "AP-STA-CONNECTED") ) /* 连接 */
            {
                attached_count++;
                if ( netdata_set_wfd_att_num(net_ctx->data_mgr, attached_count) == 0 )
                {
                    NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED, attached_count);
                }
                NET_DEBUG("number of attached devices(%u) (+)", attached_count);
            }
            else if ( strstr(buf, "AP-STA-DISCONNECTED") ) /* 断开连接 */
            {
                attached_count = (attached_count > 1 ? attached_count - 1 : 0);
                if ( netdata_set_wfd_att_num(net_ctx->data_mgr, attached_count) == 0 )
                {
                    NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED, attached_count);
                }
                NET_DEBUG("number of attached devices(%u) (-)", attached_count);
            }
            else if ( strstr(buf, "P2P-DEVICE-LOST") || strstr(buf, "P2P-GO-NEG-FAILURE") ) /* P2P设备丢失，存在误报，所以增加5秒确认 */
            {
                PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_device_lost, NULL);
            }
            else if ( strstr(buf, "AP-DISABLED") || strstr(buf, "Failed to start AP functionality") )
            {
                NET_DEBUG("AP disabled, clear number of attached devices!!!");
                attached_count = 0;
                if ( netdata_set_wfd_att_num(net_ctx->data_mgr, attached_count) == 0 )
                {
                    NETEVT_NOTIFY_I(EVT_TYPE_NET_WFD_ATTACHED_DEVICES_COUNT_CHANGED, attached_count);
                }
            }
            else if ( s_p2p_req_timer > 0 ) /* 其他事件不做处理，idle中目前只做连接超时检查 */
            {
                PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_idle_check, NULL);
            }
        }
        else
        {
            if ( netdata_get_iface_running(net_ctx->data_mgr, IFACE_ID_WFD) == 0 ) /* 监测到AP未运行，启动计时器，每30秒重试一次激活AP */
            {
                if ( reactive_timer == 0 )
                {
                    reactive_timer = pi_time(NULL);
                }
                else if ( pi_time(NULL) - reactive_timer > 30 )
                {
                    PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_p2p_group_add, NULL);
                    reactive_timer = pi_time(NULL);
                    //NET_DEBUG("starting wpa_supplicant on "IFACE_WFD);
                    //pi_runcmd(NULL, 0, 0, "wpa_supplicant -B -i "IFACE_WFD" -c "WPA_WFD_CFG_FILE" -P "WPA_WFD_PID_FILE" -f "WPA_WFD_LOG_FILE);
                }
            }
            else
            {
                if ( s_p2p_req_timer > 0 ) /* 无事件上报，检查是否有连接超时 */
                {
                    PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_idle_check, NULL);
                }
                reactive_timer = 0;
            }
            pi_msleep(500);
        }
    }

    return NULL;
}

int32_t wifi_ctrl_wfd_p2p_group(uint32_t operate)
{
    NET_DEBUG("process_wfd_p2p_group_%s", operate ? "add" : "remove");
    if ( operate == 0 )
    {
        return PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_p2p_group_remove, NULL);
    }
    else
    {
        return PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_p2p_group_add, NULL);
    }
}

int32_t wifi_ctrl_wfd_switch(uint32_t mode)
{
    NET_DEBUG("process_wfd_switch(%u)", mode);
    return PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_switch, (void *)mode);
}

int32_t wifi_ctrl_wfd_running(void)
{
    NET_DEBUG("process_wfd_running");
    return PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_running, NULL);
}

int32_t wifi_ctrl_wfd_response(uint32_t resp)
{
    NET_DEBUG("process_wfd_p2p_response(%u)", resp);
    return PUSH_TASK_TO_WFD_PROCESS_QUEUE(process_wfd_p2p_response, (void *)resp);
}
/**
 *@}
 */
