/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_mgr.h
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2023-07-13
 * @brief event manager client API
 */
#ifndef EVENT_MGR_H
#define EVENT_MGR_H

#include "event_mgr_typedef.h"

PT_BEGIN_DECLS

/**
 * @brief       Create a event manager client, return the pointer of the client object.
 * @param[in]   module_id       : The client module identify from the value of enum EVT_MODULE_E.
 * @param[in]   event_callback  : The callback function of any event registered by this client hanppened.
 * @param[in]   callback_ctx    : The parameter of the callback function from main thread.
 * @param[out]  ret_val         : If create client successfully, ret_val is 0, or else, ret_val is less than 0, such as ERR_EVT_MGR_INVALID_PARAM.
 * @return      Create a event manager client, return the pointer of the client object.
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR>
 * @data        2023-7-13
 */
EVT_MGR_CLI_S*  pi_event_mgr_create_client  (uint32_t module_id, void (*event_callback)(const EVT_MSG_S *, void *), void* callback_ctx, int32_t* ret_val);

/**
 * @brief       Destroy the pointer of the event manager client.
 * @param[in]   thiz        : The pointer of the client object.
 * <AUTHOR> Xin
 * @data        2023-7-13
 */
void            pi_event_mgr_destroy_client (EVT_MGR_CLI_S* thiz);

/**
 * @brief       Register for the event manager client these event it focuses on.
 * @param[in]   thiz        : The pointer of the client object.
 * @param[in]   event_array : Event type array, the array member is from enum EVT_TYPE_E.
 * @param[in]   event_count : The count of the array member.
 * @return      Return 0 for success, or less than 0, such as ERR_EVT_MGR_INVALID_PARAM.
 * @retval      == 0 : success\n
 *              <  0 : fail
 * <AUTHOR> Xin
 * @data        2023-7-13
 */
int32_t         pi_event_mgr_register       (EVT_MGR_CLI_S* thiz, uint32_t* event_array, size_t event_count);

/**
 * @brief       Unregister for the event manager client these event it focuses on.
 * @param[in]   thiz        : The pointer of the client object.
 * @param[in]   event_array : Event type array, the array member is from enum EVT_TYPE_E.
 * @param[in]   event_count : The count of the array member.
 * @return      Return 0 for success, or less than 0, such as ERR_EVT_MGR_INVALID_PARAM.
 * @retval      == 0 : success\n
 *              <  0 : fail
 * <AUTHOR> Xin
 * @data        2023-7-13
 */
int32_t         pi_event_mgr_unregister     (EVT_MGR_CLI_S* thiz, uint32_t* event_array, size_t event_count);

/**
 * @brief       Notify the event to service.
 * @param[in]   thiz        : The pointer of the client object.
 * @param[in]   event_type  : The event of the client want to notify others. the value from EVT_TYPE_E.
 * @param[in]   data        : The attribute data of the current event.
 * @param[in]   data_length : The length of the data.
 * @return      Return 0 for success, or less than 0, such as ERR_EVT_MGR_INVALID_PARAM.
 * @retval      == 0 : success\n
 *              <  0 : fail
 * <AUTHOR> Xin
 * @data        2023-7-13
 */
int32_t         pi_event_mgr_notify         (EVT_MGR_CLI_S* thiz, uint32_t event_type, const void* data, uint32_t data_length);

PT_END_DECLS

#endif /* EVENT_MGR_H */
/**
 *@}
 */
