#include <assert.h>
#include "pol/pol_mem.h"
#include "pol_test.h"

#define MEM_ERR_HINT "[MEM-TEST]"   

static int32_t should_return_negative_number_if_malloc_err()
{
	void *point = pi_malloc(-1);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point == NULL);

	return PASS;
}

static int32_t should_return_positive_number_if_malloc_ok()
{
	void *point = pi_malloc(1);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point != NULL);
	pi_free(point);

	return PASS;
}

static int32_t should_return_negative_number_if_realloc_err()
{
	void *point = pi_malloc(1);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point != NULL);

	void *ret_point;
	uint64_t size = 0xFFFFFFFFFFFFFFF;
	/*while ((ret_point = pi_realloc(point, size)) != NULL) {
		size += 0xFFFFFFFF;
	}*/
	ret_point = pi_realloc(point, size);

	if (ret_point == NULL) {
		perror(MEM_ERR_HINT);
	}

	assert(ret_point == NULL);
	pi_free(point);

	return PASS;
}

static int32_t should_return_positive_number_if_realloc_ok()
{
	void *point = pi_malloc(1);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point != NULL);

	
	void *ret_point = pi_realloc(point, 4);
	if (ret_point == NULL) {
		perror(MEM_ERR_HINT);
	}

	assert(ret_point != NULL);
	pi_free(ret_point);

	return PASS;
}

static int32_t should_return_negative_number_if_zalloc_err()
{
	void *point = pi_zalloc(-1);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point == NULL);

	return PASS;
}

static int32_t should_return_positive_number_if_zalloc_ok()
{
	void *point = pi_zalloc(1);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point != NULL);
	pi_free(point);

	return PASS;
}

static int32_t should_return_negative_number_if_memset_err()
{
	char buf[1024];
	
	void *point = pi_memset(buf,0xff, 1024);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point == NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_memset_ok()
{
	char buf[1024];
	
	void *point = pi_memset(buf, 0xff, 1024);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point != NULL);
	
	return PASS;
}

static int32_t should_return_negative_number_if_memcpy_err()
{
	char src_buf[1024];
	char dest_buf[512];
	void *point = pi_memcpy(dest_buf, src_buf, 512);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point == NULL);
	return PASS;
}

static int32_t should_return_positive_number_if_memcpy_ok()
{
	char src_buf[1024];
	char dest_buf[512];
	void *point = pi_memcpy(dest_buf, src_buf, 512);
	if (point == NULL) {
		perror(MEM_ERR_HINT);
	}
	assert(point != NULL);
	return PASS;
}

static int32_t should_return_negative_number_if_memcmp_err()
{
	int ret = pi_memcmp(NULL, NULL, 0);
	return PASS;
}

static int32_t should_return_positive_number_if_memcmp_ok()
{
	char src_buf[512] = {0};
	char dest_buf[512] = {0};

	int ret = pi_memcmp(dest_buf, src_buf, 512);

	assert(ret == 0);

	return PASS;
}

static int32_t should_return_negative_number_if_mmap_err()
{
 
	return PASS;
}

static int32_t should_return_positive_number_if_mmap_ok()
{
	return PASS;
}

static int32_t should_return_negative_number_if_munmap_err()
{
	return PASS;
}

static int32_t should_return_positive_number_if_munmap_ok()
{
	return PASS;
}

TEST_ITEM_S mem_test_pool[] = {
	{"pi_malloc_err", should_return_negative_number_if_malloc_err},
	{"pi_malloc", should_return_positive_number_if_malloc_ok},
	{"pi_realloc_err", should_return_negative_number_if_realloc_err},
	{"pi_realloc", should_return_positive_number_if_realloc_ok},
    {"pi_zalloc_err", should_return_negative_number_if_zalloc_err},
	{"pi_zalloc", should_return_positive_number_if_zalloc_ok},
    {"pi_memset_err", should_return_negative_number_if_memset_err},
	{"pi_memset", should_return_positive_number_if_memset_ok},
    {"pi_memcpy_err", should_return_negative_number_if_memcpy_err},
	{"pi_memcpy", should_return_positive_number_if_memcpy_ok},
    {"pi_memcmp_err", should_return_negative_number_if_memcmp_err},
	{"pi_memcmp", should_return_positive_number_if_memcmp_ok},
    {"pi_mmap_err", should_return_negative_number_if_mmap_err},
	{"pi_mmap", should_return_positive_number_if_mmap_ok},
    {"pi_munmap_err", should_return_negative_number_if_munmap_err},
	{"pi_munmap", should_return_positive_number_if_munmap_ok},
};

TEST_POOL_S mem_pool = {
    "mem",
    mem_test_pool,
    sizeof(mem_test_pool)/sizeof(TEST_ITEM_S),
};

