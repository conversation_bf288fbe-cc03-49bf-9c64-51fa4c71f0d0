/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file cmd.h
 * @addtogroup cmd
 * @{
 * <AUTHOR> zhoushujing
 * @date 2023-4-18
 * @brief ternimal cmd debug module
 * @par Usage:
 *
 * step 1 : initialize cmd module once per process
 *
 * cmd_prolog("debug");
 *
 * step 2 : register some subcmds as you needed 
 *
 * //callback \n
 * static int32_t animal_dog_process( int32_t argc, char *argv[] )\n
 * {\n
 *   if( argc<1 )return -1;\n
 *      printf("hello dog %s\n",agrv[0] );\n
 *   return 0;\n
 * }
 *
 * //callback \n
 * static int32_t animal_cat_process( int32_t argc, char *argv[] )\n
 * {\n
 * printf("hello cat\n");\n
 * return 0;\n
 * }
 * 
 * //register some subcmds for main\n
 * ret = cmd_register( "animal", "dog" animal_dog_process, NULL );\n
 * ret = cmd_register( "animal", "cat" animal_cat_process, NULL );
 * 
 * stpe 3: run a subcmd on the target ternimal
 *
 * #echo animal dog irycle > /tmp/debug \n
 * #hello dog irycle
 * 
 * #echo animal cat > /tmp/debug \n
 * #hello cat
 **/

#ifndef __CMD_H__
#define __CMD_H__

#include <pol/pol_types.h>
#include <pol/pol_define.h>

PT_BEGIN_DECLS

/**
 * @brief  register a debug cmd for a module.
 * @param[in] main_cmd_string: module name,such as fax/print/scan/copy/usb...
 * @param[in] subcmd_string:   module function name or an operation name
 * @param[in] subcmd_process:  specfic the function callback for the debug cmd
 * @param[in] subcmd_help:     how to use the cmd,what should user input
 * @return    register result
 *
 * @retval    0: if register success  \n
 *           -1: if register failed
 * <AUTHOR> 
 * @data   2023-4-18
*/
int32_t cmd_register(char *main_cmd_string, char *subcmd_string, int32_t (*subcmd_process)(int32_t argc, char *argv[]),
        const char *subcmd_help );

/**
 * @brief  cmd module initialize
 * @param[in] name  : specfic the fifo name in /tmp/ dir, examples-- \n
 * if name is "cmd", the real fifo pathname is "/tmp/cmd",the max name string length is 8
 * @return initialize result
 * @retval 0: if initialize success  \n
 *        -1: if initialize failed
 * <AUTHOR> zhoushujing
 * @data   2023-4-18
 * @note   each process just invoke initialize once and only once, and ensure with the different name

*/
int32_t cmd_prolog(char* name );

PT_END_DECLS

#endif
/**
 *@}
 */
