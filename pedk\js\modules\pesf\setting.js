const ErrorNo = globalThis.pedk.common.ERROR_NO
class netsetting {
    constructor() {

    }
	getSMTPEmailEncryptMode()
	{
		return get_smtp_email_encrypt_mode();
	}
	getSMTPLoginName()
	{
		return get_smtp_login_name();
	}
	getSMTPLoginPwd()
	{
		return get_smtp_login_pwd();
	}
	getSMTPPort()
	{
		return get_smtp_port();
	}
	getSMTPServerName()
	{
		return get_smtp_server_name();
	}
	getWiredConnectInfo()
	{
		return get_wired_connect_info();
	}
	getWiredMacAddrInfo()
	{
		return get_wired_mac_addr_info();
	}
	getWiredNet802ProtocolSwitch()
	{
		return get_wired_net_802_protocol_switch();
	}
	getWiredNetIPv4Addr()
	{
		return get_wired_net_ipv4_addr();
	}
	getWiredNetIPv4GateWay()
	{
		return get_wired_net_ipv4_gateway();
	}
	getWiredNetIPv4HostName()
	{
		return get_wired_net_ipv4_hostname();
	}
	getWiredNetIPv4Mask()
	{
		return get_wired_net_ipv4_mask();
	}
	getWiredNetIPv4Mode()
	{
		return get_wired_net_ipv4_mode();
	}
	setSMTPEmailEncryptMode(security)
	{
		return set_smtp_email_encrypt_mode(security)
	}
	setSMTPLoginName(login_name)
	{
		return set_smtp_login_name(login_name)
	}
	setSMTPLoginPwd(login_pwd)
	{
		return set_smtp_login_pwd(login_pwd)
	}
	setSMTPPort(port)
	{
		return set_smtp_port(port)
	}
	setSMTPServerName()
	{
		return set_smtp_server_name();
	}
	setWiredNet802ProtocolSwitch(enable)
	{
		return set_wired_net_802_protocol_switch(enable)
	}
	setWiredNetIPv4Addr(addr)
	{
		return set_wired_net_ipv4_addr(addr)
	}
	setWiredNetIPv4HostName(hostname)
	{
		return set_wired_net_ipv4_hostname(hostname)
	}
	setWiredNetIPv4Mode(mode)
	{
		return set_wired_net_ipv4_mode(mode)
	}
	setWiredNetIPv6Switch(enable)
	{
		return set_wired_net_ipv6_switch(enable)
	}
}



 export const FUNCTION_TYPE = {
	FUNC_T_USBPORT_PRINT: "FUNC_T_USBPORT_PRINT",
	FUNC_T_PULL_SCAN: "FUNC_T_PULL_SCAN",
	FUNC_T_PUSH_SCAN: "FUNC_T_PUSH_SCAN",
	FUNC_T_COPY: "FUNC_T_COPY",
	FUNC_T_IDCOPY: "FUNC_T_IDCOPY",
	FUNC_T_BILL: "FUNC_T_BILL",
	FUNC_T_COLOR_PRINT: "FUNC_T_COLOR_PRINT",
	FUNC_T_COLOR_COPY: "FUNC_T_COLOR_COPY",
	FUNC_T_FAX: "FUNC_T_FAX",
	FUNC_T_NET_COMMUNICATION: "FUNC_T_NET_COMMUNICATION",
	FUNC_T_MENU_SETTING: "FUNC_T_MENU_SETTING",
	FUNC_T_MENU_SYSTEM_SETTING: "FUNC_T_MENU_SYSTEM_SETTING",
	FUNC_T_MENU_PRINT_SETTING: "FUNC_T_MENU_PRINT_SETTING",
	FUNC_T_MENU_TRAY_SETTING: "FUNC_T_MENU_TRAY_SETTING",
	FUNC_T_MENU_NETWORK_SETTING: "FUNC_T_MENU_NETWORK_SETTING",
	FUNC_T_MENU_SAMPLE_PRINT_SETTING: "FUNC_T_MENU_SAMPLE_PRINT_SETTING",
	FUNC_T_MENU_SHORTCUT_SETTING: "FUNC_T_MENU_SHORTCUT_SETTING",
	FUNC_T_MENU_JOB_LIST_SETTING: "FUNC_T_MENU_JOB_LIST_SETTING",
	FUNC_T_MENU_ADDRESS_BOOK_SETTING: "FUNC_T_MENU_ADDRESS_BOOK_SETTING",
	FUNC_T_MENU_CUSTOM_DESKTOP_SETTING: "FUNC_T_MENU_CUSTOM_DESKTOP_SETTING",
	FUNC_T_SECURE_PRINT: "FUNC_T_SECURE_PRINT",
	FUNC_T_SCAN_TO_USB: "FUNC_T_SCAN_TO_USB",
	FUNC_T_SCAN_TO_PC: "FUNC_T_SCAN_TO_PC",
	FUNC_T_SCAN_TO_SMB: "FUNC_T_SCAN_TO_SMB",
	FUNC_T_SCAN_TO_FTP: "FUNC_T_SCAN_TO_FTP",
	FUNC_T_SCAN_TO_EMAIL: "FUNC_T_SCAN_TO_EMAIL",
	FUNC_T_NET_PRINT: "FUNC_T_NET_PRINT",
	FUNC_T_MONO_PRINT: "FUNC_T_MONO_PRINT",
	FUNC_T_USB_ENABLE: "FUNC_T_USB_ENABLE",
  };

export const FUNCTION_SWITCH = {
  FUNC_SW_ON: "FUNC_SW_ON",
  FUNC_SW_OFF: "FUNC_SW_OFF",
};

const func_map = new Map([
	[ FUNCTION_TYPE.FUNC_T_USBPORT_PRINT, 				0],
	[ FUNCTION_TYPE.FUNC_T_PULL_SCAN, 					1],
	[ FUNCTION_TYPE.FUNC_T_PUSH_SCAN, 					2],
	[ FUNCTION_TYPE.FUNC_T_COPY,							3],
	[ FUNCTION_TYPE.FUNC_T_IDCOPY,						4],
	[ FUNCTION_TYPE.FUNC_T_BILL,							5],
	[ FUNCTION_TYPE.FUNC_T_COLOR_PRINT,					6],
	[ FUNCTION_TYPE.FUNC_T_COLOR_COPY,					7],
	[ FUNCTION_TYPE.FUNC_T_FAX,							8],
	[ FUNCTION_TYPE.FUNC_T_NET_COMMUNICATION, 			9],
	[ FUNCTION_TYPE.FUNC_T_MENU_SETTING,					10],
	[ FUNCTION_TYPE.FUNC_T_MENU_SYSTEM_SETTING,			11],
	[ FUNCTION_TYPE.FUNC_T_MENU_PRINT_SETTING,			12],
	[ FUNCTION_TYPE.FUNC_T_MENU_TRAY_SETTING, 			13],
	[ FUNCTION_TYPE.FUNC_T_MENU_NETWORK_SETTING,			14],
	[ FUNCTION_TYPE.FUNC_T_MENU_SAMPLE_PRINT_SETTING, 	15],
	[ FUNCTION_TYPE.FUNC_T_MENU_SHORTCUT_SETTING, 		16],
	[ FUNCTION_TYPE.FUNC_T_MENU_JOB_LIST_SETTING, 		17],
	[ FUNCTION_TYPE.FUNC_T_MENU_ADDRESS_BOOK_SETTING, 	18],
	[ FUNCTION_TYPE.FUNC_T_MENU_CUSTOM_DESKTOP_SETTING,	19],
	[ FUNCTION_TYPE.FUNC_T_SECURE_PRINT,					20],
	[ FUNCTION_TYPE.FUNC_T_SCAN_TO_USB,					21],
	[ FUNCTION_TYPE.FUNC_T_SCAN_TO_PC,					22],
	[ FUNCTION_TYPE.FUNC_T_SCAN_TO_SMB,					23],
	[ FUNCTION_TYPE.FUNC_T_SCAN_TO_FTP,					24],
	[ FUNCTION_TYPE.FUNC_T_SCAN_TO_EMAIL, 				25],
	[ FUNCTION_TYPE.FUNC_T_NET_PRINT, 					26],
	[ FUNCTION_TYPE.FUNC_T_MONO_PRINT,					27],
	[ FUNCTION_TYPE.FUNC_T_USB_ENABLE,					28],

]);
export function setFunctionSwitch(func, sw)
{
	var ret = ErrorNo.EXIT_FAILURE;
	var func_int = 0, switch_int = 0;


    if(typeof func !== 'string' || typeof sw !== 'string') {
    	throw TypeError("parmeter type must be string");
    }

	if( !Object.values(FUNCTION_TYPE).includes(func) )
	{
		console.log('Input function type [',func,'] error' );
		return ret;
	}

	if( !Object.values(FUNCTION_SWITCH).includes(sw) )
	{
		console.log('Input function switch [',sw,'] error' );
		return ret;
	}

	if( "FUNC_SW_ON" === sw )
	{
		switch_int = 1;
	}
	else
	{
		switch_int = 0;
	}

	func_int = func_map.get( func );

	console.log("setFunctionSwitch func_int:"+ func_int + " switch_int:" + switch_int );
	if( 0 === js_setting_setFunctionSwitch(func_int, switch_int) )
	{
		ret = ErrorNo.EXIT_SUCCESS;
	}
	else
	{
		ret = ErrorNo.EXIT_FAILURE;
	}


    return ret;
}



class CopyRetentionParam
{
    constructor(  )
    {
		this.url			 	= "http://www.test.com/";
		this.headers		 	= undefined;
		this.format_type		= 1;
		this.level				= 1;
		this.protocol			= "unkonw_protocol";
		this.file_name_prefix	= "";
    }
}

var g_CopyRetentionSwitch = false;
var g_CopyRetentionParams = new CopyRetentionParam();


function set_CopyRetention()
{

	var http_header = "";
	var custom_field = "";
	if(g_CopyRetentionParams.url === "http://www.test.com/")
	{
		console.log("********** param not set... ");
	}
	else
	{
		http_header = JSON.stringify(g_CopyRetentionParams.headers.map);
		console.log('xxxxxxxxxxxxxxxxxxxxx setting.js headers = '  + http_header);
	}

	console.log('setCopyRetentionSwitch = '+g_CopyRetentionSwitch );
	console.log(' url = '  + g_CopyRetentionParams.url );
	console.log(' headers = '  + http_header );
	console.log(' format_type = '  + g_CopyRetentionParams.format_type );
	console.log(' level = '  + g_CopyRetentionParams.level );
	console.log(' protocol = '  + g_CopyRetentionParams.protocol );
	console.log(' file_name_prefix = '  + g_CopyRetentionParams.file_name_prefix );


	if (g_CopyRetentionParams.file_name_prefix == null) 
    {
		g_CopyRetentionParams.file_name_prefix = "";
		console.log(' null ?? file_name_prefix = '  + g_CopyRetentionParams.file_name_prefix );
	}


	copy_jsc_set_retention_param(g_CopyRetentionSwitch,g_CopyRetentionParams.url,http_header,g_CopyRetentionParams.format_type,g_CopyRetentionParams.level,g_CopyRetentionParams.protocol,g_CopyRetentionParams.file_name_prefix,custom_field);

	console.log("**********set_CopyRetention ");
}

export function setCopyRetentionSwitch(sw)
{
    if(typeof sw !== 'boolean')
    {
        console.log("Must be only one boolean parmeter")
		return ErrorNo.EINVALIDPARAM
    }

	g_CopyRetentionSwitch = sw;

	set_CopyRetention();

	return ErrorNo.EXIT_SUCCESS;
}

export function getCopyRetentionSwitch()
{
	console.log('getCopyRetentionSwitch = '+g_CopyRetentionSwitch );

	return g_CopyRetentionSwitch;
}

export function setCopyRetentionParams(param)
{		
	if(typeof param != "object")
	{
		console.log("The parmeter type error (setCopyRetentionParams)")
		return ErrorNo.EINVALIDPARAM
	}
	
	g_CopyRetentionParams = param;

	set_CopyRetention();

	return ErrorNo.EXIT_SUCCESS;
}

export function getCopyRetentionParams()
{
	return g_CopyRetentionParams;
}



//globalThis.pesf.device.setting = {}
globalThis.pedk.device.setting.setFunctionSwitch = setFunctionSwitch
globalThis.pedk.device.setting.FUNCTION_SWITCH = FUNCTION_SWITCH
globalThis.pedk.device.setting.FUNCTION_TYPE = FUNCTION_TYPE
globalThis.pedk.device.setting.getTotalPrintedSheets = getTotalPrintedSheets;
globalThis.pedk.device.setting.getTotalColorPrintPage = getTotalColorPrintPage;
globalThis.pedk.device.setting.getTotalMonoPrintPage = getTotalMonoPrintPage;
globalThis.pedk.device.setting.getTotalPagesByMediaSize = getTotalPagesByMediaSize;
globalThis.pedk.device.setting.getADFScanHostPage     = getADFScanHostPage;
globalThis.pedk.device.setting.getFBScanHostPage      = getFBScanHostPage;
globalThis.pedk.device.setting.getADFScanCopyedPage   = getADFScanCopyedPage;
globalThis.pedk.device.setting.getFBScanCopyedPage    = getFBScanCopyedPage;
//globalThis.pedk.device.setting.traySetting.getTrayPaperSize  = get_TrayPaperSize;
//globalThis.pedk.device.setting.traySetting.getTrayPaperType  = get_TrayPaperType;
globalThis.pedk.device.setting.getPowderBoxModel             = getPowderBoxModel;
globalThis.pedk.device.setting.CopyRetentionParam = CopyRetentionParam;
globalThis.pedk.device.setting.setCopyRetentionSwitch = setCopyRetentionSwitch;
globalThis.pedk.device.setting.getCopyRetentionSwitch = getCopyRetentionSwitch;
globalThis.pedk.device.setting.setCopyRetentionParams = setCopyRetentionParams;
globalThis.pedk.device.setting.getCopyRetentionParams = getCopyRetentionParams;

