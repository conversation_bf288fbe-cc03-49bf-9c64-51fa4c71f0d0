
/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file runtime.c
 * @addtogroup runtime
 * @{
 * @addtogroup runtime
 * @autor
 * @date 2024-06-11
 * @brief runtime init
 */

#include "basic/config.h"
#include "basic/log/log.h"
#include "basic/sys/sys.h"
#include "runtime/runtime.h"
#include "runtime/utils/tool_utils.h"
#include "runtime/utils/msgq_utils.h"
#include "runtime/utils/quickjs_utils.h"
#include "runtime/utils/uthash_utils.h"
#include "runtime/modules/module_manager.h"
#include "runtime/modules/bridge/bridge.h"

/*临时方案，启动app时加载依赖的js文件*/
#define PESF_JS_FILE_PATH(FILE_NAME) ("/pesf/js/modules/pesf/" FILE_NAME)
#define PESF_JS_FILE_PATH_BASIC(FILE_NAME) ("/pesf/js/modules/" FILE_NAME)

static const char *js_files[] = {
    PESF_JS_FILE_PATH_BASIC("common.js"),
    PESF_JS_FILE_PATH_BASIC("disptach_event.js"),
    PESF_JS_FILE_PATH("jobctl_api.js"),
    PESF_JS_FILE_PATH("print_api.js"),
    PESF_JS_FILE_PATH("scan_api.js"),
    PESF_JS_FILE_PATH("copy_api.js"),
    PESF_JS_FILE_PATH("ui_api.js"),
    PESF_JS_FILE_PATH("addressbook.js"),
    PESF_JS_FILE_PATH("beeper.js"),
    PESF_JS_FILE_PATH("iccard.js"),
    PESF_JS_FILE_PATH("capability.js"),
    PESF_JS_FILE_PATH("whitelist.js"),
    PESF_JS_FILE_PATH("lowpower.js"),
    PESF_JS_FILE_PATH("device_setting.js"),
    PESF_JS_FILE_PATH("setting_net.js"),
    PESF_JS_FILE_PATH("app_setting.js"),
    PESF_JS_FILE_PATH("status_api.js"),
    PESF_JS_FILE_PATH("smb.js"),
    PESF_JS_FILE_PATH("ftp.js"),
    PESF_JS_FILE_PATH("system_setting.js"),
    PESF_JS_FILE_PATH("usbdevice.js"),
    //PESF_JS_FILE_PATH("/email.js"),
    PESF_JS_FILE_PATH("setting.js"),
    //PESF_JS_FILE_PATH("total_pages.js"),
    PESF_JS_FILE_PATH("http.js"),
    PESF_JS_FILE_PATH("env.js"),
    PESF_JS_FILE_PATH("ssl.js"),
    PESF_JS_FILE_PATH("deviceinfo.js"),
    PESF_JS_FILE_PATH("productinfo.js"),
    PESF_JS_FILE_PATH("storage.js"),
    PESF_JS_FILE_PATH("traysetting.js"),
};

/*临时方案，启动app时 加载依赖的js文件*/
int32_t runtime_run(PeSFRunTime* prt)
{
    /*临时方案，启动app时 加载依赖的js文件*/
    for (size_t i = 0; i < sizeof(js_files) / sizeof(const char *); i++)
    {
        quickjs_load_js_file(prt->qjs_ctx, js_files[i]);
    }
    return (int32_t)quickjs_load_js_file(prt->qjs_ctx, prt->app_path.app_js);
}

/**
 * @brief app子线程中分发处理不同内部消息
 *
 */
void app_msg_exec(PeSFRunTime* prt ,INNER_MSG* inner_msg)
{
    switch (inner_msg->type) {
    case E_INNER_END:
        //结束消息，直接退出uv_loop
        uv_stop(prt->uv_loop);
        LOG_D("app_msg_exec","stop");
        break;

    case E_INNER_APP_MSG:
        // 转发消息给js文件
        send_to_bridge(prt, inner_msg->data_length, (const char*)inner_msg->data);
        LOG_D("app_msg_exec","send_to_bridge");
        break;
    case E_INNER_HEARTBEAT:
        // 心跳消息
        prt->heartbeat_flag = BEAT;
        break;
    // 此处可扩展
    default:
        break;
    }
}

/**
 * @brief 执行pending工作
 *
 */
static void execute_js_pending_job(PeSFRunTime* prt)
{
#ifdef PRT_DEBUG_LOG
    printf("execute the pending jobs\n");
#endif
    JSContext* ctx1;
    int err = JS_ExecutePendingJob(prt->qjs_rt, &ctx1);
    if (err < 0) {
        quickjs_utils_std_dump_error(ctx1);
    }
}

/**
 * @brief uv_idle的回调函数
 *
 */
static void libuv_idle_cb(uv_idle_t* handle)
{
#ifdef PRT_DEBUG_LOG
    printf("uv__idle_cb\n");
#endif
    // nothing
}

/**
 * @brief 判断是JobPending是否为空，如果不是空，则启动idle回调
 *
 */
static void libuv_maybe_idle(PeSFRunTime* prt)
{
#ifdef PRT_DEBUG_LOG
    printf("uv__maybe_idle\n");
#endif
    if (JS_IsJobPending(prt->qjs_rt)) {
        uv_idle_start(prt->uv_idle, libuv_idle_cb);
    } else {
        uv_idle_stop(prt->uv_idle);
    }
}

/**
 * @brief check回调
 *
 */
static void libuv_check_cb(uv_check_t* handle)
{
#ifdef PRT_DEBUG_LOG
    printf("uv__check_cb\n");
#endif
    PeSFRunTime* prt = handle->data;
    execute_js_pending_job(prt);
    libuv_maybe_idle(handle->data);
}

/**
 * @brief 预处理回调
 *
 */
static void libuv_prepare_cb(uv_prepare_t* handle)
{
#ifdef PRT_DEBUG_LOG
    printf("uv__prepare_cb\n");
#endif
    libuv_maybe_idle(handle->data);
}

int runtime_stop_loop(PeSFRunTime* prt, int code)
{
#ifdef PRT_DEBUG_LOG
    printf("runtime_stop_loop\n");
#endif
    uv_stop(prt->uv_loop);
    prt->exit_code = code;
    return 0;
}

/**
 * @brief app线程启动函数
 *
 */
static void* app_start(void* rt)
{
    int32_t ret = 0;
    int32_t loop_flag = 1;

    // 参数获取属于自己线程的运行时
    PeSFRunTime* prt = (PeSFRunTime*)rt;

	LOG_I("app_start","[%s] is running\n", prt->app_name);
    // 1.执行脚本准备
    runtime_init(prt);

    LOG_D("app_start","runtime_init ok");
    // 2.执行脚本
    ret = runtime_run(prt);
    if (ret != 0) {
        LOG_E("runtime", " Run js error!");
        loop_flag = 0;
    }
    LOG_D("app_start","runtime_run ok");

    // 3.启动libuv的预处理与check
    uv_prepare_start(prt->uv_prepare, libuv_prepare_cb);
    uv_check_start(prt->uv_check, libuv_check_cb);

    // 4.进入js循环
    ret = 0;

    do {
        libuv_maybe_idle(prt);
        // 等待事件响应。事件包括：主线程发来的消息，定时器时间到，promise事件触发
        ret = uv_run(prt->uv_loop, UV_RUN_DEFAULT);
    } while (ret == 0);

    LOG_D("app_start","break while");
    //5.打印执行错误
    quickjs_utils_std_dump_error(prt->qjs_ctx);
}

/**
 * @brief 分配app运行时
 *
 */
PeSFRunTime* runtime_create(uint8_t* data, uint16_t len)
{
    PeSFRunTime* prt = (PeSFRunTime*)malloc(sizeof(PeSFRunTime));

    // start指令，数据为应用程序名，字符串后'\0'
    prt->app_name = (char*)malloc(len + 1);
    strncpy(prt->app_name, data, len);
    prt->app_name[len] = '\0';

    // 准备基础数据
    prt->app_path.work_space = make_file_path( prt->app_name,"");
    prt->app_path.app_js = make_file_path( prt->app_name,JS_APP_NAME);
    prt->app_path.app_json = make_file_path( prt->app_name,JS_APP_NAME_JSON);
    prt->app_path.proj_config_json = make_file_path( prt->app_name,JS_APP_PROJ_CONFIG_NAME);

    // 函数绑定
    prt->start_func = app_start; // 绑定默认回调函数，pesf开发者可以自定义替换这些回调函数。
    prt->add_instance_data = add_instance_data; // 添加实例数据函数
    prt->get_instance_data = get_instance_data; // 获取实例数据函数
    prt->del_instance_data = del_instance_data; // 删除实例数据函数

    return prt;
}

/**
 * @brief 初始化app运行时
 *        其中qjs_rt、qjs_ctx必须在子线程中创建，否则会因为栈空间超出地址范围而不能使用
 */
void runtime_init(PeSFRunTime* prt)
{
    LOG_D("runtime","runtime_init start");
    // 创建JS运行时与JS上下文
    prt->qjs_rt = JS_NewRuntime();
    prt->qjs_ctx = JS_NewContext(prt->qjs_rt);

    // 2.将运行时传递给qjs_ctx的user，使JS内部也可以使用运行时外部内容。
    JS_SetContextOpaque(prt->qjs_ctx, prt);

    // 3.初始化libuv
    prt->uv_loop = uv_loop_new();
    prt->uv_async = (uv_async_t*)malloc(sizeof(uv_async_t));
    prt->uv_idle = (uv_idle_t*)malloc(sizeof(uv_idle_t));
    prt->uv_prepare = (uv_prepare_t*)malloc(sizeof(uv_prepare_t));
    prt->uv_check = (uv_check_t*)malloc(sizeof(uv_check_t));
    prt->uv_mutex = (uv_mutex_t*)malloc(sizeof(uv_mutex_t));

    uv_async_init(prt->uv_loop, prt->uv_async, receive_data_from_queue);
    uv_idle_init(prt->uv_loop, prt->uv_idle);
    uv_prepare_init(prt->uv_loop, prt->uv_prepare);
    uv_check_init(prt->uv_loop, prt->uv_check);
    uv_mutex_init(prt->uv_mutex);

    prt->uv_idle->data = prt;
    prt->uv_prepare->data = prt;
    prt->uv_check->data = prt;

    // 4.初始化消息队列
    prt->msgq.msg_cont = 0;
    prt->msgq.msg_head = NULL;
    prt->msgq.msg_tail = NULL;

    // 5.实例化全部模块
    // 5.1.实例化列表初始为NULL
    prt->instance_list = NULL;
    // 5.2.调用全部模块的实例化函数。
    modules_instance_all(prt);

    // 6.记录启动时间，填充部分动态属性
    prt->dynamic_property.start_time.time_v = get_sys_time();
    prt->dynamic_property.start_time.time_str = sys_time_to_str(prt->dynamic_property.start_time.time_v);
    prt->dynamic_property.app_name = prt->app_name;
    prt->dynamic_property.run_state = APP_RUN_BACKEND;

    // 7.心跳标记启动
    prt->heartbeat_flag = BEAT;
}

/**
 * @brief 释放运行时，简化版本，仅在创建运行时失败时调用
 *
 * @param prt
 * @return int
 */
int runtime_destroy_simple(PeSFRunTime* prt)
{
    LOG_D("runtime","runtime_destroy_simple");
    // 1. 释放基础数据
    if(NULL != prt->app_name){
       free(prt->app_name);
    }
    if(NULL != prt->app_path.work_space){
       free(prt->app_path.work_space);
    }
    if(NULL != prt->app_path.app_js){
       free(prt->app_path.app_js);
    }
    if(NULL != prt->app_path.app_json){
       free(prt->app_path.app_json);
    }
    if(NULL != prt->app_path.proj_config_json){
       free(prt->app_path.proj_config_json);
    }

    // 2. 函数绑定全部解除
    prt->start_func = NULL;
    prt->add_instance_data = NULL;
    prt->get_instance_data = NULL;
    prt->del_instance_data = NULL;

    // 3. 最后释放运行时自身
    free(prt);
}

/**
 * @brief 释放运行时，完整版本
 *
 * @param prt
 * @return int
 */
int runtime_destroy(PeSFRunTime* prt)
{
    LOG_D("runtime","runtime_destroy start");
    // 1.去实例化全部模块
    modules_generalization_all(prt);
    prt->instance_list = NULL;
    LOG_D("runtime","modules_generalization_all ok");

    // 2. 销毁uv_loop
    uv_stop(prt->uv_loop);
    free(prt->uv_async);
    free(prt->uv_idle);
    free(prt->uv_prepare);
    free(prt->uv_check);
    free(prt->uv_mutex);
    uv_loop_close(prt->uv_loop);
    free(prt->uv_loop);
    LOG_D("runtime","free all uv ok");

    // 3. 销毁JS运行时与JS上下文
    JS_FreeContext(prt->qjs_ctx);
    LOG_D("runtime","free qjs_ctx ok");
    // 在调用JS_FreeRuntime前，绑定在JS运行时上的各个回调函数必须删干净，否则会触发assert。
    // 也就是说各个子模块的generalization函数中要给自己模块的内容卸载干净。
    // 此处不能删，要求各个子模块的编写者必须把释放写好，否则会100%泄露
    // 在释放js运行时前，要检查是否有泄露的js运行时资源，
    JS_FreeRuntime(prt->qjs_rt);
    LOG_D("runtime","free qjs_rt ok");

    // 4. 函数绑定全部解除
    prt->start_func = NULL;
    prt->add_instance_data = NULL;
    prt->get_instance_data = NULL;
    prt->del_instance_data = NULL;

    // 5. 释放基础数据
    if (NULL != prt->dynamic_property.start_time.time_str) {
        free(prt->dynamic_property.start_time.time_str);
        prt->dynamic_property.start_time.time_str = NULL;
    }
    if (NULL != prt->dynamic_property.duration_time.time_str) {
        free(prt->dynamic_property.duration_time.time_str);
        prt->dynamic_property.duration_time.time_str = NULL;
    }
    if(NULL != prt->app_name){
       free(prt->app_name);
    }
    if(NULL != prt->app_path.work_space){
       free(prt->app_path.work_space);
    }
    if(NULL != prt->app_path.app_js){
       free(prt->app_path.app_js);
    }
    if(NULL != prt->app_path.app_json){
       free(prt->app_path.app_json);
    }
    if(NULL != prt->app_path.proj_config_json){
       free(prt->app_path.proj_config_json);
    }
    LOG_D("runtime","clean strings ok");

    // 6. 线程句柄置零
    prt->thread_handle = 0;

    // 7. 从哈希表中去除运行时
    hash_del(prt->dynamic_property.rtid);
    LOG_D("runtime","hash_del ok");

    // 8. 最后释放运行时自身
    free(prt);
    LOG_D("runtime","free prt ok");

    return 0;
}

/**
 * @}
 */

