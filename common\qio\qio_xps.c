/*
 * @Description:  qioxps.c
 * @brief: QIO object that create a XPS file from an image
 * @Author: <PERSON><PERSON>.zhang
 * @Date: 2023-06-20 15:34:33
 * @LastEditors: Zoey.zhang
 * @Email: <EMAIL>
 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <math.h>

#include "qiox.h"
#include "ofd/zip.h"
#include "xps/xps_tools.h"

/**
 * XPS 目录结构
 * $ROOT
 *  │  FixedDocSeq.fdseq
 *  │  [Content_Types].xml
 *  │
 *  ├─Documents
 *  │  └─1
 *  │      │  FixedDoc.fdoc
 *  │      │
 *  │      └─Pages
 *  │          │  1.fpage
 *  │          │  2.fpage
 *  │          │
 *  │          └─_rels
 *  │                  1.fpage.rels
 *  │                  2.fpage.rels
 *  │
 *  ├─Resources
 *  │  └─Images
 *  │          image_0.jpg
 *  │          image_1.jpg
 *  │
 *  └─_rels
 *          .rels
 */

#define XPS_MAX_PAGES   2048
#define XPS_UNIT        96   /* xps坐标系统的基本单位为 1/96 , 为了便于计算定义宏XPS_UNIT为96 */

// typedef struct tag_xps_page
// {
//     int  ResId;
//     char name[256];
//     char jpegname[256];
// }XPS_Page, *PXPS_Page;


typedef struct tag_xpsqio_ctx
{
    zipFile     zFile;  ///< Actual output stream qio
    int			ownqio;							///< this qio owns dest qio (to close it at close)
    int			isftp;							///<flag ftp
    int			imgcount;						///< total images read in
    int   	    maxUnitID;             ///< number of objects
    int			w, h, d, s, f;			///< current image dimensions and format
    int         xres, yres;       ///< the resolution of the image, in DPI, where applicable
    int         clipX, clipY;     ///< max dimensions of output w/h

    // XPS_Page    xpsPages[XPS_MAX_PAGES];
}XPSQIO, *PXPSQIO;

//static XPSQIO xpsQio;
//static PXPSQIO pXpsQio = &xpsQio;
// static int current_xpsFile_PageNum = 0;


static int QIOXPSread(QIO_P pqio, unsigned char* buffer, int nbuf)
{
    return -1;
}

static int QIOXPSwrite(QIO_P pqio, unsigned char* buffer, int nbuf)
{
    return -1;
}

static int QIOXPSseek(QIO_P pqio, long offset, int whence)
{
    return -1;
}

static int QIOXPSpoll(QIO_P pqio, int what, int tos, int tous)
{
    return 0;
}

static int QIOXPSclose(QIO_P pqio)
{
    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    PXPSQIO qio = (PXPSQIO)pqio->priv;
    if( !qio )
    {
        return -1;
    }
    if( !qio->zFile )
    {
        return -1;
    }

    int ret = 0;
    char *tmp_path = {"Documents/1/FixedDoc.fdoc"};
    ret = xpsFixedDocumentCreate(qio->zFile, tmp_path, qio->imgcount);
    if( ret != 0 )
    {
        pi_log_e(" create FixedDoc.fdoc failed \n");
        return -1;
    }

    ret = zipClose(qio->zFile, NULL);
    if( ret != ZIP_OK )
    {
        pi_log_e(" [qioXPS] zipClose failed \n");
        return -1;
    }

    pi_free(qio);
    pi_free(pqio);

    return ret;
}

/**
 * @description: calculateValueForXPS
 * @brief:  计算value在xps 坐标系统下的对应值, xps的基本单位 1/96inch
 *          pixelNums = pdi * inch
 * @param {int} value: 在当前resolution分辨率下的值
 * @param {int} resolution： 当前value的分辨率
 * @return {*}
 * @author: zoey.zhang
 */
static int calculateValueForXPS(int value, int resolution)
{
    int result = 0;

    float inchNums = ( (float)value ) / resolution;
    result = ceilf(inchNums * XPS_UNIT);

    return result;
}

/**
 * @description: QIOXPSAddImage
 * @brief: 向XPS文档中增加image resource
 * @param {char*} imgPath
 * @param {int} fd_dst
 * @return {*}
 * @author: zoey.zhang
 */
int QIOXPSAddImage(const char* imgPath, int fd_dst)
{
    int ret = 0;
    QIO_P pqio = (QIO_P)fd_dst;
    if( !pqio || (imgPath == NULL) )
    {
        return -1;
    }

    PXPSQIO qio = (PXPSQIO)pqio->priv;
    if( qio == NULL )
    {
        return -1;
    }
    if( qio->imgcount > XPS_MAX_PAGES )
    {
        return -1;
    }

    // PXPS_Page page = &(qio->xpsPages[qio->imgcount]);
    // sprintf(page->jpegname, "%s",imgPath);

    /* add image resources to zip */
    char tmp_path[100] = {0};
    sprintf(tmp_path, "Resources/Images/image_%d.jpg", qio->imgcount);
    ret = xpsAddResource(qio->zFile, tmp_path, imgPath);
    if( ret != 0 )
    {
        return -1;
    }

    /* create file that description page  */
    int pWidth = calculateValueForXPS(qio->w, qio->xres);     //<< page width in the xps coordinate system
    int pHeight = calculateValueForXPS(qio->h, qio->yres);    //<< page height in the xps coordinate system
    int imgWidth = pWidth;    //<< image width in the xps coordinate system
    int imgHeight = pHeight;   //<< image width in the xps coordinate system
    sprintf(tmp_path, "Documents/1/Pages/%d.fpage", qio->imgcount + 1);
    ret = xpsFixedPagesCreate(qio->zFile, tmp_path, pWidth, pHeight, qio->imgcount, imgWidth, imgHeight);
    if( ret != 0 )
    {
        return -1;
    }

    sprintf(tmp_path, "Documents/1/Pages/_rels/%d.fpage.rels", qio->imgcount + 1);
    ret = xpsPageRelsCreate(qio->zFile, tmp_path, qio->imgcount);
    if( ret != 0 )
    {
        return -1;
    }

    qio->imgcount++;


    return ret;
}

/**
 * @description: QIOXPSbeginImage
 * @brief: 记录用于创建xps page的image 信息
 * @return {*}
 * @author: zoey.zhang
 */
int QIOXPSbeginImage( QIO_P pqio,
                      int w, int h, int d, int s, int f,
                      int xres, int yres,
                      int clipx, int clipy)
{
    if( !pqio )
    {
        return -1;
    }

    PXPSQIO pio = (PXPSQIO)pqio->priv;
    if( !pio )
    {
        return -1;
    }

    if (pio->imgcount >= XPS_MAX_PAGES)
    {
        pi_log_i("Max image count reached\n");
        return -1;
    }

    pio->w = w;
    pio->h = h;
    pio->d = d;
    pio->s = s;
    pio->f = f;
    pio->xres = xres;
    pio->yres = yres;
    if (pio->xres <= 0)
    {
        pio->xres = 300;
    }
    if (pio->yres <= 0)
    {
        pio->yres = 300;
    }
    pio->clipX = clipx;
    pio->clipY = clipy;

    return 0;

}

/**
 * @description: QIOXPScreate
 * @brief: create a QIO  object for xps
 * @param {char*} filePath: output path
 * @param {int} flags:
 * @return {QIO_P} : QIO_P
 * @author: zoey.zhang
 */
QIO_P QIOXPScreate(const char* filePath, int flags)
{
    QIO_P pqio = NULL;
    PXPSQIO    priv;

    if( !filePath )
    {
        return NULL;
    }

    priv = (PXPSQIO)pi_zalloc(sizeof(XPSQIO));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    priv->zFile = zipOpen(filePath, 0);
    if( priv->zFile == NULL )
    {
        pi_free(priv);
        pi_log_e("open %s failed\n", filePath);
        return NULL;
    }

    /* init xps document */
    xpsDocumentInit(priv->zFile);

    pqio = (QIO_P)pi_malloc(sizeof(QIO_S));
    if( !pqio )
    {
        zipClose(priv->zFile, NULL);
        pi_free(priv);
        pi_log_e("QIOUSBD - usb device alloc failed\n");
        return NULL;
    }
    memset(pqio, 0x00, sizeof(QIO_S));
    pqio->priv = priv;
    pqio->close = QIOXPSclose;
    pqio->poll  = QIOXPSpoll;
    pqio->read  = QIOXPSread;
    pqio->write = QIOXPSwrite;
    pqio->seek  = QIOXPSseek;

    return pqio;

}
