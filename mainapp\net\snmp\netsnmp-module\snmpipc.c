#include <net-snmp/net-snmp-config.h>
#include <net-snmp/net-snmp-includes.h>
#include <net-snmp/agent/net-snmp-agent-includes.h>

#include "snmpipc.h"

void* ipc_shm(int* shm_fd, const char* shm_name, size_t size)
{
    void* ptr;
    int fd;

    if (shm_fd == NULL || shm_name == NULL)
    {
        return NULL;
    }

    fd = shm_open(shm_name, O_RDWR | O_CREAT, 0644);
    if (fd < 0)
    {
        snmp_log(LOG_ERR, "shm_open '%s' failed: '%s'\n", shm_name, strerror(errno));
        return NULL;
    }

    ftruncate(fd, size);
    ptr = mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (ptr == NULL)
    {
        snmp_log(LOG_ERR, "mmap '%s' failed: '%s'\n", shm_name, strerror(errno));
        close(fd);
        shm_unlink(shm_name);
    }
    else
    {
        *shm_fd = fd;
    }

    return ptr;
}

void init_snmpipc(void)
{
    return;
}

