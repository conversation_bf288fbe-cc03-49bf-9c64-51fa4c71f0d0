/**
 * @copyright 2023 Shenzhen Pantum Technology Co.Ltd all rights reserved
 * @file ips_api.h
 * @addtogroup print_image_module
 * @{
 * @addtogroup print_ips_module
 * <AUTHOR>
 * @date 2023-10-23
 * @version v0.1
 * @brief
 */

#ifndef _PRINT_IPS_API_H_
#define _PRINT_IPS_API_H_

/*** Head file area ***************************************************/
#include "ips/print_ips_public_data.h"

/**********************************************************************/

/*** Macro description area *******************************************/

/**********************************************************************/
/*** Enumerate description area ***************************************/
/**********************************************************************/

/*** Function description area ****************************************/
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief IPS模块的 prolog     \n
          这里创建了两个任务:       \n
          1. IPS的解析任务 - 启动 IPS 模块的解析功能 \n
          2. IPS的控制任务 - 主要处理 IPS 模块与其他模块之间的消息交互
 * @param[in]
 * @param[out]
 * @return  0 - 成功
 * @retval
 * <AUTHOR> @date
 * @note  N/A
 */
extern int print_ips_api_if_prolog( void );

/**
 * @brief IPS模块的 epilog
 * @param[in]
 * @param[out]
 * @return
 * @retval
 * <AUTHOR> @date
 * @note  N/A
 */
extern void print_ips_api_if_epilog( void );

/**
 * @brief debug调试接口
 * @param[in] arg - param num
 * @param[in] argv - param string
 * @param[out]
 * @return int32
 * @retval 0 success
 * @retval !0 failure
 * <AUTHOR> @date
 * @note  N/A
 */
extern int32_t  print_ips_api_debug(int argc, char *argv[]);

/**
 * @brief 预解析接口
 * @param[in] pdata
 * @param[in] buff_size
 * @param[in] pgqio - NULL
 * @param[in] result
 * @param[out]
 * @return int32
 * @retval PREPARSER_REGISTER_SUCCESS success
 * @retval PREPARSER_REGISTER_ERROR failure
 * <AUTHOR> @date
 * @note  N/A
 */
extern int32_t  print_ips_api_pre_parser(unsigned char* pdata, uint32_t buff_size,void* pgqio ,void *result);

#ifdef __cplusplus
}
#endif

/**********************************************************************/

#endif
/**
 *@ }
*/
