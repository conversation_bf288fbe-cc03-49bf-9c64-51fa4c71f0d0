/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       rule_parsing.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-10-29
 * @version    v1.0
 * @details    this is rule parsing layout for the image process mgr submoudle
 */
#ifndef RULE_PARSING_H
#define RULE_PARSING_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>


#define MAX_RULE_CNT    1024        ///< max rule cnt
#define SUCCESS         1           ///< success result
#define FAIL            0           ///< fail result

/**
 * @brief rule Object
 */
typedef  struct tag_rule_parsing
{
    char            source_rule[MAX_RULE_CNT];      ///< string is analysed
    char            *current_location;              ///< point to location of being analysed
    char            parsing_result[MAX_RULE_CNT];   ///<  result buffer
    int                result_cnt;                     ///<  result count of byte

    struct tag_rule_parsing *prev;                  ///< list prev ponit
    struct tag_rule_parsing *next;                  ///< list next ponit
}RULE_PARSING_S, *RULE_PARSING_P;


/**
 * @brief get rule object
 * @param[in] string  rule string
 * @return RULE_PARSING_P \n
 * @retval rule_parsing
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
RULE_PARSING_P rule_parsing_create_object(char* string);

/**
 * @brief destroy the object of rule
 * @param[in] RULE_PARSING_P  rule parsing
 * @return void \n
 * @retval void
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void rule_parsing_destroy_object(RULE_PARSING_P rule);

/**
 * @brief destroy the object of rule
 * @param[in] RULE_PARSING_P  rule parsing
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_get_pipe_name(RULE_PARSING_P rule);

/**
 * @brief rule_parsing_get_one_step
 * @param[in] RULE_PARSING_P  rule parsing
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_get_one_step(RULE_PARSING_P rule);

/**
 * @brief rule_parsing_get_step_name
 * @param[in] RULE_PARSING_P  rule parsing
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_get_step_name(RULE_PARSING_P rule);

/**
 * @brief rule_parsing_get_step_params
 * @param[in] RULE_PARSING_P  rule parsing
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_get_step_params(RULE_PARSING_P rule);

/**
 * @brief rule_parsing_get_attr_value_string
 * @param[in] RULE_PARSING_P  rule parsing
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_get_attr_value_string(RULE_PARSING_P rule);

/**
 * @brief rule_parsing_prolog
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_prolog();

/**
 * @brief rule_parsing_prolog
 * @return int \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int rule_parsing_epilog();

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* RULE_PARSING_H */

/**
 *@}
 */


