class MailInfo{
	constructor(index, mail_name, mail_addr, group_num)
	{
		if(mail_name === undefined)
		{
			throw new Error('mail_name is required')
		}
		if(mail_addr === undefined)
		{
			throw new Error('mail_addr is required')
		}
		this.index = index;
		this.mail_name = mail_name;
		this.mail_addr = mail_addr;
		this.group_num = group_num;
		return this;
    }
}

class GroupInfo{
	constructor(index, group_name, array)
	{
		if(group_name === undefined)
		{
			throw new Error('group_name is required')
		}
		this.index = index;
		this.group_name = group_name;
		this.array = array;
		return this;
    }
}

class FtpInfo{
	constructor(index, ftp_name, ftp_addr, ftp_subdirectory, ftp_port, ftp_anonymous, ftp_login_name, ftp_login_pwd)
	{
		if(ftp_name === undefined)
		{
			throw new Error('ftp_name is required')
		}
		if(ftp_addr === undefined)
		{
			throw new Error('ftp_addr is required')
		}
		if(ftp_subdirectory === undefined)
		{
			throw new Error('ftp_subdirectory is required')
		}
		if(ftp_port === undefined)
		{
			throw new Error('ftp_port is required')
		}
		if(ftp_anonymous === undefined)
		{
			throw new Error('ftp_anonymous is required')
		}
		if(ftp_login_name === undefined)
		{
			throw new Error('ftp_login_name is required')
		}
		if(ftp_login_pwd === undefined)
		{
			throw new Error('ftp_login_pwd is required')
		}
		this.index = index;
		this.ftp_name = ftp_name;
		this.ftp_addr = ftp_addr;
		this.ftp_subdirectory = ftp_subdirectory;
		this.ftp_port = ftp_port;
		this.ftp_anonymous = ftp_anonymous;
		this.ftp_login_name = ftp_login_name;
		this.ftp_login_pwd = ftp_login_pwd;
		return this;
    }
}

class SmbInfo{
	constructor(index, smb_name, smd_addr, smb_subdirectory, smb_port, smb_anonymous, smb_login_name, smb_login_pwd)
	{
		if(smb_name === undefined)
		{
			throw new Error('smb_name is required')
		}
		if(smd_addr === undefined)
		{
			throw new Error('smd_addr is required')
		}
		if(smb_subdirectory === undefined)
		{
			throw new Error('smb_subdirectory is required')
		}
		if(smb_port === undefined)
		{
			throw new Error('smb_port is required')
		}
		if(smb_anonymous === undefined)
		{
			throw new Error('smb_anonymous is required')
		}
		if(smb_login_name === undefined)
		{
			throw new Error('smb_login_name is required')
		}
		if(smb_login_pwd === undefined)
		{
			throw new Error('smb_login_pwd is required')
		}
		this.index = index;
		this.smb_name = smb_name;
		this.smb_addr = smd_addr;
		this.smb_subdirectory = smb_subdirectory;
		this.smb_port = smb_port;
		this.smb_anonymous = smb_anonymous;
		this.smb_login_name = smb_login_name;
		this.smb_login_pwd = smb_login_pwd;
		return this;
    }
}

export function addEmailAddr(mail_data)
    {
       	return add_email_addr(mail_data);
    }
export function getEmailAddr(index)
    {
        return get_email_addr(index);
    }
export function getEmailAddrList()
    {
    	return get_email_addr_list();
    }
export function getEmailAddrNum()
	{
		return get_email_addr_num();
	}
export function isEmailAddrFull()
	{
		return is_email_addr_full();
	}
export function modifyEmailAddr(mail_data)
	{
		return modify_email_addr(mail_data);
	}
export function removeEmailAddr(index)
	{
		return remove_email_addr(index);
	}
    //group
export function addEmailToGroup(group_index, mail_index)
    {
       	return add_email_to_group(group_index, mail_index);
    }
export function creatEmailGroup(group_data)
    {
       	return creat_email_group(group_data);
    }
export function getEmailGroup(index)
    {
        return get_email_group(index);
    }
export function getEmailGroupList()
    {
    	return get_email_group_list();
    }
export function getEmailGroupNum()
	{
		return get_email_group_num();
	}
export function isEmailGroupFull()
	{
		return is_email_group_full();
	}
export function modifyEmailGroup(index, group_data)
	{
		return modify_email_group(index, group_data);
	}
export function removeEmailFromGroup(group_index, mail_index)
	{
		return remove_email_from_group(group_index, mail_index);
	}
export function removeEmailGroup(index)
	{
		let flag = remove_email_group(index);
		if(flag === 1)
		{
			return "EXIT_SUCCESS";
		}
		else
		{
			return "EXIT_FAILURE";
		}
	}

    //ftp
export function addFTPAddr(ftp_data)
    {
       	return add_ftp_addr(ftp_data);
    }
export function getFTPAddr(index)
    {
        return get_ftp_addr(index);
    }
export function getFTPAddrList()
    {
    	return get_ftp_addr_list();
    }
export function getFTPAddrNum()
	{
		return get_ftp_addr_num();
	}
export function isFTPAddrFull()
	{
		return is_ftp_addr_full();
	}
export function modifyFTPAddr(ftp_data)
	{
		return modify_ftp_addr(ftp_data);
	}
export function removeFTPAddr(index)
	{
		let flag = remove_ftp_addr(index);
		if(flag === 1)
		{
			return "EXIT_SUCCESS";
		}
		else
		{
			return "EXIT_FAILURE";
		}
	}
    //smb
export function addSMBAddr(smb_data)
    {
       	return add_smb_addr(smb_data);
    }
export function getSMBAddr(index)
    {
        return get_smb_addr(index);
    }
export function getSMBAddrList()
    {
    	return get_smb_addr_list();
    }
export function getSMBAddrNum()
	{
		return get_smb_addr_num();
	}
export function isSMBAddrFull()
	{
		return is_smb_addr_full();
	}
export function modifySMBAddr(smb_data)
	{
		return modify_smb_addr(smb_data);
	}
export function removeSMBAddr(index)
	{
		let flag = remove_smb_addr(index);
		if(flag === 1)
		{
			return "EXIT_SUCCESS";
		}
		else
		{
			return "EXIT_FAILURE";
		}
	}

globalThis.pedk.addressbook.MailInfo = MailInfo
globalThis.pedk.addressbook.GroupInfo = GroupInfo
globalThis.pedk.addressbook.FtpInfo = FtpInfo
globalThis.pedk.addressbook.SmbInfo = SmbInfo

globalThis.pedk.addressbook.addEmailAddr = addEmailAddr
globalThis.pedk.addressbook.getEmailAddr = getEmailAddr
globalThis.pedk.addressbook.getEmailAddrList = getEmailAddrList
globalThis.pedk.addressbook.getEmailAddrNum = getEmailAddrNum
globalThis.pedk.addressbook.isEmailAddrFull = isEmailAddrFull
globalThis.pedk.addressbook.modifyEmailAddr = modifyEmailAddr
globalThis.pedk.addressbook.removeEmailAddr = removeEmailAddr


globalThis.pedk.addressbook.addEmailToGroup = addEmailToGroup
globalThis.pedk.addressbook.creatEmailGroup = creatEmailGroup
globalThis.pedk.addressbook.getEmailGroup = getEmailGroup
globalThis.pedk.addressbook.getEmailGroupList = getEmailGroupList
globalThis.pedk.addressbook.getEmailGroupNum = getEmailGroupNum
globalThis.pedk.addressbook.isEmailGroupFull = isEmailGroupFull
globalThis.pedk.addressbook.modifyEmailGroup = modifyEmailGroup
globalThis.pedk.addressbook.removeEmailFromGroup = removeEmailFromGroup
globalThis.pedk.addressbook.removeEmailGroup = removeEmailGroup

globalThis.pedk.addressbook.addFTPAddr = addFTPAddr
globalThis.pedk.addressbook.getFTPAddr = getFTPAddr
globalThis.pedk.addressbook.getFTPAddrList = getFTPAddrList
globalThis.pedk.addressbook.getFTPAddrNum = getFTPAddrNum
globalThis.pedk.addressbook.isFTPAddrFull = isFTPAddrFull
globalThis.pedk.addressbook.modifyFTPAddr = modifyFTPAddr
globalThis.pedk.addressbook.removeFTPAddr = removeFTPAddr

globalThis.pedk.addressbook.addSMBAddr = addSMBAddr
globalThis.pedk.addressbook.getSMBAddr = getSMBAddr
globalThis.pedk.addressbook.getSMBAddrList = getSMBAddrList
globalThis.pedk.addressbook.getSMBAddrNum = getSMBAddrNum
globalThis.pedk.addressbook.isSMBAddrFull = isSMBAddrFull
globalThis.pedk.addressbook.modifySMBAddr = modifySMBAddr
globalThis.pedk.addressbook.removeSMBAddr = removeSMBAddr





