/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_mgr_service.c
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2023-07-13
 * @brief event manager service
 */
#include <stdio.h>
#include <stdlib.h>
#include <sys/time.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>
#include <pthread.h>
#include <semaphore.h>

#include "pol/pol_types.h"
#include "pol/pol_string.h"
#include "pol/pol_list.h"
#include "pol/pol_mem.h"
#include "pol/pol_io.h"
#include "pol/pol_time.h"
#include "cbinder/cbinder.h"
#include "event_mgr_typedef.h"
#include "event_mgr_private.h"
#include "event_mgr_service.h"

typedef uint32_t TABLE_T;

struct module_context
{
    struct binder_death death;
    void*               target;
    int32_t             write_fd;
    char**              notify_cache;
    uint32_t*           cache_length;
    TABLE_T*            event_table;
    uint16_t            event_total;
    pthread_mutex_t     mutex;
};

struct dispatch_list_node
{
    struct list_head    list;

    uint32_t            sender_mid;
    uint32_t            disp_cmd;

    union
    {
        struct _reg
        {
            uint32_t*   event_array;
            uint32_t    event_count;
        } reg;

        struct _ntfy
        {
            uint32_t    event_type;
            uint32_t    length;
            char*       buffer;
        } ntfy;
    } u;
};

struct evt_mgr_svc
{
    struct module_context       module_ctx[EVT_MODULE_ENUM_MAX];
    struct dispatch_list_node   dispatch_head[EVT_LEVEL_DEFAULT + 1];
    pthread_mutex_t             dispatch_mutex;
    sem_t                       dispatch_sem;
    uint8_t                     cache_flag;
};

static const char* module_name(uint32_t module_id)
{
    const char* name;

#define case_out(mid, num, max) case mid: name = #mid; break;
    switch ( module_id )
    {
        EVENT_MODULE_TABLE(case_out);
        default:
        EVT_LOG("[L%d] Unknown module(%x)", __LINE__, module_id);
        name = "Unknown";
        break;
    }
#undef  case_out

    return name;
}

static int32_t send_to_module(int32_t fd, const char* data, uint32_t length)
{
    uint32_t    ntotal;
    ssize_t     nwrite;

    for ( ntotal = 0; ntotal < length; ntotal += nwrite )
    {
        nwrite = write(fd, data + ntotal, (size_t)(length - ntotal));
        if ( nwrite < 0 )
        {
            EVT_ERR("[L%d] write failed: %d<%s>", __LINE__, errno, strerror(errno));
            return -1;
        }
    }

    return 0;
}

static void module_exit(struct binder_state* bs, void* ptr)
{
    struct module_context* mctx_ptr = (struct module_context *)ptr;

    EVT_LOG("[L%d] module disconnect, target(%d)", __LINE__, (int32_t)mctx_ptr->target);
    pthread_mutex_lock(&mctx_ptr->mutex);
    if ( mctx_ptr->target != NULL )
    {
        if ( mctx_ptr->write_fd >= 0 )
        {
            close(mctx_ptr->write_fd);
            mctx_ptr->write_fd = -1;
        }
        binder_release(bs, mctx_ptr->target);
        mctx_ptr->target = NULL;
    }
    pthread_mutex_unlock(&mctx_ptr->mutex);
}

static void release_notify_cache(EVT_MGR_SVC_S* thiz)
{
    uint32_t i, j;

    for ( i = 0; i < EVT_MODULE_ENUM_MAX; ++i )
    {
        for ( j = 0; j < thiz->module_ctx[i].event_total; ++j )
        {
            if ( thiz->module_ctx[i].notify_cache[j] != NULL )
            {
                pi_free(thiz->module_ctx[i].notify_cache[j]);
            }
        }
        pi_free(thiz->module_ctx[i].notify_cache);
        pi_free(thiz->module_ctx[i].cache_length);
    }
    EVT_LOG("release notify cache");
}

EVT_MGR_SVC_S* event_mgr_svc_create(void)
{
    struct module_context*  mctx_ptr = NULL;
    EVT_MGR_SVC_S*          svc = NULL;
    int32_t                 ret = 0;
    int32_t                 i;

    svc = (EVT_MGR_SVC_S *)pi_zalloc(sizeof(EVT_MGR_SVC_S));
    RETURN_VAL_IF(svc == NULL, EVT_ERR, NULL);

    do
    {
        for ( i = 0; i < EVT_MODULE_ENUM_MAX; ++i )
        {
            mctx_ptr = &(svc->module_ctx[i]);
#define case_out(mid, num, max) case mid: mctx_ptr->event_total = EVT_INDEX(max); break;
            /* get the total number of events of the current module. */
            switch ( i )
            {
                EVENT_MODULE_TABLE(case_out);
                default:
                EVT_LOG("[L%d] Unknown module(%x)", __LINE__, i);
                mctx_ptr->event_total = 0;
                break;
            }
#undef  case_out

            /* create event table of the current module. use each bit to record whether each module has registered this event.*/
            mctx_ptr->event_table = (TABLE_T *)pi_zalloc(mctx_ptr->event_total * sizeof(TABLE_T));
            if ( mctx_ptr->event_table == NULL )
            {
                EVT_ERR("alloc event_table failed: %d<%s>", errno, strerror(errno));
                ret = ERR_EVT_MGR_MEMORY;
                break;
            }

            /* create notify cache table of the current module. backup the events that has been notified when the system start. */
            mctx_ptr->cache_length = (uint32_t *)pi_zalloc(mctx_ptr->event_total * sizeof(uint32_t));
            if ( mctx_ptr->cache_length == NULL )
            {
                EVT_ERR("alloc cache_length failed: %d<%s>", errno, strerror(errno));
                ret = ERR_EVT_MGR_MEMORY;
                break;
            }

            mctx_ptr->notify_cache = (char **)pi_zalloc(mctx_ptr->event_total * sizeof(char *));
            if ( mctx_ptr->notify_cache == NULL )
            {
                EVT_ERR("alloc notify_cache failed: %d<%s>", errno, strerror(errno));
                ret = ERR_EVT_MGR_MEMORY;
                break;
            }

            pthread_mutex_init(&mctx_ptr->mutex, NULL);
            mctx_ptr->write_fd = -1;
        }
        BREAK_IF(ret != 0, EVT_ERR);

        /* init dispatch_head and dispatch_sem */
        for ( i = 0; i <= EVT_LEVEL_DEFAULT; ++i )
        {
            pi_init_list_head(&svc->dispatch_head[i].list);
        }
        pthread_mutex_init(&svc->dispatch_mutex, NULL);
        sem_init(&svc->dispatch_sem, 0, 0);
        svc->cache_flag = 1;
    }
    while ( 0 );

    if ( ret != 0 )
    {
        EVT_ERR("[L%d] create service failed(%d)", __LINE__, ret);
        for ( i = 0; i < EVT_MODULE_ENUM_MAX; ++i )
        {
            mctx_ptr = &(svc->module_ctx[i]);
            if ( mctx_ptr->notify_cache != NULL )
            {
                pi_free(mctx_ptr->notify_cache);
            }
            if ( mctx_ptr->cache_length != NULL )
            {
                pi_free(mctx_ptr->cache_length);
            }
            if ( mctx_ptr->event_table != NULL )
            {
                pi_free(mctx_ptr->event_table);
            }
        }
        pi_free(svc);
        svc = NULL;
    }
    return svc;
}

void event_mgr_svc_start_loop(EVT_MGR_SVC_S* thiz)
{
    struct dispatch_list_node*  node = NULL;
    struct module_context*      mctx_ptr = NULL;
    time_t                      start  = pi_time(NULL);
    const char*                 buffer = NULL;
    uint32_t                    i, length = 0;
    int32_t                     ret = 0;
    uint16_t                    evt_idx = 0;
    uint8_t                     evt_mid = 0;
    TABLE_T                     flag = 1;

    RETURN_IF(thiz == NULL, EVT_ERR);

    while ( 1 )
    {
        sem_wait(&thiz->dispatch_sem);

        /* 遍历dispatch队列，优先从高优先级对别取dispatch_node并处理 */
        for ( i = 0; i <= EVT_LEVEL_DEFAULT && (node = pi_list_first_entry_or_null(&thiz->dispatch_head[i].list, struct dispatch_list_node, list)) == NULL; ++i );

        if ( node == NULL )
        {
            EVT_LOG("[L%d] dispatch list all empty!!!", __LINE__);
            continue;
        }

        switch ( node->disp_cmd )
        {
            case EVENT_MGR_CMD_REGISTER:
            {
                for ( i = 0; i < node->u.reg.event_count; ++i )
                {
                    evt_mid = EVT_MODULE(node->u.reg.event_array[i]);
                    evt_idx = EVT_INDEX(node->u.reg.event_array[i]);

                    if ( evt_mid >= EVT_MODULE_ENUM_MAX || evt_idx >= thiz->module_ctx[evt_mid].event_total )
                    {
                        EVT_ERR("module(%s) invalid register: mid(%u) idx(%u), ignored!", module_name(node->sender_mid), evt_mid, evt_idx);
                        continue;
                    }

                    if ( EVT_IS_OWN(node->u.reg.event_array[i]) && evt_mid != node->sender_mid )
                    {
                        EVT_LOG("module(%s) should not register event(0x%08x), ignored!", module_name(node->sender_mid), node->u.reg.event_array[i]);
                    }
                    else
                    {
                        thiz->module_ctx[evt_mid].event_table[evt_idx] |= (flag << node->sender_mid);
                    }

                    if ( thiz->cache_flag == 0 )
                    {
                        continue;
                    }

                    /* 系统启动前3分钟，客户端注册event时，检查缓存中的事件立即推送 */
                    buffer = thiz->module_ctx[evt_mid].notify_cache[evt_idx];
                    length = thiz->module_ctx[evt_mid].cache_length[evt_idx];
                    if ( buffer != NULL )
                    {
                        mctx_ptr = &(thiz->module_ctx[node->sender_mid]);
                        pthread_mutex_lock(&mctx_ptr->mutex);
                        if ( mctx_ptr->write_fd >= 0 )
                        {
                            ret = send_to_module(mctx_ptr->write_fd, buffer, length);
                            if ( ret < 0 )
                            {
                                EVT_ERR("[L%d] send event(%08x) to module(%s) failed", __LINE__, node->u.reg.event_array[i], module_name(node->sender_mid));
                            }
                        }
                        else
                        {
                            EVT_ERR("[L%d] module(%s) disconnected, target(%d)", __LINE__, module_name(node->sender_mid), (int32_t)mctx_ptr->target);
                            pthread_mutex_unlock(&mctx_ptr->mutex);
                            break;
                        }
                        pthread_mutex_unlock(&mctx_ptr->mutex);
                    }
                }

                pi_free(node->u.reg.event_array);
                break;
            }
            case EVENT_MGR_CMD_UNREGISTER:
            {
                for ( i = 0; i < node->u.reg.event_count; ++i )
                {
                    evt_mid = EVT_MODULE(node->u.reg.event_array[i]);
                    evt_idx = EVT_INDEX(node->u.reg.event_array[i]);

                    if ( evt_mid < EVT_MODULE_ENUM_MAX && evt_idx < thiz->module_ctx[evt_mid].event_total )
                    {
                        thiz->module_ctx[evt_mid].event_table[evt_idx] &= (~(flag << node->sender_mid));
                    }
                }

                pi_free(node->u.reg.event_array);
                break;
            }
            case EVENT_MGR_CMD_NOTIFY:
            {
                evt_mid = EVT_MODULE(node->u.ntfy.event_type);
                evt_idx = EVT_INDEX(node->u.ntfy.event_type);

                buffer = node->u.ntfy.buffer;
                length = node->u.ntfy.length;

                if ( EVT_IS_OWN(node->u.ntfy.event_type) )
                {
                    if ( evt_mid == node->sender_mid )
                    {
                        EVT_LOG("[L%d] module(%s) should not notify event(0x%08x)", __LINE__, module_name(node->sender_mid), node->u.ntfy.event_type);
                        continue;
                    }

                    mctx_ptr = &(thiz->module_ctx[evt_mid]);
                    pthread_mutex_lock(&mctx_ptr->mutex);
                    if ( mctx_ptr->write_fd >= 0 && (thiz->module_ctx[evt_mid].event_table[evt_idx] & (flag << evt_mid)) )
                    {
                        ret = send_to_module(mctx_ptr->write_fd, buffer, length);
                        if ( ret < 0 )
                        {
                            EVT_ERR("[L%d] send buffer to module(%s) failed", __LINE__, module_name(i));
                        }
                    }
                    pthread_mutex_unlock(&mctx_ptr->mutex);
                }
                else
                {
                    for ( i = 0; i < EVT_MODULE_ENUM_MAX; ++i )
                    {
                        if ( i == node->sender_mid )
                        {
                            continue; /* ignore the module itself */
                        }

                        mctx_ptr = &(thiz->module_ctx[i]);
                        pthread_mutex_lock(&mctx_ptr->mutex);
                        if ( mctx_ptr->write_fd >= 0 && (thiz->module_ctx[evt_mid].event_table[evt_idx] & (flag << i)) )
                        {
                            ret = send_to_module(mctx_ptr->write_fd, buffer, length);
                            if ( ret < 0 )
                            {
                                EVT_ERR("[L%d] send buffer to module(%s) failed", __LINE__, module_name(i));
                            }
                        }
                        pthread_mutex_unlock(&mctx_ptr->mutex);
                    }
                }

                if ( thiz->cache_flag )
                {
                    if ( thiz->module_ctx[evt_mid].notify_cache[evt_idx] != NULL )
                    {
                        pi_free(thiz->module_ctx[evt_mid].notify_cache[evt_idx]);
                    }
                    thiz->module_ctx[evt_mid].notify_cache[evt_idx] = node->u.ntfy.buffer;
                    thiz->module_ctx[evt_mid].cache_length[evt_idx] = node->u.ntfy.length;
                }
                else
                {
                    pi_free(node->u.ntfy.buffer);
                }

                break;
            }
            default:
            {
                EVT_ERR("[L%d] invalid cmd(%u)", __LINE__, node->disp_cmd);
                break;
            }
        }

        pthread_mutex_lock(&thiz->dispatch_mutex);
        pi_list_del_entry(&(node->list));
        pthread_mutex_unlock(&thiz->dispatch_mutex);
        pi_free(node);

        /* release the notify cache after program start 10 minutes. */
        if ( thiz->cache_flag && pi_time(NULL) - start > 600 )
        {
            release_notify_cache(thiz);
            thiz->cache_flag = 0;
        }
    }

    return;
}

void event_mgr_svc_destroy(EVT_MGR_SVC_S* thiz)
{
    uint32_t i, j;

    if ( thiz != NULL )
    {
        if ( thiz->cache_flag == 0 )
        {
            for ( i = 0; i < EVT_MODULE_ENUM_MAX; ++i )
            {
                pi_free(thiz->module_ctx[i].event_table);
            }
        }
        else
        {
            for ( i = 0; i < EVT_MODULE_ENUM_MAX; ++i )
            {
                for ( j = 0; j < thiz->module_ctx[i].event_total; ++j )
                {
                    if ( thiz->module_ctx[i].notify_cache[j] != NULL )
                    {
                        pi_free(thiz->module_ctx[i].notify_cache[j]);
                    }
                }
                pi_free(thiz->module_ctx[i].notify_cache);
                pi_free(thiz->module_ctx[i].cache_length);
                pi_free(thiz->module_ctx[i].event_table);
            }
        }
        pi_free(thiz);
    }

    return;
}

int32_t event_mgr_svc_dispatch_connect(EVT_MGR_SVC_S* thiz, struct binder_state* bs, uint32_t module_id, int32_t write_fd, void* obj)
{
    struct module_context* mctx_ptr = NULL;
    int32_t tmp_flags;
    int32_t ret = 0;

    RETURN_VAL_IF(thiz == NULL || module_id >= EVT_MODULE_ENUM_MAX || write_fd < 0 || obj == NULL, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);

    mctx_ptr = &thiz->module_ctx[module_id];

    pthread_mutex_lock(&mctx_ptr->mutex);
    if ( mctx_ptr->write_fd == -1 )
    {
        tmp_flags = fcntl(write_fd, F_GETFL, 0);
        tmp_flags |= O_NONBLOCK;
        fcntl(write_fd, F_SETFL, tmp_flags);

        mctx_ptr->death.func = module_exit;
        mctx_ptr->death.ptr  = mctx_ptr;
        mctx_ptr->target     = obj;
        mctx_ptr->write_fd   = write_fd;
        binder_acquire(bs, obj);
        binder_link_to_death(bs, obj, &mctx_ptr->death);
        EVT_LOG("module(%s) connected, target: %d", module_name(module_id), (int32_t)mctx_ptr->target);
    }
    else
    {
        ret = ERR_EVT_MGR_MODULE_EXIST;
    }
    pthread_mutex_unlock(&mctx_ptr->mutex);

    return ret;
}

int32_t event_mgr_svc_dispatch_disconnect(EVT_MGR_SVC_S* thiz, struct binder_state* bs, uint32_t module_id)
{
    RETURN_VAL_IF(thiz == NULL || module_id >= EVT_MODULE_ENUM_MAX, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);

    module_exit(bs, &thiz->module_ctx[module_id]);

    return 0;
}

int32_t event_mgr_svc_dispatch_register(EVT_MGR_SVC_S* thiz, uint32_t module_id, uint32_t* event_array, uint32_t event_count, uint32_t cmd)
{
    struct dispatch_list_node* node = NULL;

    RETURN_VAL_IF(thiz == NULL || module_id >= EVT_MODULE_ENUM_MAX, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);
    RETURN_VAL_IF(event_array == NULL || event_count == 0, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);

    node = (struct dispatch_list_node *)pi_zalloc(sizeof(struct dispatch_list_node));
    RETURN_VAL_IF(node == NULL, EVT_ERR, ERR_EVT_MGR_MEMORY);

    node->u.reg.event_array = (uint32_t *)pi_zalloc(event_count * sizeof(uint32_t));
    if ( node->u.reg.event_array == NULL )
    {
        pi_free(node);
        return ERR_EVT_MGR_MEMORY;
    }

    node->sender_mid        = module_id;
    node->disp_cmd          = cmd;
    memcpy(node->u.reg.event_array, event_array, event_count * sizeof(uint32_t));
    node->u.reg.event_count = event_count;

    pthread_mutex_lock(&thiz->dispatch_mutex);
    pi_list_add_tail(&node->list, &thiz->dispatch_head[0].list);
    pthread_mutex_unlock(&thiz->dispatch_mutex);

    /* activate the main looper */
    sem_post(&thiz->dispatch_sem);

    return 0;
}

int32_t event_mgr_svc_dispatch_notify(EVT_MGR_SVC_S* thiz, uint32_t module_id, uint32_t event_type, char* buffer, uint32_t length)
{
    struct dispatch_list_node* node = NULL;
    uint8_t  evt_mid = EVT_MODULE(event_type);
    uint8_t  evt_lvl = EVT_LEVEL(event_type);
    uint16_t evt_idx = EVT_INDEX(event_type);

    if ( evt_lvl < EVT_LEVEL_DEFAULT )
    {
        EVT_LOG("[L%d] priority process event(0x%08x) from(%s)", __LINE__, event_type, module_name(module_id));
    }

    RETURN_VAL_IF(thiz == NULL || module_id >= EVT_MODULE_ENUM_MAX || buffer == NULL || length == 0, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);
    RETURN_VAL_IF(evt_mid >= EVT_MODULE_ENUM_MAX || evt_lvl > EVT_LEVEL_DEFAULT,                     EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);
    RETURN_VAL_IF(evt_idx >= thiz->module_ctx[evt_mid].event_total,                                  EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);

    node = (struct dispatch_list_node *)pi_zalloc(sizeof(struct dispatch_list_node));
    RETURN_VAL_IF(node == NULL, EVT_ERR, ERR_EVT_MGR_MEMORY);

    node->u.ntfy.buffer = (char *)pi_zalloc(length);
    if ( node->u.ntfy.buffer == NULL )
    {
        EVT_ERR("[L%d] alloc failed: %d<%s>", __LINE__, errno, strerror(errno));
        pi_free(node);
        return ERR_EVT_MGR_MEMORY;
    }

    node->sender_mid        = module_id;
    node->disp_cmd          = EVENT_MGR_CMD_NOTIFY;
    node->u.ntfy.event_type = event_type;
    memcpy(node->u.ntfy.buffer, buffer, length);
    node->u.ntfy.length     = length;

    pthread_mutex_lock(&thiz->dispatch_mutex);
    pi_list_add_tail(&node->list, &thiz->dispatch_head[evt_lvl].list);
    pthread_mutex_unlock(&thiz->dispatch_mutex);

    /* activate the main looper */
    sem_post(&thiz->dispatch_sem);

    return 0;
}
/**
 *@}
 */
