export function set_ScanHttpParams(scan_http_params)
{
    if (!scan_http_params || typeof scan_http_params !== "object") {
        return "EINVALIDPARAM";
    }

    // 将 headers 转换为普通对象
    let headers = {};

    // 检查并处理 headers
    if (scan_http_params.headers) {
        // 检查是否有 forEach 方法（类似 Headers 对象的特征）
        if (typeof scan_http_params.headers.forEach === 'function') {
            try {
                scan_http_params.headers.forEach((value, key) => {
                    headers[key] = value;
                });
            } catch (e) {
                console.log("Headers 转换失败，使用原始对象");
                headers = scan_http_params.headers;
            }
        } else if (typeof scan_http_params.headers === 'object') {
            // 如果是普通对象，直接使用
            headers = scan_http_params.headers;
        } else if (typeof scan_http_params.headers === 'string') {
            // 如果是字符串，尝试解析
            try {
                headers = JSON.parse(scan_http_params.headers);
            } catch (e) {
                headers = {};
            }
        }
    }

    const params = {
        url: scan_http_params.url || "",
        headers: headers,
        file_name_prefix: scan_http_params.file_name_prefix || "",
        custom_field: scan_http_params.custom_field || {}
    };

    // 添加调试日志
    console.log("处理后的参数:", JSON.stringify(params, null, 2));

    return set_scan_http_params(JSON.stringify(params));
}
export function get_ScanHttpParams()
{
    const result = get_scan_http_params();

    try {
        return JSON.parse(result); // 尝试将结果解析为 JSON 格式
    } catch (e) {
        return result; // 如果解析失败，返回原始字符串
    }

    //return get_scan_http_params();
}

class ScanHttpParam{
	setScanHttpParams(scan_http_params){
		return set_ScanHttpParams(scan_http_params);
	}
	getScanHttpParams(mode){
		return get_ScanHttpParams();
	}
}

globalThis.pedk.device.setting.ScanHttpParam = ScanHttpParam;
globalThis.pedk.device.setting.setScanHttpParams = set_ScanHttpParams;
globalThis.pedk.device.setting.getScanHttpParams = get_ScanHttpParams;

