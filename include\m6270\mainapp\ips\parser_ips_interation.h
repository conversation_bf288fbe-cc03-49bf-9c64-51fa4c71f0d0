/**
 * @copyright 2023 Shenzhen Pantum Technology Co.Ltd all rights reserved
 * @file parser_ips_interation.h
 * @addtogroup print_parser_module
 * @{
 * @addtogroup parser_ips
 * @date 2023-07-10
 * @brief ips
 */

#ifndef _PARSER_IPS_INTERATION_H_
#define _PARSER_IPS_INTERATION_H_

/*** Head file area ***************************************************/
#include "ips/print_ips_public_data.h"
#include "ips/ips_interation_public_data.h"
/**********************************************************************/

/*** Macro description area *******************************************/
/**********************************************************************/

/*** Enumerate description area ***************************************/
// ...
/**********************************************************************/

/*** Function description area ****************************************/
#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief app get machine speed
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern uint32_t print_ips_interation_param_get_machine_speed(void);

/**
 * @brief app get machine color
 * @param[in] void
 * @param[out] N/A
 * @return uint32_t
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern uint32_t print_ips_interation_param_get_machine_color(void);

/**
 * @brief the finisher is set ?
 * @param[in]  void
 * @param[out] pcValue 1-finisher install 0-finisher no install
 * @return void
 * @retval 1 - finisher install
 * @retval 0 - finisher no install
 * <AUTHOR>
 * @date 2024-04-15
 * @note  N/A
 */
extern uint8_t print_ips_interation_config_get_finisher_install(void);

/**
 * @brief print_ips_interation_udisk_paper_size_valid_check
 * @param[in] ips_paper_size
 * @param[out] N/A
 * @return int8_t
 * @retval 0: support
 * @retval -1: no support
 * <AUTHOR>
 * @date 2024-01-08
 * @note  N/A
 */
extern const int8_t print_ips_interation_udisk_paper_size_valid_check(const uint32_t ips_paper_size);

/**
 * @brief app get tray install status
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 0 - the tray uninstall
 * @retval 1 - the tray install
 * <AUTHOR>
 * @date 2024-04-16
 * @note  N/A
 */

extern int print_ips_interation_param_get_tray_install(const IPS_INPUT_TRAY_E ips_input_tray);

/**
 * @brief app get tray_in empty status
 * @param[in] void
 * @param[out] N/A
 * @return uint8_t
 * @retval  0 - empty
 * @retval  1 - not empty
 * <AUTHOR>
 * @date 2024-04-16
 * @note  N/A
 */
extern uint8_t print_ips_interation_param_get_tray_in_empty_status(      const IPS_INPUT_TRAY_E ips_input_tray);

/**
 * @brief 通过纸盒或者纸型信息
 * @param[in]  ips_input_tray 纸盒信息
 * @param[out] media_size_ptr
 * @param[out] custom_type
 * @param[out] width
 * @param[out] height
 * @return uint8_t
 * @retval 0 success
 * @retval 1 failure
 * <AUTHOR>
 * @date 2024-04-17
 * @note  N/A
 */
extern uint8_t print_ips_interation_param_ips_get_paper_size(const IPS_INPUT_TRAY_E ips_input_tray,
    IPS_MEDIA_SIZE_E *media_size_ptr,IPS_PRINT_CUSTOM_TYPE_E* custom_type,uint32_t *width,uint32_t *height);

/**
 * @brief get paper type
 * @param[in]  ips_input_tray ips_tray_input
 * @param[in]  ips_input_tray ips_paper_type
 * @param[out]   N/A
 * @return int8_t
 * @retval N/A
 * <AUTHOR>
 * @date 2024-05-16
 * @note N/A
 */
extern int8_t print_ips_interation_param_pass_tray_get_paper_type(uint32_t ips_tray_input, uint32_t *ips_paper_type);

/**
 * @brief  IPS 输出 band 数据时，调用此函数 \n
          该函数需要把 band 数据拷贝并发送给后续数据处理模块
 * @param[in]  ppage        - POQ page 指针
 * @param[in]  plane        - 图层信息( CMYK 中的那一层 )
 * @param[in]  page_data    - 数据区指针
 * @param[in]  num_bytes    - 数据区大小(字节数)
 * @param[out]  N/A
 * @return   PAGE_OUTPUT_QUEUE_STATUS_E 枚举
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern PAGE_OUTPUT_QUEUE_STATUS_E print_ips_interation_add_band_to_page_output_queue_page(
    unsigned int plane,char* page_data,unsigned int  num_bytes);

/**
 * @brief IPS 完成 band 数据数据时，调用此函数 \n
        该函数需要配置 page 参数，确保下一页面正常输出 \n
        还需要发送消息给后续处理模块，告之，页面数据完整结束
 * @param[in]  old_set - POQ set 的指针
 * @param[out]  N/A
 * @return PAGE_OUTPUT_QUEUE_STATUS_E
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern PAGE_OUTPUT_QUEUE_STATUS_E print_ips_interation_finished_adding_to_page_output_queue_page(void);


/**
 * @brief   page output queue - 缩写为 POQ，表示页面输出队列 \n
           set 表示一组 POQ 的集合 \n
           该函数用来创建 POQ SET, 由 IPS 模块调用 \n
           我们需要把这些配置参数保存起来
 * @param[in]  copies           - POQ set 的拷贝数
 * @param[in]  collate          - 是否非逐份
 * @param[in]  producing_app    - 数据的生产者，这里肯定是 IPS 模块了
 * @param[in]  incoming_format  - band 的格式
 * @param[in]  storage_format   - 数据的存储格式
 * @param[in]  freeFunc         - 释放函数，可以为 NULL
 * @param[in]  callback         - 回调函数，完成 POQ set 的提交时，或发生异常时调用
 * @param[out]  N/A
 * @return   pointer to a PageOutputQueueSet
 * @retval PAGE_OUTPUT_QUEUE_SET_P
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern uint32_t print_ips_interation_create_page_output_queue_set
(
    unsigned int                copies,
    int                     collate,
    PAGE_OUTPUT_QUEUE_APP_E          producing_app,
    PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E incoming_format,
    PAGE_OUTPUT_QUEUE_RASTER_FORMAT_E storage_format
);



/**
 * @brief  该函数用来输出 POQ SET 的页面数据, 由 IPS 模块调用 \n
          该函数调用时，说明 IPS 模块将要输出一页 POQ SET 的数据
 * @param[in]  set         - POQ set 的指针
 * @param[in]  desc        - 页面集的配置参数，都在这个结构体里面
 * @param[in]  page_number - 页号。目前每个页面集里只有一个页面，所以取值为 1
 * @param[out]  N/A
 * @return   POQ page 指针
 * @retval PAGE_OUTPUT_QUEUE_PAGE_P
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern uint32_t print_ips_interation_add_page_to_page_output_queue_set
(
    PAGE_OUTPUT_QUEUE_PAGE_DESC_S*    desc,
    unsigned int                      page_number
);

/**
 * @brief  完成 POQ SET 添加时，IPS 模块调用此函数，没啥实质用处
 * @param[in]  set         - POQ set 的指针
 * @param[out]  N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
extern void print_ips_interation_finished_adding_pages_to_page_output_queue_set(void);

/**
 * @brief  该函数用来删除 POQ SET, 由 IPS 模块调用 \n
          该函数调用时，说明 POQ SET 的数据已经完成输出，可以发送页面结束消息了
 * @param[in]  old_set - POQ set 的指针
 * @param[out]  N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_destroy_page_output_queue_set(void);

/**
 * @brief  获得 自动纸匣的实际纸匣和长短边，和固定纸匣的长短边
 * @param[in] ips_media_size
 * @param[in] ips_media_type
 * @param[in] ips_input_tray_original
 * @param[in] print_mode
 * @param[in] ips_bookbinding
 * @param[out] edge
 * @param[out] ips_input_tray_in
 * @return char
 * @retval 0: success
 * @retval -1: failure
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern char print_ips_interation_get_tray_in_and_edge(const IPS_MEDIA_SIZE_E ips_media_size,const IPS_MEDIA_TYPE_E ips_media_type,
        const IPS_INPUT_TRAY_E ips_input_tray_original, IPS_EDGE_E *edge, IPS_INPUT_TRAY_E *ips_input_tray_in,
        const IPS_PRINT_MODE_E print_mode, const IPS_BOOKBINDING_S ips_bookbinding);

/**
 * @brief get ips lib param
 * @param[in] void
 * @param[out] N/A
 * @return IPS_LIB_PARAM_S
 * @retval IPS_LIB_PARAM_S
 * <AUTHOR>
 * @date 2023-12-05
 * @note  N/A
 */
extern const IPS_LIB_PARAM_S print_ips_interation_param_get_ips_lib_param( void );

/**
 * @brief get custom paper size tabel
 * @param[in] void
 * @param[out] IPS_CUSTOM_TYPE_TABLE_S
 * @return IPS_CUSTOM_TYPE_TABLE_S
 * @retval IPS_CUSTOM_TYPE_TABLE_S
 * <AUTHOR>
 * @date 2024-11-28
 * @note  N/A
 */
extern const IPS_CUSTOM_TYPE_TABLE_S print_ips_interation_get_custom_size_table( void );

/**
 * @brief set pdf font missing warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_pdf_font_missing_warn(void);

/**
 * @brief set pdf encrypt warn
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_pdf_encrypt_warn(void);

/**
 * @brief set pdf file large warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_pdf_file_large_warn(void);

/**
 * @brief set pdf font invalid warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
extern void print_ips_interation_io_set_pdf_font_invalid_warn(void);

/**
 * @brief DO exception
 * @param[in] count
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-07-11
 * @note  N/A
 */
extern void print_ips_interation_ioSetDOEncryptWarn(int count);

/**
 * @brief IPS模块EOJ 该函数由 IPS 模块调用，在数据读取超时时调用
 * @param[in] type  0:只更新标志位，不上报超时状态。1:更新标志位，同时上报超时状态。
 * @param[out] N/A
 * @return  void
 * @retval  N/A
 * <AUTHOR>
 * @date 2024-05-28
 * @note  N/A
 */
extern void print_ips_interation_io_input_timeout(int type);

/**
 * @brief DO unsupport file foramt
 * @param[in] count
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_ioSetDOUnsupportedFileFormat(int count);

/**
 * @brief IPS模块的数据读取接口
 * @param[in] char *buffer    - 数据缓冲区指针
 * @param[in] int bufsize     - 数据缓冲区大小
 * @param[in] 将待处理数据放到缓冲区中。
 * @param[out] N/A
 * @return   需要IPS处理的数据长度 或 -1(没有数据)
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_read_data(char *buffer, int bufsize);

/**
 * @brief IPS模块的数据回写端口接口 \n
        经测试，回写的数据为 IPS 模块解析异常时的错误报告 \n
        所以，这里只从串口输出，不回写到 QIO 端口
 * @param[in] char *buffer    - 数据缓冲区指针
 * @param[in] int bufsize     - 数据缓冲区大小
 * @param[in] i将需要回复的数据放到缓冲区中。
 * @param[out] N/A
 * @return  成功回复的数据大小；(-1)表示失败
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_write_data(char *buffer, int bytes);

/**
 * @brief IPS 模块的 timeout 参数获取接口
 * @param[in]   int *timeout - timeout 参数返回值指针
 * @param[out] N/A
 * @return  0 - 成功
 * @retval OM_ERROR_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
extern OM_ERROR_E print_ips_interation_inf_get_timeout_value_int(int* timeout);

/**
 * @brief is air print pdf job
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_is_air_print_pdf_job(void);

/**
 * @brief air print pdf job read over
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_is_air_print_pdf_read_over(void);

/**
 * @brief get job type
 * @param[in] void
 * @param[out] N/A
 * @return unsigned long
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern unsigned long print_ips_interation_GetJobSettingType(void);

/**
 * @brief get USB dummy
 * @param[in] void
 * @param[out] N/A
 * @return  int
 * @retval 0 - FALSE
 * @retval 1 - TRUE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_get_USB_dummy(void);

/**
 * @brief io is txt data stream
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 1
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
extern int print_ips_interation_io_is_txt_data_stream(void);

/**
 * @brief io txt data  stream set timeout
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_txt_data_stream_set_timeout(void);

/**
 * @brief get qio error
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_get_qio_error(void);

/**
 * @brief io always in sniffer
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_always_in_sniffer(void);

/**
 * @brief  IPS模块EOJ 该函数由 IPS 模块调用，在解析到 EOJ 信息时调用
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_end_of_job(void);

/**
 * @brief  IPS模块是否在解析中 该函数由 IPS 模块调用
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval TRUE
 * @retval FALSE
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_is_ips_in_use(void);

/**
 * @brief  使IPS模块停止数据的读取
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_stop_read_data(void);

/**
 * @brief get job job id
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: without job id
 * @retval >0: job id
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
extern uint32_t print_ips_interation_param_get_job_id_value(void);

/**
 * @brief IPS模块EOJ 该函数由 IPS 模块调用，在数据读取超时时调用
 * @param[in] job_number
 * @param[out] N/A
 * @return  int
 * @retval  0 - 成功
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_post_job_end_msg(int job_number);

/**
 * @brief get system status for mono
 * @param[in] ips_render_mode 0: color 1:mono
 * @param[out] NA/
 * @return unsigned long
 * @retval 0:base on PC driver
 * @retval 1:mono
 * <AUTHOR>
 * @date 2023-12-05
 * @note  N/A
 */
extern unsigned long print_ips_interation_get_system_status_for_mono(int ips_render_mode);

/**
 * @brief IPS模块EOJ 该函数由 IPS 模块调用，在数据读取超时时调用
 * @param[in] name
 * @param[in] bytes
 * @param[in] job_id
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_post_job_pipelint_name(const char *name, int bytes, uint32_t job_id);

/**
 * @brief This flag is only callable from IPS and inferno.
 * @param[in] func
 * @param[out] N/A
 * @return returns IPS_CANCEL_ON or IPS_CANCEL_OFF (1 or 0)
 * @retval IPS_CANCEL_FLAG_E
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern IPS_CANCEL_FLAG_E print_ips_interation_inf_get_cancel_flag(const char* func);


/**
 * @brief get job job id
 * @param[in] timeout_flag
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-01-09
 * @note N/A
 */
extern void print_ips_interation_io_set_timeout_flag(int timeout_flag);

/**
 * @brief get job job id
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: without job id
 * @retval >0: job id
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
extern  int32_t print_ips_interation_get_timeout_flag(void);

/**
 * @brief start ips
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 0:not ready
 * @retval 1:ready
 * <AUTHOR>
 * @date 2024-12-11
 * @note  N/A
 */
extern uint32_t print_ips_interation_get_mfp_ready( void );

/**
 * @brief get memobj_id
 * @param[in]  void
 * @param[out] N/A
 * @return int32_t
 * @retval memobj_id
 * <AUTHOR>
 * @date 2025-1-11
 * @note  N/A
 */
extern  int32_t print_ips_interation_get_memobj_id(void);

/**
 * @brief plane data
 * @param[in]
 * @param[out]  N/A
 * @return   PAGE_OUTPUT_QUEUE_STATUS_E 枚举
 * @retval PAGE_OUTPUT_QUEUE_STATUS_E
 * <AUTHOR>
 * @date 2024-1-11
 * @note  N/A
 */
extern PAGE_OUTPUT_QUEUE_STATUS_E print_ips_interation_add_band_to_page_output_queue_page_new(int32_t band_id);

/**
 * @brief get udisk param/airprint param
 * @param[in] void
 * @param[out] N/A
 * @return IPS_UDISK_PARAM_S
 * @retval IPS_PRINT_PARAM_S
 * <AUTHOR>
 * @date 2024-01-08
 * @note  N/A
 */
extern const IPS_PRINT_PARAM_S print_ips_interation_get_udisk_print_param(void);

/**
 * @brief IPS模块的数据返还给端口，保留在rewind区域
 * @param[in]  char *buffer    - 数据缓冲区指针
 * @param[in]  int bufsize     - 数据缓冲区大小
 * @param[in]  将需要返还的数据放到缓冲区中。
 * @param[out] N/A
 * @return   成功返还的数据大小；(-1)表示失败
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_rewind_data(char *buffer, int bytes, int flag);

/**
 * @brief  使IPS模块停止数据的读取
 * @param[in] void
 * @param[out] N/A
 * @return int
 * @retval 0
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_stop_file_read_data(void);

/**
 * @brief IPS模块的文件数据读取接口
 * @param[in] char *buffer    - 数据缓冲区指针
 * @param[in] int bufsize     - 数据缓冲区大小
 * @param[in] 将待处理数据放到缓冲区中。
 * @param[out] N/A
 * @return   需要IPS处理的数据长度 或 -1(没有数据)
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
  */
extern int print_ips_interation_io_file_read_data(char *buffer, int bufsize);

/**
 * @brief 获取 保存prn数据页数
 * @param[in] void
 * @param[out] N/A
 * @return int 交叉打印第一遍prn页数
 * @retval
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_get_first_pass_prn_pages(void);

/**
 * @brief  获取 prn可读取标志
 * @param[in] void
 * @param[out] N/A
 * @return    read enable flag
 * @retval    0 disable
 * @retval    0 enable
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern int print_ips_interation_io_get_prn_read_enable_flag(void);

/**
 * @brief get sample param
 * @param[in]   N/A
 * @param[out]   N/A
 * @return IPS_SAMPLE_PARAM_S
 * @retval   sample param
 * <AUTHOR>
 * @date   2024-05-13
 * @note   N/A
 */
extern IPS_SAMPLE_PARAM_S print_ips_interation_param_get_sample_param(void);

/**
 * @brief get job suspend status
 * @param[in] void
 * @param[out] N/A
 * @return int32_t
 * @retval 0: normal
 * @retval >0: suspend
 * <AUTHOR>
 * @date 2024-07-09
 * @note N/A
 */
extern int32_t print_ips_interation_param_get_job_suspend_status(void);


/**
 * @brief set pdf encrypt warn
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_file_encrypt_warn(void);

/**
 * @brief set file large warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_file_large_warn(void);

/**
 * @brief set font missing warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_file_font_missing_warn(void);

/**
 * @brief set file unsupport warn
 * @param[in] void
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note  N/A
 */
extern void print_ips_interation_io_set_file_unsupport_warn(void);

/**
 * @brief 设置 数据再解析使能标志。
 * @param[in] flag - io 数据再解析使能标志
 * @param[out] N/A
 * @return  void
 * @retval N/A
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
extern void print_ips_interation_set_parse_again_flag(int flag);

/**
 * @brief 获取 数据再解析使能标志。
 * @param[in] void
 * @param[out] N/A
 * @return  int
 * @retval  int - io 数据再解析使能标志
 * <AUTHOR>
 * @date 2024-12-12
 * @note N/A
 */
extern int print_ips_interation_get_parse_again_flag(void);

/**
 * @brief answer suspend ack by ips
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-02-21
 * @note N/A
 */
extern void print_ips_interation_answer_suspend_ack_by_ips(void);

/**
 * @brief imagemem release
 * @param[in] void
 * @param[out] N/A
 * @return void
 * @retval N/A
 * <AUTHOR>
 * @date 2025-05-14
 * @note N/A
 */
extern void print_ips_interation_set_imagemem_release(void);


#ifdef __cplusplus
}
#endif

/**********************************************************************/

#endif
/**
 * @}
 */
