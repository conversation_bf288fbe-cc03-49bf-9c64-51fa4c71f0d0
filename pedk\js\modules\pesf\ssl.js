const errorMap = {
    "Invalid certificate": "Invalid certificate content",
    "Invalid private key": "Invalid private key password"
};

function installCert(callback, cert_content, private_key_content, private_key_export_flag, private_key_password) {
    // 参数类型检查
    if (typeof callback !== "function") {
        return "EINVALIDPARAM";
    }
    if (typeof cert_content !== "string" || cert_content.trim() === "") {
        callback("failure", "Invalid certificate content: must be a non-empty string");
        return "EINVALIDPARAM";
    }
    if (private_key_content !== null && typeof private_key_content !== "string") {
        callback("failure", "Invalid private key content: must be a string or null");
        return "EINVALIDPARAM";
    }
    if (typeof private_key_export_flag !== "boolean") {
        callback("failure", "Invalid export flag: must be a boolean value");
        return "EINVALIDPARAM";
    }
    if (private_key_password !== null && typeof private_key_password !== "string") {
        callback("failure", "Invalid private key password: must be a string or null");
        return "EINVALIDPARAM";
    }

    // 调用 C 接口，传递参数给 C 代码
    const result = install_cert(cert_content, private_key_content, private_key_export_flag, private_key_password);

    // 根据执行结果，回调通知状态
	try {
		// 尝试解析 result 作为 JSON
		const jsonResult = JSON.parse(result);

		const formattedResult = {
			"certificate path": jsonResult.cert_path,
			"private key path": jsonResult.private_key_path
		};
		// 成功解析，回调成功信息
		callback("success", JSON.stringify(formattedResult));
		return "EXIT_SUCCESS";
	} catch (e) {
		// 如果 result 是已知的错误类型
		if (errorMap[result]) {
			callback("failure", errorMap[result]);
			return "EXIT_FAILURE";
		} else {
			// 未知错误
			callback("failure", "Unknown error during certificate installation");
			return "EXIT_FAILURE";
		}
	}

}

globalThis.pedk.net.ssl = {};

globalThis.pedk.net.ssl.install_cert = installCert;


