/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file module_manager.h
 * @addtogroup modules
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief module_manager init 
 */
#ifndef __MODULE_MANAGER_H__
#define __MODULE_MANAGER_H__

#include "runtime/runtime.h"
#include "basic/log/log.h"

//模块上下文结构体
typedef struct ModuleContext{
    char* module_name;
    void (*module_init)();                ///<模块初始化。         调用时机：PeSF启动时，初始化各自子模块
    void (*module_release)();             ///<模块结束。           调用时机：PeSF结束运行前，清理各自子模块
    
    void (*module_instance)(PeSFRunTime *prt);       ///<模块实例化    调用时机：app获取运行时时，调用各自子模块的实例化函数。将模块名与module_instance返回的绑定，注册到运行时自己的哈希表中。
    void (*module_generalization)(PeSFRunTime *prt); ///<模块去实例化  调用时机：app释放运行时时，获取各自子模块的实例化数据，然后传递给子模块的module_generalization，子模块内部释放。
}ModuleContext;

//添加模块
/**
 * @brief   module context add
 * @param[in] *md_ctx :a heap space runtime handle
 * <AUTHOR> @date    2024-06-11
 */
void module_context_add(ModuleContext *md_ctx);

//查找模块
/**
 * @brief   module context find
 * @param[in] *module_name :module name
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
ModuleContext* module_context_find(char *module_name);

//删除模块
/**
 * @brief   module context del
 * @param[in] *module_name :module name
 * <AUTHOR> @date    2024-06-11
 */
void module_context_del(char *module_name);

//初始化全部模块
/**
 * @brief   modules init all
 * <AUTHOR> @date    2024-06-11
 */
void modules_init_all();

//释放全部模块
/**
 * @brief   modules release all
 * <AUTHOR> @date    2024-06-11
 */
void modules_release_all();

//实例化全部模块
/**
 * @brief   modules instance all
 * @param[in] *prt :pesf runtime run context
 * <AUTHOR> @date    2024-06-11
 */
void modules_instance_all(PeSFRunTime *prt);

//释放实例化全部模块
/**
 * @brief   modules generalization all
 * @param[in] *prt :pesf runtime run context
 * <AUTHOR> @date    2024-06-11
 */
void modules_generalization_all(PeSFRunTime *prt);

//实例化数据处理函数
/**
 * @brief   app process on front
 * @param[in] *prt :pesf runtime run context
 * @param[in] instance_data_name :module name
 * @param[in] data :data 
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t add_instance_data(PeSFRunTime *prt, char *instance_data_name, void *data);

/**
 * @brief   get instance data
 * @param[in] *prt :pesf runtime run context
 * @param[in] instance_data_name :module name
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
void* get_instance_data(PeSFRunTime *prt, char *instance_data_name);

/**
 * @brief   del instance data
 * @param[in] *prt :pesf runtime run context
 * @param[in] instance_data_name :module name
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
int32_t del_instance_data(PeSFRunTime *prt, char *instance_data_name);

//模块注册宏定义，只要在模块中调用此宏，则相当于模块注册，注册时机在main函数之前。
#define MODULE_REGISTER(module) \
static void __attribute__((constructor))module##_runtime_registe()\
{\
    module_context_add(&module);\
}

#endif //__MODULE_MANAGER_H__

/**
 * @}
 */

