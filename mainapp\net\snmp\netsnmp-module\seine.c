/*
 * @seine seine.c
 */

#include <net-snmp/net-snmp-config.h>
#include <net-snmp/net-snmp-includes.h>
#include <net-snmp/agent/net-snmp-agent-includes.h>
#include "snmpipc.h"
#include "seine.h"

static int seine_lock = 0;
static SNMP_MAP_S* seine_map = NULL;

void init_seine_string(void);

SEINE_HANDLER_STR(ptFirmwareVersion,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 1);
SEINE_HANDLER_INT(ptCardMemory,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 2);
SEINE_HANDLER_STR(ptEngineVersion,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 3);
SEINE_HANDLER_STR(ptSerialN<PERSON>ber,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 4);
SEINE_HANDLER_STR(ptManufacturerName,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 5);
SEINE_HANDLER_STR(ptProductDate,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 6);
SEINE_HANDLER_STR(ptProductName,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 7);
SEINE_HANDLER_STR(ptBootVersion,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 8);
SEINE_HANDLER_STR(ptKernelVersion,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 9);
SEINE_HANDLER_STR(ptMACAddress,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 10);
SEINE_HANDLER_INT(ptPrintColor,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 11);
SEINE_HANDLER_INT(ptPrintSpeed,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 12);
SEINE_HANDLER_STR(ptGeneralProduceConfig,                       1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 13);
SEINE_HANDLER_STR(ptDeviceMibMpsVer,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 14);
SEINE_HANDLER_INT(ptDeviceMibPrintControlEnable,                1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 15);
SEINE_HANDLER_STR(ptDeviceMibPrintControlVer,                   1, 3, 6, 1, 4, 1, 40093, 10, 1, 1, 16);

SEINE_HANDLER_INT(ptDeviceStatus,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 2, 1);

SEINE_HANDLER_INT(ptSleepTime,                                  1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 1);
SEINE_HANDLER_INT(ptPrintSpeedMode,                             1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 2);
SEINE_HANDLER_INT(ptJobTimeOut,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 3);

SEINE_HANDLER_INT(ptInkSavingSetting,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 1);
SEINE_HANDLER_INT(ptLanguageSetting,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 2);
SEINE_HANDLER_INT(ptRestoreFactorySetting,                      1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 3);
SEINE_HANDLER_INT(ptMutePrintSetting,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 4);
SEINE_HANDLER_INT(ptScreenBrightnessSetting,                    1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 5);
SEINE_HANDLER_INT(ptShutdownConditonSetting,                    1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 6);
SEINE_HANDLER_INT(ptShutdownDelaySetting,                       1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 7);
SEINE_HANDLER_INT(ptClesnFixingPageSetting,                     1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 8);
SEINE_HANDLER_INT(ptImageCheapSetting,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 9);

SEINE_HANDLER_INT(ptVolumeIndex,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 10, 1, 1);
SEINE_HANDLER_INT(ptVolumeSetting,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 10, 1, 2);

SEINE_HANDLER_INT(ptDataFormat,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 11);
SEINE_HANDLER_INT(ptTimeFormat,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 12);
SEINE_HANDLER_STR(ptDateTimeSetting,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 13);
SEINE_HANDLER_INT(ptTimezoneSetting,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 14);
SEINE_HANDLER_INT(ptOnlineUpdateSetting,                        1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 15);

SEINE_HANDLER_INT(ptWindowsLogInEnable,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 16, 1);
SEINE_HANDLER_INT(ptServerAuthentication,                       1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 16, 2);
SEINE_HANDLER_INT(ptWindowsLogIn,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 4, 16, 3);

SEINE_HANDLER_INT(ptTrayIndex,                                  1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 1, 1, 1);
SEINE_HANDLER_INT(ptTrayPlacingPaperTips,                       1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 1, 1, 2);
SEINE_HANDLER_INT(ptPaperSizeSetting,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 1, 1, 3);
SEINE_HANDLER_INT(ptPaperTypeSetting,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 1, 1, 4);

SEINE_HANDLER_INT(ptPaperSourceSetting,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 2, 1);
SEINE_HANDLER_INT(ptPrintingQuantitySetting,                    1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 2, 2);
SEINE_HANDLER_INT(ptPrintDuplexSetting,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 2, 3);
SEINE_HANDLER_INT(ptImageOrientationSetting,                    1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 2, 4);
SEINE_HANDLER_INT(ptPrintConcentrationSetting,                  1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 2, 5);
SEINE_HANDLER_INT(ptResolutionSetting,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 5, 2, 6);

SEINE_HANDLER_INT(ptWPSModeEnable,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 6, 1);
SEINE_HANDLER_INT(ptWirelessFrequencySetting,                   1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 6, 2);
SEINE_HANDLER_INT(ptWirelessDirectConnectSetting,               1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 6, 3);
SEINE_HANDLER_INT(ptEmailEncryptionSetting,                     1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 6, 4);
SEINE_HANDLER_INT(ptNetworkContactsEnable,                      1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 6, 5);

SEINE_HANDLER_INT(ptUserLoginEnable,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 7, 1);
SEINE_HANDLER_INT(ptPanelSessionTimeoutTimeSetting,             1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 7, 2);
SEINE_HANDLER_INT(ptHardwarePortsEnable,                        1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 7, 3);
SEINE_HANDLER_INT(ptForbidScanTOEmailSmbFtpEnable,              1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 7, 4);
SEINE_HANDLER_INT(ptWebEncryptionEnable,                        1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 7, 5);
SEINE_HANDLER_INT(ptMemoryResetSetting,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 3, 7, 6);

SEINE_HANDLER_INT(ptUsbConnectedFlag,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 4, 1, 1);
SEINE_HANDLER_INT(ptNWConnectedFlag,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 4, 2, 1);

SEINE_HANDLER_INT(ptPrintGeneralStatus,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 1);

SEINE_HANDLER_STR(ptToneindexK,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 1, 1);
SEINE_HANDLER_STR(ptToneindexC,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 1, 2);
SEINE_HANDLER_STR(ptToneindexM,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 1, 3);
SEINE_HANDLER_STR(ptToneindexY,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 1, 4);

SEINE_HANDLER_STR(ptTonerUnitK,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 2, 1);
SEINE_HANDLER_STR(ptTonerUnitC,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 2, 2);
SEINE_HANDLER_STR(ptTonerUnitM,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 2, 3);
SEINE_HANDLER_STR(ptTonerUnitY,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 2, 4);

SEINE_HANDLER_INT(ptTonerRemainK,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 3, 1);
SEINE_HANDLER_INT(ptTonerRemainC,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 3, 2);
SEINE_HANDLER_INT(ptTonerRemainM,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 3, 3);
SEINE_HANDLER_INT(ptTonerRemainY,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 3, 4);

SEINE_HANDLER_INT(ptTonerMaximumK,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 4, 1);
SEINE_HANDLER_INT(ptTonerMaximumC,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 4, 2);
SEINE_HANDLER_INT(ptTonerMaximumM,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 4, 3);
SEINE_HANDLER_INT(ptTonerMaximumY,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 4, 4);

SEINE_HANDLER_STR(ptTonerModelK,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 5, 1);
SEINE_HANDLER_STR(ptTonerModelC,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 5, 2);
SEINE_HANDLER_STR(ptTonerModelM,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 5, 3);
SEINE_HANDLER_STR(ptTonerModelY,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 5, 4);

SEINE_HANDLER_STR(ptTonerSerialK,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 6, 1);
SEINE_HANDLER_STR(ptTonerSerialC,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 6, 2);
SEINE_HANDLER_STR(ptTonerSerialM,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 6, 3);
SEINE_HANDLER_STR(ptTonerSerialY,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 6, 4);

SEINE_HANDLER_INT(ptTonerStateK,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 7, 1);
SEINE_HANDLER_INT(ptTonerStateC,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 7, 2);
SEINE_HANDLER_INT(ptTonerStateM,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 7, 3);
SEINE_HANDLER_INT(ptTonerStateY,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 7, 4);

SEINE_HANDLER_STR(ptTonerDescriptionK,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 8, 1);
SEINE_HANDLER_STR(ptTonerDescriptionC,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 8, 2);
SEINE_HANDLER_STR(ptTonerDescriptionM,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 8, 3);
SEINE_HANDLER_STR(ptTonerDescriptionY,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 2, 1, 8, 4);

SEINE_HANDLER_STR(ptCartridgeUnitK,                             1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 1, 1);
SEINE_HANDLER_STR(ptCartridgeUnitC,                             1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 1, 2);
SEINE_HANDLER_STR(ptCartridgeUnitM,                             1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 1, 3);
SEINE_HANDLER_STR(ptCartridgeUnitY,                             1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 1, 4);

SEINE_HANDLER_INT(ptCartridgeRemainK,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 2, 1);
SEINE_HANDLER_INT(ptCartridgeRemainC,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 2, 2);
SEINE_HANDLER_INT(ptCartridgeRemainM,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 2, 3);
SEINE_HANDLER_INT(ptCartridgeRemainY,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 2, 4);

SEINE_HANDLER_INT(ptCartridgeMaximumK,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 3, 1);
SEINE_HANDLER_INT(ptCartridgeMaximumC,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 3, 2);
SEINE_HANDLER_INT(ptCartridgeMaximumM,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 3, 3);
SEINE_HANDLER_INT(ptCartridgeMaximumY,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 3, 4);

SEINE_HANDLER_STR(ptCartridgeModelK,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 4, 1);
SEINE_HANDLER_STR(ptCartridgeModelC,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 4, 2);
SEINE_HANDLER_STR(ptCartridgeModelM,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 4, 3);
SEINE_HANDLER_STR(ptCartridgeModelY,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 4, 4);

SEINE_HANDLER_STR(ptCartridgeSerialK,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 5, 1);
SEINE_HANDLER_STR(ptCartridgeSerialC,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 5, 2);
SEINE_HANDLER_STR(ptCartridgeSerialM,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 5, 3);
SEINE_HANDLER_STR(ptCartridgeSerialY,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 5, 4);

SEINE_HANDLER_INT(ptCartridgeStateK,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 6, 1);
SEINE_HANDLER_INT(ptCartridgeStateC,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 6, 2);
SEINE_HANDLER_INT(ptCartridgeStateM,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 6, 3);
SEINE_HANDLER_INT(ptCartridgeStateY,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 3, 1, 6, 4);

SEINE_HANDLER_INT(ptWasterTonerState,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 4, 1, 1);
SEINE_HANDLER_INT(ptWasterTonerRemain,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 4, 1, 2);
SEINE_HANDLER_INT(ptWasterTonerMaximum,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 4, 1, 3);
SEINE_HANDLER_STR(ptWasterTonerModel,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 4, 1, 4);
SEINE_HANDLER_STR(ptWasterTonerSerial,                          1, 3, 6, 1, 4, 1, 40093, 10, 1, 5, 4, 1, 5);

SEINE_HANDLER_INT(ptCopyScanMode,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 6, 1);
SEINE_HANDLER_INT(ptCopyJobType,                                1, 3, 6, 1, 4, 1, 40093, 10, 1, 7, 2);

SEINE_HANDLER_INT(ptAlertSeverityLevel,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 8, 1, 1);
SEINE_HANDLER_INT(ptAlertGroup,                                 1, 3, 6, 1, 4, 1, 40093, 10, 1, 8, 1, 2);
SEINE_HANDLER_INT(ptAlertGroupIndex,                            1, 3, 6, 1, 4, 1, 40093, 10, 1, 8, 1, 3);
SEINE_HANDLER_INT(ptAlertLocation,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 8, 1, 4);
SEINE_HANDLER_INT(ptAlertCode,                                  1, 3, 6, 1, 4, 1, 40093, 10, 1, 8, 1, 5);
SEINE_HANDLER_STR(ptAlertdescription,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 8, 1, 6);

SEINE_HANDLER_INT(ptAlertNewSeverityLevel,                      1, 3, 6, 1, 4, 1, 40093, 10, 1, 9, 1, 1);
SEINE_HANDLER_INT(ptAlertNewGroup,                              1, 3, 6, 1, 4, 1, 40093, 10, 1, 9, 1, 2);
SEINE_HANDLER_INT(ptAlertNewGroupIndex,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 9, 1, 3);
SEINE_HANDLER_INT(ptAlertNewLocation,                           1, 3, 6, 1, 4, 1, 40093, 10, 1, 9, 1, 4);
SEINE_HANDLER_INT(ptAlertNewCode,                               1, 3, 6, 1, 4, 1, 40093, 10, 1, 9, 1, 5);
SEINE_HANDLER_STR(ptAlerNewdescription,                         1, 3, 6, 1, 4, 1, 40093, 10, 1, 9, 1, 6);


SEINE_HANDLER_INT(ptJobIndex,                                   1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 1);
SEINE_HANDLER_INT(ptJobID,                                      1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 2);
SEINE_HANDLER_INT(ptJobCurPrintptgeNumber,                      1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 3);
SEINE_HANDLER_INT(ptJobTray,                                    1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 4);
SEINE_HANDLER_INT(ptJobptperSize,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 5);
SEINE_HANDLER_INT(ptJobptperMedia,                              1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 6);
SEINE_HANDLER_INT(ptJobMemory,                                  1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 7);
SEINE_HANDLER_INT(ptJobVia,                                     1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 8);
SEINE_HANDLER_STR(ptJobOwner,                                   1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 9);
SEINE_HANDLER_INT(ptPrintJobID,                                 1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 10);
SEINE_HANDLER_INT(ptJobAliveFlag,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 1, 11);

SEINE_HANDLER_INT(ptScanJobIndex,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 1);
SEINE_HANDLER_INT(ptScanJobID,                                  1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 2);
SEINE_HANDLER_INT(ptJobCurScanpageNumber,                       1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 3);
SEINE_HANDLER_INT(ptScanJobTray,                                1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 4);
SEINE_HANDLER_INT(ptScanJobpaperSize,                           1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 5);
SEINE_HANDLER_INT(ptScanJobpaperMedia,                          1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 6);
SEINE_HANDLER_INT(ptScanJobMemory,                              1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 7);
SEINE_HANDLER_INT(ptScanJobVia,                                 1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 8);
SEINE_HANDLER_STR(ptScanJobOwner,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 9);
//SEINE_HANDLER_INT(ptScanJobID,                                1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 10);
SEINE_HANDLER_INT(ptScanJobAliveFlag,                           1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 2, 11);

SEINE_HANDLER_INT(ptCopyJobIndex,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 1);
SEINE_HANDLER_INT(ptCopyJobID,                                  1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 2);
SEINE_HANDLER_INT(ptJobCurCopypageNumber,                       1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 3);
SEINE_HANDLER_INT(ptCopyJobTray,                                1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 4);
SEINE_HANDLER_INT(ptCopyJobpaperSize,                           1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 5);
SEINE_HANDLER_INT(ptCopyJobpaperMedia,                          1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 6);
SEINE_HANDLER_INT(ptCopyJobMemory,                              1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 7);
SEINE_HANDLER_INT(ptCopyJobVia,                                 1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 8);
SEINE_HANDLER_STR(ptCopyJobOwner,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 9);
//SEINE_HANDLER_INT(ptCopyJobID,                                1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 10);
SEINE_HANDLER_INT(ptCopyJobAliveFlag,                           1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 3, 11);

SEINE_HANDLER_INT(ptFaxJobIndex,                                1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 1);
SEINE_HANDLER_INT(ptFaxJobID,                                   1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 2);
SEINE_HANDLER_INT(ptJobCurFaxpageNumber,                        1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 3);
SEINE_HANDLER_INT(ptFaxJobTray,                                 1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 4);
SEINE_HANDLER_INT(ptFaxJobpaperSize,                            1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 5);
SEINE_HANDLER_INT(ptFaxJobpaperMedia,                           1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 6);
SEINE_HANDLER_INT(ptFaxJobMemory,                               1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 7);
SEINE_HANDLER_INT(ptFaxJobVia,                                  1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 8);
SEINE_HANDLER_STR(ptFaxJobOwner,                                1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 9);
//SEINE_HANDLER_INT(ptFaxJobID,                                 1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 10);
SEINE_HANDLER_INT(ptFaxJobAliveFlag,                            1, 3, 6, 1, 4, 1, 40093, 10, 2, 1, 4, 11);

SEINE_HANDLER_INT(ptEngineCounter,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 1);
SEINE_HANDLER_INT(ptPrintCounterTotal,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 2);
SEINE_HANDLER_INT(ptPrintCounterColor,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 3);
SEINE_HANDLER_INT(ptPrintCounterMono,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 4);
SEINE_HANDLER_INT(ptPrintCounterDuplex,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 5);
SEINE_HANDLER_INT(ptPrintCounterA3,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 6);
SEINE_HANDLER_INT(ptPrintCounterA4,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 7);
SEINE_HANDLER_INT(ptPrintCounterA5,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 8);
SEINE_HANDLER_INT(ptPrintCounterB4,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 9);
SEINE_HANDLER_INT(ptPrintCounterJISB5,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 10);
SEINE_HANDLER_INT(ptPrintCounterIOSB5,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 11);
SEINE_HANDLER_INT(ptPrintCounter11x17,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 12);
SEINE_HANDLER_INT(ptPrintCounterLetter,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 13);
SEINE_HANDLER_INT(ptPrintCounterLegal,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 14);
SEINE_HANDLER_INT(ptPrintCounterFolio,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 15);
SEINE_HANDLER_INT(ptPrintCounterOficio,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 16);
SEINE_HANDLER_INT(ptPrintCounterExec,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 17);
SEINE_HANDLER_INT(ptPrintCounterStatement,                      1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 18);
SEINE_HANDLER_INT(ptPrintCounter8k,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 19);
SEINE_HANDLER_INT(ptPrintCounter16k,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 20);
SEINE_HANDLER_INT(ptPrintCounterUserdef,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 21);
SEINE_HANDLER_INT(ptPrintCounterA6,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 22);
SEINE_HANDLER_INT(ptPrintCounterEnv10,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 23);
SEINE_HANDLER_INT(ptPrintCounterEnvMon,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 24);
SEINE_HANDLER_INT(ptPrintCounterEnvC6,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 25);
SEINE_HANDLER_INT(ptPrintCounterEnvC5,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 26);
SEINE_HANDLER_INT(ptPrintCounterEnvDL,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 27);
SEINE_HANDLER_INT(ptPrintCounterPosterCard,                     1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 28);
SEINE_HANDLER_INT(ptPrintCounterEquPages,                       1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 29);
SEINE_HANDLER_INT(ptPrintCounterColor_A3,                       1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 30);
SEINE_HANDLER_INT(ptPrintCounterMono_A3,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 1, 31);

SEINE_HANDLER_INT(ptScanCounterTotal,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 1);
SEINE_HANDLER_INT(ptScanCounterColor,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 2);
SEINE_HANDLER_INT(ptScanCounterMono,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 3);
SEINE_HANDLER_INT(ptScanCounterDuplex,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 4);
SEINE_HANDLER_INT(ptScanCounterA3,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 5);
SEINE_HANDLER_INT(ptScanCounterA4,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 6);
SEINE_HANDLER_INT(ptScanCounterA5,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 7);
SEINE_HANDLER_INT(ptScanCounterB4,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 8);
SEINE_HANDLER_INT(ptScanCounterJISB5,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 9);
SEINE_HANDLER_INT(ptScanCounterIOSB5,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 10);
SEINE_HANDLER_INT(ptScanCounter11x17,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 11);
SEINE_HANDLER_INT(ptScanCounterLetter,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 12);
SEINE_HANDLER_INT(ptScanCounterLegal,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 13);
SEINE_HANDLER_INT(ptScanCounterFolio,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 14);
SEINE_HANDLER_INT(ptScanCounterOficio,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 15);
SEINE_HANDLER_INT(ptScanCounterExec,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 16);
SEINE_HANDLER_INT(ptScanCounterStatement,                       1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 17);
SEINE_HANDLER_INT(ptScanCounter8k,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 18);
SEINE_HANDLER_INT(ptScanCounter16k,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 19);
SEINE_HANDLER_INT(ptScanCounterUserdef,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 20);
SEINE_HANDLER_INT(ptScanCounterA6,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 21);
SEINE_HANDLER_INT(ptScanCounterEnv10,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 22);
SEINE_HANDLER_INT(ptScanCounterEnvMon,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 23);
SEINE_HANDLER_INT(ptScanCounterEnvC6,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 24);
SEINE_HANDLER_INT(ptScanCounterEnvC5,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 25);
SEINE_HANDLER_INT(ptScanCounterEnvDL,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 26);
SEINE_HANDLER_INT(ptScanCounterPosterCard,                      1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 27);
SEINE_HANDLER_INT(ptScanCounterEquPages,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 28);
SEINE_HANDLER_INT(ptScanCounterColor_A3,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 29);
SEINE_HANDLER_INT(ptScanCounterMono_A3,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 2, 30);

SEINE_HANDLER_INT(ptCopyCounterTotal,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 1);
SEINE_HANDLER_INT(ptCopyCounterColor,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 2);
SEINE_HANDLER_INT(ptCopyCounterMono,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 3);
SEINE_HANDLER_INT(ptCopyCounterDuplex,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 4);
SEINE_HANDLER_INT(ptCopyCounterA3,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 5);
SEINE_HANDLER_INT(ptCopyCounterA4,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 6);
SEINE_HANDLER_INT(ptCopyCounterA5,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 7);
SEINE_HANDLER_INT(ptCopyCounterB4,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 8);
SEINE_HANDLER_INT(ptCopyCounterJISB5,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 9);
SEINE_HANDLER_INT(ptCopyCounterIOSB5,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 10);
SEINE_HANDLER_INT(ptCopyCounter11x17,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 11);
SEINE_HANDLER_INT(ptCopyCounterLetter,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 12);
SEINE_HANDLER_INT(ptCopyCounterLegal,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 13);
SEINE_HANDLER_INT(ptCopyCounterFolio,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 14);
SEINE_HANDLER_INT(ptCopyCounterOficio,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 15);
SEINE_HANDLER_INT(ptCopyCounterExec,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 16);
SEINE_HANDLER_INT(ptCopyCounterStatement,                       1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 17);
SEINE_HANDLER_INT(ptCopyCounter8k,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 18);
SEINE_HANDLER_INT(ptCopyCounter16k,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 19);
SEINE_HANDLER_INT(ptCopyCounterUserdef,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 20);
SEINE_HANDLER_INT(ptCopyCounterA6,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 21);
SEINE_HANDLER_INT(ptCopyCounterEnv10,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 22);
SEINE_HANDLER_INT(ptCopyCounterEnvMon,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 23);
SEINE_HANDLER_INT(ptCopyCounterEnvC6,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 24);
SEINE_HANDLER_INT(ptCopyCounterEnvC5,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 25);
SEINE_HANDLER_INT(ptCopyCounterEnvDL,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 26);
SEINE_HANDLER_INT(ptCopyCounterPosterCard,                      1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 27);
SEINE_HANDLER_INT(ptCopyCounterEquPages,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 28);
SEINE_HANDLER_INT(ptCopyCounterColor_A3,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 29);
SEINE_HANDLER_INT(ptCopyCounterMono_A3,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 3, 30);

SEINE_HANDLER_INT(ptFaxCounterTotal,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 1);
SEINE_HANDLER_INT(ptFaxCounterColor,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 2);
SEINE_HANDLER_INT(ptFaxCounterMono,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 3);
SEINE_HANDLER_INT(ptFaxCounterDuplex,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 4);
SEINE_HANDLER_INT(ptFaxCounterA3,                               1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 5);
SEINE_HANDLER_INT(ptFaxCounterA4,                               1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 6);
SEINE_HANDLER_INT(ptFaxCounterA5,                               1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 7);
SEINE_HANDLER_INT(ptFaxCounterB4,                               1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 8);
SEINE_HANDLER_INT(ptFaxCounterJISB5,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 9);
SEINE_HANDLER_INT(ptFaxCounterIOSB5,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 10);
SEINE_HANDLER_INT(ptFaxCounter11x17,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 11);
SEINE_HANDLER_INT(ptFaxCounterLetter,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 12);
SEINE_HANDLER_INT(ptFaxCounterLegal,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 13);
SEINE_HANDLER_INT(ptFaxCounterFolio,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 14);
SEINE_HANDLER_INT(ptFaxCounterOficio,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 15);
SEINE_HANDLER_INT(ptFaxCounterExec,                             1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 16);
SEINE_HANDLER_INT(ptFaxCounterStatement,                        1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 17);
SEINE_HANDLER_INT(ptFaxCounter8k,                               1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 18);
SEINE_HANDLER_INT(ptFaxCounter16k,                              1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 19);
SEINE_HANDLER_INT(ptFaxCounterUserdef,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 20);
SEINE_HANDLER_INT(ptFaxCounterA6,                               1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 21);
SEINE_HANDLER_INT(ptFaxCounterEnv10,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 22);
SEINE_HANDLER_INT(ptFaxCounterEnvMon,                           1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 23);
SEINE_HANDLER_INT(ptFaxCounterEnvC6,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 24);
SEINE_HANDLER_INT(ptFaxCounterEnvC5,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 25);
SEINE_HANDLER_INT(ptFaxCounterEnvDL,                            1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 26);
SEINE_HANDLER_INT(ptFaxCounterPosterCard,                       1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 27);
SEINE_HANDLER_INT(ptFaxCounterEquPages,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 28);
SEINE_HANDLER_INT(ptFaxCounterColor_A3,                         1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 29);
SEINE_HANDLER_INT(ptFaxCounterMono_A3,                          1, 3, 6, 1, 4, 1, 40093, 10, 3, 4, 30);

SEINE_HANDLER_STRUCT(STATIC_STATUS_S,   ptQueryStaticFeature,   1, 3, 6, 1, 4, 1, 40093, 10, 4, 1);
SEINE_HANDLER_STRUCT(DYNAMIC_STATUS_S,  ptQueryDynamicFeature,  1, 3, 6, 1, 4, 1, 40093, 10, 4, 2);
SEINE_HANDLER_STRUCT(AUDIT_JOBS_INFO_S, ptQueryAuditJobsInfo,   1, 3, 6, 1, 4, 1, 40093, 10, 4, 3);
SEINE_HANDLER_STRUCT(TRC_INFO_S,        ptQueryTRC600Info,      1, 3, 6, 1, 4, 1, 40093, 10, 4, 4);
SEINE_HANDLER_STRUCT(TRC_INFO_S,        ptQueryTRC1200Info,     1, 3, 6, 1, 4, 1, 40093, 10, 4, 5);
SEINE_HANDLER_STRUCT(TRC_INFO_S,        ptQueryTRC2400Info,     1, 3, 6, 1, 4, 1, 40093, 10, 4, 6);

SEINE_HANDLER_STR(ptPrinterHostname,                            1, 3, 6, 1, 4, 1, 40093, 10, 5, 1, 1);
SEINE_HANDLER_INT(ptIpMethod,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 1, 2);

SEINE_HANDLER_INT(ptWiredConnectStatus,                         1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 1);
SEINE_HANDLER_INT(ptWiredIpMode,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 2);
SEINE_HANDLER_STR(ptWiredIPAddress,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 3, 1);
SEINE_HANDLER_STR(ptWiredSubnetMask,                            1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 3, 2);
SEINE_HANDLER_STR(ptWiredGateway,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 3, 3);
SEINE_HANDLER_STR(ptWiredDNSServerAddress,                      1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 3, 4);

SEINE_HANDLER_STR(ptWiredIpv6LinkLocalAddress,                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 1);
SEINE_HANDLER_INT(ptWiredIpv6ManuEnableFlag,                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 2);
SEINE_HANDLER_STR(ptWiredIpv6ManuConfigureAddr,                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 3);
SEINE_HANDLER_STR(ptWiredIpv6ManuGatewayAddr,                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 4);
SEINE_HANDLER_STR(ptWiredIpv6AutoConfigureAddr1,                1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 5);
SEINE_HANDLER_STR(ptWiredIpv6AutoGatewayAddr,                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 6);
SEINE_HANDLER_STR(ptWiredIpv6ManuConfigureAddrMark,             1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 7);
SEINE_HANDLER_STR(ptWiredIpv6AutoConfigureAddrMark1,            1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 4, 8);

SEINE_HANDLER_INT(ptWiredInterfaceRate,                         1, 3, 6, 1, 4, 1, 40093, 10, 5, 2, 5);

SEINE_HANDLER_INT(ptWirelessStatus,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 1);
SEINE_HANDLER_INT(ptWirelessIpMode,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 2);

SEINE_HANDLER_STR(ptWirelessIPAddress,                          1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 3, 1);
SEINE_HANDLER_STR(ptWirelessSubnetMask,                         1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 3, 2);
SEINE_HANDLER_STR(ptWirelessGateway,                            1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 3, 3);
SEINE_HANDLER_STR(ptWirelessDNSServerAddr,                      1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 3, 4);

SEINE_HANDLER_STR(ptWirelessIpv6LinkLocalAddress,               1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 1);
SEINE_HANDLER_INT(ptWirelessIpv6ManuEnableFlag,                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 2);
SEINE_HANDLER_STR(ptWirelessIpv6ManuConfigureAddr,              1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 3);
SEINE_HANDLER_STR(ptWirelessIpv6ManuGatewayAddr,                1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 4);
SEINE_HANDLER_STR(ptWirelessIpv6AutoConfigureAddr1,             1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 5);
SEINE_HANDLER_STR(ptWirelessIpv6AutoGatewayAddr,                1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 6);
SEINE_HANDLER_STR(ptWirelessIpv6ManuConfigureAddrMark,          1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 7);
SEINE_HANDLER_STR(ptWirelessIpv6AutoConfigureAddrMark1,         1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 4, 8);

SEINE_HANDLER_STR(ptWirelessSSID,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 5);
SEINE_HANDLER_INT(ptWirelessType,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 6);
SEINE_HANDLER_INT(ptEncryptionProtocol,                         1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 7);
SEINE_HANDLER_INT(ptWEPIndex,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 8);
SEINE_HANDLER_STR(ptWirelessWEPPassword,                        1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 9);
SEINE_HANDLER_STR(ptWirelessWPAPSKPassword,                     1, 3, 6, 1, 4, 1, 40093, 10, 5, 3, 10);

SEINE_HANDLER_INT(ptDHCPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 1, 1);
SEINE_HANDLER_INT(ptDHCPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 1, 2);
SEINE_HANDLER_INT(ptDHCPv6Enable,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 1, 3);

SEINE_HANDLER_INT(ptDHCPSPort,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 2, 1);
SEINE_HANDLER_INT(ptDHCPSEnable,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 2, 2);

SEINE_HANDLER_INT(ptHTTPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 1);
SEINE_HANDLER_INT(ptHTTPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 2);
SEINE_HANDLER_INT(ptHTTPNumLinks,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 3);
SEINE_HANDLER_INT(ptHTTPBytesRemaining,                         1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 4);
SEINE_HANDLER_INT(ptHTTPLinksIndex,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 5);
SEINE_HANDLER_INT(ptHTTPLinksStatus,                            1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 6);
SEINE_HANDLER_STR(ptHTTPLinksLabel,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 7);
SEINE_HANDLER_STR(ptHTTPLinksURL,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 8);
SEINE_HANDLER_INT(ptHTTPConfigEnable,                           1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 3, 9);

SEINE_HANDLER_INT(ptHTTPSPort,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 4, 1);
SEINE_HANDLER_INT(ptHTTPSEnable,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 4, 2);

SEINE_HANDLER_INT(ptLPDPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 5, 1);
SEINE_HANDLER_INT(ptLPDEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 5, 2);

SEINE_HANDLER_INT(ptWSDPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 6, 1);
SEINE_HANDLER_INT(ptWSDEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 6, 2);

SEINE_HANDLER_INT(ptSNMPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 7, 1);
SEINE_HANDLER_INT(ptSNMPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 7, 2);
SEINE_HANDLER_INT(ptSNMPProtocolVer,                            1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 7, 3);

SEINE_HANDLER_INT(ptSMTPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 8, 1);
SEINE_HANDLER_INT(ptSMTPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 8, 2);

SEINE_HANDLER_INT(ptFTPPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 9, 1);
SEINE_HANDLER_INT(ptFTPEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 9, 2);

SEINE_HANDLER_INT(ptSMBPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 10, 1);
SEINE_HANDLER_INT(ptSMBEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 10, 2);
SEINE_HANDLER_INT(ptSMBAuthentication,                          1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 10, 3);

SEINE_HANDLER_INT(ptSLPPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 11, 1);
SEINE_HANDLER_INT(ptSLPEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 11, 2);

SEINE_HANDLER_INT(ptIPPPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 12, 1);
SEINE_HANDLER_INT(ptIPPEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 12, 2);

SEINE_HANDLER_INT(ptSNTPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 13, 1);
SEINE_HANDLER_INT(ptSNTPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 13, 2);

SEINE_HANDLER_INT(ptmDNSPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 14, 1);
SEINE_HANDLER_INT(ptmDNSEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 14, 2);

SEINE_HANDLER_INT(ptDNSPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 15, 1);
SEINE_HANDLER_INT(ptDNSEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 15, 2);
SEINE_HANDLER_INT(ptWiredIPv4DNSAllocationMethod,               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 15, 3);

SEINE_HANDLER_INT(ptBonjourPort,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 16, 1);
SEINE_HANDLER_INT(ptBonjourEnable,                              1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 16, 2);

SEINE_HANDLER_INT(ptTelnetPort,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 17, 1);
SEINE_HANDLER_INT(ptTelnetEnable,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 17, 2);

SEINE_HANDLER_INT(ptUDPPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 18, 1);
SEINE_HANDLER_INT(ptUDPEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 18, 2);

SEINE_HANDLER_INT(pt9100Port,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 19, 1);
SEINE_HANDLER_INT(pt9100Enable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 19, 2);

SEINE_HANDLER_INT(ptSoapPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 20, 1);
SEINE_HANDLER_INT(ptSoapEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 20, 2);

SEINE_HANDLER_INT(ptNetBiosPort,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 21, 1);
SEINE_HANDLER_INT(ptNetBiosEnable,                              1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 21, 2);

SEINE_HANDLER_INT(ptLLMNRPort,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 22, 1);
SEINE_HANDLER_INT(ptLLMNREnable,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 22, 2);

SEINE_HANDLER_INT(ptOpenSSLPort,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 23, 1);
SEINE_HANDLER_INT(ptOpenSSLEnable,                              1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 23, 2);

SEINE_HANDLER_INT(ptAirPrintPort,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 24, 1);
SEINE_HANDLER_INT(ptAirPrintEnable,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 24, 2);

SEINE_HANDLER_INT(ptAirScanPort,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 25, 1);
SEINE_HANDLER_INT(ptAirScanEnable,                              1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 25, 2);

SEINE_HANDLER_INT(ptMopriaPort,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 26, 1);
SEINE_HANDLER_INT(ptMopriaEnable,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 26, 2);

SEINE_HANDLER_INT(ptLLTDPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 27, 1);
SEINE_HANDLER_INT(ptLLTDEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 27, 2);

SEINE_HANDLER_INT(ptNetScanPort,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 28, 1);
SEINE_HANDLER_INT(ptNetScanEnable,                              1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 28, 2);

SEINE_HANDLER_INT(ptARPPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 29, 1);
SEINE_HANDLER_INT(ptARPEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 29, 2);

SEINE_HANDLER_INT(ptLLAPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 30, 1);
SEINE_HANDLER_INT(ptLLAEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 30, 2);

SEINE_HANDLER_INT(ptTFTPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 31, 1);
SEINE_HANDLER_INT(ptTFTPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 31, 2);

SEINE_HANDLER_INT(ptNetservicePort,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 32, 1);
SEINE_HANDLER_INT(ptNetserviceEnable,                           1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 32, 2);

SEINE_HANDLER_INT(ptWi_FiPort,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 33, 1);
SEINE_HANDLER_INT(ptWi_FiEnable,                                1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 33, 2);

SEINE_HANDLER_INT(ptBluetoothPort,                              1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 34, 1);
SEINE_HANDLER_INT(ptBluetoothEnable,                            1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 34, 2);

SEINE_HANDLER_INT(ptEthernetPort,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 35, 1);
SEINE_HANDLER_INT(ptEthernetEnable,                             1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 35, 2);

SEINE_HANDLER_INT(pt802_1XPort,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 36, 1);
SEINE_HANDLER_INT(pt802_1XEnable,                               1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 36, 2);
SEINE_HANDLER_INT(pt802_1XAuthenticationMethod,                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 36, 3);

SEINE_HANDLER_INT(ptRAWPort,                                    1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 37, 1);
SEINE_HANDLER_INT(ptRAWEnable,                                  1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 37, 2);

SEINE_HANDLER_INT(ptLDAPPort,                                   1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 38, 1);
SEINE_HANDLER_INT(ptLDAPEnable,                                 1, 3, 6, 1, 4, 1, 40093, 10, 5, 4, 38, 2);

SEINE_HANDLER_INT(ptPrintStatAllSizeTotalAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 1, 1);
SEINE_HANDLER_INT(ptPrintStatAllSizeTotalSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 1, 2);
SEINE_HANDLER_INT(ptPrintStatAllSizeTotalDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 1, 3);
SEINE_HANDLER_INT(ptPrintStatAllSizeBlackTotal,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 2, 1);
SEINE_HANDLER_INT(ptPrintStatAllSizeBlackSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 2, 2);
SEINE_HANDLER_INT(ptPrintStatAllSizeBlackDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 2, 3);
SEINE_HANDLER_INT(ptPrintStatAllSizeColorAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 3, 1);
SEINE_HANDLER_INT(ptPrintStatAllSizeColorSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 3, 2);
SEINE_HANDLER_INT(ptPrintStatAllSizeColorDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 1, 3, 3);


SEINE_HANDLER_INT(ptPrintStatUserdefTotalAll    ,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 1, 1);
SEINE_HANDLER_INT(ptPrintStatUserdefTotalSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 1, 2);
SEINE_HANDLER_INT(ptPrintStatUserdefTotalDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 1, 3);
SEINE_HANDLER_INT(ptPrintStatUserdefBlackAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 2, 1);
SEINE_HANDLER_INT(ptPrintStatUserdefBlackSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 2, 2);
SEINE_HANDLER_INT(ptPrintStatUserdefBlackDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 2, 3);
SEINE_HANDLER_INT(ptPrintStatUserdefColorAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 3, 1);
SEINE_HANDLER_INT(ptPrintStatUserdefColorSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 3, 2);
SEINE_HANDLER_INT(ptPrintStatUserdefColorDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 2, 3, 3);

SEINE_HANDLER_INT(ptPrintStatA3TotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 1, 1);
SEINE_HANDLER_INT(ptPrintStatA3TotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 1, 2);
SEINE_HANDLER_INT(ptPrintStatA3TotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 1, 3);
SEINE_HANDLER_INT(ptPrintStatA3BlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 2, 1);
SEINE_HANDLER_INT(ptPrintStatA3BlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 2, 2);
SEINE_HANDLER_INT(ptPrintStatA3BlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 2, 3);
SEINE_HANDLER_INT(ptPrintStatA3ColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 3, 1);
SEINE_HANDLER_INT(ptPrintStatA3ColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 3, 2);
SEINE_HANDLER_INT(ptPrintStatA3ColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 3, 3, 3);

SEINE_HANDLER_INT(ptPrintStatA4TotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 1, 1);
SEINE_HANDLER_INT(ptPrintStatA4TotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 1, 2);
SEINE_HANDLER_INT(ptPrintStatA4TotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 1, 3);
SEINE_HANDLER_INT(ptPrintStatA4BlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 2, 1);
SEINE_HANDLER_INT(ptPrintStatA4BlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 2, 2);
SEINE_HANDLER_INT(ptPrintStatA4BlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 2, 3);
SEINE_HANDLER_INT(ptPrintStatA4ColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 3, 1);
SEINE_HANDLER_INT(ptPrintStatA4ColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 3, 2);
SEINE_HANDLER_INT(ptPrintStatA4ColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 4, 3, 3);

SEINE_HANDLER_INT(ptPrintStatA5TotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 1, 1);
SEINE_HANDLER_INT(ptPrintStatA5TotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 1, 2);
SEINE_HANDLER_INT(ptPrintStatA5TotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 1, 3);
SEINE_HANDLER_INT(ptPrintStatA5BlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 2, 1);
SEINE_HANDLER_INT(ptPrintStatA5BlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 2, 2);
SEINE_HANDLER_INT(ptPrintStatA5BlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 2, 3);
SEINE_HANDLER_INT(ptPrintStatA5ColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 3, 1);
SEINE_HANDLER_INT(ptPrintStatA5ColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 3, 2);
SEINE_HANDLER_INT(ptPrintStatA5ColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 5, 3, 3);


SEINE_HANDLER_INT(ptPrintStatB4TotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 1, 1);
SEINE_HANDLER_INT(ptPrintStatB4TotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 1, 2);
SEINE_HANDLER_INT(ptPrintStatB4TotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 1, 3);
SEINE_HANDLER_INT(ptPrintStatB4BlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 2, 1);
SEINE_HANDLER_INT(ptPrintStatB4BlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 2, 2);
SEINE_HANDLER_INT(ptPrintStatB4BlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 2, 3);
SEINE_HANDLER_INT(ptPrintStatB4ColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 3, 1);
SEINE_HANDLER_INT(ptPrintStatB4ColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 3, 2);
SEINE_HANDLER_INT(ptPrintStatB4ColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 6, 3, 3);

SEINE_HANDLER_INT(ptPrintStatJISB5TotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 1, 1);
SEINE_HANDLER_INT(ptPrintStatJISB5TotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 1, 2);
SEINE_HANDLER_INT(ptPrintStatJISB5TotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 1, 3);
SEINE_HANDLER_INT(ptPrintStatJISB5BlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 2, 1);
SEINE_HANDLER_INT(ptPrintStatJISB5BlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 2, 2);
SEINE_HANDLER_INT(ptPrintStatJISB5BlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 2, 3);
SEINE_HANDLER_INT(ptPrintStatJISB5ColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 3, 1);
SEINE_HANDLER_INT(ptPrintStatJISB5ColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 3, 2);
SEINE_HANDLER_INT(ptPrintStatJISB5ColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 7, 3, 3);

SEINE_HANDLER_INT(ptPrintStatIOSB5TotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 1, 1);
SEINE_HANDLER_INT(ptPrintStatIOSB5TotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 1, 2);
SEINE_HANDLER_INT(ptPrintStatIOSB5TotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 1, 3);
SEINE_HANDLER_INT(ptPrintStatIOSB5BlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 2, 1);
SEINE_HANDLER_INT(ptPrintStatIOSB5BlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 2, 2);
SEINE_HANDLER_INT(ptPrintStatIOSB5BlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 2, 3);
SEINE_HANDLER_INT(ptPrintStatIOSB5ColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 3, 1);
SEINE_HANDLER_INT(ptPrintStatIOSB5ColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 3, 2);
SEINE_HANDLER_INT(ptPrintStatIOSB5ColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 8, 3, 3);

SEINE_HANDLER_INT(ptPrintStat11x17TotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 1, 1);
SEINE_HANDLER_INT(ptPrintStat11x17TotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 1, 2);
SEINE_HANDLER_INT(ptPrintStat11x17TotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 1, 3);
SEINE_HANDLER_INT(ptPrintStat11x17BlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 2, 1);
SEINE_HANDLER_INT(ptPrintStat11x17BlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 2, 2);
SEINE_HANDLER_INT(ptPrintStat11x17BlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 2, 3);
SEINE_HANDLER_INT(ptPrintStat11x17ColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 3, 1);
SEINE_HANDLER_INT(ptPrintStat11x17ColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 3, 2);
SEINE_HANDLER_INT(ptPrintStat11x17ColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 9, 3, 3);

SEINE_HANDLER_INT(ptPrintStatLetterTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 1, 1);
SEINE_HANDLER_INT(ptPrintStatLetterTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 1, 2);
SEINE_HANDLER_INT(ptPrintStatLetterTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 1, 3);
SEINE_HANDLER_INT(ptPrintStatLetterBlackAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 2, 1);
SEINE_HANDLER_INT(ptPrintStatLetterBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 2, 2);
SEINE_HANDLER_INT(ptPrintStatLetterBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 2, 3);
SEINE_HANDLER_INT(ptPrintStatLetterColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 3, 1);
SEINE_HANDLER_INT(ptPrintStatLetterColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 3, 2);
SEINE_HANDLER_INT(ptPrintStatLetterColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 10, 3, 3);

SEINE_HANDLER_INT(ptPrintStatLegalTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 1, 1);
SEINE_HANDLER_INT(ptPrintStatLegalTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 1, 2);
SEINE_HANDLER_INT(ptPrintStatLegalTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 1, 3);
SEINE_HANDLER_INT(ptPrintStatLegalBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 2, 1);
SEINE_HANDLER_INT(ptPrintStatLegalBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 2, 2);
SEINE_HANDLER_INT(ptPrintStatLegalBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 2, 3);
SEINE_HANDLER_INT(ptPrintStatLegalColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 3, 1);
SEINE_HANDLER_INT(ptPrintStatLegalColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 3, 2);
SEINE_HANDLER_INT(ptPrintStatLegalColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 11, 3, 3);

SEINE_HANDLER_INT(ptPrintStatFilioTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 1, 1);
SEINE_HANDLER_INT(ptPrintStatFilioTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 1, 2);
SEINE_HANDLER_INT(ptPrintStatFilioTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 1, 3);
SEINE_HANDLER_INT(ptPrintStatFilioBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 2, 1);
SEINE_HANDLER_INT(ptPrintStatFilioBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 2, 2);
SEINE_HANDLER_INT(ptPrintStatFilioBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 2, 3);
SEINE_HANDLER_INT(ptPrintStatFilioColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 3, 1);
SEINE_HANDLER_INT(ptPrintStatFilioColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 3, 2);
SEINE_HANDLER_INT(ptPrintStatFilioColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 12, 3, 3);

SEINE_HANDLER_INT(ptPrintStatOficioTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 1, 1);
SEINE_HANDLER_INT(ptPrintStatOficioTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 1, 2);
SEINE_HANDLER_INT(ptPrintStatOficioTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 1, 3);
SEINE_HANDLER_INT(ptPrintStatOficioBlackAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 2, 1);
SEINE_HANDLER_INT(ptPrintStatOficioBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 2, 2);
SEINE_HANDLER_INT(ptPrintStatOficioBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 2, 3);
SEINE_HANDLER_INT(ptPrintStatOficioColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 3, 1);
SEINE_HANDLER_INT(ptPrintStatOficioColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 3, 2);
SEINE_HANDLER_INT(ptPrintStatOficioColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 13, 3, 3);


SEINE_HANDLER_INT(ptPrintStatExecutiveTotalAll,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 1, 1);
SEINE_HANDLER_INT(ptPrintStatExecutiveTotalSingle,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 1, 2);
SEINE_HANDLER_INT(ptPrintStatExecutiveTotalDuplex,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 1, 3);
SEINE_HANDLER_INT(ptPrintStatExecutiveBlackAll,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 2, 1);
SEINE_HANDLER_INT(ptPrintStatExecutiveBlackSingle,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 2, 2);
SEINE_HANDLER_INT(ptPrintStatExecutiveBlackDuplex,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 2, 3);
SEINE_HANDLER_INT(ptPrintStatExecutiveColorAll,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 3, 1);
SEINE_HANDLER_INT(ptPrintStatExecutiveColorSingle,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 3, 2);
SEINE_HANDLER_INT(ptPrintStatExecutiveColorDuplex,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 14, 3, 3);

SEINE_HANDLER_INT(ptPrintStatStatementTotalAll,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 1, 1);
SEINE_HANDLER_INT(ptPrintStatStatementTotalSingle,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 1, 2);
SEINE_HANDLER_INT(ptPrintStatStatementTotalDuplex,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 1, 3);
SEINE_HANDLER_INT(ptPrintStatStatementBlackAll,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 2, 1);
SEINE_HANDLER_INT(ptPrintStatStatementBlackSingle,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 2, 2);
SEINE_HANDLER_INT(ptPrintStatStatementBlackDuplex,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 2, 3);
SEINE_HANDLER_INT(ptPrintStatStatementColorAll,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 3, 1);
SEINE_HANDLER_INT(ptPrintStatStatementColorSingle,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 3, 2);
SEINE_HANDLER_INT(ptPrintStatStatementColorDuplex,              1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 15, 3, 3);

SEINE_HANDLER_INT(ptPrintStat8KTotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 1, 1);
SEINE_HANDLER_INT(ptPrintStat8KTotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 1, 2);
SEINE_HANDLER_INT(ptPrintStat8KTotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 1, 3);
SEINE_HANDLER_INT(ptPrintStat8KBlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 2, 1);
SEINE_HANDLER_INT(ptPrintStat8KBlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 2, 2);
SEINE_HANDLER_INT(ptPrintStat8KBlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 2, 3);
SEINE_HANDLER_INT(ptPrintStat8KColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 3, 1);
SEINE_HANDLER_INT(ptPrintStat8KColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 3, 2);
SEINE_HANDLER_INT(ptPrintStat8KColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 16, 3, 3);

SEINE_HANDLER_INT(ptPrintStat16KTotalAll,                       1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 1, 1);
SEINE_HANDLER_INT(ptPrintStat16KTotalSingle,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 1, 2);
SEINE_HANDLER_INT(ptPrintStat16KTotalDuplex,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 1, 3);
SEINE_HANDLER_INT(ptPrintStat16KBlackAll,                       1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 2, 1);
SEINE_HANDLER_INT(ptPrintStat16KBlackSingle,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 2, 2);
SEINE_HANDLER_INT(ptPrintStat16KBlackDuplex,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 2, 3);
SEINE_HANDLER_INT(ptPrintStat16KColorAll,                       1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 3, 1);
SEINE_HANDLER_INT(ptPrintStat16KColorSingle,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 3, 2);
SEINE_HANDLER_INT(ptPrintStat16KColorDuplex,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 17, 3, 3);

SEINE_HANDLER_INT(ptPrintStatA6TotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 1, 1);
SEINE_HANDLER_INT(ptPrintStatA6TotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 1, 2);
SEINE_HANDLER_INT(ptPrintStatA6TotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 1, 3);
SEINE_HANDLER_INT(ptPrintStatA6BlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 2, 1);
SEINE_HANDLER_INT(ptPrintStatA6BlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 2, 2);
SEINE_HANDLER_INT(ptPrintStatA6BlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 2, 3);
SEINE_HANDLER_INT(ptPrintStatA6ColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 3, 1);
SEINE_HANDLER_INT(ptPrintStatA6ColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 3, 2);
SEINE_HANDLER_INT(ptPrintStatA6ColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 18, 3, 3);

SEINE_HANDLER_INT(ptPrintStatEnv10TotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 1, 1);
SEINE_HANDLER_INT(ptPrintStatEnv10TotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 1, 2);
SEINE_HANDLER_INT(ptPrintStatEnv10TotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 1, 3);
SEINE_HANDLER_INT(ptPrintStatEnv10BlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 2, 1);
SEINE_HANDLER_INT(ptPrintStatEnv10BlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 2, 2);
SEINE_HANDLER_INT(ptPrintStatEnv10BlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 2, 3);
SEINE_HANDLER_INT(ptPrintStatEnv10ColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 3, 1);
SEINE_HANDLER_INT(ptPrintStatEnv10ColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 3, 2);
SEINE_HANDLER_INT(ptPrintStatEnv10ColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 19, 3, 3);

SEINE_HANDLER_INT(ptPrintStatEnvMonTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 1, 1);
SEINE_HANDLER_INT(ptPrintStatEnvMonTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 1, 2);
SEINE_HANDLER_INT(ptPrintStatEnvMonTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 1, 3);
SEINE_HANDLER_INT(ptPrintStatEnvMonBlackAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 2, 1);
SEINE_HANDLER_INT(ptPrintStatEnvMonBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 2, 2);
SEINE_HANDLER_INT(ptPrintStatEnvMonBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 2, 3);
SEINE_HANDLER_INT(ptPrintStatEnvMonColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 3, 1);
SEINE_HANDLER_INT(ptPrintStatEnvMonColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 3, 2);
SEINE_HANDLER_INT(ptPrintStatEnvMonColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 20, 3, 3);

SEINE_HANDLER_INT(ptPrintStatEnvC6TotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 1, 1);
SEINE_HANDLER_INT(ptPrintStatEnvC6TotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 1, 2);
SEINE_HANDLER_INT(ptPrintStatEnvC6TotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 1, 3);
SEINE_HANDLER_INT(ptPrintStatEnvC6BlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 2, 1);
SEINE_HANDLER_INT(ptPrintStatEnvC6BlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 2, 2);
SEINE_HANDLER_INT(ptPrintStatEnvC6BlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 2, 3);
SEINE_HANDLER_INT(ptPrintStatEnvC6ColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 3, 1);
SEINE_HANDLER_INT(ptPrintStatEnvC6ColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 3, 2);
SEINE_HANDLER_INT(ptPrintStatEnvC6ColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 21, 3, 3);

SEINE_HANDLER_INT(ptPrintStatEnvC5TotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 1, 1);
SEINE_HANDLER_INT(ptPrintStatEnvC5TotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 1, 2);
SEINE_HANDLER_INT(ptPrintStatEnvC5TotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 1, 3);
SEINE_HANDLER_INT(ptPrintStatEnvC5BlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 2, 1);
SEINE_HANDLER_INT(ptPrintStatEnvC5BlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 2, 2);
SEINE_HANDLER_INT(ptPrintStatEnvC5BlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 2, 3);
SEINE_HANDLER_INT(ptPrintStatEnvC5ColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 3, 1);
SEINE_HANDLER_INT(ptPrintStatEnvC5ColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 3, 2);
SEINE_HANDLER_INT(ptPrintStatEnvC5ColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 22, 3, 3);

SEINE_HANDLER_INT(ptPrintStatEnvDLTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 1, 1);
SEINE_HANDLER_INT(ptPrintStatEnvDLTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 1, 2);
SEINE_HANDLER_INT(ptPrintStatEnvDLTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 1, 3);
SEINE_HANDLER_INT(ptPrintStatEnvDLBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 2, 1);
SEINE_HANDLER_INT(ptPrintStatEnvDLBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 2, 2);
SEINE_HANDLER_INT(ptPrintStatEnvDLBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 2, 3);
SEINE_HANDLER_INT(ptPrintStatEnvDLColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 3, 1);
SEINE_HANDLER_INT(ptPrintStatEnvDLColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 3, 2);
SEINE_HANDLER_INT(ptPrintStatEnvDLColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 23, 3, 3);

SEINE_HANDLER_INT(ptPrintStatPostcardTotalAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 1, 1);
SEINE_HANDLER_INT(ptPrintStatPostcardTotalSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 1, 2);
SEINE_HANDLER_INT(ptPrintStatPostcardTotalDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 1, 3);
SEINE_HANDLER_INT(ptPrintStatPostcardBlackAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 2, 1);
SEINE_HANDLER_INT(ptPrintStatPostcardBlackSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 2, 2);
SEINE_HANDLER_INT(ptPrintStatPostcardBlackDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 2, 3);
SEINE_HANDLER_INT(ptPrintStatPostcardColorAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 3, 1);
SEINE_HANDLER_INT(ptPrintStatPostcardColorSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 3, 2);
SEINE_HANDLER_INT(ptPrintStatPostcardColorDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 1, 24, 3, 3);

SEINE_HANDLER_INT(ptScanStatAllSizeTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 1, 1);
SEINE_HANDLER_INT(ptScanStatAllSizeTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 1, 2);
SEINE_HANDLER_INT(ptScanStatAllSizeTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 1, 3);
SEINE_HANDLER_INT(ptScanStatAllSizeBlackTotal,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 2, 1);
SEINE_HANDLER_INT(ptScanStatAllSizeBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 2, 2);
SEINE_HANDLER_INT(ptScanStatAllSizeBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 2, 3);
SEINE_HANDLER_INT(ptScanStatAllSizeColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 3, 1);
SEINE_HANDLER_INT(ptScanStatAllSizeColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 3, 2);
SEINE_HANDLER_INT(ptScanStatAllSizeColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 1, 3, 3);

SEINE_HANDLER_INT(ptScanStatUserdefTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 1, 1);
SEINE_HANDLER_INT(ptScanStatUserdefTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 1, 2);
SEINE_HANDLER_INT(ptScanStatUserdefTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 1, 3);
SEINE_HANDLER_INT(ptScanStatUserdefBlackAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 2, 1);
SEINE_HANDLER_INT(ptScanStatUserdefBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 2, 2);
SEINE_HANDLER_INT(ptScanStatUserdefBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 2, 3);
SEINE_HANDLER_INT(ptScanStatUserdefColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 3, 1);
SEINE_HANDLER_INT(ptScanStatUserdefColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 3, 2);
SEINE_HANDLER_INT(ptScanStatUserdefColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 2, 3, 3);

SEINE_HANDLER_INT(ptScanStatA3TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 1, 1);
SEINE_HANDLER_INT(ptScanStatA3TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 1, 2);
SEINE_HANDLER_INT(ptScanStatA3TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 1, 3);
SEINE_HANDLER_INT(ptScanStatA3BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 2, 1);
SEINE_HANDLER_INT(ptScanStatA3BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 2, 2);
SEINE_HANDLER_INT(ptScanStatA3BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 2, 3);
SEINE_HANDLER_INT(ptScanStatA3ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 3, 1);
SEINE_HANDLER_INT(ptScanStatA3ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 3, 2);
SEINE_HANDLER_INT(ptScanStatA3ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 3, 3, 3);

SEINE_HANDLER_INT(ptScanStatA4TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 1, 1);
SEINE_HANDLER_INT(ptScanStatA4TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 1, 2);
SEINE_HANDLER_INT(ptScanStatA4TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 1, 3);
SEINE_HANDLER_INT(ptScanStatA4BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 2, 1);
SEINE_HANDLER_INT(ptScanStatA4BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 2, 2);
SEINE_HANDLER_INT(ptScanStatA4BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 2, 3);
SEINE_HANDLER_INT(ptScanStatA4ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 3, 1);
SEINE_HANDLER_INT(ptScanStatA4ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 3, 2);
SEINE_HANDLER_INT(ptScanStatA4ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 4, 3, 3);

SEINE_HANDLER_INT(ptScanStatA5TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 1, 1);
SEINE_HANDLER_INT(ptScanStatA5TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 1, 2);
SEINE_HANDLER_INT(ptScanStatA5TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 1, 3);
SEINE_HANDLER_INT(ptScanStatA5BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 2, 1);
SEINE_HANDLER_INT(ptScanStatA5BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 2, 2);
SEINE_HANDLER_INT(ptScanStatA5BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 2, 3);
SEINE_HANDLER_INT(ptScanStatA5ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 3, 1);
SEINE_HANDLER_INT(ptScanStatA5ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 3, 2);
SEINE_HANDLER_INT(ptScanStatA5ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 5, 3, 3);

SEINE_HANDLER_INT(ptScanStatB4TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 1, 1);
SEINE_HANDLER_INT(ptScanStatB4TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 1, 2);
SEINE_HANDLER_INT(ptScanStatB4TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 1, 3);
SEINE_HANDLER_INT(ptScanStatB4BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 2, 1);
SEINE_HANDLER_INT(ptScanStatB4BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 2, 2);
SEINE_HANDLER_INT(ptScanStatB4BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 2, 3);
SEINE_HANDLER_INT(ptScanStatB4ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 3, 1);
SEINE_HANDLER_INT(ptScanStatB4ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 3, 2);
SEINE_HANDLER_INT(ptScanStatB4ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 6, 3, 3);

SEINE_HANDLER_INT(ptScanStatJISB5TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 1, 1);
SEINE_HANDLER_INT(ptScanStatJISB5TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 1, 2);
SEINE_HANDLER_INT(ptScanStatJISB5TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 1, 3);
SEINE_HANDLER_INT(ptScanStatJISB5BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 2, 1);
SEINE_HANDLER_INT(ptScanStatJISB5BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 2, 2);
SEINE_HANDLER_INT(ptScanStatJISB5BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 2, 3);
SEINE_HANDLER_INT(ptScanStatJISB5ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 3, 1);
SEINE_HANDLER_INT(ptScanStatJISB5ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 3, 2);
SEINE_HANDLER_INT(ptScanStatJISB5ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 7, 3, 3);

SEINE_HANDLER_INT(ptScanStatIOSB5TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 1, 1);
SEINE_HANDLER_INT(ptScanStatIOSB5TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 1, 2);
SEINE_HANDLER_INT(ptScanStatIOSB5TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 1, 3);
SEINE_HANDLER_INT(ptScanStatIOSB5BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 2, 1);
SEINE_HANDLER_INT(ptScanStatIOSB5BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 2, 2);
SEINE_HANDLER_INT(ptScanStatIOSB5BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 2, 3);
SEINE_HANDLER_INT(ptScanStatIOSB5ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 3, 1);
SEINE_HANDLER_INT(ptScanStatIOSB5ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 3, 2);
SEINE_HANDLER_INT(ptScanStatIOSB5ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 8, 3, 3);

SEINE_HANDLER_INT(ptScanStat11x17TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 1, 1);
SEINE_HANDLER_INT(ptScanStat11x17TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 1, 2);
SEINE_HANDLER_INT(ptScanStat11x17TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 1, 3);
SEINE_HANDLER_INT(ptScanStat11x17BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 2, 1);
SEINE_HANDLER_INT(ptScanStat11x17BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 2, 2);
SEINE_HANDLER_INT(ptScanStat11x17BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 2, 3);
SEINE_HANDLER_INT(ptScanStat11x17ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 3, 1);
SEINE_HANDLER_INT(ptScanStat11x17ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 3, 2);
SEINE_HANDLER_INT(ptScanStat11x17ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 9, 3, 3);

SEINE_HANDLER_INT(ptScanStatLetterTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 1, 1);
SEINE_HANDLER_INT(ptScanStatLetterTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 1, 2);
SEINE_HANDLER_INT(ptScanStatLetterTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 1, 3);
SEINE_HANDLER_INT(ptScanStatLetterBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 2, 1);
SEINE_HANDLER_INT(ptScanStatLetterBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 2, 2);
SEINE_HANDLER_INT(ptScanStatLetterBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 2, 3);
SEINE_HANDLER_INT(ptScanStatLetterColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 3, 1);
SEINE_HANDLER_INT(ptScanStatLetterColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 3, 2);
SEINE_HANDLER_INT(ptScanStatLetterColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 10, 3, 3);

SEINE_HANDLER_INT(ptScanStatLegalTotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 1, 1);
SEINE_HANDLER_INT(ptScanStatLegalTotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 1, 2);
SEINE_HANDLER_INT(ptScanStatLegalTotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 1, 3);
SEINE_HANDLER_INT(ptScanStatLegalBlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 2, 1);
SEINE_HANDLER_INT(ptScanStatLegalBlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 2, 2);
SEINE_HANDLER_INT(ptScanStatLegalBlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 2, 3);
SEINE_HANDLER_INT(ptScanStatLegalColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 3, 1);
SEINE_HANDLER_INT(ptScanStatLegalColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 3, 2);
SEINE_HANDLER_INT(ptScanStatLegalColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 11, 3, 3);

SEINE_HANDLER_INT(ptScanStatFilioTotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 1, 1);
SEINE_HANDLER_INT(ptScanStatFilioTotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 1, 2);
SEINE_HANDLER_INT(ptScanStatFilioTotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 1, 3);
SEINE_HANDLER_INT(ptScanStatFilioBlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 2, 1);
SEINE_HANDLER_INT(ptScanStatFilioBlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 2, 2);
SEINE_HANDLER_INT(ptScanStatFilioBlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 2, 3);
SEINE_HANDLER_INT(ptScanStatFilioColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 3, 1);
SEINE_HANDLER_INT(ptScanStatFilioColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 3, 1);
SEINE_HANDLER_INT(ptScanStatFilioColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 12, 3, 1);

SEINE_HANDLER_INT(ptScanStatOficioTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 1, 1);
SEINE_HANDLER_INT(ptScanStatOficioTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 1, 2);
SEINE_HANDLER_INT(ptScanStatOficioTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 1, 3);
SEINE_HANDLER_INT(ptScanStatOficioBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 2, 1);
SEINE_HANDLER_INT(ptScanStatOficioBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 2, 2);
SEINE_HANDLER_INT(ptScanStatOficioBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 2, 3);
SEINE_HANDLER_INT(ptScanStatOficioColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 3, 1);
SEINE_HANDLER_INT(ptScanStatOficioColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 3, 2);
SEINE_HANDLER_INT(ptScanStatOficioColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 13, 3, 3);

SEINE_HANDLER_INT(ptScanStatExecutiveTotalAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 1, 1);
SEINE_HANDLER_INT(ptScanStatExecutiveTotalSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 1, 2);
SEINE_HANDLER_INT(ptScanStatExecutiveTotalDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 1, 3);
SEINE_HANDLER_INT(ptScanStatExecutiveBlackAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 2, 1);
SEINE_HANDLER_INT(ptScanStatExecutiveBlackSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 2, 2);
SEINE_HANDLER_INT(ptScanStatExecutiveBlackDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 2, 3);
SEINE_HANDLER_INT(ptScanStatExecutiveColorAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 3, 1);
SEINE_HANDLER_INT(ptScanStatExecutiveColorSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 3, 2);
SEINE_HANDLER_INT(ptScanStatExecutiveColorDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 14, 3, 3);

SEINE_HANDLER_INT(ptScanStatStatementTotalAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 1, 1);
SEINE_HANDLER_INT(ptScanStatStatementTotalSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 1, 2);
SEINE_HANDLER_INT(ptScanStatStatementTotalDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 1, 3);
SEINE_HANDLER_INT(ptScanStatStatementBlackAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 2, 1);
SEINE_HANDLER_INT(ptScanStatStatementBlackSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 2, 2);
SEINE_HANDLER_INT(ptScanStatStatementBlackDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 2, 3);
SEINE_HANDLER_INT(ptScanStatStatementColorAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 3, 1);
SEINE_HANDLER_INT(ptScanStatStatementColorSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 3, 2);
SEINE_HANDLER_INT(ptScanStatStatementColorDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 15, 3, 3);

SEINE_HANDLER_INT(ptScanStat8KTotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 1, 1);
SEINE_HANDLER_INT(ptScanStat8KTotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 1, 2);
SEINE_HANDLER_INT(ptScanStat8KTotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 1, 3);
SEINE_HANDLER_INT(ptScanStat8KBlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 2, 1);
SEINE_HANDLER_INT(ptScanStat8KBlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 2, 2);
SEINE_HANDLER_INT(ptScanStat8KBlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 2, 3);
SEINE_HANDLER_INT(ptScanStat8KColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 3, 1);
SEINE_HANDLER_INT(ptScanStat8KColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 3, 2);
SEINE_HANDLER_INT(ptScanStat8KColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 16, 3, 3);

SEINE_HANDLER_INT(ptScanStat16KTotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 1, 1);
SEINE_HANDLER_INT(ptScanStat16KTotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 1, 2);
SEINE_HANDLER_INT(ptScanStat16KTotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 1, 3);
SEINE_HANDLER_INT(ptScanStat16KBlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 2, 1);
SEINE_HANDLER_INT(ptScanStat16KBlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 2, 2);
SEINE_HANDLER_INT(ptScanStat16KBlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 2, 3);
SEINE_HANDLER_INT(ptScanStat16KColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 3, 1);
SEINE_HANDLER_INT(ptScanStat16KColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 3, 2);
SEINE_HANDLER_INT(ptScanStat16KColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 17, 3, 3);

SEINE_HANDLER_INT(ptScanStatA6TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 1, 1);
SEINE_HANDLER_INT(ptScanStatA6TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 1, 2);
SEINE_HANDLER_INT(ptScanStatA6TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 1, 3);
SEINE_HANDLER_INT(ptScanStatA6BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 2, 1);
SEINE_HANDLER_INT(ptScanStatA6BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 2, 2);
SEINE_HANDLER_INT(ptScanStatA6BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 2, 3);
SEINE_HANDLER_INT(ptScanStatA6ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 3, 1);
SEINE_HANDLER_INT(ptScanStatA6ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 3, 2);
SEINE_HANDLER_INT(ptScanStatA6ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 18, 3, 3);

SEINE_HANDLER_INT(ptScanStatEnv10TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 1, 1);
SEINE_HANDLER_INT(ptScanStatEnv10TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 1, 2);
SEINE_HANDLER_INT(ptScanStatEnv10TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 1, 3);
SEINE_HANDLER_INT(ptScanStatEnv10BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 2, 1);
SEINE_HANDLER_INT(ptScanStatEnv10BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 2, 2);
SEINE_HANDLER_INT(ptScanStatEnv10BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 2, 3);
SEINE_HANDLER_INT(ptScanStatEnv10ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 3, 1);
SEINE_HANDLER_INT(ptScanStatEnv10ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 3, 2);
SEINE_HANDLER_INT(ptScanStatEnv10ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 19, 3, 3);

SEINE_HANDLER_INT(ptScanStatEnvMonTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 1, 1);
SEINE_HANDLER_INT(ptScanStatEnvMonTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 1, 2);
SEINE_HANDLER_INT(ptScanStatEnvMonTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 1, 3);
SEINE_HANDLER_INT(ptScanStatEnvMonBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 2, 1);
SEINE_HANDLER_INT(ptScanStatEnvMonBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 2, 2);
SEINE_HANDLER_INT(ptScanStatEnvMonBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 2, 3);
SEINE_HANDLER_INT(ptScanStatEnvMonColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 3, 1);
SEINE_HANDLER_INT(ptScanStatEnvMonColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 3, 2);
SEINE_HANDLER_INT(ptScanStatEnvMonColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 20, 3, 3);

SEINE_HANDLER_INT(ptScanStatEnvC6TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 1, 1);
SEINE_HANDLER_INT(ptScanStatEnvC6TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 1, 2);
SEINE_HANDLER_INT(ptScanStatEnvC6TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 1, 3);
SEINE_HANDLER_INT(ptScanStatEnvC6BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 2, 1);
SEINE_HANDLER_INT(ptScanStatEnvC6BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 2, 2);
SEINE_HANDLER_INT(ptScanStatEnvC6BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 2, 3);
SEINE_HANDLER_INT(ptScanStatEnvC6ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 3, 1);
SEINE_HANDLER_INT(ptScanStatEnvC6ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 3, 2);
SEINE_HANDLER_INT(ptScanStatEnvC6ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 21, 3, 3);

SEINE_HANDLER_INT(ptScanStatEnvC5TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 1, 1);
SEINE_HANDLER_INT(ptScanStatEnvC5TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 1, 2);
SEINE_HANDLER_INT(ptScanStatEnvC5TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 1, 3);
SEINE_HANDLER_INT(ptScanStatEnvC5BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 2, 1);
SEINE_HANDLER_INT(ptScanStatEnvC5BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 2, 2);
SEINE_HANDLER_INT(ptScanStatEnvC5BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 2, 3)
SEINE_HANDLER_INT(ptScanStatEnvC5ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 3, 1);
SEINE_HANDLER_INT(ptScanStatEnvC5ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 3, 2);
SEINE_HANDLER_INT(ptScanStatEnvC5ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 22, 3, 3);

SEINE_HANDLER_INT(ptScanStatEnvDLTotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 1, 1);
SEINE_HANDLER_INT(ptScanStatEnvDLTotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 1, 2);
SEINE_HANDLER_INT(ptScanStatEnvDLTotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 1, 3);
SEINE_HANDLER_INT(ptScanStatEnvDLBlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 2, 1);
SEINE_HANDLER_INT(ptScanStatEnvDLBlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 2, 2);
SEINE_HANDLER_INT(ptScanStatEnvDLBlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 2, 3);
SEINE_HANDLER_INT(ptScanStatEnvDLColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 3, 1);
SEINE_HANDLER_INT(ptScanStatEnvDLColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 3, 2);
SEINE_HANDLER_INT(ptScanStatEnvDLColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 23, 3, 3);

SEINE_HANDLER_INT(ptScanStatPostcardTotalAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 1, 1);
SEINE_HANDLER_INT(ptScanStatPostcardTotalSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 1, 2);
SEINE_HANDLER_INT(ptScanStatPostcardTotalDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 1, 3);
SEINE_HANDLER_INT(ptScanStatPostcardBlackAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 2, 1);
SEINE_HANDLER_INT(ptScanStatPostcardBlackSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 2, 2);
SEINE_HANDLER_INT(ptScanStatPostcardBlackDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 2, 3);
SEINE_HANDLER_INT(ptScanStatPostcardColorAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 3, 1);
SEINE_HANDLER_INT(ptScanStatPostcardColorSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 3, 2);
SEINE_HANDLER_INT(ptScanStatPostcardColorDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 2, 24, 3, 3);

SEINE_HANDLER_INT(ptCopyStatAllSizeTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 1, 1);
SEINE_HANDLER_INT(ptCopyStatAllSizeTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 1, 2);
SEINE_HANDLER_INT(ptCopyStatAllSizeTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 1, 3);
SEINE_HANDLER_INT(ptCopyStatAllSizeBlackTotal,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 2, 1);
SEINE_HANDLER_INT(ptCopyStatAllSizeBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 2, 2);
SEINE_HANDLER_INT(ptCopyStatAllSizeBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 2, 3);
SEINE_HANDLER_INT(ptCopyStatAllSizeColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 3, 1);
SEINE_HANDLER_INT(ptCopyStatAllSizeColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 3, 2);
SEINE_HANDLER_INT(ptCopyStatAllSizeColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 1, 3, 3);

SEINE_HANDLER_INT(ptCopyStatUserdefTotalAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 1, 1);
SEINE_HANDLER_INT(ptCopyStatUserdefTotalSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 1, 2);
SEINE_HANDLER_INT(ptCopyStatUserdefTotalDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 1, 3);
SEINE_HANDLER_INT(ptCopyStatUserdefBlackAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 2, 1);
SEINE_HANDLER_INT(ptCopyStatUserdefBlackSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 2, 2);
SEINE_HANDLER_INT(ptCopyStatUserdefBlackDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 2, 3);
SEINE_HANDLER_INT(ptCopyStatUserdefColorAll,                    1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 3, 1);
SEINE_HANDLER_INT(ptCopyStatUserdefColorSingle,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 3, 2);
SEINE_HANDLER_INT(ptCopyStatUserdefColorDuplex,                 1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 2, 3, 3);

SEINE_HANDLER_INT(ptCopyStatA3TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 1, 1);
SEINE_HANDLER_INT(ptCopyStatA3TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 1, 2);
SEINE_HANDLER_INT(ptCopyStatA3TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 1, 3);
SEINE_HANDLER_INT(ptCopyStatA3BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 2, 1);
SEINE_HANDLER_INT(ptCopyStatA3BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 2, 2);
SEINE_HANDLER_INT(ptCopyStatA3BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 2, 3);
SEINE_HANDLER_INT(ptCopyStatA3ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 3, 1);
SEINE_HANDLER_INT(ptCopyStatA3ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 3, 2);
SEINE_HANDLER_INT(ptCopyStatA3ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 3, 3, 3);

SEINE_HANDLER_INT(ptCopyStatA4TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 1, 1);
SEINE_HANDLER_INT(ptCopyStatA4TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 1, 2);
SEINE_HANDLER_INT(ptCopyStatA4TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 1, 3);
SEINE_HANDLER_INT(ptCopyStatA4BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 2, 1);
SEINE_HANDLER_INT(ptCopyStatA4BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 2, 2);
SEINE_HANDLER_INT(ptCopyStatA4BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 2, 3);
SEINE_HANDLER_INT(ptCopyStatA4ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 3, 1);
SEINE_HANDLER_INT(ptCopyStatA4ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 3, 2);
SEINE_HANDLER_INT(ptCopyStatA4ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 4, 3, 3);

SEINE_HANDLER_INT(ptCopyStatA5TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 1, 1);
SEINE_HANDLER_INT(ptCopyStatA5TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 1, 2);
SEINE_HANDLER_INT(ptCopyStatA5TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 1, 3);
SEINE_HANDLER_INT(ptCopyStatA5BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 2, 1);
SEINE_HANDLER_INT(ptCopyStatA5BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 2, 2);
SEINE_HANDLER_INT(ptCopyStatA5BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 2, 3);
SEINE_HANDLER_INT(ptCopyStatA5ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 3, 1);
SEINE_HANDLER_INT(ptCopyStatA5ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 3, 2);
SEINE_HANDLER_INT(ptCopyStatA5ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 5, 3, 3);

SEINE_HANDLER_INT(ptCopyStatB4TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 1, 1);
SEINE_HANDLER_INT(ptCopyStatB4TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 1, 2);
SEINE_HANDLER_INT(ptCopyStatB4TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 1, 3);
SEINE_HANDLER_INT(ptCopyStatB4BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 2, 1);
SEINE_HANDLER_INT(ptCopyStatB4BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 2, 2);
SEINE_HANDLER_INT(ptCopyStatB4BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 2, 3);
SEINE_HANDLER_INT(ptCopyStatB4ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 3, 1);
SEINE_HANDLER_INT(ptCopyStatB4ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 3, 2);
SEINE_HANDLER_INT(ptCopyStatB4ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 6, 3, 3);

SEINE_HANDLER_INT(ptCopyStatJISB5TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 1, 1);
SEINE_HANDLER_INT(ptCopyStatJISB5TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 1, 2);
SEINE_HANDLER_INT(ptCopyStatJISB5TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 1, 3);
SEINE_HANDLER_INT(ptCopyStatJISB5BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 2, 1);
SEINE_HANDLER_INT(ptCopyStatJISB5BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 2, 2);
SEINE_HANDLER_INT(ptCopyStatJISB5BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 2, 3);
SEINE_HANDLER_INT(ptCopyStatJISB5ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 3, 1);
SEINE_HANDLER_INT(ptCopyStatJISB5ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 3, 2);
SEINE_HANDLER_INT(ptCopyStatJISB5ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 7, 3, 3);

SEINE_HANDLER_INT(ptCopyStatIOSB5TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 1, 1);
SEINE_HANDLER_INT(ptCopyStatIOSB5TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 1, 2);
SEINE_HANDLER_INT(ptCopyStatIOSB5TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 1, 3);
SEINE_HANDLER_INT(ptCopyStatIOSB5BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 2, 1);
SEINE_HANDLER_INT(ptCopyStatIOSB5BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 2, 2);
SEINE_HANDLER_INT(ptCopyStatIOSB5BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 2, 3);
SEINE_HANDLER_INT(ptCopyStatIOSB5ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 3, 1);
SEINE_HANDLER_INT(ptCopyStatIOSB5ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 3, 2);
SEINE_HANDLER_INT(ptCopyStatIOSB5ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 8, 3, 3);


SEINE_HANDLER_INT(ptCopyStat11x17TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 1, 1);
SEINE_HANDLER_INT(ptCopyStat11x17TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 1, 2);
SEINE_HANDLER_INT(ptCopyStat11x17TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 1, 3);
SEINE_HANDLER_INT(ptCopyStat11x17BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 2, 1);
SEINE_HANDLER_INT(ptCopyStat11x17BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 2, 2);
SEINE_HANDLER_INT(ptCopyStat11x17BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 2, 3);
SEINE_HANDLER_INT(ptCopyStat11x17ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 3, 1);
SEINE_HANDLER_INT(ptCopyStat11x17ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 3, 2);
SEINE_HANDLER_INT(ptCopyStat11x17ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 9, 3, 3);

SEINE_HANDLER_INT(ptCopyStatLetterTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 1, 1);
SEINE_HANDLER_INT(ptCopyStatLetterTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 1, 2);
SEINE_HANDLER_INT(ptCopyStatLetterTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 1, 3);
SEINE_HANDLER_INT(ptCopyStatLetterBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 2, 1);
SEINE_HANDLER_INT(ptCopyStatLetterBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 2, 2);
SEINE_HANDLER_INT(ptCopyStatLetterBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 2, 3);
SEINE_HANDLER_INT(ptCopyStatLetterColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 3, 1);
SEINE_HANDLER_INT(ptCopyStatLetterColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 3, 2);
SEINE_HANDLER_INT(ptCopyStatLetterColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 10, 3, 3);

SEINE_HANDLER_INT(ptCopyStatLegalTotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 1, 1);
SEINE_HANDLER_INT(ptCopyStatLegalTotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 1, 2);
SEINE_HANDLER_INT(ptCopyStatLegalTotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 1, 3);
SEINE_HANDLER_INT(ptCopyStatLegalBlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 2, 1);
SEINE_HANDLER_INT(ptCopyStatLegalBlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 2, 2);
SEINE_HANDLER_INT(ptCopyStatLegalBlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 2, 3);
SEINE_HANDLER_INT(ptCopyStatLegalColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 3, 1);
SEINE_HANDLER_INT(ptCopyStatLegalColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 3, 2);
SEINE_HANDLER_INT(ptCopyStatLegalColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 11, 3, 3);

SEINE_HANDLER_INT(ptCopyStatFilioTotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 1, 1);
SEINE_HANDLER_INT(ptCopyStatFilioTotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 1, 2);
SEINE_HANDLER_INT(ptCopyStatFilioTotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 1, 3);
SEINE_HANDLER_INT(ptCopyStatFilioBlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 2, 1);
SEINE_HANDLER_INT(ptCopyStatFilioBlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 2, 2);
SEINE_HANDLER_INT(ptCopyStatFilioBlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 2, 3);
SEINE_HANDLER_INT(ptCopyStatFilioColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 3, 1);
SEINE_HANDLER_INT(ptCopyStatFilioColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 3, 2);
SEINE_HANDLER_INT(ptCopyStatFilioColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 12, 3, 3);

SEINE_HANDLER_INT(ptCopyStatOficioTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 1, 1);
SEINE_HANDLER_INT(ptCopyStatOficioTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 1, 2);
SEINE_HANDLER_INT(ptCopyStatOficioTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 1, 3);
SEINE_HANDLER_INT(ptCopyStatOficioBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 2, 1);
SEINE_HANDLER_INT(ptCopyStatOficioBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 2, 2);
SEINE_HANDLER_INT(ptCopyStatOficioBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 2, 3);
SEINE_HANDLER_INT(ptCopyStatOficioColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 3, 1);
SEINE_HANDLER_INT(ptCopyStatOficioColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 3, 2);
SEINE_HANDLER_INT(ptCopyStatOficioColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 13, 3, 3);

SEINE_HANDLER_INT(ptCopyStatExecutiveTotalAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 1, 1);
SEINE_HANDLER_INT(ptCopyStatExecutiveTotalSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 1, 2);
SEINE_HANDLER_INT(ptCopyStatExecutiveTotalDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 1, 3);
SEINE_HANDLER_INT(ptCopyStatExecutiveBlackAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 2, 1);
SEINE_HANDLER_INT(ptCopyStatExecutiveBlackSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 2, 2);
SEINE_HANDLER_INT(ptCopyStatExecutiveBlackDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 2, 3);
SEINE_HANDLER_INT(ptCopyStatExecutiveColorAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 3, 1);
SEINE_HANDLER_INT(ptCopyStatExecutiveColorSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 3, 2);
SEINE_HANDLER_INT(ptCopyStatExecutiveColorDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 14, 3, 3);

SEINE_HANDLER_INT(ptCopyStatStatementTotalAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 1, 1);
SEINE_HANDLER_INT(ptCopyStatStatementTotalSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 1, 2);
SEINE_HANDLER_INT(ptCopyStatStatementTotalDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 1, 3);
SEINE_HANDLER_INT(ptCopyStatStatementBlackAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 2, 1);
SEINE_HANDLER_INT(ptCopyStatStatementBlackSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 2, 2);
SEINE_HANDLER_INT(ptCopyStatStatementBlackDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 2, 3);
SEINE_HANDLER_INT(ptCopyStatStatementColorAll,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 3, 1);
SEINE_HANDLER_INT(ptCopyStatStatementColorSingle,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 3, 2);
SEINE_HANDLER_INT(ptCopyStatStatementColorDuplex,               1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 15, 3, 3);

SEINE_HANDLER_INT(ptCopyStat8KTotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 1, 1);
SEINE_HANDLER_INT(ptCopyStat8KTotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 1, 2);
SEINE_HANDLER_INT(ptCopyStat8KTotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 1, 3);
SEINE_HANDLER_INT(ptCopyStat8KBlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 2, 1);
SEINE_HANDLER_INT(ptCopyStat8KBlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 2, 2);
SEINE_HANDLER_INT(ptCopyStat8KBlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 2, 3);
SEINE_HANDLER_INT(ptCopyStat8KColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 3, 1);
SEINE_HANDLER_INT(ptCopyStat8KColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 3, 2);
SEINE_HANDLER_INT(ptCopyStat8KColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 16, 3, 3);

SEINE_HANDLER_INT(ptCopyStat16KTotalAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 1, 1);
SEINE_HANDLER_INT(ptCopyStat16KTotalSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 1, 2);
SEINE_HANDLER_INT(ptCopyStat16KTotalDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 1, 3);
SEINE_HANDLER_INT(ptCopyStat16KBlackAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 2, 1);
SEINE_HANDLER_INT(ptCopyStat16KBlackSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 2, 2);
SEINE_HANDLER_INT(ptCopyStat16KBlackDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 2, 3);
SEINE_HANDLER_INT(ptCopyStat16KColorAll,                        1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 3, 1);
SEINE_HANDLER_INT(ptCopyStat16KColorSingle,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 3, 2);
SEINE_HANDLER_INT(ptCopyStat16KColorDuplex,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 17, 3, 3);

SEINE_HANDLER_INT(ptCopyStatA6TotalAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 1, 1);
SEINE_HANDLER_INT(ptCopyStatA6TotalSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 1, 2);
SEINE_HANDLER_INT(ptCopyStatA6TotalDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 1, 3);
SEINE_HANDLER_INT(ptCopyStatA6BlackAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 2, 1);
SEINE_HANDLER_INT(ptCopyStatA6BlackSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 2, 2);
SEINE_HANDLER_INT(ptCopyStatA6BlackDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 2, 3);
SEINE_HANDLER_INT(ptCopyStatA6ColorAll,                         1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 3, 1);
SEINE_HANDLER_INT(ptCopyStatA6ColorSingle,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 3, 2);
SEINE_HANDLER_INT(ptCopyStatA6ColorDuplex,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 18, 3, 3);

SEINE_HANDLER_INT(ptCopyStatEnv10TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 1, 1);
SEINE_HANDLER_INT(ptCopyStatEnv10TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 1, 2);
SEINE_HANDLER_INT(ptCopyStatEnv10TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 1, 3);
SEINE_HANDLER_INT(ptCopyStatEnv10BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 2, 1);
SEINE_HANDLER_INT(ptCopyStatEnv10BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 2, 2);
SEINE_HANDLER_INT(ptCopyStatEnv10BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 2, 3);
SEINE_HANDLER_INT(ptCopyStatEnv10ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 3, 1);
SEINE_HANDLER_INT(ptCopyStatEnv10ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 3, 2);
SEINE_HANDLER_INT(ptCopyStatEnv10ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 19, 3, 3);

SEINE_HANDLER_INT(ptCopyStatEnvMonTotalAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 1, 1);
SEINE_HANDLER_INT(ptCopyStatEnvMonTotalSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 1, 2);
SEINE_HANDLER_INT(ptCopyStatEnvMonTotalDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 1, 3);
SEINE_HANDLER_INT(ptCopyStatEnvMonBlackAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 2, 1);
SEINE_HANDLER_INT(ptCopyStatEnvMonBlackSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 2, 2);
SEINE_HANDLER_INT(ptCopyStatEnvMonBlackDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 2, 3);
SEINE_HANDLER_INT(ptCopyStatEnvMonColorAll,                     1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 3, 1);
SEINE_HANDLER_INT(ptCopyStatEnvMonColorSingle,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 3, 2);
SEINE_HANDLER_INT(ptCopyStatEnvMonColorDuplex,                  1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 20, 3, 3);

SEINE_HANDLER_INT(ptCopyStatEnvC6TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 1, 1);
SEINE_HANDLER_INT(ptCopyStatEnvC6TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 1, 2);
SEINE_HANDLER_INT(ptCopyStatEnvC6TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 1, 3);
SEINE_HANDLER_INT(ptCopyStatEnvC6BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 2, 1);
SEINE_HANDLER_INT(ptCopyStatEnvC6BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 2, 2);
SEINE_HANDLER_INT(ptCopyStatEnvC6BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 2, 3);
SEINE_HANDLER_INT(ptCopyStatEnvC6ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 3, 1);
SEINE_HANDLER_INT(ptCopyStatEnvC6ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 3, 2);
SEINE_HANDLER_INT(ptCopyStatEnvC6ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 21, 3, 3);

SEINE_HANDLER_INT(ptCopyStatEnvC5TotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 1, 1);
SEINE_HANDLER_INT(ptCopyStatEnvC5TotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 1, 2);
SEINE_HANDLER_INT(ptCopyStatEnvC5TotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 1, 3);
SEINE_HANDLER_INT(ptCopyStatEnvC5BlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 2, 1);
SEINE_HANDLER_INT(ptCopyStatEnvC5BlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 2, 2);
SEINE_HANDLER_INT(ptCopyStatEnvC5BlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 2, 3);
SEINE_HANDLER_INT(ptCopyStatEnvC5ColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 3, 1);
SEINE_HANDLER_INT(ptCopyStatEnvC5ColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 3, 2);
SEINE_HANDLER_INT(ptCopyStatEnvC5ColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 22, 3, 3);

SEINE_HANDLER_INT(ptCopyStatEnvDLTotalAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 1, 1);
SEINE_HANDLER_INT(ptCopyStatEnvDLTotalSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 1, 2);
SEINE_HANDLER_INT(ptCopyStatEnvDLTotalDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 1, 3);
SEINE_HANDLER_INT(ptCopyStatEnvDLBlackAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 2, 1);
SEINE_HANDLER_INT(ptCopyStatEnvDLBlackSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 2, 2);
SEINE_HANDLER_INT(ptCopyStatEnvDLBlackDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 2, 3);
SEINE_HANDLER_INT(ptCopyStatEnvDLColorAll,                      1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 3, 1);
SEINE_HANDLER_INT(ptCopyStatEnvDLColorSingle,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 3, 2);
SEINE_HANDLER_INT(ptCopyStatEnvDLColorDuplex,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 23, 3, 3);

SEINE_HANDLER_INT(ptCopyStatPostcardTotalAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 1, 1);
SEINE_HANDLER_INT(ptCopyStatPostcardTotalSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 1, 2);
SEINE_HANDLER_INT(ptCopyStatPostcardTotalDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 1, 3);
SEINE_HANDLER_INT(ptCopyStatPostcardBlackAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 2, 1);
SEINE_HANDLER_INT(ptCopyStatPostcardBlackSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 2, 2);
SEINE_HANDLER_INT(ptCopyStatPostcardBlackDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 2, 3);
SEINE_HANDLER_INT(ptCopyStatPostcardColorAll,                   1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 3, 1);
SEINE_HANDLER_INT(ptCopyStatPostcardColorSingle,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 3, 2);
SEINE_HANDLER_INT(ptCopyStatPostcardColorDuplex,                1, 3, 6, 1, 4, 1, 40093, 10, 6, 3, 24, 3, 3);

//end for pantum printer mps

void init_seine(void)
{
    DEBUGMSGTL(("seine", "Initializing\n"));

    seine_map = (SNMP_MAP_S *)ipc_shm(&seine_lock, SNMP_MAP_NAME, sizeof(SNMP_MAP_S));
    if ( seine_map == NULL )
    {
        fprintf(stderr, "Failed to create shared memory so that SN3012DWMIB module will not response\n");
        return;
    }

    register_ptFirmwareVersion();
    register_ptCardMemory();
    register_ptEngineVersion();
    register_ptSerialNumber();
    register_ptManufacturerName();
    register_ptProductDate();
    register_ptProductName();
    register_ptBootVersion();
    register_ptKernelVersion();
    register_ptMACAddress();
    register_ptPrintColor();
    register_ptPrintSpeed();
    register_ptGeneralProduceConfig();
    register_ptDeviceMibMpsVer();
    register_ptDeviceMibPrintControlEnable();
    register_ptDeviceMibPrintControlVer();
    register_ptDeviceStatus();

    register_ptSleepTime();
    register_ptPrintSpeedMode();
    register_ptJobTimeOut();
    register_ptInkSavingSetting();
    register_ptLanguageSetting();
    register_ptRestoreFactorySetting();
    register_ptMutePrintSetting();
    register_ptScreenBrightnessSetting();
    register_ptShutdownConditonSetting();
    register_ptShutdownDelaySetting();
    register_ptClesnFixingPageSetting();
    register_ptImageCheapSetting();
    register_ptVolumeIndex();
    register_ptVolumeSetting();
    register_ptDataFormat();
    register_ptTimeFormat();
    register_ptDateTimeSetting();
    register_ptTimezoneSetting();
    register_ptOnlineUpdateSetting();
    register_ptWindowsLogInEnable();
    register_ptServerAuthentication();
    register_ptWindowsLogIn();
    register_ptTrayIndex();
    register_ptTrayPlacingPaperTips();
    register_ptPaperSizeSetting();
    register_ptPaperTypeSetting();
    register_ptPaperSourceSetting();
    register_ptPrintingQuantitySetting();
    register_ptPrintDuplexSetting();
    register_ptImageOrientationSetting();
    register_ptPrintConcentrationSetting();
    register_ptResolutionSetting();
    register_ptWPSModeEnable();
    register_ptWirelessFrequencySetting();
    register_ptWirelessDirectConnectSetting();
    register_ptEmailEncryptionSetting();
    register_ptNetworkContactsEnable();
    register_ptUserLoginEnable();
    register_ptPanelSessionTimeoutTimeSetting();
    register_ptHardwarePortsEnable();
    register_ptForbidScanTOEmailSmbFtpEnable();
    register_ptWebEncryptionEnable();
    register_ptMemoryResetSetting();
    register_ptUsbConnectedFlag();
    register_ptNWConnectedFlag();
    register_ptPrintGeneralStatus();

    register_ptToneindexC();
    register_ptToneindexM();
    register_ptToneindexY();
    register_ptToneindexK();
    register_ptTonerUnitC();
    register_ptTonerUnitM();
    register_ptTonerUnitY();
    register_ptTonerUnitK();
    register_ptTonerRemainC();
    register_ptTonerRemainM();
    register_ptTonerRemainY();
    register_ptTonerRemainK();
    register_ptTonerMaximumC();
    register_ptTonerMaximumM();
    register_ptTonerMaximumY();
    register_ptTonerMaximumK();
    register_ptTonerModelC();
    register_ptTonerModelM();
    register_ptTonerModelY();
    register_ptTonerModelK();
    register_ptTonerSerialC();
    register_ptTonerSerialM();
    register_ptTonerSerialY();
    register_ptTonerSerialK();
    register_ptTonerStateC();
    register_ptTonerStateM();
    register_ptTonerStateY();
    register_ptTonerStateK();
    register_ptTonerDescriptionC();
    register_ptTonerDescriptionM();
    register_ptTonerDescriptionY();
    register_ptTonerDescriptionK();

    register_ptCartridgeUnitC();
    register_ptCartridgeUnitM();
    register_ptCartridgeUnitY();
    register_ptCartridgeUnitK();
    register_ptCartridgeRemainC();
    register_ptCartridgeRemainM();
    register_ptCartridgeRemainY();
    register_ptCartridgeRemainK();
    register_ptCartridgeMaximumC();
    register_ptCartridgeMaximumM();
    register_ptCartridgeMaximumY();
    register_ptCartridgeMaximumK();
    register_ptCartridgeModelC();
    register_ptCartridgeModelM();
    register_ptCartridgeModelY();
    register_ptCartridgeModelK();
    register_ptCartridgeSerialC();
    register_ptCartridgeSerialM();
    register_ptCartridgeSerialY();
    register_ptCartridgeSerialK();
    register_ptCartridgeStateC();
    register_ptCartridgeStateM();
    register_ptCartridgeStateY();
    register_ptCartridgeStateK();

    register_ptWasterTonerState();
    register_ptWasterTonerRemain();
    register_ptWasterTonerMaximum();
    register_ptWasterTonerModel();
    register_ptWasterTonerSerial();

    register_ptCopyScanMode();
    register_ptCopyJobType();

    register_ptAlertSeverityLevel();
    register_ptAlertGroup();
    register_ptAlertGroupIndex();
    register_ptAlertLocation();
    register_ptAlertCode();
    register_ptAlertdescription();

    register_ptAlertNewSeverityLevel();
    register_ptAlertNewGroup();
    register_ptAlertNewGroupIndex();
    register_ptAlertNewLocation();
    register_ptAlertNewCode();
    register_ptAlerNewdescription();

    register_ptJobIndex();
    register_ptJobID();
    register_ptJobCurPrintptgeNumber();
    register_ptJobTray();
    register_ptJobptperSize();
    register_ptJobptperMedia();
    register_ptJobMemory();
    register_ptJobVia();
    register_ptJobOwner();
    register_ptPrintJobID();
    register_ptJobAliveFlag();

    register_ptScanJobIndex();
    register_ptScanJobID();
    register_ptJobCurScanpageNumber();
    register_ptScanJobTray();
    register_ptScanJobpaperSize();
    register_ptScanJobpaperMedia();
    register_ptScanJobMemory();
    register_ptScanJobVia();
    register_ptScanJobOwner();
    //register_ptScanJobID();
    register_ptScanJobAliveFlag();

    register_ptCopyJobIndex();
    register_ptCopyJobID();
    register_ptJobCurCopypageNumber();
    register_ptCopyJobTray();
    register_ptCopyJobpaperSize();
    register_ptCopyJobpaperMedia();
    register_ptCopyJobMemory();
    register_ptCopyJobVia();
    register_ptCopyJobOwner();
    //register_ptCopyJobID();
    register_ptCopyJobAliveFlag();

    register_ptFaxJobIndex();
    register_ptFaxJobID();
    register_ptJobCurFaxpageNumber();
    register_ptFaxJobTray();
    register_ptFaxJobpaperSize();
    register_ptFaxJobpaperMedia();
    register_ptFaxJobMemory();
    register_ptFaxJobVia();
    register_ptFaxJobOwner();
    //register_ptFaxJobID();
    register_ptFaxJobAliveFlag();

    register_ptEngineCounter();
    register_ptPrintCounterTotal();
    register_ptPrintCounterColor();
    register_ptPrintCounterMono();
    register_ptPrintCounterDuplex();
    register_ptPrintCounterA3();
    register_ptPrintCounterA4();
    register_ptPrintCounterA5();
    register_ptPrintCounterB4();
    register_ptPrintCounterJISB5();
    register_ptPrintCounterIOSB5();
    register_ptPrintCounter11x17();
    register_ptPrintCounterLetter();
    register_ptPrintCounterLegal();
    register_ptPrintCounterFolio();
    register_ptPrintCounterOficio();
    register_ptPrintCounterExec();
    register_ptPrintCounterStatement();
    register_ptPrintCounter8k();
    register_ptPrintCounter16k();
    register_ptPrintCounterUserdef();
    register_ptPrintCounterA6();
    register_ptPrintCounterEnv10();
    register_ptPrintCounterEnvMon();
    register_ptPrintCounterEnvC6();
    register_ptPrintCounterEnvC5();
    register_ptPrintCounterEnvDL();
    register_ptPrintCounterPosterCard();
    register_ptPrintCounterEquPages();
    register_ptPrintCounterColor_A3();
    register_ptPrintCounterMono_A3();

    register_ptScanCounterTotal();
    register_ptScanCounterColor();
    register_ptScanCounterMono();
    register_ptScanCounterDuplex();
    register_ptScanCounterA3();
    register_ptScanCounterA4();
    register_ptScanCounterA5();
    register_ptScanCounterB4();
    register_ptScanCounterJISB5();
    register_ptScanCounterIOSB5();
    register_ptScanCounter11x17();
    register_ptScanCounterLetter();
    register_ptScanCounterLegal();
    register_ptScanCounterFolio();
    register_ptScanCounterOficio();
    register_ptScanCounterExec();
    register_ptScanCounterStatement();
    register_ptScanCounter8k();
    register_ptScanCounter16k();
    register_ptScanCounterUserdef();
    register_ptScanCounterA6();
    register_ptScanCounterEnv10();
    register_ptScanCounterEnvMon();
    register_ptScanCounterEnvC6();
    register_ptScanCounterEnvC5();
    register_ptScanCounterEnvDL();
    register_ptScanCounterPosterCard();
    register_ptScanCounterEquPages();
    register_ptScanCounterColor_A3();
    register_ptScanCounterMono_A3();


    register_ptCopyCounterTotal();
    register_ptCopyCounterColor();
    register_ptCopyCounterMono();
    register_ptCopyCounterDuplex();
    register_ptCopyCounterA3();
    register_ptCopyCounterA4();
    register_ptCopyCounterA5();
    register_ptCopyCounterB4();
    register_ptCopyCounterJISB5();
    register_ptCopyCounterIOSB5();
    register_ptCopyCounter11x17();
    register_ptCopyCounterLetter();
    register_ptCopyCounterLegal();
    register_ptCopyCounterFolio();
    register_ptCopyCounterOficio();
    register_ptCopyCounterExec();
    register_ptCopyCounterStatement();
    register_ptCopyCounter8k();
    register_ptCopyCounter16k();
    register_ptCopyCounterUserdef();
    register_ptCopyCounterA6();
    register_ptCopyCounterEnv10();
    register_ptCopyCounterEnvMon();
    register_ptCopyCounterEnvC6();
    register_ptCopyCounterEnvC5();
    register_ptCopyCounterEnvDL();
    register_ptCopyCounterPosterCard();
    register_ptCopyCounterEquPages();
    register_ptCopyCounterColor_A3();
    register_ptCopyCounterMono_A3();

    register_ptFaxCounterTotal();
    register_ptFaxCounterColor();
    register_ptFaxCounterMono();
    register_ptFaxCounterDuplex();
    register_ptFaxCounterA3();
    register_ptFaxCounterA4();
    register_ptFaxCounterA5();
    register_ptFaxCounterB4();
    register_ptFaxCounterJISB5();
    register_ptFaxCounterIOSB5();
    register_ptFaxCounter11x17();
    register_ptFaxCounterLetter();
    register_ptFaxCounterLegal();
    register_ptFaxCounterFolio();
    register_ptFaxCounterOficio();
    register_ptFaxCounterExec();
    register_ptFaxCounterStatement();
    register_ptFaxCounter8k();
    register_ptFaxCounter16k();
    register_ptFaxCounterUserdef();
    register_ptFaxCounterA6();
    register_ptFaxCounterEnv10();
    register_ptFaxCounterEnvMon();
    register_ptFaxCounterEnvC6();
    register_ptFaxCounterEnvC5();
    register_ptFaxCounterEnvDL();
    register_ptFaxCounterPosterCard();
    register_ptFaxCounterEquPages();
    register_ptFaxCounterColor_A3();
    register_ptFaxCounterMono_A3();

    register_ptQueryStaticFeature();
    register_ptQueryDynamicFeature();
    register_ptQueryAuditJobsInfo();
    register_ptQueryTRC600Info();
    register_ptQueryTRC1200Info();
    register_ptQueryTRC2400Info();

    register_ptPrinterHostname();
    register_ptIpMethod();

    register_ptWiredConnectStatus();
    register_ptWiredIpMode();
    register_ptWiredIPAddress();
    register_ptWiredSubnetMask();
    register_ptWiredGateway();
    register_ptWiredDNSServerAddress();
    register_ptWiredIpv6LinkLocalAddress();
    register_ptWiredIpv6ManuEnableFlag();
    register_ptWiredIpv6ManuConfigureAddr();
    register_ptWiredIpv6ManuGatewayAddr();
    register_ptWiredIpv6AutoConfigureAddr1();
    register_ptWiredIpv6AutoGatewayAddr();
    register_ptWiredIpv6ManuConfigureAddrMark();
    register_ptWiredIpv6AutoConfigureAddrMark1();
    register_ptWiredInterfaceRate();

    register_ptWirelessStatus();
    register_ptWirelessIpMode();
    register_ptWirelessIPAddress();
    register_ptWirelessSubnetMask();
    register_ptWirelessGateway();
    register_ptWirelessDNSServerAddr();
    register_ptWirelessIpv6LinkLocalAddress();
    register_ptWirelessIpv6ManuEnableFlag();
    register_ptWirelessIpv6ManuConfigureAddr();
    register_ptWirelessIpv6ManuGatewayAddr();
    register_ptWirelessIpv6AutoConfigureAddr1();
    register_ptWirelessIpv6AutoGatewayAddr();
    register_ptWirelessIpv6ManuConfigureAddrMark();
    register_ptWirelessIpv6AutoConfigureAddrMark1();
    register_ptWirelessSSID();
    register_ptWirelessType();
    register_ptEncryptionProtocol();
    register_ptWEPIndex();
    register_ptWirelessWEPPassword();
    register_ptWirelessWPAPSKPassword();

    register_ptDHCPPort();
    register_ptDHCPEnable();
    register_ptDHCPv6Enable();
    register_ptDHCPSPort();
    register_ptDHCPSEnable();
    register_ptHTTPPort();
    register_ptHTTPEnable();
    register_ptHTTPNumLinks();
    register_ptHTTPBytesRemaining();
    register_ptHTTPLinksIndex();
    register_ptHTTPLinksStatus();
    register_ptHTTPLinksLabel();
    register_ptHTTPLinksURL();
    register_ptHTTPConfigEnable();
    register_ptHTTPSPort();
    register_ptHTTPSEnable();
    register_ptLPDPort();
    register_ptLPDEnable();
    register_ptWSDPort();
    register_ptWSDEnable();
    register_ptSNMPPort();
    register_ptSNMPEnable();
    register_ptSNMPProtocolVer();
    register_ptSMTPPort();
    register_ptSMTPEnable();
    register_ptFTPPort();
    register_ptFTPEnable();
    register_ptSMBPort();
    register_ptSMBEnable();
    register_ptSMBAuthentication();
    register_ptSLPPort();
    register_ptSLPEnable();
    register_ptIPPPort();
    register_ptIPPEnable();
    register_ptSNTPPort();
    register_ptSNTPEnable();
    register_ptmDNSPort();
    register_ptmDNSEnable();
    register_ptDNSPort();
    register_ptDNSEnable();
    register_ptWiredIPv4DNSAllocationMethod();
    register_ptBonjourPort();
    register_ptBonjourEnable();
    register_ptTelnetPort();
    register_ptTelnetEnable();
    register_ptUDPPort();
    register_ptUDPEnable();
    register_pt9100Port();
    register_pt9100Enable();
    register_ptSoapPort();
    register_ptSoapEnable();
    register_ptNetBiosPort();
    register_ptNetBiosEnable();
    register_ptLLMNRPort();
    register_ptLLMNREnable();
    register_ptOpenSSLPort();
    register_ptOpenSSLEnable();
    register_ptAirPrintPort();
    register_ptAirPrintEnable();
    register_ptAirScanPort();
    register_ptAirScanEnable();
    register_ptMopriaPort();
    register_ptMopriaEnable();
    register_ptLLTDPort();
    register_ptLLTDEnable();
    register_ptNetScanPort();
    register_ptNetScanEnable();
    register_ptARPPort();
    register_ptARPEnable();
    register_ptLLAPort();
    register_ptLLAEnable();
    register_ptTFTPPort();
    register_ptTFTPEnable();
    register_ptNetservicePort();
    register_ptNetserviceEnable();
    register_ptWi_FiPort();
    register_ptWi_FiEnable();
    register_ptBluetoothPort();
    register_ptBluetoothEnable();
    register_ptEthernetPort();
    register_ptEthernetEnable();
    register_pt802_1XPort();
    register_pt802_1XEnable();
    register_pt802_1XAuthenticationMethod();
    register_ptRAWPort();
    register_ptRAWEnable();
    register_ptLDAPPort();
    register_ptLDAPEnable();

    register_ptPrintStatAllSizeTotalAll();
    register_ptPrintStatAllSizeTotalSingle();
    register_ptPrintStatAllSizeTotalDuplex();
    register_ptPrintStatAllSizeBlackTotal();
    register_ptPrintStatAllSizeBlackSingle();
    register_ptPrintStatAllSizeBlackDuplex();
    register_ptPrintStatAllSizeColorAll();
    register_ptPrintStatAllSizeColorSingle();
    register_ptPrintStatAllSizeColorDuplex();
    register_ptPrintStatUserdefTotalAll();
    register_ptPrintStatUserdefTotalSingle();
    register_ptPrintStatUserdefTotalDuplex();
    register_ptPrintStatUserdefBlackAll();
    register_ptPrintStatUserdefBlackSingle();
    register_ptPrintStatUserdefBlackDuplex();
    register_ptPrintStatUserdefColorAll();
    register_ptPrintStatUserdefColorSingle();
    register_ptPrintStatUserdefColorDuplex();
    register_ptPrintStatA3TotalAll();
    register_ptPrintStatA3TotalSingle();
    register_ptPrintStatA3TotalDuplex();
    register_ptPrintStatA3BlackAll();
    register_ptPrintStatA3BlackSingle();
    register_ptPrintStatA3BlackDuplex();
    register_ptPrintStatA3ColorAll();
    register_ptPrintStatA3ColorSingle();
    register_ptPrintStatA3ColorDuplex();
    register_ptPrintStatA4TotalAll();
    register_ptPrintStatA4TotalSingle();
    register_ptPrintStatA4TotalDuplex();
    register_ptPrintStatA4BlackAll();
    register_ptPrintStatA4BlackSingle();
    register_ptPrintStatA4BlackDuplex();
    register_ptPrintStatA4ColorAll();
    register_ptPrintStatA4ColorSingle();
    register_ptPrintStatA4ColorDuplex();
    register_ptPrintStatA5TotalAll();
    register_ptPrintStatA5TotalSingle();
    register_ptPrintStatA5TotalDuplex();
    register_ptPrintStatA5BlackAll();
    register_ptPrintStatA5BlackSingle();
    register_ptPrintStatA5BlackDuplex();
    register_ptPrintStatA5ColorAll();
    register_ptPrintStatA5ColorSingle();
    register_ptPrintStatA5ColorDuplex();
    register_ptPrintStatB4TotalAll();
    register_ptPrintStatB4TotalSingle();
    register_ptPrintStatB4TotalDuplex();
    register_ptPrintStatB4BlackAll();
    register_ptPrintStatB4BlackSingle();
    register_ptPrintStatB4BlackDuplex();
    register_ptPrintStatB4ColorAll();
    register_ptPrintStatB4ColorSingle();
    register_ptPrintStatB4ColorDuplex();
    register_ptPrintStatJISB5TotalAll();
    register_ptPrintStatJISB5TotalSingle();
    register_ptPrintStatJISB5TotalDuplex();
    register_ptPrintStatJISB5BlackAll();
    register_ptPrintStatJISB5BlackSingle();
    register_ptPrintStatJISB5BlackDuplex();
    register_ptPrintStatJISB5ColorAll();
    register_ptPrintStatJISB5ColorSingle();
    register_ptPrintStatJISB5ColorDuplex();
    register_ptPrintStatIOSB5TotalAll();
    register_ptPrintStatIOSB5TotalSingle();
    register_ptPrintStatIOSB5TotalDuplex();
    register_ptPrintStatIOSB5BlackAll();
    register_ptPrintStatIOSB5BlackSingle();
    register_ptPrintStatIOSB5BlackDuplex();
    register_ptPrintStatIOSB5ColorAll();
    register_ptPrintStatIOSB5ColorSingle();
    register_ptPrintStatIOSB5ColorDuplex();
    register_ptPrintStat11x17TotalAll();
    register_ptPrintStat11x17TotalSingle();
    register_ptPrintStat11x17TotalDuplex();
    register_ptPrintStat11x17BlackAll();
    register_ptPrintStat11x17BlackSingle();
    register_ptPrintStat11x17BlackDuplex();
    register_ptPrintStat11x17ColorAll();
    register_ptPrintStat11x17ColorSingle();
    register_ptPrintStat11x17ColorDuplex();
    register_ptPrintStatLetterTotalAll();
    register_ptPrintStatLetterTotalSingle();
    register_ptPrintStatLetterTotalDuplex();
    register_ptPrintStatLetterBlackAll();
    register_ptPrintStatLetterBlackSingle();
    register_ptPrintStatLetterBlackDuplex();
    register_ptPrintStatLetterColorAll();
    register_ptPrintStatLetterColorSingle();
    register_ptPrintStatLetterColorDuplex();
    register_ptPrintStatLegalTotalAll();
    register_ptPrintStatLegalTotalSingle();
    register_ptPrintStatLegalTotalDuplex();
    register_ptPrintStatLegalBlackAll();
    register_ptPrintStatLegalBlackSingle();
    register_ptPrintStatLegalBlackDuplex();
    register_ptPrintStatLegalColorAll();
    register_ptPrintStatLegalColorSingle();
    register_ptPrintStatLegalColorDuplex();
    register_ptPrintStatFilioTotalAll();
    register_ptPrintStatFilioTotalSingle();
    register_ptPrintStatFilioTotalDuplex();
    register_ptPrintStatFilioBlackAll();
    register_ptPrintStatFilioBlackSingle();
    register_ptPrintStatFilioBlackDuplex();
    register_ptPrintStatFilioColorAll();
    register_ptPrintStatFilioColorSingle();
    register_ptPrintStatFilioColorDuplex();
    register_ptPrintStatOficioTotalAll();
    register_ptPrintStatOficioTotalSingle();
    register_ptPrintStatOficioTotalDuplex();
    register_ptPrintStatOficioBlackAll();
    register_ptPrintStatOficioBlackSingle();
    register_ptPrintStatOficioBlackDuplex();
    register_ptPrintStatOficioColorAll();
    register_ptPrintStatOficioColorSingle();
    register_ptPrintStatOficioColorDuplex();
    register_ptPrintStatExecutiveTotalAll();
    register_ptPrintStatExecutiveTotalSingle();
    register_ptPrintStatExecutiveTotalDuplex();
    register_ptPrintStatExecutiveBlackAll();
    register_ptPrintStatExecutiveBlackSingle();
    register_ptPrintStatExecutiveBlackDuplex();
    register_ptPrintStatExecutiveColorAll();
    register_ptPrintStatExecutiveColorSingle();
    register_ptPrintStatExecutiveColorDuplex();
    register_ptPrintStatStatementTotalAll();
    register_ptPrintStatStatementTotalSingle();
    register_ptPrintStatStatementTotalDuplex();
    register_ptPrintStatStatementBlackAll();
    register_ptPrintStatStatementBlackSingle();
    register_ptPrintStatStatementBlackDuplex();
    register_ptPrintStatStatementColorAll();
    register_ptPrintStatStatementColorSingle();
    register_ptPrintStatStatementColorDuplex();
    register_ptPrintStat8KTotalAll();
    register_ptPrintStat8KTotalSingle();
    register_ptPrintStat8KTotalDuplex();
    register_ptPrintStat8KBlackAll();
    register_ptPrintStat8KBlackSingle();
    register_ptPrintStat8KBlackDuplex();
    register_ptPrintStat8KColorAll();
    register_ptPrintStat8KColorSingle();
    register_ptPrintStat8KColorDuplex();
    register_ptPrintStat16KTotalAll();
    register_ptPrintStat16KTotalSingle();
    register_ptPrintStat16KTotalDuplex();
    register_ptPrintStat16KBlackAll();
    register_ptPrintStat16KBlackSingle();
    register_ptPrintStat16KBlackDuplex();
    register_ptPrintStat16KColorAll();
    register_ptPrintStat16KColorSingle();
    register_ptPrintStat16KColorDuplex();
    register_ptPrintStatA6TotalAll();
    register_ptPrintStatA6TotalSingle();
    register_ptPrintStatA6TotalDuplex();
    register_ptPrintStatA6BlackAll();
    register_ptPrintStatA6BlackSingle();
    register_ptPrintStatA6BlackDuplex();
    register_ptPrintStatA6ColorAll();
    register_ptPrintStatA6ColorSingle();
    register_ptPrintStatA6ColorDuplex();
    register_ptPrintStatEnv10TotalAll();
    register_ptPrintStatEnv10TotalSingle();
    register_ptPrintStatEnv10TotalDuplex();
    register_ptPrintStatEnv10BlackAll();
    register_ptPrintStatEnv10BlackSingle();
    register_ptPrintStatEnv10BlackDuplex();
    register_ptPrintStatEnv10ColorAll();
    register_ptPrintStatEnv10ColorSingle();
    register_ptPrintStatEnv10ColorDuplex();
    register_ptPrintStatEnvMonTotalAll();
    register_ptPrintStatEnvMonTotalSingle();
    register_ptPrintStatEnvMonTotalDuplex();
    register_ptPrintStatEnvMonBlackAll();
    register_ptPrintStatEnvMonBlackSingle();
    register_ptPrintStatEnvMonBlackDuplex();
    register_ptPrintStatEnvMonColorAll();
    register_ptPrintStatEnvMonColorSingle();
    register_ptPrintStatEnvMonColorDuplex();
    register_ptPrintStatEnvC6TotalAll();
    register_ptPrintStatEnvC6TotalSingle();
    register_ptPrintStatEnvC6TotalDuplex();
    register_ptPrintStatEnvC6BlackAll();
    register_ptPrintStatEnvC6BlackSingle();
    register_ptPrintStatEnvC6BlackDuplex();
    register_ptPrintStatEnvC6ColorAll();
    register_ptPrintStatEnvC6ColorSingle();
    register_ptPrintStatEnvC6ColorDuplex();
    register_ptPrintStatEnvC5TotalAll();
    register_ptPrintStatEnvC5TotalSingle();
    register_ptPrintStatEnvC5TotalDuplex();
    register_ptPrintStatEnvC5BlackAll();
    register_ptPrintStatEnvC5BlackSingle();
    register_ptPrintStatEnvC5BlackDuplex();
    register_ptPrintStatEnvC5ColorAll();
    register_ptPrintStatEnvC5ColorSingle();
    register_ptPrintStatEnvC5ColorDuplex();
    register_ptPrintStatEnvDLTotalAll();
    register_ptPrintStatEnvDLTotalSingle();
    register_ptPrintStatEnvDLTotalDuplex();
    register_ptPrintStatEnvDLBlackAll();
    register_ptPrintStatEnvDLBlackSingle();
    register_ptPrintStatEnvDLBlackDuplex();
    register_ptPrintStatEnvDLColorAll();
    register_ptPrintStatEnvDLColorSingle();
    register_ptPrintStatEnvDLColorDuplex();
    register_ptPrintStatPostcardTotalAll();
    register_ptPrintStatPostcardTotalSingle();
    register_ptPrintStatPostcardTotalDuplex();
    register_ptPrintStatPostcardBlackAll();
    register_ptPrintStatPostcardBlackSingle();
    register_ptPrintStatPostcardBlackDuplex();
    register_ptPrintStatPostcardColorAll();
    register_ptPrintStatPostcardColorSingle();
    register_ptPrintStatPostcardColorDuplex();

    register_ptScanStatAllSizeTotalAll();
    register_ptScanStatAllSizeTotalSingle();
    register_ptScanStatAllSizeTotalDuplex();
    register_ptScanStatAllSizeBlackTotal();
    register_ptScanStatAllSizeBlackSingle();
    register_ptScanStatAllSizeBlackDuplex();
    register_ptScanStatAllSizeColorAll();
    register_ptScanStatAllSizeColorSingle();
    register_ptScanStatAllSizeColorDuplex();
    register_ptScanStatUserdefTotalAll();
    register_ptScanStatUserdefTotalSingle();
    register_ptScanStatUserdefTotalDuplex();
    register_ptScanStatUserdefBlackAll();
    register_ptScanStatUserdefBlackSingle();
    register_ptScanStatUserdefBlackDuplex();
    register_ptScanStatUserdefColorAll();
    register_ptScanStatUserdefColorSingle();
    register_ptScanStatUserdefColorDuplex();
    register_ptScanStatA3TotalAll();
    register_ptScanStatA3TotalSingle();
    register_ptScanStatA3TotalDuplex();
    register_ptScanStatA3BlackAll();
    register_ptScanStatA3BlackSingle();
    register_ptScanStatA3BlackDuplex();
    register_ptScanStatA3ColorAll();
    register_ptScanStatA3ColorSingle();
    register_ptScanStatA3ColorDuplex();
    register_ptScanStatA4TotalAll();
    register_ptScanStatA4TotalSingle();
    register_ptScanStatA4TotalDuplex();
    register_ptScanStatA4BlackAll();
    register_ptScanStatA4BlackSingle();
    register_ptScanStatA4BlackDuplex();
    register_ptScanStatA4ColorAll();
    register_ptScanStatA4ColorSingle();
    register_ptScanStatA4ColorDuplex();
    register_ptScanStatA5TotalAll();
    register_ptScanStatA5TotalSingle();
    register_ptScanStatA5TotalDuplex();
    register_ptScanStatA5BlackAll();
    register_ptScanStatA5BlackSingle();
    register_ptScanStatA5BlackDuplex();
    register_ptScanStatA5ColorAll();
    register_ptScanStatA5ColorSingle();
    register_ptScanStatA5ColorDuplex();
    register_ptScanStatB4TotalAll();
    register_ptScanStatB4TotalSingle();
    register_ptScanStatB4TotalDuplex();
    register_ptScanStatB4BlackAll();
    register_ptScanStatB4BlackSingle();
    register_ptScanStatB4BlackDuplex();
    register_ptScanStatB4ColorAll();
    register_ptScanStatB4ColorSingle();
    register_ptScanStatB4ColorDuplex();
    register_ptScanStatJISB5TotalAll();
    register_ptScanStatJISB5TotalSingle();
    register_ptScanStatJISB5TotalDuplex();
    register_ptScanStatJISB5BlackAll();
    register_ptScanStatJISB5BlackSingle();
    register_ptScanStatJISB5BlackDuplex();
    register_ptScanStatJISB5ColorAll();
    register_ptScanStatJISB5ColorSingle();
    register_ptScanStatJISB5ColorDuplex();
    register_ptScanStatIOSB5TotalAll();
    register_ptScanStatIOSB5TotalSingle();
    register_ptScanStatIOSB5TotalDuplex();
    register_ptScanStatIOSB5BlackAll();
    register_ptScanStatIOSB5BlackSingle();
    register_ptScanStatIOSB5BlackDuplex();
    register_ptScanStatIOSB5ColorAll();
    register_ptScanStatIOSB5ColorSingle();
    register_ptScanStatIOSB5ColorDuplex();
    register_ptScanStat11x17TotalAll();
    register_ptScanStat11x17TotalSingle();
    register_ptScanStat11x17TotalDuplex();
    register_ptScanStat11x17BlackAll();
    register_ptScanStat11x17BlackSingle();
    register_ptScanStat11x17BlackDuplex();
    register_ptScanStat11x17ColorAll();
    register_ptScanStat11x17ColorSingle();
    register_ptScanStat11x17ColorDuplex();
    register_ptScanStatLetterTotalAll();
    register_ptScanStatLetterTotalSingle();
    register_ptScanStatLetterTotalDuplex();
    register_ptScanStatLetterBlackAll();
    register_ptScanStatLetterBlackSingle();
    register_ptScanStatLetterBlackDuplex();
    register_ptScanStatLetterColorAll();
    register_ptScanStatLetterColorSingle();
    register_ptScanStatLetterColorDuplex();
    register_ptScanStatLegalTotalAll();
    register_ptScanStatLegalTotalSingle();
    register_ptScanStatLegalTotalDuplex();
    register_ptScanStatLegalBlackAll();
    register_ptScanStatLegalBlackSingle();
    register_ptScanStatLegalBlackDuplex();
    register_ptScanStatLegalColorAll();
    register_ptScanStatLegalColorSingle();
    register_ptScanStatLegalColorDuplex();
    register_ptScanStatFilioTotalAll();
    register_ptScanStatFilioTotalSingle();
    register_ptScanStatFilioTotalDuplex();
    register_ptScanStatFilioBlackAll();
    register_ptScanStatFilioBlackSingle();
    register_ptScanStatFilioBlackDuplex();
    register_ptScanStatFilioColorAll();
    register_ptScanStatFilioColorSingle();
    register_ptScanStatFilioColorDuplex();
    register_ptScanStatOficioTotalAll();
    register_ptScanStatOficioTotalSingle();
    register_ptScanStatOficioTotalDuplex();
    register_ptScanStatOficioBlackAll();
    register_ptScanStatOficioBlackSingle();
    register_ptScanStatOficioBlackDuplex();
    register_ptScanStatOficioColorAll();
    register_ptScanStatOficioColorSingle();
    register_ptScanStatOficioColorDuplex();
    register_ptScanStatExecutiveTotalAll();
    register_ptScanStatExecutiveTotalSingle();
    register_ptScanStatExecutiveTotalDuplex();
    register_ptScanStatExecutiveBlackAll();
    register_ptScanStatExecutiveBlackSingle();
    register_ptScanStatExecutiveBlackDuplex();
    register_ptScanStatExecutiveColorAll();
    register_ptScanStatExecutiveColorSingle();
    register_ptScanStatExecutiveColorDuplex();
    register_ptScanStatStatementTotalAll();
    register_ptScanStatStatementTotalSingle();
    register_ptScanStatStatementTotalDuplex();
    register_ptScanStatStatementBlackAll();
    register_ptScanStatStatementBlackSingle();
    register_ptScanStatStatementBlackDuplex();
    register_ptScanStatStatementColorAll();
    register_ptScanStatStatementColorSingle();
    register_ptScanStatStatementColorDuplex();
    register_ptScanStat8KTotalAll();
    register_ptScanStat8KTotalSingle();
    register_ptScanStat8KTotalDuplex();
    register_ptScanStat8KBlackAll();
    register_ptScanStat8KBlackSingle();
    register_ptScanStat8KBlackDuplex();
    register_ptScanStat8KColorAll();
    register_ptScanStat8KColorSingle();
    register_ptScanStat8KColorDuplex();
    register_ptScanStat16KTotalAll();
    register_ptScanStat16KTotalSingle();
    register_ptScanStat16KTotalDuplex();
    register_ptScanStat16KBlackAll();
    register_ptScanStat16KBlackSingle();
    register_ptScanStat16KBlackDuplex();
    register_ptScanStat16KColorAll();
    register_ptScanStat16KColorSingle();
    register_ptScanStat16KColorDuplex();
    register_ptScanStatA6TotalAll();
    register_ptScanStatA6TotalSingle();
    register_ptScanStatA6TotalDuplex();
    register_ptScanStatA6BlackAll();
    register_ptScanStatA6BlackSingle();
    register_ptScanStatA6BlackDuplex();
    register_ptScanStatA6ColorAll();
    register_ptScanStatA6ColorSingle();
    register_ptScanStatA6ColorDuplex();
    register_ptScanStatEnv10TotalAll();
    register_ptScanStatEnv10TotalSingle();
    register_ptScanStatEnv10TotalDuplex();
    register_ptScanStatEnv10BlackAll();
    register_ptScanStatEnv10BlackSingle();
    register_ptScanStatEnv10BlackDuplex();
    register_ptScanStatEnv10ColorAll();
    register_ptScanStatEnv10ColorSingle();
    register_ptScanStatEnv10ColorDuplex();
    register_ptScanStatEnvMonTotalAll();
    register_ptScanStatEnvMonTotalSingle();
    register_ptScanStatEnvMonTotalDuplex();
    register_ptScanStatEnvMonBlackAll();
    register_ptScanStatEnvMonBlackSingle();
    register_ptScanStatEnvMonBlackDuplex();
    register_ptScanStatEnvMonColorAll();
    register_ptScanStatEnvMonColorSingle();
    register_ptScanStatEnvMonColorDuplex();
    register_ptScanStatEnvC6TotalAll();
    register_ptScanStatEnvC6TotalSingle();
    register_ptScanStatEnvC6TotalDuplex();
    register_ptScanStatEnvC6BlackAll();
    register_ptScanStatEnvC6BlackSingle();
    register_ptScanStatEnvC6BlackDuplex();
    register_ptScanStatEnvC6ColorAll();
    register_ptScanStatEnvC6ColorSingle();
    register_ptScanStatEnvC6ColorDuplex();
    register_ptScanStatEnvC5TotalAll();
    register_ptScanStatEnvC5TotalSingle();
    register_ptScanStatEnvC5TotalDuplex();
    register_ptScanStatEnvC5BlackAll();
    register_ptScanStatEnvC5BlackSingle();
    register_ptScanStatEnvC5BlackDuplex();
    register_ptScanStatEnvC5ColorAll();
    register_ptScanStatEnvC5ColorSingle();
    register_ptScanStatEnvC5ColorDuplex();
    register_ptScanStatEnvDLTotalAll();
    register_ptScanStatEnvDLTotalSingle();
    register_ptScanStatEnvDLTotalDuplex();
    register_ptScanStatEnvDLBlackAll();
    register_ptScanStatEnvDLBlackSingle();
    register_ptScanStatEnvDLBlackDuplex();
    register_ptScanStatEnvDLColorAll();
    register_ptScanStatEnvDLColorSingle();
    register_ptScanStatEnvDLColorDuplex();
    register_ptScanStatPostcardTotalAll();
    register_ptScanStatPostcardTotalSingle();
    register_ptScanStatPostcardTotalDuplex();
    register_ptScanStatPostcardBlackAll();
    register_ptScanStatPostcardBlackSingle();
    register_ptScanStatPostcardBlackDuplex();
    register_ptScanStatPostcardColorAll();
    register_ptScanStatPostcardColorSingle();
    register_ptScanStatPostcardColorDuplex();

    register_ptCopyStatAllSizeTotalAll();
    register_ptCopyStatAllSizeTotalSingle();
    register_ptCopyStatAllSizeTotalDuplex();
    register_ptCopyStatAllSizeBlackTotal();
    register_ptCopyStatAllSizeBlackSingle();
    register_ptCopyStatAllSizeBlackDuplex();
    register_ptCopyStatAllSizeColorAll();
    register_ptCopyStatAllSizeColorSingle();
    register_ptCopyStatAllSizeColorDuplex();
    register_ptCopyStatUserdefTotalAll();
    register_ptCopyStatUserdefTotalSingle();
    register_ptCopyStatUserdefTotalDuplex();
    register_ptCopyStatUserdefBlackAll();
    register_ptCopyStatUserdefBlackSingle();
    register_ptCopyStatUserdefBlackDuplex();
    register_ptCopyStatUserdefColorAll();
    register_ptCopyStatUserdefColorSingle();
    register_ptCopyStatUserdefColorDuplex();
    register_ptCopyStatA3TotalAll();
    register_ptCopyStatA3TotalSingle();
    register_ptCopyStatA3TotalDuplex();
    register_ptCopyStatA3BlackAll();
    register_ptCopyStatA3BlackSingle();
    register_ptCopyStatA3BlackDuplex();
    register_ptCopyStatA3ColorAll();
    register_ptCopyStatA3ColorSingle();
    register_ptCopyStatA3ColorDuplex();
    register_ptCopyStatA4TotalAll();
    register_ptCopyStatA4TotalSingle();
    register_ptCopyStatA4TotalDuplex();
    register_ptCopyStatA4BlackAll();
    register_ptCopyStatA4BlackSingle();
    register_ptCopyStatA4BlackDuplex();
    register_ptCopyStatA4ColorAll();
    register_ptCopyStatA4ColorSingle();
    register_ptCopyStatA4ColorDuplex();
    register_ptCopyStatA5TotalAll();
    register_ptCopyStatA5TotalSingle();
    register_ptCopyStatA5TotalDuplex();
    register_ptCopyStatA5BlackAll();
    register_ptCopyStatA5BlackSingle();
    register_ptCopyStatA5BlackDuplex();
    register_ptCopyStatA5ColorAll();
    register_ptCopyStatA5ColorSingle();
    register_ptCopyStatA5ColorDuplex();
    register_ptCopyStatB4TotalAll();
    register_ptCopyStatB4TotalSingle();
    register_ptCopyStatB4TotalDuplex();
    register_ptCopyStatB4BlackAll();
    register_ptCopyStatB4BlackSingle();
    register_ptCopyStatB4BlackDuplex();
    register_ptCopyStatB4ColorAll();
    register_ptCopyStatB4ColorSingle();
    register_ptCopyStatB4ColorDuplex();
    register_ptCopyStatJISB5TotalAll();
    register_ptCopyStatJISB5TotalSingle();
    register_ptCopyStatJISB5TotalDuplex();
    register_ptCopyStatJISB5BlackAll();
    register_ptCopyStatJISB5BlackSingle();
    register_ptCopyStatJISB5BlackDuplex();
    register_ptCopyStatJISB5ColorAll();
    register_ptCopyStatJISB5ColorSingle();
    register_ptCopyStatJISB5ColorDuplex();
    register_ptCopyStatIOSB5TotalAll();
    register_ptCopyStatIOSB5TotalSingle();
    register_ptCopyStatIOSB5TotalDuplex();
    register_ptCopyStatIOSB5BlackAll();
    register_ptCopyStatIOSB5BlackSingle();
    register_ptCopyStatIOSB5BlackDuplex();
    register_ptCopyStatIOSB5ColorAll();
    register_ptCopyStatIOSB5ColorSingle();
    register_ptCopyStatIOSB5ColorDuplex();
    register_ptCopyStat11x17TotalAll();
    register_ptCopyStat11x17TotalSingle();
    register_ptCopyStat11x17TotalDuplex();
    register_ptCopyStat11x17BlackAll();
    register_ptCopyStat11x17BlackSingle();
    register_ptCopyStat11x17BlackDuplex();
    register_ptCopyStat11x17ColorAll();
    register_ptCopyStat11x17ColorSingle();
    register_ptCopyStat11x17ColorDuplex();
    register_ptCopyStatLetterTotalAll();
    register_ptCopyStatLetterTotalSingle();
    register_ptCopyStatLetterTotalDuplex();
    register_ptCopyStatLetterBlackAll();
    register_ptCopyStatLetterBlackSingle();
    register_ptCopyStatLetterBlackDuplex();
    register_ptCopyStatLetterColorAll();
    register_ptCopyStatLetterColorSingle();
    register_ptCopyStatLetterColorDuplex();
    register_ptCopyStatLegalTotalAll();
    register_ptCopyStatLegalTotalSingle();
    register_ptCopyStatLegalTotalDuplex();
    register_ptCopyStatLegalBlackAll();
    register_ptCopyStatLegalBlackSingle();
    register_ptCopyStatLegalBlackDuplex();
    register_ptCopyStatLegalColorAll();
    register_ptCopyStatLegalColorSingle();
    register_ptCopyStatLegalColorDuplex();
    register_ptCopyStatFilioTotalAll();
    register_ptCopyStatFilioTotalSingle();
    register_ptCopyStatFilioTotalDuplex();
    register_ptCopyStatFilioBlackAll();
    register_ptCopyStatFilioBlackSingle();
    register_ptCopyStatFilioBlackDuplex();
    register_ptCopyStatFilioColorAll();
    register_ptCopyStatFilioColorSingle();
    register_ptCopyStatFilioColorDuplex();
    register_ptCopyStatOficioTotalAll();
    register_ptCopyStatOficioTotalSingle();
    register_ptCopyStatOficioTotalDuplex();
    register_ptCopyStatOficioBlackAll();
    register_ptCopyStatOficioBlackSingle();
    register_ptCopyStatOficioBlackDuplex();
    register_ptCopyStatOficioColorAll();
    register_ptCopyStatOficioColorSingle();
    register_ptCopyStatOficioColorDuplex();
    register_ptCopyStatExecutiveTotalAll();
    register_ptCopyStatExecutiveTotalSingle();
    register_ptCopyStatExecutiveTotalDuplex();
    register_ptCopyStatExecutiveBlackAll();
    register_ptCopyStatExecutiveBlackSingle();
    register_ptCopyStatExecutiveBlackDuplex();
    register_ptCopyStatExecutiveColorAll();
    register_ptCopyStatExecutiveColorSingle();
    register_ptCopyStatExecutiveColorDuplex();
    register_ptCopyStatStatementTotalAll();
    register_ptCopyStatStatementTotalSingle();
    register_ptCopyStatStatementTotalDuplex();
    register_ptCopyStatStatementBlackAll();
    register_ptCopyStatStatementBlackSingle();
    register_ptCopyStatStatementBlackDuplex();
    register_ptCopyStatStatementColorAll();
    register_ptCopyStatStatementColorSingle();
    register_ptCopyStatStatementColorDuplex();
    register_ptCopyStat8KTotalAll();
    register_ptCopyStat8KTotalSingle();
    register_ptCopyStat8KTotalDuplex();
    register_ptCopyStat8KBlackAll();
    register_ptCopyStat8KBlackSingle();
    register_ptCopyStat8KBlackDuplex();
    register_ptCopyStat8KColorAll();
    register_ptCopyStat8KColorSingle();
    register_ptCopyStat8KColorDuplex();
    register_ptCopyStat16KTotalAll();
    register_ptCopyStat16KTotalSingle();
    register_ptCopyStat16KTotalDuplex();
    register_ptCopyStat16KBlackAll();
    register_ptCopyStat16KBlackSingle();
    register_ptCopyStat16KBlackDuplex();
    register_ptCopyStat16KColorAll();
    register_ptCopyStat16KColorSingle();
    register_ptCopyStat16KColorDuplex();
    register_ptCopyStatA6TotalAll();
    register_ptCopyStatA6TotalSingle();
    register_ptCopyStatA6TotalDuplex();
    register_ptCopyStatA6BlackAll();
    register_ptCopyStatA6BlackSingle();
    register_ptCopyStatA6BlackDuplex();
    register_ptCopyStatA6ColorAll();
    register_ptCopyStatA6ColorSingle();
    register_ptCopyStatA6ColorDuplex();
    register_ptCopyStatEnv10TotalAll();
    register_ptCopyStatEnv10TotalSingle();
    register_ptCopyStatEnv10TotalDuplex();
    register_ptCopyStatEnv10BlackAll();
    register_ptCopyStatEnv10BlackSingle();
    register_ptCopyStatEnv10BlackDuplex();
    register_ptCopyStatEnv10ColorAll();
    register_ptCopyStatEnv10ColorSingle();
    register_ptCopyStatEnv10ColorDuplex();
    register_ptCopyStatEnvMonTotalAll();
    register_ptCopyStatEnvMonTotalSingle();
    register_ptCopyStatEnvMonTotalDuplex();
    register_ptCopyStatEnvMonBlackAll();
    register_ptCopyStatEnvMonBlackSingle();
    register_ptCopyStatEnvMonBlackDuplex();
    register_ptCopyStatEnvMonColorAll();
    register_ptCopyStatEnvMonColorSingle();
    register_ptCopyStatEnvMonColorDuplex();
    register_ptCopyStatEnvC6TotalAll();
    register_ptCopyStatEnvC6TotalSingle();
    register_ptCopyStatEnvC6TotalDuplex();
    register_ptCopyStatEnvC6BlackAll();
    register_ptCopyStatEnvC6BlackSingle();
    register_ptCopyStatEnvC6BlackDuplex();
    register_ptCopyStatEnvC6ColorAll();
    register_ptCopyStatEnvC6ColorSingle();
    register_ptCopyStatEnvC6ColorDuplex();
    register_ptCopyStatEnvC5TotalAll();
    register_ptCopyStatEnvC5TotalSingle();
    register_ptCopyStatEnvC5TotalDuplex();
    register_ptCopyStatEnvC5BlackAll();
    register_ptCopyStatEnvC5BlackSingle();
    register_ptCopyStatEnvC5BlackDuplex();
    register_ptCopyStatEnvC5ColorAll();
    register_ptCopyStatEnvC5ColorSingle();
    register_ptCopyStatEnvC5ColorDuplex();
    register_ptCopyStatEnvDLTotalAll();
    register_ptCopyStatEnvDLTotalSingle();
    register_ptCopyStatEnvDLTotalDuplex();
    register_ptCopyStatEnvDLBlackAll();
    register_ptCopyStatEnvDLBlackSingle();
    register_ptCopyStatEnvDLBlackDuplex();
    register_ptCopyStatEnvDLColorAll();
    register_ptCopyStatEnvDLColorSingle();
    register_ptCopyStatEnvDLColorDuplex();
    register_ptCopyStatPostcardTotalAll();
    register_ptCopyStatPostcardTotalSingle();
    register_ptCopyStatPostcardTotalDuplex();
    register_ptCopyStatPostcardBlackAll();
    register_ptCopyStatPostcardBlackSingle();
    register_ptCopyStatPostcardBlackDuplex();
    register_ptCopyStatPostcardColorAll();
    register_ptCopyStatPostcardColorSingle();
    register_ptCopyStatPostcardColorDuplex();

    return;
}

