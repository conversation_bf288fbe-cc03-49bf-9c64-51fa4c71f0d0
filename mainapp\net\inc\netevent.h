/**************************************************************
 * Copyright (c) 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * module name:     net
 * file name:       netevent.h
 * author:          <PERSON> (<EMAIL>)
 * date:            2021-09-06
 * description:     network event manager client
 **************************************************************/

#ifndef __NETEVENT_H__
#define __NETEVENT_H__

#include "netctx.h"

PT_BEGIN_DECLS

/**
 * @brief       Waiting until the platform reports whether to support cable and wifi.
 * <AUTHOR>
 * @date        2023-9-16
 */
void            netevent_client_waiting_platform_ready(void);

/**
 * @brief       Destruct netevent client object.
 * @param[in]   net_ctx : The network context pointer.
 * @return      Construct result
 * @retval      ==0     : success\n
 *              < 0     : fail
 * <AUTHOR>
 * @date        2023-9-16
 */
int32_t         netevent_client_construct(NET_CTX_S* net_ctx);

/**
 * @brief       Destruct netevent client object.
 * @param[in]   net_ctx : The network context pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            netevent_client_destruct(NET_CTX_S* net_ctx);

PT_END_DECLS

#endif /* __NETEVENT_H__ */
