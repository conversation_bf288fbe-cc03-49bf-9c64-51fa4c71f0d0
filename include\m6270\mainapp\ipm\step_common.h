/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       step_common.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-10-29
 * @version    v1.0
 * @details    this is step layout API
 */

#ifndef STEP_COMMON_H
#define STEP_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "step_core.h"
#include "pol/pol_threads.h"
#include "pol/pol_string.h"
#include "memmgr/memmgr.h"

#define IMAGE_LOCK(i)   pi_mutex_lock((i)->lock)    ///< image lock
#define IMAGE_UNLOCK(i) pi_mutex_unlock((i)->lock)  ///< image unlock
/**
 * @brief Copy the dimensions of image src to image dst
 */

#define     IMAGEcopyDimensions(dst, src)       \
            (dst)->w    = (src)->w;             \
            (dst)->h    = (src)->h;             \
            (dst)->d    = (src)->d;             \
            (dst)->s    = (src)->s;             \
            (dst)->f    = (src)->f;             \
            (dst)->xres = (src)->xres;          \
            (dst)->yres = (src)->yres;          \
            (dst)->bandh= (src)->bandh;          \
            (dst)->page_num = (src)->page_num;   \
            (dst)->page_type= (src)->page_type;    \
            (dst)->top_skip_line    = (src)->top_skip_line;     \
            (dst)->down_skip_line   = (src)->down_skip_line;     \
            (dst)->left_skip_pixel  = (src)->left_skip_pixel;     \
            (dst)->right_skip_pixel = (src)->right_skip_pixel;

/**
 * @brief IP_STEP Open interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] parms  STEP params
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_step_open(IP_STEP_P pstep, char* parms);

/**
 * @brief IP_STEP CLOSE interface api function types
 * @param[in] IP_STEP_P STEP object
 * @return void \n
 * @retval null
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void pi_step_close(IP_STEP_P pstep);

/**
 * @brief IP_STEP PENDING interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] readable step readable
 * @param[in] writeable step writeable
 * @param[in] status_in step status_in
 * @param[in] status_in step status_in
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_step_pend(
                            IP_STEP_P pstep,
                            int* readable, int* writeable,
                            IP_STEP_STATUS_E instatus, IP_STEP_STATUS_E* status
                         );

/**
 * @brief IP_STEP READ interface api function types
 * @param[in] IP_STEP_P STEP object
 * @return IMAGE_P \n
 * @retval image object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
IMAGE_P pi_step_read(IP_STEP_P pstep);

/**
 * @brief IP_STEP WRITE interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] image image object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_step_write(IP_STEP_P pstep, IMAGE_P image);


/**
 * @brief pi_step_pend_on_row
 * @param[in] IP_STEP_P STEP object
 * @param[in] readable step readable
 * @param[in] writeable step writeable
 * @param[in] status_in step status_in
 * @param[in] status_in step status_in
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_step_pend_on_row(
                    IP_STEP_P pstep,
                    int* readable, int* writeable,
                    IP_STEP_STATUS_E instatus, IP_STEP_STATUS_E* status
                   );

/**
 * @brief pi_step_pend_on_any
 * @param[in] IP_STEP_P STEP object
 * @param[in] readable step readable
 * @param[in] writeable step writeable
 * @param[in] status_in step status_in
 * @param[in] status_in step status_in
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t pi_step_pend_on_any(
                    IP_STEP_P pstep,
                    int* readable, int* writeable,
                    IP_STEP_STATUS_E instatus, IP_STEP_STATUS_E* status
                   );
#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* STEP_COMMON_H */

/**
 *@}
 */


