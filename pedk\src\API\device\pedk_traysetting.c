#include <string.h>
#include <stdio.h>
#include "pedk_traysetting.h"
#include "PEDK_event.h"
#include <quickjs.h>

#define countof(x)              (sizeof(x) / sizeof((x)[0]))

JSValue js_setting_getTrayPaperSize(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
    char * func_str;
    char PaperSize[32] = {0};
    int len = 0;

    func_str = JS_ToCString(ctx, argv[0]);
    if(NULL == func_str)
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,func_str = %s\n",__LINE__,__func__,func_str);

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_SIZE, 0,strlen(func_str)+1,func_str);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }

    len = sizeof(PaperSize);
    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_SIZE, &value,PaperSize,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,PaperSize = %s\n",__LINE__,__func__,PaperSize);

    JS_FreeCString(ctx, func_str);

    return JS_NewString(ctx, PaperSize);

}


JSValue js_setting_getTrayPaperType(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    int ret = 0;
    int value = -1;
    char * func_str;
    char PaperType[32] = {0};
    int len = 0;

    func_str = JS_ToCString(ctx, argv[0]);
    if(NULL == func_str)
    {
        printf("[line:%d]input value error\n",__LINE__);
        return JS_NewString(ctx, "EXIT_FAILURE");
    }
    printf("[line:%d] %s,func_str = %s\n",__LINE__,__func__,func_str);

    SendMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_TYPE, 0,strlen(func_str)+1,func_str);
    if( ret != 0 )
    {
        printf("[line:%d]SendMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }

    len = sizeof(PaperType);
    ret = RecvMsgToMfp(MSG_MODULE_SETTING, MSG_SETTING_SUB_TRAY_PAPER_TYPE, &value,PaperType,&len,3);
    if( ret != 0 )
    {
        printf("[line:%d]RecvMsgToMfp error\n",__LINE__);
        return JS_NewInt32(ctx, -1);
    }
    printf("[line:%d] %s,PaperType = %s\n",__LINE__,__func__,PaperType);
    JS_FreeCString(ctx, func_str);

    return JS_NewString(ctx, PaperType);
}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;


/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_tray_setting_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    /* tray_setting */
    {"get_TrayPaperSize",1,js_setting_getTrayPaperSize},
    {"get_TrayPaperType",1,js_setting_getTrayPaperType},
};


const JSCFunctionList* get_TraySetting_JSCFunctionList(int *length)
{
    *length = countof(pesf_tray_setting_funcs);
    return pesf_tray_setting_funcs;
}

int js_tray_setting_init(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;
   int count = 0;

   printf("*********start tray setting module*******\n");
   /* creat the classes */
   const JSCFunctionList* pesf_funcs = get_TraySetting_JSCFunctionList(&count);
   printf("count:%d\n",count);
   for(i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   printf("*********start tray setting init end**********\n");
   return 0;
}
