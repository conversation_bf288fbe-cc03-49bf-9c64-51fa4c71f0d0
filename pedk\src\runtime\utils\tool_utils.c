#include "basic/config.h"
#include "runtime/utils/tool_utils.h"
#include <string.h>
#include <stdlib.h>

#define TOOL_UTILS "tool_utils"

char* make_file_path(const char *app_name,const char *file_name)
{
    char *file_path = NULL;
    unsigned int file_path_len = 0;

    //计算完整路径长途
    file_path_len = strlen(PESF_WORKSPACE_PATH) + strlen("/") + strlen(app_name) + strlen("/") + strlen(file_name) + 1;
    //申请存放路径空间
    file_path = (char*)malloc(file_path_len);
    if(NULL == file_path){
        LOG_E(TOOL_UTILS, "Malloc file_path fail!\n");
        return NULL;
    }

    memset(file_path, 0x00, file_path_len);
    strcpy(file_path, PESF_WORKSPACE_PATH);	
	strcat(file_path, "/");
    strcat(file_path, app_name);
	strcat(file_path, "/");
    strcat(file_path, file_name);

	printf("file_path:%s\n",file_path);

    return file_path;
}