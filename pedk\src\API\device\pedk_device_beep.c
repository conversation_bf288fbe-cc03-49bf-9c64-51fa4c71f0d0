#include "pedk_device_beep.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define countof(x) (sizeof(x) / sizeof((x)[0]) )
#define Log(format, ...) printf("[system_setting] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);

enum volume {
    off,
    low_volume,
    medium_volume,
    high_volume
};

JSValue js_setting_getPrompSoundMode(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    unsigned char buff[5];
    int size = sizeof(buff);
    int ret = 0;
    int i = 0;
    int volume = 0;
    SendMsgToMfp(MSG_MOUDLE_BEEP, MSG_BEEP_GET_VOLUME, 1, 0, NULL);
    RecvMsgToMfp(MSG_MOUDLE_BEEP, MSG_BEEP_GET_VOLUME, &ret, buff, &size, 2);
    for (i = 0; i < size; i++)
    {
        volume = volume|(buff[i]<<(8*i));
    }
    Log("%s : %d %d\n", __func__, size, volume);

    switch(volume)
    {
        case 0:  volume = off;  break;
        case 33:  volume = low_volume;  break;
        case 66:  volume = medium_volume;  break;
        case 99:  volume = high_volume;  break;
        default: volume = off;   break;
    }
    return JS_NewInt32(ctx, volume);
}
JSValue js_setting_setPrompSoundMode(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    int volume = 0;
    int ret = 0;
    JS_ToInt32(ctx, &volume, argv[0]);
    Log("%s : %d %d\n", __func__, argc, volume);

    switch(volume)
    {
        case off:  volume = 0;  break;
        case low_volume:  volume = 33;  break;
        case medium_volume:  volume = 66;  break;
        case high_volume:  volume = 99;  break;
        default: volume = 0;   break;
    }
    
    ret = SendMsgToMfp(MSG_MOUDLE_BEEP, MSG_BEEP_SET_VOLUME, 1, sizeof(volume),  (const unsigned char *)&volume);
    if(ret < 0)
    {
        Log("[js_setting_setPrompSoundMode] js_setting_setPrompSoundMode send err \n");
    }

    return JS_NewString(ctx, "EXIT_SUCCESS");
}

JSValue js_setting_setBellSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    Log("error setBellSoundMode");
    return JS_NewString(ctx, "EXIT_FAILURE");
}

JSValue js_setting_getBellSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    Log("error getBellSoundMode");
    return JS_NewString(ctx, "EXIT_FAILURE");
}
JSValue js_setting_setFaxSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    Log("error setFaxSoundMode");
    return JS_NewString(ctx, "EXIT_FAILURE");
}
JSValue js_setting_getFaxSoundMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    Log("error js_setting_getFaxSoundMode");
    return JS_NewString(ctx, "EXIT_FAILURE");
}
JSValue js_setting_setVolSwitchMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    Log("error js_setting_setVolSwitchMode");
    return JS_NewString(ctx, "EXIT_FAILURE");
}
JSValue js_setting_getVolSwitchMode(JSContext *ctx, JSValueConst this_val,int argc, JSValueConst *argv)
{
    Log("error js_setting_getVolSwitchMode");
    return JS_NewString(ctx, "EXIT_FAILURE");
}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList pesf_setting_system_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */

    /* setting */ 
    {"js_setPrompSoundMode",1,js_setting_setPrompSoundMode},
    {"js_getPrompSoundMode",0,js_setting_getPrompSoundMode},
    {"js_setBellSoundMode",1,js_setting_setBellSoundMode},
    {"js_getBellSoundMode",0,js_setting_getBellSoundMode},
    {"js_setFaxSoundMode",1,js_setting_setFaxSoundMode},
    {"js_getFaxSoundMode",0,js_setting_getFaxSoundMode},
    {"js_setVolSwitchMode",1,js_setting_setVolSwitchMode},
    {"js_getVolSwitchMode",0,js_setting_getVolSwitchMode},
};

const JSCFunctionList* getSettingSystemJSCFunctionList(int *length)
{
    *length = countof(pesf_setting_system_funcs);
    return pesf_setting_system_funcs;
}

int js_setting_system_beep_init(JSContext *ctx, JSValueConst global)
{
   JSValue ui_proto, ui_constructor;
   int i = 0;

   Log("*********start setting system module*******\n");
   /* creat the classes */
   int count = 0;
   const JSCFunctionList* pesf_funcs = getSettingSystemJSCFunctionList(&count);
   Log("count:%d\n",count);
   for(int i = 0; i < count; i++) {
       JS_SetPropertyStr(ctx, global, pesf_funcs[i].name,
                           JS_NewCFunction(ctx, pesf_funcs[i].func, pesf_funcs[i].name, pesf_funcs[i].length));

   }
   Log("*********start setting system init end**********\n");
   return 0;
}

