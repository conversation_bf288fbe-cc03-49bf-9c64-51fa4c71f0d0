#include <stddef.h>
#include "event_log.h"

// Constants for unknown or default values
#define EVENT_LOG_UNKNOWN_STATUS     "Unknown Status"
#define EVENT_LOG_UNKNOWN_JOB        "Unknown Job"
#define EVENT_LOG_UNKNOWN_CONSUMABLE "Unknown Consumable"
#define EVENT_LOG_FIRMWARE_UPGRADE   "Firmware Upgrade"

// 状态ID到字符串的映射结构
typedef struct status_id_to_string {
    int status_id;
    char* string;
}EVENT_LOG_ID_TO_STRING;

// Job type string mapping structure
typedef struct _EVENT_LOG_JOB_STRING {
    enum event_log_name_job job_type;
    const char* string;
} EVENT_LOG_JOB_STRING_S;

// Consumable type string mapping structure
typedef struct _EVENT_LOG_CONSUMABLE_STRING {
    PRINT_CONSUMPTION_E type;
    const char* string;
} EVENT_LOG_CONSUMABLE_STRING_S;


// Job type mapping table
static const EVENT_LOG_JOB_STRING_S g_job_string_table[] = {
    {EL_NAME_JOB_PRINT, "Print Job"},
    {EL_NAME_JOB_COPY, "Copy Job"},
    {EL_NAME_JOB_SCAN, "Scan Job"}
};

// Consumable type mapping table
static const EVENT_LOG_CONSUMABLE_STRING_S g_consumable_string_table[] = {
    {TONER_C, "Cyan Toner Replacement"},
    {TONER_M, "Magenta Toner Replacement"},
    {TONER_Y, "Yellow Toner Replacement"},
    {TONER_K, "Black Toner Replacement"},
    {DRUM_C, "Cyan Drum Replacement"},
    {DRUM_M, "Magenta Drum Replacement"},
    {DRUM_Y, "Yellow Drum Replacement"},
    {DRUM_K, "Black Drum Replacement"}
};

// 状态ID到字符串的映射表
static const struct status_id_to_string status_id_to_string_table[] = {
    {STATUS_I_PRINT_INIT, "printer initializing"},                    // 打印模块初始化
    {STATUS_I_PRINT_READY, "printer ready"},                         // 打印模块空闲
    {STATUS_I_PRINT_SLEEP, "printer sleeping"},                      // 打印模块睡眠
    {STATUS_I_PRINT_WARMING, "printer warming up"},                  // 打印模块预热
    {STATUS_I_PRINT_PROCESSING, "print processing"},                 // 打印模块处理中
    {STATUS_I_PRINT_PRINTING, "printing"},                          // 打印中
    {STATUS_I_PRINT_CANCELING, "print canceling"},                  // 打印作业取消中
    {STATUS_I_PRINT_PAUSING, "print pausing"},                      // 打印作业暂停中
    {STATUS_I_PRINT_ACR_CALIBRATION, "print calibrating"},          // 打印模块校准中
    {STATUS_I_PRINT_WAITING, "print waiting"},                      // 打印模块等待中
    {STATUS_I_PRINT_HOLDPRINT_CONFIRM, "print sample confirm"},     // 样本打印，打印一份后提示用户的确认信息
    {STATUS_I_PRINT_HOLDPRINT_FINISH, "print sample finish"},       // 样本打印，打印剩余份后提示用户是否删除数据
    {STATUS_I_PRINT_PINCODEPRINT_FINISH, "pincode print finish"},   // 密码打印完成
    {STATUS_I_PRINT_PAPER_CHANGED, "paper changed"},                // 插入纸盒后【纸张自动检测弹框】消除&纸张变换完了
    {STATUS_I_PRINT_HOLDPRINT_LIST_FULL, "sample print full"},      // 样本打印超过保存最大数
    {STATUS_I_PRINT_STORAGE_JOB_FULL_FAILED, "storage full"},       // 磁盘不足或者作业数量达到上限导致作业存储失败
    {STATUS_I_PRINT_STORAGE_JOB_STREAM_FAILED, "stream read fail"}, // 流读取错误导致作业存储失败
    {STATUS_I_PRINT_IPS_PARSER_TIMEOUT, "ips parser timeout"},      // ipsparser读取io超时
    {STATUS_I_PRINT_TONER_EMPTY_COLOR, "color toner empty"},        // 彩粉尽操作选择项:黑白打印/取消打印
    {STATUS_I_PRINT_FE_STATUS_ON, "fe status on"},                  // 进入FE状态操作选择项:黑白打印/取消打印
    {STATUS_I_PRINT_UNSUPPORTED_DOCUMENT_FORMAT, "unsupported format"}, // 不支持的文档格式
    {STATUS_I_PRINT_FLIP_OVER, "manual duplex flip"},               // 手动双面打印作业，一面打印完了，需要整体翻面
    {STATUS_I_PRINT_OPC_CALIBRATION, "opc calibrating"},            // OPC校验
    {STATUS_I_PRINT_TONER_CONSUM_CALIBRATION, "toner consuming"},   // 碳粉消耗中
    {STATUS_I_PRINT_HOLDPRINT_STORAGE_FULL, "sample storage full"}, // 在样本打印存储过程中,如果发生存储空间不足,则提示用户此信息.此时作业会被自动取消
    {STATUS_I_PRINT_ABORTING, "print aborting"},                    // 打印作业异常
    {STATUS_I_PRINT_TRAY_NEED_CLOSE, "close tray"},                // 纸盒未关闭
    {STATUS_I_PRINT_EMMC_NORMAL, "emmc normal"},                    // 正常状态，只有该状态才能支持逐份作业。事实上,IDLE意味着正常，故此状态可无
    {STATUS_I_PRINT_EMMC_FORMATTING, "emmc formatting"},            // 内部存储器初始化中，该状态下需要等待格式化完成才能下发作业，作业中不会出现该状态，在等待过程中面板需要提示用户内部存储器正在格式化
    {STATUS_I_PRINT_PINCODE_SAVING, "pincode saving"},             // 密码作业存储中
    {STATUS_I_PRINT_PINCODE_SAVE_FINISH, "pincode save finish"},   // 密码作业存储完了
    {STATUS_I_PRINT_SAVING, "print saving"},                        // 存储中
    {STATUS_I_PRINT_TONER_SUPPLYING, "toner supplying"},           // 补粉中
    {STATUS_I_PRINT_INTERNAL_PAGE_CREATE_PDF, "creating report"},   // 内部页生成设备报告
    {STATUS_I_PRINT_SAMPLE_NOTIFY_PANEL_STOP_JOB, "sample stop"},  // 样本复印通知面板停止作业
    {STATUS_I_PRINT_JOB_LOCKED, "print job locked"},               // 打印作业管控信息
    {STATUS_I_PRINT_DATA_OPC_CLEAR, "opc cleaning"},               // 清除OPC感光鼓残留碳粉
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_C, "cyan toner low"},         // C碳粉即将用尽
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_M, "magenta toner low"},      // M碳粉即将用尽
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_Y, "yellow toner low"},       // Y碳粉即将用尽
    {STATUS_W_PRINT_TONER_NEAR_EMPTY_K, "black toner low"},        // K碳粉即将用尽
    {STATUS_W_PRINT_WASTE_TONER_NEAR_FULL, "waste toner near full"}, // 废碳粉仓即将满仓
    {STATUS_W_PRINT_FUSER_NEAR_LIFE_END, "fuser near end"},        // 定影寿命即将到达
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_C, "cyan drum near end"},   // C硒鼓寿命即将到达
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_M, "magenta drum near end"}, // M硒鼓寿命即将到达
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_Y, "yellow drum near end"}, // Y硒鼓寿命即将到达
    {STATUS_W_PRINT_DRUM_NEAR_LIFE_END_K, "black drum near end"},  // K硒鼓寿命即将到达
    {STATUS_W_PRINT_TRAY_MISSING_STANDARD_TRAY, "standard tray missing"}, // 标准进纸盒抽出
    {STATUS_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1, "tray1 missing"}, // 选配进纸盒1抽出
    {STATUS_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2, "tray2 missing"}, // 选配进纸盒2抽出
    {STATUS_W_PRINT_TRAY_EMPTY_STANDARD_TRAY, "standard tray empty"}, // 标准进纸盒缺纸
    {STATUS_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1, "tray1 empty"},    // 选配进纸盒1缺纸
    {STATUS_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2, "tray2 empty"},    // 选配进纸盒2缺纸
    {STATUS_W_PRINT_STANDARD_BIN_FULL, "output bin full"},           // 标准排纸盒满
    {STATUS_W_PRINT_COLOR_CALIBRATION, "color calibration error"},   // 色彩校正异常
    {STATUS_W_PRINT_COLOR_REGISTRATION, "color registration error"}, // 色彩配准异常
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_C, "cyan carrier low"},         // C硒鼓载体即将用尽
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_M, "magenta carrier low"},      // M硒鼓载体即将用尽
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_Y, "yellow carrier low"},       // Y硒鼓载体即将用尽
    {STATUS_W_PRINT_DRUM_CARRIER_LOW_K, "black carrier low"},        // K硒鼓载体即将用尽
    {STATUS_W_PRINT_REALAY_ROLLER_NEAR_LIFE_END, "transfer roller end"}, // 转印辊寿命即将到达
    {STATUS_W_PRINT_ITU_NEAR_LIFE_END, "itu near end"},             // 转写带寿命即将到达
    {STATUS_W_PRINT_TRAY2_ERROR, "tray1 error"},                    // 选配给纸盒1异常
    {STATUS_W_PRINT_TRAY3_ERROR, "tray2 error"},                    // 选配给纸盒2异常
    {STATUS_W_PRINT_TRAY2_MOTOR_ERROR, "tray1 motor error"},        // 选配给纸盒1马达异常
    {STATUS_W_PRINT_TRAY3_MOTOR_ERROR, "tray2 motor error"},        // 选配给纸盒2马达异常
    {STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_Y, "yellow density high"}, // 显影组件浓度异常高Y通道
    {STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_M, "magenta density high"}, // 显影组件浓度异常高M通道
    {STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_C, "cyan density high"},   // 显影组件浓度异常高C通道
    {STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_Y, "yellow sensor low"}, // 浓度传感器输出电压超出下限Y通道
    {STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_M, "magenta sensor low"}, // 浓度传感器输出电压超出下限M通道
    {STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_C, "cyan sensor low"}, // 浓度传感器输出电压超出下限C通道
    {STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_Y, "yellow sensor high"}, // 浓度传感器输出电压超出上限Y通道
    {STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_M, "magenta sensor high"}, // 浓度传感器输出电压超出上限M通道
    {STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_C, "cyan sensor high"}, // 浓度传感器输出电压超出上限C通道
    {STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_Y, "yellow sensor error"}, // 浓度传感器异常Y通道
    {STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_M, "magenta sensor error"}, // 浓度传感器异常M通道
    {STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_C, "cyan sensor error"},  // 浓度传感器异常C通道
    {STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_Y, "yellow supply error"}, // 碳粉供应故障Y通道
    {STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_M, "magenta supply error"}, // 碳粉供应故障M通道
    {STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_C, "cyan supply error"}, // 碳粉供应故障C通道
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_Y, "yellow supply error2"}, // 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)Y通道
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_M, "magenta supply error2"}, // 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)M通道
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_C, "cyan supply error2"}, // 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)C通道
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_Y, "yellow toner empty"}, // 粉桶碳粉空错误(粉桶寿命小于5%)Y通道
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_M, "magenta toner empty"}, // 粉桶碳粉空错误(粉桶寿命小于5%)M通道
    {STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_C, "cyan toner empty"}, // 粉桶碳粉空错误(粉桶寿命小于5%)C通道
    {STATUS_W_PRINT_INTERNAL_ENV_TEMP_ERROR, "internal temp error"}, // 内部环境温湿度传感器异常
    {STATUS_W_PRINT_EXTERNAL_ENV_TEMP_ERROR, "external temp error"}, // 外部环境温湿度传感器异常
    {STATUS_W_PRINT_FRONT_CTD_SENSOR_DETECT, "front ctd error"},    // 前侧CTDSensor检测故障
    {STATUS_W_PRINT_BACK_CTD_SENSOR_DETECT, "back ctd error"},      // 后侧CTDSensor检测故障
    {STATUS_W_PRINT_FRONT_CTD_SENSOR_ADJUST, "front ctd adjust error"}, // 前侧CTDSensor调整故障
    {STATUS_W_PRINT_BACK_CTD_SENSOR_ADJUST, "back ctd adjust error"}, // 后侧CTDSensor调整故障
    {STATUS_W_PRINT_TONER_EMPTY_C, "cyan toner empty"},             // C碳粉完全用尽
    {STATUS_W_PRINT_TONER_EMPTY_M, "magenta toner empty"},          // M碳粉完全用尽
    {STATUS_W_PRINT_TONER_EMPTY_Y, "yellow toner empty"},           // Y碳粉完全用尽
    {STATUS_W_PRINT_TONER_EMPTY_K, "black toner empty"},            // K碳粉完全用尽
    {STATUS_W_PRINT_LSU_TEMP_SENSOR_ERROR, "lsu temp error"},       // LSU温度传感器异常
    {STATUS_W_PRINT_FE0280_03, "color sensor error front"},         // 色彩校正传感器异常：前
    {STATUS_W_PRINT_FE0280_04, "color sensor error back"},          // 色彩校正传感器异常：后
    {STATUS_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1, "tray1 bottom error"}, // 选配纸盒1底板异常
    {STATUS_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2, "tray2 bottom error"}, // 选配纸盒2底板异常
    {STATUS_W_PRINT_FE0280_01, "black phase error"},                // 黑色相位传感器异常
    {STATUS_W_PRINT_FE0280_02, "color phase error"},                // 彩色相位传感器异常
    {STATUS_W_PRINT_OPTION_TRAY1_ADJUSTING, "tray1 adjusting"},     // 选配纸盒1调整中
    {STATUS_W_PRINT_OPTION_TRAY2_ADJUSTING, "tray2 adjusting"},     // 选配纸盒2调整中
    {STATUS_W_PRINT_WASTE_TONER_FULL, "waste toner full"},          // 废碳粉仓满仓
    {STATUS_W_PRINT_DRUM_LIFE_END_C, "cyan drum end"},              // C硒鼓寿命尽
    {STATUS_W_PRINT_DRUM_LIFE_END_M, "magenta drum end"},           // M硒鼓寿命尽
    {STATUS_W_PRINT_DRUM_LIFE_END_Y, "yellow drum end"},            // Y硒鼓寿命尽
    {STATUS_W_PRINT_DRUM_LIFE_END_K, "black drum end"},             // K硒鼓寿命尽
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_C, "cyan carrier empty"},    // C硒鼓载体用尽
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_M, "magenta carrier empty"}, // M硒鼓载体用尽
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_Y, "yellow carrier empty"},  // Y硒鼓载体用尽
    {STATUS_W_PRINT_DRUM_CARRIER_EMPTY_K, "black carrier empty"},   // K硒鼓载体用尽
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_C, "cyan toner low"}, // C碳粉余量不足
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_M, "magenta toner low"}, // M碳粉余量不足
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_Y, "yellow toner low"}, // Y碳粉余量不足
    {STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_K, "black toner low"}, // K碳粉余量不足
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_C, "cyan drum low"},     // C硒鼓余量不足
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_M, "magenta drum low"},  // M硒鼓余量不足
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_Y, "yellow drum low"},   // Y硒鼓余量不足
    {STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_K, "black drum low"},    // K硒鼓余量不足
    {STATUS_W_PRINT_LOW_TEMPERATURE, "low temperature"},            // 低温警告
    {STATUS_W_PRINT_MAIN_HUMITURE_SENSOR, "main temp sensor error"}, // 主温湿度传感器异常
    {STATUS_W_PRINT_SUB_HUMITURE_SENSOR, "sub temp sensor error"},  // 备用温湿度传感器异常
    {STATUS_W_PRINT_FE0280_05, "color sensor dirty"},              // 色彩校正传感器脏污
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_C, "cyan supply warning"}, // 碳粉供应异常警告C
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_M, "magenta supply warning"}, // 碳粉供应异常警告M
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_Y, "yellow supply warning"}, // 碳粉供应异常警告Y
    {STATUS_W_PRINT_TONER_SUPPLY_CAUTION_K, "black supply warning"}, // 碳粉供应异常警告K
    {STATUS_W_PRINT_MAIN_HUM_SENSOR_EXCEPTION, "main sensor error"}, // 主环境温湿度传感器读取异常
    {STATUS_W_PRINT_FE0281_00, "idc temp error"},                  // IDC温度传感器异常
    {STATUS_W_PRINT_FE0850_00, "temp sensor comm error"},          // 温湿度传感器通信异常
    {STATUS_W_PRINT_FUSER_LIFE_END, "fuser end"},                  // 定影寿命到达
    {STATUS_W_PRINT_TRAY_1_OPEN, "tray1 open"},                    // 纸盒1未插入
    {STATUS_W_PRINT_TRAY_2_OPEN, "tray2 open"},                    // 纸盒2未插入
    {STATUS_W_PRINT_TRAY_3_OPEN, "tray3 open"},                    // 纸盒3未插入
    {STATUS_W_PRINT_TRAY_4_OPEN, "tray4 open"},                    // 纸盒4未插入
    {STATUS_W_PRINT_LCT_IN_OPEN, "lct open"},                      // 内置大容量纸盒未插入
    {STATUS_W_PRINT_TRAY_1_PAPER_EMPTY, "tray1 empty"},            // 纸盒1缺纸
    {STATUS_W_PRINT_TRAY_2_PAPER_EMPTY, "tray2 empty"},            // 纸盒2缺纸
    {STATUS_W_PRINT_TRAY_3_PAPER_EMPTY, "tray3 empty"},            // 纸盒3缺纸
    {STATUS_W_PRINT_TRAY_4_PAPER_EMPTY, "tray4 empty"},            // 纸盒4缺纸
    {STATUS_W_PRINT_LCT_IN_PAPER_EMPTY, "lct empty"},              // 内置大容量纸盒缺纸
    {STATUS_W_PRINT_LCT_EX_PAPER_EMPTY, "external lct empty"},     // 外置大容量纸盒缺纸
    {STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY, "tray1 near empty"},  // 纸盒1将缺纸
    {STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY, "tray2 near empty"},  // 纸盒2将缺纸
    {STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY, "tray3 near empty"},  // 纸盒3将缺纸
    {STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY, "tray4 near empty"},  // 纸盒4将缺纸
    {STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY, "lct near empty"},    // 内置大容量纸盒将缺纸
    {STATUS_W_PRINT_LCT_EX_PAPER_NEAR_EMPTY, "external lct near empty"}, // 外置大容量纸盒将缺纸
    {STATUS_W_PRINT_LCT_IN_MOVING, "lct moving"},                  // 内建大容量纸盒正在移动纸张
    {STATUS_W_PRINT_LCT_IN_PAPER_NEAR_FULL, "lct near full"},      // 内置大容量纸盒将满
    {STATUS_W_PRINT_LCT_EX_PAPER_NEAR_FULL, "external lct near full"}, // 外置大容量纸盒将满
    {STATUS_W_PRINT_LCT_IN_PAPER_FULL, "lct full"},                // 内置大容量纸盒已满
    {STATUS_W_PRINT_LCT_EX_PAPER_FULL, "external lct full"},       // 外置大容量纸盒已满
    {STATUS_W_PRINT_Y_TONER_NEAR_EMPTY, "yellow toner near empty"}, // 黄色碳粉盒粉量低
    {STATUS_W_PRINT_M_TONER_NEAR_EMPTY, "magenta toner near empty"}, // 品红色碳粉盒粉量低
    {STATUS_W_PRINT_C_TONER_NEAR_EMPTY, "cyan toner near empty"},   // 青色碳粉盒粉量低
    {STATUS_W_PRINT_K_TONER_NEAR_EMPTY, "black toner near empty"},  // 黑色碳粉盒粉量低
    {STATUS_W_PRINT_M_DR_NEAR_LIFE, "magenta drum near life"},      // 品红色鼓组件寿命将尽
    {STATUS_W_PRINT_C_DR_NEAR_LIFE, "cyan drum near life"},         // 青色鼓组件寿命将尽
    {STATUS_W_PRINT_K_DR_NEAR_LIFE, "black drum near life"},        // 黑色鼓组件寿命将尽

    {STATUS_W_PRINT_M_DR_NEAR_LIFE, "magenta drum end"},             // 品红色鼓组件寿命尽
    {STATUS_W_PRINT_Y_DV_NEAR_LIFE, "yellow drum near life"},       // 黄色显影组件寿命将尽
    {STATUS_W_PRINT_M_DV_NEAR_LIFE, "magenta drum near life"},      // 品红色显影组件寿命将尽
    {STATUS_W_PRINT_C_DV_NEAR_LIFE, "cyan drum near life"},         // 青色显影组件寿命将尽
    {STATUS_W_PRINT_K_DV_NEAR_LIFE, "black drum near life"},        // 黑色显影组件寿命将尽
    {STATUS_W_PRINT_TRANSFER_ROLLER_UNIT_LIFE_END, "transfer roller unit life end"}, // 转印辊寿命将尽
    {STATUS_W_PRINT_TONER_FILTER_LIFE_END, "toner filter life end"}, // 粉盒过滤网寿命将尽
    {STATUS_W_PRINT_FUSING_UNIT_LIFE_END, "fusing unit life end"}, // 定影组件寿命将尽
    {STATUS_W_PRINT_TRANSFER_BELT_UNIT_LIFE_END, "transfer belt unit life end"}, // 转印带寿命将尽
    {STATUS_W_PRINT_SIDE_STITCHING_STAPLE_EMPTY, "side stitching staple empty"}, // 平订订书钉空
    {STATUS_W_PRINT_STAPLE_NEAR_EMPTY, "staple near empty"}, // 平订订书钉将空
    {STATUS_W_PRINT_SADDLE_BIND_F_STAPLE_EMPTY, "saddle bind f staple empty"}, // 鞍式装订订书钉（前）空
    {STATUS_W_PRINT_SADDLE_BIND_B_STAPLE_EMPTY, "saddle bind b staple empty"}, // 鞍式装订订书钉（后）空
    {STATUS_W_PRINT_PUNCH_WASTE_NOT_SET, "punch waste not set"}, // 打孔废料盒未安装
    {STATUS_W_PRINT_PUNCH_TRASH_BOX_FULL, "punch waste near full"}, // 打孔废料盒将满
    {STATUS_W_PRINT_NEEDLE_SCARP_OVER, "needle scrap over"}, // 平钉废盒状态
    {STATUS_W_PRINT_MAIN_ABNORMAL_LOWER_STATE, "main tray abnormal"}, // 主托盘异常下降状态异常
    {STATUS_W_PRINT_MEDIA_SENSOR_ERROR1, "media sensor error1"}, // 介质传感器异常1
    {STATUS_W_PRINT_BAROMETRIC_SENSOR_ERROR, "barometric sensor error"}, // 气压传感器异常
    {STATUS_W_PRINT_MEDIA_SENSOR_ERROR2, "media sensor error2"}, // 介质传感器异常2
    {STATUS_W_PRINT_LD_ABNORMALITY, "laser diode error"}, // 激光二极管异常
    {STATUS_W_PRINT_SKEW_ADJUSTMENT_ABNORMALITY, "skew adjust error"}, // 纠偏异常
    {STATUS_W_PRINT_IDC_SENSOR_FRONT_ABNORMALITY, "front idc sensor error"}, // IDC传感器(前)异常
    {STATUS_W_PRINT_C_DR_DV_ABNORMALITY, "cyan drum dev error"}, // 青色鼓组件或显影组件异常
    {STATUS_W_PRINT_M_DR_DV_ABNORMALITY, "magenta drum dev error"}, // 品红色鼓组件或显影组件异常
    {STATUS_W_PRINT_Y_DR_DV_ABNORMALITY, "yellow drum dev error"}, // 黄色鼓组件或显影组件异常
    {STATUS_W_PRINT_K_DR_DV_ABNORMALITY, "black drum dev error"}, // 黑色鼓组件或显影组件异常
    {STATUS_W_PRINT_IDC_SENSOR_BACK_ABNORMALITY, "back idc sensor error"}, // IDC传感器(后)异常
    {STATUS_W_PRINT_COLOR_PATTERN_TEST_ERROR, "color pattern test error"}, // 色彩对位测试样式异常
    {STATUS_W_PRINT_COLOR_CORRECTION_AMOUNT_ERROR, "color correction error"}, // 色彩对位调整异常
    {STATUS_W_PRINT_PH_OPTICAL_SYSTEM_DIRT, "ph optical system dirty"}, // PH光学系统污染
    {STATUS_W_PRINT_PAPER_WIDTH_ERROR, "paper width error"}, // 纸张宽度传感器异常
    {STATUS_W_PRINT_PAPER_TEMPERATURE_ERROR, "paper temp error"}, // 纸张温度传感器异常
    {STATUS_W_PRINT_PH_TEMPERATURE_ERROR, "ph temp error"}, // PH温度传感器异常
    {STATUS_W_PRINT_ABNORMAL_2ND_TRANSFER_ATVC, "2nd transfer atvc error"}, // 第二转印ATVC异常
    {STATUS_W_PRINT_INFLIGHT_TEMPERATURE_ERROR, "inflight temp error"}, // 机内温度传感器异常
    {STATUS_W_PRINT_TACKING_FAN_ABNORMALITY, "tacking fan error"}, // 吸附风扇异常
    {STATUS_W_PRINT_FUSER_SENSOR_TEMP_DETECT_ERROR, "fuser temp detect error"}, // 定影温度传感器检测异常
    {STATUS_W_PRINT_TRAY1_PAPER_LOW, "tray1 paper low"}, // 标准进纸盒纸张剩余量少
    {STATUS_W_PRINT_TRAY2_PAPER_LOW, "tray2 paper low"}, // 选配进纸盒1纸张剩余量少
    {STATUS_W_PRINT_TRAY3_PAPER_LOW, "tray3 paper low"}, // 选配进纸盒2纸张剩余量少
    {STATUS_W_PRINT_STAPLE_EMPTY_3, "staple empty 3"}, // 钉空3警告
    {STATUS_W_PRINT_SELECTED_OUTPUT_SOURCE_FULL, "output source full"}, // 选择出口源满
    {STATUS_W_PRINT_STAPLE_EMPTY_2, "staple empty 2"}, // 钉空2警告
    {STATUS_W_PRINT_FLOD_PAPER_TRAY_LIFT, "fold tray lift"}, // 折叠纸盒抬升警告
    {STATUS_W_PRINT_SADDLE_BIND_STAPLE_MOVE, "saddle staple moving"}, // 鞍式装订移动
    {STATUS_W_PRINT_FLAT_BINDING_STAPLER_MOVING, "flat stapler moving"}, // 平钉钉书器移动
    {STATUS_W_PRINT_TONER_SUPPLY_DOOR_OPEN, "toner supply door open"}, // 粉盒补给门打开
    {STATUS_W_PRINT_LCT_EX_DOOR_F_OPEN, "lct door f open"}, // 外置大容量纸盒（盖门F）未插入
    {STATUS_E_PRINT_BACK_COVER_OPEN, "back cover open"}, // 机器后盖打开
    {STATUS_E_PRINT_RIGHT_COVER_OPEN, "right cover open"}, // 机器右盖打开
    {STATUS_E_PRINT_FRONT_COVER_OPEN, "front cover open"}, // 机器前盖打开
    {STATUS_E_PRINT_TRAY2_COVER_OPEN, "tray2 cover open"}, // 选配给纸盒1侧盖打开
    {STATUS_E_PRINT_TRAY3_COVER_OPEN, "tray3 cover open"}, // 选配给纸盒2侧盖打开
    {STATUS_E_PRINT_TONER_EMPTY_C, "cyan toner empty"}, // C碳粉完全用尽
    {STATUS_E_PRINT_TONER_EMPTY_M, "magenta toner empty"}, // M碳粉完全用尽
    {STATUS_E_PRINT_TONER_EMPTY_Y, "yellow toner empty"}, // Y碳粉完全用尽
    {STATUS_E_PRINT_TONER_EMPTY_K, "black toner empty"}, // K碳粉完全用尽
    {STATUS_E_PRINT_TONER_MISSING_C, "cyan toner missing"}, // C碳粉筒未安装
    {STATUS_E_PRINT_TONER_MISSING_M, "magenta toner missing"}, // M碳粉筒未安装
    {STATUS_E_PRINT_TONER_MISSING_Y, "yellow toner missing"}, // Y碳粉筒未安装
    {STATUS_E_PRINT_TONER_MISSING_K, "black toner missing"}, // K碳粉筒未安装
    {STATUS_E_PRINT_TONER_MISMATCH_C, "cyan toner mismatch"}, // C碳粉筒不匹配
    {STATUS_E_PRINT_TONER_MISMATCH_M, "magenta toner mismatch"}, // M碳粉筒不匹配
    {STATUS_E_PRINT_TONER_MISMATCH_Y, "yellow toner mismatch"}, // Y碳粉筒不匹配
    {STATUS_E_PRINT_TONER_MISMATCH_K, "black toner mismatch"}, // K碳粉筒不匹配
    {STATUS_E_PRINT_DRUM_LIFE_END_C, "cyan drum end"}, // C硒鼓寿命尽
    {STATUS_E_PRINT_DRUM_LIFE_END_M, "magenta drum end"}, // M硒鼓寿命尽
    {STATUS_E_PRINT_DRUM_LIFE_END_Y, "yellow drum end"}, // Y硒鼓寿命尽
    {STATUS_E_PRINT_DRUM_LIFE_END_K, "black drum end"}, // K硒鼓寿命尽
    {STATUS_E_PRINT_DRUM_MISSING_C, "cyan drum missing"}, // C硒鼓未安装
    {STATUS_E_PRINT_DRUM_MISSING_M, "magenta drum missing"}, // M硒鼓未安装
    {STATUS_E_PRINT_DRUM_MISSING_Y, "yellow drum missing"}, // Y硒鼓未安装
    {STATUS_E_PRINT_DRUM_MISSING_K, "black drum missing"}, // K硒鼓未安装
    {STATUS_E_PRINT_DRUM_MISMATCH_C, "cyan drum mismatch"}, // C硒鼓不匹配
    {STATUS_E_PRINT_DRUM_MISMATCH_M, "magenta drum mismatch"}, // M硒鼓不匹配
    {STATUS_E_PRINT_DRUM_MISMATCH_Y, "yellow drum mismatch"}, // Y硒鼓不匹配
    {STATUS_E_PRINT_DRUM_MISMATCH_K, "black drum mismatch"}, // K硒鼓不匹配
    {STATUS_E_PRINT_WASTE_TONER_FULL, "waste toner full"}, // 废碳粉仓满仓
    {STATUS_E_PRINT_WASTE_TONER_MISSING, "waste toner missing"}, // 废粉盒未安装
    {STATUS_E_PRINT_STANDARD_BIN_FULL, "output bin full"}, // 标准排纸盒满
    {STATUS_E_PRINT_TRAY_MISSING_STANDARD_TRAY, "standard tray missing"}, // 标准进纸盒纸盒抽出
    {STATUS_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1, "tray1 missing"}, // 选配进纸盒1纸盒抽出
    {STATUS_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2, "tray2 missing"}, // 选配进纸盒2纸盒抽出
    {STATUS_E_PRINT_TRAY_EMPTY_STANDARD_TRAY, "standard tray empty"}, // 标准进纸盒纸盒缺纸
    {STATUS_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY, "multi-function tray empty"}, // 多功能进纸盒纸盒缺纸
    {STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1, "tray1 empty"}, // 选配进纸盒1纸盒缺纸
    {STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2, "tray2 empty"}, // 选配进纸盒2纸盒缺纸
    {STATUS_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH, "tray1 paper size mismatch"}, // 标准进纸盒纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH, "multi-function tray paper size mismatch"}, // 多功能进纸盒纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH, "tray1 paper type mismatch"}, // 标准进纸盒纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH, "multi-function tray paper type mismatch"}, // 多功能进纸盒纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH, "tray2 paper size mismatch"}, // 选配纸盒1纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH, "tray3 paper size mismatch"}, // 选配纸盒2纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH, "tray2 paper type mismatch"}, // 选配纸盒1纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH, "tray3 paper type mismatch"}, // 选配纸盒2纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_ENGINE_NOT_READY, "engine not ready"}, // 引擎就绪信号异常
    {STATUS_E_PRINT_ENGINE_COMMUNICATION_FAILED, "engine communication failed"}, // 引擎通信异常
    {STATUS_E_PRINT_VIDEO_DRIVE, "video driver error"}, // 图像输出单元异常,videodriver模块异常
    {STATUS_E_PRINT_VIDEO_BANDING, "video banding error"}, // 图像输出单元异常,videostartbading异常
    {STATUS_E_PRINT_FUSER_MISSING, "fuser missing"}, // 定影未安装
    {STATUS_E_PRINT_FUSER_LIFE_END, "fuser end"}, // 定影寿命到达
    {STATUS_E_PRINT_JAM_NOT_FEED_STANDARD_TRAY, "standard tray jam"}, // 标准进纸盒不给纸JAM
    {STATUS_E_PRINT_JAM_NOT_FEED_MULTI_FUNCTION_TRAY, "multi-function tray jam"}, // 多功能进纸盒不给JAM
    {STATUS_E_PRINT_JAM_NOT_FEED_DUPLEX_UINT, "duplex jam"}, // 两面不给纸JAM
    {STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_1, "tray1 jam"}, // 选配进纸盒1不给纸JAM
    {STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_2, "tray2 jam"}, // 选配进纸盒2不给纸JAM
    {STATUS_E_PRINT_JAM_UNATTAIN_CALIBRATE_SENSOR, "sensor calibration jam"}, // 校正传感器未达JAM
    {STATUS_E_PRINT_JAM_UNATTAIN_OUTPUT_SENSOR, "output sensor jam"}, // 排纸传感器未达JAM
    {STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_1, "tray1 jam"}, // 选配进纸盒1搬送传感器未达JAM
    {STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_2, "tray2 jam"}, // 选配进纸盒2搬送传感器未达JAM
    {STATUS_E_PRINT_JAM_UNATTAIN_FUSER, "fuser jam"}, // 定影传感器未达JAM
    {STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_INPUT, "duplex input jam"}, // 两面入口未达JAM
    {STATUS_E_PRINT_JAM_STRANDED_CALIBRATE_SENSOR, "sensor stranded jam"}, // 校正传感器滞留JAM
    {STATUS_E_PRINT_JAM_STRANDED_OUTPUT_SENSOR, "output sensor stranded jam"}, // 排纸传感器滞留JAM
    {STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_1, "tray1 jam"}, // 选配进纸盒1搬送传感器滞留JAM
    {STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_2, "tray2 jam"}, // 选配进纸盒2搬送传感器滞留JAM
    {STATUS_E_PRINT_JAM_STRANDED_FUSER, "fuser stranded jam"}, // 定影滞留JAM
    {STATUS_E_PRINT_JAM_STRANDED_DUPLEX_INPUT, "duplex input stranded jam"}, // 两面入口滞留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_FUSER, "fuser residual jam"}, // 定影残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT, "duplex input residual jam"}, // 双面入口残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR, "output sensor residual jam"}, // 排纸传感器残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIBRATE_SENSOR, "sensor residual jam"}, // 校正传感器残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2, "tray2 residual jam"}, // 选配进纸盒2残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1, "tray1 residual jam"}, // 选配进纸盒1残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY, "multi-function tray residual jam"}, // 多功能进纸盒残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY, "standard tray residual jam"}, // 标准进纸盒残留JAM
    {STATUS_E_PRINT_TIMEPRINT_LIST_FULL, "timeprint list full"}, // 延时打印作业列表满
    {STATUS_E_PRINT_TRAY_PAPER_SIZE_ERROR, "paper size error"}, // 纸张尺寸错误
    {STATUS_E_PRINT_TRAY_FEED_MISMATCH, "paper feed mismatch"}, // 纸张来源不匹配
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_C, "cyan carrier empty"}, // C硒鼓载体用尽
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_M, "magenta carrier empty"}, // M硒鼓载体用尽
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_Y, "yellow carrier empty"}, // Y硒鼓载体用尽
    {STATUS_E_PRINT_DRUM_CARRIER_EMPTY_K, "black carrier empty"}, // K硒鼓载体用尽
    {STATUS_E_PRINT_REALAY_ROLLER_LIFE_END, "transfer roller end"}, // 转印辊寿命到达
    {STATUS_E_PRINT_ITU_NEAR_LIFE_END, "itu near end"}, // 转写带寿命到达
    {STATUS_E_PRINT_ILLEGAL_PAGE, "illegal page"}, // 非法作业
    {STATUS_E_PRINT_JAM_UNATTAIN_CALIB_OPTIONAL_TRAY_1, "tray1 jam"}, // 选配1校正未达Jam
    {STATUS_E_PRINT_JAM_UNATTAIN_CALIB_OPTIONAL_TRAY_2, "tray2 jam"}, // 选配2校正未达Jam
    {STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_OUTPUT, "duplex output jam"}, // 两面出口未达JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_OUTPUT, "duplex output residual jam"}, // 双面入出口残留JAM
    {STATUS_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1, "tray1 bottom error"}, // 选配纸盒1底板异常
    {STATUS_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2, "tray2 bottom error"}, // 选配纸盒2底板异常
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT, "sensor residual jam"}, // 校正传感器+排出传感器残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIB_DUPLEX, "sensor residual jam"}, // 校正传感器+双面入口残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_DUPLEX, "output sensor residual jam"}, // 排出传感器+双面入口残留JAM
    {STATUS_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT_DUPLEX, "sensor residual jam"}, // 校正传感器+排出传感器+双面入口残留JAM
    {STATUS_E_PRINT_STORAGE_SPACE_NOT_ENOUGH, "storage space not enough"}, // 作业存储磁盘空间不足
    {STATUS_E_PRINT_FUSER_LIFE_TERMINATION, "fuser life termination"}, // 定影寿命终止
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_C, "cyan supply warning"}, // 碳粉供应异常C
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_M, "magenta supply warning"}, // 碳粉供应异常M
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_Y, "yellow supply warning"}, // 碳粉供应异常Y
    {STATUS_E_PRINT_TONER_SUPPLY_CAUTION_K, "black supply warning"}, // 碳粉供应异常K
    {STATUS_E_PRINT_PARSER_TIMEOUT, "parser timeout"}, // 解析超时
    {STATUS_E_PRINT_PARSER_ERROR, "parser error"}, // 解析错误
    {STATUS_E_PRINT_PARSER_DISCONNECT, "parser disconnect"}, // 连接断开
    {STATUS_E_PRINT_VIDEO, "video error"}, // 图像输出单元异常
    {STATUS_E_PRINT_IMAGE_PROCESS_ERROR, "image process error"}, // 图像处理错误
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT, "incorrect attribute"}, // 非法作业
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_TRAY_IN, "incorrect tray in"}, // 非法作业_纸盒错误
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PAPER_TYPE, "incorrect paper type"}, // 非法作业_纸张类型错误
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PAPER_SIZE, "incorrect paper size"}, // 非法作业_纸张尺寸错误
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PRINT_MODE, "incorrect print mode"}, // 非法作业_打印模式错误
    {STATUS_E_PRINT_ATTRIBUTE_INCORRECT_STAPLE, "incorrect staple"}, // 非法作业_装订模式错误
    {STATUS_E_PRINT_Y_TB_MISMATCH, "yellow toner box mismatch"}, // 黄色碳粉盒不匹配
    {STATUS_E_PRINT_M_TB_MISMATCH, "magenta toner box mismatch"}, // 品红色碳粉盒不匹配
    {STATUS_E_PRINT_C_TB_MISMATCH, "cyan toner box mismatch"}, // 青色碳粉盒不匹配
    {STATUS_E_PRINT_K_TB_MISMATCH, "black toner box mismatch"}, // 黑色碳粉盒不匹配
    {STATUS_E_PRINT_Y_DR_MISMATCH, "yellow drum mismatch"}, // 黄色鼓组件不匹配
    {STATUS_E_PRINT_M_DR_MISMATCH, "magenta drum mismatch"}, // 品红色鼓组件不匹配
    {STATUS_E_PRINT_C_DR_MISMATCH, "cyan drum mismatch"}, // 青色鼓组件不匹配
    {STATUS_E_PRINT_K_DR_MISMATCH, "black drum mismatch"}, // 黑色鼓组件不匹配
    {STATUS_E_PRINT_PRINTRUNONEMPTY_Y, "yellow toner empty"}, // Y彩粉尽作业
    {STATUS_E_PRINT_PRINTRUNONEMPTY_M, "magenta toner empty"}, // M彩粉尽作业
    {STATUS_E_PRINT_PRINTRUNONEMPTY_C, "cyan toner empty"}, // C彩粉尽作业
    {STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_Y, "Y life end continue printing"}, // Y寿命尽继续打印作业中只能取消作业
    {STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_M, "M life end continue printing"}, // M寿命尽继续打印作业中只能取消作业
    {STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_C, "C life end continue printing"}, // C寿命尽继续打印作业中只能取消作业
    {STATUS_E_PRINT_PRINTRUNONEMPTY_WAR, "tray paper size and task paper size mismatch"}, // 纸盒里纸张尺寸和任务要求的纸张尺寸不一致
    {STATUS_E_PRINT_EMMC_FULL, "emmc full"}, // 内部存储器已满，如果是下发作业前出现该状态，说明存储器寿命尽，不能支持逐份，如果是作业中出现该状态，说明逐份页数超出限制，面板需要提示用户取消作业
    {STATUS_E_PRINT_EMMC_ERROR, "emmc error"}, // 内部存储器发生错误，如果出现该状态，不能支持逐份，面板需要提示用户内部存储器损坏，等待用户点击确定后取消作业
    {STATUS_E_PRINT_UDISK_PARSE_ERROR, "udisk parse error"}, // U盘打印解析错误
    {STATUS_E_PRINT_NETWORK_ACESS_ALARM, "network access alarm"}, // 外网接入警报
    {STATUS_E_PRINT_ENGINE_SYSTEM_ABNORMAL, "engine system abnormal"}, // 引擎系统异常
    {STATUS_E_PRINT_JAM_TRAY_1_SECTION, "tray1 jam"}, // 纸盒1卡纸
    {STATUS_E_PRINT_JAM_TRAY_2_SECTION, "tray2 jam"}, // 纸盒2卡纸
    {STATUS_E_PRINT_JAM_TRAY_3_SECTION, "tray3 jam"}, // 纸盒3卡纸
    {STATUS_E_PRINT_JAM_TRAY_4_SECTION, "tray4 jam"}, // 纸盒4卡纸
    {STATUS_E_PRINT_JAM_LCT_IN_SECTION, "lct in jam"}, // 内置大容量纸盒卡纸
    {STATUS_E_PRINT_JAM_LCT_EX_SECTION, "external lct jam"}, // 外置大容量纸盒卡纸
    {STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION, "manual tray jam"}, // 多功能进纸器卡纸
    {STATUS_E_PRINT_TRAY_1_OPEN, "tray1 open"}, // 纸盒1未插入
    {STATUS_E_PRINT_TRAY_2_OPEN, "tray2 open"}, // 纸盒2未插入
    {STATUS_E_PRINT_TRAY_3_OPEN, "tray3 open"}, // 纸盒3未插入
    {STATUS_E_PRINT_TRAY_4_OPEN, "tray4 open"}, // 纸盒4未插入
    {STATUS_E_PRINT_LCT_IN_OPEN, "lct open"}, // 内置大容量纸盒未插入
    {STATUS_E_PRINT_LCT_EX_UNSET, "external lct unset"}, // 外置大容量纸盒未设置
    {STATUS_E_PRINT_TRAY_LCT_IN_SET_ERROR, "lct in set error"}, // 内置大容量纸盒安装出错
    {STATUS_E_PRINT_TRAY_1_PAPER_EMPTY, "tray1 empty"}, // 纸盒1缺纸
    {STATUS_E_PRINT_TRAY_2_PAPER_EMPTY, "tray2 empty"}, // 纸盒2缺纸
    {STATUS_E_PRINT_TRAY_3_PAPER_EMPTY, "tray3 empty"}, // 纸盒3缺纸
    {STATUS_E_PRINT_TRAY_4_PAPER_EMPTY, "tray4 empty"}, // 纸盒4缺纸
    {STATUS_E_PRINT_LCT_IN_PAPER_EMPTY, "lct empty"}, // 内置大容量纸盒缺纸
    {STATUS_E_PRINT_LCT_EX_PAPER_EMPTY, "external lct empty"}, // 外置大容量纸盒缺纸
    {STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY, "manual tray empty"}, // 多功能进纸器缺纸
    {STATUS_E_PRINT_TRAY_1_SIZE_ERROR, "tray1 size error"}, // 纸盒1纸张尺寸不匹配
    {STATUS_E_PRINT_TRAY_2_SIZE_ERROR, "tray2 size error"}, // 纸盒2纸张尺寸不匹配
    {STATUS_E_PRINT_TRAY_3_SIZE_ERROR, "tray3 size error"}, // 纸盒3纸张尺寸不匹配
    {STATUS_E_PRINT_TRAY_4_SIZE_ERROR, "tray4 size error"}, // 纸盒4纸张尺寸不匹配
    {STATUS_E_PRINT_TRAY_MANUAL_SIZE_ERROR, "manual tray size error"}, // 多功能进纸器纸张尺寸不匹配
    {STATUS_E_PRINT_LCT_IN_SIZE_ERROR, "lct in size error"}, // 内置大容量纸盒纸张尺寸不匹配
    {STATUS_E_PRINT_LCT_EX_SIZE_ERROR, "external lct size error"}, // 外置大容量纸盒纸张尺寸不匹配
    {STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL, "standard tray receive full"}, // 标准接纸架满
    {STATUS_E_PRINT_TRAY_RECEIVE_1_FULL, "tray1 receive full"}, // 接纸架1满
    {STATUS_E_PRINT_TRAY_RECEIVE_2_FULL, "tray2 receive full"}, // 接纸架2满
    {STATUS_E_PRINT_TRAY_RECEIVE_3_FULL, "tray3 receive full"}, // 接纸架3满
    {STATUS_E_PRINT_FRONT_DOOR_OPEN, "front door open"}, // 前盖打开(盖门A打开)
    {STATUS_E_PRINT_SIDE_DOOR_OPEN, "side door open"}, // 侧门打开(盖门C打开)
    {STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN, "cabinet side door open"}, // 内建大容量盖门打开(盖门D打开)
    {STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN, "toner replenishment door open"}, // 粉盒补给门打开
    {STATUS_E_PRINT_LCT_EX_OPEN, "external lct open"}, // 外置大容量纸盒打开(盖门F打开)
    {STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN, "fns horizontal unit door open"}, // 装订器水平传送单元门开(盖门G打开)
    {STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN, "fns front door open"}, // 装订器前门(盖门H打开)
    {STATUS_E_PRINT_FNS_TOP_COVER_OPEN, "fns top cover open"}, // 装订器顶盖(盖门J打开)
    {STATUS_E_PRINT_3RD_TRAY_COVER_OPEN, "3rd tray cover open"}, // 第三托盘盖打开
    {STATUS_E_PRINT_Y_TONER_EMPTY, "yellow toner empty"}, // 黄色碳粉盒空
    {STATUS_E_PRINT_M_TONER_EMPTY, "magenta toner empty"}, // 品红色碳粉盒空
    {STATUS_E_PRINT_C_TONER_EMPTY, "cyan toner empty"}, // 青色碳粉盒空
    {STATUS_E_PRINT_K_TONER_EMPTY, "black toner empty"}, // 黑色碳粉盒空
    {STATUS_E_PRINT_W_TB_UNINSTALL, "waste toner box missing"}, // 废粉盒缺失或者不响应
    {STATUS_E_PRINT_Y_TB_UNINSTALL, "yellow toner box missing"}, // 黄色碳粉盒缺失或者不响应
    {STATUS_E_PRINT_M_TB_UNINSTALL, "magenta toner box missing"}, // 品红色碳粉盒缺失或者不响应
    {STATUS_E_PRINT_C_TB_UNINSTALL, "cyan toner box missing"}, // 青色碳粉盒缺失或者不响应
    {STATUS_E_PRINT_K_TB_UNINSTALL, "black toner box missing"}, // 黑色碳粉盒缺失或者不响应
    {STATUS_E_PRINT_Y_DR_UNINSTALL, "yellow drum missing"}, // 黄色鼓组件缺失或者不响应
    {STATUS_E_PRINT_M_DR_UNINSTALL, "magenta drum missing"}, // 品红色鼓组件缺失或者不响应
    {STATUS_E_PRINT_C_DR_UNINSTALL, "cyan drum missing"}, // 青色鼓组件缺失或者不响应
    {STATUS_E_PRINT_K_DR_UNINSTALL, "black drum missing"}, // 黑色鼓组件缺失或者不响应
    {STATUS_E_PRINT_Y_DR_LIFE_STOP, "yellow drum end"}, // 黄色鼓组件寿命尽
    {STATUS_E_PRINT_M_DR_LIFE_STOP, "magenta drum end"}, // 品红色鼓组件寿命尽
    {STATUS_E_PRINT_C_DR_LIFE_STOP, "cyan drum end"}, // 青色鼓组件寿命尽
    {STATUS_E_PRINT_K_DR_LIFE_STOP, "black drum end"}, // 黑色鼓组件寿命尽
    {STATUS_E_PRINT_Y_DV_LIFE_STOP, "yellow developer end"}, // 黄色显影组件寿命尽
    {STATUS_E_PRINT_M_DV_LIFE_STOP, "magenta developer end"}, // 品红色显影组件寿命尽
    {STATUS_E_PRINT_C_DV_LIFE_STOP, "cyan developer end"}, // 青色显影组件寿命尽
    {STATUS_E_PRINT_K_DV_LIFE_STOP, "black developer end"}, // 黑色显影组件寿命尽
    {STATUS_E_PRINT_Y_DV_UNINSTALL, "yellow developer missing"}, // 黄色显影组件缺失或者不响应
    {STATUS_E_PRINT_M_DV_UNINSTALL, "magenta developer missing"}, // 品红色显影组件缺失或者不响应
    {STATUS_E_PRINT_C_DV_UNINSTALL, "cyan developer missing"}, // 青色显影组件缺失或者不响应
    {STATUS_E_PRINT_K_DV_UNINSTALL, "black developer missing"}, // 黑色显影组件缺失或者不响应
    {STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION, "tray1 paper feed jam"}, // 纸盒1进纸口卡纸
    {STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION, "tray2 paper feed jam"}, // 纸盒2进纸口卡纸
    {STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION, "tray3 paper feed jam"}, // 纸盒3进纸口卡纸
    {STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION, "tray4 paper feed jam"}, // 纸盒4进纸口卡纸
    {STATUS_E_PRINT_JAM_LCT_EX_PAPER_FEED_SECTION, "external lct paper feed jam"}, // 大容量纸盒卡纸
    {STATUS_E_PRINT_JAM_TRAY_MANUAL_FEED_SECTION, "manual tray paper feed jam"}, // 多功能进纸器卡纸
    {STATUS_E_PRINT_JAM_REFEEDER_SECTION, "re-feeder jam"}, // 重新进纸处卡纸
    {STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1, "vertical transport jam"}, // 纵向传输处卡纸
    {STATUS_E_PRINT_JAM_LCT_EX_TRANSPORT_SECTION, "external lct transport jam"}, // 外置大容量纸盒传输处卡纸
    {STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1, "2nd transfer jam"}, // 第二转印处卡纸SECTION1
    {STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1, "duplex transport jam"}, // 双面传输处卡纸
    {STATUS_E_PRINT_JAM_OUTPUT_SECTION, "output jam"}, // 出纸处卡纸
    {STATUS_E_PRINT_JAM_VER_TRANSPORT_SECTION2, "vertical transport jam"}, // 纵向传输处卡纸SECTION2
    {STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2, "2nd transfer jam"}, // 第二转印处卡纸
    {STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION, "output jam"}, // 出纸处卡纸
    {STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION, "re-feeder jam"}, // 重新进纸处卡纸
    {STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2, "duplex transport jam"}, // 双面传输处卡纸
    {STATUS_E_PRINT_FUSING_UNIT_LIFE_STOP, "fusing unit life end"}, // 定影组件寿命尽
    {STATUS_E_PRINT_TRANSFER_BELT_UNIT_LIFE_STOP, "transfer belt unit life end"}, // 转印带寿命尽
    {STATUS_E_PRINT_TRANSFER_ROLLER_UNIT_LIFE_STOP, "transfer roller unit life end"}, // 转印辊寿命尽
    {STATUS_E_PRINT_TONER_FILTER_LIFE_STOP, "toner filter life end"}, // 粉盒过滤网寿命尽
    {STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_SECTION, "1st process tray jam"}, // 第一处理托盘处卡纸
    {STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION, "tray1 exit jam"}, // 接纸架1出纸处卡纸
    {STATUS_E_PRINT_JAM_FNS_2ND_DISCHARGE_SECTION, "2nd tray discharge jam"}, // 第二托盘排纸处卡纸
    {STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1, "transfer part1 jam"}, // 传输部分1卡纸
    {STATUS_E_PRINT_JAM_FNS_3RD_TRAY_SECTION, "3rd tray jam"}, // 第三托盘处
    {STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_SECTION, "2nd process tray jam"}, // 第二处理托盘处卡纸
    {STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION, "fns entrance jam"}, // 装订器入口处卡纸
    {STATUS_E_PRINT_JAM_FNS_HOR_TRANSPORT_SECTION, "horizontal transport jam"}, // 水平传输处卡纸
    {STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION, "folder pass jam"}, // 折叠通过处卡纸
    {STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION, "folder exit jam"}, // 折叠出纸处卡纸
    {STATUS_E_PRINT_JAM_TRANSPORT_SECTION, "transport jam"}, // 传送处卡纸
    {STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION, "lct in paper feed jam"}, // 内建大容量纸盒卡纸
    {STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION, "lct in transport jam"}, // 内建大容量纸盒传输处卡纸
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_EN_NOT_ON, "saddle stitch en not on"}, // 鞍式装订入口传感器未接通卡纸
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_EX_NOT_OFF, "saddle stitch ex not off"}, // 鞍式装订出口传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON, "fns entrance not on"}, // 装订器入口传感器未接通卡纸
    {STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF, "fns front not off"}, // 装订器入口传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON, "main tray ejection not on"}, // 主托盘出纸传感器未接通卡纸
    {STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_OFF, "main tray ejection not off"}, // 主托盘出纸传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF, "stapler stack tray not off"}, // 平订纸张检测传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON, "sub tray exit not on"}, // 次托盘出纸传感器未接通卡纸
    {STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF, "sub tray exit not off"}, // 次托盘出纸传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON, "fold ejection not on"}, // 折叠出纸传感器未接通卡纸
    {STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF, "fold ejection not off"}, // 折叠出纸传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_FS_SADDLE_EN_NOT_OFF, "fs saddle en not off"}, // FS534鞍式装订入口传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_NOT_ON, "center fold stack not on"}, // 居中对折纸张检测传感器未接通卡纸
    {STATUS_E_PRINT_JAM_ZU_LARGE_SIZE_MODE, "large size mode jam"}, // 第一折叠尖端切换卡纸
    {STATUS_E_PRINT_JAM_ZU_ALL_MODE, "all mode jam"}, // 第二折叠尖端L折卡纸
    {STATUS_E_PRINT_JAM_PUNCH, "punch jam"}, // 打孔卡纸
    {STATUS_E_PRINT_JAM_FNS_STAPLE, "fns staple jam"}, // 装订卡纸
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE, "saddle stitch staple jam"}, // 鞍式装订卡纸
    {STATUS_E_PRINT_JAM_SADDLE_EN_NOT_OFF, "saddle en not off"}, // 鞍式装订入口传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_ON, "tray3 conveyance on"}, // 托盘3输送传感器未打开卡纸
    {STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_OFF, "tray3 conveyance off"}, // 托盘3输送传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_MAIN_PADDLE_HOME_SNR, "main paddle home snr"}, // MAIN_PADDLE_HOME_SNR
    {STATUS_E_PRINT_JAM_EJECT_GRIP, "eject grip"}, // EJECT_GRIP
    {STATUS_E_PRINT_JAM_ETAMPER, "etamper"}, // ETAMPER
    {STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON, "ru entrance not on"}, // 过桥单元入口传感器未接通卡纸
    {STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF, "ru entrance not off"}, // 过桥单元入口传感器未关闭卡纸
    {STATUS_E_PRINT_JAM_PUNCH_REAR_DETECTION_OFF, "punch rear detection off"}, // 打空检测传感器未关闭卡纸
    {STATUS_E_PRINT_CONTROL_JAM_1, "control jam 1"}, // 控制卡纸1
    {STATUS_E_PRINT_CONTROL_JAM_2, "control jam 2"}, // 控制卡纸2
    {STATUS_E_PRINT_CONTROL_JAM_3, "control jam 3"}, // 控制卡纸3
    {STATUS_E_PRINT_CONTROL_JAM_4, "control jam 4"}, // 控制卡纸4
    {STATUS_E_PRINT_CONTROL_JAM_5, "control jam 5"}, // 控制卡纸5
    {STATUS_E_PRINT_CONTROL_JAM_6, "control jam 6"}, // 控制卡纸6
    {STATUS_E_PRINT_CONTROL_JAM_7, "control jam 7"}, // 控制卡纸7
    {STATUS_E_PRINT_CONTROL_JAM_8, "control jam 8"}, // 控制卡纸8
    {STATUS_E_PRINT_CONTROL_JAM_9, "control jam 9"}, // 控制卡纸9
    {STATUS_E_PRINT_CONTROL_JAM_10, "control jam 10"}, // 控制卡纸10
    {STATUS_E_PRINT_CONTROL_JAM_11, "control jam 11"}, // 控制卡纸11
    {STATUS_E_PRINT_CONTROL_JAM_12, "control jam 12"}, // 控制卡纸12
    {STATUS_E_PRINT_CONTROL_JAM_13, "control jam 13"}, // 控制卡纸13
    {STATUS_E_PRINT_CONTROL_JAM_14, "control jam 14"}, // 控制卡纸14
    {STATUS_E_PRINT_CONTROL_JAM_15, "control jam 15"}, // 控制卡纸15
    {STATUS_E_PRINT_DUPLEX_DISABLE, "duplex disabled"}, // 禁止双面打印
    {STATUS_E_PRINT_ATTRIBUTE_MUTEX_INCORRECT, "attribute mutex error"}, // 非法互斥错误
    {STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_OFF, "tray3 exit sensor not on"}, // 接纸架3出纸传感器未接通卡纸
    {STATUS_E_PRINT_Y_DV_MISMATCH, "yellow developer mismatch"}, // Y通道显影组件不匹配
    {STATUS_E_PRINT_M_DV_MISMATCH, "magenta developer mismatch"}, // M通道显影组件不匹配
    {STATUS_E_PRINT_C_DV_MISMATCH, "cyan developer mismatch"}, // C通道显影组件不匹配
    {STATUS_E_PRINT_K_DV_MISMATCH, "black developer mismatch"}, // K通道显影组件不匹配
    {STATUS_E_PRINT_FEEDTRAY_1_TRAY_SET_E_ST, "standard tray setting error"}, // 标准进纸盒设置错误状态
    {STATUS_E_PRINT_FEEDTRAY_2_TRAY_SET_E_ST, "tray2 setting error"}, // 第二进纸盒设置错误状态
    {STATUS_E_PRINT_FEEDTRAY_LCT_IN_TRAY_SET_E_ST, "internal lct setting error"}, // 内置大容量设置错误状态
    {STATUS_E_PRINT_OPTION_TRAY_FULL, "optional tray full"}, // 选择纸盒满
    {STATUS_E_PRINT_PROCESS_TRAY, "process tray error"}, // 处理托盘异常（有纸）
    {STATUS_E_PRINT_PROCESS_TRAY_1ST_BIN_FULL, "1st process tray full"}, // 第一处理纸盒满
    {STATUS_E_PRINT_PROCESS_TRAY_2ND_BIN_FULL, "2nd process tray full"}, // 第二处理纸盒满
    {STATUS_E_PRINT_COLLATE_FILE, "collate job error"}, // 逐份作业错误
    {STATUS_E_PRINT_PINCODE_FILE, "pincode job error"}, // 密码作业错误
    {STATUS_E_PRINT_INTERNAL_ERROR, "print internal error"}, // 打印内部错误
    {STATUS_E_PRINT_DOC_PARSE_ERROR, "document parse error"}, // DOC解析异常
    {STATUS_E_PRINT_SDK_ATTRIBUTE_INCORRECT, "sdk error"}, // SDK错误
    {STATUS_E_PRINT_STITCHING_STAPLE_EMPTY, "stitching staple empty"}, // 平订订书钉空
    {STATUS_E_PRINT_SADDLE_BIND_F_STAPLE_EMPTY, "saddle bind front staple empty"}, // 鞍式装订订书钉（前）空
    {STATUS_E_PRINT_SADDLE_BIND_B_STAPLE_EMPTY, "saddle bind back staple empty"}, // 鞍式装订订书钉（后）空
    {STATUS_E_PRINT_STATUS_INTERNAL_PAGE_PDF_FAIL, "internal page pdf generation failed"}, // 内部页设备报告生成失败
    // {STATUS_E_PRINT_TONER_EMPTY, "toner empty error"}, // 粉尽错误------------->状态移除
    {STATUS_E_PRINT_FEEDTRAY_3_TRAY_SET_E_ST, "tray3 setting error"}, // 第三进纸盒设置错误状态
    {STATUS_E_PRINT_FEEDTRAY_4_TRAY_SET_E_ST, "tray4 setting error"}, // 第四进纸盒设置错误状态
    {STATUS_E_PRINT_JOB_LOCKED, "print job locked error"}, // 打印作业管控错误
    {STATUS_E_PRINT_TRAY_4_PAPER_SIZE_MISMATCH, "tray4 paper size mismatch"}, // 纸盒4纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_LCT_IN_PAPER_SIZE_MISMATCH, "internal lct paper size mismatch"}, // 内置大容量纸盒纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_LCT_OUT_PAPER_SIZE_MISMATCH, "external lct paper size mismatch"}, // 外置大容量纸盒纸张尺寸不匹配,需要更换为XXX
    {STATUS_E_PRINT_TRAY_4_PAPER_TYPE_MISMATCH, "tray4 paper type mismatch"}, // 纸盒4纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_LCT_IN_PAPER_TYPE_MISMATCH, "internal lct paper type mismatch"}, // 内置大容量纸盒纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_LCT_OUT_PAPER_TYPE_MISMATCH, "external lct paper type mismatch"}, // 外置大容量纸盒纸张类型不匹配,需要更换为XXXX
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_TRAY, "tray component not supported"}, // 纸盒组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_TONER, "toner component not supported"}, // 粉盒组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DRUM, "drum component not supported"}, // 鼓组件组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DV, "developer component not supported"}, // 显影组件组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_RECEIVE, "output tray component not supported"}, // 接纸架组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DOOR, "door component not supported"}, // 门组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_PUNCH, "punch component not supported"}, // 打孔组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_BIND, "staple component not supported"}, // 订钉组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_FOLD, "fold component not supported"}, // 折叠组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_SHIFT, "shift component not supported"}, // 偏移组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_Z_FOLD, "z-fold component not supported"}, // Z折组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_FINISHER, "finisher component not supported"}, // 装订器组件不支持
    {STATUS_E_PRINT_ATTRIBUTE_COMPONENT_PRO_TRAY, "process tray component not supported"}, // 处理托盘组件不支持
    {STATUS_E_PRINT_STORAGE_JOB_ERROR, "job storage failed"}, // 作业存储失败
    {STATUS_E_PRINT_STAPLE_WASTE_FULL_OR_NOT_SET, "staple waste box full or not installed"}, // 平钉废盒已满或未安装
    {STATUS_E_PRINT_PUNCH_WASTE_NOT_SET, "punch waste box not installed"}, // 打孔废料盒未安装
    {STATUS_E_PRINT_PUNCH_WASTE_FULL, "punch waste box full"}, // 打孔废料盒满
    {STATUS_E_PRINT_PROCESS_TRAY_1ST_EXIST_PAPER, "1st process tray has paper"}, // 第一处理托盘处异常有纸
    {STATUS_E_PRINT_PROCESS_TRAY_2ND_EXIST_PAPER, "2nd process tray has paper"}, // 第二处理托盘处异常有纸
    {STATUS_E_PRINT_DALAY_JOB_WITHOUT_DISK, "delayed job without hard disk"}, // 未安装硬盘，不支持延时打印作业
    {STATUS_E_PRINT_SAMPLE_JOB_WITHOUT_DISK, "sample job without hard disk"}, // 未安装硬盘，不支持样本打印作业
    {STATUS_E_PRINT_UNSUPPORT_JOB_TYPE, "unsupported job type"}, // 不支持的作业类型
    {STATUS_E_PRINT_UNATTAIN_JAM_FRONTEND, "frontend sensor not reached jam"}, // 先端传感器未达JAM
    {STATUS_E_PRINT_STRANDED_JAM_FRONTEND, "frontend sensor stranded jam"}, // 先端传感器滞留JAM
    {STATUS_E_PRINT_RESIDUAL_JAM_FRONTEND, "frontend sensor residual jam"}, // 先端传感器残留JAM
    {STATUS_E_PRINT_TRAY_1_UNSUPPORT_PAPER, "tray1 unsupported paper"}, // 纸盒1中纸张为不支持继续打印的纸张
    {STATUS_E_PRINT_TRAY_2_UNSUPPORT_PAPER, "tray2 unsupported paper"}, // 纸盒2中纸张为不支持继续打印的纸张
    {STATUS_E_PRINT_TRAY_3_UNSUPPORT_PAPER, "tray3 unsupported paper"}, // 纸盒3中纸张为不支持继续打印的纸张
    {STATUS_E_PRINT_TRAY_4_UNSUPPORT_PAPER, "tray4 unsupported paper"}, // 纸盒4中纸张为不支持继续打印的纸张
    {STATUS_E_PRINT_MP_TRAY_UNSUPPORT_PAPER, "multi-purpose tray unsupported paper"}, // 多功能进纸器中纸张为不支持继续打印的纸张
    {STATUS_E_PRINT_LCT_IN_TRAY_UNSUPPORT_PAPER, "internal lct unsupported paper"}, // 内置大容量纸盒中纸张为不支持继续打印的纸张
    // 新增状态预处理：状态ID未分配
    {STATUS_E_PRINT_PRE_PARSER_FAILED, "pre-parser failed"}, // 预解析失败
    // 新增状态预处理：状态ID未分配

    // F Status
    {STATUS_F_PRINT_LSU_FAN_ERROR, "lsu fan error"}, // LSU风扇异常
    {STATUS_F_PRINT_FUSER_EXHAUST_FAN_ERROR, "fuser exhaust fan error"}, // 定影排热风扇异常
    {STATUS_F_PRINT_LSU_FAN_ERROR_2, "lsu fan error 2"}, // LSU风扇异常2
    {STATUS_F_PRINT_MAIN_FAN_ERROR, "main fan error"}, // 主风道风扇异常
    {STATUS_F_PRINT_FUSER_TEMP_RAISED_SLOW, "fuser temperature rising slowly"}, // 通常电压时定影不能reload(预热温升过慢)
    {STATUS_F_PRINT_FUSER_THERMISTOR_ERROR, "fuser thermistor error"}, // 定影开路：1
    {STATUS_F_PRINT_FUSER_OVER_HEAT, "fuser overheating"}, // 定影高温检知（软件）1
    {STATUS_F_PRINT_FUSER_HW_OVER_HEAT, "fuser hardware overheating"}, // 定影高温检知（硬件）1
    {STATUS_F_PRINT_FUSER_THERMISTOR_ERROR_3, "fuser thermistor error 3"}, // 定影开路：3
    {STATUS_F_PRINT_FUSER_OVER_HEAT_3, "fuser overheating 3"}, // 定影高温检知（软件）3
    {STATUS_F_PRINT_FUSER_HW_OVER_HEAT_3, "fuser hardware overheating 3"}, // 定影高温检知（硬件）3
    {STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_1, "fuser temperature too low 1"}, // 定影温度过低1
    {STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_2, "fuser temperature too low 2"}, // 定影温度过低2
    {STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_3, "fuser temperature too low 3"}, // 定影温度过低3
    {STATUS_F_PRINT_THERMISTOR_DAMAGE_1, "fuser thermistor damage 1"}, // 定影热敏电阻异常：1
    {STATUS_F_PRINT_THERMISTOR_DAMAGE_2, "fuser thermistor damage 2"}, // 定影热敏电阻异常：2
    {STATUS_F_PRINT_THERMISTOR_DAMAGE_3, "fuser thermistor damage 3"}, // 定影热敏电阻异常：3
    {STATUS_F_PRINT_FUSER_MOTOR_ERROR, "fuser motor error"}, // 定影马达异常
    {STATUS_F_PRINT_COLOR_DEVELOP_MOTOR_ERROR, "color developer motor error"}, // 彩色显影马达异常
    {STATUS_F_PRINT_BLACKDEVELOP_MOTOR_ERROR, "black developer motor error"}, // 黑色显影马达异常
    {STATUS_F_PRINT_MULTI_PRISM_MOTOR_ON_TIMEOUT, "multi prism motor on timeout"}, // 多棱镜马达异常：ON时超时
    {STATUS_F_PRINT_MULTI_PRISM_MOTOR_OFF_TIMEOUT, "multi prism motor off timeout"}, // 多棱镜马达异常：OFF时超时
    {STATUS_F_PRINT_MULTI_PRISM_MOTOR_SIGNAL_ERROR, "multi prism motor signal error"}, // 多棱镜马达异常：XFERDY信号错误
    {STATUS_F_PRINT_LD0_COCURRENT_K_ERROR, "black ld0 sync detection error"}, // K色：先端：LD0同期检知异常
    {STATUS_F_PRINT_LD0_COCURRENT_Y_ERROR, "yellow ld0 sync detection error"}, // Y色：先端：LD0同期检知异常
    {STATUS_F_PRINT_MCU_COMMUNICATION_ERROR, "main-sub mcu communication error"}, // 主副MCU通信异常
    {STATUS_F_PRINT_TRAY_COMMUNICATION_ERROR, "tray communication error"}, // 二层纸盒通信异常
    {STATUS_F_PRINT_EEPROM_IIC_COMMUNICATION_ERROR, "eeprom iic communication error"}, // EEPROMIIC通信异常
    {STATUS_F_PRINT_HUMITURE_IIC_COMMUNICATION_ERROR, "humidity iic communication error"}, // 温湿度IIC通信异常
    {STATUS_F_PRINT_CTL_VIDEO_OK_NOTIFY_ERROR, "ctl video ok notify error"}, // CTL侧画像准备OK通知未送达
    {STATUS_F_PRINT_EC_WARMUP_CANNOT_STOP, "engine warmup cannot stop"}, // 引擎固件Bug之机内监视模块的预热动作无法结束
    {STATUS_F_PRINT_EC_PAGE_BUFFER_FULL, "engine page buffer full"}, // 引擎固件Bug之打印页数管理bufferfull
    {STATUS_F_PRINT_EC_PAGE_CANNOT_START, "engine page cannot start"}, // 引擎固件Bug之某页打印执行开始不可
    {STATUS_F_PRINT_EC_PAGE_CANNOT_STOP, "engine page cannot stop"}, // 引擎固件Bug之某页打印执行结束不可
    {STATUS_F_PRINT_EC_PRINT_CANNOT_STOP, "engine print cannot stop"}, // 引擎固件Bug之打印整体时序结束不可
    {STATUS_F_PRINT_ENGINE_PRARAM_FILE, "engine parameter file error"}, // 引擎参数文件异常
    {STATUS_F_PRINT_FACTORY_FILE, "factory file error"}, // 工厂校验文件异常
    {STATUS_F_PRINT_CALIBRATION_PARAM, "calibration parameter error"}, // 色彩校正参数异常
    {STATUS_F_PRINT_TEMPERATURE_UNSTABLE_1, "temperature unstable 1"}, // 温度不安定1
    {STATUS_F_PRINT_FUSER_THERMISTOR_ERROR_2, "fuser thermistor error 2"}, // 定影开路：2
    {STATUS_F_PRINT_FUSER_OVER_HEAT_2, "fuser overheating 2"}, // 定影高温检知(软件)2
    {STATUS_F_PRINT_FUSER_THERMISTOR_DAMAGE_2, "fuser thermistor damage 2"}, // 定影热敏电阻损坏：2
    {STATUS_F_PRINT_FUSER_PRESSURE_DECOMPRESS_TIMEOUT, "fuser pressure timeout"}, // 定影加/解压超时
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_K, "black charging output error"}, // 充电出力异常-K色
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_M, "magenta charging output error"}, // 充电出力异常-M色
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_Y, "yellow charging output error"}, // 充电出力异常-Y色
    {STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_C, "cyan charging output error"}, // 充电出力异常-C色
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_K, "black developer high voltage error"}, // 显影高压出力异常-K色
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_M, "magenta developer high voltage error"}, // 显影高压出力异常-M色
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_Y, "yellow developer high voltage error"}, // 显影高压出力异常-Y色
    {STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_C, "cyan developer high voltage error"}, // 显影高压出力异常-C色
    {STATUS_F_PRINT_TONER_SUPPLY_MOTOR_ERROR, "toner supply motor error"}, // 补粉马达稳定异常
    {STATUS_F_PRINT_BK_PHOTORECEPTOR_MOTOR_ERROR, "black photoreceptor motor error"}, // BK感光体马达异常
    {STATUS_F_PRINT_COLOR_PHOTORECEPTRO_MOTOR_ERROR, "color photoreceptor motor error"}, // 彩色感光体马达异常
    {STATUS_F_PRINT_1TB_COMPRESS_MOTOR_ERROR, "1tb compress motor error"}, // 1TB加压马达异常
    {STATUS_F_PRINT_NEW_DRUM_EXCHANGE_ERROR, "new drum exchange error"}, // 新光鼓单元交换异常
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_K, "black tc sensor upper limit error"}, // TC传感上限异常:K
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_M, "magenta tc sensor upper limit error"}, // TC传感上限异常:M
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_C, "cyan tc sensor upper limit error"}, // TC传感上限异常:C
    {STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_Y, "yellow tc sensor upper limit error"}, // TC传感上限异常:Y
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_K, "black tc sensor lower limit error"}, // TC传感下限异常:K
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_M, "magenta tc sensor lower limit error"}, // TC传感下限异常:M
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_C, "cyan tc sensor lower limit error"}, // TC传感下限异常:C
    {STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_Y, "yellow tc sensor lower limit error"}, // TC传感下限异常:Y
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_K, "black tc sensor init adjust error"}, // TC传感初期设定调整异常:K
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_M, "magenta tc sensor init adjust error"}, // TC传感初期设定调整异常:M
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_C, "cyan tc sensor init adjust error"}, // TC传感初期设定调整异常:C
    {STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_Y, "yellow tc sensor init adjust error"}, // TC传感初期设定调整异常:Y
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_K, "black tc sensor sampling error"}, // TC传感采样异常:K
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_M, "magenta tc sensor sampling error"}, // TC传感采样异常:M
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_C, "cyan tc sensor sampling error"}, // TC传感采样异常:C
    {STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_Y, "yellow tc sensor sampling error"}, // TC传感采样异常:Y
    {STATUS_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_1, "intermediate transfer high voltage error"}, // 中间转写高压
    {STATUS_F_PRINT_TRANS_MOTOR_ERROR_1, "intermediate transfer motor error"}, // 中间转印马达
    {STATUS_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_2, "secondary transfer high voltage error"}, // 二次转印高压电源异常
    {STATUS_F_PRINT_TRANS_SEPARATION_MOTOR_ERROR_2, "secondary transfer separation motor error"}, // 二次转印接离马达异常
    {STATUS_F_PRINT_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2, "secondary transfer constant voltage output error"}, // 二次转印高压恒压出力异常
    {STATUS_F_PRINT_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2, "secondary transfer constant current output error"}, // 二次转印高压恒流出力异常
    {STATUS_F_PRINT_TRANS_SEPARATION_HIGH_VOL_ERROR, "separation high voltage error"}, // 分离高压异常
    {STATUS_F_PRINT_REMOVE_LAMP_ERROR, "discharge lamp error"}, // 除电灯异常
    {STATUS_F_PRINT_HIGH_VOL_PLATE_INSERT_ERROR, "high voltage board insert error"}, // 高压板插入异常
    {STATUS_F_PRINT_HIGH_VOL_DETECTION_ERROR, "high voltage detection error"}, // 高压检测故障
    {STATUS_F_PRINT_LSU_CABLE_NOT_CONNECTED, "lsu cable not connected"}, // LSU排线检测：LSU排线未连接
    {STATUS_F_PRINT_DEVELOP_CO_HIGH_ERROR_K, "black developer concentration high error"}, // 显影组件浓度异常高K通道
    {STATUS_F_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_K, "black concentration sensor voltage lower limit"}, // 浓度传感器输出电压超出下限K通道
    {STATUS_F_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_K, "black concentration sensor voltage upper limit"}, // 浓度传感器输出电压超出上限K通道
    {STATUS_F_PRINT_CO_SENSOR_DETECT_ERROR_K, "black concentration sensor detection error"}, // 浓度传感器异常K通道
    {STATUS_F_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_K, "black toner supply error"}, // 碳粉供应故障K通道
    {STATUS_F_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_K, "black toner supply error (5-10%)"}, // 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)K通道
    {STATUS_F_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_K, "black toner cartridge empty (<5%)"}, // 粉桶碳粉空错误(粉桶寿命小于5%)K通道
    {STATUS_F_PRINT_ENV_TEMP_BOTH_ERROR, "internal and external temperature sensor error"}, // 内外环境温湿度传感器均异常
    {STATUS_F_PRINT_LD0_COCURRENT_K_UNSTABLE, "black ld0 sync signal unstable"}, // K色：LD0同期信号不稳定
    {STATUS_F_PRINT_LD0_COCURRENT_Y_UNSTABLE, "yellow ld0 sync signal unstable"}, // Y色：LD0同期信号不稳定
    {STATUS_F_PRINT_EC_CALIBRATION_CANNOT_STOP, "engine calibration cannot stop"}, // 引擎固件BUG之色彩校正执行结束不可
    {STATUS_F_PRINT_COMPLETE_COOLING_FAN1_ERROR, "main cooling fan 1 error"}, // 整机散热风扇1异常
    {STATUS_F_PRINT_LSU_FRONT_COOLING_FAN_ERROR, "lsu front cooling fan error"}, // LSU前侧散热风扇异常
    {STATUS_F_PRINT_FUSER_EXIT_COOLING_FAN_ERROR, "fuser exit cooling fan error"}, // 定影出口散热风扇异常
    {STATUS_F_PRINT_IMAGING_MAIN_MOTOR_K_ERROR, "black main motor error"}, // 主马达K异常
    {STATUS_F_PRINT_IMAGING_MAIN_MOTOR_YMC_ERROR, "ymc main motor error"}, // 主马达YMC异常
    {STATUS_F_PRINT_FE0340_01, "black discharge lamp error"}, // 除电灯异常:K
    {STATUS_F_PRINT_FE0340_02, "magenta discharge lamp error"}, // 除电灯异常:M
    {STATUS_F_PRINT_FE0340_03, "cyan discharge lamp error"}, // 除电灯异常:C
    {STATUS_F_PRINT_FE0340_04, "yellow discharge lamp error"}, // 除电灯异常:Y
    {STATUS_F_PRINT_FE0154_00, "fuser hardware overheating 2"}, // 定影高温检知（硬件）：2
    {STATUS_F_PRINT_FE0164_00, "fuser hardware overheating 3"}, // 定影高温检知（硬件）：3
    {STATUS_F_PRINT_FE0170_01, "zero cross signal error (relay welded)"}, // 过零信号异常(定影继电器熔着)
    {STATUS_F_PRINT_FE0170_02, "zero cross signal error (no signal)"}, // 过零信号异常(无过零信号)
    {STATUS_F_PRINT_FE0170_03, "zero cross signal error (low frequency)"}, // 过零信号异常(过零信号频率过低)
    {STATUS_F_PRINT_FE0172_01, "fuser pressure timeout"}, // 定影加压超时异常
    {STATUS_F_PRINT_FE0172_02, "fuser decompression timeout"}, // 定影解压超时异常
    {STATUS_F_PRINT_FE0174_00, "fuser not inserted"}, // 定影未插入
    {STATUS_F_PRINT_FE0280_01, "black phase sensor error"}, // 黑色相位传感器异常
    {STATUS_F_PRINT_FE0280_02, "color phase sensor error"}, // 彩色相位传感器异常
    {STATUS_F_PRINT_FE0820_00, "optional communication error"}, // 选配通信异常
    {STATUS_F_PRINT_FE0821_00, "103 communication error"}, // 103通信异常
    {STATUS_F_PRINT_FE0822_00, "003 communication error"}, // 003通信异常
    {STATUS_F_PRINT_FE0840_00, "eeprom communication error"}, // EEPROM通信异常
    {STATUS_F_PRINT_FE0171_01, "relay short circuit"}, // 继电器短路
    {STATUS_F_PRINT_FE0171_02, "thyristor short circuit"}, // 可控硅短路
    {STATUS_F_PRINT_FE0903_11, "engine auto toner supply cannot stop"}, // 引擎固件Bug之自动供粉执行无法结束
    {STATUS_F_PRINT_MAIN_MOTOR_ERROR, "main motor error"}, // 主马达异常
    {STATUS_F_PRINT_FE0830_00, "iu chip communication error"}, // IUChip通信异常
    {STATUS_F_PRINT_FE0180_00, "ac input voltage too low"}, // AC输入电压过低
    {STATUS_F_PRINT_FE0180_01, "ac input voltage too high"}, // AC输入电压过高
    {STATUS_F_PRINT_FE0181_00, "full power heating time too long"}, // 全功率加热时间过长
    {STATUS_F_PRINT_FE0182_00, "sleep mode abnormal heating"}, // 休眠模式异常加热
    {STATUS_F_PRINT_FE0183_00, "fuser heating error (safety mcu detected)"}, // 定影加温异常（产品安全MCU检知）
    {STATUS_F_PRINT_FE0184_00, "fuser temperature difference too large"}, // 定影温度采集差值过大
    {STATUS_F_PRINT_FE_UNKNOWN, "undefined fe status in engine protocol"}, // 引擎升起协议中未定义的FE状态
    {STATUS_F_PRINT_FE0142_01, "fuser center preheat temperature rise detection cannot start"}, // 定影中央预热温升检测开始不可（通常电压）
    {STATUS_F_PRINT_FE0142_03, "fuser center preheat target temperature unreachable"}, // 定影中央预热目标温度不可达（通常电压）
    {STATUS_F_PRINT_FE0145_00, "fuser center heater continuous lighting"}, // 定影中央加热器连续点灯
    {STATUS_F_PRINT_FE0152_02, "fuser edge preheat temperature rise too slow"}, // 定影端部预热温升过慢（通常电压）
    {STATUS_F_PRINT_FE0171_00, "relay and thyristor both short circuit"}, // 继电器和可控硅都短路
    {STATUS_F_PRINT_FE0152_03, "fuser preheat target temperature unreachable 2"}, // 通常电压时定影预热不可(预热目标温度不可达)：2
    {STATUS_F_PRINT_FE0153_00, "fuser software overheating 2"}, // 定影高温检知（软件）：2
    {STATUS_F_PRINT_FE0570_00, "fuser thermistor not connected"}, // 定影热敏电阻未插
    {STATUS_F_PRINT_FE0171_03, "relay or thyristor open circuit"}, // 继电器或可控硅断路
    {STATUS_F_PRINT_FE0351_01, "high voltage error 1"}, // 高压故障
    {STATUS_F_PRINT_FE0351_02, "high voltage error 2"}, // 高压故障
    {STATUS_F_PRINT_FE0560_00, "optional tray stack count error"}, // 选配纸盒叠加段数异常
    {STATUS_F_PRINT_FE0900_00, "consumable communication error"}, // 耗材通信异常
    {STATUS_F_PRINT_FE0850_00, "humidity sensor communication error"}, // 温湿度Sensor通信异常
    {STATUS_F_PRINT_FE0175_00, "full power heating time too long (soc detected)"}, // 全功率加热时间过长(SOC检知)
    {STATUS_F_PRINT_FE0822_01, "003 communication error (iic to pwm)"}, // 003通信异常（IIC转PWM）
    {STATUS_F_PRINT_FE0361_00, "room temperature too low, adjust to above 0°c"}, // 室温过低，将影响正常打印，请尽快将室温调整到0℃以上
    {STATUS_F_PRINT_FE0185_00, "standard tray paper spacing sensor error"}, // 标准纸盒纸间距传感器异常
    {STATUS_F_PRINT_FE0185_01, "multi-purpose tray paper spacing sensor error"}, // 多功能纸盒纸间距传感器异常
    {STATUS_F_PRINT_FE0901_00, "safety 003 mcu ac ready notification not delivered"}, // 安全003MCUACready通知未送达
    {STATUS_F_PRINT_FE0904_01, "engine parameter file error (unrecoverable)"}, // 引擎参数文件异常（引擎不可恢复异常）
    {STATUS_F_PRINT_FE0904_02, "factory calibration file error (unrecoverable)"}, // 工厂校验文件异常（引擎不可恢复异常）
    {STATUS_F_PRINT_FE0904_03, "color calibration parameter error (unrecoverable)"}, // 色彩校正参数异常（引擎不可恢复异常）
    {STATUS_F_PRINT_ENGINE_COMMUNICATION_ERROR, "engine communication error"}, // 引擎通讯异常
    {STATUS_F_PRINT_FE0141_01, "fuser edge thermistor disconnected"}, // 定影端部热敏电阻断线
    {STATUS_F_PRINT_FE0142_04, "fuser self-test 120°c unreachable before printing"}, // 打印前定影自检温度120℃不可达异常
    {STATUS_F_PRINT_FE0145_01, "fuser heater heating error: center"}, // 定影加热器加热异常：中间
    {STATUS_F_PRINT_FE0461_00, "ld enable signal error"}, // LD使能信号异常
    {STATUS_F_PRINT_FE0461_01, "lsu encryption authentication failed"}, // LSU加密认证失败
    {STATUS_F_PRINT_A_31_2, "a-level fatal error 31_2"}, // A级致命错误31_2
    {STATUS_F_PRINT_A_34_25, "a-level fatal error 34_25"}, // A级致命错误34_25
    {STATUS_F_PRINT_A_38_22, "a-level fatal error 38_22"}, // A级致命错误38_22
    {STATUS_F_PRINT_A_38_25, "a-level fatal error 38_25"}, // A级致命错误38_25
    {STATUS_F_PRINT_A_38_26, "a-level fatal error 38_26"}, // A级致命错误38_26
    {STATUS_F_PRINT_A_37_22, "a-level fatal error 37_22"}, // A级致命错误37_22
    {STATUS_F_PRINT_A_37_25, "a-level fatal error 37_25"}, // A级致命错误37_25
    {STATUS_F_PRINT_A_37_26, "a-level fatal error 37_26"}, // A级致命错误37_26
    {STATUS_F_PRINT_A_37_32, "a-level fatal error 37_32"}, // A级致命错误37_32
    {STATUS_F_PRINT_A_37_36, "a-level fatal error 37_36"}, // A级致命错误37_36
    {STATUS_F_PRINT_A_37_37, "a-level fatal error 37_37"}, // A级致命错误37_37
    {STATUS_F_PRINT_A_37_38, "a-level fatal error 37_38"}, // A级致命错误37_38
    {STATUS_F_PRINT_A_39_22, "a-level fatal error 39_22"}, // A级致命错误39_22
    {STATUS_F_PRINT_A_39_25, "a-level fatal error 39_25"}, // A级致命错误39_25
    {STATUS_F_PRINT_A_39_26, "a-level fatal error 39_26"}, // A级致命错误39_26
    {STATUS_F_PRINT_A_39_2B, "a-level fatal error 39_2b"}, // A级致命错误39_2B
    {STATUS_F_PRINT_A_3B_2, "a-level fatal error 3b_2"}, // A级致命错误3B_2
    {STATUS_F_PRINT_A_3B_3, "a-level fatal error 3b_3"}, // A级致命错误3B_3
    {STATUS_F_PRINT_A_3B_7, "a-level fatal error 3b_7"}, // A级致命错误3B_7
    {STATUS_F_PRINT_A_3B_9, "a-level fatal error 3b_9"}, // A级致命错误3B_9

    // B级致命错误
    {STATUS_F_PRINT_B_51_3, "b-level fatal error 51_3"}, // B级致命错误51_3
    {STATUS_F_PRINT_B_22_4, "b-level fatal error 22_4"}, // B级致命错误22_4
    {STATUS_F_PRINT_B_22_53, "b-level fatal error 22_53"}, // B级致命错误22_53
    {STATUS_F_PRINT_B_22_54, "b-level fatal error 22_54"}, // B级致命错误22_54
    {STATUS_F_PRINT_B_22_55, "b-level fatal error 22_55"}, // B级致命错误22_55
    {STATUS_F_PRINT_B_22_56, "b-level fatal error 22_56"}, // B级致命错误22_56
    {STATUS_F_PRINT_B_53_55, "b-level fatal error 53_55"}, // B级致命错误53_55
    {STATUS_F_PRINT_B_53_60, "b-level fatal error 53_60"}, // B级致命错误53_60
    {STATUS_F_PRINT_B_53_61, "b-level fatal error 53_61"}, // B级致命错误53_61
    {STATUS_F_PRINT_B_53_62, "b-level fatal error 53_62"}, // B级致命错误53_62
    {STATUS_F_PRINT_B_53_63, "b-level fatal error 53_63"}, // B级致命错误53_63
    {STATUS_F_PRINT_B_53_58, "b-level fatal error 53_58"}, // B级致命错误53_58
    {STATUS_F_PRINT_B_53_4, "b-level fatal error 53_4"}, // B级致命错误53_4
    {STATUS_F_PRINT_B_33_2, "b-level fatal error 33_2"}, // B级致命错误33_2
    {STATUS_F_PRINT_B_33_6, "b-level fatal error 33_6"}, // B级致命错误33_6
    {STATUS_F_PRINT_B_33_7, "b-level fatal error 33_7"}, // B级致命错误33_7
    {STATUS_F_PRINT_B_23_50, "b-level fatal error 23_50"}, // B级致命错误23_50
    {STATUS_F_PRINT_B_23_57, "b-level fatal error 23_57"}, // B级致命错误23_57
    {STATUS_F_PRINT_B_53_51, "b-level fatal error 53_51"}, // B级致命错误53_51
    {STATUS_F_PRINT_B_53_6, "b-level fatal error 53_6"}, // B级致命错误53_6
    {STATUS_F_PRINT_B_23_55, "b-level fatal error 23_55"}, // B级致命错误23_55
    {STATUS_F_PRINT_B_32_1, "b-level fatal error 32_1"}, // B级致命错误32_1
    {STATUS_F_PRINT_B_32_2, "b-level fatal error 32_2"}, // B级致命错误32_2
    {STATUS_F_PRINT_B_1_6, "b-level fatal error 1_6"}, // B级致命错误1_6
    {STATUS_F_PRINT_B_1_7, "b-level fatal error 1_7"}, // B级致命错误1_7
    {STATUS_F_PRINT_B_1_8, "b-level fatal error 1_8"}, // B级致命错误1_8
    {STATUS_F_PRINT_B_1_9, "b-level fatal error 1_9"}, // B级致命错误1_9
    {STATUS_F_PRINT_B_21_52, "b-level fatal error 21_52"}, // B级致命错误21_52
    {STATUS_F_PRINT_B_21_53, "b-level fatal error 21_53"}, // B级致命错误21_53
    {STATUS_F_PRINT_B_21_54, "b-level fatal error 21_54"}, // B级致命错误21_54
    {STATUS_F_PRINT_B_21_55, "b-level fatal error 21_55"}, // B级致命错误21_55
    {STATUS_F_PRINT_B_21_56, "b-level fatal error 21_56"}, // B级致命错误21_56
    {STATUS_F_PRINT_B_31_1, "b-level fatal error 31_1"}, // B级致命错误31_1
    {STATUS_F_PRINT_B_31_03, "b-level fatal error 31_03"}, // B级致命错误31_03
    {STATUS_F_PRINT_B_31_04, "b-level fatal error 31_04"}, // B级致命错误31_04
    {STATUS_F_PRINT_B_41_1, "b-level fatal error 41_1"}, // B级致命错误41_1
    {STATUS_F_PRINT_B_45_1, "b-level fatal error 45_1"}, // B级致命错误45_1
    {STATUS_F_PRINT_B_3B_8, "b-level fatal error 3b_8"}, // B级致命错误3B_8
    {STATUS_F_PRINT_B_2_6, "b-level fatal error 2_6"}, // B级致命错误2_6
    {STATUS_F_PRINT_B_2_4, "b-level fatal error 2_4"}, // B级致命错误2_4
    {STATUS_F_PRINT_B_2_2, "b-level fatal error 2_2"}, // B级致命错误2_2
    {STATUS_F_PRINT_B_2_16, "b-level fatal error 2_16"}, // B级致命错误2_16
    {STATUS_F_PRINT_B_2_8, "b-level fatal error 2_8"}, // B级致命错误2_8
    {STATUS_F_PRINT_B_2_11, "b-level fatal error 2_11"}, // B级致命错误2_11
    {STATUS_F_PRINT_B_2_10, "b-level fatal error 2_10"}, // B级致命错误2_10
    {STATUS_F_PRINT_B_2_14, "b-level fatal error 2_14"}, // B级致命错误2_14
    {STATUS_F_PRINT_B_11_E1, "b-level fatal error 11_e1"}, // B级致命错误11_E1
    {STATUS_F_PRINT_B_11_E2, "b-level fatal error 11_e2"}, // B级致命错误11_E2
    {STATUS_F_PRINT_B_11_A1, "b-level fatal error 11_a1"}, // B级致命错误11_A1
    {STATUS_F_PRINT_B_11_A2, "b-level fatal error 11_a2"}, // B级致命错误11_A2
    {STATUS_F_PRINT_B_11_C5, "b-level fatal error 11_c5"}, // B级致命错误11_C5
    {STATUS_F_PRINT_B_13_1, "b-level fatal error 13_1"}, // B级致命错误13_1
    {STATUS_F_PRINT_B_25_51, "b-level fatal error 25_51"}, // B级致命错误25_51
    {STATUS_F_PRINT_B_25_52, "b-level fatal error 25_52"}, // B级致命错误25_52
    {STATUS_F_PRINT_B_25_53, "b-level fatal error 25_53"}, // B级致命错误25_53
    {STATUS_F_PRINT_B_25_54, "b-level fatal error 25_54"}, // B级致命错误25_54
    {STATUS_F_PRINT_B_25_55, "b-level fatal error 25_55"}, // B级致命错误25_55
    {STATUS_F_PRINT_B_25_56, "b-level fatal error 25_56"}, // B级致命错误25_56
    {STATUS_F_PRINT_B_25_57, "b-level fatal error 25_57"}, // B级致命错误25_57
    {STATUS_F_PRINT_B_25_58, "b-level fatal error 25_58"}, // B级致命错误25_58
    {STATUS_F_PRINT_B_25_59, "b-level fatal error 25_59"}, // B级致命错误25_59
    {STATUS_F_PRINT_B_25_5A, "b-level fatal error 25_5a"}, // B级致命错误25_5A
    {STATUS_F_PRINT_B_25_5B, "b-level fatal error 25_5b"}, // B级致命错误25_5B
    {STATUS_F_PRINT_B_25_5C, "b-level fatal error 25_5c"}, // B级致命错误25_5C
    {STATUS_F_PRINT_B_25_61, "b-level fatal error 25_61"}, // B级致命错误25_61
    {STATUS_F_PRINT_B_25_62, "b-level fatal error 25_62"}, // B级致命错误25_62
    {STATUS_F_PRINT_B_25_63, "b-level fatal error 25_63"}, // B级致命错误25_63
    {STATUS_F_PRINT_B_25_64, "b-level fatal error 25_64"}, // B级致命错误25_64
    {STATUS_F_PRINT_B_11_2, "b-level fatal error 11_2"}, // B级致命错误11_2
    {STATUS_F_PRINT_B_11_3, "b-level fatal error 11_3"}, // B级致命错误11_3
    {STATUS_F_PRINT_B_11_4, "b-level fatal error 11_4"}, // B级致命错误11_4
    {STATUS_F_PRINT_B_11_5, "b-level fatal error 11_5"}, // B级致命错误11_5
    {STATUS_F_PRINT_B_11_6, "b-level fatal error 11_6"}, // B级致命错误11_6
    {STATUS_F_PRINT_B_11_9, "b-level fatal error 11_9"}, // B级致命错误11_9
    {STATUS_F_PRINT_B_11_12, "b-level fatal error 11_12"}, // B级致命错误11_12
    {STATUS_F_PRINT_B_11_13, "b-level fatal error 11_13"}, // B级致命错误11_13
    {STATUS_F_PRINT_B_11_14, "b-level fatal error 11_14"}, // B级致命错误11_14
    {STATUS_F_PRINT_B_11_15, "b-level fatal error 11_15"}, // B级致命错误11_15
    {STATUS_F_PRINT_B_11_24, "b-level fatal error 11_24"}, // B级致命错误11_24
    {STATUS_F_PRINT_B_11_25, "b-level fatal error 11_25"}, // B级致命错误11_25
    {STATUS_F_PRINT_B_11_27, "b-level fatal error 11_27"}, // B级致命错误11_27
    {STATUS_F_PRINT_B_11_30, "b-level fatal error 11_30"}, // B级致命错误11_30
    {STATUS_F_PRINT_B_11_31, "b-level fatal error 11_31"}, // B级致命错误11_31
    {STATUS_F_PRINT_B_11_32, "b-level fatal error 11_32"}, // B级致命错误11_32
    {STATUS_F_PRINT_B_11_40, "b-level fatal error 11_40"}, // B级致命错误11_40
    {STATUS_F_PRINT_B_11_41, "b-level fatal error 11_41"}, // B级致命错误11_41
    {STATUS_F_PRINT_B_11_42, "b-level fatal error 11_42"}, // B级致命错误11_42
    {STATUS_F_PRINT_B_11_43, "b-level fatal error 11_43"}, // B级致命错误11_43
    {STATUS_F_PRINT_B_11_44, "b-level fatal error 11_44"}, // B级致命错误11_44
    {STATUS_F_PRINT_B_11_45, "b-level fatal error 11_45"}, // B级致命错误11_45
    {STATUS_F_PRINT_B_11_46, "b-level fatal error 11_46"}, // B级致命错误11_46
    {STATUS_F_PRINT_B_11_52, "b-level fatal error 11_52"}, // B级致命错误11_52
    {STATUS_F_PRINT_B_11_56, "b-level fatal error 11_56"}, // B级致命错误11_56
    {STATUS_F_PRINT_B_11_84, "b-level fatal error 11_84"}, // B级致命错误11_84
    {STATUS_F_PRINT_B_11_95, "b-level fatal error 11_95"}, // B级致命错误11_95
    {STATUS_F_PRINT_B_11_96, "b-level fatal error 11_96"}, // B级致命错误11_96
    {STATUS_F_PRINT_B_11_97, "b-level fatal error 11_97"}, // B级致命错误11_97
    {STATUS_F_PRINT_B_24_11, "b-level fatal error 24_11"}, // B级致命错误24_11
    {STATUS_F_PRINT_B_24_12, "b-level fatal error 24_12"}, // B级致命错误24_12
    {STATUS_F_PRINT_B_24_13, "b-level fatal error 24_13"}, // B级致命错误24_13
    {STATUS_F_PRINT_B_24_14, "b-level fatal error 24_14"}, // B级致命错误24_14
    {STATUS_F_PRINT_B_2A_11, "b-level fatal error 2a_11"}, // B级致命错误2A_11
    {STATUS_F_PRINT_B_2A_12, "b-level fatal error 2a_12"}, // B级致命错误2A_12
    {STATUS_F_PRINT_B_2A_13, "b-level fatal error 2a_13"}, // B级致命错误2A_13
    {STATUS_F_PRINT_B_2A_14, "b-level fatal error 2a_14"}, // B级致命错误2A_14
    {STATUS_F_PRINT_B_51_2, "b-level fatal error 51_2"}, // B级致命错误51_2

    // C级致命错误
    {STATUS_F_PRINT_C_55_1, "c-level fatal error 55_1"}, // C级致命错误55_1
    {STATUS_F_PRINT_C_39_2A, "c-level fatal error 39_2a"}, // C级致命错误39_2A
    {STATUS_F_PRINT_C_C1_63, "c-level fatal error c1_63"}, // C级致命错误C1_63
    {STATUS_F_PRINT_C_40_A1, "c-level fatal error 40_a1"}, // C级致命错误40_A1
    {STATUS_F_PRINT_C_00_02, "c-level fatal error 00_02"}, // C级致命错误00_02
    {STATUS_F_PRINT_C_56_03, "c-level fatal error 56_03"}, // C级致命错误56_03
    {STATUS_F_PRINT_C_56_20, "c-level fatal error 56_20"}, // C级致命错误56_20
    {STATUS_F_PRINT_C_40_91, "c-level fatal error 40_91"}, // C级致命错误40_91
    {STATUS_F_PRINT_C_40_A2, "c-level fatal error 40_a2"}, // C级致命错误40_A2
    {STATUS_F_PRINT_C_40_A3, "c-level fatal error 40_a3"}, // C级致命错误40_A3
    {STATUS_F_PRINT_C_40_A4, "c-level fatal error 40_a4"}, // C级致命错误40_A4
    {STATUS_F_PRINT_C_40_A5, "c-level fatal error 40_a5"}, // C级致命错误40_A5
    {STATUS_F_PRINT_C_40_A6, "c-level fatal error 40_a6"}, // C级致命错误40_A6
    {STATUS_F_PRINT_C_10_3, "c-level fatal error 10_3"}, // C级致命错误10_3
    {STATUS_F_PRINT_C_10_4, "c-level fatal error 10_4"}, // C级致命错误10_4
    {STATUS_F_PRINT_C_10_14, "c-level fatal error 10_14"}, // C级致命错误10_14
    {STATUS_F_PRINT_C_10_81, "c-level fatal error 10_81"}, // C级致命错误10_81
    {STATUS_F_PRINT_C_10_82, "c-level fatal error 10_82"}, // C级致命错误10_82
    {STATUS_F_PRINT_C_2A_21, "c-level fatal error 2a_21"}, // C级致命错误2A_21
    {STATUS_F_PRINT_C_2A_22, "c-level fatal error 2a_22"}, // C级致命错误2A_22
    {STATUS_F_PRINT_C_2A_23, "c-level fatal error 2a_23"}, // C级致命错误2A_23
    {STATUS_F_PRINT_C_2A_24, "c-level fatal error 2a_24"}, // C级致命错误2A_24
    {STATUS_F_PRINT_C_26_50, "c-level fatal error 26_50"}, // C级致命错误26_50
    {STATUS_F_PRINT_C_26_53, "c-level fatal error 26_53"}, // C级致命错误26_53
    {STATUS_F_PRINT_C_26_52, "c-level fatal error 26_52"}, // C级致命错误26_52
    {STATUS_F_PRINT_C_26_51, "c-level fatal error 26_51"}, // C级致命错误26_51
    {STATUS_F_PRINT_C_26_54, "c-level fatal error 26_54"}, // C级致命错误26_54
    {STATUS_F_PRINT_C_2A_03, "c-level fatal error 2a_03"}, // C级致命错误2A_03
    {STATUS_F_PRINT_C_2A_02, "c-level fatal error 2a_02"}, // C级致命错误2A_02
    {STATUS_F_PRINT_C_2A_01, "c-level fatal error 2a_01"}, // C级致命错误2A_01
    {STATUS_F_PRINT_C_2A_04, "c-level fatal error 2a_04"}, // C级致命错误2A_04
    {STATUS_F_PRINT_C_D7_01, "c-level fatal error d7_01"}, // C级致命错误D7_01
    {STATUS_F_PRINT_C_D7_02, "c-level fatal error d7_02"}, // C级致命错误D7_02
    {STATUS_F_PRINT_C_D7_03, "c-level fatal error d7_03"}, // C级致命错误D7_03
    {STATUS_F_PRINT_C_D7_04, "c-level fatal error d7_04"}, // C级致命错误D7_04
    {STATUS_F_PRINT_C_D7_05, "c-level fatal error d7_05"}, // C级致命错误D7_05
    {STATUS_F_PRINT_C_D7_06, "c-level fatal error d7_06"}, // C级致命错误D7_06
    {STATUS_F_PRINT_C_56_1, "c-level fatal error 56_1"}, // C级致命错误56_1
    {STATUS_F_PRINT_C_56_6, "c-level fatal error 56_6"}, // C级致命错误56_6
    {STATUS_F_PRINT_C_56_10, "c-level fatal error 56_10"}, // C级致命错误56_10
    {STATUS_F_PRINT_C_14_2, "c-level fatal error 14_2"}, // C级致命错误14_2
    {STATUS_F_PRINT_C_14_3, "c-level fatal error 14_3"}, // C级致命错误14_3
    {STATUS_F_PRINT_C_C1_55, "c-level fatal error c1_55"}, // C级致命错误C1_55
    {STATUS_F_PRINT_C_C1_5B, "c-level fatal error c1_5b"}, // C级致命错误C1_5B
    {STATUS_F_PRINT_C_71_06, "c-level fatal error 71_06"}, // C级致命错误71_06
    {STATUS_F_PRINT_C_71_07, "c-level fatal error 71_07"}, // C级致命错误71_07
    {STATUS_F_PRINT_C_71_11, "c-level fatal error 71_11"}, // C级致命错误71_11
    {STATUS_F_PRINT_C_71_12, "c-level fatal error 71_12"}, // C级致命错误71_12
    {STATUS_F_PRINT_C_71_31, "c-level fatal error 71_31"}, // C级致命错误71_31
    {STATUS_F_PRINT_C_71_33, "c-level fatal error 71_33"}, // C级致命错误71_33
    {STATUS_F_PRINT_C_71_35, "c-level fatal error 71_35"}, // C级致命错误71_35
    {STATUS_F_PRINT_C_71_37, "c-level fatal error 71_37"}, // C级致命错误71_37
    {STATUS_F_PRINT_C_71_32, "c-level fatal error 71_32"}, // C级致命错误71_32
    {STATUS_F_PRINT_C_71_34, "c-level fatal error 71_34"}, // C级致命错误71_34
    {STATUS_F_PRINT_C_71_39, "c-level fatal error 71_39"}, // C级致命错误71_39
    {STATUS_F_PRINT_C_71_41, "c-level fatal error 71_41"}, // C级致命错误71_41
    {STATUS_F_PRINT_C_71_51, "c-level fatal error 71_51"}, // C级致命错误71_51
    {STATUS_F_PRINT_C_71_52, "c-level fatal error 71_52"}, // C级致命错误71_52
    {STATUS_F_PRINT_C_71_53, "c-level fatal error 71_53"}, // C级致命错误71_53
    {STATUS_F_PRINT_C_71_60, "c-level fatal error 71_60"}, // C级致命错误71_60
    {STATUS_F_PRINT_C_71_62, "c-level fatal error 71_62"}, // C级致命错误71_62
    {STATUS_F_PRINT_C_71_63, "c-level fatal error 71_63"}, // C级致命错误71_63
    {STATUS_F_PRINT_C_71_64, "c-level fatal error 71_64"}, // C级致命错误71_64
    {STATUS_F_PRINT_C_72_02, "c-level fatal error 72_02"}, // C级致命错误72_02
    {STATUS_F_PRINT_C_72_06, "c-level fatal error 72_06"}, // C级致命错误72_06
    {STATUS_F_PRINT_C_72_0A, "c-level fatal error 72_0a"}, // C级致命错误72_0A
    {STATUS_F_PRINT_C_72_0B, "c-level fatal error 72_0b"}, // C级致命错误72_0B
    {STATUS_F_PRINT_C_72_41, "c-level fatal error 72_41"}, // C级致命错误72_41
    {STATUS_F_PRINT_C_72_42, "c-level fatal error 72_42"}, // C级致命错误72_42
    {STATUS_F_PRINT_C_72_43, "c-level fatal error 72_43"}, // C级致命错误72_43
    {STATUS_F_PRINT_C_72_51, "c-level fatal error 72_51"}, // C级致命错误72_51
    {STATUS_F_PRINT_C_73_01, "c-level fatal error 73_01"}, // C级致命错误73_01
    {STATUS_F_PRINT_C_73_02, "c-level fatal error 73_02"}, // C级致命错误73_02
    {STATUS_F_PRINT_C_73_04, "c-level fatal error 73_04"}, // C级致命错误73_04
    {STATUS_F_PRINT_C_73_05, "c-level fatal error 73_05"}, // C级致命错误73_05
    {STATUS_F_PRINT_C_73_10, "c-level fatal error 73_10"}, // C级致命错误73_10
    {STATUS_F_PRINT_C_73_11, "c-level fatal error 73_11"}, // C级致命错误73_11
    {STATUS_F_PRINT_C_73_12, "c-level fatal error 73_12"}, // C级致命错误73_12
    {STATUS_F_PRINT_C_73_13, "c-level fatal error 73_13"}, // C级致命错误73_13
    {STATUS_F_PRINT_C_73_14, "c-level fatal error 73_14"}, // C级致命错误73_14
    {STATUS_F_PRINT_C_73_15, "c-level fatal error 73_15"}, // C级致命错误73_15
    {STATUS_F_PRINT_C_73_17, "c-level fatal error 73_17"}, // C级致命错误73_17
    {STATUS_F_PRINT_C_74_01, "c-level fatal error 74_01"}, // C级致命错误74_01
    {STATUS_F_PRINT_C_74_02, "c-level fatal error 74_02"}, // C级致命错误74_02
    {STATUS_F_PRINT_C_75_01, "c-level fatal error 75_01"}, // C级致命错误75_01
    {STATUS_F_PRINT_C_75_02, "c-level fatal error 75_02"}, // C级致命错误75_02
    {STATUS_F_PRINT_C_76_01, "c-level fatal error 76_01"}, // C级致命错误76_01
    {STATUS_F_PRINT_C_76_02, "c-level fatal error 76_02"}, // C级致命错误76_02
    {STATUS_F_PRINT_C_76_03, "c-level fatal error 76_03"}, // C级致命错误76_03
    {STATUS_F_PRINT_C_76_04, "c-level fatal error 76_04"}, // C级致命错误76_04
    {STATUS_F_PRINT_C_76_05, "c-level fatal error 76_05"}, // C级致命错误76_05
    {STATUS_F_PRINT_C_76_06, "c-level fatal error 76_06"}, // C级致命错误76_06
    {STATUS_F_PRINT_C_76_07, "c-level fatal error 76_07"}, // C级致命错误76_07
    {STATUS_F_PRINT_C_76_0A, "c-level fatal error 76_0a"}, // C级致命错误76_0A
    {STATUS_F_PRINT_C_76_0B, "c-level fatal error 76_0b"}, // C级致命错误76_0B
    {STATUS_F_PRINT_C_76_0C, "c-level fatal error 76_0c"}, // C级致命错误76_0C
    {STATUS_F_PRINT_C_76_0D, "c-level fatal error 76_0d"}, // C级致命错误76_0D
    {STATUS_F_PRINT_C_76_0E, "c-level fatal error 76_0e"}, // C级致命错误76_0E
    {STATUS_F_PRINT_C_76_0F, "c-level fatal error 76_0f"}, // C级致命错误76_0F
    {STATUS_F_PRINT_C_76_10, "c-level fatal error 76_10"}, // C级致命错误76_10
    {STATUS_F_PRINT_C_76_22, "c-level fatal error 76_22"}, // C级致命错误76_22
    {STATUS_F_PRINT_C_76_23, "c-level fatal error 76_23"}, // C级致命错误76_23
    {STATUS_F_PRINT_C_76_24, "c-level fatal error 76_24"}, // C级致命错误76_24
    {STATUS_F_PRINT_C_76_31, "c-level fatal error 76_31"}, // C级致命错误76_31
    {STATUS_F_PRINT_C_76_33, "c-level fatal error 76_33"}, // C级致命错误76_33
    {STATUS_F_PRINT_C_56_5, "c-level fatal error 56_5"}, // C级致命错误56_5
    {STATUS_F_PRINT_C_7F_01, "c-level fatal error 7f_01"}, // C级致命错误7F_01
    {STATUS_F_PRINT_C_7F_04, "c-level fatal error 7f_04"}, // C级致命错误7F_04
    {STATUS_F_PRINT_C_7F_05, "c-level fatal error 7f_05"}, // C级致命错误7F_05
    {STATUS_F_PRINT_C_7F_06, "c-level fatal error 7f_06"}, // C级致命错误7F_06
    {STATUS_F_PRINT_C_7F_07, "c-level fatal error 7f_07"}, // C级致命错误7F_07
    {STATUS_F_PRINT_C_7F_08, "c-level fatal error 7f_08"}, // C级致命错误7F_08
    {STATUS_F_PRINT_C_7F_09, "c-level fatal error 7f_09"}, // C级致命错误7F_09
    {STATUS_F_PRINT_C_7F_10, "c-level fatal error 7f_10"}, // C级致命错误7F_10
    {STATUS_F_PRINT_C_7F_11, "c-level fatal error 7f_11"}, // C级致命错误7F_11
    {STATUS_F_PRINT_C_7F_12, "c-level fatal error 7f_12"}, // C级致命错误7F_12
    {STATUS_F_PRINT_C_7F_13, "c-level fatal error 7f_13"}, // C级致命错误7F_13
    {STATUS_F_PRINT_C_7F_15, "c-level fatal error 7f_15"}, // C级致命错误7F_15
    {STATUS_F_PRINT_C_7F_16, "c-level fatal error 7f_16"}, // C级致命错误7F_16
    {STATUS_F_PRINT_C_7F_17, "c-level fatal error 7f_17"}, // C级致命错误7F_17
    {STATUS_F_PRINT_C_7F_18, "c-level fatal error 7f_18"}, // C级致命错误7F_18
    {STATUS_F_PRINT_C_7F_19, "c-level fatal error 7f_19"}, // C级致命错误7F_19
    {STATUS_F_PRINT_C_7F_20, "c-level fatal error 7f_20"}, // C级致命错误7F_20
    {STATUS_F_PRINT_C_7F_21, "c-level fatal error 7f_21"}, // C级致命错误7F_21
    {STATUS_F_PRINT_C_7F_22, "c-level fatal error 7f_22"}, // C级致命错误7F_22
    {STATUS_F_PRINT_C_7F_23, "c-level fatal error 7f_23"}, // C级致命错误7F_23
    {STATUS_F_PRINT_C_7F_24, "c-level fatal error 7f_24"}, // C级致命错误7F_24
    {STATUS_F_PRINT_C_7F_50, "c-level fatal error 7f_50"}, // C级致命错误7F_50


    // SCAN模块状态
    {STATUS_I_SCAN_IDLE, "scan ready"}, // 扫描模块就绪
    {STATUS_I_SCAN_SLEEP, "scan sleeping"}, // 扫描模块睡眠
    {STATUS_I_SCAN_PROCESSING, "scan processing"}, // 扫描处理中
    {STATUS_I_SCAN_RUNNING, "scanning"}, // 扫描中
    {STATUS_I_SCAN_CANCELING, "scan canceling"}, // 扫描取消中
    {STATUS_I_SCAN_TOFILE_SENDING, "scan file sending"}, // 扫描文件正在发送中....
    {STATUS_I_SCAN_NEXT_PAGE_WAITING, "waiting for next page"}, // 等待扫描下一页状态
    {STATUS_I_SCAN_FINISHED, "scan finished"}, // 扫描完成
    {STATUS_I_SCAN_TO_FILE_UDISK_SAVING, "saving to usb disk"}, // 扫描文件正在写入U盘
    {STATUS_I_SCAN_TO_FILE_SENT, "scan file sent"}, // 扫描文件发送完成
    {STATUS_I_SCAN_ADF_PAPER_PRESENT, "adf paper present"}, // ADF纸张存在
    {STATUS_I_SCAN_ADF_PAPER_REMOVED, "adf paper removed"}, // ADF纸张移除
    {STATUS_I_SCAN_PUT_PAPER_TO_ADF, "put paper to adf"}, // 请放纸到ADF
    {STATUS_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF, "adf manual duplex confirm"}, // 等待ADF手动双面翻面确认
    {STATUS_I_SCAN_PAPER_SIZE_UNSUPPORT_MIXED_SCAN, "paper size unsupported for mixed scan"}, // 纸张类型不支持混合扫描
    {STATUS_I_SCAN_NO_RESOURCE, "scan no resource available"}, // 扫描作业无可用资源
    {STATUS_I_SCAN_PAPER_SIZE_OVER_AREA, "scan paper size over area"}, // 下发扫描纸张尺寸超出区域
    {STATUS_I_SCAN_ADF_BOARD_WAIT_INIT, "adf board waiting init"}, // ADF模块板等待被初始化
    {STATUS_I_SCAN_ADF_BOARD_INITING, "adf board initializing"}, // ADF模块板正在初始化
    {STATUS_I_SCAN_ADF_BOARD_IDLE, "adf board idle"}, // ADF模块板处于空闲状态
    {STATUS_I_SCAN_ADF_BOARD_WORKING, "adf board working"}, // ADF模块板正在工作中
    {STATUS_I_SCAN_ADF_BOARD_ERR, "adf board error"}, // ADF模块板进入错误状态
    {STATUS_I_SCAN_ADF_BOARD_WARNING, "adf board warning"}, // ADF模块板进入警告状态
    {STATUS_I_SCAN_OUT_TO_EML_SUCCESS, "scan to email success"}, // 扫描到email成功
    {STATUS_I_SCAN_OUT_TO_FTP_SUCCESS, "scan to ftp success"}, // 扫描到FTP成功
    {STATUS_I_SCAN_OUT_TO_UDISK_SUCCESS, "scan to usb disk success"}, // 扫描到U盘成功
    {STATUS_I_SCAN_OUT_TO_WSD_SUCCESS, "scan to wsd success"}, // 扫描到WSD成功
    {STATUS_I_SCAN_OUT_TO_AIRSCAN_SUCCESS, "scan to airscan success"}, // 扫描到AIRSCAN成功
    {STATUS_I_SCAN_OUT_TO_EML_CANCEL, "scan to email canceled"}, // 扫描到email取消
    {STATUS_I_SCAN_OUT_TO_FTP_CANCEL, "scan to ftp canceled"}, // 扫描到FTP取消
    {STATUS_I_SCAN_OUT_TO_UDISK_CANCEL, "scan to usb disk canceled"}, // 扫描到U盘取消
    {STATUS_I_SCAN_OUT_TO_WSD_CANCEL, "scan to wsd canceled"}, // 扫描到WSD取消
    {STATUS_I_SCAN_OUT_TO_AIRSCAN_CANCEL, "scan to airscan canceled"}, // 扫描到AIRSCAN取消
    {STATUS_I_SCAN_LOCKED, "scanner locked"}, // 扫描仪已加锁
    {STATUS_I_SCAN_BOOK_COVER_WAITING, "book cover waiting"}, // 书稿扫描功能扫描下一页提示用户放置封面
    {STATUS_I_SCAN_BOOK_BACK_COVER_WAITING, "book back cover waiting"}, // 书稿扫描功能扫描下一页提示用户放置封底
    {STATUS_I_SCAN_BOOK_MAIN_TEXT_WAITING, "book main text waiting"}, // 书稿扫描功能扫描下一页提示用户放置正文
    {STATUS_W_SCAN_REMOVE_ADF_PAPER, "remove adf paper"}, // 需要清除ADF纸盒纸张
    {STATUS_E_SCAN_ADF_PAPER_OUT, "adf paper out"}, // ADF缺纸
    {STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT, "adf front paper mispick"}, // ADF正面进纸失败
    {STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK, "adf back paper mispick"}, // ADF背面进纸失败
    {STATUS_E_SCAN_ADF_COVER_OPEN, "adf cover open"}, // ADF开盖
    {STATUS_E_SCAN_ADF_PAPER_MISMATCH, "adf paper mismatch"}, // ADF纸型不匹配
    {STATUS_E_SCAN_FB_COVER_OPEN, "flatbed cover open"}, // FB开盖
    {STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ, "paper head not reach adj sensor"}, // 纸头超时未到达ADJ传感器
    {STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN, "paper head not reach scan sensor"}, // 纸头超时未到达SCAN传感器
    {STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT, "paper head not reach exit sensor"}, // 纸头超时未到达EXIT传感器
    {STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ, "paper tail not reach adj sensor"}, // 纸尾超时未到达ADJ传感器
    {STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN, "paper tail not reach scan sensor"}, // 纸尾超时未到达SCAN传感器
    {STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT, "paper tail not reach exit sensor"}, // 纸尾超时未到达EXIT传感器
    {STATUS_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT, "paper leave adj sensor timeout"}, // 纸张离开ADJ传感器超时
    {STATUS_E_SCAN_PAPER_JAM_REMAIN_ADJ, "paper remain at adj sensor"}, // 纸张遗留在ADJ传感器
    {STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN, "paper remain at scan sensor"}, // 纸张遗留在SCAN传感器
    {STATUS_E_SCAN_PAPER_JAM_REMAIN_EXIT, "paper remain at exit sensor"}, // 纸张遗留在EXIT传感器
    {STATUS_E_SCAN_MEMORY_LOW, "scan memory low"}, // 扫描模块内存低
    {STATUS_E_SCAN_COMMUNICATION_ERR_21, "scan communication error 21"}, // 通讯失败:21扫描数据传输超时
    {STATUS_E_SCAN_COMMUNICATION_ERR_22, "scan communication error 22"}, // 通讯失败:22无效的指令
    {STATUS_E_SCAN_COMMUNICATION_ERR_23, "scan communication error 23"}, // 通讯失败:23无效的参数
    {STATUS_E_SCAN_COMMUNICATION_ERR_24, "scan communication error 24"}, // 通讯失败:24(保留)
    {STATUS_E_SCAN_COMMUNICATION_ERR_25, "scan communication error 25"}, // 通讯失败:25USB连接出错
    {STATUS_E_SCAN_COMMUNICATION_ERR_26, "scan communication error 26"}, // 通讯失败:26网络连接出错
    {STATUS_E_SCAN_COMMUNICATION_ERR_27, "scan communication error 27"}, // 通讯失败:27wifi连接出错
    {STATUS_E_SCAN_TO_FILE_ABORTED, "scan to file aborted"}, // 扫描到文件出错
    {STATUS_E_SCAN_TO_FILE_PASSWORD_WRONG, "scan file password wrong"}, // 用户名或密码错误
    {STATUS_E_SCAN_TO_FILE_FILE_OVERSIZE, "scan file oversize"}, // 文件大小超出设定范围，过大
    {STATUS_E_SCAN_TO_FILE_SERVER_OVERSIZE, "scan file server oversize"}, // 文件大小超过服务器限制
    {STATUS_E_SCAN_TO_FILE_SEND_FAILED, "scan file send failed"}, // 文件发送失败
    {STATUS_E_SCAN_TO_FILE_VOLUME_LOW, "scan file volume low"}, // 扫描到文件容量不足
    {STATUS_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE, "usb disk space oversize"}, // U盘容量不足
    {STATUS_E_SCAN_TO_FILE_UDISK_ABORTED, "scan to usb disk aborted"}, // 扫描到U盘写文件失败
    {STATUS_E_SCAN_TO_FILE_PC_NO_RESPONSE, "pc no response"}, // PC无响应
    {STATUS_E_SCAN_EML_ESYS, "scan email system error"}, // 扫描到email系统故障
    {STATUS_E_SCAN_EML_EUSER, "scan email user error"}, // 扫描到email用户名错误
    {STATUS_E_SCAN_EML_ECONN, "scan email connection failed"}, // 扫描到email连接失败
    {STATUS_E_SCAN_EML_ETRAN, "scan email transmission failed"}, // 扫描到email传输失败
    {STATUS_E_SCAN_EML_EPASS, "scan email password error"}, // 扫描到email密码错误
    {STATUS_E_SCAN_EML_EFROM, "scan email sender error"}, // 扫描到email发件人错误
    {STATUS_E_SCAN_EML_ETO, "scan email recipient error"}, // 扫描到email收件人错误
    {STATUS_E_SCAN_EML_EATT_ACCESS, "scan email attachment access error"}, // 扫描到email附件不可访问（文件不存在等）
    {STATUS_E_SCAN_EML_EATT_TOO_BIG, "scan email attachment too big"}, // 扫描到email附件过大
    {STATUS_E_SCAN_EML_ELIMIT, "scan email size limit exceeded"}, // 扫描到email邮件大小超过了服务器限制
    {STATUS_E_SCAN_EML_ESERVER, "scan email server error"}, // 扫描到email服务器响应了其它错误信息
    {STATUS_E_SCAN_EML_MEM_LOW, "scan email memory low"}, // 扫描到email内存不足
    {STATUS_E_SCAN_SMB_SERVER_OVERSIZE, "scan smb server oversize"}, // 扫描到SMB服务器磁盘满
    {STATUS_E_SCAN_SMB_SENDFAIL, "scan smb send failed"}, // 扫描到SMB文件上传失败
    {STATUS_E_SCAN_SMB_DIR, "scan smb directory error"}, // 扫描到SMB目录错误
    {STATUS_E_SCAN_SMB_HOSTNAME, "scan smb hostname error"}, // 扫描到SMB主机名错误
    {STATUS_E_SCAN_SMB_USER_PASS, "scan smb user password error"}, // 扫描到SMB用户名或密码错误
    {STATUS_E_SCAN_SMB_SERVER_DISCONN, "scan smb server disconnected"}, // 扫描到SMB服务器网络断开
    {STATUS_E_SCAN_SMB_NET_DISCONN, "scan smb network disconnected"}, // 扫描到SMB打印机网络断开
    {STATUS_E_SCAN_FTP_ESYS, "scan ftp system error"}, // 扫描到FTP系统故障
    {STATUS_E_SCAN_FTP_ECONN, "scan ftp connection failed"}, // 扫描到FTP连接失败
    {STATUS_E_SCAN_FTP_ETRAN, "scan ftp transmission failed"}, // 扫描到FTP传输失败
    {STATUS_E_SCAN_FTP_EUSER, "scan ftp user error"}, // 扫描到FTP用户名错误
    {STATUS_E_SCAN_FTP_EPASS, "scan ftp password error"}, // 扫描到FTP密码错误
    {STATUS_E_SCAN_FTP_EFILE_ACCESS, "scan ftp file access failed"}, // 扫描到FTP文件访问失败
    {STATUS_E_SCAN_FTP_ESERVPATH, "scan ftp server path error"}, // 扫描到FTP服务器路径错误
    {STATUS_E_SCAN_FTP_ESERVER, "scan ftp server error"}, // 扫描到FTP服务器响应了其它错误信息
    {STATUS_E_SCAN_WSD_QIOERR, "scan wsd io error"}, // 扫描到WSD读取IO错误
    {STATUS_E_SCAN_WSD_COMM, "scan wsd connection failed"}, // 扫描到WSD连接失败
    {STATUS_E_SCAN_WSD_LOWMEM, "scan wsd memory low"}, // 扫描到WSD内存不足
    {STATUS_E_SCAN_WSD_FILE, "scan wsd file failed"}, // 扫描到WSD文件失败
    {STATUS_E_SCAN_AIRSCAN_ESYS, "scan airscan system error"}, // 扫描到Airscan系统故障
    {STATUS_E_SCAN_AIRSCAN_QIOERR, "scan airscan io error"}, // 扫描到Airscan读取IO错误
    {STATUS_E_SCAN_AIRSCAN_ECONN, "scan airscan connection failed"}, // 扫描到Airscan连接失败
    {STATUS_E_SCAN_AIRSCAN_ETRAN, "scan airscan transmission failed"}, // 扫描到Airscan传输失败
    {STATUS_E_SCAN_USB_NO_RESPONSE, "usb communication no response"}, // USB通信无响应
    {STATUS_E_SCAN_FTP_EADRR, "ftp address unreachable"}, // FTP地址不可达
    {STATUS_E_SCAN_SMB_IPADDR, "scan smb ip address error"}, // 扫描到SMB网络协议地址错误
    {STATUS_E_SCAN_SMB_EPORT, "scan smb port error"}, // 扫描到SMB端口错误
    {STATUS_E_SCAN_FTP_EPORT, "scan ftp port error"}, // 扫描到FTP端口错误
    {STATUS_E_SCAN_ADF_BOARD_COM_HW, "adf board communication hardware error"}, // ADF通讯检测到硬件错误
    {STATUS_E_SCAN_ADF_BOARD_COM_CRC_SERIAL, "adf board crc serial error"}, // ADF通讯连续多帧数据CRC检验出错
    {STATUS_E_SCAN_ADF_BOARD_COM_CRC_TOTAL, "adf board crc total error"}, // ADF通讯累计多帧数据CRC校验出错
    {STATUS_E_SCAN_ADF_BOARD_COM_INVALID_SERIAL, "adf board invalid serial data"}, // ADF通讯连续接收到多个字节无效数据
    {STATUS_E_SCAN_ADF_BOARD_COM_INVALID_TOTAL, "adf board invalid total data"}, // ADF通讯累计接收到多个字节无效数据
    {STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT, "scanner response timeout"}, // 扫描仪响应超时
    {STATUS_E_SCAN_ADF_BOTTOM_COVER_OPEN, "adf bottom cover open"}, // ADF下盖打开
    {STATUS_E_SCAN_DF1_COVER_OPEN, "df1 cover open"}, // DF1开盖错误
    {STATUS_F_SCAN_ADF_RELEASE_BAR_ABNORMAL, "adf release bar abnormal"}, // ADF释纸杆控制异常
    {STATUS_F_SCAN_ADF_MOTOR_ABNORMAL, "adf motor abnormal"}, // ADF马达异常
    {STATUS_F_SCAN_ADF_BOARD_HW_ERROR, "adf board hardware error"}, // 扫描ADF模块板硬件错误（初始化失败）
    {STATUS_F_SCAN_ADF_BOARD_CONNECT_FAILURE, "adf board connection failure"}, // 扫描ADF模块板连接失败
    {STATUS_F_SCAN_FB_MOTOR_ABNORMAL, "flatbed motor abnormal"}, // FB马达异常
    {STATUS_F_SCAN_HOME_CHECK_ERROR, "home check error"}, // 原点检查错误（FB马达往前走了，homesensor还在遮挡状态）
    {STATUS_F_SCAN_FINE_HOME_FAILURE, "find home failure"}, // 找原点失败
    {STATUS_F_SCAN_FINE_BLACK_MARK_FAILURE, "find black mark failure"}, // 找黑标失败
    {STATUS_F_SCAN_CIS_ABNORMAL, "cis abnormal"}, // CIS异常
    {STATUS_F_SCAN_CIS_DET_FAILURE, "cis detection failure"}, // CIS检测失败
    {STATUS_F_SCAN_AFE_ABNORMAL, "afe abnormal"}, // AFE异常
    {STATUS_F_SCAN_AFE_CHIP_CHECK_FAILURE, "afe chip check failure"}, // AFE芯片读写检测失败
    {STATUS_F_SCAN_AFE_SCAN_DATA_ABNORMAL, "afe scan data abnormal"}, // AFE扫描数据异常
    {STATUS_F_SCAN_CAL_AFE_OFFSET_FAILURE, "afe offset calibration failure"}, // AFE校准失败（OFFSET）
    {STATUS_F_SCAN_CAL_AFE_GAIN_FAILURE, "afe gain calibration failure"}, // AFE校准失败（GAIN）
    {STATUS_F_SCAN_CAL_AFE_EXPOSURE_FAILURE, "afe exposure calibration failure"}, // AFE校准失败（EXPOSURE）
    {STATUS_F_SCAN_CAL_SHADING_FAILURE, "shading calibration failure"}, // Shading失败
    {STATUS_F_SCAN_CAL_MEM_UNREADY, "calibration memory unready"}, // 校准内存不足
    {STATUS_F_SCAN_FE0640_10, "upper cis abnormal"}, // 上CIS异常
    {STATUS_F_SCAN_FE0640_11, "upper cis detection failure"}, // 上CIS检测失败
    {STATUS_F_SCAN_FE0641_10, "upper afe abnormal"}, // 上AFE异常
    {STATUS_F_SCAN_FE0641_11, "upper afe chip check failure"}, // 上AFE芯片读写检测失败
    {STATUS_F_SCAN_FE0641_12, "upper afe scan data abnormal"}, // 上AFE扫描数据异常
    {STATUS_F_SCAN_FE0642_10, "upper afe offset calibration failure"}, // 上AFE校准失败（OFFSET）
    {STATUS_F_SCAN_FE0642_11, "upper afe gain calibration failure"}, // 上AFE校准失败（GAIN）
    {STATUS_F_SCAN_FE0642_12, "upper afe exposure calibration failure"}, // 上AFE校准失败（EXPOSURE）
    {STATUS_F_SCAN_FE0643_10, "upper shading failure"}, // 上Shading失败
    {STATUS_F_SCAN_CONNECT_ENGINE_TIMEOUT, "scan engine connection timeout"}, // 扫描引擎连接超时（后端）
    {STATUS_F_SCAN_EMMC_ABNORMAL, "emmc abnormal"}, // emmc异常（后端）
    {STATUS_F_SCAN_DMA_ABNORMAL, "dma abnormal"}, // DMA异常
    {STATUS_F_SCAN_DMA_INTERRUPT_TIMEOUT, "dma interrupt timeout"}, // DMA中断超时
    {STATUS_F_SCAN_ADF_COOLING_FAN, "adf cooling fan abnormal"}, // ADF冷却风扇异常
    {STATUS_F_SCAN_ADF_CANTACT_RETRACT_MECH, "adf contact retract mechanism abnormal"}, // ADF扫描入口前方连接装置异常
    {STATUS_F_SCAN_ADF_BRUSH_MOVEMENT, "adf brush movement abnormal"}, // ADF刷子偏移
    {STATUS_F_SCAN_ADF_INIT_TIMEOUT, "adf initialization timeout"}, // ADF初始化超时
    {STATUS_F_SCAN_FB_EXPOSURE_ON_FAIL, "flatbed exposure on failed"}, // FB校准灯光开启失败
    {STATUS_F_SCAN_FB_EXPOSURE_LAMP_IPL, "flatbed exposure lamp irregular"}, // FB曝光灯无规律亮灯
    {STATUS_F_SCAN_FB_DRIVE_SYS_HOME_ABNORMAL, "flatbed drive system home sensor abnormal"}, // FB驱动系统home传感器异常
    {STATUS_F_SCAN_FB_SLIDER_OVERRUNNING, "flatbed slider overrunning"}, // FB滑动器超限
    {STATUS_F_SCAN_FB_SCAN_SEQ_TROUBLE_1, "flatbed scan sequence trouble 1"}, // FB序列错误1
    {STATUS_F_SCAN_FB_EMMC_ABNORMAL, "flatbed emmc abnormal"}, // FB-EMMC异常
    {STATUS_F_SCAN_FB_CCD_GAIN_ADJ_ABNORMAL, "flatbed ccd gain adjustment abnormal"}, // FB-CCD灯光校准异常
    {STATUS_F_SCAN_FB_ABN_IMAGE_PROCESS_CLK, "flatbed image process clock abnormal"}, // FB图像处理同步时钟输入异常
    {STATUS_F_SCAN_FB_CCD_ABNORMAL_POWER, "flatbed ccd power abnormal"}, // FB-CCD电源电压异常
    {STATUS_F_SCAN_FB_DF_EXPOSURE_ON_FAIL, "flatbed df exposure on failed"}, // FB-DF曝光灯光开启异常
    {STATUS_F_SCAN_FB_DF_EXPOSURE_LAMP_IPL, "flatbed df exposure lamp irregular"}, // FB-DF曝光灯无规律亮灯
    {STATUS_F_SCAN_FB_DF_HOME_ABNORMAL, "flatbed df home sensor abnormal"}, // FB-DF校准仪器home传感器异常
    {STATUS_F_SCAN_FB_DF_BOARD_HOME_ABNORMAL, "flatbed df board home sensor abnormal"}, // FB-DF校准仪器板载home传感器异常
    {STATUS_F_SCAN_FB_DF_CIS_CLA_ADJ_ABNORMAL, "flatbed df cis calibration abnormal"}, // FB-DF灯光校准异常
    {STATUS_F_SCAN_FB_DF_CIS_GAIN_ADJ_ABNORMA, "flatbed df cis gain adjustment abnormal"}, // FB-DF-GAIN校准异常
    {STATUS_F_SCAN_FB_CONNECT_ENGINE_TIMEOUT, "flatbed connect engine timeout"}, // FB连接引擎超时
    {STATUS_F_SCAN_FB_FATAL_IMAGE_PROCESS_CLK, "flatbed fatal image process clock"}, // FB图像处理同步时钟输入致命异常
    {STATUS_F_SCAN_FB_INIT_TIMEOUT, "flatbed initialization timeout"}, // FB初始化超时

    // COPY模块状态
    {STATUS_I_COPY_PROCESSING, "copy processing"}, // 复印状态显示，身份证显示正面扫描中，背面扫描中，复印中，其他统一显示复印中
    {STATUS_I_COPY_CANCELING, "copy canceling"}, // 复印取消中
    {STATUS_I_COPY_MANUAL_DUPLEX_CONFIRM, "manual duplex confirm"}, // 手动双面通知
    {STATUS_I_COPY_ID_CARD_CONFIRM, "id card confirm"}, // 身份证翻面通知
    {STATUS_I_COPY_MEM_LOW, "copy memory low"}, // 复印内存不足
    {STATUS_I_COPY_SAMPLE_CONFIRM, "sample copy confirm"}, // 样本复印，复印一份后提示用户的确认信息
    {STATUS_I_COPY_SAMPLE_FINISH, "sample copy finish"}, // 样本复印，复印剩余份后提示用户是否删除数据
    {STATUS_I_COPY_NEXT_ORIGINAL_CONFIRM, "next original confirm"}, // 复印下一张原稿
    {STATUS_I_COPY_PUT_PAPER_TO_ADF, "put paper to adf"}, // ADF缺纸
    {STATUS_I_COPY_NO_RESOURCE, "copy no resource available"}, // 复印作业无可用资源
    {STATUS_I_COPY_CONTINUE_CONFIRM, "continue copy confirm"}, // 故障已解除，是否继续复印？
    {STATUS_I_COPY_MIX_ORIGINAL_MISMATCH, "mixed original size mismatch"}, // 混合原稿ADF识别尺寸与面板设定不匹配
    {STATUS_I_COPY_COMPLETE, "copy complete"}, // 复印作业完成
    {STATUS_I_COPY_COLLATE_STORAGE_FULL, "collate storage full"}, // 在逐份复印存储外存过程中,如果发生存储空间不足,则提示用户此信息.此时作业会被自动取消
    {STATUS_I_COPY_ABORTING, "copy aborting"}, // 复印作业异常
    {STATUS_I_COPY_MANUAL_DUPLEX_UNSUPPORT, "manual duplex unsupported"}, // 小册子或多页合一复印不支持原稿由自动进纸器双面扫描。
    {STATUS_I_COPY_SCAN_AREA_UNSUPPORT, "scan area unsupported"}, // 复印原稿尺寸超出扫描区域。有效范围：ADF:105mm(宽) x 148mm~216 mm(宽) x356mmFB:最大216(宽) mm x297mm""
    {STATUS_I_COPY_STATISTIC_ITEM_ERROR, "copy statistic item error"}, // 复印统计项不存在
    {STATUS_I_COPY_IDLE, "copy idle"}, // 复印空闲中
    {STATUS_W_COPY_PARAMETER_ERROR, "copy parameter error"}, // 复印配置参数有误
    {STATUS_W_COPY_PERMISSION_NOT_ALLOWED, "copy permission not allowed"}, // 复印权限不允许
    {STATUS_E_COPY_SAMPLE_TONER_EMPTY, "sample copy toner empty"}, // 作业列表，样本复印，彩色模式彩色墨粉用尽
    {STATUS_E_COPY_IPM_ERROR, "copy image processing error"}, // 图像处理错误

    // PANEL模块状态
    {STATUS_I_PANEL_UPGRADE_START, "panel upgrade start"}, // Panel升级开始
    {STATUS_F_PANEL_COMMUNICATION_ERROR, "panel communication error"}, // Panel与DC的通信异常

    // USB模块状态
    {STATUS_I_USB_UDISK_INSERT, "usb disk inserted"}, // U盘插入
    {STATUS_I_USB_UDISK_EXTRACT, "usb disk extracted"}, // U盘拔出
    {STATUS_E_USB_UDISK_MISTAKE_FORMAT, "usb disk format error"}, // U盘格式错误
    {STATUS_E_USB_UDISK_FAILURE, "usb disk failure"}, // U盘设备故障
    {STATUS_F_USB_UDISK_OVERCURRENT, "usb disk overcurrent"}, // U盘过载
    {STATUS_F_USB_HOST_OVERCURRENT, "usb host overcurrent"}, // USB设备过流

    // NET模块状态
    {STATUS_I_NET_WIFI_STA_CONNECTING, "wifi station connecting"}, // WiFistation连接中
    {STATUS_I_NET_WIFI_WPS_PBC, "wps pbc connecting"}, // WPSPBC连接中
    {STATUS_I_NET_WIFI_WPS_PIN, "wps pin connecting"}, // WPSPIN连接中
    {STATUS_I_NET_WIFI_WPS_CANCEL, "wps connection canceled"}, // WPS连接撤销
    {STATUS_I_NET_WIFI_WPS_SUCCESS, "wps connection success"}, // WPS连接成功
    {STATUS_I_NET_WIFI_CONNECT_SUCCESS, "wifi connection success"}, // WiFi连接成功
    {STATUS_I_NET_WIFI_WFD_CONNECT_REQUEST, "wifi direct connection request"}, // Wi-FiP2P手动连接请求
    {STATUS_I_NET_WIFI_WPS_FAIL, "wps connection failed"}, // WPS连接失败
    {STATUS_I_NET_WIFI_WPS_OVERLAP, "wps session overlap detected"}, // 检测到多个WPS会话,请稍后再试
    {STATUS_I_NET_WIFI_WPS_TIMEOUT, "wps connection timeout"}, // WPS连接超时
    {STATUS_I_NET_WIFI_WFD_RADAR_FREQ, "wifi direct radar frequency"}, // 已连接的无线路由器使用雷达信道(52~144),Wi-Fi直连将被关闭
    {STATUS_I_NET_SMTP_TEST_SUCCESS, "smtp test success"}, // SMTP测试邮件发送成功
    {STATUS_I_NET_AIRPRINT_IDENTIFY_ACTION, "airprint identify action"}, // 查找打印机
    {STATUS_E_NET_WIFI_CONNECT_TIMEOUT, "wifi connection timeout"}, // WiFi连接失败，超时
    {STATUS_E_NET_WIFI_CONNECT_NO_SSID, "wifi no ssid found"}, // WiFi连接失败，ssid不存在
    {STATUS_E_NET_WIFI_CONNECT_ERR_PSK, "wifi password error"}, // WiFi连接失败，密码错误
    {STATUS_E_NET_WIFI_CONNECT_FAIL, "wifi connection failed"}, // WiFi连接失败，未知错误
    {STATUS_E_NET_WIFI_CONNECT_NO_RECORD, "wifi no connection record"}, // WiFi无连接记录(重连时)
    {STATUS_E_NET_WIFI_DISCONNECT, "wifi disconnected"}, // WiFi连接断开
    {STATUS_E_NET_WIFI_INIT_ERROR, "wifi initialization error"}, // WIFI故障（初始化WIFI模块错误）
    {STATUS_E_NET_SMTP_TEST_FAIL, "smtp test failed"}, // SMTP测试邮件发送失败
    {STATUS_F_NET_WIFI_FATAL_ERROR, "wifi fatal error"}, // WiFi模块异常

    // FRAMEWORK模块状态
    {STATUS_I_FRAMEWORK_SUSPEND, "job suspended"}, // JOB挂起通知
    {STATUS_I_FRAMEWORK_RESUME, "job resumed"}, // JOB恢复通知
    {STATUS_I_FRAMEWORK_REQUEST_NEXT_PAGE, "request next page"}, // job向用户请求下一页动作开始
    {STATUS_I_FRAMEWORK_JOB_INFO_UPDATE, "job info updated"}, // job信息变化通知
    {STATUS_I_FRAMEWORK_PRINTING_IN_JOB_START, "print-in job started"}, // 插印job开始
    {STATUS_I_FRAMEWORK_PRINTING_IN_JOB_FINISH, "print-in job finished"}, // 插印job结束
    {STATUS_I_FRAMEWORK_RESUME_FAILED, "job resume failed"}, // JOB挂起后恢复失败通知

    // COMMON模块状态
    {STATUS_I_COMMON_DATA_STORAGE_FULL, "data storage full"}, // 用于样本复印、逐份复印、样本打印、小册子复印等作业因存储空间不足而无法完成当前作业时，给用户提示。
    {STATUS_E_COMMON_DATA_RECEIVE_TIMEOUT, "data receive timeout"}, // 数据接收超时
    {STATUS_E_COMMON_DATA_PASER_FAILED, "data parse failed"}, // 数据解析失败

    // FWUPDATE模块状态
    {STATUS_I_FWUPDATE_CONFIRM_UPGRADE, "confirm firmware upgrade"}, // 是否进行在线升级的选择弹窗
    {STATUS_I_FWUPDATE_RESULT, "firmware upgrade result"}, // 升级结果
    {STATUS_I_FWUPDATE_WAIT_FOR_PACKAGE, "waiting for upgrade package"}, // 等待升级包下载
    {STATUS_I_FWUPDATE_DOWNLOADING_PACKAGE, "downloading upgrade package"}, // 正在下载升级包
    {STATUS_I_FWUPDATE_PACKAGE_UPGRADING, "firmware upgrading"}, // 正在升级固件
    {STATUS_I_FWUPDATE_ENGINE_UPGRADING, "engine firmware upgrading"}, // 正在升级引擎固件
    {STATUS_I_FWUPDATE_PACKAGE_VERIFING, "firmware verifying"}, // 固件校验中
    {STATUS_I_FWUPDATE_PACKAGE_VERIFY_SUSS, "firmware verify success"}, // 固件校验成功
    {STATUS_I_FWUPDATE_BASE_VALUE, "update base value"}, // 是否更新基准值
    {STATUS_I_FWUPDATE_PACKAGE_UPDATING, "firmware updating"}, // 固件升级中弹窗请求
    {STATUS_I_FWUPDATE_REMOVE_UPGRADE, "remove upgrade status"}, // 移除升级状态
    {STATUS_I_FWUPDATE_PACKAGE_VERIFY_FAILED, "firmware verify failed"}, // 验签失败
    {STATUS_E_FWUPDATE_UNACTIVE, "device not activated"}, // 机器未激活

    // POWERMGR模块状态
    {STATUS_I_POWERMGR_STATUS_UPDATE, "power manager status update"}, // 当电源管理模块状态改变时，对外更新电源管理模块当前的状态
    {STATUS_I_POWERMGR_SLEEPING, "power manager sleeping"}, // 休眠过程中
    {STATUS_I_POWERMGR_WAKINGUP, "power manager waking up"}, // 唤醒过程中
    {STATUS_I_POWERMGR_WAKEUP, "power manager wake up"}, // 唤醒完成
    {STATUS_I_POWERMGR_SLEEP, "power manager sleep"}, // 休眠
    {STATUS_I_POWERMGR_POWEROFF, "power manager power off"}, // 软关机
    {STATUS_I_POWERMGR_IDLE, "power manager idle"}, // 电源管理模块空闲

    // STORAGE模块状态
    {STATUS_E_STORAGE_INNER_MISTAKE_FORMAT, "internal storage format error"}, // 存储格式错误
    {STATUS_E_STORAGE_HARDDISK_MISTAKE_FORMAT, "hard disk format error"}, // 硬盘格式错误
    {STATUS_E_STORAGE_INNER_FAILURE, "internal storage failure"}, // 存储设备故障
    {STATUS_E_STORAGE_HARDDISK_FAILURE, "hard disk failure"}, // 硬盘故障
    {STATUS_E_STORAGE_INNER_CAPACITY_FULL, "internal storage capacity full"}, // 存储容量满错误
    {STATUS_E_STORAGE_HARDDISK_CAPACITY_FULL, "hard disk capacity full"}, // 硬盘容量满错误
    {STATUS_E_STORAGE_UDISK_CAPACITY_FULL, "usb disk capacity full"}, // U盘容量满错误
    {STATUS_W_STORAGE_INNER_HEALTH, "internal storage health low"}, // 存储健康程度低
    {STATUS_W_STORAGE_INNER_CAPACITY_FULL, "internal storage capacity full"}, // 存储容量满
    {STATUS_W_STORAGE_UDISK_CAPACITY_FULL, "usb disk capacity full"}, // U盘容量满
    {STATUS_W_STORAGE_HARDDISK_CAPACITY_FULL, "hard disk capacity full"}, // 硬盘容量满
    {STATUS_W_STORAGE_INNER_CAPACITY_WILLFULL, "internal storage capacity will full"}, // 存储容量将满
    {STATUS_W_STORAGE_UDISK_CAPACITY_WILLFULL, "usb disk capacity will full"}, // U盘容量将满
    {STATUS_W_STORAGE_HARDDISK_CAPACITY_WILLFULL, "hard disk capacity will full"}, // 硬盘容量将满
    {STATUS_W_HARDDISK_HEALTH, "hard disk health low"}, // 硬盘健康程度低
    {STATUS_I_STORAGE_IDLE, "storage manager idle"}, // 存储管理模块IDLE

    {STATUS_INVALID, "invalid status"}
};


// Get the number of entries in the status ID / JOB_TYPE / CONSUMPTION_TYE mapping table
#define STATUS_ID_TABLE_SIZE (sizeof(status_id_to_string_table) / sizeof(status_id_to_string_table[0]))
#define JOB_TYPE_TABLE_SIZE (sizeof(g_job_string_table) / sizeof(g_job_string_table[0]))
#define CONSUMABLE_TYPE_TABLE_SIZE (sizeof(g_consumable_string_table) / sizeof(g_consumable_string_table[0]))

// Function to convert status ID to string
char* event_log_status_id_to_string(STATUS_ID_E status_id)
{
    // Linear search through the mapping table
    for (int i = 0; i < STATUS_ID_TABLE_SIZE; i++) {
        if (status_id_to_string_table[i].status_id == status_id) {
            return status_id_to_string_table[i].string;
        }
    }

    // Return default string if status ID not found
    return EVENT_LOG_UNKNOWN_STATUS;
}

// Convert job type to string
const char* event_log_job_type_to_string(enum event_log_name_job job_type)
{
    size_t i;
    for (i = 0; i < JOB_TYPE_TABLE_SIZE; i++) {
        if (g_job_string_table[i].job_type == job_type) {
            return g_job_string_table[i].string;
        }
    }
    return EVENT_LOG_UNKNOWN_JOB;
}

// Convert consumable type to string
const char* event_log_consumable_type_to_string(PRINT_CONSUMPTION_E type)
{
    size_t i;
    for (i = 0; i < CONSUMABLE_TYPE_TABLE_SIZE; i++) {
        if (g_consumable_string_table[i].type == type) {
            return g_consumable_string_table[i].string;
        }
    }
    return EVENT_LOG_UNKNOWN_CONSUMABLE;
}

// Get firmware upgrade string
const char* event_log_firmware_to_string(void)
{
    return EVENT_LOG_FIRMWARE_UPGRADE;
}



