/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netqio.h
 * @addtogroup netqio
 * @{
 * <AUTHOR>
 * @date 2023-06-21
 * @brief netqio public header file
 */
#ifndef __NETQIO_H__
#define __NETQIO_H__

#define ARGC_TABLE(arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, n, ...)      (n)
#define ARGS_COUNT(...)             ARGC_TABLE(__VA_ARGS__, 8, 7, 6, 5, 4, 3, 2, 1)

typedef enum
{
    NETQIO_SSL_SERVER = 0,
    NETQIO_SSL_CLIENT,
}
NETQIO_SSL_ROLE_E;

typedef enum
{
    NETQIO_SOCK_NOCLOSE = 0,
    NETQIO_SOCK_CLOSE
}
NETQIO_SOCK_FLAG_E;

/**
 * @brief       Create a TCP/IP network stream QIO based on a connection. This function can be seen as\n
 *              QIO_S* qio_tcp_create(PI_SOCKET_T sockfd, NETQIO_SOCK_FLAG_E flag = NETQIO_SOCK_CLOSE);
 * @param[in]   sockfd  : the socket handle.
 * @param[in]   flag    : the flag in this SSL connection ( define in NETQIO_SOCK_FLAG_E ).
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @date        2023-6-19
 */
#define         qio_tcp_create(...)         qio_tcp_create_custom(ARGS_COUNT(__VA_ARGS__), __VA_ARGS__)
QIO_S*          qio_tcp_create_custom       (int32_t n, PI_SOCKET_T sockfd, ...);

/**
 * @brief       Wrap the incoming QIO with an SSL wrapper. SSL negotiation needs to being at the server so a parameter is used to indicate if the SSL connection\n
 *              on the QIO is used as a client(typical) or server. This function can be seen as\n
 *              QIO_S* qio_ssl_create(PI_SOCKET_T sockfd, NETQIO_SSL_ROLE_E role, NETQIO_SOCK_FLAG_E flag = NETQIO_SOCK_CLOSE);
 * @param[in]   sockfd  : the socket handle.
 * @param[in]   role    : the role in this SSL connection ( define in NETQIO_SSL_ROLE_E ).
 * @param[in]   flag    : the flag in this SSL connection ( define in NETQIO_SOCK_FLAG_E ).
 * @return      The qio pointer
 * @retval      != NULL : success\n
 *              == NULL : fail
 * <AUTHOR> Xin
 * @date        2023-6-19
 */
#define         qio_ssl_create(...)         qio_ssl_create_custom(ARGS_COUNT(__VA_ARGS__), __VA_ARGS__)
QIO_S*          qio_ssl_create_custom       (int32_t n, PI_SOCKET_T sockfd, NETQIO_SSL_ROLE_E role, ...);

/**
 * @brief       Init the SSL layer.
 * @param[in]   certfile: the certificate file.
 * @param[in]   priv_key: the private key.
 * @return      Initialization result
 * @retval      =0 : success\n
 *              <0 : fail
 * <AUTHOR> Xin
 * @date        2023-6-19
 */
int32_t         qio_ssl_init                (const char* certfile, const char* priv_key);

/**
 * @brief       Deinit the SSL layer.
 * <AUTHOR> Xin
 * @date        2023-6-19
 */
void            qio_ssl_deinit              (void);

#endif /* __NETQIO_H__ */
/**
 *@}
 */
