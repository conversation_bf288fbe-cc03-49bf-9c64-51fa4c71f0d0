#include "trans/monitor/monitor.h"
#include "basic/sys/sys.h"
#include <string.h>
#ifdef TRANS_MONITOR
typedef struct HUMAN_STR_TO_FUNC {
    const char* str;
    int32_t (*func)(char* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length);
} HUMAN_STR_TO_FUNC;

static int32_t human_s2p_start(char* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length)
{
    uint16_t length = 0;
    buffer[0] = 0x01;

    length = (uint16_t)strlen(&human_buffer[6]) - 1;
    buffer[1] = (uint8_t)((length & 0xff00) >> 8);
    buffer[2] = (uint8_t)(length & 0x00ff);

    memcpy(&buffer[3], &human_buffer[6], length);

    *buffer_length = 3 + length;

    return 0;
}

static int32_t human_s2p_end(char* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length)
{
    buffer[0] = 0x03;
    buffer[1] = 0x00;
    buffer[2] = 0x01;
    buffer[3] = human_buffer[4] - '0';

    *buffer_length = 4;

    return 0;
}

static int32_t human_s2p_p2a(char* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length)
{
    uint16_t length = 0;

    buffer[0] = 0x09;
    buffer[3] = (human_buffer[5] - '0');

    length = (uint16_t)strlen(&human_buffer[7]) - 1;
    length += 1; // 加上rtid位
    buffer[1] = (uint8_t)((length & 0xff00) >> 8);
    buffer[2] = (uint8_t)(length & 0x00ff);

    memcpy(&buffer[4], &human_buffer[7], length - 1);
    buffer[length + 4] = '\0';
    *buffer_length = 3 + length;

    return 0;
}

static int32_t human_s2p_get_static(char* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length)
{
    uint16_t length = 0;
    buffer[0] = 0x0b;

    length = (uint16_t)strlen(&human_buffer[11]) - 1;
    buffer[1] = (uint8_t)((length & 0xff00) >> 8);
    buffer[2] = (uint8_t)(length & 0x00ff);

    memcpy(&buffer[3], &human_buffer[11], length);

    *buffer_length = 3 + length;

    return 0;
}

static int32_t human_s2p_get_dynamic(char* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length)
{
    buffer[0] = 0x0d;
    buffer[1] = 0x00;
    buffer[2] = 0x00;

    *buffer_length = 3 ;

    return 0;
}

HUMAN_STR_TO_FUNC human_str_to_protocol[] = {
    { "start", human_s2p_start },
    { "end", human_s2p_end },
    { "send", human_s2p_p2a },
    { "get static", human_s2p_get_static },
    { "get dynamic", human_s2p_get_dynamic }
};

// 人机转换，将对人友好的cmd转换成对机器友好的协议数据
static int32_t human_protocol_conversion(uint8_t* human_buffer, uint16_t human_buffer_length, uint8_t* buffer, uint16_t* buffer_length)
{
    int32_t ret = 0;

    for (int i = 0; i < sizeof(human_str_to_protocol) / sizeof(HUMAN_STR_TO_FUNC); i++) {
        // 查找
        if (0 == strncmp(human_str_to_protocol[i].str, human_buffer, strlen(human_str_to_protocol[i].str))) {
            ret = human_str_to_protocol[i].func(human_buffer, human_buffer_length, buffer, buffer_length);
        }
    }

    return ret;
}

int32_t receive_from_human_monitor(uint8_t* buffer, uint16_t* length, uint32_t ms)
{
    int32_t ret = 0;

    char human_buffer[BUFFER_MAX_LENGTH];
    uint16_t human_length = 0;

    ret = monitor_in(human_buffer, &human_length, ms);
    if (ret != 0) {
        return ret;
    }

    ret = human_protocol_conversion(human_buffer, human_length, buffer, length);

    return ret;
}

// 机人转换，将对机器友好的cmd协议转换成对人友好的字符串，并显示到monitor
int32_t send_to_human_monitor(const uint8_t* buffer, uint16_t length)
{
    int32_t ret = 0;
    uint8_t cmd = buffer[0];

    switch (cmd) {
    case 0x00:
        printf("ping...      \n");
        break;
    case 0x02:
        printf("app start success,rtid = %d\n", buffer[3]);
        break;
    case 0x04:
        printf("app end success,rtid = %d\n", buffer[3]);
        break;
    case 0x0a:
        printf("a2p msg:rtid = %d\n", buffer[3]);
        printf("a2p msg:%s\n", &buffer[4]);
        break;
    case 0x0c:
        printf("%s\n", &buffer[3]);
        break;
    case 0x0e:
        printf("%s\n", &buffer[3]);
        break;
    case 0x0f:
        printf("reset printer\n");
        break;
    };

    return ret;
}
#endif