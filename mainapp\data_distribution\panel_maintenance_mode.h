/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_config.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-08-12
 * @brief this file copy from engine_public_struct.h
 */

#ifndef _PANEL_MAINTENANCE_MODE_H
#define _PANEL_MAINTENANCE_MODE_H



/**
 * @brief Maintenance operation enum.
 *
 */
typedef enum
{
    MAINTENANCE_REQUEST = 0x00,                 ///< maintenance mode function request
    MAINTENANCE_ACK_SUCCESS,                    ///< maintenance mode function ack reply, means function execute success
    MAINTENANCE_ACK_FAIL,                       ///< maintenance mode function ack reply, means function execute fail
    MAINTENANCE_ACK_REBOOT,                     ///< maintenance mode funciton ack reply, means function execute success and need reboot
    MAINTENANCE_STOP,                           ///< maintenance mode function stop

    MAINTENANCE_INVALID,                        ///< please keep it in the end of the enum --- invalid data
}MAINTENANCE_ACTION_TYPE_E;

/**
 * @brief maintenance function enum
 */
typedef enum
{
    FUNC_GET_SENSOR_VALUE               = 0,               ///< get engine sensor value
    FUNC_GET_TABLE_VALUE                   ,               ///< get engine table value
    FUNC_GET_VOLTAGE_VALUE                 ,               ///< get engine voltage value
    FUNC_GET_ENVIRONMENT                   ,               ///< get engine environment sensor value
    FUNC_GET_CALI_VALUE                    ,               ///< get engine calibration value
    FUNC_GET_SKEW_VALUE                    ,               ///< get engine skew value
    FUNC_GET_FUSER_TEMP                    ,               ///< get engine fuser temperature
    FUNC_GET_FUSER_SPEED                   ,               ///< get engine fuser speed
    FUNC_GET_PRINT_AREA                    ,               ///< get engine print area
    FUNC_GET_PAPER_LOOP_AMOUNT             ,               ///< get engine paper loop amount
    FUNC_GET_COLOR_POSITION                ,               ///< get engine color position offset
    FUNC_GET_FAN_DELAY                     ,               ///< get engine ozone fan delay time
    FUNC_GET_PPM                           ,               ///< get engine ppm
    FUNC_GET_MAX_DENSITY                   ,               ///< get engine max density
    FUNC_GET_MONO_DENSITY                  ,               ///< get engine mono density
    FUNC_GET_PAPER_MEDIUM_DENSITY          ,               ///< get engine paper medium density
    FUNC_GET_TROUBLE_ISOLATION             ,               ///< get engine trouble isolation part
    FUNC_GET_WARMUP_MODE                   ,               ///< get engine warmup mode
    FUNC_GET_LED_STATE                     ,               ///< get engine led state
    FUNC_GET_FS540_FINISHER_ADJUST         ,               ///< get fs540 finisher adjustment
    FUNC_GET_BACKGROUND_VOLT               ,               ///< get background voltage
    FUNC_GET_NEUTRALIZING_VOLT             ,               ///< get neutralizing voltage
    FUNC_GET_TRANSFER_OUTPUT               ,               ///< get transfer output adjustment
    FUNC_GET_CHARGE_AC_OUTPUT              ,               ///< get charge AC output adjustment
    FUNC_GET_DV_AC_VOLT_CHOICE             ,               ///< get development AC voltage choice
    FUNC_GET_DV_AC_FREQ_CHOICE             ,               ///< get development AC voltage frequency choice
    FUNC_GET_LD_ADJUST                     ,               ///< get engine LD adjustment value
    FUNC_GET_PAPER_FEED_DIRECTION_ADJUST   ,               ///< get paper feed direction adjustment value
    FUNC_GET_PAPER_SEPARATION_ADJUST       ,               ///< get paper separation adjustment value

    FUNC_SET_FUSER_TEMP                 = 200,             ///< set engine fuser temperature
    FUNC_SET_FUSER_SPEED                    ,              ///< set engine fuser speed
    FUNC_SET_PRINT_AREA                     ,              ///< set engine print area
    FUNC_SET_PAPER_LOOP_AMOUNT              ,              ///< set engine paper loop amount
    FUNC_SET_COLOR_POSITION                 ,              ///< set engine color position offset
    FUNC_SET_FAN_DELAY                      ,              ///< set engine ozone fan delay time
    FUNC_SET_PPM                            ,              ///< set engine ppm
    FUNC_SET_MAX_DENSITY                    ,              ///< set engine max density
    FUNC_SET_MONO_DENSITY                   ,              ///< set engine mono density
    FUNC_SET_PAPER_MEDIUM_DENSITY           ,              ///< set engine paper medium density
    FUNC_SET_TROUBLE_ISOLATION              ,              ///< set engine trouble isolation part
    FUNC_SET_WARMUP_MODE                    ,              ///< set engine warmup mode
    FUNC_SET_LED_STATE                      ,              ///< set engine led state
    FUNC_SET_FS540_FINISHER_ADJUST          ,              ///< set fs540 finisher adjustment
    FUNC_SET_BACKGROUND_VOLT                ,              ///< set background voltage
    FUNC_SET_NEUTRALIZING_VOLT              ,              ///< set neutralizing voltage
    FUNC_SET_TRANSFER_OUTPUT                ,              ///< set transfer output adjustment
    FUNC_SET_CHARGE_AC_OUTPUT               ,              ///< set charge AC output adjustment
    FUNC_SET_DV_AC_VOLT_CHOICE              ,              ///< set development AC voltage choice
    FUNC_SET_DV_AC_FREQ_CHOICE              ,              ///< set development AC voltage frequency choice
    FUNC_SET_LD_ADJUST                      ,              ///< set engine LD adjustment value
    FUNC_SET_PAPER_FEED_DIRECTION_ADJUST   ,               ///< set paper feed direction adjustment value
    FUNC_SET_PAPER_SEPARATION_ADJUST       ,               ///< set paper separation adjustment value

    FUNC_SKEW_ADJUST                    = 400,             ///< engine execute skew adjustment itself
    FUNC_MANUAL_TRAY_ADJUST                 ,              ///< engine execute tray adjustment itself
    FUNC_EXECUTE_CALI                       ,              ///< engine execute calibrate itself
    FUNC_EXECUTE_CALI_RAW                   ,              ///< engine execute calibrate with raw data itself
    FUNC_TCR_TONER_SUPPLY                   ,              ///< engine execute TCR toner supply itself
    FUNC_HOPPER_TONER_FILLING               ,              ///< engine execute hopper toner filling itself
    FUNC_SENSOR_CHECK                       ,              ///< engine execute sensor check itself
    // finisher sensor check
    FUNC_RU_FEED_MRT_CHECK                  ,              ///< engine execute RU FEED MRT check itself
    FUNC_FNS_ENT_FEED_MRT_CHECK             ,              ///< engine execute FNS ENT_FEED_MRT check itself
    FUNC_MAIN_EXIT_FEED_MTR_CHECK           ,              ///< engine execute MAIN EXIT FEED_MTR check itself
    FUNC_TAMPER_FRONT_MTR_CHECK             ,              ///< engine execute TAMPER FRONT MTR check itself
    FUNC_STACKER_MTR_CHECK                  ,              ///< engine execute STACKER MTR check itself
    FUNC_TAMPER_REAR_MTR_CHECK              ,              ///< engine execute TAMPER REAR_MTR check itself
    FUNC_END_FENCE_MTR_CHECK                ,              ///< engine execute END FENCE MTR check itself
    FUNC_GRIP_EJECT_MTR_CHECK               ,              ///< engine execute GRIP EJECT MTR check itself
    FUNC_BUFFER_LIFT_MTR_CHECK              ,              ///< engine execute BUFFER LIFT MTR check itself
    FUNC_SCU_MTR_CHECK                      ,              ///< engine execute SCU MTR check itself
    FUNC_STAPLER_MOVE_MTR_CHECK             ,              ///< engine execute STAPLER MOVE MTR check itself
    FUNC_PRE_EJECT_MTR_CHECK                ,              ///< engine execute PRE_EJECT_MTR check itself
    FUNC_MAIN_PADDLE_MTR_CHECK              ,              ///< engine execute MAIN PADDLE MTR check itself
    FUNC_EXIT_DIVERT_CHECK                  ,              ///< engine execute EXIT DIVERT check itself
    FUNC_END_FENCE_REAR_MTR_CHECK           ,              ///< engine execute END FENCE REAR MTR check itself
    FUNC_PADDLE_ARM_MTR_CHECK               ,              ///< engine execute PADDLE ARM MTR check itself
    FUNC_PUSHER_MTR_CHECK                   ,              ///< engine execute PUSHER MTR check itself
    FUNC_SUB_EXIT_FEED_MTR_CHECK            ,              ///< engine execute SUB EXIT FEED MTR check itself
    FUNC_BERO_MTR_CHECK                     ,              ///< engine execute BERO MTR check itself
    FUNC_ZU_FEED_MTR_CHECK                  ,              ///< engine execute ZU FEED MTR check itself
    FUNC_JS_EXIT_DIVERT_CHECK               ,              ///< engine execute JS EXIT DIVERT check itself
    FUNC_SD_ENT_FEED_MTR_CHECK              ,              ///< engine execute SD ENT FEED MTR check itself
    FUNC_SD_BELT_TRAY_FEED_MTR_CHECK        ,              ///< engine execute SD BELT TRAY FEED MTR check itself
    FUNC_ZU_CHOPPER_CHECK                   ,              ///< engine execute ZU CHOPPER check itself
    FUNC_ZU_GUIDE_MTR_CHECK                 ,              ///< engine execute ZU GUIDE MTR check itself
    FUNC_ZU_PRESS_MTR_CHECK                 ,              ///< engine execute ZU PRESS MTR check itself
    FUNC_M302_SWING_DRIVE_M_HP_SEARCH_CHECK ,         	   ///< engine execute M302 SWING DRIVE M HP SEARCH check itself
    FUNC_M302_SWING_DRIVE_M_END_FACE_DETECTION_SENSOR_CONTROL_CHECK , ///< engine execute M302 SWING DRIVE M END FACE DETECTION SENSOR CONTROL check itself
    FUNC_M301_PUNCH_DRIVE_M_HP_SEARCH_CHECK ,              ///< engine execute M301 PUNCH DRIVE M HP_SEARCH check itself
    FUNC_M301_PUNCH_DRIVE_M_PUNCH_2_HOLES_CHECK ,          ///< engine execute M301 PUNCH DRIVE M PUNCH 2 HOLES check itself
    FUNC_M301_PUNCH_DRIVE_M_PUNCH_3_4_HOLES_CHECK ,        ///< engine execute M301 PUNCH DRIVE M PUNCH 3 4 HOLES check itself
    FUNC_M203_PAPER_FEED_LILNE_SPEED_DRIVE_CHECK ,         ///< engine execute M203 PAPER FEED_LILNE_SPEED_DRIVE check itself
    FUNC_MC201_UPPER_PAPER_FEED_CLUTCH_CHECK ,             ///< engine execute MC201 UPPER PAPER FEED CLUTCH check itself
    FUNC_MC202_LOWER_PAPER_FEED_CLUTCH_CHECK ,             ///< engine execute MC202 LOWER_PAPER FEED CLUTCH check itself
    FUNC_M201_UPPER_ELEVATING_M_DESCENT_CHECK ,            ///< engine execute M201 UPPER ELEVATING M DESCENT check itself
    FUNC_M201_UPPER_ELEVATING_M_ASCENDING_CHECK ,          ///< engine execute M201 UPPER ELEVATING M ASCENDING check itself
    FUNC_M202_LOWER_ELEVATING_M_DESCENT_CHECK ,            ///< engine execute 202 LOWER ELEVATING M DESCENT check itself
    FUNC_M202_LOWER_LIFT_M_ASCEND_CHECK      ,             ///< engine execute M202 LOWER LIFT M ASCEND check itself
    FUNC_SD201_UPPER_HANDLING_SOLENOID_CHECK ,             ///< engine execute SD201 UPPER HANDLING SOLENOID check itself
    FUNC_SD202_LOWER_HANDLING_SOLENOID_CHECK ,             ///< engine execute SD202 LOWER HANDLING SOLENOID check itself
    FUNC_MC203_RESIST_TRANSFER_CLUTCH_CHECK  ,             ///< engine execute MC203 RESIST TRANSFER CLUTCH check itself
    FUNC_SD_PAPER_TRANSPORT_MOTOR_CHECK      ,             ///< engine execute Saddle-Stitcher Drive Paper Transport Motor check itself
    FUNC_SD_FOLDING_ROLLER_MOTOR_CHECK       ,             ///< engine execute Saddle-Stitcher Drive Folding Roller Motor check itself
    FUNC_SD_LEADING_EDGE_GRIPPER_SOLENOID_CHECK ,          ///< engine execute Saddle-Stitcher Drive Leading Edge Gripper Solenoid check itself
    FUNC_SD_LEADING_EDGE_STOPPER_MOTOR_CHECK ,             ///< engine execute Saddle-Stitcher Drive Leading Edge Stopper Motor check itself
    FUNC_SD_ALIGNMENT_PLATE_MOTOR_CHECK ,                  ///< engine execute Saddle-Stitcher Drive Alignment Plate Motor check itself
    FUNC_SD_FOLDING_KNIFE_MOTOR_CHECK   ,                  ///< engine execute Saddle-Stitcher Drive Folding Knife Motor check itself
    FUNC_SD_FOLDING_CHANGE_MOTOR_CHECK  ,                  ///< engine execute Saddle-Stitcher Drive Folding Change Motor check itself
    FUNC_SD_PAPER_DISCHARGE_CONTROL_MOTOR_CHECK ,          ///< engine execute Saddle-Stitcher Drive Paper Discharge Control Motor check itself
    FUNC_SD_PADDLE_MOTOR_CHECK          ,                  ///< engine execute Saddle-Stitcher Drive Paddle Motor check itself
    FUNC_SD_TRI_FOLDING_KNIFE_MOTOR     ,                  ///< engine execute Saddle-Stitcher Drive Tri-Folding Knife Motor check itself
    // printer sensor check
    FUNC_PAPER_TRANSFER_MOTOR           ,                  ///< engine execute Paper Transfer Motor check itself
    FUNC_PAPER_ENTRANCE_MOTOR           ,                  ///< engine execute Paper Entrance Motor check itself
    FUNC_PAPER_EXIT_MOTOR               ,                  ///< engine execute Paper Exit Motor(Front) check itself
    FUNC_PAPER_ALIGNMENT_PLATE_MOTOR_FRONT ,               ///< engine execute Paper Alignment Plate Motor(Front) check itself
    FUNC_MAIN_TRAY_UP_DOWN_MOTOR        ,                  ///< engine execute Main Tray up/down Motor check itself
    FUNC_PAPER_ALIGNMENT_PLATE_MOTOR_REAR ,                ///< engine execute Paper Alignment Plate Motor(Rear) Motor check itself
    FUNC_SIDE_STOPPER_GUIDE_MOTOR       ,                  ///< engine execute Side Stopper Guide Motor check itself
    FUNC_PAPER_EXIT_BELT_MOTOR          ,                  ///< engine execute Paper Exit Belt Motor check itself
    FUNC_EXIT_ROLLER_RETRACTION_MOTOR   ,                  ///< engine execute Exit Roller Retraction Motor check itself
    FUNC_TAIL_EDGE_HOLDING_PLATE_MOTOR  ,                  ///< engine execute Tail Edge Holding Plate Motor check itself
    FUNC_SIDE_STAPLER_MOVEMENT_MOTOR    ,                  ///< engine execute Side Stapler Movement Motor check itself
    FUNC_PRE_EXIT_DRIVE_MOTOR           ,                  ///< engine execute Pre Exit Drive Motor check itself
    FUNC_PUNCH_DRIVE_MOTOR              ,                  ///< engine execute Punch Drive Motor check itself
    FUNC_PADDLE_MOTOR                   ,                  ///< engine execute Paddle Motor check itself
    FUNC_OUTPUT_TRAY_CHANGE_MOTOR       ,                  ///< engine execute Output Tray Change Motor check itself
    // printer paper road check
    FUNC_SIMPLEX_FEED                   ,                  ///< engine execute simplex feed check itself
    // engine function
    FUNC_ENG_RESET                      ,                  ///< engine execute engine reset itself
    FUNC_NEW_UNIT_DETECTION_CONFIRM     ,                  ///< engine execute new unit detection confirmation itself

    FUNC_PRINT_GRADATION_PATTERN        = 800,             ///< print gradation test pattern
    FUNC_PRINT_SKEW_PATTERN             ,                  ///< print skew test pattern
    FUNC_PRINT_GRID_PATTERN             ,                  ///< print grid test pattern

    FUNC_INVALID,                                          ///< please keep it in the end of the enum --- invalid data
}MAINTENANCE_FUNCTION_TYPE_E;

typedef struct STEngMaintenanceSensorValue
{
    uint8_t                     tray1_paper_empty;                                  ///< 0-not exist, 1-exist
    uint8_t                     tray1_paper_near_empty;                             ///< 0-not exist, 1-exist
    uint8_t                     tray1_feed;                                         ///< 0-not exist, 1-exist
    uint8_t                     tray1_upper_lift_up_limit;                          ///< 0-not exist, 1-exist
    uint8_t                     tray2_paper_empty;                                  ///< 0-not exist, 1-exist
    uint8_t                     tray2_paper_near_empty;                             ///< 0-not exist, 1-exist
    uint8_t                     tray2_feed;                                         ///< 0-not exist, 1-exist
    uint8_t                     tray2_upper_lift_up_limit;                          ///< 0-not exist, 1-exist
    uint8_t                     tray2_vertical_transport;                           ///< 0-not exist, 1-exist
    uint8_t                     tray3_paper_empty;                                  ///< 0-not exist, 1-exist
    uint8_t                     tray3_paper_near_empty;                             ///< 0-not exist, 1-exist
    uint8_t                     tray3_feed;                                         ///< 0-not exist, 1-exist
    uint8_t                     tray3_upper_lift_up_limit;                          ///< 0-not exist, 1-exist
    uint8_t                     tray3_vertical_transport;                           ///< 0-not exist, 1-exist
    uint8_t                     tray4_paper_empty;                                  ///< 0-not exist, 1-exist
    uint8_t                     tray4_paper_near_empty;                             ///< 0-not exist, 1-exist
    uint8_t                     tray4_feed;                                         ///< 0-not exist, 1-exist
    uint8_t                     tray4_upper_lift_up_limit;                          ///< 0-not exist, 1-exist
    uint8_t                     tray4_vertical_transport;                           ///< 0-not exist, 1-exist
    uint8_t                     tray_manual_multi_size_1;                           ///< 0-not exist, 1-exist
    uint8_t                     tray_manual_multi_size_2;                           ///< 0-not exist, 1-exist
    uint8_t                     tray_manual_lift_up_position;                       ///< 0-not exist, 1-exist
    uint8_t                     tray_manual_paper_empty;                            ///< 0-not exist, 1-exist
    uint8_t                     tray3_4_intermediate_roller_sensor;                 ///< 0-not exist, 1-exist
    uint8_t                     tray3_4_horizon_transport_sensor;                   ///< 0-not exist, 1-exist
    uint8_t                     tray3_4_roller_front_sensor;                        ///< 0-not exist, 1-exist
    uint8_t                     tray3_4_paper_exit;                                 ///< 0-not exist, 1-exist
    uint8_t                     tray3_4_fusing_loop_detect;                         ///< 0-not exist, 1-exist
    uint8_t                     tray3_4_registration_sensor;                        ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_upper_lift_up_limit;                    ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_lower_lift_up_limit;                    ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_shift_tray_home;                        ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_feed;                                   ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_vertical_transport;                     ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_paper_empty;                            ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_main_tray_paper_empty;                  ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_paper_near_empty;                       ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_division_board_position;                ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_open;                                   ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_shift_tray_empty;                       ///< 0-not exist, 1-exist
    uint8_t                     tray_lct_in_paper_level_detection;                  ///< 0-not exist, 1-exist
    uint8_t                     waste_toner_full;                                   ///< 0-not exist, 1-exist
    uint8_t                     fusing_roller_retraction;                           ///< 0-not exist, 1-exist
    uint8_t                     transfer_belt_retraction;                           ///< 0-not exist, 1-exist
}ENG_MAINTENANCE_SENSOR_VALUE_S, *ENG_MAINTENANCE_SENSOR_VALUE_P;

/**
 * @brief km engine maintenance table value struct
 *
 */
typedef struct STEngMaintenanceTableValue
{
    int16_t                     plain_paper_vdc_c;                                  ///< plain paper vdc c value
    int16_t                     plain_paper_vdc_m;                                  ///< plain paper vdc m value
    int16_t                     plain_paper_vdc_y;                                  ///< plain paper vdc y value
    int16_t                     plain_paper_vdc_k;                                  ///< plain paper vdc k value
    int16_t                     plain_paper_vg_c;                                   ///< plain paper vg c value
    int16_t                     plain_paper_vg_m;                                   ///< plain paper vg m value
    int16_t                     plain_paper_vg_y;                                   ///< plain paper vg y value
    int16_t                     plain_paper_vg_k;                                   ///< plain paper vg k value
    int16_t                     thick_paper_1_1plus_vdc_c;                          ///< thick paper 1 and 1plus vdc c value
    int16_t                     thick_paper_1_1plus_vdc_m;                          ///< thick paper 1 and 1plus vdc m value
    int16_t                     thick_paper_1_1plus_vdc_y;                          ///< thick paper 1 and 1plus vdc y value
    int16_t                     thick_paper_1_1plus_vdc_k;                          ///< thick paper 1 and 1plus vdc k value
    int16_t                     thick_paper_1_1plus_vg_c;                           ///< thick paper 1 and 1plus vg c value
    int16_t                     thick_paper_1_1plus_vg_m;                           ///< thick paper 1 and 1plus vg m value
    int16_t                     thick_paper_1_1plus_vg_y;                           ///< thick paper 1 and 1plus vg y value
    int16_t                     thick_paper_1_1plus_vg_k;                           ///< thick paper 1 and 1plus vg k value
    int16_t                     thick_paper_2_3_4_vdc_c;                            ///< thick paper 2,3,4 vdc c value
    int16_t                     thick_paper_2_3_4_vdc_m;                            ///< thick paper 2,3,4 vdc m value
    int16_t                     thick_paper_2_3_4_vdc_y;                            ///< thick paper 2,3,4 vdc y value
    int16_t                     thick_paper_2_3_4_vdc_k;                            ///< thick paper 2,3,4 vdc k value
    int16_t                     thick_paper_2_3_4_vg_c;                             ///< thick paper 2,3,4 vg c value
    int16_t                     thick_paper_2_3_4_vg_m;                             ///< thick paper 2,3,4 vg m value
    int16_t                     thick_paper_2_3_4_vg_y;                             ///< thick paper 2,3,4 vg y value
    int16_t                     thick_paper_2_3_4_vg_k;                             ///< thick paper 2,3,4 vg k value
    int16_t                     mono_vdc_c;                                         ///< mono vdc c
    int16_t                     mono_vdc_m;                                         ///< mono vdc m
    int16_t                     mono_vdc_y;                                         ///< mono vdc y
    int16_t                     mono_vdc_k;                                         ///< mono vdc k
    int16_t                     mono_vg_c;                                          ///< mono vg c
    int16_t                     mono_vg_m;                                          ///< mono vg m
    int16_t                     mono_vg_y;                                          ///< mono vg y
    int16_t                     mono_vg_k;                                          ///< mono vg k
    int16_t                     ld_brightness_c;                                    ///< ld brightness c value
    int16_t                     ld_brightness_m;                                    ///< ld brightness m value
    int16_t                     ld_brightness_y;                                    ///< ld brightness y value
    int16_t                     ld_brightness_k;                                    ///< ld brightness k value
    int16_t                     charge_ac_output_vpp_c1;                            ///< charge ac output vpp c1
    int16_t                     charge_ac_output_vpp_m1;                            ///< charge ac output vpp m1
    int16_t                     charge_ac_output_vpp_y1;                            ///< charge ac output vpp y1
    int16_t                     charge_ac_output_vpp_k1;                            ///< charge ac output vpp k1
    int16_t                     charge_ac_output_vpp_c2;                            ///< charge ac output vpp c2
    int16_t                     charge_ac_output_vpp_m2;                            ///< charge ac output vpp m2
    int16_t                     charge_ac_output_vpp_y2;                            ///< charge ac output vpp y2
    int16_t                     charge_ac_output_vpp_k2;                            ///< charge ac output vpp k2
}ENG_MAINTENANCE_TABLE_VALUE_S, *ENG_MAINTENANCE_TABLE_VALUE_P;

/**
 * @brief km engine maintenance voltage value struct
 *
 */
typedef struct STEngMaintenanceVoltageValue
{
    int16_t                     tcr_c;                                      ///< tcr c value
    int16_t                     tcr_m;                                      ///< tcr m value
    int16_t                     tcr_y;                                      ///< tcr y value
    int16_t                     tcr_k;                                      ///< tcr k value
    int16_t                     idc_1;                                      ///< idc 1 value
    int16_t                     idc_2;                                      ///< idc 2 value
    int16_t                     middle_heat_temperature;                    ///< middle heat temperature value
    int16_t                     medium_heat_temperature;                    ///< medium heat temperature value
    int16_t                     heat_edge_temperature;                      ///< heat edge temperature value
    int16_t                     main_heating_temperature;                   ///< main heating temperature value
    int16_t                     idc_sensor_adjust_1;                        ///< idc sensor adjust 1 value
    int16_t                     idc_sensor_adjust_2;                        ///< idc sensor adjust 2 value
    int16_t                     atcv_c;                                     ///< atct c value
    int16_t                     atcv_m;                                     ///< atct m value
    int16_t                     atcv_y;                                     ///< atct y value
    int16_t                     atcv_k;                                     ///< atct k value
    int16_t                     atcv_2nd;                                   ///< atct 2nd value
}ENG_MAINTENANCE_VOLTAGE_VALUE_S, *ENG_MAINTENANCE_VOLTAGE_VALUE_P;

/**
 * @brief km engine maintenance environment value struct
 *
 */
typedef struct STEngMaintenanceEnvironment
{
    int16_t                     internal_temp;                              ///< internal temperature value
    int16_t                     humidity;                                   ///< humidity value
    int16_t                     absolute_humidity;                          ///< absolute humidity value
    int16_t                     paper_temp;                                 ///< paper temperature value
}ENG_MAINTENANCE_ENVIRONMENT_VALUE_S, *ENG_MAINTENANCE_ENVIRONMENT_VALUE_P;

/**
 * @brief km engine maintenance fuser temperature struct
 *
 */
typedef struct STEngMaintenanceFuserTemp
{
    int8_t                      plain_paper_600dpi;                         ///< plain paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      ohp_600dpi;                                 ///< OHP paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      plain_paper_plus_600dpi;                    ///< plain plus paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_1_600dpi;                             ///< thick 1 paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_1_plus_600dpi;                        ///< thick 1 plus paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_2_600dpi;                             ///< thick 2 paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_3_600dpi;                             ///< thick 3 paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      postcard_600dpi;                            ///< postcard paper 600dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      envelope_600dpi;                            ///< envelope paper 600dpi fuser temperature(-10 ~ +20), step:5
    int8_t                      plain_paper_1200dpi;                        ///< plain paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      ohp_1200dpi;                                ///< OHP paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      plain_paper_plus_1200dpi;                   ///< plain plus paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_1_1200dpi;                            ///< thick 1 paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_1_plus_1200dpi;                       ///< thick 1 plus paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_2_1200dpi;                            ///< thick 2 paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      thick_3_1200dpi;                            ///< thick 3 paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      postcard_1200dpi;                           ///< postcard paper 1200dpi fuser temperature(-20 ~ +10), step:5
    int8_t                      envelope_1200dpi;                           ///< envelope paper 1200dpi fuser temperature(-10 ~ +20), step:5
}ENG_MAINTENANCE_FUSER_TEMP_VALUE_S, *ENG_MAINTENANCE_FUSER_TEMP_VALUE_P;

/**
 * @brief km engine maintenance fuser speed struct
 *
 */
typedef struct STEngMaintenanceFuserSpeed
{
    int8_t                      plain_paper_600dpi;                         ///< plain paper 600dpi fuser speed(-20 ~ +20), step:1
    int8_t                      thick_1_1plus_600dpi;                       ///< thick 1 and 1 plus paper 600dpi fuser speed(-20 ~ +20), step:1
    int8_t                      thick_2_3_600dpi;                           ///< thick 2 paper 600dpi fuser speed(-20 ~ +20), step:1
    int8_t                      all_1200dpi;                                ///< all 1200dpi fuser speed(-20 ~ +20), step:1
}ENG_MAINTENANCE_FUSER_SPEED_VALUE_S, *ENG_MAINTENANCE_FUSER_SPEED_VALUE_P;

/**
 * @brief km engine maintenance print area struct
 *
 */
typedef struct STEngMaintenancePrintArea
{
    int8_t                      plain_paper_top_margin;                     ///< plain paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      plain_paper_plus_top_margin;                ///< plain plus paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      thick_1_plus_top_margin;                    ///< thick 1 and 1 plus paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      thick_2_top_margin;                         ///< thick 2 paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      thick_3_top_margin;                         ///< thick 3 paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      ohp_top_margin;                             ///< OHP paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      envelop_top_margin;                         ///< envelop paper top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_1_long_top_margin;                     ///< tray 1 long duplex duplex left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_1_short_top_margin;                    ///< tray 1 short duplex duplex left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_2_top_margin;                          ///< tray 2 top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_manual_top_margin;                     ///< multiple tray top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      plain_paper_top_margin_duplex;              ///< plain paper duplex top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      plain_paper_plus_top_margin_duplex;         ///< plain plus paper duplex top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      thick_1_plus_top_margin_duplex;             ///< thick 1 and 1 plus paper duplex top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      thick_2_top_margin_duplex;                  ///< thick 2 paper duplex top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      thick_3_top_margin_duplex;                  ///< thick 3 paper duplex top margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_1_left_margin;                         ///< tray 1 left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_2_left_margin;                         ///< tray 2 left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_3_left_margin;                         ///< tray 3 left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_4_left_margin;                         ///< tray 4 left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_manual_left_margin;                    ///< mulitple tray left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_1_left_margin_duplex;                  ///< tray 1 duplex left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_2_left_margin_duplex;                  ///< tray 2 duplex left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_3_left_margin_duplex;                  ///< tray 3 duplex left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_4_left_margin_duplex;                  ///< tray 4 duplex left margin(-30 ~ +30), unit:0.1mm, step:2
    int8_t                      tray_manual_left_margin_duplex;             ///< mulitple tray duplex left margin(-30 ~ +30), unit:0.1mm, step:2
}ENG_MAINTENANCE_PRINT_AREA_VALUE_S, *ENG_MAINTENANCE_PRINT_AREA_VALUE_P;

/**
 * @brief km engine maintenance paper loop amount struct
 *
 */
typedef struct
{
    int8_t                      tray_1_plain_paper_600dpi;                  ///< tray 1 plain paper loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_1_plain_paper_plus_600dpi;             ///< tray 1 plain paper plus loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_1_thick_1_plus_600dpi;                 ///< tray 1 thick 1 and 1+ loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_1_thick_2_3_600dpi;                    ///< tray 1 thick 2 and 3 loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_2_3_4_lct_plain_paper_600dpi;          ///< tray 2,3,4,lct in plain paper loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_2_3_4_lct_plain_paper_plus_600dpi;     ///< tray 2,3,4,lct in plain paper plus loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_2_3_4_lct_thick_1_plus_600dpi;         ///< tray 2,3,4,lct in thick 1 and 1+ loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_2_3_4_lct_thick_2_3_600dpi;            ///< tray 2,3,4,lct in thick 2 and 3 loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_manual_plain_paper_600dpi;             ///< tray manual plain paper loop amount in 600dpi(-11 ~ +8), step:1
    int8_t                      tray_manual_plain_paper_plus_600dpi;        ///< tray manual plain paper plus loop amount in 600dpi(-11 ~ +8), step:1
    int8_t                      tray_manual_thick_1_plus_600dpi;            ///< tray manual thick 1 and 1+ loop amount in 600dpi(-11 ~ +8), step:1
    int8_t                      tray_manual_thick_2_3_600dpi;               ///< tray manual thick 2 and 3 loop amount in 600dpi(-11 ~ +8), step:1
    int8_t                      tray_duplex_plain_paper_600dpi;             ///< tray duplex plain paper loop amount in 600dpi(-12 ~ +10), step:1
    int8_t                      tray_duplex_plain_paper_plus_600dpi;        ///< tray duplex plain paper plus loop amount in 600dpi(-12 ~ +10), step:1
    int8_t                      tray_duplex_thick_1_plus_600dpi;            ///< tray duplex thick 1 and 1+ loop amount in 600dpi(-11 ~ +7), step:1
    int8_t                      tray_duplex_thick_2_3_600dpi;               ///< tray duplex thick 2 and 3 loop amount in 600dpi(-12 ~ +10), step:1

    int8_t                      tray_1_1200dpi;                             ///< tray 1 loop amount in 1200dpi(-11 ~ +7), step:1
    int8_t                      tray_2_3_4_lct_1200dpi;                     ///< tray 2,3,4,lct in loop amount in 1200dpi(-11 ~ +7), step:1
    int8_t                      tray_manual_1200dpi;                        ///< tray manual loop amount in 1200dpi(-11 ~ +8), step:1
    int8_t                      tray_duplex_1200dpi;                        ///< tray duplex loop amount in 1200dpi(-12 ~ +10), step:1
}ENG_MAINTENANCE_PAPER_LOOP_AMOUNT_S, *ENG_MAINTENANCE_PAPER_LOOP_AMOUNT_P;

/**
 * @brief km engine maintenance color position struct
 *
 */
typedef struct STEngMaintenanceColorPos
{
    int8_t                      plain_paper_x_registration_c;               ///< plain paper x-axis registration c(-6 ~ +6), step:1
    int8_t                      plain_paper_x_registration_m;               ///< plain paper x-axis registration m(-6 ~ +6), step:1
    int8_t                      plain_paper_x_registration_y;               ///< plain paper x-axis registration y(-6 ~ +6), step:1
    int8_t                      plain_plus_paper_x_registration_c;          ///< plain plus paper x-axis registration c(-6 ~ +6), step:1
    int8_t                      plain_plus_paper_x_registration_m;          ///< plain plus paper x-axis registration m(-6 ~ +6), step:1
    int8_t                      plain_plus_paper_x_registration_y;          ///< plain plus paper x-axis registration y(-6 ~ +6), step:1
    int8_t                      thick_1_plus_paper_x_registration_c;        ///< thick 1 and 1 plus paper x-axis registration c(-6 ~ +6), step:1
    int8_t                      thick_1_plus_paper_x_registration_m;        ///< thick 1 and 1 plus paper x-axis registration m(-6 ~ +6), step:1
    int8_t                      thick_1_plus_paper_x_registration_y;        ///< thick 1 and 1 plus paper x-axis registration y(-6 ~ +6), step:1
    int8_t                      thick_2_paper_x_registration_c;             ///< thick 2 paper x-axis registration c(-6 ~ +6), step:1
    int8_t                      thick_2_paper_x_registration_m;             ///< thick 2 paper x-axis registration m(-6 ~ +6), step:1
    int8_t                      thick_2_paper_x_registration_y;             ///< thick 2 paper x-axis registration y(-6 ~ +6), step:1
    int8_t                      thick_3_paper_x_registration_c;             ///< thick 3 paper x-axis registration c(-6 ~ +6), step:1
    int8_t                      thick_3_paper_x_registration_m;             ///< thick 3 paper x-axis registration m(-6 ~ +6), step:1
    int8_t                      thick_3_paper_x_registration_y;             ///< thick 3 paper x-axis registration y(-6 ~ +6), step:1
    int8_t                      y_registration_c;                           ///< y-axis registration c(-6 ~ +6), step:1
    int8_t                      y_registration_m;                           ///< y-axis registration m(-6 ~ +6), step:1
    int8_t                      y_registration_y;                           ///< y-axis registration y(-6 ~ +6), step:1
}ENG_MAINTENANCE_COLOR_POS_VALUE_S, *ENG_MAINTENANCE_COLOR_POS_VALUE_P;

/**
 * @brief km engine maintenance fan delay struct
 *
 */
typedef struct STEngMaintenanceFanDelay
{
    int8_t                      fan_delay;                                  ///< fan delay mode,(0 ~ 15), step:1
}ENG_MAINTENANCE_FAN_DELAY_S, *ENG_MAINTENANCE_FAN_DELAY_P;

/**
 * @brief km engine maintenance ppm control struct
 *
 */
typedef struct STEngMaintenancePpmCtl
{
    int8_t                      ppm_ctrl;                                   /// thick paper ppm control, 0-100%, 1-70%, 2-50%
}ENG_MAINTENANCE_PPM_CTRL_S, *ENG_MAINTENANCE_PPM_CTRL_P;

/**
 * @brief km engine maintenance max density struct
 *
 */
typedef struct STEngMaintenanceDensityMax
{
    int8_t                      copy_max_density_c;                         ///< copy max density c(-10 ~ +10), step:1
    int8_t                      copy_max_density_m;                         ///< copy max density m(-10 ~ +10), step:1
    int8_t                      copy_max_density_y;                         ///< copy max density y(-10 ~ +10), step:1
    int8_t                      copy_max_density_k;                         ///< copy max density k(-10 ~ +10), step:1
    int8_t                      print_max_density_c;                        ///< print max density c(-10 ~ +10), step:1
    int8_t                      print_max_density_m;                        ///< print max density m(-10 ~ +10), step:1
    int8_t                      print_max_density_y;                        ///< print max density y(-10 ~ +10), step:1
    int8_t                      print_max_density_k;                        ///< print max density k(-10 ~ +10), step:1
}ENG_MAINTENANCE_DENSITY_MAX_S, *ENG_MAINTENANCE_DENSITY_MAX_P;

/**
 * @brief km engine maintenance mono density struct
 *
 */
typedef struct STEngMaintenanceDensityMono
{
    int8_t                      mono_density;                               ///< mono density(-2 ~ +2), step:1
}ENG_MAINTENANCE_DENSITY_MONO_S, *ENG_MAINTENANCE_DENSITY_MONO_P;

/**
 * @brief km engine maintenance thick paper density struct
 *
 */
typedef struct STEngMaintenanceDensityThick
{
    int8_t                      thick_type_1_density_c;                     ///< thick type 1 density c(-5 ~ +5), step:1
    int8_t                      thick_type_1_density_m;                     ///< thick type 1 density m(-5 ~ +5), step:1
    int8_t                      thick_type_1_density_y;                     ///< thick type 1 density y(-5 ~ +5), step:1
    int8_t                      thick_type_1_density_k;                     ///< thick type 1 density k(-5 ~ +5), step:1
    int8_t                      thick_type_2_density_c;                     ///< thick type 2 density c(-5 ~ +5), step:1
    int8_t                      thick_type_2_density_m;                     ///< thick type 2 density m(-5 ~ +5), step:1
    int8_t                      thick_type_2_density_y;                     ///< thick type 2 density y(-5 ~ +5), step:1
    int8_t                      thick_type_2_density_k;                     ///< thick type 2 density k(-5 ~ +5), step:1
}ENG_MAINTENANCE_DENSITY_THICK_S, *ENG_MAINTENANCE_DENSITY_THICK_P;

/**
 * @brief km engine maintenance trouble isolation struct
 *
 */
typedef struct STEngMaintenanceTroubleIsolation
{
    uint8_t                     tray1_shutdown;                             ///< 0-normal, 1-shutdown
    uint8_t                     tray2_shutdown;                             ///< 0-normal, 1-shutdown
    uint8_t                     tray3_shutdown;                             ///< 0-normal, 1-shutdown
    uint8_t                     tray4_shutdown;                             ///< 0-normal, 1-shutdown
    uint8_t                     tray_lct_in_shutdown;                       ///< 0-normal, 1-shutdown
    uint8_t                     tray_manual_shutdown;                       ///< 0-normal, 1-shutdown
    uint8_t                     center_fold_shutdown;                       ///< 0-normal, 1-shutdown
    uint8_t                     tri_fold_shutdown;                          ///< 0-normal, 1-shutdown
    uint8_t                     z_fold_shutdown;                            ///< 0-normal, 1-shutdown
    uint8_t                     punch_shutdown;                             ///< 0-normal, 1-shutdown
    uint8_t                     staple_shutdown;                            ///< 0-normal, 1-shutdown
}ENG_MAINTENANCE_TROUBLE_ISOLATION_S, *ENG_MAINTENANCE_TROUBLE_ISOLATION_P;

/**
 * @brief km engine maintenance warmup mode struct
 *
 */
typedef struct STEngMaintenanceWarmupMode
{
    uint8_t                     warmup_mode;                                ///< 0-mode 1, 1-mode 2, 2-mode 3, 3-mode 4
}ENG_MAINTENANCE_WARMUP_MODE_S, *ENG_MAINTENANCE_WARMUP_MODE_P;

/**
 * @brief km engine maintenance led state struct
 *
 */
typedef struct STEngMaintenanceLedState
{
    uint8_t                     remain_paper_led_state;                     ///< 0-mode 1, 1-mode 2
}ENG_MAINTENANCE_LED_STATE_S, *ENG_MAINTENANCE_LED_STATE_P;

/**
 * @brief km engine maintenance skew value struct
 *
 */
typedef struct STEngMaintenanceSkewValue
{
    int16_t                      skew_value_c;                               ///< skew c value
    int16_t                      skew_value_m;                               ///< skew m value
    int16_t                      skew_value_y;                               ///< skew y value
}ENG_MAINTENANCE_SKEW_VALUE_S, *ENG_MAINTENANCE_SKEW_VALUE_P;

/**
 * @brief km engine maintenance finisher adjustment struct
 *
 */
typedef struct STEngMainTenanceFinisher540Adjust
{
    int8_t                      fs540_center_pos_all;                       ///< FS540 all paper size center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_b4_sef;                    ///< FS540 B4 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_11x17_sef;                 ///< FS540 11x17 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_sra3_sef;                  ///< FS540 SRA3 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_a4_sef;                    ///< FS540 A4 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_8_5x14_sef;                ///< FS540 8.5x14 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_8k_sef;                    ///< FS540 8K SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_a3_sef;                    ///< FS540 A3 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_12x18_sef;                 ///< FS540 12x18 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_8_5x11_sef;                ///< FS540 8.5x11 SEF center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_custom_mid;                ///< FS540 custom middle center position adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_pos_custom_big;                ///< FS540 custom big center position adjustment(-100 ~ +100), unit:0.1mm, step:1

    int8_t                      fs540_center_fold_all;                      ///< FS540 all paper size center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_b4_sef;                   ///< FS540 B4 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_11x17_sef;                ///< FS540 11x17 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_sra3_sef;                 ///< FS540 SRA3 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_a4_sef;                   ///< FS540 A4 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_8_5x14_sef;               ///< FS540 8.5x14 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_8k_sef;                   ///< FS540 8K SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_a3_sef;                   ///< FS540 A3 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_12x18_sef;                ///< FS540 12x18 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_8_5x11_sef;               ///< FS540 8.5x11 SEF center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_custom_mid;               ///< FS540 custom middle center fold adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_center_fold_custom_big;               ///< FS540 custom big center fold adjustment(-100 ~ +100), unit:0.1mm, step:1

    int8_t                      fs540_vertical_punch_all;                   ///< FS540 all paper size vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_b4_sef;                ///< FS540 B4 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_a4_sef;                ///< FS540 A4 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_b5_sef;                ///< FS540 B5 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_a5_sef;                ///< FS540 A5 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_a3_sef;                ///< FS540 A3 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_a4_lef;                ///< FS540 A4 LEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_b5_lef;                ///< FS540 B5 LEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_a5_lef;                ///< FS540 A5 LEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_11x17_sef;             ///< FS540 11x17 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_8_5x14_sef;            ///< FS540 8.5x14 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_8_5x11_sef;            ///< FS540 5.5x8.5 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_5_5x8_5_sef;           ///< FS540 5.5x8.5 SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_8k_sef;                ///< FS540 8K SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_16k_sef;               ///< FS540 16K SEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_8_5x11_lef;            ///< FS540 8.5x11 LEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_5_5x8_5_lef;           ///< FS540 5.5x8.5 LEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_vertical_punch_16k_lef;               ///< FS540 16K LEF vertical punch adjustment(-50 ~ +50), unit:0.1mm, step:1

    int8_t                      fs540_vertical_punch_z_fold;                ///< FS540 vertical punch positon adjustment(-50 ~ +50), unit:0.1mm, step:1
    int8_t                      fs540_horizontal_punch_z_fold;              ///< FS540 horizontal punch positon adjustment(-50 ~ +50), unit:0.1mm, step:1

    int8_t                      fs540_1st_z_fold_all;                       ///< FS540 all paper type first Z fold positon adjustment(-128 ~ +127), unit:0.1mm, step:1
    int8_t                      fs540_1st_z_fold_plain_paper;               ///< FS540 plain paper first Z fold positon adjustment(-128 ~ +127), unit:0.1mm, step:1
    int8_t                      fs540_1st_z_fold_recycle;                   ///< FS540 recycle paper first Z fold positon adjustment(-128 ~ +127), unit:0.1mm, step:1

    int8_t                      fs540_2nd_z_fold_all;                       ///< FS540 all paper type second Z fold positon adjustment(-128 ~ +127), unit:0.1mm, step:1
    int8_t                      fs540_2nd_z_fold_plain_paper;               ///< FS540 plain paper second Z fold positon adjustment(-128 ~ +127), unit:0.1mm, step:1
    int8_t                      fs540_2nd_z_fold_recycle;                   ///< FS540 recycle paper second Z fold positon adjustment(-128 ~ +127), unit:0.1mm, step:1

    int8_t                      fs540_1st_tri_fold_all;                     ///< FS540 all paper size first tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_1st_tri_fold_a4_sef;                  ///< FS540 A4 SEF first tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_1st_tri_fold_8_5x11_sef;              ///< FS540 8.5x11 SEF first tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_1st_tri_fold_16k_sef;                 ///< FS540 16K SEF first tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1

    int8_t                      fs540_2nd_tri_fold_all;                     ///< FS540 all paper size second tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_2nd_tri_fold_a4_sef;                  ///< FS540 A4 SEF second tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_2nd_tri_fold_8_5x11_sef;              ///< FS540 8.5x11 SEF second tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1
    int8_t                      fs540_2nd_tri_fold_16k_sef;                 ///< FS540 16K SEF second tri-fold positon adjustment(-100 ~ +100), unit:0.1mm, step:1

    int8_t                      fs540_tri_fold_max;                         ///< FS540 tri-fold max copies(1~3)
    int8_t                      fs540_z_fold_max;                           ///< FS540 z fold max once execute, option: 50, 40, 30, 20

    int8_t                      fs540_tri_fold_output_setting;              ///< FS540 tri-fold output settting(0-normal, 1-Out-of-machine discharge)
    int8_t                      fs540_center_staple_output_setting;         ///< FS540 tri-fold output settting(0-normal, 1-Separated discharge, 2-Out-of-machine discharge)
}ENG_MAINTENANCE_FINISHER_540_ADJUST_S, *ENG_MAINTENANCE_FINISHER_540_ADJUST_P;

/**
 * @brief km engine maintenance calibration value struct
 *
 */
typedef struct STEngMaintenanceCaliValue
{
    int16_t                     front_x_c;                                  ///< front x-axis c calibration value
    int16_t                     front_x_m;                                  ///< front x-axis m calibration value
    int16_t                     front_x_y;                                  ///< front x-axis y calibration value
    int16_t                     front_y_c;                                  ///< front y-axis c calibration value
    int16_t                     front_y_m;                                  ///< front y-axis m calibration value
    int16_t                     front_y_y;                                  ///< front y-axis y calibration value
    int16_t                     back_x_c;                                   ///< back x-axis c calibration value
    int16_t                     back_x_m;                                   ///< back x-axis m calibration value
    int16_t                     back_x_y;                                   ///< back x-axis y calibration value
    int16_t                     back_y_c;                                   ///< back y-axis c calibration value
    int16_t                     back_y_m;                                   ///< back y-axis m calibration value
    int16_t                     back_y_y;                                   ///< back y-axis y calibration value
    int16_t                     skew_initial_value_c;                       ///< initial skew c value
    int16_t                     skew_initial_value_m;                       ///< initial skew m value
    int16_t                     skew_initial_value_y;                       ///< initial skew y value
    int16_t                     skew_adjust_value_c;                        ///< after adjustment skew c value
    int16_t                     skew_adjust_value_m;                        ///< after adjustment skew m value
    int16_t                     skew_adjust_value_y;                        ///< after adjustment skew y value
    int16_t                     skew_move_value_c;                          ///< skew c move value
    int16_t                     skew_move_value_m;                          ///< skew m move value
    int16_t                     skew_move_value_y;                          ///< skew y move value
}ENG_MAINTENANCE_CALI_VALUE_S, *ENG_MAINTENANCE_CALI_VALUE_P;

/**
 * @brief km engine maintenance background voltage struct
 *
 */
typedef struct STEngMaintenanceBackgroundVoltage
{
    int8_t                      back_vol_c;                                 ///< background voltage C color(-5 ~ +5), step:1
    int8_t                      back_vol_m;                                 ///< background voltage M color(-5 ~ +5), step:1
    int8_t                      back_vol_y;                                 ///< background voltage Y color(-5 ~ +5), step:1
    int8_t                      back_vol_k;                                 ///< background voltage K color(-5 ~ +5), step:1
}ENG_MAINTENANCE_BACKGROUND_VOLTAGE_S, *ENG_MAINTENANCE_BACKGROUND_VOLTAGE_P;

/**
 * @brief km engine maintenance neutralizing voltage struct
 *
 */
typedef struct STEngMaintenanceNeutralizingVoltage
{
    int8_t                      neutralizing_vol_1st_switch;                ///< neutralizing voltage first page auto switch(0-OFF,1-ON)
    int8_t                      neutralizing_vol_1st_page;                  ///< neutralizing voltage first page(-3 ~ +3), step:1
    int8_t                      neutralizing_vol_2nd_switch;                ///< neutralizing voltage second page auto switch(0-OFF,1-ON)
    int8_t                      neutralizing_vol_2nd;                       ///< neutralizing voltage second page(-3 ~ +3), step:1
}ENG_MAINTENANCE_NEUTRALIZING_VOLTAGE_S, *ENG_MAINTENANCE_NEUTRALIZING_VOLTAGE_P;

/**
 * @brief km engine maintenance transfer output struct
 *
 */
typedef struct STEngMaintenanceTransferOutput
{
    int8_t                      transfer_1st_c;                                         ///< 1st transfer output C color(-8 ~ +7), step:1
    int8_t                      transfer_1st_m;                                         ///< 1st transfer output M color(-8 ~ +7), step:1
    int8_t                      transfer_1st_y;                                         ///< 1st transfer output Y color(-8 ~ +7), step:1
    int8_t                      transfer_1st_k;                                         ///< 1st transfer output K color(-8 ~ +7), step:1

    int8_t                      transfer_2nd_front_600dpi_plain_color_switch;           ///< 2nd transfer output front page 600dpi plain paper color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_plain_color;                  ///< 2nd transfer output front page 600dpi plain paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_plain_mono_switch;            ///< 2nd transfer output front page 600dpi plain paper mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_plain_mono;                   ///< 2nd transfer output front page 600dpi plain paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_plain_plus_color_switch;      ///< 2nd transfer output front page 600dpi plain plus color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_plain_plus_color;             ///< 2nd transfer output front page 600dpi plain plus paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_plain_plus_mono_switch;       ///< 2nd transfer output front page 600dpi plain plus mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_plain_plus_mono;              ///< 2nd transfer output front page 600dpi plain plus paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_thick_1_color_switch;         ///< 2nd transfer output front page 600dpi thick 1 color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_thick_1_color;                ///< 2nd transfer output front page 600dpi thick 1 paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_thick_1_mono_switch;          ///< 2nd transfer output front page 600dpi thick 1 mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_thick_1_mono;                 ///< 2nd transfer output front page 600dpi thick 1 paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_thick_1plus_color_switch;     ///< 2nd transfer output front page 600dpi thick 1+ color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_thick_1plus_color;            ///< 2nd transfer output front page 600dpi thick 1 plus paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_thick_1plus_mono_switch;      ///< 2nd transfer output front page 600dpi thick 1+ mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_thick_1plus_mono;             ///< 2nd transfer output front page 600dpi thick 1 plus paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_thick_2_switch;               ///< 2nd transfer output front page 600dpi thick 2 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_thick_2;                      ///< 2nd transfer output front page 600dpi thick 2(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_thick_3_switch;               ///< 2nd transfer output front page 600dpi thick 3 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_thick_3;                      ///< 2nd transfer output front page 600dpi thick 3(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_postcard_switch;              ///< 2nd transfer output front page 600dpi postcard switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_postcard;                     ///< 2nd transfer output front page 600dpi postcard(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_envelope_switch;              ///< 2nd transfer output front page 600dpi envelope switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_envelope;                     ///< 2nd transfer output front page 600dpi envelope(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_ohp_switch;                   ///< 2nd transfer output front page 600dpi OHP switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_ohp;                          ///< 2nd transfer output front page 600dpi OHP(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_600dpi_plain_smooth_switch;          ///< 2nd transfer output front page 600dpi plain paper smooth switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_600dpi_plain_smooth;                 ///< 2nd transfer output front page 600dpi plain paper smooth(-8 ~ +7), step:1

    int8_t                      transfer_2nd_back_600dpi_plain_color_switch;            ///< 2nd transfer output back page 600dpi plain paper color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_plain_color;                   ///< 2nd transfer output back page 600dpi plain paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_plain_mono_switch;             ///< 2nd transfer output back page 600dpi plain paper mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_plain_mono;                    ///< 2nd transfer output back page 600dpi plain paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_plain_plus_color_switch;       ///< 2nd transfer output back page 600dpi plain plus color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_plain_plus_color;              ///< 2nd transfer output back page 600dpi plain plus paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_plain_plus_mono_switch;        ///< 2nd transfer output back page 600dpi plain plus mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_plain_plus_mono;               ///< 2nd transfer output back page 600dpi plain plus paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_thick_1_switch;                ///< 2nd transfer output back page 600dpi thick 1 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_thick_1;                       ///< 2nd transfer output back page 600dpi thick 1(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_thick_1plus_switch;            ///< 2nd transfer output back page 600dpi thick 1+ switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_thick_1plus;                   ///< 2nd transfer output back page 600dpi thick 1 plus(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_thick_2_switch;                ///< 2nd transfer output back page 600dpi thick 2 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_thick_2;                       ///< 2nd transfer output back page 600dpi thick 2(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_thick_3_switch;                ///< 2nd transfer output back page 600dpi thick 3 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_thick_3;                       ///< 2nd transfer output back page 600dpi thick 3(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_600dpi_postcard_switch;               ///< 2nd transfer output back page 600dpi postcard switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_600dpi_postcard;                      ///< 2nd transfer output back page 600dpi postcard(-8 ~ +7), step:1

    int8_t                      transfer_2nd_front_1200dpi_plain_color_switch;          ///< 2nd transfer output front page 1200dpi plain paper color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_plain_color;                 ///< 2nd transfer output front page 1200dpi plain paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_plain_mono_switch;           ///< 2nd transfer output front page 1200dpi plain paper mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_plain_mono;                  ///< 2nd transfer output front page 1200dpi plain paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_plain_plus_color_switch;     ///< 2nd transfer output front page 1200dpi plain plus color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_plain_plus_color;            ///< 2nd transfer output front page 1200dpi plain plus paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_plain_plus_mono_switch;      ///< 2nd transfer output front page 1200dpi plain plus mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_plain_plus_mono;             ///< 2nd transfer output front page 1200dpi plain plus paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_thick_1_color_switch;        ///< 2nd transfer output front page 1200dpi thick 1 color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_thick_1_color;               ///< 2nd transfer output front page 1200dpi thick 1 color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_thick_1_mono_switch;         ///< 2nd transfer output front page 1200dpi thick 1 mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_thick_1_mono;                ///< 2nd transfer output front page 1200dpi thick 1 mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_thick_1plus_color_switch;    ///< 2nd transfer output front page 1200dpi thick 1+ color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_thick_1plus_color;           ///< 2nd transfer output front page 1200dpi thick 1 plus color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_thick_1plus_mono_switch;     ///< 2nd transfer output front page 1200dpi thick 1+ mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_thick_1plus_mono;            ///< 2nd transfer output front page 1200dpi thick 1 plus mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_thick_2_switch;              ///< 2nd transfer output front page 1200dpi thick 2 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_thick_2;                     ///< 2nd transfer output front page 1200dpi thick 2(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_thick_3_switch;              ///< 2nd transfer output front page 1200dpi thick 3 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_thick_3;                     ///< 2nd transfer output front page 1200dpi thick 3(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_postcard_switch;             ///< 2nd transfer output front page 1200dpi postcard switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_postcard;                    ///< 2nd transfer output front page 1200dpi postcard(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_envelope_switch;             ///< 2nd transfer output front page 1200dpi envelope switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_envelope;                    ///< 2nd transfer output front page 1200dpi envelope(-8 ~ +7), step:1
    int8_t                      transfer_2nd_front_1200dpi_ohp_switch;                  ///< 2nd transfer output front page 1200dpi OHP switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_front_1200dpi_ohp;                         ///< 2nd transfer output front page 1200dpi OHP(-8 ~ +7), step:1

    int8_t                      transfer_2nd_back_1200dpi_plain_color_switch;           ///< 2nd transfer output back page 1200dpi plain paper color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_plain_color;                  ///< 2nd transfer output back page 1200dpi plain paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_plain_mono_switch;            ///< 2nd transfer output back page 1200dpi plain paper mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_plain_mono;                   ///< 2nd transfer output back page 1200dpi plain paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_plain_plus_color_switch;      ///< 2nd transfer output back page 1200dpi plain plus color switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_plain_plus_color;             ///< 2nd transfer output back page 1200dpi plain plus paper color(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_plain_plus_mono_switch;       ///< 2nd transfer output back page 1200dpi plain plus mono switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_plain_plus_mono;              ///< 2nd transfer output back page 1200dpi plain plus paper mono(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_thick_1_switch;               ///< 2nd transfer output back page 1200dpi thick 1 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_thick_1;                      ///< 2nd transfer output back page 1200dpi thick 1(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_thick_1plus_switch;           ///< 2nd transfer output back page 1200dpi thick 1+ switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_thick_1plus;                  ///< 2nd transfer output back page 1200dpi thick 1 plus(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_thick_2_switch;               ///< 2nd transfer output back page 1200dpi thick 2 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_thick_2;                      ///< 2nd transfer output back page 1200dpi thick 2(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_thick_3_switch;               ///< 2nd transfer output back page 1200dpi thick 3 switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_thick_3;                      ///< 2nd transfer output back page 1200dpi thick 3(-8 ~ +7), step:1
    int8_t                      transfer_2nd_back_1200dpi_postcard_switch;              ///< 2nd transfer output back page 1200dpi postcard switch(0-OFF,1-ON)
    int8_t                      transfer_2nd_back_1200dpi_postcard;                     ///< 2nd transfer output back page 1200dpi postcard(-8 ~ +7), step:1
}ENG_MAINTENANCE_TRANSFER_OUTPUT_S, *ENG_MAINTENANCE_TRANSFER_OUTPUT_P;

/**
 * @brief km engine maintenance charge AC output struct
 *
 */
typedef struct STEngMaintenanceChargeAcOutput
{
    int8_t                      output_c;                                     ///< charge AC output C color(-12 ~ +8), step:1
    int8_t                      output_m;                                     ///< charge AC output M color(-12 ~ +8), step:1
    int8_t                      output_y;                                     ///< charge AC output Y color(-12 ~ +8), step:1
    int8_t                      output_k;                                     ///< charge AC output K color(-12 ~ +8), step:1
}ENG_MAINTENANCE_CHARGE_AC_OUTPUT_S, *ENG_MAINTENANCE_CHARGE_AC_OUTPUT_P;

/**
 * @brief km engine maintenance development AC voltage choice struct
 *
 */
typedef struct STEngMaintenanceDvAcChoice
{
    uint8_t                     choice;                                     ///< 0-Normal Mode(OFF), 1-High altitude white spots Mode(ON)
}ENG_MAINTENANCE_DV_AC_CHOICE_S, *ENG_MAINTENANCE_DV_AC_CHOICE_P;

/**
 * @brief km engine maintenance black development AC frequency change choice struct
 *
 */
typedef struct STEngMaintenanceDvAcFreqChoice
{
    uint8_t                     choice;                                     ///< 0-standard, 1-Edge reduction, 2- Graininess priority
}ENG_MAINTENANCE_DV_AC_FREQ_CHOICE_S, *ENG_MAINTENANCE_DV_AC_FREQ_CHOICE_P;

/**
 * @brief km engine maintenance tcr toner supply struct
 *
 */
typedef struct STEngMaintenanceTonerSupplyChoice
{
    uint8_t                     color_select;                               ///< select color to supply(bit0-c,bit 1-m,bit2-y,bit3-k)
}ENG_MAINTENANCE_TONER_SUPPLY_S, *ENG_MAINTENANCE_TONER_SUPPLY_P;

/**
 * @brief km engine maintenance manual tray max or min width adjustment struct
 *
 */
typedef struct STEngMaintenanceManualTrayAdjust
{
    uint8_t                     max_or_min;                               ///< select max or min width adjustment(bit0-max,bit 1-min)
}ENG_MAINTENANCE_MANUAL_TRAY_ADJUST_S, *ENG_MAINTENANCE_MANUAL_TRAY_ADJUST_P;

/**
 * @brief km engine maintenance new unit confirm struct
 *
 */
typedef struct STEngMaintenanceNewUnitConfirm
{
    uint8_t                     fusing_unit;                              ///< fusing unit new unit detection confirm(0-normal, 1-new unit)
    uint8_t                     transfer_belt_unit;                       ///< transfer belt unit new unit detection confirm(0-normal, 1-new unit)
    uint8_t                     toner_filter_unint;                       ///< toner filter unit new unit detection confirm(0-normal, 1-new unit)
}ENG_MAINTENANCE_NEW_UNIT_CONFIRM_S, *ENG_MAINTENANCE_NEW_UNIT_CONFIRM_P;

/**
 * @brief km engine maintenance ld adjustment struct
 *
 */
typedef struct STEngMaintenanceLdAdjust
{
    int16_t                     ld_relative_position_c;                   ///< c ld relative position adjustment(-265 ~ +265), step:1, unit:0.125dots
    int16_t                     ld_relative_position_m;                   ///< m ld relative position adjustment(-265 ~ +265), step:1, unit:0.125dots
    int16_t                     ld_relative_position_y;                   ///< y ld relative position adjustment(-265 ~ +265), step:1, unit:0.125dots
    int16_t                     ld_relative_position_k;                   ///< k ld relative position adjustment(-265 ~ +265), step:1, unit:0.125dots

    int8_t                      ld_light_c;                               ///< c 2 beam ld light adjustment(-10 ~ +10), step:1
    int8_t                      ld_light_m;                               ///< m 2 beam ld light adjustment(-10 ~ +10), step:1
    int8_t                      ld_light_y;                               ///< y 2 beam ld light adjustment(-10 ~ +10), step:1
    int8_t                      ld_light_k;                               ///< k 2 beam ld light a  djustment(-10 ~ +10), step:1

    uint8_t                     ld_light_width;                           ///< laser output width setting(+0 ~ +6), step:1
}ENG_MAINTENANCE_LD_ADJUST_S, *ENG_MAINTENANCE_LD_ADJUST_P;

/**
 * @brief km engine maintenance paper feed direction adjustment struct
 *
 */
typedef struct STEngMaintenancePaperFeedDirectionAdjust
{
    int8_t                      sub_scan_magnification_plain;             ///< plain paper sub(slow) scan magnification fine tuning(-7 ~ +7), step:1
    int8_t                      sub_scan_magnification_thick1_1plus;      ///< thick 1 & thick 1+ sub(slow) scan magnification fine tuning(-7 ~ +7), step:1
    int8_t                      sub_scan_magnification_thick2_1plus;      ///< thick 2 sub(slow) scan magnification fine tuning(-7 ~ +7), step:1
    int8_t                      sub_scan_magnification_thick3_envelope;   ///< thick 3 & envelope sub(slow) scan magnification fine tuning(-7 ~ +7), step:1
}ENG_MAINTENANCE_PAPER_FEED_DIRECTION_ADJUST_S, *ENG_MAINTENANCE_PAPER_FEED_DIRECTION_ADJUST_P;

/**
 * @brief km engine maintenance paper separation adjustment struct
 *
 */
typedef struct STEngMaintenancePaperSeparationAdjust
{
    int16_t                     second_transfer_output_timing_side1;      ///< 2nd transfer output timing 1st side adjustment value(-100 ~ +100), step:1, unit:0.1mm
    int16_t                     second_transfer_output_timing_side2;      ///< 2nd transfer output timing 2nd side adjustment value(-100 ~ +100), step:1, unit:0.1mm
}ENG_MAINTENANCE_PAPER_SEPARATION_ADJUST_S, *ENG_MAINTENANCE_PAPER_SEPARATION_ADJUST_P;


/**
 * @brief km engine maintenance paper feed test struct
 *
 */
typedef struct STEngMaintenanceFeedTest
{
    TRAY_INPUT_E                tray_in;                                  ///< tray in choice
    TRAY_RECEIVE_E              tray_out;                                 ///< tray out choice
}ENG_MAINTENANCE_FEED_TEST_S, *ENG_MAINTENANCE_FEED_TEST_P;

/**
 * @brief panel reuqest to enter maintenance mode
 * @author: madechang
 */
void panel_request_enter_maintenance_mode( void );

/**
 * @brief panel reuqest to exit maintenance mode
 * @author: madechang
 */
void panel_request_exit_maintenance_mode( void );


/**
 * @brief panel reuqest to get engine config for maintenance mode
 * @author: madechang
 */
void panel_get_engine_data_init();

/**
 * @brief panel reuqest to set fuser temp
 * @author: madechang
 */
void panel_set_fuser_temp_value(  );

void panel_maintenance_mode_prolog();

void panel_maintenance_mode_lock();

void panel_maintenance_mode_unlock();


void panel_get_function_msg( MAINTENANCE_FUNCTION_TYPE_E func_type, MAINTENANCE_ACTION_TYPE_E reply, void* data );

void engine_reply_get_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply );

void panel_set_function_msg( MAINTENANCE_FUNCTION_TYPE_E func_type, MAINTENANCE_ACTION_TYPE_E reply, void* data );

void engine_reply_get_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply );

void engine_reply_set_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply );

void engine_reply_check_func_msg( MAINTENANCE_FUNCTION_TYPE_E func, MAINTENANCE_ACTION_TYPE_E reply );

void panel_request_execute_func(  uint16_t panel_dc_cmd, void* data, uint32_t data_len);

#endif /* _PANEL_CONFIG_H */

/**
 *@}
 */


