/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file event_mgr.c
 * @addtogroup event_manager
 * @{
 * <AUTHOR>
 * @date 2023-07-13
 * @brief event manager client API
 */
#include <sys/types.h>
#include <sys/socket.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>

#include "pol/pol_types.h"
#include "pol/pol_define.h"
#include "pol/pol_string.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"
#include "pol/pol_io.h"
#include "cbinder/cbinder.h"

#include "event_mgr_typedef.h"
#include "event_mgr_private.h"

struct evt_mgr_cli
{
    struct binder_state* bs;
    void*     srv;
    uint32_t  module_id;
    int32_t   read_fd;
    int32_t   write_fd;
    pthread_t thread_id;
    void*     callback_ctx;
    void    (*callback)(const EVT_MSG_S *, void *);
};

static uint32_t s_token;

#define EVT_CLIENT_INIT_FD(ptr)                                                     \
{                                                                                   \
    int32_t tmp_fd[2];                                                              \
    int32_t tmp_flags;                                                              \
    if (socketpair(AF_UNIX, SOCK_STREAM, 0, tmp_fd) == -1) {                        \
        ret = ERR_EVT_MGR_SOCKETPAIR;                                               \
        break;                                                                      \
    }                                                                               \
    (ptr)->read_fd  = tmp_fd[0];                                                    \
    (ptr)->write_fd = tmp_fd[1];                                                    \
    tmp_flags = fcntl((ptr)->read_fd, F_GETFL, 0);                                  \
    tmp_flags |= O_NONBLOCK;                                                        \
    fcntl((ptr)->read_fd, F_SETFL, tmp_flags);                                      \
}

#define EVT_CLIENT_INIT_BS(ptr)                                                     \
{                                                                                   \
    (ptr)->bs = binder_start(1);                                                    \
    if ((ptr)->bs == NULL) {                                                        \
        ret = ERR_EVT_MGR_BINDER_INIT;                                              \
        break;                                                                      \
    }                                                                               \
    (ptr)->srv = svcmgr_get_service((ptr)->bs, BINDER_SERVICE_MANAGER, "pantum.event.manager"); \
    if ((ptr)->srv == NULL) {                                                       \
        ret = ERR_EVT_MGR_BINDER_INIT;                                              \
        break;                                                                      \
    }                                                                               \
}

#define EVT_CLIENT_RELEASE(ptr)                                                     \
{                                                                                   \
    if ((ptr) != NULL) {                                                            \
        if ((ptr)->bs != NULL) {                                                    \
            binder_stop((ptr)->bs);                                                 \
        }                                                                           \
        if ((ptr)->read_fd != -1) {                                                 \
            pi_close((ptr)->read_fd);                                               \
        }                                                                           \
        if ((ptr)->write_fd != -1) {                                                \
            pi_close((ptr)->write_fd);                                              \
        }                                                                           \
        pi_free(ptr);                                                               \
    }                                                                               \
}

#define EVT_ERR(fmt, ...)   printf("[ERROR][%s]" fmt "\n", __func__, ##__VA_ARGS__)

static int32_t recv_msg(int32_t fd, EVT_MSG_S* msg)
{
    int32_t ret = -1;
    ssize_t len;

    do
    {
        len = read(fd, &msg->module_id, sizeof(msg->module_id));
        BREAK_IF(len != sizeof(msg->module_id), EVT_ERR);

        len = read(fd, &msg->event_type, sizeof(msg->event_type));
        BREAK_IF(len != sizeof(msg->event_type), EVT_ERR);

        len = read(fd, &msg->data_length, sizeof(msg->data_length));
        BREAK_IF(len != sizeof(msg->data_length), EVT_ERR);

        if ( msg->data_length == 0xFFFFFFFF )
        {
            msg->data_length = 0;
            msg->data = NULL;
        }
        else if ( msg->data_length == 0 )
        {
            msg->data = pi_zalloc(0);
        }
        else
        {
            msg->data = pi_zalloc(msg->data_length + 1);
            BREAK_IF(msg->data == NULL, EVT_ERR);

            len = read(fd, msg->data, msg->data_length);
            BREAK_IF(len != msg->data_length, EVT_ERR);
        }

        ret = 0;
    }
    while ( 0 );

    return ret;
}

static void* event_monitor(void* arg)
{
    EVT_MGR_CLI_S*  thiz = (EVT_MGR_CLI_S *)arg;
    EVT_MSG_S*      msg;
    fd_set          fds;
    int32_t         ret;

    pthread_setcancelstate(PTHREAD_CANCEL_ENABLE, NULL);
    pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS, NULL);

    RETURN_VAL_IF(thiz == NULL, EVT_ERR, NULL);

    FD_ZERO(&fds);
    FD_SET(thiz->read_fd, &fds);

    while ( 1 )
    {
        ret = select(thiz->read_fd + 1, &fds, NULL, NULL, NULL);
        if ( ret <= 0 )
        {
            EVT_ERR("select failed: %d<%s> event_monitor(%u) exit!!!", ret, strerror(errno), thiz->module_id);
            break;
        }

        if ( FD_ISSET(thiz->read_fd, &fds) )
        {

            msg = (EVT_MSG_S *)pi_zalloc(sizeof(EVT_MSG_S));
            if ( msg == NULL )
            {
                EVT_ERR("alloc EVT_MSG_S failed: %d<%s>", errno, strerror(errno));
                break;
            }

            if ( recv_msg(thiz->read_fd, msg) == 0 )
            {
                thiz->callback(msg, thiz->callback_ctx);
            }

            if ( msg->data != NULL )
            {
                pi_free(msg->data);
            }
            pi_free(msg);
        }
    }

    return NULL;
}

EVT_MGR_CLI_S* pi_event_mgr_create_client(uint32_t module_id, void (*event_callback)(const EVT_MSG_S *, void *), void* callback_ctx, int32_t* ret_val)
{
    EVT_MGR_CLI_S*      thiz = NULL;
    struct binder_io    msg, reply;
    size_t              iodata[1024];
    int32_t             ret = 0;

    do
    {
        if ( module_id >= EVT_MODULE_ENUM_MAX )
        {
            EVT_ERR("module(%u) invalid", module_id);
            ret = ERR_EVT_MGR_INVALID_PARAM;
            break;
        }

        thiz = (EVT_MGR_CLI_S*)pi_zalloc(sizeof(EVT_MGR_CLI_S));
        if ( thiz == NULL )
        {
            EVT_ERR("module(%u) alloc EVT_MGR_CLI_S failed: %d<%s>", module_id, errno, strerror(errno));
            ret = ERR_EVT_MGR_MEMORY;
            break;
        }

        thiz->module_id = module_id;
        EVT_CLIENT_INIT_FD(thiz);
        EVT_CLIENT_INIT_BS(thiz);

        bio_init(&msg, iodata, sizeof(iodata), 4);
        bio_put_obj(&msg, &s_token);
        bio_put_uint32(&msg, thiz->module_id);
        bio_put_fd(&msg, thiz->write_fd);
        ret = binder_call(thiz->bs, &msg, &reply, thiz->srv, EVENT_MGR_CMD_CONNECT);
        if ( ret != 0 )
        {
            EVT_ERR("module(%u) binder_call failed(%d)", module_id, ret);
            ret = ERR_EVT_MGR_BINDER_CALL;
            break;
        }

        ret = (int32_t)bio_get_uint32(&reply);
        binder_done(thiz->bs, &msg, &reply);
        if ( ret != 0 )
        {
            EVT_ERR("module(%u) connect failed(%d)", module_id, ret);
            break;
        }

        thiz->callback = event_callback;
        if ( thiz->callback != NULL )
        {
            thiz->callback_ctx = callback_ctx;
            pthread_create(&thiz->thread_id, NULL, event_monitor, thiz);
        }

    }
    while ( 0 );

    if ( ret_val != NULL )
    {
        *ret_val = ret;
    }
    if ( ret != 0 )
    {
        EVT_ERR("module(%u) create client failed(%d)", module_id, ret);
        EVT_CLIENT_RELEASE(thiz);
        thiz = NULL;
    }
    return thiz;
}

void pi_event_mgr_destroy_client(EVT_MGR_CLI_S* thiz)
{
    struct binder_io    msg, reply;
    size_t              iodata[1024];
    int32_t             ret;

    RETURN_IF(thiz == NULL, EVT_ERR);

    if ( thiz->callback != NULL && thiz->thread_id > 0 )
    {
        pthread_cancel(thiz->thread_id);
        pthread_join(thiz->thread_id, NULL);
    }

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, thiz->module_id);
    ret = binder_call(thiz->bs, &msg, &reply, thiz->srv, EVENT_MGR_CMD_DISCONNECT);
    if ( ret != 0 )
    {
        EVT_ERR("module(%u) binder_call failed(%d)", thiz->module_id, ret);
    }
    else
    {
        bio_get_uint32(&reply);
        binder_done(thiz->bs, &msg, &reply);
    }
    EVT_CLIENT_RELEASE(thiz);
}

int32_t pi_event_mgr_register(EVT_MGR_CLI_S* thiz, uint32_t* event_array, size_t event_count)
{
    struct binder_io    msg, reply;
    char*               iodata = NULL;
    uint32_t            iolen = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(thiz == NULL || event_array == NULL || event_count == 0, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);
    RETURN_VAL_IF(thiz->callback == NULL, EVT_ERR, ERR_EVT_MGR_UNSUPPORD_REGISTER);

    iolen = event_count * sizeof(event_array[0]) + 32;
    iodata = (char *)pi_malloc(iolen);
    RETURN_VAL_IF(iodata == NULL, EVT_ERR, ERR_EVT_MGR_MEMORY);

    bio_init(&msg, iodata, iolen, 4);
    bio_put_uint32(&msg, thiz->module_id);
    bio_put_buffer(&msg, (char *)event_array, event_count * sizeof(uint32_t));
    ret = binder_call(thiz->bs, &msg, &reply, thiz->srv, EVENT_MGR_CMD_REGISTER);
    if ( ret != 0 )
    {
        EVT_ERR("module(%u) binder_call failed(%d)", thiz->module_id, ret);
        ret = ERR_EVT_MGR_BINDER_CALL;
    }
    else
    {
        ret = (int32_t)bio_get_uint32(&reply);
        binder_done(thiz->bs, &msg, &reply);
    }
    pi_free(iodata);

    return ret;
}

int32_t pi_event_mgr_unregister(EVT_MGR_CLI_S* thiz, uint32_t* event_array, size_t event_count)
{
    struct binder_io    msg, reply;
    char*               iodata = NULL;
    uint32_t            iolen = 0;
    int32_t             ret = 0;

    RETURN_VAL_IF(thiz == NULL || event_array == NULL || event_count == 0, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);
    RETURN_VAL_IF(thiz->callback == NULL, EVT_ERR, ERR_EVT_MGR_UNSUPPORD_REGISTER);

    iolen = event_count * sizeof(event_array[0]) + 32;
    iodata = (char *)pi_malloc(iolen);
    RETURN_VAL_IF(iodata == NULL, EVT_ERR, ERR_EVT_MGR_MEMORY);

    bio_init(&msg, iodata, iolen, 4);
    bio_put_uint32(&msg, thiz->module_id);
    bio_put_buffer(&msg, (char *)event_array, event_count * sizeof(uint32_t));
    ret = binder_call(thiz->bs, &msg, &reply, thiz->srv, EVENT_MGR_CMD_UNREGISTER);
    if ( ret != 0 )
    {
        EVT_ERR("module(%u) binder_call failed(%d)", thiz->module_id, ret);
        ret = ERR_EVT_MGR_BINDER_CALL;
    }
    else
    {
        ret = (int32_t)bio_get_uint32(&reply);
        binder_done(thiz->bs, &msg, &reply);
    }
    pi_free(iodata);

    return ret;
}

int32_t pi_event_mgr_notify(EVT_MGR_CLI_S* thiz, uint32_t event_type, const void* data, uint32_t data_length)
{
    struct binder_io    msg, reply;
    char*               buffer = NULL;
    char*               iodata = NULL;
    uint32_t            offset = 0;
    uint32_t            buflen;
    uint32_t            iolen;
    int32_t             ret;

    RETURN_VAL_IF(thiz == NULL, EVT_ERR, ERR_EVT_MGR_INVALID_PARAM);

    do
    {
        buflen = sizeof(thiz->module_id) + sizeof(event_type) + sizeof(data_length) + data_length;
        buffer = (char *)pi_malloc(buflen);
        if ( buffer == NULL )
        {
            EVT_ERR("alloc buffer failed: %d<%s>", errno, strerror(errno));
            ret = ERR_EVT_MGR_MEMORY;
            break;
        }

        if ( data == NULL )
        {
            data_length = 0xFFFFFFFF; /* 参数为NULL时，将长度置为-1，以区分data为空字符串的情况 */
        }

        iolen = buflen + 32;
        iodata = (char *)pi_malloc(iolen);
        if ( iodata == NULL )
        {
            EVT_ERR("alloc iodata failed: %d<%s>", errno, strerror(errno));
            ret = ERR_EVT_MGR_MEMORY;
            break;
        }

        pi_memcpy(buffer + offset, &thiz->module_id, sizeof(thiz->module_id));
        offset += sizeof(thiz->module_id);
        pi_memcpy(buffer + offset, &event_type, sizeof(event_type));
        offset += sizeof(event_type);
        pi_memcpy(buffer + offset, &data_length, sizeof(data_length));
        if ( data != NULL && data_length > 0 )
        {
            offset += sizeof(data_length);
            pi_memcpy(buffer + offset, data, data_length);
        }

        bio_init(&msg, iodata, iolen, 4);
        bio_put_uint32(&msg, thiz->module_id);
        bio_put_uint32(&msg, event_type);
        bio_put_buffer(&msg, buffer, buflen);
        ret = binder_call(thiz->bs, &msg, &reply, thiz->srv, EVENT_MGR_CMD_NOTIFY);
        if ( ret != 0 )
        {
            EVT_ERR("module(%u) binder_call failed(%d)", thiz->module_id, ret);
            ret = ERR_EVT_MGR_BINDER_CALL;
            break;
        }

        ret = (int32_t)bio_get_uint32(&reply);
        binder_done(thiz->bs, &msg, &reply);
    }
    while ( 0 );

    if ( iodata )
    {
        pi_free(iodata);
    }
    if ( buffer )
    {
        pi_free(buffer);
    }
    return ret;
}
/**
 *@}
 */
