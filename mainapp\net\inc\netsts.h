/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netsts.h
 * @addtogroup net
 * @{
 * @addtogroup netsts
 * <AUTHOR>
 * @date 2023-8-19
 * @brief system status manager API in NET module
 */
#ifndef __NETSTS_H__
#define __NETSTS_H__

PT_BEGIN_DECLS

#define NETSTS_MAX_NUM      30

typedef struct
{
    uint32_t        status_id;
    int32_t         parameter1;
    int32_t         parameter2;
    int32_t         parameter3;
}
NETSTS_UNIT_S;

typedef struct
{
    NETSTS_UNIT_S   array[NETSTS_MAX_NUM];
    size_t          count;
}
NETSTS_PACKET_S;

void        netsts_save_packet          (NETSTS_UNIT_S* sts, size_t num);

int32_t     netsts_take_packet          (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_module_error   (NETSTS_PACKET_S* packet, uint32_t module);

uint32_t    netsts_check_status_level   (NETSTS_PACKET_S* packet, uint32_t level);

uint32_t    netsts_check_scan_running   (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_trayin_missing (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_trayout_full   (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_cover_open     (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_paper_empty    (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_paper_jam      (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_toner_empty    (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_toner_low      (NETSTS_PACKET_S* packet);

uint32_t    netsts_check_waste_toner    (NETSTS_PACKET_S* packet);

uint32_t    netsts_get_top_type(NETSTS_PACKET_S* packet);

uint32_t    netsts_check_trayin_near_empty(NETSTS_PACKET_S* packet);

PT_END_DECLS

#endif /* __NETSTS_H__ */
/**
 *@}
 */
