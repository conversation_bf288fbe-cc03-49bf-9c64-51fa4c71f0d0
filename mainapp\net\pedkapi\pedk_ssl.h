/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pedk_ssl.h
 * @pedk net
 * @{
 * @ssl netmodules
 * <AUTHOR>
 * @date 2024-6-11
 * @brief pedk ssl function for pedk
 */
#ifndef _PEDK_SSL_H_
#define _PEDK_SSL_H_

#include <openssl/bio.h>
#include <openssl/ossl_typ.h>
#include <openssl/ssl.h>
#include <openssl/err.h>
#include "nettls.h"

#define              CERT_PATH_LEN           40
#define              CERT_CONTENT_LEN        4096
#define              KEY_CONTENT_LEN         256

typedef struct load_info
{
    char             keypath[CERT_PATH_LEN];
    char             certpath[CERT_PATH_LEN];
    char             password[24];
}
PEDK_SSL_LOAD_INFO_S;

typedef struct ssl_connect_info
{
    char             keypath[CERT_PATH_LEN];
    char             certpath[CERT_PATH_LEN];
    char             hostname[16];
    char             password[24];
    int              port;
}
PEDK_SSL_CONNECT_INFO_S;

typedef struct ssl_socket_info
{
    char             ssl_data[1024];
    char             sslcon_hostname[16];
    int              sslcon_port;
}
PEDK_SSL_CONNECT_DATA_S;

typedef struct ssl_list
{
    SSL_CTX*         con_ssl_ctx;
    SSL*             con_ssl;
    struct ssl_list* next;
    char             hostname[16];
    int              port;
    int              ssl_sockfd;
}
PEDK_SSL_CONNECT_LIST_S;

// 证书安装结构体
typedef struct {
    char cert_content[CERT_CONTENT_LEN];
    char key_content[CERT_CONTENT_LEN];
    char key_password[KEY_CONTENT_LEN];
    int export_flag;
}PEDK_SSL_CERT_INFO_S;

const char*          pedk_ssl_connect_close(PEDK_SSL_CONNECT_DATA_S* close_data);
int                  pedk_ssl_connect_recv(PEDK_SSL_CONNECT_DATA_S* recv_data);
const char*          pedk_ssl_connect_send(PEDK_SSL_CONNECT_DATA_S* send_data);
const char*          pedk_ssl_create_connection(PEDK_SSL_CONNECT_INFO_S* connect_info);
const char*          pedk_ssl_load_certificate(const char* keypath, const char* certpath, char* password);
const char*          pedk_ssl_make_certificate(TLS_CERT_CONF_S* info);
int                  pedk_ssl_write_certificate(const char *cert_content);
int                  pedk_ssl_write_private_key(const char *key_content, const char *key_password);

#endif
/**
 *@}
 */
