/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_mem.h
 * @addtogroup memory
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal memory interface set
 */
#ifndef __POL_MEM_H__
#define __POL_MEM_H__

#include <sys/mman.h>
#include <stdlib.h>
#include <string.h>
#include <pol/pol_types.h>
#include <pol/pol_define.h>

PT_BEGIN_DECLS

#if defined(CONFIG_POL_MEM_DEBUG)


#if defined(CONFIG_POL_DEBUG_MODE_BACKTRACE)

/**
 * @brief Allocates a block of memory of the specified size using the Pi library's memory allocation mechanism.
 * @param[in] size The size of the memory block to be allocated.
 */
void* pi_malloc(size_t size);

/**
 * @brief Reallocates a previously allocated memory block to a new size using the Pi library's memory reallocation mechanism.
 * @param[in] ptr Pointer to the previously allocated memory block.
 * @param[in] size The new size to which the memory block should be resized.
 */
void* pi_realloc(void *ptr, size_t size);

/**
 * @brief Allocates a block of memory of the specified size and initializes it to zero using the Pi library's memory allocation mechanism.
 * @param[in] size The size of the memory block to be allocated.
 */
void* pi_zalloc(size_t size);

/**
 * @brief Copies a block of memory from the source to the destination using the Pi library's memory copy mechanism.
 * @param[in] s Pointer to the memory block to be filled.
 * @param[in] c The value to be set.
 * @param[in] n The number of bytes to be set.
 */
void* pi_memset(void *s, int c, size_t n);

/**
 * @brief Copies a block of memory from the source to the destination using the Pi library's memory copy mechanism.
 * @param[in] dest Pointer to the destination memory block.
 * @param[in] src Pointer to the source memory block.
 * @param[in] n The number of bytes to be copied.
 */
void* pi_memcpy(void *dest, const void *src, size_t n);

/**
 * @brief Compares two blocks of memory to determine their equality using the Pi library's memory comparison mechanism.
 * @param[in] s1 Pointer to the first memory block.
 * @param[in] s2 Pointer to the second memory block.
 * @param[in] n The number of bytes to be compared.
 */
int pi_memcmp(const void *s1, const void *s2, size_t n);

/**
 * @brief Maps a file or device into memory using the Pi library's memory mapping mechanism.
 * @param[in] addr The desired starting address for the mapping.
 * @param[in] length The length of the mapping.
 * @param[in] prot The desired memory protection for the mapping (e.g., read-only, read-write, etc.).
 * @param[in] flags Additional flags to control the mapping behavior (e.g., MAP_SHARED, MAP_PRIVATE).
 * @param[in] fd The file descriptor of the file to be mapped.
 * @param[in] offset The offset within the file to start the mapping.
 */
void *pi_mmap(void *addr, size_t length, int prot, int flags, int fd, off_t offset);

/**
 * @brief Unmaps a previously mapped memory region using the Pi library's memory unmapping mechanism.
 * @param[in] addr The starting address of the memory region to be unmapped.
 * @param[in] length The length of the memory region to be unmapped.
 */
int pi_munmap(void *addr, size_t length);


#elif defined(CONFIG_POL_DEBUG_MODE_CALLER)

void* _pi_malloc(const char *callfile ,const uint32_t callline, size_t size);
#define pi_malloc(size)             _pi_malloc(__FILE__,__LINE__,size)

void* _pi_realloc(const char *callfile ,const uint32_t callline, void *ptr, size_t size);
#define pi_realloc(ptr,size)             _pi_realloc(__FILE__,__LINE__,ptr,size)

void* _pi_zalloc(const char *callfile ,const uint32_t callline, size_t size);
#define pi_zalloc(size)             _pi_zalloc(__FILE__,__LINE__,size)

void* _pi_memset(const char *callfile ,const uint32_t callline, void *s, int c, size_t n);
#define pi_memset(s,c,n)             _pi_memset(__FILE__,__LINE__,s,c,n)

void* _pi_memcpy(const char *callfile ,const uint32_t callline, void *dest, const void *src, size_t n);
#define pi_memcpy(d,s,n)             _pi_memcpy(__FILE__,__LINE__,d,s,n)

int _pi_memcmp(const char *callfile ,const uint32_t callline, const void *s1, const void *s2, size_t n);
#define pi_memcmp(s1,s2,n)             _pi_memcmp(__FILE__,__LINE__,s1,s2,n)

void *_pi_mmap(const char *callfile ,const uint32_t callline, void *addr, size_t length, int prot, int flags,
        int fd, off_t offset);
#define pi_mmap(a,l,p,fl,fd,off)             _pi_mmap(__FILE__,__LINE__,a,l,p,fl,fd,off)

int _pi_munmap(const char *callfile ,const uint32_t callline, void *addr, size_t length);
#define pi_munmap(addr,length)             _pi_munmap(__FILE__,__LINE__,addr,length)

void _pi_free(const char *callfile ,const uint32_t callline, void *ptr);
#define pi_free(ptr)						{ 	_pi_free(__FILE__,__LINE__,ptr); \
												(ptr) = NULL;\
											}
#endif /* defined(CONFIG_POL_DEBUG_MODE_BACKTRACE) || define(CONFIG_POL_DEBUG_MODE_CALLER) */

#else /* defined(CONFIG_POL_MEM_DEBUG) */

#define pi_malloc       malloc
#define pi_realloc      realloc
#define pi_zalloc(s)    calloc(1, s)
#define pi_free         free
#define pi_memset       memset
#define pi_memcpy       memcpy
#define pi_memcmp       memcmp
#define pi_mmap         mmap
#define pi_munmap       munmap

#endif /* defined(CONFIG_POL_MEM_DEBUG) */

PT_END_DECLS

#endif /* __POL_MEM_H__ */
/**
 *@}
 */
