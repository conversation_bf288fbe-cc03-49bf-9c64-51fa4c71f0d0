/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_log.h
 * @addtogroup log
 * @{
 * <AUTHOR>
 * @date 2023-05-9
 * @version v1.0
 * @brief pantum osal log interface set
 * @details pantum log level from high to low is \n
 * fatal:   fatal error log  ,the program would be abort \n
 * error:   normal error log , don't affect the program running\n
 * warning: warning infomation log \n
 * info:    important information log\n
 * debug:   debug log \n
 * trace:   trace log
 *
 */
#ifndef __POL_LOG_H__
#define __POL_LOG_H__

#include "pol/pol_types.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

/**
 * @brief  log family function produce output according to the format and abort the program ,should be userd with caution
 * @param[in] caller:       caller function name
 * @param[in] format:       specific the output format
 * <AUTHOR>
 * @data   2023-05-08
*/
void    log_fatal           (const char* caller, const char* format, ...);
#define pi_log_f(fmt, ...)  log_fatal(__PRETTY_FUNCTION__, fmt, ## __VA_ARGS__)///< fatal error log ,abort the program ,should be used with caution

/**
 * @brief  log family function produce output according to the format
 * @param[in] caller:       caller function name
 * @param[in] format:       specific the output format
 * <AUTHOR>
 * @data   2023-05-08
*/
void    log_error           (const char* caller, const char* format, ...);
#define pi_log_e(fmt, ...)  log_error(__PRETTY_FUNCTION__, fmt, ## __VA_ARGS__)///< error log

/**
 * @brief  log family function produce output according to the format
 * @param[in] caller:       caller function name
 * @param[in] format:       specific the output format
 * <AUTHOR>
 * @data   2023-05-08
*/
void    log_warn            (const char* caller, const char* format, ...);
#define pi_log_w(fmt, ...)  log_warn(__PRETTY_FUNCTION__, fmt, ## __VA_ARGS__) ///<warning log

/**
 * @brief  log family function produce output according to the format
 * @param[in] caller:       caller function name
 * @param[in] format:       specific the output format
 * <AUTHOR>
 * @data   2023-05-08
*/
void    log_info            (const char* caller, const char* format, ...);
#define pi_log_i(fmt, ...)  log_info(__PRETTY_FUNCTION__, fmt, ## __VA_ARGS__)///<info log

/**
 * @brief  log family function produce output according to the format
 * @param[in] caller:       caller function name
 * @param[in] format:       specific the output format
 * <AUTHOR>
 * @data   2023-05-08
*/
void    log_debug           (const char* caller, const char* format, ...);
#define pi_log_d(fmt, ...)  log_debug(__PRETTY_FUNCTION__, fmt, ## __VA_ARGS__)///< debug log

/**
 * @brief  log family function produce output according to the format
 * @param[in] caller:       caller function name
 * @param[in] format:       specific the output format
 * <AUTHOR>
 * @data   2023-05-08
*/
void    log_trace           (const char* caller, const char* format, ...);
#define pi_log_t(fmt, ...)  log_trace(__PRETTY_FUNCTION__, fmt, ## __VA_ARGS__)///< trace log

/**
 * @brief  log set level from api
 * @return result
 * <AUTHOR>
 * @data   2024-11-20
*/
void    pi_log_set_level       (int32_t level);

/**
 * @brief  set log output level of the log data.
 * @param[in] level: output level
 * <AUTHOR>
 * @data   2023-07-19
*/
void    log_set_level       (int32_t level);

/**
 * @brief  show output level of the log data.
 * <AUTHOR>
 * @data   2023-07-19
*/
void    log_show_level      (void);

/**
 * @brief  set log output mode of the log data.
 * @param[in] mode: output mode
 * <AUTHOR>
 * @data   2023-07-19
*/
void    log_set_mode        (int32_t mode);

/**
 * @brief  show output mode of the log data.
 * <AUTHOR>
 * @data   2023-07-19
*/
void    log_show_mode       (void);

/**
 * @brief  register flush crashlog callback function
 * <AUTHOR>
 * @data   2025-06-03
*/
void log_set_flush_crashlog_callback(void (*callback)(void));


typedef struct{
    char        sig_handle[16];     ///<signal handle
    int         sig_num;            ///<signal num
    char        sig_name[12];       ///<signal name
    int         si_error;           ///<signal error num
    int         si_code;            ///<signal code
    int         size;               ///<signal bt size
    char        bac_trace[12];      ///<signal bt array
    int         si_pid_t;           ///<process pid
    int         si_uid_t;           ///<process uid
    int*        si_addr_t;          ///<process addr
    int         si_fd_t;            ///<process fd
    char**      pInfo;              ///<pointer
}CRASH_LOG_DATA_S;
/**
 * @brief  crash log record save to file
 * @param[in] crash_log_data:       crash log data
 * @return result
 * @retval =0: success
 *         <0: fail
 * <AUTHOR>
 * @data   2024-07-16
*/
int32_t pi_crashlog_record_save_to_file(CRASH_LOG_DATA_S crash_log_data);

/**
 * @brief  decrypto log from file
 * @param[in] *in_filename  : encrypto log file
 * @param[in] *out_filename : decrypto log file
 * @return result
 * @retval =0: success
 *         <0: fail
 * <AUTHOR>
 * @data   2024-07-23
*/
int32_t pi_decrypto_log_from_file(const char *in_filename, const char* out_filename);

/**
 * @brief  init log level and mode
 * <AUTHOR>
 * @data   2025-03-04
*/
void pi_log_lvl_mode_init(uint32_t output_lvl_set, uint32_t ouput_mode_set);


PT_END_DECLS

#endif  /* __POL_LOG_H__ */
/**
 *@}
 */
