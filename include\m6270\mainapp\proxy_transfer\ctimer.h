/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file ctimer.h
 * @addtogroup proxy
 * @{
 * @brief timer interface 
 * <AUTHOR> 
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef _CTIMER_H
#define _CTIMER_H

#include <pthread.h>
#include <sys/time.h>


struct ctimer
{
    void (*timeout_callback)(void*,int*);
    int tfd;
    int interval;
    int _start;
    int exit;
    struct timeval time_arg;
    void *arg;
    pthread_mutex_t mtx;
    pthread_t th;
};


/**
 * @brief initialization of the timer 
 *
 * @param t[in] the address of the ctimer structure to be initialized
 * @param timout_callback[in] a timeout will trigger a callback function 
 * @param arg[in] when the timeout occurs, this parameter is passed into \n
 *                   the callback function
 * @param timeout_arg a timeout that is a struct timeval (with seconds and microseconds)
 * @param interval[in] whether to allow timeouts to restart the timer automatically
 *
 * @return 0 on success , -1 on error 
 */
int ctimer_init(struct ctimer *t , 
        void (*timout_callback)(void*,int*) , 
        void *arg ,
        struct timeval timeout_arg,
        int interval
        );

/**
 * @brief start the timer
 *
 * @param t[in] the address of the ctimer structure
 *
 * @return 0 on success , -1 on error 
 */
int ctimer_start(struct ctimer *t);

/**
 * @brief check that the timer is started 
 *
 * @param t[in] the address of the ctimer structure
 *
 * @return 1 means started , 0 means not started , and \n
 *          -1 means error
 */
int ctimer_active(struct ctimer *t);

/**
 * @brief stop the timer
 *
 * @param t[in] the address of the ctimer structure
 *
 * @return 0 on success , -1 on error 
 */
int ctimer_stop(struct ctimer *t);

/**
 * @brief restart the timer 
 *
 * @param t[in] the address of the ctimer structure
 *
 * @return 0 on success , -1 on error 
 */
int ctimer_restart(struct ctimer *t);

/**
 * @brief 
 *
 * @param t[in] the address of the ctimer structure
 * @param timeout_arg[in] modify the parameters for \n
 *          the timeout
 *
 * @return 0 on success , -1 on error 
 */
int ctimer_update(struct ctimer *t , struct timeval timeout_arg);

/**
 * @brief destroy the timer
 *
 * @param t[in] the address of the ctimer structure
 *
 * @return 0 on success , -1 on error 
 */
int ctimer_destroy(struct ctimer *t);


#endif //_CTIMER_H
/**                                                                                                                                                                  
 * @}
 */
