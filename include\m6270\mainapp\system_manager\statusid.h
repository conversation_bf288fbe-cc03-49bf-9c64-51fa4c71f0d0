/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file statusid.h
 * @addtogroup system_manager
 * @{
 * @brief status id information
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18
 */

#ifndef _STATUSID_H
#define _STATUSID_H

#include "pol/pol_types.h"

#define STATUS_ID_TYPE_OFFSET                          (28)
#define STATUS_ID_MODULE_OFFSET                        (20)

typedef enum
{
    STATUS_ID_TYPE_INFO                            = (uint32_t)0x1 << STATUS_ID_TYPE_OFFSET,
    STATUS_ID_TYPE_WARN                            = (uint32_t)0x2 << STATUS_ID_TYPE_OFFSET,
    STATUS_ID_TYPE_ERROR                           = (uint32_t)0x3 << STATUS_ID_TYPE_OFFSET,
    STATUS_ID_TYPE_FATAL                           = (uint32_t)0x4 << STATUS_ID_TYPE_OFFSET,

    STATUS_ID_TYPE_ALL                             = (uint32_t)0xF << STATUS_ID_TYPE_OFFSET,
    STATUS_ID_TYPE_MASK                            = STATUS_ID_TYPE_ALL
} STATUS_ID_TYPE_E;

typedef enum
{
    STATUS_ID_MODULE_PRINT                         = (uint32_t)0x01 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_SCAN                          = (uint32_t)0x02 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_COPY                          = (uint32_t)0x03 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_FAX                           = (uint32_t)0x04 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_PANEL                         = (uint32_t)0x05 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_NET                           = (uint32_t)0x06 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_USB                           = (uint32_t)0x07 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_PLATFORM                      = (uint32_t)0x08 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_IMAGE                         = (uint32_t)0x09 << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_FRAMEWORK                     = (uint32_t)0x0A << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_COMMON                        = (uint32_t)0x0B << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_FWUPDATE                      = (uint32_t)0x0C << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_POWERMGR                      = (uint32_t)0x0D << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_STORAGE                       = (uint32_t)0x0E << STATUS_ID_MODULE_OFFSET,

    STATUS_ID_MODULE_ALL                           = (uint32_t)0xFF << STATUS_ID_MODULE_OFFSET,
    STATUS_ID_MODULE_MASK                          = STATUS_ID_MODULE_ALL
} STATUS_ID_MODULE_E;


typedef enum
{
    /* PRINT STATUS BEGIN */

    STATUS_I_PRINT_INIT = 0x10100001, ///< 打印模块初始化
    STATUS_I_PRINT_READY = 0x10100002, ///< 打印模块空闲
    STATUS_I_PRINT_SLEEP = 0x10100003, ///< 打印模块睡眠
    STATUS_I_PRINT_WARMING = 0x10100004, ///< 打印模块预热
    STATUS_I_PRINT_PROCESSING = 0x10101005, ///< 打印模块处理中
    STATUS_I_PRINT_PRINTING = 0x10101006, ///< 打印中
    STATUS_I_PRINT_CANCELING = 0x10101007, ///< 打印作业取消中
    STATUS_I_PRINT_PAUSING = 0x10101008, ///< 打印作业暂停中
    STATUS_I_PRINT_ACR_CALIBRATION = 0x10101009, ///< 打印模块校准中
    STATUS_I_PRINT_WAITING = 0x1010100A, ///< 打印模块等待中
    STATUS_I_PRINT_HOLDPRINT_CONFIRM = 0x1010100B, ///< 样本打印，打印一份后提示用户的确认信息
    STATUS_I_PRINT_HOLDPRINT_FINISH = 0x1010100C, ///< 样本打印，打印剩余份后提示用户是否删除数据
    STATUS_I_PRINT_PINCODEPRINT_FINISH = 0x10101011, ///< 密码打印完成
    STATUS_I_PRINT_PAPER_CHANGED = 0x10102012, ///< 插入纸盒后【纸张自动检测弹框】消除&纸张变换完了
    STATUS_I_PRINT_HOLDPRINT_LIST_FULL = 0x10101013, ///< 样本打印超过保存最大数
    STATUS_I_PRINT_STORAGE_JOB_FULL_FAILED = 0x10101014, ///< 磁盘不足或者作业数量达到上限导致作业存储失败
    STATUS_I_PRINT_STORAGE_JOB_STREAM_FAILED = 0x10101015, ///< 流读取错误导致作业存储失败
    STATUS_I_PRINT_IPS_PARSER_TIMEOUT = 0x10101016, ///< ipsparser读取io超时
    STATUS_I_PRINT_TONER_EMPTY_COLOR = 0x10100017, ///< 彩粉尽操作选择项:黑白打印/取消打印
    STATUS_I_PRINT_FE_STATUS_ON = 0x10100018, ///< 进入FE状态操作选择项:黑白打印/取消打印
    STATUS_I_PRINT_UNSUPPORTED_DOCUMENT_FORMAT = 0x10100019, ///< 不支持的文档格式
    STATUS_I_PRINT_FLIP_OVER = 0x1010001A, ///< 手动双面打印作业，一面打印完了，需要整体翻面
    STATUS_I_PRINT_OPC_CALIBRATION = 0x1010001B, ///< OPC校验
    STATUS_I_PRINT_TONER_CONSUM_CALIBRATION = 0x1010001C, ///< 碳粉消耗中
    STATUS_I_PRINT_HOLDPRINT_STORAGE_FULL = 0x1010001D, ///< 在样本打印存储过程中,如果发生存储空间不足,则提示用户此信息.此时作业会被自动取消
    STATUS_I_PRINT_ABORTING = 0x1010101E, ///< 打印作业异常
    STATUS_I_PRINT_TRAY_NEED_CLOSE = 0x10120001, ///< 纸盒未关闭
    STATUS_I_PRINT_EMMC_NORMAL = 0x10120002, ///< 正常状态，只有该状态才能支持逐份作业。事实上,IDLE意味着正常，故此状态可无
    STATUS_I_PRINT_EMMC_FORMATTING = 0x10120003, ///< 内部存储器初始化中，该状态下需要等待格式化完成才能下发作业，作业中不会出现该状态，在等待过程中面板需要提示用户内部存储器正在格式化
    STATUS_I_PRINT_PINCODE_SAVING = 0x1010101F, ///< 密码作业存储中
    STATUS_I_PRINT_PINCODE_SAVE_FINISH = 0x10101020, ///< 密码作业存储完了
    STATUS_I_PRINT_SAVING = 0x10120005, ///< 存储中
    STATUS_I_PRINT_TONER_SUPPLYING = 0x10120006, ///< 补粉中
    STATUS_I_PRINT_INTERNAL_PAGE_CREATE_PDF = 0x10120007, ///< 内部页生成设备报告
    STATUS_I_PRINT_SAMPLE_NOTIFY_PANEL_STOP_JOB = 0x10120008, ///< 样本复印通知面板停止作业
    STATUS_I_PRINT_JOB_LOCKED = 0x10120009, ///< 打印作业管控信息
    STATUS_I_PRINT_DATA_OPC_CLEAR = 0x1012000A, ///< 清除OPC感光鼓残留碳粉
    STATUS_W_PRINT_TONER_NEAR_EMPTY_C = 0x20103001, ///< C碳粉即将用尽
    STATUS_W_PRINT_TONER_NEAR_EMPTY_M = 0x20103002, ///< M碳粉即将用尽
    STATUS_W_PRINT_TONER_NEAR_EMPTY_Y = 0x20103003, ///< Y碳粉即将用尽
    STATUS_W_PRINT_TONER_NEAR_EMPTY_K = 0x20103004, ///< K碳粉即将用尽
    STATUS_W_PRINT_WASTE_TONER_NEAR_FULL = 0x20103005, ///< 废碳粉仓即将满仓
    STATUS_W_PRINT_FUSER_NEAR_LIFE_END = 0x20103006, ///< 定影寿命即将到达
    STATUS_W_PRINT_DRUM_NEAR_LIFE_END_C = 0x20103007, ///< C硒鼓寿命即将到达
    STATUS_W_PRINT_DRUM_NEAR_LIFE_END_M = 0x20103008, ///< M硒鼓寿命即将到达
    STATUS_W_PRINT_DRUM_NEAR_LIFE_END_Y = 0x20103009, ///< Y硒鼓寿命即将到达
    STATUS_W_PRINT_DRUM_NEAR_LIFE_END_K = 0x2010300A, ///< K硒鼓寿命即将到达
    STATUS_W_PRINT_TRAY_MISSING_STANDARD_TRAY = 0x2010400B, ///< 标准进纸盒抽出
    STATUS_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1 = 0x2010400C, ///< 选配进纸盒1抽出
    STATUS_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2 = 0x2010400D, ///< 选配进纸盒2抽出
    STATUS_W_PRINT_TRAY_EMPTY_STANDARD_TRAY = 0x2010200E, ///< 标准进纸盒缺纸
    STATUS_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1 = 0x20102010, ///< 选配进纸盒1缺纸
    STATUS_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2 = 0x20102011, ///< 选配进纸盒2缺纸
    STATUS_W_PRINT_STANDARD_BIN_FULL = 0x20102012, ///< 标准排纸盒满
    STATUS_W_PRINT_COLOR_CALIBRATION = 0x20105017, ///< 色彩校正异常
    STATUS_W_PRINT_COLOR_REGISTRATION = 0x20105018, ///< 色彩配准异常
    STATUS_W_PRINT_DRUM_CARRIER_LOW_C = 0x20103019, ///< C硒鼓载体即将用尽
    STATUS_W_PRINT_DRUM_CARRIER_LOW_M = 0x2010301A, ///< M硒鼓载体即将用尽
    STATUS_W_PRINT_DRUM_CARRIER_LOW_Y = 0x2010301B, ///< Y硒鼓载体即将用尽
    STATUS_W_PRINT_DRUM_CARRIER_LOW_K = 0x2010301C, ///< K硒鼓载体即将用尽
    STATUS_W_PRINT_REALAY_ROLLER_NEAR_LIFE_END = 0x2010301D, ///< 转印辊寿命即将到达
    STATUS_W_PRINT_ITU_NEAR_LIFE_END = 0x2010301E, ///< 转写带寿命即将到达
    STATUS_W_PRINT_TRAY2_ERROR = 0x2010401F, ///< 选配给纸盒1异常
    STATUS_W_PRINT_TRAY3_ERROR = 0x20104020, ///< 选配给纸盒2异常
    STATUS_W_PRINT_TRAY2_MOTOR_ERROR = 0x20104021, ///< 选配给纸盒1马达异常
    STATUS_W_PRINT_TRAY3_MOTOR_ERROR = 0x20104022, ///< 选配给纸盒2马达异常
    STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_Y = 0x20103023, ///< 显影组件浓度异常高Y通道
    STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_M = 0x20103024, ///< 显影组件浓度异常高M通道
    STATUS_W_PRINT_DEVELOP_CO_HIGH_ERROR_C = 0x20103025, ///< 显影组件浓度异常高C通道
    STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_Y = 0x20106026, ///< 浓度传感器输出电压超出下限Y通道
    STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_M = 0x20106027, ///< 浓度传感器输出电压超出下限M通道
    STATUS_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_C = 0x20106028, ///< 浓度传感器输出电压超出下限C通道
    STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_Y = 0x20106029, ///< 浓度传感器输出电压超出上限Y通道
    STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_M = 0x2010602A, ///< 浓度传感器输出电压超出上限M通道
    STATUS_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_C = 0x2010602B, ///< 浓度传感器输出电压超出上限C通道
    STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_Y = 0x2010602C, ///< 浓度传感器异常Y通道
    STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_M = 0x2010602D, ///< 浓度传感器异常M通道
    STATUS_W_PRINT_CO_SENSOR_DETECT_ERROR_C = 0x2010602E, ///< 浓度传感器异常C通道
    STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_Y = 0x2010302F, ///< 碳粉供应故障Y通道
    STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_M = 0x20103030, ///< 碳粉供应故障M通道
    STATUS_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_C = 0x20103031, ///< 碳粉供应故障C通道
    STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_Y = 0x20103032, ///< 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)Y通道
    STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_M = 0x20103033, ///< 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)M通道
    STATUS_W_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_C = 0x20103034, ///< 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)C通道
    STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_Y = 0x20103035, ///< 粉桶碳粉空错误(粉桶寿命小于5%)Y通道
    STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_M = 0x20103036, ///< 粉桶碳粉空错误(粉桶寿命小于5%)M通道
    STATUS_W_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_C = 0x20103037, ///< 粉桶碳粉空错误(粉桶寿命小于5%)C通道
    STATUS_W_PRINT_INTERNAL_ENV_TEMP_ERROR = 0x20106038, ///< 内部环境温湿度传感器异常
    STATUS_W_PRINT_EXTERNAL_ENV_TEMP_ERROR = 0x20106039, ///< 外部环境温湿度传感器异常
    STATUS_W_PRINT_FRONT_CTD_SENSOR_DETECT = 0x2010603A, ///< 前侧CTDSensor检测故障
    STATUS_W_PRINT_BACK_CTD_SENSOR_DETECT = 0x2010603B, ///< 后侧CTDSensor检测故障
    STATUS_W_PRINT_FRONT_CTD_SENSOR_ADJUST = 0x2010603C, ///< 前侧CTDSensor调整故障
    STATUS_W_PRINT_BACK_CTD_SENSOR_ADJUST = 0x2010603D, ///< 后侧CTDSensor调整故障
    STATUS_W_PRINT_TONER_EMPTY_C = 0x2010303E, ///< C碳粉完全用尽
    STATUS_W_PRINT_TONER_EMPTY_M = 0x2010303F, ///< M碳粉完全用尽
    STATUS_W_PRINT_TONER_EMPTY_Y = 0x20103040, ///< Y碳粉完全用尽
    STATUS_W_PRINT_TONER_EMPTY_K = 0x20103041, ///< K碳粉完全用尽
    STATUS_W_PRINT_LSU_TEMP_SENSOR_ERROR = 0x20106042, ///< LSU温度传感器异常
    STATUS_W_PRINT_FE0280_03 = 0x20106043, ///< 色彩校正传感器异常：前
    STATUS_W_PRINT_FE0280_04 = 0x20106044, ///< 色彩校正传感器异常：后
    STATUS_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1 = 0x20100045, ///< 选配纸盒1底板异常
    STATUS_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2 = 0x20100046, ///< 选配纸盒2底板异常
    STATUS_W_PRINT_FE0280_01 = 0x20100047, ///< 黑色相位传感器异常
    STATUS_W_PRINT_FE0280_02 = 0x20100048, ///< 彩色相位传感器异常
    STATUS_W_PRINT_OPTION_TRAY1_ADJUSTING = 0x20100049, ///< 选配纸盒1调整中
    STATUS_W_PRINT_OPTION_TRAY2_ADJUSTING = 0x2010004A, ///< 选配纸盒2调整中
    STATUS_W_PRINT_WASTE_TONER_FULL = 0x2010004B, ///< 废碳粉仓满仓
    STATUS_W_PRINT_DRUM_LIFE_END_C = 0x2010004C, ///< C硒鼓寿命尽
    STATUS_W_PRINT_DRUM_LIFE_END_M = 0x2010004D, ///< M硒鼓寿命尽
    STATUS_W_PRINT_DRUM_LIFE_END_Y = 0x2010004E, ///< Y硒鼓寿命尽
    STATUS_W_PRINT_DRUM_LIFE_END_K = 0x2010004F, ///< K硒鼓寿命尽
    STATUS_W_PRINT_DRUM_CARRIER_EMPTY_C = 0x20100050, ///< C硒鼓载体用尽
    STATUS_W_PRINT_DRUM_CARRIER_EMPTY_M = 0x20100051, ///< M硒鼓载体用尽
    STATUS_W_PRINT_DRUM_CARRIER_EMPTY_Y = 0x20100052, ///< Y硒鼓载体用尽
    STATUS_W_PRINT_DRUM_CARRIER_EMPTY_K = 0x20100053, ///< K硒鼓载体用尽
    STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_C = 0x20100054, ///< C碳粉余量不足
    STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_M = 0x20100055, ///< M碳粉余量不足
    STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_Y = 0x20100056, ///< Y碳粉余量不足
    STATUS_W_PRINT_TONER_REMAIN_INSUFFICIENT_K = 0x20100057, ///< K碳粉余量不足
    STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_C = 0x20100058, ///< C硒鼓余量不足
    STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_M = 0x20100059, ///< M硒鼓余量不足
    STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_Y = 0x2010005A, ///< Y硒鼓余量不足
    STATUS_W_PRINT_DRUM_LIFE_INSUFFICIENT_K = 0x2010005B, ///< K硒鼓余量不足
    STATUS_W_PRINT_LOW_TEMPERATURE = 0x2010005C, ///< 低温警告
    STATUS_W_PRINT_MAIN_HUMITURE_SENSOR = 0x2010005D, ///< 主温湿度传感器异常
    STATUS_W_PRINT_SUB_HUMITURE_SENSOR = 0x2010005E, ///< 备用温湿度传感器异常
    STATUS_W_PRINT_FE0280_05 = 0x2010005F, ///< 色彩校正传感器脏污
    STATUS_W_PRINT_TONER_SUPPLY_CAUTION_C = 0x20100060, ///< 碳粉供应异常警告C
    STATUS_W_PRINT_TONER_SUPPLY_CAUTION_M = 0x20100061, ///< 碳粉供应异常警告M
    STATUS_W_PRINT_TONER_SUPPLY_CAUTION_Y = 0x20100062, ///< 碳粉供应异常警告Y
    STATUS_W_PRINT_TONER_SUPPLY_CAUTION_K = 0x20100063, ///< 碳粉供应异常警告K
    STATUS_W_PRINT_MAIN_HUM_SENSOR_EXCEPTION = 0x20100064, ///< 主环境温湿度传感器读取异常
    STATUS_W_PRINT_FE0281_00 = 0x20100066, ///< IDC温度传感器异常
    STATUS_W_PRINT_FE0850_00 = 0x20100067, ///< 温湿度传感器通信异常
    STATUS_W_PRINT_FUSER_LIFE_END = 0x20100068, ///< 定影寿命到达
    STATUS_W_PRINT_TRAY_1_OPEN = 0x20120001, ///< 纸盒1未插入
    STATUS_W_PRINT_TRAY_2_OPEN = 0x20120002, ///< 纸盒2未插入
    STATUS_W_PRINT_TRAY_3_OPEN = 0x20120003, ///< 纸盒3未插入
    STATUS_W_PRINT_TRAY_4_OPEN = 0x20120004, ///< 纸盒4未插入
    STATUS_W_PRINT_LCT_IN_OPEN = 0x20120005, ///< 内置大容量纸盒未插入
    STATUS_W_PRINT_TRAY_1_PAPER_EMPTY = 0x20120006, ///< 纸盒1缺纸
    STATUS_W_PRINT_TRAY_2_PAPER_EMPTY = 0x20120007, ///< 纸盒2缺纸
    STATUS_W_PRINT_TRAY_3_PAPER_EMPTY = 0x20120008, ///< 纸盒3缺纸
    STATUS_W_PRINT_TRAY_4_PAPER_EMPTY = 0x20120009, ///< 纸盒4缺纸
    STATUS_W_PRINT_LCT_IN_PAPER_EMPTY = 0x2012000A, ///< 内置大容量纸盒缺纸
    STATUS_W_PRINT_LCT_EX_PAPER_EMPTY = 0x2012000B, ///< 外置大容量纸盒缺纸
    STATUS_W_PRINT_TRAY_1_PAPER_NEAR_EMPTY = 0x2012000C, ///< 纸盒1将缺纸
    STATUS_W_PRINT_TRAY_2_PAPER_NEAR_EMPTY = 0x2012000D, ///< 纸盒2将缺纸
    STATUS_W_PRINT_TRAY_3_PAPER_NEAR_EMPTY = 0x2012000E, ///< 纸盒3将缺纸
    STATUS_W_PRINT_TRAY_4_PAPER_NEAR_EMPTY = 0x2012000F, ///< 纸盒4将缺纸
    STATUS_W_PRINT_LCT_IN_PAPER_NEAR_EMPTY = 0x20120010, ///< 内置大容量纸盒将缺纸
    STATUS_W_PRINT_LCT_EX_PAPER_NEAR_EMPTY = 0x20120011, ///< 外置大容量纸盒将缺纸
    STATUS_W_PRINT_LCT_IN_MOVING = 0x20120012, ///< 内建大容量纸盒正在移动纸张
    STATUS_W_PRINT_LCT_IN_PAPER_NEAR_FULL = 0x20120013, ///< 内置大容量纸盒将满
    STATUS_W_PRINT_LCT_EX_PAPER_NEAR_FULL = 0x20120014, ///< 外置大容量纸盒将满
    STATUS_W_PRINT_LCT_IN_PAPER_FULL = 0x20120015, ///< 内置大容量纸盒已满
    STATUS_W_PRINT_LCT_EX_PAPER_FULL = 0x20120016, ///< 外置大容量纸盒已满
    STATUS_W_PRINT_Y_TONER_NEAR_EMPTY = 0x20120017, ///< 黄色碳粉盒粉量低
    STATUS_W_PRINT_M_TONER_NEAR_EMPTY = 0x20120018, ///< 品红色碳粉盒粉量低
    STATUS_W_PRINT_C_TONER_NEAR_EMPTY = 0x20120019, ///< 青色碳粉盒粉量低
    STATUS_W_PRINT_K_TONER_NEAR_EMPTY = 0x2012001A, ///< 黑色碳粉盒粉量低
    STATUS_W_PRINT_Y_DR_NEAR_LIFE = 0x2012001B, ///< 黄色鼓组件寿命将尽
    STATUS_W_PRINT_M_DR_NEAR_LIFE = 0x2012001C, ///< 品红色鼓组件寿命将尽
    STATUS_W_PRINT_C_DR_NEAR_LIFE = 0x2012001D, ///< 青色鼓组件寿命将尽
    STATUS_W_PRINT_K_DR_NEAR_LIFE = 0x2012001E, ///< 黑色鼓组件寿命将尽
    STATUS_W_PRINT_Y_DV_NEAR_LIFE = 0x2012001F, ///< 黄色显影组件寿命将尽
    STATUS_W_PRINT_M_DV_NEAR_LIFE = 0x20120020, ///< 品红色显影组件寿命将尽
    STATUS_W_PRINT_C_DV_NEAR_LIFE = 0x20120021, ///< 青色显影组件寿命将尽
    STATUS_W_PRINT_K_DV_NEAR_LIFE = 0x20120022, ///< 黑色显影组件寿命将尽
    STATUS_W_PRINT_TRANSFER_ROLLER_UNIT_LIFE_END = 0x20120023, ///< 转印辊寿命将尽
    STATUS_W_PRINT_TONER_FILTER_LIFE_END = 0x20120024, ///< 粉盒过滤网寿命将尽
    STATUS_W_PRINT_FUSING_UNIT_LIFE_END = 0x20120025, ///< 定影组件寿命将尽
    STATUS_W_PRINT_TRANSFER_BELT_UNIT_LIFE_END = 0x20120026, ///< 转印带寿命将尽
    STATUS_W_PRINT_SIDE_STITCHING_STAPLE_EMPTY = 0x20120027, ///< 平订订书钉空
    STATUS_W_PRINT_STAPLE_NEAR_EMPTY = 0x20120028, ///< 平订订书钉将空
    STATUS_W_PRINT_SADDLE_BIND_F_STAPLE_EMPTY = 0x20120029, ///< 鞍式装订订书钉（前）空
    STATUS_W_PRINT_SADDLE_BIND_B_STAPLE_EMPTY = 0x2012002A, ///< 鞍式装订订书钉（后）空
    STATUS_W_PRINT_PUNCH_WASTE_NOT_SET = 0x2012002B, ///< 打孔废料盒未安装
    STATUS_W_PRINT_PUNCH_TRASH_BOX_FULL = 0x2012002C, ///< 打孔废料盒满
    STATUS_W_PRINT_NEEDLE_SCARP_OVER = 0x2012002D, ///< 平钉废盒状态
    STATUS_W_PRINT_MAIN_ABNORMAL_LOWER_STATE = 0x2012002E, ///< 主托盘异常下降状态异常
    STATUS_W_PRINT_MEDIA_SENSOR_ERROR1 = 0x2012002F, ///< 介质传感器异常1
    STATUS_W_PRINT_BAROMETRIC_SENSOR_ERROR = 0x20120030, ///< 气压传感器异常
    STATUS_W_PRINT_MEDIA_SENSOR_ERROR2 = 0x20120031, ///< 介质传感器异常2
    STATUS_W_PRINT_LD_ABNORMALITY = 0x20120032, ///< 激光二极管异常
    STATUS_W_PRINT_SKEW_ADJUSTMENT_ABNORMALITY = 0x20120033, ///< 纠偏异常
    STATUS_W_PRINT_IDC_SENSOR_FRONT_ABNORMALITY = 0x20120034, ///< IDC传感器(前)异常
    STATUS_W_PRINT_C_DR_DV_ABNORMALITY = 0x20120035, ///< 青色鼓组件或显影组件异常
    STATUS_W_PRINT_M_DR_DV_ABNORMALITY = 0x20120036, ///< 品红色鼓组件或显影组件异常
    STATUS_W_PRINT_Y_DR_DV_ABNORMALITY = 0x20120037, ///< 黄色鼓组件或显影组件异常
    STATUS_W_PRINT_K_DR_DV_ABNORMALITY = 0x20120038, ///< 黑色鼓组件或显影组件异常
    STATUS_W_PRINT_IDC_SENSOR_BACK_ABNORMALITY = 0x20120039, ///< IDC传感器(后)异常
    STATUS_W_PRINT_COLOR_PATTERN_TEST_ERROR = 0x2012003A, ///< 色彩对位测试样式异常
    STATUS_W_PRINT_COLOR_CORRECTION_AMOUNT_ERROR = 0x2012003B, ///< 色彩对位调整异常
    STATUS_W_PRINT_PH_OPTICAL_SYSTEM_DIRT = 0x2012003C, ///< PH光学系统污染
    STATUS_W_PRINT_PAPER_WIDTH_ERROR = 0x2012003D, ///< 纸张宽度传感器异常
    STATUS_W_PRINT_PAPER_TEMPERATURE_ERROR = 0x2012003E, ///< 纸张温度传感器异常
    STATUS_W_PRINT_PH_TEMPERATURE_ERROR = 0x2012003F, ///< PH温度传感器异常
    STATUS_W_PRINT_ABNORMAL_2ND_TRANSFER_ATVC = 0x20120040, ///< 第二转印ATVC异常
    STATUS_W_PRINT_INFLIGHT_TEMPERATURE_ERROR = 0x20120041, ///< 机内温度传感器异常
    STATUS_W_PRINT_TACKING_FAN_ABNORMALITY = 0x20120042, ///< 平钉风扇异常
    STATUS_W_PRINT_FUSER_SENSOR_TEMP_DETECT_ERROR = 0x20120043, ///< 定影单元温度传感器温度检测异常
    STATUS_W_PRINT_TRAY1_PAPER_LOW = 0x20100069, ///< 标准进纸盒纸张剩余量少
    STATUS_W_PRINT_TRAY2_PAPER_LOW = 0x2010006A, ///< 选配进纸盒1纸张剩余量少
    STATUS_W_PRINT_TRAY3_PAPER_LOW = 0x2010006B, ///< 选配进纸盒2纸张剩余量少
    STATUS_W_PRINT_STAPLE_EMPTY_3 = 0x20120044, ///< 钉空3警告
    STATUS_W_PRINT_SELECTED_OUTPUT_SOURCE_FULL = 0x20120045, ///< 选择出口源满
    STATUS_W_PRINT_STAPLE_EMPTY_2 = 0x20120046, ///< 钉空2警告
    STATUS_W_PRINT_FLOD_PAPER_TRAY_LIFT = 0x20120047, ///< 折叠纸盒抬升警告
    STATUS_W_PRINT_SADDLE_BIND_STAPLE_MOVE = 0x20120048, ///< 鞍式装订移动
    STATUS_W_PRINT_FLAT_BINDING_STAPLER_MOVING = 0x20120049, ///< 平钉钉书器移动
    STATUS_W_PRINT_TONER_SUPPLY_DOOR_OPEN = 0x2012004A, ///< 粉盒补给门打开
    STATUS_W_PRINT_LCT_EX_DOOR_F_OPEN = 0x2012004D, ///< 外置大容量纸盒（盖门F）未插入
    STATUS_E_PRINT_BACK_COVER_OPEN = 0x30104000, ///< 机器后盖打开
    STATUS_E_PRINT_RIGHT_COVER_OPEN = 0x30104001, ///< 机器右盖打开
    STATUS_E_PRINT_FRONT_COVER_OPEN = 0x30104002, ///< 机器前盖打开
    STATUS_E_PRINT_TRAY2_COVER_OPEN = 0x30104003, ///< 选配给纸盒1侧盖打开
    STATUS_E_PRINT_TRAY3_COVER_OPEN = 0x30104004, ///< 选配给纸盒2侧盖打开
    STATUS_E_PRINT_TONER_EMPTY_C = 0x30103005, ///< C碳粉完全用尽
    STATUS_E_PRINT_TONER_EMPTY_M = 0x30103006, ///< M碳粉完全用尽
    STATUS_E_PRINT_TONER_EMPTY_Y = 0x30103007, ///< Y碳粉完全用尽
    STATUS_E_PRINT_TONER_EMPTY_K = 0x30103008, ///< K碳粉完全用尽
    STATUS_E_PRINT_TONER_MISSING_C = 0x30103009, ///< C碳粉筒未安装
    STATUS_E_PRINT_TONER_MISSING_M = 0x3010300A, ///< M碳粉筒未安装
    STATUS_E_PRINT_TONER_MISSING_Y = 0x3010300B, ///< Y碳粉筒未安装
    STATUS_E_PRINT_TONER_MISSING_K = 0x3010300C, ///< K碳粉筒未安装
    STATUS_E_PRINT_TONER_MISMATCH_C = 0x3010300D, ///< C碳粉筒不匹配
    STATUS_E_PRINT_TONER_MISMATCH_M = 0x3010300E, ///< M碳粉筒不匹配
    STATUS_E_PRINT_TONER_MISMATCH_Y = 0x30103010, ///< Y碳粉筒不匹配
    STATUS_E_PRINT_TONER_MISMATCH_K = 0x30103011, ///< K碳粉筒不匹配
    STATUS_E_PRINT_DRUM_LIFE_END_C = 0x30103012, ///< C硒鼓寿命尽
    STATUS_E_PRINT_DRUM_LIFE_END_M = 0x30103013, ///< M硒鼓寿命尽
    STATUS_E_PRINT_DRUM_LIFE_END_Y = 0x30103014, ///< Y硒鼓寿命尽
    STATUS_E_PRINT_DRUM_LIFE_END_K = 0x30103015, ///< K硒鼓寿命尽
    STATUS_E_PRINT_DRUM_MISSING_C = 0x30103016, ///< C硒鼓未安装
    STATUS_E_PRINT_DRUM_MISSING_M = 0x30103017, ///< M硒鼓未安装
    STATUS_E_PRINT_DRUM_MISSING_Y = 0x30103018, ///< Y硒鼓未安装
    STATUS_E_PRINT_DRUM_MISSING_K = 0x30103019, ///< K硒鼓未安装
    STATUS_E_PRINT_DRUM_MISMATCH_C = 0x3010301A, ///< C硒鼓不匹配
    STATUS_E_PRINT_DRUM_MISMATCH_M = 0x3010301B, ///< M硒鼓不匹配
    STATUS_E_PRINT_DRUM_MISMATCH_Y = 0x3010301C, ///< Y硒鼓不匹配
    STATUS_E_PRINT_DRUM_MISMATCH_K = 0x3010301D, ///< K硒鼓不匹配
    STATUS_E_PRINT_WASTE_TONER_FULL = 0x3010301E, ///< 废碳粉仓满仓
    STATUS_E_PRINT_WASTE_TONER_MISSING = 0x30103020, ///< 废粉盒未安装
    STATUS_E_PRINT_STANDARD_BIN_FULL = 0x30104021, ///< 标准排纸盒满
    STATUS_E_PRINT_TRAY_MISSING_STANDARD_TRAY = 0x30104022, ///< 标准进纸盒纸盒抽出
    STATUS_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1 = 0x30104023, ///< 选配进纸盒1纸盒抽出
    STATUS_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2 = 0x30104024, ///< 选配进纸盒2纸盒抽出
    STATUS_E_PRINT_TRAY_EMPTY_STANDARD_TRAY = 0x30102025, ///< 标准进纸盒纸盒缺纸
    STATUS_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY = 0x30102026, ///< 多功能进纸盒纸盒缺纸
    STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1 = 0x30102027, ///< 选配进纸盒1纸盒缺纸
    STATUS_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2 = 0x30102028, ///< 选配进纸盒2纸盒缺纸
    STATUS_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH = 0x30102029, ///< 标准进纸盒纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH = 0x3010202A, ///< 多功能进纸盒纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH = 0x3010202B, ///< 标准进纸盒纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH = 0x3010202C, ///< 多功能进纸盒纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH = 0x3010202D, ///< 选配纸盒1纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH = 0x3010202E, ///< 选配纸盒2纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH = 0x30102030, ///< 选配纸盒1纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH = 0x30102031, ///< 选配纸盒2纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_ENGINE_NOT_READY = 0x30100032, ///< 引擎就绪信号异常
    STATUS_E_PRINT_ENGINE_COMMUNICATION_FAILED = 0x30100033, ///< 引擎通信异常
    STATUS_E_PRINT_VIDEO_DRIVE = 0x30100034, ///< 图像输出单元异常,videodriver模块异常
    STATUS_E_PRINT_VIDEO_BANDING = 0x30100035, ///< 图像输出单元异常,videostartbading异常
    STATUS_E_PRINT_FUSER_MISSING = 0x30103036, ///< 定影未安装
    STATUS_E_PRINT_FUSER_LIFE_END = 0x30103037, ///< 定影寿命到达
    STATUS_E_PRINT_JAM_NOT_FEED_STANDARD_TRAY = 0x30104038, ///< 标准进纸盒不给纸JAM
    STATUS_E_PRINT_JAM_NOT_FEED_MULTI_FUNCTION_TRAY = 0x30104039, ///< 多功能进纸盒不给JAM
    STATUS_E_PRINT_JAM_NOT_FEED_DUPLEX_UINT = 0x3010403A, ///< 两面不给纸JAM
    STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_1 = 0x3010403B, ///< 选配进纸盒1不给纸JAM
    STATUS_E_PRINT_JAM_NOT_FEED_OPTIONAL_TRAY_2 = 0x3010403C, ///< 选配进纸盒2不给纸JAM
    STATUS_E_PRINT_JAM_UNATTAIN_CALIBRATE_SENSOR = 0x3010403D, ///< 校正传感器未达JAM
    STATUS_E_PRINT_JAM_UNATTAIN_OUTPUT_SENSOR = 0x3010403E, ///< 排纸传感器未达JAM
    STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_1 = 0x30104040, ///< 选配进纸盒1搬送传感器未达JAM
    STATUS_E_PRINT_JAM_UNATTAIN_OPTIONAL_TRAY_2 = 0x30104041, ///< 选配进纸盒2搬送传感器未达JAM
    STATUS_E_PRINT_JAM_UNATTAIN_FUSER = 0x30104042, ///< 定影传感器未达JAM
    STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_INPUT = 0x30104043, ///< 两面入口未达JAM
    STATUS_E_PRINT_JAM_STRANDED_CALIBRATE_SENSOR = 0x30104044, ///< 校正传感器滞留JAM
    STATUS_E_PRINT_JAM_STRANDED_OUTPUT_SENSOR = 0x30104045, ///< 排纸传感器滞留JAM
    STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_1 = 0x30104046, ///< 选配进纸盒1搬送传感器滞留JAM
    STATUS_E_PRINT_JAM_STRANDED_OPTIONAL_TRAY_2 = 0x30104047, ///< 选配进纸盒2搬送传感器滞留JAM
    STATUS_E_PRINT_JAM_STRANDED_FUSER = 0x30104048, ///< 定影滞留JAM
    STATUS_E_PRINT_JAM_STRANDED_DUPLEX_INPUT = 0x30104049, ///< 两面入口滞留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_FUSER = 0x3010404A, ///< 定影残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT = 0x3010404B, ///< 双面入口残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR = 0x3010404C, ///< 排纸传感器残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_CALIBRATE_SENSOR = 0x3010404D, ///< 校正传感器残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2 = 0x3010404E, ///< 选配进纸盒2残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1 = 0x30104050, ///< 选配进纸盒1残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY = 0x30104051, ///< 多功能进纸盒残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY = 0x30104052, ///< 标准进纸盒残留JAM
    STATUS_E_PRINT_TIMEPRINT_LIST_FULL = 0x30101053, ///< 延时打印作业列表满
    STATUS_E_PRINT_TRAY_PAPER_SIZE_ERROR = 0x30102054, ///< 纸张尺寸错误
    STATUS_E_PRINT_TRAY_FEED_MISMATCH = 0x30102055, ///< 纸张来源不匹配
    STATUS_E_PRINT_DRUM_CARRIER_EMPTY_C = 0x30103055, ///< C硒鼓载体用尽
    STATUS_E_PRINT_DRUM_CARRIER_EMPTY_M = 0x30103056, ///< M硒鼓载体用尽
    STATUS_E_PRINT_DRUM_CARRIER_EMPTY_Y = 0x30103057, ///< Y硒鼓载体用尽
    STATUS_E_PRINT_DRUM_CARRIER_EMPTY_K = 0x30103058, ///< K硒鼓载体用尽
    STATUS_E_PRINT_REALAY_ROLLER_LIFE_END = 0x30103059, ///< 转印辊寿命到达
    STATUS_E_PRINT_ITU_NEAR_LIFE_END = 0x3010305A, ///< 转写带寿命到达
    STATUS_E_PRINT_ILLEGAL_PAGE = 0x3010105B, ///< 非法作业
    STATUS_E_PRINT_JAM_UNATTAIN_CALIB_OPTIONAL_TRAY_1 = 0x3010405C, ///< 选配1校正未达Jam
    STATUS_E_PRINT_JAM_UNATTAIN_CALIB_OPTIONAL_TRAY_2 = 0x3010405D, ///< 选配2校正未达Jam
    STATUS_E_PRINT_JAM_UNATTAIN_DUPLEX_OUTPUT = 0x3010405E, ///< 两面出口未达JAM
    STATUS_E_PRINT_JAM_RESIDUAL_DUPLEX_OUTPUT = 0x3010405F, ///< 双面入出口残留JAM
    STATUS_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1 = 0x30100060, ///< 选配纸盒1底板异常
    STATUS_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2 = 0x30100061, ///< 选配纸盒2底板异常
    STATUS_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT = 0x30100062, ///< 校正传感器+排出传感器残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_CALIB_DUPLEX = 0x30100063, ///< 校正传感器+双面入口残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_OUTPUT_DUPLEX = 0x30100064, ///< 排出传感器+双面入口残留JAM
    STATUS_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT_DUPLEX = 0x30100065, ///< 校正传感器+排出传感器+双面入口残留JAM
    STATUS_E_PRINT_STORAGE_SPACE_NOT_ENOUGH = 0x30100066, ///< 作业存储磁盘空间不足
    STATUS_E_PRINT_FUSER_LIFE_TERMINATION = 0x30100067, ///< 定影寿命终止
    STATUS_E_PRINT_TONER_SUPPLY_CAUTION_C = 0x30100068, ///< 碳粉供应异常C
    STATUS_E_PRINT_TONER_SUPPLY_CAUTION_M = 0x30100069, ///< 碳粉供应异常M
    STATUS_E_PRINT_TONER_SUPPLY_CAUTION_Y = 0x3010006A, ///< 碳粉供应异常Y
    STATUS_E_PRINT_TONER_SUPPLY_CAUTION_K = 0x3010006B, ///< 碳粉供应异常K
    STATUS_E_PRINT_PARSER_TIMEOUT = 0x30120001, ///< 解析超时
    STATUS_E_PRINT_PARSER_ERROR = 0x30120002, ///< 解析错误
    STATUS_E_PRINT_PARSER_DISCONNECT = 0x30120003, ///< 连接断开
    STATUS_E_PRINT_VIDEO = 0x30120004, ///< 图像输出单元异常
    STATUS_E_PRINT_IMAGE_PROCESS_ERROR = 0x30120005, ///< 图像处理错误
    STATUS_E_PRINT_ATTRIBUTE_INCORRECT = 0x30120006, ///< 非法作业
    STATUS_E_PRINT_ATTRIBUTE_INCORRECT_TRAY_IN = 0x30120007, ///< 非法作业_纸盒错误
    STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PAPER_TYPE = 0x30120008, ///< 非法作业_纸张类型错误
    STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PAPER_SIZE = 0x30120009, ///< 非法作业_纸张尺寸错误
    STATUS_E_PRINT_ATTRIBUTE_INCORRECT_PRINT_MODE = 0x3012000A, ///< 非法作业_打印模式错误
    STATUS_E_PRINT_ATTRIBUTE_INCORRECT_STAPLE = 0x3012000B, ///< 非法作业_装订模式错误
    STATUS_E_PRINT_Y_TB_MISMATCH = 0x3012000C, ///< 黄色碳粉盒不匹配
    STATUS_E_PRINT_M_TB_MISMATCH = 0x3012000D, ///< 品红色碳粉盒不匹配
    STATUS_E_PRINT_C_TB_MISMATCH = 0x3012000E, ///< 青色碳粉盒不匹配
    STATUS_E_PRINT_K_TB_MISMATCH = 0x3012000F, ///< 黑色碳粉盒不匹配
    STATUS_E_PRINT_Y_DR_MISMATCH = 0x30120010, ///< 黄色鼓组件不匹配
    STATUS_E_PRINT_M_DR_MISMATCH = 0x30120011, ///< 品红色鼓组件不匹配
    STATUS_E_PRINT_C_DR_MISMATCH = 0x30120012, ///< 青色鼓组件不匹配
    STATUS_E_PRINT_K_DR_MISMATCH = 0x30120013, ///< 黑色鼓组件不匹配
    STATUS_E_PRINT_PRINTRUNONEMPTY_Y = 0x30120014, ///< Y彩粉尽作业
    STATUS_E_PRINT_PRINTRUNONEMPTY_M = 0x30120015, ///< M彩粉尽作业
    STATUS_E_PRINT_PRINTRUNONEMPTY_C = 0x30120016, ///< C彩粉尽作业
    STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_Y = 0x30120017, ///< Y寿命尽继续打印作业中只能取消作业
    STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_M = 0x30120018, ///< M寿命尽继续打印作业中只能取消作业
    STATUS_E_PRINT_PRINTRUNONEMPTY_INJOB_C = 0x30120019, ///< C寿命尽继续打印作业中只能取消作业
    STATUS_E_PRINT_PRINTRUNONEMPTY_WAR = 0x3012001A, ///< 纸盒里纸张尺寸和任务要求的纸张尺寸不一致
    STATUS_E_PRINT_EMMC_FULL = 0x3012001B, ///< 内部存储器已满，如果是下发作业前出现该状态，说明存储器寿命尽，不能支持逐份，如果是作业中出现该状态，说明逐份页数超出限制，面板需要提示用户取消作业
    STATUS_E_PRINT_EMMC_ERROR = 0x3012001C, ///< 内部存储器发生错误，如果出现该状态，不能支持逐份，面板需要提示用户内部存储器损坏，等待用户点击确定后取消作业
    STATUS_E_PRINT_UDISK_PARSE_ERROR = 0x3012001D, ///< U盘打印解析错误
    STATUS_E_PRINT_NETWORK_ACESS_ALARM = 0x3012001E, ///< 外网接入警报
    STATUS_E_PRINT_ENGINE_SYSTEM_ABNORMAL = 0x3012001F, ///< 引擎系统异常
    STATUS_E_PRINT_JAM_TRAY_1_SECTION = 0x30120020, ///< 纸盒1卡纸
    STATUS_E_PRINT_JAM_TRAY_2_SECTION = 0x30120021, ///< 纸盒2卡纸
    STATUS_E_PRINT_JAM_TRAY_3_SECTION = 0x30120022, ///< 纸盒3卡纸
    STATUS_E_PRINT_JAM_TRAY_4_SECTION = 0x30120023, ///< 纸盒4卡纸
    STATUS_E_PRINT_JAM_LCT_IN_SECTION = 0x30120024, ///< 内置大容量纸盒卡纸
    STATUS_E_PRINT_JAM_LCT_EX_SECTION = 0x30120025, ///< 外置大容量纸盒卡纸
    STATUS_E_PRINT_JAM_TRAY_MANUAL_SECTION = 0x30120026, ///< 多功能进纸器卡纸
    STATUS_E_PRINT_TRAY_1_OPEN = 0x30120027, ///< 纸盒1未插入
    STATUS_E_PRINT_TRAY_2_OPEN = 0x30120028, ///< 纸盒2未插入
    STATUS_E_PRINT_TRAY_3_OPEN = 0x30120029, ///< 纸盒3未插入
    STATUS_E_PRINT_TRAY_4_OPEN = 0x3012002A, ///< 纸盒4未插入
    STATUS_E_PRINT_LCT_IN_OPEN = 0x3012002B, ///< 内置大容量纸盒未插入
    STATUS_E_PRINT_LCT_EX_UNSET = 0x3012002C, ///< 外置大容量纸盒未设置
    STATUS_E_PRINT_TRAY_LCT_IN_SET_ERROR = 0x3012002D, ///< 内置大容量纸盒安装出错
    STATUS_E_PRINT_TRAY_1_PAPER_EMPTY = 0x3012002E, ///< 纸盒1缺纸
    STATUS_E_PRINT_TRAY_2_PAPER_EMPTY = 0x3012002F, ///< 纸盒2缺纸
    STATUS_E_PRINT_TRAY_3_PAPER_EMPTY = 0x30120030, ///< 纸盒3缺纸
    STATUS_E_PRINT_TRAY_4_PAPER_EMPTY = 0x30120031, ///< 纸盒4缺纸
    STATUS_E_PRINT_LCT_IN_PAPER_EMPTY = 0x30120032, ///< 内置大容量纸盒缺纸
    STATUS_E_PRINT_LCT_EX_PAPER_EMPTY = 0x30120033, ///< 外置大容量纸盒缺纸
    STATUS_E_PRINT_TRAY_MANUAL_PAPER_EMPTY = 0x30120034, ///< 多功能进纸器缺纸
    STATUS_E_PRINT_TRAY_1_SIZE_ERROR = 0x30120035, ///< 纸盒1纸张尺寸不匹配
    STATUS_E_PRINT_TRAY_2_SIZE_ERROR = 0x30120036, ///< 纸盒2纸张尺寸不匹配
    STATUS_E_PRINT_TRAY_3_SIZE_ERROR = 0x30120037, ///< 纸盒3纸张尺寸不匹配
    STATUS_E_PRINT_TRAY_4_SIZE_ERROR = 0x30120038, ///< 纸盒4纸张尺寸不匹配
    STATUS_E_PRINT_TRAY_MANUAL_SIZE_ERROR = 0x30120039, ///< 多功能进纸器纸张尺寸不匹配
    STATUS_E_PRINT_LCT_IN_SIZE_ERROR = 0x3012003A, ///< 内置大容量纸盒纸张尺寸不匹配
    STATUS_E_PRINT_LCT_EX_SIZE_ERROR = 0x3012003B, ///< 外置大容量纸盒纸张尺寸不匹配
    STATUS_E_PRINT_TRAY_RECEIVE_STANDARD_FULL = 0x3012003C, ///< 标准接纸架满
    STATUS_E_PRINT_TRAY_RECEIVE_1_FULL = 0x3012003D, ///< 接纸架1满
    STATUS_E_PRINT_TRAY_RECEIVE_2_FULL = 0x3012003E, ///< 接纸架2满
    STATUS_E_PRINT_TRAY_RECEIVE_3_FULL = 0x3012003F, ///< 接纸架3满
    STATUS_E_PRINT_FRONT_DOOR_OPEN = 0x30120040, ///< 前盖打开(盖门A打开)
    STATUS_E_PRINT_SIDE_DOOR_OPEN = 0x30120041, ///< 侧门打开(盖门C打开)
    STATUS_E_PRINT_CABINET_SIDE_DOOR_OPEN = 0x30120042, ///< 内建大容量盖门打开(盖门D打开)
    STATUS_E_PRINT_TONER_REPLENISHMENT_DOOR_OPEN = 0x30120043, ///< 粉盒补给门打开
    STATUS_E_PRINT_LCT_EX_OPEN = 0x30120044, ///< 外置大容量纸盒打开(盖门F打开)
    STATUS_E_PRINT_FNS_HORIZONTAL_UNIT_DOOR_OPEN = 0x30120045, ///< 装订器水平传送单元门开(盖门G打开)
    STATUS_E_PRINT_FNS_FRONT_DOOR_OPEN = 0x30120046, ///< 装订器前门(盖门H打开)
    STATUS_E_PRINT_FNS_TOP_COVER_OPEN = 0x30120047, ///< 装订器顶盖(盖门J打开)
    STATUS_E_PRINT_3RD_TRAY_COVER_OPEN = 0x30120048, ///< 第三托盘盖打开
    STATUS_E_PRINT_Y_TONER_EMPTY = 0x30120049, ///< 黄色碳粉盒空
    STATUS_E_PRINT_M_TONER_EMPTY = 0x3012004A, ///< 品红色碳粉盒空
    STATUS_E_PRINT_C_TONER_EMPTY = 0x3012004B, ///< 青色碳粉盒空
    STATUS_E_PRINT_K_TONER_EMPTY = 0x3012004C, ///< 黑色碳粉盒空
    STATUS_E_PRINT_W_TB_UNINSTALL = 0x3012004D, ///< 废粉盒缺失或者不响应
    STATUS_E_PRINT_Y_TB_UNINSTALL = 0x3012004E, ///< 黄色碳粉盒缺失或者不响应
    STATUS_E_PRINT_M_TB_UNINSTALL = 0x3012004F, ///< 品红色碳粉盒缺失或者不响应
    STATUS_E_PRINT_C_TB_UNINSTALL = 0x30120050, ///< 青色碳粉盒缺失或者不响应
    STATUS_E_PRINT_K_TB_UNINSTALL = 0x30120051, ///< 黑色碳粉盒缺失或者不响应
    STATUS_E_PRINT_Y_DR_UNINSTALL = 0x30120052, ///< 黄色鼓组件缺失或者不响应
    STATUS_E_PRINT_M_DR_UNINSTALL = 0x30120053, ///< 品红色鼓组件缺失或者不响应
    STATUS_E_PRINT_C_DR_UNINSTALL = 0x30120054, ///< 青色鼓组件缺失或者不响应
    STATUS_E_PRINT_K_DR_UNINSTALL = 0x30120055, ///< 黑色鼓组件缺失或者不响应
    STATUS_E_PRINT_Y_DR_LIFE_STOP = 0x30120056, ///< 黄色鼓组件寿命尽
    STATUS_E_PRINT_M_DR_LIFE_STOP = 0x30120057, ///< 品红色鼓组件寿命尽
    STATUS_E_PRINT_C_DR_LIFE_STOP = 0x30120058, ///< 青色鼓组件寿命尽
    STATUS_E_PRINT_K_DR_LIFE_STOP = 0x30120059, ///< 黑色鼓组件寿命尽
    STATUS_E_PRINT_Y_DV_LIFE_STOP = 0x3012005A, ///< 黄色显影组件寿命尽
    STATUS_E_PRINT_M_DV_LIFE_STOP = 0x3012005B, ///< 品红色显影组件寿命尽
    STATUS_E_PRINT_C_DV_LIFE_STOP = 0x3012005C, ///< 青色显影组件寿命尽
    STATUS_E_PRINT_K_DV_LIFE_STOP = 0x3012005D, ///< 黑色显影组件寿命尽
    STATUS_E_PRINT_Y_DV_UNINSTALL = 0x3012005E, ///< 黄色显影组件缺失或者不响应
    STATUS_E_PRINT_M_DV_UNINSTALL = 0x3012005F, ///< 品红色显影组件缺失或者不响应
    STATUS_E_PRINT_C_DV_UNINSTALL = 0x30120060, ///< 青色显影组件缺失或者不响应
    STATUS_E_PRINT_K_DV_UNINSTALL = 0x30120061, ///< 黑色显影组件缺失或者不响应
    STATUS_E_PRINT_JAM_TRAY_1_PAPER_FEED_SECTION = 0x30120062, ///< 纸盒1进纸口卡纸
    STATUS_E_PRINT_JAM_TRAY_2_PAPER_FEED_SECTION = 0x30120063, ///< 纸盒2进纸口卡纸
    STATUS_E_PRINT_JAM_TRAY_3_PAPER_FEED_SECTION = 0x30120064, ///< 纸盒3进纸口卡纸
    STATUS_E_PRINT_JAM_TRAY_4_PAPER_FEED_SECTION = 0x30120065, ///< 纸盒4进纸口卡纸
    STATUS_E_PRINT_JAM_LCT_EX_PAPER_FEED_SECTION = 0x30120066, ///< 大容量纸盒卡纸
    STATUS_E_PRINT_JAM_TRAY_MANUAL_FEED_SECTION = 0x30120067, ///< 多功能进纸器卡纸
    STATUS_E_PRINT_JAM_REFEEDER_SECTION = 0x30120068, ///< 重新进纸处卡纸
    STATUS_E_PRINT_JAM_VERTICAL_TRANSPORT_SECTION1 = 0x30120069, ///< 纵向传输处卡纸
    STATUS_E_PRINT_JAM_LCT_EX_TRANSPORT_SECTION = 0x3012006A, ///< 外置大容量纸盒传输处卡纸
    STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION_1 = 0x3012006B, ///< 第二转印处卡纸SECTION1
    STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION1 = 0x3012006C, ///< 双面传输处卡纸
    STATUS_E_PRINT_JAM_OUTPUT_SECTION = 0x3012006D, ///< 出纸处卡纸
    STATUS_E_PRINT_JAM_VER_TRANSPORT_SECTION2 = 0x3012006E, ///< 纵向传输处卡纸SECTION2
    STATUS_E_PRINT_JAM_2ND_TRANSFER_SECTION2 = 0x3012006F, ///< 第二转印处卡纸
    STATUS_E_PRINT_JAM_PAPER_EXIT_SECTION = 0x30120070, ///< 出纸处卡纸
    STATUS_E_PRINT_JAM_TRAY_REFEED_SECTION = 0x30120071, ///< 重新进纸处卡纸
    STATUS_E_PRINT_JAM_DUPLEX_TRANSPORT_SECTION2 = 0x30120072, ///< 双面传输处卡纸
    STATUS_E_PRINT_FUSING_UNIT_LIFE_STOP = 0x30120073, ///< 定影组件寿命尽
    STATUS_E_PRINT_TRANSFER_BELT_UNIT_LIFE_STOP = 0x30120074, ///< 转印带寿命尽
    STATUS_E_PRINT_TRANSFER_ROLLER_UNIT_LIFE_STOP = 0x30120075, ///< 转印辊寿命尽
    STATUS_E_PRINT_TONER_FILTER_LIFE_STOP = 0x30120076, ///< 粉盒过滤网寿命尽
    STATUS_E_PRINT_JAM_FNS_1ST_PROCESS_SECTION = 0x30120077, ///< 第一处理托盘处卡纸
    STATUS_E_PRINT_JAM_FNS_1ST_TRAY_EXIT_SECTION = 0x30120078, ///< 接纸架1出纸处卡纸
    STATUS_E_PRINT_JAM_FNS_2ND_DISCHARGE_SECTION = 0x30120079, ///< 第二托盘排纸处卡纸
    STATUS_E_PRINT_JAM_FNS_TRANSFER_PART1 = 0x3012007A, ///< 传输部分1卡纸
    STATUS_E_PRINT_JAM_FNS_3RD_TRAY_SECTION = 0x3012007B, ///< 第三托盘处
    STATUS_E_PRINT_JAM_FNS_2ND_PROCESS_SECTION = 0x3012007C, ///< 第二处理托盘处卡纸
    STATUS_E_PRINT_JAM_FNS_ENTRANCE_SECTION = 0x3012007D, ///< 装订器入口处卡纸
    STATUS_E_PRINT_JAM_FNS_HOR_TRANSPORT_SECTION = 0x3012007E, ///< 水平传输处卡纸
    STATUS_E_PRINT_JAM_FNS_FOLDER_PASS_SECTION = 0x3012007F, ///< 折叠通过处卡纸
    STATUS_E_PRINT_JAM_FNS_FOLDER_EXIT_SECTION = 0x30120080, ///< 折叠出纸处卡纸
    STATUS_E_PRINT_JAM_TRANSPORT_SECTION = 0x30120081, ///< 传送处卡纸
    STATUS_E_PRINT_JAM_LCT_IN_PAPER_FEED_SECTION = 0x30120082, ///< 内建大容量纸盒卡纸
    STATUS_E_PRINT_JAM_LCT_IN_TRANSPORT_SECTION = 0x30120083, ///< 内建大容量纸盒传输处卡纸
    STATUS_E_PRINT_JAM_SADDLE_STITCHER_EN_NOT_ON = 0x30120084, ///< 鞍式装订入口传感器未接通卡纸
    STATUS_E_PRINT_JAM_SADDLE_STITCHER_EX_NOT_OFF = 0x30120085, ///< 鞍式装订出口传感器未关闭卡纸
    STATUS_E_PRINT_JAM_FNS_ENTRANCE_NOT_ON = 0x30120086, ///< 装订器入口传感器未接通卡纸
    STATUS_E_PRINT_JAM_REGISTRATION_FRONT_NOT_OFF = 0x30120087, ///< 装订器入口传感器未关闭卡纸
    STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_ON = 0x30120088, ///< 主托盘出纸传感器未接通卡纸
    STATUS_E_PRINT_JAM_MAIN_TRAY_EJECTION_NOT_OFF = 0x30120089, ///< 主托盘出纸传感器未关闭卡纸
    STATUS_E_PRINT_JAM_STAPLER_STACK_TRAY_NOT_OFF = 0x3012008A, ///< 平订纸张检测传感器未关闭卡纸
    STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_ON = 0x3012008B, ///< 次托盘出纸传感器未接通卡纸
    STATUS_E_PRINT_JAM_SUB_TRAY_EXIT_NOT_OFF = 0x3012008C, ///< 次托盘出纸传感器未关闭卡纸
    STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_ON = 0x3012008D, ///< 折叠出纸传感器未接通卡纸
    STATUS_E_PRINT_JAM_FOLD_EJECTION_NOT_OFF = 0x3012008E, ///< 折叠出纸传感器未关闭卡纸
    STATUS_E_PRINT_JAM_FS_SADDLE_EN_NOT_OFF = 0x3012008F, ///< FS534鞍式装订入口传感器未关闭卡纸
    STATUS_E_PRINT_JAM_CENTER_FOLD_STACK_NOT_ON = 0x30120090, ///< 居中对折纸张检测传感器未接通卡纸
    STATUS_E_PRINT_JAM_ZU_LARGE_SIZE_MODE = 0x30120091, ///< 第一折叠尖端切换卡纸
    STATUS_E_PRINT_JAM_ZU_ALL_MODE = 0x30120092, ///< 第二折叠尖端L折卡纸
    STATUS_E_PRINT_JAM_PUNCH = 0x30120093, ///< 打孔卡纸
    STATUS_E_PRINT_JAM_FNS_STAPLE = 0x30120094, ///< 装订卡纸
    STATUS_E_PRINT_JAM_SADDLE_STITCHER_STAPLE = 0x30120095, ///< 鞍式装订卡纸
    STATUS_E_PRINT_JAM_SADDLE_EN_NOT_OFF = 0x30120096, ///< 鞍式装订入口传感器未关闭卡纸
    STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_ON = 0x30120097, ///< 托盘3输送传感器未打开卡纸
    STATUS_E_PRINT_JAM_TRAY3_CONVEYANCE_OFF = 0x30120098, ///< 托盘3输送传感器未关闭卡纸
    STATUS_E_PRINT_JAM_MAIN_PADDLE_HOME_SNR = 0x30120099, ///< MAIN_PADDLE_HOME_SNR
    STATUS_E_PRINT_JAM_EJECT_GRIP = 0x3012009A, ///< EJECT_GRIP
    STATUS_E_PRINT_JAM_ETAMPER = 0x3012009B, ///< ETAMPER
    STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_ON = 0x3012009C, ///< 过桥单元入口传感器未接通卡纸
    STATUS_E_PRINT_JAM_RU_ENTRANCE_NOT_OFF = 0x3012009D, ///< 过桥单元入口传感器未关闭卡纸
    STATUS_E_PRINT_JAM_PUNCH_REAR_DETECTION_OFF = 0x3012009E, ///< 打空检测传感器未关闭卡纸
    STATUS_E_PRINT_CONTROL_JAM_1 = 0x3012009F, ///< 控制卡纸1
    STATUS_E_PRINT_CONTROL_JAM_2 = 0x301200A0, ///< 控制卡纸2
    STATUS_E_PRINT_CONTROL_JAM_3 = 0x301200A1, ///< 控制卡纸3
    STATUS_E_PRINT_CONTROL_JAM_4 = 0x301200A2, ///< 控制卡纸4
    STATUS_E_PRINT_CONTROL_JAM_5 = 0x301200A3, ///< 控制卡纸5
    STATUS_E_PRINT_CONTROL_JAM_6 = 0x301200A4, ///< 控制卡纸6
    STATUS_E_PRINT_CONTROL_JAM_7 = 0x301200A5, ///< 控制卡纸7
    STATUS_E_PRINT_CONTROL_JAM_8 = 0x301200A6, ///< 控制卡纸8
    STATUS_E_PRINT_CONTROL_JAM_9 = 0x301200A7, ///< 控制卡纸9
    STATUS_E_PRINT_CONTROL_JAM_10 = 0x301200A8, ///< 控制卡纸10
    STATUS_E_PRINT_CONTROL_JAM_11 = 0x301200A9, ///< 控制卡纸11
    STATUS_E_PRINT_CONTROL_JAM_12 = 0x301200AA, ///< 控制卡纸12
    STATUS_E_PRINT_CONTROL_JAM_13 = 0x301200AB, ///< 控制卡纸13
    STATUS_E_PRINT_CONTROL_JAM_14 = 0x301200AC, ///< 控制卡纸14
    STATUS_E_PRINT_CONTROL_JAM_15 = 0x301200AD, ///< 控制卡纸15
    STATUS_E_PRINT_DUPLEX_DISABLE = 0x301200AE, ///< 禁止双面打印
    STATUS_E_PRINT_ATTRIBUTE_MUTEX_INCORRECT = 0x301200AF, ///< 非法互斥错误
    //STATUS_E_PRINT_ATTRIBUTE_COMPONENT_ERROR = 0x301200B0, ///< 非法部件错误------------->状态移除
    //STATUS_E_PRINT_TRAY_OPEN = 0x301200B1, ///< 纸盒打开错误------------->状态移除
    //STATUS_E_PRINT_TRAY_EMPTY = 0x301200B2, ///< 纸盒空错误------------->状态移除
    //STATUS_E_PRINT_TRAY_PAPER_TYPE_MISMATCH = 0x301200B3, ///< 纸盒介质不匹配错误------------->状态移除
    //STATUS_E_PRINT_TRAY_SIZE_MISMATCH = 0x301200B4, ///< 纸盒纸张不匹错误------------->状态移除
    STATUS_E_PRINT_JAM_SADDLE_STITCHER_EXIT_OFF = 0x301200B5, ///< 接纸架3出纸传感器未接通卡纸
    STATUS_E_PRINT_Y_DV_MISMATCH = 0x301200B6, ///< Y通道显影组件不匹配
    STATUS_E_PRINT_M_DV_MISMATCH = 0x301200B7, ///< M通道显影组件不匹配
    STATUS_E_PRINT_C_DV_MISMATCH = 0x301200B8, ///< C通道显影组件不匹配
    STATUS_E_PRINT_K_DV_MISMATCH = 0x301200B9, ///< K通道显影组件不匹配
    STATUS_E_PRINT_FEEDTRAY_1_TRAY_SET_E_ST = 0x301200BA, ///< 标准进纸盒设置错误状态
    STATUS_E_PRINT_FEEDTRAY_2_TRAY_SET_E_ST = 0x301200BB, ///< 第二进纸盒设置错误状态
    STATUS_E_PRINT_FEEDTRAY_LCT_IN_TRAY_SET_E_ST = 0x301200BC, ///< 内置大容量设置错误状态
    STATUS_E_PRINT_OPTION_TRAY_FULL = 0x301200BD, ///< 选择纸盒满
    STATUS_E_PRINT_PROCESS_TRAY = 0x301200BE, ///< 处理托盘异常（有纸）
    STATUS_E_PRINT_PROCESS_TRAY_1ST_BIN_FULL = 0x301200BF, ///< 第一处理纸盒满
    STATUS_E_PRINT_PROCESS_TRAY_2ND_BIN_FULL = 0x301200C0, ///< 第二处理纸盒满
    STATUS_E_PRINT_COLLATE_FILE = 0x301200C1, ///< 逐份作业错误
    STATUS_E_PRINT_PINCODE_FILE = 0x301200C2, ///< 密码作业错误
    STATUS_E_PRINT_INTERNAL_ERROR = 0x301200C3, ///< 打印内部错误
    STATUS_E_PRINT_DOC_PARSE_ERROR = 0x301200C4, ///< DOC解析异常
    STATUS_E_PRINT_SDK_ATTRIBUTE_INCORRECT = 0x301200C5, ///< SDK错误
    STATUS_E_PRINT_STITCHING_STAPLE_EMPTY = 0x301200C6, ///< 平订订书钉空
    STATUS_E_PRINT_SADDLE_BIND_F_STAPLE_EMPTY = 0x301200C7, ///< 鞍式装订订书钉（前）空
    STATUS_E_PRINT_SADDLE_BIND_B_STAPLE_EMPTY = 0x301200C8, ///< 鞍式装订订书钉（后）空
    STATUS_E_PRINT_STATUS_INTERNAL_PAGE_PDF_FAIL = 0x301200C9, ///< 内部页设备报告生成失败
    //STATUS_E_PRINT_TONER_EMPTY = 0x301200CA, ///< 粉尽错误------------->状态移除
    STATUS_E_PRINT_FEEDTRAY_3_TRAY_SET_E_ST = 0x301200CB, ///< 第三进纸盒设置错误状态
    STATUS_E_PRINT_FEEDTRAY_4_TRAY_SET_E_ST = 0x301200CC, ///< 第四进纸盒设置错误状态
    STATUS_E_PRINT_JOB_LOCKED = 0x301200CD, ///< 打印作业管控错误
    STATUS_E_PRINT_TRAY_4_PAPER_SIZE_MISMATCH = 0x301200CE, ///< 纸盒4纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_LCT_IN_PAPER_SIZE_MISMATCH = 0x301200CF, ///< 内置大容量纸盒纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_LCT_OUT_PAPER_SIZE_MISMATCH = 0x301200D0, ///< 外置大容量纸盒纸张尺寸不匹配,需要更换为XXX
    STATUS_E_PRINT_TRAY_4_PAPER_TYPE_MISMATCH = 0x301200D1, ///< 纸盒4纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_LCT_IN_PAPER_TYPE_MISMATCH = 0x301200D2, ///< 内置大容量纸盒纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_LCT_OUT_PAPER_TYPE_MISMATCH = 0x301200D3, ///< 外置大容量纸盒纸张类型不匹配,需要更换为XXXX
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_TRAY = 0x301200D4, ///< 纸盒组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_TONER = 0x301200D5, ///< 粉盒组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DRUM = 0x301200D6, ///< 鼓组件组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DV = 0x301200D7, ///< 显影组件组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_RECEIVE = 0x301200D8, ///< 接纸架组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_DOOR = 0x301200D9, ///< 门组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_PUNCH = 0x301200DA, ///< 打孔组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_BIND = 0x301200DB, ///< 订钉组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_FOLD = 0x301200DC, ///< 折叠组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_SHIFT = 0x301200DD, ///< 偏移组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_Z_FOLD = 0x301200DE, ///< Z折组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_FINISHER = 0x301200DF, ///< 装订器组件不支持
    STATUS_E_PRINT_ATTRIBUTE_COMPONENT_PRO_TRAY = 0x301200E0, ///< 处理托盘组件不支持
    STATUS_E_PRINT_STORAGE_JOB_ERROR = 0x301200E1, ///< 作业存储失败
    STATUS_E_PRINT_STAPLE_WASTE_FULL_OR_NOT_SET = 0x301200E5, ///< 平钉废盒已满或未安装
    STATUS_E_PRINT_PUNCH_WASTE_NOT_SET = 0x301200E6, ///< 打孔废料盒未安装
    STATUS_E_PRINT_PUNCH_WASTE_FULL = 0x301200E7, ///< 打孔废料盒满
    STATUS_E_PRINT_PROCESS_TRAY_1ST_EXIST_PAPER = 0x301200E8, ///< 第一处理托盘处异常有纸
    STATUS_E_PRINT_PROCESS_TRAY_2ND_EXIST_PAPER = 0x301200E9, ///< 第二处理托盘处异常有纸
    STATUS_E_PRINT_DALAY_JOB_WITHOUT_DISK = 0x301200EA, ///< 未安装硬盘，不支持延时打印作业
    STATUS_E_PRINT_SAMPLE_JOB_WITHOUT_DISK = 0x301200EB, ///< 未安装硬盘，不支持样本打印作业
    STATUS_E_PRINT_UNSUPPORT_JOB_TYPE    = 0x301201DC, ///< 不支持的作业类型
    STATUS_E_PRINT_UNATTAIN_JAM_FRONTEND = 0x3010006D, ///< 先端传感器未达JAM
    STATUS_E_PRINT_STRANDED_JAM_FRONTEND = 0x3010006E, ///< 先端传感器滞留JAM
    STATUS_E_PRINT_RESIDUAL_JAM_FRONTEND = 0x3010006F, ///< 先端传感器残留JAM
    STATUS_E_PRINT_TRAY_1_UNSUPPORT_PAPER = 0x301201DD,  ///< 纸盒1中纸张为不支持继续打印的纸张
    STATUS_E_PRINT_TRAY_2_UNSUPPORT_PAPER = 0x301201DE,  ///< 纸盒2中纸张为不支持继续打印的纸张
    STATUS_E_PRINT_TRAY_3_UNSUPPORT_PAPER = 0x301201DF,  ///< 纸盒3中纸张为不支持继续打印的纸张
    STATUS_E_PRINT_TRAY_4_UNSUPPORT_PAPER = 0x301201E0,  ///< 纸盒4中纸张为不支持继续打印的纸张
    STATUS_E_PRINT_MP_TRAY_UNSUPPORT_PAPER = 0x301201E1,  ///< 多功能进纸器中纸张为不支持继续打印的纸张
    STATUS_E_PRINT_LCT_IN_TRAY_UNSUPPORT_PAPER = 0x301201E2,  ///< 内置大容量纸盒中纸张为不支持继续打印的纸张
    STATUS_E_PRINT_COLOR_TONER_EMPTY = 0x301201E3,  ///< 彩色碳粉完全用尽
    STATUS_E_PRINT_PRE_PARSER_FAILED = 0x301201E4, ///< 预解析失败
    STATUS_F_PRINT_LSU_FAN_ERROR = 0x40100005, ///< LSU风扇异常
    STATUS_F_PRINT_FUSER_EXHAUST_FAN_ERROR = 0x40100006, ///< 定影排热风扇异常
    STATUS_F_PRINT_LSU_FAN_ERROR_2 = 0x40100007, ///< LSU风扇异常2
    STATUS_F_PRINT_MAIN_FAN_ERROR = 0x40100008, ///< 主风道风扇异常
    STATUS_F_PRINT_FUSER_TEMP_RAISED_SLOW = 0x40100009, ///< 通常电压时定影不能reload(预热温升过慢)
    STATUS_F_PRINT_FUSER_THERMISTOR_ERROR = 0x4010000A, ///< 定影开路：1
    STATUS_F_PRINT_FUSER_OVER_HEAT = 0x4010000B, ///< 定影高温检知（软件）1
    STATUS_F_PRINT_FUSER_HW_OVER_HEAT = 0x4010000C, ///< 定影高温检知（硬件）1
    STATUS_F_PRINT_FUSER_THERMISTOR_ERROR_3 = 0x4010000D, ///< 定影开路：3
    STATUS_F_PRINT_FUSER_OVER_HEAT_3 = 0x4010000E, ///< 定影高温检知（软件）3
    STATUS_F_PRINT_FUSER_HW_OVER_HEAT_3 = 0x4010000F, ///< 定影高温检知（硬件）3
    STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_1 = 0x40100010, ///< 定影温度过低1
    STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_2 = 0x40100011, ///< 定影温度过低2
    STATUS_F_PRINT_FUSER_TEMPERATURE_LOW_3 = 0x40100012, ///< 定影温度过低3
    STATUS_F_PRINT_THERMISTOR_DAMAGE_1 = 0x40100013, ///< 定影热敏电阻异常：1
    STATUS_F_PRINT_THERMISTOR_DAMAGE_2 = 0x40100014, ///< 定影热敏电阻异常：2
    STATUS_F_PRINT_THERMISTOR_DAMAGE_3 = 0x40100015, ///< 定影热敏电阻异常：3
    STATUS_F_PRINT_FUSER_MOTOR_ERROR = 0x40100016, ///< 定影马达异常
    STATUS_F_PRINT_COLOR_DEVELOP_MOTOR_ERROR = 0x40100017, ///< 彩色显影马达异常
    STATUS_F_PRINT_BLACKDEVELOP_MOTOR_ERROR = 0x40100018, ///< 黑色显影马达异常
    STATUS_F_PRINT_MULTI_PRISM_MOTOR_ON_TIMEOUT = 0x40100019, ///< 多棱镜马达异常：ON时超时
    STATUS_F_PRINT_MULTI_PRISM_MOTOR_OFF_TIMEOUT = 0x4010001A, ///< 多棱镜马达异常：OFF时超时
    STATUS_F_PRINT_MULTI_PRISM_MOTOR_SIGNAL_ERROR = 0x4010001B, ///< 多棱镜马达异常：XFERDY信号错误
    STATUS_F_PRINT_LD0_COCURRENT_K_ERROR = 0x4010001C, ///< K色：先端：LD0同期检知异常
    STATUS_F_PRINT_LD0_COCURRENT_Y_ERROR = 0x4010001D, ///< Y色：先端：LD0同期检知异常
    STATUS_F_PRINT_MCU_COMMUNICATION_ERROR = 0x4010001E, ///< 主副MCU通信异常
    STATUS_F_PRINT_TRAY_COMMUNICATION_ERROR = 0x4010001F, ///< 二层纸盒通信异常
    STATUS_F_PRINT_EEPROM_IIC_COMMUNICATION_ERROR = 0x40100020, ///< EEPROMIIC通信异常
    STATUS_F_PRINT_HUMITURE_IIC_COMMUNICATION_ERROR = 0x40100021, ///< 温湿度IIC通信异常
    STATUS_F_PRINT_CTL_VIDEO_OK_NOTIFY_ERROR = 0x40100022, ///< CTL侧画像准备OK通知未送达
    STATUS_F_PRINT_EC_WARMUP_CANNOT_STOP = 0x40100023, ///< 引擎固件Bug之机内监视模块的预热动作无法结束
    STATUS_F_PRINT_EC_PAGE_BUFFER_FULL = 0x40100024, ///< 引擎固件Bug之打印页数管理bufferfull
    STATUS_F_PRINT_EC_PAGE_CANNOT_START = 0x40100025, ///< 引擎固件Bug之某页打印执行开始不可
    STATUS_F_PRINT_EC_PAGE_CANNOT_STOP = 0x40100026, ///< 引擎固件Bug之某页打印执行结束不可
    STATUS_F_PRINT_EC_PRINT_CANNOT_STOP = 0x40100027, ///< 引擎固件Bug之打印整体时序结束不可
    STATUS_F_PRINT_ENGINE_PRARAM_FILE = 0x40100028, ///< 引擎参数文件异常
    STATUS_F_PRINT_FACTORY_FILE = 0x40100029, ///< 工厂校验文件异常
    STATUS_F_PRINT_CALIBRATION_PARAM = 0x4010002A, ///< 色彩校正参数异常
    STATUS_F_PRINT_TEMPERATURE_UNSTABLE_1 = 0x4010002B, ///< 温度不安定1
    STATUS_F_PRINT_FUSER_THERMISTOR_ERROR_2 = 0x4010002C, ///< 定影开路：2
    STATUS_F_PRINT_FUSER_OVER_HEAT_2 = 0x4010002D, ///< 定影高温检知(软件)2
    STATUS_F_PRINT_FUSER_THERMISTOR_DAMAGE_2 = 0x4010002E, ///< 定影热敏电阻损坏：2
    STATUS_F_PRINT_FUSER_PRESSURE_DECOMPRESS_TIMEOUT = 0x4010002F, ///< 定影加/解压超时
    STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_K = 0x40100030, ///< 充电出力异常-K色
    STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_M = 0x40100031, ///< 充电出力异常-M色
    STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_Y = 0x40100032, ///< 充电出力异常-Y色
    STATUS_F_PRINT_CHARGEING_OUTPUT_ERROR_C = 0x40100033, ///< 充电出力异常-C色
    STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_K = 0x40100034, ///< 显影高压出力异常-K色
    STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_M = 0x40100035, ///< 显影高压出力异常-M色
    STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_Y = 0x40100036, ///< 显影高压出力异常-Y色
    STATUS_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_C = 0x40100037, ///< 显影高压出力异常-C色
    STATUS_F_PRINT_TONER_SUPPLY_MOTOR_ERROR = 0x40100038, ///< 补粉马达稳定异常
    STATUS_F_PRINT_BK_PHOTORECEPTOR_MOTOR_ERROR = 0x40100039, ///< BK感光体马达异常
    STATUS_F_PRINT_COLOR_PHOTORECEPTRO_MOTOR_ERROR = 0x4010003A, ///< 彩色感光体马达异常
    STATUS_F_PRINT_1TB_COMPRESS_MOTOR_ERROR = 0x4010003B, ///< 1TB加压马达异常
    STATUS_F_PRINT_NEW_DRUM_EXCHANGE_ERROR = 0x4010003C, ///< 新光鼓单元交换异常
    STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_K = 0x4010003D, ///< TC传感上限异常:K
    STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_M = 0x4010003E, ///< TC传感上限异常:M
    STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_C = 0x4010003F, ///< TC传感上限异常:C
    STATUS_F_PRINT_TC_SENSE_UPPER_LIMIT_ERROR_Y = 0x40100040, ///< TC传感上限异常:Y
    STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_K = 0x40100041, ///< TC传感下限异常:K
    STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_M = 0x40100042, ///< TC传感下限异常:M
    STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_C = 0x40100043, ///< TC传感下限异常:C
    STATUS_F_PRINT_TC_SENSE_LOWER_LIMIT_ERROR_Y = 0x40100044, ///< TC传感下限异常:Y
    STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_K = 0x40100045, ///< TC传感初期设定调整异常:K
    STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_M = 0x40100046, ///< TC传感初期设定调整异常:M
    STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_C = 0x40100047, ///< TC传感初期设定调整异常:C
    STATUS_F_PRINT_TC_SENSE_INIT_ADJUST_ERROR_Y = 0x40100048, ///< TC传感初期设定调整异常:Y
    STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_K = 0x40100049, ///< TC传感采样异常:K
    STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_M = 0x4010004A, ///< TC传感采样异常:M
    STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_C = 0x4010004B, ///< TC传感采样异常:C
    STATUS_F_PRINT_TC_SENSE_SAMPLING_ERROR_Y = 0x4010004C, ///< TC传感采样异常:Y
    STATUS_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_1 = 0x4010004D, ///< 中间转写高压
    STATUS_F_PRINT_TRANS_MOTOR_ERROR_1 = 0x4010004E, ///< 中间转印马达
    STATUS_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_2 = 0x4010004F, ///< 二次转印高压电源异常
    STATUS_F_PRINT_TRANS_SEPARATION_MOTOR_ERROR_2 = 0x40100050, ///< 二次转印接离马达异常
    STATUS_F_PRINT_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2 = 0x40100051, ///< 二次转印高压恒压出力异常
    STATUS_F_PRINT_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2 = 0x40100052, ///< 二次转印高压恒流出力异常
    STATUS_F_PRINT_TRANS_SEPARATION_HIGH_VOL_ERROR = 0x40100053, ///< 分离高压异常
    STATUS_F_PRINT_REMOVE_LAMP_ERROR = 0x40100054, ///< 除电灯异常
    STATUS_F_PRINT_HIGH_VOL_PLATE_INSERT_ERROR = 0x40100055, ///< 高压板插入异常
    STATUS_F_PRINT_HIGH_VOL_DETECTION_ERROR = 0x40100056, ///< 高压检测故障
    STATUS_F_PRINT_LSU_CABLE_NOT_CONNECTED = 0x40100057, ///< LSU排线检测：LSU排线未连接
    STATUS_F_PRINT_DEVELOP_CO_HIGH_ERROR_K = 0x40100058, ///< 显影组件浓度异常高K通道
    STATUS_F_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_K = 0x4010005C, ///< 浓度传感器输出电压超出下限K通道
    STATUS_F_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_K = 0x40100060, ///< 浓度传感器输出电压超出上限K通道
    STATUS_F_PRINT_CO_SENSOR_DETECT_ERROR_K = 0x40100064, ///< 浓度传感器异常K通道
    STATUS_F_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_K = 0x40100068, ///< 碳粉供应故障K通道
    STATUS_F_PRINT_TONER_SUPPLY_LOWER_TEN_ERROR_K = 0x4010006C, ///< 碳粉供应故障2(粉桶寿命在[5%～10%]范围内)K通道
    STATUS_F_PRINT_TONER_SUPPLY_LOWER_FIVE_ERROR_K = 0x40100070, ///< 粉桶碳粉空错误(粉桶寿命小于5%)K通道
    STATUS_F_PRINT_ENV_TEMP_BOTH_ERROR = 0x40100074, ///< 内外环境温湿度传感器均异常
    STATUS_F_PRINT_LD0_COCURRENT_K_UNSTABLE = 0x40100075, ///< K色：LD0同期信号不稳定
    STATUS_F_PRINT_LD0_COCURRENT_Y_UNSTABLE = 0x40100076, ///< Y色：LD0同期信号不稳定
    STATUS_F_PRINT_EC_CALIBRATION_CANNOT_STOP = 0x40100077, ///< 引擎固件BUG之色彩校正执行结束不可
    STATUS_F_PRINT_COMPLETE_COOLING_FAN1_ERROR = 0x40100078, ///< 整机散热风扇1异常
    STATUS_F_PRINT_LSU_FRONT_COOLING_FAN_ERROR = 0x40100079, ///< LSU前侧散热风扇异常
    STATUS_F_PRINT_FUSER_EXIT_COOLING_FAN_ERROR = 0x4010007A, ///< 定影出口散热风扇异常
    STATUS_F_PRINT_IMAGING_MAIN_MOTOR_K_ERROR = 0x4010007B, ///< 主马达K异常
    STATUS_F_PRINT_IMAGING_MAIN_MOTOR_YMC_ERROR = 0x4010007C, ///< 主马达YMC异常
    STATUS_F_PRINT_FE0340_01 = 0x4010007D, ///< 除电灯异常:K
    STATUS_F_PRINT_FE0340_02 = 0x4010007E, ///< 除电灯异常:M
    STATUS_F_PRINT_FE0340_03 = 0x4010007F, ///< 除电灯异常:C
    STATUS_F_PRINT_FE0340_04 = 0x40100080, ///< 除电灯异常:Y
    STATUS_F_PRINT_FE0154_00 = 0x40100081, ///< 定影高温检知（硬件）：2
    STATUS_F_PRINT_FE0164_00 = 0x40100082, ///< 定影高温检知（硬件）：3
    STATUS_F_PRINT_FE0170_01 = 0x40100083, ///< 过零信号异常(定影继电器熔着)
    STATUS_F_PRINT_FE0170_02 = 0x40100084, ///< 过零信号异常(无过零信号)
    STATUS_F_PRINT_FE0170_03 = 0x40100085, ///< 过零信号异常(过零信号频率过低)
    STATUS_F_PRINT_FE0172_01 = 0x40100086, ///< 定影加压超时异常
    STATUS_F_PRINT_FE0172_02 = 0x40100087, ///< 定影解压超时异常
    STATUS_F_PRINT_FE0174_00 = 0x40100088, ///< 定影未插入
    STATUS_F_PRINT_FE0280_01 = 0x40100089, ///< 黑色相位传感器异常
    STATUS_F_PRINT_FE0280_02 = 0x4010008A, ///< 彩色相位传感器异常
    STATUS_F_PRINT_FE0820_00 = 0x4010008B, ///< 选配通信异常
    STATUS_F_PRINT_FE0821_00 = 0x4010008C, ///< 103通信异常
    STATUS_F_PRINT_FE0822_00 = 0x4010008D, ///< 003通信异常
    STATUS_F_PRINT_FE0840_00 = 0x4010008E, ///< EEPROM通信异常
    STATUS_F_PRINT_FE0171_01 = 0x4010008F, ///< 继电器短路
    STATUS_F_PRINT_FE0171_02 = 0x40100090, ///< 可控硅短路
    STATUS_F_PRINT_FE0903_11 = 0x40100091, ///< 引擎固件Bug之自动供粉执行无法结束
    STATUS_F_PRINT_MAIN_MOTOR_ERROR = 0x40100092, ///< 主马达异常
    STATUS_F_PRINT_FE0830_00 = 0x40100093, ///< IUChip通信异常
    STATUS_F_PRINT_FE0180_00 = 0x40100094, ///< AC输入电压过低
    STATUS_F_PRINT_FE0180_01 = 0x40100095, ///< AC输入电压过高
    STATUS_F_PRINT_FE0181_00 = 0x40100096, ///< 全功率加热时间过长
    STATUS_F_PRINT_FE0182_00 = 0x40100097, ///< 休眠模式异常加热
    STATUS_F_PRINT_FE0183_00 = 0x40100098, ///< 定影加温异常（产品安全MCU检知）
    STATUS_F_PRINT_FE0184_00 = 0x40100099, ///< 定影温度采集差值过大
    STATUS_F_PRINT_FE_UNKNOWN = 0x4010009A, ///< 引擎升起协议中未定义的FE状态
    STATUS_F_PRINT_FE0142_01 = 0x4010009B, ///< 定影中央预热温升检测开始不可（通常电压）
    STATUS_F_PRINT_FE0142_03 = 0x4010009C, ///< 定影中央预热目标温度不可达（通常电压）
    STATUS_F_PRINT_FE0145_00 = 0x4010009D, ///< 定影中央加热器连续点灯
    STATUS_F_PRINT_FE0152_02 = 0x4010009E, ///< 定影端部预热温升过慢（通常电压）
    STATUS_F_PRINT_FE0171_00 = 0x4010009F, ///< 继电器和可控硅都短路
    STATUS_F_PRINT_FE0152_03 = 0x40100110, ///< 通常电压时定影预热不可(预热目标温度不可达)：2
    STATUS_F_PRINT_FE0153_00 = 0x40100111, ///< 定影高温检知（软件）：2
    STATUS_F_PRINT_FE0570_00 = 0x40100118, ///< 定影热敏电阻未插
    STATUS_F_PRINT_FE0171_03 = 0x40100100, ///< 继电器或可控硅断路
    STATUS_F_PRINT_FE0351_01 = 0x40100101, ///< 高压故障
    STATUS_F_PRINT_FE0351_02 = 0x40100102, ///< 高压故障
    STATUS_F_PRINT_FE0560_00 = 0x40100103, ///< 选配纸盒叠加段数异常
    STATUS_F_PRINT_FE0900_00 = 0x40100104, ///< 耗材通信异常
    STATUS_F_PRINT_FE0850_00 = 0x40100105, ///< 温湿度Sensor通信异常
    STATUS_F_PRINT_FE0175_00 = 0x40100106, ///< 全功率加热时间过长(SOC检知)
    STATUS_F_PRINT_FE0822_01 = 0x40100107, ///< 003通信异常（IIC转PWM）
    STATUS_F_PRINT_FE0361_00 = 0x40100108, ///< 室温过低，将影响正常打印，请尽快将室温调整到0℃以上
    STATUS_F_PRINT_FE0185_00 = 0x40100109, ///< 标准纸盒纸间距传感器异常
    STATUS_F_PRINT_FE0185_01 = 0x4010010A, ///< 多功能纸盒纸间距传感器异常
    STATUS_F_PRINT_FE0901_00 = 0x4010010B, ///< 安全003MCUACready通知未送达
    STATUS_F_PRINT_FE0904_01 = 0x4010010C, ///< 引擎参数文件异常（引擎不可恢复异常）
    STATUS_F_PRINT_FE0904_02 = 0x4010010D, ///< 工厂校验文件异常（引擎不可恢复异常）
    STATUS_F_PRINT_FE0904_03 = 0x4010010E, ///< 色彩校正参数异常（引擎不可恢复异常）
    STATUS_F_PRINT_ENGINE_COMMUNICATION_ERROR = 0x4010010F, ///< 引擎通讯异常
    STATUS_F_PRINT_FE0141_01 = 0x40100119, ///< 定影端部热敏电阻断线
    STATUS_F_PRINT_FE0142_04 = 0x4010011A, ///< 打印前定影自检温度120℃不可达异常
    STATUS_F_PRINT_FE0145_01 = 0x4010011B, ///< 定影加热器加热异常：中间
    STATUS_F_PRINT_FE0461_00 = 0x4010011C, ///< LD使能信号异常
    STATUS_F_PRINT_FE0461_01 = 0x4010011D, ///< LSU加密认证失败
    STATUS_F_PRINT_A_31_2 = 0x40120001, ///< A级致命错误31_2
    STATUS_F_PRINT_A_34_25 = 0x40120002, ///< A级致命错误34_25
    STATUS_F_PRINT_A_38_22 = 0x40120003, ///< A级致命错误38_22
    STATUS_F_PRINT_A_38_25 = 0x40120004, ///< A级致命错误38_25
    STATUS_F_PRINT_A_38_26 = 0x40120005, ///< A级致命错误38_26
    STATUS_F_PRINT_A_37_22 = 0x40120006, ///< A级致命错误37_22
    STATUS_F_PRINT_A_37_25 = 0x40120007, ///< A级致命错误37_25
    STATUS_F_PRINT_A_37_26 = 0x40120008, ///< A级致命错误37_26
    STATUS_F_PRINT_A_37_32 = 0x40120009, ///< A级致命错误37_32
    STATUS_F_PRINT_A_37_36 = 0x4012000A, ///< A级致命错误37_36
    STATUS_F_PRINT_A_37_37 = 0x4012000B, ///< A级致命错误37_37
    STATUS_F_PRINT_A_37_38 = 0x4012000C, ///< A级致命错误37_38
    STATUS_F_PRINT_A_39_22 = 0x4012000D, ///< A级致命错误39_22
    STATUS_F_PRINT_A_39_25 = 0x4012000E, ///< A级致命错误39_25
    STATUS_F_PRINT_A_39_26 = 0x4012000F, ///< A级致命错误39_26
    STATUS_F_PRINT_A_39_2B = 0x40120010, ///< A级致命错误39_2B
    STATUS_F_PRINT_A_3B_2 = 0x40120011, ///< A级致命错误3B_2
    STATUS_F_PRINT_A_3B_3 = 0x40120012, ///< A级致命错误3B_3
    STATUS_F_PRINT_A_3B_7 = 0x40120013, ///< A级致命错误3B_7
    STATUS_F_PRINT_A_3B_9 = 0x40120014, ///< A级致命错误3B_9
    STATUS_F_PRINT_B_51_3 = 0x40120015, ///< B级致命错误51_3
    STATUS_F_PRINT_B_22_4 = 0x40120016, ///< B级致命错误22_4
    STATUS_F_PRINT_B_22_53 = 0x40120017, ///< B级致命错误22_53
    STATUS_F_PRINT_B_22_54 = 0x40120018, ///< B级致命错误22_54
    STATUS_F_PRINT_B_22_55 = 0x40120019, ///< B级致命错误22_55
    STATUS_F_PRINT_B_22_56 = 0x4012001A, ///< B级致命错误22_56
    STATUS_F_PRINT_B_53_55 = 0x4012001B, ///< B级致命错误53_55
    STATUS_F_PRINT_B_53_60 = 0x4012001C, ///< B级致命错误53_60
    STATUS_F_PRINT_B_53_61 = 0x4012001D, ///< B级致命错误53_61
    STATUS_F_PRINT_B_53_62 = 0x4012001E, ///< B级致命错误53_62
    STATUS_F_PRINT_B_53_63 = 0x4012001F, ///< B级致命错误53_63
    STATUS_F_PRINT_B_53_58 = 0x40120020, ///< B级致命错误53_58
    STATUS_F_PRINT_B_53_4 = 0x40120021, ///< B级致命错误53_4
    STATUS_F_PRINT_B_33_2 = 0x40120022, ///< B级致命错误33_2
    STATUS_F_PRINT_B_33_6 = 0x40120023, ///< B级致命错误33_6
    STATUS_F_PRINT_B_33_7 = 0x40120024, ///< B级致命错误33_7
    STATUS_F_PRINT_B_23_50 = 0x40120025, ///< B级致命错误23_50
    STATUS_F_PRINT_B_23_57 = 0x40120026, ///< B级致命错误23_57
    STATUS_F_PRINT_B_53_51 = 0x40120027, ///< B级致命错误53_51
    STATUS_F_PRINT_B_53_6 = 0x40120028, ///< B级致命错误53_6
    STATUS_F_PRINT_B_23_55 = 0x40120029, ///< B级致命错误23_55
    STATUS_F_PRINT_B_32_1 = 0x4012002A, ///< B级致命错误32_1
    STATUS_F_PRINT_B_32_2 = 0x4012002B, ///< B级致命错误32_2
    STATUS_F_PRINT_B_1_6 = 0x4012002C, ///< B级致命错误1_6
    STATUS_F_PRINT_B_1_7 = 0x4012002D, ///< B级致命错误1_7
    STATUS_F_PRINT_B_1_8 = 0x4012002E, ///< B级致命错误1_8
    STATUS_F_PRINT_B_1_9 = 0x4012002F, ///< B级致命错误1_9
    STATUS_F_PRINT_B_21_52 = 0x40120030, ///< B级致命错误21_52
    STATUS_F_PRINT_B_21_53 = 0x40120031, ///< B级致命错误21_53
    STATUS_F_PRINT_B_21_54 = 0x40120032, ///< B级致命错误21_54
    STATUS_F_PRINT_B_21_55 = 0x40120033, ///< B级致命错误21_55
    STATUS_F_PRINT_B_21_56 = 0x40120034, ///< B级致命错误21_56
    STATUS_F_PRINT_B_31_1 = 0x40120035, ///< B级致命错误31_1
    STATUS_F_PRINT_B_31_03 = 0x40120036, ///< B级致命错误31_03
    STATUS_F_PRINT_B_31_04 = 0x40120037, ///< B级致命错误31_04
    STATUS_F_PRINT_B_41_1 = 0x40120038, ///< B级致命错误41_1
    STATUS_F_PRINT_B_45_1 = 0x40120039, ///< B级致命错误45_1
    STATUS_F_PRINT_B_3B_8 = 0x4012003A, ///< B级致命错误3B_8
    STATUS_F_PRINT_B_2_6 = 0x4012003B, ///< B级致命错误2_6
    STATUS_F_PRINT_B_2_4 = 0x4012003C, ///< B级致命错误2_4
    STATUS_F_PRINT_B_2_2 = 0x4012003D, ///< B级致命错误2_2
    STATUS_F_PRINT_B_2_16 = 0x4012003E, ///< B级致命错误2_16
    STATUS_F_PRINT_B_2_8 = 0x4012003F, ///< B级致命错误2_8
    STATUS_F_PRINT_B_2_11 = 0x40120040, ///< B级致命错误2_11
    STATUS_F_PRINT_B_2_10 = 0x40120041, ///< B级致命错误2_10
    STATUS_F_PRINT_B_2_14 = 0x40120042, ///< B级致命错误2_14
    STATUS_F_PRINT_B_11_E1 = 0x40120043, ///< B级致命错误11_E1
    STATUS_F_PRINT_B_11_E2 = 0x40120044, ///< B级致命错误11_E2
    STATUS_F_PRINT_B_11_A1 = 0x40120045, ///< B级致命错误11_A1
    STATUS_F_PRINT_B_11_A2 = 0x40120046, ///< B级致命错误11_A2
    STATUS_F_PRINT_B_11_C5 = 0x40120047, ///< B级致命错误11_C5
    STATUS_F_PRINT_B_13_1 = 0x40120048, ///< B级致命错误13_1
    STATUS_F_PRINT_B_25_51 = 0x40120049, ///< B级致命错误25_51
    STATUS_F_PRINT_B_25_52 = 0x4012004A, ///< B级致命错误25_52
    STATUS_F_PRINT_B_25_53 = 0x4012004B, ///< B级致命错误25_53
    STATUS_F_PRINT_B_25_54 = 0x4012004C, ///< B级致命错误25_54
    STATUS_F_PRINT_B_25_55 = 0x4012004D, ///< B级致命错误25_55
    STATUS_F_PRINT_B_25_56 = 0x4012004E, ///< B级致命错误25_56
    STATUS_F_PRINT_B_25_57 = 0x4012004F, ///< B级致命错误25_57
    STATUS_F_PRINT_B_25_58 = 0x40120050, ///< B级致命错误25_58
    STATUS_F_PRINT_B_25_59 = 0x40120051, ///< B级致命错误25_59
    STATUS_F_PRINT_B_25_5A = 0x40120052, ///< B级致命错误25_5A
    STATUS_F_PRINT_B_25_5B = 0x40120053, ///< B级致命错误25_5B
    STATUS_F_PRINT_B_25_5C = 0x40120054, ///< B级致命错误25_5C
    STATUS_F_PRINT_B_25_61 = 0x40120055, ///< B级致命错误25_61
    STATUS_F_PRINT_B_25_62 = 0x40120056, ///< B级致命错误25_62
    STATUS_F_PRINT_B_25_63 = 0x40120057, ///< B级致命错误25_63
    STATUS_F_PRINT_B_25_64 = 0x40120058, ///< B级致命错误25_64
    STATUS_F_PRINT_B_11_2 = 0x40120059, ///< B级致命错误11_2
    STATUS_F_PRINT_B_11_3 = 0x4012005A, ///< B级致命错误11_3
    STATUS_F_PRINT_B_11_4 = 0x4012005B, ///< B级致命错误11_4
    STATUS_F_PRINT_B_11_5 = 0x4012005C, ///< B级致命错误11_5
    STATUS_F_PRINT_B_11_6 = 0x4012005D, ///< B级致命错误11_6
    STATUS_F_PRINT_B_11_9 = 0x4012005E, ///< B级致命错误11_9
    STATUS_F_PRINT_B_11_12 = 0x4012005F, ///< B级致命错误11_12
    STATUS_F_PRINT_B_11_13 = 0x40120060, ///< B级致命错误11_13
    STATUS_F_PRINT_B_11_14 = 0x40120061, ///< B级致命错误11_14
    STATUS_F_PRINT_B_11_15 = 0x40120062, ///< B级致命错误11_15
    STATUS_F_PRINT_B_11_24 = 0x40120063, ///< B级致命错误11_24
    STATUS_F_PRINT_B_11_25 = 0x40120064, ///< B级致命错误11_25
    STATUS_F_PRINT_B_11_27 = 0x40120065, ///< B级致命错误11_27
    STATUS_F_PRINT_B_11_30 = 0x40120066, ///< B级致命错误11_30
    STATUS_F_PRINT_B_11_31 = 0x40120067, ///< B级致命错误11_31
    STATUS_F_PRINT_B_11_32 = 0x40120068, ///< B级致命错误11_32
    STATUS_F_PRINT_B_11_40 = 0x40120069, ///< B级致命错误11_40
    STATUS_F_PRINT_B_11_41 = 0x4012006A, ///< B级致命错误11_41
    STATUS_F_PRINT_B_11_42 = 0x4012006B, ///< B级致命错误11_42
    STATUS_F_PRINT_B_11_43 = 0x4012006C, ///< B级致命错误11_43
    STATUS_F_PRINT_B_11_44 = 0x4012006D, ///< B级致命错误11_44
    STATUS_F_PRINT_B_11_45 = 0x4012006E, ///< B级致命错误11_45
    STATUS_F_PRINT_B_11_46 = 0x4012006F, ///< B级致命错误11_46
    STATUS_F_PRINT_B_11_52 = 0x40120070, ///< B级致命错误11_52
    STATUS_F_PRINT_B_11_56 = 0x40120071, ///< B级致命错误11_56
    STATUS_F_PRINT_B_11_84 = 0x40120072, ///< B级致命错误11_84
    STATUS_F_PRINT_B_11_95 = 0x40120073, ///< B级致命错误11_95
    STATUS_F_PRINT_B_11_96 = 0x40120074, ///< B级致命错误11_96
    STATUS_F_PRINT_B_11_97 = 0x40120075, ///< B级致命错误11_97
    STATUS_F_PRINT_B_24_11 = 0x40120076, ///< B级致命错误24_11
    STATUS_F_PRINT_B_24_12 = 0x40120077, ///< B级致命错误24_12
    STATUS_F_PRINT_B_24_13 = 0x40120078, ///< B级致命错误24_13
    STATUS_F_PRINT_B_24_14 = 0x40120079, ///< B级致命错误24_14
    STATUS_F_PRINT_B_2A_11 = 0x4012007A, ///< B级致命错误2A_11
    STATUS_F_PRINT_B_2A_12 = 0x4012007B, ///< B级致命错误2A_12
    STATUS_F_PRINT_B_2A_13 = 0x4012007C, ///< B级致命错误2A_13
    STATUS_F_PRINT_B_2A_14 = 0x4012007D, ///< B级致命错误2A_14
    STATUS_F_PRINT_B_51_2 = 0x4012007E, ///< B级致命错误51_2
    STATUS_F_PRINT_C_55_1 = 0x4012007F, ///< C级致命错误55_1
    STATUS_F_PRINT_C_39_2A = 0x40120080, ///< C级致命错误39_2A
    STATUS_F_PRINT_C_C1_63 = 0x40120081, ///< C级致命错误C1_63
    STATUS_F_PRINT_C_40_A1 = 0x40120082, ///< C级致命错误40_A1
    STATUS_F_PRINT_C_00_02 = 0x40120083, ///< C级致命错误00_02
    STATUS_F_PRINT_C_56_03 = 0x40120084, ///< C级致命错误56_03
    STATUS_F_PRINT_C_56_20 = 0x40120085, ///< C级致命错误56_20
    STATUS_F_PRINT_C_40_91 = 0x40120086, ///< C级致命错误40_91
    STATUS_F_PRINT_C_40_A2 = 0x40120087, ///< C级致命错误40_A2
    STATUS_F_PRINT_C_40_A3 = 0x40120088, ///< C级致命错误40_A3
    STATUS_F_PRINT_C_40_A4 = 0x40120089, ///< C级致命错误40_A4
    STATUS_F_PRINT_C_40_A5 = 0x4012008A, ///< C级致命错误40_A5
    STATUS_F_PRINT_C_40_A6 = 0x4012008B, ///< C级致命错误40_A6
    STATUS_F_PRINT_C_10_3 = 0x4012008C, ///< C级致命错误10_3
    STATUS_F_PRINT_C_10_4 = 0x4012008D, ///< C级致命错误10_4
    STATUS_F_PRINT_C_10_14 = 0x4012008E, ///< C级致命错误10_14
    STATUS_F_PRINT_C_10_81 = 0x4012008F, ///< C级致命错误10_81
    STATUS_F_PRINT_C_10_82 = 0x40120090, ///< C级致命错误10_82
    STATUS_F_PRINT_C_2A_21 = 0x40120091, ///< C级致命错误2A_21
    STATUS_F_PRINT_C_2A_22 = 0x40120092, ///< C级致命错误2A_22
    STATUS_F_PRINT_C_2A_23 = 0x40120093, ///< C级致命错误2A_23
    STATUS_F_PRINT_C_2A_24 = 0x40120094, ///< C级致命错误2A_24
    STATUS_F_PRINT_C_26_50 = 0x40120095, ///< C级致命错误26_50
    STATUS_F_PRINT_C_26_53 = 0x40120096, ///< C级致命错误26_53
    STATUS_F_PRINT_C_26_52 = 0x40120097, ///< C级致命错误26_52
    STATUS_F_PRINT_C_26_51 = 0x40120098, ///< C级致命错误26_51
    STATUS_F_PRINT_C_26_54 = 0x40120099, ///< C级致命错误26_54
    STATUS_F_PRINT_C_2A_03 = 0x4012009A, ///< C级致命错误2A_03
    STATUS_F_PRINT_C_2A_02 = 0x4012009B, ///< C级致命错误2A_02
    STATUS_F_PRINT_C_2A_01 = 0x4012009C, ///< C级致命错误2A_01
    STATUS_F_PRINT_C_2A_04 = 0x4012009D, ///< C级致命错误2A_04
    STATUS_F_PRINT_C_D7_01 = 0x4012009E, ///< C级致命错误D7_01
    STATUS_F_PRINT_C_D7_02 = 0x4012009F, ///< C级致命错误D7_02
    STATUS_F_PRINT_C_D7_03 = 0x401200A0, ///< C级致命错误D7_03
    STATUS_F_PRINT_C_D7_04 = 0x401200A1, ///< C级致命错误D7_04
    STATUS_F_PRINT_C_D7_05 = 0x401200A2, ///< C级致命错误D7_05
    STATUS_F_PRINT_C_D7_06 = 0x401200A3, ///< C级致命错误D7_06
    STATUS_F_PRINT_C_56_1 = 0x401200A4, ///< C级致命错误56_1
    STATUS_F_PRINT_C_56_6 = 0x401200A5, ///< C级致命错误56_6
    STATUS_F_PRINT_C_56_10 = 0x401200A6, ///< C级致命错误56_10
    STATUS_F_PRINT_C_14_2 = 0x401200A7, ///< C级致命错误14_2
    STATUS_F_PRINT_C_14_3 = 0x401200A8, ///< C级致命错误14_3
    STATUS_F_PRINT_C_C1_55 = 0x401200A9, ///< C级致命错误C1_55
    STATUS_F_PRINT_C_C1_5B = 0x401200AA, ///< C级致命错误C1_5B
    STATUS_F_PRINT_C_71_06 = 0x401200AB, ///< C级致命错误71_06
    STATUS_F_PRINT_C_71_07 = 0x401200AC, ///< C级致命错误71_07
    STATUS_F_PRINT_C_71_11 = 0x401200AD, ///< C级致命错误71_11
    STATUS_F_PRINT_C_71_12 = 0x401200AE, ///< C级致命错误71_12
    STATUS_F_PRINT_C_71_31 = 0x401200AF, ///< C级致命错误71_31
    STATUS_F_PRINT_C_71_33 = 0x401200B0, ///< C级致命错误71_33
    STATUS_F_PRINT_C_71_35 = 0x401200B1, ///< C级致命错误71_35
    STATUS_F_PRINT_C_71_37 = 0x401200B2, ///< C级致命错误71_37
    STATUS_F_PRINT_C_71_32 = 0x401200B3, ///< C级致命错误71_32
    STATUS_F_PRINT_C_71_34 = 0x401200B4, ///< C级致命错误71_34
    STATUS_F_PRINT_C_71_39 = 0x401200B5, ///< C级致命错误71_39
    STATUS_F_PRINT_C_71_41 = 0x401200B6, ///< C级致命错误71_41
    STATUS_F_PRINT_C_71_51 = 0x401200B7, ///< C级致命错误71_51
    STATUS_F_PRINT_C_71_52 = 0x401200B8, ///< C级致命错误71_52
    STATUS_F_PRINT_C_71_53 = 0x401200B9, ///< C级致命错误71_53
    STATUS_F_PRINT_C_71_60 = 0x401200BA, ///< C级致命错误71_60
    STATUS_F_PRINT_C_71_62 = 0x401200BB, ///< C级致命错误71_62
    STATUS_F_PRINT_C_71_63 = 0x401200BC, ///< C级致命错误71_63
    STATUS_F_PRINT_C_71_64 = 0x401200BD, ///< C级致命错误71_64
    STATUS_F_PRINT_C_72_02 = 0x401200BE, ///< C级致命错误72_02
    STATUS_F_PRINT_C_72_06 = 0x401200BF, ///< C级致命错误72_06
    STATUS_F_PRINT_C_72_0A = 0x401200C0, ///< C级致命错误72_0A
    STATUS_F_PRINT_C_72_0B = 0x401200C1, ///< C级致命错误72_0B
    STATUS_F_PRINT_C_72_41 = 0x401200C2, ///< C级致命错误72_41
    STATUS_F_PRINT_C_72_42 = 0x401200C3, ///< C级致命错误72_42
    STATUS_F_PRINT_C_72_43 = 0x401200C4, ///< C级致命错误72_43
    STATUS_F_PRINT_C_72_51 = 0x401200C5, ///< C级致命错误72_51
    STATUS_F_PRINT_C_73_01 = 0x401200C6, ///< C级致命错误73_01
    STATUS_F_PRINT_C_73_02 = 0x401200C7, ///< C级致命错误73_02
    STATUS_F_PRINT_C_73_04 = 0x401200C8, ///< C级致命错误73_04
    STATUS_F_PRINT_C_73_05 = 0x401200C9, ///< C级致命错误73_05
    STATUS_F_PRINT_C_73_10 = 0x401200CA, ///< C级致命错误73_10
    STATUS_F_PRINT_C_73_11 = 0x401200CB, ///< C级致命错误73_11
    STATUS_F_PRINT_C_73_12 = 0x401200CC, ///< C级致命错误73_12
    STATUS_F_PRINT_C_73_13 = 0x401200CD, ///< C级致命错误73_13
    STATUS_F_PRINT_C_73_14 = 0x401200CE, ///< C级致命错误73_14
    STATUS_F_PRINT_C_73_15 = 0x401200CF, ///< C级致命错误73_15
    STATUS_F_PRINT_C_73_17 = 0x401200D0, ///< C级致命错误73_17
    STATUS_F_PRINT_C_74_01 = 0x401200D1, ///< C级致命错误74_01
    STATUS_F_PRINT_C_74_02 = 0x401200D2, ///< C级致命错误74_02
    STATUS_F_PRINT_C_75_01 = 0x401200D3, ///< C级致命错误75_01
    STATUS_F_PRINT_C_75_02 = 0x401200D4, ///< C级致命错误75_02
    STATUS_F_PRINT_C_76_01 = 0x401200D5, ///< C级致命错误76_01
    STATUS_F_PRINT_C_76_02 = 0x401200D6, ///< C级致命错误76_02
    STATUS_F_PRINT_C_76_03 = 0x401200D7, ///< C级致命错误76_03
    STATUS_F_PRINT_C_76_04 = 0x401200D8, ///< C级致命错误76_04
    STATUS_F_PRINT_C_76_05 = 0x401200D9, ///< C级致命错误76_05
    STATUS_F_PRINT_C_76_06 = 0x401200DA, ///< C级致命错误76_06
    STATUS_F_PRINT_C_76_07 = 0x401200DB, ///< C级致命错误76_07
    STATUS_F_PRINT_C_76_0A = 0x401200DC, ///< C级致命错误76_0A
    STATUS_F_PRINT_C_76_0B = 0x401200DD, ///< C级致命错误76_0B
    STATUS_F_PRINT_C_76_0C = 0x401200DE, ///< C级致命错误76_0C
    STATUS_F_PRINT_C_76_0D = 0x401200DF, ///< C级致命错误76_0D
    STATUS_F_PRINT_C_76_0E = 0x401200E0, ///< C级致命错误76_0E
    STATUS_F_PRINT_C_76_0F = 0x401200E1, ///< C级致命错误76_0F
    STATUS_F_PRINT_C_76_10 = 0x401200E2, ///< C级致命错误76_10
    STATUS_F_PRINT_C_76_22 = 0x401200E3, ///< C级致命错误76_22
    STATUS_F_PRINT_C_76_23 = 0x401200E4, ///< C级致命错误76_23
    STATUS_F_PRINT_C_76_24 = 0x401200E5, ///< C级致命错误76_24
    STATUS_F_PRINT_C_76_31 = 0x401200E6, ///< C级致命错误76_31
    STATUS_F_PRINT_C_76_33 = 0x401200E7, ///< C级致命错误76_33
    STATUS_F_PRINT_C_56_5  = 0x401200E8, ///< C级致命错误56_5
    STATUS_F_PRINT_C_7F_01 = 0x401200E9, ///< C级致命错误7F_01
    STATUS_F_PRINT_C_7F_04 = 0x401200EA, ///< C级致命错误7F_04
    STATUS_F_PRINT_C_7F_05 = 0x401200EB, ///< C级致命错误7F_05
    STATUS_F_PRINT_C_7F_06 = 0x401200EC, ///< C级致命错误7F_06
    STATUS_F_PRINT_C_7F_07 = 0x401200ED, ///< C级致命错误7F_07
    STATUS_F_PRINT_C_7F_08 = 0x401200EE, ///< C级致命错误7F_08
    STATUS_F_PRINT_C_7F_09 = 0x401200EF, ///< C级致命错误7F_09
    STATUS_F_PRINT_C_7F_10 = 0x401200F0, ///< C级致命错误7F_10
    STATUS_F_PRINT_C_7F_11 = 0x401200F1, ///< C级致命错误7F_11
    STATUS_F_PRINT_C_7F_12 = 0x401200F2, ///< C级致命错误7F_12
    STATUS_F_PRINT_C_7F_13 = 0x401200F3, ///< C级致命错误7F_13
    STATUS_F_PRINT_C_7F_15 = 0x401200F4, ///< C级致命错误7F_15
    STATUS_F_PRINT_C_7F_16 = 0x401200F5, ///< C级致命错误7F_16
    STATUS_F_PRINT_C_7F_17 = 0x401200F6, ///< C级致命错误7F_17
    STATUS_F_PRINT_C_7F_18 = 0x401200F7, ///< C级致命错误7F_18
    STATUS_F_PRINT_C_7F_19 = 0x401200F8, ///< C级致命错误7F_19
    STATUS_F_PRINT_C_7F_20 = 0x401200F9, ///< C级致命错误7F_20
    STATUS_F_PRINT_C_7F_21 = 0x401200FA, ///< C级致命错误7F_21
    STATUS_F_PRINT_C_7F_22 = 0x401200FB, ///< C级致命错误7F_22
    STATUS_F_PRINT_C_7F_23 = 0x401200FC, ///< C级致命错误7F_23
    STATUS_F_PRINT_C_7F_24 = 0x401200FD, ///< C级致命错误7F_24
    STATUS_F_PRINT_C_7F_50 = 0x401200FE, ///< C级致命错误7F_50
    /* PRINT END */
    /* SCAN START */
    STATUS_I_SCAN_INIT = 0x10200000, ///< 扫描模块初始化中
    STATUS_I_SCAN_IDLE = 0x10200001, ///< 扫描模块就绪
    STATUS_I_SCAN_SLEEP = 0x10200002, ///< 扫描模块睡眠
    STATUS_I_SCAN_PROCESSING = 0x10200003, ///< 扫描处理中
    STATUS_I_SCAN_RUNNING = 0x10200004, ///< 扫描中
    STATUS_I_SCAN_CANCELING = 0x10200005, ///< 扫描取消中
    STATUS_I_SCAN_TOFILE_SENDING = 0x10200006, ///< 扫描文件正在发送中....
    STATUS_I_SCAN_NEXT_PAGE_WAITING = 0x10200007, ///< 等待扫描下一页状态
    STATUS_I_SCAN_FINISHED = 0x10200008, ///< 扫描完成
    STATUS_I_SCAN_TO_FILE_UDISK_SAVING = 0x10200009, ///< 扫描文件正在写入U盘
    STATUS_I_SCAN_TO_FILE_SENT = 0x1020000A, ///< 扫描文件发送完成
    STATUS_I_SCAN_ADF_PAPER_PRESENT = 0x10201000, ///< ADF纸张存在
    STATUS_I_SCAN_ADF_PAPER_REMOVED = 0x10201001, ///< ADF纸张移除
    STATUS_I_SCAN_PUT_PAPER_TO_ADF = 0x10201002, ///< 请放纸到ADF
    STATUS_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF = 0x10201003, ///< 等待ADF手动双面翻面确认
    STATUS_I_SCAN_PAPER_SIZE_UNSUPPORT_MIXED_SCAN = 0x10201004, ///< 纸张类型不支持混合扫描
    STATUS_I_SCAN_NO_RESOURCE = 0x10201005, ///< 扫描作业无可用资源
    STATUS_I_SCAN_PAPER_SIZE_OVER_AREA = 0x10201006, ///< 下发扫描纸张尺寸超出区域
    STATUS_I_SCAN_ADF_BOARD_WAIT_INIT = 0x10202000, ///< ADF模块板等待被初始化
    STATUS_I_SCAN_ADF_BOARD_INITING = 0x10202001, ///< ADF模块板正在初始化
    STATUS_I_SCAN_ADF_BOARD_IDLE = 0x10202002, ///< ADF模块板处于空闲状态
    STATUS_I_SCAN_ADF_BOARD_WORKING = 0x10202003, ///< ADF模块板正在工作中
    STATUS_I_SCAN_ADF_BOARD_ERR = 0x10202004, ///< ADF模块板进入错误状态
    STATUS_I_SCAN_ADF_BOARD_WARNING = 0x10202005, ///< ADF模块板进入警告状态
    STATUS_I_SCAN_OUT_TO_EML_SUCCESS = 0x10203000, ///< 扫描到email成功
    STATUS_I_SCAN_OUT_TO_FTP_SUCCESS = 0x10203001, ///< 扫描到FTP成功
    STATUS_I_SCAN_OUT_TO_UDISK_SUCCESS = 0x10203002, ///< 扫描到U盘成功
    STATUS_I_SCAN_OUT_TO_WSD_SUCCESS = 0x10203003, ///< 扫描到WSD成功
    STATUS_I_SCAN_OUT_TO_AIRSCAN_SUCCESS = 0x10203004, ///< 扫描到AIRSCAN成功
    STATUS_I_SCAN_OUT_TO_EML_CANCEL = 0x10203005, ///< 扫描到email取消
    STATUS_I_SCAN_OUT_TO_FTP_CANCEL = 0x10203006, ///< 扫描到FTP取消
    STATUS_I_SCAN_OUT_TO_UDISK_CANCEL = 0x10203007, ///< 扫描到U盘取消
    STATUS_I_SCAN_OUT_TO_WSD_CANCEL = 0x10203008, ///< 扫描到WSD取消
    STATUS_I_SCAN_OUT_TO_AIRSCAN_CANCEL = 0x10203009, ///< 扫描到AIRSCAN取消
    STATUS_I_SCAN_LOCKED = 0x10220001, ///< 扫描仪已加锁
    STATUS_I_SCAN_BOOK_COVER_WAITING = 0x10220002, ///< 书稿扫描功能扫描下一页提示用户放置封面
    STATUS_I_SCAN_BOOK_BACK_COVER_WAITING = 0x10220003, ///< 书稿扫描功能扫描下一页提示用户放置封底
    STATUS_I_SCAN_BOOK_MAIN_TEXT_WAITING = 0x10220004, ///< 书稿扫描功能扫描下一页提示用户放置正文
    STATUS_W_SCAN_REMOVE_ADF_PAPER  =  0x20220000, ///< 需要清除ADF纸盒纸张
    STATUS_E_SCAN_ADF_PAPER_OUT = 0x30200000, ///< ADF缺纸
    STATUS_E_SCAN_PAPER_MISPICK_ADF_FRONT = 0x30200001, ///< ADF正面进纸失败
    STATUS_E_SCAN_PAPER_MISPICK_ADF_BACK = 0x30200002, ///< ADF背面进纸失败
    STATUS_E_SCAN_ADF_COVER_OPEN = 0x30200003, ///< ADF开盖
    STATUS_E_SCAN_ADF_PAPER_MISMATCH = 0x30200004, ///< ADF纸型不匹配
    STATUS_E_SCAN_FB_COVER_OPEN = 0x30200005, ///< FB开盖
    STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ = 0x30201000, ///< 纸头超时未到达ADJ传感器
    STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN = 0x30201001, ///< 纸头超时未到达SCAN传感器
    STATUS_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT = 0x30201002, ///< 纸头超时未到达EXIT传感器
    STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ = 0x30201003, ///< 纸尾超时未到达ADJ传感器
    STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN = 0x30201004, ///< 纸尾超时未到达SCAN传感器
    STATUS_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT = 0x30201005, ///< 纸尾超时未到达EXIT传感器
    STATUS_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT = 0x30201006, ///< 纸张离开ADJ传感器超时
    STATUS_E_SCAN_PAPER_JAM_REMAIN_ADJ = 0x30201007, ///< 纸张遗留在ADJ传感器
    STATUS_E_SCAN_PAPER_JAM_REMAIN_SCAN = 0x30201008, ///< 纸张遗留在SCAN传感器
    STATUS_E_SCAN_PAPER_JAM_REMAIN_EXIT = 0x30201009, ///< 纸张遗留在EXIT传感器
    STATUS_E_SCAN_MEMORY_LOW = 0x30202000, ///< 扫描模块内存低
    STATUS_E_SCAN_COMMUNICATION_ERR_21 = 0x30202001, ///< 通讯失败:21扫描数据传输超时
    STATUS_E_SCAN_COMMUNICATION_ERR_22 = 0x30202002, ///< 通讯失败:22无效的指令
    STATUS_E_SCAN_COMMUNICATION_ERR_23 = 0x30202003, ///< 通讯失败:23无效的参数
    STATUS_E_SCAN_COMMUNICATION_ERR_24 = 0x30202004, ///< 通讯失败:24(保留)
    STATUS_E_SCAN_COMMUNICATION_ERR_25 = 0x30202005, ///< 通讯失败:25USB连接出错
    STATUS_E_SCAN_COMMUNICATION_ERR_26 = 0x30202006, ///< 通讯失败:26网络连接出错
    STATUS_E_SCAN_COMMUNICATION_ERR_27 = 0x30202007, ///< 通讯失败:27wifi连接出错
    STATUS_E_SCAN_TO_FILE_ABORTED = 0x30203000, ///< 扫描到文件出错
    STATUS_E_SCAN_TO_FILE_PASSWORD_WRONG = 0x30203001, ///< 用户名或密码错误
    STATUS_E_SCAN_TO_FILE_FILE_OVERSIZE = 0x30203002, ///< 文件大小超出设定范围，过大
    STATUS_E_SCAN_TO_FILE_SERVER_OVERSIZE = 0x30203003, ///< 文件大小超过服务器限制
    STATUS_E_SCAN_TO_FILE_SEND_FAILED = 0x30203004, ///< 文件发送失败
    STATUS_E_SCAN_TO_FILE_VOLUME_LOW = 0x30203005, ///< 扫描到文件容量不足
    STATUS_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE = 0x30203006, ///< U盘容量不足
    STATUS_E_SCAN_TO_FILE_UDISK_ABORTED = 0x30203007, ///< 扫描到U盘写文件失败
    STATUS_E_SCAN_TO_FILE_PC_NO_RESPONSE = 0x30203008, ///< PC无响应
    STATUS_E_SCAN_EML_ESYS = 0x30203009, ///< 扫描到email系统故障
    STATUS_E_SCAN_EML_EUSER = 0x3020300A, ///< 扫描到email用户名错误
    STATUS_E_SCAN_EML_ECONN = 0x3020300B, ///< 扫描到email连接失败
    STATUS_E_SCAN_EML_ETRAN = 0x3020300C, ///< 扫描到email传输失败
    STATUS_E_SCAN_EML_EPASS = 0x3020300D, ///< 扫描到email密码错误
    STATUS_E_SCAN_EML_EFROM = 0x3020300E, ///< 扫描到email发件人错误
    STATUS_E_SCAN_EML_ETO = 0x3020300F, ///< 扫描到email收件人错误
    STATUS_E_SCAN_EML_EATT_ACCESS = 0x30203010, ///< 扫描到email附件不可访问（文件不存在等）
    STATUS_E_SCAN_EML_EATT_TOO_BIG = 0x30203011, ///< 扫描到email附件过大
    STATUS_E_SCAN_EML_ELIMIT = 0x30203012, ///< 扫描到email邮件大小超过了服务器限制
    STATUS_E_SCAN_EML_ESERVER = 0x30203013, ///< 扫描到email服务器响应了其它错误信息
    STATUS_E_SCAN_EML_MEM_LOW = 0x30203014, ///< 扫描到email内存不足
    STATUS_E_SCAN_SMB_SERVER_OVERSIZE = 0x30203015, ///< 扫描到SMB服务器磁盘满
    STATUS_E_SCAN_SMB_SENDFAIL = 0x30203016, ///< 扫描到SMB文件上传失败
    STATUS_E_SCAN_SMB_DIR = 0x30203017, ///< 扫描到SMB目录错误
    STATUS_E_SCAN_SMB_HOSTNAME = 0x30203018, ///< 扫描到SMB主机名错误
    STATUS_E_SCAN_SMB_USER_PASS = 0x30203019, ///< 扫描到SMB用户名或密码错误
    STATUS_E_SCAN_SMB_SERVER_DISCONN = 0x3020301A, ///< 扫描到SMB服务器网络断开
    STATUS_E_SCAN_SMB_NET_DISCONN = 0x3020301B, ///< 扫描到SMB打印机网络断开
    STATUS_E_SCAN_FTP_ESYS = 0x3020301C, ///< 扫描到FTP系统故障
    STATUS_E_SCAN_FTP_ECONN = 0x3020301D, ///< 扫描到FTP连接失败
    STATUS_E_SCAN_FTP_ETRAN = 0x3020301E, ///< 扫描到FTP传输失败
    STATUS_E_SCAN_FTP_EUSER = 0x3020301F, ///< 扫描到FTP用户名错误
    STATUS_E_SCAN_FTP_EPASS = 0x30203020, ///< 扫描到FTP密码错误
    STATUS_E_SCAN_FTP_EFILE_ACCESS = 0x30203021, ///< 扫描到FTP文件访问失败
    STATUS_E_SCAN_FTP_ESERVPATH = 0x30203022, ///< 扫描到FTP服务器路径错误
    STATUS_E_SCAN_FTP_ESERVER = 0x30203023, ///< 扫描到FTP服务器响应了其它错误信息
    STATUS_E_SCAN_WSD_QIOERR = 0x30203024, ///< 扫描到WSD读取IO错误
    STATUS_E_SCAN_WSD_COMM = 0x30203025, ///< 扫描到WSD连接失败
    STATUS_E_SCAN_WSD_LOWMEM = 0x30203026, ///< 扫描到WSD内存不足
    STATUS_E_SCAN_WSD_FILE = 0x30203027, ///< 扫描到WSD文件失败
    STATUS_E_SCAN_AIRSCAN_ESYS = 0x30203028, ///< 扫描到Airscan系统故障
    STATUS_E_SCAN_AIRSCAN_QIOERR = 0x30203029, ///< 扫描到Airscan读取IO错误
    STATUS_E_SCAN_AIRSCAN_ECONN = 0x3020302A, ///< 扫描到Airscan连接失败
    STATUS_E_SCAN_AIRSCAN_ETRAN = 0x3020302B, ///< 扫描到Airscan传输失败
    STATUS_E_SCAN_USB_NO_RESPONSE = 0x3020302C, ///< USB通信无响应
    STATUS_E_SCAN_FTP_EADRR = 0x3020302D, ///< FTP地址不可达
    STATUS_E_SCAN_SMB_IPADDR = 0x3020302E, ///< 扫描到SMB网络协议地址错误
    STATUS_E_SCAN_SMB_EPORT = 0x3020302F, ///< 扫描到SMB端口错误
    STATUS_E_SCAN_FTP_EPORT = 0x30203030, ///< 扫描到FTP端口错误
    STATUS_E_SCAN_ADF_BOARD_COM_HW = 0x30204000, ///< ADF通讯检测到硬件错误
    STATUS_E_SCAN_ADF_BOARD_COM_CRC_SERIAL = 0x30204001, ///< ADF通讯连续多帧数据CRC检验出错
    STATUS_E_SCAN_ADF_BOARD_COM_CRC_TOTAL = 0x30204002, ///< ADF通讯累计多帧数据CRC校验出错
    STATUS_E_SCAN_ADF_BOARD_COM_INVALID_SERIAL = 0x30204003, ///< ADF通讯连续接收到多个字节无效数据
    STATUS_E_SCAN_ADF_BOARD_COM_INVALID_TOTAL = 0x30204004, ///< ADF通讯累计接收到多个字节无效数据
    STATUS_E_SCAN_SCANNER_RESPOND_TIMEOUT = 0x30220001, ///< 扫描仪响应超时
    STATUS_E_SCAN_ADF_BOTTOM_COVER_OPEN = 0x30220002, ///< ADF下盖打开
    STATUS_E_SCAN_DF1_COVER_OPEN = 0x30220003, ///< DF1开盖错误
    STATUS_E_SCAN_BOOK_FB_COVER_CLOSE = 0x30220005,  ///< 书本原稿场景下，请保持FB盖打开
    STATUS_F_SCAN_ADF_RELEASE_BAR_ABNORMAL = 0x40200000, ///< ADF释纸杆控制异常
    STATUS_F_SCAN_ADF_MOTOR_ABNORMAL = 0x40200001, ///< ADF马达异常
    STATUS_F_SCAN_ADF_BOARD_HW_ERROR = 0x40200002, ///< 扫描ADF模块板硬件错误（初始化失败）
    STATUS_F_SCAN_ADF_BOARD_CONNECT_FAILURE = 0x40200003, ///< 扫描ADF模块板连接失败
    STATUS_F_SCAN_FB_MOTOR_ABNORMAL = 0x40201000, ///< FB马达异常
    STATUS_F_SCAN_HOME_CHECK_ERROR = 0x40201001, ///< 原点检查错误（FB马达往前走了，homesensor还在遮挡状态）
    STATUS_F_SCAN_FINE_HOME_FAILURE = 0x40201002, ///< 找原点失败
    STATUS_F_SCAN_FINE_BLACK_MARK_FAILURE = 0x40201003, ///< 找黑标失败
    STATUS_F_SCAN_CIS_ABNORMAL = 0x40202000, ///< CIS异常
    STATUS_F_SCAN_CIS_DET_FAILURE = 0x40202001, ///< CIS检测失败
    STATUS_F_SCAN_AFE_ABNORMAL = 0x40202002, ///< AFE异常
    STATUS_F_SCAN_AFE_CHIP_CHECK_FAILURE = 0x40202003, ///< AFE芯片读写检测失败
    STATUS_F_SCAN_AFE_SCAN_DATA_ABNORMAL = 0x40202004, ///< AFE扫描数据异常
    STATUS_F_SCAN_CAL_AFE_OFFSET_FAILURE = 0x40202005, ///< AFE校准失败（OFFSET）
    STATUS_F_SCAN_CAL_AFE_GAIN_FAILURE = 0x40202006, ///< AFE校准失败（GAIN）
    STATUS_F_SCAN_CAL_AFE_EXPOSURE_FAILURE = 0x40202007, ///< AFE校准失败（EXPOSURE）
    STATUS_F_SCAN_CAL_SHADING_FAILURE = 0x40202008, ///< Shading失败
    STATUS_F_SCAN_CAL_MEM_UNREADY = 0x40202009, ///< 校准内存不足
    STATUS_F_SCAN_FE0640_10 = 0x4020200A, ///< 上CIS异常
    STATUS_F_SCAN_FE0640_11 = 0x4020200B, ///< 上CIS检测失败
    STATUS_F_SCAN_FE0641_10 = 0x4020200C, ///< 上AFE异常
    STATUS_F_SCAN_FE0641_11 = 0x4020200D, ///< 上AFE芯片读写检测失败
    STATUS_F_SCAN_FE0641_12 = 0x4020200E, ///< 上AFE扫描数据异常
    STATUS_F_SCAN_FE0642_10 = 0x4020200F, ///< 上AFE校准失败（OFFSET）
    STATUS_F_SCAN_FE0642_11 = 0x40202010, ///< 上AFE校准失败（GAIN）
    STATUS_F_SCAN_FE0642_12 = 0x40202011, ///< 上AFE校准失败（EXPOSURE）
    STATUS_F_SCAN_FE0643_10 = 0x40202012, ///< 上Shading失败
    STATUS_F_SCAN_CONNECT_ENGINE_TIMEOUT = 0x40203000, ///< 扫描引擎连接超时（后端）
    STATUS_F_SCAN_EMMC_ABNORMAL = 0x40203001, ///< emmc异常（后端）
    STATUS_F_SCAN_DMA_ABNORMAL = 0x40203002, ///< DMA异常
    STATUS_F_SCAN_DMA_INTERRUPT_TIMEOUT = 0x40203003, ///< DMA中断超时
    STATUS_F_SCAN_ADF_COOLING_FAN = 0x40220001, ///< ADF冷却风扇异常
    STATUS_F_SCAN_ADF_CANTACT_RETRACT_MECH = 0x40220002, ///< ADF扫描入口前方连接装置异常
    STATUS_F_SCAN_ADF_BRUSH_MOVEMENT = 0x40220003, ///< ADF刷子偏移
    STATUS_F_SCAN_ADF_INIT_TIMEOUT = 0x40220004, ///< ADF初始化超时
    STATUS_F_SCAN_FB_EXPOSURE_ON_FAIL = 0x40220005, ///< FB校准灯光开启失败
    STATUS_F_SCAN_FB_EXPOSURE_LAMP_IPL = 0x40220006, ///< FB曝光灯无规律亮灯
    STATUS_F_SCAN_FB_DRIVE_SYS_HOME_ABNORMAL = 0x40220007, ///< FB驱动系统home传感器异常
    STATUS_F_SCAN_FB_SLIDER_OVERRUNNING = 0x40220008, ///< FB滑动器超限
    STATUS_F_SCAN_FB_SCAN_SEQ_TROUBLE_1 = 0x40220009, ///< FB序列错误1
    STATUS_F_SCAN_FB_EMMC_ABNORMAL = 0x4022000A, ///< FB-EMMC异常
    STATUS_F_SCAN_FB_CCD_GAIN_ADJ_ABNORMAL = 0x4022000B, ///< FB-CCD灯光校准异常
    STATUS_F_SCAN_FB_ABN_IMAGE_PROCESS_CLK = 0x4022000C, ///< FB图像处理同步时钟输入异常
    STATUS_F_SCAN_FB_CCD_ABNORMAL_POWER = 0x4022000D, ///< FB-CCD电源电压异常
    STATUS_F_SCAN_FB_DF_EXPOSURE_ON_FAIL = 0x4022000E, ///< FB-DF曝光灯光开启异常
    STATUS_F_SCAN_FB_DF_EXPOSURE_LAMP_IPL = 0x4022000F, ///< FB-DF曝光灯无规律亮灯
    STATUS_F_SCAN_FB_DF_HOME_ABNORMAL = 0x40220010, ///< FB-DF校准仪器home传感器异常
    STATUS_F_SCAN_FB_DF_BOARD_HOME_ABNORMAL = 0x40220011, ///< FB-DF校准仪器板载home传感器异常
    STATUS_F_SCAN_FB_DF_CIS_CLA_ADJ_ABNORMAL = 0x40220012, ///< FB-DF灯光校准异常
    STATUS_F_SCAN_FB_DF_CIS_GAIN_ADJ_ABNORMA = 0x40220013, ///< FB-DF-GAIN校准异常
    STATUS_F_SCAN_FB_CONNECT_ENGINE_TIMEOUT = 0x40220014, ///< FB连接引擎超时
    STATUS_F_SCAN_FB_FATAL_IMAGE_PROCESS_CLK = 0x40220015, ///< FB图像处理同步时钟输入致命异常
    STATUS_F_SCAN_FB_INIT_TIMEOUT = 0x40220016, ///< FB初始化超时
    /* SCAN END */
    /* COPY START */
    STATUS_I_COPY_PROCESSING = 0x10301001, ///< 复印状态显示，身份证显示正面扫描中，背面扫描中，复印中，其他统一显示复印中
    STATUS_I_COPY_CANCELING = 0x10301002, ///< 复印取消中
    STATUS_I_COPY_MANUAL_DUPLEX_CONFIRM = 0x10301003, ///< 手动双面通知
    STATUS_I_COPY_ID_CARD_CONFIRM = 0x10301004, ///< 身份证翻面通知
    STATUS_I_COPY_MEM_LOW = 0x10301005, ///< 复印内存不足
    STATUS_I_COPY_SAMPLE_CONFIRM = 0x10301006, ///< 样本复印，复印一份后提示用户的确认信息
    STATUS_I_COPY_SAMPLE_FINISH = 0x10301007, ///< 样本复印，复印剩余份后提示用户是否删除数据
    STATUS_I_COPY_NEXT_ORIGINAL_CONFIRM = 0x10301008, ///< 复印下一张原稿
    STATUS_I_COPY_PUT_PAPER_TO_ADF = 0x10301009, ///< ADF缺纸
    STATUS_I_COPY_NO_RESOURCE = 0x10301010, ///< 复印作业无可用资源
    STATUS_I_COPY_CONTINUE_CONFIRM = 0x10301011, ///< 故障已解除，是否继续复印？
    STATUS_I_COPY_MIX_ORIGINAL_MISMATCH = 0x10301012, ///< 混合原稿ADF识别尺寸与面板设定不匹配
    STATUS_I_COPY_COMPLETE = 0x10301013, ///< 复印作业完成
    STATUS_I_COPY_COLLATE_STORAGE_FULL = 0x10301014, ///< 在逐份复印存储外存过程中,如果发生存储空间不足,则提示用户此信息.此时作业会被自动取消
    STATUS_I_COPY_ABORTING = 0x10301015, ///< 复印作业异常
    STATUS_I_COPY_MANUAL_DUPLEX_UNSUPPORT = 0x10301016, ///< 小册子或多页合一复印不支持原稿由自动进纸器双面扫描。
    STATUS_I_COPY_SCAN_AREA_UNSUPPORT = 0x10301017, ///< 复印原稿尺寸超出扫描区域。有效范围：ADF:105mm(宽) x 148mm~216 mm(宽) x356mmFB:最大216(宽) mm x297mm""
    STATUS_I_COPY_STATISTIC_ITEM_ERROR = 0x10320001, ///< 复印统计项不存在
    STATUS_I_COPY_IDLE = 0x10320002, ///< 复印空闲中
    STATUS_W_COPY_PARAMETER_ERROR = 0x20320001, ///< 复印配置参数有误
    STATUS_W_COPY_PERMISSION_NOT_ALLOWED = 0x20320002, ///< 复印权限不允许
    STATUS_E_COPY_SAMPLE_TONER_EMPTY = 0x30301001, ///< 作业列表，样本复印，彩色模式彩色墨粉用尽
    STATUS_E_COPY_IPM_ERROR = 0x30320001, ///< 图像处理错误
    /* COPY END */
    /* PANEL START */
    STATUS_I_PANEL_UPGRADE_START = 0x10500001, ///< Panel升级开始
    STATUS_F_PANEL_COMMUNICATION_ERROR = 0x40500001, ///< Panel与DC的通信异常
    /* PANEL END */
    /* USB START */
    STATUS_I_USB_UDISK_INSERT = 0x10700001, ///< U盘插入
    STATUS_I_USB_UDISK_EXTRACT = 0x10700002, ///< U盘拔出
    STATUS_E_USB_UDISK_MISTAKE_FORMAT = 0x30700001, ///< U盘格式错误
    STATUS_E_USB_UDISK_FAILURE = 0x30700002, ///< U盘设备故障
    STATUS_F_USB_UDISK_OVERCURRENT = 0x40700001, ///< U盘过载
    STATUS_F_USB_HOST_OVERCURRENT = 0x40720001, ///< USB设备过流
    /* USB END */
    /* NET START */
    STATUS_I_NET_WIFI_STA_CONNECTING = 0x10601001, ///< WiFistation连接中
    STATUS_I_NET_WIFI_WPS_PBC = 0x10601002, ///< WPSPBC连接中
    STATUS_I_NET_WIFI_WPS_PIN = 0x10601003, ///< WPSPIN连接中
    STATUS_I_NET_WIFI_WPS_CANCEL = 0x10601004, ///< WPS连接撤销
    STATUS_I_NET_WIFI_WPS_SUCCESS = 0x10601005, ///< WPS连接成功
    STATUS_I_NET_WIFI_CONNECT_SUCCESS = 0x10601006, ///< WiFi连接成功
    STATUS_I_NET_WIFI_WFD_CONNECT_REQUEST = 0x10601007, ///< Wi-FiP2P手动连接请求
    STATUS_I_NET_WIFI_WPS_FAIL = 0x10601008, ///< WPS连接失败
    STATUS_I_NET_WIFI_WPS_OVERLAP = 0x10601009, ///< 检测到多个WPS会话,请稍后再试
    STATUS_I_NET_WIFI_WPS_TIMEOUT = 0x1060100a, ///< WPS连接超时
    STATUS_I_NET_WIFI_WFD_RADAR_FREQ = 0x1060100b, ///< 已连接的无线路由器使用雷达信道(52~144),Wi-Fi直连将被关闭
    STATUS_I_NET_SMTP_TEST_SUCCESS = 0x10602001, ///< SMTP测试邮件发送成功
    STATUS_I_NET_AIRPRINT_IDENTIFY_ACTION = 0x10603001, ///< 查找打印机
    STATUS_E_NET_WIFI_CONNECT_TIMEOUT = 0x30601001, ///< WiFi连接失败，超时
    STATUS_E_NET_WIFI_CONNECT_NO_SSID = 0x30601002, ///< WiFi连接失败，ssid不存在
    STATUS_E_NET_WIFI_CONNECT_ERR_PSK = 0x30601003, ///< WiFi连接失败，密码错误
    STATUS_E_NET_WIFI_CONNECT_FAIL = 0x30601004, ///< WiFi连接失败，未知错误
    STATUS_E_NET_WIFI_CONNECT_NO_RECORD = 0x30601005, ///< WiFi无连接记录(重连时)
    STATUS_E_NET_WIFI_DISCONNECT = 0x30601006, ///< WiFi连接断开
    STATUS_E_NET_WIFI_INIT_ERROR = 0x30601007, ///< WIFI故障（初始化WIFI模块错误）
    STATUS_E_NET_SMTP_TEST_FAIL = 0x30602001, ///< SMTP测试邮件发送失败
    STATUS_F_NET_WIFI_FATAL_ERROR = 0x40601001, ///< WiFi模块异常
    /* NET END */
    /* FRAMEWORK START */
    STATUS_I_FRAMEWORK_SUSPEND = 0x10A00001, ///< JOB挂起通知
    STATUS_I_FRAMEWORK_RESUME = 0x10A00002, ///< JOB恢复通知
    STATUS_I_FRAMEWORK_REQUEST_NEXT_PAGE = 0x10A00003, ///< job向用户请求下一页动作开始
    STATUS_I_FRAMEWORK_JOB_INFO_UPDATE = 0x10A00004, ///< job信息变化通知
    STATUS_I_FRAMEWORK_PRINTING_IN_JOB_START = 0x10A00005, ///< 插印job开始
    STATUS_I_FRAMEWORK_PRINTING_IN_JOB_FINISH = 0x10A00006, ///< 插印job结束
    STATUS_I_FRAMEWORK_RESUME_FAILED = 0x10A00007, ///< JOB挂起后恢复失败通知
    /* FRAMEWORK END */
    /* COMMON START */
    STATUS_I_COMMON_DATA_STORAGE_FULL = 0x10B00001, ///< 用于样本复印、逐份复印、样本打印、小册子复印等作业因存储空间不足而无法完成当前作业时，给用户提示。
    STATUS_E_COMMON_DATA_RECEIVE_TIMEOUT = 0x30B00001, ///< 数据接收超时
    STATUS_E_COMMON_DATA_PASER_FAILED = 0x30B00002, ///< 数据解析失败
    /* COMMON END */
    /* FWUPDATE START */
    STATUS_I_FWUPDATE_CONFIRM_UPGRADE = 0x10C00001, ///< 是否进行在线升级的选择弹窗
    STATUS_I_FWUPDATE_RESULT = 0x10C00002, ///< 升级结果
    STATUS_I_FWUPDATE_WAIT_FOR_PACKAGE = 0x10C20001, ///< 等待升级包下载
    STATUS_I_FWUPDATE_DOWNLOADING_PACKAGE = 0x10C20002, ///< 正在下载升级包
    STATUS_I_FWUPDATE_PACKAGE_UPGRADING = 0x10C20003, ///< 正在升级固件
    STATUS_I_FWUPDATE_ENGINE_UPGRADING = 0x10C20004, ///< 正在升级引擎固件
    STATUS_I_FWUPDATE_PACKAGE_VERIFING = 0x10C20005, ///< 固件校验中
    STATUS_I_FWUPDATE_PACKAGE_VERIFY_SUSS = 0x10C20006, ///< 固件校验成功
    STATUS_I_FWUPDATE_BASE_VALUE = 0x10C20007, ///< 是否更新基准值
    STATUS_I_FWUPDATE_PACKAGE_UPDATING = 0x10C20008, ///< 固件升级中弹窗请求
    STATUS_I_FWUPDATE_REMOVE_UPGRADE = 0x10C20009, ///< 移除升级状态
    STATUS_I_FWUPDATE_PACKAGE_VERIFY_FAILED = 0x10C2000A, ///< 验签失败
    STATUS_E_FWUPDATE_UNACTIVE = 0x30C00001, ///< 机器未激活
    /* FWUPDATE END */
    /* POWERMGR START */
    STATUS_I_POWERMGR_STATUS_UPDATE = 0x10D20001, ///< 当电源管理模块状态改变时，对外更新电源管理模块当前的状态
    STATUS_I_POWERMGR_SLEEPING = 0x10D20005, ///< 休眠过程中
    STATUS_I_POWERMGR_WAKINGUP = 0x10D20002, ///< 唤醒过程中
    STATUS_I_POWERMGR_WAKEUP = 0x10D20003, ///< 唤醒完成
    STATUS_I_POWERMGR_SLEEP = 0x10D20004, ///< 休眠
    STATUS_I_POWERMGR_POWEROFF = 0x10D20006, ///< 软关机
    STATUS_I_POWERMGR_IDLE = 0x10D20007, ///< 电源管理模块空闲
    /* POWERMGR END */
    /* STORAGE START */
    STATUS_E_STORAGE_INNER_MISTAKE_FORMAT = 0x30E20001, ///< 存储格式错误
    STATUS_E_STORAGE_HARDDISK_MISTAKE_FORMAT = 0x30E20002, ///< 硬盘格式错误
    STATUS_E_STORAGE_INNER_FAILURE = 0x30E20003, ///< 存储设备故障
    STATUS_E_STORAGE_HARDDISK_FAILURE = 0x30E20004, ///< 硬盘故障
    STATUS_E_STORAGE_INNER_CAPACITY_FULL = 0x30E20005, ///< 存储容量满错误
    STATUS_E_STORAGE_HARDDISK_CAPACITY_FULL = 0x30E20006, ///< 硬盘容量满错误
    STATUS_E_STORAGE_UDISK_CAPACITY_FULL =0x30E20007, ///< U盘容量满错误
    STATUS_W_STORAGE_INNER_HEALTH = 0x20E20001, ///< 存储健康程度低
    STATUS_W_STORAGE_INNER_CAPACITY_FULL = 0x20E20002, ///< 存储容量满
    STATUS_W_STORAGE_UDISK_CAPACITY_FULL = 0x20E20003, ///< U盘容量满
    STATUS_W_STORAGE_HARDDISK_CAPACITY_FULL = 0x20E20004, ///< 硬盘容量满
    STATUS_W_STORAGE_INNER_CAPACITY_WILLFULL = 0x20E20005, ///< 存储容量将满
    STATUS_W_STORAGE_UDISK_CAPACITY_WILLFULL = 0x20E20006, ///< U盘容量将满
    STATUS_W_STORAGE_HARDDISK_CAPACITY_WILLFULL = 0x20E20007, ///< 硬盘容量将满
    STATUS_W_HARDDISK_HEALTH = 0x20E20008, ///< 硬盘健康程度低
    STATUS_I_STORAGE_IDLE = 0x10E20001, ///< 存储管理模块IDLE
    /* STORAGE END */

    STATUS_INVALID = 0xFFFFFFFF, ///< 无效ID
    STATUS_ID_MASK = STATUS_INVALID

} STATUS_ID_E;

#endif // _STATUSID_H
/**
 * @}
 */
