/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file job_manager.h
 * @addtogroup system_manager
 * @{
 * @brief system job management module
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18
 */



#ifndef _JOB_MANAGER_H
#define _JOB_MANAGER_H

#include "msgrouter_main.h"
#include "pol/pol_types.h"
#include "memmgr/memmgr.h"
#include "qio/qio_general.h"
#include "pol/pol_define.h"

PT_BEGIN_DECLS

#define JOB_BAND_COUNT_MIN          (16*1024)
#define MAX_NUM (2)

// a enum of job class (for the function selection)
typedef enum
{
    JOB_CLASS_NONE = 0, ///< free to use
    JOB_CLASS_PRINT = 1,    ///< print job class
    JOB_CLASS_COPY = 2,     ///< copy job class
    JOB_CLASS_SCAN = 3,     ///< scan job class
    JOB_CLASS_FAX = 4,      ///< fax job class
    JOB_CLASS_TEST = 5,
    JO<PERSON>_CLASS_OTHER = 6,
    J<PERSON><PERSON>_CLASS_ACR = 7,

    JO<PERSON>_CLASS_MAX = 0xff
}JOB_CLASS_E;

typedef enum
{
    JOB_TYPE_NONE = 0,
    JOB_TYPE_PRINT_CGDI = 1 ,
    JOB_TYPE_PRINT_MGDI = 2,
    JOB_TYPE_PRINT_IPS = 3,
    JOB_TYPE_PRINT_PWG = 4,
    JOB_TYPE_PRINT_URF = 5,
    JOB_TYPE_PRINT_SAMPLE = 6,
    JOB_TYPE_PRINT_JPEG = 7,
    JOB_TYPE_PRINT_ACL = 8,
    JOB_TYPE_PRINT_DELAY = 9,
    JOB_TYPE_PRINT_PINCODE = 10,
    JOB_TYPE_PRINT_IMAGE = 11,
    JOB_TYPE_PRINT_CREATE_PDF = 12,

    JOB_TYPE_SCAN_PUSH = 20,  ///< 除推扫到PC驱动以外的推扫
    JOB_TYPE_SCAN_HOST_PUSH = 21,  ///<推扫到PC驱动
    JOB_TYPE_SCAN_PULL = 22,
    JOB_TYPE_SCAN_PUSH_D = 23,
    JOB_TYPE_SCAN_PULL_D = 24,

    JOB_TYPE_COPY = 40,
    JOB_TYPE_COPY_D = 41,
    JOB_TYPE_COPY_C = 42,
    JOB_TYPE_COPY_CORRECT = 43,
    JOB_TYPE_COLOR_REGISTOR = 44,

    JOB_TYPE_COPY_PUSH_SCAN = 45 ,
    JOB_TYPE_COPY_SCAN = 46 ,

    JOB_TYPE_MAX = 0xff
}JOB_TYPE_E;

typedef enum
{
    JOB_VIA_UNKNOWN = 0,
    JOB_VIA_USB,
    JOB_VIA_ETHERNET,
    JOB_VIA_IPP,
    JOB_VIA_GCP,
    JOB_VIA_WIFI,
    JOB_VIA_FILE,
    JOB_VIA_INTERNAL,
    JOB_VIA_FAX,
    JOB_VIA_AIRSCAN,
    JOB_VIA_UDISK,
    JOB_VIA_COPY,
    JOB_VIA_IMAGE,
    JOB_VIA_OTHER,
    JOB_VIA_DEBUG,
    JOB_VIA_MAX
}JOB_VIA_E;

typedef enum
{
    COPY_OBJ = 1 ,///< copy structure parameters
    SCAN_OBJ = 2, ///< scan structure parameters
    PRINT_OBJ = 3, ///< print structure parameters
    FILE_OBJ = 4, ///< pincode
    CREATE_PDF_OBJ = 5 ///< pdf report
}JOB_CONFIG_OBJ_E;

typedef enum
{
    JOB_END = 0, ///<completed normally.
    JOB_ABORTED = 1 , ///<abnormal termination
    JOB_CANCELED = 2 ///<normal Cancel
}FINISHED_DETAIL_E;

typedef enum
{
    JOB_NONE = 0 , ///<Default state, such as: Printing, Scanning, Copying.
    JOB_RECEIVING ,
    JOB_RESUMING ,
    JOB_RESTARTING ,
    JOB_RECEIVED
}RUNNING_DETAIL_E;

typedef enum
{
    JOB_SUSPEND_NONE = 0 ,
    JOB_SUSPENDED 
}SUSPENDING_DETAIL_E;

typedef enum
{
    JOB_PAUSE_NONE = 0 ,
    JOB_PAUSED
}PAUSING_DETAIL_E;

typedef enum
{
    JOB_WAITING=0,
    JOB_RUNNING=1,
    JOB_CANCELING=2,
    JOB_SUSPENDING=3,
    JOB_PAUSING=4,
    JOB_FINISHED
}JOB_STATUS_E;

typedef enum
{
    JOB_NORMAL = 0 ,
    JOB_PRIORITIZED ,
    JOB_INTERRUPTED ,
    JOB_MAINTAINED ,
    JOB_UPGRADE ,
    JOB_SILENT
}JOB_PRIORITY_E;

typedef struct
{
    uint32_t job_id;
    uint8_t job_owner[32];
    uint8_t job_name[128];
    uint8_t raster_file_path[64];///>Hard Disk Path of Jobs CMYK
    uint8_t prn_file_path[64];///>Hard Disk Path of Jobs PRN
    uint32_t timed_task_timestamp;
    uint8_t username[32];///<pincode username
    uint8_t password[64];///<pincode password,must be transmitted in ciphertext.
    uint8_t sample_config_args[768];///<sample job configure args
    uint8_t print_source;
    uint32_t error_code;
    uint32_t wonum;
}ATTRIBUTE_INFO_S;

typedef struct
{
    uint32_t job_id;
    JOB_CONFIG_OBJ_E obj;
    JOB_STATUS_E status; 
    uint32_t status_detail; ///<When status is JOB_RUNNING or JOB_FINISHED, this field is valid.
    uint32_t copies;
    uint32_t copies_pre_pages;
    uint32_t current_copies;
    uint32_t current_pages;
    uint32_t printed_total_pages;
    uint32_t total_pages;
    uint32_t scan_source;///<SCAN_MODE_E
    IO_VIA_E io_via;
    uint8_t job_owner[32];
    uint8_t job_name[128];
    uint8_t raster_file_path[64];///>Hard Disk Path of Jobs CMYK
    uint8_t prn_file_path[64];///>Hard Disk Path of Jobs PRN
    JOB_PRIORITY_E  priority;
    uint32_t timestamp; ///<Timestamp indicating when the job was received (year/month/day/hour/minute/second)
    uint32_t timed_task_timestamp;
    uint8_t username[32];//pincode username
    uint8_t password[64];//pincode password,must be transmitted in ciphertext.
    uint8_t sample_config_args[768];///<sample job configure args
    JOB_TYPE_E job_type;
    uint8_t print_source;
    uint32_t error_code;
    uint32_t wonum;
}JOBINFO_S;

typedef struct
{
    uint32_t job_id;
    uint32_t copies;
    uint32_t copies_pre_pages;
    uint32_t current_copies;
    uint32_t current_pages;
    uint32_t printed_total_pages;
    uint32_t total_pages;
    uint32_t scan_source;///<SCAN_MODE_E
}PAGEINFO_S;

#define PRINT_PARAMSTR_SIZE 256 // 根据打印模块的需要定义大小
#define SCAN_PARAMSTR_SIZE  256 // 根据扫描模块的需要定义大小
#define COPY_PARAMSTR_SIZE  256 // 根据复印模块的需要定义大小

typedef struct 
{
    char param_str[PRINT_PARAMSTR_SIZE];
} PRINT_PARAMINFO_S;

typedef struct 
{
    char param_str[SCAN_PARAMSTR_SIZE];
} SCAN_PARAMINFO_S;

typedef struct 
{
    char param_str[COPY_PARAMSTR_SIZE];
} COPY_PARAMINFO_S;

typedef union 
{
    PRINT_PARAMINFO_S print;
    SCAN_PARAMINFO_S  scan;
    COPY_PARAMINFO_S  copy;
} JOB_PARAM_U;

typedef struct 
{
    uint32_t job_id;
    JOB_PARAM_U job_param;
} PARAMINFO_S;

typedef enum
{
    G_SUCCESS = 0,
    G_AUTHORITY_FAIL = 1, ///< Illegal job authority
    G_PRIORITY_LIMIT = 2, ///< multiple job priorities are not supported
    G_JOB_INVALID = 3, ///<  INVALID JOB
    G_MEMORY_FAIL = 4 ,  ///< Not enough memory
    G_RESOURCE_FAIL = 5, ///< Not enough resources
    G_UNKNOW = 6
}JOBID_RET_CODE;


// a enum of job wait option
typedef enum
{
    JOB_WAIT_FOREVER = 0 ,
    JOB_NO_WAIT = 1,
}JOB_WAIT_OPTION_E;

typedef enum
{
    JOB_NOT_MONOPLZ    = 0,    ///< job non-independence¡¤job current run ,if have other job resource ,run at the same time(most of default)
    JOB_MONOPOLIZE     = 1,    ///< job non-independence¡¤job current run ,Other jobs must be queued even if there is a resource.
    JOB_OVER_MONOPLZ   = 2,    ///< Even if there is an exclusive job running, this job will not be able to keep in line if there is a resource
}JOB_MONOP_OPTION_E;

typedef struct
{
    IO_CLASS_E io_class;
    IO_VIA_E io_via;
    void *pqio; ///< sourced from NET/USB port
    void *job_config_param; ///< structural parameters delivered by the panel
    void (*notify)(uint32_t jobid , int result , void *context);//after the job is initiated,the callback function
                                           //will be triggered to notfiy the result of the job initiation.
    void *context; //when the notify callback function is triggered,context will be passed into the function as the third parameter.
}JOB_REQUEST_S;

typedef struct
{
    void *gqio; //GQIO_S structure
    void *job_config_param; ///<same as job_config_param in the JOB_REQUEST_S
    JOB_CONFIG_OBJ_E  job_obj; ///< The structure parameter object delivered by the panel
    void *prepare_priv_args;///< same as priv_args in the PREPARSER_RESULT_S ,
                            ///<  memory free by prepare_parser caller

    MEMOBJ_P             image_mem;
    MEMOBJ_P             video_mem;
    JOB_TYPE_E           job_type;
    JOB_CLASS_E          job_class;
    JOBINFO_S           *suspend_jobinfo;///< when the suspended job is resumed, this will be a valid pointer; otherwise, it will be NULL.
} JOB_RESOURCES_DATA_S;

typedef struct
{
    MODULE_ID_E   prev_module;
    MODULE_ID_E   next_module[MAX_NUM]; //up to 2 sibling modules , not exist is 0
    uint32_t   job_id;
    uint32_t   resource_id;
    uint32_t use_resource_number;
    JOB_RESOURCES_DATA_S  *job_resource_data;
} CURRENT_RESOURCE_DATA_S , *CURRENT_RESOURCE_DATA_P;

/**
 * @brief job manager initialization
 *
 * @return 0 on success , -1 on error
 */
int32_t job_manager_prolog(void);

/**
 * @brief job manager deinitialization
 */
void job_manager_epilog(void);

/**
 * @brief determine whether the job manager is busy
 *
 * @return 0 means idle , 1 means busy
 */
int32_t job_manager_busy(void);

/**
 * @brief determine whether there are print,scan,and copy jobs in progress
 *
 * @return 0 means no , 1 means yes
 */
int32_t job_manager_has_running_job(void);

/**
 * @brief Set current mode to upgrade.
 *
 * @return 0 on success , -1 on failure
 */
int32_t job_manager_set_upgrade_mode(void);

/**
 * @brief Clear upgrade mode.
 *
 * @return 0 on success , -1 on failure
 */
int32_t job_manager_clear_upgrade_mode(void);

/**
 * @brief Set current to silent mode.
 *
 * @return 0 on success , -1 on failure
 */
int32_t job_manager_set_silent_mode(void);

/**
 * @brief apply for a job id
 * @param iovia see IO_VIA_E
 * @param wonum work order number , currently used in the pedk project.
 *
 * @return a non-negative integer on success , 0 on failure
 */
uint32_t job_manager_apply_jobid(IO_VIA_E iovia , uint32_t wonum);

/**
 * @brief Search for job information with the specified ID.
 *
 * @param jobid job number
 * @param jobinfo If jobinfo is non-NULL, the return value is also stored in the memory pointed to by jobinfo
 * @param whether to search job from historical records
 *
 * @return 0 on success , -1 on failure 
 */
int32_t job_manager_find_job(uint32_t jobid , JOBINFO_S *jobinfo , uint8_t history_valid);

/**
 * @brief Get all active job information, including waiting, running, canceling , suspending, and pausing
 *
 * @param context Context passed by the caller
 * @param callback When callback is not NULL, pass the JOBINFO_S data and the context provided by the caller as the input parameters to the callback function
 *
 * @return The number of job existing in the list
 */
uint32_t job_manager_get_active_joblist(void *context , void(*callback)(const JOBINFO_S* job , void *context));

/**
 * @brief Get all historical job information that has been completed or cancelled
 *
 * @param context Context passed by the caller
 * @param callback When callback is not NULL, pass the JOBINFO_S data and the context provided by the caller as the input parameters to the callback function
 *
 * @return The number of job information
 */
uint32_t job_manager_get_history_joblist(void *context , int(*callback)(const JOBINFO_S* job , void *context, uint32_t size));

/**
 * 
 */
void job_manager_clear_history_joblist(void);

/**
 * @brief Get the list of job of a specific type.
 *
 * @param context Context passed by the caller
 * @param callback When callback is not NULL, pass the JOBINFO_S data and the context provided by the caller as the input parameters to the callback function
 *
 * @return The number of job information
 */
uint32_t job_manager_get_specify_joblist(JOB_TYPE_E job_type , void *context , void(*callback)(const JOBINFO_S* job , void *context));

/**
 * @brief Remove specified type of job list.
 *
 * @param job_type see JOB_TYPE_E
 */
void job_manager_remove_specify_joblist(JOB_TYPE_E job_type);

PT_END_DECLS

#endif //_JOB_MANAGER_H

/**
 * @}
 */
