INCLUDE_FLAGS := -Iinclude \
				-Iqio \
				-Iqio/ofd \
				-Invram \
				-Ievent_manager

LIB_FLAGS := -L/usr/local/lib -lgtest -lpthread $(LDFLAGS) -L./ -lcommon -losal  -lb64 
SO_LIB_FLAGS :=  $(LDFLAGS) -lssl -lz -lsqlite3

DEFINE_FUNC := Linux
DEFINE_FLAGS := $(patsubst %, -D%, $(DEFINE_FUNC))

TARGET := gtest/gtest_system

GTEST_MAIN := gtest/gtest_main.cpp \

#所有参与测试模块中的.cpp文件
DIR_MODULES := gtest/cmd_gtest/*.cpp \
			   gtest/nvram_gtest/*.cpp

MODULE_CPP := $(wildcard $(DIR_MODULES))

#需要用到的测试接口关联的.c文件
MODULE_SRC := cmd/*.c \
	event_manager/*.c \
	memmgr/*.c \
	qio/*.c \
	qio/ofd/*.c \
	nvram/*.c \
	utilities/*.c

SRC := $(wildcard $(MODULE_SRC))

OBJS := $(patsubst %.cpp,%.o,$(GTEST_MAIN)) \
	$(patsubst %.cpp,%.o,$(MODULE_CPP))

GTEST_CC := gcc
GTEST_CPP := g++
CFLAGS := -g -Wextra -std=gnu99
CPPFLAGS := -g  -Wextra

.PHONY:all
all:$(TARGET)
$(TARGET):$(OBJS)
	$(GTEST_CC) -fPIC -shared -o libcommon.so $(SRC) $(INCLUDE_FLAGS) $(DEFINE_FLAGS) $(CFLAGS) $(SO_LIB_FLAGS)

	@echo " [CPP]   $@"
	$(GTEST_CPP) $(OBJS) -o $(TARGET)  $(LIB_FLAGS) $(LDFLAGS)
	@echo -e
	@echo "****************************"
	@echo "*****gtest build Finish*****"
	@echo "****************************"


%.o:%.c
	@echo " [CC]   $@"
	$(GTEST_CC) -c $(CFLAGS) $(DEFINE_FLAGS) $(INCLUDE_FLAGS)  $^ -o $@

%.o:%.cpp
	@echo " [CPP] $(CPP)  $@"
	$(GTEST_CPP) -c $(CPPFLAGS) $(DEFINE_FLAGS) $(INCLUDE_FLAGS) $^ -o $@

