#include "acl_attribute_demo.h"
#include "pol/pol_string.h"
#include "pol/pol_threads.h"
#include "pol/pol_log.h"
#include "pol/pol_mem.h"

#define MAX_INDEX_NUM       10
#define MAX_STRING_LEN      128

#define DEMO_OK        0
#define DEMO_FAIL     -1

/*the demo index shows the OID.idx */

/*OID_DEMO_UINT32_VALUE*/
static uint32_t s_uint32_value = 0;

/*(OID_DEMO_UINT32_INDEX.0 - OID_DEMO_UINT32_INDEX.7) */
static uint32_t s_uint32_index[8] = {0,1,2,3,4,5,6,7};

/*OID_DEMO_STRING_VALUE*/
static char     s_string_value[MAX_STRING_LEN] = "string_list_value";

/*(OID_DEMO_STRING_INDEX.0 - OID_DEMO_STRING_INDEX.7) */
static char     s_string_index[8][MAX_STRING_LEN] =
{
    "string_list_index_0", "string_list_index_1", "string_list_index_2", "string_list_index_3",
    "string_list_index_4", "string_list_index_5", "string_list_index_6", "string_list_index_7"
};

/******************************************************************
 *                      ATTENTION
 *
 *  -- All the callback funtion need to return value
 *
 *      -- if the callback function is processed successfully
 *                  -- return 0   or    return positive-value
 *
 *      -- if the callbcak function is processed failed
 *                  -- return negative-value
 *
 ******************************************************************/

/*OID_DEMO_UINT32_VALUE*/
int32_t demo_set_uint32_value(uint32_t value)
{
    pi_log_d( "set demo uint32 value to %d\n", value);

    /*modify the local value*/
    s_uint32_value = value;

    /*other local processing(if needed), if failed, return negative ret*/

    /*save the value to nvram (if needed)*/

    /*send the attr-modified-event to the event manager to let the observer know (if needed) */

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}

int32_t demo_get_uint32_value(uint32_t* value)
{
    if(NULL == value)
    {
        pi_log_e("NULL value pointer!\n");
        return DEMO_FAIL;
    }

    /*return the local value*/
    *value = s_uint32_value;
    pi_log_d( "get demo string value is %d\n", *value);

    /*other local processing(if needed), if failed, return negative ret*/

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}

/*(OID_DEMO_UINT32_INDEX.0 - OID_DEMO_UINT32_INDEX.7) */
int32_t demo_set_uint32_index(uint32_t index, uint32_t value)
{
    pi_log_d( "set demo uint32 index %d to %d\n", index, value);

    /*modify the local value*/
    s_uint32_index[index] = value;

    /*other local processing(if needed), if failed, return negative ret*/

    /*save the value to nvram (if needed)*/

    /*send the attr-modified-event to the event manager to let the observer know (if needed) */

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}


int32_t demo_get_uint32_index(uint32_t index, uint32_t* value)
{
    if(NULL == value)
    {
        pi_log_e("NULL value pointer!\n");
        return DEMO_FAIL;
    }

    /*return the local value*/
    *value = s_uint32_index[index];
    pi_log_d( "get demo uint32 index %d is %d\n", index, *value);

    /*other local processing(if needed), if failed, return negative ret*/

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}

/******************************************************************
 *                      ATTENTION
 *
 *  -- Suggest to use MIN macro to void the memory out of range
 *
 ******************************************************************/
//#define MIN(X, Y)       (((X) < (Y))?(X):(Y))

/*OID_DEMO_STRING_VALUE*/
int32_t demo_set_string_value(char* value, uint32_t len)
{
    if((NULL == value) || (0 == len))
    {
        pi_log_e("NULL value pointer!\n");
        return DEMO_FAIL;
    }

    pi_log_d( "set demo string value to %s\n", value);

    pi_memset(s_string_value, 0, MAX_STRING_LEN);

    /*modify the local value*/
    pi_strncpy(s_string_value, value, MIN(MAX_STRING_LEN, len));

    /*other local processing(if needed), if failed, return negative ret*/

    /*save the value to nvram (if needed)*/

    /*send the attr-modified-event to the event manager to let the observer know (if needed) */

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}


int32_t demo_get_string_value(char* value, uint32_t len)
{
    if((NULL == value) || (0 == len))
    {
        pi_log_e("NULL value pointer!\n");
        return DEMO_FAIL;
    }

    /*return the local value*/
    pi_strncpy(value, s_string_value, MIN(MAX_STRING_LEN, len));

    pi_log_d( "get demo string value is %s\n", value);

    /*other local processing(if needed), if failed, return negative ret*/

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}


/*(OID_DEMO_STRING_INDEX.0 - OID_DEMO_STRING_INDEX.7) */
int32_t demo_set_string_index(uint32_t index, char* value, uint32_t len)
{
    if((NULL == value) || (0 == len))
    {
        pi_log_e("NULL value pointer!\n");
        return DEMO_FAIL;
    }

    pi_log_d( "set demo string index %d to %s\n", index, value);

    pi_memset(s_string_index[index], 0, MAX_STRING_LEN);

    /*modify the local value*/
    pi_strncpy(s_string_index[index], value, MIN(MAX_STRING_LEN, len));

    /*other local processing(if needed), if failed, return negative ret*/

    /*save the value to nvram (if needed)*/

    /*send the attr-modified-event to the event manager to let the observer know (if needed) */

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}


int32_t demo_get_string_index(uint32_t index, char* value, uint32_t len)
{
    if((NULL == value) || (0 == len))
    {
        pi_log_e("NULL value pointer!\n");
        return DEMO_FAIL;
    }

    /*return the local value*/
    pi_strncpy(value, s_string_index[index], MIN(MAX_STRING_LEN, len));

    pi_log_d( "get demo string index %d is %s\n", index, value);

    /*other local processing(if needed), if failed, return negative ret*/

    /*if proc failed, return negative ret, if proc successfully, then return 0 or positive ret*/
    return DEMO_OK;
}


