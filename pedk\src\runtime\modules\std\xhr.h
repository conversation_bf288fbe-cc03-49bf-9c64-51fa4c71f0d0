/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       xhr.h
 * @addtogroup 
 * @{
 * <AUTHOR>
 * @date    2023/12/15
 * @version v1.0
 * @details 
 * 
 */

#ifndef __MODULES_STD_NET_XHR_H__
#define __MODULES_STD_NET_XHR_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <quickjs.h>

void add_xhr(JSContext* ctx);

#ifdef __cplusplus
}  /* End of the 'extern "C"' block */
#endif

#endif //__MODULES_STD_NET_XHR_H__

/**
*@}
*/