/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file netsock.h
 * @addtogroup net
 * @{
 * @addtogroup netsock
 * <AUTHOR>
 * @date 2023-4-20
 * @brief network socket API
 */
#ifndef __NETSOCK_H__
#define __NETSOCK_H__

#include "nettypes.h"

/**
 * @brief       Create custom socket handle. This function can be seen as\n
 *              PI_SOCKET_T netsock_create_custom(int32_t domain, int32_t type, int32_t protocol);
 * @param[in]   domain      : The domain argument specifies a communication domain, eg. AF_INET or AF_INET6.
 * @param[in]   type        : The socket has the indicated type, which specifies the communication semantics, eg. SOCK_STREAM.
 * @param[in]   protocol    : The protocol specifies a particular protocol to be used with the socket, eg. IPPROTO_IP.
 * @return      socket handle.
 * @retval      >= 0        : success\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
PI_SOCKET_T     net_socket_create_custom            (int32_t domain, int32_t type, int32_t protocol, const char* caller);
#define netsock_create_custom(a, b, c)              net_socket_create_custom(a, b, c, __func__)

/**
 * @brief       Create socket handle for udp. This function can be seen as\n
 *              PI_SOCKET_T netsock_create_multicast(uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver);
 * @param[in]   port        : The network port number.
 * @param[in]   ifid        : The network interface index.
 * @param[in]   ipver       : The IP version.
 * @return      socket handle.
 * @retval      >= 0        : success\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
PI_SOCKET_T     net_socket_create_multicast         (uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* caller);
#define netsock_create_multicast(a, b, c)           net_socket_create_multicast(a, b, c, __func__)

/**
 * @brief       Create socket handle as TCP client by URL and port. This function can be seen as\n
 *              PI_SOCKET_T netsock_create_by_url(uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver);
 * @param[in]   url         : The URL of ready to connect.
 * @param[in]   port        : The network port number.
 * @param[in]   tos         : The timeout time(s).
 * @param[in]   tous        : The timeout time(us).
 * @return      socket handle.
 * @retval      >= 0        : success\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
PI_SOCKET_T     net_socket_create_by_url            (const char* url, uint16_t port, int32_t tos, int32_t tous, const char* caller);
#define netsock_create_by_url(a, b, c, d)           net_socket_create_by_url(a, b, c, d, __func__)

/**
 * @brief       Create socket handle as TCP server. This function can be seen as\n
 *              PI_SOCKET_T netsock_create_tcpserver(const char* ipaddr, uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver, int32_t max_conn);
 * @param[in]   ipaddr      : The ip address string, it could be empty.
 * @param[in]   port        : The network port number listening.
 * @param[in]   ifid        : The network interface index.
 * @param[in]   ipver       : The IP version.
 * @param[in]   max_conn    : The max conntion count listening.
 * @return      socket handle.
 * @retval      >= 0        : success\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
PI_SOCKET_T     net_socket_create_tcpserver         (const char* ipaddr, uint16_t port, IFACE_ID_E ifid, IP_VERSION_E ipver, int32_t max_conn, const char* caller);
#define netsock_create_tcpserver(a, b, c, d, e)     net_socket_create_tcpserver(a, b, c, d, e, __func__)

/**
 * @brief       Reload these socket handle as TCP server. This function can be seen as\n
 *              netsock_reload_tcpserver(PI_SOCKET_T sockfd[IPVER_NUM], fd_set* readfds, int32_t* maxfd, int32_t update, uint16_t port, int32_t max_conn);
 * @param[in]   sockfd      : these socket handle array.
 * @param[in]   readfds     : readfds used by select.
 * @param[in]   maxfd       : maxfd used by select.
 * @param[in]   update      : update flag.
 * @param[in]   port        : The network port number listening.
 * @param[in]   max_conn    : The max conntion count listening.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            net_socket_reload_tcpserver         (PI_SOCKET_T sockfd[IPVER_NUM], fd_set* readfds, int32_t* maxfd, int32_t update, uint16_t port, int32_t max_conn, const char* caller);
#define netsock_reload_tcpserver(a, b, c, d, e, f)  net_socket_reload_tcpserver(a, b, c, d, e, f, __func__)

/**
 * @brief       Join the socket handle to multicast group. This function can be seen as\n
 *              int32_t netsock_join_multicast_group(PI_SOCKET_T sockfd, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* local_addr, const char* mcast_addr);
 * @param[in]   sockfd      : The socket handle.
 * @param[in]   ifid        : The network interface index.
 * @param[in]   ipver       : The IP version.
 * @param[in]   local_addr  : local address string.
 * @param[in]   mcast_addr  : multicast address string.
 * @return      Join result.
 * @retval      >= 0        : success\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_socket_join_multicast_group     (PI_SOCKET_T sockfd, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* local_addr, const char* mcast_addr, const char* caller);
#define netsock_join_multicast_group(a, b, c, d, e) net_socket_join_multicast_group(a, b, c, d, e, __func__)

/**
 * @brief       Leave the socket handle to multicast group. This function can be seen as\n
 *              int32_t netsock_leave_multicast_group(PI_SOCKET_T sockfd, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* mcast_addr);
 * @param[in]   sockfd      : The socket handle.
 * @param[in]   ifid        : The network interface index.
 * @param[in]   ipver       : The IP version.
 * @param[in]   mcast_addr  : multicast address string.
 * @return      Leave result.
 * @retval      >= 0        : success\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_socket_leave_multicast_group    (PI_SOCKET_T sockfd, IFACE_ID_E ifid, IP_VERSION_E ipver, const char* mcast_addr, const char* caller);
#define netsock_leave_multicast_group(a, b, c, d)   net_socket_leave_multicast_group(a, b, c, d, __func__)

/**
 * @brief       Select the socket handle. This function can be seen as\n
 *              int32_t netsock_select(PI_SOCKET_T sockfd, uint32_t read, uint32_t write, uint32_t error, int32_t tos, int32_t tous);
 * @param[in]   sockfd      : The socket handle.
 * @param[in]   read        : read flag
 * @param[in]   write       : write flag
 * @param[in]   error       : error flag
 * @param[in]   tos         : The timeout time(s).
 * @param[in]   tous        : The timeout time(us).
 * @return      join result.
 * @retval      >  0        : success\n
 *              == 0        : timeout\n
 *              <  0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_socket_select                   (PI_SOCKET_T sockfd, uint32_t read, uint32_t write, uint32_t error, int32_t tos, int32_t tous, const char* caller);
#define netsock_select(a, b, c, d, e, f)            net_socket_select(a, b, c, d, e, f, __func__)

/**
 * @brief       Peek the readable number from socket handle. This function can be seen as\n
 *              int32_t netsock_peek_readable_number(PI_SOCKET_T sockfd);
 * @param[in]   sockfd      : The socket handle.
 * @return      peek result.
 * @retval      >  0        : success\n
 *              <= 0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_socket_peek_readable_number     (PI_SOCKET_T sockfd, const char* caller);
#define netsock_peek_readable_number(a)             net_socket_peek_readable_number(a, __func__)

/**
 * @brief       Accept the connect request and construct NET_CONN_S object pointer. This function can be seen as\n
 *              NET_CONN_S* netsock_accept_connection(PI_SOCKET_T sockfd, IP_VERSION_E ipver);
 * @param[in]   sockfd      : The socket handle.
 * @param[in]   ipver       : The IP version.
 * @return      NET_CONN_S object pointer.
 * @retval      != NULL     : success\n
 *              == NULL     : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
NET_CONN_S*     net_socket_accept_connection        (PI_SOCKET_T listen_sockfd, IP_VERSION_E ipver, const char* caller);
#define netsock_accept_connection(a, b)             net_socket_accept_connection(a, b, __func__)

/**
 * @brief       Peek the readable number from this connection. This function can be seen as\n
 *              int32_t netsock_peek_connection(NET_CONN_S* pnc, int32_t timeout);
 * @param[in]   pnc         : The NET_CONN_S object pointer.
 * @param[in]   timeout     : The timeout time(s).
 * @return      peek result.
 * @retval      >  0        : success\n
 *              <= 0        : fail
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
int32_t         net_socket_peek_connection          (NET_CONN_S* pnc, int32_t timeout, const char* caller);
#define netsock_peek_connection(a, b)               net_socket_peek_connection(a, b, __func__)

/**
 * @brief       Close this connection. This function can be seen as\n
 *              int32_t netsock_close_connection(NET_CONN_S* pnc);
 * @param[in]   pnc         : The NET_CONN_S object pointer.
 * <AUTHOR> Xin
 * @date        2023-9-16
 */
void            net_socket_close_connection         (NET_CONN_S* pnc, const char* caller);
#define netsock_close_connection(a)                 net_socket_close_connection(a, __func__)

#endif /* __NETSOCK_H__ */
/**
 *@}
 */
