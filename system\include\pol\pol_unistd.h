/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file pol_unistd.h
 * @addtogroup unistd
 * @{
 * <AUTHOR>
 * @date 2023-05-06
 * @version v1.0
 * @brief pantum osal misc standard interface set
 */
#ifndef __POL_UNISTD_H__
#define __POL_UNISTD_H__

#include <unistd.h>
#include <sys/types.h>
#include <pol/pol_define.h>

PT_BEGIN_DECLS

/**
 * @brief Returns the thread ID of the calling thread
 * using the Pi library's threading mechanism.
 */
pid_t pi_gettid();


PT_END_DECLS

#endif
/**
 *@}
 */

