/* ************************************ (c) Roon ** Start File ******************************************* */
/*************************************************************************************
 * File Name   	: httc_audit.c
 * Description 	: audit log for HTTC modules
 * Data	    	: 2019 - 10 - 14
 * By	        : RoonZhang
 * Email       	: <EMAIL>
 * Platform    	: linux-4.2.8 with sdk_marvell_88pa62x0
 * Explain     	: None
 * Modify	    : None
 ************************************************************************************* */
/* head files include --------------------------------------------------------------- */
#include <string.h>
#include <stdlib.h>
#include <time.h>
#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>
#include <fcntl.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <netdb.h>
#include <sys/mman.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <net/ethernet.h>
#include <net/if.h>
#include <arpa/inet.h>
#include <errno.h>
#include "pol/pol_threads.h"
//#include "oid_api.h"
#include "httc/httc_audit.h"
#include <string.h>
#include <stdio.h>
#include "public_data_proc.h"
#include "public/msgrouter_main.h"
#include "utilities/msgrouter.h"
#include "qio/qio_general.h"
#include "job_manager.h"
#include "pol/pol_log.h"
#include "cjson/cJSON.h"
//#include "httc_version.h" 

/* global macro ----------------------------------------------------------------------- */
#define HTTC_SUCESS_LOG_MAX_NUMBER		4000  // 鏈€澶ф垚鍔熸棩蹇楁潯
#define HTTC_FAIL_LOG_MAX_NUMBER        1000  // 鏈€澶уけ璐ユ棩蹇楁潯
#define HTTC_LOG_LINE_WIDTH				1024  // 姣忔潯鏃ュ織鐨勬渶澶у 瀛楄妭)

/* global values ---------------------------------------------------------------------- */
static PI_THREAD_T  g_httc_socket_taskID  = INVALIDTHREAD;

#define RN_RUN_CMD(...)	    pi_runcmd(NULL, 0, 0, __VA_ARGS__)
#define RN_RUN_CMD_BG(...)	pi_runcmd(NULL, 0, 1, __VA_ARGS__)

/* functions statement ---------------------------------------------------------------- */
//閲嶈鍙俊鏃ュ織
static int RebuildHTTCLog(char *src_path,char *dst_path,int max_num)
{
    FILE  *fp_src = NULL;
    FILE  *fp_dst = NULL;
    int    i = 0;

    fp_src = fopen(src_path, "r");
    fp_dst = fopen(dst_path, "wb+");

    if (NULL == fp_dst)
    {
        pi_log_e("fopen %s error.\n", dst_path);
        if (fp_src) fclose(fp_src);
        return (-5);
    }

    //璺宠繃婧愭枃浠剁殑绗竴琛岋紝灏嗙浜岃鏁版嵁浣滀负绗竴琛屽啓鍏ヤ复鏃舵枃浠朵腑
    for (i = 1; i <= max_num; i++)
    {
        unsigned int src_index;
        struct log_msg_st     log;
        memset((void *)&log, 0x0, sizeof(typeof(log)));

        // 璇诲彇鍘熸棩蹇楁枃浠讹紝鏍煎紡鍖栬緭鍑哄埌log涓紝姣忔杈撳嚭涓€       
        src_index = i * sizeof(typeof(log));
        fseek(fp_src, src_index, SEEK_SET);
        fread((void *)&log, sizeof(typeof(log)), 1, fp_src);

        // 鍐欏叆鏂囨湰鏂囦欢
        fwrite(&log, sizeof(typeof(log)), 1, fp_dst);
    }
    fclose(fp_src);
    fp_src = NULL;
    fclose(fp_dst);
    fp_dst = NULL;

    //鎵ц鏂囦欢鏇挎崲鎿嶄綔
    pi_runcmd(NULL, 0, 0, "rm %s", src_path);
    pi_runcmd(NULL, 0, 0, "mv %s %s", dst_path, src_path);

    return 0;
}

/************************************************************************
 * Function Name : int HttcGetLogIndex(HTTC_LOG_TYPE log_type)
 * Description   : get httc log index
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcGetLogIndex(HTTC_LOG_TYPE log_type)
{
    FILE  *fp = NULL;
    unsigned int audit_log_index;
    int ret = 0;
    char *path = NULL;
    int log_num_max;

    if(HTTC_SUCCESS_LOG == log_type)
    {
        path = HTTC_SUCESS_LOG_FILE_PATH;
        log_num_max = HTTC_SUCESS_LOG_MAX_NUMBER;
    }
    else
    {
        path = HTTC_FAIL_LOG_FILE_PATH;
        log_num_max = HTTC_FAIL_LOG_MAX_NUMBER;
    }

    if (0 != access(path, F_OK))
    {
        audit_log_index = 0;
        ret = audit_log_index;
        fp = fopen(path, "wb+");
        if (NULL == fp)
        {
            pi_log_e("failed to create file %s\n", path);
            return (-1);
        }
        fseek(fp, 0, SEEK_SET);
        fwrite(&audit_log_index, sizeof(uint32_t), 1, fp);

        fclose(fp);
        fp = NULL;
    }
    else
    {
        fp = fopen(path, "rb+");
        if (NULL == fp)
        {
            pi_log_e("fopen %s error.\n", path);
            return (-1);
        }
        fseek(fp, 0, SEEK_SET);
        fread(&audit_log_index, sizeof(uint32_t), 1, fp);

        audit_log_index++;
        if(audit_log_index > log_num_max)
        {
            if(HTTC_SUCCESS_LOG == log_type)
            {
                RebuildHTTCLog(HTTC_SUCESS_LOG_FILE_PATH,HTTC_TEMP_LOG_FILE_PATH,HTTC_SUCESS_LOG_MAX_NUMBER);
            }
            else
            {
                RebuildHTTCLog(HTTC_FAIL_LOG_FILE_PATH,HTTC_TEMP_LOG_FILE_PATH,HTTC_FAIL_LOG_MAX_NUMBER);
            }
            audit_log_index = log_num_max;
        }
        ret = audit_log_index;

        fseek(fp, 0, SEEK_SET);
        fwrite(&audit_log_index, sizeof(uint32_t), 1, fp);

        fclose(fp);
        fp = NULL;
    }
    return ret;
}

/************************************************************************
 * Function Name : int HttcSaveLogToFlash
 * Description   : write log
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  struct log_msg_st *pLog  	log message
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcSaveLogToFlash(HTTC_LOG_TYPE log_type, struct log_msg_st *pLog)
{
    FILE  *fp = NULL;
    uint32_t log_len = sizeof(struct log_msg_st);
    uint32_t file_offset;
    int log_index;
    char *path = NULL;

    if(HTTC_SUCCESS_LOG == log_type)
        path = HTTC_SUCESS_LOG_FILE_PATH;
    else
        path = HTTC_FAIL_LOG_FILE_PATH;


    if (!pLog)
    {
        pi_log_e("pLog is NULL!!\n");
        return (-1);
    }

    log_index = HttcGetLogIndex(log_type);
    if(log_index<0)
    {
        pi_log_e("httc log_index error.\n");
        return (-1);
    }

    // 浣跨敤rb+妯″紡鎵撳紑鏂囦欢锛屽彲浠ュ線涓棿浠绘剰浣嶇疆瑕嗙洊鎻掑叆鏁版嵁
    fp = fopen(path, "rb+");
    if (NULL == fp)
    {
        pi_log_e("fopen %s error.\n", path);
        return (-1);
    }


    // 鍦ㄦ枃浠朵腑瑕嗙洊鎻掑叆
    file_offset = (log_index  * log_len) + sizeof(uint32_t);
    fseek(fp, file_offset, SEEK_SET);


    // 鍐欏叆鏃ュ織
    fwrite((const void*)pLog, sizeof(struct log_msg_st), 1, fp);

    fclose(fp);
    fp = NULL;
    return 0;
}


/************************************************************************
 * Function Name : HttcParseCJsonBootMeasure
 * Description   : 瑙ｆ瀽鍚姩搴﹂噺鏃ュ織
 * Parameters    : char* pBody
 *				  
 * Return        : void
 * Data	      	: 2020 - 10 - 28
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcParseCJsonBootMeasure(char* pBody)
{
    cJSON *json,*arrayItem,*item,*object;
    int arraySize,i;
    struct log_msg_st lms;

    json = cJSON_Parse(pBody);
    if(!json)
    {
        pi_log_e("boot measure pBody error\n");
        return -1;
    }
    arrayItem = cJSON_GetObjectItem(json, "bmeasure_log");
    if(!arrayItem)
    {
        pi_log_e("boot measure array error\n");
        return -1;
    }

    arraySize = cJSON_GetArraySize(arrayItem);
    //printf("arraySize = %d\n",arraySize);

    for(i = 0; i < arraySize; i++)
    {
        object = cJSON_GetArrayItem(arrayItem, i);

        memset(&lms, 0, sizeof(struct log_msg_st));

        //time
        item = cJSON_GetObjectItem(object, "time");
        if(NULL != item)
        {
            memcpy(lms.time, item->valuestring, strlen(item->valuestring)<TIME_LEN?strlen(item->valuestring):TIME_LEN);
            //printf("\033[35m" "time: %s-%d\n" "\033[0m", lms.time, strlen(item->valuestring));
        }

        //subject
        item = cJSON_GetObjectItem(object, "processname");
        if(NULL != item)
        {
            memcpy(lms.subject, item->valuestring, strlen(item->valuestring));
            //printf("\033[35m" "subject: %s-%d\n" "\033[0m", lms.subject, strlen(item->valuestring));
        }

        //object
        item = cJSON_GetObjectItem(object, "object");
        if(NULL != item)
        {
            memcpy(lms.object, item->valuestring, strlen(item->valuestring));
            //printf("\033[35m" "object: %s-%d\n" "\033[0m", lms.object, strlen(item->valuestring));
        }

        //local_user
        item = cJSON_GetObjectItem(object, "local_user");
        if(NULL != item)
        {
            sprintf(item->valuestring, "%d", lms.userid);
            //printf("\033[35m" "userid: %d\n" "\033[0m", lms.userid);
        }

        //pid
        item = cJSON_GetObjectItem(object, "pid");
        if(NULL != item)
        {
            sscanf(item->valuestring, "%d", &(lms.pid));
            //printf("\033[35m" "pid: %d\n" "\033[0m", lms.pid);
        }

        //type
        item = cJSON_GetObjectItem(object, "type");
        if(NULL != item)
        {
            sscanf(item->valuestring,"%d", (int*)&(lms.type));
            //printf("\033[35m" "type: %d\n" "\033[0m",lms.type);
        }

        //hash
        item = cJSON_GetObjectItem(object, "pro_hash");
        if(NULL != item)
        {
            memcpy(lms.hash, item->valuestring, strlen(item->valuestring)<HASH_LEN?strlen(item->valuestring):HASH_LEN);
            //printf("\033[35m" "pro_hash: %s-%d\n" "\033[0m", lms.hash, strlen(item->valuestring));
        }

        //result
        item = cJSON_GetObjectItem(object, "result");
        if(NULL != item)
        {
            lms.result = item->valueint;
            //printf("\033[35m" "result: %d\n" "\033[0m", lms.result);
        }

        //save httc log to flash
        if(0 != lms.result)
        {
            HttcSaveLogToFlash(HTTC_FAIL_LOG, &lms);
        }
        else if(0 == lms.result)
        {
            HttcSaveLogToFlash(HTTC_SUCCESS_LOG, &lms);
        }
    }

    cJSON_Delete(json);

    return 0;
}


/************************************************************************
 * Function Name : HttcParseCJsonReportLinux
 * Description   : 瑙ｆ瀽鐧藉悕鍗曘€佹枃浠惰闂帶鍒躲€佸姩鎬佸害閲忔棩蹇�
 * Parameters    : char* pBody
 *				  
 * Return        : void
 * Data	      	: 2020 - 10 - 28
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcParseCJsonReportLinux(char* pBody)
{
    cJSON *json, *arrayItem, *item, *object;
    int arraySize, i, k;
    struct log_msg_st lms;
    const char* ObjectItem[3] = {"whitelist_log", "fileaccess_log", "dmeasure_log"};

    json = cJSON_Parse(pBody);
    if(!json)
    {
        pi_log_e("report linux pBody error\n");
        return -1;
    }
    //寰幆瑙ｆ瀽涓夌鏃ュ織
    for(k = 0; k < (sizeof(ObjectItem)/sizeof(char *)); k++)
    {
        arrayItem = cJSON_GetObjectItem(json, ObjectItem[k]);
        if(!arrayItem)
        {
            pi_log_e("report linux array error\n");
            return -1;
        }
        arraySize = cJSON_GetArraySize(arrayItem);

        //printf("arraySize = %d\n",arraySize);

        for(i = 0;i<arraySize;i++)
        {
            object = cJSON_GetArrayItem(arrayItem, i);

            memset(&lms, 0, sizeof(struct log_msg_st));

            //time
            item = cJSON_GetObjectItem(object, "time");
            if(NULL != item)
            {
                memcpy(lms.time, item->valuestring, strlen(item->valuestring)<TIME_LEN?strlen(item->valuestring):TIME_LEN);
                //printf("\033[35m" "time: %s-%d\n" "\033[0m", lms.time, strlen(item->valuestring));
            }

            //local_user
            item = cJSON_GetObjectItem(object, "local_user");
            if(NULL != item)
            {
                sprintf(item->valuestring, "%d", lms.userid);
                //printf("\033[35m" "local_user: %d\n" "\033[0m", lms.userid);
            }

            //pid
            item = cJSON_GetObjectItem(object, "pid");
            if(NULL != item)
            {
                sscanf(item->valuestring, "%d", &(lms.pid));
                //printf("\033[35m" "pid: %d\n" "\033[0m", lms.pid);
            }

            //subject
            item = cJSON_GetObjectItem(object, "processname");
            if(NULL != item)
            {
                memcpy(lms.subject, item->valuestring, strlen(item->valuestring));
                //printf("\033[35m" "subject: %s-%d\n" "\033[0m", lms.subject, strlen(item->valuestring));
            }

            //hash
            item = cJSON_GetObjectItem(object, "pro_hash");
            if(NULL != item)
            {
                memcpy(lms.hash, item->valuestring, strlen(item->valuestring)<HASH_LEN?strlen(item->valuestring):HASH_LEN);
                //printf("\033[35m" "pro_hash: %s-%d\n" "\033[0m", lms.hash, strlen(item->valuestring));
            }

            //object
            item = cJSON_GetObjectItem(object, "object");
            if(NULL != item)
            {
                memcpy(lms.object, item->valuestring, strlen(item->valuestring));
                //printf("\033[35m" "object: %s-%d\n" "\033[0m", lms.object, strlen(item->valuestring));
            }

            //type
            item = cJSON_GetObjectItem(object, "type");
            if(NULL != item)
            {
                sscanf(item->valuestring,"%hu", (unsigned short int*)&(lms.type));
                //printf("\033[35m" "type: %d\n" "\033[0m",lms.type);
            }

            //operate
            item = cJSON_GetObjectItem(object, "operate");
            if(NULL != item)
            {
                memcpy(lms.operate,item->valuestring,strlen(item->valuestring));
                //printf("\033[35m" "operate: %s\n" "\033[0m",lms.operate);
            }
            //result
            item = cJSON_GetObjectItem(object, "result");
            if(NULL != item)
            {
                sscanf(item->valuestring, "%hu", (unsigned short int*)&(lms.result));
                //printf("\033[35m" "result: %d\n" "\033[0m", lms.result);
            }

            //save httc log to flash
            if(RESULT_FAIL == lms.result)
            {
                HttcSaveLogToFlash(HTTC_FAIL_LOG, &lms);
            }
            else if(RESULT_SUCCESS == lms.result)
            {
                HttcSaveLogToFlash(HTTC_SUCCESS_LOG, &lms);
            }
        }
    }	

    cJSON_Delete(json);
    return 0;
}

/************************************************************************
 * Function Name : HttcParseLog
 * Description   : 瑙ｆ瀽鍙俊鏃ュ織
 * Parameters    : char* szBuf
 *				  
 * Return        : void
 * Data	      	: 2020 - 10 - 28
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static int HttcParseLog(char* szBuf)
{
    char* pInterfaceName = strstr(szBuf, "/platform/");
    if (!pInterfaceName)
    {
        pi_log_e("packet error!\n");
        return -1;
    }
    pInterfaceName += strlen("/platform/");

    char* packet = strstr(pInterfaceName, " ");
    if (!packet)
    {
        pi_log_e("packet error!\n");
        return -1;
    }
    *packet = '\0';
    packet++;

    char* pBody = strstr(packet, "\r\n\r\n");
    if (!pBody)
    {
        pi_log_e("pBody error!\n");
        return -1;
    }
    pBody += strlen("\r\n\r\n");

    if (strcmp(pInterfaceName, "reportLinuxLog") == 0)
    {
        //鐧藉悕鍗曘€佹枃浠惰闂帶鍒躲€佸姩鎬佸害閲忔棩蹇�
        return HttcParseCJsonReportLinux(pBody);
    }
    else if (strcmp(pInterfaceName, "reportBootMeasureLog") == 0)
    {
        //鍚姩搴﹂噺
        return HttcParseCJsonBootMeasure(pBody);
    }
    else if (strcmp(pInterfaceName, "linux_node_trusted_report_upload") == 0)
    {
        //鍙俊鎶ュ憡
    }
    else if (strcmp(pInterfaceName, "linux_node_tpcmStatus_tcmStatus_upload") == 0)
    {
        //鍙俊鏍圭姸鎬�
    }	

    return 0;
}


/*
/platform/reportBootMeasureLog
/platform/reportBasesLog
/platform/reportLinuxLog
/platform/linux_node_trusted_report_upload
/platform/linux_node_trusted_bootmeasure
/platform/linux_node_trusted_pubKey
/platform/linux_node_trusted_report_upload
/platform/linux_node_tpcmStatus_tcmStatus_upload
/platform/confirm_Mac_Policy
/platform/confirm_DMeasure_Policy
/platform/confirm_Audit_Policy
/platform/confirm_TpcmSwitch_Policy
/platform/confirm_WhiteList_Policy
/platform/conform_linuxNetControl_policy
/platform/modAction/uploadModuleInstallInfo
/platform/upload_tpcm_primary_key
鍙俊鏈嶅姟post瀛楁涓彲鑳戒娇鐢ㄧ殑璺緞
*/
int ClienMessageCompare(char *recv_buf)
{
	char path1[64] = {"POST /platform/report\0"};
	char path2[64] = {"POST /platform/linux_node_t\0"};
	char path3[64] = {"POST /platform/confirm_\0"};
	char path4[64] = {"POST /platform/conform_\0"};
	char path5[64] = {"POST /platform/modAction\0"};
	char path6[64] = {"POST /platform/upload_tpcm_primary_key\0"};
	//printf("len1=%d len1=%d len1=%d len1=%d len1=%d len1=%d\n", strlen(path1), strlen(path2), strlen(path3), strlen(path4), strlen(path5), strlen(path6));

	if(  memcmp(path1,recv_buf,strlen(path1)) && memcmp(path2,recv_buf,strlen(path2)) &&
		 memcmp(path3,recv_buf,strlen(path3)) && memcmp(path4,recv_buf,strlen(path4)) &&
		 memcmp(path5,recv_buf,strlen(path5)) && memcmp(path6,recv_buf,strlen(path6))
	  )
	{
		//printf("ClienMessageCompare XXXXXXXXXXXXXXXXXXXXXXXXXXXXX!\n");
		return -1;
	}
	else
	{
		//printf("ClienMessageCompare is ok!\n");
		return 0;
	}
}

/************************************************************************
 * Function Name : int HttcSocket_ServerThread(void* param)
 * Description   : httc log recv thread
 * Parameters    : void* param
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
static void* HttcSocket_ServerThread(void* param)
{
    char buf[1024*512] = {0};
    int r = 0;
    int sock = socket(AF_INET, SOCK_STREAM, 0);
    char res[256] = {0};
    if (sock < 0)
    {
        printf("socket error\n");
    }

    int opt = 1;
    setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, (const void *)&opt, sizeof(opt));

    struct sockaddr_in server_socket;
    bzero(&server_socket, sizeof(server_socket));
    server_socket.sin_family = AF_INET;
    server_socket.sin_addr.s_addr = htonl(INADDR_ANY);
    server_socket.sin_port = htons(8080);

    if (bind(sock, (struct sockaddr*) &server_socket, sizeof(struct sockaddr_in)) < 0)
    {
        printf("bind() error\n");
        close(sock);
        return (void*)-1;
    }
    if (listen(sock, _BACKLOG_) < 0)
    {
        printf("listen() error\n");
        close(sock);
        return (void*)-1;
    }

    printf("----------- new tsb --------\n");
    sprintf(res, "%s%s%s%s%s","HTTP/1.1 200 OK\r\n",
            "X-Frame-Options: SAMEORIGIN\r\n",
            "X-XSS-Protection: 1; mode=block\r\n",
            "X-Content-Type-Options: nosniff\r\n",
            "Strict-Transport-Security: max-age=16070400\r\n\r\n"
           );

    HttcServerStart();

    while (1)
    {
        socklen_t len = sizeof(struct sockaddr_in);
        struct sockaddr_in addr_cli;

        int client_sock = accept(sock, (struct sockaddr*)&addr_cli, &len);
        if (client_sock < 0)
        {
            printf("accept() error\n");
            return (void*)-1;
        }

        memset(buf, 0, sizeof(buf));
        r = recv(client_sock, buf, sizeof(buf), 0);
        if(r <= 0)
        {
            printf("recv error %d", r);
            close(client_sock);
            continue;
        }
        //printf("read  from client : %s\n", buf);

        if(0 == ClienMessageCompare(buf))
		{
			HttcParseLog(buf);
			send(client_sock, res, strlen(res), 0);
		}
        close(client_sock);
    }

    close(sock);
    pi_msleep(100);
    return NULL;
}

/************************************************************************
 * Function Name : int Httc_Socket_prolog(void)
 * Description   : httc_server_socket
 * Parameters    : void
 *				  
 * Return        : void
 * Data	      	 : 2023 - 01 - 14
 * By	         : ZhangJie
 * Explain       : None
 *************************************************************************/
int Httc_Socket_prolog(void)
{
    char tpcm_ver[32] = {0};

    // create a thread to listen httc_socket
    g_httc_socket_taskID = pi_thread_create( HttcSocket_ServerThread,
            1,
            NULL,
            0,
            NULL,
            "httc_server_socket" );
    if (INVALIDTHREAD == g_httc_socket_taskID)
    {
        pi_log_e("httc_server_socket - can't start thread\n");
        return (-1);
    }

    HttcGetVersion(tpcm_ver);

    return 0;
}

/************************************************************************
 * Function Name : int HttcGetLogNumber(HTTC_LOG_TYPE log_type)
 * Description   : get httc log numbers
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
int HttcGetLogNumber(HTTC_LOG_TYPE log_type)
{
    FILE  *fp = NULL;
    uint32_t file_size = 0;
    uint32_t log_len = sizeof(struct log_msg_st);

    char *path = NULL;

    if(HTTC_SUCCESS_LOG == log_type)
        path = HTTC_SUCESS_LOG_FILE_PATH;
    else
        path = HTTC_FAIL_LOG_FILE_PATH;

    fp = fopen(path, "r");
    if (NULL == fp)
    {
        pi_log_e("fopen %s error.\n", path);
        return 0;
    }

    fseek(fp, 0, SEEK_END);
    file_size = ftell(fp);
    file_size = file_size - 4;
    fclose(fp);
    fp = NULL;

    return (file_size/log_len);
}

/************************************************************************
 * Function Name : int HttcExportLog
 * Description   : export httc audit log
 * Parameters    : HTTC_LOG_TYPE  log_type (1.HTTC_SUCCESS_LOG   2.HTTC_FAIL_LOG)
 *				   file path
 * Return        : void
 * Data	      	: 2013 - 09 - 26
 * By	        : ZhangJie
 * Explain       : None
 *************************************************************************/
int httc_export_logfile(HTTC_LOG_TYPE log_type, const char *file_path)
{
    FILE  *fp_src = NULL;
    FILE  *fp_dst = NULL;
    int    i,j;
    char   log_buff[HTTC_LOG_LINE_WIDTH];

    uint32_t src_index;
    struct log_msg_st log;
    char   str_buff[64];
    //uint32_t log_buff_index;

    //int log_num_max;
    char *src_path = NULL;
    char *des_path = NULL;
    const char* LogOperate[9] = {"W","R","A","D","M","O","C","B","W|A"};
    const char* RLogOperate[9] = {"write","read","add","delete","measure","mount","create","rename","writ add"};
    //const char* LogType[4] = {"whitelist_log^^^","fileaccess_log^^^", "no type^^^","dmeasure_log^^^"};

    if(HTTC_SUCCESS_LOG == log_type)
    {
        src_path = HTTC_SUCESS_LOG_FILE_PATH;
    }
    else
    {
        src_path = HTTC_FAIL_LOG_FILE_PATH;
    }


    fp_src = fopen(src_path, "r");
    fp_dst = fopen(file_path, "w");
    if (NULL == fp_dst)
    {
        pi_log_e("fopen %s pi_log_e.\n", des_path);
        if (fp_src) fclose(fp_src);
        return (-5);
    }

    if (NULL == fp_src)
    {
        pi_log_e("fopen %s pi_log_e.\n", src_path);

        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        snprintf((char *)log_buff, HTTC_LOG_LINE_WIDTH, 
                "      no data^^^\n");
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
        fclose(fp_dst);
        fp_dst = NULL;
        return -1;
    }

    for (i = 0; i <= HttcGetLogNumber(log_type) - 1; i++)
    {

        // 璇诲彇鏃ュ織鏂囦欢
        src_index = i *sizeof(struct log_msg_st) + sizeof(uint32_t);
        fseek(fp_src, src_index, SEEK_SET);
        fread((void *)&log, sizeof(struct log_msg_st), 1, fp_src);

        //log_buff_len   = HTTC_LOG_LINE_WIDTH;
        //log_buff_index = 0;
        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        // index
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^", i+1);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // time
        {	
            strncat(log_buff,(char *)&log.time,strlen((char *)log.time));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // type and operate
        {
            switch (log.type) 
            {
                case TYPE_WHITELIST:
                    strncat(log_buff,"WHITELIST^^^",strlen("WHITELIST^^^"));
                    strncat(log_buff,"EXEC^^^",strlen("EXEC^^^"));
                    break;

                case TYPE_FILE_ACCESS:
                    strncat(log_buff,"FILE_ACCESS^^^",strlen("FILE_ACCESS^^^"));
                    for(j = 0;j<9;j++)
                    {
                        if(0 == strncmp(LogOperate[j],(char *)&log.operate,strlen((char *)&log.operate)))
                        {
                            strncat(log_buff,RLogOperate[j],strlen(RLogOperate[j]));
                            strncat(log_buff,"^^^",strlen("^^^"));
                            break;
                        }
                    }
                    break;

                case TYPE_DMEASURE:
                    strncat(log_buff,"DMEASURE^^^",strlen("DMEASURE^^^"));
                    strncat(log_buff,"PERIODICITY_DMEASURE^^^",strlen("PERIODICITY_DMEASURE^^^"));
                    break;

                case TYPE_BMEASURE:
                    strncat(log_buff,"BMEASURE^^^",strlen("BMEASURE^^^"));
                    strncat(log_buff,"BOOT_MEASURE^^^",strlen("BOOT_MEASURE^^^"));
                    break;
            }
        }
        // userid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4u^^^",log.userid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // pid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4u^^^",log.pid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // hash
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));

            memcpy(str_buff, log.hash, HASH_LEN);

            strncat(log_buff,str_buff,strlen(str_buff));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // subject
        {	
            strncat(log_buff,(char *)&log.subject,strlen((char *)log.subject));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // object
        {	
            strncat(log_buff,(char *)&log.object,strlen((char *)log.object));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // result
        {
            if (log.type == TYPE_BMEASURE) 
            {
                if(0 == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
            else
            {
                if(RESULT_SUCCESS == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
        }
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }

        //Log(5, "%s", log_buff);
        // 鍐欏叆鏂囨湰鏂囦欢
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
    }

    fclose(fp_src);
    fp_src = NULL;
    fclose(fp_dst);
    fp_dst = NULL;
    return 0;    
}


/************************************************************************
 * Function Name : int HttcExportLog
 * Description   : export httc audit log
 * Parameters    : HTTC_LOG_TYPE log_type
 *				  unsigned int  export_start  	index of start secure log
 *				  unsigned int  export_end		index of end secure log
 * Return        : void
 * Data	      	: 2019 - 01 - 14
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
    int HttcExportLog
(
 HTTC_LOG_TYPE log_type,
 unsigned int  export_start,
 unsigned int  export_end
 )
{
    FILE  *fp_src = NULL;
    FILE  *fp_dst = NULL;
    int    i,j;
    char   log_buff[HTTC_LOG_LINE_WIDTH];

    uint32_t src_index;
    struct log_msg_st log;
    char   str_buff[64];
    /*uint32_t log_buff_len, log_buff_index;*/

    int log_num_max;
    char *src_path = NULL;
    char *des_path = NULL;
    const char* LogOperate[9] = {"W","R","A","D","M","O","C","B","W|A"};
    const char* RLogOperate[9] = {"write","read","add","delete","measure","mount","create","rename","writ add"};
    /*const char* LogType[4] = {"whitelist_log^^^","fileaccess_log^^^", "no type^^^","dmeasure_log^^^"};*/

    if(HTTC_SUCCESS_LOG == log_type)
    {
        src_path = HTTC_SUCESS_LOG_FILE_PATH;
        des_path = HTTC_WEB_SUCESS_LOG_PATH;
        log_num_max = HTTC_SUCESS_LOG_MAX_NUMBER;
    }
    else
    {
        src_path = HTTC_FAIL_LOG_FILE_PATH;
        des_path = HTTC_WEB_FAIL_LOG_PATH;
        log_num_max = HTTC_FAIL_LOG_MAX_NUMBER;
    }


    if ((log_num_max <= export_end)
            ||(log_num_max <= export_start)
            ||(export_start > export_end))
    {
        pi_log_e("invalid export_end or export_start\n");
        return (-2);
    }

    if(export_end >= HttcGetLogNumber(log_type))
    {
        export_end = HttcGetLogNumber(log_type) - 1;
    }

    fp_src = fopen(src_path, "r");
    fp_dst = fopen(des_path, "w");
    if (NULL == fp_dst)
    {
        pi_log_e("fopen %s error.\n", des_path);
        if (fp_src) fclose(fp_src);
        return (-5);
    }

    if (NULL == fp_src)
    {
        pi_log_e("fopen %s error.\n", src_path);

        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        snprintf((char *)log_buff, HTTC_LOG_LINE_WIDTH, 
                "      no data^^^\n");
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
        fclose(fp_dst);
        fp_dst = NULL;
        return -1;
    }

    for (i = export_start; i <= export_end; i++)
    {

        // 璇诲彇鏃ュ織鏂囦欢
        src_index = i *sizeof(struct log_msg_st) + sizeof(uint32_t);
        fseek(fp_src, src_index, SEEK_SET);
        fread((void *)&log, sizeof(struct log_msg_st), 1, fp_src);

        // 转锟斤拷为锟侥憋拷
        /*log_buff_len   = HTTC_LOG_LINE_WIDTH;*/
        /*log_buff_index = 0;*/
        memset((void *)log_buff, 0x0, HTTC_LOG_LINE_WIDTH);
        // index
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^", i+1);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // time
        {	
            strncat(log_buff,(char *)&log.time,strlen((char *)log.time));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // type and operate
        {
            switch (log.type) 
            {
                case TYPE_WHITELIST:
                    strncat(log_buff,"WHITELIST^^^",strlen("WHITELIST^^^"));
                    strncat(log_buff,"EXEC^^^",strlen("EXEC^^^"));
                    break;

                case TYPE_FILE_ACCESS:
                    strncat(log_buff,"FILE_ACCESS^^^",strlen("FILE_ACCESS^^^"));
                    for(j = 0;j<9;j++)
                    {
                        if(0 == strncmp(LogOperate[j],(char *)&log.operate,strlen((char *)&log.operate)))
                        {
                            strncat(log_buff,RLogOperate[j],strlen(RLogOperate[j]));
                            strncat(log_buff,"^^^",strlen("^^^"));
                            break;
                        }
                    }
                    break;

                case TYPE_DMEASURE:
                    strncat(log_buff,"DMEASURE^^^",strlen("DMEASURE^^^"));
                    strncat(log_buff,"PERIODICITY_DMEASURE^^^",strlen("PERIODICITY_DMEASURE^^^"));
                    break;

                case TYPE_BMEASURE:
                    strncat(log_buff,"BMEASURE^^^",strlen("BMEASURE^^^"));
                    strncat(log_buff,"BOOT_MEASURE^^^",strlen("BOOT_MEASURE^^^"));
                    break;
            }
        }
        // userid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^",log.userid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // pid
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));
            snprintf((char *)(str_buff), sizeof(str_buff)-1, "%4d^^^",log.pid);
            strncat(log_buff,str_buff,strlen(str_buff));
        }
        // hash
        {
            memset((void *)str_buff, 0x0, sizeof(str_buff));

            memcpy(str_buff, log.hash, HASH_LEN);

            strncat(log_buff,str_buff,strlen(str_buff));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // subject
        {	
            strncat(log_buff,(char *)&log.subject,strlen((char *)log.subject));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // object
        {	
            strncat(log_buff,(char *)&log.object,strlen((char *)log.object));
            strncat(log_buff,"^^^",strlen("^^^"));
        }
        // result
        {
            if (log.type == TYPE_BMEASURE) 
            {
                if(0 == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
            else
            {
                if(RESULT_SUCCESS == log.result) 
                {
                    strncat(log_buff,"SUCCESS^^^\n",strlen("SUCCESS^^^\n"));
                }
                else
                {
                    strncat(log_buff,"FAIL^^^\n",strlen("FAIL^^^\n"));
                }
            }
        }
        if ('\0' != log_buff[HTTC_LOG_LINE_WIDTH-1])
        {
            log_buff[HTTC_LOG_LINE_WIDTH-2] = '\n';
            log_buff[HTTC_LOG_LINE_WIDTH-1] = '\0';
        }

        //Log(5, "%s", log_buff);
        // 鍐欏叆鏂囨湰鏂囦欢
        fwrite((const void*)log_buff, strlen(log_buff), 1, fp_dst);
    }

    fclose(fp_src);
    fp_src = NULL;
    fclose(fp_dst);
    fp_dst = NULL;
    return 0;    
}

/************************************************************************
 * Function Name : int HttcGetVersion
 * Description   : export httc audit log
 * Parameters    : char * _tpcm_ver tpcm version point
 *				  
 * Return        : void
 * Data	      	: 2021 - 04 - 01
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
int HttcGetVersion(char * _tpcm_ver)
{
    char tpcm[32] = {0};
    char tsb[32] = {0};

    if (_tpcm_ver == NULL) {
        pi_log_e("param error\n");
        return -1;
    }

    HTTC_GetVersion(tpcm, 32, tsb, 32);

    memcpy(_tpcm_ver, tpcm, 32);

    pi_log_i("TPCM(%s) TSB(%s)\n", tpcm, tsb);

    return 0;
}

/***
 * Description: check httc service start result 
 * 
 * 
 * *
 * */




/*static HTTC_START_STATUS httc_start_status=ENUM_HTTC_NOT_START;*/


#define DMEASURE_SUCCESS_STR    "start Dmeasure service success!!!"
#define FAC_SUCCESS_STR         "start Fac service success!!!"
#define SMEASURE_SUCCESS_STR    "start Smeasure service success!!!"
#define HTTC_CLEARFLAG_FILE     "/usr/local/httcsec/httc_clearflag"
#define HTTC_RECORD_FILE        "/settings/httc_record"
#define HTTC_STATUE_FILE        "/tmp/httc_statue"

//-1 缁х画鏌ヨ鍙俊鐘舵€侊紙鍙俊鍚姩鏈畬鎴愶級
//0 鍙俊鍚姩鎴愬姛
//1 鍙俊鍚姩澶辫触
int get_httc_status()
{	
    int fd = 0;
    static int statue = -1;
    if(statue == 0)
        return 0;
	if(!access(HTTC_STATUE_FILE, F_OK))
	{
		fd = open(HTTC_STATUE_FILE, O_RDONLY);
		if(fd < 0)
		{
			printf("open /tmp/httc_statue error!\n");
            close(fd);
			return -1;
		}
		int ret = read(fd, &statue, sizeof(statue));
		if(ret <= 0)
		{
			printf("read statue error!\n");
            close(fd);
			return -1;
		}
		if(statue != 0)
		{
		    printf("get_httc_status : %d\n", statue);
		}	
        close(fd);
		return statue;
	}
	else
	{
		return -1;
	}
}

static int read_httc_record_file(int* run_cnt,int *fail_cnt)
{
    char linestring[32]={};
    FILE *fp = fopen(HTTC_RECORD_FILE,"r");
    int ret;
    char *fret = NULL;
    if(!fp)
    {
        pi_log_e("%s open fail :%s\n",HTTC_RECORD_FILE,strerror(errno));        
        return -1;
    }
    fret = fgets(linestring, sizeof(linestring),fp);
    if(fret==NULL&&!feof(fp)){
        pi_log_e("fgets %s 1 error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return  -1;
    }  
    ret = sscanf(linestring,"run_cnt=%d",run_cnt); 
    if(ret == 0 ||ret == EOF)
    {
        fclose(fp);    
        return -1;
    }

    printf("read success cnt:%d\n",*run_cnt);
    fret = fgets(linestring, sizeof(linestring),fp);
    if(fret==NULL&&!feof(fp)){
        pi_log_e("fgets %s 2line error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return  -1;
    }  
    ret = sscanf(linestring,"fail_cnt=%d",fail_cnt); 
    if(ret == 0 ||ret == EOF)
    {
        fclose(fp);    
        return -1;
    }

    printf("read fail cnt:%d\n",*fail_cnt);
    fclose(fp);
    return 0;
} 

static int write_httc_record_file(int run_cnt,int fail_cnt)
{
    FILE *fp = fopen(HTTC_RECORD_FILE,"w+");
    char linestring[32] = {0};
    if(!fp)

    {
        pi_log_e("%s open fail :%s\n",HTTC_RECORD_FILE,strerror(errno));        
        return -1;
    }
    sprintf(linestring,"run_cnt=%d\n",run_cnt);
    if(fputs(linestring,fp)==EOF){
        pi_log_e("fwrite %s error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return -1;
    }        
    sprintf(linestring,"fail_cnt=%d\n",fail_cnt);
    if(fputs(linestring,fp)==EOF){
        pi_log_e("fwrite %s error\n",HTTC_RECORD_FILE);
        fclose(fp);
        return -1;
    }        
    fclose(fp);
    return 0;
}

#if 0
static void record_httc_fail_cnt()
{
    int run_cnt = 0;
    int fail_cnt = 0;

    if(!read_httc_record_file(&run_cnt,&fail_cnt))
        write_httc_record_file(run_cnt,fail_cnt+1);
    else
        write_httc_record_file(1,1);

}
#endif


static void record_httc_run_cnt()
{
    int run_cnt = 0;
    int fail_cnt = 0;

    if(!read_httc_record_file(&run_cnt,&fail_cnt))
        write_httc_record_file(run_cnt+1,fail_cnt);
    else
        write_httc_record_file(1,0);

}



/************************************************************************
 * Function Name : void HttcServerStart(void)
 * Description   : HttcServerStart
 * Parameters    : void
 *				  
 * Return        : void
 * Data	      	: 2021 - 04 - 02
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
#define HTTC_CONFIG_PATH   /settings/config_httc
void HttcServerStart(void)
{
#if CONFIG_HTTC 
    system("echo CONFIG_HTTC=1 > /settings/config_httc");
    record_httc_run_cnt();
    pi_runcmd(NULL, 0, 1, "/root/httc_start.sh");
#else
    remove(HTTC_CONFIG_PATH);
#endif	
	
}

/************************************************************************
 * Function Name : void HttcServerStop(void)
 * Description   : HttcServerStop
 * Parameters    : void
 *				  
 * Return        : void
 * Data	      	: 2021 - 05 - 10
 * By	        : RoonZhang
 * Explain       : None
 *************************************************************************/
void HttcServerStop(void)
{
    RN_RUN_CMD_BG("/usr/local/httcsec/Base/srv stop");
    printf("RN_RUN_CMD_BG(/usr/local/httcsec/Base/srv stop)\n");
}


/* ************************************* (c) Roon ** End File ******************************************* */
    sync();
    return 0;
}

// 处理JSON数据并生成最终的JSON字符串
static char* pedk_process_app_json_data(PEDK_APP_CONFIG_S *app_config, char *app_json_data, size_t data_len, bool is_official)
{
    cJSON *root = NULL;
    char *json_str = NULL;

    // 参数验证
    if (app_config == NULL || app_json_data == NULL) {
        pi_log_e("Invalid parameters: app_config or app_json_data is NULL\n");
        return NULL;
    }

    root = parse_read_file(PEDK_DOWNLOAD_APP_JSON);
    if (root == NULL) {
        fprintf(stderr, "error parsing JSON.\n");
        return NULL;
    }

    const char *key_to_remove[] = {"main","description","author","license"};
    pedk_download_remove_json_keys(root, key_to_remove, sizeof(key_to_remove)/sizeof(key_to_remove[0]));

    // 添加官方包标识字段到JSON数据中
    cJSON_AddStringToObject(root, "is_official", is_official ? "true" : "false");
    pi_log_d("Added is_official field: %s\n", is_official ? "true" : "false");

    json_str = cJSON_PrintUnformatted(root);
    pi_log_d("app.json data -> net %s\n", json_str);

    if(data_len > strlen(json_str)) {
        snprintf(app_json_data, strlen(json_str), "%s", json_str);
    } else {
        pi_log_d("over buff len,error\n");
        free(json_str);
        cJSON_Delete(root);
        return NULL;
    }

    cJSON_Delete(root);
    return json_str;
}

// 移动文件到目标目录
static void pedk_move_files_to_target_dir(PEDK_APP_CONFIG_S *app_config)
{
    if (app_config == NULL || app_config->bundleName == NULL) {
        pi_log_e("Cannot move files: app_config or bundleName is NULL\n");
        return;
    }

    pi_runcmd(NULL, 0, 0, "ls %s", PEDK_ROOT_DIR_PATH);
    pi_runcmd(NULL, 0, 0, "ls %s", PEDK_ROOT_DIR_PATH_TMP);

    //将本次解压的文件放到所属的文件夹中
    pi_runcmd(NULL, 0, 0, "cd %s; find . -maxdepth 1 -type f | grep -v 'app_list.cfg' | xargs -I {} mv {} %s%s; mv resources %s%s; sync",
                                                    PEDK_ROOT_DIR_PATH_TMP,
                                                    PEDK_ROOT_DIR_PATH, app_config->bundleName,
                                                    PEDK_ROOT_DIR_PATH, app_config->bundleName);
}

// 写入app_info.json文件到本地
static int pedk_write_local_app_info(PEDK_APP_CONFIG_S *app_config, const char *json_str)
{
    char app_path[PEDK_MAX_PATH_LEN] = {0};
    FILE* app_json_fp = NULL;

    if (app_config == NULL || app_config->bundleName == NULL) {
        pi_log_e("Cannot write app info: app_config or bundleName is NULL\n");
        return -1;
    }

    snprintf(app_path, sizeof(app_path), "%s%s/app_info.json", PEDK_ROOT_DIR_PATH, app_config->bundleName);
    pi_log_d(" ssapp_path %s\n", app_path);

    app_json_fp = fopen(app_path, "w");
    if(!app_json_fp) {
        pi_log_d(" open file error\n");
        return -1;
    }

    fputs(json_str, app_json_fp);
    fclose(app_json_fp);
    return 0;
}

// 传输app_info.json到panel进程
static int pedk_transfer_app_info_to_panel(const char *json_str)
{
    int32_t app_info_fd = -1;
    int32_t err_num = 0;
    size_t json_len = strlen(json_str);
    ssize_t bytes_written;

    pi_log_d("Starting app_info.json transfer to panel: %s\n", PANEL_APP_INFO_PATH);

    // 打开panel中的app_info.json文件
    app_info_fd = pedk_open_panel_file(PANEL_APP_INFO_PATH, O_WRONLY | O_CREAT | O_TRUNC, &err_num);
    if (app_info_fd < 0) {
        return -1;
    }

    // 写入JSON字符串到panel
    bytes_written = mfp_to_panel_file_write(app_info_fd, json_str, json_len, &err_num);
    if (bytes_written != (ssize_t)json_len) {
        pi_log_e("Failed to write app_info.json to panel, expected: %zu, actual: %zd, err: %d\n",
                 json_len, bytes_written, err_num);
        pedk_close_panel_file(app_info_fd, "app_info.json");
        return -1;
    }

    // 关闭panel文件
    if (pedk_close_panel_file(app_info_fd, "app_info.json") != 0) {
        return -1;
    }

    pi_log_d("app_info.json transfer to panel completed successfully\n");
    return 0;
}

// 传输app_list.cfg到panel进程
static int pedk_transfer_app_list_to_panel(void)
{
    int32_t src_fd = -1;
    int32_t dst_fd = -1;
    int32_t err_num = 0;
    char buffer[PEDK_TRANSFER_CHUNK_SIZE];
    ssize_t bytes_read, bytes_written;
    int ret = -1;

    pi_log_d("Starting app_list.cfg transfer to panel: %s\n", PANEL_APP_INFO_PATH);

    // 打开源文件 (DC端的app_list.cfg)
    src_fd = open(PEDK_APP_LIST_FILE, O_RDONLY);
    if (src_fd < 0) {
        pi_log_e("Failed to open source app_list.cfg: %s\n", PEDK_APP_LIST_FILE);
        return -1;
    }

    // 打开目标文件 (Panel端的app_list.cfg)
    dst_fd = pedk_open_panel_file(PANEL_APP_INFO_PATH, O_WRONLY | O_CREAT | O_TRUNC, &err_num);
    if (dst_fd < 0) {
        pi_log_e("Failed to open panel app_list.cfg: %s\n", PANEL_APP_INFO_PATH);
        close(src_fd);
        return -1;
    }

    // 分块传输文件内容
    while ((bytes_read = read(src_fd, buffer, sizeof(buffer))) > 0) {
        bytes_written = mfp_to_panel_file_write(dst_fd, buffer, bytes_read, &err_num);
        if (bytes_written != bytes_read) {
            pi_log_e("Failed to write app_list.cfg to panel, expected: %zd, actual: %zd, err: %d\n",
                     bytes_read, bytes_written, err_num);
            goto cleanup;
        }
    }

    if (bytes_read < 0) {
        pi_log_e("Failed to read from source app_list.cfg\n");
        goto cleanup;
    }

    pi_log_d("app_list.cfg transfer to panel complete successfully\n");
    ret = 0;

cleanup:
    if (src_fd >= 0) {
        close(src_fd);
    }
    if (dst_fd >= 0) {
        pedk_close_panel_file(dst_fd, "app_list.cfg");
    }

    return ret;
}

//安装app接口
int pedk_download_app_install (char* app_name, char *app_json_data, size_t data_len, char *auto_boot)
{
    int ret = 0;
    PEDK_APP_CONFIG_S *app_config = NULL;
    bool is_official_app = false; // Track official status
    int check_ret = 0;

    // 参数验证
    if(app_name == NULL) {
        pi_log_d("error: file name is NULL\n");
        ret = -1;
        goto FAILURE;
    }

    // 文件检查和签名验证
    check_ret = pedk_download_file_check(app_name, auto_boot);
    if(check_ret < 0) {
        pi_log_d(" path check failed,return\n");
        ret = -1;
        goto FAILURE;
    }

    // Determine official status based on return value (1 means official)
    is_official_app = (check_ret == 1);
    pi_log_d("App installation check completed. Official status: %s\n", is_official_app ? "true" : "false");

    // 解析JSON文件
    app_config = parse_json_file(PEDK_DOWNLOAD_APP_JSON);
    if (app_config == NULL) {
        fprintf(stderr, "error parsing JSON file\n");
        ret = -1;
        goto FAILURE;
    }
    pedk_download_print_appconfig(app_config);

    // 添加app数据到文件
    if(pedk_download_add_app_json_data(app_config, auto_boot, is_official_app)) {
        pi_log_d("add_app_json_data to file failed,return\n");
        ret = -1;
        goto FAILURE;
    }

    // 解密key和app.js
    if(pedk_download_rsa_decrypt_file(PEDK_DOWNLOAD_KEY_ENC, PEDK_DOWNLOAD_RSA_PRIVATE_KEY, PEDK_DOWNLOAD_KEY_LOCAL) ||
       pedk_download_aes_decrypt_file(PEDK_DOWNLOAD_APP_JS_ENC, PEDK_DOWNLOAD_KEY_LOCAL, PEDK_DOWNLOAD_APP_JS)) {
        pi_log_d("pedk_download_aes_decrypt_file failed\n");
        ret = -1;
        goto FAILURE;
    }

    pi_log_d("Using direct app_list.cfg transfer instead of individual JSON processing\n");

    // 移动文件到目标目录
    pedk_move_files_to_target_dir(app_config);

    // 写入本地app_info.json文件 (保留本地文件，但Panel端使用app_list.cfg)
    // 为本地app_info.json生成简化的JSON数据
    cJSON *local_json = parse_read_file(PEDK_DOWNLOAD_APP_JSON);
    if (local_json != NULL) {
        // 添加官方包标识到本地JSON
        cJSON_AddStringToObject(local_json, "is_official", is_official_app ? "true" : "false");

        char *local_json_str = cJSON_PrintUnformatted(local_json);
        if (local_json_str != NULL) {
            if (pedk_write_local_app_info(app_config, local_json_str) != 0) {
                pi_log_e("Failed to write local app_info.json\n");
            }
            free(local_json_str);
        }
        cJSON_Delete(local_json);
    }

    // 传输app_list.cfg到panel进程
    if (pedk_transfer_app_list_to_panel() != 0) {
        pi_log_e("Failed to transfer app_list.cfg to panel\n");
        ret = -1;
        goto FAILURE;
    }
    pi_log_d("app_list.cfg transfer to panel completed successfully\n");

    // 跨进程传输tar包
    if (app_config && app_config->bundleName) {
        pi_log_d("Starting cross-process transfer for app: %s\n", app_config->bundleName);
        if (pedk_create_and_transfer_app_package(app_config->bundleName) != 0) {
            pi_log_e("Failed to transfer app package to panel for: %s\n", app_config->bundleName);
            ret = -1;
        } else {
            pi_log_d("Cross-process transfer completed successfully for: %s\n", app_config->bundleName);
        }
    } else {
        pi_log_e("Cannot transfer app package: app_config or bundleName is NULL\n");
        ret = -1;
    }

FAILURE:
    // 清理临时目录
    if(access(PEDK_ROOT_DIR_PATH_TMP, F_OK) == 0) {
        pi_runcmd(NULL, 0, 0, "rm -rf %s; sync", PEDK_ROOT_DIR_PATH_TMP);
    }

    // 通知panel - 安全检查app_config是否为NULL
    pedk_download_send_msg2panel(MSG_PEDK_APP_INSTALL, ret,
                                (app_config && app_config->bundleName) ? app_config->bundleName : "unknown");

    // 释放资源
    free_app_config(app_config);

    return ret;
}

int pedk_download_app_uninstall(char* bundleName)
{
    int ret = 0;

    pi_runcmd(NULL, 0, 0, "cd %s; find . -type d -name '%s' -exec rm -rf {} +; sync",PEDK_ROOT_DIR_PATH, bundleName);

    if(pedk_download_applist_delete_data(bundleName) != 0)
    {
        pi_log_d(" applist_delete_data error\n");
        ret = -1;
    }

    //pedk_download_send_msg2panel(EVT_TYPE_PEDKMGR_APP_UNINSTALL_MODIFY, ret, NULL);

    return ret;
}

