#include "PEDK_usbd.h"
#include "PEDK_event.h"

#include <quickjs.h>

#define countof(x) (sizeof(x) / sizeof((x)[0]))
#define Log(format, ...) printf("[PEDK_usbd] [%d->%s] "format, __LINE__, __func__, ##__VA_ARGS__);
/* 
    定义 QuickJS C 函数 
    *ctx     : 运行时上下文
    this_val : this对象
    argc     : 入参个数
    *argv    : 入参列表
*/
JSValue js_set_usb_device_enable(JSContext *ctx, JSValueConst this_val,
                               int argc, JSValueConst *argv)
{
    int status = 0;
    int fd = 0;
    JS_ToInt32(ctx, &fd, argv[0]);
    JS_ToInt32(ctx, &status, argv[1]);
    Log("%s : %d %d %d\n", __func__, argc,fd,status);
    SendMsgToMfp(MSG_MODULE_USBD, MSG_USB_DEVICE_ENABLE, 1, sizeof(status),  (const unsigned char *)&status);
    return JS_NewString(ctx, "OK");
}

typedef struct JSCFunctionList
{
    const char *name;
    int length;
    JSCFunction *func;
}JSCFunctionList;

/* 定义API的函数入口名称及列表 */
static const JSCFunctionList PEDK_usbd_funcs[] = {
    /* 函数入口名称，入参个数，QuickJS C 函数 */
    {"js_setusbdeviceenable", 2, js_set_usb_device_enable},

};
///
const JSCFunctionList* get_usbd_JSCFunctionList(int *length) {
	*length = countof(PEDK_usbd_funcs);
	return PEDK_usbd_funcs;
}

int js_usbd_init(JSContext *ctx, JSValueConst global)
{
    int i = 0;

    Log("*********start usbd module*******\n");
    /* creat the classes */
    int count = 0;
    const JSCFunctionList* PEDK_funcs = get_usbd_JSCFunctionList(&count);
    Log("count:%d\n",count);
    for(i = 0; i < count; i++) {
        JS_SetPropertyStr(ctx, global, PEDK_funcs[i].name,
                            JS_NewCFunction(ctx, PEDK_funcs[i].func, PEDK_funcs[i].name, PEDK_funcs[i].length));

    }
    Log("*********start usbd init end*******\n");
    return 0;
}


