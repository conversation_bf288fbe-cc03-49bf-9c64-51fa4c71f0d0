/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file qio_tiff.c
 * @addtogroup qio
 * @{
 * <AUTHOR>
 * @date 2023-04-17
 * @brief jpeg to tiff moudle
 */
#include "qiox.h"
#include "tiff.h"

#define TIFF_MAX_PAGES                          1
#define TIFF_MAX_CHUNK                          0x10000 //QIO write Maximum number of bytes at a time is 64K

#define TIFF_COLOR_TAG_COUNT                    22      //Tiff TAG number is 22
#define TIFF_HEADER_CONTENT_LEN                 8       //Tiff File header content bytes
#define TIFF_TAG_CONTENT_LEN                    12      //Tiff tag content bytes
#define TIFF_TAG_COUNT_LEN                      2       //Tiff tag quantity marker bytes
#define TIFF_TAG_END_LEN                        4       //Tiff tag end flag byte

#define TIFF_SOFTWARE                           "Pixel Translations"

#define TIFF_JPEG_IMAGE_LEN_FIRST_OFFSET_ADDR   0x00000096
#define TIFF_JPEG_IMAGE_LEN_SECOND_OFFSET_ADDR  0x000000F6

#define FMT_COLOR(f)                            ((f) & FMT_COLORMASK)

typedef struct private_info_tiff
{
    QIO_S*  pqio;       ///< Actual output stream qio
    int32_t ownqio;     ///< this qio owns dest qio (to close it at close)
    int32_t w;          ///< current image dimensions of w
    int32_t h;          ///< current image dimensions of h
    int32_t d;          ///< current image dimensions of depth
    int32_t s;          ///< current image dimensions of size
    int32_t f;          ///< current image dimensions of format
    int32_t xres;       ///< the resolution of the image, in DPI, where applicable
    int32_t yres;       ///< the resolution of the image, in DPI, where applicable
    int32_t clipX;      ///< max dimensions of output w
    int32_t clipY;      ///< max dimensions of output h
    int32_t tos;        ///< timeout, seconds, for writing qio
    int32_t tous;       ///< timeout, microseconds
    size_t  offset;     ///< bytes written to output stream
    size_t  imgstart;   ///< offset where current image starts
}
PRIV_INFO_S;

static int32_t write_tiff_two_bytes(PRIV_INFO_S* priv, uint16_t val)
{
    uint8_t buffer[2];

    buffer[0] = (uint8_t)((val)      & 0xFF);
    buffer[1] = (uint8_t)((val >> 8) & 0xFF);

    return QIO_WRITE(priv->pqio, buffer, 2);
}

static int32_t write_tiff_four_bytes(PRIV_INFO_S* priv, uint32_t val)
{
    uint8_t buffer[4];

    buffer[0] = (uint8_t)((val)       & 0xFF);
    buffer[1] = (uint8_t)((val >> 8)  & 0xFF);
    buffer[2] = (uint8_t)((val >> 16) & 0xFF);
    buffer[3] = (uint8_t)((val >> 24) & 0xFF);

    return QIO_WRITE(priv->pqio, buffer, 4);
}

static int32_t write_tiff_tag(PRIV_INFO_S* priv, uint16_t tag_name, uint16_t tag_type, uint32_t tag_len, uint32_t tag_val)
{
    RETURN_VAL_IF(write_tiff_two_bytes(priv, tag_name) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_two_bytes(priv, tag_type) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, tag_len) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, tag_val) < 0, QIO_WARN, -1);

    return 0;
}

static int32_t write_tiff_header_flag(PRIV_INFO_S* priv)
{
    RETURN_VAL_IF(write_tiff_two_bytes(priv, 0x4949) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_two_bytes(priv, 0x002A) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, 0x00000008) < 0, QIO_WARN, -1);

    return 0;
}

static int32_t write_tiff_tag_count(PRIV_INFO_S* priv)
{
    RETURN_VAL_IF(write_tiff_two_bytes(priv, 22) < 0, QIO_WARN, -1);

    return 0;
}

static int32_t write_tiff_length(PRIV_INFO_S* priv)
{
    RETURN_VAL_IF(QIO_WRITEABLE(priv->pqio, priv->tos, priv->tous) <= 0, QIO_WARN, -1);

    RETURN_VAL_IF(QIO_SEEK(priv->pqio, TIFF_JPEG_IMAGE_LEN_FIRST_OFFSET_ADDR, SEEK_SET) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, priv->offset) < 0, QIO_WARN, -1);

    RETURN_VAL_IF(QIO_SEEK(priv->pqio, TIFF_JPEG_IMAGE_LEN_SECOND_OFFSET_ADDR, SEEK_SET) < 0, QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, priv->offset) < 0, QIO_WARN, -1);

    return 0;
}

static int32_t write_tiff_header(PRIV_INFO_S* priv)
{
    uint32_t    data_offset = ( TIFF_HEADER_CONTENT_LEN + TIFF_TAG_COUNT_LEN + TIFF_COLOR_TAG_COUNT * TIFF_TAG_CONTENT_LEN + TIFF_TAG_END_LEN );
    int32_t     mono = ( FMT_COLOR(priv->f) == cfMono ? 1 : 0 );
    QIO_DEBUG("FMT_COLOR(%s)", mono ? "cfMono" : "cfRGB/cfCMYK/cfYCbCr...");

    RETURN_VAL_IF(write_tiff_header_flag(priv) < 0, QIO_WARN, -1);  /* 写入TIFF文件标签, 表明该文件是TIFF文件 */
    RETURN_VAL_IF(write_tiff_tag_count(priv) < 0,   QIO_WARN, -1);  /* 写入TIFF文件内Tag 个数 */

    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_SUBFILETYPE, TIFF_LONG, 1, 2) < 0,                                                    QIO_WARN , -1); /* SubFileType 254 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_IMAGEWIDTH, TIFF_LONG, 1, (uint32_t)priv->w) < 0,                                     QIO_WARN , -1); /* ImageWidth 256 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_IMAGELENGTH, TIFF_LONG, 1, (uint32_t)priv->h) < 0,                                    QIO_WARN , -1); /* ImageHeight 257 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_BITSPERSAMPLE, TIFF_SHORT, (mono ? 1 : 3), (mono ? 8 : data_offset + 16)) < 0,        QIO_WARN , -1); /* BitsPerSample 258 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_COMPRESSION, TIFF_SHORT, 1, COMPRESSION_JPEG) < 0,                                    QIO_WARN , -1); /* Compression 259 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_PHOTOMETRIC, TIFF_SHORT, 1, (mono ? PHOTOMETRIC_MINISBLACK : PHOTOMETRIC_YCBCR)) < 0, QIO_WARN , -1); /* PhotoMetric 262 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_FILLORDER, TIFF_SHORT, 1, FILLORDER_LSB2MSB) < 0,                                     QIO_WARN , -1); /* fillorder 266 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_STRIPOFFSETS, TIFF_LONG, 1, 320) < 0,                                                 QIO_WARN , -1); /* StripOffsets 273 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_ORIENTATION, TIFF_SHORT, 1, ORIENTATION_TOPLEFT) < 0,                                 QIO_WARN , -1); /* Orientation 274 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_SAMPLESPERPIXEL, TIFF_SHORT, 1, (mono ? 1 : 3)) < 0,                                  QIO_WARN , -1); /* SamplesPerPixel 277 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_ROWSPERSTRIP, TIFF_LONG, 1, (uint32_t)priv->h) < 0,                                   QIO_WARN , -1); /* RowsPerStrip 278 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_STRIPBYTECOUNTS, TIFF_LONG, 1, 0) < 0,                                                QIO_WARN , -1); /* StripByteCount 279 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_XRESOLUTION, TIFF_RATIONAL, 1, data_offset) < 0,                                      QIO_WARN , -1); /* XResolution 282 */
    data_offset += 8;
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_YRESOLUTION, TIFF_RATIONAL, 1, data_offset) < 0,                                      QIO_WARN , -1); /* YResolution 283 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_RESOLUTIONUNIT, TIFF_SHORT, 1, 2) < 0,                                                QIO_WARN , -1); /* ResolutionUnit 296 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_PAGENUMBER, TIFF_SHORT, 2, 65536) < 0,                                                QIO_WARN , -1); /* page numbers 297 */
    data_offset += (mono ? 8 : 14);
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_SOFTWARE, TIFF_ASCII, 20, data_offset) < 0,                                           QIO_WARN , -1); /* Software 305 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_JPEGPROC, TIFF_SHORT, 1, JPEGPROC_LOSSLESS) < 0,                                      QIO_WARN , -1); /* JpegProc 512 */
    data_offset += 20;
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_JPEGIFOFFSET, TIFF_LONG, 1, data_offset) < 0,                                         QIO_WARN , -1); /* JpegProc 513 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_JPEGIFBYTECOUNT, TIFF_LONG, 1, 0) < 0,                                                QIO_WARN , -1); /* jpegIFByteCount 514 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_YCBCRSUBSAMPLING, TIFF_SHORT, 2, 131074),                                             QIO_WARN , -1); /* JpegProc 530 */
    RETURN_VAL_IF(write_tiff_tag(priv, TIFFTAG_YCBCRPOSITIONING, TIFF_SHORT, 1, YCBCRPOSITION_COSITED) < 0,                          QIO_WARN , -1); /* JpegProc 531 */

    RETURN_VAL_IF(write_tiff_four_bytes(priv, 0) < 0,                   QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, (uint32_t)priv->xres) < 0, QIO_WARN, -1);

    RETURN_VAL_IF(write_tiff_four_bytes(priv, 1) < 0,                   QIO_WARN, -1);
    RETURN_VAL_IF(write_tiff_four_bytes(priv, (uint32_t)priv->yres) < 0, QIO_WARN, -1);

    RETURN_VAL_IF(write_tiff_four_bytes(priv, 1) < 0,                   QIO_WARN, -1);
    if ( !mono )
    {
        RETURN_VAL_IF(write_tiff_two_bytes(priv, 8) < 0,                QIO_WARN, -1);
        RETURN_VAL_IF(write_tiff_two_bytes(priv, 8) < 0,                QIO_WARN, -1);
        RETURN_VAL_IF(write_tiff_two_bytes(priv, 8) < 0,                QIO_WARN, -1);
    }

    RETURN_VAL_IF(QIO_WRITE(priv->pqio, TIFF_SOFTWARE, strlen(TIFF_SOFTWARE)) < 0, QIO_WARN, -1);

    RETURN_VAL_IF(write_tiff_two_bytes(priv, 0) < 0,      QIO_WARN, -1);
    if ( mono )
    {
        RETURN_VAL_IF(write_tiff_four_bytes(priv, 0) < 0, QIO_WARN, -1);
        RETURN_VAL_IF(write_tiff_two_bytes(priv,  0) < 0, QIO_WARN, -1);
    }

    QIO_DEBUG("OK");
    return 0;
}

static int32_t qio_tiff_poll(QIO_S* pqio, int32_t what, int32_t tos, int32_t tous)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(what != QIO_POLL_WRITE, QIO_WARN, QIOEOF);
    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    return ( priv->pqio ? QIO_WRITEABLE(priv->pqio, priv->tos, priv->tous) : 1 );
}

static int32_t qio_tiff_read(QIO_S* pqio, void* buffer, size_t nbuf)
{
    return QIOEOF;
}

static int32_t qio_tiff_write(QIO_S* pqio, void* buffer, size_t nbuf)
{
    DECL_PRIV(pqio, priv);
    size_t  chunk;
    size_t  wtotal;
    int32_t wlen;

    RETURN_VAL_IF(buffer == NULL || nbuf == 0, QIO_WARN, 0);
    RETURN_VAL_IF(priv == NULL, QIO_WARN, QIOEOF);

    for ( wtotal = 0; wtotal < nbuf; wtotal += wlen )
    {
        RETURN_VAL_IF(QIO_WRITEABLE(priv->pqio, priv->tos, priv->tous) <= 0, QIO_WARN, -1);

        chunk = ( ((nbuf - wtotal) < MAX_PDF_CHUNK_SIZE) ? (nbuf - wtotal) : MAX_PDF_CHUNK_SIZE );
        wlen  = QIO_WRITE(priv->pqio, buffer + wtotal, chunk);
        RETURN_VAL_IF(wlen < 0, QIO_WARN, -2);
    }
    priv->offset += wtotal;

    return (int32_t)wtotal;
}

static int32_t qio_tiff_seek(QIO_S* pqio, ssize_t offset, int32_t whence)
{
    return QIOEOF;
}

static int32_t qio_tiff_close(QIO_S* pqio)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(pqio == NULL, QIO_WARN, QIOEOF);

    if ( priv )
    {
        write_tiff_length(priv);
        if ( priv->pqio && priv->ownqio )
        {
            QIO_CLOSE(priv->pqio);
        }
        pi_free(priv);
    }
    memset(pqio, 0, sizeof(QIO_S));
    pi_free(pqio);

    return 0;
}

QIO_S* qio_tiff_create(QIO_S* pqio_dst, int32_t take_owner_ship)
{
    PRIV_INFO_S*    priv;
    QIO_S*          pqio;

    RETURN_VAL_IF(pqio_dst == NULL, QIO_WARN, NULL);

    priv = (PRIV_INFO_S *)pi_zalloc(sizeof(PRIV_INFO_S));
    RETURN_VAL_IF(priv == NULL, QIO_WARN, NULL);

    priv->pqio      = pqio_dst;
    priv->ownqio    = take_owner_ship;
    priv->w         = 0;
    priv->h         = 0;
    priv->d         = 0;
    priv->s         = 0;
    priv->f         = 0;
    priv->xres      = 600;
    priv->yres      = 600;
    priv->clipX     = 0;
    priv->clipY     = 0;
    priv->tos       = 15;
    priv->tous      = 0;

    pqio = (QIO_S *)pi_zalloc(sizeof(QIO_S));
    if ( pqio == NULL )
    {
        QIO_WARN("alloc TIFF<%p> QIO failed: %d<%s>", priv->pqio, errno, strerror(errno));
        pi_free(priv);
        return NULL;
    }

    pqio->close     = qio_tiff_close;
    pqio->poll      = qio_tiff_poll;
    pqio->read      = qio_tiff_read;
    pqio->write     = qio_tiff_write;
    pqio->seek      = qio_tiff_seek;
    pqio->priv      = priv;

    return pqio;
}

int32_t qio_tiff_begin_image(QIO_S* pqio, int32_t w, int32_t h, int32_t d, int32_t s, int32_t f, int32_t xres, int32_t yres, int32_t clipx, int32_t clipy)
{
    DECL_PRIV(pqio, priv);

    RETURN_VAL_IF(priv == NULL, QIO_WARN, -1);

    priv->imgstart = priv->offset;
    priv->w        = w;
    priv->h        = h;
    priv->d        = d;
    priv->s        = s;
    priv->f        = f;
    priv->xres     = xres;
    priv->yres     = yres;
    priv->clipX    = clipx;
    priv->clipY    = clipy;
    write_tiff_header(priv);

    return 0;
}
/**
 *@}
 */
