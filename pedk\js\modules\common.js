/**
 * @namespace pedk.common
 */

/**
 * 用于PeSF应用输出log
 * @since V1.0
 * @param {String/Number} msg log内容
 * @returns {String} 执行结果
 * @see class/pesf/common/errorno.js~ErrorNo.html
 */
function log(msg) { }



/**
 * 文件实体类
 * @class   结构体，数据类型
 */
class FileEntry {
    /**
     * 构造函数
     * @since V1.0
     * @param {String} filename 文件名
     * @param {String} fileext  文件扩展名
     * @param {Boolean} is_dir  是否是目录
     * @param {Number} size     文件大小
     * @param {Array} children  子目录
     */
    constructor(filename, fileext, is_dir, size, children) {
    	if(typeof filename !== 'string'){
			throw TypeError("filename")
		}
		this.filename = filename;
		if(typeof fileext !== 'string'){
			throw TypeError("fileext")
		}
		this.fileext = fileext;
		if(typeof is_dir !== 'boolean'){
			throw TypeError("is_dir")
		}
		this.is_dir = is_dir;
		if(typeof size !== 'number'){
			throw TypeError("size")
		}
		this.size = size;
		if(typeof children !== 'object'){
			throw TypeError("children")
		}
		this.children = children;
	}
}


/**common/errorno.js
 *| 错误码 | 含义 |
 *|:--:|:--:|
 *|EOPNOTSUPP| 不支持此功能 |
 *|EXIT_SUCCESS| 操作成功 |
 *|EXIT_FAILURE| 操作失败 |
 *|ETIMEDOUT| 操作超时 |
 *|EALLOWED| 允许 |
 *|ENOTALLOWED| 不允许 |
 *|ENOTPERMISSION| 没权限 |
 *|EINVALIDPARAM | 无效参数 |
 * @class 字符串类型返回值
 */
const ERROR_NO ={
    EOPNOTSUPP: "EOPNOTSUPP",
    EXIT_SUCCESS: "EXIT_SUCCESS",
    EXIT_FAILURE: "EXIT_FAILURE",
    ETIMEDOUT: "ETIMEDOUT",
    EALLOWED: "EALLOWED",
    ENOTALLOWED: "ENOTALLOWED",
    ENOTPERMISSION: "ENOTPERMISSION",
    EINVALIDPARAM: "EINVALIDPARAM",
}


 /**common/mediasize.js
 *| 纸形 |值| 4020 | kanas |
 *|:--:|:--:|:--:|
 *|A3|1|支持|支持|
 *|A4 |2|支持|支持|
 *|A4 LEF|3|支持|支持|
 *|A5 |4|支持|支持|
 *|A5 LEF|5|支持|支持|
 *|JIS B4|6|支持|支持|
 *|JIS B5|7|支持|支持|
 *|JIS B5 LEF|8|支持|支持|
 *|JIS B6|9|支持|支持|
 *|JIS B6 LEF|10|支持|支持|
 *|ISO B4|11|支持|支持|
 *|ISO B4 LEF|12|支持|支持|
 *|ISO B5|13|支持|支持|
 *|ISO B5 LEF|14|支持|支持|
 *|ISO B6|15|支持|支持|
 *|ISO B6 LEF|16|支持|支持|
 *|8K|17|支持|支持|
 *|Big 16K|18|支持|支持|
 *|Big 16K LEF|19|支持|支持|
 *|16K|20|支持|支持|
 *|16K LEF|21|支持|支持|
 *|Big 32K|22|支持|支持|
 *|Big 32K LEF|23|支持|支持|
 *|32K|24|支持|支持|
 *|32K LEF|25|支持|支持|
 *|11"×17"|26|支持|支持|
 *|Letter|27|支持|支持|
 *|Letter LEF|28|支持|支持|
 *|Legal|29|支持|支持|
 *|Folio|30|支持|支持|
 *|Oficio|31|支持|支持|
 *|Executive|32|支持|支持|
 *|Executive LEF|33|支持|支持|
 *|Statement|34|支持|支持|
 *|Statement LEF|35|支持|支持|
 *|A6|36|支持|支持|
 *|A6 LEF|37|支持|支持|
 *|No.10 Env|38|支持|支持|
 *|Monarch Env|39|支持|支持|
 *|Monarch Env LEF|40|支持|支持|
 *|C6 Env|41|支持|支持|
 *|C6 Env LEF|42|支持|支持|
 *|C5 Env（7号）|43|支持|支持|
 *|C5 Env LEF（7号）|44|支持|支持|
 *|C4 Env|45|支持|支持|
 *|DL Env（5号）|46|支持|支持|
 *|B6|47|支持|支持|
 *|B6 LEF|48|支持|支持|
 *|ZL（6号）|49|支持|支持|
 *|ZL LEF（6号）|50|支持|支持|
 *|Yougata4|51|支持|支持|
 *|Younaga3|52|支持|支持|
 *|Nagagata3|53|支持|支持|
 *|Yougata2|54|支持|支持|
 *|Yougata2 LEF|55|支持|支持|
 *|Postcard|56|支持|支持|
 *|Postcard LEF|57|支持|支持|
 *|Japanese Postcard|58|支持|支持|
 *|Japanese Postcard LEF|59|支持|支持|
 *|User Define	|60|支持|支持|
 *|Ledger	|61|支持|支持|
 *|Big 8K	|62|支持|支持|
 *|Env B6	|63|支持|支持|
 *|FoolScaps	|64|支持|支持|
 *|Invoice	|65|支持|支持|
 *|Invoice LEF	|66|支持|支持|
 *|A3 Wide	|67|支持|支持|
 *|Legal13	|68|支持|支持|
 *|Legal14	|69|支持|支持|
 *|Youkei Size4	|70|支持|支持|
 *|Choukei Size3	|71|支持|支持|
 *|Sra3	|72|支持|支持|
 *|Card	|73|支持|支持|
 * @class
 */

const MEDIA_SIZE = {
    MEDIA_SIZE_A3: "MEDIA_SIZE_A3",
    MEDIA_SIZE_A4: "MEDIA_SIZE_A4",
    MEDIA_SIZE_A4_LEF: "MEDIA_SIZE_A4_LEF",
    MEDIA_SIZE_A5: "MEDIA_SIZE_A5",
    MEDIA_SIZE_A5_LEF: "MEDIA_SIZE_A5_LEF",
    MEDIA_SIZE_JIS_B4: "MEDIA_SIZE_JIS_B4",
    MEDIA_SIZE_JIS_B5: "MEDIA_SIZE_JIS_B5",
    MEDIA_SIZE_JIS_B5_LEF: "MEDIA_SIZE_JIS_B5_LEF",
    MEDIA_SIZE_JIS_B6: "MEDIA_SIZE_JIS_B6",
    MEDIA_SIZE_JIS_B6_LEF: "MEDIA_SIZE_JIS_B6_LEF",
    MEDIA_SIZE_ISO_B4: "MEDIA_SIZE_ISO_B4",
    MEDIA_SIZE_ISO_B4_LEF: "MEDIA_SIZE_ISO_B4_LEF",
    MEDIA_SIZE_ISO_B5: "MEDIA_SIZE_ISO_B5",
    MEDIA_SIZE_ISO_B5_LEF: "MEDIA_SIZE_ISO_B5_LEF",
    MEDIA_SIZE_ISO_B6: "MEDIA_SIZE_ISO_B6",
    MEDIA_SIZE_ISO_B6_LEF: "MEDIA_SIZE_ISO_B6_LEF",
    MEDIA_SIZE_8K: "MEDIA_SIZE_8K",
    MEDIA_SIZE_BIG_16K: "MEDIA_SIZE_BIG_16K",
    MEDIA_SIZE_BIG_16K_LEF: "MEDIA_SIZE_BIG_16K_LEF",
    MEDIA_SIZE_16K: "MEDIA_SIZE_16K",
    MEDIA_SIZE_16K_LEF: "MEDIA_SIZE_16K_LEF",
    MEDIA_SIZE_BIG_32K: "MEDIA_SIZE_BIG_32K",
    MEDIA_SIZE_BIG_32K_LEF: "MEDIA_SIZE_BIG_32K_LEF",
    MEDIA_SIZE_32K: "MEDIA_SIZE_32K",
    MEDIA_SIZE_32K_LEF: "MEDIA_SIZE_32K_LEF",
    MEDIA_SIZE_11x17: "MEDIA_SIZE_11x17",
    MEDIA_SIZE_LETTER: "MEDIA_SIZE_LETTER",
    MEDIA_SIZE_LETTER_LEF: "MEDIA_SIZE_LETTER_LEF",
    MEDIA_SIZE_LEGAL: "MEDIA_SIZE_LEGAL",
    MEDIA_SIZE_FOLIO: "MEDIA_SIZE_FOLIO",
    MEDIA_SIZE_OFICIO: "MEDIA_SIZE_OFICIO",
    MEDIA_SIZE_EXECUTIVE: "MEDIA_SIZE_EXECUTIVE",
    MEDIA_SIZE_EXECUTIVE_LEF: "MEDIA_SIZE_EXECUTIVE_LEF",
    MEDIA_SIZE_STATEMENT: "MEDIA_SIZE_STATEMENT",
    MEDIA_SIZE_STATEMENT_LEF: "MEDIA_SIZE_STATEMENT_LEF",
    MEDIA_SIZE_A6: "MEDIA_SIZE_A6",
    MEDIA_SIZE_A6_LEF: "MEDIA_SIZE_A6_LEF",
    MEDIA_SIZE_NO10_ENV: "MEDIA_SIZE_NO10_ENV",
    MEDIA_SIZE_MONARCH_ENV: "MEDIA_SIZE_MONARCH_ENV",
    MEDIA_SIZE_MONARCH_ENV_LEF: "MEDIA_SIZE_MONARCH_ENV_LEF",
    MEDIA_SIZE_C6_ENV: "MEDIA_SIZE_C6_ENV",
    MEDIA_SIZE_C6_ENV_LEF: "MEDIA_SIZE_C6_ENV_LEF",
    MEDIA_SIZE_C5_ENV: "MEDIA_SIZE_C5_ENV",
    MEDIA_SIZE_C5_ENV_LEF: "MEDIA_SIZE_C5_ENV_LEF",
    MEDIA_SIZE_C4_ENV: "MEDIA_SIZE_C4_ENV",
    MEDIA_SIZE_DL_ENV: "MEDIA_SIZE_DL_ENV",
    MEDIA_SIZE_B6: "MEDIA_SIZE_B6",
    MEDIA_SIZE_B6_LEF: "MEDIA_SIZE_B6_LEF",
    MEDIA_SIZE_ZL: "MEDIA_SIZE_ZL",
    MEDIA_SIZE_ZL_LEF: "MEDIA_SIZE_ZL_LEF",
    MEDIA_SIZE_YOUGATA4: "MEDIA_SIZE_YOUGATA4",
    MEDIA_SIZE_YOUNAGE3: "MEDIA_SIZE_YOUNAGE3",
    MEDIA_SIZE_NAGAGATE3: "MEDIA_SIZE_NAGAGATE3",
    MEDIA_SIZE_YOUGATA2: "MEDIA_SIZE_YOUGATA2",
    MEDIA_SIZE_YOUGATA2_LEF: "MEDIA_SIZE_YOUGATA2_LEF",
    MEDIA_SIZE_POSTCARD: "MEDIA_SIZE_POSTCARD",
    MEDIA_SIZE_POSTCARD_LEF: "MEDIA_SIZE_POSTCARD_LEF",
    MEDIA_SIZE_JAPANESE_POSTCARD: "MEDIA_SIZE_JAPANESE_POSTCARD",
    MEDIA_SIZE_JAPANESE_POSTCARD_LEF: "MEDIA_SIZE_JAPANESE_POSTCARD_LEF",
    MEDIA_SIZE_USERDEFINE: "MEDIA_SIZE_USERDEFINE",
    MEDIA_SIZE_LEDGER: "MEDIA_SIZE_LEDGER",
    MEDIA_SIZE_BIG_8K: "MEDIA_SIZE_BIG_8K",
    MEDIA_SIZE_ENV_B6: "MEDIA_SIZE_ENV_B6",
    MEDIA_SIZE_FOOLSCAPS: "MEDIA_SIZE_FOOLSCAPS",
    MEDIA_SIZE_INVOICE: "MEDIA_SIZE_INVOICE",
    MEDIA_SIZE_INVOICE_LEF: "MEDIA_SIZE_INVOICE_LEF",
    MEDIA_SIZE_A3_WIDE: "MEDIA_SIZE_A3_WIDE",
    MEDIA_SIZE_LEGAL13: "MEDIA_SIZE_LEGAL13",
    MEDIA_SIZE_LEGAL14: "MEDIA_SIZE_LEGAL14",
    MEDIA_SIZE_YOUKEI_SIZE4: "MEDIA_SIZE_YOUKEI_SIZE4",
    MEDIA_SIZE_CHOUKEI_SIZE3: "MEDIA_SIZE_CHOUKEI_SIZE3",
    MEDIA_SIZE_SRA3: "MEDIA_SIZE_SRA3",
    MEDIA_SIZE_CARD: "MEDIA_SIZE_CARD",
}



/**common/mediatype.js
 *| 介质 |值| 4020 | kanas |
 *|:--:|:--:|:--:|
 *|普通复印纸|1|
 *|光面纸|2|
 *|无碳纸|3|
 *|铜版纸|4|
 *|白卡纸|5|
 *|牛皮纸|6|
 *|标签纸|7|
 *|薄纸|8|
 *|厚纸1|9|
 *|厚纸2|10|
 *|厚纸3|11|
 *|信头纸|12|
 *|预印纸|13|
 *|预打孔纸|14|
 *|颜色纸|15|
 *|再生纸|16|
 *|证券纸|17|
 *|信封|18|
 *|投影片|19|
 *|照片纸|20|
 *|透明胶片|21|
 *|明信片|22|
 *|普通纸+|23|
 *|厚纸1+|24|
 * @class
 */

 const MEDIA_TYPE = {
    MEDIA_TYPE_PLAIN: "MEDIA_TYPE_PLAIN",
    MEDIA_TYPE_PLAIN_PLUS: "MEDIA_TYPE_PLAIN_PLUS",
    MEDIA_TYPE_THICK_PAPER1: "MEDIA_TYPE_THICK_PAPER1",
    MEDIA_TYPE_THICK_PAPER1_PLUS: "MEDIA_TYPE_THICK_PAPER1_PLUS",
    MEDIA_TYPE_THICK_PAPER2: "MEDIA_TYPE_THICK_PAPER2",
    MEDIA_TYPE_THICK_PAPER3: "MEDIA_TYPE_THICK_PAPER3",
    MEDIA_TYPE_TRANSPARENCY: "MEDIA_TYPE_TRANSPARENCY",
    MEDIA_TYPE_POSTCARD: "MEDIA_TYPE_POSTCARD",
    MEDIA_TYPE_ENVELOPE: "MEDIA_TYPE_ENVELOPE",
    MEDIA_TYPE_LABEL: "MEDIA_TYPE_LABEL",
    MEDIA_TYPE_THIN: "MEDIA_TYPE_THIN",
    MEDIA_TYPE_THICK: "MEDIA_TYPE_THICK",
    MEDIA_TYPE_CARD_STORK: "MEDIA_TYPE_CARD_STORK",
    MEDIA_TYPE_THICKER: "MEDIA_TYPE_THICKER",
    MEDIA_TYPE_RECYCLED_PAPER: "MEDIA_TYPE_RECYCLED_PAPER",
}



const PEDK_STATUS_ID = {
    PEDK_SID_I_PRINT_INIT: "PEDK_SID_I_PRINT_INIT",
    PEDK_SID_I_PRINT_READY: "PEDK_SID_I_PRINT_READY",
    PEDK_SID_I_PRINT_SLEEP: "PEDK_SID_I_PRINT_SLEEP",
    PEDK_SID_I_PRINT_WARMING: "PEDK_SID_I_PRINT_WARMING",
    PEDK_SID_I_PRINT_PROCESSING: "PEDK_SID_I_PRINT_PROCESSING",
    PEDK_SID_I_PRINT_PRINTING: "PEDK_SID_I_PRINT_PRINTING",
    PEDK_SID_I_PRINT_CANCELING: "PEDK_SID_I_PRINT_CANCELING",
    PEDK_SID_I_PRINT_PAUSING: "PEDK_SID_I_PRINT_PAUSING",
    PEDK_SID_I_PRINT_ACR_CALIBRATION: "PEDK_SID_I_PRINT_ACR_CALIBRATION",
    PEDK_SID_I_PRINT_HOLDPRINT_CONFIRM: "PEDK_SID_I_PRINT_HOLDPRINT_CONFIRM",
    PEDK_SID_I_PRINT_HOLDPRINT_FINISH: "PEDK_SID_I_PRINT_HOLDPRINT_FINISH",
    PEDK_SID_I_PRINT_PINCODEPRINT_FINISH: "PEDK_SID_I_PRINT_PINCODEPRINT_FINISH",
    PEDK_SID_I_PRINT_PAPER_CHANGED: "PEDK_SID_I_PRINT_PAPER_CHANGED",
    PEDK_SID_I_PRINT_HOLDPRINT_LIST_FULL: "PEDK_SID_I_PRINT_HOLDPRINT_LIST_FULL",
    PEDK_SID_I_PRINT_STORAGE_JOB_FULL_FAILED: "PEDK_SID_I_PRINT_STORAGE_JOB_FULL_FAILED",
    PEDK_SID_I_PRINT_STORAGE_JOB_STREAM_FAILED: "PEDK_SID_I_PRINT_STORAGE_JOB_STREAM_FAILED",
    PEDK_SID_I_PRINT_IPS_PARSER_TIMEOUT: "PEDK_SID_I_PRINT_IPS_PARSER_TIMEOUT",
    PEDK_SID_I_PRINT_TONER_EMPTY_COLOR: "PEDK_SID_I_PRINT_TONER_EMPTY_COLOR",
    PEDK_SID_I_PRINT_FE_PEDK_SID_ON: "PEDK_SID_I_PRINT_FE_PEDK_SID_ON",
    PEDK_SID_I_PRINT_UNSUPPORTED_DOCUMENT_FORMAT: "PEDK_SID_I_PRINT_UNSUPPORTED_DOCUMENT_FORMAT",
    PEDK_SID_I_PRINT_OPC_CALIBRATION: "PEDK_SID_I_PRINT_OPC_CALIBRATION",
    PEDK_SID_I_PRINT_TONER_CONSUM_CALIBRATION: "PEDK_SID_I_PRINT_TONER_CONSUM_CALIBRATION",
    PEDK_SID_I_PRINT_HOLDPRINT_STORAGE_FULL: "PEDK_SID_I_PRINT_HOLDPRINT_STORAGE_FULL",
    PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_C: "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_C",
    PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_M: "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_M",
    PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_Y: "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_Y",
    PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_K: "PEDK_SID_W_PRINT_TONER_NEAR_EMPTY_K",
    PEDK_SID_W_PRINT_WASTE_TONER_NEAR_FULL: "PEDK_SID_W_PRINT_WASTE_TONER_NEAR_FULL",
    PEDK_SID_W_PRINT_FUSER_NEAR_LIFE_END: "PEDK_SID_W_PRINT_FUSER_NEAR_LIFE_END",
    PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_C: "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_C",
    PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_M: "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_M",
    PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_Y: "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_Y",
    PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_K: "PEDK_SID_W_PRINT_DRUM_NEAR_LIFE_END_K",
    PEDK_SID_W_PRINT_TRAY_MISSING_STANDARD_TRAY: "PEDK_SID_W_PRINT_TRAY_MISSING_STANDARD_TRAY",
    PEDK_SID_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1: "PEDK_SID_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1",
    PEDK_SID_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2: "PEDK_SID_W_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2",
    PEDK_SID_W_PRINT_TRAY_EMPTY_STANDARD_TRAY: "PEDK_SID_W_PRINT_TRAY_EMPTY_STANDARD_TRAY",
    PEDK_SID_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1: "PEDK_SID_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1",
    PEDK_SID_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2: "PEDK_SID_W_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2",
    PEDK_SID_W_PRINT_COLOR_CALIBRATION: "PEDK_SID_W_PRINT_COLOR_CALIBRATION",
    PEDK_SID_W_PRINT_COLOR_REGISTRATION: "PEDK_SID_W_PRINT_COLOR_REGISTRATION",
    PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_C: "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_C",
    PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_M: "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_M",
    PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_Y: "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_Y",
    PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_K: "PEDK_SID_W_PRINT_DRUM_CARRIER_LOW_K",
    PEDK_SID_W_PRINT_REALAY_ROLLER_NEAR_LIFE_END: "PEDK_SID_W_PRINT_REALAY_ROLLER_NEAR_LIFE_END",
    PEDK_SID_W_PRINT_ITU_NEAR_LIFE_END: "PEDK_SID_W_PRINT_ITU_NEAR_LIFE_END",
    PEDK_SID_W_PRINT_TRAY2_ERROR: "PEDK_SID_W_PRINT_TRAY2_ERROR",
    PEDK_SID_W_PRINT_TRAY3_ERROR: "PEDK_SID_W_PRINT_TRAY3_ERROR",
    PEDK_SID_W_PRINT_TRAY2_MOTOR_ERROR: "PEDK_SID_W_PRINT_TRAY2_MOTOR_ERROR",
    PEDK_SID_W_PRINT_TRAY3_MOTOR_ERROR: "PEDK_SID_W_PRINT_TRAY3_MOTOR_ERROR",
    PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_Y: "PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_Y",
    PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_M: "PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_M",
    PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_C: "PEDK_SID_W_PRINT_DEVELOP_CO_HIGH_ERROR_C",
    PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_Y: "PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_Y",
    PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_M: "PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_M",
    PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_C: "PEDK_SID_W_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_C",
    PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_Y: "PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_Y",
    PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_M: "PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_M",
    PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_C: "PEDK_SID_W_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_C",
    PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_Y: "PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_Y",
    PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_M: "PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_M",
    PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_C: "PEDK_SID_W_PRINT_CO_SENSOR_DETECT_ERROR_C",
    PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_Y: "PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_Y",
    PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_M: "PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_M",
    PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_C: "PEDK_SID_W_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_C",
    PEDK_SID_W_PRINT_INTERNAL_ENV_TEMP_ERROR: "PEDK_SID_W_PRINT_INTERNAL_ENV_TEMP_ERROR",
    PEDK_SID_W_PRINT_EXTERNAL_ENV_TEMP_ERROR: "PEDK_SID_W_PRINT_EXTERNAL_ENV_TEMP_ERROR",
    PEDK_SID_W_PRINT_FRONT_CTD_SENSOR_DETECT: "PEDK_SID_W_PRINT_FRONT_CTD_SENSOR_DETECT",
    PEDK_SID_W_PRINT_BACK_CTD_SENSOR_DETECT: "PEDK_SID_W_PRINT_BACK_CTD_SENSOR_DETECT",
    PEDK_SID_W_PRINT_FRONT_CTD_SENSOR_ADJUST: "PEDK_SID_W_PRINT_FRONT_CTD_SENSOR_ADJUST",
    PEDK_SID_W_PRINT_BACK_CTD_SENSOR_ADJUST: "PEDK_SID_W_PRINT_BACK_CTD_SENSOR_ADJUST",
    PEDK_SID_W_PRINT_TONER_EMPTY_C: "PEDK_SID_W_PRINT_TONER_EMPTY_C",
    PEDK_SID_W_PRINT_TONER_EMPTY_M: "PEDK_SID_W_PRINT_TONER_EMPTY_M",
    PEDK_SID_W_PRINT_TONER_EMPTY_Y: "PEDK_SID_W_PRINT_TONER_EMPTY_Y",
    PEDK_SID_W_PRINT_TONER_EMPTY_K: "PEDK_SID_W_PRINT_TONER_EMPTY_K",
    PEDK_SID_W_PRINT_LSU_TEMP_SENSOR_ERROR: "PEDK_SID_W_PRINT_LSU_TEMP_SENSOR_ERROR",
    PEDK_SID_W_PRINT_FE0280_03: "PEDK_SID_W_PRINT_FE0280_03",
    PEDK_SID_W_PRINT_FE0280_04: "PEDK_SID_W_PRINT_FE0280_04",
    PEDK_SID_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1: "PEDK_SID_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1",
    PEDK_SID_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2: "PEDK_SID_W_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2",
    PEDK_SID_W_PRINT_FE0280_01: "PEDK_SID_W_PRINT_FE0280_01",
    PEDK_SID_W_PRINT_FE0280_02: "PEDK_SID_W_PRINT_FE0280_02",
    PEDK_SID_W_PRINT_OPTION_TRAY1_ADJUSTING: "PEDK_SID_W_PRINT_OPTION_TRAY1_ADJUSTING",
    PEDK_SID_W_PRINT_OPTION_TRAY2_ADJUSTING: "PEDK_SID_W_PRINT_OPTION_TRAY2_ADJUSTING",
    PEDK_SID_W_PRINT_WASTE_TONER_FULL: "PEDK_SID_W_PRINT_WASTE_TONER_FULL",
    PEDK_SID_W_PRINT_DRUM_LIFE_END_C: "PEDK_SID_W_PRINT_DRUM_LIFE_END_C",
    PEDK_SID_W_PRINT_DRUM_LIFE_END_M: "PEDK_SID_W_PRINT_DRUM_LIFE_END_M",
    PEDK_SID_W_PRINT_DRUM_LIFE_END_Y: "PEDK_SID_W_PRINT_DRUM_LIFE_END_Y",
    PEDK_SID_W_PRINT_DRUM_LIFE_END_K: "PEDK_SID_W_PRINT_DRUM_LIFE_END_K",
    PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_C: "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_C",
    PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_M: "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_M",
    PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_Y: "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_Y",
    PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_K: "PEDK_SID_W_PRINT_DRUM_CARRIER_EMPTY_K",
    PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_C: "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_C",
    PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_M: "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_M",
    PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_Y: "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_Y",
    PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_K: "PEDK_SID_W_PRINT_TONER_REMAIN_INSUFFICIENT_K",
    PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_C: "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_C",
    PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_M: "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_M",
    PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_Y: "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_Y",
    PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_K: "PEDK_SID_W_PRINT_DRUM_LIFE_INSUFFICIENT_K",
    PEDK_SID_W_PRINT_LOW_TEMPERATURE: "PEDK_SID_W_PRINT_LOW_TEMPERATURE",
    PEDK_SID_E_PRINT_RIGHT_COVER_OPEN: "PEDK_SID_E_PRINT_RIGHT_COVER_OPEN",
    PEDK_SID_E_PRINT_FRONT_COVER_OPEN: "PEDK_SID_E_PRINT_FRONT_COVER_OPEN",
    PEDK_SID_E_PRINT_TRAY2_COVER_OPEN: "PEDK_SID_E_PRINT_TRAY2_COVER_OPEN",
    PEDK_SID_E_PRINT_TRAY3_COVER_OPEN: "PEDK_SID_E_PRINT_TRAY3_COVER_OPEN",
    PEDK_SID_E_PRINT_TONER_EMPTY_C: "PEDK_SID_E_PRINT_TONER_EMPTY_C",
    PEDK_SID_E_PRINT_TONER_EMPTY_M: "PEDK_SID_E_PRINT_TONER_EMPTY_M",
    PEDK_SID_E_PRINT_TONER_EMPTY_Y: "PEDK_SID_E_PRINT_TONER_EMPTY_Y",
    PEDK_SID_E_PRINT_TONER_EMPTY_K: "PEDK_SID_E_PRINT_TONER_EMPTY_K",
    PEDK_SID_E_PRINT_TONER_MISSING_C: "PEDK_SID_E_PRINT_TONER_MISSING_C",
    PEDK_SID_E_PRINT_TONER_MISSING_M: "PEDK_SID_E_PRINT_TONER_MISSING_M",
    PEDK_SID_E_PRINT_TONER_MISSING_Y: "PEDK_SID_E_PRINT_TONER_MISSING_Y",
    PEDK_SID_E_PRINT_TONER_MISSING_K: "PEDK_SID_E_PRINT_TONER_MISSING_K",
    PEDK_SID_E_PRINT_TONER_MISMATCH_C: "PEDK_SID_E_PRINT_TONER_MISMATCH_C",
    PEDK_SID_E_PRINT_TONER_MISMATCH_M: "PEDK_SID_E_PRINT_TONER_MISMATCH_M",
    PEDK_SID_E_PRINT_TONER_MISMATCH_Y: "PEDK_SID_E_PRINT_TONER_MISMATCH_Y",
    PEDK_SID_E_PRINT_TONER_MISMATCH_K: "PEDK_SID_E_PRINT_TONER_MISMATCH_K",
    PEDK_SID_E_PRINT_DRUM_LIFE_END_C: "PEDK_SID_E_PRINT_DRUM_LIFE_END_C",
    PEDK_SID_E_PRINT_DRUM_LIFE_END_M: "PEDK_SID_E_PRINT_DRUM_LIFE_END_M",
    PEDK_SID_E_PRINT_DRUM_LIFE_END_Y: "PEDK_SID_E_PRINT_DRUM_LIFE_END_Y",
    PEDK_SID_E_PRINT_DRUM_LIFE_END_K: "PEDK_SID_E_PRINT_DRUM_LIFE_END_K",
    PEDK_SID_E_PRINT_DRUM_MISSING_C: "PEDK_SID_E_PRINT_DRUM_MISSING_C",
    PEDK_SID_E_PRINT_DRUM_MISSING_M: "PEDK_SID_E_PRINT_DRUM_MISSING_M",
    PEDK_SID_E_PRINT_DRUM_MISSING_Y: "PEDK_SID_E_PRINT_DRUM_MISSING_Y",
    PEDK_SID_E_PRINT_DRUM_MISSING_K: "PEDK_SID_E_PRINT_DRUM_MISSING_K",
    PEDK_SID_E_PRINT_DRUM_MISMATCH_C: "PEDK_SID_E_PRINT_DRUM_MISMATCH_C",
    PEDK_SID_E_PRINT_DRUM_MISMATCH_M: "PEDK_SID_E_PRINT_DRUM_MISMATCH_M",
    PEDK_SID_E_PRINT_DRUM_MISMATCH_Y: "PEDK_SID_E_PRINT_DRUM_MISMATCH_Y",
    PEDK_SID_E_PRINT_DRUM_MISMATCH_K: "PEDK_SID_E_PRINT_DRUM_MISMATCH_K",
    PEDK_SID_E_PRINT_WASTE_TONER_FULL: "PEDK_SID_E_PRINT_WASTE_TONER_FULL",
    PEDK_SID_E_PRINT_WASTE_TONER_MISSING: "PEDK_SID_E_PRINT_WASTE_TONER_MISSING",
    PEDK_SID_E_PRINT_TRAY_MISSING_STANDARD_TRAY: "PEDK_SID_E_PRINT_TRAY_MISSING_STANDARD_TRAY",
    PEDK_SID_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1: "PEDK_SID_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_1",
    PEDK_SID_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2: "PEDK_SID_E_PRINT_TRAY_MISSING_OPTIONAL_TRAY_2",
    PEDK_SID_E_PRINT_TRAY_EMPTY_STANDARD_TRAY: "PEDK_SID_E_PRINT_TRAY_EMPTY_STANDARD_TRAY",
    PEDK_SID_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY: "PEDK_SID_E_PRINT_TRAY_EMPTY_MULTI_FUNCTION_TRAY",
    PEDK_SID_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1: "PEDK_SID_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_1",
    PEDK_SID_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2: "PEDK_SID_E_PRINT_TRAY_EMPTY_OPTIONAL_TRAY_2",
    PEDK_SID_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH: "PEDK_SID_E_PRINT_TRAY1_PAPER_SIZE_MISMATCH",
    PEDK_SID_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH: "PEDK_SID_E_PRINT_MPTRAY_PAPER_SIZE_MISMATCH",
    PEDK_SID_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH: "PEDK_SID_E_PRINT_TRAY1_PAPER_TYPE_MISMATCH",
    PEDK_SID_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH: "PEDK_SID_E_PRINT_MPTRAY_PAPER_TYPE_MISMATCH",
    PEDK_SID_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH: "PEDK_SID_E_PRINT_TRAY_2_PAPER_SIZE_MISMATCH",
    PEDK_SID_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH: "PEDK_SID_E_PRINT_TRAY_3_PAPER_SIZE_MISMATCH",
    PEDK_SID_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH: "PEDK_SID_E_PRINT_TRAY_2_PAPER_TYPE_MISMATCH",
    PEDK_SID_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH: "PEDK_SID_E_PRINT_TRAY_3_PAPER_TYPE_MISMATCH",
    PEDK_SID_E_PRINT_ENGINE_NOT_READY: "PEDK_SID_E_PRINT_ENGINE_NOT_READY",
    PEDK_SID_E_PRINT_ENGINE_COMMUNICATION_FAILED: "PEDK_SID_E_PRINT_ENGINE_COMMUNICATION_FAILED",
    PEDK_SID_E_PRINT_VIDEO_DRIVE: "PEDK_SID_E_PRINT_VIDEO_DRIVE",
    PEDK_SID_E_PRINT_VIDEO_BANDING: "PEDK_SID_E_PRINT_VIDEO_BANDING",
    PEDK_SID_E_PRINT_FUSER_MISSING: "PEDK_SID_E_PRINT_FUSER_MISSING",
    PEDK_SID_E_PRINT_FUSER_LIFE_END: "PEDK_SID_E_PRINT_FUSER_LIFE_END",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT: "PEDK_SID_E_PRINT_JAM_RESIDUAL_DUPLEX_INPUT",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR: "PEDK_SID_E_PRINT_JAM_RESIDUAL_OUTPUT_SENSOR",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_SENSOR: "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_SENSOR",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2: "PEDK_SID_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_2",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1: "PEDK_SID_E_PRINT_JAM_RESIDUAL_OPTIONAL_TRAY_1",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY: "PEDK_SID_E_PRINT_JAM_RESIDUAL_MULTI_FUNCTION_TRAY",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY: "PEDK_SID_E_PRINT_JAM_RESIDUAL_STANDARD_TRAY",
    PEDK_SID_E_PRINT_TIMEPRINT_LIST_FULL: "PEDK_SID_E_PRINT_TIMEPRINT_LIST_FULL",
    PEDK_SID_E_PRINT_TRAY_PAPER_SIZE_ERROR: "PEDK_SID_E_PRINT_TRAY_PAPER_SIZE_ERROR",
    PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_C: "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_C",
    PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_M: "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_M",
    PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_Y: "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_Y",
    PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_K: "PEDK_SID_E_PRINT_DRUM_CARRIER_EMPTY_K",
    PEDK_SID_E_PRINT_REALAY_ROLLER_LIFE_END: "PEDK_SID_E_PRINT_REALAY_ROLLER_LIFE_END",
    PEDK_SID_E_PRINT_ITU_NEAR_LIFE_END: "PEDK_SID_E_PRINT_ITU_NEAR_LIFE_END",
    PEDK_SID_E_PRINT_ILLEGAL_PAGE: "PEDK_SID_E_PRINT_ILLEGAL_PAGE",
    PEDK_SID_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1: "PEDK_SID_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY1",
    PEDK_SID_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2: "PEDK_SID_E_PRINT_BOTTOM_ERROR_OPTIONAL_TRAY2",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT: "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_DUPLEX: "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_DUPLEX",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_OUTPUT_DUPLEX: "PEDK_SID_E_PRINT_JAM_RESIDUAL_OUTPUT_DUPLEX",
    PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT_DUPLEX: "PEDK_SID_E_PRINT_JAM_RESIDUAL_CALIB_OUTPUT_DUPLEX",
    PEDK_SID_F_PRINT_LSU_FAN_ERROR: "PEDK_SID_F_PRINT_LSU_FAN_ERROR",
    PEDK_SID_F_PRINT_FUSER_EXHAUST_FAN_ERROR: "PEDK_SID_F_PRINT_FUSER_EXHAUST_FAN_ERROR",
    PEDK_SID_F_PRINT_FUSER_TEMP_RAISED_SLOW: "PEDK_SID_F_PRINT_FUSER_TEMP_RAISED_SLOW",
    PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR: "PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR",
    PEDK_SID_F_PRINT_FUSER_OVER_HEAT: "PEDK_SID_F_PRINT_FUSER_OVER_HEAT",
    PEDK_SID_F_PRINT_FUSER_HW_OVER_HEAT: "PEDK_SID_F_PRINT_FUSER_HW_OVER_HEAT",
    PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR_3: "PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR_3",
    PEDK_SID_F_PRINT_FUSER_OVER_HEAT_3: "PEDK_SID_F_PRINT_FUSER_OVER_HEAT_3",
    PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_1: "PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_1",
    PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_2: "PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_2",
    PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_3: "PEDK_SID_F_PRINT_FUSER_TEMPERATURE_LOW_3",
    PEDK_SID_F_PRINT_THERMISTOR_DAMAGE_1: "PEDK_SID_F_PRINT_THERMISTOR_DAMAGE_1",
    PEDK_SID_F_PRINT_THERMISTOR_DAMAGE_3: "PEDK_SID_F_PRINT_THERMISTOR_DAMAGE_3",
    PEDK_SID_F_PRINT_FUSER_MOTOR_ERROR: "PEDK_SID_F_PRINT_FUSER_MOTOR_ERROR",
    PEDK_SID_F_PRINT_BLACKDEVELOP_MOTOR_ERROR: "PEDK_SID_F_PRINT_BLACKDEVELOP_MOTOR_ERROR",
    PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_ON_TIMEOUT: "PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_ON_TIMEOUT",
    PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_OFF_TIMEOUT: "PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_OFF_TIMEOUT",
    PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_SIGNAL_ERROR: "PEDK_SID_F_PRINT_MULTI_PRISM_MOTOR_SIGNAL_ERROR",
    PEDK_SID_F_PRINT_LD0_COCURRENT_K_ERROR: "PEDK_SID_F_PRINT_LD0_COCURRENT_K_ERROR",
    PEDK_SID_F_PRINT_LD0_COCURRENT_Y_ERROR: "PEDK_SID_F_PRINT_LD0_COCURRENT_Y_ERROR",
    PEDK_SID_F_PRINT_CTL_VIDEO_OK_NOTIFY_ERROR: "PEDK_SID_F_PRINT_CTL_VIDEO_OK_NOTIFY_ERROR",
    PEDK_SID_F_PRINT_EC_WARMUP_CANNOT_STOP: "PEDK_SID_F_PRINT_EC_WARMUP_CANNOT_STOP",
    PEDK_SID_F_PRINT_EC_PAGE_BUFFER_FULL: "PEDK_SID_F_PRINT_EC_PAGE_BUFFER_FULL",
    PEDK_SID_F_PRINT_EC_PAGE_CANNOT_START: "PEDK_SID_F_PRINT_EC_PAGE_CANNOT_START",
    PEDK_SID_F_PRINT_EC_PAGE_CANNOT_STOP: "PEDK_SID_F_PRINT_EC_PAGE_CANNOT_STOP",
    PEDK_SID_F_PRINT_EC_PRINT_CANNOT_STOP: "PEDK_SID_F_PRINT_EC_PRINT_CANNOT_STOP",
    PEDK_SID_F_PRINT_TEMPERATURE_UNSTABLE_1: "PEDK_SID_F_PRINT_TEMPERATURE_UNSTABLE_1",
    PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR_2: "PEDK_SID_F_PRINT_FUSER_THERMISTOR_ERROR_2",
    PEDK_SID_F_PRINT_FUSER_OVER_HEAT_2: "PEDK_SID_F_PRINT_FUSER_OVER_HEAT_2",
    PEDK_SID_F_PRINT_FUSER_THERMISTOR_DAMAGE_2: "PEDK_SID_F_PRINT_FUSER_THERMISTOR_DAMAGE_2",
    PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_K: "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_K",
    PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_M: "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_M",
    PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_Y: "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_Y",
    PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_C: "PEDK_SID_F_PRINT_CHARGEING_OUTPUT_ERROR_C",
    PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_K: "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_K",
    PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_M: "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_M",
    PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_Y: "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_Y",
    PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_C: "PEDK_SID_F_PRINT_DEVELOP_HIGH_PRESSURE_ERROR_C",
    PEDK_SID_F_PRINT_TONER_SUPPLY_MOTOR_ERROR: "PEDK_SID_F_PRINT_TONER_SUPPLY_MOTOR_ERROR",
    PEDK_SID_F_PRINT_BK_PHOTORECEPTOR_MOTOR_ERROR: "PEDK_SID_F_PRINT_BK_PHOTORECEPTOR_MOTOR_ERROR",
    PEDK_SID_F_PRINT_COLOR_PHOTORECEPTRO_MOTOR_ERROR: "PEDK_SID_F_PRINT_COLOR_PHOTORECEPTRO_MOTOR_ERROR",
    PEDK_SID_F_PRINT_1TB_COMPRESS_MOTOR_ERROR: "PEDK_SID_F_PRINT_1TB_COMPRESS_MOTOR_ERROR",
    PEDK_SID_F_PRINT_NEW_DRUM_EXCHANGE_ERROR: "PEDK_SID_F_PRINT_NEW_DRUM_EXCHANGE_ERROR",
    PEDK_SID_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_1: "PEDK_SID_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_1",
    PEDK_SID_F_PRINT_TRANS_MOTOR_ERROR_1: "PEDK_SID_F_PRINT_TRANS_MOTOR_ERROR_1",
    PEDK_SID_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_2: "PEDK_SID_F_PRINT_TRANS_HIGH_VOLTAGE_ERROR_2",
    PEDK_SID_F_PRINT_TRANS_SEPARATION_MOTOR_ERROR_2: "PEDK_SID_F_PRINT_TRANS_SEPARATION_MOTOR_ERROR_2",
    PEDK_SID_F_PRINT_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2: "PEDK_SID_F_PRINT_TRANS_HIGH_VOL_CON_VOL_OUT_ERR_2",
    PEDK_SID_F_PRINT_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2: "PEDK_SID_F_PRINT_TRANS_HIGH_VOL_CON_CUR_OUT_ERR_2",
    PEDK_SID_F_PRINT_TRANS_SEPARATION_HIGH_VOL_ERROR: "PEDK_SID_F_PRINT_TRANS_SEPARATION_HIGH_VOL_ERROR",
    PEDK_SID_F_PRINT_HIGH_VOL_PLATE_INSERT_ERROR: "PEDK_SID_F_PRINT_HIGH_VOL_PLATE_INSERT_ERROR",
    PEDK_SID_F_PRINT_HIGH_VOL_DETECTION_ERROR: "PEDK_SID_F_PRINT_HIGH_VOL_DETECTION_ERROR",
    PEDK_SID_F_PRINT_LSU_CABLE_NOT_CONNECTED: "PEDK_SID_F_PRINT_LSU_CABLE_NOT_CONNECTED",
    PEDK_SID_F_PRINT_DEVELOP_CO_HIGH_ERROR_K: "PEDK_SID_F_PRINT_DEVELOP_CO_HIGH_ERROR_K",
    PEDK_SID_F_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_K: "PEDK_SID_F_PRINT_CO_SENSOR_VOL_LOWER_LIMIT_K",
    PEDK_SID_F_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_K: "PEDK_SID_F_PRINT_CO_SENSOR_VOL_UPPER_LIMIT_K",
    PEDK_SID_F_PRINT_CO_SENSOR_DETECT_ERROR_K: "PEDK_SID_F_PRINT_CO_SENSOR_DETECT_ERROR_K",
    PEDK_SID_F_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_K: "PEDK_SID_F_PRINT_TONER_SUPPLY_UPPER_TEN_ERROR_K",
    PEDK_SID_F_PRINT_ENV_TEMP_BOTH_ERROR: "PEDK_SID_F_PRINT_ENV_TEMP_BOTH_ERROR",
    PEDK_SID_F_PRINT_LD0_COCURRENT_K_UNSTABLE: "PEDK_SID_F_PRINT_LD0_COCURRENT_K_UNSTABLE",
    PEDK_SID_F_PRINT_LD0_COCURRENT_Y_UNSTABLE: "PEDK_SID_F_PRINT_LD0_COCURRENT_Y_UNSTABLE",
    PEDK_SID_F_PRINT_EC_CALIBRATION_CANNOT_STOP: "PEDK_SID_F_PRINT_EC_CALIBRATION_CANNOT_STOP",
    PEDK_SID_F_PRINT_COMPLETE_COOLING_FAN1_ERROR: "PEDK_SID_F_PRINT_COMPLETE_COOLING_FAN1_ERROR",
    PEDK_SID_F_PRINT_LSU_FRONT_COOLING_FAN_ERROR: "PEDK_SID_F_PRINT_LSU_FRONT_COOLING_FAN_ERROR",
    PEDK_SID_F_PRINT_FUSER_EXIT_COOLING_FAN_ERROR: "PEDK_SID_F_PRINT_FUSER_EXIT_COOLING_FAN_ERROR",
    PEDK_SID_F_PRINT_IMAGING_MAIN_MOTOR_K_ERROR: "PEDK_SID_F_PRINT_IMAGING_MAIN_MOTOR_K_ERROR",
    PEDK_SID_F_PRINT_IMAGING_MAIN_MOTOR_YMC_ERROR: "PEDK_SID_F_PRINT_IMAGING_MAIN_MOTOR_YMC_ERROR",
    PEDK_SID_F_PRINT_FE0340_01: "PEDK_SID_F_PRINT_FE0340_01",
    PEDK_SID_F_PRINT_FE0340_02: "PEDK_SID_F_PRINT_FE0340_02",
    PEDK_SID_F_PRINT_FE0340_03: "PEDK_SID_F_PRINT_FE0340_03",
    PEDK_SID_F_PRINT_FE0340_04: "PEDK_SID_F_PRINT_FE0340_04",
    PEDK_SID_F_PRINT_FE0154_00: "PEDK_SID_F_PRINT_FE0154_00",
    PEDK_SID_F_PRINT_FE0164_00: "PEDK_SID_F_PRINT_FE0164_00",
    PEDK_SID_F_PRINT_FE0170_01: "PEDK_SID_F_PRINT_FE0170_01",
    PEDK_SID_F_PRINT_FE0170_02: "PEDK_SID_F_PRINT_FE0170_02",
    PEDK_SID_F_PRINT_FE0170_03: "PEDK_SID_F_PRINT_FE0170_03",
    PEDK_SID_F_PRINT_FE0172_01: "PEDK_SID_F_PRINT_FE0172_01",
    PEDK_SID_F_PRINT_FE0172_02: "PEDK_SID_F_PRINT_FE0172_02",
    PEDK_SID_F_PRINT_FE0174_00: "PEDK_SID_F_PRINT_FE0174_00",
    PEDK_SID_F_PRINT_FE0820_00: "PEDK_SID_F_PRINT_FE0820_00",
    PEDK_SID_F_PRINT_FE0821_00: "PEDK_SID_F_PRINT_FE0821_00",
    PEDK_SID_F_PRINT_FE0822_00: "PEDK_SID_F_PRINT_FE0822_00",
    PEDK_SID_F_PRINT_FE0840_00: "PEDK_SID_F_PRINT_FE0840_00",
    PEDK_SID_F_PRINT_FE0171_01: "PEDK_SID_F_PRINT_FE0171_01",
    PEDK_SID_F_PRINT_FE0171_02: "PEDK_SID_F_PRINT_FE0171_02",
    PEDK_SID_F_PRINT_FE0903_11: "PEDK_SID_F_PRINT_FE0903_11",
    PEDK_SID_F_PRINT_FE0830_00: "PEDK_SID_F_PRINT_FE0830_00",
    PEDK_SID_F_PRINT_FE0180_00: "PEDK_SID_F_PRINT_FE0180_00",
    PEDK_SID_F_PRINT_FE0180_01: "PEDK_SID_F_PRINT_FE0180_01",
    PEDK_SID_F_PRINT_FE0181_00: "PEDK_SID_F_PRINT_FE0181_00",
    PEDK_SID_F_PRINT_FE0182_00: "PEDK_SID_F_PRINT_FE0182_00",
    PEDK_SID_F_PRINT_FE0183_00: "PEDK_SID_F_PRINT_FE0183_00",
    PEDK_SID_F_PRINT_FE0184_00: "PEDK_SID_F_PRINT_FE0184_00",
    PEDK_SID_F_PRINT_FE0900_00: "PEDK_SID_F_PRINT_FE0900_00",
    PEDK_SID_F_PRINT_FE0175_00: "PEDK_SID_F_PRINT_FE0175_00",
    PEDK_SID_F_PRINT_FE0822_01: "PEDK_SID_F_PRINT_FE0822_01",
    PEDK_SID_F_PRINT_FE0361_00: "PEDK_SID_F_PRINT_FE0361_00",
    PEDK_SID_F_PRINT_FE_UNKNOWN: "PEDK_SID_F_PRINT_FE_UNKNOWN",
    PEDK_SID_I_SCAN_INIT: "PEDK_SID_I_SCAN_INIT",
    PEDK_SID_I_SCAN_IDLE: "PEDK_SID_I_SCAN_IDLE",
    PEDK_SID_I_SCAN_SLEEP: "PEDK_SID_I_SCAN_SLEEP",
    PEDK_SID_I_SCAN_PROCESSING: "PEDK_SID_I_SCAN_PROCESSING",
    PEDK_SID_I_SCAN_RUNNING: "PEDK_SID_I_SCAN_RUNNING",
    PEDK_SID_I_SCAN_CANCELING: "PEDK_SID_I_SCAN_CANCELING",
    PEDK_SID_I_SCAN_TOFILE_SENDING: "PEDK_SID_I_SCAN_TOFILE_SENDING",
    PEDK_SID_I_SCAN_NEXT_PAGE_WAITING: "PEDK_SID_I_SCAN_NEXT_PAGE_WAITING",
    PEDK_SID_I_SCAN_FINISHED: "PEDK_SID_I_SCAN_FINISHED",
    PEDK_SID_I_SCAN_TO_FILE_UDISK_SAVING: "PEDK_SID_I_SCAN_TO_FILE_UDISK_SAVING",
    PEDK_SID_I_SCAN_TO_FILE_SENT: "PEDK_SID_I_SCAN_TO_FILE_SENT",
    PEDK_SID_I_SCAN_ADF_PAPER_PRESENT: "PEDK_SID_I_SCAN_ADF_PAPER_PRESENT",
    PEDK_SID_I_SCAN_ADF_PAPER_REMOVED: "PEDK_SID_I_SCAN_ADF_PAPER_REMOVED",
    PEDK_SID_I_SCAN_PUT_PAPER_TO_ADF: "PEDK_SID_I_SCAN_PUT_PAPER_TO_ADF",
    PEDK_SID_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF: "PEDK_SID_I_SCAN_PUT_NEXT_SIDE_CONFIRM_ADF",
    PEDK_SID_I_SCAN_PAPER_SIZE_UNSUPPORT_MIXED_SCAN: "PEDK_SID_I_SCAN_PAPER_SIZE_UNSUPPORT_MIXED_SCAN",
    PEDK_SID_I_SCAN_NO_RESOURCE: "PEDK_SID_I_SCAN_NO_RESOURCE",
    PEDK_SID_I_SCAN_ADF_BOARD_WAIT_INIT: "PEDK_SID_I_SCAN_ADF_BOARD_WAIT_INIT",
    PEDK_SID_I_SCAN_ADF_BOARD_INITING: "PEDK_SID_I_SCAN_ADF_BOARD_INITING",
    PEDK_SID_I_SCAN_ADF_BOARD_IDLE: "PEDK_SID_I_SCAN_ADF_BOARD_IDLE",
    PEDK_SID_I_SCAN_ADF_BOARD_WORKING: "PEDK_SID_I_SCAN_ADF_BOARD_WORKING",
    PEDK_SID_I_SCAN_ADF_BOARD_ERR: "PEDK_SID_I_SCAN_ADF_BOARD_ERR",
    PEDK_SID_I_SCAN_ADF_BOARD_WARNING: "PEDK_SID_I_SCAN_ADF_BOARD_WARNING",
    PEDK_SID_I_SCAN_OUT_TO_EML_SUCCESS: "PEDK_SID_I_SCAN_OUT_TO_EML_SUCCESS",
    PEDK_SID_I_SCAN_OUT_TO_FTP_SUCCESS: "PEDK_SID_I_SCAN_OUT_TO_FTP_SUCCESS",
    PEDK_SID_I_SCAN_OUT_TO_UDISK_SUCCESS: "PEDK_SID_I_SCAN_OUT_TO_UDISK_SUCCESS",
    PEDK_SID_I_SCAN_OUT_TO_WSD_SUCCESS: "PEDK_SID_I_SCAN_OUT_TO_WSD_SUCCESS",
    PEDK_SID_I_SCAN_OUT_TO_AIRSCAN_SUCCESS: "PEDK_SID_I_SCAN_OUT_TO_AIRSCAN_SUCCESS",
    PEDK_SID_I_SCAN_OUT_TO_EML_CANCEL: "PEDK_SID_I_SCAN_OUT_TO_EML_CANCEL",
    PEDK_SID_I_SCAN_OUT_TO_FTP_CANCEL: "PEDK_SID_I_SCAN_OUT_TO_FTP_CANCEL",
    PEDK_SID_I_SCAN_OUT_TO_UDISK_CANCEL: "PEDK_SID_I_SCAN_OUT_TO_UDISK_CANCEL",
    PEDK_SID_I_SCAN_OUT_TO_WSD_CANCEL: "PEDK_SID_I_SCAN_OUT_TO_WSD_CANCEL",
    PEDK_SID_I_SCAN_OUT_TO_AIRSCAN_CANCEL: "PEDK_SID_I_SCAN_OUT_TO_AIRSCAN_CANCEL",
    PEDK_SID_E_SCAN_ADF_PAPER_OUT: "PEDK_SID_E_SCAN_ADF_PAPER_OUT",
    PEDK_SID_E_SCAN_PAPER_MISPICK_ADF_FRONT: "PEDK_SID_E_SCAN_PAPER_MISPICK_ADF_FRONT",
    PEDK_SID_E_SCAN_PAPER_MISPICK_ADF_BACK: "PEDK_SID_E_SCAN_PAPER_MISPICK_ADF_BACK",
    PEDK_SID_E_SCAN_ADF_COVER_OPEN: "PEDK_SID_E_SCAN_ADF_COVER_OPEN",
    PEDK_SID_E_SCAN_ADF_PAPER_MISMATCH: "PEDK_SID_E_SCAN_ADF_PAPER_MISMATCH",
    PEDK_SID_E_SCAN_FB_COVER_OPEN: "PEDK_SID_E_SCAN_FB_COVER_OPEN",
    PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ: "PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_ADJ",
    PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN: "PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_SCAN",
    PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT: "PEDK_SID_E_SCAN_PAPER_JAM_HEAD_NOT_REACH_EXIT",
    PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ: "PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_ADJ",
    PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN: "PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_SCAN",
    PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT: "PEDK_SID_E_SCAN_PAPER_JAM_TAIL_NOT_REACH_EXIT",
    PEDK_SID_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT: "PEDK_SID_E_SCAN_PAPER_JAM_LEAVE_ADJ_TIMEOUT",
    PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_ADJ: "PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_ADJ",
    PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_SCAN: "PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_SCAN",
    PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_EXIT: "PEDK_SID_E_SCAN_PAPER_JAM_REMAIN_EXIT",
    PEDK_SID_E_SCAN_MEMORY_LOW: "PEDK_SID_E_SCAN_MEMORY_LOW",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_21: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_21",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_22: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_22",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_23: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_23",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_24: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_24",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_25: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_25",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_26: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_26",
    PEDK_SID_E_SCAN_COMMUNICATION_ERR_27: "PEDK_SID_E_SCAN_COMMUNICATION_ERR_27",
    PEDK_SID_E_SCAN_TO_FILE_ABORTED: "PEDK_SID_E_SCAN_TO_FILE_ABORTED",
    PEDK_SID_E_SCAN_TO_FILE_PASSWORD_WRONG: "PEDK_SID_E_SCAN_TO_FILE_PASSWORD_WRONG",
    PEDK_SID_E_SCAN_TO_FILE_FILE_OVERSIZE: "PEDK_SID_E_SCAN_TO_FILE_FILE_OVERSIZE",
    PEDK_SID_E_SCAN_TO_FILE_SERVER_OVERSIZE: "PEDK_SID_E_SCAN_TO_FILE_SERVER_OVERSIZE",
    PEDK_SID_E_SCAN_TO_FILE_SEND_FAILED: "PEDK_SID_E_SCAN_TO_FILE_SEND_FAILED",
    PEDK_SID_E_SCAN_TO_FILE_VOLUME_LOW: "PEDK_SID_E_SCAN_TO_FILE_VOLUME_LOW",
    PEDK_SID_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE: "PEDK_SID_E_SCAN_TO_FILE_UDISK_SPACE_OVERSIZE",
    PEDK_SID_E_SCAN_TO_FILE_UDISK_ABORTED: "PEDK_SID_E_SCAN_TO_FILE_UDISK_ABORTED",
    PEDK_SID_E_SCAN_TO_FILE_PC_NO_RESPONSE: "PEDK_SID_E_SCAN_TO_FILE_PC_NO_RESPONSE",
    PEDK_SID_E_SCAN_EML_ESYS: "PEDK_SID_E_SCAN_EML_ESYS",
    PEDK_SID_E_SCAN_EML_EUSER: "PEDK_SID_E_SCAN_EML_EUSER",
    PEDK_SID_E_SCAN_EML_ECONN: "PEDK_SID_E_SCAN_EML_ECONN",
    PEDK_SID_E_SCAN_EML_ETRAN: "PEDK_SID_E_SCAN_EML_ETRAN",
    PEDK_SID_E_SCAN_EML_EPASS: "PEDK_SID_E_SCAN_EML_EPASS",
    PEDK_SID_E_SCAN_EML_EFROM: "PEDK_SID_E_SCAN_EML_EFROM",
    PEDK_SID_E_SCAN_EML_ETO: "PEDK_SID_E_SCAN_EML_ETO",
    PEDK_SID_E_SCAN_EML_EATT_ACCESS: "PEDK_SID_E_SCAN_EML_EATT_ACCESS",
    PEDK_SID_E_SCAN_EML_EATT_TOO_BIG: "PEDK_SID_E_SCAN_EML_EATT_TOO_BIG",
    PEDK_SID_E_SCAN_EML_ELIMIT: "PEDK_SID_E_SCAN_EML_ELIMIT",
    PEDK_SID_E_SCAN_EML_ESERVER: "PEDK_SID_E_SCAN_EML_ESERVER",
    PEDK_SID_E_SCAN_EML_MEM_LOW: "PEDK_SID_E_SCAN_EML_MEM_LOW",
    PEDK_SID_E_SCAN_SMB_SERVER_OVERSIZE: "PEDK_SID_E_SCAN_SMB_SERVER_OVERSIZE",
    PEDK_SID_E_SCAN_SMB_SENDFAIL: "PEDK_SID_E_SCAN_SMB_SENDFAIL",
    PEDK_SID_E_SCAN_SMB_DIR: "PEDK_SID_E_SCAN_SMB_DIR",
    PEDK_SID_E_SCAN_SMB_HOSTNAME: "PEDK_SID_E_SCAN_SMB_HOSTNAME",
    PEDK_SID_E_SCAN_SMB_USER_PASS: "PEDK_SID_E_SCAN_SMB_USER_PASS",
    PEDK_SID_E_SCAN_SMB_SERVER_DISCONN: "PEDK_SID_E_SCAN_SMB_SERVER_DISCONN",
    PEDK_SID_E_SCAN_SMB_NET_DISCONN: "PEDK_SID_E_SCAN_SMB_NET_DISCONN",
    PEDK_SID_E_SCAN_FTP_ESYS: "PEDK_SID_E_SCAN_FTP_ESYS",
    PEDK_SID_E_SCAN_FTP_ECONN: "PEDK_SID_E_SCAN_FTP_ECONN",
    PEDK_SID_E_SCAN_FTP_ETRAN: "PEDK_SID_E_SCAN_FTP_ETRAN",
    PEDK_SID_E_SCAN_FTP_EUSER: "PEDK_SID_E_SCAN_FTP_EUSER",
    PEDK_SID_E_SCAN_FTP_EPASS: "PEDK_SID_E_SCAN_FTP_EPASS",
    PEDK_SID_E_SCAN_FTP_EFILE_ACCESS: "PEDK_SID_E_SCAN_FTP_EFILE_ACCESS",
    PEDK_SID_E_SCAN_FTP_ESERVPATH: "PEDK_SID_E_SCAN_FTP_ESERVPATH",
    PEDK_SID_E_SCAN_FTP_ESERVER: "PEDK_SID_E_SCAN_FTP_ESERVER",
    PEDK_SID_E_SCAN_WSD_QIOERR: "PEDK_SID_E_SCAN_WSD_QIOERR",
    PEDK_SID_E_SCAN_WSD_COMM: "PEDK_SID_E_SCAN_WSD_COMM",
    PEDK_SID_E_SCAN_WSD_LOWMEM: "PEDK_SID_E_SCAN_WSD_LOWMEM",
    PEDK_SID_E_SCAN_WSD_FILE: "PEDK_SID_E_SCAN_WSD_FILE",
    PEDK_SID_E_SCAN_AIRSCAN_ESYS: "PEDK_SID_E_SCAN_AIRSCAN_ESYS",
    PEDK_SID_E_SCAN_AIRSCAN_QIOERR: "PEDK_SID_E_SCAN_AIRSCAN_QIOERR",
    PEDK_SID_E_SCAN_AIRSCAN_ECONN: "PEDK_SID_E_SCAN_AIRSCAN_ECONN",
    PEDK_SID_E_SCAN_AIRSCAN_ETRAN: "PEDK_SID_E_SCAN_AIRSCAN_ETRAN",
    PEDK_SID_E_SCAN_USB_NO_RESPONSE: "PEDK_SID_E_SCAN_USB_NO_RESPONSE",
    PEDK_SID_E_SCAN_FTP_EADRR: "PEDK_SID_E_SCAN_FTP_EADRR",
    PEDK_SID_E_SCAN_SMB_IPADDR: "PEDK_SID_E_SCAN_SMB_IPADDR",
    PEDK_SID_E_SCAN_SMB_EPORT: "PEDK_SID_E_SCAN_SMB_EPORT",
    PEDK_SID_E_SCAN_FTP_EPORT: "PEDK_SID_E_SCAN_FTP_EPORT",
    PEDK_SID_E_SCAN_ADF_BOARD_COM_HW: "PEDK_SID_E_SCAN_ADF_BOARD_COM_HW",
    PEDK_SID_E_SCAN_ADF_BOARD_COM_CRC_SERIAL: "PEDK_SID_E_SCAN_ADF_BOARD_COM_CRC_SERIAL",
    PEDK_SID_E_SCAN_ADF_BOARD_COM_CRC_TOTAL: "PEDK_SID_E_SCAN_ADF_BOARD_COM_CRC_TOTAL",
    PEDK_SID_E_SCAN_ADF_BOARD_COM_INVALID_SERIAL: "PEDK_SID_E_SCAN_ADF_BOARD_COM_INVALID_SERIAL",
    PEDK_SID_E_SCAN_ADF_BOARD_COM_INVALID_TOTAL: "PEDK_SID_E_SCAN_ADF_BOARD_COM_INVALID_TOTAL",
    PEDK_SID_F_SCAN_ADF_RELEASE_BAR_ABNORMAL: "PEDK_SID_F_SCAN_ADF_RELEASE_BAR_ABNORMAL",
    PEDK_SID_F_SCAN_ADF_MOTOR_ABNORMAL: "PEDK_SID_F_SCAN_ADF_MOTOR_ABNORMAL",
    PEDK_SID_F_SCAN_ADF_BOARD_HW_ERROR: "PEDK_SID_F_SCAN_ADF_BOARD_HW_ERROR",
    PEDK_SID_F_SCAN_ADF_BOARD_CONNECT_FAILURE: "PEDK_SID_F_SCAN_ADF_BOARD_CONNECT_FAILURE",
    PEDK_SID_F_SCAN_FB_MOTOR_ABNORMAL: "PEDK_SID_F_SCAN_FB_MOTOR_ABNORMAL",
    PEDK_SID_F_SCAN_HOME_CHECK_ERROR: "PEDK_SID_F_SCAN_HOME_CHECK_ERROR",
    PEDK_SID_F_SCAN_FINE_HOME_FAILURE: "PEDK_SID_F_SCAN_FINE_HOME_FAILURE",
    PEDK_SID_F_SCAN_FINE_BLACK_MARK_FAILURE: "PEDK_SID_F_SCAN_FINE_BLACK_MARK_FAILURE",
    PEDK_SID_F_SCAN_CIS_ABNORMAL: "PEDK_SID_F_SCAN_CIS_ABNORMAL",
    PEDK_SID_F_SCAN_CIS_DET_FAILURE: "PEDK_SID_F_SCAN_CIS_DET_FAILURE",
    PEDK_SID_F_SCAN_AFE_ABNORMAL: "PEDK_SID_F_SCAN_AFE_ABNORMAL",
    PEDK_SID_F_SCAN_AFE_CHIP_CHECK_FAILURE: "PEDK_SID_F_SCAN_AFE_CHIP_CHECK_FAILURE",
    PEDK_SID_F_SCAN_AFE_SCAN_DATA_ABNORMAL: "PEDK_SID_F_SCAN_AFE_SCAN_DATA_ABNORMAL",
    PEDK_SID_F_SCAN_CAL_AFE_OFFSET_FAILURE: "PEDK_SID_F_SCAN_CAL_AFE_OFFSET_FAILURE",
    PEDK_SID_F_SCAN_CAL_AFE_GAIN_FAILURE: "PEDK_SID_F_SCAN_CAL_AFE_GAIN_FAILURE",
    PEDK_SID_F_SCAN_CAL_AFE_EXPOSURE_FAILURE: "PEDK_SID_F_SCAN_CAL_AFE_EXPOSURE_FAILURE",
    PEDK_SID_F_SCAN_CAL_SHADING_FAILURE: "PEDK_SID_F_SCAN_CAL_SHADING_FAILURE",
    PEDK_SID_F_SCAN_CAL_MEM_UNREADY: "PEDK_SID_F_SCAN_CAL_MEM_UNREADY",
    PEDK_SID_F_SCAN_CONNECT_ENGINE_TIMEOUT: "PEDK_SID_F_SCAN_CONNECT_ENGINE_TIMEOUT",
    PEDK_SID_F_SCAN_EMMC_ABNORMAL: "PEDK_SID_F_SCAN_EMMC_ABNORMAL",
    PEDK_SID_F_SCAN_DMA_ABNORMAL: "PEDK_SID_F_SCAN_DMA_ABNORMAL",
    PEDK_SID_F_SCAN_DMA_INTERRUPT_TIMEOUT: "PEDK_SID_F_SCAN_DMA_INTERRUPT_TIMEOUT",
    PEDK_SID_I_COPY_PROCESSING: "PEDK_SID_I_COPY_PROCESSING",
    PEDK_SID_I_COPY_CANCELING: "PEDK_SID_I_COPY_CANCELING",
    PEDK_SID_I_COPY_ID_CARD_CONFIRM: "PEDK_SID_I_COPY_ID_CARD_CONFIRM",
    PEDK_SID_I_COPY_MEM_LOW: "PEDK_SID_I_COPY_MEM_LOW",
    PEDK_SID_I_COPY_SAMPLE_CONFIRM: "PEDK_SID_I_COPY_SAMPLE_CONFIRM",
    PEDK_SID_I_COPY_NEXT_ORIGINAL_CONFIRM: "PEDK_SID_I_COPY_NEXT_ORIGINAL_CONFIRM",
    PEDK_SID_I_COPY_PUT_PAPER_TO_ADF: "PEDK_SID_I_COPY_PUT_PAPER_TO_ADF",
    PEDK_SID_I_COPY_NO_RESOURCE: "PEDK_SID_I_COPY_NO_RESOURCE",
    PEDK_SID_I_COPY_CONTINUE_CONFIRM: "PEDK_SID_I_COPY_CONTINUE_CONFIRM",
    PEDK_SID_I_COPY_MIX_ORIGINAL_MISMATCH: "PEDK_SID_I_COPY_MIX_ORIGINAL_MISMATCH",
    PEDK_SID_I_COPY_COMPLETE: "PEDK_SID_I_COPY_COMPLETE",
    PEDK_SID_E_COPY_SAMPLE_TONER_EMPTY: "PEDK_SID_E_COPY_SAMPLE_TONER_EMPTY",
    PEDK_SID_I_PANEL_UPGRADE_START: "PEDK_SID_I_PANEL_UPGRADE_START",
    PEDK_SID_F_PANEL_COMMUNICATION_ERROR: "PEDK_SID_F_PANEL_COMMUNICATION_ERROR",
    PEDK_SID_I_USB_UDISK_INSERT: "PEDK_SID_I_USB_UDISK_INSERT",
    PEDK_SID_I_USB_UDISK_EXTRACT: "PEDK_SID_I_USB_UDISK_EXTRACT",
    PEDK_SID_E_USB_UDISK_MISTAKE_FORMAT: "PEDK_SID_E_USB_UDISK_MISTAKE_FORMAT",
    PEDK_SID_E_USB_UDISK_FAILURE: "PEDK_SID_E_USB_UDISK_FAILURE",
    PEDK_SID_F_USB_UDISK_OVERCURRENT: "PEDK_SID_F_USB_UDISK_OVERCURRENT",
    PEDK_SID_I_NET_WIFI_STA_CONNECTING: "PEDK_SID_I_NET_WIFI_STA_CONNECTING",
    PEDK_SID_I_NET_WIFI_WPS_PBC: "PEDK_SID_I_NET_WIFI_WPS_PBC",
    PEDK_SID_I_NET_WIFI_WPS_PIN: "PEDK_SID_I_NET_WIFI_WPS_PIN",
    PEDK_SID_I_NET_WIFI_WPS_CANCEL: "PEDK_SID_I_NET_WIFI_WPS_CANCEL",
    PEDK_SID_I_NET_WIFI_WPS_SUCCESS: "PEDK_SID_I_NET_WIFI_WPS_SUCCESS",
    PEDK_SID_I_NET_WIFI_CONNECT_SUCCESS: "PEDK_SID_I_NET_WIFI_CONNECT_SUCCESS",
    PEDK_SID_I_NET_WIFI_WFD_CONNECT_REQUEST: "PEDK_SID_I_NET_WIFI_WFD_CONNECT_REQUEST",
    PEDK_SID_I_NET_SMTP_TEST_SUCCESS: "PEDK_SID_I_NET_SMTP_TEST_SUCCESS",
    PEDK_SID_I_NET_AIRPRINT_IDENTIFY_ACTION: "PEDK_SID_I_NET_AIRPRINT_IDENTIFY_ACTION",
    PEDK_SID_E_NET_WIFI_CONNECT_TIMEOUT: "PEDK_SID_E_NET_WIFI_CONNECT_TIMEOUT",
    PEDK_SID_E_NET_WIFI_CONNECT_NO_SSID: "PEDK_SID_E_NET_WIFI_CONNECT_NO_SSID",
    PEDK_SID_E_NET_WIFI_CONNECT_ERR_PSK: "PEDK_SID_E_NET_WIFI_CONNECT_ERR_PSK",
    PEDK_SID_E_NET_WIFI_CONNECT_FAIL: "PEDK_SID_E_NET_WIFI_CONNECT_FAIL",
    PEDK_SID_E_NET_WIFI_CONNECT_NO_RECORD: "PEDK_SID_E_NET_WIFI_CONNECT_NO_RECORD",
    PEDK_SID_E_NET_WIFI_DISCONNECT: "PEDK_SID_E_NET_WIFI_DISCONNECT",
    PEDK_SID_E_NET_SMTP_TEST_FAIL: "PEDK_SID_E_NET_SMTP_TEST_FAIL",
    PEDK_SID_F_NET_WIFI_FATAL_ERROR: "PEDK_SID_F_NET_WIFI_FATAL_ERROR",
    PEDK_SID_I_FRAMEWORK_SUSPEND: "PEDK_SID_I_FRAMEWORK_SUSPEND",
    PEDK_SID_I_FRAMEWORK_RESUME: "PEDK_SID_I_FRAMEWORK_RESUME",
    PEDK_SID_I_FRAMEWORK_REQUEST_NEXT_PAGE: "PEDK_SID_I_FRAMEWORK_REQUEST_NEXT_PAGE",
    PEDK_SID_I_FRAMEWORK_JOB_INFO_UPDATE: "PEDK_SID_I_FRAMEWORK_JOB_INFO_UPDATE",
    PEDK_SID_I_FRAMEWORK_PRINTING_IN_JOB_START: "PEDK_SID_I_FRAMEWORK_PRINTING_IN_JOB_START",
    PEDK_SID_I_FRAMEWORK_PRINTING_IN_JOB_FINISH: "PEDK_SID_I_FRAMEWORK_PRINTING_IN_JOB_FINISH",
    PEDK_SID_I_FRAMEWORK_RESUME_FAILED: "PEDK_SID_I_FRAMEWORK_RESUME_FAILED",
    PEDK_SID_E_COMMON_DATA_RECEIVE_TIMEOUT: "PEDK_SID_E_COMMON_DATA_RECEIVE_TIMEOUT",
    PEDK_SID_E_COMMON_DATA_PASER_FAILED: "PEDK_SID_E_COMMON_DATA_PASER_FAILED",
    PEDK_SID_E_FWUPDATE_UNACTIVE: "PEDK_SID_E_FWUPDATE_UNACTIVE",
    PEDK_SID_I_FWUPDATE_CONFIRM_UPGRADE: "PEDK_SID_I_FWUPDATE_CONFIRM_UPGRADE",
    PEDK_SID_I_DATA_DELETING: "PEDK_SID_I_DATA_DELETING",
    PEDK_SID_INVALID: "PEDK_SID_INVALID",
}

/**
 * @namespace
 * @property {String} STATUS_ID_TYPE_INFO Information
 * @property {String} STATUS_ID_TYPE_WARN Warning
 * @property {String} STATUS_ID_TYPE_ERROR Error
 * @property {String} STATUS_ID_TYPE_FATAL Fatal
 */
const STATUS_TYPE = {
    STATUS_ID_TYPE_INFO: "STATUS_ID_TYPE_INFO",
    STATUS_ID_TYPE_WARN: "STATUS_ID_TYPE_WARN",
    STATUS_ID_TYPE_ERROR: "STATUS_ID_TYPE_ERROR",
    STATUS_ID_TYPE_FATAL: "STATUS_ID_TYPE_FATAL",
}

/**
 * @namespace
 * @property {String} eSTATUS_PRI_MAX Highest
 * @property {String} eSTATUS_PRI_FATAL Fatal Error
 * @property {String} eSTATUS_PRI_INFO Information
 * @property {String} eSTATUS_PRI_CONFIRM User Confirmation
 * @property {String} eSTATUS_PRI_ERROR Error
 * @property {String} eSTATUS_PRI_ERROR_LOW Cover Open Error
 * @property {String} eSTATUS_PRI_JOB Job Status
 * @property {String} eSTATUS_PRI_WARNING Warning
 * @property {String} eSTATUS_PRI_NORMAL Normal Screen
 * @property {String} eSTATUS_PRI_MIN Lowest
 */
const STATUS_PRIORITY = {
    eSTATUS_PRI_MAX: "eSTATUS_PRI_MAX",
    eSTATUS_PRI_FATAL: "eSTATUS_PRI_FATAL",
    eSTATUS_PRI_INFO: "eSTATUS_PRI_INFO",
    eSTATUS_PRI_CONFIRM: "eSTATUS_PRI_CONFIRM",
    eSTATUS_PRI_ERROR: "eSTATUS_PRI_ERROR",
    eSTATUS_PRI_ERROR_LOW: "eSTATUS_PRI_ERROR_LOW",
    eSTATUS_PRI_JOB: "eSTATUS_PRI_JOB",
    eSTATUS_PRI_WARNING: "eSTATUS_PRI_WARNING",
    eSTATUS_PRI_NORMAL: "eSTATUS_PRI_NORMAL",
    eSTATUS_PRI_MIN: "eSTATUS_PRI_MIN",
}

/**
 * @namespace
 * @property {String} MODULE_PRINT Printing
 * @property {String} MODULE_SCAN Scanning
 * @property {String} MODULE_COPY Copying
 * @property {String} MODULE_FAX Fax
 * @property {String} MODULE_PANEL Panel
 * @property {String} MODULE_NET Network
 * @property {String} MODULE_USB USB
 * @property {String} MODULE_PLATFORM Framework
 * @property {String} MODULE_COMMON Common
 * @property {String} MODULE_FWUPDATE Upgrade
 * @property {String} MODULE_POWERMGR Power Management
 */
const STATUS_MOUDLE = {
    MODULE_PRINT: "MODULE_PRINT",
    MODULE_SCAN: "MODULE_SCAN",
    MODULE_COPY: "MODULE_COPY",
    MODULE_FAX: "MODULE_FAX",
    MODULE_PANEL: "MODULE_PANEL",
    MODULE_NET: "MODULE_NET",
    MODULE_USB: "MODULE_USB",
    MODULE_PLATFORM: "MODULE_PLATFORM",
    MODULE_COMMON: "MODULE_COMMON",
    MODULE_FWUPDATE: "MODULE_FWUPDATE",
    MODULE_POWERMGR: "MODULE_POWERMGR",
}



 /**common/tray.js
 *| 纸盒 |值| 4020 | kanas |
 *|:--:|:--:|:--:|
 *|多功能纸盒|1|支持|支持|
 *|标准纸盒|2|支持|支持|
 *|选配纸盒1|3|支持|支持|
 *|选配纸盒2|4|支持|支持|
 *|选配纸盒3|5|支持|支持|
 *|选配纸盒4|6|支持|支持|
 *|选配纸盒5|7|支持|支持|
 * @class
 */

const TRAY_TYPE = {
  	AUTO_SELECTION_TRAY: "AUTO_SELECTION_TRAY",
  	MULTI_FUNCTION_TRAY: "MULTI_FUNCTION_TRAY",
    STANDAR_TRAY: "STANDAR_TRAY",
    OPTION_TRAY1: "OPTION_TRAY1",
    OPTION_TRAY2: "OPTION_TRAY2",
    OPTION_TRAY3: "OPTION_TRAY3",
    OPTION_TRAY4: "OPTION_TRAY4",
    OPTION_TRAY5: "OPTION_TRAY5",
    EXTERNAL_HIGH_CAPACITY_TRAY: "EXTERNAL_HIGH_CAPACITY_TRAY",
    INSTALL_HIGH_CAPACITY_TRAY: "INSTALL_HIGH_CAPACITY_TRAY",
}



const SPECIAL_SCAN_SIZE = {
    SCAN_FULL_PLATEN: "SCAN_FULL_PLATEN",
    AUTO_PAPER_CHECK: "AUTO_PAPER_CHECK",

}

const TONER_TYPE = {
	TONER_TYPE_CYAN		: 'TONER_TYPE_CYAN',
	TONER_TYPE_MAGENTA	: 'TONER_TYPE_MAGENTA',
	TONER_TYPE_YELLOW	: 'TONER_TYPE_YELLOW',
	TONER_TYPE_KEY		: 'TONER_TYPE_KEY',

}

/*
JBSts_Init	初始化
JBSts_PrePare	预解析
JBSts_Ready	准备就绪
JBSts_Running	运行中
JBSts_Suspend	挂起
JBSts_Pause	暂停
JBSts_Cancelling	取消中
JBSts_Aborting	异常终止
JBSts_Finish	停止
*/
const JOB_STATE_TYPE = {
    JBSts_Init      : "JBSts_Init",
    JBSts_PrePare   : "JBSts_PrePare",
    JBSts_Ready     : "JBSts_Ready",
    JBSts_Running   : "JBSts_Running",
    JBSts_Suspend   : "JBSts_Suspend",
    JBSts_Pause     : "JBSts_Pause",
    JBSts_Cancelling: "JBSts_Cancelling",
    JBSts_Aborting  : "JBSts_Aborting",
    JBSts_Finish    : "JBSts_Finish",
    JBSts_History   : "JBSts_History",
}

const NATIVE_WINDOW = 
{
    NATIVE_WINDOW_COPY              : "NATIVE_WINDOW_COPY",             ///< Copy main menu window
    NATIVE_WINDOW_IDCOPY            : "NATIVE_WINDOW_IDCOPY",           ///< ID Copy main menu window
    NATIVE_WINDOW_BILL_COPY         : "NATIVE_WINDOW_BILL_COPY",        ///< Bill Copy main menu window
    NATIVE_WINDOW_SCAN              : "NATIVE_WINDOW_SCAN",             ///< Scan main menu window
    NATIVE_WINDOW_PRINT_FROM_USB    : "NATIVE_WINDOW_PRINT_FROM_USB",   ///< Print from Usb memory menu window
    NATIVE_WINDOW_JOB_LIST          : "NATIVE_WINDOW_JOB_LIST",         ///< Job list window
    NATIVE_WINDOW_STATUS_LIST       : "NATIVE_WINDOW_STATUS_LIST",      ///< Status list window
    NATIVE_WINDOW_USB_DRIVE_PRINT   : "NATIVE_WINDOW_USB_DRIVE_PRINT",  ///< Print from USB drive menu window
}


globalThis.pedk.common = {}
globalThis.pedk.common.FileEntry = FileEntry
globalThis.pedk.common.ERROR_NO = ERROR_NO
globalThis.pedk.common.MEDIA_SIZE = MEDIA_SIZE
globalThis.pedk.common.MEDIA_TYPE = MEDIA_TYPE
globalThis.pedk.common.PEDK_STATUS_ID = PEDK_STATUS_ID
globalThis.pedk.common.STATUS_TYPE = STATUS_TYPE
globalThis.pedk.common.STATUS_PRIORITY = STATUS_PRIORITY
globalThis.pedk.common.STATUS_MOUDLE = STATUS_MOUDLE
globalThis.pedk.common.TRAY_TYPE = TRAY_TYPE
globalThis.pedk.common.SPECIAL_SCAN_SIZE = SPECIAL_SCAN_SIZE
globalThis.pedk.common.TONER_TYPE = TONER_TYPE
globalThis.pedk.common.JOB_STATE_TYPE = JOB_STATE_TYPE
globalThis.pedk.common.NATIVE_WINDOW = NATIVE_WINDOW





