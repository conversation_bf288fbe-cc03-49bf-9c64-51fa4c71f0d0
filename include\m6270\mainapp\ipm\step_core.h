/**
 * @copyright  2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       stpe_core.h
 * @addtogroup ipm
 * @{
 * @addtogroup image process core submodule
 * <AUTHOR> (<EMAIL>)
 * @date       2021-10-29
 * @version    v1.0
 * @details    this is step layout API
 */

#ifndef STEP_CORE_H
#define STEP_CORE_H

#ifdef __cplusplus
extern "C" {
#endif /*__cplusplus */

#include <stdio.h>
#include "memmgr/memmgr.h"
#include "pipe_core.h"
#include "public_data_proc.h"

#define MAX_IP_STEP_NAME    64  ///< max ip step name
#define MAX_IP_STEP_PARM    512 ///< max ip step param

typedef struct tag_ip_step IP_STEP_S,*IP_STEP_P;
struct tag_ip_pipe;

/**
 * @brief oob status of ip step
 */
typedef enum
{
    ssOK,       ///< OK
    ssFAILED,   ///< Error, stop the pipe
    ssBUSY,     ///< Busy
    ssEOI,      ///< End of Image (page)
    ssEOJ,      ///< End of Pipeline (in only)
}IP_STEP_STATUS_E;

/**
 * @brief IP_STEP Open interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] parms  STEP params
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef int32_t         (*STEP_OPEN)        (IP_STEP_P pstep, char* parms);

/**
 * @brief IP_STEP CLOSE interface api function types
 * @param[in] IP_STEP_P STEP object
 * @return void \n
 * @retval null
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef void            (*STEP_CLOSE)       (IP_STEP_P pstep);

/**
 * @brief IP_STEP PENDING interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] readable step readable
 * @param[in] writeable step writeable
 * @param[in] status_in step status_in
 * @param[in] status step status
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef int32_t         (*STEP_PENDING)     (
                                            IP_STEP_P pstep,
                                            int32_t* readable,
                                            int32_t* writeable,
                                            IP_STEP_STATUS_E  status_in,
                                            IP_STEP_STATUS_E* status
                                            );
/**
 * @brief IP_STEP READ interface api function types
 * @param[in] IP_STEP_P STEP object
 * @return IMAGE_P \n
 * @retval image object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef IMAGE_P          (*STEP_READ)        (IP_STEP_P pstep);

/**
 * @brief IP_STEP WRITE interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] image image object
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef int32_t         (*STEP_WRITE)       (IP_STEP_P pstep, IMAGE_P image);

/**
 * @brief IP_STEP INTERNAL_INFOR interface api function types
 * @param[in] IP_STEP_P STEP object
 * @param[in] parm_cnt param count
 * @param[in] parm_list param list
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
typedef int32_t         (*INTERNAL_INFOR)       (IP_STEP_P pstep,int parm_cnt,char **parm_list);

/**
 * @brief abstract interface which implements an ip step
 */
typedef struct tag_step_interface
{
    STEP_OPEN       Open;       ///< open api function
    STEP_CLOSE      Close;      ///< close api function
    STEP_PENDING    Pending;    ///< pending api function
    STEP_READ       Read;       ///< read api function
    STEP_WRITE      Write;      ///< write api function
}IP_STEP_INTERFACE_S, *IP_STEP_INTERFACE_P;


/**
 * @brief step common param
 */
typedef struct tag_step_common_param
{
    uint32_t            cookie;                                 ///< A magic word to check struct memory is ok

    PRINT_MODE_E        print_mode;                             ///< duplex print mode:0,single;1,auto duplex;2,manual duplex
    PAPER_SIZE_E        paper_size;                             ///< pantum has 27 ones
    PAPER_TYPE_E        paper_type;                             ///< pantum has 7 ones
    PAGE_TYPE_E         page_type;                            ///< front,back
    uint16_t            paper_width;                            ///< user define paper info pixel
    uint16_t            paper_height;                           ///< user define paper info pixel
    PRINT_DENSITY_E     density;                                ///< image black degree,0,1,2,3,4
    TRAY_INPUT_E        tray_in;                                ///< the tray which engine pick from
    TRAY_INPUT_E        tray_in_original;                       ///< the tray which come from user's chooice --- Add by Zhu.Ran.zhu, 20191115
    int                 top_margin;                             ///< top margin
    int                 left_margin;                            ///< left margin
    COPY_MARGIN_INFO_S  copy_pad_info;                          ///< 6220 pip pad info
    ORIGINAL_ORIENTATION_E original_orientation;                ///< original orientation
    MODULE_ID_E         page_source;
    //MARGINE_DATA        margin[MAX_IMAGE_COMPONENTS];         ///< margin left and top set
    uint32_t            copies;                                 ///< copies,this page need how many copies
    INSERT_PAGE_E       insert_page;                            ///< is it a empty page，used at separator_page

    //print job mgr use
    uint32_t            page_number;                            ///< print job mgr will add up page counter
    //uint32_t            renderg2;      	                        ///< render model for 600 or 600 2 bit
    uint32_t            fine_field;                             ///< fine mode: 0 - off; 1 - on
    uint32_t            paper_mismatch;                         ///< paper_mismatch:0 - off;1 - on
    uint32_t            page_cmpbytestot;                       ///< compressed page data-bytes
    uint32_t            page_id;                                ///< print page id number
    uint32_t            rotation;                               ///< 0->do nothing; 1->rotate 180 degrees
    STAPLE_CONFIG_S     staple;                                 ///< staple config from driver
    SHIFT_MODE_E        shift_mode;                             ///< print shift mode
    FLIP_MODE_E         copies_flip;                            ///< staple config from driver
    uint32_t            shadow_page;                            ///< shadow page for cross offset print
    uint32_t            pip_mode;                               ///< for mix color airprint job use
    uint32_t            rotate_angle;                           ///< used for print rotate
}STEP_COMMON_PARAM_S, *STEP_COMMON_PARAM_P;

/**
 * @brief step Object
 */
/*typedef */struct tag_ip_step
{
    char                    step_name [MAX_IP_STEP_NAME];   ///< name of step
    char                    step_parms[MAX_IP_STEP_PARM];   ///< parms used to open step

    struct tag_ip_pipe       *pipe;                          ///< pipeline step belongs to, if any

    IP_STEP_INTERFACE_P      step;                           ///< the actual interface

    void                      *context;                       ///< opaque (step specific) step context
    STEP_COMMON_PARAM_S       step_comm_parms_in;
    STEP_COMMON_PARAM_S       step_comm_parms_out;
    /** images for convienience.  each step has 2 builtin images (in, out)
     *  that are opened at open time and closed at closed time by the
     *  "base-class" step functions if called (see step/stepcommon.h)
     *  since this is such a common thing for steps to do and use.
     */
    IMAGE_S                   imgin;                          ///< input image
    IMAGE_S                   imgout;                         ///< output image

     int                    eoi_pending;                    ///< eoi_pending flag
    int                     eoj_pending;                    ///< eoj_pending flag
    int                     eoi_flagged;                    ///< eoi_flagged
    int                     eoj_flagged;                    ///< eoj_flagged
    int                     first_write;                    ///< first write

    //infor
    int                     from_readable;                  ///< pre step readable
    int                     from_status;                    ///< pre step status
    int                     to_readable;                    ///< next step readable
    int                     to_writeable;                   ///< next step writeble
    int                     to_status;                      ///< next step status

    INTERNAL_INFOR          information;                    ///< interface information
    int                     is_wake;                            ///< pipe is wake
    struct tag_ip_step       *next;                          ///< chain link of steps
    struct tag_ip_step       *prev;                          ///< both way chain
};//IP_STEP_S, *IP_STEP_P;

/**************************************************************
/// Description of a parameter to a step. Parameters are passed to the
/// step in Open as a string.  For parameters that are simple integers
/// (many are) the min/max/defval describe the range.  For string parameters
/// min = max = 0.  The parm descriptions are always *always* constant
/// arrays of IPSTEPPARM with a sentinal index with parm_name == NULL
****************************************************************/

/**
 * @brief Description of a parameter to a step
 */
typedef struct tag_stepparm
{
    const char*     parm_name;          ///< name of parameter
    const char*     parm_description;   ///< what its for and how its used (optional)
    int32_t         min_val;             ///< minimum value for parm
    int32_t         max_val;             ///< maximum value
}IP_STEP_PARM_S, *IP_STEP_PARM_P;


/**
 * @brief register step
 * @param[in] name STEP name
 * @param[in] step_interface step api function(open/close/pending...)
 * @param[in] description step description
 * @param[in] parm_descriptions step parma descriptions
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t step_core_register_step(char* name,
                    IP_STEP_INTERFACE_P step_interface,
                    const char* description,
                    IP_STEP_PARM_P parm_descriptions);

/**
 * @brief step action pipeline
 * @param[in] step STEP object
 * @return void \n
 * @retval null
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void step_action_pipeline(IP_STEP_P step);

/**
 * @brief create the object of step
 * @param[in] name step name
 * @return IP_STEP_P \n
 * @retval STEP object
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
IP_STEP_P step_core_create_step(char* name);

/**
 * @brief desroy step
 * @param[in] step step object
 * @return void \n
 * @retval null
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
void step_core_detroy_step(IP_STEP_P step);

/**
 * @brief step_core_prolog
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t step_core_prolog();

/**
 * @brief step_core_epilog
 * @return int32_t \n
 * @retval FAIL / SUCCESS
 * <AUTHOR> (<EMAIL>)
 * @date 2021-10-29
 */
int32_t step_core_epilog();

#ifdef __cplusplus
}
#endif /*__cplusplus */

#endif /* STEP_CORE_H */

/**
 *@}
 */


