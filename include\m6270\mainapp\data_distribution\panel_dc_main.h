/**
 * @copyright 2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file panel_dc_main.h
 * @addtogroup panel_dc
 * @{
 * <AUTHOR>
 * @date 2023-06-09
 * @brief the panel agency program entry in dc
 */

#ifndef _PANEL_MAIN_H
#define _PANEL_MAIN_H

#include <pol/pol_types.h>

/**
 * @brief start panel register panel client and create sys msg recv thread
 * @return true or false
 * @author: madechang
 */
int32_t panel_prolog( void );

/**
* @brief create a thread to recv msg from sys_msg_mgr
* @author: madechang
*/
void* panel_sys_msg_thread(void* arg);

/**
 * @brief notify panel that dc is ready,it can enter the home window
 * @author: madechang
 */
void panel_notify_ready(void    );

/**
* @brief panel init data when wakeup from deep sleep
* @author: madechang
*/
void panel_data_init( int data );



#endif /* _PANEL_MAIN_H */

/**
 *@}
 */

