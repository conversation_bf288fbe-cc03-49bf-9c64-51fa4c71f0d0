/**
 * @copyright  2023 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file       dependent_table.h
 * @addtogroup copy system
 * @{
 * @addtogroup copy system
 * <AUTHOR>
 * @date       2023-05-29
 * @version    v1.0
 * @details    copy system entry
 */
#ifndef __COPY_SYSTEM_INTERFACE_H__
#define __COPY_SYSTEM_INTERFACE_H__

#define     pi_copy_system_prolog         init_copy_system

/**
* @brief get version of interface
* @return the pointer of version
* @autor liushaoxi
* @date 2023-05-29
*/
char *copy_system_get_interface_version();

/**
* @brief initialize the copy system
* @return -1 : Error  0 : Success
* @autor liushaoxi
* @date 2023-05-29
*/
int init_copy_system(void);

#endif
/**
 *@}
 */
