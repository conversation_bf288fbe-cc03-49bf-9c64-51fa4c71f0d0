/**
* @copyright 2024 Shenzhen Pantum Technology Co.Ltd all rights reserved
* @file job_pedkapi.c
* @addtogroup pedk jobctl
* @{
* @addtogroup pedk jobctl
* <AUTHOR>
* @date 2024-06-03
* @version v0.1
* @brief pedk jobctl
*
*/

#include <stdio.h>
#include "cjson/cJSON.h"
#include "channels.h"
#include "ulog.h"
#include "pedk_mgr.h"
#include "job_common.h"
#include "job_manager.h"
#include "job_pedkapi.h"
#include "platform_api.h"

#define JOBCTL_UNUSED(var) ((void)var)
#define JOBCTL_ITEM(arr)   (sizeof(arr) / sizeof(arr[0]))
#define JOBCTL_STR_SIZE    (32)

typedef void (*PEDK_JOBCTL_SUB_HANDLE_F)(SUB_MSG_E, int32_t, int32_t, uint8_t *, void *);

struct 
{
    int  mfp_status;
    char pedk_status[JOBCTL_STR_SIZE];
} s_job_status_map[] = 
{
    { JOB_WAITING,   "JBSts_Init"        },
    { JOB_RUNNING,   "JBSts_Running"     },    
    { JOB_CANCELING, "JBSts_Cancelling"  },
    { JOB_SUSPENDED, "JBSts_Suspend"     },
    { JOB_PAUSING,   "JBSts_Pause"       },
    { JOB_FINISHED,  "JBSts_Finish"      },
};

struct 
{
    int  mfp_type;
    char pedk_type[JOBCTL_STR_SIZE];
} s_job_type_map[] = 
{
    { PRINT_OBJ, "PRINT_NORMAL" },
    { SCAN_OBJ,  "SCAN_TO_PC"   },
    { COPY_OBJ,  "COPY_NORAML"  },
};

static char *pedk_job_status_mapping(int state)
{
    int i = 0;

    for (i = 0; i < JOBCTL_ITEM(s_job_status_map); i++)
    {
        if (state == s_job_status_map[i].mfp_status)
        {
            return s_job_status_map[i].pedk_status;
        }
    }

    return "unkown";
}

static char *pedk_job_type_mapping(int type)
{
    int i = 0;

    for (i = 0; i < JOBCTL_ITEM(s_job_type_map); i++)
    {
        if (type == s_job_type_map[i].mfp_type)
        {
            return s_job_type_map[i].pedk_type;
        }
    }

    return "unkown";
}

static void* alloc_job(void *data)
{
    JOBINFO_S *job_src   = (JOBINFO_S*)data;
    JOBINFO_S *job_alloc = NULL;
    
    if (job_src)
    {
        job_alloc = (JOBINFO_S *)malloc(sizeof(JOBINFO_S));
        if (job_alloc)
        {
            memcpy(job_alloc, job_src, sizeof(JOBINFO_S));
        }

        return job_alloc;
    }

    return job_src;
}

static void free_job(void *data)
{
    free(data);
}

static void pedk_job_entry_callback(const JOBINFO_S *job_info, void *context)
{
    if (unlikely(!job_info || !context))
    {
        return ;
    }

    struct channels *chs = (struct channels*)context;

    channels_tail_insert(chs, job_info->job_id, (void*)job_info, 0);
}

static void pedk_one_job_encap_json(cJSON *json_arr, JOBINFO_S *job)
{
    cJSON *json_obj = NULL;

    if (unlikely(!json_arr || !job))
    {
        return ;
    }

    json_obj = cJSON_CreateObject();
    
    if (unlikely(!json_obj))
    {
        ULOG_WARN(SYS_JOB_LOG, "Create json object failed!\n");
        return ;
    }
    
    cJSON_AddItemToArray(json_arr, json_obj);
    // 添加键-值的顺序与jobctl_api.js文件中JOBINFO_S类的成员变量顺序保持一致
    cJSON_AddNumberToObject(json_obj, "id",        job->job_id);
    cJSON_AddStringToObject(json_obj, "type",      pedk_job_type_mapping(job->obj));
    cJSON_AddStringToObject(json_obj, "state",     pedk_job_status_mapping(job->status));
    cJSON_AddStringToObject(json_obj, "user_name", "");
    cJSON_AddStringToObject(json_obj, "job_name",  "");
    cJSON_AddNumberToObject(json_obj, "time",      job->timestamp);    
    cJSON_AddNumberToObject(json_obj, "t_pages",   job->total_pages);    
    cJSON_AddNumberToObject(json_obj, "s_pages",   (SCAN_OBJ == job->obj) ? job->current_pages : 0); 
    cJSON_AddNumberToObject(json_obj, "p_pages",   (PRINT_OBJ == job->obj) ? job->current_pages : 0);
    cJSON_AddNumberToObject(json_obj, "r_pages",   job->total_pages);
    cJSON_AddNumberToObject(json_obj, "c_pages",   (COPY_OBJ == job->obj) ? job->current_pages : 0);
    cJSON_AddNumberToObject(json_obj, "wonum",     job->wonum);
}

static char *pedk_jobinfo_encap(JOBINFO_S *job)
{
    char  *job_str  = NULL;    
    cJSON *json_arr = NULL;
    struct channels chs;

    json_arr = cJSON_CreateArray();
    
    if (unlikely(!json_arr))
    {
        ULOG_WARN(SYS_JOB_LOG, "Create json array failed!\n");
        return NULL;
    }

    if (job)
    {        
        /* one job */
    
        pedk_one_job_encap_json(json_arr, job);
    }
    else
    {        
        /* all job */
    
        channels_init(&chs, alloc_job, free_job);

        if (0 == job_manager_get_active_joblist(&chs, pedk_job_entry_callback))
        {
            cJSON_Delete(json_arr);
            channels_destroy(&chs);
            return NULL;
        }

        while ((job = (JOBINFO_S *)channels_head_pop(&chs)))
        {
            pedk_one_job_encap_json(json_arr, job);
            free_job(job);
        }
        
        channels_destroy(&chs);
    }

    job_str = cJSON_PrintUnformatted(json_arr);
    
    cJSON_Delete(json_arr);

    ULOG_INFO(SYS_JOB_LOG,  "System Job list = %s\n", job_str);

    return job_str;
}

static void pedk_one_job_cancel_req(uint32_t job_id)
{
    ROUTER_MSG_S t_Msg;

    t_Msg.msgType   = MSG_CTRL_JOB_CANCEL;
    t_Msg.msg1      = job_id;
    t_Msg.msg2      = 0;
    t_Msg.msg3      = NULL;
    t_Msg.msgSender = MID_PANEL;

    if (0 != task_msg_send_by_router(MID_SYS_JOB_MGR, &t_Msg))
    {
        ULOG_WARN(SYS_JOB_LOG, "Send cancel msg fail job id!\n", job_id);
    }
    else
    {
        ULOG_INFO(SYS_JOB_LOG, "Send cancel msg SUCCEED job id %u!\n", job_id);
    }
}

static int pedk_job_cancel(uint32_t job_id)
{
    int        ret        = FALSE;
    JOBINFO_S *p_job_info = NULL;    
    JOBINFO_S  job_info;
    struct channels chs;

    if (job_id)
    {        
        /* cancel one job */
    
        ret = job_manager_find_job(job_id, &job_info, 0);
        
        if (0 != ret)
        {
            ULOG_WARN(SYS_JOB_LOG, "job id [%d] nonexistent\n", job_id);
            return FALSE;
        }
        
        pedk_one_job_cancel_req(job_id);

        ret = TRUE;
    }
    else
    {        
        /* cancel all job */
    
        channels_init(&chs, alloc_job, free_job);

        job_manager_get_active_joblist(&chs, pedk_job_entry_callback);

        while ((p_job_info = (JOBINFO_S*)channels_head_pop(&chs)))
        {
             pedk_one_job_cancel_req(p_job_info->job_id);
             free_job(p_job_info);
             ret = TRUE;
        }
        
        channels_destroy(&chs);
    }

    return ret;
}

static void pedk_jobctl_list(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    char *job_list     = NULL;
    int   job_list_len = 0;

    JOBCTL_UNUSED(buf_size);
    JOBCTL_UNUSED(buf);
    JOBCTL_UNUSED(ctx);

    job_list = pedk_jobinfo_encap(NULL);
    
    if (job_list)
    {
        job_list_len = strlen(job_list);
    }
    else 
    {
        job_list_len = 0;
    }

    pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, 0, (unsigned char*)job_list, job_list_len);

    if (job_list)
    {
        free(job_list); 
    }
}

static void pedk_jobctl_cancel(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    int ret = 0;

    JOBCTL_UNUSED(buf_size);
    JOBCTL_UNUSED(buf);
    JOBCTL_UNUSED(ctx);

    ret = pedk_job_cancel(respond);

    pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, ret, NULL, 0);
}

static void pedk_jobctl_apply_id(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    #if 0
    uint32_t job_id = 0;

    JOBCTL_UNUSED(buf_size);
    JOBCTL_UNUSED(buf);
    JOBCTL_UNUSED(ctx);

    job_id = job_manager_apply_jobid(0);
    
    pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, (int32_t)job_id, NULL, 0);
    #endif
}

static int pedk_fill_one_history(const JOBINFO_S *job, cJSON *arr, uint32_t size)
{    
    char      buf[64];
    cJSON    *obj;
    cJSON    *param_obj = NULL;
    struct tm t2t;
    JOB_HISTORY_S job_history;
    
    if (job->obj == 0 || job->obj > FILE_OBJ)
    {
        return -1;
    }
    
    obj = cJSON_CreateObject();
    if (!obj)
    {
        return -1;
    }

    /* JOBINFO_S */
    memset(buf , 0 , sizeof(buf));
    snprintf(buf , sizeof(buf) , "%u" , job->job_id);
    cJSON_AddStringToObject(obj, "job_id" , buf);

    cJSON_AddStringToObject(obj, "user_name" , "Admin");
    if (job->obj == PRINT_OBJ || job->obj == FILE_OBJ)
    {
        cJSON_AddStringToObject(obj, "type", "JOB_HISTORY_TYPE_PRINT");        
        cJSON_AddStringToObject(obj, "filename" , (char*)job->job_name);
    }
    else if (job->obj == SCAN_OBJ)
    {
        cJSON_AddStringToObject(obj, "type", "JOB_HISTORY_TYPE_SCAN");
        cJSON_AddStringToObject(obj, "filename" , "-");
    }
    else if (job->obj == COPY_OBJ)
    {
        cJSON_AddStringToObject(obj, "type", "JOB_HISTORY_TYPE_COPY");
        cJSON_AddStringToObject(obj, "filename" , "-");
    }
    if (job->status == JOB_FINISHED && job->status_detail == JOB_END)
    {
        cJSON_AddStringToObject(obj, "status", "JOB_HISTORY_STATUS_COMPLETED");
    }
    else
    {
        cJSON_AddStringToObject(obj, "status", "JOB_HISTORY_STATUS_CANCELLED");
    }

    localtime_r((time_t*)&job->timestamp , &t2t);
    memset(buf , 0 , sizeof(buf));
    snprintf(buf , sizeof(buf) , "%04d-%02d-%02d %02d:%02d:%02d" , 
             t2t.tm_year+1900,t2t.tm_mon+1,t2t.tm_mday,t2t.tm_hour,t2t.tm_min ,t2t.tm_sec);
    cJSON_AddStringToObject(obj, "start_time", buf);
    
    /* JOB_PARAM_S */    
    memset(&job_history, 0, sizeof(job_history));
    
    if (sizeof(JOB_HISTORY_S) == size)
    {
        memcpy(&job_history, job, sizeof(JOB_HISTORY_S));

        param_obj = (COPY_OBJ  == job->obj) ? cJSON_Parse(job_history.job_params.copy.param_str)  :
                    (PRINT_OBJ == job->obj) ? cJSON_Parse(job_history.job_params.print.param_str) :
                    (SCAN_OBJ  == job->obj) ? cJSON_Parse(job_history.job_params.scan.param_str)  : cJSON_Parse("{}");
    }
    else 
    {
        param_obj = cJSON_Parse("{}");
    }

    if (!param_obj)
    {
        param_obj = cJSON_Parse("{}");
        ULOG_WARN(SYS_JOB_LOG , "failed to parse job param\n");
    }

    cJSON_AddItemToObject(obj, "param", param_obj);
    
    cJSON_AddItemToArray(arr, obj);

    return 0;
}

static int pedk_fill_history_list(const JOBINFO_S *job , void *context, uint32_t size)
{
    cJSON *arr = (cJSON*)context;

    if (cJSON_GetArraySize(arr) >= 30)
        return 1;

    if (pedk_fill_one_history(job, arr, size))
    {
        return 0;
    }

    return 0;
}

static int pedk_fill_history_last(const JOBINFO_S *job , void *context, uint32_t size)
{
    cJSON *arr = (cJSON*)context;

    if (cJSON_GetArraySize(arr) >= 1)
        return 1;

    if (pedk_fill_one_history(job, arr, size))
    {
        return 0;
    }

    return 0;
}

static void pedk_jobctl_history_list(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    int ret = 0;
    char *str = NULL;
    cJSON *json_arr = NULL;

    JOBCTL_UNUSED(buf_size);
    JOBCTL_UNUSED(buf);
    JOBCTL_UNUSED(ctx);

    json_arr = cJSON_CreateArray();
    if (json_arr && job_manager_get_history_joblist(json_arr, pedk_fill_history_list) > 0)
    {
        ret = cJSON_GetArraySize(json_arr);
        str = cJSON_PrintUnformatted(json_arr);
        ULOG_DEBUG(SYS_JOB_LOG , "get history num[%d], history list %s..\n" , ret, str);
        pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, ret, (unsigned char*)str, strlen(str));
        free(str);
    }
    else
    {
        pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, ret , NULL , 0);
    }

    cJSON_Delete(json_arr);
}

static void pedk_jobctl_history_last(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    int ret = 0;
    char *str = NULL;
    cJSON *json_arr = NULL;

    JOBCTL_UNUSED(buf_size);
    JOBCTL_UNUSED(buf);
    JOBCTL_UNUSED(ctx);

    json_arr = cJSON_CreateArray();
    if (json_arr && job_manager_get_history_joblist(json_arr , pedk_fill_history_last) > 0)
    {
        ret = cJSON_GetArraySize(json_arr);
        str = cJSON_PrintUnformatted(json_arr);        
        ULOG_DEBUG(SYS_JOB_LOG , "get last job history %s..\n" , str);
        pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, ret, (unsigned char*)str, strlen(str));
        free(str);
    }
    else
    {
        pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, ret , NULL , 0);
    }

    cJSON_Delete(json_arr);
}

static void pedk_jobctl_history_clear(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    JOBCTL_UNUSED(buf_size);
    JOBCTL_UNUSED(buf);
    JOBCTL_UNUSED(ctx);

    job_manager_clear_history_joblist();
    
    ULOG_DEBUG(SYS_JOB_LOG , "clear all history ..\n");

    pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, sub, 0, NULL, 0);
}

static void pedk_jobctl_mgr_handle(SUB_MSG_E sub, int32_t respond, int32_t buf_size, uint8_t *buf, void *ctx)
{
    int i = 0;

    const struct 
    {
        SUB_MSG_E                sub_msg_type;
        PEDK_JOBCTL_SUB_HANDLE_F sub_handle;
    } job_handle_map[] = {
        { MSG_JOBCTL_SUB,               pedk_jobctl_list            },
        { MSG_JOBCTL_SUB_CANCEL,        pedk_jobctl_cancel          },
      //{ MSG_JOBCTL_SUB_APPLYID,       pedk_jobctl_apply_id        },
        { MSG_JOBCTL_SUB_HISTORY_LIST,  pedk_jobctl_history_list    },
        { MSG_JOBCTL_SUB_HISTORY_LAST,  pedk_jobctl_history_last    },
        { MSG_JOBCTL_SUB_HISTORY_CLEAR, pedk_jobctl_history_clear   }
    };

    for (i = 0; i < JOBCTL_ITEM(job_handle_map); ++i)
    {
        if (sub == job_handle_map[i].sub_msg_type)
        {
            job_handle_map[i].sub_handle(sub, respond, buf_size, buf, ctx);
            return ;
        }
    }

    ULOG_WARN(SYS_JOB_LOG, "Unsupport sub:%d", sub);
}

int32_t pedk_jobctl_init(void)
{
    int32_t ret = 0;

    ret = pedk_mgr_register_handler(MSG_MODULE_JOBCTL, pedk_jobctl_mgr_handle, NULL);
    
    if (0 != ret)
    {
        ULOG_ERROR(SYS_JOB_LOG, "pedk job control hander register failed\n");
    }

    return ret;
}

void pedk_jobctl_deinit(void)
{
    pedk_mgr_unregister_handler(MSG_MODULE_JOBCTL);
}

/**
 * @brief update job info to pedk
 * @param job_id
 * @param flag
 *
 * @return non return value
 */
void pedk_jobinfo_update(void *arg)
{
    char *job_str = NULL;    
    ONE_JOB_INFO_P one_job = (ONE_JOB_INFO_P)arg;

    if (unlikely(!one_job))
    {
        return ;
    }

    job_str = pedk_jobinfo_encap(&(one_job->_job_info));
    
    if (job_str)
    {
        pedk_mgr_send_msg_to_runenv(MSG_MODULE_JOBCTL, MSG_JOBCTL_SUB_NOTIFY, 1, (unsigned char*)job_str, strlen(job_str));
        free(job_str);
    }
}

/**
*@}
*/
