/* Copyright 2008 The Android Open Source Project
 */

#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <sys/mman.h>
#include <pthread.h>

#include "pol/pol_types.h"
#include "pol/pol_list.h"
#include "cbinder/cbinder.h"

#define MAX_BIO_SIZE (1 << 30)

#define BINDER_STACK_SIZE (2 * 1024 * 1024)

#define TRACE 0

#ifndef LOGE
#define LOGE(x...) fprintf(stderr, "libcbinder: " x)
#endif


void bio_init_from_txn(struct binder_io *io, struct binder_txn *txn);

static int binder_create_new_loop(struct binder_state *bs, int is_main);

#if TRACE
void hexdump(void *_data, unsigned len)
{
    unsigned char *data = _data;
    unsigned count;

    for (count = 0; count < len; count++) {
        if ((count & 15) == 0)
            fprintf(stderr,"%04x:", count);
        fprintf(stderr," %02x %c", *data,
                (*data < 32) || (*data > 126) ? '.' : *data);
        data++;
        if ((count & 15) == 15)
            fprintf(stderr,"\n");
    }
    if ((count & 15) != 0)
        fprintf(stderr,"\n");
}

void binder_dump_txn(struct binder_txn *txn)
{
    struct binder_object *obj;
    unsigned *offs = txn->offs;
    unsigned count = txn->offs_size / 4;

    fprintf(stderr,"  target %p  cookie %p  code %08x  flags %08x\n",
            txn->target, txn->cookie, txn->code, txn->flags);
    fprintf(stderr,"  pid %8d  uid %8d  data %8d  offs %8d\n",
            txn->sender_pid, txn->sender_euid, txn->data_size, txn->offs_size);
    hexdump(txn->data, txn->data_size);
    while (count--) {
        obj = (void*) (((char*) txn->data) + *offs++);
        fprintf(stderr,"  - type %08x  flags %08x  ptr %p  cookie %p\n",
                obj->type, obj->flags, obj->pointer, obj->cookie);
    }
}

#define NAME(n) case n: return #n
const char *cmd_name(uint32_t cmd)
{
    switch(cmd) {
        NAME(BR_NOOP);
        NAME(BR_TRANSACTION_COMPLETE);
        NAME(BR_INCREFS);
        NAME(BR_ACQUIRE);
        NAME(BR_RELEASE);
        NAME(BR_DECREFS);
        NAME(BR_TRANSACTION);
        NAME(BR_REPLY);
        NAME(BR_FAILED_REPLY);
        NAME(BR_DEAD_REPLY);
        NAME(BR_DEAD_BINDER);
    default: return "???";
    }
}
#else
#define hexdump(a,b) do{} while (0)
#define binder_dump_txn(txn)  do{} while (0)
#endif

#define BIO_F_SHARED    0x01  /* needs to be buffer freed */
#define BIO_F_OVERFLOW  0x02  /* ran out of space */
#define BIO_F_IOERROR   0x04
#define BIO_F_MALLOCED  0x08  /* needs to be free()'d */

struct target_handler_list
{
	struct list_head list;
	void *target;
	binder_handler handler;
	void *data;
};

struct binder_state
{
    int fd;
    void *mapped;
    unsigned mapsize;
	pthread_mutex_t mutex;
	int thread_cnt;
	struct target_handler_list target_list;
	unsigned int rdatasize;
	unsigned int maxobjects;
	pid_t pid;
};

struct binder_thread_arg_t
{
	struct binder_state *bs;
	int is_main;
};

static struct binder_state * g_binder_bs = NULL;

struct binder_state * binder_open(int max_thread)
{
	int vers;
	int result;
	struct binder_state *bs;

    bs = malloc(sizeof(*bs));
    if (bs == NULL) {
        errno = ENOMEM;
        return 0;
    }
	
	if (max_thread < 1 || max_thread > 15) {
		max_thread = 1;
	}

    bs->fd = open("/dev/binder", O_RDWR);
    if (bs->fd < 0) {
        fprintf(stderr,"binder: cannot open device (%s)\n",
                strerror(errno));
        goto fail_open;
    }
	
	fcntl(bs->fd, F_SETFD, FD_CLOEXEC);
	
	result = ioctl(bs->fd, BINDER_VERSION, &vers);

    bs->mapsize = BINDER_VM_SIZE;
    bs->mapped = mmap(NULL, BINDER_VM_SIZE, PROT_READ, MAP_PRIVATE, bs->fd, 0);
    if (bs->mapped == MAP_FAILED) {
        fprintf(stderr,"binder: cannot map device (%s)\n",
                strerror(errno));
        goto fail_map;
    }
	
	if (max_thread > 1) {
		//size_t maxThreads = max_thread;
		result = ioctl(bs->fd, BINDER_SET_MAX_THREADS, &max_thread);
		if (result == -1) {
			fprintf(stderr,"Binder ioctl to set max threads failed: %s", strerror(errno));
			goto fail_map;
		}
	}
        /* TODO: check version */
	pi_init_list_head(&(bs->target_list.list));
	bs->thread_cnt = 0;
	bs->rdatasize = REPLY_DATA_SIZE_DEFAULE;
	bs->maxobjects = REPLY_MAX_OBJECTS;
	bs->pid = getpid();
	pthread_mutex_init(&bs->mutex, NULL);
    return bs;

fail_map:
    close(bs->fd);
    bs->fd = -1;
fail_open:
    free(bs);
	bs = NULL;
    return 0;
}

static struct target_handler_list* target_handler_create(void* target, binder_handler handler, void* data)
{
    struct target_handler_list* node;

	node = (struct target_handler_list *)malloc(sizeof(struct target_handler_list));
	if (node) {
        pi_init_list_head(&(node->list));
		node->target  = target;
		node->handler = handler;
		node->data    = data;
	}

    return node;
}

static void target_handler_destroy(struct target_handler_list* node)
{
    pi_list_del_entry(&(node->list));
    free(node);
}

int binder_add_target(struct binder_state *bs, void *target, binder_handler handler, void *data)
{
	struct target_handler_list *node;
	int ret = 0;

	if (bs == NULL) {
		return 0;
	}
	/* just try to delete it first */
	binder_del_target(bs, target);
	
	pthread_mutex_lock(&bs->mutex);
	node = target_handler_create(target, handler, data);
	if (node) {
		pi_list_add_tail(&(node->list), &(bs->target_list.list));
		ret = 1;
	}else{
		ret = 0;
	}
	pthread_mutex_unlock(&bs->mutex);

	return ret; /* node will free by binder_del_target */
}

void binder_del_target(struct binder_state *bs, void *target)
{
	struct target_handler_list *node;
	struct list_head *pos, *q;

	if (bs == NULL) {
		return;
	}

	pthread_mutex_lock(&bs->mutex);
	pi_list_for_each_safe(pos, q, &(bs->target_list.list)){
		node = pi_list_entry(pos, struct target_handler_list, list);
		if (node && (node->target == target)) {
			target_handler_destroy(node);
			break;
		}
	}
	pthread_mutex_unlock(&bs->mutex);
}

static void binder_del_all_target(struct binder_state *bs)
{
	struct target_handler_list *tmp;
	struct list_head *pos, *q;

	if (bs == NULL) {
		return;
	}

	pthread_mutex_lock(&bs->mutex);
	pi_list_for_each_safe(pos, q, &(bs->target_list.list)){
		tmp= pi_list_entry(pos, struct target_handler_list, list);
		if(tmp){
			pi_list_del_entry(pos);
			free(tmp);
		}
	}
	pthread_mutex_unlock(&bs->mutex);
}

static void binder_close(struct binder_state *bs)
{
	int cnt = 0;
	int current = 0;

	if (bs == NULL) {
		return;
	}
	binder_del_all_target(bs);
    munmap(bs->mapped, bs->mapsize);
    close(bs->fd);
    bs->fd = -1;

	while(cnt < 6){
		/* atomic_read bs->thread_cnt*/
		current =  __sync_sub_and_fetch(&bs->thread_cnt, 0);
		if(current <= 0){
			break;
		}
		usleep(50*1000);
		cnt++;
	}	

    free(bs);
}

void binder_set_rdatasize(struct binder_state *bs, unsigned int newsize)
{
	if (bs == NULL) {
		return;
	}

	if (newsize <= REPLY_DATA_SIZE_MAX) {
		bs->rdatasize = newsize;
	}
}

void binder_set_maxobjects(struct binder_state *bs, unsigned int newmax)
{
	if (bs == NULL) {
		return;
	}

	if (newmax <= 1024) {
		bs->maxobjects = newmax;
	}
}


int binder_become_context_manager(struct binder_state *bs)
{
	if (bs == NULL || bs->fd < 0) {
		return -1;
	}
    return ioctl(bs->fd, BINDER_SET_CONTEXT_MGR, 0);
}

int binder_write(struct binder_state *bs, void *data, size_t len)
{
    struct binder_write_read bwr;
    int res;

	if (bs == NULL || bs->fd < 0) {
		return -1;
	}

    bwr.write_size = len;
    bwr.write_consumed = 0;
    bwr.write_buffer = (unsigned) data;
    bwr.read_size = 0;
    bwr.read_consumed = 0;
    bwr.read_buffer = 0;
    res = ioctl(bs->fd, BINDER_WRITE_READ, &bwr);
    if (res < 0) {
        LOGE("binder_write: ioctl failed (%s)\n",
                strerror(errno));
    }
    return res;
}

void binder_send_reply(struct binder_state *bs, struct binder_io *reply, void *buffer_to_free, int status)
{
    struct {
        uint32_t cmd_free;
        void *buffer;
        uint32_t cmd_reply;
        struct binder_txn txn;
    } __attribute__((packed)) data;

	if (bs == NULL) {
		return;
	}

    data.cmd_free = BC_FREE_BUFFER;
    data.buffer = buffer_to_free;
    data.cmd_reply = BC_REPLY;
    data.txn.target = 0;
    data.txn.cookie = 0;
    data.txn.code = 0;
    if (status) {
        data.txn.flags = TF_STATUS_CODE;
        data.txn.data_size = sizeof(int);
        data.txn.offs_size = 0;
        data.txn.data = &status;
        data.txn.offs = 0;
    } else {
        data.txn.flags = 0;
        data.txn.data_size = reply->data - reply->data0;
        data.txn.offs_size = ((char*) reply->offs) - ((char*) reply->offs0);
        data.txn.data = reply->data0;
        data.txn.offs = reply->offs0;
    }
    binder_write(bs, &data, sizeof(data));
}

int binder_parse(struct binder_state *bs, struct binder_io *bio, uint32_t *ptr, uint32_t size, binder_handler func)
{
    int r = 1;
    uint32_t *end = ptr + (size / 4);

	if (bs == NULL) {
		return -1;
	}

    while (ptr < end) {
        uint32_t cmd = *ptr++;
#if TRACE
        fprintf(stderr,"%s:\n", cmd_name(cmd));
#endif
        switch(cmd) {
		case BR_ERROR: 
			r = bio_get_uint32(bio);
			break;
		case BR_OK:
			break;
        case BR_NOOP:
            break;
        case BR_TRANSACTION_COMPLETE:
            break;
        case BR_INCREFS:
        case BR_ACQUIRE:
        case BR_RELEASE:
        case BR_DECREFS:
#if TRACE
            fprintf(stderr,"  %08x %08x\n", ptr[0], ptr[1]);
#endif
            ptr += 2;
            break;
        case BR_TRANSACTION: {
            struct binder_txn *txn = (void *) ptr;
            if ((end - ptr) * sizeof(uint32_t) < sizeof(struct binder_txn)) {
                LOGE("parse: txn too small!\n");
                return -1;
            }
            binder_dump_txn(txn);
            if (func) {
                unsigned *rdata = NULL;
                unsigned int rdatasize;
                struct binder_io msg;
                struct binder_io reply;
                int res;

                rdatasize = bs->rdatasize;
                rdata = malloc(rdatasize);
                if (rdata == NULL) {
                    rdatasize = REPLY_DATA_SIZE_DEFAULE;
                    rdata = malloc(rdatasize);
                }
                bio_init(&reply, rdata, rdatasize, bs->maxobjects);
                bio_init_from_txn(&msg, txn);
                res = func(bs, txn, &msg, &reply, 0);
                binder_send_reply(bs, &reply, txn->data, res);
                free(rdata);
            }
            ptr += sizeof(*txn) / sizeof(uint32_t);
            break;
        }
        case BR_REPLY: {
            struct binder_txn *txn = (void*) ptr;
            if ((end - ptr) * sizeof(uint32_t) < sizeof(struct binder_txn)) {
                LOGE("parse: reply too small!\n");
                return -1;
            }
            binder_dump_txn(txn);
            if (bio) {
                bio_init_from_txn(bio, txn);
                bio = 0;
            } else {
                    /* todo FREE BUFFER */
            }
            ptr += (sizeof(*txn) / sizeof(uint32_t));
            r = 0;
            break;
        }
        case BR_DEAD_BINDER: {
            struct binder_death *death = (void*) *ptr++;
            death->func(bs, death->ptr);
            break;
        }
        case BR_FAILED_REPLY:
            r = -1;
            break;
        case BR_DEAD_REPLY:
            r = -1;
            break;
		case BR_SPAWN_LOOPER: {
			LOGE("parse: NEW THREAD\n");
			binder_create_new_loop(bs, 0);
			break;
		}
		case BR_FINISHED:
			r = -ETIMEDOUT;
			break;
        default:
            LOGE("parse: OOPS %u\n", cmd);
            return -1;
        }
    }

    return r;
}

void binder_acquire(struct binder_state *bs, void *ptr)
{
    uint32_t cmd[2];
	if (bs == NULL) {
		return;
	}
    cmd[0] = BC_ACQUIRE;
    cmd[1] = (uint32_t) ptr;
    binder_write(bs, cmd, sizeof(cmd));
}

void binder_release(struct binder_state *bs, void *ptr)
{
    uint32_t cmd[2];
	if (bs == NULL) {
		return;
	}
    cmd[0] = BC_RELEASE;
    cmd[1] = (uint32_t) ptr;
    binder_write(bs, cmd, sizeof(cmd));
}

void binder_link_to_death(struct binder_state *bs, void *ptr, struct binder_death *death)
{
    uint32_t cmd[3];
	if (bs == NULL) {
		return;
	}
    cmd[0] = BC_REQUEST_DEATH_NOTIFICATION;
    cmd[1] = (uint32_t) ptr;
    cmd[2] = (uint32_t) death;
    binder_write(bs, cmd, sizeof(cmd));
}


int binder_call(struct binder_state *bs, struct binder_io *msg, struct binder_io *reply, void *target, uint32_t code)
{
    int res;
    struct binder_write_read bwr;
    struct {
        uint32_t cmd;
        struct binder_txn txn;
    } writebuf;
    unsigned readbuf[32];
	if (bs == NULL) {
		return -1;
	}

    if (msg->flags & BIO_F_OVERFLOW) {
        fprintf(stderr,"binder: txn buffer overflow\n");
        goto fail;
    }

    writebuf.cmd = BC_TRANSACTION;
    writebuf.txn.target = target;
    writebuf.txn.code = code;
    writebuf.txn.flags = 0;
    writebuf.txn.data_size = msg->data - msg->data0;
    writebuf.txn.offs_size = ((char*) msg->offs) - ((char*) msg->offs0);
    writebuf.txn.data = msg->data0;
    writebuf.txn.offs = msg->offs0;

    bwr.write_size = sizeof(writebuf);
    bwr.write_consumed = 0;
    bwr.write_buffer = (unsigned) &writebuf;
    
    hexdump(msg->data0, msg->data - msg->data0);
    for (;;) {
        bwr.read_size = sizeof(readbuf);
        bwr.read_consumed = 0;
        bwr.read_buffer = (unsigned) readbuf;

        res = ioctl(bs->fd, BINDER_WRITE_READ, &bwr);
        if (res < 0) {
            fprintf(stderr,"binder: ioctl failed (%s)\n", strerror(errno));
            goto fail;
        }

        res = binder_parse(bs, reply, readbuf, bwr.read_consumed, 0);
        if (res == 0) return 0;
        if (res < 0) goto fail;
    }

fail:
    memset(reply, 0, sizeof(*reply));
    reply->flags |= BIO_F_IOERROR;
    return -1;
}

static int loop_handler(struct binder_state *bs, struct binder_txn *txn,
                   struct binder_io *msg,
                   struct binder_io *reply,
                   void *data)
{
	struct target_handler_list *tmp;
	struct list_head *pos, *q;
	if (bs == NULL) {
		return -1;
	}
	binder_handler handler;
	
	pthread_mutex_lock(&bs->mutex);
	pi_list_for_each_safe(pos, q, &(bs->target_list.list)){
		tmp= pi_list_entry(pos, struct target_handler_list, list);
		if (tmp && (tmp->target == txn->target)) {
			if (tmp->handler != NULL) {
				handler = tmp->handler;
				pthread_mutex_unlock(&bs->mutex);
				handler(bs, txn, msg, reply, tmp->data);
				pthread_mutex_lock(&bs->mutex);
			}
			break;
		}
	}
	pthread_mutex_unlock(&bs->mutex);
	return 0;
}

static void *binder_thr(void *arg)
{
    int res;
    struct binder_write_read bwr;
    unsigned readbuf[32];
	struct binder_thread_arg_t *myarg = arg;
	struct binder_state *bs = myarg->bs;
	int is_main = myarg->is_main;
	if (bs == NULL) {
		return NULL;
	}

    bwr.write_size = 0;
    bwr.write_consumed = 0;
    bwr.write_buffer = 0;
    
    readbuf[0] = is_main ? BC_ENTER_LOOPER : BC_REGISTER_LOOPER;
    binder_write(bs, readbuf, sizeof(unsigned));

    do {
        bwr.read_size = sizeof(readbuf);
        bwr.read_consumed = 0;
        bwr.read_buffer = (unsigned) readbuf;

        if (bs->fd < 0)
        {
            //LOGE("invalid fd\n");
            break;
        }

        res = ioctl(bs->fd, BINDER_WRITE_READ, &bwr);
        if (res < 0) {
            LOGE("binder_loop: ioctl failed (%s)\n", strerror(errno));
            break;
        }

        res = binder_parse(bs, 0, readbuf, bwr.read_consumed, loop_handler);
        if (res == 0) {
            LOGE("binder_loop: unexpected reply?!\n");
            break;
        }
        if (res != -ETIMEDOUT && res < 0) {
            LOGE("binder_loop: io error %d %s\n", res, strerror(errno));
            break;
        }
		// Let this thread exit the thread pool if it is no longer
        // needed and it is not the main process thread.
        if (res == -ETIMEDOUT && !is_main) {
            break;
        }
    } while(res != -ECONNREFUSED && res != -EBADF);
	
	//LOGE("binder_thr: EXIT THREAD\n");
	
	readbuf[0] = BC_EXIT_LOOPER;
    binder_write(bs, readbuf, sizeof(unsigned));
	__sync_fetch_and_sub(&bs->thread_cnt, 1);
	free(arg);

    return NULL;
}

static int binder_create_new_loop(struct binder_state *bs, int is_main)
{
	pthread_t thread;
	pthread_attr_t attr;
	struct binder_thread_arg_t *arg;
	if (bs == NULL) {
		return -1;
	}
	
	pthread_attr_init (&attr);
	pthread_attr_setdetachstate (&attr, PTHREAD_CREATE_DETACHED);
	pthread_attr_setstacksize(&attr, BINDER_STACK_SIZE);
	
	arg = malloc(sizeof(struct binder_thread_arg_t));
	arg->bs = bs;
	arg->is_main = is_main;
	/* inc thread_cnt here rather than inc it in binder_thr  
	 * because the bs may be free after pthread_create and before binder_thr run
	 * if this happend, the programe  will crash.
	 * */
	__sync_fetch_and_add(&bs->thread_cnt, 1);
	pthread_create (&thread, &attr, binder_thr, (void *)arg);
	pthread_attr_destroy (&attr);
	return 0;
}

struct binder_state *binder_start(int max_thread)
{
	struct binder_state *bs;

	bs = binder_open(max_thread);
	if (bs != NULL) {
		binder_create_new_loop(bs, 1);
	}

	return bs;
}

void binder_stop(struct binder_state *bs)
{
	if (bs == NULL) {
		return;
	}
	binder_close(bs);
	if (bs == g_binder_bs) {
		g_binder_bs = NULL;
	}
}

struct binder_state * binder_global_bs(void)
{
	pid_t current_pid = 0;

	if(g_binder_bs != NULL){
		current_pid = getpid();
		if(current_pid != g_binder_bs->pid){
			binder_stop(g_binder_bs);
			g_binder_bs = binder_start(1);
		}
	}else{
		g_binder_bs = binder_start(1);
	}

	return g_binder_bs;
}

pid_t binder_get_pid(struct binder_state *bs)
{
	if (bs == NULL) {
		return -1;
	}
    return bs->pid;
}

void bio_init_from_txn(struct binder_io *bio, struct binder_txn *txn)
{
    bio->data = bio->data0 = txn->data;
    bio->offs = bio->offs0 = txn->offs;
    bio->data_avail = txn->data_size;
    bio->offs_avail = txn->offs_size / 4;
    bio->flags = BIO_F_SHARED;
}

void bio_init(struct binder_io *bio, void *data,
              uint32_t maxdata, uint32_t maxoffs)
{
    uint32_t n = maxoffs * sizeof(uint32_t);

    if (n > maxdata) {
        bio->flags = BIO_F_OVERFLOW;
        bio->data_avail = 0;
        bio->offs_avail = 0;
        return;
    }

    bio->data = bio->data0 = (char*)data + n;
    bio->offs = bio->offs0 = data;
    bio->data_avail = maxdata - n;
    bio->offs_avail = maxoffs;
    bio->flags = 0;
}

static void *bio_alloc(struct binder_io *bio, uint32_t size)
{
    size = (size + 3) & (~3);
    if (size > bio->data_avail) {
        bio->flags |= BIO_F_OVERFLOW;
        return 0;
    } else {
        void *ptr = bio->data;
        bio->data += size;
        bio->data_avail -= size;
        return ptr;
    }
}

void binder_done(struct binder_state *bs, struct binder_io *msg,
                 struct binder_io *reply)
{
	if (bs == NULL) {
		return;
	}
    if (reply->flags & BIO_F_SHARED) {
        uint32_t cmd[2];
        cmd[0] = BC_FREE_BUFFER;
        cmd[1] = (uint32_t) reply->data0;
        binder_write(bs, cmd, sizeof(cmd));
        reply->flags = 0;
    }
}

static struct binder_object *bio_alloc_obj(struct binder_io *bio)
{
    struct binder_object *obj;

    obj = bio_alloc(bio, sizeof(*obj));
    
    if (obj && bio->offs_avail) {
        bio->offs_avail--;
        *bio->offs++ = ((char*) obj) - ((char*) bio->data0);
        return obj;
    }

    bio->flags |= BIO_F_OVERFLOW;
    return 0;
}

void bio_put_uint32(struct binder_io *bio, uint32_t n)
{
    uint32_t *ptr = bio_alloc(bio, sizeof(n));
    if (ptr)
        *ptr = n;
}

void bio_put_obj(struct binder_io *bio, void *ptr)
{
    struct binder_object *obj;

    obj = bio_alloc_obj(bio);
    if (!obj)
        return;

    obj->flags = 0x7f | FLAT_BINDER_FLAG_ACCEPTS_FDS;
    obj->type = BINDER_TYPE_BINDER;
    obj->pointer = ptr;
    obj->cookie = 0;
}

void bio_put_ref(struct binder_io *bio, void *ptr)
{
    struct binder_object *obj;

    if (ptr)
        obj = bio_alloc_obj(bio);
    else
        obj = bio_alloc(bio, sizeof(*obj));

    if (!obj)
        return;

    obj->flags = 0x7f | FLAT_BINDER_FLAG_ACCEPTS_FDS;
    obj->type = BINDER_TYPE_HANDLE;
    obj->pointer = ptr;
    obj->cookie = 0;
}

void bio_put_string16(struct binder_io *bio, const uint16_t *str)
{
    uint32_t len;
    uint16_t *ptr;

    if (!str) {
        bio_put_uint32(bio, 0xffffffff);
        return;
    }

    len = 0;
    while (str[len]) len++;

    if (len >= (MAX_BIO_SIZE / sizeof(uint16_t))) {
        bio_put_uint32(bio, 0xffffffff);
        return;
    }

    bio_put_uint32(bio, len);
    len = (len + 1) * sizeof(uint16_t);
    ptr = bio_alloc(bio, len);
    if (ptr)
        memcpy(ptr, str, len);
}

void bio_put_string16_x(struct binder_io *bio, const char *_str)
{
    unsigned char *str = (unsigned char*) _str;
    uint32_t len;
    uint16_t *ptr;

    if (!str) {
        bio_put_uint32(bio, 0xffffffff);
        return;
    }

    len = strlen(_str);

    if (len >= (MAX_BIO_SIZE / sizeof(uint16_t))) {
        bio_put_uint32(bio, 0xffffffff);
        return;
    }

    bio_put_uint32(bio, len);
    ptr = bio_alloc(bio, (len + 1) * sizeof(uint16_t));
    if (!ptr)
        return;

    while (*str)
        *ptr++ = *str++;
    *ptr++ = 0;
}

void bio_put_buffer(struct binder_io *bio, const char *_str, uint32_t size)
{
    uint32_t len;
	unsigned char *str = (unsigned char*) _str;
	unsigned char *ptr;

    if (!str || size <= 0) {
        bio_put_uint32(bio, 0xffffffff);
        return;
    }

	len = size;

    if (len >= (MAX_BIO_SIZE / sizeof(unsigned char))) {
        bio_put_uint32(bio, 0xffffffff);
        return;
    }

    bio_put_uint32(bio, len);
    //len = (len + 1) * sizeof(unsigned char);
    ptr = bio_alloc(bio, len);
    if (ptr)
        memcpy(ptr, str, len);
}

void bio_put_string8(struct binder_io *bio, const char *_str)
{
	bio_put_buffer(bio, _str, strlen(_str)+1);
}

void bio_put_fd(struct binder_io *bio, int fd)
{
    struct binder_object *obj;

    obj = bio_alloc_obj(bio);
    if (!obj)
        return;

    obj->flags = 0x7f | FLAT_BINDER_FLAG_ACCEPTS_FDS;
    obj->type = BINDER_TYPE_FD;
    obj->pointer = (void *)fd;
    obj->cookie = 0;
}


static void *bio_get(struct binder_io *bio, uint32_t size)
{
    size = (size + 3) & (~3);

    if (bio->data_avail < size){
        bio->data_avail = 0;
        bio->flags |= BIO_F_OVERFLOW;
        return 0;
    }  else {
        void *ptr = bio->data;
        bio->data += size;
        bio->data_avail -= size;
        return ptr;
    }
}

uint32_t bio_get_uint32(struct binder_io *bio)
{
    uint32_t *ptr = bio_get(bio, sizeof(*ptr));
    return ptr ? *ptr : 0;
}

uint16_t *bio_get_string16(struct binder_io *bio, uint32_t *sz)
{
    uint32_t len;
    len = bio_get_uint32(bio);
    if (sz)
        *sz = len;
    return bio_get(bio, (len + 1) * sizeof(uint16_t));
}

char *bio_get_buffer(struct binder_io *bio, uint32_t *sz)
{
	uint32_t len;
    len = bio_get_uint32(bio);
    if (sz)
        *sz = len;
    //return bio_get(bio, (len + 1) * sizeof(unsigned char));
    return bio_get(bio, len * sizeof(unsigned char));
}

char *bio_get_string8(struct binder_io *bio, uint32_t *sz)
{
	return bio_get_buffer(bio, sz);
}

static struct binder_object *_bio_get_obj(struct binder_io *bio)
{
    unsigned n;
    unsigned off = bio->data - bio->data0;

        /* TODO: be smarter about this? */
    for (n = 0; n < bio->offs_avail; n++) {
        if (bio->offs[n] == off)
            return bio_get(bio, sizeof(struct binder_object));
    }

    bio->data_avail = 0;
    bio->flags |= BIO_F_OVERFLOW;
    return 0;
}

void *bio_get_ref(struct binder_io *bio)
{
    struct binder_object *obj;

    obj = _bio_get_obj(bio);
    if (!obj)
        return 0;

    if (obj->type == BINDER_TYPE_HANDLE)
        return obj->pointer;

    return 0;
}

int bio_get_fd(struct binder_io *bio)
{
    struct binder_object *obj;

    obj = _bio_get_obj(bio);
    if (!obj)
        return -1;

    if (obj->type == BINDER_TYPE_FD)
        return (int)obj->pointer;

    return -1;
}

void *svcmgr_get_service(struct binder_state *bs, void *target, const char *name)
{
    void *ptr;
    unsigned iodata[512/4];
    struct binder_io msg, reply;

	if (bs == NULL) {
		return NULL;
	}

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, 0);  // strict mode header
    bio_put_string16_x(&msg, SVC_MGR_NAME);
    bio_put_string16_x(&msg, name);

    if (binder_call(bs, &msg, &reply, target, SVC_MGR_CHECK_SERVICE))
        return 0;

    ptr = bio_get_ref(&reply);

    if (ptr)
        binder_acquire(bs, ptr);

    binder_done(bs, &msg, &reply);

    return ptr;
}

void *svcmgr_lookup(struct binder_state *bs, void *target, const char *name)
{
	if (bs == NULL) {
		return NULL;
	}
	return svcmgr_get_service(bs, target, name);
}

int svcmgr_add_service(struct binder_state *bs, void *target, const char *name, void *ptr)
{
    unsigned status;
    unsigned iodata[512/4];
    struct binder_io msg, reply;
	if (bs == NULL) {
		return -1;
	}

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, 0);  // strict mode header
    bio_put_string16_x(&msg, SVC_MGR_NAME);
    bio_put_string16_x(&msg, name);
    bio_put_obj(&msg, ptr);

    if (binder_call(bs, &msg, &reply, target, SVC_MGR_ADD_SERVICE))
        return -1;

    status = bio_get_uint32(&reply);

    binder_done(bs, &msg, &reply);

    return status;
}

int svcmgr_publish(struct binder_state *bs, void *target, const char *name, void *ptr)
{
	if (bs == NULL) {
		return -1;
	}
	return svcmgr_add_service(bs, target, name, ptr);
}

int svcmgr_del_service(struct binder_state *bs, void *target, const char *name, void *ptr)
{
    unsigned status;
    unsigned iodata[512/4];
    struct binder_io msg, reply;
	if (bs == NULL) {
		return -1;
	}

    bio_init(&msg, iodata, sizeof(iodata), 4);
    bio_put_uint32(&msg, 0);  // strict mode header
    bio_put_string16_x(&msg, SVC_MGR_NAME);
    bio_put_string16_x(&msg, name);
    bio_put_obj(&msg, ptr);

    if (binder_call(bs, &msg, &reply, target, SVC_MGR_DEL_SERVICE))
        return -1;

    status = bio_get_uint32(&reply);

    binder_done(bs, &msg, &reply);

    return status;
}

