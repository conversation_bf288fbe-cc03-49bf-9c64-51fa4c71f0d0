/**
 * @copyright 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file status_manager.c
 * @addtogroup system_manager
 * @{
 * @brief status management module
 * <AUTHOR>
 * @version 1.0
 * @date 2023-04-18
 */

#include "status_manager.h"
#include "statusid.h"
#include "event_manager/event_mgr.h"
#include "status_pedkapi.h"
#include "pol/pol_threads.h"
#include "pol/pol_time.h"
#include "pol/pol_io.h"
#include "pol/pol_mem.h"
#include "pol/pol_log.h"
#include "channels.h"
#include "ulog.h"
#include <unistd.h>
#include <errno.h>
#include <stdlib.h>
#include <sys/eventfd.h>

#define SYS_STATUS_LOG "SYS_STATUS"

static struct ulog s_log = {
    .name = SYS_STATUS_LOG,
    .file = NULL,
    .fd = -1 ,
    .limitsize = 0,
    .level = DEBUG,
    .o_time = 1,
    .o_file = 0,
    .o_func = 1,
    .o_line = 1,
    .o_module = 1,
    .o_level = 1,
    .o_pid = 0,
    .o_thread = 0,
    .o_color = 0,
    .next = NULL
};

typedef struct
{
    int32_t _exit;
    int32_t _eventfd;
    uint8_t _print_first_ready;
    uint8_t _scan_first_ready;
    uint8_t _copy_first_ready;
    uint8_t _log_flag;          ///< set log level flag , 0- no set, 1-have set
    struct channels _set[2]; //_set[0] is outward , _set[1] is inward
    PI_MUTEX_T _mtx[2]; // _mtx[0] lock _set[0] , _mtx[1] lock _set[1]
    EVT_MGR_CLI_S *_client;
    PI_THREAD_T _th;
}STATUS_DATA_S , *STATUS_DATA_P;

typedef struct
{
    uint32_t status_id_moudle;
    uint32_t error_fatal_id;
    uint32_t len;
    uint8_t *buffer; // 1~n:statusid(4byte) + statusargs1(4byte) + stastusargs2(4byte) + statusargs3(4byte)
}MODULE_STATUS_S , *MODULE_STATUS_P;

static STATUS_DATA_S _s_data;

static void* _copy_alloc_buffer(void *addr)
{
    MODULE_STATUS_P ret = (MODULE_STATUS_P)pi_malloc(sizeof(MODULE_STATUS_S));
    if (!ret)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "malloc failed\n");
        return ret;
    }
    MODULE_STATUS_P buf = (MODULE_STATUS_P)addr;

    ret->status_id_moudle = buf->status_id_moudle;
    ret->len = buf->len;
    ret->error_fatal_id = buf->error_fatal_id;
    ret->buffer = (uint8_t*)pi_malloc(ret->len);
    if (!ret->buffer)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "malloc failed\n");
        pi_free(ret);
        return NULL;
    }
    pi_memcpy(ret->buffer , buf->buffer , buf->len);
    return ret;
}

static void _free_buffer(void *addr)
{
    MODULE_STATUS_P buf = (MODULE_STATUS_P)addr;
    if (buf)
    {
        pi_free(buf->buffer);
        pi_free(buf);
    }
}

static int32_t _status_update(MODULE_STATUS_P buf)
{
    if ((buf->len <= 0 ) || (buf->len % 16))
    {
        ULOG_ERROR(SYS_STATUS_LOG , "status args invalid , len:%u \n" , buf->len);
        return 0;
    }

    ULOG_DEBUG(SYS_STATUS_LOG , "-->update status_id_moudle:%x , buf_len:%u\n" ,
            buf->status_id_moudle , buf->len);

    int32_t e_id = 0;

    for (int32_t i = 0; i < buf->len / 16; ++i)
    {
        int32_t id = *(int32_t*)(buf->buffer + i * 16);
        int32_t arg1 = *(int32_t*)(buf->buffer + i * 16 + 4);
        int32_t arg2 = *(int32_t*)(buf->buffer + i * 16 + 8);
        int32_t arg3 = *(int32_t*)(buf->buffer + i * 16 + 12);
        ULOG_DEBUG(SYS_STATUS_LOG , "-%x#%x#%x#%x\n" , id , arg1 , arg2 , arg3);
        if (  ((id & (STATUS_ID_TYPE_FATAL)) == STATUS_ID_TYPE_FATAL) ||
              ((e_id == 0) && (id & (STATUS_ID_TYPE_ERROR)) == STATUS_ID_TYPE_ERROR) )
        {
            e_id = id;
        }

        if (buf->status_id_moudle == STATUS_ID_MODULE_PRINT && _s_data._print_first_ready == 0 && id == STATUS_I_PRINT_READY)
        {
            _s_data._print_first_ready = 1;
            ULOG_INFO(SYS_STATUS_LOG , "print ready!\n");
        }
        else if (buf->status_id_moudle == STATUS_ID_MODULE_SCAN && _s_data._scan_first_ready == 0 && id == STATUS_I_SCAN_IDLE)
        {
            _s_data._scan_first_ready = 1;
            ULOG_INFO(SYS_STATUS_LOG , "scan ready!\n");
        }
        else if (buf->status_id_moudle == STATUS_ID_MODULE_COPY && _s_data._copy_first_ready == 0 && id == STATUS_I_COPY_IDLE)
        {
            _s_data._copy_first_ready = 1;
            ULOG_INFO(SYS_STATUS_LOG , "copy ready!\n");
        }
        if ( (_s_data._log_flag == 0) && ((_s_data._print_first_ready & _s_data._scan_first_ready & _s_data._copy_first_ready) == 1) )
        {
            pi_log_set_level(0);     ///< log set 0：关闭
            _s_data._log_flag = 1;
        }
    }

    if (e_id)
    {
        MODULE_STATUS_P tmp = (MODULE_STATUS_P)channels_find(&_s_data._set[1] , buf->status_id_moudle);
        if ( (tmp && tmp->error_fatal_id != e_id) ||
             (!tmp) )
        {
            buf->error_fatal_id = e_id;
            pi_event_mgr_notify(_s_data._client , EVT_TYPE_EVENTLOG_SYSTEM_STATUS , &e_id , sizeof(e_id));
            ULOG_DEBUG(SYS_STATUS_LOG , "e_id:%x\n" , e_id);
        }
        else
        {
            buf->error_fatal_id = e_id;
        }
    }

    channels_fragment_remove(&_s_data._set[1] , buf->status_id_moudle , 1);
    return channels_head_insert(&_s_data._set[1] , buf->status_id_moudle , buf , 0) == 0 ? 1:0;
}

static void _status_event_callback(const EVT_MSG_S *msg, void *ctx)
{
    (void)ctx;
    uint32_t status_id_moudle = 0;

    switch(msg->event_type)
    {
        case EVT_TYPE_SYSTEMSTATUS_FROM_SCAN:
            status_id_moudle = STATUS_ID_MODULE_SCAN;
            break;
        case EVT_TYPE_SYSTEMSTATUS_FROM_PRINT:
            status_id_moudle = STATUS_ID_MODULE_PRINT;
            break;
        case EVT_TYPE_SYSTEMSTATUS_FROM_COPY:
            status_id_moudle = STATUS_ID_MODULE_COPY;
            break;
        case EVT_TYPE_SYSTEMSTATUS_FROM_NET:
            status_id_moudle = STATUS_ID_MODULE_NET;
            break;
        /*case EVT_TYPE_SYSTEMSTATUS_FROM_WIFI:
            status_id_moudle = STATUS_ID_MODULE_WIFI;
            break;*/
        case EVT_TYPE_SYSTEMSTATUS_FROM_FWUPDATE:
            status_id_moudle = STATUS_ID_MODULE_FWUPDATE;
            break;
        case EVT_TYPE_SYSTEMSTATUS_FROM_POWERMGR:
            status_id_moudle = STATUS_ID_MODULE_POWERMGR;
            break;
        /*case EVT_TYPE_SYSTEMSTATUS_FROM_USBDEVICE:
            status_id_moudle = STATUS_ID_MODULE_USBDEVICE;
            break;
        case EVT_TYPE_SYSTEMSTATUS_FROM_USBHOST:
            status_id_moudle = STATUS_ID_MODULE_USBHOST;
            break;*/
        case EVT_TYPE_SYSTEMSTATUS_FROM_USB:
            status_id_moudle = STATUS_ID_MODULE_USB;
            break;
        case EVT_TYPE_SYSTEMSTATUS_FROM_STORAGE:
            status_id_moudle = STATUS_ID_MODULE_STORAGE;
            break;
        default:
            ULOG_ERROR(SYS_STATUS_LOG , "unknow type:%x , status_id_moudle:%x\n" ,
                    msg->event_type , msg->module_id);
            break;
    }

    if (status_id_moudle)
    {
        MODULE_STATUS_S t_buf;

        t_buf.status_id_moudle = status_id_moudle;
        t_buf.len = msg->data_length;
        t_buf.buffer = msg->data;
        t_buf.error_fatal_id = 0;

        pi_mutex_lock(_s_data._mtx[0]);
        if (_s_data._exit == 0 &&
                channels_head_insert(&_s_data._set[0] , t_buf.status_id_moudle , &t_buf , 1) == 0)
        {
            int64_t value = 1;

            pi_write(_s_data._eventfd , &value , sizeof(value));
        }
        pi_mutex_unlock(_s_data._mtx[0]);
    }
}

static MODULE_STATUS_P _pack_status(void)
{
    struct fragment *cur = NULL , *next = NULL;
    int32_t length = 0 , index = 0 , module_block = sizeof(int32_t) * 4;
    uint8_t *addr = NULL , *t = NULL;;
    MODULE_STATUS_P tp = NULL;

    length = _s_data._set[1].number * module_block * 4;
    cur = _s_data._set[1].head;
    addr = (uint8_t*)pi_malloc(length);
    if (!addr)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "pi_malloc failed\n");
        return  NULL;
    }
    while((tp = channels_iterator(cur , &next)))
    {
        if (tp->len + index >= length)
        {
            length = (tp->len + index) * 2;
            t = (uint8_t*)pi_malloc(length);
            if (!t)
            {
                ULOG_ERROR(SYS_STATUS_LOG , "pi_malloc failed\n");
                pi_free(addr);
                return NULL;
            }
            if (index)
            {
                pi_memcpy(t , addr , index);
            }
            pi_free(addr);
            addr = t;
        }

        pi_memcpy(addr + index , tp->buffer , tp->len);
        index += tp->len;
        cur = next;
    }

    tp = (MODULE_STATUS_P)pi_malloc(sizeof(MODULE_STATUS_S));
    if (!tp)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "pi_malloc failed\n");
        pi_free(addr);
        return NULL;
    }

    tp->status_id_moudle = STATUS_ID_MODULE_ALL;
    tp->len = index;
    tp->buffer = addr;

    return tp;
}

static void* _status_handle(void *args)
{
    (void)args;
    int64_t value = 0;

    while(1)
    {
        MODULE_STATUS_P msta;

        pi_mutex_lock(_s_data._mtx[0]);
        if (_s_data._exit == 1)
        {
            pi_mutex_unlock(_s_data._mtx[0]);
            break;
        }
        msta = (MODULE_STATUS_P)channels_tail_pop(&_s_data._set[0]);
        pi_mutex_unlock(_s_data._mtx[0]);

        if (!msta)
        {
            read(_s_data._eventfd , &value , sizeof(value));
            continue;
        }

        pi_mutex_lock(_s_data._mtx[1]);
        if (!_status_update(msta))
        {
            pi_mutex_unlock(_s_data._mtx[1]);
            _free_buffer(msta);
            continue;
        }
        pi_mutex_unlock(_s_data._mtx[1]);

        MODULE_STATUS_P all_st;

        pi_mutex_lock(_s_data._mtx[1]);
        all_st = _pack_status();
        pi_mutex_unlock(_s_data._mtx[1]);
        if (all_st)
        {
            //notify status update
            pi_event_mgr_notify(_s_data._client , EVT_TYPE_SYSTEMSTATUS_UPDATE , all_st->buffer , all_st->len);
            _free_buffer(all_st);
        }
    }
    return NULL;
}

int32_t status_manager_prolog(void)
{
    int32_t ret = 0;

    ulog_register(&s_log);

    _s_data._exit = 0;
    _s_data._log_flag = 0;
    _s_data._eventfd = eventfd(0 , 0);
    if (_s_data._eventfd == -1)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "create eventfd failed , %s\n" , strerror(errno)) ;
        return -1;
    }
    channels_init(&_s_data._set[0] , _copy_alloc_buffer , _free_buffer); //NOTE: _set[0] queue insert node need to malloc
    channels_init(&_s_data._set[1] , NULL , _free_buffer); //NOTE: _set[1] queue insert node don't need to malloc
    _s_data._mtx[0] = pi_mutex_create();
    if (_s_data._mtx[0] == INVALIDMTX)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "create mutex failed \n");
        close(_s_data._eventfd);
        _s_data._eventfd = -1;
        return -1;
    }
    _s_data._mtx[1] = pi_mutex_create();
    if (_s_data._mtx[1] == INVALIDMTX)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "create mutex failed , %s\n");
        close(_s_data._eventfd);
        _s_data._eventfd = -1;
        pi_mutex_destroy(_s_data._mtx[0]);
        _s_data._mtx[0] = INVALIDMTX;
        return -1;
    }
    _s_data._client = pi_event_mgr_create_client(EVT_MODULE_SYSTEMSTATUS , _status_event_callback, NULL , &ret);
    if (!_s_data._client)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "create event client failed , ret:%d\n" , ret);
        pi_mutex_destroy(_s_data._mtx[0]);
        _s_data._mtx[0] = INVALIDMTX;
        pi_mutex_destroy(_s_data._mtx[1]);
        _s_data._mtx[1] = INVALIDMTX;
        close(_s_data._eventfd);
        _s_data._eventfd = -1;
        return -1;
    }

    uint32_t event_array[] = {
        EVT_TYPE_SYSTEMSTATUS_FROM_SCAN ,
        EVT_TYPE_SYSTEMSTATUS_FROM_PRINT ,
        EVT_TYPE_SYSTEMSTATUS_FROM_COPY ,
        //EVT_TYPE_SYSTEMSTATUS_FROM_ETH ,
        //EVT_TYPE_SYSTEMSTATUS_FROM_WIFI ,
        EVT_TYPE_SYSTEMSTATUS_FROM_NET ,
        EVT_TYPE_SYSTEMSTATUS_FROM_FWUPDATE ,
        EVT_TYPE_SYSTEMSTATUS_FROM_POWERMGR,
        //EVT_TYPE_SYSTEMSTATUS_FROM_USBDEVICE,
        //EVT_TYPE_SYSTEMSTATUS_FROM_USBHOST,
        EVT_TYPE_SYSTEMSTATUS_FROM_USB ,
        EVT_TYPE_SYSTEMSTATUS_FROM_STORAGE
    };

    ret = pi_event_mgr_register(_s_data._client ,
            event_array ,
            sizeof(event_array)/sizeof(event_array[0]));
    if (ret)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "register event failed , ret:%d\n" , ret);
    }

    _s_data._th = pi_thread_create(_status_handle , PI_NORMAL_STACK , NULL , PI_LEVEL_PRIORITY, NULL , "status_manager");
    if (_s_data._th == INVALIDTHREAD)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "create thread failed\n");
        pi_event_mgr_destroy_client(_s_data._client);
        _s_data._client = NULL;
        pi_mutex_destroy(_s_data._mtx[0]);
        _s_data._mtx[0] = INVALIDMTX;
        pi_mutex_destroy(_s_data._mtx[1]);
        _s_data._mtx[1] = INVALIDMTX;
        close(_s_data._eventfd);
        _s_data._eventfd = -1;
        return -1;
    }

#if CONFIG_SDK_PEDK
    /* PEDK API处理函数注册 */
    status_pedkapi_init();
#endif
    return 0;
}

void status_manager_epilog(void)
{
    int64_t value = 1;

    pi_mutex_lock(_s_data._mtx[0]);
    _s_data._exit = 1;
    pi_mutex_unlock(_s_data._mtx[0]);

    pi_write(_s_data._eventfd , &value , sizeof(value));

    pi_sleep(1);

    if (_s_data._th != INVALIDTHREAD)
    {
        pi_thread_destroy(_s_data._th);
        _s_data._th = INVALIDTHREAD;
    }
    if (_s_data._client)
    {
        pi_event_mgr_destroy_client(_s_data._client);
        _s_data._client = NULL;
    }
    if (_s_data._mtx[0] != INVALIDMTX)
    {
        pi_mutex_destroy(_s_data._mtx[0]);
        _s_data._mtx[0] = INVALIDMTX;
    }
    if (_s_data._mtx[1] != INVALIDMTX)
    {
        pi_mutex_destroy(_s_data._mtx[1]);
        _s_data._mtx[1] = INVALIDMTX;
    }


    channels_destroy(&_s_data._set[0]);
    channels_destroy(&_s_data._set[1]);
    if (_s_data._eventfd != -1)
    {
        close(_s_data._eventfd);
        _s_data._eventfd = -1;
    }
}

int32_t status_manager_get(STATUS_ID_MODULE_E module , uint8_t **addr , uint32_t *len)
{
    if (!addr || !len)
    {
        ULOG_ERROR(SYS_STATUS_LOG , "input args invalid\n");
        return -1;
    }

    MODULE_STATUS_P mst = NULL;

    if (module == STATUS_ID_MODULE_ALL)
    {
        pi_mutex_lock(_s_data._mtx[1]);
        mst = _pack_status();
        pi_mutex_unlock(_s_data._mtx[1]);
    }
    else
    {

        pi_mutex_lock(_s_data._mtx[1]);
        MODULE_STATUS_P t = channels_find(&_s_data._set[1] , module);
        if (t)
        {
            mst = _copy_alloc_buffer(t);
        }
        pi_mutex_unlock(_s_data._mtx[1]);
    }

    if (mst)
    {
        *addr = mst->buffer;
        *len = mst->len;
        pi_free(mst);
    }
    else
    {
        *addr = NULL;
        *len = 0;
    }

    return 0;
}

int32_t status_manager_check_abnormality(STATUS_ID_MODULE_E module)
{
    int ret = 0;

    pi_mutex_lock(_s_data._mtx[1]);
    MODULE_STATUS_P t = channels_find(&_s_data._set[1] , module);

    if (t && t->error_fatal_id)
    {
        ret = 1;
    }
    pi_mutex_unlock(_s_data._mtx[1]);

    return ret;
}

int32_t status_manager_first_ready(STATUS_ID_MODULE_E module)
{
    int32_t ret = 0;

    pi_mutex_lock(_s_data._mtx[1]);
    if (module == STATUS_ID_MODULE_PRINT)
    {
        ret = _s_data._print_first_ready;
    }
    else if (module == STATUS_ID_MODULE_SCAN)
    {
        ret = _s_data._scan_first_ready;
    }
    else if (module == STATUS_ID_MODULE_COPY)
    {
        ret = _s_data._copy_first_ready;
    }
    pi_mutex_unlock(_s_data._mtx[1]);

    return ret;
}

/**
 * @}
 */
