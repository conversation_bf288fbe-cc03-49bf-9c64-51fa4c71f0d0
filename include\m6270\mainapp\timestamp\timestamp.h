#ifndef TIMESTAMP_H
#define TIMESTAMP_H

/* TimeStamp userspace interface */
#define TIMESTAMP_IOC_MAGIC     't'
#define TIMESTAMP_NAME_LEN      64

typedef enum
{
    TimeStampTrigger = 0,
    TimeStampStartEnd,
    TimeStampValue,
    TimeStampLevel,

    TimeStampStart = 100,
    TimeStampEnd,
}TimeStampType;


struct timestamp_ioc_register {
    char    name[TIMESTAMP_NAME_LEN];
    int     type;
    int     max_count;
    int     ts;
};

struct timestamp_wr_message {
    int     ts;
    int     type;
    long    value;
};

#define TIMESTAMP_REGISTER(N) \
	((((N)*(sizeof (struct timestamp_ioc_register))) < (1 << _IOC_SIZEBITS)) \
		? ((N)*(sizeof (struct timestamp_ioc_register))) : 0)

#define TIMESTAMP_IOC_REGISTER(N)   _IOW(TIMESTAMP_IOC_MAGIC, 0, char[TIMESTAMP_REGISTER(N)])

#define TIMESTAMP_IOC_RD_MODE       _IOR(TIMESTAMP_IOC_MAGIC, 1, uint8_t)
#define TIMESTAMP_IOC_WR_MODE       _IOW(TIMESTAMP_IOC_MAGIC, 1, char[sizeof(struct timestamp_wr_message)])

int timestamp_register(char *name, TimeStampType type, int maxcount);
void timestamp_append(int handle, TimeStampType type, long value);
void timestamp_cleanup(void);

#endif
