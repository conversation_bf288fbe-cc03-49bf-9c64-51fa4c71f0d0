/**************************************************************
 * @file authority_adapt.h
 * @module Authority Adaptation Layer
 * @brief Header file for authority adaptation API.
 *
 * This file defines the data structures, constants, and function
 * prototypes for the authority adaptation layer. It facilitates
 * interactions between various authority modules, such as Print,
 * Scan, and Copy, and their respective functionalities.
 *
 * @date 2024-12-13
 * <AUTHOR> - <PERSON> (<EMAIL>)
 * @copyright
 * - 2021 Zhuhai Pantum Technology Co.Ltd all rights reserved.
 **************************************************************/
#ifndef AUTHOTITY_ADAPT_H
#define AUTHOTITY_ADAPT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdint.h>
#include <stdlib.h>
#include <pthread.h>
#include <string.h>

#include "pol/pol_log.h"
#include "cjson/cJSON.h"
#include "utilities/msgrouter.h"


/** @brief Return code indicating success. */
#define RET_OK          (0)

/** @brief Return code indicating error. */
#define RET_ERROR       (-1)

/** @brief Maximum size for the user name string. */
#define USRNAME_STRING_SIZE 64

/**
 * @enum AUTHORITY_MODULE_TYPE_E
 * @brief Enumerates authority module types.
 *
 * These module types represent different functionalities
 * available in the system.
 */
typedef enum {
    AUTHORITY_MODULE_PRINT, /**< Module for print-related operations */
    AUTHORITY_MODULE_SCAN,  /**< Module for scan-related operations */
    AUTHORITY_MODULE_COPY,  /**< Module for copy-related operations */
    AUTHORITY_MODULE_MAX    /**< Sentinel value indicating maximum module count */
} AUTHORITY_MODULE_TYPE_E;

typedef enum {
    CFG_SCAN_PUSH2FTP,
    CFG_SCAN_PUSH2EMAIL,
    CFG_SCAN_PUSH2SAMBA,
    CFG_SCAN_PUSH2UNDEFINED,
    CFG_SCAN_PUSH_BROOM_MAX,
}CFG_SCAN_PUSH_TYPE_E;


typedef struct {
    int (*authority_proc) (AUTHORITY_MODULE_TYPE_E module_type, void *json_data); //参数包括 所属模块+从网络解析过来的json字串

    int (*cfg_scan_push_broom) (CFG_SCAN_PUSH_TYPE_E cfg_type, void *json_data); //scan模块推扫信息接收接口注册

}AUTHORITY_ADAPT_INTF_S;

/**
 * @brief Reset the stored user name to an empty string.
 */
void authority_adapt_reset_usrname (void);

/**
 * @brief Retrieves the current USB enable status.
 *
 * This function returns the status of the USB enable flag, which
 * indicates whether USB functionalities are currently enabled or disabled.
 *
 * @return
 * - 1: USB is enabled.
 * - 0: USB is disabled.
 *
 * @note The status is protected by a spinlock to ensure thread safety.
 */
uint32_t authority_adapt_get_usbenable (void);

//int32_t authority_adapt_data_save(char *pData);

/**
 * @brief Retrieve the stored remote jobs data.
 * @return Pointer to the remote jobs data string.
 */
char *authority_adapt_get_remotejobs_data(void);

/**
 * @brief Process local data and update internal authority information.
 * @param[in] pData The raw JSON data of local authority.
 * @return RET_OK on success, RET_ERROR on failure.
 */
int32_t authority_adapt_local_data_proc (char *pData);

/**
 * @brief Receive and process authority data.
 * @param[in] pData The raw JSON authority data.
 * @return RET_OK on success, RET_ERROR on failure.
 */
int32_t authority_adapt_recv_data_proc (char *pData);

/**
 * @brief Registers an interface callback for a specific authority module.
 *
 * This function allows modules such as Print, Scan, or Copy to
 * register their callback functions for specific operations.
 *
 * @param[in] module_type The type of the module to register (e.g., Print, Scan).
 * @param[in] intf_cb Pointer to the callback function for the module.
 * @return
 * - RET_OK: Registration successful.
 * - RET_ERROR: Invalid module type or registration failed.
 *
 * @note The registered callback must follow the defined function signature.
 */
int32_t authority_adapt_reg_intf (AUTHORITY_MODULE_TYPE_E module_type, void* intf_cb);

/**
 * @brief Retrieve the stored user name.
 * @param[out] usr_name Buffer to store the retrieved user name.
 * @param[in] usr_name_size Size of the provided buffer.
 * @return 0 on success, -1 on failure.
 */
int authority_adapt_get_usrname(char *usr_name, size_t usr_name_size);

#ifdef __cplusplus
}
#endif /* AUTHOTITY_ADAPT_H */

#endif
