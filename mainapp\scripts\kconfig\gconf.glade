<?xml version="1.0" standalone="no"?> <!--*- mode: xml -*-->

<glade-interface>

<widget class="GtkWindow" id="window1">
  <property name="visible">True</property>
  <property name="title" translatable="yes">Gtk Kernel Configurator</property>
  <property name="type">GTK_WINDOW_TOPLEVEL</property>
  <property name="window_position">GTK_WIN_POS_NONE</property>
  <property name="modal">False</property>
  <property name="default_width">640</property>
  <property name="default_height">480</property>
  <property name="resizable">True</property>
  <property name="destroy_with_parent">False</property>
  <property name="decorated">True</property>
  <property name="skip_taskbar_hint">False</property>
  <property name="skip_pager_hint">False</property>
  <property name="type_hint">GDK_WINDOW_TYPE_HINT_NORMAL</property>
  <property name="gravity">GDK_GRAVITY_NORTH_WEST</property>
  <signal name="destroy" handler="on_window1_destroy" object="window1"/>
  <signal name="size_request" handler="on_window1_size_request" object="vpaned1" last_modification_time="Fri, 11 Jan 2002 16:17:11 GMT"/>
  <signal name="delete_event" handler="on_window1_delete_event" object="window1" last_modification_time="Sun, 09 Mar 2003 19:42:46 GMT"/>

  <child>
    <widget class="GtkVBox" id="vbox1">
      <property name="visible">True</property>
      <property name="homogeneous">False</property>
      <property name="spacing">0</property>

      <child>
	<widget class="GtkMenuBar" id="menubar1">
	  <property name="visible">True</property>

	  <child>
	    <widget class="GtkMenuItem" id="file1">
	      <property name="visible">True</property>
	      <property name="label" translatable="yes">_File</property>
	      <property name="use_underline">True</property>

	      <child>
		<widget class="GtkMenu" id="file1_menu">

		  <child>
		    <widget class="GtkImageMenuItem" id="load1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Load a config file</property>
		      <property name="label" translatable="yes">_Load</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_load1_activate"/>
		      <accelerator key="L" modifiers="GDK_CONTROL_MASK" signal="activate"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image39">
			  <property name="visible">True</property>
			  <property name="stock">gtk-open</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkImageMenuItem" id="save1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Save the config in .config</property>
		      <property name="label" translatable="yes">_Save</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_save_activate"/>
		      <accelerator key="S" modifiers="GDK_CONTROL_MASK" signal="activate"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image40">
			  <property name="visible">True</property>
			  <property name="stock">gtk-save</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkImageMenuItem" id="save_as1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Save the config in a file</property>
		      <property name="label" translatable="yes">Save _as</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_save_as1_activate"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image41">
			  <property name="visible">True</property>
			  <property name="stock">gtk-save-as</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkSeparatorMenuItem" id="separator1">
		      <property name="visible">True</property>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkImageMenuItem" id="quit1">
		      <property name="visible">True</property>
		      <property name="label" translatable="yes">_Quit</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_quit1_activate"/>
		      <accelerator key="Q" modifiers="GDK_CONTROL_MASK" signal="activate"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image42">
			  <property name="visible">True</property>
			  <property name="stock">gtk-quit</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>
		</widget>
	      </child>
	    </widget>
	  </child>

	  <child>
	    <widget class="GtkMenuItem" id="options1">
	      <property name="visible">True</property>
	      <property name="label" translatable="yes">_Options</property>
	      <property name="use_underline">True</property>

	      <child>
		<widget class="GtkMenu" id="options1_menu">

		  <child>
		    <widget class="GtkCheckMenuItem" id="show_name1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Show name</property>
		      <property name="label" translatable="yes">Show _name</property>
		      <property name="use_underline">True</property>
		      <property name="active">False</property>
		      <signal name="activate" handler="on_show_name1_activate"/>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkCheckMenuItem" id="show_range1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Show range (Y/M/N)</property>
		      <property name="label" translatable="yes">Show _range</property>
		      <property name="use_underline">True</property>
		      <property name="active">False</property>
		      <signal name="activate" handler="on_show_range1_activate"/>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkCheckMenuItem" id="show_data1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Show value of the option</property>
		      <property name="label" translatable="yes">Show _data</property>
		      <property name="use_underline">True</property>
		      <property name="active">False</property>
		      <signal name="activate" handler="on_show_data1_activate"/>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkSeparatorMenuItem" id="separator2">
		      <property name="visible">True</property>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkRadioMenuItem" id="set_option_mode1">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Show normal options</property>
		      <property name="label" translatable="yes">Show normal options</property>
		      <property name="use_underline">True</property>
		      <property name="active">True</property>
		      <signal name="activate" handler="on_set_option_mode1_activate"/>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkRadioMenuItem" id="set_option_mode2">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Show all options</property>
		      <property name="label" translatable="yes">Show all _options</property>
		      <property name="use_underline">True</property>
		      <property name="active">False</property>
		      <property name="group">set_option_mode1</property>
		      <signal name="activate" handler="on_set_option_mode2_activate"/>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkRadioMenuItem" id="set_option_mode3">
		      <property name="visible">True</property>
		      <property name="tooltip" translatable="yes">Show all options with prompts</property>
		      <property name="label" translatable="yes">Show all prompt options</property>
		      <property name="use_underline">True</property>
		      <property name="active">False</property>
		      <property name="group">set_option_mode1</property>
		      <signal name="activate" handler="on_set_option_mode3_activate"/>
		    </widget>
		  </child>

		</widget>
	      </child>
	    </widget>
	  </child>

	  <child>
	    <widget class="GtkMenuItem" id="help1">
	      <property name="visible">True</property>
	      <property name="label" translatable="yes">_Help</property>
	      <property name="use_underline">True</property>

	      <child>
		<widget class="GtkMenu" id="help1_menu">

		  <child>
		    <widget class="GtkImageMenuItem" id="introduction1">
		      <property name="visible">True</property>
		      <property name="label" translatable="yes">_Introduction</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_introduction1_activate" last_modification_time="Fri, 15 Nov 2002 20:26:30 GMT"/>
		      <accelerator key="I" modifiers="GDK_CONTROL_MASK" signal="activate"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image43">
			  <property name="visible">True</property>
			  <property name="stock">gtk-dialog-question</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkImageMenuItem" id="about1">
		      <property name="visible">True</property>
		      <property name="label" translatable="yes">_About</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_about1_activate" last_modification_time="Fri, 15 Nov 2002 20:26:30 GMT"/>
		      <accelerator key="A" modifiers="GDK_CONTROL_MASK" signal="activate"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image44">
			  <property name="visible">True</property>
			  <property name="stock">gtk-properties</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>

		  <child>
		    <widget class="GtkImageMenuItem" id="license1">
		      <property name="visible">True</property>
		      <property name="label" translatable="yes">_License</property>
		      <property name="use_underline">True</property>
		      <signal name="activate" handler="on_license1_activate" last_modification_time="Fri, 15 Nov 2002 20:26:30 GMT"/>

		      <child internal-child="image">
			<widget class="GtkImage" id="image45">
			  <property name="visible">True</property>
			  <property name="stock">gtk-justify-fill</property>
			  <property name="icon_size">1</property>
			  <property name="xalign">0.5</property>
			  <property name="yalign">0.5</property>
			  <property name="xpad">0</property>
			  <property name="ypad">0</property>
			</widget>
		      </child>
		    </widget>
		  </child>
		</widget>
	      </child>
	    </widget>
	  </child>
	</widget>
	<packing>
	  <property name="padding">0</property>
	  <property name="expand">False</property>
	  <property name="fill">False</property>
	</packing>
      </child>

      <child>
	<widget class="GtkHandleBox" id="handlebox1">
	  <property name="visible">True</property>
	  <property name="shadow_type">GTK_SHADOW_OUT</property>
	  <property name="handle_position">GTK_POS_LEFT</property>
	  <property name="snap_edge">GTK_POS_TOP</property>

	  <child>
	    <widget class="GtkToolbar" id="toolbar1">
	      <property name="visible">True</property>
	      <property name="orientation">GTK_ORIENTATION_HORIZONTAL</property>
	      <property name="toolbar_style">GTK_TOOLBAR_BOTH</property>
	      <property name="tooltips">True</property>
	      <property name="show_arrow">True</property>

	      <child>
		<widget class="GtkToolButton" id="button1">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Goes up of one level (single view)</property>
		  <property name="label" translatable="yes">Back</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-undo</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_back_clicked"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolItem" id="toolitem1">
		  <property name="visible">True</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>

		  <child>
		    <widget class="GtkVSeparator" id="vseparator1">
		      <property name="visible">True</property>
		    </widget>
		  </child>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">False</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button2">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Load a config file</property>
		  <property name="label" translatable="yes">Load</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-open</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_load_clicked"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button3">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Save a config file</property>
		  <property name="label" translatable="yes">Save</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-save</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_save_activate"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolItem" id="toolitem2">
		  <property name="visible">True</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>

		  <child>
		    <widget class="GtkVSeparator" id="vseparator2">
		      <property name="visible">True</property>
		    </widget>
		  </child>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">False</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button4">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Single view</property>
		  <property name="label" translatable="yes">Single</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-missing-image</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_single_clicked" last_modification_time="Sun, 12 Jan 2003 14:28:39 GMT"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button5">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Split view</property>
		  <property name="label" translatable="yes">Split</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-missing-image</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_split_clicked" last_modification_time="Sun, 12 Jan 2003 14:28:45 GMT"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button6">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Full view</property>
		  <property name="label" translatable="yes">Full</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-missing-image</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_full_clicked" last_modification_time="Sun, 12 Jan 2003 14:28:50 GMT"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolItem" id="toolitem3">
		  <property name="visible">True</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>

		  <child>
		    <widget class="GtkVSeparator" id="vseparator3">
		      <property name="visible">True</property>
		    </widget>
		  </child>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">False</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button7">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Collapse the whole tree in the right frame</property>
		  <property name="label" translatable="yes">Collapse</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-remove</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_collapse_clicked"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkToolButton" id="button8">
		  <property name="visible">True</property>
		  <property name="tooltip" translatable="yes">Expand the whole tree in the right frame</property>
		  <property name="label" translatable="yes">Expand</property>
		  <property name="use_underline">True</property>
		  <property name="stock_id">gtk-add</property>
		  <property name="visible_horizontal">True</property>
		  <property name="visible_vertical">True</property>
		  <property name="is_important">False</property>
		  <signal name="clicked" handler="on_expand_clicked"/>
		</widget>
		<packing>
		  <property name="expand">False</property>
		  <property name="homogeneous">True</property>
		</packing>
	      </child>
	    </widget>
	  </child>
	</widget>
	<packing>
	  <property name="padding">0</property>
	  <property name="expand">False</property>
	  <property name="fill">False</property>
	</packing>
      </child>

      <child>
	<widget class="GtkHPaned" id="hpaned1">
	  <property name="width_request">1</property>
	  <property name="visible">True</property>
	  <property name="can_focus">True</property>
	  <property name="position">0</property>

	  <child>
	    <widget class="GtkScrolledWindow" id="scrolledwindow1">
	      <property name="visible">True</property>
	      <property name="hscrollbar_policy">GTK_POLICY_AUTOMATIC</property>
	      <property name="vscrollbar_policy">GTK_POLICY_AUTOMATIC</property>
	      <property name="shadow_type">GTK_SHADOW_IN</property>
	      <property name="window_placement">GTK_CORNER_TOP_LEFT</property>

	      <child>
		<widget class="GtkTreeView" id="treeview1">
		  <property name="visible">True</property>
		  <property name="can_focus">True</property>
		  <property name="headers_visible">True</property>
		  <property name="rules_hint">False</property>
		  <property name="reorderable">False</property>
		  <property name="enable_search">False</property>
		  <signal name="cursor_changed" handler="on_treeview2_cursor_changed" last_modification_time="Sun, 12 Jan 2003 15:58:22 GMT"/>
		  <signal name="button_press_event" handler="on_treeview1_button_press_event" last_modification_time="Sun, 12 Jan 2003 16:03:52 GMT"/>
		  <signal name="key_press_event" handler="on_treeview2_key_press_event" last_modification_time="Sun, 12 Jan 2003 16:11:44 GMT"/>
		</widget>
	      </child>
	    </widget>
	    <packing>
	      <property name="shrink">True</property>
	      <property name="resize">False</property>
	    </packing>
	  </child>

	  <child>
	    <widget class="GtkVPaned" id="vpaned1">
	      <property name="visible">True</property>
	      <property name="can_focus">True</property>
	      <property name="position">0</property>

	      <child>
		<widget class="GtkScrolledWindow" id="scrolledwindow2">
		  <property name="visible">True</property>
		  <property name="hscrollbar_policy">GTK_POLICY_AUTOMATIC</property>
		  <property name="vscrollbar_policy">GTK_POLICY_AUTOMATIC</property>
		  <property name="shadow_type">GTK_SHADOW_IN</property>
		  <property name="window_placement">GTK_CORNER_TOP_LEFT</property>

		  <child>
		    <widget class="GtkTreeView" id="treeview2">
		      <property name="visible">True</property>
		      <property name="can_focus">True</property>
		      <property name="has_focus">True</property>
		      <property name="headers_visible">True</property>
		      <property name="rules_hint">False</property>
		      <property name="reorderable">False</property>
		      <property name="enable_search">False</property>
		      <signal name="cursor_changed" handler="on_treeview2_cursor_changed" last_modification_time="Sun, 12 Jan 2003 15:57:55 GMT"/>
		      <signal name="button_press_event" handler="on_treeview2_button_press_event" last_modification_time="Sun, 12 Jan 2003 15:57:58 GMT"/>
		      <signal name="key_press_event" handler="on_treeview2_key_press_event" last_modification_time="Sun, 12 Jan 2003 15:58:01 GMT"/>
		    </widget>
		  </child>
		</widget>
		<packing>
		  <property name="shrink">True</property>
		  <property name="resize">False</property>
		</packing>
	      </child>

	      <child>
		<widget class="GtkScrolledWindow" id="scrolledwindow3">
		  <property name="visible">True</property>
		  <property name="hscrollbar_policy">GTK_POLICY_NEVER</property>
		  <property name="vscrollbar_policy">GTK_POLICY_AUTOMATIC</property>
		  <property name="shadow_type">GTK_SHADOW_IN</property>
		  <property name="window_placement">GTK_CORNER_TOP_LEFT</property>

		  <child>
		    <widget class="GtkTextView" id="textview3">
		      <property name="visible">True</property>
		      <property name="can_focus">True</property>
		      <property name="editable">False</property>
		      <property name="overwrite">False</property>
		      <property name="accepts_tab">True</property>
		      <property name="justification">GTK_JUSTIFY_LEFT</property>
		      <property name="wrap_mode">GTK_WRAP_WORD</property>
		      <property name="cursor_visible">True</property>
		      <property name="pixels_above_lines">0</property>
		      <property name="pixels_below_lines">0</property>
		      <property name="pixels_inside_wrap">0</property>
		      <property name="left_margin">0</property>
		      <property name="right_margin">0</property>
		      <property name="indent">0</property>
		      <property name="text" translatable="yes">Sorry, no help available for this option yet.</property>
		    </widget>
		  </child>
		</widget>
		<packing>
		  <property name="shrink">True</property>
		  <property name="resize">True</property>
		</packing>
	      </child>
	    </widget>
	    <packing>
	      <property name="shrink">True</property>
	      <property name="resize">True</property>
	    </packing>
	  </child>
	</widget>
	<packing>
	  <property name="padding">0</property>
	  <property name="expand">True</property>
	  <property name="fill">True</property>
	</packing>
      </child>
    </widget>
  </child>
</widget>

</glade-interface>
