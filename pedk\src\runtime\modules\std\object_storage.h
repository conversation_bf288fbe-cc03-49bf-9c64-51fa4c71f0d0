/**
 * @Copyright 2022 Zhuhai Pantum Technology Co.Ltd all rights reserved
 * @file object_storage.h
 * @addtogroup std
 * @{
 * @addtogroup runtime
 * @autor 
 * @date 2024-06-11
 * @brief object_storage init 
 */
#ifndef __MODULES__STD__SAVE_H__
#define __MODULES__STD__SAVE_H__

#include <quickjs.h>
#include "runtime/utils/file_utils.h"
#include "runtime/runtime.h"
#include "basic/config.h"

/**
 * @brief   js object save
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_object_save(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv);

/**
 * @brief   js object load
 * @param[in] *ctx :a heap space runtime handle
 * @param[in] this_val :val 
 * @param[in] argc :param num 
 * @param[in] *argv :param content
 * @return  Construct result
 * <AUTHOR> @date    2024-06-11
 */
JSValue js_object_load(JSContext* ctx, JSValueConst this_val, int argc, JSValueConst* argv);

#endif // __MODULES__STD__SAVE_H__
/**
 * @}
 */
